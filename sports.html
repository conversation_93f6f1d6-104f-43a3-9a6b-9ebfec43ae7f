<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Betting - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="sports.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    
    <!-- Enhanced Mobile Viewport -->
    <meta name="theme-color" content="#1565c0">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="GoldenAura Sports">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="assets/js/script.js" as="script">
    <link rel="preload" href="sports.js" as="script">
</head>
<body>
 <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                    <span class="logo-text">GoldenAura</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-balance" style="display: none;">
                    <span class="balance-label">Balance:</span>
                    <span class="balance-amount" data-balance>0.00 GA</span>
                </div>
                <div class="user-profile" style="display: none;" data-user-element="profile">
                    <span class="user-email" data-user-element="email"></span>
                    <button class="btn btn-logout" data-auth-action="logout" style="display: none;">Logout</button>
                </div>
                <div class="live-support-indicator">
                    <div class="support-dot"></div>
                    <span>Live Support</span>
                </div>
                <button class="btn btn-login" onclick="showSimpleLogin()">Login</button>
                <button class="btn btn-register" onclick="window.open('https://t.me/Goldenaura_PY_bot?start=register', '_blank')">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="sports.html" class="nav-item active">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="sports-betting-container">
            <!-- Sports Sidebar (Desktop) / Top Section (Mobile) -->
            <div class="sports-sidebar">
                <!-- Enhanced Sports Header -->
                <div class="sports-header">
                    <div>
                        <h1><i class="fas fa-futbol"></i> Sports Betting</h1>
                        <p class="subtitle">Live events and upcoming matches</p>
                    </div>
                    <div class="balance-display">
                        <div>
                            <div class="balance-label">Balance</div>
                            <div class="balance-amount" id="user-balance">10,000 GA</div>
                        </div>
                    </div>
                </div>

                <!-- Promotions Banner -->
                <div class="promotions-banner">
                    <div class="promotion-text">GoldenAura's Sport Raffle - Win</div>
                    <div class="promotion-amount">GA 50,000</div>
                    <div class="promotion-status">Coming Soon</div>
                </div>

                <!-- Search and Filters -->
                <div class="search-filters-container">
                    <div class="search-section">
                        <input type="text" placeholder="Search your event..." id="searchInput">
                    </div>
                    
                    <!-- Betting Tabs -->
                    <div class="betting-tabs">
                        <button class="betting-tab active" data-tab="lobby">Lobby</button>
                        <button class="betting-tab" data-tab="my-bets">My Bets</button>
                        <button class="betting-tab" data-tab="favourites">Favourites</button>
                        <button class="betting-tab" data-tab="starting-soon">Starting Soon</button>
                    </div>
                </div>

                <!-- Sports Navigation -->
                <div class="sports-nav">
                    <h2><i class="fas fa-trophy"></i> Top Sports</h2>
                    <div class="sports-list">
                        <a href="#" class="sport-item active" data-sport="soccer">
                            <span class="sport-emoji">⚽</span>
                            <span class="sport-name">SOCCER</span>
                            <span class="sport-count">12</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="tennis">
                            <span class="sport-emoji">🎾</span>
                            <span class="sport-name">TENNIS</span>
                            <span class="sport-count">8</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="baseball">
                            <span class="sport-emoji">⚾</span>
                            <span class="sport-name">BASEBALL</span>
                            <span class="sport-count">6</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="basketball">
                            <span class="sport-emoji">🏀</span>
                            <span class="sport-name">BASKETBALL</span>
                            <span class="sport-count">15</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="ice-hockey">
                            <span class="sport-emoji">🏒</span>
                            <span class="sport-name">ICE HOCKEY</span>
                            <span class="sport-count">4</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="cricket">
                            <span class="sport-emoji">🏏</span>
                            <span class="sport-name">CRICKET</span>
                            <span class="sport-count">9</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="horse-racing">
                            <span class="sport-emoji">🏇</span>
                            <span class="sport-name">HORSE RACING</span>
                            <span class="sport-count">22</span>
                        </a>
                        <a href="#" class="sport-item" data-sport="esports">
                            <span class="sport-emoji">🎮</span>
                            <span class="sport-name">CS2</span>
                            <span class="sport-count">7</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Main Sports Content -->
            <div class="sports-main">
                <!-- Live Events Section -->
                <div class="live-events">
                    <div class="live-events-header">
                        <h2><i class="fas fa-broadcast-tower"></i> Live Events <span class="live-indicator"></span></h2>
                        <div class="live-count">8 live</div>
                    </div>
                    
                    <div class="live-sports-scroll">
                        <div class="live-sport-item" data-sport="soccer">
                            <div class="live-sport-emoji">⚽</div>
                            <div class="live-sport-name">Soccer</div>
                        </div>
                        <div class="live-sport-item" data-sport="basketball">
                            <div class="live-sport-emoji">🏀</div>
                            <div class="live-sport-name">Basketball</div>
                        </div>
                        <div class="live-sport-item" data-sport="esports">
                            <div class="live-sport-emoji">🎮</div>
                            <div class="live-sport-name">Esports</div>
                        </div>
                        <div class="live-sport-item" data-sport="american-football">
                            <div class="live-sport-emoji">🏈</div>
                            <div class="live-sport-name">Football</div>
                        </div>
                        <div class="live-sport-item" data-sport="ice-hockey">
                            <div class="live-sport-emoji">🏒</div>
                            <div class="live-sport-name">Hockey</div>
                        </div>
                        <div class="live-sport-item" data-sport="cricket">
                            <div class="live-sport-emoji">🏏</div>
                            <div class="live-sport-name">Cricket</div>
                        </div>
                    </div>
                    
                    <div class="event-controls">
                        <select class="event-dropdown">
                            <option>Display</option>
                            <option>All Events</option>
                            <option>Live Only</option>
                            <option>Starting Soon</option>
                        </select>
                        <select class="event-dropdown">
                            <option>Standard</option>
                            <option>Compact</option>
                            <option>Detailed</option>
                        </select>
                        <select class="event-dropdown">
                            <option>Market</option>
                            <option>Moneyline</option>
                            <option>Spread</option>
                            <option>Over/Under</option>
                        </select>
                    </div>
                    
                    <div class="currency-info">
                        <span>All odds displayed in GA • $1 USD = 10 GA</span>
                    </div>
                </div>

                <!-- Matches Container -->
                <div class="matches-container">
                    <div class="matches-list" id="matches-list">
                        <!-- Sample Match Items -->
                        <div class="match-item live">
                            <div class="match-header">
                                <div class="match-time live">LIVE</div>
                                <div class="match-league">Premier League</div>
                            </div>
                            <div class="match-teams">
                                <div class="team-info">
                                    <div class="team-name">Manchester United</div>
                                    <div class="team-record">12-3-2</div>
                                </div>
                                <div class="match-vs">VS</div>
                                <div class="team-info">
                                    <div class="team-name">Liverpool FC</div>
                                    <div class="team-record">14-2-1</div>
                                </div>
                            </div>
                            <div class="match-odds">
                                <button class="odds-button" data-selection="home">
                                    <div class="odds-value">2.10</div>
                                    <div class="odds-label">Home</div>
                                </button>
                                <button class="odds-button" data-selection="draw">
                                    <div class="odds-value">3.20</div>
                                    <div class="odds-label">Draw</div>
                                </button>
                                <button class="odds-button" data-selection="away">
                                    <div class="odds-value">3.80</div>
                                    <div class="odds-label">Away</div>
                                </button>
                            </div>
                        </div>

                        <div class="match-item">
                            <div class="match-header">
                                <div class="match-time">Today 15:30</div>
                                <div class="match-league">NBA</div>
                            </div>
                            <div class="match-teams">
                                <div class="team-info">
                                    <div class="team-name">Los Angeles Lakers</div>
                                    <div class="team-record">25-12</div>
                                </div>
                                <div class="match-vs">VS</div>
                                <div class="team-info">
                                    <div class="team-name">Golden State Warriors</div>
                                    <div class="team-record">22-15</div>
                                </div>
                            </div>
                            <div class="match-odds">
                                <button class="odds-button" data-selection="home">
                                    <div class="odds-value">1.85</div>
                                    <div class="odds-label">Lakers</div>
                                </button>
                                <button class="odds-button" data-selection="away">
                                    <div class="odds-value">1.95</div>
                                    <div class="odds-label">Warriors</div>
                                </button>
                            </div>
                        </div>

                        <div class="match-item">
                            <div class="match-header">
                                <div class="match-time">Tomorrow 20:00</div>
                                <div class="match-league">Champions League</div>
                            </div>
                            <div class="match-teams">
                                <div class="team-info">
                                    <div class="team-name">Real Madrid</div>
                                    <div class="team-record">16-1-0</div>
                                </div>
                                <div class="match-vs">VS</div>
                                <div class="team-info">
                                    <div class="team-name">FC Barcelona</div>
                                    <div class="team-record">15-2-0</div>
                                </div>
                            </div>
                            <div class="match-odds">
                                <button class="odds-button" data-selection="home">
                                    <div class="odds-value">2.45</div>
                                    <div class="odds-label">Home</div>
                                </button>
                                <button class="odds-button" data-selection="draw">
                                    <div class="odds-value">3.10</div>
                                    <div class="odds-label">Draw</div>
                                </button>
                                <button class="odds-button" data-selection="away">
                                    <div class="odds-value">2.90</div>
                                    <div class="odds-label">Away</div>
                                </button>
                            </div>
                        </div>

                        <!-- Loading message for dynamic content -->
                        <div class="loading-message" style="display: none;">
                            Loading more matches...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bet Slip Sidebar (Desktop) / Bottom Panel (Mobile) -->
            <div class="sports-aside">
                <div class="bet-slip" id="betSlip">
                    <div class="bet-slip-header">
                        <h3 class="bet-slip-title"><i class="fas fa-ticket-alt"></i> Bet Slip</h3>
                        <button class="bet-slip-close" id="closeBetSlip">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div id="selected-bets">
                        <div class="empty-state">
                            <i class="fas fa-clipboard-list"></i>
                            <h3>No bets selected</h3>
                            <p>Click on odds to add selections to your bet slip</p>
                        </div>
                    </div>
                    
                    <div class="bet-controls" style="display: none;">
                        <div class="bet-type-tabs">
                            <button class="betting-tab active" data-bet-type="single">Single</button>
                            <button class="betting-tab" data-bet-type="parlay">Parlay</button>
                        </div>
                        
                        <input type="number" 
                               class="bet-stake-input" 
                               id="bet-amount" 
                               placeholder="Enter stake amount..." 
                               min="10" 
                               max="10000" 
                               step="10">
                        
                        <div class="bet-potential">
                            <span>Potential Return: </span>
                            <span id="payout-amount">0 GA</span>
                        </div>
                        
                        <button class="place-bet-btn" id="place-bet" disabled>
                            <i class="fas fa-check"></i> Place Bet
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-links">
            <a href="index.html">Home</a>
            <a href="terms.html">Terms of Service</a>
            <a href="privacy.html">Privacy Policy</a>
            <a href="support.html">Support</a>
        </div>
        <p class="footer-text">© 2025 GoldenAura. All rights reserved.</p>
    </footer>

    <!-- Enhanced JavaScript -->
    <script src="assets/js/script.js"></script>
    <script src="config.js"></script>
    <script src="api-service.js"></script>
    <script src="mock-api.js"></script>
    <script src="data-manager.js"></script>
    <script src="bet-manager.js"></script>
    <script src="sports-app.js"></script>
    
    <!-- Sports-specific responsive enhancements -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize enhanced sports features
            initializeSportsEnhancements();
        });
        
        function initializeSportsEnhancements() {
            // Set active nav item for sports
            const sportsNavItem = document.querySelector('.nav-item[href="sports.html"]');
            if (sportsNavItem) {
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                sportsNavItem.classList.add('active');
            }
            
            // Mobile bet slip functionality
            const betSlip = document.getElementById('betSlip');
            const closeBetSlip = document.getElementById('closeBetSlip');
            
            // Enhanced odds button interactions
            document.querySelectorAll('.odds-button').forEach(button => {
                button.addEventListener('click', function() {
                    // Visual feedback
                    this.classList.toggle('selected');
                    
                    // Haptic feedback
                    if (navigator.vibrate && window.innerWidth <= 767) {
                        navigator.vibrate(50);
                    }
                    
                    // Show bet slip on mobile
                    if (window.innerWidth <= 767) {
                        betSlip.classList.add('active');
                    }
                });
            });
            
            // Enhanced sport item interactions
            document.querySelectorAll('.sport-item, .live-sport-item').forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                }, { passive: true });
                
                item.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                }, { passive: true });
            });
            
            // Close bet slip functionality
            if (closeBetSlip) {
                closeBetSlip.addEventListener('click', function() {
                    betSlip.classList.remove('active');
                });
            }
            
            // Sport navigation active states
            document.querySelectorAll('.sport-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.querySelectorAll('.sport-item').forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        }
    </script>
</body>
</html>