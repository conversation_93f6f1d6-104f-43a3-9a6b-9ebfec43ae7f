<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Punto Banco Elite - Provably Fair Baccarat</title>
    <link rel="stylesheet" href="baccarat.css">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="assets/diamond_favicon.svg" type="image/svg+xml">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span class="back-text">Back to Games</span>
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">Punto Banco Elite</h1>
                <p class="game-subtitle">Elegant Baccarat with Provable Fairness</p>
            </div>
            <div class="header-right">
                <div class="view-toggle">
                    <button id="standardViewBtn" class="view-btn active">Standard</button>
                    <button id="proViewBtn" class="view-btn">Pro View</button>
                </div>
                <button id="rulesBtn" class="info-btn">
                    <i class="fas fa-book"></i>
                    <span class="btn-text">Rules</span>
                </button>
                <button id="verifyBtn" class="info-btn">
                    <i class="fas fa-shield-alt"></i>
                    <span class="btn-text">Verify</span>
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- Game Section -->
            <div class="game-section">
                <!-- Game Status & Current Shoe -->
                <div class="game-status">
                    <div class="status-item">
                        <span class="status-label">Status:</span>
                        <span class="status-value" id="gameStatus">Place Your Bets</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Cards in Shoe:</span>
                        <span class="status-value" id="cardsInShoe">416</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Round:</span>
                        <span class="status-value" id="roundId">0</span>
                    </div>
                </div>
                
                <!-- Pro View Stats Bar (visible only in Pro View) -->
                <div class="pro-stats-bar" id="proStatsBar">
                    <div class="pro-stat">
                        <span class="pro-stat-label">Player Win%:</span>
                        <span class="pro-stat-value" id="playerWinPercent">0%</span>
                    </div>
                    <div class="pro-stat">
                        <span class="pro-stat-label">Banker Win%:</span>
                        <span class="pro-stat-value" id="bankerWinPercent">0%</span>
                    </div>
                    <div class="pro-stat">
                        <span class="pro-stat-label">Tie Win%:</span>
                        <span class="pro-stat-value" id="tieWinPercent">0%</span>
                    </div>
                    <div class="pro-stat">
                        <span class="pro-stat-label">Pair%:</span>
                        <span class="pro-stat-value" id="pairPercent">0%</span>
                    </div>
                    <div class="pro-stat">
                        <span class="pro-stat-label">ROI:</span>
                        <span class="pro-stat-value" id="roiValue">0%</span>
                    </div>
                </div>
                
                <!-- Baccarat Table -->
                <div class="baccarat-table">
                    <!-- Cards Display Area -->
                    <div class="cards-display">
                        <div class="hand player-hand">
                            <div class="hand-label">PLAYER</div>
                            <div class="hand-cards" id="playerCards"></div>
                            <div class="hand-score" id="playerScore">-</div>
                        </div>
                        
                        <div class="hand banker-hand">
                            <div class="hand-label">BANKER</div>
                            <div class="hand-cards" id="bankerCards"></div>
                            <div class="hand-score" id="bankerScore">-</div>
                        </div>
                    </div>
                    
                    <!-- Betting Areas -->
                    <div class="betting-areas">
                        <div class="main-bets">
                            <div class="bet-area" id="playerBetArea" data-bet="player">
                                <div class="bet-label">PLAYER</div>
                                <div class="bet-payout">PAYS 1:1</div>
                                <div class="bet-amount" id="playerBetAmount">0</div>
                                <div class="pro-bet-win-rate">Win: <span id="playerBetWinRate">0%</span></div>
                            </div>
                            
                            <div class="bet-area" id="tieBetArea" data-bet="tie">
                                <div class="bet-label">TIE</div>
                                <div class="bet-payout">PAYS 8:1</div>
                                <div class="bet-amount" id="tieBetAmount">0</div>
                                <div class="pro-bet-win-rate">Win: <span id="tieBetWinRate">0%</span></div>
                            </div>
                            
                            <div class="bet-area" id="bankerBetArea" data-bet="banker">
                                <div class="bet-label">BANKER</div>
                                <div class="bet-payout">PAYS 0.95:1</div>
                                <div class="bet-amount" id="bankerBetAmount">0</div>
                                <div class="pro-bet-win-rate">Win: <span id="bankerBetWinRate">0%</span></div>
                            </div>
                        </div>
                        
                        <div class="side-bets">
                            <div class="bet-area small" id="playerPairBetArea" data-bet="playerPair">
                                <div class="bet-label">PLAYER PAIR</div>
                                <div class="bet-payout">PAYS 11:1</div>
                                <div class="bet-amount" id="playerPairBetAmount">0</div>
                                <div class="pro-bet-win-rate">Win: <span id="playerPairWinRate">0%</span></div>
                            </div>
                            
                            <div class="bet-area small" id="bankerPairBetArea" data-bet="bankerPair">
                                <div class="bet-label">BANKER PAIR</div>
                                <div class="bet-payout">PAYS 11:1</div>
                                <div class="bet-amount" id="bankerPairBetAmount">0</div>
                                <div class="pro-bet-win-rate">Win: <span id="bankerPairWinRate">0%</span></div>
                            </div>
                            
                            <div class="bet-area small" id="perfectPairBetArea" data-bet="perfectPair">
                                <div class="bet-label">PERFECT PAIR</div>
                                <div class="bet-payout">PAYS 25:1</div>
                                <div class="bet-amount" id="perfectPairBetAmount">0</div>
                                <div class="pro-bet-win-rate">Win: <span id="perfectPairWinRate">0%</span></div>
                            </div>
                        </div>
                    </div>

                    <!-- Outcome Display -->
                    <div class="outcome-display hidden" id="outcomeDisplay">
                        <div class="outcome-message" id="outcomeMessage"></div>
                        <div class="winnings" id="winningsAmount"></div>
                    </div>
                </div>
                
                <!-- Game Controls -->
                <div class="game-controls">
                    <div class="chip-selection">
                        <div class="chip" data-value="1">1</div>
                        <div class="chip" data-value="5">5</div>
                        <div class="chip" data-value="25">25</div>
                        <div class="chip" data-value="100">100</div>
                        <div class="chip" data-value="500">500</div>
                    </div>
                    
                    <div class="action-buttons">
                        <button id="clearBtn" class="action-btn">Clear</button>
                        <button id="dealBtn" class="action-btn primary">Deal</button>
                        <button id="replayBtn" class="action-btn">Replay</button>
                    </div>
                </div>
                
                <!-- Credits Display -->
                <div class="credits-display">
                    <div class="credits-item">
                        <span class="credits-label">Credits:</span>
                        <span class="credits-value" id="credits">10000</span>
                    </div>
                    <div class="credits-item">
                        <span class="credits-label">Total Bet:</span>
                        <span class="credits-value" id="totalBet">0</span>
                    </div>
                    <div class="credits-item">
                        <span class="credits-label">Win:</span>
                        <span class="credits-value" id="win">0</span>
                    </div>
                </div>
            </div>

            <!-- Information Panel -->
            <div class="info-panel">
                <!-- Mobile Info Toggle (visible only on mobile) -->
                <div class="mobile-info-toggle">
                    <button id="mobileInfoToggleBtn" class="mobile-toggle-btn">
                        <i class="fas fa-chart-line"></i> Show Statistics
                    </button>
                </div>
                
                <!-- Baccarat Roads -->
                <div class="info-section roads-section">
                    <h3>Baccarat Roads</h3>
                    <div class="tabs">
                        <button class="tab-btn active" data-tab="beadPlate">Bead Plate</button>
                        <button class="tab-btn" data-tab="bigRoad">Big Road</button>
                        <button class="tab-btn" data-tab="bigEye">Big Eye</button>
                        <button class="tab-btn" data-tab="smallRoad">Small Road</button>
                        <button class="tab-btn" data-tab="cockroach">Cockroach</button>
                    </div>
                    <div class="tab-content">
                        <div class="tab-pane active" id="beadPlate">
                            <div class="bead-grid" id="beadPlateGrid"></div>
                        </div>
                        <div class="tab-pane" id="bigRoad">
                            <div class="big-road-grid" id="bigRoadGrid"></div>
                        </div>
                        <div class="tab-pane" id="bigEye">
                            <div class="derived-grid" id="bigEyeGrid"></div>
                        </div>
                        <div class="tab-pane" id="smallRoad">
                            <div class="derived-grid" id="smallRoadGrid"></div>
                        </div>
                        <div class="tab-pane" id="cockroach">
                            <div class="derived-grid" id="cockroachGrid"></div>
                        </div>
                    </div>
                </div>

                <!-- Pro Trend Analysis (visible only in Pro View) -->
                <div class="info-section pro-analysis-section" id="proAnalysisSection">
                    <h3>Trend Analysis</h3>
                    <div class="pro-analysis-content">
                        <div class="pro-chart">
                            <div class="chart-container" id="trendChart">
                                <div class="chart-placeholder">Win Trend Chart</div>
                            </div>
                        </div>
                        <div class="trend-stats">
                            <div class="trend-stat">
                                <span class="trend-label">Streak:</span>
                                <span class="trend-value" id="currentStreak">0 (P)</span>
                            </div>
                            <div class="trend-stat">
                                <span class="trend-label">Max Streak:</span>
                                <span class="trend-value" id="maxStreak">0 (B)</span>
                            </div>
                            <div class="trend-stat">
                                <span class="trend-label">Pattern:</span>
                                <span class="trend-value" id="currentPattern">None</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Provably Fair -->
                <div class="info-section fair-section">
                    <h3>Provably Fair</h3>
                    <div class="fair-info">
                        <div class="seed-display">
                            <div class="seed-item">
                                <span class="seed-label">Server Seed Hash:</span>
                                <span class="seed-value" id="serverSeedHash">...</span>
                            </div>
                            <div class="seed-item">
                                <span class="seed-label">Client Seed:</span>
                                <span class="seed-value" id="clientSeed">
                                    <input type="text" id="clientSeedInput" maxlength="32" placeholder="Enter your seed">
                                </span>
                            </div>
                        </div>
                        <button id="newSeedBtn" class="seed-btn">Generate New Client Seed</button>
                    </div>
                </div>

                <!-- Hand History -->
                <div class="info-section history-section">
                    <h3>Hand History</h3>
                    <div class="history-list" id="historyList">
                        <p class="no-history">No hands played yet</p>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="info-section stats-section">
                    <h3>Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Hands Played:</span>
                            <span class="stat-value" id="handsPlayed">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Player Wins:</span>
                            <span class="stat-value" id="playerWins">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Banker Wins:</span>
                            <span class="stat-value" id="bankerWins">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Ties:</span>
                            <span class="stat-value" id="tieWins">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Player Pairs:</span>
                            <span class="stat-value" id="playerPairs">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Banker Pairs:</span>
                            <span class="stat-value" id="bankerPairs">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- Pro Advanced Stats (visible only in Pro View) -->
                <div class="info-section pro-advanced-stats" id="proAdvancedStats">
                    <h3>Advanced Statistics</h3>
                    <div class="pro-stats-grid">
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Session Duration:</span>
                            <span class="pro-stat-value" id="sessionDuration">00:00:00</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Hands Per Hour:</span>
                            <span class="pro-stat-value" id="handsPerHour">0</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Avg. Bet Size:</span>
                            <span class="pro-stat-value" id="avgBetSize">0</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Net Profit:</span>
                            <span class="pro-stat-value" id="netProfit">0</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Return on Investment:</span>
                            <span class="pro-stat-value" id="returnOnInvestment">0%</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Win/Loss Ratio:</span>
                            <span class="pro-stat-value" id="winLossRatio">0.00</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Last 10 Hands:</span>
                            <span class="pro-stat-value" id="lastTenHands">-</span>
                        </div>
                        <div class="pro-stat-item">
                            <span class="pro-stat-label">Max Profit Hand:</span>
                            <span class="pro-stat-value" id="maxProfitHand">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- Pro Betting History (visible only in Pro View) -->
                <div class="info-section pro-betting-history" id="proBettingHistory">
                    <h3>Betting History</h3>
                    <div class="pro-betting-chart" id="bettingHistoryChart">
                        <div class="chart-placeholder">Betting Performance Chart</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div class="modal-overlay hidden" id="rulesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Baccarat Rules</h2>
                    <button class="close-modal-btn" id="closeRulesBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="rules-section">
                        <h3>Game Overview</h3>
                        <p>Baccarat is a comparing card game played between the "Player" and the "Banker". Each hand receives two or three cards, and the hand closest to a score of 9 wins.</p>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Card Values</h3>
                        <ul>
                            <li>Ace = 1 point</li>
                            <li>2-9 = Face value</li>
                            <li>10, Jack, Queen, King = 0 points</li>
                        </ul>
                        <p>The score of a hand is the sum of all cards, with only the rightmost digit used. For example, a hand with 7+8=15 has a score of 5.</p>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Game Flow</h3>
                        <ol>
                            <li>Place your bets on Player, Banker, or Tie</li>
                            <li>Two cards are dealt to both Player and Banker</li>
                            <li>If either hand has a total of 8 or 9 (a "natural"), both hands stand</li>
                            <li>If not, the Player hand may draw a third card according to fixed rules</li>
                            <li>The Banker hand may then draw a third card according to fixed rules</li>
                            <li>The hand closest to 9 wins</li>
                        </ol>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Third Card Rules for Player</h3>
                        <ul>
                            <li>If Player's first two cards total 0-5: Player draws a third card</li>
                            <li>If Player's first two cards total 6-7: Player stands</li>
                            <li>If Player's first two cards total 8-9: Player stands (Natural)</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Third Card Rules for Banker</h3>
                        <p>Banker's action depends on Banker's score and Player's third card (if drawn):</p>
                        <table class="rules-table">
                            <tr>
                                <th>Banker's Score</th>
                                <th>Player's Third Card</th>
                                <th>Banker Action</th>
                            </tr>
                            <tr>
                                <td>0-2</td>
                                <td>Any or no card</td>
                                <td>Draws</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>0-7, 9 or no card</td>
                                <td>Draws</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>8</td>
                                <td>Stands</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>2-7 or no card</td>
                                <td>Draws</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>0, 1, 8, 9</td>
                                <td>Stands</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>4-7 or no card</td>
                                <td>Draws</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>0-3, 8, 9</td>
                                <td>Stands</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>6-7</td>
                                <td>Draws</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>0-5, 8, 9 or no card</td>
                                <td>Stands</td>
                            </tr>
                            <tr>
                                <td>7-9</td>
                                <td>Any or no card</td>
                                <td>Stands</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Payouts and House Edge</h3>
                        <ul>
                            <li>Player Bet: Pays 1:1 (House Edge: 1.24%)</li>
                            <li>Banker Bet: Pays 1:1 minus 5% commission (House Edge: 1.06%)</li>
                            <li>Tie Bet: Pays 8:1 (House Edge: 14.36%)</li>
                            <li>Player Pair: Pays 11:1 (House Edge: 10.36%)</li>
                            <li>Banker Pair: Pays 11:1 (House Edge: 10.36%)</li>
                            <li>Perfect Pair: Pays 25:1 (House Edge: 13.03%)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verify Modal -->
        <div class="modal-overlay hidden" id="verifyModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Verify Fairness</h2>
                    <button class="close-modal-btn" id="closeVerifyBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="verify-section">
                        <h3>How Provably Fair Works</h3>
                        <p>Our Baccarat game uses a provably fair system to ensure complete transparency:</p>
                        <ol>
                            <li>Before the shoe starts, we generate a random server seed and publish its hash</li>
                            <li>You provide a client seed (or use our randomly generated one)</li>
                            <li>These seeds plus the round ID determine the entire shoe's card order</li>
                            <li>After the shoe is finished, we reveal the server seed for verification</li>
                        </ol>
                        <p><strong>Note:</strong> Player win chance is set to 29% to create a more challenging experience.</p>
                    </div>
                    
                    <div class="verify-section">
                        <h3>Verify a Previous Hand</h3>
                        <div class="verify-inputs">
                            <div class="input-group">
                                <label for="verifyServerSeed">Server Seed:</label>
                                <input type="text" id="verifyServerSeed" placeholder="Revealed server seed">
                            </div>
                            <div class="input-group">
                                <label for="verifyClientSeed">Client Seed:</label>
                                <input type="text" id="verifyClientSeed" placeholder="Your client seed">
                            </div>
                            <div class="input-group">
                                <label for="verifyShoeId">Shoe ID:</label>
                                <input type="number" id="verifyShoeId" placeholder="Shoe number">
                            </div>
                            <div class="input-group">
                                <label for="verifyRoundId">Round ID:</label>
                                <input type="number" id="verifyRoundId" placeholder="Round number">
                            </div>
                        </div>
                        <button id="verifyHandBtn" class="verify-btn">Verify Hand</button>
                        <div class="verify-result" id="verifyResult"></div>
                    </div>
                    
                    <div class="verify-section">
                        <h3>Current Shoe Information</h3>
                        <div class="current-shoe-info" id="currentShoeInfo">
                            <p>Deal a hand to see verification information</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="baccarat.js"></script>
</body>
</html>