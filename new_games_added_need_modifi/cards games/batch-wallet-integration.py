#!/usr/bin/env python3
"""
Batch Wallet Integration Script for Card Games
This script automatically applies wallet integration to multiple card games.
"""

import os
import re
import glob

# Define the wallet integration patterns
WALLET_HEADER = '''// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('{game_name}');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        balance = walletIntegration.balance;
        walletIntegration.updateBalanceDisplay();
    }
}'''

HTML_SCRIPTS = '''    <!-- Include API Service and Wallet Integration -->
    <script src="../../../api-service.js"></script>
    <script src="../shared-wallet-integration.js"></script>
    <script src="script.js"></script>'''

INIT_PATTERN = '''// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize wallet integration first
    await initializeWallet();
    
    // Load the game
    {load_function}();
});'''

# Game configurations
GAMES = [
    {
        'folder': 'ckjack Supreme: VIP',
        'game_name': 'blackjack_vip',
        'load_function': 'loadBlackjackVIPGame'
    },
    {
        'folder': 'Spanish 21: Midnight Rush',
        'game_name': 'spanish21',
        'load_function': 'loadSpanish21Game'
    },
    {
        'folder': 'Caribbean Stud: Island Kings',
        'game_name': 'caribbean_stud',
        'load_function': 'loadCaribbeanStudGame'
    },
    {
        'folder': 'CaribbeanStudAdventure',
        'game_name': 'caribbean_stud_adv',
        'load_function': 'loadCaribbeanStudAdvGame'
    },
    {
        'folder': 'Casino War: Global Conflict',
        'game_name': 'casino_war',
        'load_function': 'loadCasinoWarGame'
    },
    {
        'folder': 'CasinoWar_HighStack',
        'game_name': 'casino_war_hs',
        'load_function': 'loadCasinoWarHSGame'
    },
    {
        'folder': 'Dragon Tiger: Turbo Tables',
        'game_name': 'dragon_tiger',
        'load_function': 'loadDragonTigerGame'
    },
    {
        'folder': 'Three Card Poker: Maverick',
        'game_name': 'three_card_poker',
        'load_function': 'loadThreeCardPokerGame'
    },
    {
        'folder': 'threeCardPokerDeluxe',
        'game_name': 'three_card_poker_deluxe',
        'load_function': 'loadThreeCardPokerDeluxeGame'
    },
    {
        'folder': 'Texas Hold\'em: All-In Arena',
        'game_name': 'texas_holdem',
        'load_function': 'loadTexasHoldemGame'
    },
    {
        'folder': 'holdem',
        'game_name': 'holdem',
        'load_function': 'loadHoldemGame'
    },
    {
        'folder': 'Let It Ride: Frontier Fortune',
        'game_name': 'let_it_ride',
        'load_function': 'loadLetItRideGame'
    },
    {
        'folder': 'LetItRide_WildWest',
        'game_name': 'let_it_ride_ww',
        'load_function': 'loadLetItRideWWGame'
    },
    {
        'folder': 'Pai Gow: Dynasty Tiles',
        'game_name': 'pai_gow',
        'load_function': 'loadPaiGowGame'
    },
    {
        'folder': 'paiGowPowreTiles',
        'game_name': 'pai_gow_power',
        'load_function': 'loadPaiGowPowerGame'
    },
    {
        'folder': 'Pontoon: Ocean King',
        'game_name': 'pontoon',
        'load_function': 'loadPontoonGame'
    },
    {
        'folder': 'ProtoonPremier',
        'game_name': 'pontoon_premier',
        'load_function': 'loadPontoonPremierGame'
    },
    {
        'folder': 'Red Dog: Wild Streak',
        'game_name': 'red_dog',
        'load_function': 'loadRedDogGame'
    },
    {
        'folder': 'RedDogRush',
        'game_name': 'red_dog_rush',
        'load_function': 'loadRedDogRushGame'
    },
    {
        'folder': 'Roulette Royale: Diamond Edition',
        'game_name': 'roulette_royale',
        'load_function': 'loadRouletteRoyaleGame'
    },
    {
        'folder': 'Roulette VR: Immersive Spin',
        'game_name': 'roulette_vr',
        'load_function': 'loadRouletteVRGame'
    },
    {
        'folder': 'RouletteFirstPerson',
        'game_name': 'roulette_fp',
        'load_function': 'loadRouletteFPGame'
    },
    {
        'folder': 'SicBoGalaxy',
        'game_name': 'sic_bo',
        'load_function': 'loadSicBoGame'
    },
    {
        'folder': 'Craps Stadium: High Roller',
        'game_name': 'craps',
        'load_function': 'loadCrapsGame'
    },
    {
        'folder': 'Keno Quest: Golden Numbers',
        'game_name': 'keno',
        'load_function': 'loadKenoGame'
    },
    {
        'folder': 'Kenolandia',
        'game_name': 'kenolandia',
        'load_function': 'loadKenolandiaGame'
    },
    {
        'folder': 'videopoker',
        'game_name': 'video_poker',
        'load_function': 'loadVideoPokerGame'
    },
    {
        'folder': 'pokerplace',
        'game_name': 'poker_place',
        'load_function': 'loadPokerPlaceGame'
    }
]

def update_script_file(game_folder, game_name, load_function):
    """Update the script.js file for a game"""
    script_path = os.path.join(game_folder, 'script.js')
    
    if not os.path.exists(script_path):
        print(f"Script file not found: {script_path}")
        return False
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace balance initialization
        content = re.sub(
            r'// Game state\s*\nlet balance = \d+;',
            WALLET_HEADER.format(game_name=game_name),
            content
        )
        
        # Replace balance -= patterns with wallet integration
        content = re.sub(
            r'balance -= (\w+);?\s*\n\s*updateBalance\(\);?',
            '''// Use wallet integration to process bet
    if (!walletIntegration || !(await walletIntegration.processBet(\\1, {
        game_action: 'bet',
        bet_amount: \\1
    }))) {
        return;
    }
    
    // Update local balance reference
    balance = walletIntegration.balance;''',
            content
        )
        
        # Replace balance += patterns with wallet integration
        content = re.sub(
            r'balance \+= (\w+);?\s*\n\s*updateBalance\(\);?',
            '''// Process winnings through wallet integration
    if (\\1 > 0 && walletIntegration) {
        await walletIntegration.processWin(\\1, {
            game_action: 'game_end',
            win_amount: \\1
        });
        balance = walletIntegration.balance;
    }''',
            content
        )
        
        # Update initialization
        content = re.sub(
            r'document\.addEventListener\(\'DOMContentLoaded\', function\(\) \{\s*\n\s*updateBalance\(\);\s*\n\s*(\w+)\(\);\s*\n\}\);',
            INIT_PATTERN.format(load_function=load_function),
            content
        )
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated script.js for {game_folder}")
        return True
        
    except Exception as e:
        print(f"Error updating {script_path}: {e}")
        return False

def update_html_file(game_folder):
    """Update the index.html file for a game"""
    html_path = os.path.join(game_folder, 'index.html')
    
    if not os.path.exists(html_path):
        print(f"HTML file not found: {html_path}")
        return False
    
    try:
        with open(html_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace script inclusion
        content = re.sub(
            r'    <script src="script\.js"></script>\s*\n</body>\s*\n</html>',
            HTML_SCRIPTS + '\n</body>\n</html>',
            content
        )
        
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Updated index.html for {game_folder}")
        return True
        
    except Exception as e:
        print(f"Error updating {html_path}: {e}")
        return False

def main():
    """Main function to process all games"""
    base_dir = "."
    
    for game in GAMES:
        game_folder = os.path.join(base_dir, game['folder'])
        
        if not os.path.exists(game_folder):
            print(f"Game folder not found: {game_folder}")
            continue
        
        print(f"\nProcessing {game['folder']}...")
        
        # Update script file
        script_success = update_script_file(
            game_folder, 
            game['game_name'], 
            game['load_function']
        )
        
        # Update HTML file
        html_success = update_html_file(game_folder)
        
        if script_success and html_success:
            print(f"✓ Successfully updated {game['folder']}")
        else:
            print(f"✗ Failed to update {game['folder']}")

if __name__ == "__main__":
    main()
