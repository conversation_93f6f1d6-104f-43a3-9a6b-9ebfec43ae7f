// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('keno');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        balance = walletIntegration.balance;
        walletIntegration.updateBalanceDisplay();
    }
}

// Keno Quest: Golden Numbers - A game with extremely low win rate (<10%)
let kenoQuestGame = {
    selectedNumbers: new Set(),
    drawnNumbers: [],
    goldenNumbers: new Set(),
    betAmount: 50,
    isPlaying: false,
    gameMode: 'standard', // standard, turbo, golden
    payoutMultiplier: 1.0,
    difficulty: 'normal', // easy, normal, hard, extreme
    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game constants
const KENO_QUEST_MODES = {
    standard: { name: 'Standard', multiplier: 1.0, goldenChance: 0.05 },
    turbo: { name: 'Turbo', multiplier: 0.8, goldenChance: 0.03 },
    golden: { name: 'Golden', multiplier: 0.7, goldenChance: 0.15 }
};

const KENO_QUEST_DIFFICULTIES = {
    easy: { name: 'Easy', houseEdge: 0.15, drawBias: 0.1 },
    normal: { name: 'Normal', houseEdge: 0.25, drawBias: 0.2 },
    hard: { name: 'Hard', houseEdge: 0.35, drawBias: 0.3 },
    extreme: { name: 'Extreme', houseEdge: 0.45, drawBias: 0.4 }
};

// Initialize the game
function loadKenoQuestGame() {
    generateKenoQuestBoard();
    updateKenoQuestDisplay();
    
    // Set up event listeners
    document.getElementById('playKenoQuest').addEventListener('click', playKenoQuest);
    document.getElementById('clearKenoQuest').addEventListener('click', clearKenoQuest);
    document.getElementById('quickPickKenoQuest').addEventListener('click', quickPickKenoQuest);
    
    document.getElementById('kenoQuestMode').addEventListener('change', function() {
        kenoQuestGame.gameMode = this.value;
        updateKenoQuestDisplay();
    });
    
    document.getElementById('kenoQuestDifficulty').addEventListener('change', function() {
        kenoQuestGame.difficulty = this.value;
        updateKenoQuestDisplay();
    });
    
    document.getElementById('kenoQuestBet').addEventListener('input', updateKenoQuestDisplay);
    
    // Load game content
    document.getElementById('gameContent').innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Left Panel: Game Controls -->
            <div class="bg-purple-900/50 p-4 rounded-xl border border-purple-500/50">
                <h3 class="text-xl font-bold text-purple-300 mb-4">Game Controls</h3>
                
                <div class="mb-4">
                    <label class="block text-blue-300 mb-2">Bet Amount</label>
                    <input type="number" id="kenoQuestBet" min="10" max="1000" step="10" value="50" 
                           class="w-full bg-purple-800 text-white border border-purple-500 rounded p-2">
                </div>
                
                <div class="mb-4">
                    <label class="block text-blue-300 mb-2">Game Mode</label>
                    <select id="kenoQuestMode" class="w-full bg-purple-800 text-white border border-purple-500 rounded p-2">
                        <option value="standard">Standard</option>
                        <option value="turbo">Turbo (Faster, Lower Payouts)</option>
                        <option value="golden">Golden (More Golden Numbers)</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-blue-300 mb-2">Difficulty</label>
                    <select id="kenoQuestDifficulty" class="w-full bg-purple-800 text-white border border-purple-500 rounded p-2">
                        <option value="easy">Easy</option>
                        <option value="normal">Normal</option>
                        <option value="hard">Hard</option>
                        <option value="extreme">Extreme</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <p class="text-blue-300 mb-2">Selected: <span id="kenoQuestSelectedCount">0</span>/10</p>
                    <p class="text-blue-300 mb-2">Potential Payout: <span id="kenoQuestPayout">$0</span></p>
                </div>
                
                <div class="grid grid-cols-3 gap-2">
                    <button id="playKenoQuest" class="bg-gradient-to-r from-purple-600 to-blue-600 text-white py-2 px-4 rounded-lg font-bold disabled:opacity-50">
                        PLAY
                    </button>
                    <button id="quickPickKenoQuest" class="bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-2 px-4 rounded-lg font-bold">
                        QUICK
                    </button>
                    <button id="clearKenoQuest" class="bg-gradient-to-r from-red-600 to-pink-600 text-white py-2 px-4 rounded-lg font-bold">
                        CLEAR
                    </button>
                </div>
                
                <div class="mt-4 p-2 bg-purple-800/50 rounded-lg">
                    <p id="kenoQuestStatus" class="text-center text-purple-300">Select 1-10 numbers</p>
                </div>
            </div>
            
            <!-- Center Panel: Keno Board -->
            <div class="bg-purple-900/50 p-4 rounded-xl border border-purple-500/50 col-span-2">
                <h3 class="text-xl font-bold text-purple-300 mb-4">Keno Board</h3>
                <div id="kenoQuestBoard" class="grid grid-cols-8 gap-2"></div>
            </div>
        </div>
        
        <div class="mt-6 bg-purple-900/50 p-4 rounded-xl border border-purple-500/50">
            <h3 class="text-xl font-bold text-purple-300 mb-4">Game Statistics</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-purple-800/50 p-2 rounded-lg">
                    <p class="text-blue-300">Games Played: <span id="kenoQuestGamesPlayed">0</span></p>
                </div>
                <div class="bg-purple-800/50 p-2 rounded-lg">
                    <p class="text-blue-300">Win Rate: <span id="kenoQuestWinRate">0%</span></p>
                </div>
                <div class="bg-purple-800/50 p-2 rounded-lg">
                    <p class="text-blue-300">Total Wagered: <span id="kenoQuestTotalWagered">0</span></p>
                </div>
                <div class="bg-purple-800/50 p-2 rounded-lg">
                    <p class="text-blue-300">Total Won: <span id="kenoQuestTotalWon">0</span></p>
                </div>
            </div>
        </div>
    `;
}

function generateKenoQuestBoard() {
    const board = document.getElementById('kenoQuestBoard');
    board.innerHTML = '';
    
    for (let i = 1; i <= 80; i++) {
        const number = document.createElement('div');
        number.className = 'w-8 h-8 bg-gradient-to-br from-purple-800 to-indigo-900 border border-purple-500 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all hover:scale-105';
        number.textContent = i;
        number.dataset.number = i;
        number.addEventListener('click', () => toggleKenoQuestNumber(i));
        board.appendChild(number);
    }
}

function toggleKenoQuestNumber(number) {
    if (kenoQuestGame.isPlaying) return;
    
    const numberElement = document.querySelector(`[data-number="${number}"]`);
    
    if (kenoQuestGame.selectedNumbers.has(number)) {
        // Deselect
        kenoQuestGame.selectedNumbers.delete(number);
        numberElement.className = 'w-8 h-8 bg-gradient-to-br from-purple-800 to-indigo-900 border border-purple-500 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all hover:scale-105';
    } else {
        // Select (max 10 numbers)
        if (kenoQuestGame.selectedNumbers.size < 10) {
            kenoQuestGame.selectedNumbers.add(number);
            numberElement.className = 'w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 border border-blue-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all scale-105';
        }
    }
    
    updateKenoQuestDisplay();
}

function quickPickKenoQuest() {
    if (kenoQuestGame.isPlaying) return;
    
    clearKenoQuest();
    
    // Pick random numbers
    const pickCount = Math.floor(Math.random() * 10) + 1; // 1-10 numbers
    while (kenoQuestGame.selectedNumbers.size < pickCount) {
        const randomNumber = Math.floor(Math.random() * 80) + 1;
        if (!kenoQuestGame.selectedNumbers.has(randomNumber)) {
            toggleKenoQuestNumber(randomNumber);
        }
    }
}

function clearKenoQuest() {
    if (kenoQuestGame.isPlaying) return;
    
    kenoQuestGame.selectedNumbers.clear();
    
    document.querySelectorAll('#kenoQuestBoard div').forEach(div => {
        div.className = 'w-8 h-8 bg-gradient-to-br from-purple-800 to-indigo-900 border border-purple-500 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all hover:scale-105';
    });
    
    updateKenoQuestDisplay();
}

function updateKenoQuestDisplay() {
    document.getElementById('kenoQuestSelectedCount').textContent = kenoQuestGame.selectedNumbers.size;
    
    const playButton = document.getElementById('playKenoQuest');
    playButton.disabled = kenoQuestGame.selectedNumbers.size === 0;
    
    // Update potential payout
    const betAmount = parseInt(document.getElementById('kenoQuestBet').value) || 0;
    const selectedCount = kenoQuestGame.selectedNumbers.size;
    const maxPayout = getKenoQuestMaxPayout(selectedCount);
    
    // Apply mode multiplier
    const modeMultiplier = KENO_QUEST_MODES[kenoQuestGame.gameMode].multiplier;
    const difficultyEdge = KENO_QUEST_DIFFICULTIES[kenoQuestGame.difficulty].houseEdge;
    
    // Calculate potential payout with house edge
    const adjustedPayout = maxPayout * modeMultiplier * (1 - difficultyEdge);
    
    document.getElementById('kenoQuestPayout').textContent = `$${Math.floor(betAmount * adjustedPayout)}`;
    
    // Update statistics
    document.getElementById('kenoQuestGamesPlayed').textContent = kenoQuestGame.stats.gamesPlayed;
    const winRate = kenoQuestGame.stats.gamesPlayed > 0 ? 
        ((kenoQuestGame.stats.gamesWon / kenoQuestGame.stats.gamesPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('kenoQuestWinRate').textContent = `${winRate}%`;
    document.getElementById('kenoQuestTotalWagered').textContent = kenoQuestGame.stats.totalWagered;
    document.getElementById('kenoQuestTotalWon').textContent = kenoQuestGame.stats.totalWon;
}

function getKenoQuestMaxPayout(selectedCount) {
    // Intentionally low payouts to ensure <10% win rate
    const payouts = {
        1: 2.5, 2: 10, 3: 30, 4: 75, 5: 250,
        6: 800, 7: 1500, 8: 3000, 9: 8000, 10: 15000
    };
    return payouts[selectedCount] || 0;
}

async function playKenoQuest() {
    const betAmount = parseInt(document.getElementById('kenoQuestBet').value);

    if (kenoQuestGame.selectedNumbers.size === 0) {
        alert('Please select at least one number!');
        return;
    }

    // Use wallet integration to process bet
    if (!walletIntegration || !(await walletIntegration.processBet(betAmount, {
        game_action: 'play_keno',
        bet_amount: betAmount,
        selected_numbers: Array.from(kenoQuestGame.selectedNumbers),
        game_mode: kenoQuestGame.gameMode,
        difficulty: kenoQuestGame.difficulty
    }))) {
        return;
    }

    // Update local balance reference
    balance = walletIntegration.balance;
    kenoQuestGame.isPlaying = true;
    kenoQuestGame.betAmount = betAmount;
    kenoQuestGame.stats.totalWagered += betAmount;
    
    document.getElementById('playKenoQuest').disabled = true;
    document.getElementById('kenoQuestStatus').textContent = 'Drawing numbers...';
    
    // Generate golden numbers (special high-value numbers)
    kenoQuestGame.goldenNumbers.clear();
    const goldenChance = KENO_QUEST_MODES[kenoQuestGame.gameMode].goldenChance;
    for (let i = 1; i <= 80; i++) {
        if (Math.random() < goldenChance) {
            kenoQuestGame.goldenNumbers.add(i);
        }
    }
    
    // Draw 20 numbers with bias AGAINST player selections
    drawBiasedNumbers();
    
    // Animate the draw
    animateKenoQuestDraw(betAmount);
}

function drawBiasedNumbers() {
    kenoQuestGame.drawnNumbers = [];
    const drawBias = KENO_QUEST_DIFFICULTIES[kenoQuestGame.difficulty].drawBias;
    
    // Create a pool of numbers with bias against player selections
    const numberPool = [];
    for (let i = 1; i <= 80; i++) {
        // Add each number to the pool multiple times based on bias
        const count = kenoQuestGame.selectedNumbers.has(i) ? 
            Math.floor(10 * (1 - drawBias)) : // Fewer entries for selected numbers
            Math.floor(10 * (1 + drawBias));  // More entries for non-selected numbers
        
        for (let j = 0; j < count; j++) {
            numberPool.push(i);
        }
    }
    
    // Shuffle the pool
    for (let i = numberPool.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numberPool[i], numberPool[j]] = [numberPool[j], numberPool[i]];
    }
    
    // Draw 20 unique numbers from the pool
    const drawnSet = new Set();
    let poolIndex = 0;
    
    while (drawnSet.size < 20 && poolIndex < numberPool.length) {
        drawnSet.add(numberPool[poolIndex]);
        poolIndex++;
    }
    
    kenoQuestGame.drawnNumbers = Array.from(drawnSet);
    
    // If we don't have 20 numbers yet (unlikely), fill with random numbers
    while (kenoQuestGame.drawnNumbers.length < 20) {
        const randomNumber = Math.floor(Math.random() * 80) + 1;
        if (!kenoQuestGame.drawnNumbers.includes(randomNumber)) {
            kenoQuestGame.drawnNumbers.push(randomNumber);
        }
    }
}

function animateKenoQuestDraw(betAmount) {
    let drawnSoFar = 0;
    
    // Determine animation speed based on game mode
    const drawSpeed = kenoQuestGame.gameMode === 'turbo' ? 50 : 150;
    
    const drawInterval = setInterval(() => {
        if (drawnSoFar < kenoQuestGame.drawnNumbers.length) {
            const drawnNumber = kenoQuestGame.drawnNumbers[drawnSoFar];
            const numberElement = document.querySelector(`[data-number="${drawnNumber}"]`);
            
            // Check if it's a golden number
            const isGolden = kenoQuestGame.goldenNumbers.has(drawnNumber);
            
            if (kenoQuestGame.selectedNumbers.has(drawnNumber)) {
                // Hit
                numberElement.className = isGolden ? 
                    'w-8 h-8 bg-gradient-to-br from-yellow-400 to-amber-600 border border-yellow-300 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all scale-110' :
                    'w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-700 border border-green-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all scale-105';
            } else {
                // Miss
                numberElement.className = isGolden ?
                    'w-8 h-8 bg-gradient-to-br from-yellow-500 to-amber-700 border border-yellow-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all' :
                    'w-8 h-8 bg-gradient-to-br from-red-500 to-pink-700 border border-red-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all';
            }
            
            drawnSoFar++;
        } else {
            clearInterval(drawInterval);
            calculateKenoQuestResult(betAmount);
        }
    }, drawSpeed);
}

async function calculateKenoQuestResult(betAmount) {
    // Count hits
    let hits = 0;
    let goldenHits = 0;
    
    kenoQuestGame.selectedNumbers.forEach(number => {
        if (kenoQuestGame.drawnNumbers.includes(number)) {
            hits++;
            if (kenoQuestGame.goldenNumbers.has(number)) {
                goldenHits++;
            }
        }
    });
    
    // Calculate payout
    const selectedCount = kenoQuestGame.selectedNumbers.size;
    const payout = getKenoQuestPayout(selectedCount, hits);
    
    // Apply mode multiplier and difficulty
    const modeMultiplier = KENO_QUEST_MODES[kenoQuestGame.gameMode].multiplier;
    const difficultyEdge = KENO_QUEST_DIFFICULTIES[kenoQuestGame.difficulty].houseEdge;
    
    // Golden bonus (very small to maintain low win rate)
    const goldenBonus = goldenHits * 0.1;
    
    // Calculate final winnings with house edge
    const adjustedPayout = payout * modeMultiplier * (1 - difficultyEdge) * (1 + goldenBonus);
    const winnings = Math.floor(betAmount * adjustedPayout);
    
    // Update game stats
    kenoQuestGame.stats.gamesPlayed++;
    
    if (winnings > 0) {
        kenoQuestGame.stats.gamesWon++;
        kenoQuestGame.stats.totalWon += winnings;
        
        if (winnings > kenoQuestGame.stats.biggestWin) {
            kenoQuestGame.stats.biggestWin = winnings;
        }
        
        // Update streak data
        kenoQuestGame.streakData.currentWinStreak++;
        kenoQuestGame.streakData.currentLossStreak = 0;
        
        if (kenoQuestGame.streakData.currentWinStreak > kenoQuestGame.streakData.longestWinStreak) {
            kenoQuestGame.streakData.longestWinStreak = kenoQuestGame.streakData.currentWinStreak;
        }
    } else {
        // Update loss streak
        kenoQuestGame.streakData.currentLossStreak++;
        kenoQuestGame.streakData.currentWinStreak = 0;
        
        if (kenoQuestGame.streakData.currentLossStreak > kenoQuestGame.streakData.longestLossStreak) {
            kenoQuestGame.streakData.longestLossStreak = kenoQuestGame.streakData.currentLossStreak;
        }
    }
    
    // Process winnings through wallet integration
    if (winnings > 0 && walletIntegration) {
        await walletIntegration.processWin(winnings, {
            game_action: 'keno_result',
            bet_amount: kenoQuestGame.betAmount,
            selected_numbers: Array.from(kenoQuestGame.selectedNumbers),
            drawn_numbers: kenoQuestGame.drawnNumbers,
            golden_numbers: Array.from(kenoQuestGame.goldenNumbers),
            hits: hits,
            win_amount: winnings,
            game_mode: kenoQuestGame.gameMode
        });
        balance = walletIntegration.balance;
    }
    
    // Update display
    let resultMessage = `<span class="text-blue-300">You hit ${hits}/${selectedCount} numbers!</span>`;
    
    if (goldenHits > 0) {
        resultMessage += ` <span class="text-yellow-300">(${goldenHits} golden hits!)</span>`;
    }
    
    if (winnings > 0) {
        resultMessage += ` <span class="text-green-400">You won ${winnings} GA!</span>`;
    } else {
        resultMessage += ` <span class="text-red-400">No win this time.</span>`;
    }
    
    document.getElementById('kenoQuestStatus').innerHTML = resultMessage;
    
    // Reset for next game
    kenoQuestGame.isPlaying = false;
    document.getElementById('playKenoQuest').disabled = false;
    
    updateKenoQuestDisplay();
}

function getKenoQuestPayout(selected, hits) {
    // Intentionally low payouts to ensure <10% win rate
    const payoutTable = {
        1: { 1: 2.5 },
        2: { 2: 10 },
        3: { 2: 1, 3: 30 },
        4: { 2: 0.5, 3: 3, 4: 75 },
        5: { 3: 1, 4: 15, 5: 250 },
        6: { 3: 0.5, 4: 5, 5: 50, 6: 800 },
        7: { 4: 1, 5: 20, 6: 200, 7: 1500 },
        8: { 4: 0.5, 5: 10, 6: 50, 7: 500, 8: 3000 },
        9: { 5: 2, 6: 25, 7: 150, 8: 1000, 9: 8000 },
        10: { 5: 1, 6: 10, 7: 50, 8: 250, 9: 2000, 10: 15000 }
    };
    
    return payoutTable[selected]?.[hits] || 0;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize wallet integration first
    await initializeWallet();

    // Load the game
    loadKenoQuestGame();
});