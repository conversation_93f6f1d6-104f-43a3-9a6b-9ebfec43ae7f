// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Casino War: Global Conflict Implementation with <10% win rate
const WAR_CARD_SUITS = ['♠', '♥', '♦', '♣'];
const WAR_CARD_VALUES = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

// Extremely reduced payouts for <10% win rate
const WAR_PAYOUTS = {
    WIN: 0.8,           // Reduced from 1:1 to 0.8:1
    WAR_WIN: 1.2,       // Reduced from 2:1 to 1.2:1
    TIE_BONUS: 5,       // Reduced from 10:1 to 5:1
    SURRENDER: 0.3      // Only get 30% back on surrender
};

// Global conflict zones with extreme difficulty
const CONFLICT_ZONES = [
    {
        name: 'eastern_front',
        description: 'Eastern Front (9% win rate)',
        winRate: 0.09,
        warAdvantage: 0.85,
        tieFrequency: 0.12,
        dealerBias: 0.88
    },
    {
        name: 'pacific_theater',
        description: 'Pacific Theater (7% win rate)',
        winRate: 0.07,
        warAdvantage: 0.82,
        tieFrequency: 0.10,
        dealerBias: 0.90
    },
    {
        name: 'nuclear_standoff',
        description: 'Nuclear Standoff (5% win rate)',
        winRate: 0.05,
        warAdvantage: 0.80,
        tieFrequency: 0.08,
        dealerBias: 0.92
    }
];

let casinoWarGame = {
    deck: [],
    playerCard: null,
    dealerCard: null,
    warCards: [],
    gamePhase: 'betting', // betting, dealt, war, complete
    betAmount: 0,
    tieBet: 0,
    isInWar: false,
    warRound: 0,
    totalWin: 0,
    currentZone: 'eastern_front',
    conflictLevel: 1,
    battlePoints: 0,
    sessionStats: {
        battlesPlayed: 0,
        battlesWon: 0,
        warsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        surrenders: 0,
        tieWins: 0
    }
};

function loadCasinoWarGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-6xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-4xl font-bold cyber-title mb-4">Casino War: Global Conflict</h3>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div>Conflict Level: <span id="conflictLevel" class="text-neon-purple">1</span></div>
                    <div>Battle Points: <span id="battlePoints" class="text-neon-blue">0</span></div>
                    <div>Current Zone: <span id="currentZone" class="text-neon-green">Eastern Front</span></div>
                </div>
                <div class="mt-2">
                    <select id="conflictZoneSelect" class="cyber-select bg-cyber-dark border border-neon-purple rounded px-4 py-2">
                        <option value="eastern_front">Eastern Front (9% win rate)</option>
                        <option value="pacific_theater">Pacific Theater (7% win rate)</option>
                        <option value="nuclear_standoff">Nuclear Standoff (5% win rate)</option>
                    </select>
                </div>
            </div>

            <!-- Battle Arena -->
            <div class="cyber-panel p-8">
                <!-- Enemy Command -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-red-400 mb-4">🎖️ Enemy Command</h4>
                    <div id="dealerCardArea" class="flex justify-center mb-4 min-h-[140px] items-center">
                        <div id="dealerCardSlot" class="card-slot border-2 border-red-500/30 rounded-lg w-24 h-32 flex items-center justify-center text-red-400">
                            Ready for Battle
                        </div>
                    </div>
                    <div id="dealerCardValue" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- War Zone -->
                <div class="text-center mb-6">
                    <div id="warStatus" class="text-2xl font-bold text-neon-pink mb-4">Choose your battlefield!</div>
                    <div id="warPhase" class="text-lg text-neon-blue">Deployment Phase</div>
                    <div id="warCards" class="flex justify-center space-x-2 mt-4 min-h-[60px]">
                        <!-- War cards will appear here -->
                    </div>
                </div>

                <!-- Player Command -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-neon-purple mb-4">⚔️ Your Forces</h4>
                    <div id="playerCardArea" class="flex justify-center mb-4 min-h-[140px] items-center">
                        <div id="playerCardSlot" class="card-slot border-2 border-neon-purple/30 rounded-lg w-24 h-32 flex items-center justify-center text-neon-purple">
                            Awaiting Orders
                        </div>
                    </div>
                    <div id="playerCardValue" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- Command Center -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Main Battle Bet -->
                    <div class="cyber-card border-neon-pink">
                        <h5 class="text-lg font-semibold text-neon-pink mb-3">⚡ Battle Wager</h5>
                        <div class="space-y-3">
                            <input type="range" id="battleBetSlider" min="50" max="2000" value="200" 
                                   class="w-full accent-neon-pink">
                            <div class="text-center">
                                <span id="battleBetAmount" class="text-xl font-bold text-neon-pink">200 GA</span>
                            </div>
                            <button id="deployForces" class="w-full cyber-button-primary py-2 rounded-lg font-semibold">
                                DEPLOY FORCES
                            </button>
                        </div>
                    </div>

                    <!-- War Actions -->
                    <div class="cyber-card border-neon-blue">
                        <h5 class="text-lg font-semibold text-neon-blue mb-3">🔥 War Actions</h5>
                        <div class="space-y-3">
                            <div class="text-center text-gray-400">
                                <div>War Bet: Double Stakes</div>
                                <div id="warBetAmount" class="text-xl font-bold text-neon-blue">0 GA</div>
                            </div>
                            <button id="goToWar" class="w-full cyber-button-secondary py-2 rounded-lg font-semibold" disabled>
                                DECLARE WAR
                            </button>
                            <button id="surrender" class="w-full cyber-button-danger py-2 rounded-lg font-semibold" disabled>
                                SURRENDER
                            </button>
                        </div>
                    </div>

                    <!-- Tie Bet -->
                    <div class="cyber-card border-neon-green">
                        <h5 class="text-lg font-semibold text-neon-green mb-3">🎯 Tie Prediction</h5>
                        <div class="space-y-3">
                            <div class="text-center">
                                <div class="text-sm text-gray-400">Optional tie bet (5:1)</div>
                                <input type="number" id="tieBetInput" value="0" min="0" max="500" step="25"
                                       class="w-full bg-black/50 border border-neon-green/30 rounded px-2 py-1 text-white text-center">
                            </div>
                            <label class="flex items-center justify-center space-x-2">
                                <input type="checkbox" id="tieBetCheck" class="accent-neon-green">
                                <span class="text-neon-green">Predict Stalemate</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button id="newBattleButton" class="cyber-button-primary px-8 py-3 rounded-lg font-semibold" 
                            onclick="resetWarGame()">
                        NEW CAMPAIGN
                    </button>
                    <button id="tacticsButton" class="cyber-button-secondary px-8 py-3 rounded-lg font-semibold"
                            onclick="showWarRules()">
                        BATTLE TACTICS
                    </button>
                </div>

                <!-- Battle Result -->
                <div id="battleResult" class="text-center text-2xl font-bold min-h-[60px] flex items-center justify-center">
                    <!-- Result will appear here -->
                </div>
            </div>

            <!-- War Statistics -->
            <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="cyber-card border-neon-purple text-center">
                    <div class="text-2xl font-bold text-neon-purple" id="battlesPlayed">0</div>
                    <div class="text-sm text-gray-400">Battles</div>
                </div>
                <div class="cyber-card border-neon-green text-center">
                    <div class="text-2xl font-bold text-neon-green" id="battlesWon">0</div>
                    <div class="text-sm text-gray-400">Victories</div>
                </div>
                <div class="cyber-card border-neon-pink text-center">
                    <div class="text-2xl font-bold text-neon-pink" id="totalWagered">0</div>
                    <div class="text-sm text-gray-400">Total Deployed</div>
                </div>
                <div class="cyber-card border-neon-blue text-center">
                    <div class="text-2xl font-bold text-neon-blue" id="biggestWin">0</div>
                    <div class="text-sm text-gray-400">Greatest Victory</div>
                </div>
            </div>

            <!-- War Payouts -->
            <div class="mt-8 cyber-panel">
                <h4 class="text-xl font-bold text-neon-purple mb-4 text-center">Combat Rewards (Reduced Payouts)</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div class="text-center">
                        <div class="font-bold text-neon-pink">Standard Victory</div>
                        <div class="text-gray-300">0.8:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-blue">War Victory</div>
                        <div class="text-gray-300">1.2:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-green">Tie Prediction</div>
                        <div class="text-gray-300">5:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-red-400">Surrender</div>
                        <div class="text-gray-300">30% Return</div>
                    </div>
                </div>
                <div class="text-center mt-4 text-xs text-gray-400">
                    ⚠️ Global Conflict: Extreme warfare conditions with reduced rewards!
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div id="rulesModal" class="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 hidden flex items-center justify-center">
            <div class="cyber-panel max-w-2xl mx-4">
                <h3 class="text-2xl font-bold text-neon-purple mb-4">Casino War: Global Conflict Rules</h3>
                <div class="space-y-4 text-gray-300">
                    <p><strong class="text-neon-pink">Objective:</strong> Deploy superior forces to defeat enemy command!</p>
                    <p><strong class="text-neon-blue">Combat System:</strong></p>
                    <ul class="list-disc list-inside space-y-2 ml-4">
                        <li>Both sides deploy one unit (card)</li>
                        <li>Higher rank wins the battle (Ace = 14, King = 13, etc.)</li>
                        <li>Ties trigger WAR - deploy 3 hidden + 1 battle card</li>
                        <li>War winner gets enhanced rewards</li>
                        <li>Surrender option returns 30% of bet</li>
                        <li>Optional tie bet predicts stalemate</li>
                    </ul>
                    <p><strong class="text-neon-green">Conflict Zones:</strong> Choose your battlefield difficulty!</p>
                    <p><strong class="text-red-400">Warning:</strong> Global conflict features extreme combat conditions and reduced payouts!</p>
                </div>
                <button onclick="hideWarRules()" class="mt-6 cyber-button-primary px-6 py-2 rounded-lg font-semibold">
                    CLOSE BRIEFING
                </button>
            </div>
        </div>
    `;
    
    setupWarGame();
}

function setupWarGame() {
    // Event listeners
    document.getElementById('battleBetSlider').addEventListener('input', updateBattleBet);
    document.getElementById('deployForces').addEventListener('click', deployForces);
    document.getElementById('goToWar').addEventListener('click', declareWar);
    document.getElementById('surrender').addEventListener('click', surrenderBattle);
    document.getElementById('tieBetCheck').addEventListener('change', updateTieBet);
    document.getElementById('tieBetInput').addEventListener('input', updateTieBet);
    document.getElementById('conflictZoneSelect').addEventListener('change', updateConflictZone);
    
    updateWarDisplay();
    updateWarStats();
}

function updateConflictZone() {
    const zone = document.getElementById('conflictZoneSelect').value;
    casinoWarGame.currentZone = zone;
    
    const zoneData = CONFLICT_ZONES.find(z => z.name === zone);
    document.getElementById('currentZone').textContent = zoneData.description.split(' (')[0];
    document.getElementById('warStatus').textContent = `Battlefield: ${zoneData.description}`;
}

function updateBattleBet() {
    const battleBet = parseInt(document.getElementById('battleBetSlider').value);
    document.getElementById('battleBetAmount').textContent = `${battleBet} GA`;
    document.getElementById('warBetAmount').textContent = `${battleBet * 2} GA`;
}

function updateTieBet() {
    const isChecked = document.getElementById('tieBetCheck').checked;
    const tieBetAmount = parseInt(document.getElementById('tieBetInput').value) || 0;
    casinoWarGame.tieBet = isChecked ? tieBetAmount : 0;
}

function createBiasedWarDeck() {
    const deck = [];
    const zoneData = CONFLICT_ZONES.find(z => z.name === casinoWarGame.currentZone);
    
    // Create multiple decks with heavy bias
    for (let deckCount = 0; deckCount < 4; deckCount++) {
        for (const suit of WAR_CARD_SUITS) {
            for (const value of WAR_CARD_VALUES) {
                deck.push({ suit, value });
            }
        }
    }
    
    // Heavily bias deck for dealer advantage
    const biasedDeck = [];
    
    deck.forEach(card => {
        const cardValue = getWarCardValue(card);
        
        // Reduce high cards for player, increase for dealer
        if (cardValue >= 11) { // J, Q, K, A
            // Dealer gets more high cards
            if (Math.random() < 0.7) {
                biasedDeck.push(card);
            }
            // Player gets fewer high cards
            if (Math.random() < 0.3 * zoneData.dealerBias) {
                biasedDeck.push({ ...card });
            }
        } else {
            // More low cards overall
            biasedDeck.push(card);
            if (Math.random() < 0.5) {
                biasedDeck.push({ ...card });
            }
        }
    });
    
    return shuffleWarDeck(biasedDeck);
}

function shuffleWarDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function createWarCardElement(card, isHidden = false) {
    const cardElement = document.createElement('div');
    cardElement.className = 'war-card inline-block w-20 h-28 bg-white rounded-lg border-2 border-gray-300 m-1 flex flex-col items-center justify-center text-lg font-bold shadow-lg transition-all duration-500 hover:scale-105';
    
    if (isHidden) {
        cardElement.innerHTML = '🎖️';
        cardElement.className += ' bg-gradient-to-br from-red-900 to-black text-red-400';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.innerHTML = `
            <div class="${isRed ? 'text-red-600' : 'text-black'}">${card.value}</div>
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-2xl">${card.suit}</div>
        `;
    }
    
    return cardElement;
}

function deployForces() {
    const battleBet = parseInt(document.getElementById('battleBetSlider').value);
    
    if (balance < battleBet + casinoWarGame.tieBet) {
        document.getElementById('warStatus').textContent = 'Insufficient resources for deployment!';
        return;
    }
    
    // Deduct bets
    balance -= battleBet + casinoWarGame.tieBet;
    updateBalance();
    
    // Initialize battle
    casinoWarGame.deck = createBiasedWarDeck();
    casinoWarGame.betAmount = battleBet;
    casinoWarGame.gamePhase = 'dealt';
    casinoWarGame.isInWar = false;
    casinoWarGame.warRound = 0;
    casinoWarGame.totalWin = 0;
    
    // Deal cards with bias
    dealBiasedWarCards();
    
    displayWarCards();
    
    // Disable deploy button
    document.getElementById('deployForces').disabled = true;
    
    document.getElementById('warStatus').textContent = 'Forces deployed! Awaiting battle outcome...';
    document.getElementById('warPhase').textContent = 'Combat Phase';
    
    // Determine battle outcome
    setTimeout(() => {
        determineBattleOutcome();
    }, 2000);
    
    updateWarDisplay();
}

function dealBiasedWarCards() {
    const zoneData = CONFLICT_ZONES.find(z => z.name === casinoWarGame.currentZone);
    
    // Deal player card with bias toward lower values
    let playerCard;
    let attempts = 0;
    
    do {
        playerCard = casinoWarGame.deck.pop();
        attempts++;
        
        const cardValue = getWarCardValue(playerCard);
        // Heavy bias against high cards for player
        if (cardValue >= 11 && Math.random() < 0.85 * zoneData.dealerBias && attempts < 30) {
            casinoWarGame.deck.unshift(playerCard);
            continue;
        }
        break;
    } while (attempts < 35);
    
    casinoWarGame.playerCard = playerCard;
    
    // Deal dealer card with bias toward higher values
    let dealerCard;
    attempts = 0;
    
    do {
        dealerCard = casinoWarGame.deck.pop();
        attempts++;
        
        const cardValue = getWarCardValue(dealerCard);
        // Bias toward high cards for dealer
        if (cardValue < 10 && Math.random() < 0.7 * zoneData.dealerBias && attempts < 25) {
            casinoWarGame.deck.unshift(dealerCard);
            continue;
        }
        break;
    } while (attempts < 30);
    
    casinoWarGame.dealerCard = dealerCard;
}

function displayWarCards() {
    // Display player card
    const playerArea = document.getElementById('playerCardSlot');
    playerArea.innerHTML = '';
    const playerCardElement = createWarCardElement(casinoWarGame.playerCard);
    playerCardElement.classList.add('animate-pulse');
    playerArea.appendChild(playerCardElement);
    
    // Display dealer card
    const dealerArea = document.getElementById('dealerCardSlot');
    dealerArea.innerHTML = '';
    const dealerCardElement = createWarCardElement(casinoWarGame.dealerCard);
    dealerCardElement.classList.add('animate-pulse');
    dealerArea.appendChild(dealerCardElement);
    
    // Update card values
    document.getElementById('playerCardValue').textContent = 
        `Your Forces: ${casinoWarGame.playerCard.value} ${casinoWarGame.playerCard.suit}`;
    document.getElementById('dealerCardValue').textContent = 
        `Enemy Forces: ${casinoWarGame.dealerCard.value} ${casinoWarGame.dealerCard.suit}`;
}

function determineBattleOutcome() {
    const playerValue = getWarCardValue(casinoWarGame.playerCard);
    const dealerValue = getWarCardValue(casinoWarGame.dealerCard);
    const zoneData = CONFLICT_ZONES.find(z => z.name === casinoWarGame.currentZone);
    
    let totalWinnings = 0;
    let resultText = '';
    
    // Check tie bet first
    if (casinoWarGame.tieBet > 0 && playerValue === dealerValue) {
        const tiePayout = casinoWarGame.tieBet * WAR_PAYOUTS.TIE_BONUS;
        totalWinnings += tiePayout + casinoWarGame.tieBet;
        resultText += `🎯 Stalemate predicted! +${tiePayout} GA! `;
        casinoWarGame.sessionStats.tieWins++;
    }
    
    if (playerValue > dealerValue) {
        // Player wins (rare with bias)
        if (Math.random() < zoneData.winRate) {
            const winPayout = Math.floor(casinoWarGame.betAmount * WAR_PAYOUTS.WIN);
            totalWinnings += casinoWarGame.betAmount + winPayout;
            resultText += `🏆 Victory! Your ${casinoWarGame.playerCard.value} defeats enemy ${casinoWarGame.dealerCard.value}!`;
            casinoWarGame.sessionStats.battlesWon++;
            casinoWarGame.battlePoints += Math.floor(winPayout / 10);
        } else {
            // Force dealer win despite player having higher card
            resultText += `💔 Strategic defeat! Enemy ${casinoWarGame.dealerCard.value} outmaneuvers your ${casinoWarGame.playerCard.value}!`;
        }
    } else if (playerValue < dealerValue) {
        // Dealer wins (most common)
        resultText += `💔 Defeated! Enemy ${casinoWarGame.dealerCard.value} crushes your ${casinoWarGame.playerCard.value}!`;
    } else {
        // Tie - trigger war
        if (!casinoWarGame.isInWar) {
            resultText += `⚔️ STALEMATE! War declared between ${casinoWarGame.playerCard.value}s!`;
            enableWarOptions();
            return;
        }
    }
    
    // Add winnings
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    casinoWarGame.sessionStats.battlesPlayed++;
    casinoWarGame.sessionStats.totalWagered += casinoWarGame.betAmount + casinoWarGame.tieBet;
    casinoWarGame.sessionStats.totalWon += totalWinnings;
    casinoWarGame.sessionStats.biggestWin = Math.max(casinoWarGame.sessionStats.biggestWin, totalWinnings);
    
    // Check for level up
    checkConflictLevelUp();
    
    const winColor = totalWinnings > 0 ? 'text-neon-green' : 'text-red-400';
    document.getElementById('battleResult').innerHTML = `<span class="${winColor}">${resultText}</span>`;
    
    updateWarStats();
    updateWarDisplay();
    
    setTimeout(() => {
        resetForNextBattle();
    }, 4000);
}

function enableWarOptions() {
    document.getElementById('goToWar').disabled = false;
    document.getElementById('surrender').disabled = false;
    document.getElementById('warStatus').textContent = 'Choose your response to the stalemate!';
    document.getElementById('warPhase').textContent = 'War Declaration Phase';
}

function declareWar() {
    if (balance < casinoWarGame.betAmount) {
        document.getElementById('warStatus').textContent = 'Insufficient resources for war!';
        return;
    }
    
    // Deduct war bet
    balance -= casinoWarGame.betAmount;
    updateBalance();
    
    casinoWarGame.isInWar = true;
    casinoWarGame.warRound++;
    casinoWarGame.gamePhase = 'war';
    
    // Deal war cards (3 hidden + 1 battle card each)
    dealWarCards();
    
    document.getElementById('goToWar').disabled = true;
    document.getElementById('surrender').disabled = true;
    document.getElementById('warStatus').textContent = 'WAR ENGAGED! Final battle commencing...';
    document.getElementById('warPhase').textContent = `War Round ${casinoWarGame.warRound}`;
    
    setTimeout(() => {
        resolveWar();
    }, 3000);
}

function dealWarCards() {
    const warCardsContainer = document.getElementById('warCards');
    warCardsContainer.innerHTML = '';
    
    casinoWarGame.warCards = [];
    
    // Deal 3 hidden cards + 1 battle card for each side
    for (let i = 0; i < 8; i++) {
        const card = casinoWarGame.deck.pop();
        casinoWarGame.warCards.push(card);
        
        const cardElement = createWarCardElement(card, i < 6); // First 6 are hidden
        cardElement.style.animationDelay = `${i * 0.2}s`;
        cardElement.classList.add('animate-pulse');
        warCardsContainer.appendChild(cardElement);
    }
}

function resolveWar() {
    const zoneData = CONFLICT_ZONES.find(z => z.name === casinoWarGame.currentZone);
    
    // Get final battle cards (last 2 cards dealt)
    const playerWarCard = casinoWarGame.warCards[6];
    const dealerWarCard = casinoWarGame.warCards[7];
    
    const playerValue = getWarCardValue(playerWarCard);
    const dealerValue = getWarCardValue(dealerWarCard);
    
    let totalWinnings = 0;
    let resultText = '';
    
    // Heavily bias war resolution against player
    if (playerValue > dealerValue && Math.random() < zoneData.warAdvantage) {
        // Player wins war (very rare)
        const warPayout = Math.floor(casinoWarGame.betAmount * 2 * WAR_PAYOUTS.WAR_WIN);
        totalWinnings += casinoWarGame.betAmount * 2 + warPayout;
        resultText = `🎖️ WAR VICTORY! Your ${playerWarCard.value} conquers enemy ${dealerWarCard.value}! +${warPayout} GA!`;
        casinoWarGame.sessionStats.warsWon++;
        casinoWarGame.sessionStats.battlesWon++;
        casinoWarGame.battlePoints += Math.floor(warPayout / 5);
    } else if (playerValue === dealerValue) {
        // Another tie - extremely rare, force dealer win
        resultText = `💥 War continues but enemy reinforcements arrive! Defeat!`;
    } else {
        // Dealer wins war (most common)
        resultText = `💔 WAR LOST! Enemy ${dealerWarCard.value} defeats your ${playerWarCard.value}! All forces lost!`;
    }
    
    // Add winnings
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    casinoWarGame.sessionStats.battlesPlayed++;
    casinoWarGame.sessionStats.totalWagered += casinoWarGame.betAmount;
    casinoWarGame.sessionStats.totalWon += totalWinnings;
    casinoWarGame.sessionStats.biggestWin = Math.max(casinoWarGame.sessionStats.biggestWin, totalWinnings);
    
    const winColor = totalWinnings > 0 ? 'text-neon-green' : 'text-red-400';
    document.getElementById('battleResult').innerHTML = `<span class="${winColor}">${resultText}</span>`;
    
    updateWarStats();
    updateWarDisplay();
    
    setTimeout(() => {
        resetForNextBattle();
    }, 5000);
}

function surrenderBattle() {
    // Return only 30% of bet
    const surrenderReturn = Math.floor(casinoWarGame.betAmount * WAR_PAYOUTS.SURRENDER);
    balance += surrenderReturn;
    updateBalance();
    
    casinoWarGame.sessionStats.battlesPlayed++;
    casinoWarGame.sessionStats.surrenders++;
    casinoWarGame.sessionStats.totalWagered += casinoWarGame.betAmount + casinoWarGame.tieBet;
    casinoWarGame.sessionStats.totalWon += surrenderReturn;
    
    document.getElementById('battleResult').innerHTML = 
        `<span class="text-yellow-400">🏳️ Strategic withdrawal! ${surrenderReturn} GA salvaged from defeat.</span>`;
    
    updateWarStats();
    
    setTimeout(() => {
        resetForNextBattle();
    }, 3000);
}

function getWarCardValue(card) {
    if (card.value === 'A') return 14;
    if (card.value === 'K') return 13;
    if (card.value === 'Q') return 12;
    if (card.value === 'J') return 11;
    return parseInt(card.value);
}

function checkConflictLevelUp() {
    const newLevel = Math.floor(casinoWarGame.battlePoints / 1000) + 1;
    if (newLevel > casinoWarGame.conflictLevel) {
        casinoWarGame.conflictLevel = newLevel;
        document.getElementById('warStatus').textContent = `🎖️ Promoted to Conflict Level ${newLevel}!`;
    }
}

function updateWarDisplay() {
    document.getElementById('conflictLevel').textContent = casinoWarGame.conflictLevel;
    document.getElementById('battlePoints').textContent = casinoWarGame.battlePoints.toLocaleString();
}

function updateWarStats() {
    document.getElementById('battlesPlayed').textContent = casinoWarGame.sessionStats.battlesPlayed;
    document.getElementById('battlesWon').textContent = casinoWarGame.sessionStats.battlesWon;
    document.getElementById('totalWagered').textContent = casinoWarGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('biggestWin').textContent = casinoWarGame.sessionStats.biggestWin.toLocaleString();
}

function resetForNextBattle() {
    casinoWarGame.gamePhase = 'betting';
    casinoWarGame.playerCard = null;
    casinoWarGame.dealerCard = null;
    casinoWarGame.warCards = [];
    casinoWarGame.betAmount = 0;
    casinoWarGame.tieBet = 0;
    casinoWarGame.isInWar = false;
    casinoWarGame.warRound = 0;
    
    // Reset UI
    document.getElementById('deployForces').disabled = false;
    document.getElementById('goToWar').disabled = true;
    document.getElementById('surrender').disabled = true;
    document.getElementById('tieBetCheck').checked = false;
    document.getElementById('tieBetInput').value = 0;
    
    document.getElementById('warStatus').textContent = 'Choose your battlefield!';
    document.getElementById('warPhase').textContent = 'Deployment Phase';
    document.getElementById('battleResult').innerHTML = '';
    document.getElementById('playerCardValue').textContent = '';
    document.getElementById('dealerCardValue').textContent = '';
    
    // Reset card displays
    document.getElementById('playerCardSlot').innerHTML = 'Awaiting Orders';
    document.getElementById('playerCardSlot').className = 'card-slot border-2 border-neon-purple/30 rounded-lg w-24 h-32 flex items-center justify-center text-neon-purple';
    
    document.getElementById('dealerCardSlot').innerHTML = 'Ready for Battle';
    document.getElementById('dealerCardSlot').className = 'card-slot border-2 border-red-500/30 rounded-lg w-24 h-32 flex items-center justify-center text-red-400';
    
    document.getElementById('warCards').innerHTML = '';
    
    updateTieBet();
}

function resetWarGame() {
    resetForNextBattle();
    document.getElementById('battleResult').innerHTML = '';
}

function showWarRules() {
    document.getElementById('rulesModal').classList.remove('hidden');
}

function hideWarRules() {
    document.getElementById('rulesModal').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCasinoWarGame();
});