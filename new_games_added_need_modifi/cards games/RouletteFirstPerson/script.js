// Roulette First Person - Immersive first-person roulette with <8% win rate
let rouletteFirstPersonGame = {
    currentNumber: null,
    lastNumbers: [],
    bets: [],
    totalBet: 0,
    isSpinning: false,
    spinSpeed: 0,
    ballPosition: 0,
    wheelPosition: 0,
    cameraAngle: 0,
    dealerPersonality: 'professional',
    tablePosition: 'center',
    lightingMode: 'casino',
    immersionLevel: 1,
    eyeTracking: false,
    handGestures: false,
    voiceCommands: false,
    personalizedBias: false,
    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        straightHits: 0,
        dealerInteractions: 0,
        eyeTrackingEvents: 0,
        gestureCommands: 0,
        voiceCommands: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    gameHistory: [],
    dealerComments: [],
    playerProfile: {
        bettingPattern: 'random',
        favoriteNumbers: [],
        riskLevel: 'medium',
        sessionTime: 0
    }
};

// Dealer personalities with bias effects
const DEALER_PERSONALITIES = {
    professional: {
        name: '<PERSON>',
        houseEdge: 0.38,
        biasStrength: 0.32,
        payoutMultiplier: 0.67,
        comments: [
            'Excellent choice, but the house has the edge.',
            'The wheel is calibrated for optimal house performance.',
            'Professional gaming requires professional odds.',
            'Your bet is noted, good luck beating the system.',
            'The mathematics favor the establishment.'
        ]
    },
    friendly: {
        name: 'Marcus Thompson',
        houseEdge: 0.35,
        biasStrength: 0.28,
        payoutMultiplier: 0.72,
        comments: [
            'Good luck! Though the odds are against you.',
            'Nice bet! The house still wins most of the time.',
            'I hope you win, but statistics say otherwise.',
            'Great choice! The wheel decides your fate.',
            'May fortune smile upon you... briefly.'
        ]
    },
    mysterious: {
        name: 'Raven Blackwood',
        houseEdge: 0.42,
        biasStrength: 0.36,
        payoutMultiplier: 0.61,
        comments: [
            'The wheel knows your secrets...',
            'Fate has already decided your outcome.',
            'The shadows whisper of your defeat.',
            'Your destiny is written in the numbers.',
            'The house always collects its due.'
        ]
    },
    intimidating: {
        name: 'Viktor Steele',
        houseEdge: 0.45,
        biasStrength: 0.40,
        payoutMultiplier: 0.58,
        comments: [
            'You think you can beat me? Amusing.',
            'Another lamb to the slaughter.',
            'Your money will soon be mine.',
            'The house crushes all who dare challenge it.',
            'Prepare to lose everything.'
        ]
    }
};

// Table positions with different bias effects
const TABLE_POSITIONS = {
    center: {
        name: 'Center Table',
        biasMultiplier: 1.0,
        houseBonus: 0.05
    },
    left: {
        name: 'Left Side',
        biasMultiplier: 1.15,
        houseBonus: 0.08
    },
    right: {
        name: 'Right Side',
        biasMultiplier: 1.12,
        houseBonus: 0.07
    },
    vip: {
        name: 'VIP Section',
        biasMultiplier: 1.25,
        houseBonus: 0.12
    }
};

// Lighting modes affecting perception
const LIGHTING_MODES = {
    casino: {
        name: 'Standard Casino',
        perceptionBias: 0.08,
        glareEffect: 0.05
    },
    dramatic: {
        name: 'Dramatic Lighting',
        perceptionBias: 0.12,
        glareEffect: 0.08
    },
    neon: {
        name: 'Neon Glow',
        perceptionBias: 0.15,
        glareEffect: 0.12
    },
    noir: {
        name: 'Film Noir',
        perceptionBias: 0.18,
        glareEffect: 0.15
    }
};

// Roulette numbers and colors
const ROULETTE_NUMBERS = {
    0: 'green', 1: 'red', 2: 'black', 3: 'red', 4: 'black', 5: 'red', 6: 'black', 7: 'red', 8: 'black', 9: 'red', 10: 'black',
    11: 'black', 12: 'red', 13: 'black', 14: 'red', 15: 'black', 16: 'red', 17: 'black', 18: 'red', 19: 'red', 20: 'black',
    21: 'red', 22: 'black', 23: 'red', 24: 'black', 25: 'red', 26: 'black', 27: 'red', 28: 'black', 29: 'black', 30: 'red',
    31: 'black', 32: 'red', 33: 'black', 34: 'red', 35: 'black', 36: 'red'
};

// First-person payout multipliers
const FP_PAYOUTS = {
    straight: 35,
    red: 1,
    black: 1,
    odd: 1,
    even: 1,
    low: 1,
    high: 1,
    dozen: 2
};

// Ultra-biased first-person number generation
function generateFirstPersonBiasedNumber() {
    const dealerData = DEALER_PERSONALITIES[rouletteFirstPersonGame.dealerPersonality];
    const positionData = TABLE_POSITIONS[rouletteFirstPersonGame.tablePosition];
    const lightingData = LIGHTING_MODES[rouletteFirstPersonGame.lightingMode];
    
    const immersionBias = rouletteFirstPersonGame.immersionLevel * 0.09;
    const totalBias = dealerData.biasStrength * positionData.biasMultiplier + 
                     lightingData.perceptionBias + immersionBias;
    
    // Create heavily biased number pool
    const numberPool = [];
    
    for (let num = 0; num <= 36; num++) {
        let weight = 1;
        
        // Check if players bet on this number
        const playerBetsOnNumber = rouletteFirstPersonGame.bets.some(bet => 
            (bet.type === 'straight' && bet.number === num) ||
            (bet.type === 'red' && ROULETTE_NUMBERS[num] === 'red') ||
            (bet.type === 'black' && ROULETTE_NUMBERS[num] === 'black') ||
            (bet.type === 'odd' && num % 2 === 1 && num !== 0) ||
            (bet.type === 'even' && num % 2 === 0 && num !== 0) ||
            (bet.type === 'low' && num >= 1 && num <= 18) ||
            (bet.type === 'high' && num >= 19 && num <= 36) ||
            (bet.type === 'dozen1' && num >= 1 && num <= 12) ||
            (bet.type === 'dozen2' && num >= 13 && num <= 24) ||
            (bet.type === 'dozen3' && num >= 25 && num <= 36)
        );
        
        if (playerBetsOnNumber) {
            weight *= (1 - totalBias * 2.1);
        } else {
            weight *= (1 + totalBias * 0.8);
        }
        
        if (num === 0 || num === 37) {
            weight *= (1 + totalBias * 2.6);
        }
        
        if (rouletteFirstPersonGame.eyeTracking) {
            if (playerBetsOnNumber) {
                weight *= 0.15;
            } else {
                weight *= 1.85;
            }
        }
        
        if (rouletteFirstPersonGame.personalizedBias) {
            if (rouletteFirstPersonGame.playerProfile.favoriteNumbers.includes(num)) {
                weight *= 0.08;
            }
        }
        
        if (rouletteFirstPersonGame.handGestures) {
            if (playerBetsOnNumber) {
                weight *= 0.18;
            }
        }
        
        if (rouletteFirstPersonGame.voiceCommands) {
            if (playerBetsOnNumber) {
                weight *= 0.12;
            }
        }
        
        weight *= (1 - lightingData.glareEffect);
        
        const copies = Math.max(1, Math.floor(weight * 100));
        for (let i = 0; i < copies; i++) {
            numberPool.push(num);
        }
    }
    
    for (let i = numberPool.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numberPool[i], numberPool[j]] = [numberPool[j], numberPool[i]];
    }
    
    return numberPool[Math.floor(Math.random() * numberPool.length)];
}

function generateFirstPersonEffects() {
    if (Math.random() < 0.35 + (rouletteFirstPersonGame.immersionLevel * 0.03)) {
        rouletteFirstPersonGame.eyeTracking = true;
        rouletteFirstPersonGame.stats.eyeTrackingEvents++;
    }
    
    if (Math.random() < 0.28 + (rouletteFirstPersonGame.immersionLevel * 0.025)) {
        rouletteFirstPersonGame.handGestures = true;
        rouletteFirstPersonGame.stats.gestureCommands++;
    }
    
    if (Math.random() < 0.22 + (rouletteFirstPersonGame.immersionLevel * 0.02)) {
        rouletteFirstPersonGame.voiceCommands = true;
        rouletteFirstPersonGame.stats.voiceCommands++;
    }
    
    if (rouletteFirstPersonGame.stats.spinsPlayed >= 5) {
        rouletteFirstPersonGame.personalizedBias = true;
        
        rouletteFirstPersonGame.bets.forEach(bet => {
            if (bet.type === 'straight' && !rouletteFirstPersonGame.playerProfile.favoriteNumbers.includes(bet.number)) {
                rouletteFirstPersonGame.playerProfile.favoriteNumbers.push(bet.number);
            }
        });
    }
    
    if (rouletteFirstPersonGame.stats.spinsPlayed > 0 && 
        rouletteFirstPersonGame.stats.spinsPlayed % 6 === 0 && 
        rouletteFirstPersonGame.immersionLevel < 15) {
        rouletteFirstPersonGame.immersionLevel++;
        updateFirstPersonStatus(`Immersion Level increased to ${rouletteFirstPersonGame.immersionLevel}!`);
    }
}

function placeBet(type, number = null, amount = null) {
    if (rouletteFirstPersonGame.isSpinning) return;
    
    const betAmount = amount || parseInt(document.getElementById('betAmount').value);
    
    if (betAmount < 5 || betAmount > 10000) {
        updateFirstPersonStatus('Bet must be between 5 and 10000 GA');
        return;
    }
    
    if (betAmount > balance) {
        updateFirstPersonStatus('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    rouletteFirstPersonGame.bets.push({ type, number, amount });
    rouletteFirstPersonGame.totalBet += betAmount;
    rouletteFirstPersonGame.stats.totalWagered += betAmount;
    
    const dealerData = DEALER_PERSONALITIES[rouletteFirstPersonGame.dealerPersonality];
    const comment = dealerData.comments[Math.floor(Math.random() * dealerData.comments.length)];
    showDealerComment(comment);
    
    updateFirstPersonDisplay();
    updateFirstPersonStatus(`${betAmount} GA bet placed on ${type}${number ? ' ' + number : ''}`);
}

function clearBets() {
    if (rouletteFirstPersonGame.isSpinning) return;
    
    balance += rouletteFirstPersonGame.totalBet;
    updateBalance();
    
    rouletteFirstPersonGame.bets = [];
    rouletteFirstPersonGame.totalBet = 0;
    
    updateFirstPersonDisplay();
    updateFirstPersonStatus('All bets cleared');
    showDealerComment('Starting fresh? The house edge remains the same.');
}

function spinFirstPersonWheel() {
    if (rouletteFirstPersonGame.isSpinning || rouletteFirstPersonGame.bets.length === 0) return;
    
    rouletteFirstPersonGame.isSpinning = true;
    
    rouletteFirstPersonGame.eyeTracking = false;
    rouletteFirstPersonGame.handGestures = false;
    rouletteFirstPersonGame.voiceCommands = false;
    
    generateFirstPersonEffects();
    
    updateFirstPersonStatus('Spinning the wheel...');
    showDealerComment('No more bets! Let\'s see what the wheel decides...');
    
    let spinDuration = 4000 + Math.random() * 3000;
    let elapsed = 0;
    const spinInterval = 80;
    
    const fpSpinAnimation = setInterval(() => {
        elapsed += spinInterval;
        
        rouletteFirstPersonGame.wheelPosition += 8;
        rouletteFirstPersonGame.ballPosition -= 12;
        rouletteFirstPersonGame.cameraAngle += 0.5;
        
        if (rouletteFirstPersonGame.eyeTracking) {
            updateFirstPersonStatus('Eye tracking active - Analyzing gaze patterns...');
        }
        
        if (rouletteFirstPersonGame.handGestures) {
            updateFirstPersonStatus('Hand gesture recognition - Reading body language...');
        }
        
        if (rouletteFirstPersonGame.voiceCommands) {
            updateFirstPersonStatus('Voice analysis active - Processing vocal stress...');
        }
        
        if (elapsed >= spinDuration) {
            clearInterval(fpSpinAnimation);
            finishFirstPersonSpin();
        }
    }, spinInterval);
}

function finishFirstPersonSpin() {
    rouletteFirstPersonGame.currentNumber = generateFirstPersonBiasedNumber();
    
    rouletteFirstPersonGame.lastNumbers.unshift(rouletteFirstPersonGame.currentNumber);
    if (rouletteFirstPersonGame.lastNumbers.length > 10) {
        rouletteFirstPersonGame.lastNumbers.pop();
    }
    
    rouletteFirstPersonGame.gameHistory.push({
        number: rouletteFirstPersonGame.currentNumber,
        bets: [...rouletteFirstPersonGame.bets],
        effects: {
            eyeTracking: rouletteFirstPersonGame.eyeTracking,
            handGestures: rouletteFirstPersonGame.handGestures,
            voiceCommands: rouletteFirstPersonGame.voiceCommands,
            personalizedBias: rouletteFirstPersonGame.personalizedBias
        }
    });
    
    calculateFirstPersonResults();
    
    rouletteFirstPersonGame.isSpinning = false;
    updateFirstPersonDisplay();
    
    setTimeout(() => {
        rouletteFirstPersonGame.bets = [];
        rouletteFirstPersonGame.totalBet = 0;
        updateFirstPersonDisplay();
    }, 3000);
}

function calculateFirstPersonResults() {
    let totalWin = 0;
    let winningBets = 0;
    const winningNumber = rouletteFirstPersonGame.currentNumber;
    const winningColor = ROULETTE_NUMBERS[winningNumber];
    
    const dealerData = DEALER_PERSONALITIES[rouletteFirstPersonGame.dealerPersonality];
    const positionData = TABLE_POSITIONS[rouletteFirstPersonGame.tablePosition];
    const lightingData = LIGHTING_MODES[rouletteFirstPersonGame.lightingMode];
    
    rouletteFirstPersonGame.bets.forEach(bet => {
        let isWinning = false;
        let payout = 0;
        
        switch (bet.type) {
            case 'straight':
                isWinning = bet.number === winningNumber;
                payout = FP_PAYOUTS.straight;
                break;
            case 'red':
                isWinning = winningColor === 'red';
                payout = FP_PAYOUTS.red;
                break;
            case 'black':
                isWinning = winningColor === 'black';
                payout = FP_PAYOUTS.black;
                break;
            case 'odd':
                isWinning = winningNumber % 2 === 1 && winningNumber !== 0;
                payout = FP_PAYOUTS.odd;
                break;
            case 'even':
                isWinning = winningNumber % 2 === 0 && winningNumber !== 0;
                payout = FP_PAYOUTS.even;
                break;
            case 'low':
                isWinning = winningNumber >= 1 && winningNumber <= 18;
                payout = FP_PAYOUTS.low;
                break;
            case 'high':
                isWinning = winningNumber >= 19 && winningNumber <= 36;
                payout = FP_PAYOUTS.high;
                break;
            case 'dozen1':
                isWinning = winningNumber >= 1 && winningNumber <= 12;
                payout = FP_PAYOUTS.dozen;
                break;
            case 'dozen2':
                isWinning = winningNumber >= 13 && winningNumber <= 24;
                payout = FP_PAYOUTS.dozen;
                break;
            case 'dozen3':
                isWinning = winningNumber >= 25 && winningNumber <= 36;
                payout = FP_PAYOUTS.dozen;
                break;
        }
        
        if (isWinning) {
            winningBets++;
            
            payout *= dealerData.payoutMultiplier;
            
            if (rouletteFirstPersonGame.eyeTracking) {
                payout *= 0.48;
            }
            
            if (rouletteFirstPersonGame.handGestures) {
                payout *= 0.55;
            }
            
            if (rouletteFirstPersonGame.voiceCommands) {
                payout *= 0.42;
            }
            
            if (rouletteFirstPersonGame.personalizedBias) {
                payout *= 0.38;
            }
            
            payout *= (1 - (rouletteFirstPersonGame.immersionLevel * 0.045));
            payout *= (1 - positionData.houseBonus);
            payout *= (1 - lightingData.perceptionBias);
            payout *= (1 - dealerData.houseEdge * 0.6);
            
            let winnings = bet.amount * payout;
            winnings = Math.floor(Math.max(1, winnings));
            
            totalWin += bet.amount + winnings;
            
            if (bet.type === 'straight') {
                rouletteFirstPersonGame.stats.straightHits++;
            }
        }
    });
    
    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        
        rouletteFirstPersonGame.stats.totalWon += (totalWin - rouletteFirstPersonGame.totalBet);
        
        if (totalWin > rouletteFirstPersonGame.stats.biggestWin) {
            rouletteFirstPersonGame.stats.biggestWin = totalWin;
        }
    }
    
    updateFirstPersonGameStats(winningBets > 0, totalWin);
    
    if (winningBets > 0) {
        const netWin = totalWin - rouletteFirstPersonGame.totalBet;
        updateFirstPersonStatus(`Result: ${winningNumber} (${winningColor}) - Won ${netWin} GA!`);
        showDealerComment('Lucky this time, but the house always wins in the end.');
    } else {
        updateFirstPersonStatus(`Result: ${winningNumber} (${winningColor}) - House wins ${rouletteFirstPersonGame.totalBet} GA`);
        showDealerComment('As expected. The house edge is mathematically guaranteed.');
    }
}

function updateFirstPersonGameStats(won, totalWin) {
    rouletteFirstPersonGame.stats.spinsPlayed++;
    
    if (won) {
        rouletteFirstPersonGame.stats.spinsWon++;
        rouletteFirstPersonGame.streakData.currentWinStreak++;
        rouletteFirstPersonGame.streakData.currentLossStreak = 0;
        
        if (rouletteFirstPersonGame.streakData.currentWinStreak > rouletteFirstPersonGame.streakData.longestWinStreak) {
            rouletteFirstPersonGame.streakData.longestWinStreak = rouletteFirstPersonGame.streakData.currentWinStreak;
        }
    } else {
        rouletteFirstPersonGame.streakData.currentLossStreak++;
        rouletteFirstPersonGame.streakData.currentWinStreak = 0;
        
        if (rouletteFirstPersonGame.streakData.currentLossStreak > rouletteFirstPersonGame.streakData.longestLossStreak) {
            rouletteFirstPersonGame.streakData.longestLossStreak = rouletteFirstPersonGame.streakData.currentLossStreak;
        }
    }
}

function updateFirstPersonDisplay() {
    if (rouletteFirstPersonGame.currentNumber !== null) {
        const color = ROULETTE_NUMBERS[rouletteFirstPersonGame.currentNumber];
        document.getElementById('currentNumber').innerHTML = `
            <div class="text-6xl font-bold ${color === 'red' ? 'text-red-400' : color === 'black' ? 'text-gray-300' : 'text-green-400'} neon-glow">
                ${rouletteFirstPersonGame.currentNumber}
            </div>
            <div class="text-xl text-${color === 'red' ? 'red' : color === 'black' ? 'gray' : 'green'}-400">
                ${color.toUpperCase()}
            </div>
        `;
    }
    
    const lastNumbersEl = document.getElementById('lastNumbers');
    lastNumbersEl.innerHTML = rouletteFirstPersonGame.lastNumbers.map(num => {
        const color = ROULETTE_NUMBERS[num];
        return `<span class="inline-block w-8 h-8 rounded-full text-center leading-8 text-sm font-bold ${
            color === 'red' ? 'bg-red-500 text-white' : 
            color === 'black' ? 'bg-gray-800 text-white' : 
            'bg-green-500 text-white'
        }">${num}</span>`;
    }).join(' ');
    
    document.getElementById('totalBet').textContent = rouletteFirstPersonGame.totalBet;
    
    const activeBetsEl = document.getElementById('activeBets');
    activeBetsEl.innerHTML = rouletteFirstPersonGame.bets.map(bet => 
        `<div class="text-sm">${bet.amount} GA on ${bet.type}${bet.number ? ' ' + bet.number : ''}</div>`
    ).join('');
    
    document.getElementById('immersionLevel').textContent = rouletteFirstPersonGame.immersionLevel;
    document.getElementById('eyeTracking').style.display = rouletteFirstPersonGame.eyeTracking ? 'block' : 'none';
    document.getElementById('handGestures').style.display = rouletteFirstPersonGame.handGestures ? 'block' : 'none';
    document.getElementById('voiceCommands').style.display = rouletteFirstPersonGame.voiceCommands ? 'block' : 'none';
    document.getElementById('personalizedBias').style.display = rouletteFirstPersonGame.personalizedBias ? 'block' : 'none';
    
    updateFirstPersonStats();
}

function updateFirstPersonStats() {
    document.getElementById('spinsPlayed').textContent = rouletteFirstPersonGame.stats.spinsPlayed;
    
    const winRate = rouletteFirstPersonGame.stats.spinsPlayed > 0 ? 
        ((rouletteFirstPersonGame.stats.spinsWon / rouletteFirstPersonGame.stats.spinsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('winRate').textContent = `${winRate}%`;
    
    document.getElementById('totalWagered').textContent = rouletteFirstPersonGame.stats.totalWagered.toLocaleString();
    document.getElementById('totalWon').textContent = rouletteFirstPersonGame.stats.totalWon.toLocaleString();
    document.getElementById('biggestWin').textContent = rouletteFirstPersonGame.stats.biggestWin.toLocaleString();
    document.getElementById('lossStreak').textContent = rouletteFirstPersonGame.streakData.currentLossStreak;
    document.getElementById('dealerInteractions').textContent = rouletteFirstPersonGame.stats.dealerInteractions;
    document.getElementById('eyeTrackingEvents').textContent = rouletteFirstPersonGame.stats.eyeTrackingEvents;
    document.getElementById('gestureCommands').textContent = rouletteFirstPersonGame.stats.gestureCommands;
    document.getElementById('voiceCommands').textContent = rouletteFirstPersonGame.stats.voiceCommands;
}

function updateFirstPersonStatus(message) {
    document.getElementById('fpStatus').textContent = message;
}

function showDealerComment(comment) {
    document.getElementById('dealerComment').textContent = comment;
    rouletteFirstPersonGame.stats.dealerInteractions++;
    
    setTimeout(() => {
        document.getElementById('dealerComment').textContent = '';
    }, 5000);
}

function changeDealerPersonality() {
    const personality = document.getElementById('dealerPersonality').value;
    rouletteFirstPersonGame.dealerPersonality = personality;
    
    const dealerData = DEALER_PERSONALITIES[personality];
    document.getElementById('dealerInfo').innerHTML = `
        Dealer: ${dealerData.name} | 
        House Edge: ${(dealerData.houseEdge * 100).toFixed(0)}% | 
        Bias: ${(dealerData.biasStrength * 100).toFixed(0)}% | 
        Payout: ${(dealerData.payoutMultiplier * 100).toFixed(0)}%
    `;
    
    showDealerComment(`Hello, I'm ${dealerData.name}. Ready to lose some money?`);
}

function changeTablePosition() {
    const position = document.getElementById('tablePosition').value;
    rouletteFirstPersonGame.tablePosition = position;
    
    const positionData = TABLE_POSITIONS[position];
    document.getElementById('positionInfo').innerHTML = `
        Position: ${positionData.name} | 
        Bias Multiplier: ${positionData.biasMultiplier}x | 
        House Bonus: +${(positionData.houseBonus * 100).toFixed(0)}%
    `;
}

function changeLightingMode() {
    const lighting = document.getElementById('lightingMode').value;
    rouletteFirstPersonGame.lightingMode = lighting;
    
    const lightingData = LIGHTING_MODES[lighting];
    document.getElementById('lightingInfo').innerHTML = `
        Lighting: ${lightingData.name} | 
        Perception Bias: ${(lightingData.perceptionBias * 100).toFixed(0)}% | 
        Glare Effect: ${(lightingData.glareEffect * 100).toFixed(0)}%
    `;
}

// Initialize first-person game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeDealerPersonality();
    changeTablePosition();
    changeLightingMode();
    
    document.getElementById('spinWheel').addEventListener('click', spinFirstPersonWheel);
    document.getElementById('clearBets').addEventListener('click', clearBets);
    document.getElementById('dealerPersonality').addEventListener('change', changeDealerPersonality);
    document.getElementById('tablePosition').addEventListener('change', changeTablePosition);
    document.getElementById('lightingMode').addEventListener('change', changeLightingMode);
    
    document.getElementById('betRed').addEventListener('click', () => placeBet('red'));
    document.getElementById('betBlack').addEventListener('click', () => placeBet('black'));
    document.getElementById('betOdd').addEventListener('click', () => placeBet('odd'));
    document.getElementById('betEven').addEventListener('click', () => placeBet('even'));
    document.getElementById('betLow').addEventListener('click', () => placeBet('low'));
    document.getElementById('betHigh').addEventListener('click', () => placeBet('high'));
    document.getElementById('betDozen1').addEventListener('click', () => placeBet('dozen1'));
    document.getElementById('betDozen2').addEventListener('click', () => placeBet('dozen2'));
    document.getElementById('betDozen3').addEventListener('click', () => placeBet('dozen3'));
    
    for (let i = 0; i <= 36; i++) {
        const btn = document.getElementById(`bet${i}`);
        if (btn) {
            btn.addEventListener('click', () => placeBet('straight', i));
        }
    }
    
    showDealerComment('Welcome to my table. I hope you brought plenty of money to lose.');
});