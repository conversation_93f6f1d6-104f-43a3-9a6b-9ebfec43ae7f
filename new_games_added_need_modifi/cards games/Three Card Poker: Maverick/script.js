// Three Card Poker: Maverick - Ultra High House Edge Implementation
// Designed to maintain <10% player win rate

// Game state
let balance = 1000;

// Game state object with extreme house bias
let threeCardPokerGame = {
    isPlaying: false,
    gameMode: 'maverick', // maverick, outlaw, desperado
    difficulty: 'normal', // easy, normal, hard, nightmare
    anteBet: 0,
    playBet: 0,
    pairPlusBet: 0,
    totalBet: 0,
    playerCards: [],
    dealerCards: [],
    playerHand: null,
    dealerHand: null,
    gameResult: '',
    totalWin: 0,
    deck: [],
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game modes with extreme house bias (ensuring <10% win rate)
const MAVERICK_MODES = {
    maverick: {
        name: 'Maverick Mode',
        houseEdge: 0.48, // 48% house edge
        dealerAdvantage: 0.35, // 35% dealer advantage
        payoutMultiplier: 0.55, // Severely reduced payouts
        qualifierBias: 0.25 // 25% bias toward dealer qualifying
    },
    outlaw: {
        name: 'Outlaw Territory',
        houseEdge: 0.55, // 55% house edge
        dealerAdvantage: 0.42, // 42% dealer advantage
        payoutMultiplier: 0.48, // Even more reduced payouts
        qualifierBias: 0.35 // 35% bias toward dealer qualifying
    },
    desperado: {
        name: 'Desperado Showdown',
        houseEdge: 0.62, // 62% house edge
        dealerAdvantage: 0.50, // 50% dealer advantage
        payoutMultiplier: 0.40, // Extremely reduced payouts
        qualifierBias: 0.45 // 45% bias toward dealer qualifying
    }
};

const MAVERICK_DIFFICULTIES = {
    easy: {
        name: 'Rookie Gunslinger',
        cardBias: 0.20, // 20% bias toward low cards for player
        dealerLuck: 0.15, // 15% dealer luck bonus
        payoutPenalty: 0.10 // 10% additional payout reduction
    },
    normal: {
        name: 'Seasoned Cowboy',
        cardBias: 0.30, // 30% bias toward low cards for player
        dealerLuck: 0.25, // 25% dealer luck bonus
        payoutPenalty: 0.20 // 20% additional payout reduction
    },
    hard: {
        name: 'Legendary Gunfighter',
        cardBias: 0.40, // 40% bias toward low cards for player
        dealerLuck: 0.35, // 35% dealer luck bonus
        payoutPenalty: 0.30 // 30% additional payout reduction
    },
    nightmare: {
        name: 'Ghost of the West',
        cardBias: 0.55, // 55% bias toward low cards for player
        dealerLuck: 0.50, // 50% dealer luck bonus
        payoutPenalty: 0.45 // 45% additional payout reduction
    }
};

// Severely reduced payout table to ensure house advantage
const MAVERICK_PAYOUTS = {
    // Ante/Play payouts (reduced from standard)
    STRAIGHT_FLUSH: 4, // Reduced from 5:1
    THREE_OF_A_KIND: 3, // Reduced from 4:1
    STRAIGHT: 1, // Reduced from 1:1 to even money
    FLUSH: 1, // Reduced from 1:1 to even money
    PAIR: 0.8, // Less than even money
    HIGH_CARD: 0.7, // Less than even money

    // Pair Plus payouts (severely reduced)
    STRAIGHT_FLUSH_PP: 35, // Reduced from 40:1
    THREE_OF_A_KIND_PP: 25, // Reduced from 30:1
    STRAIGHT_PP: 5, // Reduced from 6:1
    FLUSH_PP: 3, // Reduced from 4:1
    PAIR_PP: 1, // Reduced from 1:1

    // Ante bonus (minimal)
    ANTE_BONUS_SF: 4, // Reduced from 5:1
    ANTE_BONUS_3K: 3, // Reduced from 4:1
    ANTE_BONUS_ST: 1 // Reduced from 1:1
};

// Hand rankings for Three Card Poker
const HAND_RANKINGS = {
    STRAIGHT_FLUSH: 8,
    THREE_OF_A_KIND: 7,
    STRAIGHT: 6,
    FLUSH: 5,
    PAIR: 4,
    HIGH_CARD: 3
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadThreeCardPokerGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">MAVERICK CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="maverick">Maverick Mode</option>
                            <option value="outlaw">Outlaw Territory</option>
                            <option value="desperado">Desperado Showdown</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Rookie Gunslinger</option>
                            <option value="normal" selected>Seasoned Cowboy</option>
                            <option value="hard">Legendary Gunfighter</option>
                            <option value="nightmare">Ghost of the West</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ANTE BET</label>
                        <input type="number" id="anteBet" value="10" min="5" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PAIR PLUS BET (Optional)</label>
                        <input type="number" id="pairPlusBet" value="0" min="0" max="${Math.min(balance, 200)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        DEAL CARDS
                    </button>

                    <div id="playerActions" class="space-y-2 hidden">
                        <button id="playHand" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            PLAY (Match Ante)
                        </button>
                        <button id="foldHand" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            FOLD
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Hand Strength</div>
                        <div id="handStrength" class="text-lg font-bold text-green-400">-</div>
                    </div>
                </div>

                <!-- Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">REDUCED PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Ante/Play:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">4:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Three of a Kind:</span>
                            <span class="text-red-400">3:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight:</span>
                            <span class="text-red-400">1:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Pair Plus:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">35:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Three of a Kind:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight:</span>
                            <span class="text-red-400">5:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Flush:</span>
                            <span class="text-red-400">3:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Pair:</span>
                            <span class="text-red-400">1:1</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Table -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="pokerTable" class="relative bg-gradient-to-br from-green-900 to-emerald-900 rounded-lg p-6 h-96">
                        <!-- Dealer Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">DEALER CARDS</div>
                                <div id="dealerCards" class="flex space-x-2 justify-center">
                                    <!-- Dealer cards will appear here -->
                                </div>
                                <div id="dealerHandType" class="text-xs text-gray-400 mt-1">-</div>
                            </div>
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">YOUR CARDS</div>
                                <div id="playerCards" class="flex space-x-2 justify-center">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div id="playerHandType" class="text-xs text-gray-400 mt-1">-</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Place your bets</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                        </div>

                        <!-- Betting Areas Visual -->
                        <div class="absolute bottom-16 left-4">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">ANTE</div>
                                <div id="anteDisplay" class="text-sm font-bold text-yellow-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 left-20">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">PLAY</div>
                                <div id="playDisplay" class="text-sm font-bold text-green-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 right-4">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">PAIR+</div>
                                <div id="pairPlusDisplay" class="text-sm font-bold text-purple-400">$0</div>
                            </div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Three Card Poker: Maverick</div>
                </div>
            </div>
        </div>

        <!-- Game Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
        </div>
    `;

    initializeThreeCardPoker();
}

// Initialize the game
function initializeThreeCardPoker() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);
    document.getElementById('playHand').addEventListener('click', playHand);
    document.getElementById('foldHand').addEventListener('click', foldHand);

    // Initialize deck
    initializeDeck();
    updateGameStats();
}

// Initialize deck with bias toward dealer
function initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

    threeCardPokerGame.deck = [];

    // Create multiple copies of high cards for dealer advantage
    const difficultyData = MAVERICK_DIFFICULTIES[threeCardPokerGame.difficulty];

    for (const suit of suits) {
        for (const rank of ranks) {
            const card = {
                rank,
                suit,
                value: getCardValue(rank),
                id: `${rank}${suit}`
            };

            threeCardPokerGame.deck.push(card);

            // Add extra high cards for dealer bias
            if (card.value >= 11) { // J, Q, K, A
                const extraCopies = Math.floor(difficultyData.cardBias * 10);
                for (let i = 0; i < extraCopies; i++) {
                    threeCardPokerGame.deck.push({...card, id: `${rank}${suit}_${i}`});
                }
            }
        }
    }

    shuffleDeck();
}

// Shuffle deck with dealer bias
function shuffleDeck() {
    const difficultyData = MAVERICK_DIFFICULTIES[threeCardPokerGame.difficulty];

    // Standard shuffle
    for (let i = threeCardPokerGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [threeCardPokerGame.deck[i], threeCardPokerGame.deck[j]] = [threeCardPokerGame.deck[j], threeCardPokerGame.deck[i]];
    }

    // Apply dealer bias - move high cards toward top of deck
    if (Math.random() < difficultyData.dealerLuck) {
        const highCards = threeCardPokerGame.deck.filter(card => card.value >= 11);
        const lowCards = threeCardPokerGame.deck.filter(card => card.value < 11);

        // Bias toward high cards at top (dealer gets first)
        threeCardPokerGame.deck = [...highCards.slice(0, 10), ...lowCards, ...highCards.slice(10)];
    }
}

// Get card value for comparison
function getCardValue(rank) {
    if (rank === 'A') return 14;
    if (rank === 'K') return 13;
    if (rank === 'Q') return 12;
    if (rank === 'J') return 11;
    return parseInt(rank);
}

// Deal new hand
function dealNewHand() {
    const anteBet = parseInt(document.getElementById('anteBet').value);
    const pairPlusBet = parseInt(document.getElementById('pairPlusBet').value) || 0;

    if (anteBet + pairPlusBet > balance) {
        alert('Insufficient balance!');
        return;
    }

    // Deduct bets
    balance -= (anteBet + pairPlusBet);
    updateBalance();

    // Set game state
    threeCardPokerGame.isPlaying = true;
    threeCardPokerGame.anteBet = anteBet;
    threeCardPokerGame.pairPlusBet = pairPlusBet;
    threeCardPokerGame.playBet = 0;
    threeCardPokerGame.totalBet = anteBet + pairPlusBet;
    threeCardPokerGame.gameMode = document.getElementById('gameMode').value;
    threeCardPokerGame.difficulty = document.getElementById('difficulty').value;

    // Clear previous cards
    threeCardPokerGame.playerCards = [];
    threeCardPokerGame.dealerCards = [];

    // Deal cards with bias
    dealCardsWithBias();

    // Update display
    updateBetDisplay();
    updateGameStats();

    // Show player actions
    document.getElementById('playerActions').classList.remove('hidden');
    document.getElementById('dealCards').disabled = true;

    // Evaluate player hand
    evaluatePlayerHand();

    document.getElementById('gameStatus').textContent = 'Look at your cards - Play or Fold?';
    document.getElementById('gameMessage').textContent = 'Decide: Play to match your ante bet, or fold to forfeit.';
}

// Deal cards with heavy bias toward dealer
function dealCardsWithBias() {
    const difficultyData = MAVERICK_DIFFICULTIES[threeCardPokerGame.difficulty];

    // Deal 3 cards to dealer first (gets best cards)
    for (let i = 0; i < 3; i++) {
        const card = threeCardPokerGame.deck.pop();
        threeCardPokerGame.dealerCards.push(card);
    }

    // Deal 3 cards to player with bias toward low cards
    for (let i = 0; i < 3; i++) {
        let card;

        // Apply card bias - higher chance of low cards for player
        if (Math.random() < difficultyData.cardBias) {
            // Force low card for player
            const lowCards = threeCardPokerGame.deck.filter(c => c.value <= 9);
            if (lowCards.length > 0) {
                const randomIndex = Math.floor(Math.random() * lowCards.length);
                card = lowCards[randomIndex];
                // Remove from deck
                const deckIndex = threeCardPokerGame.deck.findIndex(c => c.id === card.id);
                threeCardPokerGame.deck.splice(deckIndex, 1);
            } else {
                card = threeCardPokerGame.deck.pop();
            }
        } else {
            card = threeCardPokerGame.deck.pop();
        }

        threeCardPokerGame.playerCards.push(card);
    }

    // Display cards
    displayPlayerCards();
    displayDealerCards(true); // Hide dealer cards initially
}

// Display player cards
function displayPlayerCards() {
    const container = document.getElementById('playerCards');
    container.innerHTML = '';

    threeCardPokerGame.playerCards.forEach(card => {
        const cardElement = createCardElement(card);
        container.appendChild(cardElement);
    });
}

// Display dealer cards
function displayDealerCards(hidden = false) {
    const container = document.getElementById('dealerCards');
    container.innerHTML = '';

    threeCardPokerGame.dealerCards.forEach(card => {
        const cardElement = createCardElement(card, hidden);
        container.appendChild(cardElement);
    });
}

// Create card element
function createCardElement(card, hidden = false) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'w-16 h-24 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-sm font-bold shadow-lg';

    if (hidden) {
        cardDiv.className += ' bg-blue-900 text-blue-300';
        cardDiv.innerHTML = '🂠';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardDiv.style.color = isRed ? '#dc2626' : '#000';
        cardDiv.innerHTML = `
            <div class="text-xs">${card.rank}</div>
            <div class="text-lg">${card.suit}</div>
            <div class="text-xs transform rotate-180">${card.rank}</div>
        `;
    }

    return cardDiv;
}

// Evaluate player hand
function evaluatePlayerHand() {
    const hand = evaluateHand(threeCardPokerGame.playerCards);
    threeCardPokerGame.playerHand = hand;

    document.getElementById('handStrength').textContent = hand.name;
    document.getElementById('playerHandType').textContent = hand.name;
}

// Evaluate dealer hand
function evaluateDealerHand() {
    const hand = evaluateHand(threeCardPokerGame.dealerCards);
    threeCardPokerGame.dealerHand = hand;

    document.getElementById('dealerHandType').textContent = hand.name;
}

// Evaluate hand strength
function evaluateHand(cards) {
    // Sort cards by value
    const sortedCards = cards.sort((a, b) => b.value - a.value);

    // Check for straight flush
    if (isFlush(cards) && isStraight(cards)) {
        return { rank: HAND_RANKINGS.STRAIGHT_FLUSH, name: 'Straight Flush', highCard: sortedCards[0].value };
    }

    // Check for three of a kind
    if (isThreeOfAKind(cards)) {
        return { rank: HAND_RANKINGS.THREE_OF_A_KIND, name: 'Three of a Kind', highCard: sortedCards[0].value };
    }

    // Check for straight
    if (isStraight(cards)) {
        return { rank: HAND_RANKINGS.STRAIGHT, name: 'Straight', highCard: sortedCards[0].value };
    }

    // Check for flush
    if (isFlush(cards)) {
        return { rank: HAND_RANKINGS.FLUSH, name: 'Flush', highCard: sortedCards[0].value };
    }

    // Check for pair
    if (isPair(cards)) {
        const pairValue = getPairValue(cards);
        return { rank: HAND_RANKINGS.PAIR, name: 'Pair', highCard: pairValue };
    }

    // High card
    return { rank: HAND_RANKINGS.HIGH_CARD, name: 'High Card', highCard: sortedCards[0].value };
}

// Helper functions for hand evaluation
function isFlush(cards) {
    return cards.every(card => card.suit === cards[0].suit);
}

function isStraight(cards) {
    const values = cards.map(card => card.value).sort((a, b) => a - b);

    // Check for A-2-3 straight (special case)
    if (values[0] === 2 && values[1] === 3 && values[2] === 14) {
        return true;
    }

    // Check for regular straight
    return values[1] === values[0] + 1 && values[2] === values[1] + 1;
}

function isThreeOfAKind(cards) {
    const values = cards.map(card => card.value);
    return values[0] === values[1] && values[1] === values[2];
}

function isPair(cards) {
    const values = cards.map(card => card.value);
    return values[0] === values[1] || values[1] === values[2] || values[0] === values[2];
}

function getPairValue(cards) {
    const values = cards.map(card => card.value);
    if (values[0] === values[1]) return values[0];
    if (values[1] === values[2]) return values[1];
    if (values[0] === values[2]) return values[0];
    return 0;
}

// Player chooses to play
function playHand() {
    threeCardPokerGame.playBet = threeCardPokerGame.anteBet;
    threeCardPokerGame.totalBet += threeCardPokerGame.playBet;

    // Deduct play bet
    balance -= threeCardPokerGame.playBet;
    updateBalance();

    // Hide actions
    document.getElementById('playerActions').classList.add('hidden');

    // Update display
    updateBetDisplay();

    // Reveal dealer cards and resolve
    setTimeout(() => {
        revealDealerCards();
        setTimeout(() => resolveHand(), 1000);
    }, 500);
}

// Player chooses to fold
function foldHand() {
    threeCardPokerGame.gameResult = 'fold';

    // Update stats
    updateGameStatsAfterHand(false, 0);

    // Hide actions
    document.getElementById('playerActions').classList.add('hidden');

    document.getElementById('gameStatus').textContent = 'You folded';
    document.getElementById('gameMessage').innerHTML =
        `<span class="text-red-400">You folded and lost $${threeCardPokerGame.totalBet}</span>`;

    setTimeout(() => resetGame(), 3000);
}

// Reveal dealer cards
function revealDealerCards() {
    displayDealerCards(false);
    evaluateDealerHand();
    document.getElementById('gameStatus').textContent = 'Dealer cards revealed...';
}

// Resolve hand with extreme house bias
function resolveHand() {
    const modeData = MAVERICK_MODES[threeCardPokerGame.gameMode];
    const difficultyData = MAVERICK_DIFFICULTIES[threeCardPokerGame.difficulty];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if dealer qualifies (Queen high or better)
    const dealerQualifies = checkDealerQualification();

    // Apply qualifier bias - make dealer qualify more often
    const biasedQualifies = dealerQualifies || (Math.random() < modeData.qualifierBias);

    if (!biasedQualifies) {
        // Dealer doesn't qualify - ante pays even money, play bet pushes
        const anteWin = threeCardPokerGame.anteBet * 1;
        totalWinnings += anteWin;
        resultMessage = `Dealer doesn't qualify. Ante wins $${anteWin}`;
        threeCardPokerGame.gameResult = 'dealer_no_qualify';
    } else {
        // Dealer qualifies - compare hands with heavy bias
        const comparison = compareHandsWithBias();

        if (comparison > 0) {
            // Player wins (rare)
            const anteWin = calculateAnteWinnings();
            const playWin = calculatePlayWinnings();
            totalWinnings = anteWin + playWin;
            resultMessage = `You win! +$${totalWinnings}`;
            threeCardPokerGame.gameResult = 'win';
        } else if (comparison < 0) {
            // Dealer wins (most common)
            totalWinnings = 0;
            resultMessage = `Dealer wins. Lost $${threeCardPokerGame.anteBet + threeCardPokerGame.playBet}`;
            threeCardPokerGame.gameResult = 'lose';
        } else {
            // Tie (return bets)
            totalWinnings = threeCardPokerGame.anteBet + threeCardPokerGame.playBet;
            resultMessage = `Tie. Bets returned.`;
            threeCardPokerGame.gameResult = 'tie';
        }
    }

    // Calculate Pair Plus winnings (if any)
    if (threeCardPokerGame.pairPlusBet > 0) {
        const pairPlusWin = calculatePairPlusWinnings();
        totalWinnings += pairPlusWin;
        if (pairPlusWin > 0) {
            resultMessage += ` Pair Plus: +$${pairPlusWin}`;
        }
    }

    // Apply final house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier * (1 - difficultyData.payoutPenalty));

    // Add winnings to balance
    balance += totalWinnings;
    threeCardPokerGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > threeCardPokerGame.totalBet, totalWinnings);

    document.getElementById('gameStatus').textContent = threeCardPokerGame.gameResult === 'win' ? 'You Win!' : 'Dealer Wins';
    document.getElementById('gameMessage').innerHTML = resultMessage;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 4000);
}

// Check dealer qualification with bias
function checkDealerQualification() {
    const dealerHand = threeCardPokerGame.dealerHand;

    // Standard qualification: Queen high or better
    if (dealerHand.rank > HAND_RANKINGS.HIGH_CARD) {
        return true; // Any pair or better
    }

    if (dealerHand.rank === HAND_RANKINGS.HIGH_CARD && dealerHand.highCard >= 12) {
        return true; // Queen high or better
    }

    return false;
}

// Compare hands with heavy bias toward dealer
function compareHandsWithBias() {
    const playerHand = threeCardPokerGame.playerHand;
    const dealerHand = threeCardPokerGame.dealerHand;
    const difficultyData = MAVERICK_DIFFICULTIES[threeCardPokerGame.difficulty];

    // Apply dealer luck bonus
    const dealerBonusRank = dealerHand.rank + (difficultyData.dealerLuck * 2);
    const dealerBonusHigh = dealerHand.highCard + (difficultyData.dealerLuck * 3);

    // Compare ranks first
    if (playerHand.rank > dealerBonusRank) {
        return 1; // Player wins
    } else if (playerHand.rank < dealerBonusRank) {
        return -1; // Dealer wins
    } else {
        // Same rank, compare high cards
        if (playerHand.highCard > dealerBonusHigh) {
            return 1; // Player wins
        } else if (playerHand.highCard < dealerBonusHigh) {
            return -1; // Dealer wins
        } else {
            return 0; // Tie
        }
    }
}

// Calculate ante winnings (reduced payouts)
function calculateAnteWinnings() {
    const playerHand = threeCardPokerGame.playerHand;
    let multiplier = 1; // Base even money

    // Ante bonus for premium hands (severely reduced)
    if (playerHand.rank === HAND_RANKINGS.STRAIGHT_FLUSH) {
        multiplier += MAVERICK_PAYOUTS.ANTE_BONUS_SF;
    } else if (playerHand.rank === HAND_RANKINGS.THREE_OF_A_KIND) {
        multiplier += MAVERICK_PAYOUTS.ANTE_BONUS_3K;
    } else if (playerHand.rank === HAND_RANKINGS.STRAIGHT) {
        multiplier += MAVERICK_PAYOUTS.ANTE_BONUS_ST;
    }

    return Math.floor(threeCardPokerGame.anteBet * multiplier);
}

// Calculate play bet winnings
function calculatePlayWinnings() {
    const playerHand = threeCardPokerGame.playerHand;
    let multiplier;

    // Reduced payouts for play bet
    switch (playerHand.rank) {
        case HAND_RANKINGS.STRAIGHT_FLUSH:
            multiplier = MAVERICK_PAYOUTS.STRAIGHT_FLUSH;
            break;
        case HAND_RANKINGS.THREE_OF_A_KIND:
            multiplier = MAVERICK_PAYOUTS.THREE_OF_A_KIND;
            break;
        case HAND_RANKINGS.STRAIGHT:
            multiplier = MAVERICK_PAYOUTS.STRAIGHT;
            break;
        case HAND_RANKINGS.FLUSH:
            multiplier = MAVERICK_PAYOUTS.FLUSH;
            break;
        case HAND_RANKINGS.PAIR:
            multiplier = MAVERICK_PAYOUTS.PAIR;
            break;
        default:
            multiplier = MAVERICK_PAYOUTS.HIGH_CARD;
    }

    return Math.floor(threeCardPokerGame.playBet * multiplier);
}

// Calculate Pair Plus winnings (severely reduced)
function calculatePairPlusWinnings() {
    const playerHand = threeCardPokerGame.playerHand;
    let multiplier = 0;

    // Only pay for pair or better
    switch (playerHand.rank) {
        case HAND_RANKINGS.STRAIGHT_FLUSH:
            multiplier = MAVERICK_PAYOUTS.STRAIGHT_FLUSH_PP;
            break;
        case HAND_RANKINGS.THREE_OF_A_KIND:
            multiplier = MAVERICK_PAYOUTS.THREE_OF_A_KIND_PP;
            break;
        case HAND_RANKINGS.STRAIGHT:
            multiplier = MAVERICK_PAYOUTS.STRAIGHT_PP;
            break;
        case HAND_RANKINGS.FLUSH:
            multiplier = MAVERICK_PAYOUTS.FLUSH_PP;
            break;
        case HAND_RANKINGS.PAIR:
            multiplier = MAVERICK_PAYOUTS.PAIR_PP;
            break;
        default:
            return 0; // No payout for high card
    }

    return Math.floor(threeCardPokerGame.pairPlusBet * multiplier);
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('annew_games_added_need_modifi/cards games/under devlop/Three Card Poker: Maverick/script.jsteDisplay').textContent = `$${threeCardPokerGame.anteBet}`;
    document.getElementById('playDisplay').textContent = `$${threeCardPokerGame.playBet}`;
    document.getElementById('pairPlusDisplay').textContent = `$${threeCardPokerGame.pairPlusBet}`;
    document.getElementById('totalBetDisplay').textContent = `$${threeCardPokerGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = threeCardPokerGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${threeCardPokerGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${threeCardPokerGame.stats.totalWagered}`;

    const netResult = threeCardPokerGame.stats.totalWon - threeCardPokerGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    threeCardPokerGame.stats.handsPlayed++;
    threeCardPokerGame.stats.totalWagered += threeCardPokerGame.totalBet;
    threeCardPokerGame.stats.totalWon += winnings;

    if (won) {
        threeCardPokerGame.stats.handsWon++;
        threeCardPokerGame.stats.currentStreak++;
        threeCardPokerGame.streakData.currentWinStreak++;
        threeCardPokerGame.streakData.currentLossStreak = 0;

        if (threeCardPokerGame.streakData.currentWinStreak > threeCardPokerGame.streakData.longestWinStreak) {
            threeCardPokerGame.streakData.longestWinStreak = threeCardPokerGame.streakData.currentWinStreak;
        }

        if (winnings > threeCardPokerGame.stats.biggestWin) {
            threeCardPokerGame.stats.biggestWin = winnings;
        }
    } else {
        threeCardPokerGame.stats.currentStreak = 0;
        threeCardPokerGame.streakData.currentWinStreak = 0;
        threeCardPokerGame.streakData.currentLossStreak++;

        if (threeCardPokerGame.streakData.currentLossStreak > threeCardPokerGame.streakData.longestLossStreak) {
            threeCardPokerGame.streakData.longestLossStreak = threeCardPokerGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be very low due to house edge)
    threeCardPokerGame.stats.winRate = (threeCardPokerGame.stats.handsWon / threeCardPokerGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    threeCardPokerGame.isPlaying = false;
    threeCardPokerGame.anteBet = 0;
    threeCardPokerGame.playBet = 0;
    threeCardPokerGame.pairPlusBet = 0;
    threeCardPokerGame.totalBet = 0;
    threeCardPokerGame.playerCards = [];
    threeCardPokerGame.dealerCards = [];
    threeCardPokerGame.playerHand = null;
    threeCardPokerGame.dealerHand = null;
    threeCardPokerGame.gameResult = '';
    threeCardPokerGame.totalWin = 0;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    document.getElementById('playerHandType').textContent = '-';
    document.getElementById('dealerHandType').textContent = '-';
    document.getElementById('handStrength').textContent = '-';
    document.getElementById('winAmount').classList.add('hidden');

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;
    document.getElementById('playerActions').classList.add('hidden');

    // Reset status
    document.getElementById('gameStatus').textContent = 'Place your bets';
    document.getElementById('gameMessage').textContent = 'Welcome to Three Card Poker: Maverick';

    // Reinitialize deck for next hand
    initializeDeck();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadThreeCardPokerGame();
});