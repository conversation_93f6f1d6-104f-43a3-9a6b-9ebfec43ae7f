// Pai Gow: Dynasty Tiles - Traditional Chinese domino game with extremely low win rate (<10%)
let paiGowGame = {
    playerTiles: [],
    dealerTiles: [],
    playerHands: { high: [], low: [] },
    dealerHands: { high: [], low: [] },
    betAmount: 0,
    gamePhase: 'betting', // betting, arranging, revealing, complete
    gameResult: null,
    totalWin: 0,
    difficulty: 'normal', // easy, normal, hard, dynasty
    gameMode: 'classic', // classic, dynasty, imperial
    commission: 0.05, // 5% commission on wins
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        handsPushed: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        commissionsLost: 0,
        dynastyWins: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Pai Gow tile set (32 tiles total)
const PAI_GOW_TILES = [
    // Supreme tiles (highest ranking)
    { name: '<PERSON> (<PERSON>)', dots: [6, 6], rank: 1, value: 12, isSupreme: true },
    { name: '<PERSON> (<PERSON>)', dots: [3, 3], rank: 1, value: 6, isSupreme: true },
    
    // Teen and Day (second highest)
    { name: 'Teen', dots: [6, 6], rank: 2, value: 12 },
    { name: 'Day', dots: [1, 1], rank: 3, value: 2 },
    
    // Yun tiles
    { name: 'Yun (Red)', dots: [4, 4], rank: 4, value: 8 },
    { name: 'Yun (Black)', dots: [1, 3], rank: 4, value: 4 },
    
    // Gor tiles
    { name: 'Gor (Red)', dots: [1, 1], rank: 5, value: 2 },
    { name: 'Gor (Black)', dots: [6, 6], rank: 5, value: 12 },
    
    // Mooy tiles
    { name: 'Mooy (Red)', dots: [1, 5], rank: 6, value: 6 },
    { name: 'Mooy (Black)', dots: [2, 4], rank: 6, value: 6 },
    
    // Chong tiles
    { name: 'Chong (Red)', dots: [5, 5], rank: 7, value: 10 },
    { name: 'Chong (Black)', dots: [3, 3], rank: 7, value: 6 },
    
    // Bon tiles
    { name: 'Bon (Red)', dots: [4, 4], rank: 8, value: 8 },
    { name: 'Bon (Black)', dots: [2, 2], rank: 8, value: 4 },
    
    // Foo tiles
    { name: 'Foo (Red)', dots: [6, 1], rank: 9, value: 7 },
    { name: 'Foo (Black)', dots: [5, 1], rank: 9, value: 6 },
    
    // Ping tiles
    { name: 'Ping (Red)', dots: [4, 2], rank: 10, value: 6 },
    { name: 'Ping (Black)', dots: [3, 1], rank: 10, value: 4 },
    
    // Tit tiles
    { name: 'Tit (Red)', dots: [5, 5], rank: 11, value: 10 },
    { name: 'Tit (Black)', dots: [4, 2], rank: 11, value: 6 },
    
    // Look tiles
    { name: 'Look (Red)', dots: [6, 4], rank: 12, value: 10 },
    { name: 'Look (Black)', dots: [1, 1], rank: 12, value: 2 },
    
    // Chop Gow (lowest ranking mixed tiles)
    { name: 'Chop Gow 1', dots: [2, 1], rank: 13, value: 3 },
    { name: 'Chop Gow 2', dots: [4, 1], rank: 13, value: 5 },
    { name: 'Chop Gow 3', dots: [3, 2], rank: 13, value: 5 },
    { name: 'Chop Gow 4', dots: [5, 2], rank: 13, value: 7 },
    { name: 'Chop Gow 5', dots: [6, 2], rank: 13, value: 8 },
    { name: 'Chop Gow 6', dots: [3, 1], rank: 13, value: 4 },
    { name: 'Chop Gow 7', dots: [5, 3], rank: 13, value: 8 },
    { name: 'Chop Gow 8', dots: [6, 3], rank: 13, value: 9 },
    { name: 'Chop Gow 9', dots: [4, 3], rank: 13, value: 7 },
    { name: 'Chop Gow 10', dots: [5, 4], rank: 13, value: 9 }
];

// Game modes with heavy house advantage
const PAI_GOW_MODES = {
    classic: { name: 'Classic', houseEdge: 0.20, tileBias: 0.15, commission: 0.05 },
    dynasty: { name: 'Dynasty', houseEdge: 0.30, tileBias: 0.25, commission: 0.07 },
    imperial: { name: 'Imperial', houseEdge: 0.40, tileBias: 0.35, commission: 0.10 }
};

const PAI_GOW_DIFFICULTIES = {
    easy: { name: 'Easy', payoutReduction: 0.15, dealerAdvantage: 0.10 },
    normal: { name: 'Normal', payoutReduction: 0.25, dealerAdvantage: 0.20 },
    hard: { name: 'Hard', payoutReduction: 0.35, dealerAdvantage: 0.30 },
    dynasty: { name: 'Dynasty Master', payoutReduction: 0.45, dealerAdvantage: 0.40 }
};

// Extremely low payout structure
const PAI_GOW_PAYOUTS = {
    WIN: 0.85,      // Reduced from 1:1 (minus commission)
    PUSH: 0,        // No payout for ties
    LOSS: 0         // Lose entire bet
};

function loadPaiGowGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Betting Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                <h4 class="text-xl font-bold mb-4 text-red-300 font-mono">DYNASTY STAKES</h4>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">BET AMOUNT</label>
                    <input type="number" id="paiGowBet" value="25" min="5" max="500" step="5"
                           class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">GAME MODE</label>
                    <select id="paiGowMode" class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                        <option value="classic">Classic (5% Commission)</option>
                        <option value="dynasty">Dynasty (7% Commission)</option>
                        <option value="imperial">Imperial (10% Commission)</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">DIFFICULTY</label>
                    <select id="paiGowDifficulty" class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                        <option value="easy">Easy</option>
                        <option value="normal">Normal</option>
                        <option value="hard">Hard</option>
                        <option value="dynasty">Dynasty Master</option>
                    </select>
                </div>
                
                <button id="dealTiles" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                    DEAL TILES
                </button>
                
                <div class="space-y-2">
                    <button id="autoArrange" class="w-full bg-blue-600 hover:bg-blue-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        AUTO ARRANGE
                    </button>
                    <button id="submitHands" class="w-full bg-green-600 hover:bg-green-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        SUBMIT HANDS
                    </button>
                </div>
                
                <div class="mt-4 p-3 bg-red-900/30 rounded-lg border border-red-500/30">
                    <p class="text-red-300 font-mono text-sm">Commission: <span id="commissionRate">5%</span></p>
                    <p class="text-white">Current Bet: <span id="currentBet">0</span> GA</p>
                    <p class="text-gray-300 text-xs">*Commission deducted from wins</p>
                </div>
            </div>
            
            <!-- Game Table -->
            <div class="col-span-2 bg-black/30 p-6 rounded-xl border border-red-500/30">
                <h4 class="text-xl font-bold mb-4 text-red-300 font-mono text-center">PAI GOW TABLE</h4>
                
                <!-- Dealer Hands -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Dealer Hands</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-black/50 p-3 rounded border border-red-500/30">
                            <p class="text-sm text-gray-400 mb-2">High Hand</p>
                            <div id="dealerHighHand" class="flex gap-1 justify-center min-h-[60px]">
                                <div class="tile-back">?</div>
                                <div class="tile-back">?</div>
                            </div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-red-500/30">
                            <p class="text-sm text-gray-400 mb-2">Low Hand</p>
                            <div id="dealerLowHand" class="flex gap-1 justify-center min-h-[60px]">
                                <div class="tile-back">?</div>
                                <div class="tile-back">?</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Player Tiles -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Your Tiles</h5>
                    <div id="playerTiles" class="flex gap-2 justify-center flex-wrap min-h-[80px]">
                        <!-- Tiles will be populated here -->
                    </div>
                </div>
                
                <!-- Player Hands -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Your Hands</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-black/50 p-3 rounded border border-red-500/30">
                            <p class="text-sm text-gray-400 mb-2">High Hand (2 tiles)</p>
                            <div id="playerHighHand" class="flex gap-1 justify-center min-h-[60px] drop-zone">
                                <!-- Drop zone for high hand -->
                            </div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-red-500/30">
                            <p class="text-sm text-gray-400 mb-2">Low Hand (2 tiles)</p>
                            <div id="playerLowHand" class="flex gap-1 justify-center min-h-[60px] drop-zone">
                                <!-- Drop zone for low hand -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Game Status -->
                <div class="text-center">
                    <div id="gameStatus" class="text-lg font-semibold text-red-300 mb-2">Place your bet to start</div>
                    <div id="gameResult" class="text-center text-xl font-bold h-8 font-mono"></div>
                </div>
            </div>
            
            <!-- Stats Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                <h4 class="text-xl font-bold mb-4 text-red-300 font-mono">DYNASTY STATS</h4>
                
                <div class="space-y-3">
                    <div class="bg-black/50 p-2 rounded border border-red-500/30">
                        <p class="text-gray-300 text-sm">Hands Played: <span id="paiGowHandsPlayed" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-red-500/30">
                        <p class="text-gray-300 text-sm">Win Rate: <span id="paiGowWinRate" class="text-white">0%</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-red-500/30">
                        <p class="text-gray-300 text-sm">Push Rate: <span id="paiGowPushRate" class="text-white">0%</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-red-500/30">
                        <p class="text-gray-300 text-sm">Total Wagered: <span id="paiGowTotalWagered" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-red-500/30">
                        <p class="text-gray-300 text-sm">Total Won: <span id="paiGowTotalWon" class="text-white">0</span></p>
                    </div>
                    <div class="bg-red-900/50 p-2 rounded border border-red-500/30">
                        <p class="text-red-300 text-sm">Commissions Lost: <span id="paiGowCommissions" class="text-white">0</span></p>
                    </div>
                    <div class="bg-red-900/50 p-2 rounded border border-red-500/30">
                        <p class="text-red-300 text-sm">Loss Streak: <span id="paiGowLossStreak" class="text-white">0</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 bg-black/30 p-4 rounded-xl border border-red-500/30">
            <h4 class="text-xl font-bold mb-4 text-red-300 font-mono">TILE RANKINGS</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                <div class="bg-black/50 p-2 rounded border border-red-500/30">
                    <p class="text-red-400">1. Gee Joon (Supreme)</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-red-500/30">
                    <p class="text-red-400">2. Teen (12 dots)</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-red-500/30">
                    <p class="text-red-400">3. Day (2 dots)</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-red-500/30">
                    <p class="text-red-400">4-12. Ranked Pairs</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-red-500/30">
                    <p class="text-red-400">13. Chop Gow (Mixed)</p>
                </div>
            </div>
            <p class="text-red-400 text-xs mt-2">* High hand must outrank low hand</p>
            <p class="text-red-400 text-xs">* Commission deducted from all wins</p>
        </div>
    `;
    setupPaiGowGame();
}

function setupPaiGowGame() {
    document.getElementById('dealTiles').addEventListener('click', startNewPaiGowGame);
    document.getElementById('autoArrange').addEventListener('click', autoArrangeTiles);
    document.getElementById('submitHands').addEventListener('click', submitPlayerHands);
    
    document.getElementById('paiGowMode').addEventListener('change', function() {
        paiGowGame.gameMode = this.value;
        updateCommissionDisplay();
        updatePaiGowDisplay();
    });
    
    document.getElementById('paiGowDifficulty').addEventListener('change', function() {
        paiGowGame.difficulty = this.value;
        updatePaiGowDisplay();
    });
    
    updateCommissionDisplay();
    updatePaiGowDisplay();
}

function createBiasedTileSet() {
    const modeData = PAI_GOW_MODES[paiGowGame.gameMode];
    const difficultyData = PAI_GOW_DIFFICULTIES[paiGowGame.difficulty];
    
    // Create heavily biased tile set against player
    const biasedTiles = [];
    
    PAI_GOW_TILES.forEach(tile => {
        let tileWeight = 1;
        
        // Reduce high-ranking tiles for player
        if (tile.rank <= 5) {
            tileWeight *= (1 - modeData.tileBias);
        }
        
        // Reduce supreme tiles significantly
        if (tile.isSupreme) {
            tileWeight *= (1 - difficultyData.dealerAdvantage);
        }
        
        // Add tiles based on weight
        const tileCount = Math.max(1, Math.floor(tileWeight * 8));
        for (let i = 0; i < tileCount; i++) {
            biasedTiles.push({ ...tile, id: `${tile.name}_${i}` });
        }
    });
    
    return shuffleTiles(biasedTiles);
}

function shuffleTiles(tiles) {
    for (let i = tiles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [tiles[i], tiles[j]] = [tiles[j], tiles[i]];
    }
    return tiles;
}

function startNewPaiGowGame() {
    const betAmount = parseInt(document.getElementById('paiGowBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Initialize game
    const tileSet = createBiasedTileSet();
    paiGowGame.playerTiles = tileSet.slice(0, 4);
    paiGowGame.dealerTiles = tileSet.slice(4, 8);
    paiGowGame.playerHands = { high: [], low: [] };
    paiGowGame.dealerHands = { high: [], low: [] };
    paiGowGame.betAmount = betAmount;
    paiGowGame.gamePhase = 'arranging';
    paiGowGame.gameResult = null;
    paiGowGame.totalWin = 0;
    
    paiGowGame.stats.totalWagered += betAmount;
    
    // Display player tiles
    displayPlayerTiles();
    
    // Arrange dealer hands optimally (with bias)
    arrangeDealerHands();
    
    // Update display
    updatePaiGowDisplay();
    updateGameStatus('Arrange your tiles into two hands. High hand must be stronger than low hand.');
    
    // Enable arrangement buttons
    document.getElementById('autoArrange').disabled = false;
    document.getElementById('submitHands').disabled = false;
    document.getElementById('dealTiles').disabled = true;
}

function displayPlayerTiles() {
    const tilesEl = document.getElementById('playerTiles');
    tilesEl.innerHTML = '';
    
    paiGowGame.playerTiles.forEach((tile, index) => {
        const tileEl = document.createElement('div');
        tileEl.className = 'pai-gow-tile draggable';
        tileEl.draggable = true;
        tileEl.dataset.tileIndex = index;
        tileEl.innerHTML = `
            <div class="tile-content">
                <div class="tile-name">${tile.name}</div>
                <div class="tile-dots">
                    <span class="dot-group">${'●'.repeat(tile.dots[0])}</span>
                    <span class="dot-group">${'●'.repeat(tile.dots[1])}</span>
                </div>
                <div class="tile-value">${tile.value}</div>
            </div>
        `;
        
        // Add drag event listeners
        tileEl.addEventListener('dragstart', handleTileDragStart);
        tileEl.addEventListener('click', handleTileClick);
        
        tilesEl.appendChild(tileEl);
    });
    
    setupDropZones();
}

function setupDropZones() {
    const dropZones = document.querySelectorAll('.drop-zone');
    
    dropZones.forEach(zone => {
        zone.addEventListener('dragover', handleDragOver);
        zone.addEventListener('drop', handleDrop);
    });
}

function handleTileDragStart(e) {
    e.dataTransfer.setData('text/plain', e.target.dataset.tileIndex);
}

function handleTileClick(e) {
    const tileIndex = parseInt(e.target.dataset.tileIndex);
    const tile = paiGowGame.playerTiles[tileIndex];
    
    // Auto-place in appropriate hand
    if (paiGowGame.playerHands.high.length < 2) {
        placeTileInHand(tile, 'high');
    } else if (paiGowGame.playerHands.low.length < 2) {
        placeTileInHand(tile, 'low');
    }
    
    updateHandDisplays();
}

function handleDragOver(e) {
    e.preventDefault();
}

function handleDrop(e) {
    e.preventDefault();
    const tileIndex = parseInt(e.dataTransfer.getData('text/plain'));
    const tile = paiGowGame.playerTiles[tileIndex];
    const handType = e.target.closest('#playerHighHand') ? 'high' : 'low';
    
    placeTileInHand(tile, handType);
    updateHandDisplays();
}

function placeTileInHand(tile, handType) {
    const hand = paiGowGame.playerHands[handType];
    
    if (hand.length < 2) {
        hand.push(tile);
        
        // Remove from available tiles
        const index = paiGowGame.playerTiles.indexOf(tile);
        if (index > -1) {
            paiGowGame.playerTiles.splice(index, 1);
        }
        
        displayPlayerTiles();
    }
}

function updateHandDisplays() {
    // Update high hand display
    const highHandEl = document.getElementById('playerHighHand');
    highHandEl.innerHTML = '';
    paiGowGame.playerHands.high.forEach(tile => {
        const tileEl = createTileElement(tile);
        tileEl.addEventListener('click', () => removeTileFromHand(tile, 'high'));
        highHandEl.appendChild(tileEl);
    });
    
    // Update low hand display
    const lowHandEl = document.getElementById('playerLowHand');
    lowHandEl.innerHTML = '';
    paiGowGame.playerHands.low.forEach(tile => {
        const tileEl = createTileElement(tile);
        tileEl.addEventListener('click', () => removeTileFromHand(tile, 'low'));
        lowHandEl.appendChild(tileEl);
    });
}

function removeTileFromHand(tile, handType) {
    const hand = paiGowGame.playerHands[handType];
    const index = hand.indexOf(tile);
    
    if (index > -1) {
        hand.splice(index, 1);
        paiGowGame.playerTiles.push(tile);
        displayPlayerTiles();
        updateHandDisplays();
    }
}

function createTileElement(tile) {
    const tileEl = document.createElement('div');
    tileEl.className = 'pai-gow-tile small';
    tileEl.innerHTML = `
        <div class="tile-content">
            <div class="tile-name">${tile.name}</div>
            <div class="tile-dots">
                <span class="dot-group">${'●'.repeat(tile.dots[0])}</span>
                <span class="dot-group">${'●'.repeat(tile.dots[1])}</span>
            </div>
            <div class="tile-value">${tile.value}</div>
        </div>
    `;
    return tileEl;
}

function autoArrangeTiles() {
    // Reset hands
    paiGowGame.playerHands = { high: [], low: [] };
    paiGowGame.playerTiles = [...paiGowGame.playerTiles, ...paiGowGame.playerHands.high, ...paiGowGame.playerHands.low];
    
    // Sort tiles by rank (ascending for suboptimal arrangement)
    const sortedTiles = [...paiGowGame.playerTiles].sort((a, b) => b.rank - a.rank);
    
    // Deliberately suboptimal arrangement to favor house
    paiGowGame.playerHands.high = [sortedTiles[0], sortedTiles[3]];
    paiGowGame.playerHands.low = [sortedTiles[1], sortedTiles[2]];
    paiGowGame.playerTiles = [];
    
    updateHandDisplays();
    displayPlayerTiles();
}

function submitPlayerHands() {
    if (paiGowGame.playerHands.high.length !== 2 || paiGowGame.playerHands.low.length !== 2) {
        alert('You must arrange all 4 tiles into two hands of 2 tiles each!');
        return;
    }
    
    // Validate hand arrangement (high hand must be stronger)
    const highHandValue = calculateHandValue(paiGowGame.playerHands.high);
    const lowHandValue = calculateHandValue(paiGowGame.playerHands.low);
    
    if (highHandValue <= lowHandValue) {
        alert('Invalid arrangement! High hand must be stronger than low hand.');
        return;
    }
    
    paiGowGame.gamePhase = 'revealing';
    
    // Reveal dealer hands
    displayDealerHands();
    
    // Determine winner
    determineWinner();
    
    // Update display
    updatePaiGowDisplay();
    
    // Disable buttons
    document.getElementById('autoArrange').disabled = true;
    document.getElementById('submitHands').disabled = true;
    document.getElementById('dealTiles').disabled = false;
}

function arrangeDealerHands() {
    // Dealer arranges optimally with bias
    const tiles = [...paiGowGame.dealerTiles];
    
    // Find best arrangement for dealer (with house advantage)
    let bestArrangement = findOptimalArrangement(tiles, true);
    
    paiGowGame.dealerHands.high = bestArrangement.high;
    paiGowGame.dealerHands.low = bestArrangement.low;
}

function findOptimalArrangement(tiles, isDealer = false) {
    const arrangements = [];
    
    // Generate all possible arrangements
    for (let i = 0; i < tiles.length; i++) {
        for (let j = i + 1; j < tiles.length; j++) {
            for (let k = 0; k < tiles.length; k++) {
                if (k === i || k === j) continue;
                for (let l = k + 1; l < tiles.length; l++) {
                    if (l === i || l === j) continue;
                    
                    const high = [tiles[i], tiles[j]];
                    const low = [tiles[k], tiles[l]];
                    
                    const highValue = calculateHandValue(high);
                    const lowValue = calculateHandValue(low);
                    
                    if (highValue > lowValue) {
                        arrangements.push({
                            high,
                            low,
                            highValue,
                            lowValue,
                            totalValue: highValue + lowValue
                        });
                    }
                }
            }
        }
    }
    
    // Sort by total value (dealer gets best, player gets suboptimal)
    arrangements.sort((a, b) => b.totalValue - a.totalValue);
    
    if (isDealer) {
        return arrangements[0] || { high: tiles.slice(0, 2), low: tiles.slice(2, 4) };
    } else {
        // Return suboptimal arrangement for player
        return arrangements[Math.floor(arrangements.length * 0.7)] || arrangements[0] || { high: tiles.slice(0, 2), low: tiles.slice(2, 4) };
    }
}

function calculateHandValue(hand) {
    if (hand.length !== 2) return 0;
    
    const [tile1, tile2] = hand;
    
    // Check for special pairs
    if (tile1.name === tile2.name) {
        return 1000 + tile1.rank; // Pairs are highest
    }
    
    // Check for special combinations
    if ((tile1.isSupreme && tile2.isSupreme)) {
        return 2000; // Supreme pair
    }
    
    // Calculate point value (mod 10)
    const pointValue = (tile1.value + tile2.value) % 10;
    return pointValue;
}

function displayDealerHands() {
    // Display dealer high hand
    const dealerHighEl = document.getElementById('dealerHighHand');
    dealerHighEl.innerHTML = '';
    paiGowGame.dealerHands.high.forEach(tile => {
        dealerHighEl.appendChild(createTileElement(tile));
    });
    
    // Display dealer low hand
    const dealerLowEl = document.getElementById('dealerLowHand');
    dealerLowEl.innerHTML = '';
    paiGowGame.dealerHands.low.forEach(tile => {
        dealerLowEl.appendChild(createTileElement(tile));
    });
}

function determineWinner() {
    const playerHighValue = calculateHandValue(paiGowGame.playerHands.high);
    const playerLowValue = calculateHandValue(paiGowGame.playerHands.low);
    const dealerHighValue = calculateHandValue(paiGowGame.dealerHands.high);
    const dealerLowValue = calculateHandValue(paiGowGame.dealerHands.low);
    
    let playerWins = 0;
    let dealerWins = 0;
    
    // Compare high hands
    if (playerHighValue > dealerHighValue) {
        playerWins++;
    } else if (dealerHighValue > playerHighValue) {
        dealerWins++;
    }
    
    // Compare low hands
    if (playerLowValue > dealerLowValue) {
        playerWins++;
    } else if (dealerLowValue > playerLowValue) {
        dealerWins++;
    }
    
    // Determine result
    if (playerWins === 2) {
        paiGowGame.gameResult = 'win';
        calculateWinnings();
        updateGameStatus('You win both hands!');
    } else if (dealerWins === 2) {
        paiGowGame.gameResult = 'loss';
        paiGowGame.totalWin = 0;
        updateGameStatus('Dealer wins both hands. You lose.');
    } else {
        paiGowGame.gameResult = 'push';
        paiGowGame.totalWin = paiGowGame.betAmount; // Return bet
        balance += paiGowGame.betAmount;
        updateBalance();
        updateGameStatus('Split hands - Push. Bet returned.');
    }
    
    updateGameStats();
    paiGowGame.gamePhase = 'complete';
}

function calculateWinnings() {
    const modeData = PAI_GOW_MODES[paiGowGame.gameMode];
    const difficultyData = PAI_GOW_DIFFICULTIES[paiGowGame.difficulty];
    
    // Base payout
    let winnings = paiGowGame.betAmount * PAI_GOW_PAYOUTS.WIN;
    
    // Apply difficulty penalty
    winnings *= (1 - difficultyData.payoutReduction);
    
    // Deduct commission
    const commission = winnings * modeData.commission;
    winnings -= commission;
    
    paiGowGame.totalWin = Math.floor(winnings);
    paiGowGame.stats.commissionsLost += Math.floor(commission);
    
    balance += paiGowGame.betAmount + paiGowGame.totalWin; // Return bet + winnings
    updateBalance();
}

function updateGameStats() {
    paiGowGame.stats.handsPlayed++;
    
    if (paiGowGame.gameResult === 'win') {
        paiGowGame.stats.handsWon++;
        paiGowGame.stats.totalWon += paiGowGame.totalWin;
        
        if (paiGowGame.totalWin > paiGowGame.stats.biggestWin) {
            paiGowGame.stats.biggestWin = paiGowGame.totalWin;
        }
        
        // Update win streak
        paiGowGame.streakData.currentWinStreak++;
        paiGowGame.streakData.currentLossStreak = 0;
        
        if (paiGowGame.streakData.currentWinStreak > paiGowGame.streakData.longestWinStreak) {
            paiGowGame.streakData.longestWinStreak = paiGowGame.streakData.currentWinStreak;
        }
    } else if (paiGowGame.gameResult === 'push') {
        paiGowGame.stats.handsPushed++;
        paiGowGame.streakData.currentWinStreak = 0;
        paiGowGame.streakData.currentLossStreak = 0;
    } else {
        // Update loss streak
        paiGowGame.streakData.currentLossStreak++;
        paiGowGame.streakData.currentWinStreak = 0;
        
        if (paiGowGame.streakData.currentLossStreak > paiGowGame.streakData.longestLossStreak) {
            paiGowGame.streakData.longestLossStreak = paiGowGame.streakData.currentLossStreak;
        }
    }
}

function updateCommissionDisplay() {
    const modeData = PAI_GOW_MODES[paiGowGame.gameMode];
    document.getElementById('commissionRate').textContent = `${(modeData.commission * 100).toFixed(0)}%`;
}

function updatePaiGowDisplay() {
    document.getElementById('currentBet').textContent = paiGowGame.betAmount;
    
    // Update game result
    if (paiGowGame.gameResult) {
        const resultEl = document.getElementById('gameResult');
        if (paiGowGame.gameResult === 'win') {
            resultEl.innerHTML = `Victory! Won ${paiGowGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono';
        } else if (paiGowGame.gameResult === 'push') {
            resultEl.innerHTML = `Push - Bet Returned`;
            resultEl.className = 'text-center text-xl font-bold text-yellow-400 h-8 font-mono';
        } else {
            resultEl.innerHTML = `Defeat - Lost ${paiGowGame.betAmount} GA`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    document.getElementById('paiGowHandsPlayed').textContent = paiGowGame.stats.handsPlayed;
    
    const winRate = paiGowGame.stats.handsPlayed > 0 ? 
        ((paiGowGame.stats.handsWon / paiGowGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('paiGowWinRate').textContent = `${winRate}%`;
    
    const pushRate = paiGowGame.stats.handsPlayed > 0 ? 
        ((paiGowGame.stats.handsPushed / paiGowGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('paiGowPushRate').textContent = `${pushRate}%`;
    
    document.getElementById('paiGowTotalWagered').textContent = paiGowGame.stats.totalWagered.toLocaleString();
    document.getElementById('paiGowTotalWon').textContent = paiGowGame.stats.totalWon.toLocaleString();
    document.getElementById('paiGowCommissions').textContent = paiGowGame.stats.commissionsLost.toLocaleString();
    document.getElementById('paiGowLossStreak').textContent = paiGowGame.streakData.currentLossStreak;
}

function updateGameStatus(message) {
    document.getElementById('gameStatus').textContent = message;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPaiGowGame();
});