// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadHoldemGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <h4 class="text-xl font-bold mb-4 text-yellow-400">TEXAS SHOWDOWN</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">ANTE BET</label>
                                <input type="number" id="holdemAnte" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <button id="dealHoldem" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                DEAL CARDS
                            </button>
                            
                            <div id="holdemActions" class="space-y-2 hidden">
                                <button id="holdemFold" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                                    FOLD
                                </button>
                                <button id="holdemCall" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                                    CALL
                                </button>
                                <button id="holdemRaise" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                    RAISE 2x
                                </button>
                            </div>
                            
                            <div class="text-center mt-4">
                                <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                                <div id="holdemTotalBet" class="text-xl font-bold text-yellow-400">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Hand Strength</div>
                                <div id="holdemHandStrength" class="text-lg font-bold text-green-400">-</div>
                            </div>
                        </div>
                        
                        <!-- Hand Rankings -->
                        <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-yellow-400">HAND RANKINGS</h5>
                            <div class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Royal Flush:</span>
                                    <span class="text-yellow-400">500:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Straight Flush:</span>
                                    <span class="text-yellow-400">50:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Four of a Kind:</span>
                                    <span class="text-yellow-400">10:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Full House:</span>
                                    <span class="text-yellow-400">3:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Flush:</span>
                                    <span class="text-yellow-400">2:1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Straight:</span>
                                    <span class="text-yellow-400">1:1</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Poker Table -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <div id="holdemTable" class="relative bg-gradient-to-br from-amber-900 to-yellow-900 rounded-lg p-6 h-96">
                                <!-- Community Cards -->
                                <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-yellow-400 mb-2">COMMUNITY CARDS</div>
                                        <div id="communityCards" class="flex space-x-2">
                                            <!-- Community cards will appear here -->
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Player Cards -->
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-blue-400 mb-2">YOUR CARDS</div>
                                        <div id="playerHoldemCards" class="flex space-x-2">
                                            <!-- Player cards will appear here -->
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Dealer Cards -->
                                <div class="absolute top-1/2 right-4 transform -translate-y-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-red-400 mb-2">DEALER</div>
                                        <div id="dealerHoldemCards" class="flex space-x-2">
                                            <!-- Dealer cards will appear here -->
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Game Phase -->
                                <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                                    <div class="text-center">
                                        <div id="holdemPhase" class="text-xl font-bold text-yellow-400">Pre-Flop</div>
                                    </div>
                                </div>
                            </div>
                            <div id="holdemStatus" class="text-center mt-4 text-lg font-semibold">Place your ante and deal the cards</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeHoldem();
        }
        
        let holdemGame = {
            isPlaying: false,
            phase: 'preflop', // preflop, flop, turn, river, showdown
            anteBet: 0,
            totalBet: 0,
            deck: [],
            playerCards: [],
            dealerCards: [],
            communityCards: [],
            playerHand: null,
            dealerHand: null
        };
        
        function initializeHoldem() {
            document.getElementById('dealHoldem').addEventListener('click', dealHoldemCards);
            document.getElementById('holdemFold').addEventListener('click', () => holdemAction('fold'));
            document.getElementById('holdemCall').addEventListener('click', () => holdemAction('call'));
            document.getElementById('holdemRaise').addEventListener('click', () => holdemAction('raise'));
            
            initializeHoldemDeck();
        }
        
        function initializeHoldemDeck() {
            const suits = ['♠', '♥', '♦', '♣'];
            const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
            
            holdemGame.deck = [];
            for (const suit of suits) {
                for (const rank of ranks) {
                    holdemGame.deck.push({ rank, suit, value: getCardValue(rank) });
                }
            }
            
            // Shuffle deck
            for (let i = holdemGame.deck.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [holdemGame.deck[i], holdemGame.deck[j]] = [holdemGame.deck[j], holdemGame.deck[i]];
            }
        }
        
        function getCardValue(rank) {
            if (rank === 'A') return 14;
            if (rank === 'K') return 13;
            if (rank === 'Q') return 12;
            if (rank === 'J') return 11;
            return parseInt(rank);
        }
        
        function dealHoldemCards() {
            const anteBet = parseInt(document.getElementById('holdemAnte').value);
            
            if (anteBet > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct ante
            balance -= anteBet;
            updateBalance();
            
            holdemGame.isPlaying = true;
            holdemGame.anteBet = anteBet;
            holdemGame.totalBet = anteBet;
            holdemGame.phase = 'preflop';
            
            // Clear cards
            holdemGame.playerCards = [];
            holdemGame.dealerCards = [];
            holdemGame.communityCards = [];
            
            document.getElementById('playerHoldemCards').innerHTML = '';
            document.getElementById('dealerHoldemCards').innerHTML = '';
            document.getElementById('communityCards').innerHTML = '';
            
            document.getElementById('dealHoldem').disabled = true;
            document.getElementById('holdemTotalBet').textContent = '$' + holdemGame.totalBet;
            document.getElementById('holdemPhase').textContent = 'Pre-Flop';
            document.getElementById('holdemStatus').textContent = 'Dealing hole cards...';
            
            // Deal hole cards
            setTimeout(() => dealHoldemCard('player'), 500);
            setTimeout(() => dealHoldemCard('dealer'), 750);
            setTimeout(() => dealHoldemCard('player'), 1000);
            setTimeout(() => dealHoldemCard('dealer'), 1250);
            
            setTimeout(() => {
                evaluatePlayerHand();
                showHoldemActions();
            }, 1500);
        }
        
        function dealHoldemCard(recipient) {
            const card = holdemGame.deck.pop();
            
            if (recipient === 'player') {
                holdemGame.playerCards.push(card);
                displayHoldemCard(card, 'playerHoldemCards');
            } else if (recipient === 'dealer') {
                holdemGame.dealerCards.push(card);
                displayHoldemCard(card, 'dealerHoldemCards', true); // Hide dealer cards initially
            } else if (recipient === 'community') {
                holdemGame.communityCards.push(card);
                displayHoldemCard(card, 'communityCards');
            }
        }
        
        function displayHoldemCard(card, containerId, hidden = false) {
            const cardElement = document.createElement('div');
            cardElement.className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
            
            if (hidden) {
                cardElement.className += ' bg-blue-900 text-blue-300';
                cardElement.innerHTML = '🂠';
            } else {
                const isRed = card.suit === '♥' || card.suit === '♦';
                cardElement.style.color = isRed ? '#dc2626' : '#000';
                cardElement.innerHTML = `
                    <div>${card.rank}</div>
                    <div>${card.suit}</div>
                `;
            }
            
            document.getElementById(containerId).appendChild(cardElement);
        }
        
        function showHoldemActions() {
            document.getElementById('holdemActions').classList.remove('hidden');
            document.getElementById('holdemStatus').textContent = 'Choose your action: Fold, Call, or Raise';
        }
        
        function holdemAction(action) {
            document.getElementById('holdemActions').classList.add('hidden');
            
            if (action === 'fold') {
                document.getElementById('holdemStatus').innerHTML = 
                    `<span class="text-red-400">You folded. Lost $${holdemGame.anteBet}</span>`;
                
                setTimeout(() => resetHoldemGame(), 2000);
                return;
            }
            
            if (action === 'raise') {
                const raiseBet = holdemGame.anteBet * 2;
                if (raiseBet > balance) {
                    alert('Insufficient balance for raise!');
                    showHoldemActions();
                    return;
                }
                
                balance -= raiseBet;
                updateBalance();
                holdemGame.totalBet += raiseBet;
                document.getElementById('holdemTotalBet').textContent = '$' + holdemGame.totalBet;
            }
            
            // Proceed to flop
            proceedToFlop();
        }
        
        function proceedToFlop() {
            holdemGame.phase = 'flop';
            document.getElementById('holdemPhase').textContent = 'Flop';
            document.getElementById('holdemStatus').textContent = 'Dealing the flop...';
            
            // Deal 3 community cards
            setTimeout(() => dealHoldemCard('community'), 500);
            setTimeout(() => dealHoldemCard('community'), 750);
            setTimeout(() => dealHoldemCard('community'), 1000);
            
            setTimeout(() => {
                evaluatePlayerHand();
                proceedToTurn();
            }, 1500);
        }
        
        function proceedToTurn() {
            holdemGame.phase = 'turn';
            document.getElementById('holdemPhase').textContent = 'Turn';
            document.getElementById('holdemStatus').textContent = 'Dealing the turn...';
            
            setTimeout(() => {
                dealHoldemCard('community');
                evaluatePlayerHand();
                setTimeout(() => proceedToRiver(), 1000);
            }, 500);
        }
        
        function proceedToRiver() {
            holdemGame.phase = 'river';
            document.getElementById('holdemPhase').textContent = 'River';
            document.getElementById('holdemStatus').textContent = 'Dealing the river...';
            
            setTimeout(() => {
                dealHoldemCard('community');
                evaluatePlayerHand();
                setTimeout(() => showdown(), 1000);
            }, 500);
        }
        
        function showdown() {
            holdemGame.phase = 'showdown';
            document.getElementById('holdemPhase').textContent = 'Showdown';
            
            // Reveal dealer cards
            const dealerCardElements = document.getElementById('dealerHoldemCards').children;
            for (let i = 0; i < dealerCardElements.length; i++) {
                const card = holdemGame.dealerCards[i];
                const isRed = card.suit === '♥' || card.suit === '♦';
                dealerCardElements[i].style.color = isRed ? '#dc2626' : '#000';
                dealerCardElements[i].className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
                dealerCardElements[i].innerHTML = `
                    <div>${card.rank}</div>
                    <div>${card.suit}</div>
                `;
            }
            
            // Evaluate both hands
            const playerBestHand = getBestPokerHand([...holdemGame.playerCards, ...holdemGame.communityCards]);
            const dealerBestHand = getBestPokerHand([...holdemGame.dealerCards, ...holdemGame.communityCards]);
            
            // Determine winner
            const comparison = comparePokerHands(playerBestHand, dealerBestHand);
            let winnings = 0;
            
            if (comparison > 0) {
                // Player wins
                const handMultiplier = getHandMultiplier(playerBestHand.rank);
                winnings = holdemGame.totalBet * (1 + handMultiplier);
                balance += winnings;
                updateBalance();
                
                document.getElementById('holdemStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">You win with ${playerBestHand.name}! Won $${winnings}</span>`;
            } else if (comparison < 0) {
                // Dealer wins
                document.getElementById('holdemStatus').innerHTML = 
                    `<span class="text-red-400">Dealer wins with ${dealerBestHand.name}. Lost $${holdemGame.totalBet}</span>`;
            } else {
                // Tie
                balance += holdemGame.totalBet; // Return bet
                updateBalance();
                document.getElementById('holdemStatus').innerHTML = 
                    `<span class="text-yellow-400">Tie with ${playerBestHand.name}. Bet returned.</span>`;
            }
            
            setTimeout(() => resetHoldemGame(), 4000);
        }
        
        function evaluatePlayerHand() {
            const allCards = [...holdemGame.playerCards, ...holdemGame.communityCards];
            if (allCards.length >= 2) {
                const bestHand = getBestPokerHand(allCards);
                document.getElementById('holdemHandStrength').textContent = bestHand.name;
            }
        }
        
        function getBestPokerHand(cards) {
            // This is a simplified poker hand evaluation
            // In a real implementation, you'd want a more robust algorithm
            
            if (cards.length < 5) {
                return { rank: 0, name: 'High Card' };
            }
            
            // Sort cards by value
            const sortedCards = cards.sort((a, b) => b.value - a.value);
            
            // Check for flush
            const suits = {};
            cards.forEach(card => {
                suits[card.suit] = (suits[card.suit] || 0) + 1;
            });
            const isFlush = Object.values(suits).some(count => count >= 5);
            
            // Check for straight
            const values = [...new Set(cards.map(card => card.value))].sort((a, b) => b - a);
            let isStraight = false;
            for (let i = 0; i <= values.length - 5; i++) {
                if (values[i] - values[i + 4] === 4) {
                    isStraight = true;
                    break;
                }
            }
            
            // Count values
            const valueCounts = {};
            cards.forEach(card => {
                valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
            });
            
            const counts = Object.values(valueCounts).sort((a, b) => b - a);
            
            // Determine hand rank
            if (isStraight && isFlush) {
                if (values[0] === 14 && values[4] === 10) {
                    return { rank: 9, name: 'Royal Flush' };
                }
                return { rank: 8, name: 'Straight Flush' };
            } else if (counts[0] === 4) {
                return { rank: 7, name: 'Four of a Kind' };
            } else if (counts[0] === 3 && counts[1] === 2) {
                return { rank: 6, name: 'Full House' };
            } else if (isFlush) {
                return { rank: 5, name: 'Flush' };
            } else if (isStraight) {
                return { rank: 4, name: 'Straight' };
            } else if (counts[0] === 3) {
                return { rank: 3, name: 'Three of a Kind' };
            } else if (counts[0] === 2 && counts[1] === 2) {
                return { rank: 2, name: 'Two Pair' };
            } else if (counts[0] === 2) {
                return { rank: 1, name: 'Pair' };
            } else {
                return { rank: 0, name: 'High Card' };
            }
        }
        
        function comparePokerHands(hand1, hand2) {
            return hand1.rank - hand2.rank;
        }
        
        function getHandMultiplier(handRank) {
            const multipliers = [0, 0, 0, 0, 1, 2, 3, 10, 50, 500]; // Index corresponds to hand rank
            return multipliers[handRank] || 0;
        }
        
        function resetHoldemGame() {
            holdemGame.isPlaying = false;
            holdemGame.phase = 'preflop';
            holdemGame.anteBet = 0;
            holdemGame.totalBet = 0;
            
            document.getElementById('dealHoldem').disabled = false;
            document.getElementById('holdemActions').classList.add('hidden');
            document.getElementById('holdemTotalBet').textContent = '0 GA';
            document.getElementById('holdemHandStrength').textContent = '-';
            document.getElementById('holdemPhase').textContent = 'Pre-Flop';
            document.getElementById('holdemStatus').textContent = 'Place your ante and deal the cards';
            
            initializeHoldemDeck();
        }
        
        // Initialize
        updateBalance();

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadHoldemGame();
});