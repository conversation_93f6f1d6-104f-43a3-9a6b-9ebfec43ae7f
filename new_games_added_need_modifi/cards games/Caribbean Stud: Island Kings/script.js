// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Caribbean Stud Poker Implementation
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

const CARIBBEAN_PAYOUTS = {
    ROYAL_FLUSH: 100,
    STRAIGHT_FLUSH: 50,
    FOUR_KIND: 20,
    FULL_HOUSE: 7,
    FLUSH: 5,
    STRAIGHT: 4,
    THREE_KIND: 3,
    TWO_PAIR: 2,
    PAIR_ACES_KINGS: 1
};

const PROGRESSIVE_PAYOUTS = {
    ROYAL_FLUSH: 100000,
    STRAIGHT_FLUSH: 10000,
    FOUR_KIND: 500,
    FULL_HOUSE: 100,
    FLUSH: 50
};

let caribbeanStudGame = {
    deck: [],
    playerCards: [],
    dealerCards: [],
    anteBet: 0,
    playBet: 0,
    progressiveBet: 0,
    gamePhase: 'betting', // betting, dealt, showdown, complete
    dealerQualifies: false,
    playerHandRank: null,
    dealerHandRank: null,
    totalWin: 0,
    progressiveJackpot: 125000,
    islandLevel: 1,
    kingdomPoints: 0,
    sessionStats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        royalFlushes: 0,
        progressiveWins: 0
    }
};

function loadCaribbeanStudGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-6xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-3xl font-bold text-purple-400 mb-2">Caribbean Stud: Island Kings</h3>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div>Island Level: <span id="islandLevel" class="text-yellow-400">1</span></div>
                    <div>Kingdom Points: <span id="kingdomPoints" class="text-blue-400">0</span></div>
                    <div>Progressive: <span id="progressiveJackpot" class="text-green-400">$125,000</span></div>
                </div>
            </div>

            <!-- Game Table -->
            <div class="bg-gradient-to-br from-green-900/40 to-blue-900/40 rounded-2xl p-8 border border-purple-500/30">
                <!-- Dealer Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-purple-300 mb-4">🏴‍☠️ Dealer's Hand</h4>
                    <div id="dealerCards" class="flex justify-center space-x-2 mb-4 min-h-[120px]">
                        <!-- Dealer cards will appear here -->
                    </div>
                    <div id="dealerHandInfo" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- Game Status -->
                <div class="text-center mb-6">
                    <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Place your ante bet to begin</div>
                    <div id="gamePhase" class="text-lg text-blue-300">Betting Phase</div>
                </div>

                <!-- Player Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-purple-300 mb-4">🏝️ Your Hand</h4>
                    <div id="playerCards" class="flex justify-center space-x-2 mb-4 min-h-[120px]">
                        <!-- Player cards will appear here -->
                    </div>
                    <div id="playerHandInfo" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- Betting Area -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Ante Bet -->
                    <div class="bg-black/30 rounded-lg p-4 border border-yellow-500/30">
                        <h5 class="text-lg font-semibold text-yellow-400 mb-3">Ante Bet</h5>
                        <div class="space-y-3">
                            <input type="range" id="anteBetSlider" min="10" max="500" value="50" 
                                   class="w-full accent-yellow-500">
                            <div class="text-center">
                                <span id="anteBetAmount" class="text-xl font-bold text-yellow-400">50 GA</span>
                            </div>
                            <button id="dealCards" class="w-full cyber-button py-2 rounded-lg font-semibold">
                                DEAL CARDS
                            </button>
                        </div>
                    </div>

                    <!-- Play Bet -->
                    <div class="bg-black/30 rounded-lg p-4 border border-blue-500/30">
                        <h5 class="text-lg font-semibold text-blue-400 mb-3">Play Bet</h5>
                        <div class="space-y-3">
                            <div class="text-center text-gray-400">
                                <div>2x Ante Bet</div>
                                <div id="playBetAmount" class="text-xl font-bold text-blue-400">0 GA</div>
                            </div>
                            <button id="playButton" class="w-full cyber-button py-2 rounded-lg font-semibold" disabled>
                                PLAY
                            </button>
                            <button id="foldButton" class="w-full bg-red-600 hover:bg-red-700 py-2 rounded-lg font-semibold" disabled>
                                FOLD
                            </button>
                        </div>
                    </div>

                    <!-- Progressive Bet -->
                    <div class="bg-black/30 rounded-lg p-4 border border-green-500/30">
                        <h5 class="text-lg font-semibold text-green-400 mb-3">Progressive Jackpot</h5>
                        <div class="space-y-3">
                            <div class="text-center">
                                <div class="text-sm text-gray-400">Optional $5 side bet</div>
                                <div class="text-lg font-bold text-green-400">$125,000</div>
                            </div>
                            <label class="flex items-center justify-center space-x-2">
                                <input type="checkbox" id="progressiveCheck" class="accent-green-500">
                                <span class="text-green-400">Play Progressive</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button id="newGameButton" class="cyber-button px-8 py-3 rounded-lg font-semibold" 
                            onclick="resetCaribbeanGame()">
                        NEW GAME
                    </button>
                    <button id="rulesButton" class="cyber-button px-8 py-3 rounded-lg font-semibold"
                            onclick="showCaribbeanRules()">
                        RULES
                    </button>
                </div>

                <!-- Game Result -->
                <div id="gameResult" class="text-center text-2xl font-bold min-h-[60px] flex items-center justify-center">
                    <!-- Result will appear here -->
                </div>
            </div>

            <!-- Statistics Panel -->
            <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-black/30 rounded-lg p-4 text-center border border-purple-500/30">
                    <div class="text-2xl font-bold text-purple-400" id="handsPlayed">0</div>
                    <div class="text-sm text-gray-400">Hands Played</div>
                </div>
                <div class="bg-black/30 rounded-lg p-4 text-center border border-green-500/30">
                    <div class="text-2xl font-bold text-green-400" id="handsWon">0</div>
                    <div class="text-sm text-gray-400">Hands Won</div>
                </div>
                <div class="bg-black/30 rounded-lg p-4 text-center border border-yellow-500/30">
                    <div class="text-2xl font-bold text-yellow-400" id="totalWagered">0</div>
                    <div class="text-sm text-gray-400">Total Wagered</div>
                </div>
                <div class="bg-black/30 rounded-lg p-4 text-center border border-blue-500/30">
                    <div class="text-2xl font-bold text-blue-400" id="biggestWin">0</div>
                    <div class="text-sm text-gray-400">Biggest Win</div>
                </div>
            </div>

            <!-- Payout Table -->
            <div class="mt-8 bg-black/30 rounded-lg p-6 border border-purple-500/30">
                <h4 class="text-xl font-bold text-purple-400 mb-4 text-center">Payout Table</h4>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div class="text-center">
                        <div class="font-bold text-yellow-400">Royal Flush</div>
                        <div class="text-gray-300">100:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-blue-400">Straight Flush</div>
                        <div class="text-gray-300">50:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-green-400">Four of a Kind</div>
                        <div class="text-gray-300">20:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-purple-400">Full House</div>
                        <div class="text-gray-300">7:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-red-400">Flush</div>
                        <div class="text-gray-300">5:1</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div id="rulesModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden flex items-center justify-center">
            <div class="bg-gradient-to-br from-purple-900/90 to-blue-900/90 rounded-xl p-8 max-w-2xl mx-4 border border-purple-500/50">
                <h3 class="text-2xl font-bold text-purple-400 mb-4">Caribbean Stud: Island Kings Rules</h3>
                <div class="space-y-4 text-gray-300">
                    <p><strong class="text-yellow-400">Objective:</strong> Beat the dealer's hand with a better poker hand.</p>
                    <p><strong class="text-blue-400">How to Play:</strong></p>
                    <ul class="list-disc list-inside space-y-2 ml-4">
                        <li>Place an ante bet to receive 5 cards</li>
                        <li>Dealer gets 5 cards (4 face down, 1 face up)</li>
                        <li>Decide to PLAY (2x ante bet) or FOLD</li>
                        <li>Dealer reveals cards - needs A-K high or better to qualify</li>
                        <li>If dealer doesn't qualify, ante pays even money, play bet pushes</li>
                        <li>If dealer qualifies, best hand wins both bets</li>
                    </ul>
                    <p><strong class="text-green-400">Progressive Jackpot:</strong> Optional $5 side bet for huge payouts on premium hands!</p>
                    <p><strong class="text-purple-400">Island Kings:</strong> Earn kingdom points and level up your island paradise!</p>
                </div>
                <button onclick="hideCaribbeanRules()" class="mt-6 cyber-button px-6 py-2 rounded-lg font-semibold">
                    CLOSE
                </button>
            </div>
        </div>
    `;
    
    setupCaribbeanStudGame();
}

function setupCaribbeanStudGame() {
    // Event listeners
    document.getElementById('anteBetSlider').addEventListener('input', updateAnteBet);
    document.getElementById('dealCards').addEventListener('click', dealCaribbeanCards);
    document.getElementById('playButton').addEventListener('click', playHand);
    document.getElementById('foldButton').addEventListener('click', foldHand);
    document.getElementById('progressiveCheck').addEventListener('change', updateProgressiveBet);
    
    updateCaribbeanDisplay();
    updateSessionStats();
}

function updateAnteBet() {
    const anteBet = parseInt(document.getElementById('anteBetSlider').value);
    document.getElementById('anteBetAmount').textContent = `${anteBet} GA`;
    document.getElementById('playBetAmount').textContent = `${anteBet * 2} GA`;
}

function updateProgressiveBet() {
    const isChecked = document.getElementById('progressiveCheck').checked;
    caribbeanStudGame.progressiveBet = isChecked ? 5 : 0;
}

function createDeck() {
    const deck = [];
    for (const suit of CARD_SUITS) {
        for (const value of CARD_VALUES) {
            deck.push({ suit, value });
        }
    }
    return shuffleDeck(deck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function createCardElement(card, isHidden = false) {
    const cardElement = document.createElement('div');
    cardElement.className = 'card inline-block w-20 h-28 bg-white rounded-lg border-2 border-gray-300 m-1 flex flex-col items-center justify-center text-lg font-bold shadow-lg transition-all duration-300';
    
    if (isHidden) {
        cardElement.innerHTML = '🂠';
        cardElement.className += ' bg-blue-900 text-white';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.innerHTML = `
            <div class="${isRed ? 'text-red-600' : 'text-black'}">${card.value}</div>
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-2xl">${card.suit}</div>
        `;
    }
    
    return cardElement;
}

function dealCaribbeanCards() {
    const anteBet = parseInt(document.getElementById('anteBetSlider').value);
    
    if (balance < anteBet + caribbeanStudGame.progressiveBet) {
        document.getElementById('gameStatus').textContent = 'Insufficient funds!';
        return;
    }
    
    // Deduct ante and progressive bets
    balance -= anteBet + caribbeanStudGame.progressiveBet;
    updateBalance();
    
    // Initialize game
    caribbeanStudGame.deck = createDeck();
    caribbeanStudGame.playerCards = [];
    caribbeanStudGame.dealerCards = [];
    caribbeanStudGame.anteBet = anteBet;
    caribbeanStudGame.playBet = 0;
    caribbeanStudGame.gamePhase = 'dealt';
    caribbeanStudGame.totalWin = 0;
    
    // Deal cards
    for (let i = 0; i < 5; i++) {
        caribbeanStudGame.playerCards.push(caribbeanStudGame.deck.pop());
        caribbeanStudGame.dealerCards.push(caribbeanStudGame.deck.pop());
    }
    
    displayPlayerCards();
    displayDealerCards(false); // Show only one dealer card
    
    // Enable play/fold buttons
    document.getElementById('playButton').disabled = false;
    document.getElementById('foldButton').disabled = false;
    document.getElementById('dealCards').disabled = true;
    
    // Evaluate player hand
    caribbeanStudGame.playerHandRank = evaluatePokerHand(caribbeanStudGame.playerCards);
    updatePlayerHandInfo();
    
    document.getElementById('gameStatus').textContent = 'Cards dealt! Play or fold?';
    document.getElementById('gamePhase').textContent = 'Decision Phase';
    
    updateCaribbeanDisplay();
}

function displayPlayerCards() {
    const container = document.getElementById('playerCards');
    container.innerHTML = '';
    
    caribbeanStudGame.playerCards.forEach(card => {
        container.appendChild(createCardElement(card));
    });
}

function displayDealerCards(showAll = false) {
    const container = document.getElementById('dealerCards');
    container.innerHTML = '';
    
    caribbeanStudGame.dealerCards.forEach((card, index) => {
        const isHidden = !showAll && index > 0;
        container.appendChild(createCardElement(card, isHidden));
    });
}

function playHand() {
    caribbeanStudGame.playBet = caribbeanStudGame.anteBet * 2;
    balance -= caribbeanStudGame.playBet;
    updateBalance();
    
    caribbeanStudGame.gamePhase = 'showdown';
    
    // Show all dealer cards
    displayDealerCards(true);
    
    // Evaluate dealer hand
    caribbeanStudGame.dealerHandRank = evaluatePokerHand(caribbeanStudGame.dealerCards);
    updateDealerHandInfo();
    
    // Check if dealer qualifies (A-K high or better)
    caribbeanStudGame.dealerQualifies = checkDealerQualification();
    
    // Determine winner
    setTimeout(() => {
        determineWinner();
    }, 1500);
    
    // Disable buttons
    document.getElementById('playButton').disabled = true;
    document.getElementById('foldButton').disabled = true;
    
    document.getElementById('gameStatus').textContent = 'Revealing dealer cards...';
    updateCaribbeanDisplay();
}

function foldHand() {
    caribbeanStudGame.gamePhase = 'complete';
    
    document.getElementById('gameResult').innerHTML = 
        '<span class="text-red-400">You folded. Ante bet lost.</span>';
    
    // Update stats
    caribbeanStudGame.sessionStats.handsPlayed++;
    caribbeanStudGame.sessionStats.totalWagered += caribbeanStudGame.anteBet + caribbeanStudGame.progressiveBet;
    
    updateSessionStats();
    resetForNextHand();
}

function checkDealerQualification() {
    const hand = caribbeanStudGame.dealerCards;
    const handRank = caribbeanStudGame.dealerHandRank;
    
    // If dealer has a pair or better, they qualify
    if (handRank && handRank.rank > 0) return true;
    
    // Check for A-K high
    const values = hand.map(card => getCardValue(card)).sort((a, b) => b - a);
    return values[0] === 14 && values[1] === 13; // Ace and King
}

function determineWinner() {
    let totalWinnings = 0;
    let resultText = '';
    
    // Check progressive jackpot first
    if (caribbeanStudGame.progressiveBet > 0) {
        const progressiveWin = checkProgressiveWin();
        if (progressiveWin > 0) {
            totalWinnings += progressiveWin;
            resultText += `🎰 Progressive Win: $${progressiveWin.toLocaleString()}! `;
            caribbeanStudGame.sessionStats.progressiveWins++;
        }
    }
    
    if (!caribbeanStudGame.dealerQualifies) {
        // Dealer doesn't qualify - ante pays even money, play bet pushes
        totalWinnings += caribbeanStudGame.anteBet * 2; // Return ante + winnings
        totalWinnings += caribbeanStudGame.playBet; // Return play bet
        resultText += '🏴‍☠️ Dealer doesn\'t qualify! Ante pays even money.';
        caribbeanStudGame.sessionStats.handsWon++;
    } else {
        // Compare hands
        const playerRank = caribbeanStudGame.playerHandRank ? caribbeanStudGame.playerHandRank.rank : -1;
        const dealerRank = caribbeanStudGame.dealerHandRank ? caribbeanStudGame.dealerHandRank.rank : -1;
        
        if (playerRank > dealerRank) {
            // Player wins
            const antePayout = caribbeanStudGame.anteBet * 2;
            const playPayout = getPlayBetPayout();
            totalWinnings += antePayout + playPayout;
            resultText += `🏆 You win! ${caribbeanStudGame.playerHandRank.name}`;
            caribbeanStudGame.sessionStats.handsWon++;
        } else if (playerRank === dealerRank) {
            // Compare high cards for ties
            const playerWins = compareHighCards(caribbeanStudGame.playerCards, caribbeanStudGame.dealerCards);
            if (playerWins) {
                const antePayout = caribbeanStudGame.anteBet * 2;
                const playPayout = getPlayBetPayout();
                totalWinnings += antePayout + playPayout;
                resultText += '🏆 You win on high card!';
                caribbeanStudGame.sessionStats.handsWon++;
            } else {
                resultText += '💔 Dealer wins on high card.';
            }
        } else {
            // Dealer wins
            resultText += `💔 Dealer wins with ${caribbeanStudGame.dealerHandRank.name}`;
        }
    }
    
    // Add winnings to balance
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    caribbeanStudGame.sessionStats.handsPlayed++;
    caribbeanStudGame.sessionStats.totalWagered += caribbeanStudGame.anteBet + caribbeanStudGame.playBet + caribbeanStudGame.progressiveBet;
    caribbeanStudGame.sessionStats.totalWon += totalWinnings;
    caribbeanStudGame.sessionStats.biggestWin = Math.max(caribbeanStudGame.sessionStats.biggestWin, totalWinnings);
    
    // Award kingdom points
    const pointsEarned = Math.floor(totalWinnings / 10);
    caribbeanStudGame.kingdomPoints += pointsEarned;
    
    // Check for level up
    checkLevelUp();
    
    document.getElementById('gameResult').innerHTML = `<span class="${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'}">${resultText}</span>`;
    
    updateSessionStats();
    updateCaribbeanDisplay();
    
    setTimeout(() => {
        resetForNextHand();
    }, 4000);
}

function getPlayBetPayout() {
    if (!caribbeanStudGame.playerHandRank) return caribbeanStudGame.playBet * 2;
    
    const multiplier = CARIBBEAN_PAYOUTS[caribbeanStudGame.playerHandRank.type] || 1;
    return caribbeanStudGame.playBet * (1 + multiplier);
}

function checkProgressiveWin() {
    if (!caribbeanStudGame.playerHandRank) return 0;
    
    const progressivePayout = PROGRESSIVE_PAYOUTS[caribbeanStudGame.playerHandRank.type];
    if (progressivePayout) {
        // Reduce jackpot
        caribbeanStudGame.progressiveJackpot = Math.max(50000, caribbeanStudGame.progressiveJackpot - progressivePayout);
        return progressivePayout;
    }
    
    return 0;
}

function compareHighCards(playerCards, dealerCards) {
    const playerValues = playerCards.map(card => getCardValue(card)).sort((a, b) => b - a);
    const dealerValues = dealerCards.map(card => getCardValue(card)).sort((a, b) => b - a);
    
    for (let i = 0; i < 5; i++) {
        if (playerValues[i] > dealerValues[i]) return true;
        if (playerValues[i] < dealerValues[i]) return false;
    }
    
    return false; // Exact tie - dealer wins
}

function checkLevelUp() {
    const newLevel = Math.floor(caribbeanStudGame.kingdomPoints / 1000) + 1;
    if (newLevel > caribbeanStudGame.islandLevel) {
        caribbeanStudGame.islandLevel = newLevel;
        document.getElementById('gameStatus').textContent = `🏝️ Island Level Up! Welcome to Level ${newLevel}!`;
    }
}

function evaluatePokerHand(cards) {
    const values = cards.map(card => getCardValue(card));
    const suits = cards.map(card => card.suit);
    
    // Count values and suits
    const valueCounts = {};
    const suitCounts = {};
    
    values.forEach(value => valueCounts[value] = (valueCounts[value] || 0) + 1);
    suits.forEach(suit => suitCounts[suit] = (suitCounts[suit] || 0) + 1);
    
    const sortedValues = values.sort((a, b) => b - a);
    const isFlush = Object.values(suitCounts).some(count => count === 5);
    const isStraight = checkStraight(sortedValues);
    
    // Check for hands in order of rank
    if (isFlush && isStraight && sortedValues[0] === 14) {
        return { rank: 9, name: 'Royal Flush', type: 'ROYAL_FLUSH' };
    }
    if (isFlush && isStraight) {
        return { rank: 8, name: 'Straight Flush', type: 'STRAIGHT_FLUSH' };
    }
    if (Object.values(valueCounts).includes(4)) {
        return { rank: 7, name: 'Four of a Kind', type: 'FOUR_KIND' };
    }
    if (Object.values(valueCounts).includes(3) && Object.values(valueCounts).includes(2)) {
        return { rank: 6, name: 'Full House', type: 'FULL_HOUSE' };
    }
    if (isFlush) {
        return { rank: 5, name: 'Flush', type: 'FLUSH' };
    }
    if (isStraight) {
        return { rank: 4, name: 'Straight', type: 'STRAIGHT' };
    }
    if (Object.values(valueCounts).includes(3)) {
        return { rank: 3, name: 'Three of a Kind', type: 'THREE_KIND' };
    }
    
    const pairs = Object.keys(valueCounts).filter(value => valueCounts[value] === 2);
    if (pairs.length === 2) {
        return { rank: 2, name: 'Two Pair', type: 'TWO_PAIR' };
    }
    if (pairs.length === 1) {
        const pairValue = parseInt(pairs[0]);
        if (pairValue >= 14 || pairValue >= 13) { // Aces or Kings
            return { rank: 1, name: 'Pair of ' + (pairValue === 14 ? 'Aces' : 'Kings'), type: 'PAIR_ACES_KINGS' };
        }
        return { rank: 0, name: 'Pair of ' + getCardName(pairValue), type: 'PAIR' };
    }
    
    return null; // High card
}

function checkStraight(sortedValues) {
    // Check for regular straight
    for (let i = 0; i < 4; i++) {
        if (sortedValues[i] - sortedValues[i + 1] !== 1) {
            // Check for A-2-3-4-5 straight (wheel)
            if (i === 0 && sortedValues[0] === 14 && sortedValues[1] === 5 && 
                sortedValues[2] === 4 && sortedValues[3] === 3 && sortedValues[4] === 2) {
                return true;
            }
            return false;
        }
    }
    return true;
}

function getCardValue(card) {
    if (card.value === 'A') return 14;
    if (card.value === 'K') return 13;
    if (card.value === 'Q') return 12;
    if (card.value === 'J') return 11;
    return parseInt(card.value);
}

function getCardName(value) {
    if (value === 14) return 'Aces';
    if (value === 13) return 'Kings';
    if (value === 12) return 'Queens';
    if (value === 11) return 'Jacks';
    return value + 's';
}

function updatePlayerHandInfo() {
    const handInfo = document.getElementById('playerHandInfo');
    if (caribbeanStudGame.playerHandRank) {
        handInfo.textContent = caribbeanStudGame.playerHandRank.name;
        handInfo.className = 'text-lg font-semibold text-yellow-400';
    } else {
        handInfo.textContent = 'High Card';
        handInfo.className = 'text-lg font-semibold text-gray-400';
    }
}

function updateDealerHandInfo() {
    const handInfo = document.getElementById('dealerHandInfo');
    if (caribbeanStudGame.dealerHandRank) {
        handInfo.textContent = caribbeanStudGame.dealerHandRank.name;
        handInfo.className = 'text-lg font-semibold text-red-400';
    } else {
        handInfo.textContent = 'High Card';
        handInfo.className = 'text-lg font-semibold text-gray-400';
    }
}

function updateCaribbeanDisplay() {
    document.getElementById('islandLevel').textContent = caribbeanStudGame.islandLevel;
    document.getElementById('kingdomPoints').textContent = caribbeanStudGame.kingdomPoints.toLocaleString();
    document.getElementById('progressiveJackpot').textContent = '$' + caribbeanStudGame.progressiveJackpot.toLocaleString();
}

function updateSessionStats() {
    document.getElementById('handsPlayed').textContent = caribbeanStudGame.sessionStats.handsPlayed;
    document.getElementById('handsWon').textContent = caribbeanStudGame.sessionStats.handsWon;
    document.getElementById('totalWagered').textContent = caribbeanStudGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('biggestWin').textContent = caribbeanStudGame.sessionStats.biggestWin.toLocaleString();
}

function resetForNextHand() {
    caribbeanStudGame.gamePhase = 'betting';
    caribbeanStudGame.playerCards = [];
    caribbeanStudGame.dealerCards = [];
    caribbeanStudGame.anteBet = 0;
    caribbeanStudGame.playBet = 0;
    caribbeanStudGame.playerHandRank = null;
    caribbeanStudGame.dealerHandRank = null;
    
    // Reset UI
    document.getElementById('dealCards').disabled = false;
    document.getElementById('playButton').disabled = true;
    document.getElementById('foldButton').disabled = true;
    document.getElementById('progressiveCheck').checked = false;
    
    document.getElementById('gameStatus').textContent = 'Place your ante bet to begin';
    document.getElementById('gamePhase').textContent = 'Betting Phase';
    document.getElementById('gameResult').innerHTML = '';
    document.getElementById('playerHandInfo').textContent = '';
    document.getElementById('dealerHandInfo').textContent = '';
    
    // Clear card displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    
    updateProgressiveBet();
}

function resetCaribbeanGame() {
    resetForNextHand();
    document.getElementById('gameResult').innerHTML = '';
}

function showCaribbeanRules() {
    document.getElementById('rulesModal').classList.remove('hidden');
}

function hideCaribbeanRules() {
    document.getElementById('rulesModal').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCaribbeanStudGame();
});