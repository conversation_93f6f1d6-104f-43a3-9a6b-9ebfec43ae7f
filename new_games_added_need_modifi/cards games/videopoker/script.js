// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('videopoker');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        balance = walletIntegration.balance;
        walletIntegration.updateBalanceDisplay();

        // Update bet input limits
        const betInput = document.getElementById('videoPokerBet');
        if (betInput) {
            betInput.setAttribute('max', balance);
            // Ensure current bet doesn't exceed balance
            if (parseFloat(betInput.value) > balance) {
                betInput.value = Math.min(5, balance);
            }
        }
    }
}

        function loadVideoPokerGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-teal-500/30">
                            <h4 class="text-xl font-bold mb-4 text-teal-400">CYBER VIDEO POKER</h4>

                            <!-- Wallet Balance Display -->
                            <div class="mb-4 p-3 bg-gradient-to-r from-teal-900/20 to-cyan-900/20 rounded-lg border border-teal-500/30">
                                <div id="balanceDisplay" class="text-center">
                                    <span class="text-teal-400 neon-glow">Loading balance...</span>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT (GA)</label>
                                <input type="number" id="videoPokerBet" value="5" min="1" max="${balance || 1000}"
                                       class="w-full bg-black/50 border border-teal-500/50 rounded-lg px-3 py-2 text-white"
                                       placeholder="Enter bet amount">
                                <div class="text-xs text-gray-400 mt-1">Min: 1 GA | Max: ${balance || 1000} GA</div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET LEVEL</label>
                                <select id="videoPokerLevel" class="w-full bg-black/50 border border-teal-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="1">1 Credit</option>
                                    <option value="2">2 Credits</option>
                                    <option value="3">3 Credits</option>
                                    <option value="4">4 Credits</option>
                                    <option value="5">5 Credits (Max)</option>
                                </select>
                            </div>
                            
                            <button id="dealVideoPoker" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-2">
                                DEAL CARDS
                            </button>
                            
                            <button id="drawVideoPoker" class="w-full py-3 rounded-lg font-bold bg-teal-600 hover:bg-teal-700 text-white mb-4" disabled>
                                DRAW CARDS
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                                <div id="videoPokerTotalBet" class="text-xl font-bold text-teal-400">5 GA</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Current Hand</div>
                                <div id="videoPokerHand" class="text-lg font-bold text-green-400">-</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Payout</div>
                                <div id="videoPokerPayout" class="text-xl font-bold text-yellow-400">0 GA</div>
                            </div>
                        </div>
                        
                        <!-- Paytable -->
                        <div class="bg-black/30 p-4 rounded-xl border border-teal-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-teal-400">PAYTABLE (MAX BET)</h5>
                            <div class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Royal Flush:</span>
                                    <span class="text-teal-400">4000</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Straight Flush:</span>
                                    <span class="text-teal-400">250</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Four of a Kind:</span>
                                    <span class="text-teal-400">125</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Full House:</span>
                                    <span class="text-teal-400">45</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Flush:</span>
                                    <span class="text-teal-400">30</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Straight:</span>
                                    <span class="text-teal-400">20</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Three of a Kind:</span>
                                    <span class="text-teal-400">15</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Two Pair:</span>
                                    <span class="text-teal-400">10</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Jacks or Better:</span>
                                    <span class="text-teal-400">5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Video Poker Machine -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-teal-500/30">
                            <div id="videoPokerMachine" class="relative bg-gradient-to-br from-teal-900 to-cyan-900 rounded-lg p-6 h-96">
                                <!-- Machine Display -->
                                <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-teal-400 mb-2">CYBER VIDEO POKER</div>
                                        <div class="text-xs text-gray-400">Jacks or Better</div>
                                    </div>
                                </div>
                                
                                <!-- Card Display -->
                                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                    <div class="text-center">
                                        <div id="videoPokerCards" class="flex space-x-3 mb-4">
                                            <!-- Cards will appear here -->
                                        </div>
                                        <div id="videoPokerHolds" class="flex space-x-3 hidden">
                                            <!-- Hold indicators will appear here -->
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Game State Display -->
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center">
                                    <div id="videoPokerPhase" class="text-xl font-bold text-teal-400 mb-2">Insert Credits</div>
                                    <div class="text-sm text-gray-400">
                                        Credits: <span id="videoPokerCredits" class="text-teal-400">0</span>
                                    </div>
                                </div>
                            </div>
                            <div id="videoPokerStatus" class="text-center mt-4 text-lg font-semibold">Place your bet and deal the cards</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeVideoPoker();
        }
        
        let videoPokerGame = {
            isPlaying: false,
            betAmount: 5,
            betLevel: 1,
            deck: [],
            playerCards: [],
            heldCards: [false, false, false, false, false],
            phase: 'deal' // deal, hold, draw, result
        };
        
        function initializeVideoPoker() {
            document.getElementById('dealVideoPoker').addEventListener('click', dealVideoPoker);
            document.getElementById('drawVideoPoker').addEventListener('click', drawVideoPoker);
            document.getElementById('videoPokerBet').addEventListener('input', updateVideoPokerBet);
            document.getElementById('videoPokerLevel').addEventListener('change', updateVideoPokerBet);

            // Add input validation for bet amount
            const betInput = document.getElementById('videoPokerBet');
            if (betInput) {
                betInput.addEventListener('input', function() {
                    const value = parseFloat(this.value);
                    const max = parseFloat(this.getAttribute('max'));

                    // Validate bet amount
                    if (value > max) {
                        this.value = max;
                        if (walletIntegration) {
                            walletIntegration.showNotification(`Maximum bet is ${max} GA`, 'error');
                        }
                    }

                    if (value < 1) {
                        this.value = 1;
                    }
                });

                // Update max value with current balance
                if (walletIntegration) {
                    betInput.setAttribute('max', walletIntegration.balance || 1000);
                }
            }
            
            initializeVideoPokerDeck();
            updateVideoPokerBet();
        }
        
        function initializeVideoPokerDeck() {
            const suits = ['♠', '♥', '♦', '♣'];
            const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
            
            videoPokerGame.deck = [];
            for (const suit of suits) {
                for (const rank of ranks) {
                    videoPokerGame.deck.push({ 
                        rank, 
                        suit, 
                        value: getVideoPokerCardValue(rank) 
                    });
                }
            }
            
            shuffleVideoPokerDeck();
        }
        
        function shuffleVideoPokerDeck() {
            for (let i = videoPokerGame.deck.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [videoPokerGame.deck[i], videoPokerGame.deck[j]] = 
                [videoPokerGame.deck[j], videoPokerGame.deck[i]];
            }
        }
        
        function getVideoPokerCardValue(rank) {
            if (rank === 'A') return 14;
            if (rank === 'K') return 13;
            if (rank === 'Q') return 12;
            if (rank === 'J') return 11;
            return parseInt(rank);
        }
        
        function updateVideoPokerBet() {
            const betAmount = parseInt(document.getElementById('videoPokerBet').value);
            const betLevel = parseInt(document.getElementById('videoPokerLevel').value);
            const totalBet = betAmount * betLevel;
            
            videoPokerGame.betAmount = betAmount;
            videoPokerGame.betLevel = betLevel;
            
            document.getElementById('videoPokerTotalBet').textContent = totalBet + ' GA';
            document.getElementById('videoPokerCredits').textContent = betLevel;
        }
        
        async function dealVideoPoker() {
            const totalBet = videoPokerGame.betAmount * videoPokerGame.betLevel;

            // Use wallet integration to process bet
            if (!walletIntegration || !(await walletIntegration.processBet(totalBet, {
                game_action: 'deal',
                bet_amount: videoPokerGame.betAmount,
                bet_level: videoPokerGame.betLevel,
                total_bet: totalBet
            }))) {
                return;
            }

            // Update local balance reference
            balance = walletIntegration.balance;
            
            videoPokerGame.isPlaying = true;
            videoPokerGame.phase = 'hold';
            videoPokerGame.playerCards = [];
            videoPokerGame.heldCards = [false, false, false, false, false];
            
            // Clear display
            document.getElementById('videoPokerCards').innerHTML = '';
            document.getElementById('videoPokerHolds').innerHTML = '';
            document.getElementById('videoPokerHolds').classList.add('hidden');
            
            // Deal 5 cards
            for (let i = 0; i < 5; i++) {
                const card = videoPokerGame.deck.pop();
                videoPokerGame.playerCards.push(card);
                
                setTimeout(() => {
                    displayVideoPokerCard(card, i);
                    if (i === 4) {
                        setTimeout(() => {
                            showVideoPokerHolds();
                            evaluateVideoPokerHand();
                        }, 500);
                    }
                }, i * 200);
            }
            
            // Update UI
            document.getElementById('dealVideoPoker').disabled = true;
            document.getElementById('videoPokerPhase').textContent = 'Select Cards to Hold';
            document.getElementById('videoPokerStatus').textContent = 'Click cards to hold, then draw';
        }
        
        function displayVideoPokerCard(card, index) {
            const cardElement = document.createElement('div');
            cardElement.className = 'w-16 h-20 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-sm font-bold cursor-pointer transition-all hover:scale-105';
            cardElement.dataset.index = index;
            
            const isRed = card.suit === '♥' || card.suit === '♦';
            cardElement.style.color = isRed ? '#dc2626' : '#000';
            
            cardElement.innerHTML = `
                <div>${card.rank}</div>
                <div class="text-lg">${card.suit}</div>
            `;
            
            // Add click listener for card selection
            cardElement.addEventListener('click', () => toggleVideoPokerHold(index));
            
            document.getElementById('videoPokerCards').appendChild(cardElement);
        }
        
        function showVideoPokerHolds() {
            const holdsDiv = document.getElementById('videoPokerHolds');
            holdsDiv.innerHTML = '';
            
            for (let i = 0; i < 5; i++) {
                const holdDiv = document.createElement('div');
                holdDiv.className = 'w-16 text-center';
                holdDiv.innerHTML = `
                    <div id="hold${i}" class="text-xs font-bold text-red-400 opacity-0 transition-opacity">HOLD</div>
                `;
                holdsDiv.appendChild(holdDiv);
            }
            
            holdsDiv.classList.remove('hidden');
            document.getElementById('drawVideoPoker').disabled = false;
        }
        
        function toggleVideoPokerHold(index) {
            if (videoPokerGame.phase !== 'hold') return;
            
            videoPokerGame.heldCards[index] = !videoPokerGame.heldCards[index];
            
            const cardElement = document.querySelector(`[data-index="${index}"]`);
            const holdElement = document.getElementById(`hold${index}`);
            
            if (videoPokerGame.heldCards[index]) {
                cardElement.classList.add('ring-4', 'ring-teal-400', 'bg-teal-100');
                holdElement.style.opacity = '1';
            } else {
                cardElement.classList.remove('ring-4', 'ring-teal-400', 'bg-teal-100');
                holdElement.style.opacity = '0';
            }
        }
        
        function drawVideoPoker() {
            videoPokerGame.phase = 'draw';
            document.getElementById('drawVideoPoker').disabled = true;
            document.getElementById('videoPokerPhase').textContent = 'Drawing Cards...';
            document.getElementById('videoPokerStatus').textContent = 'Replacing non-held cards...';
            
            // Replace non-held cards
            let drawDelay = 0;
            for (let i = 0; i < 5; i++) {
                if (!videoPokerGame.heldCards[i]) {
                    setTimeout(() => {
                        const newCard = videoPokerGame.deck.pop();
                        videoPokerGame.playerCards[i] = newCard;
                        
                        // Update display
                        const cardElement = document.querySelector(`[data-index="${i}"]`);
                        const isRed = newCard.suit === '♥' || newCard.suit === '♦';
                        cardElement.style.color = isRed ? '#dc2626' : '#000';
                        cardElement.innerHTML = `
                            <div>${newCard.rank}</div>
                            <div class="text-lg">${newCard.suit}</div>
                        `;
                        cardElement.classList.remove('ring-4', 'ring-teal-400', 'bg-teal-100');
                    }, drawDelay);
                    drawDelay += 300;
                }
            }
            
            setTimeout(async () => {
                await evaluateVideoPokerFinalHand();
                endVideoPokerGame();
            }, drawDelay + 500);
        }
        
        function evaluateVideoPokerHand() {
            const hand = analyzeVideoPokerHand(videoPokerGame.playerCards);
            document.getElementById('videoPokerHand').textContent = hand.name;
            return hand;
        }
        
        async function evaluateVideoPokerFinalHand() {
            const hand = analyzeVideoPokerHand(videoPokerGame.playerCards);
            document.getElementById('videoPokerHand').textContent = hand.name;
            
            // Calculate payout
            const payout = calculateVideoPokerPayout(hand.rank);
            document.getElementById('videoPokerPayout').textContent = payout + ' GA';
            
            if (payout > 0 && walletIntegration) {
                await walletIntegration.processWin(payout, {
                    game_action: 'win',
                    hand_type: hand.name,
                    hand_rank: hand.rank,
                    payout: payout,
                    cards: videoPokerGame.playerCards.map(card => `${card.rank}${card.suit}`)
                });
                
                // Update local balance reference
                balance = walletIntegration.balance;
            }
            
            return { hand, payout };
        }
        
        function analyzeVideoPokerHand(cards) {
            // Sort cards by value
            const sortedCards = [...cards].sort((a, b) => b.value - a.value);
            
            // Check for flush
            const suits = {};
            cards.forEach(card => {
                suits[card.suit] = (suits[card.suit] || 0) + 1;
            });
            const isFlush = Object.values(suits).some(count => count === 5);
            
            // Check for straight
            const values = sortedCards.map(card => card.value);
            let isStraight = true;
            for (let i = 0; i < 4; i++) {
                if (values[i] - values[i + 1] !== 1) {
                    isStraight = false;
                    break;
                }
            }
            
            // Special case: A-2-3-4-5 straight
            if (!isStraight && values[0] === 14 && values[1] === 5 && values[2] === 4 && values[3] === 3 && values[4] === 2) {
                isStraight = true;
            }
            
            // Count values
            const valueCounts = {};
            cards.forEach(card => {
                valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
            });
            
            const counts = Object.values(valueCounts).sort((a, b) => b - a);
            
            // Determine hand rank
            if (isStraight && isFlush) {
                if (values[0] === 14 && values[1] === 13) {
                    return { rank: 9, name: 'Royal Flush' };
                }
                return { rank: 8, name: 'Straight Flush' };
            } else if (counts[0] === 4) {
                return { rank: 7, name: 'Four of a Kind' };
            } else if (counts[0] === 3 && counts[1] === 2) {
                return { rank: 6, name: 'Full House' };
            } else if (isFlush) {
                return { rank: 5, name: 'Flush' };
            } else if (isStraight) {
                return { rank: 4, name: 'Straight' };
            } else if (counts[0] === 3) {
                return { rank: 3, name: 'Three of a Kind' };
            } else if (counts[0] === 2 && counts[1] === 2) {
                return { rank: 2, name: 'Two Pair' };
            } else if (counts[0] === 2) {
                // Check if it's Jacks or Better
                const pairValue = Object.keys(valueCounts).find(key => valueCounts[key] === 2);
                if (parseInt(pairValue) >= 11) {
                    return { rank: 1, name: 'Jacks or Better' };
                }
                return { rank: 0, name: 'Low Pair' };
            } else {
                return { rank: 0, name: 'High Card' };
            }
        }
        
        function calculateVideoPokerPayout(handRank) {
            const payouts = [0, 1, 2, 3, 4, 6, 9, 25, 50, 250]; // Base payouts
            
            // Royal Flush bonus for max bet
            if (handRank === 9 && videoPokerGame.betLevel === 5) {
                return videoPokerGame.betAmount * 800; // Special Royal Flush payout
            }
            
            const basePayout = payouts[handRank] || 0;
            return videoPokerGame.betAmount * videoPokerGame.betLevel * basePayout;
        }
        
        function endVideoPokerGame() {
            // Result is already evaluated in the calling function
            const hand = analyzeVideoPokerHand(videoPokerGame.playerCards);
            const payout = calculateVideoPokerPayout(hand.rank);
            const result = { hand, payout };
            
            videoPokerGame.isPlaying = false;
            videoPokerGame.phase = 'result';
            
            if (result.payout > 0) {
                document.getElementById('videoPokerPhase').textContent = 'Winner!';
                document.getElementById('videoPokerStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">${result.hand.name}! Won $${result.payout}</span>`;
            } else {
                document.getElementById('videoPokerPhase').textContent = 'Game Over';
                document.getElementById('videoPokerStatus').innerHTML = 
                    `<span class="text-red-400">${result.hand.name} - No payout</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                resetVideoPoker();
            }, 3000);
        }
        
        function resetVideoPoker() {
            videoPokerGame.isPlaying = false;
            videoPokerGame.phase = 'deal';
            videoPokerGame.heldCards = [false, false, false, false, false];
            
            // Reset UI
            document.getElementById('dealVideoPoker').disabled = false;
            document.getElementById('drawVideoPoker').disabled = true;
            document.getElementById('videoPokerCards').innerHTML = '';
            document.getElementById('videoPokerHolds').classList.add('hidden');
            document.getElementById('videoPokerHand').textContent = '-';
            document.getElementById('videoPokerPayout').textContent = '0 GA';
            document.getElementById('videoPokerPhase').textContent = 'Insert Credits';
            document.getElementById('videoPokerStatus').textContent = 'Place your bet and deal the cards';
            
            // Reshuffle if deck is low
            if (videoPokerGame.deck.length < 10) {
                initializeVideoPokerDeck();
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Initialize wallet integration first
        await initializeWallet();
        updateBalance();
        loadVideoPokerGame();
    } catch (error) {
        console.error('Failed to initialize wallet:', error);
        // Fallback to demo mode or show error
        document.getElementById('balanceDisplay').innerHTML = 
            '<span class="text-red-400">Wallet connection failed</span>';
    }
});