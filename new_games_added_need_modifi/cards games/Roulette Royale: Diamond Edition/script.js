// Roulette Royale: Diamond Edition - Ultra-luxury roulette with <8% win rate
let rouletteRoyaleGame = {
    currentNumber: null,
    lastNumbers: [],
    bets: [],
    totalBet: 0,
    isSpinning: false,
    spinSpeed: 0,
    ballPosition: 0,
    wheelPosition: 0,
    royaleLevel: 1, // 1-15 (higher = more house advantage)
    diamondMultiplier: 1.0,
    luxuryMode: 'classic', // classic, premium, platinum, diamond
    tableLimit: 'standard', // standard, high, vip, royale
    magneticField: false,
    diamondDeflection: false,
    royaleBias: false,
    platinumPenalty: false,
    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        straightHits: 0,
        royaleBonus: 0,
        diamondDeflections: 0,
        magneticInterference: 0,
        platinumPenalties: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    wheelHistory: [],
    biasedNumbers: new Set()
};

// Luxury modes with extreme house bias
const ROYALE_MODES = {
    classic: {
        name: 'Classic Royale',
        houseEdge: 0.38, // 38% house edge
        magneticChance: 0.22,
        deflectionChance: 0.18,
        payoutMultiplier: 0.68, // Severely reduced payouts
        biasStrength: 0.25
    },
    premium: {
        name: 'Premium Diamond',
        houseEdge: 0.43, // 43% house edge
        magneticChance: 0.28,
        deflectionChance: 0.24,
        payoutMultiplier: 0.62,
        biasStrength: 0.32
    },
    platinum: {
        name: 'Platinum Elite',
        houseEdge: 0.48, // 48% house edge
        magneticChance: 0.34,
        deflectionChance: 0.30,
        payoutMultiplier: 0.56,
        biasStrength: 0.39
    },
    diamond: {
        name: 'Diamond Royale',
        houseEdge: 0.53, // 53% house edge
        magneticChance: 0.40,
        deflectionChance: 0.36,
        payoutMultiplier: 0.50,
        biasStrength: 0.46
    }
};

const TABLE_LIMITS = {
    standard: {
        name: 'Standard Table',
        minBet: 5,
        maxBet: 500,
        biasMultiplier: 1.0,
        magneticBonus: 0.0
    },
    high: {
        name: 'High Roller',
        minBet: 25,
        maxBet: 2500,
        biasMultiplier: 1.3,
        magneticBonus: 0.08
    },
    vip: {
        name: 'VIP Lounge',
        minBet: 100,
        maxBet: 10000,
        biasMultiplier: 1.6,
        magneticBonus: 0.15
    },
    royale: {
        name: 'Royale Suite',
        minBet: 500,
        maxBet: 50000,
        biasMultiplier: 2.0,
        magneticBonus: 0.22
    }
};

// Roulette numbers and colors
const ROULETTE_NUMBERS = {
    0: 'green', 37: 'green', // European + American zeros
    1: 'red', 3: 'red', 5: 'red', 7: 'red', 9: 'red', 12: 'red', 14: 'red', 16: 'red',
    18: 'red', 19: 'red', 21: 'red', 23: 'red', 25: 'red', 27: 'red', 30: 'red',
    32: 'red', 34: 'red', 36: 'red',
    2: 'black', 4: 'black', 6: 'black', 8: 'black', 10: 'black', 11: 'black',
    13: 'black', 15: 'black', 17: 'black', 20: 'black', 22: 'black', 24: 'black',
    26: 'black', 28: 'black', 29: 'black', 31: 'black', 33: 'black', 35: 'black'
};

// Severely reduced payouts
const ROYALE_PAYOUTS = {
    straight: 28.5,    // Reduced from 35:1
    split: 14.2,       // Reduced from 17:1
    street: 9.1,       // Reduced from 11:1
    corner: 6.8,       // Reduced from 8:1
    line: 4.2,         // Reduced from 5:1
    dozen: 2.1,        // Reduced from 2:1
    column: 2.1,       // Reduced from 2:1
    red: 0.85,         // Reduced from 1:1
    black: 0.85,       // Reduced from 1:1
    odd: 0.85,         // Reduced from 1:1
    even: 0.85,        // Reduced from 1:1
    low: 0.85,         // Reduced from 1:1 (1-18)
    high: 0.85         // Reduced from 1:1 (19-36)
};

// Ultra-biased number generation
function generateBiasedRouletteNumber() {
    const modeData = ROYALE_MODES[rouletteRoyaleGame.luxuryMode];
    const tableData = TABLE_LIMITS[rouletteRoyaleGame.tableLimit];
    const levelBias = rouletteRoyaleGame.royaleLevel * 0.07;
    const totalBias = modeData.biasStrength * tableData.biasMultiplier + levelBias;
    
    // Create heavily biased number pool
    const numberPool = [];
    
    // Add all numbers with different weights
    for (let num = 0; num <= 36; num++) {
        let weight = 1;
        
        // Check if players bet on this number
        const playerBetsOnNumber = rouletteRoyaleGame.bets.some(bet => 
            (bet.type === 'straight' && bet.number === num) ||
            (bet.type === 'red' && ROULETTE_NUMBERS[num] === 'red') ||
            (bet.type === 'black' && ROULETTE_NUMBERS[num] === 'black') ||
            (bet.type === 'odd' && num % 2 === 1 && num !== 0) ||
            (bet.type === 'even' && num % 2 === 0 && num !== 0) ||
            (bet.type === 'low' && num >= 1 && num <= 18) ||
            (bet.type === 'high' && num >= 19 && num <= 36) ||
            (bet.type === 'dozen1' && num >= 1 && num <= 12) ||
            (bet.type === 'dozen2' && num >= 13 && num <= 24) ||
            (bet.type === 'dozen3' && num >= 25 && num <= 36)
        );
        
        if (playerBetsOnNumber) {
            // Drastically reduce weight for numbers players bet on
            weight *= (1 - totalBias * 1.8);
        } else {
            // Increase weight for numbers players didn't bet on
            weight *= (1 + totalBias * 0.6);
        }
        
        // Extra bias toward 0 and 37 (house numbers)
        if (num === 0 || num === 37) {
            weight *= (1 + totalBias * 2.2);
        }
        
        // Apply magnetic field effect
        if (rouletteRoyaleGame.magneticField) {
            if (playerBetsOnNumber) {
                weight *= 0.15; // Extreme magnetic repulsion
            } else {
                weight *= 1.85; // Magnetic attraction
            }
        }
        
        // Add to pool based on weight
        const copies = Math.max(1, Math.floor(weight * 100));
        for (let i = 0; i < copies; i++) {
            numberPool.push(num);
        }
    }
    
    // Shuffle and select
    for (let i = numberPool.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numberPool[i], numberPool[j]] = [numberPool[j], numberPool[i]];
    }
    
    return numberPool[Math.floor(Math.random() * numberPool.length)];
}

function generateRoyaleEffects() {
    const modeData = ROYALE_MODES[rouletteRoyaleGame.luxuryMode];
    const tableData = TABLE_LIMITS[rouletteRoyaleGame.tableLimit];
    
    // Magnetic field (very common, hurts player)
    if (Math.random() < modeData.magneticChance + tableData.magneticBonus) {
        rouletteRoyaleGame.magneticField = true;
        rouletteRoyaleGame.stats.magneticInterference++;
    }
    
    // Diamond deflection (common, redirects ball away from player bets)
    if (Math.random() < modeData.deflectionChance + (rouletteRoyaleGame.royaleLevel * 0.03)) {
        rouletteRoyaleGame.diamondDeflection = true;
        rouletteRoyaleGame.stats.diamondDeflections++;
    }
    
    // Royale bias (always active at higher levels)
    if (rouletteRoyaleGame.royaleLevel >= 5 || Math.random() < 0.15) {
        rouletteRoyaleGame.royaleBias = true;
    }
    
    // Platinum penalty (reduces payouts)
    if (rouletteRoyaleGame.luxuryMode === 'platinum' || rouletteRoyaleGame.luxuryMode === 'diamond') {
        if (Math.random() < 0.35 + (rouletteRoyaleGame.royaleLevel * 0.04)) {
            rouletteRoyaleGame.platinumPenalty = true;
            rouletteRoyaleGame.stats.platinumPenalties++;
        }
    }
    
    // Increase royale level occasionally
    if (rouletteRoyaleGame.stats.spinsPlayed > 0 && 
        rouletteRoyaleGame.stats.spinsPlayed % 8 === 0 && 
        rouletteRoyaleGame.royaleLevel < 15) {
        rouletteRoyaleGame.royaleLevel++;
        updateRouletteStatus(`Royale Level increased to ${rouletteRoyaleGame.royaleLevel}!`);
    }
}

function placeBet(type, number = null, amount = null) {
    if (rouletteRoyaleGame.isSpinning) return;
    
    const betAmount = amount || parseInt(document.getElementById('betAmount').value);
    const tableData = TABLE_LIMITS[rouletteRoyaleGame.tableLimit];
    
    if (betAmount < tableData.minBet || betAmount > tableData.maxBet) {
        updateRouletteStatus(`Bet must be between ${tableData.minBet} and ${tableData.maxBet} GA`);
        return;
    }
    
    if (betAmount > balance) {
        updateRouletteStatus('Insufficient balance!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Add bet
    rouletteRoyaleGame.bets.push({ type, number, amount });
    rouletteRoyaleGame.totalBet += betAmount;
    rouletteRoyaleGame.stats.totalWagered += betAmount;
    
    updateRouletteDisplay();
    updateRouletteStatus(`${betAmount} GA bet placed on ${type}${number ? ' ' + number : ''}`);
}

function clearBets() {
    if (rouletteRoyaleGame.isSpinning) return;
    
    // Refund bets
    balance += rouletteRoyaleGame.totalBet;
    updateBalance();
    
    // Clear bets
    rouletteRoyaleGame.bets = [];
    rouletteRoyaleGame.totalBet = 0;
    
    updateRouletteDisplay();
    updateRouletteStatus('All bets cleared');
}

function spinRoyaleWheel() {
    if (rouletteRoyaleGame.isSpinning || rouletteRoyaleGame.bets.length === 0) return;
    
    rouletteRoyaleGame.isSpinning = true;
    
    // Reset effects
    rouletteRoyaleGame.magneticField = false;
    rouletteRoyaleGame.diamondDeflection = false;
    rouletteRoyaleGame.royaleBias = false;
    rouletteRoyaleGame.platinumPenalty = false;
    
    // Generate luxury effects
    generateRoyaleEffects();
    
    updateRouletteStatus('Spinning the Diamond Wheel...');
    
    // Animate spin
    let spinDuration = 3000 + Math.random() * 2000;
    let elapsed = 0;
    const spinInterval = 50;
    
    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        
        // Update wheel position
        rouletteRoyaleGame.wheelPosition += 8 + Math.random() * 12;
        rouletteRoyaleGame.ballPosition += 15 + Math.random() * 10;
        
        // Apply effects during spin
        if (rouletteRoyaleGame.magneticField) {
            updateRouletteStatus('Magnetic field detected - Ball trajectory altered!');
        }
        
        if (rouletteRoyaleGame.diamondDeflection) {
            updateRouletteStatus('Diamond deflection active - Ball redirected!');
        }
        
        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishRoyaleSpin();
        }
    }, spinInterval);
}

function finishRoyaleSpin() {
    // Generate biased result
    rouletteRoyaleGame.currentNumber = generateBiasedRouletteNumber();
    
    // Add to history
    rouletteRoyaleGame.lastNumbers.unshift(rouletteRoyaleGame.currentNumber);
    if (rouletteRoyaleGame.lastNumbers.length > 10) {
        rouletteRoyaleGame.lastNumbers.pop();
    }
    
    rouletteRoyaleGame.wheelHistory.push({
        number: rouletteRoyaleGame.currentNumber,
        bets: [...rouletteRoyaleGame.bets],
        effects: {
            magnetic: rouletteRoyaleGame.magneticField,
            deflection: rouletteRoyaleGame.diamondDeflection,
            bias: rouletteRoyaleGame.royaleBias,
            penalty: rouletteRoyaleGame.platinumPenalty
        }
    });
    
    // Calculate results
    calculateRouletteResults();
    
    rouletteRoyaleGame.isSpinning = false;
    updateRouletteDisplay();
    
    // Clear bets for next spin
    setTimeout(() => {
        rouletteRoyaleGame.bets = [];
        rouletteRoyaleGame.totalBet = 0;
        updateRouletteDisplay();
    }, 3000);
}

function calculateRouletteResults() {
    let totalWin = 0;
    let winningBets = 0;
    const winningNumber = rouletteRoyaleGame.currentNumber;
    const winningColor = ROULETTE_NUMBERS[winningNumber];
    
    const modeData = ROYALE_MODES[rouletteRoyaleGame.luxuryMode];
    
    rouletteRoyaleGame.bets.forEach(bet => {
        let isWinning = false;
        let payout = 0;
        
        // Check if bet wins
        switch (bet.type) {
            case 'straight':
                isWinning = bet.number === winningNumber;
                payout = ROYALE_PAYOUTS.straight;
                break;
            case 'red':
                isWinning = winningColor === 'red';
                payout = ROYALE_PAYOUTS.red;
                break;
            case 'black':
                isWinning = winningColor === 'black';
                payout = ROYALE_PAYOUTS.black;
                break;
            case 'odd':
                isWinning = winningNumber % 2 === 1 && winningNumber !== 0;
                payout = ROYALE_PAYOUTS.odd;
                break;
            case 'even':
                isWinning = winningNumber % 2 === 0 && winningNumber !== 0;
                payout = ROYALE_PAYOUTS.even;
                break;
            case 'low':
                isWinning = winningNumber >= 1 && winningNumber <= 18;
                payout = ROYALE_PAYOUTS.low;
                break;
            case 'high':
                isWinning = winningNumber >= 19 && winningNumber <= 36;
                payout = ROYALE_PAYOUTS.high;
                break;
            case 'dozen1':
                isWinning = winningNumber >= 1 && winningNumber <= 12;
                payout = ROYALE_PAYOUTS.dozen;
                break;
            case 'dozen2':
                isWinning = winningNumber >= 13 && winningNumber <= 24;
                payout = ROYALE_PAYOUTS.dozen;
                break;
            case 'dozen3':
                isWinning = winningNumber >= 25 && winningNumber <= 36;
                payout = ROYALE_PAYOUTS.dozen;
                break;
        }
        
        if (isWinning) {
            winningBets++;
            
            // Apply mode multiplier (reduces payouts further)
            payout *= modeData.payoutMultiplier;
            
            // Apply platinum penalty
            if (rouletteRoyaleGame.platinumPenalty) {
                payout *= 0.45; // 55% penalty
            }
            
            // Apply royale level penalty
            payout *= (1 - (rouletteRoyaleGame.royaleLevel * 0.035));
            
            // Apply house edge
            payout *= (1 - modeData.houseEdge * 0.4);
            
            // Calculate winnings
            let winnings = bet.amount * payout;
            
            // Minimum win of 1 GA
            winnings = Math.floor(Math.max(1, winnings));
            
            totalWin += bet.amount + winnings; // Return bet + winnings
            
            if (bet.type === 'straight') {
                rouletteRoyaleGame.stats.straightHits++;
            }
        }
    });
    
    // Add winnings to balance
    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        
        rouletteRoyaleGame.stats.totalWon += (totalWin - rouletteRoyaleGame.totalBet);
        
        if (totalWin > rouletteRoyaleGame.stats.biggestWin) {
            rouletteRoyaleGame.stats.biggestWin = totalWin;
        }
    }
    
    // Update game stats
    updateRouletteGameStats(winningBets > 0, totalWin);
    
    // Update status
    if (winningBets > 0) {
        const netWin = totalWin - rouletteRoyaleGame.totalBet;
        updateRouletteStatus(`Number ${winningNumber} (${winningColor}) - Won ${netWin} GA!`);
    } else {
        updateRouletteStatus(`Number ${winningNumber} (${winningColor}) - House wins ${rouletteRoyaleGame.totalBet} GA`);
    }
}

function updateRouletteGameStats(won, totalWin) {
    rouletteRoyaleGame.stats.spinsPlayed++;
    
    if (won) {
        rouletteRoyaleGame.stats.spinsWon++;
        
        // Update win streak
        rouletteRoyaleGame.streakData.currentWinStreak++;
        rouletteRoyaleGame.streakData.currentLossStreak = 0;
        
        if (rouletteRoyaleGame.streakData.currentWinStreak > rouletteRoyaleGame.streakData.longestWinStreak) {
            rouletteRoyaleGame.streakData.longestWinStreak = rouletteRoyaleGame.streakData.currentWinStreak;
        }
    } else {
        // Update loss streak
        rouletteRoyaleGame.streakData.currentLossStreak++;
        rouletteRoyaleGame.streakData.currentWinStreak = 0;
        
        if (rouletteRoyaleGame.streakData.currentLossStreak > rouletteRoyaleGame.streakData.longestLossStreak) {
            rouletteRoyaleGame.streakData.longestLossStreak = rouletteRoyaleGame.streakData.currentLossStreak;
        }
    }
}

function updateRouletteDisplay() {
    // Update current number
    if (rouletteRoyaleGame.currentNumber !== null) {
        const color = ROULETTE_NUMBERS[rouletteRoyaleGame.currentNumber];
        document.getElementById('currentNumber').innerHTML = `
            <div class="text-6xl font-bold ${color === 'red' ? 'text-red-400' : color === 'black' ? 'text-gray-300' : 'text-green-400'} neon-glow">
                ${rouletteRoyaleGame.currentNumber}
            </div>
            <div class="text-xl text-${color === 'red' ? 'red' : color === 'black' ? 'gray' : 'green'}-400">
                ${color.toUpperCase()}
            </div>
        `;
    }
    
    // Update last numbers
    const lastNumbersEl = document.getElementById('lastNumbers');
    lastNumbersEl.innerHTML = rouletteRoyaleGame.lastNumbers.map(num => {
        const color = ROULETTE_NUMBERS[num];
        return `<span class="inline-block w-8 h-8 rounded-full text-center leading-8 text-sm font-bold ${
            color === 'red' ? 'bg-red-500 text-white' : 
            color === 'black' ? 'bg-gray-800 text-white' : 
            'bg-green-500 text-white'
        }">${num}</span>`;
    }).join(' ');
    
    // Update total bet
    document.getElementById('totalBet').textContent = rouletteRoyaleGame.totalBet;
    
    // Update active bets
    const activeBetsEl = document.getElementById('activeBets');
    activeBetsEl.innerHTML = rouletteRoyaleGame.bets.map(bet => 
        `<div class="text-sm">${bet.amount} GA on ${bet.type}${bet.number ? ' ' + bet.number : ''}</div>`
    ).join('');
    
    // Update effects
    document.getElementById('royaleLevel').textContent = rouletteRoyaleGame.royaleLevel;
    document.getElementById('magneticField').style.display = rouletteRoyaleGame.magneticField ? 'block' : 'none';
    document.getElementById('diamondDeflection').style.display = rouletteRoyaleGame.diamondDeflection ? 'block' : 'none';
    document.getElementById('royaleBias').style.display = rouletteRoyaleGame.royaleBias ? 'block' : 'none';
    document.getElementById('platinumPenalty').style.display = rouletteRoyaleGame.platinumPenalty ? 'block' : 'none';
    
    // Update statistics
    updateRouletteStats();
}

function updateRouletteStats() {
    document.getElementById('spinsPlayed').textContent = rouletteRoyaleGame.stats.spinsPlayed;
    
    const winRate = rouletteRoyaleGame.stats.spinsPlayed > 0 ? 
        ((rouletteRoyaleGame.stats.spinsWon / rouletteRoyaleGame.stats.spinsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('winRate').textContent = `${winRate}%`;
    
    document.getElementById('totalWagered').textContent = rouletteRoyaleGame.stats.totalWagered.toLocaleString();
    document.getElementById('totalWon').textContent = rouletteRoyaleGame.stats.totalWon.toLocaleString();
    document.getElementById('biggestWin').textContent = rouletteRoyaleGame.stats.biggestWin.toLocaleString();
    document.getElementById('straightHits').textContent = rouletteRoyaleGame.stats.straightHits;
    document.getElementById('lossStreak').textContent = rouletteRoyaleGame.streakData.currentLossStreak;
    document.getElementById('magneticInterference').textContent = rouletteRoyaleGame.stats.magneticInterference;
    document.getElementById('diamondDeflections').textContent = rouletteRoyaleGame.stats.diamondDeflections;
    document.getElementById('platinumPenalties').textContent = rouletteRoyaleGame.stats.platinumPenalties;
}

function updateRouletteStatus(message) {
    document.getElementById('rouletteStatus').textContent = message;
}

function changeLuxuryMode() {
    const mode = document.getElementById('luxuryMode').value;
    rouletteRoyaleGame.luxuryMode = mode;
    
    const modeData = ROYALE_MODES[mode];
    document.getElementById('luxuryModeInfo').innerHTML = `
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Magnetic: ${(modeData.magneticChance * 100).toFixed(0)}% | 
        Deflection: ${(modeData.deflectionChance * 100).toFixed(0)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}%
    `;
}

function changeTableLimit() {
    const limit = document.getElementById('tableLimit').value;
    rouletteRoyaleGame.tableLimit = limit;
    
    const limitData = TABLE_LIMITS[limit];
    document.getElementById('tableLimitInfo').innerHTML = `
        Min Bet: ${limitData.minBet} GA | 
        Max Bet: ${limitData.maxBet} GA | 
        Bias: ${(limitData.biasMultiplier * 100).toFixed(0)}% | 
        Magnetic: +${(limitData.magneticBonus * 100).toFixed(0)}%
    `;
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeLuxuryMode();
    changeTableLimit();
    
    // Add event listeners
    document.getElementById('spinWheel').addEventListener('click', spinRoyaleWheel);
    document.getElementById('clearBets').addEventListener('click', clearBets);
    document.getElementById('luxuryMode').addEventListener('change', changeLuxuryMode);
    document.getElementById('tableLimit').addEventListener('change', changeTableLimit);
    
    // Betting buttons
    document.getElementById('betRed').addEventListener('click', () => placeBet('red'));
    document.getElementById('betBlack').addEventListener('click', () => placeBet('black'));
    document.getElementById('betOdd').addEventListener('click', () => placeBet('odd'));
    document.getElementById('betEven').addEventListener('click', () => placeBet('even'));
    document.getElementById('betLow').addEventListener('click', () => placeBet('low'));
    document.getElementById('betHigh').addEventListener('click', () => placeBet('high'));
    document.getElementById('betDozen1').addEventListener('click', () => placeBet('dozen1'));
    document.getElementById('betDozen2').addEventListener('click', () => placeBet('dozen2'));
    document.getElementById('betDozen3').addEventListener('click', () => placeBet('dozen3'));
    
    // Straight number bets (0-36)
    for (let i = 0; i <= 36; i++) {
        const btn = document.getElementById(`bet${i}`);
        if (btn) {
            btn.addEventListener('click', () => placeBet('straight', i));
        }
    }
});