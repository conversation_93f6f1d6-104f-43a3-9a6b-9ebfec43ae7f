// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Blackjack Supreme VIP Implementation with premium features
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// VIP Blackjack payouts and features
const VIP_PAYOUTS = {
    BLACKJACK: 2.5,        // 3:2 premium payout
    WIN: 2.0,              // 1:1 standard win
    PUSH: 1.0,             // Return bet
    INSURANCE: 3.0,        // 2:1 insurance
    SURRENDER: 0.5,        // 50% return
    PERFECT_PAIRS: 25,     // 25:1 for perfect pairs
    LUCKY_LADIES: 1000,    // Up to 1000:1
    ROYAL_MATCH: 25,       // 25:1 for suited royals
    VIP_BONUS: 1.5         // VIP multiplier
};

// VIP membership tiers
const VIP_TIERS = [
    {
        name: 'platinum',
        description: 'Platinum VIP (Min: 100 GA)',
        minBet: 100,
        maxBet: 2000,
        bonusMultiplier: 1.1,
        specialFeatures: ['Basic Strategy Hints', 'Card Counting Display']
    },
    {
        name: 'diamond',
        description: 'Diamond VIP (Min: 500 GA)',
        minBet: 500,
        maxBet: 10000,
        bonusMultiplier: 1.25,
        specialFeatures: ['Advanced Strategy', 'Hole Card Peek', 'Auto-Play']
    },
    {
        name: 'supreme',
        description: 'Supreme VIP (Min: 2000 GA)',
        minBet: 2000,
        maxBet: 50000,
        bonusMultiplier: 1.5,
        specialFeatures: ['Perfect Strategy AI', 'Predictive Analytics', 'Exclusive Tables']
    }
];

let blackjackSupremeGame = {
    deck: [],
    playerHands: [[]],
    dealerCards: [],
    currentHandIndex: 0,
    gamePhase: 'betting', // betting, dealing, playing, dealer, complete
    betAmount: 0,
    handBets: [0],
    
    // VIP features
    vipTier: 'platinum',
    vipLevel: 1,
    supremePoints: 0,
    
    // Advanced betting
    sideBets: {
        perfectPairs: { active: false, amount: 0, odds: 25 },
        luckyLadies: { active: false, amount: 0, odds: 1000 },
        royalMatch: { active: false, amount: 0, odds: 25 },
        insurance: { active: false, amount: 0, odds: 2 }
    },
    
    // Strategy features
    basicStrategy: true,
    cardCounting: false,
    runningCount: 0,
    trueCount: 0,
    cardsDealt: 0,
    
    // Hand tracking
    canSplit: false,
    canDoubleDown: false,
    canSurrender: true,
    maxSplits: 3,
    splitsUsed: 0,
    
    // VIP statistics
    sessionStats: {
        handsPlayed: 0,
        handsWon: 0,
        blackjacks: 0,
        perfectPairs: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        surrenders: 0,
        splits: 0,
        doubleDowns: 0,
        insuranceWins: 0,
        supremeWins: 0
    },
    
    // Premium features
    autoStrategy: false,
    showRecommendations: true,
    holeCardPeek: false,
    predictiveMode: false
};

function loadBlackjackSupremeGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-7xl mx-auto">
            <!-- VIP Header -->
            <div class="text-center mb-6">
                <h3 class="text-4xl font-bold cyber-title mb-4">Blackjack Supreme: VIP</h3>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div>VIP Level: <span id="vipLevel" class="text-purple-400">1</span></div>
                    <div>Supreme Points: <span id="supremePoints" class="text-blue-400">0</span></div>
                    <div>Current Tier: <span id="currentVipTier" class="text-green-400">Platinum VIP</span></div>
                </div>
                <div class="mt-2">
                    <select id="vipTierSelect" class="cyber-select bg-cyber-dark border border-purple-500 rounded px-4 py-2">
                        <option value="platinum">Platinum VIP (Min: 100 GA)</option>
                        <option value="diamond">Diamond VIP (Min: 500 GA)</option>
                        <option value="supreme">Supreme VIP (Min: 2000 GA)</option>
                    </select>
                </div>
            </div>

            <!-- Game Table -->
            <div class="cyber-card border-purple-500 mb-8 p-6">
                <!-- Dealer Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-purple-400 mb-4">🎩 VIP Dealer</h4>
                    <div id="dealerArea" class="flex justify-center space-x-2 mb-4 min-h-[120px] items-center">
                        <div class="card-placeholder">Dealer Cards</div>
                    </div>
                    <div id="dealerValue" class="text-lg font-bold text-white"></div>
                </div>

                <!-- Player Area -->
                <div class="text-center">
                    <h4 class="text-xl font-semibold text-green-400 mb-4">👑 Your Hand</h4>
                    <div id="playerArea" class="flex justify-center space-x-2 mb-4 min-h-[120px] items-center">
                        <div class="card-placeholder">Your Cards</div>
                    </div>
                    <div id="playerValue" class="text-lg font-bold text-white mb-4"></div>
                    
                    <!-- Action Buttons -->
                    <div class="flex justify-center space-x-4 mb-4">
                        <button id="hitButton" class="cyber-button bg-green-600 hover:bg-green-700 px-6 py-2 rounded-lg font-semibold" disabled>
                            HIT
                        </button>
                        <button id="standButton" class="cyber-button bg-red-600 hover:bg-red-700 px-6 py-2 rounded-lg font-semibold" disabled>
                            STAND
                        </button>
                        <button id="doubleButton" class="cyber-button bg-yellow-600 hover:bg-yellow-700 px-6 py-2 rounded-lg font-semibold" disabled>
                            DOUBLE
                        </button>
                        <button id="splitButton" class="cyber-button bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg font-semibold" disabled>
                            SPLIT
                        </button>
                        <button id="surrenderButton" class="cyber-button bg-gray-600 hover:bg-gray-700 px-6 py-2 rounded-lg font-semibold" disabled>
                            SURRENDER
                        </button>
                    </div>
                </div>
            </div>

            <!-- Game Controls -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
                <!-- Betting Panel -->
                <div class="cyber-card border-purple-500">
                    <h5 class="text-lg font-semibold text-purple-400 mb-3">💎 VIP Betting</h5>
                    <div class="space-y-3">
                        <div class="text-center">
                            <div class="text-sm text-gray-400" id="betRange">Range: 100 - 2,000 GA</div>
                            <input type="number" id="supremeBetInput" value="100" min="100" max="2000" step="25"
                                   class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white text-center text-lg font-bold">
                        </div>
                        <div class="text-center">
                            <span id="supremeBetAmount" class="text-xl font-bold text-purple-400">100 GA</span>
                        </div>
                        <button id="dealCards" class="w-full cyber-button-primary py-3 rounded-lg font-semibold">
                            DEAL CARDS
                        </button>
                        <div class="grid grid-cols-3 gap-2">
                            <button onclick="setQuickBet(100)" class="cyber-button-secondary py-1 text-sm rounded">100</button>
                            <button onclick="setQuickBet(500)" class="cyber-button-secondary py-1 text-sm rounded">500</button>
                            <button onclick="setMaxBet()" class="cyber-button-secondary py-1 text-sm rounded">MAX</button>
                        </div>
                    </div>
                </div>

                <!-- Side Bets -->
                <div class="cyber-card border-green-500">
                    <h5 class="text-lg font-semibold text-green-400 mb-3">🍀 VIP Side Bets</h5>
                    <div class="space-y-2 text-sm">
                        <label class="flex items-center justify-between">
                            <span>Perfect Pairs (25:1)</span>
                            <input type="checkbox" id="perfectPairsCheck" class="accent-green-400">
                        </label>
                        <input type="number" id="perfectPairsAmount" value="25" min="5" max="500" step="5"
                               class="w-full bg-black/50 border border-green-500/30 rounded px-2 py-1 text-white text-xs">
                        
                        <label class="flex items-center justify-between">
                            <span>Lucky Ladies (1000:1)</span>
                            <input type="checkbox" id="luckyLadiesCheck" class="accent-green-400">
                        </label>
                        <input type="number" id="luckyLadiesAmount" value="10" min="5" max="100" step="5"
                               class="w-full bg-black/50 border border-green-500/30 rounded px-2 py-1 text-white text-xs">
                        
                        <label class="flex items-center justify-between">
                            <span>Royal Match (25:1)</span>
                            <input type="checkbox" id="royalMatchCheck" class="accent-green-400">
                        </label>
                        <input type="number" id="royalMatchAmount" value="20" min="5" max="200" step="5"
                               class="w-full bg-black/50 border border-green-500/30 rounded px-2 py-1 text-white text-xs">
                    </div>
                </div>

                <!-- VIP Features -->
                <div class="cyber-card border-yellow-500">
                    <h5 class="text-lg font-semibold text-yellow-400 mb-3">⭐ VIP Features</h5>
                    <div class="space-y-2 text-sm">
                        <label class="flex items-center justify-between">
                            <span>Strategy Hints</span>
                            <input type="checkbox" id="strategyHints" checked class="accent-yellow-400">
                        </label>
                        <label class="flex items-center justify-between">
                            <span>Card Counting</span>
                            <input type="checkbox" id="cardCounting" class="accent-yellow-400">
                        </label>
                        <label class="flex items-center justify-between">
                            <span>Auto Strategy</span>
                            <input type="checkbox" id="autoStrategy" class="accent-yellow-400">
                        </label>
                        <label class="flex items-center justify-between">
                            <span>Hole Card Peek</span>
                            <input type="checkbox" id="holeCardPeek" class="accent-yellow-400">
                        </label>
                    </div>
                    
                    <!-- Card Count Display -->
                    <div id="cardCountDisplay" class="mt-4 p-2 bg-black/30 rounded hidden">
                        <div class="text-xs text-center">
                            <div>Running: <span id="runningCount" class="text-yellow-400">0</span></div>
                            <div>True: <span id="trueCount" class="text-yellow-400">0</span></div>
                        </div>
                    </div>
                </div>

                <!-- Strategy Recommendations -->
                <div class="cyber-card border-blue-500">
                    <h5 class="text-lg font-semibold text-blue-400 mb-3">🧠 Strategy Assistant</h5>
                    <div id="recommendationText" class="text-sm text-center p-3 bg-black/30 rounded min-h-[60px] flex items-center justify-center">
                        Place your bet to begin
                    </div>
                    <div class="mt-3 space-y-1 text-xs">
                        <div>Hand: <span id="currentHandInfo" class="text-blue-400">-</span></div>
                        <div>Dealer: <span id="dealerUpCard" class="text-blue-400">-</span></div>
                        <div>Optimal: <span id="optimalPlay" class="text-blue-400">-</span></div>
                    </div>
                </div>
            </div>

            <!-- Game Status -->
            <div class="cyber-card border-gray-500 mb-6">
                <div class="text-center">
                    <div id="gameResult" class="text-xl font-bold mb-2"></div>
                    <div id="gameStatus" class="text-lg text-gray-300">Ready to play VIP Blackjack</div>
                </div>
            </div>

            <!-- Statistics Dashboard -->
            <div class="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                <div class="cyber-card border-purple-500/50 text-center">
                    <div class="text-2xl font-bold text-purple-400" id="handsPlayed">0</div>
                    <div class="text-sm text-gray-400">Hands Played</div>
                </div>
                <div class="cyber-card border-green-500/50 text-center">
                    <div class="text-2xl font-bold text-green-400" id="handsWon">0</div>
                    <div class="text-sm text-gray-400">Hands Won</div>
                </div>
                <div class="cyber-card border-yellow-500/50 text-center">
                    <div class="text-2xl font-bold text-yellow-400" id="blackjacks">0</div>
                    <div class="text-sm text-gray-400">Blackjacks</div>
                </div>
                <div class="cyber-card border-blue-500/50 text-center">
                    <div class="text-2xl font-bold text-blue-400" id="totalWagered">0</div>
                    <div class="text-sm text-gray-400">Total Wagered</div>
                </div>
                <div class="cyber-card border-red-500/50 text-center">
                    <div class="text-2xl font-bold text-red-400" id="biggestWin">0</div>
                    <div class="text-sm text-gray-400">Biggest Win</div>
                </div>
            </div>
        </div>
    `;

    setupBlackjackSupremeGame();
}

function setupBlackjackSupremeGame() {
    // Event listeners
    document.getElementById('supremeBetInput').addEventListener('input', updateSupremeBet);
    document.getElementById('dealCards').addEventListener('click', dealSupremeCards);
    document.getElementById('hitButton').addEventListener('click', hitCard);
    document.getElementById('standButton').addEventListener('click', standHand);
    document.getElementById('doubleButton').addEventListener('click', doubleDown);
    document.getElementById('splitButton').addEventListener('click', splitHand);
    document.getElementById('surrenderButton').addEventListener('click', surrenderHand);
    document.getElementById('vipTierSelect').addEventListener('change', updateVipTier);
    
    // Side bet listeners
    document.getElementById('perfectPairsCheck').addEventListener('change', updateSideBets);
    document.getElementById('luckyLadiesCheck').addEventListener('change', updateSideBets);
    document.getElementById('royalMatchCheck').addEventListener('change', updateSideBets);
    
    // VIP feature listeners
    document.getElementById('strategyHints').addEventListener('change', toggleStrategyHints);
    document.getElementById('cardCounting').addEventListener('change', toggleCardCounting);
    document.getElementById('autoStrategy').addEventListener('change', toggleAutoStrategy);
    document.getElementById('holeCardPeek').addEventListener('change', toggleHoleCardPeek);
    
    // Initialize game
    initializeSupremeDeck();
    updateSupremeDisplay();
    updateSupremeStats();
}

function updateVipTier() {
    const tier = document.getElementById('vipTierSelect').value;
    blackjackSupremeGame.vipTier = tier;
    
    const tierData = VIP_TIERS.find(t => t.name === tier);
    document.getElementById('currentVipTier').textContent = tierData.description.split(' (')[0];
    document.getElementById('betRange').textContent = `Range: ${tierData.minBet} - ${tierData.maxBet.toLocaleString()} GA`;
    
    // Update bet input constraints
    const betInput = document.getElementById('supremeBetInput');
    betInput.min = tierData.minBet;
    betInput.max = tierData.maxBet;
    betInput.value = tierData.minBet;
    
    updateSupremeBet();
    updateVipFeatures();
}

function updateVipFeatures() {
    const tierData = VIP_TIERS.find(t => t.name === blackjackSupremeGame.vipTier);
    
    // Enable/disable features based on tier
    document.getElementById('cardCounting').disabled = !tierData.specialFeatures.includes('Card Counting Display');
    document.getElementById('holeCardPeek').disabled = !tierData.specialFeatures.includes('Hole Card Peek');
    document.getElementById('autoStrategy').disabled = !tierData.specialFeatures.includes('Perfect Strategy AI');
}

function updateSupremeBet() {
    const bet = parseInt(document.getElementById('supremeBetInput').value);
    const tierData = VIP_TIERS.find(t => t.name === blackjackSupremeGame.vipTier);
    
    // Enforce tier limits
    const clampedBet = Math.max(tierData.minBet, Math.min(tierData.maxBet, bet));
    document.getElementById('supremeBetInput').value = clampedBet;
    
    document.getElementById('supremeBetAmount').textContent = `${clampedBet.toLocaleString()} GA`;
    blackjackSupremeGame.betAmount = clampedBet;
}

function updateSideBets() {
    blackjackSupremeGame.sideBets.perfectPairs.active = document.getElementById('perfectPairsCheck').checked;
    blackjackSupremeGame.sideBets.perfectPairs.amount = parseInt(document.getElementById('perfectPairsAmount').value) || 0;
    
    blackjackSupremeGame.sideBets.luckyLadies.active = document.getElementById('luckyLadiesCheck').checked;
    blackjackSupremeGame.sideBets.luckyLadies.amount = parseInt(document.getElementById('luckyLadiesAmount').value) || 0;
    
    blackjackSupremeGame.sideBets.royalMatch.active = document.getElementById('royalMatchCheck').checked;
    blackjackSupremeGame.sideBets.royalMatch.amount = parseInt(document.getElementById('royalMatchAmount').value) || 0;
}

function setQuickBet(amount) {
    const tierData = VIP_TIERS.find(t => t.name === blackjackSupremeGame.vipTier);
    const clampedAmount = Math.max(tierData.minBet, Math.min(tierData.maxBet, amount));
    document.getElementById('supremeBetInput').value = clampedAmount;
    updateSupremeBet();
}

function setMaxBet() {
    const tierData = VIP_TIERS.find(t => t.name === blackjackSupremeGame.vipTier);
    const maxAffordable = Math.min(tierData.maxBet, balance);
    document.getElementById('supremeBetInput').value = maxAffordable;
    updateSupremeBet();
}

function initializeSupremeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    
    blackjackSupremeGame.deck = [];
    
    // Create 6 decks for VIP play
    for (let d = 0; d < 6; d++) {
        for (const suit of suits) {
            for (const value of values) {
                blackjackSupremeGame.deck.push({ suit, value });
            }
        }
    }
    
    // Shuffle deck
    for (let i = blackjackSupremeGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [blackjackSupremeGame.deck[i], blackjackSupremeGame.deck[j]] = [blackjackSupremeGame.deck[j], blackjackSupremeGame.deck[i]];
    }
    
    blackjackSupremeGame.cardsDealt = 0;
    blackjackSupremeGame.runningCount = 0;
}

function createSupremeCardElement(card, isHidden = false) {
    const cardElement = document.createElement('div');
    cardElement.className = 'card bg-white rounded-lg shadow-lg p-2 w-16 h-24 flex flex-col justify-between text-center border-2';
    
    if (isHidden) {
        cardElement.innerHTML = '💎';
        cardElement.className += ' bg-gradient-to-br from-purple-900 to-black text-purple-400';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.innerHTML = `
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-xs">${card.value}</div>
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-lg">${card.suit}</div>
        `;
        
        // Update card counting
        if (blackjackSupremeGame.cardCounting) {
            updateCardCount(card);
        }
    }
    
    return cardElement;
}

function updateCardCount(card) {
    const value = card.value;
    if (['2', '3', '4', '5', '6'].includes(value)) {
        blackjackSupremeGame.runningCount += 1;
    } else if (['10', 'J', 'Q', 'K', 'A'].includes(value)) {
        blackjackSupremeGame.runningCount -= 1;
    }
    
    blackjackSupremeGame.cardsDealt++;
}

function dealSupremeCards() {
    if (balance < blackjackSupremeGame.betAmount) {
        document.getElementById('gameStatus').textContent = 'Insufficient balance!';
        return;
    }
    
    // Calculate total side bets
    let totalSideBets = 0;
    Object.values(blackjackSupremeGame.sideBets).forEach(bet => {
        if (bet.active) totalSideBets += bet.amount;
    });
    
    if (balance < blackjackSupremeGame.betAmount + totalSideBets) {
        document.getElementById('gameStatus').textContent = 'Insufficient balance for side bets!';
        return;
    }
    
    // Deduct bets
    balance -= blackjackSupremeGame.betAmount + totalSideBets;
    updateBalance();
    
    // Initialize hand
    blackjackSupremeGame.playerHands = [[]];
    blackjackSupremeGame.dealerCards = [];
    blackjackSupremeGame.handBets = [blackjackSupremeGame.betAmount];
    blackjackSupremeGame.currentHandIndex = 0;
    blackjackSupremeGame.gamePhase = 'dealing';
    blackjackSupremeGame.splitsUsed = 0;
    
    // Deal initial cards
    blackjackSupremeGame.playerHands[0].push(blackjackSupremeGame.deck.pop());
    blackjackSupremeGame.dealerCards.push(blackjackSupremeGame.deck.pop());
    blackjackSupremeGame.playerHands[0].push(blackjackSupremeGame.deck.pop());
    blackjackSupremeGame.dealerCards.push(blackjackSupremeGame.deck.pop());
    
    displaySupremeCards();
    
    // Check for dealer ace (insurance)
    if (blackjackSupremeGame.dealerCards[0].value === 'A') {
        showInsuranceOffer();
        return;
    }
    
    // Check for blackjacks
    checkInitialConditions();
    
    // Disable deal button
    document.getElementById('dealCards').disabled = true;
    
    updateSupremeDisplay();
}

function displaySupremeCards() {
    // Display dealer cards
    const dealerArea = document.getElementById('dealerArea');
    dealerArea.innerHTML = '';
    
    blackjackSupremeGame.dealerCards.forEach((card, index) => {
        const isHidden = index === 1 && blackjackSupremeGame.gamePhase !== 'dealer' && blackjackSupremeGame.gamePhase !== 'complete';
        const cardElement = createSupremeCardElement(card, isHidden);
        dealerArea.appendChild(cardElement);
    });
    
    // Display player cards
    const playerArea = document.getElementById('playerArea');
    playerArea.innerHTML = '';
    
    blackjackSupremeGame.playerHands.forEach((hand, handIndex) => {
        const handContainer = document.createElement('div');
        handContainer.className = `hand-container flex space-x-2 ${handIndex === blackjackSupremeGame.currentHandIndex ? 'ring-2 ring-green-400 rounded p-2' : ''}`;
        
        hand.forEach(card => {
            const cardElement = createSupremeCardElement(card);
            handContainer.appendChild(cardElement);
        });
        
        playerArea.appendChild(handContainer);
    });
    
    updateHandValues();
    updateActionButtons();
    updateStrategyRecommendation();
}

function calculateHandValue(hand) {
    let value = 0;
    let aces = 0;
    
    hand.forEach(card => {
        if (card.value === 'A') {
            aces++;
            value += 11;
        } else if (['K', 'Q', 'J'].includes(card.value)) {
            value += 10;
        } else {
            value += parseInt(card.value);
        }
    });
    
    // Adjust for aces
    while (value > 21 && aces > 0) {
        value -= 10;
        aces--;
    }
    
    const isSoft = aces > 0 && value <= 21;
    const isBlackjack = hand.length === 2 && value === 21;
    
    return {
        value: value,
        isSoft: isSoft,
        isBlackjack: isBlackjack,
        display: value > 21 ? `BUST (${value})` : isBlackjack ? 'BLACKJACK!' : isSoft ? `Soft ${value}` : value.toString()
    };
}

function updateHandValues() {
    // Update dealer value
    const dealerValue = calculateHandValue(blackjackSupremeGame.dealerCards);
    if (blackjackSupremeGame.gamePhase === 'dealer' || blackjackSupremeGame.gamePhase === 'complete') {
        document.getElementById('dealerValue').textContent = `Dealer: ${dealerValue.display}`;
    } else {
        const upCardValue = blackjackSupremeGame.dealerCards[0].value === 'A' ? 11 : 
                           ['K', 'Q', 'J'].includes(blackjackSupremeGame.dealerCards[0].value) ? 10 : 
                           parseInt(blackjackSupremeGame.dealerCards[0].value);
        document.getElementById('dealerValue').textContent = `Dealer: ${upCardValue} + ?`;
    }
    
    // Update player value
    const currentHand = blackjackSupremeGame.playerHands[blackjackSupremeGame.currentHandIndex];
    const playerValue = calculateHandValue(currentHand);
    document.getElementById('playerValue').textContent = `Player: ${playerValue.display}`;
}

function updateActionButtons() {
    const currentHand = blackjackSupremeGame.playerHands[blackjackSupremeGame.currentHandIndex];
    const handValue = calculateHandValue(currentHand);
    const isPlaying = blackjackSupremeGame.gamePhase === 'playing';
    
    // Hit button
    document.getElementById('hitButton').disabled = !isPlaying || handValue.value >= 21;
    
    // Stand button
    document.getElementById('standButton').disabled = !isPlaying;
    
    // Double down button
    const canDouble = isPlaying && currentHand.length === 2 && balance >= blackjackSupremeGame.handBets[blackjackSupremeGame.currentHandIndex];
    document.getElementById('doubleButton').disabled = !canDouble;
    
    // Split button
    const canSplit = isPlaying && currentHand.length === 2 && 
                    currentHand[0].value === currentHand[1].value && 
                    blackjackSupremeGame.splitsUsed < blackjackSupremeGame.maxSplits &&
                    balance >= blackjackSupremeGame.handBets[blackjackSupremeGame.currentHandIndex];
    document.getElementById('splitButton').disabled = !canSplit;
    
    // Surrender button
    const canSurrender = isPlaying && currentHand.length === 2 && blackjackSupremeGame.canSurrender;
    document.getElementById('surrenderButton').disabled = !canSurrender;
}

function checkInitialConditions() {
    const playerValue = calculateHandValue(blackjackSupremeGame.playerHands[0]);
    const dealerValue = calculateHandValue(blackjackSupremeGame.dealerCards);
    
    if (playerValue.isBlackjack && dealerValue.isBlackjack) {
        // Push
        endHand('push', blackjackSupremeGame.betAmount);
    } else if (playerValue.isBlackjack) {
        // Player blackjack
        blackjackSupremeGame.sessionStats.blackjacks++;
        endHand('blackjack', Math.floor(blackjackSupremeGame.betAmount * VIP_PAYOUTS.BLACKJACK));
    } else if (dealerValue.isBlackjack) {
        // Dealer blackjack
        endHand('dealer_blackjack', 0);
    } else {
        // Normal play
        blackjackSupremeGame.gamePhase = 'playing';
        updateActionButtons();
    }
}

function hitCard() {
    const currentHand = blackjackSupremeGame.playerHands[blackjackSupremeGame.currentHandIndex];
    currentHand.push(blackjackSupremeGame.deck.pop());
    
    displaySupremeCards();
    
    const handValue = calculateHandValue(currentHand);
    if (handValue.value >= 21) {
        // Auto-stand on 21 or bust
        setTimeout(() => {
            standHand();
        }, 1000);
    }
}

function standHand() {
    blackjackSupremeGame.currentHandIndex++;
    
    if (blackjackSupremeGame.currentHandIndex >= blackjackSupremeGame.playerHands.length) {
        // All hands complete, dealer plays
        playDealerHand();
    } else {
        // Move to next hand
        displaySupremeCards();
    }
}

function doubleDown() {
    const currentHandIndex = blackjackSupremeGame.currentHandIndex;
    const additionalBet = blackjackSupremeGame.handBets[currentHandIndex];
    
    if (balance < additionalBet) {
        document.getElementById('gameStatus').textContent = 'Insufficient balance to double down!';
        return;
    }
    
    balance -= additionalBet;
    blackjackSupremeGame.handBets[currentHandIndex] *= 2;
    updateBalance();
    
    // Hit one card and stand
    hitCard();
    blackjackSupremeGame.sessionStats.doubleDowns++;
    
    setTimeout(() => {
        standHand();
    }, 1500);
}

function splitHand() {
    const currentHand = blackjackSupremeGame.playerHands[blackjackSupremeGame.currentHandIndex];
    const splitBet = blackjackSupremeGame.handBets[blackjackSupremeGame.currentHandIndex];
    
    if (balance < splitBet) {
        document.getElementById('gameStatus').textContent = 'Insufficient balance to split!';
        return;
    }
    
    balance -= splitBet;
    updateBalance();
    
    // Create new hand with second card
    const newHand = [currentHand.pop()];
    blackjackSupremeGame.playerHands.push(newHand);
    blackjackSupremeGame.handBets.push(splitBet);
    
    // Deal new cards to both hands
    currentHand.push(blackjackSupremeGame.deck.pop());
    newHand.push(blackjackSupremeGame.deck.pop());
    
    blackjackSupremeGame.splitsUsed++;
    blackjackSupremeGame.sessionStats.splits++;
    
    displaySupremeCards();
}

function surrenderHand() {
    const surrenderAmount = Math.floor(blackjackSupremeGame.betAmount * VIP_PAYOUTS.SURRENDER);
    balance += surrenderAmount;
    updateBalance();
    
    blackjackSupremeGame.sessionStats.surrenders++;
    endHand('surrender', surrenderAmount);
}

function showInsuranceOffer() {
    const insuranceAmount = Math.floor(blackjackSupremeGame.betAmount / 2);
    const takeInsurance = confirm(`Dealer shows Ace. Take insurance for ${insuranceAmount} GA?`);
    
    if (takeInsurance && balance >= insuranceAmount) {
        balance -= insuranceAmount;
        blackjackSupremeGame.sideBets.insurance.active = true;
        blackjackSupremeGame.sideBets.insurance.amount = insuranceAmount;
        updateBalance();
    }
    
    checkInitialConditions();
}

function playDealerHand() {
    blackjackSupremeGame.gamePhase = 'dealer';
    displaySupremeCards();
    
    const dealerPlay = () => {
        const dealerValue = calculateHandValue(blackjackSupremeGame.dealerCards);
        
        if (dealerValue.value < 17 || (dealerValue.value === 17 && dealerValue.isSoft)) {
            blackjackSupremeGame.dealerCards.push(blackjackSupremeGame.deck.pop());
            displaySupremeCards();
            setTimeout(dealerPlay, 1000);
        } else {
            resolveAllHands();
        }
    };
    
    setTimeout(dealerPlay, 1000);
}

function resolveAllHands() {
    blackjackSupremeGame.gamePhase = 'complete';
    const dealerValue = calculateHandValue(blackjackSupremeGame.dealerCards);
    const tierData = VIP_TIERS.find(t => t.name === blackjackSupremeGame.vipTier);
    
    let totalWinnings = 0;
    let results = [];
    
    blackjackSupremeGame.playerHands.forEach((hand, index) => {
        const handValue = calculateHandValue(hand);
        const betAmount = blackjackSupremeGame.handBets[index];
        let handWinnings = 0;
        let result = '';
        
        if (handValue.value > 21) {
            result = 'BUST';
        } else if (dealerValue.value > 21) {
            handWinnings = betAmount * VIP_PAYOUTS.WIN;
            result = 'WIN (Dealer Bust)';
            blackjackSupremeGame.sessionStats.handsWon++;
        } else if (handValue.value > dealerValue.value) {
            handWinnings = betAmount * VIP_PAYOUTS.WIN;
            result = 'WIN';
            blackjackSupremeGame.sessionStats.handsWon++;
        } else if (handValue.value === dealerValue.value) {
            handWinnings = betAmount;
            result = 'PUSH';
        } else {
            result = 'LOSE';
        }
        
        // Apply VIP bonus
        if (handWinnings > betAmount) {
            const bonus = Math.floor((handWinnings - betAmount) * (tierData.bonusMultiplier - 1));
            handWinnings += bonus;
            if (bonus > 0) {
                result += ` (+${bonus} VIP Bonus)`;
            }
        }
        
        totalWinnings += handWinnings;
        results.push(`Hand ${index + 1}: ${result}`);
    });
    
    // Check side bets
    const sideBetResults = checkSideBets();
    totalWinnings += sideBetResults.winnings;
    if (sideBetResults.results.length > 0) {
        results.push(...sideBetResults.results);
    }
    
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    blackjackSupremeGame.sessionStats.handsPlayed++;
    blackjackSupremeGame.sessionStats.totalWagered += blackjackSupremeGame.handBets.reduce((a, b) => a + b, 0);
    blackjackSupremeGame.sessionStats.totalWon += totalWinnings;
    blackjackSupremeGame.sessionStats.biggestWin = Math.max(blackjackSupremeGame.sessionStats.biggestWin, totalWinnings);
    
    // Award supreme points
    blackjackSupremeGame.supremePoints += Math.floor(totalWinnings / 10);
    checkVipLevelUp();
    
    // Display results
    document.getElementById('gameResult').innerHTML = results.join('<br>');
    document.getElementById('gameStatus').textContent = `Total winnings: ${totalWinnings} GA`;
    
    updateSupremeStats();
    updateSupremeDisplay();
    
    // Reset for next hand
    setTimeout(() => {
        resetForNextHand();
    }, 5000);
}

function checkSideBets() {
    let totalWinnings = 0;
    let results = [];
    
    const playerHand = blackjackSupremeGame.playerHands[0];
    
    // Perfect Pairs
    if (blackjackSupremeGame.sideBets.perfectPairs.active) {
        const bet = blackjackSupremeGame.sideBets.perfectPairs.amount;
        if (playerHand.length >= 2 && playerHand[0].value === playerHand[1].value) {
            const payout = bet * VIP_PAYOUTS.PERFECT_PAIRS;
            totalWinnings += payout + bet;
            results.push(`Perfect Pairs: +${payout} GA`);
            blackjackSupremeGame.sessionStats.perfectPairs++;
        }
    }
    
    // Lucky Ladies (20 with queens)
    if (blackjackSupremeGame.sideBets.luckyLadies.active) {
        const bet = blackjackSupremeGame.sideBets.luckyLadies.amount;
        const handValue = calculateHandValue(playerHand);
        if (handValue.value === 20 && playerHand.every(card => card.value === 'Q')) {
            const payout = bet * VIP_PAYOUTS.LUCKY_LADIES;
            totalWinnings += payout + bet;
            results.push(`Lucky Ladies: +${payout} GA`);
        }
    }
    
    // Royal Match (suited K-Q)
    if (blackjackSupremeGame.sideBets.royalMatch.active) {
        const bet = blackjackSupremeGame.sideBets.royalMatch.amount;
        if (playerHand.length >= 2 && 
            ((playerHand[0].value === 'K' && playerHand[1].value === 'Q') ||
             (playerHand[0].value === 'Q' && playerHand[1].value === 'K')) &&
            playerHand[0].suit === playerHand[1].suit) {
            const payout = bet * VIP_PAYOUTS.ROYAL_MATCH;
            totalWinnings += payout + bet;
            results.push(`Royal Match: +${payout} GA`);
        }
    }
    
    // Insurance
    if (blackjackSupremeGame.sideBets.insurance.active) {
        const bet = blackjackSupremeGame.sideBets.insurance.amount;
        const dealerValue = calculateHandValue(blackjackSupremeGame.dealerCards);
        if (dealerValue.isBlackjack) {
            const payout = bet * VIP_PAYOUTS.INSURANCE;
            totalWinnings += payout + bet;
            results.push(`Insurance: +${payout} GA`);
            blackjackSupremeGame.sessionStats.insuranceWins++;
        }
    }
    
    return { winnings: totalWinnings, results: results };
}

function endHand(result, winnings) {
    balance += winnings;
    updateBalance();
    
    blackjackSupremeGame.gamePhase = 'complete';
    document.getElementById('gameResult').textContent = result.toUpperCase();
    document.getElementById('gameStatus').textContent = `Winnings: ${winnings} GA`;
    
    // Update stats
    blackjackSupremeGame.sessionStats.handsPlayed++;
    if (winnings > blackjackSupremeGame.betAmount) {
        blackjackSupremeGame.sessionStats.handsWon++;
    }
    blackjackSupremeGame.sessionStats.totalWagered += blackjackSupremeGame.betAmount;
    blackjackSupremeGame.sessionStats.totalWon += winnings;
    blackjackSupremeGame.sessionStats.biggestWin = Math.max(blackjackSupremeGame.sessionStats.biggestWin, winnings);
    
    updateSupremeStats();
    
    setTimeout(() => {
        resetForNextHand();
    }, 3000);
}

function resetForNextHand() {
    blackjackSupremeGame.gamePhase = 'betting';
    blackjackSupremeGame.currentHandIndex = 0;
    blackjackSupremeGame.canSurrender = true;
    blackjackSupremeGame.splitsUsed = 0;
    
    // Reset side bets
    Object.keys(blackjackSupremeGame.sideBets).forEach(key => {
        blackjackSupremeGame.sideBets[key].active = false;
    });
    
    // Clear displays
    document.getElementById('dealerArea').innerHTML = '<div class="card-placeholder">Dealer Cards</div>';
    document.getElementById('playerArea').innerHTML = '<div class="card-placeholder">Your Cards</div>';
    document.getElementById('dealerValue').textContent = '';
    document.getElementById('playerValue').textContent = '';
    document.getElementById('gameResult').textContent = '';
    document.getElementById('gameStatus').textContent = 'Ready for next hand';
    document.getElementById('recommendationText').textContent = 'Place your bet to begin';
    
    // Reset checkboxes
    document.getElementById('perfectPairsCheck').checked = false;
    document.getElementById('luckyLadiesCheck').checked = false;
    document.getElementById('royalMatchCheck').checked = false;
    
    // Enable deal button
    document.getElementById('dealCards').disabled = false;
    
    // Disable action buttons
    document.getElementById('hitButton').disabled = true;
    document.getElementById('standButton').disabled = true;
    document.getElementById('doubleButton').disabled = true;
    document.getElementById('splitButton').disabled = true;
    document.getElementById('surrenderButton').disabled = true;
    
    // Reshuffle if deck is low
    if (blackjackSupremeGame.deck.length < 52) {
        initializeSupremeDeck();
        document.getElementById('gameStatus').textContent = 'Deck reshuffled - Ready for next hand';
    }
}

function checkVipLevelUp() {
    const newLevel = Math.floor(blackjackSupremeGame.supremePoints / 1000) + 1;
    if (newLevel > blackjackSupremeGame.vipLevel) {
        blackjackSupremeGame.vipLevel = newLevel;
        document.getElementById('recommendationText').textContent = `🎉 VIP LEVEL UP! Now Level ${newLevel}!`;
    }
}

function updateSupremeDisplay() {
    document.getElementById('vipLevel').textContent = blackjackSupremeGame.vipLevel;
    document.getElementById('supremePoints').textContent = blackjackSupremeGame.supremePoints.toLocaleString();
    
    if (blackjackSupremeGame.cardCounting) {
        document.getElementById('cardCountDisplay').classList.remove('hidden');
        document.getElementById('runningCount').textContent = blackjackSupremeGame.runningCount;
        const decksRemaining = Math.max(1, Math.floor((312 - blackjackSupremeGame.cardsDealt) / 52));
        blackjackSupremeGame.trueCount = Math.round(blackjackSupremeGame.runningCount / decksRemaining);
        document.getElementById('trueCount').textContent = blackjackSupremeGame.trueCount;
    }
}

function updateSupremeStats() {
    document.getElementById('handsPlayed').textContent = blackjackSupremeGame.sessionStats.handsPlayed;
    document.getElementById('handsWon').textContent = blackjackSupremeGame.sessionStats.handsWon;
    document.getElementById('blackjacks').textContent = blackjackSupremeGame.sessionStats.blackjacks;
    document.getElementById('totalWagered').textContent = blackjackSupremeGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('biggestWin').textContent = blackjackSupremeGame.sessionStats.biggestWin.toLocaleString();
}

function updateStrategyRecommendation() {
    if (!blackjackSupremeGame.basicStrategy || blackjackSupremeGame.gamePhase !== 'playing') {
        return;
    }
    
    const currentHand = blackjackSupremeGame.playerHands[blackjackSupremeGame.currentHandIndex];
    const dealerUpCard = blackjackSupremeGame.dealerCards[0];
    const handValue = calculateHandValue(currentHand);
    
    const recommendation = getBasicStrategyRecommendation(handValue, dealerUpCard, currentHand);
    document.getElementById('recommendationText').textContent = recommendation;
    
    // Update strategy info
    document.getElementById('currentHandInfo').textContent = handValue.display;
    document.getElementById('dealerUpCard').textContent = dealerUpCard.value + dealerUpCard.suit;
    document.getElementById('optimalPlay').textContent = recommendation;
    
    if (blackjackSupremeGame.autoStrategy) {
        executeAutoStrategy(recommendation);
    }
}

function getBasicStrategyRecommendation(handValue, dealerUpCard, hand) {
    const dealerValue = dealerUpCard.value === 'A' ? 11 : 
                       ['K', 'Q', 'J'].includes(dealerUpCard.value) ? 10 : 
                       parseInt(dealerUpCard.value);
    
    // Pairs
    if (hand.length === 2 && hand[0].value === hand[1].value) {
        const pairValue = hand[0].value;
        if (['A', '8'].includes(pairValue)) return 'SPLIT';
        if (pairValue === '10' || ['K', 'Q', 'J'].includes(pairValue)) return 'STAND';
        if (pairValue === '9' && ![7, 10, 11].includes(dealerValue)) return 'SPLIT';
        if (['2', '3', '6', '7'].includes(pairValue) && dealerValue <= 7) return 'SPLIT';
        if (['4', '5'].includes(pairValue)) return 'HIT';
    }
    
    // Soft hands
    if (handValue.isSoft) {
        if (handValue.value >= 19) return 'STAND';
        if (handValue.value === 18) {
            if (dealerValue <= 6) return 'DOUBLE or HIT';
            if ([7, 8].includes(dealerValue)) return 'STAND';
            return 'HIT';
        }
        if (handValue.value <= 17) {
            if (dealerValue <= 6) return 'DOUBLE or HIT';
            return 'HIT';
        }
    }
    
    // Hard hands
    if (handValue.value >= 17) return 'STAND';
    if (handValue.value >= 13 && dealerValue <= 6) return 'STAND';
    if (handValue.value === 12 && [4, 5, 6].includes(dealerValue)) return 'STAND';
    if (handValue.value === 11) return 'DOUBLE or HIT';
    if (handValue.value === 10 && dealerValue <= 9) return 'DOUBLE or HIT';
    if (handValue.value === 9 && [3, 4, 5, 6].includes(dealerValue)) return 'DOUBLE or HIT';
    
    return 'HIT';
}

function executeAutoStrategy(recommendation) {
    if (!blackjackSupremeGame.autoStrategy) return;
    
    setTimeout(() => {
        if (recommendation.includes('HIT')) {
            hitCard();
        } else if (recommendation.includes('STAND')) {
            standHand();
        } else if (recommendation.includes('DOUBLE')) {
            if (!document.getElementById('doubleButton').disabled) {
                doubleDown();
            } else {
                hitCard();
            }
        } else if (recommendation.includes('SPLIT')) {
            if (!document.getElementById('splitButton').disabled) {
                splitHand();
            } else {
                hitCard();
            }
        }
    }, 1500);
}

function toggleStrategyHints() {
    blackjackSupremeGame.basicStrategy = document.getElementById('strategyHints').checked;
}

function toggleCardCounting() {
    blackjackSupremeGame.cardCounting = document.getElementById('cardCounting').checked;
    if (blackjackSupremeGame.cardCounting) {
        document.getElementById('cardCountDisplay').classList.remove('hidden');
    } else {
        document.getElementById('cardCountDisplay').classList.add('hidden');
    }
}

function toggleAutoStrategy() {
    blackjackSupremeGame.autoStrategy = document.getElementById('autoStrategy').checked;
}

function toggleHoleCardPeek() {
    blackjackSupremeGame.holeCardPeek = document.getElementById('holeCardPeek').checked;
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBlackjackSupremeGame();
});