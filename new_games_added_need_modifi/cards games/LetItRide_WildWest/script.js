
// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Let It Ride Wild West Implementation ---

const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

const POKER_HANDS = {
    ROYAL_FLUSH: { name: 'Royal Flush', payout: 1000 },
    STRAIGHT_FLUSH: { name: 'Straight Flush', payout: 200 },
    FOUR_KIND: { name: 'Four of a Kind', payout: 50 },
    FULL_HOUSE: { name: 'Full House', payout: 11 },
    FLUSH: { name: 'Flush', payout: 8 },
    STRAIGHT: { name: 'Straight', payout: 5 },
    THREE_KIND: { name: 'Three of a Kind', payout: 3 },
    TWO_PAIR: { name: 'Two Pair', payout: 2 },
    PAIR_10S: { name: 'Pair of 10s or Better', payout: 1 }
};

let letItRideGame = {
    deck: [],
    playerCards: [],
    communityCards: [],
    bets: [0, 0, 0],
    gamePhase: 'betting', // betting, reveal1, reveal2, complete
    totalWin: 0,
    handResult: null
};

function loadLetItRideGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Betting Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                <h4 class="text-xl font-bold mb-4 text-amber-300 font-mono">SALOON STAKES</h4>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">BET AMOUNT</label>
                    <input type="number" id="letItRideBet" value="25" min="5" max="500" step="5"
                           class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                </div>
                
                <button id="dealCards" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                    DEAL CARDS
                </button>
                
                <div class="space-y-2 text-sm font-mono">
                    <div class="flex justify-between">
                        <span class="text-gray-300">Bet 1:</span>
                        <span id="bet1Amount" class="text-amber-400">$0</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">Bet 2:</span>
                        <span id="bet2Amount" class="text-amber-400">$0</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">Bet 3:</span>
                        <span id="bet3Amount" class="text-amber-400">$0</span>
                    </div>
                    <div class="flex justify-between font-bold">
                        <span class="text-gray-300">Total Win:</span>
                        <span id="totalWin" class="text-green-400">$0</span>
                    </div>
                </div>
                
                <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-amber-500/20">
                    <h5 class="text-sm font-bold mb-2 text-amber-300 font-mono">PAYTABLE</h5>
                    <div class="space-y-1 text-xs">
                        <div class="flex justify-between"><span>Royal Flush:</span><span class="text-yellow-400">1000:1</span></div>
                        <div class="flex justify-between"><span>Straight Flush:</span><span class="text-purple-400">200:1</span></div>
                        <div class="flex justify-between"><span>Four of a Kind:</span><span class="text-red-400">50:1</span></div>
                        <div class="flex justify-between"><span>Full House:</span><span class="text-blue-400">11:1</span></div>
                        <div class="flex justify-between"><span>Flush:</span><span class="text-cyan-400">8:1</span></div>
                        <div class="flex justify-between"><span>Straight:</span><span class="text-green-400">5:1</span></div>
                        <div class="flex justify-between"><span>Three of a Kind:</span><span class="text-orange-400">3:1</span></div>
                        <div class="flex justify-between"><span>Two Pair:</span><span class="text-pink-400">2:1</span></div>
                        <div class="flex justify-between"><span>Pair 10s+:</span><span class="text-gray-400">1:1</span></div>
                    </div>
                </div>
            </div>

            <!-- Game Table -->
            <div class="lg:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <!-- Player Cards -->
                    <div class="mb-6">
                        <h5 class="text-lg font-bold mb-3 text-amber-300 font-mono">YOUR CARDS</h5>
                        <div id="playerCards" class="flex gap-4 justify-center"></div>
                    </div>
                    
                    <!-- Community Cards -->
                    <div class="mb-6">
                        <h5 class="text-lg font-bold mb-3 text-amber-300 font-mono">COMMUNITY CARDS</h5>
                        <div id="communityCards" class="flex gap-4 justify-center"></div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div id="actionButtons" class="flex gap-4 justify-center mb-6"></div>
                    
                    <!-- Game Status -->
                    <div id="letItRideStatus" class="text-center text-lg font-semibold text-amber-300 mb-4 h-8 font-mono">
                        Welcome to the Wild West Saloon
                    </div>
                    
                    <!-- Hand Result -->
                    <div id="handResult" class="text-center text-xl font-bold text-green-400 h-8 font-mono"></div>
                </div>
            </div>
        </div>
    `;
    setupLetItRideGame();
}

function setupLetItRideGame() {
    document.getElementById('dealCards').addEventListener('click', startNewGame);
    updateLetItRideDisplay();
}

function createDeck() {
    const deck = [];
    for (const suit of CARD_SUITS) {
        for (const value of CARD_VALUES) {
            deck.push({ suit, value });
        }
    }
    return shuffleDeck(deck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function createCardElement(card, faceDown = false) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'card bg-white rounded-lg p-4 text-center shadow-lg border-2 border-gray-300 w-20 h-28 flex flex-col justify-between';
    
    if (faceDown) {
        cardDiv.innerHTML = `
            <div class="text-amber-600 text-2xl">🤠</div>
            <div class="text-amber-600 text-xs font-bold">WILD WEST</div>
        `;
        cardDiv.classList.add('bg-amber-100');
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        const color = isRed ? 'text-red-600' : 'text-black';
        cardDiv.innerHTML = `
            <div class="${color} text-lg font-bold">${card.value}</div>
            <div class="${color} text-2xl">${card.suit}</div>
            <div class="${color} text-lg font-bold" style="transform: rotate(180deg)">${card.value}</div>
        `;
    }
    
    return cardDiv;
}

function startNewGame() {
    const betAmount = parseInt(document.getElementById('letItRideBet').value);
    
    if (balance < betAmount * 3) {
        document.getElementById('letItRideStatus').textContent = 'INSUFFICIENT FUNDS FOR THREE BETS';
        return;
    }
    
    // Initialize game
    letItRideGame.deck = createDeck();
    letItRideGame.playerCards = [letItRideGame.deck.pop(), letItRideGame.deck.pop()];
    letItRideGame.communityCards = [letItRideGame.deck.pop(), letItRideGame.deck.pop()];
    letItRideGame.bets = [betAmount, betAmount, betAmount];
    letItRideGame.gamePhase = 'reveal1';
    letItRideGame.totalWin = 0;
    letItRideGame.handResult = null;
    
    balance -= betAmount * 3;
    updateBalance();
    
    // Display cards
    displayPlayerCards();
    displayCommunityCards(1); // Show first community card
    showActionButtons();
    
    document.getElementById('letItRideStatus').textContent = 'First community card revealed. Let it ride or pull back bet 1?';
    updateLetItRideDisplay();
}

function displayPlayerCards() {
    const container = document.getElementById('playerCards');
    container.innerHTML = '';
    
    letItRideGame.playerCards.forEach(card => {
        container.appendChild(createCardElement(card));
    });
}

function displayCommunityCards(count) {
    const container = document.getElementById('communityCards');
    container.innerHTML = '';
    
    for (let i = 0; i < 2; i++) {
        if (i < count) {
            container.appendChild(createCardElement(letItRideGame.communityCards[i]));
        } else {
            container.appendChild(createCardElement(null, true));
        }
    }
}

function showActionButtons() {
    const container = document.getElementById('actionButtons');
    container.innerHTML = '';
    
    if (letItRideGame.gamePhase === 'reveal1') {
        const letItRideBtn = document.createElement('button');
        letItRideBtn.textContent = 'LET IT RIDE';
        letItRideBtn.className = 'cyber-button px-6 py-3 rounded-lg font-semibold text-white';
        letItRideBtn.addEventListener('click', () => proceedToNextPhase());
        
        const pullBackBtn = document.createElement('button');
        pullBackBtn.textContent = 'PULL BACK BET 1';
        pullBackBtn.className = 'bg-red-600 hover:bg-red-700 px-6 py-3 rounded-lg font-semibold text-white';
        pullBackBtn.addEventListener('click', () => pullBackBet(0));
        
        container.appendChild(letItRideBtn);
        container.appendChild(pullBackBtn);
    } else if (letItRideGame.gamePhase === 'reveal2') {
        const letItRideBtn = document.createElement('button');
        letItRideBtn.textContent = 'LET IT RIDE';
        letItRideBtn.className = 'cyber-button px-6 py-3 rounded-lg font-semibold text-white';
        letItRideBtn.addEventListener('click', () => proceedToNextPhase());
        
        const pullBackBtn = document.createElement('button');
        pullBackBtn.textContent = 'PULL BACK BET 2';
        pullBackBtn.className = 'bg-red-600 hover:bg-red-700 px-6 py-3 rounded-lg font-semibold text-white';
        pullBackBtn.addEventListener('click', () => pullBackBet(1));
        
        container.appendChild(letItRideBtn);
        container.appendChild(pullBackBtn);
    }
}

function pullBackBet(betIndex) {
    balance += letItRideGame.bets[betIndex];
    letItRideGame.bets[betIndex] = 0;
    updateBalance();
    proceedToNextPhase();
}

function proceedToNextPhase() {
    if (letItRideGame.gamePhase === 'reveal1') {
        letItRideGame.gamePhase = 'reveal2';
        displayCommunityCards(2); // Show second community card
        showActionButtons();
        document.getElementById('letItRideStatus').textContent = 'Second community card revealed. Let it ride or pull back bet 2?';
    } else if (letItRideGame.gamePhase === 'reveal2') {
        letItRideGame.gamePhase = 'complete';
        evaluateHand();
        document.getElementById('actionButtons').innerHTML = '';
        
        const newGameBtn = document.createElement('button');
        newGameBtn.textContent = 'NEW GAME';
        newGameBtn.className = 'cyber-button px-6 py-3 rounded-lg font-semibold text-white';
        newGameBtn.addEventListener('click', startNewGame);
        document.getElementById('actionButtons').appendChild(newGameBtn);
    }
    updateLetItRideDisplay();
}

function evaluateHand() {
    const allCards = [...letItRideGame.playerCards, ...letItRideGame.communityCards];
    const handRank = getPokerHandRank(allCards);
    
    if (handRank) {
        const totalBet = letItRideGame.bets.reduce((sum, bet) => sum + bet, 0);
        letItRideGame.totalWin = totalBet * handRank.payout;
        balance += letItRideGame.totalWin;
        updateBalance();
        
        document.getElementById('letItRideStatus').textContent = `${handRank.name}! You win $${letItRideGame.totalWin}`;
        document.getElementById('handResult').textContent = handRank.name;
    } else {
        document.getElementById('letItRideStatus').textContent = 'No winning hand. Better luck next time!';
        document.getElementById('handResult').textContent = 'No Win';
    }
}

function getPokerHandRank(cards) {
    const values = cards.map(card => {
        if (card.value === 'A') return 14;
        if (card.value === 'K') return 13;
        if (card.value === 'Q') return 12;
        if (card.value === 'J') return 11;
        return parseInt(card.value);
    }).sort((a, b) => b - a);
    
    const suits = cards.map(card => card.suit);
    const valueCounts = {};
    values.forEach(value => valueCounts[value] = (valueCounts[value] || 0) + 1);
    
    const isFlush = suits.every(suit => suit === suits[0]);
    const isStraight = values.every((value, index) => index === 0 || value === values[index - 1] - 1);
    const isRoyalStraight = values.join(',') === '14,13,12,11,10';
    
    // Check for poker hands
    if (isFlush && isRoyalStraight) return POKER_HANDS.ROYAL_FLUSH;
    if (isFlush && isStraight) return POKER_HANDS.STRAIGHT_FLUSH;
    if (Object.values(valueCounts).includes(4)) return POKER_HANDS.FOUR_KIND;
    if (Object.values(valueCounts).includes(3) && Object.values(valueCounts).includes(2)) return POKER_HANDS.FULL_HOUSE;
    if (isFlush) return POKER_HANDS.FLUSH;
    if (isStraight) return POKER_HANDS.STRAIGHT;
    if (Object.values(valueCounts).includes(3)) return POKER_HANDS.THREE_KIND;
    
    const pairs = Object.keys(valueCounts).filter(value => valueCounts[value] === 2);
    if (pairs.length === 2) return POKER_HANDS.TWO_PAIR;
    if (pairs.length === 1 && parseInt(pairs[0]) >= 10) return POKER_HANDS.PAIR_10S;
    
    return null;
}

function updateLetItRideDisplay() {
    document.getElementById('bet1Amount').textContent = `$${letItRideGame.bets[0]}`;
    document.getElementById('bet2Amount').textContent = `$${letItRideGame.bets[1]}`;
    document.getElementById('bet3Amount').textContent = `$${letItRideGame.bets[2]}`;
    document.getElementById('totalWin').textContent = `$${letItRideGame.totalWin}`;
    
    const dealButton = document.getElementById('dealCards');
    dealButton.disabled = letItRideGame.gamePhase !== 'betting';
    dealButton.textContent = letItRideGame.gamePhase === 'betting' ? 'DEAL CARDS' : 'GAME IN PROGRESS';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadLetItRideGame();
});

