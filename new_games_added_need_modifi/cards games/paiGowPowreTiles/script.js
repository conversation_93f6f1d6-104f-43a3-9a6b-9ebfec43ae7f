// Pai <PERSON>w Power Tiles - Cyberpunk variant with extremely low win rate (<10%)
let paiGowPowerGame = {
    playerTiles: [],
    dealerTiles: [],
    playerHands: { high: [], low: [] },
    dealerHands: { high: [], low: [] },
    betAmount: 0,
    powerChips: 0,
    gamePhase: 'betting', // betting, arranging, revealing, complete
    gameResult: null,
    totalWin: 0,
    difficulty: 'normal', // easy, normal, hard, neon
    gameMode: 'standard', // standard, power, ultra
    commission: 0.05, // 5% commission on wins
    powerBoost: false, // Power boost active
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        handsPushed: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        commissionsLost: 0,
        powerBoostsUsed: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Pai Gow Power Tiles set (32 tiles with cyberpunk theme)
const POWER_TILES = [
    // Supreme tiles (highest ranking)
    { name: 'Quantum Pair (Red)', dots: [6, 6], rank: 1, value: 12, isSupreme: true, powerLevel: 5 },
    { name: 'Quantum Pair (Blue)', dots: [3, 3], rank: 1, value: 6, isSupreme: true, powerLevel: 5 },
    
    // High-tier tiles
    { name: 'Neon Teen', dots: [6, 6], rank: 2, value: 12, powerLevel: 4 },
    { name: 'Cyber Day', dots: [1, 1], rank: 3, value: 2, powerLevel: 4 },
    
    // Mid-tier tiles
    { name: 'Yun (Red)', dots: [4, 4], rank: 4, value: 8, powerLevel: 3 },
    { name: 'Yun (Blue)', dots: [1, 3], rank: 4, value: 4, powerLevel: 3 },
    { name: 'Gor (Red)', dots: [1, 1], rank: 5, value: 2, powerLevel: 3 },
    { name: 'Gor (Blue)', dots: [6, 6], rank: 5, value: 12, powerLevel: 3 },
    { name: 'Mooy (Red)', dots: [1, 5], rank: 6, value: 6, powerLevel: 3 },
    { name: 'Mooy (Blue)', dots: [2, 4], rank: 6, value: 6, powerLevel: 3 },
    
    // Standard tiles
    { name: 'Chong (Red)', dots: [5, 5], rank: 7, value: 10, powerLevel: 2 },
    { name: 'Chong (Blue)', dots: [3, 3], rank: 7, value: 6, powerLevel: 2 },
    { name: 'Bon (Red)', dots: [4, 4], rank: 8, value: 8, powerLevel: 2 },
    { name: 'Bon (Blue)', dots: [2, 2], rank: 8, value: 4, powerLevel: 2 },
    { name: 'Foo (Red)', dots: [6, 1], rank: 9, value: 7, powerLevel: 2 },
    { name: 'Foo (Blue)', dots: [5, 1], rank: 9, value: 6, powerLevel: 2 },
    
    // Low-tier tiles
    { name: 'Ping (Red)', dots: [4, 2], rank: 10, value: 6, powerLevel: 1 },
    { name: 'Ping (Blue)', dots: [3, 1], rank: 10, value: 4, powerLevel: 1 },
    { name: 'Tit (Red)', dots: [5, 5], rank: 11, value: 10, powerLevel: 1 },
    { name: 'Tit (Blue)', dots: [4, 2], rank: 11, value: 6, powerLevel: 1 },
    { name: 'Look (Red)', dots: [6, 4], rank: 12, value: 10, powerLevel: 1 },
    { name: 'Look (Blue)', dots: [1, 1], rank: 12, value: 2, powerLevel: 1 },
    
    // Glitch tiles (lowest ranking)
    { name: 'Glitch 1', dots: [2, 1], rank: 13, value: 3, powerLevel: 0 },
    { name: 'Glitch 2', dots: [4, 1], rank: 13, value: 5, powerLevel: 0 },
    { name: 'Glitch 3', dots: [3, 2], rank: 13, value: 5, powerLevel: 0 },
    { name: 'Glitch 4', dots: [5, 2], rank: 13, value: 7, powerLevel: 0 },
    { name: 'Glitch 5', dots: [6, 2], rank: 13, value: 8, powerLevel: 0 },
    { name: 'Glitch 6', dots: [3, 1], rank: 13, value: 4, powerLevel: 0 },
    { name: 'Glitch 7', dots: [5, 3], rank: 13, value: 8, powerLevel: 0 },
    { name: 'Glitch 8', dots: [6, 3], rank: 13, value: 9, powerLevel: 0 }
];

// Game modes with heavy house advantage
const POWER_MODES = {
    standard: { name: 'Standard', houseEdge: 0.25, tileBias: 0.20, commission: 0.05, powerMultiplier: 1.0 },
    power: { name: 'Power', houseEdge: 0.35, tileBias: 0.30, commission: 0.07, powerMultiplier: 1.5 },
    ultra: { name: 'Ultra', houseEdge: 0.45, tileBias: 0.40, commission: 0.10, powerMultiplier: 2.0 }
};

const POWER_DIFFICULTIES = {
    easy: { name: 'Easy', payoutReduction: 0.20, dealerAdvantage: 0.15, powerPenalty: 0.10 },
    normal: { name: 'Normal', payoutReduction: 0.30, dealerAdvantage: 0.25, powerPenalty: 0.20 },
    hard: { name: 'Hard', payoutReduction: 0.40, dealerAdvantage: 0.35, powerPenalty: 0.30 },
    neon: { name: 'Neon Master', payoutReduction: 0.50, dealerAdvantage: 0.45, powerPenalty: 0.40 }
};

// Extremely low payout structure
const POWER_PAYOUTS = {
    WIN: 0.80,      // Reduced from 1:1 (minus commission)
    PUSH: 0,        // No payout for ties
    LOSS: 0,        // Lose entire bet
    POWER_BOOST: 1.2 // 20% boost when power is activated (but still low)
};

function loadPaiGowPowerGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Betting Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                <h4 class="text-xl font-bold mb-4 text-purple-300 font-mono">POWER STAKES</h4>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">BET AMOUNT</label>
                    <input type="number" id="powerBet" value="25" min="5" max="500" step="5"
                           class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">GAME MODE</label>
                    <select id="powerMode" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                        <option value="standard">Standard (5% Commission)</option>
                        <option value="power">Power (7% Commission)</option>
                        <option value="ultra">Ultra (10% Commission)</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">DIFFICULTY</label>
                    <select id="powerDifficulty" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                        <option value="easy">Easy</option>
                        <option value="normal">Normal</option>
                        <option value="hard">Hard</option>
                        <option value="neon">Neon Master</option>
                    </select>
                </div>
                
                <button id="dealPowerTiles" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                    DEAL TILES
                </button>
                
                <div class="space-y-2">
                    <button id="autoArrangePower" class="w-full bg-blue-600 hover:bg-blue-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        AUTO ARRANGE
                    </button>
                    <button id="activatePower" class="w-full bg-purple-600 hover:bg-purple-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        ACTIVATE POWER (0)
                    </button>
                    <button id="submitPowerHands" class="w-full bg-green-600 hover:bg-green-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        SUBMIT HANDS
                    </button>
                </div>
                
                <div class="mt-4 p-3 bg-purple-900/30 rounded-lg border border-purple-500/30">
                    <p class="text-purple-300 font-mono text-sm">Commission: <span id="powerCommissionRate">5%</span></p>
                    <p class="text-white">Current Bet: <span id="currentPowerBet">0</span> GA</p>
                    <p class="text-white">Power Chips: <span id="powerChips">0</span></p>
                    <p class="text-gray-300 text-xs">*Commission deducted from wins</p>
                </div>
            </div>
            
            <!-- Game Table -->
            <div class="col-span-2 bg-black/30 p-6 rounded-xl border border-purple-500/30">
                <h4 class="text-xl font-bold mb-4 text-purple-300 font-mono text-center">POWER TILES TABLE</h4>
                
                <!-- Dealer Hands -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Dealer Hands</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-black/50 p-3 rounded border border-purple-500/30">
                            <p class="text-sm text-gray-400 mb-2">High Hand</p>
                            <div id="dealerHighPower" class="flex gap-1 justify-center min-h-[60px]">
                                <div class="tile-back">?</div>
                                <div class="tile-back">?</div>
                            </div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-purple-500/30">
                            <p class="text-sm text-gray-400 mb-2">Low Hand</p>
                            <div id="dealerLowPower" class="flex gap-1 justify-center min-h-[60px]">
                                <div class="tile-back">?</div>
                                <div class="tile-back">?</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Player Tiles -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Your Tiles</h5>
                    <div id="playerPowerTiles" class="flex gap-2 justify-center flex-wrap min-h-[80px]">
                        <!-- Tiles will be populated here -->
                    </div>
                </div>
                
                <!-- Player Hands -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Your Hands</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-black/50 p-3 rounded border border-purple-500/30">
                            <p class="text-sm text-gray-400 mb-2">High Hand (2 tiles)</p>
                            <div id="playerHighPower" class="flex gap-1 justify-center min-h-[60px] drop-zone">
                                <!-- Drop zone for high hand -->
                            </div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-purple-500/30">
                            <p class="text-sm text-gray-400 mb-2">Low Hand (2 tiles)</p>
                            <div id="playerLowPower" class="flex gap-1 justify-center min-h-[60px] drop-zone">
                                <!-- Drop zone for low hand -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Game Status -->
                <div class="text-center">
                    <div id="powerGameStatus" class="text-lg font-semibold text-purple-300 mb-2">Place your bet to start</div>
                    <div id="powerGameResult" class="text-center text-xl font-bold h-8 font-mono"></div>
                </div>
            </div>
            
            <!-- Stats Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                <h4 class="text-xl font-bold mb-4 text-purple-300 font-mono">POWER STATS</h4>
                
                <div class="space-y-3">
                    <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                        <p class="text-gray-300 text-sm">Hands Played: <span id="powerHandsPlayed" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                        <p class="text-gray-300 text-sm">Win Rate: <span id="powerWinRate" class="text-white">0%</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                        <p class="text-gray-300 text-sm">Push Rate: <span id="powerPushRate" class="text-white">0%</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                        <p class="text-gray-300 text-sm">Total Wagered: <span id="powerTotalWagered" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                        <p class="text-gray-300 text-sm">Total Won: <span id="powerTotalWon" class="text-white">0</span></p>
                    </div>
                    <div class="bg-purple-900/50 p-2 rounded border border-purple-500/30">
                        <p class="text-purple-300 text-sm">Commissions Lost: <span id="powerCommissions" class="text-white">0</span></p>
                    </div>
                    <div class="bg-purple-900/50 p-2 rounded border border-purple-500/30">
                        <p class="text-purple-300 text-sm">Power Boosts Used: <span id="powerBoostsUsed" class="text-white">0</span></p>
                    </div>
                    <div class="bg-red-900/50 p-2 rounded border border-red-500/30">
                        <p class="text-red-300 text-sm">Loss Streak: <span id="powerLossStreak" class="text-white">0</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 bg-black/30 p-4 rounded-xl border border-purple-500/30">
            <h4 class="text-xl font-bold mb-4 text-purple-300 font-mono">POWER TILE RANKINGS</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                    <p class="text-purple-400">1. Quantum Pair (Supreme)</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                    <p class="text-purple-400">2. Neon Teen (12 dots)</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                    <p class="text-purple-400">3. Cyber Day (2 dots)</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                    <p class="text-purple-400">4-12. Ranked Pairs</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-purple-500/30">
                    <p class="text-purple-400">13. Glitch Tiles (Mixed)</p>
                </div>
            </div>
            <p class="text-purple-400 text-xs mt-2">* High hand must outrank low hand</p>
            <p class="text-purple-400 text-xs">* Commission deducted from all wins</p>
            <p class="text-purple-400 text-xs">* Power Boost increases payout but is rarely effective</p>
        </div>
    `;
    setupPaiGowPowerGame();
}

function setupPaiGowPowerGame() {
    document.getElementById('dealPowerTiles').addEventListener('click', startNewPowerGame);
    document.getElementById('autoArrangePower').addEventListener('click', autoArrangePowerTiles);
    document.getElementById('activatePower').addEventListener('click', activatePowerBoost);
    document.getElementById('submitPowerHands').addEventListener('click', submitPowerHands);
    
    document.getElementById('powerMode').addEventListener('change', function() {
        paiGowPowerGame.gameMode = this.value;
        updatePowerCommissionDisplay();
        updatePowerDisplay();
    });
    
    document.getElementById('powerDifficulty').addEventListener('change', function() {
        paiGowPowerGame.difficulty = this.value;
        updatePowerDisplay();
    });
    
    updatePowerCommissionDisplay();
    updatePowerDisplay();
}

function createBiasedPowerTileSet() {
    const modeData = POWER_MODES[paiGowPowerGame.gameMode];
    const difficultyData = POWER_DIFFICULTIES[paiGowPowerGame.difficulty];
    
    // Create heavily biased tile set against player
    const biasedTiles = [];
    
    POWER_TILES.forEach(tile => {
        let tileWeight = 1;
        
        // Reduce high-ranking tiles for player
        if (tile.rank <= 5) {
            tileWeight *= (1 - modeData.tileBias);
        }
        
        // Reduce supreme tiles significantly
        if (tile.isSupreme) {
            tileWeight *= (1 - difficultyData.dealerAdvantage);
        }
        
        // Reduce high power level tiles
        if (tile.powerLevel >= 3) {
            tileWeight *= (1 - difficultyData.powerPenalty);
        }
        
        // Add tiles based on weight
        const tileCount = Math.max(1, Math.floor(tileWeight * 8));
        for (let i = 0; i < tileCount; i++) {
            biasedTiles.push({ ...tile, id: `${tile.name}_${i}` });
        }
    });
    
    return shufflePowerTiles(biasedTiles);
}

function shufflePowerTiles(tiles) {
    for (let i = tiles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [tiles[i], tiles[j]] = [tiles[j], tiles[i]];
    }
    return tiles;
}

function startNewPowerGame() {
    const betAmount = parseInt(document.getElementById('powerBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Initialize game
    const tileSet = createBiasedPowerTileSet();
    paiGowPowerGame.playerTiles = tileSet.slice(0, 4);
    paiGowPowerGame.dealerTiles = tileSet.slice(4, 8);
    paiGowPowerGame.playerHands = { high: [], low: [] };
    paiGowPowerGame.dealerHands = { high: [], low: [] };
    paiGowPowerGame.betAmount = betAmount;
    paiGowPowerGame.gamePhase = 'arranging';
    paiGowPowerGame.gameResult = null;
    paiGowPowerGame.totalWin = 0;
    paiGowPowerGame.powerBoost = false;
    
    paiGowPowerGame.stats.totalWagered += betAmount;
    
    // Display player tiles
    displayPowerTiles();
    
    // Arrange dealer hands optimally (with bias)
    arrangeDealerPowerHands();
    
    // Update display
    updatePowerDisplay();
    updatePowerGameStatus('Arrange your tiles into two hands. High hand must be stronger than low hand.');
    
    // Enable arrangement buttons
    document.getElementById('autoArrangePower').disabled = false;
    document.getElementById('submitPowerHands').disabled = false;
    document.getElementById('dealPowerTiles').disabled = true;
    
    // Enable power boost if player has power chips
    document.getElementById('activatePower').disabled = paiGowPowerGame.powerChips <= 0;
    document.getElementById('activatePower').textContent = `ACTIVATE POWER (${paiGowPowerGame.powerChips})`;
}

function displayPowerTiles() {
    const tilesEl = document.getElementById('playerPowerTiles');
    tilesEl.innerHTML = '';
    
    paiGowPowerGame.playerTiles.forEach((tile, index) => {
        const tileEl = document.createElement('div');
        tileEl.className = `power-tile draggable power-level-${tile.powerLevel}`;
        tileEl.draggable = true;
        tileEl.dataset.tileIndex = index;
        tileEl.innerHTML = `
            <div class="tile-content">
                <div class="tile-name">${tile.name}</div>
                <div class="tile-dots">
                    <span class="dot-group">${'●'.repeat(tile.dots[0])}</span>
                    <span class="dot-group">${'●'.repeat(tile.dots[1])}</span>
                </div>
                <div class="tile-value">${tile.value}</div>
                <div class="power-level">P${tile.powerLevel}</div>
            </div>
        `;
        
        // Add drag event listeners
        tileEl.addEventListener('dragstart', handlePowerTileDragStart);
        tileEl.addEventListener('click', handlePowerTileClick);
        
        tilesEl.appendChild(tileEl);
    });
    
    setupPowerDropZones();
}

function setupPowerDropZones() {
    const dropZones = document.querySelectorAll('.drop-zone');
    
    dropZones.forEach(zone => {
        zone.addEventListener('dragover', handlePowerDragOver);
        zone.addEventListener('drop', handlePowerDrop);
    });
}

function handlePowerTileDragStart(e) {
    e.dataTransfer.setData('text/plain', e.target.dataset.tileIndex);
}

function handlePowerTileClick(e) {
    const tileIndex = parseInt(e.target.closest('.power-tile').dataset.tileIndex);
    const tile = paiGowPowerGame.playerTiles[tileIndex];
    
    // Auto-place in appropriate hand
    if (paiGowPowerGame.playerHands.high.length < 2) {
        placeTileInPowerHand(tile, 'high');
    } else if (paiGowPowerGame.playerHands.low.length < 2) {
        placeTileInPowerHand(tile, 'low');
    }
    
    updatePowerHandDisplays();
}

function handlePowerDragOver(e) {
    e.preventDefault();
}

function handlePowerDrop(e) {
    e.preventDefault();
    const tileIndex = parseInt(e.dataTransfer.getData('text/plain'));
    const tile = paiGowPowerGame.playerTiles[tileIndex];
    const handType = e.target.closest('#playerHighPower') ? 'high' : 'low';
    
    placeTileInPowerHand(tile, handType);
    updatePowerHandDisplays();
}

function placeTileInPowerHand(tile, handType) {
    const hand = paiGowPowerGame.playerHands[handType];
    
    if (hand.length < 2) {
        hand.push(tile);
        
        // Remove from available tiles
        const index = paiGowPowerGame.playerTiles.indexOf(tile);
        if (index > -1) {
            paiGowPowerGame.playerTiles.splice(index, 1);
        }
        
        displayPowerTiles();
    }
}

function updatePowerHandDisplays() {
    // Update high hand display
    const highHandEl = document.getElementById('playerHighPower');
    highHandEl.innerHTML = '';
    paiGowPowerGame.playerHands.high.forEach(tile => {
        const tileEl = createPowerTileElement(tile);
        tileEl.addEventListener('click', () => removeTileFromPowerHand(tile, 'high'));
        highHandEl.appendChild(tileEl);
    });
    
    // Update low hand display
    const lowHandEl = document.getElementById('playerLowPower');
    lowHandEl.innerHTML = '';
    paiGowPowerGame.playerHands.low.forEach(tile => {
        const tileEl = createPowerTileElement(tile);
        tileEl.addEventListener('click', () => removeTileFromPowerHand(tile, 'low'));
        lowHandEl.appendChild(tileEl);
    });
}

function removeTileFromPowerHand(tile, handType) {
    const hand = paiGowPowerGame.playerHands[handType];
    const index = hand.indexOf(tile);
    
    if (index > -1) {
        hand.splice(index, 1);
        paiGowPowerGame.playerTiles.push(tile);
        displayPowerTiles();
        updatePowerHandDisplays();
    }
}

function createPowerTileElement(tile) {
    const tileEl = document.createElement('div');
    tileEl.className = `power-tile small power-level-${tile.powerLevel}`;
    tileEl.innerHTML = `
        <div class="tile-content">
            <div class="tile-name">${tile.name}</div>
            <div class="tile-dots">
                <span class="dot-group">${'●'.repeat(tile.dots[0])}</span>
                <span class="dot-group">${'●'.repeat(tile.dots[1])}</span>
            </div>
            <div class="tile-value">${tile.value}</div>
            <div class="power-level">P${tile.powerLevel}</div>
        </div>
    `;
    return tileEl;
}

function autoArrangePowerTiles() {
    // Reset hands
    paiGowPowerGame.playerHands = { high: [], low: [] };
    paiGowPowerGame.playerTiles = [...paiGowPowerGame.playerTiles, ...paiGowPowerGame.playerHands.high, ...paiGowPowerGame.playerHands.low];
    
    // Sort tiles by rank (ascending for suboptimal arrangement)
    const sortedTiles = [...paiGowPowerGame.playerTiles].sort((a, b) => b.rank - a.rank);
    
    // Deliberately suboptimal arrangement to favor house
    paiGowPowerGame.playerHands.high = [sortedTiles[0], sortedTiles[3]];
    paiGowPowerGame.playerHands.low = [sortedTiles[1], sortedTiles[2]];
        paiGowPowerGame.playerTiles = [];
    
    updatePowerHandDisplays();
    displayPowerTiles();
}

function activatePowerBoost() {
    if (paiGowPowerGame.powerChips > 0) {
        paiGowPowerGame.powerChips--;
        paiGowPowerGame.powerBoost = true;
        paiGowPowerGame.stats.powerBoostsUsed++;
        
        document.getElementById('activatePower').disabled = true;
        document.getElementById('activatePower').textContent = 'POWER ACTIVATED!';
        
        updatePowerDisplay();
        updatePowerGameStatus('Power boost activated! Slight payout increase if you win.');
    }
}

function arrangeDealerPowerHands() {
    // Dealer gets optimal arrangement with bias
    const dealerTiles = [...paiGowPowerGame.dealerTiles];
    
    // Apply dealer advantage - boost dealer tiles
    const difficultyData = POWER_DIFFICULTIES[paiGowPowerGame.difficulty];
    dealerTiles.forEach(tile => {
        // Boost dealer tile ranks artificially
        tile.effectiveRank = Math.max(1, tile.rank - Math.floor(difficultyData.dealerAdvantage * 5));
        tile.effectiveValue = tile.value + Math.floor(difficultyData.dealerAdvantage * 3);
    });
    
    // Sort by effective rank for optimal arrangement
    dealerTiles.sort((a, b) => a.effectiveRank - b.effectiveRank);
    
    // Optimal dealer arrangement
    paiGowPowerGame.dealerHands.high = [dealerTiles[0], dealerTiles[1]];
    paiGowPowerGame.dealerHands.low = [dealerTiles[2], dealerTiles[3]];
}

function submitPowerHands() {
    if (paiGowPowerGame.playerHands.high.length !== 2 || paiGowPowerGame.playerHands.low.length !== 2) {
        alert('You must arrange all 4 tiles into two hands of 2 tiles each.');
        return;
    }
    
    // Validate hand arrangement (high must be stronger than low)
    const highValue = calculatePowerHandValue(paiGowPowerGame.playerHands.high);
    const lowValue = calculatePowerHandValue(paiGowPowerGame.playerHands.low);
    
    if (highValue <= lowValue) {
        alert('Invalid arrangement! High hand must be stronger than low hand.');
        return;
    }
    
    paiGowPowerGame.gamePhase = 'revealing';
    
    // Reveal dealer hands
    revealDealerPowerHands();
    
    // Compare hands and determine result
    comparePowerHands();
    
    // Disable buttons
    document.getElementById('autoArrangePower').disabled = true;
    document.getElementById('submitPowerHands').disabled = true;
    document.getElementById('activatePower').disabled = true;
    
    setTimeout(() => {
        document.getElementById('dealPowerTiles').disabled = false;
        paiGowPowerGame.gamePhase = 'complete';
    }, 3000);
}

function revealDealerPowerHands() {
    // Display dealer high hand
    const dealerHighEl = document.getElementById('dealerHighPower');
    dealerHighEl.innerHTML = '';
    paiGowPowerGame.dealerHands.high.forEach(tile => {
        dealerHighEl.appendChild(createPowerTileElement(tile));
    });
    
    // Display dealer low hand
    const dealerLowEl = document.getElementById('dealerLowPower');
    dealerLowEl.innerHTML = '';
    paiGowPowerGame.dealerHands.low.forEach(tile => {
        dealerLowEl.appendChild(createPowerTileElement(tile));
    });
}

function calculatePowerHandValue(hand) {
    if (hand.length !== 2) return 0;
    
    const tile1 = hand[0];
    const tile2 = hand[1];
    
    // Check for pairs (same rank)
    if (tile1.rank === tile2.rank) {
        return 1000 + tile1.rank; // Pairs rank higher
    }
    
    // Check for special combinations
    const totalDots = (tile1.dots[0] + tile1.dots[1] + tile2.dots[0] + tile2.dots[1]) % 10;
    const rankSum = tile1.rank + tile2.rank;
    
    // Supreme combinations
    if ((tile1.isSupreme && tile2.isSupreme) || 
        (tile1.name.includes('Quantum') && tile2.name.includes('Quantum'))) {
        return 2000 + totalDots;
    }
    
    // High power combinations
    const powerSum = tile1.powerLevel + tile2.powerLevel;
    if (powerSum >= 8) {
        return 500 + totalDots + powerSum;
    }
    
    // Standard value calculation
    return totalDots + (rankSum * 0.1);
}

function comparePowerHands() {
    const playerHighValue = calculatePowerHandValue(paiGowPowerGame.playerHands.high);
    const playerLowValue = calculatePowerHandValue(paiGowPowerGame.playerHands.low);
    const dealerHighValue = calculatePowerHandValue(paiGowPowerGame.dealerHands.high);
    const dealerLowValue = calculatePowerHandValue(paiGowPowerGame.dealerHands.low);
    
    let playerWins = 0;
    let dealerWins = 0;
    
    // Apply heavy house bias to comparisons
    const modeData = POWER_MODES[paiGowPowerGame.gameMode];
    const difficultyData = POWER_DIFFICULTIES[paiGowPowerGame.difficulty];
    
    // Bias factor heavily favors dealer
    const biasFactor = 1 + modeData.houseEdge + difficultyData.dealerAdvantage;
    
    // Compare high hands (with bias)
    if (playerHighValue > dealerHighValue * biasFactor) {
        playerWins++;
    } else if (dealerHighValue * biasFactor >= playerHighValue) {
        dealerWins++;
    }
    
    // Compare low hands (with bias)
    if (playerLowValue > dealerLowValue * biasFactor) {
        playerWins++;
    } else if (dealerLowValue * biasFactor >= playerLowValue) {
        dealerWins++;
    }
    
    // Determine result with additional house edge
    const houseEdgeRoll = Math.random();
    
    if (playerWins === 2 && houseEdgeRoll > modeData.houseEdge) {
        paiGowPowerGame.gameResult = 'win';
        calculatePowerWinnings();
        updatePowerGameStatus('Rare victory! You win both hands!');
        
        // Award power chips occasionally
        if (Math.random() < 0.1) {
            paiGowPowerGame.powerChips++;
        }
    } else if (dealerWins === 2 || houseEdgeRoll <= modeData.houseEdge) {
        paiGowPowerGame.gameResult = 'loss';
        paiGowPowerGame.totalWin = 0;
        updatePowerGameStatus('Dealer wins both hands. You lose.');
    } else {
        // Even ties favor the house slightly
        if (Math.random() < 0.3) {
            paiGowPowerGame.gameResult = 'loss';
            paiGowPowerGame.totalWin = 0;
            updatePowerGameStatus('Split hands - House wins on ties.');
        } else {
            paiGowPowerGame.gameResult = 'push';
            paiGowPowerGame.totalWin = paiGowPowerGame.betAmount; // Return bet
            balance += paiGowPowerGame.betAmount;
            updateBalance();
            updatePowerGameStatus('Split hands - Push. Bet returned.');
        }
    }
    
    updatePowerGameStats();
    updatePowerDisplay();
}

function calculatePowerWinnings() {
    const modeData = POWER_MODES[paiGowPowerGame.gameMode];
    const difficultyData = POWER_DIFFICULTIES[paiGowPowerGame.difficulty];
    
    // Base payout (already reduced)
    let winnings = paiGowPowerGame.betAmount * POWER_PAYOUTS.WIN;
    
    // Apply power boost if active (minimal effect)
    if (paiGowPowerGame.powerBoost) {
        winnings *= POWER_PAYOUTS.POWER_BOOST;
    }
    
    // Apply difficulty penalty
    winnings *= (1 - difficultyData.payoutReduction);
    
    // Deduct commission
    const commission = winnings * modeData.commission;
    winnings -= commission;
    
    // Additional house edge reduction
    winnings *= (1 - modeData.houseEdge * 0.5);
    
    paiGowPowerGame.totalWin = Math.floor(Math.max(1, winnings));
    paiGowPowerGame.stats.commissionsLost += Math.floor(commission);
    
    balance += paiGowPowerGame.betAmount + paiGowPowerGame.totalWin; // Return bet + winnings
    updateBalance();
}

function updatePowerGameStats() {
    paiGowPowerGame.stats.handsPlayed++;
    
    if (paiGowPowerGame.gameResult === 'win') {
        paiGowPowerGame.stats.handsWon++;
        paiGowPowerGame.stats.totalWon += paiGowPowerGame.totalWin;
        
        if (paiGowPowerGame.totalWin > paiGowPowerGame.stats.biggestWin) {
            paiGowPowerGame.stats.biggestWin = paiGowPowerGame.totalWin;
        }
        
        // Update win streak
        paiGowPowerGame.streakData.currentWinStreak++;
        paiGowPowerGame.streakData.currentLossStreak = 0;
        
        if (paiGowPowerGame.streakData.currentWinStreak > paiGowPowerGame.streakData.longestWinStreak) {
            paiGowPowerGame.streakData.longestWinStreak = paiGowPowerGame.streakData.currentWinStreak;
        }
    } else if (paiGowPowerGame.gameResult === 'push') {
        paiGowPowerGame.stats.handsPushed++;
        paiGowPowerGame.streakData.currentWinStreak = 0;
        paiGowPowerGame.streakData.currentLossStreak = 0;
    } else {
        // Update loss streak
        paiGowPowerGame.streakData.currentLossStreak++;
        paiGowPowerGame.streakData.currentWinStreak = 0;
        
        if (paiGowPowerGame.streakData.currentLossStreak > paiGowPowerGame.streakData.longestLossStreak) {
            paiGowPowerGame.streakData.longestLossStreak = paiGowPowerGame.streakData.currentLossStreak;
        }
    }
}

function updatePowerCommissionDisplay() {
    const modeData = POWER_MODES[paiGowPowerGame.gameMode];
    document.getElementById('powerCommissionRate').textContent = `${(modeData.commission * 100).toFixed(0)}%`;
}

function updatePowerDisplay() {
    document.getElementById('currentPowerBet').textContent = paiGowPowerGame.betAmount;
    document.getElementById('powerChips').textContent = paiGowPowerGame.powerChips;
    
    // Update game result
    if (paiGowPowerGame.gameResult) {
        const resultEl = document.getElementById('powerGameResult');
        if (paiGowPowerGame.gameResult === 'win') {
            resultEl.innerHTML = `POWER WIN! +${paiGowPowerGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono';
        } else if (paiGowPowerGame.gameResult === 'push') {
            resultEl.innerHTML = `PUSH - Bet Returned`;
            resultEl.className = 'text-center text-xl font-bold text-yellow-400 h-8 font-mono';
        } else {
            resultEl.innerHTML = `POWER DRAIN - Lost ${paiGowPowerGame.betAmount} GA`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    document.getElementById('powerHandsPlayed').textContent = paiGowPowerGame.stats.handsPlayed;
    
    const winRate = paiGowPowerGame.stats.handsPlayed > 0 ? 
        ((paiGowPowerGame.stats.handsWon / paiGowPowerGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('powerWinRate').textContent = `${winRate}%`;
    
    const pushRate = paiGowPowerGame.stats.handsPlayed > 0 ? 
        ((paiGowPowerGame.stats.handsPushed / paiGowPowerGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('powerPushRate').textContent = `${pushRate}%`;
    
    document.getElementById('powerTotalWagered').textContent = paiGowPowerGame.stats.totalWagered.toLocaleString();
    document.getElementById('powerTotalWon').textContent = paiGowPowerGame.stats.totalWon.toLocaleString();
    document.getElementById('powerCommissions').textContent = paiGowPowerGame.stats.commissionsLost.toLocaleString();
    document.getElementById('powerBoostsUsed').textContent = paiGowPowerGame.stats.powerBoostsUsed;
    document.getElementById('powerLossStreak').textContent = paiGowPowerGame.streakData.currentLossStreak;
}

function updatePowerGameStatus(message) {
    document.getElementById('powerGameStatus').textContent = message;
}

// CSS styles for power tiles
const powerTileStyles = `
<style>
.power-tile {
    width: 80px;
    height: 100px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #8b5cf6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.power-tile.small {
    width: 60px;
    height: 75px;
}

.power-tile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.power-tile.draggable:hover {
    border-color: #a78bfa;
}

.tile-content {
    text-align: center;
    color: white;
    font-size: 10px;
    line-height: 1.2;
}

.tile-name {
    font-weight: bold;
    margin-bottom: 2px;
    font-size: 8px;
}

.tile-dots {
    margin: 2px 0;
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.dot-group {
    font-size: 12px;
    color: #fbbf24;
}

.tile-value {
    font-weight: bold;
    color: #10b981;
    margin-top: 2px;
}

.power-level {
    position: absolute;
    top: 2px;
    right: 2px;
    background: rgba(139, 92, 246, 0.8);
    color: white;
    font-size: 8px;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: bold;
}

.power-level-0 { border-color: #6b7280; }
.power-level-1 { border-color: #3b82f6; }
.power-level-2 { border-color: #10b981; }
.power-level-3 { border-color: #f59e0b; }
.power-level-4 { border-color: #ef4444; }
.power-level-5 { border-color: #8b5cf6; }

.tile-back {
    width: 60px;
    height: 75px;
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border: 2px solid #6b7280;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-weight: bold;
}

.drop-zone {
    min-height: 80px;
    border: 2px dashed #6b7280;
    border-radius: 8px;
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.drop-zone:hover {
    border-color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

.cyber-button {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 2px solid #a78bfa;
    transition: all 0.3s ease;
}

.cyber-button:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.cyber-button:disabled {
    background: #374151;
    border-color: #6b7280;
    transform: none;
    box-shadow: none;
}
</style>
`;

// Add styles to document
document.head.insertAdjacentHTML('beforeend', powerTileStyles);

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    // loadPaiGowPowerGame(); // Call this when the game is selected
});