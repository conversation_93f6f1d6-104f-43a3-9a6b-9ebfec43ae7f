// Pontoon: Ocean King - Cyberpunk Blackjack variant with extremely low win rate (<10%)
let pontoonOceanGame = {
    playerHand: [],
    dealerHand: [],
    playerScore: 0,
    dealerScore: 0,
    betAmount: 0,
    gamePhase: 'betting', // betting, playing, dealer, complete
    gameResult: null,
    totalWin: 0,
    difficulty: 'normal', // easy, normal, hard, abyss
    gameMode: 'classic', // classic, ocean, tsunami
    oceanPower: 0, // Special ocean energy meter
    tidalWave: false, // Bonus feature
    kraken: false, // Penalty feature
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        handsPushed: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        pontoons: 0, // Natural 21s
        fivecardtricks: 0,
        busts: 0,
        oceanWins: 0,
        krakenLosses: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game constants with heavy house bias
const PONTOON_MODES = {
    classic: { 
        name: 'Classic Pontoon', 
        houseEdge: 0.35, // 35% house edge
        oceanChance: 0.05,
        payoutMultiplier: 0.75 // Reduced payouts
    },
    ocean: { 
        name: '<PERSON> King', 
        houseEdge: 0.40, // 40% house edge
        oceanChance: 0.08,
        payoutMultiplier: 0.70
    },
    tsunami: { 
        name: 'Tsunami Mode', 
        houseEdge: 0.45, // 45% house edge
        oceanChance: 0.03,
        payoutMultiplier: 0.65
    }
};

const PONTOON_DIFFICULTIES = {
    easy: { 
        name: 'Shallow Waters', 
        dealerAdvantage: 0.15,
        bustPenalty: 1.2,
        cardBias: 0.20
    },
    normal: { 
        name: 'Deep Ocean', 
        dealerAdvantage: 0.25,
        bustPenalty: 1.5,
        cardBias: 0.35
    },
    hard: { 
        name: 'Storm Seas', 
        dealerAdvantage: 0.35,
        bustPenalty: 2.0,
        cardBias: 0.50
    },
    abyss: { 
        name: 'Abyssal Depths', 
        dealerAdvantage: 0.45,
        bustPenalty: 3.0,
        cardBias: 0.65
    }
};

const PONTOON_PAYOUTS = {
    PONTOON: 1.5, // Natural 21 (reduced from 2.5:1)
    FIVE_CARD_TRICK: 1.2, // 5 cards under 21 (reduced from 2:1)
    WIN: 0.8, // Regular win (less than 1:1)
    OCEAN_BONUS: 0.1, // Minimal ocean bonus
    TIDAL_MULTIPLIER: 1.1 // Tiny tidal bonus
};

// Biased deck creation - favors dealer
function createBiasedPontoonDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const deck = [];
    
    const modeData = PONTOON_MODES[pontoonOceanGame.gameMode];
    const difficultyData = PONTOON_DIFFICULTIES[pontoonOceanGame.difficulty];
    
    suits.forEach(suit => {
        ranks.forEach(rank => {
            let cardWeight = 1;
            const value = getCardValue(rank);
            
            // Reduce high cards for player advantage
            if (value === 10 || rank === 'A') {
                cardWeight *= (1 - difficultyData.cardBias);
            }
            
            // Increase low cards that cause busts
            if (value >= 6 && value <= 9) {
                cardWeight *= (1 + difficultyData.cardBias);
            }
            
            // Add cards based on weight
            const cardCount = Math.max(1, Math.floor(cardWeight * 4));
            for (let i = 0; i < cardCount; i++) {
                deck.push({ suit, rank, value });
            }
        });
    });
    
    return shuffleDeck(deck);
}

function getCardValue(rank) {
    if (rank === 'A') return 11; // Ace high initially
    if (['J', 'Q', 'K'].includes(rank)) return 10;
    return parseInt(rank);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function dealBiasedCard(isDealer = false) {
    if (pontoonOceanGame.deck.length === 0) {
        pontoonOceanGame.deck = createBiasedPontoonDeck();
    }
    
    const difficultyData = PONTOON_DIFFICULTIES[pontoonOceanGame.difficulty];
    
    // Apply additional bias when dealing
    if (isDealer && Math.random() < difficultyData.dealerAdvantage) {
        // Give dealer better cards
        const goodCards = pontoonOceanGame.deck.filter(card => 
            card.value === 10 || card.rank === 'A' || 
            (card.value >= 7 && card.value <= 9)
        );
        
        if (goodCards.length > 0) {
            const cardIndex = pontoonOceanGame.deck.indexOf(goodCards[0]);
            return pontoonOceanGame.deck.splice(cardIndex, 1)[0];
        }
    }
    
    return pontoonOceanGame.deck.pop();
}

function calculateHandScore(hand) {
    let score = 0;
    let aces = 0;
    
    hand.forEach(card => {
        if (card.rank === 'A') {
            aces++;
            score += 11;
        } else {
            score += card.value;
        }
    });
    
    // Adjust for aces
    while (score > 21 && aces > 0) {
        score -= 10;
        aces--;
    }
    
    return score;
}

function isPontoon(hand) {
    return hand.length === 2 && calculateHandScore(hand) === 21;
}

function isFiveCardTrick(hand) {
    return hand.length === 5 && calculateHandScore(hand) <= 21;
}

function startPontoonGame() {
    const betAmount = parseInt(document.getElementById('pontoonBet').value);
    
    if (betAmount > balance || betAmount <= 0) {
        updatePontoonStatus('Invalid bet amount!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    pontoonOceanGame.playerHand = [];
    pontoonOceanGame.dealerHand = [];
    pontoonOceanGame.betAmount = betAmount;
    pontoonOceanGame.gamePhase = 'playing';
    pontoonOceanGame.gameResult = null;
    pontoonOceanGame.totalWin = 0;
    pontoonOceanGame.tidalWave = false;
    pontoonOceanGame.kraken = false;
    
    pontoonOceanGame.stats.totalWagered += betAmount;
    
    // Create biased deck
    pontoonOceanGame.deck = createBiasedPontoonDeck();
    
    // Deal initial cards
    pontoonOceanGame.playerHand.push(dealBiasedCard(false));
    pontoonOceanGame.dealerHand.push(dealBiasedCard(true));
    pontoonOceanGame.playerHand.push(dealBiasedCard(false));
    pontoonOceanGame.dealerHand.push(dealBiasedCard(true)); // Dealer gets second card face down
    
    // Calculate scores
    pontoonOceanGame.playerScore = calculateHandScore(pontoonOceanGame.playerHand);
    pontoonOceanGame.dealerScore = calculateHandScore(pontoonOceanGame.dealerHand);
    
    // Check for natural pontoons
    if (isPontoon(pontoonOceanGame.playerHand)) {
        if (isPontoon(pontoonOceanGame.dealerHand)) {
            // Both have pontoon - house wins ties
            pontoonOceanGame.gameResult = 'push';
            pontoonOceanGame.totalWin = pontoonOceanGame.betAmount;
            balance += pontoonOceanGame.betAmount;
            updateBalance();
            updatePontoonStatus('Both Pontoons - House wins ties!');
        } else {
            // Player pontoon (rare win)
            pontoonOceanGame.gameResult = 'pontoon';
            calculatePontoonWinnings();
            updatePontoonStatus('PONTOON! Rare natural 21!');
        }
        pontoonOceanGame.gamePhase = 'complete';
        updatePontoonGameStats();
    } else if (isPontoon(pontoonOceanGame.dealerHand)) {
        // Dealer pontoon
        pontoonOceanGame.gameResult = 'loss';
        updatePontoonStatus('Dealer Pontoon - You lose!');
        pontoonOceanGame.gamePhase = 'complete';
        updatePontoonGameStats();
    }
    
    // Generate ocean effects
    generateOceanEffects();
    
    updatePontoonDisplay();
    
    // Enable/disable buttons
    document.getElementById('hitPontoon').disabled = pontoonOceanGame.gamePhase !== 'playing';
    document.getElementById('stickPontoon').disabled = pontoonOceanGame.gamePhase !== 'playing';
    document.getElementById('dealPontoon').disabled = pontoonOceanGame.gamePhase === 'playing';
}

function generateOceanEffects() {
    const modeData = PONTOON_MODES[pontoonOceanGame.gameMode];
    
    // Ocean power (rarely helps player)
    if (Math.random() < modeData.oceanChance) {
        pontoonOceanGame.oceanPower = Math.floor(Math.random() * 3) + 1;
    }
    
    // Tidal wave (minimal bonus)
    if (Math.random() < 0.05) {
        pontoonOceanGame.tidalWave = true;
    }
    
    // Kraken (penalty for player)
    if (Math.random() < 0.15) {
        pontoonOceanGame.kraken = true;
    }
}

function hitCard() {
    if (pontoonOceanGame.gamePhase !== 'playing') return;
    
    // Deal card with bias against player
    const newCard = dealBiasedCard(false);
    pontoonOceanGame.playerHand.push(newCard);
    pontoonOceanGame.playerScore = calculateHandScore(pontoonOceanGame.playerHand);
    
    // Check for bust with penalty
    if (pontoonOceanGame.playerScore > 21) {
        const difficultyData = PONTOON_DIFFICULTIES[pontoonOceanGame.difficulty];
        
        // Apply kraken penalty
        if (pontoonOceanGame.kraken) {
            pontoonOceanGame.stats.krakenLosses++;
            updatePontoonStatus('KRAKEN ATTACK! Bust with extra penalty!');
        } else {
            updatePontoonStatus('BUST! Over 21!');
        }
        
        pontoonOceanGame.gameResult = 'bust';
        pontoonOceanGame.gamePhase = 'complete';
        pontoonOceanGame.stats.busts++;
        updatePontoonGameStats();
    } else if (isFiveCardTrick(pontoonOceanGame.playerHand)) {
        // Five card trick (rare)
        pontoonOceanGame.gameResult = 'fivecard';
        calculatePontoonWinnings();
        updatePontoonStatus('FIVE CARD TRICK! Rare achievement!');
        pontoonOceanGame.gamePhase = 'complete';
        updatePontoonGameStats();
    }
    
    updatePontoonDisplay();
    
    // Disable hit if 5 cards or bust
    if (pontoonOceanGame.playerHand.length >= 5 || pontoonOceanGame.playerScore > 21) {
        document.getElementById('hitPontoon').disabled = true;
    }
}

function stickHand() {
    if (pontoonOceanGame.gamePhase !== 'playing') return;
    
    pontoonOceanGame.gamePhase = 'dealer';
    document.getElementById('hitPontoon').disabled = true;
    document.getElementById('stickPontoon').disabled = true;
    
    // Dealer plays with advantage
    playDealerHand();
}

function playDealerHand() {
    const difficultyData = PONTOON_DIFFICULTIES[pontoonOceanGame.difficulty];
    
    // Dealer hits on soft 17 and has advantage
    while (pontoonOceanGame.dealerScore < 17 || 
           (pontoonOceanGame.dealerScore === 17 && hasAce(pontoonOceanGame.dealerHand))) {
        
        // Dealer gets biased cards
        const newCard = dealBiasedCard(true);
        pontoonOceanGame.dealerHand.push(newCard);
        pontoonOceanGame.dealerScore = calculateHandScore(pontoonOceanGame.dealerHand);
        
        // Dealer rarely busts due to bias
        if (pontoonOceanGame.dealerScore > 21 && Math.random() > difficultyData.dealerAdvantage) {
            break;
        }
    }
    
    // Compare hands and determine result
    compareHands();
    pontoonOceanGame.gamePhase = 'complete';
    updatePontoonGameStats();
    updatePontoonDisplay();
    
    document.getElementById('dealPontoon').disabled = false;
}

function hasAce(hand) {
    return hand.some(card => card.rank === 'A');
}

function compareHands() {
    const modeData = PONTOON_MODES[pontoonOceanGame.gameMode];
    
    if (pontoonOceanGame.dealerScore > 21) {
        // Dealer bust (rare) - but apply house edge
        if (Math.random() < modeData.houseEdge) {
            pontoonOceanGame.gameResult = 'push'; // House wins even on dealer bust
            pontoonOceanGame.totalWin = pontoonOceanGame.betAmount;
            balance += pontoonOceanGame.betAmount;
            updateBalance();
            updatePontoonStatus('Dealer bust - but house edge applies!');
        } else {
            pontoonOceanGame.gameResult = 'win';
            calculatePontoonWinnings();
            updatePontoonStatus('Dealer bust - You win!');
        }
    } else if (pontoonOceanGame.playerScore > pontoonOceanGame.dealerScore) {
        // Player higher - but apply heavy house edge
        if (Math.random() < modeData.houseEdge * 1.5) {
            pontoonOceanGame.gameResult = 'loss';
            updatePontoonStatus('House edge - You lose despite higher score!');
        } else {
            pontoonOceanGame.gameResult = 'win';
            calculatePontoonWinnings();
            updatePontoonStatus('Higher score - You win!');
        }
    } else if (pontoonOceanGame.playerScore === pontoonOceanGame.dealerScore) {
        // Tie - house wins most ties
        if (Math.random() < 0.7) {
            pontoonOceanGame.gameResult = 'loss';
            updatePontoonStatus('Tie - House wins!');
        } else {
            pontoonOceanGame.gameResult = 'push';
            pontoonOceanGame.totalWin = pontoonOceanGame.betAmount;
            balance += pontoonOceanGame.betAmount;
            updateBalance();
            updatePontoonStatus('Tie - Push!');
        }
    } else {
        // Dealer higher
        pontoonOceanGame.gameResult = 'loss';
        updatePontoonStatus('Dealer wins with higher score!');
    }
}

function calculatePontoonWinnings() {
    const modeData = PONTOON_MODES[pontoonOceanGame.gameMode];
    const difficultyData = PONTOON_DIFFICULTIES[pontoonOceanGame.difficulty];
    
    let payout = 0;
    
    if (pontoonOceanGame.gameResult === 'pontoon') {
        payout = PONTOON_PAYOUTS.PONTOON;
        pontoonOceanGame.stats.pontoons++;
    } else if (pontoonOceanGame.gameResult === 'fivecard') {
        payout = PONTOON_PAYOUTS.FIVE_CARD_TRICK;
        pontoonOceanGame.stats.fivecardtricks++;
    } else {
        payout = PONTOON_PAYOUTS.WIN;
    }
    
    // Apply mode multiplier (reduces payouts)
    payout *= modeData.payoutMultiplier;
    
    // Apply ocean bonus (minimal)
    if (pontoonOceanGame.oceanPower > 0) {
        payout += PONTOON_PAYOUTS.OCEAN_BONUS * pontoonOceanGame.oceanPower;
        pontoonOceanGame.stats.oceanWins++;
    }
    
    // Apply tidal wave bonus (tiny)
    if (pontoonOceanGame.tidalWave) {
        payout *= PONTOON_PAYOUTS.TIDAL_MULTIPLIER;
    }
    
    // Apply kraken penalty
    if (pontoonOceanGame.kraken) {
        payout *= 0.5; // 50% penalty
    }
    
    // Calculate final winnings
    let winnings = pontoonOceanGame.betAmount * payout;
    
    // Apply additional house edge reduction
    winnings *= (1 - modeData.houseEdge * 0.3);
    
    pontoonOceanGame.totalWin = Math.floor(Math.max(1, winnings));
    
    balance += pontoonOceanGame.betAmount + pontoonOceanGame.totalWin;
    updateBalance();
}

function updatePontoonGameStats() {
    pontoonOceanGame.stats.handsPlayed++;
    
    if (pontoonOceanGame.gameResult === 'win' || 
        pontoonOceanGame.gameResult === 'pontoon' || 
        pontoonOceanGame.gameResult === 'fivecard') {
        
        pontoonOceanGame.stats.handsWon++;
        pontoonOceanGame.stats.totalWon += pontoonOceanGame.totalWin;
        
        if (pontoonOceanGame.totalWin > pontoonOceanGame.stats.biggestWin) {
            pontoonOceanGame.stats.biggestWin = pontoonOceanGame.totalWin;
        }
        
        // Update win streak
        pontoonOceanGame.streakData.currentWinStreak++;
        pontoonOceanGame.streakData.currentLossStreak = 0;
        
        if (pontoonOceanGame.streakData.currentWinStreak > pontoonOceanGame.streakData.longestWinStreak) {
            pontoonOceanGame.streakData.longestWinStreak = pontoonOceanGame.streakData.currentWinStreak;
        }
    } else if (pontoonOceanGame.gameResult === 'push') {
        pontoonOceanGame.stats.handsPushed++;
        pontoonOceanGame.streakData.currentWinStreak = 0;
        pontoonOceanGame.streakData.currentLossStreak = 0;
    } else {
        // Update loss streak
        pontoonOceanGame.streakData.currentLossStreak++;
        pontoonOceanGame.streakData.currentWinStreak = 0;
        
        if (pontoonOceanGame.streakData.currentLossStreak > pontoonOceanGame.streakData.longestLossStreak) {
            pontoonOceanGame.streakData.longestLossStreak = pontoonOceanGame.streakData.currentLossStreak;
        }
    }
}

function updatePontoonDisplay() {
    // Update player hand
    const playerHandEl = document.getElementById('playerPontoonHand');
    playerHandEl.innerHTML = '';
    pontoonOceanGame.playerHand.forEach(card => {
        const cardEl = document.createElement('div');
        cardEl.className = 'playing-card';
        cardEl.innerHTML = `
            <div class="card-content">
                <div class="card-rank">${card.rank}</div>
                <div class="card-suit">${card.suit}</div>
            </div>
        `;
        playerHandEl.appendChild(cardEl);
    });
    
    // Update dealer hand
    const dealerHandEl = document.getElementById('dealerPontoonHand');
    dealerHandEl.innerHTML = '';
    pontoonOceanGame.dealerHand.forEach((card, index) => {
        const cardEl = document.createElement('div');
        
        if (index === 1 && pontoonOceanGame.gamePhase === 'playing') {
            // Hide dealer's second card
            cardEl.className = 'playing-card face-down';
            cardEl.innerHTML = '<div class="card-back">?</div>';
        } else {
            cardEl.className = 'playing-card';
            cardEl.innerHTML = `
                <div class="card-content">
                    <div class="card-rank">${card.rank}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
            `;
        }
        dealerHandEl.appendChild(cardEl);
    });
    
    // Update scores
    document.getElementById('playerPontoonScore').textContent = pontoonOceanGame.playerScore;
    
    if (pontoonOceanGame.gamePhase === 'playing') {
        document.getElementById('dealerPontoonScore').textContent = pontoonOceanGame.dealerHand[0].value;
    } else {
        document.getElementById('dealerPontoonScore').textContent = pontoonOceanGame.dealerScore;
    }
    
    // Update ocean effects
    document.getElementById('oceanPower').textContent = pontoonOceanGame.oceanPower;
    document.getElementById('tidalWave').style.display = pontoonOceanGame.tidalWave ? 'block' : 'none';
    document.getElementById('krakenWarning').style.display = pontoonOceanGame.kraken ? 'block' : 'none';
    
    // Update game result
    if (pontoonOceanGame.gameResult) {
        const resultEl = document.getElementById('pontoonGameResult');
        if (pontoonOceanGame.gameResult === 'win' || 
            pontoonOceanGame.gameResult === 'pontoon' || 
            pontoonOceanGame.gameResult === 'fivecard') {
            resultEl.innerHTML = `OCEAN VICTORY! +${pontoonOceanGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono';
        } else if (pontoonOceanGame.gameResult === 'push') {
            resultEl.innerHTML = `TIDAL PUSH - Bet Returned`;
            resultEl.className = 'text-center text-xl font-bold text-yellow-400 h-8 font-mono';
        } else {
            resultEl.innerHTML = `DROWNED - Lost ${pontoonOceanGame.betAmount} GA`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    updatePontoonStats();
}

function updatePontoonStats() {
    document.getElementById('pontoonHandsPlayed').textContent = pontoonOceanGame.stats.handsPlayed;
    
    const winRate = pontoonOceanGame.stats.handsPlayed > 0 ? 
        ((pontoonOceanGame.stats.handsWon / pontoonOceanGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('pontoonWinRate').textContent = `${winRate}%`;
    
    document.getElementById('pontoonTotalWagered').textContent = pontoonOceanGame.stats.totalWagered.toLocaleString();
    document.getElementById('pontoonTotalWon').textContent = pontoonOceanGame.stats.totalWon.toLocaleString();
    document.getElementById('pontoonBiggestWin').textContent = pontoonOceanGame.stats.biggestWin.toLocaleString();
    document.getElementById('pontoonPontoons').textContent = pontoonOceanGame.stats.pontoons;
    document.getElementById('pontoonFiveCards').textContent = pontoonOceanGame.stats.fivecardtricks;
    document.getElementById('pontoonBusts').textContent = pontoonOceanGame.stats.busts;
    document.getElementById('pontoonLossStreak').textContent = pontoonOceanGame.streakData.currentLossStreak;
}

function updatePontoonStatus(message) {
    document.getElementById('pontoonGameStatus').textContent = message;
}

function changePontoonMode() {
    const mode = document.getElementById('pontoonMode').value;
    pontoonOceanGame.gameMode = mode;
    
    const modeData = PONTOON_MODES[mode];
    document.getElementById('pontoonModeInfo').innerHTML = `
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Ocean Chance: ${(modeData.oceanChance * 100).toFixed(1)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}%
    `;
}

function changePontoonDifficulty() {
    const difficulty = document.getElementById('pontoonDifficulty').value;
    pontoonOceanGame.difficulty = difficulty;
    
    const diffData = PONTOON_DIFFICULTIES[difficulty];
    document.getElementById('pontoonDifficultyInfo').innerHTML = `
        Dealer Advantage: ${(diffData.dealerAdvantage * 100).toFixed(0)}% | 
        Card Bias: ${(diffData.cardBias * 100).toFixed(0)}% | 
        Bust Penalty: ${diffData.bustPenalty}x
    `;
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changePontoonMode();
    changePontoonDifficulty();
});