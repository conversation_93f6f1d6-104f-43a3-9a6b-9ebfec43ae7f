// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Caribbean Stud Adventure Implementation with <10% win rate
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// Extremely low payouts to ensure <10% win rate
const ADVENTURE_PAYOUTS = {
    ROYAL_FLUSH: 25,      // Reduced from 100
    STRAIGHT_FLUSH: 15,   // Reduced from 50
    FOUR_KIND: 8,         // Reduced from 20
    FULL_HOUSE: 3,        // Reduced from 7
    FLUSH: 2,             // Reduced from 5
    STRAIGHT: 1.5,        // Reduced from 4
    THREE_KIND: 1.2,      // Reduced from 3
    TWO_PAIR: 1,          // Reduced from 2
    PAIR_ACES_KINGS: 0.8  // Reduced from 1
};

// Reduced progressive payouts
const PROGRESSIVE_PAYOUTS = {
    ROYAL_FLUSH: 15000,   // Reduced from 100000
    STRAIGHT_FLUSH: 2500, // Reduced from 10000
    FOUR_KIND: 150,       // Reduced from 500
    FULL_HOUSE: 25,       // Reduced from 100
    FLUSH: 10             // Reduced from 50
};

// Adventure difficulty modes with extreme bias
const ADVENTURE_MODES = [
    {
        name: 'explorer',
        description: 'Explorer Mode (8% win rate)',
        winRate: 0.08,
        dealerAdvantage: 0.92,
        qualificationBonus: 0.85,
        handBias: 0.88
    },
    {
        name: 'treasure_hunter',
        description: 'Treasure Hunter (6% win rate)',
        winRate: 0.06,
        dealerAdvantage: 0.94,
        qualificationBonus: 0.82,
        handBias: 0.90
    },
    {
        name: 'pirate_legend',
        description: 'Pirate Legend (4% win rate)',
        winRate: 0.04,
        dealerAdvantage: 0.96,
        qualificationBonus: 0.80,
        handBias: 0.92
    }
];

let caribbeanAdventureGame = {
    deck: [],
    playerCards: [],
    dealerCards: [],
    anteBet: 0,
    playBet: 0,
    progressiveBet: 0,
    gamePhase: 'betting',
    dealerQualifies: false,
    playerHandRank: null,
    dealerHandRank: null,
    totalWin: 0,
    progressiveJackpot: 75000,
    adventureLevel: 1,
    treasurePoints: 0,
    currentMode: 'explorer',
    sessionStats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        royalFlushes: 0,
        progressiveWins: 0,
        treasuresFound: 0
    }
};

function loadCaribbeanStudAdventureGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-6xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-4xl font-bold cyber-title mb-4">Caribbean Stud Adventure</h3>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div>Adventure Level: <span id="adventureLevel" class="text-neon-purple">1</span></div>
                    <div>Treasure Points: <span id="treasurePoints" class="text-neon-blue">0</span></div>
                    <div>Progressive: <span id="progressiveJackpot" class="text-neon-green">$75,000</span></div>
                </div>
                <div class="mt-2">
                    <select id="adventureModeSelect" class="cyber-select bg-cyber-dark border border-neon-purple rounded px-4 py-2">
                        <option value="explorer">Explorer Mode (8% win rate)</option>
                        <option value="treasure_hunter">Treasure Hunter (6% win rate)</option>
                        <option value="pirate_legend">Pirate Legend (4% win rate)</option>
                    </select>
                </div>
            </div>

            <!-- Game Table -->
            <div class="cyber-panel p-8">
                <!-- Dealer Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-neon-purple mb-4">🏴‍☠️ Captain's Hand</h4>
                    <div id="dealerCards" class="flex justify-center space-x-2 mb-4 min-h-[120px]">
                        <!-- Dealer cards will appear here -->
                    </div>
                    <div id="dealerHandInfo" class="text-lg font-semibold text-gray-300"></div>
                    <div id="dealerQualification" class="text-sm text-gray-400 mt-2"></div>
                </div>

                <!-- Adventure Status -->
                <div class="text-center mb-6">
                    <div id="gameStatus" class="text-xl font-bold text-neon-pink mb-2">Begin your treasure hunt!</div>
                    <div id="gamePhase" class="text-lg text-neon-blue">Betting Phase</div>
                    <div id="adventureBonus" class="text-sm text-neon-green mt-2"></div>
                </div>

                <!-- Player Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-neon-purple mb-4">🗺️ Your Hand</h4>
                    <div id="playerCards" class="flex justify-center space-x-2 mb-4 min-h-[120px]">
                        <!-- Player cards will appear here -->
                    </div>
                    <div id="playerHandInfo" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- Betting Area -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Ante Bet -->
                    <div class="cyber-card border-neon-pink">
                        <h5 class="text-lg font-semibold text-neon-pink mb-3">⚓ Ante Bet</h5>
                        <div class="space-y-3">
                            <input type="range" id="anteBetSlider" min="25" max="1000" value="100" 
                                   class="w-full accent-neon-pink">
                            <div class="text-center">
                                <span id="anteBetAmount" class="text-xl font-bold text-neon-pink">100 GA</span>
                            </div>
                            <button id="dealCards" class="w-full cyber-button-primary py-2 rounded-lg font-semibold">
                                SET SAIL
                            </button>
                        </div>
                    </div>

                    <!-- Play Bet -->
                    <div class="cyber-card border-neon-blue">
                        <h5 class="text-lg font-semibold text-neon-blue mb-3">⚔️ Battle Bet</h5>
                        <div class="space-y-3">
                            <div class="text-center text-gray-400">
                                <div>2x Ante Bet</div>
                                <div id="playBetAmount" class="text-xl font-bold text-neon-blue">0 GA</div>
                            </div>
                            <button id="playButton" class="w-full cyber-button-secondary py-2 rounded-lg font-semibold" disabled>
                                FIGHT
                            </button>
                            <button id="foldButton" class="w-full cyber-button-danger py-2 rounded-lg font-semibold" disabled>
                                RETREAT
                            </button>
                        </div>
                    </div>

                    <!-- Progressive Bet -->
                    <div class="cyber-card border-neon-green">
                        <h5 class="text-lg font-semibold text-neon-green mb-3">💎 Treasure Hunt</h5>
                        <div class="space-y-3">
                            <div class="text-center">
                                <div class="text-sm text-gray-400">Optional $10 treasure bet</div>
                                <div class="text-lg font-bold text-neon-green">$75,000</div>
                            </div>
                            <label class="flex items-center justify-center space-x-2">
                                <input type="checkbox" id="progressiveCheck" class="accent-neon-green">
                                <span class="text-neon-green">Hunt Treasure</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button id="newGameButton" class="cyber-button-primary px-8 py-3 rounded-lg font-semibold" 
                            onclick="resetAdventureGame()">
                        NEW VOYAGE
                    </button>
                    <button id="rulesButton" class="cyber-button-secondary px-8 py-3 rounded-lg font-semibold"
                            onclick="showAdventureRules()">
                        TREASURE MAP
                    </button>
                </div>

                <!-- Game Result -->
                <div id="gameResult" class="text-center text-2xl font-bold min-h-[60px] flex items-center justify-center">
                    <!-- Result will appear here -->
                </div>
            </div>

            <!-- Statistics Panel -->
            <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="cyber-card border-neon-purple text-center">
                    <div class="text-2xl font-bold text-neon-purple" id="handsPlayed">0</div>
                    <div class="text-sm text-gray-400">Voyages</div>
                </div>
                <div class="cyber-card border-neon-green text-center">
                    <div class="text-2xl font-bold text-neon-green" id="handsWon">0</div>
                    <div class="text-sm text-gray-400">Victories</div>
                </div>
                <div class="cyber-card border-neon-pink text-center">
                    <div class="text-2xl font-bold text-neon-pink" id="totalWagered">0</div>
                    <div class="text-sm text-gray-400">Total Wagered</div>
                </div>
                <div class="cyber-card border-neon-blue text-center">
                    <div class="text-2xl font-bold text-neon-blue" id="biggestWin">0</div>
                    <div class="text-sm text-gray-400">Greatest Treasure</div>
                </div>
            </div>

            <!-- Adventure Payout Table -->
            <div class="mt-8 cyber-panel">
                <h4 class="text-xl font-bold text-neon-purple mb-4 text-center">Treasure Rewards (Reduced Payouts)</h4>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div class="text-center">
                        <div class="font-bold text-neon-pink">Royal Flush</div>
                        <div class="text-gray-300">25:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-blue">Straight Flush</div>
                        <div class="text-gray-300">15:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-green">Four of a Kind</div>
                        <div class="text-gray-300">8:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-purple">Full House</div>
                        <div class="text-gray-300">3:1</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-red-400">Flush</div>
                        <div class="text-gray-300">2:1</div>
                    </div>
                </div>
                <div class="text-center mt-4 text-xs text-gray-400">
                    ⚠️ Adventure Mode: Reduced payouts for extreme challenge!
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div id="rulesModal" class="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 hidden flex items-center justify-center">
            <div class="cyber-panel max-w-2xl mx-4">
                <h3 class="text-2xl font-bold text-neon-purple mb-4">Caribbean Stud Adventure Rules</h3>
                <div class="space-y-4 text-gray-300">
                    <p><strong class="text-neon-pink">Objective:</strong> Defeat the pirate captain with a superior poker hand!</p>
                    <p><strong class="text-neon-blue">Adventure Gameplay:</strong></p>
                    <ul class="list-disc list-inside space-y-2 ml-4">
                        <li>Place ante bet to begin your treasure hunt</li>
                        <li>Captain gets 5 cards (4 hidden, 1 revealed)</li>
                        <li>Choose to FIGHT (2x ante) or RETREAT (lose ante)</li>
                        <li>Captain needs A-K high or better to qualify for battle</li>
                        <li>If captain doesn't qualify, ante pays reduced odds</li>
                        <li>Best hand wins both bets with reduced payouts</li>
                    </ul>
                    <p><strong class="text-neon-green">Treasure Hunt:</strong> Optional $10 bet for massive progressive rewards!</p>
                    <p><strong class="text-red-400">Warning:</strong> Adventure mode features extremely challenging odds and reduced payouts!</p>
                </div>
                <button onclick="hideAdventureRules()" class="mt-6 cyber-button-primary px-6 py-2 rounded-lg font-semibold">
                    CLOSE MAP
                </button>
            </div>
        </div>
    `;
    
    setupAdventureGame();
}

function setupAdventureGame() {
    // Event listeners
    document.getElementById('anteBetSlider').addEventListener('input', updateAnteBet);
    document.getElementById('dealCards').addEventListener('click', dealAdventureCards);
    document.getElementById('playButton').addEventListener('click', playAdventureHand);
    document.getElementById('foldButton').addEventListener('click', foldAdventureHand);
    document.getElementById('progressiveCheck').addEventListener('change', updateProgressiveBet);
    document.getElementById('adventureModeSelect').addEventListener('change', updateAdventureMode);
    
    updateAdventureDisplay();
    updateSessionStats();
}

function updateAdventureMode() {
    const mode = document.getElementById('adventureModeSelect').value;
    caribbeanAdventureGame.currentMode = mode;
    
    const modeData = ADVENTURE_MODES.find(m => m.name === mode);
    document.getElementById('adventureBonus').textContent = 
        `Current Mode: ${modeData.description} - Extreme Challenge!`;
}

function updateAnteBet() {
    const anteBet = parseInt(document.getElementById('anteBetSlider').value);
    document.getElementById('anteBetAmount').textContent = `${anteBet} GA`;
    document.getElementById('playBetAmount').textContent = `${anteBet * 2} GA`;
}

function updateProgressiveBet() {
    const isChecked = document.getElementById('progressiveCheck').checked;
    caribbeanAdventureGame.progressiveBet = isChecked ? 10 : 0;
}

function createBiasedDeck() {
    const deck = [];
    const modeData = ADVENTURE_MODES.find(m => m.name === caribbeanAdventureGame.currentMode);
    
    // Create multiple decks with heavy bias against player
    for (let deckCount = 0; deckCount < 3; deckCount++) {
        for (const suit of CARD_SUITS) {
            for (const value of CARD_VALUES) {
                deck.push({ suit, value });
            }
        }
    }
    
    // Heavily bias deck composition
    const biasedDeck = [];
    
    // Add more low cards and fewer high cards for player disadvantage
    deck.forEach(card => {
        const cardValue = getCardValue(card);
        
        // Reduce high cards (10, J, Q, K, A) frequency
        if (cardValue >= 10) {
            if (Math.random() < 0.3 * modeData.handBias) {
                biasedDeck.push(card);
            }
        } else {
            // Increase low cards frequency
            biasedDeck.push(card);
            if (Math.random() < 0.4) {
                biasedDeck.push({ ...card }); // Add duplicate
            }
        }
    });
    
    return shuffleDeck(biasedDeck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function createCardElement(card, isHidden = false) {
    const cardElement = document.createElement('div');
    cardElement.className = 'card inline-block w-20 h-28 bg-white rounded-lg border-2 border-gray-300 m-1 flex flex-col items-center justify-center text-lg font-bold shadow-lg transition-all duration-300 hover:scale-105';
    
    if (isHidden) {
        cardElement.innerHTML = '🂠';
        cardElement.className += ' bg-gradient-to-br from-blue-900 to-purple-900 text-neon-blue';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.innerHTML = `
            <div class="${isRed ? 'text-red-600' : 'text-black'}">${card.value}</div>
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-2xl">${card.suit}</div>
        `;
    }
    
    return cardElement;
}

function dealAdventureCards() {
    const anteBet = parseInt(document.getElementById('anteBetSlider').value);
    
    if (balance < anteBet + caribbeanAdventureGame.progressiveBet) {
        document.getElementById('gameStatus').textContent = 'Insufficient treasure for this voyage!';
        return;
    }
    
    // Deduct ante and progressive bets
    balance -= anteBet + caribbeanAdventureGame.progressiveBet;
    updateBalance();
    
    // Initialize game with biased deck
    caribbeanAdventureGame.deck = createBiasedDeck();
    caribbeanAdventureGame.playerCards = [];
    caribbeanAdventureGame.dealerCards = [];
    caribbeanAdventureGame.anteBet = anteBet;
    caribbeanAdventureGame.playBet = 0;
    caribbeanAdventureGame.gamePhase = 'dealt';
    caribbeanAdventureGame.totalWin = 0;
    
    // Deal cards with bias
    dealBiasedCards();
    
    displayPlayerCards();
    displayDealerCards(false);
    
    // Enable play/fold buttons
    document.getElementById('playButton').disabled = false;
    document.getElementById('foldButton').disabled = false;
    document.getElementById('dealCards').disabled = true;
    
    // Evaluate player hand
    caribbeanAdventureGame.playerHandRank = evaluatePokerHand(caribbeanAdventureGame.playerCards);
    updatePlayerHandInfo();
    
    document.getElementById('gameStatus').textContent = 'Captain awaits your decision!';
    document.getElementById('gamePhase').textContent = 'Battle Phase';
    
    updateAdventureDisplay();
}

function dealBiasedCards() {
    const modeData = ADVENTURE_MODES.find(m => m.name === caribbeanAdventureGame.currentMode);
    
    // Deal player cards with heavy bias toward weak hands
    for (let i = 0; i < 5; i++) {
        let playerCard;
        let attempts = 0;
        
        do {
            playerCard = caribbeanAdventureGame.deck.pop();
            attempts++;
            
            // Heavily bias against good cards for player
            const cardValue = getCardValue(playerCard);
            if (cardValue >= 10 && Math.random() < 0.8 * modeData.handBias && attempts < 20) {
                caribbeanAdventureGame.deck.unshift(playerCard); // Put back
                continue;
            }
            break;
        } while (attempts < 25);
        
        caribbeanAdventureGame.playerCards.push(playerCard);
    }
    
    // Deal dealer cards with bias toward stronger hands
    for (let i = 0; i < 5; i++) {
        let dealerCard;
        let attempts = 0;
        
        do {
            dealerCard = caribbeanAdventureGame.deck.pop();
            attempts++;
            
            // Bias toward good cards for dealer
            const cardValue = getCardValue(dealerCard);
            if (cardValue < 10 && Math.random() < 0.6 * modeData.dealerAdvantage && attempts < 15) {
                caribbeanAdventureGame.deck.unshift(dealerCard); // Put back
                continue;
            }
            break;
        } while (attempts < 20);
        
        caribbeanAdventureGame.dealerCards.push(dealerCard);
    }
}

function displayPlayerCards() {
    const container = document.getElementById('playerCards');
    container.innerHTML = '';
    
    caribbeanAdventureGame.playerCards.forEach((card, index) => {
        const cardElement = createCardElement(card);
        cardElement.style.animationDelay = `${index * 0.1}s`;
        cardElement.classList.add('animate-pulse');
        container.appendChild(cardElement);
    });
}

function displayDealerCards(showAll = false) {
    const container = document.getElementById('dealerCards');
    container.innerHTML = '';
    
    caribbeanAdventureGame.dealerCards.forEach((card, index) => {
        const isHidden = !showAll && index > 0;
        const cardElement = createCardElement(card, isHidden);
        cardElement.style.animationDelay = `${index * 0.1}s`;
        cardElement.classList.add('animate-pulse');
        container.appendChild(cardElement);
    });
}

function playAdventureHand() {
    caribbeanAdventureGame.playBet = caribbeanAdventureGame.anteBet * 2;
    balance -= caribbeanAdventureGame.playBet;
    updateBalance();
    
    caribbeanAdventureGame.gamePhase = 'showdown';
    
    // Show all dealer cards
    displayDealerCards(true);
    
    // Evaluate dealer hand
    caribbeanAdventureGame.dealerHandRank = evaluatePokerHand(caribbeanAdventureGame.dealerCards);
    updateDealerHandInfo();
    
    // Check if dealer qualifies with bias
    caribbeanAdventureGame.dealerQualifies = checkBiasedDealerQualification();
    
    // Determine winner with extreme bias
    setTimeout(() => {
        determineAdventureWinner();
    }, 2000);
    
    // Disable buttons
    document.getElementById('playButton').disabled = true;
    document.getElementById('foldButton').disabled = true;
    
    document.getElementById('gameStatus').textContent = 'Captain reveals their hand...';
    updateAdventureDisplay();
}

function foldAdventureHand() {
    caribbeanAdventureGame.gamePhase = 'complete';
    
    document.getElementById('gameResult').innerHTML = 
        '<span class="text-red-400">🏳️ You retreated! Ante treasure lost to the depths.</span>';
    
    // Update stats
    caribbeanAdventureGame.sessionStats.handsPlayed++;
    caribbeanAdventureGame.sessionStats.totalWagered += caribbeanAdventureGame.anteBet + caribbeanAdventureGame.progressiveBet;
    
    updateSessionStats();
    setTimeout(() => resetForNextHand(), 3000);
}

function checkBiasedDealerQualification() {
    const modeData = ADVENTURE_MODES.find(m => m.name === caribbeanAdventureGame.currentMode);
    const hand = caribbeanAdventureGame.dealerCards;
    const handRank = caribbeanAdventureGame.dealerHandRank;
    
    // Heavily bias dealer to qualify more often
    if (handRank && handRank.rank > 0) {
        document.getElementById('dealerQualification').textContent = '⚔️ Captain qualifies with ' + handRank.name;
        return true;
    }
    
    // Check for A-K high with bias toward qualification
    const values = hand.map(card => getCardValue(card)).sort((a, b) => b - a);
    const hasAceKing = values[0] === 14 && values[1] === 13;
    
    // Increase qualification chance artificially
    const qualificationBonus = Math.random() < modeData.qualificationBonus;
    
    if (hasAceKing || qualificationBonus) {
        document.getElementById('dealerQualification').textContent = '⚔️ Captain qualifies for battle!';
        return true;
    }
    
    document.getElementById('dealerQualification').textContent = '🏳️ Captain does not qualify';
    return false;
}

function determineAdventureWinner() {
    const modeData = ADVENTURE_MODES.find(m => m.name === caribbeanAdventureGame.currentMode);
    let totalWinnings = 0;
    let resultText = '';
    
    // Check progressive jackpot first (very rare)
    if (caribbeanAdventureGame.progressiveBet > 0) {
        const progressiveWin = checkProgressiveWin();
        if (progressiveWin > 0) {
            totalWinnings += progressiveWin;
            resultText += `💎 TREASURE FOUND: $${progressiveWin.toLocaleString()}! `;
            caribbeanAdventureGame.sessionStats.progressiveWins++;
            caribbeanAdventureGame.sessionStats.treasuresFound++;
        }
    }
    
    if (!caribbeanAdventureGame.dealerQualifies) {
        // Dealer doesn't qualify - severely reduced payout
        const reducedPayout = Math.floor(caribbeanAdventureGame.anteBet * 1.2); // Only 20% profit
        totalWinnings += caribbeanAdventureGame.anteBet + reducedPayout;
        totalWinnings += caribbeanAdventureGame.playBet; // Return play bet
        resultText += '🏴‍☠️ Captain retreats! Minimal treasure gained.';
        caribbeanAdventureGame.sessionStats.handsWon++;
    } else {
        // Compare hands with extreme bias against player
        const playerRank = caribbeanAdventureGame.playerHandRank ? caribbeanAdventureGame.playerHandRank.rank : -1;
        const dealerRank = caribbeanAdventureGame.dealerHandRank ? caribbeanAdventureGame.dealerHandRank.rank : -1;
        
        // Apply extreme bias - player needs significantly better hand
        const playerAdvantage = playerRank - dealerRank;
        const biasThreshold = 2; // Player needs 2+ rank advantage
        
        if (playerAdvantage > biasThreshold || (playerAdvantage > 0 && Math.random() < modeData.winRate)) {
            // Player wins (very rare)
            const antePayout = caribbeanAdventureGame.anteBet * 2;
            const playPayout = getReducedPlayBetPayout();
            totalWinnings += antePayout + playPayout;
            resultText += `🏆 Victory! ${caribbeanAdventureGame.playerHandRank.name} conquers!`;
            caribbeanAdventureGame.sessionStats.handsWon++;
            
            // Award treasure points
            caribbeanAdventureGame.treasurePoints += Math.floor(totalWinnings / 20);
        } else if (playerRank === dealerRank) {
            // Ties favor dealer heavily
            if (Math.random() < 0.2) { // Only 20% chance player wins ties
                const antePayout = caribbeanAdventureGame.anteBet * 2;
                totalWinnings += antePayout + caribbeanAdventureGame.playBet;
                resultText += '⚖️ Narrow victory on high card!';
                caribbeanAdventureGame.sessionStats.handsWon++;
            } else {
                resultText += '💔 Captain wins the tie! No treasure found.';
            }
        } else {
            // Dealer wins (most common outcome)
            resultText += `💔 Captain's ${caribbeanAdventureGame.dealerHandRank.name} claims victory!`;
        }
    }
    
    // Add winnings to balance
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    caribbeanAdventureGame.sessionStats.handsPlayed++;
    caribbeanAdventureGame.sessionStats.totalWagered += caribbeanAdventureGame.anteBet + caribbeanAdventureGame.playBet + caribbeanAdventureGame.progressiveBet;
    caribbeanAdventureGame.sessionStats.totalWon += totalWinnings;
    caribbeanAdventureGame.sessionStats.biggestWin = Math.max(caribbeanAdventureGame.sessionStats.biggestWin, totalWinnings);
    
    // Check for level up (very rare)
    checkAdventureLevelUp();
    
    const winColor = totalWinnings > 0 ? 'text-neon-green' : 'text-red-400';
    document.getElementById('gameResult').innerHTML = `<span class="${winColor}">${resultText}</span>`;
    
    updateSessionStats();
    updateAdventureDisplay();
    
    setTimeout(() => {
        resetForNextHand();
    }, 5000);
}

function getReducedPlayBetPayout() {
    if (!caribbeanAdventureGame.playerHandRank) return caribbeanAdventureGame.playBet * 2;
    
    const multiplier = ADVENTURE_PAYOUTS[caribbeanAdventureGame.playerHandRank.type] || 1;
    return Math.floor(caribbeanAdventureGame.playBet * (1 + multiplier * 0.5)); // Further reduced
}

function checkProgressiveWin() {
    if (!caribbeanAdventureGame.playerHandRank) return 0;
    
    const progressivePayout = PROGRESSIVE_PAYOUTS[caribbeanAdventureGame.playerHandRank.type];
    if (progressivePayout && Math.random() < 0.001) { // 0.1% chance even with qualifying hand
        caribbeanAdventureGame.progressiveJackpot = Math.max(25000, caribbeanAdventureGame.progressiveJackpot - progressivePayout);
        return progressivePayout;
    }
    
    return 0;
}

function checkAdventureLevelUp() {
    const newLevel = Math.floor(caribbeanAdventureGame.treasurePoints / 2000) + 1;
    if (newLevel > caribbeanAdventureGame.adventureLevel) {
        caribbeanAdventureGame.adventureLevel = newLevel;
        document.getElementById('gameStatus').textContent = `🏝️ Adventure Level Up! Welcome to Level ${newLevel}!`;
    }
}

function evaluatePokerHand(cards) {
    const values = cards.map(card => getCardValue(card));
    const suits = cards.map(card => card.suit);
    
    // Count values and suits
    const valueCounts = {};
    const suitCounts = {};
    
    values.forEach(value => valueCounts[value] = (valueCounts[value] || 0) + 1);
    suits.forEach(suit => suitCounts[suit] = (suitCounts[suit] || 0) + 1);
    
    const sortedValues = values.sort((a, b) => b - a);
    const isFlush = Object.values(suitCounts).some(count => count === 5);
    const isStraight = checkStraight(sortedValues);
    
    // Check for hands in order of rank
    if (isFlush && isStraight && sortedValues[0] === 14) {
        return { rank: 9, name: 'Royal Flush', type: 'ROYAL_FLUSH' };
    }
    if (isFlush && isStraight) {
        return { rank: 8, name: 'Straight Flush', type: 'STRAIGHT_FLUSH' };
    }
    if (Object.values(valueCounts).includes(4)) {
        return { rank: 7, name: 'Four of a Kind', type: 'FOUR_KIND' };
    }
    if (Object.values(valueCounts).includes(3) && Object.values(valueCounts).includes(2)) {
        return { rank: 6, name: 'Full House', type: 'FULL_HOUSE' };
    }
    if (isFlush) {
        return { rank: 5, name: 'Flush', type: 'FLUSH' };
    }
    if (isStraight) {
        return { rank: 4, name: 'Straight', type: 'STRAIGHT' };
    }
    if (Object.values(valueCounts).includes(3)) {
        return { rank: 3, name: 'Three of a Kind', type: 'THREE_KIND' };
    }
    
    const pairs = Object.keys(valueCounts).filter(value => valueCounts[value] === 2);
    if (pairs.length === 2) {
        return { rank: 2, name: 'Two Pair', type: 'TWO_PAIR' };
    }
    if (pairs.length === 1) {
        const pairValue = parseInt(pairs[0]);
        if (pairValue >= 14 || pairValue >= 13) {
            return { rank: 1, name: 'Pair of ' + (pairValue === 14 ? 'Aces' : 'Kings'), type: 'PAIR_ACES_KINGS' };
        }
        return { rank: 0, name: 'Pair of ' + getCardName(pairValue), type: 'PAIR' };
    }
    
    return null; // High card
}

function checkStraight(sortedValues) {
    for (let i = 0; i < 4; i++) {
        if (sortedValues[i] - sortedValues[i + 1] !== 1) {
            if (i === 0 && sortedValues[0] === 14 && sortedValues[1] === 5 && 
                sortedValues[2] === 4 && sortedValues[3] === 3 && sortedValues[4] === 2) {
                return true;
            }
            return false;
        }
    }
    return true;
}

function getCardValue(card) {
    if (card.value === 'A') return 14;
    if (card.value === 'K') return 13;
    if (card.value === 'Q') return 12;
    if (card.value === 'J') return 11;
    return parseInt(card.value);
}

function getCardName(value) {
    if (value === 14) return 'Aces';
    if (value === 13) return 'Kings';
    if (value === 12) return 'Queens';
    if (value === 11) return 'Jacks';
    return value + 's';
}

function updatePlayerHandInfo() {
    const handInfo = document.getElementById('playerHandInfo');
    if (caribbeanAdventureGame.playerHandRank) {
        handInfo.textContent = caribbeanAdventureGame.playerHandRank.name;
        handInfo.className = 'text-lg font-semibold text-neon-pink';
    } else {
        handInfo.textContent = 'High Card';
        handInfo.className = 'text-lg font-semibold text-gray-400';
    }
}

function updateDealerHandInfo() {
    const handInfo = document.getElementById('dealerHandInfo');
    if (caribbeanAdventureGame.dealerHandRank) {
        handInfo.textContent = caribbeanAdventureGame.dealerHandRank.name;
        handInfo.className = 'text-lg font-semibold text-red-400';
    } else {
        handInfo.textContent = 'High Card';
        handInfo.className = 'text-lg font-semibold text-gray-400';
    }
}

function updateAdventureDisplay() {
    document.getElementById('adventureLevel').textContent = caribbeanAdventureGame.adventureLevel;
    document.getElementById('treasurePoints').textContent = caribbeanAdventureGame.treasurePoints.toLocaleString();
    document.getElementById('progressiveJackpot').textContent = '$' + caribbeanAdventureGame.progressiveJackpot.toLocaleString();
}

function updateSessionStats() {
    document.getElementById('handsPlayed').textContent = caribbeanAdventureGame.sessionStats.handsPlayed;
    document.getElementById('handsWon').textContent = caribbeanAdventureGame.sessionStats.handsWon;
    document.getElementById('totalWagered').textContent = caribbeanAdventureGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('biggestWin').textContent = caribbeanAdventureGame.sessionStats.biggestWin.toLocaleString();
}

function resetForNextHand() {
    caribbeanAdventureGame.gamePhase = 'betting';
    caribbeanAdventureGame.playerCards = [];
    caribbeanAdventureGame.dealerCards = [];
    caribbeanAdventureGame.anteBet = 0;
    caribbeanAdventureGame.playBet = 0;
    caribbeanAdventureGame.playerHandRank = null;
    caribbeanAdventureGame.dealerHandRank = null;
    
    // Reset UI
    document.getElementById('dealCards').disabled = false;
    document.getElementById('playButton').disabled = true;
    document.getElementById('foldButton').disabled = true;
    document.getElementById('progressiveCheck').checked = false;
    
    document.getElementById('gameStatus').textContent = 'Begin your next treasure hunt!';
    document.getElementById('gamePhase').textContent = 'Betting Phase';
    document.getElementById('gameResult').innerHTML = '';
    document.getElementById('playerHandInfo').textContent = '';
    document.getElementById('dealerHandInfo').textContent = '';
    document.getElementById('dealerQualification').textContent = '';
    
    // Clear card displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    
    updateProgressiveBet();
}

function resetAdventureGame() {
    resetForNextHand();
    document.getElementById('gameResult').innerHTML = '';
}

function showAdventureRules() {
    document.getElementById('rulesModal').classList.remove('hidden');
}

function hideAdventureRules() {
    document.getElementById('rulesModal').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCaribbeanStudAdventureGame();
});