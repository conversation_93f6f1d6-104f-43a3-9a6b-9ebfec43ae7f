// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadPokerPlaceGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-violet-500/30">
                            <h4 class="text-xl font-bold mb-4 text-violet-400">POKER PLACE</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">ENTRY BET</label>
                                <input type="number" id="pokerBet" value="25" min="5" max="${balance}" 
                                       class="w-full bg-black/50 border border-violet-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">GAME MODE</label>
                                <select id="pokerMode" class="w-full bg-black/50 border border-violet-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="standard">Standard (1x-10x)</option>
                                    <option value="premium">Premium (2x-25x)</option>
                                    <option value="elite">Elite (5x-100x)</option>
                                </select>
                            </div>
                            
                            <button id="joinPokerTable" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                JOIN TABLE
                            </button>
                            
                            <div id="pokerGameActions" class="space-y-2 hidden">
                                <button id="pokerHold" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                    HOLD CARDS
                                </button>
                                <button id="pokerDraw" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                                    DRAW NEW
                                </button>
                                <button id="pokerDouble" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                                    DOUBLE DOWN
                                </button>
                            </div>
                            
                            <div class="text-center mt-4">
                                <div class="text-sm text-gray-400 mb-1">Table Stakes</div>
                                <div id="pokerTableStakes" class="text-xl font-bold text-violet-400">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Hand Value</div>
                                <div id="pokerHandValue" class="text-lg font-bold text-green-400">-</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Multiplier</div>
                                <div id="pokerMultiplier" class="text-lg font-bold text-yellow-400">1x</div>
                            </div>
                        </div>
                        
                        <!-- Premium Payouts -->
                        <div class="bg-black/30 p-4 rounded-xl border border-violet-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-violet-400">PREMIUM PAYOUTS</h5>
                            <div id="pokerPayouts" class="text-sm space-y-1">
                                <!-- Payouts will be generated based on mode -->
                            </div>
                        </div>
                        
                        <!-- Session Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-violet-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-violet-400">SESSION STATS</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Hands Played:</span>
                                    <span id="pokerHandsPlayed" class="text-violet-400">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Hands Won:</span>
                                    <span id="pokerHandsWon" class="text-green-400">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Best Hand:</span>
                                    <span id="pokerBestHand" class="text-yellow-400">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Poker Table -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-violet-500/30">
                            <div id="pokerTable" class="relative bg-gradient-to-br from-violet-900 to-purple-900 rounded-lg p-6 h-96">
                                <!-- Player Hand -->
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-violet-400 mb-2">YOUR HAND</div>
                                        <div id="pokerPlayerHand" class="flex space-x-2">
                                            <!-- Player cards will appear here -->
                                        </div>
                                        <div class="mt-2">
                                            <div id="cardSelections" class="flex space-x-2 hidden">
                                                <!-- Card selection checkboxes will appear here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Dealer/House Area -->
                                <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-purple-400 mb-2">HOUSE STANDARDS</div>
                                        <div class="text-xs text-gray-400">Jacks or Better to Qualify</div>
                                    </div>
                                </div>
                                
                                <!-- Game Info -->
                                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                                    <div class="text-6xl mb-4">♠️</div>
                                    <div id="pokerGamePhase" class="text-xl font-bold text-violet-400">Premium Poker Place</div>
                                    <div id="pokerRoundInfo" class="text-sm text-gray-400 mt-2">Select your stake and join the table</div>
                                </div>
                                
                                <!-- Side Info -->
                                <div class="absolute top-4 left-4">
                                    <div class="text-sm text-gray-400">Mode:</div>
                                    <div id="currentPokerMode" class="text-violet-400 font-bold">Standard</div>
                                </div>
                                
                                <div class="absolute top-4 right-4">
                                    <div class="text-sm text-gray-400">Round:</div>
                                    <div id="pokerRound" class="text-violet-400 font-bold">-</div>
                                </div>
                            </div>
                            <div id="pokerStatus" class="text-center mt-4 text-lg font-semibold">Choose your stake and enter the premium poker experience</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializePokerPlace();
        }
        
        let pokerPlaceGame = {
            isPlaying: false,
            mode: 'standard',
            entryBet: 0,
            totalStakes: 0,
            round: 0,
            deck: [],
            playerHand: [],
            selectedCards: [],
            handsPlayed: 0,
            handsWon: 0,
            bestHand: null,
            doubleDown: false
        };
        
        function initializePokerPlace() {
            document.getElementById('joinPokerTable').addEventListener('click', joinPokerTable);
            document.getElementById('pokerHold').addEventListener('click', holdPokerCards);
            document.getElementById('pokerDraw').addEventListener('click', drawNewCards);
            document.getElementById('pokerDouble').addEventListener('click', doubleDownPoker);
            document.getElementById('pokerMode').addEventListener('change', updatePokerMode);
            
            updatePokerMode();
            initializePokerDeck();
        }
        
        function updatePokerMode() {
            pokerPlaceGame.mode = document.getElementById('pokerMode').value;
            document.getElementById('currentPokerMode').textContent = 
                pokerPlaceGame.mode.charAt(0).toUpperCase() + pokerPlaceGame.mode.slice(1);
            
            updatePokerPayouts();
        }
        
        function updatePokerPayouts() {
            const payoutsDiv = document.getElementById('pokerPayouts');
            const mode = pokerPlaceGame.mode;
            
            const basePayouts = {
                'Royal Flush': 500,
                'Straight Flush': 50,
                'Four of a Kind': 25,
                'Full House': 9,
                'Flush': 6,
                'Straight': 4,
                'Three of a Kind': 3,
                'Two Pair': 2,
                'Jacks or Better': 1
            };
            
            const multipliers = {
                'standard': 1,
                'premium': 2,
                'elite': 4
            };
            
            const multiplier = multipliers[mode];
            
            payoutsDiv.innerHTML = '';
            Object.entries(basePayouts).forEach(([hand, payout]) => {
                const finalPayout = payout * multiplier;
                const row = document.createElement('div');
                row.className = 'flex justify-between';
                row.innerHTML = `
                    <span class="text-gray-400">${hand}:</span>
                    <span class="text-violet-400">${finalPayout}:1</span>
                `;
                payoutsDiv.appendChild(row);
            });
        }
        
        function initializePokerDeck() {
            const suits = ['♠', '♥', '♦', '♣'];
            const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
            
            pokerPlaceGame.deck = [];
            for (const suit of suits) {
                for (const rank of ranks) {
                    pokerPlaceGame.deck.push({ 
                        rank, 
                        suit, 
                        value: getPokerCardValue(rank) 
                    });
                }
            }
            
            shufflePokerDeck();
        }
        
        function shufflePokerDeck() {
            for (let i = pokerPlaceGame.deck.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [pokerPlaceGame.deck[i], pokerPlaceGame.deck[j]] = 
                [pokerPlaceGame.deck[j], pokerPlaceGame.deck[i]];
            }
        }
        
        function getPokerCardValue(rank) {
            if (rank === 'A') return 14;
            if (rank === 'K') return 13;
            if (rank === 'Q') return 12;
            if (rank === 'J') return 11;
            return parseInt(rank);
        }
        
        function joinPokerTable() {
            const entryBet = parseInt(document.getElementById('pokerBet').value);
            
            if (entryBet > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct entry bet
            balance -= entryBet;
            updateBalance();
            
            pokerPlaceGame.isPlaying = true;
            pokerPlaceGame.entryBet = entryBet;
            pokerPlaceGame.totalStakes = entryBet;
            pokerPlaceGame.round = 1;
            pokerPlaceGame.doubleDown = false;
            
            // Update UI
            document.getElementById('joinPokerTable').disabled = true;
            document.getElementById('pokerTableStakes').textContent = '$' + pokerPlaceGame.totalStakes;
            document.getElementById('pokerRound').textContent = pokerPlaceGame.round;
            document.getElementById('pokerGamePhase').textContent = 'Dealing Cards...';
            document.getElementById('pokerStatus').textContent = 'Welcome to the table! Dealing your hand...';
            
            // Deal initial hand
            dealInitialPokerHand();
        }
        
        function dealInitialPokerHand() {
            pokerPlaceGame.playerHand = [];
            pokerPlaceGame.selectedCards = [false, false, false, false, false];
            
            // Clear display
            document.getElementById('pokerPlayerHand').innerHTML = '';
            document.getElementById('cardSelections').innerHTML = '';
            
            // Deal 5 cards
            for (let i = 0; i < 5; i++) {
                const card = pokerPlaceGame.deck.pop();
                pokerPlaceGame.playerHand.push(card);
                
                setTimeout(() => {
                    displayPokerCard(card, i);
                    if (i === 4) {
                        setTimeout(() => {
                            evaluatePokerHand();
                            showPokerActions();
                        }, 500);
                    }
                }, i * 300);
            }
        }
        
        function displayPokerCard(card, index) {
            const cardElement = document.createElement('div');
            cardElement.className = 'w-16 h-20 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-sm font-bold cursor-pointer transition-all hover:scale-105';
            cardElement.dataset.index = index;
            
            const isRed = card.suit === '♥' || card.suit === '♦';
            cardElement.style.color = isRed ? '#dc2626' : '#000';
            
            cardElement.innerHTML = `
                <div>${card.rank}</div>
                <div class="text-lg">${card.suit}</div>
            `;
            
            // Add click listener for card selection
            cardElement.addEventListener('click', () => toggleCardSelection(index));
            
            document.getElementById('pokerPlayerHand').appendChild(cardElement);
            
            // Add selection checkbox
            const selectionDiv = document.createElement('div');
            selectionDiv.className = 'w-16 text-center';
            selectionDiv.innerHTML = `
                <input type="checkbox" id="select${index}" class="card-selector" data-index="${index}">
                <label for="select${index}" class="text-xs text-violet-400 block mt-1">Hold</label>
            `;
            
            document.getElementById('cardSelections').appendChild(selectionDiv);
        }
        
        function toggleCardSelection(index) {
            const checkbox = document.getElementById(`select${index}`);
            checkbox.checked = !checkbox.checked;
            pokerPlaceGame.selectedCards[index] = checkbox.checked;
            
            const cardElement = document.querySelector(`[data-index="${index}"]`);
            if (checkbox.checked) {
                cardElement.classList.add('ring-2', 'ring-violet-400', 'bg-violet-100');
            } else {
                cardElement.classList.remove('ring-2', 'ring-violet-400', 'bg-violet-100');
            }
        }
        
        function showPokerActions() {
            document.getElementById('pokerGameActions').classList.remove('hidden');
            document.getElementById('cardSelections').classList.remove('hidden');
            document.getElementById('pokerGamePhase').textContent = 'Select Cards to Hold';
            document.getElementById('pokerStatus').textContent = 'Select cards to hold, then choose your action';
            
            // Update double down availability
            const doubleDownCost = pokerPlaceGame.entryBet;
            document.getElementById('pokerDouble').disabled = doubleDownCost > balance;
        }
        
        function holdPokerCards() {
            // Just hold selected cards - equivalent to drawing with no selection
            drawNewCards();
        }
        
        function drawNewCards() {
            document.getElementById('pokerGameActions').classList.add('hidden');
            document.getElementById('cardSelections').classList.add('hidden');
            
            document.getElementById('pokerGamePhase').textContent = 'Drawing Cards...';
            document.getElementById('pokerStatus').textContent = 'Drawing new cards...';
            
            // Replace non-selected cards
            let drawDelay = 0;
            for (let i = 0; i < 5; i++) {
                if (!pokerPlaceGame.selectedCards[i]) {
                    setTimeout(() => {
                        const newCard = pokerPlaceGame.deck.pop();
                        pokerPlaceGame.playerHand[i] = newCard;
                        
                        // Update display
                        const cardElement = document.querySelector(`[data-index="${i}"]`);
                        const isRed = newCard.suit === '♥' || newCard.suit === '♦';
                        cardElement.style.color = isRed ? '#dc2626' : '#000';
                        cardElement.innerHTML = `
                            <div>${newCard.rank}</div>
                            <div class="text-lg">${newCard.suit}</div>
                        `;
                        cardElement.classList.remove('ring-2', 'ring-violet-400', 'bg-violet-100');
                    }, drawDelay);
                    drawDelay += 300;
                }
            }
            
            setTimeout(() => {
                evaluatePokerHand();
                endPokerRound();
            }, drawDelay + 500);
        }
        
        function doubleDownPoker() {
            const doubleDownCost = pokerPlaceGame.entryBet;
            
            if (doubleDownCost > balance) {
                alert('Insufficient balance for double down!');
                return;
            }
            
            balance -= doubleDownCost;
            updateBalance();
            
            pokerPlaceGame.totalStakes += doubleDownCost;
            pokerPlaceGame.doubleDown = true;
            
            document.getElementById('pokerTableStakes').textContent = '$' + pokerPlaceGame.totalStakes;
            document.getElementById('pokerStatus').textContent = 'Double down activated! Stakes doubled.';
            
            // Proceed with draw
            drawNewCards();
        }
        
        function evaluatePokerHand() {
            const hand = analyzePokerHand(pokerPlaceGame.playerHand);
            
            document.getElementById('pokerHandValue').textContent = hand.name;
            
            // Calculate multiplier based on hand and mode
            const baseMultiplier = getPokerHandMultiplier(hand.rank);
            const modeMultipliers = { 'standard': 1, 'premium': 2, 'elite': 4 };
            const finalMultiplier = baseMultiplier * modeMultipliers[pokerPlaceGame.mode];
            
            if (pokerPlaceGame.doubleDown) {
                finalMultiplier *= 2;
            }
            
            document.getElementById('pokerMultiplier').textContent = finalMultiplier + 'x';
            
            return { hand, multiplier: finalMultiplier };
        }
        
        function analyzePokerHand(cards) {
            // Sort cards by value
            const sortedCards = [...cards].sort((a, b) => b.value - a.value);
            
            // Check for flush
            const suits = {};
            cards.forEach(card => {
                suits[card.suit] = (suits[card.suit] || 0) + 1;
            });
            const isFlush = Object.values(suits).some(count => count === 5);
            
            // Check for straight
            const values = sortedCards.map(card => card.value);
            let isStraight = true;
            for (let i = 0; i < 4; i++) {
                if (values[i] - values[i + 1] !== 1) {
                    isStraight = false;
                    break;
                }
            }
            
            // Special case: A-2-3-4-5 straight
            if (!isStraight && values[0] === 14 && values[1] === 5 && values[2] === 4 && values[3] === 3 && values[4] === 2) {
                isStraight = true;
            }
            
            // Count values
            const valueCounts = {};
            cards.forEach(card => {
                valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
            });
            
            const counts = Object.values(valueCounts).sort((a, b) => b - a);
            
            // Determine hand rank
            if (isStraight && isFlush) {
                if (values[0] === 14 && values[1] === 13) {
                    return { rank: 9, name: 'Royal Flush' };
                }
                return { rank: 8, name: 'Straight Flush' };
            } else if (counts[0] === 4) {
                return { rank: 7, name: 'Four of a Kind' };
            } else if (counts[0] === 3 && counts[1] === 2) {
                return { rank: 6, name: 'Full House' };
            } else if (isFlush) {
                return { rank: 5, name: 'Flush' };
            } else if (isStraight) {
                return { rank: 4, name: 'Straight' };
            } else if (counts[0] === 3) {
                return { rank: 3, name: 'Three of a Kind' };
            } else if (counts[0] === 2 && counts[1] === 2) {
                return { rank: 2, name: 'Two Pair' };
            } else if (counts[0] === 2) {
                // Check if it's Jacks or Better
                const pairValue = Object.keys(valueCounts).find(key => valueCounts[key] === 2);
                if (parseInt(pairValue) >= 11) {
                    return { rank: 1, name: 'Jacks or Better' };
                }
                return { rank: 0, name: 'Low Pair' };
            } else {
                return { rank: 0, name: 'High Card' };
            }
        }
        
        function getPokerHandMultiplier(handRank) {
            const multipliers = [0, 1, 2, 3, 4, 6, 9, 25, 50, 500];
            return multipliers[handRank] || 0;
        }
        
        function endPokerRound() {
            const result = evaluatePokerHand();
            const { hand, multiplier } = result;
            
            pokerPlaceGame.handsPlayed++;
            document.getElementById('pokerHandsPlayed').textContent = pokerPlaceGame.handsPlayed;
            
            let winnings = 0;
            if (multiplier > 0) {
                winnings = pokerPlaceGame.totalStakes * multiplier;
                balance += winnings;
                updateBalance();
                
                pokerPlaceGame.handsWon++;
                document.getElementById('pokerHandsWon').textContent = pokerPlaceGame.handsWon;
                
                // Update best hand
                if (!pokerPlaceGame.bestHand || hand.rank > pokerPlaceGame.bestHand.rank) {
                    pokerPlaceGame.bestHand = hand;
                    document.getElementById('pokerBestHand').textContent = hand.name;
                }
                
                document.getElementById('pokerGamePhase').textContent = 'Winner!';
                document.getElementById('pokerStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">${hand.name}! Won $${winnings} (${multiplier}x)</span>`;
            } else {
                document.getElementById('pokerGamePhase').textContent = 'Hand Complete';
                document.getElementById('pokerStatus').innerHTML = 
                    `<span class="text-red-400">${hand.name} - No qualifying hand. Lost $${pokerPlaceGame.totalStakes}</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                resetPokerPlace();
            }, 4000);
        }
        
        function resetPokerPlace() {
            pokerPlaceGame.isPlaying = false;
            pokerPlaceGame.entryBet = 0;
            pokerPlaceGame.totalStakes = 0;
            pokerPlaceGame.round = 0;
            pokerPlaceGame.doubleDown = false;
            pokerPlaceGame.selectedCards = [];
            
            // Reset UI
            document.getElementById('joinPokerTable').disabled = false;
            document.getElementById('pokerGameActions').classList.add('hidden');
            document.getElementById('cardSelections').classList.add('hidden');
            document.getElementById('pokerTableStakes').textContent = '0 GA';
            document.getElementById('pokerHandValue').textContent = '-';
            document.getElementById('pokerMultiplier').textContent = '1x';
            document.getElementById('pokerRound').textContent = '-';
            document.getElementById('pokerGamePhase').textContent = 'Premium Poker Place';
            document.getElementById('pokerStatus').textContent = 'Choose your stake and enter the premium poker experience';
            
            // Clear cards
            document.getElementById('pokerPlayerHand').innerHTML = '';
            
            // Reshuffle if deck is low
            if (pokerPlaceGame.deck.length < 20) {
                initializePokerDeck();
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPokerPlaceGame();
});