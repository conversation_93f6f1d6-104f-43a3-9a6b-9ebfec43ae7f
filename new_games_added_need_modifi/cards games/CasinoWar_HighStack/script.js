// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// CasinoWar HighStack Implementation with extreme high stakes
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_VALUES = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

// High stakes payouts with massive betting requirements
const HIGH_STACK_PAYOUTS = {
    WIN: 1.0,           // Standard 1:1
    WAR_WIN: 2.0,       // Standard 2:1
    TIE_BONUS: 10,      // 10:1 for tie bet
    SURRENDER: 0.5,     // 50% return on surrender
    VIP_MULTIPLIER: 1.5 // VIP bonus multiplier
};

// High stakes tiers with massive minimum bets
const STAKE_TIERS = [
    {
        name: 'high_roller',
        description: 'High Roller (Min: 500 GA)',
        minBet: 500,
        maxBet: 5000,
        vipBonus: 1.1,
        warAdvantage: 0.95
    },
    {
        name: 'whale',
        description: '<PERSON>hale (Min: 2000 GA)',
        minBet: 2000,
        maxBet: 20000,
        vipBonus: 1.25,
        warAdvantage: 0.90
    },
    {
        name: 'legend',
        description: 'Legend (Min: 10000 GA)',
        minBet: 10000,
        maxBet: 100000,
        vipBonus: 1.5,
        warAdvantage: 0.85
    }
];

let highStackGame = {
    deck: [],
    playerCard: null,
    dealerCard: null,
    warCards: [],
    gamePhase: 'betting',
    betAmount: 0,
    tieBet: 0,
    isInWar: false,
    warRound: 0,
    totalWin: 0,
    currentTier: 'high_roller',
    vipLevel: 1,
    prestigePoints: 0,
    sessionStats: {
        handsPlayed: 0,
        handsWon: 0,
        warsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        surrenders: 0,
        tieWins: 0,
        highestBet: 0
    }
};

function loadHighStackGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-6xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-4xl font-bold cyber-title mb-4">CasinoWar HighStack</h3>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div>VIP Level: <span id="vipLevel" class="text-neon-purple">1</span></div>
                    <div>Prestige: <span id="prestigePoints" class="text-neon-blue">0</span></div>
                    <div>Current Tier: <span id="currentTier" class="text-neon-green">High Roller</span></div>
                </div>
                <div class="mt-2">
                    <select id="stakeTierSelect" class="cyber-select bg-cyber-dark border border-neon-purple rounded px-4 py-2">
                        <option value="high_roller">High Roller (Min: 500 GA)</option>
                        <option value="whale">Whale (Min: 2000 GA)</option>
                        <option value="legend">Legend (Min: 10000 GA)</option>
                    </select>
                </div>
            </div>

            <!-- High Stakes Arena -->
            <div class="cyber-panel p-8">
                <!-- Dealer Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-red-400 mb-4">🎩 Elite Dealer</h4>
                    <div id="dealerCardArea" class="flex justify-center mb-4 min-h-[140px] items-center">
                        <div id="dealerCardSlot" class="card-slot border-2 border-red-500/30 rounded-lg w-32 h-44 flex items-center justify-center text-red-400">
                            Premium Table Ready
                        </div>
                    </div>
                    <div id="dealerCardValue" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- War Zone -->
                <div class="text-center mb-6">
                    <div id="warStatus" class="text-2xl font-bold text-neon-pink mb-4">Place your high stakes bet!</div>
                    <div id="warPhase" class="text-lg text-neon-blue">High Stakes Phase</div>
                    <div id="warCards" class="flex justify-center space-x-2 mt-4 min-h-[80px]">
                        <!-- War cards will appear here -->
                    </div>
                </div>

                <!-- Player Area -->
                <div class="text-center mb-8">
                    <h4 class="text-xl font-semibold text-neon-purple mb-4">💎 VIP Player</h4>
                    <div id="playerCardArea" class="flex justify-center mb-4 min-h-[140px] items-center">
                        <div id="playerCardSlot" class="card-slot border-2 border-neon-purple/30 rounded-lg w-32 h-44 flex items-center justify-center text-neon-purple">
                            Awaiting High Stakes
                        </div>
                    </div>
                    <div id="playerCardValue" class="text-lg font-semibold text-gray-300"></div>
                </div>

                <!-- High Stakes Betting -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <!-- Main Bet -->
                    <div class="cyber-card border-neon-pink">
                        <h5 class="text-lg font-semibold text-neon-pink mb-3">💰 High Stakes Bet</h5>
                        <div class="space-y-3">
                            <div class="text-center">
                                <div class="text-sm text-gray-400" id="betRange">Range: 500 - 5,000 GA</div>
                                <input type="number" id="stakeBetInput" value="500" min="500" max="5000" step="100"
                                       class="w-full bg-black/50 border border-neon-pink/30 rounded px-3 py-2 text-white text-center text-xl font-bold">
                            </div>
                            <div class="text-center">
                                <span id="stakeBetAmount" class="text-2xl font-bold text-neon-pink">500 GA</span>
                            </div>
                            <button id="dealCards" class="w-full cyber-button-primary py-3 rounded-lg font-semibold text-lg">
                                DEAL HIGH STAKES
                            </button>
                        </div>
                    </div>

                    <!-- War Actions -->
                    <div class="cyber-card border-neon-blue">
                        <h5 class="text-lg font-semibold text-neon-blue mb-3">⚔️ War Stakes</h5>
                        <div class="space-y-3">
                            <div class="text-center text-gray-400">
                                <div>War Bet: Double Stakes</div>
                                <div id="warBetAmount" class="text-xl font-bold text-neon-blue">0 GA</div>
                                <div class="text-sm">VIP Bonus: <span id="vipBonus">+10%</span></div>
                            </div>
                            <button id="goToWar" class="w-full cyber-button-secondary py-2 rounded-lg font-semibold" disabled>
                                DECLARE WAR
                            </button>
                            <button id="surrender" class="w-full cyber-button-danger py-2 rounded-lg font-semibold" disabled>
                                SURRENDER (50%)
                            </button>
                        </div>
                    </div>

                    <!-- Tie Bet -->
                    <div class="cyber-card border-neon-green">
                        <h5 class="text-lg font-semibold text-neon-green mb-3">🎯 Tie Bet</h5>
                        <div class="space-y-3">
                            <div class="text-center">
                                <div class="text-sm text-gray-400">Tie bet (10:1 payout)</div>
                                <input type="number" id="tieBetInput" value="0" min="0" max="1000" step="50"
                                       class="w-full bg-black/50 border border-neon-green/30 rounded px-2 py-1 text-white text-center">
                            </div>
                            <label class="flex items-center justify-center space-x-2">
                                <input type="checkbox" id="tieBetCheck" class="accent-neon-green">
                                <span class="text-neon-green">Bet on Tie</span>
                            </label>
                            <div class="text-center text-xs text-gray-400">
                                Max: 1,000 GA
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Bet Buttons -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button onclick="setQuickBet(500)" class="cyber-button-secondary px-4 py-2 rounded-lg font-semibold">
                        500 GA
                    </button>
                    <button onclick="setQuickBet(1000)" class="cyber-button-secondary px-4 py-2 rounded-lg font-semibold">
                        1K GA
                    </button>
                    <button onclick="setQuickBet(2500)" class="cyber-button-secondary px-4 py-2 rounded-lg font-semibold">
                        2.5K GA
                    </button>
                    <button onclick="setQuickBet(5000)" class="cyber-button-secondary px-4 py-2 rounded-lg font-semibold">
                        5K GA
                    </button>
                    <button onclick="setMaxBet()" class="cyber-button-primary px-4 py-2 rounded-lg font-semibold">
                        MAX BET
                    </button>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4 mb-6">
                    <button id="newHandButton" class="cyber-button-primary px-8 py-3 rounded-lg font-semibold" 
                            onclick="resetHighStackGame()">
                        NEW HAND
                    </button>
                    <button id="rulesButton" class="cyber-button-secondary px-8 py-3 rounded-lg font-semibold"
                            onclick="showHighStackRules()">
                        HIGH STAKES RULES
                    </button>
                </div>

                <!-- Result Display -->
                <div id="gameResult" class="text-center text-2xl font-bold min-h-[60px] flex items-center justify-center">
                    <!-- Result will appear here -->
                </div>
            </div>

            <!-- High Stakes Statistics -->
            <div class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="cyber-card border-neon-purple text-center">
                    <div class="text-2xl font-bold text-neon-purple" id="handsPlayed">0</div>
                    <div class="text-sm text-gray-400">Hands Played</div>
                </div>
                <div class="cyber-card border-neon-green text-center">
                    <div class="text-2xl font-bold text-neon-green" id="handsWon">0</div>
                    <div class="text-sm text-gray-400">Hands Won</div>
                </div>
                <div class="cyber-card border-neon-pink text-center">
                    <div class="text-2xl font-bold text-neon-pink" id="totalWagered">0</div>
                    <div class="text-sm text-gray-400">Total Wagered</div>
                </div>
                <div class="cyber-card border-neon-blue text-center">
                    <div class="text-2xl font-bold text-neon-blue" id="biggestWin">0</div>
                    <div class="text-sm text-gray-400">Biggest Win</div>
                </div>
            </div>

            <!-- VIP Benefits -->
            <div class="mt-8 cyber-panel">
                <h4 class="text-xl font-bold text-neon-purple mb-4 text-center">VIP High Stakes Benefits</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div class="text-center">
                        <div class="font-bold text-neon-pink">High Roller Tier</div>
                        <div class="text-gray-300">Min: 500 GA | Bonus: +10%</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-blue">Whale Tier</div>
                        <div class="text-gray-300">Min: 2,000 GA | Bonus: +25%</div>
                    </div>
                    <div class="text-center">
                        <div class="font-bold text-neon-green">Legend Tier</div>
                        <div class="text-gray-300">Min: 10,000 GA | Bonus: +50%</div>
                    </div>
                </div>
                <div class="text-center mt-4 text-xs text-gray-400">
                    💎 Higher stakes unlock better VIP bonuses and exclusive features!
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div id="rulesModal" class="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 hidden flex items-center justify-center">
            <div class="cyber-panel max-w-2xl mx-4">
                <h3 class="text-2xl font-bold text-neon-purple mb-4">CasinoWar HighStack Rules</h3>
                <div class="space-y-4 text-gray-300">
                    <p><strong class="text-neon-pink">Objective:</strong> Beat the dealer with a higher card in high stakes combat!</p>
                    <p><strong class="text-neon-blue">High Stakes System:</strong></p>
                    <ul class="list-disc list-inside space-y-2 ml-4">
                        <li>Minimum bets start at 500 GA</li>
                        <li>Higher card wins (Ace = 14, King = 13, etc.)</li>
                        <li>Ties trigger WAR - bet doubles automatically</li>
                        <li>War winner gets 2:1 payout + VIP bonus</li>
                        <li>Surrender returns 50% of bet</li>
                        <li>Optional tie bet pays 10:1</li>
                    </ul>
                    <p><strong class="text-neon-green">VIP Tiers:</strong> Higher stakes unlock better bonuses!</p>
                    <p><strong class="text-yellow-400">Warning:</strong> High stakes mean high rewards but also high risks!</p>
                </div>
                <button onclick="hideHighStackRules()" class="mt-6 cyber-button-primary px-6 py-2 rounded-lg font-semibold">
                    CLOSE RULES
                </button>
            </div>
        </div>
    `;
    
    setupHighStackGame();
}

function setupHighStackGame() {
    // Event listeners
    document.getElementById('stakeBetInput').addEventListener('input', updateStakeBet);
    document.getElementById('dealCards').addEventListener('click', dealHighStackCards);
    document.getElementById('goToWar').addEventListener('click', declareHighStackWar);
    document.getElementById('surrender').addEventListener('click', surrenderHighStack);
    document.getElementById('tieBetCheck').addEventListener('change', updateTieBet);
    document.getElementById('tieBetInput').addEventListener('input', updateTieBet);
    document.getElementById('stakeTierSelect').addEventListener('change', updateStakeTier);
    
    updateHighStackDisplay();
    updateHighStackStats();
}

function updateStakeTier() {
    const tier = document.getElementById('stakeTierSelect').value;
    highStackGame.currentTier = tier;
    
    const tierData = STAKE_TIERS.find(t => t.name === tier);
    document.getElementById('currentTier').textContent = tierData.description.split(' (')[0];
    document.getElementById('betRange').textContent = `Range: ${tierData.minBet.toLocaleString()} - ${tierData.maxBet.toLocaleString()} GA`;
    document.getElementById('vipBonus').textContent = `+${Math.round((tierData.vipBonus - 1) * 100)}%`;
    
    // Update bet input constraints
    const betInput = document.getElementById('stakeBetInput');
    betInput.min = tierData.minBet;
    betInput.max = tierData.maxBet;
    betInput.value = tierData.minBet;
    
    updateStakeBet();
}

function updateStakeBet() {
    const stakeBet = parseInt(document.getElementById('stakeBetInput').value);
    const tierData = STAKE_TIERS.find(t => t.name === highStackGame.currentTier);
    
    // Enforce tier limits
    const clampedBet = Math.max(tierData.minBet, Math.min(tierData.maxBet, stakeBet));
    document.getElementById('stakeBetInput').value = clampedBet;
    
    document.getElementById('stakeBetAmount').textContent = `${clampedBet.toLocaleString()} GA`;
    document.getElementById('warBetAmount').textContent = `${(clampedBet * 2).toLocaleString()} GA`;
}

function updateTieBet() {
    const isChecked = document.getElementById('tieBetCheck').checked;
    const tieBetAmount = parseInt(document.getElementById('tieBetInput').value) || 0;
    highStackGame.tieBet = isChecked ? tieBetAmount : 0;
}

function setQuickBet(amount) {
    const tierData = STAKE_TIERS.find(t => t.name === highStackGame.currentTier);
    const clampedAmount = Math.max(tierData.minBet, Math.min(tierData.maxBet, amount));
    document.getElementById('stakeBetInput').value = clampedAmount;
    updateStakeBet();
}

function setMaxBet() {
    const tierData = STAKE_TIERS.find(t => t.name === highStackGame.currentTier);
    const maxAffordable = Math.min(tierData.maxBet, balance);
    document.getElementById('stakeBetInput').value = maxAffordable;
    updateStakeBet();
}

function createHighStackDeck() {
    const deck = [];
    
    // Create standard deck
    for (const suit of CARD_SUITS) {
        for (const value of CARD_VALUES) {
            deck.push({ suit, value });
        }
    }
    
    return shuffleDeck(deck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function createHighStackCardElement(card, isHidden = false) {
    const cardElement = document.createElement('div');
    cardElement.className = 'high-stack-card inline-block w-28 h-36 bg-white rounded-lg border-2 border-gray-300 m-1 flex flex-col items-center justify-center text-xl font-bold shadow-xl transition-all duration-500 hover:scale-105';
    
    if (isHidden) {
        cardElement.innerHTML = '💎';
        cardElement.className += ' bg-gradient-to-br from-purple-900 to-black text-purple-400';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.innerHTML = `
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-lg">${card.value}</div>
            <div class="${isRed ? 'text-red-600' : 'text-black'} text-3xl">${card.suit}</div>
        `;
    }
    
    return cardElement;
}

function dealHighStackCards() {
    const stakeBet = parseInt(document.getElementById('stakeBetInput').value);
    const tierData = STAKE_TIERS.find(t => t.name === highStackGame.currentTier);
    
    if (stakeBet < tierData.minBet) {
        document.getElementById('warStatus').textContent = `Minimum bet for ${tierData.description} is ${tierData.minBet} GA!`;
        return;
    }
    
    if (balance < stakeBet + highStackGame.tieBet) {
        document.getElementById('warStatus').textContent = 'Insufficient balance for high stakes!';
        return;
    }
    
    // Deduct bets
    balance -= stakeBet + highStackGame.tieBet;
    updateBalance();
    
    // Initialize game
    highStackGame.deck = createHighStackDeck();
    highStackGame.betAmount = stakeBet;
    highStackGame.gamePhase = 'dealt';
    highStackGame.isInWar = false;
    highStackGame.warRound = 0;
    highStackGame.totalWin = 0;
    
    // Deal cards
    highStackGame.playerCard = highStackGame.deck.pop();
    highStackGame.dealerCard = highStackGame.deck.pop();
    
    displayHighStackCards();
    
    // Disable deal button
    document.getElementById('dealCards').disabled = true;
    
    document.getElementById('warStatus').textContent = 'Cards dealt! Determining outcome...';
    document.getElementById('warPhase').textContent = 'Resolution Phase';
    
    // Update stats
    highStackGame.sessionStats.highestBet = Math.max(highStackGame.sessionStats.highestBet, stakeBet);
    
    // Determine outcome
    setTimeout(() => {
        determineHighStackOutcome();
    }, 2000);
    
    updateHighStackDisplay();
}

function displayHighStackCards() {
    // Display player card
    const playerArea = document.getElementById('playerCardSlot');
    playerArea.innerHTML = '';
    const playerCardElement = createHighStackCardElement(highStackGame.playerCard);
    playerCardElement.classList.add('animate-pulse');
    playerArea.appendChild(playerCardElement);
    
    // Display dealer card
    const dealerArea = document.getElementById('dealerCardSlot');
    dealerArea.innerHTML = '';
    const dealerCardElement = createHighStackCardElement(highStackGame.dealerCard);
    dealerCardElement.classList.add('animate-pulse');
    dealerArea.appendChild(dealerCardElement);
    
    // Update card values
    document.getElementById('playerCardValue').textContent = 
        `Your Card: ${highStackGame.playerCard.value} ${highStackGame.playerCard.suit}`;
    document.getElementById('dealerCardValue').textContent = 
        `Dealer Card: ${highStackGame.dealerCard.value} ${highStackGame.dealerCard.suit}`;
}

function determineHighStackOutcome() {
    const playerValue = getCardValue(highStackGame.playerCard);
    const dealerValue = getCardValue(highStackGame.dealerCard);
    const tierData = STAKE_TIERS.find(t => t.name === highStackGame.currentTier);
    
    let totalWinnings = 0;
    let resultText = '';
    
    // Check tie bet first
    if (highStackGame.tieBet > 0 && playerValue === dealerValue) {
        const tiePayout = highStackGame.tieBet * HIGH_STACK_PAYOUTS.TIE_BONUS;
        totalWinnings += tiePayout + highStackGame.tieBet;
        resultText += `🎯 TIE BET WINS! +${tiePayout.toLocaleString()} GA! `;
        highStackGame.sessionStats.tieWins++;
    }
    
    if (playerValue > dealerValue) {
        // Player wins
        const basePayout = highStackGame.betAmount * HIGH_STACK_PAYOUTS.WIN;
        const vipBonus = Math.floor(basePayout * (tierData.vipBonus - 1));
        const totalPayout = basePayout + vipBonus;
        totalWinnings += highStackGame.betAmount + totalPayout;
        resultText += `🏆 HIGH STAKES WIN! ${highStackGame.playerCard.value} beats ${highStackGame.dealerCard.value}! +${totalPayout.toLocaleString()} GA`;
        if (vipBonus > 0) {
            resultText += ` (VIP Bonus: +${vipBonus.toLocaleString()})`;
        }
        highStackGame.sessionStats.handsWon++;
        highStackGame.prestigePoints += Math.floor(totalPayout / 100);
    } else if (playerValue < dealerValue) {
        // Dealer wins
        resultText += `💔 DEALER WINS! ${highStackGame.dealerCard.value} beats ${highStackGame.playerCard.value}!`;
    } else {
        // Tie - trigger war
        if (!highStackGame.isInWar) {
            resultText += `⚔️ TIE! War declared between ${highStackGame.playerCard.value}s!`;
            enableHighStackWarOptions();
            return;
        }
    }
    
    // Add winnings
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    highStackGame.sessionStats.handsPlayed++;
    highStackGame.sessionStats.totalWagered += highStackGame.betAmount + highStackGame.tieBet;
    highStackGame.sessionStats.totalWon += totalWinnings;
    highStackGame.sessionStats.biggestWin = Math.max(highStackGame.sessionStats.biggestWin, totalWinnings);
    
    // Check for VIP level up
    checkVIPLevelUp();
    
    const winColor = totalWinnings > 0 ? 'text-neon-green' : 'text-red-400';
    document.getElementById('gameResult').innerHTML = `<span class="${winColor}">${resultText}</span>`;
    
    updateHighStackStats();
    updateHighStackDisplay();
    
    setTimeout(() => {
        resetForNextHand();
    }, 4000);
}

function enableHighStackWarOptions() {
    document.getElementById('goToWar').disabled = false;
    document.getElementById('surrender').disabled = false;
    document.getElementById('warStatus').textContent = 'Choose your war action!';
    document.getElementById('warPhase').textContent = 'War Decision Phase';
}

function declareHighStackWar() {
    if (balance < highStackGame.betAmount) {
        document.getElementById('warStatus').textContent = 'Insufficient balance for war!';
        return;
    }
    
    // Deduct war bet
    balance -= highStackGame.betAmount;
    updateBalance();
    
    highStackGame.isInWar = true;
    highStackGame.warRound++;
    highStackGame.gamePhase = 'war';
    
    // Deal war cards (3 hidden + 1 battle card each)
    dealHighStackWarCards();
    
    document.getElementById('goToWar').disabled = true;
    document.getElementById('surrender').disabled = true;
    document.getElementById('warStatus').textContent = 'WAR ENGAGED! Final cards being dealt...';
    document.getElementById('warPhase').textContent = `War Round ${highStackGame.warRound}`;
    
    setTimeout(() => {
        resolveHighStackWar();
    }, 3000);
}

function dealHighStackWarCards() {
    const warCardsContainer = document.getElementById('warCards');
    warCardsContainer.innerHTML = '';
    
    highStackGame.warCards = [];
    
    // Deal 3 hidden cards + 1 battle card for each side
    for (let i = 0; i < 8; i++) {
        const card = highStackGame.deck.pop();
        highStackGame.warCards.push(card);
        
        const cardElement = createHighStackCardElement(card, i < 6); // First 6 are hidden
        cardElement.style.animationDelay = `${i * 0.3}s`;
        cardElement.classList.add('animate-pulse');
        warCardsContainer.appendChild(cardElement);
    }
}

function resolveHighStackWar() {
    const tierData = STAKE_TIERS.find(t => t.name === highStackGame.currentTier);
    
    // Get final battle cards (last 2 cards dealt)
    const playerWarCard = highStackGame.warCards[6];
    const dealerWarCard = highStackGame.warCards[7];
    
    const playerValue = getCardValue(playerWarCard);
    const dealerValue = getCardValue(dealerWarCard);
    
    let totalWinnings = 0;
    let resultText = '';
    
    if (playerValue > dealerValue) {
        // Player wins war
        const basePayout = highStackGame.betAmount * 2 * HIGH_STACK_PAYOUTS.WAR_WIN;
        const vipBonus = Math.floor(basePayout * (tierData.vipBonus - 1));
        const totalPayout = basePayout + vipBonus;
        totalWinnings += highStackGame.betAmount * 2 + totalPayout;
        resultText = `🎖️ WAR VICTORY! ${playerWarCard.value} conquers ${dealerWarCard.value}! +${totalPayout.toLocaleString()} GA`;
        if (vipBonus > 0) {
            resultText += ` (VIP Bonus: +${vipBonus.toLocaleString()})`;
        }
        highStackGame.sessionStats.warsWon++;
        highStackGame.sessionStats.handsWon++;
        highStackGame.prestigePoints += Math.floor(totalPayout / 50);
    } else if (playerValue === dealerValue) {
        // Another tie - very rare
        resultText = `💥 Another tie! ${playerWarCard.value} vs ${dealerWarCard.value} - Push!`;
        totalWinnings += highStackGame.betAmount * 2; // Return war bets
    } else {
        // Dealer wins war
        resultText = `💔 WAR LOST! ${dealerWarCard.value} defeats ${playerWarCard.value}! All stakes lost!`;
    }
    
    // Add winnings
    balance += totalWinnings;
    updateBalance();
    
    // Update stats
    highStackGame.sessionStats.handsPlayed++;
    highStackGame.sessionStats.totalWagered += highStackGame.betAmount;
    highStackGame.sessionStats.totalWon += totalWinnings;
    highStackGame.sessionStats.biggestWin = Math.max(highStackGame.sessionStats.biggestWin, totalWinnings);
    
    const winColor = totalWinnings > 0 ? 'text-neon-green' : 'text-red-400';
    document.getElementById('gameResult').innerHTML = `<span class="${winColor}">${resultText}</span>`;
    
    updateHighStackStats();
    updateHighStackDisplay();
    
    setTimeout(() => {
        resetForNextHand();
    }, 5000);
}

function surrenderHighStack() {
    // Return 50% of bet
    const surrenderReturn = Math.floor(highStackGame.betAmount * HIGH_STACK_PAYOUTS.SURRENDER);
    balance += surrenderReturn;
    updateBalance();
    
    highStackGame.sessionStats.handsPlayed++;
    highStackGame.sessionStats.surrenders++;
    highStackGame.sessionStats.totalWagered += highStackGame.betAmount + highStackGame.tieBet;
    highStackGame.sessionStats.totalWon += surrenderReturn;
    
    document.getElementById('gameResult').innerHTML = 
        `<span class="text-yellow-400">🏳️ SURRENDER! ${surrenderReturn.toLocaleString()} GA returned (50%).</span>`;
    
    updateHighStackStats();
    
    setTimeout(() => {
        resetForNextHand();
    }, 3000);
}

function getCardValue(card) {
    if (card.value === 'A') return 14;
    if (card.value === 'K') return 13;
    if (card.value === 'Q') return 12;
    if (card.value === 'J') return 11;
    return parseInt(card.value);
}

function checkVIPLevelUp() {
    const newLevel = Math.floor(highStackGame.prestigePoints / 5000) + 1;
    if (newLevel > highStackGame.vipLevel) {
        highStackGame.vipLevel = newLevel;
        document.getElementById('warStatus').textContent = `💎 VIP LEVEL UP! Now Level ${newLevel}!`;
    }
}

function updateHighStackDisplay() {
    document.getElementById('vipLevel').textContent = highStackGame.vipLevel;
    document.getElementById('prestigePoints').textContent = highStackGame.prestigePoints.toLocaleString();
}

function updateHighStackStats() {
    document.getElementById('handsPlayed').textContent = highStackGame.sessionStats.handsPlayed;
    document.getElementById('handsWon').textContent = highStackGame.sessionStats.handsWon;
    document.getElementById('totalWagered').textContent = highStackGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('biggestWin').textContent = highStackGame.sessionStats.biggestWin.toLocaleString();
}

function resetForNextHand() {
    highStackGame.gamePhase = 'betting';
    highStackGame.playerCard = null;
    highStackGame.dealerCard = null;
    highStackGame.warCards = [];
    highStackGame.betAmount = 0;
    highStackGame.tieBet = 0;
    highStackGame.isInWar = false;
    highStackGame.warRound = 0;
    
    // Reset UI
    document.getElementById('dealCards').disabled = false;
    document.getElementById('goToWar').disabled = true;
    document.getElementById('surrender').disabled = true;
    document.getElementById('tieBetCheck').checked = false;
    document.getElementById('tieBetInput').value = 0;
    
    document.getElementById('warStatus').textContent = 'Place your high stakes bet!';
    document.getElementById('warPhase').textContent = 'High Stakes Phase';
    document.getElementById('gameResult').innerHTML = '';
    document.getElementById('playerCardValue').textContent = '';
    document.getElementById('dealerCardValue').textContent = '';
    
    // Reset card displays
    document.getElementById('playerCardSlot').innerHTML = 'Awaiting High Stakes';
    document.getElementById('playerCardSlot').className = 'card-slot border-2 border-neon-purple/30 rounded-lg w-32 h-44 flex items-center justify-center text-neon-purple';
    
    document.getElementById('dealerCardSlot').innerHTML = 'Premium Table Ready';
    document.getElementById('dealerCardSlot').className = 'card-slot border-2 border-red-500/30 rounded-lg w-32 h-44 flex items-center justify-center text-red-400';
    
    document.getElementById('warCards').innerHTML = '';
    
    updateTieBet();
}

function resetHighStackGame() {
    resetForNextHand();
    document.getElementById('gameResult').innerHTML = '';
}

function showHighStackRules() {
    document.getElementById('rulesModal').classList.remove('hidden');
}

function hideHighStackRules() {
    document.getElementById('rulesModal').classList.add('hidden');
}

// Add these enhancements to the existing game

// Enhanced sound system for high stakes atmosphere
const HIGH_STACK_SOUNDS = {
    cardDeal: new Audio('assets/sounds/card-deal.mp3'),
    warDeclared: new Audio('assets/sounds/war-horn.mp3'),
    bigWin: new Audio('assets/sounds/big-win.mp3'),
    vipLevelUp: new Audio('assets/sounds/vip-fanfare.mp3'),
    surrender: new Audio('assets/sounds/surrender.mp3'),
    tieWin: new Audio('assets/sounds/tie-win.mp3')
};

// Auto-bet system for high rollers
let autoBetSettings = {
    enabled: false,
    betAmount: 500,
    stopOnWin: false,
    stopOnLoss: false,
    maxRounds: 10,
    currentRound: 0
};

// Tournament mode for competitive high stakes
let tournamentMode = {
    active: false,
    entryFee: 10000,
    prizePool: 0,
    participants: 1,
    roundsRemaining: 10,
    leaderboard: []
};

// Add provably fair system
function generateHighStackSeeds() {
    const clientSeed = Math.random().toString(36).substring(2, 15);
    const serverSeed = Math.random().toString(36).substring(2, 15);
    const nonce = Date.now();
    
    return { clientSeed, serverSeed, nonce };
}

// Enhanced statistics tracking
function trackAdvancedStats(outcome, betAmount, winAmount) {
    const stats = highStackGame.sessionStats;
    
    // Track win/loss streaks
    if (winAmount > betAmount) {
        stats.currentWinStreak = (stats.currentWinStreak || 0) + 1;
        stats.longestWinStreak = Math.max(stats.longestWinStreak || 0, stats.currentWinStreak);
        stats.currentLossStreak = 0;
    } else if (winAmount === 0) {
        stats.currentLossStreak = (stats.currentLossStreak || 0) + 1;
        stats.longestLossStreak = Math.max(stats.longestLossStreak || 0, stats.currentLossStreak);
        stats.currentWinStreak = 0;
    }
    
    // Track hourly performance
    const hour = new Date().getHours();
    if (!stats.hourlyPerformance) stats.hourlyPerformance = {};
    if (!stats.hourlyPerformance[hour]) {
        stats.hourlyPerformance[hour] = { wins: 0, losses: 0, profit: 0 };
    }
    
    if (winAmount > betAmount) {
        stats.hourlyPerformance[hour].wins++;
        stats.hourlyPerformance[hour].profit += (winAmount - betAmount);
    } else if (winAmount === 0) {
        stats.hourlyPerformance[hour].losses++;
        stats.hourlyPerformance[hour].profit -= betAmount;
    }
}

// Add achievement system
const HIGH_STACK_ACHIEVEMENTS = {
    'first_war': { name: 'First War', description: 'Win your first war', reward: 1000 },
    'high_roller': { name: 'High Roller', description: 'Bet 5,000 GA in a single hand', reward: 2500 },
    'war_master': { name: 'War Master', description: 'Win 10 wars', reward: 5000 },
    'legend_tier': { name: 'Legend Status', description: 'Reach Legend tier', reward: 10000 },
    'big_winner': { name: 'Big Winner', description: 'Win 50,000 GA in a single hand', reward: 25000 }
};

function checkAchievements(betAmount, winAmount, isWar) {
    const achievements = highStackGame.achievements || {};
    
    // Check for new achievements
    if (isWar && winAmount > betAmount && !achievements.first_war) {
        unlockAchievement('first_war');
    }
    
    if (betAmount >= 5000 && !achievements.high_roller) {
        unlockAchievement('high_roller');
    }
    
    if (winAmount >= 50000 && !achievements.big_winner) {
        unlockAchievement('big_winner');
    }
}

function unlockAchievement(achievementId) {
    const achievement = HIGH_STACK_ACHIEVEMENTS[achievementId];
    if (!highStackGame.achievements) highStackGame.achievements = {};
    
    highStackGame.achievements[achievementId] = true;
    balance += achievement.reward;
    
    showAchievementNotification(achievement);
    updateBalance();
}

function showAchievementNotification(achievement) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-gradient-to-r from-yellow-400 to-yellow-600 text-black p-4 rounded-lg shadow-xl z-50 animate-bounce';
    notification.innerHTML = `
        <div class="font-bold">🏆 Achievement Unlocked!</div>
        <div>${achievement.name}</div>
        <div class="text-sm">${achievement.description}</div>
        <div class="text-sm font-bold">Reward: +${achievement.reward} GA</div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Add side bet system
const SIDE_BETS = {
    'suited_match': { name: 'Suited Match', payout: 4, description: 'Both cards same suit' },
    'color_match': { name: 'Color Match', payout: 2, description: 'Both cards same color' },
    'royal_war': { name: 'Royal War', payout: 25, description: 'War with face cards' },
    'ace_duel': { name: 'Ace Duel', payout: 50, description: 'War with Aces' }
};

function addSideBetOptions() {
    const sideBetHTML = `
        <div class="cyber-card border-neon-yellow mt-4">
            <h5 class="text-lg font-semibold text-neon-yellow mb-3">🎲 Side Bets</h5>
            <div class="grid grid-cols-2 gap-2 text-sm">
                ${Object.entries(SIDE_BETS).map(([id, bet]) => `
                    <label class="flex items-center space-x-2">
                        <input type="checkbox" id="sideBet_${id}" class="accent-neon-yellow">
                        <span>${bet.name} (${bet.payout}:1)</span>
                    </label>
                    <input type="number" id="sideBetAmount_${id}" value="0" min="0" max="500" 
                           class="w-full bg-black/50 border border-neon-yellow/30 rounded px-2 py-1 text-white text-xs">
                `).join('')}
            </div>
        </div>
    `;
    
    // Insert after tie bet section
    const tieBetCard = document.querySelector('.cyber-card.border-neon-green');
    if (tieBetCard) {
        tieBetCard.insertAdjacentHTML('afterend', sideBetHTML);
    }
}

// Enhanced auto-bet system
function setupAutoBet() {
    const autoBetHTML = `
        <div class="cyber-panel mt-8">
            <h4 class="text-xl font-bold text-neon-blue mb-4 text-center">🤖 Auto-Bet System</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm mb-2">Bet Amount</label>
                    <input type="number" id="autoBetAmount" value="500" min="500" max="10000" 
                           class="w-full bg-black/50 border border-neon-blue/30 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="block text-sm mb-2">Max Rounds</label>
                    <input type="number" id="autoMaxRounds" value="10" min="1" max="100" 
                           class="w-full bg-black/50 border border-neon-blue/30 rounded px-3 py-2 text-white">
                </div>
                <div>
                    <label class="flex items-center space-x-2 mt-6">
                        <input type="checkbox" id="autoStopOnBigWin" class="accent-neon-blue">
                        <span class="text-sm">Stop on 10x win</span>
                    </label>
                </div>
                <div class="flex items-end">
                    <button id="toggleAutoBet" class="w-full cyber-button-secondary py-2 rounded-lg font-semibold">
                        START AUTO-BET
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Add after statistics section
    const statsSection = document.querySelector('.mt-8.grid.grid-cols-2');
    if (statsSection) {
        statsSection.insertAdjacentHTML('afterend', autoBetHTML);
        
        document.getElementById('toggleAutoBet').addEventListener('click', toggleAutoBet);
    }
}

function toggleAutoBet() {
    const button = document.getElementById('toggleAutoBet');
    
    if (!autoBetSettings.enabled) {
        autoBetSettings.enabled = true;
        autoBetSettings.betAmount = parseInt(document.getElementById('autoBetAmount').value);
        autoBetSettings.maxRounds = parseInt(document.getElementById('autoMaxRounds').value);
        autoBetSettings.stopOnBigWin = document.getElementById('autoStopOnBigWin').checked;
        autoBetSettings.currentRound = 0;
        
        button.textContent = 'STOP AUTO-BET';
        button.className = 'w-full cyber-button-danger py-2 rounded-lg font-semibold';
        
        runAutoBet();
    } else {
        autoBetSettings.enabled = false;
        button.textContent = 'START AUTO-BET';
        button.className = 'w-full cyber-button-secondary py-2 rounded-lg font-semibold';
    }
}

function runAutoBet() {
    if (!autoBetSettings.enabled || autoBetSettings.currentRound >= autoBetSettings.maxRounds) {
        toggleAutoBet();
        return;
    }
    
    if (balance < autoBetSettings.betAmount) {
        toggleAutoBet();
        document.getElementById('warStatus').textContent = 'Auto-bet stopped: Insufficient balance';
        return;
    }
    
    // Set bet amount and deal
    document.getElementById('stakeBetInput').value = autoBetSettings.betAmount;
    updateStakeBet();
    
    setTimeout(() => {
        dealHighStackCards();
        autoBetSettings.currentRound++;
    }, 2000);
}

// Tournament mode
function initializeTournament() {
    if (balance < tournamentMode.entryFee) {
        document.getElementById('warStatus').textContent = `Insufficient balance for tournament entry (${tournamentMode.entryFee} GA)`;
        return;
    }
    
    balance -= tournamentMode.entryFee;
    updateBalance();
    
    tournamentMode.active = true;
    tournamentMode.prizePool = tournamentMode.entryFee * 5; // 5x entry fee prize
    tournamentMode.roundsRemaining = 10;
    
    document.getElementById('warStatus').textContent = `🏆 Tournament started! Prize pool: ${tournamentMode.prizePool} GA`;
    
    // Add tournament UI
    const tournamentHTML = `
        <div id="tournamentPanel" class="cyber-panel mt-4 border-yellow-400">
            <h4 class="text-xl font-bold text-yellow-400 mb-2 text-center">🏆 High Stakes Tournament</h4>
            <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-yellow-400" id="tournamentRounds">${tournamentMode.roundsRemaining}</div>
                    <div class="text-sm">Rounds Left</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-400" id="tournamentPrize">${tournamentMode.prizePool}</div>
                    <div class="text-sm">Prize Pool</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-blue-400" id="tournamentScore">0</div>
                    <div class="text-sm">Tournament Score</div>
                </div>
            </div>
        </div>
    `;
    
    document.querySelector('.cyber-panel').insertAdjacentHTML('afterend', tournamentHTML);
}

// Add these functions to the existing setupHighStackGame function
function enhanceHighStackGame() {
    addSideBetOptions();
    setupAutoBet();
    
    // Add tournament button
    const tournamentButton = document.createElement('button');
    tournamentButton.textContent = 'JOIN TOURNAMENT (10K GA)';
    tournamentButton.className = 'cyber-button-primary px-6 py-2 rounded-lg font-semibold ml-4';
    tournamentButton.onclick = initializeTournament;
    
    document.getElementById('rulesButton').insertAdjacentElement('afterend', tournamentButton);
}

// Enhanced game result processing
function processEnhancedResult(outcome, betAmount, winAmount, isWar = false) {
    // Track advanced statistics
    trackAdvancedStats(outcome, betAmount, winAmount);
    
    // Check achievements
    checkAchievements(betAmount, winAmount, isWar);
    
    // Process side bets
    processSideBets();
    
    // Update tournament if active
    if (tournamentMode.active) {
        updateTournamentProgress(winAmount > betAmount);
    }
    
    // Continue auto-bet if enabled
    if (autoBetSettings.enabled) {
        setTimeout(() => {
            runAutoBet();
        }, 3000);
    }
}

function processSideBets() {
    if (!highStackGame.playerCard || !highStackGame.dealerCard) return;
    
    let sideBetWinnings = 0;
    
    Object.entries(SIDE_BETS).forEach(([id, bet]) => {
        const checkbox = document.getElementById(`sideBet_${id}`);
        const amountInput = document.getElementById(`sideBetAmount_${id}`);
        
        if (checkbox?.checked && amountInput?.value > 0) {
            const betAmount = parseInt(amountInput.value);
            let won = false;
            
            switch (id) {
                case 'suited_match':
                    won = highStackGame.playerCard.suit === highStackGame.dealerCard.suit;
                    break;
                case 'color_match':
                    const playerRed = ['♥', '♦'].includes(highStackGame.playerCard.suit);
                    const dealerRed = ['♥', '♦'].includes(highStackGame.dealerCard.suit);
                    won = playerRed === dealerRed;
                    break;
                case 'royal_war':
                    won = highStackGame.isInWar && 
                         ['J', 'Q', 'K'].includes(highStackGame.playerCard.value) &&
                         ['J', 'Q', 'K'].includes(highStackGame.dealerCard.value);
                    break;
                case 'ace_duel':
                    won = highStackGame.isInWar && 
                         highStackGame.playerCard.value === 'A' &&
                         highStackGame.dealerCard.value === 'A';
                    break;
            }
            
            if (won) {
                const payout = betAmount * bet.payout;
                sideBetWinnings += payout + betAmount;
                
                // Show side bet win notification
                const notification = document.createElement('div');
                notification.className = 'fixed top-20 right-4 bg-yellow-500 text-black p-3 rounded-lg shadow-xl z-50';
                notification.innerHTML = `🎲 ${bet.name} wins! +${payout} GA`;
                document.body.appendChild(notification);
                
                setTimeout(() => notification.remove(), 3000);
            }
        }
    });
    
    if (sideBetWinnings > 0) {
        balance += sideBetWinnings;
        updateBalance();
    }
}

function updateTournamentProgress(won) {
    if (!tournamentMode.active) return;
    
    tournamentMode.roundsRemaining--;
    
    if (won) {
        const score = parseInt(document.getElementById('tournamentScore').textContent) + 100;
        document.getElementById('tournamentScore').textContent = score;
    }
    
    document.getElementById('tournamentRounds').textContent = tournamentMode.roundsRemaining;
    
    if (tournamentMode.roundsRemaining <= 0) {
        endTournament();
    }
}

function endTournament() {
    const finalScore = parseInt(document.getElementById('tournamentScore').textContent);
    const prizeWon = Math.floor(tournamentMode.prizePool * (finalScore / 1000)); // Score-based prize
    
    balance += prizeWon;
    updateBalance();
    
    document.getElementById('warStatus').textContent = 
        `🏆 Tournament complete! Final score: ${finalScore}. Prize won: ${prizeWon} GA`;
    
    tournamentMode.active = false;
    document.getElementById('tournamentPanel')?.remove();
}

// Call enhancement function when game loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadHighStackGame();
    setTimeout(enhanceHighStackGame, 1000); // Add enhancements after main game loads
});
