// Spanish 21: Midnight Rush - Cyberpunk Spanish 21 with extremely low win rate (<7%)
let spanish21Game = {
    playerHands: [[]],
    dealerHand: [],
    currentHandIndex: 0,
    playerScores: [0],
    dealerScore: 0,
    bets: [0],
    gamePhase: 'betting', // betting, playing, dealer, complete
    gameResult: [],
    totalWin: 0,
    deck: [],
    canSplit: false,
    canDoubleDown: false,
    canSurrender: false,
    difficulty: 'midnight', // midnight, rush, cyber, void
    gameMode: 'classic', // classic, rush, ultimate
    rushLevel: 1, // 1-15 (higher = more house advantage)
    midnightBonus: false, // Rarely activated
    cyberPenalty: false, // Frequently activated
    voidMode: false, // Ultimate penalty mode
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        handsPushed: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        blackjacks: 0,
        spanish21s: 0, // 21 with 5+ cards
        busts: 0,
        surrenders: 0,
        doubles: 0,
        splits: 0,
        midnightBonuses: 0,
        cyberPenalties: 0,
        voidLosses: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    handHistory: []
};

// Difficulty levels with extreme house bias
const SPANISH21_DIFFICULTIES = {
    midnight: {
        name: 'Midnight Rush',
        dealerAdvantage: 0.42, // 42% dealer advantage
        playerPenalty: 0.28, // 28% player penalty
        bustPenalty: 1.5, // 50% extra loss on bust
        payoutReduction: 0.25 // 25% payout reduction
    },
    rush: {
        name: 'Cyber Rush',
        dealerAdvantage: 0.48, // 48% dealer advantage
        playerPenalty: 0.34, // 34% player penalty
        bustPenalty: 1.7, // 70% extra loss on bust
        payoutReduction: 0.32 // 32% payout reduction
    },
    cyber: {
        name: 'Cyber Void',
        dealerAdvantage: 0.54, // 54% dealer advantage
        playerPenalty: 0.40, // 40% player penalty
        bustPenalty: 2.0, // 100% extra loss on bust
        payoutReduction: 0.38 // 38% payout reduction
    },
    void: {
        name: 'Void Matrix',
        dealerAdvantage: 0.62, // 62% dealer advantage
        playerPenalty: 0.47, // 47% player penalty
        bustPenalty: 2.5, // 150% extra loss on bust
        payoutReduction: 0.45 // 45% payout reduction
    }
};

// Game modes with severe house bias
const SPANISH21_MODES = {
    classic: {
        name: 'Classic Midnight',
        houseEdge: 0.38, // 38% house edge
        bonusChance: 0.02, // 2% bonus chance
        penaltyChance: 0.35, // 35% penalty chance
        payoutMultiplier: 0.62 // Severely reduced payouts
    },
    rush: {
        name: 'Midnight Rush',
        houseEdge: 0.45, // 45% house edge
        bonusChance: 0.015, // 1.5% bonus chance
        penaltyChance: 0.42, // 42% penalty chance
        payoutMultiplier: 0.55 // Severely reduced payouts
    },
    ultimate: {
        name: 'Ultimate Void',
        houseEdge: 0.52, // 52% house edge
        bonusChance: 0.01, // 1% bonus chance
        penaltyChance: 0.49, // 49% penalty chance
        payoutMultiplier: 0.48 // Severely reduced payouts
    }
};

// Severely reduced payouts (much lower than standard Spanish 21)
const SPANISH21_PAYOUTS = {
    blackjack: 1.2, // Reduced from 3:2 to 1.2:1
    spanish21: 2.5, // Reduced from 3:1 to 2.5:1
    fiveCardTrick: 1.8, // Reduced from 3:2 to 1.8:1
    sixCardTrick: 2.2, // Reduced from 2:1 to 2.2:1
    sevenCardTrick: 2.8, // Reduced from 3:1 to 2.8:1
    regular: 0.75, // Reduced from 1:1 to 0.75:1
    surrender: 0.3, // Lose 70% instead of 50%
    doubleDown: 0.6 // Reduced double down payout
};

// Special bonus combinations (rarely triggered)
const SPANISH21_BONUSES = {
    '777suited': { payout: 50, chance: 0.0001 }, // 0.01% chance
    '777mixed': { payout: 25, chance: 0.0005 }, // 0.05% chance
    '678suited': { payout: 30, chance: 0.0003 }, // 0.03% chance
    '678mixed': { payout: 15, chance: 0.001 }, // 0.1% chance
    'midnight21': { payout: 40, chance: 0.0002 } // 0.02% chance
};

function createSpanish21Deck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', 'J', 'Q', 'K']; // No 10s in Spanish 21
    const deck = [];
    
    // Create 6-8 decks for house advantage
    for (let d = 0; d < 8; d++) {
        for (let suit of suits) {
            for (let value of values) {
                deck.push({
                    value: value,
                    suit: suit,
                    numValue: value === 'A' ? 11 : ['J', 'Q', 'K'].includes(value) ? 10 : parseInt(value)
                });
            }
        }
    }
    
    return shuffleArray(deck);
}

function dealSpanish21Card(isDealer = false) {
    if (spanish21Game.deck.length < 20) {
        spanish21Game.deck = createSpanish21Deck();
    }
    
    const difficultyData = SPANISH21_DIFFICULTIES[spanish21Game.difficulty];
    const modeData = SPANISH21_MODES[spanish21Game.gameMode];
    
    let card;
    
    if (isDealer) {
        // Dealer gets favorable cards
        if (Math.random() < difficultyData.dealerAdvantage) {
            // Give dealer advantageous cards
            const favorableCards = spanish21Game.deck.filter(c => 
                c.numValue >= 7 && c.numValue <= 11
            );
            if (favorableCards.length > 0) {
                const cardIndex = Math.floor(Math.random() * favorableCards.length);
                card = favorableCards[cardIndex];
                spanish21Game.deck.splice(spanish21Game.deck.indexOf(card), 1);
            } else {
                card = spanish21Game.deck.pop();
            }
        } else {
            card = spanish21Game.deck.pop();
        }
    } else {
        // Player gets unfavorable cards
        if (Math.random() < difficultyData.playerPenalty) {
            // Give player disadvantageous cards
            const unfavorableCards = spanish21Game.deck.filter(c => 
                c.numValue <= 6 || c.numValue === 10
            );
            if (unfavorableCards.length > 0) {
                const cardIndex = Math.floor(Math.random() * unfavorableCards.length);
                card = unfavorableCards[cardIndex];
                spanish21Game.deck.splice(spanish21Game.deck.indexOf(card), 1);
            } else {
                card = spanish21Game.deck.pop();
            }
        } else {
            card = spanish21Game.deck.pop();
        }
    }
    
    return card;
}

function calculateSpanish21Score(hand) {
    let score = 0;
    let aces = 0;
    
    for (let card of hand) {
        if (card.value === 'A') {
            aces++;
            score += 11;
        } else {
            score += card.numValue;
        }
    }
    
    // Adjust for aces
    while (score > 21 && aces > 0) {
        score -= 10;
        aces--;
    }
    
    return score;
}

function startSpanish21Game() {
    const betAmount = parseInt(document.getElementById('spanish21Bet').value);
    if (betAmount < 10 || betAmount > balance) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Initialize game
    spanish21Game.deck = createSpanish21Deck();
    spanish21Game.playerHands = [[]];
    spanish21Game.dealerHand = [];
    spanish21Game.currentHandIndex = 0;
    spanish21Game.playerScores = [0];
    spanish21Game.dealerScore = 0;
    spanish21Game.bets = [betAmount];
    spanish21Game.gamePhase = 'playing';
    spanish21Game.gameResult = [];
    spanish21Game.totalWin = 0;
    
    // Trigger cyber effects
    triggerSpanish21Effects();
    
    // Deal initial cards
    spanish21Game.playerHands[0].push(dealSpanish21Card(false));
    spanish21Game.dealerHand.push(dealSpanish21Card(true));
    spanish21Game.playerHands[0].push(dealSpanish21Card(false));
    spanish21Game.dealerHand.push(dealSpanish21Card(true));
    
    // Calculate scores
    spanish21Game.playerScores[0] = calculateSpanish21Score(spanish21Game.playerHands[0]);
    spanish21Game.dealerScore = calculateSpanish21Score([spanish21Game.dealerHand[0]]);
    
    // Check for player blackjack (extremely rare win)
    if (spanish21Game.playerScores[0] === 21) {
        if (spanish21Game.playerHands[0].length === 2) {
            // Check for special Spanish 21 combinations
            if (checkSpanish21Bonus(spanish21Game.playerHands[0])) {
                spanish21Game.stats.spanish21s++;
            } else {
                spanish21Game.stats.blackjacks++;
            }
        }
        spanish21Game.gamePhase = 'dealer';
        dealerPlaySpanish21();
    } else {
        updateSpanish21Options();
    }
    
    updateSpanish21Display();
    updateSpanish21Status('Choose your action...');
}

function triggerSpanish21Effects() {
    const modeData = SPANISH21_MODES[spanish21Game.gameMode];
    
    // Reset effects
    spanish21Game.midnightBonus = false;
    spanish21Game.cyberPenalty = false;
    spanish21Game.voidMode = false;
    
    // Trigger effects (mostly negative)
    if (Math.random() < modeData.bonusChance) {
        spanish21Game.midnightBonus = true;
        spanish21Game.stats.midnightBonuses++;
        updateSpanish21Status('Midnight bonus activated!');
    }
    
    if (Math.random() < modeData.penaltyChance) {
        spanish21Game.cyberPenalty = true;
        spanish21Game.stats.cyberPenalties++;
        updateSpanish21Status('Cyber penalty engaged - reduced payouts!');
    }
    
    if (spanish21Game.difficulty === 'void' && Math.random() < 0.3) {
        spanish21Game.voidMode = true;
        spanish21Game.stats.voidLosses++;
        updateSpanish21Status('VOID MODE - extreme penalties active!');
    }
}

function hitSpanish21() {
    if (spanish21Game.gamePhase !== 'playing') return;
    
    const currentHand = spanish21Game.currentHandIndex;
    spanish21Game.playerHands[currentHand].push(dealSpanish21Card(false));
    spanish21Game.playerScores[currentHand] = calculateSpanish21Score(spanish21Game.playerHands[currentHand]);
    
    // Check for bust with severe penalty
    if (spanish21Game.playerScores[currentHand] > 21) {
        const difficultyData = SPANISH21_DIFFICULTIES[spanish21Game.difficulty];
        
        // Apply bust penalty (lose more than just the bet)
        const bustPenalty = Math.floor(spanish21Game.bets[currentHand] * difficultyData.bustPenalty);
        balance -= (bustPenalty - spanish21Game.bets[currentHand]); // Additional penalty
        
        spanish21Game.stats.busts++;
        spanish21Game.gameResult[currentHand] = 'bust';
        
        if (spanish21Game.cyberPenalty) {
            updateSpanish21Status('CYBER BUST! Severe penalty applied!');
        } else {
            updateSpanish21Status('BUST! Over 21!');
        }
        
        nextHandOrDealer();
    } else if (spanish21Game.playerScores[currentHand] === 21) {
        // Auto-stand on 21
        standSpanish21();
    } else {
        updateSpanish21Options();
    }
    
    updateSpanish21Display();
    updateBalance();
}

function standSpanish21() {
    if (spanish21Game.gamePhase !== 'playing') return;
    
    nextHandOrDealer();
}

function doubleDownSpanish21() {
    if (spanish21Game.gamePhase !== 'playing' || !spanish21Game.canDoubleDown) return;
    
    const currentHand = spanish21Game.currentHandIndex;
    const additionalBet = spanish21Game.bets[currentHand];
    
    if (balance < additionalBet) return;
    
    balance -= additionalBet;
    spanish21Game.bets[currentHand] *= 2;
    spanish21Game.stats.doubles++;
    
    // Deal one card
    spanish21Game.playerHands[currentHand].push(dealSpanish21Card(false));
    spanish21Game.playerScores[currentHand] = calculateSpanish21Score(spanish21Game.playerHands[currentHand]);
    
    // Check for bust
    if (spanish21Game.playerScores[currentHand] > 21) {
        const difficultyData = SPANISH21_DIFFICULTIES[spanish21Game.difficulty];
        const bustPenalty = Math.floor(spanish21Game.bets[currentHand] * difficultyData.bustPenalty);
        balance -= (bustPenalty - spanish21Game.bets[currentHand]);
        
        spanish21Game.stats.busts++;
        spanish21Game.gameResult[currentHand] = 'bust';
        updateSpanish21Status('DOUBLE DOWN BUST! Severe penalty!');
    }
    
    nextHandOrDealer();
    updateSpanish21Display();
    updateBalance();
}

function splitSpanish21() {
    if (spanish21Game.gamePhase !== 'playing' || !spanish21Game.canSplit) return;
    
    const currentHand = spanish21Game.currentHandIndex;
    const splitBet = spanish21Game.bets[currentHand];
    
    if (balance < splitBet) return;
    
    balance -= splitBet;
    spanish21Game.stats.splits++;
    
    // Create new hand
    const splitCard = spanish21Game.playerHands[currentHand].pop();
    spanish21Game.playerHands.push([splitCard]);
    spanish21Game.bets.push(splitBet);
    spanish21Game.playerScores.push(0);
    spanish21Game.gameResult.push(null);
    
    // Deal new cards to both hands
    spanish21Game.playerHands[currentHand].push(dealSpanish21Card(false));
    spanish21Game.playerHands[spanish21Game.playerHands.length - 1].push(dealSpanish21Card(false));
    
    // Recalculate scores
    spanish21Game.playerScores[currentHand] = calculateSpanish21Score(spanish21Game.playerHands[currentHand]);
    spanish21Game.playerScores[spanish21Game.playerScores.length - 1] = 
        calculateSpanish21Score(spanish21Game.playerHands[spanish21Game.playerHands.length - 1]);
    
    updateSpanish21Options();
    updateSpanish21Display();
    updateBalance();
}

function surrenderSpanish21() {
    if (spanish21Game.gamePhase !== 'playing' || !spanish21Game.canSurrender) return;
    
    const currentHand = spanish21Game.currentHandIndex;
    const surrenderPayout = SPANISH21_PAYOUTS.surrender;
    
    // Return only 30% of bet (lose 70%)
    const returnAmount = Math.floor(spanish21Game.bets[currentHand] * surrenderPayout);
    balance += returnAmount;
    
    spanish21Game.stats.surrenders++;
    spanish21Game.gameResult[currentHand] = 'surrender';
    
    updateSpanish21Status('Surrendered - 70% of bet lost!');
    nextHandOrDealer();
    updateSpanish21Display();
    updateBalance();
}

function nextHandOrDealer() {
    spanish21Game.currentHandIndex++;
    
    if (spanish21Game.currentHandIndex < spanish21Game.playerHands.length) {
        // Play next hand
        updateSpanish21Options();
    } else {
        // All hands played, dealer's turn
        spanish21Game.gamePhase = 'dealer';
        dealerPlaySpanish21();
    }
}

function dealerPlaySpanish21() {
    // Dealer hits on soft 17 (house advantage)
    while (spanish21Game.dealerScore < 17 || 
           (spanish21Game.dealerScore === 17 && hasSoftAce(spanish21Game.dealerHand))) {
        spanish21Game.dealerHand.push(dealSpanish21Card(true));
        spanish21Game.dealerScore = calculateSpanish21Score(spanish21Game.dealerHand);
    }
    
    // Calculate final results
    calculateSpanish21Results();
    spanish21Game.gamePhase = 'complete';
    updateSpanish21Display();
}

function hasSoftAce(hand) {
    let score = 0;
    let aces = 0;
    
    for (let card of hand) {
        if (card.value === 'A') {
            aces++;
            score += 11;
        } else {
            score += card.numValue;
        }
    }
    
    return score <= 21 && aces > 0 && score !== calculateSpanish21Score(hand) + 10;
}

function calculateSpanish21Results() {
    const difficultyData = SPANISH21_DIFFICULTIES[spanish21Game.difficulty];
    const modeData = SPANISH21_MODES[spanish21Game.gameMode];
    
    spanish21Game.totalWin = 0;
    
    for (let i = 0; i < spanish21Game.playerHands.length; i++) {
        if (spanish21Game.gameResult[i]) continue; // Already determined (bust/surrender)
        
        const playerScore = spanish21Game.playerScores[i];
        const betAmount = spanish21Game.bets[i];
        let payout = 0;
        
        if (playerScore > 21) {
            spanish21Game.gameResult[i] = 'bust';
        } else if (spanish21Game.dealerScore > 21) {
            // Dealer bust - player wins
            payout = calculateSpanish21Payout(spanish21Game.playerHands[i], betAmount);
            spanish21Game.gameResult[i] = 'win';
        } else if (playerScore > spanish21Game.dealerScore) {
            // Player wins
            payout = calculateSpanish21Payout(spanish21Game.playerHands[i], betAmount);
            spanish21Game.gameResult[i] = 'win';
        } else if (playerScore < spanish21Game.dealerScore) {
            spanish21Game.gameResult[i] = 'lose';
        } else {
            // Push - but with penalty in this game
            payout = Math.floor(betAmount * 0.8); // Lose 20% even on push
            spanish21Game.gameResult[i] = 'push';
        }
        
        // Apply cyber penalties
        if (spanish21Game.cyberPenalty && payout > 0) {
            payout *= 0.6; // 40% cyber penalty
        }
        
        if (spanish21Game.voidMode && payout > 0) {
            payout *= 0.4; // 60% void penalty
        }
        
        // Apply mode and difficulty penalties
        payout *= modeData.payoutMultiplier;
        payout *= (1 - difficultyData.payoutReduction);
        
        spanish21Game.totalWin += Math.floor(payout);
    }
    
    // Update stats
    spanish21Game.stats.handsPlayed++;
    spanish21Game.stats.totalWagered += spanish21Game.bets.reduce((a, b) => a + b, 0);
    
    if (spanish21Game.totalWin > 0) {
        spanish21Game.stats.handsWon++;
        spanish21Game.stats.totalWon += spanish21Game.totalWin;
        spanish21Game.stats.biggestWin = Math.max(spanish21Game.stats.biggestWin, spanish21Game.totalWin);
        spanish21Game.streakData.currentWinStreak++;
        spanish21Game.streakData.currentLossStreak = 0;
        spanish21Game.streakData.longestWinStreak = Math.max(
            spanish21Game.streakData.longestWinStreak,
            spanish21Game.streakData.currentWinStreak
        );
        balance += spanish21Game.totalWin;
        updateSpanish21Status(`Midnight win! +${spanish21Game.totalWin} GA`);
    } else {
        spanish21Game.streakData.currentLossStreak++;
        spanish21Game.streakData.currentWinStreak = 0;
        spanish21Game.streakData.longestLossStreak = Math.max(
            spanish21Game.streakData.longestLossStreak,
            spanish21Game.streakData.currentLossStreak
        );
        updateSpanish21Status('The midnight claims your bet...');
    }
    
    // Add to history
    spanish21Game.handHistory.unshift({
        playerHands: spanish21Game.playerHands.map(hand => [...hand]),
        dealerHand: [...spanish21Game.dealerHand],
        playerScores: [...spanish21Game.playerScores],
        dealerScore: spanish21Game.dealerScore,
        bets: [...spanish21Game.bets],
        results: [...spanish21Game.gameResult],
        totalWin: spanish21Game.totalWin
    });
    
    if (spanish21Game.handHistory.length > 15) {
        spanish21Game.handHistory.pop();
    }
    
    updateBalance();
}

function calculateSpanish21Payout(hand, betAmount) {
    const score = calculateSpanish21Score(hand);
    
    if (score === 21) {
        if (hand.length === 2) {
            // Check for special combinations
            if (checkSpanish21Bonus(hand)) {
                return Math.floor(betAmount * SPANISH21_PAYOUTS.spanish21);
            } else {
                return Math.floor(betAmount * SPANISH21_PAYOUTS.blackjack);
            }
        } else if (hand.length === 5) {
            return Math.floor(betAmount * SPANISH21_PAYOUTS.fiveCardTrick);
        } else if (hand.length === 6) {
            return Math.floor(betAmount * SPANISH21_PAYOUTS.sixCardTrick);
        } else if (hand.length >= 7) {
            return Math.floor(betAmount * SPANISH21_PAYOUTS.sevenCardTrick);
        }
    }
    
    return Math.floor(betAmount * SPANISH21_PAYOUTS.regular);
}

function checkSpanish21Bonus(hand) {
    if (hand.length !== 2) return false;
    
    const values = hand.map(c => c.value).sort();
    const suits = hand.map(c => c.suit);
    
    // Check for 7-7-7 (impossible with 2 cards, but checking for future)
    // Check for 6-7-8 combinations
    if (values.join('') === '67' || values.join('') === '68' || values.join('') === '78') {
        return Math.random() < 0.001; // 0.1% chance even with right cards
    }
    
    return false;
}

function updateSpanish21Options() {
    const currentHand = spanish21Game.currentHandIndex;
    const hand = spanish21Game.playerHands[currentHand];
    const score = spanish21Game.playerScores[currentHand];
    
    // Can split if first two cards have same value and enough balance
    spanish21Game.canSplit = hand.length === 2 && 
                            hand[0].numValue === hand[1].numValue && 
                            balance >= spanish21Game.bets[currentHand] &&
                            spanish21Game.playerHands.length < 4; // Max 4 hands
    
    // Can double down if first two cards and enough balance
    spanish21Game.canDoubleDown = hand.length === 2 && 
                                 balance >= spanish21Game.bets[currentHand];
    
    // Can surrender if first two cards
    spanish21Game.canSurrender = hand.length === 2;
    
    // Update button states
    document.getElementById('hitSpanish21').disabled = score >= 21;
    document.getElementById('standSpanish21').disabled = false;
    document.getElementById('doubleSpanish21').disabled = !spanish21Game.canDoubleDown;
    document.getElementById('splitSpanish21').disabled = !spanish21Game.canSplit;
    document.getElementById('surrenderSpanish21').disabled = !spanish21Game.canSurrender;
}

function updateSpanish21Display() {
    // Update player hands
    const playerHandsEl = document.getElementById('playerHands');
    playerHandsEl.innerHTML = spanish21Game.playerHands.map((hand, index) => {
        const isActive = index === spanish21Game.currentHandIndex && spanish21Game.gamePhase === 'playing';
        const result = spanish21Game.gameResult[index];
        const score = spanish21Game.playerScores[index];
        
        return `
            <div class="hand ${isActive ? 'border-purple-400' : ''} ${result ? 'opacity-75' : ''}">
                <div class="text-sm mb-2">Hand ${index + 1} - Bet: ${spanish21Game.bets[index]} GA</div>
                <div class="cards mb-2">
                    ${hand.map(card => `<span class="card">${card.value}${card.suit}</span>`).join('')}
                </div>
                <div class="score">Score: ${score} ${result ? `(${result.toUpperCase()})` : ''}</div>
            </div>
        `;
    }).join('');
    
    // Update dealer hand
    const dealerHandEl = document.getElementById('dealerHand');
    if (spanish21Game.gamePhase === 'playing') {
        // Hide dealer's second card
        dealerHandEl.innerHTML = `
            <div class="cards mb-2">
                <span class="card">${spanish21Game.dealerHand[0].value}${spanish21Game.dealerHand[0].suit}</span>
                <span class="card">🂠</span>
            </div>
            <div class="score">Score: ${spanish21Game.dealerHand[0].numValue}</div>
        `;
    } else {
        dealerHandEl.innerHTML = `
            <div class="cards mb-2">
                ${spanish21Game.dealerHand.map(card => `<span class="card">${card.value}${card.suit}</span>`).join('')}
            </div>
            <div class="score">Score: ${spanish21Game.dealerScore}</div>
        `;
    }
    
    // Update game controls
    const gamePhase = spanish21Game.gamePhase;
    document.getElementById('startSpanish21').disabled = gamePhase !== 'betting';
    document.getElementById('hitSpanish21').disabled = gamePhase !== 'playing';
    document.getElementById('standSpanish21').disabled = gamePhase !== 'playing';
    document.getElementById('doubleSpanish21').disabled = gamePhase !== 'playing' || !spanish21Game.canDoubleDown;
    document.getElementById('splitSpanish21').disabled = gamePhase !== 'playing' || !spanish21Game.canSplit;
    document.getElementById('surrenderSpanish21').disabled = gamePhase !== 'playing' || !spanish21Game.canSurrender;
    
    updateSpanish21Stats();
}

function updateSpanish21Stats() {
    document.getElementById('handsPlayed').textContent = spanish21Game.stats.handsPlayed;
    
    const winRate = spanish21Game.stats.handsPlayed > 0 ? 
        ((spanish21Game.stats.handsWon / spanish21Game.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('winRate').textContent = `${winRate}%`;
    
    document.getElementById('totalWagered').textContent = spanish21Game.stats.totalWagered.toLocaleString();
    document.getElementById('totalWon').textContent = spanish21Game.stats.totalWon.toLocaleString();
    document.getElementById('biggestWin').textContent = spanish21Game.stats.biggestWin.toLocaleString();
    document.getElementById('lossStreak').textContent = spanish21Game.streakData.currentLossStreak;
    document.getElementById('blackjacks').textContent = spanish21Game.stats.blackjacks;
    document.getElementById('spanish21s').textContent = spanish21Game.stats.spanish21s;
    document.getElementById('busts').textContent = spanish21Game.stats.busts;
    document.getElementById('cyberPenalties').textContent = spanish21Game.stats.cyberPenalties;
}

function updateSpanish21Status(message) {
    document.getElementById('spanish21Status').textContent = message;
}

function changeSpanish21Difficulty() {
    const difficulty = document.getElementById('spanish21Difficulty').value;
    spanish21Game.difficulty = difficulty;
    
    const difficultyData = SPANISH21_DIFFICULTIES[difficulty];
    document.getElementById('difficultyInfo').innerHTML = `
        Difficulty: ${difficultyData.name} | 
        Dealer Advantage: ${(difficultyData.dealerAdvantage * 100).toFixed(0)}% | 
        Payout Reduction: ${(difficultyData.payoutReduction * 100).toFixed(0)}%
    `;
}

function changeSpanish21Mode() {
    const mode = document.getElementById('spanish21Mode').value;
    spanish21Game.gameMode = mode;
    
    const modeData = SPANISH21_MODES[mode];
    document.getElementById('modeInfo').innerHTML = `
        Mode: ${modeData.name} | 
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}%
    `;
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeSpanish21Difficulty();
    changeSpanish21Mode();
    
    document.getElementById('startSpanish21').addEventListener('click', startSpanish21Game);
    document.getElementById('hitSpanish21').addEventListener('click', hitSpanish21);
    document.getElementById('standSpanish21').addEventListener('click', standSpanish21);
    document.getElementById('doubleSpanish21').addEventListener('click', doubleDownSpanish21);
    document.getElementById('splitSpanish21').addEventListener('click', splitSpanish21);
    document.getElementById('surrenderSpanish21').addEventListener('click', surrenderSpanish21);
    document.getElementById('spanish21Difficulty').addEventListener('change', changeSpanish21Difficulty);
    document.getElementById('spanish21Mode').addEventListener('change', changeSpanish21Mode);
    
    updateSpanish21Status('Welcome to Spanish 21: Midnight Rush - where the night never ends in your favor!');
});