// Red Dog Rush - Cyberpunk Red Dog with extremely low win rate (<5%)
let redDogRushGame = {
    deck: [],
    playerCard: null,
    dealerCard: null,
    middleCard: null,
    spread: 0,
    betAmount: 0,
    raiseBet: 0,
    gamePhase: 'betting', // betting, raised, revealing, complete
    gameResult: null,
    totalWin: 0,
    rushLevel: 1, // 1-10 (higher = more house advantage)
    rushMultiplier: 1.0,
    difficulty: 'cyber', // cyber, matrix, quantum, void
    gameMode: 'rush', // rush, turbo, ultimate
    cyberBonus: false,
    matrixPenalty: false,
    quantumNegation: false,
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        spreadsHit: 0,
        rushBonuses: 0,
        matrixPenalties: 0,
        quantumNegations: 0,
        voidLosses: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    cardHistory: []
};

// Game modes with extreme house bias
const RUSH_MODES = {
    rush: { 
        name: '<PERSON><PERSON> Rush', 
        houseEdge: 0.42, // 42% house edge
        rushChance: 0.03, // 3% chance for bonus
        payoutMultiplier: 0.62, // Severely reduced payouts
        spreadPenalty: 0.22 // 22% spread penalty
    },
    turbo: { 
        name: 'Turbo Matrix', 
        houseEdge: 0.47, // 47% house edge
        rushChance: 0.02, // 2% chance for bonus
        payoutMultiplier: 0.58,
        spreadPenalty: 0.27 // 27% spread penalty
    },
    ultimate: { 
        name: 'Ultimate Void', 
        houseEdge: 0.52, // 52% house edge
        rushChance: 0.01, // 1% chance for bonus
        payoutMultiplier: 0.53,
        spreadPenalty: 0.32 // 32% spread penalty
    }
};

const RUSH_DIFFICULTIES = {
    cyber: { 
        name: 'Cyber Grid', 
        cardBias: 0.38,
        spreadReduction: 0.43,
        matrixChance: 0.22,
        levelMultiplier: 1.0
    },
    matrix: { 
        name: 'Matrix Core', 
        cardBias: 0.53,
        spreadReduction: 0.58,
        matrixChance: 0.32,
        levelMultiplier: 1.4
    },
    quantum: { 
        name: 'Quantum Field', 
        cardBias: 0.68,
        spreadReduction: 0.73,
        matrixChance: 0.42,
        levelMultiplier: 1.7
    },
    void: { 
        name: 'Void Nexus', 
        cardBias: 0.83,
        spreadReduction: 0.88,
        matrixChance: 0.52,
        levelMultiplier: 2.1
    }
};

// Extremely reduced spread payouts
const RUSH_PAYOUTS = {
    1: 3.8,  // 1-spread (reduced from 5:1)
    2: 1.7,  // 2-spread (reduced from 2:1)
    3: 1.3,  // 3-spread (reduced from 1.5:1)
    4: 1.0,  // 4-spread (reduced from 1.2:1)
    5: 0.8,  // 5-spread (reduced from 1:1)
    6: 0.7,  // 6-spread (reduced from 1:1)
    7: 0.6,  // 7-spread (reduced from 1:1)
    8: 0.5,  // 8-spread (reduced from 1:1)
    9: 0.4,  // 9-spread (reduced from 1:1)
    10: 0.3, // 10-spread (reduced from 1:1)
    11: 0.2  // 11-spread (reduced from 1:1)
};

// Ultra-biased deck creation
function createRushDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const deck = [];
    
    const modeData = RUSH_MODES[redDogRushGame.gameMode];
    const difficultyData = RUSH_DIFFICULTIES[redDogRushGame.difficulty];
    const levelBias = redDogRushGame.rushLevel * 0.09; // Increases with rush level
    
    suits.forEach(suit => {
        ranks.forEach(rank => {
            let cardWeight = 1;
            const value = getRushCardValue(rank);
            
            // Extreme bias to create losing scenarios
            // Reduce middle-value cards that could create good spreads
            if (value >= 5 && value <= 9) {
                cardWeight *= (1 - difficultyData.cardBias - levelBias);
            }
            
            // Increase extreme cards (A, 2, K, Q, J) that create bad spreads
            if (value <= 2 || value >= 11) {
                cardWeight *= (1 + difficultyData.cardBias + levelBias);
            }
            
            // Further reduce based on rush level
            if (redDogRushGame.rushLevel > 6) {
                if (value >= 4 && value <= 10) {
                    cardWeight *= 0.15; // Extreme reduction of middle cards
                }
            }
            
            // Add cards based on weight
            const cardCount = Math.max(1, Math.floor(cardWeight * 9));
            for (let i = 0; i < cardCount; i++) {
                deck.push({ suit, rank, value });
            }
        });
    });
    
    return shuffleRushDeck(deck);
}

function getRushCardValue(rank) {
    if (rank === 'A') return 1;
    if (rank === 'J') return 11;
    if (rank === 'Q') return 12;
    if (rank === 'K') return 13;
    return parseInt(rank);
}

function shuffleRushDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function dealBiasedRushCard(cardType = 'normal') {
    if (redDogRushGame.deck.length === 0) {
        redDogRushGame.deck = createRushDeck();
    }
    
    const difficultyData = RUSH_DIFFICULTIES[redDogRushGame.difficulty];
    const cardBias = difficultyData.cardBias + (redDogRushGame.rushLevel * 0.06);
    
    // Apply extreme bias based on card type and game state
    if (cardType === 'middle' && Math.random() < cardBias * 1.6) {
        // For middle card, heavily bias toward cards that don't fall in spread
        const playerValue = redDogRushGame.playerCard.value;
        const dealerValue = redDogRushGame.dealerCard.value;
        const minValue = Math.min(playerValue, dealerValue);
        const maxValue = Math.max(playerValue, dealerValue);
        
        // Find cards outside the spread
        const outsideSpreadCards = redDogRushGame.deck.filter(card => 
            card.value <= minValue || card.value >= maxValue
        );
        
        if (outsideSpreadCards.length > 0) {
            const cardIndex = redDogRushGame.deck.indexOf(outsideSpreadCards[0]);
            return redDogRushGame.deck.splice(cardIndex, 1)[0];
        }
    } else if (cardType === 'extreme' && Math.random() < cardBias * 1.2) {
        // Give extreme cards (A, 2, K, Q, J) to create bad spreads
        const extremeCards = redDogRushGame.deck.filter(card => 
            card.value <= 2 || card.value >= 11
        );
        
        if (extremeCards.length > 0) {
            const cardIndex = redDogRushGame.deck.indexOf(extremeCards[0]);
            return redDogRushGame.deck.splice(cardIndex, 1)[0];
        }
    }
    
    return redDogRushGame.deck.pop();
}

function calculateRushSpread(card1, card2) {
    const value1 = card1.value;
    const value2 = card2.value;
    const minValue = Math.min(value1, value2);
    const maxValue = Math.max(value1, value2);
    
    return maxValue - minValue - 1;
}

function startRushGame() {
    const betAmount = parseInt(document.getElementById('rushBet').value);
    
    if (betAmount > balance || betAmount <= 0) {
        updateRushStatus('Invalid bet amount!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    redDogRushGame.playerCard = null;
    redDogRushGame.dealerCard = null;
    redDogRushGame.middleCard = null;
    redDogRushGame.betAmount = betAmount;
    redDogRushGame.raiseBet = 0;
    redDogRushGame.gamePhase = 'betting';
    redDogRushGame.gameResult = null;
    redDogRushGame.totalWin = 0;
    redDogRushGame.cyberBonus = false;
    redDogRushGame.matrixPenalty = false;
    redDogRushGame.quantumNegation = false;
    
    redDogRushGame.stats.totalWagered += betAmount;
    
    // Create extremely biased deck
    redDogRushGame.deck = createRushDeck();
    
    // Deal first two cards with extreme bias toward bad spreads
    redDogRushGame.playerCard = dealBiasedRushCard('extreme');
    redDogRushGame.dealerCard = dealBiasedRushCard('extreme');
    
    // Calculate spread
    redDogRushGame.spread = calculateRushSpread(redDogRushGame.playerCard, redDogRushGame.dealerCard);
    
    // Check for consecutive cards (automatic loss)
    if (redDogRushGame.spread === 0) {
        redDogRushGame.gameResult = 'consecutive';
        redDogRushGame.gamePhase = 'complete';
        updateRushStatus('Consecutive cards - Cyber defeat!');
        updateRushGameStats();
        updateRushDisplay();
        return;
    }
    
    // Check for pair (push - but house takes commission)
    if (redDogRushGame.playerCard.value === redDogRushGame.dealerCard.value) {
        // House takes commission even on pushes
        const commission = Math.floor(betAmount * 0.12);
        redDogRushGame.totalWin = betAmount - commission;
        balance += redDogRushGame.totalWin;
        updateBalance();
        
        redDogRushGame.gameResult = 'pair';
        redDogRushGame.gamePhase = 'complete';
        updateRushStatus(`Pair - Push with ${commission} GA commission!`);
        updateRushGameStats();
        updateRushDisplay();
        return;
    }
    
    // Generate cyber effects
    generateRushEffects();
    
    // Apply spread reduction based on difficulty
    const difficultyData = RUSH_DIFFICULTIES[redDogRushGame.difficulty];
    if (Math.random() < difficultyData.spreadReduction) {
        redDogRushGame.spread = Math.max(1, redDogRushGame.spread - 1);
        updateRushStatus('Matrix interference - Spread reduced!');
    }
    
    updateRushDisplay();
    
    // Enable raise button for spreads 1-4 only
    document.getElementById('raiseRush').disabled = redDogRushGame.spread > 4;
    document.getElementById('callRush').disabled = false;
}

function generateRushEffects() {
    const modeData = RUSH_MODES[redDogRushGame.gameMode];
    const difficultyData = RUSH_DIFFICULTIES[redDogRushGame.difficulty];
    
    // Rush bonus (extremely rare)
    if (Math.random() < modeData.rushChance) {
        redDogRushGame.cyberBonus = true;
    }
    
    // Matrix penalty (very common)
    if (Math.random() < difficultyData.matrixChance + (redDogRushGame.rushLevel * 0.04)) {
        redDogRushGame.matrixPenalty = true;
    }
    
    // Quantum negation (common on potential wins)
    if (redDogRushGame.spread <= 3 && Math.random() < 0.35) {
        redDogRushGame.quantumNegation = true;
    }
    
    // Increase rush level occasionally (makes game harder)
    if (redDogRushGame.stats.handsPlayed > 0 && 
        redDogRushGame.stats.handsPlayed % 7 === 0 && 
        redDogRushGame.rushLevel < 10) {
        redDogRushGame.rushLevel++;
        updateRushStatus(`Rush Level increased to ${redDogRushGame.rushLevel}!`);
    }
}

function raiseRushBet() {
    if (redDogRushGame.gamePhase !== 'betting' || redDogRushGame.spread > 4) return;
    
    const raiseAmount = redDogRushGame.betAmount;
    
    if (raiseAmount > balance) {
        updateRushStatus('Insufficient balance to raise!');
        return;
    }
    
    // Deduct raise bet
    balance -= raiseAmount;
    updateBalance();
    
    redDogRushGame.raiseBet = raiseAmount;
    redDogRushGame.gamePhase = 'raised';
    redDogRushGame.stats.totalWagered += raiseAmount;
    
    updateRushStatus(`Raised by ${raiseAmount} GA! Total bet: ${redDogRushGame.betAmount + redDogRushGame.raiseBet} GA`);
    
    // Disable raise, enable call
    document.getElementById('raiseRush').disabled = true;
    document.getElementById('callRush').disabled = false;
    
    updateRushDisplay();
}

function callRushBet() {
    if (redDogRushGame.gamePhase !== 'betting' && redDogRushGame.gamePhase !== 'raised') return;
    
    redDogRushGame.gamePhase = 'revealing';
    
    // Deal middle card with extreme bias against player
    redDogRushGame.middleCard = dealBiasedRushCard('middle');
    
    // Add to card history for tracking
    redDogRushGame.cardHistory.push({
        player: redDogRushGame.playerCard.value,
        dealer: redDogRushGame.dealerCard.value,
        middle: redDogRushGame.middleCard.value,
        spread: redDogRushGame.spread
    });
    
    // Determine result
    const middleValue = redDogRushGame.middleCard.value;
    const playerValue = redDogRushGame.playerCard.value;
    const dealerValue = redDogRushGame.dealerCard.value;
    const minValue = Math.min(playerValue, dealerValue);
    const maxValue = Math.max(playerValue, dealerValue);
    
    if (middleValue > minValue && middleValue < maxValue) {
        // Player wins (extremely rare due to biased middle card)
        const modeData = RUSH_MODES[redDogRushGame.gameMode];
        
        // Apply quantum negation (house can negate wins)
        if (redDogRushGame.quantumNegation || Math.random() < modeData.houseEdge * 1.3) {
            redDogRushGame.gameResult = 'quantum_negated';
            redDogRushGame.stats.quantumNegations++;
            updateRushStatus('Win negated by quantum interference!');
        } else {
            redDogRushGame.gameResult = 'win';
            calculateRushWinnings();
            updateRushStatus(`Rush hit! Spread ${redDogRushGame.spread} wins!`);
        }
    } else {
        // Player loses (very common)
        redDogRushGame.gameResult = 'loss';
        redDogRushGame.stats.voidLosses++;
        updateRushStatus(`Middle card ${middleValue} misses spread - Void loss!`);
    }
    
    redDogRushGame.gamePhase = 'complete';
    updateRushGameStats();
    updateRushDisplay();
    
    // Disable buttons
    document.getElementById('raiseRush').disabled = true;
    document.getElementById('callRush').disabled = true;
    document.getElementById('dealRush').disabled = false;
}

function calculateRushWinnings() {
    const modeData = RUSH_MODES[redDogRushGame.gameMode];
    const difficultyData = RUSH_DIFFICULTIES[redDogRushGame.difficulty];
    
    // Get base payout (already severely reduced)
    let payout = RUSH_PAYOUTS[redDogRushGame.spread] || 0.2;
    
    // Apply mode multiplier (further reduces payouts)
    payout *= modeData.payoutMultiplier;
    
    // Apply spread penalty
    payout *= (1 - modeData.spreadPenalty);
    
    // Apply rush bonus (minimal effect)
    if (redDogRushGame.cyberBonus) {
        payout += 0.08; // Tiny bonus
        redDogRushGame.stats.rushBonuses++;
    }
    
    // Apply matrix penalty (severe)
    if (redDogRushGame.matrixPenalty) {
        payout *= 0.35; // 65% penalty
        redDogRushGame.stats.matrixPenalties++;
    }
    
    // Apply rush level penalty
    payout *= (1 - (redDogRushGame.rushLevel * 0.045));
    
    // Calculate total bet
    const totalBet = redDogRushGame.betAmount + redDogRushGame.raiseBet;
    
    // Calculate winnings
    let winnings = totalBet * payout;
    
    // Apply additional house edge
    winnings *= (1 - modeData.houseEdge * 0.35);
    
    // Minimum win of 1 GA
    redDogRushGame.totalWin = Math.floor(Math.max(1, winnings));
    
    // Add original bet back plus winnings
    balance += totalBet + redDogRushGame.totalWin;
    updateBalance();
}

function updateRushGameStats() {
    redDogRushGame.stats.handsPlayed++;
    
    if (redDogRushGame.gameResult === 'win') {
        redDogRushGame.stats.handsWon++;
        redDogRushGame.stats.totalWon += redDogRushGame.totalWin;
        redDogRushGame.stats.spreadsHit++;
        
        if (redDogRushGame.totalWin > redDogRushGame.stats.biggestWin) {
            redDogRushGame.stats.biggestWin = redDogRushGame.totalWin;
        }
        
        // Update win streak
        redDogRushGame.streakData.currentWinStreak++;
        redDogRushGame.streakData.currentLossStreak = 0;
        
        if (redDogRushGame.streakData.currentWinStreak > redDogRushGame.streakData.longestWinStreak) {
            redDogRushGame.streakData.longestWinStreak = redDogRushGame.streakData.currentWinStreak;
        }
    } else {
        // Update loss streak
        redDogRushGame.streakData.currentLossStreak++;
        redDogRushGame.streakData.currentWinStreak = 0;
        
        if (redDogRushGame.streakData.currentLossStreak > redDogRushGame.streakData.longestLossStreak) {
            redDogRushGame.streakData.longestLossStreak = redDogRushGame.streakData.currentLossStreak;
        }
    }
}

function updateRushDisplay() {
    // Update player card
    if (redDogRushGame.playerCard) {
        const playerCardEl = document.getElementById('rushPlayerCard');
        playerCardEl.innerHTML = `
            <div class="cyber-card neon-border">
                <div class="card-content">
                    <div class="card-rank">${redDogRushGame.playerCard.rank}</div>
                    <div class="card-suit">${redDogRushGame.playerCard.suit}</div>
                </div>
            </div>
        `;
    }
    
    // Update dealer card
    if (redDogRushGame.dealerCard) {
        const dealerCardEl = document.getElementById('rushDealerCard');
        dealerCardEl.innerHTML = `
            <div class="cyber-card neon-border">
                <div class="card-content">
                    <div class="card-rank">${redDogRushGame.dealerCard.rank}</div>
                    <div class="card-suit">${redDogRushGame.dealerCard.suit}</div>
                </div>
            </div>
        `;
    }
    
    // Update middle card
    const middleCardEl = document.getElementById('rushMiddleCard');
    if (redDogRushGame.middleCard) {
        middleCardEl.innerHTML = `
            <div class="cyber-card neon-border ${redDogRushGame.gameResult === 'win' ? 'winning-card' : 'losing-card'}">
                <div class="card-content">
                    <div class="card-rank">${redDogRushGame.middleCard.rank}</div>
                    <div class="card-suit">${redDogRushGame.middleCard.suit}</div>
                </div>
            </div>
        `;
    } else {
        middleCardEl.innerHTML = `
            <div class="cyber-card face-down">
                <div class="card-back">?</div>
            </div>
        `;
    }
    
    // Update spread display
    document.getElementById('rushSpread').textContent = redDogRushGame.spread;
    
    // Update payout display
    const payout = RUSH_PAYOUTS[redDogRushGame.spread] || 0;
    document.getElementById('rushPayout').textContent = `${payout.toFixed(1)}:1`;
    
    // Update rush effects
    document.getElementById('rushLevel').textContent = redDogRushGame.rushLevel;
    document.getElementById('rushBonus').style.display = redDogRushGame.cyberBonus ? 'block' : 'none';
    document.getElementById('matrixPenalty').style.display = redDogRushGame.matrixPenalty ? 'block' : 'none';
    document.getElementById('quantumNegation').style.display = redDogRushGame.quantumNegation ? 'block' : 'none';
    
    // Update game result
    if (redDogRushGame.gameResult) {
        const resultEl = document.getElementById('rushGameResult');
        if (redDogRushGame.gameResult === 'win') {
            resultEl.innerHTML = `CYBER RUSH! +${redDogRushGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono neon-glow';
        } else if (redDogRushGame.gameResult === 'pair') {
            resultEl.innerHTML = `PAIR PUSH - Commission Deducted`;
            resultEl.className = 'text-center text-xl font-bold text-yellow-400 h-8 font-mono';
        } else if (redDogRushGame.gameResult === 'quantum_negated') {
            resultEl.innerHTML = `QUANTUM VOID - Win Negated!`;
            resultEl.className = 'text-center text-xl font-bold text-purple-400 h-8 font-mono';
        } else {
            const totalLoss = redDogRushGame.betAmount + redDogRushGame.raiseBet;
            resultEl.innerHTML = `VOID DEFEAT - Lost ${totalLoss} GA`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    updateRushStats();
}

function updateRushStats() {
    document.getElementById('rushHandsPlayed').textContent = redDogRushGame.stats.handsPlayed;
    
    const winRate = redDogRushGame.stats.handsPlayed > 0 ? 
        ((redDogRushGame.stats.handsWon / redDogRushGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('rushWinRate').textContent = `${winRate}%`;
    
    document.getElementById('rushTotalWagered').textContent = redDogRushGame.stats.totalWagered.toLocaleString();
    document.getElementById('rushTotalWon').textContent = redDogRushGame.stats.totalWon.toLocaleString();
    document.getElementById('rushBiggestWin').textContent = redDogRushGame.stats.biggestWin.toLocaleString();
    document.getElementById('rushSpreadsHit').textContent = redDogRushGame.stats.spreadsHit;
    document.getElementById('rushLossStreak').textContent = redDogRushGame.streakData.currentLossStreak;
    document.getElementById('rushBonuses').textContent = redDogRushGame.stats.rushBonuses;
    document.getElementById('rushMatrixPenalties').textContent = redDogRushGame.stats.matrixPenalties;
    document.getElementById('rushQuantumNegations').textContent = redDogRushGame.stats.quantumNegations;
    document.getElementById('rushVoidLosses').textContent = redDogRushGame.stats.voidLosses;
}

function updateRushStatus(message) {
    document.getElementById('rushGameStatus').textContent = message;
}

function changeRushMode() {
    const mode = document.getElementById('rushMode').value;
    redDogRushGame.gameMode = mode;
    
    const modeData = RUSH_MODES[mode];
    document.getElementById('rushModeInfo').innerHTML = `
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Rush Chance: ${(modeData.rushChance * 100).toFixed(1)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}% | 
        Spread Penalty: ${(modeData.spreadPenalty * 100).toFixed(0)}%
    `;
}

function changeRushDifficulty() {
    const difficulty = document.getElementById('rushDifficulty').value;
    redDogRushGame.difficulty = difficulty;
    
    const diffData = RUSH_DIFFICULTIES[difficulty];
    document.getElementById('rushDifficultyInfo').innerHTML = `
        Card Bias: ${(diffData.cardBias * 100).toFixed(0)}% | 
        Spread Reduction: ${(diffData.spreadReduction * 100).toFixed(0)}% | 
        Matrix Chance: ${(diffData.matrixChance * 100).toFixed(0)}% | 
        Level Multiplier: ${diffData.levelMultiplier}x
    `;
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeRushMode();
    changeRushDifficulty();
    
    // Add event listeners
    document.getElementById('dealRush').addEventListener('click', startRushGame);
    document.getElementById('raiseRush').addEventListener('click', raiseRushBet);
    document.getElementById('callRush').addEventListener('click', callRushBet);
    document.getElementById('rushMode').addEventListener('change', changeRushMode);
    document.getElementById('rushDifficulty').addEventListener('change', changeRushDifficulty);
});