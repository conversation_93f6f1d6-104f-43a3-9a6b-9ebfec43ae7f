// Three Card Poker Deluxe - Ultra High House Edge Implementation
// Designed to maintain <10% player win rate with premium features

// Game state
let balance = 1000;

// Game state object with extreme house bias
let threeCardPokerDeluxeGame = {
    isPlaying: false,
    gameMode: 'deluxe', // deluxe, premium, ultimate, legendary
    difficulty: 'normal', // easy, normal, hard, nightmare, impossible
    anteBet: 0,
    playBet: 0,
    pairPlusBet: 0,
    sixCardBonusBet: 0, // Deluxe feature
    progressiveBet: 0, // Deluxe feature
    totalBet: 0,
    playerCards: [],
    dealerCards: [],
    playerHand: null,
    dealerHand: null,
    gameResult: '',
    totalWin: 0,
    deck: [],
    progressiveJackpot: 5000, // Fake progressive that rarely pays
    bonusRound: false,
    multiplierActive: false,
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        bonusRoundsTriggered: 0,
        progressiveHits: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game modes with extreme house bias (ensuring <8% win rate for deluxe)
const DELUXE_MODES = {
    deluxe: {
        name: 'Deluxe Edition',
        houseEdge: 0.52, // 52% house edge
        dealerAdvantage: 0.38, // 38% dealer advantage
        payoutMultiplier: 0.48, // Severely reduced payouts
        qualifierBias: 0.30, // 30% bias toward dealer qualifying
        bonusChance: 0.02 // 2% bonus chance
    },
    premium: {
        name: 'Premium Elite',
        houseEdge: 0.58, // 58% house edge
        dealerAdvantage: 0.45, // 45% dealer advantage
        payoutMultiplier: 0.42, // Even more reduced payouts
        qualifierBias: 0.38, // 38% bias toward dealer qualifying
        bonusChance: 0.015 // 1.5% bonus chance
    },
    ultimate: {
        name: 'Ultimate Master',
        houseEdge: 0.65, // 65% house edge
        dealerAdvantage: 0.52, // 52% dealer advantage
        payoutMultiplier: 0.35, // Extremely reduced payouts
        qualifierBias: 0.45, // 45% bias toward dealer qualifying
        bonusChance: 0.01 // 1% bonus chance
    },
    legendary: {
        name: 'Legendary Void',
        houseEdge: 0.72, // 72% house edge
        dealerAdvantage: 0.60, // 60% dealer advantage
        payoutMultiplier: 0.28, // Brutally reduced payouts
        qualifierBias: 0.55, // 55% bias toward dealer qualifying
        bonusChance: 0.005 // 0.5% bonus chance
    }
};

const DELUXE_DIFFICULTIES = {
    easy: {
        name: 'Novice Player',
        cardBias: 0.25, // 25% bias toward low cards for player
        dealerLuck: 0.18, // 18% dealer luck bonus
        payoutPenalty: 0.12, // 12% additional payout reduction
        progressiveTax: 0.15 // 15% progressive tax
    },
    normal: {
        name: 'Casual Gambler',
        cardBias: 0.35, // 35% bias toward low cards for player
        dealerLuck: 0.28, // 28% dealer luck bonus
        payoutPenalty: 0.22, // 22% additional payout reduction
        progressiveTax: 0.25 // 25% progressive tax
    },
    hard: {
        name: 'High Roller',
        cardBias: 0.45, // 45% bias toward low cards for player
        dealerLuck: 0.38, // 38% dealer luck bonus
        payoutPenalty: 0.32, // 32% additional payout reduction
        progressiveTax: 0.35 // 35% progressive tax
    },
    nightmare: {
        name: 'Casino Veteran',
        cardBias: 0.58, // 58% bias toward low cards for player
        dealerLuck: 0.52, // 52% dealer luck bonus
        payoutPenalty: 0.45, // 45% additional payout reduction
        progressiveTax: 0.48 // 48% progressive tax
    },
    impossible: {
        name: 'Quantum Nightmare',
        cardBias: 0.70, // 70% bias toward low cards for player
        dealerLuck: 0.65, // 65% dealer luck bonus
        payoutPenalty: 0.58, // 58% additional payout reduction
        progressiveTax: 0.60 // 60% progressive tax
    }
};

// Severely reduced payout table for deluxe version
const DELUXE_PAYOUTS = {
    // Ante/Play payouts (heavily reduced)
    STRAIGHT_FLUSH: 3, // Reduced from 5:1
    THREE_OF_A_KIND: 2, // Reduced from 4:1
    STRAIGHT: 0.8, // Less than even money
    FLUSH: 0.8, // Less than even money
    PAIR: 0.6, // Significantly less than even money
    HIGH_CARD: 0.5, // Half payout

    // Pair Plus payouts (brutally reduced)
    STRAIGHT_FLUSH_PP: 30, // Reduced from 40:1
    THREE_OF_A_KIND_PP: 20, // Reduced from 30:1
    STRAIGHT_PP: 4, // Reduced from 6:1
    FLUSH_PP: 2.5, // Reduced from 4:1
    PAIR_PP: 0.8, // Less than even money

    // Six Card Bonus (new deluxe feature with terrible odds)
    SIX_CARD_ROYAL: 500, // Extremely rare
    SIX_CARD_SF: 100, // Very rare
    SIX_CARD_4K: 50, // Rare
    SIX_CARD_FH: 25, // Uncommon
    SIX_CARD_FLUSH: 15, // Low payout
    SIX_CARD_STRAIGHT: 8, // Very low payout
    SIX_CARD_3K: 5, // Minimal payout

    // Progressive payouts (fake - almost never hit)
    PROGRESSIVE_ROYAL: 0.1, // 10% of displayed jackpot
    PROGRESSIVE_SF: 0.05, // 5% of displayed jackpot

    // Ante bonus (minimal)
    ANTE_BONUS_SF: 3, // Reduced from 5:1
    ANTE_BONUS_3K: 2, // Reduced from 4:1
    ANTE_BONUS_ST: 0.8 // Less than even money
};

// Hand rankings for Three Card Poker
const HAND_RANKINGS = {
    STRAIGHT_FLUSH: 8,
    THREE_OF_A_KIND: 7,
    STRAIGHT: 6,
    FLUSH: 5,
    PAIR: 4,
    HIGH_CARD: 3
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadThreeCardPokerDeluxeGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">DELUXE CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="deluxe">Deluxe Edition</option>
                            <option value="premium">Premium Elite</option>
                            <option value="ultimate">Ultimate Master</option>
                            <option value="legendary">Legendary Void</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Novice Player</option>
                            <option value="normal" selected>Casual Gambler</option>
                            <option value="hard">High Roller</option>
                            <option value="nightmare">Casino Veteran</option>
                            <option value="impossible">Quantum Nightmare</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ANTE BET</label>
                        <input type="number" id="anteBet" value="10" min="5" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PAIR PLUS BET</label>
                        <input type="number" id="pairPlusBet" value="0" min="0" max="${Math.min(balance, 200)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">6-CARD BONUS BET</label>
                        <input type="number" id="sixCardBonusBet" value="0" min="0" max="${Math.min(balance, 100)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PROGRESSIVE BET</label>
                        <input type="number" id="progressiveBet" value="0" min="0" max="${Math.min(balance, 50)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        DEAL CARDS
                    </button>

                    <div id="playerActions" class="space-y-2 hidden">
                        <button id="playHand" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            PLAY (Match Ante)
                        </button>
                        <button id="foldHand" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            FOLD
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Hand Strength</div>
                        <div id="handStrength" class="text-lg font-bold text-green-400">-</div>
                    </div>
                </div>

                <!-- Progressive Jackpot Display -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">PROGRESSIVE JACKPOT</h5>
                    <div class="text-center">
                        <div id="progressiveAmount" class="text-2xl font-bold text-yellow-400 neon-glow">$5,000</div>
                        <div class="text-xs text-gray-400 mt-1">Royal Flush Required</div>
                        <div class="text-xs text-red-400">Actual payout: 10% of displayed</div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">DELUXE PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Ante/Play:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">3:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Three of a Kind:</span>
                            <span class="text-red-400">2:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight:</span>
                            <span class="text-red-400">0.8:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Pair Plus:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">30:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Three of a Kind:</span>
                            <span class="text-red-400">20:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight:</span>
                            <span class="text-red-400">4:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">6-Card Bonus:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Royal Flush:</span>
                            <span class="text-red-400">500:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">100:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*All payouts subject to house edge reduction</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="pokerTable" class="relative bg-gradient-to-br from-purple-900 to-indigo-900 rounded-lg p-6 h-96">
                        <!-- Dealer Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">DEALER CARDS</div>
                                <div id="dealerCards" class="flex space-x-2 justify-center">
                                    <!-- Dealer cards will appear here -->
                                </div>
                                <div id="dealerHandType" class="text-xs text-gray-400 mt-1">-</div>
                            </div>
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">YOUR CARDS</div>
                                <div id="playerCards" class="flex space-x-2 justify-center">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div id="playerHandType" class="text-xs text-gray-400 mt-1">-</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Place your bets</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="bonusIndicator" class="text-sm text-purple-400 hidden">BONUS ROUND!</div>
                        </div>

                        <!-- Betting Areas Visual -->
                        <div class="absolute bottom-16 left-2">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">ANTE</div>
                                <div id="anteDisplay" class="text-sm font-bold text-yellow-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 left-16">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">PLAY</div>
                                <div id="playDisplay" class="text-sm font-bold text-green-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 right-16">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">PAIR+</div>
                                <div id="pairPlusDisplay" class="text-sm font-bold text-purple-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 right-2">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">6-CARD</div>
                                <div id="sixCardDisplay" class="text-sm font-bold text-blue-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute top-16 right-4">
                            <div class="text-center">
                                <div class="text-xs text-gray-400">PROGRESSIVE</div>
                                <div id="progressiveDisplay" class="text-sm font-bold text-yellow-400">$0</div>
                            </div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Three Card Poker Deluxe</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Bonus Rounds</div>
                <div id="bonusRounds" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Progressive Hits</div>
                <div id="progressiveHits" class="text-xl font-bold text-yellow-400">0</div>
            </div>
        </div>
    `;

    initializeThreeCardPokerDeluxe();
}

// Initialize the game
function initializeThreeCardPokerDeluxe() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);
    document.getElementById('playHand').addEventListener('click', playHand);
    document.getElementById('foldHand').addEventListener('click', foldHand);

    // Initialize deck and progressive
    initializeDeck();
    updateProgressiveJackpot();
    updateGameStats();
}

// Initialize deck with extreme bias toward dealer
function initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

    threeCardPokerDeluxeGame.deck = [];

    // Create multiple copies of high cards for dealer advantage
    const difficultyData = DELUXE_DIFFICULTIES[threeCardPokerDeluxeGame.difficulty];

    for (const suit of suits) {
        for (const rank of ranks) {
            const card = {
                rank,
                suit,
                value: getCardValue(rank),
                id: `${rank}${suit}`
            };

            threeCardPokerDeluxeGame.deck.push(card);

            // Add extra high cards for dealer bias (more extreme than regular version)
            if (card.value >= 11) { // J, Q, K, A
                const extraCopies = Math.floor(difficultyData.cardBias * 15); // More bias
                for (let i = 0; i < extraCopies; i++) {
                    threeCardPokerDeluxeGame.deck.push({...card, id: `${rank}${suit}_${i}`});
                }
            }
        }
    }

    shuffleDeck();
}

// Shuffle deck with extreme dealer bias
function shuffleDeck() {
    const difficultyData = DELUXE_DIFFICULTIES[threeCardPokerDeluxeGame.difficulty];

    // Standard shuffle
    for (let i = threeCardPokerDeluxeGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [threeCardPokerDeluxeGame.deck[i], threeCardPokerDeluxeGame.deck[j]] = [threeCardPokerDeluxeGame.deck[j], threeCardPokerDeluxeGame.deck[i]];
    }

    // Apply extreme dealer bias - move high cards toward top of deck
    if (Math.random() < difficultyData.dealerLuck) {
        const highCards = threeCardPokerDeluxeGame.deck.filter(card => card.value >= 10);
        const lowCards = threeCardPokerDeluxeGame.deck.filter(card => card.value < 10);

        // Extreme bias toward high cards at top (dealer gets first)
        threeCardPokerDeluxeGame.deck = [...highCards.slice(0, 15), ...lowCards, ...highCards.slice(15)];
    }
}

// Get card value for comparison
function getCardValue(rank) {
    if (rank === 'A') return 14;
    if (rank === 'K') return 13;
    if (rank === 'Q') return 12;
    if (rank === 'J') return 11;
    return parseInt(rank);
}

// Deal new hand with all deluxe features
function dealNewHand() {
    const anteBet = parseInt(document.getElementById('anteBet').value);
    const pairPlusBet = parseInt(document.getElementById('pairPlusBet').value) || 0;
    const sixCardBonusBet = parseInt(document.getElementById('sixCardBonusBet').value) || 0;
    const progressiveBet = parseInt(document.getElementById('progressiveBet').value) || 0;

    const totalBets = anteBet + pairPlusBet + sixCardBonusBet + progressiveBet;

    if (totalBets > balance) {
        alert('Insufficient balance!');
        return;
    }

    // Deduct all bets
    balance -= totalBets;
    updateBalance();

    // Set game state
    threeCardPokerDeluxeGame.isPlaying = true;
    threeCardPokerDeluxeGame.anteBet = anteBet;
    threeCardPokerDeluxeGame.pairPlusBet = pairPlusBet;
    threeCardPokerDeluxeGame.sixCardBonusBet = sixCardBonusBet;
    threeCardPokerDeluxeGame.progressiveBet = progressiveBet;
    threeCardPokerDeluxeGame.playBet = 0;
    threeCardPokerDeluxeGame.totalBet = totalBets;
    threeCardPokerDeluxeGame.gameMode = document.getElementById('gameMode').value;
    threeCardPokerDeluxeGame.difficulty = document.getElementById('difficulty').value;

    // Clear previous cards
    threeCardPokerDeluxeGame.playerCards = [];
    threeCardPokerDeluxeGame.dealerCards = [];

    // Check for bonus round (very rare)
    checkBonusRound();

    // Deal cards with extreme bias
    dealCardsWithExtremeBias();

    // Update display
    updateBetDisplay();
    updateGameStats();

    // Show player actions
    document.getElementById('playerActions').classList.remove('hidden');
    document.getElementById('dealCards').disabled = true;

    // Evaluate player hand
    evaluatePlayerHand();

    document.getElementById('gameStatus').textContent = 'Look at your cards - Play or Fold?';
    document.getElementById('gameMessage').textContent = 'Decide: Play to match your ante bet, or fold to forfeit all bets.';
}

// Check for bonus round (extremely rare)
function checkBonusRound() {
    const modeData = DELUXE_MODES[threeCardPokerDeluxeGame.gameMode];

    if (Math.random() < modeData.bonusChance) {
        threeCardPokerDeluxeGame.bonusRound = true;
        threeCardPokerDeluxeGame.multiplierActive = true;
        document.getElementById('bonusIndicator').classList.remove('hidden');
        threeCardPokerDeluxeGame.stats.bonusRoundsTriggered++;
    }
}

// Deal cards with extreme bias toward dealer
function dealCardsWithExtremeBias() {
    const difficultyData = DELUXE_DIFFICULTIES[threeCardPokerDeluxeGame.difficulty];

    // Deal 3 cards to dealer first (gets best cards)
    for (let i = 0; i < 3; i++) {
        const card = threeCardPokerDeluxeGame.deck.pop();
        threeCardPokerDeluxeGame.dealerCards.push(card);
    }

    // Deal 3 cards to player with extreme bias toward low cards
    for (let i = 0; i < 3; i++) {
        let card;

        // Apply extreme card bias - much higher chance of low cards for player
        if (Math.random() < difficultyData.cardBias) {
            // Force low card for player
            const lowCards = threeCardPokerDeluxeGame.deck.filter(c => c.value <= 8);
            if (lowCards.length > 0) {
                const randomIndex = Math.floor(Math.random() * lowCards.length);
                card = lowCards[randomIndex];
                // Remove from deck
                const deckIndex = threeCardPokerDeluxeGame.deck.findIndex(c => c.id === card.id);
                threeCardPokerDeluxeGame.deck.splice(deckIndex, 1);
            } else {
                card = threeCardPokerDeluxeGame.deck.pop();
            }
        } else {
            card = threeCardPokerDeluxeGame.deck.pop();
        }

        threeCardPokerDeluxeGame.playerCards.push(card);
    }

    // Display cards
    displayPlayerCards();
    displayDealerCards(true); // Hide dealer cards initially
}

// Display player cards
function displayPlayerCards() {
    const container = document.getElementById('playerCards');
    container.innerHTML = '';

    threeCardPokerDeluxeGame.playerCards.forEach(card => {
        const cardElement = createCardElement(card);
        container.appendChild(cardElement);
    });
}

// Display dealer cards
function displayDealerCards(hidden = false) {
    const container = document.getElementById('dealerCards');
    container.innerHTML = '';

    threeCardPokerDeluxeGame.dealerCards.forEach(card => {
        const cardElement = createCardElement(card, hidden);
        container.appendChild(cardElement);
    });
}

// Create card element with deluxe styling
function createCardElement(card, hidden = false) {
    const cardDiv = document.createElement('div');
    cardDiv.className = 'w-16 h-24 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-sm font-bold shadow-lg transform transition-all duration-300 hover:scale-105';

    if (hidden) {
        cardDiv.className += ' bg-purple-900 text-purple-300';
        cardDiv.innerHTML = '🂠';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardDiv.style.color = isRed ? '#dc2626' : '#000';
        cardDiv.innerHTML = `
            <div class="text-xs">${card.rank}</div>
            <div class="text-lg">${card.suit}</div>
            <div class="text-xs transform rotate-180">${card.rank}</div>
        `;

        // Add special effects for high cards
        if (card.value >= 11) {
            cardDiv.classList.add('ring-2', 'ring-yellow-400');
        }
    }

    return cardDiv;
}

// Evaluate player hand
function evaluatePlayerHand() {
    const hand = evaluateHand(threeCardPokerDeluxeGame.playerCards);
    threeCardPokerDeluxeGame.playerHand = hand;

    document.getElementById('handStrength').textContent = hand.name;
    document.getElementById('playerHandType').textContent = hand.name;
}

// Evaluate dealer hand
function evaluateDealerHand() {
    const hand = evaluateHand(threeCardPokerDeluxeGame.dealerCards);
    threeCardPokerDeluxeGame.dealerHand = hand;

    document.getElementById('dealerHandType').textContent = hand.name;
}

// Evaluate hand strength (same logic as regular version)
function evaluateHand(cards) {
    const sortedCards = cards.sort((a, b) => b.value - a.value);

    if (isFlush(cards) && isStraight(cards)) {
        return { rank: HAND_RANKINGS.STRAIGHT_FLUSH, name: 'Straight Flush', highCard: sortedCards[0].value };
    }

    if (isThreeOfAKind(cards)) {
        return { rank: HAND_RANKINGS.THREE_OF_A_KIND, name: 'Three of a Kind', highCard: sortedCards[0].value };
    }

    if (isStraight(cards)) {
        return { rank: HAND_RANKINGS.STRAIGHT, name: 'Straight', highCard: sortedCards[0].value };
    }

    if (isFlush(cards)) {
        return { rank: HAND_RANKINGS.FLUSH, name: 'Flush', highCard: sortedCards[0].value };
    }

    if (isPair(cards)) {
        const pairValue = getPairValue(cards);
        return { rank: HAND_RANKINGS.PAIR, name: 'Pair', highCard: pairValue };
    }

    return { rank: HAND_RANKINGS.HIGH_CARD, name: 'High Card', highCard: sortedCards[0].value };
}

// Helper functions for hand evaluation
function isFlush(cards) {
    return cards.every(card => card.suit === cards[0].suit);
}

function isStraight(cards) {
    const values = cards.map(card => card.value).sort((a, b) => a - b);

    if (values[0] === 2 && values[1] === 3 && values[2] === 14) {
        return true;
    }

    return values[1] === values[0] + 1 && values[2] === values[1] + 1;
}

function isThreeOfAKind(cards) {
    const values = cards.map(card => card.value);
    return values[0] === values[1] && values[1] === values[2];
}

function isPair(cards) {
    const values = cards.map(card => card.value);
    return values[0] === values[1] || values[1] === values[2] || values[0] === values[2];
}

function getPairValue(cards) {
    const values = cards.map(card => card.value);
    if (values[0] === values[1]) return values[0];
    if (values[1] === values[2]) return values[1];
    if (values[0] === values[2]) return values[0];
    return 0;
}

// Player chooses to play
function playHand() {
    threeCardPokerDeluxeGame.playBet = threeCardPokerDeluxeGame.anteBet;
    threeCardPokerDeluxeGame.totalBet += threeCardPokerDeluxeGame.playBet;

    // Deduct play bet
    balance -= threeCardPokerDeluxeGame.playBet;
    updateBalance();

    // Hide actions
    document.getElementById('playerActions').classList.add('hidden');

    // Update display
    updateBetDisplay();

    // Reveal dealer cards and resolve
    setTimeout(() => {
        revealDealerCards();
        setTimeout(() => resolveHandDeluxe(), 1000);
    }, 500);
}

// Player chooses to fold
function foldHand() {
    threeCardPokerDeluxeGame.gameResult = 'fold';

    // Update stats
    updateGameStatsAfterHand(false, 0);

    // Hide actions
    document.getElementById('playerActions').classList.add('hidden');

    document.getElementById('gameStatus').textContent = 'You folded';
    document.getElementById('gameMessage').innerHTML =
        `<span class="text-red-400">You folded and lost $${threeCardPokerDeluxeGame.totalBet}</span>`;

    setTimeout(() => resetGame(), 3000);
}

// Reveal dealer cards
function revealDealerCards() {
    displayDealerCards(false);
    evaluateDealerHand();
    document.getElementById('gameStatus').textContent = 'Dealer cards revealed...';
}

// Resolve hand with extreme house bias and deluxe features
function resolveHandDeluxe() {
    const modeData = DELUXE_MODES[threeCardPokerDeluxeGame.gameMode];
    const difficultyData = DELUXE_DIFFICULTIES[threeCardPokerDeluxeGame.difficulty];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if dealer qualifies with bias
    const dealerQualifies = checkDealerQualificationDeluxe();

    if (!dealerQualifies) {
        // Dealer doesn't qualify - ante pays even money, play bet pushes
        const anteWin = threeCardPokerDeluxeGame.anteBet * 1;
        totalWinnings += anteWin;
        resultMessage = `Dealer doesn't qualify. Ante wins $${anteWin}`;
        threeCardPokerDeluxeGame.gameResult = 'dealer_no_qualify';
    } else {
        // Dealer qualifies - compare hands with extreme bias
        const comparison = compareHandsWithExtremeBias();

        if (comparison > 0) {
            // Player wins (extremely rare)
            const anteWin = calculateDeluxeAnteWinnings();
            const playWin = calculateDeluxePlayWinnings();
            totalWinnings = anteWin + playWin;
            resultMessage = `You win! +$${totalWinnings}`;
            threeCardPokerDeluxeGame.gameResult = 'win';
        } else if (comparison < 0) {
            // Dealer wins (most common)
            totalWinnings = 0;
            resultMessage = `Dealer wins. Lost $${threeCardPokerDeluxeGame.anteBet + threeCardPokerDeluxeGame.playBet}`;
            threeCardPokerDeluxeGame.gameResult = 'lose';
        } else {
            // Tie (return bets)
            totalWinnings = threeCardPokerDeluxeGame.anteBet + threeCardPokerDeluxeGame.playBet;
            resultMessage = `Tie. Bets returned.`;
            threeCardPokerDeluxeGame.gameResult = 'tie';
        }
    }

    // Calculate side bet winnings (all with terrible odds)
    totalWinnings += calculateSideBetWinnings();

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier * (1 - difficultyData.payoutPenalty));

    // Apply progressive tax
    if (threeCardPokerDeluxeGame.progressiveBet > 0) {
        totalWinnings = Math.floor(totalWinnings * (1 - difficultyData.progressiveTax));
    }

    // Add winnings to balance
    balance += totalWinnings;
    threeCardPokerDeluxeGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > threeCardPokerDeluxeGame.totalBet, totalWinnings);
    updateProgressiveJackpot();

    document.getElementById('gameStatus').textContent = threeCardPokerDeluxeGame.gameResult === 'win' ? 'You Win!' : 'Dealer Wins';
    document.getElementById('gameMessage').innerHTML = resultMessage;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 4000);
}

// Check dealer qualification with extreme bias
function checkDealerQualificationDeluxe() {
    const dealerHand = threeCardPokerDeluxeGame.dealerHand;
    const modeData = DELUXE_MODES[threeCardPokerDeluxeGame.gameMode];

    // Standard qualification: Queen high or better
    let qualifies = false;
    if (dealerHand.rank > HAND_RANKINGS.HIGH_CARD) {
        qualifies = true;
    } else if (dealerHand.rank === HAND_RANKINGS.HIGH_CARD && dealerHand.highCard >= 12) {
        qualifies = true;
    }

    // Apply extreme qualifier bias - dealer almost always qualifies
    return qualifies || (Math.random() < modeData.qualifierBias);
}

// Compare hands with extreme bias toward dealer
function compareHandsWithExtremeBias() {
    const playerHand = threeCardPokerDeluxeGame.playerHand;
    const dealerHand = threeCardPokerDeluxeGame.dealerHand;
    const difficultyData = DELUXE_DIFFICULTIES[threeCardPokerDeluxeGame.difficulty];

    // Apply extreme dealer luck bonus
    const dealerBonusRank = dealerHand.rank + (difficultyData.dealerLuck * 3);
    const dealerBonusHigh = dealerHand.highCard + (difficultyData.dealerLuck * 5);

    // Compare ranks first
    if (playerHand.rank > dealerBonusRank) {
        return 1; // Player wins
    } else if (playerHand.rank < dealerBonusRank) {
        return -1; // Dealer wins
    } else {
        // Same rank, compare high cards
        if (playerHand.highCard > dealerBonusHigh) {
            return 1; // Player wins
        } else if (playerHand.highCard < dealerBonusHigh) {
            return -1; // Dealer wins
        } else {
            return 0; // Tie
        }
    }
}

// Calculate deluxe ante winnings (heavily reduced)
function calculateDeluxeAnteWinnings() {
    const playerHand = threeCardPokerDeluxeGame.playerHand;
    let multiplier = 1; // Base even money

    // Ante bonus for premium hands (severely reduced)
    if (playerHand.rank === HAND_RANKINGS.STRAIGHT_FLUSH) {
        multiplier += DELUXE_PAYOUTS.ANTE_BONUS_SF;
    } else if (playerHand.rank === HAND_RANKINGS.THREE_OF_A_KIND) {
        multiplier += DELUXE_PAYOUTS.ANTE_BONUS_3K;
    } else if (playerHand.rank === HAND_RANKINGS.STRAIGHT) {
        multiplier += DELUXE_PAYOUTS.ANTE_BONUS_ST;
    }

    return Math.floor(threeCardPokerDeluxeGame.anteBet * multiplier);
}

// Calculate deluxe play bet winnings
function calculateDeluxePlayWinnings() {
    const playerHand = threeCardPokerDeluxeGame.playerHand;
    let multiplier;

    // Heavily reduced payouts for play bet
    switch (playerHand.rank) {
        case HAND_RANKINGS.STRAIGHT_FLUSH:
            multiplier = DELUXE_PAYOUTS.STRAIGHT_FLUSH;
            break;
        case HAND_RANKINGS.THREE_OF_A_KIND:
            multiplier = DELUXE_PAYOUTS.THREE_OF_A_KIND;
            break;
        case HAND_RANKINGS.STRAIGHT:
            multiplier = DELUXE_PAYOUTS.STRAIGHT;
            break;
        case HAND_RANKINGS.FLUSH:
            multiplier = DELUXE_PAYOUTS.FLUSH;
            break;
        case HAND_RANKINGS.PAIR:
            multiplier = DELUXE_PAYOUTS.PAIR;
            break;
        default:
            multiplier = DELUXE_PAYOUTS.HIGH_CARD;
    }

    return Math.floor(threeCardPokerDeluxeGame.playBet * multiplier);
}

// Calculate all side bet winnings (terrible odds)
function calculateSideBetWinnings() {
    let totalSideWinnings = 0;

    // Pair Plus winnings
    if (threeCardPokerDeluxeGame.pairPlusBet > 0) {
        totalSideWinnings += calculatePairPlusWinningsDeluxe();
    }

    // Six Card Bonus winnings
    if (threeCardPokerDeluxeGame.sixCardBonusBet > 0) {
        totalSideWinnings += calculateSixCardBonusWinnings();
    }

    // Progressive winnings (almost never pays)
    if (threeCardPokerDeluxeGame.progressiveBet > 0) {
        totalSideWinnings += calculateProgressiveWinnings();
    }

    return totalSideWinnings;
}

// Calculate Pair Plus winnings (heavily reduced)
function calculatePairPlusWinningsDeluxe() {
    const playerHand = threeCardPokerDeluxeGame.playerHand;
    let multiplier = 0;

    switch (playerHand.rank) {
        case HAND_RANKINGS.STRAIGHT_FLUSH:
            multiplier = DELUXE_PAYOUTS.STRAIGHT_FLUSH_PP;
            break;
        case HAND_RANKINGS.THREE_OF_A_KIND:
            multiplier = DELUXE_PAYOUTS.THREE_OF_A_KIND_PP;
            break;
        case HAND_RANKINGS.STRAIGHT:
            multiplier = DELUXE_PAYOUTS.STRAIGHT_PP;
            break;
        case HAND_RANKINGS.FLUSH:
            multiplier = DELUXE_PAYOUTS.FLUSH_PP;
            break;
        case HAND_RANKINGS.PAIR:
            multiplier = DELUXE_PAYOUTS.PAIR_PP;
            break;
        default:
            return 0; // No payout for high card
    }

    return Math.floor(threeCardPokerDeluxeGame.pairPlusBet * multiplier);
}

// Calculate Six Card Bonus winnings (extremely rare)
function calculateSixCardBonusWinnings() {
    // Combine player and dealer cards for 6-card evaluation
    const allCards = [...threeCardPokerDeluxeGame.playerCards, ...threeCardPokerDeluxeGame.dealerCards];
    const sixCardHand = evaluateSixCardHand(allCards);

    let multiplier = 0;

    // Extremely rare payouts
    if (sixCardHand.isRoyalFlush && Math.random() < 0.001) { // 0.1% chance even with royal
        multiplier = DELUXE_PAYOUTS.SIX_CARD_ROYAL;
    } else if (sixCardHand.isStraightFlush && Math.random() < 0.005) { // 0.5% chance
        multiplier = DELUXE_PAYOUTS.SIX_CARD_SF;
    } else if (sixCardHand.isFourOfAKind && Math.random() < 0.01) { // 1% chance
        multiplier = DELUXE_PAYOUTS.SIX_CARD_4K;
    }
    // Other hands have even worse odds...

    return Math.floor(threeCardPokerDeluxeGame.sixCardBonusBet * multiplier);
}

// Calculate Progressive winnings (fake jackpot)
function calculateProgressiveWinnings() {
    const playerHand = threeCardPokerDeluxeGame.playerHand;

    // Only royal flush or straight flush can win (extremely rare)
    if (playerHand.rank === HAND_RANKINGS.STRAIGHT_FLUSH) {
        if (playerHand.highCard === 14 && Math.random() < 0.0001) { // 0.01% chance for royal
            threeCardPokerDeluxeGame.stats.progressiveHits++;
            return Math.floor(threeCardPokerDeluxeGame.progressiveJackpot * DELUXE_PAYOUTS.PROGRESSIVE_ROYAL);
        } else if (Math.random() < 0.0005) { // 0.05% chance for SF
            return Math.floor(threeCardPokerDeluxeGame.progressiveJackpot * DELUXE_PAYOUTS.PROGRESSIVE_SF);
        }
    }

    return 0;
}

// Evaluate six card hand (simplified)
function evaluateSixCardHand(cards) {
    // This is a simplified evaluation - in reality would be much more complex
    const suits = {};
    const ranks = {};

    cards.forEach(card => {
        suits[card.suit] = (suits[card.suit] || 0) + 1;
        ranks[card.value] = (ranks[card.value] || 0) + 1;
    });

    const isFlush = Object.values(suits).some(count => count >= 5);
    const isFourOfAKind = Object.values(ranks).some(count => count >= 4);
    const isRoyalFlush = isFlush && cards.some(c => c.value === 14) && cards.some(c => c.value === 13);

    return {
        isRoyalFlush,
        isStraightFlush: isFlush,
        isFourOfAKind,
        isFlush
    };
}

// Update progressive jackpot (fake growth)
function updateProgressiveJackpot() {
    // Fake progressive growth
    threeCardPokerDeluxeGame.progressiveJackpot += Math.floor(Math.random() * 10) + 1;
    document.getElementById('progressiveAmount').textContent = `$${threeCardPokerDeluxeGame.progressiveJackpot.toLocaleString()}`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('anteDisplay').textContent = `$${threeCardPokerDeluxeGame.anteBet}`;
    document.getElementById('playDisplay').textContent = `$${threeCardPokerDeluxeGame.playBet}`;
    document.getElementById('pairPlusDisplay').textContent = `$${threeCardPokerDeluxeGame.pairPlusBet}`;
    document.getElementById('sixCardDisplay').textContent = `$${threeCardPokerDeluxeGame.sixCardBonusBet}`;
    document.getElementById('progressiveDisplay').textContent = `$${threeCardPokerDeluxeGame.progressiveBet}`;
    document.getElementById('totalBetDisplay').textContent = `$${threeCardPokerDeluxeGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = threeCardPokerDeluxeGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${threeCardPokerDeluxeGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${threeCardPokerDeluxeGame.stats.totalWagered}`;
    document.getElementById('bonusRounds').textContent = threeCardPokerDeluxeGame.stats.bonusRoundsTriggered;
    document.getElementById('progressiveHits').textContent = threeCardPokerDeluxeGame.stats.progressiveHits;

    const netResult = threeCardPokerDeluxeGame.stats.totalWon - threeCardPokerDeluxeGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    threeCardPokerDeluxeGame.stats.handsPlayed++;
    threeCardPokerDeluxeGame.stats.totalWagered += threeCardPokerDeluxeGame.totalBet;
    threeCardPokerDeluxeGame.stats.totalWon += winnings;

    if (won) {
        threeCardPokerDeluxeGame.stats.handsWon++;
        threeCardPokerDeluxeGame.stats.currentStreak++;
        threeCardPokerDeluxeGame.streakData.currentWinStreak++;
        threeCardPokerDeluxeGame.streakData.currentLossStreak = 0;

        if (threeCardPokerDeluxeGame.streakData.currentWinStreak > threeCardPokerDeluxeGame.streakData.longestWinStreak) {
            threeCardPokerDeluxeGame.streakData.longestWinStreak = threeCardPokerDeluxeGame.streakData.currentWinStreak;
        }

        if (winnings > threeCardPokerDeluxeGame.stats.biggestWin) {
            threeCardPokerDeluxeGame.stats.biggestWin = winnings;
        }
    } else {
        threeCardPokerDeluxeGame.stats.currentStreak = 0;
        threeCardPokerDeluxeGame.streakData.currentWinStreak = 0;
        threeCardPokerDeluxeGame.streakData.currentLossStreak++;

        if (threeCardPokerDeluxeGame.streakData.currentLossStreak > threeCardPokerDeluxeGame.streakData.longestLossStreak) {
            threeCardPokerDeluxeGame.streakData.longestLossStreak = threeCardPokerDeluxeGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to house edge)
    threeCardPokerDeluxeGame.stats.winRate = (threeCardPokerDeluxeGame.stats.handsWon / threeCardPokerDeluxeGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    threeCardPokerDeluxeGame.isPlaying = false;
    threeCardPokerDeluxeGame.anteBet = 0;
    threeCardPokerDeluxeGame.playBet = 0;
    threeCardPokerDeluxeGame.pairPlusBet = 0;
    threeCardPokerDeluxeGame.sixCardBonusBet = 0;
    threeCardPokerDeluxeGame.progressiveBet = 0;
    threeCardPokerDeluxeGame.totalBet = 0;
    threeCardPokerDeluxeGame.playerCards = [];
    threeCardPokerDeluxeGame.dealerCards = [];
    threeCardPokerDeluxeGame.playerHand = null;
    threeCardPokerDeluxeGame.dealerHand = null;
    threeCardPokerDeluxeGame.gameResult = '';
    threeCardPokerDeluxeGame.totalWin = 0;
    threeCardPokerDeluxeGame.bonusRound = false;
    threeCardPokerDeluxeGame.multiplierActive = false;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    document.getElementById('playerHandType').textContent = '-';
    document.getElementById('dealerHandType').textContent = '-';
    document.getElementById('handStrength').textContent = '-';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('bonusIndicator').classList.add('hidden');

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;
    document.getElementById('playerActions').classList.add('hidden');

    // Reset status
    document.getElementById('gameStatus').textContent = 'Place your bets';
    document.getElementById('gameMessage').textContent = 'Welcome to Three Card Poker Deluxe';

    // Reinitialize deck for next hand
    initializeDeck();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadThreeCardPokerDeluxeGame();
});