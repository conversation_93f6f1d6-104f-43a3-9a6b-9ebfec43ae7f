// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('let_it_ride');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        balance = walletIntegration.balance;
        walletIntegration.updateBalanceDisplay();
    }
}

// Let It Ride: Frontier Fortune - Poker variant with extremely low win rate (<10%)
let letItRideGame = {
    deck: [],
    playerCards: [],
    communityCards: [],
    bets: [0, 0, 0], // Three betting circles
    gamePhase: 'betting', // betting, reveal1, reveal2, complete
    totalWin: 0,
    handResult: null,
    difficulty: 'normal', // easy, normal, hard, frontier
    gameMode: 'classic', // classic, frontier, wildwest
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        pairTensWins: 0,
        straightWins: 0,
        flushWins: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Card constants
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_VALUES = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

// Game modes with heavy house edge
const LET_IT_RIDE_MODES = {
    classic: { name: 'Classic', houseEdge: 0.25, deckBias: 0.15 },
    frontier: { name: 'Frontier', houseEdge: 0.35, deckBias: 0.25 },
    wildwest: { name: 'Wild West', houseEdge: 0.45, deckBias: 0.35 }
};

const LET_IT_RIDE_DIFFICULTIES = {
    easy: { name: 'Easy', payoutReduction: 0.15, cardBias: 0.10 },
    normal: { name: 'Normal', payoutReduction: 0.25, cardBias: 0.20 },
    hard: { name: 'Hard', payoutReduction: 0.35, cardBias: 0.30 },
    frontier: { name: 'Frontier', payoutReduction: 0.45, cardBias: 0.40 }
};

// Extremely low payout table to ensure <10% win rate
const LET_IT_RIDE_PAYOUTS = {
    'Royal Flush': 800,      // Reduced from 1000
    'Straight Flush': 150,   // Reduced from 200
    'Four of a Kind': 40,    // Reduced from 50
    'Full House': 8,         // Reduced from 11
    'Flush': 5,              // Reduced from 8
    'Straight': 4,           // Reduced from 5
    'Three of a Kind': 2,    // Reduced from 3
    'Two Pair': 1.5,         // Reduced from 2
    'Pair of 10s or Better': 0.8  // Reduced from 1
};

function loadLetItRideGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Betting Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                <h4 class="text-xl font-bold mb-4 text-amber-300 font-mono">SALOON STAKES</h4>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">BET AMOUNT</label>
                    <input type="number" id="letItRideBet" value="25" min="5" max="500" step="5"
                           class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">GAME MODE</label>
                    <select id="letItRideMode" class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                        <option value="classic">Classic</option>
                        <option value="frontier">Frontier (Higher Stakes)</option>
                        <option value="wildwest">Wild West (Extreme)</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-2 text-gray-300">DIFFICULTY</label>
                    <select id="letItRideDifficulty" class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                        <option value="easy">Easy</option>
                        <option value="normal">Normal</option>
                        <option value="hard">Hard</option>
                        <option value="frontier">Frontier</option>
                    </select>
                </div>
                
                <button id="dealCards" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                    DEAL CARDS
                </button>
                
                <div class="space-y-2">
                    <button id="pullBet1" class="w-full bg-red-600 hover:bg-red-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        PULL BET 1
                    </button>
                    <button id="letItRide1" class="w-full bg-green-600 hover:bg-green-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        LET IT RIDE 1
                    </button>
                    <button id="pullBet2" class="w-full bg-red-600 hover:bg-red-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        PULL BET 2
                    </button>
                    <button id="letItRide2" class="w-full bg-green-600 hover:bg-green-700 py-2 rounded-lg font-semibold text-white disabled:opacity-50" disabled>
                        LET IT RIDE 2
                    </button>
                </div>
                
                <div class="mt-4 p-3 bg-amber-900/30 rounded-lg border border-amber-500/30">
                    <p class="text-amber-300 font-mono text-sm">Active Bets:</p>
                    <p class="text-white">Bet 1: <span id="bet1Amount">0</span> GA</p>
                    <p class="text-white">Bet 2: <span id="bet2Amount">0</span> GA</p>
                    <p class="text-white">Bet 3: <span id="bet3Amount">0</span> GA</p>
                </div>
            </div>
            
            <!-- Game Table -->
            <div class="col-span-2 bg-black/30 p-6 rounded-xl border border-amber-500/30">
                <h4 class="text-xl font-bold mb-4 text-amber-300 font-mono text-center">POKER TABLE</h4>
                
                <!-- Player Cards -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Your Cards</h5>
                    <div id="playerCards" class="flex gap-2 justify-center">
                        <div class="card-back">?</div>
                        <div class="card-back">?</div>
                    </div>
                </div>
                
                <!-- Community Cards -->
                <div class="mb-6">
                    <h5 class="text-lg font-semibold mb-2 text-gray-300">Community Cards</h5>
                    <div id="communityCards" class="flex gap-2 justify-center">
                        <div class="card-back">?</div>
                        <div class="card-back">?</div>
                        <div class="card-back">?</div>
                    </div>
                </div>
                
                <!-- Betting Circles -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="betting-circle" id="bettingCircle1">
                        <span class="text-amber-300 font-bold">BET 1</span>
                        <span id="circle1Amount" class="text-white">0 GA</span>
                    </div>
                    <div class="betting-circle" id="bettingCircle2">
                        <span class="text-amber-300 font-bold">BET 2</span>
                        <span id="circle2Amount" class="text-white">0 GA</span>
                    </div>
                    <div class="betting-circle" id="bettingCircle3">
                        <span class="text-amber-300 font-bold">BET 3</span>
                        <span id="circle3Amount" class="text-white">0 GA</span>
                    </div>
                </div>
                
                <!-- Game Status -->
                <div class="text-center">
                    <div id="gameStatus" class="text-lg font-semibold text-amber-300 mb-2">Place your bets to start</div>
                    <div id="handResult" class="text-center text-xl font-bold text-green-400 h-8 font-mono"></div>
                </div>
            </div>
            
            <!-- Stats Panel -->
            <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                <h4 class="text-xl font-bold mb-4 text-amber-300 font-mono">FRONTIER STATS</h4>
                
                <div class="space-y-3">
                    <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                        <p class="text-gray-300 text-sm">Hands Played: <span id="letItRideHandsPlayed" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                        <p class="text-gray-300 text-sm">Win Rate: <span id="letItRideWinRate" class="text-white">0%</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                        <p class="text-gray-300 text-sm">Total Wagered: <span id="letItRideTotalWagered" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                        <p class="text-gray-300 text-sm">Total Won: <span id="letItRideTotalWon" class="text-white">0</span></p>
                    </div>
                    <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                        <p class="text-gray-300 text-sm">Biggest Win: <span id="letItRideBiggestWin" class="text-white">0</span></p>
                    </div>
                    <div class="bg-red-900/50 p-2 rounded border border-red-500/30">
                        <p class="text-red-300 text-sm">Loss Streak: <span id="letItRideLossStreak" class="text-white">0</span></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 bg-black/30 p-4 rounded-xl border border-amber-500/30">
            <h4 class="text-xl font-bold mb-4 text-amber-300 font-mono">PAYOUT TABLE</h4>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Royal Flush: 800:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Straight Flush: 150:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Four of a Kind: 40:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Full House: 8:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Flush: 5:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Straight: 4:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Three of a Kind: 2:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Two Pair: 1.5:1</p>
                </div>
                <div class="bg-black/50 p-2 rounded border border-amber-500/30">
                    <p class="text-amber-400">Pair 10s+: 0.8:1</p>
                </div>
            </div>
            <p class="text-amber-400 text-xs mt-2">* Payouts reduced based on difficulty and mode settings</p>
        </div>
    `;
    setupLetItRideGame();
}

function setupLetItRideGame() {
    document.getElementById('dealCards').addEventListener('click', startNewGame);
    document.getElementById('pullBet1').addEventListener('click', () => pullBet(1));
    document.getElementById('letItRide1').addEventListener('click', () => letItRide(1));
    document.getElementById('pullBet2').addEventListener('click', () => pullBet(2));
    document.getElementById('letItRide2').addEventListener('click', () => letItRide(2));
    
    document.getElementById('letItRideMode').addEventListener('change', function() {
        letItRideGame.gameMode = this.value;
        updateLetItRideDisplay();
    });
    
    document.getElementById('letItRideDifficulty').addEventListener('change', function() {
        letItRideGame.difficulty = this.value;
        updateLetItRideDisplay();
    });
    
    updateLetItRideDisplay();
}

function createBiasedDeck() {
    const modeData = LET_IT_RIDE_MODES[letItRideGame.gameMode];
    const difficultyData = LET_IT_RIDE_DIFFICULTIES[letItRideGame.difficulty];
    
    // Create heavily biased deck against good hands
    const biasedDeck = [];
    
    for (const suit of CARD_SUITS) {
        for (const value of CARD_VALUES) {
            const card = { suit, value };
            const cardValue = getCardValue(card);
            
            // Reduce high cards and pairs significantly
            let cardWeight = 1;
            
            // Reduce face cards and aces
            if (['J', 'Q', 'K', 'A'].includes(value)) {
                cardWeight *= (1 - modeData.deckBias);
            }
            
            // Reduce 10s (needed for pairs of 10s or better)
            if (value === '10') {
                cardWeight *= (1 - difficultyData.cardBias);
            }
            
            // Add cards based on weight
            const cardCount = Math.max(1, Math.floor(cardWeight * 10));
            for (let i = 0; i < cardCount; i++) {
                biasedDeck.push({ ...card });
            }
        }
    }
    
    return shuffleDeck(biasedDeck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function getCardValue(card) {
    if (card.value === 'A') return 14;
    if (card.value === 'K') return 13;
    if (card.value === 'Q') return 12;
    if (card.value === 'J') return 11;
    return parseInt(card.value);
}

async function startNewGame() {
    const betAmount = parseInt(document.getElementById('letItRideBet').value);
    const totalBet = betAmount * 3;

    // Use wallet integration to process bet
    if (!walletIntegration || !(await walletIntegration.processBet(totalBet, {
        game_action: 'start_game',
        bet_amount: betAmount,
        total_bet: totalBet,
        game_mode: letItRideGame.gameMode,
        difficulty: letItRideGame.difficulty
    }))) {
        return;
    }

    // Update local balance reference
    balance = walletIntegration.balance;
    
    // Initialize game
    letItRideGame.deck = createBiasedDeck();
    letItRideGame.playerCards = [];
    letItRideGame.communityCards = [];
    letItRideGame.bets = [betAmount, betAmount, betAmount];
    letItRideGame.gamePhase = 'reveal1';
    letItRideGame.totalWin = 0;
    letItRideGame.handResult = null;
    
    letItRideGame.stats.totalWagered += betAmount * 3;
    
    // Deal initial cards
    dealInitialCards();
    
    // Update display
    updateLetItRideDisplay();
    updateGameStatus('First community card revealed. Pull Bet 1 or Let It Ride?');
    
    // Enable first decision buttons
    document.getElementById('pullBet1').disabled = false;
    document.getElementById('letItRide1').disabled = false;
    document.getElementById('dealCards').disabled = true;
}

function dealInitialCards() {
    // Deal 2 cards to player
    letItRideGame.playerCards = [
        letItRideGame.deck.pop(),
        letItRideGame.deck.pop()
    ];
    
    // Deal 3 community cards
    letItRideGame.communityCards = [
        letItRideGame.deck.pop(),
        letItRideGame.deck.pop(),
        letItRideGame.deck.pop()
    ];
    
    // Display player cards
    displayPlayerCards();
    
    // Show first community card
    displayCommunityCards(1);
}

function displayPlayerCards() {
    const playerCardsEl = document.getElementById('playerCards');
    playerCardsEl.innerHTML = '';
    
    letItRideGame.playerCards.forEach(card => {
        const cardEl = document.createElement('div');
        cardEl.className = 'playing-card';
        cardEl.innerHTML = `
            <div class="card-content">
                <span class="card-value">${card.value}</span>
                <span class="card-suit ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'}">${card.suit}</span>
            </div>
        `;
        playerCardsEl.appendChild(cardEl);
    });
}

function displayCommunityCards(revealed) {
    const communityCardsEl = document.getElementById('communityCards');
    communityCardsEl.innerHTML = '';
    
    letItRideGame.communityCards.forEach((card, index) => {
        const cardEl = document.createElement('div');
        
        if (index < revealed) {
            cardEl.className = 'playing-card';
            cardEl.innerHTML = `
                <div class="card-content">
                    <span class="card-value">${card.value}</span>
                    <span class="card-suit ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'}">${card.suit}</span>
                </div>
            `;
        } else {
            cardEl.className = 'card-back';
            cardEl.textContent = '?';
        }
        
        communityCardsEl.appendChild(cardEl);
    });
}

async function pullBet(betNumber) {
    const refundAmount = letItRideGame.bets[betNumber - 1];

    // Return bet to player through wallet integration
    if (refundAmount > 0 && walletIntegration) {
        await walletIntegration.processWin(refundAmount, {
            game_action: 'pull_bet',
            bet_number: betNumber,
            refund_amount: refundAmount
        });
        balance = walletIntegration.balance;
    }
    
    letItRideGame.bets[betNumber - 1] = 0;
    
    if (betNumber === 1) {
        // Disable first decision buttons, show second community card
        document.getElementById('pullBet1').disabled = true;
        document.getElementById('letItRide1').disabled = true;
        
        displayCommunityCards(2);
        letItRideGame.gamePhase = 'reveal2';
        updateGameStatus('Second community card revealed. Pull Bet 2 or Let It Ride?');
        
        // Enable second decision buttons
        document.getElementById('pullBet2').disabled = false;
        document.getElementById('letItRide2').disabled = false;
    } else {
        // Final reveal
        finalReveal();
    }
    
    updateLetItRideDisplay();
}

function letItRide(betNumber) {
    if (betNumber === 1) {
        // Disable first decision buttons, show second community card
        document.getElementById('pullBet1').disabled = true;
        document.getElementById('letItRide1').disabled = true;
        
        displayCommunityCards(2);
        letItRideGame.gamePhase = 'reveal2';
        updateGameStatus('Second community card revealed. Pull Bet 2 or Let It Ride?');
        
        // Enable second decision buttons
        document.getElementById('pullBet2').disabled = false;
        document.getElementById('letItRide2').disabled = false;
    } else {
        // Final reveal
        finalReveal();
    }
    
    updateLetItRideDisplay();
}

function finalReveal() {
    // Show all community cards
    displayCommunityCards(3);
    letItRideGame.gamePhase = 'complete';
    
    // Disable all decision buttons
    document.getElementById('pullBet2').disabled = true;
    document.getElementById('letItRide2').disabled = true;
    
    // Evaluate hand
    const handResult = evaluateHand();
    letItRideGame.handResult = handResult;
    
    // Calculate winnings with heavy house edge
    calculateWinnings(handResult);
    
    // Update stats
    updateGameStats(handResult);
    
    // Update display
    updateLetItRideDisplay();
    updateGameStatus(`Hand complete: ${handResult.name}`);
    
    // Enable new game
    document.getElementById('dealCards').disabled = false;
}

function evaluateHand() {
    // Combine player cards with community cards
    const allCards = [...letItRideGame.playerCards, ...letItRideGame.communityCards];
    
    // Sort cards by value
    allCards.sort((a, b) => getCardValue(b) - getCardValue(a));
    
    // Check for each hand type (in order of strength)
    if (isRoyalFlush(allCards)) return { name: 'Royal Flush', rank: 10 };
    if (isStraightFlush(allCards)) return { name: 'Straight Flush', rank: 9 };
    if (isFourOfAKind(allCards)) return { name: 'Four of a Kind', rank: 8 };
    if (isFullHouse(allCards)) return { name: 'Full House', rank: 7 };
    if (isFlush(allCards)) return { name: 'Flush', rank: 6 };
    if (isStraight(allCards)) return { name: 'Straight', rank: 5 };
    if (isThreeOfAKind(allCards)) return { name: 'Three of a Kind', rank: 4 };
    if (isTwoPair(allCards)) return { name: 'Two Pair', rank: 3 };
    if (isPairTensOrBetter(allCards)) return { name: 'Pair of 10s or Better', rank: 2 };
    
    return { name: 'High Card', rank: 1 };
}

function isRoyalFlush(cards) {
    const royalValues = ['A', 'K', 'Q', 'J', '10'];
    for (const suit of CARD_SUITS) {
        const suitCards = cards.filter(card => card.suit === suit);
        if (suitCards.length >= 5) {
            const hasAllRoyal = royalValues.every(value => 
                suitCards.some(card => card.value === value)
            );
            if (hasAllRoyal) return true;
        }
    }
    return false;
}

function isStraightFlush(cards) {
    for (const suit of CARD_SUITS) {
        const suitCards = cards.filter(card => card.suit === suit);
        if (suitCards.length >= 5 && isStraight(suitCards)) {
            return true;
        }
    }
    return false;
}

function isFourOfAKind(cards) {
    const valueCounts = {};
    cards.forEach(card => {
        valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
    });
    return Object.values(valueCounts).some(count => count >= 4);
}

function isFullHouse(cards) {
    const valueCounts = {};
    cards.forEach(card => {
        valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
    });
    const counts = Object.values(valueCounts).sort((a, b) => b - a);
    return counts[0] >= 3 && counts[1] >= 2;
}

function isFlush(cards) {
    const suitCounts = {};
    cards.forEach(card => {
        suitCounts[card.suit] = (suitCounts[card.suit] || 0) + 1;
    });
    return Object.values(suitCounts).some(count => count >= 5);
}

function isStraight(cards) {
    const values = [...new Set(cards.map(card => getCardValue(card)))].sort((a, b) => b - a);
    
    // Check for regular straight
    for (let i = 0; i <= values.length - 5; i++) {
        let consecutive = true;
        for (let j = 1; j < 5; j++) {
            if (values[i + j] !== values[i] - j) {
                consecutive = false;
                break;
            }
        }
        if (consecutive) return true;
    }
    
    // Check for A-2-3-4-5 straight
    if (values.includes(14) && values.includes(5) && values.includes(4) && values.includes(3) && values.includes(2)) {
        return true;
    }
    
    return false;
}

function isThreeOfAKind(cards) {
    const valueCounts = {};
    cards.forEach(card => {
        valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
    });
    return Object.values(valueCounts).some(count => count >= 3);
}

function isTwoPair(cards) {
    const valueCounts = {};
    cards.forEach(card => {
        valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
    });
    const pairs = Object.values(valueCounts).filter(count => count >= 2);
    return pairs.length >= 2;
}

function isPairTensOrBetter(cards) {
    const valueCounts = {};
    cards.forEach(card => {
        valueCounts[card.value] = (valueCounts[card.value] || 0) + 1;
    });
    
    const highPairs = ['10', 'J', 'Q', 'K', 'A'];
    return highPairs.some(value => valueCounts[value] >= 2);
}

async function calculateWinnings(handResult) {
    if (handResult.rank < 2) {
        // No winning hand
        letItRideGame.totalWin = 0;
        return;
    }
    
    // Get base payout
    const basePayout = LET_IT_RIDE_PAYOUTS[handResult.name] || 0;
    
    // Apply mode and difficulty penalties
    const modeData = LET_IT_RIDE_MODES[letItRideGame.gameMode];
    const difficultyData = LET_IT_RIDE_DIFFICULTIES[letItRideGame.difficulty];
    
    // Calculate total house edge
    const totalHouseEdge = modeData.houseEdge + (difficultyData.payoutReduction * 0.5);
    const adjustedPayout = basePayout * (1 - totalHouseEdge) * (1 - difficultyData.payoutReduction);
    
    // Calculate winnings for each active bet
    let totalWinnings = 0;
    letItRideGame.bets.forEach(bet => {
        if (bet > 0) {
            totalWinnings += Math.floor(bet * adjustedPayout);
        }
    });
    
    letItRideGame.totalWin = totalWinnings;

    // Process winnings through wallet integration
    if (totalWinnings > 0 && walletIntegration) {
        await walletIntegration.processWin(totalWinnings, {
            game_action: 'game_complete',
            hand_result: handResult,
            active_bets: letItRideGame.bets.filter(bet => bet > 0),
            win_amount: totalWinnings,
            player_cards: letItRideGame.playerCards,
            community_cards: letItRideGame.communityCards
        });
        balance = walletIntegration.balance;
    }
}

function updateGameStats(handResult) {
    letItRideGame.stats.handsPlayed++;
    
    if (letItRideGame.totalWin > 0) {
        letItRideGame.stats.handsWon++;
        letItRideGame.stats.totalWon += letItRideGame.totalWin;
        
        if (letItRideGame.totalWin > letItRideGame.stats.biggestWin) {
            letItRideGame.stats.biggestWin = letItRideGame.totalWin;
        }
        
        // Update win streak
        letItRideGame.streakData.currentWinStreak++;
        letItRideGame.streakData.currentLossStreak = 0;
        
        if (letItRideGame.streakData.currentWinStreak > letItRideGame.streakData.longestWinStreak) {
            letItRideGame.streakData.longestWinStreak = letItRideGame.streakData.currentWinStreak;
        }
        
        // Track specific hand types
        if (handResult.name === 'Pair of 10s or Better') letItRideGame.stats.pairTensWins++;
        if (handResult.name === 'Straight') letItRideGame.stats.straightWins++;
        if (handResult.name === 'Flush') letItRideGame.stats.flushWins++;
    } else {
        // Update loss streak
        letItRideGame.streakData.currentLossStreak++;
        letItRideGame.streakData.currentWinStreak = 0;
        
        if (letItRideGame.streakData.currentLossStreak > letItRideGame.streakData.longestLossStreak) {
            letItRideGame.streakData.longestLossStreak = letItRideGame.streakData.currentLossStreak;
        }
    }
}

function updateLetItRideDisplay() {
    // Update bet amounts
    document.getElementById('bet1Amount').textContent = letItRideGame.bets[0];
    document.getElementById('bet2Amount').textContent = letItRideGame.bets[1];
    document.getElementById('bet3Amount').textContent = letItRideGame.bets[2];
    
    document.getElementById('circle1Amount').textContent = `${letItRideGame.bets[0]} GA`;
    document.getElementById('circle2Amount').textContent = `${letItRideGame.bets[1]} GA`;
    document.getElementById('circle3Amount').textContent = `${letItRideGame.bets[2]} GA`;
    
    // Update hand result
    if (letItRideGame.handResult) {
        const resultEl = document.getElementById('handResult');
        if (letItRideGame.totalWin > 0) {
            resultEl.innerHTML = `${letItRideGame.handResult.name} - Won ${letItRideGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono';
        } else {
            resultEl.innerHTML = `${letItRideGame.handResult.name} - No payout`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    document.getElementById('letItRideHandsPlayed').textContent = letItRideGame.stats.handsPlayed;
    
    const winRate = letItRideGame.stats.handsPlayed > 0 ? 
        ((letItRideGame.stats.handsWon / letItRideGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('letItRideWinRate').textContent = `${winRate}%`;
    
    document.getElementById('letItRideTotalWagered').textContent = letItRideGame.stats.totalWagered.toLocaleString();
    document.getElementById('letItRideTotalWon').textContent = letItRideGame.stats.totalWon.toLocaleString();
    document.getElementById('letItRideBiggestWin').textContent = letItRideGame.stats.biggestWin.toLocaleString();
    document.getElementById('letItRideLossStreak').textContent = letItRideGame.streakData.currentLossStreak;
}

function updateGameStatus(message) {
    document.getElementById('gameStatus').textContent = message;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize wallet integration first
    await initializeWallet();

    // Load the game
    loadLetItRideGame();
});