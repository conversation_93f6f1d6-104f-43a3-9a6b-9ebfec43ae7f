// Roulette VR: Immersive Spin - Virtual Reality roulette with <7% win rate
let rouletteVRGame = {
    currentNumber: null,
    lastNumbers: [],
    bets: [],
    totalBet: 0,
    isSpinning: false,
    vrMode: 'standard', // standard, enhanced, premium, ultimate
    immersionLevel: 1, // 1-20 (higher = more VR interference)
    vrHeadset: 'basic', // basic, advanced, pro, quantum
    trackingSystem: 'optical', // optical, magnetic, neural, quantum
    hapticFeedback: false,
    motionSickness: false,
    vrGlitch: false,
    neuralSync: false,
    quantumEntanglement: false,
    ballPhysics: 'realistic',
    wheelCalibration: 100,
    vrLatency: 0,
    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        vrGlitches: 0,
        motionSicknessEvents: 0,
        neuralInterference: 0,
        quantumFluctuations: 0,
        calibrationDrift: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    vrHistory: [],
    glitchedNumbers: new Set()
};

// VR modes with extreme interference
const VR_MODES = {
    standard: {
        name: 'Standard VR',
        houseEdge: 0.41, // 41% house edge
        glitchChance: 0.25,
        motionSicknessChance: 0.18,
        payoutMultiplier: 0.65,
        interferenceStrength: 0.28
    },
    enhanced: {
        name: 'Enhanced Reality',
        houseEdge: 0.46, // 46% house edge
        glitchChance: 0.32,
        motionSicknessChance: 0.24,
        payoutMultiplier: 0.58,
        interferenceStrength: 0.35
    },
    premium: {
        name: 'Premium VR',
        houseEdge: 0.51, // 51% house edge
        glitchChance: 0.39,
        motionSicknessChance: 0.30,
        payoutMultiplier: 0.51,
        interferenceStrength: 0.42
    },
    ultimate: {
        name: 'Ultimate Reality',
        houseEdge: 0.56, // 56% house edge
        glitchChance: 0.46,
        motionSicknessChance: 0.36,
        payoutMultiplier: 0.44,
        interferenceStrength: 0.49
    }
};

const VR_HEADSETS = {
    basic: {
        name: 'Basic VR',
        trackingAccuracy: 0.75,
        latencyMultiplier: 1.0,
        glitchResistance: 0.6,
        biasBonus: 0.0
    },
    advanced: {
        name: 'Advanced VR',
        trackingAccuracy: 0.68,
        latencyMultiplier: 1.2,
        glitchResistance: 0.5,
        biasBonus: 0.12
    },
    pro: {
        name: 'Pro VR',
        trackingAccuracy: 0.61,
        latencyMultiplier: 1.4,
        glitchResistance: 0.4,
        biasBonus: 0.18
    },
    quantum: {
        name: 'Quantum VR',
        trackingAccuracy: 0.54,
        latencyMultiplier: 1.6,
        glitchResistance: 0.3,
        biasBonus: 0.25
    }
};

const TRACKING_SYSTEMS = {
    optical: {
        name: 'Optical Tracking',
        precision: 0.72,
        interferenceChance: 0.22,
        driftRate: 0.15
    },
    magnetic: {
        name: 'Magnetic Tracking',
        precision: 0.65,
        interferenceChance: 0.28,
        driftRate: 0.22
    },
    neural: {
        name: 'Neural Interface',
        precision: 0.58,
        interferenceChance: 0.34,
        driftRate: 0.29
    },
    quantum: {
        name: 'Quantum Tracking',
        precision: 0.51,
        interferenceChance: 0.40,
        driftRate: 0.36
    }
};

// Roulette numbers and colors
const ROULETTE_NUMBERS = {
    0: 'green', 37: 'green',
    1: 'red', 3: 'red', 5: 'red', 7: 'red', 9: 'red', 12: 'red', 14: 'red', 16: 'red',
    18: 'red', 19: 'red', 21: 'red', 23: 'red', 25: 'red', 27: 'red', 30: 'red',
    32: 'red', 34: 'red', 36: 'red',
    2: 'black', 4: 'black', 6: 'black', 8: 'black', 10: 'black', 11: 'black',
    13: 'black', 15: 'black', 17: 'black', 20: 'black', 22: 'black', 24: 'black',
    26: 'black', 28: 'black', 29: 'black', 31: 'black', 33: 'black', 35: 'black'
};

// VR-reduced payouts
const VR_PAYOUTS = {
    straight: 26.8,    // Reduced from 35:1
    split: 13.1,       // Reduced from 17:1
    street: 8.4,       // Reduced from 11:1
    corner: 6.2,       // Reduced from 8:1
    line: 3.8,         // Reduced from 5:1
    dozen: 1.9,        // Reduced from 2:1
    column: 1.9,       // Reduced from 2:1
    red: 0.78,         // Reduced from 1:1
    black: 0.78,       // Reduced from 1:1
    odd: 0.78,         // Reduced from 1:1
    even: 0.78,        // Reduced from 1:1
    low: 0.78,         // Reduced from 1:1
    high: 0.78         // Reduced from 1:1
};

// VR-biased number generation with interference
function generateVRBiasedNumber() {
    const modeData = VR_MODES[rouletteVRGame.vrMode];
    const headsetData = VR_HEADSETS[rouletteVRGame.vrHeadset];
    const trackingData = TRACKING_SYSTEMS[rouletteVRGame.trackingSystem];
    
    const immersionBias = rouletteVRGame.immersionLevel * 0.08;
    const totalInterference = modeData.interferenceStrength * headsetData.biasBonus + immersionBias;
    
    // Create VR-interference biased pool
    const numberPool = [];
    
    for (let num = 0; num <= 36; num++) {
        let weight = 1;
        
        // Check if players bet on this number
        const playerBetsOnNumber = rouletteVRGame.bets.some(bet => 
            (bet.type === 'straight' && bet.number === num) ||
            (bet.type === 'red' && ROULETTE_NUMBERS[num] === 'red') ||
            (bet.type === 'black' && ROULETTE_NUMBERS[num] === 'black') ||
            (bet.type === 'odd' && num % 2 === 1 && num !== 0) ||
            (bet.type === 'even' && num % 2 === 0 && num !== 0) ||
            (bet.type === 'low' && num >= 1 && num <= 18) ||
            (bet.type === 'high' && num >= 19 && num <= 36) ||
            (bet.type === 'dozen1' && num >= 1 && num <= 12) ||
            (bet.type === 'dozen2' && num >= 13 && num <= 24) ||
            (bet.type === 'dozen3' && num >= 25 && num <= 36)
        );
        
        if (playerBetsOnNumber) {
            // VR interference reduces weight for player bets
            weight *= (1 - totalInterference * 1.9);
        } else {
            // VR enhancement increases weight for non-player bets
            weight *= (1 + totalInterference * 0.7);
        }
        
        // VR glitch bias toward house numbers
        if (num === 0 || num === 37) {
            weight *= (1 + totalInterference * 2.4);
        }
        
        // Apply VR glitch effects
        if (rouletteVRGame.vrGlitch) {
            if (playerBetsOnNumber) {
                weight *= 0.12; // Extreme VR glitch interference
            } else {
                weight *= 1.95; // VR glitch favors house
            }
        }
        
        // Neural sync interference
        if (rouletteVRGame.neuralSync) {
            if (playerBetsOnNumber) {
                weight *= 0.08; // Neural interference blocks player wins
            }
        }
        
        // Quantum entanglement effects
        if (rouletteVRGame.quantumEntanglement) {
            if (playerBetsOnNumber) {
                weight *= 0.05; // Quantum uncertainty favors house
            } else {
                weight *= 2.1;
            }
        }
        
        // Tracking system precision affects bias
        weight *= trackingData.precision;
        
        // Add to pool based on weight
        const copies = Math.max(1, Math.floor(weight * 100));
        for (let i = 0; i < copies; i++) {
            numberPool.push(num);
        }
    }
    
    // Shuffle and select
    for (let i = numberPool.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numberPool[i], numberPool[j]] = [numberPool[j], numberPool[i]];
    }
    
    return numberPool[Math.floor(Math.random() * numberPool.length)];
}

function generateVREffects() {
    const modeData = VR_MODES[rouletteVRGame.vrMode];
    const headsetData = VR_HEADSETS[rouletteVRGame.vrHeadset];
    const trackingData = TRACKING_SYSTEMS[rouletteVRGame.trackingSystem];
    
    // VR glitch (very common, disrupts player experience)
    if (Math.random() < modeData.glitchChance * (1 - headsetData.glitchResistance)) {
        rouletteVRGame.vrGlitch = true;
        rouletteVRGame.stats.vrGlitches++;
    }
    
    // Motion sickness (common, affects player decisions)
    if (Math.random() < modeData.motionSicknessChance + (rouletteVRGame.immersionLevel * 0.02)) {
        rouletteVRGame.motionSickness = true;
        rouletteVRGame.stats.motionSicknessEvents++;
    }
    
    // Neural sync interference (advanced headsets)
    if (rouletteVRGame.vrHeadset === 'neural' || rouletteVRGame.vrHeadset === 'quantum') {
        if (Math.random() < 0.28 + (rouletteVRGame.immersionLevel * 0.03)) {
            rouletteVRGame.neuralSync = true;
            rouletteVRGame.stats.neuralInterference++;
        }
    }
    
    // Quantum entanglement (quantum headset only)
    if (rouletteVRGame.vrHeadset === 'quantum') {
        if (Math.random() < 0.35 + (rouletteVRGame.immersionLevel * 0.04)) {
            rouletteVRGame.quantumEntanglement = true;
            rouletteVRGame.stats.quantumFluctuations++;
        }
    }
    
    // Tracking system interference
    if (Math.random() < trackingData.interferenceChance) {
        rouletteVRGame.wheelCalibration -= trackingData.driftRate * 10;
        rouletteVRGame.stats.calibrationDrift++;
    }
    
    // Increase immersion level occasionally
    if (rouletteVRGame.stats.spinsPlayed > 0 && 
        rouletteVRGame.stats.spinsPlayed % 7 === 0 && 
        rouletteVRGame.immersionLevel < 20) {
        rouletteVRGame.immersionLevel++;
        updateVRStatus(`VR Immersion Level increased to ${rouletteVRGame.immersionLevel}!`);
    }
}

function placeBet(type, number = null, amount = null) {
    if (rouletteVRGame.isSpinning) return;
    
    const betAmount = amount || parseInt(document.getElementById('betAmount').value);
    
    if (betAmount < 5 || betAmount > 10000) {
        updateVRStatus('Bet must be between 5 and 10000 GA');
        return;
    }
    
    if (betAmount > balance) {
        updateVRStatus('Insufficient balance!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Add bet
    rouletteVRGame.bets.push({ type, number, amount });
    rouletteVRGame.totalBet += betAmount;
    rouletteVRGame.stats.totalWagered += betAmount;
    
    updateVRDisplay();
    updateVRStatus(`${betAmount} GA bet placed on ${type}${number ? ' ' + number : ''}`);
}

function clearBets() {
    if (rouletteVRGame.isSpinning) return;
    
    // Refund bets
    balance += rouletteVRGame.totalBet;
    updateBalance();
    
    // Clear bets
    rouletteVRGame.bets = [];
    rouletteVRGame.totalBet = 0;
    
    updateVRDisplay();
    updateVRStatus('All bets cleared');
}

function spinVRWheel() {
    if (rouletteVRGame.isSpinning || rouletteVRGame.bets.length === 0) return;
    
    rouletteVRGame.isSpinning = true;
    
    // Reset VR effects
    rouletteVRGame.vrGlitch = false;
    rouletteVRGame.motionSickness = false;
    rouletteVRGame.neuralSync = false;
    rouletteVRGame.quantumEntanglement = false;
    
    // Generate VR effects
    generateVREffects();
    
    updateVRStatus('Initializing VR spin sequence...');
    
    // VR spin animation with interference
    let spinDuration = 3500 + Math.random() * 2500;
    let elapsed = 0;
    const spinInterval = 60;
    
    const vrSpinAnimation = setInterval(() => {
        elapsed += spinInterval;
        
        // Apply VR effects during spin
        if (rouletteVRGame.vrGlitch) {
            updateVRStatus('VR GLITCH DETECTED - Reality distortion in progress!');
        }
        
        if (rouletteVRGame.motionSickness) {
            updateVRStatus('Motion sickness warning - Recalibrating headset...');
        }
        
        if (rouletteVRGame.neuralSync) {
            updateVRStatus('Neural interface interference - Thought patterns disrupted!');
        }
        
        if (rouletteVRGame.quantumEntanglement) {
            updateVRStatus('Quantum entanglement detected - Reality flux active!');
        }
        
        if (elapsed >= spinDuration) {
            clearInterval(vrSpinAnimation);
            finishVRSpin();
        }
    }, spinInterval);
}

function finishVRSpin() {
    // Generate VR-biased result
    rouletteVRGame.currentNumber = generateVRBiasedNumber();
    
    // Add to history
    rouletteVRGame.lastNumbers.unshift(rouletteVRGame.currentNumber);
    if (rouletteVRGame.lastNumbers.length > 10) {
        rouletteVRGame.lastNumbers.pop();
    }
    
    rouletteVRGame.vrHistory.push({
        number: rouletteVRGame.currentNumber,
        bets: [...rouletteVRGame.bets],
        effects: {
            glitch: rouletteVRGame.vrGlitch,
            motionSickness: rouletteVRGame.motionSickness,
            neuralSync: rouletteVRGame.neuralSync,
            quantumEntanglement: rouletteVRGame.quantumEntanglement
        }
    });
    
    // Calculate results
    calculateVRResults();
    
    rouletteVRGame.isSpinning = false;
    updateVRDisplay();
    
    // Clear bets for next spin
    setTimeout(() => {
        rouletteVRGame.bets = [];
        rouletteVRGame.totalBet = 0;
        updateVRDisplay();
    }, 3000);
}

function calculateVRResults() {
    let totalWin = 0;
    let winningBets = 0;
    const winningNumber = rouletteVRGame.currentNumber;
    const winningColor = ROULETTE_NUMBERS[winningNumber];
    
    const modeData = VR_MODES[rouletteVRGame.vrMode];
    const headsetData = VR_HEADSETS[rouletteVRGame.vrHeadset];
    
    rouletteVRGame.bets.forEach(bet => {
        let isWinning = false;
        let payout = 0;
        
        // Check if bet wins
        switch (bet.type) {
            case 'straight':
                isWinning = bet.number === winningNumber;
                payout = VR_PAYOUTS.straight;
                break;
            case 'red':
                isWinning = winningColor === 'red';
                payout = VR_PAYOUTS.red;
                break;
            case 'black':
                isWinning = winningColor === 'black';
                payout = VR_PAYOUTS.black;
                break;
            case 'odd':
                isWinning = winningNumber % 2 === 1 && winningNumber !== 0;
                payout = VR_PAYOUTS.odd;
                break;
            case 'even':
                isWinning = winningNumber % 2 === 0 && winningNumber !== 0;
                payout = VR_PAYOUTS.even;
                break;
            case 'low':
                isWinning = winningNumber >= 1 && winningNumber <= 18;
                payout = VR_PAYOUTS.low;
                break;
            case 'high':
                isWinning = winningNumber >= 19 && winningNumber <= 36;
                payout = VR_PAYOUTS.high;
                break;
            case 'dozen1':
                isWinning = winningNumber >= 1 && winningNumber <= 12;
                payout = VR_PAYOUTS.dozen;
                break;
            case 'dozen2':
                isWinning = winningNumber >= 13 && winningNumber <= 24;
                payout = VR_PAYOUTS.dozen;
                break;
            case 'dozen3':
                isWinning = winningNumber >= 25 && winningNumber <= 36;
                payout = VR_PAYOUTS.dozen;
                break;
        }
        
        if (isWinning) {
            winningBets++;
            
            // Apply VR mode multiplier (reduces payouts)
            payout *= modeData.payoutMultiplier;
            
            // Apply VR glitch penalty
            if (rouletteVRGame.vrGlitch) {
                payout *= 0.42; // 58% VR glitch penalty
            }
            
            // Apply motion sickness penalty
            if (rouletteVRGame.motionSickness) {
                payout *= 0.68; // 32% motion sickness penalty
            }
            
            // Apply neural sync penalty
            if (rouletteVRGame.neuralSync) {
                payout *= 0.35; // 65% neural interference penalty
            }
            
            // Apply quantum entanglement penalty
            if (rouletteVRGame.quantumEntanglement) {
                payout *= 0.28; // 72% quantum penalty
            }
            
            // Apply immersion level penalty
            payout *= (1 - (rouletteVRGame.immersionLevel * 0.04));
            
            // Apply headset tracking penalty
            payout *= headsetData.trackingAccuracy;
            
            // Apply house edge
            payout *= (1 - modeData.houseEdge * 0.5);
            
            // Calculate winnings
            let winnings = bet.amount * payout;
            
            // Minimum win of 1 GA
            winnings = Math.floor(Math.max(1, winnings));
            
            totalWin += bet.amount + winnings;
            
            if (bet.type === 'straight') {
                rouletteVRGame.stats.straightHits++;
            }
        }
    });
    
    // Add winnings to balance
    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        
        rouletteVRGame.stats.totalWon += (totalWin - rouletteVRGame.totalBet);
        
        if (totalWin > rouletteVRGame.stats.biggestWin) {
            rouletteVRGame.stats.biggestWin = totalWin;
        }
    }
    
    // Update game stats
    updateVRGameStats(winningBets > 0, totalWin);
    
    // Update status
    if (winningBets > 0) {
        const netWin = totalWin - rouletteVRGame.totalBet;
        updateVRStatus(`VR Result: ${winningNumber} (${winningColor}) - Won ${netWin} GA!`);
    } else {
        updateVRStatus(`VR Result: ${winningNumber} (${winningColor}) - House wins ${rouletteVRGame.totalBet} GA`);
    }
}

function updateVRGameStats(won, totalWin) {
    rouletteVRGame.stats.spinsPlayed++;
    
    if (won) {
        rouletteVRGame.stats.spinsWon++;
        
        // Update win streak
        rouletteVRGame.streakData.currentWinStreak++;
        rouletteVRGame.streakData.currentLossStreak = 0;
        
        if (rouletteVRGame.streakData.currentWinStreak > rouletteVRGame.streakData.longestWinStreak) {
            rouletteVRGame.streakData.longestWinStreak = rouletteVRGame.streakData.currentWinStreak;
        }
    } else {
        // Update loss streak
        rouletteVRGame.streakData.currentLossStreak++;
        rouletteVRGame.streakData.currentWinStreak = 0;
        
        if (rouletteVRGame.streakData.currentLossStreak > rouletteVRGame.streakData.longestLossStreak) {
            rouletteVRGame.streakData.longestLossStreak = rouletteVRGame.streakData.currentLossStreak;
        }
    }
}

function updateVRDisplay() {
    // Update current number
    if (rouletteVRGame.currentNumber !== null) {
        const color = ROULETTE_NUMBERS[rouletteVRGame.currentNumber];
        document.getElementById('currentNumber').innerHTML = `
            <div class="text-6xl font-bold ${color === 'red' ? 'text-red-400' : color === 'black' ? 'text-gray-300' : 'text-green-400'} neon-glow">
                ${rouletteVRGame.currentNumber}
            </div>
            <div class="text-xl text-${color === 'red' ? 'red' : color === 'black' ? 'gray' : 'green'}-400">
                ${color.toUpperCase()}
            </div>
        `;
    }
    
    // Update last numbers
    const lastNumbersEl = document.getElementById('lastNumbers');
    lastNumbersEl.innerHTML = rouletteVRGame.lastNumbers.map(num => {
        const color = ROULETTE_NUMBERS[num];
        return `<span class="inline-block w-8 h-8 rounded-full text-center leading-8 text-sm font-bold ${
            color === 'red' ? 'bg-red-500 text-white' : 
            color === 'black' ? 'bg-gray-800 text-white' : 
            'bg-green-500 text-white'
        }">${num}</span>`;
    }).join(' ');
    
    // Update total bet
    document.getElementById('totalBet').textContent = rouletteVRGame.totalBet;
    
    // Update active bets
    const activeBetsEl = document.getElementById('activeBets');
    activeBetsEl.innerHTML = rouletteVRGame.bets.map(bet => 
        `<div class="text-sm">${bet.amount} GA on ${bet.type}${bet.number ? ' ' + bet.number : ''}</div>`
    ).join('');
    
    // Update VR effects
    document.getElementById('immersionLevel').textContent = rouletteVRGame.immersionLevel;
    document.getElementById('vrGlitch').style.display = rouletteVRGame.vrGlitch ? 'block' : 'none';
    document.getElementById('motionSickness').style.display = rouletteVRGame.motionSickness ? 'block' : 'none';
    document.getElementById('neuralSync').style.display = rouletteVRGame.neuralSync ? 'block' : 'none';
    document.getElementById('quantumEntanglement').style.display = rouletteVRGame.quantumEntanglement ? 'block' : 'none';
    
    // Update statistics
    updateVRStats();
}

function updateVRStats() {
    document.getElementById('spinsPlayed').textContent = rouletteVRGame.stats.spinsPlayed;
    
    const winRate = rouletteVRGame.stats.spinsPlayed > 0 ? 
        ((rouletteVRGame.stats.spinsWon / rouletteVRGame.stats.spinsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('winRate').textContent = `${winRate}%`;
    
    document.getElementById('totalWagered').textContent = rouletteVRGame.stats.totalWagered.toLocaleString();
    document.getElementById('totalWon').textContent = rouletteVRGame.stats.totalWon.toLocaleString();
    document.getElementById('biggestWin').textContent = rouletteVRGame.stats.biggestWin.toLocaleString();
    document.getElementById('lossStreak').textContent = rouletteVRGame.streakData.currentLossStreak;
    document.getElementById('vrGlitches').textContent = rouletteVRGame.stats.vrGlitches;
    document.getElementById('motionSicknessEvents').textContent = rouletteVRGame.stats.motionSicknessEvents;
    document.getElementById('neuralInterference').textContent = rouletteVRGame.stats.neuralInterference;
    document.getElementById('quantumFluctuations').textContent = rouletteVRGame.stats.quantumFluctuations;
}

function updateVRStatus(message) {
    document.getElementById('vrStatus').textContent = message;
}

function changeVRMode() {
    const mode = document.getElementById('vrMode').value;
    rouletteVRGame.vrMode = mode;
    
    const modeData = VR_MODES[mode];
    document.getElementById('vrModeInfo').innerHTML = `
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Glitch: ${(modeData.glitchChance * 100).toFixed(0)}% | 
        Motion Sickness: ${(modeData.motionSicknessChance * 100).toFixed(0)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}%
    `;
}

function changeVRHeadset() {
    const headset = document.getElementById('vrHeadset').value;
    rouletteVRGame.vrHeadset = headset;
    
    const headsetData = VR_HEADSETS[headset];
    document.getElementById('vrHeadsetInfo').innerHTML = `
        Tracking: ${(headsetData.trackingAccuracy * 100).toFixed(0)}% | 
        Latency: ${headsetData.latencyMultiplier}x | 
        Glitch Resistance: ${(headsetData.glitchResistance * 100).toFixed(0)}% | 
        Bias: +${(headsetData.biasBonus * 100).toFixed(0)}%
    `;
}

function changeTrackingSystem() {
    const tracking = document.getElementById('trackingSystem').value;
    rouletteVRGame.trackingSystem = tracking;
    
    const trackingData = TRACKING_SYSTEMS[tracking];
    document.getElementById('trackingSystemInfo').innerHTML = `
        Precision: ${(trackingData.precision * 100).toFixed(0)}% | 
        Interference: ${(trackingData.interferenceChance * 100).toFixed(0)}% | 
        Drift Rate: ${(trackingData.driftRate * 100).toFixed(0)}%
    `;
}

// Initialize VR game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeVRMode();
    changeVRHeadset();
    changeTrackingSystem();
    
    // Add event listeners
    document.getElementById('spinWheel').addEventListener('click', spinVRWheel);
    document.getElementById('clearBets').addEventListener('click', clearBets);
    document.getElementById('vrMode').addEventListener('change', changeVRMode);
    document.getElementById('vrHeadset').addEventListener('change', changeVRHeadset);
    document.getElementById('trackingSystem').addEventListener('change', changeTrackingSystem);
    
    // Betting buttons
    document.getElementById('betRed').addEventListener('click', () => placeBet('red'));
    document.getElementById('betBlack').addEventListener('click', () => placeBet('black'));
    document.getElementById('betOdd').addEventListener('click', () => placeBet('odd'));
    document.getElementById('betEven').addEventListener('click', () => placeBet('even'));
    document.getElementById('betLow').addEventListener('click', () => placeBet('low'));
    document.getElementById('betHigh').addEventListener('click', () => placeBet('high'));
    document.getElementById('betDozen1').addEventListener('click', () => placeBet('dozen1'));
    document.getElementById('betDozen2').addEventListener('click', () => placeBet('dozen2'));
    document.getElementById('betDozen3').addEventListener('click', () => placeBet('dozen3'));
    
    // Straight number bets (0-36)
    for (let i = 0; i <= 36; i++) {
        const btn = document.getElementById(`bet${i}`);
        if (btn) {
            btn.addEventListener('click', () => placeBet('straight', i));
        }
    }
});