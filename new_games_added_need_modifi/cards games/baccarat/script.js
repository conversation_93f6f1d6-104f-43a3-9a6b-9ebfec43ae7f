// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-yellow-400 neon-glow">${balance} GA</span>`;
}

        function loadBaccaratGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Betting Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <h4 class="text-xl font-bold mb-4 text-yellow-400">GOLDENAURA BACCARAT</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="baccaratBet" value="10" min="1" max="${balance}"
                                       class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm mb-3 text-gray-300">PLACE YOUR BET</label>
                                <div class="space-y-2">
                                    <button id="betPlayer" class="bet-option w-full py-3 rounded-lg font-bold bg-blue-600/20 border-2 border-blue-500 text-blue-400 hover:bg-blue-600/40 transition-all">
                                        PLAYER (1:1)
                                    </button>
                                    <button id="betBanker" class="bet-option w-full py-3 rounded-lg font-bold bg-red-600/20 border-2 border-red-500 text-red-400 hover:bg-red-600/40 transition-all">
                                        BANKER (0.95:1)
                                    </button>
                                    <button id="betTie" class="bet-option w-full py-3 rounded-lg font-bold bg-purple-600/20 border-2 border-purple-500 text-purple-400 hover:bg-purple-600/40 transition-all">
                                        TIE (8:1)
                                    </button>
                                </div>
                            </div>
                            
                            <button id="dealBaccarat" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4" disabled>
                                DEAL CARDS
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                                <div id="baccaratCurrentBet" class="text-xl font-bold text-yellow-400">None</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="baccaratPotentialWin" class="text-xl font-bold text-yellow-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Game History -->
                        <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-yellow-400">RECENT RESULTS</h5>
                            <div id="baccaratHistory" class="flex flex-wrap gap-2">
                                <!-- Recent results will appear here -->
                            </div>
                        </div>
                        
                        <!-- Card Values -->
                        <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-yellow-400">CARD VALUES</h5>
                            <div class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">A:</span>
                                    <span class="text-yellow-400">1</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">2-9:</span>
                                    <span class="text-yellow-400">Face Value</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">10,J,Q,K:</span>
                                    <span class="text-yellow-400">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Baccarat Table -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <div id="baccaratTable" class="relative bg-gradient-to-br from-yellow-900 to-amber-900 rounded-lg p-6 h-96">
                                <!-- Player Area -->
                                <div class="absolute top-4 left-4">
                                    <div class="text-center">
                                        <div class="text-sm text-blue-400 mb-2">PLAYER</div>
                                        <div id="playerCards" class="flex space-x-2 mb-2">
                                            <!-- Player cards will appear here -->
                                        </div>
                                        <div id="playerTotal" class="text-2xl font-bold text-blue-400">-</div>
                                    </div>
                                </div>
                                
                                <!-- Banker Area -->
                                <div class="absolute top-4 right-4">
                                    <div class="text-center">
                                        <div class="text-sm text-red-400 mb-2">BANKER</div>
                                        <div id="bankerCards" class="flex space-x-2 mb-2">
                                            <!-- Banker cards will appear here -->
                                        </div>
                                        <div id="bankerTotal" class="text-2xl font-bold text-red-400">-</div>
                                    </div>
                                </div>
                                
                                <!-- Center Info -->
                                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                                    <div class="text-6xl mb-4">🃏</div>
                                    <div id="gamePhase" class="text-xl font-bold text-yellow-400">Place Your Bet</div>
                                </div>
                                
                                <!-- Betting Areas -->
                                <div class="absolute bottom-4 left-4 right-4">
                                    <div class="grid grid-cols-3 gap-4 text-center text-sm">
                                        <div class="bet-area py-2 px-4 rounded">
                                            <span class="text-blue-400 font-bold">PLAYER</span><br>
                                            <span class="text-yellow-400">1:1</span>
                                        </div>
                                        <div class="bet-area py-2 px-4 rounded">
                                            <span class="text-purple-400 font-bold">TIE</span><br>
                                            <span class="text-yellow-400">8:1</span>
                                        </div>
                                        <div class="bet-area py-2 px-4 rounded">
                                            <span class="text-red-400 font-bold">BANKER</span><br>
                                            <span class="text-yellow-400">0.95:1</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="baccaratStatus" class="text-center mt-4 text-lg font-semibold">Select your bet and deal the cards</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeBaccarat();
        }
        
        let baccaratGame = {
            isPlaying: false,
            currentBet: null,
            betAmount: 0,
            deck: [],
            playerCards: [],
            bankerCards: [],
            playerTotal: 0,
            bankerTotal: 0
        };
        
        function initializeBaccarat() {
            document.getElementById('betPlayer').addEventListener('click', () => placeBaccaratBet('player'));
            document.getElementById('betBanker').addEventListener('click', () => placeBaccaratBet('banker'));
            document.getElementById('betTie').addEventListener('click', () => placeBaccaratBet('tie'));
            document.getElementById('dealBaccarat').addEventListener('click', dealBaccaratCards);
            
            initializeDeck();
        }
        
        function initializeDeck() {
            const suits = ['♠', '♥', '♦', '♣'];
            const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
            
            baccaratGame.deck = [];
            for (const suit of suits) {
                for (const rank of ranks) {
                    baccaratGame.deck.push({ rank, suit });
                }
            }
            
            // Shuffle deck
            for (let i = baccaratGame.deck.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [baccaratGame.deck[i], baccaratGame.deck[j]] = [baccaratGame.deck[j], baccaratGame.deck[i]];
            }
        }
        
        function placeBaccaratBet(betType) {
            if (baccaratGame.isPlaying) return;
            
            const betAmount = parseInt(document.getElementById('baccaratBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Reset previous bet selection
            document.querySelectorAll('.bet-option').forEach(btn => {
                btn.classList.remove('ring-2', 'ring-green-400');
            });
            
            // Highlight selected bet
            let selectedButton;
            switch(betType) {
                case 'player':
                    selectedButton = document.getElementById('betPlayer');
                    break;
                case 'banker':
                    selectedButton = document.getElementById('betBanker');
                    break;
                case 'tie':
                    selectedButton = document.getElementById('betTie');
                    break;
            }

            selectedButton.classList.add('ring-2', 'ring-yellow-400');
            
            // Set bet
            baccaratGame.currentBet = betType;
            baccaratGame.betAmount = betAmount;
            
            // Update UI
            document.getElementById('baccaratCurrentBet').textContent = 
                betType.charAt(0).toUpperCase() + betType.slice(1) + ' - $' + betAmount;
            
            // Calculate potential win
            let potentialWin;
            switch(betType) {
                case 'player':
                    potentialWin = betAmount * 2;
                    break;
                case 'banker':
                    potentialWin = Math.floor(betAmount * 1.95);
                    break;
                case 'tie':
                    potentialWin = betAmount * 9;
                    break;
            }
            document.getElementById('baccaratPotentialWin').textContent = '$' + potentialWin;
            
            document.getElementById('dealBaccarat').disabled = false;
            document.getElementById('baccaratStatus').textContent = 'Bet placed! Deal the cards to begin';
        }
        
        function dealBaccaratCards() {
            if (!baccaratGame.currentBet) return;
            
            // Deduct bet
            balance -= baccaratGame.betAmount;
            updateBalance();
            
            baccaratGame.isPlaying = true;
            document.getElementById('dealBaccarat').disabled = true;
            document.querySelectorAll('.bet-option').forEach(btn => btn.disabled = true);
            
            // Clear previous cards
            baccaratGame.playerCards = [];
            baccaratGame.bankerCards = [];
            document.getElementById('playerCards').innerHTML = '';
            document.getElementById('bankerCards').innerHTML = '';
            document.getElementById('playerTotal').textContent = '-';
            document.getElementById('bankerTotal').textContent = '-';
            
            document.getElementById('gamePhase').textContent = 'Dealing Cards...';
            document.getElementById('baccaratStatus').textContent = 'Dealing initial cards...';
            
            // Deal initial cards with animation
            setTimeout(() => dealCard('player'), 500);
            setTimeout(() => dealCard('banker'), 1000);
            setTimeout(() => dealCard('player'), 1500);
            setTimeout(() => dealCard('banker'), 2000);
            
            // Check if third cards needed
            setTimeout(() => checkThirdCards(), 2500);
        }
        
        function dealCard(recipient) {
            if (baccaratGame.deck.length === 0) {
                initializeDeck(); // Reshuffle if deck empty
            }
            
            const card = baccaratGame.deck.pop();
            
            if (recipient === 'player') {
                baccaratGame.playerCards.push(card);
                displayCard(card, 'playerCards');
                baccaratGame.playerTotal = calculateBaccaratTotal(baccaratGame.playerCards);
                document.getElementById('playerTotal').textContent = baccaratGame.playerTotal;
            } else {
                baccaratGame.bankerCards.push(card);
                displayCard(card, 'bankerCards');
                baccaratGame.bankerTotal = calculateBaccaratTotal(baccaratGame.bankerCards);
                document.getElementById('bankerTotal').textContent = baccaratGame.bankerTotal;
            }
        }
        
        function displayCard(card, containerId) {
            const cardElement = document.createElement('div');
            cardElement.className = 'w-12 h-16 baccarat-card rounded flex flex-col items-center justify-center text-black text-xs font-bold';

            const isRed = card.suit === '♥' || card.suit === '♦';
            cardElement.style.color = isRed ? '#dc2626' : '#000';

            cardElement.innerHTML = `
                <div>${card.rank}</div>
                <div>${card.suit}</div>
            `;

            document.getElementById(containerId).appendChild(cardElement);
        }
        
        function calculateBaccaratTotal(cards) {
            let total = 0;
            
            for (const card of cards) {
                let value = 0;
                if (card.rank === 'A') {
                    value = 1;
                } else if (['J', 'Q', 'K'].includes(card.rank)) {
                    value = 0;
                } else {
                    value = parseInt(card.rank) || 0;
                }
                total += value;
            }
            
            return total % 10; // Only the last digit counts
        }
        
        function checkThirdCards() {
            document.getElementById('gamePhase').textContent = 'Checking Rules...';
            
            // Natural win check (8 or 9)
            if (baccaratGame.playerTotal >= 8 || baccaratGame.bankerTotal >= 8) {
                setTimeout(() => determineBaccaratWinner(), 1000);
                return;
            }
            
            let playerThirdCard = null;
            
            // Player third card rule
            if (baccaratGame.playerTotal <= 5) {
                setTimeout(() => {
                    dealCard('player');
                    playerThirdCard = baccaratGame.playerCards[2];
                    document.getElementById('baccaratStatus').textContent = 'Player draws third card...';
                    
                    // Check banker third card after player
                    setTimeout(() => checkBankerThirdCard(playerThirdCard), 1000);
                }, 1000);
            } else {
                // Check banker third card immediately
                setTimeout(() => checkBankerThirdCard(null), 1000);
            }
        }
        
        function checkBankerThirdCard(playerThirdCard) {
            const bankerShouldDraw = shouldBankerDraw(baccaratGame.bankerTotal, playerThirdCard);
            
            if (bankerShouldDraw) {
                setTimeout(() => {
                    dealCard('banker');
                    document.getElementById('baccaratStatus').textContent = 'Banker draws third card...';
                    setTimeout(() => determineBaccaratWinner(), 1000);
                }, 500);
            } else {
                setTimeout(() => determineBaccaratWinner(), 1000);
            }
        }
        
        function shouldBankerDraw(bankerTotal, playerThirdCard) {
            if (bankerTotal >= 7) return false;
            if (bankerTotal <= 2) return true;
            
            if (!playerThirdCard) {
                return bankerTotal <= 5;
            }
            
            const thirdCardValue = calculateBaccaratTotal([playerThirdCard]);
            
            switch (bankerTotal) {
                case 3: return thirdCardValue !== 8;
                case 4: return [2, 3, 4, 5, 6, 7].includes(thirdCardValue);
                case 5: return [4, 5, 6, 7].includes(thirdCardValue);
                case 6: return [6, 7].includes(thirdCardValue);
                default: return false;
            }
        }
        
        function determineBaccaratWinner() {
            document.getElementById('gamePhase').textContent = 'Final Results';
            
            let winner;
            if (baccaratGame.playerTotal > baccaratGame.bankerTotal) {
                winner = 'player';
            } else if (baccaratGame.bankerTotal > baccaratGame.playerTotal) {
                winner = 'banker';
            } else {
                winner = 'tie';
            }
            
            // Calculate winnings
            let winnings = 0;
            let won = false;
            
            if (baccaratGame.currentBet === winner) {
                won = true;
                switch (winner) {
                    case 'player':
                        winnings = baccaratGame.betAmount * 2;
                        break;
                    case 'banker':
                        winnings = Math.floor(baccaratGame.betAmount * 1.95);
                        break;
                    case 'tie':
                        winnings = baccaratGame.betAmount * 9;
                        break;
                }
            }
            
            if (winnings > 0) {
                balance += winnings;
                updateBalance();
            }
            
            // Add to history
            addBaccaratHistory(winner);
            
            // Update status
            const winnerText = winner.charAt(0).toUpperCase() + winner.slice(1);
            if (won) {
                document.getElementById('baccaratStatus').innerHTML =
                    `<span class="text-yellow-400 neon-glow">${winnerText} wins! You won ${winnings} GA</span>`;
            } else {
                document.getElementById('baccaratStatus').innerHTML =
                    `<span class="text-red-400">${winnerText} wins! You lost $${baccaratGame.betAmount}</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                resetBaccaratGame();
            }, 3000);
        }
        
        function addBaccaratHistory(winner) {
            const history = document.getElementById('baccaratHistory');
            const result = document.createElement('div');
            
            let color = 'bg-blue-600';
            if (winner === 'banker') color = 'bg-red-600';
            if (winner === 'tie') color = 'bg-purple-600';
            
            result.className = `w-8 h-8 ${color} rounded-full flex items-center justify-center text-white text-xs font-bold`;
            result.textContent = winner.charAt(0).toUpperCase();
            
            history.insertBefore(result, history.firstChild);
            
            // Keep only last 12 results
            while (history.children.length > 12) {
                history.removeChild(history.lastChild);
            }
        }
        
        function resetBaccaratGame() {
            baccaratGame.isPlaying = false;
            baccaratGame.currentBet = null;
            baccaratGame.betAmount = 0;
            
            // Reset UI
            document.getElementById('dealBaccarat').disabled = true;
            document.querySelectorAll('.bet-option').forEach(btn => {
                btn.disabled = false;
                btn.classList.remove('ring-2', 'ring-yellow-400');
            });
            
            document.getElementById('baccaratCurrentBet').textContent = 'None';
            document.getElementById('baccaratPotentialWin').textContent = '0 GA';
            document.getElementById('gamePhase').textContent = 'Place Your Bet';
            document.getElementById('baccaratStatus').textContent = 'Select your bet and deal the cards';
        }
        
        // Initialize
        updateBalance();

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBaccaratGame();
});