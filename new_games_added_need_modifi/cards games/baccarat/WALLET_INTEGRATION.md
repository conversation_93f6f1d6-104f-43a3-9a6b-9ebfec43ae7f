# Baccarat Wallet Integration

This document explains the real-time wallet integration implemented for the Baccarat game.

## Features Implemented

### 1. Real-Time Balance Loading
- Balance is loaded from the backend API on game initialization
- Automatic refresh every 30 seconds to keep balance current
- Loading states and error handling for network issues

### 2. Transaction Recording
- All bets are recorded as transactions in the database
- Wins are recorded as separate win transactions
- Proper balance validation before allowing bets

### 3. Enhanced User Experience
- Real-time balance updates in both header and game area
- Input validation to prevent invalid bet amounts
- User-friendly notifications for errors and wins
- Automatic bet input limits based on current balance

### 4. Security Features
- Authentication token validation for all API calls
- Server-side balance validation
- Transaction integrity with proper error handling

## API Integration

### Endpoints Used
- `GET /api/wallet` - Fetch current wallet balance
- `POST /api/games/transaction` - Record game transactions (bets/wins)

### Transaction Types
- `bet` - Records when player places a bet (deducts from balance)
- `win` - Records when player wins (adds to balance)

## Technical Implementation

### Balance Management
```javascript
// Load balance from API
async function loadWalletBalance() {
    const walletBalance = await walletAPI.getWalletBalance();
    balance = parseFloat(walletBalance) || 0;
    updateBalance();
}

// Record transactions
async function recordGameTransaction(type, amount, metadata) {
    await walletAPI.recordGameTransaction({
        type: type,
        amount: amount,
        game: 'baccarat',
        metadata: metadata
    });
}
```

### Error Handling
- Network errors are handled gracefully
- Failed transactions restore previous balance state
- User notifications for all error conditions
- Game continues to function even if transaction recording fails

### Real-Time Updates
- Balance refreshes every 30 seconds automatically
- Immediate updates after each game action
- Synchronization with backend state

## Database Schema

### Transactions Table
- `type`: 'bet' or 'win'
- `amount`: Transaction amount in GA currency
- `game`: 'baccarat'
- `metadata`: JSON containing game-specific data
  - `bet_type`: 'player', 'banker', or 'tie'
  - `result`: Game outcome
  - `cards`: Card details for audit trail

## Usage Instructions

1. **Authentication Required**: Players must be logged in with valid token
2. **Minimum Balance**: Players need sufficient balance to place bets
3. **Real-Time Sync**: Balance updates automatically during gameplay
4. **Transaction History**: All game actions are recorded for audit

## Error Scenarios Handled

1. **Insufficient Balance**: Prevents bet placement with clear message
2. **Network Errors**: Graceful degradation with user notification
3. **Authentication Failure**: Redirects to login or shows auth error
4. **Transaction Failures**: Restores game state and notifies user

## Future Enhancements

1. **Offline Mode**: Cache balance for temporary offline play
2. **Transaction History**: In-game view of recent transactions
3. **Betting Limits**: Configurable min/max bet amounts
4. **Responsible Gaming**: Integration with spending limits

## Testing

To test the integration:

1. Ensure backend API is running
2. Have a valid user account with GA balance
3. Open the Baccarat game
4. Verify balance loads correctly
5. Place bets and verify transactions are recorded
6. Check balance updates in real-time

## Dependencies

- `api-service.js` - Main API service for backend communication
- Backend Laravel API with Sanctum authentication
- MySQL database with transactions table
- Valid authentication token in localStorage
