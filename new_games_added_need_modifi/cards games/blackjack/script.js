// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('blackjack');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        walletIntegration.updateBalanceDisplay();
    }
}

        function loadBlackjackGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <h4 class="text-xl font-bold mb-4 text-yellow-400">CYBER BLACKJACK</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="blackjackBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <button id="dealBlackjack" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                DEAL CARDS
                            </button>
                            
                            <div class="space-y-2">
                                <button id="hitBlackjack" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white" disabled>
                                    HIT
                                </button>
                                <button id="standBlackjack" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white" disabled>
                                    STAND
                                </button>
                                <button id="doubleBlackjack" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white" disabled>
                                    DOUBLE DOWN
                                </button>
                            </div>
                            
                            <div class="mt-4 text-center">
                                <div class="text-sm text-gray-400 mb-1">Game Status</div>
                                <div id="blackjackStatus" class="text-lg font-bold text-yellow-400">Place your bet</div>
                            </div>
                        </div>
                        
                        <!-- Game Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-yellow-400">CARD VALUES</h5>
                            <div class="text-sm space-y-1 text-gray-300">
                                <div>A = 1 or 11</div>
                                <div>K, Q, J = 10</div>
                                <div>2-10 = Face value</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Game Board -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <!-- Dealer's Hand -->
                            <div class="mb-8">
                                <h5 class="text-lg font-bold mb-3 text-red-400">AI DEALER</h5>
                                <div class="flex items-center mb-2">
                                    <span class="text-sm text-gray-400 mr-4">Total:</span>
                                    <span id="dealerTotal" class="text-xl font-bold text-red-400">-</span>
                                </div>
                                <div id="dealerCards" class="flex space-x-2 min-h-[100px]">
                                    <!-- Dealer's cards will appear here -->
                                </div>
                            </div>
                            
                            <!-- Player's Hand -->
                            <div>
                                <h5 class="text-lg font-bold mb-3 text-green-400">PLAYER</h5>
                                <div class="flex items-center mb-2">
                                    <span class="text-sm text-gray-400 mr-4">Total:</span>
                                    <span id="playerTotal" class="text-xl font-bold text-green-400">-</span>
                                </div>
                                <div id="playerCards" class="flex space-x-2 min-h-[100px]">
                                    <!-- Player's cards will appear here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeBlackjack();
        }
        
        let blackjackGame = {
            deck: [],
            playerCards: [],
            dealerCards: [],
            gameActive: false,
            betAmount: 0,
            dealerHidden: true
        };
        
        function initializeBlackjack() {
            document.getElementById('dealBlackjack').addEventListener('click', dealBlackjackCards);
            document.getElementById('hitBlackjack').addEventListener('click', hitBlackjack);
            document.getElementById('standBlackjack').addEventListener('click', standBlackjack);
            document.getElementById('doubleBlackjack').addEventListener('click', doubleDownBlackjack);
        }
        
        function createDeck() {
            const suits = ['♠', '♥', '♦', '♣'];
            const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
            const deck = [];
            
            for (let suit of suits) {
                for (let rank of ranks) {
                    deck.push({ suit, rank });
                }
            }
            
            // Shuffle deck
            for (let i = deck.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [deck[i], deck[j]] = [deck[j], deck[i]];
            }
            
            return deck;
        }
        
        function getCardValue(card) {
            if (card.rank === 'A') return 11;
            if (['K', 'Q', 'J'].includes(card.rank)) return 10;
            return parseInt(card.rank);
        }
        
        function calculateHandValue(cards) {
            let value = 0;
            let aces = 0;
            
            for (let card of cards) {
                if (card.rank === 'A') {
                    aces++;
                    value += 11;
                } else if (['K', 'Q', 'J'].includes(card.rank)) {
                    value += 10;
                } else {
                    value += parseInt(card.rank);
                }
            }
            
            // Handle aces
            while (value > 21 && aces > 0) {
                value -= 10;
                aces--;
            }
            
            return value;
        }
        
        function renderCard(card, hidden = false) {
            const cardElement = document.createElement('div');
            cardElement.className = 'w-16 h-24 bg-gradient-to-br from-gray-100 to-white border-2 border-gray-300 rounded-lg flex flex-col items-center justify-center text-black font-bold shadow-lg';
            
            if (hidden) {
                cardElement.className = 'w-16 h-24 bg-gradient-to-br from-purple-600 to-blue-600 border-2 border-purple-400 rounded-lg flex flex-col items-center justify-center text-white font-bold shadow-lg neon-border';
                cardElement.innerHTML = '<div class="text-2xl">?</div>';
            } else {
                const color = ['♥', '♦'].includes(card.suit) ? 'text-red-500' : 'text-black';
                cardElement.innerHTML = `
                    <div class="text-xs">${card.rank}</div>
                    <div class="text-lg ${color}">${card.suit}</div>
                    <div class="text-xs rotate-180">${card.rank}</div>
                `;
            }
            
            return cardElement;
        }
        
        async function dealBlackjackCards() {
            const betAmount = parseInt(document.getElementById('blackjackBet').value);

            // Use wallet integration to process bet
            if (!walletIntegration || !(await walletIntegration.processBet(betAmount, {
                game_action: 'deal',
                bet_amount: betAmount
            }))) {
                return;
            }

            // Initialize game
            blackjackGame.deck = createDeck();
            blackjackGame.playerCards = [];
            blackjackGame.dealerCards = [];
            blackjackGame.gameActive = true;
            blackjackGame.betAmount = betAmount;
            blackjackGame.dealerHidden = true;

            // Update local balance reference
            balance = walletIntegration.balance;
            
            // Deal initial cards
            blackjackGame.playerCards.push(blackjackGame.deck.pop());
            blackjackGame.dealerCards.push(blackjackGame.deck.pop());
            blackjackGame.playerCards.push(blackjackGame.deck.pop());
            blackjackGame.dealerCards.push(blackjackGame.deck.pop());
            
            renderBlackjackHands();
            
            // Check for blackjack
            const playerValue = calculateHandValue(blackjackGame.playerCards);
            if (playerValue === 21) {
                // Player blackjack
                blackjackGame.dealerHidden = false;
                renderBlackjackHands();
                
                const dealerValue = calculateHandValue(blackjackGame.dealerCards);
                if (dealerValue === 21) {
                    endBlackjackGame('push');
                } else {
                    endBlackjackGame('blackjack');
                }
            } else {
                // Enable player actions
                document.getElementById('hitBlackjack').disabled = false;
                document.getElementById('standBlackjack').disabled = false;
                document.getElementById('doubleBlackjack').disabled = false;
                document.getElementById('dealBlackjack').disabled = true;
                document.getElementById('blackjackStatus').textContent = 'Make your move';
            }
        }
        
        function renderBlackjackHands() {
            // Clear previous cards
            document.getElementById('dealerCards').innerHTML = '';
            document.getElementById('playerCards').innerHTML = '';
            
            // Render dealer cards
            blackjackGame.dealerCards.forEach((card, index) => {
                const hidden = blackjackGame.dealerHidden && index === 1;
                const cardElement = renderCard(card, hidden);
                document.getElementById('dealerCards').appendChild(cardElement);
            });
            
            // Render player cards
            blackjackGame.playerCards.forEach(card => {
                const cardElement = renderCard(card);
                document.getElementById('playerCards').appendChild(cardElement);
            });
            
            // Update totals
            const playerTotal = calculateHandValue(blackjackGame.playerCards);
            document.getElementById('playerTotal').textContent = playerTotal;
            
            if (blackjackGame.dealerHidden) {
                document.getElementById('dealerTotal').textContent = '?';
            } else {
                const dealerTotal = calculateHandValue(blackjackGame.dealerCards);
                document.getElementById('dealerTotal').textContent = dealerTotal;
            }
        }
        
        function hitBlackjack() {
            if (!blackjackGame.gameActive) return;
            
            blackjackGame.playerCards.push(blackjackGame.deck.pop());
            renderBlackjackHands();
            
            const playerValue = calculateHandValue(blackjackGame.playerCards);
            if (playerValue > 21) {
                endBlackjackGame('bust');
            } else if (playerValue === 21) {
                standBlackjack();
            }
            
            // Disable double down after first hit
            document.getElementById('doubleBlackjack').disabled = true;
        }
        
        function standBlackjack() {
            if (!blackjackGame.gameActive) return;
            
            // Reveal dealer's hidden card
            blackjackGame.dealerHidden = false;
            renderBlackjackHands();
            
            // Dealer must hit on 16 and below, stand on 17 and above
            let dealerValue = calculateHandValue(blackjackGame.dealerCards);
            
            function dealerDraw() {
                if (dealerValue < 17) {
                    setTimeout(() => {
                        blackjackGame.dealerCards.push(blackjackGame.deck.pop());
                        renderBlackjackHands();
                        dealerValue = calculateHandValue(blackjackGame.dealerCards);
                        
                        if (dealerValue > 21) {
                            endBlackjackGame('dealer_bust');
                        } else {
                            dealerDraw();
                        }
                    }, 1000);
                } else {
                    // Compare hands
                    const playerValue = calculateHandValue(blackjackGame.playerCards);
                    
                    if (playerValue > dealerValue) {
                        endBlackjackGame('win');
                    } else if (playerValue < dealerValue) {
                        endBlackjackGame('lose');
                    } else {
                        endBlackjackGame('push');
                    }
                }
            }
            
            dealerDraw();
            
            // Disable player actions
            document.getElementById('hitBlackjack').disabled = true;
            document.getElementById('standBlackjack').disabled = true;
            document.getElementById('doubleBlackjack').disabled = true;
        }
        
        async function doubleDownBlackjack() {
            if (!blackjackGame.gameActive) return;

            // Use wallet integration to process additional bet
            if (!walletIntegration || !(await walletIntegration.processBet(blackjackGame.betAmount, {
                game_action: 'double_down',
                original_bet: blackjackGame.betAmount
            }))) {
                return;
            }

            // Double the bet amount
            blackjackGame.betAmount *= 2;
            balance = walletIntegration.balance;
            
            // Hit once and stand
            blackjackGame.playerCards.push(blackjackGame.deck.pop());
            renderBlackjackHands();
            
            const playerValue = calculateHandValue(blackjackGame.playerCards);
            if (playerValue > 21) {
                endBlackjackGame('bust');
            } else {
                standBlackjack();
            }
        }
        
        function endBlackjackGame(result) {
            blackjackGame.gameActive = false;
            
            let winnings = 0;
            let message = '';
            
            switch (result) {
                case 'blackjack':
                    winnings = Math.floor(blackjackGame.betAmount * 2.5);
                    message = '<span class="text-yellow-400 neon-glow">BLACKJACK!</span>';
                    break;
                case 'win':
                case 'dealer_bust':
                    winnings = blackjackGame.betAmount * 2;
                    message = '<span class="text-green-400 neon-glow">YOU WIN!</span>';
                    break;
                case 'push':
                    winnings = blackjackGame.betAmount;
                    message = '<span class="text-yellow-400">PUSH!</span>';
                    break;
                case 'lose':
                case 'bust':
                    winnings = 0;
                    message = '<span class="text-red-400">YOU LOSE!</span>';
                    break;
            }
            
            // Process winnings through wallet integration
            if (winnings > 0 && walletIntegration) {
                await walletIntegration.processWin(winnings, {
                    game_action: 'game_end',
                    result: result,
                    bet_amount: blackjackGame.betAmount,
                    win_amount: winnings
                });
                balance = walletIntegration.balance;
            }

            document.getElementById('blackjackStatus').innerHTML = message;
            
            // Reset buttons
            setTimeout(() => {
                document.getElementById('dealBlackjack').disabled = false;
                document.getElementById('hitBlackjack').disabled = true;
                document.getElementById('standBlackjack').disabled = true;
                document.getElementById('doubleBlackjack').disabled = true;
                document.getElementById('blackjackStatus').textContent = 'Place your bet';
            }, 3000);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize wallet integration first
    await initializeWallet();

    // Load the game
    loadBlackjackGame();
});