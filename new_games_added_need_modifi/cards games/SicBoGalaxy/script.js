// Sic Bo Galaxy - Cosmic dice game with extremely low win rate (<8%)
let sicBoGalaxyGame = {
    dice: [1, 1, 1],
    bets: {},
    totalBet: 0,
    isRolling: false,
    galaxyMode: 'nebula', // nebula, pulsar, quasar, blackhole
    cosmicLevel: 1, // 1-20 (higher = more house advantage)
    stellarMultiplier: 1.0,
    difficulty: 'cosmic', // cosmic, stellar, galactic, universal
    quantumField: false,
    darkMatter: false,
    stellarWind: false,
    cosmicRadiation: false,
    wormholeActive: false,
    stats: {
        rollsPlayed: 0,
        rollsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        tripleHits: 0,
        cosmicEvents: 0,
        quantumFluctuations: 0,
        darkMatterEvents: 0,
        wormholeJumps: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    rollHistory: []
};

// Galaxy modes with extreme house bias
const GALAXY_MODES = {
    nebula: {
        name: 'Nebula Drift',
        houseEdge: 0.45, // 45% house edge
        cosmicChance: 0.02, // 2% chance for bonus
        payoutMultiplier: 0.58, // Severely reduced payouts
        quantumPenalty: 0.25 // 25% quantum penalty
    },
    pulsar: {
        name: 'Pulsar Storm',
        houseEdge: 0.50, // 50% house edge
        cosmicChance: 0.015, // 1.5% chance for bonus
        payoutMultiplier: 0.53,
        quantumPenalty: 0.30 // 30% quantum penalty
    },
    quasar: {
        name: 'Quasar Burst',
        houseEdge: 0.55, // 55% house edge
        cosmicChance: 0.01, // 1% chance for bonus
        payoutMultiplier: 0.48,
        quantumPenalty: 0.35 // 35% quantum penalty
    },
    blackhole: {
        name: 'Black Hole Void',
        houseEdge: 0.62, // 62% house edge
        cosmicChance: 0.005, // 0.5% chance for bonus
        payoutMultiplier: 0.42,
        quantumPenalty: 0.42 // 42% quantum penalty
    }
};

// Difficulty levels with extreme bias
const GALAXY_DIFFICULTIES = {
    cosmic: {
        name: 'Cosmic',
        diceBias: 0.35, // 35% bias against player
        payoutReduction: 0.22, // 22% payout reduction
        cosmicInterference: 0.28
    },
    stellar: {
        name: 'Stellar',
        diceBias: 0.42, // 42% bias against player
        payoutReduction: 0.28, // 28% payout reduction
        cosmicInterference: 0.35
    },
    galactic: {
        name: 'Galactic',
        diceBias: 0.49, // 49% bias against player
        payoutReduction: 0.34, // 34% payout reduction
        cosmicInterference: 0.42
    },
    universal: {
        name: 'Universal',
        diceBias: 0.56, // 56% bias against player
        payoutReduction: 0.40, // 40% payout reduction
        cosmicInterference: 0.49
    }
};

// Severely reduced payouts (much lower than standard Sic Bo)
const GALAXY_PAYOUTS = {
    // Small/Big bets (normally 1:1, now much lower)
    small: 0.65, // 65% of bet
    big: 0.65,
    
    // Specific totals (normally 6:1 to 60:1, now severely reduced)
    total4: 35, // Reduced from 60:1
    total5: 20, // Reduced from 30:1
    total6: 12, // Reduced from 18:1
    total7: 8,  // Reduced from 12:1
    total8: 6,  // Reduced from 8:1
    total9: 4,  // Reduced from 6:1
    total10: 4, // Reduced from 6:1
    total11: 4, // Reduced from 6:1
    total12: 4, // Reduced from 6:1
    total13: 6, // Reduced from 8:1
    total14: 8, // Reduced from 12:1
    total15: 12, // Reduced from 18:1
    total16: 20, // Reduced from 30:1
    total17: 35, // Reduced from 60:1
    
    // Single number bets (normally 1:1, 2:1, 3:1)
    single: 0.7, // Reduced from 1:1
    double: 1.2, // Reduced from 2:1
    triple: 2.0, // Reduced from 3:1
    
    // Specific triples (normally 180:1, now severely reduced)
    specificTriple: 85, // Reduced from 180:1
    
    // Any triple (normally 30:1, now severely reduced)
    anyTriple: 18, // Reduced from 30:1
    
    // Specific doubles (normally 10:1, now severely reduced)
    specificDouble: 6, // Reduced from 10:1
    
    // Two dice combinations (normally 5:1, now severely reduced)
    combination: 3.2 // Reduced from 5:1
};

// Cosmic events that affect gameplay
const COSMIC_EVENTS = [
    'Quantum fluctuation detected - dice probability altered',
    'Dark matter interference - payouts reduced',
    'Stellar wind affecting dice trajectory',
    'Cosmic radiation spike - bet values decreased',
    'Wormhole opened - dice results randomized',
    'Pulsar burst - all bets voided',
    'Black hole gravity - dice pulled toward low numbers',
    'Nebula cloud - visibility reduced, bets confused'
];

function rollGalaxyDice() {
    if (sicBoGalaxyGame.isRolling || sicBoGalaxyGame.totalBet === 0) return;
    
    sicBoGalaxyGame.isRolling = true;
    updateGalaxyStatus('Rolling cosmic dice...');
    
    // Trigger cosmic events
    triggerCosmicEvents();
    
    // Animate dice roll
    animateGalaxyDiceRoll();
    
    setTimeout(() => {
        // Generate biased dice results
        sicBoGalaxyGame.dice = generateBiasedGalaxyDice();
        
        // Calculate results
        const result = calculateGalaxyResult();
        
        // Update game state
        sicBoGalaxyGame.stats.rollsPlayed++;
        sicBoGalaxyGame.stats.totalWagered += sicBoGalaxyGame.totalBet;
        
        if (result.totalWin > 0) {
            sicBoGalaxyGame.stats.rollsWon++;
            sicBoGalaxyGame.stats.totalWon += result.totalWin;
            sicBoGalaxyGame.stats.biggestWin = Math.max(sicBoGalaxyGame.stats.biggestWin, result.totalWin);
            sicBoGalaxyGame.streakData.currentWinStreak++;
            sicBoGalaxyGame.streakData.currentLossStreak = 0;
            sicBoGalaxyGame.streakData.longestWinStreak = Math.max(
                sicBoGalaxyGame.streakData.longestWinStreak,
                sicBoGalaxyGame.streakData.currentWinStreak
            );
            balance += result.totalWin;
            updateGalaxyStatus(`Cosmic win! +${result.totalWin} GA`);
        } else {
            sicBoGalaxyGame.streakData.currentLossStreak++;
            sicBoGalaxyGame.streakData.currentWinStreak = 0;
            sicBoGalaxyGame.streakData.longestLossStreak = Math.max(
                sicBoGalaxyGame.streakData.longestLossStreak,
                sicBoGalaxyGame.streakData.currentLossStreak
            );
            updateGalaxyStatus('The cosmos claims your bet...');
        }
        
        // Add to history
        sicBoGalaxyGame.rollHistory.unshift({
            dice: [...sicBoGalaxyGame.dice],
            total: sicBoGalaxyGame.dice.reduce((a, b) => a + b, 0),
            bets: {...sicBoGalaxyGame.bets},
            win: result.totalWin,
            cosmicEvents: result.cosmicEvents
        });
        
        if (sicBoGalaxyGame.rollHistory.length > 20) {
            sicBoGalaxyGame.rollHistory.pop();
        }
        
        // Clear bets and update display
        sicBoGalaxyGame.bets = {};
        sicBoGalaxyGame.totalBet = 0;
        sicBoGalaxyGame.isRolling = false;
        
        updateGalaxyDisplay();
        updateBalance();
        
    }, 3000);
}

function generateBiasedGalaxyDice() {
    const modeData = GALAXY_MODES[sicBoGalaxyGame.galaxyMode];
    const difficultyData = GALAXY_DIFFICULTIES[sicBoGalaxyGame.difficulty];
    const bias = difficultyData.diceBias;
    
    const dice = [];
    
    for (let i = 0; i < 3; i++) {
        // Create heavily biased dice against player bets
        let diceValue;
        
        // Check if player has bets that would benefit from high/low values
        const hasSmallBets = sicBoGalaxyGame.bets.small > 0;
        const hasBigBets = sicBoGalaxyGame.bets.big > 0;
        const hasSpecificBets = Object.keys(sicBoGalaxyGame.bets).some(key => 
            key.startsWith('total') || key.startsWith('single') || key.startsWith('triple')
        );
        
        if (Math.random() < bias) {
            // Bias against player
            if (hasSmallBets && !hasBigBets) {
                // Player bet small, force high numbers
                diceValue = Math.random() < 0.7 ? (4 + Math.floor(Math.random() * 3)) : Math.floor(Math.random() * 6) + 1;
            } else if (hasBigBets && !hasSmallBets) {
                // Player bet big, force low numbers
                diceValue = Math.random() < 0.7 ? (1 + Math.floor(Math.random() * 3)) : Math.floor(Math.random() * 6) + 1;
            } else if (hasSpecificBets) {
                // Avoid specific numbers player bet on
                const avoidNumbers = getPlayerBetNumbers();
                do {
                    diceValue = Math.floor(Math.random() * 6) + 1;
                } while (avoidNumbers.includes(diceValue) && Math.random() < 0.6);
            } else {
                diceValue = Math.floor(Math.random() * 6) + 1;
            }
        } else {
            diceValue = Math.floor(Math.random() * 6) + 1;
        }
        
        dice.push(diceValue);
    }
    
    return dice;
}

function getPlayerBetNumbers() {
    const numbers = [];
    Object.keys(sicBoGalaxyGame.bets).forEach(betType => {
        if (betType.startsWith('single')) {
            const num = parseInt(betType.replace('single', ''));
            numbers.push(num);
        }
    });
    return numbers;
}

function triggerCosmicEvents() {
    const modeData = GALAXY_MODES[sicBoGalaxyGame.galaxyMode];
    
    // Reset cosmic effects
    sicBoGalaxyGame.quantumField = false;
    sicBoGalaxyGame.darkMatter = false;
    sicBoGalaxyGame.stellarWind = false;
    sicBoGalaxyGame.cosmicRadiation = false;
    sicBoGalaxyGame.wormholeActive = false;
    
    // Trigger cosmic events (mostly negative for player)
    if (Math.random() < 0.35) { // 35% chance of cosmic interference
        const eventType = Math.random();
        
        if (eventType < 0.25) {
            sicBoGalaxyGame.quantumField = true;
            sicBoGalaxyGame.stats.quantumFluctuations++;
            showCosmicEvent('Quantum field activated - dice probabilities altered!');
        } else if (eventType < 0.45) {
            sicBoGalaxyGame.darkMatter = true;
            sicBoGalaxyGame.stats.darkMatterEvents++;
            showCosmicEvent('Dark matter detected - payouts reduced by cosmic forces!');
        } else if (eventType < 0.65) {
            sicBoGalaxyGame.stellarWind = true;
            showCosmicEvent('Stellar wind interference - dice trajectory affected!');
        } else if (eventType < 0.85) {
            sicBoGalaxyGame.cosmicRadiation = true;
            showCosmicEvent('Cosmic radiation spike - bet values decreased!');
        } else {
            sicBoGalaxyGame.wormholeActive = true;
            sicBoGalaxyGame.stats.wormholeJumps++;
            showCosmicEvent('Wormhole opened - reality distorted!');
        }
        
        sicBoGalaxyGame.stats.cosmicEvents++;
    }
}

function calculateGalaxyResult() {
    const total = sicBoGalaxyGame.dice.reduce((a, b) => a + b, 0);
    const modeData = GALAXY_MODES[sicBoGalaxyGame.galaxyMode];
    const difficultyData = GALAXY_DIFFICULTIES[sicBoGalaxyGame.difficulty];
    
    let totalWin = 0;
    const cosmicEvents = [];
    
    // Check each bet type
    Object.keys(sicBoGalaxyGame.bets).forEach(betType => {
        const betAmount = sicBoGalaxyGame.bets[betType];
        let payout = 0;
        let won = false;
        
        // Determine if bet wins (with cosmic interference)
        if (betType === 'small' && total >= 4 && total <= 10) {
            payout = GALAXY_PAYOUTS.small;
            won = true;
        } else if (betType === 'big' && total >= 11 && total <= 17) {
            payout = GALAXY_PAYOUTS.big;
            won = true;
        } else if (betType.startsWith('total')) {
            const targetTotal = parseInt(betType.replace('total', ''));
            if (total === targetTotal) {
                payout = GALAXY_PAYOUTS[betType] || 2;
                won = true;
            }
        } else if (betType.startsWith('single')) {
            const targetNumber = parseInt(betType.replace('single', ''));
            const count = sicBoGalaxyGame.dice.filter(d => d === targetNumber).length;
            if (count > 0) {
                payout = count === 1 ? GALAXY_PAYOUTS.single : 
                        count === 2 ? GALAXY_PAYOUTS.double : GALAXY_PAYOUTS.triple;
                won = true;
            }
        } else if (betType === 'anyTriple') {
            if (sicBoGalaxyGame.dice[0] === sicBoGalaxyGame.dice[1] && 
                sicBoGalaxyGame.dice[1] === sicBoGalaxyGame.dice[2]) {
                payout = GALAXY_PAYOUTS.anyTriple;
                won = true;
                sicBoGalaxyGame.stats.tripleHits++;
            }
        } else if (betType.startsWith('specificTriple')) {
            const targetNumber = parseInt(betType.replace('specificTriple', ''));
            if (sicBoGalaxyGame.dice.every(d => d === targetNumber)) {
                payout = GALAXY_PAYOUTS.specificTriple;
                won = true;
                sicBoGalaxyGame.stats.tripleHits++;
            }
        }
        
        if (won) {
            // Apply cosmic penalties
            let finalPayout = payout;
            
            // Apply mode multiplier (reduces payouts)
            finalPayout *= modeData.payoutMultiplier;
            
            // Apply difficulty reduction
            finalPayout *= (1 - difficultyData.payoutReduction);
            
            // Apply cosmic event penalties
            if (sicBoGalaxyGame.quantumField) {
                finalPayout *= 0.7; // 30% quantum penalty
                cosmicEvents.push('Quantum field reduced payout');
            }
            
            if (sicBoGalaxyGame.darkMatter) {
                finalPayout *= 0.6; // 40% dark matter penalty
                cosmicEvents.push('Dark matter absorbed winnings');
            }
            
            if (sicBoGalaxyGame.stellarWind) {
                finalPayout *= 0.8; // 20% stellar wind penalty
                cosmicEvents.push('Stellar wind scattered coins');
            }
            
            if (sicBoGalaxyGame.cosmicRadiation) {
                finalPayout *= 0.75; // 25% radiation penalty
                cosmicEvents.push('Cosmic radiation degraded payout');
            }
            
            if (sicBoGalaxyGame.wormholeActive) {
                finalPayout *= 0.5; // 50% wormhole penalty
                cosmicEvents.push('Wormhole consumed half the winnings');
            }
            
            // Apply quantum penalty from mode
            finalPayout *= (1 - modeData.quantumPenalty);
            
            totalWin += Math.floor(betAmount * finalPayout);
        }
    });
    
    return { totalWin, cosmicEvents };
}

function placeSicBoBet(betType, amount) {
    if (sicBoGalaxyGame.isRolling || balance < amount) return;
    
    if (!sicBoGalaxyGame.bets[betType]) {
        sicBoGalaxyGame.bets[betType] = 0;
    }
    
    sicBoGalaxyGame.bets[betType] += amount;
    sicBoGalaxyGame.totalBet += amount;
    balance -= amount;
    
    updateGalaxyDisplay();
    updateBalance();
}

function clearSicBoBets() {
    if (sicBoGalaxyGame.isRolling) return;
    
    // Refund bets
    balance += sicBoGalaxyGame.totalBet;
    
    sicBoGalaxyGame.bets = {};
    sicBoGalaxyGame.totalBet = 0;
    
    updateGalaxyDisplay();
    updateBalance();
}

function changeGalaxyMode() {
    const mode = document.getElementById('galaxyMode').value;
    sicBoGalaxyGame.galaxyMode = mode;
    
    const modeData = GALAXY_MODES[mode];
    document.getElementById('modeInfo').innerHTML = `
        Mode: ${modeData.name} | 
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Cosmic Chance: ${(modeData.cosmicChance * 100).toFixed(1)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}%
    `;
}

function changeGalaxyDifficulty() {
    const difficulty = document.getElementById('galaxyDifficulty').value;
    sicBoGalaxyGame.difficulty = difficulty;
    
    const difficultyData = GALAXY_DIFFICULTIES[difficulty];
    document.getElementById('difficultyInfo').innerHTML = `
        Difficulty: ${difficultyData.name} | 
        Dice Bias: ${(difficultyData.diceBias * 100).toFixed(0)}% | 
        Payout Reduction: ${(difficultyData.payoutReduction * 100).toFixed(0)}%
    `;
}

function animateGalaxyDiceRoll() {
    const diceElements = [
        document.getElementById('dice1'),
        document.getElementById('dice2'),
        document.getElementById('dice3')
    ];
    
    let rollCount = 0;
    const rollInterval = setInterval(() => {
        diceElements.forEach(el => {
            el.textContent = Math.floor(Math.random() * 6) + 1;
            el.style.transform = `rotate(${Math.random() * 360}deg) scale(${0.8 + Math.random() * 0.4})`;
        });
        
        rollCount++;
        if (rollCount >= 20) {
            clearInterval(rollInterval);
            diceElements.forEach((el, i) => {
                el.textContent = sicBoGalaxyGame.dice[i];
                el.style.transform = 'rotate(0deg) scale(1)';
            });
        }
    }, 150);
}

function updateGalaxyDisplay() {
    // Update dice display
    document.getElementById('dice1').textContent = sicBoGalaxyGame.dice[0];
    document.getElementById('dice2').textContent = sicBoGalaxyGame.dice[1];
    document.getElementById('dice3').textContent = sicBoGalaxyGame.dice[2];
    
    const total = sicBoGalaxyGame.dice.reduce((a, b) => a + b, 0);
    document.getElementById('diceTotal').textContent = total;
    
    // Update bet display
    document.getElementById('totalBet').textContent = sicBoGalaxyGame.totalBet;
    
    const activeBetsEl = document.getElementById('activeBets');
    activeBetsEl.innerHTML = Object.keys(sicBoGalaxyGame.bets).map(betType => 
        `<div class="text-sm">${sicBoGalaxyGame.bets[betType]} GA on ${betType}</div>`
    ).join('');
    
    // Update cosmic status
    const cosmicEffects = [];
    if (sicBoGalaxyGame.quantumField) cosmicEffects.push('Quantum Field');
    if (sicBoGalaxyGame.darkMatter) cosmicEffects.push('Dark Matter');
    if (sicBoGalaxyGame.stellarWind) cosmicEffects.push('Stellar Wind');
    if (sicBoGalaxyGame.cosmicRadiation) cosmicEffects.push('Cosmic Radiation');
    if (sicBoGalaxyGame.wormholeActive) cosmicEffects.push('Wormhole');
    
    document.getElementById('cosmicEffects').textContent = cosmicEffects.join(', ') || 'None';
    
    updateGalaxyStats();
}

function updateGalaxyStats() {
    document.getElementById('rollsPlayed').textContent = sicBoGalaxyGame.stats.rollsPlayed;
    
    const winRate = sicBoGalaxyGame.stats.rollsPlayed > 0 ? 
        ((sicBoGalaxyGame.stats.rollsWon / sicBoGalaxyGame.stats.rollsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('winRate').textContent = `${winRate}%`;
    
    document.getElementById('totalWagered').textContent = sicBoGalaxyGame.stats.totalWagered.toLocaleString();
    document.getElementById('totalWon').textContent = sicBoGalaxyGame.stats.totalWon.toLocaleString();
    document.getElementById('biggestWin').textContent = sicBoGalaxyGame.stats.biggestWin.toLocaleString();
    document.getElementById('lossStreak').textContent = sicBoGalaxyGame.streakData.currentLossStreak;
    document.getElementById('tripleHits').textContent = sicBoGalaxyGame.stats.tripleHits;
    document.getElementById('cosmicEvents').textContent = sicBoGalaxyGame.stats.cosmicEvents;
    document.getElementById('quantumFluctuations').textContent = sicBoGalaxyGame.stats.quantumFluctuations;
    document.getElementById('darkMatterEvents').textContent = sicBoGalaxyGame.stats.darkMatterEvents;
    document.getElementById('wormholeJumps').textContent = sicBoGalaxyGame.stats.wormholeJumps;
}

function updateGalaxyStatus(message) {
    document.getElementById('galaxyStatus').textContent = message;
}

function showCosmicEvent(message) {
    document.getElementById('cosmicEvent').textContent = message;
    
    setTimeout(() => {
        document.getElementById('cosmicEvent').textContent = '';
    }, 5000);
}

// Initialize galaxy game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeGalaxyMode();
    changeGalaxyDifficulty();
    
    document.getElementById('rollDice').addEventListener('click', rollGalaxyDice);
    document.getElementById('clearBets').addEventListener('click', clearSicBoBets);
    document.getElementById('galaxyMode').addEventListener('change', changeGalaxyMode);
    document.getElementById('galaxyDifficulty').addEventListener('change', changeGalaxyDifficulty);
    
    // Betting buttons
    document.getElementById('betSmall').addEventListener('click', () => placeSicBoBet('small', 25));
    document.getElementById('betBig').addEventListener('click', () => placeSicBoBet('big', 25));
    document.getElementById('betAnyTriple').addEventListener('click', () => placeSicBoBet('anyTriple', 50));
    
    // Number betting buttons
    for (let i = 1; i <= 6; i++) {
        const btn = document.getElementById(`betSingle${i}`);
        if (btn) {
            btn.addEventListener('click', () => placeSicBoBet(`single${i}`, 20));
        }
        
        const tripleBtn = document.getElementById(`betTriple${i}`);
        if (tripleBtn) {
            tripleBtn.addEventListener('click', () => placeSicBoBet(`specificTriple${i}`, 100));
        }
    }
    
    // Total betting buttons
    for (let i = 4; i <= 17; i++) {
        const btn = document.getElementById(`betTotal${i}`);
        if (btn) {
            btn.addEventListener('click', () => placeSicBoBet(`total${i}`, 30));
        }
    }
    
    updateGalaxyStatus('Welcome to Sic Bo Galaxy - where the cosmos decides your fate!');
});