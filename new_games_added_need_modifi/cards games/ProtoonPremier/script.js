// Protoon Premier - Elite Pontoon variant with extremely low win rate (<5%)
let protoonPremierGame = {
    playerHand: [],
    dealerHand: [],
    playerScore: 0,
    dealerScore: 0,
    betAmount: 0,
    gamePhase: 'betting', // betting, playing, dealer, complete
    gameResult: null,
    totalWin: 0,
    difficulty: 'premier', // premier, elite, master, legend
    gameMode: 'classic', // classic, premier, ultimate
    premierLevel: 1, // 1-10 (higher = more house advantage)
    eliteBonus: false, // Rarely activated bonus
    masterPenalty: false, // Frequently activated penalty
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        handsPushed: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        protoons: 0, // Natural 21s
        fivecardtricks: 0,
        busts: 0,
        eliteBonuses: 0,
        masterPenalties: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game constants with extreme house bias
const PROTOON_MODES = {
    classic: { 
        name: 'Classic Premier', 
        houseEdge: 0.45, // 45% house edge
        eliteChance: 0.02, // 2% chance for bonus
        payoutMultiplier: 0.60, // Severely reduced payouts
        commission: 0.15 // 15% commission
    },
    premier: { 
        name: 'Premier Elite', 
        houseEdge: 0.50, // 50% house edge
        eliteChance: 0.01, // 1% chance for bonus
        payoutMultiplier: 0.55,
        commission: 0.20 // 20% commission
    },
    ultimate: { 
        name: 'Ultimate Master', 
        houseEdge: 0.55, // 55% house edge
        eliteChance: 0.005, // 0.5% chance for bonus
        payoutMultiplier: 0.50,
        commission: 0.25 // 25% commission
    }
};

const PROTOON_DIFFICULTIES = {
    premier: { 
        name: 'Premier League', 
        dealerAdvantage: 0.30,
        bustPenalty: 2.0,
        cardBias: 0.40,
        levelMultiplier: 1.0
    },
    elite: { 
        name: 'Elite Division', 
        dealerAdvantage: 0.40,
        bustPenalty: 2.5,
        cardBias: 0.55,
        levelMultiplier: 1.2
    },
    master: { 
        name: 'Master Class', 
        dealerAdvantage: 0.50,
        bustPenalty: 3.0,
        cardBias: 0.70,
        levelMultiplier: 1.5
    },
    legend: { 
        name: 'Legendary Tier', 
        dealerAdvantage: 0.60,
        bustPenalty: 4.0,
        cardBias: 0.85,
        levelMultiplier: 2.0
    }
};

const PROTOON_PAYOUTS = {
    PROTOON: 1.2, // Natural 21 (severely reduced from 2.5:1)
    FIVE_CARD_TRICK: 1.0, // 5 cards under 21 (reduced from 2:1)
    WIN: 0.6, // Regular win (less than 1:1)
    ELITE_BONUS: 0.05, // Tiny elite bonus
    PREMIER_MULTIPLIER: 1.05 // Minimal premier bonus
};

// Extremely biased deck creation - heavily favors dealer
function createProtoonDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const deck = [];
    
    const modeData = PROTOON_MODES[protoonPremierGame.gameMode];
    const difficultyData = PROTOON_DIFFICULTIES[protoonPremierGame.difficulty];
    const levelBias = protoonPremierGame.premierLevel * 0.1; // Increases with level
    
    suits.forEach(suit => {
        ranks.forEach(rank => {
            let cardWeight = 1;
            const value = getProtoonCardValue(rank);
            
            // Drastically reduce high cards for player
            if (value === 10 || rank === 'A') {
                cardWeight *= (1 - difficultyData.cardBias - levelBias);
            }
            
            // Increase bust-inducing cards
            if (value >= 6 && value <= 9) {
                cardWeight *= (1 + difficultyData.cardBias + levelBias);
            }
            
            // Further reduce based on premier level
            if (protoonPremierGame.premierLevel > 5) {
                if (value === 10 || rank === 'A') {
                    cardWeight *= 0.3; // Extreme reduction
                }
            }
            
            // Add cards based on weight
            const cardCount = Math.max(1, Math.floor(cardWeight * 6));
            for (let i = 0; i < cardCount; i++) {
                deck.push({ suit, rank, value });
            }
        });
    });
    
    return shuffleProtoonDeck(deck);
}

function getProtoonCardValue(rank) {
    if (rank === 'A') return 11; // Ace high initially
    if (['J', 'Q', 'K'].includes(rank)) return 10;
    return parseInt(rank);
}

function shuffleProtoonDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function dealProtoonCard(isDealer = false) {
    if (protoonPremierGame.deck.length === 0) {
        protoonPremierGame.deck = createProtoonDeck();
    }
    
    const difficultyData = PROTOON_DIFFICULTIES[protoonPremierGame.difficulty];
    const dealerAdvantage = difficultyData.dealerAdvantage + (protoonPremierGame.premierLevel * 0.05);
    
    // Apply extreme bias when dealing
    if (isDealer && Math.random() < dealerAdvantage) {
        // Give dealer premium cards
        const premiumCards = protoonPremierGame.deck.filter(card => 
            card.value === 10 || card.rank === 'A' || 
            (card.value >= 8 && card.value <= 10)
        );
        
        if (premiumCards.length > 0) {
            const cardIndex = protoonPremierGame.deck.indexOf(premiumCards[0]);
            return protoonPremierGame.deck.splice(cardIndex, 1)[0];
        }
    } else if (!isDealer && Math.random() < dealerAdvantage * 0.8) {
        // Give player bust-inducing cards
        const bustCards = protoonPremierGame.deck.filter(card => 
            card.value >= 6 && card.value <= 9
        );
        
        if (bustCards.length > 0) {
            const cardIndex = protoonPremierGame.deck.indexOf(bustCards[0]);
            return protoonPremierGame.deck.splice(cardIndex, 1)[0];
        }
    }
    
    return protoonPremierGame.deck.pop();
}

function calculateProtoonScore(hand) {
    let score = 0;
    let aces = 0;
    
    hand.forEach(card => {
        if (card.rank === 'A') {
            aces++;
            score += 11;
        } else {
            score += card.value;
        }
    });
    
    // Adjust for aces
    while (score > 21 && aces > 0) {
        score -= 10;
        aces--;
    }
    
    return score;
}

function isProtoon(hand) {
    return hand.length === 2 && calculateProtoonScore(hand) === 21;
}

function isFiveCardTrick(hand) {
    return hand.length === 5 && calculateProtoonScore(hand) <= 21;
}

function startProtoonGame() {
    const betAmount = parseInt(document.getElementById('protoonBet').value);
    
    if (betAmount > balance || betAmount <= 0) {
        updateProtoonStatus('Invalid bet amount!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    protoonPremierGame.playerHand = [];
    protoonPremierGame.dealerHand = [];
    protoonPremierGame.betAmount = betAmount;
    protoonPremierGame.gamePhase = 'playing';
    protoonPremierGame.gameResult = null;
    protoonPremierGame.totalWin = 0;
    protoonPremierGame.eliteBonus = false;
    protoonPremierGame.masterPenalty = false;
    
    protoonPremierGame.stats.totalWagered += betAmount;
    
    // Create extremely biased deck
    protoonPremierGame.deck = createProtoonDeck();
    
    // Deal initial cards with extreme bias
    protoonPremierGame.playerHand.push(dealProtoonCard(false));
    protoonPremierGame.dealerHand.push(dealProtoonCard(true));
    protoonPremierGame.playerHand.push(dealProtoonCard(false));
    protoonPremierGame.dealerHand.push(dealProtoonCard(true));
    
    // Calculate scores
    protoonPremierGame.playerScore = calculateProtoonScore(protoonPremierGame.playerHand);
    protoonPremierGame.dealerScore = calculateProtoonScore(protoonPremierGame.dealerHand);
    
    // Check for natural protoons (extremely rare for player)
    if (isProtoon(protoonPremierGame.playerHand)) {
        if (isProtoon(protoonPremierGame.dealerHand)) {
            // Both have protoon - house always wins
            protoonPremierGame.gameResult = 'loss';
            updateProtoonStatus('Both Protoons - House wins all ties!');
        } else {
            // Player protoon (ultra rare win) - but apply house edge
            const modeData = PROTOON_MODES[protoonPremierGame.gameMode];
            if (Math.random() < modeData.houseEdge) {
                protoonPremierGame.gameResult = 'loss';
                updateProtoonStatus('Protoon negated by house edge!');
            } else {
                protoonPremierGame.gameResult = 'protoon';
                calculateProtoonWinnings();
                updateProtoonStatus('PROTOON! Ultra rare natural 21!');
            }
        }
        protoonPremierGame.gamePhase = 'complete';
        updateProtoonGameStats();
    } else if (isProtoon(protoonPremierGame.dealerHand)) {
        // Dealer protoon (common)
        protoonPremierGame.gameResult = 'loss';
        updateProtoonStatus('Dealer Protoon - You lose!');
        protoonPremierGame.gamePhase = 'complete';
        updateProtoonGameStats();
    }
    
    // Generate premier effects
    generatePremierEffects();
    
    updateProtoonDisplay();
    
    // Enable/disable buttons
    document.getElementById('hitProtoon').disabled = protoonPremierGame.gamePhase !== 'playing';
    document.getElementById('stickProtoon').disabled = protoonPremierGame.gamePhase !== 'playing';
    document.getElementById('dealProtoon').disabled = protoonPremierGame.gamePhase === 'playing';
}

function generatePremierEffects() {
    const modeData = PROTOON_MODES[protoonPremierGame.gameMode];
    
    // Elite bonus (extremely rare)
    if (Math.random() < modeData.eliteChance) {
        protoonPremierGame.eliteBonus = true;
    }
    
    // Master penalty (very common)
    if (Math.random() < 0.25 + (protoonPremierGame.premierLevel * 0.05)) {
        protoonPremierGame.masterPenalty = true;
    }
    
    // Increase premier level occasionally (makes game harder)
    if (protoonPremierGame.stats.handsPlayed > 0 && 
        protoonPremierGame.stats.handsPlayed % 10 === 0 && 
        protoonPremierGame.premierLevel < 10) {
        protoonPremierGame.premierLevel++;
        updateProtoonStatus(`Premier Level increased to ${protoonPremierGame.premierLevel}!`);
    }
}

function hitProtoonCard() {
    if (protoonPremierGame.gamePhase !== 'playing') return;
    
    // Deal card with extreme bias against player
    const newCard = dealProtoonCard(false);
    protoonPremierGame.playerHand.push(newCard);
    protoonPremierGame.playerScore = calculateProtoonScore(protoonPremierGame.playerHand);
    
    // Check for bust with severe penalty
    if (protoonPremierGame.playerScore > 21) {
        const difficultyData = PROTOON_DIFFICULTIES[protoonPremierGame.difficulty];
        
        // Apply master penalty
        if (protoonPremierGame.masterPenalty) {
            protoonPremierGame.stats.masterPenalties++;
            updateProtoonStatus('MASTER PENALTY! Bust with extreme loss!');
        } else {
            updateProtoonStatus('BUST! Over 21!');
        }
        
        protoonPremierGame.gameResult = 'bust';
        protoonPremierGame.gamePhase = 'complete';
        protoonPremierGame.stats.busts++;
        updateProtoonGameStats();
    } else if (isFiveCardTrick(protoonPremierGame.playerHand)) {
        // Five card trick (ultra rare) - but apply house edge
        const modeData = PROTOON_MODES[protoonPremierGame.gameMode];
        if (Math.random() < modeData.houseEdge * 1.5) {
            protoonPremierGame.gameResult = 'loss';
            updateProtoonStatus('Five cards negated by house edge!');
        } else {
            protoonPremierGame.gameResult = 'fivecard';
            calculateProtoonWinnings();
            updateProtoonStatus('FIVE CARD TRICK! Ultra rare achievement!');
        }
        protoonPremierGame.gamePhase = 'complete';
        updateProtoonGameStats();
    }
    
    updateProtoonDisplay();
    
    // Disable hit if 5 cards or bust
    if (protoonPremierGame.playerHand.length >= 5 || protoonPremierGame.playerScore > 21) {
        document.getElementById('hitProtoon').disabled = true;
    }
}

function stickProtoonHand() {
    if (protoonPremierGame.gamePhase !== 'playing') return;
    
    protoonPremierGame.gamePhase = 'dealer';
    document.getElementById('hitProtoon').disabled = true;
    document.getElementById('stickProtoon').disabled = true;
    
    // Dealer plays with extreme advantage
    playProtoonDealerHand();
}

function playProtoonDealerHand() {
    const difficultyData = PROTOON_DIFFICULTIES[protoonPremierGame.difficulty];
    const dealerAdvantage = difficultyData.dealerAdvantage + (protoonPremierGame.premierLevel * 0.05);
    
    // Dealer hits on soft 17 and has extreme advantage
    while (protoonPremierGame.dealerScore < 17 || 
           (protoonPremierGame.dealerScore === 17 && hasProtoonAce(protoonPremierGame.dealerHand))) {
        
        // Dealer gets premium cards
        const newCard = dealProtoonCard(true);
        protoonPremierGame.dealerHand.push(newCard);
        protoonPremierGame.dealerScore = calculateProtoonScore(protoonPremierGame.dealerHand);
        
        // Dealer almost never busts due to extreme bias
        if (protoonPremierGame.dealerScore > 21 && Math.random() > dealerAdvantage * 1.5) {
            break;
        }
    }
    
    // Compare hands and determine result
    compareProtoonHands();
    protoonPremierGame.gamePhase = 'complete';
    updateProtoonGameStats();
    updateProtoonDisplay();
    
    document.getElementById('dealProtoon').disabled = false;
}

function hasProtoonAce(hand) {
    return hand.some(card => card.rank === 'A');
}

function compareProtoonHands() {
    const modeData = PROTOON_MODES[protoonPremierGame.gameMode];
    const houseEdge = modeData.houseEdge + (protoonPremierGame.premierLevel * 0.02);
    
    if (protoonPremierGame.dealerScore > 21) {
        // Dealer bust (extremely rare) - but apply massive house edge
        if (Math.random() < houseEdge * 1.8) {
            protoonPremierGame.gameResult = 'loss';
            updateProtoonStatus('Dealer bust negated by house edge!');
        } else {
            protoonPremierGame.gameResult = 'win';
            calculateProtoonWinnings();
            updateProtoonStatus('Dealer bust - Ultra rare win!');
        }
    } else if (protoonPremierGame.playerScore > protoonPremierGame.dealerScore) {
        // Player higher - but apply extreme house edge
        if (Math.random() < houseEdge * 2.0) {
            protoonPremierGame.gameResult = 'loss';
            updateProtoonStatus('Higher score negated by house edge!');
        } else {
            protoonPremierGame.gameResult = 'win';
            calculateProtoonWinnings();
            updateProtoonStatus('Higher score - Rare win!');
        }
    } else if (protoonPremierGame.playerScore === protoonPremierGame.dealerScore) {
        // Tie - house wins almost all ties
        if (Math.random() < 0.9) {
            protoonPremierGame.gameResult = 'loss';
            updateProtoonStatus('Tie - House wins!');
        } else {
            protoonPremierGame.gameResult = 'push';
            protoonPremierGame.totalWin = protoonPremierGame.betAmount;
            balance += protoonPremierGame.betAmount;
            updateBalance();
            updateProtoonStatus('Tie - Rare push!');
        }
    } else {
        // Dealer higher
        protoonPremierGame.gameResult = 'loss';
        updateProtoonStatus('Dealer wins with higher score!');
    }
}

function calculateProtoonWinnings() {
    const modeData = PROTOON_MODES[protoonPremierGame.gameMode];
    const difficultyData = PROTOON_DIFFICULTIES[protoonPremierGame.difficulty];
    
    let payout = 0;
    
    if (protoonPremierGame.gameResult === 'protoon') {
        payout = PROTOON_PAYOUTS.PROTOON;
        protoonPremierGame.stats.protoons++;
    } else if (protoonPremierGame.gameResult === 'fivecard') {
        payout = PROTOON_PAYOUTS.FIVE_CARD_TRICK;
        protoonPremierGame.stats.fivecardtricks++;
    } else {
        payout = PROTOON_PAYOUTS.WIN;
    }
    
    // Apply mode multiplier (severely reduces payouts)
    payout *= modeData.payoutMultiplier;
    
    // Apply elite bonus (minimal)
    if (protoonPremierGame.eliteBonus) {
        payout += PROTOON_PAYOUTS.ELITE_BONUS;
        protoonPremierGame.stats.eliteBonuses++;
    }
    
    // Apply master penalty
    if (protoonPremierGame.masterPenalty) {
        payout *= 0.3; // 70% penalty
    }
    
    // Apply premier level penalty
    payout *= (1 - (protoonPremierGame.premierLevel * 0.05));
    
    // Calculate final winnings
    let winnings = protoonPremierGame.betAmount * payout;
    
    // Deduct commission
    const commission = winnings * modeData.commission;
    winnings -= commission;
    
    // Apply additional house edge reduction
    winnings *= (1 - modeData.houseEdge * 0.4);
    
    protoonPremierGame.totalWin = Math.floor(Math.max(1, winnings));
    
    balance += protoonPremierGame.betAmount + protoonPremierGame.totalWin;
    updateBalance();
}

function updateProtoonGameStats() {
    protoonPremierGame.stats.handsPlayed++;
    
    if (protoonPremierGame.gameResult === 'win' || 
        protoonPremierGame.gameResult === 'protoon' || 
        protoonPremierGame.gameResult === 'fivecard') {
        
        protoonPremierGame.stats.handsWon++;
        protoonPremierGame.stats.totalWon += protoonPremierGame.totalWin;
        
        if (protoonPremierGame.totalWin > protoonPremierGame.stats.biggestWin) {
            protoonPremierGame.stats.biggestWin = protoonPremierGame.totalWin;
        }
        
        // Update win streak
        protoonPremierGame.streakData.currentWinStreak++;
        protoonPremierGame.streakData.currentLossStreak = 0;
        
        if (protoonPremierGame.streakData.currentWinStreak > protoonPremierGame.streakData.longestWinStreak) {
            protoonPremierGame.streakData.longestWinStreak = protoonPremierGame.streakData.currentWinStreak;
        }
    } else if (protoonPremierGame.gameResult === 'push') {
        protoonPremierGame.stats.handsPushed++;
        protoonPremierGame.streakData.currentWinStreak = 0;
        protoonPremierGame.streakData.currentLossStreak = 0;
    } else {
        // Update loss streak
        protoonPremierGame.streakData.currentLossStreak++;
        protoonPremierGame.streakData.currentWinStreak = 0;
        
        if (protoonPremierGame.streakData.currentLossStreak > protoonPremierGame.streakData.longestLossStreak) {
            protoonPremierGame.streakData.longestLossStreak = protoonPremierGame.streakData.currentLossStreak;
        }
    }
}

function updateProtoonDisplay() {
    // Update player hand
    const playerHandEl = document.getElementById('playerProtoonHand');
    playerHandEl.innerHTML = '';
    protoonPremierGame.playerHand.forEach(card => {
        const cardEl = document.createElement('div');
        cardEl.className = 'playing-card premier-card';
        cardEl.innerHTML = `
            <div class="card-content">
                <div class="card-rank">${card.rank}</div>
                <div class="card-suit">${card.suit}</div>
            </div>
        `;
        playerHandEl.appendChild(cardEl);
    });
    
    // Update dealer hand
    const dealerHandEl = document.getElementById('dealerProtoonHand');
    dealerHandEl.innerHTML = '';
    protoonPremierGame.dealerHand.forEach((card, index) => {
        const cardEl = document.createElement('div');
        
        if (index === 1 && protoonPremierGame.gamePhase === 'playing') {
            // Hide dealer's second card
            cardEl.className = 'playing-card premier-card face-down';
            cardEl.innerHTML = '<div class="card-back">?</div>';
        } else {
            cardEl.className = 'playing-card premier-card';
            cardEl.innerHTML = `
                <div class="card-content">
                    <div class="card-rank">${card.rank}</div>
                    <div class="card-suit">${card.suit}</div>
                </div>
            `;
        }
        dealerHandEl.appendChild(cardEl);
    });
    
    // Update scores
    document.getElementById('playerProtoonScore').textContent = protoonPremierGame.playerScore;
    
    if (protoonPremierGame.gamePhase === 'playing') {
        document.getElementById('dealerProtoonScore').textContent = protoonPremierGame.dealerHand[0].value;
    } else {
        document.getElementById('dealerProtoonScore').textContent = protoonPremierGame.dealerScore;
    }
    
    // Update premier effects
    document.getElementById('premierLevel').textContent = protoonPremierGame.premierLevel;
    document.getElementById('eliteBonus').style.display = protoonPremierGame.eliteBonus ? 'block' : 'none';
    document.getElementById('masterPenalty').style.display = protoonPremierGame.masterPenalty ? 'block' : 'none';
    
    // Update game result
    if (protoonPremierGame.gameResult) {
        const resultEl = document.getElementById('protoonGameResult');
        if (protoonPremierGame.gameResult === 'win' || 
            protoonPremierGame.gameResult === 'protoon' || 
            protoonPremierGame.gameResult === 'fivecard') {
            resultEl.innerHTML = `PREMIER VICTORY! +${protoonPremierGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono';
        } else if (protoonPremierGame.gameResult === 'push') {
            resultEl.innerHTML = `PREMIER PUSH - Bet Returned`;
            resultEl.className = 'text-center text-xl font-bold text-yellow-400 h-8 font-mono';
        } else {
            resultEl.innerHTML = `PREMIER DEFEAT - Lost ${protoonPremierGame.betAmount} GA`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    updateProtoonStats();
}

function updateProtoonStats() {
    document.getElementById('protoonHandsPlayed').textContent = protoonPremierGame.stats.handsPlayed;
    
    const winRate = protoonPremierGame.stats.handsPlayed > 0 ? 
        ((protoonPremierGame.stats.handsWon / protoonPremierGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('protoonWinRate').textContent = `${winRate}%`;
    
    document.getElementById('protoonTotalWagered').textContent = protoonPremierGame.stats.totalWagered.toLocaleString();
    document.getElementById('protoonTotalWon').textContent = protoonPremierGame.stats.totalWon.toLocaleString();
    document.getElementById('protoonBiggestWin').textContent = protoonPremierGame.stats.biggestWin.toLocaleString();
    document.getElementById('protoonProtoons').textContent = protoonPremierGame.stats.protoons;
    document.getElementById('protoonFiveCards').textContent = protoonPremierGame.stats.fivecardtricks;
    document.getElementById('protoonBusts').textContent = protoonPremierGame.stats.busts;
    document.getElementById('protoonLossStreak').textContent = protoonPremierGame.streakData.currentLossStreak;
    document.getElementById('protoonEliteBonuses').textContent = protoonPremierGame.stats.eliteBonuses;
    document.getElementById('protoonMasterPenalties').textContent = protoonPremierGame.stats.masterPenalties;
}

function updateProtoonStatus(message) {
    document.getElementById('protoonGameStatus').textContent = message;
}

function changeProtoonMode() {
    const mode = document.getElementById('protoonMode').value;
    protoonPremierGame.gameMode = mode;
    
    const modeData = PROTOON_MODES[mode];
    document.getElementById('protoonModeInfo').innerHTML = `
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Elite Chance: ${(modeData.eliteChance * 100).toFixed(1)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}% | 
        Commission: ${(modeData.commission * 100).toFixed(0)}%
    `;
}

function changeProtoonDifficulty() {
    const difficulty = document.getElementById('protoonDifficulty').value;
    protoonPremierGame.difficulty = difficulty;
    
    const diffData = PROTOON_DIFFICULTIES[difficulty];
    document.getElementById('protoonDifficultyInfo').innerHTML = `
        Dealer Advantage: ${(diffData.dealerAdvantage * 100).toFixed(0)}% | 
        Card Bias: ${(diffData.cardBias * 100).toFixed(0)}% | 
        Bust Penalty: ${diffData.bustPenalty}x | 
        Level Multiplier: ${diffData.levelMultiplier}x
    `;
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeProtoonMode();
    changeProtoonDifficulty();
});