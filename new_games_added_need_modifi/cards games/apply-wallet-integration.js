/**
 * <PERSON><PERSON><PERSON> to apply wallet integration to card games
 * This script contains the common patterns for updating games
 */

// Common wallet integration header to replace balance initialization
const WALLET_INTEGRATION_HEADER = `// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('GAME_NAME');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        balance = walletIntegration.balance;
        walletIntegration.updateBalanceDisplay();
    }
}`;

// Common bet processing function
const BET_PROCESSING_PATTERN = `
// Use wallet integration to process bet
if (!walletIntegration || !(await walletIntegration.processBet(betAmount, {
    game_action: 'ACTION_NAME',
    bet_amount: betAmount
}))) {
    return;
}

// Update local balance reference
balance = walletIntegration.balance;`;

// Common win processing function
const WIN_PROCESSING_PATTERN = `
// Process winnings through wallet integration
if (winnings > 0 && walletIntegration) {
    await walletIntegration.processWin(winnings, {
        game_action: 'game_end',
        result: result,
        bet_amount: betAmount,
        win_amount: winnings
    });
    balance = walletIntegration.balance;
}`;

// Common initialization pattern
const INITIALIZATION_PATTERN = `
// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize wallet integration first
    await initializeWallet();
    
    // Load the game
    LOAD_GAME_FUNCTION();
});`;

// HTML script inclusion pattern
const HTML_SCRIPT_PATTERN = `    <!-- Include API Service and Wallet Integration -->
    <script src="../../../api-service.js"></script>
    <script src="../shared-wallet-integration.js"></script>
    <script src="script.js"></script>`;

/**
 * Game-specific configurations
 */
const GAME_CONFIGS = {
    'BlackJackSurrenderElite': {
        gameName: 'blackjack_surrender',
        loadFunction: 'loadBlackjackSurrenderGame',
        betFunctions: ['dealBlackjackSurrenderCards', 'doubleDownBlackjackSurrender'],
        winFunction: 'endBlackjackSurrenderGame'
    },
    'Spanish 21: Midnight Rush': {
        gameName: 'spanish21',
        loadFunction: 'loadSpanish21Game',
        betFunctions: ['dealSpanish21Cards', 'doubleDownSpanish21'],
        winFunction: 'endSpanish21Game'
    },
    'ckjack Supreme: VIP': {
        gameName: 'blackjack_vip',
        loadFunction: 'loadBlackjackVIPGame',
        betFunctions: ['dealBlackjackVIPCards', 'doubleDownBlackjackVIP'],
        winFunction: 'endBlackjackVIPGame'
    },
    'Baccarat First Person': {
        gameName: 'baccarat_fp',
        loadFunction: 'loadBaccaratFPGame',
        betFunctions: ['dealBaccaratFPCards'],
        winFunction: 'endBaccaratFPGame'
    },
    'Caribbean Stud: Island Kings': {
        gameName: 'caribbean_stud',
        loadFunction: 'loadCaribbeanStudGame',
        betFunctions: ['dealCaribbeanStudCards'],
        winFunction: 'endCaribbeanStudGame'
    },
    'CaribbeanStudAdventure': {
        gameName: 'caribbean_stud_adv',
        loadFunction: 'loadCaribbeanStudAdvGame',
        betFunctions: ['dealCaribbeanStudAdvCards'],
        winFunction: 'endCaribbeanStudAdvGame'
    },
    'Casino War: Global Conflict': {
        gameName: 'casino_war',
        loadFunction: 'loadCasinoWarGame',
        betFunctions: ['dealCasinoWarCards'],
        winFunction: 'endCasinoWarGame'
    },
    'CasinoWar_HighStack': {
        gameName: 'casino_war_hs',
        loadFunction: 'loadCasinoWarHSGame',
        betFunctions: ['dealCasinoWarHSCards'],
        winFunction: 'endCasinoWarHSGame'
    },
    'Dragon Tiger: Turbo Tables': {
        gameName: 'dragon_tiger',
        loadFunction: 'loadDragonTigerGame',
        betFunctions: ['dealDragonTigerCards'],
        winFunction: 'endDragonTigerGame'
    },
    'Three Card Poker: Maverick': {
        gameName: 'three_card_poker',
        loadFunction: 'loadThreeCardPokerGame',
        betFunctions: ['dealThreeCardPokerCards'],
        winFunction: 'endThreeCardPokerGame'
    },
    'threeCardPokerDeluxe': {
        gameName: 'three_card_poker_deluxe',
        loadFunction: 'loadThreeCardPokerDeluxeGame',
        betFunctions: ['dealThreeCardPokerDeluxeCards'],
        winFunction: 'endThreeCardPokerDeluxeGame'
    },
    'Texas Hold\'em: All-In Arena': {
        gameName: 'texas_holdem',
        loadFunction: 'loadTexasHoldemGame',
        betFunctions: ['dealTexasHoldemCards', 'betTexasHoldem'],
        winFunction: 'endTexasHoldemGame'
    },
    'holdem': {
        gameName: 'holdem',
        loadFunction: 'loadHoldemGame',
        betFunctions: ['dealHoldemCards', 'betHoldem'],
        winFunction: 'endHoldemGame'
    },
    'Let It Ride: Frontier Fortune': {
        gameName: 'let_it_ride',
        loadFunction: 'loadLetItRideGame',
        betFunctions: ['dealLetItRideCards'],
        winFunction: 'endLetItRideGame'
    },
    'LetItRide_WildWest': {
        gameName: 'let_it_ride_ww',
        loadFunction: 'loadLetItRideWWGame',
        betFunctions: ['dealLetItRideWWCards'],
        winFunction: 'endLetItRideWWGame'
    },
    'Pai Gow: Dynasty Tiles': {
        gameName: 'pai_gow',
        loadFunction: 'loadPaiGowGame',
        betFunctions: ['dealPaiGowCards'],
        winFunction: 'endPaiGowGame'
    },
    'paiGowPowreTiles': {
        gameName: 'pai_gow_power',
        loadFunction: 'loadPaiGowPowerGame',
        betFunctions: ['dealPaiGowPowerCards'],
        winFunction: 'endPaiGowPowerGame'
    },
    'Pontoon: Ocean King': {
        gameName: 'pontoon',
        loadFunction: 'loadPontoonGame',
        betFunctions: ['dealPontoonCards', 'doubleDownPontoon'],
        winFunction: 'endPontoonGame'
    },
    'ProtoonPremier': {
        gameName: 'pontoon_premier',
        loadFunction: 'loadPontoonPremierGame',
        betFunctions: ['dealPontoonPremierCards'],
        winFunction: 'endPontoonPremierGame'
    },
    'Red Dog: Wild Streak': {
        gameName: 'red_dog',
        loadFunction: 'loadRedDogGame',
        betFunctions: ['dealRedDogCards'],
        winFunction: 'endRedDogGame'
    },
    'RedDogRush': {
        gameName: 'red_dog_rush',
        loadFunction: 'loadRedDogRushGame',
        betFunctions: ['dealRedDogRushCards'],
        winFunction: 'endRedDogRushGame'
    },
    'Roulette Royale: Diamond Edition': {
        gameName: 'roulette_royale',
        loadFunction: 'loadRouletteRoyaleGame',
        betFunctions: ['spinRouletteRoyale'],
        winFunction: 'endRouletteRoyaleGame'
    },
    'Roulette VR: Immersive Spin': {
        gameName: 'roulette_vr',
        loadFunction: 'loadRouletteVRGame',
        betFunctions: ['spinRouletteVR'],
        winFunction: 'endRouletteVRGame'
    },
    'RouletteFirstPerson': {
        gameName: 'roulette_fp',
        loadFunction: 'loadRouletteFPGame',
        betFunctions: ['spinRouletteFP'],
        winFunction: 'endRouletteFPGame'
    },
    'SicBoGalaxy': {
        gameName: 'sic_bo',
        loadFunction: 'loadSicBoGame',
        betFunctions: ['rollSicBoDice'],
        winFunction: 'endSicBoGame'
    },
    'Craps Stadium: High Roller': {
        gameName: 'craps',
        loadFunction: 'loadCrapsGame',
        betFunctions: ['rollCrapsDice'],
        winFunction: 'endCrapsGame'
    },
    'Keno Quest: Golden Numbers': {
        gameName: 'keno',
        loadFunction: 'loadKenoGame',
        betFunctions: ['drawKenoNumbers'],
        winFunction: 'endKenoGame'
    },
    'Kenolandia': {
        gameName: 'kenolandia',
        loadFunction: 'loadKenolandiaGame',
        betFunctions: ['drawKenolandiaNumbers'],
        winFunction: 'endKenolandiaGame'
    },
    'videopoker': {
        gameName: 'video_poker',
        loadFunction: 'loadVideoPokerGame',
        betFunctions: ['dealVideoPokerCards'],
        winFunction: 'endVideoPokerGame'
    },
    'pokerplace': {
        gameName: 'poker_place',
        loadFunction: 'loadPokerPlaceGame',
        betFunctions: ['dealPokerPlaceCards'],
        winFunction: 'endPokerPlaceGame'
    }
};

// Export configurations for use in automation
window.WALLET_INTEGRATION_CONFIGS = {
    WALLET_INTEGRATION_HEADER,
    BET_PROCESSING_PATTERN,
    WIN_PROCESSING_PATTERN,
    INITIALIZATION_PATTERN,
    HTML_SCRIPT_PATTERN,
    GAME_CONFIGS
};
