// Game state
let balance = 1000;

// BlackJack Surrender Elite game state
let blackjackSurrenderGame = {
    isPlaying: false,
    gamePhase: 'betting',
    betAmount: 0,
    
    // Enhanced Elite features
    difficulty: 'elite',
    deckCount: 6,
    penetration: 0.75,
    surrenderAllowed: true,
    doubleAfterSplit: true,
    resplitAces: false,
    
    // Game mechanics
    deck: [],
    playerHands: [[]],
    dealerCards: [],
    currentHandIndex: 0,
    dealerHidden: true,
    
    // Elite surrender features
    surrenderValue: 0.5,
    earlyLateSupport: true,
    surrenderRecommendations: true,
    
    // Advanced betting
    sideBets: {
        perfectPairs: { active: false, amount: 0, odds: 25 },
        twentyOnePlusThree: { active: false, amount: 0, odds: 9 },
        insurance: { active: false, amount: 0, odds: 2 },
        luckyLadies: { active: false, amount: 0, odds: 1000 },
        royalMatch: { active: false, amount: 0, odds: 25 }
    },
    
    // Elite strategy features
    basicStrategy: true,
    cardCounting: false,
    runningCount: 0,
    trueCount: 0,
    cardsDealt: 0,
    
    // Hand tracking
    handResults: [],
    splits: 0,
    maxSplits: 3,
    
    // Elite statistics
    sessionStats: {
        handsPlayed: 0,
        handsWon: 0,
        handsSurrendered: 0,
        blackjacks: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        surrenderSavings: 0,
        perfectDecisions: 0,
        strategicErrors: 0
    },
    
    // Advanced features
    autoStrategy: false,
    showRecommendations: true,
    cardCountingDisplay: false,
    holeCardPeek: true,
    
    // Elite surrender scenarios
    surrenderScenarios: {
        hard16vs9: true,
        hard16vs10: true,
        hard16vsA: true,
        hard15vs10: true,
        hard15vsA: false,
        soft17vsA: false
    }
};

// Elite strategy tables
const BASIC_STRATEGY = {
    hard: {
        // Player total: [2,3,4,5,6,7,8,9,10,A]
        5: ['H','H','H','H','H','H','H','H','H','H'],
        6: ['H','H','H','H','H','H','H','H','H','H'],
        7: ['H','H','H','H','H','H','H','H','H','H'],
        8: ['H','H','H','H','H','H','H','H','H','H'],
        9: ['H','D','D','D','D','H','H','H','H','H'],
        10: ['D','D','D','D','D','D','D','D','H','H'],
        11: ['D','D','D','D','D','D','D','D','D','H'],
        12: ['H','H','S','S','S','H','H','H','H','H'],
        13: ['S','S','S','S','S','H','H','H','H','H'],
        14: ['S','S','S','S','S','H','H','H','H','H'],
        15: ['S','S','S','S','S','H','H','H','R','R'],
        16: ['S','S','S','S','S','H','H','R','R','R'],
        17: ['S','S','S','S','S','S','S','S','S','S'],
        18: ['S','S','S','S','S','S','S','S','S','S'],
        19: ['S','S','S','S','S','S','S','S','S','S'],
        20: ['S','S','S','S','S','S','S','S','S','S'],
        21: ['S','S','S','S','S','S','S','S','S','S']
    },
    soft: {
        // Soft hands (Ace + other card)
        13: ['H','H','H','D','D','H','H','H','H','H'], // A,2
        14: ['H','H','H','D','D','H','H','H','H','H'], // A,3
        15: ['H','H','D','D','D','H','H','H','H','H'], // A,4
        16: ['H','H','D','D','D','H','H','H','H','H'], // A,5
        17: ['H','D','D','D','D','H','H','H','H','H'], // A,6
        18: ['S','D','D','D','D','S','S','H','H','H'], // A,7
        19: ['S','S','S','S','S','S','S','S','S','S'], // A,8
        20: ['S','S','S','S','S','S','S','S','S','S'], // A,9
        21: ['S','S','S','S','S','S','S','S','S','S']  // A,10
    },
    pairs: {
        // Pair splitting strategy
        'A': ['Y','Y','Y','Y','Y','Y','Y','Y','Y','Y'],
        '2': ['Y','Y','Y','Y','Y','Y','N','N','N','N'],
        '3': ['Y','Y','Y','Y','Y','Y','N','N','N','N'],
        '4': ['N','N','N','Y','Y','N','N','N','N','N'],
        '5': ['N','N','N','N','N','N','N','N','N','N'],
        '6': ['Y','Y','Y','Y','Y','N','N','N','N','N'],
        '7': ['Y','Y','Y','Y','Y','Y','N','N','N','N'],
        '8': ['Y','Y','Y','Y','Y','Y','Y','Y','Y','Y'],
        '9': ['Y','Y','Y','Y','Y','N','Y','Y','N','N'],
        '10': ['N','N','N','N','N','N','N','N','N','N']
    }
};

// Card counting systems
const CARD_COUNTING = {
    hiLo: {
        '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,
        '7': 0, '8': 0, '9': 0,
        '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1
    },
    omega2: {
        '2': 1, '3': 1, '4': 2, '5': 2, '6': 2, '7': 1,
        '8': 0, '9': -1, '10': -2, 'J': -2, 'Q': -2, 'K': -2, 'A': 0
    }
};

// Elite surrender recommendations
const SURRENDER_MATRIX = {
    hard: {
        15: { 10: true, 'J': true, 'Q': true, 'K': true, 'A': true },
        16: { 9: true, 10: true, 'J': true, 'Q': true, 'K': true, 'A': true },
        17: { 'A': true } // Only vs Ace in some variations
    },
    soft: {
        17: { 'A': false } // Rarely surrender soft 17
    }
};

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadBlackjackSurrenderEliteGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Game Controls & Strategy -->
            <div class="lg:col-span-1">
                <!-- Betting Panel -->
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-4">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">⚡ BLACKJACK SURRENDER ELITE ⚡</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 BET AMOUNT</label>
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <button onclick="setBetAmountBJ(25)" class="quick-bet-btn bg-green-600/20 border border-green-500 text-green-400 py-1 px-2 rounded text-xs hover:bg-green-600/40">25</button>
                            <button onclick="setBetAmountBJ(100)" class="quick-bet-btn bg-blue-600/20 border border-blue-500 text-blue-400 py-1 px-2 rounded text-xs hover:bg-blue-600/40">100</button>
                            <button onclick="setBetAmountBJ(250)" class="quick-bet-btn bg-purple-600/20 border border-purple-500 text-purple-400 py-1 px-2 rounded text-xs hover:bg-purple-600/40">250</button>
                        </div>
                        <input type="number" id="blackjackEliteBet" value="50" min="10" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <!-- Side Bets -->
                    <div class="mb-4">
                        <label class="block text-sm mb-3 text-gray-300">🎰 ELITE SIDE BETS</label>
                        <div class="space-y-2">
                            <button id="betPerfectPairs" class="side-bet-btn w-full py-2 rounded-lg font-bold bg-yellow-600/20 border border-yellow-500 text-yellow-400 hover:bg-yellow-600/40 transition-all text-sm">
                                💎 Perfect Pairs (25:1)
                            </button>
                            <button id="bet21Plus3" class="side-bet-btn w-full py-2 rounded-lg font-bold bg-orange-600/20 border border-orange-500 text-orange-400 hover:bg-orange-600/40 transition-all text-sm">
                                🃏 21+3 (9:1)
                            </button>
                            <button id="betLuckyLadies" class="side-bet-btn w-full py-2 rounded-lg font-bold bg-pink-600/20 border border-pink-500 text-pink-400 hover:bg-pink-600/40 transition-all text-sm">
                                👸 Lucky Ladies (1000:1)
                            </button>
                        </div>
                    </div>

                    <button id="dealBlackjackElite" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🎴 DEAL CARDS 🎴
                    </button>

                    <!-- Current Bets Display -->
                    <div class="bg-black/50 p-4 rounded-lg">
                        <div class="text-center">
                            <div class="text-sm text-gray-400 mb-1">Main Bet</div>
                            <div id="blackjackMainBetElite" class="text-lg font-bold text-green-400">0 GA</div>
                            <div class="text-sm text-gray-400 mb-1 mt-2">Side Bets</div>
                            <div id="blackjackSideBetstElite" class="text-sm text-yellow-400">None</div>
                            <div class="text-sm text-gray-400 mb-1 mt-2">Total Risk</div>
                            <div id="totalRiskElite" class="text-xl font-bold text-red-400">0 GA</div>
                        </div>
                    </div>
                </div>

                <!-- Elite Strategy Panel -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🧠 ELITE STRATEGY</h5>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="showRecommendations" checked class="mr-2">
                            <span class="text-gray-300 text-sm">Strategy Hints</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="autoStrategy" class="mr-2">
                            <span class="text-gray-300 text-sm">Auto Strategy</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="cardCountingDisplay" class="mr-2">
                            <span class="text-gray-300 text-sm">Card Counting</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="surrenderRecommendations" checked class="mr-2">
                            <span class="text-gray-300 text-sm">Surrender Hints</span>
                        </label>
                    </div>

                    <!-- Strategy Recommendation Display -->
                    <div class="mt-4 bg-black/50 p-3 rounded-lg">
                        <div class="text-sm text-gray-400 mb-1">Recommendation</div>
                        <div id="strategyRecommendation" class="text-cyan-400 font-bold text-sm min-h-[20px]">
                            Place your bet to begin
                        </div>
                    </div>

                    <!-- Card Counting Display -->
                    <div id="cardCountingPanel" class="mt-4 bg-black/50 p-3 rounded-lg hidden">
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Running:</span>
                                <span class="text-yellow-400" id="runningCount">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">True:</span>
                                <span class="text-green-400" id="trueCount">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Dealt:</span>
                                <span class="text-blue-400" id="cardsDealt">0</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Advantage:</span>
                                <span class="text-purple-400" id="playerAdvantage">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Session Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 ELITE STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Hands:</span>
                            <span class="text-white" id="handsPlayedElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Won:</span>
                            <span class="text-green-400" id="handsWonElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Surrendered:</span>
                            <span class="text-yellow-400" id="handsSurrenderedElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Blackjacks:</span>
                            <span class="text-purple-400" id="blackjacksElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Wagered:</span>
                            <span class="text-red-400" id="totalWageredElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Won:</span>
                            <span class="text-green-400" id="totalWonElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Surrender $:</span>
                            <span class="text-cyan-400" id="surrenderSavingsElite">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Strategy:</span>
                            <span class="text-blue-400" id="strategyAccuracyElite">100%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Game Area -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <!-- Dealer Area -->
                    <div class="text-center mb-6">
                        <h5 class="text-lg font-bold mb-3 text-red-400">🎩 DEALER</h5>
                        <div id="dealerCardsElite" class="flex justify-center space-x-2 mb-2 min-h-[80px]">
                            <!-- Dealer cards will appear here -->
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total</div>
                            <div id="dealerTotalElite" class="text-2xl font-bold text-red-400">-</div>
                        </div>
                    </div>

                    <!-- Game Actions -->
                    <div class="text-center mb-6">
                        <div class="grid grid-cols-2 lg:grid-cols-5 gap-2">
                            <button id="hitElite" class="action-btn bg-green-600/20 border border-green-500 text-green-400 py-2 px-4 rounded-lg font-bold hover:bg-green-600/40 transition-all" disabled>
                                👊 HIT
                            </button>
                            <button id="standElite" class="action-btn bg-blue-600/20 border border-blue-500 text-blue-400 py-2 px-4 rounded-lg font-bold hover:bg-blue-600/40 transition-all" disabled>
                                ✋ STAND
                            </button>
                            <button id="doubleElite" class="action-btn bg-yellow-600/20 border border-yellow-500 text-yellow-400 py-2 px-4 rounded-lg font-bold hover:bg-yellow-600/40 transition-all" disabled>
                                💰 DOUBLE
                            </button>
                            <button id="splitElite" class="action-btn bg-purple-600/20 border border-purple-500 text-purple-400 py-2 px-4 rounded-lg font-bold hover:bg-purple-600/40 transition-all" disabled>
                                ✂️ SPLIT
                            </button>
                            <button id="surrenderElite" class="action-btn bg-red-600/20 border border-red-500 text-red-400 py-2 px-4 rounded-lg font-bold hover:bg-red-600/40 transition-all" disabled>
                                🏳️ SURRENDER
                            </button>
                        </div>
                        
                        <!-- Insurance Bet -->
                        <div id="insurancePanel" class="mt-4 hidden">
                            <button id="insuranceElite" class="cyber-button bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-6 rounded-lg">
                                🛡️ INSURANCE (2:1)
                            </button>
                        </div>
                    </div>

                    <!-- Player Hands Area -->
                    <div class="text-center">
                        <h5 class="text-lg font-bold mb-3 text-green-400">👤 PLAYER HANDS</h5>
                        <div id="playerHandsElite" class="space-y-4">
                            <!-- Player hands will appear here -->
                        </div>
                    </div>

                    <!-- Game Status -->
                    <div class="text-center mt-6">
                        <div id="gamePhaseElite" class="text-xl font-bold text-purple-400 mb-2">Place Your Bet</div>
                        <div id="gameStatusElite" class="text-lg font-semibold mb-2">Select bet amount and deal cards</div>
                        <div id="gameResultElite" class="text-xl font-bold"></div>
                    </div>
                </div>
            </div>

            <!-- Hand History & Analytics -->
            <div class="lg:col-span-1">
                <!-- Current Hand Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🎯 CURRENT HAND</h5>
                    <div class="text-xs space-y-1">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Hand:</span>
                            <span class="text-white" id="currentHandNumber">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Active:</span>
                            <span class="text-green-400" id="activeHandIndex">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Splits:</span>
                            <span class="text-yellow-400" id="currentSplits">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Can Split:</span>
                            <span class="text-blue-400" id="canSplitDisplay">No</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Can Double:</span>
                            <span class="text-purple-400" id="canDoubleDisplay">No</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Can Surrender:</span>
                            <span class="text-red-400" id="canSurrenderDisplay">No</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Hands -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📈 RECENT HANDS</h5>
                    <div id="recentHandsElite" class="space-y-2 max-h-32 overflow-y-auto">
                        <!-- Recent hand results will appear here -->
                    </div>
                </div>

                <!-- Surrender Analysis -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏳️ SURRENDER ANALYSIS</h5>
                    <div class="text-xs space-y-1">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total Surrenders:</span>
                            <span class="text-yellow-400" id="totalSurrenders">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Money Saved:</span>
                            <span class="text-green-400" id="moneySaved">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Surrender Rate:</span>
                            <span class="text-cyan-400" id="surrenderRate">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Optimal Rate:</span>
                            <span class="text-blue-400" id="optimalSurrenderRate">3-5%</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚡ QUICK ACTIONS</h5>
                    <div class="space-y-2">
                        <button onclick="resetShoe()" class="w-full cyber-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                            🔄 New Shoe
                        </button>
                        <button onclick="toggleDifficulty()" class="w-full cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                            ⚙️ Difficulty
                        </button>
                        <button onclick="showStrategyChart()" class="w-full cyber-button bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                            📊 Strategy Chart
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Complete Overlay -->
        <div id="blackjackOverlayElite" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🎴 HAND COMPLETE 🎴</h3>
                <div id="finalResultElite" class="text-xl mb-4"></div>
                <div id="finalWinAmountElite" class="text-lg mb-4"></div>
                <div id="strategyFeedbackElite" class="text-sm text-cyan-400 italic mb-6"></div>
                <button onclick="hideBlackjackOverlayElite()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    NEXT HAND
                </button>
            </div>
        </div>

        <!-- Strategy Chart Modal -->
        <div id="strategyChartModal" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-6 rounded-xl border border-purple-500/50 max-w-4xl max-h-[90vh] overflow-y-auto">
                <h3 class="text-xl font-bold mb-4 text-purple-400">📊 BASIC STRATEGY CHART</h3>
                <div id="strategyChartContent" class="text-xs">
                    <!-- Strategy chart will be generated here -->
                </div>
                <button onclick="hideStrategyChart()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg mt-4">
                    CLOSE
                </button>
            </div>
        </div>
    `;

    initializeBlackjackSurrenderElite();
}

function initializeBlackjackSurrenderElite() {
    // Initialize event listeners
    document.getElementById('dealBlackjackElite').addEventListener('click', dealBlackjackElite);
    document.getElementById('hitElite').addEventListener('click', hitElite);
    document.getElementById('standElite').addEventListener('click', standElite);
    document.getElementById('doubleElite').addEventListener('click', doubleElite);
    document.getElementById('splitElite').addEventListener('click', splitElite);
    document.getElementById('surrenderElite').addEventListener('click', surrenderElite);
    document.getElementById('insuranceElite').addEventListener('click', takeInsurance);
    
    // Side bet listeners
    document.getElementById('betPerfectPairs').addEventListener('click', () => toggleSideBet('perfectPairs'));
    document.getElementById('bet21Plus3').addEventListener('click', () => toggleSideBet('twentyOnePlusThree'));
    document.getElementById('betLuckyLadies').addEventListener('click', () => toggleSideBet('luckyLadies'));
    
    // Settings listeners
    document.getElementById('cardCountingDisplay').addEventListener('change', toggleCardCounting);
    
    // Initialize game
    initializeDeckElite();
    updateEliteDisplay();
    updateSessionStatsElite();
}

function initializeDeckElite() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    
    blackjackSurrenderGame.deck = [];
    
    // Create multiple decks based on difficulty
    for (let deckNum = 0; deckNum < blackjackSurrenderGame.deckCount; deckNum++) {
        for (const suit of suits) {
            for (const rank of ranks) {
                blackjackSurrenderGame.deck.push({ rank, suit });
            }
        }
    }
    
    // Shuffle deck
    for (let i = blackjackSurrenderGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [blackjackSurrenderGame.deck[i], blackjackSurrenderGame.deck[j]] = [blackjackSurrenderGame.deck[j], blackjackSurrenderGame.deck[i]];
    }
    
    // Reset counting
    blackjackSurrenderGame.runningCount = 0;
    blackjackSurrenderGame.cardsDealt = 0;
}

function setBetAmountBJ(amount) {
    document.getElementById('blackjackEliteBet').value = amount;
    updateEliteDisplay();
}

function toggleSideBet(betType) {
    if (blackjackSurrenderGame.isPlaying) return;
    
    const betAmount = parseInt(document.getElementById('blackjackEliteBet').value);
    const sideBet = blackjackSurrenderGame.sideBets[betType];
    
    if (sideBet.active) {
        sideBet.active = false;
        sideBet.amount = 0;
        document.getElementById(`bet${betType.charAt(0).toUpperCase() + betType.slice(1).replace(/([A-Z])/g, '$1')}`).classList.remove('ring-2', 'ring-yellow-400');
    } else {
        if (betAmount > balance) return;
        sideBet.active = true;
        sideBet.amount = betAmount;
        document.getElementById(`bet${betType.charAt(0).toUpperCase() + betType.slice(1).replace(/([A-Z])/g, '$1')}`).classList.add('ring-2', 'ring-yellow-400');
    }
    
    updateEliteDisplay();
}

function updateEliteDisplay() {
    const betAmount = parseInt(document.getElementById('blackjackEliteBet').value) || 0;
    document.getElementById('blackjackMainBetElite').textContent = betAmount + ' GA';
    
    const activeSideBets = Object.entries(blackjackSurrenderGame.sideBets)
        .filter(([_, bet]) => bet.active)
        .map(([type, bet]) => `${type}: ${bet.amount}`)
        .join(', ');
    
    document.getElementById('blackjackSideBetstElite').textContent = activeSideBets || 'None';
    
    const totalRisk = betAmount + Object.values(blackjackSurrenderGame.sideBets)
        .filter(bet => bet.active)
        .reduce((sum, bet) => sum + bet.amount, 0);
    
    document.getElementById('totalRiskElite').textContent = totalRisk + ' GA';
}

function dealBlackjackElite() {
    const betAmount = parseInt(document.getElementById('blackjackEliteBet').value);
    
    if (betAmount > balance) {
        document.getElementById('strategyRecommendation').textContent = 'Insufficient balance for this bet!';
        return;
    }
    
    // Calculate total bet
    const totalBet = betAmount + Object.values(blackjackSurrenderGame.sideBets)
        .filter(bet => bet.active)
        .reduce((sum, bet) => sum + bet.amount, 0);
    
    // Deduct bet
    balance -= totalBet;
    updateBalance();
    
    // Initialize hand
    blackjackSurrenderGame.isPlaying = true;
    blackjackSurrenderGame.betAmount = betAmount;
    blackjackSurrenderGame.playerHands = [[]];
    blackjackSurrenderGame.dealerCards = [];
    blackjackSurrenderGame.currentHandIndex = 0;
    blackjackSurrenderGame.dealerHidden = true;
    blackjackSurrenderGame.splits = 0;
    
    // Update UI
    document.getElementById('dealBlackjackElite').disabled = true;
    document.getElementById('gamePhaseElite').textContent = 'Dealing Cards...';
    document.getElementById('gameStatusElite').textContent = 'Cards being dealt...';
    
    // Deal initial cards
    setTimeout(() => dealCardElite('player', 0), 500);
    setTimeout(() => dealCardElite('dealer'), 1000);
    setTimeout(() => dealCardElite('player', 0), 1500);
    setTimeout(() => dealCardElite('dealer'), 2000);
    
    // Check for blackjack and insurance
    setTimeout(() => checkInitialConditions(), 2500);
}

function dealCardElite(recipient, handIndex = 0) {
    if (blackjackSurrenderGame.deck.length < 20) {
        initializeDeckElite(); // Reshuffle if running low
    }
    
    const card = blackjackSurrenderGame.deck.pop();
    
    // Update card counting
    if (blackjackSurrenderGame.cardCounting) {
        const countValue = CARD_COUNTING.hiLo[card.rank] || 0;
        blackjackSurrenderGame.runningCount += countValue;
        blackjackSurrenderGame.cardsDealt++;
        updateCardCountingDisplay();
    }
    
    if (recipient === 'player') {
        blackjackSurrenderGame.playerHands[handIndex].push(card);
    } else {
        blackjackSurrenderGame.dealerCards.push(card);
    }
    
    renderHandsElite();
    updateHandInfo();
}

function renderHandsElite() {
    // Render dealer cards
    const dealerContainer = document.getElementById('dealerCardsElite');
    dealerContainer.innerHTML = '';
    
    blackjackSurrenderGame.dealerCards.forEach((card, index) => {
        const cardElement = createCardElementElite(card, index === 1 && blackjackSurrenderGame.dealerHidden);
        dealerContainer.appendChild(cardElement);
    });
    
    // Update dealer total
    const dealerTotal = blackjackSurrenderGame.dealerHidden ? 
        calculateHandValueElite([blackjackSurrenderGame.dealerCards[0]]) + '?' :
        calculateHandValueElite(blackjackSurrenderGame.dealerCards);
    document.getElementById('dealerTotalElite').textContent = dealerTotal;
    
    // Render player hands
    const playerContainer = document.getElementById('playerHandsElite');
    playerContainer.innerHTML = '';
    
    blackjackSurrenderGame.playerHands.forEach((hand, handIndex) => {
        const handDiv = document.createElement('div');
        handDiv.className = `player-hand ${handIndex === blackjackSurrenderGame.currentHandIndex ? 'border-2 border-green-400' : 'border border-gray-500'} p-4 rounded-lg`;
        
        const handTitle = document.createElement('div');
        handTitle.className = 'text-sm font-bold mb-2';
        handTitle.textContent = `Hand ${handIndex + 1}${blackjackSurrenderGame.playerHands.length > 1 ? ` (${blackjackSurrenderGame.betAmount} GA)` : ''}`;
        handDiv.appendChild(handTitle);
        
        const cardsDiv = document.createElement('div');
        cardsDiv.className = 'flex justify-center space-x-2 mb-2';
        
        hand.forEach(card => {
            const cardElement = createCardElementElite(card, false);
            cardsDiv.appendChild(cardElement);
        });
        
        handDiv.appendChild(cardsDiv);
        
        const totalDiv = document.createElement('div');
        totalDiv.className = 'text-center text-xl font-bold text-green-400';
        totalDiv.textContent = calculateHandValueElite(hand);
        handDiv.appendChild(totalDiv);
        
        playerContainer.appendChild(handDiv);
    });
}

function createCardElementElite(card, isHidden = false) {
    const cardElement = document.createElement('div');
    cardElement.className = 'card-elite inline-block w-16 h-24 bg-white rounded-lg border-2 border-gray-300 m-1 flex items-center justify-center text-2xl font-bold shadow-lg';
    
    if (isHidden) {
        cardElement.innerHTML = '🂠';
        cardElement.className += ' bg-blue-900 text-white';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.innerHTML = `${card.rank}${card.suit}`;
        cardElement.className += isRed ? ' text-red-600' : ' text-black';
    }
    
    return cardElement;
}

function calculateHandValueElite(hand) {
    let total = 0;
    let aces = 0;
    
    for (const card of hand) {
        if (card.rank === 'A') {
            aces++;
            total += 11;
        } else if (['J', 'Q', 'K'].includes(card.rank)) {
            total += 10;
        } else {
            total += parseInt(card.rank);
        }
    }
    
    while (total > 21 && aces > 0) {
        total -= 10;
        aces--;
    }
    
    return total;
}

function checkInitialConditions() {
    const playerTotal = calculateHandValueElite(blackjackSurrenderGame.playerHands[0]);
    const dealerUpCard = blackjackSurrenderGame.dealerCards[0];
    
    // Check for insurance
    if (dealerUpCard.rank === 'A') {
        showInsuranceOption();
        return;
    }
    
    // Check for player blackjack
    if (playerTotal === 21) {
        // Check for dealer blackjack
        const dealerTotal = calculateHandValueElite(blackjackSurrenderGame.dealerCards);
        if (dealerTotal === 21) {
            // Push
            endHandElite('push', 0);
        } else {
            // Player blackjack wins
            blackjackSurrenderGame.sessionStats.blackjacks++;
            endHandElite('blackjack', Math.floor(blackjackSurrenderGame.betAmount * 2.5));
        }
        return;
    }
    
    // Enable player actions
    enablePlayerActions();
    updateStrategyRecommendation();
}

function showInsuranceOption() {
    document.getElementById('insurancePanel').classList.remove('hidden');
    document.getElementById('gameStatusElite').textContent = 'Dealer showing Ace - Insurance available';
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (!document.getElementById('insurancePanel').classList.contains('hidden')) {
            document.getElementById('insurancePanel').classList.add('hidden');
            checkInitialConditions();
        }
    }, 10000);
}

function takeInsurance() {
    const insuranceAmount = Math.floor(blackjackSurrenderGame.betAmount / 2);
    
    if (insuranceAmount > balance) {
        document.getElementById('strategyRecommendation').textContent = 'Insufficient balance for insurance!';
        return;
    }
    
    balance -= insuranceAmount;
    updateBalance();
    
    blackjackSurrenderGame.sideBets.insurance.active = true;
    blackjackSurrenderGame.sideBets.insurance.amount = insuranceAmount;
    
    document.getElementById('insurancePanel').classList.add('hidden');
    
    // Reveal dealer hole card to check for blackjack
    blackjackSurrenderGame.dealerHidden = false;
    renderHandsElite();
    
    const dealerTotal = calculateHandValueElite(blackjackSurrenderGame.dealerCards);
    
    if (dealerTotal === 21) {
        // Dealer has blackjack - insurance pays 2:1
        const insuranceWin = insuranceAmount * 3;
        balance += insuranceWin;
        updateBalance();
        
        const playerTotal = calculateHandValueElite(blackjackSurrenderGame.playerHands[0]);
        if (playerTotal === 21) {
            endHandElite('push', 0);
        } else {
            endHandElite('dealer_blackjack', insuranceWin - blackjackSurrenderGame.betAmount);
        }
    } else {
        // No dealer blackjack - continue play
        blackjackSurrenderGame.dealerHidden = true;
        renderHandsElite();
        checkInitialConditions();
    }
}

function enablePlayerActions() {
    const currentHand = blackjackSurrenderGame.playerHands[blackjackSurrenderGame.currentHandIndex];
    const handTotal = calculateHandValueElite(currentHand);
    
    // Basic actions
    document.getElementById('hitElite').disabled = false;
    document.getElementById('standElite').disabled = false;
    
    // Double down (only on first two cards)
    document.getElementById('doubleElite').disabled = currentHand.length !== 2 || balance < blackjackSurrenderGame.betAmount;
    
    // Split (only pairs on first two cards)
    const canSplit = currentHand.length === 2 && 
                    getCardValue(currentHand[0]) === getCardValue(currentHand[1]) &&
                    blackjackSurrenderGame.splits < blackjackSurrenderGame.maxSplits &&
                    balance >= blackjackSurrenderGame.betAmount;
    document.getElementById('splitElite').disabled = !canSplit;
    
    // Surrender (only on first two cards of first hand)
    const canSurrender = currentHand.length === 2 && 
                        blackjackSurrenderGame.currentHandIndex === 0 &&
                        blackjackSurrenderGame.surrenderAllowed;
    document.getElementById('surrenderElite').disabled = !canSurrender;
    
    document.getElementById('gamePhaseElite').textContent = 'Your Turn';
    document.getElementById('gameStatusElite').textContent = `Hand ${blackjackSurrenderGame.currentHandIndex + 1} - Choose your action`;
    
    updateHandInfo();
}

function getCardValue(card) {
    if (card.rank === 'A') return 1;
    if (['J', 'Q', 'K'].includes(card.rank)) return 10;
    return parseInt(card.rank);
}

function hitElite() {
    dealCardElite('player', blackjackSurrenderGame.currentHandIndex);
    
    const currentHand = blackjackSurrenderGame.playerHands[blackjackSurrenderGame.currentHandIndex];
    const handTotal = calculateHandValueElite(currentHand);
    
    if (handTotal > 21) {
        // Bust
        nextHandOrDealer();
    } else if (handTotal === 21) {
        // 21 - automatically stand
        standElite();
    } else {
        // Continue playing
        enablePlayerActions();
        updateStrategyRecommendation();
    }
}

function standElite() {
    nextHandOrDealer();
}

function doubleElite() {
    if (balance < blackjackSurrenderGame.betAmount) {
        document.getElementById('strategyRecommendation').textContent = 'Insufficient balance to double down!';
        return;
    }
    
    balance -= blackjackSurrenderGame.betAmount;
    updateBalance();
    
    // Deal one card and stand
    dealCardElite('player', blackjackSurrenderGame.currentHandIndex);
    
    const currentHand = blackjackSurrenderGame.playerHands[blackjackSurrenderGame.currentHandIndex];
    const handTotal = calculateHandValueElite(currentHand);
    
    if (handTotal > 21) {
        nextHandOrDealer();
    } else {
        nextHandOrDealer();
    }
}

function splitElite() {
    if (balance < blackjackSurrenderGame.betAmount) {
        document.getElementById('strategyRecommendation').textContent = 'Insufficient balance to split!';
        return;
    }
    
    balance -= blackjackSurrenderGame.betAmount;
    updateBalance();
    
    const currentHand = blackjackSurrenderGame.playerHands[blackjackSurrenderGame.currentHandIndex];
    const splitCard = currentHand.pop();
    
    // Create new hand with the split card
    blackjackSurrenderGame.playerHands.push([splitCard]);
    blackjackSurrenderGame.splits++;
    
    // Deal new cards to both hands
    dealCardElite('player', blackjackSurrenderGame.currentHandIndex);
    dealCardElite('player', blackjackSurrenderGame.playerHands.length - 1);
    
    enablePlayerActions();
    updateStrategyRecommendation();
}

function surrenderElite() {
    // Return half the bet
    const surrenderAmount = Math.floor(blackjackSurrenderGame.betAmount * 0.5);
    balance += surrenderAmount;
    updateBalance();
    
    blackjackSurrenderGame.sessionStats.handsSurrendered++;
    blackjackSurrenderGame.sessionStats.surrenderSavings += surrenderAmount;
    
    endHandElite('surrender', -Math.floor(blackjackSurrenderGame.betAmount * 0.5));
}

function nextHandOrDealer() {
    blackjackSurrenderGame.currentHandIndex++;
    
    if (blackjackSurrenderGame.currentHandIndex < blackjackSurrenderGame.playerHands.length) {
        // Move to next hand
        enablePlayerActions();
        updateStrategyRecommendation();
    } else {
        // All hands complete - dealer plays
        playDealerElite();
    }
}

function playDealerElite() {
    blackjackSurrenderGame.dealerHidden = false;
    renderHandsElite();
    
    document.getElementById('gamePhaseElite').textContent = 'Dealer Playing';
    document.getElementById('gameStatusElite').textContent = 'Dealer must hit on 16 and stand on 17';
    
    // Disable all player actions
    document.querySelectorAll('.action-btn').forEach(btn => btn.disabled = true);
    
    const dealerPlay = () => {
        const dealerTotal = calculateHandValueElite(blackjackSurrenderGame.dealerCards);
        
        if (dealerTotal < 17) {
            setTimeout(() => {
                dealCardElite('dealer');
                dealerPlay();
            }, 1000);
        } else {
            // Dealer finished
            setTimeout(() => determineWinnersElite(), 1000);
        }
    };
    
    dealerPlay();
}

function determineWinnersElite() {
    const dealerTotal = calculateHandValueElite(blackjackSurrenderGame.dealerCards);
    let totalWinnings = 0;
    let results = [];
    
    // Check side bets first
    let sideBetWinnings = 0;
    Object.entries(blackjackSurrenderGame.sideBets).forEach(([type, bet]) => {
        if (bet.active) {
            // Simple side bet logic - can be expanded
            if (type === 'perfectPairs' && blackjackSurrenderGame.playerHands[0].length >= 2) {
                const firstTwo = blackjackSurrenderGame.playerHands[0].slice(0, 2);
                if (firstTwo[0].rank === firstTwo[1].rank) {
                    sideBetWinnings += bet.amount * bet.odds;
                }
            }
        }
    });
    
    blackjackSurrenderGame.playerHands.forEach((hand, index) => {
        const playerTotal = calculateHandValueElite(hand);
        let result = '';
        let winnings = 0;
        
        if (playerTotal > 21) {
            result = 'Bust';
            winnings = 0;
        } else if (dealerTotal > 21) {
            result = 'Win (Dealer Bust)';
            winnings = blackjackSurrenderGame.betAmount * 2;
        } else if (playerTotal > dealerTotal) {
            result = 'Win';
            winnings = blackjackSurrenderGame.betAmount * 2;
        } else if (playerTotal === dealerTotal) {
            result = 'Push';
            winnings = blackjackSurrenderGame.betAmount;
        } else {
            result = 'Lose';
            winnings = 0;
        }
        
        results.push({ hand: index + 1, result, winnings });
        totalWinnings += winnings;
    });
    
    totalWinnings += sideBetWinnings;
    balance += totalWinnings;
    updateBalance();
    
    // Update session stats
    blackjackSurrenderGame.sessionStats.totalWon += totalWinnings;
    blackjackSurrenderGame.sessionStats.biggestWin = Math.max(
        blackjackSurrenderGame.sessionStats.biggestWin, 
        totalWinnings - blackjackSurrenderGame.betAmount
    );
    
    updateSessionStatsElite();
    showResultsElite(results, sideBetWinnings, totalWinnings);
}

function calculateSideBetWinnings() {
    let winnings = 0;
    const playerHand = blackjackSurrenderGame.playerHands[0];
    const dealerUpCard = blackjackSurrenderGame.dealerCards[0];
    
    // Perfect Pairs
    if (blackjackSurrenderGame.sideBets.perfectPairs.active) {
        if (playerHand.length >= 2 && playerHand[0].rank === playerHand[1].rank) {
            let multiplier = 6; // Mixed pair
            if (playerHand[0].suit === playerHand[1].suit) {
                multiplier = 25; // Perfect pair
            } else if ((playerHand[0].suit === '♠' || playerHand[0].suit === '♣') === 
                      (playerHand[1].suit === '♠' || playerHand[1].suit === '♣')) {
                multiplier = 12; // Colored pair
            }
            winnings += blackjackSurrenderGame.sideBets.perfectPairs.amount * (multiplier + 1);
        }
    }
    
    // 21+3 (Three card poker with player's first two cards and dealer's up card)
    if (blackjackSurrenderGame.sideBets.twentyOnePlusThree.active && playerHand.length >= 2) {
        const threeCards = [playerHand[0], playerHand[1], dealerUpCard];
        const multiplier = evaluate21Plus3(threeCards);
        if (multiplier > 0) {
            winnings += blackjackSurrenderGame.sideBets.twentyOnePlusThree.amount * (multiplier + 1);
        }
    }
    
    // Lucky Ladies (Player's first two cards total 20)
    if (blackjackSurrenderGame.sideBets.luckyLadies.active && playerHand.length >= 2) {
        const total = calculateHandValueElite([playerHand[0], playerHand[1]]);
        if (total === 20) {
            let multiplier = 4; // Any 20
            if (playerHand[0].rank === 'Q' && playerHand[1].rank === 'Q') {
                if (playerHand[0].suit === playerHand[1].suit) {
                    multiplier = 1000; // Matched Queen of Hearts
                } else {
                    multiplier = 125; // Matched Queens
                }
            }
            winnings += blackjackSurrenderGame.sideBets.luckyLadies.amount * (multiplier + 1);
        }
    }
    
    return winnings;
}

function evaluate21Plus3(cards) {
    // Simplified 21+3 evaluation
    const ranks = cards.map(c => c.rank);
    const suits = cards.map(c => c.suit);
    
    // Straight flush
    if (suits.every(s => s === suits[0]) && isSequential(ranks)) return 40;
    
    // Three of a kind
    if (ranks.every(r => r === ranks[0])) return 30;
    
    // Straight
    if (isSequential(ranks)) return 10;
    
    // Flush
    if (suits.every(s => s === suits[0])) return 5;
    
    return 0;
}

function isSequential(ranks) {
    const values = ranks.map(r => {
        if (r === 'A') return 1;
        if (r === 'J') return 11;
        if (r === 'Q') return 12;
        if (r === 'K') return 13;
        return parseInt(r);
    }).sort((a, b) => a - b);
    
    return values[2] - values[0] === 2 && values[1] - values[0] === 1;
}

function updateSessionStatsElite(results, totalWinnings) {
    blackjackSurrenderGame.sessionStats.handsPlayed++;
    
    if (results.some(r => r.result.includes('Win'))) {
        blackjackSurrenderGame.sessionStats.handsWon++;
    }
    
    blackjackSurrenderGame.sessionStats.totalWagered += blackjackSurrenderGame.betAmount * blackjackSurrenderGame.playerHands.length;
    blackjackSurrenderGame.sessionStats.totalWon += totalWinnings;
    blackjackSurrenderGame.sessionStats.biggestWin = Math.max(blackjackSurrenderGame.sessionStats.biggestWin, totalWinnings);
    
    // Update display
    document.getElementById('handsPlayedElite').textContent = blackjackSurrenderGame.sessionStats.handsPlayed;
    document.getElementById('handsWonElite').textContent = blackjackSurrenderGame.sessionStats.handsWon;
    document.getElementById('handsSurrenderedElite').textContent = blackjackSurrenderGame.sessionStats.handsSurrendered;
    document.getElementById('blackjacksElite').textContent = blackjackSurrenderGame.sessionStats.blackjacks;
    document.getElementById('totalWageredElite').textContent = blackjackSurrenderGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('totalWonElite').textContent = blackjackSurrenderGame.sessionStats.totalWon.toLocaleString();
    document.getElementById('surrenderSavingsElite').textContent = blackjackSurrenderGame.sessionStats.surrenderSavings.toLocaleString();
    
    const accuracy = blackjackSurrenderGame.sessionStats.handsPlayed > 0 ? 
        Math.round((blackjackSurrenderGame.sessionStats.perfectDecisions / blackjackSurrenderGame.sessionStats.handsPlayed) * 100) : 100;
    document.getElementById('strategyAccuracyElite').textContent = accuracy + '%';
}

function showResultsElite(results, sideBetWinnings, totalWinnings) {
    const resultText = results.map(r => `Hand ${r.hand}: ${r.result}`).join('<br>');
    
    document.getElementById('gamePhaseElite').textContent = 'Hand Complete';
    document.getElementById('gameStatusElite').innerHTML = resultText;
    
    setTimeout(() => {
        document.getElementById('finalResultElite').innerHTML = resultText;
        document.getElementById('finalWinAmountElite').innerHTML = 
            totalWinnings > 0 ? `<span class="text-green-400">+${totalWinnings.toLocaleString()} GA</span>` : 
                               `<span class="text-red-400">No winnings this hand</span>`;
        
        document.getElementById('strategyFeedbackElite').textContent = getStrategyFeedback();
        document.getElementById('blackjackOverlayElite').classList.remove('hidden');
    }, 3000);
}

function getStrategyFeedback() {
    const handsPlayed = blackjackSurrenderGame.sessionStats.handsPlayed;
    const winRate = handsPlayed > 0 ? (blackjackSurrenderGame.sessionStats.handsWon / handsPlayed * 100).toFixed(1) : 0;
    
    if (handsPlayed < 5) {
        return 'Keep playing to get strategy feedback!';
    } else if (winRate > 60) {
        return 'Excellent play! You\'re following basic strategy well.';
    } else if (winRate > 45) {
        return 'Good strategy! Consider reviewing surrender scenarios.';
    } else {
        return 'Review basic strategy chart for better results.';
    }
}

function updateStrategyRecommendation() {
    if (!document.getElementById('showRecommendations').checked) {
        document.getElementById('strategyRecommendation').textContent = 'Strategy hints disabled';
        return;
    }
    
    const currentHand = blackjackSurrenderGame.playerHands[blackjackSurrenderGame.currentHandIndex];
    const dealerUpCard = blackjackSurrenderGame.dealerCards[0];
    const recommendation = getBasicStrategyRecommendation(currentHand, dealerUpCard);
    
    document.getElementById('strategyRecommendation').textContent = recommendation;
}

function getBasicStrategyRecommendation(hand, dealerUpCard) {
    const total = calculateHandValueElite(hand);
    const dealerValue = getCardValue(dealerUpCard);
    const dealerIndex = Math.min(dealerValue - 2, 9);
    
    // Check for pairs
    if (hand.length === 2 && getCardValue(hand[0]) === getCardValue(hand[1])) {
        const pairValue = hand[0].rank;
        if (BASIC_STRATEGY.pairs[pairValue]) {
            const action = BASIC_STRATEGY.pairs[pairValue][dealerIndex];
            if (action === 'Y') return 'Recommendation: SPLIT';
        }
    }
    
    // Check for soft hands
    const hasAce = hand.some(card => card.rank === 'A');
    if (hasAce && total <= 21) {
        const softTotal = total;
        if (BASIC_STRATEGY.soft[softTotal]) {
            const action = BASIC_STRATEGY.soft[softTotal][dealerIndex];
            return `Recommendation: ${getActionText(action)}`;
        }
    }
    
    // Hard hands
    if (BASIC_STRATEGY.hard[total]) {
        const action = BASIC_STRATEGY.hard[total][dealerIndex];
        return `Recommendation: ${getActionText(action)}`;
    }
    
    return total >= 17 ? 'Recommendation: STAND' : 'Recommendation: HIT';
}

function getActionText(action) {
    switch (action) {
        case 'H': return 'HIT';
        case 'S': return 'STAND';
        case 'D': return 'DOUBLE';
        case 'R': return 'SURRENDER';
        case 'Y': return 'SPLIT';
        case 'N': return 'DON\'T SPLIT';
        default: return action;
    }
}

function updateHandInfo() {
    const currentHand = blackjackSurrenderGame.playerHands[blackjackSurrenderGame.currentHandIndex];
    
    document.getElementById('currentHandNumber').textContent = blackjackSurrenderGame.playerHands.length;
    document.getElementById('activeHandIndex').textContent = blackjackSurrenderGame.currentHandIndex + 1;
    document.getElementById('currentSplits').textContent = blackjackSurrenderGame.splits;
    
    const canSplit = currentHand.length === 2 && 
                    getCardValue(currentHand[0]) === getCardValue(currentHand[1]) &&
                    blackjackSurrenderGame.splits < blackjackSurrenderGame.maxSplits;
    document.getElementById('canSplitDisplay').textContent = canSplit ? 'Yes' : 'No';
    
    const canDouble = currentHand.length === 2 && balance >= blackjackSurrenderGame.betAmount;
    document.getElementById('canDoubleDisplay').textContent = canDouble ? 'Yes' : 'No';
    
    const canSurrender = currentHand.length === 2 && blackjackSurrenderGame.currentHandIndex === 0;
    document.getElementById('canSurrenderDisplay').textContent = canSurrender ? 'Yes' : 'No';
}

function toggleCardCounting() {
    const enabled = document.getElementById('cardCountingDisplay').checked;
    blackjackSurrenderGame.cardCounting = enabled;
    
    if (enabled) {
        document.getElementById('cardCountingPanel').classList.remove('hidden');
        updateCardCountingDisplay();
    } else {
        document.getElementById('cardCountingPanel').classList.add('hidden');
    }
}

function updateCardCountingDisplay() {
    if (!blackjackSurrenderGame.cardCounting) return;
    
    document.getElementById('runningCount').textContent = blackjackSurrenderGame.runningCount;
    
    const decksRemaining = Math.max(1, (blackjackSurrenderGame.deck.length / 52));
    const trueCount = Math.round(blackjackSurrenderGame.runningCount / decksRemaining * 10) / 10;
    blackjackSurrenderGame.trueCount = trueCount;
    
    document.getElementById('trueCount').textContent = trueCount;
    document.getElementById('cardsDealt').textContent = blackjackSurrenderGame.cardsDealt;
    
    const advantage = Math.max(0, (trueCount - 1) * 0.5);
    document.getElementById('playerAdvantage').textContent = advantage.toFixed(1) + '%';
}

function endHandElite(result, winnings) {
    blackjackSurrenderGame.isPlaying = false;
    
    // Update balance
    balance += winnings;
    updateBalance();
    
    // Update statistics
    blackjackSurrenderGame.sessionStats.handsPlayed++;
    if (winnings > 0) {
        blackjackSurrenderGame.sessionStats.handsWon++;
    }
    if (result === 'blackjack') {
        blackjackSurrenderGame.sessionStats.blackjacks++;
    }
    
    blackjackSurrenderGame.sessionStats.totalWagered += blackjackSurrenderGame.betAmount;
    blackjackSurrenderGame.sessionStats.totalWon += winnings;
    blackjackSurrenderGame.sessionStats.biggestWin = Math.max(blackjackSurrenderGame.sessionStats.biggestWin, winnings);
    
    updateSessionStatsElite();
    
    // Show result
    let resultText = '';
    switch (result) {
        case 'blackjack':
            resultText = '🎉 BLACKJACK! 🎉';
            break;
        case 'push':
            resultText = '🤝 PUSH 🤝';
            break;
        case 'surrender':
            resultText = '🏳️ SURRENDERED 🏳️';
            break;
        case 'dealer_blackjack':
            resultText = '💔 DEALER BLACKJACK 💔';
            break;
        default:
            resultText = result;
    }
    
    setTimeout(() => {
        document.getElementById('finalResultElite').innerHTML = `<span class="${winnings > 0 ? 'text-green-400' : 'text-red-400'}">${resultText}</span>`;
        document.getElementById('finalWinAmountElite').innerHTML = 
            winnings > 0 ? `<span class="text-green-400">+${winnings.toLocaleString()} GA</span>` : 
                          `<span class="text-red-400">-${blackjackSurrenderGame.betAmount.toLocaleString()} GA</span>`;
        
        document.getElementById('strategyFeedbackElite').textContent = getStrategyFeedback();
        document.getElementById('blackjackOverlayElite').classList.remove('hidden');
    }, 2000);
}

function hideBlackjackOverlayElite() {
    document.getElementById('blackjackOverlayElite').classList.add('hidden');
    resetBlackjackEliteGame();
}

function resetBlackjackEliteGame() {
    blackjackSurrenderGame.isPlaying = false;
    blackjackSurrenderGame.gamePhase = 'betting';
    blackjackSurrenderGame.betAmount = 0;
    blackjackSurrenderGame.playerHands = [[]];
    blackjackSurrenderGame.dealerCards = [];
    blackjackSurrenderGame.currentHandIndex = 0;
    blackjackSurrenderGame.dealerHidden = true;
    blackjackSurrenderGame.splits = 0;
    
    // Reset side bets
    Object.keys(blackjackSurrenderGame.sideBets).forEach(key => {
        blackjackSurrenderGame.sideBets[key].active = false;
        blackjackSurrenderGame.sideBets[key].amount = 0;
    });
    
    // Reset UI
    document.getElementById('dealBlackjackElite').disabled = false;
    document.querySelectorAll('.action-btn').forEach(btn => btn.disabled = true);
    document.querySelectorAll('.side-bet-btn').forEach(btn => {
        btn.classList.remove('ring-2', 'ring-yellow-400');
    });
    document.getElementById('insurancePanel').classList.add('hidden');
    
    document.getElementById('gamePhaseElite').textContent = 'Place Your Bet';
    document.getElementById('gameStatusElite').textContent = 'Select bet amount and deal cards';
    document.getElementById('gameResultElite').innerHTML = '';
    document.getElementById('strategyRecommendation').textContent = 'Place your bet to begin';
    
    // Clear card displays
    document.getElementById('dealerCardsElite').innerHTML = '';
    document.getElementById('playerHandsElite').innerHTML = '';
    document.getElementById('dealerTotalElite').textContent = '-';
    
    updateEliteDisplay();
    updateHandInfo();
    
    // Add recent hand to history
    addToRecentHands();
}

function addToRecentHands() {
    const recentContainer = document.getElementById('recentHandsElite');
    const handResult = document.createElement('div');
    handResult.className = 'text-xs p-2 bg-black/50 rounded border-l-2 border-purple-500';
    
    const result = blackjackSurrenderGame.sessionStats.handsWon > 0 ? 'Win' : 'Loss';
    const color = result === 'Win' ? 'text-green-400' : 'text-red-400';
    
    handResult.innerHTML = `
        <div class="flex justify-between">
            <span>Hand ${blackjackSurrenderGame.sessionStats.handsPlayed}</span>
            <span class="${color}">${result}</span>
        </div>
        <div class="text-gray-400">${blackjackSurrenderGame.betAmount} GA</div>
    `;
    
    recentContainer.insertBefore(handResult, recentContainer.firstChild);
    
    // Keep only last 5 hands
    while (recentContainer.children.length > 5) {
        recentContainer.removeChild(recentContainer.lastChild);
    }
}

function resetShoe() {
    initializeDeckElite();
    document.getElementById('strategyRecommendation').textContent = 'New shoe shuffled - fresh start!';
}

function toggleDifficulty() {
    const difficulties = ['elite', 'professional', 'master'];
    const currentIndex = difficulties.indexOf(blackjackSurrenderGame.difficulty);
    const nextIndex = (currentIndex + 1) % difficulties.length;
    
    blackjackSurrenderGame.difficulty = difficulties[nextIndex];
    
    // Adjust settings based on difficulty
    switch (blackjackSurrenderGame.difficulty) {
        case 'elite':
            blackjackSurrenderGame.deckCount = 6;
            blackjackSurrenderGame.surrenderAllowed = true;
            break;
        case 'professional':
            blackjackSurrenderGame.deckCount = 8;
            blackjackSurrenderGame.surrenderAllowed = true;
            break;
        case 'master':
            blackjackSurrenderGame.deckCount = 8;
            blackjackSurrenderGame.surrenderAllowed = false;
            break;
    }
    
    initializeDeckElite();
    document.getElementById('strategyRecommendation').textContent = `Difficulty: ${blackjackSurrenderGame.difficulty.toUpperCase()}`;
}

function showStrategyChart() {
    const modal = document.getElementById('strategyChartModal');
    const content = document.getElementById('strategyChartContent');
    
    content.innerHTML = generateStrategyChartHTML();
    modal.classList.remove('hidden');
}

function hideStrategyChart() {
    document.getElementById('strategyChartModal').classList.add('hidden');
}

function generateStrategyChartHTML() {
    const dealerCards = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'A'];
    
    let html = '<div class="space-y-6">';
    
    // Hard totals
    html += '<div><h4 class="text-lg font-bold text-purple-400 mb-2">Hard Totals</h4>';
    html += '<table class="w-full text-xs border border-gray-600">';
    html += '<tr class="bg-gray-800"><th class="p-1 border border-gray-600">Player</th>';
    dealerCards.forEach(card => {
        html += `<th class="p-1 border border-gray-600">${card}</th>`;
    });
    html += '</tr>';
    
    for (let total = 5; total <= 21; total++) {
        if (BASIC_STRATEGY.hard[total]) {
            html += `<tr><td class="p-1 border border-gray-600 font-bold">${total}</td>`;
            BASIC_STRATEGY.hard[total].forEach(action => {
                const color = getActionColor(action);
                html += `<td class="p-1 border border-gray-600 ${color}">${action}</td>`;
            });
            html += '</tr>';
        }
    }
    html += '</table></div>';
    
    // Soft totals
    html += '<div><h4 class="text-lg font-bold text-purple-400 mb-2">Soft Totals</h4>';
    html += '<table class="w-full text-xs border border-gray-600">';
    html += '<tr class="bg-gray-800"><th class="p-1 border border-gray-600">Player</th>';
    dealerCards.forEach(card => {
        html += `<th class="p-1 border border-gray-600">${card}</th>`;
    });
    html += '</tr>';
    
    for (let total = 13; total <= 21; total++) {
        if (BASIC_STRATEGY.soft[total]) {
            html += `<tr><td class="p-1 border border-gray-600 font-bold">A,${total-11}</td>`;
            BASIC_STRATEGY.soft[total].forEach(action => {
                const color = getActionColor(action);
                html += `<td class="p-1 border border-gray-600 ${color}">${action}</td>`;
            });
            html += '</tr>';
        }
    }
    html += '</table></div>';
    
    // Pairs
    html += '<div><h4 class="text-lg font-bold text-purple-400 mb-2">Pairs</h4>';
    html += '<table class="w-full text-xs border border-gray-600">';
    html += '<tr class="bg-gray-800"><th class="p-1 border border-gray-600">Pair</th>';
    dealerCards.forEach(card => {
        html += `<th class="p-1 border border-gray-600">${card}</th>`;
    });
    html += '</tr>';
    
    Object.entries(BASIC_STRATEGY.pairs).forEach(([pair, actions]) => {
        html += `<tr><td class="p-1 border border-gray-600 font-bold">${pair},${pair}</td>`;
        actions.forEach(action => {
            const color = action === 'Y' ? 'bg-green-600' : 'bg-red-600';
            html += `<td class="p-1 border border-gray-600 ${color}">${action}</td>`;
        });
        html += '</tr>';
    });
    html += '</table></div>';
    
    html += '</div>';
    
    // Legend
    html += '<div class="mt-4 text-xs"><strong>Legend:</strong> H=Hit, S=Stand, D=Double, R=Surrender, Y=Split, N=Don\'t Split</div>';
    
    return html;
}

function getActionColor(action) {
    switch (action) {
        case 'H': return 'bg-blue-600';
        case 'S': return 'bg-red-600';
        case 'D': return 'bg-yellow-600';
        case 'R': return 'bg-orange-600';
        default: return 'bg-gray-600';
    }
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBlackjackSurrenderEliteGame();
});
