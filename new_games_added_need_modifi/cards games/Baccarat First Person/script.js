// Game state
let balance = 0; // Will be loaded from wallet API
let walletIntegration = null;

// Initialize wallet integration
function initializeWallet() {
    walletIntegration = new GameWalletIntegration('baccarat_fp');
    return walletIntegration.initialize();
}

// Update balance display (delegated to wallet integration)
function updateBalance() {
    if (walletIntegration) {
        balance = walletIntegration.balance;
        walletIntegration.updateBalanceDisplay();
    }
}

// Baccarat First Person game state
let baccaratFirstPersonGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    currentBet: null,
    
    // Enhanced first-person features
    viewMode: 'table',
    cameraAngle: 'center',
    zoomLevel: 1.0,
    immersiveMode: false,
    
    // Game mechanics
    deck: [],
    playerCards: [],
    bankerCards: [],
    playerTotal: 0,
    bankerTotal: 0,
    
    // First-person enhancements
    dealerPersonality: 'professional',
    tableTheme: 'classic',
    cardAnimations: true,
    soundEffects: true,
    
    // Advanced betting
    sideBets: {
        playerPair: { active: false, amount: 0, odds: 11 },
        bankerPair: { active: false, amount: 0, odds: 11 },
        perfectPair: { active: false, amount: 0, odds: 25 },
        bigSmall: { active: false, amount: 0, type: null, odds: 0.54 },
        anyPair: { active: false, amount: 0, odds: 5 }
    },
    
    // Statistics and tracking
    gameHistory: [],
    sessionStats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        currentStreak: 0,
        bestStreak: 0
    },
    
    // First-person immersion
    dealerComments: [],
    tableChatter: true,
    personalizedExperience: true,
    handGestures: true,
    
    // Advanced features
    roadmaps: {
        bigRoad: [],
        bigEyeRoad: [],
        smallRoad: [],
        cockroachRoad: []
    },
    
    // Betting patterns
    bettingStrategy: 'manual',
    autoPlay: false,
    quickBet: false,
    favoriteAmounts: [10, 25, 50, 100, 250]
};

// Dealer personalities and themes
const DEALER_PERSONALITIES = {
    professional: {
        name: 'Elena Rodriguez',
        greeting: 'Welcome to the Baccarat table. Good luck!',
        comments: [
            'Excellent choice!',
            'The cards are shuffled and ready.',
            'May fortune favor you today.',
            'A classic bet, well played.',
            'The tension builds...'
        ],
        style: 'formal'
    },
    friendly: {
        name: 'Marcus Chen',
        greeting: 'Hey there! Ready for some exciting Baccarat?',
        comments: [
            'Nice bet! I like your style.',
            'Ooh, this could be interesting!',
            'The cards are looking good today.',
            'You\'re on fire!',
            'Let\'s see what happens next!'
        ],
        style: 'casual'
    },
    mysterious: {
        name: 'Sophia Noir',
        greeting: 'The cards hold many secrets... shall we discover them?',
        comments: [
            'Intriguing choice...',
            'The fates are aligning...',
            'Mystery surrounds this hand.',
            'The cards whisper of fortune.',
            'Destiny awaits...'
        ],
        style: 'enigmatic'
    },
    veteran: {
        name: 'Giovanni Rossi',
        greeting: 'Ah, another player seeks the thrill of Baccarat.',
        comments: [
            'In my 30 years, I\'ve seen it all.',
            'The cards never lie, my friend.',
            'Experience teaches patience.',
            'A wise choice from a seasoned player.',
            'The game reveals character.'
        ],
        style: 'wise'
    }
};

const TABLE_THEMES = {
    classic: {
        name: 'Classic Casino',
        feltColor: '#0f5132',
        accentColor: '#ffd700',
        cardBack: 'traditional',
        ambiance: 'elegant'
    },
    modern: {
        name: 'Modern Luxury',
        feltColor: '#1a1a2e',
        accentColor: '#16213e',
        cardBack: 'sleek',
        ambiance: 'contemporary'
    },
    royal: {
        name: 'Royal Palace',
        feltColor: '#4a0e4e',
        accentColor: '#ffd700',
        cardBack: 'ornate',
        ambiance: 'regal'
    },
    neon: {
        name: 'Neon Nights',
        feltColor: '#0a0a0f',
        accentColor: '#9945ff',
        cardBack: 'cyber',
        ambiance: 'futuristic'
    }
};

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadBaccaratFirstPersonGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Game Controls & Settings -->
            <div class="lg:col-span-1">
                <!-- Betting Panel -->
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-4">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎯 FIRST PERSON BACCARAT 🎯</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 BET AMOUNT</label>
                        <div class="grid grid-cols-3 gap-2 mb-2">
                            <button onclick="setBetAmount(10)" class="quick-bet-btn bg-green-600/20 border border-green-500 text-green-400 py-1 px-2 rounded text-xs hover:bg-green-600/40">10</button>
                            <button onclick="setBetAmount(50)" class="quick-bet-btn bg-blue-600/20 border border-blue-500 text-blue-400 py-1 px-2 rounded text-xs hover:bg-blue-600/40">50</button>
                            <button onclick="setBetAmount(100)" class="quick-bet-btn bg-purple-600/20 border border-purple-500 text-purple-400 py-1 px-2 rounded text-xs hover:bg-purple-600/40">100</button>
                        </div>
                        <input type="number" id="baccaratFirstPersonBet" value="25" min="5" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <!-- Main Bets -->
                    <div class="mb-4">
                        <label class="block text-sm mb-3 text-gray-300">🎲 MAIN BETS</label>
                        <div class="space-y-2">
                            <button id="betPlayerFP" class="bet-option-fp w-full py-3 rounded-lg font-bold bg-blue-600/20 border-2 border-blue-500 text-blue-400 hover:bg-blue-600/40 transition-all">
                                👤 PLAYER (1:1)
                            </button>
                            <button id="betBankerFP" class="bet-option-fp w-full py-3 rounded-lg font-bold bg-red-600/20 border-2 border-red-500 text-red-400 hover:bg-red-600/40 transition-all">
                                🏛️ BANKER (0.95:1)
                            </button>
                            <button id="betTieFP" class="bet-option-fp w-full py-3 rounded-lg font-bold bg-purple-600/20 border-2 border-purple-500 text-purple-400 hover:bg-purple-600/40 transition-all">
                                🤝 TIE (8:1)
                            </button>
                        </div>
                    </div>

                    <!-- Side Bets -->
                    <div class="mb-4">
                        <label class="block text-sm mb-3 text-gray-300">🎰 SIDE BETS</label>
                        <div class="space-y-2">
                            <button id="betPlayerPair" class="side-bet-btn w-full py-2 rounded-lg font-bold bg-yellow-600/20 border border-yellow-500 text-yellow-400 hover:bg-yellow-600/40 transition-all text-sm">
                                👥 Player Pair (11:1)
                            </button>
                            <button id="betBankerPair" class="side-bet-btn w-full py-2 rounded-lg font-bold bg-orange-600/20 border border-orange-500 text-orange-400 hover:bg-orange-600/40 transition-all text-sm">
                                🏛️👥 Banker Pair (11:1)
                            </button>
                            <button id="betPerfectPair" class="side-bet-btn w-full py-2 rounded-lg font-bold bg-pink-600/20 border border-pink-500 text-pink-400 hover:bg-pink-600/40 transition-all text-sm">
                                💎 Perfect Pair (25:1)
                            </button>
                        </div>
                    </div>

                    <button id="dealBaccaratFP" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4" disabled>
                        🎴 DEAL CARDS 🎴
                    </button>

                    <!-- Current Bets Display -->
                    <div class="bg-black/50 p-4 rounded-lg">
                        <div class="text-center">
                            <div class="text-sm text-gray-400 mb-1">Main Bet</div>
                            <div id="baccaratMainBet" class="text-lg font-bold text-green-400">None</div>
                            <div class="text-sm text-gray-400 mb-1 mt-2">Side Bets</div>
                            <div id="baccaratSideBets" class="text-sm text-yellow-400">None</div>
                            <div class="text-sm text-gray-400 mb-1 mt-2">Total Wagered</div>
                            <div id="totalWagered" class="text-xl font-bold text-red-400">0 GA</div>
                        </div>
                    </div>
                </div>

                <!-- Dealer & Settings -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🎭 DEALER & SETTINGS</h5>
                    
                    <div class="mb-3">
                        <label class="block text-sm mb-2 text-gray-300">Dealer</label>
                        <select id="dealerPersonality" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white text-sm"
                                onchange="changeDealerPersonality()">
                            <option value="professional">Elena - Professional</option>
                            <option value="friendly">Marcus - Friendly</option>
                            <option value="mysterious">Sophia - Mysterious</option>
                            <option value="veteran">Giovanni - Veteran</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="block text-sm mb-2 text-gray-300">Table Theme</label>
                        <select id="tableTheme" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white text-sm"
                                onchange="changeTableTheme()">
                            <option value="classic">Classic Casino</option>
                            <option value="modern">Modern Luxury</option>
                            <option value="royal">Royal Palace</option>
                            <option value="neon" selected>Neon Nights</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <label class="flex items-center">
                            <input type="checkbox" id="cardAnimations" checked class="mr-2">
                            <span class="text-gray-300">Animations</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="soundEffects" checked class="mr-2">
                            <span class="text-gray-300">Sound FX</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="tableChatter" checked class="mr-2">
                            <span class="text-gray-300">Dealer Chat</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="immersiveMode" class="mr-2">
                            <span class="text-gray-300">Immersive</span>
                        </label>
                    </div>
                </div>

                <!-- Session Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 SESSION STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Hands:</span>
                            <span class="text-white" id="handsPlayed">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Won:</span>
                            <span class="text-green-400" id="handsWon">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Wagered:</span>
                            <span class="text-red-400" id="totalWageredStat">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Won:</span>
                            <span class="text-green-400" id="totalWonStat">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Streak:</span>
                            <span class="text-yellow-400" id="currentStreak">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Best:</span>
                            <span class="text-purple-400" id="bestStreak">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Game Area -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <!-- Dealer Area -->
                    <div class="text-center mb-6">
                        <div class="mb-4">
                            <div class="w-20 h-20 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full mx-auto mb-2 flex items-center justify-center text-2xl">
                                👩‍💼
                            </div>
                            <div class="text-lg font-bold text-purple-400" id="dealerName">Elena Rodriguez</div>
                            <div class="text-sm text-gray-400" id="dealerGreeting">Welcome to the Baccarat table. Good luck!</div>
                        </div>
                        
                        <!-- Dealer Comments -->
                        <div class="bg-black/50 p-3 rounded-lg border border-purple-500/30 mb-4">
                            <div id="dealerComment" class="text-cyan-400 italic text-sm min-h-[20px]">
                                Ready when you are...
                            </div>
                        </div>
                    </div>

                    <!-- Game Table -->
                    <div id="baccaratTableFP" class="relative bg-gradient-to-br from-green-900 to-emerald-900 rounded-lg p-6 h-80 mb-4">
                        <!-- Player Area -->
                        <div class="absolute top-4 left-4">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2 font-bold">👤 PLAYER</div>
                                <div id="playerCardsFP" class="flex space-x-2 mb-2 min-h-[64px]">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div id="playerTotalFP" class="text-2xl font-bold text-blue-400">-</div>
                            </div>
                        </div>
                        
                        <!-- Banker Area -->
                        <div class="absolute top-4 right-4">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2 font-bold">🏛️ BANKER</div>
                                <div id="bankerCardsFP" class="flex space-x-2 mb-2 min-h-[64px]">
                                    <!-- Banker cards will appear here -->
                                </div>
                                <div id="bankerTotalFP" class="text-2xl font-bold text-red-400">-</div>
                            </div>
                        </div>
                        
                        <!-- Center Info -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                            <div class="text-6xl mb-4">🎴</div>
                            <div id="gamePhaseFP" class="text-xl font-bold text-green-400">Place Your Bets</div>
                            <div id="gameTimer" class="text-lg text-yellow-400 mt-2"></div>
                        </div>
                        
                        <!-- Betting Areas Visual -->
                        <div class="absolute bottom-4 left-4 right-4">
                            <div class="grid grid-cols-3 gap-4 text-center text-xs">
                                <div class="py-2 px-4 bg-blue-600/20 border border-blue-500 rounded">
                                    👤 PLAYER<br>1:1
                                </div>
                                <div class="py-2 px-4 bg-purple-600/20 border border-purple-500 rounded">
                                    🤝 TIE<br>8:1
                                </div>
                                <div class="py-2 px-4 bg-red-600/20 border border-red-500 rounded">
                                    🏛️ BANKER<br>0.95:1
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Game Status -->
                    <div class="text-center">
                        <div id="baccaratStatusFP" class="text-lg font-semibold mb-2">Select your bet and deal the cards</div>
                        <div id="baccaratResultFP" class="text-xl font-bold"></div>
                    </div>
                </div>
            </div>

            <!-- Game History & Roadmaps -->
            <div class="lg:col-span-1">
                <!-- Recent Results -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📈 RECENT RESULTS</h5>
                    <div id="baccaratHistoryFP" class="grid grid-cols-6 gap-1">
                        <!-- Recent results will appear here -->
                    </div>
                </div>

                <!-- Big Road -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🛣️ BIG ROAD</h5>
                    <div id="bigRoad" class="grid grid-cols-6 gap-1 h-32 overflow-hidden">
                        <!-- Big road pattern will appear here -->
                    </div>
                </div>

                <!-- Betting Patterns -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mb-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🎯 QUICK ACTIONS</h5>
                    <div class="space-y-2">
                        <button onclick="repeatLastBet()" class="w-full cyber-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                            🔄 Repeat Last Bet
                        </button>
                        <button onclick="clearAllBets()" class="w-full cyber-button bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                            🗑️ Clear All Bets
                        </button>
                        <button onclick="toggleAutoPlay()" class="w-full cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg text-sm">
                            ⚡ Auto Play
                        </button>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 STATISTICS</h5>
                    <div class="text-xs space-y-1">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Player Wins:</span>
                            <span class="text-blue-400" id="playerWins">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Banker Wins:</span>
                            <span class="text-red-400" id="bankerWins">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tie Results:</span>
                            <span class="text-purple-400" id="tieResults">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Player %:</span>
                            <span class="text-blue-400" id="playerPercent">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Banker %:</span>
                            <span class="text-red-400" id="bankerPercent">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Complete Overlay -->
        <div id="baccaratOverlayFP" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🎴 HAND COMPLETE 🎴</h3>
                <div id="finalResultFP" class="text-xl mb-4"></div>
                <div id="finalWinAmountFP" class="text-lg mb-4"></div>
                <div id="dealerFinalComment" class="text-sm text-cyan-400 italic mb-6"></div>
                <button onclick="hideBaccaratOverlayFP()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    NEXT HAND
                </button>
            </div>
        </div>
    `;

    initializeBaccaratFirstPerson();
}

function initializeBaccaratFirstPerson() {
    // Initialize event listeners
    document.getElementById('betPlayerFP').addEventListener('click', () => placeBaccaratBetFP('player'));
    document.getElementById('betBankerFP').addEventListener('click', () => placeBaccaratBetFP('banker'));
    document.getElementById('betTieFP').addEventListener('click', () => placeBaccaratBetFP('tie'));
    
    // Side bet listeners
    document.getElementById('betPlayerPair').addEventListener('click', () => placeSideBet('playerPair'));
    document.getElementById('betBankerPair').addEventListener('click', () => placeSideBet('bankerPair'));
    document.getElementById('betPerfectPair').addEventListener('click', () => placeSideBet('perfectPair'));
    
    document.getElementById('dealBaccaratFP').addEventListener('click', dealBaccaratCardsFP);
    
    // Initialize deck and dealer
    initializeDeckFP();
    changeDealerPersonality();
    changeTableTheme();
    updateSessionStats();
}

function initializeDeckFP() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    
    baccaratFirstPersonGame.deck = [];
    for (const suit of suits) {
        for (const rank of ranks) {
            baccaratFirstPersonGame.deck.push({ rank, suit });
        }
    }
    
    // Shuffle deck
    for (let i = baccaratFirstPersonGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [baccaratFirstPersonGame.deck[i], baccaratFirstPersonGame.deck[j]] = [baccaratFirstPersonGame.deck[j], baccaratFirstPersonGame.deck[i]];
    }
}

function setBetAmount(amount) {
    document.getElementById('baccaratFirstPersonBet').value = amount;
    updateBetDisplay();
}

function placeBaccaratBetFP(betType) {
    if (baccaratFirstPersonGame.isPlaying) return;
    
    const betAmount = parseInt(document.getElementById('baccaratFirstPersonBet').value);
    
    if (betAmount > balance) {
        showDealerComment('Insufficient balance for that bet, my friend.');
        return;
    }
    
    // Reset previous bet selection
    document.querySelectorAll('.bet-option-fp').forEach(btn => {
        btn.classList.remove('ring-2', 'ring-green-400');
    });
    
    // Highlight selected bet
    let selectedButton;
    switch(betType) {
        case 'player':
            selectedButton = document.getElementById('betPlayerFP');
            break;
        case 'banker':
            selectedButton = document.getElementById('betBankerFP');
            break;
        case 'tie':
            selectedButton = document.getElementById('betTieFP');
            break;
    }
    
    selectedButton.classList.add('ring-2', 'ring-green-400');
    
    // Set bet
    baccaratFirstPersonGame.currentBet = betType;
    baccaratFirstPersonGame.betAmount = betAmount;
    
    updateBetDisplay();
    showDealerComment(getRandomDealerComment('bet'));
    
    document.getElementById('dealBaccaratFP').disabled = false;
    document.getElementById('baccaratStatusFP').textContent = 'Bet placed! Deal the cards to begin';
}

function placeSideBet(betType) {
    if (baccaratFirstPersonGame.isPlaying) return;
    
    const betAmount = parseInt(document.getElementById('baccaratFirstPersonBet').value);
    
    if (betAmount > balance) {
        showDealerComment('Not enough for that side bet.');
        return;
    }
    
    const sideBet = baccaratFirstPersonGame.sideBets[betType];
    
    if (sideBet.active) {
        // Remove side bet
        sideBet.active = false;
        sideBet.amount = 0;
        document.getElementById(`bet${betType.charAt(0).toUpperCase() + betType.slice(1)}`).classList.remove('ring-2', 'ring-yellow-400');
    } else {
        // Add side bet
        sideBet.active = true;
        sideBet.amount = betAmount;
        document.getElementById(`bet${betType.charAt(0).toUpperCase() + betType.slice(1)}`).classList.add('ring-2', 'ring-yellow-400');
    }
    
    updateBetDisplay();
    showDealerComment('Side bets add excitement to the game!');
}

function updateBetDisplay() {
    const mainBetText = baccaratFirstPersonGame.currentBet ? 
        `${baccaratFirstPersonGame.currentBet.charAt(0).toUpperCase() + baccaratFirstPersonGame.currentBet.slice(1)} - ${baccaratFirstPersonGame.betAmount} GA` : 
        'None';
    
    document.getElementById('baccaratMainBet').textContent = mainBetText;
    
    // Side bets display
    const activeSideBets = Object.entries(baccaratFirstPersonGame.sideBets)
        .filter(([_, bet]) => bet.active)
        .map(([type, bet]) => `${type}: ${bet.amount}`)
        .join(', ');
    
    document.getElementById('baccaratSideBets').textContent = activeSideBets || 'None';
    
    // Total wagered
    const totalWagered = baccaratFirstPersonGame.betAmount + 
        Object.values(baccaratFirstPersonGame.sideBets)
            .filter(bet => bet.active)
            .reduce((sum, bet) => sum + bet.amount, 0);
    
    document.getElementById('totalWagered').textContent = totalWagered + ' GA';
}

async function dealBaccaratCardsFP() {
    if (!baccaratFirstPersonGame.currentBet) return;

    // Calculate total bet
    const totalBet = baccaratFirstPersonGame.betAmount +
        Object.values(baccaratFirstPersonGame.sideBets)
            .filter(bet => bet.active)
            .reduce((sum, bet) => sum + bet.amount, 0);

    // Use wallet integration to process bet
    if (!walletIntegration || !(await walletIntegration.processBet(totalBet, {
        game_action: 'deal',
        bet_type: baccaratFirstPersonGame.currentBet,
        bet_amount: baccaratFirstPersonGame.betAmount,
        side_bets: baccaratFirstPersonGame.sideBets
    }))) {
        return;
    }

    // Update local balance reference
    balance = walletIntegration.balance;
    
    baccaratFirstPersonGame.isPlaying = true;
    document.getElementById('dealBaccaratFP').disabled = true;
    document.querySelectorAll('.bet-option-fp, .side-bet-btn').forEach(btn => btn.disabled = true);
    
    // Clear previous cards
    baccaratFirstPersonGame.playerCards = [];
    baccaratFirstPersonGame.bankerCards = [];
    document.getElementById('playerCardsFP').innerHTML = '';
    document.getElementById('bankerCardsFP').innerHTML = '';
    document.getElementById('playerTotalFP').textContent = '-';
    document.getElementById('bankerTotalFP').textContent = '-';
    
    document.getElementById('gamePhaseFP').textContent = 'Dealing Cards...';
    document.getElementById('baccaratStatusFP').textContent = 'Dealing initial cards...';
    showDealerComment('Here we go! Let the cards reveal their secrets...');
    
    // Deal initial cards with enhanced animation
    setTimeout(() => dealCardFP('player'), 500);
    setTimeout(() => dealCardFP('banker'), 1000);
    setTimeout(() => dealCardFP('player'), 1500);
    setTimeout(() => dealCardFP('banker'), 2000);
    
    // Check if third cards needed
    setTimeout(() => checkThirdCardsFP(), 2500);
}

function dealCardFP(recipient) {
    if (baccaratFirstPersonGame.deck.length === 0) {
        initializeDeckFP(); // Reshuffle if deck empty
    }
    
    const card = baccaratFirstPersonGame.deck.pop();
    
    if (recipient === 'player') {
        baccaratFirstPersonGame.playerCards.push(card);
        displayCardFP(card, 'playerCardsFP');
        baccaratFirstPersonGame.playerTotal = calculateBaccaratTotalFP(baccaratFirstPersonGame.playerCards);
        document.getElementById('playerTotalFP').textContent = baccaratFirstPersonGame.playerTotal;
    } else {
        baccaratFirstPersonGame.bankerCards.push(card);
        displayCardFP(card, 'bankerCardsFP');
        baccaratFirstPersonGame.bankerTotal = calculateBaccaratTotalFP(baccaratFirstPersonGame.bankerCards);
        document.getElementById('bankerTotalFP').textContent = baccaratFirstPersonGame.bankerTotal;
    }
}

function displayCardFP(card, containerId) {
    const cardElement = document.createElement('div');
    cardElement.className = 'card-fp w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold transform transition-all duration-500';
    
    const isRed = card.suit === '♥' || card.suit === '♦';
    cardElement.style.color = isRed ? '#dc2626' : '#000';
    
    cardElement.innerHTML = `
        <div>${card.rank}</div>
        <div>${card.suit}</div>
    `;
    
    // Add animation if enabled
    if (baccaratFirstPersonGame.cardAnimations) {
        cardElement.style.transform = 'scale(0) rotate(180deg)';
        setTimeout(() => {
            cardElement.style.transform = 'scale(1) rotate(0deg)';
        }, 100);
    }
    
    document.getElementById(containerId).appendChild(cardElement);
}

function calculateBaccaratTotalFP(cards) {
    let total = 0;
    
    for (const card of cards) {
        let value = 0;
        if (card.rank === 'A') {
            value = 1;
        } else if (['J', 'Q', 'K'].includes(card.rank)) {
            value = 0;
        } else {
            value = parseInt(card.rank) || 0;
        }
        total += value;
    }
    
    return total % 10; // Only the last digit counts
}

function checkThirdCardsFP() {
    document.getElementById('gamePhaseFP').textContent = 'Checking Rules...';
    showDealerComment('Let me check the drawing rules...');
    
    // Natural win check (8 or 9)
    if (baccaratFirstPersonGame.playerTotal >= 8 || baccaratFirstPersonGame.bankerTotal >= 8) {
        setTimeout(() => determineBaccaratWinnerFP(), 1000);
        return;
    }
    
    let playerThirdCard = null;
    
    // Player third card rule
    if (baccaratFirstPersonGame.playerTotal <= 5) {
        setTimeout(() => {
            dealCardFP('player');
            playerThirdCard = baccaratFirstPersonGame.playerCards[2];
            document.getElementById('baccaratStatusFP').textContent = 'Player draws third card...';
            showDealerComment('Player draws a third card.');
            
            // Check banker third card after player
            setTimeout(() => checkBankerThirdCardFP(playerThirdCard), 1000);
        }, 1000);
    } else {
        // Check banker third card immediately
        setTimeout(() => checkBankerThirdCardFP(null), 1000);
    }
}

function checkBankerThirdCardFP(playerThirdCard) {
    const bankerShouldDraw = shouldBankerDrawFP(baccaratFirstPersonGame.bankerTotal, playerThirdCard);
    
    if (bankerShouldDraw) {
        setTimeout(() => {
            dealCardFP('banker');
            document.getElementById('baccaratStatusFP').textContent = 'Banker draws third card...';
            showDealerComment('Banker must draw according to the rules.');
            setTimeout(() => determineBaccaratWinnerFP(), 1000);
        }, 500);
    } else {
        setTimeout(() => determineBaccaratWinnerFP(), 1000);
    }
}

function shouldBankerDrawFP(bankerTotal, playerThirdCard) {
    if (bankerTotal >= 7) return false;
    if (bankerTotal <= 2) return true;
    
    if (!playerThirdCard) {
        return bankerTotal <= 5;
    }
    
    const thirdCardValue = calculateBaccaratTotalFP([playerThirdCard]);
    
    switch (bankerTotal) {
        case 3: return thirdCardValue !== 8;
        case 4: return [2, 3, 4, 5, 6, 7].includes(thirdCardValue);
        case 5: return [4, 5, 6, 7].includes(thirdCardValue);
        case 6: return [6, 7].includes(thirdCardValue);
        default: return false;
    }
}

async function determineBaccaratWinnerFP() {
    document.getElementById('gamePhaseFP').textContent = 'Final Results';
    
    let winner;
    if (baccaratFirstPersonGame.playerTotal > baccaratFirstPersonGame.bankerTotal) {
        winner = 'player';
    } else if (baccaratFirstPersonGame.bankerTotal > baccaratFirstPersonGame.playerTotal) {
        winner = 'banker';
    } else {
        winner = 'tie';
    }
    
    // Calculate main bet winnings
    let mainWinnings = 0;
    let won = false;
    
    if (baccaratFirstPersonGame.currentBet === winner) {
        won = true;
        switch (winner) {
            case 'player':
                mainWinnings = baccaratFirstPersonGame.betAmount * 2;
                break;
            case 'banker':
                mainWinnings = Math.floor(baccaratFirstPersonGame.betAmount * 1.95);
                break;
            case 'tie':
                mainWinnings = baccaratFirstPersonGame.betAmount * 9;
                break;
        }
    }
    
    // Calculate side bet winnings
    let sideBetWinnings = 0;
    const playerPair = isPlayerPair();
    const bankerPair = isBankerPair();
    const perfectPair = isPerfectPair();
    
    if (baccaratFirstPersonGame.sideBets.playerPair.active && playerPair) {
        sideBetWinnings += baccaratFirstPersonGame.sideBets.playerPair.amount * 12;
    }
    if (baccaratFirstPersonGame.sideBets.bankerPair.active && bankerPair) {
        sideBetWinnings += baccaratFirstPersonGame.sideBets.bankerPair.amount * 12;
    }
    if (baccaratFirstPersonGame.sideBets.perfectPair.active && perfectPair) {
        sideBetWinnings += baccaratFirstPersonGame.sideBets.perfectPair.amount * 26;
    }
    
    const totalWinnings = mainWinnings + sideBetWinnings;

    // Process winnings through wallet integration
    if (totalWinnings > 0 && walletIntegration) {
        await walletIntegration.processWin(totalWinnings, {
            game_action: 'game_end',
            result: winner,
            bet_type: baccaratFirstPersonGame.currentBet,
            bet_amount: baccaratFirstPersonGame.betAmount,
            win_amount: totalWinnings,
            main_winnings: mainWinnings,
            side_bet_winnings: sideBetWinnings
        });
        balance = walletIntegration.balance;
    }
    
    // Update session stats
    baccaratFirstPersonGame.sessionStats.handsPlayed++;
    if (won || sideBetWinnings > 0) {
        baccaratFirstPersonGame.sessionStats.handsWon++;
        baccaratFirstPersonGame.sessionStats.currentStreak++;
        baccaratFirstPersonGame.sessionStats.bestStreak = Math.max(
            baccaratFirstPersonGame.sessionStats.bestStreak, 
            baccaratFirstPersonGame.sessionStats.currentStreak
        );
    } else {
        baccaratFirstPersonGame.sessionStats.currentStreak = 0;
    }
    
    baccaratFirstPersonGame.sessionStats.totalWagered += baccaratFirstPersonGame.betAmount + 
        Object.values(baccaratFirstPersonGame.sideBets)
            .filter(bet => bet.active)
            .reduce((sum, bet) => sum + bet.amount, 0);
    
    baccaratFirstPersonGame.sessionStats.totalWon += totalWinnings;
    baccaratFirstPersonGame.sessionStats.biggestWin = Math.max(
        baccaratFirstPersonGame.sessionStats.biggestWin, 
        totalWinnings
    );
    
    // Add to history
    addBaccaratHistoryFP(winner);
    updateRoadmaps(winner);
    updateSessionStats();
    
    // Show results
    const winnerText = winner.charAt(0).toUpperCase() + winner.slice(1);
    if (totalWinnings > 0) {
        document.getElementById('baccaratStatusFP').innerHTML = 
            `<span class="text-green-400 neon-glow">${winnerText} wins! You won ${totalWinnings.toLocaleString()} GA</span>`;
        showDealerComment(getRandomDealerComment('win'));
    } else {
        document.getElementById('baccaratStatusFP').innerHTML = 
            `<span class="text-red-400">${winnerText} wins! Better luck next hand.</span>`;
        showDealerComment(getRandomDealerComment('lose'));
    }
    
    // Show overlay after delay
    setTimeout(() => {
        showBaccaratOverlayFP(winner, totalWinnings, won || sideBetWinnings > 0);
    }, 3000);
}

function isPlayerPair() {
    return baccaratFirstPersonGame.playerCards.length >= 2 && 
           baccaratFirstPersonGame.playerCards[0].rank === baccaratFirstPersonGame.playerCards[1].rank;
}

function isBankerPair() {
    return baccaratFirstPersonGame.bankerCards.length >= 2 && 
           baccaratFirstPersonGame.bankerCards[0].rank === baccaratFirstPersonGame.bankerCards[1].rank;
}

function isPerfectPair() {
    return (isPlayerPair() && 
            baccaratFirstPersonGame.playerCards[0].suit === baccaratFirstPersonGame.playerCards[1].suit) ||
           (isBankerPair() && 
            baccaratFirstPersonGame.bankerCards[0].suit === baccaratFirstPersonGame.bankerCards[1].suit);
}

function addBaccaratHistoryFP(winner) {
    const history = document.getElementById('baccaratHistoryFP');
    const result = document.createElement('div');
    
    let color = 'bg-blue-600';
    let icon = '👤';
    if (winner === 'banker') {
        color = 'bg-red-600';
        icon = '🏛️';
    }
    if (winner === 'tie') {
        color = 'bg-purple-600';
        icon = '🤝';
    }
    
    result.className = `w-8 h-8 ${color} rounded-full flex items-center justify-center text-white text-xs font-bold`;
    result.textContent = icon;
    
    history.insertBefore(result, history.firstChild);
    
    // Keep only last 30 results
    while (history.children.length > 30) {
        history.removeChild(history.lastChild);
    }
    
    // Update statistics
    updateGameStatistics(winner);
}

function updateRoadmaps(winner) {
    // Add to big road
    baccaratFirstPersonGame.roadmaps.bigRoad.push(winner);
    
    const bigRoad = document.getElementById('bigRoad');
    const roadElement = document.createElement('div');
    
    let color = 'bg-blue-500';
    if (winner === 'banker') color = 'bg-red-500';
    if (winner === 'tie') color = 'bg-green-500';
    
    roadElement.className = `w-4 h-4 ${color} rounded-sm`;
    bigRoad.appendChild(roadElement);
    
    // Keep only last 36 results
    while (bigRoad.children.length > 36) {
        bigRoad.removeChild(bigRoad.firstChild);
    }
}

function updateGameStatistics(winner) {
    const history = baccaratFirstPersonGame.gameHistory;
    history.push(winner);
    
    const playerWins = history.filter(w => w === 'player').length;
    const bankerWins = history.filter(w => w === 'banker').length;
    const tieResults = history.filter(w => w === 'tie').length;
    const total = history.length;
    
    document.getElementById('playerWins').textContent = playerWins;
    document.getElementById('bankerWins').textContent = bankerWins;
    document.getElementById('tieResults').textContent = tieResults;
    
    if (total > 0) {
        document.getElementById('playerPercent').textContent = Math.round((playerWins / total) * 100) + '%';
        document.getElementById('bankerPercent').textContent = Math.round((bankerWins / total) * 100) + '%';
    }
}

function updateSessionStats() {
    const stats = baccaratFirstPersonGame.sessionStats;
    
    document.getElementById('handsPlayed').textContent = stats.handsPlayed;
    document.getElementById('handsWon').textContent = stats.handsWon;
    document.getElementById('totalWageredStat').textContent = stats.totalWagered.toLocaleString();
    document.getElementById('totalWonStat').textContent = stats.totalWon.toLocaleString();
    document.getElementById('currentStreak').textContent = stats.currentStreak;
    document.getElementById('bestStreak').textContent = stats.bestStreak;
}

function changeDealerPersonality() {
    const personality = document.getElementById('dealerPersonality').value;
    const dealer = DEALER_PERSONALITIES[personality];
    
    baccaratFirstPersonGame.dealerPersonality = personality;
    
    document.getElementById('dealerName').textContent = dealer.name;
    document.getElementById('dealerGreeting').textContent = dealer.greeting;
    
    showDealerComment(dealer.greeting);
}

function changeTableTheme() {
    const theme = document.getElementById('tableTheme').value;
    const themeData = TABLE_THEMES[theme];
    
    baccaratFirstPersonGame.tableTheme = theme;
    
    const table = document.getElementById('baccaratTableFP');
    table.style.background = `linear-gradient(135deg, ${themeData.feltColor}, ${themeData.accentColor})`;
}

function showDealerComment(comment) {
    if (!document.getElementById('tableChatter').checked) return;
    
    const commentElement = document.getElementById('dealerComment');
    commentElement.textContent = comment;
    
    // Add typing animation
    commentElement.classList.add('animate-pulse');
    setTimeout(() => {
        commentElement.classList.remove('animate-pulse');
    }, 2000);
}

function getRandomDealerComment(type) {
    const dealer = DEALER_PERSONALITIES[baccaratFirstPersonGame.dealerPersonality];
    
    switch (type) {
        case 'bet':
            return dealer.comments[Math.floor(Math.random() * dealer.comments.length)];
        case 'win':
            return ['Congratulations!', 'Well played!', 'Fortune smiles upon you!', 'Excellent!'][Math.floor(Math.random() * 4)];
        case 'lose':
            return ['Better luck next time.', 'The cards can be unpredictable.', 'Another hand awaits.', 'Stay focused.'][Math.floor(Math.random() * 4)];
        default:
            return dealer.comments[0];
    }
}

function showBaccaratOverlayFP(winner, winnings, won) {
    document.getElementById('baccaratOverlayFP').classList.remove('hidden');
    
    const winnerText = winner.charAt(0).toUpperCase() + winner.slice(1);
    document.getElementById('finalResultFP').innerHTML = 
        won ? `<span class="text-green-400">🎉 ${winnerText} Wins! 🎉</span>` : 
              `<span class="text-red-400">💔 ${winnerText} Wins 💔</span>`;
    
    document.getElementById('finalWinAmountFP').innerHTML = 
        won ? `<span class="text-green-400">+${winnings.toLocaleString()} GA</span>` : 
              `<span class="text-red-400">No winnings this hand</span>`;
    
    document.getElementById('dealerFinalComment').textContent = 
        won ? getRandomDealerComment('win') : getRandomDealerComment('lose');
}

function hideBaccaratOverlayFP() {
    document.getElementById('baccaratOverlayFP').classList.add('hidden');
    resetBaccaratGameFP();
}

function resetBaccaratGameFP() {
    baccaratFirstPersonGame.isPlaying = false;
    baccaratFirstPersonGame.currentBet = null;
    baccaratFirstPersonGame.betAmount = 0;
    
    // Reset side bets
    Object.keys(baccaratFirstPersonGame.sideBets).forEach(key => {
        baccaratFirstPersonGame.sideBets[key].active = false;
        baccaratFirstPersonGame.sideBets[key].amount = 0;
    });
    
    // Reset UI
    document.getElementById('dealBaccaratFP').disabled = true;
    document.querySelectorAll('.bet-option-fp, .side-bet-btn').forEach(btn => {
        btn.disabled = false;
        btn.classList.remove('ring-2', 'ring-green-400', 'ring-yellow-400');
    });
    
    updateBetDisplay();
    document.getElementById('gamePhaseFP').textContent = 'Place Your Bets';
    document.getElementById('baccaratStatusFP').textContent = 'Select your bet and deal the cards';
    document.getElementById('baccaratResultFP').innerHTML = '';
    
    showDealerComment('Ready for the next hand?');
}

function repeatLastBet() {
    // Implementation for repeat last bet
    showDealerComment('Repeating your previous bet configuration.');
}

function clearAllBets() {
    resetBaccaratGameFP();
    showDealerComment('All bets cleared. Place your new bets.');
}

function toggleAutoPlay() {
    baccaratFirstPersonGame.autoPlay = !baccaratFirstPersonGame.autoPlay;
    showDealerComment(baccaratFirstPersonGame.autoPlay ? 'Auto play enabled.' : 'Auto play disabled.');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    // Initialize wallet integration first
    await initializeWallet();

    // Load the game
    loadBaccaratFirstPersonGame();
});