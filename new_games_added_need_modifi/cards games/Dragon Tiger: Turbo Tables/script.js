// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Dragon Tiger: Turbo Tables - High-speed card game with extremely low win rate
const CARD_VALUES = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
const CARD_SUITS = ['♠', '♥', '♦', '♣'];

// Betting limits and payouts (REDUCED PAYOUTS)
const TURBO_PAYOUTS = {
    DRAGON: 0.85, // Reduced from 1:1
    TIGER: 0.85,  // Reduced from 1:1
    TIE: 8,       // Reduced from 11:1
    SUITED_TIE: 25, // Reduced from 50:1
    DRAGON_ODD: 0.8, // Reduced from 1:1
    DRAGON_EVEN: 0.8,
    TIGER_ODD: 0.8,
    TIGER_EVEN: 0.8,
    DRAGON_RED: 0.8,
    DRAGON_BLACK: 0.8,
    TIGER_RED: 0.8,
    TIGER_BLACK: 0.8,
    BIG: 0.8,     // Reduced from 1:1
    SMALL: 0.8    // Reduced from 1:1
};

// Table speed tiers (faster = less time to think = more mistakes)
const TURBO_SPEEDS = [
    { name: 'standard', dealTime: 3000, betTime: 10000, description: 'Standard Speed' },
    { name: 'turbo', dealTime: 2000, betTime: 7000, description: 'Turbo Speed' },
    { name: 'lightning', dealTime: 1000, betTime: 5000, description: 'Lightning Speed' },
    { name: 'flash', dealTime: 800, betTime: 3000, description: 'Flash Speed' },
    { name: 'quantum', dealTime: 500, betTime: 2000, description: 'Quantum Speed' }
];

// Table limits for high rollers
const TABLE_LIMITS = [
    { name: 'regular', minBet: 50, maxBet: 1000, description: 'Regular Table (50-1K)' },
    { name: 'high', minBet: 500, maxBet: 10000, description: 'High Roller (500-10K)' },
    { name: 'vip', minBet: 5000, maxBet: 50000, description: 'VIP Table (5K-50K)' },
    { name: 'elite', minBet: 10000, maxBet: 100000, description: 'Elite Table (10K-100K)' }
];

// Biased deck configuration (heavily favors house)
const BIASED_DECK_CONFIG = {
    // Dealer advantage for Dragon (first card)
    dragonLowCardReduction: 0.7,  // Reduce low cards for Dragon
    dragonHighCardBoost: 0.3,     // Reduce high cards for Dragon
    
    // Dealer advantage for Tiger (second card)
    tigerHighCardBoost: 1.5,      // Boost high cards for Tiger
    tigerLowCardReduction: 0.5,   // Reduce low cards for Tiger
    
    // Tie probability reduction
    tieReduction: 0.3,            // Reduce chance of ties
    
    // Card distribution skew
    redBlackSkew: 0.2,            // Skew red/black distribution
    oddEvenSkew: 0.2              // Skew odd/even distribution
};

let dragonTigerGame = {
    // Game state
    isPlaying: false,
    gamePhase: 'betting', // betting, dealing, result
    roundNumber: 0,
    
    // Cards
    dragonCard: null,
    tigerCard: null,
    
    // Game settings
    currentSpeed: 'turbo',
    currentLimit: 'regular',
    autoBetting: false,
    
    // Current bets
    bets: {
        dragon: 0,
        tiger: 0,
        tie: 0,
        suitedTie: 0,
        dragonOdd: 0,
        dragonEven: 0,
        tigerOdd: 0,
        tigerEven: 0,
        dragonRed: 0,
        dragonBlack: 0,
        tigerRed: 0,
        tigerBlack: 0,
        big: 0,
        small: 0
    },
    
    // Statistics
    stats: {
        totalRounds: 0,
        dragonWins: 0,
        tigerWins: 0,
        tieWins: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestStreak: 0,
        lastResults: []
    },
    
    // Timers
    betTimer: null,
    dealTimer: null
};

// Initialize game
function loadDragonTigerGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-6xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-3xl font-bold cyber-title mb-2">Dragon Tiger: Turbo Tables</h3>
                <div class="flex justify-center items-center space-x-4 text-lg">
                    <div>Round: <span id="roundNumber" class="text-purple-400">0</span></div>
                    <div>Speed: 
                        <select id="speedSelect" class="cyber-select bg-cyber-dark border border-purple-500 rounded px-2 py-1">
                            ${TURBO_SPEEDS.map(speed => 
                                `<option value="${speed.name}">${speed.description}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div>Limits: 
                        <select id="limitSelect" class="cyber-select bg-cyber-dark border border-purple-500 rounded px-2 py-1">
                            ${TABLE_LIMITS.map(limit => 
                                `<option value="${limit.name}">${limit.description}</option>`
                            ).join('')}
                        </select>
                    </div>
                </div>
                <div id="timerDisplay" class="mt-2 text-xl font-bold text-yellow-400">Place your bets</div>
            </div>

            <!-- Card Display -->
            <div class="grid grid-cols-3 gap-4 mb-8">
                <div class="cyber-card border-red-500 p-4 text-center">
                    <h4 class="text-xl font-bold text-red-400 mb-2">DRAGON</h4>
                    <div id="dragonCard" class="playing-card mx-auto">
                        <div class="card-back"></div>
                    </div>
                </div>
                
                <div class="cyber-card border-purple-500 p-4 text-center">
                    <h4 class="text-xl font-bold text-purple-400 mb-2">RESULT</h4>
                    <div id="gameResult" class="text-2xl font-bold text-white mb-2">-</div>
                    <div id="winAmount" class="text-xl text-green-400"></div>
                </div>
                
                <div class="cyber-card border-blue-500 p-4 text-center">
                    <h4 class="text-xl font-bold text-blue-400 mb-2">TIGER</h4>
                    <div id="tigerCard" class="playing-card mx-auto">
                        <div class="card-back"></div>
                    </div>
                </div>
            </div>

            <!-- Betting Area -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Main Bets -->
                <div class="cyber-card border-green-500">
                    <h4 class="text-xl font-bold text-green-400 mb-4 text-center">MAIN BETS</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="text-center">
                            <button id="dragonBetBtn" class="cyber-button-secondary w-full py-2 rounded-lg mb-2">
                                DRAGON (0.85:1)
                            </button>
                            <input type="number" id="dragonBet" value="0" min="0" step="50"
                                   class="w-full bg-black/50 border border-red-500/30 rounded px-2 py-1 text-white text-center">
                        </div>
                        
                        <div class="text-center">
                            <button id="tieBetBtn" class="cyber-button-secondary w-full py-2 rounded-lg mb-2">
                                TIE (8:1)
                            </button>
                            <input type="number" id="tieBet" value="0" min="0" step="10"
                                   class="w-full bg-black/50 border border-purple-500/30 rounded px-2 py-1 text-white text-center">
                            <button id="suitedTieBetBtn" class="cyber-button-secondary w-full py-1 rounded-lg mt-2 text-xs">
                                SUITED TIE (25:1)
                            </button>
                            <input type="number" id="suitedTieBet" value="0" min="0" step="10"
                                   class="w-full bg-black/50 border border-purple-500/30 rounded px-2 py-1 text-white text-center">
                        </div>
                        
                        <div class="text-center">
                            <button id="tigerBetBtn" class="cyber-button-secondary w-full py-2 rounded-lg mb-2">
                                TIGER (0.85:1)
                            </button>
                            <input type="number" id="tigerBet" value="0" min="0" step="50"
                                   class="w-full bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-center">
                        </div>
                    </div>
                </div>
                
                <!-- Side Bets -->
                <div class="cyber-card border-yellow-500">
                    <h4 class="text-xl font-bold text-yellow-400 mb-4 text-center">SIDE BETS (0.8:1)</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h5 class="text-sm font-semibold text-red-400 mb-2">DRAGON</h5>
                            <div class="grid grid-cols-2 gap-2 mb-2">
                                <div>
                                    <button id="dragonOddBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        ODD
                                    </button>
                                    <input type="number" id="dragonOddBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-red-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                                <div>
                                    <button id="dragonEvenBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        EVEN
                                    </button>
                                    <input type="number" id="dragonEvenBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-red-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-2">
                                <div>
                                    <button id="dragonRedBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        RED
                                    </button>
                                    <input type="number" id="dragonRedBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-red-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                                <div>
                                    <button id="dragonBlackBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        BLACK
                                    </button>
                                    <input type="number" id="dragonBlackBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-red-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="text-sm font-semibold text-blue-400 mb-2">TIGER</h5>
                            <div class="grid grid-cols-2 gap-2 mb-2">
                                <div>
                                    <button id="tigerOddBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        ODD
                                    </button>
                                    <input type="number" id="tigerOddBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                                <div>
                                    <button id="tigerEvenBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        EVEN
                                    </button>
                                    <input type="number" id="tigerEvenBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-2">
                                <div>
                                    <button id="tigerRedBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        RED
                                    </button>
                                    <input type="number" id="tigerRedBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                                <div>
                                    <button id="tigerBlackBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-xs mb-1">
                                        BLACK
                                    </button>
                                    <input type="number" id="tigerBlackBet" value="0" min="0" step="50"
                                           class="w-full bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-xs text-center">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <div>
                            <button id="bigBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-sm mb-1">
                                BIG (A-6)
                            </button>
                            <input type="number" id="bigBet" value="0" min="0" step="50"
                                   class="w-full bg-black/50 border border-yellow-500/30 rounded px-2 py-1 text-white text-sm text-center">
                        </div>
                        <div>
                            <button id="smallBtn" class="cyber-button-secondary w-full py-1 rounded-lg text-sm mb-1">
                                SMALL (7-K)
                            </button>
                            <input type="number" id="smallBet" value="0" min="0" step="50"
                                   class="w-full bg-black/50 border border-yellow-500/30 rounded px-2 py-1 text-white text-sm text-center">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Control Buttons -->
            <div class="flex justify-center space-x-4 mb-8">
                <button id="placeBetsBtn" class="cyber-button-primary px-8 py-3 rounded-lg font-bold">
                    PLACE BETS
                </button>
                <button id="clearBetsBtn" class="cyber-button-secondary px-6 py-3 rounded-lg font-semibold">
                    CLEAR BETS
                </button>
                <button id="autoBetBtn" class="cyber-button-secondary px-6 py-3 rounded-lg font-semibold">
                    AUTO BET
                </button>
            </div>

            <!-- Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Game Stats -->
                <div class="cyber-card border-purple-500">
                    <h4 class="text-xl font-bold text-purple-400 mb-4 text-center">GAME STATISTICS</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <div class="mb-2">Total Rounds: <span id="totalRounds" class="text-white">0</span></div>
                            <div class="mb-2">Dragon Wins: <span id="dragonWins" class="text-red-400">0</span></div>
                            <div class="mb-2">Tiger Wins: <span id="tigerWins" class="text-blue-400">0</span></div>
                            <div class="mb-2">Ties: <span id="tieWins" class="text-purple-400">0</span></div>
                        </div>
                        <div>
                            <div class="mb-2">Total Wagered: <span id="totalWagered" class="text-yellow-400">0</span></div>
                            <div class="mb-2">Total Won: <span id="totalWon" class="text-green-400">0</span></div>
                            <div class="mb-2">Win Rate: <span id="winRate" class="text-red-400">0%</span></div>
                            <div class="mb-2">Biggest Win: <span id="biggestWin" class="text-green-400">0</span></div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Results -->
                <div class="cyber-card border-blue-500">
                    <h4 class="text-xl font-bold text-blue-400 mb-4 text-center">RECENT RESULTS</h4>
                    <div class="flex flex-wrap gap-2 justify-center" id="recentResults">
                        <!-- Results will be added here -->
                    </div>
                    <div class="mt-4 text-center">
                        <div class="text-sm">Current Streak: <span id="currentStreak" class="text-yellow-400">0</span></div>
                        <div class="text-sm">Longest Streak: <span id="longestStreak" class="text-yellow-400">0</span></div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupDragonTigerGame();
}

function setupDragonTigerGame() {
    // Set up button event listeners
    document.getElementById('placeBetsBtn').addEventListener('click', placeDragonTigerBets);
    document.getElementById('clearBetsBtn').addEventListener('click', clearDragonTigerBets);
    document.getElementById('autoBetBtn').addEventListener('click', toggleAutoBet);
    document.getElementById('speedSelect').addEventListener('change', updateGameSpeed);
    document.getElementById('limitSelect').addEventListener('change', updateTableLimits);
    
    // Quick bet buttons
    document.getElementById('dragonBetBtn').addEventListener('click', () => quickBet('dragon'));
    document.getElementById('tigerBetBtn').addEventListener('click', () => quickBet('tiger'));
    document.getElementById('tieBetBtn').addEventListener('click', () => quickBet('tie'));
    document.getElementById('suitedTieBetBtn').addEventListener('click', () => quickBet('suitedTie'));
    document.getElementById('dragonOddBtn').addEventListener('click', () => quickBet('dragonOdd'));
    document.getElementById('dragonEvenBtn').addEventListener('click', () => quickBet('dragonEven'));
    document.getElementById('tigerOddBtn').addEventListener('click', () => quickBet('tigerOdd'));
    document.getElementById('tigerEvenBtn').addEventListener('click', () => quickBet('tigerEven'));
    document.getElementById('dragonRedBtn').addEventListener('click', () => quickBet('dragonRed'));
    document.getElementById('dragonBlackBtn').addEventListener('click', () => quickBet('dragonBlack'));
    document.getElementById('tigerRedBtn').addEventListener('click', () => quickBet('tigerRed'));
    document.getElementById('tigerBlackBtn').addEventListener('click', () => quickBet('tigerBlack'));
    document.getElementById('bigBtn').addEventListener('click', () => quickBet('big'));
    document.getElementById('smallBtn').addEventListener('click', () => quickBet('small'));
    
    updateGameSpeed();
    updateTableLimits();
    updateStats();
    
    // Start first round
    startNewRound();
}

function updateGameSpeed() {
    dragonTigerGame.currentSpeed = document.getElementById('speedSelect').value;
    
    // If a round is in progress, adjust timers
    if (dragonTigerGame.isPlaying && dragonTigerGame.gamePhase === 'betting') {
        clearTimeout(dragonTigerGame.betTimer);
        const speedData = TURBO_SPEEDS.find(s => s.name === dragonTigerGame.currentSpeed);
        dragonTigerGame.betTimer = setTimeout(dealCards, speedData.betTime);
        updateTimerDisplay(speedData.betTime / 1000);
    }
}

function updateTableLimits() {
    dragonTigerGame.currentLimit = document.getElementById('limitSelect').value;
    const limitData = TABLE_LIMITS.find(l => l.name === dragonTigerGame.currentLimit);
    
    // Update all bet inputs with new min/max
    document.querySelectorAll('input[type="number"]').forEach(input => {
        if (input.id.includes('Bet')) {
            input.min = limitData.minBet;
            input.max = limitData.maxBet;
            input.step = Math.max(10, limitData.minBet / 10);
        }
    });
}

function quickBet(betType) {
    if (!dragonTigerGame.isPlaying || dragonTigerGame.gamePhase !== 'betting') return;
    
    const limitData = TABLE_LIMITS.find(l => l.name === dragonTigerGame.currentLimit);
    const input = document.getElementById(`${betType}Bet`);
    const currentValue = parseInt(input.value) || 0;
    
    // Increment bet by min bet amount
    const newValue = currentValue + limitData.minBet;
    if (newValue <= limitData.maxBet) {
        input.value = newValue;
    }
}

function startNewRound() {
    dragonTigerGame.isPlaying = true;
    dragonTigerGame.gamePhase = 'betting';
    dragonTigerGame.roundNumber++;
    
    // Reset cards
    document.getElementById('dragonCard').innerHTML = '<div class="card-back"></div>';
    document.getElementById('tigerCard').innerHTML = '<div class="card-back"></div>';
    
    // Reset result display
    document.getElementById('gameResult').textContent = '-';
    document.getElementById('winAmount').textContent = '';
    document.getElementById('roundNumber').textContent = dragonTigerGame.roundNumber;
    
    // Start betting timer
    const speedData = TURBO_SPEEDS.find(s => s.name === dragonTigerGame.currentSpeed);
    updateTimerDisplay(speedData.betTime / 1000);
    
    dragonTigerGame.betTimer = setTimeout(dealCards, speedData.betTime);
}

function updateTimerDisplay(seconds) {
    const timerDisplay = document.getElementById('timerDisplay');
    
    // Update every second
    let timeLeft = seconds;
    timerDisplay.textContent = `Betting: ${timeLeft}s`;
    
    const interval = setInterval(() => {
        timeLeft--;
        if (timeLeft <= 0) {
            clearInterval(interval);
            timerDisplay.textContent = 'Dealing cards...';
        } else {
            timerDisplay.textContent = `Betting: ${timeLeft}s`;
        }
    }, 1000);
}

function placeDragonTigerBets() {
    if (!dragonTigerGame.isPlaying || dragonTigerGame.gamePhase !== 'betting') return;
    
    // Collect all bets
    const bets = {
        dragon: parseInt(document.getElementById('dragonBet').value) || 0,
        tiger: parseInt(document.getElementById('tigerBet').value) || 0,
        tie: parseInt(document.getElementById('tieBet').value) || 0,
        suitedTie: parseInt(document.getElementById('suitedTieBet').value) || 0,
        dragonOdd: parseInt(document.getElementById('dragonOddBet').value) || 0,
        dragonEven: parseInt(document.getElementById('dragonEvenBet').value) || 0,
        tigerOdd: parseInt(document.getElementById('tigerOddBet').value) || 0,
        tigerEven: parseInt(document.getElementById('tigerEvenBet').value) || 0,
        dragonRed: parseInt(document.getElementById('dragonRedBet').value) || 0,
        dragonBlack: parseInt(document.getElementById('dragonBlackBet').value) || 0,
        tigerRed: parseInt(document.getElementById('tigerRedBet').value) || 0,
        tigerBlack: parseInt(document.getElementById('tigerBlackBet').value) || 0,
        big: parseInt(document.getElementById('bigBet').value) || 0,
        small: parseInt(document.getElementById('smallBet').value) || 0
    };
    
    // Calculate total bet amount
    const totalBet = Object.values(bets).reduce((sum, bet) => sum + bet, 0);
    
    if (totalBet === 0) {
        alert('Please place at least one bet!');
        return;
    }
    
    if (totalBet > balance) {
        alert('Insufficient balance for all bets!');
        return;
    }
    
    // Deduct from balance
    balance -= totalBet;
    updateBalance();
    
    // Save bets
    dragonTigerGame.bets = bets;
    dragonTigerGame.stats.totalWagered += totalBet;
    
    // Skip timer and deal immediately
    clearTimeout(dragonTigerGame.betTimer);
    dealCards();
}

function clearDragonTigerBets() {
    document.querySelectorAll('input[type="number"]').forEach(input => {
        if (input.id.includes('Bet')) {
            input.value = 0;
        }
    });
}

function toggleAutoBet() {
    dragonTigerGame.autoBetting = !dragonTigerGame.autoBetting;
    const autoBetBtn = document.getElementById('autoBetBtn');
    
    if (dragonTigerGame.autoBetting) {
        autoBetBtn.textContent = 'STOP AUTO';
        autoBetBtn.classList.add('bg-red-600');
    } else {
        autoBetBtn.textContent = 'AUTO BET';
        autoBetBtn.classList.remove('bg-red-600');
    }
}

function dealCards() {
    if (!dragonTigerGame.isPlaying) return;
    
    dragonTigerGame.gamePhase = 'dealing';
    document.getElementById('timerDisplay').textContent = 'Dealing cards...';
    
    // Collect bets if auto-betting is enabled
    if (dragonTigerGame.autoBetting) {
        const limitData = TABLE_LIMITS.find(l => l.name === dragonTigerGame.currentLimit);
        
        // Simple auto-betting strategy
        const autoBet = limitData.minBet;
        if (balance >= autoBet * 3) {
            document.getElementById('dragonBet').value = autoBet;
            document.getElementById('tigerBet').value = autoBet;
            document.getElementById('tieBet').value = Math.floor(autoBet / 5);
            
            placeDragonTigerBets();
        } else {
            // Stop auto-betting if balance is too low
            toggleAutoBet();
        }
    }
    
    // Generate biased cards
    const { dragonCard, tigerCard } = generateBiasedCards();
    dragonTigerGame.dragonCard = dragonCard;
    dragonTigerGame.tigerCard = tigerCard;
    
    // Display cards with animation
    setTimeout(() => {
        displayCard('dragonCard', dragonCard);
        
        setTimeout(() => {
            displayCard('tigerCard', tigerCard);
            
            // Determine winner after both cards are shown
            setTimeout(() => {
                determineWinner();
            }, 500);
        }, 500);
    }, 500);
}

function generateBiasedCards() {
    // Create biased deck that favors the house
    const deck = createBiasedDeck();
    
    // Draw two cards
    const dragonCard = deck.pop();
    const tigerCard = deck.pop();
    
    return { dragonCard, tigerCard };
}

function createBiasedDeck() {
    const deck = [];
    
    // Create a biased deck with multiple copies of cards
    for (const suit of CARD_SUITS) {
        for (const value of CARD_VALUES) {
            const card = { suit, value };
            const cardValue = getCardValue(card);
            const isRed = (suit === '♥' || suit === '♦');
            
            // Base copies
            let copies = 1;
            
            // Apply bias based on configuration
            if (cardValue <= 6) {
                // Low cards - reduce for Dragon, boost for Tiger
                copies *= BIASED_DECK_CONFIG.dragonLowCardReduction;
                copies *= (1 + BIASED_DECK_CONFIG.tigerHighCardBoost * 0.3);
            } else {
                // High cards - reduce for Dragon, boost for Tiger
                copies *= BIASED_DECK_CONFIG.dragonHighCardBoost;
                copies *= BIASED_DECK_CONFIG.tigerHighCardBoost;
            }
            
            // Red/Black bias
            if (isRed) {
                copies *= (1 - BIASED_DECK_CONFIG.redBlackSkew);
            } else {
                copies *= (1 + BIASED_DECK_CONFIG.redBlackSkew);
            }
            
            // Odd/Even bias
            if (cardValue % 2 === 1) {
                copies *= (1 - BIASED_DECK_CONFIG.oddEvenSkew);
            } else {
                copies *= (1 + BIASED_DECK_CONFIG.oddEvenSkew);
            }
            
            // Add cards to deck
            for (let i = 0; i < Math.max(1, Math.floor(copies * 4)); i++) {
                deck.push(card);
            }
        }
    }
    
    // Shuffle deck
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    
    return deck;
}

function getCardValue(card) {
    const valueMap = {
        '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
        'J': 11, 'Q': 12, 'K': 13, 'A': 14
    };
    return valueMap[card.value];
}

function displayCard(elementId, card) {
    const element = document.getElementById(elementId);
    const isRed = (card.suit === '♥' || card.suit === '♦');
    
    element.innerHTML = `
        <div class="playing-card-face ${isRed ? 'text-red-500' : 'text-black'} bg-white border-2 border-gray-300 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold">${card.value}</div>
            <div class="text-3xl">${card.suit}</div>
        </div>
    `;
}

function determineWinner() {
    const dragonValue = getCardValue(dragonTigerGame.dragonCard);
    const tigerValue = getCardValue(dragonTigerGame.tigerCard);
    
    let result;
    let winner;
    
    if (dragonValue > tigerValue) {
        result = 'DRAGON WINS';
        winner = 'dragon';
        dragonTigerGame.stats.dragonWins++;
    } else if (tigerValue > dragonValue) {
        result = 'TIGER WINS';
        winner = 'tiger';
        dragonTigerGame.stats.tigerWins++;
    } else {
        // Check for suited tie
        if (dragonTigerGame.dragonCard.suit === dragonTigerGame.tigerCard.suit) {
            result = 'SUITED TIE';
            winner = 'suitedTie';
        } else {
            result = 'TIE';
            winner = 'tie';
        }
        dragonTigerGame.stats.tieWins++;
    }
    
    // Display result
    document.getElementById('gameResult').textContent = result;
    
    // Calculate winnings
    const winnings = calculateWinnings(winner);
    
    if (winnings > 0) {
        balance += winnings;
        updateBalance();
        dragonTigerGame.stats.totalWon += winnings;
        
        if (winnings > dragonTigerGame.stats.biggestWin) {
            dragonTigerGame.stats.biggestWin = winnings;
        }
        
        document.getElementById('winAmount').textContent = `+${winnings} GA`;
        document.getElementById('winAmount').className = 'text-xl text-green-400';
    } else {
        document.getElementById('winAmount').textContent = 'No Win';
        document.getElementById('winAmount').className = 'text-xl text-red-400';
    }
    
    // Update statistics
    dragonTigerGame.stats.totalRounds++;
    updateStats();
    updateRecentResults(winner);
    
    // Clear bets for next round
    clearDragonTigerBets();
    
    // Start next round after delay
    const speedData = TURBO_SPEEDS.find(s => s.name === dragonTigerGame.currentSpeed);
    setTimeout(() => {
        startNewRound();
    }, speedData.dealTime);
}

function calculateWinnings(winner) {
    let totalWinnings = 0;
    const bets = dragonTigerGame.bets;
    const dragonCard = dragonTigerGame.dragonCard;
    const tigerCard = dragonTigerGame.tigerCard;
    
    // Apply house edge penalty (reduces all payouts by 15%)
    const houseEdgePenalty = 0.85;
    
    // Main bets
    if (winner === 'dragon' && bets.dragon > 0) {
        totalWinnings += bets.dragon * (1 + TURBO_PAYOUTS.DRAGON) * houseEdgePenalty;
    }
    if (winner === 'tiger' && bets.tiger > 0) {
        totalWinnings += bets.tiger * (1 + TURBO_PAYOUTS.TIGER) * houseEdgePenalty;
    }
    if (winner === 'tie' && bets.tie > 0) {
        totalWinnings += bets.tie * (1 + TURBO_PAYOUTS.TIE) * houseEdgePenalty;
    }
    if (winner === 'suitedTie' && bets.suitedTie > 0) {
        totalWinnings += bets.suitedTie * (1 + TURBO_PAYOUTS.SUITED_TIE) * houseEdgePenalty;
    }
    
    // Side bets - Dragon
    const dragonValue = getCardValue(dragonCard);
    const dragonIsRed = (dragonCard.suit === '♥' || dragonCard.suit === '♦');
    const dragonIsOdd = dragonValue % 2 === 1;
    
    if (bets.dragonOdd > 0 && dragonIsOdd) {
        totalWinnings += bets.dragonOdd * (1 + TURBO_PAYOUTS.DRAGON_ODD) * houseEdgePenalty;
    }
    if (bets.dragonEven > 0 && !dragonIsOdd) {
        totalWinnings += bets.dragonEven * (1 + TURBO_PAYOUTS.DRAGON_EVEN) * houseEdgePenalty;
    }
    if (bets.dragonRed > 0 && dragonIsRed) {
        totalWinnings += bets.dragonRed * (1 + TURBO_PAYOUTS.DRAGON_RED) * houseEdgePenalty;
    }
    if (bets.dragonBlack > 0 && !dragonIsRed) {
        totalWinnings += bets.dragonBlack * (1 + TURBO_PAYOUTS.DRAGON_BLACK) * houseEdgePenalty;
    }
    
    // Side bets - Tiger
    const tigerValue = getCardValue(tigerCard);
    const tigerIsRed = (tigerCard.suit === '♥' || tigerCard.suit === '♦');
    const tigerIsOdd = tigerValue % 2 === 1;
    
    if (bets.tigerOdd > 0 && tigerIsOdd) {
        totalWinnings += bets.tigerOdd * (1 + TURBO_PAYOUTS.TIGER_ODD) * houseEdgePenalty;
    }
    if (bets.tigerEven > 0 && !tigerIsOdd) {
        totalWinnings += bets.tigerEven * (1 + TURBO_PAYOUTS.TIGER_EVEN) * houseEdgePenalty;
    }
    if (bets.tigerRed > 0 && tigerIsRed) {
        totalWinnings += bets.tigerRed * (1 + TURBO_PAYOUTS.TIGER_RED) * houseEdgePenalty;
    }
    if (bets.tigerBlack > 0 && !tigerIsRed) {
        totalWinnings += bets.tigerBlack * (1 + TURBO_PAYOUTS.TIGER_BLACK) * houseEdgePenalty;
    }
    
    // Big/Small bets (based on winning card)
    const winningCard = winner === 'dragon' ? dragonCard : winner === 'tiger' ? tigerCard : null;
    if (winningCard) {
        const winningValue = getCardValue(winningCard);
        const isBig = winningValue >= 8; // A, K, Q, J, 10, 9, 8
        
        if (bets.big > 0 && isBig) {
            totalWinnings += bets.big * (1 + TURBO_PAYOUTS.BIG) * houseEdgePenalty;
        }
        if (bets.small > 0 && !isBig) {
            totalWinnings += bets.small * (1 + TURBO_PAYOUTS.SMALL) * houseEdgePenalty;
        }
    }
    
    return Math.floor(totalWinnings);
}

function updateStats() {
    document.getElementById('totalRounds').textContent = dragonTigerGame.stats.totalRounds;
    document.getElementById('dragonWins').textContent = dragonTigerGame.stats.dragonWins;
    document.getElementById('tigerWins').textContent = dragonTigerGame.stats.tigerWins;
    document.getElementById('tieWins').textContent = dragonTigerGame.stats.tieWins;
    document.getElementById('totalWagered').textContent = dragonTigerGame.stats.totalWagered.toLocaleString();
    document.getElementById('totalWon').textContent = dragonTigerGame.stats.totalWon.toLocaleString();
    document.getElementById('biggestWin').textContent = dragonTigerGame.stats.biggestWin.toLocaleString();
    
    // Calculate win rate (intentionally low due to house edge)
    const winRate = dragonTigerGame.stats.totalWagered > 0 ? 
        ((dragonTigerGame.stats.totalWon / dragonTigerGame.stats.totalWagered) * 100) : 0;
    document.getElementById('winRate').textContent = `${Math.max(0, winRate).toFixed(1)}%`;
}

function updateRecentResults(winner) {
    // Add to recent results
    dragonTigerGame.stats.lastResults.unshift(winner);
    if (dragonTigerGame.stats.lastResults.length > 20) {
        dragonTigerGame.stats.lastResults.pop();
    }
    
    // Update streak tracking
    if (dragonTigerGame.stats.lastResults.length > 1) {
        const lastWinner = dragonTigerGame.stats.lastResults[1];
        if (winner === lastWinner) {
            dragonTigerGame.stats.currentStreak++;
        } else {
            if (dragonTigerGame.stats.currentStreak > dragonTigerGame.stats.longestStreak) {
                dragonTigerGame.stats.longestStreak = dragonTigerGame.stats.currentStreak;
            }
            dragonTigerGame.stats.currentStreak = 1;
        }
    } else {
        dragonTigerGame.stats.currentStreak = 1;
    }
    
    // Update display
    const recentResultsDiv = document.getElementById('recentResults');
    recentResultsDiv.innerHTML = '';
    
    dragonTigerGame.stats.lastResults.forEach(result => {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold';
        
        switch(result) {
            case 'dragon':
                resultDiv.className += ' bg-red-600 text-white';
                resultDiv.textContent = 'D';
                break;
            case 'tiger':
                resultDiv.className += ' bg-blue-600 text-white';
                resultDiv.textContent = 'T';
                break;
            case 'tie':
                resultDiv.className += ' bg-purple-600 text-white';
                resultDiv.textContent = 'TIE';
                break;
            case 'suitedTie':
                resultDiv.className += ' bg-yellow-600 text-black';
                resultDiv.textContent = 'ST';
                break;
        }
        
        recentResultsDiv.appendChild(resultDiv);
    });
    
    document.getElementById('currentStreak').textContent = dragonTigerGame.stats.currentStreak;
    document.getElementById('longestStreak').textContent = dragonTigerGame.stats.longestStreak;
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDragonTigerGame();
});