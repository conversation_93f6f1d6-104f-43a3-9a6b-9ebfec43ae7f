/**
 * Shared Wallet Integration Module for Card Games
 * 
 * This module provides a unified wallet integration system for all card games.
 * It handles balance loading, transaction recording, and real-time updates.
 */

class GameWalletIntegration {
    constructor(gameName) {
        this.gameName = gameName;
        this.balance = 0;
        this.walletAPI = null;
        this.isLoadingBalance = false;
        this.balanceRefreshInterval = null;
        
        this.initializeWalletAPI();
    }

    /**
     * Initialize wallet API service
     */
    initializeWalletAPI() {
        // Check if SportsBettingAPI is available globally
        if (window.SportsBettingAPI) {
            this.walletAPI = new window.SportsBettingAPI();
        } else {
            console.warn('SportsBettingAPI not available. Using fallback.');
            // Fallback to basic fetch if API service not available
            this.walletAPI = {
                async getWalletBalance() {
                    const token = localStorage.getItem('auth_token');
                    if (!token) {
                        throw new Error('No authentication token found');
                    }
                    
                    const response = await fetch('/api/wallet', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        }
                    });
                    
                    if (!response.ok) {
                        throw new Error('Failed to fetch wallet balance');
                    }
                    
                    const data = await response.json();
                    return data.balance;
                },
                
                async recordGameTransaction(transactionData) {
                    const token = localStorage.getItem('auth_token');
                    if (!token) {
                        throw new Error('No authentication token found');
                    }
                    
                    const response = await fetch('/api/games/transaction', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(transactionData)
                    });
                    
                    if (!response.ok) {
                        throw new Error('Failed to record transaction');
                    }
                    
                    return await response.json();
                }
            };
        }
    }

    /**
     * Load balance from wallet API
     */
    async loadWalletBalance() {
        if (this.isLoadingBalance) return;
        
        this.isLoadingBalance = true;
        
        try {
            // Show loading state
            this.updateBalanceDisplay('Loading...');
            
            // Fetch balance from API
            const walletBalance = await this.walletAPI.getWalletBalance();
            this.balance = parseFloat(walletBalance) || 0;
            
            // Update display
            this.updateBalanceDisplay();
            
            // Update max bet amounts in inputs
            this.updateBetInputLimits();
            
        } catch (error) {
            console.error('Failed to load wallet balance:', error);
            
            // Show error state
            this.updateBalanceDisplay('Error loading balance', true);
            
            // Disable betting if balance can't be loaded
            this.disableBetting();
            
            // Show user-friendly error
            this.showNotification('Unable to load wallet balance. Please refresh the page.', 'error');
        } finally {
            this.isLoadingBalance = false;
        }
    }

    /**
     * Update balance display in all relevant elements
     */
    updateBalanceDisplay(customText = null, isError = false) {
        let balanceText;
        
        if (customText) {
            const colorClass = isError ? 'text-red-400' : 'text-yellow-400';
            balanceText = `Balance: <span class="${colorClass}">${customText}</span>`;
        } else {
            balanceText = `Balance: <span class="text-yellow-400 neon-glow">${this.balance.toLocaleString()} GA</span>`;
        }
        
        // Update header balance display
        const headerBalanceDisplay = document.getElementById('balanceDisplay');
        if (headerBalanceDisplay) {
            headerBalanceDisplay.innerHTML = balanceText;
        }
        
        // Update game area balance display if it exists
        const gameBalanceDisplay = document.querySelector('#gameContent #balanceDisplay');
        if (gameBalanceDisplay) {
            gameBalanceDisplay.innerHTML = balanceText;
        }
    }

    /**
     * Update bet input limits based on current balance
     */
    updateBetInputLimits() {
        const betInputs = document.querySelectorAll('input[type="number"][id*="bet"], input[type="number"][id*="Bet"]');
        betInputs.forEach(input => {
            input.setAttribute('max', this.balance);
            // Ensure current value doesn't exceed balance
            if (parseFloat(input.value) > this.balance) {
                input.value = Math.min(10, this.balance);
            }
        });
    }

    /**
     * Validate bet amount
     */
    validateBetAmount(betAmount) {
        if (isNaN(betAmount) || betAmount <= 0) {
            this.showNotification('Please enter a valid bet amount.', 'error');
            return false;
        }
        
        if (betAmount > this.balance) {
            this.showNotification('Insufficient balance! Please add funds to your wallet.', 'error');
            return false;
        }
        
        if (betAmount < 1) {
            this.showNotification('Minimum bet is 1 GA.', 'error');
            return false;
        }
        
        return true;
    }

    /**
     * Record game transaction
     */
    async recordGameTransaction(type, amount, metadata = {}) {
        try {
            const transactionData = {
                type: type, // 'bet' or 'win'
                amount: amount,
                game: this.gameName,
                metadata: {
                    game_type: this.gameName,
                    ...metadata
                }
            };
            
            if (this.walletAPI.recordGameTransaction) {
                await this.walletAPI.recordGameTransaction(transactionData);
            } else {
                throw new Error('Game transaction method not available');
            }
        } catch (error) {
            console.error('Failed to record transaction:', error);
            // Don't throw error to avoid breaking game flow
        }
    }

    /**
     * Process bet (deduct from balance and record transaction)
     */
    async processBet(betAmount, metadata = {}) {
        if (!this.validateBetAmount(betAmount)) {
            return false;
        }

        try {
            // Deduct bet from balance
            this.balance -= betAmount;
            this.updateBalanceDisplay();
            
            // Record bet transaction
            await this.recordGameTransaction('bet', betAmount, metadata);
            
            return true;
        } catch (error) {
            console.error('Error processing bet:', error);
            
            // Restore balance if transaction failed
            this.balance += betAmount;
            this.updateBalanceDisplay();
            
            this.showNotification('Failed to process bet. Please try again.', 'error');
            return false;
        }
    }

    /**
     * Process win (add to balance and record transaction)
     */
    async processWin(winAmount, metadata = {}) {
        try {
            // Add winnings to balance
            this.balance += winAmount;
            this.updateBalanceDisplay();
            
            // Record win transaction
            await this.recordGameTransaction('win', winAmount, metadata);
            
            this.showNotification(`Congratulations! You won ${winAmount} GA!`, 'success');
            
            return true;
        } catch (error) {
            console.error('Error recording win transaction:', error);
            // Don't break the game flow, just log the error
            this.showNotification('Game completed but transaction recording failed.', 'error');
            return false;
        }
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'error' ? 'bg-red-600 text-white' : 
            type === 'success' ? 'bg-green-600 text-white' : 
            'bg-blue-600 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    /**
     * Disable betting controls
     */
    disableBetting() {
        const betButtons = document.querySelectorAll('button[id*="bet"], button[id*="Bet"], button[id*="deal"], button[id*="Deal"]');
        betButtons.forEach(button => {
            button.disabled = true;
        });
    }

    /**
     * Start periodic balance refresh
     */
    startBalanceRefresh() {
        this.balanceRefreshInterval = setInterval(async () => {
            try {
                await this.loadWalletBalance();
            } catch (error) {
                console.error('Failed to refresh balance:', error);
            }
        }, 30000); // 30 seconds
    }

    /**
     * Stop periodic balance refresh
     */
    stopBalanceRefresh() {
        if (this.balanceRefreshInterval) {
            clearInterval(this.balanceRefreshInterval);
            this.balanceRefreshInterval = null;
        }
    }

    /**
     * Initialize wallet integration for a game
     */
    async initialize() {
        // Load initial balance
        await this.loadWalletBalance();
        
        // Start periodic refresh
        this.startBalanceRefresh();
        
        // Add input validation to bet inputs
        this.addBetInputValidation();
    }

    /**
     * Add input validation to bet inputs
     */
    addBetInputValidation() {
        const betInputs = document.querySelectorAll('input[type="number"][id*="bet"], input[type="number"][id*="Bet"]');
        betInputs.forEach(input => {
            input.addEventListener('input', () => {
                const value = parseFloat(input.value);
                const max = parseFloat(input.getAttribute('max'));
                
                if (value > max) {
                    input.value = max;
                    this.showNotification(`Maximum bet is ${max} GA`, 'error');
                }
                
                if (value < 1) {
                    input.value = 1;
                }
            });
        });
    }

    /**
     * Cleanup when leaving the game
     */
    cleanup() {
        this.stopBalanceRefresh();
    }
}

// Export for use in games
window.GameWalletIntegration = GameWalletIntegration;
