// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Craps Stadium High Roller Implementation with premium features
const DICE_FACES = [1, 2, 3, 4, 5, 6];

// High Roller betting limits and payouts (REDUCED PAYOUTS)
const HIGH_ROLLER_LIMITS = {
    PASS_LINE: { min: 100, max: 25000, payout: 0.85 }, // Reduced from 1:1
    DONT_PASS: { min: 100, max: 25000, payout: 0.85 },
    FIELD: { min: 50, max: 10000, payout: 0.8 }, // Reduced from 1:1
    ANY_CRAPS: { min: 25, max: 5000, payout: 5 }, // Reduced from 7:1
    HARD_WAYS: { min: 25, max: 2500, payout: 6 }, // Reduced from 9:1
    HOP_BETS: { min: 25, max: 1000, payout: 20 }, // Reduced from 30:1
    FIRE_BET: { min: 100, max: 1000, payout: 500 }, // Reduced from 999:1
    ODDS: { min: 0, max: 100000, payout: 1.5 } // Reduced from 2:1
};

// Stadium tiers for high rollers (REDUCED BONUSES)
const STADIUM_TIERS = [
    {
        name: 'vip_box',
        description: 'VIP Box (Min: 500 GA)',
        minBet: 500,
        maxBet: 10000,
        oddsMultiplier: 2, // Reduced from 3
        bonusMultiplier: 0.95, // PENALTY instead of bonus
        houseEdge: 0.15, // 15% house edge
        features: ['Basic Statistics', 'Hot/Cold Numbers']
    },
    {
        name: 'premium_suite',
        description: 'Premium Suite (Min: 2000 GA)',
        minBet: 2000,
        maxBet: 50000,
        oddsMultiplier: 3, // Reduced from 5
        bonusMultiplier: 0.9, // 10% penalty
        houseEdge: 0.18, // 18% house edge
        features: ['Advanced Analytics', 'Trend Tracking', 'Auto-Betting']
    },
    {
        name: 'high_roller_penthouse',
        description: 'High Roller Penthouse (Min: 10000 GA)',
        minBet: 10000,
        maxBet: 250000,
        oddsMultiplier: 4, // Reduced from 10
        bonusMultiplier: 0.85, // 15% penalty
        houseEdge: 0.22, // 22% house edge
        features: ['Predictive AI', 'Custom Strategies', 'Exclusive Bets']
    }
];

// Biased dice probabilities (heavily favor 7s and losing combinations)
const BIASED_DICE_WEIGHTS = {
    2: 0.08,  // Reduced probability
    3: 0.09,  // Reduced probability
    4: 0.10,  // Reduced probability
    5: 0.11,  // Reduced probability
    6: 0.12,  // Reduced probability
    7: 0.25,  // HEAVILY increased (normal is ~16.7%)
    8: 0.10,  // Reduced probability
    9: 0.08,  // Reduced probability
    10: 0.04, // Heavily reduced
    11: 0.02, // Heavily reduced
    12: 0.01  // Heavily reduced
};

let crapsStadiumGame = {
    // Game state
    gamePhase: 'comeOut', // comeOut, point
    point: null,
    shooterRoll: 0,
    currentTier: 'vip_box',
    
    // Dice and rolls
    dice1: 0,
    dice2: 0,
    rollHistory: [],
    hotNumbers: {},
    coldNumbers: {},
    
    // Betting
    activeBets: {
        passLine: { amount: 0, active: false },
        dontPass: { amount: 0, active: false },
        field: { amount: 0, active: false },
        odds: { amount: 0, active: false },
        come: { amount: 0, active: false },
        dontCome: { amount: 0, active: false },
        place: { 4: 0, 5: 0, 6: 0, 8: 0, 9: 0, 10: 0 },
        hardWays: { 4: 0, 6: 0, 8: 0, 10: 0 },
        proposition: {
            anyCraps: 0,
            any7: 0,
            yo11: 0,
            ace2: 0,
            ace12: 0,
            horn: 0
        },
        fireBet: { amount: 0, active: false, points: [] }
    },
    
    // Stadium features
    stadiumLevel: 1,
    prestigePoints: 0,
    rollStreak: 0,
    maxRollStreak: 0,
    
    // Advanced features
    autoRoll: false,
    predictiveMode: false,
    customStrategy: null,
    
    // Statistics
    sessionStats: {
        rollsTotal: 0,
        passLineWins: 0,
        pointsMade: 0,
        sevenOuts: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        longestRoll: 0,
        fireBetHits: 0,
        hardWayHits: 0
    }
};

// Biased dice rolling function
function rollBiasedDice() {
    const random = Math.random();
    let cumulative = 0;
    
    for (let total = 2; total <= 12; total++) {
        cumulative += BIASED_DICE_WEIGHTS[total];
        if (random <= cumulative) {
            // Generate dice combination for this total
            const combinations = getDiceCombinations(total);
            const combo = combinations[Math.floor(Math.random() * combinations.length)];
            return { dice1: combo[0], dice2: combo[1], total: total };
        }
    }
    
    // Fallback (should never reach here)
    return { dice1: 1, dice2: 6, total: 7 };
}

function getDiceCombinations(total) {
    const combinations = [];
    for (let d1 = 1; d1 <= 6; d1++) {
        for (let d2 = 1; d2 <= 6; d2++) {
            if (d1 + d2 === total) {
                combinations.push([d1, d2]);
            }
        }
    }
    return combinations;
}

function loadCrapsStadiumGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-8xl mx-auto">
            <!-- Stadium Header -->
            <div class="text-center mb-6">
                <h3 class="text-4xl font-bold cyber-title mb-4">Craps Stadium: High Roller</h3>
                <div class="flex justify-center items-center space-x-8 text-lg">
                    <div>Stadium Level: <span id="stadiumLevel" class="text-purple-400">1</span></div>
                    <div>Prestige: <span id="prestigePoints" class="text-blue-400">0</span></div>
                    <div>Roll Streak: <span id="rollStreak" class="text-green-400">0</span></div>
                </div>
                <div class="mt-2">
                    <select id="stadiumTierSelect" class="cyber-select bg-cyber-dark border border-purple-500 rounded px-4 py-2">
                        <option value="vip_box">VIP Box (Min: 500 GA)</option>
                        <option value="premium_suite">Premium Suite (Min: 2000 GA)</option>
                        <option value="high_roller_penthouse">High Roller Penthouse (Min: 10000 GA)</option>
                    </select>
                </div>
                <div class="mt-2 text-sm text-red-400">⚠️ House Edge: <span id="houseEdgeDisplay">15%</span></div>
            </div>

            <!-- Craps Table Layout -->
            <div class="cyber-card border-green-500 mb-8 p-6 bg-gradient-to-br from-green-900/20 to-black/50">
                <!-- Dice Display -->
                <div class="text-center mb-8">
                    <h4 class="text-2xl font-bold text-green-400 mb-4">🎲 STADIUM DICE 🎲</h4>
                    <div class="flex justify-center space-x-4 mb-4">
                        <div id="dice1" class="dice bg-white text-black text-4xl font-bold w-16 h-16 flex items-center justify-center rounded-lg shadow-lg">
                            ?
                        </div>
                        <div id="dice2" class="dice bg-white text-black text-4xl font-bold w-16 h-16 flex items-center justify-center rounded-lg shadow-lg">
                            ?
                        </div>
                    </div>
                    <div id="rollTotal" class="text-3xl font-bold text-yellow-400 mb-2">-</div>
                    <div id="gamePhaseDisplay" class="text-xl text-blue-400">Come Out Roll</div>
                    <div id="pointDisplay" class="text-lg text-purple-400"></div>
                </div>

                <!-- Main Betting Area -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Pass Line / Don't Pass -->
                    <div class="cyber-card border-blue-500">
                        <h5 class="text-lg font-semibold text-blue-400 mb-3 text-center">🎯 LINE BETS</h5>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Pass Line (0.85:1)</label>
                                <input type="number" id="passLineBet" value="0" min="0" max="25000" step="100"
                                       class="w-24 bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Don't Pass (0.85:1)</label>
                                <input type="number" id="dontPassBet" value="0" min="0" max="25000" step="100"
                                       class="w-24 bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Odds Bet (1.5:1)</label>
                                <input type="number" id="oddsBet" value="0" min="0" max="100000" step="100"
                                       class="w-24 bg-black/50 border border-blue-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <!-- Field & Come Bets -->
                    <div class="cyber-card border-yellow-500">
                        <h5 class="text-lg font-semibold text-yellow-400 mb-3 text-center">🌟 FIELD & COME</h5>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Field (0.8:1)</label>
                                <input type="number" id="fieldBet" value="0" min="0" max="10000" step="50"
                                       class="w-24 bg-black/50 border border-yellow-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Come (0.85:1)</label>
                                <input type="number" id="comeBet" value="0" min="0" max="25000" step="100"
                                       class="w-24 bg-black/50 border border-yellow-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Don't Come (0.85:1)</label>
                                <input type="number" id="dontComeBet" value="0" min="0" max="25000" step="100"
                                       class="w-24 bg-black/50 border border-yellow-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <!-- Fire Bet -->
                    <div class="cyber-card border-red-500">
                        <h5 class="text-lg font-semibold text-red-400 mb-3 text-center">🔥 FIRE BET</h5>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <label class="text-sm">Fire Bet (500:1)</label>
                                <input type="number" id="fireBet" value="0" min="0" max="1000" step="25"
                                       class="w-24 bg-black/50 border border-red-500/30 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="text-xs text-gray-400">
                                <div>Points Made: <span id="firePoints" class="text-red-400">0</span></div>
                                <div class="mt-1">6+ points = BIG WIN!</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Place Bets -->
                <div class="cyber-card border-purple-500 mb-6">
                    <h5 class="text-lg font-semibold text-purple-400 mb-3 text-center">🎲 PLACE BETS</h5>
                    <div class="grid grid-cols-6 gap-2">
                        ${[4, 5, 6, 8, 9, 10].map(num => `
                            <div class="text-center">
                                <div class="text-sm font-bold text-purple-400">${num}</div>
                                <input type="number" id="place${num}Bet" value="0" min="0" max="5000" step="25"
                                       class="w-full bg-black/50 border border-purple-500/30 rounded px-1 py-1 text-white text-xs">
                                <div class="text-xs text-gray-400">${getReducedPlaceOdds(num)}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Hard Ways -->
                <div class="cyber-card border-orange-500 mb-6">
                    <h5 class="text-lg font-semibold text-orange-400 mb-3 text-center">💎 HARD WAYS</h5>
                    <div class="grid grid-cols-4 gap-4">
                        ${[4, 6, 8, 10].map(num => `
                            <div class="text-center">
                                <div class="text-sm font-bold text-orange-400">Hard ${num}</div>
                                <input type="number" id="hard${num}Bet" value="0" min="0" max="2500" step="25"
                                       class="w-full bg-black/50 border border-orange-500/30 rounded px-2 py-1 text-white text-sm">
                                <div class="text-xs text-gray-400">${getReducedHardWayOdds(num)}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <!-- Proposition Bets -->
                <div class="cyber-card border-pink-500 mb-6">
                    <h5 class="text-lg font-semibold text-pink-400 mb-3 text-center">🎰 PROPOSITION BETS</h5>
                    <div class="grid grid-cols-3 lg:grid-cols-6 gap-3">
                        <div class="text-center">
                            <div class="text-xs font-bold text-pink-400">Any Craps</div>
                            <input type="number" id="anyCrapsBet" value="0" min="0" max="5000" step="25"
                                   class="w-full bg-black/50 border border-pink-500/30 rounded px-1 py-1 text-white text-xs">
                            <div class="text-xs text-gray-400">5:1</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs font-bold text-pink-400">Any 7</div>
                            <input type="number" id="any7Bet" value="0" min="0" max="5000" step="25"
                                   class="w-full bg-black/50 border border-pink-500/30 rounded px-1 py-1 text-white text-xs">
                            <div class="text-xs text-gray-400">3:1</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs font-bold text-pink-400">Yo (11)</div>
                            <input type="number" id="yo11Bet" value="0" min="0" max="2500" step="25"
                                   class="w-full bg-black/50 border border-pink-500/30 rounded px-1 py-1 text-white text-xs">
                            <div class="text-xs text-gray-400">10:1</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs font-bold text-pink-400">Ace-Deuce</div>
                            <input type="number" id="ace2Bet" value="0" min="0" max="1000" step="25"
                                   class="w-full bg-black/50 border border-pink-500/30 rounded px-1 py-1 text-white text-xs">
                            <div class="text-xs text-gray-400">10:1</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs font-bold text-pink-400">Boxcars</div>
                            <input type="number" id="ace12Bet" value="0" min="0" max="1000" step="25"
                                   class="w-full bg-black/50 border border-pink-500/30 rounded px-1 py-1 text-white text-xs">
                            <div class="text-xs text-gray-400">20:1</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs font-bold text-pink-400">Horn Bet</div>
                            <input type="number" id="hornBet" value="0" min="0" max="2000" step="100"
                                   class="w-full bg-black/50 border border-pink-500/30 rounded px-1 py-1 text-white text-xs">
                            <div class="text-xs text-gray-400">15:4</div>
                        </div>
                    </div>
                </div>

                <!-- Roll Controls -->
                <div class="text-center">
                    <button id="rollDice" class="cyber-button-primary px-8 py-4 rounded-lg font-bold text-xl mr-4">
                        🎲 ROLL DICE 🎲
                    </button>
                    <button id="clearBets" class="cyber-button-secondary px-6 py-3 rounded-lg font-semibold">
                        CLEAR BETS
                    </button>
                </div>
            </div>

            <!-- Stadium Features -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
                <!-- Quick Bets -->
                <div class="cyber-card border-green-500">
                    <h5 class="text-lg font-semibold text-green-400 mb-3">⚡ QUICK BETS</h5>
                    <div class="space-y-2">
                        <button onclick="setQuickBet('conservative')" class="w-full cyber-button-secondary py-2 text-sm rounded">
                            Conservative (Pass + Odds)
                        </button>
                        <button onclick="setQuickBet('aggressive')" class="w-full cyber-button-secondary py-2 text-sm rounded">
                            Aggressive (All Place)
                        </button>
                        <button onclick="setQuickBet('proposition')" class="w-full cyber-button-secondary py-2 text-sm rounded">
                            High Risk (Props)
                        </button>
                        <button onclick="setQuickBet('fire')" class="w-full cyber-button-secondary py-2 text-sm rounded">
                            Fire Strategy
                        </button>
                    </div>
                </div>

                <!-- Hot/Cold Numbers -->
                <div class="cyber-card border-yellow-500">
                    <h5 class="text-lg font-semibold text-yellow-400 mb-3">🔥 HOT/COLD</h5>
                    <div class="space-y-2 text-sm">
                        <div>Hot Numbers:</div>
                        <div id="hotNumbers" class="text-red-400 font-bold">-</div>
                        <div>Cold Numbers:</div>
                        <div id="coldNumbers" class="text-blue-400 font-bold">-</div>
                        <div>Last 10 Rolls:</div>
                        <div id="recentRolls" class="text-gray-300 text-xs">-</div>
                    </div>
                </div>

                <!-- Stadium Features -->
                <div class="cyber-card border-purple-500">
                    <h5 class="text-lg font-semibold text-purple-400 mb-3">🏟️ STADIUM FEATURES</h5>
                    <div class="space-y-2 text-sm">
                        <label class="flex items-center justify-between">
                            <span>Auto Roll</span>
                            <input type="checkbox" id="autoRoll" class="accent-purple-400">
                        </label>
                        <label class="flex items-center justify-between">
                            <span>Predictive Mode</span>
                            <input type="checkbox" id="predictiveMode" class="accent-purple-400">
                        </label>
                        <label class="flex items-center justify-between">
                            <span>Sound Effects</span>
                            <input type="checkbox" id="soundEffects" checked class="accent-purple-400">
                        </label>
                        <div class="mt-3">
                            <label class="block text-xs mb-1">Auto Roll Speed</label>
                            <input type="range" id="autoRollSpeed" min="1000" max="5000" value="2000" step="500"
                                   class="w-full accent-purple-400">
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="cyber-card border-blue-500">
                    <h5 class="text-lg font-semibold text-blue-400 mb-3">📊 SESSION STATS</h5>
                    <div class="space-y-1 text-sm">
                        <div>Rolls: <span id="totalRolls" class="text-blue-400">0</span></div>
                        <div>Points Made: <span id="pointsMade" class="text-green-400">0</span></div>
                        <div>Seven Outs: <span id="sevenOuts" class="text-red-400">0</span></div>
                        <div>Longest Roll: <span id="longestRoll" class="text-yellow-400">0</span></div>
                        <div>Total Wagered: <span id="totalWagered" class="text-purple-400">0</span></div>
                        <div>Biggest Win: <span id="biggestWin" class="text-green-400">0</span></div>
                        <div>Win Rate: <span id="winRate" class="text-red-400">0%</span></div>
                    </div>
                </div>
            </div>

            <!-- Game Status -->
            <div class="cyber-card border-gray-500 text-center">
                <div id="gameResult" class="text-xl font-bold mb-2"></div>
                <div id="gameStatus" class="text-lg text-gray-300">Place your bets and roll the dice!</div>
            </div>
        </div>
    `;

    setupCrapsStadiumGame();
}

function setupCrapsStadiumGame() {
    // Event listeners
    document.getElementById('rollDice').addEventListener('click', rollStadiumDice);
    document.getElementById('clearBets').addEventListener('click', clearAllBets);
    document.getElementById('stadiumTierSelect').addEventListener('change', updateStadiumTier);
    document.getElementById('autoRoll').addEventListener('change', toggleAutoRoll);
    document.getElementById('predictiveMode').addEventListener('change', togglePredictiveMode);
    
    updateStadiumDisplay();
    updateStadiumStats();
    initializeHotColdTracking();
    updateHouseEdgeDisplay();
}

function getReducedPlaceOdds(number) {
    const odds = { 4: '6:5', 5: '5:5', 6: '5:6', 8: '5:6', 9: '5:5', 10: '6:5' };
    return odds[number];
}

function getReducedHardWayOdds(number) {
    const odds = { 4: '5:1', 6: '6:1', 8: '6:1', 10: '5:1' };
    return odds[number];
}

function updateStadiumTier() {
    const tier = document.getElementById('stadiumTierSelect').value;
    crapsStadiumGame.currentTier = tier;
    
    const tierData = STADIUM_TIERS.find(t => t.name === tier);
    updateBettingLimits(tierData);
    updateHouseEdgeDisplay();
}

function updateHouseEdgeDisplay() {
    const tierData = STADIUM_TIERS.find(t => t.name === crapsStadiumGame.currentTier);
    document.getElementById('houseEdgeDisplay').textContent = `${Math.round(tierData.houseEdge * 100)}%`;
}

function updateBettingLimits(tierData) {
    // Update minimum bet requirements based on tier
    const passLineBet = document.getElementById('passLineBet');
    const dontPassBet = document.getElementById('dontPassBet');
    
    passLineBet.min = tierData.minBet;
    passLineBet.max = tierData.maxBet;
    dontPassBet.min = tierData.minBet;
    dontPassBet.max = tierData.maxBet;
}

function setQuickBet(strategy) {
    clearAllBets();
    const tierData = STADIUM_TIERS.find(t => t.name === crapsStadiumGame.currentTier);
    const baseBet = tierData.minBet;
    
    switch (strategy) {
        case 'conservative':
            document.getElementById('passLineBet').value = baseBet;
            document.getElementById('oddsBet').value = baseBet * 2;
            break;
        case 'aggressive':
            [6, 8].forEach(num => {
                document.getElementById(`place${num}Bet`).value = baseBet;
            });
            break;
        case 'proposition':
            document.getElementById('anyCrapsBet').value = Math.floor(baseBet / 4);
            document.getElementById('any7Bet').value = Math.floor(baseBet / 4);
            document.getElementById('yo11Bet').value = Math.floor(baseBet / 8);
            break;
        case 'fire':
            document.getElementById('passLineBet').value = baseBet;
            document.getElementById('fireBet').value = Math.min(1000, baseBet);
            break;
    }
}

function rollStadiumDice() {
    // Collect all bets
    const totalBetAmount = collectAllBets();
    
    if (totalBetAmount === 0) {
        document.getElementById('gameStatus').textContent = 'Place a bet first!';
        return;
    }
    
    if (balance < totalBetAmount) {
        document.getElementById('gameStatus').textContent = 'Insufficient balance!';
        return;
    }
    
    // Deduct bets from balance
    balance -= totalBetAmount;
    crapsStadiumGame.sessionStats.totalWagered += totalBetAmount;
    updateBalance();
    
    // Roll dice with animation
    animateDiceRoll(() => {
        // Use biased dice rolling
        const diceResult = rollBiasedDice();
        crapsStadiumGame.dice1 = diceResult.dice1;
        crapsStadiumGame.dice2 = diceResult.dice2;
        const total = diceResult.total;
        
        // Update display
        document.getElementById('dice1').textContent = crapsStadiumGame.dice1;
        document.getElementById('dice2').textContent = crapsStadiumGame.dice2;
        document.getElementById('rollTotal').textContent = total;
        
        // Update tracking
        updateRollTracking(total);
        crapsStadiumGame.rollHistory.push(total);
        crapsStadiumGame.sessionStats.rollsTotal++;
        crapsStadiumGame.shooterRoll++;
        crapsStadiumGame.rollStreak++;
        
        // Process the roll
        processStadiumRoll(total);
        
        // Update displays
        updateStadiumDisplay();
        updateStadiumStats();
        updateHotColdDisplay();
        
        // Auto roll if enabled
        if (crapsStadiumGame.autoRoll) {
            const speed = parseInt(document.getElementById('autoRollSpeed').value);
            setTimeout(() => {
                if (crapsStadiumGame.autoRoll) {
                    rollStadiumDice();
                }
            }, speed);
        }
    });
}

function animateDiceRoll(callback) {
    const dice1 = document.getElementById('dice1');
    const dice2 = document.getElementById('dice2');
    
    let animationCount = 0;
    const maxAnimations = 10;
    
    const animate = () => {
        dice1.textContent = Math.floor(Math.random() * 6) + 1;
        dice2.textContent = Math.floor(Math.random() * 6) + 1;
        
        animationCount++;
        if (animationCount < maxAnimations) {
            setTimeout(animate, 100);
        } else {
            callback();
        }
    };
    
    animate();
}

function collectAllBets() {
    let total = 0;
    
    // Line bets
    total += parseInt(document.getElementById('passLineBet').value) || 0;
    total += parseInt(document.getElementById('dontPassBet').value) || 0;
    total += parseInt(document.getElementById('oddsBet').value) || 0;
    total += parseInt(document.getElementById('fieldBet').value) || 0;
    total += parseInt(document.getElementById('comeBet').value) || 0;
    total += parseInt(document.getElementById('dontComeBet').value) || 0;
    total += parseInt(document.getElementById('fireBet').value) || 0;
    
    // Place bets
    [4, 5, 6, 8, 9, 10].forEach(num => {
        total += parseInt(document.getElementById(`place${num}Bet`).value) || 0;
    });
    
    // Hard ways
    [4, 6, 8, 10].forEach(num => {
        total += parseInt(document.getElementById(`hard${num}Bet`).value) || 0;
    });
    
    // Proposition bets
    total += parseInt(document.getElementById('anyCrapsBet').value) || 0;
    total += parseInt(document.getElementById('any7Bet').value) || 0;
    total += parseInt(document.getElementById('yo11Bet').value) || 0;
    total += parseInt(document.getElementById('ace2Bet').value) || 0;
    total += parseInt(document.getElementById('ace12Bet').value) || 0;
    total += parseInt(document.getElementById('hornBet').value) || 0;
    
    return total;
}

function processStadiumRoll(total) {
    const tierData = STADIUM_TIERS.find(t => t.name === crapsStadiumGame.currentTier);
    let totalWinnings = 0;
    let results = [];
    
    // Apply house edge penalty to all winnings
    const houseEdgePenalty = 1 - tierData.houseEdge;
    
    // Come out roll logic
    if (crapsStadiumGame.gamePhase === 'comeOut') {
        if ([7, 11].includes(total)) {
            // Pass line wins (REDUCED PAYOUT)
            const passLineBet = parseInt(document.getElementById('passLineBet').value) || 0;
            if (passLineBet > 0) {
                const baseWinnings = passLineBet * HIGH_ROLLER_LIMITS.PASS_LINE.payout;
                const winnings = (passLineBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
                totalWinnings += winnings;
                results.push(`Pass Line wins: +${Math.floor(winnings - passLineBet)} GA`);
                crapsStadiumGame.sessionStats.passLineWins++;
            }
            
            results.push('Natural winner!');
        } else if ([2, 3, 12].includes(total)) {
            // Craps - Pass line loses, Don't pass wins (except 12) - REDUCED PAYOUT
            const dontPassBet = parseInt(document.getElementById('dontPassBet').value) || 0;
            if (dontPassBet > 0 && total !== 12) {
                const baseWinnings = dontPassBet * HIGH_ROLLER_LIMITS.DONT_PASS.payout;
                const winnings = (dontPassBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
                totalWinnings += winnings;
                results.push(`Don't Pass wins: +${Math.floor(winnings - dontPassBet)} GA`);
            }
            
            results.push('Craps!');
        } else {
            // Point established
            crapsStadiumGame.point = total;
            crapsStadiumGame.gamePhase = 'point';
            document.getElementById('gamePhaseDisplay').textContent = 'Point Phase';
            document.getElementById('pointDisplay').textContent = `Point: ${total}`;
            results.push(`Point ${total} established`);
        }
    } else {
        // Point phase
        if (total === crapsStadiumGame.point) {
            // Point made (VERY RARE due to biased dice)
            const passLineBet = parseInt(document.getElementById('passLineBet').value) || 0;
            const oddsBet = parseInt(document.getElementById('oddsBet').value) || 0;
            
            if (passLineBet > 0) {
                const baseWinnings = passLineBet * HIGH_ROLLER_LIMITS.PASS_LINE.payout;
                const winnings = (passLineBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
                totalWinnings += winnings;
                results.push(`Pass Line wins: +${Math.floor(winnings - passLineBet)} GA`);
            }
            
            if (oddsBet > 0) {
                const oddsMultiplier = getReducedOddsMultiplier(crapsStadiumGame.point);
                const baseWinnings = oddsBet * oddsMultiplier;
                const winnings = (oddsBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
                totalWinnings += winnings;
                results.push(`Odds bet wins: +${Math.floor(winnings - oddsBet)} GA`);
            }
            
            // Fire bet progression (MUCH HARDER)
            updateFireBet();
            
            crapsStadiumGame.sessionStats.pointsMade++;
            crapsStadiumGame.maxRollStreak = Math.max(crapsStadiumGame.maxRollStreak, crapsStadiumGame.rollStreak);
            
            // Reset for new come out
            crapsStadiumGame.gamePhase = 'comeOut';
            crapsStadiumGame.point = null;
            crapsStadiumGame.shooterRoll = 0;
            document.getElementById('gamePhaseDisplay').textContent = 'Come Out Roll';
            document.getElementById('pointDisplay').textContent = '';
            
            results.push(`Point ${total} made!`);
        } else if (total === 7) {
            // Seven out (VERY COMMON due to biased dice)
            const dontPassBet = parseInt(document.getElementById('dontPassBet').value) || 0;
            if (dontPassBet > 0) {
                const baseWinnings = dontPassBet * HIGH_ROLLER_LIMITS.DONT_PASS.payout;
                const winnings = (dontPassBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
                totalWinnings += winnings;
                results.push(`Don't Pass wins: +${Math.floor(winnings - dontPassBet)} GA`);
            }
            
            crapsStadiumGame.sessionStats.sevenOuts++;
            crapsStadiumGame.sessionStats.longestRoll = Math.max(crapsStadiumGame.sessionStats.longestRoll, crapsStadiumGame.shooterRoll);
            
            // Reset fire bet
            crapsStadiumGame.activeBets.fireBet.points = [];
            
            // Reset for new come out
            crapsStadiumGame.gamePhase = 'comeOut';
            crapsStadiumGame.point = null;
            crapsStadiumGame.shooterRoll = 0;
            crapsStadiumGame.rollStreak = 0;
            document.getElementById('gamePhaseDisplay').textContent = 'Come Out Roll';
            document.getElementById('pointDisplay').textContent = '';
            
            results.push('Seven out!');
        }
    }
    
    // Check field bet (REDUCED PAYOUTS)
    const fieldWinnings = checkReducedFieldBet(total, tierData, houseEdgePenalty);
    totalWinnings += fieldWinnings.amount;
    if (fieldWinnings.result) results.push(fieldWinnings.result);
    
    // Check place bets (REDUCED PAYOUTS)
    const placeWinnings = checkReducedPlaceBets(total, tierData, houseEdgePenalty);
    totalWinnings += placeWinnings.amount;
    results.push(...placeWinnings.results);
    
    // Check hard ways (REDUCED PAYOUTS)
    const hardWayWinnings = checkReducedHardWays(total, tierData, houseEdgePenalty);
    totalWinnings += hardWayWinnings.amount;
    results.push(...hardWayWinnings.results);
    
    // Check proposition bets (REDUCED PAYOUTS)
    const propWinnings = checkReducedPropositionBets(total, tierData, houseEdgePenalty);
    totalWinnings += propWinnings.amount;
    results.push(...propWinnings.results);
    
    // Add winnings to balance
    balance += totalWinnings;
    updateBalance();
    
    // Update session stats
    crapsStadiumGame.sessionStats.totalWon += totalWinnings;
    crapsStadiumGame.sessionStats.biggestWin = Math.max(crapsStadiumGame.sessionStats.biggestWin, totalWinnings);
    crapsStadiumGame.prestigePoints += Math.floor(totalWinnings / 500); // Reduced prestige gain
    
    // Display results
    if (results.length > 0) {
        document.getElementById('gameResult').innerHTML = results.join('<br>');
    } else {
        document.getElementById('gameResult').textContent = `Rolled ${total}`;
    }
    
    document.getElementById('gameStatus').textContent = totalWinnings > 0 ? 
        `Total winnings: ${Math.floor(totalWinnings)} GA` : 'House wins!';
}

function checkReducedFieldBet(total, tierData, houseEdgePenalty) {
    const fieldBet = parseInt(document.getElementById('fieldBet').value) || 0;
    if (fieldBet === 0) return { amount: 0, result: null };
    
    if ([3, 4, 9, 10, 11].includes(total)) {
        const baseWinnings = fieldBet * HIGH_ROLLER_LIMITS.FIELD.payout;
        const winnings = (fieldBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        return { amount: winnings, result: `Field wins: +${Math.floor(winnings - fieldBet)} GA` };
    } else if ([2, 12].includes(total)) {
        const baseWinnings = fieldBet * HIGH_ROLLER_LIMITS.FIELD.payout * 1.5; // Reduced from 2x
        const winnings = (fieldBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        return { amount: winnings, result: `Field (1.5x) wins: +${Math.floor(winnings - fieldBet)} GA` };
    }
    
    return { amount: 0, result: null };
}

function checkReducedPlaceBets(total, tierData, houseEdgePenalty) {
    let totalWinnings = 0;
    let results = [];
    
    if ([4, 5, 6, 8, 9, 10].includes(total)) {
        const placeBet = parseInt(document.getElementById(`place${total}Bet`).value) || 0;
        if (placeBet > 0) {
            // Heavily reduced multipliers
            const multipliers = { 4: 1.2, 5: 1.0, 6: 0.83, 8: 0.83, 9: 1.0, 10: 1.2 };
            const baseWinnings = placeBet * multipliers[total];
            const winnings = (placeBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
            totalWinnings += winnings;
            results.push(`Place ${total} wins: +${Math.floor(winnings - placeBet)} GA`);
        }
    }
    
    return { amount: totalWinnings, results: results };
}

function checkReducedHardWays(total, tierData, houseEdgePenalty) {
    let totalWinnings = 0;
    let results = [];
    
    if ([4, 6, 8, 10].includes(total)) {
        const isHard = crapsStadiumGame.dice1 === crapsStadiumGame.dice2;
        const hardBet = parseInt(document.getElementById(`hard${total}Bet`).value) || 0;
        
        if (hardBet > 0) {
            if (isHard) {
                // Reduced multipliers
                const multipliers = { 4: 5, 6: 6, 8: 6, 10: 5 };
                const baseWinnings = hardBet * multipliers[total];
                const winnings = (hardBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
                totalWinnings += winnings;
                results.push(`Hard ${total} wins: +${Math.floor(winnings - hardBet)} GA`);
                crapsStadiumGame.sessionStats.hardWayHits++;
            }
        }
    }
    
    return { amount: totalWinnings, results: results };
}

function checkReducedPropositionBets(total, tierData, houseEdgePenalty) {
    let totalWinnings = 0;
    let results = [];
    
    // Any Craps (2, 3, 12) - REDUCED PAYOUT
    const anyCrapsBet = parseInt(document.getElementById('anyCrapsBet').value) || 0;
    if (anyCrapsBet > 0 && [2, 3, 12].includes(total)) {
        const baseWinnings = anyCrapsBet * 5; // Reduced from 7:1
        const winnings = (anyCrapsBet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        totalWinnings += winnings;
        results.push(`Any Craps wins: +${Math.floor(winnings - anyCrapsBet)} GA`);
    }
    
    // Any 7 - REDUCED PAYOUT
    const any7Bet = parseInt(document.getElementById('any7Bet').value) || 0;
    if (any7Bet > 0 && total === 7) {
        const baseWinnings = any7Bet * 3; // Reduced from 4:1
        const winnings = (any7Bet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        totalWinnings += winnings;
        results.push(`Any 7 wins: +${Math.floor(winnings - any7Bet)} GA`);
    }
    
    // Yo (11) - REDUCED PAYOUT
    const yo11Bet = parseInt(document.getElementById('yo11Bet').value) || 0;
    if (yo11Bet > 0 && total === 11) {
        const baseWinnings = yo11Bet * 10; // Reduced from 15:1
        const winnings = (yo11Bet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        totalWinnings += winnings;
        results.push(`Yo-leven wins: +${Math.floor(winnings - yo11Bet)} GA`);
    }
    
    // Ace-Deuce (3) - REDUCED PAYOUT
    const ace2Bet = parseInt(document.getElementById('ace2Bet').value) || 0;
    if (ace2Bet > 0 && total === 3) {
        const baseWinnings = ace2Bet * 10; // Reduced from 15:1
        const winnings = (ace2Bet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        totalWinnings += winnings;
        results.push(`Ace-Deuce wins: +${Math.floor(winnings - ace2Bet)} GA`);
    }
    
    // Boxcars (12) - REDUCED PAYOUT
    const ace12Bet = parseInt(document.getElementById('ace12Bet').value) || 0;
    if (ace12Bet > 0 && total === 12) {
        const baseWinnings = ace12Bet * 20; // Reduced from 30:1
        const winnings = (ace12Bet + baseWinnings) * tierData.bonusMultiplier * houseEdgePenalty;
        totalWinnings += winnings;
        results.push(`Boxcars wins: +${Math.floor(winnings - ace12Bet)} GA`);
    }
    
    return { amount: totalWinnings, results: results };
}

function updateFireBet() {
    const fireBet = parseInt(document.getElementById('fireBet').value) || 0;
    if (fireBet === 0) return;
    
    if (!crapsStadiumGame.activeBets.fireBet.points.includes(crapsStadiumGame.point)) {
        crapsStadiumGame.activeBets.fireBet.points.push(crapsStadiumGame.point);
        
        const pointsCount = crapsStadiumGame.activeBets.fireBet.points.length;
        document.getElementById('firePoints').textContent = pointsCount;
        
        // Fire bet payouts (MUCH HARDER - need 6+ points)
        if (pointsCount >= 6) {
            const multipliers = { 6: 25, 7: 100, 8: 500 };
            const multiplier = multipliers[Math.min(pointsCount, 8)] || 500;
            const winnings = fireBet * multiplier;
            
            balance += winnings;
            updateBalance();
            
            crapsStadiumGame.sessionStats.fireBetHits++;
            document.getElementById('gameResult').innerHTML += `<br>🔥 FIRE BET PAYS ${multiplier}:1! +${winnings} GA`;
        }
    }
}

function getReducedOddsMultiplier(point) {
    // Reduced odds multipliers
    const multipliers = { 4: 1.2, 5: 1.0, 6: 0.8, 8: 0.8, 9: 1.0, 10: 1.2 };
    return multipliers[point] || 1;
}

function updateRollTracking(total) {
    // Update hot/cold tracking
    if (!crapsStadiumGame.hotNumbers[total]) {
        crapsStadiumGame.hotNumbers[total] = 0;
    }
    crapsStadiumGame.hotNumbers[total]++;
    
    // Keep only last 50 rolls for hot/cold calculation
    if (crapsStadiumGame.rollHistory.length > 50) {
        crapsStadiumGame.rollHistory.shift();
    }
}

function initializeHotColdTracking() {
    for (let i = 2; i <= 12; i++) {
        crapsStadiumGame.hotNumbers[i] = 0;
    }
}

function updateHotColdDisplay() {
    if (crapsStadiumGame.rollHistory.length < 10) return;
    
    // Calculate frequencies
    const frequencies = {};
    crapsStadiumGame.rollHistory.slice(-20).forEach(roll => {
        frequencies[roll] = (frequencies[roll] || 0) + 1;
    });
    
    // Find hot and cold numbers
    const sorted = Object.entries(frequencies).sort((a, b) => b[1] - a[1]);
    const hot = sorted.slice(0, 3).map(([num, freq]) => `${num}(${freq})`).join(' ');
    const cold = sorted.slice(-3).map(([num, freq]) => `${num}(${freq})`).join(' ');
    
    document.getElementById('hotNumbers').textContent = hot || '-';
    document.getElementById('coldNumbers').textContent = cold || '-';
    document.getElementById('recentRolls').textContent = crapsStadiumGame.rollHistory.slice(-10).join(' ');
}

function clearAllBets() {
    // Clear all bet inputs
    document.querySelectorAll('input[type="number"]').forEach(input => {
        if (input.id.includes('Bet')) {
            input.value = 0;
        }
    });
}

function toggleAutoRoll() {
    crapsStadiumGame.autoRoll = document.getElementById('autoRoll').checked;
}

function togglePredictiveMode() {
    crapsStadiumGame.predictiveMode = document.getElementById('predictiveMode').checked;
    
    if (crapsStadiumGame.predictiveMode) {
        // Show predictive analytics
        showPredictiveAnalytics();
    }
}

function showPredictiveAnalytics() {
    // Simple prediction based on recent trends
    const recentRolls = crapsStadiumGame.rollHistory.slice(-10);
    if (recentRolls.length < 5) return;
    
    const avg = recentRolls.reduce((a, b) => a + b, 0) / recentRolls.length;
    const prediction = avg > 7 ? 'Trending HIGH' : avg < 7 ? 'Trending LOW' : 'Balanced';
    
    document.getElementById('gameStatus').textContent += ` | Prediction: ${prediction}`;
}

function updateStadiumDisplay() {
    document.getElementById('stadiumLevel').textContent = crapsStadiumGame.stadiumLevel;
    document.getElementById('prestigePoints').textContent = crapsStadiumGame.prestigePoints.toLocaleString();
    document.getElementById('rollStreak').textContent = crapsStadiumGame.rollStreak;
    
    // Check for level up (MUCH HARDER)
    const newLevel = Math.floor(crapsStadiumGame.prestigePoints / 10000) + 1; // Increased requirement
    if (newLevel > crapsStadiumGame.stadiumLevel) {
        crapsStadiumGame.stadiumLevel = newLevel;
        document.getElementById('gameStatus').textContent = `🎉 STADIUM LEVEL UP! Now Level ${newLevel}!`;
    }
}

function updateStadiumStats() {
    document.getElementById('totalRolls').textContent = crapsStadiumGame.sessionStats.rollsTotal;
    document.getElementById('pointsMade').textContent = crapsStadiumGame.sessionStats.pointsMade;
    document.getElementById('sevenOuts').textContent = crapsStadiumGame.sessionStats.sevenOuts;
    document.getElementById('longestRoll').textContent = crapsStadiumGame.sessionStats.longestRoll;
    document.getElementById('totalWagered').textContent = crapsStadiumGame.sessionStats.totalWagered.toLocaleString();
    document.getElementById('biggestWin').textContent = crapsStadiumGame.sessionStats.biggestWin.toLocaleString();
    
    // Calculate and display win rate
    const winRate = crapsStadiumGame.sessionStats.rollsTotal > 0 ? 
        ((crapsStadiumGame.sessionStats.totalWon / crapsStadiumGame.sessionStats.totalWagered) * 100) : 0;
    document.getElementById('winRate').textContent = `${Math.max(0, winRate).toFixed(1)}%`;
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCrapsStadiumGame();
});