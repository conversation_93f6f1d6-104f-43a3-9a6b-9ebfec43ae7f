// Texas Hold'em: All-In Arena - Game Logic
// Advanced poker game with multiple AI opponents and tournament-style play

// Game state management
let balance = 1000;
let gameState = {
    isPlaying: false,
    gameMode: 'tournament', // 'tournament' or 'cash'
    phase: 'preflop', // 'preflop', 'flop', 'turn', 'river', 'showdown'
    round: 1,
    maxRounds: 10,
    pot: 0,
    currentBet: 0,
    minRaise: 0,
    deck: [],
    communityCards: [],
    players: [],
    currentPlayerIndex: 0,
    dealerIndex: 0,
    smallBlind: 10,
    bigBlind: 20,
    actionHistory: [],
    tournamentPrizes: [500, 300, 200] // 1st, 2nd, 3rd place
};

// Player class for managing player data
class Player {
    constructor(name, chips, isHuman = false, aiPersonality = 'balanced') {
        this.name = name;
        this.chips = chips;
        this.isHuman = isHuman;
        this.cards = [];
        this.currentBet = 0;
        this.totalBet = 0;
        this.isActive = true;
        this.isFolded = false;
        this.isAllIn = false;
        this.position = '';
        this.aiPersonality = aiPersonality; // 'aggressive', 'conservative', 'balanced', 'bluffer'
        this.handStrength = 0;
        this.bestHand = null;
    }

    bet(amount) {
        const actualBet = Math.min(amount, this.chips);
        this.chips -= actualBet;
        this.currentBet += actualBet;
        this.totalBet += actualBet;
        gameState.pot += actualBet;

        if (this.chips === 0) {
            this.isAllIn = true;
        }

        return actualBet;
    }

    fold() {
        this.isFolded = true;
        this.isActive = false;
    }

    reset() {
        this.cards = [];
        this.currentBet = 0;
        this.totalBet = 0;
        this.isFolded = false;
        this.isAllIn = false;
        this.handStrength = 0;
        this.bestHand = null;
    }
}

// Initialize game
function initializeGame() {
    updateBalance();
    loadGameInterface();
}

// Update balance display
function updateBalance() {
    const balanceElement = document.getElementById('balanceDisplay');
    if (balanceElement) {
        balanceElement.innerHTML =
            `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
    }
}

// Load the main game interface
function loadGameInterface() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls Panel -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">GAME SETUP</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="tournament">Tournament (10 Rounds)</option>
                            <option value="cash">Cash Game</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BUY-IN AMOUNT</label>
                        <input type="number" id="buyInAmount" value="200" min="100" max="${balance}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">OPPONENTS</label>
                        <select id="opponentCount" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="3">3 AI Players</option>
                            <option value="5">5 AI Players</option>
                            <option value="7">7 AI Players</option>
                        </select>
                    </div>

                    <button id="startGame" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START GAME
                    </button>
                </div>

                <!-- Player Actions Panel -->
                <div id="actionPanel" class="bg-black/30 p-6 rounded-xl border border-purple-500/30 hidden">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">YOUR ACTIONS</h4>

                    <div class="space-y-3">
                        <button id="foldBtn" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            FOLD
                        </button>
                        <button id="checkCallBtn" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            CHECK/CALL
                        </button>
                        <div class="flex space-x-2">
                            <button id="raiseBtn" class="flex-1 py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                RAISE
                            </button>
                            <button id="allInBtn" class="flex-1 py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                                ALL-IN
                            </button>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm mb-2 text-gray-300">RAISE AMOUNT</label>
                            <input type="range" id="raiseSlider" min="0" max="1000" value="50"
                                   class="w-full mb-2">
                            <div class="flex justify-between text-sm text-gray-400">
                                <span id="minRaise">Min: $20</span>
                                <span id="raiseAmount">$50</span>
                                <span id="maxRaise">Max: $1000</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Game Info Panel -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-6">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">GAME INFO</h5>
                    <div class="text-sm space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Round:</span>
                            <span id="roundInfo" class="text-white">1/10</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Pot:</span>
                            <span id="potAmount" class="text-green-400">$0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Small Blind:</span>
                            <span id="smallBlindAmount" class="text-yellow-400">$10</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Big Blind:</span>
                            <span id="bigBlindAmount" class="text-yellow-400">$20</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Your Hand:</span>
                            <span id="handStrength" class="text-blue-400">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="gameTable" class="relative bg-gradient-to-br from-green-900 to-emerald-900 rounded-lg p-8 min-h-[600px]">
                        <!-- Welcome Screen -->
                        <div id="welcomeScreen" class="flex flex-col items-center justify-center h-full text-center">
                            <h3 class="text-4xl font-bold text-purple-400 mb-4 neon-glow">
                                Texas Hold'em: All-In Arena
                            </h3>
                            <p class="text-xl text-gray-300 mb-8">
                                Experience the ultimate poker tournament with advanced AI opponents
                            </p>
                            <div class="grid grid-cols-2 gap-6 text-left">
                                <div class="bg-black/30 p-4 rounded-lg">
                                    <h4 class="text-lg font-bold text-green-400 mb-2">Tournament Mode</h4>
                                    <ul class="text-sm text-gray-300 space-y-1">
                                        <li>• 10 rounds of intense poker</li>
                                        <li>• Increasing blinds each round</li>
                                        <li>• Prize pool distribution</li>
                                        <li>• Elimination format</li>
                                    </ul>
                                </div>
                                <div class="bg-black/30 p-4 rounded-lg">
                                    <h4 class="text-lg font-bold text-blue-400 mb-2">Cash Game</h4>
                                    <ul class="text-sm text-gray-300 space-y-1">
                                        <li>• Continuous play</li>
                                        <li>• Fixed blinds</li>
                                        <li>• Leave anytime</li>
                                        <li>• Rebuy options</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Game Table Layout (Hidden initially) -->
                        <div id="tableLayout" class="hidden">
                            <!-- Community Cards Area -->
                            <div class="absolute top-8 left-1/2 transform -translate-x-1/2">
                                <div class="text-center">
                                    <div class="text-sm text-yellow-400 mb-2">COMMUNITY CARDS</div>
                                    <div id="communityCards" class="flex space-x-2 justify-center">
                                        <!-- Community cards will appear here -->
                                    </div>
                                    <div class="mt-2">
                                        <span class="text-lg font-bold text-green-400">POT: $</span>
                                        <span id="potDisplay" class="text-lg font-bold text-green-400">0</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Players positioned around the table -->
                            <div id="playersContainer">
                                <!-- Players will be positioned here dynamically -->
                            </div>

                            <!-- Game Phase and Status -->
                            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                                <div id="gamePhase" class="text-2xl font-bold text-purple-400 mb-2">Pre-Flop</div>
                                <div id="gameStatus" class="text-lg text-gray-300">Waiting to start...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    initializeEventListeners();
}

// Initialize event listeners
function initializeEventListeners() {
    document.getElementById('startGame').addEventListener('click', startNewGame);
    document.getElementById('foldBtn').addEventListener('click', () => playerAction('fold'));
    document.getElementById('checkCallBtn').addEventListener('click', () => playerAction('check_call'));
    document.getElementById('raiseBtn').addEventListener('click', () => playerAction('raise'));
    document.getElementById('allInBtn').addEventListener('click', () => playerAction('all_in'));

    // Raise slider
    const raiseSlider = document.getElementById('raiseSlider');
    raiseSlider.addEventListener('input', updateRaiseAmount);
}

// Update raise amount display
function updateRaiseAmount() {
    const slider = document.getElementById('raiseSlider');
    const amount = parseInt(slider.value);
    document.getElementById('raiseAmount').textContent = `$${amount}`;
}

// Start a new game
function startNewGame() {
    const buyIn = parseInt(document.getElementById('buyInAmount').value);
    const opponentCount = parseInt(document.getElementById('opponentCount').value);
    const mode = document.getElementById('gameMode').value;

    if (buyIn > balance) {
        alert('Insufficient balance for buy-in!');
        return;
    }

    // Deduct buy-in from balance
    balance -= buyIn;
    updateBalance();

    // Initialize game state
    gameState.isPlaying = true;
    gameState.gameMode = mode;
    gameState.round = 1;
    gameState.pot = 0;
    gameState.currentBet = 0;
    gameState.phase = 'preflop';
    gameState.players = [];
    gameState.actionHistory = [];

    // Create players
    createPlayers(buyIn, opponentCount);

    // Setup table
    setupTable();

    // Start first hand
    startNewHand();
}

// Create players for the game
function createPlayers(buyIn, opponentCount) {
    // Add human player
    const humanPlayer = new Player('You', buyIn, true);
    gameState.players.push(humanPlayer);

    // AI personalities and names
    const aiProfiles = [
        { name: 'Alex "The Shark"', personality: 'aggressive' },
        { name: 'Maya "Ice Queen"', personality: 'conservative' },
        { name: 'Jake "Wild Card"', personality: 'bluffer' },
        { name: 'Sarah "Calculator"', personality: 'balanced' },
        { name: 'Tony "The Rock"', personality: 'conservative' },
        { name: 'Luna "Mystic"', personality: 'bluffer' },
        { name: 'Rex "Bulldozer"', personality: 'aggressive' }
    ];

    // Add AI players
    for (let i = 0; i < opponentCount; i++) {
        const profile = aiProfiles[i % aiProfiles.length];
        const aiPlayer = new Player(profile.name, buyIn, false, profile.personality);
        gameState.players.push(aiPlayer);
    }

    // Set initial dealer and blinds
    gameState.dealerIndex = 0;
    gameState.currentPlayerIndex = 0;
}

// Setup the visual table
function setupTable() {
    document.getElementById('welcomeScreen').classList.add('hidden');
    document.getElementById('tableLayout').classList.remove('hidden');
    document.getElementById('actionPanel').classList.remove('hidden');

    // Position players around the table
    positionPlayers();

    // Update game info
    updateGameInfo();
}

// Position players visually around the table
function positionPlayers() {
    const container = document.getElementById('playersContainer');
    container.innerHTML = '';

    const playerCount = gameState.players.length;
    const positions = getPlayerPositions(playerCount);

    gameState.players.forEach((player, index) => {
        const position = positions[index];
        const playerElement = createPlayerElement(player, index, position);
        container.appendChild(playerElement);
    });
}

// Get positions for players around the table
function getPlayerPositions(playerCount) {
    const positions = [];
    const centerX = 50; // Percentage
    const centerY = 50; // Percentage
    const radiusX = 35; // Horizontal radius
    const radiusY = 25; // Vertical radius

    for (let i = 0; i < playerCount; i++) {
        const angle = (i / playerCount) * 2 * Math.PI - Math.PI / 2; // Start from top
        const x = centerX + radiusX * Math.cos(angle);
        const y = centerY + radiusY * Math.sin(angle);

        positions.push({
            x: Math.max(5, Math.min(95, x)), // Keep within bounds
            y: Math.max(10, Math.min(90, y))
        });
    }

    return positions;
}

// Create player element for the table
function createPlayerElement(player, index, position) {
    const playerDiv = document.createElement('div');
    playerDiv.className = 'absolute transform -translate-x-1/2 -translate-y-1/2';
    playerDiv.style.left = `${position.x}%`;
    playerDiv.style.top = `${position.y}%`;
    playerDiv.id = `player-${index}`;

    const isDealer = index === gameState.dealerIndex;
    const isActive = index === gameState.currentPlayerIndex;

    playerDiv.innerHTML = `
        <div class="bg-black/50 p-3 rounded-lg border ${isActive ? 'border-yellow-400' : 'border-gray-500'} min-w-[120px] text-center">
            <div class="flex items-center justify-center mb-1">
                <span class="text-sm font-bold ${player.isHuman ? 'text-green-400' : 'text-blue-400'}">
                    ${player.name}
                </span>
                ${isDealer ? '<span class="ml-1 text-yellow-400">🎯</span>' : ''}
            </div>
            <div class="text-xs text-gray-300 mb-2">$${player.chips}</div>
            <div id="player-cards-${index}" class="flex justify-center space-x-1 mb-2">
                <!-- Player cards will appear here -->
            </div>
            <div class="text-xs">
                <div class="text-gray-400">Bet: $<span id="player-bet-${index}">${player.currentBet}</span></div>
                ${player.isFolded ? '<div class="text-red-400">FOLDED</div>' : ''}
                ${player.isAllIn ? '<div class="text-yellow-400">ALL-IN</div>' : ''}
            </div>
        </div>
    `;

    return playerDiv;
}

// Initialize deck
function initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

    gameState.deck = [];
    for (const suit of suits) {
        for (const rank of ranks) {
            gameState.deck.push({
                rank,
                suit,
                value: getCardValue(rank),
                id: `${rank}${suit}`
            });
        }
    }

    // Shuffle deck
    shuffleDeck();
}

// Shuffle the deck
function shuffleDeck() {
    for (let i = gameState.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
    }
}

// Get card value for comparison
function getCardValue(rank) {
    if (rank === 'A') return 14;
    if (rank === 'K') return 13;
    if (rank === 'Q') return 12;
    if (rank === 'J') return 11;
    return parseInt(rank);
}

// Start a new hand
function startNewHand() {
    // Reset players for new hand
    gameState.players.forEach(player => player.reset());

    // Clear community cards
    gameState.communityCards = [];
    gameState.pot = 0;
    gameState.currentBet = 0;
    gameState.phase = 'preflop';
    gameState.actionHistory = [];

    // Initialize deck
    initializeDeck();

    // Post blinds
    postBlinds();

    // Deal hole cards
    dealHoleCards();

    // Update display
    updateGameDisplay();

    // Start betting round
    startBettingRound();
}

// Post small and big blinds
function postBlinds() {
    const playerCount = gameState.players.length;
    const smallBlindIndex = (gameState.dealerIndex + 1) % playerCount;
    const bigBlindIndex = (gameState.dealerIndex + 2) % playerCount;

    // Post small blind
    const smallBlindPlayer = gameState.players[smallBlindIndex];
    smallBlindPlayer.bet(gameState.smallBlind);
    smallBlindPlayer.position = 'SB';

    // Post big blind
    const bigBlindPlayer = gameState.players[bigBlindIndex];
    bigBlindPlayer.bet(gameState.bigBlind);
    bigBlindPlayer.position = 'BB';

    gameState.currentBet = gameState.bigBlind;
    gameState.minRaise = gameState.bigBlind;

    // Set action to player after big blind
    gameState.currentPlayerIndex = (bigBlindIndex + 1) % playerCount;
}

// Deal hole cards to all players
function dealHoleCards() {
    // Deal 2 cards to each player
    for (let round = 0; round < 2; round++) {
        for (let i = 0; i < gameState.players.length; i++) {
            const card = gameState.deck.pop();
            gameState.players[i].cards.push(card);
        }
    }
}

// Start betting round
function startBettingRound() {
    // Check if human player needs to act
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];

    if (currentPlayer.isHuman && !currentPlayer.isFolded && !currentPlayer.isAllIn) {
        enablePlayerActions();
        updateGameStatus(`Your turn to act. Current bet: $${gameState.currentBet}`);
    } else {
        // AI player or folded/all-in player
        setTimeout(() => processAIAction(), 1000);
    }
}

// Enable player action buttons
function enablePlayerActions() {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];
    const callAmount = gameState.currentBet - currentPlayer.currentBet;

    // Update button text and states
    const checkCallBtn = document.getElementById('checkCallBtn');
    if (callAmount === 0) {
        checkCallBtn.textContent = 'CHECK';
    } else {
        checkCallBtn.textContent = `CALL $${callAmount}`;
    }

    // Update raise slider
    const raiseSlider = document.getElementById('raiseSlider');
    const minRaise = gameState.currentBet + gameState.minRaise;
    const maxRaise = currentPlayer.chips + currentPlayer.currentBet;

    raiseSlider.min = minRaise;
    raiseSlider.max = maxRaise;
    raiseSlider.value = Math.min(minRaise + gameState.bigBlind, maxRaise);

    document.getElementById('minRaise').textContent = `Min: $${minRaise}`;
    document.getElementById('maxRaise').textContent = `Max: $${maxRaise}`;
    updateRaiseAmount();

    // Enable/disable buttons based on game state
    document.getElementById('foldBtn').disabled = false;
    document.getElementById('checkCallBtn').disabled = callAmount > currentPlayer.chips;
    document.getElementById('raiseBtn').disabled = minRaise > currentPlayer.chips + currentPlayer.currentBet;
    document.getElementById('allInBtn').disabled = currentPlayer.chips === 0;
}

// Process player action
function playerAction(action) {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];

    switch (action) {
        case 'fold':
            currentPlayer.fold();
            gameState.actionHistory.push({ player: currentPlayer.name, action: 'fold' });
            break;

        case 'check_call':
            const callAmount = gameState.currentBet - currentPlayer.currentBet;
            if (callAmount > 0) {
                currentPlayer.bet(callAmount);
                gameState.actionHistory.push({ player: currentPlayer.name, action: 'call', amount: callAmount });
            } else {
                gameState.actionHistory.push({ player: currentPlayer.name, action: 'check' });
            }
            break;

        case 'raise':
            const raiseAmount = parseInt(document.getElementById('raiseSlider').value);
            const totalBet = raiseAmount - currentPlayer.currentBet;
            currentPlayer.bet(totalBet);
            gameState.currentBet = raiseAmount;
            gameState.minRaise = raiseAmount - (gameState.currentBet - gameState.minRaise);
            gameState.actionHistory.push({ player: currentPlayer.name, action: 'raise', amount: raiseAmount });
            break;

        case 'all_in':
            const allInAmount = currentPlayer.chips;
            currentPlayer.bet(allInAmount);
            if (currentPlayer.currentBet > gameState.currentBet) {
                gameState.currentBet = currentPlayer.currentBet;
            }
            gameState.actionHistory.push({ player: currentPlayer.name, action: 'all-in', amount: allInAmount });
            break;
    }

    // Disable action buttons
    disablePlayerActions();

    // Update display
    updateGameDisplay();

    // Move to next player
    nextPlayer();
}

// Disable player action buttons
function disablePlayerActions() {
    document.getElementById('foldBtn').disabled = true;
    document.getElementById('checkCallBtn').disabled = true;
    document.getElementById('raiseBtn').disabled = true;
    document.getElementById('allInBtn').disabled = true;
}

// Move to next player
function nextPlayer() {
    // Find next active player
    let nextIndex = (gameState.currentPlayerIndex + 1) % gameState.players.length;
    let foundActivePlayer = false;

    // Check if betting round is complete
    if (isBettingRoundComplete()) {
        proceedToNextPhase();
        return;
    }

    // Find next player who can act
    for (let i = 0; i < gameState.players.length; i++) {
        const player = gameState.players[nextIndex];
        if (!player.isFolded && !player.isAllIn) {
            // Check if player needs to act (hasn't matched current bet)
            if (player.currentBet < gameState.currentBet) {
                foundActivePlayer = true;
                break;
            }
        }
        nextIndex = (nextIndex + 1) % gameState.players.length;
    }

    if (foundActivePlayer) {
        gameState.currentPlayerIndex = nextIndex;
        setTimeout(() => startBettingRound(), 500);
    } else {
        proceedToNextPhase();
    }
}

// Check if betting round is complete
function isBettingRoundComplete() {
    const activePlayers = gameState.players.filter(p => !p.isFolded);

    if (activePlayers.length <= 1) {
        return true;
    }

    // Check if all active players have matched the current bet or are all-in
    return activePlayers.every(player =>
        player.currentBet === gameState.currentBet || player.isAllIn
    );
}

// Process AI player action
function processAIAction() {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];

    if (currentPlayer.isFolded || currentPlayer.isAllIn) {
        nextPlayer();
        return;
    }

    // Calculate AI decision
    const decision = calculateAIDecision(currentPlayer);

    // Execute decision
    executeAIDecision(currentPlayer, decision);

    // Update display
    updateGameDisplay();

    // Move to next player
    setTimeout(() => nextPlayer(), 1000);
}

// Calculate AI decision based on personality and hand strength
function calculateAIDecision(player) {
    const handStrength = evaluateHandStrength(player.cards, gameState.communityCards);
    const callAmount = gameState.currentBet - player.currentBet;
    const potOdds = callAmount / (gameState.pot + callAmount);

    // Base decision on hand strength
    let decision = { action: 'fold', amount: 0 };

    // Personality modifiers
    const personality = getPersonalityModifiers(player.aiPersonality);

    // Adjust hand strength based on personality
    const adjustedStrength = handStrength * personality.aggression;

    // Decision logic
    if (adjustedStrength > 0.8) {
        // Strong hand - likely to raise or call
        if (Math.random() < personality.raiseFrequency) {
            const raiseAmount = calculateRaiseAmount(player, personality);
            decision = { action: 'raise', amount: raiseAmount };
        } else {
            decision = { action: 'call', amount: callAmount };
        }
    } else if (adjustedStrength > 0.5) {
        // Medium hand - call or check
        if (callAmount === 0) {
            decision = { action: 'check', amount: 0 };
        } else if (potOdds < 0.3 || Math.random() < personality.callFrequency) {
            decision = { action: 'call', amount: callAmount };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    } else if (adjustedStrength > 0.2) {
        // Weak hand - mostly fold, sometimes bluff
        if (callAmount === 0) {
            decision = { action: 'check', amount: 0 };
        } else if (Math.random() < personality.bluffFrequency) {
            const raiseAmount = calculateRaiseAmount(player, personality);
            decision = { action: 'raise', amount: raiseAmount };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    } else {
        // Very weak hand - fold unless free
        if (callAmount === 0) {
            decision = { action: 'check', amount: 0 };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    }

    // Check if player can afford the action
    if (decision.amount > player.chips) {
        if (player.chips > 0) {
            decision = { action: 'all_in', amount: player.chips };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    }

    return decision;
}

// Get personality modifiers for AI
function getPersonalityModifiers(personality) {
    switch (personality) {
        case 'aggressive':
            return {
                aggression: 1.3,
                raiseFrequency: 0.4,
                callFrequency: 0.7,
                bluffFrequency: 0.3
            };
        case 'conservative':
            return {
                aggression: 0.7,
                raiseFrequency: 0.1,
                callFrequency: 0.4,
                bluffFrequency: 0.05
            };
        case 'bluffer':
            return {
                aggression: 1.1,
                raiseFrequency: 0.3,
                callFrequency: 0.5,
                bluffFrequency: 0.4
            };
        default: // balanced
            return {
                aggression: 1.0,
                raiseFrequency: 0.2,
                callFrequency: 0.6,
                bluffFrequency: 0.15
            };
    }
}

// Calculate raise amount for AI
function calculateRaiseAmount(player, personality) {
    const minRaise = gameState.currentBet + gameState.minRaise;
    const maxRaise = player.chips + player.currentBet;
    const potSize = gameState.pot;

    // Base raise on pot size and personality
    let raiseMultiplier = 0.5 + (personality.aggression - 1) * 0.3;
    raiseMultiplier += Math.random() * 0.3; // Add some randomness

    const targetRaise = Math.floor(potSize * raiseMultiplier);
    return Math.max(minRaise, Math.min(targetRaise, maxRaise));
}

// Process AI player action
function processAIAction() {
    const currentPlayer = gameState.players[gameState.currentPlayerIndex];

    if (currentPlayer.isFolded || currentPlayer.isAllIn) {
        nextPlayer();
        return;
    }

    // Calculate AI decision
    const decision = calculateAIDecision(currentPlayer);

    // Execute decision
    executeAIDecision(currentPlayer, decision);

    // Update display
    updateGameDisplay();

    // Move to next player
    setTimeout(() => nextPlayer(), 1000);
}

// Calculate AI decision based on personality and hand strength
function calculateAIDecision(player) {
    const handStrength = evaluateHandStrength(player.cards, gameState.communityCards);
    const callAmount = gameState.currentBet - player.currentBet;
    const potOdds = callAmount / (gameState.pot + callAmount);

    // Base decision on hand strength
    let decision = { action: 'fold', amount: 0 };

    // Personality modifiers
    const personality = getPersonalityModifiers(player.aiPersonality);

    // Adjust hand strength based on personality
    const adjustedStrength = handStrength * personality.aggression;

    // Decision logic
    if (adjustedStrength > 0.8) {
        // Strong hand - likely to raise or call
        if (Math.random() < personality.raiseFrequency) {
            const raiseAmount = calculateRaiseAmount(player, personality);
            decision = { action: 'raise', amount: raiseAmount };
        } else {
            decision = { action: 'call', amount: callAmount };
        }
    } else if (adjustedStrength > 0.5) {
        // Medium hand - call or check
        if (callAmount === 0) {
            decision = { action: 'check', amount: 0 };
        } else if (potOdds < 0.3 || Math.random() < personality.callFrequency) {
            decision = { action: 'call', amount: callAmount };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    } else if (adjustedStrength > 0.2) {
        // Weak hand - mostly fold, sometimes bluff
        if (callAmount === 0) {
            decision = { action: 'check', amount: 0 };
        } else if (Math.random() < personality.bluffFrequency) {
            const raiseAmount = calculateRaiseAmount(player, personality);
            decision = { action: 'raise', amount: raiseAmount };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    } else {
        // Very weak hand - fold unless free
        if (callAmount === 0) {
            decision = { action: 'check', amount: 0 };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    }

    // Check if player can afford the action
    if (decision.amount > player.chips) {
        if (player.chips > 0) {
            decision = { action: 'all_in', amount: player.chips };
        } else {
            decision = { action: 'fold', amount: 0 };
        }
    }

    return decision;
}

// Get personality modifiers for AI
function getPersonalityModifiers(personality) {
    switch (personality) {
        case 'aggressive':
            return {
                aggression: 1.3,
                raiseFrequency: 0.4,
                callFrequency: 0.7,
                bluffFrequency: 0.3
            };
        case 'conservative':
            return {
                aggression: 0.7,
                raiseFrequency: 0.1,
                callFrequency: 0.4,
                bluffFrequency: 0.05
            };
        case 'bluffer':
            return {
                aggression: 1.1,
                raiseFrequency: 0.3,
                callFrequency: 0.5,
                bluffFrequency: 0.4
            };
        default: // balanced
            return {
                aggression: 1.0,
                raiseFrequency: 0.2,
                callFrequency: 0.6,
                bluffFrequency: 0.15
            };
    }
}

// Calculate raise amount for AI
function calculateRaiseAmount(player, personality) {
    const minRaise = gameState.currentBet + gameState.minRaise;
    const maxRaise = player.chips + player.currentBet;
    const potSize = gameState.pot;

    // Base raise on pot size and personality
    let raiseMultiplier = 0.5 + (personality.aggression - 1) * 0.3;
    raiseMultiplier += Math.random() * 0.3; // Add some randomness

    const targetRaise = Math.floor(potSize * raiseMultiplier);
    return Math.max(minRaise, Math.min(targetRaise, maxRaise));
}

// Execute AI decision
function executeAIDecision(player, decision) {
    switch (decision.action) {
        case 'fold':
            player.fold();
            gameState.actionHistory.push({ player: player.name, action: 'fold' });
            updateGameStatus(`${player.name} folds`);
            break;

        case 'check':
            gameState.actionHistory.push({ player: player.name, action: 'check' });
            updateGameStatus(`${player.name} checks`);
            break;

        case 'call':
            player.bet(decision.amount);
            gameState.actionHistory.push({ player: player.name, action: 'call', amount: decision.amount });
            updateGameStatus(`${player.name} calls $${decision.amount}`);
            break;

        case 'raise':
            const totalBet = decision.amount - player.currentBet;
            player.bet(totalBet);
            gameState.currentBet = decision.amount;
            gameState.minRaise = decision.amount - (gameState.currentBet - gameState.minRaise);
            gameState.actionHistory.push({ player: player.name, action: 'raise', amount: decision.amount });
            updateGameStatus(`${player.name} raises to $${decision.amount}`);
            break;

        case 'all_in':
            const allInAmount = player.chips;
            player.bet(allInAmount);
            if (player.currentBet > gameState.currentBet) {
                gameState.currentBet = player.currentBet;
            }
            gameState.actionHistory.push({ player: player.name, action: 'all-in', amount: allInAmount });
            updateGameStatus(`${player.name} goes ALL-IN for $${allInAmount}!`);
            break;
    }
}