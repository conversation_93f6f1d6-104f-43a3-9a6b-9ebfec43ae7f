// Kenolandia - Cyber Keno with extremely low win rate (<10%)
let kenolandiaGame = {
    selectedNumbers: new Set(),
    drawnNumbers: [],
    cyberNumbers: new Set(),
    betAmount: 25,
    isPlaying: false,
    gameMode: 'classic', // classic, cyber, matrix, quantum
    multiplier: 1.0,
    difficulty: 'normal', // easy, normal, hard, nightmare
    autoPlay: false,
    autoPlayCount: 0,
    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        cyberHits: 0,
        perfectGames: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    lastResults: []
};

// Game constants with heavy house edge
const KENOLANDIA_MODES = {
    classic: { name: 'Classic', multiplier: 1.0, cyberChance: 0.03, houseEdge: 0.20 },
    cyber: { name: '<PERSON><PERSON>', multiplier: 0.85, cyberChance: 0.08, houseEdge: 0.25 },
    matrix: { name: 'Matrix', multiplier: 0.75, cyberChance: 0.12, houseEdge: 0.30 },
    quantum: { name: 'Quantum', multiplier: 0.65, cyberChance: 0.15, houseEdge: 0.35 }
};

const KENOLANDIA_DIFFICULTIES = {
    easy: { name: 'Easy', houseEdge: 0.15, drawBias: 0.15, payoutReduction: 0.10 },
    normal: { name: 'Normal', houseEdge: 0.25, drawBias: 0.25, payoutReduction: 0.20 },
    hard: { name: 'Hard', houseEdge: 0.35, drawBias: 0.35, payoutReduction: 0.30 },
    nightmare: { name: 'Nightmare', houseEdge: 0.45, drawBias: 0.45, payoutReduction: 0.40 }
};

// Initialize the game
function loadKenolandiaGame() {
    generateKenolandiaBoard();
    updateKenolandiaDisplay();
    setupKenolandiaEventListeners();
    
    // Load game content
    document.getElementById('gameContent').innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Control Panel -->
            <div class="bg-black/40 p-6 rounded-xl border border-cyan-500/30 backdrop-blur-sm">
                <h3 class="text-xl font-bold text-cyan-400 mb-4 font-mono">CYBER CONTROLS</h3>
                
                <div class="mb-4">
                    <label class="block text-cyan-300 mb-2 font-mono">BET AMOUNT</label>
                    <input type="number" id="kenolandiaBet" min="5" max="2000" step="5" value="25" 
                           class="w-full bg-black/60 text-cyan-400 border border-cyan-500/50 rounded p-2 font-mono">
                </div>
                
                <div class="mb-4">
                    <label class="block text-cyan-300 mb-2 font-mono">GAME MODE</label>
                    <select id="kenolandiaMode" class="w-full bg-black/60 text-cyan-400 border border-cyan-500/50 rounded p-2 font-mono">
                        <option value="classic">Classic</option>
                        <option value="cyber">Cyber (More Cyber Numbers)</option>
                        <option value="matrix">Matrix (High Risk)</option>
                        <option value="quantum">Quantum (Extreme Risk)</option>
                    </select>
                </div>
                
                <div class="mb-4">
                    <label class="block text-cyan-300 mb-2 font-mono">DIFFICULTY</label>
                    <select id="kenolandiaDifficulty" class="w-full bg-black/60 text-cyan-400 border border-cyan-500/50 rounded p-2 font-mono">
                        <option value="easy">Easy</option>
                        <option value="normal">Normal</option>
                        <option value="hard">Hard</option>
                        <option value="nightmare">Nightmare</option>
                    </select>
                </div>
                
                <div class="mb-4 p-3 bg-purple-900/30 rounded border border-purple-500/30">
                    <p class="text-cyan-300 mb-1 font-mono">Selected: <span id="kenolandiaSelectedCount">0</span>/15</p>
                    <p class="text-cyan-300 mb-1 font-mono">Max Payout: <span id="kenolandiaPayout">0</span> GA</p>
                    <p class="text-purple-300 text-sm font-mono">Cyber Multiplier: <span id="kenolandiaMultiplier">1.0x</span></p>
                </div>
                
                <div class="grid grid-cols-2 gap-2 mb-4">
                    <button id="playKenolandia" class="cyber-button-primary py-2 px-4 rounded font-mono font-bold disabled:opacity-50">
                        EXECUTE
                    </button>
                    <button id="quickPickKenolandia" class="cyber-button py-2 px-4 rounded font-mono font-bold">
                        QUICK
                    </button>
                    <button id="clearKenolandia" class="cyber-button-danger py-2 px-4 rounded font-mono font-bold">
                        CLEAR
                    </button>
                    <button id="autoPlayKenolandia" class="cyber-button py-2 px-4 rounded font-mono font-bold">
                        AUTO
                    </button>
                </div>
                
                <div class="p-3 bg-black/60 rounded border border-cyan-500/30">
                    <p id="kenolandiaStatus" class="text-center text-cyan-400 font-mono text-sm">Select numbers to begin</p>
                </div>
            </div>
            
            <!-- Keno Board -->
            <div class="col-span-2 bg-black/40 p-6 rounded-xl border border-cyan-500/30 backdrop-blur-sm">
                <h3 class="text-xl font-bold text-cyan-400 mb-4 font-mono text-center">KENOLANDIA MATRIX</h3>
                <div id="kenolandiaBoard" class="grid grid-cols-10 gap-1 max-w-2xl mx-auto"></div>
                
                <div class="mt-4 p-3 bg-purple-900/30 rounded border border-purple-500/30">
                    <h4 class="text-purple-400 font-mono mb-2">LAST DRAW RESULTS</h4>
                    <div id="kenolandiaLastDraw" class="flex flex-wrap gap-1"></div>
                </div>
            </div>
            
            <!-- Stats Panel -->
            <div class="bg-black/40 p-6 rounded-xl border border-cyan-500/30 backdrop-blur-sm">
                <h3 class="text-xl font-bold text-cyan-400 mb-4 font-mono">CYBER STATS</h3>
                
                <div class="space-y-3">
                    <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                        <p class="text-cyan-300 font-mono text-sm">Games: <span id="kenolandiaGamesPlayed">0</span></p>
                    </div>
                    <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                        <p class="text-cyan-300 font-mono text-sm">Win Rate: <span id="kenolandiaWinRate">0%</span></p>
                    </div>
                    <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                        <p class="text-cyan-300 font-mono text-sm">Wagered: <span id="kenolandiaTotalWagered">0</span></p>
                    </div>
                    <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                        <p class="text-cyan-300 font-mono text-sm">Won: <span id="kenolandiaTotalWon">0</span></p>
                    </div>
                    <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                        <p class="text-cyan-300 font-mono text-sm">Biggest: <span id="kenolandiaBiggestWin">0</span></p>
                    </div>
                    <div class="bg-purple-900/60 p-2 rounded border border-purple-500/30">
                        <p class="text-purple-300 font-mono text-sm">Cyber Hits: <span id="kenolandiaCyberHits">0</span></p>
                    </div>
                    <div class="bg-red-900/60 p-2 rounded border border-red-500/30">
                        <p class="text-red-300 font-mono text-sm">Loss Streak: <span id="kenolandiaLossStreak">0</span></p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h4 class="text-cyan-400 font-mono mb-2">RECENT RESULTS</h4>
                    <div id="kenolandiaRecentResults" class="grid grid-cols-5 gap-1"></div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 bg-black/40 p-4 rounded-xl border border-cyan-500/30 backdrop-blur-sm">
            <h3 class="text-xl font-bold text-cyan-400 mb-4 font-mono">PAYOUT TABLE</h3>
            <div class="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs font-mono">
                <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                    <p class="text-cyan-400">1 Hit: 3x</p>
                </div>
                <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                    <p class="text-cyan-400">2 Hits: 12x</p>
                </div>
                <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                    <p class="text-cyan-400">3 Hits: 42x</p>
                </div>
                <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                    <p class="text-cyan-400">4 Hits: 100x</p>
                </div>
                <div class="bg-black/60 p-2 rounded border border-cyan-500/30">
                    <p class="text-cyan-400">5+ Hits: 500x+</p>
                </div>
            </div>
            <p class="text-purple-400 text-xs mt-2 font-mono">* Cyber numbers provide 2x multiplier bonus</p>
        </div>
    `;
    
    generateKenolandiaBoard();
    setupKenolandiaEventListeners();
}

function generateKenolandiaBoard() {
    const board = document.getElementById('kenolandiaBoard');
    if (!board) return;
    
    board.innerHTML = '';
    
    for (let i = 1; i <= 80; i++) {
        const number = document.createElement('div');
        number.className = 'w-8 h-8 bg-black/60 border border-cyan-500/30 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all hover:bg-cyan-500/20 hover:scale-105';
        number.textContent = i;
        number.dataset.number = i;
        number.addEventListener('click', () => toggleKenolandiaNumber(i));
        board.appendChild(number);
    }
}

function setupKenolandiaEventListeners() {
    const playBtn = document.getElementById('playKenolandia');
    const clearBtn = document.getElementById('clearKenolandia');
    const quickBtn = document.getElementById('quickPickKenolandia');
    const autoBtn = document.getElementById('autoPlayKenolandia');
    const modeSelect = document.getElementById('kenolandiaMode');
    const difficultySelect = document.getElementById('kenolandiaDifficulty');
    const betInput = document.getElementById('kenolandiaBet');
    
    if (playBtn) playBtn.addEventListener('click', playKenolandia);
    if (clearBtn) clearBtn.addEventListener('click', clearKenolandia);
    if (quickBtn) quickBtn.addEventListener('click', quickPickKenolandia);
    if (autoBtn) autoBtn.addEventListener('click', toggleAutoPlay);
    
    if (modeSelect) {
        modeSelect.addEventListener('change', function() {
            kenolandiaGame.gameMode = this.value;
            updateKenolandiaDisplay();
        });
    }
    
    if (difficultySelect) {
        difficultySelect.addEventListener('change', function() {
            kenolandiaGame.difficulty = this.value;
            updateKenolandiaDisplay();
        });
    }
    
    if (betInput) {
        betInput.addEventListener('input', updateKenolandiaDisplay);
    }
}

function toggleKenolandiaNumber(number) {
    if (kenolandiaGame.isPlaying) return;
    
    const numberElement = document.querySelector(`[data-number="${number}"]`);
    if (!numberElement) return;
    
    if (kenolandiaGame.selectedNumbers.has(number)) {
        // Deselect
        kenolandiaGame.selectedNumbers.delete(number);
        numberElement.className = 'w-8 h-8 bg-black/60 border border-cyan-500/30 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all hover:bg-cyan-500/20 hover:scale-105';
    } else {
        // Select (max 15 numbers)
        if (kenolandiaGame.selectedNumbers.size < 15) {
            kenolandiaGame.selectedNumbers.add(number);
            numberElement.className = 'w-8 h-8 bg-gradient-to-br from-cyan-500 to-blue-600 border border-cyan-400 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all scale-105 text-white';
        }
    }
    
    updateKenolandiaDisplay();
}

function quickPickKenolandia() {
    if (kenolandiaGame.isPlaying) return;
    
    clearKenolandia();
    
    // Pick 5-12 random numbers
    const pickCount = Math.floor(Math.random() * 8) + 5;
    while (kenolandiaGame.selectedNumbers.size < pickCount) {
        const randomNumber = Math.floor(Math.random() * 80) + 1;
        if (!kenolandiaGame.selectedNumbers.has(randomNumber)) {
            toggleKenolandiaNumber(randomNumber);
        }
    }
}

function clearKenolandia() {
    if (kenolandiaGame.isPlaying) return;
    
    kenolandiaGame.selectedNumbers.clear();
    
    document.querySelectorAll('#kenolandiaBoard div').forEach(div => {
        div.className = 'w-8 h-8 bg-black/60 border border-cyan-500/30 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all hover:bg-cyan-500/20 hover:scale-105';
    });
    
    updateKenolandiaDisplay();
}

function toggleAutoPlay() {
    kenolandiaGame.autoPlay = !kenolandiaGame.autoPlay;
    const autoBtn = document.getElementById('autoPlayKenolandia');
    
    if (kenolandiaGame.autoPlay) {
        autoBtn.textContent = 'STOP';
        autoBtn.className = 'cyber-button-danger py-2 px-4 rounded font-mono font-bold';
        kenolandiaGame.autoPlayCount = 0;
        
        if (kenolandiaGame.selectedNumbers.size > 0) {
            playKenolandia();
        }
    } else {
        autoBtn.textContent = 'AUTO';
        autoBtn.className = 'cyber-button py-2 px-4 rounded font-mono font-bold';
    }
}

function updateKenolandiaDisplay() {
    const selectedCountEl = document.getElementById('kenolandiaSelectedCount');
    const payoutEl = document.getElementById('kenolandiaPayout');
    const multiplierEl = document.getElementById('kenolandiaMultiplier');
    
    if (selectedCountEl) selectedCountEl.textContent = kenolandiaGame.selectedNumbers.size;
    
    const playButton = document.getElementById('playKenolandia');
    if (playButton) playButton.disabled = kenolandiaGame.selectedNumbers.size === 0;
    
    // Calculate potential payout
    const betAmount = parseInt(document.getElementById('kenolandiaBet')?.value) || 0;
    const selectedCount = kenolandiaGame.selectedNumbers.size;
    const maxPayout = getKenolandiaMaxPayout(selectedCount);
    
    // Apply mode and difficulty penalties
    const modeData = KENOLANDIA_MODES[kenolandiaGame.gameMode];
    const difficultyData = KENOLANDIA_DIFFICULTIES[kenolandiaGame.difficulty];
    
    const totalMultiplier = modeData.multiplier * (1 - difficultyData.payoutReduction);
    const adjustedPayout = maxPayout * totalMultiplier;
    
    if (payoutEl) payoutEl.textContent = Math.floor(betAmount * adjustedPayout).toLocaleString();
    if (multiplierEl) multiplierEl.textContent = `${totalMultiplier.toFixed(2)}x`;
    
    // Update statistics
    updateKenolandiaStats();
}

function getKenolandiaMaxPayout(selectedCount) {
    // Extremely low payouts to ensure <10% win rate
    const payouts = {
        1: 3, 2: 12, 3: 42, 4: 100, 5: 500,
        6: 1000, 7: 2500, 8: 5000, 9: 10000, 10: 25000,
        11: 50000, 12: 100000, 13: 200000, 14: 500000, 15: 1000000
    };
    return payouts[selectedCount] || 0;
}

function playKenolandia() {
    const betAmount = parseInt(document.getElementById('kenolandiaBet')?.value) || 0;
    
    if (betAmount > balance || kenolandiaGame.selectedNumbers.size === 0) {
        if (betAmount > balance) {
            document.getElementById('kenolandiaStatus').textContent = 'Insufficient balance!';
        }
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    kenolandiaGame.isPlaying = true;
    kenolandiaGame.betAmount = betAmount;
    kenolandiaGame.stats.totalWagered += betAmount;
    
    document.getElementById('playKenolandia').disabled = true;
    document.getElementById('kenolandiaStatus').textContent = 'Initializing cyber matrix...';
    
    // Generate cyber numbers (special multiplier numbers)
    generateCyberNumbers();
    
    // Draw numbers with heavy bias against player
    drawBiasedKenolandiaNumbers();
    
    // Animate the draw
    animateKenolandiaDraw(betAmount);
}

function generateCyberNumbers() {
    kenolandiaGame.cyberNumbers.clear();
    const modeData = KENOLANDIA_MODES[kenolandiaGame.gameMode];
    
    // Generate cyber numbers (but make them rarely hit player selections)
    for (let i = 1; i <= 80; i++) {
        if (Math.random() < modeData.cyberChance) {
            // If player selected this number, reduce chance of it being cyber
            if (kenolandiaGame.selectedNumbers.has(i)) {
                if (Math.random() < 0.3) { // Only 30% chance if player selected
                    kenolandiaGame.cyberNumbers.add(i);
                }
            } else {
                kenolandiaGame.cyberNumbers.add(i);
            }
        }
    }
}

function drawBiasedKenolandiaNumbers() {
    kenolandiaGame.drawnNumbers = [];
    const difficultyData = KENOLANDIA_DIFFICULTIES[kenolandiaGame.difficulty];
    const drawBias = difficultyData.drawBias;
    
    // Create heavily biased pool against player selections
    const numberPool = [];
    for (let i = 1; i <= 80; i++) {
        let weight;
        
        if (kenolandiaGame.selectedNumbers.has(i)) {
            // Drastically reduce weight for selected numbers
            weight = Math.max(1, Math.floor(100 * (1 - drawBias)));
        } else {
            // Increase weight for non-selected numbers
            weight = Math.floor(100 * (1 + drawBias));
        }
        
        // Add number to pool based on weight
        for (let j = 0; j < weight; j++) {
            numberPool.push(i);
        }
    }
    
    // Shuffle pool
    for (let i = numberPool.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [numberPool[i], numberPool[j]] = [numberPool[j], numberPool[i]];
    }
    
    // Draw 20 unique numbers
    const drawnSet = new Set();
    let poolIndex = 0;
    
    while (drawnSet.size < 20 && poolIndex < numberPool.length) {
        drawnSet.add(numberPool[poolIndex]);
        poolIndex++;
    }
    
    kenolandiaGame.drawnNumbers = Array.from(drawnSet).sort((a, b) => a - b);
    
    // Ensure we have 20 numbers
    while (kenolandiaGame.drawnNumbers.length < 20) {
        const randomNumber = Math.floor(Math.random() * 80) + 1;
        if (!kenolandiaGame.drawnNumbers.includes(randomNumber)) {
            kenolandiaGame.drawnNumbers.push(randomNumber);
        }
    }
}

function animateKenolandiaDraw(betAmount) {
    let drawnSoFar = 0;
    const drawSpeed = kenolandiaGame.gameMode === 'quantum' ? 30 : 80;
    
    // Clear previous draw display
    const lastDrawEl = document.getElementById('kenolandiaLastDraw');
    if (lastDrawEl) lastDrawEl.innerHTML = '';
    
    const drawInterval = setInterval(() => {
        if (drawnSoFar < kenolandiaGame.drawnNumbers.length) {
            const drawnNumber = kenolandiaGame.drawnNumbers[drawnSoFar];
            const numberElement = document.querySelector(`[data-number="${drawnNumber}"]`);
            const isCyber = kenolandiaGame.cyberNumbers.has(drawnNumber);
            
            if (numberElement) {
                if (kenolandiaGame.selectedNumbers.has(drawnNumber)) {
                    // Hit - player selected this number
                    numberElement.className = isCyber ? 
                        'w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-600 border border-purple-300 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all scale-110 text-white animate-pulse' :
                        'w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 border border-green-400 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all scale-105 text-white';
                } else {
                    // Miss - number drawn but not selected
                    numberElement.className = isCyber ?
                        'w-8 h-8 bg-gradient-to-br from-purple-600 to-indigo-700 border border-purple-400 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all text-white' :
                        'w-8 h-8 bg-gradient-to-br from-red-500 to-pink-600 border border-red-400 rounded cursor-pointer flex items-center justify-center text-xs font-bold font-mono transition-all text-white';
                }
            }
            
            // Add to last draw display
            if (lastDrawEl) {
                const drawNumber = document.createElement('div');
                drawNumber.className = isCyber ? 
                    'w-6 h-6 bg-purple-600 border border-purple-400 rounded flex items-center justify-center text-xs font-bold text-white' :
                    'w-6 h-6 bg-cyan-600 border border-cyan-400 rounded flex items-center justify-center text-xs font-bold text-white';
                drawNumber.textContent = drawnNumber;
                lastDrawEl.appendChild(drawNumber);
            }
            
            drawnSoFar++;
        } else {
            clearInterval(drawInterval);
            calculateKenolandiaResult(betAmount);
        }
    }, drawSpeed);
}

function calculateKenolandiaResult(betAmount) {
    // Count hits and cyber hits
    let hits = 0;
    let cyberHits = 0;
    
    kenolandiaGame.selectedNumbers.forEach(number => {
        if (kenolandiaGame.drawnNumbers.includes(number)) {
            hits++;
            if (kenolandiaGame.cyberNumbers.has(number)) {
                cyberHits++;
            }
        }
    });
    
    // Calculate base payout
    const selectedCount = kenolandiaGame.selectedNumbers.size;
    const basePayout = getKenolandiaPayout(selectedCount, hits);
    
    // Apply mode and difficulty penalties
    const modeData = KENOLANDIA_MODES[kenolandiaGame.gameMode];
    const difficultyData = KENOLANDIA_DIFFICULTIES[kenolandiaGame.difficulty];
    
    // Cyber multiplier (minimal bonus to maintain low win rate)
    const cyberMultiplier = 1 + (cyberHits * 0.5); // Only 50% bonus per cyber hit
    
    // Calculate final winnings with heavy house edge
    const totalHouseEdge = modeData.houseEdge + difficultyData.houseEdge;
    const adjustedPayout = basePayout * modeData.multiplier * (1 - difficultyData.payoutReduction) * (1 - totalHouseEdge) * cyberMultiplier;
    const winnings = Math.floor(betAmount * adjustedPayout);
    
    // Update game stats
    kenolandiaGame.stats.gamesPlayed++;
    kenolandiaGame.stats.cyberHits += cyberHits;
    
    if (winnings > 0) {
        kenolandiaGame.stats.gamesWon++;
        kenolandiaGame.stats.totalWon += winnings;
        
        if (winnings > kenolandiaGame.stats.biggestWin) {
            kenolandiaGame.stats.biggestWin = winnings;
        }
        
        // Update win streak
        kenolandiaGame.streakData.currentWinStreak++;
        kenolandiaGame.streakData.currentLossStreak = 0;
        
        if (kenolandiaGame.streakData.currentWinStreak > kenolandiaGame.streakData.longestWinStreak) {
            kenolandiaGame.streakData.longestWinStreak = kenolandiaGame.streakData.currentWinStreak;
        }
    } else {
        // Update loss streak
        kenolandiaGame.streakData.currentLossStreak++;
        kenolandiaGame.streakData.currentWinStreak = 0;
        
        if (kenolandiaGame.streakData.currentLossStreak > kenolandiaGame.streakData.longestLossStreak) {
            kenolandiaGame.streakData.longestLossStreak = kenolandiaGame.streakData.currentLossStreak;
        }
    }
    
    // Add winnings to balance
    balance += winnings;
    updateBalance();
    
    // Update recent results
    kenolandiaGame.lastResults.unshift(hits > 0 ? 'win' : 'loss');
    if (kenolandiaGame.lastResults.length > 25) {
        kenolandiaGame.lastResults.pop();
    }
    
    // Display result
    let resultMessage = `<span class="text-cyan-300">Hits: ${hits}/${selectedCount}</span>`;
    
    if (cyberHits > 0) {
        resultMessage += ` <span class="text-purple-300">(${cyberHits} cyber!)</span>`;
    }
    
    if (winnings > 0) {
        resultMessage += ` <span class="text-green-400">Won: ${winnings} GA</span>`;
    } else {
        resultMessage += ` <span class="text-red-400">No payout</span>`;
    }
    
    document.getElementById('kenolandiaStatus').innerHTML = resultMessage;
    
    // Reset for next game
    kenolandiaGame.isPlaying = false;
    document.getElementById('playKenolandia').disabled = false;
    
    updateKenolandiaDisplay();
    
    // Continue auto play if enabled
    if (kenolandiaGame.autoPlay && kenolandiaGame.selectedNumbers.size > 0) {
        kenolandiaGame.autoPlayCount++;
        setTimeout(() => {
            if (kenolandiaGame.autoPlay && balance >= betAmount) {
                playKenolandia();
            } else {
                toggleAutoPlay(); // Stop auto play if insufficient balance
            }
        }, 1000);
    }
}

function getKenolandiaPayout(selected, hits) {
    // Extremely low payout table to ensure <10% win rate
    const payoutTable = {
        1: { 1: 3.0 },
        2: { 1: 0, 2: 12 },
        3: { 1: 0, 2: 2, 3: 42 },
        4: { 1: 0, 2: 1, 3: 5, 4: 100 },
        5: { 2: 0, 3: 2, 4: 20, 5: 500 },
        6: { 2: 0, 3: 1, 4: 8, 5: 90, 6: 1000 },
        7: { 3: 0, 4: 2, 5: 20, 6: 400, 7: 2500 },
        8: { 3: 0, 4: 1, 5: 10, 6: 80, 7: 1000, 8: 5000 },
        9: { 4: 0, 5: 5, 6: 50, 7: 300, 8: 2000, 9: 10000 },
        10: { 4: 0, 5: 2, 6: 20, 7: 100, 8: 500, 9: 5000, 10: 25000 },
        11: { 5: 1, 6: 10, 7: 50, 8: 200, 9: 2000, 10: 10000, 11: 50000 },
        12: { 5: 0, 6: 5, 7: 25, 8: 100, 9: 1000, 10: 5000, 11: 25000, 12: 100000 },
        13: { 6: 2, 7: 10, 8: 50, 9: 500, 10: 2000, 11: 10000, 12: 50000, 13: 200000 },
        14: { 6: 1, 7: 5, 8: 25, 9: 200, 10: 1000, 11: 5000, 12: 25000, 13: 100000, 14: 500000 },
        15: { 7: 2, 8: 10, 9: 100, 10: 500, 11: 2000, 12: 10000, 13: 50000, 14: 250000, 15: 1000000 }
    };
    
    return payoutTable[selected]?.[hits] || 0;
}

function updateKenolandiaStats() {
    const elements = {
        gamesPlayed: document.getElementById('kenolandiaGamesPlayed'),
        winRate: document.getElementById('kenolandiaWinRate'),
        totalWagered: document.getElementById('kenolandiaTotalWagered'),
        totalWon: document.getElementById('kenolandiaTotalWon'),
        biggestWin: document.getElementById('kenolandiaBiggestWin'),
        cyberHits: document.getElementById('kenolandiaCyberHits'),
        lossStreak: document.getElementById('kenolandiaLossStreak')
    };
    
    if (elements.gamesPlayed) elements.gamesPlayed.textContent = kenolandiaGame.stats.gamesPlayed;
    
    if (elements.winRate) {
        const winRate = kenolandiaGame.stats.gamesPlayed > 0 ? 
            ((kenolandiaGame.stats.gamesWon / kenolandiaGame.stats.gamesPlayed) * 100).toFixed(1) : '0.0';
        elements.winRate.textContent = `${winRate}%`;
    }
    
    if (elements.totalWagered) elements.totalWagered.textContent = kenolandiaGame.stats.totalWagered.toLocaleString();
    if (elements.totalWon) elements.totalWon.textContent = kenolandiaGame.stats.totalWon.toLocaleString();
    if (elements.biggestWin) elements.biggestWin.textContent = kenolandiaGame.stats.biggestWin.toLocaleString();
    if (elements.cyberHits) elements.cyberHits.textContent = kenolandiaGame.stats.cyberHits;
    if (elements.lossStreak) elements.lossStreak.textContent = kenolandiaGame.streakData.currentLossStreak;
    
    // Update recent results display
    const recentResultsEl = document.getElementById('kenolandiaRecentResults');
    if (recentResultsEl) {
        recentResultsEl.innerHTML = '';
        kenolandiaGame.lastResults.slice(0, 25).forEach(result => {
            const resultDiv = document.createElement('div');
            resultDiv.className = result === 'win' ? 
                'w-4 h-4 bg-green-500 rounded-full' : 
                'w-4 h-4 bg-red-500 rounded-full';
            recentResultsEl.appendChild(resultDiv);
        });
    }
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadKenolandiaGame();
});