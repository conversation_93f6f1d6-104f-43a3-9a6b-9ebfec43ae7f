// Red Dog: Wild Streak - Cyber-themed Red Dog with extremely low win rate (<5%)
let redDogGame = {
    playerCard: null,
    dealerCard: null,
    middleCard: null,
    spread: 0,
    betAmount: 0,
    raiseBet: 0,
    gamePhase: 'betting', // betting, raised, revealing, complete
    gameResult: null,
    totalWin: 0,
    wildStreak: 0,
    maxWildStreak: 0,
    cyberLevel: 1, // 1-10 (higher = more house advantage)
    streakMultiplier: 1.0,
    difficulty: 'wild', // wild, cyber, matrix, quantum
    gameMode: 'classic', // classic, streak, ultimate
    cyberBonus: false, // Rarely activated
    matrixPenalty: false, // Frequently activated
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        biggestWin: 0,
        consecutiveWins: 0,
        spreadsHit: 0,
        wildStreaks: 0,
        cyberBonuses: 0,
        matrixPenalties: 0,
        quantumNegations: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    },
    cardHistory: []
};

// Game constants with extreme house bias
const RED_DOG_MODES = {
    classic: { 
        name: 'Classic Wild', 
        houseEdge: 0.40, // 40% house edge
        cyberChance: 0.03, // 3% chance for bonus
        payoutMultiplier: 0.65, // Severely reduced payouts
        spreadPenalty: 0.20 // 20% spread penalty
    },
    streak: { 
        name: 'Wild Streak', 
        houseEdge: 0.45, // 45% house edge
        cyberChance: 0.02, // 2% chance for bonus
        payoutMultiplier: 0.60,
        spreadPenalty: 0.25 // 25% spread penalty
    },
    ultimate: { 
        name: 'Ultimate Matrix', 
        houseEdge: 0.50, // 50% house edge
        cyberChance: 0.01, // 1% chance for bonus
        payoutMultiplier: 0.55,
        spreadPenalty: 0.30 // 30% spread penalty
    }
};

const RED_DOG_DIFFICULTIES = {
    wild: { 
        name: 'Wild Cyber', 
        cardBias: 0.35,
        spreadReduction: 0.40,
        matrixChance: 0.20,
        levelMultiplier: 1.0
    },
    cyber: { 
        name: 'Cyber Matrix', 
        cardBias: 0.50,
        spreadReduction: 0.55,
        matrixChance: 0.30,
        levelMultiplier: 1.3
    },
    matrix: { 
        name: 'Matrix Quantum', 
        cardBias: 0.65,
        spreadReduction: 0.70,
        matrixChance: 0.40,
        levelMultiplier: 1.6
    },
    quantum: { 
        name: 'Quantum Void', 
        cardBias: 0.80,
        spreadReduction: 0.85,
        matrixChance: 0.50,
        levelMultiplier: 2.0
    }
};

// Severely reduced spread payouts
const RED_DOG_PAYOUTS = {
    1: 4.0,  // 1-spread (reduced from 5:1)
    2: 1.8,  // 2-spread (reduced from 2:1)
    3: 1.4,  // 3-spread (reduced from 1.5:1)
    4: 1.1,  // 4-spread (reduced from 1.2:1)
    5: 0.9,  // 5-spread (reduced from 1:1)
    6: 0.8,  // 6-spread (reduced from 1:1)
    7: 0.7,  // 7-spread (reduced from 1:1)
    8: 0.6,  // 8-spread (reduced from 1:1)
    9: 0.5,  // 9-spread (reduced from 1:1)
    10: 0.4, // 10-spread (reduced from 1:1)
    11: 0.3  // 11-spread (reduced from 1:1)
};

// Extremely biased deck creation
function createRedDogDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const deck = [];
    
    const modeData = RED_DOG_MODES[redDogGame.gameMode];
    const difficultyData = RED_DOG_DIFFICULTIES[redDogGame.difficulty];
    const levelBias = redDogGame.cyberLevel * 0.08; // Increases with cyber level
    
    suits.forEach(suit => {
        ranks.forEach(rank => {
            let cardWeight = 1;
            const value = getRedDogCardValue(rank);
            
            // Extreme bias to create losing scenarios
            // Reduce middle-value cards that could create good spreads
            if (value >= 5 && value <= 9) {
                cardWeight *= (1 - difficultyData.cardBias - levelBias);
            }
            
            // Increase extreme cards (A, 2, K, Q, J) that create bad spreads
            if (value <= 2 || value >= 11) {
                cardWeight *= (1 + difficultyData.cardBias + levelBias);
            }
            
            // Further reduce based on cyber level
            if (redDogGame.cyberLevel > 5) {
                if (value >= 4 && value <= 10) {
                    cardWeight *= 0.2; // Extreme reduction of middle cards
                }
            }
            
            // Add cards based on weight
            const cardCount = Math.max(1, Math.floor(cardWeight * 8));
            for (let i = 0; i < cardCount; i++) {
                deck.push({ suit, rank, value });
            }
        });
    });
    
    return shuffleRedDogDeck(deck);
}

function getRedDogCardValue(rank) {
    if (rank === 'A') return 1;
    if (rank === 'J') return 11;
    if (rank === 'Q') return 12;
    if (rank === 'K') return 13;
    return parseInt(rank);
}

function shuffleRedDogDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function dealBiasedRedDogCard(cardType = 'normal') {
    if (redDogGame.deck.length === 0) {
        redDogGame.deck = createRedDogDeck();
    }
    
    const difficultyData = RED_DOG_DIFFICULTIES[redDogGame.difficulty];
    const cardBias = difficultyData.cardBias + (redDogGame.cyberLevel * 0.05);
    
    // Apply extreme bias based on card type and game state
    if (cardType === 'middle' && Math.random() < cardBias * 1.5) {
        // For middle card, heavily bias toward cards that don't fall in spread
        const playerValue = redDogGame.playerCard.value;
        const dealerValue = redDogGame.dealerCard.value;
        const minValue = Math.min(playerValue, dealerValue);
        const maxValue = Math.max(playerValue, dealerValue);
        
        // Find cards outside the spread
        const outsideSpreadCards = redDogGame.deck.filter(card => 
            card.value <= minValue || card.value >= maxValue
        );
        
        if (outsideSpreadCards.length > 0) {
            const cardIndex = redDogGame.deck.indexOf(outsideSpreadCards[0]);
            return redDogGame.deck.splice(cardIndex, 1)[0];
        }
    } else if (cardType === 'extreme' && Math.random() < cardBias) {
        // Give extreme cards (A, 2, K, Q, J) to create bad spreads
        const extremeCards = redDogGame.deck.filter(card => 
            card.value <= 2 || card.value >= 11
        );
        
        if (extremeCards.length > 0) {
            const cardIndex = redDogGame.deck.indexOf(extremeCards[0]);
            return redDogGame.deck.splice(cardIndex, 1)[0];
        }
    }
    
    return redDogGame.deck.pop();
}

function calculateSpread(card1, card2) {
    const value1 = card1.value;
    const value2 = card2.value;
    const minValue = Math.min(value1, value2);
    const maxValue = Math.max(value1, value2);
    
    return maxValue - minValue - 1;
}

function startRedDogGame() {
    const betAmount = parseInt(document.getElementById('redDogBet').value);
    
    if (betAmount > balance || betAmount <= 0) {
        updateRedDogStatus('Invalid bet amount!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    redDogGame.playerCard = null;
    redDogGame.dealerCard = null;
    redDogGame.middleCard = null;
    redDogGame.betAmount = betAmount;
    redDogGame.raiseBet = 0;
    redDogGame.gamePhase = 'betting';
    redDogGame.gameResult = null;
    redDogGame.totalWin = 0;
    redDogGame.cyberBonus = false;
    redDogGame.matrixPenalty = false;
    
    redDogGame.stats.totalWagered += betAmount;
    
    // Create extremely biased deck
    redDogGame.deck = createRedDogDeck();
    
    // Deal first two cards with extreme bias toward bad spreads
    redDogGame.playerCard = dealBiasedRedDogCard('extreme');
    redDogGame.dealerCard = dealBiasedRedDogCard('extreme');
    
    // Calculate spread
    redDogGame.spread = calculateSpread(redDogGame.playerCard, redDogGame.dealerCard);
    
    // Check for consecutive cards (automatic loss)
    if (redDogGame.spread === 0) {
        redDogGame.gameResult = 'consecutive';
        redDogGame.gamePhase = 'complete';
        updateRedDogStatus('Consecutive cards - Automatic loss!');
        updateRedDogGameStats();
        updateRedDogDisplay();
        return;
    }
    
    // Check for pair (push - but house takes commission)
    if (redDogGame.playerCard.value === redDogGame.dealerCard.value) {
        const modeData = RED_DOG_MODES[redDogGame.gameMode];
        
        // House takes commission even on pushes
        const commission = Math.floor(betAmount * 0.1);
        redDogGame.totalWin = betAmount - commission;
        balance += redDogGame.totalWin;
        updateBalance();
        
        redDogGame.gameResult = 'pair';
        redDogGame.gamePhase = 'complete';
        updateRedDogStatus(`Pair - Push with ${commission} GA commission!`);
        updateRedDogGameStats();
        updateRedDogDisplay();
        return;
    }
    
    // Generate cyber effects
    generateCyberEffects();
    
    // Apply spread reduction based on difficulty
    const difficultyData = RED_DOG_DIFFICULTIES[redDogGame.difficulty];
    if (Math.random() < difficultyData.spreadReduction) {
        redDogGame.spread = Math.max(1, redDogGame.spread - 1);
        updateRedDogStatus('Matrix interference - Spread reduced!');
    }
    
    updateRedDogDisplay();
    
    // Enable raise button for spreads 1-4 only
    document.getElementById('raiseRedDog').disabled = redDogGame.spread > 4;
    document.getElementById('callRedDog').disabled = false;
}

function generateCyberEffects() {
    const modeData = RED_DOG_MODES[redDogGame.gameMode];
    const difficultyData = RED_DOG_DIFFICULTIES[redDogGame.difficulty];
    
    // Cyber bonus (extremely rare)
    if (Math.random() < modeData.cyberChance) {
        redDogGame.cyberBonus = true;
    }
    
    // Matrix penalty (very common)
    if (Math.random() < difficultyData.matrixChance + (redDogGame.cyberLevel * 0.03)) {
        redDogGame.matrixPenalty = true;
    }
    
    // Increase cyber level occasionally (makes game harder)
    if (redDogGame.stats.handsPlayed > 0 && 
        redDogGame.stats.handsPlayed % 8 === 0 && 
        redDogGame.cyberLevel < 10) {
        redDogGame.cyberLevel++;
        updateRedDogStatus(`Cyber Level increased to ${redDogGame.cyberLevel}!`);
    }
    
    // Reset wild streak on loss (very common)
    if (redDogGame.wildStreak > 0 && Math.random() < 0.7) {
        redDogGame.wildStreak = 0;
        updateRedDogStatus('Wild streak broken by cyber interference!');
    }
}

function raiseRedDogBet() {
    if (redDogGame.gamePhase !== 'betting' || redDogGame.spread > 4) return;
    
    const raiseAmount = redDogGame.betAmount;
    
    if (raiseAmount > balance) {
        updateRedDogStatus('Insufficient balance to raise!');
        return;
    }
    
    // Deduct raise bet
    balance -= raiseAmount;
    updateBalance();
    
    redDogGame.raiseBet = raiseAmount;
    redDogGame.gamePhase = 'raised';
    redDogGame.stats.totalWagered += raiseAmount;
    
    updateRedDogStatus(`Raised by ${raiseAmount} GA! Total bet: ${redDogGame.betAmount + redDogGame.raiseBet} GA`);
    
    // Disable raise, enable call
    document.getElementById('raiseRedDog').disabled = true;
    document.getElementById('callRedDog').disabled = false;
    
    updateRedDogDisplay();
}

function callRedDogBet() {
    if (redDogGame.gamePhase !== 'betting' && redDogGame.gamePhase !== 'raised') return;
    
    redDogGame.gamePhase = 'revealing';
    
    // Deal middle card with extreme bias against player
    redDogGame.middleCard = dealBiasedRedDogCard('middle');
    
    // Add to card history for tracking
    redDogGame.cardHistory.push({
        player: redDogGame.playerCard.value,
        dealer: redDogGame.dealerCard.value,
        middle: redDogGame.middleCard.value,
        spread: redDogGame.spread
    });
    
    // Determine result
    const middleValue = redDogGame.middleCard.value;
    const playerValue = redDogGame.playerCard.value;
    const dealerValue = redDogGame.dealerCard.value;
    const minValue = Math.min(playerValue, dealerValue);
    const maxValue = Math.max(playerValue, dealerValue);
    
    if (middleValue > minValue && middleValue < maxValue) {
        // Player wins (extremely rare due to biased middle card)
        const modeData = RED_DOG_MODES[redDogGame.gameMode];
        
        // Apply quantum negation (house can negate wins)
        if (Math.random() < modeData.houseEdge * 1.2) {
            redDogGame.gameResult = 'quantum_negated';
            redDogGame.stats.quantumNegations++;
            updateRedDogStatus('Win negated by quantum interference!');
        } else {
            redDogGame.gameResult = 'win';
            calculateRedDogWinnings();
            updateRedDogStatus(`Wild streak hit! Spread ${redDogGame.spread} wins!`);
        }
    } else {
        // Player loses (very common)
        redDogGame.gameResult = 'loss';
        updateRedDogStatus(`Middle card ${middleValue} misses spread - You lose!`);
    }
    
    redDogGame.gamePhase = 'complete';
    updateRedDogGameStats();
    updateRedDogDisplay();
    
    // Disable buttons
    document.getElementById('raiseRedDog').disabled = true;
    document.getElementById('callRedDog').disabled = true;
    document.getElementById('dealRedDog').disabled = false;
}

function calculateRedDogWinnings() {
    const modeData = RED_DOG_MODES[redDogGame.gameMode];
    const difficultyData = RED_DOG_DIFFICULTIES[redDogGame.difficulty];
    
    // Get base payout (already severely reduced)
    let payout = RED_DOG_PAYOUTS[redDogGame.spread] || 0.3;
    
    // Apply mode multiplier (further reduces payouts)
    payout *= modeData.payoutMultiplier;
    
    // Apply spread penalty
    payout *= (1 - modeData.spreadPenalty);
    
    // Apply cyber bonus (minimal effect)
    if (redDogGame.cyberBonus) {
        payout += 0.1; // Tiny bonus
        redDogGame.stats.cyberBonuses++;
    }
    
    // Apply matrix penalty (severe)
    if (redDogGame.matrixPenalty) {
        payout *= 0.4; // 60% penalty
        redDogGame.stats.matrixPenalties++;
    }
    
    // Apply cyber level penalty
    payout *= (1 - (redDogGame.cyberLevel * 0.04));
    
    // Apply wild streak bonus (very small)
    const streakBonus = Math.min(0.2, redDogGame.wildStreak * 0.02);
    payout += streakBonus;
    
    // Calculate total bet
    const totalBet = redDogGame.betAmount + redDogGame.raiseBet;
    
    // Calculate winnings
    let winnings = totalBet * payout;
    
    // Apply additional house edge
    winnings *= (1 - modeData.houseEdge * 0.3);
    
    // Minimum win of 1 GA
    redDogGame.totalWin = Math.floor(Math.max(1, winnings));
    
    // Add original bet back plus winnings
    balance += totalBet + redDogGame.totalWin;
    updateBalance();
    
    // Increase wild streak (rarely)
    if (Math.random() < 0.3) {
        redDogGame.wildStreak++;
        redDogGame.maxWildStreak = Math.max(redDogGame.maxWildStreak, redDogGame.wildStreak);
    }
}

function updateRedDogGameStats() {
    redDogGame.stats.handsPlayed++;
    
    if (redDogGame.gameResult === 'win') {
        redDogGame.stats.handsWon++;
        redDogGame.stats.totalWon += redDogGame.totalWin;
        redDogGame.stats.spreadsHit++;
        
        if (redDogGame.totalWin > redDogGame.stats.biggestWin) {
            redDogGame.stats.biggestWin = redDogGame.totalWin;
        }
        
        // Update win streak
        redDogGame.streakData.currentWinStreak++;
        redDogGame.streakData.currentLossStreak = 0;
        
        if (redDogGame.streakData.currentWinStreak > redDogGame.streakData.longestWinStreak) {
            redDogGame.streakData.longestWinStreak = redDogGame.streakData.currentWinStreak;
        }
    } else {
        // Update loss streak
        redDogGame.streakData.currentLossStreak++;
        redDogGame.streakData.currentWinStreak = 0;
        
        if (redDogGame.streakData.currentLossStreak > redDogGame.streakData.longestLossStreak) {
            redDogGame.streakData.longestLossStreak = redDogGame.streakData.currentLossStreak;
        }
    }
}

function updateRedDogDisplay() {
    // Update player card
    if (redDogGame.playerCard) {
        const playerCardEl = document.getElementById('redDogPlayerCard');
        playerCardEl.innerHTML = `
            <div class="cyber-card neon-border">
                <div class="card-content">
                    <div class="card-rank">${redDogGame.playerCard.rank}</div>
                    <div class="card-suit">${redDogGame.playerCard.suit}</div>
                </div>
            </div>
        `;
    }
    
    // Update dealer card
    if (redDogGame.dealerCard) {
        const dealerCardEl = document.getElementById('redDogDealerCard');
        dealerCardEl.innerHTML = `
            <div class="cyber-card neon-border">
                <div class="card-content">
                    <div class="card-rank">${redDogGame.dealerCard.rank}</div>
                    <div class="card-suit">${redDogGame.dealerCard.suit}</div>
                </div>
            </div>
        `;
    }
    
    // Update middle card
    const middleCardEl = document.getElementById('redDogMiddleCard');
    if (redDogGame.middleCard) {
        middleCardEl.innerHTML = `
            <div class="cyber-card neon-border ${redDogGame.gameResult === 'win' ? 'winning-card' : 'losing-card'}">
                <div class="card-content">
                    <div class="card-rank">${redDogGame.middleCard.rank}</div>
                    <div class="card-suit">${redDogGame.middleCard.suit}</div>
                </div>
            </div>
        `;
    } else {
        middleCardEl.innerHTML = `
            <div class="cyber-card face-down">
                <div class="card-back">?</div>
            </div>
        `;
    }
    
    // Update spread display
    document.getElementById('redDogSpread').textContent = redDogGame.spread;
    
    // Update payout display
    const payout = RED_DOG_PAYOUTS[redDogGame.spread] || 0;
    document.getElementById('redDogPayout').textContent = `${payout.toFixed(1)}:1`;
    
    // Update cyber effects
    document.getElementById('cyberLevel').textContent = redDogGame.cyberLevel;
    document.getElementById('wildStreak').textContent = redDogGame.wildStreak;
    document.getElementById('cyberBonus').style.display = redDogGame.cyberBonus ? 'block' : 'none';
    document.getElementById('matrixPenalty').style.display = redDogGame.matrixPenalty ? 'block' : 'none';
    
    // Update game result
    if (redDogGame.gameResult) {
        const resultEl = document.getElementById('redDogGameResult');
        if (redDogGame.gameResult === 'win') {
            resultEl.innerHTML = `WILD STREAK! +${redDogGame.totalWin} GA!`;
            resultEl.className = 'text-center text-xl font-bold text-green-400 h-8 font-mono neon-glow';
        } else if (redDogGame.gameResult === 'pair') {
            resultEl.innerHTML = `PAIR PUSH - Commission Deducted`;
            resultEl.className = 'text-center text-xl font-bold text-yellow-400 h-8 font-mono';
        } else if (redDogGame.gameResult === 'quantum_negated') {
            resultEl.innerHTML = `QUANTUM NEGATION - Win Voided!`;
            resultEl.className = 'text-center text-xl font-bold text-purple-400 h-8 font-mono';
        } else {
            const totalLoss = redDogGame.betAmount + redDogGame.raiseBet;
            resultEl.innerHTML = `CYBER DEFEAT - Lost ${totalLoss} GA`;
            resultEl.className = 'text-center text-xl font-bold text-red-400 h-8 font-mono';
        }
    }
    
    // Update statistics
    updateRedDogStats();
}

function updateRedDogStats() {
    document.getElementById('redDogHandsPlayed').textContent = redDogGame.stats.handsPlayed;
    
    const winRate = redDogGame.stats.handsPlayed > 0 ? 
        ((redDogGame.stats.handsWon / redDogGame.stats.handsPlayed) * 100).toFixed(1) : '0.0';
    document.getElementById('redDogWinRate').textContent = `${winRate}%`;
    
    document.getElementById('redDogTotalWagered').textContent = redDogGame.stats.totalWagered.toLocaleString();
    document.getElementById('redDogTotalWon').textContent = redDogGame.stats.totalWon.toLocaleString();
    document.getElementById('redDogBiggestWin').textContent = redDogGame.stats.biggestWin.toLocaleString();
    document.getElementById('redDogSpreadsHit').textContent = redDogGame.stats.spreadsHit;
    document.getElementById('redDogWildStreaks').textContent = redDogGame.stats.wildStreaks;
    document.getElementById('redDogLossStreak').textContent = redDogGame.streakData.currentLossStreak;
    document.getElementById('redDogCyberBonuses').textContent = redDogGame.stats.cyberBonuses;
    document.getElementById('redDogMatrixPenalties').textContent = redDogGame.stats.matrixPenalties;
    document.getElementById('redDogQuantumNegations').textContent = redDogGame.stats.quantumNegations;
}

function updateRedDogStatus(message) {
    document.getElementById('redDogGameStatus').textContent = message;
}

function changeRedDogMode() {
    const mode = document.getElementById('redDogMode').value;
    redDogGame.gameMode = mode;
    
    const modeData = RED_DOG_MODES[mode];
    document.getElementById('redDogModeInfo').innerHTML = `
        House Edge: ${(modeData.houseEdge * 100).toFixed(0)}% | 
        Cyber Chance: ${(modeData.cyberChance * 100).toFixed(1)}% | 
        Payout: ${(modeData.payoutMultiplier * 100).toFixed(0)}% | 
        Spread Penalty: ${(modeData.spreadPenalty * 100).toFixed(0)}%
    `;
}

function changeRedDogDifficulty() {
    const difficulty = document.getElementById('redDogDifficulty').value;
    redDogGame.difficulty = difficulty;
    
    const diffData = RED_DOG_DIFFICULTIES[difficulty];
    document.getElementById('redDogDifficultyInfo').innerHTML = `
        Card Bias: ${(diffData.cardBias * 100).toFixed(0)}% | 
        Spread Reduction: ${(diffData.spreadReduction * 100).toFixed(0)}% | 
        Matrix Chance: ${(diffData.matrixChance * 100).toFixed(0)}% | 
        Level Multiplier: ${diffData.levelMultiplier}x
    `;
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    changeRedDogMode();
    changeRedDogDifficulty();
    
    // Add event listeners
    document.getElementById('dealRedDog').addEventListener('click', startRedDogGame);
    document.getElementById('raiseRedDog').addEventListener('click', raiseRedDogBet);
    document.getElementById('callRedDog').addEventListener('click', callRedDogBet);
    document.getElementById('redDogMode').addEventListener('change', changeRedDogMode);
    document.getElementById('redDogDifficulty').addEventListener('change', changeRedDogDifficulty);
});