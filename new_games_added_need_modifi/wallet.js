// wallet.js

// Initialize the wallet if it doesn't exist in localStorage
function initializeWallet(initialBalance = 1000) {
    if (localStorage.getItem('walletBalance') === null) {
        localStorage.setItem('walletBalance', initialBalance);
    }
}

// Get the current balance from localStorage
function getWalletBalance() {
    return parseInt(localStorage.getItem('walletBalance'), 10);
}

// Update the balance in localStorage
function updateWalletBalance(newBalance) {
    localStorage.setItem('walletBalance', newBalance);
    // Dispatch a custom event to notify other parts of the application that the balance has changed.
    window.dispatchEvent(new Event('walletBalanceChanged'));
}

// Add funds to the wallet
function addToWallet(amount) {
    const currentBalance = getWalletBalance();
    updateWalletBalance(currentBalance + amount);
}

// Subtract funds from the wallet
function subtractFromWallet(amount) {
    const currentBalance = getWalletBalance();
    if (currentBalance >= amount) {
        updateWalletBalance(currentBalance - amount);
        return true; // successful
    }
    return false; // insufficient funds
}

// Listen for storage changes from other tabs/windows
window.addEventListener('storage', (event) => {
    if (event.key === 'walletBalance') {
        window.dispatchEvent(new Event('walletBalanceChanged'));
    }
});

// Initialize the wallet on script load
initializeWallet();
