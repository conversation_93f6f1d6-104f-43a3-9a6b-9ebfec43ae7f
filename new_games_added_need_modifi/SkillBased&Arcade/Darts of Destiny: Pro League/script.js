// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Pro League Darts Implementation
const PRO_TARGETS = {
    bullseye: { points: 100, multiplier: 20, radius: 12, color: '#ff0000' },
    double20: { points: 40, multiplier: 8, radius: 25, color: '#ff4400' },
    triple20: { points: 60, multiplier: 12, radius: 35, color: '#ff8800' },
    inner: { points: 30, multiplier: 6, radius: 50, color: '#00ff00' },
    middle: { points: 20, multiplier: 4, radius: 70, color: '#ffff00' },
    outer: { points: 10, multiplier: 2, radius: 90, color: '#0080ff' },
    edge: { points: 5, multiplier: 1, radius: 110, color: '#ff8000' },
    miss: { points: 0, multiplier: 0, radius: 130, color: '#666666' }
};

const PRO_LEAGUES = [
    { name: 'amateur', description: 'Local Club', entry: 50, darts: 3, timeLimit: 45, multiplier: 1.0, opponents: 2 },
    { name: 'regional', description: 'Regional Championship', entry: 150, darts: 5, timeLimit: 60, multiplier: 1.5, opponents: 4 },
    { name: 'national', description: 'National Tournament', entry: 500, darts: 7, timeLimit: 75, multiplier: 2.0, opponents: 6 },
    { name: 'international', description: 'World Series', entry: 1500, darts: 9, timeLimit: 90, multiplier: 3.0, opponents: 8 },
    { name: 'legendary', description: 'Pro League Elite', entry: 5000, darts: 12, timeLimit: 120, multiplier: 5.0, opponents: 12 }
];

const PRO_DIFFICULTIES = [
    { name: 'rookie', description: 'Learning the ropes', aimAssist: 0.7, windEffect: 0.3, pressure: 0.2, multiplier: 0.8 },
    { name: 'semi-pro', description: 'Weekend warrior', aimAssist: 0.5, windEffect: 0.5, pressure: 0.4, multiplier: 1.0 },
    { name: 'professional', description: 'Tournament ready', aimAssist: 0.3, windEffect: 0.7, pressure: 0.6, multiplier: 1.5 },
    { name: 'world-class', description: 'Elite competitor', aimAssist: 0.1, windEffect: 0.9, pressure: 0.8, multiplier: 2.5 },
    { name: 'legendary', description: 'Hall of Fame', aimAssist: 0.0, windEffect: 1.0, pressure: 1.0, multiplier: 4.0 }
];

let proLeagueGame = {
    isPlaying: false,
    league: 'amateur',
    difficulty: 'semi-pro',
    entryFee: 0,
    currentDart: 0,
    totalDarts: 3,
    playerScore: 0,
    opponentScores: [],
    throws: [],
    timeLeft: 45,
    timer: null,
    canvas: null,
    ctx: null,
    isAiming: false,
    aimX: 0,
    aimY: 0,
    powerLevel: 0,
    isCharging: false,
    windDirection: 0,
    windStrength: 0,
    pressure: 0,
    currentRound: 1,
    totalRounds: 3,
    leagueRank: 1,
    proPoints: 0,
    championships: 0,
    perfectGames: 0,
    rivalryLevel: 0,
    sponsorshipDeals: 0,
    animationId: null,
    opponents: []
};

function loadProLeagueGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Tournament Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🏆 PRO LEAGUE DARTS 🏆</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 LEAGUE LEVEL</label>
                        <select id="proLeague" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${PRO_LEAGUES.map(league => 
                                `<option value="${league.name}">${league.name.toUpperCase()} - ${league.description} (${league.entry} GA)</option>`
                            ).join('')}
                        </select>
                        <div id="leagueInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ SKILL LEVEL</label>
                        <select id="proDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${PRO_DIFFICULTIES.map(diff => 
                                `<option value="${diff.name}">${diff.name.toUpperCase()} - ${diff.description}</option>`
                            ).join('')}
                        </select>
                        <div id="difficultyInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <button id="enterTournament" class="w-full cyber-button px-4 py-3 rounded-lg font-semibold text-white mb-4">
                        ENTER TOURNAMENT
                    </button>
                    
                    <div class="text-center mb-4">
                        <div id="tournamentTimer" class="text-2xl font-bold text-cyan-400 mb-2">0:45</div>
                        <div id="tournamentStatus" class="text-sm text-gray-300">Ready for professional competition</div>
                    </div>
                    
                    <!-- Tournament Progress -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 mb-4">
                        <div class="text-sm text-gray-300 mb-2">🏁 TOURNAMENT PROGRESS</div>
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-cyan-400">Round:</span>
                            <span id="currentRound" class="text-yellow-400">1/3</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-cyan-400">Position:</span>
                            <span id="currentPosition" class="text-green-400">#1</span>
                        </div>
                    </div>
                    
                    <!-- Pressure Meter -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 mb-4">
                        <div class="text-sm text-gray-300 mb-2">😰 PRESSURE LEVEL</div>
                        <div class="bg-gray-700 rounded-full h-3 mb-2">
                            <div id="pressureMeter" class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-3 rounded-full transition-all" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <span id="pressureLevel" class="text-white font-bold">0%</span>
                        </div>
                    </div>
                    
                    <!-- Wind Conditions -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 mb-4">
                        <div class="text-sm text-gray-300 mb-2">🌪️ ARENA CONDITIONS</div>
                        <div class="flex items-center justify-between">
                            <span id="windDirection" class="text-cyan-400">→</span>
                            <div class="flex-1 mx-2">
                                <div class="bg-gray-700 rounded-full h-2">
                                    <div id="windStrengthBar" class="bg-red-500 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                            <span id="windStrength" class="text-yellow-400">0%</span>
                        </div>
                    </div>
                    
                    <!-- Power Meter -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20">
                        <div class="text-sm text-gray-300 mb-2">💪 THROW POWER</div>
                        <div class="bg-gray-700 rounded-full h-4 mb-2">
                            <div id="powerMeter" class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-4 rounded-full transition-all" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <span id="powerLevel" class="text-white font-bold">0%</span>
                        </div>
                    </div>
                </div>
                
                <!-- Leaderboard -->
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 LEADERBOARD</h5>
                    <div id="leaderboard" class="space-y-2 max-h-48 overflow-y-auto">
                        <!-- Leaderboard will appear here -->
                    </div>
                </div>
            </div>
            
            <!-- Professional Dartboard -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="flex justify-between items-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400">🎯 PROFESSIONAL DARTBOARD</h5>
                        <div class="text-right">
                            <div class="text-sm text-gray-300">Dart: <span id="currentDart" class="text-cyan-400">0</span>/<span id="totalDarts">3</span></div>
                            <div class="text-sm text-gray-300">Score: <span id="playerScore" class="text-yellow-400">0</span></div>
                        </div>
                    </div>
                    
                    <div class="relative bg-black/50 rounded-lg p-4 flex items-center justify-center" style="min-height: 500px;">
                        <canvas id="proCanvas" width="450" height="450" class="border border-purple-500/30 rounded-lg cursor-crosshair"></canvas>
                        
                        <!-- Aim Crosshair -->
                        <div id="aimCrosshair" class="absolute pointer-events-none hidden">
                            <div class="w-8 h-8 border-2 border-red-500 rounded-full animate-pulse">
                                <div class="w-2 h-2 bg-red-500 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <div id="throwResult" class="text-lg font-semibold mb-2"></div>
                        <div id="gameInstructions" class="text-sm text-gray-400">
                            Click to aim, hold SPACE to charge power, release to throw!
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Pro Stats Panel -->
        <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mt-8">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-purple-400" id="leagueRank">1</div>
                <div class="text-sm text-gray-300">League Rank</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-cyan-400" id="proPoints">0</div>
                <div class="text-sm text-gray-300">Pro Points</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-gold-400" id="championships">0</div>
                <div class="text-sm text-gray-300">Championships</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-green-400" id="perfectGames">0</div>
                <div class="text-sm text-gray-300">Perfect Games</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-red-400" id="rivalryLevel">0</div>
                <div class="text-sm text-gray-300">Rivalry Level</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-yellow-400" id="sponsorshipDeals">0</div>
                <div class="text-sm text-gray-300">Sponsorships</div>
            </div>
        </div>
    `;
    
    setupProLeagueGame();
}

function setupProLeagueGame() {
    proLeagueGame.canvas = document.getElementById('proCanvas');
    proLeagueGame.ctx = proLeagueGame.canvas.getContext('2d');
    
    // Event listeners
    document.getElementById('enterTournament').addEventListener('click', startTournament);
    document.getElementById('proLeague').addEventListener('change', updateLeague);
    document.getElementById('proDifficulty').addEventListener('change', updateDifficulty);
    
    // Canvas events
    proLeagueGame.canvas.addEventListener('mousemove', handleMouseMove);
    proLeagueGame.canvas.addEventListener('click', handleCanvasClick);
    
    // Keyboard events
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    updateLeague();
    updateDifficulty();
    generateArenaConditions();
    drawProDartboard();
    updateProDisplay();
    generateOpponents();
}

function updateLeague() {
    const league = document.getElementById('proLeague').value;
    proLeagueGame.league = league;
    
    const leagueData = PRO_LEAGUES.find(l => l.name === league);
    proLeagueGame.totalDarts = leagueData.darts;
    proLeagueGame.timeLeft = leagueData.timeLimit;
    proLeagueGame.entryFee = leagueData.entry;
    
    document.getElementById('leagueInfo').textContent = 
        `${leagueData.darts} darts, ${leagueData.timeLimit}s, ${leagueData.multiplier}x multiplier, ${leagueData.opponents} opponents`;
    document.getElementById('totalDarts').textContent = leagueData.darts;
    document.getElementById('tournamentTimer').textContent = `0:${leagueData.timeLimit.toString().padStart(2, '0')}`;
}

function updateDifficulty() {
    const difficulty = document.getElementById('proDifficulty').value;
    proLeagueGame.difficulty = difficulty;
    
    const diffData = PRO_DIFFICULTIES.find(d => d.name === difficulty);
    document.getElementById('difficultyInfo').textContent = 
        `Aim assist: ${Math.floor(diffData.aimAssist * 100)}%, Conditions: ${Math.floor(diffData.windEffect * 100)}%, Pressure: ${Math.floor(diffData.pressure * 100)}%`;
}

function generateOpponents() {
    const leagueData = PRO_LEAGUES.find(l => l.name === proLeagueGame.league);
    proLeagueGame.opponents = [];
    
    const names = ['Thunder', 'Precision', 'Viper', 'Ace', 'Bullseye', 'Storm', 'Hawk', 'Blade', 'Arrow', 'Target', 'Dart', 'Strike'];
    
    for (let i = 0; i < leagueData.opponents; i++) {
        const skill = 0.3 + (Math.random() * 0.7); // 30-100% skill
        proLeagueGame.opponents.push({
            name: names[i % names.length] + (i > 11 ? ` ${Math.floor(i/12) + 1}` : ''),
            skill: skill,
            score: 0,
            finished: false
        });
    }
    
    updateLeaderboard();
}

function generateArenaConditions() {
    // Generate arena conditions
    proLeagueGame.windDirection = Math.random() * 360;
    proLeagueGame.windStrength = Math.random() * 0.9 + 0.1;
    
    // Calculate pressure based on league and performance
    const leagueData = PRO_LEAGUES.find(l => l.name === proLeagueGame.league);
    const diffData = PRO_DIFFICULTIES.find(d => d.name === proLeagueGame.difficulty);
    
    proLeagueGame.pressure = Math.min(100, 
        (leagueData.opponents * 10) + 
        (diffData.pressure * 50) + 
        (proLeagueGame.currentRound * 15)
    );
    
    // Update displays
    const windArrows = ['↑', '↗', '→', '↘', '↓', '↙', '←', '↖'];
    const arrowIndex = Math.floor(proLeagueGame.windDirection / 45);
    document.getElementById('windDirection').textContent = windArrows[arrowIndex];
    document.getElementById('windStrength').textContent = Math.floor(proLeagueGame.windStrength * 100) + '%';
    document.getElementById('windStrengthBar').style.width = (proLeagueGame.windStrength * 100) + '%';
    document.getElementById('pressureLevel').textContent = Math.floor(proLeagueGame.pressure) + '%';
    document.getElementById('pressureMeter').style.width = proLeagueGame.pressure + '%';
}

function startTournament() {
    if (proLeagueGame.entryFee > balance) {
        document.getElementById('tournamentStatus').textContent = 'Insufficient funds for tournament entry!';
        return;
    }
    
    if (proLeagueGame.isPlaying) return;
    
    // Deduct entry fee
    balance -= proLeagueGame.entryFee;
    updateBalance();
    
    // Initialize tournament
    proLeagueGame.isPlaying = true;
    proLeagueGame.currentDart = 0;
    proLeagueGame.playerScore = 0;
    proLeagueGame.throws = [];
    proLeagueGame.currentRound = 1;
    
    const leagueData = PRO_LEAGUES.find(l => l.name === proLeagueGame.league);
    proLeagueGame.timeLeft = leagueData.timeLimit;
    proLeagueGame.totalDarts = leagueData.darts;
    
    // Generate opponents and conditions
    generateOpponents();
    generateArenaConditions();
    
    // Start timer
    startTournamentTimer();
    
    // Update UI
    document.getElementById('enterTournament').disabled = true;
    document.getElementById('tournamentStatus').textContent = 'Tournament in progress - show your skills!';
    document.getElementById('gameInstructions').innerHTML = 
        '<span class="text-yellow-400">Professional competition! Click to aim → Hold SPACE to charge → Release to throw!</span>';
    
    updateProDisplay();
    drawProDartboard();
}

function handleMouseMove(e) {
    if (!proLeagueGame.isPlaying) return;
    
    const rect = proLeagueGame.canvas.getBoundingClientRect();
    proLeagueGame.aimX = e.clientX - rect.left;
    proLeagueGame.aimY = e.clientY - rect.top;
    
    // Show crosshair
    const crosshair = document.getElementById('aimCrosshair');
    crosshair.style.left = (rect.left + proLeagueGame.aimX - 16) + 'px';
    crosshair.style.top = (rect.top + proLeagueGame.aimY - 16) + 'px';
    crosshair.classList.remove('hidden');
}

function handleCanvasClick(e) {
    if (!proLeagueGame.isPlaying || proLeagueGame.isCharging) return;
    
    const rect = proLeagueGame.canvas.getBoundingClientRect();
    proLeagueGame.aimX = e.clientX - rect.left;
    proLeagueGame.aimY = e.clientY - rect.top;
    
    proLeagueGame.isAiming = true;
    document.getElementById('gameInstructions').innerHTML = 
        '<span class="text-cyan-400">Hold SPACE to charge power, release to throw!</span>';
}

function handleKeyDown(e) {
    if (!proLeagueGame.isPlaying || !proLeagueGame.isAiming) return;
    
    if (e.code === 'Space' && !proLeagueGame.isCharging) {
        e.preventDefault();
        proLeagueGame.isCharging = true;
        chargePower();
    }
}

function handleKeyUp(e) {
    if (!proLeagueGame.isPlaying || !proLeagueGame.isCharging) return;
    
    if (e.code === 'Space') {
        e.preventDefault();
        proLeagueGame.isCharging = false;
        throwProDart();
    }
}

function chargePower() {
    if (!proLeagueGame.isCharging) return;
    
    // Pressure affects power stability
    const pressureEffect = proLeagueGame.pressure / 100;
    const powerIncrement = 2 + (Math.random() * pressureEffect * 2);
    
    proLeagueGame.powerLevel = Math.min(100, proLeagueGame.powerLevel + powerIncrement);
    document.getElementById('powerLevel').textContent = Math.floor(proLeagueGame.powerLevel) + '%';
    document.getElementById('powerMeter').style.width = proLeagueGame.powerLevel + '%';
    
    // Power oscillation increases with pressure
    if (proLeagueGame.powerLevel >= 100) {
        proLeagueGame.powerLevel = 0;
    }
    
    requestAnimationFrame(chargePower);
}

function throwProDart() {
    if (proLeagueGame.currentDart >= proLeagueGame.totalDarts) return;
    
    proLeagueGame.currentDart++;
    proLeagueGame.isAiming = false;
    
    // Calculate throw with professional modifiers
    const diffData = PRO_DIFFICULTIES.find(d => d.name === proLeagueGame.difficulty);
    
    // Wind effect
    const windEffect = proLeagueGame.windStrength * diffData.windEffect;
    const windX = Math.cos(proLeagueGame.windDirection * Math.PI / 180) * windEffect * 25;
    const windY = Math.sin(proLeagueGame.windDirection * Math.PI / 180) * windEffect * 25;
    
    // Pressure effect (affects accuracy)
    const pressureEffect = (proLeagueGame.pressure / 100) * diffData.pressure;
    const pressureX = (Math.random() - 0.5) * pressureEffect * 30;
    const pressureY = (Math.random() - 0.5) * pressureEffect * 30;
    
    // Power accuracy (optimal 75-85%)
    const powerAccuracy = 1 - Math.abs(proLeagueGame.powerLevel - 80) / 80;
    
    // Aim assist
    const centerX = proLeagueGame.canvas.width / 2;
    const centerY = proLeagueGame.canvas.height / 2;
    const aimAssist = diffData.aimAssist;
    
    const finalX = proLeagueGame.aimX + windX + pressureX + 
                   (centerX - proLeagueGame.aimX) * aimAssist * (1 - powerAccuracy);
    const finalY = proLeagueGame.aimY + windY + pressureY + 
                   (centerY - proLeagueGame.aimY) * aimAssist * (1 - powerAccuracy);
    
    // Calculate distance and determine hit
    const distance = Math.sqrt(Math.pow(finalX - centerX, 2) + Math.pow(finalY - centerY, 2));
    
    let hitResult = null;
    for (const [key, target] of Object.entries(PRO_TARGETS)) {
        if (distance <= target.radius) {
            hitResult = { type: key, ...target };
            break;
        }
    }
    
    if (!hitResult) {
        hitResult = PRO_TARGETS.miss;
        hitResult.type = 'miss';
    }
    
    // Record throw
    const throwData = {
        x: finalX,
        y: finalY,
        distance: distance,
        result: hitResult,
        power: proLeagueGame.powerLevel,
        pressure: proLeagueGame.pressure
    };
    
    proLeagueGame.throws.push(throwData);
    proLeagueGame.playerScore += hitResult.points;
    
    // Reset power
    proLeagueGame.powerLevel = 0;
    document.getElementById('powerLevel').textContent = '0%';
    document.getElementById('powerMeter').style.width = '0%';
    
    // Update display
    updateProDisplay();
    drawProDartboard();
    
    // Show result
    let resultText = `Dart ${proLeagueGame.currentDart}: ${hitResult.type.toUpperCase()}`;
    if (hitResult.points > 0) {
        resultText += ` (+${hitResult.points} points)`;
    }
    
    document.getElementById('throwResult').innerHTML = 
        `<span class="${hitResult.type === 'bullseye' ? 'text-red-400' : hitResult.type === 'miss' ? 'text-gray-400' : 'text-green-400'}">${resultText}</span>`;
    
    // Simulate opponent throws
    simulateOpponentThrows();
    
    // Check if round complete
    if (proLeagueGame.currentDart >= proLeagueGame.totalDarts) {
        setTimeout(() => endTournamentRound(), 2000);
    } else {
        setTimeout(() => {
            generateArenaConditions();
            document.getElementById('gameInstructions').innerHTML = 
                '<span class="text-yellow-400">Next dart! Click to aim → Hold SPACE to charge → Release to throw!</span>';
        }, 1500);
    }
}

function simulateOpponentThrows() {
    proLeagueGame.opponents.forEach(opponent => {
        if (!opponent.finished) {
            // Simulate opponent performance based on skill
            const baseScore = opponent.skill * 80; // 0-80 base points
            const variance = (Math.random() - 0.5) * 40; // ±20 variance
            const dartScore = Math.max(0, Math.floor(baseScore + variance));
            opponent.score += dartScore;
        }
    });
    
    updateLeaderboard();
}

function updateLeaderboard() {
    const leaderboard = document.getElementById('leaderboard');
    
    // Create combined leaderboard
    const allPlayers = [
        { name: 'YOU', score: proLeagueGame.playerScore, isPlayer: true },
        ...proLeagueGame.opponents.map(opp => ({ name: opp.name, score: opp.score, isPlayer: false }))
    ];
    
    // Sort by score
    allPlayers.sort((a, b) => b.score - a.score);
    
    leaderboard.innerHTML = allPlayers.map((player, index) => `
        <div class="flex justify-between items-center p-2 rounded ${
            player.isPlayer ? 'bg-purple-500/20 border border-purple-500/30' : 'bg-gray-500/10'
        }">
            <span class="text-sm ${player.isPlayer ? 'text-purple-400 font-bold' : 'text-gray-300'}">#${index + 1} ${player.name}</span>
            <span class="text-sm text-yellow-400">${player.score}pts</span>
        </div>
    `).join('');
    
    // Update position
    const playerPosition = allPlayers.findIndex(p => p.isPlayer) + 1;
    document.getElementById('currentPosition').textContent = `#${playerPosition}`;
}

function startTournamentTimer() {
    proLeagueGame.timer = setInterval(() => {
        proLeagueGame.timeLeft--;
        
        const minutes = Math.floor(proLeagueGame.timeLeft / 60);
        const seconds = proLeagueGame.timeLeft % 60;
        document.getElementById('tournamentTimer').textContent = 
            `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        // Color coding for urgency
        const timerElement = document.getElementById('tournamentTimer');
        if (proLeagueGame.timeLeft <= 15) {
            timerElement.className = 'text-2xl font-bold text-red-400 animate-pulse';
        } else if (proLeagueGame.timeLeft <= 30) {
            timerElement.className = 'text-2xl font-bold text-yellow-400';
        } else {
            timerElement.className = 'text-2xl font-bold text-cyan-400';
        }
        
        if (proLeagueGame.timeLeft <= 0) {
            endTournamentRound();
        }
    }, 1000);
}

function endTournamentRound() {
    proLeagueGame.isPlaying = false;
    proLeagueGame.isAiming = false;
    proLeagueGame.isCharging = false;
    
    if (proLeagueGame.timer) {
        clearInterval(proLeagueGame.timer);
    }
    
    // Hide crosshair
    document.getElementById('aimCrosshair').classList.add('hidden');
    
    // Calculate final standings
    const allPlayers = [
        { name: 'YOU', score: proLeagueGame.playerScore, isPlayer: true },
        ...proLeagueGame.opponents.map(opp => ({ name: opp.name, score: opp.score, isPlayer: false }))
    ];
    allPlayers.sort((a, b) => b.score - a.score);
    
    const playerPosition = allPlayers.findIndex(p => p.isPlayer) + 1;
    const leagueData = PRO_LEAGUES.find(l => l.name === proLeagueGame.league);
    const diffData = PRO_DIFFICULTIES.find(d => d.name === proLeagueGame.difficulty);
    
    // Determine if player advances/wins
    const topPositions = Math.ceil(leagueData.opponents / 3); // Top 33% advance
    
    if (playerPosition <= topPositions) {
        // Calculate winnings
        let baseMultiplier = leagueData.multiplier * diffData.multiplier;
        
        // Position bonus
        const positionBonus = (topPositions - playerPosition + 1) / topPositions * 2; // Up to 2x for 1st
        
        // Performance bonus
        const avgScore = proLeagueGame.playerScore / proLeagueGame.totalDarts;
        const performanceBonus = Math.min(avgScore / 100, 1.5); // Up to 1.5x for perfect avg
        
        const totalMultiplier = baseMultiplier * (1 + positionBonus + performanceBonus);
        const winnings = Math.floor(proLeagueGame.entryFee * totalMultiplier * 0.8); // 80% base rate
        
        balance += winnings;
        updateBalance();
        
        // Update stats
        proLeagueGame.proPoints += Math.floor(winnings / 50);
        if (playerPosition === 1) {
            proLeagueGame.championships++;
            proLeagueGame.leagueRank = Math.max(1, proLeagueGame.leagueRank - 1);
        }
        
        if (avgScore >= 90) {
            proLeagueGame.perfectGames++;
            proLeagueGame.sponsorshipDeals++;
        }
        
        document.getElementById('throwResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🏆 TOURNAMENT ${playerPosition === 1 ? 'CHAMPION' : 'QUALIFIER'}! 🏆</span>`;
        document.getElementById('tournamentStatus').innerHTML = 
            `Position #${playerPosition}! Score: ${proLeagueGame.playerScore}, Won: ${winnings} GA!`;
    } else {
        proLeagueGame.rivalryLevel++;
        document.getElementById('throwResult').innerHTML = 
            `<span class="text-red-400">💥 ELIMINATED! 💥</span>`;
        document.getElementById('tournamentStatus').innerHTML = 
            `Position #${playerPosition}/${allPlayers.length} - Need top ${topPositions} to advance!`;
    }
    
    updateProDisplay();
    resetTournamentControls();
}

function resetTournamentControls() {
    setTimeout(() => {
        document.getElementById('enterTournament').disabled = false;
        document.getElementById('throwResult').textContent = '';
        document.getElementById('tournamentStatus').textContent = 'Ready for professional competition';
        document.getElementById('gameInstructions').textContent = 
            'Click to aim, hold SPACE to charge power, release to throw!';
        document.getElementById('currentDart').textContent = '0';
        document.getElementById('playerScore').textContent = '0';
        document.getElementById('powerLevel').textContent = '0%';
        document.getElementById('powerMeter').style.width = '0%';
        document.getElementById('currentRound').textContent = '1/3';
        document.getElementById('currentPosition').textContent = '#1';
        
        // Reset game state
        proLeagueGame.currentDart = 0;
        proLeagueGame.playerScore = 0;
        proLeagueGame.throws = [];
        proLeagueGame.powerLevel = 0;
        proLeagueGame.currentRound = 1;
        
        generateOpponents();
        drawProDartboard();
    }, 6000);
}

function drawProDartboard() {
    const ctx = proLeagueGame.ctx;
    const canvas = proLeagueGame.canvas;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw professional dartboard rings
    Object.entries(PRO_TARGETS).reverse().forEach(([key, target]) => {
        if (key === 'miss') return;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, target.radius, 0, 2 * Math.PI);
        ctx.fillStyle = target.color + '30';
        ctx.fill();
        ctx.strokeStyle = target.color;
        ctx.lineWidth = 3;
        ctx.stroke();
        
        // Add labels
        if (target.radius > 15) {
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(target.points + 'pts', centerX, centerY - target.radius + 20);
        }
    });
    
    // Draw center point
    ctx.beginPath();
    ctx.arc(centerX, centerY, 4, 0, 2 * Math.PI);
    ctx.fillStyle = '#ffffff';
    ctx.fill();
    
    // Draw thrown darts
    proLeagueGame.throws.forEach((throwData, index) => {
        ctx.beginPath();
        ctx.arc(throwData.x, throwData.y, 5, 0, 2 * Math.PI);
        ctx.fillStyle = throwData.result.type === 'bullseye' ? '#ff0000' : 
                       throwData.result.type === 'miss' ? '#666666' : '#00ff00';
        ctx.fill();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Add dart number
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(index + 1, throwData.x, throwData.y - 10);
    });
}

function updateProDisplay() {
    document.getElementById('currentDart').textContent = proLeagueGame.currentDart;
    document.getElementById('playerScore').textContent = proLeagueGame.playerScore;
    document.getElementById('leagueRank').textContent = proLeagueGame.leagueRank;
    document.getElementById('proPoints').textContent = proLeagueGame.proPoints;
    document.getElementById('championships').textContent = proLeagueGame.championships;
    document.getElementById('perfectGames').textContent = proLeagueGame.perfectGames;
    document.getElementById('rivalryLevel').textContent = proLeagueGame.rivalryLevel;
    document.getElementById('sponsorshipDeals').textContent = proLeagueGame.sponsorshipDeals;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadProLeagueGame();
});