
// Game state
// let balance = 1000; // Balance is now managed by wallet.js
let pixelJackpotGame = {
    isPlaying: false,
    betAmount: 0,
    gameMode: 'classic',
    difficulty: 'normal',
    pixelSpeed: 'medium',
    targetSize: 'normal',
    powerUps: 'none',
    consecutiveHits: 0,
    totalGames: 0,
    perfectHits: 0,
    maxCombo: 0,
    arcadeLevel: 1,
    pixelEnergy: 0,
    currentTarget: null,
    timeLeft: 0,
    score: 0,
    targets: [],
    gameTimer: null,
    canvas: null,
    ctx: null
};

// Game modes that affect difficulty
const GAME_MODES = [
    { name: 'classic', timeBonus: 1.0, description: 'Classic Arcade' },
    { name: 'speed', timeBonus: 0.7, description: 'Speed Mode' },
    { name: 'precision', timeBonus: 0.5, description: 'Precision Mode' },
    { name: 'chaos', timeBonus: 0.3, description: 'Chaos Mode' },
    { name: 'nightmare', timeBonus: 0.15, description: 'Nightmare Mode' }
];

// Difficulty levels
const DIFFICULTY_LEVELS = [
    { name: 'easy', targetCount: 8, hitWindow: 1.2, description: 'Easy (8 targets)' },
    { name: 'normal', targetCount: 12, hitWindow: 1.0, description: 'Normal (12 targets)' },
    { name: 'hard', targetCount: 18, hitWindow: 0.8, description: 'Hard (18 targets)' },
    { name: 'expert', targetCount: 25, hitWindow: 0.6, description: 'Expert (25 targets)' },
    { name: 'insane', targetCount: 35, hitWindow: 0.4, description: 'Insane (35 targets)' }
];

// Pixel speeds
const PIXEL_SPEEDS = [
    { name: 'slow', speed: 0.8, description: 'Slow Pixels' },
    { name: 'medium', speed: 1.2, description: 'Medium Speed' },
    { name: 'fast', speed: 1.8, description: 'Fast Pixels' },
    { name: 'lightning', speed: 2.5, description: 'Lightning Speed' },
    { name: 'quantum', speed: 3.5, description: 'Quantum Speed' }
];

// Target sizes
const TARGET_SIZES = [
    { name: 'large', size: 1.4, description: 'Large Targets' },
    { name: 'normal', size: 1.0, description: 'Normal Size' },
    { name: 'small', size: 0.7, description: 'Small Targets' },
    { name: 'tiny', size: 0.5, description: 'Tiny Targets' },
    { name: 'micro', size: 0.3, description: 'Micro Targets' }
];

// Power-ups
const POWER_UPS = [
    { name: 'none', effect: 1.0, description: 'No Power-ups' },
    { name: 'slowmo', effect: 0.9, description: 'Slow Motion' },
    { name: 'bigshot', effect: 0.8, description: 'Big Shot' },
    { name: 'multishot', effect: 0.7, description: 'Multi Shot' },
    { name: 'godmode', effect: 0.6, description: 'God Mode' }
];

// Update balance display
function updateBalance() {
    const balance = getWalletBalance();
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
    
    // Update max bet amount
    const betInput = document.getElementById('pixelBet');
    if (betInput) {
        betInput.max = balance;
    }
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Pixel Jackpot Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎮 PIXEL JACKPOT 🎮</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 ARCADE BET</label>
                        <input type="number" id="pixelBet" value="20" min="5" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic" selected>🕹️ Classic Arcade</option>
                            <option value="speed">⚡ Speed Mode</option>
                            <option value="precision">🎯 Precision Mode</option>
                            <option value="chaos">🌪️ Chaos Mode</option>
                            <option value="nightmare">💀 Nightmare Mode</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="easy">🟢 Easy (8 targets)</option>
                            <option value="normal" selected>🟡 Normal (12 targets)</option>
                            <option value="hard">🟠 Hard (18 targets)</option>
                            <option value="expert">🔴 Expert (25 targets)</option>
                            <option value="insane">💀 Insane (35 targets)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏃 PIXEL SPEED</label>
                        <select id="pixelSpeed" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="slow">🐌 Slow Pixels</option>
                            <option value="medium" selected>🚶 Medium Speed</option>
                            <option value="fast">🏃 Fast Pixels</option>
                            <option value="lightning">⚡ Lightning Speed</option>
                            <option value="quantum">⚛️ Quantum Speed</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET SIZE</label>
                        <select id="targetSize" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="large">🔵 Large Targets</option>
                            <option value="normal" selected>⚪ Normal Size</option>
                            <option value="small">🔸 Small Targets</option>
                            <option value="tiny">🔹 Tiny Targets</option>
                            <option value="micro">• Micro Targets</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💫 POWER-UPS</label>
                        <select id="powerUps" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="none" selected>❌ No Power-ups</option>
                            <option value="slowmo">⏰ Slow Motion</option>
                            <option value="bigshot">💥 Big Shot</option>
                            <option value="multishot">🔫 Multi Shot</option>
                            <option value="godmode">👑 God Mode</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="startPixelGame" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🎮 START PIXEL HUNT
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎯 Success Rate</div>
                        <div id="pixelWinChance" class="text-lg font-bold text-red-400">5%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="pixelPotentialPayout" class="text-2xl font-bold text-purple-400 neon-glow">400 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🎮 Arcade Level</div>
                        <div id="arcadeLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⚡ Pixel Energy</div>
                        <div id="pixelEnergyDisplay" class="text-lg font-bold text-cyan-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Game Canvas -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400 text-center">🎮 PIXEL ARENA 🎮</h5>
                    
                    <!-- Game Canvas -->
                    <div class="mb-6">
                        <canvas id="pixelCanvas" width="400" height="300" 
                                class="w-full border border-purple-400/50 rounded-lg bg-black/80">
                        </canvas>
                    </div>
                    
                    <!-- Game Stats -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🎮 Game Status</div>
                            <div id="gameStatus" class="text-lg font-bold text-purple-400">Ready to play...</div>
                            <div class="text-xs text-gray-400">Mode: <span id="modeStatus" class="text-blue-400">Classic</span></div>
                            <div class="text-xs text-gray-400">Difficulty: <span id="difficultyStatus" class="text-orange-400">Normal</span></div>
                            <div class="text-xs text-gray-400">Speed: <span id="speedStatus" class="text-green-400">Medium</span></div>
                        </div>
                    </div>
                    
                    <!-- Score Display -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="text-sm text-gray-400">Score</div>
                                    <div id="currentScore" class="text-lg font-bold text-cyan-400">0</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-400">Hits</div>
                                    <div id="currentHits" class="text-lg font-bold text-green-400">0</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-400">Time</div>
                                    <div id="timeRemaining" class="text-lg font-bold text-red-400">30s</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Combo Meter -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🔥 Combo Meter</div>
                            <div class="w-full bg-black/50 rounded-full h-3">
                                <div id="comboMeter" class="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="comboLevel" class="text-center mt-1 text-orange-300">No Combo</div>
                        </div>
                    </div>
                    
                    <div id="pixelResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="pixelStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Pixel Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 ARCADE RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Perfect Games</div>
                            <div id="consecutiveHits" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Games</div>
                            <div id="totalGames" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Perfect Hits</div>
                            <div id="perfectHits" class="text-lg font-bold text-yellow-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Max Combo</div>
                            <div id="maxCombo" class="text-lg font-bold text-orange-400">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- Pixel Power System -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">⚡ PIXEL POWER ⚡</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="pixelPowerBar" class="bg-gradient-to-r from-purple-500 to-cyan-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="pixelPowerText" class="text-center mt-2 text-cyan-300">Pixel Power Charging...</div>
                </div>
            </div>
        </div>
    `;
    
    initializePixelJackpot();
}

function initializePixelJackpot() {
    pixelJackpotGame.canvas = document.getElementById('pixelCanvas');
    pixelJackpotGame.ctx = pixelJackpotGame.canvas.getContext('2d');
    
    document.getElementById('startPixelGame').addEventListener('click', startPixelGame);
    document.getElementById('gameMode').addEventListener('change', updateGameMode);
    document.getElementById('difficulty').addEventListener('change', updateDifficulty);
    document.getElementById('pixelSpeed').addEventListener('change', updatePixelSpeed);
    document.getElementById('targetSize').addEventListener('change', updateTargetSize);
    document.getElementById('powerUps').addEventListener('change', updatePowerUps);
    
    // Add click listener to canvas
    pixelJackpotGame.canvas.addEventListener('click', handleCanvasClick);
    
    updateGameMode();
    updateDifficulty();
    updatePixelSpeed();
    updateTargetSize();
    updatePowerUps();
    updatePixelPayout();
    
    drawPixelArena();
}

function updateGameMode() {
    const mode = document.getElementById('gameMode').value;
    pixelJackpotGame.gameMode = mode;
    
    const modeData = GAME_MODES.find(m => m.name === mode);
    document.getElementById('modeStatus').textContent = modeData.description;
    
    updatePixelPayout();
}

function updateDifficulty() {
    const difficulty = document.getElementById('difficulty').value;
    pixelJackpotGame.difficulty = difficulty;
    
    const difficultyData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    document.getElementById('difficultyStatus').textContent = difficultyData.description;
    
    updatePixelPayout();
}

function updatePixelSpeed() {
    const speed = document.getElementById('pixelSpeed').value;
    pixelJackpotGame.pixelSpeed = speed;
    
    const speedData = PIXEL_SPEEDS.find(s => s.name === speed);
    document.getElementById('speedStatus').textContent = speedData.description;
    
    updatePixelPayout();
}

function updateTargetSize() {
    const size = document.getElementById('targetSize').value;
    pixelJackpotGame.targetSize = size;
    
    updatePixelPayout();
}

function updatePowerUps() {
    const powerUps = document.getElementById('powerUps').value;
    pixelJackpotGame.powerUps = powerUps;
    
    updatePixelPayout();
}

function updatePixelPayout() {
    const betAmount = parseInt(document.getElementById('pixelBet').value) || 20;
    
    // Calculate extremely low success probability (1-8%)
    const modeData = GAME_MODES.find(m => m.name === pixelJackpotGame.gameMode);
    const difficultyData = DIFFICULTY_LEVELS.find(d => d.name === pixelJackpotGame.difficulty);
    const speedData = PIXEL_SPEEDS.find(s => s.name === pixelJackpotGame.pixelSpeed);
    const sizeData = TARGET_SIZES.find(s => s.name === pixelJackpotGame.targetSize);
    const powerData = POWER_UPS.find(p => p.name === pixelJackpotGame.powerUps);
    
    // Base success chance is extremely low
    const baseSuccessChance = 0.08; // 8% base
    const finalSuccessChance = Math.max(0.01, 
        baseSuccessChance * modeData.timeBonus * difficultyData.hitWindow * 
        sizeData.size / speedData.speed * powerData.effect);
    
    const successPercent = Math.max(1, Math.floor(finalSuccessChance * 100));
    const multiplier = Math.floor(100 / finalSuccessChance * 0.2); // Very low multiplier
    const potentialPayout = Math.floor(betAmount * multiplier);
    
    document.getElementById('pixelWinChance').textContent = successPercent + '%';
    document.getElementById('pixelPotentialPayout').textContent = potentialPayout + ' GA';
}

function startPixelGame() {
    const betAmount = parseInt(document.getElementById('pixelBet').value);
    
    if (pixelJackpotGame.isPlaying) return;

    if (!subtractFromWallet(betAmount)) {
        alert('Insufficient arcade credits!');
        return;
    }
    
    pixelJackpotGame.betAmount = betAmount;
    pixelJackpotGame.isPlaying = true;
    pixelJackpotGame.totalGames++;
    pixelJackpotGame.score = 0;
    pixelJackpotGame.targets = [];
    pixelJackpotGame.consecutiveHits = 0;
    
    // Get game settings
    const modeData = GAME_MODES.find(m => m.name === pixelJackpotGame.gameMode);
    const difficultyData = DIFFICULTY_LEVELS.find(d => d.name === pixelJackpotGame.difficulty);
    
    pixelJackpotGame.timeLeft = Math.floor(30 * modeData.timeBonus);
    
    // Disable start button
    document.getElementById('startPixelGame').disabled = true;
    
    document.getElementById('pixelResult').textContent = '';
    document.getElementById('pixelStatus').textContent = 'Pixel hunt in progress...';
    document.getElementById('gameStatus').textContent = 'HUNTING PIXELS...';
    
    // Generate targets
    generatePixelTargets();
    
    // Start game timer
    startPixelTimer();
}

function generatePixelTargets() {
    const difficultyData = DIFFICULTY_LEVELS.find(d => d.name === pixelJackpotGame.difficulty);
    const speedData = PIXEL_SPEEDS.find(s => s.name === pixelJackpotGame.pixelSpeed);
    const sizeData = TARGET_SIZES.find(s => s.name === pixelJackpotGame.targetSize);
    
    pixelJackpotGame.targets = [];
    
    for (let i = 0; i < difficultyData.targetCount; i++) {
        const target = {
            x: Math.random() * (pixelJackpotGame.canvas.width - 40) + 20,
            y: Math.random() * (pixelJackpotGame.canvas.height - 40) + 20,
            vx: (Math.random() - 0.5) * speedData.speed * 4,
            vy: (Math.random() - 0.5) * speedData.speed * 4,
            size: Math.floor(15 * sizeData.size),
            color: `hsl(${Math.random() * 360}, 70%, 60%)`,
            hit: false,
            id: i
        };
        pixelJackpotGame.targets.push(target);
    }
}

function startPixelTimer() {
    pixelJackpotGame.gameTimer = setInterval(() => {
        if (pixelJackpotGame.timeLeft <= 0) {
            endPixelGame();
            return;
        }
        
        pixelJackpotGame.timeLeft--;
        updatePixelDisplay();
        updateTargets();
        drawPixelArena();
        
    }, 1000);
}

function updateTargets() {
    const speedData = PIXEL_SPEEDS.find(s => s.name === pixelJackpotGame.pixelSpeed);
    
    pixelJackpotGame.targets.forEach(target => {
        if (target.hit) return;
        
        // Update position
        target.x += target.vx;
        target.y += target.vy;
        
        // Bounce off walls
        if (target.x <= target.size || target.x >= pixelJackpotGame.canvas.width - target.size) {
            target.vx *= -1;
        }
        if (target.y <= target.size || target.y >= pixelJackpotGame.canvas.height - target.size) {
            target.vy *= -1;
        }
        
        // Keep in bounds
        target.x = Math.max(target.size, Math.min(pixelJackpotGame.canvas.width - target.size, target.x));
        target.y = Math.max(target.size, Math.min(pixelJackpotGame.canvas.height - target.size, target.y));
        
        // Random direction changes (chaos mode)
        if (pixelJackpotGame.gameMode === 'chaos' && Math.random() < 0.05) {
            target.vx = (Math.random() - 0.5) * speedData.speed * 6;
            target.vy = (Math.random() - 0.5) * speedData.speed * 6;
        }
    });
}

function handleCanvasClick(event) {
    if (!pixelJackpotGame.isPlaying) return;
    
    const rect = pixelJackpotGame.canvas.getBoundingClientRect();
    const scaleX = pixelJackpotGame.canvas.width / rect.width;
    const scaleY = pixelJackpotGame.canvas.height / rect.height;
    
    const clickX = (event.clientX - rect.left) * scaleX;
    const clickY = (event.clientY - rect.top) * scaleY;
    
    // Check for hits
    let hitTarget = null;
    for (let target of pixelJackpotGame.targets) {
        if (target.hit) continue;
        
        const distance = Math.sqrt((clickX - target.x) ** 2 + (clickY - target.y) ** 2);
        if (distance <= target.size) {
            hitTarget = target;
            break;
        }
    }
    
    if (hitTarget) {
        hitTarget.hit = true;
        pixelJackpotGame.score += 100;
        pixelJackpotGame.consecutiveHits++;
        pixelJackpotGame.perfectHits++;
        
        // Update combo
        const comboPercent = Math.min(100, (pixelJackpotGame.consecutiveHits / 10) * 100);
        document.getElementById('comboMeter').style.width = comboPercent + '%';
        document.getElementById('comboLevel').textContent = `${pixelJackpotGame.consecutiveHits}x Combo`;
        
        // Check if all targets hit
        const remainingTargets = pixelJackpotGame.targets.filter(t => !t.hit).length;
        if (remainingTargets === 0) {
            endPixelGame(true);
        }
    }
    
    updatePixelDisplay();
}

function drawPixelArena() {
    const ctx = pixelJackpotGame.ctx;
    
    // Clear canvas
    ctx.fillStyle = '#000011';
    ctx.fillRect(0, 0, pixelJackpotGame.canvas.width, pixelJackpotGame.canvas.height);
    
    // Draw grid
    ctx.strokeStyle = '#333366';
    ctx.lineWidth = 1;
    for (let x = 0; x < pixelJackpotGame.canvas.width; x += 20) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, pixelJackpotGame.canvas.height);
        ctx.stroke();
    }
    for (let y = 0; y < pixelJackpotGame.canvas.height; y += 20) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(pixelJackpotGame.canvas.width, y);
        ctx.stroke();
    }
    
    // Draw targets
    pixelJackpotGame.targets.forEach(target => {
        if (target.hit) {
            // Draw hit effect
            ctx.fillStyle = '#00ff0050';
            ctx.beginPath();
            ctx.arc(target.x, target.y, target.size * 2, 0, Math.PI * 2);
            ctx.fill();
        } else {
            // Draw target
            ctx.fillStyle = target.color;
            ctx.beginPath();
            ctx.arc(target.x, target.y, target.size, 0, Math.PI * 2);
            ctx.fill();
            
            // Draw target border
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
    });
    
    // Draw crosshair if game is active
    if (pixelJackpotGame.isPlaying) {
        ctx.strokeStyle = '#ff0066';
        ctx.lineWidth = 2;
        const centerX = pixelJackpotGame.canvas.width / 2;
        const centerY = pixelJackpotGame.canvas.height / 2;
        
        ctx.beginPath();
        ctx.moveTo(centerX - 10, centerY);
        ctx.lineTo(centerX + 10, centerY);
        ctx.moveTo(centerX, centerY - 10);
        ctx.lineTo(centerX, centerY + 10);
        ctx.stroke();
    }
}

function endPixelGame(perfect = false) {
    pixelJackpotGame.isPlaying = false;
    clearInterval(pixelJackpotGame.gameTimer);
    
    const difficultyData = DIFFICULTY_LEVELS.find(d => d.name === pixelJackpotGame.difficulty);
    const targetsHit = pixelJackpotGame.targets.filter(t => t.hit).length;
    const hitPercentage = (targetsHit / difficultyData.targetCount) * 100;
    
    // Extremely strict winning conditions
    let won = false;
    if (perfect && targetsHit === difficultyData.targetCount && pixelJackpotGame.timeLeft > 0) {
        // Perfect game - all targets hit with time remaining
        won = true;
        pixelJackpotGame.arcadeLevel += 2;
    } else if (hitPercentage >= 95 && pixelJackpotGame.timeLeft > 5) {
        // Near perfect - 95%+ targets with 5+ seconds remaining
        won = true;
        pixelJackpotGame.arcadeLevel++;
    }
    
    if (won) {
        // Calculate winnings
        const baseMultiplier = perfect ? 20 : 10;
        const timeBonus = Math.floor(pixelJackpotGame.timeLeft / 5);
        const comboBonus = Math.floor(pixelJackpotGame.consecutiveHits / 5);
        const totalMultiplier = baseMultiplier + timeBonus + comboBonus;
        
        const payout = Math.floor(pixelJackpotGame.betAmount * totalMultiplier * 0.1); // Very low multiplier
        if (payout > 0) {
            addToWallet(payout);
            document.getElementById('pixelResult').textContent = `🎉 YOU WON ${payout} GA! 🎉`;
        }
        document.getElementById('pixelStatus').innerHTML = 
            `Perfect pixel hunt! Hit ${targetsHit}/${difficultyData.targetCount} targets! Won ${payout} GA!`;
    } else {
        document.getElementById('pixelResult').innerHTML = 
            `<span class="text-red-400">💥 PIXELS ESCAPED! 💥</span>`;
        document.getElementById('pixelStatus').textContent = 
            `Hit ${targetsHit}/${difficultyData.targetCount} targets (${Math.floor(hitPercentage)}%). Need 95%+ to win!`;
    }
    
    // Update max combo
    pixelJackpotGame.maxCombo = Math.max(pixelJackpotGame.maxCombo, pixelJackpotGame.consecutiveHits);
    
    updatePixelDisplay();
    resetPixelControls();
}

function resetPixelControls() {
    setTimeout(() => {
        document.getElementById('startPixelGame').disabled = false;
        document.getElementById('gameStatus').textContent = 'Ready to play...';
        pixelJackpotGame.targets = [];
        drawPixelArena();
    }, 3000);
}

function updatePixelDisplay() {
    document.getElementById('currentScore').textContent = pixelJackpotGame.score;
    document.getElementById('currentHits').textContent = pixelJackpotGame.targets.filter(t => t.hit).length;
    document.getElementById('timeRemaining').textContent = pixelJackpotGame.timeLeft + 's';
    document.getElementById('arcadeLevelDisplay').textContent = pixelJackpotGame.arcadeLevel;
    document.getElementById('consecutiveHits').textContent = pixelJackpotGame.consecutiveHits;
    document.getElementById('totalGames').textContent = pixelJackpotGame.totalGames;
    document.getElementById('perfectHits').textContent = pixelJackpotGame.perfectHits;
    document.getElementById('maxCombo').textContent = pixelJackpotGame.maxCombo;
    
    // Update pixel energy
    pixelJackpotGame.pixelEnergy = Math.min(100, (pixelJackpotGame.arcadeLevel / 20) * 100);
    document.getElementById('pixelEnergyDisplay').textContent = Math.floor(pixelJackpotGame.pixelEnergy) + '%';
    
    // Update pixel power bar
    document.getElementById('pixelPowerBar').style.width = pixelJackpotGame.pixelEnergy + '%';
    
    let powerStatus = 'Pixel Power Charging...';
    if (pixelJackpotGame.pixelEnergy >= 100) powerStatus = 'MAXIMUM PIXEL POWER!';
    else if (pixelJackpotGame.pixelEnergy >= 75) powerStatus = 'High Pixel Energy';
    else if (pixelJackpotGame.pixelEnergy >= 50) powerStatus = 'Pixel Power Building';
    else if (pixelJackpotGame.pixelEnergy >= 25) powerStatus = 'Pixel Energy Rising';
    
    document.getElementById('pixelPowerText').textContent = powerStatus;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Listen for balance changes
    window.addEventListener('walletBalanceChanged', updateBalance);

    updateBalance();
    loadArcticAdventureGame();
});

