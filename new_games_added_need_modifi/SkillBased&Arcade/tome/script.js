// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadTomeGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                            <h4 class="text-xl font-bold mb-4 text-purple-400">TOME OF LIFE</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">OFFERING AMOUNT</label>
                                <input type="number" id="tomeBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">CHAPTER DIFFICULTY</label>
                                <select id="tomeChapter" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="1">Chapter 1 - Novice (2x max)</option>
                                    <option value="2" selected>Chapter 2 - Adept (5x max)</option>
                                    <option value="3">Chapter 3 - Master (10x max)</option>
                                    <option value="4">Chapter 4 - Sage (25x max)</option>
                                    <option value="5">Chapter 5 - Archon (100x max)</option>
                                </select>
                            </div>
                            
                            <button id="startTome" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                OPEN TOME
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Chapter</div>
                                <div id="tomeCurrentChapter" class="text-xl font-bold text-purple-400">-</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Life Force</div>
                                <div id="tomeLifeForce" class="text-xl font-bold text-green-400 neon-glow">100%</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Wisdom Gained</div>
                                <div id="tomeWisdom" class="text-xl font-bold text-blue-400">0</div>
                            </div>
                        </div>
                        
                        <!-- Character Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-purple-400">CHARACTER STATUS</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Courage:</span>
                                    <span id="tomeCourage" class="text-red-400">50</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Intelligence:</span>
                                    <span id="tomeIntelligence" class="text-blue-400">50</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Luck:</span>
                                    <span id="tomeLuck" class="text-green-400">50</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Experience:</span>
                                    <span id="tomeExperience" class="text-yellow-400">0</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Rewards -->
                        <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-purple-400">CURRENT QUEST</h5>
                            <div id="tomeQuestReward" class="text-center">
                                <div class="text-gray-400">No active quest</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tome Display -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                            <div id="tomeDisplay" class="relative bg-gradient-to-br from-purple-900 to-black rounded-lg p-6 h-96 overflow-y-auto">
                                <!-- Tome Content -->
                                <div id="tomeContent" class="text-center">
                                    <div class="text-6xl mb-4">📚</div>
                                    <h3 class="text-2xl font-bold text-purple-400 mb-4">The Digital Tome of Life</h3>
                                    <p class="text-gray-300 mb-6">
                                        Ancient wisdom awaits those brave enough to seek it. 
                                        Each chapter holds mysteries that can either grant great rewards 
                                        or test your resolve. Choose your path wisely...
                                    </p>
                                    <p class="text-yellow-400 text-sm">
                                        Select a chapter and make an offering to begin your journey.
                                    </p>
                                </div>
                                
                                <!-- Quest Content (Hidden initially) -->
                                <div id="questContent" class="hidden">
                                    <div id="questStory" class="mb-6 text-gray-300"></div>
                                    <div id="questChoices" class="space-y-3">
                                        <!-- Choices will be generated here -->
                                    </div>
                                </div>
                            </div>
                            <div id="tomeStatus" class="text-center mt-4 text-lg font-semibold">Make an offering to unlock the tome's secrets</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeTome();
        }
        
        let tomeGame = {
            isPlaying: false,
            chapter: 2,
            betAmount: 0,
            lifeForce: 100,
            wisdom: 0,
            courage: 50,
            intelligence: 50,
            luck: 50,
            experience: 0,
            currentQuest: null,
            questStep: 0
        };
        
        function initializeTome() {
            document.getElementById('startTome').addEventListener('click', startTomeGame);
            document.getElementById('tomeChapter').addEventListener('change', updateTomeChapter);
        }
        
        function updateTomeChapter() {
            tomeGame.chapter = parseInt(document.getElementById('tomeChapter').value);
        }
        
        function startTomeGame() {
            const betAmount = parseInt(document.getElementById('tomeBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize game state
            tomeGame.isPlaying = true;
            tomeGame.betAmount = betAmount;
            tomeGame.lifeForce = 100;
            tomeGame.wisdom = 0;
            tomeGame.questStep = 0;
            
            // Generate random starting stats
            tomeGame.courage = Math.floor(Math.random() * 50) + 25;
            tomeGame.intelligence = Math.floor(Math.random() * 50) + 25;
            tomeGame.luck = Math.floor(Math.random() * 50) + 25;
            
            // Update UI
            document.getElementById('startTome').disabled = true;
            document.getElementById('tomeCurrentChapter').textContent = tomeGame.chapter;
            updateTomeStats();
            
            // Generate quest
            generateQuest();
        }
        
        function updateTomeStats() {
            document.getElementById('tomeLifeForce').textContent = tomeGame.lifeForce + '%';
            document.getElementById('tomeWisdom').textContent = tomeGame.wisdom;
            document.getElementById('tomeCourage').textContent = tomeGame.courage;
            document.getElementById('tomeIntelligence').textContent = tomeGame.intelligence;
            document.getElementById('tomeLuck').textContent = tomeGame.luck;
            document.getElementById('tomeExperience').textContent = tomeGame.experience;
        }
        
        function generateQuest() {
            const quests = getQuestsForChapter(tomeGame.chapter);
            tomeGame.currentQuest = quests[Math.floor(Math.random() * quests.length)];
            
            document.getElementById('tomeContent').classList.add('hidden');
            document.getElementById('questContent').classList.remove('hidden');
            
            displayQuestStep();
        }
        
        function getQuestsForChapter(chapter) {
            const questTemplates = {
                1: [
                    {
                        title: "The Glowing Orb",
                        steps: [
                            {
                                story: "You discover a glowing orb in an ancient chamber. It pulses with mysterious energy.",
                                choices: [
                                    { text: "Touch the orb", stat: "courage", difficulty: 30, reward: 1.5 },
                                    { text: "Study it carefully", stat: "intelligence", difficulty: 20, reward: 1.2 },
                                    { text: "Leave it alone", stat: "luck", difficulty: 10, reward: 1.1 }
                                ]
                            }
                        ]
                    }
                ],
                2: [
                    {
                        title: "The Riddling Sphinx",
                        steps: [
                            {
                                story: "A digital sphinx blocks your path, asking a riddle about the nature of existence.",
                                choices: [
                                    { text: "Answer with logic", stat: "intelligence", difficulty: 40, reward: 2.5 },
                                    { text: "Answer with heart", stat: "courage", difficulty: 35, reward: 2.2 },
                                    { text: "Guess randomly", stat: "luck", difficulty: 60, reward: 3.0 }
                                ]
                            }
                        ]
                    }
                ],
                3: [
                    {
                        title: "The Temporal Rift",
                        steps: [
                            {
                                story: "You encounter a rift in time itself. Through it, you see visions of possible futures.",
                                choices: [
                                    { text: "Step through boldly", stat: "courage", difficulty: 50, reward: 5.0 },
                                    { text: "Analyze the patterns", stat: "intelligence", difficulty: 45, reward: 4.5 },
                                    { text: "Trust your instincts", stat: "luck", difficulty: 55, reward: 6.0 }
                                ]
                            }
                        ]
                    }
                ],
                4: [
                    {
                        title: "The Cosmic Guardian",
                        steps: [
                            {
                                story: "An ancient guardian of cosmic knowledge challenges you to prove your worth.",
                                choices: [
                                    { text: "Face it in combat", stat: "courage", difficulty: 65, reward: 12.0 },
                                    { text: "Engage in debate", stat: "intelligence", difficulty: 60, reward: 10.0 },
                                    { text: "Seek divine favor", stat: "luck", difficulty: 70, reward: 15.0 }
                                ]
                            }
                        ]
                    }
                ],
                5: [
                    {
                        title: "The Source of All",
                        steps: [
                            {
                                story: "You stand before the source of all digital existence. The final truth awaits.",
                                choices: [
                                    { text: "Merge with the source", stat: "courage", difficulty: 80, reward: 50.0 },
                                    { text: "Comprehend its essence", stat: "intelligence", difficulty: 75, reward: 40.0 },
                                    { text: "Channel pure chance", stat: "luck", difficulty: 85, reward: 80.0 }
                                ]
                            }
                        ]
                    }
                ]
            };
            
            return questTemplates[chapter] || questTemplates[1];
        }
        
        function displayQuestStep() {
            const quest = tomeGame.currentQuest;
            const step = quest.steps[tomeGame.questStep];
            
            document.getElementById('questStory').innerHTML = `
                <h4 class="text-xl font-bold text-purple-400 mb-3">${quest.title}</h4>
                <p>${step.story}</p>
            `;
            
            const choicesDiv = document.getElementById('questChoices');
            choicesDiv.innerHTML = '';
            
            step.choices.forEach((choice, index) => {
                const button = document.createElement('button');
                button.className = 'w-full py-3 px-4 rounded-lg font-bold text-white border-2 border-purple-500 bg-purple-600/20 hover:bg-purple-600/40 transition-all';
                button.innerHTML = `
                    <div class="flex justify-between items-center">
                        <span>${choice.text}</span>
                        <span class="text-sm text-gray-400">(${choice.stat.charAt(0).toUpperCase() + choice.stat.slice(1)})</span>
                    </div>
                `;
                button.addEventListener('click', () => makeChoice(index));
                choicesDiv.appendChild(button);
            });
            
            // Update quest reward display
            const maxReward = Math.max(...step.choices.map(c => c.reward));
            document.getElementById('tomeQuestReward').innerHTML = `
                <div class="text-green-400 text-lg font-bold">Up to ${maxReward}x</div>
                <div class="text-sm text-gray-400">Max potential multiplier</div>
            `;
        }
        
        function makeChoice(choiceIndex) {
            const quest = tomeGame.currentQuest;
            const step = quest.steps[tomeGame.questStep];
            const choice = step.choices[choiceIndex];
            
            // Get player's stat for this choice
            const playerStat = tomeGame[choice.stat];
            
            // Check success based on stat vs difficulty
            const successChance = Math.max(10, Math.min(90, playerStat + tomeGame.luck / 2 - choice.difficulty + 20));
            const success = Math.random() * 100 < successChance;
            
            // Calculate result
            let multiplier = 1;
            let lifeLoss = 0;
            let wisdomGain = 0;
            let experienceGain = 0;
            
            if (success) {
                multiplier = choice.reward;
                wisdomGain = Math.floor(choice.difficulty / 10);
                experienceGain = choice.difficulty;
                
                // Boost stats slightly on success
                tomeGame[choice.stat] = Math.min(100, tomeGame[choice.stat] + 5);
            } else {
                lifeLoss = Math.floor(choice.difficulty / 3);
                multiplier = 0.1; // Small consolation reward
                experienceGain = Math.floor(choice.difficulty / 3);
            }
            
            // Apply changes
            tomeGame.lifeForce = Math.max(0, tomeGame.lifeForce - lifeLoss);
            tomeGame.wisdom += wisdomGain;
            tomeGame.experience += experienceGain;
            
            // Calculate winnings
            const winnings = Math.floor(tomeGame.betAmount * multiplier);
            balance += winnings;
            updateBalance();
            updateTomeStats();
            
            // Show result
            const resultText = success ? 
                `<span class="text-green-400 neon-glow">Success! Your ${choice.stat} served you well. Won $${winnings}!</span>` :
                `<span class="text-red-400">Failed! Lost ${lifeLoss}% life force. Won $${winnings}</span>`;
            
            document.getElementById('tomeStatus').innerHTML = resultText;
            
            // End game
            setTimeout(() => {
                endTomeGame();
            }, 3000);
        }
        
        function endTomeGame() {
            tomeGame.isPlaying = false;
            
            // Reset UI
            document.getElementById('startTome').disabled = false;
            document.getElementById('tomeContent').classList.remove('hidden');
            document.getElementById('questContent').classList.add('hidden');
            document.getElementById('tomeCurrentChapter').textContent = '-';
            document.getElementById('tomeQuestReward').innerHTML = '<div class="text-gray-400">No active quest</div>';
            
            // Reset stats
            tomeGame.lifeForce = 100;
            tomeGame.wisdom = 0;
            tomeGame.experience = 0;
            updateTomeStats();
            
            document.getElementById('tomeStatus').textContent = 'Make an offering to unlock the tome\'s secrets';
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadTomeGame();
});