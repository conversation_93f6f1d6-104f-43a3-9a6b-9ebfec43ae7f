// Game state
let balance = 1000;

// Slingo Showdown 6 game state
let slingoGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'rookie',
    gameMode: 'classic',
    showdownLevel: 6,
    
    // Enhanced Slingo mechanics
    slingoCard: [],
    markedNumbers: [],
    calledNumbers: [],
    currentNumber: null,
    numbersLeft: 75,
    ballsDrawn: 0,
    maxBalls: 20,
    
    // Showdown 6 features
    powerUps: [],
    multiplierChain: 1.0,
    bonusRounds: 0,
    perfectMarks: 0,
    streakCount: 0,
    maxStreak: 0,
    
    // Advanced scoring
    slingoLines: 0,
    diagonalLines: 0,
    fullHouse: false,
    blackout: false,
    cornerBonus: false,
    centerBonus: false,
    
    // Performance tracking
    totalGames: 0,
    winStreak: 0,
    maxWinStreak: 0,
    averageMarks: 0,
    skillRating: 0,
    
    // Showdown 6 special features
    wildCards: 0,
    superWilds: 0,
    jokerBalls: 0,
    devilBalls: 0,
    bonusSpins: 0,
    
    // Enhanced timing mechanics
    reactionTimes: [],
    markingAccuracy: 0,
    speedBonus: 0,
    consistencyRating: 0,
    
    // Progressive features
    experiencePoints: 0,
    showdownRank: 'Slingo Novice',
    unlockedPatterns: ['line', 'diagonal'],
    achievements: []
};

// Ultra-strict Showdown 6 difficulty settings
const DIFFICULTY_LEVELS = [
    {
        name: 'rookie',
        description: 'Slingo Rookie (5% win rate)',
        winRate: 0.05,
        multiplier: 1.3,
        ballCount: 20,
        requiredLines: 3,
        timeLimit: 180,
        wildChance: 0.08,
        jokerChance: 0.05
    },
    {
        name: 'amateur',
        description: 'Slingo Amateur (4% win rate)',
        winRate: 0.04,
        multiplier: 1.7,
        ballCount: 18,
        requiredLines: 4,
        timeLimit: 150,
        wildChance: 0.06,
        jokerChance: 0.04
    },
    {
        name: 'pro',
        description: 'Slingo Professional (3% win rate)',
        winRate: 0.03,
        multiplier: 2.2,
        ballCount: 16,
        requiredLines: 4,
        timeLimit: 120,
        wildChance: 0.05,
        jokerChance: 0.03
    },
    {
        name: 'expert',
        description: 'Slingo Expert (2.5% win rate)',
        winRate: 0.025,
        multiplier: 2.8,
        ballCount: 15,
        requiredLines: 5,
        timeLimit: 100,
        wildChance: 0.04,
        jokerChance: 0.025
    },
    {
        name: 'master',
        description: 'Slingo Master (2% win rate)',
        winRate: 0.02,
        multiplier: 3.5,
        ballCount: 14,
        requiredLines: 5,
        timeLimit: 90,
        wildChance: 0.03,
        jokerChance: 0.02
    },
    {
        name: 'grandmaster',
        description: 'Slingo Grandmaster (1.5% win rate)',
        winRate: 0.015,
        multiplier: 4.2,
        ballCount: 12,
        requiredLines: 6,
        timeLimit: 75,
        wildChance: 0.025,
        jokerChance: 0.015
    }
];

const GAME_MODES = [
    {
        name: 'classic',
        description: 'Classic Slingo',
        cardSize: 5,
        paylineMultiplier: 1.0,
        specialFeatures: false
    },
    {
        name: 'turbo',
        description: 'Turbo Slingo',
        cardSize: 5,
        paylineMultiplier: 1.4,
        specialFeatures: true
    },
    {
        name: 'mega',
        description: 'Mega Slingo',
        cardSize: 5,
        paylineMultiplier: 1.8,
        specialFeatures: true
    },
    {
        name: 'showdown',
        description: 'Showdown 6 Elite',
        cardSize: 5,
        paylineMultiplier: 2.2,
        specialFeatures: true
    },
    {
        name: 'ultimate',
        description: 'Ultimate Showdown',
        cardSize: 5,
        paylineMultiplier: 2.8,
        specialFeatures: true
    }
];

// Enhanced special balls for Showdown 6
const SPECIAL_BALLS = [
    { type: 'wild', symbol: '🌟', description: 'Wild - Mark any number in column', chance: 0.08, color: '#ffd700' },
    { type: 'super_wild', symbol: '⭐', description: 'Super Wild - Mark any number anywhere', chance: 0.03, color: '#ff6b35' },
    { type: 'joker', symbol: '🃏', description: 'Joker - Mark entire column', chance: 0.02, color: '#9945ff' },
    { type: 'devil', symbol: '👹', description: 'Devil - Removes random marked number', chance: 0.015, color: '#ff0000' },
    { type: 'bonus', symbol: '💎', description: 'Bonus - Extra ball and multiplier', chance: 0.01, color: '#00ffff' },
    { type: 'time', symbol: '⏰', description: 'Time Bonus - Adds 30 seconds', chance: 0.008, color: '#00ff00' }
];

// Winning patterns for Showdown 6
const WINNING_PATTERNS = [
    { name: 'Single Line', description: 'Any horizontal line', multiplier: 1.2, pattern: 'line' },
    { name: 'Double Line', description: 'Two horizontal lines', multiplier: 1.8, pattern: 'double_line' },
    { name: 'Triple Line', description: 'Three horizontal lines', multiplier: 2.5, pattern: 'triple_line' },
    { name: 'Diagonal', description: 'Any diagonal line', multiplier: 2.0, pattern: 'diagonal' },
    { name: 'Four Corners', description: 'All four corner numbers', multiplier: 2.2, pattern: 'corners' },
    { name: 'X Pattern', description: 'Both diagonal lines', multiplier: 3.0, pattern: 'x_pattern' },
    { name: 'Full House', description: 'All numbers except center', multiplier: 4.0, pattern: 'full_house' },
    { name: 'Blackout', description: 'All 25 numbers marked', multiplier: 6.0, pattern: 'blackout' },
    { name: 'Showdown 6', description: 'Special Showdown pattern', multiplier: 8.0, pattern: 'showdown_6' }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadSlingoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Slingo Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎯 SLINGO SHOWDOWN 6 🎯</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 SHOWDOWN BET</label>
                        <input type="number" id="slingoBet" value="75" min="25" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateSlingoPayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🏆 SKILL LEVEL</label>
                            <select id="slingoDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSlingoSettings()">
                                <option value="rookie">Rookie (5%)</option>
                                <option value="amateur" selected>Amateur (4%)</option>
                                <option value="pro">Professional (3%)</option>
                                <option value="expert">Expert (2.5%)</option>
                                <option value="master">Master (2%)</option>
                                <option value="grandmaster">Grandmaster (1.5%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">⚡ GAME MODE</label>
                            <select id="slingoMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSlingoSettings()">
                                <option value="classic" selected>Classic Slingo</option>
                                <option value="turbo">Turbo Slingo</option>
                                <option value="mega">Mega Slingo</option>
                                <option value="showdown">Showdown 6 Elite</option>
                                <option value="ultimate">Ultimate Showdown</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="slingoWinRate">4%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Required Lines:</div>
                                <div class="text-yellow-400 font-bold" id="requiredLines">4</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Max Balls:</div>
                                <div class="text-blue-400 font-bold" id="maxBalls">18</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Limit:</div>
                                <div class="text-purple-400 font-bold" id="timeLimit">150s</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="slingoPotentialPayout">1275 GA</div>
                        <div class="text-sm text-gray-400">Potential Showdown Payout</div>
                    </div>

                    <button onclick="startSlingoGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        🎯 START SHOWDOWN 6 🎯
                    </button>

                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="markNumber()" id="markBtn"
                                class="cyber-button bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50"
                                disabled>
                            ✓ MARK (Space)
                        </button>
                        <button onclick="skipBall()" id="skipBtn"
                                class="cyber-button bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50"
                                disabled>
                            ⏭️ SKIP (S)
                        </button>
                    </div>
                </div>

                <!-- Enhanced Performance Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚡ SHOWDOWN PERFORMANCE</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Slingo Lines:</span>
                            <span class="text-green-400" id="slingoLines">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Diagonal Lines:</span>
                            <span class="text-cyan-400" id="diagonalLines">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Marks:</span>
                            <span class="text-yellow-400" id="perfectMarks">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Current Streak:</span>
                            <span class="text-pink-400" id="streakCount">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Multiplier Chain:</span>
                            <span class="text-purple-400" id="multiplierChain">1.0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Skill Rating:</span>
                            <span class="text-orange-400" id="skillRating">0</span>
                        </div>
                    </div>
                </div>

                <!-- Showdown 6 Progression -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌟 SHOWDOWN MASTERY</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Showdown Rank:</span>
                            <span class="text-yellow-400" id="showdownRank">Slingo Novice</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="slingoXpBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="slingoXpProgress">0 / 100 XP</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-xs mt-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Win Streak:</span>
                            <span class="text-green-400" id="winStreak">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total Games:</span>
                            <span class="text-blue-400" id="totalGames">0</span>
                        </div>
                    </div>
                </div>

                <!-- Power-ups Display -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🔮 ACTIVE POWER-UPS</h5>
                    <div id="activePowerUps" class="text-sm text-gray-400">
                        No active power-ups
                    </div>
                </div>
            </div>

            <!-- Enhanced Slingo Game Board -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="slingoTitle">Slingo Showdown 6</h5>
                        <p class="text-sm text-gray-400" id="slingoDescription">Master the ultimate Slingo challenge</p>
                    </div>
                    
                    <!-- Game Status -->
                    <div class="bg-black/50 p-3 rounded-lg mb-4">
                        <div class="grid grid-cols-3 gap-4 text-center text-sm">
                            <div>
                                <div class="text-gray-400">Current Ball</div>
                                <div class="text-2xl font-bold text-yellow-400" id="currentBall">-</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Balls Left</div>
                                <div class="text-xl font-bold text-blue-400" id="ballsLeft">18</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Left</div>
                                <div class="text-xl font-bold text-red-400" id="timeLeft">150</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Enhanced Slingo Card -->
                    <div class="relative bg-gradient-to-b from-purple-900/50 to-black/50 rounded-lg p-4 mb-4 border-2 border-purple-500/30">
                        <div class="grid grid-cols-5 gap-1 mb-4" id="slingoCard">
                            <!-- Slingo card will be generated here -->
                        </div>
                        
                        <!-- Pattern Progress -->
                        <div class="grid grid-cols-5 gap-1 text-xs text-center">
                            <div class="text-gray-400">B</div>
                            <div class="text-gray-400">I</div>
                            <div class="text-gray-400">N</div>
                            <div class="text-gray-400">G</div>
                            <div class="text-gray-400">O</div>
                        </div>
                    </div>

                    <!-- Enhanced Ball Display -->
                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <h6 class="text-sm font-bold text-gray-400 mb-2">CURRENT DRAW</h6>
                        <div class="text-center">
                            <div id="drawnBall" class="inline-block w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center text-2xl font-bold text-black mb-2">
                                ?
                            </div>
                            <div id="ballType" class="text-sm text-gray-400">Regular Ball</div>
                        </div>
                    </div>

                    <!-- Game Results -->
                    <div class="text-center">
                        <div id="slingoResult" class="text-xl font-bold mb-2"></div>
                        <div id="slingoWinAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Pattern Guide -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🎯 WINNING PATTERNS</h5>
                    <div class="grid grid-cols-1 gap-2 text-xs">
                        ${WINNING_PATTERNS.slice(0, 6).map(pattern => 
                            `<div class="flex justify-between">
                                <span class="text-gray-400">${pattern.name}:</span>
                                <span class="text-yellow-400">${pattern.multiplier}x</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>

                <!-- Special Balls Guide -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">✨ SPECIAL BALLS</h5>
                    <div class="grid grid-cols-1 gap-1 text-xs">
                        ${SPECIAL_BALLS.slice(0, 4).map(ball => 
                            `<div class="flex items-center gap-2">
                                <span style="color: ${ball.color}">${ball.symbol}</span>
                                <span class="text-gray-400">${ball.description}</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Game Overlay -->
        <div id="slingoOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🎯 SHOWDOWN 6 COMPLETE 🎯</h3>
                <div id="finalSlingoResult" class="text-xl mb-4"></div>
                <div id="finalSlingoStats" class="text-sm text-gray-400 mb-6"></div>
                <div id="patternResults" class="text-xs text-cyan-400 mb-4"></div>
                <button onclick="hideSlingoOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    RETURN TO SHOWDOWN
                </button>
            </div>
        </div>
    `;

    updateSlingoSettings();
    generateSlingoCard();
    
    // Add keyboard listeners
    document.addEventListener('keydown', handleSlingoKeyPress);
}

function updateSlingoSettings() {
    const difficulty = document.getElementById('slingoDifficulty').value;
    const mode = document.getElementById('slingoMode').value;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    const modeData = GAME_MODES.find(m => m.name === mode);
    
    slingoGame.difficulty = difficulty;
    slingoGame.gameMode = mode;
    
    document.getElementById('slingoWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('requiredLines').textContent = diffData.requiredLines;
    document.getElementById('maxBalls').textContent = diffData.ballCount;
    document.getElementById('timeLimit').textContent = diffData.timeLimit + 's';
    
    slingoGame.maxBalls = diffData.ballCount;
    
    updateSlingoPayout();
}

function updateSlingoPayout() {
    const betAmount = parseInt(document.getElementById('slingoBet').value) || 75;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === slingoGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === slingoGame.gameMode);
    
    const totalMultiplier = diffData.multiplier * modeData.paylineMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 17); // Showdown 6 multiplier
    
    document.getElementById('slingoPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function generateSlingoCard() {
    const cardContainer = document.getElementById('slingoCard');
    cardContainer.innerHTML = '';
    
    slingoGame.slingoCard = [];
    slingoGame.markedNumbers = [];
    
    // Generate 5x5 Slingo card
    const ranges = [
        [1, 15],   // B column
        [16, 30],  // I column
        [31, 45],  // N column
        [46, 60],  // G column
        [61, 75]   // O column
    ];
    
    for (let row = 0; row < 5; row++) {
        slingoGame.slingoCard[row] = [];
        for (let col = 0; col < 5; col++) {
            let number;
            
            // Center square is FREE
            if (row === 2 && col === 2) {
                number = 'FREE';
                slingoGame.markedNumbers.push(`${row}-${col}`);
            } else {
                const [min, max] = ranges[col];
                do {
                    number = Math.floor(Math.random() * (max - min + 1)) + min;
                } while (slingoGame.slingoCard.some(r => r && r.includes(number)));
            }
            
            slingoGame.slingoCard[row][col] = number;
            
            const cell = document.createElement('div');
            cell.className = 'bg-black/50 border border-purple-500/30 rounded-lg h-12 flex items-center justify-center text-lg font-bold cursor-pointer transition-all duration-300';
            cell.id = `cell-${row}-${col}`;
            cell.textContent = number;
            
            if (number === 'FREE') {
                cell.classList.add('bg-green-600', 'border-green-400', 'marked');
                cell.style.color = '#ffffff';
            }
            
            cell.addEventListener('click', () => markCell(row, col));
            cardContainer.appendChild(cell);
        }
    }
}

function startSlingoGame() {
    const betAmount = parseInt(document.getElementById('slingoBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance for Showdown 6!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset enhanced game state
    slingoGame.isPlaying = true;
    slingoGame.gamePhase = 'playing';
    slingoGame.betAmount = betAmount;
    slingoGame.calledNumbers = [];
    slingoGame.ballsDrawn = 0;
    slingoGame.slingoLines = 0;
    slingoGame.diagonalLines = 0;
    slingoGame.perfectMarks = 0;
    slingoGame.streakCount = 0;
    slingoGame.multiplierChain = 1.0;
    slingoGame.powerUps = [];
    slingoGame.reactionTimes = [];
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === slingoGame.difficulty);
    slingoGame.timeRemaining = diffData.timeLimit;
    
    document.getElementById('markBtn').disabled = false;
    document.getElementById('skipBtn').disabled = false;
    document.getElementById('ballsLeft').textContent = slingoGame.maxBalls;
    
    hideSlingoOverlay();
    startGameTimer();
    drawNextBall();
    updateSlingoDisplay();
}

function startGameTimer() {
    slingoGame.gameTimer = setInterval(() => {
        slingoGame.timeRemaining--;
        document.getElementById('timeLeft').textContent = slingoGame.timeRemaining;
        
        if (slingoGame.timeRemaining <= 0) {
            endSlingoGame(false, 'Time expired!');
        }
    }, 1000);
}

function drawNextBall() {
    if (!slingoGame.isPlaying || slingoGame.ballsDrawn >= slingoGame.maxBalls) {
        endSlingoGame(false, 'No more balls!');
        return;
    }
    
    // Generate available numbers
    const availableNumbers = [];
    for (let i = 1; i <= 75; i++) {
        if (!slingoGame.calledNumbers.includes(i)) {
            availableNumbers.push(i);
        }
    }
    
    if (availableNumbers.length === 0) {
        endSlingoGame(false, 'All numbers called!');
        return;
    }
    
    // Check for special balls
    const specialBall = getSpecialBall();
    
    if (specialBall) {
        slingoGame.currentNumber = specialBall;
        displaySpecialBall(specialBall);
    } else {
        // Draw regular number
        const randomIndex = Math.floor(Math.random() * availableNumbers.length);
        slingoGame.currentNumber = availableNumbers[randomIndex];
        slingoGame.calledNumbers.push(slingoGame.currentNumber);
        displayRegularBall(slingoGame.currentNumber);
    }
    
    slingoGame.ballsDrawn++;
    slingoGame.ballDrawTime = Date.now();
    
    document.getElementById('ballsLeft').textContent = slingoGame.maxBalls - slingoGame.ballsDrawn;
    updateSlingoDisplay();
}

function getSpecialBall() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === slingoGame.difficulty);
    const rand = Math.random();
    
    if (rand < diffData.wildChance) {
        return SPECIAL_BALLS.find(b => b.type === 'wild');
    } else if (rand < diffData.wildChance + (diffData.jokerChance / 2)) {
        return SPECIAL_BALLS.find(b => b.type === 'super_wild');
    } else if (rand < diffData.wildChance + diffData.jokerChance) {
        return SPECIAL_BALLS.find(b => b.type === 'joker');
    } else if (rand < diffData.wildChance + diffData.jokerChance + 0.01) {
        return SPECIAL_BALLS.find(b => b.type === 'devil');
    } else if (rand < diffData.wildChance + diffData.jokerChance + 0.015) {
        return SPECIAL_BALLS.find(b => b.type === 'bonus');
    }
    
    return null;
}

function displayRegularBall(number) {
    const ballEl = document.getElementById('drawnBall');
    const typeEl = document.getElementById('ballType');
    
    ballEl.textContent = number;
    ballEl.className = 'inline-block w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center text-2xl font-bold text-black mb-2';
    typeEl.textContent = 'Regular Ball';
    
    document.getElementById('currentBall').textContent = number;
}

function displaySpecialBall(specialBall) {
    const ballEl = document.getElementById('drawnBall');
    const typeEl = document.getElementById('ballType');
    
    ballEl.textContent = specialBall.symbol;
    ballEl.className = 'inline-block w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-2';
    ballEl.style.background = `radial-gradient(circle, ${specialBall.color}, ${specialBall.color}80)`;
    ballEl.style.color = '#ffffff';
    ballEl.style.textShadow = `0 0 10px ${specialBall.color}`;
    
    typeEl.textContent = specialBall.description;
    typeEl.style.color = specialBall.color;
    
    document.getElementById('currentBall').textContent = specialBall.symbol;
}

function markNumber() {
    if (!slingoGame.isPlaying || !slingoGame.currentNumber) return;
    
    const markTime = Date.now();
    const reactionTime = markTime - slingoGame.ballDrawTime;
    slingoGame.reactionTimes.push(reactionTime);
    
    if (typeof slingoGame.currentNumber === 'object') {
        // Handle special ball
        handleSpecialBall(slingoGame.currentNumber);
    } else {
        // Handle regular number
        const marked = markRegularNumber(slingoGame.currentNumber);
        if (marked) {
            slingoGame.perfectMarks++;
            slingoGame.streakCount++;
            slingoGame.maxStreak = Math.max(slingoGame.maxStreak, slingoGame.streakCount);
            
            // Fast marking bonus
            if (reactionTime < 1000) {
                slingoGame.multiplierChain += 0.1;
            }
        } else {
            slingoGame.streakCount = 0;
        }
    }
    
    checkWinningPatterns();
    updateSlingoDisplay();
    
    setTimeout(() => {
        drawNextBall();
    }, 1000);
}

function markRegularNumber(number) {
    let marked = false;
    
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            if (slingoGame.slingoCard[row][col] === number) {
                const cellId = `cell-${row}-${col}`;
                const cell = document.getElementById(cellId);
                
                if (!slingoGame.markedNumbers.includes(`${row}-${col}`)) {
                    slingoGame.markedNumbers.push(`${row}-${col}`);
                    cell.classList.add('bg-green-600', 'border-green-400', 'marked');
                    cell.style.color = '#ffffff';
                    marked = true;
                }
            }
        }
    }
    
    return marked;
}

function handleSpecialBall(specialBall) {
    switch (specialBall.type) {
        case 'wild':
            // Player can mark any number in the column of the drawn number
            highlightWildOptions();
            break;
        case 'super_wild':
            // Player can mark any unmarked number
            highlightSuperWildOptions();
            break;
        case 'joker':
            // Mark entire column
            markEntireColumn();
            slingoGame.jokerBalls++;
            break;
        case 'devil':
            // Remove random marked number
            removeRandomMarkedNumber();
            slingoGame.devilBalls++;
            break;
        case 'bonus':
            // Extra ball and multiplier
            slingoGame.bonusSpins++;
            slingoGame.multiplierChain += 0.5;
            break;
    }
}

function highlightWildOptions() {
    // For simplicity, mark the first available number in a random column
    const col = Math.floor(Math.random() * 5);
    for (let row = 0; row < 5; row++) {
        if (!slingoGame.markedNumbers.includes(`${row}-${col}`)) {
            slingoGame.markedNumbers.push(`${row}-${col}`);
            const cell = document.getElementById(`cell-${row}-${col}`);
            cell.classList.add('bg-purple-600', 'border-purple-400', 'marked');
            cell.style.color = '#ffffff';
            slingoGame.wildCards++;
            break;
        }
    }
}

function highlightSuperWildOptions() {
    // Mark any unmarked number
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            if (!slingoGame.markedNumbers.includes(`${row}-${col}`)) {
                slingoGame.markedNumbers.push(`${row}-${col}`);
                const cell = document.getElementById(`cell-${row}-${col}`);
                cell.classList.add('bg-pink-600', 'border-pink-400', 'marked');
                cell.style.color = '#ffffff';
                slingoGame.superWilds++;
                return;
            }
        }
    }
}

function markEntireColumn() {
    const col = Math.floor(Math.random() * 5);
    for (let row = 0; row < 5; row++) {
        if (!slingoGame.markedNumbers.includes(`${row}-${col}`)) {
            slingoGame.markedNumbers.push(`${row}-${col}`);
            const cell = document.getElementById(`cell-${row}-${col}`);
            cell.classList.add('bg-blue-600', 'border-blue-400', 'marked');
            cell.style.color = '#ffffff';
        }
    }
}

function removeRandomMarkedNumber() {
    const markedCells = slingoGame.markedNumbers.filter(pos => pos !== '2-2'); // Don't remove FREE space
    if (markedCells.length > 0) {
        const randomIndex = Math.floor(Math.random() * markedCells.length);
        const removedPos = markedCells[randomIndex];
        
        slingoGame.markedNumbers = slingoGame.markedNumbers.filter(pos => pos !== removedPos);
        
        const cell = document.getElementById(`cell-${removedPos}`);
        cell.classList.remove('bg-green-600', 'border-green-400', 'bg-purple-600', 'border-purple-400', 'bg-pink-600', 'border-pink-400', 'bg-blue-600', 'border-blue-400', 'marked');
        cell.classList.add('bg-black/50', 'border-purple-500/30');
        cell.style.color = '';
    }
}

function skipBall() {
    if (!slingoGame.isPlaying) return;
    
    slingoGame.streakCount = 0;
    drawNextBall();
}

function markCell(row, col) {
    if (!slingoGame.isPlaying) return;
    
    const cellPos = `${row}-${col}`;
    if (slingoGame.markedNumbers.includes(cellPos)) return;
    
    // Only allow marking if it matches current number or is a special ball action
    const cellNumber = slingoGame.slingoCard[row][col];
    if (cellNumber === slingoGame.currentNumber || typeof slingoGame.currentNumber === 'object') {
        markNumber();
    }
}

function checkWinningPatterns() {
    slingoGame.slingoLines = 0;
    slingoGame.diagonalLines = 0;
    
    // Check horizontal lines
    for (let row = 0; row < 5; row++) {
        let lineComplete = true;
        for (let col = 0; col < 5; col++) {
            if (!slingoGame.markedNumbers.includes(`${row}-${col}`)) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) slingoGame.slingoLines++;
    }
    
    // Check diagonal lines
    let diagonal1 = true, diagonal2 = true;
    for (let i = 0; i < 5; i++) {
        if (!slingoGame.markedNumbers.includes(`${i}-${i}`)) diagonal1 = false;
        if (!slingoGame.markedNumbers.includes(`${i}-${4-i}`)) diagonal2 = false;
    }
    if (diagonal1) slingoGame.diagonalLines++;
    if (diagonal2) slingoGame.diagonalLines++;
    
    // Check for full house and blackout
    slingoGame.fullHouse = slingoGame.markedNumbers.length >= 24; // All except center
    slingoGame.blackout = slingoGame.markedNumbers.length >= 25; // All numbers
    
    // Check for four corners
    const corners = ['0-0', '0-4', '4-0', '4-4'];
    slingoGame.cornerBonus = corners.every(corner => slingoGame.markedNumbers.includes(corner));
    
    // Check win condition
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === slingoGame.difficulty);
    if (slingoGame.slingoLines >= diffData.requiredLines || slingoGame.blackout) {
        endSlingoGame(true, 'Showdown 6 Victory!');
    }
}

function updateSlingoDisplay() {
    document.getElementById('slingoLines').textContent = slingoGame.slingoLines;
    document.getElementById('diagonalLines').textContent = slingoGame.diagonalLines;
    document.getElementById('perfectMarks').textContent = slingoGame.perfectMarks;
    document.getElementById('streakCount').textContent = slingoGame.streakCount;
    document.getElementById('multiplierChain').textContent = Math.floor(slingoGame.multiplierChain * 10) / 10 + 'x';
    document.getElementById('skillRating').textContent = slingoGame.skillPoints;
    
    // Update power-ups display
    const powerUpsEl = document.getElementById('activePowerUps');
    if (slingoGame.powerUps.length > 0) {
        powerUpsEl.innerHTML = slingoGame.powerUps.map(p => `<span class="text-cyan-400">${p}</span>`).join('<br>');
    } else {
        powerUpsEl.textContent = 'No active power-ups';
    }
}

function endSlingoGame(won, message) {
    slingoGame.isPlaying = false;
    clearInterval(slingoGame.gameTimer);
    
    document.getElementById('markBtn').disabled = true;
    document.getElementById('skipBtn').disabled = true;
    
    let winnings = 0;
    
    if (won) {
        const diffData = DIFFICULTY_LEVELS.find(d => d.name === slingoGame.difficulty);
        const modeData = GAME_MODES.find(m => m.name === slingoGame.gameMode);
        
        // Calculate enhanced winnings
        let baseMultiplier = diffData.multiplier * modeData.paylineMultiplier;
        
        // Pattern bonuses
        const lineBonus = slingoGame.slingoLines * 0.3;
        const diagonalBonus = slingoGame.diagonalLines * 0.4;
        const perfectBonus = slingoGame.perfectMarks * 0.1;
        const streakBonus = slingoGame.maxStreak * 0.05;
        const speedBonus = slingoGame.reactionTimes.length > 0 ? 
            (slingoGame.reactionTimes.filter(t => t < 1000).length / slingoGame.reactionTimes.length) * 0.5 : 0;
        
        // Special pattern bonuses
        let patternBonus = 0;
        if (slingoGame.blackout) patternBonus += 2.0;
        else if (slingoGame.fullHouse) patternBonus += 1.5;
        if (slingoGame.cornerBonus) patternBonus += 0.8;
        
        const totalMultiplier = baseMultiplier * (1 + lineBonus + diagonalBonus + perfectBonus + streakBonus + speedBonus + patternBonus) * slingoGame.multiplierChain;
        winnings = Math.floor(slingoGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        slingoGame.winStreak++;
        slingoGame.maxWinStreak = Math.max(slingoGame.maxWinStreak, slingoGame.winStreak);
        slingoGame.experiencePoints += 25;
        
        document.getElementById('slingoResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🎯 SHOWDOWN 6 VICTORY! 🎯</span>`;
        document.getElementById('slingoWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        slingoGame.winStreak = 0;
        
        document.getElementById('slingoResult').innerHTML = 
            `<span class="text-red-400">🎯 SHOWDOWN FAILED 🎯</span>`;
        document.getElementById('slingoWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">${message}</div>`;
    }
    
    slingoGame.totalGames++;
    updateSlingoDisplay();
    
    setTimeout(() => {
        document.getElementById('slingoOverlay').classList.remove('hidden');
        document.getElementById('finalSlingoResult').innerHTML = 
            won ? '<span class="text-green-400">🎯 SHOWDOWN 6 MASTERY! 🎯</span>' : 
                  '<span class="text-red-400">💔 SHOWDOWN DEFEAT 💔</span>';
        
        const avgReaction = slingoGame.reactionTimes.length > 0 ? 
            Math.floor(slingoGame.reactionTimes.reduce((a, b) => a + b, 0) / slingoGame.reactionTimes.length) : 0;
        
        document.getElementById('finalSlingoStats').innerHTML = 
            `Lines: ${slingoGame.slingoLines} | Diagonals: ${slingoGame.diagonalLines} | Perfect: ${slingoGame.perfectMarks}<br>
             Max Streak: ${slingoGame.maxStreak} | Avg Reaction: ${avgReaction}ms<br>
             Multiplier Chain: ${Math.floor(slingoGame.multiplierChain * 10) / 10}x`;
        
        const patterns = [];
        if (slingoGame.blackout) patterns.push('BLACKOUT');
        if (slingoGame.fullHouse) patterns.push('FULL HOUSE');
        if (slingoGame.cornerBonus) patterns.push('FOUR CORNERS');
        
        document.getElementById('patternResults').textContent = 
            patterns.length > 0 ? `Special Patterns: ${patterns.join(', ')}` : '';
    }, 3000);
}

function handleSlingoKeyPress(e) {
    if (!slingoGame.isPlaying) return;
    
    if (e.code === 'Space') {
        e.preventDefault();
        markNumber();
    } else if (e.key.toLowerCase() === 's') {
        e.preventDefault();
        skipBall();
    }
}

function hideSlingoOverlay() {
    document.getElementById('slingoOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSlingoGame();
});