// Game state
let balance = 1000;

// Fortune Wheel game state
let fortuneWheelGame = {
    isSpinning: false,
    isActive: false,
    currentSpeed: 0,
    rotation: 0,
    targetRotation: 0,
    segments: [],
    betAmount: 0,
    selectedSegment: null,
    skillLevel: 'novice',
    wheelMomentum: 0,
    stopTiming: 0,
    perfectStops: 0,
    totalSpins: 0,
    winStreak: 0,
    maxStreak: 0,
    skillPoints: 0,
    wheelMastery: 1,
    precisionBonus: 0,
    reactionTime: 0,
    stopWindow: 100, // milliseconds
    animationId: null,
    lastStopAttempt: 0,
    skillZone: { start: 0, end: 30 }, // degrees
    wheelCanvas: null,
    ctx: null
};

// Skill levels with extremely low win rates
const SKILL_LEVELS = [
    { name: 'novice', stopWindow: 80, winRate: 0.02, multiplier: 0.5, description: 'Learning the basics' },
    { name: 'amateur', stopWindow: 70, winRate: 0.03, multiplier: 0.7, description: 'Getting the hang of it' },
    { name: 'skilled', stopWindow: 60, winRate: 0.04, multiplier: 1.0, description: 'Developing precision' },
    { name: 'expert', stopWindow: 50, winRate: 0.05, multiplier: 1.3, description: 'Master of timing' },
    { name: 'master', stopWindow: 40, winRate: 0.06, multiplier: 1.5, description: 'Legendary precision' }
];

// Wheel segments with very low probability prizes
const WHEEL_SEGMENTS = [
    { color: '#ff4444', value: 0, label: 'LOSE', probability: 0.4, type: 'lose' },
    { color: '#ff6666', value: 0, label: 'LOSE', probability: 0.25, type: 'lose' },
    { color: '#ff8888', value: 0, label: 'LOSE', probability: 0.15, type: 'lose' },
    { color: '#ffaa44', value: 0.5, label: '0.5x', probability: 0.08, type: 'small' },
    { color: '#ffcc44', value: 1, label: '1x', probability: 0.06, type: 'break_even' },
    { color: '#44ff44', value: 2, label: '2x', probability: 0.03, type: 'small_win' },
    { color: '#44ffaa', value: 3, label: '3x', probability: 0.015, type: 'medium_win' },
    { color: '#44aaff', value: 5, label: '5x', probability: 0.008, type: 'big_win' },
    { color: '#aa44ff', value: 10, label: '10x', probability: 0.004, type: 'huge_win' },
    { color: '#ff44aa', value: 25, label: 'JACKPOT', probability: 0.001, type: 'jackpot' }
];

function loadFortuneWheelGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Wheel Area -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="relative">
                        <canvas id="fortuneWheelCanvas" width="400" height="400" 
                                class="w-full max-w-md mx-auto border border-yellow-500/30 rounded-full bg-black/50"></canvas>
                        
                        <!-- Wheel Pointer -->
                        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                            <div class="w-0 h-0 border-l-4 border-r-4 border-b-8 border-transparent border-b-yellow-400"></div>
                        </div>
                        
                        <!-- Skill Zone Indicator -->
                        <div id="skillZoneIndicator" class="absolute top-4 left-4 bg-green-500/20 p-2 rounded border border-green-500/50">
                            <div class="text-xs text-green-400">SKILL ZONE</div>
                            <div id="skillZoneStatus" class="text-sm font-bold text-green-300">READY</div>
                        </div>
                        
                        <!-- Speed Indicator -->
                        <div class="absolute top-4 right-4 bg-blue-500/20 p-2 rounded border border-blue-500/50">
                            <div class="text-xs text-blue-400">SPEED</div>
                            <div id="wheelSpeed" class="text-sm font-bold text-blue-300">0 RPM</div>
                        </div>
                    </div>
                    
                    <!-- Controls -->
                    <div class="mt-6 space-y-4">
                        <div class="flex justify-center space-x-4">
                            <button id="spinWheel" class="cyber-button px-8 py-3 rounded-lg font-bold text-white">
                                SPIN WHEEL
                            </button>
                            <button id="stopWheel" class="cyber-button px-8 py-3 rounded-lg font-bold text-white bg-red-600" disabled>
                                SKILL STOP
                            </button>
                        </div>
                        
                        <!-- Timing Display -->
                        <div class="text-center">
                            <div id="timingFeedback" class="text-lg font-semibold text-cyan-400 min-h-6"></div>
                            <div id="reactionDisplay" class="text-sm text-gray-400 min-h-4"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Game Controls -->
            <div class="space-y-6">
                <!-- Bet Controls -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Bet Settings</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Bet Amount</label>
                            <select id="wheelBet" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="10">10 GA</option>
                                <option value="25">25 GA</option>
                                <option value="50">50 GA</option>
                                <option value="100">100 GA</option>
                                <option value="250">250 GA</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Skill Level</label>
                            <select id="skillLevel" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="novice">Novice (2% win rate)</option>
                                <option value="amateur">Amateur (3% win rate)</option>
                                <option value="skilled">Skilled (4% win rate)</option>
                                <option value="expert">Expert (5% win rate)</option>
                                <option value="master">Master (6% win rate)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Skill Stats</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-400" id="perfectStops">0</div>
                            <div class="text-gray-300">Perfect Stops</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-400" id="winStreak">0</div>
                            <div class="text-gray-300">Win Streak</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-400" id="skillPoints">0</div>
                            <div class="text-gray-300">Skill Points</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-400" id="wheelMastery">1</div>
                            <div class="text-gray-300">Mastery Level</div>
                        </div>
                    </div>
                </div>
                
                <!-- Current Result -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Result</h3>
                    <div id="wheelResult" class="text-center text-lg font-semibold text-cyan-400 min-h-12">
                        Place your bet and spin the wheel!
                    </div>
                    <div id="winAmount" class="text-center text-2xl font-bold text-green-400 mt-2 min-h-8"></div>
                </div>
                
                <!-- Instructions -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">How to Play</h3>
                    <div class="text-sm text-gray-300 space-y-2">
                        <p>• Spin the wheel and use skill to stop it</p>
                        <p>• Time your stop to land on winning segments</p>
                        <p>• Green skill zone gives bonus accuracy</p>
                        <p>• Perfect timing builds skill points</p>
                        <p>• Higher skill levels = better precision</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    initializeFortuneWheel();
}

function initializeFortuneWheel() {
    // Get canvas and context
    fortuneWheelGame.wheelCanvas = document.getElementById('fortuneWheelCanvas');
    fortuneWheelGame.ctx = fortuneWheelGame.wheelCanvas.getContext('2d');
    
    // Event listeners
    document.getElementById('spinWheel').addEventListener('click', spinFortuneWheel);
    document.getElementById('stopWheel').addEventListener('click', stopFortuneWheel);
    document.getElementById('skillLevel').addEventListener('change', updateSkillLevel);
    
    // Initialize segments
    generateWheelSegments();
    drawFortuneWheel();
    updateSkillLevel();
    updateWheelDisplay();
}

function generateWheelSegments() {
    fortuneWheelGame.segments = [];
    let currentAngle = 0;
    
    // Create segments based on probability
    WHEEL_SEGMENTS.forEach((segment, index) => {
        const segmentAngle = 360 * segment.probability;
        
        fortuneWheelGame.segments.push({
            ...segment,
            startAngle: currentAngle,
            endAngle: currentAngle + segmentAngle,
            index: index
        });
        
        currentAngle += segmentAngle;
    });
    
    // Randomly position skill zone
    fortuneWheelGame.skillZone.start = Math.random() * 330;
    fortuneWheelGame.skillZone.end = fortuneWheelGame.skillZone.start + 30;
}

function drawFortuneWheel() {
    const canvas = fortuneWheelGame.wheelCanvas;
    const ctx = fortuneWheelGame.ctx;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 180;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw segments
    fortuneWheelGame.segments.forEach(segment => {
        const startAngle = (segment.startAngle + fortuneWheelGame.rotation) * Math.PI / 180;
        const endAngle = (segment.endAngle + fortuneWheelGame.rotation) * Math.PI / 180;
        
        // Draw segment
        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, startAngle, endAngle);
        ctx.closePath();
        ctx.fillStyle = segment.color;
        ctx.fill();
        ctx.strokeStyle = '#000';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Draw label
        const labelAngle = (segment.startAngle + segment.endAngle) / 2 + fortuneWheelGame.rotation;
        const labelX = centerX + Math.cos(labelAngle * Math.PI / 180) * (radius * 0.7);
        const labelY = centerY + Math.sin(labelAngle * Math.PI / 180) * (radius * 0.7);
        
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(segment.label, labelX, labelY);
    });
    
    // Draw skill zone indicator
    const skillStart = (fortuneWheelGame.skillZone.start + fortuneWheelGame.rotation) * Math.PI / 180;
    const skillEnd = (fortuneWheelGame.skillZone.end + fortuneWheelGame.rotation) * Math.PI / 180;
    
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius + 10, skillStart, skillEnd);
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 6;
    ctx.stroke();
    
    // Draw center circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, 20, 0, 2 * Math.PI);
    ctx.fillStyle = '#333';
    ctx.fill();
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.stroke();
}

function spinFortuneWheel() {
    const betAmount = parseInt(document.getElementById('wheelBet').value);
    
    if (betAmount > balance || fortuneWheelGame.isSpinning) {
        if (betAmount > balance) {
            document.getElementById('wheelResult').textContent = 'Insufficient balance!';
        }
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    fortuneWheelGame.betAmount = betAmount;
    updateBalance();
    
    // Start spinning
    fortuneWheelGame.isSpinning = true;
    fortuneWheelGame.isActive = true;
    fortuneWheelGame.currentSpeed = 15 + Math.random() * 10; // Random initial speed
    fortuneWheelGame.totalSpins++;
    
    // Update UI
    document.getElementById('spinWheel').disabled = true;
    document.getElementById('stopWheel').disabled = false;
    document.getElementById('wheelResult').textContent = 'Wheel spinning... time your stop!';
    document.getElementById('winAmount').textContent = '';
    document.getElementById('timingFeedback').textContent = 'Watch for the skill zone!';
    
    // Start animation
    animateWheel();
    
    // Auto-stop after 8 seconds if player doesn't stop
    setTimeout(() => {
        if (fortuneWheelGame.isSpinning) {
            stopFortuneWheel(true); // Auto-stop
        }
    }, 8000);
}

function stopFortuneWheel(autoStop = false) {
    if (!fortuneWheelGame.isSpinning) return;
    
    const stopTime = Date.now();
    fortuneWheelGame.reactionTime = stopTime - fortuneWheelGame.lastStopAttempt;
    fortuneWheelGame.lastStopAttempt = stopTime;
    
    // Calculate stop accuracy
    const currentAngle = fortuneWheelGame.rotation % 360;
    const skillData = SKILL_LEVELS.find(s => s.name === fortuneWheelGame.skillLevel);
    
    // Check if in skill zone
    let inSkillZone = false;
    if (fortuneWheelGame.skillZone.end > 360) {
        inSkillZone = currentAngle >= fortuneWheelGame.skillZone.start || 
                     currentAngle <= (fortuneWheelGame.skillZone.end - 360);
    } else {
        inSkillZone = currentAngle >= fortuneWheelGame.skillZone.start && 
                     currentAngle <= fortuneWheelGame.skillZone.end;
    }
    
    // Calculate timing accuracy
    const speedFactor = Math.max(0, 1 - (fortuneWheelGame.currentSpeed / 20));
    const skillBonus = inSkillZone ? 0.3 : 0;
    const reactionBonus = fortuneWheelGame.reactionTime < skillData.stopWindow ? 0.2 : 0;
    
    const totalAccuracy = speedFactor + skillBonus + reactionBonus;
    const isPerfect = totalAccuracy >= 0.8 && inSkillZone && !autoStop;
    
    // Determine result with extremely low win rate
    const baseWinChance = skillData.winRate;
    const accuracyMultiplier = Math.max(0.1, totalAccuracy);
    const finalWinChance = baseWinChance * accuracyMultiplier;
    
    const won = Math.random() < finalWinChance && !autoStop;
    
    // Stop the wheel
    fortuneWheelGame.isSpinning = false;
    fortuneWheelGame.isActive = false;
    
    if (fortuneWheelGame.animationId) {
        cancelAnimationFrame(fortuneWheelGame.animationId);
    }
    
    // Determine final segment
    let finalSegment;
    if (won) {
        // Select a winning segment based on probability
        const winningSegments = WHEEL_SEGMENTS.filter(s => s.value > 0);
        const totalProb = winningSegments.reduce((sum, s) => sum + s.probability, 0);
        let random = Math.random() * totalProb;
        
        for (const segment of winningSegments) {
            random -= segment.probability;
            if (random <= 0) {
                finalSegment = segment;
                break;
            }
        }
        finalSegment = finalSegment || winningSegments[winningSegments.length - 1];
    } else {
        // Select a losing segment
        const losingSegments = WHEEL_SEGMENTS.filter(s => s.value === 0);
        finalSegment = losingSegments[Math.floor(Math.random() * losingSegments.length)];
    }
    
    // Calculate winnings
    let winnings = 0;
    if (won && finalSegment.value > 0) {
        winnings = Math.floor(fortuneWheelGame.betAmount * finalSegment.value);
        balance += winnings;
        updateBalance();
        
        // Update stats
        fortuneWheelGame.winStreak++;
        if (isPerfect) {
            fortuneWheelGame.perfectStops++;
            fortuneWheelGame.skillPoints += 10;
        } else {
            fortuneWheelGame.skillPoints += 2;
        }
    } else {
        fortuneWheelGame.winStreak = 0;
    }
    
    // Update mastery
    if (fortuneWheelGame.skillPoints >= fortuneWheelGame.wheelMastery * 100) {
        fortuneWheelGame.wheelMastery++;
    }
    
    // Update max streak
    fortuneWheelGame.maxStreak = Math.max(fortuneWheelGame.maxStreak, fortuneWheelGame.winStreak);
    
    // Show result
    if (won) {
        document.getElementById('wheelResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🎯 ${finalSegment.label} WIN! 🎯</span>`;
        document.getElementById('winAmount').textContent = `+${winnings} GA`;
    } else {
        document.getElementById('wheelResult').innerHTML = 
            `<span class="text-red-400">💔 ${finalSegment.label} - Better luck next time! 💔</span>`;
        document.getElementById('winAmount').textContent = '';
    }
    
    // Show timing feedback
    if (isPerfect) {
        document.getElementById('timingFeedback').innerHTML = 
            '<span class="text-green-400 animate-pulse">PERFECT STOP!</span>';
    } else if (inSkillZone) {
        document.getElementById('timingFeedback').innerHTML = 
            '<span class="text-yellow-400">Good timing!</span>';
    } else if (autoStop) {
        document.getElementById('timingFeedback').innerHTML = 
            '<span class="text-red-400">Too slow - auto stopped!</span>';
    } else {
        document.getElementById('timingFeedback').innerHTML = 
            '<span class="text-gray-400">Missed the skill zone</span>';
    }
    
    document.getElementById('reactionDisplay').textContent = 
        `Reaction: ${fortuneWheelGame.reactionTime}ms`;
    
    updateWheelDisplay();
    
    // Reset controls
    setTimeout(() => {
        document.getElementById('spinWheel').disabled = false;
        document.getElementById('stopWheel').disabled = true;
        document.getElementById('wheelResult').textContent = 'Place your bet and spin the wheel!';
        document.getElementById('winAmount').textContent = '';
        document.getElementById('timingFeedback').textContent = '';
        document.getElementById('reactionDisplay').textContent = '';
        
        // Regenerate skill zone position
        generateWheelSegments();
        drawFortuneWheel();
    }, 5000);
}

function animateWheel() {
    if (!fortuneWheelGame.isSpinning) return;
    
    // Update rotation
    fortuneWheelGame.rotation += fortuneWheelGame.currentSpeed;
    
    // Gradually slow down
    fortuneWheelGame.currentSpeed *= 0.995;
    
    // Update speed display
    const rpm = Math.floor(fortuneWheelGame.currentSpeed * 4);
    document.getElementById('wheelSpeed').textContent = rpm + ' RPM';
    
    // Update skill zone status
    const currentAngle = fortuneWheelGame.rotation % 360;
    let inSkillZone = false;
    if (fortuneWheelGame.skillZone.end > 360) {
        inSkillZone = currentAngle >= fortuneWheelGame.skillZone.start || 
                     currentAngle <= (fortuneWheelGame.skillZone.end - 360);
    } else {
        inSkillZone = currentAngle >= fortuneWheelGame.skillZone.start && 
                     currentAngle <= fortuneWheelGame.skillZone.end;
    }
    
    document.getElementById('skillZoneStatus').textContent = inSkillZone ? 'ACTIVE!' : 'READY';
    document.getElementById('skillZoneStatus').className = inSkillZone ? 
        'text-sm font-bold text-green-300 animate-pulse' : 'text-sm font-bold text-green-300';
    
    // Redraw wheel
    drawFortuneWheel();
    
    fortuneWheelGame.animationId = requestAnimationFrame(animateWheel);
}

function updateSkillLevel() {
    const skillLevel = document.getElementById('skillLevel').value;
    fortuneWheelGame.skillLevel = skillLevel;
    
    const skillData = SKILL_LEVELS.find(s => s.name === skillLevel);
    fortuneWheelGame.stopWindow = skillData.stopWindow;
}

function updateWheelDisplay() {
    document.getElementById('perfectStops').textContent = fortuneWheelGame.perfectStops;
    document.getElementById('winStreak').textContent = fortuneWheelGame.winStreak;
    document.getElementById('skillPoints').textContent = fortuneWheelGame.skillPoints;
    document.getElementById('wheelMastery').textContent = fortuneWheelGame.wheelMastery;
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadFortuneWheelGame();
});