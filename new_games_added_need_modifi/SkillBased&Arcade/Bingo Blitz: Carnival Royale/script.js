// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Bingo Blitz: Carnival Royale Game Implementation ---

// Enhanced game constants for Carnival Royale
const ROYALE_PATTERNS = [
    { name: 'single', description: 'Single Line', difficulty: 1, multiplier: 3, royalBonus: 1.0 },
    { name: 'double', description: 'Double Lines', difficulty: 2, multiplier: 6, royalBonus: 1.2 },
    { name: 'corners', description: 'Royal Corners', difficulty: 3, multiplier: 8, royalBonus: 1.5 },
    { name: 'crown', description: 'Crown Pattern', difficulty: 4, multiplier: 12, royalBonus: 2.0 },
    { name: 'throne', description: 'Royal Throne', difficulty: 5, multiplier: 18, royalBonus: 2.5 },
    { name: 'palace', description: 'Royal Palace', difficulty: 6, multiplier: 25, royalBonus: 3.0 }
];

const ROYALE_MODES = [
    { name: 'court', speed: 3500, description: 'Royal Court', bonus: 1.0, difficulty: 1 },
    { name: 'noble', speed: 2800, description: 'Noble Speed', bonus: 1.3, difficulty: 2 },
    { name: 'royal', speed: 2200, description: 'Royal Pace', bonus: 1.6, difficulty: 3 },
    { name: 'imperial', speed: 1600, description: 'Imperial Rush', bonus: 2.0, difficulty: 4 },
    { name: 'legendary', speed: 1000, description: 'Legendary Speed', bonus: 2.5, difficulty: 5 },
    { name: 'mythical', speed: 600, description: 'Mythical Velocity', bonus: 3.5, difficulty: 6 }
];

const ROYAL_BALLS = [
    { name: 'silver', chance: 0.08, multiplier: 3, description: 'Silver Ball - 3x Royal Multiplier' },
    { name: 'gold', chance: 0.05, multiplier: 8, description: 'Gold Ball - 8x Royal Multiplier' },
    { name: 'platinum', chance: 0.03, multiplier: 15, description: 'Platinum Ball - 15x Royal Multiplier' },
    { name: 'diamond', chance: 0.02, multiplier: 25, description: 'Diamond Ball - 25x Royal Multiplier' },
    { name: 'crown', chance: 0.01, multiplier: 50, description: 'Crown Ball - 50x ROYAL JACKPOT!' },
    { name: 'legendary', chance: 0.005, multiplier: 100, description: 'Legendary Ball - 100x ULTIMATE ROYALE!' }
];

const ROYAL_CHALLENGES = [
    { name: 'speed', description: 'Lightning Reflexes', requirement: 'avg_reaction < 0.8', bonus: 5 },
    { name: 'precision', description: 'Perfect Accuracy', requirement: 'miss_ratio < 0.05', bonus: 8 },
    { name: 'endurance', description: 'Royal Stamina', requirement: 'numbers_called > 50', bonus: 3 },
    { name: 'mastery', description: 'Pattern Master', requirement: 'perfect_marks > 15', bonus: 10 },
    { name: 'royalty', description: 'True Royalty', requirement: 'all_challenges', bonus: 25 }
];

let bingoRoyaleGame = {
    isPlaying: false,
    gamePhase: 'waiting', // waiting, playing, finished
    betAmount: 0,
    selectedPattern: 'single',
    gameMode: 'court',
    
    // Enhanced game state
    bingoCard: [],
    calledNumbers: [],
    markedNumbers: new Set(),
    currentNumber: null,
    numbersLeft: 75,
    gameTimer: null,
    callSpeed: 3500,
    
    // Royal features
    royalBalls: 0,
    crownBonus: 0,
    royaleLevel: 1,
    consecutiveWins: 0,
    totalGames: 0,
    perfectGames: 0,
    maxStreak: 0,
    
    // Royal atmosphere
    royalFanfare: false,
    courtLevel: 1,
    nobleRank: 'Peasant',
    royalPower: 0,
    
    // Advanced performance tracking
    reactionTimes: [],
    averageReaction: 0,
    missedCalls: 0,
    perfectMarks: 0,
    challengesCompleted: new Set(),
    royalAchievements: 0,
    
    // Multi-card system
    cardCount: 1,
    activeCards: [],
    
    // Progressive features
    royalMultiplier: 1.0,
    crownJewels: 0,
    thronePoints: 0
};

function loadBingoRoyaleGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-7xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-4xl font-bold text-purple-400 mb-2">👑 BINGO BLITZ: CARNIVAL ROYALE 👑</h3>
                <p class="text-blue-300">The ultimate royal bingo experience with multiple cards and legendary rewards!</p>
            </div>

            <!-- Game Controls -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <!-- Bet Controls -->
                <div class="game-card p-4">
                    <h4 class="text-lg font-semibold text-purple-400 mb-3">👑 Royal Setup</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Royal Bet:</label>
                            <select id="royaleBet" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="25">25 GA</option>
                                <option value="50">50 GA</option>
                                <option value="100">100 GA</option>
                                <option value="250">250 GA</option>
                                <option value="500">500 GA</option>
                                <option value="1000">1000 GA</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Royal Pattern:</label>
                            <select id="royalePattern" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                ${ROYALE_PATTERNS.map(p => 
                                    `<option value="${p.name}">${p.description} (${p.multiplier}x)</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Royal Mode:</label>
                            <select id="royaleMode" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                ${ROYALE_MODES.map(m => 
                                    `<option value="${m.name}">${m.description} (${m.bonus}x)</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Cards:</label>
                            <select id="cardCount" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="1">1 Card (Standard)</option>
                                <option value="2">2 Cards (+50% bet)</option>
                                <option value="3">3 Cards (+100% bet)</option>
                                <option value="4">4 Cards (+200% bet)</option>
                            </select>
                        </div>
                        <button id="startRoyale" class="w-full cyber-button px-4 py-2 rounded font-semibold">
                            START ROYAL BINGO
                        </button>
                    </div>
                </div>

                <!-- Royal Stats -->
                <div class="game-card p-4">
                    <h4 class="text-lg font-semibold text-purple-400 mb-3">👑 Royal Stats</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Royale Level:</span>
                            <span id="royaleLevel" class="text-purple-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Noble Rank:</span>
                            <span id="nobleRank" class="text-gold-400">Peasant</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Crown Jewels:</span>
                            <span id="crownJewels" class="text-diamond">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Throne Points:</span>
                            <span id="thronePoints" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Royal Streak:</span>
                            <span id="royalStreak" class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Perfect Games:</span>
                            <span id="royalPerfectGames" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Achievements:</span>
                            <span id="royalAchievements" class="text-pink-400">0</span>
                        </div>
                    </div>
                </div>

                <!-- Royal Features -->
                <div class="game-card p-4">
                    <h4 class="text-lg font-semibold text-purple-400 mb-3">💎 Royal Features</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Royal Balls:</span>
                            <span id="royalBalls" class="text-gold-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Crown Bonus:</span>
                            <span id="crownBonus" class="text-rainbow">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Royal Multiplier:</span>
                            <span id="royalMultiplier" class="text-cyan-400">1.0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Perfect Marks:</span>
                            <span id="royalPerfectMarks" class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Missed Calls:</span>
                            <span id="royalMissedCalls" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Avg Reaction:</span>
                            <span id="royalAvgReaction" class="text-orange-400">0.0s</span>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="text-xs text-gray-400 mb-1">Royal Power:</div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div id="royalPowerBar" class="bg-gradient-to-r from-purple-500 via-gold-500 to-diamond h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div id="royalPowerText" class="text-xs text-center mt-1 text-purple-400">Royal Court Assembling...</div>
                    </div>
                </div>

                <!-- Royal Challenges -->
                <div class="game-card p-4">
                    <h4 class="text-lg font-semibold text-purple-400 mb-3">🏆 Royal Challenges</h4>
                    <div class="space-y-1 text-xs">
                        ${ROYAL_CHALLENGES.map(c => 
                            `<div id="challenge-${c.name}" class="flex justify-between p-1 rounded ${c.name === 'royalty' ? 'bg-gold-500/20' : 'bg-gray-700/30'}">
                                <span>${c.description}</span>
                                <span class="challenge-status text-gray-400">⏳</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>

            <!-- Game Area -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Bingo Cards -->
                <div class="lg:col-span-2">
                    <div class="game-card p-6">
                        <h4 class="text-xl font-semibold text-purple-400 mb-4 text-center">👑 Royal Bingo Cards 👑</h4>
                        <div id="royalCardsContainer" class="grid gap-4">
                            <!-- Bingo cards will be generated here -->
                        </div>
                        <div class="text-center mt-4">
                            <div id="royaleStatus" class="text-lg font-semibold text-blue-300 mb-2">Ready for royal bingo!</div>
                            <div id="royaleResult" class="text-xl font-bold"></div>
                        </div>
                    </div>
                </div>

                <!-- Royal Caller -->
                <div class="game-card p-6">
                    <h4 class="text-xl font-semibold text-purple-400 mb-4 text-center">🎯 Royal Caller 🎯</h4>
                    
                    <!-- Current Number Display -->
                    <div class="text-center mb-6">
                        <div id="royalNumberDisplay" class="text-6xl font-bold text-gold-400 mb-2 royal-glow">--</div>
                        <div id="royalNumberLetter" class="text-2xl text-purple-400 font-semibold">ROYALE</div>
                        <div id="royalBallIndicator" class="text-lg mt-2"></div>
                    </div>

                    <!-- Game Progress -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-300 mb-1">
                            <span>Royal Numbers:</span>
                            <span id="royalNumbersCount">0/75</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-3">
                            <div id="royalProgressBar" class="bg-gradient-to-r from-purple-500 via-gold-500 to-diamond h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- Royal Numbers History -->
                    <div class="mb-4">
                        <div class="text-sm text-gray-300 mb-2">Royal History:</div>
                        <div id="royalNumbersHistory" class="flex flex-wrap gap-1 min-h-[60px] p-2 bg-black/30 rounded border border-purple-500/20">
                            <!-- Called numbers will appear here -->
                        </div>
                    </div>

                    <!-- Performance Meters -->
                    <div class="space-y-3">
                        <div>
                            <div class="flex justify-between text-xs text-gray-300 mb-1">
                                <span>Reaction Speed:</span>
                                <span id="royalReactionTime">0.00s</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div id="reactionSpeedBar" class="bg-gradient-to-r from-red-500 to-green-500 h-2 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                        </div>
                        
                        <div>
                            <div class="flex justify-between text-xs text-gray-300 mb-1">
                                <span>Accuracy:</span>
                                <span id="royalAccuracy">100%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div id="accuracyBar" class="bg-gradient-to-r from-red-500 to-green-500 h-2 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Royal Guide -->
            <div class="game-card p-4 mt-6">
                <h4 class="text-lg font-semibold text-purple-400 mb-3">👑 Royal Guide & Legendary Features</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <div class="text-purple-300 font-semibold mb-2">Royal Patterns:</div>
                        <div class="space-y-1">
                            ${ROYALE_PATTERNS.map(p => 
                                `<div>👑 <span class="text-gold-400">${p.description}:</span> ${p.multiplier}x (${p.royalBonus}x royal)</div>`
                            ).join('')}
                        </div>
                    </div>
                    <div>
                        <div class="text-purple-300 font-semibold mb-2">Legendary Balls:</div>
                        <div class="space-y-1">
                            ${ROYAL_BALLS.slice(0, 3).map(b => 
                                `<div>💎 <span class="text-diamond">${b.description}</span></div>`
                            ).join('')}
                        </div>
                    </div>
                    <div>
                        <div class="text-purple-300 font-semibold mb-2">Noble Ranks:</div>
                        <div class="space-y-1">
                            <div>🏰 <span class="text-gray-400">Peasant:</span> Starting rank</div>
                            <div>⚔️ <span class="text-blue-400">Knight:</span> 5+ wins</div>
                            <div>👑 <span class="text-purple-400">Noble:</span> 15+ wins</div>
                            <div>💎 <span class="text-gold-400">Royalty:</span> 50+ wins</div>
                            <div>🌟 <span class="text-rainbow">Legend:</span> 100+ wins</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupBingoRoyaleGame();
}

function setupBingoRoyaleGame() {
    document.getElementById('startRoyale').addEventListener('click', startRoyaleGame);
    document.getElementById('cardCount').addEventListener('change', updateCardDisplay);
    updateRoyaleDisplay();
    generateRoyaleCards();
}

function generateRoyaleCards() {
    const cardCount = parseInt(document.getElementById('cardCount').value);
    const container = document.getElementById('royalCardsContainer');
    container.innerHTML = '';
    
    bingoRoyaleGame.activeCards = [];
    
    for (let cardIndex = 0; cardIndex < cardCount; cardIndex++) {
        const cardDiv = document.createElement('div');
        cardDiv.className = 'border-2 border-purple-500/30 rounded-lg p-4 bg-black/20';
        cardDiv.innerHTML = `
            <div class="text-center mb-2">
                <span class="text-purple-400 font-semibold">Royal Card ${cardIndex + 1}</span>
            </div>
            <div id="royalCard${cardIndex}" class="grid grid-cols-5 gap-1">
                <!-- Card content will be generated -->
            </div>
        `;
        container.appendChild(cardDiv);
        
        generateSingleRoyaleCard(cardIndex);
    }
    
    // Adjust grid layout based on card count
    if (cardCount === 1) {
        container.className = 'grid gap-4';
    } else if (cardCount === 2) {
        container.className = 'grid grid-cols-1 lg:grid-cols-2 gap-4';
    } else {
        container.className = 'grid grid-cols-1 md:grid-cols-2 gap-4';
    }
}

function generateSingleRoyaleCard(cardIndex) {
    const cardContainer = document.getElementById(`royalCard${cardIndex}`);
    cardContainer.innerHTML = '';
    
    // Generate BINGO header
    const headers = ['B', 'I', 'N', 'G', 'O'];
    headers.forEach(letter => {
        const headerCell = document.createElement('div');
        headerCell.className = 'bg-purple-600 text-white font-bold text-lg h-8 flex items-center justify-center rounded border-2 border-purple-400';
        headerCell.textContent = letter;
        cardContainer.appendChild(headerCell);
    });
    
    // Generate card numbers
    const cardNumbers = [];
    const ranges = [
        [1, 15],   // B column
        [16, 30],  // I column
        [31, 45],  // N column
        [46, 60],  // G column
        [61, 75]   // O column
    ];
    
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            const cell = document.createElement('div');
            cell.className = 'bg-black/50 border-2 border-purple-500/30 rounded h-8 flex items-center justify-center text-sm font-semibold cursor-pointer transition-all duration-200 hover:border-purple-400';
            
            if (row === 2 && col === 2) {
                // Free space in center
                cell.textContent = 'FREE';
                cell.className += ' bg-green-600/50 border-green-400';
                cardNumbers.push('FREE');
            } else {
                const [min, max] = ranges[col];
                let number;
                do {
                    number = Math.floor(Math.random() * (max - min + 1)) + min;
                } while (cardNumbers.includes(number));
                
                cell.textContent = number;
                cell.dataset.number = number;
                cell.dataset.card = cardIndex;
                cardNumbers.push(number);
                
                cell.addEventListener('click', () => markRoyaleNumber(number, cardIndex, cell));
            }
            
            cardContainer.appendChild(cell);
        }
    }
    
    bingoRoyaleGame.activeCards[cardIndex] = {
        numbers: cardNumbers,
        marked: new Set(['FREE'])
    };
}

function updateCardDisplay() {
    generateRoyaleCards();
}

function startRoyaleGame() {
    if (bingoRoyaleGame.isPlaying) return;
    
    bingoRoyaleGame.betAmount = parseInt(document.getElementById('royaleBet').value);
    bingoRoyaleGame.selectedPattern = document.getElementById('royalePattern').value;
    bingoRoyaleGame.gameMode = document.getElementById('royaleMode').value;
    bingoRoyaleGame.cardCount = parseInt(document.getElementById('cardCount').value);
    
    // Calculate total bet with card multiplier
    const cardMultipliers = { 1: 1, 2: 1.5, 3: 2, 4: 3 };
    const totalBet = Math.floor(bingoRoyaleGame.betAmount * cardMultipliers[bingoRoyaleGame.cardCount]);
    
    if (balance < totalBet) {
        document.getElementById('royaleStatus').textContent = 'Insufficient royal treasury!';
        return;
    }
    
    balance -= totalBet;
    updateBalance();
    
    bingoRoyaleGame.isPlaying = true;
    bingoRoyaleGame.gamePhase = 'playing';
    bingoRoyaleGame.totalGames++;
    bingoRoyaleGame.calledNumbers = [];
    bingoRoyaleGame.currentNumber = null;
    bingoRoyaleGame.numbersLeft = 75;
    bingoRoyaleGame.reactionTimes = [];
    bingoRoyaleGame.missedCalls = 0;
    bingoRoyaleGame.perfectMarks = 0;
    bingoRoyaleGame.royalFanfare = true;
    
    // Reset all card markings
    bingoRoyaleGame.activeCards.forEach((card, index) => {
        card.marked = new Set(['FREE']);
        document.querySelectorAll(`#royalCard${index} div[data-number]`).forEach(cell => {
            cell.classList.remove('bg-gold-500', 'border-gold-400', 'marked', 'perfect-mark');
        });
    });
    
    // Set call speed based on mode
    const modeData = ROYALE_MODES.find(m => m.name === bingoRoyaleGame.gameMode);
    bingoRoyaleGame.callSpeed = modeData.speed;
    
    document.getElementById('startRoyale').disabled = true;
    document.getElementById('royaleStatus').textContent = 'Royal bingo commencing! Prepare for glory!';
    
    // Start calling numbers
    setTimeout(() => {
        callNextRoyaleNumber();
    }, 2000);
    
    updateRoyaleDisplay();
}

function callNextRoyaleNumber() {
    if (!bingoRoyaleGame.isPlaying || bingoRoyaleGame.numbersLeft === 0) return;
    
    // Generate available numbers
    const availableNumbers = [];
    for (let i = 1; i <= 75; i++) {
        if (!bingoRoyaleGame.calledNumbers.includes(i)) {
            availableNumbers.push(i);
        }
    }
    
    if (availableNumbers.length === 0) {
        endRoyaleGame(false, 'All royal numbers called - no champion!');
        return;
    }
    
    // Select random number
    const randomIndex = Math.floor(Math.random() * availableNumbers.length);
    const calledNumber = availableNumbers[randomIndex];
    
    // Check for royal ball
    let isRoyalBall = false;
    let royalMultiplier = 1;
    let royalType = '';
    
    for (const royal of ROYAL_BALLS) {
        if (Math.random() < royal.chance) {
            isRoyalBall = true;
            royalMultiplier = royal.multiplier;
            royalType = royal.name;
            bingoRoyaleGame.royalBalls++;
            break;
        }
    }
    
    bingoRoyaleGame.calledNumbers.push(calledNumber);
    bingoRoyaleGame.currentNumber = calledNumber;
    bingoRoyaleGame.numbersLeft--;
    
    // Update display
    document.getElementById('royalNumberDisplay').textContent = calledNumber;
    
    // Determine letter
    let letter = '';
    if (calledNumber <= 15) letter = 'B';
    else if (calledNumber <= 30) letter = 'I';
    else if (calledNumber <= 45) letter = 'N';
    else if (calledNumber <= 60) letter = 'G';
    else letter = 'O';
    
    document.getElementById('royalNumberLetter').textContent = letter + '-' + calledNumber;
    
    // Royal ball indicator
    if (isRoyalBall) {
        document.getElementById('royalBallIndicator').innerHTML = 
            `<span class="text-diamond royal-glow">💎 ${royalType.toUpperCase()} ROYAL BALL! ${royalMultiplier}x 💎</span>`;
        bingoRoyaleGame.crownBonus += royalMultiplier * 3;
        bingoRoyaleGame.royalMultiplier += royalMultiplier * 0.1;
    } else {
        document.getElementById('royalBallIndicator').textContent = '';
    }
    
    // Add to history
    const historyContainer = document.getElementById('royalNumbersHistory');
    const numberSpan = document.createElement('span');
    numberSpan.className = `px-2 py-1 rounded text-xs ${isRoyalBall ? 'bg-diamond text-black' : 'bg-purple-500 text-white'}`;
    numberSpan.textContent = letter + calledNumber;
    historyContainer.appendChild(numberSpan);
    
    // Keep only last 15 numbers visible
    while (historyContainer.children.length > 15) {
        historyContainer.removeChild(historyContainer.firstChild);
    }
    
    // Update progress
    const progress = ((75 - bingoRoyaleGame.numbersLeft) / 75) * 100;
    document.getElementById('royalProgressBar').style.width = progress + '%';
    document.getElementById('royalNumbersCount').textContent = `${75 - bingoRoyaleGame.numbersLeft}/75`;
    
    // Start reaction timer for this number
    bingoRoyaleGame.numberCallTime = Date.now();
    
    // Check if any card has this number
    let hasNumber = false;
    bingoRoyaleGame.activeCards.forEach(card => {
        if (card.numbers.includes(calledNumber)) {
            hasNumber = true;
        }
    });
    
    if (hasNumber) {
        document.getElementById('royaleStatus').innerHTML = 
            `<span class="text-gold-400 royal-glow">👑 ${letter}-${calledNumber} called! Mark it swiftly, noble one! 👑</span>`;
    } else {
        document.getElementById('royaleStatus').textContent = `${letter}-${calledNumber} called!`;
    }
    
    updateRoyaleDisplay();
    
    // Schedule next number
    bingoRoyaleGame.gameTimer = setTimeout(() => {
        // Check for missed numbers across all cards
        bingoRoyaleGame.activeCards.forEach(card => {
            if (card.numbers.includes(calledNumber) && !card.marked.has(calledNumber)) {
                bingoRoyaleGame.missedCalls++;
                bingoRoyaleGame.royalPower = Math.max(0, bingoRoyaleGame.royalPower - 5);
            }
        });
        
        callNextRoyaleNumber();
    }, bingoRoyaleGame.callSpeed);
}

function markRoyaleNumber(number, cardIndex, cell) {
    if (!bingoRoyaleGame.isPlaying || bingoRoyaleGame.activeCards[cardIndex].marked.has(number)) return;
    
    // Check if this number was called
    if (!bingoRoyaleGame.calledNumbers.includes(number)) {
        // Wrong number marked - severe penalty
        document.getElementById('royaleStatus').innerHTML = 
            `<span class="text-red-400">❌ ${number} not called yet! Royal penalty! ❌</span>`;
        bingoRoyaleGame.royalPower = Math.max(0, bingoRoyaleGame.royalPower - 20);
        return;
    }
    
    // Calculate reaction time
    const reactionTime = (Date.now() - bingoRoyaleGame.numberCallTime) / 1000;
    bingoRoyaleGame.reactionTimes.push(reactionTime);
    
    // Update reaction display
    document.getElementById('royalReactionTime').textContent = reactionTime.toFixed(2) + 's';
    
    // Update reaction speed bar (green = fast, red = slow)
    const speedPercent = Math.max(0, Math.min(100, (2 - reactionTime) * 50));
    document.getElementById('reactionSpeedBar').style.width = speedPercent + '%';
    
    // Mark as perfect if under 0.8 seconds
    if (reactionTime < 0.8) {
        bingoRoyaleGame.perfectMarks++;
        bingoRoyaleGame.royalPower = Math.min(100, bingoRoyaleGame.royalPower + 3);
        bingoRoyaleGame.thronePoints += 5;
        cell.classList.add('perfect-mark');
    }
    
    // Mark the number
    bingoRoyaleGame.activeCards[cardIndex].marked.add(number);
    cell.classList.add('bg-gold-500', 'border-gold-400', 'marked');
    cell.style.transform = 'scale(1.1)';
    setTimeout(() => cell.style.transform = 'scale(1)', 200);
    
    // Check for winning pattern on this card
    if (checkRoyaleWinningPattern(cardIndex)) {
        endRoyaleGame(true, `ROYAL BINGO! Card ${cardIndex + 1} achieves victory!`);
        return;
    }
    
    updateRoyaleDisplay();
    updateRoyaleChallenges();
}

function checkRoyaleWinningPattern(cardIndex) {
    const pattern = ROYALE_PATTERNS.find(p => p.name === bingoRoyaleGame.selectedPattern);
    const card = bingoRoyaleGame.activeCards[cardIndex];
    const numbers = card.numbers;
    const marked = card.marked;
    
    switch (pattern.name) {
        case 'single':
            return checkRoyaleLines(numbers, marked);
        case 'double':
            return checkRoyaleDoubleLines(numbers, marked);
        case 'corners':
            return checkRoyaleCorners(numbers, marked);
        case 'crown':
            return checkRoyaleCrown(numbers, marked);
        case 'throne':
            return checkRoyaleThrone(numbers, marked);
        case 'palace':
            return checkRoyalePalace(numbers, marked);
        default:
            return false;
    }
}

function checkRoyaleLines(numbers, marked) {
    // Check rows, columns, and diagonals (same as basic bingo)
    // Check rows
    for (let row = 0; row < 5; row++) {
        let lineComplete = true;
        for (let col = 0; col < 5; col++) {
            const index = row * 5 + col;
            if (!marked.has(numbers[index])) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) return true;
    }
    
    // Check columns
    for (let col = 0; col < 5; col++) {
        let lineComplete = true;
        for (let row = 0; row < 5; row++) {
            const index = row * 5 + col;
            if (!marked.has(numbers[index])) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) return true;
    }
    
    // Check diagonals
    let diag1 = true, diag2 = true;
    for (let i = 0; i < 5; i++) {
        if (!marked.has(numbers[i * 5 + i])) diag1 = false;
        if (!marked.has(numbers[i * 5 + (4 - i)])) diag2 = false;
    }
    
    return diag1 || diag2;
}

function checkRoyaleDoubleLines(numbers, marked) {
    let completedLines = 0;
    
    // Check all possible lines and count completed ones
    // Rows
    for (let row = 0; row < 5; row++) {
        let lineComplete = true;
        for (let col = 0; col < 5; col++) {
            const index = row * 5 + col;
            if (!marked.has(numbers[index])) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) completedLines++;
    }
    
    // Columns
    for (let col = 0; col < 5; col++) {
        let lineComplete = true;
        for (let row = 0; row < 5; row++) {
            const index = row * 5 + col;
            if (!marked.has(numbers[index])) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) completedLines++;
    }
    
    // Diagonals
    let diag1 = true, diag2 = true;
    for (let i = 0; i < 5; i++) {
        if (!marked.has(numbers[i * 5 + i])) diag1 = false;
        if (!marked.has(numbers[i * 5 + (4 - i)])) diag2 = false;
    }
    if (diag1) completedLines++;
    if (diag2) completedLines++;
    
    return completedLines >= 2;
}

function checkRoyaleCorners(numbers, marked) {
    const corners = [numbers[0], numbers[4], numbers[20], numbers[24]];
    return corners.every(num => marked.has(num));
}

function checkRoyaleCrown(numbers, marked) {
    // Crown pattern: top row + center column
    const crownPositions = [0, 1, 2, 3, 4, 7, 12, 17, 22];
    return crownPositions.every(pos => marked.has(numbers[pos]));
}

function checkRoyaleThrone(numbers, marked) {
    // Throne pattern: frame + center cross
    const thronePositions = [
        0, 1, 2, 3, 4,     // Top row
        5, 9,              // Left and right of second row
        10, 12, 14,        // Left, center, right of third row
        15, 19,            // Left and right of fourth row
        20, 21, 22, 23, 24 // Bottom row
    ];
    return thronePositions.every(pos => marked.has(numbers[pos]));
}

function checkRoyalePalace(numbers, marked) {
    // Palace pattern: full house (all numbers)
    return numbers.every(num => marked.has(num));
}

function updateRoyaleChallenges() {
    const avgReaction = bingoRoyaleGame.reactionTimes.length > 0 ? 
        bingoRoyaleGame.reactionTimes.reduce((a, b) => a + b, 0) / bingoRoyaleGame.reactionTimes.length : 0;
    const missRatio = bingoRoyaleGame.missedCalls / Math.max(1, bingoRoyaleGame.calledNumbers.length);
    const numbersCalledCount = bingoRoyaleGame.calledNumbers.length;
    
    // Update individual challenges
    updateChallenge('speed', avgReaction < 0.8);
    updateChallenge('precision', missRatio < 0.05);
    updateChallenge('endurance', numbersCalledCount > 50);
    updateChallenge('mastery', bingoRoyaleGame.perfectMarks > 15);
    
    // Check if all challenges completed
    const allCompleted = bingoRoyaleGame.challengesCompleted.size >= 4;
    updateChallenge('royalty', allCompleted);
}

function updateChallenge(challengeName, completed) {
    const challengeElement = document.getElementById(`challenge-${challengeName}`);
    const statusElement = challengeElement.querySelector('.challenge-status');
    
    if (completed && !bingoRoyaleGame.challengesCompleted.has(challengeName)) {
        bingoRoyaleGame.challengesCompleted.add(challengeName);
        bingoRoyaleGame.royalAchievements++;
        statusElement.textContent = '✅';
        statusElement.className = 'challenge-status text-green-400';
        challengeElement.classList.add('bg-green-500/20');
        
        // Award bonus points
        const challenge = ROYAL_CHALLENGES.find(c => c.name === challengeName);
        bingoRoyaleGame.thronePoints += challenge.bonus;
        bingoRoyaleGame.royalPower = Math.min(100, bingoRoyaleGame.royalPower + challenge.bonus);
    } else if (!completed) {
        statusElement.textContent = '⏳';
        statusElement.className = 'challenge-status text-gray-400';
    }
}

function endRoyaleGame(won, message) {
    bingoRoyaleGame.isPlaying = false;
    bingoRoyaleGame.gamePhase = 'finished';
    
    if (bingoRoyaleGame.gameTimer) {
        clearTimeout(bingoRoyaleGame.gameTimer);
    }
    
    if (won) {
        // Extremely strict winning conditions for Royale
        const patternData = ROYALE_PATTERNS.find(p => p.name === bingoRoyaleGame.selectedPattern);
        const modeData = ROYALE_MODES.find(m => m.name === bingoRoyaleGame.gameMode);
        
        // Calculate performance metrics
        const avgReaction = bingoRoyaleGame.reactionTimes.length > 0 ? 
            bingoRoyaleGame.reactionTimes.reduce((a, b) => a + b, 0) / bingoRoyaleGame.reactionTimes.length : 0;
        const perfectRatio = bingoRoyaleGame.perfectMarks / Math.max(1, bingoRoyaleGame.reactionTimes.length);
        const missRatio = bingoRoyaleGame.missedCalls / Math.max(1, bingoRoyaleGame.calledNumbers.length);
        const challengeRatio = bingoRoyaleGame.challengesCompleted.size / ROYAL_CHALLENGES.length;
        
        // Ultra-strict royal requirements: 90%+ perfect, <5% misses, <0.8s avg reaction, 80%+ challenges
        if (perfectRatio >= 0.9 && missRatio < 0.05 && avgReaction < 0.8 && challengeRatio >= 0.8) {
            let baseMultiplier = patternData.multiplier * patternData.royalBonus * modeData.bonus;
            
            // Royal bonuses
            const reactionBonus = Math.max(0, (0.8 - avgReaction) * 5);
            const perfectBonus = perfectRatio * 4;
            const challengeBonus = challengeRatio * 3;
            const crownBonus = bingoRoyaleGame.crownBonus / 100;
            const royalBonus = bingoRoyaleGame.royalBalls * 0.3;
            const cardBonus = bingoRoyaleGame.cardCount * 0.2;
            
            const totalMultiplier = baseMultiplier * (1 + reactionBonus + perfectBonus + challengeBonus + crownBonus + royalBonus + cardBonus);
            const winnings = Math.floor(bingoRoyaleGame.betAmount * totalMultiplier * 0.08); // Very low base rate
            
            balance += winnings;
            updateBalance();
            
            bingoRoyaleGame.consecutiveWins++;
            bingoRoyaleGame.perfectGames++;
            bingoRoyaleGame.royaleLevel++;
            bingoRoyaleGame.crownJewels += Math.floor(winnings / 100);
            bingoRoyaleGame.royalPower = 100;
            
            // Update noble rank
            updateNobleRank();
            
            document.getElementById('royaleResult').innerHTML = 
                `<span class="text-diamond royal-glow">👑 ROYAL CHAMPION! 👑</span>`;
            document.getElementById('royaleStatus').innerHTML = 
                `${message} Legendary royal performance! Won ${winnings} GA!`;
        } else {
            document.getElementById('royaleResult').innerHTML = 
                `<span class="text-gold-400">👑 ROYAL BINGO BUT NOT LEGENDARY! 👑</span>`;
            document.getElementById('royaleStatus').innerHTML = 
                `${message} - Need 90%+ perfect marks, <5% misses, <0.8s avg reaction, 80%+ challenges for royal victory!`;
        }
    } else {
        bingoRoyaleGame.consecutiveWins = 0;
        bingoRoyaleGame.royalPower = Math.max(0, bingoRoyaleGame.royalPower - 30);
        
        document.getElementById('royaleResult').innerHTML = 
            `<span class="text-red-400">💥 ROYAL COURT DISMISSED! 💥</span>`;
        document.getElementById('royaleStatus').textContent = message;
    }
    
    // Update max streak
    bingoRoyaleGame.maxStreak = Math.max(bingoRoyaleGame.maxStreak, bingoRoyaleGame.consecutiveWins);
    
    updateRoyaleDisplay();
    resetRoyaleControls();
}

function updateNobleRank() {
    const wins = bingoRoyaleGame.perfectGames;
    let rank = 'Peasant';
    
    if (wins >= 100) rank = 'Legend';
    else if (wins >= 50) rank = 'Royalty';
    else if (wins >= 15) rank = 'Noble';
    else if (wins >= 5) rank = 'Knight';
    
    bingoRoyaleGame.nobleRank = rank;
}

function resetRoyaleControls() {
    setTimeout(() => {
        document.getElementById('startRoyale').disabled = false;
        document.getElementById('royalNumberDisplay').textContent = '--';
        document.getElementById('royalNumberLetter').textContent = 'ROYALE';
        document.getElementById('royalBallIndicator').textContent = '';
        document.getElementById('royalNumbersHistory').innerHTML = '';
        document.getElementById('royalProgressBar').style.width = '0%';
        document.getElementById('royalNumbersCount').textContent = '0/75';
        document.getElementById('royalReactionTime').textContent = '0.00s';
        document.getElementById('royaleStatus').textContent = 'Ready for royal bingo!';
        
        // Reset challenges
        bingoRoyaleGame.challengesCompleted.clear();
        ROYAL_CHALLENGES.forEach(challenge => {
            const challengeElement = document.getElementById(`challenge-${challenge.name}`);
            const statusElement = challengeElement.querySelector('.challenge-status');
            statusElement.textContent = '⏳';
            statusElement.className = 'challenge-status text-gray-400';
            challengeElement.classList.remove('bg-green-500/20');
        });
        
        // Reset cards
        generateRoyaleCards();
    }, 6000);
}

function updateRoyaleDisplay() {
    document.getElementById('royaleLevel').textContent = bingoRoyaleGame.royaleLevel;
    document.getElementById('nobleRank').textContent = bingoRoyaleGame.nobleRank;
    document.getElementById('crownJewels').textContent = bingoRoyaleGame.crownJewels;
    document.getElementById('thronePoints').textContent = bingoRoyaleGame.thronePoints;
    document.getElementById('royalStreak').textContent = bingoRoyaleGame.consecutiveWins;
    document.getElementById('royalPerfectGames').textContent = bingoRoyaleGame.perfectGames;
    document.getElementById('royalAchievements').textContent = bingoRoyaleGame.royalAchievements;
    document.getElementById('royalBalls').textContent = bingoRoyaleGame.royalBalls;
    document.getElementById('crownBonus').textContent = Math.floor(bingoRoyaleGame.crownBonus) + '%';
    document.getElementById('royalMultiplier').textContent = bingoRoyaleGame.royalMultiplier.toFixed(1) + 'x';
    document.getElementById('royalPerfectMarks').textContent = bingoRoyaleGame.perfectMarks;
    document.getElementById('royalMissedCalls').textContent = bingoRoyaleGame.missedCalls;
    
    // Calculate and display average reaction time
    if (bingoRoyaleGame.reactionTimes.length > 0) {
        const avg = bingoRoyaleGame.reactionTimes.reduce((a, b) => a + b, 0) / bingoRoyaleGame.reactionTimes.length;
        document.getElementById('royalAvgReaction').textContent = avg.toFixed(2) + 's';
    }
    
    // Update accuracy
    const totalCalls = bingoRoyaleGame.reactionTimes.length + bingoRoyaleGame.missedCalls;
    const accuracy = totalCalls > 0 ? ((totalCalls - bingoRoyaleGame.missedCalls) / totalCalls) * 100 : 100;
    document.getElementById('royalAccuracy').textContent = accuracy.toFixed(1) + '%';
    document.getElementById('accuracyBar').style.width = accuracy + '%';
    
    // Update royal power
    document.getElementById('royalPowerBar').style.width = bingoRoyaleGame.royalPower + '%';
    
    let powerStatus = 'Royal Court Assembling...';
    if (bingoRoyaleGame.royalPower >= 100) powerStatus = 'MAXIMUM ROYAL POWER!';
    else if (bingoRoyaleGame.royalPower >= 80) powerStatus = 'Royal Authority High';
    else if (bingoRoyaleGame.royalPower >= 60) powerStatus = 'Noble Influence Growing';
    else if (bingoRoyaleGame.royalPower >= 40) powerStatus = 'Court Favor Rising';
    else if (bingoRoyaleGame.royalPower >= 20) powerStatus = 'Royal Presence Weak';
    
    document.getElementById('royalPowerText').textContent = powerStatus;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBingoRoyaleGame();
});