// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Crypto Crossword: Word Wealth Game Implementation
const CRYPTO_WORDS = {
    // 3-letter words
    3: ['BTC', 'ETH', 'DeFi', 'NFT', 'DAO', 'ICO', 'KYC', 'AML', 'API', 'GPU'],
    // 4-letter words  
    4: ['HODL', 'FOMO', 'HASH', 'MINT', 'BURN', 'FORK', 'PUMP', 'DUMP', 'MOON', 'BEAR'],
    // 5-letter words
    5: ['BLOCK', 'CHAIN', 'TOKEN', 'WHALE', 'SHILL', 'STAKE', 'YIELD', 'SMART', 'TRADE', 'VAULT'],
    // 6-letter words
    6: ['MINING', 'WALLET', 'LEDGE<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ID<PERSON>', 'LIQUID', 'STABLE', 'MARKET', 'CRYP<PERSON>', 'DEGE<PERSON>'],
    // 7-letter words
    7: ['B<PERSON>COIN', 'ETHEREUM', 'POLYGON', 'SOLANA', 'CARDANO', 'AVALANCHE', 'PHANTOM', 'METAMASK', 'UNISWAP', 'PANCAKE'],
    // 8+ letter words
    8: ['BLOCKCHAIN', 'CONSENSUS', 'VALIDATOR', 'LIGHTNING', 'CROSSCHAIN', 'METAVERSE', 'GAMEFI', 'STABLECOIN']
};

const CRYPTO_CLUES = {
    'BTC': 'Digital gold (3)',
    'ETH': 'Smart contract platform (3)', 
    'DEFI': 'Decentralized finance (4)',
    'NFT': 'Non-fungible token (3)',
    'DAO': 'Decentralized autonomous org (3)',
    'HODL': 'Hold on for dear life (4)',
    'FOMO': 'Fear of missing out (4)',
    'HASH': 'Cryptographic fingerprint (4)',
    'MINT': 'Create new tokens (4)',
    'BURN': 'Destroy tokens permanently (4)',
    'FORK': 'Blockchain split (4)',
    'PUMP': 'Price surge (4)',
    'DUMP': 'Mass selling (4)',
    'MOON': 'Price going up dramatically (4)',
    'BEAR': 'Downward market trend (4)',
    'BLOCK': 'Data container in chain (5)',
    'CHAIN': 'Linked blocks of data (5)',
    'TOKEN': 'Digital asset unit (5)',
    'WHALE': 'Large crypto holder (5)',
    'SHILL': 'Promote aggressively (5)',
    'STAKE': 'Lock tokens for rewards (5)',
    'YIELD': 'Farming returns (5)',
    'SMART': '___ contract (5)',
    'TRADE': 'Buy and sell (5)',
    'VAULT': 'Secure storage (5)',
    'MINING': 'Block validation process (6)',
    'WALLET': 'Crypto storage app (6)',
    'LEDGER': 'Transaction record book (6)',
    'ORACLE': 'External data provider (6)',
    'BRIDGE': 'Cross-chain connector (6)',
    'LIQUID': 'Easy to trade (6)',
    'STABLE': '___ coin (6)',
    'MARKET': 'Trading venue (6)',
    'CRYPTO': 'Digital currency (6)',
    'DEGENS': 'Risky traders (6)',
    'BITCOIN': 'First cryptocurrency (7)',
    'ETHEREUM': 'Vitalik\'s creation (8)',
    'POLYGON': 'Layer 2 scaling (7)',
    'SOLANA': 'Fast blockchain (6)',
    'CARDANO': 'Academic blockchain (7)',
    'PHANTOM': 'Solana wallet (7)',
    'METAMASK': 'Popular ETH wallet (8)',
    'UNISWAP': 'DEX protocol (7)',
    'PANCAKE': '___ Swap (7)',
    'BLOCKCHAIN': 'Distributed ledger tech (10)',
    'CONSENSUS': 'Network agreement (9)',
    'VALIDATOR': 'Network verifier (9)',
    'LIGHTNING': 'Bitcoin layer 2 (9)',
    'CROSSCHAIN': 'Multi-blockchain (10)',
    'METAVERSE': 'Virtual world (9)',
    'STABLECOIN': 'Pegged cryptocurrency (10)'
};

let cryptoWordGame = {
    isPlaying: false,
    gridSize: 15,
    grid: [],
    words: [],
    foundWords: new Set(),
    selectedCells: [],
    currentWord: '',
    difficulty: 'medium',
    timeLimit: 300, // 5 minutes
    timeLeft: 300,
    timer: null,
    betAmount: 50,
    wordValues: {},
    totalValue: 0,
    perfectBonus: false,
    speedBonus: 0,
    cryptoLevel: 1,
    hashPower: 0,
    miningRewards: 0,
    wordStreak: 0,
    maxStreak: 0,
    hintsUsed: 0,
    maxHints: 3,
    blocksMined: 0,
    cryptoMultiplier: 1.0
};

const DIFFICULTY_SETTINGS = {
    easy: { 
        words: 8, 
        timeLimit: 420, 
        multiplier: 0.8, 
        gridSize: 12,
        description: 'Beginner crypto terms'
    },
    medium: { 
        words: 12, 
        timeLimit: 300, 
        multiplier: 1.0, 
        gridSize: 15,
        description: 'Intermediate blockchain vocab'
    },
    hard: { 
        words: 16, 
        timeLimit: 240, 
        multiplier: 1.5, 
        gridSize: 18,
        description: 'Advanced DeFi terminology'
    },
    expert: { 
        words: 20, 
        timeLimit: 180, 
        multiplier: 2.0, 
        gridSize: 20,
        description: 'Master crypto linguist'
    }
};

function loadCryptoWordGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🔤 CRYPTO CROSSWORD 🔤</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 WORD STAKE</label>
                        <input type="number" id="wordBet" value="50" min="20" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 DIFFICULTY</label>
                        <select id="wordDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Easy - ${DIFFICULTY_SETTINGS.easy.description}</option>
                            <option value="medium" selected>Medium - ${DIFFICULTY_SETTINGS.medium.description}</option>
                            <option value="hard">Hard - ${DIFFICULTY_SETTINGS.hard.description}</option>
                            <option value="expert">Expert - ${DIFFICULTY_SETTINGS.expert.description}</option>
                        </select>
                    </div>
                    
                    <button id="startWordGame" class="w-full cyber-button px-4 py-3 rounded-lg font-semibold text-white mb-4">
                        START WORD MINING
                    </button>
                    
                    <button id="useHint" class="w-full bg-yellow-600 hover:bg-yellow-500 px-4 py-2 rounded-lg font-semibold text-white mb-4" disabled>
                        USE HINT (${cryptoWordGame.maxHints - cryptoWordGame.hintsUsed} left)
                    </button>
                    
                    <div class="text-center">
                        <div id="wordTimer" class="text-2xl font-bold text-cyan-400 mb-2">5:00</div>
                        <div id="wordStatus" class="text-sm text-gray-300">Ready to mine crypto words</div>
                    </div>
                </div>
                
                <!-- Word List -->
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📝 WORD CLUES</h5>
                    <div id="wordClues" class="space-y-2 max-h-64 overflow-y-auto">
                        <!-- Clues will be populated here -->
                    </div>
                </div>
            </div>
            
            <!-- Game Grid -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="flex justify-between items-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400">🔍 CRYPTO GRID</h5>
                        <div class="text-right">
                            <div class="text-sm text-gray-300">Words Found: <span id="wordsFound" class="text-green-400">0</span>/<span id="totalWords">0</span></div>
                            <div class="text-sm text-gray-300">Current Value: <span id="currentValue" class="text-yellow-400">0 GA</span></div>
                        </div>
                    </div>
                    
                    <div id="wordGrid" class="grid gap-1 mx-auto justify-center mb-4" style="max-width: 600px;">
                        <!-- Grid will be generated here -->
                    </div>
                    
                    <div class="text-center">
                        <div id="selectedWord" class="text-lg font-bold text-cyan-400 mb-2">Select letters to form words</div>
                        <div id="wordResult" class="text-lg font-semibold"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Stats Panel -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-purple-400" id="cryptoLevel">1</div>
                <div class="text-sm text-gray-300">Crypto Level</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-cyan-400" id="hashPower">0</div>
                <div class="text-sm text-gray-300">Hash Power</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-green-400" id="wordStreak">0</div>
                <div class="text-sm text-gray-300">Word Streak</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-yellow-400" id="blocksMined">0</div>
                <div class="text-sm text-gray-300">Blocks Mined</div>
            </div>
        </div>
    `;
    
    setupCryptoWordGame();
}

function setupCryptoWordGame() {
    document.getElementById('startWordGame').addEventListener('click', startWordGame);
    document.getElementById('useHint').addEventListener('click', useHint);
    document.getElementById('wordDifficulty').addEventListener('change', updateDifficulty);
    
    updateDifficulty();
    updateWordDisplay();
}

function updateDifficulty() {
    const difficulty = document.getElementById('wordDifficulty').value;
    cryptoWordGame.difficulty = difficulty;
    const settings = DIFFICULTY_SETTINGS[difficulty];
    cryptoWordGame.gridSize = settings.gridSize;
    cryptoWordGame.timeLimit = settings.timeLimit;
}

function startWordGame() {
    const bet = parseInt(document.getElementById('wordBet').value);
    
    if (bet > balance) {
        document.getElementById('wordStatus').textContent = 'Insufficient balance for word mining!';
        return;
    }
    
    if (cryptoWordGame.isPlaying) return;
    
    // Deduct bet
    balance -= bet;
    cryptoWordGame.betAmount = bet;
    updateBalance();
    
    // Initialize game
    cryptoWordGame.isPlaying = true;
    cryptoWordGame.foundWords.clear();
    cryptoWordGame.selectedCells = [];
    cryptoWordGame.currentWord = '';
    cryptoWordGame.totalValue = 0;
    cryptoWordGame.hintsUsed = 0;
    cryptoWordGame.wordStreak = 0;
    cryptoWordGame.speedBonus = 0;
    
    const settings = DIFFICULTY_SETTINGS[cryptoWordGame.difficulty];
    cryptoWordGame.timeLeft = settings.timeLimit;
    
    // Generate puzzle
    generateWordPuzzle();
    
    // Start timer
    startWordTimer();
    
    // Update UI
    document.getElementById('startWordGame').disabled = true;
    document.getElementById('useHint').disabled = false;
    document.getElementById('wordStatus').textContent = 'Mining crypto words... Find all hidden terms!';
    
    updateWordDisplay();
}

function generateWordPuzzle() {
    const settings = DIFFICULTY_SETTINGS[cryptoWordGame.difficulty];
    const gridSize = settings.gridSize;
    
    // Initialize empty grid
    cryptoWordGame.grid = Array(gridSize).fill().map(() => Array(gridSize).fill(''));
    cryptoWordGame.words = [];
    cryptoWordGame.wordValues = {};
    
    // Select words to place
    const selectedWords = selectWordsForPuzzle(settings.words);
    
    // Place words in grid
    selectedWords.forEach(word => {
        if (placeWordInGrid(word)) {
            cryptoWordGame.words.push(word);
            // Calculate word value based on length and difficulty
            const baseValue = word.length * 10;
            const difficultyMultiplier = settings.multiplier;
            cryptoWordGame.wordValues[word] = Math.floor(baseValue * difficultyMultiplier);
        }
    });
    
    // Fill empty cells with random letters
    fillEmptyCells();
    
    // Generate clues
    generateWordClues();
    
    // Create grid UI
    createGridUI();
    
    document.getElementById('totalWords').textContent = cryptoWordGame.words.length;
}

function selectWordsForPuzzle(count) {
    const allWords = [];
    
    // Add words from different length categories
    Object.keys(CRYPTO_WORDS).forEach(length => {
        CRYPTO_WORDS[length].forEach(word => {
            if (CRYPTO_CLUES[word]) {
                allWords.push(word);
            }
        });
    });
    
    // Shuffle and select
    const shuffled = allWords.sort(() => Math.random() - 0.5);
    return shuffled.slice(0, count);
}

function placeWordInGrid(word) {
    const gridSize = cryptoWordGame.gridSize;
    const maxAttempts = 100;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        const direction = Math.random() < 0.5 ? 'horizontal' : 'vertical';
        const row = Math.floor(Math.random() * gridSize);
        const col = Math.floor(Math.random() * gridSize);
        
        if (canPlaceWord(word, row, col, direction)) {
            placeWord(word, row, col, direction);
            return true;
        }
    }
    
    return false;
}

function canPlaceWord(word, row, col, direction) {
    const gridSize = cryptoWordGame.gridSize;
    
    if (direction === 'horizontal') {
        if (col + word.length > gridSize) return false;
        for (let i = 0; i < word.length; i++) {
            const cell = cryptoWordGame.grid[row][col + i];
            if (cell !== '' && cell !== word[i]) return false;
        }
    } else {
        if (row + word.length > gridSize) return false;
        for (let i = 0; i < word.length; i++) {
            const cell = cryptoWordGame.grid[row + i][col];
            if (cell !== '' && cell !== word[i]) return false;
        }
    }
    
    return true;
}

function placeWord(word, row, col, direction) {
    if (direction === 'horizontal') {
        for (let i = 0; i < word.length; i++) {
            cryptoWordGame.grid[row][col + i] = word[i];
        }
    } else {
        for (let i = 0; i < word.length; i++) {
            cryptoWordGame.grid[row + i][col] = word[i];
        }
    }
}

function fillEmptyCells() {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const gridSize = cryptoWordGame.gridSize;
    
    for (let row = 0; row < gridSize; row++) {
        for (let col = 0; col < gridSize; col++) {
            if (cryptoWordGame.grid[row][col] === '') {
                cryptoWordGame.grid[row][col] = letters[Math.floor(Math.random() * letters.length)];
            }
        }
    }
}

function generateWordClues() {
    const cluesContainer = document.getElementById('wordClues');
    cluesContainer.innerHTML = '';
    
    cryptoWordGame.words.forEach((word, index) => {
        const clueDiv = document.createElement('div');
        clueDiv.className = 'flex justify-between items-center p-2 bg-black/20 rounded border border-purple-500/20';
        clueDiv.innerHTML = `
            <span class="text-gray-300">${CRYPTO_CLUES[word] || `${word} (${word.length})`}</span>
            <span class="text-yellow-400 font-bold">${cryptoWordGame.wordValues[word]} GA</span>
        `;
        clueDiv.id = `clue-${word}`;
        cluesContainer.appendChild(clueDiv);
    });
}

function createGridUI() {
    const gridContainer = document.getElementById('wordGrid');
    const gridSize = cryptoWordGame.gridSize;
    
    gridContainer.style.gridTemplateColumns = `repeat(${gridSize}, 1fr)`;
    gridContainer.innerHTML = '';
    
    for (let row = 0; row < gridSize; row++) {
        for (let col = 0; col < gridSize; col++) {
            const cell = document.createElement('div');
            cell.className = 'w-8 h-8 bg-black/50 border border-purple-500/30 rounded flex items-center justify-center text-sm font-bold text-white cursor-pointer hover:bg-purple-500/20 transition-all';
            cell.textContent = cryptoWordGame.grid[row][col];
            cell.dataset.row = row;
            cell.dataset.col = col;
            
            cell.addEventListener('click', () => selectCell(row, col, cell));
            
            gridContainer.appendChild(cell);
        }
    }
}

function selectCell(row, col, cellElement) {
    if (!cryptoWordGame.isPlaying) return;
    
    const cellKey = `${row}-${col}`;
    const cellIndex = cryptoWordGame.selectedCells.findIndex(c => c.key === cellKey);
    
    if (cellIndex >= 0) {
        // Deselect cell
        cryptoWordGame.selectedCells.splice(cellIndex, 1);
        cellElement.classList.remove('bg-cyan-500', 'border-cyan-400');
    } else {
        // Select cell
        cryptoWordGame.selectedCells.push({
            key: cellKey,
            row: row,
            col: col,
            letter: cryptoWordGame.grid[row][col],
            element: cellElement
        });
        cellElement.classList.add('bg-cyan-500', 'border-cyan-400');
    }
    
    // Update current word
    updateCurrentWord();
}

function updateCurrentWord() {
    cryptoWordGame.currentWord = cryptoWordGame.selectedCells
        .map(cell => cell.letter)
        .join('');
    
    document.getElementById('selectedWord').textContent = 
        cryptoWordGame.currentWord || 'Select letters to form words';
    
    // Check if word is valid
    if (cryptoWordGame.currentWord.length >= 3) {
        checkWord();
    }
}

function checkWord() {
    const word = cryptoWordGame.currentWord;
    
    if (cryptoWordGame.words.includes(word) && !cryptoWordGame.foundWords.has(word)) {
        // Valid word found!
        foundWord(word);
    }
}

function foundWord(word) {
    cryptoWordGame.foundWords.add(word);
    cryptoWordGame.wordStreak++;
    cryptoWordGame.maxStreak = Math.max(cryptoWordGame.maxStreak, cryptoWordGame.wordStreak);
    
    // Add value
    const wordValue = cryptoWordGame.wordValues[word];
    cryptoWordGame.totalValue += wordValue;
    
    // Highlight found word
    cryptoWordGame.selectedCells.forEach(cell => {
        cell.element.classList.remove('bg-cyan-500', 'border-cyan-400');
        cell.element.classList.add('bg-green-500', 'border-green-400', 'found-word');
    });
    
    // Update clue
    const clueElement = document.getElementById(`clue-${word}`);
    if (clueElement) {
        clueElement.classList.add('bg-green-500/20', 'border-green-500/50');
        clueElement.innerHTML += ' <span class="text-green-400">✓ FOUND</span>';
    }
    
    // Clear selection
    cryptoWordGame.selectedCells = [];
    cryptoWordGame.currentWord = '';
    
    // Update display
    document.getElementById('selectedWord').innerHTML = 
        `<span class="text-green-400">✓ ${word} FOUND! +${wordValue} GA</span>`;
    document.getElementById('wordsFound').textContent = cryptoWordGame.foundWords.size;
    document.getElementById('currentValue').textContent = cryptoWordGame.totalValue + ' GA';
    
    // Add mining effects
    cryptoWordGame.hashPower += word.length * 10;
    cryptoWordGame.blocksMined++;
    
    // Check for completion
    if (cryptoWordGame.foundWords.size === cryptoWordGame.words.length) {
        endWordGame(true);
    }
    
    updateWordDisplay();
    
    // Clear message after delay
    setTimeout(() => {
        if (cryptoWordGame.isPlaying) {
            document.getElementById('selectedWord').textContent = 'Select letters to form words';
        }
    }, 2000);
}

function useHint() {
    if (!cryptoWordGame.isPlaying || cryptoWordGame.hintsUsed >= cryptoWordGame.maxHints) return;
    
    // Find an unfound word
    const unfoundWords = cryptoWordGame.words.filter(word => !cryptoWordGame.foundWords.has(word));
    if (unfoundWords.length === 0) return;
    
    const hintWord = unfoundWords[Math.floor(Math.random() * unfoundWords.length)];
    cryptoWordGame.hintsUsed++;
    
    // Highlight first letter of hint word
    const gridSize = cryptoWordGame.gridSize;
    for (let row = 0; row < gridSize; row++) {
        for (let col = 0; col < gridSize; col++) {
            if (cryptoWordGame.grid[row][col] === hintWord[0]) {
                const cellElement = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                if (cellElement && !cellElement.classList.contains('found-word')) {
                    cellElement.classList.add('bg-yellow-500', 'border-yellow-400');
                    setTimeout(() => {
                        cellElement.classList.remove('bg-yellow-500', 'border-yellow-400');
                    }, 3000);
                    break;
                }
            }
        }
    }
    
    document.getElementById('wordStatus').innerHTML = 
        `<span class="text-yellow-400">💡 Hint: Look for "${hintWord[0]}..." (${hintWord.length} letters)</span>`;
    
    // Update hint button
    const hintButton = document.getElementById('useHint');
    hintButton.textContent = `USE HINT (${cryptoWordGame.maxHints - cryptoWordGame.hintsUsed} left)`;
    if (cryptoWordGame.hintsUsed >= cryptoWordGame.maxHints) {
        hintButton.disabled = true;
    }
    
    setTimeout(() => {
        if (cryptoWordGame.isPlaying) {
            document.getElementById('wordStatus').textContent = 'Mining crypto words... Find all hidden terms!';
        }
    }, 5000);
}

function startWordTimer() {
    cryptoWordGame.timer = setInterval(() => {
        cryptoWordGame.timeLeft--;
        
        const minutes = Math.floor(cryptoWordGame.timeLeft / 60);
        const seconds = cryptoWordGame.timeLeft % 60;
        document.getElementById('wordTimer').textContent = 
            `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        // Color coding for urgency
        const timerElement = document.getElementById('wordTimer');
        if (cryptoWordGame.timeLeft <= 30) {
            timerElement.className = 'text-2xl font-bold text-red-400 animate-pulse';
        } else if (cryptoWordGame.timeLeft <= 60) {
            timerElement.className = 'text-2xl font-bold text-yellow-400';
        } else {
            timerElement.className = 'text-2xl font-bold text-cyan-400';
        }
        
        if (cryptoWordGame.timeLeft <= 0) {
            endWordGame(false);
        }
    }, 1000);
}

function endWordGame(completed) {
    cryptoWordGame.isPlaying = false;
    
    if (cryptoWordGame.timer) {
        clearInterval(cryptoWordGame.timer);
    }
    
    const settings = DIFFICULTY_SETTINGS[cryptoWordGame.difficulty];
    const foundRatio = cryptoWordGame.foundWords.size / cryptoWordGame.words.length;
    
    if (completed || foundRatio >= 0.7) {
        // Calculate winnings
        let totalWinnings = cryptoWordGame.totalValue;
        
        // Completion bonus
        if (completed) {
            totalWinnings *= 2;
            cryptoWordGame.perfectBonus = true;
        }
        
        // Time bonus
        const timeBonus = Math.max(0, cryptoWordGame.timeLeft / settings.timeLimit);
        totalWinnings *= (1 + timeBonus);
        
        // Streak bonus
        const streakBonus = Math.min(cryptoWordGame.wordStreak * 0.1, 1.0);
        totalWinnings *= (1 + streakBonus);
        
        // Difficulty multiplier
        totalWinnings *= settings.multiplier;
        
        // Apply very low base rate (5-15% of calculated value)
        const finalWinnings = Math.floor(totalWinnings * (0.05 + Math.random() * 0.1));
        
        balance += finalWinnings;
        updateBalance();
        
        // Update stats
        cryptoWordGame.cryptoLevel++;
        cryptoWordGame.miningRewards += finalWinnings;
        cryptoWordGame.cryptoMultiplier += 0.1;
        
        document.getElementById('wordResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🎉 CRYPTO WORDS MINED! 🎉</span>`;
        document.getElementById('wordStatus').innerHTML = 
            `Found ${cryptoWordGame.foundWords.size}/${cryptoWordGame.words.length} words! Earned ${finalWinnings} GA!`;
    } else {
        document.getElementById('wordResult').innerHTML = 
            `<span class="text-red-400">💥 MINING INCOMPLETE! 💥</span>`;
        document.getElementById('wordStatus').innerHTML = 
            `Only found ${cryptoWordGame.foundWords.size}/${cryptoWordGame.words.length} words. Need 70%+ completion to earn rewards.`;
    }
    
    updateWordDisplay();
    resetWordControls();
}

function resetWordControls() {
    setTimeout(() => {
        document.getElementById('startWordGame').disabled = false;
        document.getElementById('useHint').disabled = true;
        document.getElementById('useHint').textContent = `USE HINT (${cryptoWordGame.maxHints} left)`;
        document.getElementById('wordTimer').textContent = '5:00';
        document.getElementById('wordTimer').className = 'text-2xl font-bold text-cyan-400';
        document.getElementById('selectedWord').textContent = 'Select letters to form words';
        document.getElementById('wordResult').textContent = '';
        document.getElementById('wordStatus').textContent = 'Ready to mine crypto words';
        document.getElementById('wordsFound').textContent = '0';
        document.getElementById('totalWords').textContent = '0';
        document.getElementById('currentValue').textContent = '0 GA';
        document.getElementById('wordGrid').innerHTML = '';
        document.getElementById('wordClues').innerHTML = '';
    }, 5000);
}

function updateWordDisplay() {
    document.getElementById('cryptoLevel').textContent = cryptoWordGame.cryptoLevel;
    document.getElementById('hashPower').textContent = cryptoWordGame.hashPower;
    document.getElementById('wordStreak').textContent = cryptoWordGame.wordStreak;
    document.getElementById('blocksMined').textContent = cryptoWordGame.blocksMined;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCryptoWordGame();
});