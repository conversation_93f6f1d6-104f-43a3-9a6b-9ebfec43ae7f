// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadRPSGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                            <h4 class="text-xl font-bold mb-4 text-red-400">RPS SHOWDOWN</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="rpsBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">GAME MODE</label>
                                <select id="rpsMode" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="single">Single Round (2x win)</option>
                                    <option value="best3">Best of 3 (3x win)</option>
                                    <option value="best5">Best of 5 (5x win)</option>
                                </select>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm mb-3 text-gray-300">CHOOSE YOUR WEAPON</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <button id="rpsRock" class="rps-choice py-4 rounded-lg font-bold bg-gray-600 hover:bg-gray-700 text-white border-2 border-transparent" data-choice="rock">
                                        🪨<br>ROCK
                                    </button>
                                    <button id="rpsPaper" class="rps-choice py-4 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white border-2 border-transparent" data-choice="paper">
                                        📄<br>PAPER
                                    </button>
                                    <button id="rpsScissors" class="rps-choice py-4 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white border-2 border-transparent" data-choice="scissors">
                                        ✂️<br>SCISSORS
                                    </button>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Score</div>
                                <div id="rpsScore" class="text-xl font-bold text-red-400">Player 0 - 0 AI</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="rpsPotentialWin" class="text-xl font-bold text-green-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- AI Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-red-400">AI ANALYSIS</h5>
                            <div class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Difficulty:</span>
                                    <span class="text-red-400">Adaptive</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Pattern Recognition:</span>
                                    <span class="text-orange-400">Active</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Prediction Mode:</span>
                                    <span class="text-purple-400">Neural</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Battle Arena -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                            <div id="rpsArena" class="relative bg-black/50 rounded-lg p-6 h-96">
                                <!-- Player Side -->
                                <div class="absolute left-6 top-1/2 transform -translate-y-1/2 text-center">
                                    <div class="text-sm text-gray-400 mb-2">PLAYER</div>
                                    <div id="playerChoice" class="w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-3xl border-4 border-cyan-400 neon-border">
                                        ?
                                    </div>
                                    <div id="playerName" class="text-cyan-400 font-bold mt-2">YOU</div>
                                </div>
                                
                                <!-- VS -->
                                <div class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                                    <div class="text-4xl font-bold text-red-400 neon-glow">VS</div>
                                    <div id="rpsCountdown" class="text-2xl font-bold text-yellow-400 mt-2"></div>
                                </div>
                                
                                <!-- AI Side -->
                                <div class="absolute right-6 top-1/2 transform -translate-y-1/2 text-center">
                                    <div class="text-sm text-gray-400 mb-2">AI</div>
                                    <div id="aiChoice" class="w-20 h-20 bg-gradient-to-br from-red-500 to-pink-500 rounded-full flex items-center justify-center text-3xl border-4 border-red-400 neon-border">
                                        ?
                                    </div>
                                    <div id="aiName" class="text-red-400 font-bold mt-2">CYBER-AI</div>
                                </div>
                                
                                <!-- Result -->
                                <div id="rpsRoundResult" class="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center text-xl font-bold"></div>
                            </div>
                            <div id="rpsStatus" class="text-center mt-4 text-lg font-semibold">Choose your weapon to start the battle</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeRPSGame();
        }
        
        let rpsGame = {
            isPlaying: false,
            betAmount: 0,
            mode: 'single',
            playerScore: 0,
            aiScore: 0,
            currentRound: 0,
            maxRounds: 1,
            playerHistory: []
        };
        
        function initializeRPSGame() {
            document.querySelectorAll('.rps-choice').forEach(button => {
                button.addEventListener('click', () => makeRPSChoice(button.dataset.choice));
            });
            
            document.getElementById('rpsMode').addEventListener('change', updateRPSMode);
            updateRPSMode();
        }
        
        function updateRPSMode() {
            const mode = document.getElementById('rpsMode').value;
            rpsGame.mode = mode;
            
            const betAmount = parseInt(document.getElementById('rpsBet').value) || 0;
            let multiplier = 2;
            
            switch(mode) {
                case 'single':
                    rpsGame.maxRounds = 1;
                    multiplier = 2;
                    break;
                case 'best3':
                    rpsGame.maxRounds = 3;
                    multiplier = 3;
                    break;
                case 'best5':
                    rpsGame.maxRounds = 5;
                    multiplier = 5;
                    break;
            }
            
            document.getElementById('rpsPotentialWin').textContent = '$' + (betAmount * multiplier);
        }
        
        function makeRPSChoice(playerChoice) {
            const betAmount = parseInt(document.getElementById('rpsBet').value);
            
            if (!rpsGame.isPlaying) {
                // Start new game
                if (betAmount > balance) {
                    alert('Insufficient balance!');
                    return;
                }
                
                balance -= betAmount;
                updateBalance();
                
                rpsGame.isPlaying = true;
                rpsGame.betAmount = betAmount;
                rpsGame.playerScore = 0;
                rpsGame.aiScore = 0;
                rpsGame.currentRound = 0;
                rpsGame.playerHistory = [];
                
                document.getElementById('rpsStatus').textContent = 'Battle in progress...';
            }
            
            // Add to player history for AI learning
            rpsGame.playerHistory.push(playerChoice);
            
            // Generate AI choice
            const aiChoice = generateAIChoice();
            
            // Animate the battle
            animateRPSBattle(playerChoice, aiChoice);
        }
        
        function generateAIChoice() {
            const choices = ['rock', 'paper', 'scissors'];
            
            // For first few rounds, play randomly
            if (rpsGame.playerHistory.length <= 2) {
                return choices[Math.floor(Math.random() * 3)];
            }
            
            // Analyze player patterns
            const history = rpsGame.playerHistory;
            const recent = history.slice(-3);
            
            // Check for patterns
            if (recent.length >= 2) {
                const lastChoice = recent[recent.length - 1];
                const secondLast = recent[recent.length - 2];
                
                // If player repeated last choice, counter it
                if (lastChoice === secondLast) {
                    const counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    };
                    return counters[lastChoice];
                }
            }
            
            // Count frequencies
            const counts = { rock: 0, paper: 0, scissors: 0 };
            history.forEach(choice => counts[choice]++);
            
            // Counter the most frequent choice
            const mostFrequent = Object.keys(counts).reduce((a, b) => 
                counts[a] > counts[b] ? a : b
            );
            
            const counters = {
                'rock': 'paper',
                'paper': 'scissors',
                'scissors': 'rock'
            };
            
            // 70% chance to counter, 30% random
            if (Math.random() < 0.7) {
                return counters[mostFrequent];
            } else {
                return choices[Math.floor(Math.random() * 3)];
            }
        }
        
        function animateRPSBattle(playerChoice, aiChoice) {
            const playerDisplay = document.getElementById('playerChoice');
            const aiDisplay = document.getElementById('aiChoice');
            const countdown = document.getElementById('rpsCountdown');
            
            // Disable choices during animation
            document.querySelectorAll('.rps-choice').forEach(btn => btn.disabled = true);
            
            // Countdown animation
            let count = 3;
            countdown.textContent = count;
            
            const countInterval = setInterval(() => {
                count--;
                if (count > 0) {
                    countdown.textContent = count;
                } else {
                    countdown.textContent = 'FIGHT!';
                    clearInterval(countInterval);
                    
                    // Reveal choices
                    setTimeout(() => {
                        showRPSChoices(playerChoice, aiChoice);
                        countdown.textContent = '';
                    }, 500);
                }
            }, 800);
        }
        
        function showRPSChoices(playerChoice, aiChoice) {
            const playerDisplay = document.getElementById('playerChoice');
            const aiDisplay = document.getElementById('aiChoice');
            
            const choiceEmojis = {
                'rock': '🪨',
                'paper': '📄',
                'scissors': '✂️'
            };
            
            playerDisplay.textContent = choiceEmojis[playerChoice];
            aiDisplay.textContent = choiceEmojis[aiChoice];
            
            // Determine winner
            const result = determineRPSWinner(playerChoice, aiChoice);
            
            // Update scores
            if (result === 'player') {
                rpsGame.playerScore++;
            } else if (result === 'ai') {
                rpsGame.aiScore++;
            }
            
            rpsGame.currentRound++;
            
            // Update display
            document.getElementById('rpsScore').textContent = 
                `Player ${rpsGame.playerScore} - ${rpsGame.aiScore} AI`;
            
            // Show round result
            const resultDisplay = document.getElementById('rpsRoundResult');
            if (result === 'player') {
                resultDisplay.innerHTML = '<span class="text-green-400 neon-glow">ROUND WIN!</span>';
            } else if (result === 'ai') {
                resultDisplay.innerHTML = '<span class="text-red-400">ROUND LOST!</span>';
            } else {
                resultDisplay.innerHTML = '<span class="text-yellow-400">DRAW!</span>';
            }
            
            // Check for game end
            setTimeout(() => {
                if (checkRPSGameEnd()) {
                    endRPSGame();
                } else {
                    // Continue to next round
                    resultDisplay.textContent = '';
                    playerDisplay.textContent = '?';
                    aiDisplay.textContent = '?';
                    document.querySelectorAll('.rps-choice').forEach(btn => btn.disabled = false);
                    document.getElementById('rpsStatus').textContent = 'Choose your next move';
                }
            }, 2000);
        }
        
        function determineRPSWinner(player, ai) {
            if (player === ai) return 'draw';
            
            const winConditions = {
                'rock': 'scissors',
                'paper': 'rock',
                'scissors': 'paper'
            };
            
            return winConditions[player] === ai ? 'player' : 'ai';
        }
        
        function checkRPSGameEnd() {
            if (rpsGame.mode === 'single') {
                return rpsGame.currentRound >= 1;
            } else if (rpsGame.mode === 'best3') {
                return rpsGame.playerScore >= 2 || rpsGame.aiScore >= 2;
            } else if (rpsGame.mode === 'best5') {
                return rpsGame.playerScore >= 3 || rpsGame.aiScore >= 3;
            }
            return false;
        }
        
        function endRPSGame() {
            rpsGame.isPlaying = false;
            
            // Determine final winner
            let gameWon = false;
            if (rpsGame.mode === 'single') {
                gameWon = rpsGame.playerScore > rpsGame.aiScore;
            } else {
                const targetWins = rpsGame.mode === 'best3' ? 2 : 3;
                gameWon = rpsGame.playerScore >= targetWins;
            }
            
            // Calculate winnings
            if (gameWon) {
                const multipliers = { single: 2, best3: 3, best5: 5 };
                const winnings = rpsGame.betAmount * multipliers[rpsGame.mode];
                balance += winnings;
                updateBalance();
                
                document.getElementById('rpsStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">VICTORY! Won $${winnings}!</span>`;
            } else {
                document.getElementById('rpsStatus').innerHTML = 
                    `<span class="text-red-400">DEFEAT! AI wins this round!</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                document.getElementById('rpsScore').textContent = 'Player 0 - 0 AI';
                document.getElementById('rpsRoundResult').textContent = '';
                document.getElementById('playerChoice').textContent = '?';
                document.getElementById('aiChoice').textContent = '?';
                document.getElementById('rpsStatus').textContent = 'Choose your weapon to start the battle';
                document.querySelectorAll('.rps-choice').forEach(btn => btn.disabled = false);
            }, 3000);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRPSGame();
});