// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadPumpGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                            <h4 class="text-xl font-bold mb-4 text-orange-400">NEURAL PUMP</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="pumpBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">TARGET MULTIPLIER</label>
                                <input type="number" id="pumpTarget" value="2.00" min="1.01" step="0.01" 
                                       class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <button id="startPump" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                START PUMP
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Market Price</div>
                                <div id="pumpPrice" class="text-3xl font-bold text-orange-400 neon-glow">$1.00</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="pumpPotentialWin" class="text-xl font-bold text-green-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Market Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-orange-400">MARKET ANALYSIS</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Volatility:</span>
                                    <span id="pumpVolatility" class="text-orange-400">Medium</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Trend:</span>
                                    <span id="pumpTrend" class="text-green-400">Bullish</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Risk Level:</span>
                                    <span id="pumpRisk" class="text-red-400">High</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Pumps -->
                        <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-orange-400">RECENT PUMPS</h5>
                            <div id="pumpHistory" class="space-y-1 max-h-32 overflow-y-auto text-sm">
                                <!-- Recent results will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Market Chart -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                            <div id="pumpChart" class="relative bg-black/50 rounded-lg p-4 h-96 overflow-hidden">
                                <canvas id="pumpCanvas" width="400" height="350" class="w-full h-full"></canvas>
                            </div>
                            <div id="pumpStatus" class="text-center mt-4 text-lg font-semibold">Market is ready for pumping</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializePump();
        }
        
        let pumpGame = {
            canvas: null,
            ctx: null,
            isPlaying: false,
            currentPrice: 1.00,
            betAmount: 0,
            targetMultiplier: 2.00,
            startTime: 0,
            priceHistory: [],
            volatility: 0.02,
            trend: 1,
            pumpDuration: 0,
            animationId: null
        };
        
        function initializePump() {
            pumpGame.canvas = document.getElementById('pumpCanvas');
            pumpGame.ctx = pumpGame.canvas.getContext('2d');
            
            // Initialize price history
            pumpGame.priceHistory = Array(100).fill(1.00);
            
            drawPumpChart();
            updatePumpStats();
            
            document.getElementById('startPump').addEventListener('click', startPumpGame);
        }
        
        function drawPumpChart() {
            const ctx = pumpGame.ctx;
            const canvas = pumpGame.canvas;
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid
            ctx.strokeStyle = '#ff690020';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 40) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 40) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            // Draw price line
            if (pumpGame.priceHistory.length > 1) {
                const maxPrice = Math.max(...pumpGame.priceHistory);
                const minPrice = Math.min(...pumpGame.priceHistory);
                const priceRange = maxPrice - minPrice || 1;
                
                ctx.strokeStyle = '#ff6900';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let i = 0; i < pumpGame.priceHistory.length; i++) {
                    const x = (i / (pumpGame.priceHistory.length - 1)) * canvas.width;
                    const normalizedPrice = (pumpGame.priceHistory[i] - minPrice) / priceRange;
                    const y = canvas.height - (normalizedPrice * (canvas.height - 40)) - 20;
                    
                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
                
                // Highlight current position
                if (pumpGame.isPlaying) {
                    const currentX = ((pumpGame.priceHistory.length - 1) / (pumpGame.priceHistory.length - 1)) * canvas.width;
                    const currentNormalizedPrice = (pumpGame.currentPrice - minPrice) / priceRange;
                    const currentY = canvas.height - (currentNormalizedPrice * (canvas.height - 40)) - 20;
                    
                    ctx.fillStyle = '#ff6900';
                    ctx.beginPath();
                    ctx.arc(currentX - 20, currentY, 6, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // Draw target line
            if (pumpGame.isPlaying) {
                const targetPrice = pumpGame.targetMultiplier;
                const maxPrice = Math.max(...pumpGame.priceHistory);
                const minPrice = Math.min(...pumpGame.priceHistory);
                const priceRange = maxPrice - minPrice || 1;
                
                if (targetPrice >= minPrice && targetPrice <= maxPrice) {
                    const normalizedTarget = (targetPrice - minPrice) / priceRange;
                    const targetY = canvas.height - (normalizedTarget * (canvas.height - 40)) - 20;
                    
                    ctx.strokeStyle = '#39ff14';
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(0, targetY);
                    ctx.lineTo(canvas.width, targetY);
                    ctx.stroke();
                    ctx.setLineDash([]);
                    
                    // Target label
                    ctx.fillStyle = '#39ff14';
                    ctx.font = '12px monospace';
                    ctx.fillText('TARGET', 10, targetY - 5);
                }
            }
        }
        
        function updatePumpStats() {
            // Update volatility display
            if (pumpGame.volatility < 0.015) {
                document.getElementById('pumpVolatility').textContent = 'Low';
            } else if (pumpGame.volatility < 0.025) {
                document.getElementById('pumpVolatility').textContent = 'Medium';
            } else {
                document.getElementById('pumpVolatility').textContent = 'High';
            }
            
            // Update trend
            const recentPrices = pumpGame.priceHistory.slice(-10);
            const avgRecent = recentPrices.reduce((a, b) => a + b, 0) / recentPrices.length;
            const oldPrices = pumpGame.priceHistory.slice(-20, -10);
            const avgOld = oldPrices.reduce((a, b) => a + b, 0) / oldPrices.length;
            
            if (avgRecent > avgOld * 1.02) {
                document.getElementById('pumpTrend').innerHTML = '<span class="text-green-400">Bullish</span>';
            } else if (avgRecent < avgOld * 0.98) {
                document.getElementById('pumpTrend').innerHTML = '<span class="text-red-400">Bearish</span>';
            } else {
                document.getElementById('pumpTrend').innerHTML = '<span class="text-yellow-400">Sideways</span>';
            }
        }
        
        function startPumpGame() {
            const betAmount = parseInt(document.getElementById('pumpBet').value);
            const targetMultiplier = parseFloat(document.getElementById('pumpTarget').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize game state
            pumpGame.isPlaying = true;
            pumpGame.currentPrice = 1.00;
            pumpGame.betAmount = betAmount;
            pumpGame.targetMultiplier = targetMultiplier;
            pumpGame.startTime = Date.now();
            pumpGame.pumpDuration = Math.random() * 8 + 2; // 2-10 seconds
            pumpGame.priceHistory = [1.00];
            
            // Update UI
            document.getElementById('startPump').disabled = true;
            document.getElementById('pumpStatus').textContent = 'Market is pumping! Watch the price climb...';
            
            animatePump();
        }
        
        function animatePump() {
            function animate() {
                if (!pumpGame.isPlaying) return;
                
                const elapsed = (Date.now() - pumpGame.startTime) / 1000;
                
                // Generate price movement
                const pumpProgress = Math.min(elapsed / pumpGame.pumpDuration, 1);
                const baseGrowth = Math.pow(1.1, elapsed); // Exponential growth
                const volatility = (Math.random() - 0.5) * pumpGame.volatility * 2;
                const trendEffect = pumpGame.trend * 0.01;
                
                pumpGame.currentPrice = baseGrowth + volatility + trendEffect;
                pumpGame.priceHistory.push(pumpGame.currentPrice);
                
                // Limit history length
                if (pumpGame.priceHistory.length > 100) {
                    pumpGame.priceHistory.shift();
                }
                
                // Update display
                document.getElementById('pumpPrice').textContent = '$' + pumpGame.currentPrice.toFixed(2);
                document.getElementById('pumpPotentialWin').textContent = 
                    '$' + Math.floor(pumpGame.betAmount * pumpGame.currentPrice);
                
                // Check if target reached
                if (pumpGame.currentPrice >= pumpGame.targetMultiplier) {
                    // Success!
                    const winnings = Math.floor(pumpGame.betAmount * pumpGame.currentPrice);
                    balance += winnings;
                    updateBalance();
                    
                    endPumpGame(true, winnings);
                    return;
                }
                
                // Check if pump duration exceeded (crash)
                if (pumpProgress >= 1) {
                    // Market crashed
                    endPumpGame(false, 0);
                    return;
                }
                
                drawPumpChart();
                updatePumpStats();
                
                pumpGame.animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        function endPumpGame(won, winnings) {
            pumpGame.isPlaying = false;
            if (pumpGame.animationId) {
                cancelAnimationFrame(pumpGame.animationId);
            }
            
            // Update UI
            document.getElementById('startPump').disabled = false;
            
            if (won) {
                document.getElementById('pumpStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Target reached! Won $${winnings}</span>`;
            } else {
                document.getElementById('pumpStatus').innerHTML = 
                    `<span class="text-red-400">Market crashed at $${pumpGame.currentPrice.toFixed(2)}!</span>`;
            }
            
            // Add to history
            addPumpHistory(pumpGame.currentPrice, won);
            
            // Reset after delay
            setTimeout(() => {
                pumpGame.currentPrice = 1.00;
                pumpGame.priceHistory = Array(100).fill(1.00);
                document.getElementById('pumpPrice').textContent = '$1.00';
                document.getElementById('pumpPotentialWin').textContent = '0 GA';
                document.getElementById('pumpStatus').textContent = 'Market is ready for pumping';
                drawPumpChart();
            }, 3000);
        }
        
        function addPumpHistory(finalPrice, won) {
            const history = document.getElementById('pumpHistory');
            const result = document.createElement('div');
            result.className = `flex justify-between py-1 ${won ? 'text-green-400' : 'text-red-400'}`;
            result.innerHTML = `
                <span>$${finalPrice.toFixed(2)}</span>
                <span class="text-xs">${won ? 'WIN' : 'CRASH'}</span>
            `;
            
            history.insertBefore(result, history.firstChild);
            
            // Keep only last 8 results
            while (history.children.length > 8) {
                history.removeChild(history.lastChild);
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPumpGame();
});