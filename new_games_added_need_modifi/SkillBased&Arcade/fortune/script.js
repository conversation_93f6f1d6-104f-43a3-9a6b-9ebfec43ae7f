// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadFortuneGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <h4 class="text-xl font-bold mb-4 text-yellow-400">COMIC FORTUNE</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">CARD PRICE</label>
                                <select id="fortunePrice" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="5">$5 Card</option>
                                    <option value="10" selected>$10 Card</option>
                                    <option value="25">$25 Card</option>
                                    <option value="50">$50 Card</option>
                                    <option value="100">$100 Card</option>
                                </select>
                            </div>
                            
                            <button id="buyFortuneCard" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                BUY CARD
                            </button>
                            
                            <button id="revealAllFortune" class="w-full py-3 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white mb-4" disabled>
                                REVEAL ALL
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Win</div>
                                <div id="fortuneWin" class="text-2xl font-bold text-green-400 neon-glow">$0</div>
                            </div>
                        </div>
                        
                        <!-- Prize Table -->
                        <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-yellow-400">PRIZE TABLE</h5>
                            <div id="fortunePrizeTable" class="text-sm space-y-1">
                                <!-- Prize table will be generated -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Scratch Card -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                            <div id="fortuneCard" class="relative bg-gradient-to-br from-yellow-400 to-orange-600 rounded-xl p-4 h-96">
                                <div class="text-center text-black font-bold text-xl mb-4">CYBER FORTUNE CARD</div>
                                <div id="fortuneGrid" class="grid grid-cols-3 gap-4 h-64">
                                    <!-- Scratch areas will be generated here -->
                                </div>
                                <div class="text-center text-black font-bold text-sm mt-2">
                                    Match 3 symbols to win!
                                </div>
                            </div>
                            <div id="fortuneStatus" class="text-center mt-4 text-lg font-semibold">Buy a card to start scratching</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeFortune();
        }
        
        let fortuneGame = {
            isPlaying: false,
            cardPrice: 10,
            symbols: [],
            revealed: [],
            winAmount: 0
        };
        
        function initializeFortune() {
            document.getElementById('buyFortuneCard').addEventListener('click', buyFortuneCard);
            document.getElementById('revealAllFortune').addEventListener('click', revealAllFortune);
            document.getElementById('fortunePrice').addEventListener('change', updateFortunePriceTable);
            
            updateFortunePriceTable();
        }
        
        function updateFortunePriceTable() {
            fortuneGame.cardPrice = parseInt(document.getElementById('fortunePrice').value);
            
            const prizeTable = document.getElementById('fortunePrizeTable');
            const prizes = getFortuneePrizes(fortuneGame.cardPrice);
            
            prizeTable.innerHTML = '';
            Object.entries(prizes).forEach(([symbol, prize]) => {
                const row = document.createElement('div');
                row.className = 'flex justify-between items-center';
                row.innerHTML = `
                    <span>${symbol} x3:</span>
                    <span class="text-yellow-400">$${prize}</span>
                `;
                prizeTable.appendChild(row);
            });
        }
        
        function getFortuneePrizes(cardPrice) {
            const multipliers = {
                '💎': 100,
                '👑': 50,
                '⚡': 25,
                '🔥': 10,
                '⭐': 5,
                '🎯': 2,
                '🍀': 1
            };
            
            const prizes = {};
            Object.entries(multipliers).forEach(([symbol, mult]) => {
                prizes[symbol] = cardPrice * mult;
            });
            
            return prizes;
        }
        
        function buyFortuneCard() {
            if (fortuneGame.cardPrice > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct card price
            balance -= fortuneGame.cardPrice;
            updateBalance();
            
            // Initialize game
            fortuneGame.isPlaying = true;
            fortuneGame.symbols = generateFortuneSymbols();
            fortuneGame.revealed = Array(9).fill(false);
            fortuneGame.winAmount = 0;
            
            // Update UI
            document.getElementById('buyFortuneCard').disabled = true;
            document.getElementById('revealAllFortune').disabled = false;
            document.getElementById('fortuneStatus').textContent = 'Click areas to scratch and reveal symbols!';
            document.getElementById('fortuneWin').textContent = '0 GA';
            
            generateScratchAreas();
        }
        
        function generateFortuneSymbols() {
            const symbols = ['💎', '👑', '⚡', '🔥', '⭐', '🎯', '🍀'];
            const cardSymbols = [];
            
            // Determine if this is a winning card (20% chance)
            const isWinning = Math.random() < 0.2;
            
            if (isWinning) {
                // Create a winning combination
                const winningSymbol = symbols[Math.floor(Math.random() * symbols.length)];
                const positions = [];
                
                // Place 3 of the winning symbol
                while (positions.length < 3) {
                    const pos = Math.floor(Math.random() * 9);
                    if (!positions.includes(pos)) {
                        positions.push(pos);
                    }
                }
                
                // Fill the rest randomly
                for (let i = 0; i < 9; i++) {
                    if (positions.includes(i)) {
                        cardSymbols[i] = winningSymbol;
                    } else {
                        cardSymbols[i] = symbols[Math.floor(Math.random() * symbols.length)];
                    }
                }
            } else {
                // Create a non-winning card
                for (let i = 0; i < 9; i++) {
                    cardSymbols[i] = symbols[Math.floor(Math.random() * symbols.length)];
                }
                
                // Ensure no 3 matches
                const symbolCounts = {};
                cardSymbols.forEach(symbol => {
                    symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
                });
                
                // If any symbol appears 3+ times, replace some
                Object.entries(symbolCounts).forEach(([symbol, count]) => {
                    if (count >= 3) {
                        let replaced = 0;
                        for (let i = 0; i < cardSymbols.length && replaced < count - 2; i++) {
                            if (cardSymbols[i] === symbol) {
                                cardSymbols[i] = symbols.find(s => s !== symbol);
                                replaced++;
                            }
                        }
                    }
                });
            }
            
            return cardSymbols;
        }
        
        function generateScratchAreas() {
            const grid = document.getElementById('fortuneGrid');
            grid.innerHTML = '';
            
            for (let i = 0; i < 9; i++) {
                const area = document.createElement('div');
                area.className = 'relative bg-gray-600 rounded-lg cursor-pointer overflow-hidden';
                area.dataset.index = i;
                
                // Covered layer
                const cover = document.createElement('div');
                cover.className = 'absolute inset-0 bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold scratch-cover';
                cover.textContent = 'SCRATCH';
                area.appendChild(cover);
                
                // Hidden symbol
                const symbol = document.createElement('div');
                symbol.className = 'absolute inset-0 flex items-center justify-center text-4xl font-bold hidden';
                symbol.textContent = fortuneGame.symbols[i];
                area.appendChild(symbol);
                
                area.addEventListener('click', () => scratchArea(i));
                grid.appendChild(area);
            }
        }
        
        function scratchArea(index) {
            if (!fortuneGame.isPlaying || fortuneGame.revealed[index]) return;
            
            fortuneGame.revealed[index] = true;
            
            const area = document.querySelector(`[data-index="${index}"]`);
            const cover = area.querySelector('.scratch-cover');
            const symbol = area.querySelector('div:last-child');
            
            // Animate scratch reveal
            cover.style.transition = 'opacity 0.3s ease';
            cover.style.opacity = '0';
            symbol.classList.remove('hidden');
            
            setTimeout(() => {
                cover.remove();
            }, 300);
            
            // Check for wins
            checkFortuneWin();
        }
        
        function revealAllFortune() {
            if (!fortuneGame.isPlaying) return;
            
            for (let i = 0; i < 9; i++) {
                if (!fortuneGame.revealed[i]) {
                    scratchArea(i);
                }
            }
        }
        
        function checkFortuneWin() {
            // Count symbol occurrences in revealed areas
            const symbolCounts = {};
            fortuneGame.revealed.forEach((revealed, index) => {
                if (revealed) {
                    const symbol = fortuneGame.symbols[index];
                    symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
                }
            });
            
            // Check for 3 matches
            let winningSymbol = null;
            Object.entries(symbolCounts).forEach(([symbol, count]) => {
                if (count >= 3) {
                    winningSymbol = symbol;
                }
            });
            
            if (winningSymbol) {
                const prizes = getFortuneePrizes(fortuneGame.cardPrice);
                fortuneGame.winAmount = prizes[winningSymbol];
                
                balance += fortuneGame.winAmount;
                updateBalance();
                
                document.getElementById('fortuneWin').textContent = '$' + fortuneGame.winAmount;
                document.getElementById('fortuneStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Winner! 3 ${winningSymbol} = $${fortuneGame.winAmount}</span>`;
            }
            
            // Check if all revealed
            if (fortuneGame.revealed.every(r => r)) {
                endFortuneGame();
            }
        }
        
        function endFortuneGame() {
            fortuneGame.isPlaying = false;
            
            // Update UI
            document.getElementById('buyFortuneCard').disabled = false;
            document.getElementById('revealAllFortune').disabled = true;
            
            if (fortuneGame.winAmount === 0) {
                document.getElementById('fortuneStatus').innerHTML = 
                    `<span class="text-red-400">Better luck next time!</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                document.getElementById('fortuneStatus').textContent = 'Buy a card to start scratching';
                document.getElementById('fortuneWin').textContent = '0 GA';
            }, 3000);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadFortuneGame();
});