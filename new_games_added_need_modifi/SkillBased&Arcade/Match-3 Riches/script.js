// Game state
let balance = 1000;

// Match-3 Riches game state
let match3Game = {
    isPlaying: false,
    gamePhase: 'waiting', // waiting, playing, finished
    grid: [],
    gridSize: 8,
    selectedCell: null,
    score: 0,
    moves: 0,
    maxMoves: 20,
    timeLimit: 120, // 2 minutes
    timeLeft: 120,
    timer: null,
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'classic',
    targetScore: 1000,
    combo: 0,
    maxCombo: 0,
    specialGems: 0,
    cascades: 0,
    perfectMatches: 0,
    totalMatches: 0,
    gemTypes: 6,
    powerUps: {
        bomb: 0,
        lightning: 0,
        rainbow: 0
    },
    multiplier: 1.0,
    canvas: null,
    ctx: null,
    animating: false,
    particles: [],
    level: 1,
    experience: 0
};

// Difficulty settings with very low win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        gridSize: 6,
        gemTypes: 5,
        timeLimit: 180,
        maxMoves: 25,
        targetScore: 800,
        winRate: 0.08,
        multiplier: 0.8,
        description: 'Beginner friendly'
    },
    {
        name: 'normal',
        gridSize: 8,
        gemTypes: 6,
        timeLimit: 120,
        maxMoves: 20,
        targetScore: 1200,
        winRate: 0.06,
        multiplier: 1.0,
        description: 'Standard challenge'
    },
    {
        name: 'hard',
        gridSize: 10,
        gemTypes: 7,
        timeLimit: 90,
        maxMoves: 18,
        targetScore: 1800,
        winRate: 0.04,
        multiplier: 1.5,
        description: 'Expert level'
    },
    {
        name: 'expert',
        gridSize: 12,
        gemTypes: 8,
        timeLimit: 75,
        maxMoves: 15,
        targetScore: 2500,
        winRate: 0.03,
        multiplier: 2.0,
        description: 'Master jeweler'
    },
    {
        name: 'legendary',
        gridSize: 14,
        gemTypes: 9,
        timeLimit: 60,
        maxMoves: 12,
        targetScore: 3500,
        winRate: 0.02,
        multiplier: 3.0,
        description: 'Legendary riches'
    }
];

// Game modes
const GAME_MODES = [
    {
        name: 'classic',
        description: 'Reach target score',
        scoreMultiplier: 1.0,
        timeBonus: true
    },
    {
        name: 'blitz',
        description: 'Speed matching',
        scoreMultiplier: 1.2,
        timeBonus: true
    },
    {
        name: 'puzzle',
        description: 'Limited moves',
        scoreMultiplier: 1.5,
        timeBonus: false
    },
    {
        name: 'endless',
        description: 'Survival mode',
        scoreMultiplier: 0.8,
        timeBonus: false
    }
];

// Gem types with colors and values
const GEM_TYPES = [
    { id: 0, color: '#FF4444', name: 'Ruby', value: 10 },
    { id: 1, color: '#44FF44', name: 'Emerald', value: 15 },
    { id: 2, color: '#4444FF', name: 'Sapphire', value: 20 },
    { id: 3, color: '#FFFF44', name: 'Topaz', value: 25 },
    { id: 4, color: '#FF44FF', name: 'Amethyst', value: 30 },
    { id: 5, color: '#44FFFF', name: 'Aquamarine', value: 35 },
    { id: 6, color: '#FF8844', name: 'Citrine', value: 40 },
    { id: 7, color: '#8844FF', name: 'Tanzanite', value: 45 },
    { id: 8, color: '#FFFFFF', name: 'Diamond', value: 50 }
];

function loadMatch3RichesGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Board -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">💎 MATCH-3 RICHES 💎</h4>
                    
                    <!-- Game Canvas -->
                    <div class="relative">
                        <canvas id="match3Canvas" width="480" height="480" 
                                class="w-full max-w-lg mx-auto border border-purple-500/30 rounded-lg bg-black/50 cursor-pointer"></canvas>
                        
                        <!-- Game Overlay -->
                        <div id="gameOverlay" class="absolute inset-0 flex items-center justify-center bg-black/70 rounded-lg hidden">
                            <div class="text-center">
                                <div id="overlayTitle" class="text-3xl font-bold text-purple-400 mb-4"></div>
                                <div id="overlayMessage" class="text-lg text-white mb-4"></div>
                                <button id="overlayButton" class="cyber-button px-6 py-3 rounded-lg font-bold">
                                    CONTINUE
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Game Stats Bar -->
                    <div class="mt-4 grid grid-cols-4 gap-4 text-center">
                        <div class="bg-black/50 p-3 rounded border border-blue-500/30">
                            <div class="text-xs text-blue-400">SCORE</div>
                            <div id="currentScore" class="text-lg font-bold text-blue-300">0</div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-green-500/30">
                            <div class="text-xs text-green-400">MOVES</div>
                            <div id="movesLeft" class="text-lg font-bold text-green-300">20</div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-yellow-500/30">
                            <div class="text-xs text-yellow-400">TIME</div>
                            <div id="timeLeft" class="text-lg font-bold text-yellow-300">2:00</div>
                        </div>
                        <div class="bg-black/50 p-3 rounded border border-purple-500/30">
                            <div class="text-xs text-purple-400">COMBO</div>
                            <div id="currentCombo" class="text-lg font-bold text-purple-300">0x</div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-400 mb-1">
                            <span>Progress to Target</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="bg-gray-700 rounded-full h-3">
                            <div id="progressBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-full rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Game Controls -->
            <div class="space-y-6">
                <!-- Game Setup -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Game Setup</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Bet Amount</label>
                            <select id="match3Bet" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="25">25 GA - Casual</option>
                                <option value="50">50 GA - Standard</option>
                                <option value="100">100 GA - High Stakes</option>
                                <option value="250">250 GA - Expert</option>
                                <option value="500">500 GA - Master</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Difficulty</label>
                            <select id="match3Difficulty" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="easy">Easy (8% win rate)</option>
                                <option value="normal">Normal (6% win rate)</option>
                                <option value="hard">Hard (4% win rate)</option>
                                <option value="expert">Expert (3% win rate)</option>
                                <option value="legendary">Legendary (2% win rate)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Game Mode</label>
                            <select id="gameMode" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="classic">Classic Mode</option>
                                <option value="blitz">Blitz Mode</option>
                                <option value="puzzle">Puzzle Mode</option>
                                <option value="endless">Endless Mode</option>
                            </select>
                        </div>
                        
                        <button id="startMatch3" class="w-full cyber-button py-3 rounded-lg font-bold">
                            START GAME
                        </button>
                    </div>
                </div>
                
                <!-- Power-ups -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Power-ups</h3>
                    <div class="grid grid-cols-3 gap-2">
                        <button id="useBomb" class="cyber-button text-xs py-2 px-1 rounded disabled:opacity-50" disabled>
                            💣 <span id="bombCount">0</span>
                        </button>
                        <button id="useLightning" class="cyber-button text-xs py-2 px-1 rounded disabled:opacity-50" disabled>
                            ⚡ <span id="lightningCount">0</span>
                        </button>
                        <button id="useRainbow" class="cyber-button text-xs py-2 px-1 rounded disabled:opacity-50" disabled>
                            🌈 <span id="rainbowCount">0</span>
                        </button>
                    </div>
                </div>
                
                <!-- Player Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Player Stats</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-400" id="playerLevel">1</div>
                            <div class="text-gray-300">Level</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-400" id="maxComboStat">0</div>
                            <div class="text-gray-300">Max Combo</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-400" id="perfectMatches">0</div>
                            <div class="text-gray-300">Perfect Matches</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-400" id="specialGemsStat">0</div>
                            <div class="text-gray-300">Special Gems</div>
                        </div>
                    </div>
                </div>
                
                <!-- Current Game Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Current Game</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Target Score:</span>
                            <span id="targetScore" class="text-purple-400">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Multiplier:</span>
                            <span id="currentMultiplier" class="text-green-400">1.0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Cascades:</span>
                            <span id="cascadeCount" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Win Chance:</span>
                            <span id="winChance" class="text-yellow-400">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- Game Result -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">Result</h3>
                    <div id="match3Result" class="text-center text-lg font-semibold text-cyan-400 min-h-12">
                        Select difficulty and start matching gems!
                    </div>
                    <div id="winAmount" class="text-center text-2xl font-bold text-green-400 mt-2 min-h-8"></div>
                </div>
                
                <!-- Instructions -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">How to Play</h3>
                    <div class="text-sm text-gray-300 space-y-2">
                        <p>• Click and drag to swap adjacent gems</p>
                        <p>• Match 3+ gems of the same color</p>
                        <p>• Create combos for higher scores</p>
                        <p>• Use power-ups strategically</p>
                        <p>• Reach target score to win prizes</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupMatch3Game();
}

function setupMatch3Game() {
    match3Game.canvas = document.getElementById('match3Canvas');
    match3Game.ctx = match3Game.canvas.getContext('2d');
    
    // Event listeners
    document.getElementById('startMatch3').addEventListener('click', startMatch3Game);
    document.getElementById('match3Difficulty').addEventListener('change', updateDifficultySettings);
    document.getElementById('gameMode').addEventListener('change', updateGameMode);
    document.getElementById('overlayButton').addEventListener('click', hideGameOverlay);
    
    // Canvas events
    match3Game.canvas.addEventListener('click', handleCanvasClick);
    match3Game.canvas.addEventListener('mousedown', handleMouseDown);
    match3Game.canvas.addEventListener('mousemove', handleMouseMove);
    match3Game.canvas.addEventListener('mouseup', handleMouseUp);
    
    // Power-up buttons
    document.getElementById('useBomb').addEventListener('click', () => usePowerUp('bomb'));
    document.getElementById('useLightning').addEventListener('click', () => usePowerUp('lightning'));
    document.getElementById('useRainbow').addEventListener('click', () => usePowerUp('rainbow'));
    
    updateDifficultySettings();
    updateMatch3Display();
    drawMatch3Grid();
}

function updateDifficultySettings() {
    const difficulty = document.getElementById('match3Difficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    match3Game.difficulty = difficulty;
    match3Game.gridSize = diffData.gridSize;
    match3Game.gemTypes = diffData.gemTypes;
    match3Game.timeLimit = diffData.timeLimit;
    match3Game.maxMoves = diffData.maxMoves;
    match3Game.targetScore = diffData.targetScore;
    
    document.getElementById('targetScore').textContent = diffData.targetScore;
    document.getElementById('winChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    
    if (!match3Game.isPlaying) {
        initializeGrid();
        drawMatch3Grid();
    }
}

function updateGameMode() {
    match3Game.gameMode = document.getElementById('gameMode').value;
}

function startMatch3Game() {
    const betAmount = parseInt(document.getElementById('match3Bet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    match3Game.isPlaying = true;
    match3Game.gamePhase = 'playing';
    match3Game.betAmount = betAmount;
    match3Game.score = 0;
    match3Game.moves = 0;
    match3Game.timeLeft = match3Game.timeLimit;
    match3Game.combo = 0;
    match3Game.maxCombo = 0;
    match3Game.cascades = 0;
    match3Game.perfectMatches = 0;
    match3Game.totalMatches = 0;
    match3Game.specialGems = 0;
    match3Game.multiplier = 1.0;
    
    document.getElementById('startMatch3').disabled = true;
    
    // Initialize grid
    initializeGrid();
    
    // Start timer
    match3Game.timer = setInterval(() => {
        match3Game.timeLeft--;
        if (match3Game.timeLeft <= 0) {
            endMatch3Game('Time\'s up!');
        }
        updateMatch3Display();
    }, 1000);
    
    updateMatch3Display();
    drawMatch3Grid();
}

function initializeGrid() {
    match3Game.grid = [];
    
    // Create grid with random gems (ensuring no initial matches)
    for (let row = 0; row < match3Game.gridSize; row++) {
        match3Game.grid[row] = [];
        for (let col = 0; col < match3Game.gridSize; col++) {
            let gemType;
            let attempts = 0;
            
            do {
                gemType = Math.floor(Math.random() * match3Game.gemTypes);
                attempts++;
            } while (attempts < 10 && wouldCreateMatch(row, col, gemType));
            
            match3Game.grid[row][col] = {
                type: gemType,
                special: false,
                selected: false,
                falling: false,
                matched: false
            };
        }
    }
}

function wouldCreateMatch(row, col, gemType) {
    // Check horizontal match
    let horizontalCount = 1;
    
    // Check left
    for (let c = col - 1; c >= 0 && match3Game.grid[row][c] && match3Game.grid[row][c].type === gemType; c--) {
        horizontalCount++;
    }
    
    // Check right
    for (let c = col + 1; c < match3Game.gridSize && match3Game.grid[row][c] && match3Game.grid[row][c].type === gemType; c++) {
        horizontalCount++;
    }
    
    if (horizontalCount >= 3) return true;
    
    // Check vertical match
    let verticalCount = 1;
    
    // Check up
    for (let r = row - 1; r >= 0 && match3Game.grid[r][col] && match3Game.grid[r][col].type === gemType; r--) {
        verticalCount++;
    }
    
    // Check down
    for (let r = row + 1; r < match3Game.gridSize && match3Game.grid[r][col] && match3Game.grid[r][col].type === gemType; r++) {
        verticalCount++;
    }
    
    return verticalCount >= 3;
}

function handleCanvasClick(event) {
    if (!match3Game.isPlaying || match3Game.animating) return;
    
    const rect = match3Game.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const cellSize = match3Game.canvas.width / match3Game.gridSize;
    const col = Math.floor(x / cellSize);
    const row = Math.floor(y / cellSize);
    
    if (row >= 0 && row < match3Game.gridSize && col >= 0 && col < match3Game.gridSize) {
        selectCell(row, col);
    }
}

function handleMouseDown(event) {
    if (!match3Game.isPlaying || match3Game.animating) return;
    
    const rect = match3Game.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const cellSize = match3Game.canvas.width / match3Game.gridSize;
    const col = Math.floor(x / cellSize);
    const row = Math.floor(y / cellSize);
    
    if (row >= 0 && row < match3Game.gridSize && col >= 0 && col < match3Game.gridSize) {
        match3Game.dragStart = { row, col };
    }
}

function handleMouseMove(event) {
    // Handle drag preview if needed
}

function handleMouseUp(event) {
    if (!match3Game.isPlaying || match3Game.animating || !match3Game.dragStart) return;
    
    const rect = match3Game.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const cellSize = match3Game.canvas.width / match3Game.gridSize;
    const col = Math.floor(x / cellSize);
    const row = Math.floor(y / cellSize);
    
    if (row >= 0 && row < match3Game.gridSize && col >= 0 && col < match3Game.gridSize) {
        const startRow = match3Game.dragStart.row;
        const startCol = match3Game.dragStart.col;
        
        // Check if adjacent
        const rowDiff = Math.abs(row - startRow);
        const colDiff = Math.abs(col - startCol);
        
        if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
            attemptSwap(startRow, startCol, row, col);
        }
    }
    
    match3Game.dragStart = null;
}

function selectCell(row, col) {
    if (match3Game.selectedCell) {
        const prevRow = match3Game.selectedCell.row;
        const prevCol = match3Game.selectedCell.col;
        
        // Deselect previous cell
        match3Game.grid[prevRow][prevCol].selected = false;
        
        // Check if clicking same cell
        if (prevRow === row && prevCol === col) {
            match3Game.selectedCell = null;
            drawMatch3Grid();
            return;
        }
        
        // Check if adjacent
        const rowDiff = Math.abs(row - prevRow);
        const colDiff = Math.abs(col - prevCol);
        
        if ((rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1)) {
            attemptSwap(prevRow, prevCol, row, col);
            match3Game.selectedCell = null;
        } else {
            // Select new cell
            match3Game.selectedCell = { row, col };
            match3Game.grid[row][col].selected = true;
        }
    } else {
        // Select cell
        match3Game.selectedCell = { row, col };
        match3Game.grid[row][col].selected = true;
    }
    
    drawMatch3Grid();
}

function attemptSwap(row1, col1, row2, col2) {
    // Temporarily swap gems
    const temp = match3Game.grid[row1][col1];
    match3Game.grid[row1][col1] = match3Game.grid[row2][col2];
    match3Game.grid[row2][col2] = temp;
    
    // Check for matches
    const matches = findMatches();
    
    if (matches.length > 0) {
        // Valid move
        match3Game.moves++;
        processMatches(matches);
        
        if (match3Game.moves >= match3Game.maxMoves) {
            setTimeout(() => {
                endMatch3Game('No moves left!');
            }, 1000);
        }
    } else {
        // Invalid move - swap back
        const temp2 = match3Game.grid[row1][col1];
        match3Game.grid[row1][col1] = match3Game.grid[row2][col2];
        match3Game.grid[row2][col2] = temp2;
    }
    
    // Clear selections
    if (match3Game.selectedCell) {
        match3Game.grid[match3Game.selectedCell.row][match3Game.selectedCell.col].selected = false;
        match3Game.selectedCell = null;
    }
    
    updateMatch3Display();
    drawMatch3Grid();
}

function findMatches() {
    const matches = [];
    const visited = new Set();
    
    // Find horizontal matches
    for (let row = 0; row < match3Game.gridSize; row++) {
        let count = 1;
        let currentType = match3Game.grid[row][0].type;
        
        for (let col = 1; col < match3Game.gridSize; col++) {
            if (match3Game.grid[row][col].type === currentType) {
                count++;
            } else {
                if (count >= 3) {
                    for (let c = col - count; c < col; c++) {
                        const key = `${row},${c}`;
                        if (!visited.has(key)) {
                            matches.push({ row, col: c });
                            visited.add(key);
                        }
                    }
                }
                count = 1;
                currentType = match3Game.grid[row][col].type;
            }
        }
        
        // Check end of row
        if (count >= 3) {
            for (let c = match3Game.gridSize - count; c < match3Game.gridSize; c++) {
                const key = `${row},${c}`;
                if (!visited.has(key)) {
                    matches.push({ row, col: c });
                    visited.add(key);
                }
            }
        }
    }
    
    // Find vertical matches
    for (let col = 0; col < match3Game.gridSize; col++) {
        let count = 1;
        let currentType = match3Game.grid[0][col].type;
        
        for (let row = 1; row < match3Game.gridSize; row++) {
            if (match3Game.grid[row][col].type === currentType) {
                count++;
            } else {
                if (count >= 3) {
                    for (let r = row - count; r < row; r++) {
                        const key = `${r},${col}`;
                        if (!visited.has(key)) {
                            matches.push({ row: r, col });
                            visited.add(key);
                        }
                    }
                }
                count = 1;
                currentType = match3Game.grid[row][col].type;
            }
        }
        
        // Check end of column
        if (count >= 3) {
            for (let r = match3Game.gridSize - count; r < match3Game.gridSize; r++) {
                const key = `${r},${col}`;
                if (!visited.has(key)) {
                    matches.push({ row: r, col });
                    visited.add(key);
                }
            }
        }
    }
    
    return matches;
}

function processMatches(matches) {
    match3Game.animating = true;
    match3Game.totalMatches++;
    
    // Calculate score
    let matchScore = 0;
    const baseScore = 50;
    
    matches.forEach(match => {
        const gem = match3Game.grid[match.row][match.col];
        const gemValue = GEM_TYPES[gem.type].value;
        matchScore += baseScore + gemValue;
        
        // Mark for removal
        gem.matched = true;
        
        // Create particle effect
        createParticle(match.row, match.col, gem.type);
    });
    
    // Apply combo multiplier
    match3Game.combo++;
    match3Game.maxCombo = Math.max(match3Game.maxCombo, match3Game.combo);
    
    const comboMultiplier = 1 + (match3Game.combo - 1) * 0.2;
    matchScore = Math.floor(matchScore * comboMultiplier * match3Game.multiplier);
    
    match3Game.score += matchScore;
    
    // Check for perfect match (5+ gems)
    if (matches.length >= 5) {
        match3Game.perfectMatches++;
        match3Game.score += 500;
        
        // Award power-up
        const powerUpType = ['bomb', 'lightning', 'rainbow'][Math.floor(Math.random() * 3)];
        match3Game.powerUps[powerUpType]++;
    }
    
    // Check for special gems (4+ gems)
    if (matches.length >= 4) {
        match3Game.specialGems++;
        match3Game.multiplier += 0.1;
    }
    
    setTimeout(() => {
        removeMatches();
        dropGems();
        fillEmptySpaces();
        
        // Check for cascades
        const newMatches = findMatches();
        if (newMatches.length > 0) {
            match3Game.cascades++;
            processMatches(newMatches);
        } else {
            match3Game.combo = 0;
            match3Game.animating = false;
            
            // Check win condition
            if (match3Game.score >= match3Game.targetScore) {
                endMatch3Game('Target reached!');
            }
        }
        
        updateMatch3Display();
        drawMatch3Grid();
    }, 500);
}

function removeMatches() {
    for (let row = 0; row < match3Game.gridSize; row++) {
        for (let col = 0; col < match3Game.gridSize; col++) {
            if (match3Game.grid[row][col].matched) {
                match3Game.grid[row][col] = null;
            }
        }
    }
}

function dropGems() {
    for (let col = 0; col < match3Game.gridSize; col++) {
        let writePos = match3Game.gridSize - 1;
        
        for (let row = match3Game.gridSize - 1; row >= 0; row--) {
            if (match3Game.grid[row][col] !== null) {
                if (writePos !== row) {
                    match3Game.grid[writePos][col] = match3Game.grid[row][col];
                    match3Game.grid[row][col] = null;
                }
                writePos--;
            }
        }
    }
}

function fillEmptySpaces() {
    for (let col = 0; col < match3Game.gridSize; col++) {
        for (let row = 0; row < match3Game.gridSize; row++) {
            if (match3Game.grid[row][col] === null) {
                match3Game.grid[row][col] = {
                    type: Math.floor(Math.random() * match3Game.gemTypes),
                    special: false,
                    selected: false,
                    falling: true,
                    matched: false
                };
            }
        }
    }
}

function createParticle(row, col, gemType) {
    const cellSize = match3Game.canvas.width / match3Game.gridSize;
    const x = col * cellSize + cellSize / 2;
    const y = row * cellSize + cellSize / 2;
    
    match3Game.particles.push({
        x: x,
        y: y,
        vx: (Math.random() - 0.5) * 4,
        vy: (Math.random() - 0.5) * 4,
        color: GEM_TYPES[gemType].color,
        life: 30,
        maxLife: 30
    });
}

function usePowerUp(type) {
    if (!match3Game.isPlaying || match3Game.powerUps[type] <= 0) return;
    
    match3Game.powerUps[type]--;
    
    switch (type) {
        case 'bomb':
            // Remove 3x3 area
            const bombRow = Math.floor(match3Game.gridSize / 2);
            const bombCol = Math.floor(match3Game.gridSize / 2);
            
            for (let r = Math.max(0, bombRow - 1); r <= Math.min(match3Game.gridSize - 1, bombRow + 1); r++) {
                for (let c = Math.max(0, bombCol - 1); c <= Math.min(match3Game.gridSize - 1, bombCol + 1); c++) {
                    match3Game.grid[r][c].matched = true;
                }
            }
            
            processMatches([]);
            break;
            
        case 'lightning':
            // Remove random row and column
            const lightningRow = Math.floor(Math.random() * match3Game.gridSize);
            const lightningCol = Math.floor(Math.random() * match3Game.gridSize);
            
            for (let c = 0; c < match3Game.gridSize; c++) {
                match3Game.grid[lightningRow][c].matched = true;
            }
            for (let r = 0; r < match3Game.gridSize; r++) {
                match3Game.grid[r][lightningCol].matched = true;
            }
            
            processMatches([]);
            break;
            
        case 'rainbow':
            // Remove all gems of one type
            const targetType = Math.floor(Math.random() * match3Game.gemTypes);
            
            for (let row = 0; row < match3Game.gridSize; row++) {
                for (let col = 0; col < match3Game.gridSize; col++) {
                    if (match3Game.grid[row][col].type === targetType) {
                        match3Game.grid[row][col].matched = true;
                    }
                }
            }
            
            processMatches([]);
            break;
    }
    
    updatePowerUpDisplay();
}

function endMatch3Game(reason) {
    match3Game.isPlaying = false;
    match3Game.gamePhase = 'finished';
    
    if (match3Game.timer) {
        clearInterval(match3Game.timer);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === match3Game.difficulty);
    const modeData = GAME_MODES.find(m => m.name === match3Game.gameMode);
    
    // Determine if player won (extremely strict requirements)
    const scoreRatio = match3Game.score / match3Game.targetScore;
    const baseWinChance = diffData.winRate;
    
    // Additional requirements for winning
    const minCombo = 5; // Need at least 5x combo
    const minCascades = 3; // Need at least 3 cascades
    const minPerfectMatches = 2; // Need at least 2 perfect matches
    
    const meetsRequirements = match3Game.maxCombo >= minCombo && 
                             match3Game.cascades >= minCascades && 
                             match3Game.perfectMatches >= minPerfectMatches &&
                             scoreRatio >= 1.0;
    
    const finalWinChance = meetsRequirements ? baseWinChance * scoreRatio : 0;
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier;
        const comboBonus = match3Game.maxCombo * 0.1;
        const cascadeBonus = match3Game.cascades * 0.05;
        const perfectBonus = match3Game.perfectMatches * 0.2;
        const timeBonus = modeData.timeBonus ? (match3Game.timeLeft / match3Game.timeLimit) * 0.3 : 0;
        
        const totalMultiplier = baseMultiplier * (1 + comboBonus + cascadeBonus + perfectBonus + timeBonus);
        const winnings = Math.floor(match3Game.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        // Update stats
        match3Game.level++;
        match3Game.experience += match3Game.score;
        
        document.getElementById('match3Result').innerHTML = 
            `<span class="text-green-400 neon-glow">💎 RICHES UNLOCKED! 💎</span>`;
        document.getElementById('winAmount').textContent = `+${winnings} GA`;
    } else {
        document.getElementById('match3Result').innerHTML = 
            `<span class="text-red-400">💔 ${reason} 💔</span>`;
        document.getElementById('winAmount').textContent = 
            meetsRequirements ? 'Close! Try again!' : `Need ${minCombo}x combo, ${minCascades} cascades, ${minPerfectMatches} perfect matches!`;
    }
    
    updateMatch3Display();
    
    // Reset controls
    setTimeout(() => {
        document.getElementById('startMatch3').disabled = false;
        document.getElementById('match3Result').textContent = 'Select difficulty and start matching gems!';
        document.getElementById('winAmount').textContent = '';
        
        // Reset game state
        match3Game.score = 0;
        match3Game.moves = 0;
        match3Game.timeLeft = match3Game.timeLimit;
        match3Game.combo = 0;
        match3Game.cascades = 0;
        match3Game.perfectMatches = 0;
        match3Game.totalMatches = 0;
        match3Game.specialGems = 0;
        match3Game.multiplier = 1.0;
        
        updateMatch3Display();
        initializeGrid();
        drawMatch3Grid();
    }, 8000);
}

function drawMatch3Grid() {
    const ctx = match3Game.ctx;
    const canvas = match3Game.canvas;
    const cellSize = canvas.width / match3Game.gridSize;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw grid
    for (let row = 0; row < match3Game.gridSize; row++) {
        for (let col = 0; col < match3Game.gridSize; col++) {
            const x = col * cellSize;
            const y = row * cellSize;
            
            // Draw cell background
            ctx.fillStyle = '#16213e';
            ctx.fillRect(x + 1, y + 1, cellSize - 2, cellSize - 2);
            
            // Draw gem
            if (match3Game.grid[row] && match3Game.grid[row][col]) {
                const gem = match3Game.grid[row][col];
                const gemType = GEM_TYPES[gem.type];
                
                // Gem color
                ctx.fillStyle = gem.selected ? '#ffffff' : gemType.color;
                
                // Draw gem as circle
                ctx.beginPath();
                ctx.arc(x + cellSize / 2, y + cellSize / 2, cellSize * 0.35, 0, 2 * Math.PI);
                ctx.fill();
                
                // Add shine effect
                ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.beginPath();
                ctx.arc(x + cellSize / 2 - cellSize * 0.1, y + cellSize / 2 - cellSize * 0.1, cellSize * 0.15, 0, 2 * Math.PI);
                ctx.fill();
                
                // Draw selection border
                if (gem.selected) {
                    ctx.strokeStyle = '#ffff00';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(x + 2, y + 2, cellSize - 4, cellSize - 4);
                }
                
                // Draw special gem indicator
                if (gem.special) {
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('★', x + cellSize / 2, y + cellSize / 2 + 4);
                }
            }
        }
    }
    
    // Draw particles
    match3Game.particles = match3Game.particles.filter(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.life--;
        
        const alpha = particle.life / particle.maxLife;
        ctx.fillStyle = particle.color + Math.floor(alpha * 255).toString(16).padStart(2, '0');
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, 3, 0, 2 * Math.PI);
        ctx.fill();
        
        return particle.life > 0;
    });
    
    // Redraw if particles exist
    if (match3Game.particles.length > 0) {
        requestAnimationFrame(drawMatch3Grid);
    }
}

function updateMatch3Display() {
    document.getElementById('currentScore').textContent = match3Game.score;
    document.getElementById('movesLeft').textContent = Math.max(0, match3Game.maxMoves - match3Game.moves);
    document.getElementById('currentCombo').textContent = match3Game.combo + 'x';
    document.getElementById('currentMultiplier').textContent = match3Game.multiplier.toFixed(1) + 'x';
    document.getElementById('cascadeCount').textContent = match3Game.cascades;
    
    // Time display
    const minutes = Math.floor(match3Game.timeLeft / 60);
    const seconds = match3Game.timeLeft % 60;
    document.getElementById('timeLeft').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    // Progress bar
    const progress = Math.min(100, (match3Game.score / match3Game.targetScore) * 100);
    document.getElementById('progressBar').style.width = progress + '%';
    document.getElementById('progressPercent').textContent = Math.floor(progress) + '%';
    
    // Player stats
    document.getElementById('playerLevel').textContent = match3Game.level;
    document.getElementById('maxComboStat').textContent = match3Game.maxCombo;
    document.getElementById('perfectMatches').textContent = match3Game.perfectMatches;
    document.getElementById('specialGemsStat').textContent = match3Game.specialGems;
    
    updatePowerUpDisplay();
}

function updatePowerUpDisplay() {
    document.getElementById('bombCount').textContent = match3Game.powerUps.bomb;
    document.getElementById('lightningCount').textContent = match3Game.powerUps.lightning;
    document.getElementById('rainbowCount').textContent = match3Game.powerUps.rainbow;
    
    document.getElementById('useBomb').disabled = match3Game.powerUps.bomb <= 0 || !match3Game.isPlaying;
    document.getElementById('useLightning').disabled = match3Game.powerUps.lightning <= 0 || !match3Game.isPlaying;
    document.getElementById('useRainbow').disabled = match3Game.powerUps.rainbow <= 0 || !match3Game.isPlaying;
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMatch3RichesGame();
});