// Game state
let balance = 1000;

// Solitaire Stake game state
let solitaireGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'classic',
    gameMode: 'klondike',
    stakeLevel: 1,
    
    // Solitaire mechanics
    deck: [],
    tableau: [[], [], [], [], [], [], []],
    foundation: [[], [], [], []],
    waste: [],
    stock: [],
    selectedCard: null,
    selectedPile: null,
    
    // Stake features
    moveCount: 0,
    timeElapsed: 0,
    perfectMoves: 0,
    undoCount: 0,
    hintCount: 0,
    streakCount: 0,
    maxStreak: 0,
    
    // Scoring system
    score: 0,
    multiplier: 1.0,
    bonusPoints: 0,
    timeBonus: 0,
    efficiencyBonus: 0,
    
    // Performance tracking
    totalGames: 0,
    winStreak: 0,
    maxWinStreak: 0,
    averageMoves: 0,
    averageTime: 0,
    skillRating: 0,
    
    // Special features
    powerUps: [],
    achievements: [],
    cardReveals: 0,
    foundationMoves: 0,
    tableauMoves: 0,
    
    // Enhanced timing
    startTime: 0,
    moveHistory: [],
    undoStack: [],
    gameTimer: null,
    
    // Progressive features
    stakePoints: 0,
    stakeRank: 'Novice Player',
    unlockedModes: ['klondike'],
    masteryLevel: 1
};

// Solitaire difficulty settings
const SOLITAIRE_DIFFICULTIES = [
    {
        name: 'classic',
        description: 'Classic Solitaire (15% win rate)',
        winRate: 0.15,
        multiplier: 1.2,
        drawCount: 3,
        undoLimit: 10,
        hintLimit: 5,
        timeLimit: 600
    },
    {
        name: 'challenge',
        description: 'Challenge Mode (12% win rate)',
        winRate: 0.12,
        multiplier: 1.8,
        drawCount: 3,
        undoLimit: 5,
        hintLimit: 3,
        timeLimit: 480
    },
    {
        name: 'expert',
        description: 'Expert Stakes (8% win rate)',
        winRate: 0.08,
        multiplier: 2.5,
        drawCount: 1,
        undoLimit: 3,
        hintLimit: 1,
        timeLimit: 360
    },
    {
        name: 'master',
        description: 'Master Stakes (5% win rate)',
        winRate: 0.05,
        multiplier: 3.5,
        drawCount: 1,
        undoLimit: 1,
        hintLimit: 0,
        timeLimit: 300
    },
    {
        name: 'grandmaster',
        description: 'Grandmaster (3% win rate)',
        winRate: 0.03,
        multiplier: 5.0,
        drawCount: 1,
        undoLimit: 0,
        hintLimit: 0,
        timeLimit: 240
    },
    {
        name: 'legendary',
        description: 'Legendary Stakes (1% win rate)',
        winRate: 0.01,
        multiplier: 8.0,
        drawCount: 1,
        undoLimit: 0,
        hintLimit: 0,
        timeLimit: 180
    }
];

const SOLITAIRE_MODES = [
    {
        name: 'klondike',
        description: 'Classic Klondike',
        tableauSize: 7,
        foundationCount: 4,
        payoutMultiplier: 1.0
    },
    {
        name: 'spider',
        description: 'Spider Solitaire',
        tableauSize: 10,
        foundationCount: 8,
        payoutMultiplier: 1.5
    },
    {
        name: 'freecell',
        description: 'FreeCell',
        tableauSize: 8,
        foundationCount: 4,
        payoutMultiplier: 1.3
    },
    {
        name: 'pyramid',
        description: 'Pyramid Solitaire',
        tableauSize: 7,
        foundationCount: 1,
        payoutMultiplier: 1.8
    },
    {
        name: 'yukon',
        description: 'Yukon Solitaire',
        tableauSize: 7,
        foundationCount: 4,
        payoutMultiplier: 2.0
    },
    {
        name: 'golf',
        description: 'Golf Solitaire',
        tableauSize: 7,
        foundationCount: 1,
        payoutMultiplier: 1.4
    }
];

// Card definitions
const SUITS = ['hearts', 'diamonds', 'clubs', 'spades'];
const RANKS = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
const SUIT_SYMBOLS = {
    hearts: '♥',
    diamonds: '♦',
    clubs: '♣',
    spades: '♠'
};

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadSolitaireGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">♠️ SOLITAIRE STAKE ♠️</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 STAKE AMOUNT</label>
                        <input type="number" id="solitaireBet" value="50" min="25" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateSolitairePayout()">
                    </div>

                    <div class="grid grid-cols-1 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎯 DIFFICULTY</label>
                            <select id="solitaireDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSolitaireSettings()">
                                <option value="classic" selected>Classic (15%)</option>
                                <option value="challenge">Challenge (12%)</option>
                                <option value="expert">Expert Stakes (8%)</option>
                                <option value="master">Master Stakes (5%)</option>
                                <option value="grandmaster">Grandmaster (3%)</option>
                                <option value="legendary">Legendary (1%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🃏 GAME MODE</label>
                            <select id="solitaireMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSolitaireSettings()">
                                <option value="klondike" selected>Klondike</option>
                                <option value="spider">Spider</option>
                                <option value="freecell">FreeCell</option>
                                <option value="pyramid">Pyramid</option>
                                <option value="yukon">Yukon</option>
                                <option value="golf">Golf</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="solitaireWinRate">15%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Draw Count:</div>
                                <div class="text-yellow-400 font-bold" id="drawCount">3</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Undo Limit:</div>
                                <div class="text-blue-400 font-bold" id="undoLimit">10</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Limit:</div>
                                <div class="text-purple-400 font-bold" id="timeLimit">600s</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="solitairePotentialPayout">400 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startSolitaireGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        ♠️ DEAL CARDS ♠️
                    </button>

                    <div class="grid grid-cols-3 gap-2">
                        <button onclick="undoMove()" id="undoBtn"
                                class="cyber-button bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            ↶ UNDO
                        </button>
                        <button onclick="getHint()" id="hintBtn"
                                class="cyber-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            💡 HINT
                        </button>
                        <button onclick="surrenderGame()" id="surrenderBtn"
                                class="cyber-button bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            🏳️ FOLD
                        </button>
                    </div>
                </div>

                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 GAME STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Moves:</span>
                            <span class="text-green-400" id="moveCount">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Time:</span>
                            <span class="text-cyan-400" id="gameTime">0:00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Score:</span>
                            <span class="text-yellow-400" id="currentScore">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Multiplier:</span>
                            <span class="text-pink-400" id="scoreMultiplier">1.0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Reveals:</span>
                            <span class="text-purple-400" id="cardReveals">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Foundation:</span>
                            <span class="text-orange-400" id="foundationCards">0</span>
                        </div>
                    </div>
                </div>

                <!-- Solitaire Mastery -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 SOLITAIRE MASTERY</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Stake Rank:</span>
                            <span class="text-yellow-400" id="stakeRank">Novice Player</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="stakeProgressBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="stakeProgress">0 / 100 Points</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-xs mt-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Win Streak:</span>
                            <span class="text-green-400" id="winStreak">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total Games:</span>
                            <span class="text-blue-400" id="totalGames">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Board -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="gameTitle">Solitaire Stake</h5>
                        <p class="text-sm text-gray-400" id="gameDescription">Classic card staking with skill-based rewards</p>
                    </div>
                    
                    <!-- Stock and Waste -->
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex gap-4">
                            <div class="text-center">
                                <div class="text-xs text-gray-400 mb-1">STOCK</div>
                                <div id="stockPile" class="w-16 h-20 bg-gradient-to-br from-blue-600 to-purple-600 border border-blue-400 rounded-lg cursor-pointer flex items-center justify-center text-white font-bold"
                                     onclick="drawFromStock()">
                                    🂠
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="text-xs text-gray-400 mb-1">WASTE</div>
                                <div id="wastePile" class="w-16 h-20 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400">
                                    -
                                </div>
                            </div>
                        </div>
                        
                        <!-- Foundation Piles -->
                        <div class="flex gap-2">
                            <div class="text-center">
                                <div class="text-xs text-gray-400 mb-1">FOUNDATION</div>
                                <div class="flex gap-1">
                                    <div id="foundation-0" class="w-14 h-18 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400 text-xs foundation-pile" data-suit="hearts">♥</div>
                                    <div id="foundation-1" class="w-14 h-18 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400 text-xs foundation-pile" data-suit="diamonds">♦</div>
                                    <div id="foundation-2" class="w-14 h-18 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400 text-xs foundation-pile" data-suit="clubs">♣</div>
                                    <div id="foundation-3" class="w-14 h-18 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400 text-xs foundation-pile" data-suit="spades">♠</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tableau -->
                    <div class="grid grid-cols-7 gap-2" id="tableau">
                        <!-- Tableau columns will be generated here -->
                    </div>

                    <!-- Game Results -->
                    <div class="text-center mt-6">
                        <div id="solitaireResult" class="text-xl font-bold mb-2"></div>
                        <div id="solitaireWinAmount" class="text-lg"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Complete Overlay -->
        <div id="solitaireOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">♠️ GAME COMPLETE ♠️</h3>
                <div id="finalSolitaireResult" class="text-xl mb-4"></div>
                <div id="finalSolitaireStats" class="text-sm text-gray-400 mb-6"></div>
                <div id="solitaireAchievements" class="text-xs text-cyan-400 mb-4"></div>
                <button onclick="hideSolitaireOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    NEW GAME
                </button>
            </div>
        </div>
    `;

    updateSolitaireSettings();
    initializeTableau();
    
    // Add keyboard listeners
    document.addEventListener('keydown', handleSolitaireKeyPress);
}

function updateSolitaireSettings() {
    const difficulty = document.getElementById('solitaireDifficulty').value;
    const mode = document.getElementById('solitaireMode').value;
    
    const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === difficulty);
    const modeData = SOLITAIRE_MODES.find(m => m.name === mode);
    
    solitaireGame.difficulty = difficulty;
    solitaireGame.gameMode = mode;
    
    document.getElementById('solitaireWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('drawCount').textContent = diffData.drawCount;
    document.getElementById('undoLimit').textContent = diffData.undoLimit;
    document.getElementById('timeLimit').textContent = diffData.timeLimit + 's';
    
    updateSolitairePayout();
}

function updateSolitairePayout() {
    const betAmount = parseInt(document.getElementById('solitaireBet').value) || 50;
    const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === solitaireGame.difficulty);
    const modeData = SOLITAIRE_MODES.find(m => m.name === solitaireGame.gameMode);
    
    const totalMultiplier = diffData.multiplier * modeData.payoutMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 8);
    
    document.getElementById('solitairePotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function createDeck() {
    const deck = [];
    for (const suit of SUITS) {
        for (const rank of RANKS) {
            deck.push({
                suit: suit,
                rank: rank,
                value: rank === 'A' ? 1 : (rank === 'J' ? 11 : (rank === 'Q' ? 12 : (rank === 'K' ? 13 : parseInt(rank)))),
                color: suit === 'hearts' || suit === 'diamonds' ? 'red' : 'black',
                faceUp: false
            });
        }
    }
    return shuffleDeck(deck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function initializeTableau() {
    const tableauContainer = document.getElementById('tableau');
    tableauContainer.innerHTML = '';
    
    for (let col = 0; col < 7; col++) {
        const column = document.createElement('div');
        column.className = 'tableau-column min-h-24';
        column.id = `tableau-${col}`;
        column.dataset.column = col;
        
        const header = document.createElement('div');
        header.className = 'text-xs text-gray-400 text-center mb-2';
        header.textContent = `COL ${col + 1}`;
        column.appendChild(header);
        
        tableauContainer.appendChild(column);
    }
}

function startSolitaireGame() {
    const betAmount = parseInt(document.getElementById('solitaireBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance for Solitaire Stake!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    solitaireGame.isPlaying = true;
    solitaireGame.gamePhase = 'playing';
    solitaireGame.betAmount = betAmount;
    solitaireGame.moveCount = 0;
    solitaireGame.timeElapsed = 0;
    solitaireGame.score = 0;
    solitaireGame.multiplier = 1.0;
    solitaireGame.cardReveals = 0;
    solitaireGame.foundationMoves = 0;
    solitaireGame.undoCount = 0;
    solitaireGame.hintCount = 0;
    solitaireGame.moveHistory = [];
    solitaireGame.undoStack = [];
    
    // Initialize deck and deal
    solitaireGame.deck = createDeck();
    dealCards();
    
    solitaireGame.startTime = Date.now();
    startGameTimer();
    
    document.getElementById('undoBtn').disabled = false;
    document.getElementById('hintBtn').disabled = false;
    document.getElementById('surrenderBtn').disabled = false;
    
    hideSolitaireOverlay();
    updateSolitaireDisplay();
}

function dealCards() {
    // Clear all piles
    solitaireGame.tableau = [[], [], [], [], [], [], []];
    solitaireGame.foundation = [[], [], [], []];
    solitaireGame.waste = [];
    solitaireGame.stock = [...solitaireGame.deck];
    
    // Deal tableau cards (Klondike style)
    for (let col = 0; col < 7; col++) {
        for (let row = 0; row <= col; row++) {
            const card = solitaireGame.stock.pop();
            if (row === col) {
                card.faceUp = true;
            }
            solitaireGame.tableau[col].push(card);
        }
    }
    
    renderGame();
}

function renderGame() {
    renderTableau();
    renderStock();
    renderWaste();
    renderFoundation();
}

function renderTableau() {
    for (let col = 0; col < 7; col++) {
        const column = document.getElementById(`tableau-${col}`);
        const header = column.querySelector('div');
        column.innerHTML = '';
        column.appendChild(header);
        
        solitaireGame.tableau[col].forEach((card, index) => {
            const cardEl = createCardElement(card, `tableau-${col}-${index}`);
            cardEl.style.marginTop = index > 0 ? '-40px' : '0';
            cardEl.addEventListener('click', () => handleCardClick(card, 'tableau', col, index));
            column.appendChild(cardEl);
        });
        
        // Add drop zone
        if (solitaireGame.tableau[col].length === 0) {
            const dropZone = document.createElement('div');
            dropZone.className = 'w-16 h-20 border-2 border-dashed border-gray-500 rounded-lg flex items-center justify-center text-gray-500 text-xs';
            dropZone.textContent = 'K';
            dropZone.addEventListener('click', () => handleEmptyTableauClick(col));
            column.appendChild(dropZone);
        }
    }
}

function renderStock() {
    const stockEl = document.getElementById('stockPile');
    if (solitaireGame.stock.length > 0) {
        stockEl.textContent = '🂠';
        stockEl.className = 'w-16 h-20 bg-gradient-to-br from-blue-600 to-purple-600 border border-blue-400 rounded-lg cursor-pointer flex items-center justify-center text-white font-bold';
    } else {
        stockEl.textContent = '↻';
        stockEl.className = 'w-16 h-20 bg-gray-600 border border-gray-400 rounded-lg cursor-pointer flex items-center justify-center text-white font-bold';
    }
}

function renderWaste() {
    const wasteEl = document.getElementById('wastePile');
    if (solitaireGame.waste.length > 0) {
        const topCard = solitaireGame.waste[solitaireGame.waste.length - 1];
        wasteEl.innerHTML = createCardContent(topCard);
        wasteEl.className = 'w-16 h-20 bg-white border border-gray-300 rounded-lg flex flex-col items-center justify-center text-xs cursor-pointer';
        wasteEl.addEventListener('click', () => handleCardClick(topCard, 'waste', 0, solitaireGame.waste.length - 1));
    } else {
        wasteEl.textContent = '-';
        wasteEl.className = 'w-16 h-20 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400';
    }
}

function renderFoundation() {
    for (let i = 0; i < 4; i++) {
        const foundationEl = document.getElementById(`foundation-${i}`);
        if (solitaireGame.foundation[i].length > 0) {
            const topCard = solitaireGame.foundation[i][solitaireGame.foundation[i].length - 1];
            foundationEl.innerHTML = createCardContent(topCard);
            foundationEl.className = 'w-14 h-18 bg-white border border-gray-300 rounded-lg flex flex-col items-center justify-center text-xs';
        } else {
            foundationEl.innerHTML = SUIT_SYMBOLS[SUITS[i]];
            foundationEl.className = 'w-14 h-18 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400 text-xs foundation-pile';
        }
        foundationEl.addEventListener('click', () => handleFoundationClick(i));
    }
}

function createCardElement(card, id) {
    const cardEl = document.createElement('div');
    cardEl.id = id;
    cardEl.className = card.faceUp ? 
        'w-16 h-20 bg-white border border-gray-300 rounded-lg flex flex-col items-center justify-center text-xs cursor-pointer hover:scale-105 transition-transform' :
        'w-16 h-20 bg-gradient-to-br from-blue-600 to-purple-600 border border-blue-400 rounded-lg flex items-center justify-center text-white font-bold cursor-pointer';
    
    if (card.faceUp) {
        cardEl.innerHTML = createCardContent(card);
    } else {
        cardEl.textContent = '🂠';
    }
    
    return cardEl;
}

function createCardContent(card) {
    const color = card.color === 'red' ? 'text-red-500' : 'text-black';
    return `
        <div class="${color} font-bold">${card.rank}</div>
        <div class="${color}">${SUIT_SYMBOLS[card.suit]}</div>
    `;
}

function handleCardClick(card, pile, pileIndex, cardIndex) {
    if (!solitaireGame.isPlaying) return;
    
    if (solitaireGame.selectedCard) {
        // Try to move selected card to this position
        if (canMoveCard(solitaireGame.selectedCard, card, pile, pileIndex)) {
            moveCard(solitaireGame.selectedCard, card, pile, pileIndex);
        }
        clearSelection();
    } else {
        // Select this card
        if (card.faceUp && canSelectCard(card, pile, pileIndex, cardIndex)) {
            selectCard(card, pile, pileIndex, cardIndex);
        }
    }
}

function canSelectCard(card, pile, pileIndex, cardIndex) {
    if (pile === 'tableau') {
        return cardIndex === solitaireGame.tableau[pileIndex].length - 1;
    } else if (pile === 'waste') {
        return cardIndex === solitaireGame.waste.length - 1;
    }
    return false;
}

function selectCard(card, pile, pileIndex, cardIndex) {
    solitaireGame.selectedCard = { card, pile, pileIndex, cardIndex };
    
    // Highlight selected card
    const cardEl = document.getElementById(`${pile}-${pileIndex}-${cardIndex}`);
    if (cardEl) {
        cardEl.classList.add('ring-2', 'ring-yellow-400');
    }
}

function clearSelection() {
    if (solitaireGame.selectedCard) {
        const { pile, pileIndex, cardIndex } = solitaireGame.selectedCard;
        const cardEl = document.getElementById(`${pile}-${pileIndex}-${cardIndex}`);
        if (cardEl) {
            cardEl.classList.remove('ring-2', 'ring-yellow-400');
        }
    }
    solitaireGame.selectedCard = null;
}

function canMoveCard(selectedCard, targetCard, targetPile, targetPileIndex) {
    const { card: sourceCard } = selectedCard;
    
    if (targetPile === 'tableau') {
        // Can move to tableau if target is one rank higher and opposite color
        return targetCard.value === sourceCard.value + 1 && targetCard.color !== sourceCard.color;
    } else if (targetPile === 'foundation') {
        // Can move to foundation if same suit and one rank higher
        return targetCard.suit === sourceCard.suit && targetCard.value === sourceCard.value - 1;
    }
    
    return false;
}

function moveCard(selectedCard, targetCard, targetPile, targetPileIndex) {
    const { card: sourceCard, pile: sourcePile, pileIndex: sourcePileIndex } = selectedCard;
    
    // Save move for undo
    solitaireGame.undoStack.push({
        from: { pile: sourcePile, pileIndex: sourcePileIndex },
        to: { pile: targetPile, pileIndex: targetPileIndex },
        card: sourceCard
    });
    
    // Remove card from source
    if (sourcePile === 'tableau') {
        solitaireGame.tableau[sourcePileIndex].pop();
        // Flip next card if exists
        const nextCard = solitaireGame.tableau[sourcePileIndex][solitaireGame.tableau[sourcePileIndex].length - 1];
        if (nextCard && !nextCard.faceUp) {
            nextCard.faceUp = true;
            solitaireGame.cardReveals++;
        }
    } else if (sourcePile === 'waste') {
        solitaireGame.waste.pop();
    }
    
    // Add card to target
    if (targetPile === 'tableau') {
        solitaireGame.tableau[targetPileIndex].push(sourceCard);
    } else if (targetPile === 'foundation') {
        solitaireGame.foundation[targetPileIndex].push(sourceCard);
        solitaireGame.foundationMoves++;
    }
    
    solitaireGame.moveCount++;
    updateScore(10);
    
    renderGame();
    updateSolitaireDisplay();
    checkWinCondition();
}

function handleEmptyTableauClick(col) {
    if (solitaireGame.selectedCard && solitaireGame.selectedCard.card.rank === 'K') {
        // Move King to empty tableau
        moveCardToEmptyTableau(solitaireGame.selectedCard, col);
        clearSelection();
    }
}

function moveCardToEmptyTableau(selectedCard, col) {
    const { card: sourceCard, pile: sourcePile, pileIndex: sourcePileIndex } = selectedCard;
    
    // Save move for undo
    solitaireGame.undoStack.push({
        from: { pile: sourcePile, pileIndex: sourcePileIndex },
        to: { pile: 'tableau', pileIndex: col },
        card: sourceCard
    });
    
    // Remove card from source
    if (sourcePile === 'tableau') {
        solitaireGame.tableau[sourcePileIndex].pop();
    } else if (sourcePile === 'waste') {
        solitaireGame.waste.pop();
    }
    
    // Add card to empty tableau
    solitaireGame.tableau[col].push(sourceCard);
    
    solitaireGame.moveCount++;
    updateScore(10);
    
    renderGame();
    updateSolitaireDisplay();
}

function handleFoundationClick(foundationIndex) {
    if (solitaireGame.selectedCard) {
        const { card } = solitaireGame.selectedCard;
        const foundation = solitaireGame.foundation[foundationIndex];
        
        // Check if card can be placed on foundation
        if (foundation.length === 0 && card.rank === 'A') {
            // Ace on empty foundation
            moveCardToFoundation(solitaireGame.selectedCard, foundationIndex);
        } else if (foundation.length > 0) {
            const topCard = foundation[foundation.length - 1];
            if (card.suit === topCard.suit && card.value === topCard.value + 1) {
                moveCardToFoundation(solitaireGame.selectedCard, foundationIndex);
            }
        }
        clearSelection();
    }
}

function moveCardToFoundation(selectedCard, foundationIndex) {
    const { card: sourceCard, pile: sourcePile, pileIndex: sourcePileIndex } = selectedCard;
    
    // Save move for undo
    solitaireGame.undoStack.push({
        from: { pile: sourcePile, pileIndex: sourcePileIndex },
        to: { pile: 'foundation', pileIndex: foundationIndex },
        card: sourceCard
    });
    
    // Remove card from source
    if (sourcePile === 'tableau') {
        solitaireGame.tableau[sourcePileIndex].pop();
        // Flip next card if exists
        const nextCard = solitaireGame.tableau[sourcePileIndex][solitaireGame.tableau[sourcePileIndex].length - 1];
        if (nextCard && !nextCard.faceUp) {
            nextCard.faceUp = true;
            solitaireGame.cardReveals++;
        }
    } else if (sourcePile === 'waste') {
        solitaireGame.waste.pop();
    }
    
    // Add card to foundation
    solitaireGame.foundation[foundationIndex].push(sourceCard);
    solitaireGame.foundationMoves++;
    
    solitaireGame.moveCount++;
    updateScore(20);
    
    renderGame();
    updateSolitaireDisplay();
    checkWinCondition();
}

function drawFromStock() {
    if (!solitaireGame.isPlaying) return;
    
    const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === solitaireGame.difficulty);
    
    if (solitaireGame.stock.length > 0) {
        // Draw cards from stock to waste
        for (let i = 0; i < diffData.drawCount && solitaireGame.stock.length > 0; i++) {
            const card = solitaireGame.stock.pop();
            card.faceUp = true;
            solitaireGame.waste.push(card);
        }
    } else if (solitaireGame.waste.length > 0) {
        // Reset stock from waste
        while (solitaireGame.waste.length > 0) {
            const card = solitaireGame.waste.pop();
            card.faceUp = false;
            solitaireGame.stock.push(card);
        }
    }
    
    renderStock();
    renderWaste();
}

function updateScore(points) {
    solitaireGame.score += points * solitaireGame.multiplier;
    
    // Increase multiplier for consecutive good moves
    if (points > 0) {
        solitaireGame.streakCount++;
        solitaireGame.multiplier = Math.min(3.0, 1.0 + (solitaireGame.streakCount * 0.1));
    } else {
        solitaireGame.streakCount = 0;
        solitaireGame.multiplier = 1.0;
    }
}

function startGameTimer() {
    solitaireGame.gameTimer = setInterval(() => {
        solitaireGame.timeElapsed++;
        const minutes = Math.floor(solitaireGame.timeElapsed / 60);
        const seconds = solitaireGame.timeElapsed % 60;
        document.getElementById('gameTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === solitaireGame.difficulty);
        if (solitaireGame.timeElapsed >= diffData.timeLimit) {
            endSolitaireGame(false, 'Time limit exceeded!');
        }
    }, 1000);
}

function checkWinCondition() {
    // Check if all cards are in foundation
    const totalFoundationCards = solitaireGame.foundation.reduce((sum, pile) => sum + pile.length, 0);
    if (totalFoundationCards === 52) {
        endSolitaireGame(true, 'Perfect Solitaire!');
    }
}

function undoMove() {
    if (solitaireGame.undoStack.length === 0) return;
    
    const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === solitaireGame.difficulty);
    if (solitaireGame.undoCount >= diffData.undoLimit) return;
    
    const lastMove = solitaireGame.undoStack.pop();
    solitaireGame.undoCount++;
    
    // Reverse the move
    // Implementation would depend on move structure
    
    renderGame();
    updateSolitaireDisplay();
}

function getHint() {
    const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === solitaireGame.difficulty);
    if (solitaireGame.hintCount >= diffData.hintLimit) return;
    
    solitaireGame.hintCount++;
    
    // Find possible moves and highlight one
    // Implementation would analyze current game state
    
    updateSolitaireDisplay();
}

function surrenderGame() {
    endSolitaireGame(false, 'Game surrendered');
}

function updateSolitaireDisplay() {
    document.getElementById('moveCount').textContent = solitaireGame.moveCount;
    document.getElementById('currentScore').textContent = solitaireGame.score;
    document.getElementById('scoreMultiplier').textContent = solitaireGame.multiplier.toFixed(1) + 'x';
    document.getElementById('cardReveals').textContent = solitaireGame.cardReveals;
    document.getElementById('foundationCards').textContent = solitaireGame.foundation.reduce((sum, pile) => sum + pile.length, 0);
}

function endSolitaireGame(won, message) {
    solitaireGame.isPlaying = false;
    clearInterval(solitaireGame.gameTimer);
    
    document.getElementById('undoBtn').disabled = true;
    document.getElementById('hintBtn').disabled = true;
    document.getElementById('surrenderBtn').disabled = true;
    
    let winnings = 0;
    
    if (won) {
        const diffData = SOLITAIRE_DIFFICULTIES.find(d => d.name === solitaireGame.difficulty);
        const modeData = SOLITAIRE_MODES.find(m => m.name === solitaireGame.gameMode);
        
        // Calculate winnings
        let baseMultiplier = diffData.multiplier * modeData.payoutMultiplier;
        
        // Time bonus
        const timeBonus = Math.max(0, (diffData.timeLimit - solitaireGame.timeElapsed) / diffData.timeLimit);
        
        // Efficiency bonus
        const efficiencyBonus = solitaireGame.moveCount < 200 ? (200 - solitaireGame.moveCount) / 200 : 0;
        
        const totalMultiplier = baseMultiplier * (1 + timeBonus + efficiencyBonus) * solitaireGame.multiplier;
        winnings = Math.floor(solitaireGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        solitaireGame.winStreak++;
        solitaireGame.maxWinStreak = Math.max(solitaireGame.maxWinStreak, solitaireGame.winStreak);
        solitaireGame.stakePoints += 50;
        
        document.getElementById('solitaireResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">♠️ SOLITAIRE WON! ♠️</span>`;
        document.getElementById('solitaireWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        solitaireGame.winStreak = 0;
        
        document.getElementById('solitaireResult').innerHTML = 
            `<span class="text-red-400">♠️ GAME OVER ♠️</span>`;
        document.getElementById('solitaireWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">${message}</div>`;
    }
    
    solitaireGame.totalGames++;
    updateSolitaireDisplay();
    
    setTimeout(() => {
        document.getElementById('solitaireOverlay').classList.remove('hidden');
        document.getElementById('finalSolitaireResult').innerHTML = 
            won ? '<span class="text-green-400">♠️ SOLITAIRE MASTERY! ♠️</span>' : 
                  '<span class="text-red-400">💔 BETTER LUCK NEXT TIME 💔</span>';
        
        document.getElementById('finalSolitaireStats').innerHTML = 
            `Moves: ${solitaireGame.moveCount} | Time: ${Math.floor(solitaireGame.timeElapsed / 60)}:${(solitaireGame.timeElapsed % 60).toString().padStart(2, '0')}<br>
             Score: ${solitaireGame.score} | Reveals: ${solitaireGame.cardReveals} | Foundation: ${solitaireGame.foundationMoves}`;
    }, 3000);
}

function handleSolitaireKeyPress(e) {
    if (!solitaireGame.isPlaying) return;
    
    if (e.key.toLowerCase() === 'u') {
        e.preventDefault();
        undoMove();
    } else if (e.key.toLowerCase() === 'h') {
        e.preventDefault();
        getHint();
    } else if (e.code === 'Space') {
        e.preventDefault();
        drawFromStock();
    }
}

function hideSolitaireOverlay() {
    document.getElementById('solitaireOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSolitaireGame();
});