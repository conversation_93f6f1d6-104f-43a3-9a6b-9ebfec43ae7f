// Game state
let balance = 1000;

// Puzzle Poker: Strategy King game state
let puzzlePokerGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'classic',
    puzzleType: 'sequence',
    
    // Puzzle mechanics
    currentPuzzle: null,
    puzzleGrid: [],
    playerHand: [],
    targetHand: [],
    availableCards: [],
    movesUsed: 0,
    maxMoves: 0,
    timeLeft: 0,
    round: 1,
    maxRounds: 5,
    
    // Performance tracking
    perfectSolutions: 0,
    optimalMoves: 0,
    sequencesCompleted: 0,
    strategicPlays: 0,
    comboChains: 0,
    timeBonus: 0,
    efficiencyScore: 0,
    patternRecognition: 0,
    
    // Puzzle types completed
    sequencePuzzles: 0,
    buildPuzzles: 0,
    transformPuzzles: 0,
    optimizePuzzles: 0,
    
    // Advanced metrics
    averageMoves: 0,
    averageTime: 0,
    consistencyRating: 0,
    strategyDepth: 0,
    
    // Special achievements
    flawlessRounds: 0,
    speedSolutions: 0,
    minimalMoveSolutions: 0,
    complexPatterns: 0
};

// Ultra-strict difficulty settings with sub-8% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        rounds: 5,
        timeLimit: 180,
        maxMoves: 12,
        winRate: 0.08,
        multiplier: 0.7,
        puzzleComplexity: 0.6,
        patternDepth: 3,
        description: 'Beginner Puzzles (8% win rate)'
    },
    {
        name: 'normal',
        rounds: 5,
        timeLimit: 150,
        maxMoves: 10,
        winRate: 0.06,
        multiplier: 1.0,
        puzzleComplexity: 0.75,
        patternDepth: 4,
        description: 'Standard Puzzles (6% win rate)'
    },
    {
        name: 'hard',
        rounds: 6,
        timeLimit: 120,
        maxMoves: 8,
        winRate: 0.04,
        multiplier: 1.4,
        puzzleComplexity: 0.85,
        patternDepth: 5,
        description: 'Expert Puzzles (4% win rate)'
    },
    {
        name: 'expert',
        rounds: 7,
        timeLimit: 90,
        maxMoves: 6,
        winRate: 0.03,
        multiplier: 1.8,
        puzzleComplexity: 0.92,
        patternDepth: 6,
        description: 'Master Puzzles (3% win rate)'
    },
    {
        name: 'legendary',
        rounds: 8,
        timeLimit: 60,
        maxMoves: 5,
        winRate: 0.02,
        multiplier: 2.2,
        puzzleComplexity: 0.96,
        patternDepth: 7,
        description: 'Grandmaster Puzzles (2% win rate)'
    }
];

const GAME_MODES = [
    {
        name: 'classic',
        description: 'Standard Puzzle Poker',
        scoreMultiplier: 1.0,
        timeBonus: 1.0,
        moveBonus: 1.0
    },
    {
        name: 'blitz',
        description: 'Speed Puzzle Mode',
        scoreMultiplier: 1.3,
        timeBonus: 1.5,
        moveBonus: 0.8
    },
    {
        name: 'precision',
        description: 'Minimal Move Mode',
        scoreMultiplier: 1.5,
        timeBonus: 0.8,
        moveBonus: 1.8
    },
    {
        name: 'master',
        description: 'Ultimate Challenge',
        scoreMultiplier: 1.8,
        timeBonus: 1.2,
        moveBonus: 1.4
    }
];

const PUZZLE_TYPES = [
    {
        name: 'sequence',
        description: 'Build Sequential Hands',
        complexity: 1.0,
        timeMultiplier: 1.0
    },
    {
        name: 'build',
        description: 'Construct Target Hand',
        complexity: 1.2,
        timeMultiplier: 1.1
    },
    {
        name: 'transform',
        description: 'Transform Hand Type',
        complexity: 1.4,
        timeMultiplier: 1.2
    },
    {
        name: 'optimize',
        description: 'Find Optimal Solution',
        complexity: 1.6,
        timeMultiplier: 1.3
    }
];

const POKER_HANDS = [
    { name: 'High Card', value: 1, pattern: [1,1,1,1,1] },
    { name: 'Pair', value: 2, pattern: [2,1,1,1] },
    { name: 'Two Pair', value: 3, pattern: [2,2,1] },
    { name: 'Three of a Kind', value: 4, pattern: [3,1,1] },
    { name: 'Straight', value: 5, pattern: [1,1,1,1,1] },
    { name: 'Flush', value: 6, pattern: [1,1,1,1,1] },
    { name: 'Full House', value: 7, pattern: [3,2] },
    { name: 'Four of a Kind', value: 8, pattern: [4,1] },
    { name: 'Straight Flush', value: 9, pattern: [1,1,1,1,1] },
    { name: 'Royal Flush', value: 10, pattern: [1,1,1,1,1] }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadPuzzlePokerGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Puzzle Poker Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🧩 PUZZLE POKER: STRATEGY KING 🧩</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 PUZZLE BET</label>
                        <input type="number" id="puzzleBet" value="75" min="25" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updatePuzzlePayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎯 DIFFICULTY</label>
                            <select id="puzzleDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updatePuzzleDifficultySettings()">
                                <option value="easy">Easy (8%)</option>
                                <option value="normal" selected>Normal (6%)</option>
                                <option value="hard">Hard (4%)</option>
                                <option value="expert">Expert (3%)</option>
                                <option value="legendary">Legendary (2%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎮 MODE</label>
                            <select id="puzzleMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updatePuzzleModeSettings()">
                                <option value="classic" selected>Classic</option>
                                <option value="blitz">Blitz</option>
                                <option value="precision">Precision</option>
                                <option value="master">Master</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🧩 PUZZLE TYPE</label>
                        <select id="puzzleType" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                onchange="updatePuzzlePayout()">
                            <option value="sequence" selected>Sequence Building</option>
                            <option value="build">Hand Construction</option>
                            <option value="transform">Hand Transformation</option>
                            <option value="optimize">Optimal Strategy</option>
                        </select>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Rounds:</div>
                                <div class="text-white font-bold" id="totalRounds">5</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Win Chance:</div>
                                <div class="text-red-400 font-bold" id="puzzleWinChance">6%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Max Moves:</div>
                                <div class="text-yellow-400 font-bold" id="maxMoves">10</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Limit:</div>
                                <div class="text-blue-400 font-bold" id="timeLimit">2m 30s</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="puzzlePotentialPayout">525 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startPuzzlePokerGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        🧩 START PUZZLE CHALLENGE 🧩
                    </button>
                </div>

                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 PUZZLE STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Round:</span>
                            <span class="text-white" id="currentRound">0/0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect:</span>
                            <span class="text-green-400" id="perfectSolutions">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Optimal:</span>
                            <span class="text-yellow-400" id="optimalMoves">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sequences:</span>
                            <span class="text-blue-400" id="sequencesCompleted">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Strategy:</span>
                            <span class="text-purple-400" id="strategicPlays">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Time Left:</span>
                            <span class="text-red-400" id="puzzleTimeLeft">0:00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Puzzle Area -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="puzzleTitle">Puzzle Challenge</h5>
                        <p class="text-sm text-gray-400" id="puzzleDescription">Solve poker puzzles using strategy and logic</p>
                    </div>
                    
                    <!-- Target Hand Display -->
                    <div class="mb-4">
                        <div class="text-sm text-gray-400 mb-2">Target Hand:</div>
                        <div id="targetHandDisplay" class="flex justify-center space-x-2 mb-2">
                            <!-- Target cards will be displayed here -->
                        </div>
                        <div class="text-center text-sm font-bold" id="targetHandType">Royal Flush</div>
                    </div>

                    <!-- Current Hand Display -->
                    <div class="mb-4">
                        <div class="text-sm text-gray-400 mb-2">Current Hand:</div>
                        <div id="currentHandDisplay" class="flex justify-center space-x-2 mb-2">
                            <!-- Current cards will be displayed here -->
                        </div>
                        <div class="text-center text-sm" id="currentHandType">High Card</div>
                    </div>

                    <!-- Available Cards -->
                    <div class="mb-4">
                        <div class="text-sm text-gray-400 mb-2">Available Cards:</div>
                        <div id="availableCardsDisplay" class="grid grid-cols-6 gap-2 justify-center">
                            <!-- Available cards will be displayed here -->
                        </div>
                    </div>

                    <!-- Move Counter and Controls -->
                    <div class="flex justify-between items-center mb-4">
                        <div class="text-sm">
                            <span class="text-gray-400">Moves:</span>
                            <span class="text-white font-bold" id="movesUsed">0</span>
                            <span class="text-gray-400">/</span>
                            <span class="text-yellow-400" id="movesLimit">10</span>
                        </div>
                        <div class="space-x-2">
                            <button onclick="undoMove()" id="undoButton" 
                                    class="cyber-button bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm" disabled>
                                UNDO
                            </button>
                            <button onclick="resetPuzzle()" id="resetButton" 
                                    class="cyber-button bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                RESET
                            </button>
                        </div>
                    </div>

                    <!-- Puzzle Result -->
                    <div class="text-center">
                        <div id="puzzleResult" class="text-xl font-bold mb-2"></div>
                        <div id="puzzleWinAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Puzzle History -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 RECENT PUZZLES</h5>
                    <div id="puzzleHistory" class="space-y-1 text-sm max-h-32 overflow-y-auto">
                        <div class="text-gray-500 text-center">No puzzles solved yet</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Overlay -->
        <div id="gameOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🧩 PUZZLE COMPLETE 🧩</h3>
                <div id="finalResult" class="text-xl mb-4"></div>
                <div id="finalStats" class="text-sm text-gray-400 mb-6"></div>
                <button onclick="hideGameOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    CONTINUE PUZZLING
                </button>
            </div>
        </div>
    `;

    updatePuzzleDifficultySettings();
    updatePuzzleModeSettings();
    updatePuzzlePayout();
    initializePuzzleDisplay();
}

function updatePuzzleDifficultySettings() {
    const difficulty = document.getElementById('puzzleDifficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    puzzlePokerGame.difficulty = difficulty;
    puzzlePokerGame.maxRounds = diffData.rounds;
    
    document.getElementById('totalRounds').textContent = diffData.rounds;
    document.getElementById('puzzleWinChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('maxMoves').textContent = diffData.maxMoves;
    document.getElementById('timeLimit').textContent = Math.floor(diffData.timeLimit / 60) + 'm ' + (diffData.timeLimit % 60) + 's';
    
    updatePuzzlePayout();
}

function updatePuzzleModeSettings() {
    const mode = document.getElementById('puzzleMode').value;
    puzzlePokerGame.gameMode = mode;
    updatePuzzlePayout();
}

function updatePuzzlePayout() {
    const betAmount = parseInt(document.getElementById('puzzleBet').value) || 75;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === puzzlePokerGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === puzzlePokerGame.gameMode);
    const puzzleData = PUZZLE_TYPES.find(p => p.name === document.getElementById('puzzleType').value);
    
    const totalMultiplier = diffData.multiplier * modeData.scoreMultiplier * puzzleData.complexity;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 7); // Conservative multiplier
    
    document.getElementById('puzzlePotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function startPuzzlePokerGame() {
    const betAmount = parseInt(document.getElementById('puzzleBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === puzzlePokerGame.difficulty);
    
    // Reset game state
    puzzlePokerGame.isPlaying = true;
    puzzlePokerGame.gamePhase = 'playing';
    puzzlePokerGame.betAmount = betAmount;
    puzzlePokerGame.round = 1;
    puzzlePokerGame.timeLeft = diffData.timeLimit;
    puzzlePokerGame.maxMoves = diffData.maxMoves;
    puzzlePokerGame.puzzleType = document.getElementById('puzzleType').value;
    
    // Reset performance tracking
    puzzlePokerGame.perfectSolutions = 0;
    puzzlePokerGame.optimalMoves = 0;
    puzzlePokerGame.sequencesCompleted = 0;
    puzzlePokerGame.strategicPlays = 0;
    puzzlePokerGame.comboChains = 0;
    puzzlePokerGame.timeBonus = 0;
    puzzlePokerGame.efficiencyScore = 0;
    puzzlePokerGame.patternRecognition = 0;
    
    puzzlePokerGame.sequencePuzzles = 0;
    puzzlePokerGame.buildPuzzles = 0;
    puzzlePokerGame.transformPuzzles = 0;
    puzzlePokerGame.optimizePuzzles = 0;
    
    puzzlePokerGame.flawlessRounds = 0;
    puzzlePokerGame.speedSolutions = 0;
    puzzlePokerGame.minimalMoveSolutions = 0;
    puzzlePokerGame.complexPatterns = 0;
    
    hideGameOverlay();
    startNextPuzzle();
    
    // Start game timer
    puzzlePokerGame.gameTimer = setInterval(() => {
        puzzlePokerGame.timeLeft--;
        updatePuzzleDisplay();
        if (puzzlePokerGame.timeLeft <= 0) {
            endPuzzlePokerGame('TIME UP');
        }
    }, 1000);
}

function startNextPuzzle() {
    if (puzzlePokerGame.round > puzzlePokerGame.maxRounds) {
        endPuzzlePokerGame('ALL PUZZLES COMPLETE');
        return;
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === puzzlePokerGame.difficulty);
    
    // Generate new puzzle
    puzzlePokerGame.currentPuzzle = generatePuzzle(diffData);
    puzzlePokerGame.movesUsed = 0;
    
    updatePuzzleDisplay();
    displayPuzzle();
}

function generatePuzzle(diffData) {
    const puzzleType = puzzlePokerGame.puzzleType;
    const complexity = diffData.puzzleComplexity;
    
    let puzzle = {
        type: puzzleType,
        targetHand: generateTargetHand(complexity),
        startingHand: [],
        availableCards: [],
        solution: [],
        optimalMoves: 0
    };
    
    // Generate starting hand and available cards based on puzzle type
    switch (puzzleType) {
        case 'sequence':
            puzzle = generateSequencePuzzle(puzzle, complexity);
            break;
        case 'build':
            puzzle = generateBuildPuzzle(puzzle, complexity);
            break;
        case 'transform':
            puzzle = generateTransformPuzzle(puzzle, complexity);
            break;
        case 'optimize':
            puzzle = generateOptimizePuzzle(puzzle, complexity);
            break;
    }
    
    return puzzle;
}

function generateTargetHand(complexity) {
    const handTypes = POKER_HANDS.filter(h => h.value >= Math.floor(complexity * 10));
    const targetType = handTypes[Math.floor(Math.random() * handTypes.length)];
    
    return generateHandOfType(targetType);
}

function generateHandOfType(handType) {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    let hand = [];
    
    switch (handType.name) {
        case 'Royal Flush':
            const suit = suits[Math.floor(Math.random() * suits.length)];
            hand = ['10', 'J', 'Q', 'K', 'A'].map(rank => ({ rank, suit, value: ranks.indexOf(rank) + 2 }));
            break;
        case 'Straight Flush':
            const flushSuit = suits[Math.floor(Math.random() * suits.length)];
            const startRank = Math.floor(Math.random() * 9);
            hand = ranks.slice(startRank, startRank + 5).map(rank => ({ rank, suit: flushSuit, value: ranks.indexOf(rank) + 2 }));
            break;
        case 'Four of a Kind':
            const quadRank = ranks[Math.floor(Math.random() * ranks.length)];
            hand = suits.slice(0, 4).map(suit => ({ rank: quadRank, suit, value: ranks.indexOf(quadRank) + 2 }));
            const kicker = ranks.filter(r => r !== quadRank)[Math.floor(Math.random() * 12)];
            hand.push({ rank: kicker, suit: suits[0], value: ranks.indexOf(kicker) + 2 });
            break;
        case 'Full House':
            const tripRank = ranks[Math.floor(Math.random() * ranks.length)];
            const pairRank = ranks.filter(r => r !== tripRank)[Math.floor(Math.random() * 12)];
            hand = [
                ...suits.slice(0, 3).map(suit => ({ rank: tripRank, suit, value: ranks.indexOf(tripRank) + 2 })),
                ...suits.slice(0, 2).map(suit => ({ rank: pairRank, suit, value: ranks.indexOf(pairRank) + 2 }))
            ];
            break;
        default:
            // Generate random hand for other types
            for (let i = 0; i < 5; i++) {
                const rank = ranks[Math.floor(Math.random() * ranks.length)];
                const suit = suits[Math.floor(Math.random() * suits.length)];
                hand.push({ rank, suit, value: ranks.indexOf(rank) + 2 });
            }
    }
    
    return hand;
}

function generateSequencePuzzle(puzzle, complexity) {
    // Generate a sequence building puzzle
    puzzle.startingHand = generateRandomHand(3);
    puzzle.availableCards = generateRandomCards(8);
    puzzle.optimalMoves = Math.floor(4 + complexity * 3);
    return puzzle;
}

function generateBuildPuzzle(puzzle, complexity) {
    // Generate a hand construction puzzle
    puzzle.startingHand = generateRandomHand(2);
    puzzle.availableCards = generateRandomCards(10);
    puzzle.optimalMoves = Math.floor(5 + complexity * 4);
    return puzzle;
}

function generateTransformPuzzle(puzzle, complexity) {
    // Generate a hand transformation puzzle
    puzzle.startingHand = generateRandomHand(5);
    puzzle.availableCards = generateRandomCards(6);
    puzzle.optimalMoves = Math.floor(3 + complexity * 2);
    return puzzle;
}

function generateOptimizePuzzle(puzzle, complexity) {
    // Generate an optimization puzzle
    puzzle.startingHand = generateRandomHand(4);
    puzzle.availableCards = generateRandomCards(12);
    puzzle.optimalMoves = Math.floor(6 + complexity * 5);
    return puzzle;
}

function generateRandomHand(count) {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const hand = [];
    for (let i = 0; i < count; i++) {
        const rank = ranks[Math.floor(Math.random() * ranks.length)];
        const suit = suits[Math.floor(Math.random() * suits.length)];
        hand.push({ rank, suit, value: ranks.indexOf(rank) + 2 });
    }
    return hand;
}

function generateRandomCards(count) {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const cards = [];
    for (let i = 0; i < count; i++) {
        const rank = ranks[Math.floor(Math.random() * ranks.length)];
        const suit = suits[Math.floor(Math.random() * suits.length)];
        cards.push({ rank, suit, value: ranks.indexOf(rank) + 2 });
    }
    return cards;
}

function initializePuzzleDisplay() {
    // Initialize empty displays
    document.getElementById('targetHandDisplay').innerHTML = '';
    document.getElementById('currentHandDisplay').innerHTML = '';
    document.getElementById('availableCardsDisplay').innerHTML = '';
    document.getElementById('puzzleTitle').textContent = 'Ready to Start';
    document.getElementById('puzzleDescription').textContent = 'Click START to begin your puzzle challenge';
}

function displayPuzzle() {
    const puzzle = puzzlePokerGame.currentPuzzle;
    
    // Update puzzle info
    document.getElementById('puzzleTitle').textContent = `Round ${puzzlePokerGame.round}: ${puzzle.type.charAt(0).toUpperCase() + puzzle.type.slice(1)} Puzzle`;
    document.getElementById('puzzleDescription').textContent = PUZZLE_TYPES.find(p => p.name === puzzle.type).description;
    
    // Display target hand
    displayCards('targetHandDisplay', puzzle.targetHand);
    document.getElementById('targetHandType').textContent = evaluateHandType(puzzle.targetHand);
    
    // Display current hand
    puzzlePokerGame.playerHand = [...puzzle.startingHand];
    displayCards('currentHandDisplay', puzzlePokerGame.playerHand);
    document.getElementById('currentHandType').textContent = evaluateHandType(puzzlePokerGame.playerHand);
    
    // Display available cards
    puzzlePokerGame.availableCards = [...puzzle.availableCards];
    displayAvailableCards();
    
    updatePuzzleDisplay();
}

function displayCards(containerId, cards) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    cards.forEach((card, index) => {
        const cardEl = document.createElement('div');
        cardEl.className = 'bg-white text-black rounded-lg p-2 text-center text-sm font-bold min-w-[50px] cursor-pointer hover:bg-gray-200';
        cardEl.innerHTML = `${card.rank}<br>${card.suit}`;
        cardEl.onclick = () => selectCard(containerId, index);
        container.appendChild(cardEl);
    });
}

function displayAvailableCards() {
    const container = document.getElementById('availableCardsDisplay');
    container.innerHTML = '';
    
    puzzlePokerGame.availableCards.forEach((card, index) => {
        const cardEl = document.createElement('div');
        cardEl.className = 'bg-gray-700 text-white rounded-lg p-2 text-center text-xs font-bold cursor-pointer hover:bg-gray-600';
        cardEl.innerHTML = `${card.rank}<br>${card.suit}`;
        cardEl.onclick = () => selectAvailableCard(index);
        container.appendChild(cardEl);
    });
}

function selectCard(containerId, index) {
    if (!puzzlePokerGame.isPlaying) return;
    
    // Handle card selection logic
    if (containerId === 'currentHandDisplay') {
        // Remove card from current hand
        if (puzzlePokerGame.playerHand.length > 1) {
            const removedCard = puzzlePokerGame.playerHand.splice(index, 1)[0];
            puzzlePokerGame.availableCards.push(removedCard);
            makeMove();
        }
    }
}

function selectAvailableCard(index) {
    if (!puzzlePokerGame.isPlaying) return;
    
    // Add card to current hand if there's space
    if (puzzlePokerGame.playerHand.length < 5) {
        const selectedCard = puzzlePokerGame.availableCards.splice(index, 1)[0];
        puzzlePokerGame.playerHand.push(selectedCard);
        makeMove();
    }
}

function makeMove() {
    puzzlePokerGame.movesUsed++;
    
    // Update displays
    displayCards('currentHandDisplay', puzzlePokerGame.playerHand);
    document.getElementById('currentHandType').textContent = evaluateHandType(puzzlePokerGame.playerHand);
    displayAvailableCards();
    updatePuzzleDisplay();
    
    // Check if puzzle is solved
    if (isPuzzleSolved()) {
        solvePuzzle();
    }
    
    // Check if out of moves
    if (puzzlePokerGame.movesUsed >= puzzlePokerGame.maxMoves) {
        failPuzzle();
    }
}

function isPuzzleSolved() {
    const currentType = evaluateHandType(puzzlePokerGame.playerHand);
    const targetType = evaluateHandType(puzzlePokerGame.currentPuzzle.targetHand);
    
    // For sequence puzzles, check if hand matches target exactly
    if (puzzlePokerGame.puzzleType === 'sequence') {
        return JSON.stringify(puzzlePokerGame.playerHand.sort()) === JSON.stringify(puzzlePokerGame.currentPuzzle.targetHand.sort());
    }
    
    // For other puzzles, check if hand type matches
    return currentType === targetType;
}

function solvePuzzle() {
    const puzzle = puzzlePokerGame.currentPuzzle;
    const movesUsed = puzzlePokerGame.movesUsed;
    const timeUsed = (DIFFICULTY_LEVELS.find(d => d.name === puzzlePokerGame.difficulty).timeLimit - puzzlePokerGame.timeLeft);
    
    // Track performance
    if (movesUsed <= puzzle.optimalMoves) {
        puzzlePokerGame.optimalMoves++;
        if (movesUsed === puzzle.optimalMoves) {
            puzzlePokerGame.perfectSolutions++;
        }
    }
    
    if (timeUsed <= 30) {
        puzzlePokerGame.speedSolutions++;
    }
    
    if (movesUsed <= Math.floor(puzzle.optimalMoves * 0.8)) {
        puzzlePokerGame.minimalMoveSolutions++;
    }
    
    // Track puzzle type completions
    switch (puzzlePokerGame.puzzleType) {
        case 'sequence':
            puzzlePokerGame.sequencePuzzles++;
            break;
        case 'build':
            puzzlePokerGame.buildPuzzles++;
            break;
        case 'transform':
            puzzlePokerGame.transformPuzzles++;
            break;
        case 'optimize':
            puzzlePokerGame.optimizePuzzles++;
            break;
    }
    
    puzzlePokerGame.sequencesCompleted++;
    puzzlePokerGame.strategicPlays += Math.floor(movesUsed / 2);
    
    addPuzzleHistory(puzzlePokerGame.round, movesUsed, timeUsed, true);
    
    // Move to next puzzle
    puzzlePokerGame.round++;
    setTimeout(() => startNextPuzzle(), 2000);
}

function failPuzzle() {
    addPuzzleHistory(puzzlePokerGame.round, puzzlePokerGame.movesUsed, 0, false);
    endPuzzlePokerGame('PUZZLE FAILED');
}

function evaluateHandType(hand) {
    if (hand.length < 5) return 'Incomplete Hand';
    
    // Sort hand by value
    const sortedHand = hand.sort((a, b) => a.value - b.value);
    
    // Check for flush
    const isFlush = hand.every(card => card.suit === hand[0].suit);
    
    // Check for straight
    const values = sortedHand.map(card => card.value);
    const isStraight = values.every((val, i) => i === 0 || val === values[i-1] + 1);
    
    // Count ranks
    const rankCounts = {};
    hand.forEach(card => {
        rankCounts[card.rank] = (rankCounts[card.rank] || 0) + 1;
    });
    const counts = Object.values(rankCounts).sort((a, b) => b - a);
    
    // Determine hand type
    if (isStraight && isFlush) {
        if (values[0] === 10) return 'Royal Flush';
        return 'Straight Flush';
    }
    if (counts[0] === 4) return 'Four of a Kind';
    if (counts[0] === 3 && counts[1] === 2) return 'Full House';
    if (isFlush) return 'Flush';
    if (isStraight) return 'Straight';
    if (counts[0] === 3) return 'Three of a Kind';
    if (counts[0] === 2 && counts[1] === 2) return 'Two Pair';
    if (counts[0] === 2) return 'Pair';
    return 'High Card';
}

function undoMove() {
    // Implement undo functionality
    if (puzzlePokerGame.movesUsed > 0) {
        puzzlePokerGame.movesUsed--;
        updatePuzzleDisplay();
    }
}

function resetPuzzle() {
    // Reset current puzzle
    if (puzzlePokerGame.currentPuzzle) {
        puzzlePokerGame.movesUsed = 0;
        displayPuzzle();
    }
}

function updatePuzzleDisplay() {
    document.getElementById('currentRound').textContent = `${puzzlePokerGame.round}/${puzzlePokerGame.maxRounds}`;
    document.getElementById('perfectSolutions').textContent = puzzlePokerGame.perfectSolutions;
    document.getElementById('optimalMoves').textContent = puzzlePokerGame.optimalMoves;
    document.getElementById('sequencesCompleted').textContent = puzzlePokerGame.sequencesCompleted;
    document.getElementById('strategicPlays').textContent = puzzlePokerGame.strategicPlays;
    document.getElementById('puzzleTimeLeft').textContent = 
        Math.floor(puzzlePokerGame.timeLeft / 60) + ':' + 
        String(puzzlePokerGame.timeLeft % 60).padStart(2, '0');
    
    document.getElementById('movesUsed').textContent = puzzlePokerGame.movesUsed;
    document.getElementById('movesLimit').textContent = puzzlePokerGame.maxMoves;
    
    // Update button states
    document.getElementById('undoButton').disabled = puzzlePokerGame.movesUsed === 0;
}

function endPuzzlePokerGame(reason) {
    puzzlePokerGame.isPlaying = false;
    puzzlePokerGame.gamePhase = 'finished';
    
    if (puzzlePokerGame.gameTimer) {
        clearInterval(puzzlePokerGame.gameTimer);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === puzzlePokerGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === puzzlePokerGame.gameMode);
    
    // Ultra-strict winning requirements
    const completionRate = puzzlePokerGame.sequencesCompleted / puzzlePokerGame.maxRounds;
    const baseWinChance = diffData.winRate;
    
    // Extremely demanding requirements
    const minPerfectSolutions = Math.floor(puzzlePokerGame.maxRounds * 0.8);  // Need 80%+ perfect
    const minOptimalMoves = Math.floor(puzzlePokerGame.maxRounds * 0.7);      // Need 70%+ optimal
    const minSequencesPuzzles = 2;                                            // Need 2+ sequence puzzles
    const minBuildPuzzles = 2;                                               // Need 2+ build puzzles
    const minTransformPuzzles = 1;                                           // Need 1+ transform puzzle
    const minOptimizePuzzles = 1;                                            // Need 1+ optimize puzzle
    const minSpeedSolutions = 3;                                             // Need 3+ speed solutions
    const minMinimalMoveSolutions = 2;                                       // Need 2+ minimal move solutions
    const minStrategicPlays = puzzlePokerGame.maxRounds * 3;                 // Need strategic depth
    const requiredCompletionRate = 1.0;                                     // Must complete all puzzles
    
    const meetsAllRequirements = 
        reason === 'ALL PUZZLES COMPLETE' &&
        puzzlePokerGame.perfectSolutions >= minPerfectSolutions &&
        puzzlePokerGame.optimalMoves >= minOptimalMoves &&
        puzzlePokerGame.sequencePuzzles >= minSequencesPuzzles &&
        puzzlePokerGame.buildPuzzles >= minBuildPuzzles &&
        puzzlePokerGame.transformPuzzles >= minTransformPuzzles &&
        puzzlePokerGame.optimizePuzzles >= minOptimizePuzzles &&
        puzzlePokerGame.speedSolutions >= minSpeedSolutions &&
        puzzlePokerGame.minimalMoveSolutions >= minMinimalMoveSolutions &&
        puzzlePokerGame.strategicPlays >= minStrategicPlays &&
        completionRate >= requiredCompletionRate;
    
    // Performance bonuses
    const perfectRatio = puzzlePokerGame.perfectSolutions / puzzlePokerGame.maxRounds;
    const optimalRatio = puzzlePokerGame.optimalMoves / puzzlePokerGame.maxRounds;
    const speedRatio = puzzlePokerGame.speedSolutions / puzzlePokerGame.maxRounds;
    const diversityBonus = (puzzlePokerGame.sequencePuzzles > 0 ? 0.25 : 0) +
                          (puzzlePokerGame.buildPuzzles > 0 ? 0.25 : 0) +
                          (puzzlePokerGame.transformPuzzles > 0 ? 0.25 : 0) +
                          (puzzlePokerGame.optimizePuzzles > 0 ? 0.25 : 0);
    
    const performanceMultiplier = 1 + perfectRatio * 0.5 + optimalRatio * 0.3 + speedRatio * 0.2 + diversityBonus;
    const finalWinChance = meetsAllRequirements ? baseWinChance * performanceMultiplier : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with bonuses
        const puzzleData = PUZZLE_TYPES.find(p => p.name === puzzlePokerGame.puzzleType);
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier * puzzleData.complexity;
        const perfectBonus = puzzlePokerGame.perfectSolutions * 0.2;
        const optimalBonus = puzzlePokerGame.optimalMoves * 0.15;
        const speedBonus = puzzlePokerGame.speedSolutions * 0.1;
        const diversityBonusMultiplier = diversityBonus * 0.3;
        const strategicBonus = Math.min(0.5, puzzlePokerGame.strategicPlays / (puzzlePokerGame.maxRounds * 5));
        
        const totalMultiplier = baseMultiplier * (1 + perfectBonus + optimalBonus + speedBonus + 
                                                 diversityBonusMultiplier + strategicBonus);
        const winnings = Math.floor(puzzlePokerGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('puzzleResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🧩 PUZZLE MASTER! 🧩</span>`;
        document.getElementById('puzzleWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        const requirements = [
            `${minPerfectSolutions}+ Perfect Solutions (${puzzlePokerGame.perfectSolutions})`,
            `${minOptimalMoves}+ Optimal Moves (${puzzlePokerGame.optimalMoves})`,
            `${minSequencesPuzzles}+ Sequence Puzzles (${puzzlePokerGame.sequencePuzzles})`,
            `${minBuildPuzzles}+ Build Puzzles (${puzzlePokerGame.buildPuzzles})`,
            `${minTransformPuzzles}+ Transform Puzzles (${puzzlePokerGame.transformPuzzles})`,
            `${minOptimizePuzzles}+ Optimize Puzzles (${puzzlePokerGame.optimizePuzzles})`,
            `${minSpeedSolutions}+ Speed Solutions (${puzzlePokerGame.speedSolutions})`,
            `${minMinimalMoveSolutions}+ Minimal Move Solutions (${puzzlePokerGame.minimalMoveSolutions})`,
            `${minStrategicPlays}+ Strategic Plays (${puzzlePokerGame.strategicPlays})`,
            `Complete All Puzzles (${completionRate * 100}%)`
        ];
        
        document.getElementById('puzzleResult').innerHTML = 
            `<span class="text-red-400">🧩 ${reason} 🧩</span>`;
        document.getElementById('puzzleWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Need: ${requirements.slice(0, 5).join(', ')}<br>
            ${requirements.slice(5, 10).join(', ')}</div>`;
    }
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
        document.getElementById('finalResult').innerHTML = 
            won ? '<span class="text-green-400">🏆 PUZZLE CHAMPION! 🏆</span>' : 
                  '<span class="text-red-400">💔 PUZZLE INCOMPLETE 💔</span>';
        document.getElementById('finalStats').innerHTML = 
            `Puzzles Solved: ${puzzlePokerGame.sequencesCompleted}/${puzzlePokerGame.maxRounds}<br>
             Perfect Solutions: ${puzzlePokerGame.perfectSolutions} | Optimal Moves: ${puzzlePokerGame.optimalMoves}<br>
             Speed Solutions: ${puzzlePokerGame.speedSolutions} | Strategic Plays: ${puzzlePokerGame.strategicPlays}`;
    }, 3000);
}

function addPuzzleHistory(round, moves, time, solved) {
    const history = document.getElementById('puzzleHistory');
    const result = document.createElement('div');
    result.className = `flex justify-between py-1 ${solved ? 'text-green-400' : 'text-red-400'}`;
    result.innerHTML = `
        <span>Round ${round}: ${moves} moves</span>
        <span class="text-xs">${solved ? 'SOLVED' : 'FAILED'}</span>
    `;
    
    history.insertBefore(result, history.firstChild);
    
    // Keep only last 8 results
    while (history.children.length > 8) {
        history.removeChild(history.lastChild);
    }
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPuzzlePokerGame();
});