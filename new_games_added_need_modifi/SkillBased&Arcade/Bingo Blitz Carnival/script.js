// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Bingo Blitz Carnival Game Implementation ---

// Game constants for Bingo Blitz Carnival
const BINGO_PATTERNS = [
    { name: 'line', description: 'Any Line', difficulty: 1, multiplier: 2 },
    { name: 'corners', description: 'Four Corners', difficulty: 2, multiplier: 4 },
    { name: 'x', description: 'X Pattern', difficulty: 3, multiplier: 6 },
    { name: 'plus', description: 'Plus Pattern', difficulty: 3, multiplier: 6 },
    { name: 'frame', description: 'Frame/Border', difficulty: 4, multiplier: 8 },
    { name: 'fullhouse', description: 'Full House', difficulty: 5, multiplier: 15 }
];

const CARNIVAL_MODES = [
    { name: 'classic', speed: 3000, description: 'Classic Carnival', bonus: 1.0 },
    { name: 'turbo', speed: 2000, description: 'Turbo Carnival', bonus: 1.2 },
    { name: 'blitz', speed: 1500, description: 'Blitz Mode', bonus: 1.5 },
    { name: 'lightning', speed: 1000, description: 'Lightning Fast', bonus: 2.0 },
    { name: 'quantum', speed: 500, description: 'Quantum Speed', bonus: 3.0 }
];

const SPECIAL_BALLS = [
    { name: 'golden', chance: 0.05, multiplier: 5, description: 'Golden Ball - 5x multiplier' },
    { name: 'rainbow', chance: 0.03, multiplier: 10, description: 'Rainbow Ball - 10x multiplier' },
    { name: 'carnival', chance: 0.02, multiplier: 20, description: 'Carnival Ball - 20x multiplier' },
    { name: 'jackpot', chance: 0.01, multiplier: 50, description: 'Jackpot Ball - 50x multiplier' }
];

let bingoBlitzGame = {
    isPlaying: false,
    gamePhase: 'waiting', // waiting, playing, finished
    betAmount: 0,
    selectedPattern: 'line',
    gameMode: 'classic',
    
    // Game state
    bingoCard: [],
    calledNumbers: [],
    markedNumbers: new Set(),
    currentNumber: null,
    numbersLeft: 75,
    gameTimer: null,
    callSpeed: 3000,
    
    // Special features
    specialBalls: 0,
    carnivalBonus: 0,
    blitzLevel: 1,
    consecutiveWins: 0,
    totalGames: 0,
    perfectGames: 0,
    maxStreak: 0,
    
    // Carnival atmosphere
    confettiActive: false,
    musicLevel: 1,
    crowdExcitement: 0,
    
    // Performance tracking
    reactionTime: [],
    averageReaction: 0,
    missedCalls: 0,
    perfectMarks: 0
};

function loadBingoBlitzGame() {
    document.getElementById('gameContent').innerHTML = `
        <div class="max-w-6xl mx-auto">
            <!-- Game Header -->
            <div class="text-center mb-6">
                <h3 class="text-3xl font-bold text-purple-400 mb-2">🎪 BINGO BLITZ CARNIVAL 🎪</h3>
                <p class="text-blue-300">Fast-paced carnival bingo with special balls and patterns!</p>
            </div>

            <!-- Game Controls -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <!-- Bet Controls -->
                <div class="game-card p-4">
                    <h4 class="text-xl font-semibold text-purple-400 mb-3">🎯 Game Setup</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Bet Amount:</label>
                            <select id="bingoBet" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                <option value="10">10 GA</option>
                                <option value="25">25 GA</option>
                                <option value="50">50 GA</option>
                                <option value="100">100 GA</option>
                                <option value="250">250 GA</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Pattern:</label>
                            <select id="bingoPattern" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                ${BINGO_PATTERNS.map(p => 
                                    `<option value="${p.name}">${p.description} (${p.multiplier}x)</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Game Mode:</label>
                            <select id="bingoMode" class="w-full bg-black/50 border border-purple-500/30 rounded px-3 py-2 text-white">
                                ${CARNIVAL_MODES.map(m => 
                                    `<option value="${m.name}">${m.description} (${m.bonus}x)</option>`
                                ).join('')}
                            </select>
                        </div>
                        <button id="startBingo" class="w-full cyber-button px-4 py-2 rounded font-semibold">
                            START CARNIVAL BINGO
                        </button>
                    </div>
                </div>

                <!-- Game Stats -->
                <div class="game-card p-4">
                    <h4 class="text-xl font-semibold text-purple-400 mb-3">📊 Carnival Stats</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Blitz Level:</span>
                            <span id="blitzLevel" class="text-purple-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Consecutive Wins:</span>
                            <span id="consecutiveWins" class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Perfect Games:</span>
                            <span id="perfectGames" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Max Streak:</span>
                            <span id="maxStreak" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Avg Reaction:</span>
                            <span id="avgReaction" class="text-pink-400">0.0s</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Crowd Excitement:</span>
                            <span id="crowdExcitement" class="text-orange-400">0%</span>
                        </div>
                    </div>
                </div>

                <!-- Special Features -->
                <div class="game-card p-4">
                    <h4 class="text-xl font-semibold text-purple-400 mb-3">🎪 Carnival Features</h4>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Special Balls:</span>
                            <span id="specialBalls" class="text-gold-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Carnival Bonus:</span>
                            <span id="carnivalBonus" class="text-rainbow">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Music Level:</span>
                            <span id="musicLevel" class="text-cyan-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Perfect Marks:</span>
                            <span id="perfectMarks" class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Missed Calls:</span>
                            <span id="missedCalls" class="text-red-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="text-xs text-gray-400 mb-1">Carnival Energy:</div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div id="carnivalEnergyBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div id="carnivalEnergyText" class="text-xs text-center mt-1 text-purple-400">Carnival Starting...</div>
                    </div>
                </div>
            </div>

            <!-- Game Area -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Bingo Card -->
                <div class="game-card p-6">
                    <h4 class="text-xl font-semibold text-purple-400 mb-4 text-center">🎪 Your Bingo Card 🎪</h4>
                    <div id="bingoCard" class="grid grid-cols-5 gap-2 mb-4">
                        <!-- Bingo card will be generated here -->
                    </div>
                    <div class="text-center">
                        <div id="bingoStatus" class="text-lg font-semibold text-blue-300 mb-2">Ready for carnival bingo!</div>
                        <div id="bingoResult" class="text-xl font-bold"></div>
                    </div>
                </div>

                <!-- Number Caller -->
                <div class="game-card p-6">
                    <h4 class="text-xl font-semibold text-purple-400 mb-4 text-center">🎯 Number Caller 🎯</h4>
                    
                    <!-- Current Number Display -->
                    <div class="text-center mb-6">
                        <div id="currentNumberDisplay" class="text-6xl font-bold text-yellow-400 mb-2 carnival-glow">--</div>
                        <div id="numberLetter" class="text-2xl text-purple-400 font-semibold">BINGO</div>
                        <div id="specialBallIndicator" class="text-lg mt-2"></div>
                    </div>

                    <!-- Game Progress -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-300 mb-1">
                            <span>Numbers Called:</span>
                            <span id="numbersCalledCount">0/75</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div id="gameProgressBar" class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- Called Numbers History -->
                    <div class="mb-4">
                        <div class="text-sm text-gray-300 mb-2">Recently Called:</div>
                        <div id="calledNumbersHistory" class="flex flex-wrap gap-1 min-h-[40px] p-2 bg-black/30 rounded border border-purple-500/20">
                            <!-- Called numbers will appear here -->
                        </div>
                    </div>

                    <!-- Reaction Timer -->
                    <div class="text-center">
                        <div class="text-sm text-gray-300 mb-1">Reaction Time:</div>
                        <div id="reactionTimer" class="text-2xl font-bold text-green-400">0.00s</div>
                        <div class="text-xs text-gray-400 mt-1">Click numbers quickly for bonus points!</div>
                    </div>
                </div>
            </div>

            <!-- Pattern Guide -->
            <div class="game-card p-4 mt-6">
                <h4 class="text-lg font-semibold text-purple-400 mb-3">🎯 Pattern Guide & Special Features</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <div class="text-purple-300 font-semibold mb-2">Winning Patterns:</div>
                        <div class="space-y-1">
                            ${BINGO_PATTERNS.map(p => 
                                `<div>🎯 <span class="text-yellow-400">${p.description}:</span> ${p.multiplier}x multiplier</div>`
                            ).join('')}
                        </div>
                    </div>
                    <div>
                        <div class="text-purple-300 font-semibold mb-2">Special Balls:</div>
                        <div class="space-y-1">
                            ${SPECIAL_BALLS.map(b => 
                                `<div>✨ <span class="text-gold-400">${b.description}</span></div>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupBingoBlitzGame();
}

function setupBingoBlitzGame() {
    document.getElementById('startBingo').addEventListener('click', startBingoGame);
    updateBingoDisplay();
    generateBingoCard();
}

function generateBingoCard() {
    const cardContainer = document.getElementById('bingoCard');
    cardContainer.innerHTML = '';
    
    // Generate BINGO header
    const headers = ['B', 'I', 'N', 'G', 'O'];
    headers.forEach(letter => {
        const headerCell = document.createElement('div');
        headerCell.className = 'bg-purple-600 text-white font-bold text-xl h-12 flex items-center justify-center rounded border-2 border-purple-400';
        headerCell.textContent = letter;
        cardContainer.appendChild(headerCell);
    });
    
    // Generate card numbers
    bingoBlitzGame.bingoCard = [];
    const ranges = [
        [1, 15],   // B column
        [16, 30],  // I column
        [31, 45],  // N column
        [46, 60],  // G column
        [61, 75]   // O column
    ];
    
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            const cell = document.createElement('div');
            cell.className = 'bg-black/50 border-2 border-purple-500/30 rounded h-12 flex items-center justify-center text-lg font-semibold cursor-pointer transition-all duration-200 hover:border-purple-400';
            
            if (row === 2 && col === 2) {
                // Free space in center
                cell.textContent = 'FREE';
                cell.className += ' bg-green-600/50 border-green-400';
                bingoBlitzGame.bingoCard.push('FREE');
                bingoBlitzGame.markedNumbers.add('FREE');
            } else {
                const [min, max] = ranges[col];
                let number;
                do {
                    number = Math.floor(Math.random() * (max - min + 1)) + min;
                } while (bingoBlitzGame.bingoCard.includes(number));
                
                cell.textContent = number;
                cell.dataset.number = number;
                bingoBlitzGame.bingoCard.push(number);
                
                cell.addEventListener('click', () => markNumber(number, cell));
            }
            
            cardContainer.appendChild(cell);
        }
    }
}

function startBingoGame() {
    if (bingoBlitzGame.isPlaying) return;
    
    bingoBlitzGame.betAmount = parseInt(document.getElementById('bingoBet').value);
    bingoBlitzGame.selectedPattern = document.getElementById('bingoPattern').value;
    bingoBlitzGame.gameMode = document.getElementById('bingoMode').value;
    
    if (balance < bingoBlitzGame.betAmount) {
        document.getElementById('bingoStatus').textContent = 'Insufficient funds for carnival entry!';
        return;
    }
    
    balance -= bingoBlitzGame.betAmount;
    updateBalance();
    
    bingoBlitzGame.isPlaying = true;
    bingoBlitzGame.gamePhase = 'playing';
    bingoBlitzGame.totalGames++;
    bingoBlitzGame.calledNumbers = [];
    bingoBlitzGame.markedNumbers = new Set(['FREE']);
    bingoBlitzGame.currentNumber = null;
    bingoBlitzGame.numbersLeft = 75;
    bingoBlitzGame.reactionTime = [];
    bingoBlitzGame.missedCalls = 0;
    bingoBlitzGame.perfectMarks = 0;
    bingoBlitzGame.confettiActive = true;
    
    // Set call speed based on mode
    const modeData = CARNIVAL_MODES.find(m => m.name === bingoBlitzGame.gameMode);
    bingoBlitzGame.callSpeed = modeData.speed;
    
    document.getElementById('startBingo').disabled = true;
    document.getElementById('bingoStatus').textContent = 'Carnival bingo starting! Get ready!';
    
    // Reset card markings
    document.querySelectorAll('#bingoCard div[data-number]').forEach(cell => {
        cell.classList.remove('bg-yellow-500', 'border-yellow-400', 'marked');
    });
    
    // Start calling numbers
    setTimeout(() => {
        callNextNumber();
    }, 2000);
    
    updateBingoDisplay();
}

function callNextNumber() {
    if (!bingoBlitzGame.isPlaying || bingoBlitzGame.numbersLeft === 0) return;
    
    // Generate available numbers
    const availableNumbers = [];
    for (let i = 1; i <= 75; i++) {
        if (!bingoBlitzGame.calledNumbers.includes(i)) {
            availableNumbers.push(i);
        }
    }
    
    if (availableNumbers.length === 0) {
        endBingoGame(false, 'All numbers called - no winner!');
        return;
    }
    
    // Select random number
    const randomIndex = Math.floor(Math.random() * availableNumbers.length);
    const calledNumber = availableNumbers[randomIndex];
    
    // Check for special ball
    let isSpecialBall = false;
    let specialMultiplier = 1;
    let specialType = '';
    
    for (const special of SPECIAL_BALLS) {
        if (Math.random() < special.chance) {
            isSpecialBall = true;
            specialMultiplier = special.multiplier;
            specialType = special.name;
            bingoBlitzGame.specialBalls++;
            break;
        }
    }
    
    bingoBlitzGame.calledNumbers.push(calledNumber);
    bingoBlitzGame.currentNumber = calledNumber;
    bingoBlitzGame.numbersLeft--;
    
    // Update display
    document.getElementById('currentNumberDisplay').textContent = calledNumber;
    
    // Determine letter
    let letter = '';
    if (calledNumber <= 15) letter = 'B';
    else if (calledNumber <= 30) letter = 'I';
    else if (calledNumber <= 45) letter = 'N';
    else if (calledNumber <= 60) letter = 'G';
    else letter = 'O';
    
    document.getElementById('numberLetter').textContent = letter + '-' + calledNumber;
    
    // Special ball indicator
    if (isSpecialBall) {
        document.getElementById('specialBallIndicator').innerHTML = 
            `<span class="text-gold-400 carnival-glow">✨ ${specialType.toUpperCase()} BALL! ${specialMultiplier}x ✨</span>`;
        bingoBlitzGame.carnivalBonus += specialMultiplier * 5;
    } else {
        document.getElementById('specialBallIndicator').textContent = '';
    }
    
    // Add to history
    const historyContainer = document.getElementById('calledNumbersHistory');
    const numberSpan = document.createElement('span');
    numberSpan.className = `px-2 py-1 rounded text-xs ${isSpecialBall ? 'bg-gold-500 text-black' : 'bg-purple-500 text-white'}`;
    numberSpan.textContent = letter + calledNumber;
    historyContainer.appendChild(numberSpan);
    
    // Keep only last 10 numbers visible
    while (historyContainer.children.length > 10) {
        historyContainer.removeChild(historyContainer.firstChild);
    }
    
    // Update progress
    const progress = ((75 - bingoBlitzGame.numbersLeft) / 75) * 100;
    document.getElementById('gameProgressBar').style.width = progress + '%';
    document.getElementById('numbersCalledCount').textContent = `${75 - bingoBlitzGame.numbersLeft}/75`;
    
    // Start reaction timer for this number
    bingoBlitzGame.numberCallTime = Date.now();
    
    // Check if player has this number
    if (bingoBlitzGame.bingoCard.includes(calledNumber)) {
        document.getElementById('bingoStatus').innerHTML = 
            `<span class="text-yellow-400 carnival-glow">🎯 ${letter}-${calledNumber} called! Mark it quickly! 🎯</span>`;
    } else {
        document.getElementById('bingoStatus').textContent = `${letter}-${calledNumber} called!`;
    }
    
    updateBingoDisplay();
    
    // Schedule next number
    bingoBlitzGame.gameTimer = setTimeout(() => {
        // Check if number was missed
        if (bingoBlitzGame.bingoCard.includes(calledNumber) && !bingoBlitzGame.markedNumbers.has(calledNumber)) {
            bingoBlitzGame.missedCalls++;
            bingoBlitzGame.crowdExcitement = Math.max(0, bingoBlitzGame.crowdExcitement - 10);
        }
        
        callNextNumber();
    }, bingoBlitzGame.callSpeed);
}

function markNumber(number, cell) {
    if (!bingoBlitzGame.isPlaying || bingoBlitzGame.markedNumbers.has(number)) return;
    
    // Check if this number was called
    if (!bingoBlitzGame.calledNumbers.includes(number)) {
        // Wrong number marked - penalty
        document.getElementById('bingoStatus').innerHTML = 
            `<span class="text-red-400">❌ ${number} not called yet! Penalty! ❌</span>`;
        bingoBlitzGame.crowdExcitement = Math.max(0, bingoBlitzGame.crowdExcitement - 15);
        return;
    }
    
    // Calculate reaction time
    const reactionTime = (Date.now() - bingoBlitzGame.numberCallTime) / 1000;
    bingoBlitzGame.reactionTime.push(reactionTime);
    
    // Update reaction display
    document.getElementById('reactionTimer').textContent = reactionTime.toFixed(2) + 's';
    
    // Mark as perfect if under 1 second
    if (reactionTime < 1.0) {
        bingoBlitzGame.perfectMarks++;
        bingoBlitzGame.crowdExcitement = Math.min(100, bingoBlitzGame.crowdExcitement + 5);
        cell.classList.add('perfect-mark');
    }
    
    // Mark the number
    bingoBlitzGame.markedNumbers.add(number);
    cell.classList.add('bg-yellow-500', 'border-yellow-400', 'marked');
    cell.style.transform = 'scale(1.1)';
    setTimeout(() => cell.style.transform = 'scale(1)', 200);
    
    // Check for winning pattern
    if (checkWinningPattern()) {
        endBingoGame(true, 'BINGO! Pattern completed!');
    }
    
    updateBingoDisplay();
}

function checkWinningPattern() {
    const pattern = BINGO_PATTERNS.find(p => p.name === bingoBlitzGame.selectedPattern);
    const card = bingoBlitzGame.bingoCard;
    const marked = bingoBlitzGame.markedNumbers;
    
    switch (pattern.name) {
        case 'line':
            return checkLines(card, marked);
        case 'corners':
            return checkCorners(card, marked);
        case 'x':
            return checkXPattern(card, marked);
        case 'plus':
            return checkPlusPattern(card, marked);
        case 'frame':
            return checkFrame(card, marked);
        case 'fullhouse':
            return checkFullHouse(card, marked);
        default:
            return false;
    }
}

function checkLines(card, marked) {
    // Check rows
    for (let row = 0; row < 5; row++) {
        let lineComplete = true;
        for (let col = 0; col < 5; col++) {
            const index = row * 5 + col + 5; // +5 for header row
            const number = card[index - 5];
            if (!marked.has(number)) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) return true;
    }
    
    // Check columns
    for (let col = 0; col < 5; col++) {
        let lineComplete = true;
        for (let row = 0; row < 5; row++) {
            const index = row * 5 + col;
            const number = card[index];
            if (!marked.has(number)) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) return true;
    }
    
    // Check diagonals
    let diag1 = true, diag2 = true;
    for (let i = 0; i < 5; i++) {
        if (!marked.has(card[i * 5 + i])) diag1 = false;
        if (!marked.has(card[i * 5 + (4 - i)])) diag2 = false;
    }
    
    return diag1 || diag2;
}

function checkCorners(card, marked) {
    const corners = [card[0], card[4], card[20], card[24]]; // Four corners
    return corners.every(num => marked.has(num));
}

function checkXPattern(card, marked) {
    const xPositions = [0, 4, 6, 8, 12, 16, 18, 20, 24]; // X pattern positions
    return xPositions.every(pos => marked.has(card[pos]));
}

function checkPlusPattern(card, marked) {
    const plusPositions = [2, 10, 11, 12, 13, 14, 22]; // Plus pattern positions
    return plusPositions.every(pos => marked.has(card[pos]));
}

function checkFrame(card, marked) {
    const framePositions = [
        0, 1, 2, 3, 4,     // Top row
        5, 9,              // Left and right of second row
        10, 14,            // Left and right of third row
        15, 19,            // Left and right of fourth row
        20, 21, 22, 23, 24 // Bottom row
    ];
    return framePositions.every(pos => marked.has(card[pos]));
}

function checkFullHouse(card, marked) {
    return card.every(num => marked.has(num));
}

function endBingoGame(won, message) {
    bingoBlitzGame.isPlaying = false;
    bingoBlitzGame.gamePhase = 'finished';
    
    if (bingoBlitzGame.gameTimer) {
        clearTimeout(bingoBlitzGame.gameTimer);
    }
    
    if (won) {
        // Calculate winnings - very strict requirements
        const patternData = BINGO_PATTERNS.find(p => p.name === bingoBlitzGame.selectedPattern);
        const modeData = CARNIVAL_MODES.find(m => m.name === bingoBlitzGame.gameMode);
        
        // Calculate average reaction time
        const avgReaction = bingoBlitzGame.reactionTime.length > 0 ? 
            bingoBlitzGame.reactionTime.reduce((a, b) => a + b, 0) / bingoBlitzGame.reactionTime.length : 0;
        
        // Very strict winning conditions
        const perfectRatio = bingoBlitzGame.perfectMarks / Math.max(1, bingoBlitzGame.reactionTime.length);
        const missRatio = bingoBlitzGame.missedCalls / Math.max(1, bingoBlitzGame.calledNumbers.length);
        
        // Must have 80%+ perfect marks, <10% missed calls, and <1.5s average reaction
        if (perfectRatio >= 0.8 && missRatio < 0.1 && avgReaction < 1.5) {
            let baseMultiplier = patternData.multiplier * modeData.bonus;
            
            // Bonuses
            const reactionBonus = Math.max(0, (1.5 - avgReaction) * 2);
            const perfectBonus = perfectRatio * 3;
            const carnivalBonus = bingoBlitzGame.carnivalBonus / 100;
            const specialBonus = bingoBlitzGame.specialBalls * 0.5;
            
            const totalMultiplier = baseMultiplier * (1 + reactionBonus + perfectBonus + carnivalBonus + specialBonus);
            const winnings = Math.floor(bingoBlitzGame.betAmount * totalMultiplier * 0.12); // Very low base
            
            balance += winnings;
            updateBalance();
            
            bingoBlitzGame.consecutiveWins++;
            bingoBlitzGame.perfectGames++;
            bingoBlitzGame.blitzLevel++;
            bingoBlitzGame.crowdExcitement = 100;
            
            document.getElementById('bingoResult').innerHTML = 
                `<span class="text-green-400 neon-glow">🎪 CARNIVAL BINGO CHAMPION! 🎪</span>`;
            document.getElementById('bingoStatus').innerHTML = 
                `${message} Perfect carnival performance! Won ${winnings} GA!`;
        } else {
            document.getElementById('bingoResult').innerHTML = 
                `<span class="text-yellow-400">🎯 BINGO BUT NOT PERFECT! 🎯</span>`;
            document.getElementById('bingoStatus').innerHTML = 
                `${message} Need 80%+ perfect marks, <10% misses, <1.5s avg reaction to win!`;
        }
    } else {
        bingoBlitzGame.consecutiveWins = 0;
        bingoBlitzGame.crowdExcitement = Math.max(0, bingoBlitzGame.crowdExcitement - 20);
        
        document.getElementById('bingoResult').innerHTML = 
            `<span class="text-red-400">💥 CARNIVAL OVER! 💥</span>`;
        document.getElementById('bingoStatus').textContent = message;
    }
    
    // Update max streak
    bingoBlitzGame.maxStreak = Math.max(bingoBlitzGame.maxStreak, bingoBlitzGame.consecutiveWins);
    
    updateBingoDisplay();
    resetBingoControls();
}

function resetBingoControls() {
    setTimeout(() => {
        document.getElementById('startBingo').disabled = false;
        document.getElementById('currentNumberDisplay').textContent = '--';
        document.getElementById('numberLetter').textContent = 'BINGO';
        document.getElementById('specialBallIndicator').textContent = '';
        document.getElementById('calledNumbersHistory').innerHTML = '';
        document.getElementById('gameProgressBar').style.width = '0%';
        document.getElementById('numbersCalledCount').textContent = '0/75';
        document.getElementById('reactionTimer').textContent = '0.00s';
        document.getElementById('bingoStatus').textContent = 'Ready for carnival bingo!';
        
        // Reset card
        generateBingoCard();
    }, 5000);
}

function updateBingoDisplay() {
    document.getElementById('blitzLevel').textContent = bingoBlitzGame.blitzLevel;
    document.getElementById('consecutiveWins').textContent = bingoBlitzGame.consecutiveWins;
    document.getElementById('perfectGames').textContent = bingoBlitzGame.perfectGames;
    document.getElementById('maxStreak').textContent = bingoBlitzGame.maxStreak;
    document.getElementById('specialBalls').textContent = bingoBlitzGame.specialBalls;
    document.getElementById('carnivalBonus').textContent = Math.floor(bingoBlitzGame.carnivalBonus) + '%';
    document.getElementById('musicLevel').textContent = bingoBlitzGame.musicLevel;
    document.getElementById('perfectMarks').textContent = bingoBlitzGame.perfectMarks;
    document.getElementById('missedCalls').textContent = bingoBlitzGame.missedCalls;
    document.getElementById('crowdExcitement').textContent = Math.floor(bingoBlitzGame.crowdExcitement) + '%';
    
    // Calculate average reaction time
    if (bingoBlitzGame.reactionTime.length > 0) {
        const avg = bingoBlitzGame.reactionTime.reduce((a, b) => a + b, 0) / bingoBlitzGame.reactionTime.length;
        document.getElementById('avgReaction').textContent = avg.toFixed(2) + 's';
    }
    
    // Update carnival energy
    const energy = Math.min(100, (bingoBlitzGame.blitzLevel / 20) * 100 + bingoBlitzGame.crowdExcitement);
    document.getElementById('carnivalEnergyBar').style.width = energy + '%';
    
    let energyStatus = 'Carnival Starting...';
    if (energy >= 100) energyStatus = 'MAXIMUM CARNIVAL ENERGY!';
    else if (energy >= 75) energyStatus = 'High Carnival Energy';
    else if (energy >= 50) energyStatus = 'Carnival Building';
    else if (energy >= 25) energyStatus = 'Carnival Warming Up';
    
    document.getElementById('carnivalEnergyText').textContent = energyStatus;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBingoBlitzGame();
});