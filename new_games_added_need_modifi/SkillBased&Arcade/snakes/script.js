// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadSnakesGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                            <h4 class="text-xl font-bold mb-4 text-green-400">CYBER SNAKES</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="snakesBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">AUTO CASHOUT AT</label>
                                <input type="number" id="snakesAutoCashout" value="2.00" min="1.01" step="0.01" 
                                       class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                                <div class="text-xs text-gray-400 mt-1">Leave empty for manual cashout</div>
                            </div>
                            
                            <button id="playSnakes" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                RIDE THE SNAKE
                            </button>
                            
                            <button id="cashoutSnakes" class="w-full py-3 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white mb-4" disabled>
                                CASH OUT
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Multiplier</div>
                                <div id="snakesMultiplier" class="text-3xl font-bold text-green-400 neon-glow">1.00x</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="snakesPotentialWin" class="text-xl font-bold text-yellow-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Recent Results -->
                        <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-green-400">RECENT CRASHES</h5>
                            <div id="snakesHistory" class="space-y-2 max-h-40 overflow-y-auto">
                                <!-- Recent results will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Game Display -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                            <div id="snakesDisplay" class="relative bg-black/50 rounded-lg p-4 h-96 overflow-hidden">
                                <canvas id="snakesCanvas" width="400" height="350" class="w-full h-full"></canvas>
                            </div>
                            <div id="snakesStatus" class="text-center mt-4 text-lg font-semibold">Ready to ride the snake</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeSnakes();
        }
        
        let snakesGame = {
            canvas: null,
            ctx: null,
            isPlaying: false,
            multiplier: 1.00,
            betAmount: 0,
            startTime: 0,
            crashPoint: 0,
            autoCashout: 0,
            animationId: null
        };
        
        function initializeSnakes() {
            snakesGame.canvas = document.getElementById('snakesCanvas');
            snakesGame.ctx = snakesGame.canvas.getContext('2d');
            
            drawSnakesGame();
            
            document.getElementById('playSnakes').addEventListener('click', startSnakesGame);
            document.getElementById('cashoutSnakes').addEventListener('click', cashoutSnakes);
        }
        
        function drawSnakesGame() {
            const ctx = snakesGame.ctx;
            const canvas = snakesGame.canvas;
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid
            ctx.strokeStyle = '#9945ff20';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 20) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 20) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            if (snakesGame.isPlaying) {
                // Draw snake curve
                const progress = (Date.now() - snakesGame.startTime) / 1000;
                const curve = Math.log(snakesGame.multiplier) * 50;
                
                ctx.strokeStyle = '#39ff14';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(0, canvas.height - 50);
                
                for (let x = 0; x < progress * 50 && x < canvas.width; x += 2) {
                    const y = canvas.height - 50 - (Math.log(1 + x / 50) * 50);
                    ctx.lineTo(x, Math.max(0, y));
                }
                ctx.stroke();
                
                // Draw snake head
                const headX = Math.min(progress * 50, canvas.width - 20);
                const headY = canvas.height - 50 - (Math.log(1 + headX / 50) * 50);
                
                ctx.fillStyle = '#39ff14';
                ctx.beginPath();
                ctx.arc(headX, Math.max(10, headY), 8, 0, Math.PI * 2);
                ctx.fill();
                
                // Snake eyes
                ctx.fillStyle = '#ff2d92';
                ctx.beginPath();
                ctx.arc(headX - 3, Math.max(7, headY - 3), 2, 0, Math.PI * 2);
                ctx.arc(headX + 3, Math.max(7, headY - 3), 2, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // Draw multiplier text
            ctx.fillStyle = '#39ff14';
            ctx.font = 'bold 24px monospace';
            ctx.textAlign = 'center';
            ctx.fillText(`${snakesGame.multiplier.toFixed(2)}x`, canvas.width / 2, 40);
            
            if (snakesGame.isPlaying && snakesGame.multiplier >= snakesGame.crashPoint) {
                // Snake crashed
                ctx.fillStyle = '#ff2d92';
                ctx.font = 'bold 48px monospace';
                ctx.fillText('CRASHED!', canvas.width / 2, canvas.height / 2);
            }
        }
        
        function startSnakesGame() {
            const betAmount = parseInt(document.getElementById('snakesBet').value);
            const autoCashout = parseFloat(document.getElementById('snakesAutoCashout').value) || 0;
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize game state
            snakesGame.isPlaying = true;
            snakesGame.multiplier = 1.00;
            snakesGame.betAmount = betAmount;
            snakesGame.startTime = Date.now();
            snakesGame.autoCashout = autoCashout;
            snakesGame.crashPoint = generateCrashPoint();
            
            // Update UI
            document.getElementById('playSnakes').disabled = true;
            document.getElementById('cashoutSnakes').disabled = false;
            document.getElementById('snakesStatus').textContent = 'Snake is rising... Cash out before it crashes!';
            
            animateSnakes();
        }
        
        function generateCrashPoint() {
            // Generate crash point using exponential distribution
            // 99% of crashes happen before 100x
            const random = Math.random();
            return Math.max(1.01, Math.pow(1 / random, 0.15));
        }
        
        function animateSnakes() {
            function animate() {
                if (!snakesGame.isPlaying) return;
                
                const elapsed = (Date.now() - snakesGame.startTime) / 1000;
                snakesGame.multiplier = 1 + elapsed * 0.5 + Math.pow(elapsed, 2) * 0.1;
                
                // Update display
                document.getElementById('snakesMultiplier').textContent = snakesGame.multiplier.toFixed(2) + 'x';
                document.getElementById('snakesPotentialWin').textContent = 
                    '$' + Math.floor(snakesGame.betAmount * snakesGame.multiplier);
                
                // Check for crash
                if (snakesGame.multiplier >= snakesGame.crashPoint) {
                    crashSnakes();
                    return;
                }
                
                // Check for auto cashout
                if (snakesGame.autoCashout > 0 && snakesGame.multiplier >= snakesGame.autoCashout) {
                    cashoutSnakes();
                    return;
                }
                
                drawSnakesGame();
                snakesGame.animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        function cashoutSnakes() {
            if (!snakesGame.isPlaying) return;
            
            const winnings = Math.floor(snakesGame.betAmount * snakesGame.multiplier);
            balance += winnings;
            updateBalance();
            
            endSnakesGame(true, winnings);
        }
        
        function crashSnakes() {
            endSnakesGame(false, 0);
        }
        
        function endSnakesGame(won, winnings) {
            snakesGame.isPlaying = false;
            if (snakesGame.animationId) {
                cancelAnimationFrame(snakesGame.animationId);
            }
            
            // Update UI
            document.getElementById('playSnakes').disabled = false;
            document.getElementById('cashoutSnakes').disabled = true;
            
            if (won) {
                document.getElementById('snakesStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Cashed out at ${snakesGame.multiplier.toFixed(2)}x! Won $${winnings}</span>`;
            } else {
                document.getElementById('snakesStatus').innerHTML = 
                    `<span class="text-red-400">Snake crashed at ${snakesGame.crashPoint.toFixed(2)}x!</span>`;
            }
            
            // Add to history
            addSnakesHistory(snakesGame.multiplier >= snakesGame.crashPoint ? snakesGame.crashPoint : snakesGame.multiplier, won);
            
            drawSnakesGame();
            
            // Reset after delay
            setTimeout(() => {
                snakesGame.multiplier = 1.00;
                document.getElementById('snakesMultiplier').textContent = '1.00x';
                document.getElementById('snakesPotentialWin').textContent = '0 GA';
                document.getElementById('snakesStatus').textContent = 'Ready to ride the snake';
                drawSnakesGame();
            }, 3000);
        }
        
        function addSnakesHistory(multiplier, won) {
            const history = document.getElementById('snakesHistory');
            const result = document.createElement('div');
            result.className = `flex justify-between items-center py-1 px-2 rounded ${
                won ? 'bg-green-600/20' : 'bg-red-600/20'
            }`;
            result.innerHTML = `
                <span class="${won ? 'text-green-400' : 'text-red-400'}">${multiplier.toFixed(2)}x</span>
                <span class="text-xs text-gray-400">${won ? 'WIN' : 'CRASH'}</span>
            `;
            
            history.insertBefore(result, history.firstChild);
            
            // Keep only last 10 results
            while (history.children.length > 10) {
                history.removeChild(history.lastChild);
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSnakesGame();
});