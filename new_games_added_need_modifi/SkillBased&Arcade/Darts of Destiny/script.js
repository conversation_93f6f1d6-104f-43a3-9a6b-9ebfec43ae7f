// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Darts of Destiny Game Implementation
const DART_TARGETS = {
    bullseye: { points: 50, multiplier: 10, radius: 15, color: '#ff0000' },
    inner: { points: 25, multiplier: 5, radius: 35, color: '#00ff00' },
    middle: { points: 15, multiplier: 3, radius: 55, color: '#ffff00' },
    outer: { points: 10, multiplier: 2, radius: 75, color: '#0080ff' },
    edge: { points: 5, multiplier: 1, radius: 95, color: '#ff8000' },
    miss: { points: 0, multiplier: 0, radius: 120, color: '#666666' }
};

const GAME_MODES = [
    { name: 'classic', description: 'Standard 3 dart throws', darts: 3, multiplier: 1.0, timeLimit: 30 },
    { name: 'precision', description: 'Single perfect throw', darts: 1, multiplier: 2.5, timeLimit: 15 },
    { name: 'rapid', description: '5 quick throws', darts: 5, multiplier: 0.8, timeLimit: 20 },
    { name: 'marathon', description: '10 dart endurance', darts: 10, multiplier: 1.5, timeLimit: 60 },
    { name: 'destiny', description: 'Ultimate challenge', darts: 7, multiplier: 3.0, timeLimit: 45 }
];

const DIFFICULTY_LEVELS = [
    { name: 'novice', description: 'Beginner friendly', aimAssist: 0.8, windEffect: 0.2, multiplier: 0.7 },
    { name: 'amateur', description: 'Casual player', aimAssist: 0.6, windEffect: 0.4, multiplier: 1.0 },
    { name: 'professional', description: 'Skilled thrower', aimAssist: 0.3, windEffect: 0.6, multiplier: 1.5 },
    { name: 'master', description: 'Expert level', aimAssist: 0.1, windEffect: 0.8, multiplier: 2.0 },
    { name: 'legendary', description: 'Destiny awaits', aimAssist: 0.0, windEffect: 1.0, multiplier: 3.0 }
];

let dartsGame = {
    isPlaying: false,
    gameMode: 'classic',
    difficulty: 'amateur',
    betAmount: 0,
    currentDart: 0,
    totalDarts: 3,
    score: 0,
    throws: [],
    timeLeft: 30,
    timer: null,
    canvas: null,
    ctx: null,
    isAiming: false,
    aimX: 0,
    aimY: 0,
    powerLevel: 0,
    isCharging: false,
    windDirection: 0,
    windStrength: 0,
    perfectThrows: 0,
    consecutiveHits: 0,
    maxCombo: 0,
    dartsLevel: 1,
    destinyPoints: 0,
    legendaryThrows: 0,
    bullseyeStreak: 0,
    totalGames: 0,
    perfectGames: 0,
    animationId: null
};

function loadDartsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎯 DARTS OF DESTINY 🎯</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 DART STAKE</label>
                        <input type="number" id="dartsBet" value="25" min="10" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${GAME_MODES.map(mode => 
                                `<option value="${mode.name}">${mode.name.toUpperCase()} - ${mode.description}</option>`
                            ).join('')}
                        </select>
                        <div id="modeInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${DIFFICULTY_LEVELS.map(diff => 
                                `<option value="${diff.name}">${diff.name.toUpperCase()} - ${diff.description}</option>`
                            ).join('')}
                        </select>
                        <div id="difficultyInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <button id="startDarts" class="w-full cyber-button px-4 py-3 rounded-lg font-semibold text-white mb-4">
                        THROW DARTS
                    </button>
                    
                    <div class="text-center mb-4">
                        <div id="dartsTimer" class="text-2xl font-bold text-cyan-400 mb-2">0:30</div>
                        <div id="dartsStatus" class="text-sm text-gray-300">Ready to throw darts of destiny</div>
                    </div>
                    
                    <!-- Wind Indicator -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 mb-4">
                        <div class="text-sm text-gray-300 mb-2">🌪️ WIND CONDITIONS</div>
                        <div class="flex items-center justify-between">
                            <span id="windDirection" class="text-cyan-400">→</span>
                            <div class="flex-1 mx-2">
                                <div class="bg-gray-700 rounded-full h-2">
                                    <div id="windStrengthBar" class="bg-red-500 h-2 rounded-full" style="width: 0%"></div>
                                </div>
                            </div>
                            <span id="windStrength" class="text-yellow-400">0%</span>
                        </div>
                    </div>
                    
                    <!-- Power Meter -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20">
                        <div class="text-sm text-gray-300 mb-2">💪 THROW POWER</div>
                        <div class="bg-gray-700 rounded-full h-4 mb-2">
                            <div id="powerMeter" class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-4 rounded-full transition-all" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <span id="powerLevel" class="text-white font-bold">0%</span>
                        </div>
                    </div>
                </div>
                
                <!-- Throw History -->
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 THROW HISTORY</h5>
                    <div id="throwHistory" class="space-y-2 max-h-48 overflow-y-auto">
                        <!-- Throw results will appear here -->
                    </div>
                </div>
            </div>
            
            <!-- Dartboard -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="flex justify-between items-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400">🎯 DARTBOARD</h5>
                        <div class="text-right">
                            <div class="text-sm text-gray-300">Dart: <span id="currentDart" class="text-cyan-400">0</span>/<span id="totalDarts">3</span></div>
                            <div class="text-sm text-gray-300">Score: <span id="currentScore" class="text-yellow-400">0</span></div>
                        </div>
                    </div>
                    
                    <div class="relative bg-black/50 rounded-lg p-4 flex items-center justify-center" style="min-height: 500px;">
                        <canvas id="dartsCanvas" width="400" height="400" class="border border-purple-500/30 rounded-lg cursor-crosshair"></canvas>
                        
                        <!-- Aim Crosshair -->
                        <div id="aimCrosshair" class="absolute pointer-events-none hidden">
                            <div class="w-6 h-6 border-2 border-red-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <div id="throwResult" class="text-lg font-semibold mb-2"></div>
                        <div id="gameInstructions" class="text-sm text-gray-400">
                            Click to aim, hold SPACE to charge power, release to throw!
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Stats Panel -->
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mt-8">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-purple-400" id="dartsLevel">1</div>
                <div class="text-sm text-gray-300">Darts Level</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-cyan-400" id="destinyPoints">0</div>
                <div class="text-sm text-gray-300">Destiny Points</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-green-400" id="perfectThrows">0</div>
                <div class="text-sm text-gray-300">Perfect Throws</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-yellow-400" id="bullseyeStreak">0</div>
                <div class="text-sm text-gray-300">Bullseye Streak</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-red-400" id="legendaryThrows">0</div>
                <div class="text-sm text-gray-300">Legendary Throws</div>
            </div>
        </div>
    `;
    
    setupDartsGame();
}

function setupDartsGame() {
    dartsGame.canvas = document.getElementById('dartsCanvas');
    dartsGame.ctx = dartsGame.canvas.getContext('2d');
    
    // Event listeners
    document.getElementById('startDarts').addEventListener('click', startDartsGame);
    document.getElementById('gameMode').addEventListener('change', updateGameMode);
    document.getElementById('difficulty').addEventListener('change', updateDifficulty);
    
    // Canvas events
    dartsGame.canvas.addEventListener('mousemove', handleMouseMove);
    dartsGame.canvas.addEventListener('click', handleCanvasClick);
    
    // Keyboard events
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    updateGameMode();
    updateDifficulty();
    generateWindConditions();
    drawDartboard();
    updateDartsDisplay();
}

function updateGameMode() {
    const mode = document.getElementById('gameMode').value;
    dartsGame.gameMode = mode;
    
    const modeData = GAME_MODES.find(m => m.name === mode);
    dartsGame.totalDarts = modeData.darts;
    dartsGame.timeLeft = modeData.timeLimit;
    
    document.getElementById('modeInfo').textContent = 
        `${modeData.darts} darts, ${modeData.timeLimit}s, ${modeData.multiplier}x multiplier`;
    document.getElementById('totalDarts').textContent = modeData.darts;
    document.getElementById('dartsTimer').textContent = `0:${modeData.timeLimit.toString().padStart(2, '0')}`;
}

function updateDifficulty() {
    const difficulty = document.getElementById('difficulty').value;
    dartsGame.difficulty = difficulty;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    document.getElementById('difficultyInfo').textContent = 
        `Aim assist: ${Math.floor(diffData.aimAssist * 100)}%, Wind: ${Math.floor(diffData.windEffect * 100)}%, ${diffData.multiplier}x multiplier`;
}

function generateWindConditions() {
    // Generate random wind
    dartsGame.windDirection = Math.random() * 360;
    dartsGame.windStrength = Math.random() * 0.8 + 0.1; // 10-90%
    
    // Update wind display
    const windArrows = ['↑', '↗', '→', '↘', '↓', '↙', '←', '↖'];
    const arrowIndex = Math.floor(dartsGame.windDirection / 45);
    document.getElementById('windDirection').textContent = windArrows[arrowIndex];
    document.getElementById('windStrength').textContent = Math.floor(dartsGame.windStrength * 100) + '%';
    document.getElementById('windStrengthBar').style.width = (dartsGame.windStrength * 100) + '%';
}

function startDartsGame() {
    const bet = parseInt(document.getElementById('dartsBet').value);
    
    if (bet > balance) {
        document.getElementById('dartsStatus').textContent = 'Insufficient balance for dart throwing!';
        return;
    }
    
    if (dartsGame.isPlaying) return;
    
    // Deduct bet
    balance -= bet;
    dartsGame.betAmount = bet;
    updateBalance();
    
    // Initialize game
    dartsGame.isPlaying = true;
    dartsGame.currentDart = 0;
    dartsGame.score = 0;
    dartsGame.throws = [];
    dartsGame.consecutiveHits = 0;
    dartsGame.totalGames++;
    
    const modeData = GAME_MODES.find(m => m.name === dartsGame.gameMode);
    dartsGame.timeLeft = modeData.timeLimit;
    dartsGame.totalDarts = modeData.darts;
    
    // Generate new wind conditions
    generateWindConditions();
    
    // Start timer
    startDartsTimer();
    
    // Update UI
    document.getElementById('startDarts').disabled = true;
    document.getElementById('dartsStatus').textContent = 'Aim carefully and throw your darts!';
    document.getElementById('gameInstructions').innerHTML = 
        '<span class="text-yellow-400">Click to aim → Hold SPACE to charge → Release to throw!</span>';
    
    updateDartsDisplay();
    drawDartboard();
}

function handleMouseMove(e) {
    if (!dartsGame.isPlaying) return;
    
    const rect = dartsGame.canvas.getBoundingClientRect();
    dartsGame.aimX = e.clientX - rect.left;
    dartsGame.aimY = e.clientY - rect.top;
    
    // Show crosshair
    const crosshair = document.getElementById('aimCrosshair');
    crosshair.style.left = (rect.left + dartsGame.aimX - 12) + 'px';
    crosshair.style.top = (rect.top + dartsGame.aimY - 12) + 'px';
    crosshair.classList.remove('hidden');
}

function handleCanvasClick(e) {
    if (!dartsGame.isPlaying || dartsGame.isCharging) return;
    
    const rect = dartsGame.canvas.getBoundingClientRect();
    dartsGame.aimX = e.clientX - rect.left;
    dartsGame.aimY = e.clientY - rect.top;
    
    dartsGame.isAiming = true;
    document.getElementById('gameInstructions').innerHTML = 
        '<span class="text-cyan-400">Hold SPACE to charge power, release to throw!</span>';
}

function handleKeyDown(e) {
    if (!dartsGame.isPlaying || !dartsGame.isAiming) return;
    
    if (e.code === 'Space' && !dartsGame.isCharging) {
        e.preventDefault();
        dartsGame.isCharging = true;
        chargePower();
    }
}

function handleKeyUp(e) {
    if (!dartsGame.isPlaying || !dartsGame.isCharging) return;
    
    if (e.code === 'Space') {
        e.preventDefault();
        dartsGame.isCharging = false;
        throwDart();
    }
}

function chargePower() {
    if (!dartsGame.isCharging) return;
    
    dartsGame.powerLevel = Math.min(100, dartsGame.powerLevel + 2);
    document.getElementById('powerLevel').textContent = dartsGame.powerLevel + '%';
    document.getElementById('powerMeter').style.width = dartsGame.powerLevel + '%';
    
    // Power oscillation for difficulty
    if (dartsGame.powerLevel >= 100) {
        dartsGame.powerLevel = 0;
    }
    
    requestAnimationFrame(chargePower);
}

function throwDart() {
    if (dartsGame.currentDart >= dartsGame.totalDarts) return;
    
    dartsGame.currentDart++;
    dartsGame.isAiming = false;
    
    // Calculate throw accuracy with difficulty modifiers
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === dartsGame.difficulty);
    
    // Apply wind effect
    const windEffect = dartsGame.windStrength * diffData.windEffect;
    const windX = Math.cos(dartsGame.windDirection * Math.PI / 180) * windEffect * 20;
    const windY = Math.sin(dartsGame.windDirection * Math.PI / 180) * windEffect * 20;
    
    // Apply power accuracy (optimal power is 70-85%)
    const powerAccuracy = 1 - Math.abs(dartsGame.powerLevel - 77.5) / 77.5;
    
    // Apply aim assist
    const centerX = dartsGame.canvas.width / 2;
    const centerY = dartsGame.canvas.height / 2;
    const aimAssist = diffData.aimAssist;
    
    const finalX = dartsGame.aimX + windX + (centerX - dartsGame.aimX) * aimAssist * (1 - powerAccuracy);
    const finalY = dartsGame.aimY + windY + (centerY - dartsGame.aimY) * aimAssist * (1 - powerAccuracy);
    
    // Calculate distance from center
    const distance = Math.sqrt(Math.pow(finalX - centerX, 2) + Math.pow(finalY - centerY, 2));
    
    // Determine hit result
    let hitResult = null;
    for (const [key, target] of Object.entries(DART_TARGETS)) {
        if (distance <= target.radius) {
            hitResult = { type: key, ...target };
            break;
        }
    }
    
    if (!hitResult) {
        hitResult = DART_TARGETS.miss;
        hitResult.type = 'miss';
    }
    
    // Record throw
    const throwData = {
        x: finalX,
        y: finalY,
        distance: distance,
        result: hitResult,
        power: dartsGame.powerLevel,
        windEffect: windEffect
    };
    
    dartsGame.throws.push(throwData);
    dartsGame.score += hitResult.points;
    
    // Update streaks and stats
    if (hitResult.type === 'bullseye') {
        dartsGame.bullseyeStreak++;
        dartsGame.perfectThrows++;
        dartsGame.consecutiveHits++;
        dartsGame.destinyPoints += 50;
        
        if (dartsGame.powerLevel >= 70 && dartsGame.powerLevel <= 85) {
            dartsGame.legendaryThrows++;
            dartsGame.destinyPoints += 100;
        }
    } else if (hitResult.type !== 'miss') {
        dartsGame.consecutiveHits++;
        dartsGame.destinyPoints += hitResult.points;
    } else {
        dartsGame.consecutiveHits = 0;
        dartsGame.bullseyeStreak = 0;
    }
    
    dartsGame.maxCombo = Math.max(dartsGame.maxCombo, dartsGame.consecutiveHits);
    
    // Add to history
    addThrowToHistory(throwData);
    
    // Reset power
    dartsGame.powerLevel = 0;
    document.getElementById('powerLevel').textContent = '0%';
    document.getElementById('powerMeter').style.width = '0%';
    
    // Update display
    updateDartsDisplay();
    drawDartboard();
    
    // Show result
    let resultText = `Dart ${dartsGame.currentDart}: ${hitResult.type.toUpperCase()}`;
    if (hitResult.points > 0) {
        resultText += ` (+${hitResult.points} points)`;
    }
    
    document.getElementById('throwResult').innerHTML = 
        `<span class="${hitResult.type === 'bullseye' ? 'text-red-400' : hitResult.type === 'miss' ? 'text-gray-400' : 'text-green-400'}">${resultText}</span>`;
    
    // Check if game complete
    if (dartsGame.currentDart >= dartsGame.totalDarts) {
        setTimeout(() => endDartsGame(), 1500);
    } else {
        // Generate new wind for next throw
        setTimeout(() => {
            generateWindConditions();
            document.getElementById('gameInstructions').innerHTML = 
                '<span class="text-yellow-400">Click to aim → Hold SPACE to charge → Release to throw!</span>';
        }, 1500);
    }
}

function addThrowToHistory(throwData) {
    const history = document.getElementById('throwHistory');
    const throwDiv = document.createElement('div');
    throwDiv.className = `flex justify-between items-center p-2 rounded ${
        throwData.result.type === 'bullseye' ? 'bg-red-500/20 border border-red-500/30' :
        throwData.result.type === 'miss' ? 'bg-gray-500/20 border border-gray-500/30' :
        'bg-green-500/20 border border-green-500/30'
    }`;
    
    throwDiv.innerHTML = `
        <span class="text-sm">Dart ${dartsGame.currentDart}</span>
        <span class="text-sm font-bold">${throwData.result.type.toUpperCase()}</span>
        <span class="text-sm text-yellow-400">${throwData.result.points}pts</span>
    `;
    
    history.insertBefore(throwDiv, history.firstChild);
    
    // Keep only last 10 throws
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

function startDartsTimer() {
    dartsGame.timer = setInterval(() => {
        dartsGame.timeLeft--;
        
        const minutes = Math.floor(dartsGame.timeLeft / 60);
        const seconds = dartsGame.timeLeft % 60;
        document.getElementById('dartsTimer').textContent = 
            `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        // Color coding for urgency
        const timerElement = document.getElementById('dartsTimer');
        if (dartsGame.timeLeft <= 10) {
            timerElement.className = 'text-2xl font-bold text-red-400 animate-pulse';
        } else if (dartsGame.timeLeft <= 20) {
            timerElement.className = 'text-2xl font-bold text-yellow-400';
        } else {
            timerElement.className = 'text-2xl font-bold text-cyan-400';
        }
        
        if (dartsGame.timeLeft <= 0) {
            endDartsGame();
        }
    }, 1000);
}

function endDartsGame() {
    dartsGame.isPlaying = false;
    dartsGame.isAiming = false;
    dartsGame.isCharging = false;
    
    if (dartsGame.timer) {
        clearInterval(dartsGame.timer);
    }
    
    // Hide crosshair
    document.getElementById('aimCrosshair').classList.add('hidden');
    
    const modeData = GAME_MODES.find(m => m.name === dartsGame.gameMode);
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === dartsGame.difficulty);
    
    // Calculate performance metrics
    const avgScore = dartsGame.score / dartsGame.totalDarts;
    const bullseyeRatio = dartsGame.throws.filter(t => t.result.type === 'bullseye').length / dartsGame.totalDarts;
    const hitRatio = dartsGame.throws.filter(t => t.result.type !== 'miss').length / dartsGame.totalDarts;
    const perfectPowerThrows = dartsGame.throws.filter(t => t.power >= 70 && t.power <= 85).length;
    
    // Extremely strict winning conditions
    const minScore = dartsGame.totalDarts * 35; // Need avg 35+ points per dart
    const minBullseyeRatio = 0.6; // Need 60%+ bullseyes
    const minHitRatio = 0.9; // Need 90%+ hits
    const minPerfectPower = dartsGame.totalDarts * 0.7; // Need 70%+ perfect power
    
    if (dartsGame.score >= minScore && bullseyeRatio >= minBullseyeRatio && 
        hitRatio >= minHitRatio && perfectPowerThrows >= minPerfectPower) {
        
        // Calculate winnings
        let baseMultiplier = modeData.multiplier * diffData.multiplier;
        
        // Performance bonuses
        const scoreBonus = (avgScore / 50) * 2; // Up to 2x for perfect avg
        const bullseyeBonus = bullseyeRatio * 3; // Up to 3x for all bullseyes
        const comboBonus = Math.min(dartsGame.maxCombo * 0.2, 2); // Up to 2x for combos
        const powerBonus = (perfectPowerThrows / dartsGame.totalDarts) * 1.5; // Up to 1.5x for perfect power
        const timeBonus = Math.max(0, dartsGame.timeLeft / modeData.timeLimit); // Time remaining bonus
        
        const totalMultiplier = baseMultiplier * (1 + scoreBonus + bullseyeBonus + comboBonus + powerBonus + timeBonus);
        const winnings = Math.floor(dartsGame.betAmount * totalMultiplier * 0.06); // Very low base rate
        
        balance += winnings;
        updateBalance();
        
        // Update stats
        dartsGame.dartsLevel++;
        dartsGame.destinyPoints += Math.floor(winnings / 10);
        if (bullseyeRatio === 1.0) {
            dartsGame.perfectGames++;
        }
        
        document.getElementById('throwResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🎯 DESTINY ACHIEVED! 🎯</span>`;
        document.getElementById('dartsStatus').innerHTML = 
            `Perfect dart mastery! Score: ${dartsGame.score}, Won: ${winnings} GA!`;
    } else {
        document.getElementById('throwResult').innerHTML = 
            `<span class="text-red-400">💥 DESTINY DENIED! 💥</span>`;
        document.getElementById('dartsStatus').innerHTML = 
            `Score: ${dartsGame.score} - Need ${minScore}+ score, ${Math.floor(minBullseyeRatio*100)}%+ bullseyes, ${Math.floor(minHitRatio*100)}%+ hits, ${Math.floor(minPerfectPower/dartsGame.totalDarts*100)}%+ perfect power!`;
    }
    
    updateDartsDisplay();
    resetDartsControls();
}

function resetDartsControls() {
    setTimeout(() => {
        document.getElementById('startDarts').disabled = false;
        document.getElementById('throwResult').textContent = '';
        document.getElementById('dartsStatus').textContent = 'Ready to throw darts of destiny';
        document.getElementById('gameInstructions').textContent = 
            'Click to aim, hold SPACE to charge power, release to throw!';
        document.getElementById('currentDart').textContent = '0';
        document.getElementById('currentScore').textContent = '0';
        document.getElementById('powerLevel').textContent = '0%';
        document.getElementById('powerMeter').style.width = '0%';
        document.getElementById('dartsTimer').textContent = '0:30';
        document.getElementById('dartsTimer').className = 'text-2xl font-bold text-cyan-400';
        document.getElementById('throwHistory').innerHTML = '';
        
        // Reset game state
        dartsGame.currentDart = 0;
        dartsGame.score = 0;
        dartsGame.throws = [];
        dartsGame.powerLevel = 0;
        
        drawDartboard();
    }, 5000);
}

function drawDartboard() {
    const ctx = dartsGame.ctx;
    const canvas = dartsGame.canvas;
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw dartboard rings (from outside to inside)
    Object.entries(DART_TARGETS).reverse().forEach(([key, target]) => {
        if (key === 'miss') return;
        
        ctx.beginPath();
        ctx.arc(centerX, centerY, target.radius, 0, 2 * Math.PI);
        ctx.fillStyle = target.color + '40';
        ctx.fill();
        ctx.strokeStyle = target.color;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Add point labels
        if (target.radius > 20) {
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(target.points + 'pts', centerX, centerY - target.radius + 15);
        }
    });
    
    // Draw center point
    ctx.beginPath();
    ctx.arc(centerX, centerY, 3, 0, 2 * Math.PI);
    ctx.fillStyle = '#ffffff';
    ctx.fill();
    
    // Draw thrown darts
    dartsGame.throws.forEach((throwData, index) => {
        ctx.beginPath();
        ctx.arc(throwData.x, throwData.y, 4, 0, 2 * Math.PI);
        ctx.fillStyle = throwData.result.type === 'bullseye' ? '#ff0000' : 
                       throwData.result.type === 'miss' ? '#666666' : '#00ff00';
        ctx.fill();
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // Add dart number
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(index + 1, throwData.x, throwData.y - 8);
    });
}

function updateDartsDisplay() {
    document.getElementById('currentDart').textContent = dartsGame.currentDart;
    document.getElementById('currentScore').textContent = dartsGame.score;
    document.getElementById('dartsLevel').textContent = dartsGame.dartsLevel;
    document.getElementById('destinyPoints').textContent = dartsGame.destinyPoints;
    document.getElementById('perfectThrows').textContent = dartsGame.perfectThrows;
    document.getElementById('bullseyeStreak').textContent = dartsGame.bullseyeStreak;
    document.getElementById('legendaryThrows').textContent = dartsGame.legendaryThrows;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDartsGame();
});