// Game state
let balance = 1000;

// Basketball Slam game state
let basketballGame = {
    isPlaying: false,
    gamePhase: 'waiting', // waiting, aiming, shooting, finished
    currentShot: 0,
    totalShots: 0,
    successfulShots: 0,
    betAmount: 0,
    difficulty: 'rookie',
    shotType: 'freethrow',
    windSpeed: 0,
    hoopMovement: false,
    powerLevel: 0,
    angle: 45,
    isCharging: false,
    timeRemaining: 0,
    gameTimer: null,
    perfectShots: 0,
    streak: 0,
    maxStreak: 0,
    totalGames: 0,
    hoopPosition: 50, // percentage from left
    hoopDirection: 1,
    ballTrajectory: null
};

// Game constants
const DIFFICULTIES = [
    { name: 'rookie', shots: 5, timeLimit: 30, windMax: 2, hoopSpeed: 0, multiplier: 0.5 },
    { name: 'pro', shots: 8, timeLimit: 25, windMax: 5, hoopSpeed: 1, multiplier: 1.0 },
    { name: 'allstar', shots: 12, timeLimit: 20, windMax: 8, hoopSpeed: 2, multiplier: 1.5 },
    { name: 'legend', shots: 15, timeLimit: 15, windMax: 12, hoopSpeed: 3, multiplier: 2.0 }
];

const SHOT_TYPES = [
    { name: 'freethrow', difficulty: 1, bonus: 1.0, description: 'Standard free throw' },
    { name: 'threepoint', difficulty: 2, bonus: 1.5, description: '3-point shot' },
    { name: 'halfcourt', difficulty: 4, bonus: 3.0, description: 'Half-court shot' },
    { name: 'buzzer', difficulty: 6, bonus: 5.0, description: 'Buzzer beater' }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadBasketballGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">🏀 BASKETBALL SLAM</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">BET AMOUNT</label>
                        <input type="number" id="basketballBet" value="50" min="10" max="500" step="10"
                               class="w-full bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">DIFFICULTY</label>
                        <select id="basketballDifficulty" class="w-full bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2">
                            <option value="rookie">Rookie (5 shots, 30s)</option>
                            <option value="pro">Pro (8 shots, 25s)</option>
                            <option value="allstar">All-Star (12 shots, 20s)</option>
                            <option value="legend">Legend (15 shots, 15s)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">SHOT TYPE</label>
                        <select id="basketballShotType" class="w-full bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2">
                            <option value="freethrow">Free Throw (1x)</option>
                            <option value="threepoint">3-Point Shot (1.5x)</option>
                            <option value="halfcourt">Half-Court (3x)</option>
                            <option value="buzzer">Buzzer Beater (5x)</option>
                        </select>
                    </div>
                    
                    <button id="startBasketball" class="w-full cyber-button py-3 rounded-lg font-bold">
                        START SHOOTING
                    </button>
                    
                    <div class="mt-6 space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Shots Made:</span>
                            <span id="shotsMade" class="text-green-400">0/0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Perfect Shots:</span>
                            <span id="perfectShots" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Current Streak:</span>
                            <span id="currentStreak" class="text-orange-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Max Streak:</span>
                            <span id="maxStreak" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Time Left:</span>
                            <span id="timeLeft" class="text-red-400">0s</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <div id="basketballCourt" class="relative bg-gradient-to-b from-orange-900/20 to-orange-800/20 rounded-lg h-96 border-2 border-orange-500/50 overflow-hidden">
                        <!-- Basketball Court -->
                        <div class="absolute inset-0">
                            <!-- Court lines -->
                            <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-white/50"></div>
                            <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-16 bg-white/50"></div>
                            
                            <!-- Moving Hoop -->
                            <div id="basketballHoop" class="absolute top-8 w-16 h-8 transition-all duration-100" style="left: 50%;">
                                <div class="w-full h-2 bg-orange-500 rounded-full"></div>
                                <div class="w-full h-6 border-2 border-orange-500 border-t-0 rounded-b-full"></div>
                            </div>
                            
                            <!-- Ball -->
                            <div id="basketball" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-orange-500 rounded-full border-2 border-orange-700 hidden">
                                <div class="absolute inset-1 border border-orange-700 rounded-full"></div>
                                <div class="absolute top-1/2 left-0 right-0 h-0.5 bg-orange-700"></div>
                                <div class="absolute left-1/2 top-0 bottom-0 w-0.5 bg-orange-700"></div>
                            </div>
                            
                            <!-- Wind indicator -->
                            <div id="windIndicator" class="absolute top-4 left-4 text-white">
                                <div class="text-sm">Wind: <span id="windSpeed">0</span> mph</div>
                                <div id="windDirection" class="text-xs">→</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shooting Controls -->
                    <div id="shootingControls" class="mt-4 hidden">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">POWER</label>
                                <div class="relative">
                                    <div class="w-full h-4 bg-black/50 rounded-full border border-orange-500/30">
                                        <div id="powerBar" class="h-full bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 rounded-full transition-all duration-100" style="width: 50%"></div>
                                    </div>
                                    <div class="text-center mt-1 text-sm" id="powerLevel">50%</div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">ANGLE</label>
                                <div class="relative">
                                    <div class="w-full h-4 bg-black/50 rounded-full border border-orange-500/30">
                                        <div id="angleBar" class="h-full bg-blue-500 rounded-full transition-all duration-100" style="width: 45%"></div>
                                    </div>
                                    <div class="text-center mt-1 text-sm" id="angleLevel">45°</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <button id="shootBall" class="cyber-button px-8 py-3 rounded-lg font-bold">
                                🏀 SHOOT!
                            </button>
                            <div class="mt-2 text-sm text-gray-400">
                                Hold SPACE to charge power, release to shoot
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <div id="basketballStatus" class="text-lg font-semibold">Ready to dominate the court!</div>
                        <div id="basketballResult" class="text-xl font-bold mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupBasketballGame();
}

function setupBasketballGame() {
    document.getElementById('startBasketball').addEventListener('click', startBasketballGame);
    document.getElementById('shootBall').addEventListener('click', shootBasketball);
    
    // Keyboard controls
    document.addEventListener('keydown', handleBasketballKeyDown);
    document.addEventListener('keyup', handleBasketballKeyUp);
    
    updateBasketballDisplay();
}

function startBasketballGame() {
    const betAmount = parseInt(document.getElementById('basketballBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    basketballGame.isPlaying = true;
    basketballGame.gamePhase = 'aiming';
    basketballGame.betAmount = betAmount;
    basketballGame.difficulty = document.getElementById('basketballDifficulty').value;
    basketballGame.shotType = document.getElementById('basketballShotType').value;
    basketballGame.currentShot = 0;
    basketballGame.successfulShots = 0;
    basketballGame.perfectShots = 0;
    basketballGame.streak = 0;
    basketballGame.totalGames++;
    
    const diffData = DIFFICULTIES.find(d => d.name === basketballGame.difficulty);
    basketballGame.totalShots = diffData.shots;
    basketballGame.timeRemaining = diffData.timeLimit;
    
    document.getElementById('startBasketball').disabled = true;
    document.getElementById('shootingControls').classList.remove('hidden');
    
    // Start game timer
    basketballGame.gameTimer = setInterval(() => {
        basketballGame.timeRemaining--;
        if (basketballGame.timeRemaining <= 0) {
            endBasketballGame(false, 'Time\'s up!');
        }
        updateBasketballDisplay();
    }, 1000);
    
    // Start environmental effects
    startEnvironmentalEffects();
    nextShot();
}

function startEnvironmentalEffects() {
    const diffData = DIFFICULTIES.find(d => d.name === basketballGame.difficulty);
    
    // Random wind
    setInterval(() => {
        if (basketballGame.isPlaying) {
            basketballGame.windSpeed = (Math.random() - 0.5) * diffData.windMax;
            updateWindDisplay();
        }
    }, 2000);
    
    // Moving hoop
    if (diffData.hoopSpeed > 0) {
        setInterval(() => {
            if (basketballGame.isPlaying) {
                basketballGame.hoopPosition += basketballGame.hoopDirection * diffData.hoopSpeed;
                if (basketballGame.hoopPosition <= 20 || basketballGame.hoopPosition >= 80) {
                    basketballGame.hoopDirection *= -1;
                }
                document.getElementById('basketballHoop').style.left = basketballGame.hoopPosition + '%';
            }
        }, 100);
    }
}

function nextShot() {
    if (basketballGame.currentShot >= basketballGame.totalShots) {
        endBasketballGame(true, 'All shots completed!');
        return;
    }
    
    basketballGame.currentShot++;
    basketballGame.gamePhase = 'aiming';
    basketballGame.powerLevel = 50;
    basketballGame.angle = 45;
    
    document.getElementById('basketballStatus').textContent = 
        `Shot ${basketballGame.currentShot}/${basketballGame.totalShots} - Aim carefully!`;
    
    updateBasketballDisplay();
}

function handleBasketballKeyDown(e) {
    if (!basketballGame.isPlaying || basketballGame.gamePhase !== 'aiming') return;
    
    if (e.code === 'Space' && !basketballGame.isCharging) {
        e.preventDefault();
        basketballGame.isCharging = true;
        chargePower();
    }
}

function handleBasketballKeyUp(e) {
    if (!basketballGame.isPlaying || basketballGame.gamePhase !== 'aiming') return;
    
    if (e.code === 'Space' && basketballGame.isCharging) {
        e.preventDefault();
        basketballGame.isCharging = false;
        shootBasketball();
    }
}

function chargePower() {
    if (!basketballGame.isCharging) return;
    
    // Oscillating power meter
    const time = Date.now() / 100;
    basketballGame.powerLevel = 50 + Math.sin(time) * 40;
    basketballGame.angle = 45 + Math.cos(time * 0.7) * 30;
    
    updatePowerDisplay();
    
    if (basketballGame.isCharging) {
        requestAnimationFrame(chargePower);
    }
}

function shootBasketball() {
    if (basketballGame.gamePhase !== 'aiming') return;
    
    basketballGame.gamePhase = 'shooting';
    basketballGame.isCharging = false;
    
    // Calculate shot success based on multiple factors
    const shotSuccess = calculateShotSuccess();
    
    // Animate ball
    animateBasketball(shotSuccess);
}

function calculateShotSuccess() {
    const diffData = DIFFICULTIES.find(d => d.name === basketballGame.difficulty);
    const shotData = SHOT_TYPES.find(s => s.name === basketballGame.shotType);
    
    // Perfect power range (40-60%)
    const powerAccuracy = Math.max(0, 100 - Math.abs(basketballGame.powerLevel - 50) * 4);
    
    // Perfect angle range (35-55°)
    const angleAccuracy = Math.max(0, 100 - Math.abs(basketballGame.angle - 45) * 3);
    
    // Wind effect
    const windEffect = Math.max(0, 100 - Math.abs(basketballGame.windSpeed) * 8);
    
    // Hoop movement penalty
    const hoopPenalty = diffData.hoopSpeed > 0 ? Math.abs(basketballGame.hoopPosition - 50) * 2 : 0;
    
    // Time pressure
    const timePressure = basketballGame.timeRemaining < 5 ? 20 : 0;
    
    // Calculate final accuracy
    const totalAccuracy = (powerAccuracy + angleAccuracy + windEffect - hoopPenalty - timePressure) / 3;
    
    // Very strict success rate
    const baseSuccessRate = Math.max(0, totalAccuracy - 60); // Need 60%+ accuracy for any chance
    const difficultyPenalty = shotData.difficulty * 15;
    const finalSuccessRate = Math.max(0, baseSuccessRate - difficultyPenalty);
    
    const isPerfect = totalAccuracy >= 95;
    const isSuccess = Math.random() * 100 < finalSuccessRate;
    
    return { isSuccess, isPerfect, accuracy: totalAccuracy };
}

function animateBasketball(shotResult) {
    const ball = document.getElementById('basketball');
    const hoop = document.getElementById('basketballHoop');
    
    ball.classList.remove('hidden');
    
    // Calculate trajectory
    const startX = 50; // Center bottom
    const endX = basketballGame.hoopPosition;
    const windOffset = basketballGame.windSpeed * 2;
    
    let progress = 0;
    const animationDuration = 1000;
    const startTime = Date.now();
    
    function animate() {
        const elapsed = Date.now() - startTime;
        progress = elapsed / animationDuration;
        
        if (progress >= 1) {
            // Shot completed
            ball.classList.add('hidden');
            processShotResult(shotResult);
            return;
        }
        
        // Calculate ball position
        const x = startX + (endX - startX + windOffset) * progress;
        const y = 85 - Math.sin(progress * Math.PI) * 60; // Arc trajectory
        
        ball.style.left = x + '%';
        ball.style.bottom = y + '%';
        
        requestAnimationFrame(animate);
    }
    
    animate();
}

function processShotResult(shotResult) {
    if (shotResult.isSuccess) {
        basketballGame.successfulShots++;
        basketballGame.streak++;
        basketballGame.maxStreak = Math.max(basketballGame.maxStreak, basketballGame.streak);
        
        if (shotResult.isPerfect) {
            basketballGame.perfectShots++;
            document.getElementById('basketballStatus').innerHTML = 
                `<span class="text-yellow-400">🏀 PERFECT SHOT! SWISH! 🏀</span>`;
        } else {
            document.getElementById('basketballStatus').innerHTML = 
                `<span class="text-green-400">🏀 GOOD SHOT! SCORED! 🏀</span>`;
        }
    } else {
        basketballGame.streak = 0;
        document.getElementById('basketballStatus').innerHTML = 
            `<span class="text-red-400">💥 MISSED! TRY AGAIN! 💥</span>`;
    }
    
    updateBasketballDisplay();
    
    setTimeout(() => {
        if (basketballGame.currentShot >= basketballGame.totalShots) {
            endBasketballGame(true, 'All shots completed!');
        } else {
            nextShot();
        }
    }, 2000);
}

function endBasketballGame(completed, message) {
    basketballGame.isPlaying = false;
    basketballGame.gamePhase = 'finished';
    
    if (basketballGame.gameTimer) {
        clearInterval(basketballGame.gameTimer);
    }
    
    // Calculate winnings - extremely strict requirements
    let winnings = 0;
    const shotData = SHOT_TYPES.find(s => s.name === basketballGame.shotType);
    const diffData = DIFFICULTIES.find(d => d.name === basketballGame.difficulty);
    
    // Need 80%+ accuracy AND at least 3 perfect shots to win anything
    const accuracy = (basketballGame.successfulShots / basketballGame.totalShots) * 100;
    const perfectRatio = (basketballGame.perfectShots / basketballGame.totalShots) * 100;
    
    if (accuracy >= 80 && basketballGame.perfectShots >= 3 && basketballGame.streak >= 5) {
        const baseMultiplier = shotData.bonus * diffData.multiplier;
        const perfectBonus = perfectRatio / 100;
        const streakBonus = Math.min(basketballGame.maxStreak / 10, 1);
        
        const totalMultiplier = baseMultiplier * (1 + perfectBonus + streakBonus);
        winnings = Math.floor(basketballGame.betAmount * totalMultiplier * 0.1); // Very low base multiplier
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('basketballResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🏆 BASKETBALL LEGEND! 🏆</span>`;
        document.getElementById('basketballStatus').innerHTML = 
            `${message} Perfect performance! Won ${winnings} GA!`;
    } else {
        document.getElementById('basketballResult').innerHTML = 
            `<span class="text-red-400">🏀 GAME OVER! 🏀</span>`;
        document.getElementById('basketballStatus').innerHTML = 
            `${message} Need 80%+ accuracy, 3+ perfect shots, and 5+ streak to win!`;
    }
    
    updateBasketballDisplay();
    resetBasketballControls();
}

function resetBasketballControls() {
    setTimeout(() => {
        document.getElementById('startBasketball').disabled = false;
        document.getElementById('shootingControls').classList.add('hidden');
        document.getElementById('basketball').classList.add('hidden');
        document.getElementById('basketballHoop').style.left = '50%';
        basketballGame.hoopPosition = 50;
    }, 5000);
}

function updateBasketballDisplay() {
    document.getElementById('shotsMade').textContent = 
        `${basketballGame.successfulShots}/${basketballGame.totalShots}`;
    document.getElementById('perfectShots').textContent = basketballGame.perfectShots;
    document.getElementById('currentStreak').textContent = basketballGame.streak;
    document.getElementById('maxStreak').textContent = basketballGame.maxStreak;
    document.getElementById('timeLeft').textContent = basketballGame.timeRemaining + 's';
    
    updatePowerDisplay();
}

function updatePowerDisplay() {
    document.getElementById('powerBar').style.width = basketballGame.powerLevel + '%';
    document.getElementById('powerLevel').textContent = Math.round(basketballGame.powerLevel) + '%';
    document.getElementById('angleBar').style.width = (basketballGame.angle / 90) * 100 + '%';
    document.getElementById('angleLevel').textContent = Math.round(basketballGame.angle) + '°';
}

function updateWindDisplay() {
    document.getElementById('windSpeed').textContent = Math.abs(basketballGame.windSpeed).toFixed(1);
    document.getElementById('windDirection').textContent = basketballGame.windSpeed > 0 ? '→' : '←';
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBasketballGame();
});