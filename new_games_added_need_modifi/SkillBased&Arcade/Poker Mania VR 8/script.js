// Game state
let balance = 1000;

// Poker Mania VR 8 game state
let pokerManiaGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'classic',
    
    // VR Poker specific
    vrLevel: 1,
    immersionScore: 0,
    handGestures: 0,
    perfectReads: 0,
    bluffSuccess: 0,
    foldAccuracy: 0,
    
    // Poker mechanics
    playerHand: [],
    dealerHand: [],
    communityCards: [],
    currentBet: 0,
    pot: 0,
    round: 1,
    maxRounds: 8,
    
    // Performance tracking
    handsWon: 0,
    perfectHands: 0,
    bluffsAttempted: 0,
    bluffsSuccessful: 0,
    foldsMade: 0,
    correctFolds: 0,
    allInWins: 0,
    straightFlushes: 0,
    royalFlushes: 0,
    
    // VR immersion tracking
    eyeTracking: 0,
    handTracking: 0,
    voiceCommands: 0,
    gestureAccuracy: 0,
    reactionTime: 0,
    focusLevel: 0,
    
    // Opponents
    opponents: [],
    opponentCount: 7,
    
    // Special features
    vrBonus: 1.0,
    immersionMultiplier: 1.0,
    skillMultiplier: 1.0,
    timeLeft: 0
};

// Ultra-strict difficulty settings with sub-8% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        rounds: 8,
        opponentSkill: 0.6,
        timeLimit: 600,
        targetWins: 6,
        winRate: 0.08,
        multiplier: 0.7,
        blindIncrease: 1.2,
        description: 'Casual VR Poker (8% win rate)'
    },
    {
        name: 'normal',
        rounds: 8,
        opponentSkill: 0.75,
        timeLimit: 480,
        targetWins: 6,
        winRate: 0.06,
        multiplier: 1.0,
        blindIncrease: 1.5,
        description: 'Standard VR Poker (6% win rate)'
    },
    {
        name: 'hard',
        rounds: 8,
        opponentSkill: 0.85,
        timeLimit: 360,
        targetWins: 7,
        winRate: 0.04,
        multiplier: 1.4,
        blindIncrease: 1.8,
        description: 'Pro VR Poker (4% win rate)'
    },
    {
        name: 'expert',
        rounds: 8,
        opponentSkill: 0.92,
        timeLimit: 300,
        targetWins: 7,
        winRate: 0.03,
        multiplier: 1.8,
        blindIncrease: 2.0,
        description: 'Elite VR Poker (3% win rate)'
    },
    {
        name: 'legendary',
        rounds: 8,
        opponentSkill: 0.96,
        timeLimit: 240,
        targetWins: 8,
        winRate: 0.02,
        multiplier: 2.2,
        blindIncrease: 2.5,
        description: 'VR Master (2% win rate)'
    }
];

const VR_MODES = [
    {
        name: 'classic',
        description: 'Standard VR Poker',
        scoreMultiplier: 1.0,
        immersionBonus: 1.0,
        gestureRequired: 5
    },
    {
        name: 'immersive',
        description: 'Full VR Experience',
        scoreMultiplier: 1.3,
        immersionBonus: 1.5,
        gestureRequired: 10
    },
    {
        name: 'tournament',
        description: 'VR Tournament Mode',
        scoreMultiplier: 1.6,
        immersionBonus: 2.0,
        gestureRequired: 15
    }
];

const HAND_RANKINGS = [
    { name: 'High Card', value: 1, multiplier: 0.1 },
    { name: 'Pair', value: 2, multiplier: 0.2 },
    { name: 'Two Pair', value: 3, multiplier: 0.4 },
    { name: 'Three of a Kind', value: 4, multiplier: 0.6 },
    { name: 'Straight', value: 5, multiplier: 0.8 },
    { name: 'Flush', value: 6, multiplier: 1.0 },
    { name: 'Full House', value: 7, multiplier: 1.5 },
    { name: 'Four of a Kind', value: 8, multiplier: 2.0 },
    { name: 'Straight Flush', value: 9, multiplier: 3.0 },
    { name: 'Royal Flush', value: 10, multiplier: 5.0 }
];

function updatePokerDifficultySettings() {
    const difficulty = document.getElementById('pokerDifficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    pokerManiaGame.difficulty = difficulty;
    pokerManiaGame.maxRounds = diffData.rounds;
    
    document.getElementById('targetWins').textContent = diffData.targetWins;
    document.getElementById('winChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('opponentSkill').textContent = Math.floor(diffData.opponentSkill * 100) + '%';
    document.getElementById('timeLimit').textContent = Math.floor(diffData.timeLimit / 60) + 'm';
    
    // Update potential payout
    const betAmount = parseInt(document.getElementById('pokerBet').value) || 100;
    const potentialPayout = Math.floor(betAmount * diffData.multiplier * 8); // Very conservative
    document.getElementById('potentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function updatePokerModeSettings() {
    const mode = document.getElementById('pokerMode').value;
    const modeData = VR_MODES.find(m => m.name === mode);
    
    pokerManiaGame.gameMode = mode;
    
    document.getElementById('modeDescription').textContent = modeData.description;
    document.getElementById('gestureRequired').textContent = modeData.gestureRequired;
    document.getElementById('immersionBonus').textContent = Math.floor(modeData.immersionBonus * 100) + '%';
}

function startPokerManiaGame() {
    const betAmount = parseInt(document.getElementById('pokerBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pokerManiaGame.difficulty);
    const modeData = VR_MODES.find(m => m.name === pokerManiaGame.gameMode);
    
    // Reset game state
    pokerManiaGame.isPlaying = true;
    pokerManiaGame.gamePhase = 'playing';
    pokerManiaGame.betAmount = betAmount;
    pokerManiaGame.round = 1;
    pokerManiaGame.pot = betAmount * 8; // 8 players total
    pokerManiaGame.timeLeft = diffData.timeLimit;
    
    // Reset performance tracking
    pokerManiaGame.handsWon = 0;
    pokerManiaGame.perfectHands = 0;
    pokerManiaGame.bluffsAttempted = 0;
    pokerManiaGame.bluffsSuccessful = 0;
    pokerManiaGame.foldsMade = 0;
    pokerManiaGame.correctFolds = 0;
    pokerManiaGame.allInWins = 0;
    pokerManiaGame.straightFlushes = 0;
    pokerManiaGame.royalFlushes = 0;
    
    // Reset VR tracking
    pokerManiaGame.eyeTracking = 0;
    pokerManiaGame.handTracking = 0;
    pokerManiaGame.voiceCommands = 0;
    pokerManiaGame.gestureAccuracy = 0;
    pokerManiaGame.reactionTime = 0;
    pokerManiaGame.focusLevel = 0;
    pokerManiaGame.immersionScore = 0;
    
    // Initialize opponents
    initializeOpponents(diffData);
    
    hideGameOverlay();
    updatePokerDisplay();
    
    // Start game timer
    pokerManiaGame.gameTimer = setInterval(() => {
        pokerManiaGame.timeLeft--;
        updatePokerDisplay();
        if (pokerManiaGame.timeLeft <= 0) {
            endPokerGame('TIME UP');
        }
    }, 1000);
    
    // Start first hand
    dealNewHand();
}

function initializeOpponents(diffData) {
    pokerManiaGame.opponents = [];
    for (let i = 0; i < pokerManiaGame.opponentCount; i++) {
        pokerManiaGame.opponents.push({
            id: i,
            name: `VR Player ${i + 1}`,
            skill: diffData.opponentSkill + (Math.random() - 0.5) * 0.1,
            chips: 1000,
            isActive: true,
            aggression: Math.random() * 0.8 + 0.2,
            bluffFreq: Math.random() * 0.3 + 0.1
        });
    }
}

function dealNewHand() {
    // Deal cards
    pokerManiaGame.playerHand = generateHand();
    pokerManiaGame.dealerHand = generateHand();
    pokerManiaGame.communityCards = generateCommunityCards();
    
    // Simulate VR interactions
    simulateVRInteractions();
    
    // Play the hand
    setTimeout(() => playPokerHand(), 2000);
}

function generateHand() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const hand = [];
    for (let i = 0; i < 2; i++) {
        const suit = suits[Math.floor(Math.random() * suits.length)];
        const rank = ranks[Math.floor(Math.random() * ranks.length)];
        hand.push({ suit, rank, value: ranks.indexOf(rank) + 2 });
    }
    return hand;
}

function generateCommunityCards() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const cards = [];
    for (let i = 0; i < 5; i++) {
        const suit = suits[Math.floor(Math.random() * suits.length)];
        const rank = ranks[Math.floor(Math.random() * ranks.length)];
        cards.push({ suit, rank, value: ranks.indexOf(rank) + 2 });
    }
    return cards;
}

function simulateVRInteractions() {
    const modeData = VR_MODES.find(m => m.name === pokerManiaGame.gameMode);
    
    // Simulate eye tracking accuracy
    pokerManiaGame.eyeTracking += Math.random() * 10;
    
    // Simulate hand gesture recognition
    const gestureAttempts = Math.floor(Math.random() * 5) + 1;
    const gestureSuccess = Math.floor(Math.random() * gestureAttempts);
    pokerManiaGame.handTracking += gestureSuccess;
    pokerManiaGame.gestureAccuracy = pokerManiaGame.handTracking / (pokerManiaGame.round * 3);
    
    // Simulate voice commands
    if (Math.random() < 0.7) {
        pokerManiaGame.voiceCommands++;
    }
    
    // Simulate reaction time (lower is better)
    pokerManiaGame.reactionTime += Math.random() * 2000 + 500; // 500-2500ms
    
    // Calculate focus level based on performance
    pokerManiaGame.focusLevel = Math.min(100, 
        (pokerManiaGame.eyeTracking * 2 + pokerManiaGame.gestureAccuracy * 50 + 
         pokerManiaGame.voiceCommands * 5) / pokerManiaGame.round
    );
    
    // Update immersion score
    pokerManiaGame.immersionScore = 
        (pokerManiaGame.focusLevel + pokerManiaGame.gestureAccuracy * 100) / 2;
}

function playPokerHand() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pokerManiaGame.difficulty);
    
    // Evaluate player hand strength
    const playerStrength = evaluatePokerHand(pokerManiaGame.playerHand, pokerManiaGame.communityCards);
    const dealerStrength = evaluatePokerHand(pokerManiaGame.dealerHand, pokerManiaGame.communityCards);
    
    // Simulate betting decisions with VR elements
    const playerAction = simulatePlayerAction(playerStrength);
    const opponentActions = simulateOpponentActions(diffData);
    
    // Determine hand outcome
    const handWon = playerStrength.value > dealerStrength.value;
    
    if (handWon) {
        pokerManiaGame.handsWon++;
        
        // Check for perfect hand
        if (playerStrength.value >= 7) { // Full house or better
            pokerManiaGame.perfectHands++;
        }
        
        // Track special hands
        if (playerStrength.value === 9) pokerManiaGame.straightFlushes++;
        if (playerStrength.value === 10) pokerManiaGame.royalFlushes++;
        
        showPokerMessage(`WON: ${playerStrength.name}!`, 2000);
    } else {
        showPokerMessage(`LOST: ${dealerStrength.name}`, 2000);
    }
    
    // Track bluffs and folds
    if (playerAction === 'bluff') {
        pokerManiaGame.bluffsAttempted++;
        if (handWon) pokerManiaGame.bluffsSuccessful++;
    }
    
    if (playerAction === 'fold') {
        pokerManiaGame.foldsMade++;
        if (!handWon) pokerManiaGame.correctFolds++;
    }
    
    pokerManiaGame.round++;
    updatePokerDisplay();
    
    // Check if game should continue
    if (pokerManiaGame.round > pokerManiaGame.maxRounds) {
        endPokerGame('TOURNAMENT COMPLETE');
    } else {
        setTimeout(() => dealNewHand(), 3000);
    }
}

function evaluatePokerHand(hand, community) {
    // Simplified hand evaluation - in real game would be more complex
    const allCards = [...hand, ...community];
    const values = allCards.map(c => c.value).sort((a, b) => b - a);
    const suits = allCards.map(c => c.suit);
    
    // Check for flush
    const suitCounts = {};
    suits.forEach(suit => suitCounts[suit] = (suitCounts[suit] || 0) + 1);
    const isFlush = Object.values(suitCounts).some(count => count >= 5);
    
    // Check for straight
    const uniqueValues = [...new Set(values)];
    const isStraight = uniqueValues.length >= 5 && 
        uniqueValues.slice(0, 5).every((val, i) => i === 0 || val === uniqueValues[i-1] - 1);
    
    // Simplified ranking
    if (isFlush && isStraight && values[0] === 14) return HAND_RANKINGS[9]; // Royal Flush
    if (isFlush && isStraight) return HAND_RANKINGS[8]; // Straight Flush
    if (isFlush) return HAND_RANKINGS[5]; // Flush
    if (isStraight) return HAND_RANKINGS[4]; // Straight
    
    // Check pairs/trips/quads
    const valueCounts = {};
    values.forEach(val => valueCounts[val] = (valueCounts[val] || 0) + 1);
    const counts = Object.values(valueCounts).sort((a, b) => b - a);
    
    if (counts[0] === 4) return HAND_RANKINGS[7]; // Four of a kind
    if (counts[0] === 3 && counts[1] === 2) return HAND_RANKINGS[6]; // Full house
    if (counts[0] === 3) return HAND_RANKINGS[3]; // Three of a kind
    if (counts[0] === 2 && counts[1] === 2) return HAND_RANKINGS[2]; // Two pair
    if (counts[0] === 2) return HAND_RANKINGS[1]; // Pair
    
    return HAND_RANKINGS[0]; // High card
}

function simulatePlayerAction(handStrength) {
    const actions = ['call', 'raise', 'fold', 'bluff', 'all-in'];
    
    // Weight actions based on hand strength and VR performance
    const vrBonus = pokerManiaGame.gestureAccuracy * 0.3;
    const strengthBonus = handStrength.value / 10;
    
    if (handStrength.value >= 8 && Math.random() < 0.3 + vrBonus) {
        if (Math.random() < 0.2) {
            pokerManiaGame.allInWins++;
            return 'all-in';
        }
        return 'raise';
    }
    
    if (handStrength.value <= 3 && Math.random() < 0.4 + vrBonus) {
        return Math.random() < 0.7 ? 'fold' : 'bluff';
    }
    
    return 'call';
}

function simulateOpponentActions(diffData) {
    return pokerManiaGame.opponents.map(opponent => {
        const action = Math.random() < opponent.skill ? 'optimal' : 'suboptimal';
        return { id: opponent.id, action };
    });
}

function endPokerGame(reason) {
    pokerManiaGame.isPlaying = false;
    pokerManiaGame.gamePhase = 'finished';
    
    if (pokerManiaGame.gameTimer) {
        clearInterval(pokerManiaGame.gameTimer);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pokerManiaGame.difficulty);
    const modeData = VR_MODES.find(m => m.name === pokerManiaGame.gameMode);
    
    // Ultra-strict VR poker winning requirements
    const winRatio = pokerManiaGame.handsWon / pokerManiaGame.maxRounds;
    const baseWinChance = diffData.winRate;
    
    // Extremely demanding requirements
    const minHandsWon = diffData.targetWins;        // Need target wins
    const minPerfectHands = 3;                      // Need 3+ perfect hands
    const minBluffSuccess = 0.7;                    // Need 70%+ bluff success
    const minFoldAccuracy = 0.8;                    // Need 80%+ fold accuracy
    const minGestureAccuracy = 0.6;                 // Need 60%+ gesture accuracy
    const minImmersionScore = 70;                   // Need 70+ immersion score
    const minFocusLevel = 80;                       // Need 80+ focus level
    const minVoiceCommands = modeData.gestureRequired; // Need required voice commands
    const maxAvgReactionTime = 1500;                // Need <1.5s avg reaction
    const minAllInWins = 1;                         // Need 1+ all-in win
    const minSpecialHands = 1;                      // Need 1+ straight flush or royal
    
    const bluffSuccessRate = pokerManiaGame.bluffsAttempted > 0 ? 
        pokerManiaGame.bluffsSuccessful / pokerManiaGame.bluffsAttempted : 0;
    const foldAccuracy = pokerManiaGame.foldsMade > 0 ? 
        pokerManiaGame.correctFolds / pokerManiaGame.foldsMade : 0;
    const avgReactionTime = pokerManiaGame.reactionTime / pokerManiaGame.round;
    const specialHands = pokerManiaGame.straightFlushes + pokerManiaGame.royalFlushes;
    
    const meetsAllRequirements = 
        pokerManiaGame.handsWon >= minHandsWon &&
        pokerManiaGame.perfectHands >= minPerfectHands &&
        bluffSuccessRate >= minBluffSuccess &&
        foldAccuracy >= minFoldAccuracy &&
        pokerManiaGame.gestureAccuracy >= minGestureAccuracy &&
        pokerManiaGame.immersionScore >= minImmersionScore &&
        pokerManiaGame.focusLevel >= minFocusLevel &&
        pokerManiaGame.voiceCommands >= minVoiceCommands &&
        avgReactionTime <= maxAvgReactionTime &&
        pokerManiaGame.allInWins >= minAllInWins &&
        specialHands >= minSpecialHands &&
        winRatio >= 0.75; // Need 75%+ win rate
    
    // Performance bonuses
    const vrPerformance = (pokerManiaGame.gestureAccuracy + pokerManiaGame.immersionScore / 100 + 
                          pokerManiaGame.focusLevel / 100) / 3;
    const skillPerformance = (bluffSuccessRate + foldAccuracy + winRatio) / 3;
    const timeEfficiency = Math.max(0, (1500 - avgReactionTime) / 1500);
    
    const performanceBonus = (vrPerformance + skillPerformance + timeEfficiency) / 3;
    const finalWinChance = meetsAllRequirements ? baseWinChance * winRatio * (1 + performanceBonus * 0.5) : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with VR bonuses
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier;
        const vrBonus = vrPerformance * 0.5;
        const skillBonus = skillPerformance * 0.4;
        const perfectBonus = pokerManiaGame.perfectHands * 0.2;
        const specialBonus = specialHands * 0.3;
        const allInBonus = pokerManiaGame.allInWins * 0.25;
        
        const totalMultiplier = baseMultiplier * (1 + vrBonus + skillBonus + perfectBonus + 
                                                 specialBonus + allInBonus);
        const winnings = Math.floor(pokerManiaGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('pokerResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🎰 VR POKER CHAMPION! 🎰</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        const requirements = [
            `${minHandsWon}+ Hands Won (${pokerManiaGame.handsWon})`,
            `${minPerfectHands}+ Perfect (${pokerManiaGame.perfectHands})`,
            `${Math.floor(minBluffSuccess * 100)}%+ Bluff Success (${Math.floor(bluffSuccessRate * 100)}%)`,
            `${Math.floor(minFoldAccuracy * 100)}%+ Fold Accuracy (${Math.floor(foldAccuracy * 100)}%)`,
            `${Math.floor(minGestureAccuracy * 100)}%+ Gestures (${Math.floor(pokerManiaGame.gestureAccuracy * 100)}%)`,
            `${minImmersionScore}+ Immersion (${Math.floor(pokerManiaGame.immersionScore)})`,
            `${minFocusLevel}+ Focus (${Math.floor(pokerManiaGame.focusLevel)})`,
            `${minVoiceCommands}+ Voice Cmds (${pokerManiaGame.voiceCommands})`,
            `<${maxAvgReactionTime}ms Reaction (${Math.floor(avgReactionTime)}ms)`,
            `${minAllInWins}+ All-In Wins (${pokerManiaGame.allInWins})`,
            `${minSpecialHands}+ Special Hands (${specialHands})`,
            `75%+ Win Rate (${Math.floor(winRatio * 100)}%)`
        ];
        
        document.getElementById('pokerResult').innerHTML = 
            `<span class="text-red-400">🃏 ${reason} 🃏</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Need: ${requirements.slice(0, 4).join(', ')}<br>
            ${requirements.slice(4, 8).join(', ')}<br>
            ${requirements.slice(8, 12).join(', ')}</div>`;
    }
    
    updatePokerDisplay();
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
    }, 3000);
}

function showPokerMessage(text, duration) {
    // Implementation for showing game messages
    console.log(text);
}

function updatePokerDisplay() {
    document.getElementById('currentRound').textContent = pokerManiaGame.round;
    document.getElementById('handsWon').textContent = pokerManiaGame.handsWon;
    document.getElementById('perfectHands').textContent = pokerManiaGame.perfectHands;
    document.getElementById('bluffSuccess').textContent = 
        `${pokerManiaGame.bluffsSuccessful}/${pokerManiaGame.bluffsAttempted}`;
    document.getElementById('foldAccuracy').textContent = 
        `${pokerManiaGame.correctFolds}/${pokerManiaGame.foldsMade}`;
    document.getElementById('immersionScore').textContent = Math.floor(pokerManiaGame.immersionScore);
    document.getElementById('gestureAccuracy').textContent = 
        Math.floor(pokerManiaGame.gestureAccuracy * 100) + '%';
    document.getElementById('focusLevel').textContent = Math.floor(pokerManiaGame.focusLevel);
    document.getElementById('timeLeft').textContent = 
        Math.floor(pokerManiaGame.timeLeft / 60) + ':' + 
        String(pokerManiaGame.timeLeft % 60).padStart(2, '0');
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    updatePokerDifficultySettings();
    updatePokerModeSettings();
});