// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadHiLoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-lime-500/30">
                    <h4 class="text-xl font-bold mb-4 text-lime-400">NEURAL HILO</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="hiloBet" value="10" min="1" max="${balance}"
                               class="w-full bg-black/50 border border-lime-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PREDICTION MODE</label>
                        <select id="hiloMode" class="w-full bg-black/50 border border-lime-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Normal (2x)</option>
                            <option value="enhanced">Enhanced (3x)</option>
                            <option value="neural">Neural (5x)</option>
                        </select>
                    </div>

                    <button id="startHiLo" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START SEQUENCE
                    </button>

                    <div id="hiloActions" class="space-y-2 hidden">
                        <button id="hiloHigh" class="w-full py-3 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            📈 HIGHER
                        </button>
                        <button id="hiloLow" class="w-full py-3 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            📉 LOWER
                        </button>
                        <button id="hiloCashout" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            CASHOUT
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Sequence</div>
                        <div id="hiloSequence" class="text-xl font-bold text-lime-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Multiplier</div>
                        <div id="hiloMultiplier" class="text-2xl font-bold text-green-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                        <div id="hiloPotentialWin" class="text-xl font-bold text-yellow-400">0 GA</div>
                    </div>
                </div>

                <div class="bg-black/30 p-4 rounded-xl border border-lime-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-lime-400">NEURAL ANALYSIS</h5>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Higher Probability:</span>
                            <span id="hiloHighProb" class="text-green-400">50%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Lower Probability:</span>
                            <span id="hiloLowProb" class="text-red-400">50%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sequence Risk:</span>
                            <span id="hiloRisk" class="text-yellow-400">Low</span>
                        </div>
                    </div>
                </div>

                <div class="bg-black/30 p-4 rounded-xl border border-lime-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-lime-400">CARD HISTORY</h5>
                    <div id="hiloHistory" class="flex flex-wrap gap-2">
                        </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-lime-500/30">
                    <div id="hiloInterface" class="relative bg-gradient-to-br from-lime-900 to-emerald-900 rounded-lg p-6 h-96">
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="text-center">
                                <div class="text-sm text-lime-400 mb-2">CURRENT DATA POINT</div>
                                <div id="hiloCurrentCard" class="w-32 h-40 bg-white rounded-lg border-4 border-lime-400 flex flex-col items-center justify-center text-black text-2xl font-bold neon-border mb-4">
                                    <div>?</div>
                                    <div>?</div>
                                </div>
                                <div id="hiloCardValue" class="text-lg font-bold text-lime-400">Ready</div>
                            </div>
                        </div>

                        <div class="absolute top-4 right-4">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">NEXT</div>
                                <div id="hiloNextCard" class="w-16 h-20 bg-gray-800 rounded border-2 border-gray-600 flex items-center justify-center text-gray-400 text-sm">
                                    🂠
                                </div>
                            </div>
                        </div>

                        <div class="absolute top-4 left-4">
                            <div class="text-center">
                                <div class="text-3xl text-green-400 mb-2">↗️</div>
                                <div class="text-sm text-green-400">HIGHER</div>
                            </div>
                        </div>

                        <div class="absolute bottom-4 left-4">
                            <div class="text-center">
                                <div class="text-3xl text-red-400 mb-2">↘️</div>
                                <div class="text-sm text-red-400">LOWER</div>
                            </div>
                        </div>

                        <div class="absolute bottom-4 right-4">
                            <div class="text-center">
                                <div class="text-sm text-gray-400">MODE</div>
                                <div id="hiloCurrentMode" class="text-lime-400 font-bold">Normal</div>
                            </div>
                        </div>
                    </div>
                    <div id="hiloStatus" class="text-center mt-4 text-lg font-semibold">Select your prediction mode and start the sequence</div>
                </div>
            </div>
        </div>
    `;

    initializeHiLo();
}

let hiloGame = {
    isPlaying: false,
    mode: 'normal',
    betAmount: 0,
    sequence: 0,
    multiplier: 1.00,
    deck: [],
    currentCard: null,
    nextCard: null,
    cardHistory: []
};

function initializeHiLo() {
    document.getElementById('startHiLo').addEventListener('click', startHiLoGame);
    document.getElementById('hiloHigh').addEventListener('click', () => makeHiLoPrediction('high'));
    document.getElementById('hiloLow').addEventListener('click', () => makeHiLoPrediction('low'));
    document.getElementById('hiloCashout').addEventListener('click', cashoutHiLo);
    document.getElementById('hiloMode').addEventListener('change', updateHiLoMode);

    initializeHiLoDeck();
    updateHiLoMode();
}

function initializeHiLoDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];

    hiloGame.deck = [];
    for (const suit of suits) {
        for (const rank of ranks) {
            hiloGame.deck.push({
                rank,
                suit,
                value: getHiLoCardValue(rank)
            });
        }
    }

    shuffleHiLoDeck();
}

function shuffleHiLoDeck() {
    for (let i = hiloGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [hiloGame.deck[i], hiloGame.deck[j]] =
            [hiloGame.deck[j], hiloGame.deck[i]];
    }
}

function getHiLoCardValue(rank) {
    if (rank === 'A') return 14;
    if (rank === 'K') return 13;
    if (rank === 'Q') return 12;
    if (rank === 'J') return 11;
    return parseInt(rank);
}

function updateHiLoMode() {
    hiloGame.mode = document.getElementById('hiloMode').value;

    const modeNames = { 'normal': 'Normal', 'enhanced': 'Enhanced', 'neural': 'Neural' };
    document.getElementById('hiloCurrentMode').textContent = modeNames[hiloGame.mode];
}

function startHiLoGame() {
    const betAmount = parseInt(document.getElementById('hiloBet').value);

    // Validate bet amount
    if (isNaN(betAmount) || betAmount <= 0) {
        alert('Please enter a valid bet amount.');
        return;
    }

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    // Deduct bet
    balance -= betAmount;
    updateBalance();

    hiloGame.isPlaying = true;
    hiloGame.betAmount = betAmount;
    hiloGame.sequence = 0;
    hiloGame.multiplier = 1.00;
    hiloGame.cardHistory = [];

    // Ensure deck is full and shuffled before drawing
    initializeHiLoDeck(); // Reshuffle deck at the start of each new game

    // Draw first card
    hiloGame.currentCard = hiloGame.deck.pop();
    hiloGame.nextCard = hiloGame.deck.pop();

    displayHiLoCurrentCard();
    updateHiLoDisplay();

    // Show actions
    document.getElementById('startHiLo').disabled = true;
    document.getElementById('hiloActions').classList.remove('hidden');
    document.getElementById('hiloStatus').textContent = 'Predict if the next card will be higher or lower';
}

function displayHiLoCurrentCard() {
    const cardElement = document.getElementById('hiloCurrentCard');
    const card = hiloGame.currentCard;

    const isRed = card.suit === '♥' || card.suit === '♦';
    cardElement.style.color = isRed ? '#dc2626' : '#000';
    cardElement.innerHTML = `
        <div>${card.rank}</div>
        <div class="text-lg">${card.suit}</div>
    `;

    document.getElementById('hiloCardValue').textContent = `Value: ${card.value}`;

    // Update neural analysis
    updateHiLoAnalysis();
}

function updateHiLoAnalysis() {
    const currentValue = hiloGame.currentCard.value;

    // Calculate probabilities based on remaining cards
    let higherCount = 0;
    let lowerCount = 0;
    let equalCount = 0;

    hiloGame.deck.forEach(card => {
        if (card.value > currentValue) higherCount++;
        else if (card.value < currentValue) lowerCount++;
        else equalCount++;
    });

    const total = hiloGame.deck.length;
    let higherProb = 0;
    let lowerProb = 0;

    if (total > 0) { // Avoid division by zero if deck is somehow empty
        higherProb = Math.round((higherCount / total) * 100);
        lowerProb = Math.round((lowerCount / total) * 100);
    }


    document.getElementById('hiloHighProb').textContent = higherProb + '%';
    document.getElementById('hiloLowProb').textContent = lowerProb + '%';

    // Update risk level
    const riskLevel = hiloGame.sequence < 3 ? 'Low' : hiloGame.sequence < 6 ? 'Medium' : 'High';
    document.getElementById('hiloRisk').textContent = riskLevel;
}

function updateHiLoDisplay() {
    document.getElementById('hiloSequence').textContent = hiloGame.sequence;
    document.getElementById('hiloMultiplier').textContent = hiloGame.multiplier.toFixed(2) + 'x';

    const potentialWin = Math.floor(hiloGame.betAmount * hiloGame.multiplier);
    document.getElementById('hiloPotentialWin').textContent = potentialWin + ' GA'; // Consistent unit
}

function makeHiLoPrediction(prediction) {
    if (!hiloGame.isPlaying) return; // Prevent predictions if game not active

    const currentValue = hiloGame.currentCard.value;
    const nextValue = hiloGame.nextCard.value;

    let correct = false;
    if (prediction === 'high' && nextValue > currentValue) correct = true;
    if (prediction === 'low' && nextValue < currentValue) correct = true;

    // Add current card to history
    addHiLoHistory(hiloGame.currentCard);

    if (correct) {
        // Correct prediction
        hiloGame.sequence++;

        // Update multiplier based on mode
        const modeMultipliers = { 'normal': 0.5, 'enhanced': 0.8, 'neural': 1.2 };
        hiloGame.multiplier += modeMultipliers[hiloGame.mode];

        // Move to next card
        hiloGame.currentCard = hiloGame.nextCard;

        // Draw new next card, reshuffle if deck is empty
        if (hiloGame.deck.length === 0) {
            initializeHiLoDeck(); // Reshuffle deck if empty
        }
        hiloGame.nextCard = hiloGame.deck.pop();

        displayHiLoCurrentCard();
        updateHiLoDisplay();

        document.getElementById('hiloStatus').innerHTML =
            `<span class="text-green-400">Correct! Sequence continues...</span>`;

        // Check if deck is running low (e.g., 5 cards left before next draw)
        if (hiloGame.deck.length < 5) {
            // Optional: You might want to give a warning or automatically cash out
            // For now, the existing auto-cashout logic if deck is too low is fine
        }
    } else {
        // Wrong prediction
        endHiLoGame(false);
    }
}

function cashoutHiLo() {
    if (!hiloGame.isPlaying) return; // Prevent cashout if game not active
    endHiLoGame(true);
}

function endHiLoGame(cashout) {
    hiloGame.isPlaying = false;

    const winnings = cashout ? Math.floor(hiloGame.betAmount * hiloGame.multiplier) : 0;

    if (winnings > 0) {
        balance += winnings;
        updateBalance();

        document.getElementById('hiloStatus').innerHTML =
            `<span class="text-green-400 neon-glow">Cashed out! Sequence ${hiloGame.sequence} - Won ${winnings} GA</span>`;
    } else {
        document.getElementById('hiloStatus').innerHTML =
            `<span class="text-red-400">Wrong prediction! Sequence ended at ${hiloGame.sequence}</span>`;
    }

    // Reset after delay
    setTimeout(() => {
        resetHiLo();
    }, 3000);
}

function addHiLoHistory(card) {
    const history = document.getElementById('hiloHistory');
    const cardElement = document.createElement('div');

    const isRed = card.suit === '♥' || card.suit === '♦';
    cardElement.className = `w-8 h-10 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-xs font-bold`;
    cardElement.style.color = isRed ? '#dc2626' : '#000';
    cardElement.innerHTML = `
        <div>${card.rank}</div>
        <div style="font-size: 8px">${card.suit}</div>
    `;

    history.insertBefore(cardElement, history.firstChild);

    // Keep only last 10 cards
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

function resetHiLo() {
    hiloGame.isPlaying = false;
    hiloGame.sequence = 0;
    hiloGame.multiplier = 1.00;
    hiloGame.betAmount = 0; // Reset bet amount for next game

    // Reset UI
    document.getElementById('startHiLo').disabled = false;
    document.getElementById('hiloActions').classList.add('hidden');
    document.getElementById('hiloSequence').textContent = '0';
    document.getElementById('hiloMultiplier').textContent = '1.00x';
    document.getElementById('hiloPotentialWin').textContent = '0 GA'; // Consistent unit
    document.getElementById('hiloCardValue').textContent = 'Ready';
    document.getElementById('hiloStatus').textContent = 'Select your prediction mode and start the sequence';
    document.getElementById('hiloBet').value = '10'; // Reset bet input to default

    // Reset card display
    document.getElementById('hiloCurrentCard').innerHTML = `
        <div>?</div>
        <div>?</div>
    `;
    document.getElementById('hiloCurrentCard').style.color = '#000';
    document.getElementById('hiloHistory').innerHTML = ''; // Clear history

    // Reshuffle deck for the next game
    initializeHiLoDeck();
    updateHiLoAnalysis(); // Update probabilities based on new deck
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadHiLoGame();
});