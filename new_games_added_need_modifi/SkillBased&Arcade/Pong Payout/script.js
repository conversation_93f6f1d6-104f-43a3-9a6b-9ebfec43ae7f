// Game state
let balance = 1000;

// Pong Payout game state
let pongGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'classic',
    paddleSize: 'normal',
    ballSpeed: 'medium',
    
    // Game mechanics
    canvas: null,
    ctx: null,
    animationId: null,
    gameTimer: null,
    timeLeft: 0,
    score: 0,
    opponentScore: 0,
    targetScore: 11,
    
    // Paddle and ball physics
    playerPaddle: { x: 20, y: 200, width: 15, height: 80, speed: 8 },
    opponentPaddle: { x: 765, y: 200, width: 15, height: 80, speed: 6 },
    ball: { x: 400, y: 250, vx: 5, vy: 3, radius: 8, trail: [] },
    
    // Performance tracking
    perfectHits: 0,
    consecutiveHits: 0,
    maxConsecutive: 0,
    powerShots: 0,
    cornerShots: 0,
    speedBoosts: 0,
    rallies: 0,
    longestRally: 0,
    currentRally: 0,
    
    // Special mechanics
    powerUpActive: false,
    powerUpType: null,
    powerUpTime: 0,
    ballSpeedMultiplier: 1.0,
    paddleSpeedMultiplier: 1.0,
    
    // AI difficulty scaling
    aiReactionTime: 0.8,
    aiAccuracy: 0.75,
    aiAggression: 0.6,
    
    // Visual effects
    particles: [],
    screenShake: 0,
    lastUpdate: 0
};

// Ultra-strict difficulty settings with sub-8% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        targetScore: 11,
        timeLimit: 300,
        winRate: 0.08,
        multiplier: 0.7,
        aiSpeed: 0.6,
        aiAccuracy: 0.65,
        ballSpeedIncrease: 1.02,
        description: 'Beginner Pong (8% win rate)'
    },
    {
        name: 'normal',
        targetScore: 11,
        timeLimit: 240,
        winRate: 0.06,
        multiplier: 1.0,
        aiSpeed: 0.8,
        aiAccuracy: 0.78,
        ballSpeedIncrease: 1.03,
        description: 'Standard Pong (6% win rate)'
    },
    {
        name: 'hard',
        targetScore: 15,
        timeLimit: 180,
        winRate: 0.04,
        multiplier: 1.4,
        aiSpeed: 0.9,
        aiAccuracy: 0.88,
        ballSpeedIncrease: 1.04,
        description: 'Expert Pong (4% win rate)'
    },
    {
        name: 'expert',
        targetScore: 21,
        timeLimit: 150,
        winRate: 0.03,
        multiplier: 1.8,
        aiSpeed: 0.95,
        aiAccuracy: 0.94,
        ballSpeedIncrease: 1.05,
        description: 'Master Pong (3% win rate)'
    },
    {
        name: 'legendary',
        targetScore: 25,
        timeLimit: 120,
        winRate: 0.02,
        multiplier: 2.2,
        aiSpeed: 0.98,
        aiAccuracy: 0.97,
        ballSpeedIncrease: 1.06,
        description: 'Legendary Pong (2% win rate)'
    }
];

const GAME_MODES = [
    {
        name: 'classic',
        description: 'Standard Pong',
        scoreMultiplier: 1.0,
        specialEffects: false,
        powerUps: false
    },
    {
        name: 'power',
        description: 'Power-Up Pong',
        scoreMultiplier: 1.2,
        specialEffects: true,
        powerUps: true
    },
    {
        name: 'speed',
        description: 'Speed Pong',
        scoreMultiplier: 1.4,
        specialEffects: true,
        powerUps: false
    },
    {
        name: 'precision',
        description: 'Precision Pong',
        scoreMultiplier: 1.6,
        specialEffects: false,
        powerUps: false
    }
];

const PADDLE_SIZES = [
    { name: 'small', height: 60, multiplier: 1.4, description: 'Small Paddle' },
    { name: 'normal', height: 80, multiplier: 1.0, description: 'Normal Paddle' },
    { name: 'large', height: 100, multiplier: 0.8, description: 'Large Paddle' }
];

const BALL_SPEEDS = [
    { name: 'slow', speed: 4, multiplier: 0.8, description: 'Slow Ball' },
    { name: 'medium', speed: 5, multiplier: 1.0, description: 'Medium Ball' },
    { name: 'fast', speed: 7, multiplier: 1.3, description: 'Fast Ball' },
    { name: 'extreme', speed: 9, multiplier: 1.6, description: 'Extreme Ball' }
];

const POWER_UPS = [
    { type: 'speed', duration: 5000, effect: 'Increased paddle speed' },
    { type: 'size', duration: 8000, effect: 'Larger paddle' },
    { type: 'slow', duration: 6000, effect: 'Slower ball' },
    { type: 'multi', duration: 10000, effect: 'Multi-ball' }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadPongGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Pong Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🏓 PONG PAYOUT 🏓</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 PONG BET</label>
                        <input type="number" id="pongBet" value="50" min="10" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updatePongPayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎯 DIFFICULTY</label>
                            <select id="pongDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updatePongDifficultySettings()">
                                <option value="easy">Easy (8%)</option>
                                <option value="normal" selected>Normal (6%)</option>
                                <option value="hard">Hard (4%)</option>
                                <option value="expert">Expert (3%)</option>
                                <option value="legendary">Legendary (2%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎮 MODE</label>
                            <select id="pongMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updatePongModeSettings()">
                                <option value="classic" selected>Classic</option>
                                <option value="power">Power-Up</option>
                                <option value="speed">Speed</option>
                                <option value="precision">Precision</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🏓 PADDLE SIZE</label>
                            <select id="paddleSize" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updatePongPayout()">
                                <option value="small">Small (+40%)</option>
                                <option value="normal" selected>Normal</option>
                                <option value="large">Large (-20%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">⚡ BALL SPEED</label>
                            <select id="ballSpeed" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updatePongPayout()">
                                <option value="slow">Slow (-20%)</option>
                                <option value="medium" selected>Medium</option>
                                <option value="fast">Fast (+30%)</option>
                                <option value="extreme">Extreme (+60%)</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Target Score:</div>
                                <div class="text-white font-bold" id="targetScore">11</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Win Chance:</div>
                                <div class="text-red-400 font-bold" id="pongWinChance">6%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">AI Skill:</div>
                                <div class="text-yellow-400 font-bold" id="aiSkillLevel">78%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Limit:</div>
                                <div class="text-blue-400 font-bold" id="timeLimit">4m</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="pongPotentialPayout">350 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startPongGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        🏓 START PONG MATCH 🏓
                    </button>
                </div>

                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 MATCH STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Score:</span>
                            <span class="text-white" id="currentScore">0 - 0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Hits:</span>
                            <span class="text-green-400" id="perfectHits">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Consecutive:</span>
                            <span class="text-yellow-400" id="consecutiveHits">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Power Shots:</span>
                            <span class="text-blue-400" id="powerShots">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rallies:</span>
                            <span class="text-purple-400" id="rallies">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Time Left:</span>
                            <span class="text-red-400" id="timeLeft">0:00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Canvas -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="relative">
                        <canvas id="pongCanvas" width="800" height="500" 
                                class="w-full border border-purple-500/50 rounded-lg bg-black">
                        </canvas>
                        <div class="absolute inset-0 flex items-center justify-center pointer-events-none">
                            <div id="pongMessage" class="text-2xl font-bold text-white opacity-0 transition-opacity duration-500"></div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <div class="text-sm text-gray-400 mb-2">Use ↑↓ Arrow Keys or W/S to move paddle</div>
                        <div id="pongResult" class="text-xl font-bold mb-2"></div>
                        <div id="winAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Game History -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 RECENT MATCHES</h5>
                    <div id="pongHistory" class="space-y-1 text-sm max-h-32 overflow-y-auto">
                        <div class="text-gray-500 text-center">No matches yet</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Overlay -->
        <div id="gameOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🏓 MATCH COMPLETE 🏓</h3>
                <div id="finalResult" class="text-xl mb-4"></div>
                <div id="finalStats" class="text-sm text-gray-400 mb-6"></div>
                <button onclick="hideGameOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    PLAY AGAIN
                </button>
            </div>
        </div>
    `;

    // Initialize canvas
    pongGame.canvas = document.getElementById('pongCanvas');
    pongGame.ctx = pongGame.canvas.getContext('2d');
    
    // Add event listeners
    document.addEventListener('keydown', handlePongInput);
    document.addEventListener('keyup', handlePongInputUp);
    
    updatePongDifficultySettings();
    updatePongModeSettings();
    updatePongPayout();
    drawPongTable();
}

let keysPressed = {};

function handlePongInput(event) {
    keysPressed[event.key] = true;
}

function handlePongInputUp(event) {
    keysPressed[event.key] = false;
}

function updatePongDifficultySettings() {
    const difficulty = document.getElementById('pongDifficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    pongGame.difficulty = difficulty;
    pongGame.targetScore = diffData.targetScore;
    
    document.getElementById('targetScore').textContent = diffData.targetScore;
    document.getElementById('pongWinChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('aiSkillLevel').textContent = Math.floor(diffData.aiAccuracy * 100) + '%';
    document.getElementById('timeLimit').textContent = Math.floor(diffData.timeLimit / 60) + 'm';
    
    updatePongPayout();
}

function updatePongModeSettings() {
    const mode = document.getElementById('pongMode').value;
    pongGame.gameMode = mode;
    updatePongPayout();
}

function updatePongPayout() {
    const betAmount = parseInt(document.getElementById('pongBet').value) || 50;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pongGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === pongGame.gameMode);
    const paddleData = PADDLE_SIZES.find(p => p.name === document.getElementById('paddleSize').value);
    const speedData = BALL_SPEEDS.find(s => s.name === document.getElementById('ballSpeed').value);
    
    const totalMultiplier = diffData.multiplier * modeData.scoreMultiplier * 
                           paddleData.multiplier * speedData.multiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 7); // Conservative multiplier
    
    document.getElementById('pongPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function startPongGame() {
    const betAmount = parseInt(document.getElementById('pongBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pongGame.difficulty);
    const paddleData = PADDLE_SIZES.find(p => p.name === document.getElementById('paddleSize').value);
    const speedData = BALL_SPEEDS.find(s => s.name === document.getElementById('ballSpeed').value);
    
    // Reset game state
    pongGame.isPlaying = true;
    pongGame.gamePhase = 'playing';
    pongGame.betAmount = betAmount;
    pongGame.score = 0;
    pongGame.opponentScore = 0;
    pongGame.timeLeft = diffData.timeLimit;
    pongGame.targetScore = diffData.targetScore;
    
    // Reset performance tracking
    pongGame.perfectHits = 0;
    pongGame.consecutiveHits = 0;
    pongGame.maxConsecutive = 0;
    pongGame.powerShots = 0;
    pongGame.cornerShots = 0;
    pongGame.speedBoosts = 0;
    pongGame.rallies = 0;
    pongGame.longestRally = 0;
    pongGame.currentRally = 0;
    
    // Set AI difficulty
    pongGame.aiReactionTime = diffData.aiSpeed;
    pongGame.aiAccuracy = diffData.aiAccuracy;
    pongGame.aiAggression = diffData.aiSpeed * 0.8;
    
    // Set paddle size
    pongGame.playerPaddle.height = paddleData.height;
    pongGame.opponentPaddle.height = paddleData.height;
    
    // Set ball speed
    const baseSpeed = speedData.speed;
    pongGame.ball.vx = Math.random() > 0.5 ? baseSpeed : -baseSpeed;
    pongGame.ball.vy = (Math.random() - 0.5) * 4;
    
    // Reset ball position
    resetBall();
    
    hideGameOverlay();
    updatePongDisplay();
    
    // Start game timer
    pongGame.gameTimer = setInterval(() => {
        pongGame.timeLeft--;
        updatePongDisplay();
        if (pongGame.timeLeft <= 0) {
            endPongGame('TIME UP');
        }
    }, 1000);
    
    // Start game loop
    pongGame.lastUpdate = Date.now();
    gameLoop();
}

function resetBall() {
    pongGame.ball.x = pongGame.canvas.width / 2;
    pongGame.ball.y = pongGame.canvas.height / 2;
    pongGame.ball.trail = [];
    
    // Random direction
    const speedData = BALL_SPEEDS.find(s => s.name === document.getElementById('ballSpeed').value);
    const speed = speedData.speed;
    pongGame.ball.vx = Math.random() > 0.5 ? speed : -speed;
    pongGame.ball.vy = (Math.random() - 0.5) * 4;
}

function gameLoop() {
    if (!pongGame.isPlaying) return;
    
    const now = Date.now();
    const deltaTime = (now - pongGame.lastUpdate) / 16.67; // 60 FPS normalization
    pongGame.lastUpdate = now;
    
    updatePongPhysics(deltaTime);
    updateAI(deltaTime);
    updatePlayerInput(deltaTime);
    updatePowerUps(deltaTime);
    checkPongCollisions();
    updateParticles(deltaTime);
    
    drawPongTable();
    updatePongDisplay();
    
    pongGame.animationId = requestAnimationFrame(gameLoop);
}

function updatePlayerInput(deltaTime) {
    const speed = pongGame.playerPaddle.speed * pongGame.paddleSpeedMultiplier * deltaTime;
    
    if (keysPressed['ArrowUp'] || keysPressed['w'] || keysPressed['W']) {
        pongGame.playerPaddle.y = Math.max(0, pongGame.playerPaddle.y - speed);
    }
    if (keysPressed['ArrowDown'] || keysPressed['s'] || keysPressed['S']) {
        pongGame.playerPaddle.y = Math.min(
            pongGame.canvas.height - pongGame.playerPaddle.height, 
            pongGame.playerPaddle.y + speed
        );
    }
}

function updateAI(deltaTime) {
    const ball = pongGame.ball;
    const paddle = pongGame.opponentPaddle;
    
    // AI prediction and movement
    const ballCenterY = ball.y;
    const paddleCenterY = paddle.y + paddle.height / 2;
    const targetY = ballCenterY - paddle.height / 2;
    
    // AI reaction time and accuracy
    const reactionDelay = (1 - pongGame.aiReactionTime) * 100;
    const accuracy = pongGame.aiAccuracy;
    
    if (Math.random() < accuracy) {
        const speed = paddle.speed * pongGame.aiReactionTime * deltaTime;
        const diff = targetY - paddle.y;
        
        if (Math.abs(diff) > reactionDelay) {
            if (diff > 0) {
                paddle.y = Math.min(
                    pongGame.canvas.height - paddle.height,
                    paddle.y + speed
                );
            } else {
                paddle.y = Math.max(0, paddle.y - speed);
            }
        }
    }
}

function updatePongPhysics(deltaTime) {
    const ball = pongGame.ball;
    
    // Update ball position
    ball.x += ball.vx * deltaTime;
    ball.y += ball.vy * deltaTime;
    
    // Add to trail
    ball.trail.push({ x: ball.x, y: ball.y });
    if (ball.trail.length > 10) {
        ball.trail.shift();
    }
    
    // Ball boundary collision (top/bottom)
    if (ball.y <= ball.radius || ball.y >= pongGame.canvas.height - ball.radius) {
        ball.vy *= -1;
        ball.y = Math.max(ball.radius, Math.min(pongGame.canvas.height - ball.radius, ball.y));
        createParticles(ball.x, ball.y, '#00ffff');
    }
    
    // Ball out of bounds (left/right)
    if (ball.x <= 0) {
        pongGame.opponentScore++;
        pongGame.currentRally = 0;
        resetBall();
        checkGameEnd();
    } else if (ball.x >= pongGame.canvas.width) {
        pongGame.score++;
        pongGame.currentRally = 0;
        resetBall();
        checkGameEnd();
    }
}

function checkPongCollisions() {
    const ball = pongGame.ball;
    
    // Player paddle collision
    if (ball.x - ball.radius <= pongGame.playerPaddle.x + pongGame.playerPaddle.width &&
        ball.x + ball.radius >= pongGame.playerPaddle.x &&
        ball.y >= pongGame.playerPaddle.y &&
        ball.y <= pongGame.playerPaddle.y + pongGame.playerPaddle.height &&
        ball.vx < 0) {
        
        handlePaddleHit(pongGame.playerPaddle, true);
    }
    
    // Opponent paddle collision
    if (ball.x + ball.radius >= pongGame.opponentPaddle.x &&
        ball.x - ball.radius <= pongGame.opponentPaddle.x + pongGame.opponentPaddle.width &&
        ball.y >= pongGame.opponentPaddle.y &&
        ball.y <= pongGame.opponentPaddle.y + pongGame.opponentPaddle.height &&
        ball.vx > 0) {
        
        handlePaddleHit(pongGame.opponentPaddle, false);
    }
}

function handlePaddleHit(paddle, isPlayer) {
    const ball = pongGame.ball;
    
    // Calculate hit position on paddle (0 to 1)
    const hitPos = (ball.y - paddle.y) / paddle.height;
    const angle = (hitPos - 0.5) * Math.PI / 3; // Max 60 degree angle
    
    // Reverse and modify ball direction
    ball.vx *= -1;
    ball.vy = Math.sin(angle) * Math.abs(ball.vx);
    
    // Increase ball speed slightly
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pongGame.difficulty);
    const speedIncrease = diffData.ballSpeedIncrease;
    ball.vx *= speedIncrease;
    ball.vy *= speedIncrease;
    
    if (isPlayer) {
        pongGame.consecutiveHits++;
        pongGame.maxConsecutive = Math.max(pongGame.maxConsecutive, pongGame.consecutiveHits);
        pongGame.currentRally++;
        
        // Check for perfect hit (center of paddle)
        if (Math.abs(hitPos - 0.5) < 0.2) {
            pongGame.perfectHits++;
            createParticles(ball.x, ball.y, '#00ff00');
        }
        
        // Check for power shot (edge hits)
        if (Math.abs(hitPos - 0.5) > 0.4) {
            pongGame.powerShots++;
            createParticles(ball.x, ball.y, '#ff00ff');
        }
        
        // Check for corner shot
        if (ball.y < 50 || ball.y > pongGame.canvas.height - 50) {
            pongGame.cornerShots++;
        }
    } else {
        pongGame.consecutiveHits = 0;
        if (pongGame.currentRally > pongGame.longestRally) {
            pongGame.longestRally = pongGame.currentRally;
            if (pongGame.currentRally >= 10) {
                pongGame.rallies++;
            }
        }
    }
    
    // Screen shake effect
    pongGame.screenShake = 5;
    
    createParticles(ball.x, ball.y, '#ffffff');
}

function updatePowerUps(deltaTime) {
    if (pongGame.powerUpActive) {
        pongGame.powerUpTime -= deltaTime * 16.67;
        if (pongGame.powerUpTime <= 0) {
            deactivatePowerUp();
        }
    }
    
    // Random power-up spawn in power mode
    const modeData = GAME_MODES.find(m => m.name === pongGame.gameMode);
    if (modeData.powerUps && Math.random() < 0.001) {
        activateRandomPowerUp();
    }
}

function activateRandomPowerUp() {
    if (pongGame.powerUpActive) return;
    
    const powerUp = POWER_UPS[Math.floor(Math.random() * POWER_UPS.length)];
    pongGame.powerUpActive = true;
    pongGame.powerUpType = powerUp.type;
    pongGame.powerUpTime = powerUp.duration;
    
    switch (powerUp.type) {
        case 'speed':
            pongGame.paddleSpeedMultiplier = 1.5;
            break;
        case 'size':
            pongGame.playerPaddle.height *= 1.3;
            break;
        case 'slow':
            pongGame.ballSpeedMultiplier = 0.7;
            break;
        case 'multi':
            // Multi-ball effect would be complex, simplified here
            pongGame.speedBoosts++;
            break;
    }
    
    showPongMessage(`POWER-UP: ${powerUp.effect.toUpperCase()}!`, 2000);
}

function deactivatePowerUp() {
    switch (pongGame.powerUpType) {
        case 'speed':
            pongGame.paddleSpeedMultiplier = 1.0;
            break;
        case 'size':
            const paddleData = PADDLE_SIZES.find(p => p.name === document.getElementById('paddleSize').value);
            pongGame.playerPaddle.height = paddleData.height;
            break;
        case 'slow':
            pongGame.ballSpeedMultiplier = 1.0;
            break;
    }
    
    pongGame.powerUpActive = false;
    pongGame.powerUpType = null;
}

function createParticles(x, y, color) {
    for (let i = 0; i < 8; i++) {
        pongGame.particles.push({
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 10,
            vy: (Math.random() - 0.5) * 10,
            life: 30,
            maxLife: 30,
            color: color
        });
    }
}

function updateParticles(deltaTime) {
    pongGame.particles = pongGame.particles.filter(particle => {
        particle.x += particle.vx * deltaTime;
        particle.y += particle.vy * deltaTime;
        particle.life -= deltaTime;
        return particle.life > 0;
    });
    
    if (pongGame.screenShake > 0) {
        pongGame.screenShake -= deltaTime;
    }
}

function checkGameEnd() {
    if (pongGame.score >= pongGame.targetScore) {
        endPongGame('PLAYER WINS');
    } else if (pongGame.opponentScore >= pongGame.targetScore) {
        endPongGame('OPPONENT WINS');
    }
}

function endPongGame(reason) {
    pongGame.isPlaying = false;
    pongGame.gamePhase = 'finished';
    
    if (pongGame.gameTimer) {
        clearInterval(pongGame.gameTimer);
    }
    
    if (pongGame.animationId) {
        cancelAnimationFrame(pongGame.animationId);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pongGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === pongGame.gameMode);
    
    // Ultra-strict winning requirements
    const scoreRatio = pongGame.score / pongGame.targetScore;
    const baseWinChance = diffData.winRate;
    
    // Extremely demanding requirements
    const minPerfectHits = 8;           // Need 8+ perfect hits
    const minConsecutiveHits = 15;      // Need 15+ consecutive hits
    const minPowerShots = 5;            // Need 5+ power shots
    const minCornerShots = 3;           // Need 3+ corner shots
    const minRallies = 2;               // Need 2+ long rallies (10+ hits)
    const minLongestRally = 12;         // Need 12+ hit longest rally
    const maxOpponentScore = Math.floor(pongGame.targetScore * 0.3); // Opponent can't score more than 30%
    
    const playerWon = reason === 'PLAYER WINS';
    const meetsAllRequirements = 
        playerWon &&
        pongGame.perfectHits >= minPerfectHits &&
        pongGame.maxConsecutive >= minConsecutiveHits &&
        pongGame.powerShots >= minPowerShots &&
        pongGame.cornerShots >= minCornerShots &&
        pongGame.rallies >= minRallies &&
        pongGame.longestRally >= minLongestRally &&
        pongGame.opponentScore <= maxOpponentScore &&
        scoreRatio >= 1.0;
    
    // Performance bonuses
    const perfectRatio = pongGame.perfectHits / Math.max(1, pongGame.score);
    const rallyBonus = pongGame.longestRally / 20;
    const dominanceBonus = Math.max(0, (pongGame.score - pongGame.opponentScore) / pongGame.targetScore);
    
    const performanceMultiplier = 1 + perfectRatio * 0.3 + rallyBonus * 0.2 + dominanceBonus * 0.4;
    const finalWinChance = meetsAllRequirements ? baseWinChance * performanceMultiplier : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with bonuses
        const paddleData = PADDLE_SIZES.find(p => p.name === document.getElementById('paddleSize').value);
        const speedData = BALL_SPEEDS.find(s => s.name === document.getElementById('ballSpeed').value);
        
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier * 
                              paddleData.multiplier * speedData.multiplier;
        const perfectBonus = pongGame.perfectHits * 0.1;
        const consecutiveBonus = pongGame.maxConsecutive * 0.05;
        const powerShotBonus = pongGame.powerShots * 0.08;
        const rallyBonusMultiplier = pongGame.rallies * 0.15;
        const dominanceBonusMultiplier = dominanceBonus * 0.5;
        
        const totalMultiplier = baseMultiplier * (1 + perfectBonus + consecutiveBonus + 
                                                 powerShotBonus + rallyBonusMultiplier + dominanceBonusMultiplier);
        const winnings = Math.floor(pongGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('pongResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🏓 PONG CHAMPION! 🏓</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
        
        addPongHistory(pongGame.score, pongGame.opponentScore, true);
    } else {
        const requirements = [
            `${minPerfectHits}+ Perfect Hits (${pongGame.perfectHits})`,
            `${minConsecutiveHits}+ Consecutive (${pongGame.maxConsecutive})`,
            `${minPowerShots}+ Power Shots (${pongGame.powerShots})`,
            `${minCornerShots}+ Corner Shots (${pongGame.cornerShots})`,
            `${minRallies}+ Long Rallies (${pongGame.rallies})`,
            `${minLongestRally}+ Longest Rally (${pongGame.longestRally})`,
            `Max ${maxOpponentScore} Opponent Score (${pongGame.opponentScore})`,
            `Must Win Match (${playerWon ? 'YES' : 'NO'})`
        ];
        
        document.getElementById('pongResult').innerHTML = 
            `<span class="text-red-400">🏓 ${reason} 🏓</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Need: ${requirements.slice(0, 4).join(', ')}<br>
            ${requirements.slice(4, 8).join(', ')}</div>`;
        
        addPongHistory(pongGame.score, pongGame.opponentScore, false);
    }
    
    updatePongDisplay();
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
        document.getElementById('finalResult').innerHTML = 
            won ? '<span class="text-green-400">🏆 VICTORY! 🏆</span>' : 
                  '<span class="text-red-400">💔 DEFEAT 💔</span>';
        document.getElementById('finalStats').innerHTML = 
            `Final Score: ${pongGame.score} - ${pongGame.opponentScore}<br>
             Perfect Hits: ${pongGame.perfectHits} | Longest Rally: ${pongGame.longestRally}<br>
             Power Shots: ${pongGame.powerShots} | Max Consecutive: ${pongGame.maxConsecutive}`;
    }, 3000);
}

function drawPongTable() {
    const ctx = pongGame.ctx;
    const canvas = pongGame.canvas;
    
    // Apply screen shake
    ctx.save();
    if (pongGame.screenShake > 0) {
        ctx.translate(
            (Math.random() - 0.5) * pongGame.screenShake,
            (Math.random() - 0.5) * pongGame.screenShake
        );
    }
    
    // Clear canvas
    ctx.fillStyle = '#000011';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw center line
    ctx.strokeStyle = '#444';
    ctx.lineWidth = 2;
    ctx.setLineDash([10, 10]);
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2, 0);
    ctx.lineTo(canvas.width / 2, canvas.height);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // Draw paddles
    ctx.fillStyle = '#00ffff';
    ctx.fillRect(pongGame.playerPaddle.x, pongGame.playerPaddle.y, 
                 pongGame.playerPaddle.width, pongGame.playerPaddle.height);
    
    ctx.fillStyle = '#ff4444';
    ctx.fillRect(pongGame.opponentPaddle.x, pongGame.opponentPaddle.y, 
                 pongGame.opponentPaddle.width, pongGame.opponentPaddle.height);
    
    // Draw ball trail
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    for (let i = 0; i < pongGame.ball.trail.length - 1; i++) {
        const alpha = i / pongGame.ball.trail.length;
        ctx.globalAlpha = alpha * 0.5;
        ctx.moveTo(pongGame.ball.trail[i].x, pongGame.ball.trail[i].y);
        ctx.lineTo(pongGame.ball.trail[i + 1].x, pongGame.ball.trail[i + 1].y);
    }
    ctx.stroke();
    ctx.globalAlpha = 1;
    
    // Draw ball
    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    ctx.arc(pongGame.ball.x, pongGame.ball.y, pongGame.ball.radius, 0, Math.PI * 2);
    ctx.fill();
    
    // Draw particles
    pongGame.particles.forEach(particle => {
        const alpha = particle.life / particle.maxLife;
        ctx.globalAlpha = alpha;
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, 2, 0, Math.PI * 2);
        ctx.fill();
    });
    ctx.globalAlpha = 1;
    
    // Draw scores
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 48px monospace';
    ctx.textAlign = 'center';
    ctx.fillText(pongGame.score.toString(), canvas.width / 4, 60);
    ctx.fillText(pongGame.opponentScore.toString(), (canvas.width * 3) / 4, 60);
    
    // Draw power-up indicator
    if (pongGame.powerUpActive) {
        ctx.fillStyle = '#ff00ff';
        ctx.font = 'bold 16px monospace';
        ctx.textAlign = 'left';
        ctx.fillText(`POWER-UP: ${pongGame.powerUpType.toUpperCase()}`, 10, canvas.height - 20);
        
        // Power-up timer bar
        const barWidth = 100;
        const barHeight = 8;
        const timeRatio = pongGame.powerUpTime / 5000; // Assuming 5s duration
        
        ctx.fillStyle = '#333';
        ctx.fillRect(10, canvas.height - 40, barWidth, barHeight);
        ctx.fillStyle = '#ff00ff';
        ctx.fillRect(10, canvas.height - 40, barWidth * timeRatio, barHeight);
    }
    
    ctx.restore();
}

function showPongMessage(text, duration) {
    const messageEl = document.getElementById('pongMessage');
    messageEl.textContent = text;
    messageEl.style.opacity = '1';
    
    setTimeout(() => {
        messageEl.style.opacity = '0';
    }, duration);
}

function updatePongDisplay() {
    document.getElementById('currentScore').textContent = `${pongGame.score} - ${pongGame.opponentScore}`;
    document.getElementById('perfectHits').textContent = pongGame.perfectHits;
    document.getElementById('consecutiveHits').textContent = pongGame.consecutiveHits;
    document.getElementById('powerShots').textContent = pongGame.powerShots;
    document.getElementById('rallies').textContent = pongGame.rallies;
    document.getElementById('timeLeft').textContent = 
        Math.floor(pongGame.timeLeft / 60) + ':' + 
        String(pongGame.timeLeft % 60).padStart(2, '0');
}

function addPongHistory(playerScore, opponentScore, won) {
    const history = document.getElementById('pongHistory');
    const result = document.createElement('div');
    result.className = `flex justify-between py-1 ${won ? 'text-green-400' : 'text-red-400'}`;
    result.innerHTML = `
        <span>${playerScore} - ${opponentScore}</span>
        <span class="text-xs">${won ? 'WIN' : 'LOSS'}</span>
    `;
    
    history.insertBefore(result, history.firstChild);
    
    // Keep only last 8 results
    while (history.children.length > 8) {
        history.removeChild(history.lastChild);
    }
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPongGame();
});