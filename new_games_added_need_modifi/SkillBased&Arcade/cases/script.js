// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadCasesGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Case Selection -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-teal-500/30">
                            <h4 class="text-xl font-bold mb-4 text-teal-400">NEON CASES</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">SELECT CASE TYPE</label>
                                <select id="caseType" class="w-full bg-black/50 border border-teal-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="basic">Basic Case - $10</option>
                                    <option value="rare" selected>Rare Case - $25</option>
                                    <option value="epic">Epic Case - $50</option>
                                    <option value="legendary">Legendary Case - $100</option>
                                </select>
                            </div>
                            
                            <button id="buyCase" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                BUY CASE
                            </button>
                            
                            <button id="openCase" class="w-full py-3 rounded-lg font-bold bg-teal-600 hover:bg-teal-700 text-white mb-4" disabled>
                                OPEN CASE
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Cases Owned</div>
                                <div id="casesOwned" class="text-xl font-bold text-teal-400 neon-glow">0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Total Value Won</div>
                                <div id="totalValueWon" class="text-xl font-bold text-green-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Drop Rates -->
                        <div class="bg-black/30 p-4 rounded-xl border border-teal-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-teal-400">DROP RATES</h5>
                            <div id="caseDropRates" class="text-sm space-y-1">
                                <!-- Drop rates will be generated -->
                            </div>
                        </div>
                        
                        <!-- Inventory -->
                        <div class="bg-black/30 p-4 rounded-xl border border-teal-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-teal-400">INVENTORY</h5>
                            <div id="casesInventory" class="max-h-32 overflow-y-auto text-sm">
                                <div class="text-gray-400 text-center">No cases owned</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Case Opening Animation -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-teal-500/30">
                            <div id="caseOpeningArea" class="relative bg-black/50 rounded-lg p-6 h-96">
                                <!-- Case Display -->
                                <div id="caseDisplay" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                    <div id="caseBox" class="w-32 h-32 bg-gradient-to-br from-teal-500 to-blue-600 rounded-xl flex items-center justify-center text-4xl border-4 border-teal-400 neon-border cursor-pointer transition-all duration-500">
                                        📦
                                    </div>
                                    <div id="caseLabel" class="text-center mt-2 text-teal-400 font-bold">No Case</div>
                                </div>
                                
                                <!-- Opening Animation -->
                                <div id="openingAnimation" class="absolute inset-0 hidden">
                                    <div class="flex items-center justify-center h-full">
                                        <div id="rewardDisplay" class="text-center">
                                            <div id="rewardIcon" class="text-8xl mb-4">💎</div>
                                            <div id="rewardName" class="text-2xl font-bold mb-2">Amazing Item</div>
                                            <div id="rewardValue" class="text-xl text-green-400 neon-glow">$100</div>
                                            <div id="rewardRarity" class="text-sm text-purple-400 mt-2">Legendary</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Item Carousel -->
                                <div id="itemCarousel" class="absolute bottom-4 left-0 right-0 hidden">
                                    <div class="flex items-center justify-center space-x-2 overflow-hidden">
                                        <div id="carouselItems" class="flex space-x-2 transition-transform duration-3000">
                                            <!-- Carousel items will be generated -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="caseStatus" class="text-center mt-4 text-lg font-semibold">Select and buy a case to get started</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeCases();
        }
        
        let casesGame = {
            ownedCases: { basic: 0, rare: 0, epic: 0, legendary: 0 },
            totalValueWon: 0,
            isOpening: false
        };
        
        function initializeCases() {
            document.getElementById('buyCase').addEventListener('click', buyCase);
            document.getElementById('openCase').addEventListener('click', openCase);
            document.getElementById('caseType').addEventListener('change', updateCaseInfo);
            
            updateCaseInfo();
            updateCasesDisplay();
        }
        
        function updateCaseInfo() {
            const caseType = document.getElementById('caseType').value;
            updateDropRates(caseType);
            updateCaseVisual(caseType);
            updateCasesDisplay();
        }
        
        function updateDropRates(caseType) {
            const dropRates = getCaseDropRates(caseType);
            const dropRatesDiv = document.getElementById('caseDropRates');
            
            dropRatesDiv.innerHTML = '';
            Object.entries(dropRates).forEach(([rarity, rate]) => {
                const row = document.createElement('div');
                row.className = 'flex justify-between items-center';
                const color = getRarityColor(rarity);
                row.innerHTML = `
                    <span class="${color}">${rarity}:</span>
                    <span class="text-gray-400">${rate}%</span>
                `;
                dropRatesDiv.appendChild(row);
            });
        }
        
        function getCaseDropRates(caseType) {
            const rates = {
                basic: { Common: 60, Uncommon: 30, Rare: 9, Epic: 1 },
                rare: { Common: 40, Uncommon: 35, Rare: 20, Epic: 4, Legendary: 1 },
                epic: { Uncommon: 40, Rare: 35, Epic: 20, Legendary: 5 },
                legendary: { Rare: 50, Epic: 35, Legendary: 15 }
            };
            return rates[caseType] || rates.basic;
        }
        
        function getRarityColor(rarity) {
            const colors = {
                'Common': 'text-gray-400',
                'Uncommon': 'text-green-400',
                'Rare': 'text-blue-400',
                'Epic': 'text-purple-400',
                'Legendary': 'text-yellow-400'
            };
            return colors[rarity] || 'text-gray-400';
        }
        
        function updateCaseVisual(caseType) {
            const caseBox = document.getElementById('caseBox');
            const caseLabel = document.getElementById('caseLabel');
            
            const caseStyles = {
                basic: { 
                    gradient: 'from-gray-500 to-gray-700',
                    border: 'border-gray-400',
                    label: 'Basic Case'
                },
                rare: { 
                    gradient: 'from-teal-500 to-blue-600',
                    border: 'border-teal-400',
                    label: 'Rare Case'
                },
                epic: { 
                    gradient: 'from-purple-500 to-pink-600',
                    border: 'border-purple-400',
                    label: 'Epic Case'
                },
                legendary: { 
                    gradient: 'from-yellow-500 to-orange-600',
                    border: 'border-yellow-400',
                    label: 'Legendary Case'
                }
            };
            
            const style = caseStyles[caseType];
            caseBox.className = `w-32 h-32 bg-gradient-to-br ${style.gradient} rounded-xl flex items-center justify-center text-4xl border-4 ${style.border} neon-border cursor-pointer transition-all duration-500`;
            caseLabel.textContent = style.label;
        }
        
        function updateCasesDisplay() {
            const totalCases = Object.values(casesGame.ownedCases).reduce((a, b) => a + b, 0);
            document.getElementById('casesOwned').textContent = totalCases;
            document.getElementById('totalValueWon').textContent = '$' + casesGame.totalValueWon;
            
            // Update inventory
            const inventory = document.getElementById('casesInventory');
            inventory.innerHTML = '';
            
            if (totalCases === 0) {
                inventory.innerHTML = '<div class="text-gray-400 text-center">No cases owned</div>';
            } else {
                Object.entries(casesGame.ownedCases).forEach(([type, count]) => {
                    if (count > 0) {
                        const item = document.createElement('div');
                        item.className = 'flex justify-between py-1';
                        item.innerHTML = `
                            <span class="capitalize">${type} Cases:</span>
                            <span class="text-teal-400">${count}</span>
                        `;
                        inventory.appendChild(item);
                    }
                });
            }
            
            // Update open button
            const caseType = document.getElementById('caseType').value;
            document.getElementById('openCase').disabled = casesGame.ownedCases[caseType] === 0 || casesGame.isOpening;
        }
        
        function buyCase() {
            const caseType = document.getElementById('caseType').value;
            const prices = { basic: 10, rare: 25, epic: 50, legendary: 100 };
            const price = prices[caseType];
            
            if (price > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            balance -= price;
            updateBalance();
            
            casesGame.ownedCases[caseType]++;
            updateCasesDisplay();
            
            document.getElementById('caseStatus').innerHTML = 
                `<span class="text-green-400">Purchased ${caseType} case!</span>`;
            
            setTimeout(() => {
                document.getElementById('caseStatus').textContent = 'Case ready to open!';
            }, 2000);
        }
        
        function openCase() {
            const caseType = document.getElementById('caseType').value;
            
            if (casesGame.ownedCases[caseType] === 0 || casesGame.isOpening) {
                return;
            }
            
            casesGame.isOpening = true;
            casesGame.ownedCases[caseType]--;
            updateCasesDisplay();
            
            // Generate reward
            const reward = generateCaseReward(caseType);
            
            // Animate case opening
            animateCaseOpening(reward);
        }
        
        function generateCaseReward(caseType) {
            const dropRates = getCaseDropRates(caseType);
            const random = Math.random() * 100;
            
            let cumulative = 0;
            let selectedRarity = 'Common';
            
            for (const [rarity, rate] of Object.entries(dropRates)) {
                cumulative += rate;
                if (random <= cumulative) {
                    selectedRarity = rarity;
                    break;
                }
            }
            
            // Generate item based on rarity
            const items = generateItemsForRarity(selectedRarity, caseType);
            const selectedItem = items[Math.floor(Math.random() * items.length)];
            
            return selectedItem;
        }
        
        function generateItemsForRarity(rarity, caseType) {
            const baseValues = { basic: 1, rare: 2.5, epic: 5, legendary: 10 };
            const rarityMultipliers = { 
                Common: 0.5, Uncommon: 1, Rare: 3, Epic: 8, Legendary: 25 
            };
            
            const icons = {
                Common: ['🔧', '⚙️', '🔩', '🪙'],
                Uncommon: ['💚', '🛡️', '⚔️', '🎯'],
                Rare: ['💙', '💎', '🔮', '⭐'],
                Epic: ['💜', '👑', '🏆', '⚡'],
                Legendary: ['💛', '🌟', '🎊', '💫']
            };
            
            const names = {
                Common: ['Scrap Metal', 'Basic Component', 'Simple Tool', 'Copper Coin'],
                Uncommon: ['Enhanced Part', 'Combat Gear', 'Targeting System', 'Silver Coin'],
                Rare: ['Rare Crystal', 'Diamond Fragment', 'Magic Orb', 'Gold Star'],
                Epic: ['Royal Crown', 'Victory Trophy', 'Lightning Core', 'Purple Gem'],
                Legendary: ['Golden Star', 'Cosmic Fragment', 'Celebration Token', 'Stellar Core']
            };
            
            const baseValue = baseValues[caseType] * rarityMultipliers[rarity];
            const variance = 0.3; // 30% variance
            const minValue = Math.max(1, baseValue * (1 - variance));
            const maxValue = baseValue * (1 + variance);
            const value = Math.floor(Math.random() * (maxValue - minValue + 1)) + minValue;
            
            const availableIcons = icons[rarity];
            const availableNames = names[rarity];
            
            return [{
                icon: availableIcons[Math.floor(Math.random() * availableIcons.length)],
                name: availableNames[Math.floor(Math.random() * availableNames.length)],
                rarity: rarity,
                value: value
            }];
        }
        
        function animateCaseOpening(reward) {
            const caseBox = document.getElementById('caseBox');
            const caseDisplay = document.getElementById('caseDisplay');
            const openingAnimation = document.getElementById('openingAnimation');
            
            document.getElementById('caseStatus').textContent = 'Opening case...';
            
            // Shake animation
            caseBox.style.animation = 'shake 0.5s ease-in-out infinite';
            
            setTimeout(() => {
                // Stop shaking and hide case
                caseBox.style.animation = '';
                caseDisplay.style.opacity = '0';
                
                // Show opening animation
                openingAnimation.classList.remove('hidden');
                
                // Reveal reward
                document.getElementById('rewardIcon').textContent = reward.icon;
                document.getElementById('rewardName').textContent = reward.name;
                document.getElementById('rewardValue').textContent = '$' + reward.value;
                document.getElementById('rewardRarity').textContent = reward.rarity;
                document.getElementById('rewardRarity').className = `text-sm mt-2 ${getRarityColor(reward.rarity)}`;
                
                // Add value to balance and total
                balance += reward.value;
                casesGame.totalValueWon += reward.value;
                updateBalance();
                updateCasesDisplay();
                
                document.getElementById('caseStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">You got: ${reward.name} worth $${reward.value}!</span>`;
                
                // Reset after delay
                setTimeout(() => {
                    openingAnimation.classList.add('hidden');
                    caseDisplay.style.opacity = '1';
                    casesGame.isOpening = false;
                    updateCasesDisplay();
                    document.getElementById('caseStatus').textContent = 'Select and buy a case to get started';
                }, 4000);
                
            }, 2000);
        }
        
        // Add shake animation to CSS
        const shakeCSS = `
            @keyframes shake {
                0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
                25% { transform: translate(-50%, -50%) rotate(-2deg); }
                75% { transform: translate(-50%, -50%) rotate(2deg); }
            }
        `;
        
        if (!document.getElementById('shake-style')) {
            const style = document.createElement('style');
            style.id = 'shake-style';
            style.textContent = shakeCSS;
            document.head.appendChild(style);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCasesGame();
});