// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadFlipGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                            <h4 class="text-xl font-bold mb-4 text-cyan-400">QUANTUM FLIP</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="flipBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">CHOOSE SIDE</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <button id="flipHeads" class="flip-choice py-3 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white border-2 border-transparent">
                                        HEADS
                                    </button>
                                    <button id="flipTails" class="flip-choice py-3 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white border-2 border-transparent">
                                        TAILS
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">MULTIPLIER</label>
                                <select id="flipMultiplier" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="2">2x (50% chance)</option>
                                    <option value="3">3x (33% chance)</option>
                                    <option value="4">4x (25% chance)</option>
                                    <option value="5">5x (20% chance)</option>
                                    <option value="10">10x (10% chance)</option>
                                </select>
                            </div>
                            
                            <button id="flipCoin" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4" disabled>
                                FLIP COIN
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Win Chance</div>
                                <div id="flipWinChance" class="text-xl font-bold text-cyan-400 neon-glow">50%</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="flipPotentialWin" class="text-xl font-bold text-green-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Recent Flips -->
                        <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-cyan-400">RECENT FLIPS</h5>
                            <div id="flipHistory" class="flex flex-wrap gap-2">
                                <!-- Recent flips will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Coin Display -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                            <div id="flipDisplay" class="relative bg-black/50 rounded-lg p-4 h-96 flex items-center justify-center">
                                <div id="quantumCoin" class="w-48 h-48 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full flex items-center justify-center text-6xl font-bold border-4 border-cyan-400 neon-border transition-all duration-500">
                                    ?
                                </div>
                            </div>
                            <div id="flipStatus" class="text-center mt-4 text-lg font-semibold">Choose your side and flip the quantum coin</div>
                            <div id="flipResult" class="text-center mt-2 text-xl font-bold"></div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeFlip();
        }
        
        let flipGame = {
            selectedSide: null,
            isFlipping: false,
            multiplier: 2
        };
        
        function initializeFlip() {
            document.getElementById('flipHeads').addEventListener('click', () => selectFlipSide('heads'));
            document.getElementById('flipTails').addEventListener('click', () => selectFlipSide('tails'));
            document.getElementById('flipCoin').addEventListener('click', flipCoin);
            document.getElementById('flipMultiplier').addEventListener('change', updateFlipMultiplier);
            
            updateFlipCalculations();
        }
        
        function selectFlipSide(side) {
            if (flipGame.isFlipping) return;
            
            flipGame.selectedSide = side;
            
            // Update button styles
            document.querySelectorAll('.flip-choice').forEach(btn => {
                btn.classList.remove('border-cyan-400', 'neon-border');
                btn.classList.add('border-transparent');
            });
            
            const selectedButton = document.getElementById(side === 'heads' ? 'flipHeads' : 'flipTails');
            selectedButton.classList.add('border-cyan-400', 'neon-border');
            selectedButton.classList.remove('border-transparent');
            
            // Enable flip button
            document.getElementById('flipCoin').disabled = false;
            
            updateFlipCalculations();
        }
        
        function updateFlipMultiplier() {
            flipGame.multiplier = parseInt(document.getElementById('flipMultiplier').value);
            updateFlipCalculations();
        }
        
        function updateFlipCalculations() {
            if (!flipGame.selectedSide) return;
            
            const betAmount = parseInt(document.getElementById('flipBet').value) || 0;
            const winChance = Math.round(100 / flipGame.multiplier);
            const potentialWin = betAmount * flipGame.multiplier;
            
            document.getElementById('flipWinChance').textContent = winChance + '%';
            document.getElementById('flipPotentialWin').textContent = '$' + potentialWin;
        }
        
        function flipCoin() {
            const betAmount = parseInt(document.getElementById('flipBet').value);
            
            if (betAmount > balance || !flipGame.selectedSide || flipGame.isFlipping) {
                if (betAmount > balance) alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            flipGame.isFlipping = true;
            
            // Update UI
            document.getElementById('flipCoin').disabled = true;
            document.getElementById('flipStatus').textContent = 'Quantum coin is spinning...';
            document.getElementById('flipResult').textContent = '';
            
            // Animate coin flip
            const coin = document.getElementById('quantumCoin');
            let flipCount = 0;
            const maxFlips = 20;
            
            const flipInterval = setInterval(() => {
                flipCount++;
                
                // Alternate between ? and spinning effect
                if (flipCount % 2 === 0) {
                    coin.textContent = '⚡';
                    coin.style.transform = 'rotateY(180deg) scale(1.1)';
                } else {
                    coin.textContent = '🌀';
                    coin.style.transform = 'rotateY(0deg) scale(0.9)';
                }
                
                // Change colors during spin
                const colors = ['from-cyan-400 to-blue-600', 'from-purple-400 to-pink-600', 'from-green-400 to-teal-600'];
                coin.className = `w-48 h-48 bg-gradient-to-br ${colors[flipCount % colors.length]} rounded-full flex items-center justify-center text-6xl font-bold border-4 border-cyan-400 neon-border transition-all duration-500`;
                
                if (flipCount >= maxFlips) {
                    clearInterval(flipInterval);
                    resolveCoinFlip(betAmount);
                }
            }, 150);
        }
        
        function resolveCoinFlip(betAmount) {
            // Determine result based on multiplier odds
            const winChance = 100 / flipGame.multiplier;
            const random = Math.random() * 100;
            const won = random < winChance;
            
            // Determine coin side (for visual only)
            let resultSide;
            if (flipGame.multiplier === 2) {
                // True 50/50 for 2x multiplier
                resultSide = Math.random() < 0.5 ? 'heads' : 'tails';
                const actualWin = (resultSide === flipGame.selectedSide);
                if (actualWin !== won) {
                    resultSide = flipGame.selectedSide === 'heads' ? 'tails' : 'heads';
                }
            } else {
                // For other multipliers, show based on win/loss
                resultSide = won ? flipGame.selectedSide : (flipGame.selectedSide === 'heads' ? 'tails' : 'heads');
            }
            
            // Update coin display
            const coin = document.getElementById('quantumCoin');
            coin.style.transform = 'rotateY(0deg) scale(1)';
            
            if (resultSide === 'heads') {
                coin.textContent = '👑';
                coin.className = 'w-48 h-48 bg-gradient-to-br from-blue-400 to-cyan-600 rounded-full flex items-center justify-center text-6xl font-bold border-4 border-blue-400 neon-border transition-all duration-500';
            } else {
                coin.textContent = '⚡';
                coin.className = 'w-48 h-48 bg-gradient-to-br from-purple-400 to-pink-600 rounded-full flex items-center justify-center text-6xl font-bold border-4 border-purple-400 neon-border transition-all duration-500';
            }
            
            // Calculate winnings
            let winnings = 0;
            if (won) {
                winnings = betAmount * flipGame.multiplier;
                balance += winnings;
                updateBalance();
            }
            
            // Update display
            if (won) {
                document.getElementById('flipStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">You won!</span>`;
                document.getElementById('flipResult').innerHTML = 
                    `<span class="text-green-400">Coin landed on ${resultSide.toUpperCase()}! Won $${winnings}</span>`;
            } else {
                document.getElementById('flipStatus').innerHTML = 
                    `<span class="text-red-400">You lost!</span>`;
                document.getElementById('flipResult').innerHTML = 
                    `<span class="text-red-400">Coin landed on ${resultSide.toUpperCase()}!</span>`;
            }
            
            // Add to history
            addFlipHistory(resultSide, won);
            
            // Reset game state
            setTimeout(() => {
                flipGame.isFlipping = false;
                flipGame.selectedSide = null;
                
                // Reset UI
                document.querySelectorAll('.flip-choice').forEach(btn => {
                    btn.classList.remove('border-cyan-400', 'neon-border');
                    btn.classList.add('border-transparent');
                });
                
                document.getElementById('flipCoin').disabled = true;
                document.getElementById('flipStatus').textContent = 'Choose your side and flip the quantum coin';
                document.getElementById('flipResult').textContent = '';
                
                // Reset coin
                coin.textContent = '?';
                coin.className = 'w-48 h-48 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full flex items-center justify-center text-6xl font-bold border-4 border-cyan-400 neon-border transition-all duration-500';
                coin.style.transform = 'rotateY(0deg) scale(1)';
                
                updateFlipCalculations();
            }, 3000);
        }
        
        function addFlipHistory(result, won) {
            const history = document.getElementById('flipHistory');
            const flip = document.createElement('div');
            flip.className = `w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold border-2 ${
                won ? 'bg-green-600/20 border-green-400 text-green-400' : 'bg-red-600/20 border-red-400 text-red-400'
            }`;
            flip.textContent = result === 'heads' ? '👑' : '⚡';
            flip.title = `${result.toUpperCase()} - ${won ? 'WIN' : 'LOSE'}`;
            
            history.insertBefore(flip, history.firstChild);
            
            // Keep only last 15 flips
            while (history.children.length > 15) {
                history.removeChild(history.lastChild);
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadFlipGame();
});