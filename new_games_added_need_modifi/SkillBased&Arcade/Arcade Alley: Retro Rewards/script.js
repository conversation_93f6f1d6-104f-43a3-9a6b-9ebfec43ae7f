// Game state
let balance = 1000;
let retroRewardsGame = {
    isPlaying: false,
    betAmount: 0,
    gameType: 'memory',
    complexity: 'normal',
    reactionSpeed: 'medium',
    patternLength: 'standard',
    distractions: 'none',
    consecutiveWins: 0,
    totalGames: 0,
    perfectRounds: 0,
    maxStreak: 0,
    retroLevel: 1,
    retroEnergy: 0,
    currentPattern: [],
    playerPattern: [],
    currentStep: 0,
    gamePhase: 'waiting',
    sequenceTimer: null,
    reactionTimer: null,
    startTime: 0
};

// Game types
const GAME_TYPES = [
    { name: 'memory', difficulty: 1.0, description: 'Memory Pattern' },
    { name: 'reaction', difficulty: 1.3, description: 'Reaction Speed' },
    { name: 'sequence', difficulty: 1.6, description: 'Sequence Match' },
    { name: 'rhythm', difficulty: 2.0, description: 'Rhythm Game' },
    { name: 'chaos', difficulty: 2.5, description: 'Chaos Mode' }
];

// Complexity levels
const COMPLEXITY_LEVELS = [
    { name: 'simple', steps: 4, timeWindow: 2.0, description: 'Simple (4 steps)' },
    { name: 'normal', steps: 6, timeWindow: 1.5, description: 'Normal (6 steps)' },
    { name: 'complex', steps: 9, timeWindow: 1.2, description: 'Complex (9 steps)' },
    { name: 'expert', steps: 12, timeWindow: 1.0, description: 'Expert (12 steps)' },
    { name: 'master', steps: 16, timeWindow: 0.8, description: 'Master (16 steps)' }
];

// Reaction speeds
const REACTION_SPEEDS = [
    { name: 'slow', window: 2.5, description: 'Slow Reactions' },
    { name: 'medium', window: 1.8, description: 'Medium Speed' },
    { name: 'fast', window: 1.2, description: 'Fast Reactions' },
    { name: 'lightning', window: 0.8, description: 'Lightning Speed' },
    { name: 'instant', window: 0.5, description: 'Instant Reactions' }
];

// Pattern lengths
const PATTERN_LENGTHS = [
    { name: 'short', length: 3, description: 'Short Patterns' },
    { name: 'standard', length: 5, description: 'Standard Length' },
    { name: 'long', length: 8, description: 'Long Patterns' },
    { name: 'extended', length: 12, description: 'Extended Patterns' },
    { name: 'marathon', length: 20, description: 'Marathon Mode' }
];

// Distractions
const DISTRACTIONS = [
    { name: 'none', effect: 1.0, description: 'No Distractions' },
    { name: 'visual', effect: 1.3, description: 'Visual Noise' },
    { name: 'audio', effect: 1.5, description: 'Audio Interference' },
    { name: 'motion', effect: 1.8, description: 'Motion Blur' },
    { name: 'chaos', effect: 2.2, description: 'Total Chaos' }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Retro Rewards Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                    <h4 class="text-xl font-bold mb-4 text-pink-400">🕹️ RETRO REWARDS 🕹️</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 RETRO BET</label>
                        <input type="number" id="retroBet" value="15" min="5" max="${balance}" 
                               class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 GAME TYPE</label>
                        <select id="gameType" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="memory" selected>🧠 Memory Pattern</option>
                            <option value="reaction">⚡ Reaction Speed</option>
                            <option value="sequence">🔢 Sequence Match</option>
                            <option value="rhythm">🎵 Rhythm Game</option>
                            <option value="chaos">🌪️ Chaos Mode</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🧩 COMPLEXITY</label>
                        <select id="complexity" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="simple">🟢 Simple (4 steps)</option>
                            <option value="normal" selected>🟡 Normal (6 steps)</option>
                            <option value="complex">🟠 Complex (9 steps)</option>
                            <option value="expert">🔴 Expert (12 steps)</option>
                            <option value="master">💀 Master (16 steps)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ REACTION SPEED</label>
                        <select id="reactionSpeed" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="slow">🐌 Slow Reactions</option>
                            <option value="medium" selected>🚶 Medium Speed</option>
                            <option value="fast">🏃 Fast Reactions</option>
                            <option value="lightning">⚡ Lightning Speed</option>
                            <option value="instant">🚀 Instant Reactions</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">📏 PATTERN LENGTH</label>
                        <select id="patternLength" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="short">📏 Short Patterns</option>
                            <option value="standard" selected>📐 Standard Length</option>
                            <option value="long">📏 Long Patterns</option>
                            <option value="extended">📏 Extended Patterns</option>
                            <option value="marathon">🏃 Marathon Mode</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌪️ DISTRACTIONS</label>
                        <select id="distractions" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="none" selected>❌ No Distractions</option>
                            <option value="visual">👁️ Visual Noise</option>
                            <option value="audio">🔊 Audio Interference</option>
                            <option value="motion">💫 Motion Blur</option>
                            <option value="chaos">🌪️ Total Chaos</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="startRetroGame" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🕹️ START RETRO CHALLENGE
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎯 Success Rate</div>
                        <div id="retroWinChance" class="text-lg font-bold text-red-400">3%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="retroPotentialPayout" class="text-2xl font-bold text-pink-400 neon-glow">500 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🕹️ Retro Level</div>
                        <div id="retroLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⚡ Retro Energy</div>
                        <div id="retroEnergyDisplay" class="text-lg font-bold text-cyan-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Game Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                    <h5 class="text-lg font-bold mb-4 text-pink-400 text-center">🕹️ RETRO ARCADE 🕹️</h5>
                    
                    <!-- Game Grid -->
                    <div class="mb-6">
                        <div id="retroGameGrid" class="grid grid-cols-4 gap-2 mb-4 max-w-md mx-auto">
                            <!-- Game buttons will be generated here -->
                        </div>
                    </div>
                    
                    <!-- Game Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🕹️ Game Status</div>
                            <div id="retroGameStatus" class="text-lg font-bold text-pink-400">Ready to play...</div>
                            <div class="text-xs text-gray-400">Type: <span id="typeStatus" class="text-blue-400">Memory</span></div>
                            <div class="text-xs text-gray-400">Complexity: <span id="complexityStatus" class="text-orange-400">Normal</span></div>
                            <div class="text-xs text-gray-400">Speed: <span id="speedStatus" class="text-green-400">Medium</span></div>
                        </div>
                    </div>
                    
                    <!-- Progress Display -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <div class="text-sm text-gray-400">Round</div>
                                    <div id="currentRound" class="text-lg font-bold text-cyan-400">0</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-400">Step</div>
                                    <div id="currentStep" class="text-lg font-bold text-green-400">0</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-400">Time</div>
                                    <div id="reactionTime" class="text-lg font-bold text-red-400">0.0s</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pattern Display -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🔢 Pattern Progress</div>
                            <div class="w-full bg-black/50 rounded-full h-3">
                                <div id="patternProgress" class="bg-gradient-to-r from-pink-500 to-purple-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="patternStatus" class="text-center mt-1 text-pink-300">Waiting for pattern...</div>
                        </div>
                    </div>
                    
                    <!-- Accuracy Meter -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🎯 Accuracy Meter</div>
                            <div class="w-full bg-black/50 rounded-full h-3">
                                <div id="accuracyMeter" class="bg-gradient-to-r from-red-500 to-green-500 h-3 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                            <div id="accuracyLevel" class="text-center mt-1 text-green-300">Perfect Accuracy</div>
                        </div>
                    </div>
                    
                    <div id="retroResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="retroStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Retro Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-pink-400">📊 RETRO RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Perfect Games</div>
                            <div id="consecutiveWins" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Games</div>
                            <div id="totalGames" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Perfect Rounds</div>
                            <div id="perfectRounds" class="text-lg font-bold text-yellow-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Max Streak</div>
                            <div id="maxStreak" class="text-lg font-bold text-orange-400">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- Retro Power System -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚡ RETRO POWER ⚡</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="retroPowerBar" class="bg-gradient-to-r from-pink-500 to-purple-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="retroPowerText" class="text-center mt-2 text-purple-300">Retro Power Charging...</div>
                </div>
            </div>
        </div>
    `;
    
    initializeRetroRewards();
}

function initializeRetroRewards() {
    document.getElementById('startRetroGame').addEventListener('click', startRetroGame);
    document.getElementById('gameType').addEventListener('change', updateGameType);
    document.getElementById('complexity').addEventListener('change', updateComplexity);
    document.getElementById('reactionSpeed').addEventListener('change', updateReactionSpeed);
    document.getElementById('patternLength').addEventListener('change', updatePatternLength);
    document.getElementById('distractions').addEventListener('change', updateDistractions);
    
    generateRetroGrid();
    updateGameType();
    updateComplexity();
    updateReactionSpeed();
    updatePatternLength();
    updateDistractions();
    updateRetroPayout();
}

function generateRetroGrid() {
    const grid = document.getElementById('retroGameGrid');
    grid.innerHTML = '';
    
    for (let i = 0; i < 16; i++) {
        const button = document.createElement('button');
        button.className = 'w-16 h-16 bg-black/50 border border-pink-500/30 rounded text-white text-lg hover:bg-pink-500/30 transition-all';
        button.textContent = i + 1;
        button.addEventListener('click', () => handleGridClick(i));
        grid.appendChild(button);
    }
}

function updateGameType() {
    const type = document.getElementById('gameType').value;
    retroRewardsGame.gameType = type;
    
    const typeData = GAME_TYPES.find(t => t.name === type);
    document.getElementById('typeStatus').textContent = typeData.description;
    
    updateRetroPayout();
}

function updateComplexity() {
    const complexity = document.getElementById('complexity').value;
    retroRewardsGame.complexity = complexity;
    
    const complexityData = COMPLEXITY_LEVELS.find(c => c.name === complexity);
    document.getElementById('complexityStatus').textContent = complexityData.description;
    
    updateRetroPayout();
}

function updateReactionSpeed() {
    const speed = document.getElementById('reactionSpeed').value;
    retroRewardsGame.reactionSpeed = speed;
    
    const speedData = REACTION_SPEEDS.find(s => s.name === speed);
    document.getElementById('speedStatus').textContent = speedData.description;
    
    updateRetroPayout();
}

function updatePatternLength() {
    const length = document.getElementById('patternLength').value;
    retroRewardsGame.patternLength = length;
    
    updateRetroPayout();
}

function updateDistractions() {
    const distractions = document.getElementById('distractions').value;
    retroRewardsGame.distractions = distractions;
    
    updateRetroPayout();
}

function updateRetroPayout() {
    const betAmount = parseInt(document.getElementById('retroBet').value) || 15;
    
    // Calculate extremely low success probability (1-6%)
    const typeData = GAME_TYPES.find(t => t.name === retroRewardsGame.gameType);
    const complexityData = COMPLEXITY_LEVELS.find(c => c.name === retroRewardsGame.complexity);
    const speedData = REACTION_SPEEDS.find(s => s.name === retroRewardsGame.reactionSpeed);
    const lengthData = PATTERN_LENGTHS.find(l => l.name === retroRewardsGame.patternLength);
    const distractionData = DISTRACTIONS.find(d => d.name === retroRewardsGame.distractions);
    
    // Base success chance is extremely low
    const baseSuccessChance = 0.06; // 6% base
    const finalSuccessChance = Math.max(0.01, 
        baseSuccessChance / typeData.difficulty * complexityData.timeWindow * 
        speedData.window / lengthData.length * distractionData.effect);
    
    const successPercent = Math.max(1, Math.floor(finalSuccessChance * 100));
    const multiplier = Math.floor(100 / finalSuccessChance * 0.15); // Very low multiplier
    const potentialPayout = Math.floor(betAmount * multiplier);
    
    document.getElementById('retroWinChance').textContent = successPercent + '%';
    document.getElementById('retroPotentialPayout').textContent = potentialPayout + ' GA';
}

function startRetroGame() {
    const betAmount = parseInt(document.getElementById('retroBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient retro credits!');
        return;
    }
    
    if (retroRewardsGame.isPlaying) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    retroRewardsGame.betAmount = betAmount;
    retroRewardsGame.isPlaying = true;
    retroRewardsGame.totalGames++;
    retroRewardsGame.currentPattern = [];
    retroRewardsGame.playerPattern = [];
    retroRewardsGame.currentStep = 0;
    retroRewardsGame.gamePhase = 'showing';
    
    // Disable start button
    document.getElementById('startRetroGame').disabled = true;
    
    document.getElementById('retroResult').textContent = '';
    document.getElementById('retroStatus').textContent = 'Retro challenge starting...';
    document.getElementById('retroGameStatus').textContent = 'WATCH THE PATTERN...';
    
    // Generate pattern based on settings
    generateRetroPattern();
    
    // Show pattern
    showRetroPattern();
}

function generateRetroPattern() {
    const lengthData = PATTERN_LENGTHS.find(l => l.name === retroRewardsGame.patternLength);
    const complexityData = COMPLEXITY_LEVELS.find(c => c.name === retroRewardsGame.complexity);
    
    retroRewardsGame.currentPattern = [];
    
    // Generate pattern based on game type
    switch (retroRewardsGame.gameType) {
        case 'memory':
            for (let i = 0; i < lengthData.length; i++) {
                retroRewardsGame.currentPattern.push(Math.floor(Math.random() * 16));
            }
            break;
        case 'reaction':
            // Single target for reaction time
            retroRewardsGame.currentPattern = [Math.floor(Math.random() * 16)];
            break;
        case 'sequence':
            // Sequential pattern
            for (let i = 0; i < complexityData.steps; i++) {
                retroRewardsGame.currentPattern.push(Math.floor(Math.random() * 16));
            }
            break;
        case 'rhythm':
            // Rhythmic pattern with timing
            for (let i = 0; i < lengthData.length; i++) {
                retroRewardsGame.currentPattern.push({
                    button: Math.floor(Math.random() * 16),
                    timing: 500 + Math.random() * 1000
                });
            }
            break;
        case 'chaos':
            // Chaotic pattern with multiple elements
            for (let i = 0; i < lengthData.length * 2; i++) {
                retroRewardsGame.currentPattern.push(Math.floor(Math.random() * 16));
            }
            break;
    }
}

function showRetroPattern() {
    const speedData = REACTION_SPEEDS.find(s => s.name === retroRewardsGame.reactionSpeed);
    const distractionData = DISTRACTIONS.find(d => d.name === retroRewardsGame.distractions);
    
    let currentIndex = 0;
    const showInterval = Math.max(200, 800 / speedData.window);
    
    const showTimer = setInterval(() => {
        // Clear previous highlights
        document.querySelectorAll('#retroGameGrid button').forEach(btn => {
            btn.classList.remove('bg-pink-500', 'bg-red-500', 'bg-blue-500');
        });
        
        if (currentIndex >= retroRewardsGame.currentPattern.length) {
            clearInterval(showTimer);
            startPlayerInput();
            return;
        }
        
        // Highlight current pattern element
        const patternElement = retroRewardsGame.currentPattern[currentIndex];
        const buttonIndex = typeof patternElement === 'object' ? patternElement.button : patternElement;
        
        const button = document.querySelectorAll('#retroGameGrid button')[buttonIndex];
        button.classList.add('bg-pink-500');
        
        // Add distractions
        if (retroRewardsGame.distractions !== 'none') {
            addDistractions();
        }
        
        currentIndex++;
        
        // Update progress
        const progress = (currentIndex / retroRewardsGame.currentPattern.length) * 100;
        document.getElementById('patternProgress').style.width = progress + '%';
        document.getElementById('patternStatus').textContent = `Showing ${currentIndex}/${retroRewardsGame.currentPattern.length}`;
        
    }, showInterval);
}

function addDistractions() {
    const distractionData = DISTRACTIONS.find(d => d.name === retroRewardsGame.distractions);
    
    switch (retroRewardsGame.distractions) {
        case 'visual':
            // Flash random buttons
            for (let i = 0; i < 3; i++) {
                const randomButton = document.querySelectorAll('#retroGameGrid button')[Math.floor(Math.random() * 16)];
                randomButton.classList.add('bg-gray-500');
                setTimeout(() => randomButton.classList.remove('bg-gray-500'), 100);
            }
            break;
        case 'audio':
            // Audio interference (visual representation)
            document.getElementById('retroGameStatus').style.color = '#ff6666';
            setTimeout(() => document.getElementById('retroGameStatus').style.color = '', 200);
            break;
        case 'motion':
            // Motion blur effect
            document.getElementById('retroGameGrid').style.filter = 'blur(2px)';
            setTimeout(() => document.getElementById('retroGameGrid').style.filter = '', 300);
            break;
        case 'chaos':
            // Multiple distractions
            addDistractions.call(this, 'visual');
            addDistractions.call(this, 'motion');
            break;
    }
}

function startPlayerInput() {
    retroRewardsGame.gamePhase = 'input';
    retroRewardsGame.startTime = Date.now();
    
    document.getElementById('retroGameStatus').textContent = 'REPEAT THE PATTERN!';
    document.getElementById('patternStatus').textContent = 'Your turn to input...';
    
    // Set timeout for input
    const speedData = REACTION_SPEEDS.find(s => s.name === retroRewardsGame.reactionSpeed);
    const timeLimit = speedData.window * retroRewardsGame.currentPattern.length * 1000;
    
    retroRewardsGame.reactionTimer = setTimeout(() => {
        endRetroGame(false, 'Time limit exceeded!');
    }, timeLimit);
}

function handleGridClick(buttonIndex) {
    if (retroRewardsGame.gamePhase !== 'input') return;
    
    retroRewardsGame.playerPattern.push(buttonIndex);
    
    // Highlight clicked button
    const button = document.querySelectorAll('#retroGameGrid button')[buttonIndex];
    button.classList.add('bg-blue-500');
    setTimeout(() => button.classList.remove('bg-blue-500'), 200);
    
    // Check if pattern matches so far
    const currentStep = retroRewardsGame.playerPattern.length - 1;
    const expectedElement = retroRewardsGame.currentPattern[currentStep];
    const expectedButton = typeof expectedElement === 'object' ? expectedElement.button : expectedElement;
    
    if (buttonIndex !== expectedButton) {
        // Wrong input
        endRetroGame(false, 'Wrong pattern!');
        return;
    }
    
    // Update progress
    const progress = (retroRewardsGame.playerPattern.length / retroRewardsGame.currentPattern.length) * 100;
    document.getElementById('patternProgress').style.width = progress + '%';
    document.getElementById('currentStep').textContent = retroRewardsGame.playerPattern.length;
    
    // Check if pattern is complete
    if (retroRewardsGame.playerPattern.length >= retroRewardsGame.currentPattern.length) {
        // Pattern completed successfully
        const reactionTime = (Date.now() - retroRewardsGame.startTime) / 1000;
        document.getElementById('reactionTime').textContent = reactionTime.toFixed(2) + 's';
        
        // Check if reaction time is good enough
        const speedData = REACTION_SPEEDS.find(s => s.name === retroRewardsGame.reactionSpeed);
        const maxAllowedTime = speedData.window * retroRewardsGame.currentPattern.length;
        
        if (reactionTime <= maxAllowedTime) {
            endRetroGame(true, `Perfect! Completed in ${reactionTime.toFixed(2)}s`);
        } else {
            endRetroGame(false, `Too slow! ${reactionTime.toFixed(2)}s (max: ${maxAllowedTime.toFixed(1)}s)`);
        }
    }
}

function endRetroGame(won, message) {
    retroRewardsGame.isPlaying = false;
    retroRewardsGame.gamePhase = 'finished';
    
    if (retroRewardsGame.reactionTimer) {
        clearTimeout(retroRewardsGame.reactionTimer);
    }
    
    if (won) {
        // Extremely strict winning conditions - perfect execution required
        const reactionTime = (Date.now() - retroRewardsGame.startTime) / 1000;
        const speedData = REACTION_SPEEDS.find(s => s.name === retroRewardsGame.reactionSpeed);
        const perfectTime = speedData.window * retroRewardsGame.currentPattern.length * 0.7; // Must be 70% faster than limit
        
        if (reactionTime <= perfectTime) {
            // Calculate winnings
            const typeData = GAME_TYPES.find(t => t.name === retroRewardsGame.gameType);
            const baseMultiplier = Math.floor(10 / typeData.difficulty);
            const speedBonus = Math.floor((perfectTime - reactionTime) * 5);
            const totalMultiplier = Math.max(1, baseMultiplier + speedBonus);
            
            const winnings = Math.floor(retroRewardsGame.betAmount * totalMultiplier * 0.08); // Very low multiplier
            balance += winnings;
            updateBalance();
            
            retroRewardsGame.consecutiveWins++;
            retroRewardsGame.perfectRounds++;
            retroRewardsGame.retroLevel++;
            
            document.getElementById('retroResult').innerHTML = 
                `<span class="text-green-400 neon-glow">🕹️ RETRO MASTER! 🕹️</span>`;
            document.getElementById('retroStatus').innerHTML = 
                `${message}! Perfect execution! Won ${winnings} GA!`;
        } else {
            document.getElementById('retroResult').innerHTML = 
                `<span class="text-yellow-400">⚡ GOOD BUT NOT PERFECT! ⚡</span>`;
            document.getElementById('retroStatus').textContent = 
                `${message} - Need perfect timing to win! (${reactionTime.toFixed(2)}s vs ${perfectTime.toFixed(2)}s required)`;
        }
    } else {
        retroRewardsGame.consecutiveWins = 0;
        
        document.getElementById('retroResult').innerHTML = 
            `<span class="text-red-400">💥 RETRO FAIL! 💥</span>`;
        document.getElementById('retroStatus').textContent = message;
    }
    
    // Update max streak
    retroRewardsGame.maxStreak = Math.max(retroRewardsGame.maxStreak, retroRewardsGame.consecutiveWins);
    
    updateRetroDisplay();
    resetRetroControls();
}

function resetRetroControls() {
    setTimeout(() => {
        document.getElementById('startRetroGame').disabled = false;
        document.getElementById('retroGameStatus').textContent = 'Ready to play...';
        document.getElementById('patternProgress').style.width = '0%';
        document.getElementById('patternStatus').textContent = 'Waiting for pattern...';
        document.getElementById('accuracyMeter').style.width = '100%';
        document.getElementById('accuracyLevel').textContent = 'Perfect Accuracy';
        document.getElementById('currentRound').textContent = '0';
        document.getElementById('currentStep').textContent = '0';
        document.getElementById('reactionTime').textContent = '0.0s';
        
        // Clear button highlights
        document.querySelectorAll('#retroGameGrid button').forEach(btn => {
            btn.classList.remove('bg-pink-500', 'bg-red-500', 'bg-blue-500', 'bg-gray-500');
        });
    }, 3000);
}

function updateRetroDisplay() {
    document.getElementById('retroLevelDisplay').textContent = retroRewardsGame.retroLevel;
    document.getElementById('consecutiveWins').textContent = retroRewardsGame.consecutiveWins;
    document.getElementById('totalGames').textContent = retroRewardsGame.totalGames;
    document.getElementById('perfectRounds').textContent = retroRewardsGame.perfectRounds;
    document.getElementById('maxStreak').textContent = retroRewardsGame.maxStreak;
    
    // Update retro energy
    retroRewardsGame.retroEnergy = Math.min(100, (retroRewardsGame.retroLevel / 25) * 100);
    document.getElementById('retroEnergyDisplay').textContent = Math.floor(retroRewardsGame.retroEnergy) + '%';
    
    // Update retro power bar
    document.getElementById('retroPowerBar').style.width = retroRewardsGame.retroEnergy + '%';
    
    let powerStatus = 'Retro Power Charging...';
    if (retroRewardsGame.retroEnergy >= 100) powerStatus = 'MAXIMUM RETRO POWER!';
    else if (retroRewardsGame.retroEnergy >= 75) powerStatus = 'High Retro Energy';
    else if (retroRewardsGame.retroEnergy >= 50) powerStatus = 'Retro Power Building';
    else if (retroRewardsGame.retroEnergy >= 25) powerStatus = 'Retro Energy Rising';
    
    document.getElementById('retroPowerText').textContent = powerStatus;
    
    // Update accuracy based on performance
    const accuracy = retroRewardsGame.totalGames > 0 ? 
        (retroRewardsGame.perfectRounds / retroRewardsGame.totalGames) * 100 : 100;
    document.getElementById('accuracyMeter').style.width = accuracy + '%';
    document.getElementById('accuracyLevel').textContent = 
        accuracy >= 90 ? 'Perfect Accuracy' :
        accuracy >= 70 ? 'High Accuracy' :
        accuracy >= 50 ? 'Good Accuracy' :
        accuracy >= 30 ? 'Fair Accuracy' : 'Poor Accuracy';
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});
