// Game state
let balance = 1000;

// Golf Swing game state
let golfGame = {
    isPlaying: false,
    gamePhase: 'waiting', // waiting, aiming, swinging, finished
    currentHole: 0,
    totalHoles: 0,
    betAmount: 0,
    difficulty: 'amateur',
    courseType: 'parkland',
    windSpeed: 0,
    windDirection: 0,
    powerLevel: 0,
    accuracy: 50,
    isCharging: false,
    timeRemaining: 0,
    gameTimer: null,
    perfectShots: 0,
    eagleCount: 0,
    birdieCount: 0,
    parCount: 0,
    totalStrokes: 0,
    courseRecord: 0,
    proLevel: 1,
    swingMastery: 0,
    ballPosition: { x: 50, y: 90 }, // percentage from left/top
    holePosition: { x: 50, y: 10 },
    currentDistance: 0,
    holeDistance: 150, // yards
    clubSelection: 'driver',
    greenCondition: 'normal',
    slope: 0,
    canvas: null,
    ctx: null,
    animationId: null
};

// Difficulty levels with low win rates
const DIFFICULTY_LEVELS = [
    { 
        name: 'amateur', 
        holes: 3, 
        timeLimit: 180, 
        windMax: 5, 
        winRate: 0.08,
        multiplier: 1.0,
        description: 'Beginner friendly course'
    },
    { 
        name: 'pro', 
        holes: 6, 
        timeLimit: 300, 
        windMax: 10, 
        winRate: 0.06,
        multiplier: 1.5,
        description: 'Professional challenge'
    },
    { 
        name: 'championship', 
        holes: 9, 
        timeLimit: 450, 
        windMax: 15, 
        winRate: 0.04,
        multiplier: 2.0,
        description: 'Championship level'
    },
    { 
        name: 'masters', 
        holes: 12, 
        timeLimit: 600, 
        windMax: 20, 
        winRate: 0.03,
        multiplier: 3.0,
        description: 'Masters tournament'
    },
    { 
        name: 'legendary', 
        holes: 18, 
        timeLimit: 900, 
        windMax: 25, 
        winRate: 0.02,
        multiplier: 5.0,
        description: 'Legendary golf course'
    }
];

// Course types
const COURSE_TYPES = [
    { 
        name: 'parkland', 
        description: 'Traditional parkland course',
        windFactor: 1.0,
        difficultyBonus: 0
    },
    { 
        name: 'links', 
        description: 'Coastal links course',
        windFactor: 1.5,
        difficultyBonus: 0.1
    },
    { 
        name: 'desert', 
        description: 'Desert championship course',
        windFactor: 1.3,
        difficultyBonus: 0.15
    },
    { 
        name: 'mountain', 
        description: 'High altitude mountain course',
        windFactor: 1.8,
        difficultyBonus: 0.2
    }
];

// Golf clubs
const GOLF_CLUBS = [
    { name: 'driver', distance: 250, accuracy: 0.6, description: 'Maximum distance' },
    { name: 'iron', distance: 150, accuracy: 0.8, description: 'Balanced shot' },
    { name: 'wedge', distance: 80, accuracy: 0.9, description: 'High accuracy' },
    { name: 'putter', distance: 30, accuracy: 0.95, description: 'Putting green' }
];

function loadGolfSwingGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Golf Course -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">⛳ GOLF COURSE ⛳</h4>
                    
                    <!-- Course Canvas -->
                    <div class="relative">
                        <canvas id="golfCanvas" width="500" height="300" 
                                class="w-full border border-green-500/30 rounded-lg bg-gradient-to-b from-sky-200 to-green-200"></canvas>
                        
                        <!-- Wind Indicator -->
                        <div id="windIndicator" class="absolute top-2 left-2 bg-black/50 p-2 rounded border border-blue-500/50">
                            <div class="text-xs text-blue-400">WIND</div>
                            <div id="windDisplay" class="text-sm font-bold text-blue-300">0 mph ➡️</div>
                        </div>
                        
                        <!-- Distance Display -->
                        <div class="absolute top-2 right-2 bg-black/50 p-2 rounded border border-yellow-500/50">
                            <div class="text-xs text-yellow-400">DISTANCE</div>
                            <div id="distanceDisplay" class="text-sm font-bold text-yellow-300">150 yards</div>
                        </div>
                        
                        <!-- Ball Trail -->
                        <div id="ballTrail" class="absolute pointer-events-none"></div>
                    </div>
                    
                    <!-- Swing Controls -->
                    <div id="swingControls" class="mt-4 space-y-4 hidden">
                        <!-- Power Meter -->
                        <div>
                            <label class="block text-sm text-gray-300 mb-2">SWING POWER</label>
                            <div class="relative bg-gray-700 rounded-full h-6">
                                <div id="powerMeter" class="h-full bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 rounded-full transition-all duration-100" style="width: 0%"></div>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white" id="powerLevel">0%</div>
                            </div>
                        </div>
                        
                        <!-- Accuracy Meter -->
                        <div>
                            <label class="block text-sm text-gray-300 mb-2">SWING ACCURACY</label>
                            <div class="relative bg-gray-700 rounded-full h-6">
                                <div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-green-400 opacity-50"></div>
                                <div id="accuracyMeter" class="h-full bg-blue-500 rounded-full transition-all duration-100" style="width: 50%"></div>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white" id="accuracyLevel">50%</div>
                            </div>
                        </div>
                        
                        <!-- Club Selection -->
                        <div class="flex justify-center space-x-2">
                            <select id="clubSelect" class="bg-black/50 border border-green-500/30 rounded px-3 py-2 text-white">
                                <option value="driver">Driver (250y)</option>
                                <option value="iron">Iron (150y)</option>
                                <option value="wedge">Wedge (80y)</option>
                                <option value="putter">Putter (30y)</option>
                            </select>
                        </div>
                        
                        <div class="text-center">
                            <button id="swingClub" class="cyber-button px-8 py-3 rounded-lg font-bold">
                                ⛳ SWING!
                            </button>
                            <div class="mt-2 text-sm text-gray-400">
                                Hold SPACE to charge power, release to swing
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <div id="golfStatus" class="text-lg font-semibold">Ready to tee off!</div>
                        <div id="golfResult" class="text-xl font-bold mt-2"></div>
                        <div id="holeInfo" class="text-sm text-gray-400 mt-2"></div>
                    </div>
                </div>
            </div>
            
            <!-- Game Controls -->
            <div class="space-y-6">
                <!-- Game Setup -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30">
                    <h3 class="text-xl font-bold text-green-400 mb-4">Tournament Entry</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Entry Fee</label>
                            <select id="golfBet" class="w-full bg-black/50 border border-green-500/30 rounded px-3 py-2 text-white">
                                <option value="50">50 GA - Amateur</option>
                                <option value="100">100 GA - Pro</option>
                                <option value="200">200 GA - Championship</option>
                                <option value="500">500 GA - Masters</option>
                                <option value="1000">1000 GA - Legendary</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Difficulty</label>
                            <select id="golfDifficulty" class="w-full bg-black/50 border border-green-500/30 rounded px-3 py-2 text-white">
                                <option value="amateur">Amateur (8% win rate)</option>
                                <option value="pro">Pro (6% win rate)</option>
                                <option value="championship">Championship (4% win rate)</option>
                                <option value="masters">Masters (3% win rate)</option>
                                <option value="legendary">Legendary (2% win rate)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-1">Course Type</label>
                            <select id="courseType" class="w-full bg-black/50 border border-green-500/30 rounded px-3 py-2 text-white">
                                <option value="parkland">Parkland Course</option>
                                <option value="links">Links Course</option>
                                <option value="desert">Desert Course</option>
                                <option value="mountain">Mountain Course</option>
                            </select>
                        </div>
                        
                        <button id="startGolf" class="w-full cyber-button py-3 rounded-lg font-bold">
                            START TOURNAMENT
                        </button>
                    </div>
                </div>
                
                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30">
                    <h3 class="text-xl font-bold text-green-400 mb-4">Golf Stats</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-400" id="eagleCount">0</div>
                            <div class="text-gray-300">Eagles</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-400" id="birdieCount">0</div>
                            <div class="text-gray-300">Birdies</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-400" id="perfectShots">0</div>
                            <div class="text-gray-300">Perfect Shots</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-400" id="proLevel">1</div>
                            <div class="text-gray-300">Pro Level</div>
                        </div>
                    </div>
                </div>
                
                <!-- Current Round -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30">
                    <h3 class="text-xl font-bold text-green-400 mb-4">Current Round</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Hole:</span>
                            <span id="currentHole" class="text-green-400">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Par:</span>
                            <span id="currentPar" class="text-yellow-400">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Strokes:</span>
                            <span id="currentStrokes" class="text-blue-400">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Time Left:</span>
                            <span id="timeRemaining" class="text-red-400">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Total Score:</span>
                            <span id="totalScore" class="text-purple-400">Even</span>
                        </div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30">
                    <h3 class="text-xl font-bold text-green-400 mb-4">How to Play</h3>
                    <div class="text-sm text-gray-300 space-y-2">
                        <p>• Hold SPACE to charge swing power</p>
                        <p>• Release at perfect timing for accuracy</p>
                        <p>• Choose the right club for distance</p>
                        <p>• Account for wind and course conditions</p>
                        <p>• Complete under par to win prizes</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupGolfGame();
}

function setupGolfGame() {
    golfGame.canvas = document.getElementById('golfCanvas');
    golfGame.ctx = golfGame.canvas.getContext('2d');
    
    document.getElementById('startGolf').addEventListener('click', startGolfTournament);
    document.getElementById('swingClub').addEventListener('click', swingGolfClub);
    document.getElementById('clubSelect').addEventListener('change', updateClubSelection);
    
    // Keyboard controls
    document.addEventListener('keydown', handleGolfKeyDown);
    document.addEventListener('keyup', handleGolfKeyUp);
    
    updateGolfDisplay();
    drawGolfCourse();
}

function startGolfTournament() {
    const betAmount = parseInt(document.getElementById('golfBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance for tournament entry!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    golfGame.isPlaying = true;
    golfGame.gamePhase = 'aiming';
    golfGame.betAmount = betAmount;
    golfGame.difficulty = document.getElementById('golfDifficulty').value;
    golfGame.courseType = document.getElementById('courseType').value;
    golfGame.currentHole = 0;
    golfGame.totalStrokes = 0;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === golfGame.difficulty);
    golfGame.totalHoles = diffData.holes;
    golfGame.timeRemaining = diffData.timeLimit;
    
    document.getElementById('startGolf').disabled = true;
    document.getElementById('swingControls').classList.remove('hidden');
    
    // Start game timer
    golfGame.gameTimer = setInterval(() => {
        golfGame.timeRemaining--;
        if (golfGame.timeRemaining <= 0) {
            endGolfTournament(false, 'Time\'s up!');
        }
        updateGolfDisplay();
    }, 1000);
    
    nextHole();
}

function nextHole() {
    if (golfGame.currentHole >= golfGame.totalHoles) {
        endGolfTournament(true, 'Tournament completed!');
        return;
    }
    
    golfGame.currentHole++;
    golfGame.gamePhase = 'aiming';
    golfGame.powerLevel = 0;
    golfGame.accuracy = 50;
    golfGame.currentStrokes = 0;
    
    // Generate hole parameters
    const holeData = generateHoleData();
    golfGame.holeDistance = holeData.distance;
    golfGame.holePar = holeData.par;
    golfGame.slope = holeData.slope;
    golfGame.greenCondition = holeData.condition;
    
    // Reset ball position
    golfGame.ballPosition = { x: 50, y: 90 };
    golfGame.holePosition = { x: 50 + (Math.random() - 0.5) * 30, y: 10 };
    
    // Generate environmental conditions
    generateEnvironmentalConditions();
    
    document.getElementById('golfStatus').textContent = 
        `Hole ${golfGame.currentHole}/${golfGame.totalHoles} - Par ${golfGame.holePar}`;
    
    updateGolfDisplay();
    drawGolfCourse();
}

function generateHoleData() {
    const holeTypes = [
        { distance: 150, par: 3, slope: 0, condition: 'normal' },
        { distance: 200, par: 3, slope: 5, condition: 'fast' },
        { distance: 350, par: 4, slope: -3, condition: 'slow' },
        { distance: 450, par: 4, slope: 8, condition: 'normal' },
        { distance: 520, par: 5, slope: -5, condition: 'fast' }
    ];
    
    return holeTypes[Math.floor(Math.random() * holeTypes.length)];
}

function generateEnvironmentalConditions() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === golfGame.difficulty);
    const courseData = COURSE_TYPES.find(c => c.name === golfGame.courseType);
    
    // Generate wind
    golfGame.windSpeed = Math.random() * diffData.windMax * courseData.windFactor;
    golfGame.windDirection = Math.random() * 360;
    
    updateWindDisplay();
}

function updateWindDisplay() {
    const windArrows = ['⬆️', '↗️', '➡️', '↘️', '⬇️', '↙️', '⬅️', '↖️'];
    const arrowIndex = Math.floor(golfGame.windDirection / 45) % 8;
    
    document.getElementById('windDisplay').textContent = 
        `${Math.round(golfGame.windSpeed)} mph ${windArrows[arrowIndex]}`;
}

function handleGolfKeyDown(e) {
    if (!golfGame.isPlaying || golfGame.gamePhase !== 'aiming') return;
    
    if (e.code === 'Space' && !golfGame.isCharging) {
        e.preventDefault();
        golfGame.isCharging = true;
        chargePower();
    }
}

function handleGolfKeyUp(e) {
    if (!golfGame.isPlaying || golfGame.gamePhase !== 'aiming') return;
    
    if (e.code === 'Space' && golfGame.isCharging) {
        e.preventDefault();
        golfGame.isCharging = false;
        swingGolfClub();
    }
}

function chargePower() {
    if (!golfGame.isCharging) return;
    
    // Oscillating power and accuracy meters
    const time = Date.now() / 150;
    golfGame.powerLevel = 50 + Math.sin(time) * 45;
    golfGame.accuracy = 50 + Math.cos(time * 0.8) * 40;
    
    updatePowerDisplay();
    
    if (golfGame.isCharging) {
        requestAnimationFrame(chargePower);
    }
}

function updatePowerDisplay() {
    document.getElementById('powerMeter').style.width = Math.max(0, golfGame.powerLevel) + '%';
    document.getElementById('powerLevel').textContent = Math.round(golfGame.powerLevel) + '%';
    
    document.getElementById('accuracyMeter').style.width = Math.max(0, golfGame.accuracy) + '%';
    document.getElementById('accuracyLevel').textContent = Math.round(golfGame.accuracy) + '%';
}

function updateClubSelection() {
    golfGame.clubSelection = document.getElementById('clubSelect').value;
    const club = GOLF_CLUBS.find(c => c.name === golfGame.clubSelection);
    
    document.getElementById('distanceDisplay').textContent = 
        `${Math.round(golfGame.holeDistance)} yards (Club: ${club.distance}y)`;
}

function swingGolfClub() {
    if (golfGame.gamePhase !== 'aiming') return;
    
    golfGame.gamePhase = 'swinging';
    golfGame.isCharging = false;
    golfGame.currentStrokes++;
    
    // Calculate shot result
    const shotResult = calculateShotResult();
    
    // Animate ball
    animateGolfBall(shotResult);
}

function calculateShotResult() {
    const club = GOLF_CLUBS.find(c => c.name === golfGame.clubSelection);
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === golfGame.difficulty);
    const courseData = COURSE_TYPES.find(c => c.name === golfGame.courseType);
    
    // Power accuracy (optimal around 75-85%)
    const powerAccuracy = golfGame.powerLevel >= 75 && golfGame.powerLevel <= 85 ? 1.0 : 
                         Math.max(0.3, 1 - Math.abs(golfGame.powerLevel - 80) / 80);
    
    // Swing accuracy (optimal around 50%)
    const swingAccuracy = Math.max(0.3, 1 - Math.abs(golfGame.accuracy - 50) / 50);
    
    // Club suitability
    const distanceRatio = golfGame.holeDistance / club.distance;
    const clubSuitability = distanceRatio >= 0.8 && distanceRatio <= 1.2 ? 1.0 : 
                           Math.max(0.4, 1 - Math.abs(distanceRatio - 1) * 0.5);
    
    // Environmental factors
    const windEffect = 1 - (golfGame.windSpeed / 30) * 0.3;
    const slopeEffect = 1 - Math.abs(golfGame.slope) / 100;
    const courseEffect = 1 - courseData.difficultyBonus;
    
    // Combined accuracy
    const totalAccuracy = powerAccuracy * swingAccuracy * clubSuitability * 
                         windEffect * slopeEffect * courseEffect * club.accuracy;
    
    // Calculate distance achieved
    const powerFactor = golfGame.powerLevel / 100;
    const achievedDistance = club.distance * powerFactor * totalAccuracy;
    
    // Calculate final position
    const distanceToHole = Math.abs(achievedDistance - golfGame.holeDistance);
    const lateralError = (1 - swingAccuracy) * 50; // yards off-line
    
    // Determine shot quality
    let shotQuality = 'poor';
    let isHoleOut = false;
    
    if (distanceToHole <= 2 && lateralError <= 2) {
        shotQuality = 'hole-in-one';
        isHoleOut = true;
    } else if (distanceToHole <= 5 && lateralError <= 5) {
        shotQuality = 'excellent';
    } else if (distanceToHole <= 15 && lateralError <= 10) {
        shotQuality = 'good';
    } else if (distanceToHole <= 30 && lateralError <= 20) {
        shotQuality = 'fair';
    }
    
    return {
        accuracy: totalAccuracy,
        distance: achievedDistance,
        distanceToHole: distanceToHole,
        lateralError: lateralError,
        quality: shotQuality,
        isHoleOut: isHoleOut,
        isPerfect: totalAccuracy >= 0.9
    };
}

function animateGolfBall(shotResult) {
    const canvas = golfGame.canvas;
    const startX = golfGame.ballPosition.x;
    const startY = golfGame.ballPosition.y;
    const endX = golfGame.holePosition.x + (shotResult.lateralError / 50) * 20;
    const endY = Math.max(10, 90 - (shotResult.distance / golfGame.holeDistance) * 80);
    
    let progress = 0;
    const animationDuration = 2000;
    const startTime = Date.now();
    
    function animate() {
        const elapsed = Date.now() - startTime;
        progress = Math.min(elapsed / animationDuration, 1);
        
        // Parabolic trajectory
        const x = startX + (endX - startX) * progress;
        const y = startY + (endY - startY) * progress - Math.sin(progress * Math.PI) * 30;
        
        // Update ball position
        golfGame.ballPosition = { x: x, y: y };
        
        drawGolfCourse();
        
        if (progress < 1) {
            golfGame.animationId = requestAnimationFrame(animate);
        } else {
            processShotResult(shotResult);
        }
    }
    
    animate();
}

function processShotResult(shotResult) {
    golfGame.gamePhase = 'aiming';
    
    if (shotResult.isHoleOut) {
        // Hole completed
        const scoreVsPar = golfGame.currentStrokes - golfGame.holePar;
        let scoreText = '';
        
        if (golfGame.currentStrokes === 1) {
            scoreText = 'HOLE-IN-ONE!';
            golfGame.perfectShots++;
        } else if (scoreVsPar <= -2) {
            scoreText = 'EAGLE!';
            golfGame.eagleCount++;
        } else if (scoreVsPar === -1) {
            scoreText = 'BIRDIE!';
            golfGame.birdieCount++;
        } else if (scoreVsPar === 0) {
            scoreText = 'PAR';
            golfGame.parCount++;
        } else {
            scoreText = `BOGEY +${scoreVsPar}`;
        }
        
        if (shotResult.isPerfect) {
            golfGame.perfectShots++;
            golfGame.swingMastery += 5;
        } else {
            golfGame.swingMastery += 1;
        }
        
        golfGame.totalStrokes += golfGame.currentStrokes;
        
        document.getElementById('golfResult').innerHTML = 
            `<span class="text-green-400 neon-glow">⛳ ${scoreText} ⛳</span>`;
        
        setTimeout(() => {
            nextHole();
        }, 3000);
        
    } else {
        // Continue hole
        document.getElementById('golfStatus').textContent = 
            `${Math.round(shotResult.distanceToHole)} yards to pin - ${shotResult.quality} shot`;
        
        // Update distance for next shot
        golfGame.holeDistance = shotResult.distanceToHole;
        document.getElementById('distanceDisplay').textContent = 
            `${Math.round(golfGame.holeDistance)} yards`;
    }
    
    updateGolfDisplay();
}

function endGolfTournament(completed, reason) {
    golfGame.isPlaying = false;
    golfGame.gamePhase = 'finished';
    
    if (golfGame.gameTimer) {
        clearInterval(golfGame.gameTimer);
    }
    
    if (golfGame.animationId) {
        cancelAnimationFrame(golfGame.animationId);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === golfGame.difficulty);
    const expectedPar = golfGame.totalHoles * 4; // Average par 4
    const scoreVsPar = golfGame.totalStrokes - expectedPar;
    
    // Determine if player won (under par with very low probability)
    const baseWinChance = diffData.winRate;
    const performanceBonus = Math.max(0, (expectedPar - golfGame.totalStrokes) / expectedPar);
    const finalWinChance = baseWinChance * (1 + performanceBonus);
    
    const won = completed && scoreVsPar < 0 && Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings
        const baseMultiplier = diffData.multiplier;
        const scoreBonus = Math.abs(scoreVsPar) * 0.5;
        const perfectBonus = golfGame.perfectShots * 0.2;
        
        const totalMultiplier = baseMultiplier * (1 + scoreBonus + perfectBonus);
        const winnings = Math.floor(golfGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        // Update stats
        golfGame.proLevel++;
        
        document.getElementById('golfResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🏆 TOURNAMENT CHAMPION! 🏆</span>`;
        document.getElementById('golfStatus').innerHTML = 
            `Score: ${scoreVsPar} under par! Won: ${winnings} GA!`;
    } else {
        document.getElementById('golfResult').innerHTML = 
            `<span class="text-red-400">⛳ ${reason} ⛳</span>`;
        document.getElementById('golfStatus').innerHTML = 
            completed ? `Final Score: ${scoreVsPar >= 0 ? '+' : ''}${scoreVsPar} - Need under par to win!` : reason;
    }
    
    updateGolfDisplay();
    resetGolfControls();
}

function resetGolfControls() {
    setTimeout(() => {
        document.getElementById('startGolf').disabled = false;
        document.getElementById('swingControls').classList.add('hidden');
        document.getElementById('golfResult').textContent = '';
        document.getElementById('golfStatus').textContent = 'Ready to tee off!';
        
        // Reset game state
        golfGame.currentHole = 0;
        golfGame.totalStrokes = 0;
        golfGame.currentStrokes = 0;
        
        updateGolfDisplay();
        drawGolfCourse();
    }, 8000);
}

function drawGolfCourse() {
    const ctx = golfGame.ctx;
    const canvas = golfGame.canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw sky gradient
    const skyGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    skyGradient.addColorStop(0, '#87CEEB');
    skyGradient.addColorStop(0.7, '#98FB98');
    skyGradient.addColorStop(1, '#228B22');
    
    ctx.fillStyle = skyGradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw fairway
    ctx.fillStyle = '#32CD32';
    ctx.fillRect(0, canvas.height * 0.3, canvas.width, canvas.height * 0.6);
    
    // Draw green
    ctx.fillStyle = '#228B22';
    ctx.beginPath();
    ctx.ellipse(canvas.width * golfGame.holePosition.x / 100, 
                canvas.height * golfGame.holePosition.y / 100, 
                40, 30, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // Draw hole
    ctx.fillStyle = '#000';
    ctx.beginPath();
    ctx.arc(canvas.width * golfGame.holePosition.x / 100, 
            canvas.height * golfGame.holePosition.y / 100, 
            4, 0, 2 * Math.PI);
    ctx.fill();
    
    // Draw flag
    ctx.strokeStyle = '#FFD700';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(canvas.width * golfGame.holePosition.x / 100, 
               canvas.height * golfGame.holePosition.y / 100);
    ctx.lineTo(canvas.width * golfGame.holePosition.x / 100, 
               canvas.height * golfGame.holePosition.y / 100 - 20);
    ctx.stroke();
    
    // Draw flag
    ctx.fillStyle = '#FF0000';
    ctx.fillRect(canvas.width * golfGame.holePosition.x / 100, 
                 canvas.height * golfGame.holePosition.y / 100 - 20, 
                 15, 10);
    
    // Draw golf ball
    ctx.fillStyle = '#FFFFFF';
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(canvas.width * golfGame.ballPosition.x / 100, 
            canvas.height * golfGame.ballPosition.y / 100, 
            6, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
    
    // Draw distance line
    if (golfGame.isPlaying) {
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(canvas.width * golfGame.ballPosition.x / 100, 
                   canvas.height * golfGame.ballPosition.y / 100);
        ctx.lineTo(canvas.width * golfGame.holePosition.x / 100, 
                   canvas.height * golfGame.holePosition.y / 100);
        ctx.stroke();
        ctx.setLineDash([]);
    }
}

function updateGolfDisplay() {
    document.getElementById('eagleCount').textContent = golfGame.eagleCount;
    document.getElementById('birdieCount').textContent = golfGame.birdieCount;
    document.getElementById('perfectShots').textContent = golfGame.perfectShots;
    document.getElementById('proLevel').textContent = golfGame.proLevel;
    
    document.getElementById('currentHole').textContent = 
        golfGame.isPlaying ? `${golfGame.currentHole}/${golfGame.totalHoles}` : '-';
    document.getElementById('currentPar').textContent = 
        golfGame.isPlaying ? golfGame.holePar : '-';
    document.getElementById('currentStrokes').textContent = 
        golfGame.isPlaying ? golfGame.currentStrokes : '-';
    
    if (golfGame.isPlaying) {
        const minutes = Math.floor(golfGame.timeRemaining / 60);
        const seconds = golfGame.timeRemaining % 60;
        document.getElementById('timeRemaining').textContent = 
            `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const expectedPar = golfGame.currentHole * 4;
        const currentScore = golfGame.totalStrokes + golfGame.currentStrokes;
        const scoreVsPar = currentScore - expectedPar;
        
        document.getElementById('totalScore').textContent = 
            scoreVsPar === 0 ? 'Even' : 
            scoreVsPar > 0 ? `+${scoreVsPar}` : `${scoreVsPar}`;
    } else {
        document.getElementById('timeRemaining').textContent = '-';
        document.getElementById('totalScore').textContent = 'Even';
    }
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadGolfSwingGame();
});