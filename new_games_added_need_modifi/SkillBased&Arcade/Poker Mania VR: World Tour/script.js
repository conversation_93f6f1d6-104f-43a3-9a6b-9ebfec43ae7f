// Game state
let balance = 1000;

// Poker Mania VR World Tour game state
let pokerWorldTourGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'classic',
    currentLocation: 'vegas',
    
    // World Tour specific
    locationsVisited: 0,
    totalTournaments: 0,
    worldRanking: 1000,
    prestigeLevel: 1,
    globalScore: 0,
    
    // VR Tournament mechanics
    currentTournament: 1,
    maxTournaments: 5,
    playersEliminated: 0,
    finalTableReached: false,
    championshipWon: false,
    
    // Performance tracking
    handsWon: 0,
    perfectHands: 0,
    bluffsAttempted: 0,
    bluffsSuccessful: 0,
    foldsMade: 0,
    correctFolds: 0,
    allInWins: 0,
    straightFlushes: 0,
    royalFlushes: 0,
    headsUpWins: 0,
    
    // VR World Tour tracking
    eyeTracking: 0,
    handTracking: 0,
    voiceCommands: 0,
    gestureAccuracy: 0,
    reactionTime: 0,
    focusLevel: 0,
    culturalAdaptation: 0,
    languageBonus: 0,
    
    // Tournament progression
    opponents: [],
    opponentCount: 15, // Start with 16 players
    currentPosition: 16,
    prizePool: 0,
    
    // Special features
    vrBonus: 1.0,
    locationMultiplier: 1.0,
    tourMultiplier: 1.0,
    timeLeft: 0
};

// Ultra-strict difficulty settings with sub-7% win rates for world tour
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        tournaments: 5,
        opponentSkill: 0.65,
        timeLimit: 900,
        targetWins: 4,
        winRate: 0.07,
        multiplier: 0.8,
        blindIncrease: 1.3,
        description: 'Amateur World Tour (7% win rate)'
    },
    {
        name: 'normal',
        tournaments: 5,
        opponentSkill: 0.78,
        timeLimit: 720,
        targetWins: 4,
        winRate: 0.05,
        multiplier: 1.2,
        blindIncrease: 1.6,
        description: 'Pro World Tour (5% win rate)'
    },
    {
        name: 'hard',
        tournaments: 5,
        opponentSkill: 0.88,
        timeLimit: 600,
        targetWins: 5,
        winRate: 0.03,
        multiplier: 1.6,
        blindIncrease: 2.0,
        description: 'Elite World Tour (3% win rate)'
    },
    {
        name: 'expert',
        tournaments: 5,
        opponentSkill: 0.94,
        timeLimit: 480,
        targetWins: 5,
        winRate: 0.02,
        multiplier: 2.0,
        blindIncrease: 2.5,
        description: 'Champion Tour (2% win rate)'
    },
    {
        name: 'legendary',
        tournaments: 5,
        opponentSkill: 0.98,
        timeLimit: 360,
        targetWins: 5,
        winRate: 0.015,
        multiplier: 2.5,
        blindIncrease: 3.0,
        description: 'Legendary Tour (1.5% win rate)'
    }
];

const WORLD_LOCATIONS = [
    {
        name: 'vegas',
        displayName: 'Las Vegas, USA',
        description: 'High-stakes desert poker',
        scoreMultiplier: 1.0,
        culturalBonus: 1.0,
        specialRequirement: 'aggressive_play'
    },
    {
        name: 'monaco',
        displayName: 'Monte Carlo, Monaco',
        description: 'Elegant European style',
        scoreMultiplier: 1.2,
        culturalBonus: 1.3,
        specialRequirement: 'conservative_play'
    },
    {
        name: 'macau',
        displayName: 'Macau, China',
        description: 'Asian poker mastery',
        scoreMultiplier: 1.4,
        culturalBonus: 1.5,
        specialRequirement: 'perfect_timing'
    },
    {
        name: 'london',
        displayName: 'London, UK',
        description: 'Traditional British poker',
        scoreMultiplier: 1.3,
        culturalBonus: 1.4,
        specialRequirement: 'bluff_mastery'
    },
    {
        name: 'tokyo',
        displayName: 'Tokyo, Japan',
        description: 'Precision Japanese style',
        scoreMultiplier: 1.6,
        culturalBonus: 1.8,
        specialRequirement: 'technical_perfection'
    }
];

const VR_TOUR_MODES = [
    {
        name: 'classic',
        description: 'Standard World Tour',
        scoreMultiplier: 1.0,
        immersionBonus: 1.0,
        gestureRequired: 8
    },
    {
        name: 'immersive',
        description: 'Full VR World Experience',
        scoreMultiplier: 1.4,
        immersionBonus: 1.6,
        gestureRequired: 15
    },
    {
        name: 'championship',
        description: 'World Championship Mode',
        scoreMultiplier: 1.8,
        immersionBonus: 2.2,
        gestureRequired: 25
    }
];

const HAND_RANKINGS = [
    { name: 'High Card', value: 1, multiplier: 0.1 },
    { name: 'Pair', value: 2, multiplier: 0.2 },
    { name: 'Two Pair', value: 3, multiplier: 0.4 },
    { name: 'Three of a Kind', value: 4, multiplier: 0.6 },
    { name: 'Straight', value: 5, multiplier: 0.8 },
    { name: 'Flush', value: 6, multiplier: 1.0 },
    { name: 'Full House', value: 7, multiplier: 1.5 },
    { name: 'Four of a Kind', value: 8, multiplier: 2.0 },
    { name: 'Straight Flush', value: 9, multiplier: 3.0 },
    { name: 'Royal Flush', value: 10, multiplier: 5.0 }
];

function updateWorldTourDifficultySettings() {
    const difficulty = document.getElementById('worldTourDifficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    pokerWorldTourGame.difficulty = difficulty;
    pokerWorldTourGame.maxTournaments = diffData.tournaments;
    
    document.getElementById('targetTournaments').textContent = diffData.targetWins;
    document.getElementById('tourWinChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('worldOpponentSkill').textContent = Math.floor(diffData.opponentSkill * 100) + '%';
    document.getElementById('tourTimeLimit').textContent = Math.floor(diffData.timeLimit / 60) + 'm';
    
    // Update potential payout
    const betAmount = parseInt(document.getElementById('worldTourBet').value) || 200;
    const potentialPayout = Math.floor(betAmount * diffData.multiplier * 15); // Very conservative
    document.getElementById('tourPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function updateWorldTourModeSettings() {
    const mode = document.getElementById('worldTourMode').value;
    const modeData = VR_TOUR_MODES.find(m => m.name === mode);
    
    pokerWorldTourGame.gameMode = mode;
    
    document.getElementById('tourModeDescription').textContent = modeData.description;
    document.getElementById('tourGestureRequired').textContent = modeData.gestureRequired;
    document.getElementById('tourImmersionBonus').textContent = Math.floor(modeData.immersionBonus * 100) + '%';
}

function updateWorldTourLocation() {
    const location = document.getElementById('worldTourLocation').value;
    const locationData = WORLD_LOCATIONS.find(l => l.name === location);
    
    pokerWorldTourGame.currentLocation = location;
    
    document.getElementById('locationDescription').textContent = locationData.description;
    document.getElementById('locationMultiplier').textContent = Math.floor(locationData.scoreMultiplier * 100) + '%';
    document.getElementById('culturalBonus').textContent = Math.floor(locationData.culturalBonus * 100) + '%';
    document.getElementById('specialRequirement').textContent = locationData.specialRequirement.replace('_', ' ');
}

function startWorldTourGame() {
    const betAmount = parseInt(document.getElementById('worldTourBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pokerWorldTourGame.difficulty);
    const modeData = VR_TOUR_MODES.find(m => m.name === pokerWorldTourGame.gameMode);
    const locationData = WORLD_LOCATIONS.find(l => l.name === pokerWorldTourGame.currentLocation);
    
    // Reset game state
    pokerWorldTourGame.isPlaying = true;
    pokerWorldTourGame.gamePhase = 'playing';
    pokerWorldTourGame.betAmount = betAmount;
    pokerWorldTourGame.currentTournament = 1;
    pokerWorldTourGame.prizePool = betAmount * 16; // 16 players total
    pokerWorldTourGame.timeLeft = diffData.timeLimit;
    pokerWorldTourGame.currentPosition = 16;
    
    // Reset performance tracking
    pokerWorldTourGame.handsWon = 0;
    pokerWorldTourGame.perfectHands = 0;
    pokerWorldTourGame.bluffsAttempted = 0;
    pokerWorldTourGame.bluffsSuccessful = 0;
    pokerWorldTourGame.foldsMade = 0;
    pokerWorldTourGame.correctFolds = 0;
    pokerWorldTourGame.allInWins = 0;
    pokerWorldTourGame.straightFlushes = 0;
    pokerWorldTourGame.royalFlushes = 0;
    pokerWorldTourGame.headsUpWins = 0;
    pokerWorldTourGame.playersEliminated = 0;
    
    // Reset VR World Tour tracking
    pokerWorldTourGame.eyeTracking = 0;
    pokerWorldTourGame.handTracking = 0;
    pokerWorldTourGame.voiceCommands = 0;
    pokerWorldTourGame.gestureAccuracy = 0;
    pokerWorldTourGame.reactionTime = 0;
    pokerWorldTourGame.focusLevel = 0;
    pokerWorldTourGame.culturalAdaptation = 0;
    pokerWorldTourGame.languageBonus = 0;
    pokerWorldTourGame.globalScore = 0;
    
    // Tournament flags
    pokerWorldTourGame.finalTableReached = false;
    pokerWorldTourGame.championshipWon = false;
    
    // Initialize tournament
    initializeWorldTournament(diffData, locationData);
    
    hideGameOverlay();
    updateWorldTourDisplay();
    
    // Start game timer
    pokerWorldTourGame.gameTimer = setInterval(() => {
        pokerWorldTourGame.timeLeft--;
        updateWorldTourDisplay();
        if (pokerWorldTourGame.timeLeft <= 0) {
            endWorldTourGame('TIME UP');
        }
    }, 1000);
    
    // Start first tournament
    playTournamentRound();
}

function initializeWorldTournament(diffData, locationData) {
    pokerWorldTourGame.opponents = [];
    for (let i = 0; i < pokerWorldTourGame.opponentCount; i++) {
        pokerWorldTourGame.opponents.push({
            id: i,
            name: `${locationData.displayName} Pro ${i + 1}`,
            skill: diffData.opponentSkill + (Math.random() - 0.5) * 0.05,
            chips: 1000,
            isActive: true,
            aggression: Math.random() * 0.9 + 0.1,
            bluffFreq: Math.random() * 0.4 + 0.1,
            culturalStyle: locationData.specialRequirement
        });
    }
}

function playTournamentRound() {
    // Simulate VR interactions for world tour
    simulateWorldTourVRInteractions();
    
    // Play multiple hands in tournament format
    const handsInRound = Math.floor(Math.random() * 3) + 2; // 2-4 hands per round
    
    for (let i = 0; i < handsInRound; i++) {
        setTimeout(() => {
            playWorldTourHand();
        }, i * 3000);
    }
    
    // Advance tournament after hands
    setTimeout(() => {
        advanceTournament();
    }, handsInRound * 3000 + 2000);
}

function simulateWorldTourVRInteractions() {
    const modeData = VR_TOUR_MODES.find(m => m.name === pokerWorldTourGame.gameMode);
    const locationData = WORLD_LOCATIONS.find(l => l.name === pokerWorldTourGame.currentLocation);
    
    // Enhanced VR tracking for world tour
    pokerWorldTourGame.eyeTracking += Math.random() * 15 + 5;
    
    // Cultural adaptation simulation
    const culturalSuccess = Math.random() * 10;
    pokerWorldTourGame.culturalAdaptation += culturalSuccess;
    
    // Language bonus (simulated communication)
    if (Math.random() < 0.6) {
        pokerWorldTourGame.languageBonus += Math.random() * 5;
    }
    
    // Hand gesture recognition (more complex for world tour)
    const gestureAttempts = Math.floor(Math.random() * 8) + 3;
    const gestureSuccess = Math.floor(Math.random() * gestureAttempts * 0.8);
    pokerWorldTourGame.handTracking += gestureSuccess;
    pokerWorldTourGame.gestureAccuracy = pokerWorldTourGame.handTracking / (pokerWorldTourGame.currentTournament * 5);
    
    // Voice commands with cultural context
    if (Math.random() < 0.8) {
        pokerWorldTourGame.voiceCommands++;
    }
    
    // Reaction time (affected by cultural adaptation)
    const culturalBonus = pokerWorldTourGame.culturalAdaptation / 100;
    pokerWorldTourGame.reactionTime += Math.random() * 2500 + 400 - (culturalBonus * 200);
    
    // Calculate enhanced focus level
    pokerWorldTourGame.focusLevel = Math.min(100, 
        (pokerWorldTourGame.eyeTracking * 1.5 + pokerWorldTourGame.gestureAccuracy * 60 + 
         pokerWorldTourGame.voiceCommands * 6 + pokerWorldTourGame.culturalAdaptation * 2) / pokerWorldTourGame.currentTournament
    );
    
    // Update global score
    pokerWorldTourGame.globalScore += pokerWorldTourGame.focusLevel * locationData.scoreMultiplier;
}

function playWorldTourHand() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pokerWorldTourGame.difficulty);
    const locationData = WORLD_LOCATIONS.find(l => l.name === pokerWorldTourGame.currentLocation);
    
    // Generate hands
    const playerHand = generateHand();
    const opponentHands = pokerWorldTourGame.opponents.filter(o => o.isActive).map(() => generateHand());
    const communityCards = generateCommunityCards();
    
    // Evaluate hands
    const playerStrength = evaluatePokerHand(playerHand, communityCards);
    
    // Simulate complex tournament action
    const handResult = simulateWorldTourAction(playerStrength, locationData);
    
    if (handResult.won) {
        pokerWorldTourGame.handsWon++;
        
        // Check for perfect hand
        if (playerStrength.value >= 7) {
            pokerWorldTourGame.perfectHands++;
        }
        
        // Track special hands
        if (playerStrength.value === 9) pokerWorldTourGame.straightFlushes++;
        if (playerStrength.value === 10) pokerWorldTourGame.royalFlushes++;
        
        // Eliminate opponents occasionally
        if (Math.random() < 0.3 && pokerWorldTourGame.opponents.filter(o => o.isActive).length > 8) {
            const activeOpponents = pokerWorldTourGame.opponents.filter(o => o.isActive);
            const eliminatedOpponent = activeOpponents[Math.floor(Math.random() * activeOpponents.length)];
            eliminatedOpponent.isActive = false;
            pokerWorldTourGame.playersEliminated++;
            pokerWorldTourGame.currentPosition--;
        }
        
        showWorldTourMessage(`WON: ${playerStrength.name}! Position: ${pokerWorldTourGame.currentPosition}`, 2000);
    } else {
        showWorldTourMessage(`LOST: ${playerStrength.name}`, 2000);
    }
    
    // Track performance
    if (handResult.action === 'bluff') {
        pokerWorldTourGame.bluffsAttempted++;
        if (handResult.won) pokerWorldTourGame.bluffsSuccessful++;
    }
    
    if (handResult.action === 'fold') {
        pokerWorldTourGame.foldsMade++;
        if (!handResult.won) pokerWorldTourGame.correctFolds++;
    }
    
    if (handResult.action === 'all-in' && handResult.won) {
        pokerWorldTourGame.allInWins++;
    }
    
    updateWorldTourDisplay();
}

function generateHand() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const hand = [];
    for (let i = 0; i < 2; i++) {
        const suit = suits[Math.floor(Math.random() * suits.length)];
        const rank = ranks[Math.floor(Math.random() * ranks.length)];
        hand.push({ suit, rank, value: ranks.indexOf(rank) + 2 });
    }
    return hand;
}

function generateCommunityCards() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    const cards = [];
    for (let i = 0; i < 5; i++) {
        const suit = suits[Math.floor(Math.random() * suits.length)];
        const rank = ranks[Math.floor(Math.random() * ranks.length)];
        cards.push({ suit, rank, value: ranks.indexOf(rank) + 2 });
    }
    return cards;
}

function evaluatePokerHand(hand, community) {
    // Simplified hand evaluation
    const allCards = [...hand, ...community];
    const values = allCards.map(c => c.value).sort((a, b) => b - a);
    const suits = allCards.map(c => c.suit);
    
    // Check for flush
    const suitCounts = {};
    suits.forEach(suit => suitCounts[suit] = (suitCounts[suit] || 0) + 1);
    const isFlush = Object.values(suitCounts).some(count => count >= 5);
    
    // Check for straight
    const uniqueValues = [...new Set(values)];
    const isStraight = uniqueValues.length >= 5 && 
        uniqueValues.slice(0, 5).every((val, i) => i === 0 || val === uniqueValues[i-1] - 1);
    
    // Simplified ranking
    if (isFlush && isStraight && values[0] === 14) return HAND_RANKINGS[9]; // Royal Flush
    if (isFlush && isStraight) return HAND_RANKINGS[8]; // Straight Flush
    if (isFlush) return HAND_RANKINGS[5]; // Flush
    if (isStraight) return HAND_RANKINGS[4]; // Straight
    
    // Check pairs/trips/quads
    const valueCounts = {};
    values.forEach(val => valueCounts[val] = (valueCounts[val] || 0) + 1);
    const counts = Object.values(valueCounts).sort((a, b) => b - a);
    
    if (counts[0] === 4) return HAND_RANKINGS[7]; // Four of a kind
    if (counts[0] === 3 && counts[1] === 2) return HAND_RANKINGS[6]; // Full house
    if (counts[0] === 3) return HAND_RANKINGS[3]; // Three of a kind
    if (counts[0] === 2 && counts[1] === 2) return HAND_RANKINGS[2]; // Two pair
    if (counts[0] === 2) return HAND_RANKINGS[1]; // Pair
    
    return HAND_RANKINGS[0]; // High card
}

function simulateWorldTourAction(handStrength, locationData) {
    const actions = ['call', 'raise', 'fold', 'bluff', 'all-in'];
    
    // Weight actions based on hand strength, VR performance, and cultural adaptation
    const vrBonus = pokerWorldTourGame.gestureAccuracy * 0.4;
    const culturalBonus = pokerWorldTourGame.culturalAdaptation / 200;
    const strengthBonus = handStrength.value / 10;
    
    let action = 'call';
    let won = false;
    
    if (handStrength.value >= 8 && Math.random() < 0.4 + vrBonus + culturalBonus) {
        action = Math.random() < 0.25 ? 'all-in' : 'raise';
        won = Math.random() < 0.8;
    } else if (handStrength.value <= 3 && Math.random() < 0.5 + vrBonus) {
        action = Math.random() < 0.6 ? 'fold' : 'bluff';
        won = action === 'bluff' ? Math.random() < 0.3 : false;
    } else {
        won = Math.random() < 0.5 + strengthBonus;
    }
    
    return { action, won };
}

function advanceTournament() {
    pokerWorldTourGame.currentTournament++;
    
    // Check for final table
    if (pokerWorldTourGame.currentPosition <= 9 && !pokerWorldTourGame.finalTableReached) {
        pokerWorldTourGame.finalTableReached = true;
        showWorldTourMessage('FINAL TABLE REACHED!', 3000);
    }
    
    // Check for heads-up
    if (pokerWorldTourGame.currentPosition <= 2) {
        pokerWorldTourGame.headsUpWins++;
        showWorldTourMessage('HEADS-UP PLAY!', 3000);
    }
    
    // Check for championship
    if (pokerWorldTourGame.currentPosition === 1) {
        pokerWorldTourGame.championshipWon = true;
        showWorldTourMessage('TOURNAMENT CHAMPION!', 3000);
    }
    
    // Continue or end tournament
    if (pokerWorldTourGame.currentTournament > pokerWorldTourGame.maxTournaments || 
        pokerWorldTourGame.currentPosition > 10) {
        endWorldTourGame('TOURNAMENT COMPLETE');
    } else {
        setTimeout(() => playTournamentRound(), 2000);
    }
}

function endWorldTourGame(reason) {
    pokerWorldTourGame.isPlaying = false;
    pokerWorldTourGame.gamePhase = 'finished';
    
    if (pokerWorldTourGame.gameTimer) {
        clearInterval(pokerWorldTourGame.gameTimer);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pokerWorldTourGame.difficulty);
    const modeData = VR_TOUR_MODES.find(m => m.name === pokerWorldTourGame.gameMode);
    const locationData = WORLD_LOCATIONS.find(l => l.name === pokerWorldTourGame.currentLocation);
    
    // Ultra-strict World Tour winning requirements
    const tournamentRatio = pokerWorldTourGame.currentTournament / pokerWorldTourGame.maxTournaments;
    const baseWinChance = diffData.winRate;
    
    // Extremely demanding world tour requirements
    const minHandsWon = 25;                         // Need 25+ hands won
    const minPerfectHands = 4;                      // Need 4+ perfect hands
    const minBluffSuccess = 0.75;                   // Need 75%+ bluff success
    const minFoldAccuracy = 0.85;                   // Need 85%+ fold accuracy
    const minGestureAccuracy = 0.7;                 // Need 70%+ gesture accuracy
    const minFocusLevel = 85;                       // Need 85+ focus level
    const minVoiceCommands = modeData.gestureRequired; // Need required voice commands
    const maxAvgReactionTime = 1200;                // Need <1.2s avg reaction
    const minAllInWins = 2;                         // Need 2+ all-in wins
    const minSpecialHands = 2;                      // Need 2+ special hands
    const minPlayersEliminated = 8;                 // Need 8+ eliminations
    const minCulturalAdaptation = 60;               // Need 60+ cultural adaptation
    const minLanguageBonus = 25;                    // Need 25+ language bonus
    const minGlobalScore = 500;                     // Need 500+ global score
    const minFinalPosition = 3;                     // Need top 3 finish
    const requireFinalTable = true;                 // Must reach final table
    const requireHeadsUp = true;                    // Must reach heads-up
    
    const bluffSuccessRate = pokerWorldTourGame.bluffsAttempted > 0 ? 
        pokerWorldTourGame.bluffsSuccessful / pokerWorldTourGame.bluffsAttempted : 0;
    const foldAccuracy = pokerWorldTourGame.foldsMade > 0 ? 
        pokerWorldTourGame.correctFolds / pokerWorldTourGame.foldsMade : 0;
    const avgReactionTime = pokerWorldTourGame.reactionTime / Math.max(1, pokerWorldTourGame.currentTournament);
    const specialHands = pokerWorldTourGame.straightFlushes + pokerWorldTourGame.royalFlushes;
    
    const meetsAllRequirements = 
        pokerWorldTourGame.handsWon >= minHandsWon &&
        pokerWorldTourGame.perfectHands >= minPerfectHands &&
        bluffSuccessRate >= minBluffSuccess &&
        foldAccuracy >= minFoldAccuracy &&
        pokerWorldTourGame.gestureAccuracy >= minGestureAccuracy &&
        pokerWorldTourGame.focusLevel >= minFocusLevel &&
        pokerWorldTourGame.voiceCommands >= minVoiceCommands &&
        avgReactionTime <= maxAvgReactionTime &&
        pokerWorldTourGame.allInWins >= minAllInWins &&
        specialHands >= minSpecialHands &&
        pokerWorldTourGame.playersEliminated >= minPlayersEliminated &&
        pokerWorldTourGame.culturalAdaptation >= minCulturalAdaptation &&
        pokerWorldTourGame.languageBonus >= minLanguageBonus &&
        pokerWorldTourGame.globalScore >= minGlobalScore &&
        pokerWorldTourGame.currentPosition <= minFinalPosition &&
        pokerWorldTourGame.finalTableReached === requireFinalTable &&
        pokerWorldTourGame.headsUpWins >= 1 &&
        tournamentRatio >= 0.8; // Need 80%+ tournament completion
    
    // Performance bonuses
    const vrPerformance = (pokerWorldTourGame.gestureAccuracy + pokerWorldTourGame.focusLevel / 100) / 2;
    const culturalPerformance = (pokerWorldTourGame.culturalAdaptation + pokerWorldTourGame.languageBonus) / 200;
    const skillPerformance = (bluffSuccessRate + foldAccuracy) / 2;
    const positionBonus = Math.max(0, (4 - pokerWorldTourGame.currentPosition) / 3);
    
    const performanceBonus = (vrPerformance + culturalPerformance + skillPerformance + positionBonus) / 4;
    const finalWinChance = meetsAllRequirements ? baseWinChance * tournamentRatio * (1 + performanceBonus * 0.6) : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with world tour bonuses
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier * locationData.scoreMultiplier;
        const vrBonus = vrPerformance * 0.6;
        const culturalBonus = culturalPerformance * 0.8;
        const skillBonus = skillPerformance * 0.5;
        const perfectBonus = pokerWorldTourGame.perfectHands * 0.25;
        const specialBonus = specialHands * 0.4;
        const eliminationBonus = pokerWorldTourGame.playersEliminated * 0.1;
        const positionBonusMultiplier = positionBonus * 0.7;
        const championshipBonus = pokerWorldTourGame.championshipWon ? 1.0 : 0;
        
        const totalMultiplier = baseMultiplier * (1 + vrBonus + culturalBonus + skillBonus + 
            perfectBonus + specialBonus + eliminationBonus + positionBonusMultiplier + championshipBonus);
        const winnings = Math.floor(pokerWorldTourGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('worldTourResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🌍 WORLD TOUR CHAMPION! 🌍</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        const requirements = [
            `${minHandsWon}+ Hands Won (${pokerWorldTourGame.handsWon})`,
            `${minPerfectHands}+ Perfect (${pokerWorldTourGame.perfectHands})`,
            `${Math.floor(minBluffSuccess * 100)}%+ Bluff Success (${Math.floor(bluffSuccessRate * 100)}%)`,
            `${Math.floor(minFoldAccuracy * 100)}%+ Fold Accuracy (${Math.floor(foldAccuracy * 100)}%)`,
            `${Math.floor(minGestureAccuracy * 100)}%+ Gestures (${Math.floor(pokerWorldTourGame.gestureAccuracy * 100)}%)`,
            `${minFocusLevel}+ Focus (${Math.floor(pokerWorldTourGame.focusLevel)})`,
            `${minVoiceCommands}+ Voice Cmds (${pokerWorldTourGame.voiceCommands})`,
            `<${maxAvgReactionTime}ms Reaction (${Math.floor(avgReactionTime)}ms)`,
            `${minAllInWins}+ All-In Wins (${pokerWorldTourGame.allInWins})`,
            `${minSpecialHands}+ Special Hands (${specialHands})`,
            `${minPlayersEliminated}+ Eliminations (${pokerWorldTourGame.playersEliminated})`,
            `${minCulturalAdaptation}+ Cultural (${Math.floor(pokerWorldTourGame.culturalAdaptation)})`,
            `${minLanguageBonus}+ Language (${Math.floor(pokerWorldTourGame.languageBonus)})`,
            `${minGlobalScore}+ Global Score (${Math.floor(pokerWorldTourGame.globalScore)})`,
            `Top ${minFinalPosition} Finish (${pokerWorldTourGame.currentPosition})`,
            `Final Table (${pokerWorldTourGame.finalTableReached ? 'YES' : 'NO'})`,
            `Heads-Up (${pokerWorldTourGame.headsUpWins > 0 ? 'YES' : 'NO'})`
        ];
        
        document.getElementById('worldTourResult').innerHTML = 
            `<span class="text-red-400">🃏 ${reason} 🃏</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Need: ${requirements.slice(0, 6).join(', ')}<br>
            ${requirements.slice(6, 12).join(', ')}<br>
            ${requirements.slice(12, 17).join(', ')}</div>`;
    }
    
    updateWorldTourDisplay();
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
    }, 3000);
}

function showWorldTourMessage(text, duration) {
    console.log(text);
}

function updateWorldTourDisplay() {
    document.getElementById('currentTournament').textContent = pokerWorldTourGame.currentTournament;
    document.getElementById('currentPosition').textContent = pokerWorldTourGame.currentPosition;
    document.getElementById('handsWon').textContent = pokerWorldTourGame.handsWon;
    document.getElementById('perfectHands').textContent = pokerWorldTourGame.perfectHands;
    document.getElementById('playersEliminated').textContent = pokerWorldTourGame.playersEliminated;
    document.getElementById('bluffSuccess').textContent = 
        `${pokerWorldTourGame.bluffsSuccessful}/${pokerWorldTourGame.bluffsAttempted}`;
    document.getElementById('foldAccuracy').textContent = 
        `${pokerWorldTourGame.correctFolds}/${pokerWorldTourGame.foldsMade}`;
    document.getElementById('gestureAccuracy').textContent = 
        Math.floor(pokerWorldTourGame.gestureAccuracy * 100) + '%';
    document.getElementById('focusLevel').textContent = Math.floor(pokerWorldTourGame.focusLevel);
    document.getElementById('culturalAdaptation').textContent = Math.floor(pokerWorldTourGame.culturalAdaptation);
    document.getElementById('languageBonus').textContent = Math.floor(pokerWorldTourGame.languageBonus);
    document.getElementById('globalScore').textContent = Math.floor(pokerWorldTourGame.globalScore);
    document.getElementById('finalTableStatus').textContent = pokerWorldTourGame.finalTableReached ? 'REACHED' : 'NOT YET';
    document.getElementById('timeLeft').textContent = 
        Math.floor(pokerWorldTourGame.timeLeft / 60) + ':' + 
        String(pokerWorldTourGame.timeLeft % 60).padStart(2, '0');
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    updateWorldTourDifficultySettings();
    updateWorldTourModeSettings();
    updateWorldTourLocation();
});