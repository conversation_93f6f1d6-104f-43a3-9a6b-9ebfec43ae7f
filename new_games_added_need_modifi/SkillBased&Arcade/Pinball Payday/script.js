// Game state
let balance = 1000;

// Pinball Payday game state
let pinballGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'classic',
    
    // Physics and game state
    ball: { x: 250, y: 450, vx: 0, vy: 0, radius: 8, active: true },
    leftFlipper: { angle: -30, active: false },
    rightFlipper: { angle: 30, active: false },
    plunger: { power: 0, charging: false, released: false },
    
    // Game mechanics
    score: 0,
    ballsLeft: 3,
    currentBall: 1,
    multiplier: 1,
    combo: 0,
    maxCombo: 0,
    
    // Targets and features
    bumpers: [],
    targets: [],
    ramps: [],
    lanes: [],
    bonusTargets: [],
    
    // Special features
    multiball: false,
    extraBalls: [],
    jackpotActive: false,
    jackpotValue: 0,
    bonusMultiplier: 1,
    skillShotActive: true,
    
    // Performance tracking
    totalHits: 0,
    perfectShots: 0,
    rampShots: 0,
    bumperHits: 0,
    targetHits: 0,
    skillShots: 0,
    
    // Physics
    gravity: 0.3,
    friction: 0.98,
    bounceDamping: 0.7,
    
    // Canvas and animation
    canvas: null,
    ctx: null,
    animationId: null,
    
    // Timing
    gameStartTime: 0,
    ballTime: 0,
    lastUpdate: 0,
    
    // Audio simulation
    soundEffects: true,
    
    // Table elements
    tableWidth: 500,
    tableHeight: 600
};

// Ultra-strict difficulty settings with sub-8% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        ballCount: 5,
        targetScore: 50000,
        timeLimit: 300,
        winRate: 0.08,
        multiplier: 0.6,
        bumperPower: 1.2,
        flipperPower: 1.3,
        description: 'Beginner Table (8% win rate)'
    },
    {
        name: 'normal',
        ballCount: 3,
        targetScore: 75000,
        timeLimit: 240,
        winRate: 0.06,
        multiplier: 1.0,
        bumperPower: 1.0,
        flipperPower: 1.0,
        description: 'Standard Table (6% win rate)'
    },
    {
        name: 'hard',
        ballCount: 3,
        targetScore: 100000,
        timeLimit: 180,
        winRate: 0.04,
        multiplier: 1.4,
        bumperPower: 0.8,
        flipperPower: 0.8,
        description: 'Expert Table (4% win rate)'
    },
    {
        name: 'expert',
        ballCount: 2,
        targetScore: 150000,
        timeLimit: 150,
        winRate: 0.03,
        multiplier: 1.8,
        bumperPower: 0.6,
        flipperPower: 0.6,
        description: 'Master Table (3% win rate)'
    },
    {
        name: 'legendary',
        ballCount: 1,
        targetScore: 250000,
        timeLimit: 120,
        winRate: 0.02,
        multiplier: 2.5,
        bumperPower: 0.4,
        flipperPower: 0.4,
        description: 'Legendary Table (2% win rate)'
    }
];

const GAME_MODES = [
    { name: 'classic', description: 'Standard Pinball', scoreMultiplier: 1.0, timeBonus: true },
    { name: 'blitz', description: 'Speed Pinball', scoreMultiplier: 1.3, timeBonus: true },
    { name: 'precision', description: 'Skill Shots Only', scoreMultiplier: 1.6, timeBonus: false },
    { name: 'multiball', description: 'Multiple Balls', scoreMultiplier: 0.9, timeBonus: false }
];

function loadPinballPaydayGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Pinball Table -->
            <div class="lg:col-span-2">
                <div class="bg-black/40 p-6 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h4 class="text-2xl font-bold mb-4 text-center bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                        🎯 PINBALL PAYDAY 🎯
                    </h4>
                    
                    <!-- Pinball Table Canvas -->
                    <div class="relative mx-auto max-w-lg">
                        <canvas id="pinballCanvas" width="500" height="600" 
                                class="w-full border-2 border-purple-500/50 rounded-lg bg-gradient-to-b from-blue-900/30 to-purple-900/30 shadow-2xl"></canvas>
                        
                        <!-- Game Overlay -->
                        <div id="gameOverlay" class="absolute inset-0 flex items-center justify-center bg-black/80 rounded-lg hidden backdrop-blur-sm">
                            <div class="text-center p-6">
                                <div id="overlayTitle" class="text-4xl font-bold text-purple-400 mb-4 animate-pulse"></div>
                                <div id="overlayMessage" class="text-xl text-white mb-6"></div>
                                <button id="overlayButton" class="cyber-button px-8 py-3 rounded-lg font-bold text-lg">
                                    CONTINUE
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Game Stats Grid -->
                    <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="bg-gradient-to-br from-blue-600/30 to-blue-800/30 p-3 rounded-lg border border-blue-500/30">
                            <div class="text-xs text-blue-300 font-semibold">SCORE</div>
                            <div id="currentScore" class="text-xl font-bold text-blue-100">0</div>
                        </div>
                        <div class="bg-gradient-to-br from-green-600/30 to-green-800/30 p-3 rounded-lg border border-green-500/30">
                            <div class="text-xs text-green-300 font-semibold">BALL</div>
                            <div id="ballInfo" class="text-xl font-bold text-green-100">1/3</div>
                        </div>
                        <div class="bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 p-3 rounded-lg border border-yellow-500/30">
                            <div class="text-xs text-yellow-300 font-semibold">MULTIPLIER</div>
                            <div id="currentMultiplier" class="text-xl font-bold text-yellow-100">1x</div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-600/30 to-purple-800/30 p-3 rounded-lg border border-purple-500/30">
                            <div class="text-xs text-purple-300 font-semibold">COMBO</div>
                            <div id="currentCombo" class="text-xl font-bold text-purple-100">0</div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-300 mb-2">
                            <span class="font-semibold">Progress to Target</span>
                            <span id="progressPercent" class="font-bold">0%</span>
                        </div>
                        <div class="bg-gray-800 rounded-full h-4 border border-gray-600">
                            <div id="progressBar" class="bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 h-full rounded-full transition-all duration-500 shadow-lg" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <!-- Controls Info -->
                    <div class="mt-4 text-center text-sm text-gray-300">
                        <p><strong>SPACE:</strong> Launch Ball | <strong>A/LEFT:</strong> Left Flipper | <strong>D/RIGHT:</strong> Right Flipper</p>
                    </div>
                </div>
            </div>
            
            <!-- Game Controls -->
            <div class="space-y-4">
                <!-- Game Setup -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">🎮 Table Setup</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-2 font-semibold">Bet Amount</label>
                            <select id="pinballBet" class="w-full bg-black/60 border border-purple-500/40 rounded-lg px-3 py-2 text-white focus:border-purple-400">
                                <option value="30">30 GA - Casual</option>
                                <option value="60">60 GA - Standard</option>
                                <option value="120">120 GA - High Stakes</option>
                                <option value="300">300 GA - Expert</option>
                                <option value="600">600 GA - Master</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-2 font-semibold">Table Difficulty</label>
                            <select id="pinballDifficulty" class="w-full bg-black/60 border border-purple-500/40 rounded-lg px-3 py-2 text-white focus:border-purple-400">
                                <option value="easy">Easy (8% win rate)</option>
                                <option value="normal">Normal (6% win rate)</option>
                                <option value="hard">Hard (4% win rate)</option>
                                <option value="expert">Expert (3% win rate)</option>
                                <option value="legendary">Legendary (2% win rate)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-2 font-semibold">Game Mode</label>
                            <select id="pinballMode" class="w-full bg-black/60 border border-purple-500/40 rounded-lg px-3 py-2 text-white focus:border-purple-400">
                                <option value="classic">Classic Mode</option>
                                <option value="blitz">Blitz Mode (+30%)</option>
                                <option value="precision">Precision Mode (+60%)</option>
                                <option value="multiball">Multiball Mode</option>
                            </select>
                        </div>
                        
                        <button id="startPinball" class="w-full cyber-button py-3 rounded-lg font-bold text-lg bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-500 hover:to-pink-500">
                            🚀 START GAME
                        </button>
                    </div>
                </div>
                
                <!-- Special Features -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">⭐ Special Features</h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div class="text-center p-2 bg-black/30 rounded border border-yellow-500/20">
                            <div class="text-lg font-bold text-yellow-400" id="jackpotValue">0</div>
                            <div class="text-gray-300">Jackpot</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-green-500/20">
                            <div class="text-lg font-bold text-green-400" id="skillShotStatus">READY</div>
                            <div class="text-gray-300">Skill Shot</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-blue-500/20">
                            <div class="text-lg font-bold text-blue-400" id="multiballStatus">OFF</div>
                            <div class="text-gray-300">Multiball</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-purple-500/20">
                            <div class="text-lg font-bold text-purple-400" id="bonusMultiplier">1x</div>
                            <div class="text-gray-300">Bonus</div>
                        </div>
                    </div>
                </div>
                
                <!-- Performance Stats -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">📊 Performance</h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div class="text-center p-2 bg-black/30 rounded border border-red-500/20">
                            <div class="text-lg font-bold text-red-400" id="bumperHits">0</div>
                            <div class="text-gray-300">Bumpers</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-green-500/20">
                            <div class="text-lg font-bold text-green-400" id="targetHits">0</div>
                            <div class="text-gray-300">Targets</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-blue-500/20">
                            <div class="text-lg font-bold text-blue-400" id="rampShots">0</div>
                            <div class="text-gray-300">Ramps</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-yellow-500/20">
                            <div class="text-lg font-bold text-yellow-400" id="skillShots">0</div>
                            <div class="text-gray-300">Skill Shots</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-pink-500/20">
                            <div class="text-lg font-bold text-pink-400" id="perfectShots">0</div>
                            <div class="text-gray-300">Perfect</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-cyan-500/20">
                            <div class="text-lg font-bold text-cyan-400" id="maxComboStat">0</div>
                            <div class="text-gray-300">Max Combo</div>
                        </div>
                    </div>
                </div>
                
                <!-- Current Game Info -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">🎯 Game Info</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Target Score:</span>
                            <span id="targetScore" class="text-purple-400 font-bold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Balls Left:</span>
                            <span id="ballsLeft" class="text-green-400 font-bold">3</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Time Left:</span>
                            <span id="timeLeft" class="text-yellow-400 font-bold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Win Chance:</span>
                            <span id="winChance" class="text-red-400 font-bold">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- Game Result -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">🏆 Result</h3>
                    <div id="pinballResult" class="text-center text-lg font-semibold text-cyan-400 min-h-12 p-2">
                        Select difficulty and start playing pinball!
                    </div>
                    <div id="winAmount" class="text-center text-2xl font-bold text-green-400 mt-2 min-h-8"></div>
                </div>
                
                <!-- Instructions -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">📖 How to Play</h3>
                    <div class="text-sm text-gray-300 space-y-2">
                        <p>• <strong>Launch:</strong> Hold SPACE to charge plunger</p>
                        <p>• <strong>Flippers:</strong> A/LEFT and D/RIGHT keys</p>
                        <p>• <strong>Skill Shot:</strong> Perfect launch timing</p>
                        <p>• <strong>Combos:</strong> Hit targets in sequence</p>
                        <p>• <strong>Ramps:</strong> Guide ball up ramps for bonuses</p>
                        <p>• <strong>Multiball:</strong> Activate for massive scoring</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupPinballGame();
}

function setupPinballGame() {
    pinballGame.canvas = document.getElementById('pinballCanvas');
    pinballGame.ctx = pinballGame.canvas.getContext('2d');
    
    // Event listeners
    document.getElementById('startPinball').addEventListener('click', startPinballGame);
    document.getElementById('pinballDifficulty').addEventListener('change', updateDifficultySettings);
    document.getElementById('pinballMode').addEventListener('change', updateGameMode);
    document.getElementById('overlayButton').addEventListener('click', hideGameOverlay);
    
    // Keyboard controls
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    updateDifficultySettings();
    updatePinballDisplay();
    initializePinballTable();
    drawPinballTable();
}

function updateDifficultySettings() {
    const difficulty = document.getElementById('pinballDifficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    pinballGame.difficulty = difficulty;
    pinballGame.ballsLeft = diffData.ballCount;
    
    document.getElementById('targetScore').textContent = diffData.targetScore.toLocaleString();
    document.getElementById('winChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('ballsLeft').textContent = diffData.ballCount;
    
    // Update jackpot based on difficulty
    pinballGame.jackpotValue = Math.floor(diffData.targetScore * 0.3);
    document.getElementById('jackpotValue').textContent = pinballGame.jackpotValue.toLocaleString();
}

function updateGameMode() {
    pinballGame.gameMode = document.getElementById('pinballMode').value;
}

function startPinballGame() {
    const betAmount = parseInt(document.getElementById('pinballBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballGame.difficulty);
    
    // Reset all game state
    pinballGame.isPlaying = true;
    pinballGame.gamePhase = 'playing';
    pinballGame.betAmount = betAmount;
    pinballGame.score = 0;
    pinballGame.ballsLeft = diffData.ballCount;
    pinballGame.currentBall = 1;
    pinballGame.multiplier = 1;
    pinballGame.combo = 0;
    pinballGame.maxCombo = 0;
    pinballGame.totalHits = 0;
    pinballGame.perfectShots = 0;
    pinballGame.rampShots = 0;
    pinballGame.bumperHits = 0;
    pinballGame.targetHits = 0;
    pinballGame.skillShots = 0;
    pinballGame.multiball = false;
    pinballGame.extraBalls = [];
    pinballGame.jackpotActive = false;
    pinballGame.bonusMultiplier = 1;
    pinballGame.skillShotActive = true;
    pinballGame.gameStartTime = Date.now();
    pinballGame.ballTime = Date.now();
    
    document.getElementById('startPinball').disabled = true;
    
    // Initialize ball position
    resetBall();
    
    // Start game timer
    pinballGame.gameTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - pinballGame.gameStartTime) / 1000);
        const timeLeft = Math.max(0, diffData.timeLimit - elapsed);
        
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        document.getElementById('timeLeft').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        if (timeLeft <= 0) {
            endPinballGame('⏰ Time\'s up!');
        }
    }, 1000);
    
    // Start physics loop
    pinballGame.lastUpdate = Date.now();
    gameLoop();
    
    updatePinballDisplay();
}

function initializePinballTable() {
    // Initialize bumpers
    pinballGame.bumpers = [
        { x: 150, y: 200, radius: 25, active: true, hits: 0 },
        { x: 250, y: 180, radius: 25, active: true, hits: 0 },
        { x: 350, y: 200, radius: 25, active: true, hits: 0 },
        { x: 200, y: 280, radius: 20, active: true, hits: 0 },
        { x: 300, y: 280, radius: 20, active: true, hits: 0 }
    ];
    
    // Initialize targets
    pinballGame.targets = [
        { x: 100, y: 150, width: 15, height: 40, active: true, value: 1000, hits: 0 },
        { x: 120, y: 150, width: 15, height: 40, active: true, value: 1000, hits: 0 },
        { x: 140, y: 150, width: 15, height: 40, active: true, value: 1000, hits: 0 },
        { x: 360, y: 150, width: 15, height: 40, active: true, value: 1000, hits: 0 },
        { x: 380, y: 150, width: 15, height: 40, active: true, value: 1000, hits: 0 },
        { x: 400, y: 150, width: 15, height: 40, active: true, value: 1000, hits: 0 }
    ];
    
    // Initialize ramps
    pinballGame.ramps = [
        { x1: 50, y1: 300, x2: 150, y2: 100, width: 20, active: true, value: 5000 },
        { x1: 350, y1: 100, x2: 450, y2: 300, width: 20, active: true, value: 5000 }
    ];
    
    // Initialize bonus targets
    pinballGame.bonusTargets = [
        { x: 250, y: 120, radius: 15, active: true, value: 10000, special: 'jackpot' },
        { x: 100, y: 400, radius: 12, active: true, value: 2000, special: 'multiball' },
        { x: 400, y: 400, radius: 12, active: true, value: 2000, special: 'skillshot' }
    ];
}

function resetBall() {
    pinballGame.ball = {
        x: 470,
        y: 550,
        vx: 0,
        vy: 0,
        radius: 8,
        active: true
    };
    
    pinballGame.plunger = {
        power: 0,
        charging: false,
        released: false
    };
    
    pinballGame.skillShotActive = true;
    pinballGame.ballTime = Date.now();
}

function handleKeyDown(event) {
    if (!pinballGame.isPlaying) return;
    
    switch (event.code) {
        case 'Space':
            event.preventDefault();
            if (!pinballGame.plunger.released && pinballGame.ball.x > 450) {
                pinballGame.plunger.charging = true;
            }
            break;
        case 'KeyA':
        case 'ArrowLeft':
            event.preventDefault();
            pinballGame.leftFlipper.active = true;
            break;
        case 'KeyD':
        case 'ArrowRight':
            event.preventDefault();
            pinballGame.rightFlipper.active = true;
            break;
    }
}

function handleKeyUp(event) {
    if (!pinballGame.isPlaying) return;
    
    switch (event.code) {
        case 'Space':
            event.preventDefault();
            if (pinballGame.plunger.charging && !pinballGame.plunger.released) {
                launchBall();
            }
            break;
        case 'KeyA':
        case 'ArrowLeft':
            event.preventDefault();
            pinballGame.leftFlipper.active = false;
            break;
        case 'KeyD':
        case 'ArrowRight':
            event.preventDefault();
            pinballGame.rightFlipper.active = false;
            break;
    }
}

function launchBall() {
    const power = Math.min(pinballGame.plunger.power, 100);
    const launchForce = (power / 100) * 20;
    
    pinballGame.ball.vy = -launchForce;
    pinballGame.ball.vx = (Math.random() - 0.5) * 2;
    
    pinballGame.plunger.released = true;
    pinballGame.plunger.charging = false;
    pinballGame.plunger.power = 0;
    
    // Check for skill shot (perfect launch timing)
    if (power >= 85 && power <= 95 && pinballGame.skillShotActive) {
        pinballGame.skillShots++;
        pinballGame.score += 25000;
        pinballGame.skillShotActive = false;
        
        // Visual feedback
        showMessage('SKILL SHOT! +25,000', 2000);
    }
}

function gameLoop() {
    if (!pinballGame.isPlaying) return;
    
    const now = Date.now();
    const deltaTime = (now - pinballGame.lastUpdate) / 16.67; // 60 FPS normalization
    pinballGame.lastUpdate = now;
    
    updatePhysics(deltaTime);
    checkCollisions();
    updatePinballDisplay();
    drawPinballTable();
    
    pinballGame.animationId = requestAnimationFrame(gameLoop);
}

function updatePhysics(deltaTime) {
    const ball = pinballGame.ball;
    
    if (!ball.active) return;
    
    // Charge plunger
    if (pinballGame.plunger.charging) {
        pinballGame.plunger.power = Math.min(pinballGame.plunger.power + 2 * deltaTime, 100);
    }
    
    // Apply gravity
    ball.vy += pinballGame.gravity * deltaTime;
    
    // Apply friction
    ball.vx *= Math.pow(pinballGame.friction, deltaTime);
    ball.vy *= Math.pow(pinballGame.friction, deltaTime);
    
    // Update position
    ball.x += ball.vx * deltaTime;
    ball.y += ball.vy * deltaTime;
    
    // Table boundaries
    if (ball.x - ball.radius < 0) {
        ball.x = ball.radius;
        ball.vx = Math.abs(ball.vx) * pinballGame.bounceDamping;
    }
    if (ball.x + ball.radius > pinballGame.tableWidth) {
        ball.x = pinballGame.tableWidth - ball.radius;
        ball.vx = -Math.abs(ball.vx) * pinballGame.bounceDamping;
    }
    if (ball.y - ball.radius < 0) {
        ball.y = ball.radius;
        ball.vy = Math.abs(ball.vy) * pinballGame.bounceDamping;
    }
    
    // Ball drain (bottom)
    if (ball.y > pinballGame.tableHeight + 50) {
        drainBall();
    }
    
    // Update flipper angles
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballGame.difficulty);
    const flipperSpeed = 8 * diffData.flipperPower;
    
    if (pinballGame.leftFlipper.active) {
        pinballGame.leftFlipper.angle = Math.min(pinballGame.leftFlipper.angle + flipperSpeed, 30);
    } else {
        pinballGame.leftFlipper.angle = Math.max(pinballGame.leftFlipper.angle - flipperSpeed, -30);
    }
    
    if (pinballGame.rightFlipper.active) {
        pinballGame.rightFlipper.angle = Math.max(pinballGame.rightFlipper.angle - flipperSpeed, -30);
    } else {
        pinballGame.rightFlipper.angle = Math.min(pinballGame.rightFlipper.angle + flipperSpeed, 30);
    }
}

function checkCollisions() {
    const ball = pinballGame.ball;
    if (!ball.active) return;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballGame.difficulty);
    
    // Bumper collisions
    pinballGame.bumpers.forEach(bumper => {
        if (!bumper.active) return;
        
        const dx = ball.x - bumper.x;
        const dy = ball.y - bumper.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < ball.radius + bumper.radius) {
            // Collision response
            const angle = Math.atan2(dy, dx);
            const force = 8 * diffData.bumperPower;
            
            ball.vx = Math.cos(angle) * force;
            ball.vy = Math.sin(angle) * force;
            
            // Move ball out of bumper
            const overlap = ball.radius + bumper.radius - distance;
            ball.x += Math.cos(angle) * overlap;
            ball.y += Math.sin(angle) * overlap;
            
            // Score and effects
            bumper.hits++;
            pinballGame.bumperHits++;
            pinballGame.totalHits++;
            pinballGame.combo++;
            
            const score = 500 * pinballGame.multiplier * pinballGame.bonusMultiplier;
            pinballGame.score += score;
            
            // Visual feedback
            bumper.glow = 10;
        }
    });
    
    // Target collisions
    pinballGame.targets.forEach(target => {
        if (!target.active) return;
        
        if (ball.x + ball.radius > target.x && 
            ball.x - ball.radius < target.x + target.width &&
            ball.y + ball.radius > target.y && 
            ball.y - ball.radius < target.y + target.height) {
            
            target.active = false;
            target.hits++;
            pinballGame.targetHits++;
            pinballGame.totalHits++;
            pinballGame.combo++;
            
            const score = target.value * pinballGame.multiplier * pinballGame.bonusMultiplier;
            pinballGame.score += score;
            
            // Bounce ball
            if (ball.x < target.x || ball.x > target.x + target.width) {
                ball.vx *= -pinballGame.bounceDamping;
            } else {
                ball.vy *= -pinballGame.bounceDamping;
            }
            
            // Check if all targets in group are hit
            checkTargetGroups();
        }
    });
    
    // Bonus target collisions
    pinballGame.bonusTargets.forEach(target => {
        if (!target.active) return;
        
        const dx = ball.x - target.x;
        const dy = ball.y - target.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < ball.radius + target.radius) {
            target.active = false;
            pinballGame.totalHits++;
            pinballGame.combo++;
            
            const score = target.value * pinballGame.multiplier * pinballGame.bonusMultiplier;
            pinballGame.score += score;
            
            // Special effects
            switch (target.special) {
                case 'jackpot':
                    if (pinballGame.jackpotActive) {
                        pinballGame.score += pinballGame.jackpotValue;
                        showMessage(`JACKPOT! +${pinballGame.jackpotValue.toLocaleString()}`, 3000);
                        pinballGame.jackpotActive = false;
                    } else {
                        pinballGame.jackpotActive = true;
                        showMessage('JACKPOT ACTIVATED!', 2000);
                    }
                    break;
                case 'multiball':
                    activateMultiball();
                    break;
                case 'skillshot':
                    pinballGame.skillShotActive = true;
                    pinballGame.multiplier++;
                    showMessage(`MULTIPLIER UP! ${pinballGame.multiplier}x`, 2000);
                    break;
            }
            
            // Bounce ball
            const angle = Math.atan2(dy, dx);
            ball.vx = Math.cos(angle) * 6;
            ball.vy = Math.sin(angle) * 6;
        }
    });
    
    // Flipper collisions
    checkFlipperCollisions();
    
    // Update max combo
    pinballGame.maxCombo = Math.max(pinballGame.maxCombo, pinballGame.combo);
    
    // Combo timeout
    if (pinballGame.combo > 0) {
        setTimeout(() => {
            pinballGame.combo = Math.max(0, pinballGame.combo - 1);
        }, 2000);
    }
}

function checkFlipperCollisions() {
    const ball = pinballGame.ball;
    const leftFlipperX = 150;
    const leftFlipperY = 520;
    const rightFlipperX = 350;
    const rightFlipperY = 520;
    const flipperLength = 60;
    
    // Left flipper
    const leftAngleRad = (pinballGame.leftFlipper.angle * Math.PI) / 180;
    const leftEndX = leftFlipperX + Math.cos(leftAngleRad) * flipperLength;
    const leftEndY = leftFlipperY + Math.sin(leftAngleRad) * flipperLength;
    
    if (checkLineCollision(ball, leftFlipperX, leftFlipperY, leftEndX, leftEndY)) {
        const force = pinballGame.leftFlipper.active ? 12 : 6;
        ball.vx = -Math.abs(ball.vx) - force;
        ball.vy = -Math.abs(ball.vy) - force;
        
        if (pinballGame.leftFlipper.active) {
            pinballGame.perfectShots++;
        }
    }
    
    // Right flipper
    const rightAngleRad = (pinballGame.rightFlipper.angle * Math.PI) / 180;
    const rightEndX = rightFlipperX + Math.cos(Math.PI - rightAngleRad) * flipperLength;
    const rightEndY = rightFlipperY + Math.sin(Math.PI - rightAngleRad) * flipperLength;
    
    if (checkLineCollision(ball, rightFlipperX, rightFlipperY, rightEndX, rightEndY)) {
        const force = pinballGame.rightFlipper.active ? 12 : 6;
        ball.vx = Math.abs(ball.vx) + force;
        ball.vy = -Math.abs(ball.vy) - force;
        
        if (pinballGame.rightFlipper.active) {
            pinballGame.perfectShots++;
        }
    }
}

function checkLineCollision(ball, x1, y1, x2, y2) {
    const A = ball.x - x1;
    const B = ball.y - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return false;
    
    const param = dot / lenSq;
    
    let xx, yy;
    
    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }
    
    const dx = ball.x - xx;
    const dy = ball.y - yy;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    return distance < ball.radius + 5;
}

function checkTargetGroups() {
    // Check if target groups are completed for bonuses
    const leftTargets = pinballGame.targets.slice(0, 3);
    const rightTargets = pinballGame.targets.slice(3, 6);
    
    if (leftTargets.every(t => !t.active)) {
        pinballGame.bonusMultiplier += 0.5;
        pinballGame.score += 15000;
        showMessage('LEFT TARGETS COMPLETE! +15,000', 2000);
        
        // Reset targets
        leftTargets.forEach(t => t.active = true);
    }
    
    if (rightTargets.every(t => !t.active)) {
        pinballGame.bonusMultiplier += 0.5;
        pinballGame.score += 15000;
        showMessage('RIGHT TARGETS COMPLETE! +15,000', 2000);
        
        // Reset targets
        rightTargets.forEach(t => t.active = true);
    }
}

function activateMultiball() {
    if (pinballGame.multiball) return;
    
    pinballGame.multiball = true;
    pinballGame.multiplier += 2;
    
    // Add extra balls
    for (let i = 0; i < 2; i++) {
        pinballGame.extraBalls.push({
            x: 250 + (Math.random() - 0.5) * 100,
            y: 300,
            vx: (Math.random() - 0.5) * 10,
            vy: -Math.random() * 8,
            radius: 8,
            active: true
        });
    }
    
    showMessage('MULTIBALL ACTIVATED!', 3000);
    
    // Deactivate after 30 seconds
    setTimeout(() => {
        pinballGame.multiball = false;
        pinballGame.extraBalls = [];
        showMessage('MULTIBALL ENDED', 2000);
    }, 30000);
}

function drainBall() {
    pinballGame.ballsLeft--;
    pinballGame.currentBall++;
    
    if (pinballGame.ballsLeft <= 0) {
        endPinballGame('🎱 All balls drained!');
    } else {
        // Reset for next ball
        resetBall();
        pinballGame.combo = 0;
        pinballGame.skillShotActive = true;
        
        showMessage(`BALL ${pinballGame.currentBall}`, 2000);
    }
}

function endPinballGame(reason) {
    pinballGame.isPlaying = false;
    pinballGame.gamePhase = 'finished';
    
    if (pinballGame.gameTimer) {
        clearInterval(pinballGame.gameTimer);
    }
    
    if (pinballGame.animationId) {
        cancelAnimationFrame(pinballGame.animationId);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === pinballGame.gameMode);
    
    // Ultra-strict winning requirements
    const scoreRatio = pinballGame.score / diffData.targetScore;
    const baseWinChance = diffData.winRate;
    
    // Extremely demanding requirements
    const minBumperHits = 25;      // Need 25+ bumper hits
    const minTargetHits = 15;      // Need 15+ target hits
    const minRampShots = 3;        // Need 3+ ramp shots
    const minSkillShots = 2;       // Need 2+ skill shots
    const minPerfectShots = 8;     // Need 8+ perfect flipper shots
    const minCombo = 12;           // Need 12+ combo
    const minMultiplier = 3;       // Need 3x+ multiplier
    
    const meetsAllRequirements = 
        pinballGame.bumperHits >= minBumperHits && 
        pinballGame.targetHits >= minTargetHits && 
        pinballGame.rampShots >= minRampShots &&
        pinballGame.skillShots >= minSkillShots &&
        pinballGame.perfectShots >= minPerfectShots &&
        pinballGame.maxCombo >= minCombo &&
        pinballGame.multiplier >= minMultiplier &&
        scoreRatio >= 1.3; // Need 130% of target score
    
    // Performance bonuses
    const ballEfficiency = (diffData.ballCount - pinballGame.currentBall + 1) / diffData.ballCount;
    const timeEfficiency = 1.0; // Time bonus if applicable
    
    const efficiencyBonus = (ballEfficiency + timeEfficiency) / 2;
    const finalWinChance = meetsAllRequirements ? baseWinChance * scoreRatio * (1 + efficiencyBonus) : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with bonuses
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier;
        const comboBonus = (pinballGame.maxCombo - 10) * 0.1;
        const skillBonus = pinballGame.skillShots * 0.2;
        const perfectBonus = pinballGame.perfectShots * 0.05;
        const multiballBonus = pinballGame.multiball ? 0.5 : 0;
        const efficiencyBonusMultiplier = ballEfficiency * 0.3;
        
        const totalMultiplier = baseMultiplier * (1 + comboBonus + skillBonus + perfectBonus + multiballBonus + efficiencyBonusMultiplier);
        const winnings = Math.floor(pinballGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('pinballResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🎯 PINBALL WIZARD! 🎯</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        const requirements = [
            `${minBumperHits}+ Bumpers (${pinballGame.bumperHits})`,
            `${minTargetHits}+ Targets (${pinballGame.targetHits})`,
            `${minRampShots}+ Ramps (${pinballGame.rampShots})`,
            `${minSkillShots}+ Skill (${pinballGame.skillShots})`,
            `${minPerfectShots}+ Perfect (${pinballGame.perfectShots})`,
            `${minCombo}+ Combo (${pinballGame.maxCombo})`,
            `${minMultiplier}x+ Multi (${pinballGame.multiplier}x)`,
            `130%+ Score (${Math.floor(scoreRatio * 100)}%)`
        ];
        
        document.getElementById('pinballResult').innerHTML = 
            `<span class="text-red-400">🎱 ${reason} 🎱</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<div class="text-sm text-yellow-400 mt-2">Need: ${requirements.slice(0, 4).join(', ')}<br>${requirements.slice(4).join(', ')}</div>`;
    }
    
    updatePinballDisplay();
    
    // Reset after delay
    setTimeout(() => {
        document.getElementById('startPinball').disabled = false;
        document.getElementById('pinballResult').textContent = 'Select difficulty and start playing pinball!';
        document.getElementById('winAmount').textContent = '';
        
        // Reset game state
        pinballGame.score = 0;
        pinballGame.ballsLeft = diffData.ballCount;
        pinballGame.currentBall = 1;
        pinballGame.multiplier = 1;
        pinballGame.combo = 0;
        pinballGame.maxCombo = 0;
        pinballGame.totalHits = 0;
        pinballGame.perfectShots = 0;
        pinballGame.rampShots = 0;
        pinballGame.bumperHits = 0;
        pinballGame.targetHits = 0;
        pinballGame.skillShots = 0;
        pinballGame.multiball = false;
        pinballGame.extraBalls = [];
        pinballGame.jackpotActive = false;
        pinballGame.bonusMultiplier = 1;
        
        updatePinballDisplay();
        initializePinballTable();
        resetBall();
        drawPinballTable();
    }, 12000);
}

function drawPinballTable() {
    const ctx = pinballGame.ctx;
    const canvas = pinballGame.canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw background gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#1a1a3e');
    gradient.addColorStop(0.5, '#2a2a5e');
    gradient.addColorStop(1, '#1a1a3e');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw table outline
    ctx.strokeStyle = '#666699';
    ctx.lineWidth = 3;
    ctx.strokeRect(0, 0, canvas.width, canvas.height);
    
    // Draw bumpers
    pinballGame.bumpers.forEach(bumper => {
        if (!bumper.active) return;
        
        const glowIntensity = bumper.glow || 0;
        
        // Glow effect
        if (glowIntensity > 0) {
            ctx.shadowColor = '#ff6600';
            ctx.shadowBlur = glowIntensity;
            bumper.glow--;
        }
        
        ctx.fillStyle = '#ff3366';
        ctx.beginPath();
        ctx.arc(bumper.x, bumper.y, bumper.radius, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.shadowBlur = 0;
        
        // Bumper highlight
        ctx.fillStyle = '#ff99aa';
        ctx.beginPath();
        ctx.arc(bumper.x - 5, bumper.y - 5, bumper.radius * 0.3, 0, 2 * Math.PI);
        ctx.fill();
    });
    
    // Draw targets
    pinballGame.targets.forEach(target => {
        if (!target.active) return;
        
        ctx.fillStyle = '#33ff66';
        ctx.fillRect(target.x, target.y, target.width, target.height);
        
        // Target highlight
        ctx.fillStyle = '#99ffaa';
        ctx.fillRect(target.x + 2, target.y + 2, target.width - 4, 8);
    });
    
    // Draw ramps
    pinballGame.ramps.forEach(ramp => {
        if (!ramp.active) return;
        
        ctx.strokeStyle = '#ffff33';
        ctx.lineWidth = ramp.width;
        ctx.beginPath();
        ctx.moveTo(ramp.x1, ramp.y1);
        ctx.lineTo(ramp.x2, ramp.y2);
        ctx.stroke();
    });
    
    // Draw bonus targets
    pinballGame.bonusTargets.forEach(target => {
        if (!target.active) return;
        
        let color = '#ff33ff';
        if (target.special === 'jackpot') color = '#ffff00';
        if (target.special === 'multiball') color = '#00ffff';
        if (target.special === 'skillshot') color = '#ff9900';
        
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(target.x, target.y, target.radius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Special symbol
        ctx.fillStyle = '#000000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        let symbol = '★';
        if (target.special === 'jackpot') symbol = '$';
        if (target.special === 'multiball') symbol = '●';
        if (target.special === 'skillshot') symbol = '◆';
        
        ctx.fillText(symbol, target.x, target.y);
    });
    
    // Draw flippers
    drawFlipper(ctx, 150, 520, pinballGame.leftFlipper.angle, 60, '#cccccc');
    drawFlipper(ctx, 350, 520, 180 - pinballGame.rightFlipper.angle, 60, '#cccccc');
    
    // Draw plunger
    if (!pinballGame.plunger.released) {
        const plungerY = 550 - (pinballGame.plunger.power / 100) * 30;
        ctx.fillStyle = '#999999';
        ctx.fillRect(465, plungerY, 10, 30);
        
        // Power indicator
        ctx.fillStyle = `hsl(${120 - pinballGame.plunger.power * 1.2}, 70%, 50%)`;
        ctx.fillRect(480, 520, 15, -(pinballGame.plunger.power / 100) * 60);
    }
    
    // Draw main ball
    if (pinballGame.ball.active) {
        drawBall(ctx, pinballGame.ball);
    }
    
    // Draw extra balls (multiball)
    pinballGame.extraBalls.forEach(ball => {
        if (ball.active) {
            drawBall(ctx, ball);
        }
    });
    
    // Draw special effects text
    if (pinballGame.messageText && pinballGame.messageTime > Date.now()) {
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeText(pinballGame.messageText, canvas.width / 2, 100);
        ctx.fillText(pinballGame.messageText, canvas.width / 2, 100);
    }
}

function drawFlipper(ctx, x, y, angle, length, color) {
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate((angle * Math.PI) / 180);
    
    ctx.fillStyle = color;
    ctx.fillRect(0, -8, length, 16);
    
    // Flipper highlight
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(2, -6, length - 4, 4);
    
    ctx.restore();
}

function drawBall(ctx, ball) {
    // Ball shadow
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.beginPath();
    ctx.arc(ball.x + 2, ball.y + 2, ball.radius, 0, 2 * Math.PI);
    ctx.fill();
    
    // Ball gradient
    const ballGradient = ctx.createRadialGradient(
        ball.x - 3, ball.y - 3, 0,
        ball.x, ball.y, ball.radius
    );
    ballGradient.addColorStop(0, '#ffffff');
    ballGradient.addColorStop(0.3, '#cccccc');
    ballGradient.addColorStop(1, '#666666');
    
    ctx.fillStyle = ballGradient;
    ctx.beginPath();
    ctx.arc(ball.x, ball.y, ball.radius, 0, 2 * Math.PI);
    ctx.fill();
}

function showMessage(text, duration) {
    pinballGame.messageText = text;
    pinballGame.messageTime = Date.now() + duration;
}

function updatePinballDisplay() {
    document.getElementById('currentScore').textContent = pinballGame.score.toLocaleString();
    document.getElementById('ballInfo').textContent = `${pinballGame.currentBall}/${pinballGame.ballsLeft + pinballGame.currentBall - 1}`;
    document.getElementById('currentMultiplier').textContent = pinballGame.multiplier + 'x';
    document.getElementById('currentCombo').textContent = pinballGame.combo;
    document.getElementById('ballsLeft').textContent = pinballGame.ballsLeft;
    document.getElementById('bonusMultiplier').textContent = pinballGame.bonusMultiplier.toFixed(1) + 'x';
    
    // Progress bar
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballGame.difficulty);
    const progress = Math.min(100, (pinballGame.score / diffData.targetScore) * 100);
    document.getElementById('progressBar').style.width = progress + '%';
    document.getElementById('progressPercent').textContent = Math.floor(progress) + '%';
    
    // Performance stats
    document.getElementById('bumperHits').textContent = pinballGame.bumperHits;
    document.getElementById('targetHits').textContent = pinballGame.targetHits;
    document.getElementById('rampShots').textContent = pinballGame.rampShots;
    document.getElementById('skillShots').textContent = pinballGame.skillShots;
    document.getElementById('perfectShots').textContent = pinballGame.perfectShots;
    document.getElementById('maxComboStat').textContent = pinballGame.maxCombo;
    
    // Special features
    document.getElementById('skillShotStatus').textContent = pinballGame.skillShotActive ? 'READY' : 'USED';
    document.getElementById('multiballStatus').textContent = pinballGame.multiball ? 'ACTIVE' : 'OFF';
    document.getElementById('jackpotValue').textContent = pinballGame.jackpotActive ? 
        pinballGame.jackpotValue.toLocaleString() : '0';
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPinballPaydayGame();
});