@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;600;700&display=swap');

:root {
    /* GoldenAura Primary Colors */
    --golden-primary: #FFD700;
    --golden-bright: #FFA500;
    --golden-dark: #B8860B;
    --golden-amber: #FFBF00;
    --cyber-gold: #E6AC00;
    --neon-gold: #FFE135;
    --golden-purple: #8B4513;

    /* Cyber Elements (kept for blend) */
    --neon-purple: #9945ff;
    --neon-pink: #ff2d92;
    --neon-blue: #00d4ff;
    --neon-green: #39ff14;
    --cyber-dark: #0a0a0f;
    --cyber-darker: #050508;

    /* New GoldenAura Gradients */
    --golden-gradient: linear-gradient(135deg, #FFD700, #FFA500, #B8860B);
    --cyber-golden-gradient: linear-gradient(135deg, #E6AC00, #9945ff, #FFD700);
}

* {
    scrollbar-width: thin;
    scrollbar-color: var(--golden-primary) var(--cyber-dark);
}

body {
    font-family: 'Rajdhani', sans-serif;
    background: linear-gradient(135deg, #0a0a0f 0%, #1a1000 25%, #2e1a00 50%, #1a0b2e 75%, #0a0a0f 100%);
    background-attachment: fixed;
    background-size: 400% 400%;
    animation: gradientShift 10s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.neon-glow {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
}

.neon-border {
    box-shadow: 0 0 10px currentColor, inset 0 0 10px rgba(255, 255, 255, 0.1);
}

.cyber-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px),
        linear-gradient(rgba(153, 69, 255, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(153, 69, 255, 0.05) 1px, transparent 1px);
    background-size: 20px 20px, 20px 20px, 40px 40px, 40px 40px;
    pointer-events: none;
}

.glitch-effect {
    position: relative;
    color: #fff;
}

.glitch-effect::before,
.glitch-effect::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch-effect::before {
    animation: glitch-1 0.3s infinite;
    color: var(--golden-bright);
    z-index: -1;
}

.glitch-effect::after {
    animation: glitch-2 0.3s infinite;
    color: var(--cyber-gold);
    z-index: -2;
}

@keyframes glitch-1 {
    0%, 14%, 15%, 49%, 50%, 99%, 100% { transform: translate3d(0, 0, 0); }
    1%, 2% { transform: translate3d(-1px, 0, 0); }
    3%, 4% { transform: translate3d(1px, 0, 0); }
}

@keyframes glitch-2 {
    0%, 20%, 21%, 62%, 63%, 99%, 100% { transform: translate3d(0, 0, 0); }
    21%, 22% { transform: translate3d(-1px, 0, 0); }
    23%, 24% { transform: translate3d(1px, 0, 0); }
}

.game-card {
    background: linear-gradient(145deg, rgba(10, 10, 15, 0.8), rgba(46, 26, 0, 0.6));
    border: 1px solid rgba(255, 215, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.1);
}

.game-card:hover {
    transform: translateY(-5px);
    border-color: var(--golden-primary);
    box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3), 0 0 30px rgba(255, 165, 0, 0.2);
}

.cyber-button {
    background: linear-gradient(45deg, var(--golden-primary), var(--golden-bright));
    border: 1px solid var(--cyber-gold);
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.cyber-button:hover {
    background: linear-gradient(45deg, var(--golden-bright), var(--neon-gold));
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.5), 0 0 35px rgba(255, 165, 0, 0.3);
    transform: translateY(-2px);
}

.cyber-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.cyber-button:hover::before {
    left: 100%;
}

/* Additional GoldenAura Enhancements */
.golden-text {
    background: linear-gradient(45deg, var(--golden-primary), var(--golden-bright));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.golden-border {
    border: 2px solid var(--golden-primary);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.golden-glow {
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.3),
        0 0 40px rgba(255, 165, 0, 0.2),
        inset 0 0 20px rgba(255, 215, 0, 0.1);
}

/* Enhanced card styling */
.baccarat-card {
    background: linear-gradient(145deg, #ffffff, #f8f8f8);
    border: 2px solid var(--golden-dark);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.baccarat-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 215, 0, 0.4);
}

/* Betting area enhancements */
.bet-area {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.05));
    border: 1px solid rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.bet-area:hover {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));
    border-color: var(--golden-primary);
}