// Game state
let balance = 1000;

// Skill Slots: Cyber Arena game state
let skillSlotsGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'novice',
    slotMode: 'classic',
    arenaLevel: 1,
    
    // Slot mechanics
    reels: [[], [], [], [], []],
    reelPositions: [0, 0, 0, 0, 0],
    reelSpeeds: [0, 0, 0, 0, 0],
    isSpinning: false,
    stoppedReels: 0,
    
    // Skill-based timing
    skillZones: [],
    perfectStops: 0,
    goodStops: 0,
    missedStops: 0,
    timingAccuracy: 0,
    
    // Cyber Arena features
    cyberLevel: 1,
    hackingPower: 0,
    digitalBonus: 0,
    matrixMultiplier: 1.0,
    glitchEffects: 0,
    
    // Performance tracking
    totalSpins: 0,
    perfectSpinStreak: 0,
    maxPerfectStreak: 0,
    skillPoints: 0,
    arenaRank: 'Rookie',
    
    // Advanced metrics
    reactionTime: [],
    consistencyRating: 0,
    precisionScore: 0,
    cyberMastery: 0,
    
    // Combo system
    currentCombo: 0,
    maxCombo: 0,
    comboMultiplier: 1.0,
    
    // Arena progression
    experiencePoints: 0,
    nextLevelXP: 100,
    unlockedFeatures: ['basic_slots'],
    achievements: []
};

// Ultra-strict difficulty settings with sub-6% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'novice',
        description: 'Rookie Arena (6% win rate)',
        winRate: 0.06,
        multiplier: 0.9,
        skillZoneSize: 0.15,
        reelSpeed: 1.0,
        requiredPerfectStops: 2,
        timingWindow: 150
    },
    {
        name: 'adept',
        description: 'Cyber Warrior (4.5% win rate)',
        winRate: 0.045,
        multiplier: 1.3,
        skillZoneSize: 0.12,
        reelSpeed: 1.3,
        requiredPerfectStops: 3,
        timingWindow: 120
    },
    {
        name: 'expert',
        description: 'Digital Master (3% win rate)',
        winRate: 0.03,
        multiplier: 1.8,
        skillZoneSize: 0.09,
        reelSpeed: 1.6,
        requiredPerfectStops: 4,
        timingWindow: 100
    },
    {
        name: 'master',
        description: 'Matrix Elite (2% win rate)',
        winRate: 0.02,
        multiplier: 2.4,
        skillZoneSize: 0.06,
        reelSpeed: 2.0,
        requiredPerfectStops: 5,
        timingWindow: 80
    },
    {
        name: 'grandmaster',
        description: 'Cyber God (1.5% win rate)',
        winRate: 0.015,
        multiplier: 3.0,
        skillZoneSize: 0.04,
        reelSpeed: 2.5,
        requiredPerfectStops: 5,
        timingWindow: 60
    }
];

const SLOT_MODES = [
    {
        name: 'classic',
        description: 'Classic 5-Reel Slots',
        reelCount: 5,
        paylineMultiplier: 1.0,
        specialFeatures: false
    },
    {
        name: 'turbo',
        description: 'Turbo Speed Mode',
        reelCount: 5,
        paylineMultiplier: 1.4,
        specialFeatures: true
    },
    {
        name: 'matrix',
        description: 'Matrix Grid Mode',
        reelCount: 5,
        paylineMultiplier: 1.8,
        specialFeatures: true
    },
    {
        name: 'cyber_storm',
        description: 'Cyber Storm Arena',
        reelCount: 5,
        paylineMultiplier: 2.2,
        specialFeatures: true
    }
];

// Cyber-themed slot symbols with varying rarities
const CYBER_SYMBOLS = [
    { symbol: '🔴', name: 'Red Node', value: 1, rarity: 0.25, color: '#ff4444' },
    { symbol: '🟡', name: 'Yellow Core', value: 2, rarity: 0.20, color: '#ffaa00' },
    { symbol: '🟢', name: 'Green Data', value: 3, rarity: 0.18, color: '#44ff44' },
    { symbol: '🔵', name: 'Blue Circuit', value: 4, rarity: 0.15, color: '#4444ff' },
    { symbol: '🟣', name: 'Purple Matrix', value: 5, rarity: 0.12, color: '#aa44ff' },
    { symbol: '⚡', name: 'Lightning Bolt', value: 8, rarity: 0.06, color: '#ffff44' },
    { symbol: '💎', name: 'Cyber Diamond', value: 12, rarity: 0.03, color: '#44ffff' },
    { symbol: '🌟', name: 'Digital Star', value: 20, rarity: 0.008, color: '#ffffff' },
    { symbol: '👑', name: 'Cyber Crown', value: 50, rarity: 0.002, color: '#ffd700' }
];

const SKILL_ZONES = [
    { name: 'Perfect', multiplier: 2.0, color: '#00ff00', size: 0.04 },
    { name: 'Excellent', multiplier: 1.5, color: '#44ff44', size: 0.08 },
    { name: 'Good', multiplier: 1.2, color: '#88ff88', size: 0.12 },
    { name: 'Fair', multiplier: 1.0, color: '#ffff44', size: 0.16 },
    { name: 'Miss', multiplier: 0.5, color: '#ff4444', size: 1.0 }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadSkillSlotsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Skill Slots Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎰 SKILL SLOTS: CYBER ARENA 🎰</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 CYBER BET</label>
                        <input type="number" id="slotsBet" value="75" min="25" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateSlotsPayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🏆 ARENA LEVEL</label>
                            <select id="slotsDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSlotsSettings()">
                                <option value="novice">Rookie (6%)</option>
                                <option value="adept" selected>Warrior (4.5%)</option>
                                <option value="expert">Master (3%)</option>
                                <option value="master">Elite (2%)</option>
                                <option value="grandmaster">God (1.5%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">⚡ SLOT MODE</label>
                            <select id="slotsMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSlotsSettings()">
                                <option value="classic" selected>Classic 5-Reel</option>
                                <option value="turbo">Turbo Speed</option>
                                <option value="matrix">Matrix Grid</option>
                                <option value="cyber_storm">Cyber Storm</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="slotsWinRate">4.5%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Perfect Stops:</div>
                                <div class="text-yellow-400 font-bold" id="requiredPerfectStops">3/5</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Skill Zone:</div>
                                <div class="text-blue-400 font-bold" id="skillZoneSize">12%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Timing Window:</div>
                                <div class="text-purple-400 font-bold" id="timingWindow">120ms</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="slotsPotentialPayout">975 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startSkillSlots()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        🎰 SPIN CYBER REELS 🎰
                    </button>

                    <button onclick="stopReel()" id="stopReelBtn"
                            class="w-full cyber-button bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 disabled:opacity-50"
                            disabled>
                        ⏹️ STOP REEL (Space)
                    </button>
                </div>

                <!-- Skill Performance -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚡ SKILL PERFORMANCE</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Stops:</span>
                            <span class="text-green-400" id="perfectStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Good Stops:</span>
                            <span class="text-blue-400" id="goodStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Missed Stops:</span>
                            <span class="text-red-400" id="missedStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Accuracy:</span>
                            <span class="text-yellow-400" id="timingAccuracy">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Combo:</span>
                            <span class="text-pink-400" id="currentCombo">0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Skill Points:</span>
                            <span class="text-purple-400" id="skillPoints">0</span>
                        </div>
                    </div>
                </div>

                <!-- Arena Progression -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 ARENA PROGRESSION</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Arena Rank:</span>
                            <span class="text-yellow-400" id="arenaRank">Rookie</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="xpBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="xpProgress">0 / 100 XP</div>
                    </div>
                </div>
            </div>

            <!-- Cyber Slot Machine -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="slotsTitle">Cyber Arena Slots</h5>
                        <p class="text-sm text-gray-400" id="slotsDescription">Perfect timing wins the jackpot</p>
                    </div>
                    
                    <!-- Slot Machine Display -->
                    <div class="relative bg-gradient-to-b from-purple-900/50 to-black/50 rounded-lg p-6 mb-4 border-2 border-purple-500/30">
                        <!-- Skill Zone Indicator -->
                        <div class="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs">
                            <div id="skillZoneIndicator" class="bg-black/70 px-3 py-1 rounded text-green-400 font-bold">
                                SKILL ZONE: READY
                            </div>
                        </div>
                        
                        <!-- Reels Container -->
                        <div class="grid grid-cols-5 gap-2 mb-4" id="reelsContainer">
                            <!-- Reels will be generated here -->
                        </div>
                        
                        <!-- Skill Zone Visual -->
                        <div class="relative h-8 bg-gray-800 rounded mb-4">
                            <div id="skillZoneBar" class="absolute top-0 left-0 h-full bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-yellow-500 to-red-500 rounded"></div>
                            <div id="timingIndicator" class="absolute top-0 w-1 h-full bg-white shadow-lg transition-all duration-100" style="left: 0%"></div>
                            <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">
                                TIMING ZONE
                            </div>
                        </div>
                        
                        <!-- Payline Display -->
                        <div class="grid grid-cols-5 gap-2 mb-4">
                            <div class="text-center text-xs text-gray-400">REEL 1</div>
                            <div class="text-center text-xs text-gray-400">REEL 2</div>
                            <div class="text-center text-xs text-gray-400">REEL 3</div>
                            <div class="text-center text-xs text-gray-400">REEL 4</div>
                            <div class="text-center text-xs text-gray-400">REEL 5</div>
                        </div>
                        
                        <!-- Current Reel Indicator -->
                        <div class="text-center mb-2">
                            <span class="text-purple-400 font-bold" id="currentReelIndicator">Press SPACE to stop Reel 1</span>
                        </div>
                    </div>

                    <!-- Spin Results -->
                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <h6 class="text-sm font-bold text-gray-400 mb-2">SPIN RESULTS</h6>
                        <div id="spinResults" class="text-center">
                            <div class="text-gray-500">Ready to spin...</div>
                        </div>
                    </div>

                    <!-- Win Display -->
                    <div class="text-center">
                        <div id="spinResult" class="text-xl font-bold mb-2"></div>
                        <div id="spinWinAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Symbol Paytable -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">💎 SYMBOL VALUES</h5>
                    <div class="grid grid-cols-3 gap-2 text-xs" id="symbolPaytable">
                        <!-- Symbol values will be generated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Overlay -->
        <div id="gameOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🎰 SPIN COMPLETE 🎰</h3>
                <div id="finalSpinResult" class="text-xl mb-4"></div>
                <div id="finalSpinStats" class="text-sm text-gray-400 mb-6"></div>
                <button onclick="hideGameOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    BACK TO ARENA
                </button>
            </div>
        </div>
    `;

    updateSlotsSettings();
    generateReels();
    generateSymbolPaytable();
    initializeSkillZone();
    
    // Add keyboard listener for spacebar
    document.addEventListener('keydown', handleKeyPress);
}

function updateSlotsSettings() {
    const difficulty = document.getElementById('slotsDifficulty').value;
    const mode = document.getElementById('slotsMode').value;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    const modeData = SLOT_MODES.find(m => m.name === mode);
    
    skillSlotsGame.difficulty = difficulty;
    skillSlotsGame.slotMode = mode;
    
    document.getElementById('slotsWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('requiredPerfectStops').textContent = diffData.requiredPerfectStops + '/5';
    document.getElementById('skillZoneSize').textContent = Math.floor(diffData.skillZoneSize * 100) + '%';
    document.getElementById('timingWindow').textContent = diffData.timingWindow + 'ms';
    
    updateSlotsPayout();
}

function updateSlotsPayout() {
    const betAmount = parseInt(document.getElementById('slotsBet').value) || 75;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === skillSlotsGame.difficulty);
    const modeData = SLOT_MODES.find(m => m.name === skillSlotsGame.slotMode);
    
    const totalMultiplier = diffData.multiplier * modeData.paylineMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 13); // Conservative multiplier
    
    document.getElementById('slotsPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function generateReels() {
    const container = document.getElementById('reelsContainer');
    container.innerHTML = '';
    
    for (let i = 0; i < 5; i++) {
        const reel = document.createElement('div');
        reel.className = 'relative bg-black/50 rounded-lg border border-purple-500/30 h-32 overflow-hidden';
        reel.id = `reel${i}`;
        
        const reelContent = document.createElement('div');
        reelContent.className = 'absolute inset-0 flex flex-col items-center justify-center transition-transform duration-100';
        reelContent.id = `reelContent${i}`;
        
        // Generate reel symbols
        skillSlotsGame.reels[i] = [];
        for (let j = 0; j < 20; j++) {
            const symbol = getRandomSymbol();
            skillSlotsGame.reels[i].push(symbol);
            
            const symbolEl = document.createElement('div');
            symbolEl.className = 'text-4xl py-2 flex items-center justify-center h-16';
            symbolEl.textContent = symbol.symbol;
            symbolEl.style.color = symbol.color;
            reelContent.appendChild(symbolEl);
        }
        
        reel.appendChild(reelContent);
        container.appendChild(reel);
    }
}

function getRandomSymbol() {
    const rand = Math.random();
    let cumulativeProbability = 0;
    
    for (const symbol of CYBER_SYMBOLS) {
        cumulativeProbability += symbol.rarity;
        if (rand <= cumulativeProbability) {
            return symbol;
        }
    }
    
    return CYBER_SYMBOLS[0]; // Fallback
}

function generateSymbolPaytable() {
    const container = document.getElementById('symbolPaytable');
    container.innerHTML = '';
    
    CYBER_SYMBOLS.forEach(symbol => {
        const item = document.createElement('div');
        item.className = 'flex items-center justify-between bg-black/20 p-2 rounded';
        item.innerHTML = `
            <span style="color: ${symbol.color}">${symbol.symbol}</span>
            <span class="text-yellow-400 font-bold">${symbol.value}x</span>
        `;
        container.appendChild(item);
    });
}

function initializeSkillZone() {
    const skillZoneBar = document.getElementById('skillZoneBar');
    skillZoneBar.style.width = '100%';
    
    updateSkillDisplay();
}

function startSkillSlots() {
    const betAmount = parseInt(document.getElementById('slotsBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    skillSlotsGame.isPlaying = true;
    skillSlotsGame.gamePhase = 'spinning';
    skillSlotsGame.betAmount = betAmount;
    skillSlotsGame.stoppedReels = 0;
    skillSlotsGame.perfectStops = 0;
    skillSlotsGame.goodStops = 0;
    skillSlotsGame.missedStops = 0;
    skillSlotsGame.currentCombo = 0;
    skillSlotsGame.reactionTime = [];
    
    // Start spinning all reels
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === skillSlotsGame.difficulty);
    
    for (let i = 0; i < 5; i++) {
        skillSlotsGame.reelSpeeds[i] = diffData.reelSpeed * (0.8 + Math.random() * 0.4);
        skillSlotsGame.reelPositions[i] = 0;
    }
    
    skillSlotsGame.isSpinning = true;
    skillSlotsGame.spinStartTime = Date.now();
    
    document.getElementById('stopReelBtn').disabled = false;
    document.getElementById('currentReelIndicator').textContent = 'Press SPACE to stop Reel 1';
    document.getElementById('skillZoneIndicator').textContent = 'SKILL ZONE: ACTIVE';
    document.getElementById('skillZoneIndicator').className = 'bg-black/70 px-3 py-1 rounded text-yellow-400 font-bold animate-pulse';
    
    hideGameOverlay();
    startReelAnimation();
    startSkillZoneAnimation();
}

function startReelAnimation() {
    if (!skillSlotsGame.isSpinning) return;
    
    for (let i = skillSlotsGame.stoppedReels; i < 5; i++) {
        skillSlotsGame.reelPositions[i] += skillSlotsGame.reelSpeeds[i];
        
        const reelContent = document.getElementById(`reelContent${i}`);
        const translateY = -(skillSlotsGame.reelPositions[i] % (20 * 64)); // 20 symbols * 64px height
        reelContent.style.transform = `translateY(${translateY}px)`;
    }
    
    if (skillSlotsGame.isSpinning) {
        requestAnimationFrame(startReelAnimation);
    }
}

function startSkillZoneAnimation() {
    if (!skillSlotsGame.isSpinning) return;
    
    const indicator = document.getElementById('timingIndicator');
    const time = Date.now() - skillSlotsGame.spinStartTime;
    const position = (time / 20) % 100; // Complete cycle every 2 seconds
    
    indicator.style.left = position + '%';
    
    if (skillSlotsGame.isSpinning) {
        requestAnimationFrame(startSkillZoneAnimation);
    }
}

function stopReel() {
    if (!skillSlotsGame.isSpinning || skillSlotsGame.stoppedReels >= 5) return;
    
    const reelIndex = skillSlotsGame.stoppedReels;
    const stopTime = Date.now();
    const reactionTime = stopTime - skillSlotsGame.spinStartTime;
    skillSlotsGame.reactionTime.push(reactionTime);
    
    // Calculate timing accuracy
    const indicator = document.getElementById('timingIndicator');
    const position = parseFloat(indicator.style.left);
    const accuracy = calculateTimingAccuracy(position);
    
    // Stop the reel at current position
    const finalPosition = Math.floor(skillSlotsGame.reelPositions[reelIndex] / 64) % 20;
    const reelContent = document.getElementById(`reelContent${reelIndex}`);
    reelContent.style.transform = `translateY(-${finalPosition * 64}px)`;
    
    // Update performance stats
    if (accuracy.zone === 'Perfect') {
        skillSlotsGame.perfectStops++;
        skillSlotsGame.currentCombo++;
        skillSlotsGame.skillPoints += 10;
    } else if (accuracy.zone === 'Excellent' || accuracy.zone === 'Good') {
        skillSlotsGame.goodStops++;
        skillSlotsGame.currentCombo++;
        skillSlotsGame.skillPoints += 5;
    } else {
        skillSlotsGame.missedStops++;
        skillSlotsGame.currentCombo = 0;
    }
    
    skillSlotsGame.stoppedReels++;
    skillSlotsGame.maxCombo = Math.max(skillSlotsGame.maxCombo, skillSlotsGame.currentCombo);
    
    // Update display
    updateSkillDisplay();
    
    // Show timing feedback
    showTimingFeedback(accuracy, reelIndex);
    
    if (skillSlotsGame.stoppedReels < 5) {
        document.getElementById('currentReelIndicator').textContent = `Press SPACE to stop Reel ${skillSlotsGame.stoppedReels + 1}`;
    } else {
        // All reels stopped
        skillSlotsGame.isSpinning = false;
        document.getElementById('stopReelBtn').disabled = true;
        document.getElementById('currentReelIndicator').textContent = 'Calculating results...';
        document.getElementById('skillZoneIndicator').textContent = 'SKILL ZONE: COMPLETE';
        document.getElementById('skillZoneIndicator').className = 'bg-black/70 px-3 py-1 rounded text-green-400 font-bold';
        
        setTimeout(() => {
            calculateSpinResult();
        }, 1000);
    }
}

function calculateTimingAccuracy(position) {
    // Perfect zone: 48-52%
    if (position >= 48 && position <= 52) {
        return { zone: 'Perfect', multiplier: 2.0, color: '#00ff00' };
    }
    // Excellent zone: 45-47% or 53-55%
    else if ((position >= 45 && position < 48) || (position > 52 && position <= 55)) {
        return { zone: 'Excellent', multiplier: 1.5, color: '#44ff44' };
    }
    // Good zone: 40-44% or 56-60%
    else if ((position >= 40 && position < 45) || (position > 55 && position <= 60)) {
        return { zone: 'Good', multiplier: 1.2, color: '#88ff88' };
    }
    // Fair zone: 30-39% or 61-70%
    else if ((position >= 30 && position < 40) || (position > 60 && position <= 70)) {
        return { zone: 'Fair', multiplier: 1.0, color: '#ffff44' };
    }
    // Miss zone: everything else
    else {
        return { zone: 'Miss', multiplier: 0.5, color: '#ff4444' };
    }
}

function showTimingFeedback(accuracy, reelIndex) {
    const reel = document.getElementById(`reel${reelIndex}`);
    const feedback = document.createElement('div');
    feedback.className = 'absolute inset-0 flex items-center justify-center text-lg font-bold pointer-events-none z-10';
    feedback.style.color = accuracy.color;
    feedback.textContent = accuracy.zone;
    
    reel.appendChild(feedback);
    
    setTimeout(() => {
        reel.removeChild(feedback);
    }, 1000);
}

function updateSkillDisplay() {
    document.getElementById('perfectStops').textContent = skillSlotsGame.perfectStops;
    document.getElementById('goodStops').textContent = skillSlotsGame.goodStops;
    document.getElementById('missedStops').textContent = skillSlotsGame.missedStops;
    
    const totalStops = skillSlotsGame.perfectStops + skillSlotsGame.goodStops + skillSlotsGame.missedStops;
    const accuracy = totalStops > 0 ? Math.floor(((skillSlotsGame.perfectStops + skillSlotsGame.goodStops) / totalStops) * 100) : 0;
    document.getElementById('timingAccuracy').textContent = accuracy + '%';
    
    document.getElementById('currentCombo').textContent = skillSlotsGame.currentCombo + 'x';
    document.getElementById('skillPoints').textContent = skillSlotsGame.skillPoints;
    
    // Update XP bar
    const xpPercentage = (skillSlotsGame.experiencePoints / skillSlotsGame.nextLevelXP) * 100;
    document.getElementById('xpBar').style.width = Math.min(100, xpPercentage) + '%';
    document.getElementById('xpProgress').textContent = `${skillSlotsGame.experiencePoints} / ${skillSlotsGame.nextLevelXP} XP`;
}

function calculateSpinResult() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === skillSlotsGame.difficulty);
    const modeData = SLOT_MODES.find(m => m.name === skillSlotsGame.slotMode);
    
    // Ultra-strict winning requirements
    const perfectStopRequirement = skillSlotsGame.perfectStops >= diffData.requiredPerfectStops;
    const accuracyRequirement = skillSlotsGame.perfectStops + skillSlotsGame.goodStops >= 4;
    const comboRequirement = skillSlotsGame.maxCombo >= 3;
    const timingRequirement = skillSlotsGame.missedStops <= 1;
    
    // Calculate average reaction time
    const avgReactionTime = skillSlotsGame.reactionTime.reduce((a, b) => a + b, 0) / skillSlotsGame.reactionTime.length;
    const reactionRequirement = avgReactionTime <= diffData.timingWindow;
    
    // Check for symbol matches (simplified)
    const symbolMatch = checkSymbolMatches();
    
    const meetsAllRequirements = 
        perfectStopRequirement && 
        accuracyRequirement && 
        comboRequirement && 
        timingRequirement && 
        reactionRequirement &&
        symbolMatch.hasWin;
    
    const finalWinChance = meetsAllRequirements ? diffData.winRate : 0;
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with bonuses
        const baseMultiplier = diffData.multiplier * modeData.paylineMultiplier;
        const perfectBonus = skillSlotsGame.perfectStops * 0.2;
        const comboBonus = skillSlotsGame.maxCombo * 0.1;
        const speedBonus = avgReactionTime < diffData.timingWindow * 0.8 ? 0.3 : 0;
        const symbolBonus = symbolMatch.multiplier;
        
        const totalMultiplier = baseMultiplier * (1 + perfectBonus + comboBonus + speedBonus) * symbolBonus;
        const winnings = Math.floor(skillSlotsGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        skillSlotsGame.perfectSpinStreak++;
        skillSlotsGame.maxPerfectStreak = Math.max(skillSlotsGame.maxPerfectStreak, skillSlotsGame.perfectSpinStreak);
        skillSlotsGame.experiencePoints += 25;
        
        document.getElementById('spinResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🎰 JACKPOT! 🎰</span>`;
        document.getElementById('spinWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        skillSlotsGame.perfectSpinStreak = 0;
        
        const requirements = [
            `${diffData.requiredPerfectStops}+ perfect stops (${skillSlotsGame.perfectStops})`,
            `4+ accurate stops (${skillSlotsGame.perfectStops + skillSlotsGame.goodStops})`,
            `3+ combo streak (${skillSlotsGame.maxCombo})`,
            `≤1 missed stop (${skillSlotsGame.missedStops})`,
            `<${diffData.timingWindow}ms reaction (${Math.floor(avgReactionTime)}ms)`
        ];
        
        document.getElementById('spinResult').innerHTML = 
            `<span class="text-red-400">🎰 NO WIN 🎰</span>`;
        document.getElementById('spinWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Need: ${requirements.slice(0, 3).join(', ')}<br>
            ${requirements.slice(3, 5).join(', ')}</div>`;
    }
    
    skillSlotsGame.totalSpins++;
    updateSkillDisplay();
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
        document.getElementById('finalSpinResult').innerHTML = 
            won ? '<span class="text-green-400">🎰 CYBER JACKPOT! 🎰</span>' : 
                  '<span class="text-red-400">💔 SPIN FAILED 💔</span>';
        document.getElementById('finalSpinStats').innerHTML = 
            `Perfect Stops: ${skillSlotsGame.perfectStops}/${diffData.requiredPerfectStops}<br>
             Timing Accuracy: ${Math.floor(((skillSlotsGame.perfectStops + skillSlotsGame.goodStops) / 5) * 100)}%<br>
             Max Combo: ${skillSlotsGame.maxCombo} | Avg Reaction: ${Math.floor(avgReactionTime)}ms`;
    }, 3000);
}

function checkSymbolMatches() {
    // Simplified symbol matching - check for 3+ matching symbols
    const symbols = [];
    for (let i = 0; i < 5; i++) {
        const position = Math.floor(skillSlotsGame.reelPositions[i] / 64) % 20;
        symbols.push(skillSlotsGame.reels[i][position]);
    }
    
    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol.symbol] = (symbolCounts[symbol.symbol] || 0) + 1;
    });
    
    // Find best match
    let bestMatch = { count: 0, symbol: null, multiplier: 1 };
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 3) {
            const symbolData = CYBER_SYMBOLS.find(s => s.symbol === symbol);
            const multiplier = symbolData.value * (count - 2); // 3=1x, 4=2x, 5=3x
            if (multiplier > bestMatch.multiplier) {
                bestMatch = { count, symbol, multiplier, hasWin: true };
            }
        }
    }
    
    return bestMatch.hasWin ? bestMatch : { hasWin: false, multiplier: 1 };
}

function handleKeyPress(e) {
    if (e.code === 'Space' && skillSlotsGame.isSpinning) {
        e.preventDefault();
        stopReel();
    }
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSkillSlotsGame();
});