// Game state
let balance = 1000;

// Tetris Treasure Tumble game state
let tetrisGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'classic',
    gameMode: 'treasure',
    treasureLevel: 1,
    
    // Tetris mechanics
    board: Array(20).fill().map(() => Array(10).fill(0)),
    currentPiece: null,
    nextPiece: null,
    holdPiece: null,
    canHold: true,
    pieceX: 0,
    pieceY: 0,
    
    // Treasure features
    treasureBlocks: new Set(),
    treasureMultiplier: 1.0,
    treasureChains: 0,
    treasureCombo: 0,
    maxCombo: 0,
    goldBlocks: 0,
    diamondBlocks: 0,
    
    // Game mechanics
    level: 1,
    lines: 0,
    score: 0,
    dropTimer: 0,
    dropInterval: 1000,
    softDrop: false,
    
    // Performance tracking
    totalLines: 0,
    tetrisCount: 0,
    tSpinCount: 0,
    perfectClears: 0,
    treasuresCollected: 0,
    
    // Special features
    powerUps: [],
    achievements: [],
    streakCount: 0,
    maxStreak: 0,
    
    // Enhanced timing
    startTime: 0,
    gameTimer: null,
    lastDrop: 0,
    
    // Progressive features
    treasurePoints: 0,
    treasureRank: 'Treasure Hunter',
    unlockedModes: ['treasure'],
    masteryLevel: 1
};

// Tetris pieces
const TETRIS_PIECES = {
    I: {
        shape: [[1, 1, 1, 1]],
        color: 'cyan',
        treasure: false
    },
    O: {
        shape: [
            [1, 1],
            [1, 1]
        ],
        color: 'yellow',
        treasure: false
    },
    T: {
        shape: [
            [0, 1, 0],
            [1, 1, 1]
        ],
        color: 'purple',
        treasure: false
    },
    S: {
        shape: [
            [0, 1, 1],
            [1, 1, 0]
        ],
        color: 'green',
        treasure: false
    },
    Z: {
        shape: [
            [1, 1, 0],
            [0, 1, 1]
        ],
        color: 'red',
        treasure: false
    },
    J: {
        shape: [
            [1, 0, 0],
            [1, 1, 1]
        ],
        color: 'blue',
        treasure: false
    },
    L: {
        shape: [
            [0, 0, 1],
            [1, 1, 1]
        ],
        color: 'orange',
        treasure: false
    },
    // Special treasure pieces
    GOLD: {
        shape: [
            [2, 2],
            [2, 2]
        ],
        color: 'gold',
        treasure: true,
        value: 50
    },
    DIAMOND: {
        shape: [[3]],
        color: 'diamond',
        treasure: true,
        value: 100
    },
    RUBY: {
        shape: [
            [0, 4, 0],
            [4, 4, 4]
        ],
        color: 'ruby',
        treasure: true,
        value: 75
    }
};

// Treasure Tumble difficulty settings
const TREASURE_DIFFICULTIES = [
    {
        name: 'classic',
        description: 'Classic Tetris (25% treasure rate)',
        treasureRate: 0.25,
        multiplier: 1.5,
        dropSpeed: 1000,
        treasureValue: 1.0,
        lineGoal: 40
    },
    {
        name: 'treasure',
        description: 'Treasure Hunt (35% treasure rate)',
        treasureRate: 0.35,
        multiplier: 2.0,
        dropSpeed: 800,
        treasureValue: 1.2,
        lineGoal: 35
    },
    {
        name: 'expert',
        description: 'Expert Tumble (20% treasure rate)',
        treasureRate: 0.20,
        multiplier: 2.8,
        dropSpeed: 600,
        treasureValue: 1.5,
        lineGoal: 50
    },
    {
        name: 'master',
        description: 'Master Tumble (15% treasure rate)',
        treasureRate: 0.15,
        multiplier: 3.5,
        dropSpeed: 400,
        treasureValue: 2.0,
        lineGoal: 60
    },
    {
        name: 'legendary',
        description: 'Legendary Tumble (10% treasure rate)',
        treasureRate: 0.10,
        multiplier: 5.0,
        dropSpeed: 300,
        treasureValue: 3.0,
        lineGoal: 80
    },
    {
        name: 'ultimate',
        description: 'Ultimate Treasure (5% treasure rate)',
        treasureRate: 0.05,
        multiplier: 8.0,
        dropSpeed: 200,
        treasureValue: 5.0,
        lineGoal: 100
    }
];

const TREASURE_MODES = [
    {
        name: 'treasure',
        description: 'Classic Treasure Hunt',
        specialPieces: ['GOLD'],
        payoutMultiplier: 1.0
    },
    {
        name: 'diamond',
        description: 'Diamond Rush',
        specialPieces: ['DIAMOND', 'GOLD'],
        payoutMultiplier: 1.5
    },
    {
        name: 'ruby',
        description: 'Ruby Rain',
        specialPieces: ['RUBY', 'GOLD'],
        payoutMultiplier: 1.3
    },
    {
        name: 'mixed',
        description: 'Mixed Treasures',
        specialPieces: ['GOLD', 'DIAMOND', 'RUBY'],
        payoutMultiplier: 2.0
    },
    {
        name: 'cascade',
        description: 'Treasure Cascade',
        specialPieces: ['GOLD', 'DIAMOND'],
        payoutMultiplier: 2.5
    },
    {
        name: 'avalanche',
        description: 'Treasure Avalanche',
        specialPieces: ['GOLD', 'DIAMOND', 'RUBY'],
        payoutMultiplier: 3.0
    }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadTetrisGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🧩 TETRIS TREASURE TUMBLE 🧩</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 TREASURE BET</label>
                        <input type="number" id="tetrisBet" value="100" min="50" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateTetrisPayout()">
                    </div>

                    <div class="grid grid-cols-1 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎯 DIFFICULTY</label>
                            <select id="tetrisDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateTetrisSettings()">
                                <option value="classic" selected>Classic (25%)</option>
                                <option value="treasure">Treasure Hunt (35%)</option>
                                <option value="expert">Expert Tumble (20%)</option>
                                <option value="master">Master Tumble (15%)</option>
                                <option value="legendary">Legendary (10%)</option>
                                <option value="ultimate">Ultimate (5%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">💎 TREASURE MODE</label>
                            <select id="tetrisMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateTetrisSettings()">
                                <option value="treasure" selected>Treasure Hunt</option>
                                <option value="diamond">Diamond Rush</option>
                                <option value="ruby">Ruby Rain</option>
                                <option value="mixed">Mixed Treasures</option>
                                <option value="cascade">Treasure Cascade</option>
                                <option value="avalanche">Treasure Avalanche</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Treasure Rate:</div>
                                <div class="text-yellow-400 font-bold" id="treasureRate">25%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Drop Speed:</div>
                                <div class="text-cyan-400 font-bold" id="dropSpeed">1000ms</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Line Goal:</div>
                                <div class="text-green-400 font-bold" id="lineGoal">40</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Treasure Value:</div>
                                <div class="text-purple-400 font-bold" id="treasureValue">1.0x</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="tetrisPotentialPayout">800 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startTetrisGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        🧩 START TUMBLING 🧩
                    </button>

                    <div class="grid grid-cols-3 gap-2">
                        <button onclick="holdPiece()" id="holdBtn"
                                class="cyber-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            📦 HOLD
                        </button>
                        <button onclick="hardDrop()" id="dropBtn"
                                class="cyber-button bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            ⬇️ DROP
                        </button>
                        <button onclick="pauseGame()" id="pauseBtn"
                                class="cyber-button bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            ⏸️ PAUSE
                        </button>
                    </div>
                </div>

                <!-- Next and Hold Pieces -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <h5 class="text-sm font-bold mb-2 text-purple-400">NEXT</h5>
                            <div id="nextPieceDisplay" class="bg-black/50 p-2 rounded-lg h-16 flex items-center justify-center">
                                -
                            </div>
                        </div>
                        <div>
                            <h5 class="text-sm font-bold mb-2 text-purple-400">HOLD</h5>
                            <div id="holdPieceDisplay" class="bg-black/50 p-2 rounded-lg h-16 flex items-center justify-center">
                                -
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 TREASURE STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Level:</span>
                            <span class="text-green-400" id="currentLevel">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Lines:</span>
                            <span class="text-cyan-400" id="linesCleared">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Score:</span>
                            <span class="text-yellow-400" id="currentScore">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Treasures:</span>
                            <span class="text-pink-400" id="treasuresFound">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Combo:</span>
                            <span class="text-orange-400" id="currentCombo">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Multiplier:</span>
                            <span class="text-purple-400" id="treasureMultiplier">1.0x</span>
                        </div>
                    </div>
                </div>

                <!-- Treasure Mastery -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 TREASURE MASTERY</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Treasure Rank:</span>
                            <span class="text-yellow-400" id="treasureRank">Treasure Hunter</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="treasureProgressBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="treasureProgress">0 / 100 Points</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-xs mt-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tetris:</span>
                            <span class="text-green-400" id="tetrisCount">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">T-Spins:</span>
                            <span class="text-blue-400" id="tSpinCount">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Board -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="gameTitle">Tetris Treasure Tumble</h5>
                        <p class="text-sm text-gray-400" id="gameDescription">Clear lines and collect treasures for massive payouts!</p>
                    </div>
                    
                    <!-- Tetris Board -->
                    <div class="flex justify-center mb-6">
                        <div class="relative">
                            <canvas id="tetrisCanvas" width="300" height="600" 
                                    class="border-2 border-purple-500/50 rounded-lg bg-black/50"></canvas>
                            <div id="gameOverOverlay" class="absolute inset-0 bg-black/80 flex items-center justify-center rounded-lg hidden">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-red-400 mb-2">GAME OVER</div>
                                    <div class="text-sm text-gray-400">Press Space to restart</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Controls Info -->
                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <h6 class="text-sm font-bold mb-2 text-purple-400">🎮 CONTROLS</h6>
                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div>← → : Move</div>
                            <div>↓ : Soft Drop</div>
                            <div>↑ : Rotate</div>
                            <div>Space : Hard Drop</div>
                            <div>C : Hold Piece</div>
                            <div>P : Pause</div>
                        </div>
                    </div>

                    <!-- Game Results -->
                    <div class="text-center mt-6">
                        <div id="tetrisResult" class="text-xl font-bold mb-2"></div>
                        <div id="tetrisWinAmount" class="text-lg"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Complete Overlay -->
        <div id="tetrisOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🧩 TREASURE COMPLETE 🧩</h3>
                <div id="finalTetrisResult" class="text-xl mb-4"></div>
                <div id="finalTetrisStats" class="text-sm text-gray-400 mb-6"></div>
                <div id="tetrisAchievements" class="text-xs text-cyan-400 mb-4"></div>
                <button onclick="hideTetrisOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    NEW GAME
                </button>
            </div>
        </div>
    `;

    updateTetrisSettings();
    initializeTetrisCanvas();
    
    // Add keyboard listeners
    document.addEventListener('keydown', handleTetrisKeyPress);
}

function updateTetrisSettings() {
    const difficulty = document.getElementById('tetrisDifficulty').value;
    const mode = document.getElementById('tetrisMode').value;
    
    const diffData = TREASURE_DIFFICULTIES.find(d => d.name === difficulty);
    const modeData = TREASURE_MODES.find(m => m.name === mode);
    
    tetrisGame.difficulty = difficulty;
    tetrisGame.gameMode = mode;
    
    document.getElementById('treasureRate').textContent = Math.floor(diffData.treasureRate * 100) + '%';
    document.getElementById('dropSpeed').textContent = diffData.dropSpeed + 'ms';
    document.getElementById('lineGoal').textContent = diffData.lineGoal;
    document.getElementById('treasureValue').textContent = diffData.treasureValue.toFixed(1) + 'x';
    
    updateTetrisPayout();
}

function updateTetrisPayout() {
    const betAmount = parseInt(document.getElementById('tetrisBet').value) || 100;
    const diffData = TREASURE_DIFFICULTIES.find(d => d.name === tetrisGame.difficulty);
    const modeData = TREASURE_MODES.find(m => m.name === tetrisGame.gameMode);
    
    const totalMultiplier = diffData.multiplier * modeData.payoutMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 8);
    
    document.getElementById('tetrisPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function initializeTetrisCanvas() {
    const canvas = document.getElementById('tetrisCanvas');
    const ctx = canvas.getContext('2d');
    
    tetrisGame.canvas = canvas;
    tetrisGame.ctx = ctx;
    
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw grid
    drawTetrisGrid();
}

function drawTetrisGrid() {
    const ctx = tetrisGame.ctx;
    const canvas = tetrisGame.canvas;
    
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    
    // Draw vertical lines
    for (let x = 0; x <= 10; x++) {
        ctx.beginPath();
        ctx.moveTo(x * 30, 0);
        ctx.lineTo(x * 30, canvas.height);
        ctx.stroke();
    }
    
    // Draw horizontal lines
    for (let y = 0; y <= 20; y++) {
        ctx.beginPath();
        ctx.moveTo(0, y * 30);
        ctx.lineTo(canvas.width, y * 30);
        ctx.stroke();
    }
}

function generatePiece() {
    const diffData = TREASURE_DIFFICULTIES.find(d => d.name === tetrisGame.difficulty);
    const modeData = TREASURE_MODES.find(m => m.name === tetrisGame.gameMode);
    
    // Check if treasure piece should spawn
    if (Math.random() < diffData.treasureRate) {
        const treasurePieces = modeData.specialPieces;
        const pieceType = treasurePieces[Math.floor(Math.random() * treasurePieces.length)];
        return {
            type: pieceType,
            shape: TETRIS_PIECES[pieceType].shape,
            color: TETRIS_PIECES[pieceType].color,
            treasure: true,
            value: TETRIS_PIECES[pieceType].value || 25
        };
    } else {
        // Regular piece
        const regularPieces = ['I', 'O', 'T', 'S', 'Z', 'J', 'L'];
        const pieceType = regularPieces[Math.floor(Math.random() * regularPieces.length)];
        return {
            type: pieceType,
            shape: TETRIS_PIECES[pieceType].shape,
            color: TETRIS_PIECES[pieceType].color,
            treasure: false,
            value: 0
        };
    }
}

function startTetrisGame() {
    const betAmount = parseInt(document.getElementById('tetrisBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance for Tetris Treasure Tumble!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    tetrisGame.isPlaying = true;
    tetrisGame.gamePhase = 'playing';
    tetrisGame.betAmount = betAmount;
    tetrisGame.board = Array(20).fill().map(() => Array(10).fill(0));
    tetrisGame.level = 1;
    tetrisGame.lines = 0;
    tetrisGame.score = 0;
    tetrisGame.treasuresCollected = 0;
    tetrisGame.treasureCombo = 0;
    tetrisGame.treasureMultiplier = 1.0;
    tetrisGame.tetrisCount = 0;
    tetrisGame.tSpinCount = 0;
    
    // Generate first pieces
    tetrisGame.currentPiece = generatePiece();
    tetrisGame.nextPiece = generatePiece();
    tetrisGame.holdPiece = null;
    tetrisGame.canHold = true;
    
    // Position current piece
    tetrisGame.pieceX = 4;
    tetrisGame.pieceY = 0;
    
    const diffData = TREASURE_DIFFICULTIES.find(d => d.name === tetrisGame.difficulty);
    tetrisGame.dropInterval = diffData.dropSpeed;
    
    tetrisGame.startTime = Date.now();
    startTetrisTimer();
    
    document.getElementById('holdBtn').disabled = false;
    document.getElementById('dropBtn').disabled = false;
    document.getElementById('pauseBtn').disabled = false;
    
    hideTetrisOverlay();
    updateTetrisDisplay();
    renderTetrisGame();
}

function startTetrisTimer() {
    tetrisGame.gameTimer = setInterval(() => {
        if (!tetrisGame.isPlaying) return;
        
        tetrisGame.dropTimer += 100;
        
        if (tetrisGame.dropTimer >= tetrisGame.dropInterval) {
            movePieceDown();
            tetrisGame.dropTimer = 0;
        }
        
        renderTetrisGame();
    }, 100);
}

function movePieceDown() {
    if (canMovePiece(tetrisGame.pieceX, tetrisGame.pieceY + 1, tetrisGame.currentPiece.shape)) {
        tetrisGame.pieceY++;
    } else {
        placePiece();
    }
}

function canMovePiece(x, y, shape) {
    for (let row = 0; row < shape.length; row++) {
        for (let col = 0; col < shape[row].length; col++) {
            if (shape[row][col]) {
                const newX = x + col;
                const newY = y + row;
                
                if (newX < 0 || newX >= 10 || newY >= 20) return false;
                if (newY >= 0 && tetrisGame.board[newY][newX]) return false;
            }
        }
    }
    return true;
}

function placePiece() {
    const piece = tetrisGame.currentPiece;
    
    // Place piece on board
    for (let row = 0; row < piece.shape.length; row++) {
        for (let col = 0; col < piece.shape[row].length; col++) {
            if (piece.shape[row][col]) {
                const x = tetrisGame.pieceX + col;
                const y = tetrisGame.pieceY + row;
                
                if (y >= 0) {
                    tetrisGame.board[y][x] = {
                        color: piece.color,
                        treasure: piece.treasure,
                        value: piece.value
                    };
                    
                    if (piece.treasure) {
                        tetrisGame.treasureBlocks.add(`${y}-${x}`);
                    }
                }
            }
        }
    }
    
    // Check for completed lines
    checkLines();
    
    // Check game over
    if (tetrisGame.pieceY <= 1) {
        endTetrisGame();
        return;
    }
    
    // Get next piece
    tetrisGame.currentPiece = tetrisGame.nextPiece;
    tetrisGame.nextPiece = generatePiece();
    tetrisGame.pieceX = 4;
    tetrisGame.pieceY = 0;
    tetrisGame.canHold = true;
    
    updateTetrisDisplay();
}

function checkLines() {
    let linesCleared = 0;
    let treasuresInLines = 0;
    
    for (let row = 19; row >= 0; row--) {
        if (tetrisGame.board[row].every(cell => cell !== 0)) {
            // Count treasures in this line
            for (let col = 0; col < 10; col++) {
                if (tetrisGame.board[row][col].treasure) {
                    treasuresInLines += tetrisGame.board[row][col].value;
                    tetrisGame.treasuresCollected++;
                }
            }
            
            // Remove line
            tetrisGame.board.splice(row, 1);
            tetrisGame.board.unshift(Array(10).fill(0));
            linesCleared++;
            row++; // Check same row again
        }
    }
    
    if (linesCleared > 0) {
        tetrisGame.lines += linesCleared;
        
        // Calculate score
        let lineScore = 0;
        switch (linesCleared) {
            case 1: lineScore = 100; break;
            case 2: lineScore = 300; break;
            case 3: lineScore = 500; break;
            case 4: 
                lineScore = 800;
                tetrisGame.tetrisCount++;
                break;
        }
        
        // Treasure bonus
        if (treasuresInLines > 0) {
            tetrisGame.treasureCombo++;
            tetrisGame.treasureMultiplier = Math.min(5.0, 1.0 + (tetrisGame.treasureCombo * 0.2));
            lineScore += treasuresInLines * tetrisGame.treasureMultiplier;
        } else {
            tetrisGame.treasureCombo = 0;
            tetrisGame.treasureMultiplier = 1.0;
        }
        
        tetrisGame.score += lineScore * tetrisGame.level;
        
        // Level up
        if (tetrisGame.lines >= tetrisGame.level * 10) {
            tetrisGame.level++;
            const diffData = TREASURE_DIFFICULTIES.find(d => d.name === tetrisGame.difficulty);
            tetrisGame.dropInterval = Math.max(100, diffData.dropSpeed - (tetrisGame.level * 50));
        }
        
        // Check win condition
        const diffData = TREASURE_DIFFICULTIES.find(d => d.name === tetrisGame.difficulty);
        if (tetrisGame.lines >= diffData.lineGoal) {
            endTetrisGame(true);
        }
    }
}

function rotatePiece() {
    if (!tetrisGame.currentPiece) return;
    
    const rotated = rotateMatrix(tetrisGame.currentPiece.shape);
    
    if (canMovePiece(tetrisGame.pieceX, tetrisGame.pieceY, rotated)) {
        tetrisGame.currentPiece.shape = rotated;
    }
}

function rotateMatrix(matrix) {
    const rows = matrix.length;
    const cols = matrix[0].length;
    const rotated = Array(cols).fill().map(() => Array(rows).fill(0));
    
    for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
            rotated[col][rows - 1 - row] = matrix[row][col];
        }
    }
    
    return rotated;
}

function holdPiece() {
    if (!tetrisGame.canHold || !tetrisGame.isPlaying) return;
    
    if (tetrisGame.holdPiece) {
        // Swap current and hold pieces
        const temp = tetrisGame.currentPiece;
        tetrisGame.currentPiece = tetrisGame.holdPiece;
        tetrisGame.holdPiece = temp;
    } else {
        // Move current to hold, get next
        tetrisGame.holdPiece = tetrisGame.currentPiece;
        tetrisGame.currentPiece = tetrisGame.nextPiece;
        tetrisGame.nextPiece = generatePiece();
    }
    
    tetrisGame.pieceX = 4;
    tetrisGame.pieceY = 0;
    tetrisGame.canHold = false;
    
    updateTetrisDisplay();
}

function hardDrop() {
    if (!tetrisGame.isPlaying) return;
    
    while (canMovePiece(tetrisGame.pieceX, tetrisGame.pieceY + 1, tetrisGame.currentPiece.shape)) {
        tetrisGame.pieceY++;
        tetrisGame.score += 2; // Hard drop bonus
    }
    
    placePiece();
}

function renderTetrisGame() {
    const ctx = tetrisGame.ctx;
    const canvas = tetrisGame.canvas;
    
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw grid
    drawTetrisGrid();
    
    // Draw placed pieces
    for (let row = 0; row < 20; row++) {
        for (let col = 0; col < 10; col++) {
            if (tetrisGame.board[row][col]) {
                drawBlock(col * 30, row * 30, tetrisGame.board[row][col].color, tetrisGame.board[row][col].treasure);
            }
        }
    }
    
    // Draw current piece
    if (tetrisGame.currentPiece) {
        const piece = tetrisGame.currentPiece;
        for (let row = 0; row < piece.shape.length; row++) {
            for (let col = 0; col < piece.shape[row].length; col++) {
                if (piece.shape[row][col]) {
                    const x = (tetrisGame.pieceX + col) * 30;
                    const y = (tetrisGame.pieceY + row) * 30;
                    drawBlock(x, y, piece.color, piece.treasure);
                }
            }
        }
    }
    
    // Update piece displays
    updatePieceDisplays();
}

function drawBlock(x, y, color, treasure = false) {
    const ctx = tetrisGame.ctx;
    
    // Color mapping
    const colors = {
        cyan: '#00FFFF',
        yellow: '#FFFF00',
        purple: '#800080',
        green: '#00FF00',
        red: '#FF0000',
        blue: '#0000FF',
        orange: '#FFA500',
        gold: '#FFD700',
        diamond: '#B9F2FF',
        ruby: '#E0115F'
    };
    
    ctx.fillStyle = colors[color] || '#FFFFFF';
    ctx.fillRect(x + 1, y + 1, 28, 28);
    
    if (treasure) {
        // Add treasure sparkle effect
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(x + 5, y + 5, 4, 4);
        ctx.fillRect(x + 21, y + 21, 4, 4);
        ctx.fillRect(x + 21, y + 5, 4, 4);
        ctx.fillRect(x + 5, y + 21, 4, 4);
    }
    
    // Border
    ctx.strokeStyle = '#FFFFFF';
    ctx.lineWidth = 1;
    ctx.strokeRect(x + 1, y + 1, 28, 28);
}

function updatePieceDisplays() {
    // Update next piece display
    const nextDisplay = document.getElementById('nextPieceDisplay');
    if (tetrisGame.nextPiece) {
        const pieceSymbol = getPieceSymbol(tetrisGame.nextPiece);
        nextDisplay.innerHTML = `<span style="color: ${getPieceColor(tetrisGame.nextPiece.color)}">${pieceSymbol}</span>`;
    }
    
    // Update hold piece display
    const holdDisplay = document.getElementById('holdPieceDisplay');
    if (tetrisGame.holdPiece) {
        const pieceSymbol = getPieceSymbol(tetrisGame.holdPiece);
        holdDisplay.innerHTML = `<span style="color: ${getPieceColor(tetrisGame.holdPiece.color)}">${pieceSymbol}</span>`;
    } else {
        holdDisplay.textContent = '-';
    }
}

function getPieceSymbol(piece) {
    const symbols = {
        I: '▬',
        O: '⬜',
        T: '⊤',
        S: '⟆',
        Z: '⟅',
        J: '⌐',
        L: '⌙',
        GOLD: '💰',
        DIAMOND: '💎',
        RUBY: '💍'
    };
    return symbols[piece.type] || '■';
}

function getPieceColor(color) {
    const colors = {
        cyan: '#00FFFF',
        yellow: '#FFFF00',
        purple: '#800080',
        green: '#00FF00',
        red: '#FF0000',
        blue: '#0000FF',
        orange: '#FFA500',
        gold: '#FFD700',
        diamond: '#B9F2FF',
        ruby: '#E0115F'
    };
    return colors[color] || '#FFFFFF';
}

function updateTetrisDisplay() {
    document.getElementById('currentLevel').textContent = tetrisGame.level;
    document.getElementById('linesCleared').textContent = tetrisGame.lines;
    document.getElementById('currentScore').textContent = tetrisGame.score.toLocaleString();
    document.getElementById('treasuresFound').textContent = tetrisGame.treasuresCollected;
    document.getElementById('currentCombo').textContent = tetrisGame.treasureCombo;
    document.getElementById('treasureMultiplier').textContent = tetrisGame.treasureMultiplier.toFixed(1) + 'x';
    document.getElementById('tetrisCount').textContent = tetrisGame.tetrisCount;
    document.getElementById('tSpinCount').textContent = tetrisGame.tSpinCount;
}

function endTetrisGame(won = false) {
    tetrisGame.isPlaying = false;
    clearInterval(tetrisGame.gameTimer);
    
    document.getElementById('holdBtn').disabled = true;
    document.getElementById('dropBtn').disabled = true;
    document.getElementById('pauseBtn').disabled = true;
    
    let winnings = 0;
    
    if (won) {
        const diffData = TREASURE_DIFFICULTIES.find(d => d.name === tetrisGame.difficulty);
        const modeData = TREASURE_MODES.find(m => m.name === tetrisGame.gameMode);
        
        // Calculate winnings
        let baseMultiplier = diffData.multiplier * modeData.payoutMultiplier;
        
        // Line completion bonus
        const lineBonus = tetrisGame.lines / diffData.lineGoal;
        
        // Treasure bonus
        const treasureBonus = tetrisGame.treasuresCollected * 0.1;
        
        // Level bonus
        const levelBonus = tetrisGame.level * 0.05;
        
        const totalMultiplier = baseMultiplier * (1 + lineBonus + treasureBonus + levelBonus);
        winnings = Math.floor(tetrisGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        tetrisGame.treasurePoints += 100;
        
        document.getElementById('tetrisResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🧩 TREASURE MASTERY! 🧩</span>`;
        document.getElementById('tetrisWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        document.getElementById('tetrisResult').innerHTML = 
            `<span class="text-red-400">🧩 GAME OVER 🧩</span>`;
        document.getElementById('tetrisWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Lines: ${tetrisGame.lines} | Score: ${tetrisGame.score.toLocaleString()}</div>`;
    }
    
    updateTetrisDisplay();
    
    setTimeout(() => {
        document.getElementById('tetrisOverlay').classList.remove('hidden');
        document.getElementById('finalTetrisResult').innerHTML = 
            won ? '<span class="text-green-400">🧩 TREASURE MASTER! 🧩</span>' : 
                  '<span class="text-red-400">💔 BLOCKS FELL 💔</span>';
        
        document.getElementById('finalTetrisStats').innerHTML = 
            `Lines: ${tetrisGame.lines} | Level: ${tetrisGame.level} | Score: ${tetrisGame.score.toLocaleString()}<br>
             Treasures: ${tetrisGame.treasuresCollected} | Tetris: ${tetrisGame.tetrisCount} | Max Combo: ${tetrisGame.treasureCombo}`;
    }, 3000);
}

function pauseGame() {
    if (!tetrisGame.isPlaying) return;
    
    if (tetrisGame.gameTimer) {
        clearInterval(tetrisGame.gameTimer);
        tetrisGame.gameTimer = null;
        document.getElementById('pauseBtn').textContent = '▶️ RESUME';
    } else {
        startTetrisTimer();
        document.getElementById('pauseBtn').textContent = '⏸️ PAUSE';
    }
}

function handleTetrisKeyPress(e) {
    if (!tetrisGame.isPlaying) return;
    
    switch(e.key) {
        case 'ArrowLeft':
            e.preventDefault();
            if (canMovePiece(tetrisGame.pieceX - 1, tetrisGame.pieceY, tetrisGame.currentPiece.shape)) {
                tetrisGame.pieceX--;
            }
            break;
        case 'ArrowRight':
            e.preventDefault();
            if (canMovePiece(tetrisGame.pieceX + 1, tetrisGame.pieceY, tetrisGame.currentPiece.shape)) {
                tetrisGame.pieceX++;
            }
            break;
        case 'ArrowDown':
            e.preventDefault();
            tetrisGame.softDrop = true;
            if (canMovePiece(tetrisGame.pieceX, tetrisGame.pieceY + 1, tetrisGame.currentPiece.shape)) {
                tetrisGame.pieceY++;
                tetrisGame.score += 1;
            }
            break;
        case 'ArrowUp':
            e.preventDefault();
            rotatePiece();
            break;
        case ' ':
            e.preventDefault();
            hardDrop();
            break;
        case 'c':
        case 'C':
            e.preventDefault();
            holdPiece();
            break;
        case 'p':
        case 'P':
            e.preventDefault();
            pauseGame();
            break;
    }
    
    renderTetrisGame();
}

function hideTetrisOverlay() {
    document.getElementById('tetrisOverlay').classList.add('hidden');
    document.getElementById('gameOverOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadTetrisGame();
});