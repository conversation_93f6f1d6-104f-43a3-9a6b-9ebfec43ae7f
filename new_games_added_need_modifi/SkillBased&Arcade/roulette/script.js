// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadRouletteGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Betting Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                            <h4 class="text-xl font-bold mb-4 text-red-400">CYBER ROULETTE</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="rouletteBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET TYPE</label>
                                <select id="rouletteBetType" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="red">Red (1:1)</option>
                                    <option value="black">Black (1:1)</option>
                                    <option value="odd">Odd (1:1)</option>
                                    <option value="even">Even (1:1)</option>
                                    <option value="low">Low 1-18 (1:1)</option>
                                    <option value="high">High 19-36 (1:1)</option>
                                    <option value="dozen1">1st Dozen (2:1)</option>
                                    <option value="dozen2">2nd Dozen (2:1)</option>
                                    <option value="dozen3">3rd Dozen (2:1)</option>
                                    <option value="straight">Straight Up (35:1)</option>
                                </select>
                            </div>
                            
                            <div id="straightNumberSelect" class="mb-4 hidden">
                                <label class="block text-sm mb-2 text-gray-300">SELECT NUMBER (0-36)</label>
                                <input type="number" id="rouletteNumber" min="0" max="36" value="0" 
                                       class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <button id="spinRoulette" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                SPIN WHEEL
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Potential Win</div>
                                <div id="roulettePotentialWin" class="text-xl font-bold text-green-400">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Last Number</div>
                                <div id="rouletteLastNumber" class="text-xl font-bold text-red-400">-</div>
                            </div>
                        </div>
                        
                        <!-- Recent Numbers -->
                        <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-red-400">RECENT NUMBERS</h5>
                            <div id="rouletteHistory" class="flex flex-wrap gap-2">
                                <!-- Recent numbers will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Roulette Wheel -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                            <div id="rouletteWheel" class="relative bg-black/50 rounded-full w-80 h-80 mx-auto">
                                <canvas id="rouletteCanvas" width="320" height="320" class="w-full h-full rounded-full"></canvas>
                                
                                <!-- Ball -->
                                <div id="rouletteBall" class="absolute w-4 h-4 bg-white rounded-full border-2 border-yellow-400 neon-border hidden"
                                     style="top: 10px; left: 50%; transform: translateX(-50%);"></div>
                                
                                <!-- Center -->
                                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-gradient-to-br from-red-500 to-black rounded-full border-4 border-red-400 neon-border flex items-center justify-center">
                                    <div class="text-white font-bold text-xs">CYBER</div>
                                </div>
                            </div>
                            <div id="rouletteStatus" class="text-center mt-4 text-lg font-semibold">Place your bet and spin the wheel</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeRoulette();
        }
        
        let rouletteGame = {
            canvas: null,
            ctx: null,
            isSpinning: false,
            wheelRotation: 0,
            ballAngle: 0,
            ballSpeed: 0,
            numbers: [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26],
            redNumbers: [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36],
            animationId: null
        };
        
        function initializeRoulette() {
            rouletteGame.canvas = document.getElementById('rouletteCanvas');
            rouletteGame.ctx = rouletteGame.canvas.getContext('2d');
            
            drawRouletteWheel();
            
            document.getElementById('spinRoulette').addEventListener('click', spinRouletteWheel);
            document.getElementById('rouletteBetType').addEventListener('change', updateRouletteBetType);
            document.getElementById('rouletteBet').addEventListener('input', updateRoulettePotentialWin);
            
            updateRouletteBetType();
            updateRoulettePotentialWin();
        }
        
        function drawRouletteWheel() {
            const ctx = rouletteGame.ctx;
            const canvas = rouletteGame.canvas;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = canvas.width / 2 - 10;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw wheel segments
            const segmentAngle = (2 * Math.PI) / rouletteGame.numbers.length;
            
            rouletteGame.numbers.forEach((number, index) => {
                const startAngle = (index * segmentAngle) + rouletteGame.wheelRotation;
                const endAngle = ((index + 1) * segmentAngle) + rouletteGame.wheelRotation;
                
                // Determine color
                let color = '#000'; // Black for 0
                if (number !== 0) {
                    color = rouletteGame.redNumbers.includes(number) ? '#dc2626' : '#000';
                }
                
                // Draw segment
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.arc(centerX, centerY, radius, startAngle, endAngle);
                ctx.closePath();
                ctx.fillStyle = color;
                ctx.fill();
                
                // Draw border
                ctx.strokeStyle = '#9945ff';
                ctx.lineWidth = 1;
                ctx.stroke();
                
                // Draw number
                const textAngle = startAngle + segmentAngle / 2;
                const textRadius = radius * 0.8;
                const textX = centerX + Math.cos(textAngle) * textRadius;
                const textY = centerY + Math.sin(textAngle) * textRadius;
                
                ctx.save();
                ctx.translate(textX, textY);
                ctx.rotate(textAngle + Math.PI / 2);
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 12px monospace';
                ctx.textAlign = 'center';
                ctx.fillText(number.toString(), 0, 0);
                ctx.restore();
            });
            
            // Draw outer ring
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.strokeStyle = '#ff2d92';
            ctx.lineWidth = 3;
            ctx.stroke();
        }
        
        function updateRouletteBetType() {
            const betType = document.getElementById('rouletteBetType').value;
            const straightSelect = document.getElementById('straightNumberSelect');
            
            if (betType === 'straight') {
                straightSelect.classList.remove('hidden');
            } else {
                straightSelect.classList.add('hidden');
            }
            
            updateRoulettePotentialWin();
        }
        
        function updateRoulettePotentialWin() {
            const betAmount = parseInt(document.getElementById('rouletteBet').value) || 0;
            const betType = document.getElementById('rouletteBetType').value;
            
            const payouts = {
                'red': 2, 'black': 2, 'odd': 2, 'even': 2, 'low': 2, 'high': 2,
                'dozen1': 3, 'dozen2': 3, 'dozen3': 3, 'straight': 36
            };
            
            const potentialWin = betAmount * payouts[betType];
            document.getElementById('roulettePotentialWin').textContent = '$' + potentialWin;
        }
        
        function spinRouletteWheel() {
            const betAmount = parseInt(document.getElementById('rouletteBet').value);
            
            if (betAmount > balance || rouletteGame.isSpinning) {
                if (betAmount > balance) alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            rouletteGame.isSpinning = true;
            
            // Update UI
            document.getElementById('spinRoulette').disabled = true;
            document.getElementById('rouletteStatus').textContent = 'Spinning the wheel...';
            
            // Show ball
            const ball = document.getElementById('rouletteBall');
            ball.classList.remove('hidden');
            
            // Start animation
            rouletteGame.ballSpeed = 0.3;
            rouletteGame.ballAngle = 0;
            
            animateRouletteWheel();
        }
        
        function animateRouletteWheel() {
            // Slow down ball
            rouletteGame.ballSpeed *= 0.995;
            rouletteGame.ballAngle += rouletteGame.ballSpeed;
            
            // Rotate wheel (opposite direction)
            rouletteGame.wheelRotation += 0.02;
            
            // Update ball position
            const ball = document.getElementById('rouletteBall');
            const wheelRadius = 140;
            const ballX = 160 + Math.cos(rouletteGame.ballAngle) * wheelRadius;
            const ballY = 160 + Math.sin(rouletteGame.ballAngle) * wheelRadius;
            
            ball.style.left = ballX + 'px';
            ball.style.top = ballY + 'px';
            
            drawRouletteWheel();
            
            // Check if ball should stop
            if (rouletteGame.ballSpeed < 0.001) {
                // Determine winning number
                const segmentAngle = (2 * Math.PI) / rouletteGame.numbers.length;
                const ballRelativeAngle = (rouletteGame.ballAngle - rouletteGame.wheelRotation) % (2 * Math.PI);
                const segmentIndex = Math.floor(ballRelativeAngle / segmentAngle);
                const winningNumber = rouletteGame.numbers[segmentIndex] || 0;
                
                endRouletteGame(winningNumber);
            } else {
                rouletteGame.animationId = requestAnimationFrame(animateRouletteWheel);
            }
        }
        
        function endRouletteGame(winningNumber) {
            rouletteGame.isSpinning = false;
            
            // Hide ball
            document.getElementById('rouletteBall').classList.add('hidden');
            
            // Update last number
            document.getElementById('rouletteLastNumber').textContent = winningNumber;
            
            // Add to history
            addRouletteHistory(winningNumber);
            
            // Check win
            const betType = document.getElementById('rouletteBetType').value;
            const betAmount = parseInt(document.getElementById('rouletteBet').value);
            let won = false;
            
            switch(betType) {
                case 'red':
                    won = rouletteGame.redNumbers.includes(winningNumber);
                    break;
                case 'black':
                    won = winningNumber !== 0 && !rouletteGame.redNumbers.includes(winningNumber);
                    break;
                case 'odd':
                    won = winningNumber !== 0 && winningNumber % 2 === 1;
                    break;
                case 'even':
                    won = winningNumber !== 0 && winningNumber % 2 === 0;
                    break;
                case 'low':
                    won = winningNumber >= 1 && winningNumber <= 18;
                    break;
                case 'high':
                    won = winningNumber >= 19 && winningNumber <= 36;
                    break;
                case 'dozen1':
                    won = winningNumber >= 1 && winningNumber <= 12;
                    break;
                case 'dozen2':
                    won = winningNumber >= 13 && winningNumber <= 24;
                    break;
                case 'dozen3':
                    won = winningNumber >= 25 && winningNumber <= 36;
                    break;
                case 'straight':
                    const selectedNumber = parseInt(document.getElementById('rouletteNumber').value);
                    won = winningNumber === selectedNumber;
                    break;
            }
            
            // Calculate winnings
            if (won) {
                const payouts = {
                    'red': 2, 'black': 2, 'odd': 2, 'even': 2, 'low': 2, 'high': 2,
                    'dozen1': 3, 'dozen2': 3, 'dozen3': 3, 'straight': 36
                };
                const winnings = betAmount * payouts[betType];
                balance += winnings;
                updateBalance();
                
                document.getElementById('rouletteStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Winner! Number ${winningNumber} - Won $${winnings}</span>`;
            } else {
                document.getElementById('rouletteStatus').innerHTML = 
                    `<span class="text-red-400">Number ${winningNumber} - House wins!</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                document.getElementById('spinRoulette').disabled = false;
                document.getElementById('rouletteStatus').textContent = 'Place your bet and spin the wheel';
            }, 3000);
        }
        
        function addRouletteHistory(number) {
            const history = document.getElementById('rouletteHistory');
            const numberEl = document.createElement('div');
            
            let bgColor = 'bg-green-600'; // For 0
            if (number !== 0) {
                bgColor = rouletteGame.redNumbers.includes(number) ? 'bg-red-600' : 'bg-gray-800';
            }
            
            numberEl.className = `w-8 h-8 ${bgColor} rounded-full flex items-center justify-center text-white text-sm font-bold border border-white`;
            numberEl.textContent = number;
            
            history.insertBefore(numberEl, history.firstChild);
            
            // Keep only last 10 numbers
            while (history.children.length > 10) {
                history.removeChild(history.lastChild);
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRouletteGame();
});