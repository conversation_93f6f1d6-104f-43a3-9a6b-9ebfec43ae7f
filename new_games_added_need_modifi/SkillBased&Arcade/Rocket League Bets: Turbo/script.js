// Game state
let balance = 1000;

// Rocket League Bets: Turbo game state
let rocketLeagueGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'rookie',
    gameMode: 'standard',
    matchType: 'ranked',
    
    // Match mechanics
    currentMatch: null,
    playerTeam: 'blue',
    opponentTeam: 'orange',
    playerScore: 0,
    opponentScore: 0,
    matchTime: 300, // 5 minutes
    timeLeft: 300,
    
    // Turbo features
    turboMeter: 0,
    turboBoosts: 0,
    aerialHits: 0,
    saves: 0,
    goals: 0,
    assists: 0,
    demolitions: 0,
    epicSaves: 0,
    
    // Performance tracking
    ballTouches: 0,
    centerBalls: 0,
    clearBalls: 0,
    shotAccuracy: 0,
    boostEfficiency: 0,
    positioning: 0,
    teamwork: 0,
    consistency: 0,
    
    // Advanced metrics
    aerialAccuracy: 0,
    powerShotGoals: 0,
    redirectGoals: 0,
    longShotGoals: 0,
    overtimeGoals: 0,
    hatTricks: 0,
    perfectMatches: 0,
    comebackWins: 0,
    
    // Skill categories
    mechanicalSkill: 0,
    gameIQ: 0,
    rotationSkill: 0,
    aerialControl: 0,
    boostManagement: 0,
    defensiveSkill: 0,
    
    // Match history
    matchesPlayed: 0,
    matchesWon: 0,
    totalGoals: 0,
    totalSaves: 0,
    totalAssists: 0,
    winStreak: 0,
    maxWinStreak: 0
};

// Ultra-strict difficulty settings with sub-7% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'rookie',
        description: 'Bronze League (7% win rate)',
        winRate: 0.07,
        multiplier: 0.8,
        opponentSkill: 0.3,
        matchDuration: 300,
        requiredScore: 3,
        turboRequirement: 50
    },
    {
        name: 'challenger',
        description: 'Silver League (5% win rate)',
        winRate: 0.05,
        multiplier: 1.2,
        opponentSkill: 0.5,
        matchDuration: 300,
        requiredScore: 4,
        turboRequirement: 75
    },
    {
        name: 'rising_star',
        description: 'Gold League (4% win rate)',
        winRate: 0.04,
        multiplier: 1.6,
        opponentSkill: 0.7,
        matchDuration: 300,
        requiredScore: 5,
        turboRequirement: 100
    },
    {
        name: 'champion',
        description: 'Platinum League (3% win rate)',
        winRate: 0.03,
        multiplier: 2.0,
        opponentSkill: 0.85,
        matchDuration: 300,
        requiredScore: 6,
        turboRequirement: 125
    },
    {
        name: 'grand_champion',
        description: 'Diamond League (2% win rate)',
        winRate: 0.02,
        multiplier: 2.5,
        opponentSkill: 0.95,
        matchDuration: 300,
        requiredScore: 7,
        turboRequirement: 150
    }
];

const GAME_MODES = [
    {
        name: 'standard',
        description: 'Standard 3v3 Match',
        scoreMultiplier: 1.0,
        difficultyBonus: 1.0,
        specialRequirements: false
    },
    {
        name: 'doubles',
        description: '2v2 Doubles Match',
        scoreMultiplier: 1.2,
        difficultyBonus: 1.1,
        specialRequirements: true
    },
    {
        name: 'solo_duel',
        description: '1v1 Solo Duel',
        scoreMultiplier: 1.5,
        difficultyBonus: 1.3,
        specialRequirements: true
    },
    {
        name: 'chaos',
        description: '4v4 Chaos Mode',
        scoreMultiplier: 1.8,
        difficultyBonus: 1.5,
        specialRequirements: true
    }
];

const MATCH_TYPES = [
    {
        name: 'ranked',
        description: 'Competitive Ranked',
        multiplier: 1.0,
        pressure: 1.0
    },
    {
        name: 'tournament',
        description: 'Tournament Mode',
        multiplier: 1.4,
        pressure: 1.3
    },
    {
        name: 'championship',
        description: 'Championship Finals',
        multiplier: 1.8,
        pressure: 1.6
    },
    {
        name: 'world_series',
        description: 'World Series',
        multiplier: 2.2,
        pressure: 2.0
    }
];

const TURBO_ACTIONS = [
    { name: 'Aerial Hit', points: 10, difficulty: 0.3, description: 'Hit ball while airborne' },
    { name: 'Epic Save', points: 25, difficulty: 0.6, description: 'Last-second goal save' },
    { name: 'Power Shot', points: 15, difficulty: 0.4, description: 'High-speed shot on goal' },
    { name: 'Redirect Goal', points: 30, difficulty: 0.7, description: 'Score off teammate pass' },
    { name: 'Demolition', points: 20, difficulty: 0.5, description: 'Demolish opponent' },
    { name: 'Center Ball', points: 12, difficulty: 0.35, description: 'Center ball for teammate' },
    { name: 'Clear Ball', points: 8, difficulty: 0.25, description: 'Clear ball from danger' },
    { name: 'Long Shot', points: 35, difficulty: 0.8, description: 'Score from own half' },
    { name: 'Bicycle Kick', points: 40, difficulty: 0.85, description: 'Backwards aerial goal' },
    { name: 'Double Touch', points: 45, difficulty: 0.9, description: 'Air dribble to goal' }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadRocketLeagueGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Rocket League Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">🚀 ROCKET LEAGUE BETS: TURBO 🚀</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 ROCKET BET</label>
                        <input type="number" id="rocketBet" value="100" min="50" max="${balance}" 
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateRocketPayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🏆 LEAGUE</label>
                            <select id="rocketDifficulty" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateRocketSettings()">
                                <option value="rookie">Bronze (7%)</option>
                                <option value="challenger" selected>Silver (5%)</option>
                                <option value="rising_star">Gold (4%)</option>
                                <option value="champion">Platinum (3%)</option>
                                <option value="grand_champion">Diamond (2%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">⚽ MODE</label>
                            <select id="rocketMode" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateRocketSettings()">
                                <option value="standard" selected>3v3 Standard</option>
                                <option value="doubles">2v2 Doubles</option>
                                <option value="solo_duel">1v1 Solo Duel</option>
                                <option value="chaos">4v4 Chaos</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏅 MATCH TYPE</label>
                        <select id="rocketMatchType" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white"
                                onchange="updateRocketSettings()">
                            <option value="ranked" selected>Ranked Match</option>
                            <option value="tournament">Tournament</option>
                            <option value="championship">Championship</option>
                            <option value="world_series">World Series</option>
                        </select>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="rocketWinRate">5%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Required Score:</div>
                                <div class="text-yellow-400 font-bold" id="requiredScore">4 Goals</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Turbo Needed:</div>
                                <div class="text-blue-400 font-bold" id="turboRequired">75 Points</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Match Time:</div>
                                <div class="text-purple-400 font-bold" id="matchDuration">5:00</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="rocketPotentialPayout">1,200 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startRocketLeagueMatch()" 
                            class="w-full cyber-button bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        🚀 START ROCKET MATCH 🚀
                    </button>
                </div>

                <!-- Match Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">📊 MATCH STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Score:</span>
                            <span class="text-white" id="currentScore">0 - 0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Time:</span>
                            <span class="text-blue-400" id="matchTimeLeft">5:00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Turbo:</span>
                            <span class="text-yellow-400" id="turboMeter">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Goals:</span>
                            <span class="text-green-400" id="playerGoals">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Saves:</span>
                            <span class="text-purple-400" id="playerSaves">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Assists:</span>
                            <span class="text-pink-400" id="playerAssists">0</span>
                        </div>
                    </div>
                </div>

                <!-- Turbo Actions -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">⚡ TURBO ACTIONS</h5>
                    <div class="grid grid-cols-2 gap-2" id="turboActions">
                        <!-- Turbo action buttons will be generated here -->
                    </div>
                </div>
            </div>

            <!-- Match Arena -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-orange-400" id="matchTitle">Rocket League Arena</h5>
                        <p class="text-sm text-gray-400" id="matchDescription">Score goals and perform turbo actions to win</p>
                    </div>
                    
                    <!-- Arena Display -->
                    <div class="relative bg-green-900/30 rounded-lg p-4 mb-4 min-h-[300px] border-2 border-white/20">
                        <!-- Goal Posts -->
                        <div class="absolute left-0 top-1/2 transform -translate-y-1/2 w-2 h-16 bg-blue-500 rounded"></div>
                        <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-2 h-16 bg-orange-500 rounded"></div>
                        
                        <!-- Center Circle -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 border-2 border-white/30 rounded-full"></div>
                        
                        <!-- Ball -->
                        <div id="ballPosition" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white rounded-full shadow-lg transition-all duration-500"></div>
                        
                        <!-- Player Car -->
                        <div id="playerCar" class="absolute bottom-4 left-1/4 w-8 h-4 bg-blue-500 rounded transition-all duration-300"></div>
                        
                        <!-- Opponent Cars -->
                        <div id="opponentCar1" class="absolute top-4 right-1/4 w-8 h-4 bg-orange-500 rounded transition-all duration-300"></div>
                        <div id="opponentCar2" class="absolute top-1/3 right-1/3 w-8 h-4 bg-orange-500 rounded transition-all duration-300"></div>
                        
                        <!-- Boost Pads -->
                        <div class="absolute top-1/4 left-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                        <div class="absolute top-1/4 right-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-1/4 left-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-1/4 right-1/4 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                        
                        <!-- Score Display -->
                        <div class="absolute top-2 left-1/2 transform -translate-x-1/2 bg-black/70 px-4 py-2 rounded">
                            <span class="text-blue-400 font-bold" id="blueScore">0</span>
                            <span class="text-white mx-2">-</span>
                            <span class="text-orange-400 font-bold" id="orangeScore">0</span>
                        </div>
                        
                        <!-- Time Display -->
                        <div class="absolute top-2 right-2 bg-black/70 px-3 py-1 rounded">
                            <span class="text-white font-bold" id="arenaTimeLeft">5:00</span>
                        </div>
                        
                        <!-- Turbo Meter -->
                        <div class="absolute bottom-2 left-2 bg-black/70 px-3 py-1 rounded">
                            <div class="text-xs text-gray-400 mb-1">TURBO</div>
                            <div class="w-20 h-2 bg-gray-700 rounded">
                                <div id="turboBar" class="h-full bg-gradient-to-r from-yellow-400 to-orange-500 rounded transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Match Events -->
                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <h6 class="text-sm font-bold text-gray-400 mb-2">MATCH EVENTS</h6>
                        <div id="matchEvents" class="text-xs space-y-1 max-h-24 overflow-y-auto">
                            <div class="text-gray-500">Match starting...</div>
                        </div>
                    </div>

                    <!-- Match Result -->
                    <div class="text-center">
                        <div id="matchResult" class="text-xl font-bold mb-2"></div>
                        <div id="matchWinAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Match History -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">🏆 RECENT MATCHES</h5>
                    <div id="matchHistory" class="space-y-1 text-sm max-h-32 overflow-y-auto">
                        <div class="text-gray-500 text-center">No matches played yet</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Overlay -->
        <div id="gameOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-orange-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-orange-400">🚀 MATCH COMPLETE 🚀</h3>
                <div id="finalMatchResult" class="text-xl mb-4"></div>
                <div id="finalMatchStats" class="text-sm text-gray-400 mb-6"></div>
                <button onclick="hideGameOverlay()" 
                        class="cyber-button bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-6 rounded-lg">
                    BACK TO ARENA
                </button>
            </div>
        </div>
    `;

    updateRocketSettings();
    generateTurboActions();
    initializeArena();
}

function updateRocketSettings() {
    const difficulty = document.getElementById('rocketDifficulty').value;
    const mode = document.getElementById('rocketMode').value;
    const matchType = document.getElementById('rocketMatchType').value;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    const modeData = GAME_MODES.find(m => m.name === mode);
    const typeData = MATCH_TYPES.find(t => t.name === matchType);
    
    rocketLeagueGame.difficulty = difficulty;
    rocketLeagueGame.gameMode = mode;
    rocketLeagueGame.matchType = matchType;
    
    document.getElementById('rocketWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('requiredScore').textContent = diffData.requiredScore + ' Goals';
    document.getElementById('turboRequired').textContent = diffData.turboRequirement + ' Points';
    document.getElementById('matchDuration').textContent = Math.floor(diffData.matchDuration / 60) + ':00';
    
    updateRocketPayout();
}

function updateRocketPayout() {
    const betAmount = parseInt(document.getElementById('rocketBet').value) || 100;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === rocketLeagueGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === rocketLeagueGame.gameMode);
    const typeData = MATCH_TYPES.find(t => t.name === rocketLeagueGame.matchType);
    
    const totalMultiplier = diffData.multiplier * modeData.scoreMultiplier * typeData.multiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 12); // Conservative multiplier
    
    document.getElementById('rocketPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function generateTurboActions() {
    const container = document.getElementById('turboActions');
    container.innerHTML = '';
    
    TURBO_ACTIONS.slice(0, 6).forEach(action => {
        const button = document.createElement('button');
        button.className = 'cyber-button bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white text-xs py-2 px-3 rounded transition-all duration-300';
        button.innerHTML = `${action.name}<br><span class="text-xs">+${action.points}</span>`;
        button.onclick = () => attemptTurboAction(action);
        container.appendChild(button);
    });
}

function startRocketLeagueMatch() {
    const betAmount = parseInt(document.getElementById('rocketBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === rocketLeagueGame.difficulty);
    
    // Reset game state
    rocketLeagueGame.isPlaying = true;
    rocketLeagueGame.gamePhase = 'playing';
    rocketLeagueGame.betAmount = betAmount;
    rocketLeagueGame.timeLeft = diffData.matchDuration;
    rocketLeagueGame.playerScore = 0;
    rocketLeagueGame.opponentScore = 0;
    
    // Reset performance tracking
    rocketLeagueGame.turboMeter = 0;
    rocketLeagueGame.turboBoosts = 0;
    rocketLeagueGame.aerialHits = 0;
    rocketLeagueGame.saves = 0;
    rocketLeagueGame.goals = 0;
    rocketLeagueGame.assists = 0;
    rocketLeagueGame.demolitions = 0;
    rocketLeagueGame.epicSaves = 0;
    
    rocketLeagueGame.ballTouches = 0;
    rocketLeagueGame.centerBalls = 0;
    rocketLeagueGame.clearBalls = 0;
    rocketLeagueGame.shotAccuracy = 0;
    rocketLeagueGame.boostEfficiency = 0;
    rocketLeagueGame.positioning = 0;
    rocketLeagueGame.teamwork = 0;
    rocketLeagueGame.consistency = 0;
    
    rocketLeagueGame.aerialAccuracy = 0;
    rocketLeagueGame.powerShotGoals = 0;
    rocketLeagueGame.redirectGoals = 0;
    rocketLeagueGame.longShotGoals = 0;
    rocketLeagueGame.overtimeGoals = 0;
    
    hideGameOverlay();
    addMatchEvent('🚀 Match started! Good luck!');
    updateMatchDisplay();
    
    // Start match timer
    rocketLeagueGame.matchTimer = setInterval(() => {
        rocketLeagueGame.timeLeft--;
        updateMatchDisplay();
        
        // Simulate opponent actions
        if (Math.random() < diffData.opponentSkill * 0.1) {
            simulateOpponentAction();
        }
        
        if (rocketLeagueGame.timeLeft <= 0) {
            endRocketLeagueMatch();
        }
    }, 1000);
}

function attemptTurboAction(action) {
    if (!rocketLeagueGame.isPlaying) return;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === rocketLeagueGame.difficulty);
    const successChance = (1 - action.difficulty) * (1 - diffData.opponentSkill * 0.5);
    
    if (Math.random() < successChance) {
        // Successful action
        rocketLeagueGame.turboMeter += action.points;
        rocketLeagueGame.ballTouches++;
        
        // Update specific stats
        switch (action.name) {
            case 'Aerial Hit':
                rocketLeagueGame.aerialHits++;
                break;
            case 'Epic Save':
                rocketLeagueGame.saves++;
                rocketLeagueGame.epicSaves++;
                break;
            case 'Power Shot':
                if (Math.random() < 0.3) {
                    rocketLeagueGame.goals++;
                    rocketLeagueGame.powerShotGoals++;
                    rocketLeagueGame.playerScore++;
                    addMatchEvent(`⚽ GOAL! Power shot finds the net!`);
                }
                break;
            case 'Redirect Goal':
                rocketLeagueGame.goals++;
                rocketLeagueGame.redirectGoals++;
                rocketLeagueGame.assists++;
                rocketLeagueGame.playerScore++;
                addMatchEvent(`🎯 GOAL! Perfect redirect!`);
                break;
            case 'Demolition':
                rocketLeagueGame.demolitions++;
                break;
            case 'Center Ball':
                rocketLeagueGame.centerBalls++;
                if (Math.random() < 0.2) {
                    rocketLeagueGame.assists++;
                    addMatchEvent(`🎯 Assist! Great center ball!`);
                }
                break;
            case 'Clear Ball':
                rocketLeagueGame.clearBalls++;
                break;
            case 'Long Shot':
                rocketLeagueGame.goals++;
                rocketLeagueGame.longShotGoals++;
                rocketLeagueGame.playerScore++;
                addMatchEvent(`🚀 GOAL! Incredible long shot!`);
                break;
            case 'Bicycle Kick':
                rocketLeagueGame.goals++;
                rocketLeagueGame.playerScore++;
                addMatchEvent(`🤸 GOAL! Amazing bicycle kick!`);
                break;
            case 'Double Touch':
                rocketLeagueGame.goals++;
                rocketLeagueGame.playerScore++;
                addMatchEvent(`✨ GOAL! Spectacular double touch!`);
                break;
        }
        
        addMatchEvent(`⚡ ${action.name} successful! +${action.points} turbo`);
        animateBallMovement();
    } else {
        addMatchEvent(`❌ ${action.name} failed!`);
        // Opponent might score on failed attempts
        if (Math.random() < diffData.opponentSkill * 0.15) {
            rocketLeagueGame.opponentScore++;
            addMatchEvent(`😤 Opponent scores on counter-attack!`);
        }
    }
    
    updateMatchDisplay();
}

function simulateOpponentAction() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === rocketLeagueGame.difficulty);
    
    if (Math.random() < diffData.opponentSkill * 0.3) {
        rocketLeagueGame.opponentScore++;
        addMatchEvent(`😤 Opponent scores!`);
        updateMatchDisplay();
    }
}

function animateBallMovement() {
    const ball = document.getElementById('ballPosition');
    const randomX = Math.random() * 80 + 10; // 10-90% of container width
    const randomY = Math.random() * 80 + 10; // 10-90% of container height
    
    ball.style.left = randomX + '%';
    ball.style.top = randomY + '%';
}

function updateMatchDisplay() {
    // Update scores
    document.getElementById('currentScore').textContent = `${rocketLeagueGame.playerScore} - ${rocketLeagueGame.opponentScore}`;
    document.getElementById('blueScore').textContent = rocketLeagueGame.playerScore;
    document.getElementById('orangeScore').textContent = rocketLeagueGame.opponentScore;
    
    // Update time
    const minutes = Math.floor(rocketLeagueGame.timeLeft / 60);
    const seconds = rocketLeagueGame.timeLeft % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('matchTimeLeft').textContent = timeString;
    document.getElementById('arenaTimeLeft').textContent = timeString;
    
    // Update stats
    document.getElementById('turboMeter').textContent = rocketLeagueGame.turboMeter;
    document.getElementById('playerGoals').textContent = rocketLeagueGame.goals;
    document.getElementById('playerSaves').textContent = rocketLeagueGame.saves;
    document.getElementById('playerAssists').textContent = rocketLeagueGame.assists;
    
    // Update turbo bar
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === rocketLeagueGame.difficulty);
    const turboPercentage = Math.min(100, (rocketLeagueGame.turboMeter / diffData.turboRequirement) * 100);
    document.getElementById('turboBar').style.width = turboPercentage + '%';
}

function addMatchEvent(event) {
    const container = document.getElementById('matchEvents');
    const eventEl = document.createElement('div');
    eventEl.className = 'text-white';
    eventEl.textContent = event;
    
    container.insertBefore(eventEl, container.firstChild);
    
    // Keep only last 5 events
    while (container.children.length > 5) {
        container.removeChild(container.lastChild);
    }
}

function initializeArena() {
    // Initialize arena display
    updateMatchDisplay();
}

function endRocketLeagueMatch() {
    rocketLeagueGame.isPlaying = false;
    rocketLeagueGame.gamePhase = 'finished';
    
    if (rocketLeagueGame.matchTimer) {
        clearInterval(rocketLeagueGame.matchTimer);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === rocketLeagueGame.difficulty);
    const modeData = GAME_MODES.find(m => m.name === rocketLeagueGame.gameMode);
    const typeData = MATCH_TYPES.find(t => t.name === rocketLeagueGame.matchType);
    
    // Ultra-strict winning requirements
    const baseWinChance = diffData.winRate;
    const scoreRequirement = rocketLeagueGame.playerScore >= diffData.requiredScore;
    const turboRequirement = rocketLeagueGame.turboMeter >= diffData.turboRequirement;
    const winRequirement = rocketLeagueGame.playerScore > rocketLeagueGame.opponentScore;
    
    // Additional performance requirements
    const minSaves = Math.floor(diffData.requiredScore * 0.5);
    const minAssists = Math.floor(diffData.requiredScore * 0.3);
    const minAerialHits = Math.floor(diffData.turboRequirement * 0.2);
    const minBallTouches = Math.floor(diffData.turboRequirement * 0.5);
    
    const performanceRequirements = 
        rocketLeagueGame.saves >= minSaves &&
        rocketLeagueGame.assists >= minAssists &&
        rocketLeagueGame.aerialHits >= minAerialHits &&
        rocketLeagueGame.ballTouches >= minBallTouches;
    
    // Special achievements bonus
    const specialBonus = 
        (rocketLeagueGame.epicSaves > 0 ? 0.1 : 0) +
        (rocketLeagueGame.powerShotGoals > 0 ? 0.1 : 0) +
        (rocketLeagueGame.redirectGoals > 0 ? 0.15 : 0) +
        (rocketLeagueGame.longShotGoals > 0 ? 0.2 : 0) +
        (rocketLeagueGame.demolitions > 0 ? 0.05 : 0);
    
    const meetsAllRequirements = scoreRequirement && turboRequirement && winRequirement && performanceRequirements;
    const finalWinChance = meetsAllRequirements ? baseWinChance * (1 + specialBonus) : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with bonuses
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier * typeData.multiplier;
        const scoreBonus = Math.max(0, rocketLeagueGame.playerScore - diffData.requiredScore) * 0.1;
        const turboBonus = Math.max(0, rocketLeagueGame.turboMeter - diffData.turboRequirement) * 0.005;
        const performanceBonus = (rocketLeagueGame.saves * 0.05) + (rocketLeagueGame.assists * 0.08) + (rocketLeagueGame.aerialHits * 0.03);
        
        const totalMultiplier = baseMultiplier * (1 + scoreBonus + turboBonus + performanceBonus + specialBonus);
        const winnings = Math.floor(rocketLeagueGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        rocketLeagueGame.matchesWon++;
        rocketLeagueGame.winStreak++;
        rocketLeagueGame.maxWinStreak = Math.max(rocketLeagueGame.maxWinStreak, rocketLeagueGame.winStreak);
        
        document.getElementById('matchResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🏆 VICTORY! 🏆</span>`;
        document.getElementById('matchWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        rocketLeagueGame.winStreak = 0;
        
        const requirements = [
            `Score ${diffData.requiredScore}+ goals (${rocketLeagueGame.playerScore})`,
            `Earn ${diffData.turboRequirement}+ turbo (${rocketLeagueGame.turboMeter})`,
            `Win the match (${rocketLeagueGame.playerScore > rocketLeagueGame.opponentScore ? 'YES' : 'NO'})`,
            `${minSaves}+ saves (${rocketLeagueGame.saves})`,
            `${minAssists}+ assists (${rocketLeagueGame.assists})`,
            `${minAerialHits}+ aerial hits (${rocketLeagueGame.aerialHits})`
        ];
        
        document.getElementById('matchResult').innerHTML = 
            `<span class="text-red-400">🚀 MATCH LOST 🚀</span>`;
        document.getElementById('matchWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Need: ${requirements.slice(0, 3).join(', ')}<br>
            ${requirements.slice(3, 6).join(', ')}</div>`;
    }
    
    rocketLeagueGame.matchesPlayed++;
    rocketLeagueGame.totalGoals += rocketLeagueGame.goals;
    rocketLeagueGame.totalSaves += rocketLeagueGame.saves;
    rocketLeagueGame.totalAssists += rocketLeagueGame.assists;
    
    addMatchHistory(rocketLeagueGame.playerScore, rocketLeagueGame.opponentScore, rocketLeagueGame.turboMeter, won);
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
        document.getElementById('finalMatchResult').innerHTML = 
            won ? '<span class="text-green-400">🏆 ROCKET CHAMPION! 🏆</span>' : 
                  '<span class="text-red-400">💔 MATCH DEFEAT 💔</span>';
        document.getElementById('finalMatchStats').innerHTML = 
            `Final Score: ${rocketLeagueGame.playerScore} - ${rocketLeagueGame.opponentScore}<br>
             Goals: ${rocketLeagueGame.goals} | Saves: ${rocketLeagueGame.saves} | Assists: ${rocketLeagueGame.assists}<br>
             Turbo Points: ${rocketLeagueGame.turboMeter} | Aerial Hits: ${rocketLeagueGame.aerialHits}`;
    }, 3000);
}

function addMatchHistory(playerScore, opponentScore, turbo, won) {
    const history = document.getElementById('matchHistory');
    const result = document.createElement('div');
    result.className = `flex justify-between py-1 ${won ? 'text-green-400' : 'text-red-400'}`;
    result.innerHTML = `
        <span>${playerScore}-${opponentScore} (${turbo} turbo)</span>
        <span class="text-xs">${won ? 'WIN' : 'LOSS'}</span>
    `;
    
    history.insertBefore(result, history.firstChild);
    
    // Keep only last 8 results
    while (history.children.length > 8) {
        history.removeChild(history.lastChild);
    }
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRocketLeagueGame();
});