// Game state
let balance = 1000;

// Slingo Showdown: Fusion game state
let fusionGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'fusion',
    gameMode: 'hybrid',
    fusionLevel: 1,
    
    // Fusion Slingo mechanics
    slingoCard: [],
    markedNumbers: [],
    calledNumbers: [],
    currentNumber: null,
    numbersLeft: 75,
    ballsDrawn: 0,
    maxBalls: 25,
    
    // Fusion features
    fusionPowers: [],
    energyChain: 1.0,
    fusionCombos: 0,
    perfectFusions: 0,
    chainCount: 0,
    maxChain: 0,
    
    // Hybrid scoring
    slingoLines: 0,
    diagonalLines: 0,
    fusionPatterns: 0,
    megaFusion: false,
    ultraFusion: false,
    cosmicFusion: false,
    
    // Performance tracking
    totalFusions: 0,
    fusionStreak: 0,
    maxFusionStreak: 0,
    averageFusions: 0,
    fusionRating: 0,
    
    // Fusion special features
    elementalBalls: 0,
    fusionBalls: 0,
    cosmicBalls: 0,
    voidBalls: 0,
    quantumSpins: 0,
    
    // Enhanced timing mechanics
    fusionTimes: [],
    fusionAccuracy: 0,
    speedFusion: 0,
    rhythmRating: 0,
    
    // Progressive features
    fusionEnergy: 0,
    fusionRank: 'Fusion Initiate',
    unlockedElements: ['fire', 'water'],
    fusionAchievements: []
};

// Fusion difficulty settings
const FUSION_LEVELS = [
    {
        name: 'fusion',
        description: 'Fusion Initiate (8% win rate)',
        winRate: 0.08,
        multiplier: 1.5,
        ballCount: 25,
        requiredFusions: 2,
        timeLimit: 200,
        elementalChance: 0.12,
        fusionChance: 0.08
    },
    {
        name: 'advanced',
        description: 'Advanced Fusion (6% win rate)',
        winRate: 0.06,
        multiplier: 2.0,
        ballCount: 22,
        requiredFusions: 3,
        timeLimit: 180,
        elementalChance: 0.10,
        fusionChance: 0.06
    },
    {
        name: 'master',
        description: 'Fusion Master (4% win rate)',
        winRate: 0.04,
        multiplier: 2.8,
        ballCount: 20,
        requiredFusions: 4,
        timeLimit: 160,
        elementalChance: 0.08,
        fusionChance: 0.05
    },
    {
        name: 'cosmic',
        description: 'Cosmic Fusion (3% win rate)',
        winRate: 0.03,
        multiplier: 3.5,
        ballCount: 18,
        requiredFusions: 5,
        timeLimit: 140,
        elementalChance: 0.06,
        fusionChance: 0.04
    },
    {
        name: 'quantum',
        description: 'Quantum Fusion (2% win rate)',
        winRate: 0.02,
        multiplier: 4.5,
        ballCount: 16,
        requiredFusions: 6,
        timeLimit: 120,
        elementalChance: 0.05,
        fusionChance: 0.03
    },
    {
        name: 'transcendent',
        description: 'Transcendent Fusion (1% win rate)',
        winRate: 0.01,
        multiplier: 6.0,
        ballCount: 14,
        requiredFusions: 7,
        timeLimit: 100,
        elementalChance: 0.04,
        fusionChance: 0.02
    }
];

const FUSION_MODES = [
    {
        name: 'hybrid',
        description: 'Hybrid Fusion',
        cardSize: 5,
        paylineMultiplier: 1.0,
        fusionFeatures: true
    },
    {
        name: 'elemental',
        description: 'Elemental Fusion',
        cardSize: 5,
        paylineMultiplier: 1.6,
        fusionFeatures: true
    },
    {
        name: 'cosmic',
        description: 'Cosmic Fusion',
        cardSize: 5,
        paylineMultiplier: 2.2,
        fusionFeatures: true
    },
    {
        name: 'quantum',
        description: 'Quantum Fusion',
        cardSize: 5,
        paylineMultiplier: 2.8,
        fusionFeatures: true
    },
    {
        name: 'transcendent',
        description: 'Transcendent Fusion',
        cardSize: 5,
        paylineMultiplier: 3.5,
        fusionFeatures: true
    }
];

// Enhanced elemental balls for Fusion
const ELEMENTAL_BALLS = [
    { type: 'fire', symbol: '🔥', description: 'Fire - Burns adjacent numbers', chance: 0.12, color: '#ff4500', element: 'fire' },
    { type: 'water', symbol: '💧', description: 'Water - Flows to connected numbers', chance: 0.12, color: '#0080ff', element: 'water' },
    { type: 'earth', symbol: '🌍', description: 'Earth - Marks entire row', chance: 0.10, color: '#8b4513', element: 'earth' },
    { type: 'air', symbol: '💨', description: 'Air - Marks diagonal pattern', chance: 0.10, color: '#87ceeb', element: 'air' },
    { type: 'lightning', symbol: '⚡', description: 'Lightning - Random fusion marks', chance: 0.08, color: '#ffff00', element: 'lightning' },
    { type: 'ice', symbol: '❄️', description: 'Ice - Freezes time for bonus', chance: 0.06, color: '#00ffff', element: 'ice' },
    { type: 'void', symbol: '🌌', description: 'Void - Removes and redistributes', chance: 0.04, color: '#4b0082', element: 'void' },
    { type: 'fusion', symbol: '⚛️', description: 'Fusion - Combines all elements', chance: 0.02, color: '#ff69b4', element: 'fusion' }
];

// Fusion patterns
const FUSION_PATTERNS = [
    { name: 'Elemental Line', description: 'Any horizontal fusion line', multiplier: 1.5, pattern: 'elemental_line' },
    { name: 'Fusion Cross', description: 'Cross pattern fusion', multiplier: 2.0, pattern: 'fusion_cross' },
    { name: 'Elemental Spiral', description: 'Spiral fusion pattern', multiplier: 2.5, pattern: 'elemental_spiral' },
    { name: 'Cosmic Diamond', description: 'Diamond fusion pattern', multiplier: 3.0, pattern: 'cosmic_diamond' },
    { name: 'Quantum Grid', description: 'Grid fusion pattern', multiplier: 3.5, pattern: 'quantum_grid' },
    { name: 'Fusion Mandala', description: 'Mandala fusion pattern', multiplier: 4.0, pattern: 'fusion_mandala' },
    { name: 'Elemental Storm', description: 'Storm fusion pattern', multiplier: 5.0, pattern: 'elemental_storm' },
    { name: 'Cosmic Fusion', description: 'Ultimate cosmic pattern', multiplier: 6.0, pattern: 'cosmic_fusion' },
    { name: 'Transcendent Unity', description: 'Perfect fusion harmony', multiplier: 8.0, pattern: 'transcendent_unity' }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadSlingoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Fusion Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">⚛️ SLINGO SHOWDOWN: FUSION ⚛️</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 FUSION BET</label>
                        <input type="number" id="fusionBet" value="100" min="50" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateFusionPayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">⚛️ FUSION LEVEL</label>
                            <select id="fusionDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateFusionSettings()">
                                <option value="fusion" selected>Fusion Initiate (8%)</option>
                                <option value="advanced">Advanced Fusion (6%)</option>
                                <option value="master">Fusion Master (4%)</option>
                                <option value="cosmic">Cosmic Fusion (3%)</option>
                                <option value="quantum">Quantum Fusion (2%)</option>
                                <option value="transcendent">Transcendent (1%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🌟 FUSION MODE</label>
                            <select id="fusionMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateFusionSettings()">
                                <option value="hybrid" selected>Hybrid Fusion</option>
                                <option value="elemental">Elemental Fusion</option>
                                <option value="cosmic">Cosmic Fusion</option>
                                <option value="quantum">Quantum Fusion</option>
                                <option value="transcendent">Transcendent Fusion</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="fusionWinRate">8%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Required Fusions:</div>
                                <div class="text-yellow-400 font-bold" id="requiredFusions">2</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Max Balls:</div>
                                <div class="text-blue-400 font-bold" id="maxFusionBalls">25</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Limit:</div>
                                <div class="text-purple-400 font-bold" id="fusionTimeLimit">200s</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="fusionPotentialPayout">2000 GA</div>
                        <div class="text-sm text-gray-400">Potential Fusion Payout</div>
                    </div>

                    <button onclick="startFusionGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        ⚛️ START FUSION ⚛️
                    </button>

                    <div class="grid grid-cols-2 gap-2">
                        <button onclick="fuseMark()" id="fuseBtn"
                                class="cyber-button bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50"
                                disabled>
                            ⚛️ FUSE (Space)
                        </button>
                        <button onclick="skipFusion()" id="skipFusionBtn"
                                class="cyber-button bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded-lg disabled:opacity-50"
                                disabled>
                            ⏭️ SKIP (S)
                        </button>
                    </div>
                </div>

                <!-- Enhanced Fusion Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚡ FUSION PERFORMANCE</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fusion Lines:</span>
                            <span class="text-green-400" id="fusionLines">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fusion Patterns:</span>
                            <span class="text-cyan-400" id="fusionPatterns">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Fusions:</span>
                            <span class="text-yellow-400" id="perfectFusions">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fusion Chain:</span>
                            <span class="text-pink-400" id="fusionChain">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Energy Chain:</span>
                            <span class="text-purple-400" id="energyChain">1.0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fusion Rating:</span>
                            <span class="text-orange-400" id="fusionRating">0</span>
                        </div>
                    </div>
                </div>

                <!-- Fusion Mastery -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌟 FUSION MASTERY</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Fusion Rank:</span>
                            <span class="text-yellow-400" id="fusionRank">Fusion Initiate</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="fusionEnergyBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="fusionEnergyProgress">0 / 150 Energy</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-xs mt-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fusion Streak:</span>
                            <span class="text-green-400" id="fusionStreak">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total Fusions:</span>
                            <span class="text-blue-400" id="totalFusions">0</span>
                        </div>
                    </div>
                </div>

                <!-- Elemental Powers -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🔮 ELEMENTAL POWERS</h5>
                    <div id="activeFusionPowers" class="text-sm text-gray-400">
                        No active elemental powers
                    </div>
                </div>
            </div>

            <!-- Enhanced Fusion Game Board -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="fusionTitle">Slingo Showdown: Fusion</h5>
                        <p class="text-sm text-gray-400" id="fusionDescription">Harness elemental forces in ultimate fusion</p>
                    </div>
                    
                    <!-- Game Status -->
                    <div class="bg-black/50 p-3 rounded-lg mb-4">
                        <div class="grid grid-cols-3 gap-4 text-center text-sm">
                            <div>
                                <div class="text-gray-400">Current Element</div>
                                <div class="text-2xl font-bold text-yellow-400" id="currentElement">-</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Balls Left</div>
                                <div class="text-xl font-bold text-blue-400" id="fusionBallsLeft">25</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Left</div>
                                <div class="text-xl font-bold text-red-400" id="fusionTimeLeft">200</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Enhanced Fusion Card -->
                    <div class="relative bg-gradient-to-b from-purple-900/50 to-black/50 rounded-lg p-4 mb-4 border-2 border-purple-500/30">
                        <div class="grid grid-cols-5 gap-1 mb-4" id="fusionCard">
                            <!-- Fusion card will be generated here -->
                        </div>
                        
                        <!-- Elemental Indicators -->
                        <div class="grid grid-cols-5 gap-1 text-xs text-center">
                            <div class="text-gray-400">F</div>
                            <div class="text-gray-400">U</div>
                            <div class="text-gray-400">S</div>
                            <div class="text-gray-400">I</div>
                            <div class="text-gray-400">O</div>
                        </div>
                    </div>

                    <!-- Enhanced Element Display -->
                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <h6 class="text-sm font-bold text-gray-400 mb-2">CURRENT FUSION</h6>
                        <div class="text-center">
                            <div id="drawnElement" class="inline-block w-16 h-16 rounded-full bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center text-2xl font-bold text-white mb-2">
                                ?
                            </div>
                            <div id="elementType" class="text-sm text-gray-400">Awaiting Fusion</div>
                        </div>
                    </div>

                    <!-- Game Results -->
                    <div class="text-center">
                        <div id="fusionResult" class="text-xl font-bold mb-2"></div>
                        <div id="fusionWinAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Fusion Pattern Guide -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚛️ FUSION PATTERNS</h5>
                    <div class="grid grid-cols-1 gap-2 text-xs">
                        ${FUSION_PATTERNS.slice(0, 6).map(pattern => 
                            `<div class="flex justify-between">
                                <span class="text-gray-400">${pattern.name}:</span>
                                <span class="text-yellow-400">${pattern.multiplier}x</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>

                <!-- Elemental Guide -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌟 ELEMENTAL FORCES</h5>
                    <div class="grid grid-cols-1 gap-1 text-xs">
                        ${ELEMENTAL_BALLS.slice(0, 5).map(ball => 
                            `<div class="flex items-center gap-2">
                                <span style="color: ${ball.color}">${ball.symbol}</span>
                                <span class="text-gray-400">${ball.description}</span>
                            </div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Fusion Overlay -->
        <div id="fusionOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">⚛️ FUSION COMPLETE ⚛️</h3>
                <div id="finalFusionResult" class="text-xl mb-4"></div>
                <div id="finalFusionStats" class="text-sm text-gray-400 mb-6"></div>
                <div id="fusionPatternResults" class="text-xs text-cyan-400 mb-4"></div>
                <button onclick="hideFusionOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    RETURN TO FUSION
                </button>
            </div>
        </div>
    `;

    updateFusionSettings();
    generateFusionCard();
    
    // Add keyboard listeners
    document.addEventListener('keydown', handleFusionKeyPress);
}

function updateFusionSettings() {
    const difficulty = document.getElementById('fusionDifficulty').value;
    const mode = document.getElementById('fusionMode').value;
    
    const diffData = FUSION_LEVELS.find(d => d.name === difficulty);
    const modeData = FUSION_MODES.find(m => m.name === mode);
    
    fusionGame.difficulty = difficulty;
    fusionGame.gameMode = mode;
    
    document.getElementById('fusionWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('requiredFusions').textContent = diffData.requiredFusions;
    document.getElementById('maxFusionBalls').textContent = diffData.ballCount;
    document.getElementById('fusionTimeLimit').textContent = diffData.timeLimit + 's';
    
    fusionGame.maxBalls = diffData.ballCount;
    
    updateFusionPayout();
}

function updateFusionPayout() {
    const betAmount = parseInt(document.getElementById('fusionBet').value) || 100;
    const diffData = FUSION_LEVELS.find(d => d.name === fusionGame.difficulty);
    const modeData = FUSION_MODES.find(m => m.name === fusionGame.gameMode);
    
    const totalMultiplier = diffData.multiplier * modeData.paylineMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 20); // Fusion multiplier
    
    document.getElementById('fusionPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function generateFusionCard() {
    const cardContainer = document.getElementById('fusionCard');
    cardContainer.innerHTML = '';
    
    fusionGame.slingoCard = [];
    fusionGame.markedNumbers = [];
    
    // Generate 5x5 Fusion card
    const ranges = [
        [1, 15],   // F column
        [16, 30],  // U column
        [31, 45],  // S column
        [46, 60],  // I column
        [61, 75]   // O column
    ];
    
    for (let row = 0; row < 5; row++) {
        fusionGame.slingoCard[row] = [];
        for (let col = 0; col < 5; col++) {
            let number;
            
            // Center square is FUSION
            if (row === 2 && col === 2) {
                number = 'FUSION';
                fusionGame.markedNumbers.push(`${row}-${col}`);
            } else {
                const [min, max] = ranges[col];
                do {
                    number = Math.floor(Math.random() * (max - min + 1)) + min;
                } while (fusionGame.slingoCard.some(r => r && r.includes(number)));
            }
            
            fusionGame.slingoCard[row][col] = number;
            
            const cell = document.createElement('div');
            cell.className = 'bg-black/50 border border-purple-500/30 rounded-lg h-12 flex items-center justify-center text-lg font-bold cursor-pointer transition-all duration-300';
            cell.id = `fusion-cell-${row}-${col}`;
            cell.textContent = number;
            
            if (number === 'FUSION') {
                cell.classList.add('bg-gradient-to-br', 'from-purple-600', 'to-pink-600', 'border-purple-400', 'marked');
                cell.style.color = '#ffffff';
                cell.style.textShadow = '0 0 10px #ff69b4';
            }
            
            cell.addEventListener('click', () => markFusionCell(row, col));
            cardContainer.appendChild(cell);
        }
    }
}

function startFusionGame() {
    const betAmount = parseInt(document.getElementById('fusionBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance for Fusion!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset fusion game state
    fusionGame.isPlaying = true;
    fusionGame.gamePhase = 'playing';
    fusionGame.betAmount = betAmount;
    fusionGame.calledNumbers = [];
    fusionGame.ballsDrawn = 0;
    fusionGame.slingoLines = 0;
    fusionGame.fusionPatterns = 0;
    fusionGame.perfectFusions = 0;
    fusionGame.chainCount = 0;
    fusionGame.energyChain = 1.0;
    fusionGame.fusionPowers = [];
    fusionGame.fusionTimes = [];
    
    const diffData = FUSION_LEVELS.find(d => d.name === fusionGame.difficulty);
    fusionGame.timeRemaining = diffData.timeLimit;
    
    document.getElementById('fuseBtn').disabled = false;
    document.getElementById('skipFusionBtn').disabled = false;
    document.getElementById('fusionBallsLeft').textContent = fusionGame.maxBalls;
    
    hideFusionOverlay();
    startFusionTimer();
    drawNextElement();
    updateFusionDisplay();
}

function startFusionTimer() {
    fusionGame.gameTimer = setInterval(() => {
        fusionGame.timeRemaining--;
        document.getElementById('fusionTimeLeft').textContent = fusionGame.timeRemaining;
        
        if (fusionGame.timeRemaining <= 0) {
            endFusionGame(false, 'Fusion time expired!');
        }
    }, 1000);
}

function drawNextElement() {
    if (!fusionGame.isPlaying || fusionGame.ballsDrawn >= fusionGame.maxBalls) {
        endFusionGame(false, 'No more fusion energy!');
        return;
    }
    
    // Generate available numbers
    const availableNumbers = [];
    for (let i = 1; i <= 75; i++) {
        if (!fusionGame.calledNumbers.includes(i)) {
            availableNumbers.push(i);
        }
    }
    
    if (availableNumbers.length === 0) {
        endFusionGame(false, 'All elements fused!');
        return;
    }
    
    // Check for elemental balls
    const elementalBall = getElementalBall();
    
    if (elementalBall) {
        fusionGame.currentNumber = elementalBall;
        displayElementalBall(elementalBall);
    } else {
        // Draw regular number
        const randomIndex = Math.floor(Math.random() * availableNumbers.length);
        fusionGame.currentNumber = availableNumbers[randomIndex];
        fusionGame.calledNumbers.push(fusionGame.currentNumber);
        displayRegularElement(fusionGame.currentNumber);
    }
    
    fusionGame.ballsDrawn++;
    fusionGame.elementDrawTime = Date.now();
    
    document.getElementById('fusionBallsLeft').textContent = fusionGame.maxBalls - fusionGame.ballsDrawn;
    updateFusionDisplay();
}

function getElementalBall() {
    const diffData = FUSION_LEVELS.find(d => d.name === fusionGame.difficulty);
    const rand = Math.random();
    
    if (rand < diffData.elementalChance) {
        const availableElements = ELEMENTAL_BALLS.filter(e => rand < e.chance);
        if (availableElements.length > 0) {
            return availableElements[Math.floor(Math.random() * availableElements.length)];
        }
    }
    
    return null;
}

function displayRegularElement(number) {
    const elementEl = document.getElementById('drawnElement');
    const typeEl = document.getElementById('elementType');
    
    elementEl.textContent = number;
    elementEl.className = 'inline-block w-16 h-16 rounded-full bg-gradient-to-br from-purple-400 to-pink-500 flex items-center justify-center text-2xl font-bold text-white mb-2';
    typeEl.textContent = 'Regular Element';
    
    document.getElementById('currentElement').textContent = number;
}

function displayElementalBall(elementalBall) {
    const elementEl = document.getElementById('drawnElement');
    const typeEl = document.getElementById('elementType');
    
    elementEl.textContent = elementalBall.symbol;
    elementEl.className = 'inline-block w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-2';
    elementEl.style.background = `radial-gradient(circle, ${elementalBall.color}, ${elementalBall.color}80)`;
    elementEl.style.color = '#ffffff';
    elementEl.style.textShadow = `0 0 15px ${elementalBall.color}`;
    
    typeEl.textContent = elementalBall.description;
    typeEl.style.color = elementalBall.color;
    
    document.getElementById('currentElement').textContent = elementalBall.symbol;
}

function fuseMark() {
    if (!fusionGame.isPlaying || !fusionGame.currentNumber) return;
    
    const fuseTime = Date.now();
    const reactionTime = fuseTime - fusionGame.elementDrawTime;
    fusionGame.fusionTimes.push(reactionTime);
    
    if (typeof fusionGame.currentNumber === 'object') {
        // Handle elemental ball
        handleElementalBall(fusionGame.currentNumber);
    } else {
        // Handle regular number
        const fused = fuseRegularNumber(fusionGame.currentNumber);
        if (fused) {
            fusionGame.perfectFusions++;
            fusionGame.chainCount++;
            fusionGame.maxChain = Math.max(fusionGame.maxChain, fusionGame.chainCount);
            
            // Fast fusion bonus
            if (reactionTime < 800) {
                fusionGame.energyChain += 0.15;
            }
        } else {
            fusionGame.chainCount = 0;
        }
    }
    
    checkFusionPatterns();
    updateFusionDisplay();
    
    setTimeout(() => {
        drawNextElement();
    }, 1200);
}

function fuseRegularNumber(number) {
    let fused = false;
    
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            if (fusionGame.slingoCard[row][col] === number) {
                const cellId = `fusion-cell-${row}-${col}`;
                const cell = document.getElementById(cellId);
                
                if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
                    fusionGame.markedNumbers.push(`${row}-${col}`);
                    cell.classList.add('bg-gradient-to-br', 'from-green-500', 'to-blue-500', 'border-cyan-400', 'marked');
                    cell.style.color = '#ffffff';
                    cell.style.textShadow = '0 0 10px #00ffff';
                    fused = true;
                }
            }
        }
    }
    
    return fused;
}

function handleElementalBall(elementalBall) {
    switch (elementalBall.type) {
        case 'fire':
            // Burns adjacent numbers
            fuseAdjacentCells();
            fusionGame.elementalBalls++;
            break;
        case 'water':
            // Flows to connected numbers
            fuseConnectedCells();
            fusionGame.elementalBalls++;
            break;
        case 'earth':
            // Marks entire row
            fuseEntireRow();
            fusionGame.elementalBalls++;
            break;
        case 'air':
            // Marks diagonal pattern
            fuseDiagonalPattern();
            fusionGame.elementalBalls++;
            break;
        case 'lightning':
            // Random fusion marks
            fuseRandomCells(3);
            fusionGame.elementalBalls++;
            break;
        case 'ice':
            // Freezes time for bonus
            fusionGame.timeRemaining += 30;
            fuseRandomCells(1);
            break;
        case 'void':
            // Removes and redistributes
            redistributeFusedCells();
            fusionGame.voidBalls++;
            break;
        case 'fusion':
            // Combines all elements
            triggerMegaFusion();
            fusionGame.fusionBalls++;
            break;
    }
}

function fuseAdjacentCells() {
    const centerRow = 2, centerCol = 2;
    const adjacent = [
        [centerRow-1, centerCol], [centerRow+1, centerCol],
        [centerRow, centerCol-1], [centerRow, centerCol+1]
    ];
    
    adjacent.forEach(([row, col]) => {
        if (row >= 0 && row < 5 && col >= 0 && col < 5) {
            if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
                fusionGame.markedNumbers.push(`${row}-${col}`);
                const cell = document.getElementById(`fusion-cell-${row}-${col}`);
                cell.classList.add('bg-gradient-to-br', 'from-red-500', 'to-orange-500', 'border-red-400', 'marked');
                cell.style.color = '#ffffff';
                cell.style.textShadow = '0 0 10px #ff4500';
            }
        }
    });
}

function fuseConnectedCells() {
    // Fuse cells in a flowing pattern
    const pattern = [[1,1], [1,2], [1,3], [2,1], [2,3], [3,1], [3,2], [3,3]];
    pattern.forEach(([row, col]) => {
        if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
            fusionGame.markedNumbers.push(`${row}-${col}`);
            const cell = document.getElementById(`fusion-cell-${row}-${col}`);
            cell.classList.add('bg-gradient-to-br', 'from-blue-500', 'to-cyan-500', 'border-blue-400', 'marked');
            cell.style.color = '#ffffff';
            cell.style.textShadow = '0 0 10px #0080ff';
        }
    });
}

function fuseEntireRow() {
    const row = Math.floor(Math.random() * 5);
    for (let col = 0; col < 5; col++) {
        if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
            fusionGame.markedNumbers.push(`${row}-${col}`);
            const cell = document.getElementById(`fusion-cell-${row}-${col}`);
            cell.classList.add('bg-gradient-to-br', 'from-yellow-600', 'to-brown-600', 'border-yellow-400', 'marked');
            cell.style.color = '#ffffff';
            cell.style.textShadow = '0 0 10px #8b4513';
        }
    }
}

function fuseDiagonalPattern() {
    // Fuse both diagonals
    for (let i = 0; i < 5; i++) {
        if (!fusionGame.markedNumbers.includes(`${i}-${i}`)) {
            fusionGame.markedNumbers.push(`${i}-${i}`);
            const cell = document.getElementById(`fusion-cell-${i}-${i}`);
            cell.classList.add('bg-gradient-to-br', 'from-cyan-500', 'to-teal-500', 'border-cyan-400', 'marked');
            cell.style.color = '#ffffff';
            cell.style.textShadow = '0 0 10px #87ceeb';
        }
        
        if (!fusionGame.markedNumbers.includes(`${i}-${4-i}`)) {
            fusionGame.markedNumbers.push(`${i}-${4-i}`);
            const cell = document.getElementById(`fusion-cell-${i}-${4-i}`);
            cell.classList.add('bg-gradient-to-br', 'from-cyan-500', 'to-teal-500', 'border-cyan-400', 'marked');
            cell.style.color = '#ffffff';
            cell.style.textShadow = '0 0 10px #87ceeb';
        }
    }
}

function fuseRandomCells(count) {
    const availableCells = [];
    for (let row = 0; row < 5; row++) {
        for (let col = 0; col < 5; col++) {
            if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
                availableCells.push([row, col]);
            }
        }
    }
    
    for (let i = 0; i < Math.min(count, availableCells.length); i++) {
        const randomIndex = Math.floor(Math.random() * availableCells.length);
        const [row, col] = availableCells.splice(randomIndex, 1)[0];
        
        fusionGame.markedNumbers.push(`${row}-${col}`);
        const cell = document.getElementById(`fusion-cell-${row}-${col}`);
        cell.classList.add('bg-gradient-to-br', 'from-yellow-400', 'to-yellow-600', 'border-yellow-400', 'marked');
        cell.style.color = '#000000';
        cell.style.textShadow = '0 0 10px #ffff00';
    }
}

function redistributeFusedCells() {
    // Remove some marked cells and add others
    const markedCells = fusionGame.markedNumbers.filter(pos => pos !== '2-2');
    if (markedCells.length > 2) {
        // Remove 2 random marked cells
        for (let i = 0; i < 2; i++) {
            const randomIndex = Math.floor(Math.random() * markedCells.length);
            const removedPos = markedCells.splice(randomIndex, 1)[0];
            
            fusionGame.markedNumbers = fusionGame.markedNumbers.filter(pos => pos !== removedPos);
            const cell = document.getElementById(`fusion-cell-${removedPos}`);
            cell.className = 'bg-black/50 border border-purple-500/30 rounded-lg h-12 flex items-center justify-center text-lg font-bold cursor-pointer transition-all duration-300';
            cell.style.color = '';
            cell.style.textShadow = '';
        }
    }
    
    // Add 3 new random cells
    fuseRandomCells(3);
}

function triggerMegaFusion() {
    // Mark multiple cells in a special pattern
    fusionGame.energyChain += 1.0;
    fusionGame.quantumSpins++;
    
    // Mark corners and center cross
    const megaPattern = [
        [0,0], [0,4], [4,0], [4,4], // corners
        [2,0], [2,1], [2,3], [2,4], // center row
        [0,2], [1,2], [3,2], [4,2]  // center column
    ];
    
    megaPattern.forEach(([row, col]) => {
        if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
            fusionGame.markedNumbers.push(`${row}-${col}`);
            const cell = document.getElementById(`fusion-cell-${row}-${col}`);
            cell.classList.add('bg-gradient-to-br', 'from-pink-500', 'to-purple-600', 'border-pink-400', 'marked');
            cell.style.color = '#ffffff';
            cell.style.textShadow = '0 0 15px #ff69b4';
        }
    });
}

function skipFusion() {
    if (!fusionGame.isPlaying) return;
    
    fusionGame.chainCount = 0;
    drawNextElement();
}

function markFusionCell(row, col) {
    if (!fusionGame.isPlaying) return;
    
    const cellPos = `${row}-${col}`;
    if (fusionGame.markedNumbers.includes(cellPos)) return;
    
    // Only allow marking if it matches current number or is an elemental ball action
    const cellNumber = fusionGame.slingoCard[row][col];
    if (cellNumber === fusionGame.currentNumber || typeof fusionGame.currentNumber === 'object') {
        fuseMark();
    }
}

function checkFusionPatterns() {
    fusionGame.slingoLines = 0;
    fusionGame.fusionPatterns = 0;
    
    // Check horizontal lines
    for (let row = 0; row < 5; row++) {
        let lineComplete = true;
        for (let col = 0; col < 5; col++) {
            if (!fusionGame.markedNumbers.includes(`${row}-${col}`)) {
                lineComplete = false;
                break;
            }
        }
        if (lineComplete) {
            fusionGame.slingoLines++;
            fusionGame.fusionPatterns++;
        }
    }
    
    // Check for special fusion patterns
    fusionGame.megaFusion = fusionGame.markedNumbers.length >= 20;
    fusionGame.ultraFusion = fusionGame.markedNumbers.length >= 23;
    fusionGame.cosmicFusion = fusionGame.markedNumbers.length >= 25;
    
    // Check win condition
    const diffData = FUSION_LEVELS.find(d => d.name === fusionGame.difficulty);
    if (fusionGame.fusionPatterns >= diffData.requiredFusions || fusionGame.cosmicFusion) {
        endFusionGame(true, 'Fusion Mastery Achieved!');
    }
}

function updateFusionDisplay() {
    document.getElementById('fusionLines').textContent = fusionGame.slingoLines;
    document.getElementById('fusionPatterns').textContent = fusionGame.fusionPatterns;
    document.getElementById('perfectFusions').textContent = fusionGame.perfectFusions;
    document.getElementById('fusionChain').textContent = fusionGame.chainCount;
    document.getElementById('energyChain').textContent = Math.floor(fusionGame.energyChain * 10) / 10 + 'x';
    document.getElementById('fusionRating').textContent = fusionGame.fusionRating;
    
    // Update fusion powers display
    const powersEl = document.getElementById('activeFusionPowers');
    if (fusionGame.fusionPowers.length > 0) {
        powersEl.innerHTML = fusionGame.fusionPowers.map(p => `<span class="text-cyan-400">${p}</span>`).join('<br>');
    } else {
        powersEl.textContent = 'No active elemental powers';
    }
}

function endFusionGame(won, message) {
    fusionGame.isPlaying = false;
    clearInterval(fusionGame.gameTimer);
    
    document.getElementById('fuseBtn').disabled = true;
    document.getElementById('skipFusionBtn').disabled = true;
    
    let winnings = 0;
    
    if (won) {
        const diffData = FUSION_LEVELS.find(d => d.name === fusionGame.difficulty);
        const modeData = FUSION_MODES.find(m => m.name === fusionGame.gameMode);
        
        // Calculate fusion winnings
        let baseMultiplier = diffData.multiplier * modeData.paylineMultiplier;
        
        // Fusion bonuses
        const patternBonus = fusionGame.fusionPatterns * 0.4;
        const chainBonus = fusionGame.maxChain * 0.1;
        const speedBonus = fusionGame.fusionTimes.length > 0 ? 
            (fusionGame.fusionTimes.filter(t => t < 800).length / fusionGame.fusionTimes.length) * 0.6 : 0;
        
        // Special fusion bonuses
        let fusionBonus = 0;
        if (fusionGame.cosmicFusion) fusionBonus += 3.0;
        else if (fusionGame.ultraFusion) fusionBonus += 2.0;
        else if (fusionGame.megaFusion) fusionBonus += 1.0;
        
        const totalMultiplier = baseMultiplier * (1 + patternBonus + chainBonus + speedBonus + fusionBonus) * fusionGame.energyChain;
        winnings = Math.floor(fusionGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        fusionGame.fusionStreak++;
        fusionGame.maxFusionStreak = Math.max(fusionGame.maxFusionStreak, fusionGame.fusionStreak);
        fusionGame.fusionEnergy += 35;
        
        document.getElementById('fusionResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">⚛️ FUSION MASTERY! ⚛️</span>`;
        document.getElementById('fusionWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        fusionGame.fusionStreak = 0;
        
        document.getElementById('fusionResult').innerHTML = 
            `<span class="text-red-400">⚛️ FUSION FAILED ⚛️</span>`;
        document.getElementById('fusionWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">${message}</div>`;
    }
    
    fusionGame.totalFusions++;
    updateFusionDisplay();
    
    setTimeout(() => {
        document.getElementById('fusionOverlay').classList.remove('hidden');
        document.getElementById('finalFusionResult').innerHTML = 
            won ? '<span class="text-green-400">⚛️ FUSION TRANSCENDENCE! ⚛️</span>' : 
                  '<span class="text-red-400">💔 FUSION COLLAPSE 💔</span>';
        
        const avgFusion = fusionGame.fusionTimes.length > 0 ? 
            Math.floor(fusionGame.fusionTimes.reduce((a, b) => a + b, 0) / fusionGame.fusionTimes.length) : 0;
        
        document.getElementById('finalFusionStats').innerHTML = 
            `Patterns: ${fusionGame.fusionPatterns} | Perfect: ${fusionGame.perfectFusions} | Chain: ${fusionGame.maxChain}<br>
             Avg Fusion: ${avgFusion}ms | Energy Chain: ${Math.floor(fusionGame.energyChain * 10) / 10}x<br>
             Elements: ${fusionGame.elementalBalls} | Quantum: ${fusionGame.quantumSpins}`;
        
        const patterns = [];
        if (fusionGame.cosmicFusion) patterns.push('COSMIC FUSION');
        if (fusionGame.ultraFusion) patterns.push('ULTRA FUSION');
        if (fusionGame.megaFusion) patterns.push('MEGA FUSION');
        
        document.getElementById('fusionPatternResults').textContent = 
            patterns.length > 0 ? `Fusion Patterns: ${patterns.join(', ')}` : '';
    }, 3000);
}

function handleFusionKeyPress(e) {
    if (!fusionGame.isPlaying) return;
    
    if (e.code === 'Space') {
        e.preventDefault();
        fuseMark();
    } else if (e.key.toLowerCase() === 's') {
        e.preventDefault();
        skipFusion();
    }
}

function hideFusionOverlay() {
    document.getElementById('fusionOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSlingoGame();
});