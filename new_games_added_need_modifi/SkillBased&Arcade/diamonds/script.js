// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadDiamondsGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                            <h4 class="text-xl font-bold mb-4 text-pink-400">DIGITAL DIAMONDS</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="diamondsBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">AUTO COLLECT AT</label>
                                <input type="number" id="diamondsAutoCollect" value="2.00" min="1.01" step="0.01" 
                                       class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                                <div class="text-xs text-gray-400 mt-1">Leave empty for manual collect</div>
                            </div>
                            
                            <button id="startDiamonds" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                START MINING
                            </button>
                            
                            <button id="collectDiamonds" class="w-full py-3 rounded-lg font-bold bg-pink-600 hover:bg-pink-700 text-white mb-4" disabled>
                                COLLECT GEMS
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Multiplier</div>
                                <div id="diamondsMultiplier" class="text-3xl font-bold text-pink-400 neon-glow">1.00x</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Gems Collected</div>
                                <div id="diamondsCollected" class="text-xl font-bold text-blue-400">0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="diamondsPotentialWin" class="text-xl font-bold text-green-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Mining Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-pink-400">MINING DATA</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Mining Speed:</span>
                                    <span id="diamondsMiningSpeed" class="text-pink-400">Normal</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Crash Risk:</span>
                                    <span id="diamondsCrashRisk" class="text-red-400">Low</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Gem Quality:</span>
                                    <span id="diamondsQuality" class="text-blue-400">Standard</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Sessions -->
                        <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-pink-400">RECENT SESSIONS</h5>
                            <div id="diamondsHistory" class="space-y-1 max-h-32 overflow-y-auto text-sm">
                                <!-- Recent results will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mining Display -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                            <div id="diamondsDisplay" class="relative bg-black/50 rounded-lg p-4 h-96 overflow-hidden">
                                <canvas id="diamondsCanvas" width="400" height="350" class="w-full h-full"></canvas>
                                
                                <!-- Floating Gems -->
                                <div id="floatingGems" class="absolute inset-0 pointer-events-none">
                                    <!-- Animated gems will appear here -->
                                </div>
                            </div>
                            <div id="diamondsStatus" class="text-center mt-4 text-lg font-semibold">Ready to start mining digital diamonds</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeDiamonds();
        }
        
        let diamondsGame = {
            canvas: null,
            ctx: null,
            isPlaying: false,
            multiplier: 1.00,
            betAmount: 0,
            startTime: 0,
            crashPoint: 0,
            autoCollect: 0,
            gemsCollected: 0,
            miningSpeed: 1,
            animationId: null,
            particles: []
        };
        
        function initializeDiamonds() {
            diamondsGame.canvas = document.getElementById('diamondsCanvas');
            diamondsGame.ctx = diamondsGame.canvas.getContext('2d');
            
            drawDiamondsScene();
            
            document.getElementById('startDiamonds').addEventListener('click', startDiamondsGame);
            document.getElementById('collectDiamonds').addEventListener('click', collectDiamonds);
        }
        
        function drawDiamondsScene() {
            const ctx = diamondsGame.ctx;
            const canvas = diamondsGame.canvas;
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw mining background
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#1a0033');
            gradient.addColorStop(0.5, '#330066');
            gradient.addColorStop(1, '#000');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw mining grid
            ctx.strokeStyle = '#ff2d9220';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 30) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 30) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            if (diamondsGame.isPlaying) {
                // Draw gems based on current multiplier
                const gemCount = Math.floor(diamondsGame.multiplier * 3);
                for (let i = 0; i < gemCount; i++) {
                    const x = (i * 50 + 50) % canvas.width;
                    const y = 50 + (Math.floor(i * 50 / canvas.width) * 60);
                    
                    drawGem(ctx, x, y, 15);
                }
                
                // Draw particles
                diamondsGame.particles.forEach(particle => {
                    ctx.fillStyle = particle.color;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // Update particle
                    particle.y -= particle.speed;
                    particle.size *= 0.99;
                    particle.opacity *= 0.98;
                });
                
                // Remove dead particles
                diamondsGame.particles = diamondsGame.particles.filter(p => p.opacity > 0.1);
                
                // Add new particles
                if (Math.random() < 0.3) {
                    diamondsGame.particles.push({
                        x: Math.random() * canvas.width,
                        y: canvas.height,
                        size: Math.random() * 3 + 2,
                        speed: Math.random() * 2 + 1,
                        color: `rgba(255, 45, 146, ${Math.random() * 0.8 + 0.2})`,
                        opacity: 1
                    });
                }
            }
            
            // Draw multiplier text
            ctx.fillStyle = '#ff2d92';
            ctx.font = 'bold 32px monospace';
            ctx.textAlign = 'center';
            ctx.fillText(`${diamondsGame.multiplier.toFixed(2)}x`, canvas.width / 2, 50);
            
            if (diamondsGame.isPlaying && diamondsGame.multiplier >= diamondsGame.crashPoint) {
                // Crash effect
                ctx.fillStyle = '#ff2d92';
                ctx.font = 'bold 48px monospace';
                ctx.fillText('CRASH!', canvas.width / 2, canvas.height / 2);
                
                // Add crash particles
                for (let i = 0; i < 20; i++) {
                    diamondsGame.particles.push({
                        x: canvas.width / 2 + (Math.random() - 0.5) * 100,
                        y: canvas.height / 2 + (Math.random() - 0.5) * 100,
                        size: Math.random() * 8 + 4,
                        speed: Math.random() * 5 + 2,
                        color: '#ff0000',
                        opacity: 1
                    });
                }
            }
        }
        
        function drawGem(ctx, x, y, size) {
            // Draw diamond shape
            ctx.fillStyle = '#00d4ff';
            ctx.beginPath();
            ctx.moveTo(x, y - size);
            ctx.lineTo(x + size * 0.6, y - size * 0.3);
            ctx.lineTo(x + size * 0.6, y + size * 0.3);
            ctx.lineTo(x, y + size);
            ctx.lineTo(x - size * 0.6, y + size * 0.3);
            ctx.lineTo(x - size * 0.6, y - size * 0.3);
            ctx.closePath();
            ctx.fill();
            
            // Add glow
            ctx.strokeStyle = '#39ff14';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        function startDiamondsGame() {
            const betAmount = parseInt(document.getElementById('diamondsBet').value);
            const autoCollect = parseFloat(document.getElementById('diamondsAutoCollect').value) || 0;
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize game state
            diamondsGame.isPlaying = true;
            diamondsGame.multiplier = 1.00;
            diamondsGame.betAmount = betAmount;
            diamondsGame.startTime = Date.now();
            diamondsGame.autoCollect = autoCollect;
            diamondsGame.crashPoint = generateCrashPoint();
            diamondsGame.gemsCollected = 0;
            diamondsGame.particles = [];
            
            // Update UI
            document.getElementById('startDiamonds').disabled = true;
            document.getElementById('collectDiamonds').disabled = false;
            document.getElementById('diamondsStatus').textContent = 'Mining in progress... Collect gems before crash!';
            
            updateDiamondsStats();
            animateDiamonds();
        }
        
        function generateCrashPoint() {
            // Generate crash point using exponential distribution
            const random = Math.random();
            return Math.max(1.01, Math.pow(1 / random, 0.12));
        }
        
        function updateDiamondsStats() {
            // Update mining speed based on multiplier
            if (diamondsGame.multiplier < 2) {
                document.getElementById('diamondsMiningSpeed').textContent = 'Normal';
            } else if (diamondsGame.multiplier < 5) {
                document.getElementById('diamondsMiningSpeed').textContent = 'Fast';
            } else {
                document.getElementById('diamondsMiningSpeed').textContent = 'Ultra Fast';
            }
            
            // Update crash risk
            if (diamondsGame.multiplier < 3) {
                document.getElementById('diamondsCrashRisk').textContent = 'Low';
            } else if (diamondsGame.multiplier < 10) {
                document.getElementById('diamondsCrashRisk').textContent = 'Medium';
            } else {
                document.getElementById('diamondsCrashRisk').textContent = 'High';
            }
            
            // Update gem quality
            if (diamondsGame.multiplier < 5) {
                document.getElementById('diamondsQuality').textContent = 'Standard';
            } else if (diamondsGame.multiplier < 15) {
                document.getElementById('diamondsQuality').textContent = 'Premium';
            } else {
                document.getElementById('diamondsQuality').textContent = 'Legendary';
            }
        }
        
        function animateDiamonds() {
            function animate() {
                if (!diamondsGame.isPlaying) return;
                
                const elapsed = (Date.now() - diamondsGame.startTime) / 1000;
                diamondsGame.multiplier = 1 + elapsed * 0.3 + Math.pow(elapsed, 1.8) * 0.05;
                diamondsGame.gemsCollected = Math.floor(diamondsGame.multiplier * 10);
                
                // Update display
                document.getElementById('diamondsMultiplier').textContent = diamondsGame.multiplier.toFixed(2) + 'x';
                document.getElementById('diamondsCollected').textContent = diamondsGame.gemsCollected;
                document.getElementById('diamondsPotentialWin').textContent = 
                    '$' + Math.floor(diamondsGame.betAmount * diamondsGame.multiplier);
                
                updateDiamondsStats();
                
                // Check for crash
                if (diamondsGame.multiplier >= diamondsGame.crashPoint) {
                    crashDiamonds();
                    return;
                }
                
                // Check for auto collect
                if (diamondsGame.autoCollect > 0 && diamondsGame.multiplier >= diamondsGame.autoCollect) {
                    collectDiamonds();
                    return;
                }
                
                drawDiamondsScene();
                diamondsGame.animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        function collectDiamonds() {
            if (!diamondsGame.isPlaying) return;
            
            const winnings = Math.floor(diamondsGame.betAmount * diamondsGame.multiplier);
            balance += winnings;
            updateBalance();
            
            endDiamondsGame(true, winnings);
        }
        
        function crashDiamonds() {
            endDiamondsGame(false, 0);
        }
        
        function endDiamondsGame(won, winnings) {
            diamondsGame.isPlaying = false;
            if (diamondsGame.animationId) {
                cancelAnimationFrame(diamondsGame.animationId);
            }
            
            // Update UI
            document.getElementById('startDiamonds').disabled = false;
            document.getElementById('collectDiamonds').disabled = true;
            
            if (won) {
                document.getElementById('diamondsStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Gems collected at ${diamondsGame.multiplier.toFixed(2)}x! Won $${winnings}</span>`;
            } else {
                document.getElementById('diamondsStatus').innerHTML = 
                    `<span class="text-red-400">Mining crashed at ${diamondsGame.crashPoint.toFixed(2)}x!</span>`;
            }
            
            // Add to history
            addDiamondsHistory(diamondsGame.multiplier >= diamondsGame.crashPoint ? diamondsGame.crashPoint : diamondsGame.multiplier, won);
            
            drawDiamondsScene();
            
            // Reset after delay
            setTimeout(() => {
                diamondsGame.multiplier = 1.00;
                diamondsGame.gemsCollected = 0;
                document.getElementById('diamondsMultiplier').textContent = '1.00x';
                document.getElementById('diamondsCollected').textContent = '0';
                document.getElementById('diamondsPotentialWin').textContent = '0 GA';
                document.getElementById('diamondsStatus').textContent = 'Ready to start mining digital diamonds';
                drawDiamondsScene();
            }, 3000);
        }
        
        function addDiamondsHistory(multiplier, won) {
            const history = document.getElementById('diamondsHistory');
            const result = document.createElement('div');
            result.className = `flex justify-between py-1 ${won ? 'text-green-400' : 'text-red-400'}`;
            result.innerHTML = `
                <span>${multiplier.toFixed(2)}x</span>
                <span class="text-xs">${won ? 'COLLECTED' : 'CRASHED'}</span>
            `;
            
            history.insertBefore(result, history.firstChild);
            
            // Keep only last 8 results
            while (history.children.length > 8) {
                history.removeChild(history.lastChild);
            }
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDiamondsGame();
});