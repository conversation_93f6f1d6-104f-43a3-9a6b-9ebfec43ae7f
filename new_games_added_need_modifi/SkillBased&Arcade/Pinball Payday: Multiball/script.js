// Game state
let balance = 1000;

// Pinball Payday: Multiball game state
let pinballMultiballGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'normal',
    gameMode: 'multiball',
    
    // Physics and game state
    balls: [],
    leftFlipper: { angle: -30, active: false },
    rightFlipper: { angle: 30, active: false },
    plunger: { power: 0, charging: false, released: false },
    
    // Game mechanics
    score: 0,
    ballsLeft: 3,
    currentBall: 1,
    multiplier: 1,
    combo: 0,
    maxCombo: 0,
    
    // Multiball specific
    multiballActive: false,
    multiballLevel: 1,
    maxBalls: 6,
    ballsInPlay: 0,
    multiballBonus: 1,
    jackpotMultiplier: 1,
    
    // Targets and features
    bumpers: [],
    targets: [],
    ramps: [],
    lanes: [],
    bonusTargets: [],
    multiballTargets: [],
    
    // Special features
    jackpotActive: false,
    jackpotValue: 0,
    bonusMultiplier: 1,
    skillShotActive: true,
    superJackpotActive: false,
    
    // Performance tracking
    totalHits: 0,
    perfectShots: 0,
    rampShots: 0,
    bumperHits: 0,
    targetHits: 0,
    skillShots: 0,
    multiballHits: 0,
    simultaneousHits: 0,
    
    // Physics
    gravity: 0.25,
    friction: 0.985,
    bounceDamping: 0.75,
    
    // Canvas and animation
    canvas: null,
    ctx: null,
    animationId: null,
    
    // Timing
    gameStartTime: 0,
    ballTime: 0,
    lastUpdate: 0,
    multiballStartTime: 0,
    
    // Audio simulation
    soundEffects: true,
    
    // Table elements
    tableWidth: 500,
    tableHeight: 600
};

// Ultra-strict difficulty settings with sub-6% win rates for multiball
const DIFFICULTY_LEVELS = [
    {
        name: 'easy',
        ballCount: 5,
        targetScore: 100000,
        timeLimit: 300,
        winRate: 0.08,
        multiplier: 0.6,
        bumperPower: 1.2,
        flipperPower: 1.3,
        multiballChance: 0.7,
        description: 'Beginner Multiball (8% win rate)'
    },
    {
        name: 'normal',
        ballCount: 4,
        targetScore: 150000,
        timeLimit: 240,
        winRate: 0.06,
        multiplier: 1.0,
        bumperPower: 1.0,
        flipperPower: 1.0,
        multiballChance: 0.5,
        description: 'Standard Multiball (6% win rate)'
    },
    {
        name: 'hard',
        ballCount: 3,
        targetScore: 200000,
        timeLimit: 180,
        winRate: 0.04,
        multiplier: 1.3,
        bumperPower: 0.7,
        flipperPower: 0.7,
        multiballChance: 0.4,
        description: 'Expert Multiball (4% win rate)'
    },
    {
        name: 'expert',
        ballCount: 3,
        targetScore: 300000,
        timeLimit: 150,
        winRate: 0.03,
        multiplier: 1.6,
        bumperPower: 0.5,
        flipperPower: 0.5,
        multiballChance: 0.3,
        description: 'Master Multiball (3% win rate)'
    },
    {
        name: 'legendary',
        ballCount: 2,
        targetScore: 500000,
        timeLimit: 120,
        winRate: 0.02,
        multiplier: 2.0,
        bumperPower: 0.3,
        flipperPower: 0.3,
        multiballChance: 0.2,
        description: 'Legendary Multiball (2% win rate)'
    }
];

const MULTIBALL_MODES = [
    { name: 'chaos', description: 'Chaos Multiball', ballCount: 4, scoreMultiplier: 1.2, timeBonus: true },
    { name: 'frenzy', description: 'Frenzy Multiball', ballCount: 5, scoreMultiplier: 1.4, timeBonus: true },
    { name: 'mayhem', description: 'Mayhem Multiball', ballCount: 6, scoreMultiplier: 1.6, timeBonus: false },
    { name: 'insanity', description: 'Insanity Multiball', ballCount: 8, scoreMultiplier: 1.8, timeBonus: false }
];

function loadMultiballPinballGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Multiball Pinball Table -->
            <div class="lg:col-span-2">
                <div class="bg-black/40 p-6 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h4 class="text-2xl font-bold mb-4 text-center bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent">
                        🎯 MULTIBALL PINBALL PAYDAY 🎯
                    </h4>
                    
                    <!-- Multiball Table Canvas -->
                    <div class="relative mx-auto max-w-lg">
                        <canvas id="multiballCanvas" width="500" height="600" 
                                class="w-full border-2 border-purple-500/50 rounded-lg bg-gradient-to-b from-purple-900/30 via-blue-900/30 to-cyan-900/30 shadow-2xl"></canvas>
                        
                        <!-- Game Overlay -->
                        <div id="gameOverlay" class="absolute inset-0 flex items-center justify-center bg-black/80 rounded-lg hidden backdrop-blur-sm">
                            <div class="text-center p-6">
                                <div id="overlayTitle" class="text-4xl font-bold text-purple-400 mb-4 animate-pulse"></div>
                                <div id="overlayMessage" class="text-xl text-white mb-6"></div>
                                <button id="overlayButton" class="cyber-button px-8 py-3 rounded-lg font-bold text-lg">
                                    CONTINUE
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Multiball Stats Grid -->
                    <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="bg-gradient-to-br from-blue-600/30 to-blue-800/30 p-3 rounded-lg border border-blue-500/30">
                            <div class="text-xs text-blue-300 font-semibold">SCORE</div>
                            <div id="currentScore" class="text-xl font-bold text-blue-100">0</div>
                        </div>
                        <div class="bg-gradient-to-br from-green-600/30 to-green-800/30 p-3 rounded-lg border border-green-500/30">
                            <div class="text-xs text-green-300 font-semibold">BALLS IN PLAY</div>
                            <div id="ballsInPlay" class="text-xl font-bold text-green-100">0</div>
                        </div>
                        <div class="bg-gradient-to-br from-yellow-600/30 to-yellow-800/30 p-3 rounded-lg border border-yellow-500/30">
                            <div class="text-xs text-yellow-300 font-semibold">MULTIBALL LVL</div>
                            <div id="multiballLevel" class="text-xl font-bold text-yellow-100">1</div>
                        </div>
                        <div class="bg-gradient-to-br from-purple-600/30 to-purple-800/30 p-3 rounded-lg border border-purple-500/30">
                            <div class="text-xs text-purple-300 font-semibold">COMBO</div>
                            <div id="currentCombo" class="text-xl font-bold text-purple-100">0</div>
                        </div>
                    </div>
                    
                    <!-- Multiball Progress Bar -->
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-300 mb-2">
                            <span class="font-semibold">Multiball Progress</span>
                            <span id="multiballProgress" class="font-bold">0%</span>
                        </div>
                        <div class="bg-gray-800 rounded-full h-4 border border-gray-600">
                            <div id="multiballBar" class="bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 h-full rounded-full transition-all duration-500 shadow-lg" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <!-- Controls Info -->
                    <div class="mt-4 text-center text-sm text-gray-300">
                        <p><strong>SPACE:</strong> Launch Ball | <strong>A/LEFT:</strong> Left Flipper | <strong>D/RIGHT:</strong> Right Flipper</p>
                        <p class="text-cyan-400 mt-1"><strong>MULTIBALL:</strong> Control multiple balls simultaneously!</p>
                    </div>
                </div>
            </div>
            
            <!-- Game Controls -->
            <div class="space-y-4">
                <!-- Game Setup -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">🎮 Multiball Setup</h3>
                    
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-300 mb-2 font-semibold">Bet Amount</label>
                            <select id="multiballBet" class="w-full bg-black/60 border border-purple-500/40 rounded-lg px-3 py-2 text-white focus:border-purple-400">
                                <option value="50">50 GA - Casual Multiball</option>
                                <option value="100">100 GA - Standard Multiball</option>
                                <option value="200">200 GA - High Stakes</option>
                                <option value="400">400 GA - Expert Multiball</option>
                                <option value="800">800 GA - Master Multiball</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-2 font-semibold">Table Difficulty</label>
                            <select id="multiballDifficulty" class="w-full bg-black/60 border border-purple-500/40 rounded-lg px-3 py-2 text-white focus:border-purple-400">
                                <option value="easy">Easy (6% win rate)</option>
                                <option value="normal">Normal (5% win rate)</option>
                                <option value="hard">Hard (4% win rate)</option>
                                <option value="expert">Expert (3% win rate)</option>
                                <option value="legendary">Legendary (2% win rate)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm text-gray-300 mb-2 font-semibold">Multiball Mode</label>
                            <select id="multiballMode" class="w-full bg-black/60 border border-purple-500/40 rounded-lg px-3 py-2 text-white focus:border-purple-400">
                                <option value="chaos">Chaos (4 balls)</option>
                                <option value="frenzy">Frenzy (5 balls)</option>
                                <option value="mayhem">Mayhem (6 balls)</option>
                                <option value="insanity">Insanity (8 balls)</option>
                            </select>
                        </div>
                        
                        <button id="startMultiball" class="w-full cyber-button py-3 rounded-lg font-bold text-lg bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 hover:from-purple-500 hover:via-pink-500 hover:to-cyan-500">
                            🚀 START MULTIBALL
                        </button>
                    </div>
                </div>
                
                <!-- Multiball Features -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">⭐ Multiball Features</h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div class="text-center p-2 bg-black/30 rounded border border-yellow-500/20">
                            <div class="text-lg font-bold text-yellow-400" id="jackpotValue">0</div>
                            <div class="text-gray-300">Jackpot</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-cyan-500/20">
                            <div class="text-lg font-bold text-cyan-400" id="superJackpot">OFF</div>
                            <div class="text-gray-300">Super Jackpot</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-green-500/20">
                            <div class="text-lg font-bold text-green-400" id="multiballStatus">READY</div>
                            <div class="text-gray-300">Multiball</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-purple-500/20">
                            <div class="text-lg font-bold text-purple-400" id="bonusMultiplier">1x</div>
                            <div class="text-gray-300">Bonus</div>
                        </div>
                    </div>
                </div>
                
                <!-- Performance Stats -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">📊 Multiball Performance</h3>
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div class="text-center p-2 bg-black/30 rounded border border-red-500/20">
                            <div class="text-lg font-bold text-red-400" id="bumperHits">0</div>
                            <div class="text-gray-300">Bumpers</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-green-500/20">
                            <div class="text-lg font-bold text-green-400" id="targetHits">0</div>
                            <div class="text-gray-300">Targets</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-blue-500/20">
                            <div class="text-lg font-bold text-blue-400" id="rampShots">0</div>
                            <div class="text-gray-300">Ramps</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-yellow-500/20">
                            <div class="text-lg font-bold text-yellow-400" id="skillShots">0</div>
                            <div class="text-gray-300">Skill Shots</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-pink-500/20">
                            <div class="text-lg font-bold text-pink-400" id="multiballHits">0</div>
                            <div class="text-gray-300">Multiball Hits</div>
                        </div>
                        <div class="text-center p-2 bg-black/30 rounded border border-cyan-500/20">
                            <div class="text-lg font-bold text-cyan-400" id="simultaneousHits">0</div>
                            <div class="text-gray-300">Simultaneous</div>
                        </div>
                    </div>
                </div>
                
                <!-- Current Game Info -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">🎯 Game Info</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Target Score:</span>
                            <span id="targetScore" class="text-purple-400 font-bold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Balls Left:</span>
                            <span id="ballsLeft" class="text-green-400 font-bold">3</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Time Left:</span>
                            <span id="timeLeft" class="text-yellow-400 font-bold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Win Chance:</span>
                            <span id="winChance" class="text-red-400 font-bold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Max Balls:</span>
                            <span id="maxBalls" class="text-cyan-400 font-bold">4</span>
                        </div>
                    </div>
                </div>
                
                <!-- Game Result -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">🏆 Result</h3>
                    <div id="multiballResult" class="text-center text-lg font-semibold text-cyan-400 min-h-12 p-2">
                        Select difficulty and start multiball pinball!
                    </div>
                    <div id="winAmount" class="text-center text-2xl font-bold text-green-400 mt-2 min-h-8"></div>
                </div>
                
                <!-- Instructions -->
                <div class="bg-black/40 p-4 rounded-xl border border-purple-500/40 backdrop-blur-sm">
                    <h3 class="text-xl font-bold text-purple-400 mb-4">📖 Multiball Guide</h3>
                    <div class="text-sm text-gray-300 space-y-2">
                        <p>• <strong>Launch:</strong> Hold SPACE to charge plunger</p>
                        <p>• <strong>Flippers:</strong> Control all balls with A/D keys</p>
                        <p>• <strong>Multiball:</strong> Hit multiball targets to activate</p>
                        <p>• <strong>Simultaneous:</strong> Hit multiple targets at once</p>
                        <p>• <strong>Super Jackpot:</strong> Activate during multiball</p>
                        <p>• <strong>Ball Management:</strong> Keep multiple balls alive</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupMultiballGame();
}

function setupMultiballGame() {
    pinballMultiballGame.canvas = document.getElementById('multiballCanvas');
    pinballMultiballGame.ctx = pinballMultiballGame.canvas.getContext('2d');
    
    // Event listeners
    document.getElementById('startMultiball').addEventListener('click', startMultiballGame);
    document.getElementById('multiballDifficulty').addEventListener('change', updateMultiballSettings);
    document.getElementById('multiballMode').addEventListener('change', updateMultiballMode);
    document.getElementById('overlayButton').addEventListener('click', hideGameOverlay);
    
    // Keyboard controls
    document.addEventListener('keydown', handleMultiballKeyDown);
    document.addEventListener('keyup', handleMultiballKeyUp);
    
    updateMultiballSettings();
    updateMultiballDisplay();
    initializeMultiballTable();
    drawMultiballTable();
}

function updateMultiballSettings() {
    const difficulty = document.getElementById('multiballDifficulty').value;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    
    pinballMultiballGame.difficulty = difficulty;
    pinballMultiballGame.ballsLeft = diffData.ballCount;
    
    document.getElementById('targetScore').textContent = diffData.targetScore.toLocaleString();
    document.getElementById('winChance').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('ballsLeft').textContent = diffData.ballCount;
    
    // Update jackpot based on difficulty
    pinballMultiballGame.jackpotValue = Math.floor(diffData.targetScore * 0.4);
    document.getElementById('jackpotValue').textContent = pinballMultiballGame.jackpotValue.toLocaleString();
}

function updateMultiballMode() {
    const mode = document.getElementById('multiballMode').value;
    const modeData = MULTIBALL_MODES.find(m => m.name === mode);
    
    pinballMultiballGame.gameMode = mode;
    pinballMultiballGame.maxBalls = modeData.ballCount;
    
    document.getElementById('maxBalls').textContent = modeData.ballCount;
}

function startMultiballGame() {
    const betAmount = parseInt(document.getElementById('multiballBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballMultiballGame.difficulty);
    const modeData = MULTIBALL_MODES.find(m => m.name === pinballMultiballGame.gameMode);
    
    // Reset all game state
    pinballMultiballGame.isPlaying = true;
    pinballMultiballGame.gamePhase = 'playing';
    pinballMultiballGame.betAmount = betAmount;
    pinballMultiballGame.score = 0;
    pinballMultiballGame.ballsLeft = diffData.ballCount;
    pinballMultiballGame.currentBall = 1;
    pinballMultiballGame.multiplier = 1;
    pinballMultiballGame.combo = 0;
    pinballMultiballGame.maxCombo = 0;
    pinballMultiballGame.totalHits = 0;
    pinballMultiballGame.perfectShots = 0;
    pinballMultiballGame.rampShots = 0;
    pinballMultiballGame.bumperHits = 0;
    pinballMultiballGame.targetHits = 0;
    pinballMultiballGame.skillShots = 0;
    pinballMultiballGame.multiballHits = 0;
    pinballMultiballGame.simultaneousHits = 0;
    pinballMultiballGame.multiballActive = false;
    pinballMultiballGame.multiballLevel = 1;
    pinballMultiballGame.ballsInPlay = 0;
    pinballMultiballGame.jackpotActive = false;
    pinballMultiballGame.superJackpotActive = false;
    pinballMultiballGame.bonusMultiplier = 1;
    pinballMultiballGame.skillShotActive = true;
    pinballMultiballGame.gameStartTime = Date.now();
    pinballMultiballGame.ballTime = Date.now();
    
    // Initialize balls array
    pinballMultiballGame.balls = [];
    
    document.getElementById('startMultiball').disabled = true;
    
    // Initialize first ball
    addNewBall();
    
    // Start game timer
    pinballMultiballGame.gameTimer = setInterval(() => {
        const elapsed = Math.floor((Date.now() - pinballMultiballGame.gameStartTime) / 1000);
        const timeLeft = Math.max(0, diffData.timeLimit - elapsed);
        
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        document.getElementById('timeLeft').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        if (timeLeft <= 0) {
            endMultiballGame('⏰ Time\'s up!');
        }
    }, 1000);
    
    // Start physics loop
    pinballMultiballGame.lastUpdate = Date.now();
    multiballGameLoop();
    
    updateMultiballDisplay();
}

function initializeMultiballTable() {
    // Initialize bumpers (more for multiball chaos)
    pinballMultiballGame.bumpers = [
        { x: 120, y: 180, radius: 25, active: true, hits: 0 },
        { x: 200, y: 160, radius: 25, active: true, hits: 0 },
        { x: 280, y: 180, radius: 25, active: true, hits: 0 },
        { x: 380, y: 180, radius: 25, active: true, hits: 0 },
        { x: 150, y: 260, radius: 20, active: true, hits: 0 },
        { x: 250, y: 240, radius: 20, active: true, hits: 0 },
        { x: 350, y: 260, radius: 20, active: true, hits: 0 }
    ];
    
    // Initialize targets
    pinballMultiballGame.targets = [
        { x: 80, y: 140, width: 15, height: 40, active: true, value: 1500, hits: 0 },
        { x: 100, y: 140, width: 15, height: 40, active: true, value: 1500, hits: 0 },
        { x: 120, y: 140, width: 15, height: 40, active: true, value: 1500, hits: 0 },
        { x: 380, y: 140, width: 15, height: 40, active: true, value: 1500, hits: 0 },
        { x: 400, y: 140, width: 15, height: 40, active: true, value: 1500, hits: 0 },
        { x: 420, y: 140, width: 15, height: 40, active: true, value: 1500, hits: 0 }
    ];
    
    // Initialize ramps
    pinballMultiballGame.ramps = [
        { x1: 40, y1: 320, x2: 140, y2: 120, width: 25, active: true, value: 8000 },
        { x1: 360, y1: 120, x2: 460, y2: 320, width: 25, active: true, value: 8000 }
    ];
    
    // Initialize multiball targets (special for activating multiball)
    pinballMultiballGame.multiballTargets = [
        { x: 250, y: 100, radius: 18, active: true, value: 15000, special: 'multiball' },
        { x: 80, y: 380, radius: 15, active: true, value: 5000, special: 'multiball' },
        { x: 420, y: 380, radius: 15, active: true, value: 5000, special: 'multiball' }
    ];
    
    // Initialize bonus targets
    pinballMultiballGame.bonusTargets = [
        { x: 250, y: 80, radius: 20, active: true, value: 25000, special: 'jackpot' },
        { x: 100, y: 420, radius: 12, active: true, value: 3000, special: 'skillshot' },
        { x: 400, y: 420, radius: 12, active: true, value: 3000, special: 'superjackpot' }
    ];
}

function addNewBall() {
    const newBall = {
        x: 470,
        y: 550,
        vx: 0,
        vy: 0,
        radius: 8,
        active: true,
        id: Date.now() + Math.random()
    };
    
    pinballMultiballGame.balls.push(newBall);
    pinballMultiballGame.ballsInPlay++;
    
    // Reset plunger for new ball
    pinballMultiballGame.plunger = {
        power: 0,
        charging: false,
        released: false
    };
    
    pinballMultiballGame.skillShotActive = true;
    pinballMultiballGame.ballTime = Date.now();
}

function handleMultiballKeyDown(event) {
    if (!pinballMultiballGame.isPlaying) return;
    
    switch (event.code) {
        case 'Space':
            event.preventDefault();
            // Launch any balls that are in launch position
            const launchBalls = pinballMultiballGame.balls.filter(ball => ball.x > 450 && ball.active);
            if (launchBalls.length > 0 && !pinballMultiballGame.plunger.released) {
                pinballMultiballGame.plunger.charging = true;
            }
            break;
        case 'KeyA':
        case 'ArrowLeft':
            event.preventDefault();
            pinballMultiballGame.leftFlipper.active = true;
            break;
        case 'KeyD':
        case 'ArrowRight':
            event.preventDefault();
            pinballMultiballGame.rightFlipper.active = true;
            break;
    }
}

function handleMultiballKeyUp(event) {
    if (!pinballMultiballGame.isPlaying) return;
    
    switch (event.code) {
        case 'Space':
            event.preventDefault();
            if (pinballMultiballGame.plunger.charging && !pinballMultiballGame.plunger.released) {
                launchMultiballs();
            }
            break;
        case 'KeyA':
        case 'ArrowLeft':
            event.preventDefault();
            pinballMultiballGame.leftFlipper.active = false;
            break;
        case 'KeyD':
        case 'ArrowRight':
            event.preventDefault();
            pinballMultiballGame.rightFlipper.active = false;
            break;
    }
}

function launchMultiballs() {
    const power = Math.min(pinballMultiballGame.plunger.power, 100);
    const launchForce = (power / 100) * 20;
    
    // Launch all balls in launch position
    pinballMultiballGame.balls.forEach(ball => {
        if (ball.x > 450 && ball.active) {
            ball.vy = -launchForce;
            ball.vx = (Math.random() - 0.5) * 3;
        }
    });
    
    pinballMultiballGame.plunger.released = true;
    pinballMultiballGame.plunger.charging = false;
    pinballMultiballGame.plunger.power = 0;
    
    // Check for skill shot
    if (power >= 85 && power <= 95 && pinballMultiballGame.skillShotActive) {
        pinballMultiballGame.skillShots++;
        pinballMultiballGame.score += 35000;
        pinballMultiballGame.skillShotActive = false;
        
        showMultiballMessage('MULTIBALL SKILL SHOT! +35,000', 2000);
    }
}

function multiballGameLoop() {
    if (!pinballMultiballGame.isPlaying) return;
    
    const now = Date.now();
    const deltaTime = (now - pinballMultiballGame.lastUpdate) / 16.67;
    pinballMultiballGame.lastUpdate = now;
    
    updateMultiballPhysics(deltaTime);
    checkMultiballCollisions();
    updateMultiballDisplay();
    drawMultiballTable();
    
    pinballMultiballGame.animationId = requestAnimationFrame(multiballGameLoop);
}

function updateMultiballPhysics(deltaTime) {
    // Charge plunger
    if (pinballMultiballGame.plunger.charging) {
        pinballMultiballGame.plunger.power = Math.min(pinballMultiballGame.plunger.power + 2 * deltaTime, 100);
    }
    
    // Update all balls
    pinballMultiballGame.balls.forEach(ball => {
        if (!ball.active) return;
        
        // Apply gravity
        ball.vy += pinballMultiballGame.gravity * deltaTime;
        
        // Apply friction
        ball.vx *= Math.pow(pinballMultiballGame.friction, deltaTime);
        ball.vy *= Math.pow(pinballMultiballGame.friction, deltaTime);
        
        // Update position
        ball.x += ball.vx * deltaTime;
        ball.y += ball.vy * deltaTime;
        
        // Table boundaries
        if (ball.x - ball.radius < 0) {
            ball.x = ball.radius;
            ball.vx = Math.abs(ball.vx) * pinballMultiballGame.bounceDamping;
        }
        if (ball.x + ball.radius > pinballMultiballGame.tableWidth) {
            ball.x = pinballMultiballGame.tableWidth - ball.radius;
            ball.vx = -Math.abs(ball.vx) * pinballMultiballGame.bounceDamping;
        }
        if (ball.y - ball.radius < 0) {
            ball.y = ball.radius;
            ball.vy = Math.abs(ball.vy) * pinballMultiballGame.bounceDamping;
        }
        
        // Ball drain (bottom)
        if (ball.y > pinballMultiballGame.tableHeight + 50) {
            drainMultiball(ball);
        }
    });
    
    // Update flipper angles
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballMultiballGame.difficulty);
    const flipperSpeed = 8 * diffData.flipperPower;
    
    if (pinballMultiballGame.leftFlipper.active) {
        pinballMultiballGame.leftFlipper.angle = Math.min(pinballMultiballGame.leftFlipper.angle + flipperSpeed, 30);
    } else {
        pinballMultiballGame.leftFlipper.angle = Math.max(pinballMultiballGame.leftFlipper.angle - flipperSpeed, -30);
    }
    
    if (pinballMultiballGame.rightFlipper.active) {
        pinballMultiballGame.rightFlipper.angle = Math.max(pinballMultiballGame.rightFlipper.angle - flipperSpeed, -30);
    } else {
        pinballMultiballGame.rightFlipper.angle = Math.min(pinballMultiballGame.rightFlipper.angle + flipperSpeed, 30);
    }
}

function checkMultiballCollisions() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballMultiballGame.difficulty);
    let simultaneousHits = 0;
    
    pinballMultiballGame.balls.forEach(ball => {
        if (!ball.active) return;
        
        let ballHitSomething = false;
        
        // Bumper collisions
        pinballMultiballGame.bumpers.forEach(bumper => {
            if (!bumper.active) return;
            
            const dx = ball.x - bumper.x;
            const dy = ball.y - bumper.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < ball.radius + bumper.radius) {
                const angle = Math.atan2(dy, dx);
                const force = 8 * diffData.bumperPower;
                
                ball.vx = Math.cos(angle) * force;
                ball.vy = Math.sin(angle) * force;
                
                const overlap = ball.radius + bumper.radius - distance;
                ball.x += Math.cos(angle) * overlap;
                ball.y += Math.sin(angle) * overlap;
                
                bumper.hits++;
                pinballMultiballGame.bumperHits++;
                pinballMultiballGame.totalHits++;
                pinballMultiballGame.combo++;
                ballHitSomething = true;
                
                const score = 750 * pinballMultiballGame.multiplier * pinballMultiballGame.bonusMultiplier;
                pinballMultiballGame.score += score;
                
                bumper.glow = 10;
            }
        });
        
        // Target collisions
        pinballMultiballGame.targets.forEach(target => {
            if (!target.active) return;
            
            if (ball.x + ball.radius > target.x && 
                ball.x - ball.radius < target.x + target.width &&
                ball.y + ball.radius > target.y && 
                ball.y - ball.radius < target.y + target.height) {
                
                target.active = false;
                target.hits++;
                pinballMultiballGame.targetHits++;
                pinballMultiballGame.totalHits++;
                pinballMultiballGame.combo++;
                ballHitSomething = true;
                
                const score = target.value * pinballMultiballGame.multiplier * pinballMultiballGame.bonusMultiplier;
                pinballMultiballGame.score += score;
                
                if (ball.x < target.x || ball.x > target.x + target.width) {
                    ball.vx *= -pinballMultiballGame.bounceDamping;
                } else {
                    ball.vy *= -pinballMultiballGame.bounceDamping;
                }
                
                checkMultiballTargetGroups();
            }
        });
        
        // Multiball target collisions
        pinballMultiballGame.multiballTargets.forEach(target => {
            if (!target.active) return;
            
            const dx = ball.x - target.x;
            const dy = ball.y - target.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < ball.radius + target.radius) {
                target.active = false;
                pinballMultiballGame.multiballHits++;
                pinballMultiballGame.totalHits++;
                pinballMultiballGame.combo++;
                ballHitSomething = true;
                
                const score = target.value * pinballMultiballGame.multiplier * pinballMultiballGame.bonusMultiplier;
                pinballMultiballGame.score += score;
                
                activateMultiball();
                
                const angle = Math.atan2(dy, dx);
                ball.vx = Math.cos(angle) * 8;
                ball.vy = Math.sin(angle) * 8;
            }
        });
        
        // Bonus target collisions
        pinballMultiballGame.bonusTargets.forEach(target => {
            if (!target.active) return;
            
            const dx = ball.x - target.x;
            const dy = ball.y - target.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < ball.radius + target.radius) {
                target.active = false;
                pinballMultiballGame.totalHits++;
                pinballMultiballGame.combo++;
                ballHitSomething = true;
                
                const score = target.value * pinballMultiballGame.multiplier * pinballMultiballGame.bonusMultiplier;
                pinballMultiballGame.score += score;
                
                switch (target.special) {
                    case 'jackpot':
                        if (pinballMultiballGame.jackpotActive) {
                            const jackpotScore = pinballMultiballGame.jackpotValue * pinballMultiballGame.multiballLevel;
                            pinballMultiballGame.score += jackpotScore;
                            showMultiballMessage(`MULTIBALL JACKPOT! +${jackpotScore.toLocaleString()}`, 3000);
                            pinballMultiballGame.jackpotActive = false;
                        } else {
                            pinballMultiballGame.jackpotActive = true;
                            showMultiballMessage('JACKPOT ACTIVATED!', 2000);
                        }
                        break;
                    case 'superjackpot':
                        if (pinballMultiballGame.multiballActive) {
                            pinballMultiballGame.superJackpotActive = true;
                            const superScore = pinballMultiballGame.jackpotValue * 2 * pinballMultiballGame.multiballLevel;
                            pinballMultiballGame.score += superScore;
                            showMultiballMessage(`SUPER JACKPOT! +${superScore.toLocaleString()}`, 4000);
                        }
                        break;
                    case 'skillshot':
                        pinballMultiballGame.skillShotActive = true;
                        pinballMultiballGame.multiplier++;
                        showMultiballMessage(`MULTIPLIER UP! ${pinballMultiballGame.multiplier}x`, 2000);
                        break;
                }
                
                const angle = Math.atan2(dy, dx);
                ball.vx = Math.cos(angle) * 6;
                ball.vy = Math.sin(angle) * 6;
            }
        });
        
        // Flipper collisions
        checkMultiballFlipperCollisions(ball);
        
        if (ballHitSomething) {
            simultaneousHits++;
        }
    });
    
    // Track simultaneous hits
    if (simultaneousHits > 1) {
        pinballMultiballGame.simultaneousHits += simultaneousHits;
        const bonusScore = simultaneousHits * 5000 * pinballMultiballGame.multiplier;
        pinballMultiballGame.score += bonusScore;
        showMultiballMessage(`${simultaneousHits}x SIMULTANEOUS! +${bonusScore.toLocaleString()}`, 2000);
    }
    
    // Update max combo
    pinballMultiballGame.maxCombo = Math.max(pinballMultiballGame.maxCombo, pinballMultiballGame.combo);
    
    // Combo timeout
    if (pinballMultiballGame.combo > 0) {
        setTimeout(() => {
            pinballMultiballGame.combo = Math.max(0, pinballMultiballGame.combo - 1);
        }, 2000);
    }
}

function checkMultiballFlipperCollisions(ball) {
    const leftFlipperX = 150;
    const leftFlipperY = 520;
    const rightFlipperX = 350;
    const rightFlipperY = 520;
    const flipperLength = 60;
    
    // Left flipper
    const leftAngleRad = (pinballMultiballGame.leftFlipper.angle * Math.PI) / 180;
    const leftEndX = leftFlipperX + Math.cos(leftAngleRad) * flipperLength;
    const leftEndY = leftFlipperY + Math.sin(leftAngleRad) * flipperLength;
    
    if (checkLineCollision(ball, leftFlipperX, leftFlipperY, leftEndX, leftEndY)) {
        const force = pinballMultiballGame.leftFlipper.active ? 12 : 6;
        ball.vx = -Math.abs(ball.vx) - force;
        ball.vy = -Math.abs(ball.vy) - force;
        
        if (pinballMultiballGame.leftFlipper.active) {
            pinballMultiballGame.perfectShots++;
        }
    }
    
    // Right flipper
    const rightAngleRad = (pinballMultiballGame.rightFlipper.angle * Math.PI) / 180;
    const rightEndX = rightFlipperX + Math.cos(Math.PI - rightAngleRad) * flipperLength;
    const rightEndY = rightFlipperY + Math.sin(Math.PI - rightAngleRad) * flipperLength;
    
    if (checkLineCollision(ball, rightFlipperX, rightFlipperY, rightEndX, rightEndY)) {
        const force = pinballMultiballGame.rightFlipper.active ? 12 : 6;
        ball.vx = Math.abs(ball.vx) + force;
        ball.vy = -Math.abs(ball.vy) - force;
        
        if (pinballMultiballGame.rightFlipper.active) {
            pinballMultiballGame.perfectShots++;
        }
    }
}

function checkLineCollision(ball, x1, y1, x2, y2) {
    const A = ball.x - x1;
    const B = ball.y - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    
    if (lenSq === 0) return false;
    
    const param = dot / lenSq;
    
    let xx, yy;
    
    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }
    
    const dx = ball.x - xx;
    const dy = ball.y - yy;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    return distance < ball.radius + 5;
}

function checkMultiballTargetGroups() {
    const leftTargets = pinballMultiballGame.targets.slice(0, 3);
    const rightTargets = pinballMultiballGame.targets.slice(3, 6);
    
    if (leftTargets.every(t => !t.active)) {
        pinballMultiballGame.bonusMultiplier += 0.5;
        pinballMultiballGame.score += 20000;
        showMultiballMessage('LEFT TARGETS COMPLETE! +20,000', 2000);
        
        leftTargets.forEach(t => t.active = true);
    }
    
    if (rightTargets.every(t => !t.active)) {
        pinballMultiballGame.bonusMultiplier += 0.5;
        pinballMultiballGame.score += 20000;
        showMultiballMessage('RIGHT TARGETS COMPLETE! +20,000', 2000);
        
        rightTargets.forEach(t => t.active = true);
    }
}

function activateMultiball() {
    if (pinballMultiballGame.multiballActive) {
        // Upgrade multiball level
        pinballMultiballGame.multiballLevel++;
        pinballMultiballGame.multiplier += 1;
        showMultiballMessage(`MULTIBALL LEVEL ${pinballMultiballGame.multiballLevel}!`, 2000);
    } else {
        pinballMultiballGame.multiballActive = true;
        pinballMultiballGame.multiballStartTime = Date.now();
        pinballMultiballGame.multiplier += 2;
        
        showMultiballMessage('MULTIBALL ACTIVATED!', 3000);
    }
    
    // Add more balls up to max
    const modeData = MULTIBALL_MODES.find(m => m.name === pinballMultiballGame.gameMode);
    const ballsToAdd = Math.min(3, modeData.ballCount - pinballMultiballGame.ballsInPlay);
    
    for (let i = 0; i < ballsToAdd; i++) {
        setTimeout(() => {
            const newBall = {
                x: 250 + (Math.random() - 0.5) * 150,
                y: 300,
                vx: (Math.random() - 0.5) * 12,
                vy: -Math.random() * 10,
                radius: 8,
                active: true,
                id: Date.now() + Math.random()
            };
            pinballMultiballGame.balls.push(newBall);
            pinballMultiballGame.ballsInPlay++;
        }, i * 500);
    }
    
    // Reset multiball targets after delay
    setTimeout(() => {
        pinballMultiballGame.multiballTargets.forEach(t => t.active = true);
    }, 5000);
}

function drainMultiball(ball) {
    ball.active = false;
    pinballMultiballGame.ballsInPlay--;
    
    // Remove ball from array
    pinballMultiballGame.balls = pinballMultiballGame.balls.filter(b => b.id !== ball.id);
    
    if (pinballMultiballGame.ballsInPlay <= 0) {
        pinballMultiballGame.ballsLeft--;
        pinballMultiballGame.currentBall++;
        
        if (pinballMultiballGame.ballsLeft <= 0) {
            endMultiballGame('🎱 All balls drained!');
        } else {
            // Add new ball for next round
            addNewBall();
            pinballMultiballGame.combo = 0;
            pinballMultiballGame.skillShotActive = true;
            pinballMultiballGame.multiballActive = false;
            pinballMultiballGame.multiballLevel = 1;
            
            showMultiballMessage(`BALL ${pinballMultiballGame.currentBall}`, 2000);
        }
    }
}

function endMultiballGame(reason) {
    pinballMultiballGame.isPlaying = false;
    pinballMultiballGame.gamePhase = 'finished';
    
    if (pinballMultiballGame.gameTimer) {
        clearInterval(pinballMultiballGame.gameTimer);
    }
    
    if (pinballMultiballGame.animationId) {
        cancelAnimationFrame(pinballMultiballGame.animationId);
    }
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballMultiballGame.difficulty);
    const modeData = MULTIBALL_MODES.find(m => m.name === pinballMultiballGame.gameMode);
    
    // Ultra-strict multiball winning requirements
    const scoreRatio = pinballMultiballGame.score / diffData.targetScore;
    const baseWinChance = diffData.winRate;
    
    // Extremely demanding multiball requirements
    const minBumperHits = 35;           // Need 35+ bumper hits
    const minTargetHits = 20;           // Need 20+ target hits
    const minRampShots = 4;             // Need 4+ ramp shots
    const minSkillShots = 3;            // Need 3+ skill shots
    const minPerfectShots = 12;         // Need 12+ perfect flipper shots
    const minCombo = 15;                // Need 15+ combo
    const minMultiplier = 4;            // Need 4x+ multiplier
    const minMultiballHits = 8;         // Need 8+ multiball target hits
    const minSimultaneousHits = 5;      // Need 5+ simultaneous hits
    const minMultiballLevel = 2;        // Need level 2+ multiball
    
    const meetsAllRequirements = 
        pinballMultiballGame.bumperHits >= minBumperHits && 
        pinballMultiballGame.targetHits >= minTargetHits && 
        pinballMultiballGame.rampShots >= minRampShots &&
        pinballMultiballGame.skillShots >= minSkillShots &&
        pinballMultiballGame.perfectShots >= minPerfectShots &&
        pinballMultiballGame.maxCombo >= minCombo &&
        pinballMultiballGame.multiplier >= minMultiplier &&
        pinballMultiballGame.multiballHits >= minMultiballHits &&
        pinballMultiballGame.simultaneousHits >= minSimultaneousHits &&
        pinballMultiballGame.multiballLevel >= minMultiballLevel &&
        scoreRatio >= 1.4; // Need 140% of target score
    
    // Performance bonuses
    const ballEfficiency = (diffData.ballCount - pinballMultiballGame.currentBall + 1) / diffData.ballCount;
    const multiballBonus = pinballMultiballGame.multiballLevel / 5;
    
    const efficiencyBonus = (ballEfficiency + multiballBonus) / 2;
    const finalWinChance = meetsAllRequirements ? baseWinChance * scoreRatio * (1 + efficiencyBonus) : 0;
    
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate winnings with multiball bonuses
        const baseMultiplier = diffData.multiplier * modeData.scoreMultiplier;
        const comboBonus = (pinballMultiballGame.maxCombo - 15) * 0.1;
        const skillBonus = pinballMultiballGame.skillShots * 0.25;
        const perfectBonus = pinballMultiballGame.perfectShots * 0.06;
        const multiballBonusMultiplier = pinballMultiballGame.multiballLevel * 0.3;
        const simultaneousBonus = pinballMultiballGame.simultaneousHits * 0.1;
        const efficiencyBonusMultiplier = ballEfficiency * 0.4;
        
        const totalMultiplier = baseMultiplier * (1 + comboBonus + skillBonus + perfectBonus + multiballBonusMultiplier + simultaneousBonus + efficiencyBonusMultiplier);
        const winnings = Math.floor(pinballMultiballGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        document.getElementById('multiballResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🎯 MULTIBALL MASTER! 🎯</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        const requirements = [
            `${minBumperHits}+ Bumpers (${pinballMultiballGame.bumperHits})`,
            `${minTargetHits}+ Targets (${pinballMultiballGame.targetHits})`,
            `${minRampShots}+ Ramps (${pinballMultiballGame.rampShots})`,
            `${minSkillShots}+ Skill (${pinballMultiballGame.skillShots})`,
            `${minPerfectShots}+ Perfect (${pinballMultiballGame.perfectShots})`,
            `${minCombo}+ Combo (${pinballMultiballGame.maxCombo})`,
            `${minMultiplier}x+ Multi (${pinballMultiballGame.multiplier}x)`,
            `${minMultiballHits}+ MB Hits (${pinballMultiballGame.multiballHits})`,
            `${minSimultaneousHits}+ Simul (${pinballMultiballGame.simultaneousHits})`,
            `Level ${minMultiballLevel}+ MB (${pinballMultiballGame.multiballLevel})`,
            `140%+ Score (${Math.floor(scoreRatio * 100)}%)`
        ];
        
        document.getElementById('multiballResult').innerHTML = 
            `<span class="text-red-400">🎱 ${reason} 🎱</span>`;
        document.getElementById('winAmount').innerHTML = 
            `<div class="text-sm text-yellow-400 mt-2">Need: ${requirements.slice(0, 5).join(', ')}<br>${requirements.slice(5, 10).join(', ')}<br>${requirements.slice(10).join(', ')}</div>`;
    }
    
    updateMultiballDisplay();
    
    // Reset after delay
    setTimeout(() => {
        document.getElementById('startMultiball').disabled = false;
        document.getElementById('multiballResult').textContent = 'Select difficulty and start multiball pinball!';
        document.getElementById('winAmount').textContent = '';
        
        // Reset game state
        pinballMultiballGame.score = 0;
        pinballMultiballGame.ballsLeft = diffData.ballCount;
        pinballMultiballGame.currentBall = 1;
        pinballMultiballGame.multiplier = 1;
        pinballMultiballGame.combo = 0;
        pinballMultiballGame.maxCombo = 0;
        pinballMultiballGame.totalHits = 0;
        pinballMultiballGame.perfectShots = 0;
        pinballMultiballGame.rampShots = 0;
        pinballMultiballGame.bumperHits = 0;
        pinballMultiballGame.targetHits = 0;
        pinballMultiballGame.skillShots = 0;
        pinballMultiballGame.multiballHits = 0;
        pinballMultiballGame.simultaneousHits = 0;
        pinballMultiballGame.multiballActive = false;
        pinballMultiballGame.multiballLevel = 1;
        pinballMultiballGame.ballsInPlay = 0;
        pinballMultiballGame.jackpotActive = false;
        pinballMultiballGame.superJackpotActive = false;
        pinballMultiballGame.bonusMultiplier = 1;
        pinballMultiballGame.balls = [];
        
        updateMultiballDisplay();
        initializeMultiballTable();
        drawMultiballTable();
    }, 15000);
}

function drawMultiballTable() {
    const ctx = pinballMultiballGame.ctx;
    const canvas = pinballMultiballGame.canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw multiball background gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#2a1a4e');
    gradient.addColorStop(0.3, '#3a2a6e');
    gradient.addColorStop(0.7, '#4a3a8e');
    gradient.addColorStop(1, '#2a1a4e');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw table outline with multiball glow
    ctx.strokeStyle = pinballMultiballGame.multiballActive ? '#00ffff' : '#6666aa';
    ctx.lineWidth = 3;
    if (pinballMultiballGame.multiballActive) {
        ctx.shadowColor = '#00ffff';
        ctx.shadowBlur = 10;
    }
    ctx.strokeRect(0, 0, canvas.width, canvas.height);
    ctx.shadowBlur = 0;
    
    // Draw bumpers
    pinballMultiballGame.bumpers.forEach(bumper => {
        if (!bumper.active) return;
        
        const glowIntensity = bumper.glow || 0;
        
        if (glowIntensity > 0) {
            ctx.shadowColor = '#ff6600';
            ctx.shadowBlur = glowIntensity;
            bumper.glow--;
        }
        
        ctx.fillStyle = pinballMultiballGame.multiballActive ? '#ff4488' : '#ff3366';
        ctx.beginPath();
        ctx.arc(bumper.x, bumper.y, bumper.radius, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.shadowBlur = 0;
        
        ctx.fillStyle = '#ffaacc';
        ctx.beginPath();
        ctx.arc(bumper.x - 5, bumper.y - 5, bumper.radius * 0.3, 0, 2 * Math.PI);
        ctx.fill();
    });
    
    // Draw targets
    pinballMultiballGame.targets.forEach(target => {
        if (!target.active) return;
        
        ctx.fillStyle = pinballMultiballGame.multiballActive ? '#44ff88' : '#33ff66';
        ctx.fillRect(target.x, target.y, target.width, target.height);
        
        ctx.fillStyle = '#aaffcc';
        ctx.fillRect(target.x + 2, target.y + 2, target.width - 4, 8);
    });
    
    // Draw ramps
    pinballMultiballGame.ramps.forEach(ramp => {
        if (!ramp.active) return;
        
        ctx.strokeStyle = pinballMultiballGame.multiballActive ? '#ffff44' : '#ffff33';
        ctx.lineWidth = ramp.width;
        ctx.beginPath();
        ctx.moveTo(ramp.x1, ramp.y1);
        ctx.lineTo(ramp.x2, ramp.y2);
        ctx.stroke();
    });
    
    // Draw multiball targets
    pinballMultiballGame.multiballTargets.forEach(target => {
        if (!target.active) return;
        
        let color = '#ff33ff';
        if (pinballMultiballGame.multiballActive) color = '#ff66ff';
        
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(target.x, target.y, target.radius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Multiball symbol
        ctx.fillStyle = '#000000';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('●●●', target.x, target.y);
    });
    
    // Draw bonus targets
    pinballMultiballGame.bonusTargets.forEach(target => {
        if (!target.active) return;
        
        let color = '#ff33ff';
        if (target.special === 'jackpot') color = pinballMultiballGame.jackpotActive ? '#ffff00' : '#ffaa00';
        if (target.special === 'superjackpot') color = pinballMultiballGame.superJackpotActive ? '#00ffff' : '#0088ff';
        if (target.special === 'skillshot') color = '#ff9900';
        
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(target.x, target.y, target.radius, 0, 2 * Math.PI);
        ctx.fill();
        
        // Special symbol
        ctx.fillStyle = '#000000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        let symbol = '★';
        if (target.special === 'jackpot') symbol = '$';
        if (target.special === 'superjackpot') symbol = '$$';
        if (target.special === 'skillshot') symbol = '◆';
        
        ctx.fillText(symbol, target.x, target.y);
    });
    
    // Draw flippers
    drawMultiballFlipper(ctx, 150, 520, pinballMultiballGame.leftFlipper.angle, 60, '#cccccc');
    drawMultiballFlipper(ctx, 350, 520, 180 - pinballMultiballGame.rightFlipper.angle, 60, '#cccccc');
    
    // Draw plunger
    if (!pinballMultiballGame.plunger.released) {
        const plungerY = 550 - (pinballMultiballGame.plunger.power / 100) * 30;
        ctx.fillStyle = '#999999';
        ctx.fillRect(465, plungerY, 10, 30);
        
        // Power indicator
        ctx.fillStyle = `hsl(${120 - pinballMultiballGame.plunger.power * 1.2}, 70%, 50%)`;
        ctx.fillRect(480, 520, 15, -(pinballMultiballGame.plunger.power / 100) * 60);
    }
    
    // Draw all balls
    pinballMultiballGame.balls.forEach(ball => {
        if (ball.active) {
            drawMultiballBall(ctx, ball);
        }
    });
    
    // Draw multiball effects
    if (pinballMultiballGame.multiballActive) {
        // Multiball glow effect
        ctx.strokeStyle = '#00ffff';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
        ctx.setLineDash([]);
        
        // Multiball level indicator
        ctx.fillStyle = '#00ffff';
        ctx.font = 'bold 16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`MULTIBALL LEVEL ${pinballMultiballGame.multiballLevel}`, canvas.width / 2, 30);
    }
    
    // Draw special effects text
    if (pinballMultiballGame.messageText && pinballMultiballGame.messageTime > Date.now()) {
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 20px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.strokeText(pinballMultiballGame.messageText, canvas.width / 2, 100);
        ctx.fillText(pinballMultiballGame.messageText, canvas.width / 2, 100);
    }
}

function drawMultiballFlipper(ctx, x, y, angle, length, color) {
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate((angle * Math.PI) / 180);
    
    ctx.fillStyle = color;
    ctx.fillRect(0, -8, length, 16);
    
    // Flipper highlight
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(2, -6, length - 4, 4);
    
    ctx.restore();
}

function drawMultiballBall(ctx, ball) {
    // Ball shadow
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.beginPath();
    ctx.arc(ball.x + 2, ball.y + 2, ball.radius, 0, 2 * Math.PI);
    ctx.fill();
    
    // Ball gradient with multiball glow
    const ballGradient = ctx.createRadialGradient(
        ball.x - 3, ball.y - 3, 0,
        ball.x, ball.y, ball.radius
    );
    
    if (pinballMultiballGame.multiballActive) {
        ballGradient.addColorStop(0, '#ffffff');
        ballGradient.addColorStop(0.3, '#ccffff');
        ballGradient.addColorStop(1, '#0088ff');
    } else {
        ballGradient.addColorStop(0, '#ffffff');
        ballGradient.addColorStop(0.3, '#cccccc');
        ballGradient.addColorStop(1, '#666666');
    }
    
    ctx.fillStyle = ballGradient;
    ctx.beginPath();
    ctx.arc(ball.x, ball.y, ball.radius, 0, 2 * Math.PI);
    ctx.fill();
    
    // Multiball trail effect
    if (pinballMultiballGame.multiballActive && (Math.abs(ball.vx) > 5 || Math.abs(ball.vy) > 5)) {
        ctx.fillStyle = 'rgba(0, 255, 255, 0.3)';
        ctx.beginPath();
        ctx.arc(ball.x - ball.vx * 0.5, ball.y - ball.vy * 0.5, ball.radius * 0.8, 0, 2 * Math.PI);
        ctx.fill();
    }
}

function showMultiballMessage(text, duration) {
    pinballMultiballGame.messageText = text;
    pinballMultiballGame.messageTime = Date.now() + duration;
}

function updateMultiballDisplay() {
    document.getElementById('currentScore').textContent = pinballMultiballGame.score.toLocaleString();
    document.getElementById('ballsInPlay').textContent = pinballMultiballGame.ballsInPlay;
    document.getElementById('multiballLevel').textContent = pinballMultiballGame.multiballLevel;
    document.getElementById('currentCombo').textContent = pinballMultiballGame.combo;
    document.getElementById('ballsLeft').textContent = pinballMultiballGame.ballsLeft;
    document.getElementById('bonusMultiplier').textContent = pinballMultiballGame.bonusMultiplier.toFixed(1) + 'x';
    
    // Multiball progress bar
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === pinballMultiballGame.difficulty);
    const progress = Math.min(100, (pinballMultiballGame.score / diffData.targetScore) * 100);
    document.getElementById('multiballBar').style.width = progress + '%';
    document.getElementById('multiballProgress').textContent = Math.floor(progress) + '%';
    
    // Performance stats
    document.getElementById('bumperHits').textContent = pinballMultiballGame.bumperHits;
    document.getElementById('targetHits').textContent = pinballMultiballGame.targetHits;
    document.getElementById('rampShots').textContent = pinballMultiballGame.rampShots;
    document.getElementById('skillShots').textContent = pinballMultiballGame.skillShots;
    document.getElementById('multiballHits').textContent = pinballMultiballGame.multiballHits;
    document.getElementById('simultaneousHits').textContent = pinballMultiballGame.simultaneousHits;
    
    // Special features
    document.getElementById('multiballStatus').textContent = pinballMultiballGame.multiballActive ? 'ACTIVE' : 'READY';
    document.getElementById('jackpotValue').textContent = pinballMultiballGame.jackpotActive ? 
        pinballMultiballGame.jackpotValue.toLocaleString() : '0';
    document.getElementById('superJackpot').textContent = pinballMultiballGame.superJackpotActive ? 'ACTIVE' : 'OFF';
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMultiballPinballGame();
});
