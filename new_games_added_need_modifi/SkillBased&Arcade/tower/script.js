// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadTowerGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                            <h4 class="text-xl font-bold mb-4 text-red-400">DRAGON TOWER</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="towerBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">DIFFICULTY</label>
                                <select id="towerDifficulty" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="easy">Easy (2 choices)</option>
                                    <option value="medium" selected>Medium (3 choices)</option>
                                    <option value="hard">Hard (4 choices)</option>
                                </select>
                            </div>
                            
                            <button id="startTower" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                ENTER TOWER
                            </button>
                            
                            <button id="cashoutTower" class="w-full py-3 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white mb-4" disabled>
                                ESCAPE TOWER
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Level</div>
                                <div id="towerLevel" class="text-2xl font-bold text-red-400 neon-glow">0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Current Multiplier</div>
                                <div id="towerMultiplier" class="text-2xl font-bold text-green-400 neon-glow">1.00x</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="towerPotentialWin" class="text-xl font-bold text-yellow-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Difficulty Info -->
                        <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-red-400">TOWER INFO</h5>
                            <div class="text-sm space-y-1 text-gray-300">
                                <div>🏰 Climb 10 levels to win</div>
                                <div>🐉 Avoid the dragons</div>
                                <div>💎 Each level multiplies your bet</div>
                                <div>⚡ Cash out anytime to keep winnings</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tower Display -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                            <div id="towerDisplay" class="relative bg-black/50 rounded-lg p-4 h-96 overflow-y-auto">
                                <div id="towerLevels" class="space-y-2">
                                    <!-- Tower levels will be generated here -->
                                </div>
                            </div>
                            <div id="towerStatus" class="text-center mt-4 text-lg font-semibold">Choose your difficulty and enter the tower</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeTower();
        }
        
        let towerGame = {
            isPlaying: false,
            currentLevel: 0,
            betAmount: 0,
            difficulty: 'medium',
            choices: 3,
            levels: [],
            dragonPositions: []
        };
        
        function initializeTower() {
            document.getElementById('startTower').addEventListener('click', startTowerGame);
            document.getElementById('cashoutTower').addEventListener('click', cashoutTower);
            document.getElementById('towerDifficulty').addEventListener('change', updateTowerDifficulty);
            
            updateTowerDifficulty();
            generateTowerLevels();
        }
        
        function updateTowerDifficulty() {
            const difficulty = document.getElementById('towerDifficulty').value;
            towerGame.difficulty = difficulty;
            
            switch(difficulty) {
                case 'easy':
                    towerGame.choices = 2;
                    break;
                case 'medium':
                    towerGame.choices = 3;
                    break;
                case 'hard':
                    towerGame.choices = 4;
                    break;
            }
            
            if (!towerGame.isPlaying) {
                generateTowerLevels();
            }
        }
        
        function generateTowerLevels() {
            const levelsContainer = document.getElementById('towerLevels');
            levelsContainer.innerHTML = '';
            
            // Generate 10 levels
            for (let level = 10; level >= 1; level--) {
                const levelDiv = document.createElement('div');
                levelDiv.className = 'flex items-center justify-between mb-2';
                levelDiv.innerHTML = `
                    <div class="text-sm text-gray-400 w-8">L${level}</div>
                    <div class="flex space-x-2 flex-1 justify-center" id="level-${level}">
                        ${Array(towerGame.choices).fill(0).map((_, i) => 
                            `<div class="tower-choice w-12 h-12 bg-gradient-to-br from-red-600 to-purple-600 border border-red-400 rounded-lg cursor-pointer flex items-center justify-center text-lg transition-all hover:scale-105 neon-border" data-level="${level}" data-choice="${i}">
                                ?
                            </div>`
                        ).join('')}
                    </div>
                    <div class="text-sm text-yellow-400 w-16 text-right">${getTowerMultiplier(level).toFixed(2)}x</div>
                `;
                levelsContainer.appendChild(levelDiv);
            }
        }
        
        function getTowerMultiplier(level) {
            const baseMultiplier = Math.pow(1.5, level);
            const difficultyMultiplier = towerGame.choices === 2 ? 0.8 : towerGame.choices === 3 ? 1 : 1.3;
            return baseMultiplier * difficultyMultiplier;
        }
        
        function startTowerGame() {
            const betAmount = parseInt(document.getElementById('towerBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize game state
            towerGame.isPlaying = true;
            towerGame.currentLevel = 0;
            towerGame.betAmount = betAmount;
            towerGame.dragonPositions = [];
            
            // Generate dragon positions for each level
            for (let level = 1; level <= 10; level++) {
                const dragonPosition = Math.floor(Math.random() * towerGame.choices);
                towerGame.dragonPositions[level] = dragonPosition;
            }
            
            // Update UI
            document.getElementById('startTower').disabled = true;
            document.getElementById('cashoutTower').disabled = false;
            document.getElementById('towerStatus').textContent = 'Choose a path on level 1. Avoid the dragons!';
            
            // Enable level 1 choices
            enableTowerLevel(1);
            updateTowerDisplay();
        }
        
        function enableTowerLevel(level) {
            const choices = document.querySelectorAll(`[data-level="${level}"]`);
            choices.forEach(choice => {
                choice.classList.remove('opacity-50', 'cursor-not-allowed');
                choice.classList.add('cursor-pointer', 'hover:scale-105');
                choice.addEventListener('click', () => makeTowerChoice(level, parseInt(choice.dataset.choice)));
            });
        }
        
        function disableTowerLevel(level) {
            const choices = document.querySelectorAll(`[data-level="${level}"]`);
            choices.forEach(choice => {
                choice.classList.add('opacity-50', 'cursor-not-allowed');
                choice.classList.remove('cursor-pointer', 'hover:scale-105');
                choice.replaceWith(choice.cloneNode(true)); // Remove event listeners
            });
        }
        
        function makeTowerChoice(level, choice) {
            if (!towerGame.isPlaying || level !== towerGame.currentLevel + 1) return;
            
            const choiceElement = document.querySelector(`[data-level="${level}"][data-choice="${choice}"]`);
            const dragonPosition = towerGame.dragonPositions[level];
            
            if (choice === dragonPosition) {
                // Hit a dragon - game over
                choiceElement.textContent = '🐉';
                choiceElement.className = 'w-12 h-12 bg-red-600 border border-red-400 rounded-lg flex items-center justify-center text-lg neon-glow';
                
                // Reveal all dragons on this level
                const levelChoices = document.querySelectorAll(`[data-level="${level}"]`);
                levelChoices.forEach((el, i) => {
                    if (i === dragonPosition && i !== choice) {
                        el.textContent = '🐉';
                        el.className = 'w-12 h-12 bg-red-800 border border-red-600 rounded-lg flex items-center justify-center text-lg';
                    } else if (i !== dragonPosition) {
                        el.textContent = '💎';
                        el.className = 'w-12 h-12 bg-green-600 border border-green-400 rounded-lg flex items-center justify-center text-lg';
                    }
                });
                
                endTowerGame(false, 0);
            } else {
                // Safe choice
                choiceElement.textContent = '💎';
                choiceElement.className = 'w-12 h-12 bg-green-600 border border-green-400 rounded-lg flex items-center justify-center text-lg neon-glow';
                
                towerGame.currentLevel = level;
                disableTowerLevel(level);
                
                // Check if reached the top
                if (level === 10) {
                    // Won the game!
                    const finalMultiplier = getTowerMultiplier(10);
                    const winnings = Math.floor(towerGame.betAmount * finalMultiplier);
                    balance += winnings;
                    updateBalance();
                    
                    endTowerGame(true, winnings);
                } else {
                    // Continue to next level
                    enableTowerLevel(level + 1);
                    document.getElementById('towerStatus').textContent = `Level ${level} cleared! Choose path for level ${level + 1}`;
                }
                
                updateTowerDisplay();
            }
        }
        
        function updateTowerDisplay() {
            document.getElementById('towerLevel').textContent = towerGame.currentLevel;
            
            if (towerGame.currentLevel > 0) {
                const multiplier = getTowerMultiplier(towerGame.currentLevel);
                document.getElementById('towerMultiplier').textContent = multiplier.toFixed(2) + 'x';
                document.getElementById('towerPotentialWin').textContent = 
                    '$' + Math.floor(towerGame.betAmount * multiplier);
            } else {
                document.getElementById('towerMultiplier').textContent = '1.00x';
                document.getElementById('towerPotentialWin').textContent = '0 GA';
            }
        }
        
        function cashoutTower() {
            if (!towerGame.isPlaying || towerGame.currentLevel === 0) return;
            
            const multiplier = getTowerMultiplier(towerGame.currentLevel);
            const winnings = Math.floor(towerGame.betAmount * multiplier);
            balance += winnings;
            updateBalance();
            
            endTowerGame(true, winnings);
        }
        
        function endTowerGame(won, winnings) {
            towerGame.isPlaying = false;
            
            // Update UI
            document.getElementById('startTower').disabled = false;
            document.getElementById('cashoutTower').disabled = true;
            
            if (won) {
                if (towerGame.currentLevel === 10) {
                    document.getElementById('towerStatus').innerHTML = 
                        `<span class="text-yellow-400 neon-glow">TOWER CONQUERED! Won $${winnings}!</span>`;
                } else {
                    document.getElementById('towerStatus').innerHTML = 
                        `<span class="text-green-400 neon-glow">Escaped at level ${towerGame.currentLevel}! Won $${winnings}!</span>`;
                }
            } else {
                document.getElementById('towerStatus').innerHTML = 
                    `<span class="text-red-400">Dragon caught you on level ${towerGame.currentLevel + 1}!</span>`;
            }
            
            // Disable all levels
            for (let level = 1; level <= 10; level++) {
                disableTowerLevel(level);
            }
            
            // Reset after delay
            setTimeout(() => {
                towerGame.currentLevel = 0;
                updateTowerDisplay();
                generateTowerLevels();
                document.getElementById('towerStatus').textContent = 'Choose your difficulty and enter the tower';
            }, 3000);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadTowerGame();
});