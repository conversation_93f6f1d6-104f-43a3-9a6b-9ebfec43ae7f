// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadSamuraiGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                            <h4 class="text-xl font-bold mb-4 text-blue-400">BLUE SAMURAI</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="samuraiBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">HONOR LEVEL</label>
                                <select id="samuraiHonor" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="1">Novice (1x base)</option>
                                    <option value="2" selected>Warrior (2x base)</option>
                                    <option value="3">Master (3x base)</option>
                                    <option value="5">Legend (5x base)</option>
                                </select>
                            </div>
                            
                            <button id="startSamuraiQuest" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                BEGIN QUEST
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Honor Points</div>
                                <div id="samuraiHonorPoints" class="text-xl font-bold text-blue-400 neon-glow">0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Quest Reward</div>
                                <div id="samuraiReward" class="text-xl font-bold text-green-400">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Honor Multiplier</div>
                                <div id="samuraiMultiplier" class="text-lg font-bold text-yellow-400">1x</div>
                            </div>
                        </div>
                        
                        <!-- Samurai Stats -->
                        <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-blue-400">WARRIOR STATUS</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Strength:</span>
                                    <span id="samuraiStrength" class="text-red-400">50</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Agility:</span>
                                    <span id="samuraiAgility" class="text-green-400">50</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Wisdom:</span>
                                    <span id="samuraiWisdom" class="text-blue-400">50</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">Honor:</span>
                                    <span id="samuraiHonorLevel" class="text-purple-400">Warrior</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quest Symbols -->
                        <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-blue-400">QUEST SYMBOLS</h5>
                            <div class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span>⚔️ Katana:</span>
                                    <span class="text-blue-400">10x-100x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>🏯 Temple:</span>
                                    <span class="text-blue-400">5x-50x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>🎋 Bamboo:</span>
                                    <span class="text-blue-400">3x-25x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>🌸 Sakura:</span>
                                    <span class="text-blue-400">2x-15x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>🪙 Coin:</span>
                                    <span class="text-blue-400">1x-5x</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quest Arena -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                            <div id="samuraiArena" class="relative bg-gradient-to-br from-blue-900 to-indigo-900 rounded-lg p-6 h-96">
                                <!-- Quest Content -->
                                <div id="samuraiQuest" class="text-center h-full flex flex-col justify-center">
                                    <div class="text-6xl mb-4">⚔️</div>
                                    <h3 class="text-2xl font-bold text-blue-400 mb-4">The Way of the Blue Samurai</h3>
                                    <p class="text-gray-300 mb-6">
                                        Face the trials of the digital dojo. Your honor level determines the 
                                        multiplier for your quest rewards. Higher honor brings greater risk 
                                        but legendary rewards await the brave.
                                    </p>
                                </div>
                                
                                <!-- Quest Trials (Hidden initially) -->
                                <div id="questTrials" class="hidden h-full">
                                    <div class="grid grid-cols-3 gap-4 h-full">
                                        <div id="trial0" class="trial-card bg-black/50 rounded-lg p-4 cursor-pointer border-2 border-blue-500/30 hover:border-blue-500 transition-all">
                                            <div class="text-center h-full flex flex-col justify-center">
                                                <div class="text-4xl mb-2">⚔️</div>
                                                <div class="text-sm text-blue-400">Strength Trial</div>
                                            </div>
                                        </div>
                                        <div id="trial1" class="trial-card bg-black/50 rounded-lg p-4 cursor-pointer border-2 border-green-500/30 hover:border-green-500 transition-all">
                                            <div class="text-center h-full flex flex-col justify-center">
                                                <div class="text-4xl mb-2">🏃</div>
                                                <div class="text-sm text-green-400">Agility Trial</div>
                                            </div>
                                        </div>
                                        <div id="trial2" class="trial-card bg-black/50 rounded-lg p-4 cursor-pointer border-2 border-purple-500/30 hover:border-purple-500 transition-all">
                                            <div class="text-center h-full flex flex-col justify-center">
                                                <div class="text-4xl mb-2">🧠</div>
                                                <div class="text-sm text-purple-400">Wisdom Trial</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div id="trialResult" class="absolute inset-0 bg-black/80 rounded-lg hidden flex items-center justify-center">
                                        <div class="text-center">
                                            <div id="resultSymbol" class="text-8xl mb-4">⚔️</div>
                                            <div id="resultText" class="text-2xl font-bold mb-2 text-blue-400">Victory!</div>
                                            <div id="resultReward" class="text-xl text-green-400">+500 Honor</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="samuraiStatus" class="text-center mt-4 text-lg font-semibold">Choose your honor level and begin your quest</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeSamurai();
        }
        
        let samuraiGame = {
            isQuesting: false,
            honorLevel: 2,
            honorPoints: 0,
            strength: 50,
            agility: 50,
            wisdom: 50,
            betAmount: 0,
            currentTrial: null
        };
        
        function initializeSamurai() {
            document.getElementById('startSamuraiQuest').addEventListener('click', startSamuraiQuest);
            document.getElementById('samuraiHonor').addEventListener('change', updateSamuraiHonor);
            
            // Add trial event listeners
            for (let i = 0; i < 3; i++) {
                document.getElementById(`trial${i}`).addEventListener('click', () => selectTrial(i));
            }
            
            updateSamuraiStats();
        }
        
        function updateSamuraiHonor() {
            samuraiGame.honorLevel = parseInt(document.getElementById('samuraiHonor').value);
            
            const honorNames = { 1: 'Novice', 2: 'Warrior', 3: 'Master', 5: 'Legend' };
            document.getElementById('samuraiHonorLevel').textContent = honorNames[samuraiGame.honorLevel];
            document.getElementById('samuraiMultiplier').textContent = samuraiGame.honorLevel + 'x';
            
            updateSamuraiStats();
        }
        
        function updateSamuraiStats() {
            document.getElementById('samuraiHonorPoints').textContent = samuraiGame.honorPoints;
            document.getElementById('samuraiStrength').textContent = samuraiGame.strength;
            document.getElementById('samuraiAgility').textContent = samuraiGame.agility;
            document.getElementById('samuraiWisdom').textContent = samuraiGame.wisdom;
        }
        
        function startSamuraiQuest() {
            const betAmount = parseInt(document.getElementById('samuraiBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize quest
            samuraiGame.isQuesting = true;
            samuraiGame.betAmount = betAmount;
            
            // Generate random stats
            samuraiGame.strength = Math.floor(Math.random() * 50) + 25;
            samuraiGame.agility = Math.floor(Math.random() * 50) + 25;
            samuraiGame.wisdom = Math.floor(Math.random() * 50) + 25;
            
            updateSamuraiStats();
            
            // Show trials
            document.getElementById('samuraiQuest').classList.add('hidden');
            document.getElementById('questTrials').classList.remove('hidden');
            document.getElementById('startSamuraiQuest').disabled = true;
            document.getElementById('samuraiStatus').textContent = 'Choose your trial path to test your honor';
        }
        
        function selectTrial(trialIndex) {
            if (!samuraiGame.isQuesting || samuraiGame.currentTrial !== null) return;
            
            samuraiGame.currentTrial = trialIndex;
            
            // Disable trial cards
            document.querySelectorAll('.trial-card').forEach(card => {
                card.style.pointerEvents = 'none';
                card.style.opacity = '0.5';
            });
            
            // Highlight selected trial
            document.getElementById(`trial${trialIndex}`).style.opacity = '1';
            document.getElementById(`trial${trialIndex}`).style.transform = 'scale(1.05)';
            
            // Process trial after delay
            setTimeout(() => {
                processTrial(trialIndex);
            }, 1000);
        }
        
        function processTrial(trialIndex) {
            const trials = ['strength', 'agility', 'wisdom'];
            const trialType = trials[trialIndex];
            const playerStat = samuraiGame[trialType];
            
            // Calculate success based on stat and honor level
            const difficulty = 30 + (samuraiGame.honorLevel * 15);
            const successChance = Math.max(10, Math.min(90, playerStat - difficulty + 50));
            const success = Math.random() * 100 < successChance;
            
            // Calculate rewards
            let honorGain = 0;
            let multiplier = 1;
            let symbol = '⚔️';
            let resultText = '';
            
            if (success) {
                honorGain = samuraiGame.honorLevel * 100;
                multiplier = samuraiGame.honorLevel * (Math.random() * 3 + 2); // 2x-5x base
                
                const symbols = ['⚔️', '🏯', '🎋', '🌸', '🪙'];
                const values = [100, 50, 25, 15, 5];
                
                // Higher success gives better symbols
                const symbolIndex = Math.floor((100 - successChance) / 20);
                symbol = symbols[Math.min(symbolIndex, 4)];
                multiplier *= values[Math.min(symbolIndex, 4)] / 10;
                
                resultText = 'Victory Achieved!';
            } else {
                honorGain = Math.floor(samuraiGame.honorLevel * 25); // Consolation honor
                multiplier = 0.5; // Small consolation prize
                symbol = '💀';
                resultText = 'Trial Failed...';
            }
            
            // Apply results
            samuraiGame.honorPoints += honorGain;
            const winnings = Math.floor(samuraiGame.betAmount * multiplier);
            balance += winnings;
            updateBalance();
            updateSamuraiStats();
            
            // Show result
            document.getElementById('resultSymbol').textContent = symbol;
            document.getElementById('resultText').textContent = resultText;
            document.getElementById('resultText').className = success ? 
                'text-2xl font-bold mb-2 text-green-400' : 
                'text-2xl font-bold mb-2 text-red-400';
            document.getElementById('resultReward').innerHTML = `
                <div>+${honorGain} Honor</div>
                <div class="text-yellow-400">Won $${winnings}</div>
            `;
            
            document.getElementById('samuraiReward').textContent = '$' + winnings;
            document.getElementById('trialResult').classList.remove('hidden');
            
            // Update status
            if (success) {
                document.getElementById('samuraiStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Honor restored! ${trialType.charAt(0).toUpperCase() + trialType.slice(1)} trial completed - Won $${winnings}</span>`;
            } else {
                document.getElementById('samuraiStatus').innerHTML = 
                    `<span class="text-red-400">Trial failed... The path of honor continues - Won $${winnings}</span>`;
            }
            
            // End quest after delay
            setTimeout(() => {
                endSamuraiQuest();
            }, 3000);
        }
        
        function endSamuraiQuest() {
            samuraiGame.isQuesting = false;
            samuraiGame.currentTrial = null;
            
            // Reset UI
            document.getElementById('samuraiQuest').classList.remove('hidden');
            document.getElementById('questTrials').classList.add('hidden');
            document.getElementById('trialResult').classList.add('hidden');
            document.getElementById('startSamuraiQuest').disabled = false;
            
            // Reset trial cards
            document.querySelectorAll('.trial-card').forEach(card => {
                card.style.pointerEvents = 'auto';
                card.style.opacity = '1';
                card.style.transform = 'scale(1)';
            });
            
            document.getElementById('samuraiStatus').textContent = 'Choose your honor level and begin your quest';
            document.getElementById('samuraiReward').textContent = '0 GA';
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSamuraiGame();
});