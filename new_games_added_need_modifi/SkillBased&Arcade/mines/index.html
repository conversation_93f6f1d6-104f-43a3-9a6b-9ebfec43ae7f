<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyber Mines - GoldenAura</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="min-h-screen text-white">
    <!-- Cyber Grid Background -->
    <div class="cyber-grid fixed inset-0 pointer-events-none"></div>
    
    <!-- Header -->
    <header class="relative z-10 border-b border-purple-500/30 bg-black/50 backdrop-blur-sm">
        <div class="container mx-auto px-4 py-4 flex items-center justify-between">
            <h1 class="text-4xl font-bold font-mono glitch-effect neon-glow text-purple-400" data-text="GoldenAura">
                GoldenAura
            </h1>
            <div class="flex items-center space-x-4">
                <div id="balanceDisplay" class="text-xl font-semibold">
                    Balance: <span class="text-green-400 neon-glow">1000 GA</span>
                </div>
                <button onclick="window.location.href='../../index.html'" class="cyber-button px-6 py-2 rounded-lg font-semibold text-white">
                    BACK TO LOBBY
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="relative z-10 container mx-auto px-4 py-8">
        <!-- Game Title -->
        <section class="text-center mb-8">
            <h2 class="text-5xl font-black mb-4 font-mono text-purple-400 neon-glow">
                CYBER MINES
            </h2>
            <p class="text-xl text-blue-300 max-w-2xl mx-auto">
                Navigate the digital minefield and multiply your rewards
            </p>
        </section>

        <!-- Game Area -->
        <section class="game-card p-8 rounded-xl">
            <div id="gameContent">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                            <h4 class="text-xl font-bold mb-4 text-purple-400">GAME SETTINGS</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="minesBet" value="10" min="1" max="1000" 
                                       class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">MINES COUNT</label>
                                <select id="minesCount" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="3">3 Mines</option>
                                    <option value="5" selected>5 Mines</option>
                                    <option value="8">8 Mines</option>
                                    <option value="12">12 Mines</option>
                                </select>
                            </div>
                            
                            <button id="startMines" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                START GAME
                            </button>
                            
                            <button id="cashoutMines" class="w-full py-3 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white mb-4" disabled>
                                CASH OUT
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Multiplier</div>
                                <div id="minesMultiplier" class="text-2xl font-bold text-green-400 neon-glow">1.00x</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Game Board -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                            <div id="minesBoard" class="grid grid-cols-5 gap-2 max-w-md mx-auto">
                                <!-- Board will be generated here -->
                            </div>
                            <div id="minesStatus" class="text-center mt-4 text-lg font-semibold"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="relative z-10 border-t border-purple-500/30 bg-black/50 backdrop-blur-sm mt-16">
        <div class="container mx-auto px-4 py-6 text-center">
            <p class="text-gray-400">© 2024 GoldenAura. Enter the future of gaming.</p>
            <p class="text-sm text-gray-500 mt-2">Play responsibly. Must be 18+ to play.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>