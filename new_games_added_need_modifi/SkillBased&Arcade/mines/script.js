// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

let minesGame = {
    board: [],
    mines: [],
    revealed: [],
    gameActive: false,
    betAmount: 0,
    tilesRevealed: 0
};

function initializeMines() {
    const board = document.getElementById('minesBoard');
    board.innerHTML = '';
    
    // Create 25 tiles (5x5 grid)
    for (let i = 0; i < 25; i++) {
        const tile = document.createElement('div');
        tile.className = 'w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 border border-purple-400 rounded-lg cursor-pointer flex items-center justify-center text-lg font-bold transition-all hover:scale-105 neon-border';
        tile.dataset.index = i;
        tile.addEventListener('click', () => revealTile(i));
        board.appendChild(tile);
    }
    
    // Event listeners
    document.getElementById('startMines').addEventListener('click', startMinesGame);
    document.getElementById('cashoutMines').addEventListener('click', cashoutMines);
}

function startMinesGame() {
    const betAmount = parseInt(document.getElementById('minesBet').value);
    const minesCount = parseInt(document.getElementById('minesCount').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    // Reset game state
    minesGame.board = Array(25).fill(0);
    minesGame.mines = [];
    minesGame.revealed = Array(25).fill(false);
    minesGame.gameActive = true;
    minesGame.betAmount = betAmount;
    minesGame.tilesRevealed = 0;
    
    // Place mines randomly
    while (minesGame.mines.length < minesCount) {
        const pos = Math.floor(Math.random() * 25);
        if (!minesGame.mines.includes(pos)) {
            minesGame.mines.push(pos);
            minesGame.board[pos] = 1; // 1 = mine
        }
    }
    
    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();
    
    // Update UI
    document.getElementById('startMines').disabled = true;
    document.getElementById('cashoutMines').disabled = false;
    document.getElementById('minesStatus').textContent = 'Click tiles to reveal them. Avoid the mines!';
    updateMinesMultiplier();
    
    // Reset board visual
    const tiles = document.querySelectorAll('#minesBoard > div');
    tiles.forEach(tile => {
        tile.className = 'w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 border border-purple-400 rounded-lg cursor-pointer flex items-center justify-center text-lg font-bold transition-all hover:scale-105 neon-border';
        tile.textContent = '';
    });
}

function revealTile(index) {
    if (!minesGame.gameActive || minesGame.revealed[index]) return;
    
    const tile = document.querySelector(`[data-index="${index}"]`);
    minesGame.revealed[index] = true;
    
    if (minesGame.board[index] === 1) {
        // Hit a mine - game over
        tile.className = 'w-12 h-12 bg-red-600 border border-red-400 rounded-lg flex items-center justify-center text-lg font-bold';
        tile.textContent = '💣';
        
        // Reveal all mines
        minesGame.mines.forEach(minePos => {
            if (minePos !== index) {
                const mineTile = document.querySelector(`[data-index="${minePos}"]`);
                mineTile.className = 'w-12 h-12 bg-red-800 border border-red-600 rounded-lg flex items-center justify-center text-lg font-bold';
                mineTile.textContent = '💣';
            }
        });
        
        endMinesGame(false);
    } else {
        // Safe tile
        tile.className = 'w-12 h-12 bg-green-600 border border-green-400 rounded-lg flex items-center justify-center text-lg font-bold neon-glow';
        tile.textContent = '💎';
        minesGame.tilesRevealed++;
        updateMinesMultiplier();
        
        // Check for win condition
        if (minesGame.tilesRevealed === 25 - minesGame.mines.length) {
            endMinesGame(true);
        }
    }
}

function updateMinesMultiplier() {
    const safeTiles = 25 - minesGame.mines.length;
    const multiplier = Math.pow(1.5, minesGame.tilesRevealed);
    document.getElementById('minesMultiplier').textContent = multiplier.toFixed(2) + 'x';
}

function cashoutMines() {
    if (!minesGame.gameActive) return;
    
    const safeTiles = 25 - minesGame.mines.length;
    const multiplier = Math.pow(1.5, minesGame.tilesRevealed);
    const winnings = Math.floor(minesGame.betAmount * multiplier);
    
    balance += winnings;
    updateBalance();
    
    endMinesGame(true, winnings);
}

function endMinesGame(won, winnings = 0) {
    minesGame.gameActive = false;
    document.getElementById('startMines').disabled = false;
    document.getElementById('cashoutMines').disabled = true;
    
    if (won) {
        document.getElementById('minesStatus').innerHTML = 
            `<span class="text-green-400 neon-glow">YOU WON ${winnings} GA!</span>`;
    } else {
        document.getElementById('minesStatus').innerHTML = 
            `<span class="text-red-400">BOOM! You hit a mine!</span>`;
    }
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeMines();
    updateBalance();
});