// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Fishing Tournament Implementation
const FISH_TYPES = {
    legendary: { name: 'Legendary Bass', points: 500, rarity: 0.01, size: [80, 100], color: '#ff0000' },
    trophy: { name: 'Trophy Trout', points: 200, rarity: 0.05, size: [60, 80], color: '#ffd700' },
    rare: { name: 'Rare Salmon', points: 100, rarity: 0.1, size: [40, 60], color: '#ff8000' },
    uncommon: { name: 'Uncommon Pike', points: 50, rarity: 0.2, size: [30, 50], color: '#00ff00' },
    common: { name: 'Common Perch', points: 25, rarity: 0.3, size: [20, 40], color: '#0080ff' },
    small: { name: '<PERSON> Minnow', points: 10, rarity: 0.34, size: [10, 25], color: '#808080' }
};

const TOURNAMENT_TYPES = [
    { name: 'local', description: 'Local Lake Tournament', entry: 100, duration: 60, participants: 8, multiplier: 1.0 },
    { name: 'regional', description: 'Regional Championship', entry: 300, duration: 90, participants: 16, multiplier: 1.5 },
    { name: 'national', description: 'National Pro Circuit', entry: 800, duration: 120, participants: 32, multiplier: 2.0 },
    { name: 'world', description: 'World Championship', entry: 2000, duration: 150, participants: 64, multiplier: 3.0 },
    { name: 'masters', description: 'Masters Invitational', entry: 5000, duration: 180, participants: 128, multiplier: 5.0 }
];

const FISHING_SPOTS = [
    { name: 'shallow', description: 'Shallow Waters', difficulty: 0.3, rareBonus: 0.1, timeBonus: 1.2 },
    { name: 'deep', description: 'Deep Lake', difficulty: 0.5, rareBonus: 0.3, timeBonus: 1.0 },
    { name: 'rapids', description: 'Rocky Rapids', difficulty: 0.7, rareBonus: 0.5, timeBonus: 0.8 },
    { name: 'mystical', description: 'Mystical Pond', difficulty: 0.9, rareBonus: 0.8, timeBonus: 0.6 }
];

const SKILL_LEVELS = [
    { name: 'novice', description: 'Weekend Angler', castAccuracy: 0.6, reelSpeed: 0.7, patience: 0.8, multiplier: 0.8 },
    { name: 'amateur', description: 'Club Member', castAccuracy: 0.7, reelSpeed: 0.8, patience: 0.9, multiplier: 1.0 },
    { name: 'experienced', description: 'Tournament Ready', castAccuracy: 0.8, reelSpeed: 0.9, patience: 1.0, multiplier: 1.3 },
    { name: 'professional', description: 'Pro Angler', castAccuracy: 0.9, reelSpeed: 1.0, patience: 1.1, multiplier: 1.8 },
    { name: 'master', description: 'Fishing Legend', castAccuracy: 1.0, reelSpeed: 1.2, patience: 1.3, multiplier: 2.5 }
];

let fishingTournament = {
    isActive: false,
    tournamentType: 'local',
    fishingSpot: 'shallow',
    skillLevel: 'amateur',
    entryFee: 0,
    timeLeft: 60,
    timer: null,
    canvas: null,
    ctx: null,
    isCasting: false,
    isReeling: false,
    castPower: 0,
    castAccuracy: 0,
    currentFish: null,
    reelProgress: 0,
    totalScore: 0,
    fishCaught: [],
    participants: [],
    currentRank: 1,
    tournamentLevel: 1,
    masterPoints: 0,
    perfectCatches: 0,
    legendaryFish: 0,
    trophyCollection: 0,
    sponsorshipLevel: 0,
    animationId: null,
    waterLevel: 0,
    bobberX: 0,
    bobberY: 0,
    lineLength: 0
};

function loadFishingTournament() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Tournament Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mb-6">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎣 FISHIN' FRENZY TOURNAMENT 🎣</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏆 TOURNAMENT LEVEL</label>
                        <select id="tournamentType" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${TOURNAMENT_TYPES.map(tournament => 
                                `<option value="${tournament.name}">${tournament.name.toUpperCase()} - ${tournament.description} (${tournament.entry} GA)</option>`
                            ).join('')}
                        </select>
                        <div id="tournamentInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌊 FISHING SPOT</label>
                        <select id="fishingSpot" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${FISHING_SPOTS.map(spot => 
                                `<option value="${spot.name}">${spot.name.toUpperCase()} - ${spot.description}</option>`
                            ).join('')}
                        </select>
                        <div id="spotInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ SKILL LEVEL</label>
                        <select id="skillLevel" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            ${SKILL_LEVELS.map(skill => 
                                `<option value="${skill.name}">${skill.name.toUpperCase()} - ${skill.description}</option>`
                            ).join('')}
                        </select>
                        <div id="skillInfo" class="text-xs text-gray-400 mt-1"></div>
                    </div>
                    
                    <button id="joinTournament" class="w-full cyber-button px-4 py-3 rounded-lg font-semibold text-white mb-4">
                        JOIN TOURNAMENT
                    </button>
                    
                    <div class="text-center mb-4">
                        <div id="tournamentTimer" class="text-2xl font-bold text-cyan-400 mb-2">1:00</div>
                        <div id="tournamentStatus" class="text-sm text-gray-300">Ready to compete in fishing tournament</div>
                    </div>
                    
                    <!-- Cast Power Meter -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 mb-4">
                        <div class="text-sm text-gray-300 mb-2">🎯 CAST POWER</div>
                        <div class="bg-gray-700 rounded-full h-4 mb-2">
                            <div id="castPowerMeter" class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-4 rounded-full transition-all" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <span id="castPowerLevel" class="text-white font-bold">0%</span>
                        </div>
                    </div>
                    
                    <!-- Reel Progress -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 mb-4">
                        <div class="text-sm text-gray-300 mb-2">🎣 REEL PROGRESS</div>
                        <div class="bg-gray-700 rounded-full h-3 mb-2">
                            <div id="reelProgressBar" class="bg-blue-500 h-3 rounded-full transition-all" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <span id="reelStatus" class="text-cyan-400 text-sm">Ready to cast</span>
                        </div>
                    </div>
                    
                    <!-- Current Catch -->
                    <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20">
                        <div class="text-sm text-gray-300 mb-2">🐟 CURRENT CATCH</div>
                        <div id="currentCatch" class="text-center text-yellow-400">
                            No fish on the line
                        </div>
                    </div>
                </div>
                
                <!-- Leaderboard -->
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🏆 TOURNAMENT LEADERBOARD</h5>
                    <div id="tournamentLeaderboard" class="space-y-2 max-h-48 overflow-y-auto">
                        <!-- Leaderboard will appear here -->
                    </div>
                </div>
            </div>
            
            <!-- Fishing Lake -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="flex justify-between items-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400">🌊 TOURNAMENT LAKE</h5>
                        <div class="text-right">
                            <div class="text-sm text-gray-300">Score: <span id="totalScore" class="text-yellow-400">0</span> pts</div>
                            <div class="text-sm text-gray-300">Rank: <span id="currentRank" class="text-cyan-400">#1</span></div>
                        </div>
                    </div>
                    
                    <div class="relative bg-gradient-to-b from-sky-400 to-blue-800 rounded-lg p-4" style="min-height: 500px;">
                        <canvas id="fishingCanvas" width="500" height="450" class="border border-blue-500/30 rounded-lg cursor-crosshair"></canvas>
                        
                        <!-- Fishing Bobber -->
                        <div id="fishingBobber" class="absolute w-4 h-4 bg-red-500 rounded-full hidden animate-bounce">
                            <div class="w-2 h-2 bg-white rounded-full absolute top-1 left-1"></div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <div id="fishingResult" class="text-lg font-semibold mb-2"></div>
                        <div id="fishingInstructions" class="text-sm text-gray-400">
                            Hold SPACE to charge cast power, release to cast! Click rapidly to reel in fish!
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Fishing Stats Panel -->
        <div class="grid grid-cols-2 md:grid-cols-6 gap-4 mt-8">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-purple-400" id="tournamentLevel">1</div>
                <div class="text-sm text-gray-300">Tournament Level</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-cyan-400" id="masterPoints">0</div>
                <div class="text-sm text-gray-300">Master Points</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-green-400" id="perfectCatches">0</div>
                <div class="text-sm text-gray-300">Perfect Catches</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-gold-400" id="legendaryFish">0</div>
                <div class="text-sm text-gray-300">Legendary Fish</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-yellow-400" id="trophyCollection">0</div>
                <div class="text-sm text-gray-300">Trophy Collection</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-2xl font-bold text-red-400" id="sponsorshipLevel">0</div>
                <div class="text-sm text-gray-300">Sponsorship Level</div>
            </div>
        </div>
        
        <!-- Fish Collection -->
        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30 mt-8">
            <h5 class="text-lg font-bold mb-3 text-purple-400">🐟 TOURNAMENT CATCH</h5>
            <div id="fishCollection" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <!-- Fish collection will appear here -->
            </div>
        </div>
    `;
    
    setupFishingTournament();
}

function setupFishingTournament() {
    fishingTournament.canvas = document.getElementById('fishingCanvas');
    fishingTournament.ctx = fishingTournament.canvas.getContext('2d');
    
    // Event listeners
    document.getElementById('joinTournament').addEventListener('click', startFishingTournament);
    document.getElementById('tournamentType').addEventListener('change', updateTournamentType);
    document.getElementById('fishingSpot').addEventListener('change', updateFishingSpot);
    document.getElementById('skillLevel').addEventListener('change', updateSkillLevel);
    
    // Canvas events
    fishingTournament.canvas.addEventListener('click', handleCanvasClick);
    
    // Keyboard events
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    updateTournamentType();
    updateFishingSpot();
    updateSkillLevel();
    generateParticipants();
    drawFishingLake();
    updateFishingDisplay();
}

function updateTournamentType() {
    const type = document.getElementById('tournamentType').value;
    fishingTournament.tournamentType = type;
    
    const tournamentData = TOURNAMENT_TYPES.find(t => t.name === type);
    fishingTournament.timeLeft = tournamentData.duration;
    fishingTournament.entryFee = tournamentData.entry;
    
    document.getElementById('tournamentInfo').textContent = 
        `${tournamentData.duration}s, ${tournamentData.participants} participants, ${tournamentData.multiplier}x multiplier`;
    document.getElementById('tournamentTimer').textContent = `${Math.floor(tournamentData.duration/60)}:${(tournamentData.duration%60).toString().padStart(2, '0')}`;
}

function updateFishingSpot() {
    const spot = document.getElementById('fishingSpot').value;
    fishingTournament.fishingSpot = spot;
    
    const spotData = FISHING_SPOTS.find(s => s.name === spot);
    document.getElementById('spotInfo').textContent = 
        `Difficulty: ${Math.floor(spotData.difficulty * 100)}%, Rare bonus: ${Math.floor(spotData.rareBonus * 100)}%, Time: ${Math.floor(spotData.timeBonus * 100)}%`;
}

function updateSkillLevel() {
    const skill = document.getElementById('skillLevel').value;
    fishingTournament.skillLevel = skill;
    
    const skillData = SKILL_LEVELS.find(s => s.name === skill);
    document.getElementById('skillInfo').textContent = 
        `Cast: ${Math.floor(skillData.castAccuracy * 100)}%, Reel: ${Math.floor(skillData.reelSpeed * 100)}%, Patience: ${Math.floor(skillData.patience * 100)}%`;
}

function generateParticipants() {
    const tournamentData = TOURNAMENT_TYPES.find(t => t.name === fishingTournament.tournamentType);
    fishingTournament.participants = [];
    
    const names = ['Bass Master', 'Reel Deal', 'Hook Line', 'Sinker Joe', 'Angler Ace', 'Fish Whisperer', 'Rod Bender', 'Net Caster', 'Lure Legend', 'Bait Boss'];
    
    for (let i = 0; i < tournamentData.participants - 1; i++) {
        const skill = 0.2 + (Math.random() * 0.8); // 20-100% skill
        fishingTournament.participants.push({
            name: names[i % names.length] + (i >= names.length ? ` ${Math.floor(i/names.length) + 1}` : ''),
            skill: skill,
            score: 0,
            fishCount: 0
        });
    }
    
    updateTournamentLeaderboard();
}

function startFishingTournament() {
    if (fishingTournament.entryFee > balance) {
        document.getElementById('tournamentStatus').textContent = 'Insufficient funds for tournament entry!';
        return;
    }
    
    if (fishingTournament.isActive) return;
    
    // Deduct entry fee
    balance -= fishingTournament.entryFee;
    updateBalance();
    
    // Initialize tournament
    fishingTournament.isActive = true;
    fishingTournament.totalScore = 0;
    fishingTournament.fishCaught = [];
    fishingTournament.currentRank = 1;
    
    const tournamentData = TOURNAMENT_TYPES.find(t => t.name === fishingTournament.tournamentType);
    fishingTournament.timeLeft = tournamentData.duration;
    
    // Generate participants
    generateParticipants();
    
    // Start timer
    startTournamentTimer();
    
    // Update UI
    document.getElementById('joinTournament').disabled = true;
    document.getElementById('tournamentStatus').textContent = 'Tournament active - cast your line!';
    document.getElementById('fishingInstructions').innerHTML = 
        '<span class="text-yellow-400">Hold SPACE to charge cast, release to cast! Click rapidly to reel!</span>';
    
    updateFishingDisplay();
    drawFishingLake();
    
    // Start water animation
    animateWater();
}

function handleKeyDown(e) {
    if (!fishingTournament.isActive) return;
    
    if (e.code === 'Space' && !fishingTournament.isCasting && !fishingTournament.isReeling) {
        e.preventDefault();
        fishingTournament.isCasting = true;
        chargeCast();
    }
}

function handleKeyUp(e) {
    if (!fishingTournament.isActive || !fishingTournament.isCasting) return;
    
    if (e.code === 'Space') {
        e.preventDefault();
        fishingTournament.isCasting = false;
        castLine();
    }
}

function handleCanvasClick(e) {
    if (!fishingTournament.isActive || !fishingTournament.isReeling) return;
    
    // Rapid clicking to reel in fish
    fishingTournament.reelProgress += 5;
    document.getElementById('reelProgressBar').style.width = Math.min(100, fishingTournament.reelProgress) + '%';
    
    if (fishingTournament.reelProgress >= 100) {
        catchFish();
    }
}

function chargeCast() {
    if (!fishingTournament.isCasting) return;
    
    fishingTournament.castPower = Math.min(100, fishingTournament.castPower + 3);
    document.getElementById('castPowerLevel').textContent = Math.floor(fishingTournament.castPower) + '%';
    document.getElementById('castPowerMeter').style.width = fishingTournament.castPower + '%';
    
    // Power oscillation
    if (fishingTournament.castPower >= 100) {
        fishingTournament.castPower = 0;
    }
    
    requestAnimationFrame(chargeCast);
}

function castLine() {
    const skillData = SKILL_LEVELS.find(s => s.name === fishingTournament.skillLevel);
    const spotData = FISHING_SPOTS.find(s => s.name === fishingTournament.fishingSpot);
    
    // Calculate cast accuracy based on power and skill
    const powerAccuracy = 1 - Math.abs(fishingTournament.castPower - 75) / 75; // Optimal around 75%
    const finalAccuracy = powerAccuracy * skillData.castAccuracy;
    
    // Position bobber based on cast
    const canvas = fishingTournament.canvas;
    const maxDistance = Math.min(canvas.width * 0.8, fishingTournament.castPower * 4);
    fishingTournament.bobberX = (canvas.width / 2) + (Math.random() - 0.5) * (1 - finalAccuracy) * 100;
    fishingTournament.bobberY = 100 + maxDistance * (finalAccuracy + Math.random() * 0.3);
    
    // Show bobber
    const bobber = document.getElementById('fishingBobber');
    bobber.style.left = fishingTournament.bobberX + 'px';
    bobber.style.top = fishingTournament.bobberY + 'px';
    bobber.classList.remove('hidden');
    
    // Reset cast power
    fishingTournament.castPower = 0;
    document.getElementById('castPowerLevel').textContent = '0%';
    document.getElementById('castPowerMeter').style.width = '0%';
    
    // Wait for fish
    setTimeout(() => {
        if (fishingTournament.isActive) {
            generateFishBite(finalAccuracy, spotData);
        }
    }, 2000 + Math.random() * 3000);
    
    document.getElementById('reelStatus').textContent = 'Waiting for fish...';
}

function generateFishBite(accuracy, spotData) {
    // Calculate fish probability based on spot and accuracy
    const baseChance = 0.6 + (accuracy * 0.3) + (spotData.rareBonus * 0.1);
    
    if (Math.random() < baseChance) {
        // Fish bites!
        const fishType = selectFishType(spotData);
        fishingTournament.currentFish = fishType;
        fishingTournament.isReeling = true;
        fishingTournament.reelProgress = 0;
        
        document.getElementById('currentCatch').innerHTML = 
            `<span style="color: ${fishType.color}">${fishType.name}</span>`;
        document.getElementById('reelStatus').textContent = 'Fish on the line! Click rapidly!';
        document.getElementById('fishingInstructions').innerHTML = 
            '<span class="text-red-400 animate-pulse">FISH ON THE LINE! Click rapidly to reel it in!</span>';
        
        // Fish escape timer
        setTimeout(() => {
            if (fishingTournament.isReeling && fishingTournament.reelProgress < 100) {
                fishEscapes();
            }
        }, 5000 + (fishType.rarity * 10000)); // Rarer fish are harder to catch
        
    } else {
        // No bite
        document.getElementById('reelStatus').textContent = 'No bite... try again!';
        setTimeout(() => {
            resetCast();
        }, 1500);
    }
}

function selectFishType(spotData) {
    // Adjust probabilities based on spot
    const adjustedFish = Object.entries(FISH_TYPES).map(([key, fish]) => ({
        key,
        ...fish,
        adjustedRarity: fish.rarity * (1 + spotData.rareBonus)
    }));
    
    const totalRarity = adjustedFish.reduce((sum, fish) => sum + fish.adjustedRarity, 0);
    let random = Math.random() * totalRarity;
    
    for (const fish of adjustedFish) {
        random -= fish.adjustedRarity;
        if (random <= 0) {
            return fish;
        }
    }
    
    return adjustedFish[adjustedFish.length - 1]; // Fallback
}

function catchFish() {
    if (!fishingTournament.currentFish) return;
    
    const fish = fishingTournament.currentFish;
    const skillData = SKILL_LEVELS.find(s => s.name === fishingTournament.skillLevel);
    
    // Calculate size and bonus points
    const sizeRange = fish.size[1] - fish.size[0];
    const fishSize = fish.size[0] + (Math.random() * sizeRange);
    const sizeBonus = Math.floor((fishSize / fish.size[1]) * fish.points * 0.5);
    const totalPoints = fish.points + sizeBonus;
    
    // Add to catch
    const catchData = {
        ...fish,
        size: Math.floor(fishSize),
        points: totalPoints,
        timestamp: Date.now()
    };
    
    fishingTournament.fishCaught.push(catchData);
    fishingTournament.totalScore += totalPoints;
    
    // Update stats
    if (fish.key === 'legendary') {
        fishingTournament.legendaryFish++;
        fishingTournament.sponsorshipLevel++;
    }
    if (fish.key === 'trophy') {
        fishingTournament.trophyCollection++;
    }
    if (fishSize >= fish.size[1] * 0.95) {
        fishingTournament.perfectCatches++;
    }
    
    fishingTournament.masterPoints += Math.floor(totalPoints / 10);
    
    // Show result
    document.getElementById('fishingResult').innerHTML = 
        `<span style="color: ${fish.color}" class="neon-glow">🎣 CAUGHT ${fish.name.toUpperCase()}! 🎣</span><br>
         <span class="text-yellow-400">Size: ${Math.floor(fishSize)}cm, Points: +${totalPoints}</span>`;
    
    // Simulate opponent catches
    simulateOpponentFishing();
    
    // Update displays
    updateFishingDisplay();
    updateFishCollection();
    updateTournamentLeaderboard();
    
    // Reset for next cast
    setTimeout(() => {
        resetCast();
    }, 3000);
}

function fishEscapes() {
    fishingTournament.isReeling = false;
    fishingTournament.currentFish = null;
    
    document.getElementById('fishingResult').innerHTML = 
        '<span class="text-red-400">💔 The fish got away! 💔</span>';
    document.getElementById('currentCatch').textContent = 'Fish escaped!';
    
    setTimeout(() => {
        resetCast();
    }, 2000);
}

function resetCast() {
    fishingTournament.isReeling = false;
    fishingTournament.currentFish = null;
    fishingTournament.reelProgress = 0;
    
    document.getElementById('fishingBobber').classList.add('hidden');
    document.getElementById('reelProgressBar').style.width = '0%';
    document.getElementById('reelStatus').textContent = 'Ready to cast';
    document.getElementById('currentCatch').textContent = 'No fish on the line';
    document.getElementById('fishingInstructions').innerHTML = 
        'Hold SPACE to charge cast power, release to cast! Click rapidly to reel in fish!';
    document.getElementById('fishingResult').textContent = '';
}

function simulateOpponentFishing() {
    fishingTournament.participants.forEach(participant => {
        if (Math.random() < participant.skill * 0.3) { // 30% chance per player catch
            const fishTypes = Object.values(FISH_TYPES);
            const randomFish = fishTypes[Math.floor(Math.random() * fishTypes.length)];
            const points = randomFish.points + Math.floor(Math.random() * randomFish.points * 0.5);
            
            participant.score += points;
            participant.fishCount++;
        }
    });
}

function startTournamentTimer() {
    fishingTournament.timer = setInterval(() => {
        fishingTournament.timeLeft--;
        
        const minutes = Math.floor(fishingTournament.timeLeft / 60);
        const seconds = fishingTournament.timeLeft % 60;
        document.getElementById('tournamentTimer').textContent = 
            `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        // Color coding for urgency
        const timerElement = document.getElementById('tournamentTimer');
        if (fishingTournament.timeLeft <= 30) {
            timerElement.className = 'text-2xl font-bold text-red-400 animate-pulse';
        } else if (fishingTournament.timeLeft <= 60) {
            timerElement.className = 'text-2xl font-bold text-yellow-400';
        } else {
            timerElement.className = 'text-2xl font-bold text-cyan-400';
        }
        
        if (fishingTournament.timeLeft <= 0) {
            endFishingTournament();
        }
    }, 1000);
}

function endFishingTournament() {
    fishingTournament.isActive = false;
    fishingTournament.isReeling = false;
    fishingTournament.isCasting = false;
    
    if (fishingTournament.timer) {
        clearInterval(fishingTournament.timer);
    }
    
    // Calculate final standings
    const allParticipants = [
        { name: 'YOU', score: fishingTournament.totalScore, fishCount: fishingTournament.fishCaught.length, isPlayer: true },
        ...fishingTournament.participants.map(p => ({ ...p, isPlayer: false }))
    ];
    allParticipants.sort((a, b) => b.score - a.score);
    
    const playerPosition = allParticipants.findIndex(p => p.isPlayer) + 1;
    const tournamentData = TOURNAMENT_TYPES.find(t => t.name === fishingTournament.tournamentType);
    const skillData = SKILL_LEVELS.find(s => s.name === fishingTournament.skillLevel);
    
    // Determine winnings
    const topPositions = Math.ceil(tournamentData.participants / 4); // Top 25% win
    
    if (playerPosition <= topPositions) {
        // Calculate winnings
        let baseMultiplier = tournamentData.multiplier * skillData.multiplier;
        
        // Position bonus
        const positionBonus = (topPositions - playerPosition + 1) / topPositions * 3; // Up to 3x for 1st
        
        // Performance bonus
        const avgPoints = fishingTournament.totalScore / Math.max(1, fishingTournament.fishCaught.length);
        const performanceBonus = Math.min(avgPoints / 100, 2); // Up to 2x for high avg
        
        const totalMultiplier = baseMultiplier * (1 + positionBonus + performanceBonus);
        const winnings = Math.floor(fishingTournament.entryFee * totalMultiplier * 0.75); // 75% base rate
        
        balance += winnings;
        updateBalance();
        
        // Update stats
        if (playerPosition === 1) {
            fishingTournament.tournamentLevel++;
            fishingTournament.sponsorshipLevel += 2;
        }
        
        document.getElementById('fishingResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🏆 TOURNAMENT ${playerPosition === 1 ? 'CHAMPION' : 'WINNER'}! 🏆</span>`;
        document.getElementById('tournamentStatus').innerHTML = 
            `Position #${playerPosition}! Score: ${fishingTournament.totalScore}, Won: ${winnings} GA!`;
    } else {
        document.getElementById('fishingResult').innerHTML = 
            `<span class="text-red-400">🎣 Better luck next time! 🎣</span>`;
        document.getElementById('tournamentStatus').innerHTML = 
            `Position #${playerPosition}/${allParticipants.length} - Need top ${topPositions} to win prizes!`;
    }
    
    updateFishingDisplay();
    resetTournamentControls();
}

function resetTournamentControls() {
    setTimeout(() => {
        document.getElementById('joinTournament').disabled = false;
        document.getElementById('fishingResult').textContent = '';
        document.getElementById('tournamentStatus').textContent = 'Ready to compete in fishing tournament';
        resetCast();
        
        // Reset some game state
        fishingTournament.totalScore = 0;
        fishingTournament.fishCaught = [];
        fishingTournament.currentRank = 1;
        
        generateParticipants();
        updateFishingDisplay();
        updateFishCollection();
        drawFishingLake();
    }, 8000);
}

function updateTournamentLeaderboard() {
    const leaderboard = document.getElementById('tournamentLeaderboard');
    
    // Create combined leaderboard
    const allParticipants = [
        { name: 'YOU', score: fishingTournament.totalScore, fishCount: fishingTournament.fishCaught.length, isPlayer: true },
        ...fishingTournament.participants.map(p => ({ ...p, isPlayer: false }))
    ];
    
    // Sort by score
    allParticipants.sort((a, b) => b.score - a.score);
    
    leaderboard.innerHTML = allParticipants.slice(0, 10).map((participant, index) => `
        <div class="flex justify-between items-center p-2 rounded ${
            participant.isPlayer ? 'bg-purple-500/20 border border-purple-500/30' : 'bg-gray-500/10'
        }">
            <span class="text-sm ${participant.isPlayer ? 'text-purple-400 font-bold' : 'text-gray-300'}">#${index + 1} ${participant.name}</span>
            <span class="text-sm text-yellow-400">${participant.score}pts (${participant.fishCount}🐟)</span>
        </div>
    `).join('');
    
    // Update rank
    const playerPosition = allParticipants.findIndex(p => p.isPlayer) + 1;
    fishingTournament.currentRank = playerPosition;
}

function updateFishCollection() {
    const collection = document.getElementById('fishCollection');
    
    if (fishingTournament.fishCaught.length === 0) {
        collection.innerHTML = '<div class="col-span-full text-center text-gray-400">No fish caught yet</div>';
        return;
    }
    
    collection.innerHTML = fishingTournament.fishCaught.map((fish, index) => `
        <div class="bg-black/20 p-3 rounded-lg border border-purple-500/20 text-center">
            <div class="text-2xl mb-2">🐟</div>
            <div class="text-xs text-gray-300">${fish.name}</div>
            <div class="text-sm font-bold" style="color: ${fish.color}">${fish.size}cm</div>
            <div class="text-xs text-yellow-400">${fish.points}pts</div>
        </div>
    `).join('');
}

function drawFishingLake() {
    const ctx = fishingTournament.ctx;
    const canvas = fishingTournament.canvas;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw water with animated waves
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#87CEEB');
    gradient.addColorStop(0.3, '#4682B4');
    gradient.addColorStop(1, '#191970');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw animated water surface
    ctx.strokeStyle = '#ADD8E6';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    for (let x = 0; x < canvas.width; x += 10) {
        const y = 50 + Math.sin((x + fishingTournament.waterLevel) * 0.02) * 5;
        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();
    
    // Draw fish silhouettes in water
    for (let i = 0; i < 8; i++) {
        const x = (i * 60) + 30 + Math.sin(fishingTournament.waterLevel * 0.01 + i) * 20;
        const y = 150 + i * 30 + Math.cos(fishingTournament.waterLevel * 0.015 + i) * 15;
        
        ctx.fillStyle = `rgba(255, 255, 255, ${0.1 + Math.sin(fishingTournament.waterLevel * 0.02 + i) * 0.05})`;
        ctx.beginPath();
        ctx.ellipse(x, y, 15, 8, 0, 0, 2 * Math.PI);
        ctx.fill();
        
        // Fish tail
        ctx.beginPath();
        ctx.moveTo(x - 15, y);
        ctx.lineTo(x - 25, y - 5);
        ctx.lineTo(x - 25, y + 5);
        ctx.closePath();
        ctx.fill();
    }
    
    // Draw fishing line if bobber is visible
    if (!document.getElementById('fishingBobber').classList.contains('hidden')) {
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2, 10);
        ctx.lineTo(fishingTournament.bobberX, fishingTournament.bobberY);
        ctx.stroke();
    }
}

function animateWater() {
    if (!fishingTournament.isActive) return;
    
    fishingTournament.waterLevel += 2;
    drawFishingLake();
    
    fishingTournament.animationId = requestAnimationFrame(animateWater);
}

function updateFishingDisplay() {
    document.getElementById('totalScore').textContent = fishingTournament.totalScore;
    document.getElementById('currentRank').textContent = `#${fishingTournament.currentRank}`;
    document.getElementById('tournamentLevel').textContent = fishingTournament.tournamentLevel;
    document.getElementById('masterPoints').textContent = fishingTournament.masterPoints;
    document.getElementById('perfectCatches').textContent = fishingTournament.perfectCatches;
    document.getElementById('legendaryFish').textContent = fishingTournament.legendaryFish;
    document.getElementById('trophyCollection').textContent = fishingTournament.trophyCollection;
    document.getElementById('sponsorshipLevel').textContent = fishingTournament.sponsorshipLevel;
    
    updateTournamentLeaderboard();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadFishingTournament();
});