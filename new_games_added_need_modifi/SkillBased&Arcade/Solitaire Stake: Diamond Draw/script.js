// Game state
let balance = 1000;

// Diamond Draw Solitaire game state
let diamondGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'classic',
    gameMode: 'diamond',
    stakeLevel: 1,
    
    // Diamond Draw mechanics
    deck: [],
    diamondGrid: Array(9).fill().map(() => Array(9).fill(null)),
    hand: [],
    discardPile: [],
    reservePile: [],
    selectedCard: null,
    selectedPosition: null,
    
    // Diamond formation
    diamondPattern: [
        [4],
        [3, 5],
        [2, 4, 6],
        [1, 3, 5, 7],
        [0, 2, 4, 6, 8],
        [1, 3, 5, 7],
        [2, 4, 6],
        [3, 5],
        [4]
    ],
    
    // Game mechanics
    moveCount: 0,
    timeElapsed: 0,
    cardsPlaced: 0,
    perfectPlacements: 0,
    chainCount: 0,
    maxChain: 0,
    
    // Scoring system
    score: 0,
    multiplier: 1.0,
    bonusPoints: 0,
    diamondBonus: 0,
    sequenceBonus: 0,
    
    // Performance tracking
    totalGames: 0,
    winStreak: 0,
    maxWinStreak: 0,
    averageMoves: 0,
    averageTime: 0,
    skillRating: 0,
    
    // Special features
    powerUps: [],
    achievements: [],
    diamondsFormed: 0,
    perfectDiamonds: 0,
    
    // Enhanced timing
    startTime: 0,
    moveHistory: [],
    undoStack: [],
    gameTimer: null,
    
    // Progressive features
    stakePoints: 0,
    stakeRank: 'Diamond Novice',
    unlockedModes: ['diamond'],
    masteryLevel: 1
};

// Diamond Draw difficulty settings
const DIAMOND_DIFFICULTIES = [
    {
        name: 'classic',
        description: 'Classic Diamond (20% win rate)',
        winRate: 0.20,
        multiplier: 1.5,
        handSize: 7,
        undoLimit: 8,
        hintLimit: 4,
        timeLimit: 480
    },
    {
        name: 'challenge',
        description: 'Challenge Mode (15% win rate)',
        winRate: 0.15,
        multiplier: 2.2,
        handSize: 6,
        undoLimit: 5,
        hintLimit: 3,
        timeLimit: 420
    },
    {
        name: 'expert',
        description: 'Expert Stakes (12% win rate)',
        winRate: 0.12,
        multiplier: 3.0,
        handSize: 5,
        undoLimit: 3,
        hintLimit: 2,
        timeLimit: 360
    },
    {
        name: 'master',
        description: 'Master Stakes (8% win rate)',
        winRate: 0.08,
        multiplier: 4.5,
        handSize: 4,
        undoLimit: 2,
        hintLimit: 1,
        timeLimit: 300
    },
    {
        name: 'grandmaster',
        description: 'Grandmaster (5% win rate)',
        winRate: 0.05,
        multiplier: 6.0,
        handSize: 3,
        undoLimit: 1,
        hintLimit: 0,
        timeLimit: 240
    },
    {
        name: 'legendary',
        description: 'Legendary Stakes (3% win rate)',
        winRate: 0.03,
        multiplier: 8.5,
        handSize: 3,
        undoLimit: 0,
        hintLimit: 0,
        timeLimit: 180
    }
];

const DIAMOND_MODES = [
    {
        name: 'diamond',
        description: 'Classic Diamond Formation',
        gridSize: 9,
        targetPattern: 'diamond',
        payoutMultiplier: 1.0
    },
    {
        name: 'pyramid',
        description: 'Pyramid Formation',
        gridSize: 7,
        targetPattern: 'pyramid',
        payoutMultiplier: 1.3
    },
    {
        name: 'star',
        description: 'Star Formation',
        gridSize: 9,
        targetPattern: 'star',
        payoutMultiplier: 1.8
    },
    {
        name: 'cross',
        description: 'Cross Formation',
        gridSize: 7,
        targetPattern: 'cross',
        payoutMultiplier: 1.5
    },
    {
        name: 'spiral',
        description: 'Spiral Formation',
        gridSize: 9,
        targetPattern: 'spiral',
        payoutMultiplier: 2.2
    },
    {
        name: 'hexagon',
        description: 'Hexagon Formation',
        gridSize: 9,
        targetPattern: 'hexagon',
        payoutMultiplier: 2.5
    }
];

// Card definitions
const SUITS = ['hearts', 'diamonds', 'clubs', 'spades'];
const RANKS = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
const SUIT_SYMBOLS = {
    hearts: '♥',
    diamonds: '♦',
    clubs: '♣',
    spades: '♠'
};

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadSolitaireGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">💎 DIAMOND DRAW 💎</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 STAKE AMOUNT</label>
                        <input type="number" id="diamondBet" value="75" min="25" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateDiamondPayout()">
                    </div>

                    <div class="grid grid-cols-1 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🎯 DIFFICULTY</label>
                            <select id="diamondDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateDiamondSettings()">
                                <option value="classic" selected>Classic (20%)</option>
                                <option value="challenge">Challenge (15%)</option>
                                <option value="expert">Expert Stakes (12%)</option>
                                <option value="master">Master Stakes (8%)</option>
                                <option value="grandmaster">Grandmaster (5%)</option>
                                <option value="legendary">Legendary (3%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">💎 FORMATION</label>
                            <select id="diamondMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateDiamondSettings()">
                                <option value="diamond" selected>Diamond</option>
                                <option value="pyramid">Pyramid</option>
                                <option value="star">Star</option>
                                <option value="cross">Cross</option>
                                <option value="spiral">Spiral</option>
                                <option value="hexagon">Hexagon</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="diamondWinRate">20%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Hand Size:</div>
                                <div class="text-yellow-400 font-bold" id="handSize">7</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Undo Limit:</div>
                                <div class="text-blue-400 font-bold" id="undoLimit">8</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Time Limit:</div>
                                <div class="text-purple-400 font-bold" id="timeLimit">480s</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="diamondPotentialPayout">600 GA</div>
                        <div class="text-sm text-gray-400">Potential Payout</div>
                    </div>

                    <button onclick="startDiamondGame()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        💎 DRAW DIAMONDS 💎
                    </button>

                    <div class="grid grid-cols-3 gap-2">
                        <button onclick="undoMove()" id="undoBtn"
                                class="cyber-button bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            ↶ UNDO
                        </button>
                        <button onclick="getHint()" id="hintBtn"
                                class="cyber-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            💡 HINT
                        </button>
                        <button onclick="surrenderGame()" id="surrenderBtn"
                                class="cyber-button bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-2 rounded-lg disabled:opacity-50 text-xs"
                                disabled>
                            🏳️ FOLD
                        </button>
                    </div>
                </div>

                <!-- Game Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 DIAMOND STATS</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Moves:</span>
                            <span class="text-green-400" id="moveCount">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Time:</span>
                            <span class="text-cyan-400" id="gameTime">0:00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Score:</span>
                            <span class="text-yellow-400" id="currentScore">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Multiplier:</span>
                            <span class="text-pink-400" id="scoreMultiplier">1.0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Placed:</span>
                            <span class="text-purple-400" id="cardsPlaced">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Chain:</span>
                            <span class="text-orange-400" id="chainCount">0</span>
                        </div>
                    </div>
                </div>

                <!-- Diamond Mastery -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">💎 DIAMOND MASTERY</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Stake Rank:</span>
                            <span class="text-yellow-400" id="stakeRank">Diamond Novice</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="stakeProgressBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="stakeProgress">0 / 100 Points</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-xs mt-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Win Streak:</span>
                            <span class="text-green-400" id="winStreak">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Diamonds:</span>
                            <span class="text-blue-400" id="diamondsFormed">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Board -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="gameTitle">Diamond Draw</h5>
                        <p class="text-sm text-gray-400" id="gameDescription">Form perfect diamond patterns with strategic card placement</p>
                    </div>
                    
                    <!-- Hand Cards -->
                    <div class="mb-6">
                        <div class="text-center mb-2">
                            <div class="text-sm text-gray-400">YOUR HAND</div>
                        </div>
                        <div class="flex justify-center gap-2" id="handCards">
                            <!-- Hand cards will be rendered here -->
                        </div>
                    </div>
                    
                    <!-- Diamond Grid -->
                    <div class="flex justify-center mb-6">
                        <div class="grid grid-cols-9 gap-1 p-4 bg-black/50 rounded-xl border border-purple-500/30" id="diamondGrid">
                            <!-- Diamond grid will be rendered here -->
                        </div>
                    </div>

                    <!-- Reserve and Discard -->
                    <div class="flex justify-center gap-8 mb-4">
                        <div class="text-center">
                            <div class="text-xs text-gray-400 mb-1">RESERVE</div>
                            <div id="reservePile" class="w-16 h-20 bg-gradient-to-br from-green-600 to-blue-600 border border-green-400 rounded-lg cursor-pointer flex items-center justify-center text-white font-bold"
                                 onclick="drawFromReserve()">
                                🂠
                            </div>
                            <div class="text-xs text-gray-400 mt-1" id="reserveCount">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-xs text-gray-400 mb-1">DISCARD</div>
                            <div id="discardPile" class="w-16 h-20 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400">
                                -
                            </div>
                            <div class="text-xs text-gray-400 mt-1" id="discardCount">0</div>
                        </div>
                    </div>

                    <!-- Game Results -->
                    <div class="text-center mt-6">
                        <div id="diamondResult" class="text-xl font-bold mb-2"></div>
                        <div id="diamondWinAmount" class="text-lg"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Complete Overlay -->
        <div id="diamondOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">💎 DIAMOND COMPLETE 💎</h3>
                <div id="finalDiamondResult" class="text-xl mb-4"></div>
                <div id="finalDiamondStats" class="text-sm text-gray-400 mb-6"></div>
                <div id="diamondAchievements" class="text-xs text-cyan-400 mb-4"></div>
                <button onclick="hideDiamondOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    NEW GAME
                </button>
            </div>
        </div>
    `;

    updateDiamondSettings();
    initializeDiamondGrid();
    
    // Add keyboard listeners
    document.addEventListener('keydown', handleDiamondKeyPress);
}

function updateDiamondSettings() {
    const difficulty = document.getElementById('diamondDifficulty').value;
    const mode = document.getElementById('diamondMode').value;
    
    const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === difficulty);
    const modeData = DIAMOND_MODES.find(m => m.name === mode);
    
    diamondGame.difficulty = difficulty;
    diamondGame.gameMode = mode;
    
    document.getElementById('diamondWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('handSize').textContent = diffData.handSize;
    document.getElementById('undoLimit').textContent = diffData.undoLimit;
    document.getElementById('timeLimit').textContent = diffData.timeLimit + 's';
    
    updateDiamondPayout();
}

function updateDiamondPayout() {
    const betAmount = parseInt(document.getElementById('diamondBet').value) || 75;
    const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
    const modeData = DIAMOND_MODES.find(m => m.name === diamondGame.gameMode);
    
    const totalMultiplier = diffData.multiplier * modeData.payoutMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 8);
    
    document.getElementById('diamondPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function createDeck() {
    const deck = [];
    for (const suit of SUITS) {
        for (const rank of RANKS) {
            deck.push({
                suit: suit,
                rank: rank,
                value: rank === 'A' ? 1 : (rank === 'J' ? 11 : (rank === 'Q' ? 12 : (rank === 'K' ? 13 : parseInt(rank)))),
                color: suit === 'hearts' || suit === 'diamonds' ? 'red' : 'black',
                faceUp: true
            });
        }
    }
    return shuffleDeck(deck);
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function initializeDiamondGrid() {
    const gridContainer = document.getElementById('diamondGrid');
    gridContainer.innerHTML = '';
    
    for (let row = 0; row < 9; row++) {
        for (let col = 0; col < 9; col++) {
            const cell = document.createElement('div');
            cell.className = 'w-8 h-10 border border-gray-600 rounded flex items-center justify-center text-xs cursor-pointer';
            cell.id = `grid-${row}-${col}`;
            cell.dataset.row = row;
            cell.dataset.col = col;
            
            // Check if this position is part of the diamond pattern
            if (diamondGame.diamondPattern[row] && diamondGame.diamondPattern[row].includes(col)) {
                cell.classList.add('bg-purple-900/30', 'border-purple-400/50');
                cell.addEventListener('click', () => handleGridClick(row, col));
            } else {
                cell.classList.add('bg-black/50', 'border-gray-700');
            }
            
            gridContainer.appendChild(cell);
        }
    }
}

function startDiamondGame() {
    const betAmount = parseInt(document.getElementById('diamondBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance for Diamond Draw!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset game state
    diamondGame.isPlaying = true;
    diamondGame.gamePhase = 'playing';
    diamondGame.betAmount = betAmount;
    diamondGame.moveCount = 0;
    diamondGame.timeElapsed = 0;
    diamondGame.score = 0;
    diamondGame.multiplier = 1.0;
    diamondGame.cardsPlaced = 0;
    diamondGame.chainCount = 0;
    diamondGame.perfectPlacements = 0;
    diamondGame.moveHistory = [];
    diamondGame.undoStack = [];
    
    // Initialize deck and deal
    diamondGame.deck = createDeck();
    dealDiamondCards();
    
    diamondGame.startTime = Date.now();
    startGameTimer();
    
    document.getElementById('undoBtn').disabled = false;
    document.getElementById('hintBtn').disabled = false;
    document.getElementById('surrenderBtn').disabled = false;
    
    hideDiamondOverlay();
    updateDiamondDisplay();
}

function dealDiamondCards() {
    const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
    
    // Clear all piles
    diamondGame.hand = [];
    diamondGame.reservePile = [];
    diamondGame.discardPile = [];
    diamondGame.diamondGrid = Array(9).fill().map(() => Array(9).fill(null));
    
    // Deal hand cards
    for (let i = 0; i < diffData.handSize; i++) {
        if (diamondGame.deck.length > 0) {
            diamondGame.hand.push(diamondGame.deck.pop());
        }
    }
    
    // Remaining cards go to reserve
    diamondGame.reservePile = [...diamondGame.deck];
    
    renderDiamondGame();
}

function renderDiamondGame() {
    renderHand();
    renderDiamondGrid();
    renderReserve();
    renderDiscard();
}

function renderHand() {
    const handContainer = document.getElementById('handCards');
    handContainer.innerHTML = '';
    
    diamondGame.hand.forEach((card, index) => {
        const cardEl = createCardElement(card, `hand-${index}`);
        cardEl.addEventListener('click', () => selectHandCard(card, index));
        handContainer.appendChild(cardEl);
    });
}

function renderDiamondGrid() {
    for (let row = 0; row < 9; row++) {
        for (let col = 0; col < 9; col++) {
            const cell = document.getElementById(`grid-${row}-${col}`);
            const card = diamondGame.diamondGrid[row][col];
            
            if (card) {
                cell.innerHTML = createCardContent(card);
                cell.className = 'w-8 h-10 bg-white border border-gray-300 rounded flex flex-col items-center justify-center text-xs';
            } else if (diamondGame.diamondPattern[row] && diamondGame.diamondPattern[row].includes(col)) {
                cell.innerHTML = '';
                cell.className = 'w-8 h-10 border border-purple-400/50 bg-purple-900/30 rounded flex items-center justify-center text-xs cursor-pointer';
            }
        }
    }
}

function renderReserve() {
    const reserveEl = document.getElementById('reservePile');
    const countEl = document.getElementById('reserveCount');
    
    if (diamondGame.reservePile.length > 0) {
        reserveEl.textContent = '🂠';
        reserveEl.className = 'w-16 h-20 bg-gradient-to-br from-green-600 to-blue-600 border border-green-400 rounded-lg cursor-pointer flex items-center justify-center text-white font-bold';
    } else {
        reserveEl.textContent = '-';
        reserveEl.className = 'w-16 h-20 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400';
    }
    
    countEl.textContent = diamondGame.reservePile.length;
}

function renderDiscard() {
    const discardEl = document.getElementById('discardPile');
    const countEl = document.getElementById('discardCount');
    
    if (diamondGame.discardPile.length > 0) {
        const topCard = diamondGame.discardPile[diamondGame.discardPile.length - 1];
        discardEl.innerHTML = createCardContent(topCard);
        discardEl.className = 'w-16 h-20 bg-white border border-gray-300 rounded-lg flex flex-col items-center justify-center text-xs';
    } else {
        discardEl.textContent = '-';
        discardEl.className = 'w-16 h-20 bg-black/50 border border-gray-500 rounded-lg flex items-center justify-center text-gray-400';
    }
    
    countEl.textContent = diamondGame.discardPile.length;
}

function createCardElement(card, id) {
    const cardEl = document.createElement('div');
    cardEl.id = id;
    cardEl.className = 'w-12 h-16 bg-white border border-gray-300 rounded-lg flex flex-col items-center justify-center text-xs cursor-pointer hover:scale-105 transition-transform';
    cardEl.innerHTML = createCardContent(card);
    return cardEl;
}

function createCardContent(card) {
    const color = card.color === 'red' ? 'text-red-500' : 'text-black';
    return `
        <div class="${color} font-bold text-xs">${card.rank}</div>
        <div class="${color} text-sm">${SUIT_SYMBOLS[card.suit]}</div>
    `;
}

function selectHandCard(card, index) {
    if (!diamondGame.isPlaying) return;
    
    // Clear previous selection
    clearSelection();
    
    // Select this card
    diamondGame.selectedCard = { card, source: 'hand', index };
    
    // Highlight selected card
    const cardEl = document.getElementById(`hand-${index}`);
    if (cardEl) {
        cardEl.classList.add('ring-2', 'ring-yellow-400');
    }
}

function handleGridClick(row, col) {
    if (!diamondGame.isPlaying || !diamondGame.selectedCard) return;
    
    // Check if position is valid for diamond pattern
    if (!diamondGame.diamondPattern[row] || !diamondGame.diamondPattern[row].includes(col)) return;
    
    // Check if position is empty
    if (diamondGame.diamondGrid[row][col] !== null) return;
    
    // Place card
    placeCardOnGrid(row, col);
}

function placeCardOnGrid(row, col) {
    const { card, source, index } = diamondGame.selectedCard;
    
    // Save move for undo
    diamondGame.undoStack.push({
        action: 'place',
        card: card,
        position: { row, col },
        source: source,
        sourceIndex: index
    });
    
    // Remove card from source
    if (source === 'hand') {
        diamondGame.hand.splice(index, 1);
    }
    
    // Place card on grid
    diamondGame.diamondGrid[row][col] = card;
    diamondGame.cardsPlaced++;
    diamondGame.moveCount++;
    
    // Check for sequences and bonuses
    checkDiamondSequences(row, col);
    
    // Update score
    updateScore(25);
    
    // Clear selection
    clearSelection();
    
    // Check if hand is empty - draw new cards
    if (diamondGame.hand.length === 0 && diamondGame.reservePile.length > 0) {
        drawFromReserve();
    }
    
    renderDiamondGame();
    updateDiamondDisplay();
    checkWinCondition();
}

function checkDiamondSequences(row, col) {
    const card = diamondGame.diamondGrid[row][col];
    let sequenceFound = false;
    
    // Check horizontal sequence
    let horizontalCount = 1;
    // Check left
    for (let c = col - 1; c >= 0; c--) {
        const leftCard = diamondGame.diamondGrid[row][c];
        if (leftCard && leftCard.value === card.value - (col - c)) {
            horizontalCount++;
        } else {
            break;
        }
    }
    // Check right
    for (let c = col + 1; c < 9; c++) {
        const rightCard = diamondGame.diamondGrid[row][c];
        if (rightCard && rightCard.value === card.value + (c - col)) {
            horizontalCount++;
        } else {
            break;
        }
    }
    
    if (horizontalCount >= 3) {
        sequenceFound = true;
        diamondGame.chainCount++;
    }
    
    // Check vertical sequence
    let verticalCount = 1;
    // Check up
    for (let r = row - 1; r >= 0; r--) {
        const upCard = diamondGame.diamondGrid[r][col];
        if (upCard && upCard.value === card.value - (row - r)) {
            verticalCount++;
        } else {
            break;
        }
    }
    // Check down
    for (let r = row + 1; r < 9; r++) {
        const downCard = diamondGame.diamondGrid[r][col];
        if (downCard && downCard.value === card.value + (r - row)) {
            verticalCount++;
        } else {
            break;
        }
    }
    
    if (verticalCount >= 3) {
        sequenceFound = true;
        diamondGame.chainCount++;
    }
    
    if (sequenceFound) {
        diamondGame.perfectPlacements++;
        updateScore(50);
    }
    
    diamondGame.maxChain = Math.max(diamondGame.maxChain, diamondGame.chainCount);
}

function drawFromReserve() {
    if (!diamondGame.isPlaying || diamondGame.reservePile.length === 0) return;
    
    const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
    
    // Draw cards to fill hand
    while (diamondGame.hand.length < diffData.handSize && diamondGame.reservePile.length > 0) {
        diamondGame.hand.push(diamondGame.reservePile.pop());
    }
    
    renderDiamondGame();
}

function clearSelection() {
    if (diamondGame.selectedCard) {
        const { source, index } = diamondGame.selectedCard;
        const cardEl = document.getElementById(`${source}-${index}`);
        if (cardEl) {
            cardEl.classList.remove('ring-2', 'ring-yellow-400');
        }
    }
    diamondGame.selectedCard = null;
}

function updateScore(points) {
    diamondGame.score += points * diamondGame.multiplier;
    
    // Increase multiplier for consecutive good moves
    if (points > 0) {
        diamondGame.multiplier = Math.min(3.0, 1.0 + (diamondGame.chainCount * 0.2));
    }
}

function startGameTimer() {
    diamondGame.gameTimer = setInterval(() => {
        diamondGame.timeElapsed++;
        const minutes = Math.floor(diamondGame.timeElapsed / 60);
        const seconds = diamondGame.timeElapsed % 60;
        document.getElementById('gameTime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
        if (diamondGame.timeElapsed >= diffData.timeLimit) {
            endDiamondGame(false, 'Time limit exceeded!');
        }
    }, 1000);
}

function checkWinCondition() {
    // Check if diamond pattern is complete
    let filledPositions = 0;
    let totalPositions = 0;
    
    for (let row = 0; row < 9; row++) {
        for (let col = 0; col < 9; col++) {
            if (diamondGame.diamondPattern[row] && diamondGame.diamondPattern[row].includes(col)) {
                totalPositions++;
                if (diamondGame.diamondGrid[row][col] !== null) {
                    filledPositions++;
                }
            }
        }
    }
    
    if (filledPositions === totalPositions) {
        diamondGame.diamondsFormed++;
        endDiamondGame(true, 'Perfect Diamond Formation!');
    } else if (diamondGame.hand.length === 0 && diamondGame.reservePile.length === 0) {
        // Check completion percentage
        const completionRate = filledPositions / totalPositions;
        if (completionRate >= 0.8) {
            endDiamondGame(true, 'Diamond Formation Complete!');
        } else {
            endDiamondGame(false, 'Insufficient diamond formation');
        }
    }
}

function undoMove() {
    if (diamondGame.undoStack.length === 0) return;
    
    const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
    if (diamondGame.undoCount >= diffData.undoLimit) return;
    
    const lastMove = diamondGame.undoStack.pop();
    diamondGame.undoCount++;
    
    // Reverse the move
    if (lastMove.action === 'place') {
        diamondGame.diamondGrid[lastMove.position.row][lastMove.position.col] = null;
        if (lastMove.source === 'hand') {
            diamondGame.hand.splice(lastMove.sourceIndex, 0, lastMove.card);
        }
        diamondGame.cardsPlaced--;
    }
    
    renderDiamondGame();
    updateDiamondDisplay();
}

function getHint() {
    const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
    if (diamondGame.hintCount >= diffData.hintLimit) return;
    
    diamondGame.hintCount++;
    
    // Find best placement for current hand
    // Implementation would analyze optimal positions
    
    updateDiamondDisplay();
}

function surrenderGame() {
    endDiamondGame(false, 'Game surrendered');
}

function updateDiamondDisplay() {
    document.getElementById('moveCount').textContent = diamondGame.moveCount;
    document.getElementById('currentScore').textContent = diamondGame.score;
    document.getElementById('scoreMultiplier').textContent = diamondGame.multiplier.toFixed(1) + 'x';
    document.getElementById('cardsPlaced').textContent = diamondGame.cardsPlaced;
    document.getElementById('chainCount').textContent = diamondGame.chainCount;
}

function endDiamondGame(won, message) {
    diamondGame.isPlaying = false;
    clearInterval(diamondGame.gameTimer);
    
    document.getElementById('undoBtn').disabled = true;
    document.getElementById('hintBtn').disabled = true;
    document.getElementById('surrenderBtn').disabled = true;
    
    let winnings = 0;
    
    if (won) {
        const diffData = DIAMOND_DIFFICULTIES.find(d => d.name === diamondGame.difficulty);
        const modeData = DIAMOND_MODES.find(m => m.name === diamondGame.gameMode);
        
        // Calculate winnings
        let baseMultiplier = diffData.multiplier * modeData.payoutMultiplier;
        
        // Time bonus
        const timeBonus = Math.max(0, (diffData.timeLimit - diamondGame.timeElapsed) / diffData.timeLimit);
        
        // Completion bonus
        const completionBonus = diamondGame.cardsPlaced / 41; // 41 positions in diamond
        
        // Chain bonus
        const chainBonus = diamondGame.chainCount * 0.1;
        
        const totalMultiplier = baseMultiplier * (1 + timeBonus + completionBonus + chainBonus) * diamondGame.multiplier;
        winnings = Math.floor(diamondGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        diamondGame.winStreak++;
        diamondGame.maxWinStreak = Math.max(diamondGame.maxWinStreak, diamondGame.winStreak);
        diamondGame.stakePoints += 75;
        
        document.getElementById('diamondResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">💎 DIAMOND MASTERY! 💎</span>`;
        document.getElementById('diamondWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        diamondGame.winStreak = 0;
        
        document.getElementById('diamondResult').innerHTML = 
            `<span class="text-red-400">💎 FORMATION FAILED 💎</span>`;
        document.getElementById('diamondWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">${message}</div>`;
    }
    
    diamondGame.totalGames++;
    updateDiamondDisplay();
    
    setTimeout(() => {
        document.getElementById('diamondOverlay').classList.remove('hidden');
        document.getElementById('finalDiamondResult').innerHTML = 
            won ? '<span class="text-green-400">💎 DIAMOND PERFECTION! 💎</span>' : 
                  '<span class="text-red-400">💔 FORMATION INCOMPLETE 💔</span>';
        
        document.getElementById('finalDiamondStats').innerHTML = 
            `Moves: ${diamondGame.moveCount} | Time: ${Math.floor(diamondGame.timeElapsed / 60)}:${(diamondGame.timeElapsed % 60).toString().padStart(2, '0')}<br>
             Score: ${diamondGame.score} | Placed: ${diamondGame.cardsPlaced} | Chains: ${diamondGame.chainCount}`;
    }, 3000);
}

function handleDiamondKeyPress(e) {
    if (!diamondGame.isPlaying) return;
    
    if (e.key.toLowerCase() === 'u') {
        e.preventDefault();
        undoMove();
    } else if (e.key.toLowerCase() === 'h') {
        e.preventDefault();
        getHint();
    } else if (e.code === 'Space') {
        e.preventDefault();
        drawFromReserve();
    }
}

function hideDiamondOverlay() {
    document.getElementById('diamondOverlay').classList.add('hidden');
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSolitaireGame();
});