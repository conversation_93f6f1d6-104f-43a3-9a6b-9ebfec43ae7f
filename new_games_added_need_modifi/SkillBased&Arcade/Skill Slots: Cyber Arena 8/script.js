// Game state
let balance = 1000;

// Skill Slots: Cyber Arena 8 game state
let skillSlotsGame = {
    isPlaying: false,
    gamePhase: 'waiting',
    betAmount: 0,
    difficulty: 'novice',
    slotMode: 'classic',
    arenaLevel: 8,
    
    // Enhanced slot mechanics
    reels: [[], [], [], [], []],
    reelPositions: [0, 0, 0, 0, 0],
    reelSpeeds: [0, 0, 0, 0, 0],
    isSpinning: false,
    stoppedReels: 0,
    
    // Advanced skill-based timing
    skillZones: [],
    perfectStops: 0,
    excellentStops: 0,
    goodStops: 0,
    fairStops: 0,
    missedStops: 0,
    timingAccuracy: 0,
    
    // Cyber Arena 8 features
    cyberLevel: 8,
    hackingPower: 0,
    digitalBonus: 0,
    matrixMultiplier: 1.0,
    glitchEffects: 0,
    quantumSync: 0,
    neuralLink: false,
    
    // Performance tracking
    totalSpins: 0,
    perfectSpinStreak: 0,
    maxPerfectStreak: 0,
    skillPoints: 0,
    arenaRank: 'Cyber Initiate',
    
    // Advanced metrics
    reactionTime: [],
    consistencyRating: 0,
    precisionScore: 0,
    cyberMastery: 0,
    
    // Enhanced combo system
    currentCombo: 0,
    maxCombo: 0,
    comboMultiplier: 1.0,
    superCombo: false,
    
    // Arena 8 progression
    experiencePoints: 0,
    nextLevelXP: 150,
    unlockedFeatures: ['basic_slots', 'quantum_mode'],
    achievements: [],
    
    // Multi-zone timing
    activeZones: 3,
    zoneAccuracy: [0, 0, 0],
    synchronization: 0
};

// Ultra-strict Arena 8 difficulty settings with sub-4% win rates
const DIFFICULTY_LEVELS = [
    {
        name: 'novice',
        description: 'Cyber Initiate (4% win rate)',
        winRate: 0.04,
        multiplier: 1.2,
        skillZoneSize: 0.12,
        reelSpeed: 1.2,
        requiredPerfectStops: 3,
        timingWindow: 120,
        zones: 2
    },
    {
        name: 'adept',
        description: 'Neural Warrior (3% win rate)',
        winRate: 0.03,
        multiplier: 1.6,
        skillZoneSize: 0.10,
        reelSpeed: 1.5,
        requiredPerfectStops: 4,
        timingWindow: 100,
        zones: 3
    },
    {
        name: 'expert',
        description: 'Quantum Master (2.5% win rate)',
        winRate: 0.025,
        multiplier: 2.1,
        skillZoneSize: 0.08,
        reelSpeed: 1.8,
        requiredPerfectStops: 4,
        timingWindow: 85,
        zones: 3
    },
    {
        name: 'master',
        description: 'Matrix Overlord (2% win rate)',
        winRate: 0.02,
        multiplier: 2.8,
        skillZoneSize: 0.06,
        reelSpeed: 2.2,
        requiredPerfectStops: 5,
        timingWindow: 70,
        zones: 4
    },
    {
        name: 'grandmaster',
        description: 'Cyber Deity (1.5% win rate)',
        winRate: 0.015,
        multiplier: 3.5,
        skillZoneSize: 0.04,
        reelSpeed: 2.8,
        requiredPerfectStops: 5,
        timingWindow: 55,
        zones: 5
    }
];

const SLOT_MODES = [
    {
        name: 'classic',
        description: 'Classic Quantum Slots',
        reelCount: 5,
        paylineMultiplier: 1.0,
        specialFeatures: false,
        complexity: 1
    },
    {
        name: 'turbo',
        description: 'Turbo Neural Mode',
        reelCount: 5,
        paylineMultiplier: 1.5,
        specialFeatures: true,
        complexity: 2
    },
    {
        name: 'matrix',
        description: 'Matrix Sync Grid',
        reelCount: 5,
        paylineMultiplier: 2.0,
        specialFeatures: true,
        complexity: 3
    },
    {
        name: 'quantum',
        description: 'Quantum Entanglement',
        reelCount: 5,
        paylineMultiplier: 2.5,
        specialFeatures: true,
        complexity: 4
    },
    {
        name: 'cyber_storm',
        description: 'Cyber Storm Arena 8',
        reelCount: 5,
        paylineMultiplier: 3.0,
        specialFeatures: true,
        complexity: 5
    }
];

// Enhanced cyber-themed slot symbols with Arena 8 exclusives
const CYBER_SYMBOLS = [
    { symbol: '🔴', name: 'Red Node', value: 1, rarity: 0.22, color: '#ff4444' },
    { symbol: '🟡', name: 'Yellow Core', value: 2, rarity: 0.18, color: '#ffaa00' },
    { symbol: '🟢', name: 'Green Data', value: 3, rarity: 0.16, color: '#44ff44' },
    { symbol: '🔵', name: 'Blue Circuit', value: 4, rarity: 0.14, color: '#4444ff' },
    { symbol: '🟣', name: 'Purple Matrix', value: 5, rarity: 0.12, color: '#aa44ff' },
    { symbol: '⚡', name: 'Lightning Bolt', value: 8, rarity: 0.08, color: '#ffff44' },
    { symbol: '💎', name: 'Cyber Diamond', value: 12, rarity: 0.05, color: '#44ffff' },
    { symbol: '🌟', name: 'Digital Star', value: 20, rarity: 0.03, color: '#ffffff' },
    { symbol: '👑', name: 'Cyber Crown', value: 35, rarity: 0.015, color: '#ffd700' },
    { symbol: '🔮', name: 'Quantum Orb', value: 50, rarity: 0.003, color: '#ff00ff' },
    { symbol: '🌌', name: 'Neural Galaxy', value: 100, rarity: 0.002, color: '#00ffff' }
];

const SKILL_ZONES = [
    { name: 'Perfect', multiplier: 2.5, color: '#00ff00', size: 0.03 },
    { name: 'Excellent', multiplier: 2.0, color: '#44ff44', size: 0.06 },
    { name: 'Good', multiplier: 1.5, color: '#88ff88', size: 0.10 },
    { name: 'Fair', multiplier: 1.2, color: '#ffff44', size: 0.14 },
    { name: 'Miss', multiplier: 0.3, color: '#ff4444', size: 1.0 }
];

// Arena 8 quantum effects
const QUANTUM_EFFECTS = [
    { name: 'Sync Boost', description: 'Perfect timing extends to adjacent reels', chance: 0.15 },
    { name: 'Time Dilation', description: 'Slower timing window for next reel', chance: 0.12 },
    { name: 'Neural Link', description: 'Combo multiplier doubles', chance: 0.08 },
    { name: 'Quantum Tunnel', description: 'Skip timing requirement for one reel', chance: 0.05 },
    { name: 'Matrix Override', description: 'All symbols become wilds', chance: 0.02 }
];

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance.toLocaleString()} GA</span>`;
}

function loadSkillSlotsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Skill Slots Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🎰 SKILL SLOTS: CYBER ARENA 8 🎰</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 QUANTUM BET</label>
                        <input type="number" id="slotsBet" value="100" min="50" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                               onchange="updateSlotsPayout()">
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">🏆 ARENA LEVEL</label>
                            <select id="slotsDifficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSlotsSettings()">
                                <option value="novice">Initiate (4%)</option>
                                <option value="adept" selected>Warrior (3%)</option>
                                <option value="expert">Master (2.5%)</option>
                                <option value="master">Overlord (2%)</option>
                                <option value="grandmaster">Deity (1.5%)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm mb-2 text-gray-300">⚡ QUANTUM MODE</label>
                            <select id="slotsMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white"
                                    onchange="updateSlotsSettings()">
                                <option value="classic" selected>Quantum Classic</option>
                                <option value="turbo">Neural Turbo</option>
                                <option value="matrix">Matrix Sync</option>
                                <option value="quantum">Quantum Entangle</option>
                                <option value="cyber_storm">Arena 8 Storm</option>
                            </select>
                        </div>
                    </div>

                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-400">Win Rate:</div>
                                <div class="text-red-400 font-bold" id="slotsWinRate">3%</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Perfect Stops:</div>
                                <div class="text-yellow-400 font-bold" id="requiredPerfectStops">4/5</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Skill Zones:</div>
                                <div class="text-blue-400 font-bold" id="activeZones">3</div>
                            </div>
                            <div>
                                <div class="text-gray-400">Timing Window:</div>
                                <div class="text-purple-400 font-bold" id="timingWindow">100ms</div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="text-2xl font-bold text-green-400" id="slotsPotentialPayout">1600 GA</div>
                        <div class="text-sm text-gray-400">Potential Quantum Payout</div>
                    </div>

                    <button onclick="startSkillSlots()" 
                            class="w-full cyber-button bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 mb-4">
                        🎰 ACTIVATE QUANTUM REELS 🎰
                    </button>

                    <button onclick="stopReel()" id="stopReelBtn"
                            class="w-full cyber-button bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 disabled:opacity-50"
                            disabled>
                        ⏹️ QUANTUM STOP (Space)
                    </button>
                </div>

                <!-- Enhanced Skill Performance -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">⚡ QUANTUM PERFORMANCE</h5>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Stops:</span>
                            <span class="text-green-400" id="perfectStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Excellent Stops:</span>
                            <span class="text-cyan-400" id="excellentStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Good Stops:</span>
                            <span class="text-blue-400" id="goodStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fair Stops:</span>
                            <span class="text-yellow-400" id="fairStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Missed Stops:</span>
                            <span class="text-red-400" id="missedStops">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Accuracy:</span>
                            <span class="text-yellow-400" id="timingAccuracy">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Quantum Combo:</span>
                            <span class="text-pink-400" id="currentCombo">0x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Neural Points:</span>
                            <span class="text-purple-400" id="skillPoints">0</span>
                        </div>
                    </div>
                </div>

                <!-- Arena 8 Progression -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌌 ARENA 8 MASTERY</h5>
                    <div class="mb-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Cyber Rank:</span>
                            <span class="text-yellow-400" id="arenaRank">Cyber Initiate</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mt-1">
                            <div id="xpBar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1" id="xpProgress">0 / 150 XP</div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-xs mt-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Quantum Sync:</span>
                            <span class="text-cyan-400" id="quantumSync">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Neural Link:</span>
                            <span class="text-pink-400" id="neuralLink">Inactive</span>
                        </div>
                    </div>
                </div>

                <!-- Quantum Effects -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🔮 QUANTUM EFFECTS</h5>
                    <div id="activeEffects" class="text-sm text-gray-400">
                        No active quantum effects
                    </div>
                </div>
            </div>

            <!-- Enhanced Cyber Slot Machine -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="text-center mb-4">
                        <h5 class="text-lg font-bold text-purple-400" id="slotsTitle">Quantum Arena 8 Slots</h5>
                        <p class="text-sm text-gray-400" id="slotsDescription">Master quantum timing to unlock the matrix</p>
                    </div>
                    
                    <!-- Enhanced Slot Machine Display -->
                    <div class="relative bg-gradient-to-b from-purple-900/50 to-black/50 rounded-lg p-6 mb-4 border-2 border-purple-500/30">
                        <!-- Multi-Zone Skill Indicator -->
                        <div class="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs">
                            <div id="skillZoneIndicator" class="bg-black/70 px-3 py-1 rounded text-green-400 font-bold">
                                QUANTUM ZONES: READY
                            </div>
                        </div>
                        
                        <!-- Reels Container -->
                        <div class="grid grid-cols-5 gap-2 mb-4" id="reelsContainer">
                            <!-- Reels will be generated here -->
                        </div>
                        
                        <!-- Multi-Zone Skill System -->
                        <div class="space-y-2 mb-4">
                            <div class="relative h-6 bg-gray-800 rounded" id="skillZone1">
                                <div class="absolute top-0 left-0 h-full bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-yellow-500 to-red-500 rounded"></div>
                                <div id="timingIndicator1" class="absolute top-0 w-1 h-full bg-white shadow-lg transition-all duration-50" style="left: 0%"></div>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">ZONE 1</div>
                            </div>
                            <div class="relative h-6 bg-gray-800 rounded" id="skillZone2">
                                <div class="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 via-cyan-500 via-green-500 via-cyan-500 to-blue-500 rounded"></div>
                                <div id="timingIndicator2" class="absolute top-0 w-1 h-full bg-white shadow-lg transition-all duration-50" style="left: 0%"></div>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">ZONE 2</div>
                            </div>
                            <div class="relative h-6 bg-gray-800 rounded" id="skillZone3">
                                <div class="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 via-pink-500 via-green-500 via-pink-500 to-purple-500 rounded"></div>
                                <div id="timingIndicator3" class="absolute top-0 w-1 h-full bg-white shadow-lg transition-all duration-50" style="left: 0%"></div>
                                <div class="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">ZONE 3</div>
                            </div>
                        </div>
                        
                        <!-- Quantum Synchronization Display -->
                        <div class="grid grid-cols-5 gap-2 mb-4">
                            <div class="text-center text-xs text-gray-400">REEL 1</div>
                            <div class="text-center text-xs text-gray-400">REEL 2</div>
                            <div class="text-center text-xs text-gray-400">REEL 3</div>
                            <div class="text-center text-xs text-gray-400">REEL 4</div>
                            <div class="text-center text-xs text-gray-400">REEL 5</div>
                        </div>
                        
                        <!-- Current Reel Indicator -->
                        <div class="text-center mb-2">
                            <span class="text-purple-400 font-bold" id="currentReelIndicator">Press SPACE for quantum stop</span>
                        </div>
                    </div>

                    <!-- Enhanced Spin Results -->
                    <div class="bg-black/50 p-4 rounded-lg mb-4">
                        <h6 class="text-sm font-bold text-gray-400 mb-2">QUANTUM RESULTS</h6>
                        <div id="spinResults" class="text-center">
                            <div class="text-gray-500">Quantum field ready...</div>
                        </div>
                    </div>

                    <!-- Win Display -->
                    <div class="text-center">
                        <div id="spinResult" class="text-xl font-bold mb-2"></div>
                        <div id="spinWinAmount" class="text-lg"></div>
                    </div>
                </div>

                <!-- Enhanced Symbol Paytable -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌌 QUANTUM SYMBOLS</h5>
                    <div class="grid grid-cols-3 gap-2 text-xs" id="symbolPaytable">
                        <!-- Symbol values will be generated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Game Overlay -->
        <div id="gameOverlay" class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 hidden">
            <div class="bg-black/90 p-8 rounded-xl border border-purple-500/50 text-center max-w-md">
                <h3 class="text-2xl font-bold mb-4 text-purple-400">🌌 QUANTUM SPIN COMPLETE 🌌</h3>
                <div id="finalSpinResult" class="text-xl mb-4"></div>
                <div id="finalSpinStats" class="text-sm text-gray-400 mb-6"></div>
                <div id="quantumEffectsResult" class="text-xs text-cyan-400 mb-4"></div>
                <button onclick="hideGameOverlay()" 
                        class="cyber-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-6 rounded-lg">
                    RETURN TO ARENA 8
                </button>
            </div>
        </div>
    `;

    updateSlotsSettings();
    generateReels();
    generateSymbolPaytable();
    initializeSkillZones();
    
    // Add keyboard listener for spacebar
    document.addEventListener('keydown', handleKeyPress);
}

function updateSlotsSettings() {
    const difficulty = document.getElementById('slotsDifficulty').value;
    const mode = document.getElementById('slotsMode').value;
    
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === difficulty);
    const modeData = SLOT_MODES.find(m => m.name === mode);
    
    skillSlotsGame.difficulty = difficulty;
    skillSlotsGame.slotMode = mode;
    skillSlotsGame.activeZones = diffData.zones;
    
    document.getElementById('slotsWinRate').textContent = Math.floor(diffData.winRate * 100) + '%';
    document.getElementById('requiredPerfectStops').textContent = diffData.requiredPerfectStops + '/5';
    document.getElementById('activeZones').textContent = diffData.zones;
    document.getElementById('timingWindow').textContent = diffData.timingWindow + 'ms';
    
    updateSlotsPayout();
}

function updateSlotsPayout() {
    const betAmount = parseInt(document.getElementById('slotsBet').value) || 100;
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === skillSlotsGame.difficulty);
    const modeData = SLOT_MODES.find(m => m.name === skillSlotsGame.slotMode);
    
    const totalMultiplier = diffData.multiplier * modeData.paylineMultiplier;
    const potentialPayout = Math.floor(betAmount * totalMultiplier * 16); // Arena 8 multiplier
    
    document.getElementById('slotsPotentialPayout').textContent = potentialPayout.toLocaleString() + ' GA';
}

function generateReels() {
    const container = document.getElementById('reelsContainer');
    container.innerHTML = '';
    
    for (let i = 0; i < 5; i++) {
        const reel = document.createElement('div');
        reel.className = 'relative bg-black/50 rounded-lg border border-purple-500/30 h-32 overflow-hidden';
        reel.id = `reel${i}`;
        
        const reelContent = document.createElement('div');
        reelContent.className = 'absolute inset-0 flex flex-col items-center justify-center transition-transform duration-100';
        reelContent.id = `reelContent${i}`;
        
        // Generate enhanced reel symbols
        skillSlotsGame.reels[i] = [];
        for (let j = 0; j < 25; j++) {
            const symbol = getRandomSymbol();
            skillSlotsGame.reels[i].push(symbol);
            
            const symbolEl = document.createElement('div');
            symbolEl.className = 'text-4xl py-2 flex items-center justify-center h-16';
            symbolEl.textContent = symbol.symbol;
            symbolEl.style.color = symbol.color;
            symbolEl.style.textShadow = `0 0 10px ${symbol.color}`;
            reelContent.appendChild(symbolEl);
        }
        
        reel.appendChild(reelContent);
        container.appendChild(reel);
    }
}

function getRandomSymbol() {
    const rand = Math.random();
    let cumulativeProbability = 0;
    
    for (const symbol of CYBER_SYMBOLS) {
        cumulativeProbability += symbol.rarity;
        if (rand <= cumulativeProbability) {
            return symbol;
        }
    }
    
    return CYBER_SYMBOLS[0]; // Fallback
}

function generateSymbolPaytable() {
    const container = document.getElementById('symbolPaytable');
    container.innerHTML = '';
    
    CYBER_SYMBOLS.forEach(symbol => {
        const item = document.createElement('div');
        item.className = 'flex items-center justify-between bg-black/20 p-2 rounded';
        item.innerHTML = `
            <span style="color: ${symbol.color}; text-shadow: 0 0 5px ${symbol.color}">${symbol.symbol}</span>
            <span class="text-yellow-400 font-bold">${symbol.value}x</span>
        `;
        container.appendChild(item);
    });
}

function initializeSkillZones() {
    updateSkillDisplay();
}

function startSkillSlots() {
    const betAmount = parseInt(document.getElementById('slotsBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient quantum energy!');
        return;
    }
    
    balance -= betAmount;
    updateBalance();
    
    // Reset enhanced game state
    skillSlotsGame.isPlaying = true;
    skillSlotsGame.gamePhase = 'spinning';
    skillSlotsGame.betAmount = betAmount;
    skillSlotsGame.stoppedReels = 0;
    skillSlotsGame.perfectStops = 0;
    skillSlotsGame.excellentStops = 0;
    skillSlotsGame.goodStops = 0;
    skillSlotsGame.fairStops = 0;
    skillSlotsGame.missedStops = 0;
    skillSlotsGame.currentCombo = 0;
    skillSlotsGame.reactionTime = [];
    skillSlotsGame.zoneAccuracy = [0, 0, 0];
    skillSlotsGame.quantumSync = 0;
    
    // Start spinning all reels
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === skillSlotsGame.difficulty);
    
    for (let i = 0; i < 5; i++) {
        skillSlotsGame.reelSpeeds[i] = diffData.reelSpeed * (0.7 + Math.random() * 0.6);
        skillSlotsGame.reelPositions[i] = 0;
    }
    
    skillSlotsGame.isSpinning = true;
    skillSlotsGame.spinStartTime = Date.now();
    
    document.getElementById('stopReelBtn').disabled = false;
    document.getElementById('currentReelIndicator').textContent = 'Quantum Reel 1 - SPACE to stop';
    document.getElementById('skillZoneIndicator').textContent = 'QUANTUM ZONES: ACTIVE';
    document.getElementById('skillZoneIndicator').className = 'bg-black/70 px-3 py-1 rounded text-yellow-400 font-bold animate-pulse';
    
    hideGameOverlay();
    startReelAnimation();
    startMultiZoneAnimation();
}

function startReelAnimation() {
    if (!skillSlotsGame.isSpinning) return;
    
    for (let i = skillSlotsGame.stoppedReels; i < 5; i++) {
        skillSlotsGame.reelPositions[i] += skillSlotsGame.reelSpeeds[i];
        
        const reelContent = document.getElementById(`reelContent${i}`);
        const translateY = -(skillSlotsGame.reelPositions[i] % (25 * 64)); // 25 symbols * 64px height
        reelContent.style.transform = `translateY(${translateY}px)`;
    }
    
    if (skillSlotsGame.isSpinning) {
        requestAnimationFrame(startReelAnimation);
    }
}

function startMultiZoneAnimation() {
    if (!skillSlotsGame.isSpinning) return;
    
    const time = Date.now() - skillSlotsGame.spinStartTime;
    
    // Animate multiple timing zones with different speeds
    for (let i = 1; i <= skillSlotsGame.activeZones; i++) {
        const indicator = document.getElementById(`timingIndicator${i}`);
        if (indicator) {
            const speed = 15 + (i * 5); // Different speeds for each zone
            const position = (time / speed) % 100;
            indicator.style.left = position + '%';
        }
    }
    
    if (skillSlotsGame.isSpinning) {
        requestAnimationFrame(startMultiZoneAnimation);
    }
}

function stopReel() {
    if (!skillSlotsGame.isSpinning || skillSlotsGame.stoppedReels >= 5) return;
    
    const reelIndex = skillSlotsGame.stoppedReels;
    const stopTime = Date.now();
    const reactionTime = stopTime - skillSlotsGame.spinStartTime;
    skillSlotsGame.reactionTime.push(reactionTime);
    
    // Calculate multi-zone timing accuracy
    const zoneAccuracies = [];
    for (let i = 1; i <= skillSlotsGame.activeZones; i++) {
        const indicator = document.getElementById(`timingIndicator${i}`);
        if (indicator) {
            const position = parseFloat(indicator.style.left);
            zoneAccuracies.push(calculateTimingAccuracy(position));
        }
    }
    
    // Find best accuracy across all zones
    const bestAccuracy = zoneAccuracies.reduce((best, current) => 
        current.multiplier > best.multiplier ? current : best
    );
    
    // Stop the reel at current position
    const finalPosition = Math.floor(skillSlotsGame.reelPositions[reelIndex] / 64) % 25;
    const reelContent = document.getElementById(`reelContent${reelIndex}`);
    reelContent.style.transform = `translateY(-${finalPosition * 64}px)`;
    
    // Update enhanced performance stats
    if (bestAccuracy.zone === 'Perfect') {
        skillSlotsGame.perfectStops++;
        skillSlotsGame.currentCombo += 2;
        skillSlotsGame.skillPoints += 15;
        skillSlotsGame.quantumSync += 20;
    } else if (bestAccuracy.zone === 'Excellent') {
        skillSlotsGame.excellentStops++;
        skillSlotsGame.currentCombo += 1.5;
        skillSlotsGame.skillPoints += 12;
        skillSlotsGame.quantumSync += 15;
    } else if (bestAccuracy.zone === 'Good') {
        skillSlotsGame.goodStops++;
        skillSlotsGame.currentCombo++;
        skillSlotsGame.skillPoints += 8;
        skillSlotsGame.quantumSync += 10;
    } else if (bestAccuracy.zone === 'Fair') {
        skillSlotsGame.fairStops++;
        skillSlotsGame.currentCombo += 0.5;
        skillSlotsGame.skillPoints += 3;
        skillSlotsGame.quantumSync += 5;
    } else {
        skillSlotsGame.missedStops++;
        skillSlotsGame.currentCombo = 0;
        skillSlotsGame.quantumSync = Math.max(0, skillSlotsGame.quantumSync - 10);
    }
    
    skillSlotsGame.stoppedReels++;
    skillSlotsGame.maxCombo = Math.max(skillSlotsGame.maxCombo, skillSlotsGame.currentCombo);
    
    // Check for quantum effects
    checkQuantumEffects(bestAccuracy);
    
    // Update display
    updateSkillDisplay();
    
    // Show enhanced timing feedback
    showTimingFeedback(bestAccuracy, reelIndex);
    
    if (skillSlotsGame.stoppedReels < 5) {
        document.getElementById('currentReelIndicator').textContent = `Quantum Reel ${skillSlotsGame.stoppedReels + 1} - SPACE to stop`;
    } else {
        // All reels stopped
        skillSlotsGame.isSpinning = false;
        document.getElementById('stopReelBtn').disabled = true;
        document.getElementById('currentReelIndicator').textContent = 'Processing quantum results...';
        document.getElementById('skillZoneIndicator').textContent = 'QUANTUM ZONES: COMPLETE';
        document.getElementById('skillZoneIndicator').className = 'bg-black/70 px-3 py-1 rounded text-green-400 font-bold';
        
        setTimeout(() => {
            calculateSpinResult();
        }, 1500);
    }
}

function calculateTimingAccuracy(position) {
    // Enhanced timing zones for Arena 8
    if (position >= 49 && position <= 51) {
        return { zone: 'Perfect', multiplier: 2.5, color: '#00ff00' };
    }
    else if ((position >= 46 && position < 49) || (position > 51 && position <= 54)) {
        return { zone: 'Excellent', multiplier: 2.0, color: '#44ff44' };
    }
    else if ((position >= 42 && position < 46) || (position > 54 && position <= 58)) {
        return { zone: 'Good', multiplier: 1.5, color: '#88ff88' };
    }
    else if ((position >= 35 && position < 42) || (position > 58 && position <= 65)) {
        return { zone: 'Fair', multiplier: 1.2, color: '#ffff44' };
    }
    else {
        return { zone: 'Miss', multiplier: 0.3, color: '#ff4444' };
    }
}

function checkQuantumEffects(accuracy) {
    if (accuracy.zone === 'Perfect' && Math.random() < 0.2) {
        const effect = QUANTUM_EFFECTS[Math.floor(Math.random() * QUANTUM_EFFECTS.length)];
        applyQuantumEffect(effect);
    }
}

function applyQuantumEffect(effect) {
    const activeEffectsEl = document.getElementById('activeEffects');
    activeEffectsEl.innerHTML = `<span class="text-cyan-400 animate-pulse">${effect.name}: ${effect.description}</span>`;
    
    // Apply effect logic here
    switch (effect.name) {
        case 'Neural Link':
            skillSlotsGame.neuralLink = true;
            skillSlotsGame.currentCombo *= 2;
            break;
        case 'Time Dilation':
            // Extend timing window for next reel
            break;
        // Add more effect implementations
    }
}

function showTimingFeedback(accuracy, reelIndex) {
    const reel = document.getElementById(`reel${reelIndex}`);
    const feedback = document.createElement('div');
    feedback.className = 'absolute inset-0 flex items-center justify-center text-lg font-bold pointer-events-none z-10';
    feedback.style.color = accuracy.color;
    feedback.style.textShadow = `0 0 10px ${accuracy.color}`;
    feedback.textContent = accuracy.zone;
    
    reel.appendChild(feedback);
    
    setTimeout(() => {
        reel.removeChild(feedback);
    }, 1200);
}

function updateSkillDisplay() {
    document.getElementById('perfectStops').textContent = skillSlotsGame.perfectStops;
    document.getElementById('excellentStops').textContent = skillSlotsGame.excellentStops;
    document.getElementById('goodStops').textContent = skillSlotsGame.goodStops;
    document.getElementById('fairStops').textContent = skillSlotsGame.fairStops;
    document.getElementById('missedStops').textContent = skillSlotsGame.missedStops;
    
    const totalStops = skillSlotsGame.perfectStops + skillSlotsGame.excellentStops + skillSlotsGame.goodStops + skillSlotsGame.fairStops + skillSlotsGame.missedStops;
    const accuracy = totalStops > 0 ? Math.floor(((skillSlotsGame.perfectStops + skillSlotsGame.excellentStops + skillSlotsGame.goodStops) / totalStops) * 100) : 0;
    document.getElementById('timingAccuracy').textContent = accuracy + '%';
    
    document.getElementById('currentCombo').textContent = Math.floor(skillSlotsGame.currentCombo * 10) / 10 + 'x';
    document.getElementById('skillPoints').textContent = skillSlotsGame.skillPoints;
    document.getElementById('quantumSync').textContent = Math.min(100, skillSlotsGame.quantumSync) + '%';
    document.getElementById('neuralLink').textContent = skillSlotsGame.neuralLink ? 'Active' : 'Inactive';
    
    // Update XP bar
    const xpPercentage = (skillSlotsGame.experiencePoints / skillSlotsGame.nextLevelXP) * 100;
    document.getElementById('xpBar').style.width = Math.min(100, xpPercentage) + '%';
    document.getElementById('xpProgress').textContent = `${skillSlotsGame.experiencePoints} / ${skillSlotsGame.nextLevelXP} XP`;
}

function calculateSpinResult() {
    const diffData = DIFFICULTY_LEVELS.find(d => d.name === skillSlotsGame.difficulty);
    const modeData = SLOT_MODES.find(m => m.name === skillSlotsGame.slotMode);
    
    // Ultra-strict Arena 8 winning requirements
    const perfectStopRequirement = skillSlotsGame.perfectStops >= diffData.requiredPerfectStops;
    const accuracyRequirement = (skillSlotsGame.perfectStops + skillSlotsGame.excellentStops + skillSlotsGame.goodStops) >= 4;
    const comboRequirement = skillSlotsGame.maxCombo >= 4;
    const timingRequirement = skillSlotsGame.missedStops <= 1;
    const quantumRequirement = skillSlotsGame.quantumSync >= 60;
    
    // Calculate average reaction time
    const avgReactionTime = skillSlotsGame.reactionTime.reduce((a, b) => a + b, 0) / skillSlotsGame.reactionTime.length;
    const reactionRequirement = avgReactionTime <= diffData.timingWindow;
    
    // Check for symbol matches
    const symbolMatch = checkSymbolMatches();
    
    const meetsAllRequirements = 
        perfectStopRequirement && 
        accuracyRequirement && 
        comboRequirement && 
        timingRequirement && 
        reactionRequirement &&
        quantumRequirement &&
        symbolMatch.hasWin;
    
    const finalWinChance = meetsAllRequirements ? diffData.winRate : 0;
    const won = Math.random() < finalWinChance;
    
    if (won) {
        // Calculate enhanced winnings with Arena 8 bonuses
        const baseMultiplier = diffData.multiplier * modeData.paylineMultiplier;
        const perfectBonus = skillSlotsGame.perfectStops * 0.3;
        const excellentBonus = skillSlotsGame.excellentStops * 0.2;
        const comboBonus = skillSlotsGame.maxCombo * 0.15;
        const speedBonus = avgReactionTime < diffData.timingWindow * 0.7 ? 0.4 : 0;
        const quantumBonus = skillSlotsGame.quantumSync / 100;
        const neuralBonus = skillSlotsGame.neuralLink ? 0.5 : 0;
        const symbolBonus = symbolMatch.multiplier;
        
        const totalMultiplier = baseMultiplier * (1 + perfectBonus + excellentBonus + comboBonus + speedBonus + quantumBonus + neuralBonus) * symbolBonus;
        const winnings = Math.floor(skillSlotsGame.betAmount * totalMultiplier);
        
        balance += winnings;
        updateBalance();
        
        skillSlotsGame.perfectSpinStreak++;
        skillSlotsGame.maxPerfectStreak = Math.max(skillSlotsGame.maxPerfectStreak, skillSlotsGame.perfectSpinStreak);
        skillSlotsGame.experiencePoints += 40;
        
        document.getElementById('spinResult').innerHTML = 
            `<span class="text-green-400 animate-pulse">🌌 QUANTUM JACKPOT! 🌌</span>`;
        document.getElementById('spinWinAmount').innerHTML = 
            `<span class="animate-bounce">+${winnings.toLocaleString()} GA</span>`;
    } else {
        skillSlotsGame.perfectSpinStreak = 0;
        
        const requirements = [
            `${diffData.requiredPerfectStops}+ perfect stops (${skillSlotsGame.perfectStops})`,
            `4+ accurate stops (${skillSlotsGame.perfectStops + skillSlotsGame.excellentStops + skillSlotsGame.goodStops})`,
            `4+ combo streak (${Math.floor(skillSlotsGame.maxCombo * 10) / 10})`,
            `≤1 missed stop (${skillSlotsGame.missedStops})`,
            `60%+ quantum sync (${Math.min(100, skillSlotsGame.quantumSync)}%)`
        ];
        
        document.getElementById('spinResult').innerHTML = 
            `<span class="text-red-400">🌌 QUANTUM FAILURE 🌌</span>`;
        document.getElementById('spinWinAmount').innerHTML = 
            `<div class="text-xs text-yellow-400 mt-2">Arena 8 Requirements:<br>
            ${requirements.slice(0, 3).join('<br>')}<br>
            ${requirements.slice(3, 5).join('<br>')}</div>`;
    }
    
    skillSlotsGame.totalSpins++;
    updateSkillDisplay();
    
    setTimeout(() => {
        document.getElementById('gameOverlay').classList.remove('hidden');
        document.getElementById('finalSpinResult').innerHTML = 
            won ? '<span class="text-green-400">🌌 QUANTUM MASTERY ACHIEVED! 🌌</span>' : 
                  '<span class="text-red-400">💔 QUANTUM DESYNC 💔</span>';
        document.getElementById('finalSpinStats').innerHTML = 
            `Perfect: ${skillSlotsGame.perfectStops} | Excellent: ${skillSlotsGame.excellentStops} | Good: ${skillSlotsGame.goodStops}<br>
             Quantum Sync: ${Math.min(100, skillSlotsGame.quantumSync)}% | Max Combo: ${Math.floor(skillSlotsGame.maxCombo * 10) / 10}<br>
             Avg Reaction: ${Math.floor(avgReactionTime)}ms | Neural Link: ${skillSlotsGame.neuralLink ? 'Active' : 'Inactive'}`;
        
        const activeEffects = document.getElementById('activeEffects').textContent;
        document.getElementById('quantumEffectsResult').textContent = 
            activeEffects !== 'No active quantum effects' ? activeEffects : '';
    }, 3000);
}

function checkSymbolMatches() {
    // Enhanced symbol matching for Arena 8
    const symbols = [];
    for (let i = 0; i < 5; i++) {
        const position = Math.floor(skillSlotsGame.reelPositions[i] / 64) % 25;
        symbols.push(skillSlotsGame.reels[i][position]);
    }
    
    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol.symbol] = (symbolCounts[symbol.symbol] || 0) + 1;
    });
    
    // Find best match with Arena 8 bonuses
    let bestMatch = { count: 0, symbol: null, multiplier: 1 };
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 3) {
            const symbolData = CYBER_SYMBOLS.find(s => s.symbol === symbol);
            const multiplier = symbolData.value * (count - 1); // Enhanced: 3=2x, 4=3x, 5=4x
            if (multiplier > bestMatch.multiplier) {
                bestMatch = { count, symbol, multiplier, hasWin: true };
            }
        }
    }
    
    return bestMatch.hasWin ? bestMatch : { hasWin: false, multiplier: 1 };
}

function handleKeyPress(e) {
    if (e.code === 'Space' && skillSlotsGame.isSpinning) {
        e.preventDefault();
        stopReel();
    }
}

function hideGameOverlay() {
    document.getElementById('gameOverlay').classList.add('hidden');
}

// Initialize the enhanced game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSkillSlotsGame();
});