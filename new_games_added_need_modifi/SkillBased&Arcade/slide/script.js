// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadSlideGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                            <h4 class="text-xl font-bold mb-4 text-cyan-400">NEURAL SLIDE</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="slideBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">SLIDE SPEED</label>
                                <select id="slideSpeed" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="slow">Slow (Safe)</option>
                                    <option value="medium" selected>Medium (Balanced)</option>
                                    <option value="fast">Fast (Risky)</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">TARGET ZONE</label>
                                <input type="number" id="slideTarget" value="5" min="1" max="10" 
                                       class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                                <div class="text-xs text-gray-400 mt-1">Higher zones = bigger multipliers</div>
                            </div>
                            
                            <button id="startSlide" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                START SLIDE
                            </button>
                            
                            <button id="stopSlide" class="w-full py-3 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white mb-4" disabled>
                                STOP SLIDE
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Current Zone</div>
                                <div id="slideCurrentZone" class="text-2xl font-bold text-cyan-400 neon-glow">0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Zone Multiplier</div>
                                <div id="slideMultiplier" class="text-2xl font-bold text-green-400 neon-glow">1.00x</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Potential Win</div>
                                <div id="slidePotentialWin" class="text-xl font-bold text-yellow-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Zone Info -->
                        <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-cyan-400">ZONE MULTIPLIERS</h5>
                            <div id="slideZoneInfo" class="text-sm space-y-1">
                                <!-- Zone info will be generated -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Slide Track -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                            <div id="slideTrack" class="relative bg-black/50 rounded-lg p-4 h-96 overflow-hidden">
                                <canvas id="slideCanvas" width="400" height="350" class="w-full h-full"></canvas>
                                
                                <!-- Slider -->
                                <div id="slideRunner" class="absolute w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-full border-4 border-cyan-400 neon-border transition-all duration-200"
                                     style="top: 50%; left: 20px; transform: translateY(-50%);">
                                </div>
                            </div>
                            <div id="slideStatus" class="text-center mt-4 text-lg font-semibold">Set your target and start sliding</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeSlide();
        }
        
        let slideGame = {
            canvas: null,
            ctx: null,
            isPlaying: false,
            currentZone: 0,
            targetZone: 5,
            position: 0,
            speed: 0,
            direction: 1,
            betAmount: 0,
            animationId: null,
            zones: []
        };
        
        function initializeSlide() {
            slideGame.canvas = document.getElementById('slideCanvas');
            slideGame.ctx = slideGame.canvas.getContext('2d');
            
            generateSlideZones();
            drawSlideTrack();
            updateSlideZoneInfo();
            
            document.getElementById('startSlide').addEventListener('click', startSlideGame);
            document.getElementById('stopSlide').addEventListener('click', stopSlideGame);
            document.getElementById('slideTarget').addEventListener('input', updateSlideTarget);
        }
        
        function generateSlideZones() {
            slideGame.zones = [];
            const zoneWidth = slideGame.canvas.width / 10;
            
            for (let i = 0; i < 10; i++) {
                const multiplier = 1 + (i * 0.5) + Math.pow(i, 1.2) * 0.1;
                slideGame.zones.push({
                    x: i * zoneWidth,
                    width: zoneWidth,
                    zone: i + 1,
                    multiplier: multiplier,
                    color: getZoneColor(i + 1)
                });
            }
        }
        
        function getZoneColor(zone) {
            if (zone <= 2) return '#4ade80'; // Green
            if (zone <= 4) return '#22d3ee'; // Cyan
            if (zone <= 6) return '#a855f7'; // Purple
            if (zone <= 8) return '#f59e0b'; // Orange
            return '#ef4444'; // Red
        }
        
        function drawSlideTrack() {
            const ctx = slideGame.ctx;
            const canvas = slideGame.canvas;
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw zones
            slideGame.zones.forEach((zone, index) => {
                // Zone background
                ctx.fillStyle = zone.color + '40';
                ctx.fillRect(zone.x, 0, zone.width, canvas.height);
                
                // Zone border
                ctx.strokeStyle = zone.color;
                ctx.lineWidth = 2;
                ctx.strokeRect(zone.x, 0, zone.width, canvas.height);
                
                // Zone number
                ctx.fillStyle = zone.color;
                ctx.font = 'bold 24px monospace';
                ctx.textAlign = 'center';
                ctx.fillText(zone.zone.toString(), zone.x + zone.width / 2, 50);
                
                // Zone multiplier
                ctx.font = 'bold 16px monospace';
                ctx.fillText(zone.multiplier.toFixed(2) + 'x', zone.x + zone.width / 2, canvas.height - 30);
                
                // Highlight target zone
                if (zone.zone === slideGame.targetZone) {
                    ctx.strokeStyle = '#39ff14';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(zone.x + 2, 2, zone.width - 4, canvas.height - 4);
                }
            });
            
            // Draw current position indicator
            if (slideGame.isPlaying) {
                const currentX = slideGame.position;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(currentX, canvas.height / 2, 6, 0, Math.PI * 2);
                ctx.fill();
                
                // Trail effect
                ctx.strokeStyle = '#00d4ff';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(currentX - 20, canvas.height / 2);
                ctx.lineTo(currentX, canvas.height / 2);
                ctx.stroke();
            }
        }
        
        function updateSlideZoneInfo() {
            const zoneInfo = document.getElementById('slideZoneInfo');
            zoneInfo.innerHTML = '';
            
            slideGame.zones.forEach(zone => {
                const row = document.createElement('div');
                row.className = 'flex justify-between items-center';
                row.innerHTML = `
                    <span style="color: ${zone.color}">Zone ${zone.zone}:</span>
                    <span class="text-gray-400">${zone.multiplier.toFixed(2)}x</span>
                `;
                zoneInfo.appendChild(row);
            });
        }
        
        function updateSlideTarget() {
            slideGame.targetZone = parseInt(document.getElementById('slideTarget').value);
            drawSlideTrack();
        }
        
        function startSlideGame() {
            const betAmount = parseInt(document.getElementById('slideBet').value);
            const speed = document.getElementById('slideSpeed').value;
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= betAmount;
            updateBalance();
            
            // Initialize game state
            slideGame.isPlaying = true;
            slideGame.betAmount = betAmount;
            slideGame.position = 20;
            slideGame.currentZone = 0;
            slideGame.direction = 1;
            
            // Set speed based on selection
            switch(speed) {
                case 'slow':
                    slideGame.speed = 1.5;
                    break;
                case 'medium':
                    slideGame.speed = 2.5;
                    break;
                case 'fast':
                    slideGame.speed = 4;
                    break;
            }
            
            // Update UI
            document.getElementById('startSlide').disabled = true;
            document.getElementById('stopSlide').disabled = false;
            document.getElementById('slideStatus').textContent = 'Sliding through zones... Stop at the right moment!';
            
            animateSlide();
        }
        
        function animateSlide() {
            function animate() {
                if (!slideGame.isPlaying) return;
                
                // Update position
                slideGame.position += slideGame.speed * slideGame.direction;
                
                // Bounce off walls
                if (slideGame.position >= slideGame.canvas.width - 20) {
                    slideGame.direction = -1;
                } else if (slideGame.position <= 20) {
                    slideGame.direction = 1;
                }
                
                // Determine current zone
                const zoneIndex = Math.floor((slideGame.position - 20) / (slideGame.canvas.width / 10));
                slideGame.currentZone = Math.max(0, Math.min(9, zoneIndex)) + 1;
                
                // Update display
                document.getElementById('slideCurrentZone').textContent = slideGame.currentZone;
                
                if (slideGame.zones[slideGame.currentZone - 1]) {
                    const multiplier = slideGame.zones[slideGame.currentZone - 1].multiplier;
                    document.getElementById('slideMultiplier').textContent = multiplier.toFixed(2) + 'x';
                    document.getElementById('slidePotentialWin').textContent = 
                        '$' + Math.floor(slideGame.betAmount * multiplier);
                }
                
                drawSlideTrack();
                slideGame.animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        function stopSlideGame() {
            if (!slideGame.isPlaying) return;
            
            slideGame.isPlaying = false;
            if (slideGame.animationId) {
                cancelAnimationFrame(slideGame.animationId);
            }
            
            // Calculate result
            const currentZoneData = slideGame.zones[slideGame.currentZone - 1];
            let winnings = 0;
            let success = false;
            
            if (currentZoneData) {
                // Check if player reached or exceeded target zone
                if (slideGame.currentZone >= slideGame.targetZone) {
                    success = true;
                    winnings = Math.floor(slideGame.betAmount * currentZoneData.multiplier);
                    balance += winnings;
                    updateBalance();
                }
            }
            
            // Update UI
            document.getElementById('startSlide').disabled = false;
            document.getElementById('stopSlide').disabled = true;
            
            if (success) {
                document.getElementById('slideStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Success! Stopped in Zone ${slideGame.currentZone} - Won $${winnings}</span>`;
            } else {
                document.getElementById('slideStatus').innerHTML = 
                    `<span class="text-red-400">Failed! Needed Zone ${slideGame.targetZone}, got Zone ${slideGame.currentZone}</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                slideGame.currentZone = 0;
                slideGame.position = 20;
                document.getElementById('slideCurrentZone').textContent = '0';
                document.getElementById('slideMultiplier').textContent = '1.00x';
                document.getElementById('slidePotentialWin').textContent = '0 GA';
                document.getElementById('slideStatus').textContent = 'Set your target and start sliding';
                drawSlideTrack();
            }, 3000);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSlideGame();
});