// Game state
const walletIntegration = new GameWalletIntegration('LimboLegends');
let limboGame = {
    isPlaying: false,
    currentMultiplier: 1.0,
    targetMultiplier: 2.0,
    betAmount: 0,
    legendLevel: 1,
    limboPoints: 0,
    gameMode: 'classic',
    difficulty: 'normal',
    maxMultiplier: 0,
    attempts: 0,
    streakCount: 0,
    limboDepth: 0
};

// Difficulty settings that affect crash probability
const DIFFICULTY_SETTINGS = [
    { name: 'normal', crashMultiplier: 1.0, description: 'Normal Limbo' },
    { name: 'hard', crashMultiplier: 1.3, description: 'Hard Limbo' },
    { name: 'extreme', crashMultiplier: 1.6, description: 'Extreme Limbo' },
    { name: 'nightmare', crashMultiplier: 2.0, description: 'Nightmare Limbo' },
    { name: 'legend', crashMultiplier: 2.5, description: 'Legend Mode' }
];



function loadLimboLegendsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Limbo Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">🔥 LIMBO LEGENDS 🔥</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 LIMBO WAGER</label>
                        <input type="number" id="limboBet" value="25" min="5"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET MULTIPLIER</label>
                        <input type="number" id="targetMultiplier" value="2.0" min="1.1" max="20.0" step="0.1"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                        <div class="text-xs text-gray-400 mt-1">Potential Win: <span id="potentialWin" class="text-green-400">50 GA</span></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ DIFFICULTY</label>
                        <select id="limboDifficulty" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Normal Limbo</option>
                            <option value="hard">Hard Limbo (1.3x risk)</option>
                            <option value="extreme">Extreme Limbo (1.6x risk)</option>
                            <option value="nightmare">Nightmare Limbo (2x risk)</option>
                            <option value="legend">Legend Mode (2.5x risk)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 GAME MODE</label>
                        <select id="limboMode" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic">Classic Limbo</option>
                            <option value="turbo">Turbo Mode (2x speed)</option>
                            <option value="marathon">Marathon Mode</option>
                            <option value="sudden">Sudden Death</option>
                        </select>
                    </div>
                    
                    <button id="startLimbo" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🔥 ENTER THE LIMBO 🔥
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🔥 Current Multiplier</div>
                        <div id="currentMultiplier" class="text-3xl font-bold text-red-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">👑 Legend Level</div>
                        <div id="legendLevel" class="text-lg font-bold text-purple-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Limbo Points</div>
                        <div id="limboPoints" class="text-lg font-bold text-blue-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏆 Max Multiplier</div>
                        <div id="maxMultiplier" class="text-lg font-bold text-yellow-400">1.00x</div>
                    </div>
                </div>
            </div>
            
            <!-- Limbo Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h5 class="text-lg font-bold mb-4 text-red-400 text-center">🔥 LIMBO CHAMBER 🔥</h5>
                    
                    <!-- Limbo Visualization -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-b from-red-900 via-orange-800 to-yellow-700 h-48 rounded-lg border border-red-400 relative overflow-hidden">
                            <div id="limboSprite" class="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-2xl transition-all duration-300">
                                🔥
                            </div>
                            <div id="multiplierMarkers" class="absolute inset-0 text-xs text-gray-300">
                                <!-- Multiplier markers will be generated here -->
                            </div>
                            <div id="flames" class="absolute inset-0">
                                <!-- Flame effects will appear here -->
                            </div>
                            <div id="targetMarker" class="absolute w-full h-0.5 bg-green-400 opacity-70" style="bottom: 30%;">
                                <span class="absolute right-2 -top-4 text-xs text-green-400">Target</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Limbo Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-black/50 p-3 rounded-lg text-center">
                            <div class="text-sm text-gray-400">Streak</div>
                            <div id="streakCount" class="text-lg font-bold text-orange-400">0</div>
                        </div>
                        <div class="bg-black/50 p-3 rounded-lg text-center">
                            <div class="text-sm text-gray-400">Attempts</div>
                            <div id="attemptCount" class="text-lg font-bold text-red-400">0</div>
                        </div>
                    </div>
                    
                    <div id="limboResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="limboStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Limbo History -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">📊 LIMBO HISTORY</h5>
                    <div id="limboHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Heat Meter -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">🌡️ HEAT METER 🌡️</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="heatBar" class="bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 h-4 rounded-full transition-all duration-300" style="width: 25%"></div>
                    </div>
                    <div id="heatText" class="text-center mt-2 text-orange-300">Warming Up</div>
                </div>
            </div>
        </div>
    `;
    
    initializeLimboGame();
}

function initializeLimboGame() {
    document.getElementById('startLimbo').addEventListener('click', startLimbo);
    document.getElementById('targetMultiplier').addEventListener('input', updatePotentialWin);
    document.getElementById('limboDifficulty').addEventListener('change', updateDifficulty);
    document.getElementById('limboMode').addEventListener('change', updateGameMode);
    
    updateDifficulty();
    updateGameMode();
    generateMultiplierMarkers();
    updatePotentialWin();
}

function updateDifficulty() {
    const difficulty = document.getElementById('limboDifficulty').value;
    limboGame.difficulty = difficulty;
}

function updateGameMode() {
    const mode = document.getElementById('limboMode').value;
    limboGame.gameMode = mode;
}

function updatePotentialWin() {
    const bet = parseInt(document.getElementById('limboBet').value) || 0;
    const target = parseFloat(document.getElementById('targetMultiplier').value);
    const potentialWin = Math.floor(bet * target);
    document.getElementById('potentialWin').textContent = potentialWin + ' GA';
    
    // Update target marker position
    const targetMarker = document.getElementById('targetMarker');
    if (targetMarker) {
        const position = Math.min(90, (target - 1) * 20);
        targetMarker.style.bottom = position + '%';
    }
}

function generateMultiplierMarkers() {
    const markers = document.getElementById('multiplierMarkers');
    markers.innerHTML = '';
    
    const multipliers = [1.5, 2.0, 3.0, 5.0, 10.0];
    multipliers.forEach((mult, index) => {
        const marker = document.createElement('div');
        marker.className = 'absolute right-2 text-xs';
        marker.style.bottom = `${15 + index * 15}%`;
        marker.textContent = `${mult}x`;
        markers.appendChild(marker);
    });
}

async function startLimbo() {
    const betAmount = parseInt(document.getElementById('limboBet').value);
    const targetMultiplier = parseFloat(document.getElementById('targetMultiplier').value);

    if (limboGame.isPlaying) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    limboGame.betAmount = betAmount;
    limboGame.targetMultiplier = targetMultiplier;
    limboGame.isPlaying = true;
    limboGame.currentMultiplier = 1.0;
    limboGame.attempts++;
    
    // Disable start button
    document.getElementById('startLimbo').disabled = true;
    
    document.getElementById('limboResult').textContent = '';
    document.getElementById('limboStatus').textContent = 'Descending into the limbo...';
    
    // Start limbo simulation
    startLimboSimulation();
}

function startLimboSimulation() {
    const updateSpeed = limboGame.gameMode === 'turbo' ? 100 : 200;
    
    const limboInterval = setInterval(() => {
        if (!limboGame.isPlaying) {
            clearInterval(limboInterval);
            return;
        }
        
        // Calculate crash probability (extremely high - 88%+ chance)
        const crashChance = calculateLimboCrashProbability();
        
        if (Math.random() < crashChance) {
            // Crash before reaching target
            crashLimbo();
            clearInterval(limboInterval);
            return;
        }
        
        // Increase multiplier slightly
        const increment = 0.02 + (Math.random() * 0.05);
        limboGame.currentMultiplier += increment;
        limboGame.maxMultiplier = Math.max(limboGame.maxMultiplier, limboGame.currentMultiplier);
        
        // Check if target reached (very rare)
        if (limboGame.currentMultiplier >= limboGame.targetMultiplier) {
            reachLimboTarget();
            clearInterval(limboInterval);
            return;
        }
        
        updateLimboDisplay();
        
    }, updateSpeed);
}

function calculateLimboCrashProbability() {
    // Base crash probability is extremely high (88%)
    let crashChance = 0.88;
    
    // Difficulty makes it worse
    const difficulty = DIFFICULTY_SETTINGS.find(d => d.name === limboGame.difficulty);
    crashChance += (difficulty.crashMultiplier - 1) * 0.2;
    
    // Multiplier increases crash chance exponentially
    crashChance += Math.pow(limboGame.currentMultiplier - 1, 1.8) * 0.3;
    
    // Game mode effects
    switch(limboGame.gameMode) {
        case 'turbo': crashChance *= 1.15; break;
        case 'marathon': crashChance *= 1.1; break;
        case 'sudden': crashChance *= 1.25; break;
    }
    
    // Higher targets are nearly impossible
    if (limboGame.targetMultiplier > 5.0) {
        crashChance += (limboGame.targetMultiplier - 5.0) * 0.1;
    }
    
    // Cap at 97% to give tiny hope
    return Math.min(0.97, crashChance);
}

function crashLimbo() {
    limboGame.isPlaying = false;
    limboGame.streakCount = 0;
    
    document.getElementById('limboResult').innerHTML = 
        `<span class="text-red-400">🔥 BURNED IN LIMBO! 🔥</span>`;
    
    let crashReason = 'Limbo flames';
    if (limboGame.currentMultiplier > 5.0) crashReason = 'Extreme heat';
    else if (limboGame.difficulty === 'nightmare' || limboGame.difficulty === 'legend') crashReason = 'Nightmare difficulty';
    else if (limboGame.gameMode === 'sudden') crashReason = 'Sudden death';
    
    document.getElementById('limboStatus').textContent = 
        `Crashed at ${limboGame.currentMultiplier.toFixed(2)}x due to ${crashReason}!`;
    
    addLimboHistory(false);
    updateHeatMeter();
    resetLimboControls();
}

async function reachLimboTarget() {
    limboGame.isPlaying = false;
    limboGame.legendLevel++;
    limboGame.limboPoints += Math.floor(limboGame.targetMultiplier * 10);
    limboGame.streakCount++;

    // Calculate winnings
    const baseWinnings = limboGame.betAmount * limboGame.targetMultiplier;
    const difficulty = DIFFICULTY_SETTINGS.find(d => d.name === limboGame.difficulty);
    const difficultyBonus = difficulty.crashMultiplier;
    const payout = Math.floor(baseWinnings * difficultyBonus);
    const netWinnings = payout - limboGame.betAmount;

    if (netWinnings > 0) {
        await walletIntegration.processWin(netWinnings);
    }

    document.getElementById('limboResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🏆 LIMBO LEGEND! 🏆</span>`;
    document.getElementById('limboStatus').innerHTML = 
        `Reached ${limboGame.targetMultiplier.toFixed(2)}x! Earned ${payout} GA!`;
    
    addLimboHistory(true);
    updateHeatMeter();
    resetLimboControls();
}

function updateLimboDisplay() {
    document.getElementById('currentMultiplier').textContent = limboGame.currentMultiplier.toFixed(2) + 'x';
    document.getElementById('legendLevel').textContent = limboGame.legendLevel;
    document.getElementById('limboPoints').textContent = limboGame.limboPoints;
    document.getElementById('maxMultiplier').textContent = limboGame.maxMultiplier.toFixed(2) + 'x';
    document.getElementById('streakCount').textContent = limboGame.streakCount;
    document.getElementById('attemptCount').textContent = limboGame.attempts;
    
    // Update limbo sprite position
    const sprite = document.getElementById('limboSprite');
    const maxDisplayMultiplier = 10.0;
    const position = Math.min(90, ((limboGame.currentMultiplier - 1) / (maxDisplayMultiplier - 1)) * 90);
    sprite.style.bottom = position + '%';
    
    // Generate flame effects
    generateFlameEffects();
}

function generateFlameEffects() {
    const flames = document.getElementById('flames');
    flames.innerHTML = '';
    
    // Add flame effects based on current multiplier
    const flameCount = Math.min(8, Math.floor(limboGame.currentMultiplier));
    for (let i = 0; i < flameCount; i++) {
        const flame = document.createElement('div');
        flame.className = 'absolute text-lg opacity-70 animate-pulse';
        flame.textContent = ['🔥', '💥', '⚡', '🌋'][Math.floor(Math.random() * 4)];
        flame.style.left = Math.random() * 80 + '%';
        flame.style.top = Math.random() * 80 + '%';
        flames.appendChild(flame);
    }
}

function updateHeatMeter() {
    const heat = Math.min(100, limboGame.attempts * 5 + limboGame.currentMultiplier * 10);
    document.getElementById('heatBar').style.width = heat + '%';
    
    let heatLevel = 'Warming Up';
    if (heat > 80) heatLevel = 'Inferno';
    else if (heat > 60) heatLevel = 'Blazing';
    else if (heat > 40) heatLevel = 'Hot';
    else if (heat > 20) heatLevel = 'Warm';
    document.getElementById('heatText').textContent = heatLevel;
}

function addLimboHistory(successful) {
    const history = document.getElementById('limboHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-xs font-bold ${
        successful ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = `${limboGame.currentMultiplier.toFixed(2)}x`;
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 15) {
        history.removeChild(history.lastChild);
    }
}

function resetLimboControls() {
    setTimeout(() => {
        document.getElementById('startLimbo').disabled = false;
        
        // Reset sprite position
        document.getElementById('limboSprite').style.bottom = '2%';
        
        // Clear effects
        document.getElementById('flames').innerHTML = '';
        
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadLimboLegendsGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

