// Game state
let balance = 1000;
let diamondDuelsGame = {
    isRolling: false,
    playerDice: [],
    opponentDice: [],
    betAmount: 0,
    duelType: 'classic',
    diamondMultiplier: 1,
    streakCount: 0,
    powerUps: {
        diamondShield: false,
        luckyCharm: false,
        doubleRoll: false
    }
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Game Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">DIAMOND DICE DUELS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="diamondBet" value="10" min="1" max="${balance}" 
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DUEL TYPE</label>
                        <select id="duelType" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic">Classic Duel (3 dice)</option>
                            <option value="royal">Royal Duel (5 dice)</option>
                            <option value="legendary">Legendary Duel (7 dice)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">POWER-UPS</label>
                        <div class="grid grid-cols-3 gap-2">
                            <button id="diamondShield" class="power-up-btn bg-blue-600/30 p-2 rounded text-xs">
                                💎 Shield<br><span class="text-xs text-gray-400">50 GA</span>
                            </button>
                            <button id="luckyCharm" class="power-up-btn bg-green-600/30 p-2 rounded text-xs">
                                🍀 Charm<br><span class="text-xs text-gray-400">75 GA</span>
                            </button>
                            <button id="doubleRoll" class="power-up-btn bg-purple-600/30 p-2 rounded text-xs">
                                🎲 Double<br><span class="text-xs text-gray-400">100 GA</span>
                            </button>
                        </div>
                    </div>
                    
                    <button id="startDuel" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START DUEL
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">Diamond Multiplier</div>
                        <div id="diamondMultiplier" class="text-2xl font-bold text-cyan-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Win Streak</div>
                        <div id="winStreak" class="text-lg font-bold text-yellow-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Duel Arena -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h5 class="text-lg font-bold mb-4 text-cyan-400 text-center">DUEL ARENA</h5>
                    
                    <!-- Player Dice -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2">YOUR DICE</div>
                        <div id="playerDice" class="flex justify-center space-x-2 mb-2">
                            <!-- Dice will be generated here -->
                        </div>
                        <div id="playerTotal" class="text-center text-lg font-bold text-green-400">Total: 0</div>
                    </div>
                    
                    <!-- VS Indicator -->
                    <div class="text-center text-2xl font-bold text-red-400 mb-6">VS</div>
                    
                    <!-- Opponent Dice -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2">OPPONENT DICE</div>
                        <div id="opponentDice" class="flex justify-center space-x-2 mb-2">
                            <!-- Dice will be generated here -->
                        </div>
                        <div id="opponentTotal" class="text-center text-lg font-bold text-red-400">Total: 0</div>
                    </div>
                    
                    <div id="duelResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="duelStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Duel History -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">DUEL HISTORY</h5>
                    <div id="duelHistory" class="flex flex-wrap gap-2"></div>
                </div>
            </div>
        </div>
    `;
    
    initializeDiamondDuels();
}

function initializeDiamondDuels() {
    document.getElementById('startDuel').addEventListener('click', startDiamondDuel);
    document.getElementById('duelType').addEventListener('change', updateDuelType);
    
    // Power-up buttons
    document.getElementById('diamondShield').addEventListener('click', () => buyPowerUp('diamondShield', 50));
    document.getElementById('luckyCharm').addEventListener('click', () => buyPowerUp('luckyCharm', 75));
    document.getElementById('doubleRoll').addEventListener('click', () => buyPowerUp('doubleRoll', 100));
    
    updateDuelType();
}

function updateDuelType() {
    const type = document.getElementById('duelType').value;
    diamondDuelsGame.duelType = type;
    
    // Update multiplier based on duel type
    switch(type) {
        case 'classic': diamondDuelsGame.diamondMultiplier = 1.0; break;
        case 'royal': diamondDuelsGame.diamondMultiplier = 1.2; break;
        case 'legendary': diamondDuelsGame.diamondMultiplier = 1.5; break;
    }
    
    document.getElementById('diamondMultiplier').textContent = diamondDuelsGame.diamondMultiplier.toFixed(2) + 'x';
}

function buyPowerUp(type, cost) {
    if (balance < cost) {
        alert('Insufficient balance for power-up!');
        return;
    }
    
    if (diamondDuelsGame.powerUps[type]) {
        alert('Power-up already active!');
        return;
    }
    
    balance -= cost;
    updateBalance();
    diamondDuelsGame.powerUps[type] = true;
    
    // Update button appearance
    const button = document.getElementById(type);
    button.style.backgroundColor = 'rgba(34, 197, 94, 0.3)';
    button.style.border = '1px solid rgb(34, 197, 94)';
}

function startDiamondDuel() {
    const betAmount = parseInt(document.getElementById('diamondBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    if (diamondDuelsGame.isRolling) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    diamondDuelsGame.betAmount = betAmount;
    diamondDuelsGame.isRolling = true;
    
    // Disable start button
    document.getElementById('startDuel').disabled = true;
    document.getElementById('duelResult').textContent = '';
    document.getElementById('duelStatus').textContent = 'Rolling dice...';
    
    // Generate dice based on duel type
    const diceCount = getDiceCount();
    generateDiceDisplay(diceCount);
    
    // Animate dice rolling
    animateDiceRoll(diceCount);
}

function getDiceCount() {
    switch(diamondDuelsGame.duelType) {
        case 'classic': return 3;
        case 'royal': return 5;
        case 'legendary': return 7;
        default: return 3;
    }
}

function generateDiceDisplay(count) {
    const playerDiceEl = document.getElementById('playerDice');
    const opponentDiceEl = document.getElementById('opponentDice');
    
    playerDiceEl.innerHTML = '';
    opponentDiceEl.innerHTML = '';
    
    for (let i = 0; i < count; i++) {
        // Player dice
        const playerDie = document.createElement('div');
        playerDie.className = 'w-12 h-12 bg-cyan-600 rounded-lg flex items-center justify-center text-white font-bold text-lg';
        playerDie.textContent = '?';
        playerDiceEl.appendChild(playerDie);
        
        // Opponent dice
        const opponentDie = document.createElement('div');
        opponentDie.className = 'w-12 h-12 bg-red-600 rounded-lg flex items-center justify-center text-white font-bold text-lg';
        opponentDie.textContent = '?';
        opponentDiceEl.appendChild(opponentDie);
    }
}

function animateDiceRoll(diceCount) {
    const playerDiceEls = document.querySelectorAll('#playerDice > div');
    const opponentDiceEls = document.querySelectorAll('#opponentDice > div');
    
    let rollCount = 0;
    const rollInterval = setInterval(() => {
        // Animate player dice
        playerDiceEls.forEach(die => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        });
        
        // Animate opponent dice
        opponentDiceEls.forEach(die => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        });
        
        rollCount++;
        
        if (rollCount >= 20) {
            clearInterval(rollInterval);
            finalizeDiceRoll(diceCount);
        }
    }, 100);
}

function finalizeDiceRoll(diceCount) {
    // Generate final dice values with extreme bias against player
    diamondDuelsGame.playerDice = [];
    diamondDuelsGame.opponentDice = [];
    
    // Player dice - heavily biased toward low values (< 10% win rate)
    for (let i = 0; i < diceCount; i++) {
        // 90% chance of rolling 1-3, 10% chance of rolling 4-6
        const roll = Math.random() < 0.9 ? 
            Math.floor(Math.random() * 3) + 1 : 
            Math.floor(Math.random() * 3) + 4;
        diamondDuelsGame.playerDice.push(roll);
    }
    
    // Opponent dice - heavily biased toward high values
    for (let i = 0; i < diceCount; i++) {
        // 85% chance of rolling 4-6, 15% chance of rolling 1-3
        const roll = Math.random() < 0.85 ? 
            Math.floor(Math.random() * 3) + 4 : 
            Math.floor(Math.random() * 3) + 1;
        diamondDuelsGame.opponentDice.push(roll);
    }
    
    // Apply power-up effects
    applyPowerUpEffects();
    
    // Display final dice
    const playerDiceEls = document.querySelectorAll('#playerDice > div');
    const opponentDiceEls = document.querySelectorAll('#opponentDice > div');
    
    playerDiceEls.forEach((die, index) => {
        die.textContent = diamondDuelsGame.playerDice[index];
    });
    
    opponentDiceEls.forEach((die, index) => {
        die.textContent = diamondDuelsGame.opponentDice[index];
    });
    
    // Calculate totals
    const playerTotal = diamondDuelsGame.playerDice.reduce((sum, die) => sum + die, 0);
    const opponentTotal = diamondDuelsGame.opponentDice.reduce((sum, die) => sum + die, 0);
    
    document.getElementById('playerTotal').textContent = `Total: ${playerTotal}`;
    document.getElementById('opponentTotal').textContent = `Total: ${opponentTotal}`;
    
    // Determine winner and payout
    resolveDuel(playerTotal, opponentTotal);
}

function applyPowerUpEffects() {
    // Diamond Shield - protects one die from being too low
    if (diamondDuelsGame.powerUps.diamondShield) {
        const lowestIndex = diamondDuelsGame.playerDice.indexOf(Math.min(...diamondDuelsGame.playerDice));
        if (diamondDuelsGame.playerDice[lowestIndex] < 4) {
            diamondDuelsGame.playerDice[lowestIndex] = Math.floor(Math.random() * 3) + 4;
        }
        diamondDuelsGame.powerUps.diamondShield = false;
    }
    
    // Lucky Charm - reroll lowest die
    if (diamondDuelsGame.powerUps.luckyCharm) {
        const lowestIndex = diamondDuelsGame.playerDice.indexOf(Math.min(...diamondDuelsGame.playerDice));
        diamondDuelsGame.playerDice[lowestIndex] = Math.floor(Math.random() * 6) + 1;
        diamondDuelsGame.powerUps.luckyCharm = false;
    }
    
    // Double Roll - roll an extra die and keep the best
    if (diamondDuelsGame.powerUps.doubleRoll) {
        const extraRoll = Math.floor(Math.random() * 6) + 1;
        const lowestValue = Math.min(...diamondDuelsGame.playerDice);
        const lowestIndex = diamondDuelsGame.playerDice.indexOf(lowestValue);
        if (extraRoll > lowestValue) {
            diamondDuelsGame.playerDice[lowestIndex] = extraRoll;
        }
        diamondDuelsGame.powerUps.doubleRoll = false;
    }
}

function resolveDuel(playerTotal, opponentTotal) {
    let won = false;
    let winnings = 0;
    
    if (playerTotal > opponentTotal) {
        won = true;
        diamondDuelsGame.streakCount++;
        
        // Calculate winnings with multipliers
        const baseMultiplier = diamondDuelsGame.diamondMultiplier;
        const streakBonus = 1 + (diamondDuelsGame.streakCount * 0.1);
        const totalMultiplier = baseMultiplier * streakBonus;
        
        winnings = Math.floor(diamondDuelsGame.betAmount * totalMultiplier * 1.8);
        balance += winnings;
        updateBalance();
        
        document.getElementById('duelResult').innerHTML = 
            `<span class="text-green-400 neon-glow">VICTORY!</span>`;
        document.getElementById('duelStatus').innerHTML = 
            `You won ${winnings} GA! Streak: ${diamondDuelsGame.streakCount}`;
    } else if (playerTotal < opponentTotal) {
        diamondDuelsGame.streakCount = 0;
        document.getElementById('duelResult').innerHTML = 
            `<span class="text-red-400">DEFEAT!</span>`;
        document.getElementById('duelStatus').innerHTML = 
            `Your opponent's dice were superior! Streak broken.`;
    } else {
        // Tie - return half bet (still a loss)
        winnings = Math.floor(diamondDuelsGame.betAmount * 0.5);
        balance += winnings;
        updateBalance();
        
        document.getElementById('duelResult').innerHTML = 
            `<span class="text-yellow-400">TIE!</span>`;
        document.getElementById('duelStatus').innerHTML = 
            `Tied duel! Returned ${winnings} GA.`;
    }
    
    // Update streak display
    document.getElementById('winStreak').textContent = diamondDuelsGame.streakCount;
    
    // Add to history
    addDuelHistory(playerTotal, opponentTotal, won);
    
    // Reset power-up buttons
    resetPowerUpButtons();
    
    // Re-enable start button
    setTimeout(() => {
        document.getElementById('startDuel').disabled = false;
        diamondDuelsGame.isRolling = false;
    }, 2000);
}

function addDuelHistory(playerTotal, opponentTotal, won) {
    const history = document.getElementById('duelHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-sm font-bold ${
        won ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = `${playerTotal} vs ${opponentTotal}`;
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

function resetPowerUpButtons() {
    ['diamondShield', 'luckyCharm', 'doubleRoll'].forEach(id => {
        const button = document.getElementById(id);
        button.style.backgroundColor = '';
        button.style.border = '';
    });
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

