// Game state
const walletIntegration = new GameWalletIntegration('Hilo Crypto Kings');
let hiloCryptoGame = {
    isPlaying: false,
    currentCrypto: null,
    nextCrypto: null,
    cryptoMarket: [],
    betAmount: 0,
    streak: 0,
    kingdomLevel: 1,
    cryptoVault: 0,
    marketVolatility: 50,
    gameMode: 'standard',
    tradeHistory: [],
    multiplier: 1.0,
    bullRun: false,
    bearMarket: false,
    whaleActivity: false
};

// Crypto currencies with values
const CRYPTO_CURRENCIES = [
    { name: 'Bitcoin', symbol: '₿', baseValue: 50000, volatility: 0.15 },
    { name: 'Ethereum', symbol: 'Ξ', baseValue: 3000, volatility: 0.18 },
    { name: 'Cardano', symbol: '₳', baseValue: 1.2, volatility: 0.25 },
    { name: 'Solana', symbol: '◎', baseValue: 100, volatility: 0.22 },
    { name: 'Polygon', symbol: '⬟', baseValue: 0.8, volatility: 0.28 },
    { name: 'Chainlink', symbol: '🔗', baseValue: 15, volatility: 0.20 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', symbol: 'Ð', baseValue: 0.08, volatility: 0.35 },
    { name: 'Shiba', symbol: '🐕', baseValue: 0.00001, volatility: 0.40 }
];



function loadHiloCryptoKingsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Trading Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">👑 HILO CRYPTO KINGS 👑</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 TRADING CAPITAL</label>
                        <input type="number" id="cryptoBet" value="25" min="5"
                               class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">📊 TRADING MODE</label>
                        <select id="tradingMode" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Trading</option>
                            <option value="leverage">Leverage Trading (2x)</option>
                            <option value="futures">Futures Trading (3x)</option>
                            <option value="defi">DeFi Yield (5x)</option>
                        </select>
                    </div>
                    
                    <button id="startCryptoTrading" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🚀 START CRYPTO TRADING 🚀
                    </button>
                    
                    <div class="grid grid-cols-2 gap-2 mb-4">
                        <button id="guessPump" class="bg-green-600/30 hover:bg-green-600/50 py-2 rounded font-bold text-white" disabled>
                            📈 PUMP
                        </button>
                        <button id="guessDump" class="bg-red-600/30 hover:bg-red-600/50 py-2 rounded font-bold text-white" disabled>
                            📉 DUMP
                        </button>
                    </div>
                    
                    <button id="withdrawProfit" class="bg-yellow-600/30 hover:bg-yellow-600/50 w-full py-2 rounded font-bold text-white mb-4" disabled>
                        💎 WITHDRAW PROFITS
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🔥 Trading Streak</div>
                        <div id="tradingStreak" class="text-2xl font-bold text-orange-400 neon-glow">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">👑 Kingdom Level</div>
                        <div id="kingdomLevel" class="text-lg font-bold text-purple-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏦 Crypto Vault</div>
                        <div id="cryptoVault" class="text-lg font-bold text-blue-400">0 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">📊 Multiplier</div>
                        <div id="tradingMultiplier" class="text-lg font-bold text-green-400">1.00x</div>
                    </div>
                </div>
            </div>
            
            <!-- Market Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h5 class="text-lg font-bold mb-4 text-blue-400 text-center">📱 CRYPTO EXCHANGE 📱</h5>
                    
                    <!-- Current Crypto -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 text-center">CURRENT POSITION</div>
                        <div class="bg-black/50 p-4 rounded-lg border border-blue-400">
                            <div id="currentCrypto" class="text-center text-2xl font-bold text-blue-400 mb-2">
                                Select Trading Pair
                            </div>
                            <div id="cryptoPrice" class="text-center text-lg font-bold text-white">$0.00</div>
                            <div id="priceChange" class="text-center text-sm text-gray-400">Ready to Trade</div>
                        </div>
                    </div>
                    
                    <!-- Market Indicators -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 text-center">MARKET SENTIMENT</div>
                        <div class="grid grid-cols-3 gap-2 text-center text-xs">
                            <div class="bg-green-600/20 p-2 rounded">
                                <div class="text-green-400 font-bold">BULL RUN</div>
                                <div id="bullStatus" class="text-gray-300">Inactive</div>
                            </div>
                            <div class="bg-red-600/20 p-2 rounded">
                                <div class="text-red-400 font-bold">BEAR MARKET</div>
                                <div id="bearStatus" class="text-gray-300">Inactive</div>
                            </div>
                            <div class="bg-purple-600/20 p-2 rounded">
                                <div class="text-purple-400 font-bold">WHALE ALERT</div>
                                <div id="whaleStatus" class="text-gray-300">Quiet</div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="cryptoResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="cryptoStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Trading History -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">📈 TRADING HISTORY</h5>
                    <div id="tradingHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Market Volatility -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">⚡ MARKET VOLATILITY ⚡</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="volatilityBar" class="bg-gradient-to-r from-green-500 to-red-500 h-4 rounded-full transition-all duration-300" style="width: 50%"></div>
                    </div>
                    <div id="volatilityText" class="text-center mt-2 text-orange-300">Moderate</div>
                </div>
            </div>
        </div>
    `;
    
    initializeHiloCrypto();
}

function initializeHiloCrypto() {
    document.getElementById('startCryptoTrading').addEventListener('click', startCryptoTrading);
    document.getElementById('guessPump').addEventListener('click', () => makeTrade('pump'));
    document.getElementById('guessDump').addEventListener('click', () => makeTrade('dump'));
    document.getElementById('withdrawProfit').addEventListener('click', withdrawProfits);
    document.getElementById('tradingMode').addEventListener('change', updateTradingMode);
    
    initializeCryptoMarket();
    updateTradingMode();
}

function updateTradingMode() {
    const mode = document.getElementById('tradingMode').value;
    hiloCryptoGame.gameMode = mode;
    
    // Update multiplier based on mode (but keep win rates terrible)
    switch(mode) {
        case 'standard': hiloCryptoGame.multiplier = 1.0; break;
        case 'leverage': hiloCryptoGame.multiplier = 1.8; break;
        case 'futures': hiloCryptoGame.multiplier = 2.5; break;
        case 'defi': hiloCryptoGame.multiplier = 4.0; break;
    }
    
    document.getElementById('tradingMultiplier').textContent = hiloCryptoGame.multiplier.toFixed(2) + 'x';
}

function initializeCryptoMarket() {
    hiloCryptoGame.cryptoMarket = [];
    CRYPTO_CURRENCIES.forEach(crypto => {
        const currentPrice = crypto.baseValue * (0.8 + Math.random() * 0.4);
        hiloCryptoGame.cryptoMarket.push({
            ...crypto,
            currentPrice: currentPrice,
            previousPrice: currentPrice
        });
    });
}

async function startCryptoTrading() {
    const betAmount = parseInt(document.getElementById('cryptoBet').value);

    if (hiloCryptoGame.isPlaying) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    hiloCryptoGame.betAmount = betAmount;
    hiloCryptoGame.isPlaying = true;
    hiloCryptoGame.streak = 0;
    hiloCryptoGame.tradeHistory = [];
    
    // Select random crypto
    selectRandomCrypto();
    
    // Enable trading buttons
    document.getElementById('guessPump').disabled = false;
    document.getElementById('guessDump').disabled = false;
    document.getElementById('withdrawProfit').disabled = false;
    document.getElementById('startCryptoTrading').disabled = true;
    
    document.getElementById('cryptoResult').textContent = '';
    document.getElementById('cryptoStatus').textContent = 'Market analysis complete. Make your prediction!';
    
    updateMarketDisplay();
}

function selectRandomCrypto() {
    const randomIndex = Math.floor(Math.random() * hiloCryptoGame.cryptoMarket.length);
    hiloCryptoGame.currentCrypto = hiloCryptoGame.cryptoMarket[randomIndex];
    
    // Update market volatility
    hiloCryptoGame.marketVolatility = 30 + Math.random() * 70;
    
    // Random market events (mostly negative)
    hiloCryptoGame.bullRun = Math.random() < 0.08; // 8% chance
    hiloCryptoGame.bearMarket = Math.random() < 0.25; // 25% chance
    hiloCryptoGame.whaleActivity = Math.random() < 0.15; // 15% chance
    
    displayCurrentCrypto();
}

function displayCurrentCrypto() {
    const crypto = hiloCryptoGame.currentCrypto;
    document.getElementById('currentCrypto').textContent = 
        `${crypto.symbol} ${crypto.name}`;
    document.getElementById('cryptoPrice').textContent = 
        `$${crypto.currentPrice.toFixed(crypto.currentPrice < 1 ? 6 : 2)}`;
    
    // Update market status
    document.getElementById('bullStatus').textContent = hiloCryptoGame.bullRun ? 'ACTIVE' : 'Inactive';
    document.getElementById('bearStatus').textContent = hiloCryptoGame.bearMarket ? 'ACTIVE' : 'Inactive';
    document.getElementById('whaleStatus').textContent = hiloCryptoGame.whaleActivity ? 'DETECTED' : 'Quiet';
    
    // Update volatility
    document.getElementById('volatilityBar').style.width = hiloCryptoGame.marketVolatility + '%';
    let volatilityLevel = 'Low';
    if (hiloCryptoGame.marketVolatility > 70) volatilityLevel = 'Extreme';
    else if (hiloCryptoGame.marketVolatility > 50) volatilityLevel = 'High';
    else if (hiloCryptoGame.marketVolatility > 30) volatilityLevel = 'Moderate';
    document.getElementById('volatilityText').textContent = volatilityLevel;
}

function makeTrade(prediction) {
    if (!hiloCryptoGame.isPlaying) return;
    
    // Generate next price with extreme bias against player (< 8% win rate)
    const crypto = hiloCryptoGame.currentCrypto;
    const currentPrice = crypto.currentPrice;
    
    // Extremely biased price movement - 92% chance of wrong prediction
    let priceChange;
    if (Math.random() < 0.92) {
        // Generate movement opposite to prediction
        if (prediction === 'pump') {
            // Generate dump when player predicts pump
            priceChange = -0.05 - (Math.random() * 0.25); // -5% to -30%
        } else {
            // Generate pump when player predicts dump  
            priceChange = 0.05 + (Math.random() * 0.25); // +5% to +30%
        }
    } else {
        // 8% chance of correct prediction
        if (prediction === 'pump') {
            priceChange = 0.03 + (Math.random() * 0.15); // +3% to +18%
        } else {
            priceChange = -0.03 - (Math.random() * 0.15); // -3% to -18%
        }
    }
    
    // Apply market modifiers (mostly negative)
    if (hiloCryptoGame.bearMarket) {
        priceChange -= 0.08; // Bear market penalty
    }
    if (hiloCryptoGame.bullRun && prediction === 'pump') {
        priceChange += 0.05; // Small bull run bonus
    }
    if (hiloCryptoGame.whaleActivity) {
        priceChange *= 1.5; // Amplify movement
    }
    
    const newPrice = currentPrice * (1 + priceChange);
    crypto.previousPrice = currentPrice;
    crypto.currentPrice = newPrice;
    
    // Determine if prediction was correct
    const actualMovement = newPrice > currentPrice ? 'pump' : 'dump';
    const correct = prediction === actualMovement;
    
    // Add to history
    hiloCryptoGame.tradeHistory.push({
        crypto: crypto.name,
        prediction: prediction,
        actual: actualMovement,
        priceChange: ((newPrice - currentPrice) / currentPrice * 100).toFixed(2),
        correct: correct
    });
    
    if (correct) {
        hiloCryptoGame.streak++;
        hiloCryptoGame.kingdomLevel = Math.floor(hiloCryptoGame.streak / 3) + 1;
        
        document.getElementById('cryptoResult').innerHTML = 
            `<span class="text-green-400 neon-glow">PROFITABLE TRADE! 📈</span>`;
        document.getElementById('cryptoStatus').textContent = 
            `Streak: ${hiloCryptoGame.streak} - Continue trading or withdraw profits?`;
        
        // Select new crypto for next trade
        selectRandomCrypto();
        
    } else {
        // Game over - lose everything
        endCryptoTrading(false);
        
        document.getElementById('cryptoResult').innerHTML = 
            `<span class="text-red-400">LIQUIDATED! 💥</span>`;
        document.getElementById('cryptoStatus').textContent = 
            `Market moved ${actualMovement.toUpperCase()}. Your position was liquidated!`;
    }
    
    updateMarketDisplay();
    updateTradingHistory();
}

async function withdrawProfits() {
    if (!hiloCryptoGame.isPlaying || hiloCryptoGame.streak === 0) return;

    await endCryptoTrading(true);
}

async function endCryptoTrading(withdraw) {
    hiloCryptoGame.isPlaying = false;

    if (withdraw && hiloCryptoGame.streak > 0) {
        // Calculate profits with extremely low multipliers
        const baseMultiplier = Math.pow(1.12, hiloCryptoGame.streak); // Very low growth
        const modeBonus = hiloCryptoGame.multiplier;
        const kingdomBonus = 1 + (hiloCryptoGame.kingdomLevel * 0.05);

        const payout = Math.floor(hiloCryptoGame.betAmount * baseMultiplier * modeBonus * kingdomBonus);
        const winnings = payout - hiloCryptoGame.betAmount;
        hiloCryptoGame.cryptoVault += Math.floor(payout * 0.1); // 10% to vault

        if (winnings > 0) {
            await walletIntegration.processWin(winnings);
        }

        document.getElementById('cryptoResult').innerHTML = 
            `<span class="text-green-400 neon-glow">PROFITS WITHDRAWN! 💰</span>`;
        document.getElementById('cryptoStatus').innerHTML = 
            `Earned ${payout} GA with ${hiloCryptoGame.streak} successful trades!`;
    }
    
    // Reset game state
    setTimeout(() => {
        document.getElementById('guessPump').disabled = true;
        document.getElementById('guessDump').disabled = true;
        document.getElementById('withdrawProfit').disabled = true;
        document.getElementById('startCryptoTrading').disabled = false;
        
        hiloCryptoGame.streak = 0;
        updateMarketDisplay();
    }, 3000);
}

function updateMarketDisplay() {
    document.getElementById('tradingStreak').textContent = hiloCryptoGame.streak;
    document.getElementById('kingdomLevel').textContent = hiloCryptoGame.kingdomLevel;
    document.getElementById('cryptoVault').textContent = hiloCryptoGame.cryptoVault + ' GA';
    
    if (hiloCryptoGame.currentCrypto) {
        displayCurrentCrypto();
    }
}

function updateTradingHistory() {
    const history = document.getElementById('tradingHistory');
    const lastTrade = hiloCryptoGame.tradeHistory[hiloCryptoGame.tradeHistory.length - 1];
    
    if (lastTrade) {
        const item = document.createElement('div');
        item.className = `px-2 py-1 rounded text-xs font-bold ${
            lastTrade.correct ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
        }`;
        item.textContent = `${lastTrade.crypto}: ${lastTrade.priceChange}%`;
        
        history.insertBefore(item, history.firstChild);
        
        while (history.children.length > 15) {
            history.removeChild(history.lastChild);
        }
    }
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadHiloCryptoKingsGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

