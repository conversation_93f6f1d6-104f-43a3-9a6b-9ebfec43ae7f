// Game state
let balance = 1000;
let limboSkyGame = {
    isPlaying: false,
    currentHeight: 0,
    targetHeight: 2.0,
    betAmount: 0,
    skyLevel: 1,
    cloudPower: 0,
    gameMode: 'standard',
    weatherCondition: 'clear',
    windSpeed: 0,
    turbulence: false,
    stormWarning: false,
    multiplier: 1.0,
    maxHeight: 0,
    attempts: 0
};

// Weather conditions that affect gameplay
const WEATHER_CONDITIONS = [
    { name: 'clear', effect: 1.0, description: 'Clear Skies' },
    { name: 'cloudy', effect: 1.2, description: 'Cloudy Weather' },
    { name: 'windy', effect: 1.5, description: 'High Winds' },
    { name: 'stormy', effect: 2.0, description: 'Storm Warning' },
    { name: 'hurricane', effect: 3.0, description: 'Hurricane Alert' }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Sky High Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🌌 LIMBO LEGENDS: SKY HIGH 🌌</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 SKY WAGER</label>
                        <input type="number" id="skyBet" value="30" min="5" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET HEIGHT</label>
                        <input type="number" id="targetHeight" value="2.0" min="1.1" max="10.0" step="0.1"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                        <div class="text-xs text-gray-400 mt-1">Potential Payout: <span id="potentialPayout" class="text-green-400">2.00x</span></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌤️ FLIGHT MODE</label>
                        <select id="skyMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Flight</option>
                            <option value="stratosphere">Stratosphere (2x risk)</option>
                            <option value="mesosphere">Mesosphere (3x risk)</option>
                            <option value="thermosphere">Thermosphere (5x risk)</option>
                        </select>
                    </div>
                    
                    <button id="startSkyFlight" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🚀 ASCEND TO SKY 🚀
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">☁️ Current Height</div>
                        <div id="currentSkyHeight" class="text-3xl font-bold text-purple-400 neon-glow">0.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🌟 Sky Level</div>
                        <div id="skyLevelDisplay" class="text-lg font-bold text-blue-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">☁️ Cloud Power</div>
                        <div id="cloudPowerDisplay" class="text-lg font-bold text-cyan-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏔️ Max Height</div>
                        <div id="maxSkyHeight" class="text-lg font-bold text-yellow-400">0.00x</div>
                    </div>
                </div>
            </div>
            
            <!-- Sky Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400 text-center">🌌 SKY DASHBOARD 🌌</h5>
                    
                    <!-- Sky Visualization -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-t from-purple-900 via-blue-800 to-indigo-900 h-48 rounded-lg border border-purple-400 relative overflow-hidden">
                            <div id="skySprite" class="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-2xl transition-all duration-500">
                                🎈
                            </div>
                            <div id="heightMarkers" class="absolute inset-0 text-xs text-gray-400">
                                <!-- Height markers will be generated here -->
                            </div>
                            <div id="clouds" class="absolute inset-0">
                                <!-- Clouds will appear here -->
                            </div>
                            <div id="weatherEffects" class="absolute inset-0">
                                <!-- Weather effects will appear here -->
                            </div>
                            <div id="targetLine" class="absolute w-full h-0.5 bg-green-400 opacity-50" style="bottom: 20%;">
                                <span class="absolute right-2 -top-4 text-xs text-green-400">Target</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Weather Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🌤️ Weather Conditions</div>
                            <div id="weatherStatus" class="text-lg font-bold text-blue-400">Clear Skies</div>
                            <div class="text-xs text-gray-400">Wind Speed: <span id="windSpeed" class="text-cyan-400">0 mph</span></div>
                        </div>
                    </div>
                    
                    <div id="skyResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="skyStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Flight History -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 FLIGHT HISTORY</h5>
                    <div id="skyHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Atmospheric Pressure -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">🌪️ ATMOSPHERIC PRESSURE 🌪️</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="pressureBar" class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-4 rounded-full transition-all duration-300" style="width: 30%"></div>
                    </div>
                    <div id="pressureText" class="text-center mt-2 text-cyan-300">Stable</div>
                </div>
            </div>
        </div>
    `;
    
    initializeSkyHighGame();
}

function initializeSkyHighGame() {
    document.getElementById('startSkyFlight').addEventListener('click', startSkyFlight);
    document.getElementById('targetHeight').addEventListener('input', updatePotentialPayout);
    document.getElementById('skyMode').addEventListener('change', updateSkyMode);
    
    updateSkyMode();
    generateHeightMarkers();
    updatePotentialPayout();
}

function updateSkyMode() {
    const mode = document.getElementById('skyMode').value;
    limboSkyGame.gameMode = mode;
    
    // Update multiplier based on mode
    switch(mode) {
        case 'standard': limboSkyGame.multiplier = 1.0; break;
        case 'stratosphere': limboSkyGame.multiplier = 1.8; break;
        case 'mesosphere': limboSkyGame.multiplier = 2.5; break;
        case 'thermosphere': limboSkyGame.multiplier = 4.0; break;
    }
}

function updatePotentialPayout() {
    const target = parseFloat(document.getElementById('targetHeight').value);
    const payout = target * limboSkyGame.multiplier;
    document.getElementById('potentialPayout').textContent = payout.toFixed(2) + 'x';
    
    // Update target line position
    const targetLine = document.getElementById('targetLine');
    if (targetLine) {
        const position = Math.min(90, (target - 1) * 30);
        targetLine.style.bottom = position + '%';
    }
}

function generateHeightMarkers() {
    const markers = document.getElementById('heightMarkers');
    markers.innerHTML = '';
    
    const heights = [1.5, 2.0, 3.0, 5.0, 10.0];
    heights.forEach((height, index) => {
        const marker = document.createElement('div');
        marker.className = 'absolute right-2 text-xs';
        marker.style.bottom = `${20 + index * 15}%`;
        marker.textContent = `${height}x`;
        markers.appendChild(marker);
    });
}

function startSkyFlight() {
    const betAmount = parseInt(document.getElementById('skyBet').value);
    const targetHeight = parseFloat(document.getElementById('targetHeight').value);
    
    if (betAmount > balance) {
        alert('Insufficient sky credits!');
        return;
    }
    
    if (limboSkyGame.isPlaying) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    limboSkyGame.betAmount = betAmount;
    limboSkyGame.targetHeight = targetHeight;
    limboSkyGame.isPlaying = true;
    limboSkyGame.currentHeight = 1.0;
    limboSkyGame.attempts++;
    
    // Generate weather conditions (mostly bad)
    generateWeatherConditions();
    
    // Disable start button
    document.getElementById('startSkyFlight').disabled = true;
    
    document.getElementById('skyResult').textContent = '';
    document.getElementById('skyStatus').textContent = 'Ascending through the atmosphere...';
    
    // Start sky simulation
    startSkySimulation();
}

function generateWeatherConditions() {
    // Bias towards bad weather (80% chance of adverse conditions)
    let weatherIndex;
    if (Math.random() < 0.8) {
        // Bad weather (cloudy to hurricane)
        weatherIndex = 1 + Math.floor(Math.random() * 4);
    } else {
        // Clear weather (20% chance)
        weatherIndex = 0;
    }
    
    const weather = WEATHER_CONDITIONS[weatherIndex];
    limboSkyGame.weatherCondition = weather.name;
    limboSkyGame.windSpeed = Math.floor(Math.random() * 50) + 10;
    limboSkyGame.turbulence = Math.random() < 0.4;
    limboSkyGame.stormWarning = weather.effect > 2.0;
    
    updateWeatherDisplay();
}

function updateWeatherDisplay() {
    const weather = WEATHER_CONDITIONS.find(w => w.name === limboSkyGame.weatherCondition);
    document.getElementById('weatherStatus').textContent = weather.description;
    document.getElementById('windSpeed').textContent = limboSkyGame.windSpeed + ' mph';
    
    // Update pressure based on weather
    const pressure = Math.min(100, weather.effect * 25 + limboSkyGame.windSpeed);
    document.getElementById('pressureBar').style.width = pressure + '%';
    
    let pressureLevel = 'Stable';
    if (pressure > 80) pressureLevel = 'Critical';
    else if (pressure > 60) pressureLevel = 'High';
    else if (pressure > 40) pressureLevel = 'Moderate';
    document.getElementById('pressureText').textContent = pressureLevel;
}

function startSkySimulation() {
    const skyInterval = setInterval(() => {
        if (!limboSkyGame.isPlaying) {
            clearInterval(skyInterval);
            return;
        }
        
        // Calculate crash probability (extremely high - 90%+ chance)
        const crashChance = calculateSkyCrashProbability();
        
        if (Math.random() < crashChance) {
            // Crash before reaching target
            crashSkyFlight();
            clearInterval(skyInterval);
            return;
        }
        
        // Increase height slightly
        limboSkyGame.currentHeight += 0.05 + (Math.random() * 0.1);
        limboSkyGame.maxHeight = Math.max(limboSkyGame.maxHeight, limboSkyGame.currentHeight);
        
        // Check if target reached (very rare)
        if (limboSkyGame.currentHeight >= limboSkyGame.targetHeight) {
            reachSkyTarget();
            clearInterval(skyInterval);
            return;
        }
        
        updateSkyDisplay();
        
    }, 200); // Update every 200ms for smooth animation
}

function calculateSkyCrashProbability() {
    // Base crash probability is extremely high (90%)
    let crashChance = 0.90;
    
    // Weather makes it worse
    const weather = WEATHER_CONDITIONS.find(w => w.name === limboSkyGame.weatherCondition);
    crashChance += (weather.effect - 1) * 0.15;
    
    // Height increases crash chance exponentially
    crashChance += Math.pow(limboSkyGame.currentHeight - 1, 2) * 0.25;
    
    // Mode multipliers increase crash chance
    switch(limboSkyGame.gameMode) {
        case 'stratosphere': crashChance *= 1.2; break;
        case 'mesosphere': crashChance *= 1.4; break;
        case 'thermosphere': crashChance *= 1.6; break;
    }
    
    // Wind and turbulence
    if (limboSkyGame.turbulence) crashChance *= 1.3;
    crashChance += (limboSkyGame.windSpeed / 100) * 0.2;
    
    // Cap at 98% to give tiny hope
    return Math.min(0.98, crashChance);
}

function crashSkyFlight() {
    limboSkyGame.isPlaying = false;
    
    document.getElementById('skyResult').innerHTML = 
        `<span class="text-red-400">💥 ATMOSPHERIC FAILURE! 💥</span>`;
    
    let crashReason = 'Altitude sickness';
    if (limboSkyGame.stormWarning) crashReason = 'Storm collision';
    else if (limboSkyGame.turbulence) crashReason = 'Severe turbulence';
    else if (limboSkyGame.windSpeed > 40) crashReason = 'Wind shear';
    else if (limboSkyGame.currentHeight > 3.0) crashReason = 'Oxygen depletion';
    
    document.getElementById('skyStatus').textContent = 
        `Crashed at ${limboSkyGame.currentHeight.toFixed(2)}x due to ${crashReason}!`;
    
    addSkyHistory(false);
    resetSkyControls();
}

function reachSkyTarget() {
    limboSkyGame.isPlaying = false;
    limboSkyGame.skyLevel++;
    limboSkyGame.cloudPower += Math.floor(limboSkyGame.targetHeight);
    
    // Calculate winnings (very low multipliers)
    const baseWinnings = limboSkyGame.betAmount * limboSkyGame.targetHeight;
    const modeBonus = limboSkyGame.multiplier;
    const weatherPenalty = WEATHER_CONDITIONS.find(w => w.name === limboSkyGame.weatherCondition).effect;
    
    const winnings = Math.floor(baseWinnings * modeBonus / weatherPenalty);
    balance += winnings;
    updateBalance();
    
    document.getElementById('skyResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🌟 SKY TARGET REACHED! 🌟</span>`;
    document.getElementById('skyStatus').innerHTML = 
        `Reached ${limboSkyGame.targetHeight.toFixed(2)}x! Earned ${winnings} GA!`;
    
    addSkyHistory(true);
    resetSkyControls();
}

function updateSkyDisplay() {
    document.getElementById('currentSkyHeight').textContent = limboSkyGame.currentHeight.toFixed(2) + 'x';
    document.getElementById('skyLevelDisplay').textContent = limboSkyGame.skyLevel;
    document.getElementById('cloudPowerDisplay').textContent = limboSkyGame.cloudPower;
    document.getElementById('maxSkyHeight').textContent = limboSkyGame.maxHeight.toFixed(2) + 'x';
    
    // Update sky sprite position
    const sprite = document.getElementById('skySprite');
    const maxDisplayHeight = 10.0;
    const position = Math.min(90, ((limboSkyGame.currentHeight - 1) / (maxDisplayHeight - 1)) * 90);
    sprite.style.bottom = position + '%';
    
    // Generate weather effects
    generateWeatherEffects();
}

function generateWeatherEffects() {
    const effects = document.getElementById('weatherEffects');
    effects.innerHTML = '';
    
    // Add weather-based visual effects
    if (limboSkyGame.weatherCondition !== 'clear') {
        for (let i = 0; i < 5; i++) {
            const effect = document.createElement('div');
            effect.className = 'absolute text-lg opacity-60';
            
            switch(limboSkyGame.weatherCondition) {
                case 'cloudy': effect.textContent = '☁️'; break;
                case 'windy': effect.textContent = '💨'; break;
                case 'stormy': effect.textContent = '⛈️'; break;
                case 'hurricane': effect.textContent = '🌪️'; break;
            }
            
            effect.style.left = Math.random() * 80 + '%';
            effect.style.top = Math.random() * 80 + '%';
            effects.appendChild(effect);
        }
    }
}

function addSkyHistory(successful) {
    const history = document.getElementById('skyHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-xs font-bold ${
        successful ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = `${limboSkyGame.currentHeight.toFixed(2)}x`;
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 15) {
        history.removeChild(history.lastChild);
    }
}

function resetSkyControls() {
    setTimeout(() => {
        document.getElementById('startSkyFlight').disabled = false;
        
        // Reset sprite position
        document.getElementById('skySprite').style.bottom = '2%';
        
        // Clear effects
        document.getElementById('weatherEffects').innerHTML = '';
        
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

