// Game state
let balance = 1000;
let blackHoleGame = {
    isPlaying: false,
    selectedNumbers: new Set(),
    drawnNumbers: [],
    betAmount: 0,
    gravitationalPull: 1,
    blackHoleIntensity: 0,
    eventHorizon: false
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadBlackHoleGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">BLACK HOLE BONANZA</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="blackHoleBet" value="10" min="1" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GRAVITATIONAL PULL</label>
                        <input type="range" id="gravitySlider" min="1" max="5" value="1" 
                               class="w-full accent-purple-500">
                        <div class="flex justify-between text-sm text-gray-400">
                            <span>Weak</span>
                            <span id="gravityValue">1</span>
                            <span>Extreme</span>
                        </div>
                    </div>
                    
                    <button id="playBlackHole" class="cyber-button w-full py-3 rounded-lg font-bold text-white">
                        ACTIVATE BLACK HOLE
                    </button>
                    
                    <div class="mt-4 text-center">
                        <div class="text-sm text-gray-400 mb-1">Selected Numbers</div>
                        <div id="selectedCount" class="text-xl font-bold text-purple-400">0/10</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Event Horizon</div>
                        <div id="eventHorizon" class="text-lg font-bold text-red-400">STABLE</div>
                    </div>
                </div>
            </div>
            
            <!-- Number Grid -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400">COSMIC NUMBERS</h5>
                    <div id="numberGrid" class="grid grid-cols-8 gap-2"></div>
                    <div class="mt-4 text-center">
                        <button id="clearSelection" class="cyber-button-secondary px-4 py-2 rounded text-sm">
                            CLEAR ALL
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Black Hole Visualization -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="blackHoleCanvas" class="relative bg-gradient-to-br from-black to-purple-900 rounded-lg h-64 overflow-hidden">
                        <div id="blackHoleCore" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-black rounded-full border-2 border-purple-500"></div>
                        <div id="accretionDisk" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 border-2 border-purple-400 rounded-full opacity-50"></div>
                        <div id="drawnNumbersDisplay" class="absolute inset-0"></div>
                    </div>
                    <div id="blackHoleStatus" class="text-center mt-4 text-lg font-semibold">Select numbers to feed the black hole</div>
                </div>
                
                <!-- Results -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">COSMIC RESULTS</h5>
                    <div id="blackHoleResults" class="text-center text-gray-300">
                        <div>Numbers Consumed: <span id="numbersConsumed">0</span></div>
                        <div>Gravitational Bonus: <span id="gravityBonus">0x</span></div>
                        <div>Total Winnings: <span id="totalWinnings" class="text-green-400">0 GA</span></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    initializeBlackHole();
}

function initializeBlackHole() {
    document.getElementById('playBlackHole').addEventListener('click', startBlackHole);
    document.getElementById('clearSelection').addEventListener('click', clearSelection);
    document.getElementById('gravitySlider').addEventListener('input', updateGravity);
    
    createNumberGrid();
    updateGravity();
}

function createNumberGrid() {
    const grid = document.getElementById('numberGrid');
    grid.innerHTML = '';
    
    for (let i = 1; i <= 80; i++) {
        const numberBtn = document.createElement('button');
        numberBtn.className = 'w-8 h-8 bg-black/50 border border-purple-500/50 rounded text-white text-sm hover:bg-purple-600/30 transition-colors';
        numberBtn.textContent = i;
        numberBtn.addEventListener('click', () => toggleNumber(i, numberBtn));
        grid.appendChild(numberBtn);
    }
}

function toggleNumber(number, button) {
    if (blackHoleGame.selectedNumbers.has(number)) {
        blackHoleGame.selectedNumbers.delete(number);
        button.classList.remove('bg-purple-600', 'border-purple-400');
        button.classList.add('bg-black/50', 'border-purple-500/50');
    } else {
        if (blackHoleGame.selectedNumbers.size >= 10) {
            alert('Maximum 10 numbers can be selected!');
            return;
        }
        blackHoleGame.selectedNumbers.add(number);
        button.classList.remove('bg-black/50', 'border-purple-500/50');
        button.classList.add('bg-purple-600', 'border-purple-400');
    }
    
    updateSelectedCount();
}

function clearSelection() {
    blackHoleGame.selectedNumbers.clear();
    const buttons = document.querySelectorAll('#numberGrid button');
    buttons.forEach(btn => {
        btn.classList.remove('bg-purple-600', 'border-purple-400');
        btn.classList.add('bg-black/50', 'border-purple-500/50');
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    document.getElementById('selectedCount').textContent = `${blackHoleGame.selectedNumbers.size}/10`;
}

function updateGravity() {
    const gravity = document.getElementById('gravitySlider').value;
    document.getElementById('gravityValue').textContent = gravity;
    blackHoleGame.gravitationalPull = parseInt(gravity);
    
    // Update event horizon status
    const eventHorizonEl = document.getElementById('eventHorizon');
    if (gravity >= 4) {
        eventHorizonEl.textContent = 'CRITICAL';
        eventHorizonEl.className = 'text-lg font-bold text-red-400 animate-pulse';
    } else if (gravity >= 3) {
        eventHorizonEl.textContent = 'UNSTABLE';
        eventHorizonEl.className = 'text-lg font-bold text-yellow-400';
    } else {
        eventHorizonEl.textContent = 'STABLE';
        eventHorizonEl.className = 'text-lg font-bold text-green-400';
    }
}

function startBlackHole() {
    const betAmount = parseInt(document.getElementById('blackHoleBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    if (blackHoleGame.selectedNumbers.size === 0) {
        alert('Please select at least one number!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    blackHoleGame.isPlaying = true;
    blackHoleGame.betAmount = betAmount;
    blackHoleGame.blackHoleIntensity = 0;
    
    document.getElementById('playBlackHole').disabled = true;
    document.getElementById('blackHoleStatus').textContent = 'Black hole consuming cosmic numbers...';
    
    // Generate drawn numbers with gravitational influence
    generateBlackHoleNumbers();
    animateBlackHole();
}

function generateBlackHoleNumbers() {
    blackHoleGame.drawnNumbers = [];
    const totalNumbers = 20; // Draw 20 numbers like keno
    
    // Gravitational pull affects number distribution
    const gravityEffect = blackHoleGame.gravitationalPull / 5;
    
    while (blackHoleGame.drawnNumbers.length < totalNumbers) {
        let randomNumber;
        
        if (Math.random() < gravityEffect * 0.3) {
            // Gravity pulls selected numbers (reduces win chance)
            const selectedArray = Array.from(blackHoleGame.selectedNumbers);
            if (selectedArray.length > 0 && Math.random() < 0.15) { // Only 15% chance even with gravity
                randomNumber = selectedArray[Math.floor(Math.random() * selectedArray.length)];
            } else {
                randomNumber = Math.floor(Math.random() * 80) + 1;
            }
        } else {
            randomNumber = Math.floor(Math.random() * 80) + 1;
        }
        
        if (!blackHoleGame.drawnNumbers.includes(randomNumber)) {
            blackHoleGame.drawnNumbers.push(randomNumber);
        }
    }
}

function animateBlackHole() {
    let drawnSoFar = 0;
    const drawInterval = setInterval(() => {
        if (drawnSoFar < blackHoleGame.drawnNumbers.length) {
            const number = blackHoleGame.drawnNumbers[drawnSoFar];
            
            // Animate number being consumed
            animateNumberConsumption(number);
            
            // Update black hole intensity
            blackHoleGame.blackHoleIntensity += 5;
            updateBlackHoleVisual();
            
            drawnSoFar++;
        } else {
            clearInterval(drawInterval);
            calculateBlackHoleResult();
        }
    }, 200);
}

function animateNumberConsumption(number) {
    const display = document.getElementById('drawnNumbersDisplay');
    const numberEl = document.createElement('div');
    numberEl.className = 'absolute w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold';
    numberEl.textContent = number;
    
    // Random starting position
    numberEl.style.left = Math.random() * 80 + 10 + '%';
    numberEl.style.top = Math.random() * 80 + 10 + '%';
    
    display.appendChild(numberEl);
    
    // Animate towards black hole center
    setTimeout(() => {
        numberEl.style.transition = 'all 1s ease-in';
        numberEl.style.left = '50%';
        numberEl.style.top = '50%';
        numberEl.style.transform = 'translate(-50%, -50%) scale(0)';
        numberEl.style.opacity = '0';
    }, 100);
    
    // Remove element
    setTimeout(() => {
        if (numberEl.parentNode) {
            numberEl.parentNode.removeChild(numberEl);
        }
    }, 1200);
}

function updateBlackHoleVisual() {
    const core = document.getElementById('blackHoleCore');
    const disk = document.getElementById('accretionDisk');
    
    const intensity = Math.min(100, blackHoleGame.blackHoleIntensity);
    const scale = 1 + (intensity / 200);
    
    core.style.transform = `translate(-50%, -50%) scale(${scale})`;
    disk.style.transform = `translate(-50%, -50%) scale(${scale}) rotate(${intensity * 3.6}deg)`;
    
    // Add glow effect
    core.style.boxShadow = `0 0 ${intensity/2}px purple`;
}

function calculateBlackHoleResult() {
    // Count consumed numbers (hits)
    let consumed = 0;
    blackHoleGame.selectedNumbers.forEach(number => {
        if (blackHoleGame.drawnNumbers.includes(number)) {
            consumed++;
        }
    });
    
    // Calculate payout with very low win rate
    const selectedCount = blackHoleGame.selectedNumbers.size;
    let payout = 0;
    
    // Extremely low win rates (< 20%)
    const payoutTable = {
        1: { 1: 2.5 },
        2: { 2: 8 },
        3: { 2: 1, 3: 25 },
        4: { 3: 2, 4: 50 },
        5: { 3: 1, 4: 15, 5: 100 },
        6: { 4: 5, 5: 50, 6: 200 },
        7: { 4: 2, 5: 20, 6: 100, 7: 500 },
        8: { 5: 10, 6: 50, 7: 200, 8: 1000 },
        9: { 5: 5, 6: 25, 7: 100, 8: 500, 9: 2000 },
        10: { 6: 10, 7: 50, 8: 200, 9: 1000, 10: 5000 }
    };
    
    payout = payoutTable[selectedCount]?.[consumed] || 0;
    
    // Gravitational bonus (risk vs reward)
    const gravityBonus = blackHoleGame.gravitationalPull * 0.5;
    const finalPayout = payout * (1 + gravityBonus);
    
    const winnings = Math.floor(blackHoleGame.betAmount * finalPayout);
    balance += winnings;
    updateBalance();
    
    // Update results display
    document.getElementById('numbersConsumed').textContent = consumed;
    document.getElementById('gravityBonus').textContent = (1 + gravityBonus).toFixed(1) + 'x';
    document.getElementById('totalWinnings').textContent = winnings + ' GA';
    
    if (winnings > blackHoleGame.betAmount) {
        document.getElementById('blackHoleStatus').innerHTML = 
            `<span class="text-green-400 neon-glow">Black hole rewards you! ${consumed}/${selectedCount} numbers consumed!</span>`;
    } else {
        document.getElementById('blackHoleStatus').innerHTML = 
            `<span class="text-red-400">The void consumes all! ${consumed}/${selectedCount} numbers consumed.</span>`;
    }
    
    resetBlackHoleGame();
}

function resetBlackHoleGame() {
    setTimeout(() => {
        blackHoleGame.isPlaying = false;
        document.getElementById('playBlackHole').disabled = false;
        document.getElementById('blackHoleStatus').textContent = 'Select numbers to feed the black hole';
        
        // Reset visual
        const core = document.getElementById('blackHoleCore');
        const disk = document.getElementById('accretionDisk');
        core.style.transform = 'translate(-50%, -50%) scale(1)';
        disk.style.transform = 'translate(-50%, -50%) scale(1) rotate(0deg)';
        core.style.boxShadow = 'none';
        
        // Clear drawn numbers display
        document.getElementById('drawnNumbersDisplay').innerHTML = '';
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBlackHoleGame();
});

