// Game state
let balance = 1000;
let speedRouletteGame = {
    isSpinning: false,
    betAmount: 0,
    targetMultiplier: 2.0,
    currentMultiplier: 1.0,
    maxMultiplier: 1.0,
    spinSpeed: 'normal',
    rouletteMode: 'classic',
    ballMaterial: 'steel',
    wheelCondition: 'new',
    consecutiveWins: 0,
    totalSpins: 0,
    crashCount: 0,
    maxSpeed: 0,
    spinLevel: 1,
    wheelWear: 0
};

// Spin speeds that affect crash probability
const SPIN_SPEEDS = [
    { name: 'slow', crashBonus: 0.86, description: 'Slow Spin (10s)' },
    { name: 'normal', crashBonus: 0.89, description: 'Normal Spin (7s)' },
    { name: 'fast', crashBonus: 0.92, description: 'Fast Spin (5s)' },
    { name: 'turbo', crashBonus: 0.95, description: 'Turbo Spin (3s)' },
    { name: 'lightning', crashBonus: 0.98, description: 'Lightning Spin (1s)' }
];

// Roulette modes
const ROULETTE_MODES = [
    { name: 'classic', multiplier: 1.0, description: 'Classic European' },
    { name: 'american', multiplier: 1.2, description: 'American (Double Zero)' },
    { name: 'french', multiplier: 1.4, description: 'French Rules' },
    { name: 'mini', multiplier: 1.6, description: 'Mini Roulette' },
    { name: 'multi', multiplier: 2.0, description: 'Multi-Wheel Chaos' }
];

// Ball materials
const BALL_MATERIALS = [
    { name: 'plastic', friction: 0.85, description: 'Plastic Ball' },
    { name: 'steel', friction: 0.88, description: 'Steel Ball' },
    { name: 'ceramic', friction: 0.91, description: 'Ceramic Ball' },
    { name: 'titanium', friction: 0.94, description: 'Titanium Ball' },
    { name: 'quantum', friction: 0.97, description: 'Quantum Ball' }
];

// Wheel conditions
const WHEEL_CONDITIONS = [
    { name: 'new', stability: 1.0, description: 'New Wheel' },
    { name: 'used', stability: 1.1, description: 'Used Wheel' },
    { name: 'worn', stability: 1.3, description: 'Worn Wheel' },
    { name: 'damaged', stability: 1.6, description: 'Damaged Wheel' },
    { name: 'broken', stability: 2.2, description: 'Broken Wheel' }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Speed Roulette Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">🎰 SPEED ROULETTE: 10-SECOND SPIN 🎰</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 SPIN BET</label>
                        <input type="number" id="rouletteBet" value="40" min="10" max="${balance}" 
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET MULTIPLIER</label>
                        <input type="number" id="rouletteTargetMultiplier" value="2.0" min="1.1" max="100" step="0.1" 
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ SPIN SPEED</label>
                        <select id="spinSpeed" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="slow">🐌 Slow Spin (10s)</option>
                            <option value="normal" selected>⚡ Normal Spin (7s)</option>
                            <option value="fast">🚀 Fast Spin (5s)</option>
                            <option value="turbo">💨 Turbo Spin (3s)</option>
                            <option value="lightning">⚡ Lightning Spin (1s)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎲 ROULETTE MODE</label>
                        <select id="rouletteMode" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic">Classic European</option>
                            <option value="american">American (Double Zero)</option>
                            <option value="french">French Rules (Harder)</option>
                            <option value="mini">Mini Roulette (Extreme)</option>
                            <option value="multi">Multi-Wheel Chaos (Impossible)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚪ BALL MATERIAL</label>
                        <select id="ballMaterial" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="plastic">Plastic Ball</option>
                            <option value="steel" selected>Steel Ball</option>
                            <option value="ceramic">Ceramic Ball (Harder)</option>
                            <option value="titanium">Titanium Ball (Extreme)</option>
                            <option value="quantum">Quantum Ball (Impossible)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎡 WHEEL CONDITION</label>
                        <select id="wheelCondition" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="new" selected>New Wheel</option>
                            <option value="used">Used Wheel</option>
                            <option value="worn">Worn Wheel (Harder)</option>
                            <option value="damaged">Damaged Wheel (Extreme)</option>
                            <option value="broken">Broken Wheel (Impossible)</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="startSpeedSpin" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🎰 START SPEED SPIN
                        </button>
                        <button id="stopSpin" class="bg-red-600 hover:bg-red-700 flex-1 py-3 rounded-lg font-bold text-white" disabled>
                            🛑 STOP SPIN
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Win Probability</div>
                        <div id="rouletteWinChance" class="text-lg font-bold text-red-400">7%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="roulettePotentialPayout" class="text-2xl font-bold text-red-400 neon-glow">80 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🎰 Spin Level</div>
                        <div id="spinLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⚙️ Wheel Wear</div>
                        <div id="wheelWearDisplay" class="text-lg font-bold text-yellow-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Speed Roulette Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h5 class="text-lg font-bold mb-4 text-red-400 text-center">🎰 SPEED ROULETTE WHEEL 🎰</h5>
                    
                    <!-- Roulette Multiplier Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-red-900/50 to-black/50 p-8 rounded-xl border border-red-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🌟 Current Multiplier</div>
                                <div id="rouletteMultiplierDisplay" class="text-8xl font-bold text-red-400 neon-glow mb-4">1.00x</div>
                                <div class="text-sm text-gray-400">Target: <span id="rouletteTargetDisplay" class="text-red-400">2.00x</span></div>
                                <div class="text-sm text-gray-400">Max Speed: <span id="maxSpeedDisplay" class="text-green-400">0 RPM</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Spin Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🎡 Spin Status</div>
                            <div id="spinStatus" class="text-lg font-bold text-red-400">Ready to spin...</div>
                            <div class="text-xs text-gray-400">Speed: <span id="speedStatus" class="text-blue-400">Normal</span></div>
                            <div class="text-xs text-gray-400">Mode: <span id="modeStatus" class="text-green-400">Classic</span></div>
                            <div class="text-xs text-gray-400">Ball: <span id="ballStatus" class="text-orange-400">Steel</span></div>
                            <div class="text-xs text-gray-400">Wheel: <span id="wheelStatus" class="text-purple-400">New</span></div>
                        </div>
                    </div>
                    
                    <!-- Spin Timer -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">⏱️ Spin Timer</div>
                            <div class="w-full bg-black/50 rounded-full h-4">
                                <div id="spinTimer" class="bg-gradient-to-r from-red-500 to-orange-500 h-4 rounded-full transition-all duration-100" style="width: 0%"></div>
                            </div>
                            <div id="timeRemaining" class="text-center mt-2 text-red-300">10.0s</div>
                        </div>
                    </div>
                    
                    <div id="rouletteResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="rouletteStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Spin Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">📊 SPIN RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Winning Spins</div>
                            <div id="consecutiveWins" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Spins</div>
                            <div id="totalSpins" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Crash Spins</div>
                            <div id="crashCount" class="text-lg font-bold text-red-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Win Rate</div>
                            <div id="winRate" class="text-lg font-bold text-orange-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Wheel Maintenance -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">🔧 WHEEL MAINTENANCE 🔧</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="maintenanceProgress" class="bg-gradient-to-r from-yellow-500 to-red-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="maintenanceText" class="text-center mt-2 text-yellow-300">Perfect Condition</div>
                </div>
            </div>
        </div>
    `;
    
    initializeSpeedRoulette();
}

function initializeSpeedRoulette() {
    document.getElementById('startSpeedSpin').addEventListener('click', startSpeedSpin);
    document.getElementById('stopSpin').addEventListener('click', stopSpin);
    document.getElementById('rouletteTargetMultiplier').addEventListener('input', updateRoulettePayout);
    document.getElementById('spinSpeed').addEventListener('change', updateSpinSpeed);
    document.getElementById('rouletteMode').addEventListener('change', updateRouletteMode);
    document.getElementById('ballMaterial').addEventListener('change', updateBallMaterial);
    document.getElementById('wheelCondition').addEventListener('change', updateWheelCondition);
    
    updateSpinSpeed();
    updateRouletteMode();
    updateBallMaterial();
    updateWheelCondition();
    updateRoulettePayout();
}

function updateSpinSpeed() {
    const speed = document.getElementById('spinSpeed').value;
    speedRouletteGame.spinSpeed = speed;
    
    const speedData = SPIN_SPEEDS.find(s => s.name === speed);
    document.getElementById('speedStatus').textContent = speedData.description;
    
    updateRoulettePayout();
}

function updateRouletteMode() {
    const mode = document.getElementById('rouletteMode').value;
    speedRouletteGame.rouletteMode = mode;
    
    const modeData = ROULETTE_MODES.find(m => m.name === mode);
    document.getElementById('modeStatus').textContent = modeData.description;
    
    updateRoulettePayout();
}

function updateBallMaterial() {
    const material = document.getElementById('ballMaterial').value;
    speedRouletteGame.ballMaterial = material;
    
    const ballData = BALL_MATERIALS.find(b => b.name === material);
    document.getElementById('ballStatus').textContent = ballData.description;
    
    updateRoulettePayout();
}

function updateWheelCondition() {
    const condition = document.getElementById('wheelCondition').value;
    speedRouletteGame.wheelCondition = condition;
    
    const wheelData = WHEEL_CONDITIONS.find(w => w.name === condition);
    document.getElementById('wheelStatus').textContent = wheelData.description;
    
    updateRoulettePayout();
}

function updateRoulettePayout() {
    const betAmount = parseInt(document.getElementById('rouletteBet').value) || 40;
    const targetMultiplier = parseFloat(document.getElementById('rouletteTargetMultiplier').value) || 2.0;
    
    speedRouletteGame.targetMultiplier = targetMultiplier;
    document.getElementById('rouletteTargetDisplay').textContent = targetMultiplier.toFixed(2) + 'x';
    
    // Calculate extremely low success probability
    const speedData = SPIN_SPEEDS.find(s => s.name === speedRouletteGame.spinSpeed);
    const modeData = ROULETTE_MODES.find(m => m.name === speedRouletteGame.rouletteMode);
    const ballData = BALL_MATERIALS.find(b => b.name === speedRouletteGame.ballMaterial);
    const wheelData = WHEEL_CONDITIONS.find(w => w.name === speedRouletteGame.wheelCondition);
    
    // Base crash probability increases with target multiplier
    let baseCrashChance = 0.87 + (targetMultiplier - 1.0) * 0.018;
    
    // Apply all modifiers (make it extremely hard)
    const finalCrashChance = Math.min(0.995, 
        baseCrashChance * speedData.crashBonus * modeData.multiplier * 
        ballData.friction * wheelData.stability);
    
    const successChance = Math.max(1, Math.floor((1 - finalCrashChance) * 100));
    const potentialPayout = Math.floor(betAmount * targetMultiplier);
    
    document.getElementById('rouletteWinChance').textContent = successChance + '%';
    document.getElementById('roulettePotentialPayout').textContent = potentialPayout + ' GA';
}

function startSpeedSpin() {
    const betAmount = parseInt(document.getElementById('rouletteBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient betting chips!');
        return;
    }
    
    if (speedRouletteGame.isSpinning) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    speedRouletteGame.betAmount = betAmount;
    speedRouletteGame.isSpinning = true;
    speedRouletteGame.currentMultiplier = 1.0;
    speedRouletteGame.totalSpins++;
    
    // Disable start button, enable stop
    document.getElementById('startSpeedSpin').disabled = true;
    document.getElementById('stopSpin').disabled = false;
    
    document.getElementById('rouletteResult').textContent = '';
    document.getElementById('rouletteStatus').textContent = 'Roulette wheel spinning...';
    document.getElementById('spinStatus').textContent = 'SPINNING...';
    
    // Start speed roulette simulation
    startRouletteSimulation();
}

function startRouletteSimulation() {
    const speedData = SPIN_SPEEDS.find(s => s.name === speedRouletteGame.spinSpeed);
    const modeData = ROULETTE_MODES.find(m => m.name === speedRouletteGame.rouletteMode);
    const ballData = BALL_MATERIALS.find(b => b.name === speedRouletteGame.ballMaterial);
    const wheelData = WHEEL_CONDITIONS.find(w => w.name === speedRouletteGame.wheelCondition);
    
    // Get spin duration based on speed
    let spinDuration = 10000; // 10 seconds default
    switch(speedRouletteGame.spinSpeed) {
        case 'slow': spinDuration = 10000; break;
        case 'normal': spinDuration = 7000; break;
        case 'fast': spinDuration = 5000; break;
        case 'turbo': spinDuration = 3000; break;
        case 'lightning': spinDuration = 1000; break;
    }
    
    let startTime = Date.now();
    let timeRemaining = spinDuration;
    
    const spinInterval = setInterval(() => {
        if (!speedRouletteGame.isSpinning) {
            clearInterval(spinInterval);
            return;
        }
        
        // Update timer
        const elapsed = Date.now() - startTime;
        timeRemaining = Math.max(0, spinDuration - elapsed);
        const progress = (elapsed / spinDuration) * 100;
        
        document.getElementById('spinTimer').style.width = Math.min(100, progress) + '%';
        document.getElementById('timeRemaining').textContent = (timeRemaining / 1000).toFixed(1) + 's';
        
        // Calculate crash probability (extremely high - 87-99.5% chance)
        const baseCrashChance = 0.87 + (speedRouletteGame.targetMultiplier - 1.0) * 0.018;
        const finalCrashChance = Math.min(0.995, 
            baseCrashChance * speedData.crashBonus * modeData.multiplier * 
            ballData.friction * wheelData.stability);
        
        // Increase crash chance as time progresses
        const timeCrashChance = finalCrashChance + (progress / 100) * 0.02;
        
        if (Math.random() < timeCrashChance || timeRemaining <= 0) {
            // Spin crashes or time runs out
            crashRouletteSpin();
            clearInterval(spinInterval);
            return;
        }
        
        // Increase multiplier slightly
        const increment = 0.012 + (Math.random() * 0.025);
        speedRouletteGame.currentMultiplier += increment;
        speedRouletteGame.maxSpeed = Math.max(speedRouletteGame.maxSpeed, 
            Math.floor(speedRouletteGame.currentMultiplier * 150)); // RPM
        
        // Check if target reached (extremely rare)
        if (speedRouletteGame.currentMultiplier >= speedRouletteGame.targetMultiplier) {
            reachRouletteTarget();
            clearInterval(spinInterval);
            return;
        }
        
        updateRouletteDisplay();
        
    }, 80); // Fast updates for smooth timer
}

function stopSpin() {
    if (!speedRouletteGame.isSpinning) return;
    
    speedRouletteGame.isSpinning = false;
    speedRouletteGame.consecutiveWins++;
    speedRouletteGame.spinLevel++;
    speedRouletteGame.wheelWear += 3;
    
    // Calculate winnings
    const winnings = Math.floor(speedRouletteGame.betAmount * speedRouletteGame.currentMultiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('rouletteResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🎰 SPIN STOPPED! 🎰</span>`;
    document.getElementById('rouletteStatus').innerHTML = 
        `Stopped at ${speedRouletteGame.currentMultiplier.toFixed(2)}x! Won ${winnings} GA!`;
    
    document.getElementById('spinStatus').textContent = 'SPIN SUCCESSFUL';
    
    resetRouletteControls();
}

function crashRouletteSpin() {
    speedRouletteGame.isSpinning = false;
    speedRouletteGame.crashCount++;
    speedRouletteGame.consecutiveWins = 0;
    speedRouletteGame.wheelWear += 8;
    
    document.getElementById('rouletteResult').innerHTML = 
        `<span class="text-red-400">💥 ROULETTE CRASH! 💥</span>`;
    
    let crashReason = 'Ball friction';
    if (speedRouletteGame.currentMultiplier > 3.0) crashReason = 'Centrifugal force';
    else if (speedRouletteGame.spinSpeed === 'lightning' || speedRouletteGame.spinSpeed === 'turbo') crashReason = 'Excessive speed';
    else if (speedRouletteGame.rouletteMode === 'multi' || speedRouletteGame.rouletteMode === 'mini') crashReason = 'Complex wheel mechanics';
    else if (speedRouletteGame.wheelCondition === 'broken' || speedRouletteGame.wheelCondition === 'damaged') crashReason = 'Wheel malfunction';
    
    document.getElementById('rouletteStatus').textContent = 
        `Crashed at ${speedRouletteGame.currentMultiplier.toFixed(2)}x due to ${crashReason}!`;
    
    document.getElementById('spinStatus').textContent = 'SPIN FAILED';
    
    resetRouletteControls();
}

function reachRouletteTarget() {
    speedRouletteGame.isSpinning = false;
    speedRouletteGame.consecutiveWins++;
    speedRouletteGame.spinLevel += 2;
    speedRouletteGame.wheelWear += 5;
    
    // Calculate winnings
    const winnings = Math.floor(speedRouletteGame.betAmount * speedRouletteGame.targetMultiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('rouletteResult').innerHTML = 
        `<span class="text-gold-400 neon-glow">🎰 TARGET HIT! 🎰</span>`;
    document.getElementById('rouletteStatus').innerHTML = 
        `Hit ${speedRouletteGame.targetMultiplier.toFixed(2)}x target! Won ${winnings} GA!`;
    
    document.getElementById('spinStatus').textContent = 'TARGET ACHIEVED';
    
    resetRouletteControls();
}

function resetRouletteControls() {
    setTimeout(() => {
        document.getElementById('startSpeedSpin').disabled = false;
        document.getElementById('stopSpin').disabled = true;
        document.getElementById('spinStatus').textContent = 'Ready to spin...';
        document.getElementById('spinTimer').style.width = '0%';
        document.getElementById('timeRemaining').textContent = '10.0s';
        updateRouletteDisplay();
    }, 3000);
}

function updateRouletteDisplay() {
    document.getElementById('rouletteMultiplierDisplay').textContent = speedRouletteGame.currentMultiplier.toFixed(2) + 'x';
    document.getElementById('maxSpeedDisplay').textContent = speedRouletteGame.maxSpeed + ' RPM';
    document.getElementById('spinLevelDisplay').textContent = speedRouletteGame.spinLevel;
    document.getElementById('wheelWearDisplay').textContent = speedRouletteGame.wheelWear + '%';
    document.getElementById('consecutiveWins').textContent = speedRouletteGame.consecutiveWins;
    document.getElementById('totalSpins').textContent = speedRouletteGame.totalSpins;
    document.getElementById('crashCount').textContent = speedRouletteGame.crashCount;
    
    // Calculate win rate
    const winRate = speedRouletteGame.totalSpins > 0 ? 
        Math.floor((speedRouletteGame.consecutiveWins / speedRouletteGame.totalSpins) * 100) : 0;
    document.getElementById('winRate').textContent = winRate + '%';
    
    // Update maintenance progress
    const maintenanceNeeded = Math.min(100, speedRouletteGame.wheelWear);
    document.getElementById('maintenanceProgress').style.width = maintenanceNeeded + '%';
    
    let maintenanceStatus = 'Perfect Condition';
    if (speedRouletteGame.wheelWear > 80) maintenanceStatus = 'Critical Maintenance Required';
    else if (speedRouletteGame.wheelWear > 60) maintenanceStatus = 'Major Maintenance Needed';
    else if (speedRouletteGame.wheelWear > 40) maintenanceStatus = 'Maintenance Required';
    else if (speedRouletteGame.wheelWear > 20) maintenanceStatus = 'Minor Wear Detected';
    
    document.getElementById('maintenanceText').textContent = maintenanceStatus;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

