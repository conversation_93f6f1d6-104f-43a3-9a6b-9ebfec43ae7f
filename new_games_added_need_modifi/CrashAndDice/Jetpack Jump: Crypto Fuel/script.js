// Game state
let balance = 1000;
let jetpackGame = {
    isFlying: false,
    altitude: 0,
    fuelLevel: 100,
    betAmount: 0,
    jumpCount: 0,
    maxAltitude: 0,
    cryptoFuel: 0,
    boosterLevel: 1,
    gameMode: 'standard',
    obstacles: [],
    powerups: [],
    crashMultiplier: 1.0,
    autoEject: false,
    ejectAltitude: 100
};

// Game constants
const FUEL_CONSUMPTION_RATE = 2.5;
const ALTITUDE_GAIN_RATE = 8;
const CRASH_PROBABILITY_BASE = 0.15; // 15% base crash chance per second
const CRYPTO_FUEL_TYPES = ['⚡', '🔋', '⛽', '🚀', '💎', '🌟'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Flight Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">🚀 JETPACK JUMP: CRYPTO FUEL 🚀</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 FLIGHT INVESTMENT</label>
                        <input type="number" id="jetpackBet" value="20" min="5" max="${balance}" 
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 FLIGHT MODE</label>
                        <select id="flightMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Flight</option>
                            <option value="turbo">Turbo Boost (2x risk)</option>
                            <option value="extreme">Extreme Mode (3x risk)</option>
                            <option value="cosmic">Cosmic Journey (5x risk)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="flex items-center text-gray-300">
                            <input type="checkbox" id="autoEject" class="mr-2">
                            <span>🪂 Auto-Eject at Altitude:</span>
                        </label>
                        <input type="number" id="ejectAltitude" value="100" min="50" max="500" step="10"
                               class="w-full mt-2 bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startJetpack" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🚀 IGNITE JETPACK 🚀
                    </button>
                    
                    <button id="ejectNow" class="bg-yellow-600/30 hover:bg-yellow-600/50 w-full py-2 rounded font-bold text-white mb-4" disabled>
                        🪂 EMERGENCY EJECT
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🏔️ Current Altitude</div>
                        <div id="currentAltitude" class="text-3xl font-bold text-cyan-400 neon-glow">0m</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⛽ Fuel Level</div>
                        <div id="fuelLevel" class="text-lg font-bold text-orange-400">100%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🚀 Booster Level</div>
                        <div id="boosterLevel" class="text-lg font-bold text-purple-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Crypto Fuel</div>
                        <div id="cryptoFuelCount" class="text-lg font-bold text-blue-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Flight Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h5 class="text-lg font-bold mb-4 text-cyan-400 text-center">🌌 FLIGHT DASHBOARD 🌌</h5>
                    
                    <!-- Flight Visualization -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-t from-blue-900 to-purple-900 h-48 rounded-lg border border-cyan-400 relative overflow-hidden">
                            <div id="jetpackSprite" class="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-2xl transition-all duration-300">
                                🚀
                            </div>
                            <div id="altitudeMarkers" class="absolute inset-0 text-xs text-gray-400">
                                <!-- Altitude markers will be generated here -->
                            </div>
                            <div id="obstacles" class="absolute inset-0">
                                <!-- Obstacles will appear here -->
                            </div>
                            <div id="powerups" class="absolute inset-0">
                                <!-- Powerups will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Flight Stats -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-black/50 p-3 rounded-lg text-center">
                            <div class="text-sm text-gray-400">Max Altitude</div>
                            <div id="maxAltitude" class="text-lg font-bold text-green-400">0m</div>
                        </div>
                        <div class="bg-black/50 p-3 rounded-lg text-center">
                            <div class="text-sm text-gray-400">Jump Count</div>
                            <div id="jumpCount" class="text-lg font-bold text-yellow-400">0</div>
                        </div>
                    </div>
                    
                    <div id="jetpackResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="jetpackStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Flight Log -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">📊 FLIGHT LOG</h5>
                    <div id="flightHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Fuel Efficiency -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">⚡ FUEL EFFICIENCY ⚡</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="efficiencyBar" class="bg-gradient-to-r from-red-500 to-green-500 h-4 rounded-full transition-all duration-300" style="width: 50%"></div>
                    </div>
                    <div id="efficiencyText" class="text-center mt-2 text-orange-300">Standard</div>
                </div>
            </div>
        </div>
    `;
    
    initializeJetpackGame();
}

function initializeJetpackGame() {
    document.getElementById('startJetpack').addEventListener('click', startJetpackFlight);
    document.getElementById('ejectNow').addEventListener('click', ejectJetpack);
    document.getElementById('flightMode').addEventListener('change', updateFlightMode);
    document.getElementById('autoEject').addEventListener('change', updateAutoEject);
    document.getElementById('ejectAltitude').addEventListener('change', updateAutoEject);
    
    updateFlightMode();
    generateAltitudeMarkers();
}

function updateFlightMode() {
    const mode = document.getElementById('flightMode').value;
    jetpackGame.gameMode = mode;
    
    // Update crash multiplier based on mode
    switch(mode) {
        case 'standard': jetpackGame.crashMultiplier = 1.0; break;
        case 'turbo': jetpackGame.crashMultiplier = 1.8; break;
        case 'extreme': jetpackGame.crashMultiplier = 2.5; break;
        case 'cosmic': jetpackGame.crashMultiplier = 4.0; break;
    }
}

function updateAutoEject() {
    jetpackGame.autoEject = document.getElementById('autoEject').checked;
    jetpackGame.ejectAltitude = parseInt(document.getElementById('ejectAltitude').value);
}

function generateAltitudeMarkers() {
    const markers = document.getElementById('altitudeMarkers');
    markers.innerHTML = '';
    
    for (let i = 0; i < 5; i++) {
        const marker = document.createElement('div');
        marker.className = 'absolute right-2 text-xs';
        marker.style.top = `${20 + i * 30}%`;
        marker.textContent = `${(4 - i) * 50}m`;
        markers.appendChild(marker);
    }
}

function startJetpackFlight() {
    const betAmount = parseInt(document.getElementById('jetpackBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient fuel credits!');
        return;
    }
    
    if (jetpackGame.isFlying) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    jetpackGame.betAmount = betAmount;
    jetpackGame.isFlying = true;
    jetpackGame.altitude = 0;
    jetpackGame.fuelLevel = 100;
    jetpackGame.obstacles = [];
    jetpackGame.powerups = [];
    
    // Enable eject button
    document.getElementById('ejectNow').disabled = false;
    document.getElementById('startJetpack').disabled = true;
    
    document.getElementById('jetpackResult').textContent = '';
    document.getElementById('jetpackStatus').textContent = 'Jetpack ignited! Ascending...';
    
    // Start flight simulation
    startFlightSimulation();
}

function startFlightSimulation() {
    const flightInterval = setInterval(() => {
        if (!jetpackGame.isFlying) {
            clearInterval(flightInterval);
            return;
        }
        
        // Consume fuel
        jetpackGame.fuelLevel -= FUEL_CONSUMPTION_RATE;
        
        // Gain altitude (if fuel available)
        if (jetpackGame.fuelLevel > 0) {
            jetpackGame.altitude += ALTITUDE_GAIN_RATE;
            jetpackGame.maxAltitude = Math.max(jetpackGame.maxAltitude, jetpackGame.altitude);
        }
        
        // Check for crash (extremely high probability - 85%+ chance per second)
        const crashChance = calculateCrashProbability();
        if (Math.random() < crashChance) {
            crashJetpack();
            clearInterval(flightInterval);
            return;
        }
        
        // Check for fuel depletion
        if (jetpackGame.fuelLevel <= 0) {
            crashJetpack();
            clearInterval(flightInterval);
            return;
        }
        
        // Check auto-eject
        if (jetpackGame.autoEject && jetpackGame.altitude >= jetpackGame.ejectAltitude) {
            ejectJetpack();
            clearInterval(flightInterval);
            return;
        }
        
        // Generate obstacles and powerups
        generateFlightElements();
        
        // Update display
        updateFlightDisplay();
        
    }, 1000); // Update every second
}

function calculateCrashProbability() {
    // Base crash probability starts high and increases with altitude
    let crashChance = CRASH_PROBABILITY_BASE;
    
    // Increase crash chance with altitude (exponential growth)
    crashChance += (jetpackGame.altitude / 100) * 0.12;
    
    // Mode multipliers make it even worse
    switch(jetpackGame.gameMode) {
        case 'turbo': crashChance *= 1.4; break;
        case 'extreme': crashChance *= 1.8; break;
        case 'cosmic': crashChance *= 2.2; break;
    }
    
    // Fuel level affects stability
    if (jetpackGame.fuelLevel < 30) {
        crashChance *= 1.6; // Low fuel = much higher crash chance
    }
    
    // Cap at 95% to give tiny hope
    return Math.min(0.95, crashChance);
}

function generateFlightElements() {
    // Generate obstacles (frequent)
    if (Math.random() < 0.4) {
        jetpackGame.obstacles.push({
            type: ['🌩️', '🌪️', '☄️', '🛸', '⚡'][Math.floor(Math.random() * 5)],
            altitude: jetpackGame.altitude + 20 + Math.random() * 50
        });
    }
    
    // Generate powerups (very rare)
    if (Math.random() < 0.05) {
        jetpackGame.powerups.push({
            type: CRYPTO_FUEL_TYPES[Math.floor(Math.random() * CRYPTO_FUEL_TYPES.length)],
            altitude: jetpackGame.altitude + 30 + Math.random() * 40
        });
    }
    
    // Remove passed elements
    jetpackGame.obstacles = jetpackGame.obstacles.filter(obs => obs.altitude > jetpackGame.altitude - 10);
    jetpackGame.powerups = jetpackGame.powerups.filter(pow => pow.altitude > jetpackGame.altitude - 10);
}

function crashJetpack() {
    jetpackGame.isFlying = false;
    jetpackGame.jumpCount++;
    
    document.getElementById('jetpackResult').innerHTML = 
        `<span class="text-red-400">💥 JETPACK MALFUNCTION! 💥</span>`;
    
    let crashReason = 'Engine failure';
    if (jetpackGame.fuelLevel <= 0) crashReason = 'Fuel depletion';
    else if (jetpackGame.altitude > 200) crashReason = 'Altitude sickness';
    else if (jetpackGame.obstacles.length > 3) crashReason = 'Obstacle collision';
    
    document.getElementById('jetpackStatus').textContent = 
        `Crashed at ${Math.floor(jetpackGame.altitude)}m due to ${crashReason}!`;
    
    addFlightLog(false);
    resetFlightControls();
}

function ejectJetpack() {
    if (!jetpackGame.isFlying) return;
    
    jetpackGame.isFlying = false;
    jetpackGame.jumpCount++;
    
    // Calculate winnings based on altitude (very low multipliers)
    const altitudeMultiplier = 1 + (jetpackGame.altitude / 1000); // 0.1% per 100m
    const modeBonus = jetpackGame.crashMultiplier;
    const fuelBonus = jetpackGame.fuelLevel > 50 ? 1.1 : 1.0;
    const cryptoBonus = 1 + (jetpackGame.cryptoFuel * 0.02);
    
    const winnings = Math.floor(jetpackGame.betAmount * altitudeMultiplier * modeBonus * fuelBonus * cryptoBonus);
    balance += winnings;
    updateBalance();
    
    document.getElementById('jetpackResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🪂 SUCCESSFUL LANDING! 🪂</span>`;
    document.getElementById('jetpackStatus').innerHTML = 
        `Ejected at ${Math.floor(jetpackGame.altitude)}m! Earned ${winnings} GA!`;
    
    // Increase booster level slightly
    if (jetpackGame.altitude > jetpackGame.maxAltitude * 0.8) {
        jetpackGame.boosterLevel++;
        jetpackGame.cryptoFuel += Math.floor(jetpackGame.altitude / 100);
    }
    
    addFlightLog(true);
    resetFlightControls();
}

function updateFlightDisplay() {
    document.getElementById('currentAltitude').textContent = Math.floor(jetpackGame.altitude) + 'm';
    document.getElementById('fuelLevel').textContent = Math.floor(jetpackGame.fuelLevel) + '%';
    document.getElementById('maxAltitude').textContent = Math.floor(jetpackGame.maxAltitude) + 'm';
    document.getElementById('jumpCount').textContent = jetpackGame.jumpCount;
    document.getElementById('boosterLevel').textContent = jetpackGame.boosterLevel;
    document.getElementById('cryptoFuelCount').textContent = jetpackGame.cryptoFuel;
    
    // Update jetpack sprite position
    const sprite = document.getElementById('jetpackSprite');
    const maxDisplayAltitude = 200;
    const position = Math.min(90, (jetpackGame.altitude / maxDisplayAltitude) * 90);
    sprite.style.bottom = position + '%';
    
    // Update fuel efficiency
    const efficiency = Math.max(0, 100 - (jetpackGame.altitude / 10));
    document.getElementById('efficiencyBar').style.width = efficiency + '%';
    
    let efficiencyLevel = 'Excellent';
    if (efficiency < 30) efficiencyLevel = 'Critical';
    else if (efficiency < 50) efficiencyLevel = 'Poor';
    else if (efficiency < 70) efficiencyLevel = 'Fair';
    document.getElementById('efficiencyText').textContent = efficiencyLevel;
    
    // Display obstacles and powerups
    displayFlightElements();
}

function displayFlightElements() {
    const obstaclesEl = document.getElementById('obstacles');
    const powerupsEl = document.getElementById('powerups');
    
    obstaclesEl.innerHTML = '';
    powerupsEl.innerHTML = '';
    
    jetpackGame.obstacles.forEach(obs => {
        const el = document.createElement('div');
        el.className = 'absolute text-lg';
        el.textContent = obs.type;
        el.style.left = Math.random() * 80 + '%';
        el.style.top = Math.random() * 80 + '%';
        obstaclesEl.appendChild(el);
    });
    
    jetpackGame.powerups.forEach(pow => {
        const el = document.createElement('div');
        el.className = 'absolute text-lg animate-pulse';
        el.textContent = pow.type;
        el.style.left = Math.random() * 80 + '%';
        el.style.top = Math.random() * 80 + '%';
        powerupsEl.appendChild(el);
    });
}

function addFlightLog(successful) {
    const history = document.getElementById('flightHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-xs font-bold ${
        successful ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = `${Math.floor(jetpackGame.altitude)}m`;
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 12) {
        history.removeChild(history.lastChild);
    }
}

function resetFlightControls() {
    setTimeout(() => {
        document.getElementById('ejectNow').disabled = true;
        document.getElementById('startJetpack').disabled = false;
        
        // Reset sprite position
        document.getElementById('jetpackSprite').style.bottom = '2%';
        
        // Clear elements
        document.getElementById('obstacles').innerHTML = '';
        document.getElementById('powerups').innerHTML = '';
        
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

