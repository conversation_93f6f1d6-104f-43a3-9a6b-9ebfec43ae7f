// Game state
const walletIntegration = new GameWalletIntegration('PredictionDiceOracle');
let oracleDiceGame = {
    isPlaying: false,
    betAmount: 0,
    prediction: 'under',
    targetNumber: 50,
    oracleLevel: 1,
    mysticPower: 0,
    ancientWisdom: 0,
    divineIntervention: false,
    cosmicAlignment: 'neutral',
    prophecyMode: 'standard',
    consecutiveWins: 0,
    totalRolls: 0,
    oracleStreak: 0
};

// Oracle prophecy modes that affect difficulty
const PROPHECY_MODES = [
    { name: 'standard', bias: 0.92, description: 'Standard Oracle Reading' },
    { name: 'mystic', bias: 0.94, description: 'Mystic Vision (Harder)' },
    { name: 'divine', bias: 0.96, description: 'Divine Prophecy (Extreme)' },
    { name: 'cosmic', bias: 0.98, description: 'Cosmic Oracle (Nearly Impossible)' },
    { name: 'eternal', bias: 0.99, description: 'Eternal Wisdom (Impossible)' }
];

// Cosmic alignments that affect outcomes
const COSMIC_ALIGNMENTS = [
    { name: 'neutral', modifier: 1.0, description: 'Neutral Cosmos' },
    { name: 'chaotic', modifier: 1.2, description: 'Chaotic Energy' },
    { name: 'cursed', modifier: 1.5, description: 'Cursed Alignment' },
    { name: 'doomed', modifier: 2.0, description: 'Doomed Fate' }
];



function loadPredictionDiceOracleGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Oracle Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🔮 PREDICTION DICE: ORACLE 🔮</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 ORACLE OFFERING</label>
                        <input type="number" id="oracleBet" value="45" min="10"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 ORACLE PREDICTION</label>
                        <select id="oraclePrediction" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="under">🔻 Roll Under (Descending Fate)</option>
                            <option value="over">🔺 Roll Over (Ascending Destiny)</option>
                            <option value="exact">🎯 Exact Number (Divine Precision)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🔢 MYSTICAL TARGET</label>
                        <input type="range" id="oracleTarget" min="1" max="99" value="50" 
                               class="w-full accent-purple-500">
                        <div class="flex justify-between text-sm text-gray-400">
                            <span>1</span>
                            <span id="oracleTargetValue" class="text-purple-400 font-bold">50</span>
                            <span>99</span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌟 PROPHECY MODE</label>
                        <select id="prophecyMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Oracle Reading</option>
                            <option value="mystic">Mystic Vision (Harder)</option>
                            <option value="divine">Divine Prophecy (Extreme)</option>
                            <option value="cosmic">Cosmic Oracle (Nearly Impossible)</option>
                            <option value="eternal">Eternal Wisdom (Impossible)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌌 COSMIC ALIGNMENT</label>
                        <select id="cosmicAlignment" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="neutral">Neutral Cosmos</option>
                            <option value="chaotic">Chaotic Energy (1.2x harder)</option>
                            <option value="cursed">Cursed Alignment (1.5x harder)</option>
                            <option value="doomed">Doomed Fate (2x harder)</option>
                        </select>
                    </div>
                    
                    <button id="consultOracle" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🔮 CONSULT THE ORACLE 🔮
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Oracle's Wisdom</div>
                        <div id="oracleWinChance" class="text-lg font-bold text-red-400">8%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Mystical Multiplier</div>
                        <div id="oracleMultiplier" class="text-2xl font-bold text-purple-400 neon-glow">12.50x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🌟 Oracle Level</div>
                        <div id="oracleLevelDisplay" class="text-lg font-bold text-blue-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⚡ Mystic Power</div>
                        <div id="mysticPowerDisplay" class="text-lg font-bold text-yellow-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Oracle Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400 text-center">🔮 ORACLE'S CHAMBER 🔮</h5>
                    
                    <!-- Oracle Dice Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-purple-900/50 to-black/50 p-8 rounded-xl border border-purple-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🎲 Sacred Dice Roll</div>
                                <div id="oracleDiceDisplay" class="text-8xl font-bold text-purple-400 neon-glow mb-4">?</div>
                                <div class="text-sm text-gray-400">Target: <span id="oracleTargetDisplay" class="text-purple-400">50</span></div>
                                <div class="text-sm text-gray-400">Prediction: <span id="oraclePredictionDisplay" class="text-blue-400">Under</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Oracle Wisdom -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🌟 Ancient Wisdom</div>
                            <div id="ancientWisdom" class="text-lg font-bold text-purple-400">Awaiting consultation...</div>
                            <div class="text-xs text-gray-400">Cosmic State: <span id="cosmicState" class="text-blue-400">Neutral</span></div>
                            <div class="text-xs text-gray-400">Divine Favor: <span id="divineFavor" class="text-red-400">Absent</span></div>
                        </div>
                    </div>
                    
                    <div id="oracleResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="oracleStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Oracle Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 ORACLE RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Consecutive Wins</div>
                            <div id="consecutiveWins" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Oracle Streak</div>
                            <div id="oracleStreakDisplay" class="text-lg font-bold text-orange-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Consultations</div>
                            <div id="totalRolls" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Success Rate</div>
                            <div id="successRate" class="text-lg font-bold text-red-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Mystical Progress -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">🌌 MYSTICAL PROGRESS 🌌</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="mysticalProgress" class="bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="mysticalText" class="text-center mt-2 text-blue-300">Novice Oracle</div>
                </div>
            </div>
        </div>
    `;
    
    initializeOracleDice();
}

function initializeOracleDice() {
    document.getElementById('consultOracle').addEventListener('click', consultOracle);
    document.getElementById('oracleTarget').addEventListener('input', updateOracleCalculations);
    document.getElementById('oraclePrediction').addEventListener('change', updateOracleCalculations);
    document.getElementById('prophecyMode').addEventListener('change', updateProphecyMode);
    document.getElementById('cosmicAlignment').addEventListener('change', updateCosmicAlignment);
    
    updateOracleCalculations();
    updateProphecyMode();
    updateCosmicAlignment();
}

function updateOracleCalculations() {
    const target = parseInt(document.getElementById('oracleTarget').value);
    const prediction = document.getElementById('oraclePrediction').value;
    
    document.getElementById('oracleTargetValue').textContent = target;
    document.getElementById('oracleTargetDisplay').textContent = target;
    document.getElementById('oraclePredictionDisplay').textContent = 
        prediction === 'under' ? 'Under' : prediction === 'over' ? 'Over' : 'Exact';
    
    oracleDiceGame.targetNumber = target;
    oracleDiceGame.prediction = prediction;
    
    // Calculate extremely low win chances
    let baseWinChance;
    if (prediction === 'under') {
        baseWinChance = Math.max(1, target - 1);
    } else if (prediction === 'over') {
        baseWinChance = Math.max(1, 100 - target);
    } else { // exact
        baseWinChance = 1; // Only 1% chance for exact
    }
    
    // Apply prophecy mode bias (makes it much harder)
    const prophecyData = PROPHECY_MODES.find(p => p.name === oracleDiceGame.prophecyMode);
    const cosmicData = COSMIC_ALIGNMENTS.find(c => c.name === oracleDiceGame.cosmicAlignment);
    
    // Reduce win chance dramatically
    const finalWinChance = Math.max(1, Math.floor(baseWinChance * (1 - prophecyData.bias) / cosmicData.modifier));
    
    // Calculate multiplier (higher for lower chances)
    const multiplier = Math.max(1.5, 95 / finalWinChance);
    
    document.getElementById('oracleWinChance').textContent = finalWinChance + '%';
    document.getElementById('oracleMultiplier').textContent = multiplier.toFixed(2) + 'x';
}

function updateProphecyMode() {
    const mode = document.getElementById('prophecyMode').value;
    oracleDiceGame.prophecyMode = mode;
    
    const prophecyData = PROPHECY_MODES.find(p => p.name === mode);
    document.getElementById('ancientWisdom').textContent = prophecyData.description;
    
    updateOracleCalculations();
}

function updateCosmicAlignment() {
    const alignment = document.getElementById('cosmicAlignment').value;
    oracleDiceGame.cosmicAlignment = alignment;
    
    const cosmicData = COSMIC_ALIGNMENTS.find(c => c.name === alignment);
    document.getElementById('cosmicState').textContent = cosmicData.description;
    
    // Update divine favor based on alignment
    const favorLevel = alignment === 'neutral' ? 'Neutral' : 
                      alignment === 'chaotic' ? 'Unstable' :
                      alignment === 'cursed' ? 'Hostile' : 'Malevolent';
    document.getElementById('divineFavor').textContent = favorLevel;
    
    updateOracleCalculations();
}

async function consultOracle() {
    const betAmount = parseInt(document.getElementById('oracleBet').value);

    if (oracleDiceGame.isPlaying) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    oracleDiceGame.betAmount = betAmount;
    oracleDiceGame.isPlaying = true;
    oracleDiceGame.totalRolls++;
    
    // Disable button during roll
    document.getElementById('consultOracle').disabled = true;
    
    // Animate dice roll
    let rollCount = 0;
    const rollAnimation = setInterval(() => {
        const randomRoll = Math.floor(Math.random() * 100) + 1;
        document.getElementById('oracleDiceDisplay').textContent = randomRoll;
        rollCount++;
        
        if (rollCount >= 20) {
            clearInterval(rollAnimation);
            performOracleReading();
        }
    }, 100);
}

async function performOracleReading() {
    const target = oracleDiceGame.targetNumber;
    const prediction = oracleDiceGame.prediction;
    const prophecyData = PROPHECY_MODES.find(p => p.name === oracleDiceGame.prophecyMode);
    const cosmicData = COSMIC_ALIGNMENTS.find(c => c.name === oracleDiceGame.cosmicAlignment);
    
    // Generate extremely biased roll result
    let roll;
    const biasChance = prophecyData.bias * cosmicData.modifier;
    
    if (Math.random() < biasChance) {
        // Generate losing roll (92-99% of the time)
        if (prediction === 'under') {
            // Generate roll >= target when player predicts under
            roll = Math.floor(Math.random() * (100 - target + 1)) + target;
        } else if (prediction === 'over') {
            // Generate roll <= target when player predicts over
            roll = Math.floor(Math.random() * target) + 1;
        } else { // exact
            // Generate any number except target
            do {
                roll = Math.floor(Math.random() * 100) + 1;
            } while (roll === target);
        }
    } else {
        // Generate winning roll (1-8% of the time)
        if (prediction === 'under') {
            roll = Math.floor(Math.random() * (target - 1)) + 1;
        } else if (prediction === 'over') {
            roll = Math.floor(Math.random() * (100 - target)) + target + 1;
        } else { // exact
            roll = target;
        }
    }
    
    // Display final roll
    document.getElementById('oracleDiceDisplay').textContent = roll;
    
    // Check win condition
    let won = false;
    if (prediction === 'under' && roll < target) won = true;
    if (prediction === 'over' && roll > target) won = true;
    if (prediction === 'exact' && roll === target) won = true;
    
    // Process result
    if (won) {
        oracleDiceGame.consecutiveWins++;
        oracleDiceGame.oracleStreak++;
        oracleDiceGame.oracleLevel++;
        oracleDiceGame.mysticPower += 25;
        oracleDiceGame.ancientWisdom += 10;
        
        // Calculate winnings
        let baseWinChance;
        if (prediction === 'under') {
            baseWinChance = Math.max(1, target - 1);
        } else if (prediction === 'over') {
            baseWinChance = Math.max(1, 100 - target);
        } else {
            baseWinChance = 1;
        }
        
        const finalWinChance = Math.max(1, Math.floor(baseWinChance * (1 - prophecyData.bias) / cosmicData.modifier));
        const multiplier = Math.max(1.5, 95 / finalWinChance);
        const winnings = Math.floor(oracleDiceGame.betAmount * multiplier);
        const netWinnings = winnings - oracleDiceGame.betAmount;

        if (netWinnings > 0) {
            await walletIntegration.processWin(netWinnings);
        }
        
        document.getElementById('oracleResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🌟 ORACLE'S BLESSING! 🌟</span>`;
        document.getElementById('oracleStatus').innerHTML = 
            `The Oracle smiles upon you! Won ${winnings} GA with ${multiplier.toFixed(2)}x multiplier!`;
        
        document.getElementById('ancientWisdom').textContent = 'The cosmos align in your favor...';
        document.getElementById('divineFavor').textContent = 'Blessed';
        
    } else {
        oracleDiceGame.consecutiveWins = 0;
        
        document.getElementById('oracleResult').innerHTML = 
            `<span class="text-red-400">💀 ORACLE'S CURSE! 💀</span>`;
        document.getElementById('oracleStatus').innerHTML = 
            `The Oracle's vision was clouded. Lost ${oracleDiceGame.betAmount} GA.`;
        
        document.getElementById('ancientWisdom').textContent = 'The fates conspire against you...';
        document.getElementById('divineFavor').textContent = 'Forsaken';
    }
    
    updateOracleDisplay();
    
    // Re-enable button
    setTimeout(() => {
        document.getElementById('consultOracle').disabled = false;
        oracleDiceGame.isPlaying = false;
    }, 3000);
}

function updateOracleDisplay() {
    document.getElementById('oracleLevelDisplay').textContent = oracleDiceGame.oracleLevel;
    document.getElementById('mysticPowerDisplay').textContent = oracleDiceGame.mysticPower;
    document.getElementById('consecutiveWins').textContent = oracleDiceGame.consecutiveWins;
    document.getElementById('oracleStreakDisplay').textContent = oracleDiceGame.oracleStreak;
    document.getElementById('totalRolls').textContent = oracleDiceGame.totalRolls;
    
    // Calculate success rate
    const successRate = oracleDiceGame.totalRolls > 0 ? 
        Math.floor((oracleDiceGame.oracleStreak / oracleDiceGame.totalRolls) * 100) : 0;
    document.getElementById('successRate').textContent = successRate + '%';
    
    // Update mystical progress
    const progress = Math.min(100, (oracleDiceGame.mysticPower / 500) * 100);
    document.getElementById('mysticalProgress').style.width = progress + '%';
    
    let mysticalLevel = 'Novice Oracle';
    if (oracleDiceGame.mysticPower > 400) mysticalLevel = 'Grand Oracle';
    else if (oracleDiceGame.mysticPower > 300) mysticalLevel = 'Master Oracle';
    else if (oracleDiceGame.mysticPower > 200) mysticalLevel = 'Wise Oracle';
    else if (oracleDiceGame.mysticPower > 100) mysticalLevel = 'Apprentice Oracle';
    
    document.getElementById('mysticalText').textContent = mysticalLevel;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadPredictionDiceOracleGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

