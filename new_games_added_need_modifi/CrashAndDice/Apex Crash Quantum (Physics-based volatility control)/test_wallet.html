<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Orbitron', sans-serif;
            background-color: #0a0a1a;
            color: #e0e0e0;
        }
        .neon-glow { text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #0ff, 0 0 20px #0ff; }
    </style>
</head>
<body class="p-8">
    <h1 class="text-3xl font-bold text-cyan-400 neon-glow mb-4">Wallet Integration Test</h1>
    <div id="balanceDisplay" class="text-2xl mb-4">Balance: -- GA</div>
    <div class="space-x-2">
        <button id="testBet" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded">Bet 10 GA</button>
        <button id="testWin" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">Win 20 GA</button>
        <button id="resetBalance" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">Reset Balance</button>
    </div>

    <script src="script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            updateBalanceDisplay();

            document.getElementById('testBet').addEventListener('click', () => {
                const currentBalance = getWalletBalance();
                if (currentBalance >= 10) {
                    updateWalletBalance(currentBalance - 10);
                } else {
                    alert('Insufficient balance for test bet!');
                }
            });

            document.getElementById('testWin').addEventListener('click', () => {
                const currentBalance = getWalletBalance();
                updateWalletBalance(currentBalance + 20);
            });

            document.getElementById('resetBalance').addEventListener('click', () => {
                localStorage.removeItem('userBalance_GA');
                updateBalanceDisplay();
                alert('Balance has been reset to default.');
            });
        });
    </script>
</body>
</html>
