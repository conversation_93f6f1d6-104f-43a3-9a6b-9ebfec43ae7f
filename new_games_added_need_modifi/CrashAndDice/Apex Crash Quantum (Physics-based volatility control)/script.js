// Game state
let walletIntegration;

let apexCrashGame = {
    isPlaying: false,
    multiplier: 1.00,
    betAmount: 0,
    startTime: 0,
    crashPoint: 0,
    autoCashout: 0,
    volatilityLevel: 1, // 1-5 scale
    quantumField: 0, // 0-100 quantum energy
    physicsAcceleration: 1.0
};



function loadApexCrashGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Game Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">APEX CRASH QUANTUM</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="apexBet" value="10" min="1" 
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">AUTO CASHOUT</label>
                        <input type="number" id="apexAutoCashout" value="2.00" min="1.01" step="0.01" 
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VOLATILITY CONTROL</label>
                        <input type="range" id="volatilitySlider" min="1" max="5" value="1" 
                               class="w-full accent-cyan-500">
                        <div class="flex justify-between text-sm text-gray-400">
                            <span>Stable</span>
                            <span id="volatilityValue">1</span>
                            <span>Chaotic</span>
                        </div>
                    </div>
                    
                    <button id="playApex" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        LAUNCH QUANTUM ROCKET
                    </button>
                    
                    <button id="cashoutApex" class="cyber-button-secondary w-full py-3 rounded-lg font-bold text-white" disabled>
                        CASH OUT
                    </button>
                    
                    <div class="mt-4 text-center">
                        <div class="text-sm text-gray-400 mb-1">Current Multiplier</div>
                        <div id="apexMultiplier" class="text-3xl font-bold text-cyan-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Quantum Field</div>
                        <div id="quantumField" class="text-lg font-bold text-purple-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Quantum Visualization -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div id="quantumCanvas" class="relative bg-gradient-to-br from-blue-900 to-purple-900 rounded-lg h-96 overflow-hidden">
                        <div id="quantumRocket" class="absolute bottom-4 left-4 w-8 h-8 bg-cyan-400 rounded-full transition-all duration-100"></div>
                        <div id="quantumParticles" class="absolute inset-0"></div>
                        <div id="crashEffect" class="absolute inset-0 bg-red-500/50 opacity-0 transition-opacity duration-500"></div>
                    </div>
                    <div id="apexStatus" class="text-center mt-4 text-lg font-semibold">Ready for quantum launch</div>
                </div>
                
                <!-- Crash History -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">CRASH HISTORY</h5>
                    <div id="apexHistory" class="flex flex-wrap gap-2"></div>
                </div>
            </div>
        </div>
    `;
    
    initializeApexCrash();
}

function initializeApexCrash() {
    document.getElementById('playApex').addEventListener('click', startApexCrash);
    document.getElementById('cashoutApex').addEventListener('click', cashoutApex);
    document.getElementById('volatilitySlider').addEventListener('input', updateVolatility);
    
    updateVolatility();
}

function updateVolatility() {
    const volatility = document.getElementById('volatilitySlider').value;
    document.getElementById('volatilityValue').textContent = volatility;
    apexCrashGame.volatilityLevel = parseInt(volatility);
}

function generateCrashPoint() {
    // Physics-based crash point with volatility control
    const baseRandom = Math.random();
    const volatilityFactor = apexCrashGame.volatilityLevel / 5;
    
    // Higher volatility = more extreme outcomes
    let adjustedRandom;
    if (volatilityFactor > 0.6) {
        // High volatility: more crashes below 2x, but occasional high multipliers
        adjustedRandom = Math.pow(baseRandom, 2 - volatilityFactor);
    } else {
        // Low volatility: more predictable crashes
        adjustedRandom = Math.pow(baseRandom, 1.5);
    }
    
    // Ensure house edge (win rate < 20%)
    const houseEdge = 0.85; // 85% house edge for low win rate
    const crashPoint = 1 / (1 - adjustedRandom * houseEdge);
    
    return Math.max(1.01, Math.min(crashPoint, 50.0));
}

async function startApexCrash() {
    const betAmount = parseInt(document.getElementById('apexBet').value);
    const autoCashout = parseFloat(document.getElementById('apexAutoCashout').value);

    const betProcessed = await walletIntegration.processBet(betAmount, { 
        action: 'start_crash',
        autoCashout: autoCashout,
        volatility: apexCrashGame.volatilityLevel
    });

    if (!betProcessed) {
        return; // Stop if bet fails
    }
    
    // Initialize game state
    apexCrashGame.isPlaying = true;
    apexCrashGame.multiplier = 1.00;
    apexCrashGame.betAmount = betAmount;
    apexCrashGame.startTime = Date.now();
    apexCrashGame.autoCashout = autoCashout;
    apexCrashGame.crashPoint = generateCrashPoint();
    apexCrashGame.quantumField = 0;
    apexCrashGame.physicsAcceleration = 1.0;
    
    // Update UI
    document.getElementById('playApex').disabled = true;
    document.getElementById('cashoutApex').disabled = false;
    document.getElementById('apexStatus').textContent = 'Quantum rocket launching...';
    
    animateApexCrash();
}

function animateApexCrash() {
    if (!apexCrashGame.isPlaying) return;
    
    const elapsed = (Date.now() - apexCrashGame.startTime) / 1000;
    
    // Physics-based multiplier calculation
    apexCrashGame.physicsAcceleration += apexCrashGame.volatilityLevel * 0.1;
    apexCrashGame.multiplier = 1 + (elapsed * apexCrashGame.physicsAcceleration * 0.5);
    
    // Quantum field increases with time
    apexCrashGame.quantumField = Math.min(100, elapsed * 10);
    
    // Update displays
    document.getElementById('apexMultiplier').textContent = apexCrashGame.multiplier.toFixed(2) + 'x';
    document.getElementById('quantumField').textContent = Math.floor(apexCrashGame.quantumField) + '%';
    
    // Update rocket position
    const rocket = document.getElementById('quantumRocket');
    const progress = Math.min(90, elapsed * 15);
    rocket.style.bottom = progress + '%';
    rocket.style.left = (4 + Math.sin(elapsed * 2) * 10) + '%';
    
    // Generate quantum particles
    generateQuantumParticles();
    
    // Check for crash
    if (apexCrashGame.multiplier >= apexCrashGame.crashPoint) {
        crashApex();
        return;
    }
    
    // Check auto cashout
    if (apexCrashGame.multiplier >= apexCrashGame.autoCashout) {
        cashoutApex();
        return;
    }
    
    setTimeout(animateApexCrash, 100);
}

function generateQuantumParticles() {
    const container = document.getElementById('quantumParticles');
    
    // Add new particle
    const particle = document.createElement('div');
    particle.className = 'absolute w-1 h-1 bg-cyan-400 rounded-full';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.opacity = '0.8';
    
    container.appendChild(particle);
    
    // Remove particle after animation
    setTimeout(() => {
        if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
        }
    }, 2000);
    
    // Limit particles
    while (container.children.length > 20) {
        container.removeChild(container.firstChild);
    }
}

async function cashoutApex() {
    if (!apexCrashGame.isPlaying) return;
    
    const winnings = Math.floor(apexCrashGame.betAmount * apexCrashGame.multiplier);
    await walletIntegration.processWin(winnings, { 
        action: 'cashout',
        multiplier: apexCrashGame.multiplier.toFixed(2)
    });
    
    apexCrashGame.isPlaying = false;
    
    document.getElementById('apexStatus').innerHTML = 
        `<span class="text-green-400 neon-glow">Cashed out at ${apexCrashGame.multiplier.toFixed(2)}x! Won ${winnings} GA</span>`;
    
    addApexHistory(apexCrashGame.multiplier, true);
    resetApexGame();
}

function crashApex() {
    apexCrashGame.isPlaying = false;
    
    // Show crash effect
    document.getElementById('crashEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('crashEffect').style.opacity = '0';
    }, 1000);
    
    document.getElementById('apexStatus').innerHTML = 
        `<span class="text-red-400">Crashed at ${apexCrashGame.crashPoint.toFixed(2)}x! Better luck next time!</span>`;
    
    addApexHistory(apexCrashGame.crashPoint, false);
    resetApexGame();
}

function addApexHistory(multiplier, won) {
    const history = document.getElementById('apexHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-sm font-bold ${
        won ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = multiplier.toFixed(2) + 'x';
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

function resetApexGame() {
    setTimeout(() => {
        document.getElementById('playApex').disabled = false;
        document.getElementById('cashoutApex').disabled = true;
        document.getElementById('apexStatus').textContent = 'Ready for quantum launch';
        document.getElementById('apexMultiplier').textContent = '1.00x';
        document.getElementById('quantumField').textContent = '0%';
        
        // Reset rocket position
        const rocket = document.getElementById('quantumRocket');
        rocket.style.bottom = '4%';
        rocket.style.left = '4%';
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async function() {
    walletIntegration = new GameWalletIntegration('ApexCrashQuantum');
    await walletIntegration.initialize();

    loadApexCrashGame();
});

