// Game state
let balance = 1000;
let dynastyDiceGame = {
    isRolling: false,
    emperorDice: [],
    playerDice: [],
    betAmount: 0,
    dynastyLevel: 1,
    dragonPower: 0,
    imperialFavor: 0,
    mandateOfHeaven: false,
    rollType: 'standard'
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Imperial Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">DYNASTY DICE: EMPEROR'S ROLL</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">IMPERIAL WAGER</label>
                        <input type="number" id="dynastyBet" value="10" min="1" max="${balance}" 
                               class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ROLL TYPE</label>
                        <select id="rollType" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Roll (5 dice)</option>
                            <option value="imperial">Imperial Roll (7 dice)</option>
                            <option value="celestial">Celestial Roll (9 dice)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CHALLENGE THE EMPEROR</label>
                        <select id="challengeType" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="total">Highest Total Wins</option>
                            <option value="pairs">Most Pairs Wins</option>
                            <option value="sequence">Longest Sequence Wins</option>
                        </select>
                    </div>
                    
                    <button id="rollDynastyDice" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        CHALLENGE THE EMPEROR
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">Dynasty Level</div>
                        <div id="dynastyLevel" class="text-2xl font-bold text-yellow-400 neon-glow">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Dragon Power</div>
                        <div id="dragonPower" class="text-lg font-bold text-red-400">0%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Imperial Favor</div>
                        <div id="imperialFavor" class="text-lg font-bold text-purple-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Imperial Court -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h5 class="text-lg font-bold mb-4 text-yellow-400 text-center">IMPERIAL COURT</h5>
                    
                    <!-- Emperor's Dice -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 flex items-center">
                            <span>👑 EMPEROR'S DICE</span>
                        </div>
                        <div id="emperorDice" class="flex justify-center flex-wrap gap-2 mb-2">
                            <!-- Dice will be generated here -->
                        </div>
                        <div id="emperorScore" class="text-center text-lg font-bold text-yellow-400">Score: 0</div>
                    </div>
                    
                    <!-- Challenge Indicator -->
                    <div class="text-center text-2xl font-bold text-red-400 mb-6">⚔️ CHALLENGE ⚔️</div>
                    
                    <!-- Player's Dice -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 flex items-center">
                            <span>🎲 YOUR DICE</span>
                        </div>
                        <div id="playerDice" class="flex justify-center flex-wrap gap-2 mb-2">
                            <!-- Dice will be generated here -->
                        </div>
                        <div id="playerScore" class="text-center text-lg font-bold text-green-400">Score: 0</div>
                    </div>
                    
                    <div id="dynastyResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="dynastyStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Imperial History -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">IMPERIAL CHRONICLES</h5>
                    <div id="dynastyHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Mandate of Heaven -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">MANDATE OF HEAVEN</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="mandateBar" class="bg-purple-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="mandateText" class="text-center mt-2 text-purple-300">Dormant</div>
                </div>
            </div>
        </div>
    `;
    
    initializeDynastyDice();
}

function initializeDynastyDice() {
    document.getElementById('rollDynastyDice').addEventListener('click', rollDynastyDice);
    document.getElementById('rollType').addEventListener('change', updateRollType);
    document.getElementById('challengeType').addEventListener('change', updateChallengeType);
    
    updateRollType();
}

function updateRollType() {
    const type = document.getElementById('rollType').value;
    dynastyDiceGame.rollType = type;
}

function updateChallengeType() {
    // Update challenge type for scoring
}

function rollDynastyDice() {
    const betAmount = parseInt(document.getElementById('dynastyBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient imperial treasury!');
        return;
    }
    
    if (dynastyDiceGame.isRolling) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    dynastyDiceGame.betAmount = betAmount;
    dynastyDiceGame.isRolling = true;
    
    // Disable roll button
    document.getElementById('rollDynastyDice').disabled = true;
    document.getElementById('dynastyResult').textContent = '';
    document.getElementById('dynastyStatus').textContent = 'Rolling the imperial dice...';
    
    // Generate dice based on roll type
    const diceCount = getDynastyDiceCount();
    generateDynastyDiceDisplay(diceCount);
    
    // Animate dice rolling
    animateDynastyDiceRoll(diceCount);
}

function getDynastyDiceCount() {
    switch(dynastyDiceGame.rollType) {
        case 'standard': return 5;
        case 'imperial': return 7;
        case 'celestial': return 9;
        default: return 5;
    }
}

function generateDynastyDiceDisplay(count) {
    const emperorDiceEl = document.getElementById('emperorDice');
    const playerDiceEl = document.getElementById('playerDice');
    
    emperorDiceEl.innerHTML = '';
    playerDiceEl.innerHTML = '';
    
    for (let i = 0; i < count; i++) {
        // Emperor dice
        const emperorDie = document.createElement('div');
        emperorDie.className = 'w-10 h-10 bg-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-sm border-2 border-yellow-400';
        emperorDie.textContent = '?';
        emperorDiceEl.appendChild(emperorDie);
        
        // Player dice
        const playerDie = document.createElement('div');
        playerDie.className = 'w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center text-white font-bold text-sm border-2 border-green-400';
        playerDie.textContent = '?';
        playerDiceEl.appendChild(playerDie);
    }
}

function animateDynastyDiceRoll(diceCount) {
    const emperorDiceEls = document.querySelectorAll('#emperorDice > div');
    const playerDiceEls = document.querySelectorAll('#playerDice > div');
    
    let rollCount = 0;
    const rollInterval = setInterval(() => {
        // Animate emperor dice
        emperorDiceEls.forEach(die => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        });
        
        // Animate player dice
        playerDiceEls.forEach(die => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        });
        
        rollCount++;
        
        if (rollCount >= 25) {
            clearInterval(rollInterval);
            finalizeDynastyDiceRoll(diceCount);
        }
    }, 120);
}

function finalizeDynastyDiceRoll(diceCount) {
    // Generate final dice values with extreme bias toward Emperor (< 10% win rate)
    dynastyDiceGame.emperorDice = [];
    dynastyDiceGame.playerDice = [];
    
    // Emperor dice - heavily biased toward high values
    for (let i = 0; i < diceCount; i++) {
        // 88% chance of rolling 4-6, 12% chance of rolling 1-3
        const roll = Math.random() < 0.88 ? 
            Math.floor(Math.random() * 3) + 4 : 
            Math.floor(Math.random() * 3) + 1;
        dynastyDiceGame.emperorDice.push(roll);
    }
    
    // Player dice - heavily biased toward low values
    for (let i = 0; i < diceCount; i++) {
        // 92% chance of rolling 1-3, 8% chance of rolling 4-6
        const roll = Math.random() < 0.92 ? 
            Math.floor(Math.random() * 3) + 1 : 
            Math.floor(Math.random() * 3) + 4;
        dynastyDiceGame.playerDice.push(roll);
    }
    
    // Apply dragon power and imperial favor
    applyDynastyEffects();
    
    // Display final dice
    const emperorDiceEls = document.querySelectorAll('#emperorDice > div');
    const playerDiceEls = document.querySelectorAll('#playerDice > div');
    
    emperorDiceEls.forEach((die, index) => {
        die.textContent = dynastyDiceGame.emperorDice[index];
    });
    
    playerDiceEls.forEach((die, index) => {
        die.textContent = dynastyDiceGame.playerDice[index];
    });
    
    // Calculate scores based on challenge type
    const challengeType = document.getElementById('challengeType').value;
    const emperorScore = calculateDynastyScore(dynastyDiceGame.emperorDice, challengeType);
    const playerScore = calculateDynastyScore(dynastyDiceGame.playerDice, challengeType);
    
    document.getElementById('emperorScore').textContent = `Score: ${emperorScore}`;
    document.getElementById('playerScore').textContent = `Score: ${playerScore}`;
    
    // Resolve challenge
    resolveDynastyChallenge(emperorScore, playerScore);
}

function applyDynastyEffects() {
    // Dragon Power - slight boost to highest die
    if (dynastyDiceGame.dragonPower > 50) {
        const maxIndex = dynastyDiceGame.playerDice.indexOf(Math.max(...dynastyDiceGame.playerDice));
        if (dynastyDiceGame.playerDice[maxIndex] < 6) {
            dynastyDiceGame.playerDice[maxIndex] = Math.min(6, dynastyDiceGame.playerDice[maxIndex] + 1);
        }
    }
    
    // Imperial Favor - reroll one low die
    if (dynastyDiceGame.imperialFavor > 75) {
        const minIndex = dynastyDiceGame.playerDice.indexOf(Math.min(...dynastyDiceGame.playerDice));
        dynastyDiceGame.playerDice[minIndex] = Math.floor(Math.random() * 6) + 1;
    }
    
    // Mandate of Heaven activation
    if (dynastyDiceGame.dragonPower >= 100 && dynastyDiceGame.imperialFavor >= 100) {
        dynastyDiceGame.mandateOfHeaven = true;
        // Boost all dice slightly
        dynastyDiceGame.playerDice = dynastyDiceGame.playerDice.map(die => Math.min(6, die + 1));
    }
}

function calculateDynastyScore(dice, challengeType) {
    switch(challengeType) {
        case 'total':
            return dice.reduce((sum, die) => sum + die, 0);
        case 'pairs':
            return countPairs(dice);
        case 'sequence':
            return longestSequence(dice);
        default:
            return dice.reduce((sum, die) => sum + die, 0);
    }
}

function countPairs(dice) {
    const counts = {};
    dice.forEach(die => counts[die] = (counts[die] || 0) + 1);
    return Object.values(counts).filter(count => count >= 2).length;
}

function longestSequence(dice) {
    const sorted = [...new Set(dice)].sort((a, b) => a - b);
    let maxLength = 1;
    let currentLength = 1;
    
    for (let i = 1; i < sorted.length; i++) {
        if (sorted[i] === sorted[i-1] + 1) {
            currentLength++;
            maxLength = Math.max(maxLength, currentLength);
        } else {
            currentLength = 1;
        }
    }
    
    return maxLength;
}

function resolveDynastyChallenge(emperorScore, playerScore) {
    let won = false;
    let winnings = 0;
    
    if (playerScore > emperorScore) {
        won = true;
        dynastyDiceGame.dynastyLevel++;
        dynastyDiceGame.dragonPower = Math.min(100, dynastyDiceGame.dragonPower + 20);
        dynastyDiceGame.imperialFavor = Math.min(100, dynastyDiceGame.imperialFavor + 15);
        
        // Calculate winnings with dynasty bonuses
        const levelMultiplier = 1 + (dynastyDiceGame.dynastyLevel * 0.1);
        const mandateBonus = dynastyDiceGame.mandateOfHeaven ? 2.0 : 1.0;
        
        winnings = Math.floor(dynastyDiceGame.betAmount * levelMultiplier * mandateBonus * 1.9);
        balance += winnings;
        updateBalance();
        
        document.getElementById('dynastyResult').innerHTML = 
            `<span class="text-green-400 neon-glow">IMPERIAL VICTORY!</span>`;
        document.getElementById('dynastyStatus').innerHTML = 
            `You have pleased the Emperor! Won ${winnings} GA! Dynasty Level: ${dynastyDiceGame.dynastyLevel}`;
    } else {
        // Reset some progress on loss
        dynastyDiceGame.dragonPower = Math.max(0, dynastyDiceGame.dragonPower - 10);
        dynastyDiceGame.imperialFavor = Math.max(0, dynastyDiceGame.imperialFavor - 5);
        
        document.getElementById('dynastyResult').innerHTML = 
            `<span class="text-red-400">IMPERIAL DEFEAT!</span>`;
        document.getElementById('dynastyStatus').innerHTML = 
            `The Emperor's wisdom prevails! Your challenge has failed.`;
    }
    
    // Update displays
    document.getElementById('dynastyLevel').textContent = dynastyDiceGame.dynastyLevel;
    document.getElementById('dragonPower').textContent = Math.floor(dynastyDiceGame.dragonPower) + '%';
    document.getElementById('imperialFavor').textContent = Math.floor(dynastyDiceGame.imperialFavor) + '%';
    
    // Update mandate of heaven
    const mandateProgress = (dynastyDiceGame.dragonPower + dynastyDiceGame.imperialFavor) / 2;
    document.getElementById('mandateBar').style.width = mandateProgress + '%';
    document.getElementById('mandateText').textContent = 
        dynastyDiceGame.mandateOfHeaven ? 'ACTIVE' : mandateProgress >= 90 ? 'AWAKENING' : 'DORMANT';
    
    // Reset mandate if used
    if (dynastyDiceGame.mandateOfHeaven) {
        dynastyDiceGame.mandateOfHeaven = false;
        dynastyDiceGame.dragonPower = 0;
        dynastyDiceGame.imperialFavor = 0;
    }
    
    // Add to history
    addDynastyHistory(playerScore, emperorScore, won);
    
    // Re-enable roll button
    setTimeout(() => {
        document.getElementById('rollDynastyDice').disabled = false;
        dynastyDiceGame.isRolling = false;
    }, 3000);
}

function addDynastyHistory(playerScore, emperorScore, won) {
    const history = document.getElementById('dynastyHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-sm font-bold ${
        won ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = `${playerScore} vs ${emperorScore}`;
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

