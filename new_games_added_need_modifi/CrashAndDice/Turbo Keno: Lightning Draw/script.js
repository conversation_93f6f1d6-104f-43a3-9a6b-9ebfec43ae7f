// Game state
const walletIntegration = new GameWalletIntegration('TurboKeno');
let turboKenoGame = {
    isPlaying: false,
    selectedNumbers: new Set(),
    drawnNumbers: [],
    betAmount: 0,
    gameMode: 'turbo',
    drawSpeed: 'lightning',
    numberPattern: 'random',
    ballType: 'standard',
    machineCondition: 'new',
    consecutiveWins: 0,
    totalGames: 0,
    hitCount: 0,
    maxHits: 0,
    kenoLevel: 1,
    luckyStreak: 0
};

// Game modes that affect hit probability
const GAME_MODES = [
    { name: 'classic', hitBonus: 0.85, description: 'Classic Keno' },
    { name: 'turbo', hitBonus: 0.82, description: 'Turbo Keno' },
    { name: 'lightning', hitBonus: 0.78, description: 'Lightning Draw' },
    { name: 'mega', hitBonus: 0.74, description: 'Mega Speed' },
    { name: 'quantum', hitBonus: 0.70, description: 'Quantum Draw' }
];

// Draw speeds
const DRAW_SPEEDS = [
    { name: 'normal', speed: 1.0, description: 'Normal Speed' },
    { name: 'fast', speed: 1.3, description: 'Fast Draw' },
    { name: 'turbo', speed: 1.6, description: 'Turbo Speed' },
    { name: 'lightning', speed: 2.0, description: 'Lightning Speed' },
    { name: 'instant', speed: 2.5, description: 'Instant Draw' }
];

// Number patterns
const NUMBER_PATTERNS = [
    { name: 'random', bias: 1.0, description: 'Random Pattern' },
    { name: 'hot', bias: 1.2, description: 'Hot Numbers' },
    { name: 'cold', bias: 1.4, description: 'Cold Numbers' },
    { name: 'sequential', bias: 1.7, description: 'Sequential Pattern' },
    { name: 'chaos', bias: 2.2, description: 'Chaos Mode' }
];

// Ball types
const BALL_TYPES = [
    { name: 'standard', accuracy: 0.88, description: 'Standard Balls' },
    { name: 'weighted', accuracy: 0.84, description: 'Weighted Balls' },
    { name: 'magnetic', accuracy: 0.80, description: 'Magnetic Balls' },
    { name: 'plasma', accuracy: 0.76, description: 'Plasma Balls' },
    { name: 'quantum', accuracy: 0.72, description: 'Quantum Balls' }
];

// Machine conditions
const MACHINE_CONDITIONS = [
    { name: 'new', reliability: 1.0, description: 'New Machine' },
    { name: 'used', reliability: 1.1, description: 'Used Machine' },
    { name: 'worn', reliability: 1.3, description: 'Worn Machine' },
    { name: 'damaged', reliability: 1.6, description: 'Damaged Machine' },
    { name: 'broken', reliability: 2.1, description: 'Broken Machine' }
];

function loadTurboKenoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Turbo Keno Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">⚡ TURBO KENO: LIGHTNING DRAW ⚡</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 KENO BET</label>
                        <input type="number" id="kenoBet" value="25" min="5" 
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic">🎲 Classic Keno</option>
                            <option value="turbo" selected>⚡ Turbo Keno</option>
                            <option value="lightning">🌩️ Lightning Draw</option>
                            <option value="mega">💨 Mega Speed</option>
                            <option value="quantum">⚛️ Quantum Draw (Extreme)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏃 DRAW SPEED</label>
                        <select id="drawSpeed" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">🐌 Normal Speed</option>
                            <option value="fast">🏃 Fast Draw</option>
                            <option value="turbo">💨 Turbo Speed</option>
                            <option value="lightning" selected>⚡ Lightning Speed</option>
                            <option value="instant">🚀 Instant Draw</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🔢 NUMBER PATTERN</label>
                        <select id="numberPattern" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="random" selected>🎲 Random Pattern</option>
                            <option value="hot">🔥 Hot Numbers</option>
                            <option value="cold">❄️ Cold Numbers</option>
                            <option value="sequential">📊 Sequential Pattern</option>
                            <option value="chaos">🌪️ Chaos Mode (Extreme)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚪ BALL TYPE</label>
                        <select id="ballType" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard" selected>⚪ Standard Balls</option>
                            <option value="weighted">⚫ Weighted Balls</option>
                            <option value="magnetic">🧲 Magnetic Balls</option>
                            <option value="plasma">🔮 Plasma Balls</option>
                            <option value="quantum">⚛️ Quantum Balls (Extreme)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎰 MACHINE CONDITION</label>
                        <select id="machineCondition" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="new" selected>🆕 New Machine</option>
                            <option value="used">🔧 Used Machine</option>
                            <option value="worn">⚙️ Worn Machine</option>
                            <option value="damaged">💥 Damaged Machine</option>
                            <option value="broken">🔥 Broken Machine (Extreme)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <button id="clearNumbers" class="w-full bg-red-600 hover:bg-red-700 py-2 rounded-lg font-bold text-white mb-2">
                            🗑️ CLEAR ALL NUMBERS
                        </button>
                        <button id="quickPick" class="w-full bg-purple-600 hover:bg-purple-700 py-2 rounded-lg font-bold text-white">
                            🎲 QUICK PICK (10 NUMBERS)
                        </button>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="startKeno" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            ⚡ START LIGHTNING DRAW
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎯 Selected Numbers</div>
                        <div id="selectedCount" class="text-lg font-bold text-cyan-400">0/20</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="kenoPotentialPayout" class="text-2xl font-bold text-cyan-400 neon-glow">0 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🎰 Keno Level</div>
                        <div id="kenoLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🍀 Lucky Streak</div>
                        <div id="luckyStreakDisplay" class="text-lg font-bold text-yellow-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Keno Board and Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h5 class="text-lg font-bold mb-4 text-cyan-400 text-center">⚡ LIGHTNING KENO BOARD ⚡</h5>
                    
                    <!-- Keno Number Grid -->
                    <div class="mb-6">
                        <div id="kenoBoard" class="grid grid-cols-10 gap-1 mb-4">
                            <!-- Numbers 1-80 will be generated here -->
                        </div>
                    </div>
                    
                    <!-- Draw Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">⚡ Draw Status</div>
                            <div id="drawStatus" class="text-lg font-bold text-cyan-400">Select your numbers...</div>
                            <div class="text-xs text-gray-400">Mode: <span id="modeStatus" class="text-blue-400">Turbo</span></div>
                            <div class="text-xs text-gray-400">Speed: <span id="speedStatus" class="text-green-400">Lightning</span></div>
                            <div class="text-xs text-gray-400">Pattern: <span id="patternStatus" class="text-orange-400">Random</span></div>
                            <div class="text-xs text-gray-400">Machine: <span id="machineStatus" class="text-purple-400">New</span></div>
                        </div>
                    </div>
                    
                    <!-- Draw Progress -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🎲 Draw Progress</div>
                            <div class="w-full bg-black/50 rounded-full h-4">
                                <div id="drawProgress" class="bg-gradient-to-r from-cyan-500 to-blue-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="drawProgressText" class="text-center mt-2 text-cyan-300">Ready to draw</div>
                        </div>
                    </div>
                    
                    <!-- Drawn Numbers Display -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🎯 Drawn Numbers</div>
                            <div id="drawnNumbers" class="flex flex-wrap gap-1 min-h-[40px]">
                                <!-- Drawn numbers will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <div id="kenoResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="kenoStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Keno Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">📊 KENO RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Winning Games</div>
                            <div id="consecutiveWins" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Games</div>
                            <div id="totalGames" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Hits</div>
                            <div id="hitCount" class="text-lg font-bold text-yellow-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Max Hits</div>
                            <div id="maxHits" class="text-lg font-bold text-orange-400">0</div>
                        </div>
                    </div>
                </div>
                
                <!-- Lightning System -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">⚡ LIGHTNING SYSTEM ⚡</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="lightningCharge" class="bg-gradient-to-r from-yellow-500 to-cyan-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="lightningText" class="text-center mt-2 text-yellow-300">Lightning Charging...</div>
                </div>
            </div>
        </div>
    `;
    
    initializeTurboKeno();
}

function initializeTurboKeno() {
    document.getElementById('startKeno').addEventListener('click', startKeno);
    document.getElementById('clearNumbers').addEventListener('click', clearNumbers);
    document.getElementById('quickPick').addEventListener('click', quickPick);
    document.getElementById('gameMode').addEventListener('change', updateGameMode);
    document.getElementById('drawSpeed').addEventListener('change', updateDrawSpeed);
    document.getElementById('numberPattern').addEventListener('change', updateNumberPattern);
    document.getElementById('ballType').addEventListener('change', updateBallType);
    document.getElementById('machineCondition').addEventListener('change', updateMachineCondition);
    
    generateKenoBoard();
    updateGameMode();
    updateDrawSpeed();
    updateNumberPattern();
    updateBallType();
    updateMachineCondition();
    updatePayout();
}

function generateKenoBoard() {
    const board = document.getElementById('kenoBoard');
    board.innerHTML = '';
    
    for (let i = 1; i <= 80; i++) {
        const numberButton = document.createElement('button');
        numberButton.className = 'w-8 h-8 bg-black/50 border border-cyan-500/30 rounded text-white text-sm hover:bg-cyan-500/30 transition-all';
        numberButton.textContent = i;
        numberButton.addEventListener('click', () => toggleNumber(i));
        board.appendChild(numberButton);
    }
}

function toggleNumber(number) {
    if (turboKenoGame.selectedNumbers.has(number)) {
        turboKenoGame.selectedNumbers.delete(number);
        document.querySelector(`#kenoBoard button:nth-child(${number})`).classList.remove('bg-cyan-500', 'text-black');
    } else if (turboKenoGame.selectedNumbers.size < 20) {
        turboKenoGame.selectedNumbers.add(number);
        document.querySelector(`#kenoBoard button:nth-child(${number})`).classList.add('bg-cyan-500', 'text-black');
    }
    
    updateSelectedCount();
    updatePayout();
}

function clearNumbers() {
    turboKenoGame.selectedNumbers.clear();
    document.querySelectorAll('#kenoBoard button').forEach(btn => {
        btn.classList.remove('bg-cyan-500', 'text-black');
    });
    updateSelectedCount();
    updatePayout();
}

function quickPick() {
    clearNumbers();
    while (turboKenoGame.selectedNumbers.size < 10) {
        const randomNumber = Math.floor(Math.random() * 80) + 1;
        if (!turboKenoGame.selectedNumbers.has(randomNumber)) {
            toggleNumber(randomNumber);
        }
    }
}

function updateSelectedCount() {
    document.getElementById('selectedCount').textContent = `${turboKenoGame.selectedNumbers.size}/20`;
}

function updateGameMode() {
    const mode = document.getElementById('gameMode').value;
    turboKenoGame.gameMode = mode;
    
    const modeData = GAME_MODES.find(m => m.name === mode);
    document.getElementById('modeStatus').textContent = modeData.description;
    
    updatePayout();
}

function updateDrawSpeed() {
    const speed = document.getElementById('drawSpeed').value;
    turboKenoGame.drawSpeed = speed;
    
    const speedData = DRAW_SPEEDS.find(s => s.name === speed);
    document.getElementById('speedStatus').textContent = speedData.description;
    
    updatePayout();
}

function updateNumberPattern() {
    const pattern = document.getElementById('numberPattern').value;
    turboKenoGame.numberPattern = pattern;
    
    const patternData = NUMBER_PATTERNS.find(p => p.name === pattern);
    document.getElementById('patternStatus').textContent = patternData.description;
    
    updatePayout();
}

function updateBallType() {
    const ballType = document.getElementById('ballType').value;
    turboKenoGame.ballType = ballType;
    
    const ballData = BALL_TYPES.find(b => b.name === ballType);
    document.getElementById('ballStatus').textContent = ballData.description;
    
    updatePayout();
}

function updateMachineCondition() {
    const condition = document.getElementById('machineCondition').value;
    turboKenoGame.machineCondition = condition;
    
    const machineData = MACHINE_CONDITIONS.find(m => m.name === condition);
    document.getElementById('machineStatus').textContent = machineData.description;
    
    updatePayout();
}

function updatePayout() {
    const betAmount = parseInt(document.getElementById('kenoBet').value) || 25;
    const selectedCount = turboKenoGame.selectedNumbers.size;
    
    if (selectedCount === 0) {
        document.getElementById('kenoPotentialPayout').textContent = '0 GA';
        return;
    }
    
    // Calculate potential payout (very low)
    const basePayout = getKenoPayout(selectedCount, Math.floor(selectedCount * 0.6));
    const potentialPayout = Math.floor(betAmount * basePayout);
    
    document.getElementById('kenoPotentialPayout').textContent = potentialPayout + ' GA';
}

function getKenoPayout(selected, hits) {
    // Extremely low payout table
    const payoutTable = {
        1: { 1: 3.0 },
        2: { 2: 12.0 },
        3: { 2: 1.0, 3: 42.0 },
        4: { 2: 0.5, 3: 4.0, 4: 120.0 },
        5: { 3: 1.0, 4: 12.0, 5: 750.0 },
        6: { 3: 0.5, 4: 3.0, 5: 75.0, 6: 1500.0 },
        7: { 4: 1.0, 5: 20.0, 6: 400.0, 7: 7500.0 },
        8: { 5: 10.0, 6: 80.0, 7: 1500.0, 8: 25000.0 },
        9: { 5: 5.0, 6: 25.0, 7: 300.0, 8: 4000.0, 9: 40000.0 },
        10: { 5: 2.0, 6: 15.0, 7: 100.0, 8: 1000.0, 9: 10000.0, 10: 100000.0 }
    };
    
    if (payoutTable[selected] && payoutTable[selected][hits]) {
    const betAmount = parseInt(document.getElementById('kenoBet').value);

    if (isNaN(betAmount) || betAmount <= 0) {
        alert('Please enter a valid bet amount.');
        return;
    }

    if (turboKenoGame.selectedNumbers.size < 1) {
        alert('Please select at least one number to start the game.');
        return;
    }

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return; // Error handled by the shared module
    }

    turboKenoGame.isPlaying = true;
    turboKenoGame.betAmount = betAmount;
    turboKenoGame.totalGames++;

    document.getElementById('startKeno').disabled = true;
    document.getElementById('clearNumbers').disabled = true;
    document.getElementById('quickPick').disabled = true;

    startLightningDraw();
}

// ... (rest of the code remains the same)

async function calculateKenoResult() {
    const hits = turboKenoGame.drawnNumbers.filter(num => turboKenoGame.selectedNumbers.has(num)).length;
    const selectedCount = turboKenoGame.selectedNumbers.size;
    const payoutMultiplier = getKenoPayout(selectedCount, hits);
    const winnings = Math.floor(turboKenoGame.betAmount * payoutMultiplier);
    const netWinnings = winnings - turboKenoGame.betAmount;

    if (netWinnings > 0) {
        await walletIntegration.processWin(netWinnings);
    }

    turboKenoGame.hitCount += hits;
    if (hits > turboKenoGame.maxHits) {
        turboKenoGame.maxHits = hits;
    }

    if (winnings > 0) {
        turboKenoGame.consecutiveWins++;
        turboKenoGame.luckyStreak++;
        document.getElementById('kenoResult').innerHTML = `<span class="text-green-400 neon-glow">🎉 YOU WON ${winnings} GA! 🎉</span>`;
        document.getElementById('kenoStatus').textContent = `You hit ${hits} out of ${selectedCount} numbers!`;
    } else {
        turboKenoGame.consecutiveWins = 0;
        turboKenoGame.luckyStreak = 0;
        document.getElementById('kenoResult').innerHTML = `<span class="text-red-400">😢 Better luck next time! 😢</span>`;
        document.getElementById('kenoStatus').textContent = `You hit ${hits} out of ${selectedCount} numbers.`;
    }

    updateKenoDisplay();
    resetKenoControls();
}

function resetKenoControls() {
    setTimeout(() => {
        document.getElementById('startKeno').disabled = false;
        document.getElementById('clearNumbers').disabled = false;
        document.getElementById('quickPick').disabled = false;
        document.getElementById('drawStatus').textContent = 'Select your numbers...';
        document.getElementById('drawProgress').style.width = '0%';
        document.getElementById('drawProgressText').textContent = 'Ready to draw';
        turboKenoGame.isPlaying = false;
    }, 3000);
}

function updateKenoDisplay() {
    document.getElementById('kenoLevelDisplay').textContent = turboKenoGame.kenoLevel;
    document.getElementById('luckyStreakDisplay').textContent = turboKenoGame.luckyStreak;
    document.getElementById('consecutiveWins').textContent = turboKenoGame.consecutiveWins;
    document.getElementById('totalGames').textContent = turboKenoGame.totalGames;
    document.getElementById('hitCount').textContent = turboKenoGame.hitCount;
    document.getElementById('maxHits').textContent = turboKenoGame.maxHits;
    
    // Update lightning charge
    const charge = Math.min(100, (turboKenoGame.luckyStreak / 10) * 100);
    document.getElementById('lightningCharge').style.width = charge + '%';
    
    let lightningStatus = 'Lightning Charging...';
    if (turboKenoGame.luckyStreak >= 10) lightningStatus = 'MAXIMUM LIGHTNING POWER!';
    else if (turboKenoGame.luckyStreak >= 7) lightningStatus = 'High Lightning Energy';
    else if (turboKenoGame.luckyStreak >= 5) lightningStatus = 'Lightning Building';
    else if (turboKenoGame.luckyStreak >= 3) lightningStatus = 'Lightning Sparks';
    
    document.getElementById('lightningText').textContent = lightningStatus;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadTurboKenoGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

