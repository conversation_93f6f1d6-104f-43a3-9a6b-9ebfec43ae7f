// Game state
const walletIntegration = new GameWalletIntegration('Keno');



        function loadKenoGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                            <h4 class="text-xl font-bold mb-4 text-pink-400">MATRIX SELECTION</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="kenoBet" value="10" min="1"
                                       class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <div class="text-sm text-gray-300 mb-2">
                                    SELECTED NUMBERS: <span id="kenoSelectedCount" class="text-pink-400 font-bold">0</span>/10
                                </div>
                                <div class="text-xs text-gray-400">Pick 1-10 numbers</div>
                            </div>
                            
                            <button id="quickPickKeno" class="w-full py-2 rounded-lg font-bold bg-pink-600 hover:bg-pink-700 text-white mb-2">
                                QUICK PICK
                            </button>
                            
                            <button id="clearKeno" class="w-full py-2 rounded-lg font-bold bg-gray-600 hover:bg-gray-700 text-white mb-4">
                                CLEAR ALL
                            </button>
                            
                            <button id="playKeno" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4" disabled>
                                PLAY KENO
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Potential Payout</div>
                                <div id="kenoPayout" class="text-xl font-bold text-green-400 neon-glow">$0</div>
                            </div>
                        </div>
                        
                        <!-- Paytable -->
                        <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-pink-400">PAYTABLE</h5>
                            <div id="kenoPaytable" class="text-xs space-y-1"></div>
                        </div>
                    </div>
                    
                    <!-- Keno Board -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                            <div id="kenoBoard" class="grid grid-cols-8 gap-2 mb-6">
                                <!-- Numbers 1-80 will be generated here -->
                            </div>
                            <div id="kenoResults" class="mt-4">
                                <div id="kenoStatus" class="text-center text-lg font-semibold mb-4"></div>
                                <div id="kenoDrawnNumbers" class="text-center"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeKeno();
        }
        
        let kenoGame = {
            selectedNumbers: new Set(),
            drawnNumbers: [],
            isPlaying: false
        };
        
        function initializeKeno() {
            generateKenoBoard();
            updateKenoPaytable();
            
            document.getElementById('quickPickKeno').addEventListener('click', quickPickKeno);
            document.getElementById('clearKeno').addEventListener('click', clearKeno);
            document.getElementById('playKeno').addEventListener('click', playKeno);
        }
        
        function generateKenoBoard() {
            const board = document.getElementById('kenoBoard');
            board.innerHTML = '';
            
            for (let i = 1; i <= 80; i++) {
                const number = document.createElement('div');
                number.className = 'w-8 h-8 bg-gradient-to-br from-pink-600 to-purple-600 border border-pink-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all hover:scale-105 neon-border';
                number.textContent = i;
                number.dataset.number = i;
                number.addEventListener('click', () => toggleKenoNumber(i));
                board.appendChild(number);
            }
        }
        
        function toggleKenoNumber(number) {
            if (kenoGame.isPlaying) return;
            
            const numberElement = document.querySelector(`[data-number="${number}"]`);
            
            if (kenoGame.selectedNumbers.has(number)) {
                // Deselect
                kenoGame.selectedNumbers.delete(number);
                numberElement.className = 'w-8 h-8 bg-gradient-to-br from-pink-600 to-purple-600 border border-pink-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all hover:scale-105 neon-border';
            } else {
                // Select (max 10 numbers)
                if (kenoGame.selectedNumbers.size < 10) {
                    kenoGame.selectedNumbers.add(number);
                    numberElement.className = 'w-8 h-8 bg-gradient-to-br from-green-500 to-blue-500 border border-green-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all scale-105 neon-glow';
                }
            }
            
            updateKenoDisplay();
        }
        
        function quickPickKeno() {
            if (kenoGame.isPlaying) return;
            
            clearKeno();
            
            // Pick random numbers
            const pickCount = Math.floor(Math.random() * 10) + 1; // 1-10 numbers
            while (kenoGame.selectedNumbers.size < pickCount) {
                const randomNumber = Math.floor(Math.random() * 80) + 1;
                if (!kenoGame.selectedNumbers.has(randomNumber)) {
                    toggleKenoNumber(randomNumber);
                }
            }
        }
        
        function clearKeno() {
            if (kenoGame.isPlaying) return;
            
            kenoGame.selectedNumbers.clear();
            
            // Reset all number styles
            for (let i = 1; i <= 80; i++) {
                const numberElement = document.querySelector(`[data-number="${i}"]`);
                numberElement.className = 'w-8 h-8 bg-gradient-to-br from-pink-600 to-purple-600 border border-pink-400 rounded-lg cursor-pointer flex items-center justify-center text-sm font-bold transition-all hover:scale-105 neon-border';
            }
            
            updateKenoDisplay();
        }
        
        function updateKenoDisplay() {
            document.getElementById('kenoSelectedCount').textContent = kenoGame.selectedNumbers.size;
            
            const playButton = document.getElementById('playKeno');
            playButton.disabled = kenoGame.selectedNumbers.size === 0;
            
            // Update potential payout
            const betAmount = parseInt(document.getElementById('kenoBet').value) || 0;
            const selectedCount = kenoGame.selectedNumbers.size;
            const maxPayout = getKenoMaxPayout(selectedCount);
            
            document.getElementById('kenoPayout').textContent = `$${Math.floor(betAmount * maxPayout)}`;
        }
        
        function getKenoMaxPayout(selectedCount) {
            const payouts = {
                1: 3.75, 2: 15, 3: 46, 4: 100, 5: 300,
                6: 1000, 7: 2500, 8: 5000, 9: 10000, 10: 25000
            };
            return payouts[selectedCount] || 0;
        }
        
        function updateKenoPaytable() {
            const paytable = document.getElementById('kenoPaytable');
            const payouts = {
                1: { hits: { 1: 3.75 } },
                2: { hits: { 2: 15 } },
                3: { hits: { 2: 1, 3: 46 } },
                4: { hits: { 2: 1, 3: 5, 4: 100 } },
                5: { hits: { 3: 2, 4: 20, 5: 300 } },
                6: { hits: { 3: 1, 4: 5, 5: 100, 6: 1000 } },
                7: { hits: { 4: 2, 5: 25, 6: 400, 7: 2500 } },
                8: { hits: { 5: 10, 6: 100, 7: 1000, 8: 5000 } },
                9: { hits: { 5: 5, 6: 50, 7: 300, 8: 2000, 9: 10000 } },
                10: { hits: { 5: 2, 6: 20, 7: 100, 8: 500, 9: 5000, 10: 25000 } }
            };
            
            paytable.innerHTML = '';
            for (let spots = 1; spots <= 10; spots++) {
                const row = document.createElement('div');
                row.className = 'flex justify-between items-center';
                row.innerHTML = `
                    <span class="text-gray-400">${spots} spots:</span>
                    <span class="text-pink-400">${getKenoMaxPayout(spots)}x max</span>
                `;
                paytable.appendChild(row);
            }
        }
        
        async function playKeno() {
            const betAmount = parseInt(document.getElementById('kenoBet').value);

            if (kenoGame.selectedNumbers.size === 0) {
                return;
            }

            const transaction = await walletIntegration.processBet(betAmount);
            if (!transaction.success) {
                return;
            }

            kenoGame.isPlaying = true;
            
            document.getElementById('playKeno').disabled = true;
            document.getElementById('kenoStatus').textContent = 'Drawing numbers...';
            
            // Draw 20 numbers
            kenoGame.drawnNumbers = [];
            while (kenoGame.drawnNumbers.length < 20) {
                const randomNumber = Math.floor(Math.random() * 80) + 1;
                if (!kenoGame.drawnNumbers.includes(randomNumber)) {
                    kenoGame.drawnNumbers.push(randomNumber);
                }
            }
            
            // Animate the draw
            animateKenoDraw(betAmount);
        }
        
        function animateKenoDraw(betAmount) {
            let drawnSoFar = 0;
            const drawInterval = setInterval(() => {
                if (drawnSoFar < kenoGame.drawnNumbers.length) {
                    const number = kenoGame.drawnNumbers[drawnSoFar];
                    const numberElement = document.querySelector(`[data-number="${number}"]`);
                    
                    if (kenoGame.selectedNumbers.has(number)) {
                        // Hit - yellow glow
                        numberElement.className = 'w-8 h-8 bg-gradient-to-br from-yellow-500 to-orange-500 border border-yellow-400 rounded-lg flex items-center justify-center text-sm font-bold neon-glow scale-110';
                    } else {
                        // Miss - red
                        numberElement.className = 'w-8 h-8 bg-gradient-to-br from-red-600 to-red-800 border border-red-400 rounded-lg flex items-center justify-center text-sm font-bold';
                    }
                    
                    drawnSoFar++;
                } else {
                    clearInterval(drawInterval);
                    calculateKenoResult(betAmount);
                }
            }, 100);
        }
        
        async function calculateKenoResult(betAmount) {
            // Count hits
            let hits = 0;
            kenoGame.selectedNumbers.forEach(number => {
                if (kenoGame.drawnNumbers.includes(number)) {
                    hits++;
                }
            });

            // Calculate payout
            const selectedCount = kenoGame.selectedNumbers.size;
            const payoutMultiplier = getKenoPayout(selectedCount, hits);
            const payout = Math.floor(betAmount * payoutMultiplier);
            const netWinnings = payout - betAmount;

            if (netWinnings > 0) {
                await walletIntegration.processWin(netWinnings);
            }

            // Update display
            document.getElementById('kenoStatus').innerHTML = 
                `<span class="text-pink-400">You hit ${hits}/${selectedCount} numbers!</span>`;

            if (payout > betAmount) {
                document.getElementById('kenoDrawnNumbers').innerHTML = 
                    `<span class="text-green-400 neon-glow">You won ${payout} GA!</span>`;
            } else if (payout === betAmount) {
                document.getElementById('kenoDrawnNumbers').innerHTML = 
                    `<span class="text-yellow-400">Break even!</span>`;
            } else {
                document.getElementById('kenoDrawnNumbers').innerHTML = 
                    `<span class="text-red-400">You won ${payout} GA</span>`;
            }
            
            // Reset game state
            setTimeout(() => {
                kenoGame.isPlaying = false;
                document.getElementById('playKeno').disabled = false;
                clearKeno();
                generateKenoBoard();
                document.getElementById('kenoStatus').textContent = '';
                document.getElementById('kenoDrawnNumbers').textContent = '';
            }, 3000);
        }
        
        function getKenoPayout(selected, hits) {
            const payoutTable = {
                1: { 1: 3.75 },
                2: { 2: 15 },
                3: { 2: 1, 3: 46 },
                4: { 2: 1, 3: 5, 4: 100 },
                5: { 3: 2, 4: 20, 5: 300 },
                6: { 3: 1, 4: 5, 5: 100, 6: 1000 },
                7: { 4: 2, 5: 25, 6: 400, 7: 2500 },
                8: { 5: 10, 6: 100, 7: 1000, 8: 5000 },
                9: { 5: 5, 6: 50, 7: 300, 8: 2000, 9: 10000 },
                10: { 5: 2, 6: 20, 7: 100, 8: 500, 9: 5000, 10: 25000 }
            };
            
            return payoutTable[selected]?.[hits] || 0;
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadKenoGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});