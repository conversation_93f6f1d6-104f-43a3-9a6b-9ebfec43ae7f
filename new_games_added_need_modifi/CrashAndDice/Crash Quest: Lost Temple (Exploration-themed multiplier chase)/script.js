
// Game state
let balance = 1000;
let explorationGame = {
    isExploring: false,
    multiplier: 1.00,
    betAmount: 0,
    startTime: 0,
    crashPoint: 0,
    autoCashout: 0,
    expeditionType: 'surface',
    artifacts: 0,
    dangerLevel: 0,
    mysticalEnergy: 0,
    ancientGuardian: false
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Expedition Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">CRASH QUEST: FROZEN TUNDRA</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">EXPEDITION BUDGET</label>
                        <input type="number" id="expeditionBet" value="10" min="1" max="${balance}" 
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">EMERGENCY EXTRACTION</label>
                        <input type="number" id="expeditionAutoCashout" value="2.00" min="1.01" step="0.01" 
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">EXPEDITION TYPE</label>
                        <select id="expeditionType" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="surface">Surface Exploration (Safer)</option>
                            <option value="deep">Deep Excavation (Risky)</option>
                            <option value="forbidden">Forbidden Depths (Extreme)</option>
                        </select>
                    </div>
                    
                    <button id="startExpedition" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        BEGIN EXPEDITION
                    </button>
                    
                    <button id="extractTeam" class="cyber-button-secondary w-full py-3 rounded-lg font-bold text-white" disabled>
                        EXTRACT TEAM
                    </button>
                    
                    <div class="mt-4 text-center">
                        <div class="text-sm text-gray-400 mb-1">Discovery Multiplier</div>
                        <div id="expeditionMultiplier" class="text-3xl font-bold text-orange-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Artifacts Found</div>
                        <div id="artifactsFound" class="text-lg font-bold text-yellow-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Danger Level</div>
                        <div id="dangerLevel" class="text-lg font-bold text-red-400">Safe</div>
                    </div>
                </div>
            </div>
            
            <!-- Tundra Visualization -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <div id="tundraCanvas" class="relative bg-gradient-to-b from-blue-200 to-white rounded-lg h-96 overflow-hidden">
                        <div id="expeditionTeam" class="absolute top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-orange-500 rounded-full transition-all duration-200"></div>
                        <div id="artifacts" class="absolute inset-0"></div>
                        <div id="hazards" class="absolute inset-0"></div>
                        <div id="blizzardEffect" class="absolute inset-0 bg-blue-600/60 opacity-0 transition-opacity duration-500"></div>
                        <div id="guardianEffect" class="absolute inset-0 bg-purple-600/40 opacity-0 transition-opacity duration-1000"></div>
                    </div>
                    <div id="expeditionStatus" class="text-center mt-4 text-lg font-semibold">Ready to explore the frozen tundra</div>
                </div>
                
                <!-- Discovery Log -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">DISCOVERY LOG</h5>
                    <div id="expeditionHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Mystical Energy -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">MYSTICAL ENERGY</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="mysticalEnergyBar" class="bg-purple-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="mysticalEnergyText" class="text-center mt-2 text-purple-300">0%</div>
                </div>
            </div>
        </div>
    `;
    
    initializeExpedition();
}

function initializeExpedition() {
    document.getElementById('startExpedition').addEventListener('click', startExpedition);
    document.getElementById('extractTeam').addEventListener('click', extractTeam);
    document.getElementById('expeditionType').addEventListener('change', updateExpeditionType);
    
    updateExpeditionType();
}

function updateExpeditionType() {
    const type = document.getElementById('expeditionType').value;
    explorationGame.expeditionType = type;
}

function generateExpeditionCrashPoint() {
    // Ultra-harsh crash points for < 10% win rate
    const type = explorationGame.expeditionType;
    const baseRandom = Math.random();
    
    // Expedition type affects extreme risk
    let riskMultiplier;
    switch(type) {
        case 'surface': riskMultiplier = 0.96; break; // 96% house edge
        case 'deep': riskMultiplier = 0.93; break; // 93% house edge
        case 'forbidden': riskMultiplier = 0.89; break; // 89% house edge
    }
    
    // Generate crash point with extreme house edge
    const adjustedRandom = Math.pow(baseRandom, 4); // Fourth power for ultra-low values
    const crashPoint = 1 / (1 - adjustedRandom * riskMultiplier);
    
    return Math.max(1.01, Math.min(crashPoint, 20.0));
}

function startExpedition() {
    const betAmount = parseInt(document.getElementById('expeditionBet').value);
    const autoCashout = parseFloat(document.getElementById('expeditionAutoCashout').value);
    
    if (betAmount > balance) {
        alert('Insufficient funds for expedition!');
        return;
    }
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    // Initialize game state
    explorationGame.isExploring = true;
    explorationGame.multiplier = 1.00;
    explorationGame.betAmount = betAmount;
    explorationGame.startTime = Date.now();
    explorationGame.autoCashout = autoCashout;
    explorationGame.crashPoint = generateExpeditionCrashPoint();
    explorationGame.artifacts = 0;
    explorationGame.dangerLevel = 0;
    explorationGame.mysticalEnergy = 0;
    explorationGame.ancientGuardian = false;
    
    // Update UI
    document.getElementById('startExpedition').disabled = true;
    document.getElementById('extractTeam').disabled = false;
    document.getElementById('expeditionStatus').textContent = 'Expedition team exploring the frozen tundra...';
    
    animateExpedition();
}

function animateExpedition() {
    if (!explorationGame.isExploring) return;
    
    const elapsed = (Date.now() - explorationGame.startTime) / 1000;
    
    // Calculate multiplier with expedition bonuses
    const typeMultiplier = explorationGame.expeditionType === 'surface' ? 0.8 : 
                          explorationGame.expeditionType === 'deep' ? 1.2 : 1.5;
    explorationGame.multiplier = 1 + (elapsed * 0.25 * typeMultiplier);
    
    // Increase danger level over time
    explorationGame.dangerLevel = Math.min(100, elapsed * 12);
    
    // Mystical energy builds up
    explorationGame.mysticalEnergy = Math.min(100, elapsed * 8);
    
    // Update displays
    document.getElementById('expeditionMultiplier').textContent = explorationGame.multiplier.toFixed(2) + 'x';
    document.getElementById('artifactsFound').textContent = explorationGame.artifacts;
    
    // Update danger level display
    const dangerEl = document.getElementById('dangerLevel');
    if (explorationGame.dangerLevel < 30) {
        dangerEl.textContent = 'Safe';
        dangerEl.className = 'text-lg font-bold text-green-400';
    } else if (explorationGame.dangerLevel < 60) {
        dangerEl.textContent = 'Risky';
        dangerEl.className = 'text-lg font-bold text-yellow-400';
    } else if (explorationGame.dangerLevel < 85) {
        dangerEl.textContent = 'Dangerous';
        dangerEl.className = 'text-lg font-bold text-orange-400';
    } else {
        dangerEl.textContent = 'LETHAL';
        dangerEl.className = 'text-lg font-bold text-red-400 animate-pulse';
    }
    
    // Update mystical energy bar
    document.getElementById('mysticalEnergyBar').style.width = explorationGame.mysticalEnergy + '%';
    document.getElementById('mysticalEnergyText').textContent = Math.floor(explorationGame.mysticalEnergy) + '%';
    
    // Update team position
    const team = document.getElementById('expeditionTeam');
    const progress = Math.min(85, elapsed * 6);
    team.style.top = progress + '%';
    team.style.left = (50 + Math.sin(elapsed) * 20) + '%';
    
    // Generate expedition elements
    generateExpeditionElements();
    
    // Ancient Guardian activation
    if (explorationGame.mysticalEnergy >= 80 && !explorationGame.ancientGuardian) {
        activateAncientGuardian();
    }
    
    // Check for blizzard (crash)
    if (explorationGame.multiplier >= explorationGame.crashPoint) {
        triggerBlizzard();
        return;
    }
    
    // Check auto extraction
    if (explorationGame.multiplier >= explorationGame.autoCashout) {
        extractTeam();
        return;
    }
    
    setTimeout(animateExpedition, 120);
}

function generateExpeditionElements() {
    // Find artifacts
    if (Math.random() < 0.25) {
        const artifact = document.createElement('div');
        artifact.className = 'absolute w-4 h-4 bg-yellow-300 rounded-full animate-pulse';
        artifact.style.left = Math.random() * 85 + 5 + '%';
        artifact.style.top = Math.random() * 85 + 5 + '%';
        
        document.getElementById('artifacts').appendChild(artifact);
        explorationGame.artifacts++;
        
        setTimeout(() => {
            if (artifact.parentNode) {
                artifact.parentNode.removeChild(artifact);
            }
        }, 2500);
    }
    
    // Environmental hazards
    if (Math.random() < 0.35) {
        const hazard = document.createElement('div');
        hazard.className = 'absolute w-3 h-3 bg-blue-400 rounded-full opacity-70';
        hazard.style.left = Math.random() * 90 + 5 + '%';
        hazard.style.top = Math.random() * 90 + 5 + '%';
        
        document.getElementById('hazards').appendChild(hazard);
        
        setTimeout(() => {
            if (hazard.parentNode) {
                hazard.parentNode.removeChild(hazard);
            }
        }, 1800);
    }
    
    // Limit elements
    const artifacts = document.getElementById('artifacts');
    const hazards = document.getElementById('hazards');
    while (artifacts.children.length > 6) {
        artifacts.removeChild(artifacts.firstChild);
    }
    while (hazards.children.length > 12) {
        hazards.removeChild(hazards.firstChild);
    }
}

function activateAncientGuardian() {
    explorationGame.ancientGuardian = true;
    
    // Show guardian effect
    document.getElementById('guardianEffect').style.opacity = '1';
    
    // Guardian provides massive multiplier boost but increases crash risk
    explorationGame.multiplier *= 2.5;
    explorationGame.crashPoint *= 0.7; // Reduce crash point significantly
    
    document.getElementById('expeditionStatus').innerHTML = 
        `<span class="text-purple-400 animate-pulse">Ancient Guardian awakened! Mystical power surges!</span>`;
    
    setTimeout(() => {
        document.getElementById('guardianEffect').style.opacity = '0.2';
    }, 3000);
}

function extractTeam() {
    if (!explorationGame.isExploring) return;
    
    // Calculate winnings with artifact and guardian bonuses
    let artifactBonus = 1 + (explorationGame.artifacts * 0.15);
    let guardianBonus = explorationGame.ancientGuardian ? 1.8 : 1;
    let mysticalBonus = 1 + (explorationGame.mysticalEnergy / 200);
    
    const totalBonus = artifactBonus * guardianBonus * mysticalBonus;
    const winnings = Math.floor(explorationGame.betAmount * explorationGame.multiplier * totalBonus);
    balance += winnings;
    updateBalance();
    
    explorationGame.isExploring = false;
    
    document.getElementById('expeditionStatus').innerHTML = 
        `<span class="text-green-400 neon-glow">Team extracted at ${explorationGame.multiplier.toFixed(2)}x! Found ${explorationGame.artifacts} artifacts! Won ${winnings} GA</span>`;
    
    addExpeditionHistory(explorationGame.multiplier, true);
    resetExpeditionGame();
}

function triggerBlizzard() {
    explorationGame.isExploring = false;
    
    // Show blizzard effect
    document.getElementById('blizzardEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('blizzardEffect').style.opacity = '0';
    }, 2000);
    
    document.getElementById('expeditionStatus').innerHTML = 
        `<span class="text-red-400">Deadly blizzard at ${explorationGame.crashPoint.toFixed(2)}x! The expedition team is lost in the frozen tundra!</span>`;
    
    addExpeditionHistory(explorationGame.crashPoint, false);
    resetExpeditionGame();
}

function addExpeditionHistory(multiplier, extracted) {
    const history = document.getElementById('expeditionHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-sm font-bold ${
        extracted ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = multiplier.toFixed(2) + 'x';
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

function resetExpeditionGame() {
    setTimeout(() => {
        document.getElementById('startExpedition').disabled = false;
        document.getElementById('extractTeam').disabled = true;
        document.getElementById('expeditionStatus').textContent = 'Ready to explore the frozen tundra';
        document.getElementById('expeditionMultiplier').textContent = '1.00x';
        document.getElementById('artifactsFound').textContent = '0';
        document.getElementById('dangerLevel').textContent = 'Safe';
        document.getElementById('dangerLevel').className = 'text-lg font-bold text-green-400';
        
        // Reset mystical energy
        document.getElementById('mysticalEnergyBar').style.width = '0%';
        document.getElementById('mysticalEnergyText').textContent = '0%';
        
        // Reset team position
        const team = document.getElementById('expeditionTeam');
        team.style.top = '4%';
        team.style.left = '50%';
        
        // Clear expedition elements
        document.getElementById('artifacts').innerHTML = '';
        document.getElementById('hazards').innerHTML = '';
        document.getElementById('guardianEffect').style.opacity = '0';
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

