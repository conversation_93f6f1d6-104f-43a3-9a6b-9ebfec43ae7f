// Game state
const walletIntegration = new GameWalletIntegration('Dynasty Dice (Historic)');
let historicDynastyGame = {
    isRolling: false,
    emperorDice: [],
    playerDice: [],
    betAmount: 0,
    dynastyLevel: 1,
    dragonPower: 0,
    imperialFavor: 0,
    mandateOfHeaven: false,
    rollType: 'standard',
    ancientWisdom: 0,
    celestialBlessing: false
};



function loadHistoricDynastyDiceGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Imperial Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">🏮 HISTORIC DYNASTY DICE 🏮</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🪙 IMPERIAL TRIBUTE</label>
                        <input type="number" id="dynastyBet" value="10" min="1"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎲 ANCIENT RITUAL</label>
                        <select id="rollType" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Ritual (5 dice)</option>
                            <option value="imperial">Imperial Ceremony (7 dice)</option>
                            <option value="celestial">Celestial Rite (9 dice)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚔️ CHALLENGE TYPE</label>
                        <select id="challengeType" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="total">Highest Sum Prevails</option>
                            <option value="pairs">Most Matching Pairs</option>
                            <option value="sequence">Longest Sequence</option>
                            <option value="harmony">Perfect Harmony (even/odd)</option>
                        </select>
                    </div>
                    
                    <button id="rollDynastyDice" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🐉 CHALLENGE THE DRAGON EMPEROR 🐉
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🏯 Dynasty Level</div>
                        <div id="dynastyLevel" class="text-2xl font-bold text-red-400 neon-glow">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🐉 Dragon Power</div>
                        <div id="dragonPower" class="text-lg font-bold text-orange-400">0%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">👑 Imperial Favor</div>
                        <div id="imperialFavor" class="text-lg font-bold text-yellow-400">0%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">📜 Ancient Wisdom</div>
                        <div id="ancientWisdom" class="text-lg font-bold text-blue-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Imperial Court -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h5 class="text-lg font-bold mb-4 text-red-400 text-center">🏛️ FORBIDDEN CITY COURT 🏛️</h5>
                    
                    <!-- Emperor's Dice -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 flex items-center">
                            <span>🐲 DRAGON EMPEROR'S DICE</span>
                        </div>
                        <div id="emperorDice" class="flex justify-center flex-wrap gap-2 mb-2">
                            <!-- Dice will be generated here -->
                        </div>
                        <div id="emperorScore" class="text-center text-lg font-bold text-red-400">Score: 0</div>
                    </div>
                    
                    <!-- Challenge Indicator -->
                    <div class="text-center text-2xl font-bold text-yellow-400 mb-6">⚔️ 天命 TIANMING ⚔️</div>
                    
                    <!-- Player's Dice -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 flex items-center">
                            <span>🎲 YOUR HUMBLE DICE</span>
                        </div>
                        <div id="playerDice" class="flex justify-center flex-wrap gap-2 mb-2">
                            <!-- Dice will be generated here -->
                        </div>
                        <div id="playerScore" class="text-center text-lg font-bold text-green-400">Score: 0</div>
                    </div>
                    
                    <div id="dynastyResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="dynastyStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Imperial History -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">📚 IMPERIAL CHRONICLES</h5>
                    <div id="dynastyHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Mandate of Heaven -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌟 MANDATE OF HEAVEN 🌟</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="mandateBar" class="bg-gradient-to-r from-purple-500 to-yellow-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="mandateText" class="text-center mt-2 text-purple-300">Dormant</div>
                </div>
            </div>
        </div>
    `;
    
    initializeDynastyDice();
}

function initializeDynastyDice() {
    document.getElementById('rollDynastyDice').addEventListener('click', rollDynastyDice);
    document.getElementById('rollType').addEventListener('change', updateRollType);
    document.getElementById('challengeType').addEventListener('change', updateChallengeType);
    
    updateRollType();
}

function updateRollType() {
    const type = document.getElementById('rollType').value;
    historicDynastyGame.rollType = type;
}

function updateChallengeType() {
    // Update challenge type for scoring
}

async function rollDynastyDice() {
    const betAmount = parseInt(document.getElementById('dynastyBet').value);

    if (historicDynastyGame.isRolling) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    historicDynastyGame.betAmount = betAmount;
    historicDynastyGame.isRolling = true;
    
    // Disable roll button
    document.getElementById('rollDynastyDice').disabled = true;
    document.getElementById('dynastyResult').textContent = '';
    document.getElementById('dynastyStatus').textContent = 'The Dragon Emperor contemplates your challenge...';
    
    // Generate dice based on roll type
    const diceCount = getDynastyDiceCount();
    generateDynastyDiceDisplay(diceCount);
    
    // Animate dice rolling
    animateDynastyDiceRoll(diceCount);
}

function getDynastyDiceCount() {
    switch(historicDynastyGame.rollType) {
        case 'standard': return 5;
        case 'imperial': return 7;
        case 'celestial': return 9;
        default: return 5;
    }
}

function generateDynastyDiceDisplay(count) {
    const emperorDiceEl = document.getElementById('emperorDice');
    const playerDiceEl = document.getElementById('playerDice');
    
    emperorDiceEl.innerHTML = '';
    playerDiceEl.innerHTML = '';
    
    for (let i = 0; i < count; i++) {
        // Emperor dice - golden with dragon symbols
        const emperorDie = document.createElement('div');
        emperorDie.className = 'w-12 h-12 bg-gradient-to-br from-red-600 to-yellow-600 rounded-lg flex items-center justify-center text-white font-bold text-lg border-2 border-yellow-400 shadow-lg';
        emperorDie.textContent = '龍';
        emperorDiceEl.appendChild(emperorDie);
        
        // Player dice - simple wooden style
        const playerDie = document.createElement('div');
        playerDie.className = 'w-12 h-12 bg-gradient-to-br from-amber-800 to-amber-600 rounded-lg flex items-center justify-center text-white font-bold text-lg border-2 border-amber-400';
        playerDie.textContent = '?';
        playerDiceEl.appendChild(playerDie);
    }
}

function animateDynastyDiceRoll(diceCount) {
    const emperorDiceEls = document.querySelectorAll('#emperorDice > div');
    const playerDiceEls = document.querySelectorAll('#playerDice > div');
    
    let rollCount = 0;
    const rollInterval = setInterval(() => {
        // Animate emperor dice with Chinese numerals
        emperorDiceEls.forEach(die => {
            const chineseNums = ['一', '二', '三', '四', '五', '六'];
            die.textContent = chineseNums[Math.floor(Math.random() * 6)];
        });
        
        // Animate player dice
        playerDiceEls.forEach(die => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        });
        
        rollCount++;
        
        if (rollCount >= 30) {
            clearInterval(rollInterval);
            finalizeDynastyDiceRoll(diceCount);
        }
    }, 150);
}

function finalizeDynastyDiceRoll(diceCount) {
    // Generate final dice values with extreme bias toward Emperor (< 8% win rate)
    historicDynastyGame.emperorDice = [];
    historicDynastyGame.playerDice = [];
    
    // Emperor dice - extremely biased toward high values
    for (let i = 0; i < diceCount; i++) {
        // 90% chance of rolling 4-6, 10% chance of rolling 1-3
        const roll = Math.random() < 0.90 ? 
            Math.floor(Math.random() * 3) + 4 : 
            Math.floor(Math.random() * 3) + 1;
        historicDynastyGame.emperorDice.push(roll);
    }
    
    // Player dice - extremely biased toward low values
    for (let i = 0; i < diceCount; i++) {
        // 94% chance of rolling 1-3, 6% chance of rolling 4-6
        const roll = Math.random() < 0.94 ? 
            Math.floor(Math.random() * 3) + 1 : 
            Math.floor(Math.random() * 3) + 4;
        historicDynastyGame.playerDice.push(roll);
    }
    
    // Apply ancient effects
    applyAncientEffects();
    
    // Display final dice
    const emperorDiceEls = document.querySelectorAll('#emperorDice > div');
    const playerDiceEls = document.querySelectorAll('#playerDice > div');
    
    const chineseNums = ['一', '二', '三', '四', '五', '六'];
    emperorDiceEls.forEach((die, index) => {
        die.textContent = chineseNums[historicDynastyGame.emperorDice[index] - 1];
    });
    
    playerDiceEls.forEach((die, index) => {
        die.textContent = historicDynastyGame.playerDice[index];
    });
    
    // Calculate scores based on challenge type
    const challengeType = document.getElementById('challengeType').value;
    const emperorScore = calculateDynastyScore(historicDynastyGame.emperorDice, challengeType);
    const playerScore = calculateDynastyScore(historicDynastyGame.playerDice, challengeType);
    
    document.getElementById('emperorScore').textContent = `Score: ${emperorScore}`;
    document.getElementById('playerScore').textContent = `Score: ${playerScore}`;
    
    // Resolve challenge
    resolveDynastyChallenge(emperorScore, playerScore);
}

function applyAncientEffects() {
    // Dragon Power - minimal boost
    if (historicDynastyGame.dragonPower > 60) {
        const maxIndex = historicDynastyGame.playerDice.indexOf(Math.max(...historicDynastyGame.playerDice));
        if (historicDynastyGame.playerDice[maxIndex] < 6 && Math.random() < 0.3) {
            historicDynastyGame.playerDice[maxIndex] = Math.min(6, historicDynastyGame.playerDice[maxIndex] + 1);
        }
    }
    
    // Imperial Favor - rare reroll
    if (historicDynastyGame.imperialFavor > 80 && Math.random() < 0.25) {
        const minIndex = historicDynastyGame.playerDice.indexOf(Math.min(...historicDynastyGame.playerDice));
        historicDynastyGame.playerDice[minIndex] = Math.floor(Math.random() * 6) + 1;
    }
    
    // Celestial Blessing - very rare major boost
    if (historicDynastyGame.ancientWisdom >= 50 && Math.random() < 0.05) {
        historicDynastyGame.celestialBlessing = true;
        historicDynastyGame.playerDice = historicDynastyGame.playerDice.map(die => Math.min(6, die + 2));
    }
}

function calculateDynastyScore(dice, challengeType) {
    switch(challengeType) {
        case 'total':
            return dice.reduce((sum, die) => sum + die, 0);
        case 'pairs':
            return countPairs(dice);
        case 'sequence':
            return longestSequence(dice);
        case 'harmony':
            return calculateHarmony(dice);
        default:
            return dice.reduce((sum, die) => sum + die, 0);
    }
}

function countPairs(dice) {
    const counts = {};
    dice.forEach(die => counts[die] = (counts[die] || 0) + 1);
    return Object.values(counts).filter(count => count >= 2).length;
}

function longestSequence(dice) {
    const sorted = [...new Set(dice)].sort((a, b) => a - b);
    let maxLength = 1;
    let currentLength = 1;
    
    for (let i = 1; i < sorted.length; i++) {
        if (sorted[i] === sorted[i-1] + 1) {
            currentLength++;
            maxLength = Math.max(maxLength, currentLength);
        } else {
            currentLength = 1;
        }
    }
    
    return maxLength;
}

function calculateHarmony(dice) {
    const evenCount = dice.filter(die => die % 2 === 0).length;
    const oddCount = dice.length - evenCount;
    return Math.abs(evenCount - oddCount);
}

async function resolveDynastyChallenge(emperorScore, playerScore) {
    let won = false;
    let winnings = 0;

    if (playerScore > emperorScore) {
        won = true;
        historicDynastyGame.dynastyLevel++;
        historicDynastyGame.dragonPower = Math.min(100, historicDynastyGame.dragonPower + 15);
        historicDynastyGame.imperialFavor = Math.min(100, historicDynastyGame.imperialFavor + 12);
        historicDynastyGame.ancientWisdom += 3;

        // Calculate winnings with bonuses
        const levelMultiplier = 1 + (historicDynastyGame.dynastyLevel * 0.08);
        const celestialBonus = historicDynastyGame.celestialBlessing ? 3.0 : 1.0;
        const payout = Math.floor(historicDynastyGame.betAmount * levelMultiplier * celestialBonus * 1.8);
        winnings = payout - historicDynastyGame.betAmount;

        if (winnings > 0) {
            await walletIntegration.processWin(winnings);
        }

        document.getElementById('dynastyResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🎉 IMPERIAL VICTORY! 🎉</span>`;
        document.getElementById('dynastyStatus').innerHTML = 
            `The Dragon Emperor acknowledges your skill! Won ${payout} GA!`;
    } else {
        // Reset some progress on loss
        historicDynastyGame.dragonPower = Math.max(0, historicDynastyGame.dragonPower - 8);
        historicDynastyGame.imperialFavor = Math.max(0, historicDynastyGame.imperialFavor - 5);
        
        document.getElementById('dynastyResult').innerHTML = 
            `<span class="text-red-400">🐉 IMPERIAL DEFEAT! 🐉</span>`;
        document.getElementById('dynastyStatus').innerHTML = 
            `The Dragon Emperor's ancient wisdom prevails! Your challenge fails.`;
    }
    
    // Update displays
    document.getElementById('dynastyLevel').textContent = historicDynastyGame.dynastyLevel;
    document.getElementById('dragonPower').textContent = Math.floor(historicDynastyGame.dragonPower) + '%';
    document.getElementById('imperialFavor').textContent = Math.floor(historicDynastyGame.imperialFavor) + '%';
    document.getElementById('ancientWisdom').textContent = historicDynastyGame.ancientWisdom;
    
    // Update mandate of heaven
    const mandateProgress = (historicDynastyGame.dragonPower + historicDynastyGame.imperialFavor + historicDynastyGame.ancientWisdom) / 3;
    document.getElementById('mandateBar').style.width = Math.min(100, mandateProgress) + '%';
    document.getElementById('mandateText').textContent = 
        mandateProgress >= 95 ? 'AWAKENED' : mandateProgress >= 80 ? 'STIRRING' : 'DORMANT';
    
    // Reset celestial blessing
    if (historicDynastyGame.celestialBlessing) {
        historicDynastyGame.celestialBlessing = false;
    }
    
    // Add to history
    addDynastyHistory(playerScore, emperorScore, won);
    
    // Re-enable roll button
    setTimeout(() => {
        document.getElementById('rollDynastyDice').disabled = false;
        historicDynastyGame.isRolling = false;
    }, 3000);
}

function addDynastyHistory(playerScore, emperorScore, won) {
    const history = document.getElementById('dynastyHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-sm font-bold ${
        won ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = `${playerScore} vs ${emperorScore}`;
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 12) {
        history.removeChild(history.lastChild);
    }
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadHistoricDynastyDiceGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

