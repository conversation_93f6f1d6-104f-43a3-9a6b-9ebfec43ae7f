// Game state
let balance = 1000;
let moonMissionGame = {
    isFlying: false,
    betAmount: 0,
    targetMultiplier: 2.5,
    currentMultiplier: 1.0,
    maxMultiplier: 1.0,
    missionLevel: 1,
    lunarFuel: 100,
    gravityResistance: 0,
    moonPhase: 'new',
    trajectoryMode: 'direct',
    consecutiveMissions: 0,
    totalMissions: 0,
    crashLandings: 0,
    maxLunarDistance: 0,
    missionComplexity: 'simple',
    oxygenLevel: 100,
    heatShield: 'basic'
};

// Moon phases that affect difficulty
const MOON_PHASES = [
    { name: 'new', crashBonus: 0.87, description: 'New Moon (Easiest)' },
    { name: 'crescent', crashBonus: 0.90, description: 'Crescent Moon' },
    { name: 'quarter', crashBonus: 0.93, description: 'Quarter Moon' },
    { name: 'gibbous', crashBonus: 0.95, description: 'Gibbous Moon' },
    { name: 'full', crashBonus: 0.98, description: 'Full Moon (Hardest)' }
];

// Trajectory modes
const TRAJECTORY_MODES = [
    { name: 'direct', multiplier: 1.0, description: 'Direct Trajectory' },
    { name: 'orbital', multiplier: 1.2, description: 'Orbital Slingshot (1.2x harder)' },
    { name: 'gravity', multiplier: 1.5, description: 'Gravity Assist (1.5x harder)' },
    { name: 'hohmann', multiplier: 1.8, description: 'Hohmann Transfer (1.8x harder)' },
    { name: 'lagrange', multiplier: 2.3, description: 'Lagrange Point (2.3x harder)' }
];

// Mission complexity levels
const MISSION_COMPLEXITY = [
    { name: 'simple', multiplier: 1.0, description: 'Simple Flyby' },
    { name: 'orbit', multiplier: 1.3, description: 'Lunar Orbit (1.3x harder)' },
    { name: 'landing', multiplier: 1.7, description: 'Moon Landing (1.7x harder)' },
    { name: 'base', multiplier: 2.1, description: 'Lunar Base (2.1x harder)' },
    { name: 'colony', multiplier: 2.8, description: 'Moon Colony (2.8x harder)' }
];

// Heat shield types
const HEAT_SHIELDS = [
    { name: 'basic', protection: 0.85, description: 'Basic Heat Shield' },
    { name: 'ceramic', protection: 0.88, description: 'Ceramic Tiles' },
    { name: 'ablative', protection: 0.91, description: 'Ablative Shield' },
    { name: 'plasma', protection: 0.94, description: 'Plasma Shield' },
    { name: 'quantum', protection: 0.97, description: 'Quantum Field' }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Moon Mission Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h4 class="text-xl font-bold mb-4 text-gray-400">🌙 ROCKET REBORN: MOON MISSION 🌙</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 MISSION BUDGET</label>
                        <input type="number" id="moonBet" value="60" min="15" max="${balance}" 
                               class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET MULTIPLIER</label>
                        <input type="number" id="moonTargetMultiplier" value="2.5" min="1.2" max="100" step="0.1" 
                               class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌙 MOON PHASE</label>
                        <select id="moonPhase" class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="new">🌑 New Moon (Easiest)</option>
                            <option value="crescent">🌒 Crescent Moon</option>
                            <option value="quarter">🌓 Quarter Moon</option>
                            <option value="gibbous">🌔 Gibbous Moon</option>
                            <option value="full">🌕 Full Moon (Hardest)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🛰️ TRAJECTORY MODE</label>
                        <select id="trajectoryMode" class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="direct">Direct Trajectory</option>
                            <option value="orbital">Orbital Slingshot (1.2x harder)</option>
                            <option value="gravity">Gravity Assist (1.5x harder)</option>
                            <option value="hohmann">Hohmann Transfer (1.8x harder)</option>
                            <option value="lagrange">Lagrange Point (2.3x harder)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏗️ MISSION COMPLEXITY</label>
                        <select id="missionComplexity" class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="simple">Simple Flyby</option>
                            <option value="orbit">Lunar Orbit (1.3x harder)</option>
                            <option value="landing">Moon Landing (1.7x harder)</option>
                            <option value="base">Lunar Base (2.1x harder)</option>
                            <option value="colony">Moon Colony (2.8x harder)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🛡️ HEAT SHIELD</label>
                        <select id="heatShield" class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Heat Shield</option>
                            <option value="ceramic">Ceramic Tiles (Better)</option>
                            <option value="ablative">Ablative Shield (Good)</option>
                            <option value="plasma">Plasma Shield (Excellent)</option>
                            <option value="quantum">Quantum Field (Perfect)</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="launchMoonMission" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🚀 LAUNCH TO MOON
                        </button>
                        <button id="abortMission" class="bg-red-600 hover:bg-red-700 flex-1 py-3 rounded-lg font-bold text-white" disabled>
                            🚨 ABORT MISSION
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Mission Success Rate</div>
                        <div id="moonWinChance" class="text-lg font-bold text-red-400">4%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="moonPotentialPayout" class="text-2xl font-bold text-gray-400 neon-glow">150 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🌙 Mission Level</div>
                        <div id="missionLevelDisplay" class="text-lg font-bold text-blue-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⛽ Lunar Fuel</div>
                        <div id="lunarFuelDisplay" class="text-lg font-bold text-yellow-400">100%</div>
                    </div>
                </div>
            </div>
            
            <!-- Moon Mission Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h5 class="text-lg font-bold mb-4 text-gray-400 text-center">🌙 LUNAR MISSION CONTROL 🌙</h5>
                    
                    <!-- Moon Multiplier Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-gray-900/50 to-black/50 p-8 rounded-xl border border-gray-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🌟 Current Multiplier</div>
                                <div id="moonMultiplierDisplay" class="text-8xl font-bold text-gray-400 neon-glow mb-4">1.00x</div>
                                <div class="text-sm text-gray-400">Target: <span id="moonTargetDisplay" class="text-gray-400">2.50x</span></div>
                                <div class="text-sm text-gray-400">Max Distance: <span id="maxDistanceDisplay" class="text-green-400">0 km</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mission Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🛰️ Mission Status</div>
                            <div id="missionStatus" class="text-lg font-bold text-gray-400">Ready for lunar launch...</div>
                            <div class="text-xs text-gray-400">Phase: <span id="phaseStatus" class="text-blue-400">New Moon</span></div>
                            <div class="text-xs text-gray-400">Trajectory: <span id="trajectoryStatus" class="text-green-400">Direct</span></div>
                            <div class="text-xs text-gray-400">Complexity: <span id="complexityStatus" class="text-orange-400">Simple</span></div>
                            <div class="text-xs text-gray-400">Shield: <span id="shieldStatus" class="text-purple-400">Basic</span></div>
                        </div>
                    </div>
                    
                    <!-- Life Support Systems -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🧑‍🚀 Life Support Systems</div>
                            <div class="flex justify-between mb-1">
                                <span class="text-xs text-gray-400">Oxygen Level</span>
                                <span id="oxygenLevel" class="text-xs text-green-400">100%</span>
                            </div>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="oxygenBar" class="bg-green-500 h-2 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-xs text-gray-400">Gravity Resistance</span>
                                <span id="gravityDisplay" class="text-xs text-blue-400">0%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div id="moonResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="moonStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Mission Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-gray-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-gray-400">📊 LUNAR MISSION RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Successful Missions</div>
                            <div id="consecutiveMissions" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Missions</div>
                            <div id="totalMissions" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Crash Landings</div>
                            <div id="crashLandings" class="text-lg font-bold text-red-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Mission Success Rate</div>
                            <div id="missionSuccessRate" class="text-lg font-bold text-orange-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Lunar Technology Progress -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">🔬 LUNAR TECHNOLOGY 🔬</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="lunarTechProgress" class="bg-gradient-to-r from-gray-500 to-blue-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="lunarTechText" class="text-center mt-2 text-blue-300">Basic Lunar Technology</div>
                </div>
            </div>
        </div>
    `;
    
    initializeMoonMission();
}

function initializeMoonMission() {
    document.getElementById('launchMoonMission').addEventListener('click', launchMoonMission);
    document.getElementById('abortMission').addEventListener('click', abortMission);
    document.getElementById('moonTargetMultiplier').addEventListener('input', updateMoonPayout);
    document.getElementById('moonPhase').addEventListener('change', updateMoonPhase);
    document.getElementById('trajectoryMode').addEventListener('change', updateTrajectoryMode);
    document.getElementById('missionComplexity').addEventListener('change', updateMissionComplexity);
    document.getElementById('heatShield').addEventListener('change', updateHeatShield);
    
    updateMoonPhase();
    updateTrajectoryMode();
    updateMissionComplexity();
    updateHeatShield();
    updateMoonPayout();
}

function updateMoonPhase() {
    const phase = document.getElementById('moonPhase').value;
    moonMissionGame.moonPhase = phase;
    
    const phaseData = MOON_PHASES.find(p => p.name === phase);
    document.getElementById('phaseStatus').textContent = phaseData.description;
    
    updateMoonPayout();
}

function updateTrajectoryMode() {
    const mode = document.getElementById('trajectoryMode').value;
    moonMissionGame.trajectoryMode = mode;
    
    const trajectoryData = TRAJECTORY_MODES.find(t => t.name === mode);
    document.getElementById('trajectoryStatus').textContent = trajectoryData.description;
    
    updateMoonPayout();
}

function updateMissionComplexity() {
    const complexity = document.getElementById('missionComplexity').value;
    moonMissionGame.missionComplexity = complexity;
    
    const complexityData = MISSION_COMPLEXITY.find(c => c.name === complexity);
    document.getElementById('complexityStatus').textContent = complexityData.description;
    
    updateMoonPayout();
}

function updateHeatShield() {
    const shield = document.getElementById('heatShield').value;
    moonMissionGame.heatShield = shield;
    
    const shieldData = HEAT_SHIELDS.find(h => h.name === shield);
    document.getElementById('shieldStatus').textContent = shieldData.description;
    
    updateMoonPayout();
}

function updateMoonPayout() {
    const betAmount = parseInt(document.getElementById('moonBet').value) || 60;
    const targetMultiplier = parseFloat(document.getElementById('moonTargetMultiplier').value) || 2.5;
    
    moonMissionGame.targetMultiplier = targetMultiplier;
    document.getElementById('moonTargetDisplay').textContent = targetMultiplier.toFixed(2) + 'x';
    
    // Calculate extremely low success probability
    const phaseData = MOON_PHASES.find(p => p.name === moonMissionGame.moonPhase);
    const trajectoryData = TRAJECTORY_MODES.find(t => t.name === moonMissionGame.trajectoryMode);
    const complexityData = MISSION_COMPLEXITY.find(c => c.name === moonMissionGame.missionComplexity);
    const shieldData = HEAT_SHIELDS.find(h => h.name === moonMissionGame.heatShield);
    
    // Base crash probability increases with target multiplier and moon distance
    let baseCrashChance = 0.88 + (targetMultiplier - 1.0) * 0.015;
    
    // Apply all modifiers (make it extremely hard)
    const finalCrashChance = Math.min(0.995, 
        baseCrashChance * phaseData.crashBonus * trajectoryData.multiplier * 
        complexityData.multiplier / shieldData.protection);
    
    const successChance = Math.max(1, Math.floor((1 - finalCrashChance) * 100));
    const potentialPayout = Math.floor(betAmount * targetMultiplier);
    
    document.getElementById('moonWinChance').textContent = successChance + '%';
    document.getElementById('moonPotentialPayout').textContent = potentialPayout + ' GA';
}

function launchMoonMission() {
    const betAmount = parseInt(document.getElementById('moonBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient mission budget!');
        return;
    }
    
    if (moonMissionGame.isFlying) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    moonMissionGame.betAmount = betAmount;
    moonMissionGame.isFlying = true;
    moonMissionGame.currentMultiplier = 1.0;
    moonMissionGame.totalMissions++;
    moonMissionGame.oxygenLevel = 100;
    
    // Disable launch button, enable abort
    document.getElementById('launchMoonMission').disabled = true;
    document.getElementById('abortMission').disabled = false;
    
    document.getElementById('moonResult').textContent = '';
    document.getElementById('moonStatus').textContent = 'Launching lunar mission...';
    document.getElementById('missionStatus').textContent = 'ASCENDING TO MOON...';
    
    // Start moon mission simulation
    startMoonFlight();
}

function startMoonFlight() {
    const phaseData = MOON_PHASES.find(p => p.name === moonMissionGame.moonPhase);
    const trajectoryData = TRAJECTORY_MODES.find(t => t.name === moonMissionGame.trajectoryMode);
    const complexityData = MISSION_COMPLEXITY.find(c => c.name === moonMissionGame.missionComplexity);
    const shieldData = HEAT_SHIELDS.find(h => h.name === moonMissionGame.heatShield);
    
    const flightInterval = setInterval(() => {
        if (!moonMissionGame.isFlying) {
            clearInterval(flightInterval);
            return;
        }
        
        // Calculate crash probability (extremely high - 88-99.5% chance)
        const baseCrashChance = 0.88 + (moonMissionGame.targetMultiplier - 1.0) * 0.015;
        const finalCrashChance = Math.min(0.995, 
            baseCrashChance * phaseData.crashBonus * trajectoryData.multiplier * 
            complexityData.multiplier / shieldData.protection);
        
        // Increase crash chance as we get closer to moon
        const distanceCrashChance = finalCrashChance + (moonMissionGame.currentMultiplier - 1.0) * 0.008;
        
        // Decrease oxygen and increase crash chance
        moonMissionGame.oxygenLevel = Math.max(0, moonMissionGame.oxygenLevel - 0.5);
        if (moonMissionGame.oxygenLevel <= 20) {
            // Critical oxygen levels increase crash chance
            const oxygenCrashChance = distanceCrashChance + (20 - moonMissionGame.oxygenLevel) * 0.02;
            if (Math.random() < oxygenCrashChance) {
                crashMoonMission();
                clearInterval(flightInterval);
                return;
            }
        } else if (Math.random() < distanceCrashChance) {
            // Normal crash
            crashMoonMission();
            clearInterval(flightInterval);
            return;
        }
        
        // Increase multiplier slightly (slower than regular rocket)
        const increment = 0.008 + (Math.random() * 0.015);
        moonMissionGame.currentMultiplier += increment;
        moonMissionGame.maxLunarDistance = Math.max(moonMissionGame.maxLunarDistance, 
            Math.floor((moonMissionGame.currentMultiplier - 1.0) * 50000)); // Distance in km
        
        // Check if target reached (extremely rare)
        if (moonMissionGame.currentMultiplier >= moonMissionGame.targetMultiplier) {
            reachMoonTarget();
            clearInterval(flightInterval);
            return;
        }
        
        updateMoonDisplay();
        
    }, 120); // Slower updates for moon mission
}

function abortMission() {
    if (!moonMissionGame.isFlying) return;
    
    moonMissionGame.isFlying = false;
    moonMissionGame.consecutiveMissions++;
    moonMissionGame.missionLevel++;
    moonMissionGame.gravityResistance += 8;
    
    // Calculate winnings
    const winnings = Math.floor(moonMissionGame.betAmount * moonMissionGame.currentMultiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('moonResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🚀 MISSION ABORTED SAFELY! 🚀</span>`;
    document.getElementById('moonStatus').innerHTML = 
        `Returned at ${moonMissionGame.currentMultiplier.toFixed(2)}x! Won ${winnings} GA!`;
    
    document.getElementById('missionStatus').textContent = 'MISSION COMPLETED';
    
    resetMoonControls();
}

function crashMoonMission() {
    moonMissionGame.isFlying = false;
    moonMissionGame.crashLandings++;
    moonMissionGame.consecutiveMissions = 0;
    
    document.getElementById('moonResult').innerHTML = 
        `<span class="text-red-400">💥 LUNAR CRASH LANDING! 💥</span>`;
    
    let crashReason = 'Navigation failure';
    if (moonMissionGame.oxygenLevel <= 0) crashReason = 'Oxygen depletion';
    else if (moonMissionGame.currentMultiplier > 4.0) crashReason = 'Lunar gravity well';
    else if (moonMissionGame.trajectoryMode === 'lagrange' || moonMissionGame.trajectoryMode === 'hohmann') crashReason = 'Trajectory miscalculation';
    else if (moonMissionGame.missionComplexity === 'colony' || moonMissionGame.missionComplexity === 'base') crashReason = 'Complex mission failure';
    
    document.getElementById('moonStatus').textContent = 
        `Crashed at ${moonMissionGame.currentMultiplier.toFixed(2)}x due to ${crashReason}!`;
    
    document.getElementById('missionStatus').textContent = 'MISSION FAILED';
    
    resetMoonControls();
}

function reachMoonTarget() {
    moonMissionGame.isFlying = false;
    moonMissionGame.consecutiveMissions++;
    moonMissionGame.missionLevel += 3;
    moonMissionGame.gravityResistance += 15;
    
    // Calculate winnings
    const winnings = Math.floor(moonMissionGame.betAmount * moonMissionGame.targetMultiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('moonResult').innerHTML = 
        `<span class="text-gold-400 neon-glow">🌙 LUNAR TARGET ACHIEVED! 🌙</span>`;
    document.getElementById('moonStatus').innerHTML = 
        `Reached ${moonMissionGame.targetMultiplier.toFixed(2)}x lunar target! Won ${winnings} GA!`;
    
    document.getElementById('missionStatus').textContent = 'LUNAR SUCCESS';
    
    resetMoonControls();
}

function resetMoonControls() {
    setTimeout(() => {
        document.getElementById('launchMoonMission').disabled = false;
        document.getElementById('abortMission').disabled = true;
        document.getElementById('missionStatus').textContent = 'Ready for lunar launch...';
        moonMissionGame.oxygenLevel = 100;
        updateMoonDisplay();
    }, 3000);
}

function updateMoonDisplay() {
    document.getElementById('moonMultiplierDisplay').textContent = moonMissionGame.currentMultiplier.toFixed(2) + 'x';
    document.getElementById('maxDistanceDisplay').textContent = moonMissionGame.maxLunarDistance.toLocaleString() + ' km';
    document.getElementById('missionLevelDisplay').textContent = moonMissionGame.missionLevel;
    document.getElementById('lunarFuelDisplay').textContent = moonMissionGame.lunarFuel + '%';
    document.getElementById('consecutiveMissions').textContent = moonMissionGame.consecutiveMissions;
    document.getElementById('totalMissions').textContent = moonMissionGame.totalMissions;
    document.getElementById('crashLandings').textContent = moonMissionGame.crashLandings;
    document.getElementById('gravityDisplay').textContent = moonMissionGame.gravityResistance + '%';
    
    // Update oxygen level
    document.getElementById('oxygenLevel').textContent = Math.floor(moonMissionGame.oxygenLevel) + '%';
    document.getElementById('oxygenBar').style.width = moonMissionGame.oxygenLevel + '%';
    
    // Change oxygen bar color based on level
    const oxygenBar = document.getElementById('oxygenBar');
    if (moonMissionGame.oxygenLevel > 60) {
        oxygenBar.className = 'bg-green-500 h-2 rounded-full transition-all duration-300';
    } else if (moonMissionGame.oxygenLevel > 30) {
        oxygenBar.className = 'bg-yellow-500 h-2 rounded-full transition-all duration-300';
    } else {
        oxygenBar.className = 'bg-red-500 h-2 rounded-full transition-all duration-300';
    }
    
    // Calculate success rate
    const successRate = moonMissionGame.totalMissions > 0 ? 
        Math.floor((moonMissionGame.consecutiveMissions / moonMissionGame.totalMissions) * 100) : 0;
    document.getElementById('missionSuccessRate').textContent = successRate + '%';
    
    // Update lunar technology progress
    const progress = Math.min(100, (moonMissionGame.gravityResistance / 300) * 100);
    document.getElementById('lunarTechProgress').style.width = progress + '%';
    
    let techLevel = 'Basic Lunar Technology';
    if (moonMissionGame.gravityResistance > 250) techLevel = 'Advanced Lunar Colony Tech';
    else if (moonMissionGame.gravityResistance > 200) techLevel = 'Lunar Base Technology';
    else if (moonMissionGame.gravityResistance > 150) techLevel = 'Lunar Landing Systems';
    else if (moonMissionGame.gravityResistance > 100) techLevel = 'Lunar Orbit Technology';
    else if (moonMissionGame.gravityResistance > 50) techLevel = 'Improved Lunar Tech';
    
    document.getElementById('lunarTechText').textContent = techLevel;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

