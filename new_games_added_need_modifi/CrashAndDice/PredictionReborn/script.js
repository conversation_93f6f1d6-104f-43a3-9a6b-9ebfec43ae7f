// Game state
const walletIntegration = new GameWalletIntegration('PredictionReborn');
let predictionRebornGame = {
    isPlaying: false,
    betAmount: 0,
    prediction: 'under',
    targetNumber: 50,
    rebornLevel: 1,
    soulPower: 0,
    karmaPoints: 0,
    reincarnationCycle: 1,
    spiritualAwakening: false,
    destinyMode: 'mortal',
    consecutiveWins: 0,
    totalRebirths: 0,
    enlightenmentStreak: 0,
    chakraAlignment: 'neutral'
};

// Destiny modes that affect difficulty
const DESTINY_MODES = [
    { name: 'mortal', bias: 0.91, description: 'Mortal Realm' },
    { name: 'spiritual', bias: 0.93, description: 'Spiritual Plane' },
    { name: 'celestial', bias: 0.95, description: 'Celestial Sphere' },
    { name: 'divine', bias: 0.97, description: 'Divine Realm' },
    { name: 'transcendent', bias: 0.99, description: 'Transcendent State' }
];

// Chakra alignments that affect outcomes
const CHAKRA_ALIGNMENTS = [
    { name: 'neutral', modifier: 1.0, description: 'Balanced Chakras' },
    { name: 'blocked', modifier: 1.3, description: 'Blocked Energy' },
    { name: 'corrupted', modifier: 1.6, description: 'Corrupted Chakras' },
    { name: 'shattered', modifier: 2.2, description: 'Shattered Soul' }
];



function loadPredictionRebornGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Reborn Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">🌟 PREDICTION REBORN 🌟</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 SOUL OFFERING</label>
                        <input type="number" id="rebornBet" value="40" min="10"
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 DESTINY PREDICTION</label>
                        <select id="rebornPrediction" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="under">🔻 Descending Path (Roll Under)</option>
                            <option value="over">🔺 Ascending Path (Roll Over)</option>
                            <option value="exact">🎯 Perfect Karma (Exact Number)</option>
                            <option value="range">📊 Soul Range (Within 5 numbers)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🔢 KARMIC TARGET</label>
                        <input type="range" id="rebornTarget" min="1" max="99" value="50" 
                               class="w-full accent-cyan-500">
                        <div class="flex justify-between text-sm text-gray-400">
                            <span>1</span>
                            <span id="rebornTargetValue" class="text-cyan-400 font-bold">50</span>
                            <span>99</span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌌 DESTINY MODE</label>
                        <select id="destinyMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="mortal">Mortal Realm</option>
                            <option value="spiritual">Spiritual Plane (Harder)</option>
                            <option value="celestial">Celestial Sphere (Extreme)</option>
                            <option value="divine">Divine Realm (Nearly Impossible)</option>
                            <option value="transcendent">Transcendent State (Impossible)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🧘 CHAKRA ALIGNMENT</label>
                        <select id="chakraAlignment" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="neutral">Balanced Chakras</option>
                            <option value="blocked">Blocked Energy (1.3x harder)</option>
                            <option value="corrupted">Corrupted Chakras (1.6x harder)</option>
                            <option value="shattered">Shattered Soul (2.2x harder)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🔄 REINCARNATION CYCLE</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="spiritualAwakening" class="mr-2">
                                <span class="text-sm">Spiritual Awakening (Higher stakes)</span>
                            </label>
                        </div>
                    </div>
                    
                    <button id="seekRebirth" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        🌟 SEEK REBIRTH 🌟
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Karmic Chance</div>
                        <div id="rebornWinChance" class="text-lg font-bold text-red-400">7%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Soul Multiplier</div>
                        <div id="rebornMultiplier" class="text-2xl font-bold text-cyan-400 neon-glow">14.29x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🌟 Reborn Level</div>
                        <div id="rebornLevelDisplay" class="text-lg font-bold text-blue-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⚡ Soul Power</div>
                        <div id="soulPowerDisplay" class="text-lg font-bold text-yellow-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Reborn Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h5 class="text-lg font-bold mb-4 text-cyan-400 text-center">🌟 CHAMBER OF REBIRTH 🌟</h5>
                    
                    <!-- Reborn Dice Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-cyan-900/50 to-black/50 p-8 rounded-xl border border-cyan-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🎲 Karmic Dice Roll</div>
                                <div id="rebornDiceDisplay" class="text-8xl font-bold text-cyan-400 neon-glow mb-4">?</div>
                                <div class="text-sm text-gray-400">Target: <span id="rebornTargetDisplay" class="text-cyan-400">50</span></div>
                                <div class="text-sm text-gray-400">Path: <span id="rebornPredictionDisplay" class="text-blue-400">Descending</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Soul Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🧘 Soul Status</div>
                            <div id="soulStatus" class="text-lg font-bold text-cyan-400">Awaiting rebirth...</div>
                            <div class="text-xs text-gray-400">Destiny: <span id="destinyState" class="text-blue-400">Mortal</span></div>
                            <div class="text-xs text-gray-400">Chakras: <span id="chakraState" class="text-green-400">Balanced</span></div>
                            <div class="text-xs text-gray-400">Karma: <span id="karmaDisplay" class="text-purple-400">0</span></div>
                        </div>
                    </div>
                    
                    <div id="rebornResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="rebornStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Reincarnation Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">📊 REINCARNATION RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Consecutive Wins</div>
                            <div id="consecutiveRebornWins" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Enlightenment Streak</div>
                            <div id="enlightenmentStreakDisplay" class="text-lg font-bold text-orange-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Rebirths</div>
                            <div id="totalRebirthsDisplay" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Ascension Rate</div>
                            <div id="ascensionRate" class="text-lg font-bold text-red-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Spiritual Progress -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">🌌 SPIRITUAL PROGRESS 🌌</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="spiritualProgress" class="bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="spiritualText" class="text-center mt-2 text-purple-300">Mortal Soul</div>
                </div>
                
                <!-- Reincarnation Cycle -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">🔄 REINCARNATION CYCLE</h5>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-400" id="reincarnationCycleDisplay">1</div>
                        <div class="text-sm text-gray-400">Current Cycle</div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    initializePredictionReborn();
}

function initializePredictionReborn() {
    document.getElementById('seekRebirth').addEventListener('click', seekRebirth);
    document.getElementById('rebornTarget').addEventListener('input', updateRebornCalculations);
    document.getElementById('rebornPrediction').addEventListener('change', updateRebornCalculations);
    document.getElementById('destinyMode').addEventListener('change', updateDestinyMode);
    document.getElementById('chakraAlignment').addEventListener('change', updateChakraAlignment);
    document.getElementById('spiritualAwakening').addEventListener('change', updateSpiritualAwakening);
    
    updateRebornCalculations();
    updateDestinyMode();
    updateChakraAlignment();
}

function updateRebornCalculations() {
    const target = parseInt(document.getElementById('rebornTarget').value);
    const prediction = document.getElementById('rebornPrediction').value;
    
    document.getElementById('rebornTargetValue').textContent = target;
    document.getElementById('rebornTargetDisplay').textContent = target;
    
    let predictionText = '';
    switch(prediction) {
        case 'under': predictionText = 'Descending'; break;
        case 'over': predictionText = 'Ascending'; break;
        case 'exact': predictionText = 'Perfect'; break;
        case 'range': predictionText = 'Range'; break;
    }
    document.getElementById('rebornPredictionDisplay').textContent = predictionText;
    
    predictionRebornGame.targetNumber = target;
    predictionRebornGame.prediction = prediction;
    
    // Calculate extremely low win chances
    let baseWinChance;
    if (prediction === 'under') {
        baseWinChance = Math.max(1, target - 1);
    } else if (prediction === 'over') {
        baseWinChance = Math.max(1, 100 - target);
    } else if (prediction === 'exact') {
        baseWinChance = 1; // Only 1% chance for exact
    } else { // range
        baseWinChance = Math.max(1, Math.min(10, target <= 5 ? target + 4 : target >= 95 ? 104 - target : 10));
    }
    
    // Apply destiny mode bias and chakra modifier
    const destinyData = DESTINY_MODES.find(d => d.name === predictionRebornGame.destinyMode);
    const chakraData = CHAKRA_ALIGNMENTS.find(c => c.name === predictionRebornGame.chakraAlignment);
    
    // Reduce win chance dramatically
    const finalWinChance = Math.max(1, Math.floor(baseWinChance * (1 - destinyData.bias) / chakraData.modifier));
    
    // Calculate multiplier (higher for lower chances)
    const multiplier = Math.max(1.5, 95 / finalWinChance);
    
    document.getElementById('rebornWinChance').textContent = finalWinChance + '%';
    document.getElementById('rebornMultiplier').textContent = multiplier.toFixed(2) + 'x';
}

function updateDestinyMode() {
    const mode = document.getElementById('destinyMode').value;
    predictionRebornGame.destinyMode = mode;
    
    const destinyData = DESTINY_MODES.find(d => d.name === mode);
    document.getElementById('destinyState').textContent = destinyData.description;
    
    updateRebornCalculations();
}

function updateChakraAlignment() {
    const alignment = document.getElementById('chakraAlignment').value;
    predictionRebornGame.chakraAlignment = alignment;
    
    const chakraData = CHAKRA_ALIGNMENTS.find(c => c.name === alignment);
    document.getElementById('chakraState').textContent = chakraData.description;
    
    updateRebornCalculations();
}

function updateSpiritualAwakening() {
    predictionRebornGame.spiritualAwakening = document.getElementById('spiritualAwakening').checked;
}

async function seekRebirth() {
    const betAmount = parseInt(document.getElementById('rebornBet').value);

    if (predictionRebornGame.isPlaying) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    predictionRebornGame.betAmount = betAmount;
    predictionRebornGame.isPlaying = true;
    predictionRebornGame.totalRebirths++;
    
    // Disable button during roll
    document.getElementById('seekRebirth').disabled = true;
    
    // Animate dice roll
    let rollCount = 0;
    const rollAnimation = setInterval(() => {
        const randomRoll = Math.floor(Math.random() * 100) + 1;
        document.getElementById('rebornDiceDisplay').textContent = randomRoll;
        rollCount++;
        
        if (rollCount >= 25) {
            clearInterval(rollAnimation);
            performRebirthReading();
        }
    }, 80);
}

async function performRebirthReading() {
    const target = predictionRebornGame.targetNumber;
    const prediction = predictionRebornGame.prediction;
    const destinyData = DESTINY_MODES.find(d => d.name === predictionRebornGame.destinyMode);
    const chakraData = CHAKRA_ALIGNMENTS.find(c => c.name === predictionRebornGame.chakraAlignment);
    
    // Generate extremely biased roll result
    let roll;
    const biasChance = destinyData.bias * chakraData.modifier;
    
    // Apply spiritual awakening penalty
    const finalBias = predictionRebornGame.spiritualAwakening ? 
        Math.min(0.995, biasChance * 1.1) : biasChance;
    
    if (Math.random() < finalBias) {
        // Generate losing roll (91-99.5% of the time)
        if (prediction === 'under') {
            roll = Math.floor(Math.random() * (100 - target + 1)) + target;
        } else if (prediction === 'over') {
            roll = Math.floor(Math.random() * target) + 1;
        } else if (prediction === 'exact') {
            do {
                roll = Math.floor(Math.random() * 100) + 1;
            } while (roll === target);
        } else { // range
            const rangeStart = Math.max(1, target - 5);
            const rangeEnd = Math.min(100, target + 5);
            do {
                roll = Math.floor(Math.random() * 100) + 1;
            } while (roll >= rangeStart && roll <= rangeEnd);
        }
    } else {
        // Generate winning roll (0.5-9% of the time)
        if (prediction === 'under') {
            roll = Math.floor(Math.random() * (target - 1)) + 1;
        } else if (prediction === 'over') {
            roll = Math.floor(Math.random() * (100 - target)) + target + 1;
        } else if (prediction === 'exact') {
            roll = target;
        } else { // range
            const rangeStart = Math.max(1, target - 5);
            const rangeEnd = Math.min(100, target + 5);
            roll = Math.floor(Math.random() * (rangeEnd - rangeStart + 1)) + rangeStart;
        }
    }
    
    // Display final roll
    document.getElementById('rebornDiceDisplay').textContent = roll;
    
    // Check win condition
    let won = false;
    if (prediction === 'under' && roll < target) won = true;
    if (prediction === 'over' && roll > target) won = true;
    if (prediction === 'exact' && roll === target) won = true;
    if (prediction === 'range' && Math.abs(roll - target) <= 5) won = true;
    
    // Process result
    if (won) {
        predictionRebornGame.consecutiveWins++;
        predictionRebornGame.enlightenmentStreak++;
        predictionRebornGame.rebornLevel++;
        predictionRebornGame.soulPower += 30;
        predictionRebornGame.karmaPoints += 15;
        predictionRebornGame.reincarnationCycle++;
        
        // Calculate winnings
        let baseWinChance;
        if (prediction === 'under') {
            baseWinChance = Math.max(1, target - 1);
        } else if (prediction === 'over') {
            baseWinChance = Math.max(1, 100 - target);
        } else if (prediction === 'exact') {
            baseWinChance = 1;
        } else {
            baseWinChance = Math.max(1, Math.min(10, target <= 5 ? target + 4 : target >= 95 ? 104 - target : 10));
        }
        
        const finalWinChance = Math.max(1, Math.floor(baseWinChance * (1 - destinyData.bias) / chakraData.modifier));
        const multiplier = Math.max(1.5, 95 / finalWinChance) * (predictionRebornGame.spiritualAwakening ? 1.5 : 1);
        const winnings = Math.floor(predictionRebornGame.betAmount * multiplier);
        const netWinnings = winnings - predictionRebornGame.betAmount;

        if (netWinnings > 0) {
            await walletIntegration.processWin(netWinnings);
        }
        
        document.getElementById('rebornResult').innerHTML = 
            `<span class="text-green-400 neon-glow">🌟 REBIRTH ACHIEVED! 🌟</span>`;
        document.getElementById('rebornStatus').innerHTML = 
            `Your soul ascends! Won ${winnings} GA with ${multiplier.toFixed(2)}x multiplier!`;
        
        document.getElementById('soulStatus').textContent = 'Soul energy harmonized...';
        document.getElementById('chakraState').textContent = 'Aligned';
        
    } else {
        predictionRebornGame.consecutiveWins = 0;
        
        document.getElementById('rebornResult').innerHTML = 
            `<span class="text-red-400">💀 KARMIC DEBT! 💀</span>`;
        document.getElementById('rebornStatus').innerHTML = 
            `Your soul remains bound. Lost ${predictionRebornGame.betAmount} GA.`;
        
        document.getElementById('soulStatus').textContent = 'Soul trapped in suffering...';
        document.getElementById('chakraState').textContent = 'Disrupted';
    }
    
    updateRebornDisplay();
    
    // Re-enable button
    setTimeout(() => {
        document.getElementById('seekRebirth').disabled = false;
        predictionRebornGame.isPlaying = false;
    }, 3000);
}

function updateRebornDisplay() {
    document.getElementById('rebornLevelDisplay').textContent = predictionRebornGame.rebornLevel;
    document.getElementById('soulPowerDisplay').textContent = predictionRebornGame.soulPower;
    document.getElementById('consecutiveRebornWins').textContent = predictionRebornGame.consecutiveWins;
    document.getElementById('enlightenmentStreakDisplay').textContent = predictionRebornGame.enlightenmentStreak;
    document.getElementById('totalRebirthsDisplay').textContent = predictionRebornGame.totalRebirths;
    document.getElementById('karmaDisplay').textContent = predictionRebornGame.karmaPoints;
    document.getElementById('reincarnationCycleDisplay').textContent = predictionRebornGame.reincarnationCycle;
    
    // Calculate ascension rate
    const ascensionRate = predictionRebornGame.totalRebirths > 0 ? 
        Math.floor((predictionRebornGame.enlightenmentStreak / predictionRebornGame.totalRebirths) * 100) : 0;
    document.getElementById('ascensionRate').textContent = ascensionRate + '%';
    
    // Update spiritual progress
    const progress = Math.min(100, (predictionRebornGame.soulPower / 600) * 100);
    document.getElementById('spiritualProgress').style.width = progress + '%';
    
    let spiritualLevel = 'Mortal Soul';
    if (predictionRebornGame.soulPower > 500) spiritualLevel = 'Transcendent Being';
    else if (predictionRebornGame.soulPower > 400) spiritualLevel = 'Enlightened Master';
    else if (predictionRebornGame.soulPower > 300) spiritualLevel = 'Spiritual Adept';
    else if (predictionRebornGame.soulPower > 200) spiritualLevel = 'Awakened Soul';
    else if (predictionRebornGame.soulPower > 100) spiritualLevel = 'Seeking Spirit';
    
    document.getElementById('spiritualText').textContent = spiritualLevel;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadPredictionRebornGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});
