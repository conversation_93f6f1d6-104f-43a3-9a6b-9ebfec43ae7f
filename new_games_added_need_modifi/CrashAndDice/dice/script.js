// Game state
const walletIntegration = new GameWalletIntegration('Dice');



function loadDiceGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Game Controls -->
                    <div>
                        <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                            <h4 class="text-xl font-bold mb-4 text-blue-400">PREDICTION SETTINGS</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="diceBet" value="10" min="1"
                                       class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">PREDICTION</label>
                                <select id="dicePrediction" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="under">Roll Under</option>
                                    <option value="over">Roll Over</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">TARGET NUMBER</label>
                                <input type="range" id="diceTarget" min="1" max="99" value="50" 
                                       class="w-full accent-blue-500">
                                <div class="flex justify-between text-sm text-gray-400">
                                    <span>1</span>
                                    <span id="diceTargetValue">50</span>
                                    <span>99</span>
                                </div>
                            </div>
                            
                            <div class="mb-4 text-center">
                                <div class="text-sm text-gray-400 mb-1">Win Chance</div>
                                <div id="diceWinChance" class="text-xl font-bold text-blue-400 neon-glow">49%</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Multiplier</div>
                                <div id="diceMultiplier" class="text-xl font-bold text-green-400 neon-glow">2.00x</div>
                            </div>
                            
                            <button id="rollDice" class="cyber-button w-full py-3 rounded-lg font-bold text-white">
                                ROLL DICE
                            </button>
                        </div>
                    </div>
                    
                    <!-- Dice Display -->
                    <div>
                        <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30 text-center">
                            <div id="diceDisplay" class="w-32 h-32 mx-auto mb-6 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center text-6xl font-bold border-4 border-blue-400 neon-border">
                                ?
                            </div>
                            <div id="diceResult" class="text-2xl font-bold mb-4"></div>
                            <div id="diceStatus" class="text-lg"></div>
                        </div>
                        
                        <!-- Recent Rolls -->
                        <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-blue-400">RECENT ROLLS</h5>
                            <div id="recentRolls" class="flex flex-wrap gap-2">
                                <!-- Recent rolls will appear here -->
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeDice();
        }
        
        function initializeDice() {
            const targetSlider = document.getElementById('diceTarget');
            const targetValue = document.getElementById('diceTargetValue');
            const predictionSelect = document.getElementById('dicePrediction');
            
            function updateDiceCalculations() {
                const target = parseInt(targetSlider.value);
                const prediction = predictionSelect.value;
                
                targetValue.textContent = target;
                
                let winChance;
                if (prediction === 'under') {
                    winChance = target - 1;
                } else {
                    winChance = 100 - target;
                }
                
                const multiplier = Math.max(1.01, 98 / winChance);
                
                document.getElementById('diceWinChance').textContent = winChance + '%';
                document.getElementById('diceMultiplier').textContent = multiplier.toFixed(2) + 'x';
            }
            
            targetSlider.addEventListener('input', updateDiceCalculations);
            predictionSelect.addEventListener('change', updateDiceCalculations);
            
            document.getElementById('rollDice').addEventListener('click', rollDice);
            
            updateDiceCalculations();
        }
        
        async function rollDice() {
            const betAmount = parseInt(document.getElementById('diceBet').value);
            const target = parseInt(document.getElementById('diceTarget').value);
            const prediction = document.getElementById('dicePrediction').value;

            const transaction = await walletIntegration.processBet(betAmount);
            if (!transaction.success) {
                return;
            }

            // Animate dice roll
            const diceDisplay = document.getElementById('diceDisplay');
            let rollAnimation = setInterval(() => {
                diceDisplay.textContent = Math.floor(Math.random() * 100) + 1;
            }, 100);

            setTimeout(async () => {
                clearInterval(rollAnimation);

                // Final roll
                const roll = Math.floor(Math.random() * 100) + 1;
                diceDisplay.textContent = roll;

                // Check win condition
                let won = false;
                if (prediction === 'under' && roll < target) won = true;
                if (prediction === 'over' && roll > target) won = true;

                // Calculate winnings
                let winChance;
                if (prediction === 'under') {
                    winChance = target - 1;
                } else {
                    winChance = 100 - target;
                }
                const multiplier = Math.max(1.01, 98 / winChance);

                if (won) {
                    const payout = Math.floor(betAmount * multiplier);
                    const winnings = payout - betAmount;
                    if (winnings > 0) {
                        await walletIntegration.processWin(winnings);
                    }

                    document.getElementById('diceResult').innerHTML = 
                        `<span class="text-green-400 neon-glow">WIN!</span>`;
                    document.getElementById('diceStatus').innerHTML = 
                        `You won ${payout} GA!`;
                } else {
                    document.getElementById('diceResult').innerHTML = 
                        `<span class="text-red-400">LOSE!</span>`;
                    document.getElementById('diceStatus').innerHTML = 
                        `Better luck next time!`;
                }

                // Add to recent rolls
                addRecentRoll(roll, won);

            }, 2000);
        }
        
        function addRecentRoll(roll, won) {
            const recentRolls = document.getElementById('recentRolls');
            const rollElement = document.createElement('div');
            rollElement.className = `w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                won ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
            }`;
            rollElement.textContent = roll;
            
            recentRolls.insertBefore(rollElement, recentRolls.firstChild);
            
            // Keep only last 10 rolls
            while (recentRolls.children.length > 10) {
                recentRolls.removeChild(recentRolls.lastChild);
            }
        }
        


// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadDiceGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});