// Game state
let balance = 1000;
let hiloRoyalGame = {
    isPlaying: false,
    currentCard: null,
    nextCard: null,
    deck: [],
    betAmount: 0,
    streak: 0,
    royalPoints: 0,
    flushBonus: 0,
    gameMode: 'classic',
    cardHistory: [],
    multiplier: 1.0
};

// Card suits and values
const SUITS = ['♠', '♥', '♦', '♣'];
const VALUES = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
const ROYAL_CARDS = ['10', 'J', 'Q', 'K', 'A'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Game Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">♠ HILO: ROYAL FLUSH ♠</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 BET AMOUNT</label>
                        <input type="number" id="hiloBet" value="10" min="1" max="${balance}" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎮 GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic">Classic HiLo</option>
                            <option value="royal">Royal Mode</option>
                            <option value="flush">Flush Hunter</option>
                        </select>
                    </div>
                    
                    <button id="startHilo" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        👑 START ROYAL GAME 👑
                    </button>
                    
                    <div class="grid grid-cols-2 gap-2 mb-4">
                        <button id="guessHigher" class="bg-green-600/30 hover:bg-green-600/50 py-2 rounded font-bold text-white" disabled>
                            📈 HIGHER
                        </button>
                        <button id="guessLower" class="bg-red-600/30 hover:bg-red-600/50 py-2 rounded font-bold text-white" disabled>
                            📉 LOWER
                        </button>
                    </div>
                    
                    <button id="cashOut" class="bg-yellow-600/30 hover:bg-yellow-600/50 w-full py-2 rounded font-bold text-white mb-4" disabled>
                        💎 CASH OUT
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🔥 Streak</div>
                        <div id="streakCount" class="text-2xl font-bold text-orange-400 neon-glow">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">👑 Royal Points</div>
                        <div id="royalPoints" class="text-lg font-bold text-purple-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Multiplier</div>
                        <div id="multiplier" class="text-lg font-bold text-blue-400">1.00x</div>
                    </div>
                </div>
            </div>
            
            <!-- Game Area -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400 text-center">🃏 ROYAL COURT 🃏</h5>
                    
                    <!-- Current Card -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 text-center">CURRENT CARD</div>
                        <div id="currentCard" class="mx-auto w-24 h-36 bg-white rounded-lg flex items-center justify-center text-4xl font-bold border-2 border-purple-400 shadow-lg">
                            🃏
                        </div>
                        <div id="cardValue" class="text-center mt-2 text-lg font-bold text-white">Ready to Play</div>
                    </div>
                    
                    <!-- Next Card Hint -->
                    <div class="mb-6">
                        <div class="text-sm text-gray-400 mb-2 text-center">NEXT CARD PREVIEW</div>
                        <div id="nextCardHint" class="text-center text-gray-500">Make your guess...</div>
                    </div>
                    
                    <div id="hiloResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="hiloStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Card History -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📚 CARD HISTORY</h5>
                    <div id="cardHistory" class="flex flex-wrap gap-2"></div>
                </div>
                
                <!-- Royal Flush Progress -->
                <div class="bg-black/30 p-4 rounded-xl border border-gold-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">👑 ROYAL FLUSH PROGRESS 👑</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="flushBar" class="bg-gradient-to-r from-purple-500 to-yellow-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="flushText" class="text-center mt-2 text-yellow-300">No Royal Cards</div>
                </div>
            </div>
        </div>
    `;
    
    initializeHiloRoyal();
}

function initializeHiloRoyal() {
    document.getElementById('startHilo').addEventListener('click', startHiloGame);
    document.getElementById('guessHigher').addEventListener('click', () => makeGuess('higher'));
    document.getElementById('guessLower').addEventListener('click', () => makeGuess('lower'));
    document.getElementById('cashOut').addEventListener('click', cashOutGame);
    document.getElementById('gameMode').addEventListener('change', updateGameMode);
    
    initializeDeck();
    updateGameMode();
}

function updateGameMode() {
    const mode = document.getElementById('gameMode').value;
    hiloRoyalGame.gameMode = mode;
    
    // Update multiplier based on mode
    switch(mode) {
        case 'classic': hiloRoyalGame.multiplier = 1.0; break;
        case 'royal': hiloRoyalGame.multiplier = 1.3; break;
        case 'flush': hiloRoyalGame.multiplier = 1.5; break;
    }
    
    document.getElementById('multiplier').textContent = hiloRoyalGame.multiplier.toFixed(2) + 'x';
}

function initializeDeck() {
    hiloRoyalGame.deck = [];
    for (let suit of SUITS) {
        for (let value of VALUES) {
            hiloRoyalGame.deck.push({ suit, value, numValue: getCardNumValue(value) });
        }
    }
    shuffleDeck();
}

function getCardNumValue(value) {
    if (value === 'A') return 1;
    if (value === 'J') return 11;
    if (value === 'Q') return 12;
    if (value === 'K') return 13;
    return parseInt(value);
}

function shuffleDeck() {
    for (let i = hiloRoyalGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [hiloRoyalGame.deck[i], hiloRoyalGame.deck[j]] = [hiloRoyalGame.deck[j], hiloRoyalGame.deck[i]];
    }
}

function startHiloGame() {
    const betAmount = parseInt(document.getElementById('hiloBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    if (hiloRoyalGame.isPlaying) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    hiloRoyalGame.betAmount = betAmount;
    hiloRoyalGame.isPlaying = true;
    hiloRoyalGame.streak = 0;
    hiloRoyalGame.cardHistory = [];
    
    // Reset deck if needed
    if (hiloRoyalGame.deck.length < 10) {
        initializeDeck();
    }
    
    // Draw first card
    drawNewCard();
    
    // Enable game buttons
    document.getElementById('guessHigher').disabled = false;
    document.getElementById('guessLower').disabled = false;
    document.getElementById('cashOut').disabled = false;
    document.getElementById('startHilo').disabled = true;
    
    document.getElementById('hiloResult').textContent = '';
    document.getElementById('hiloStatus').textContent = 'Make your guess: Higher or Lower?';
    
    updateDisplay();
}

function drawNewCard() {
    // Biased card drawing - favor cards that make winning harder
    let card;
    
    if (hiloRoyalGame.currentCard) {
        const currentValue = hiloRoyalGame.currentCard.numValue;
        
        // Extreme bias: 92% chance of drawing a card that makes the guess wrong
        if (Math.random() < 0.92) {
            // Draw cards that will likely result in wrong guesses
            const availableCards = hiloRoyalGame.deck.filter(c => {
                // If current card is low (1-6), bias toward lower cards
                // If current card is high (8-13), bias toward higher cards
                // If current card is middle (7), bias toward extreme values
                if (currentValue <= 6) {
                    return c.numValue <= currentValue + 1;
                } else if (currentValue >= 8) {
                    return c.numValue >= currentValue - 1;
                } else {
                    return c.numValue <= 4 || c.numValue >= 10;
                }
            });
            
            if (availableCards.length > 0) {
                const randomIndex = Math.floor(Math.random() * availableCards.length);
                card = availableCards[randomIndex];
                hiloRoyalGame.deck = hiloRoyalGame.deck.filter(c => c !== card);
            } else {
                card = hiloRoyalGame.deck.pop();
            }
        } else {
            // 8% chance of fair draw
            card = hiloRoyalGame.deck.pop();
        }
    } else {
        // First card - slightly bias toward middle values
        card = hiloRoyalGame.deck.pop();
    }
    
    hiloRoyalGame.currentCard = card;
    displayCard(card);
}

function displayCard(card) {
    const cardEl = document.getElementById('currentCard');
    const valueEl = document.getElementById('cardValue');
    
    // Set card color based on suit
    const isRed = card.suit === '♥' || card.suit === '♦';
    cardEl.style.color = isRed ? '#ef4444' : '#000000';
    
    cardEl.textContent = `${card.value}${card.suit}`;
    valueEl.textContent = `${card.value} of ${getSuitName(card.suit)}`;
    
    // Check for royal cards
    if (ROYAL_CARDS.includes(card.value)) {
        hiloRoyalGame.royalPoints++;
        updateRoyalProgress();
    }
}

function getSuitName(suit) {
    switch(suit) {
        case '♠': return 'Spades';
        case '♥': return 'Hearts';
        case '♦': return 'Diamonds';
        case '♣': return 'Clubs';
        default: return '';
    }
}

function makeGuess(guess) {
    if (!hiloRoyalGame.isPlaying) return;
    
    // Draw next card
    const nextCard = hiloRoyalGame.deck.pop();
    const currentValue = hiloRoyalGame.currentCard.numValue;
    const nextValue = nextCard.numValue;
    
    let correct = false;
    if (guess === 'higher' && nextValue > currentValue) correct = true;
    if (guess === 'lower' && nextValue < currentValue) correct = true;
    if (nextValue === currentValue) correct = false; // Ties lose
    
    // Add to history
    hiloRoyalGame.cardHistory.push({
        card: hiloRoyalGame.currentCard,
        guess: guess,
        correct: correct
    });
    
    if (correct) {
        hiloRoyalGame.streak++;
        hiloRoyalGame.currentCard = nextCard;
        displayCard(nextCard);
        
        document.getElementById('hiloResult').innerHTML = 
            `<span class="text-green-400 neon-glow">CORRECT!</span>`;
        document.getElementById('hiloStatus').textContent = 
            `Streak: ${hiloRoyalGame.streak} - Continue or Cash Out?`;
        
        updateDisplay();
        updateCardHistory();
        
        // Check for royal cards
        if (ROYAL_CARDS.includes(nextCard.value)) {
            hiloRoyalGame.royalPoints++;
            updateRoyalProgress();
        }
        
    } else {
        // Game over - lose everything
        endGame(false);
        
        document.getElementById('hiloResult').innerHTML = 
            `<span class="text-red-400">WRONG!</span>`;
        document.getElementById('hiloStatus').textContent = 
            `Next card was ${nextCard.value}${nextCard.suit}. Game Over!`;
    }
}

function cashOutGame() {
    if (!hiloRoyalGame.isPlaying || hiloRoyalGame.streak === 0) return;
    
    endGame(true);
}

function endGame(cashOut) {
    hiloRoyalGame.isPlaying = false;
    
    if (cashOut && hiloRoyalGame.streak > 0) {
        // Calculate winnings with extremely low multipliers
        const baseMultiplier = Math.pow(1.15, hiloRoyalGame.streak); // Very low growth
        const modeBonus = hiloRoyalGame.multiplier;
        const royalBonus = hiloRoyalGame.royalPoints > 3 ? 1.2 : 1.0;
        
        const winnings = Math.floor(hiloRoyalGame.betAmount * baseMultiplier * modeBonus * royalBonus);
        balance += winnings;
        updateBalance();
        
        document.getElementById('hiloResult').innerHTML = 
            `<span class="text-green-400 neon-glow">CASHED OUT!</span>`;
        document.getElementById('hiloStatus').innerHTML = 
            `Won ${winnings} GA with ${hiloRoyalGame.streak} streak!`;
    }
    
    // Reset game state
    setTimeout(() => {
        document.getElementById('guessHigher').disabled = true;
        document.getElementById('guessLower').disabled = true;
        document.getElementById('cashOut').disabled = true;
        document.getElementById('startHilo').disabled = false;
        
        hiloRoyalGame.streak = 0;
        hiloRoyalGame.royalPoints = 0;
        updateDisplay();
        updateRoyalProgress();
    }, 3000);
}

function updateDisplay() {
    document.getElementById('streakCount').textContent = hiloRoyalGame.streak;
    document.getElementById('royalPoints').textContent = hiloRoyalGame.royalPoints;
}

function updateCardHistory() {
    const history = document.getElementById('cardHistory');
    const lastPlay = hiloRoyalGame.cardHistory[hiloRoyalGame.cardHistory.length - 1];
    
    if (lastPlay) {
        const item = document.createElement('div');
        item.className = `px-2 py-1 rounded text-sm font-bold ${
            lastPlay.correct ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
        }`;
        item.textContent = `${lastPlay.card.value}${lastPlay.card.suit}`;
        
        history.insertBefore(item, history.firstChild);
        
        while (history.children.length > 10) {
            history.removeChild(history.lastChild);
        }
    }
}

function updateRoyalProgress() {
    const progress = Math.min(100, (hiloRoyalGame.royalPoints / 5) * 100);
    document.getElementById('flushBar').style.width = progress + '%';
    
    let text = 'No Royal Cards';
    if (hiloRoyalGame.royalPoints >= 5) text = 'ROYAL FLUSH ACHIEVED!';
    else if (hiloRoyalGame.royalPoints >= 3) text = `${hiloRoyalGame.royalPoints}/5 Royal Cards`;
    else if (hiloRoyalGame.royalPoints > 0) text = `${hiloRoyalGame.royalPoints}/5 Royal Cards`;
    
    document.getElementById('flushText').textContent = text;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

