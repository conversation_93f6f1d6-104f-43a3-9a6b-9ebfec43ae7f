
// Game state
const walletIntegration = new GameWalletIntegration('Crash Quest: Lost Temple');
let crashQuestGame = {
    isExploring: false,
    multiplier: 1.00,
    betAmount: 0,
    startTime: 0,
    crashPoint: 0,
    autoCashout: 0,
    templeDepth: 1,
    ancientCurse: 0,
    treasureFound: 0,
    explorationMode: 'cautious'
};



function loadCrashQuestGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Game Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <h4 class="text-xl font-bold mb-4 text-amber-400">CRASH QUEST: LOST TEMPLE</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="templeBet" value="10" min="1"
                               class="w-full bg-black/50 border border-amber-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">AUTO ESCAPE</label>
                        <input type="number" id="templeAutoCashout" value="2.00" min="1.01" step="0.01" 
                               class="w-full bg-black/50 border border-amber-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">EXPLORATION MODE</label>
                        <select id="explorationMode" class="w-full bg-black/50 border border-amber-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="cautious">Cautious (Safer)</option>
                            <option value="bold">Bold (Risky)</option>
                            <option value="reckless">Reckless (Extreme)</option>
                        </select>
                    </div>
                    
                    <button id="startExploration" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ENTER TEMPLE
                    </button>
                    
                    <button id="escapeTemple" class="cyber-button-secondary w-full py-3 rounded-lg font-bold text-white" disabled>
                        ESCAPE WITH TREASURE
                    </button>
                    
                    <div class="mt-4 text-center">
                        <div class="text-sm text-gray-400 mb-1">Temple Multiplier</div>
                        <div id="templeMultiplier" class="text-3xl font-bold text-amber-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Temple Depth</div>
                        <div id="templeDepth" class="text-lg font-bold text-orange-400">Level 1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Ancient Curse</div>
                        <div id="ancientCurse" class="text-lg font-bold text-red-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Temple Visualization -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <div id="templeCanvas" class="relative bg-gradient-to-b from-amber-900 to-black rounded-lg h-96 overflow-hidden">
                        <div id="explorer" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-blue-400 rounded-full transition-all duration-200"></div>
                        <div id="treasures" class="absolute inset-0"></div>
                        <div id="traps" class="absolute inset-0"></div>
                        <div id="collapseEffect" class="absolute inset-0 bg-red-600/70 opacity-0 transition-opacity duration-500"></div>
                    </div>
                    <div id="templeStatus" class="text-center mt-4 text-lg font-semibold">Ready to explore the lost temple</div>
                </div>
                
                <!-- Exploration History -->
                <div class="bg-black/30 p-4 rounded-xl border border-amber-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-amber-400">EXPEDITION HISTORY</h5>
                    <div id="templeHistory" class="flex flex-wrap gap-2"></div>
                </div>
            </div>
        </div>
    `;
    
    initializeTempleQuest();
}

function initializeTempleQuest() {
    document.getElementById('startExploration').addEventListener('click', startTempleExploration);
    document.getElementById('escapeTemple').addEventListener('click', escapeTemple);
    document.getElementById('explorationMode').addEventListener('change', updateExplorationMode);
    
    updateExplorationMode();
}

function updateExplorationMode() {
    const mode = document.getElementById('explorationMode').value;
    crashQuestGame.explorationMode = mode;
}

function generateTempleCrashPoint() {
    // Extremely harsh crash points for < 10% win rate
    const mode = crashQuestGame.explorationMode;
    const baseRandom = Math.random();
    
    // Mode affects risk distribution
    let riskMultiplier;
    switch(mode) {
        case 'cautious': riskMultiplier = 0.95; break; // 95% house edge
        case 'bold': riskMultiplier = 0.92; break; // 92% house edge  
        case 'reckless': riskMultiplier = 0.88; break; // 88% house edge
    }
    
    // Generate crash point with extreme house edge
    const adjustedRandom = Math.pow(baseRandom, 3); // Cube for extreme low values
    const crashPoint = 1 / (1 - adjustedRandom * riskMultiplier);
    
    return Math.max(1.01, Math.min(crashPoint, 25.0));
}

async function startTempleExploration() {
    const betAmount = parseInt(document.getElementById('templeBet').value);
    const autoCashout = parseFloat(document.getElementById('templeAutoCashout').value);

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    // Initialize game state
    crashQuestGame.isExploring = true;
    crashQuestGame.multiplier = 1.00;
    crashQuestGame.betAmount = betAmount;
    crashQuestGame.startTime = Date.now();
    crashQuestGame.autoCashout = autoCashout;
    crashQuestGame.crashPoint = generateTempleCrashPoint();
    crashQuestGame.templeDepth = 1;
    crashQuestGame.ancientCurse = 0;
    crashQuestGame.treasureFound = 0;
    
    // Update UI
    document.getElementById('startExploration').disabled = true;
    document.getElementById('escapeTemple').disabled = false;
    document.getElementById('templeStatus').textContent = 'Exploring the ancient temple...';
    
    animateTempleExploration();
}

function animateTempleExploration() {
    if (!crashQuestGame.isExploring) return;
    
    const elapsed = (Date.now() - crashQuestGame.startTime) / 1000;
    
    // Calculate multiplier with temple depth progression
    const depthBonus = Math.pow(crashQuestGame.templeDepth, 1.2);
    crashQuestGame.multiplier = 1 + (elapsed * 0.3 * depthBonus);
    
    // Increase temple depth over time
    crashQuestGame.templeDepth = Math.floor(elapsed / 2) + 1;
    
    // Ancient curse increases with depth (higher crash risk)
    crashQuestGame.ancientCurse = Math.min(100, crashQuestGame.templeDepth * 8);
    
    // Update displays
    document.getElementById('templeMultiplier').textContent = crashQuestGame.multiplier.toFixed(2) + 'x';
    document.getElementById('templeDepth').textContent = `Level ${crashQuestGame.templeDepth}`;
    document.getElementById('ancientCurse').textContent = Math.floor(crashQuestGame.ancientCurse) + '%';
    
    // Update explorer position
    const explorer = document.getElementById('explorer');
    const progress = Math.min(85, elapsed * 8);
    explorer.style.bottom = progress + '%';
    
    // Generate temple elements
    generateTempleElements();
    
    // Check for temple collapse (crash)
    if (crashQuestGame.multiplier >= crashQuestGame.crashPoint) {
        templeCollapse();
        return;
    }
    
    // Check auto escape
    if (crashQuestGame.multiplier >= crashQuestGame.autoCashout) {
        escapeTemple();
        return;
    }
    
    setTimeout(animateTempleExploration, 150);
}

function generateTempleElements() {
    // Add treasures
    if (Math.random() < 0.3) {
        const treasure = document.createElement('div');
        treasure.className = 'absolute w-3 h-3 bg-yellow-400 rounded-full animate-pulse';
        treasure.style.left = Math.random() * 90 + 5 + '%';
        treasure.style.top = Math.random() * 90 + 5 + '%';
        
        document.getElementById('treasures').appendChild(treasure);
        crashQuestGame.treasureFound++;
        
        setTimeout(() => {
            if (treasure.parentNode) {
                treasure.parentNode.removeChild(treasure);
            }
        }, 2000);
    }
    
    // Add traps
    if (Math.random() < 0.4) {
        const trap = document.createElement('div');
        trap.className = 'absolute w-2 h-2 bg-red-500 rounded-full';
        trap.style.left = Math.random() * 90 + 5 + '%';
        trap.style.top = Math.random() * 90 + 5 + '%';
        
        document.getElementById('traps').appendChild(trap);
        
        setTimeout(() => {
            if (trap.parentNode) {
                trap.parentNode.removeChild(trap);
            }
        }, 1500);
    }
    
    // Limit elements
    const treasures = document.getElementById('treasures');
    const traps = document.getElementById('traps');
    while (treasures.children.length > 8) {
        treasures.removeChild(treasures.firstChild);
    }
    while (traps.children.length > 10) {
        traps.removeChild(traps.firstChild);
    }
}

async function escapeTemple() {
    if (!crashQuestGame.isExploring) return;

    // Calculate winnings with treasure bonus
    const treasureBonus = 1 + (crashQuestGame.treasureFound * 0.1);
    const totalMultiplier = crashQuestGame.multiplier * treasureBonus;
    const winnings = Math.floor(crashQuestGame.betAmount * (totalMultiplier - 1));

    if (winnings > 0) {
        await walletIntegration.processWin(winnings);
    }

    crashQuestGame.isExploring = false;
    
    document.getElementById('templeStatus').innerHTML = 
        `<span class="text-green-400 neon-glow">Escaped at ${crashQuestGame.multiplier.toFixed(2)}x! Found ${crashQuestGame.treasureFound} treasures! Won ${winnings} GA</span>`;
    
    addTempleHistory(crashQuestGame.multiplier, true);
    resetTempleGame();
}

function templeCollapse() {
    crashQuestGame.isExploring = false;
    
    // Show collapse effect
    document.getElementById('collapseEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('collapseEffect').style.opacity = '0';
    }, 1500);
    
    document.getElementById('templeStatus').innerHTML = 
        `<span class="text-red-400">Temple collapsed at ${crashQuestGame.crashPoint.toFixed(2)}x! The ancient curse claims another explorer!</span>`;
    
    addTempleHistory(crashQuestGame.crashPoint, false);
    resetTempleGame();
}

function addTempleHistory(multiplier, escaped) {
    const history = document.getElementById('templeHistory');
    const item = document.createElement('div');
    item.className = `px-2 py-1 rounded text-sm font-bold ${
        escaped ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
    }`;
    item.textContent = multiplier.toFixed(2) + 'x';
    
    history.insertBefore(item, history.firstChild);
    
    while (history.children.length > 10) {
        history.removeChild(history.lastChild);
    }
}

function resetTempleGame() {
    setTimeout(() => {
        document.getElementById('startExploration').disabled = false;
        document.getElementById('escapeTemple').disabled = true;
        document.getElementById('templeStatus').textContent = 'Ready to explore the lost temple';
        document.getElementById('templeMultiplier').textContent = '1.00x';
        document.getElementById('templeDepth').textContent = 'Level 1';
        document.getElementById('ancientCurse').textContent = '0%';
        
        // Reset explorer position
        const explorer = document.getElementById('explorer');
        explorer.style.bottom = '4%';
        
        // Clear temple elements
        document.getElementById('treasures').innerHTML = '';
        document.getElementById('traps').innerHTML = '';
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadCrashQuestGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

