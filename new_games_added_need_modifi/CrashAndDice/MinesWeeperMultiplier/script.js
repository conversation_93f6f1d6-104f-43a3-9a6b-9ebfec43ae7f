// Game state
const walletIntegration = new GameWalletIntegration('MinesweeperMultiplier');
let minesweeperMultiplierGame = {
    isPlaying: false,
    gridSize: 4,
    mineCount: 8,
    betAmount: 0,
    revealedCells: 0,
    currentMultiplier: 1.0,
    gameGrid: [],
    revealedGrid: [],
    multiplierLevel: 1,
    streakCount: 0,
    riskLevel: 'medium',
    autoMode: false,
    targetMultiplier: 2.0,
    maxMultiplier: 0,
    totalRevealed: 0
};

// Risk levels that affect mine density and multipliers
const RISK_LEVELS = [
    { name: 'low', mines: 6, multiplier: 0.8, description: 'Low Risk (6 mines)' },
    { name: 'medium', mines: 8, multiplier: 1.0, description: 'Medium Risk (8 mines)' },
    { name: 'high', mines: 10, multiplier: 1.2, description: 'High Risk (10 mines)' },
    { name: 'extreme', mines: 12, multiplier: 1.5, description: 'Extreme Risk (12 mines)' },
    { name: 'insane', mines: 14, multiplier: 2.0, description: 'Insane Risk (14 mines)' }
];



function loadMinesweeperMultiplierGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Multiplier Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">💎 MINESWEEPER MULTIPLIER 💎</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 MULTIPLIER BET</label>
                        <input type="number" id="multiplierBet" value="35" min="10"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">⚡ RISK LEVEL</label>
                        <select id="riskLevel" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="low">Low Risk (6 mines)</option>
                            <option value="medium">Medium Risk (8 mines)</option>
                            <option value="high">High Risk (10 mines)</option>
                            <option value="extreme">Extreme Risk (12 mines)</option>
                            <option value="insane">Insane Risk (14 mines)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET MULTIPLIER</label>
                        <input type="number" id="targetMultiplier" value="2.0" min="1.1" max="10.0" step="0.1"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                        <div class="text-xs text-gray-400 mt-1">Potential Win: <span id="potentialMultiplierWin" class="text-green-400">70 GA</span></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🤖 AUTO MODE</label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="autoMode" class="mr-2">
                                <span class="text-sm">Auto-reveal safe cells</span>
                            </label>
                        </div>
                    </div>
                    
                    <button id="startMultiplierGame" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        💎 START MULTIPLIER HUNT 💎
                    </button>
                    
                    <button id="cashoutMultiplier" class="bg-green-600 hover:bg-green-700 w-full py-3 rounded-lg font-bold text-white mb-4" disabled>
                        🏆 LOCK IN MULTIPLIER 🏆
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">💎 Current Multiplier</div>
                        <div id="currentMultiplierDisplay" class="text-3xl font-bold text-purple-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🔍 Cells Revealed</div>
                        <div id="multiplierCellsRevealed" class="text-lg font-bold text-green-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏅 Multiplier Level</div>
                        <div id="multiplierLevelDisplay" class="text-lg font-bold text-blue-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🔥 Streak Count</div>
                        <div id="streakCountDisplay" class="text-lg font-bold text-orange-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Multiplier Grid Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400 text-center">💎 MULTIPLIER GRID 💎</h5>
                    
                    <!-- Multiplier Grid -->
                    <div class="mb-6">
                        <div id="multiplierGrid" class="grid grid-cols-4 gap-2 mx-auto" style="max-width: 280px;">
                            <!-- Grid cells will be generated here -->
                        </div>
                    </div>
                    
                    <!-- Risk Analysis -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">⚡ Risk Analysis</div>
                            <div id="riskAnalysis" class="text-lg font-bold text-purple-400">Medium Risk</div>
                            <div class="text-xs text-gray-400">Mine Density: <span id="multiplierMineDensity" class="text-red-400">50%</span></div>
                            <div class="text-xs text-gray-400">Success Rate: <span id="successRate" class="text-green-400">12%</span></div>
                        </div>
                    </div>
                    
                    <div id="multiplierResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="multiplierStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Multiplier Progress -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📈 MULTIPLIER PROGRESS</h5>
                    <div class="w-full bg-black/50 rounded-full h-4 mb-2">
                        <div id="multiplierProgress" class="bg-gradient-to-r from-purple-500 to-pink-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-400">
                        <span>1.00x</span>
                        <span id="targetMultiplierDisplay">2.00x</span>
                    </div>
                </div>
                
                <!-- Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">📊 SESSION STATS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Max Multiplier</div>
                            <div id="maxMultiplierDisplay" class="text-lg font-bold text-yellow-400">1.00x</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Revealed</div>
                            <div id="totalRevealedDisplay" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    initializeMultiplierMinesweeper();
}

function initializeMultiplierMinesweeper() {
    document.getElementById('startMultiplierGame').addEventListener('click', startMultiplierGame);
    document.getElementById('cashoutMultiplier').addEventListener('click', cashoutMultiplierGame);
    document.getElementById('riskLevel').addEventListener('change', updateRiskLevel);
    document.getElementById('targetMultiplier').addEventListener('input', updatePotentialWin);
    document.getElementById('autoMode').addEventListener('change', updateAutoMode);
    
    updateRiskLevel();
    updatePotentialWin();
}

function updateRiskLevel() {
    const risk = document.getElementById('riskLevel').value;
    minesweeperMultiplierGame.riskLevel = risk;
    
    const riskData = RISK_LEVELS.find(r => r.name === risk);
    minesweeperMultiplierGame.mineCount = riskData.mines;
    
    // Update displays
    document.getElementById('riskAnalysis').textContent = riskData.description;
    
    const totalCells = minesweeperMultiplierGame.gridSize * minesweeperMultiplierGame.gridSize;
    const density = (riskData.mines / totalCells) * 100;
    document.getElementById('multiplierMineDensity').textContent = density.toFixed(0) + '%';
    
    // Calculate extremely low success rate
    const successRate = Math.max(5, 25 - (riskData.mines * 2));
    document.getElementById('successRate').textContent = successRate + '%';
    
    updatePotentialWin();
}

function updateAutoMode() {
    minesweeperMultiplierGame.autoMode = document.getElementById('autoMode').checked;
}

function updatePotentialWin() {
    const bet = parseInt(document.getElementById('multiplierBet').value) || 0;
    const target = parseFloat(document.getElementById('targetMultiplier').value);
    const riskData = RISK_LEVELS.find(r => r.name === minesweeperMultiplierGame.riskLevel);
    
    const potentialWin = Math.floor(bet * target * riskData.multiplier);
    document.getElementById('potentialMultiplierWin').textContent = potentialWin + ' GA';
    document.getElementById('targetMultiplierDisplay').textContent = target.toFixed(2) + 'x';
    
    minesweeperMultiplierGame.targetMultiplier = target;
}

async function startMultiplierGame() {
    const betAmount = parseInt(document.getElementById('multiplierBet').value);

    if (minesweeperMultiplierGame.isPlaying) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    // Initialize game
    minesweeperMultiplierGame.betAmount = betAmount;
    minesweeperMultiplierGame.isPlaying = true;
    minesweeperMultiplierGame.revealedCells = 0;
    minesweeperMultiplierGame.currentMultiplier = 1.0;
    
    // Generate extremely dangerous grid
    generateMultiplierGrid();
    renderMultiplierGrid();
    
    // Disable start button, enable cashout
    document.getElementById('startMultiplierGame').disabled = true;
    document.getElementById('cashoutMultiplier').disabled = false;
    
    document.getElementById('multiplierResult').textContent = '';
    document.getElementById('multiplierStatus').textContent = 'Multiplier hunt initiated. Click cells to build multiplier...';
    
    updateMultiplierDisplay();
}

function generateMultiplierGrid() {
    const size = minesweeperMultiplierGame.gridSize;
    const riskData = RISK_LEVELS.find(r => r.name === minesweeperMultiplierGame.riskLevel);
    
    // Initialize empty grid
    minesweeperMultiplierGame.gameGrid = Array(size).fill().map(() => Array(size).fill(false));
    minesweeperMultiplierGame.revealedGrid = Array(size).fill().map(() => Array(size).fill(false));
    
    // Calculate mine count (extremely high density)
    let mineCount = riskData.mines;
    
    // Add extra mines for higher difficulty (make it nearly impossible)
    if (minesweeperMultiplierGame.targetMultiplier > 3.0) {
        mineCount += Math.floor((minesweeperMultiplierGame.targetMultiplier - 3.0) * 2);
    }
    
    // Ensure at least 75% mine density for maximum difficulty
    const totalCells = size * size;
    const minMines = Math.floor(totalCells * 0.75);
    mineCount = Math.max(mineCount, minMines);
    
    // Cap at 87% to leave minimal safe spots
    mineCount = Math.min(mineCount, Math.floor(totalCells * 0.87));
    
    // Place mines randomly
    let minesPlaced = 0;
    while (minesPlaced < mineCount) {
        const row = Math.floor(Math.random() * size);
        const col = Math.floor(Math.random() * size);
        
        if (!minesweeperMultiplierGame.gameGrid[row][col]) {
            minesweeperMultiplierGame.gameGrid[row][col] = true;
            minesPlaced++;
        }
    }
    
    minesweeperMultiplierGame.mineCount = mineCount;
}

function renderMultiplierGrid() {
    const gridContainer = document.getElementById('multiplierGrid');
    const size = minesweeperMultiplierGame.gridSize;
    
    gridContainer.innerHTML = '';
    
    // Create grid cells
    for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
            const cell = document.createElement('div');
            cell.className = 'multiplier-cell bg-black/50 border border-purple-500/30 rounded-lg h-16 w-16 flex items-center justify-center text-lg font-bold cursor-pointer hover:bg-purple-500/20 transition-all duration-200';
            cell.dataset.row = row;
            cell.dataset.col = col;
            cell.textContent = '💎';
            
            cell.addEventListener('click', () => revealMultiplierCell(row, col));
            gridContainer.appendChild(cell);
        }
    }
}

function revealMultiplierCell(row, col) {
    if (!minesweeperMultiplierGame.isPlaying || minesweeperMultiplierGame.revealedGrid[row][col]) {
        return;
    }
    
    const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
    minesweeperMultiplierGame.revealedGrid[row][col] = true;
    
    if (minesweeperMultiplierGame.gameGrid[row][col]) {
        // Hit a mine - game over
        cell.textContent = '💥';
        cell.className = 'multiplier-cell bg-red-600 border border-red-400 rounded-lg h-16 w-16 flex items-center justify-center text-lg font-bold';
        
        explodeMultiplierMine();
    } else {
        // Safe cell - increase multiplier
        cell.textContent = '⭐';
        cell.className = 'multiplier-cell bg-green-600 border border-green-400 rounded-lg h-16 w-16 flex items-center justify-center text-lg font-bold';
        
        minesweeperMultiplierGame.revealedCells++;
        minesweeperMultiplierGame.totalRevealed++;
        
        // Calculate multiplier (very slow growth)
        const riskData = RISK_LEVELS.find(r => r.name === minesweeperMultiplierGame.riskLevel);
        const baseIncrease = 0.12 * riskData.multiplier; // Very small increase
        minesweeperMultiplierGame.currentMultiplier += baseIncrease;
        minesweeperMultiplierGame.maxMultiplier = Math.max(minesweeperMultiplierGame.maxMultiplier, minesweeperMultiplierGame.currentMultiplier);
        
        updateMultiplierDisplay();
        
        // Auto-cashout if target reached
        if (minesweeperMultiplierGame.currentMultiplier >= minesweeperMultiplierGame.targetMultiplier) {
            setTimeout(() => {
                cashoutMultiplierGame();
            }, 1000);
        }
        
        // Auto mode - reveal another cell (dangerous)
        if (minesweeperMultiplierGame.autoMode && minesweeperMultiplierGame.isPlaying) {
            setTimeout(() => {
                autoRevealCell();
            }, 500);
        }
    }
}

function autoRevealCell() {
    const size = minesweeperMultiplierGame.gridSize;
    const unrevealed = [];
    
    // Find unrevealed cells
    for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
            if (!minesweeperMultiplierGame.revealedGrid[row][col]) {
                unrevealed.push({row, col});
            }
        }
    }
    
    if (unrevealed.length > 0) {
        const randomCell = unrevealed[Math.floor(Math.random() * unrevealed.length)];
        revealMultiplierCell(randomCell.row, randomCell.col);
    }
}

function explodeMultiplierMine() {
    minesweeperMultiplierGame.isPlaying = false;
    minesweeperMultiplierGame.streakCount = 0;
    
    // Reveal all mines
    const size = minesweeperMultiplierGame.gridSize;
    for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
            if (minesweeperMultiplierGame.gameGrid[row][col] && !minesweeperMultiplierGame.revealedGrid[row][col]) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.textContent = '💣';
                cell.className = 'multiplier-cell bg-red-500/50 border border-red-400 rounded-lg h-16 w-16 flex items-center justify-center text-lg font-bold';
            }
        }
    }
    
    document.getElementById('multiplierResult').innerHTML = 
        `<span class="text-red-400">💥 MULTIPLIER DESTROYED! 💥</span>`;
    
    let explosionReason = 'High-risk mining';
    if (minesweeperMultiplierGame.riskLevel === 'insane') explosionReason = 'Insane risk level';
    else if (minesweeperMultiplierGame.autoMode) explosionReason = 'Auto-mode failure';
    else if (minesweeperMultiplierGame.targetMultiplier > 5.0) explosionReason = 'Extreme target multiplier';
    
    document.getElementById('multiplierStatus').textContent = 
        `Multiplier hunt failed due to ${explosionReason}! Lost ${minesweeperMultiplierGame.betAmount} GA.`;
    
    resetMultiplierGame();
}

async function cashoutMultiplierGame() {
    if (!minesweeperMultiplierGame.isPlaying && minesweeperMultiplierGame.revealedCells === 0) {
        return;
    }

    // Calculate winnings
    const riskData = RISK_LEVELS.find(r => r.name === minesweeperMultiplierGame.riskLevel);
    const difficultyBonus = 1.0 + ((riskData.multiplier - 1) * 0.1);
    const payout = Math.floor(minesweeperMultiplierGame.betAmount * minesweeperMultiplierGame.currentMultiplier * difficultyBonus);
    const netWinnings = payout - minesweeperMultiplierGame.betAmount;

    if (netWinnings > 0) {
        await walletIntegration.processWin(netWinnings);
    }

    document.getElementById('multiplierResult').innerHTML = 
        `<span class="text-green-400 neon-glow">💰 MULTIPLIER SECURED! 💰</span>`;
    document.getElementById('multiplierStatus').innerHTML = 
        `Cashed out at ${minesweeperMultiplierGame.currentMultiplier.toFixed(2)}x for ${payout} GA!`;

    minesweeperMultiplierGame.isPlaying = false;
    minesweeperMultiplierGame.streakCount++;
    minesweeperMultiplierGame.multiplierLevel++;

    resetMultiplierGame();
}

function updateMultiplierDisplay() {
    document.getElementById('currentMultiplierDisplay').textContent = minesweeperMultiplierGame.currentMultiplier.toFixed(2) + 'x';
    document.getElementById('multiplierCellsRevealed').textContent = minesweeperMultiplierGame.revealedCells;
    document.getElementById('multiplierLevelDisplay').textContent = minesweeperMultiplierGame.multiplierLevel;
    document.getElementById('streakCountDisplay').textContent = minesweeperMultiplierGame.streakCount;
    document.getElementById('maxMultiplierDisplay').textContent = minesweeperMultiplierGame.maxMultiplier.toFixed(2) + 'x';
    document.getElementById('totalRevealedDisplay').textContent = minesweeperMultiplierGame.totalRevealed;
    
    // Update progress bar
    const progress = Math.min(100, (minesweeperMultiplierGame.currentMultiplier / minesweeperMultiplierGame.targetMultiplier) * 100);
    document.getElementById('multiplierProgress').style.width = progress + '%';
}

function resetMultiplierGame() {
    setTimeout(() => {
        document.getElementById('startMultiplierGame').disabled = false;
        document.getElementById('cashoutMultiplier').disabled = true;
        
        // Clear grid
        document.getElementById('multiplierGrid').innerHTML = '';
        
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadMinesweeperMultiplierGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

