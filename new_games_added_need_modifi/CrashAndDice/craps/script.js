// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadCrapsGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Betting Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                            <h4 class="text-xl font-bold mb-4 text-purple-400">CYBER CRAPS</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="crapsBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET TYPE</label>
                                <select id="crapsBetType" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="pass">Pass Line (1:1)</option>
                                    <option value="dontpass">Don't Pass (1:1)</option>
                                    <option value="field">Field Bet (1:1, 2:1, 3:1)</option>
                                    <option value="any7">Any 7 (4:1)</option>
                                    <option value="any11">Any 11 (15:1)</option>
                                    <option value="anycrap">Any Craps (7:1)</option>
                                    <option value="hard6">Hard 6 (9:1)</option>
                                    <option value="hard8">Hard 8 (9:1)</option>
                                </select>
                            </div>
                            
                            <button id="rollCraps" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                ROLL DICE
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Potential Win</div>
                                <div id="crapsPotentialWin" class="text-xl font-bold text-green-400">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Game Phase</div>
                                <div id="crapsPhase" class="text-lg font-bold text-purple-400">Come Out</div>
                            </div>
                        </div>
                        
                        <!-- Bet Info -->
                        <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-purple-400">BET INFO</h5>
                            <div id="crapsBetInfo" class="text-sm text-gray-300">
                                <div><strong>Pass Line:</strong></div>
                                <div>Win on 7,11 (come out)</div>
                                <div>Lose on 2,3,12 (come out)</div>
                                <div>Point established on 4,5,6,8,9,10</div>
                            </div>
                        </div>
                        
                        <!-- Recent Rolls -->
                        <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-purple-400">RECENT ROLLS</h5>
                            <div id="crapsHistory" class="flex flex-wrap gap-2">
                                <!-- Recent rolls will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Craps Table -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                            <div id="crapsTable" class="relative bg-gradient-to-br from-green-900 to-green-800 rounded-lg p-6 h-96">
                                <!-- Dice Display -->
                                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                    <div class="flex space-x-4">
                                        <div id="crapsDie1" class="w-16 h-16 bg-white rounded-lg flex items-center justify-center text-2xl font-bold text-black border-2 border-purple-400 neon-border">
                                            ?
                                        </div>
                                        <div id="crapsDie2" class="w-16 h-16 bg-white rounded-lg flex items-center justify-center text-2xl font-bold text-black border-2 border-purple-400 neon-border">
                                            ?
                                        </div>
                                    </div>
                                    <div id="crapsTotal" class="text-center mt-2 text-2xl font-bold text-white neon-glow">-</div>
                                </div>
                                
                                <!-- Point Display -->
                                <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                                    <div class="text-center">
                                        <div class="text-sm text-gray-300">POINT</div>
                                        <div id="crapsPoint" class="text-3xl font-bold text-yellow-400 neon-glow">OFF</div>
                                    </div>
                                </div>
                                
                                <!-- Bet Areas -->
                                <div class="absolute bottom-4 left-4">
                                    <div class="text-xs text-white">PASS LINE</div>
                                </div>
                                <div class="absolute bottom-4 right-4">
                                    <div class="text-xs text-white">DON'T PASS</div>
                                </div>
                                
                                <!-- Field Numbers -->
                                <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                                    <div class="text-xs text-white text-center">FIELD</div>
                                    <div class="text-xs text-gray-300">2,3,4,9,10,11,12</div>
                                </div>
                            </div>
                            <div id="crapsStatus" class="text-center mt-4 text-lg font-semibold">Place your bet and roll the dice</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeCraps();
        }
        
        let crapsGame = {
            isPlaying: false,
            phase: 'comeout', // 'comeout' or 'point'
            point: null,
            betAmount: 0,
            betType: 'pass'
        };
        
        function initializeCraps() {
            document.getElementById('rollCraps').addEventListener('click', rollCrapsDice);
            document.getElementById('crapsBetType').addEventListener('change', updateCrapsBetInfo);
            document.getElementById('crapsBet').addEventListener('input', updateCrapsPotentialWin);
            
            updateCrapsBetInfo();
            updateCrapsPotentialWin();
        }
        
        function updateCrapsBetInfo() {
            const betType = document.getElementById('crapsBetType').value;
            const infoDiv = document.getElementById('crapsBetInfo');
            
            const betDescriptions = {
                'pass': `<div><strong>Pass Line:</strong></div>
                        <div>Win on 7,11 (come out)</div>
                        <div>Lose on 2,3,12 (come out)</div>
                        <div>Point: Win if point rolled before 7</div>`,
                'dontpass': `<div><strong>Don't Pass:</strong></div>
                            <div>Win on 2,3 (come out)</div>
                            <div>Lose on 7,11 (come out)</div>
                            <div>Point: Win if 7 rolled before point</div>`,
                'field': `<div><strong>Field Bet:</strong></div>
                         <div>Win on 2,3,4,9,10,11,12</div>
                         <div>2,12 pay 2:1, others 1:1</div>
                         <div>Lose on 5,6,7,8</div>`,
                'any7': `<div><strong>Any 7:</strong></div>
                        <div>Win if next roll is 7</div>
                        <div>Pays 4:1</div>`,
                'any11': `<div><strong>Any 11:</strong></div>
                         <div>Win if next roll is 11</div>
                         <div>Pays 15:1</div>`,
                'anycrap': `<div><strong>Any Craps:</strong></div>
                           <div>Win on 2,3,12</div>
                           <div>Pays 7:1</div>`,
                'hard6': `<div><strong>Hard 6:</strong></div>
                         <div>Win on 3+3</div>
                         <div>Lose on any other 6 or 7</div>
                         <div>Pays 9:1</div>`,
                'hard8': `<div><strong>Hard 8:</strong></div>
                         <div>Win on 4+4</div>
                         <div>Lose on any other 8 or 7</div>
                         <div>Pays 9:1</div>`
            };
            
            infoDiv.innerHTML = betDescriptions[betType];
            updateCrapsPotentialWin();
        }
        
        function updateCrapsPotentialWin() {
            const betAmount = parseInt(document.getElementById('crapsBet').value) || 0;
            const betType = document.getElementById('crapsBetType').value;
            
            const payouts = {
                'pass': 2, 'dontpass': 2, 'field': 2,
                'any7': 5, 'any11': 16, 'anycrap': 8,
                'hard6': 10, 'hard8': 10
            };
            
            const potentialWin = betAmount * payouts[betType];
            document.getElementById('crapsPotentialWin').textContent = '$' + potentialWin;
        }
        
        function rollCrapsDice() {
            const betAmount = parseInt(document.getElementById('crapsBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            if (!crapsGame.isPlaying) {
                // Start new game
                balance -= betAmount;
                updateBalance();
                
                crapsGame.isPlaying = true;
                crapsGame.betAmount = betAmount;
                crapsGame.betType = document.getElementById('crapsBetType').value;
            }
            
            // Animate dice roll
            animateCrapsDice();
        }
        
        function animateCrapsDice() {
            const die1 = document.getElementById('crapsDie1');
            const die2 = document.getElementById('crapsDie2');
            
            document.getElementById('rollCraps').disabled = true;
            document.getElementById('crapsStatus').textContent = 'Rolling dice...';
            
            let rollCount = 0;
            const rollInterval = setInterval(() => {
                die1.textContent = Math.floor(Math.random() * 6) + 1;
                die2.textContent = Math.floor(Math.random() * 6) + 1;
                rollCount++;
                
                if (rollCount >= 15) {
                    clearInterval(rollInterval);
                    
                    // Final roll
                    const finalDie1 = Math.floor(Math.random() * 6) + 1;
                    const finalDie2 = Math.floor(Math.random() * 6) + 1;
                    const total = finalDie1 + finalDie2;
                    
                    die1.textContent = finalDie1;
                    die2.textContent = finalDie2;
                    document.getElementById('crapsTotal').textContent = total;
                    
                    // Add to history
                    addCrapsHistory(finalDie1, finalDie2, total);
                    
                    // Resolve bet
                    resolveCrapsBet(finalDie1, finalDie2, total);
                }
            }, 100);
        }
        
        function resolveCrapsBet(die1, die2, total) {
            let won = false;
            let payout = 1;
            
            switch (crapsGame.betType) {
                case 'pass':
                    if (crapsGame.phase === 'comeout') {
                        if ([7, 11].includes(total)) {
                            won = true;
                            payout = 2;
                        } else if ([2, 3, 12].includes(total)) {
                            won = false;
                        } else {
                            // Establish point
                            crapsGame.point = total;
                            crapsGame.phase = 'point';
                            document.getElementById('crapsPoint').textContent = total;
                            document.getElementById('crapsPhase').textContent = 'Point';
                            document.getElementById('crapsStatus').textContent = `Point is ${total}. Roll ${total} to win, 7 to lose.`;
                            document.getElementById('rollCraps').disabled = false;
                            return;
                        }
                    } else {
                        if (total === crapsGame.point) {
                            won = true;
                            payout = 2;
                        } else if (total === 7) {
                            won = false;
                        } else {
                            document.getElementById('crapsStatus').textContent = `Point is ${crapsGame.point}. Keep rolling!`;
                            document.getElementById('rollCraps').disabled = false;
                            return;
                        }
                    }
                    break;
                    
                case 'dontpass':
                    if (crapsGame.phase === 'comeout') {
                        if ([2, 3].includes(total)) {
                            won = true;
                            payout = 2;
                        } else if ([7, 11].includes(total)) {
                            won = false;
                        } else if (total === 12) {
                            // Push
                            balance += crapsGame.betAmount;
                            updateBalance();
                            endCrapsGame('push');
                            return;
                        } else {
                            crapsGame.point = total;
                            crapsGame.phase = 'point';
                            document.getElementById('crapsPoint').textContent = total;
                            document.getElementById('crapsPhase').textContent = 'Point';
                            document.getElementById('crapsStatus').textContent = `Point is ${total}. Roll 7 to win, ${total} to lose.`;
                            document.getElementById('rollCraps').disabled = false;
                            return;
                        }
                    } else {
                        if (total === 7) {
                            won = true;
                            payout = 2;
                        } else if (total === crapsGame.point) {
                            won = false;
                        } else {
                            document.getElementById('crapsStatus').textContent = `Point is ${crapsGame.point}. Keep rolling!`;
                            document.getElementById('rollCraps').disabled = false;
                            return;
                        }
                    }
                    break;
                    
                case 'field':
                    if ([2, 3, 4, 9, 10, 11, 12].includes(total)) {
                        won = true;
                        payout = [2, 12].includes(total) ? 3 : 2;
                    }
                    break;
                    
                case 'any7':
                    won = total === 7;
                    payout = 5;
                    break;
                    
                case 'any11':
                    won = total === 11;
                    payout = 16;
                    break;
                    
                case 'anycrap':
                    won = [2, 3, 12].includes(total);
                    payout = 8;
                    break;
                    
                case 'hard6':
                    if (total === 6 && die1 === 3 && die2 === 3) {
                        won = true;
                        payout = 10;
                    } else if (total === 6 || total === 7) {
                        won = false;
                    } else {
                        document.getElementById('crapsStatus').textContent = 'Waiting for Hard 6 (3+3) or 7...';
                        document.getElementById('rollCraps').disabled = false;
                        return;
                    }
                    break;
                    
                case 'hard8':
                    if (total === 8 && die1 === 4 && die2 === 4) {
                        won = true;
                        payout = 10;
                    } else if (total === 8 || total === 7) {
                        won = false;
                    } else {
                        document.getElementById('crapsStatus').textContent = 'Waiting for Hard 8 (4+4) or 7...';
                        document.getElementById('rollCraps').disabled = false;
                        return;
                    }
                    break;
            }
            
            // Calculate winnings
            if (won) {
                const winnings = crapsGame.betAmount * payout;
                balance += winnings;
                updateBalance();
                endCrapsGame('win', winnings);
            } else {
                endCrapsGame('lose');
            }
        }
        
        function addCrapsHistory(die1, die2, total) {
            const history = document.getElementById('crapsHistory');
            const roll = document.createElement('div');
            roll.className = 'flex items-center space-x-1 bg-purple-600/20 border border-purple-400 rounded px-2 py-1';
            roll.innerHTML = `
                <span class="text-xs">${die1}</span>
                <span class="text-xs">+</span>
                <span class="text-xs">${die2}</span>
                <span class="text-xs">=</span>
                <span class="text-sm font-bold">${total}</span>
            `;
            
            history.insertBefore(roll, history.firstChild);
            
            // Keep only last 8 rolls
            while (history.children.length > 8) {
                history.removeChild(history.lastChild);
            }
        }
        
        function endCrapsGame(result, winnings = 0) {
            crapsGame.isPlaying = false;
            
            // Reset game state for line bets
            if (['pass', 'dontpass'].includes(crapsGame.betType)) {
                crapsGame.phase = 'comeout';
                crapsGame.point = null;
                document.getElementById('crapsPoint').textContent = 'OFF';
                document.getElementById('crapsPhase').textContent = 'Come Out';
            }
            
            // Update status
            if (result === 'win') {
                document.getElementById('crapsStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Winner! $${winnings}</span>`;
            } else if (result === 'push') {
                document.getElementById('crapsStatus').innerHTML = 
                    `<span class="text-yellow-400">Push! Bet returned</span>`;
            } else {
                document.getElementById('crapsStatus').innerHTML = 
                    `<span class="text-red-400">House wins!</span>`;
            }
            
            // Reset after delay
            setTimeout(() => {
                document.getElementById('crapsStatus').textContent = 'Place your bet and roll the dice';
                document.getElementById('rollCraps').disabled = false;
            }, 3000);
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCrapsGame();
});