// Game state
let balance = 1000;
let rocketRebornGame = {
    isFlying: false,
    betAmount: 0,
    targetMultiplier: 2.0,
    currentMultiplier: 1.0,
    maxMultiplier: 1.0,
    rocketLevel: 1,
    fuelEfficiency: 0,
    enginePower: 0,
    navigationSystem: 'basic',
    boosterMode: 'standard',
    consecutiveFlights: 0,
    totalLaunches: 0,
    crashCount: 0,
    maxAltitude: 0,
    missionType: 'orbital'
};

// Navigation systems that affect crash probability
const NAVIGATION_SYSTEMS = [
    { name: 'basic', crashBonus: 0.88, description: 'Basic Navigation' },
    { name: 'advanced', crashBonus: 0.91, description: 'Advanced GPS' },
    { name: 'quantum', crashBonus: 0.94, description: 'Quantum Navigation' },
    { name: 'ai', crashBonus: 0.96, description: 'AI Autopilot' },
    { name: 'neural', crashBonus: 0.98, description: 'Neural Interface' }
];

// Booster modes that affect difficulty
const BOOSTER_MODES = [
    { name: 'standard', multiplier: 1.0, description: 'Standard Boosters' },
    { name: 'turbo', multiplier: 1.3, description: 'Turbo Boosters (1.3x harder)' },
    { name: 'plasma', multiplier: 1.6, description: 'Plasma Boosters (1.6x harder)' },
    { name: 'antimatter', multiplier: 2.0, description: 'Antimatter Boosters (2x harder)' },
    { name: 'warp', multiplier: 2.5, description: 'Warp Drive (2.5x harder)' }
];

// Mission types
const MISSION_TYPES = [
    { name: 'orbital', multiplier: 1.0, description: 'Orbital Flight' },
    { name: 'lunar', multiplier: 1.4, description: 'Lunar Mission' },
    { name: 'mars', multiplier: 1.8, description: 'Mars Mission' },
    { name: 'jupiter', multiplier: 2.2, description: 'Jupiter Mission' },
    { name: 'interstellar', multiplier: 3.0, description: 'Interstellar Journey' }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Rocket Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">🚀 ROCKET REBORN 🚀</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 LAUNCH BUDGET</label>
                        <input type="number" id="rocketBet" value="50" min="10" max="${balance}" 
                               class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET MULTIPLIER</label>
                        <input type="number" id="targetMultiplier" value="2.0" min="1.1" max="100" step="0.1" 
                               class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🧭 NAVIGATION SYSTEM</label>
                        <select id="navigationSystem" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Navigation</option>
                            <option value="advanced">Advanced GPS (Harder)</option>
                            <option value="quantum">Quantum Navigation (Extreme)</option>
                            <option value="ai">AI Autopilot (Nearly Impossible)</option>
                            <option value="neural">Neural Interface (Impossible)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🔥 BOOSTER MODE</label>
                        <select id="boosterMode" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Boosters</option>
                            <option value="turbo">Turbo Boosters (1.3x harder)</option>
                            <option value="plasma">Plasma Boosters (1.6x harder)</option>
                            <option value="antimatter">Antimatter Boosters (2x harder)</option>
                            <option value="warp">Warp Drive (2.5x harder)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌌 MISSION TYPE</label>
                        <select id="missionType" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="orbital">Orbital Flight</option>
                            <option value="lunar">Lunar Mission (1.4x harder)</option>
                            <option value="mars">Mars Mission (1.8x harder)</option>
                            <option value="jupiter">Jupiter Mission (2.2x harder)</option>
                            <option value="interstellar">Interstellar Journey (3x harder)</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="launchRocket" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🚀 LAUNCH ROCKET
                        </button>
                        <button id="ejectRocket" class="bg-red-600 hover:bg-red-700 flex-1 py-3 rounded-lg font-bold text-white" disabled>
                            🪂 EJECT
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Success Probability</div>
                        <div id="rocketWinChance" class="text-lg font-bold text-red-400">6%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="potentialPayout" class="text-2xl font-bold text-blue-400 neon-glow">100 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🚀 Rocket Level</div>
                        <div id="rocketLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⛽ Fuel Efficiency</div>
                        <div id="fuelEfficiencyDisplay" class="text-lg font-bold text-yellow-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Rocket Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h5 class="text-lg font-bold mb-4 text-blue-400 text-center">🚀 MISSION CONTROL 🚀</h5>
                    
                    <!-- Rocket Multiplier Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-blue-900/50 to-black/50 p-8 rounded-xl border border-blue-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🌟 Current Multiplier</div>
                                <div id="rocketMultiplierDisplay" class="text-8xl font-bold text-blue-400 neon-glow mb-4">1.00x</div>
                                <div class="text-sm text-gray-400">Target: <span id="rocketTargetDisplay" class="text-blue-400">2.00x</span></div>
                                <div class="text-sm text-gray-400">Max Reached: <span id="maxMultiplierDisplay" class="text-green-400">1.00x</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Flight Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🛰️ Flight Status</div>
                            <div id="flightStatus" class="text-lg font-bold text-blue-400">Ready for launch...</div>
                            <div class="text-xs text-gray-400">Navigation: <span id="navStatus" class="text-green-400">Basic</span></div>
                            <div class="text-xs text-gray-400">Boosters: <span id="boosterStatus" class="text-orange-400">Standard</span></div>
                            <div class="text-xs text-gray-400">Mission: <span id="missionStatus" class="text-purple-400">Orbital</span></div>
                        </div>
                    </div>
                    
                    <div id="rocketResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="rocketStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Flight Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">📊 FLIGHT RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Successful Flights</div>
                            <div id="consecutiveFlights" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Launches</div>
                            <div id="totalLaunches" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Crash Count</div>
                            <div id="crashCount" class="text-lg font-bold text-red-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Success Rate</div>
                            <div id="successRate" class="text-lg font-bold text-orange-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Engine Progress -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">🔧 ENGINE PROGRESS 🔧</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="engineProgress" class="bg-gradient-to-r from-green-500 to-blue-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="engineText" class="text-center mt-2 text-green-300">Basic Rocket Engine</div>
                </div>
            </div>
        </div>
    `;
    
    initializeRocketReborn();
}

function initializeRocketReborn() {
    document.getElementById('launchRocket').addEventListener('click', launchRocket);
    document.getElementById('ejectRocket').addEventListener('click', ejectRocket);
    document.getElementById('targetMultiplier').addEventListener('input', updatePotentialPayout);
    document.getElementById('navigationSystem').addEventListener('change', updateNavigationSystem);
    document.getElementById('boosterMode').addEventListener('change', updateBoosterMode);
    document.getElementById('missionType').addEventListener('change', updateMissionType);
    
    updateNavigationSystem();
    updateBoosterMode();
    updateMissionType();
    updatePotentialPayout();
}

function updateNavigationSystem() {
    const system = document.getElementById('navigationSystem').value;
    rocketRebornGame.navigationSystem = system;
    
    const navData = NAVIGATION_SYSTEMS.find(n => n.name === system);
    document.getElementById('navStatus').textContent = navData.description;
    
    updatePotentialPayout();
}

function updateBoosterMode() {
    const mode = document.getElementById('boosterMode').value;
    rocketRebornGame.boosterMode = mode;
    
    const boosterData = BOOSTER_MODES.find(b => b.name === mode);
    document.getElementById('boosterStatus').textContent = boosterData.description;
    
    updatePotentialPayout();
}

function updateMissionType() {
    const mission = document.getElementById('missionType').value;
    rocketRebornGame.missionType = mission;
    
    const missionData = MISSION_TYPES.find(m => m.name === mission);
    document.getElementById('missionStatus').textContent = missionData.description;
    
    updatePotentialPayout();
}

function updatePotentialPayout() {
    const betAmount = parseInt(document.getElementById('rocketBet').value) || 50;
    const targetMultiplier = parseFloat(document.getElementById('targetMultiplier').value) || 2.0;
    
    rocketRebornGame.targetMultiplier = targetMultiplier;
    document.getElementById('rocketTargetDisplay').textContent = targetMultiplier.toFixed(2) + 'x';
    
    // Calculate extremely low success probability
    const navData = NAVIGATION_SYSTEMS.find(n => n.name === rocketRebornGame.navigationSystem);
    const boosterData = BOOSTER_MODES.find(b => b.name === rocketRebornGame.boosterMode);
    const missionData = MISSION_TYPES.find(m => m.name === rocketRebornGame.missionType);
    
    // Base crash probability increases with target multiplier
    let baseCrashChance = 0.85 + (targetMultiplier - 1.0) * 0.02;
    
    // Apply system modifiers (make it much harder)
    const finalCrashChance = Math.min(0.99, baseCrashChance * navData.crashBonus * boosterData.multiplier * missionData.multiplier);
    const successChance = Math.max(1, Math.floor((1 - finalCrashChance) * 100));
    
    const potentialPayout = Math.floor(betAmount * targetMultiplier);
    
    document.getElementById('rocketWinChance').textContent = successChance + '%';
    document.getElementById('potentialPayout').textContent = potentialPayout + ' GA';
}

function launchRocket() {
    const betAmount = parseInt(document.getElementById('rocketBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient launch budget!');
        return;
    }
    
    if (rocketRebornGame.isFlying) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    rocketRebornGame.betAmount = betAmount;
    rocketRebornGame.isFlying = true;
    rocketRebornGame.currentMultiplier = 1.0;
    rocketRebornGame.totalLaunches++;
    
    // Disable launch button, enable eject
    document.getElementById('launchRocket').disabled = true;
    document.getElementById('ejectRocket').disabled = false;
    
    document.getElementById('rocketResult').textContent = '';
    document.getElementById('rocketStatus').textContent = 'Rocket launching...';
    document.getElementById('flightStatus').textContent = 'ASCENDING...';
    
    // Start flight simulation
    startRocketFlight();
}

function startRocketFlight() {
    const navData = NAVIGATION_SYSTEMS.find(n => n.name === rocketRebornGame.navigationSystem);
    const boosterData = BOOSTER_MODES.find(b => b.name === rocketRebornGame.boosterMode);
    const missionData = MISSION_TYPES.find(m => m.name === rocketRebornGame.missionType);
    
    const flightInterval = setInterval(() => {
        if (!rocketRebornGame.isFlying) {
            clearInterval(flightInterval);
            return;
        }
        
        // Calculate crash probability (extremely high - 85-99% chance)
        const baseCrashChance = 0.85 + (rocketRebornGame.targetMultiplier - 1.0) * 0.02;
        const finalCrashChance = Math.min(0.99, baseCrashChance * navData.crashBonus * boosterData.multiplier * missionData.multiplier);
        
        // Increase crash chance as multiplier gets higher
        const currentCrashChance = finalCrashChance + (rocketRebornGame.currentMultiplier - 1.0) * 0.01;
        
        if (Math.random() < currentCrashChance) {
            // Rocket crashes
            crashRocket();
            clearInterval(flightInterval);
            return;
        }
        
        // Increase multiplier slightly
        const increment = 0.01 + (Math.random() * 0.02);
        rocketRebornGame.currentMultiplier += increment;
        rocketRebornGame.maxMultiplier = Math.max(rocketRebornGame.maxMultiplier, rocketRebornGame.currentMultiplier);
        
        // Check if target reached (very rare)
        if (rocketRebornGame.currentMultiplier >= rocketRebornGame.targetMultiplier) {
            reachTarget();
            clearInterval(flightInterval);
            return;
        }
        
        updateRocketDisplay();
        
    }, 100);
}

function ejectRocket() {
    if (!rocketRebornGame.isFlying) return;
    
    rocketRebornGame.isFlying = false;
    rocketRebornGame.consecutiveFlights++;
    rocketRebornGame.rocketLevel++;
    rocketRebornGame.fuelEfficiency += 5;
    rocketRebornGame.enginePower += 3;
    
    // Calculate winnings
    const winnings = Math.floor(rocketRebornGame.betAmount * rocketRebornGame.currentMultiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('rocketResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🪂 SUCCESSFUL EJECTION! 🪂</span>`;
    document.getElementById('rocketStatus').innerHTML = 
        `Ejected at ${rocketRebornGame.currentMultiplier.toFixed(2)}x! Won ${winnings} GA!`;
    
    document.getElementById('flightStatus').textContent = 'MISSION SUCCESSFUL';
    
    resetRocketControls();
}

function crashRocket() {
    rocketRebornGame.isFlying = false;
    rocketRebornGame.crashCount++;
    rocketRebornGame.consecutiveFlights = 0;
    
    document.getElementById('rocketResult').innerHTML = 
        `<span class="text-red-400">💥 ROCKET EXPLOSION! 💥</span>`;
    
    let crashReason = 'Engine failure';
    if (rocketRebornGame.currentMultiplier > 3.0) crashReason = 'Structural failure';
    else if (rocketRebornGame.boosterMode === 'warp' || rocketRebornGame.boosterMode === 'antimatter') crashReason = 'Booster malfunction';
    else if (rocketRebornGame.missionType === 'interstellar' || rocketRebornGame.missionType === 'jupiter') crashReason = 'Navigation error';
    
    document.getElementById('rocketStatus').textContent = 
        `Crashed at ${rocketRebornGame.currentMultiplier.toFixed(2)}x due to ${crashReason}!`;
    
    document.getElementById('flightStatus').textContent = 'MISSION FAILED';
    
    resetRocketControls();
}

function reachTarget() {
    rocketRebornGame.isFlying = false;
    rocketRebornGame.consecutiveFlights++;
    rocketRebornGame.rocketLevel += 2;
    rocketRebornGame.fuelEfficiency += 10;
    rocketRebornGame.enginePower += 8;
    
    // Calculate winnings
    const winnings = Math.floor(rocketRebornGame.betAmount * rocketRebornGame.targetMultiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('rocketResult').innerHTML = 
        `<span class="text-gold-400 neon-glow">🌟 TARGET ACHIEVED! 🌟</span>`;
    document.getElementById('rocketStatus').innerHTML = 
        `Reached ${rocketRebornGame.targetMultiplier.toFixed(2)}x target! Won ${winnings} GA!`;
    
    document.getElementById('flightStatus').textContent = 'TARGET REACHED';
    
    resetRocketControls();
}

function resetRocketControls() {
    setTimeout(() => {
        document.getElementById('launchRocket').disabled = false;
        document.getElementById('ejectRocket').disabled = true;
        document.getElementById('flightStatus').textContent = 'Ready for launch...';
        updateRocketDisplay();
    }, 3000);
}

function updateRocketDisplay() {
    document.getElementById('rocketMultiplierDisplay').textContent = rocketRebornGame.currentMultiplier.toFixed(2) + 'x';
    document.getElementById('maxMultiplierDisplay').textContent = rocketRebornGame.maxMultiplier.toFixed(2) + 'x';
    document.getElementById('rocketLevelDisplay').textContent = rocketRebornGame.rocketLevel;
    document.getElementById('fuelEfficiencyDisplay').textContent = rocketRebornGame.fuelEfficiency + '%';
    document.getElementById('consecutiveFlights').textContent = rocketRebornGame.consecutiveFlights;
    document.getElementById('totalLaunches').textContent = rocketRebornGame.totalLaunches;
    document.getElementById('crashCount').textContent = rocketRebornGame.crashCount;
    
    // Calculate success rate
    const successRate = rocketRebornGame.totalLaunches > 0 ? 
        Math.floor((rocketRebornGame.consecutiveFlights / rocketRebornGame.totalLaunches) * 100) : 0;
    document.getElementById('successRate').textContent = successRate + '%';
    
    // Update engine progress
    const progress = Math.min(100, (rocketRebornGame.enginePower / 200) * 100);
    document.getElementById('engineProgress').style.width = progress + '%';
    
    let engineLevel = 'Basic Rocket Engine';
    if (rocketRebornGame.enginePower > 150) engineLevel = 'Quantum Drive Engine';
    else if (rocketRebornGame.enginePower > 120) engineLevel = 'Fusion Rocket Engine';
    else if (rocketRebornGame.enginePower > 90) engineLevel = 'Ion Drive Engine';
    else if (rocketRebornGame.enginePower > 60) engineLevel = 'Nuclear Engine';
    else if (rocketRebornGame.enginePower > 30) engineLevel = 'Advanced Chemical Engine';
    
    document.getElementById('engineText').textContent = engineLevel;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

