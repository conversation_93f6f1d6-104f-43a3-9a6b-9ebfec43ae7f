// Game state
const walletIntegration = new GameWalletIntegration('MinesweeperCryptoGrid');
let cryptoMinesweeperGame = {
    isPlaying: false,
    gridSize: 5,
    mineCount: 12,
    betAmount: 0,
    revealedCells: 0,
    currentMultiplier: 1.0,
    gameGrid: [],
    revealedGrid: [],
    cryptoLevel: 1,
    hashPower: 0,
    blockchainSecurity: 'low',
    miningDifficulty: 'normal',
    quantumEncryption: false,
    aiDetection: false,
    gridHacked: false,
    safeSpots: 0
};

// Mining difficulties that affect mine density
const MINING_DIFFICULTIES = [
    { name: 'normal', mineMultiplier: 1.0, description: 'Standard Mining' },
    { name: 'advanced', mineMultiplier: 1.4, description: 'Advanced Encryption' },
    { name: 'quantum', mineMultiplier: 1.8, description: 'Quantum Security' },
    { name: 'ai', mineMultiplier: 2.2, description: 'AI-Protected Grid' },
    { name: 'fortress', mineMultiplier: 2.8, description: 'Crypto Fortress' }
];



function loadCryptoMinesweeperGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Crypto Grid Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">⚡ CRYPTO GRID MINESWEEPER ⚡</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 MINING STAKE</label>
                        <input type="number" id="cryptoMineBet" value="40" min="10"
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🔒 GRID SECURITY</label>
                        <select id="gridSecurity" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="low">Low Security (12 mines)</option>
                            <option value="medium">Medium Security (15 mines)</option>
                            <option value="high">High Security (18 mines)</option>
                            <option value="maximum">Maximum Security (20 mines)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🛡️ MINING DIFFICULTY</label>
                        <select id="miningDifficulty" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Standard Mining</option>
                            <option value="advanced">Advanced Encryption (1.4x mines)</option>
                            <option value="quantum">Quantum Security (1.8x mines)</option>
                            <option value="ai">AI-Protected Grid (2.2x mines)</option>
                            <option value="fortress">Crypto Fortress (2.8x mines)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">📊 GRID SIZE</label>
                        <select id="gridSize" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="5">5x5 Grid (25 cells)</option>
                            <option value="6">6x6 Grid (36 cells)</option>
                            <option value="7">7x7 Grid (49 cells)</option>
                        </select>
                    </div>
                    
                    <button id="startCryptoMining" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ⚡ START CRYPTO MINING ⚡
                    </button>
                    
                    <button id="cashoutCrypto" class="bg-green-600 hover:bg-green-700 w-full py-3 rounded-lg font-bold text-white mb-4" disabled>
                        💰 SECURE PROFITS 💰
                    </button>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">⚡ Current Multiplier</div>
                        <div id="currentCryptoMultiplier" class="text-3xl font-bold text-cyan-400 neon-glow">1.00x</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🔍 Cells Revealed</div>
                        <div id="cellsRevealed" class="text-lg font-bold text-green-400">0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Crypto Level</div>
                        <div id="cryptoLevelDisplay" class="text-lg font-bold text-purple-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">⚡ Hash Power</div>
                        <div id="hashPowerDisplay" class="text-lg font-bold text-yellow-400">0</div>
                    </div>
                </div>
            </div>
            
            <!-- Crypto Grid Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h5 class="text-lg font-bold mb-4 text-cyan-400 text-center">🔐 CRYPTO MINING GRID 🔐</h5>
                    
                    <!-- Mining Grid -->
                    <div class="mb-6">
                        <div id="cryptoMiningGrid" class="grid gap-1 mx-auto" style="max-width: 300px;">
                            <!-- Grid cells will be generated here -->
                        </div>
                    </div>
                    
                    <!-- Security Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🛡️ Security Status</div>
                            <div id="securityStatus" class="text-lg font-bold text-cyan-400">Scanning...</div>
                            <div class="text-xs text-gray-400">Encryption: <span id="encryptionLevel" class="text-blue-400">Standard</span></div>
                            <div class="text-xs text-gray-400">AI Detection: <span id="aiStatus" class="text-red-400">Inactive</span></div>
                        </div>
                    </div>
                    
                    <div id="cryptoMineResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="cryptoMineStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Mining Stats -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">📊 MINING STATISTICS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Safe Zones</div>
                            <div id="safeZones" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Mine Density</div>
                            <div id="mineDensity" class="text-lg font-bold text-red-400">48%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Blockchain Security -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">🔗 BLOCKCHAIN SECURITY 🔗</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="securityBar" class="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-4 rounded-full transition-all duration-300" style="width: 25%"></div>
                    </div>
                    <div id="securityText" class="text-center mt-2 text-blue-300">Vulnerable</div>
                </div>
            </div>
        </div>
    `;
    
    initializeCryptoMinesweeper();
}

function initializeCryptoMinesweeper() {
    document.getElementById('startCryptoMining').addEventListener('click', startCryptoMining);
    document.getElementById('cashoutCrypto').addEventListener('click', cashoutCryptoMining);
    document.getElementById('gridSecurity').addEventListener('change', updateGridSecurity);
    document.getElementById('miningDifficulty').addEventListener('change', updateMiningDifficulty);
    document.getElementById('gridSize').addEventListener('change', updateGridSize);
    
    updateGridSecurity();
    updateMiningDifficulty();
    updateGridSize();
}

function updateGridSecurity() {
    const security = document.getElementById('gridSecurity').value;
    cryptoMinesweeperGame.blockchainSecurity = security;
    
    // Update mine count based on security level
    switch(security) {
        case 'low': cryptoMinesweeperGame.mineCount = 12; break;
        case 'medium': cryptoMinesweeperGame.mineCount = 15; break;
        case 'high': cryptoMinesweeperGame.mineCount = 18; break;
        case 'maximum': cryptoMinesweeperGame.mineCount = 20; break;
    }
    
    updateMineDensity();
}

function updateMiningDifficulty() {
    const difficulty = document.getElementById('miningDifficulty').value;
    cryptoMinesweeperGame.miningDifficulty = difficulty;
    
    // Apply difficulty multiplier to mine count
    const difficultyData = MINING_DIFFICULTIES.find(d => d.name === difficulty);
    const baseMines = cryptoMinesweeperGame.mineCount;
    cryptoMinesweeperGame.mineCount = Math.floor(baseMines * difficultyData.mineMultiplier);
    
    // Enable special features for higher difficulties
    cryptoMinesweeperGame.quantumEncryption = difficulty === 'quantum' || difficulty === 'ai' || difficulty === 'fortress';
    cryptoMinesweeperGame.aiDetection = difficulty === 'ai' || difficulty === 'fortress';
    
    updateMineDensity();
    updateSecurityDisplay();
}

function updateGridSize() {
    const size = parseInt(document.getElementById('gridSize').value);
    cryptoMinesweeperGame.gridSize = size;
    updateMineDensity();
}

function updateMineDensity() {
    const totalCells = cryptoMinesweeperGame.gridSize * cryptoMinesweeperGame.gridSize;
    const density = Math.min(95, (cryptoMinesweeperGame.mineCount / totalCells) * 100);
    document.getElementById('mineDensity').textContent = density.toFixed(0) + '%';
}

function updateSecurityDisplay() {
    const difficulty = MINING_DIFFICULTIES.find(d => d.name === cryptoMinesweeperGame.miningDifficulty);
    document.getElementById('encryptionLevel').textContent = difficulty.description;
    document.getElementById('aiStatus').textContent = cryptoMinesweeperGame.aiDetection ? 'Active' : 'Inactive';
    
    // Update security bar
    const securityLevel = Math.min(100, difficulty.mineMultiplier * 30);
    document.getElementById('securityBar').style.width = securityLevel + '%';
    
    let securityText = 'Vulnerable';
    if (securityLevel > 80) securityText = 'Fortress';
    else if (securityLevel > 60) securityText = 'Secure';
    else if (securityLevel > 40) securityText = 'Protected';
    document.getElementById('securityText').textContent = securityText;
}

async function startCryptoMining() {
    const betAmount = parseInt(document.getElementById('cryptoMineBet').value);

    if (cryptoMinesweeperGame.isPlaying) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    // Initialize game
    cryptoMinesweeperGame.betAmount = betAmount;
    cryptoMinesweeperGame.isPlaying = true;
    cryptoMinesweeperGame.revealedCells = 0;
    cryptoMinesweeperGame.currentMultiplier = 1.0;
    cryptoMinesweeperGame.safeSpots = 0;
    
    // Generate extremely dangerous grid
    generateCryptoGrid();
    renderCryptoGrid();
    
    // Disable start button, enable cashout
    document.getElementById('startCryptoMining').disabled = true;
    document.getElementById('cashoutCrypto').disabled = false;
    
    document.getElementById('cryptoMineResult').textContent = '';
    document.getElementById('cryptoMineStatus').textContent = 'Mining grid initialized. Click cells to reveal...';
    document.getElementById('securityStatus').textContent = 'Active Scanning';
    
    updateCryptoDisplay();
}

function generateCryptoGrid() {
    const size = cryptoMinesweeperGame.gridSize;
    const totalCells = size * size;
    
    // Initialize empty grid
    cryptoMinesweeperGame.gameGrid = Array(size).fill().map(() => Array(size).fill(false));
    cryptoMinesweeperGame.revealedGrid = Array(size).fill().map(() => Array(size).fill(false));
    
    // Calculate actual mine count (extremely high density)
    let actualMineCount = cryptoMinesweeperGame.mineCount;
    
    // Add extra mines for AI detection and quantum encryption
    if (cryptoMinesweeperGame.aiDetection) actualMineCount += 3;
    if (cryptoMinesweeperGame.quantumEncryption) actualMineCount += 2;
    
    // Ensure mine density is at least 70% for maximum difficulty
    const minMines = Math.floor(totalCells * 0.70);
    actualMineCount = Math.max(actualMineCount, minMines);
    
    // Cap at 90% to leave some safe spots
    actualMineCount = Math.min(actualMineCount, Math.floor(totalCells * 0.90));
    
    // Place mines randomly
    let minesPlaced = 0;
    while (minesPlaced < actualMineCount) {
        const row = Math.floor(Math.random() * size);
        const col = Math.floor(Math.random() * size);
        
        if (!cryptoMinesweeperGame.gameGrid[row][col]) {
            cryptoMinesweeperGame.gameGrid[row][col] = true;
            minesPlaced++;
        }
    }
    
    // Update mine count display
    cryptoMinesweeperGame.mineCount = actualMineCount;
    updateMineDensity();
}

function renderCryptoGrid() {
    const gridContainer = document.getElementById('cryptoMiningGrid');
    const size = cryptoMinesweeperGame.gridSize;
    
    // Set grid layout
    gridContainer.style.gridTemplateColumns = `repeat(${size}, 1fr)`;
    gridContainer.innerHTML = '';
    
    // Create grid cells
    for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
            const cell = document.createElement('div');
            cell.className = 'crypto-cell bg-black/50 border border-cyan-500/30 rounded-lg h-12 w-12 flex items-center justify-center text-lg font-bold cursor-pointer hover:bg-cyan-500/20 transition-all duration-200';
            cell.dataset.row = row;
            cell.dataset.col = col;
            cell.textContent = '🔒';
            
            cell.addEventListener('click', () => revealCryptoCell(row, col));
            gridContainer.appendChild(cell);
        }
    }
}

function revealCryptoCell(row, col) {
    if (!cryptoMinesweeperGame.isPlaying || cryptoMinesweeperGame.revealedGrid[row][col]) {
        return;
    }
    
    const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
    cryptoMinesweeperGame.revealedGrid[row][col] = true;
    
    if (cryptoMinesweeperGame.gameGrid[row][col]) {
        // Hit a mine - game over
        cell.textContent = '💥';
        cell.className = 'crypto-cell bg-red-600 border border-red-400 rounded-lg h-12 w-12 flex items-center justify-center text-lg font-bold';
        
        explodeCryptoMine();
    } else {
        // Safe cell
        cell.textContent = '💎';
        cell.className = 'crypto-cell bg-green-600 border border-green-400 rounded-lg h-12 w-12 flex items-center justify-center text-lg font-bold';
        
        cryptoMinesweeperGame.revealedCells++;
        cryptoMinesweeperGame.safeSpots++;
        
        // Calculate multiplier (very low growth)
        cryptoMinesweeperGame.currentMultiplier = 1.0 + (cryptoMinesweeperGame.revealedCells * 0.15);
        
        updateCryptoDisplay();
        
        // Check for win condition (extremely rare)
        const totalCells = cryptoMinesweeperGame.gridSize * cryptoMinesweeperGame.gridSize;
        const safeCells = totalCells - cryptoMinesweeperGame.mineCount;
        
        if (cryptoMinesweeperGame.revealedCells >= safeCells) {
            completeCryptoMining();
        }
    }
}

function explodeCryptoMine() {
    cryptoMinesweeperGame.isPlaying = false;
    
    // Reveal all mines
    const size = cryptoMinesweeperGame.gridSize;
    for (let row = 0; row < size; row++) {
        for (let col = 0; col < size; col++) {
            if (cryptoMinesweeperGame.gameGrid[row][col] && !cryptoMinesweeperGame.revealedGrid[row][col]) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                cell.textContent = '💣';
                cell.className = 'crypto-cell bg-red-500/50 border border-red-400 rounded-lg h-12 w-12 flex items-center justify-center text-lg font-bold';
            }
        }
    }
    
    document.getElementById('cryptoMineResult').innerHTML = 
        `<span class="text-red-400">💥 CRYPTO MINE DETONATED! 💥</span>`;
    
    let explosionReason = 'Security breach';
    if (cryptoMinesweeperGame.aiDetection) explosionReason = 'AI detection triggered';
    else if (cryptoMinesweeperGame.quantumEncryption) explosionReason = 'Quantum encryption failed';
    else if (cryptoMinesweeperGame.miningDifficulty === 'fortress') explosionReason = 'Fortress security activated';
    
    document.getElementById('cryptoMineStatus').textContent = 
        `Mining operation failed due to ${explosionReason}! Lost ${cryptoMinesweeperGame.betAmount} GA.`;
    
    document.getElementById('securityStatus').textContent = 'BREACH DETECTED';
    
    resetCryptoMining();
}

async function completeCryptoMining() {
    cryptoMinesweeperGame.isPlaying = false;
    cryptoMinesweeperGame.cryptoLevel++;
    cryptoMinesweeperGame.hashPower += cryptoMinesweeperGame.revealedCells * 10;

    // Calculate massive winnings for this impossible feat
    const payout = cryptoMinesweeperGame.betAmount * cryptoMinesweeperGame.currentMultiplier * 5; // Extra bonus
    const netWinnings = payout - cryptoMinesweeperGame.betAmount;

    if (netWinnings > 0) {
        await walletIntegration.processWin(netWinnings);
    }

    document.getElementById('cryptoMineResult').innerHTML =
        `<span class="text-green-400 neon-glow">🎉 GRID CLEARED! 🎉</span>`;
    document.getElementById('cryptoMineStatus').textContent =
        `You cleared the entire grid! Earned ${payout.toFixed(0)} GA!`;

    resetCryptoMining();
}

async function cashoutCryptoMining() {
    if (!cryptoMinesweeperGame.isPlaying) return;

    cryptoMinesweeperGame.isPlaying = false;

    // Calculate winnings
    const payout = cryptoMinesweeperGame.betAmount * cryptoMinesweeperGame.currentMultiplier;
    const netWinnings = payout - cryptoMinesweeperGame.betAmount;

    if (netWinnings > 0) {
        await walletIntegration.processWin(netWinnings);
    }

    document.getElementById('cryptoMineResult').innerHTML =
        `<span class="text-green-400">💰 PROFITS SECURED! 💰</span>`;
    document.getElementById('cryptoMineStatus').textContent =
        `Cashed out with a ${cryptoMinesweeperGame.currentMultiplier.toFixed(2)}x multiplier! Earned ${payout.toFixed(0)} GA.`;

    cryptoMinesweeperGame.cryptoLevel++;
    cryptoMinesweeperGame.hashPower += cryptoMinesweeperGame.revealedCells * 5;

    resetCryptoMining();
}

function updateCryptoDisplay() {
    document.getElementById('currentCryptoMultiplier').textContent = cryptoMinesweeperGame.currentMultiplier.toFixed(2) + 'x';
    document.getElementById('cellsRevealed').textContent = cryptoMinesweeperGame.revealedCells;
    document.getElementById('cryptoLevelDisplay').textContent = cryptoMinesweeperGame.cryptoLevel;
    document.getElementById('hashPowerDisplay').textContent = cryptoMinesweeperGame.hashPower;
    document.getElementById('safeZones').textContent = cryptoMinesweeperGame.safeSpots;
}

function resetCryptoMining() {
    setTimeout(() => {
        document.getElementById('startCryptoMining').disabled = false;
        document.getElementById('cashoutCrypto').disabled = true;
        document.getElementById('securityStatus').textContent = 'Scanning...';
        
        // Clear grid
        document.getElementById('cryptoMiningGrid').innerHTML = '';
        
    }, 3000);
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadCryptoMinesweeperGame();
    } catch (error) {
        console.error("Failed to initialize wallet:", error);
        const gameContent = document.getElementById('gameContent');
        if (gameContent) {
            gameContent.innerHTML = `<div class="text-red-500 text-center">Failed to load game wallet. Please refresh the page.</div>`;
        }
    }
});

