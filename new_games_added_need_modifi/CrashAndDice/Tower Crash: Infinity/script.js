// Game state
const walletIntegration = new GameWalletIntegration('TowerCrashInfinity');
let towerCrashGame = {
    isClimbing: false,
    betAmount: 0,
    targetFloor: 10,
    currentFloor: 1,
    maxFloor: 1,
    towerType: 'glass',
    climbingSpeed: 'normal',
    safetyEquipment: 'basic',
    weatherCondition: 'clear',
    consecutiveClimbs: 0,
    totalClimbs: 0,
    fallCount: 0,
    maxHeight: 0,
    towerLevel: 1,
    structuralIntegrity: 100
};

// Tower types that affect stability
const TOWER_TYPES = [
    { name: 'glass', stability: 0.84, description: 'Glass Tower' },
    { name: 'steel', stability: 0.87, description: 'Steel Tower' },
    { name: 'concrete', stability: 0.90, description: 'Concrete Tower' },
    { name: 'crystal', stability: 0.93, description: 'Crystal Tower' },
    { name: 'quantum', stability: 0.96, description: 'Quantum Tower' }
];

// Climbing speeds
const CLIMBING_SPEEDS = [
    { name: 'slow', risk: 1.0, description: 'Slow Climb' },
    { name: 'normal', risk: 1.2, description: 'Normal Climb' },
    { name: 'fast', risk: 1.5, description: 'Fast Climb (1.5x risk)' },
    { name: 'sprint', risk: 1.9, description: 'Sprint Climb (1.9x risk)' },
    { name: 'rocket', risk: 2.4, description: 'Rocket Climb (2.4x risk)' }
];

// Safety equipment
const SAFETY_EQUIPMENT = [
    { name: 'none', protection: 0.80, description: 'No Safety Equipment' },
    { name: 'basic', protection: 0.85, description: 'Basic Safety Gear' },
    { name: 'advanced', protection: 0.88, description: 'Advanced Gear' },
    { name: 'professional', protection: 0.91, description: 'Professional Gear' },
    { name: 'military', protection: 0.94, description: 'Military Grade Gear' }
];

// Weather conditions
const WEATHER_CONDITIONS = [
    { name: 'clear', effect: 1.0, description: 'Clear Weather' },
    { name: 'windy', effect: 1.3, description: 'Windy (1.3x harder)' },
    { name: 'rain', effect: 1.6, description: 'Rainy (1.6x harder)' },
    { name: 'storm', effect: 2.0, description: 'Storm (2x harder)' },
    { name: 'hurricane', effect: 2.8, description: 'Hurricane (2.8x harder)' }
];



function loadTowerCrashInfinityGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Tower Crash Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">🏗️ TOWER CRASH: INFINITY 🏗️</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 CLIMB BET</label>
                        <input type="number" id="towerBet" value="50" min="10" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET FLOOR</label>
                        <input type="number" id="targetFloor" value="10" min="2" max="1000" 
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏗️ TOWER TYPE</label>
                        <select id="towerType" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="glass" selected>🏢 Glass Tower</option>
                            <option value="steel">🏭 Steel Tower</option>
                            <option value="concrete">🏛️ Concrete Tower</option>
                            <option value="crystal">💎 Crystal Tower (Harder)</option>
                            <option value="quantum">⚛️ Quantum Tower (Extreme)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏃 CLIMBING SPEED</label>
                        <select id="climbingSpeed" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="slow">🐌 Slow Climb</option>
                            <option value="normal" selected>🚶 Normal Climb</option>
                            <option value="fast">🏃 Fast Climb (1.5x risk)</option>
                            <option value="sprint">💨 Sprint Climb (1.9x risk)</option>
                            <option value="rocket">🚀 Rocket Climb (2.4x risk)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🦺 SAFETY EQUIPMENT</label>
                        <select id="safetyEquipment" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="none">No Safety Equipment</option>
                            <option value="basic" selected>Basic Safety Gear</option>
                            <option value="advanced">Advanced Gear</option>
                            <option value="professional">Professional Gear</option>
                            <option value="military">Military Grade Gear</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌤️ WEATHER CONDITION</label>
                        <select id="weatherCondition" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="clear" selected>☀️ Clear Weather</option>
                            <option value="windy">💨 Windy (1.3x harder)</option>
                            <option value="rain">🌧️ Rainy (1.6x harder)</option>
                            <option value="storm">⛈️ Storm (2x harder)</option>
                            <option value="hurricane">🌪️ Hurricane (2.8x harder)</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="startClimb" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🧗 START CLIMB
                        </button>
                        <button id="evacuate" class="bg-red-600 hover:bg-red-700 flex-1 py-3 rounded-lg font-bold text-white" disabled>
                            🚁 EVACUATE
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Success Probability</div>
                        <div id="towerWinChance" class="text-lg font-bold text-red-400">5%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="towerPotentialPayout" class="text-2xl font-bold text-purple-400 neon-glow">500 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏗️ Tower Level</div>
                        <div id="towerLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏗️ Structural Integrity</div>
                        <div id="structuralIntegrityDisplay" class="text-lg font-bold text-yellow-400">100%</div>
                    </div>
                </div>
            </div>
            
            <!-- Tower Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h5 class="text-lg font-bold mb-4 text-purple-400 text-center">🏗️ INFINITY TOWER 🏗️</h5>
                    
                    <!-- Tower Floor Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-purple-900/50 to-black/50 p-8 rounded-xl border border-purple-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🏢 Current Floor</div>
                                <div id="towerFloorDisplay" class="text-8xl font-bold text-purple-400 neon-glow mb-4">1</div>
                                <div class="text-sm text-gray-400">Target: <span id="towerTargetDisplay" class="text-purple-400">Floor 10</span></div>
                                <div class="text-sm text-gray-400">Max Height: <span id="maxHeightDisplay" class="text-green-400">0m</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Climb Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🧗 Climb Status</div>
                            <div id="climbStatus" class="text-lg font-bold text-purple-400">Ready to climb...</div>
                            <div class="text-xs text-gray-400">Tower: <span id="towerStatus" class="text-blue-400">Glass</span></div>
                            <div class="text-xs text-gray-400">Speed: <span id="speedStatus" class="text-green-400">Normal</span></div>
                            <div class="text-xs text-gray-400">Safety: <span id="safetyStatus" class="text-orange-400">Basic</span></div>
                            <div class="text-xs text-gray-400">Weather: <span id="weatherStatus" class="text-cyan-400">Clear</span></div>
                        </div>
                    </div>
                    
                    <!-- Tower Stability -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🏗️ Tower Stability</div>
                            <div class="w-full bg-black/50 rounded-full h-4">
                                <div id="stabilityBar" class="bg-gradient-to-r from-green-500 to-red-500 h-4 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                            <div id="stabilityLevel" class="text-center mt-2 text-green-300">100% Stable</div>
                        </div>
                    </div>
                    
                    <!-- Wind Meter -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">💨 Wind Conditions</div>
                            <div class="w-full bg-black/50 rounded-full h-3">
                                <div id="windMeter" class="bg-gradient-to-r from-blue-500 to-red-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="windLevel" class="text-center mt-1 text-blue-300">Calm</div>
                        </div>
                    </div>
                    
                    <div id="towerResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="towerStatus" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Climb Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">📊 CLIMB RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Successful Climbs</div>
                            <div id="consecutiveClimbs" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Climbs</div>
                            <div id="totalClimbs" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Falls</div>
                            <div id="fallCount" class="text-lg font-bold text-red-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Success Rate</div>
                            <div id="climbSuccessRate" class="text-lg font-bold text-orange-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Tower Engineering -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">🔧 TOWER ENGINEERING 🔧</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="engineeringProgress" class="bg-gradient-to-r from-purple-500 to-blue-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="engineeringText" class="text-center mt-2 text-blue-300">Basic Tower Design</div>
                </div>
            </div>
        </div>
    `;
    
    initializeTowerCrash();
}

function initializeTowerCrash() {
    document.getElementById('startClimb').addEventListener('click', startClimb);
    document.getElementById('evacuate').addEventListener('click', evacuate);
    document.getElementById('targetFloor').addEventListener('input', updateTowerPayout);
    document.getElementById('towerType').addEventListener('change', updateTowerType);
    document.getElementById('climbingSpeed').addEventListener('change', updateClimbingSpeed);
    document.getElementById('safetyEquipment').addEventListener('change', updateSafetyEquipment);
    document.getElementById('weatherCondition').addEventListener('change', updateWeatherCondition);
    
    updateTowerType();
    updateClimbingSpeed();
    updateSafetyEquipment();
    updateWeatherCondition();
    updateTowerPayout();
}

function updateTowerType() {
    const type = document.getElementById('towerType').value;
    towerCrashGame.towerType = type;
    
    const towerData = TOWER_TYPES.find(t => t.name === type);
    document.getElementById('towerStatus').textContent = towerData.description;
    
    updateTowerPayout();
}

function updateClimbingSpeed() {
    const speed = document.getElementById('climbingSpeed').value;
    towerCrashGame.climbingSpeed = speed;
    
    const speedData = CLIMBING_SPEEDS.find(s => s.name === speed);
    document.getElementById('speedStatus').textContent = speedData.description;
    
    updateTowerPayout();
}

function updateSafetyEquipment() {
    const equipment = document.getElementById('safetyEquipment').value;
    towerCrashGame.safetyEquipment = equipment;
    
    const equipmentData = SAFETY_EQUIPMENT.find(e => e.name === equipment);
    document.getElementById('safetyStatus').textContent = equipmentData.description;
    
    updateTowerPayout();
}

function updateWeatherCondition() {
    const weather = document.getElementById('weatherCondition').value;
    towerCrashGame.weatherCondition = weather;
    
    const weatherData = WEATHER_CONDITIONS.find(w => w.name === weather);
    document.getElementById('weatherStatus').textContent = weatherData.description;
    
    // Update wind meter
    let windIntensity = 0;
    switch(weather) {
        case 'clear': windIntensity = 0; break;
        case 'windy': windIntensity = 30; break;
        case 'rain': windIntensity = 60; break;
        case 'storm': windIntensity = 80; break;
        case 'hurricane': windIntensity = 100; break;
    }
    
    document.getElementById('windMeter').style.width = windIntensity + '%';
    document.getElementById('windLevel').textContent = weatherData.description;
    
    updateTowerPayout();
}

function updateTowerPayout() {
    const betAmount = parseInt(document.getElementById('towerBet').value) || 50;
    const targetFloor = parseInt(document.getElementById('targetFloor').value) || 10;
    
    towerCrashGame.targetFloor = targetFloor;
    document.getElementById('towerTargetDisplay').textContent = `Floor ${targetFloor}`;
    
    // Calculate extremely low success probability
    const towerData = TOWER_TYPES.find(t => t.name === towerCrashGame.towerType);
    const speedData = CLIMBING_SPEEDS.find(s => s.name === towerCrashGame.climbingSpeed);
    const equipmentData = SAFETY_EQUIPMENT.find(e => e.name === towerCrashGame.safetyEquipment);
    const weatherData = WEATHER_CONDITIONS.find(w => w.name === towerCrashGame.weatherCondition);
    
    // Base crash probability increases exponentially with floor height
    let baseCrashChance = 0.85 + (Math.log(targetFloor) / Math.log(100)) * 0.12;
    
    // Apply all modifiers (make it extremely hard)
    const finalCrashChance = Math.min(0.995, 
        baseCrashChance / towerData.stability * speedData.risk * 
        weatherData.effect / equipmentData.protection);
    
    const successChance = Math.max(1, Math.floor((1 - finalCrashChance) * 100));
    const multiplier = 1 + (targetFloor - 1) * 0.5; // 0.5x per floor
    const potentialPayout = Math.floor(betAmount * multiplier);
    
    document.getElementById('towerWinChance').textContent = successChance + '%';
    document.getElementById('towerPotentialPayout').textContent = potentialPayout + ' GA';
}

async function startClimb() {
    const betAmount = parseInt(document.getElementById('towerBet').value);

    if (towerCrashGame.isClimbing) return;

    const transaction = await walletIntegration.processBet(betAmount);
    if (!transaction.success) {
        return;
    }

    towerCrashGame.betAmount = betAmount;
    towerCrashGame.isClimbing = true;
    towerCrashGame.currentFloor = 1;
    towerCrashGame.totalClimbs++;
    towerCrashGame.structuralIntegrity = 100;

    document.getElementById('startClimb').disabled = true;
    document.getElementById('evacuate').disabled = false;

    document.getElementById('towerResult').textContent = '';
    document.getElementById('towerStatus').textContent = 'Beginning tower climb...';
    document.getElementById('climbStatus').textContent = 'CLIMBING...';

    startTowerClimb();
}

function startTowerClimb() {
    const towerData = TOWER_TYPES.find(t => t.name === towerCrashGame.towerType);
    const speedData = CLIMBING_SPEEDS.find(s => s.name === towerCrashGame.climbingSpeed);
    const equipmentData = SAFETY_EQUIPMENT.find(e => e.name === towerCrashGame.safetyEquipment);
    const weatherData = WEATHER_CONDITIONS.find(w => w.name === towerCrashGame.weatherCondition);
    
    const climbInterval = setInterval(() => {
        if (!towerCrashGame.isClimbing) {
            clearInterval(climbInterval);
            return;
        }
        
        // Calculate crash probability (extremely high - 85-99.5% chance)
        const baseCrashChance = 0.85 + (Math.log(towerCrashGame.targetFloor) / Math.log(100)) * 0.12;
        const finalCrashChance = Math.min(0.995, 
            baseCrashChance / towerData.stability * speedData.risk * 
            weatherData.effect / equipmentData.protection);
        
        // Increase crash chance as we climb higher
        const heightCrashChance = finalCrashChance + (towerCrashGame.currentFloor - 1) * 0.005;
        
        // Decrease structural integrity
        towerCrashGame.structuralIntegrity = Math.max(0, towerCrashGame.structuralIntegrity - 0.8);
        if (towerCrashGame.structuralIntegrity <= 20) {
            // Critical structural failure
            const structuralCrashChance = heightCrashChance + (20 - towerCrashGame.structuralIntegrity) * 0.03;
            if (Math.random() < structuralCrashChance) {
                fallFromTower();
                clearInterval(climbInterval);
                return;
            }
        } else if (Math.random() < heightCrashChance) {
            // Normal fall
            fallFromTower();
            clearInterval(climbInterval);
            return;
        }
        
        // Climb to next floor (slower progression)
        const floorIncrement = 0.15 + (Math.random() * 0.25);
        towerCrashGame.currentFloor += floorIncrement;
        towerCrashGame.maxHeight = Math.max(towerCrashGame.maxHeight, 
            Math.floor((towerCrashGame.currentFloor - 1) * 3.5)); // Height in meters
        
        // Check if target reached (extremely rare)
        if (towerCrashGame.currentFloor >= towerCrashGame.targetFloor) {
            reachTargetFloor();
            clearInterval(climbInterval);
            return;
        }
        
        updateTowerDisplay();
        
    }, 150); // Slower updates for tower climbing
}

function evacuate() {
    if (!towerCrashGame.isClimbing) return;
    
    towerCrashGame.isClimbing = false;
    towerCrashGame.consecutiveClimbs++;
    towerCrashGame.towerLevel++;
    
    // Calculate winnings based on floors climbed
    const floorsClimbed = Math.floor(towerCrashGame.currentFloor);
    const multiplier = 1 + (floorsClimbed - 1) * 0.5;
    const winnings = Math.floor(towerCrashGame.betAmount * multiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('towerResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🚁 SUCCESSFUL EVACUATION! 🚁</span>`;
    document.getElementById('towerStatus').innerHTML = 
        `Evacuated from floor ${floorsClimbed}! Won ${winnings} GA!`;
    
    document.getElementById('climbStatus').textContent = 'EVACUATION SUCCESSFUL';
    
    resetTowerControls();
}

function fallFromTower() {
    towerCrashGame.isClimbing = false;
    towerCrashGame.fallCount++;
    towerCrashGame.consecutiveClimbs = 0;
    
    document.getElementById('towerResult').innerHTML = 
        `<span class="text-red-400">💥 TOWER COLLAPSE! 💥</span>`;
    
    let fallReason = 'Structural failure';
    if (towerCrashGame.structuralIntegrity <= 0) fallReason = 'Complete structural collapse';
    else if (towerCrashGame.currentFloor > 20) fallReason = 'Extreme height instability';
    else if (towerCrashGame.climbingSpeed === 'rocket' || towerCrashGame.climbingSpeed === 'sprint') fallReason = 'Excessive climbing speed';
    else if (towerCrashGame.weatherCondition === 'hurricane' || towerCrashGame.weatherCondition === 'storm') fallReason = 'Severe weather conditions';
    
    const floorsClimbed = Math.floor(towerCrashGame.currentFloor);
    document.getElementById('towerStatus').textContent = 
        `Fell from floor ${floorsClimbed} due to ${fallReason}!`;
    
    document.getElementById('climbStatus').textContent = 'CLIMB FAILED';
    
    resetTowerControls();
}

function reachTargetFloor() {
    towerCrashGame.isClimbing = false;
    towerCrashGame.consecutiveClimbs++;
    towerCrashGame.towerLevel += 3;
    
    // Calculate winnings
    const multiplier = 1 + (towerCrashGame.targetFloor - 1) * 0.5;
    const winnings = Math.floor(towerCrashGame.betAmount * multiplier);
    const netWinnings = winnings - towerCrashGame.betAmount;

    if (netWinnings > 0) {
        walletIntegration.processWin(netWinnings);
    }

    document.getElementById('towerResult').innerHTML = 
        `<span class="text-gold-400 neon-glow">🏗️ TARGET FLOOR REACHED! 🏗️</span>`;
    document.getElementById('towerStatus').innerHTML = 
        `Reached floor ${towerCrashGame.targetFloor}! Won ${winnings} GA!`;
    
    document.getElementById('climbStatus').textContent = 'CLIMB COMPLETED';
    
    resetTowerControls();
}

function resetTowerControls() {
    setTimeout(() => {
        document.getElementById('startClimb').disabled = false;
        document.getElementById('evacuate').disabled = true;
        document.getElementById('climbStatus').textContent = 'Ready to climb...';
        towerCrashGame.structuralIntegrity = 100;
        updateTowerDisplay();
    }, 3000);
}

function updateTowerDisplay() {
    const currentFloor = Math.floor(towerCrashGame.currentFloor);
    document.getElementById('towerFloorDisplay').textContent = currentFloor;
    document.getElementById('maxHeightDisplay').textContent = towerCrashGame.maxHeight + 'm';
    document.getElementById('towerLevelDisplay').textContent = towerCrashGame.towerLevel;
    document.getElementById('structuralIntegrityDisplay').textContent = Math.floor(towerCrashGame.structuralIntegrity) + '%';
    document.getElementById('consecutiveClimbs').textContent = towerCrashGame.consecutiveClimbs;
    document.getElementById('totalClimbs').textContent = towerCrashGame.totalClimbs;
    document.getElementById('fallCount').textContent = towerCrashGame.fallCount;
    
    // Update stability bar
    document.getElementById('stabilityBar').style.width = towerCrashGame.structuralIntegrity + '%';
    document.getElementById('stabilityLevel').textContent = Math.floor(towerCrashGame.structuralIntegrity) + '% Stable';
    
    // Change stability bar color based on integrity
    const stabilityBar = document.getElementById('stabilityBar');
    if (towerCrashGame.structuralIntegrity > 60) {
        stabilityBar.className = 'bg-green-500 h-4 rounded-full transition-all duration-300';
    } else if (towerCrashGame.structuralIntegrity > 30) {
        stabilityBar.className = 'bg-yellow-500 h-4 rounded-full transition-all duration-300';
    } else {
        stabilityBar.className = 'bg-red-500 h-4 rounded-full transition-all duration-300';
    }
    
    // Calculate success rate
    const successRate = towerCrashGame.totalClimbs > 0 ? 
        Math.floor((towerCrashGame.consecutiveClimbs / towerCrashGame.totalClimbs) * 100) : 0;
    document.getElementById('climbSuccessRate').textContent = successRate + '%';
    
    // Update engineering progress
    const progress = Math.min(100, (towerCrashGame.towerLevel / 50) * 100);
    document.getElementById('engineeringProgress').style.width = progress + '%';
    
    let engineeringLevel = 'Basic Tower Design';
    if (towerCrashGame.towerLevel > 40) engineeringLevel = 'Quantum Tower Architecture';
    else if (towerCrashGame.towerLevel > 30) engineeringLevel = 'Advanced Skyscraper Design';
    else if (towerCrashGame.towerLevel > 20) engineeringLevel = 'Professional Tower Engineering';
    else if (towerCrashGame.towerLevel > 10) engineeringLevel = 'Improved Structural Design';
    
    document.getElementById('engineeringText').textContent = engineeringLevel;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await walletIntegration.initialize();
        loadTowerCrashInfinityGame();
    } catch (error) {
        console.error('Failed to initialize wallet:', error);
        const gameContent = document.getElementById('gameContent');
        gameContent.innerHTML = `<div class="text-center text-red-500">Failed to load game. Please try again later.</div>`;
    }
});
