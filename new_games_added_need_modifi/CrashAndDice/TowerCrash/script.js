// Game state
let balance = 1000;
let towerCrashGame = {
    isClimbing: false,
    betAmount: 0,
    targetFloor: 8,
    currentFloor: 1,
    maxFloor: 1,
    towerType: 'wooden',
    climbingSpeed: 'normal',
    safetyGear: 'none',
    weatherCondition: 'clear',
    consecutiveClimbs: 0,
    totalClimbs: 0,
    fallCount: 0,
    maxHeight: 0,
    towerLevel: 1,
    structuralDamage: 0
};

// Tower types that affect stability
const TOWER_TYPES = [
    { name: 'wooden', stability: 0.82, description: 'Wooden Tower' },
    { name: 'brick', stability: 0.85, description: 'Brick Tower' },
    { name: 'steel', stability: 0.88, description: 'Steel Tower' },
    { name: 'concrete', stability: 0.91, description: 'Concrete Tower' },
    { name: 'titanium', stability: 0.94, description: 'Titanium Tower' }
];

// Climbing speeds
const CLIMBING_SPEEDS = [
    { name: 'slow', risk: 0.9, description: 'Slow Climb' },
    { name: 'normal', risk: 1.2, description: 'Normal Climb' },
    { name: 'fast', risk: 1.6, description: 'Fast Climb (1.6x risk)' },
    { name: 'sprint', risk: 2.1, description: 'Sprint Climb (2.1x risk)' },
    { name: 'rocket', risk: 2.8, description: 'Rocket Climb (2.8x risk)' }
];

// Safety gear
const SAFETY_GEAR = [
    { name: 'none', protection: 0.75, description: 'No Safety Gear' },
    { name: 'basic', protection: 0.82, description: 'Basic Harness' },
    { name: 'advanced', protection: 0.86, description: 'Advanced Gear' },
    { name: 'professional', protection: 0.89, description: 'Professional Gear' },
    { name: 'military', protection: 0.92, description: 'Military Grade' }
];

// Weather conditions
const WEATHER_CONDITIONS = [
    { name: 'clear', effect: 1.0, description: 'Clear Weather' },
    { name: 'windy', effect: 1.4, description: 'Windy (1.4x harder)' },
    { name: 'rain', effect: 1.8, description: 'Rainy (1.8x harder)' },
    { name: 'storm', effect: 2.3, description: 'Storm (2.3x harder)' },
    { name: 'hurricane', effect: 3.2, description: 'Hurricane (3.2x harder)' }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Tower Crash Controls -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">🏗️ TOWER CRASH 🏗️</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">💰 CLIMB BET</label>
                        <input type="number" id="towerBet" value="30" min="10" max="${balance}" 
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🎯 TARGET FLOOR</label>
                        <input type="number" id="targetFloor" value="8" min="2" max="50" 
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏗️ TOWER TYPE</label>
                        <select id="towerType" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="wooden" selected>🪵 Wooden Tower</option>
                            <option value="brick">🧱 Brick Tower</option>
                            <option value="steel">🏭 Steel Tower</option>
                            <option value="concrete">🏛️ Concrete Tower</option>
                            <option value="titanium">⚡ Titanium Tower (Extreme)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🏃 CLIMBING SPEED</label>
                        <select id="climbingSpeed" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="slow">🐌 Slow Climb</option>
                            <option value="normal" selected>🚶 Normal Climb</option>
                            <option value="fast">🏃 Fast Climb (1.6x risk)</option>
                            <option value="sprint">💨 Sprint Climb (2.1x risk)</option>
                            <option value="rocket">🚀 Rocket Climb (2.8x risk)</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🦺 SAFETY GEAR</label>
                        <select id="safetyGear" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="none" selected>No Safety Gear</option>
                            <option value="basic">Basic Harness</option>
                            <option value="advanced">Advanced Gear</option>
                            <option value="professional">Professional Gear</option>
                            <option value="military">Military Grade</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">🌤️ WEATHER</label>
                        <select id="weatherCondition" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="clear" selected>☀️ Clear Weather</option>
                            <option value="windy">💨 Windy (1.4x harder)</option>
                            <option value="rain">🌧️ Rainy (1.8x harder)</option>
                            <option value="storm">⛈️ Storm (2.3x harder)</option>
                            <option value="hurricane">🌪️ Hurricane (3.2x harder)</option>
                        </select>
                    </div>
                    
                    <div class="flex space-x-2 mb-4">
                        <button id="startClimb" class="cyber-button flex-1 py-3 rounded-lg font-bold text-white">
                            🧗 START CLIMB
                        </button>
                        <button id="evacuate" class="bg-red-600 hover:bg-red-700 flex-1 py-3 rounded-lg font-bold text-white" disabled>
                            🚁 EVACUATE
                        </button>
                    </div>
                    
                    <div class="text-center">
                        <div class="text-sm text-gray-400 mb-1">🎲 Success Probability</div>
                        <div id="towerWinChance" class="text-lg font-bold text-red-400">8%</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💎 Potential Payout</div>
                        <div id="towerPotentialPayout" class="text-2xl font-bold text-orange-400 neon-glow">240 GA</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">🏗️ Tower Level</div>
                        <div id="towerLevelDisplay" class="text-lg font-bold text-green-400">1</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">💥 Structural Damage</div>
                        <div id="structuralDamageDisplay" class="text-lg font-bold text-red-400">0%</div>
                    </div>
                </div>
            </div>
            
            <!-- Tower Display -->
            <div>
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h5 class="text-lg font-bold mb-4 text-orange-400 text-center">🏗️ CRASH TOWER 🏗️</h5>
                    
                    <!-- Tower Floor Display -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-orange-900/50 to-black/50 p-8 rounded-xl border border-orange-400/30">
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-2">🏢 Current Floor</div>
                                <div id="towerFloorDisplay" class="text-8xl font-bold text-orange-400 neon-glow mb-4">1</div>
                                <div class="text-sm text-gray-400">Target: <span id="towerTargetDisplay" class="text-orange-400">Floor 8</span></div>
                                <div class="text-sm text-gray-400">Max Height: <span id="maxHeightDisplay" class="text-green-400">0m</span></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Climb Status -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-1">🧗 Climb Status</div>
                            <div id="climbStatus" class="text-lg font-bold text-orange-400">Ready to climb...</div>
                            <div class="text-xs text-gray-400">Tower: <span id="towerStatus" class="text-blue-400">Wooden</span></div>
                            <div class="text-xs text-gray-400">Speed: <span id="speedStatus" class="text-green-400">Normal</span></div>
                            <div class="text-xs text-gray-400">Safety: <span id="safetyStatus" class="text-red-400">None</span></div>
                            <div class="text-xs text-gray-400">Weather: <span id="weatherStatus" class="text-cyan-400">Clear</span></div>
                        </div>
                    </div>
                    
                    <!-- Tower Stability -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">🏗️ Tower Stability</div>
                            <div class="w-full bg-black/50 rounded-full h-4">
                                <div id="stabilityBar" class="bg-gradient-to-r from-green-500 to-red-500 h-4 rounded-full transition-all duration-300" style="width: 100%"></div>
                            </div>
                            <div id="stabilityLevel" class="text-center mt-2 text-green-300">100% Stable</div>
                        </div>
                    </div>
                    
                    <!-- Danger Meter -->
                    <div class="mb-4">
                        <div class="bg-black/50 p-3 rounded-lg">
                            <div class="text-sm text-gray-400 mb-2">⚠️ Danger Level</div>
                            <div class="w-full bg-black/50 rounded-full h-3">
                                <div id="dangerMeter" class="bg-gradient-to-r from-yellow-500 to-red-500 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="dangerLevel" class="text-center mt-1 text-yellow-300">Safe</div>
                        </div>
                    </div>
                    
                    <div id="towerResult" class="text-center text-xl font-bold mb-4"></div>
                    <div id="towerStatusText" class="text-center text-gray-300"></div>
                </div>
                
                <!-- Climb Statistics -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">📊 CLIMB RECORDS</h5>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Successful Climbs</div>
                            <div id="consecutiveClimbs" class="text-lg font-bold text-green-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Total Climbs</div>
                            <div id="totalClimbs" class="text-lg font-bold text-cyan-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Falls</div>
                            <div id="fallCount" class="text-lg font-bold text-red-400">0</div>
                        </div>
                        <div class="text-center">
                            <div class="text-sm text-gray-400">Success Rate</div>
                            <div id="climbSuccessRate" class="text-lg font-bold text-orange-400">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Tower Engineering -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">🔧 TOWER ENGINEERING 🔧</h5>
                    <div class="w-full bg-black/50 rounded-full h-4">
                        <div id="engineeringProgress" class="bg-gradient-to-r from-orange-500 to-blue-500 h-4 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div id="engineeringText" class="text-center mt-2 text-blue-300">Basic Tower Design</div>
                </div>
            </div>
        </div>
    `;
    
    initializeTowerCrash();
}

function initializeTowerCrash() {
    document.getElementById('startClimb').addEventListener('click', startClimb);
    document.getElementById('evacuate').addEventListener('click', evacuate);
    document.getElementById('targetFloor').addEventListener('input', updateTowerPayout);
    document.getElementById('towerType').addEventListener('change', updateTowerType);
    document.getElementById('climbingSpeed').addEventListener('change', updateClimbingSpeed);
    document.getElementById('safetyGear').addEventListener('change', updateSafetyGear);
    document.getElementById('weatherCondition').addEventListener('change', updateWeatherCondition);
    
    updateTowerType();
    updateClimbingSpeed();
    updateSafetyGear();
    updateWeatherCondition();
    updateTowerPayout();
}

function updateTowerType() {
    const type = document.getElementById('towerType').value;
    towerCrashGame.towerType = type;
    
    const towerData = TOWER_TYPES.find(t => t.name === type);
    document.getElementById('towerStatus').textContent = towerData.description;
    
    updateTowerPayout();
}

function updateClimbingSpeed() {
    const speed = document.getElementById('climbingSpeed').value;
    towerCrashGame.climbingSpeed = speed;
    
    const speedData = CLIMBING_SPEEDS.find(s => s.name === speed);
    document.getElementById('speedStatus').textContent = speedData.description;
    
    updateTowerPayout();
}

function updateSafetyGear() {
    const gear = document.getElementById('safetyGear').value;
    towerCrashGame.safetyGear = gear;
    
    const gearData = SAFETY_GEAR.find(g => g.name === gear);
    document.getElementById('safetyStatus').textContent = gearData.description;
    
    updateTowerPayout();
}

function updateWeatherCondition() {
    const weather = document.getElementById('weatherCondition').value;
    towerCrashGame.weatherCondition = weather;
    
    const weatherData = WEATHER_CONDITIONS.find(w => w.name === weather);
    document.getElementById('weatherStatus').textContent = weatherData.description;
    
    // Update danger meter
    let dangerLevel = 0;
    switch(weather) {
        case 'clear': dangerLevel = 0; break;
        case 'windy': dangerLevel = 40; break;
        case 'rain': dangerLevel = 60; break;
        case 'storm': dangerLevel = 80; break;
        case 'hurricane': dangerLevel = 100; break;
    }
    
    document.getElementById('dangerMeter').style.width = dangerLevel + '%';
    document.getElementById('dangerLevel').textContent = weatherData.description;
    
    updateTowerPayout();
}

function updateTowerPayout() {
    const betAmount = parseInt(document.getElementById('towerBet').value) || 30;
    const targetFloor = parseInt(document.getElementById('targetFloor').value) || 8;
    
    towerCrashGame.targetFloor = targetFloor;
    document.getElementById('towerTargetDisplay').textContent = `Floor ${targetFloor}`;
    
    // Calculate extremely low success probability
    const towerData = TOWER_TYPES.find(t => t.name === towerCrashGame.towerType);
    const speedData = CLIMBING_SPEEDS.find(s => s.name === towerCrashGame.climbingSpeed);
    const gearData = SAFETY_GEAR.find(g => g.name === towerCrashGame.safetyGear);
    const weatherData = WEATHER_CONDITIONS.find(w => w.name === towerCrashGame.weatherCondition);
    
    // Base crash probability increases with floor height
    let baseCrashChance = 0.88 + (targetFloor / 50) * 0.08;
    
    // Apply all modifiers (make it extremely hard)
    const finalCrashChance = Math.min(0.996, 
        baseCrashChance / towerData.stability * speedData.risk * 
        weatherData.effect / gearData.protection);
    
    const successChance = Math.max(1, Math.floor((1 - finalCrashChance) * 100));
    const multiplier = 1 + (targetFloor - 1) * 0.8; // 0.8x per floor
    const potentialPayout = Math.floor(betAmount * multiplier);
    
    document.getElementById('towerWinChance').textContent = successChance + '%';
    document.getElementById('towerPotentialPayout').textContent = potentialPayout + ' GA';
}

function startClimb() {
    const betAmount = parseInt(document.getElementById('towerBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient climbing funds!');
        return;
    }
    
    if (towerCrashGame.isClimbing) return;
    
    // Deduct bet
    balance -= betAmount;
    updateBalance();
    
    towerCrashGame.betAmount = betAmount;
    towerCrashGame.isClimbing = true;
    towerCrashGame.currentFloor = 1;
    towerCrashGame.totalClimbs++;
    towerCrashGame.structuralDamage = 0;
    
    // Disable start button, enable evacuate
    document.getElementById('startClimb').disabled = true;
    document.getElementById('evacuate').disabled = false;
    
    document.getElementById('towerResult').textContent = '';
    document.getElementById('towerStatusText').textContent = 'Beginning tower climb...';
    document.getElementById('climbStatus').textContent = 'CLIMBING...';
    
    // Start tower climb simulation
    startTowerClimb();
}

function startTowerClimb() {
    const towerData = TOWER_TYPES.find(t => t.name === towerCrashGame.towerType);
    const speedData = CLIMBING_SPEEDS.find(s => s.name === towerCrashGame.climbingSpeed);
    const gearData = SAFETY_GEAR.find(g => g.name === towerCrashGame.safetyGear);
    const weatherData = WEATHER_CONDITIONS.find(w => w.name === towerCrashGame.weatherCondition);
    
    const climbInterval = setInterval(() => {
        if (!towerCrashGame.isClimbing) {
            clearInterval(climbInterval);
            return;
        }
        
        // Calculate crash probability (extremely high - 88-99.6% chance)
        const baseCrashChance = 0.88 + (towerCrashGame.targetFloor / 50) * 0.08;
        const finalCrashChance = Math.min(0.996, 
            baseCrashChance / towerData.stability * speedData.risk * 
            weatherData.effect / gearData.protection);
        
        // Increase crash chance as we climb higher
        const heightCrashChance = finalCrashChance + (towerCrashGame.currentFloor - 1) * 0.008;
        
        // Increase structural damage
        towerCrashGame.structuralDamage = Math.min(100, towerCrashGame.structuralDamage + 1.2);
        if (towerCrashGame.structuralDamage >= 80) {
            // Critical structural failure
            const structuralCrashChance = heightCrashChance + (towerCrashGame.structuralDamage - 80) * 0.04;
            if (Math.random() < structuralCrashChance) {
                fallFromTower();
                clearInterval(climbInterval);
                return;
            }
        } else if (Math.random() < heightCrashChance) {
            // Normal fall
            fallFromTower();
            clearInterval(climbInterval);
            return;
        }
        
        // Climb to next floor (slower progression)
        const floorIncrement = 0.12 + (Math.random() * 0.18);
        towerCrashGame.currentFloor += floorIncrement;
        towerCrashGame.maxHeight = Math.max(towerCrashGame.maxHeight, 
            Math.floor((towerCrashGame.currentFloor - 1) * 4)); // Height in meters
        
        // Check if target reached (extremely rare)
        if (towerCrashGame.currentFloor >= towerCrashGame.targetFloor) {
            reachTargetFloor();
            clearInterval(climbInterval);
            return;
        }
        
        updateTowerDisplay();
        
    }, 180); // Slower updates for tower climbing
}

function evacuate() {
    if (!towerCrashGame.isClimbing) return;
    
    towerCrashGame.isClimbing = false;
    towerCrashGame.consecutiveClimbs++;
    towerCrashGame.towerLevel++;
    
    // Calculate winnings based on floors climbed
    const floorsClimbed = Math.floor(towerCrashGame.currentFloor);
    const multiplier = 1 + (floorsClimbed - 1) * 0.8;
    const winnings = Math.floor(towerCrashGame.betAmount * multiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('towerResult').innerHTML = 
        `<span class="text-green-400 neon-glow">🚁 SUCCESSFUL EVACUATION! 🚁</span>`;
    document.getElementById('towerStatusText').innerHTML = 
        `Evacuated from floor ${floorsClimbed}! Won ${winnings} GA!`;
    
    document.getElementById('climbStatus').textContent = 'EVACUATION SUCCESSFUL';
    
    resetTowerControls();
}

function fallFromTower() {
    towerCrashGame.isClimbing = false;
    towerCrashGame.fallCount++;
    towerCrashGame.consecutiveClimbs = 0;
    
    document.getElementById('towerResult').innerHTML = 
        `<span class="text-red-400">💥 TOWER COLLAPSE! 💥</span>`;
    
    let fallReason = 'Structural failure';
    if (towerCrashGame.structuralDamage >= 100) fallReason = 'Complete structural collapse';
    else if (towerCrashGame.currentFloor > 15) fallReason = 'Extreme height instability';
    else if (towerCrashGame.climbingSpeed === 'rocket' || towerCrashGame.climbingSpeed === 'sprint') fallReason = 'Excessive climbing speed';
    else if (towerCrashGame.weatherCondition === 'hurricane' || towerCrashGame.weatherCondition === 'storm') fallReason = 'Severe weather conditions';
    
    const floorsClimbed = Math.floor(towerCrashGame.currentFloor);
    document.getElementById('towerStatusText').textContent = 
        `Fell from floor ${floorsClimbed} due to ${fallReason}!`;
    
    document.getElementById('climbStatus').textContent = 'CLIMB FAILED';
    
    resetTowerControls();
}

function reachTargetFloor() {
    towerCrashGame.isClimbing = false;
    towerCrashGame.consecutiveClimbs++;
    towerCrashGame.towerLevel += 3;
    
    // Calculate winnings
    const multiplier = 1 + (towerCrashGame.targetFloor - 1) * 0.8;
    const winnings = Math.floor(towerCrashGame.betAmount * multiplier);
    balance += winnings;
    updateBalance();
    
    document.getElementById('towerResult').innerHTML = 
        `<span class="text-gold-400 neon-glow">🏗️ TARGET FLOOR REACHED! 🏗️</span>`;
    document.getElementById('towerStatusText').innerHTML = 
        `Reached floor ${towerCrashGame.targetFloor}! Won ${winnings} GA!`;
    
    document.getElementById('climbStatus').textContent = 'CLIMB COMPLETED';
    
    resetTowerControls();
}

function resetTowerControls() {
    setTimeout(() => {
        document.getElementById('startClimb').disabled = false;
        document.getElementById('evacuate').disabled = true;
        document.getElementById('climbStatus').textContent = 'Ready to climb...';
        towerCrashGame.structuralDamage = 0;
        updateTowerDisplay();
    }, 3000);
}

function updateTowerDisplay() {
    const currentFloor = Math.floor(towerCrashGame.currentFloor);
    const stability = Math.max(0, 100 - towerCrashGame.structuralDamage);
    
    document.getElementById('towerFloorDisplay').textContent = currentFloor;
    document.getElementById('maxHeightDisplay').textContent = towerCrashGame.maxHeight + 'm';
    document.getElementById('towerLevelDisplay').textContent = towerCrashGame.towerLevel;
    document.getElementById('structuralDamageDisplay').textContent = Math.floor(towerCrashGame.structuralDamage) + '%';
    document.getElementById('consecutiveClimbs').textContent = towerCrashGame.consecutiveClimbs;
    document.getElementById('totalClimbs').textContent = towerCrashGame.totalClimbs;
    document.getElementById('fallCount').textContent = towerCrashGame.fallCount;
    
    // Update stability bar
    document.getElementById('stabilityBar').style.width = stability + '%';
    document.getElementById('stabilityLevel').textContent = Math.floor(stability) + '% Stable';
    
    // Change stability bar color based on integrity
    const stabilityBar = document.getElementById('stabilityBar');
    if (stability > 60) {
        stabilityBar.className = 'bg-green-500 h-4 rounded-full transition-all duration-300';
    } else if (stability > 30) {
        stabilityBar.className = 'bg-yellow-500 h-4 rounded-full transition-all duration-300';
    } else {
        stabilityBar.className = 'bg-red-500 h-4 rounded-full transition-all duration-300';
    }
    
    // Calculate success rate
    const successRate = towerCrashGame.totalClimbs > 0 ? 
        Math.floor((towerCrashGame.consecutiveClimbs / towerCrashGame.totalClimbs) * 100) : 0;
    document.getElementById('climbSuccessRate').textContent = successRate + '%';
    
    // Update engineering progress
    const progress = Math.min(100, (towerCrashGame.towerLevel / 30) * 100);
    document.getElementById('engineeringProgress').style.width = progress + '%';
    
    let engineeringLevel = 'Basic Tower Design';
    if (towerCrashGame.towerLevel > 25) engineeringLevel = 'Master Tower Architecture';
    else if (towerCrashGame.towerLevel > 20) engineeringLevel = 'Advanced Skyscraper Design';
    else if (towerCrashGame.towerLevel > 15) engineeringLevel = 'Professional Tower Engineering';
    else if (towerCrashGame.towerLevel > 10) engineeringLevel = 'Improved Structural Design';
    
    document.getElementById('engineeringText').textContent = engineeringLevel;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadArcticAdventureGame();
});

