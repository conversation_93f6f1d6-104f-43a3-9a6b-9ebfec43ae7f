// Rhythm Reels Royale - Beat-Matching Slot with Combo Multipliers
// Skill-Based Hybrid Implementation with Music/Rhythm Theme
// Designed to maintain 3-5% player win rate with rhythm skill elements

// Game state
let balance = 1000;

// Game state object with rhythm system
let rhythmReelsGame = {
    isPlaying: false,
    musicGenre: 'electronic', // electronic, rock, jazz, classical, hip_hop
    difficulty: 'normal', // easy, normal, hard, expert, master
    betAmount: 0,
    totalBet: 0,

    // Music and rhythm system
    music: {
        bpm: 120, // beats per minute
        timeSignature: '4/4',
        key: 'C Major',
        tempo: 'moderate',
        volume: 0.70,
        currentBeat: 1,
        totalBeats: 16,
        beatAccuracy: 0.65, // 65% accuracy (improved)
        rhythmStreak: 0,
        perfectBeats: 0,
        missedBeats: 0
    },

    // Beat-matching mechanics
    beatMatching: {
        active: false,
        targetBeat: 1,
        playerInput: 0,
        timing: 0.60, // 60% timing accuracy
        precision: 0.55, // 55% precision
        combo: 0,
        maxCombo: 0,
        comboMultiplier: 1.0,
        perfectWindow: 0.15, // 150ms perfect window (increased)
        goodWindow: 0.30, // 300ms good window (increased)
        beatScore: 0,
        totalScore: 0
    },

    // Slot reels (5 reels, 4 rows for rhythm grid)
    reels: [
        [], [], [], [], []
    ],

    // Musical symbols with rhythm values
    symbols: [
        { name: 'Whole Note', value: 100, rarity: 0.08, beats: 4, multiplier: 2.5 },
        { name: 'Half Note', value: 95, rarity: 0.10, beats: 2, multiplier: 2.2 },
        { name: 'Quarter Note', value: 90, rarity: 0.12, beats: 1, multiplier: 2.0 },
        { name: 'Eighth Note', value: 85, rarity: 0.15, beats: 0.5, multiplier: 1.8 },
        { name: 'Treble Clef', value: 80, rarity: 0.18, beats: 1, multiplier: 1.6 },
        { name: 'Bass Clef', value: 75, rarity: 0.20, beats: 1, multiplier: 1.4 },
        { name: 'Sharp', value: 70, rarity: 0.25, beats: 0.5, multiplier: 1.3 },
        { name: 'Flat', value: 65, rarity: 0.30, beats: 0.5, multiplier: 1.2 },
        { name: 'Natural', value: 60, rarity: 0.35, beats: 0.5, multiplier: 1.1 },
        { name: 'Rest', value: 55, rarity: 0.40, beats: 1, multiplier: 1.0 },
        { name: 'Metronome', value: 50, rarity: 0.45, beats: 1, multiplier: 0.9 },
        { name: 'Music Note', value: 45, rarity: 0.50, beats: 0.25, multiplier: 0.8 }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        perfectBeats: 0,
        maxCombo: 0,
        totalBeatsHit: 0,
        rhythmAccuracy: 0.65,
        skillPoints: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Music genres with balanced skill requirements (3-5% win rate)
const MUSIC_GENRES = {
    electronic: {
        name: 'Electronic',
        bpm: 128,
        skillWeight: 0.30, // 30% skill influence (increased)
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.90, // Good payouts
        beatBonus: 0.25, // 25% beat bonus
        comboBonus: 0.30 // 30% combo bonus
    },
    rock: {
        name: 'Rock',
        bpm: 120,
        skillWeight: 0.35, // 35% skill influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.95, // Better payouts
        beatBonus: 0.30, // 30% beat bonus
        comboBonus: 0.35 // 35% combo bonus
    },
    jazz: {
        name: 'Jazz',
        bpm: 100,
        skillWeight: 0.40, // 40% skill influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.00, // Full payouts
        beatBonus: 0.35, // 35% beat bonus
        comboBonus: 0.40 // 40% combo bonus
    },
    classical: {
        name: 'Classical',
        bpm: 80,
        skillWeight: 0.45, // 45% skill influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        beatBonus: 0.40, // 40% beat bonus
        comboBonus: 0.45 // 45% combo bonus
    },
    hip_hop: {
        name: 'Hip-Hop',
        bpm: 90,
        skillWeight: 0.35, // 35% skill influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.95, // Good payouts
        beatBonus: 0.30, // 30% beat bonus
        comboBonus: 0.35 // 35% combo bonus
    }
};

const DIFFICULTY_LEVELS = {
    easy: {
        name: 'Easy',
        timingWindow: 0.40, // 400ms window
        comboRequirement: 3, // 3 hits for combo
        skillBonus: 0.20 // 20% skill bonus
    },
    normal: {
        name: 'Normal',
        timingWindow: 0.30, // 300ms window
        comboRequirement: 4, // 4 hits for combo
        skillBonus: 0.25 // 25% skill bonus
    },
    hard: {
        name: 'Hard',
        timingWindow: 0.20, // 200ms window
        comboRequirement: 5, // 5 hits for combo
        skillBonus: 0.30 // 30% skill bonus
    },
    expert: {
        name: 'Expert',
        timingWindow: 0.15, // 150ms window
        comboRequirement: 6, // 6 hits for combo
        skillBonus: 0.35 // 35% skill bonus
    },
    master: {
        name: 'Master',
        timingWindow: 0.10, // 100ms window
        comboRequirement: 8, // 8 hits for combo
        skillBonus: 0.40 // 40% skill bonus
    }
};

// Improved payout table with rhythm theme (3-5% win rate)
const RHYTHM_PAYOUTS = {
    // Perfect rhythm combinations (moderately reduced)
    PERFECT_SYMPHONY: 800, // Reduced from 1600:1 but still excellent
    RHYTHM_MASTER: 600, // Reduced from 1200:1
    BEAT_PERFECT: 400, // Reduced from 800:1
    COMBO_KING: 300, // Reduced from 600:1

    // Musical combinations
    FIVE_WHOLE_NOTES: 500, // Reduced from 1000:1
    FIVE_HALF_NOTES: 300, // Reduced from 600:1
    FIVE_QUARTER_NOTES: 200, // Reduced from 400:1

    // Rhythm bonuses (actually apply more often)
    BEAT_ACCURACY: 0.60, // 60% of displayed bonus (increased)
    COMBO_MULTIPLIER: 0.50, // 50% of displayed bonus (increased)
    RHYTHM_STREAK: 0.40, // 40% of displayed bonus (increased)
    PERFECT_TIMING: 0.35 // 35% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadRhythmReelsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">RHYTHM CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">MUSIC GENRE</label>
                        <select id="musicGenre" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="electronic">Electronic (128 BPM)</option>
                            <option value="rock">Rock (120 BPM)</option>
                            <option value="jazz">Jazz (100 BPM)</option>
                            <option value="classical">Classical (80 BPM)</option>
                            <option value="hip_hop">Hip-Hop (90 BPM)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Easy (400ms)</option>
                            <option value="normal" selected>Normal (300ms)</option>
                            <option value="hard">Hard (200ms)</option>
                            <option value="expert">Expert (150ms)</option>
                            <option value="master">Master (100ms)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startRhythm" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START RHYTHM REELS
                    </button>

                    <div id="rhythmActions" class="space-y-2 hidden">
                        <div class="text-center mb-3">
                            <div class="text-sm text-purple-400 mb-2">HIT THE BEAT!</div>
                            <button id="beatButton" class="w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold text-xl transition-all duration-150 transform hover:scale-105 active:scale-95">
                                ♪
                            </button>
                        </div>

                        <div class="text-center">
                            <div class="text-xs text-gray-400">Space bar or click to hit beat</div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Combo</div>
                        <div id="comboDisplay" class="text-lg font-bold text-purple-400">0x</div>
                    </div>
                </div>

                <!-- Music Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">MUSIC STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="currentBPM" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">BPM: 120</div>
                        </div>
                        <div id="beatAccuracy" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">ACCURACY: 65%</div>
                        </div>
                        <div id="rhythmStreak" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">STREAK: 0</div>
                        </div>
                        <div id="perfectBeats" class="text-center p-2 rounded bg-black/50">
                            <div class="text-pink-400 font-bold">PERFECT: 0</div>
                        </div>
                    </div>
                </div>

                <!-- Improved Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">RHYTHM PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Perfect Rhythms:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Symphony:</span>
                            <span class="text-purple-400">800:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rhythm Master:</span>
                            <span class="text-purple-400">600:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Beat Perfect:</span>
                            <span class="text-purple-400">400:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Rhythm Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Beat Accuracy:</span>
                            <span class="text-purple-400">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Combo Multiplier:</span>
                            <span class="text-purple-400">50%</span>
                        </div>
                        <div class="text-xs text-purple-400 mt-2">*Rhythm skill improves payouts</div>
                        <div class="text-xs text-purple-400">*Combos multiply winnings</div>
                    </div>
                </div>
            </div>

            <!-- Main Rhythm Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <!-- Rhythm Reels Arena -->
                    <div id="rhythmArena" class="relative bg-gradient-to-br from-black via-purple-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Musical Background -->
                        <div id="musicalBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="rhythmGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#ff00ff;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#8000ff;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#4000ff;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="musicPattern" width="80" height="80" patternUnits="userSpaceOnUse">
                                        <circle cx="40" cy="40" r="20" fill="none" stroke="#ff00ff" stroke-width="2" opacity="0.3"/>
                                        <path d="M30,30 Q40,20 50,30 Q40,40 30,30" fill="#8000ff" opacity="0.4"/>
                                        <circle cx="20" cy="60" r="3" fill="#ff00ff" opacity="0.5"/>
                                        <circle cx="60" cy="20" r="3" fill="#ff00ff" opacity="0.5"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#musicPattern)" />
                                <circle id="rhythmCore" cx="50%" cy="50%" r="25%" fill="url(#rhythmGradient)" class="animate-pulse" />
                                <g id="beatEffects">
                                    <!-- Beat effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Beat Visualization -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">BEAT TRACK</div>
                                <div id="beatTrack" class="flex space-x-2">
                                    <!-- 16 beat indicators -->
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                    <div class="beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400"></div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Hit beats as they light up</div>
                            </div>
                        </div>

                        <!-- Rhythm Reels Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">RHYTHM REELS</div>
                                <div id="reelsDisplay" class="grid grid-cols-5 gap-2">
                                    <!-- 5 reels, 4 rows each for musical structure -->
                                    <div id="reel1" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel2" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel3" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel4" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel5" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Combo Multiplier Display -->
                        <div id="comboMultiplier" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">COMBO MULTIPLIER</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="comboBar" class="bg-gradient-to-r from-purple-400 to-pink-400 h-4 rounded-full transition-all duration-300 animate-pulse" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>1x</span>
                                    <span id="currentCombo">Combo: 0x</span>
                                    <span>10x</span>
                                </div>
                            </div>
                        </div>

                        <!-- Timing Indicator -->
                        <div id="timingIndicator" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">TIMING</div>
                                <div class="w-20 bg-gray-700 rounded-full h-3">
                                    <div id="timingBar" class="bg-yellow-400 h-3 rounded-full transition-all duration-100" style="width: 60%"></div>
                                </div>
                                <div class="text-xs text-yellow-400 mt-1">60%</div>
                            </div>
                        </div>

                        <!-- Beat Counter -->
                        <div id="beatCounter" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">BEAT</div>
                                <div class="text-2xl font-bold text-white text-center">1/16</div>
                                <div class="text-xs text-gray-400 mt-1">Current</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Ready to rock...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="rhythmEvent" class="text-sm font-bold text-purple-400 hidden animate-pulse">PERFECT BEAT!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Rhythm Reels Royale - Match the Beat for Combo Multipliers</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-purple-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-purple-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Perfect Beats</div>
                <div id="perfectBeatsCount" class="text-xl font-bold text-pink-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Max Combo</div>
                <div id="maxCombo" class="text-xl font-bold text-cyan-400">0x</div>
            </div>
        </div>
    `;

    initializeRhythmReels();
}

// Initialize the game
function initializeRhythmReels() {
    document.getElementById('startRhythm').addEventListener('click', startRhythmReels);
    document.getElementById('beatButton').addEventListener('click', hitBeat);

    // Add keyboard listener for spacebar
    document.addEventListener('keydown', (e) => {
        if (e.code === 'Space' && rhythmReelsGame.isPlaying) {
            e.preventDefault();
            hitBeat();
        }
    });

    // Initialize rhythm systems
    initializeRhythmSystems();
    generateBeatEffects();
    updateGameStats();
}

// Initialize rhythm systems
function initializeRhythmSystems() {
    // Reset music system
    rhythmReelsGame.music.bpm = 120;
    rhythmReelsGame.music.timeSignature = '4/4';
    rhythmReelsGame.music.key = getRandomKey();
    rhythmReelsGame.music.tempo = 'moderate';
    rhythmReelsGame.music.volume = 0.70;
    rhythmReelsGame.music.currentBeat = 1;
    rhythmReelsGame.music.totalBeats = 16;
    rhythmReelsGame.music.beatAccuracy = Math.min(0.85, 0.65 + (rhythmReelsGame.stats.skillPoints * 0.01));
    rhythmReelsGame.music.rhythmStreak = 0;
    rhythmReelsGame.music.perfectBeats = 0;
    rhythmReelsGame.music.missedBeats = 0;

    // Reset beat-matching mechanics
    rhythmReelsGame.beatMatching.active = false;
    rhythmReelsGame.beatMatching.targetBeat = 1;
    rhythmReelsGame.beatMatching.playerInput = 0;
    rhythmReelsGame.beatMatching.timing = 0.60;
    rhythmReelsGame.beatMatching.precision = 0.55;
    rhythmReelsGame.beatMatching.combo = 0;
    rhythmReelsGame.beatMatching.maxCombo = 0;
    rhythmReelsGame.beatMatching.comboMultiplier = 1.0;
    rhythmReelsGame.beatMatching.perfectWindow = 0.15;
    rhythmReelsGame.beatMatching.goodWindow = 0.30;
    rhythmReelsGame.beatMatching.beatScore = 0;
    rhythmReelsGame.beatMatching.totalScore = 0;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        rhythmReelsGame.reels[i] = [];
    }

    updateRhythmDisplay();
}

// Generate beat effects
function generateBeatEffects() {
    const container = document.getElementById('beatEffects');
    container.innerHTML = '';

    for (let i = 0; i < 8; i++) {
        const effect = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        effect.setAttribute('cx', `${Math.random() * 100}%`);
        effect.setAttribute('cy', `${Math.random() * 100}%`);
        effect.setAttribute('r', `${Math.random() * 3 + 1}%`);
        effect.setAttribute('fill', '#ff00ff');
        effect.setAttribute('opacity', '0.6');
        effect.classList.add('animate-pulse');
        effect.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(effect);
    }
}

// Get random musical key
function getRandomKey() {
    const keys = ['C Major', 'G Major', 'D Major', 'A Major', 'E Major', 'F Major', 'Bb Major'];
    return keys[Math.floor(Math.random() * keys.length)];
}

// Start rhythm reels
function startRhythmReels() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    rhythmReelsGame.isPlaying = true;
    rhythmReelsGame.betAmount = betAmount;
    rhythmReelsGame.totalBet = betAmount;
    rhythmReelsGame.musicGenre = document.getElementById('musicGenre').value;
    rhythmReelsGame.difficulty = document.getElementById('difficulty').value;

    // Activate rhythm systems
    activateRhythmSystems();

    // Start beat sequence
    setTimeout(() => {
        startBeatSequence();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startRhythm').disabled = true;
    document.getElementById('rhythmActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Get ready to hit the beat...';
}

// Activate rhythm systems
function activateRhythmSystems() {
    const genreData = MUSIC_GENRES[rhythmReelsGame.musicGenre];
    const difficultyData = DIFFICULTY_LEVELS[rhythmReelsGame.difficulty];

    rhythmReelsGame.music.bpm = genreData.bpm;
    rhythmReelsGame.beatMatching.perfectWindow = difficultyData.timingWindow;
    rhythmReelsGame.beatMatching.goodWindow = difficultyData.timingWindow * 2;

    // Update music information
    updateMusicInfo();

    // Update visual effects
    updateRhythmDisplay();
    updateBeatEffects();
}

// Update music information
function updateMusicInfo() {
    document.getElementById('currentBPM').innerHTML =
        `<div class="text-yellow-400 font-bold">BPM: ${rhythmReelsGame.music.bpm}</div>`;
    document.getElementById('beatAccuracy').innerHTML =
        `<div class="text-green-400 font-bold">ACCURACY: ${Math.floor(rhythmReelsGame.music.beatAccuracy * 100)}%</div>`;
    document.getElementById('rhythmStreak').innerHTML =
        `<div class="text-blue-400 font-bold">STREAK: ${rhythmReelsGame.music.rhythmStreak}</div>`;
    document.getElementById('perfectBeats').innerHTML =
        `<div class="text-pink-400 font-bold">PERFECT: ${rhythmReelsGame.music.perfectBeats}</div>`;
}

// Start beat sequence
function startBeatSequence() {
    rhythmReelsGame.beatMatching.active = true;
    rhythmReelsGame.music.currentBeat = 1;

    document.getElementById('gameStatus').textContent = 'Hit the beats as they light up!';

    // Start beat loop
    const beatInterval = 60000 / rhythmReelsGame.music.bpm; // ms per beat
    let beatTimer = setInterval(() => {
        if (!rhythmReelsGame.isPlaying || rhythmReelsGame.music.currentBeat > rhythmReelsGame.music.totalBeats) {
            clearInterval(beatTimer);
            if (rhythmReelsGame.isPlaying) {
                setTimeout(() => {
                    spinRhythmReels();
                }, 500);
            }
            return;
        }

        // Light up current beat
        lightUpBeat(rhythmReelsGame.music.currentBeat - 1);

        // Update beat counter
        document.getElementById('beatCounter').querySelector('.text-2xl').textContent =
            `${rhythmReelsGame.music.currentBeat}/${rhythmReelsGame.music.totalBeats}`;

        rhythmReelsGame.music.currentBeat++;
    }, beatInterval);
}

// Light up beat indicator
function lightUpBeat(beatIndex) {
    const beatIndicators = document.querySelectorAll('.beat-indicator');

    // Reset all indicators
    beatIndicators.forEach(indicator => {
        indicator.className = 'beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400';
    });

    // Light up current beat
    if (beatIndicators[beatIndex]) {
        beatIndicators[beatIndex].className = 'beat-indicator w-4 h-4 rounded-full bg-purple-400 border border-purple-400 animate-pulse';

        // Set target beat for player input
        rhythmReelsGame.beatMatching.targetBeat = beatIndex + 1;
    }
}

// Hit beat with skill influence (3-5% win rate)
function hitBeat() {
    if (!rhythmReelsGame.beatMatching.active) return;

    const currentTime = Date.now();
    const genreData = MUSIC_GENRES[rhythmReelsGame.musicGenre];
    const difficultyData = DIFFICULTY_LEVELS[rhythmReelsGame.difficulty];

    // Calculate timing accuracy
    const beatInterval = 60000 / rhythmReelsGame.music.bpm;
    const expectedTime = (rhythmReelsGame.beatMatching.targetBeat - 1) * beatInterval;
    const timingDifference = Math.abs(currentTime % beatInterval - expectedTime % beatInterval);

    // Normalize timing difference
    const normalizedTiming = Math.min(timingDifference / beatInterval, 0.5);

    // Apply skill influence (increased)
    const skillBonus = rhythmReelsGame.stats.skillPoints * 0.005;
    const adjustedTiming = Math.max(0, normalizedTiming - skillBonus);

    // Determine hit quality
    let hitQuality = 'miss';
    let scoreMultiplier = 0;

    if (adjustedTiming <= rhythmReelsGame.beatMatching.perfectWindow) {
        hitQuality = 'perfect';
        scoreMultiplier = 1.0;
        rhythmReelsGame.music.perfectBeats++;
        rhythmReelsGame.stats.perfectBeats++;
        rhythmReelsGame.beatMatching.combo++;
        rhythmReelsGame.music.rhythmStreak++;
    } else if (adjustedTiming <= rhythmReelsGame.beatMatching.goodWindow) {
        hitQuality = 'good';
        scoreMultiplier = 0.7;
        rhythmReelsGame.beatMatching.combo++;
        rhythmReelsGame.music.rhythmStreak++;
    } else if (adjustedTiming <= 0.40) {
        hitQuality = 'fair';
        scoreMultiplier = 0.4;
        rhythmReelsGame.beatMatching.combo = Math.max(0, rhythmReelsGame.beatMatching.combo - 1);
    } else {
        hitQuality = 'miss';
        scoreMultiplier = 0;
        rhythmReelsGame.music.missedBeats++;
        rhythmReelsGame.beatMatching.combo = 0;
        rhythmReelsGame.music.rhythmStreak = 0;
    }

    // Update combo multiplier
    updateComboMultiplier();

    // Update timing display
    const timingPercentage = Math.max(0, 100 - (adjustedTiming * 200));
    document.getElementById('timingBar').style.width = `${timingPercentage}%`;
    document.getElementById('timingIndicator').querySelector('.text-xs:last-child').textContent = `${Math.floor(timingPercentage)}%`;

    // Visual feedback
    provideBeatFeedback(hitQuality);

    // Update stats
    rhythmReelsGame.stats.totalBeatsHit++;
    rhythmReelsGame.stats.skillPoints += scoreMultiplier;

    // Update max combo
    if (rhythmReelsGame.beatMatching.combo > rhythmReelsGame.beatMatching.maxCombo) {
        rhythmReelsGame.beatMatching.maxCombo = rhythmReelsGame.beatMatching.combo;
        rhythmReelsGame.stats.maxCombo = rhythmReelsGame.beatMatching.maxCombo;
    }

    // Update displays
    updateMusicInfo();
    updateComboDisplay();
}

// Update combo multiplier
function updateComboMultiplier() {
    const difficultyData = DIFFICULTY_LEVELS[rhythmReelsGame.difficulty];

    if (rhythmReelsGame.beatMatching.combo >= difficultyData.comboRequirement) {
        rhythmReelsGame.beatMatching.comboMultiplier = 1.0 + (rhythmReelsGame.beatMatching.combo * 0.2);
    } else {
        rhythmReelsGame.beatMatching.comboMultiplier = 1.0;
    }

    // Cap at 10x multiplier
    rhythmReelsGame.beatMatching.comboMultiplier = Math.min(10.0, rhythmReelsGame.beatMatching.comboMultiplier);
}

// Provide beat feedback
function provideBeatFeedback(quality) {
    const beatButton = document.getElementById('beatButton');

    // Reset button
    beatButton.className = 'w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold text-xl transition-all duration-150 transform hover:scale-105 active:scale-95';

    // Add quality-specific styling
    switch (quality) {
        case 'perfect':
            beatButton.classList.add('ring-4', 'ring-yellow-400', 'bg-gradient-to-br', 'from-yellow-400', 'to-orange-400');
            document.getElementById('rhythmEvent').classList.remove('hidden');
            document.getElementById('rhythmEvent').textContent = 'PERFECT BEAT!';
            break;
        case 'good':
            beatButton.classList.add('ring-2', 'ring-green-400', 'bg-gradient-to-br', 'from-green-400', 'to-blue-400');
            document.getElementById('rhythmEvent').classList.remove('hidden');
            document.getElementById('rhythmEvent').textContent = 'GOOD BEAT!';
            break;
        case 'fair':
            beatButton.classList.add('ring-1', 'ring-yellow-400');
            document.getElementById('rhythmEvent').classList.remove('hidden');
            document.getElementById('rhythmEvent').textContent = 'FAIR BEAT';
            break;
        case 'miss':
            beatButton.classList.add('ring-2', 'ring-red-400', 'bg-gradient-to-br', 'from-red-400', 'to-red-600');
            document.getElementById('rhythmEvent').classList.remove('hidden');
            document.getElementById('rhythmEvent').textContent = 'MISSED!';
            break;
    }

    // Reset feedback after delay
    setTimeout(() => {
        beatButton.className = 'w-20 h-20 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold text-xl transition-all duration-150 transform hover:scale-105 active:scale-95';
        document.getElementById('rhythmEvent').classList.add('hidden');
    }, 500);
}

// Update combo display
function updateComboDisplay() {
    const combo = rhythmReelsGame.beatMatching.combo;
    const multiplier = rhythmReelsGame.beatMatching.comboMultiplier;
    const comboPercentage = Math.min(100, (combo / 10) * 100);

    document.getElementById('comboBar').style.width = `${comboPercentage}%`;
    document.getElementById('currentCombo').textContent = `Combo: ${multiplier.toFixed(1)}x`;
    document.getElementById('comboDisplay').textContent = `${multiplier.toFixed(1)}x`;
}

// Spin rhythm reels with beat-matching influence (3-5% win rate)
function spinRhythmReels() {
    rhythmReelsGame.beatMatching.active = false;
    document.getElementById('gameStatus').textContent = 'Spinning rhythm reels...';

    // Calculate rhythm accuracy
    const totalBeats = rhythmReelsGame.music.totalBeats;
    const perfectBeats = rhythmReelsGame.music.perfectBeats;
    const missedBeats = rhythmReelsGame.music.missedBeats;
    const rhythmAccuracy = (totalBeats - missedBeats) / totalBeats;

    // Update rhythm accuracy
    rhythmReelsGame.stats.rhythmAccuracy = rhythmAccuracy;

    // Generate symbols for each reel with rhythm influence
    for (let reel = 0; reel < 5; reel++) {
        rhythmReelsGame.reels[reel] = [];
        for (let row = 0; row < 4; row++) {
            const symbol = generateRhythmInfluencedSymbol(rhythmAccuracy);
            rhythmReelsGame.reels[reel].push(symbol);
        }
    }

    // Animate reel spinning with musical effects
    animateRhythmReelSpin();

    // Resolve after reels stop
    setTimeout(() => {
        resolveRhythmSpin();
    }, 4000);
}

// Generate rhythm-influenced symbol (improved for 3-5% win rate)
function generateRhythmInfluencedSymbol(rhythmAccuracy) {
    const genreData = MUSIC_GENRES[rhythmReelsGame.musicGenre];

    // Apply rhythm accuracy bonus (increased)
    if (rhythmAccuracy >= 0.80 && Math.random() < 0.30) { // 30% chance for high-value symbols
        const highValueSymbols = rhythmReelsGame.symbols.filter(s => s.value >= 80);
        return highValueSymbols[Math.floor(Math.random() * highValueSymbols.length)];
    } else if (rhythmAccuracy >= 0.60 && Math.random() < 0.50) { // 50% chance for medium-value symbols
        const mediumValueSymbols = rhythmReelsGame.symbols.filter(s => s.value >= 60 && s.value < 80);
        return mediumValueSymbols[Math.floor(Math.random() * mediumValueSymbols.length)];
    } else {
        // Random selection from all symbols
        return selectSymbolByBalancedRarity();
    }
}

// Select symbol by balanced rarity (improved distribution)
function selectSymbolByBalancedRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Normal order to give fair distribution
    for (let i = 0; i < rhythmReelsGame.symbols.length; i++) {
        const symbol = rhythmReelsGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to random symbol
    return rhythmReelsGame.symbols[Math.floor(Math.random() * rhythmReelsGame.symbols.length)];
}

// Animate rhythm reel spinning
function animateRhythmReelSpin() {
    for (let reel = 0; reel < 5; reel++) {
        for (let row = 0; row < 4; row++) {
            const symbolElement = document.querySelector(`#reel${reel + 1} .reel-symbol:nth-child(${row + 1})`);

            // Add spinning animation
            symbolElement.classList.add('animate-spin');

            setTimeout(() => {
                symbolElement.classList.remove('animate-spin');
                displaySymbolInReel(rhythmReelsGame.reels[reel][row], symbolElement);
            }, 1000 + reel * 300 + row * 100);
        }
    }
}

// Display symbol in reel
function displaySymbolInReel(symbol, element) {
    const symbolIcon = getMusicalSymbolIcon(symbol.name);

    element.innerHTML = `
        <div class="text-lg">${symbolIcon}</div>
        <div class="text-xs text-white font-bold">${symbol.name.split(' ')[0]}</div>
    `;

    // Add symbol-specific styling
    if (symbol.value >= 90) {
        element.classList.add('ring-2', 'ring-purple-400', 'animate-pulse', 'bg-purple-900/50');
    } else if (symbol.value >= 70) {
        element.classList.add('ring-1', 'ring-pink-400');
    } else if (symbol.name.includes('Note')) {
        element.classList.add('ring-1', 'ring-blue-400');
    }
}

// Get musical symbol icon
function getMusicalSymbolIcon(symbolName) {
    const icons = {
        'Whole Note': '𝅝',
        'Half Note': '𝅗𝅥',
        'Quarter Note': '♩',
        'Eighth Note': '♪',
        'Treble Clef': '𝄞',
        'Bass Clef': '𝄢',
        'Sharp': '♯',
        'Flat': '♭',
        'Natural': '♮',
        'Rest': '𝄽',
        'Metronome': '🎵',
        'Music Note': '♫'
    };

    return icons[symbolName] || '♪';
}

// Resolve rhythm spin with beat-matching bonuses (3-5% win rate)
function resolveRhythmSpin() {
    const genreData = MUSIC_GENRES[rhythmReelsGame.musicGenre];
    const difficultyData = DIFFICULTY_LEVELS[rhythmReelsGame.difficulty];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate symbol winnings with improved payouts
    const allSymbols = [];
    rhythmReelsGame.reels.forEach(reel => {
        allSymbols.push(...reel);
    });

    // Count symbol occurrences
    const symbolCounts = {};
    allSymbols.forEach(symbol => {
        symbolCounts[symbol.name] = (symbolCounts[symbol.name] || 0) + 1;
    });

    // Calculate payouts (improved rates)
    Object.entries(symbolCounts).forEach(([symbolName, count]) => {
        if (count >= 3) { // Need at least 3 symbols
            const symbol = rhythmReelsGame.symbols.find(s => s.name === symbolName);
            if (symbol) {
                let symbolPayout = rhythmReelsGame.betAmount * (symbol.value / 100); // Better conversion

                // Apply count multiplier (improved)
                if (count === 4) symbolPayout *= 1.5; // Better multiplier
                if (count === 5) symbolPayout *= 2.0; // Better multiplier

                totalWinnings += symbolPayout;
            }
        }
    });

    // Apply beat accuracy bonus (actually applies more often)
    if (rhythmReelsGame.stats.rhythmAccuracy >= 0.70 && totalWinnings > 0) {
        const accuracyBonus = Math.floor(totalWinnings * RHYTHM_PAYOUTS.BEAT_ACCURACY);
        totalWinnings += accuracyBonus;
        resultMessage += ' + Beat Accuracy!';
    }

    // Apply combo multiplier bonus
    if (rhythmReelsGame.beatMatching.comboMultiplier > 1.0 && totalWinnings > 0) {
        const comboBonus = Math.floor(totalWinnings * RHYTHM_PAYOUTS.COMBO_MULTIPLIER * (rhythmReelsGame.beatMatching.comboMultiplier - 1.0));
        totalWinnings += comboBonus;
        resultMessage += ` + ${rhythmReelsGame.beatMatching.comboMultiplier.toFixed(1)}x Combo!`;
    }

    // Apply rhythm streak bonus
    if (rhythmReelsGame.music.rhythmStreak >= 5 && totalWinnings > 0) {
        const streakBonus = Math.floor(totalWinnings * RHYTHM_PAYOUTS.RHYTHM_STREAK);
        totalWinnings += streakBonus;
        resultMessage += ' + Rhythm Streak!';
    }

    // Apply perfect timing bonus
    if (rhythmReelsGame.music.perfectBeats >= 8 && totalWinnings > 0) {
        const perfectBonus = Math.floor(totalWinnings * RHYTHM_PAYOUTS.PERFECT_TIMING);
        totalWinnings += perfectBonus;
        resultMessage += ' + Perfect Timing!';
    }

    // Check for special rhythm combinations
    if (rhythmReelsGame.music.perfectBeats >= 12) {
        const perfectBonus = Math.floor(rhythmReelsGame.betAmount * RHYTHM_PAYOUTS.PERFECT_SYMPHONY / 100);
        totalWinnings += perfectBonus;
        resultMessage += ' + Perfect Symphony!';
    }

    // Apply genre multiplier
    totalWinnings = Math.floor(totalWinnings * genreData.payoutMultiplier);

    // Apply difficulty bonus
    totalWinnings = Math.floor(totalWinnings * (1 + difficultyData.skillBonus));

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(rhythmReelsGame.betAmount * 0.5); // 50% return
        resultMessage = 'Rhythm participation bonus';
    }

    // Add winnings to balance
    balance += totalWinnings;
    rhythmReelsGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(totalWinnings > rhythmReelsGame.betAmount, totalWinnings);

    if (!resultMessage) {
        if (totalWinnings > rhythmReelsGame.betAmount) {
            resultMessage = 'Perfect rhythm performance!';
        } else if (totalWinnings > 0) {
            resultMessage = 'Good beat matching';
        } else {
            resultMessage = 'Keep practicing your rhythm';
        }
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Genre: ${MUSIC_GENRES[rhythmReelsGame.musicGenre].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update rhythm display
function updateRhythmDisplay() {
    updateMusicInfo();
    updateComboDisplay();
}

// Update beat effects
function updateBeatEffects() {
    // Update beat effects based on current rhythm
    const bpm = rhythmReelsGame.music.bpm;
    const effects = document.querySelectorAll('#beatEffects circle');

    effects.forEach((effect, index) => {
        const animationDuration = 60000 / bpm; // ms per beat
        effect.style.animationDuration = `${animationDuration}ms`;

        if (rhythmReelsGame.beatMatching.active) {
            effect.setAttribute('opacity', '0.8');
            effect.setAttribute('fill', '#ff00ff');
        } else {
            effect.setAttribute('opacity', '0.4');
            effect.setAttribute('fill', '#8000ff');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${rhythmReelsGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = rhythmReelsGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${rhythmReelsGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${rhythmReelsGame.stats.totalWagered}`;
    document.getElementById('perfectBeatsCount').textContent = rhythmReelsGame.stats.perfectBeats;
    document.getElementById('maxCombo').textContent = `${rhythmReelsGame.stats.maxCombo}x`;

    const netResult = rhythmReelsGame.stats.totalWon - rhythmReelsGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-purple-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    rhythmReelsGame.stats.spinsPlayed++;
    rhythmReelsGame.stats.totalWagered += rhythmReelsGame.betAmount;
    rhythmReelsGame.stats.totalWon += winnings;

    if (won) {
        rhythmReelsGame.stats.spinsWon++;
        rhythmReelsGame.stats.currentStreak++;
        rhythmReelsGame.streakData.currentWinStreak++;
        rhythmReelsGame.streakData.currentLossStreak = 0;

        if (rhythmReelsGame.streakData.currentWinStreak > rhythmReelsGame.streakData.longestWinStreak) {
            rhythmReelsGame.streakData.longestWinStreak = rhythmReelsGame.streakData.currentWinStreak;
        }

        if (winnings > rhythmReelsGame.stats.biggestWin) {
            rhythmReelsGame.stats.biggestWin = winnings;
        }
    } else {
        rhythmReelsGame.stats.currentStreak = 0;
        rhythmReelsGame.streakData.currentWinStreak = 0;
        rhythmReelsGame.streakData.currentLossStreak++;

        if (rhythmReelsGame.streakData.currentLossStreak > rhythmReelsGame.streakData.longestLossStreak) {
            rhythmReelsGame.streakData.longestLossStreak = rhythmReelsGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to rhythm mechanics)
    rhythmReelsGame.stats.winRate = (rhythmReelsGame.stats.spinsWon / rhythmReelsGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next rhythm session
function resetGame() {
    rhythmReelsGame.isPlaying = false;
    rhythmReelsGame.betAmount = 0;
    rhythmReelsGame.totalBet = 0;
    rhythmReelsGame.gameResult = '';
    rhythmReelsGame.totalWin = 0;

    // Reset music system
    rhythmReelsGame.music.currentBeat = 1;
    rhythmReelsGame.music.rhythmStreak = 0;
    rhythmReelsGame.music.perfectBeats = 0;
    rhythmReelsGame.music.missedBeats = 0;

    // Reset beat-matching system
    rhythmReelsGame.beatMatching.active = false;
    rhythmReelsGame.beatMatching.combo = 0;
    rhythmReelsGame.beatMatching.comboMultiplier = 1.0;
    rhythmReelsGame.beatMatching.beatScore = 0;
    rhythmReelsGame.beatMatching.totalScore = 0;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        rhythmReelsGame.reels[i] = [];
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('rhythmEvent').classList.add('hidden');
    document.getElementById('rhythmActions').classList.add('hidden');

    // Reset reel displays
    const reelSymbols = document.querySelectorAll('.reel-symbol');
    reelSymbols.forEach(symbol => {
        symbol.innerHTML = '';
        symbol.className = 'reel-symbol w-12 h-12 bg-black/50 border border-purple-400 rounded flex items-center justify-center text-xs';
    });

    // Reset beat indicators
    const beatIndicators = document.querySelectorAll('.beat-indicator');
    beatIndicators.forEach(indicator => {
        indicator.className = 'beat-indicator w-4 h-4 rounded-full bg-gray-700 border border-purple-400';
    });

    // Reset music status
    updateMusicInfo();

    // Reset combo display
    document.getElementById('comboBar').style.width = '0%';
    document.getElementById('currentCombo').textContent = 'Combo: 1.0x';
    document.getElementById('comboDisplay').textContent = '1.0x';

    // Reset timing display
    document.getElementById('timingBar').style.width = '60%';
    document.getElementById('timingIndicator').querySelector('.text-xs:last-child').textContent = '60%';

    // Reset beat counter
    document.getElementById('beatCounter').querySelector('.text-2xl').textContent = '1/16';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startRhythm').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Ready to rock...';
    document.getElementById('gameMessage').textContent = 'Welcome to Rhythm Reels Royale - Match the Beat for Combo Multipliers';

    // Reinitialize systems for next session
    initializeRhythmSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRhythmReelsGame();
});