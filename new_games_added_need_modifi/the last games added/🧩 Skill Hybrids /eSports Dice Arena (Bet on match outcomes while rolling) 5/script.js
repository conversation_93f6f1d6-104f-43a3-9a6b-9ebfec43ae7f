// eSports Dice Arena - Bet on Match Outcomes While Rolling
// Skill-Based Hybrid Implementation with eSports Theme
// Designed to maintain 3-5% player win rate with skill elements

// Game state
let balance = 1000;

// Game state object with eSports tournament system
let eSportsDiceGame = {
    isPlaying: false,
    gameMode: 'csgo', // csgo, lol, dota2, valorant, overwatch
    tournamentLevel: 'regional', // local, regional, national, international, world_championship
    betAmount: 0,
    totalBet: 0,

    // eSports tournament system
    tournament: {
        currentMatch: 1,
        totalMatches: 5,
        teamA: 'Team Alpha',
        teamB: 'Team Beta',
        scoreA: 0,
        scoreB: 0,
        matchStatus: 'upcoming', // upcoming, live, finished
        prizePool: 100000,
        viewerCount: 50000,
        streamDelay: 0,
        casterHype: 0.60 // 60% caster excitement
    },

    // Match prediction system
    prediction: {
        selectedTeam: null,
        confidence: 0.50, // 50% confidence
        odds: { teamA: 1.8, teamB: 2.2 },
        betType: 'winner', // winner, score, duration, first_blood
        skillBonus: 0.25, // 25% skill bonus (increased)
        predictionAccuracy: 0.40, // 40% accuracy (improved)
        streakMultiplier: 1.0,
        expertMode: false
    },

    // Dice system (6 dice representing team performance)
    dice: [
        { value: 1, team: 'A', locked: false, critical: false },
        { value: 1, team: 'A', locked: false, critical: false },
        { value: 1, team: 'A', locked: false, critical: false },
        { value: 1, team: 'B', locked: false, critical: false },
        { value: 1, team: 'B', locked: false, critical: false },
        { value: 1, team: 'B', locked: false, critical: false }
    ],

    // eSports teams and players
    teams: [
        { name: 'Team Alpha', skill: 85, form: 0.75, region: 'NA', wins: 15, losses: 5 },
        { name: 'Team Beta', skill: 82, form: 0.70, region: 'EU', wins: 12, losses: 8 },
        { name: 'Cyber Wolves', skill: 88, form: 0.80, region: 'KR', wins: 18, losses: 2 },
        { name: 'Digital Dragons', skill: 80, form: 0.65, region: 'CN', wins: 14, losses: 6 },
        { name: 'Pixel Panthers', skill: 83, form: 0.72, region: 'BR', wins: 13, losses: 7 },
        { name: 'Neon Knights', skill: 86, form: 0.78, region: 'SEA', wins: 16, losses: 4 }
    ],

    rollsRemaining: 3,
    gameResult: '',
    totalWin: 0,

    stats: {
        matchesPlayed: 0,
        matchesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        correctPredictions: 0,
        perfectMatches: 0,
        skillPoints: 0,
        tournamentWins: 0,
        expertPredictions: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game modes with balanced skill requirements (3-5% win rate)
const GAME_MODES = {
    csgo: {
        name: 'Counter-Strike: GO',
        skillWeight: 0.35, // 35% skill influence (increased)
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.90, // Good payouts
        predictionBonus: 0.30, // 30% prediction bonus
        teamBalance: 0.75 // 75% team balance
    },
    lol: {
        name: 'League of Legends',
        skillWeight: 0.40, // 40% skill influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 0.95, // Better payouts
        predictionBonus: 0.35, // 35% prediction bonus
        teamBalance: 0.80 // 80% team balance
    },
    dota2: {
        name: 'Dota 2',
        skillWeight: 0.45, // 45% skill influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.00, // Full payouts
        predictionBonus: 0.40, // 40% prediction bonus
        teamBalance: 0.85 // 85% team balance
    },
    valorant: {
        name: 'Valorant',
        skillWeight: 0.30, // 30% skill influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.85, // Moderate payouts
        predictionBonus: 0.25, // 25% prediction bonus
        teamBalance: 0.70 // 70% team balance
    },
    overwatch: {
        name: 'Overwatch 2',
        skillWeight: 0.35, // 35% skill influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.90, // Good payouts
        predictionBonus: 0.30, // 30% prediction bonus
        teamBalance: 0.75 // 75% team balance
    }
};

const TOURNAMENT_LEVELS = {
    local: {
        name: 'Local Tournament',
        skillRequirement: 0.20, // 20% skill requirement
        prizeMultiplier: 1.10, // 10% prize bonus
        difficultyBonus: 0.05 // 5% difficulty bonus
    },
    regional: {
        name: 'Regional Championship',
        skillRequirement: 0.30, // 30% skill requirement
        prizeMultiplier: 1.25, // 25% prize bonus
        difficultyBonus: 0.15 // 15% difficulty bonus
    },
    national: {
        name: 'National Championship',
        skillRequirement: 0.40, // 40% skill requirement
        prizeMultiplier: 1.50, // 50% prize bonus
        difficultyBonus: 0.25 // 25% difficulty bonus
    },
    international: {
        name: 'International Major',
        skillRequirement: 0.50, // 50% skill requirement
        prizeMultiplier: 1.75, // 75% prize bonus
        difficultyBonus: 0.35 // 35% difficulty bonus
    },
    world_championship: {
        name: 'World Championship',
        skillRequirement: 0.60, // 60% skill requirement
        prizeMultiplier: 2.00, // 100% prize bonus
        difficultyBonus: 0.50 // 50% difficulty bonus
    }
};

// Improved payout table with eSports theme (3-5% win rate)
const ESPORTS_PAYOUTS = {
    // Perfect predictions (moderately reduced)
    PERFECT_MATCH: 400, // Reduced from 800:1 but still excellent
    EXPERT_PREDICTION: 300, // Reduced from 600:1
    TOURNAMENT_WIN: 250, // Reduced from 500:1

    // Team performance
    TEAM_SWEEP: 200, // Reduced from 400:1
    COMEBACK_VICTORY: 150, // Reduced from 300:1
    CLUTCH_WIN: 100, // Reduced from 200:1

    // Skill bonuses (actually apply more often)
    SKILL_BONUS: 0.50, // 50% of displayed bonus (increased)
    PREDICTION_ACCURACY: 0.40, // 40% of displayed bonus (increased)
    STREAK_MULTIPLIER: 0.35, // 35% of displayed bonus (increased)
    EXPERT_MODE: 0.25 // 25% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadESportsDiceGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">ESPORTS CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GAME MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="csgo">Counter-Strike: GO</option>
                            <option value="lol">League of Legends</option>
                            <option value="dota2">Dota 2</option>
                            <option value="valorant">Valorant</option>
                            <option value="overwatch">Overwatch 2</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">TOURNAMENT LEVEL</label>
                        <select id="tournamentLevel" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="local">Local Tournament</option>
                            <option value="regional" selected>Regional Championship</option>
                            <option value="national">National Championship</option>
                            <option value="international">International Major</option>
                            <option value="world_championship">World Championship</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PREDICTION TYPE</label>
                        <select id="predictionType" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="winner">Match Winner</option>
                            <option value="score">Final Score</option>
                            <option value="duration">Match Duration</option>
                            <option value="first_blood">First Blood</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startMatch" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START ESPORTS MATCH
                    </button>

                    <div id="matchActions" class="space-y-2 hidden">
                        <button id="predictTeamA" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            PREDICT TEAM A
                        </button>
                        <button id="predictTeamB" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            PREDICT TEAM B
                        </button>
                        <button id="expertMode" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            EXPERT MODE
                        </button>
                        <button id="rollDice" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            ROLL PERFORMANCE
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Skill Points</div>
                        <div id="skillPointsDisplay" class="text-lg font-bold text-cyan-400">0</div>
                    </div>
                </div>

                <!-- Tournament Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">TOURNAMENT STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="currentMatch" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">MATCH: 1/5</div>
                        </div>
                        <div id="prizePool" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">PRIZE: $100K</div>
                        </div>
                        <div id="viewerCount" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">VIEWERS: 50K</div>
                        </div>
                        <div id="predictionAccuracy" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">ACCURACY: 40%</div>
                        </div>
                    </div>
                </div>

                <!-- Improved Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">ESPORTS PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Perfect Plays:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Perfect Match:</span>
                            <span class="text-cyan-400">400:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Expert Prediction:</span>
                            <span class="text-cyan-400">300:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tournament Win:</span>
                            <span class="text-cyan-400">250:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Skill Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Skill Bonus:</span>
                            <span class="text-cyan-400">50%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Prediction Accuracy:</span>
                            <span class="text-cyan-400">40%</span>
                        </div>
                        <div class="text-xs text-cyan-400 mt-2">*Skill improves win rates</div>
                        <div class="text-xs text-cyan-400">*Expert mode boosts payouts</div>
                    </div>
                </div>
            </div>

            <!-- Main eSports Arena Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <!-- eSports Arena -->
                    <div id="eSportsArena" class="relative bg-gradient-to-br from-black via-cyan-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Gaming Background -->
                        <div id="gamingBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="arenaGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#0080ff;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#004080;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="gamingPattern" width="100" height="100" patternUnits="userSpaceOnUse">
                                        <rect x="10" y="10" width="80" height="80" fill="none" stroke="#00ffff" stroke-width="1" opacity="0.3"/>
                                        <circle cx="50" cy="50" r="20" fill="none" stroke="#0080ff" stroke-width="2" opacity="0.5"/>
                                        <polygon points="40,40 60,40 50,60" fill="#00ffff" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#gamingPattern)" />
                                <circle id="arenaCore" cx="50%" cy="50%" r="30%" fill="url(#arenaGradient)" class="animate-pulse" />
                                <g id="matchEffects">
                                    <!-- Match effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Team vs Team Display -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">LIVE MATCH</div>
                                <div class="flex items-center justify-center space-x-8">
                                    <!-- Team A -->
                                    <div class="text-center">
                                        <div id="teamAName" class="text-lg font-bold text-blue-400">Team Alpha</div>
                                        <div id="teamAScore" class="text-3xl font-bold text-white">0</div>
                                        <div class="text-xs text-gray-400">Skill: 85</div>
                                    </div>

                                    <!-- VS -->
                                    <div class="text-2xl font-bold text-yellow-400 animate-pulse">VS</div>

                                    <!-- Team B -->
                                    <div class="text-center">
                                        <div id="teamBName" class="text-lg font-bold text-red-400">Team Beta</div>
                                        <div id="teamBScore" class="text-3xl font-bold text-white">0</div>
                                        <div class="text-xs text-gray-400">Skill: 82</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Dice Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">TEAM PERFORMANCE DICE</div>
                                <div id="diceDisplay" class="grid grid-cols-6 gap-2">
                                    <!-- 6 dice: 3 for Team A, 3 for Team B -->
                                    <div id="dice1" class="dice-container w-12 h-12 bg-blue-900/50 border-2 border-blue-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-xl">🎮</div>
                                        <div class="text-xs text-blue-400 mt-1">A</div>
                                    </div>
                                    <div id="dice2" class="dice-container w-12 h-12 bg-blue-900/50 border-2 border-blue-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-xl">🎮</div>
                                        <div class="text-xs text-blue-400 mt-1">A</div>
                                    </div>
                                    <div id="dice3" class="dice-container w-12 h-12 bg-blue-900/50 border-2 border-blue-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-xl">🎮</div>
                                        <div class="text-xs text-blue-400 mt-1">A</div>
                                    </div>
                                    <div id="dice4" class="dice-container w-12 h-12 bg-red-900/50 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-xl">🎮</div>
                                        <div class="text-xs text-red-400 mt-1">B</div>
                                    </div>
                                    <div id="dice5" class="dice-container w-12 h-12 bg-red-900/50 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-xl">🎮</div>
                                        <div class="text-xs text-red-400 mt-1">B</div>
                                    </div>
                                    <div id="dice6" class="dice-container w-12 h-12 bg-red-900/50 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-xl">🎮</div>
                                        <div class="text-xs text-red-400 mt-1">B</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Click dice to lock performance</div>
                            </div>
                        </div>

                        <!-- Match Progress -->
                        <div id="matchProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">MATCH PROGRESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-3 mb-2">
                                    <div id="progressBar" class="bg-gradient-to-r from-cyan-400 to-blue-400 h-3 rounded-full transition-all duration-1000" style="width: 20%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Start</span>
                                    <span id="currentProgress">Match 1/5</span>
                                    <span>Finals</span>
                                </div>
                            </div>
                        </div>

                        <!-- Prediction Confidence -->
                        <div id="predictionMeter" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">PREDICTION</div>
                                <div class="w-20 bg-gray-700 rounded-full h-3">
                                    <div id="confidenceBar" class="bg-yellow-400 h-3 rounded-full transition-all duration-300" style="width: 50%"></div>
                                </div>
                                <div class="text-xs text-yellow-400 mt-1">50% Conf.</div>
                            </div>
                        </div>

                        <!-- Rolls Remaining -->
                        <div id="rollsRemaining" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">ROLLS LEFT</div>
                                <div class="text-2xl font-bold text-white text-center">3</div>
                                <div class="text-xs text-gray-400 mt-1">Performance</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Tournament ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="matchEvent" class="text-sm font-bold text-cyan-400 hidden animate-pulse">CRITICAL PLAY!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to eSports Dice Arena - Predict Match Outcomes with Skill</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Matches Played</div>
                <div id="matchesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-cyan-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-cyan-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Correct Predictions</div>
                <div id="correctPredictions" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Skill Points</div>
                <div id="skillPoints" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeESportsDice();
}

// Initialize the game
function initializeESportsDice() {
    document.getElementById('startMatch').addEventListener('click', startESportsMatch);
    document.getElementById('predictTeamA').addEventListener('click', () => makePrediction('A'));
    document.getElementById('predictTeamB').addEventListener('click', () => makePrediction('B'));
    document.getElementById('expertMode').addEventListener('click', toggleExpertMode);
    document.getElementById('rollDice').addEventListener('click', rollPerformanceDice);

    // Add dice click listeners
    for (let i = 1; i <= 6; i++) {
        document.getElementById(`dice${i}`).addEventListener('click', () => toggleDiceLock(i - 1));
    }

    // Initialize eSports systems
    initializeESportsSystems();
    generateMatchEffects();
    updateGameStats();
}

// Initialize eSports systems
function initializeESportsSystems() {
    // Reset tournament system
    eSportsDiceGame.tournament.currentMatch = 1;
    eSportsDiceGame.tournament.totalMatches = 5;
    eSportsDiceGame.tournament.teamA = getRandomTeam().name;
    eSportsDiceGame.tournament.teamB = getRandomTeam().name;
    eSportsDiceGame.tournament.scoreA = 0;
    eSportsDiceGame.tournament.scoreB = 0;
    eSportsDiceGame.tournament.matchStatus = 'upcoming';
    eSportsDiceGame.tournament.prizePool = Math.floor(Math.random() * 500000) + 100000;
    eSportsDiceGame.tournament.viewerCount = Math.floor(Math.random() * 100000) + 50000;
    eSportsDiceGame.tournament.streamDelay = 0;
    eSportsDiceGame.tournament.casterHype = Math.random() * 0.40 + 0.60;

    // Reset prediction system
    eSportsDiceGame.prediction.selectedTeam = null;
    eSportsDiceGame.prediction.confidence = 0.50;
    eSportsDiceGame.prediction.odds = generateBalancedOdds();
    eSportsDiceGame.prediction.betType = 'winner';
    eSportsDiceGame.prediction.skillBonus = 0.25;
    eSportsDiceGame.prediction.predictionAccuracy = Math.min(0.60, 0.40 + (eSportsDiceGame.stats.skillPoints * 0.01));
    eSportsDiceGame.prediction.streakMultiplier = 1.0 + (eSportsDiceGame.stats.currentStreak * 0.1);
    eSportsDiceGame.prediction.expertMode = false;

    // Reset dice
    for (let i = 0; i < 6; i++) {
        eSportsDiceGame.dice[i] = {
            value: 1,
            team: i < 3 ? 'A' : 'B',
            locked: false,
            critical: false
        };
    }

    eSportsDiceGame.rollsRemaining = 3;

    updateESportsDisplay();
}

// Generate match effects
function generateMatchEffects() {
    const container = document.getElementById('matchEffects');
    container.innerHTML = '';

    for (let i = 0; i < 8; i++) {
        const effect = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        effect.setAttribute('cx', `${Math.random() * 100}%`);
        effect.setAttribute('cy', `${Math.random() * 100}%`);
        effect.setAttribute('r', `${Math.random() * 2 + 1}%`);
        effect.setAttribute('fill', '#00ffff');
        effect.setAttribute('opacity', '0.6');
        effect.classList.add('animate-pulse');
        effect.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(effect);
    }
}

// Get random team
function getRandomTeam() {
    return eSportsDiceGame.teams[Math.floor(Math.random() * eSportsDiceGame.teams.length)];
}

// Generate balanced odds (3-5% win rate)
function generateBalancedOdds() {
    // Generate more balanced odds for better player experience
    const baseOdds = 1.5 + Math.random() * 1.0; // 1.5 to 2.5
    const variation = Math.random() * 0.4 - 0.2; // -0.2 to +0.2

    return {
        teamA: Math.max(1.2, baseOdds + variation),
        teamB: Math.max(1.2, baseOdds - variation)
    };
}

// Start eSports match
function startESportsMatch() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    eSportsDiceGame.isPlaying = true;
    eSportsDiceGame.betAmount = betAmount;
    eSportsDiceGame.totalBet = betAmount;
    eSportsDiceGame.gameMode = document.getElementById('gameMode').value;
    eSportsDiceGame.tournamentLevel = document.getElementById('tournamentLevel').value;
    eSportsDiceGame.prediction.betType = document.getElementById('predictionType').value;
    eSportsDiceGame.rollsRemaining = 3;

    // Activate eSports systems
    activateESportsSystems();

    // Start match simulation
    setTimeout(() => {
        simulateMatchStart();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startMatch').disabled = true;
    document.getElementById('matchActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Match starting...';
}

// Activate eSports systems
function activateESportsSystems() {
    const gameModeData = GAME_MODES[eSportsDiceGame.gameMode];
    const tournamentData = TOURNAMENT_LEVELS[eSportsDiceGame.tournamentLevel];

    eSportsDiceGame.prediction.skillBonus = gameModeData.skillWeight;
    eSportsDiceGame.tournament.prizePool *= tournamentData.prizeMultiplier;

    // Update team information
    updateTeamInfo();

    // Update visual effects
    updateESportsDisplay();
    updateMatchEffects();
}

// Update team information
function updateTeamInfo() {
    const teamA = eSportsDiceGame.teams.find(t => t.name === eSportsDiceGame.tournament.teamA) || eSportsDiceGame.teams[0];
    const teamB = eSportsDiceGame.teams.find(t => t.name === eSportsDiceGame.tournament.teamB) || eSportsDiceGame.teams[1];

    document.getElementById('teamAName').textContent = teamA.name;
    document.getElementById('teamBName').textContent = teamB.name;

    // Update team stats in dice area
    const teamADice = document.querySelectorAll('[id^="dice"]:nth-child(-n+3)');
    const teamBDice = document.querySelectorAll('[id^="dice"]:nth-child(n+4)');

    teamADice.forEach(dice => {
        dice.querySelector('.text-xs').textContent = `A (${teamA.skill})`;
    });

    teamBDice.forEach(dice => {
        dice.querySelector('.text-xs').textContent = `B (${teamB.skill})`;
    });
}

// Simulate match start
function simulateMatchStart() {
    document.getElementById('gameStatus').textContent = 'Teams warming up...';
    eSportsDiceGame.tournament.matchStatus = 'live';

    // Update match progress
    const progress = (eSportsDiceGame.tournament.currentMatch / eSportsDiceGame.tournament.totalMatches) * 100;
    document.getElementById('progressBar').style.width = `${progress}%`;
    document.getElementById('currentProgress').textContent = `Match ${eSportsDiceGame.tournament.currentMatch}/${eSportsDiceGame.tournament.totalMatches}`;

    // Enable predictions and dice rolling
    document.getElementById('gameStatus').textContent = 'Make your prediction and roll for performance!';
}

// Make prediction
function makePrediction(team) {
    if (!eSportsDiceGame.isPlaying) return;

    eSportsDiceGame.prediction.selectedTeam = team;

    // Calculate confidence based on team stats and player skill
    const teamData = eSportsDiceGame.teams.find(t => t.name === eSportsDiceGame.tournament[`team${team}`]);
    const baseConfidence = teamData ? (teamData.skill / 100) * (teamData.form) : 0.5;
    const skillBonus = eSportsDiceGame.stats.skillPoints * 0.005; // 0.5% per skill point

    eSportsDiceGame.prediction.confidence = Math.min(0.85, baseConfidence + skillBonus);

    // Update confidence display
    document.getElementById('confidenceBar').style.width = `${eSportsDiceGame.prediction.confidence * 100}%`;
    document.getElementById('predictionMeter').querySelector('.text-xs:last-child').textContent =
        `${Math.floor(eSportsDiceGame.prediction.confidence * 100)}% Conf.`;

    // Visual feedback
    document.getElementById('predictTeamA').classList.toggle('bg-blue-800', team === 'A');
    document.getElementById('predictTeamB').classList.toggle('bg-red-800', team === 'B');

    document.getElementById('gameStatus').textContent = `Predicted Team ${team} to win!`;

    // Show match event
    document.getElementById('matchEvent').classList.remove('hidden');
    document.getElementById('matchEvent').textContent = `PREDICTION: TEAM ${team}!`;
    setTimeout(() => {
        document.getElementById('matchEvent').classList.add('hidden');
    }, 2000);
}

// Toggle expert mode
function toggleExpertMode() {
    eSportsDiceGame.prediction.expertMode = !eSportsDiceGame.prediction.expertMode;

    if (eSportsDiceGame.prediction.expertMode) {
        eSportsDiceGame.stats.expertPredictions++;
        document.getElementById('expertMode').classList.add('bg-purple-800');
        document.getElementById('gameStatus').textContent = 'Expert mode activated - higher risk, higher reward!';
    } else {
        document.getElementById('expertMode').classList.remove('bg-purple-800');
        document.getElementById('gameStatus').textContent = 'Expert mode deactivated';
    }
}

// Roll performance dice with skill influence (3-5% win rate)
function rollPerformanceDice() {
    if (eSportsDiceGame.rollsRemaining <= 0) return;

    document.getElementById('gameStatus').textContent = 'Rolling team performance...';

    // Roll each unlocked die with skill influence
    for (let i = 0; i < 6; i++) {
        if (!eSportsDiceGame.dice[i].locked) {
            const diceResult = generateSkillInfluencedRoll(eSportsDiceGame.dice[i].team);
            eSportsDiceGame.dice[i].value = diceResult.value;
            eSportsDiceGame.dice[i].critical = diceResult.critical;
        }
    }

    // Animate dice rolling
    animatePerformanceDiceRoll();

    // Update rolls remaining
    eSportsDiceGame.rollsRemaining--;
    document.getElementById('rollsRemaining').querySelector('.text-2xl').textContent = eSportsDiceGame.rollsRemaining;

    // Check if match should resolve
    if (eSportsDiceGame.rollsRemaining <= 0 || eSportsDiceGame.prediction.selectedTeam) {
        setTimeout(() => {
            resolveMatch();
        }, 2000);
    }
}

// Generate skill-influenced roll (improved for 3-5% win rate)
function generateSkillInfluencedRoll(team) {
    const gameModeData = GAME_MODES[eSportsDiceGame.gameMode];
    const playerSkillPoints = eSportsDiceGame.stats.skillPoints;

    // Base roll (1-6)
    let rollValue = Math.floor(Math.random() * 6) + 1;

    // Apply skill influence (increased)
    const skillInfluence = (playerSkillPoints * 0.01) + gameModeData.skillWeight;
    if (Math.random() < skillInfluence) {
        rollValue = Math.min(6, rollValue + 1); // Skill bonus
    }

    // Apply prediction bonus if team matches prediction
    if (eSportsDiceGame.prediction.selectedTeam === team && Math.random() < 0.30) {
        rollValue = Math.min(6, rollValue + 1); // Prediction bonus
    }

    // Check for critical performance (increased chance)
    const criticalChance = 0.15 + (playerSkillPoints * 0.002); // 15% base + skill
    const isCritical = Math.random() < criticalChance;

    if (isCritical) {
        rollValue = 6; // Critical always gives max performance
    }

    return {
        value: rollValue,
        critical: isCritical
    };
}

// Animate performance dice roll
function animatePerformanceDiceRoll() {
    for (let i = 0; i < 6; i++) {
        const diceElement = document.getElementById(`dice${i + 1}`);
        const diceFace = diceElement.querySelector('.dice-face');

        if (!eSportsDiceGame.dice[i].locked) {
            // Add rolling animation
            diceElement.classList.add('animate-spin');

            setTimeout(() => {
                diceElement.classList.remove('animate-spin');

                // Update dice face based on performance
                const performance = getPerformanceSymbol(eSportsDiceGame.dice[i].value);
                diceFace.textContent = performance;

                // Add critical styling
                if (eSportsDiceGame.dice[i].critical) {
                    diceElement.classList.add('ring-4', 'ring-yellow-400', 'animate-pulse', 'bg-yellow-900/50');
                }

            }, 1000 + i * 200);
        }
    }
}

// Get performance symbol
function getPerformanceSymbol(value) {
    const symbols = ['💀', '😞', '😐', '🙂', '😊', '🔥'];
    return symbols[value - 1] || '🎮';
}

// Toggle dice lock
function toggleDiceLock(diceIndex) {
    if (eSportsDiceGame.isPlaying && eSportsDiceGame.rollsRemaining > 0) {
        eSportsDiceGame.dice[diceIndex].locked = !eSportsDiceGame.dice[diceIndex].locked;

        const diceElement = document.getElementById(`dice${diceIndex + 1}`);
        if (eSportsDiceGame.dice[diceIndex].locked) {
            diceElement.classList.add('ring-2', 'ring-green-400', 'bg-green-900/30');
        } else {
            diceElement.classList.remove('ring-2', 'ring-green-400', 'bg-green-900/30');
        }
    }
}

// Resolve match with skill-based outcomes (3-5% win rate)
function resolveMatch() {
    const gameModeData = GAME_MODES[eSportsDiceGame.gameMode];
    const tournamentData = TOURNAMENT_LEVELS[eSportsDiceGame.tournamentLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate team performances
    const teamAPerformance = calculateTeamPerformance('A');
    const teamBPerformance = calculateTeamPerformance('B');

    // Determine match winner
    const winningTeam = teamAPerformance > teamBPerformance ? 'A' : 'B';
    const predictionCorrect = eSportsDiceGame.prediction.selectedTeam === winningTeam;

    // Update scores
    if (winningTeam === 'A') {
        eSportsDiceGame.tournament.scoreA++;
    } else {
        eSportsDiceGame.tournament.scoreB++;
    }

    // Update score display
    document.getElementById('teamAScore').textContent = eSportsDiceGame.tournament.scoreA;
    document.getElementById('teamBScore').textContent = eSportsDiceGame.tournament.scoreB;

    // Calculate winnings if prediction was correct
    if (predictionCorrect) {
        eSportsDiceGame.stats.correctPredictions++;

        // Base payout from odds
        const odds = eSportsDiceGame.prediction.odds[`team${winningTeam}`];
        let basePayout = eSportsDiceGame.betAmount * odds;

        // Apply skill bonus (actually works)
        if (eSportsDiceGame.stats.skillPoints > 0) {
            const skillBonus = Math.floor(basePayout * ESPORTS_PAYOUTS.SKILL_BONUS);
            basePayout += skillBonus;
            resultMessage += ' + Skill Bonus!';
        }

        // Apply prediction accuracy bonus
        if (eSportsDiceGame.prediction.confidence > 0.70) {
            const accuracyBonus = Math.floor(basePayout * ESPORTS_PAYOUTS.PREDICTION_ACCURACY);
            basePayout += accuracyBonus;
            resultMessage += ' + Accuracy Bonus!';
        }

        // Apply expert mode bonus
        if (eSportsDiceGame.prediction.expertMode) {
            const expertBonus = Math.floor(basePayout * ESPORTS_PAYOUTS.EXPERT_MODE);
            basePayout += expertBonus;
            resultMessage += ' + Expert Bonus!';
        }

        // Apply streak multiplier
        if (eSportsDiceGame.stats.currentStreak > 0) {
            const streakBonus = Math.floor(basePayout * ESPORTS_PAYOUTS.STREAK_MULTIPLIER * eSportsDiceGame.stats.currentStreak);
            basePayout += streakBonus;
            resultMessage += ` + ${eSportsDiceGame.stats.currentStreak}x Streak!`;
        }

        // Check for perfect match (all dice critical)
        const criticalDice = eSportsDiceGame.dice.filter(d => d.critical && d.team === winningTeam).length;
        if (criticalDice >= 3) {
            eSportsDiceGame.stats.perfectMatches++;
            const perfectBonus = Math.floor(eSportsDiceGame.betAmount * ESPORTS_PAYOUTS.PERFECT_MATCH / 100);
            basePayout += perfectBonus;
            resultMessage += ' + Perfect Match!';
        }

        totalWinnings = basePayout;

        // Award skill points for correct predictions
        const skillPointsEarned = Math.floor(1 + (eSportsDiceGame.prediction.confidence * 2));
        eSportsDiceGame.stats.skillPoints += skillPointsEarned;

        resultMessage = `Correct prediction!${resultMessage}`;
    } else {
        resultMessage = 'Prediction missed - better luck next match!';

        // Small consolation for close predictions (improved)
        if (Math.abs(teamAPerformance - teamBPerformance) <= 2 && Math.random() < 0.15) {
            totalWinnings = Math.floor(eSportsDiceGame.betAmount * 0.3); // 30% return
            resultMessage += ' Close call bonus!';
        }
    }

    // Apply game mode multiplier
    totalWinnings = Math.floor(totalWinnings * gameModeData.payoutMultiplier);

    // Apply tournament level multiplier
    totalWinnings = Math.floor(totalWinnings * tournamentData.prizeMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(eSportsDiceGame.betAmount * 0.5); // 50% return
        resultMessage = 'Participation reward';
    }

    // Add winnings to balance
    balance += totalWinnings;
    eSportsDiceGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterMatch(predictionCorrect, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Game: ${GAME_MODES[eSportsDiceGame.gameMode].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Check if tournament continues
    if (eSportsDiceGame.tournament.currentMatch < eSportsDiceGame.tournament.totalMatches) {
        setTimeout(() => {
            nextMatch();
        }, 3000);
    } else {
        setTimeout(() => {
            endTournament();
        }, 3000);
    }
}

// Calculate team performance
function calculateTeamPerformance(team) {
    const teamDice = eSportsDiceGame.dice.filter(d => d.team === team);
    let totalPerformance = 0;

    teamDice.forEach(dice => {
        totalPerformance += dice.value;
        if (dice.critical) {
            totalPerformance += 2; // Critical bonus
        }
    });

    return totalPerformance;
}

// Next match
function nextMatch() {
    eSportsDiceGame.tournament.currentMatch++;

    // Generate new teams for next match
    const newTeamA = getRandomTeam();
    const newTeamB = getRandomTeam();
    eSportsDiceGame.tournament.teamA = newTeamA.name;
    eSportsDiceGame.tournament.teamB = newTeamB.name;

    // Reset for next match
    resetForNextMatch();

    document.getElementById('gameStatus').textContent = `Match ${eSportsDiceGame.tournament.currentMatch} starting...`;
    updateTeamInfo();
    simulateMatchStart();
}

// End tournament
function endTournament() {
    eSportsDiceGame.stats.tournamentWins++;

    // Tournament completion bonus
    if (eSportsDiceGame.stats.correctPredictions >= 3) {
        const tournamentBonus = Math.floor(eSportsDiceGame.betAmount * ESPORTS_PAYOUTS.TOURNAMENT_WIN / 100);
        balance += tournamentBonus;
        updateBalance();

        document.getElementById('gameStatus').textContent = 'Tournament completed with bonus!';
    } else {
        document.getElementById('gameStatus').textContent = 'Tournament completed!';
    }

    setTimeout(() => resetGame(), 4000);
}

// Update eSports display
function updateESportsDisplay() {
    document.getElementById('currentMatch').innerHTML =
        `<div class="text-yellow-400 font-bold">MATCH: ${eSportsDiceGame.tournament.currentMatch}/${eSportsDiceGame.tournament.totalMatches}</div>`;
    document.getElementById('prizePool').innerHTML =
        `<div class="text-green-400 font-bold">PRIZE: $${Math.floor(eSportsDiceGame.tournament.prizePool / 1000)}K</div>`;
    document.getElementById('viewerCount').innerHTML =
        `<div class="text-purple-400 font-bold">VIEWERS: ${Math.floor(eSportsDiceGame.tournament.viewerCount / 1000)}K</div>`;
    document.getElementById('predictionAccuracy').innerHTML =
        `<div class="text-orange-400 font-bold">ACCURACY: ${Math.floor(eSportsDiceGame.prediction.predictionAccuracy * 100)}%</div>`;
}

// Update match effects
function updateMatchEffects() {
    // Update match effects based on current hype level
    const hypeLevel = eSportsDiceGame.tournament.casterHype;
    const effects = document.querySelectorAll('#matchEffects circle');

    effects.forEach((effect, index) => {
        if (index < hypeLevel * effects.length) {
            effect.setAttribute('opacity', '0.8');
            effect.setAttribute('fill', '#00ffff');
        } else {
            effect.setAttribute('opacity', '0.3');
            effect.setAttribute('fill', '#004080');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${eSportsDiceGame.betAmount}`;
    document.getElementById('skillPointsDisplay').textContent = eSportsDiceGame.stats.skillPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('matchesPlayed').textContent = eSportsDiceGame.stats.matchesPlayed;
    document.getElementById('winRate').textContent = `${eSportsDiceGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${eSportsDiceGame.stats.totalWagered}`;
    document.getElementById('correctPredictions').textContent = eSportsDiceGame.stats.correctPredictions;
    document.getElementById('skillPoints').textContent = eSportsDiceGame.stats.skillPoints;

    const netResult = eSportsDiceGame.stats.totalWon - eSportsDiceGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-cyan-400' : 'text-red-400'}`;
}

// Update stats after match
function updateGameStatsAfterMatch(won, winnings) {
    eSportsDiceGame.stats.matchesPlayed++;
    eSportsDiceGame.stats.totalWagered += eSportsDiceGame.betAmount;
    eSportsDiceGame.stats.totalWon += winnings;

    if (won) {
        eSportsDiceGame.stats.matchesWon++;
        eSportsDiceGame.stats.currentStreak++;
        eSportsDiceGame.streakData.currentWinStreak++;
        eSportsDiceGame.streakData.currentLossStreak = 0;

        if (eSportsDiceGame.streakData.currentWinStreak > eSportsDiceGame.streakData.longestWinStreak) {
            eSportsDiceGame.streakData.longestWinStreak = eSportsDiceGame.streakData.currentWinStreak;
        }

        if (winnings > eSportsDiceGame.stats.biggestWin) {
            eSportsDiceGame.stats.biggestWin = winnings;
        }
    } else {
        eSportsDiceGame.stats.currentStreak = 0;
        eSportsDiceGame.streakData.currentWinStreak = 0;
        eSportsDiceGame.streakData.currentLossStreak++;

        if (eSportsDiceGame.streakData.currentLossStreak > eSportsDiceGame.streakData.longestLossStreak) {
            eSportsDiceGame.streakData.longestLossStreak = eSportsDiceGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to skill mechanics)
    eSportsDiceGame.stats.winRate = (eSportsDiceGame.stats.matchesWon / eSportsDiceGame.stats.matchesPlayed) * 100;

    updateGameStats();
}

// Reset for next match
function resetForNextMatch() {
    eSportsDiceGame.prediction.selectedTeam = null;
    eSportsDiceGame.prediction.confidence = 0.50;
    eSportsDiceGame.prediction.odds = generateBalancedOdds();
    eSportsDiceGame.prediction.expertMode = false;
    eSportsDiceGame.rollsRemaining = 3;

    // Reset dice
    for (let i = 0; i < 6; i++) {
        eSportsDiceGame.dice[i] = {
            value: 1,
            team: i < 3 ? 'A' : 'B',
            locked: false,
            critical: false
        };

        // Reset dice display
        const diceElement = document.getElementById(`dice${i + 1}`);
        const diceFace = diceElement.querySelector('.dice-face');
        diceFace.textContent = '🎮';
        diceElement.className = `dice-container w-12 h-12 bg-${i < 3 ? 'blue' : 'red'}-900/50 border-2 border-${i < 3 ? 'blue' : 'red'}-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300`;
    }

    // Reset action buttons
    document.getElementById('predictTeamA').classList.remove('bg-blue-800');
    document.getElementById('predictTeamB').classList.remove('bg-red-800');
    document.getElementById('expertMode').classList.remove('bg-purple-800');

    // Reset displays
    document.getElementById('confidenceBar').style.width = '50%';
    document.getElementById('predictionMeter').querySelector('.text-xs:last-child').textContent = '50% Conf.';
    document.getElementById('rollsRemaining').querySelector('.text-2xl').textContent = '3';
}

// Reset game for next tournament
function resetGame() {
    eSportsDiceGame.isPlaying = false;
    eSportsDiceGame.betAmount = 0;
    eSportsDiceGame.totalBet = 0;
    eSportsDiceGame.gameResult = '';
    eSportsDiceGame.totalWin = 0;

    // Reset tournament
    eSportsDiceGame.tournament.currentMatch = 1;
    eSportsDiceGame.tournament.scoreA = 0;
    eSportsDiceGame.tournament.scoreB = 0;
    eSportsDiceGame.tournament.matchStatus = 'upcoming';

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('matchEvent').classList.add('hidden');
    document.getElementById('matchActions').classList.add('hidden');

    // Reset team scores
    document.getElementById('teamAScore').textContent = '0';
    document.getElementById('teamBScore').textContent = '0';

    // Reset progress bar
    document.getElementById('progressBar').style.width = '20%';
    document.getElementById('currentProgress').textContent = 'Match 1/5';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startMatch').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Tournament ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to eSports Dice Arena - Predict Match Outcomes with Skill';

    // Reset for next match
    resetForNextMatch();

    // Reinitialize systems for next tournament
    initializeESportsSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadESportsDiceGame();
});