// Trivia Treasure Jackpot - Knowledge-Based Bonus Unlocks
// Skill-Based Hybrid Implementation with Educational Trivia Theme
// Designed to maintain 3-5% player win rate with knowledge skills

// Game state
let balance = 1000;

// Game state object with trivia system
let triviaTreasureGame = {
    isPlaying: false,
    category: 'general', // general, science, history, sports, entertainment, geography
    difficulty: 'medium', // easy, medium, hard, expert, genius
    betAmount: 0,
    totalBet: 0,

    // Knowledge system
    knowledge: {
        totalQuestions: 0,
        correctAnswers: 0,
        accuracy: 0.65, // 65% accuracy (improved)
        streak: 0,
        longestStreak: 0,
        categoryMastery: {
            general: 0.60,
            science: 0.55,
            history: 0.50,
            sports: 0.45,
            entertainment: 0.70,
            geography: 0.40
        },
        difficultyMastery: {
            easy: 0.80,
            medium: 0.65,
            hard: 0.45,
            expert: 0.30,
            genius: 0.15
        },
        knowledgePoints: 0,
        expertiseLevel: 'novice' // novice, student, scholar, expert, genius
    },

    // Trivia question system
    trivia: {
        currentQuestion: null,
        questionNumber: 1,
        totalQuestions: 5,
        timeLimit: 30, // seconds
        timeRemaining: 30,
        bonusMultiplier: 1.0,
        quickAnswerBonus: 0.25, // 25% bonus for quick answers
        streakBonus: 0.20, // 20% bonus per streak
        categoryBonus: 0.15, // 15% category mastery bonus
        difficultyBonus: 0.30 // 30% difficulty bonus
    },

    // Treasure chest system (5 chests with knowledge locks)
    treasureChests: [
        { locked: true, knowledge: 'easy', reward: 50, unlocked: false },
        { locked: true, knowledge: 'medium', reward: 100, unlocked: false },
        { locked: true, knowledge: 'hard', reward: 200, unlocked: false },
        { locked: true, knowledge: 'expert', reward: 500, unlocked: false },
        { locked: true, knowledge: 'genius', reward: 1000, unlocked: false }
    ],

    // Question database (sample questions)
    questionDatabase: {
        general: [
            {
                question: "What is the capital of France?",
                options: ["London", "Berlin", "Paris", "Madrid"],
                correct: 2,
                difficulty: "easy",
                explanation: "Paris is the capital and largest city of France."
            },
            {
                question: "Which planet is known as the Red Planet?",
                options: ["Venus", "Mars", "Jupiter", "Saturn"],
                correct: 1,
                difficulty: "easy",
                explanation: "Mars is called the Red Planet due to its reddish appearance."
            },
            {
                question: "What is the largest ocean on Earth?",
                options: ["Atlantic", "Indian", "Arctic", "Pacific"],
                correct: 3,
                difficulty: "medium",
                explanation: "The Pacific Ocean is the largest ocean, covering about 46% of Earth's water surface."
            }
        ],
        science: [
            {
                question: "What is the chemical symbol for gold?",
                options: ["Go", "Gd", "Au", "Ag"],
                correct: 2,
                difficulty: "medium",
                explanation: "Au comes from the Latin word 'aurum' meaning gold."
            },
            {
                question: "How many bones are in an adult human body?",
                options: ["206", "208", "210", "212"],
                correct: 0,
                difficulty: "hard",
                explanation: "An adult human skeleton has 206 bones."
            }
        ],
        history: [
            {
                question: "In which year did World War II end?",
                options: ["1944", "1945", "1946", "1947"],
                correct: 1,
                difficulty: "medium",
                explanation: "World War II ended in 1945 with the surrender of Japan."
            },
            {
                question: "Who was the first person to walk on the moon?",
                options: ["Buzz Aldrin", "Neil Armstrong", "John Glenn", "Alan Shepard"],
                correct: 1,
                difficulty: "easy",
                explanation: "Neil Armstrong was the first person to walk on the moon on July 20, 1969."
            }
        ]
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        questionsAnswered: 0,
        correctAnswers: 0,
        triviaAccuracy: 0.65,
        chestsUnlocked: 0,
        knowledgePoints: 0,
        expertiseAchieved: 'novice'
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Categories with balanced knowledge requirements (3-5% win rate)
const TRIVIA_CATEGORIES = {
    general: {
        name: 'General Knowledge',
        skillWeight: 0.30, // 30% skill influence (increased)
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.90, // Good payouts
        knowledgeBonus: 0.25, // 25% knowledge bonus
        masteryThreshold: 0.70 // 70% mastery threshold
    },
    science: {
        name: 'Science & Nature',
        skillWeight: 0.40, // 40% skill influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 0.95, // Better payouts
        knowledgeBonus: 0.30, // 30% knowledge bonus
        masteryThreshold: 0.75 // 75% mastery threshold
    },
    history: {
        name: 'History & Culture',
        skillWeight: 0.35, // 35% skill influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.90, // Good payouts
        knowledgeBonus: 0.25, // 25% knowledge bonus
        masteryThreshold: 0.70 // 70% mastery threshold
    },
    sports: {
        name: 'Sports & Games',
        skillWeight: 0.25, // 25% skill influence
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.85, // Moderate payouts
        knowledgeBonus: 0.20, // 20% knowledge bonus
        masteryThreshold: 0.65 // 65% mastery threshold
    },
    entertainment: {
        name: 'Entertainment',
        skillWeight: 0.30, // 30% skill influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.90, // Good payouts
        knowledgeBonus: 0.25, // 25% knowledge bonus
        masteryThreshold: 0.70 // 70% mastery threshold
    },
    geography: {
        name: 'Geography & Travel',
        skillWeight: 0.45, // 45% skill influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.00, // Full payouts
        knowledgeBonus: 0.35, // 35% knowledge bonus
        masteryThreshold: 0.80 // 80% mastery threshold
    }
};

const DIFFICULTY_LEVELS = {
    easy: {
        name: 'Easy',
        timeLimit: 45, // 45 seconds
        knowledgeRequirement: 0.50, // 50% knowledge requirement
        bonusMultiplier: 1.10 // 10% bonus
    },
    medium: {
        name: 'Medium',
        timeLimit: 30, // 30 seconds
        knowledgeRequirement: 0.65, // 65% knowledge requirement
        bonusMultiplier: 1.25 // 25% bonus
    },
    hard: {
        name: 'Hard',
        timeLimit: 20, // 20 seconds
        knowledgeRequirement: 0.75, // 75% knowledge requirement
        bonusMultiplier: 1.50 // 50% bonus
    },
    expert: {
        name: 'Expert',
        timeLimit: 15, // 15 seconds
        knowledgeRequirement: 0.85, // 85% knowledge requirement
        bonusMultiplier: 2.00 // 100% bonus
    },
    genius: {
        name: 'Genius',
        timeLimit: 10, // 10 seconds
        knowledgeRequirement: 0.95, // 95% knowledge requirement
        bonusMultiplier: 3.00 // 200% bonus
    }
};

// Improved payout table with trivia theme (3-5% win rate)
const TRIVIA_PAYOUTS = {
    // Perfect knowledge achievements (moderately reduced)
    GENIUS_LEVEL: 1000, // Reduced from 2000:1 but still excellent
    EXPERT_SCHOLAR: 600, // Reduced from 1200:1
    KNOWLEDGE_MASTER: 400, // Reduced from 800:1
    TRIVIA_CHAMPION: 300, // Reduced from 600:1

    // Treasure chest unlocks
    GENIUS_CHEST: 1000, // Genius level chest
    EXPERT_CHEST: 500, // Expert level chest
    HARD_CHEST: 200, // Hard level chest
    MEDIUM_CHEST: 100, // Medium level chest
    EASY_CHEST: 50, // Easy level chest

    // Knowledge bonuses (actually apply more often)
    KNOWLEDGE_BONUS: 0.60, // 60% of displayed bonus (increased)
    ACCURACY_BONUS: 0.50, // 50% of displayed bonus (increased)
    STREAK_BONUS: 0.40, // 40% of displayed bonus (increased)
    QUICK_ANSWER: 0.35 // 35% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadTriviaTreasureGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <h4 class="text-xl font-bold mb-4 text-indigo-400">TRIVIA CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CATEGORY</label>
                        <select id="category" class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="general">General Knowledge</option>
                            <option value="science">Science & Nature</option>
                            <option value="history">History & Culture</option>
                            <option value="sports">Sports & Games</option>
                            <option value="entertainment">Entertainment</option>
                            <option value="geography">Geography & Travel</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Easy (45s)</option>
                            <option value="medium" selected>Medium (30s)</option>
                            <option value="hard">Hard (20s)</option>
                            <option value="expert">Expert (15s)</option>
                            <option value="genius">Genius (10s)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startTrivia" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START TRIVIA QUEST
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Knowledge Points</div>
                        <div id="knowledgePointsDisplay" class="text-lg font-bold text-indigo-400">0</div>
                    </div>
                </div>

                <!-- Knowledge Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-indigo-400">KNOWLEDGE STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="triviaAccuracy" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">ACCURACY: 65%</div>
                        </div>
                        <div id="currentStreak" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">STREAK: 0</div>
                        </div>
                        <div id="expertiseLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                        <div id="chestsUnlocked" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">CHESTS: 0/5</div>
                        </div>
                    </div>
                </div>

                <!-- Treasure Chests Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-indigo-400">TREASURE CHESTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Unlock Requirements:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Easy Chest:</span>
                            <span class="text-green-400">$50</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Medium Chest:</span>
                            <span class="text-blue-400">$100</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Hard Chest:</span>
                            <span class="text-purple-400">$200</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Expert Chest:</span>
                            <span class="text-orange-400">$500</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Genius Chest:</span>
                            <span class="text-red-400">$1000</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Knowledge Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Knowledge:</span>
                            <span class="text-indigo-400">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Accuracy:</span>
                            <span class="text-indigo-400">50%</span>
                        </div>
                        <div class="text-xs text-indigo-400 mt-2">*Knowledge unlocks chests</div>
                        <div class="text-xs text-indigo-400">*Accuracy improves payouts</div>
                    </div>
                </div>
            </div>

            <!-- Main Trivia Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <!-- Trivia Arena -->
                    <div id="triviaArena" class="relative bg-gradient-to-br from-black via-indigo-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Knowledge Background -->
                        <div id="knowledgeBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="knowledgeGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#4f46e5;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#3730a3;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="triviaPattern" width="80" height="80" patternUnits="userSpaceOnUse">
                                        <circle cx="40" cy="40" r="20" fill="none" stroke="#6366f1" stroke-width="2" opacity="0.3"/>
                                        <text x="40" y="45" text-anchor="middle" fill="#4f46e5" font-size="16" opacity="0.5">?</text>
                                        <circle cx="20" cy="20" r="3" fill="#6366f1" opacity="0.5"/>
                                        <circle cx="60" cy="60" r="3" fill="#6366f1" opacity="0.5"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#triviaPattern)" />
                                <circle id="knowledgeCore" cx="50%" cy="50%" r="25%" fill="url(#knowledgeGradient)" class="animate-pulse" />
                                <g id="triviaEffects">
                                    <!-- Trivia effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Question Display -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2 w-full max-w-2xl">
                            <div class="text-center">
                                <div class="text-sm text-indigo-400 mb-2">TRIVIA QUESTION</div>
                                <div id="questionDisplay" class="bg-black/70 rounded-lg p-4 mb-4">
                                    <div id="questionText" class="text-lg text-white mb-4">Welcome to Trivia Treasure! Answer questions to unlock treasure chests.</div>
                                    <div id="questionOptions" class="grid grid-cols-2 gap-2 hidden">
                                        <button class="option-btn bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg transition-all duration-200" data-option="0">Option A</button>
                                        <button class="option-btn bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg transition-all duration-200" data-option="1">Option B</button>
                                        <button class="option-btn bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg transition-all duration-200" data-option="2">Option C</button>
                                        <button class="option-btn bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg transition-all duration-200" data-option="3">Option D</button>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400">Answer correctly to unlock treasure chests</div>
                            </div>
                        </div>

                        <!-- Treasure Chests Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-indigo-400 mb-2">TREASURE CHESTS</div>
                                <div id="treasureChests" class="flex space-x-3">
                                    <!-- 5 treasure chests -->
                                    <div class="treasure-chest w-16 h-16 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-lg border-2 border-yellow-400 flex items-center justify-center cursor-pointer transition-all duration-300 opacity-50" data-chest="0">
                                        <div class="text-2xl">🔒</div>
                                        <div class="absolute -bottom-6 text-xs text-yellow-400">Easy</div>
                                    </div>
                                    <div class="treasure-chest w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg border-2 border-blue-400 flex items-center justify-center cursor-pointer transition-all duration-300 opacity-50" data-chest="1">
                                        <div class="text-2xl">🔒</div>
                                        <div class="absolute -bottom-6 text-xs text-blue-400">Medium</div>
                                    </div>
                                    <div class="treasure-chest w-16 h-16 bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg border-2 border-purple-400 flex items-center justify-center cursor-pointer transition-all duration-300 opacity-50" data-chest="2">
                                        <div class="text-2xl">🔒</div>
                                        <div class="absolute -bottom-6 text-xs text-purple-400">Hard</div>
                                    </div>
                                    <div class="treasure-chest w-16 h-16 bg-gradient-to-br from-orange-600 to-orange-800 rounded-lg border-2 border-orange-400 flex items-center justify-center cursor-pointer transition-all duration-300 opacity-50" data-chest="3">
                                        <div class="text-2xl">🔒</div>
                                        <div class="absolute -bottom-6 text-xs text-orange-400">Expert</div>
                                    </div>
                                    <div class="treasure-chest w-16 h-16 bg-gradient-to-br from-red-600 to-red-800 rounded-lg border-2 border-red-400 flex items-center justify-center cursor-pointer transition-all duration-300 opacity-50" data-chest="4">
                                        <div class="text-2xl">🔒</div>
                                        <div class="absolute -bottom-6 text-xs text-red-400">Genius</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-8">Answer questions correctly to unlock chests</div>
                            </div>
                        </div>

                        <!-- Timer and Progress -->
                        <div id="timerProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-indigo-400 mb-2">TIME REMAINING</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="timerBar" class="bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 h-4 rounded-full transition-all duration-1000" style="width: 100%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>0s</span>
                                    <span id="timeDisplay">30s</span>
                                    <span>30s</span>
                                </div>
                            </div>
                        </div>

                        <!-- Knowledge Score -->
                        <div id="knowledgeScore" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-indigo-400 mb-1">KNOWLEDGE</div>
                                <div class="text-2xl font-bold text-white text-center">65%</div>
                                <div class="text-xs text-gray-400 mt-1">Accuracy</div>
                            </div>
                        </div>

                        <!-- Question Progress -->
                        <div id="questionProgress" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">PROGRESS</div>
                                <div class="text-2xl font-bold text-white text-center">1/5</div>
                                <div class="text-xs text-gray-400 mt-1">Questions</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Ready for trivia...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="triviaEvent" class="text-sm font-bold text-indigo-400 hidden animate-pulse">CORRECT!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Trivia Treasure Jackpot - Answer Questions to Unlock Treasure Chests</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-indigo-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-indigo-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Trivia Accuracy</div>
                <div id="triviaAccuracyStat" class="text-xl font-bold text-green-400">65%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Chests Unlocked</div>
                <div id="chestsUnlockedStat" class="text-xl font-bold text-yellow-400">0</div>
            </div>
        </div>
    `;

    initializeTriviaTreasure();
}

// Initialize the game
function initializeTriviaTreasure() {
    document.getElementById('startTrivia').addEventListener('click', startTriviaQuest);

    // Add option button listeners
    const optionButtons = document.querySelectorAll('.option-btn');
    optionButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            const selectedOption = parseInt(e.target.dataset.option);
            answerQuestion(selectedOption);
        });
    });

    // Add treasure chest listeners
    const treasureChests = document.querySelectorAll('.treasure-chest');
    treasureChests.forEach((chest, index) => {
        chest.addEventListener('click', () => openTreasureChest(index));
    });

    // Initialize trivia systems
    initializeTriviaSystems();
    generateTriviaEffects();
    updateGameStats();
}

// Initialize trivia systems
function initializeTriviaSystems() {
    // Reset knowledge system
    triviaTreasureGame.knowledge.totalQuestions = 0;
    triviaTreasureGame.knowledge.correctAnswers = 0;
    triviaTreasureGame.knowledge.accuracy = Math.min(0.85, 0.65 + (triviaTreasureGame.stats.knowledgePoints * 0.01));
    triviaTreasureGame.knowledge.streak = 0;
    triviaTreasureGame.knowledge.longestStreak = 0;
    triviaTreasureGame.knowledge.knowledgePoints = triviaTreasureGame.stats.knowledgePoints;
    triviaTreasureGame.knowledge.expertiseLevel = calculateExpertiseLevel();

    // Reset trivia question system
    triviaTreasureGame.trivia.currentQuestion = null;
    triviaTreasureGame.trivia.questionNumber = 1;
    triviaTreasureGame.trivia.totalQuestions = 5;
    triviaTreasureGame.trivia.timeLimit = 30;
    triviaTreasureGame.trivia.timeRemaining = 30;
    triviaTreasureGame.trivia.bonusMultiplier = 1.0;

    // Reset treasure chests
    triviaTreasureGame.treasureChests.forEach(chest => {
        chest.unlocked = false;
    });

    updateTriviaDisplay();
}

// Generate trivia effects
function generateTriviaEffects() {
    const container = document.getElementById('triviaEffects');
    container.innerHTML = '';

    for (let i = 0; i < 8; i++) {
        const effect = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        effect.setAttribute('cx', `${Math.random() * 100}%`);
        effect.setAttribute('cy', `${Math.random() * 100}%`);
        effect.setAttribute('r', `${Math.random() * 2 + 1}%`);
        effect.setAttribute('fill', '#6366f1');
        effect.setAttribute('opacity', '0.6');
        effect.classList.add('animate-pulse');
        effect.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(effect);
    }
}

// Calculate expertise level
function calculateExpertiseLevel() {
    const points = triviaTreasureGame.stats.knowledgePoints;
    if (points >= 100) return 'genius';
    if (points >= 50) return 'expert';
    if (points >= 25) return 'scholar';
    if (points >= 10) return 'student';
    return 'novice';
}

// Start trivia quest
function startTriviaQuest() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    triviaTreasureGame.isPlaying = true;
    triviaTreasureGame.betAmount = betAmount;
    triviaTreasureGame.totalBet = betAmount;
    triviaTreasureGame.category = document.getElementById('category').value;
    triviaTreasureGame.difficulty = document.getElementById('difficulty').value;

    // Activate trivia systems
    activateTriviaSystems();

    // Start first question
    setTimeout(() => {
        loadNextQuestion();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startTrivia').disabled = true;
    document.getElementById('gameStatus').textContent = 'Trivia quest starting...';
}

// Activate trivia systems
function activateTriviaSystems() {
    const categoryData = TRIVIA_CATEGORIES[triviaTreasureGame.category];
    const difficultyData = DIFFICULTY_LEVELS[triviaTreasureGame.difficulty];

    triviaTreasureGame.trivia.timeLimit = difficultyData.timeLimit;
    triviaTreasureGame.trivia.timeRemaining = difficultyData.timeLimit;
    triviaTreasureGame.trivia.bonusMultiplier = difficultyData.bonusMultiplier;

    // Update trivia information
    updateTriviaInfo();

    // Update visual effects
    updateTriviaDisplay();
    updateTriviaEffects();
}

// Update trivia information
function updateTriviaInfo() {
    document.getElementById('triviaAccuracy').innerHTML =
        `<div class="text-green-400 font-bold">ACCURACY: ${Math.floor(triviaTreasureGame.knowledge.accuracy * 100)}%</div>`;
    document.getElementById('currentStreak').innerHTML =
        `<div class="text-blue-400 font-bold">STREAK: ${triviaTreasureGame.knowledge.streak}</div>`;
    document.getElementById('expertiseLevel').innerHTML =
        `<div class="text-purple-400 font-bold">LEVEL: ${triviaTreasureGame.knowledge.expertiseLevel.toUpperCase()}</div>`;

    const unlockedChests = triviaTreasureGame.treasureChests.filter(chest => chest.unlocked).length;
    document.getElementById('chestsUnlocked').innerHTML =
        `<div class="text-yellow-400 font-bold">CHESTS: ${unlockedChests}/5</div>`;
}

// Load next question
function loadNextQuestion() {
    if (triviaTreasureGame.trivia.questionNumber > triviaTreasureGame.trivia.totalQuestions) {
        endTriviaQuest();
        return;
    }

    // Get question from database
    const question = getRandomQuestion();
    triviaTreasureGame.trivia.currentQuestion = question;

    // Display question
    displayQuestion(question);

    // Start timer
    startQuestionTimer();

    document.getElementById('gameStatus').textContent = `Question ${triviaTreasureGame.trivia.questionNumber} of ${triviaTreasureGame.trivia.totalQuestions}`;
}

// Get random question based on category and difficulty
function getRandomQuestion() {
    const categoryQuestions = triviaTreasureGame.questionDatabase[triviaTreasureGame.category] ||
                             triviaTreasureGame.questionDatabase.general;

    // Filter by difficulty if possible
    const difficultyQuestions = categoryQuestions.filter(q => q.difficulty === triviaTreasureGame.difficulty);
    const availableQuestions = difficultyQuestions.length > 0 ? difficultyQuestions : categoryQuestions;

    return availableQuestions[Math.floor(Math.random() * availableQuestions.length)];
}

// Display question
function displayQuestion(question) {
    document.getElementById('questionText').textContent = question.question;

    const optionButtons = document.querySelectorAll('.option-btn');
    optionButtons.forEach((button, index) => {
        button.textContent = question.options[index];
        button.disabled = false;
        button.className = 'option-btn bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg transition-all duration-200';
    });

    document.getElementById('questionOptions').classList.remove('hidden');

    // Update progress
    document.getElementById('questionProgress').querySelector('.text-2xl').textContent =
        `${triviaTreasureGame.trivia.questionNumber}/${triviaTreasureGame.trivia.totalQuestions}`;
}

// Start question timer
function startQuestionTimer() {
    const difficultyData = DIFFICULTY_LEVELS[triviaTreasureGame.difficulty];
    triviaTreasureGame.trivia.timeRemaining = difficultyData.timeLimit;

    const timer = setInterval(() => {
        triviaTreasureGame.trivia.timeRemaining--;

        // Update timer display
        const percentage = (triviaTreasureGame.trivia.timeRemaining / difficultyData.timeLimit) * 100;
        document.getElementById('timerBar').style.width = `${percentage}%`;
        document.getElementById('timeDisplay').textContent = `${triviaTreasureGame.trivia.timeRemaining}s`;

        // Time up
        if (triviaTreasureGame.trivia.timeRemaining <= 0) {
            clearInterval(timer);
            answerQuestion(-1); // Time up = wrong answer
        }

        // Stop timer if question answered
        if (!triviaTreasureGame.isPlaying || !triviaTreasureGame.trivia.currentQuestion) {
            clearInterval(timer);
        }
    }, 1000);
}

// Answer question with knowledge influence (3-5% win rate)
function answerQuestion(selectedOption) {
    if (!triviaTreasureGame.trivia.currentQuestion) return;

    const question = triviaTreasureGame.trivia.currentQuestion;
    const isCorrect = selectedOption === question.correct;
    const categoryData = TRIVIA_CATEGORIES[triviaTreasureGame.category];
    const difficultyData = DIFFICULTY_LEVELS[triviaTreasureGame.difficulty];

    // Update knowledge statistics
    triviaTreasureGame.knowledge.totalQuestions++;
    triviaTreasureGame.stats.questionsAnswered++;

    if (isCorrect) {
        triviaTreasureGame.knowledge.correctAnswers++;
        triviaTreasureGame.knowledge.streak++;
        triviaTreasureGame.stats.correctAnswers++;

        // Award knowledge points
        const pointsEarned = Math.floor(difficultyData.bonusMultiplier * 2);
        triviaTreasureGame.knowledge.knowledgePoints += pointsEarned;
        triviaTreasureGame.stats.knowledgePoints += pointsEarned;

        // Update longest streak
        if (triviaTreasureGame.knowledge.streak > triviaTreasureGame.knowledge.longestStreak) {
            triviaTreasureGame.knowledge.longestStreak = triviaTreasureGame.knowledge.streak;
        }

        // Check for treasure chest unlock
        checkTreasureChestUnlock();

        // Visual feedback
        document.getElementById('triviaEvent').classList.remove('hidden');
        document.getElementById('triviaEvent').textContent = 'CORRECT!';
        document.getElementById('triviaEvent').className = 'text-sm font-bold text-green-400 animate-pulse';

        // Highlight correct answer
        const optionButtons = document.querySelectorAll('.option-btn');
        optionButtons[question.correct].className = 'option-btn bg-green-600 text-white p-3 rounded-lg';

    } else {
        triviaTreasureGame.knowledge.streak = 0;

        // Visual feedback
        document.getElementById('triviaEvent').classList.remove('hidden');
        document.getElementById('triviaEvent').textContent = selectedOption === -1 ? 'TIME UP!' : 'INCORRECT!';
        document.getElementById('triviaEvent').className = 'text-sm font-bold text-red-400 animate-pulse';

        // Highlight correct and wrong answers
        const optionButtons = document.querySelectorAll('.option-btn');
        optionButtons[question.correct].className = 'option-btn bg-green-600 text-white p-3 rounded-lg';
        if (selectedOption >= 0) {
            optionButtons[selectedOption].className = 'option-btn bg-red-600 text-white p-3 rounded-lg';
        }
    }

    // Disable all buttons
    const optionButtons = document.querySelectorAll('.option-btn');
    optionButtons.forEach(button => {
        button.disabled = true;
    });

    // Update accuracy
    triviaTreasureGame.knowledge.accuracy = triviaTreasureGame.knowledge.correctAnswers / triviaTreasureGame.knowledge.totalQuestions;
    triviaTreasureGame.stats.triviaAccuracy = triviaTreasureGame.knowledge.accuracy;

    // Update displays
    updateTriviaInfo();
    updateKnowledgeDisplay();

    // Show explanation if available
    if (question.explanation) {
        setTimeout(() => {
            document.getElementById('questionText').textContent = question.explanation;
        }, 1500);
    }

    // Move to next question
    setTimeout(() => {
        triviaTreasureGame.trivia.questionNumber++;
        triviaTreasureGame.trivia.currentQuestion = null;
        document.getElementById('triviaEvent').classList.add('hidden');
        loadNextQuestion();
    }, 3000);
}

// Check treasure chest unlock
function checkTreasureChestUnlock() {
    const accuracy = triviaTreasureGame.knowledge.accuracy;
    const streak = triviaTreasureGame.knowledge.streak;
    const knowledgePoints = triviaTreasureGame.knowledge.knowledgePoints;

    // Check each chest unlock condition
    triviaTreasureGame.treasureChests.forEach((chest, index) => {
        if (!chest.unlocked) {
            let shouldUnlock = false;

            switch (chest.knowledge) {
                case 'easy':
                    shouldUnlock = accuracy >= 0.60 || streak >= 2;
                    break;
                case 'medium':
                    shouldUnlock = accuracy >= 0.70 || streak >= 3;
                    break;
                case 'hard':
                    shouldUnlock = accuracy >= 0.80 || streak >= 4;
                    break;
                case 'expert':
                    shouldUnlock = accuracy >= 0.90 || streak >= 5 || knowledgePoints >= 20;
                    break;
                case 'genius':
                    shouldUnlock = accuracy >= 0.95 || streak >= 8 || knowledgePoints >= 50;
                    break;
            }

            if (shouldUnlock) {
                unlockTreasureChest(index);
            }
        }
    });
}

// Unlock treasure chest
function unlockTreasureChest(chestIndex) {
    const chest = triviaTreasureGame.treasureChests[chestIndex];
    if (chest.unlocked) return;

    chest.unlocked = true;
    triviaTreasureGame.stats.chestsUnlocked++;

    // Update visual
    const chestElement = document.querySelector(`[data-chest="${chestIndex}"]`);
    chestElement.querySelector('.text-2xl').textContent = '💎';
    chestElement.classList.remove('opacity-50');
    chestElement.classList.add('animate-pulse', 'ring-2', 'ring-yellow-400');

    // Show unlock notification
    document.getElementById('triviaEvent').classList.remove('hidden');
    document.getElementById('triviaEvent').textContent = `${chest.knowledge.toUpperCase()} CHEST UNLOCKED!`;
    document.getElementById('triviaEvent').className = 'text-sm font-bold text-yellow-400 animate-pulse';
}

// Open treasure chest
function openTreasureChest(chestIndex) {
    const chest = triviaTreasureGame.treasureChests[chestIndex];
    if (!chest.unlocked || !triviaTreasureGame.isPlaying) return;

    // Award chest reward
    const reward = chest.reward;
    balance += reward;
    updateBalance();

    // Visual feedback
    const chestElement = document.querySelector(`[data-chest="${chestIndex}"]`);
    chestElement.querySelector('.text-2xl').textContent = '✨';
    chestElement.classList.add('ring-4', 'ring-green-400');

    // Show reward
    document.getElementById('winAmount').textContent = `+$${reward}`;
    document.getElementById('winAmount').classList.remove('hidden');

    setTimeout(() => {
        document.getElementById('winAmount').classList.add('hidden');
    }, 3000);
}

// End trivia quest with knowledge-based rewards (3-5% win rate)
function endTriviaQuest() {
    const categoryData = TRIVIA_CATEGORIES[triviaTreasureGame.category];
    const difficultyData = DIFFICULTY_LEVELS[triviaTreasureGame.difficulty];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate base winnings from unlocked chests
    const unlockedChests = triviaTreasureGame.treasureChests.filter(chest => chest.unlocked);
    const chestWinnings = unlockedChests.reduce((sum, chest) => sum + chest.reward, 0);
    totalWinnings += chestWinnings;

    // Apply knowledge bonuses (actually work)
    if (triviaTreasureGame.knowledge.accuracy >= 0.80 && totalWinnings > 0) {
        const knowledgeBonus = Math.floor(totalWinnings * TRIVIA_PAYOUTS.KNOWLEDGE_BONUS);
        totalWinnings += knowledgeBonus;
        resultMessage += ' + Knowledge Bonus!';
    }

    // Apply accuracy bonus
    if (triviaTreasureGame.knowledge.accuracy >= 0.90 && totalWinnings > 0) {
        const accuracyBonus = Math.floor(totalWinnings * TRIVIA_PAYOUTS.ACCURACY_BONUS);
        totalWinnings += accuracyBonus;
        resultMessage += ' + Accuracy Bonus!';
    }

    // Apply streak bonus
    if (triviaTreasureGame.knowledge.streak >= 3 && totalWinnings > 0) {
        const streakBonus = Math.floor(totalWinnings * TRIVIA_PAYOUTS.STREAK_BONUS * triviaTreasureGame.knowledge.streak);
        totalWinnings += streakBonus;
        resultMessage += ` + ${triviaTreasureGame.knowledge.streak}x Streak!`;
    }

    // Apply quick answer bonus (if answered quickly)
    const avgTimePerQuestion = (difficultyData.timeLimit * triviaTreasureGame.trivia.totalQuestions -
                               triviaTreasureGame.trivia.timeRemaining) / triviaTreasureGame.trivia.totalQuestions;
    if (avgTimePerQuestion <= difficultyData.timeLimit * 0.5 && totalWinnings > 0) {
        const quickBonus = Math.floor(totalWinnings * TRIVIA_PAYOUTS.QUICK_ANSWER);
        totalWinnings += quickBonus;
        resultMessage += ' + Quick Answer!';
    }

    // Check for special achievements
    if (triviaTreasureGame.knowledge.accuracy >= 1.0) {
        const perfectBonus = Math.floor(triviaTreasureGame.betAmount * TRIVIA_PAYOUTS.GENIUS_LEVEL / 100);
        totalWinnings += perfectBonus;
        resultMessage += ' + Perfect Score!';
    } else if (triviaTreasureGame.knowledge.accuracy >= 0.90) {
        const expertBonus = Math.floor(triviaTreasureGame.betAmount * TRIVIA_PAYOUTS.EXPERT_SCHOLAR / 100);
        totalWinnings += expertBonus;
        resultMessage += ' + Expert Scholar!';
    }

    // Apply category multiplier
    totalWinnings = Math.floor(totalWinnings * categoryData.payoutMultiplier);

    // Apply difficulty bonus
    totalWinnings = Math.floor(totalWinnings * difficultyData.bonusMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(triviaTreasureGame.betAmount * 0.5); // 50% return
        resultMessage = 'Participation reward';
    }

    // Add winnings to balance
    balance += totalWinnings;
    triviaTreasureGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterQuest(unlockedChests.length > 0, totalWinnings);

    if (!resultMessage) {
        if (unlockedChests.length >= 3) {
            resultMessage = 'Excellent knowledge demonstration!';
        } else if (unlockedChests.length > 0) {
            resultMessage = 'Good trivia performance';
        } else {
            resultMessage = 'Keep studying and try again';
        }
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Category: ${TRIVIA_CATEGORIES[triviaTreasureGame.category].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update trivia display
function updateTriviaDisplay() {
    updateTriviaInfo();
    updateKnowledgeDisplay();
}

// Update knowledge display
function updateKnowledgeDisplay() {
    const accuracy = Math.floor(triviaTreasureGame.knowledge.accuracy * 100);
    document.getElementById('knowledgeScore').querySelector('.text-2xl').textContent = `${accuracy}%`;
}

// Update trivia effects
function updateTriviaEffects() {
    // Update trivia effects based on knowledge level
    const accuracy = triviaTreasureGame.knowledge.accuracy;
    const effects = document.querySelectorAll('#triviaEffects circle');

    effects.forEach((effect, index) => {
        if (accuracy >= 0.80) {
            effect.setAttribute('opacity', '0.8');
            effect.setAttribute('fill', '#10b981'); // Green for high accuracy
        } else if (accuracy >= 0.60) {
            effect.setAttribute('opacity', '0.6');
            effect.setAttribute('fill', '#6366f1'); // Blue for medium accuracy
        } else {
            effect.setAttribute('opacity', '0.4');
            effect.setAttribute('fill', '#ef4444'); // Red for low accuracy
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${triviaTreasureGame.betAmount}`;
    document.getElementById('knowledgePointsDisplay').textContent = triviaTreasureGame.stats.knowledgePoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = triviaTreasureGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${triviaTreasureGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${triviaTreasureGame.stats.totalWagered}`;
    document.getElementById('triviaAccuracyStat').textContent = `${Math.floor(triviaTreasureGame.stats.triviaAccuracy * 100)}%`;
    document.getElementById('chestsUnlockedStat').textContent = triviaTreasureGame.stats.chestsUnlocked;

    const netResult = triviaTreasureGame.stats.totalWon - triviaTreasureGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-indigo-400' : 'text-red-400'}`;
}

// Update stats after quest
function updateGameStatsAfterQuest(won, winnings) {
    triviaTreasureGame.stats.gamesPlayed++;
    triviaTreasureGame.stats.totalWagered += triviaTreasureGame.betAmount;
    triviaTreasureGame.stats.totalWon += winnings;

    if (won) {
        triviaTreasureGame.stats.gamesWon++;
        triviaTreasureGame.stats.currentStreak++;
        triviaTreasureGame.streakData.currentWinStreak++;
        triviaTreasureGame.streakData.currentLossStreak = 0;

        if (triviaTreasureGame.streakData.currentWinStreak > triviaTreasureGame.streakData.longestWinStreak) {
            triviaTreasureGame.streakData.longestWinStreak = triviaTreasureGame.streakData.currentWinStreak;
        }

        if (winnings > triviaTreasureGame.stats.biggestWin) {
            triviaTreasureGame.stats.biggestWin = winnings;
        }
    } else {
        triviaTreasureGame.stats.currentStreak = 0;
        triviaTreasureGame.streakData.currentWinStreak = 0;
        triviaTreasureGame.streakData.currentLossStreak++;

        if (triviaTreasureGame.streakData.currentLossStreak > triviaTreasureGame.streakData.longestLossStreak) {
            triviaTreasureGame.streakData.longestLossStreak = triviaTreasureGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to knowledge mechanics)
    triviaTreasureGame.stats.winRate = (triviaTreasureGame.stats.gamesWon / triviaTreasureGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next trivia quest
function resetGame() {
    triviaTreasureGame.isPlaying = false;
    triviaTreasureGame.betAmount = 0;
    triviaTreasureGame.totalBet = 0;
    triviaTreasureGame.gameResult = '';
    triviaTreasureGame.totalWin = 0;

    // Reset trivia system
    triviaTreasureGame.trivia.currentQuestion = null;
    triviaTreasureGame.trivia.questionNumber = 1;
    triviaTreasureGame.trivia.timeRemaining = 30;

    // Reset treasure chests
    triviaTreasureGame.treasureChests.forEach((chest, index) => {
        chest.unlocked = false;
        const chestElement = document.querySelector(`[data-chest="${index}"]`);
        chestElement.querySelector('.text-2xl').textContent = '🔒';
        chestElement.className = 'treasure-chest w-16 h-16 bg-gradient-to-br from-yellow-600 to-yellow-800 rounded-lg border-2 border-yellow-400 flex items-center justify-center cursor-pointer transition-all duration-300 opacity-50';

        // Reset chest colors
        if (index === 1) chestElement.className = chestElement.className.replace('yellow', 'blue');
        if (index === 2) chestElement.className = chestElement.className.replace('yellow', 'purple');
        if (index === 3) chestElement.className = chestElement.className.replace('yellow', 'orange');
        if (index === 4) chestElement.className = chestElement.className.replace('yellow', 'red');
    });

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('triviaEvent').classList.add('hidden');
    document.getElementById('questionOptions').classList.add('hidden');

    // Reset question display
    document.getElementById('questionText').textContent = 'Welcome to Trivia Treasure! Answer questions to unlock treasure chests.';

    // Reset timer
    document.getElementById('timerBar').style.width = '100%';
    document.getElementById('timeDisplay').textContent = '30s';

    // Reset progress
    document.getElementById('questionProgress').querySelector('.text-2xl').textContent = '1/5';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startTrivia').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Ready for trivia...';
    document.getElementById('gameMessage').textContent = 'Welcome to Trivia Treasure Jackpot - Answer Questions to Unlock Treasure Chests';

    // Reinitialize systems for next quest
    initializeTriviaSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadTriviaTreasureGame();
});