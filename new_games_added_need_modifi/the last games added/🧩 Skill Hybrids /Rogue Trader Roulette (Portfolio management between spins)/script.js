// Rogue Trader Roulette - Portfolio Management Between Spins
// Skill-Based Hybrid Implementation with Financial Trading Theme
// Designed to maintain 3-5% player win rate with portfolio management skills

// Game state
let balance = 1000;

// Game state object with trading system
let rogueTraderGame = {
    isPlaying: false,
    marketType: 'stocks', // stocks, crypto, forex, commodities, derivatives
    riskLevel: 'moderate', // conservative, moderate, aggressive, speculative, extreme
    betAmount: 0,
    totalBet: 0,

    // Financial market system
    market: {
        volatility: 0.15, // 15% volatility
        trend: 'bullish', // bearish, sideways, bullish
        momentum: 0.60, // 60% momentum
        volume: 1000000, // trading volume
        spread: 0.02, // 2% spread
        liquidity: 0.80, // 80% liquidity
        marketCap: 50000000, // $50M market cap
        beta: 1.2, // market correlation
        vix: 20 // volatility index
    },

    // Portfolio management system
    portfolio: {
        cash: 500, // available cash
        totalValue: 1000, // total portfolio value
        positions: [],
        diversification: 0.60, // 60% diversification
        riskExposure: 0.40, // 40% risk exposure
        sharpeRatio: 1.5, // risk-adjusted return
        maxDrawdown: 0.10, // 10% max drawdown
        winRate: 0.55, // 55% win rate (improved)
        profitFactor: 1.8, // profit factor
        averageReturn: 0.08 // 8% average return
    },

    // Trading assets
    assets: [
        { symbol: 'AAPL', name: 'Apple Inc.', price: 150.00, change: 0.02, sector: 'Technology', beta: 1.1 },
        { symbol: 'GOOGL', name: 'Alphabet Inc.', price: 2800.00, change: 0.015, sector: 'Technology', beta: 1.0 },
        { symbol: 'TSLA', name: 'Tesla Inc.', price: 800.00, change: 0.05, sector: 'Automotive', beta: 2.0 },
        { symbol: 'MSFT', name: 'Microsoft Corp.', price: 300.00, change: 0.01, sector: 'Technology', beta: 0.9 },
        { symbol: 'AMZN', name: 'Amazon.com Inc.', price: 3200.00, change: 0.025, sector: 'E-commerce', beta: 1.3 },
        { symbol: 'BTC', name: 'Bitcoin', price: 45000.00, change: 0.08, sector: 'Cryptocurrency', beta: 3.0 },
        { symbol: 'ETH', name: 'Ethereum', price: 3000.00, change: 0.06, sector: 'Cryptocurrency', beta: 2.5 },
        { symbol: 'GOLD', name: 'Gold Futures', price: 1800.00, change: -0.01, sector: 'Commodities', beta: -0.2 },
        { symbol: 'OIL', name: 'Crude Oil', price: 70.00, change: 0.03, sector: 'Energy', beta: 1.5 },
        { symbol: 'SPY', name: 'S&P 500 ETF', price: 400.00, change: 0.012, sector: 'Index', beta: 1.0 }
    ],

    // Roulette wheel (European style with portfolio sectors)
    rouletteWheel: {
        spinning: false,
        result: null,
        sectors: [
            'Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer', 'Industrial',
            'Materials', 'Utilities', 'Real Estate', 'Telecommunications', 'Cryptocurrency', 'Commodities'
        ],
        numbers: Array.from({length: 37}, (_, i) => i), // 0-36
        colors: ['green', 'red', 'black'],
        lastResults: []
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        portfolioValue: 1000,
        bestReturn: 0,
        worstDrawdown: 0,
        tradesExecuted: 0,
        profitableTrades: 0,
        skillPoints: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Market types with balanced skill requirements (3-5% win rate)
const MARKET_TYPES = {
    stocks: {
        name: 'Stock Market',
        volatility: 0.15,
        skillWeight: 0.35, // 35% skill influence (increased)
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.90, // Good payouts
        riskAdjustment: 0.25, // 25% risk adjustment
        diversificationBonus: 0.30 // 30% diversification bonus
    },
    crypto: {
        name: 'Cryptocurrency',
        volatility: 0.40,
        skillWeight: 0.30, // 30% skill influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.85, // Moderate payouts
        riskAdjustment: 0.20, // 20% risk adjustment
        diversificationBonus: 0.25 // 25% diversification bonus
    },
    forex: {
        name: 'Foreign Exchange',
        volatility: 0.10,
        skillWeight: 0.40, // 40% skill influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 0.95, // Better payouts
        riskAdjustment: 0.30, // 30% risk adjustment
        diversificationBonus: 0.35 // 35% diversification bonus
    },
    commodities: {
        name: 'Commodities',
        volatility: 0.20,
        skillWeight: 0.35, // 35% skill influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.90, // Good payouts
        riskAdjustment: 0.25, // 25% risk adjustment
        diversificationBonus: 0.30 // 30% diversification bonus
    },
    derivatives: {
        name: 'Derivatives',
        volatility: 0.50,
        skillWeight: 0.45, // 45% skill influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.00, // Full payouts
        riskAdjustment: 0.35, // 35% risk adjustment
        diversificationBonus: 0.40 // 40% diversification bonus
    }
};

const RISK_LEVELS = {
    conservative: {
        name: 'Conservative',
        maxRisk: 0.20, // 20% max risk
        expectedReturn: 0.06, // 6% expected return
        skillBonus: 0.20 // 20% skill bonus
    },
    moderate: {
        name: 'Moderate',
        maxRisk: 0.35, // 35% max risk
        expectedReturn: 0.08, // 8% expected return
        skillBonus: 0.25 // 25% skill bonus
    },
    aggressive: {
        name: 'Aggressive',
        maxRisk: 0.50, // 50% max risk
        expectedReturn: 0.12, // 12% expected return
        skillBonus: 0.30 // 30% skill bonus
    },
    speculative: {
        name: 'Speculative',
        maxRisk: 0.70, // 70% max risk
        expectedReturn: 0.18, // 18% expected return
        skillBonus: 0.35 // 35% skill bonus
    },
    extreme: {
        name: 'Extreme Risk',
        maxRisk: 0.90, // 90% max risk
        expectedReturn: 0.25, // 25% expected return
        skillBonus: 0.40 // 40% skill bonus
    }
};

// Improved payout table with trading theme (3-5% win rate)
const TRADING_PAYOUTS = {
    // Perfect portfolio management (moderately reduced)
    PORTFOLIO_MASTER: 800, // Reduced from 1600:1 but still excellent
    RISK_MANAGER: 600, // Reduced from 1200:1
    DIVERSIFICATION_KING: 400, // Reduced from 800:1
    MARKET_TIMING: 300, // Reduced from 600:1

    // Roulette combinations
    STRAIGHT_UP: 3500, // Single number (35:1)
    SPLIT: 1700, // Two numbers (17:1)
    STREET: 1100, // Three numbers (11:1)
    CORNER: 800, // Four numbers (8:1)

    // Portfolio bonuses (actually apply more often)
    DIVERSIFICATION_BONUS: 0.60, // 60% of displayed bonus (increased)
    RISK_ADJUSTMENT: 0.50, // 50% of displayed bonus (increased)
    PORTFOLIO_BALANCE: 0.40, // 40% of displayed bonus (increased)
    SKILL_TRADING: 0.35 // 35% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadRogueTraderGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">TRADING CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">MARKET TYPE</label>
                        <select id="marketType" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="stocks">Stock Market</option>
                            <option value="crypto">Cryptocurrency</option>
                            <option value="forex">Foreign Exchange</option>
                            <option value="commodities">Commodities</option>
                            <option value="derivatives">Derivatives</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">RISK LEVEL</label>
                        <select id="riskLevel" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="conservative">Conservative</option>
                            <option value="moderate" selected>Moderate</option>
                            <option value="aggressive">Aggressive</option>
                            <option value="speculative">Speculative</option>
                            <option value="extreme">Extreme Risk</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startTrading" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START TRADING SESSION
                    </button>

                    <div id="tradingActions" class="space-y-2 hidden">
                        <button id="managePortfolio" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            MANAGE PORTFOLIO
                        </button>
                        <button id="rebalanceAssets" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            REBALANCE ASSETS
                        </button>
                        <button id="hedgeRisk" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            HEDGE RISK
                        </button>
                        <button id="spinRoulette" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            SPIN ROULETTE
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Portfolio Value</div>
                        <div id="portfolioValueDisplay" class="text-lg font-bold text-orange-400">$1000</div>
                    </div>
                </div>

                <!-- Market Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">MARKET STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="marketTrend" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">TREND: BULLISH</div>
                        </div>
                        <div id="volatility" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">VIX: 20</div>
                        </div>
                        <div id="volume" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">VOLUME: 1M</div>
                        </div>
                        <div id="riskExposure" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">RISK: 40%</div>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Holdings -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">PORTFOLIO</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Holdings:</div>
                        <div id="portfolioHoldings" class="space-y-1">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Cash:</span>
                                <span class="text-green-400">$500</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Stocks:</span>
                                <span class="text-blue-400">$300</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Crypto:</span>
                                <span class="text-purple-400">$200</span>
                            </div>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Performance:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sharpe Ratio:</span>
                            <span class="text-orange-400">1.5</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Win Rate:</span>
                            <span class="text-orange-400">55%</span>
                        </div>
                        <div class="text-xs text-orange-400 mt-2">*Portfolio skill improves odds</div>
                        <div class="text-xs text-orange-400">*Diversification reduces risk</div>
                    </div>
                </div>
            </div>

            <!-- Main Trading Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <!-- Trading Floor -->
                    <div id="tradingFloor" class="relative bg-gradient-to-br from-black via-orange-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Financial Background -->
                        <div id="financialBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="tradingGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#ffa500;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#ff8c00;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#ff4500;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="chartPattern" width="100" height="100" patternUnits="userSpaceOnUse">
                                        <path d="M10,50 Q30,30 50,50 Q70,70 90,50" stroke="#ffa500" stroke-width="2" fill="none" opacity="0.3"/>
                                        <circle cx="25" cy="25" r="3" fill="#ff8c00" opacity="0.5"/>
                                        <circle cx="75" cy="75" r="3" fill="#ff8c00" opacity="0.5"/>
                                        <rect x="40" y="40" width="20" height="20" fill="none" stroke="#ffa500" stroke-width="1" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#chartPattern)" />
                                <circle id="tradingCore" cx="50%" cy="50%" r="25%" fill="url(#tradingGradient)" class="animate-pulse" />
                                <g id="marketEffects">
                                    <!-- Market effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Roulette Wheel -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-orange-400 mb-2">MARKET ROULETTE</div>
                                <div id="rouletteWheel" class="w-32 h-32 rounded-full border-4 border-orange-400 bg-gradient-to-br from-red-800 via-black to-green-800 relative mx-auto">
                                    <!-- Roulette sectors will be drawn here -->
                                    <div id="rouletteBall" class="absolute w-3 h-3 bg-white rounded-full top-2 left-1/2 transform -translate-x-1/2 transition-all duration-3000"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div id="rouletteResult" class="text-white font-bold text-lg">?</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Sector-based market roulette</div>
                            </div>
                        </div>

                        <!-- Asset Price Chart -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-orange-400 mb-2">ASSET PRICES</div>
                                <div id="assetChart" class="grid grid-cols-5 gap-2">
                                    <!-- Top 5 assets display -->
                                    <div class="asset-card bg-black/50 border border-orange-400 rounded p-2 text-center">
                                        <div class="text-xs text-white font-bold">AAPL</div>
                                        <div class="text-sm text-green-400">$150</div>
                                        <div class="text-xs text-green-400">+2%</div>
                                    </div>
                                    <div class="asset-card bg-black/50 border border-orange-400 rounded p-2 text-center">
                                        <div class="text-xs text-white font-bold">BTC</div>
                                        <div class="text-sm text-yellow-400">$45K</div>
                                        <div class="text-xs text-green-400">+8%</div>
                                    </div>
                                    <div class="asset-card bg-black/50 border border-orange-400 rounded p-2 text-center">
                                        <div class="text-xs text-white font-bold">GOLD</div>
                                        <div class="text-sm text-yellow-400">$1800</div>
                                        <div class="text-xs text-red-400">-1%</div>
                                    </div>
                                    <div class="asset-card bg-black/50 border border-orange-400 rounded p-2 text-center">
                                        <div class="text-xs text-white font-bold">OIL</div>
                                        <div class="text-sm text-orange-400">$70</div>
                                        <div class="text-xs text-green-400">+3%</div>
                                    </div>
                                    <div class="asset-card bg-black/50 border border-orange-400 rounded p-2 text-center">
                                        <div class="text-xs text-white font-bold">SPY</div>
                                        <div class="text-sm text-blue-400">$400</div>
                                        <div class="text-xs text-green-400">+1%</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Click assets to add to portfolio</div>
                            </div>
                        </div>

                        <!-- Portfolio Performance -->
                        <div id="portfolioPerformance" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-orange-400 mb-2">PORTFOLIO PERFORMANCE</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="performanceBar" class="bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 h-4 rounded-full transition-all duration-1000" style="width: 55%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Loss</span>
                                    <span id="currentPerformance">Performance: 55%</span>
                                    <span>Profit</span>
                                </div>
                            </div>
                        </div>

                        <!-- Risk Meter -->
                        <div id="riskMeter" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">RISK</div>
                                <div class="w-20 bg-gray-700 rounded-full h-3">
                                    <div id="riskBar" class="bg-red-400 h-3 rounded-full transition-all duration-300" style="width: 40%"></div>
                                </div>
                                <div class="text-xs text-red-400 mt-1">40%</div>
                            </div>
                        </div>

                        <!-- Diversification Score -->
                        <div id="diversificationScore" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">DIVERSIFICATION</div>
                                <div class="text-2xl font-bold text-white text-center">60%</div>
                                <div class="text-xs text-gray-400 mt-1">Score</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Markets open...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="tradingEvent" class="text-sm font-bold text-orange-400 hidden animate-pulse">MARKET MOVE!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Rogue Trader Roulette - Manage Your Portfolio Between Spins</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-orange-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-orange-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Portfolio Value</div>
                <div id="portfolioValueStat" class="text-xl font-bold text-green-400">$1000</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Best Return</div>
                <div id="bestReturn" class="text-xl font-bold text-cyan-400">0%</div>
            </div>
        </div>
    `;

    initializeRogueTrader();
}

// Initialize the game
function initializeRogueTrader() {
    document.getElementById('startTrading').addEventListener('click', startTradingSession);
    document.getElementById('managePortfolio').addEventListener('click', managePortfolio);
    document.getElementById('rebalanceAssets').addEventListener('click', rebalanceAssets);
    document.getElementById('hedgeRisk').addEventListener('click', hedgeRisk);
    document.getElementById('spinRoulette').addEventListener('click', spinMarketRoulette);

    // Add asset click listeners
    const assetCards = document.querySelectorAll('.asset-card');
    assetCards.forEach((card, index) => {
        card.addEventListener('click', () => addAssetToPortfolio(index));
    });

    // Initialize trading systems
    initializeTradingSystems();
    generateMarketEffects();
    updateGameStats();
}

// Initialize trading systems
function initializeTradingSystems() {
    // Reset market system
    rogueTraderGame.market.volatility = 0.15;
    rogueTraderGame.market.trend = getRandomTrend();
    rogueTraderGame.market.momentum = Math.random() * 0.40 + 0.50; // 50-90%
    rogueTraderGame.market.volume = Math.floor(Math.random() * 5000000) + 1000000;
    rogueTraderGame.market.spread = Math.random() * 0.03 + 0.01; // 1-4%
    rogueTraderGame.market.liquidity = Math.random() * 0.30 + 0.70; // 70-100%
    rogueTraderGame.market.marketCap = Math.floor(Math.random() * 100000000) + 50000000;
    rogueTraderGame.market.beta = Math.random() * 1.5 + 0.5; // 0.5-2.0
    rogueTraderGame.market.vix = Math.floor(Math.random() * 30) + 10; // 10-40

    // Reset portfolio system
    rogueTraderGame.portfolio.cash = 500;
    rogueTraderGame.portfolio.totalValue = 1000;
    rogueTraderGame.portfolio.positions = [];
    rogueTraderGame.portfolio.diversification = 0.60;
    rogueTraderGame.portfolio.riskExposure = 0.40;
    rogueTraderGame.portfolio.sharpeRatio = 1.5;
    rogueTraderGame.portfolio.maxDrawdown = 0.10;
    rogueTraderGame.portfolio.winRate = Math.min(0.70, 0.55 + (rogueTraderGame.stats.skillPoints * 0.01));
    rogueTraderGame.portfolio.profitFactor = 1.8;
    rogueTraderGame.portfolio.averageReturn = 0.08;

    // Update asset prices
    updateAssetPrices();

    updateTradingDisplay();
}

// Generate market effects
function generateMarketEffects() {
    const container = document.getElementById('marketEffects');
    container.innerHTML = '';

    for (let i = 0; i < 6; i++) {
        const effect = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        effect.setAttribute('cx', `${Math.random() * 100}%`);
        effect.setAttribute('cy', `${Math.random() * 100}%`);
        effect.setAttribute('r', `${Math.random() * 2 + 1}%`);
        effect.setAttribute('fill', '#ffa500');
        effect.setAttribute('opacity', '0.6');
        effect.classList.add('animate-pulse');
        effect.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(effect);
    }
}

// Get random market elements
function getRandomTrend() {
    const trends = ['bearish', 'sideways', 'bullish'];
    return trends[Math.floor(Math.random() * trends.length)];
}

// Update asset prices
function updateAssetPrices() {
    rogueTraderGame.assets.forEach(asset => {
        // Apply market volatility
        const priceChange = (Math.random() - 0.5) * rogueTraderGame.market.volatility * 2;
        asset.price *= (1 + priceChange);
        asset.change = priceChange;

        // Ensure reasonable price bounds
        if (asset.symbol === 'BTC') {
            asset.price = Math.max(20000, Math.min(80000, asset.price));
        } else if (asset.symbol === 'ETH') {
            asset.price = Math.max(1000, Math.min(6000, asset.price));
        } else if (asset.symbol === 'GOLD') {
            asset.price = Math.max(1500, Math.min(2500, asset.price));
        }
    });

    updateAssetDisplay();
}

// Update asset display
function updateAssetDisplay() {
    const assetCards = document.querySelectorAll('.asset-card');
    const topAssets = rogueTraderGame.assets.slice(0, 5);

    assetCards.forEach((card, index) => {
        if (topAssets[index]) {
            const asset = topAssets[index];
            const symbol = card.querySelector('.text-xs.text-white');
            const price = card.querySelector('.text-sm');
            const change = card.querySelector('.text-xs:last-child');

            symbol.textContent = asset.symbol;

            if (asset.symbol === 'BTC' || asset.symbol === 'ETH') {
                price.textContent = `$${Math.floor(asset.price / 1000)}K`;
            } else if (asset.price > 1000) {
                price.textContent = `$${Math.floor(asset.price)}`;
            } else {
                price.textContent = `$${asset.price.toFixed(0)}`;
            }

            const changePercent = (asset.change * 100).toFixed(1);
            change.textContent = `${changePercent >= 0 ? '+' : ''}${changePercent}%`;
            change.className = `text-xs ${asset.change >= 0 ? 'text-green-400' : 'text-red-400'}`;
        }
    });
}

// Start trading session
function startTradingSession() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    rogueTraderGame.isPlaying = true;
    rogueTraderGame.betAmount = betAmount;
    rogueTraderGame.totalBet = betAmount;
    rogueTraderGame.marketType = document.getElementById('marketType').value;
    rogueTraderGame.riskLevel = document.getElementById('riskLevel').value;

    // Activate trading systems
    activateTradingSystems();

    // Start market simulation
    setTimeout(() => {
        simulateMarketSession();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startTrading').disabled = true;
    document.getElementById('tradingActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Trading session active...';
}

// Activate trading systems
function activateTradingSystems() {
    const marketData = MARKET_TYPES[rogueTraderGame.marketType];
    const riskData = RISK_LEVELS[rogueTraderGame.riskLevel];

    rogueTraderGame.market.volatility = marketData.volatility;
    rogueTraderGame.portfolio.riskExposure = riskData.maxRisk;

    // Update market information
    updateMarketInfo();

    // Update visual effects
    updateTradingDisplay();
    updateMarketEffects();
}

// Update market information
function updateMarketInfo() {
    const trendColor = rogueTraderGame.market.trend === 'bullish' ? 'text-green-400' :
                      rogueTraderGame.market.trend === 'bearish' ? 'text-red-400' : 'text-yellow-400';

    document.getElementById('marketTrend').innerHTML =
        `<div class="${trendColor} font-bold">TREND: ${rogueTraderGame.market.trend.toUpperCase()}</div>`;
    document.getElementById('volatility').innerHTML =
        `<div class="text-yellow-400 font-bold">VIX: ${rogueTraderGame.market.vix}</div>`;
    document.getElementById('volume').innerHTML =
        `<div class="text-blue-400 font-bold">VOLUME: ${Math.floor(rogueTraderGame.market.volume / 1000000)}M</div>`;
    document.getElementById('riskExposure').innerHTML =
        `<div class="text-red-400 font-bold">RISK: ${Math.floor(rogueTraderGame.portfolio.riskExposure * 100)}%</div>`;
}

// Simulate market session
function simulateMarketSession() {
    document.getElementById('gameStatus').textContent = 'Market session in progress - manage your portfolio!';

    // Update asset prices periodically
    updateAssetPrices();

    // Enable portfolio management
    document.getElementById('gameStatus').textContent = 'Manage portfolio then spin roulette when ready!';
}

// Manage portfolio with skill influence
function managePortfolio() {
    if (!rogueTraderGame.isPlaying) return;

    // Calculate portfolio optimization
    const optimization = calculatePortfolioOptimization();

    // Apply skill-based improvements
    const skillBonus = rogueTraderGame.stats.skillPoints * 0.01;
    rogueTraderGame.portfolio.diversification = Math.min(0.90, rogueTraderGame.portfolio.diversification + skillBonus);
    rogueTraderGame.portfolio.sharpeRatio = Math.min(3.0, rogueTraderGame.portfolio.sharpeRatio + skillBonus);

    // Update portfolio value
    rogueTraderGame.portfolio.totalValue *= (1 + optimization.expectedReturn);
    rogueTraderGame.stats.portfolioValue = rogueTraderGame.portfolio.totalValue;

    // Award skill points
    rogueTraderGame.stats.skillPoints += 1;

    // Visual feedback
    document.getElementById('tradingEvent').classList.remove('hidden');
    document.getElementById('tradingEvent').textContent = 'PORTFOLIO OPTIMIZED!';
    setTimeout(() => {
        document.getElementById('tradingEvent').classList.add('hidden');
    }, 2000);

    updatePortfolioDisplay();
}

// Calculate portfolio optimization
function calculatePortfolioOptimization() {
    const marketData = MARKET_TYPES[rogueTraderGame.marketType];
    const riskData = RISK_LEVELS[rogueTraderGame.riskLevel];

    // Modern Portfolio Theory calculations
    const expectedReturn = riskData.expectedReturn * (1 + rogueTraderGame.portfolio.diversification * 0.2);
    const riskAdjustment = rogueTraderGame.portfolio.riskExposure * marketData.riskAdjustment;

    return {
        expectedReturn: expectedReturn - riskAdjustment,
        riskReduction: rogueTraderGame.portfolio.diversification * 0.1,
        sharpeImprovement: 0.1
    };
}

// Rebalance assets
function rebalanceAssets() {
    if (!rogueTraderGame.isPlaying) return;

    // Rebalancing improves diversification
    rogueTraderGame.portfolio.diversification = Math.min(0.95, rogueTraderGame.portfolio.diversification + 0.05);
    rogueTraderGame.portfolio.riskExposure = Math.max(0.10, rogueTraderGame.portfolio.riskExposure - 0.05);

    // Award skill points
    rogueTraderGame.stats.skillPoints += 0.5;

    // Visual feedback
    document.getElementById('tradingEvent').classList.remove('hidden');
    document.getElementById('tradingEvent').textContent = 'ASSETS REBALANCED!';
    setTimeout(() => {
        document.getElementById('tradingEvent').classList.add('hidden');
    }, 2000);

    updatePortfolioDisplay();
}

// Hedge risk
function hedgeRisk() {
    if (!rogueTraderGame.isPlaying) return;

    // Hedging reduces risk exposure
    rogueTraderGame.portfolio.riskExposure = Math.max(0.05, rogueTraderGame.portfolio.riskExposure - 0.10);
    rogueTraderGame.portfolio.maxDrawdown = Math.max(0.02, rogueTraderGame.portfolio.maxDrawdown - 0.02);

    // Award skill points
    rogueTraderGame.stats.skillPoints += 0.5;

    // Visual feedback
    document.getElementById('tradingEvent').classList.remove('hidden');
    document.getElementById('tradingEvent').textContent = 'RISK HEDGED!';
    setTimeout(() => {
        document.getElementById('tradingEvent').classList.add('hidden');
    }, 2000);

    updatePortfolioDisplay();
}

// Add asset to portfolio
function addAssetToPortfolio(assetIndex) {
    if (!rogueTraderGame.isPlaying) return;

    const asset = rogueTraderGame.assets[assetIndex];
    if (!asset) return;

    // Add to portfolio positions
    const existingPosition = rogueTraderGame.portfolio.positions.find(p => p.symbol === asset.symbol);
    if (existingPosition) {
        existingPosition.quantity += 1;
    } else {
        rogueTraderGame.portfolio.positions.push({
            symbol: asset.symbol,
            quantity: 1,
            avgPrice: asset.price,
            sector: asset.sector
        });
    }

    // Update diversification based on sector spread
    updateDiversification();

    // Award skill points
    rogueTraderGame.stats.skillPoints += 0.25;
    rogueTraderGame.stats.tradesExecuted++;

    updatePortfolioDisplay();
}

// Update diversification
function updateDiversification() {
    const sectors = [...new Set(rogueTraderGame.portfolio.positions.map(p => p.sector))];
    const sectorCount = sectors.length;
    const maxSectors = 8; // Maximum possible sectors

    rogueTraderGame.portfolio.diversification = Math.min(0.95, sectorCount / maxSectors + 0.20);
}

// Spin market roulette with portfolio influence (3-5% win rate)
function spinMarketRoulette() {
    if (!rogueTraderGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Spinning market roulette...';

    // Calculate portfolio influence on odds
    const portfolioBonus = calculatePortfolioBonus();

    // Spin roulette with improved odds
    const rouletteResult = generateRouletteResult(portfolioBonus);

    // Animate roulette spin
    animateRouletteSpin(rouletteResult);

    // Resolve after spin
    setTimeout(() => {
        resolveRouletteSpin(rouletteResult, portfolioBonus);
    }, 3000);
}

// Calculate portfolio bonus
function calculatePortfolioBonus() {
    const diversificationBonus = rogueTraderGame.portfolio.diversification * 0.15; // Up to 15% bonus
    const riskAdjustment = (1 - rogueTraderGame.portfolio.riskExposure) * 0.10; // Up to 10% bonus
    const sharpeBonus = (rogueTraderGame.portfolio.sharpeRatio - 1.0) * 0.05; // Sharpe ratio bonus
    const skillBonus = rogueTraderGame.stats.skillPoints * 0.002; // Skill points bonus

    return Math.min(0.25, diversificationBonus + riskAdjustment + sharpeBonus + skillBonus);
}

// Generate roulette result with portfolio influence (improved for 3-5% win rate)
function generateRouletteResult(portfolioBonus) {
    const marketData = MARKET_TYPES[rogueTraderGame.marketType];

    // Apply portfolio bonus to improve odds
    const adjustedOdds = 0.027 + portfolioBonus; // Base 2.7% + portfolio bonus

    // Generate result
    let result = {
        number: Math.floor(Math.random() * 37), // 0-36
        color: 'black',
        sector: rogueTraderGame.rouletteWheel.sectors[Math.floor(Math.random() * 12)],
        isWin: false,
        multiplier: 1.0
    };

    // Determine color
    if (result.number === 0) {
        result.color = 'green';
    } else if ([1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(result.number)) {
        result.color = 'red';
    } else {
        result.color = 'black';
    }

    // Check for win with improved odds
    if (Math.random() < adjustedOdds) {
        result.isWin = true;

        // Determine win type and multiplier
        if (result.number === rogueTraderGame.betAmount % 37) { // Straight up bet simulation
            result.multiplier = 35.0; // 35:1 payout
        } else if (result.color === 'red' || result.color === 'black') {
            result.multiplier = 2.0; // Even money bet
        } else {
            result.multiplier = 1.5; // Modest win
        }
    }

    return result;
}

// Animate roulette spin
function animateRouletteSpin(result) {
    const rouletteWheel = document.getElementById('rouletteWheel');
    const rouletteBall = document.getElementById('rouletteBall');
    const rouletteResult = document.getElementById('rouletteResult');

    // Add spinning animation
    rouletteWheel.style.transform = 'rotate(1440deg)'; // 4 full rotations
    rouletteWheel.style.transition = 'transform 3s ease-out';

    // Animate ball
    rouletteBall.style.transform = 'rotate(-1800deg)'; // Counter-rotation
    rouletteBall.style.transition = 'transform 3s ease-out';

    // Update result display
    setTimeout(() => {
        rouletteResult.textContent = result.number;
        rouletteResult.className = `text-white font-bold text-lg ${result.color === 'red' ? 'text-red-400' :
                                   result.color === 'green' ? 'text-green-400' : 'text-gray-300'}`;

        // Reset transforms
        rouletteWheel.style.transform = 'rotate(0deg)';
        rouletteWheel.style.transition = 'none';
        rouletteBall.style.transform = 'rotate(0deg)';
        rouletteBall.style.transition = 'none';
    }, 3000);
}

// Resolve roulette spin with portfolio bonuses (3-5% win rate)
function resolveRouletteSpin(result, portfolioBonus) {
    const marketData = MARKET_TYPES[rogueTraderGame.marketType];
    const riskData = RISK_LEVELS[rogueTraderGame.riskLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    if (result.isWin) {
        // Base payout
        totalWinnings = rogueTraderGame.betAmount * result.multiplier;

        // Apply diversification bonus (actually works)
        if (rogueTraderGame.portfolio.diversification >= 0.70 && totalWinnings > 0) {
            const diversificationBonus = Math.floor(totalWinnings * TRADING_PAYOUTS.DIVERSIFICATION_BONUS);
            totalWinnings += diversificationBonus;
            resultMessage += ' + Diversification Bonus!';
        }

        // Apply risk adjustment bonus
        if (rogueTraderGame.portfolio.riskExposure <= 0.30 && totalWinnings > 0) {
            const riskBonus = Math.floor(totalWinnings * TRADING_PAYOUTS.RISK_ADJUSTMENT);
            totalWinnings += riskBonus;
            resultMessage += ' + Risk Management!';
        }

        // Apply portfolio balance bonus
        if (rogueTraderGame.portfolio.positions.length >= 3 && totalWinnings > 0) {
            const balanceBonus = Math.floor(totalWinnings * TRADING_PAYOUTS.PORTFOLIO_BALANCE);
            totalWinnings += balanceBonus;
            resultMessage += ' + Portfolio Balance!';
        }

        // Apply skill trading bonus
        if (rogueTraderGame.stats.skillPoints >= 5 && totalWinnings > 0) {
            const skillBonus = Math.floor(totalWinnings * TRADING_PAYOUTS.SKILL_TRADING);
            totalWinnings += skillBonus;
            resultMessage += ' + Skill Trading!';
        }

        // Check for special portfolio achievements
        if (rogueTraderGame.portfolio.diversification >= 0.90 && rogueTraderGame.portfolio.sharpeRatio >= 2.5) {
            const masterBonus = Math.floor(rogueTraderGame.betAmount * TRADING_PAYOUTS.PORTFOLIO_MASTER / 100);
            totalWinnings += masterBonus;
            resultMessage += ' + Portfolio Master!';
        }

        resultMessage = `Market win!${resultMessage}`;
        rogueTraderGame.stats.profitableTrades++;
    } else {
        resultMessage = 'Market moved against position';

        // Small consolation for good portfolio management (improved)
        if (rogueTraderGame.portfolio.diversification >= 0.80 && Math.random() < 0.15) {
            totalWinnings = Math.floor(rogueTraderGame.betAmount * 0.25); // 25% return
            resultMessage += ' - Portfolio protection bonus!';
        }
    }

    // Apply market type multiplier
    totalWinnings = Math.floor(totalWinnings * marketData.payoutMultiplier);

    // Apply risk level bonus
    totalWinnings = Math.floor(totalWinnings * (1 + riskData.skillBonus));

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(rogueTraderGame.betAmount * 0.4); // 40% return
        resultMessage = 'Market maker rebate';
    }

    // Update portfolio value based on market performance
    const marketPerformance = result.isWin ? 1.02 : 0.98; // 2% swing
    rogueTraderGame.portfolio.totalValue *= marketPerformance;
    rogueTraderGame.stats.portfolioValue = rogueTraderGame.portfolio.totalValue;

    // Calculate return percentage
    const returnPercentage = ((rogueTraderGame.portfolio.totalValue - 1000) / 1000) * 100;
    if (returnPercentage > rogueTraderGame.stats.bestReturn) {
        rogueTraderGame.stats.bestReturn = returnPercentage;
    }

    // Add winnings to balance
    balance += totalWinnings;
    rogueTraderGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(result.isWin, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Market: ${MARKET_TYPES[rogueTraderGame.marketType].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update trading display
function updateTradingDisplay() {
    updateMarketInfo();
    updatePortfolioDisplay();
}

// Update portfolio display
function updatePortfolioDisplay() {
    // Update portfolio value
    document.getElementById('portfolioValueDisplay').textContent = `$${Math.floor(rogueTraderGame.portfolio.totalValue)}`;
    document.getElementById('portfolioValueStat').textContent = `$${Math.floor(rogueTraderGame.portfolio.totalValue)}`;

    // Update performance bar
    const performance = rogueTraderGame.portfolio.winRate * 100;
    document.getElementById('performanceBar').style.width = `${performance}%`;
    document.getElementById('currentPerformance').textContent = `Performance: ${Math.floor(performance)}%`;

    // Update risk meter
    const riskPercentage = rogueTraderGame.portfolio.riskExposure * 100;
    document.getElementById('riskBar').style.width = `${riskPercentage}%`;
    document.getElementById('riskMeter').querySelector('.text-xs:last-child').textContent = `${Math.floor(riskPercentage)}%`;

    // Update diversification score
    const diversificationPercentage = rogueTraderGame.portfolio.diversification * 100;
    document.getElementById('diversificationScore').querySelector('.text-2xl').textContent = `${Math.floor(diversificationPercentage)}%`;

    // Update portfolio holdings
    updatePortfolioHoldings();
}

// Update portfolio holdings
function updatePortfolioHoldings() {
    const holdingsContainer = document.getElementById('portfolioHoldings');
    holdingsContainer.innerHTML = '';

    // Cash position
    const cashDiv = document.createElement('div');
    cashDiv.className = 'flex justify-between';
    cashDiv.innerHTML = `
        <span class="text-gray-400">Cash:</span>
        <span class="text-green-400">$${Math.floor(rogueTraderGame.portfolio.cash)}</span>
    `;
    holdingsContainer.appendChild(cashDiv);

    // Asset positions
    const sectorTotals = {};
    rogueTraderGame.portfolio.positions.forEach(position => {
        const asset = rogueTraderGame.assets.find(a => a.symbol === position.symbol);
        if (asset) {
            const value = position.quantity * asset.price;
            if (!sectorTotals[asset.sector]) {
                sectorTotals[asset.sector] = 0;
            }
            sectorTotals[asset.sector] += value;
        }
    });

    // Display sector totals
    Object.entries(sectorTotals).forEach(([sector, value]) => {
        const sectorDiv = document.createElement('div');
        sectorDiv.className = 'flex justify-between';
        sectorDiv.innerHTML = `
            <span class="text-gray-400">${sector}:</span>
            <span class="text-blue-400">$${Math.floor(value)}</span>
        `;
        holdingsContainer.appendChild(sectorDiv);
    });
}

// Update market effects
function updateMarketEffects() {
    // Update market effects based on volatility
    const volatility = rogueTraderGame.market.volatility;
    const effects = document.querySelectorAll('#marketEffects circle');

    effects.forEach((effect, index) => {
        const intensity = volatility * 2; // Scale volatility
        effect.setAttribute('opacity', Math.min(0.8, intensity));

        if (rogueTraderGame.market.trend === 'bullish') {
            effect.setAttribute('fill', '#00ff00');
        } else if (rogueTraderGame.market.trend === 'bearish') {
            effect.setAttribute('fill', '#ff0000');
        } else {
            effect.setAttribute('fill', '#ffa500');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${rogueTraderGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = rogueTraderGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${rogueTraderGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${rogueTraderGame.stats.totalWagered}`;
    document.getElementById('bestReturn').textContent = `${rogueTraderGame.stats.bestReturn.toFixed(1)}%`;

    const netResult = rogueTraderGame.stats.totalWon - rogueTraderGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-orange-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    rogueTraderGame.stats.spinsPlayed++;
    rogueTraderGame.stats.totalWagered += rogueTraderGame.betAmount;
    rogueTraderGame.stats.totalWon += winnings;

    if (won) {
        rogueTraderGame.stats.spinsWon++;
        rogueTraderGame.stats.currentStreak++;
        rogueTraderGame.streakData.currentWinStreak++;
        rogueTraderGame.streakData.currentLossStreak = 0;

        if (rogueTraderGame.streakData.currentWinStreak > rogueTraderGame.streakData.longestWinStreak) {
            rogueTraderGame.streakData.longestWinStreak = rogueTraderGame.streakData.currentWinStreak;
        }

        if (winnings > rogueTraderGame.stats.biggestWin) {
            rogueTraderGame.stats.biggestWin = winnings;
        }
    } else {
        rogueTraderGame.stats.currentStreak = 0;
        rogueTraderGame.streakData.currentWinStreak = 0;
        rogueTraderGame.streakData.currentLossStreak++;

        if (rogueTraderGame.streakData.currentLossStreak > rogueTraderGame.streakData.longestLossStreak) {
            rogueTraderGame.streakData.longestLossStreak = rogueTraderGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to portfolio mechanics)
    rogueTraderGame.stats.winRate = (rogueTraderGame.stats.spinsWon / rogueTraderGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next trading session
function resetGame() {
    rogueTraderGame.isPlaying = false;
    rogueTraderGame.betAmount = 0;
    rogueTraderGame.totalBet = 0;
    rogueTraderGame.gameResult = '';
    rogueTraderGame.totalWin = 0;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('tradingEvent').classList.add('hidden');
    document.getElementById('tradingActions').classList.add('hidden');

    // Reset roulette display
    document.getElementById('rouletteResult').textContent = '?';
    document.getElementById('rouletteResult').className = 'text-white font-bold text-lg';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startTrading').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Markets open...';
    document.getElementById('gameMessage').textContent = 'Welcome to Rogue Trader Roulette - Manage Your Portfolio Between Spins';

    // Update asset prices for next session
    updateAssetPrices();

    // Reinitialize systems for next session
    initializeTradingSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRogueTraderGame();
});