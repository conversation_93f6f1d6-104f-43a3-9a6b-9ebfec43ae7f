// Golf Swing Payout - Stroke-Based Accuracy Multipliers
// Skill-Based Hybrid Implementation with Golf Theme
// Designed to maintain 3-5% player win rate with skill elements

// Game state
let balance = 1000;

// Game state object with golf course system
let golfSwingGame = {
    isPlaying: false,
    courseType: 'municipal', // municipal, country_club, championship, pga_tour, masters
    difficulty: 'beginner', // beginner, amateur, pro, tour_pro, legend
    betAmount: 0,
    totalBet: 0,

    // Golf course system
    course: {
        currentHole: 1,
        totalHoles: 9,
        par: 4,
        distance: 350, // yards
        hazards: ['sand', 'water'],
        wind: { speed: 5, direction: 'headwind' }, // mph
        green: { slope: 'moderate', speed: 8 },
        weather: 'sunny',
        temperature: 72 // fahrenheit
    },

    // Golf swing mechanics
    swing: {
        power: 0.50, // 50% power
        accuracy: 0.60, // 60% accuracy
        timing: 0.55, // 55% timing
        club: 'driver', // driver, iron, wedge, putter
        stance: 'normal', // normal, open, closed
        grip: 'standard', // standard, strong, weak
        backswing: 0.50, // 50% backswing
        followThrough: 0.50, // 50% follow-through
        ballPosition: 'center' // forward, center, back
    },

    // Stroke tracking system
    strokes: {
        currentStroke: 1,
        maxStrokes: 6, // max strokes per hole
        strokeHistory: [],
        accuracy: 0.60, // 60% accuracy (improved)
        consistency: 0.55, // 55% consistency
        skillLevel: 0.40, // 40% skill level (increased)
        handicap: 18, // golf handicap
        bestRound: 100,
        averageScore: 95
    },

    // Golf equipment and conditions
    equipment: [
        { name: 'Driver', distance: 250, accuracy: 0.70, loft: 10.5 },
        { name: '3-Wood', distance: 220, accuracy: 0.75, loft: 15 },
        { name: '5-Iron', distance: 160, accuracy: 0.80, loft: 27 },
        { name: '7-Iron', distance: 140, accuracy: 0.85, loft: 34 },
        { name: '9-Iron', distance: 120, accuracy: 0.90, loft: 42 },
        { name: 'Pitching Wedge', distance: 100, accuracy: 0.95, loft: 48 },
        { name: 'Sand Wedge', distance: 80, accuracy: 0.85, loft: 56 },
        { name: 'Putter', distance: 30, accuracy: 0.98, loft: 4 }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        holesPlayed: 0,
        holesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        perfectShots: 0,
        eaglesScored: 0,
        birdiesScored: 0,
        parsScored: 0,
        skillPoints: 0,
        bestAccuracy: 0.60
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Course types with balanced skill requirements (3-5% win rate)
const COURSE_TYPES = {
    municipal: {
        name: 'Municipal Course',
        skillWeight: 0.30, // 30% skill influence (increased)
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.90, // Good payouts
        accuracyBonus: 0.25, // 25% accuracy bonus
        difficultyFactor: 0.80 // 80% difficulty (easier)
    },
    country_club: {
        name: 'Country Club',
        skillWeight: 0.35, // 35% skill influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.95, // Better payouts
        accuracyBonus: 0.30, // 30% accuracy bonus
        difficultyFactor: 0.85 // 85% difficulty
    },
    championship: {
        name: 'Championship Course',
        skillWeight: 0.40, // 40% skill influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.00, // Full payouts
        accuracyBonus: 0.35, // 35% accuracy bonus
        difficultyFactor: 0.90 // 90% difficulty
    },
    pga_tour: {
        name: 'PGA Tour Course',
        skillWeight: 0.45, // 45% skill influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        accuracyBonus: 0.40, // 40% accuracy bonus
        difficultyFactor: 0.95 // 95% difficulty
    },
    masters: {
        name: 'Masters Tournament',
        skillWeight: 0.50, // 50% skill influence
        luckFactor: 0.50, // 50% luck factor
        payoutMultiplier: 1.20, // Best payouts
        accuracyBonus: 0.45, // 45% accuracy bonus
        difficultyFactor: 1.00 // 100% difficulty (hardest)
    }
};

const DIFFICULTY_LEVELS = {
    beginner: {
        name: 'Beginner',
        skillRequirement: 0.20, // 20% skill requirement
        accuracyBonus: 0.30, // 30% accuracy bonus
        strokePenalty: 0.10 // 10% stroke penalty
    },
    amateur: {
        name: 'Amateur',
        skillRequirement: 0.30, // 30% skill requirement
        accuracyBonus: 0.25, // 25% accuracy bonus
        strokePenalty: 0.15 // 15% stroke penalty
    },
    pro: {
        name: 'Professional',
        skillRequirement: 0.40, // 40% skill requirement
        accuracyBonus: 0.20, // 20% accuracy bonus
        strokePenalty: 0.20 // 20% stroke penalty
    },
    tour_pro: {
        name: 'Tour Professional',
        skillRequirement: 0.50, // 50% skill requirement
        accuracyBonus: 0.15, // 15% accuracy bonus
        strokePenalty: 0.25 // 25% stroke penalty
    },
    legend: {
        name: 'Legend',
        skillRequirement: 0.60, // 60% skill requirement
        accuracyBonus: 0.10, // 10% accuracy bonus
        strokePenalty: 0.30 // 30% stroke penalty
    }
};

// Improved payout table with golf theme (3-5% win rate)
const GOLF_PAYOUTS = {
    // Perfect shots (moderately reduced)
    HOLE_IN_ONE: 1000, // Reduced from 2000:1 but still excellent
    EAGLE: 500, // Reduced from 1000:1
    BIRDIE: 200, // Reduced from 400:1
    PAR: 100, // Reduced from 200:1

    // Accuracy bonuses
    PERFECT_ACCURACY: 300, // Reduced from 600:1
    CONSISTENT_PLAY: 150, // Reduced from 300:1
    SKILL_SHOT: 100, // Reduced from 200:1

    // Stroke-based multipliers (actually apply more often)
    ACCURACY_MULTIPLIER: 0.60, // 60% of displayed bonus (increased)
    CONSISTENCY_BONUS: 0.45, // 45% of displayed bonus (increased)
    SKILL_BONUS: 0.35, // 35% of displayed bonus (increased)
    STROKE_EFFICIENCY: 0.25 // 25% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadGolfSwingGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">GOLF CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">COURSE TYPE</label>
                        <select id="courseType" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="municipal">Municipal Course</option>
                            <option value="country_club">Country Club</option>
                            <option value="championship">Championship Course</option>
                            <option value="pga_tour">PGA Tour Course</option>
                            <option value="masters">Masters Tournament</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIFFICULTY</label>
                        <select id="difficulty" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="beginner">Beginner</option>
                            <option value="amateur" selected>Amateur</option>
                            <option value="pro">Professional</option>
                            <option value="tour_pro">Tour Professional</option>
                            <option value="legend">Legend</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CLUB SELECTION</label>
                        <select id="clubSelection" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="driver">Driver (250y)</option>
                            <option value="3-wood">3-Wood (220y)</option>
                            <option value="5-iron">5-Iron (160y)</option>
                            <option value="7-iron">7-Iron (140y)</option>
                            <option value="9-iron">9-Iron (120y)</option>
                            <option value="wedge">Pitching Wedge (100y)</option>
                            <option value="sand">Sand Wedge (80y)</option>
                            <option value="putter">Putter (30y)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startRound" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START GOLF ROUND
                    </button>

                    <div id="swingActions" class="space-y-2 hidden">
                        <div class="mb-3">
                            <label class="block text-sm mb-2 text-gray-300">SWING POWER</label>
                            <input type="range" id="swingPower" min="0" max="100" value="50"
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-400 mt-1">
                                <span>Soft</span>
                                <span id="powerValue">50%</span>
                                <span>Full</span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="block text-sm mb-2 text-gray-300">ACCURACY</label>
                            <input type="range" id="swingAccuracy" min="0" max="100" value="60"
                                   class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                            <div class="flex justify-between text-xs text-gray-400 mt-1">
                                <span>Wild</span>
                                <span id="accuracyValue">60%</span>
                                <span>Precise</span>
                            </div>
                        </div>

                        <button id="takeShot" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            TAKE SHOT
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Skill Points</div>
                        <div id="skillPointsDisplay" class="text-lg font-bold text-green-400">0</div>
                    </div>
                </div>

                <!-- Golf Course Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">COURSE STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="currentHole" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">HOLE: 1/9</div>
                        </div>
                        <div id="holePar" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">PAR: 4</div>
                        </div>
                        <div id="holeDistance" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">350 YDS</div>
                        </div>
                        <div id="windConditions" class="text-center p-2 rounded bg-black/50">
                            <div class="text-cyan-400 font-bold">WIND: 5mph</div>
                        </div>
                    </div>
                </div>

                <!-- Improved Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">GOLF PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Perfect Shots:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Hole-in-One:</span>
                            <span class="text-green-400">1000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Eagle:</span>
                            <span class="text-green-400">500:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Birdie:</span>
                            <span class="text-green-400">200:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Par:</span>
                            <span class="text-green-400">100:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Skill Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Accuracy:</span>
                            <span class="text-green-400">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Consistency:</span>
                            <span class="text-green-400">45%</span>
                        </div>
                        <div class="text-xs text-green-400 mt-2">*Skill improves accuracy</div>
                        <div class="text-xs text-green-400">*Stroke efficiency matters</div>
                    </div>
                </div>
            </div>

            <!-- Main Golf Course Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <!-- Golf Course -->
                    <div id="golfCourse" class="relative bg-gradient-to-br from-green-900 via-green-800 to-green-900 rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Course Background -->
                        <div id="courseBackground" class="absolute inset-0 pointer-events-none opacity-60">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="greenGradient" cx="80%" cy="80%" r="40%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#90EE90;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#228B22;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="grassPattern" width="60" height="60" patternUnits="userSpaceOnUse">
                                        <rect width="60" height="60" fill="#228B22" opacity="0.3"/>
                                        <circle cx="30" cy="30" r="15" fill="none" stroke="#90EE90" stroke-width="1" opacity="0.4"/>
                                        <circle cx="30" cy="30" r="5" fill="#ffffff" opacity="0.6"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#grassPattern)" />
                                <circle id="golfHole" cx="80%" cy="80%" r="3%" fill="url(#greenGradient)" class="animate-pulse" />
                                <g id="courseEffects">
                                    <!-- Course effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Golf Ball and Trajectory -->
                        <div class="absolute bottom-8 left-8">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">TEE POSITION</div>
                                <div id="golfBall" class="w-6 h-6 bg-white rounded-full border-2 border-gray-300 mx-auto animate-pulse"></div>
                                <div class="text-xs text-gray-400 mt-1">Ball Position</div>
                            </div>
                        </div>

                        <!-- Swing Meter -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">SWING METER</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="swingMeter" class="bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 h-4 rounded-full transition-all duration-300" style="width: 50%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Weak</span>
                                    <span id="currentSwing">Perfect Zone</span>
                                    <span>Over</span>
                                </div>
                            </div>
                        </div>

                        <!-- Accuracy Indicator -->
                        <div id="accuracyIndicator" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">ACCURACY</div>
                                <div class="w-20 bg-gray-700 rounded-full h-3">
                                    <div id="accuracyBar" class="bg-yellow-400 h-3 rounded-full transition-all duration-300" style="width: 60%"></div>
                                </div>
                                <div class="text-xs text-yellow-400 mt-1">60%</div>
                            </div>
                        </div>

                        <!-- Stroke Counter -->
                        <div id="strokeCounter" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">STROKES</div>
                                <div class="text-2xl font-bold text-white text-center">1</div>
                                <div class="text-xs text-gray-400 mt-1">Current</div>
                            </div>
                        </div>

                        <!-- Wind Indicator -->
                        <div id="windIndicator" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-cyan-400 mb-1">WIND</div>
                                <div class="flex items-center space-x-2">
                                    <div class="text-lg">🌬️</div>
                                    <div class="text-sm text-white">5mph</div>
                                </div>
                                <div class="text-xs text-cyan-400 mt-1">Headwind</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Ready to tee off...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="golfEvent" class="text-sm font-bold text-green-400 hidden animate-pulse">PERFECT SHOT!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Golf Swing Payout - Master Your Stroke Accuracy</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Holes Played</div>
                <div id="holesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-green-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-green-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Perfect Shots</div>
                <div id="perfectShots" class="text-xl font-bold text-yellow-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Best Accuracy</div>
                <div id="bestAccuracy" class="text-xl font-bold text-cyan-400">60%</div>
            </div>
        </div>
    `;

    initializeGolfSwing();
}

// Initialize the game
function initializeGolfSwing() {
    document.getElementById('startRound').addEventListener('click', startGolfRound);
    document.getElementById('takeShot').addEventListener('click', takeGolfShot);

    // Add swing control listeners
    document.getElementById('swingPower').addEventListener('input', updateSwingPower);
    document.getElementById('swingAccuracy').addEventListener('input', updateSwingAccuracy);

    // Initialize golf systems
    initializeGolfSystems();
    generateCourseEffects();
    updateGameStats();
}

// Initialize golf systems
function initializeGolfSystems() {
    // Reset course system
    golfSwingGame.course.currentHole = 1;
    golfSwingGame.course.totalHoles = 9;
    golfSwingGame.course.par = generateRandomPar();
    golfSwingGame.course.distance = generateRandomDistance();
    golfSwingGame.course.hazards = generateRandomHazards();
    golfSwingGame.course.wind = generateRandomWind();
    golfSwingGame.course.green = { slope: getRandomSlope(), speed: Math.floor(Math.random() * 5) + 6 };
    golfSwingGame.course.weather = getRandomWeather();
    golfSwingGame.course.temperature = Math.floor(Math.random() * 30) + 60;

    // Reset swing mechanics
    golfSwingGame.swing.power = 0.50;
    golfSwingGame.swing.accuracy = 0.60;
    golfSwingGame.swing.timing = 0.55;
    golfSwingGame.swing.club = 'driver';
    golfSwingGame.swing.stance = 'normal';
    golfSwingGame.swing.grip = 'standard';
    golfSwingGame.swing.backswing = 0.50;
    golfSwingGame.swing.followThrough = 0.50;
    golfSwingGame.swing.ballPosition = 'center';

    // Reset stroke tracking
    golfSwingGame.strokes.currentStroke = 1;
    golfSwingGame.strokes.maxStrokes = 6;
    golfSwingGame.strokes.strokeHistory = [];
    golfSwingGame.strokes.accuracy = Math.min(0.80, 0.60 + (golfSwingGame.stats.skillPoints * 0.01));
    golfSwingGame.strokes.consistency = Math.min(0.75, 0.55 + (golfSwingGame.stats.skillPoints * 0.008));
    golfSwingGame.strokes.skillLevel = Math.min(0.60, 0.40 + (golfSwingGame.stats.skillPoints * 0.01));
    golfSwingGame.strokes.handicap = Math.max(0, 18 - Math.floor(golfSwingGame.stats.skillPoints / 10));

    updateGolfDisplay();
}

// Generate course effects
function generateCourseEffects() {
    const container = document.getElementById('courseEffects');
    container.innerHTML = '';

    // Add trees
    for (let i = 0; i < 5; i++) {
        const tree = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        tree.setAttribute('cx', `${Math.random() * 60 + 10}%`);
        tree.setAttribute('cy', `${Math.random() * 60 + 10}%`);
        tree.setAttribute('r', `${Math.random() * 3 + 2}%`);
        tree.setAttribute('fill', '#228B22');
        tree.setAttribute('opacity', '0.7');
        container.appendChild(tree);
    }

    // Add sand traps
    for (let i = 0; i < 2; i++) {
        const sand = document.createElementNS('http://www.w3.org/2000/svg', 'ellipse');
        sand.setAttribute('cx', `${Math.random() * 40 + 30}%`);
        sand.setAttribute('cy', `${Math.random() * 40 + 30}%`);
        sand.setAttribute('rx', `${Math.random() * 5 + 3}%`);
        sand.setAttribute('ry', `${Math.random() * 3 + 2}%`);
        sand.setAttribute('fill', '#F4A460');
        sand.setAttribute('opacity', '0.8');
        container.appendChild(sand);
    }
}

// Generate random course elements
function generateRandomPar() {
    const pars = [3, 4, 4, 4, 5];
    return pars[Math.floor(Math.random() * pars.length)];
}

function generateRandomDistance() {
    const par = golfSwingGame.course.par;
    if (par === 3) return Math.floor(Math.random() * 50) + 120; // 120-170 yards
    if (par === 4) return Math.floor(Math.random() * 100) + 300; // 300-400 yards
    if (par === 5) return Math.floor(Math.random() * 150) + 450; // 450-600 yards
    return 350;
}

function generateRandomHazards() {
    const allHazards = ['sand', 'water', 'trees', 'rough', 'bunker'];
    const numHazards = Math.floor(Math.random() * 3) + 1;
    const hazards = [];
    for (let i = 0; i < numHazards; i++) {
        hazards.push(allHazards[Math.floor(Math.random() * allHazards.length)]);
    }
    return [...new Set(hazards)]; // Remove duplicates
}

function generateRandomWind() {
    const directions = ['headwind', 'tailwind', 'crosswind-left', 'crosswind-right'];
    return {
        speed: Math.floor(Math.random() * 15) + 2, // 2-16 mph
        direction: directions[Math.floor(Math.random() * directions.length)]
    };
}

function getRandomSlope() {
    const slopes = ['flat', 'slight', 'moderate', 'steep'];
    return slopes[Math.floor(Math.random() * slopes.length)];
}

function getRandomWeather() {
    const weather = ['sunny', 'cloudy', 'overcast', 'light-rain'];
    return weather[Math.floor(Math.random() * weather.length)];
}

// Start golf round
function startGolfRound() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    golfSwingGame.isPlaying = true;
    golfSwingGame.betAmount = betAmount;
    golfSwingGame.totalBet = betAmount;
    golfSwingGame.courseType = document.getElementById('courseType').value;
    golfSwingGame.difficulty = document.getElementById('difficulty').value;
    golfSwingGame.swing.club = document.getElementById('clubSelection').value;
    golfSwingGame.strokes.currentStroke = 1;

    // Activate golf systems
    activateGolfSystems();

    // Start hole simulation
    setTimeout(() => {
        startHole();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startRound').disabled = true;
    document.getElementById('swingActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Approaching the tee...';
}

// Activate golf systems
function activateGolfSystems() {
    const courseData = COURSE_TYPES[golfSwingGame.courseType];
    const difficultyData = DIFFICULTY_LEVELS[golfSwingGame.difficulty];

    golfSwingGame.strokes.skillLevel = courseData.skillWeight;
    golfSwingGame.strokes.accuracy += difficultyData.accuracyBonus;

    // Update course information
    updateCourseInfo();

    // Update visual effects
    updateGolfDisplay();
    updateCourseEffects();
}

// Update course information
function updateCourseInfo() {
    document.getElementById('currentHole').innerHTML =
        `<div class="text-yellow-400 font-bold">HOLE: ${golfSwingGame.course.currentHole}/${golfSwingGame.course.totalHoles}</div>`;
    document.getElementById('holePar').innerHTML =
        `<div class="text-blue-400 font-bold">PAR: ${golfSwingGame.course.par}</div>`;
    document.getElementById('holeDistance').innerHTML =
        `<div class="text-purple-400 font-bold">${golfSwingGame.course.distance} YDS</div>`;
    document.getElementById('windConditions').innerHTML =
        `<div class="text-cyan-400 font-bold">WIND: ${golfSwingGame.course.wind.speed}mph</div>`;

    // Update wind indicator
    document.getElementById('windIndicator').querySelector('.text-sm').textContent = `${golfSwingGame.course.wind.speed}mph`;
    document.getElementById('windIndicator').querySelector('.text-xs:last-child').textContent =
        golfSwingGame.course.wind.direction.replace('-', ' ').toUpperCase();
}

// Start hole
function startHole() {
    document.getElementById('gameStatus').textContent = 'Set up your shot and swing when ready!';

    // Update stroke counter
    document.getElementById('strokeCounter').querySelector('.text-2xl').textContent = golfSwingGame.strokes.currentStroke;
}

// Update swing power
function updateSwingPower(event) {
    const power = parseInt(event.target.value);
    golfSwingGame.swing.power = power / 100;

    document.getElementById('powerValue').textContent = `${power}%`;

    // Update swing meter
    updateSwingMeter();
}

// Update swing accuracy
function updateSwingAccuracy(event) {
    const accuracy = parseInt(event.target.value);
    golfSwingGame.swing.accuracy = accuracy / 100;

    document.getElementById('accuracyValue').textContent = `${accuracy}%`;
    document.getElementById('accuracyBar').style.width = `${accuracy}%`;
    document.getElementById('accuracyIndicator').querySelector('.text-xs:last-child').textContent = `${accuracy}%`;

    // Update swing meter
    updateSwingMeter();
}

// Update swing meter
function updateSwingMeter() {
    const power = golfSwingGame.swing.power;
    const accuracy = golfSwingGame.swing.accuracy;

    // Calculate optimal zone (60-80% power with high accuracy)
    const isOptimal = power >= 0.60 && power <= 0.80 && accuracy >= 0.70;

    document.getElementById('swingMeter').style.width = `${power * 100}%`;

    if (isOptimal) {
        document.getElementById('currentSwing').textContent = 'Perfect Zone';
        document.getElementById('swingMeter').className = 'bg-gradient-to-r from-green-400 to-green-600 h-4 rounded-full transition-all duration-300';
    } else if (power < 0.30) {
        document.getElementById('currentSwing').textContent = 'Too Weak';
        document.getElementById('swingMeter').className = 'bg-gradient-to-r from-red-400 to-red-600 h-4 rounded-full transition-all duration-300';
    } else if (power > 0.90) {
        document.getElementById('currentSwing').textContent = 'Too Strong';
        document.getElementById('swingMeter').className = 'bg-gradient-to-r from-red-400 to-red-600 h-4 rounded-full transition-all duration-300';
    } else {
        document.getElementById('currentSwing').textContent = 'Good Zone';
        document.getElementById('swingMeter').className = 'bg-gradient-to-r from-yellow-400 to-yellow-600 h-4 rounded-full transition-all duration-300';
    }
}

// Take golf shot with skill influence (3-5% win rate)
function takeGolfShot() {
    if (!golfSwingGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Taking shot...';

    // Calculate shot result with skill influence
    const shotResult = calculateShotResult();

    // Add to stroke history
    golfSwingGame.strokes.strokeHistory.push(shotResult);

    // Animate shot
    animateGolfShot(shotResult);

    // Update stroke counter
    golfSwingGame.strokes.currentStroke++;
    document.getElementById('strokeCounter').querySelector('.text-2xl').textContent = golfSwingGame.strokes.currentStroke;

    // Check if hole is complete
    setTimeout(() => {
        if (shotResult.result === 'hole' || golfSwingGame.strokes.currentStroke > golfSwingGame.strokes.maxStrokes) {
            resolveHole();
        } else {
            document.getElementById('gameStatus').textContent = 'Set up your next shot...';
        }
    }, 2000);
}

// Calculate shot result with skill influence (improved for 3-5% win rate)
function calculateShotResult() {
    const courseData = COURSE_TYPES[golfSwingGame.courseType];
    const difficultyData = DIFFICULTY_LEVELS[golfSwingGame.difficulty];
    const playerSkillPoints = golfSwingGame.stats.skillPoints;

    // Base accuracy calculation
    let shotAccuracy = golfSwingGame.swing.accuracy;

    // Apply skill influence (increased)
    const skillInfluence = (playerSkillPoints * 0.005) + courseData.skillWeight;
    shotAccuracy += skillInfluence;

    // Apply power influence
    const powerOptimal = Math.abs(golfSwingGame.swing.power - 0.70); // Optimal at 70%
    shotAccuracy -= powerOptimal * 0.3; // Penalty for non-optimal power

    // Apply wind influence
    const windPenalty = golfSwingGame.course.wind.speed * 0.01;
    shotAccuracy -= windPenalty;

    // Apply difficulty penalty
    shotAccuracy -= difficultyData.strokePenalty;

    // Ensure accuracy stays within bounds
    shotAccuracy = Math.max(0.1, Math.min(0.95, shotAccuracy));

    // Determine shot result
    let result = 'miss';
    let distance = 0;
    let accuracy = shotAccuracy;

    if (shotAccuracy >= 0.90) {
        // Perfect shot
        if (golfSwingGame.strokes.currentStroke === 1 && Math.random() < 0.02) { // 2% hole-in-one chance
            result = 'hole-in-one';
            golfSwingGame.stats.perfectShots++;
        } else {
            result = 'perfect';
            golfSwingGame.stats.perfectShots++;
        }
        distance = getClubDistance() * golfSwingGame.swing.power;
    } else if (shotAccuracy >= 0.75) {
        // Excellent shot
        result = 'excellent';
        distance = getClubDistance() * golfSwingGame.swing.power * 0.95;
    } else if (shotAccuracy >= 0.60) {
        // Good shot
        result = 'good';
        distance = getClubDistance() * golfSwingGame.swing.power * 0.85;
    } else if (shotAccuracy >= 0.40) {
        // Fair shot
        result = 'fair';
        distance = getClubDistance() * golfSwingGame.swing.power * 0.70;
    } else {
        // Poor shot
        result = 'poor';
        distance = getClubDistance() * golfSwingGame.swing.power * 0.50;
    }

    // Check if shot reaches hole
    const remainingDistance = golfSwingGame.course.distance - getTotalDistance();
    if (distance >= remainingDistance && result !== 'poor') {
        result = 'hole';
    }

    return {
        result: result,
        distance: distance,
        accuracy: accuracy,
        power: golfSwingGame.swing.power,
        club: golfSwingGame.swing.club
    };
}

// Get club distance
function getClubDistance() {
    const club = golfSwingGame.equipment.find(e => e.name.toLowerCase().includes(golfSwingGame.swing.club));
    return club ? club.distance : 150;
}

// Get total distance covered
function getTotalDistance() {
    return golfSwingGame.strokes.strokeHistory.reduce((total, shot) => total + shot.distance, 0);
}

// Animate golf shot
function animateGolfShot(shotResult) {
    const golfBall = document.getElementById('golfBall');

    // Add shot animation
    golfBall.classList.add('animate-bounce');

    // Show shot result
    document.getElementById('golfEvent').classList.remove('hidden');

    switch (shotResult.result) {
        case 'hole-in-one':
            document.getElementById('golfEvent').textContent = 'HOLE-IN-ONE!';
            golfBall.style.backgroundColor = '#FFD700';
            break;
        case 'perfect':
            document.getElementById('golfEvent').textContent = 'PERFECT SHOT!';
            golfBall.style.backgroundColor = '#90EE90';
            break;
        case 'excellent':
            document.getElementById('golfEvent').textContent = 'EXCELLENT!';
            golfBall.style.backgroundColor = '#87CEEB';
            break;
        case 'good':
            document.getElementById('golfEvent').textContent = 'GOOD SHOT!';
            golfBall.style.backgroundColor = '#F0E68C';
            break;
        case 'fair':
            document.getElementById('golfEvent').textContent = 'FAIR SHOT';
            golfBall.style.backgroundColor = '#DDA0DD';
            break;
        case 'poor':
            document.getElementById('golfEvent').textContent = 'POOR SHOT';
            golfBall.style.backgroundColor = '#F08080';
            break;
        case 'hole':
            document.getElementById('golfEvent').textContent = 'IN THE HOLE!';
            golfBall.style.backgroundColor = '#FFD700';
            break;
    }

    setTimeout(() => {
        golfBall.classList.remove('animate-bounce');
        golfBall.style.backgroundColor = '#ffffff';
        document.getElementById('golfEvent').classList.add('hidden');
    }, 2000);
}

// Resolve hole with skill-based scoring (3-5% win rate)
function resolveHole() {
    const courseData = COURSE_TYPES[golfSwingGame.courseType];
    const difficultyData = DIFFICULTY_LEVELS[golfSwingGame.difficulty];

    let totalWinnings = 0;
    let resultMessage = '';
    let scoreRelativeToPar = 0;

    // Calculate final score
    const finalStrokes = golfSwingGame.strokes.currentStroke - 1; // Subtract 1 because we increment after shot
    const par = golfSwingGame.course.par;
    scoreRelativeToPar = finalStrokes - par;

    // Determine score type and calculate winnings
    if (golfSwingGame.strokes.strokeHistory.some(shot => shot.result === 'hole-in-one')) {
        // Hole-in-one
        totalWinnings = Math.floor(golfSwingGame.betAmount * GOLF_PAYOUTS.HOLE_IN_ONE / 100);
        resultMessage = 'HOLE-IN-ONE!';
        golfSwingGame.stats.skillPoints += 10;
    } else if (scoreRelativeToPar <= -2) {
        // Eagle or better
        totalWinnings = Math.floor(golfSwingGame.betAmount * GOLF_PAYOUTS.EAGLE / 100);
        resultMessage = 'EAGLE!';
        golfSwingGame.stats.eaglesScored++;
        golfSwingGame.stats.skillPoints += 5;
    } else if (scoreRelativeToPar === -1) {
        // Birdie
        totalWinnings = Math.floor(golfSwingGame.betAmount * GOLF_PAYOUTS.BIRDIE / 100);
        resultMessage = 'BIRDIE!';
        golfSwingGame.stats.birdiesScored++;
        golfSwingGame.stats.skillPoints += 3;
    } else if (scoreRelativeToPar === 0) {
        // Par
        totalWinnings = Math.floor(golfSwingGame.betAmount * GOLF_PAYOUTS.PAR / 100);
        resultMessage = 'PAR!';
        golfSwingGame.stats.parsScored++;
        golfSwingGame.stats.skillPoints += 1;
    } else {
        // Over par
        resultMessage = `BOGEY +${scoreRelativeToPar}`;

        // Small consolation for close attempts (improved)
        if (scoreRelativeToPar <= 2 && Math.random() < 0.20) {
            totalWinnings = Math.floor(golfSwingGame.betAmount * 0.25); // 25% return
            resultMessage += ' - Close attempt bonus!';
        }
    }

    // Apply accuracy bonus (actually works)
    const averageAccuracy = golfSwingGame.strokes.strokeHistory.reduce((sum, shot) => sum + shot.accuracy, 0) / golfSwingGame.strokes.strokeHistory.length;
    if (averageAccuracy >= 0.80 && totalWinnings > 0) {
        const accuracyBonus = Math.floor(totalWinnings * GOLF_PAYOUTS.ACCURACY_MULTIPLIER);
        totalWinnings += accuracyBonus;
        resultMessage += ' + Accuracy Bonus!';
    }

    // Apply consistency bonus
    const accuracyVariance = calculateAccuracyVariance();
    if (accuracyVariance <= 0.15 && totalWinnings > 0) {
        const consistencyBonus = Math.floor(totalWinnings * GOLF_PAYOUTS.CONSISTENCY_BONUS);
        totalWinnings += consistencyBonus;
        resultMessage += ' + Consistency Bonus!';
    }

    // Apply skill bonus
    if (golfSwingGame.stats.skillPoints > 0 && totalWinnings > 0) {
        const skillBonus = Math.floor(totalWinnings * GOLF_PAYOUTS.SKILL_BONUS);
        totalWinnings += skillBonus;
        resultMessage += ' + Skill Bonus!';
    }

    // Apply stroke efficiency bonus
    if (finalStrokes <= par && totalWinnings > 0) {
        const efficiencyBonus = Math.floor(totalWinnings * GOLF_PAYOUTS.STROKE_EFFICIENCY);
        totalWinnings += efficiencyBonus;
        resultMessage += ' + Efficiency Bonus!';
    }

    // Apply course multiplier
    totalWinnings = Math.floor(totalWinnings * courseData.payoutMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(golfSwingGame.betAmount * 0.4); // 40% return
        resultMessage = 'Participation reward';
    }

    // Update best accuracy
    if (averageAccuracy > golfSwingGame.stats.bestAccuracy) {
        golfSwingGame.stats.bestAccuracy = averageAccuracy;
    }

    // Add winnings to balance
    balance += totalWinnings;
    golfSwingGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHole(scoreRelativeToPar <= 0, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Course: ${COURSE_TYPES[golfSwingGame.courseType].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Check if round continues
    if (golfSwingGame.course.currentHole < golfSwingGame.course.totalHoles) {
        setTimeout(() => {
            nextHole();
        }, 3000);
    } else {
        setTimeout(() => {
            endRound();
        }, 3000);
    }
}

// Calculate accuracy variance for consistency bonus
function calculateAccuracyVariance() {
    const accuracies = golfSwingGame.strokes.strokeHistory.map(shot => shot.accuracy);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    return Math.sqrt(variance);
}

// Next hole
function nextHole() {
    golfSwingGame.course.currentHole++;

    // Generate new hole
    golfSwingGame.course.par = generateRandomPar();
    golfSwingGame.course.distance = generateRandomDistance();
    golfSwingGame.course.hazards = generateRandomHazards();
    golfSwingGame.course.wind = generateRandomWind();

    // Reset for next hole
    resetForNextHole();

    document.getElementById('gameStatus').textContent = `Hole ${golfSwingGame.course.currentHole} - Approaching tee...`;
    updateCourseInfo();
    startHole();
}

// End round
function endRound() {
    // Round completion bonus
    if (golfSwingGame.stats.skillPoints >= 20) {
        const roundBonus = Math.floor(golfSwingGame.betAmount * 0.5);
        balance += roundBonus;
        updateBalance();

        document.getElementById('gameStatus').textContent = 'Round completed with skill bonus!';
    } else {
        document.getElementById('gameStatus').textContent = 'Round completed!';
    }

    setTimeout(() => resetGame(), 4000);
}

// Update golf display
function updateGolfDisplay() {
    updateCourseInfo();
}

// Update course effects
function updateCourseEffects() {
    // Update course effects based on weather and conditions
    const weather = golfSwingGame.course.weather;
    const effects = document.querySelectorAll('#courseEffects circle');

    effects.forEach((effect, index) => {
        if (weather === 'sunny') {
            effect.setAttribute('opacity', '0.8');
            effect.setAttribute('fill', '#228B22');
        } else if (weather === 'cloudy') {
            effect.setAttribute('opacity', '0.6');
            effect.setAttribute('fill', '#696969');
        } else {
            effect.setAttribute('opacity', '0.4');
            effect.setAttribute('fill', '#2F4F4F');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${golfSwingGame.betAmount}`;
    document.getElementById('skillPointsDisplay').textContent = golfSwingGame.stats.skillPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('holesPlayed').textContent = golfSwingGame.stats.holesPlayed;
    document.getElementById('winRate').textContent = `${golfSwingGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${golfSwingGame.stats.totalWagered}`;
    document.getElementById('perfectShots').textContent = golfSwingGame.stats.perfectShots;
    document.getElementById('bestAccuracy').textContent = `${Math.floor(golfSwingGame.stats.bestAccuracy * 100)}%`;

    const netResult = golfSwingGame.stats.totalWon - golfSwingGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hole
function updateGameStatsAfterHole(won, winnings) {
    golfSwingGame.stats.holesPlayed++;
    golfSwingGame.stats.totalWagered += golfSwingGame.betAmount;
    golfSwingGame.stats.totalWon += winnings;

    if (won) {
        golfSwingGame.stats.holesWon++;
        golfSwingGame.stats.currentStreak++;
        golfSwingGame.streakData.currentWinStreak++;
        golfSwingGame.streakData.currentLossStreak = 0;

        if (golfSwingGame.streakData.currentWinStreak > golfSwingGame.streakData.longestWinStreak) {
            golfSwingGame.streakData.longestWinStreak = golfSwingGame.streakData.currentWinStreak;
        }

        if (winnings > golfSwingGame.stats.biggestWin) {
            golfSwingGame.stats.biggestWin = winnings;
        }
    } else {
        golfSwingGame.stats.currentStreak = 0;
        golfSwingGame.streakData.currentWinStreak = 0;
        golfSwingGame.streakData.currentLossStreak++;

        if (golfSwingGame.streakData.currentLossStreak > golfSwingGame.streakData.longestLossStreak) {
            golfSwingGame.streakData.longestLossStreak = golfSwingGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to skill mechanics)
    golfSwingGame.stats.winRate = (golfSwingGame.stats.holesWon / golfSwingGame.stats.holesPlayed) * 100;

    updateGameStats();
}

// Reset for next hole
function resetForNextHole() {
    golfSwingGame.strokes.currentStroke = 1;
    golfSwingGame.strokes.strokeHistory = [];

    // Reset swing controls
    document.getElementById('swingPower').value = 50;
    document.getElementById('swingAccuracy').value = 60;
    golfSwingGame.swing.power = 0.50;
    golfSwingGame.swing.accuracy = 0.60;

    // Reset displays
    document.getElementById('powerValue').textContent = '50%';
    document.getElementById('accuracyValue').textContent = '60%';
    document.getElementById('accuracyBar').style.width = '60%';
    document.getElementById('accuracyIndicator').querySelector('.text-xs:last-child').textContent = '60%';
    document.getElementById('strokeCounter').querySelector('.text-2xl').textContent = '1';

    // Reset swing meter
    updateSwingMeter();
}

// Reset game for next round
function resetGame() {
    golfSwingGame.isPlaying = false;
    golfSwingGame.betAmount = 0;
    golfSwingGame.totalBet = 0;
    golfSwingGame.gameResult = '';
    golfSwingGame.totalWin = 0;

    // Reset course
    golfSwingGame.course.currentHole = 1;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('golfEvent').classList.add('hidden');
    document.getElementById('swingActions').classList.add('hidden');

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startRound').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Ready to tee off...';
    document.getElementById('gameMessage').textContent = 'Welcome to Golf Swing Payout - Master Your Stroke Accuracy';

    // Reset for next hole
    resetForNextHole();

    // Reinitialize systems for next round
    initializeGolfSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadGolfSwingGame();
});