// Poker Multiverse - Simultaneous Hands Across Parallel Dimensions
// Ultra High House Edge Implementation with Dimensional Manipulation
// Designed to maintain <2% player win rate

// Game state
let balance = 1000;

// Game state object with multiverse system
let pokerMultiverseGame = {
    isPlaying: false,
    multiverseMode: 'standard', // standard, parallel, quantum, infinite
    dimensionCount: 3, // 3, 5, 7, 9, 11 dimensions
    betAmount: 0,
    anteBet: 0,
    playBet: 0,
    totalBet: 0,

    // Player cards and hands across dimensions
    playerCards: [],
    playerHands: [], // Array of hands across dimensions

    // Dealer cards and hands across dimensions
    dealerCards: [],
    dealerHands: [], // Array of hands across dimensions

    // Multiverse system
    multiverse: {
        active: false,
        dimensions: [],
        parallelOutcomes: [],
        dimensionalRifts: [],
        quantumEntanglement: false,
        realityStability: 1.0,
        dimensionalFlux: 0.0,
        convergencePoint: null,
        paradoxLevel: 0
    },

    // Deck management across dimensions
    decks: [], // Separate deck for each dimension
    gameResult: '',
    totalWin: 0,

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        dimensionalShifts: 0,
        paradoxesResolved: 0,
        multiverseCollapses: 0,
        convergenceEvents: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Multiverse modes with extreme dimensional bias
const MULTIVERSE_MODES = {
    standard: {
        name: 'Standard Multiverse',
        houseEdge: 0.72, // 72% house edge
        dimensionalBias: 0.35, // 35% dimensional bias
        payoutMultiplier: 0.28, // Severely reduced payouts
        realityStability: 0.80, // 80% reality stability
        paradoxChance: 0.25 // 25% paradox chance
    },
    parallel: {
        name: 'Parallel Dimensions',
        houseEdge: 0.78, // 78% house edge
        dimensionalBias: 0.45, // 45% dimensional bias
        payoutMultiplier: 0.24, // Even more reduced payouts
        realityStability: 0.70, // 70% reality stability
        paradoxChance: 0.35 // 35% paradox chance
    },
    quantum: {
        name: 'Quantum Multiverse',
        houseEdge: 0.84, // 84% house edge
        dimensionalBias: 0.58, // 58% dimensional bias
        payoutMultiplier: 0.20, // Extremely reduced payouts
        realityStability: 0.55, // 55% reality stability
        paradoxChance: 0.48 // 48% paradox chance
    },
    infinite: {
        name: 'Infinite Dimensions',
        houseEdge: 0.91, // 91% house edge
        dimensionalBias: 0.72, // 72% dimensional bias
        payoutMultiplier: 0.15, // Brutally reduced payouts
        realityStability: 0.35, // 35% reality stability
        paradoxChance: 0.65 // 65% paradox chance
    }
};

const DIMENSION_CONFIGS = {
    3: {
        name: '3 Dimensions',
        cardManipulation: 0.30, // 30% card manipulation
        handDistortion: 0.25, // 25% hand distortion
        convergenceRate: 0.20, // 20% convergence rate
        stabilityPenalty: 0.15 // 15% stability penalty
    },
    5: {
        name: '5 Dimensions',
        cardManipulation: 0.42, // 42% card manipulation
        handDistortion: 0.35, // 35% hand distortion
        convergenceRate: 0.30, // 30% convergence rate
        stabilityPenalty: 0.25 // 25% stability penalty
    },
    7: {
        name: '7 Dimensions',
        cardManipulation: 0.55, // 55% card manipulation
        handDistortion: 0.48, // 48% hand distortion
        convergenceRate: 0.42, // 42% convergence rate
        stabilityPenalty: 0.38 // 38% stability penalty
    },
    9: {
        name: '9 Dimensions',
        cardManipulation: 0.68, // 68% card manipulation
        handDistortion: 0.62, // 62% hand distortion
        convergenceRate: 0.55, // 55% convergence rate
        stabilityPenalty: 0.52 // 52% stability penalty
    },
    11: {
        name: '11 Dimensions',
        cardManipulation: 0.82, // 82% card manipulation
        handDistortion: 0.75, // 75% hand distortion
        convergenceRate: 0.68, // 68% convergence rate
        stabilityPenalty: 0.68 // 68% stability penalty
    }
};

// Severely reduced payout table with dimensional effects
const MULTIVERSE_PAYOUTS = {
    // Standard poker payouts (heavily reduced)
    ROYAL_FLUSH: 150, // Reduced from 500:1
    STRAIGHT_FLUSH: 40, // Reduced from 100:1
    FOUR_OF_A_KIND: 15, // Reduced from 40:1
    FULL_HOUSE: 6, // Reduced from 10:1
    FLUSH: 4, // Reduced from 6:1
    STRAIGHT: 3, // Reduced from 4:1
    THREE_OF_A_KIND: 2, // Reduced from 3:1
    TWO_PAIR: 1.5, // Reduced from 2:1
    PAIR_JACKS_OR_BETTER: 0.8, // Reduced from 1:1

    // Ante bet payouts (terrible)
    ANTE_WIN: 0.65, // Reduced from 1:1

    // Play bet payouts (worse)
    PLAY_WIN: 0.60, // Reduced from 1:1

    // Dimensional bonuses (fake - almost never apply)
    DIMENSIONAL_CONVERGENCE: 0.05, // 5% of displayed bonus
    MULTIVERSE_COLLAPSE: 0.02, // 2% of displayed bonus
    PARADOX_RESOLUTION: 0.01 // 1% of displayed bonus
};

// Standard 52-card deck
const SUITS = ['♠', '♥', '♦', '♣'];
const RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
const CARD_VALUES = {
    '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
    'J': 11, 'Q': 12, 'K': 13, 'A': 14
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadPokerMultiverseGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">MULTIVERSE CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">MULTIVERSE MODE</label>
                        <select id="multiverseMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Multiverse</option>
                            <option value="parallel">Parallel Dimensions</option>
                            <option value="quantum">Quantum Multiverse</option>
                            <option value="infinite">Infinite Dimensions</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIMENSION COUNT</label>
                        <select id="dimensionCount" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="3">3 Dimensions</option>
                            <option value="5" selected>5 Dimensions</option>
                            <option value="7">7 Dimensions</option>
                            <option value="9">9 Dimensions</option>
                            <option value="11">11 Dimensions</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-lg font-bold mb-3 text-cyan-400">BETTING AREA</h5>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Ante Bet:</label>
                                <input type="number" id="anteBet" value="10" min="5" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-cyan-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Play Bet:</label>
                                <input type="number" id="playBet" value="0" min="0" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-cyan-500/50 rounded px-2 py-1 text-white text-sm" disabled>
                            </div>
                        </div>
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        OPEN DIMENSIONAL RIFTS
                    </button>

                    <div id="playerActions" class="space-y-2 hidden">
                        <button id="foldBtn" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            FOLD ALL DIMENSIONS
                        </button>
                        <button id="callBtn" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            CALL ACROSS MULTIVERSE
                        </button>
                        <button id="raiseBtn" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            RAISE DIMENSIONAL STAKES
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Reality Stability</div>
                        <div id="realityStability" class="text-lg font-bold text-cyan-400">100%</div>
                    </div>
                </div>

                <!-- Multiverse Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">MULTIVERSE STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="multiverseStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-cyan-400 font-bold">MULTIVERSE: STABLE</div>
                        </div>
                        <div id="dimensionalRifts" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">RIFTS: CLOSED</div>
                        </div>
                        <div id="quantumEntanglement" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">ENTANGLEMENT: INACTIVE</div>
                        </div>
                        <div id="paradoxLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">PARADOX LEVEL: 0</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">MULTIVERSE PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Poker Hands:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Royal Flush:</span>
                            <span class="text-red-400">150:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">40:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Four of a Kind:</span>
                            <span class="text-red-400">15:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Full House:</span>
                            <span class="text-red-400">6:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Standard Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Ante Win:</span>
                            <span class="text-red-400">0.65:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Play Win:</span>
                            <span class="text-red-400">0.60:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Dimensional flux affects payouts</div>
                        <div class="text-xs text-red-400">*Multiverse collapse may void wins</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div id="pokerTable" class="relative bg-gradient-to-br from-black via-cyan-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Dimensional Rifts Visual Effect -->
                        <div id="dimensionalRifts" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="multiverseGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.4" />
                                        <stop offset="50%" style="stop-color:#0080ff;stop-opacity:0.2" />
                                        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
                                    </radialGradient>
                                    <pattern id="dimensionalGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#00ffff" stroke-width="0.5" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#dimensionalGrid)" />
                                <g id="riftPortals">
                                    <!-- Dimensional rifts will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Dimension Tabs -->
                        <div class="absolute top-2 left-2 right-2">
                            <div id="dimensionTabs" class="flex space-x-1 overflow-x-auto">
                                <!-- Dimension tabs will be generated here -->
                            </div>
                        </div>

                        <!-- Active Dimension Display -->
                        <div id="activeDimension" class="absolute top-12 left-4 right-4 bottom-4">
                            <!-- Player Area -->
                            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                <div class="text-center">
                                    <div class="text-sm text-blue-400 mb-2">PLAYER HAND</div>
                                    <div id="playerCards" class="flex space-x-2 justify-center mb-2">
                                        <!-- Player cards will appear here -->
                                    </div>
                                    <div id="playerHandRank" class="text-lg font-bold text-blue-400">-</div>
                                </div>
                            </div>

                            <!-- Dealer Area -->
                            <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                                <div class="text-center">
                                    <div class="text-sm text-red-400 mb-2">DEALER HAND</div>
                                    <div id="dealerCards" class="flex space-x-2 justify-center mb-2">
                                        <!-- Dealer cards will appear here -->
                                    </div>
                                    <div id="dealerHandRank" class="text-lg font-bold text-red-400">-</div>
                                </div>
                            </div>

                            <!-- Game Status -->
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                                <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Multiverse stabilizing...</div>
                                <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                                <div id="dimensionalEvent" class="text-sm font-bold text-cyan-400 hidden animate-pulse">DIMENSIONAL SHIFT DETECTED!</div>
                            </div>
                        </div>

                        <!-- Dimensional Convergence Visualization -->
                        <div id="convergenceEffects" class="absolute inset-0 pointer-events-none">
                            <!-- Convergence effects will be visualized here -->
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Poker Multiverse - Where Every Hand Exists in Infinite Realities</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Dimensional Shifts</div>
                <div id="dimensionalShifts" class="text-xl font-bold text-cyan-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Multiverse Collapses</div>
                <div id="multiverseCollapses" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializePokerMultiverse();
}

// Initialize the game
function initializePokerMultiverse() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);
    document.getElementById('foldBtn').addEventListener('click', () => playerAction('fold'));
    document.getElementById('callBtn').addEventListener('click', () => playerAction('call'));
    document.getElementById('raiseBtn').addEventListener('click', () => playerAction('raise'));

    // Initialize multiverse system and decks
    initializeMultiverseSystem();
    initializeDecksAcrossDimensions();
    updateGameStats();
}

// Initialize multiverse system
function initializeMultiverseSystem() {
    pokerMultiverseGame.multiverse.active = false;
    pokerMultiverseGame.multiverse.dimensions = [];
    pokerMultiverseGame.multiverse.parallelOutcomes = [];
    pokerMultiverseGame.multiverse.dimensionalRifts = [];
    pokerMultiverseGame.multiverse.quantumEntanglement = false;
    pokerMultiverseGame.multiverse.realityStability = 1.0;
    pokerMultiverseGame.multiverse.dimensionalFlux = 0.0;
    pokerMultiverseGame.multiverse.convergencePoint = null;
    pokerMultiverseGame.multiverse.paradoxLevel = 0;

    updateMultiverseDisplay();
}

// Initialize decks across dimensions
function initializeDecksAcrossDimensions() {
    const dimensionCount = parseInt(document.getElementById('dimensionCount').value) || 5;
    pokerMultiverseGame.decks = [];

    for (let d = 0; d < dimensionCount; d++) {
        const deck = createDimensionalDeck(d);
        pokerMultiverseGame.decks.push(deck);
    }

    generateDimensionTabs();
}

// Create deck for specific dimension with bias
function createDimensionalDeck(dimensionIndex) {
    const deck = [];
    const dimensionConfig = DIMENSION_CONFIGS[pokerMultiverseGame.dimensionCount];

    for (const suit of SUITS) {
        for (const rank of RANKS) {
            const card = {
                rank,
                suit,
                value: CARD_VALUES[rank],
                id: `${rank}${suit}_d${dimensionIndex}`,
                dimension: dimensionIndex,
                dimensionalCharge: Math.random(),
                quantumState: 'classical',
                entangled: false
            };

            deck.push(card);

            // Add extra copies of house-favorable cards based on dimension
            if (Math.random() < dimensionConfig.cardManipulation) {
                // Higher dimensions favor low cards for player, high cards for dealer
                if ((card.value <= 7 && dimensionIndex % 2 === 0) ||
                    (card.value >= 10 && dimensionIndex % 2 === 1)) {
                    const extraCopies = Math.floor(dimensionConfig.cardManipulation * 3);
                    for (let i = 0; i < extraCopies; i++) {
                        deck.push({
                            ...card,
                            id: `${rank}${suit}_d${dimensionIndex}_extra_${i}`,
                            dimensionalCharge: Math.random() + 0.3
                        });
                    }
                }
            }
        }
    }

    return shuffleDimensionalDeck(deck, dimensionIndex);
}

// Shuffle deck with dimensional effects
function shuffleDimensionalDeck(deck, dimensionIndex) {
    const dimensionConfig = DIMENSION_CONFIGS[pokerMultiverseGame.dimensionCount];

    // Standard shuffle
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }

    // Dimensional manipulation - cards with higher charge rise to top
    if (Math.random() < dimensionConfig.cardManipulation) {
        deck.sort((a, b) => {
            // Higher dimensional charge cards are more likely to be dealt
            return b.dimensionalCharge - a.dimensionalCharge;
        });
    }

    return deck;
}

// Generate dimension tabs
function generateDimensionTabs() {
    const container = document.getElementById('dimensionTabs');
    container.innerHTML = '';

    const dimensionCount = parseInt(document.getElementById('dimensionCount').value) || 5;

    for (let d = 0; d < dimensionCount; d++) {
        const tab = document.createElement('div');
        tab.className = `px-3 py-1 rounded text-xs font-bold cursor-pointer transition-all ${d === 0 ? 'bg-cyan-500 text-black' : 'bg-black/50 text-cyan-400 hover:bg-cyan-900/50'}`;
        tab.textContent = `D-${d + 1}`;
        tab.addEventListener('click', () => switchToDimension(d));
        container.appendChild(tab);
    }
}

// Deal new hand across all dimensions
function dealNewHand() {
    const anteBet = parseInt(document.getElementById('anteBet').value);

    if (anteBet > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (anteBet <= 0) {
        alert('Please enter a valid ante bet!');
        return;
    }

    // Deduct ante bet from balance
    balance -= anteBet;
    updateBalance();

    // Set game state
    pokerMultiverseGame.isPlaying = true;
    pokerMultiverseGame.anteBet = anteBet;
    pokerMultiverseGame.playBet = 0;
    pokerMultiverseGame.totalBet = anteBet;
    pokerMultiverseGame.multiverseMode = document.getElementById('multiverseMode').value;
    pokerMultiverseGame.dimensionCount = parseInt(document.getElementById('dimensionCount').value);

    // Clear previous hands
    pokerMultiverseGame.playerCards = [];
    pokerMultiverseGame.playerHands = [];
    pokerMultiverseGame.dealerCards = [];
    pokerMultiverseGame.dealerHands = [];

    // Activate multiverse system
    activateMultiverseSystem();

    // Deal cards across all dimensions
    setTimeout(() => {
        dealCardsAcrossDimensions();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('dealCards').disabled = true;
    document.getElementById('gameStatus').textContent = 'Opening dimensional rifts...';
}

// Activate multiverse system
function activateMultiverseSystem() {
    const modeData = MULTIVERSE_MODES[pokerMultiverseGame.multiverseMode];
    const dimensionConfig = DIMENSION_CONFIGS[pokerMultiverseGame.dimensionCount];

    pokerMultiverseGame.multiverse.active = true;
    pokerMultiverseGame.multiverse.realityStability = modeData.realityStability - (Math.random() * 0.2);
    pokerMultiverseGame.multiverse.dimensionalFlux = Math.random() * modeData.dimensionalBias;

    // Activate dimensional effects based on mode
    if (Math.random() < modeData.paradoxChance) {
        activateDimensionalRifts();
    }

    if (Math.random() < dimensionConfig.convergenceRate) {
        activateQuantumEntanglement();
    }

    if (Math.random() < modeData.paradoxChance) {
        activateParadoxField();
    }

    // Generate dimensional rifts
    generateDimensionalRifts();

    // Update visual effects
    updateMultiverseDisplay();
    updateDimensionalEffects();
}

// Activate dimensional rifts
function activateDimensionalRifts() {
    pokerMultiverseGame.multiverse.dimensionalRifts = [];
    pokerMultiverseGame.stats.dimensionalShifts++;

    document.getElementById('dimensionalRifts').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">RIFTS: ACTIVE</div>';

    // Create rifts between dimensions
    const riftCount = Math.floor(Math.random() * 4) + 2; // 2-5 rifts
    for (let i = 0; i < riftCount; i++) {
        const rift = {
            fromDimension: Math.floor(Math.random() * pokerMultiverseGame.dimensionCount),
            toDimension: Math.floor(Math.random() * pokerMultiverseGame.dimensionCount),
            intensity: Math.random(),
            effect: Math.random() < 0.5 ? 'card_swap' : 'hand_distortion'
        };
        pokerMultiverseGame.multiverse.dimensionalRifts.push(rift);
    }
}

// Activate quantum entanglement
function activateQuantumEntanglement() {
    pokerMultiverseGame.multiverse.quantumEntanglement = true;
    pokerMultiverseGame.stats.convergenceEvents++;

    document.getElementById('quantumEntanglement').innerHTML =
        '<div class="text-green-400 font-bold animate-pulse">ENTANGLEMENT: ACTIVE</div>';

    // Entangle cards across dimensions
    pokerMultiverseGame.decks.forEach(deck => {
        deck.forEach(card => {
            if (Math.random() < 0.3) { // 30% chance of entanglement
                card.entangled = true;
                card.quantumState = 'entangled';
            }
        });
    });
}

// Activate paradox field
function activateParadoxField() {
    pokerMultiverseGame.multiverse.paradoxLevel = Math.floor(Math.random() * 5) + 1;

    document.getElementById('paradoxLevel').innerHTML =
        `<div class="text-red-400 font-bold animate-pulse">PARADOX LEVEL: ${pokerMultiverseGame.multiverse.paradoxLevel}</div>`;

    // Show dimensional event
    document.getElementById('dimensionalEvent').classList.remove('hidden');
    setTimeout(() => {
        document.getElementById('dimensionalEvent').classList.add('hidden');
    }, 3000);
}

// Generate dimensional rifts visualization
function generateDimensionalRifts() {
    const container = document.getElementById('riftPortals');
    container.innerHTML = '';

    pokerMultiverseGame.multiverse.dimensionalRifts.forEach((rift, index) => {
        const portal = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        portal.setAttribute('cx', `${Math.random() * 100}%`);
        portal.setAttribute('cy', `${Math.random() * 100}%`);
        portal.setAttribute('r', `${5 + rift.intensity * 10}`);
        portal.setAttribute('fill', 'url(#multiverseGradient)');
        portal.setAttribute('opacity', '0.6');
        portal.classList.add('animate-pulse');
        container.appendChild(portal);
    });
}

// Deal cards across all dimensions
function dealCardsAcrossDimensions() {
    document.getElementById('gameStatus').textContent = 'Dealing cards across dimensions...';

    // Deal 5 cards to player and dealer in each dimension
    for (let d = 0; d < pokerMultiverseGame.dimensionCount; d++) {
        const playerHand = [];
        const dealerHand = [];

        // Deal 5 cards to each
        for (let c = 0; c < 5; c++) {
            setTimeout(() => {
                const playerCard = dealCardFromDimension(d, 'player');
                const dealerCard = dealCardFromDimension(d, 'dealer');

                if (playerCard) playerHand.push(playerCard);
                if (dealerCard) dealerHand.push(dealerCard);

                // Update display for first dimension
                if (d === 0) {
                    displayCard(playerCard, 'playerCards');
                    displayCard(dealerCard, 'dealerCards');
                }
            }, (d * 5 + c) * 200);
        }

        pokerMultiverseGame.playerHands[d] = playerHand;
        pokerMultiverseGame.dealerHands[d] = dealerHand;
    }

    // Enable player actions after all cards are dealt
    setTimeout(() => {
        enablePlayerActions();
    }, pokerMultiverseGame.dimensionCount * 5 * 200 + 1000);
}

// Deal card from specific dimension
function dealCardFromDimension(dimensionIndex, recipient) {
    const dimensionConfig = DIMENSION_CONFIGS[pokerMultiverseGame.dimensionCount];
    const deck = pokerMultiverseGame.decks[dimensionIndex];

    if (!deck || deck.length === 0) {
        // Reinitialize deck if empty
        pokerMultiverseGame.decks[dimensionIndex] = createDimensionalDeck(dimensionIndex);
        return dealCardFromDimension(dimensionIndex, recipient);
    }

    let card;

    // Apply dimensional bias to card selection
    if (Math.random() < dimensionConfig.cardManipulation) {
        card = selectDimensionalBiasedCard(deck, recipient, dimensionIndex);
    } else {
        card = deck.pop();
    }

    if (!card) return null;

    // Apply dimensional effects to card
    applyDimensionalEffects(card, dimensionIndex);

    return card;
}

// Select dimensionally-biased card
function selectDimensionalBiasedCard(deck, recipient, dimensionIndex) {
    const dimensionConfig = DIMENSION_CONFIGS[pokerMultiverseGame.dimensionCount];
    let preferredCards;

    if (recipient === 'player') {
        // Dimensional bias gives player lower-value cards
        preferredCards = deck.filter(card =>
            card.value <= 8 || (card.value <= 10 && Math.random() < dimensionConfig.handDistortion)
        );
    } else {
        // Dimensional bias gives dealer higher-value cards
        preferredCards = deck.filter(card =>
            card.value >= 10 || (card.value >= 8 && Math.random() < dimensionConfig.handDistortion)
        );
    }

    if (preferredCards.length === 0) {
        return deck.pop();
    }

    const selectedCard = preferredCards[Math.floor(Math.random() * preferredCards.length)];

    // Remove from deck
    const index = deck.findIndex(card => card.id === selectedCard.id);
    if (index !== -1) {
        deck.splice(index, 1);
    }

    return selectedCard;
}

// Apply dimensional effects to card
function applyDimensionalEffects(card, dimensionIndex) {
    const modeData = MULTIVERSE_MODES[pokerMultiverseGame.multiverseMode];

    // Dimensional rifts can alter card properties
    pokerMultiverseGame.multiverse.dimensionalRifts.forEach(rift => {
        if (rift.fromDimension === dimensionIndex && Math.random() < rift.intensity) {
            if (rift.effect === 'card_swap') {
                // Card might be swapped with one from another dimension
                card.dimension = rift.toDimension;
            } else if (rift.effect === 'hand_distortion') {
                // Card value might be altered
                card.value = Math.max(2, Math.min(14, card.value + (Math.random() < 0.5 ? -1 : 1)));
            }
        }
    });

    // Quantum entanglement effects
    if (pokerMultiverseGame.multiverse.quantumEntanglement && card.entangled) {
        card.quantumState = 'entangled';
        // Entangled cards may have synchronized values across dimensions
        if (Math.random() < 0.4) {
            card.value = Math.max(2, card.value - 1); // Slight penalty for entangled cards
        }
    }

    // Paradox field effects
    if (pokerMultiverseGame.multiverse.paradoxLevel > 0) {
        const paradoxChance = pokerMultiverseGame.multiverse.paradoxLevel * 0.1;
        if (Math.random() < paradoxChance) {
            // Paradox can completely change the card
            card.rank = RANKS[Math.floor(Math.random() * RANKS.length)];
            card.value = CARD_VALUES[card.rank];
        }
    }
}

// Display card with dimensional effects
function displayCard(card, containerId) {
    if (!card) return;

    const container = document.getElementById(containerId);
    const cardElement = document.createElement('div');

    cardElement.className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold shadow-lg transform transition-all duration-500';

    // Add dimensional effects
    if (card.entangled) {
        cardElement.classList.add('ring-2', 'ring-green-400', 'animate-pulse');
    }

    if (card.dimension !== undefined && card.dimension > 0) {
        cardElement.classList.add('ring-2', 'ring-cyan-400');
        cardElement.style.boxShadow = '0 0 10px #00ffff';
    }

    const isRed = card.suit === '♥' || card.suit === '♦';
    cardElement.style.color = isRed ? '#dc2626' : '#000';

    cardElement.innerHTML = `
        <div class="text-xs">${card.rank}</div>
        <div class="text-lg">${card.suit}</div>
        <div class="text-xs transform rotate-180">${card.rank}</div>
    `;

    // Dimensional animation
    cardElement.style.transform = 'translateY(-20px) rotate(5deg) scale(1.1)';
    setTimeout(() => {
        cardElement.style.transform = 'translateY(0) rotate(0deg) scale(1)';
    }, 300);

    container.appendChild(cardElement);
}

// Enable player actions
function enablePlayerActions() {
    document.getElementById('playerActions').classList.remove('hidden');
    document.getElementById('playBet').disabled = false;
    document.getElementById('gameStatus').textContent = 'Choose your action across all dimensions';
    document.getElementById('gameMessage').textContent = 'Your decision will affect all parallel realities simultaneously';

    // Calculate and display hand rankings for first dimension
    updateHandRankings();
}

// Player action handler
function playerAction(action) {
    const playBet = parseInt(document.getElementById('playBet').value) || 0;

    if (action === 'fold') {
        // Fold across all dimensions - lose ante bet
        resolveHandsAcrossDimensions('fold');
    } else if (action === 'call') {
        // Call - match ante bet as play bet
        if (pokerMultiverseGame.anteBet > balance) {
            alert('Insufficient balance for call!');
            return;
        }
        balance -= pokerMultiverseGame.anteBet;
        pokerMultiverseGame.playBet = pokerMultiverseGame.anteBet;
        pokerMultiverseGame.totalBet += pokerMultiverseGame.playBet;
        updateBalance();
        resolveHandsAcrossDimensions('call');
    } else if (action === 'raise') {
        // Raise - custom play bet
        if (playBet <= 0 || playBet > balance) {
            alert('Please enter a valid raise amount!');
            return;
        }
        balance -= playBet;
        pokerMultiverseGame.playBet = playBet;
        pokerMultiverseGame.totalBet += playBet;
        updateBalance();
        resolveHandsAcrossDimensions('raise');
    }

    document.getElementById('playerActions').classList.add('hidden');
    updateBetDisplay();
}

// Switch to different dimension view
function switchToDimension(dimensionIndex) {
    // Update tab appearance
    const tabs = document.querySelectorAll('#dimensionTabs > div');
    tabs.forEach((tab, index) => {
        if (index === dimensionIndex) {
            tab.className = 'px-3 py-1 rounded text-xs font-bold cursor-pointer transition-all bg-cyan-500 text-black';
        } else {
            tab.className = 'px-3 py-1 rounded text-xs font-bold cursor-pointer transition-all bg-black/50 text-cyan-400 hover:bg-cyan-900/50';
        }
    });

    // Clear current display
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';

    // Display cards from selected dimension
    if (pokerMultiverseGame.playerHands[dimensionIndex]) {
        pokerMultiverseGame.playerHands[dimensionIndex].forEach(card => {
            displayCard(card, 'playerCards');
        });
    }

    if (pokerMultiverseGame.dealerHands[dimensionIndex]) {
        pokerMultiverseGame.dealerHands[dimensionIndex].forEach(card => {
            displayCard(card, 'dealerCards');
        });
    }

    // Update hand rankings for this dimension
    updateHandRankingsForDimension(dimensionIndex);
}

// Update hand rankings
function updateHandRankings() {
    if (pokerMultiverseGame.playerHands[0] && pokerMultiverseGame.playerHands[0].length === 5) {
        const playerRank = evaluatePokerHand(pokerMultiverseGame.playerHands[0]);
        document.getElementById('playerHandRank').textContent = playerRank.name;
    }

    if (pokerMultiverseGame.dealerHands[0] && pokerMultiverseGame.dealerHands[0].length === 5) {
        const dealerRank = evaluatePokerHand(pokerMultiverseGame.dealerHands[0]);
        document.getElementById('dealerHandRank').textContent = dealerRank.name;
    }
}

// Update hand rankings for specific dimension
function updateHandRankingsForDimension(dimensionIndex) {
    if (pokerMultiverseGame.playerHands[dimensionIndex] && pokerMultiverseGame.playerHands[dimensionIndex].length === 5) {
        const playerRank = evaluatePokerHand(pokerMultiverseGame.playerHands[dimensionIndex]);
        document.getElementById('playerHandRank').textContent = `${playerRank.name} (D-${dimensionIndex + 1})`;
    }

    if (pokerMultiverseGame.dealerHands[dimensionIndex] && pokerMultiverseGame.dealerHands[dimensionIndex].length === 5) {
        const dealerRank = evaluatePokerHand(pokerMultiverseGame.dealerHands[dimensionIndex]);
        document.getElementById('dealerHandRank').textContent = `${dealerRank.name} (D-${dimensionIndex + 1})`;
    }
}

// Evaluate poker hand
function evaluatePokerHand(cards) {
    if (!cards || cards.length !== 5) {
        return { name: 'Invalid Hand', rank: 0, value: 0 };
    }

    // Sort cards by value
    const sortedCards = [...cards].sort((a, b) => a.value - b.value);
    const values = sortedCards.map(card => card.value);
    const suits = sortedCards.map(card => card.suit);

    // Count occurrences of each value
    const valueCounts = {};
    values.forEach(value => {
        valueCounts[value] = (valueCounts[value] || 0) + 1;
    });

    const counts = Object.values(valueCounts).sort((a, b) => b - a);
    const isFlush = suits.every(suit => suit === suits[0]);
    const isStraight = values.every((value, index) => index === 0 || value === values[index - 1] + 1) ||
                      (values[0] === 2 && values[1] === 3 && values[2] === 4 && values[3] === 5 && values[4] === 14); // A-2-3-4-5

    // Check for royal flush
    if (isFlush && isStraight && values[0] === 10) {
        return { name: 'Royal Flush', rank: 10, value: 10000 };
    }

    // Check for straight flush
    if (isFlush && isStraight) {
        return { name: 'Straight Flush', rank: 9, value: 9000 + values[4] };
    }

    // Check for four of a kind
    if (counts[0] === 4) {
        return { name: 'Four of a Kind', rank: 8, value: 8000 + values[2] };
    }

    // Check for full house
    if (counts[0] === 3 && counts[1] === 2) {
        return { name: 'Full House', rank: 7, value: 7000 + values[2] };
    }

    // Check for flush
    if (isFlush) {
        return { name: 'Flush', rank: 6, value: 6000 + values[4] };
    }

    // Check for straight
    if (isStraight) {
        return { name: 'Straight', rank: 5, value: 5000 + values[4] };
    }

    // Check for three of a kind
    if (counts[0] === 3) {
        return { name: 'Three of a Kind', rank: 4, value: 4000 + values[2] };
    }

    // Check for two pair
    if (counts[0] === 2 && counts[1] === 2) {
        return { name: 'Two Pair', rank: 3, value: 3000 + Math.max(...values) };
    }

    // Check for one pair
    if (counts[0] === 2) {
        return { name: 'One Pair', rank: 2, value: 2000 + values[2] };
    }

    // High card
    return { name: 'High Card', rank: 1, value: 1000 + values[4] };
}

// Resolve hands across all dimensions with extreme bias
function resolveHandsAcrossDimensions(playerAction) {
    const modeData = MULTIVERSE_MODES[pokerMultiverseGame.multiverseMode];
    const dimensionConfig = DIMENSION_CONFIGS[pokerMultiverseGame.dimensionCount];

    let totalWinnings = 0;
    let dimensionsWon = 0;
    let resultMessage = '';

    if (playerAction === 'fold') {
        // Player folded - lose all bets
        resultMessage = 'Folded across all dimensions';
        pokerMultiverseGame.gameResult = 'fold';
    } else {
        // Evaluate hands across all dimensions
        for (let d = 0; d < pokerMultiverseGame.dimensionCount; d++) {
            const playerHand = pokerMultiverseGame.playerHands[d];
            const dealerHand = pokerMultiverseGame.dealerHands[d];

            if (!playerHand || !dealerHand || playerHand.length !== 5 || dealerHand.length !== 5) {
                continue;
            }

            let playerRank = evaluatePokerHand(playerHand);
            let dealerRank = evaluatePokerHand(dealerHand);

            // Apply dimensional distortion to player hand (penalty)
            if (Math.random() < dimensionConfig.handDistortion) {
                playerRank.value = Math.floor(playerRank.value * (1 - dimensionConfig.stabilityPenalty));
            }

            // Apply dimensional enhancement to dealer hand (bonus)
            if (Math.random() < dimensionConfig.handDistortion) {
                dealerRank.value = Math.floor(dealerRank.value * (1 + dimensionConfig.stabilityPenalty));
            }

            // Apply multiverse collapse penalty
            if (pokerMultiverseGame.multiverse.paradoxLevel > 0) {
                const paradoxPenalty = pokerMultiverseGame.multiverse.paradoxLevel * 0.1;
                if (Math.random() < paradoxPenalty && playerRank.value > dealerRank.value) {
                    // Paradox reverses player win
                    [playerRank.value, dealerRank.value] = [dealerRank.value, playerRank.value];
                    pokerMultiverseGame.stats.paradoxesResolved++;
                }
            }

            // Determine winner for this dimension
            if (playerRank.value > dealerRank.value) {
                dimensionsWon++;
            }
        }

        // Calculate winnings based on dimensions won
        const winPercentage = dimensionsWon / pokerMultiverseGame.dimensionCount;

        if (winPercentage > 0.5) {
            // Player wins majority of dimensions
            let anteWin = Math.floor(pokerMultiverseGame.anteBet * MULTIVERSE_PAYOUTS.ANTE_WIN);
            let playWin = Math.floor(pokerMultiverseGame.playBet * MULTIVERSE_PAYOUTS.PLAY_WIN);

            // Apply dimensional convergence bonus (fake - almost never applies)
            if (pokerMultiverseGame.multiverse.quantumEntanglement && Math.random() < 0.02) {
                const convergenceBonus = Math.floor((anteWin + playWin) * MULTIVERSE_PAYOUTS.DIMENSIONAL_CONVERGENCE);
                totalWinnings += convergenceBonus;
            }

            totalWinnings += anteWin + playWin;
            resultMessage = `Won ${dimensionsWon}/${pokerMultiverseGame.dimensionCount} dimensions`;
        } else if (winPercentage === 0.5) {
            // Tie - return bets
            totalWinnings += pokerMultiverseGame.anteBet + pokerMultiverseGame.playBet;
            resultMessage = `Tie across dimensions`;
        } else {
            // Player loses majority - no winnings
            resultMessage = `Lost ${pokerMultiverseGame.dimensionCount - dimensionsWon}/${pokerMultiverseGame.dimensionCount} dimensions`;
        }

        pokerMultiverseGame.gameResult = winPercentage > 0.5 ? 'win' : winPercentage === 0.5 ? 'tie' : 'lose';
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier);

    // Apply reality stability penalty
    const stabilityPenalty = 1 - pokerMultiverseGame.multiverse.realityStability;
    totalWinnings = Math.floor(totalWinnings * (1 - stabilityPenalty * 0.5));

    // Check for multiverse collapse (voids all wins)
    if (Math.random() < modeData.paradoxChance * 0.3) {
        totalWinnings = 0;
        resultMessage += ' - MULTIVERSE COLLAPSED!';
        pokerMultiverseGame.stats.multiverseCollapses++;
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    pokerMultiverseGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > pokerMultiverseGame.totalBet, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Reality stability: ${Math.floor(pokerMultiverseGame.multiverse.realityStability * 100)}%`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update multiverse display
function updateMultiverseDisplay() {
    const stability = Math.floor(pokerMultiverseGame.multiverse.realityStability * 100);

    if (pokerMultiverseGame.multiverse.active) {
        document.getElementById('multiverseStatus').innerHTML =
            `<div class="text-cyan-400 font-bold animate-pulse">MULTIVERSE: ACTIVE (${stability}%)</div>`;
        document.getElementById('realityStability').textContent = `${stability}%`;
    } else {
        document.getElementById('multiverseStatus').innerHTML =
            '<div class="text-cyan-400 font-bold">MULTIVERSE: STABLE</div>';
        document.getElementById('realityStability').textContent = '100%';
    }
}

// Update dimensional effects visualization
function updateDimensionalEffects() {
    // Update dimensional field visualization based on current state
    const dimensionalGrid = document.querySelector('#dimensionalRifts rect');
    if (dimensionalGrid) {
        if (pokerMultiverseGame.multiverse.active) {
            dimensionalGrid.style.opacity = '0.6';
        } else {
            dimensionalGrid.style.opacity = '0.3';
        }
    }
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('totalBetDisplay').textContent = `$${pokerMultiverseGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = pokerMultiverseGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${pokerMultiverseGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${pokerMultiverseGame.stats.totalWagered}`;
    document.getElementById('dimensionalShifts').textContent = pokerMultiverseGame.stats.dimensionalShifts;
    document.getElementById('multiverseCollapses').textContent = pokerMultiverseGame.stats.multiverseCollapses;

    const netResult = pokerMultiverseGame.stats.totalWon - pokerMultiverseGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    pokerMultiverseGame.stats.handsPlayed++;
    pokerMultiverseGame.stats.totalWagered += pokerMultiverseGame.totalBet;
    pokerMultiverseGame.stats.totalWon += winnings;

    if (won) {
        pokerMultiverseGame.stats.handsWon++;
        pokerMultiverseGame.stats.currentStreak++;
        pokerMultiverseGame.streakData.currentWinStreak++;
        pokerMultiverseGame.streakData.currentLossStreak = 0;

        if (pokerMultiverseGame.streakData.currentWinStreak > pokerMultiverseGame.streakData.longestWinStreak) {
            pokerMultiverseGame.streakData.longestWinStreak = pokerMultiverseGame.streakData.currentWinStreak;
        }

        if (winnings > pokerMultiverseGame.stats.biggestWin) {
            pokerMultiverseGame.stats.biggestWin = winnings;
        }
    } else {
        pokerMultiverseGame.stats.currentStreak = 0;
        pokerMultiverseGame.streakData.currentWinStreak = 0;
        pokerMultiverseGame.streakData.currentLossStreak++;

        if (pokerMultiverseGame.streakData.currentLossStreak > pokerMultiverseGame.streakData.longestLossStreak) {
            pokerMultiverseGame.streakData.longestLossStreak = pokerMultiverseGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to dimensional effects)
    pokerMultiverseGame.stats.winRate = (pokerMultiverseGame.stats.handsWon / pokerMultiverseGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    pokerMultiverseGame.isPlaying = false;
    pokerMultiverseGame.anteBet = 0;
    pokerMultiverseGame.playBet = 0;
    pokerMultiverseGame.totalBet = 0;
    pokerMultiverseGame.playerCards = [];
    pokerMultiverseGame.playerHands = [];
    pokerMultiverseGame.dealerCards = [];
    pokerMultiverseGame.dealerHands = [];
    pokerMultiverseGame.gameResult = '';
    pokerMultiverseGame.totalWin = 0;

    // Reset multiverse system
    pokerMultiverseGame.multiverse.active = false;
    pokerMultiverseGame.multiverse.dimensions = [];
    pokerMultiverseGame.multiverse.parallelOutcomes = [];
    pokerMultiverseGame.multiverse.dimensionalRifts = [];
    pokerMultiverseGame.multiverse.quantumEntanglement = false;
    pokerMultiverseGame.multiverse.realityStability = 1.0;
    pokerMultiverseGame.multiverse.dimensionalFlux = 0.0;
    pokerMultiverseGame.multiverse.convergencePoint = null;
    pokerMultiverseGame.multiverse.paradoxLevel = 0;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    document.getElementById('playerHandRank').textContent = '-';
    document.getElementById('dealerHandRank').textContent = '-';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('dimensionalEvent').classList.add('hidden');
    document.getElementById('convergenceEffects').innerHTML = '';
    document.getElementById('playerActions').classList.add('hidden');
    document.getElementById('riftPortals').innerHTML = '';

    // Reset multiverse status
    document.getElementById('multiverseStatus').innerHTML =
        '<div class="text-cyan-400 font-bold">MULTIVERSE: STABLE</div>';
    document.getElementById('dimensionalRifts').innerHTML =
        '<div class="text-purple-400 font-bold">RIFTS: CLOSED</div>';
    document.getElementById('quantumEntanglement').innerHTML =
        '<div class="text-green-400 font-bold">ENTANGLEMENT: INACTIVE</div>';
    document.getElementById('paradoxLevel').innerHTML =
        '<div class="text-red-400 font-bold">PARADOX LEVEL: 0</div>';

    // Reset bet inputs
    document.getElementById('anteBet').value = 10;
    document.getElementById('playBet').value = 0;
    document.getElementById('playBet').disabled = true;

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Multiverse stabilizing...';
    document.getElementById('gameMessage').textContent = 'Welcome to Poker Multiverse - Where Every Hand Exists in Infinite Realities';

    // Reset dimension tabs to first dimension
    const tabs = document.querySelectorAll('#dimensionTabs > div');
    tabs.forEach((tab, index) => {
        if (index === 0) {
            tab.className = 'px-3 py-1 rounded text-xs font-bold cursor-pointer transition-all bg-cyan-500 text-black';
        } else {
            tab.className = 'px-3 py-1 rounded text-xs font-bold cursor-pointer transition-all bg-black/50 text-cyan-400 hover:bg-cyan-900/50';
        }
    });

    // Reinitialize systems for next hand
    initializeMultiverseSystem();
    initializeDecksAcrossDimensions();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPokerMultiverseGame();
});