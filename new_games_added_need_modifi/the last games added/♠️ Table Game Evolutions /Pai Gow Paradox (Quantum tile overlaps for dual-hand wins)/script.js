// Pai <PERSON>w Paradox - Quantum Tile Overlaps for Dual-Hand Wins
// Ultra High House Edge Implementation with Quantum Mechanics
// Designed to maintain <3% player win rate

// Game state
let balance = 1000;

// Game state object with quantum paradox system
let paiGowParadoxGame = {
    isPlaying: false,
    quantumMode: 'standard', // standard, entangled, superposition, paradox
    paradoxLevel: 'normal', // normal, complex, extreme, impossible, transcendent
    betAmount: 0,
    bonusBet: 0,
    fortuneBet: 0,
    totalBet: 0,

    // Player tiles and hands
    playerTiles: [],
    playerHighHand: [],
    playerLowHand: [],

    // Dealer tiles and hands
    dealerTiles: [],
    dealerHighHand: [],
    dealerLowHand: [],

    // Quantum system
    quantum: {
        active: false,
        entanglement: false,
        superposition: false,
        paradoxField: 0,
        tileOverlaps: [],
        dualRealities: [],
        quantumStates: [],
        coherence: 1.0,
        decoherence: 0.0,
        waveFunction: 'collapsed'
    },

    // Tile set (32 tiles in Pai Gow)
    tiles: [],
    gameResult: '',
    totalWin: 0,

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        quantumEvents: 0,
        paradoxesResolved: 0,
        dualWins: 0,
        pushes: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Quantum modes with extreme paradox bias
const QUANTUM_MODES = {
    standard: {
        name: 'Standard Quantum',
        houseEdge: 0.68, // 68% house edge
        paradoxChance: 0.20, // 20% paradox chance
        payoutMultiplier: 0.35, // Severely reduced payouts
        entanglementRate: 0.15, // 15% entanglement rate
        coherenceDecay: 0.10 // 10% coherence decay
    },
    entangled: {
        name: 'Quantum Entanglement',
        houseEdge: 0.74, // 74% house edge
        paradoxChance: 0.30, // 30% paradox chance
        payoutMultiplier: 0.30, // Even more reduced payouts
        entanglementRate: 0.25, // 25% entanglement rate
        coherenceDecay: 0.15 // 15% coherence decay
    },
    superposition: {
        name: 'Quantum Superposition',
        houseEdge: 0.80, // 80% house edge
        paradoxChance: 0.40, // 40% paradox chance
        payoutMultiplier: 0.25, // Extremely reduced payouts
        entanglementRate: 0.35, // 35% entanglement rate
        coherenceDecay: 0.22 // 22% coherence decay
    },
    paradox: {
        name: 'Quantum Paradox',
        houseEdge: 0.87, // 87% house edge
        paradoxChance: 0.55, // 55% paradox chance
        payoutMultiplier: 0.18, // Brutally reduced payouts
        entanglementRate: 0.50, // 50% entanglement rate
        coherenceDecay: 0.30 // 30% coherence decay
    }
};

const PARADOX_LEVELS = {
    normal: {
        name: 'Normal Paradox',
        tileBias: 0.30, // 30% tile bias
        handManipulation: 0.25, // 25% hand manipulation
        dualRealityChance: 0.15, // 15% dual reality chance
        quantumInterference: 0.20 // 20% quantum interference
    },
    complex: {
        name: 'Complex Paradox',
        tileBias: 0.42, // 42% tile bias
        handManipulation: 0.35, // 35% hand manipulation
        dualRealityChance: 0.25, // 25% dual reality chance
        quantumInterference: 0.30 // 30% quantum interference
    },
    extreme: {
        name: 'Extreme Paradox',
        tileBias: 0.55, // 55% tile bias
        handManipulation: 0.48, // 48% hand manipulation
        dualRealityChance: 0.35, // 35% dual reality chance
        quantumInterference: 0.42 // 42% quantum interference
    },
    impossible: {
        name: 'Impossible Paradox',
        tileBias: 0.68, // 68% tile bias
        handManipulation: 0.62, // 62% hand manipulation
        dualRealityChance: 0.48, // 48% dual reality chance
        quantumInterference: 0.55 // 55% quantum interference
    },
    transcendent: {
        name: 'Transcendent Paradox',
        tileBias: 0.82, // 82% tile bias
        handManipulation: 0.75, // 75% hand manipulation
        dualRealityChance: 0.65, // 65% dual reality chance
        quantumInterference: 0.70 // 70% quantum interference
    }
};

// Severely reduced payout table with quantum effects
const PARADOX_PAYOUTS = {
    // Standard wins (heavily reduced)
    BOTH_HANDS_WIN: 0.60, // Reduced from 1:1
    ONE_HAND_WIN_PUSH: 0.0, // Push (no payout)

    // Bonus bet payouts (terrible odds)
    STRAIGHT_FLUSH: 15, // Reduced from 50:1
    FOUR_OF_A_KIND: 10, // Reduced from 25:1
    FULL_HOUSE: 4, // Reduced from 5:1
    FLUSH: 2.5, // Reduced from 4:1
    STRAIGHT: 1.5, // Reduced from 3:1
    THREE_OF_A_KIND: 1, // Reduced from 2:1

    // Fortune bet (almost never pays)
    SEVEN_PAIR_FORTUNE: 200, // Extremely rare
    ROYAL_FLUSH_FORTUNE: 150, // Very rare
    STRAIGHT_FLUSH_FORTUNE: 50, // Rare
    FOUR_OF_A_KIND_FORTUNE: 25, // Uncommon

    // Quantum paradox bonuses (fake - almost never apply)
    DUAL_REALITY_BONUS: 0.1, // 10% of displayed bonus
    QUANTUM_ENTANGLEMENT: 0.05, // 5% of displayed bonus
    PARADOX_RESOLUTION: 0.02 // 2% of displayed bonus
};

// Pai Gow tile definitions (32 tiles total)
const PAI_GOW_TILES = [
    // Supreme pair (Gee Joon)
    { id: 1, name: 'Gee Joon 1', dots: [1, 2], rank: 1, value: 3, isSupreme: true },
    { id: 2, name: 'Gee Joon 2', dots: [2, 4], rank: 1, value: 6, isSupreme: true },

    // Teen and Day (highest ranking pair)
    { id: 3, name: 'Teen', dots: [6, 6], rank: 2, value: 12 },
    { id: 4, name: 'Day', dots: [1, 1], rank: 2, value: 2 },

    // Yun and Gor (second highest pair)
    { id: 5, name: 'Yun', dots: [4, 4], rank: 3, value: 8 },
    { id: 6, name: 'Gor', dots: [1, 3], rank: 3, value: 4 },

    // Mooy and Chong (third highest pair)
    { id: 7, name: 'Mooy', dots: [5, 5], rank: 4, value: 10 },
    { id: 8, name: 'Chong', dots: [3, 3], rank: 4, value: 6 },

    // Bon and Foo (fourth highest pair)
    { id: 9, name: 'Bon', dots: [2, 2], rank: 5, value: 4 },
    { id: 10, name: 'Foo', dots: [5, 6], rank: 5, value: 11 },

    // Ping and Tit (fifth highest pair)
    { id: 11, name: 'Ping', dots: [4, 6], rank: 6, value: 10 },
    { id: 12, name: 'Tit', dots: [1, 6], rank: 6, value: 7 },

    // Long and Look (sixth highest pair)
    { id: 13, name: 'Long', dots: [1, 5], rank: 7, value: 6 },
    { id: 14, name: 'Look', dots: [4, 5], rank: 7, value: 9 },

    // Chop and Chat (seventh highest pair)
    { id: 15, name: 'Chop', dots: [2, 6], rank: 8, value: 8 },
    { id: 16, name: 'Chat', dots: [3, 6], rank: 8, value: 9 },

    // Individual tiles (no pairs)
    { id: 17, name: 'Ngor', dots: [2, 5], rank: 9, value: 7 },
    { id: 18, name: 'Wun', dots: [3, 4], rank: 10, value: 7 },
    { id: 19, name: 'Yat', dots: [2, 3], rank: 11, value: 5 },
    { id: 20, name: 'Ban', dots: [2, 4], rank: 12, value: 6 },
    { id: 21, name: 'Mun', dots: [1, 4], rank: 13, value: 5 },
    { id: 22, name: 'Bong', dots: [1, 2], rank: 14, value: 3 },
    { id: 23, name: 'Sup', dots: [3, 5], rank: 15, value: 8 },
    { id: 24, name: 'Chit', dots: [1, 3], rank: 16, value: 4 },
    { id: 25, name: 'Ng', dots: [2, 3], rank: 17, value: 5 },
    { id: 26, name: 'Luk', dots: [1, 2], rank: 18, value: 3 },
    { id: 27, name: 'Chat', dots: [1, 1], rank: 19, value: 2 },
    { id: 28, name: 'Baat', dots: [4, 4], rank: 20, value: 8 },
    { id: 29, name: 'Gau', dots: [3, 6], rank: 21, value: 9 },
    { id: 30, name: 'Sahp', dots: [5, 5], rank: 22, value: 10 },
    { id: 31, name: 'Sahp Yat', dots: [5, 6], rank: 23, value: 11 },
    { id: 32, name: 'Sahp Yee', dots: [6, 6], rank: 24, value: 12 }
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadPaiGowParadoxGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">QUANTUM PARADOX CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">QUANTUM MODE</label>
                        <select id="quantumMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Quantum</option>
                            <option value="entangled">Quantum Entanglement</option>
                            <option value="superposition">Quantum Superposition</option>
                            <option value="paradox">Quantum Paradox</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PARADOX LEVEL</label>
                        <select id="paradoxLevel" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Normal Paradox</option>
                            <option value="complex" selected>Complex Paradox</option>
                            <option value="extreme">Extreme Paradox</option>
                            <option value="impossible">Impossible Paradox</option>
                            <option value="transcendent">Transcendent Paradox</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-lg font-bold mb-3 text-purple-400">BETTING AREA</h5>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Main Bet:</label>
                                <input type="number" id="betAmount" value="10" min="5" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Bonus Bet:</label>
                                <input type="number" id="bonusBet" value="0" min="0" max="${Math.min(balance, 200)}"
                                       class="w-24 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Fortune Bet:</label>
                                <input type="number" id="fortuneBet" value="0" min="0" max="${Math.min(balance, 100)}"
                                       class="w-24 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <button id="dealTiles" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ACTIVATE QUANTUM FIELD
                    </button>

                    <div id="handArrangement" class="hidden">
                        <h5 class="text-lg font-bold mb-3 text-purple-400">ARRANGE HANDS</h5>
                        <div class="space-y-2">
                            <button id="autoArrange" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                                AUTO ARRANGE
                            </button>
                            <button id="confirmHands" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                CONFIRM HANDS
                            </button>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Quantum Coherence</div>
                        <div id="quantumCoherence" class="text-lg font-bold text-purple-400">100%</div>
                    </div>
                </div>

                <!-- Quantum Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">QUANTUM STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="quantumFieldStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">FIELD: STABLE</div>
                        </div>
                        <div id="entanglementStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">ENTANGLEMENT: INACTIVE</div>
                        </div>
                        <div id="superpositionStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">SUPERPOSITION: COLLAPSED</div>
                        </div>
                        <div id="paradoxStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">PARADOX: RESOLVED</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">PARADOX PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Standard Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Both Hands Win:</span>
                            <span class="text-red-400">0.60:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">One Hand Win:</span>
                            <span class="text-red-400">Push</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Bonus Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">15:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Four of a Kind:</span>
                            <span class="text-red-400">10:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Full House:</span>
                            <span class="text-red-400">4:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Quantum effects may alter outcomes</div>
                        <div class="text-xs text-red-400">*Paradoxes rarely resolve favorably</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="paiGowTable" class="relative bg-gradient-to-br from-black via-purple-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Quantum Field Visual Effect -->
                        <div id="quantumFieldEffect" class="absolute inset-0 pointer-events-none opacity-20">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="paradoxGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#8000ff;stop-opacity:0.4" />
                                        <stop offset="50%" style="stop-color:#4000ff;stop-opacity:0.2" />
                                        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
                                    </radialGradient>
                                    <pattern id="paradoxGrid" width="25" height="25" patternUnits="userSpaceOnUse">
                                        <path d="M 25 0 L 0 0 0 25" fill="none" stroke="#8000ff" stroke-width="0.5" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#paradoxGrid)" />
                                <circle id="paradoxCore" cx="50%" cy="50%" r="30" fill="url(#paradoxGradient)" class="animate-pulse" />
                            </svg>
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-full">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">PLAYER TILES</div>
                                <div id="playerTiles" class="flex space-x-2 justify-center mb-4 min-h-16">
                                    <!-- Player tiles will appear here -->
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center">
                                        <div class="text-xs text-blue-400 mb-1">HIGH HAND</div>
                                        <div id="playerHighHand" class="flex space-x-1 justify-center min-h-12 bg-blue-900/20 rounded p-2">
                                            <!-- Player high hand -->
                                        </div>
                                        <div id="playerHighValue" class="text-sm text-blue-400 mt-1">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xs text-blue-400 mb-1">LOW HAND</div>
                                        <div id="playerLowHand" class="flex space-x-1 justify-center min-h-12 bg-blue-900/20 rounded p-2">
                                            <!-- Player low hand -->
                                        </div>
                                        <div id="playerLowValue" class="text-sm text-blue-400 mt-1">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dealer Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2 w-full">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">DEALER TILES</div>
                                <div id="dealerTiles" class="flex space-x-2 justify-center mb-4 min-h-16">
                                    <!-- Dealer tiles will appear here -->
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center">
                                        <div class="text-xs text-red-400 mb-1">HIGH HAND</div>
                                        <div id="dealerHighHand" class="flex space-x-1 justify-center min-h-12 bg-red-900/20 rounded p-2">
                                            <!-- Dealer high hand -->
                                        </div>
                                        <div id="dealerHighValue" class="text-sm text-red-400 mt-1">-</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-xs text-red-400 mb-1">LOW HAND</div>
                                        <div id="dealerLowHand" class="flex space-x-1 justify-center min-h-12 bg-red-900/20 rounded p-2">
                                            <!-- Dealer low hand -->
                                        </div>
                                        <div id="dealerLowValue" class="text-sm text-red-400 mt-1">-</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Quantum field stabilizing...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="quantumEvent" class="text-sm font-bold text-purple-400 hidden animate-pulse">QUANTUM PARADOX DETECTED!</div>
                        </div>

                        <!-- Quantum Overlaps Visualization -->
                        <div id="quantumOverlaps" class="absolute inset-0 pointer-events-none">
                            <!-- Quantum tile overlaps will be visualized here -->
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Pai Gow Paradox - Where Quantum Mechanics Meet Ancient Tiles</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Quantum Events</div>
                <div id="quantumEvents" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Dual Wins</div>
                <div id="dualWins" class="text-xl font-bold text-green-400">0</div>
            </div>
        </div>
    `;

    initializePaiGowParadox();
}

// Initialize the game
function initializePaiGowParadox() {
    document.getElementById('dealTiles').addEventListener('click', dealNewHand);
    document.getElementById('autoArrange').addEventListener('click', autoArrangeHands);
    document.getElementById('confirmHands').addEventListener('click', confirmHands);

    // Initialize quantum system and tiles
    initializeQuantumSystem();
    initializeTileSet();
    updateGameStats();
}

// Initialize quantum system
function initializeQuantumSystem() {
    paiGowParadoxGame.quantum.active = false;
    paiGowParadoxGame.quantum.entanglement = false;
    paiGowParadoxGame.quantum.superposition = false;
    paiGowParadoxGame.quantum.paradoxField = 0;
    paiGowParadoxGame.quantum.tileOverlaps = [];
    paiGowParadoxGame.quantum.dualRealities = [];
    paiGowParadoxGame.quantum.quantumStates = [];
    paiGowParadoxGame.quantum.coherence = 1.0;
    paiGowParadoxGame.quantum.decoherence = 0.0;
    paiGowParadoxGame.quantum.waveFunction = 'collapsed';

    updateQuantumDisplay();
}

// Initialize tile set with quantum properties
function initializeTileSet() {
    paiGowParadoxGame.tiles = [];

    // Create tiles with quantum properties
    PAI_GOW_TILES.forEach(tileTemplate => {
        const tile = {
            ...tileTemplate,
            quantumState: 'classical',
            entangled: false,
            superposed: false,
            paradoxPotential: Math.random(),
            quantumCharge: Math.random(),
            dualReality: false
        };
        paiGowParadoxGame.tiles.push(tile);
    });

    shuffleTilesWithQuantum();
}

// Shuffle tiles with quantum effects
function shuffleTilesWithQuantum() {
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];

    // Standard shuffle
    for (let i = paiGowParadoxGame.tiles.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [paiGowParadoxGame.tiles[i], paiGowParadoxGame.tiles[j]] = [paiGowParadoxGame.tiles[j], paiGowParadoxGame.tiles[i]];
    }

    // Quantum manipulation - tiles with higher quantum charge rise to top
    if (Math.random() < paradoxData.tileBias) {
        paiGowParadoxGame.tiles.sort((a, b) => {
            // Higher quantum charge tiles are more likely to be dealt
            return b.quantumCharge - a.quantumCharge;
        });
    }
}

// Deal new hand with quantum effects
function dealNewHand() {
    const betAmount = parseInt(document.getElementById('betAmount').value);
    const bonusBet = parseInt(document.getElementById('bonusBet').value) || 0;
    const fortuneBet = parseInt(document.getElementById('fortuneBet').value) || 0;

    const totalBets = betAmount + bonusBet + fortuneBet;

    if (totalBets > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bets from balance
    balance -= totalBets;
    updateBalance();

    // Set game state
    paiGowParadoxGame.isPlaying = true;
    paiGowParadoxGame.betAmount = betAmount;
    paiGowParadoxGame.bonusBet = bonusBet;
    paiGowParadoxGame.fortuneBet = fortuneBet;
    paiGowParadoxGame.totalBet = totalBets;
    paiGowParadoxGame.quantumMode = document.getElementById('quantumMode').value;
    paiGowParadoxGame.paradoxLevel = document.getElementById('paradoxLevel').value;

    // Clear previous tiles and hands
    paiGowParadoxGame.playerTiles = [];
    paiGowParadoxGame.playerHighHand = [];
    paiGowParadoxGame.playerLowHand = [];
    paiGowParadoxGame.dealerTiles = [];
    paiGowParadoxGame.dealerHighHand = [];
    paiGowParadoxGame.dealerLowHand = [];

    // Activate quantum system
    activateQuantumSystem();

    // Deal tiles with quantum effects
    setTimeout(() => {
        dealTilesWithQuantum();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('dealTiles').disabled = true;
    document.getElementById('gameStatus').textContent = 'Quantum field activating...';
}

// Activate quantum system
function activateQuantumSystem() {
    const modeData = QUANTUM_MODES[paiGowParadoxGame.quantumMode];
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];

    paiGowParadoxGame.quantum.active = true;
    paiGowParadoxGame.quantum.paradoxField = Math.random() * modeData.paradoxChance;
    paiGowParadoxGame.quantum.coherence = 1.0 - (Math.random() * modeData.coherenceDecay);

    // Activate quantum effects based on mode
    if (Math.random() < modeData.entanglementRate) {
        activateQuantumEntanglement();
    }

    if (Math.random() < paradoxData.dualRealityChance) {
        activateQuantumSuperposition();
    }

    if (Math.random() < modeData.paradoxChance) {
        activateQuantumParadox();
    }

    // Generate quantum overlaps
    generateQuantumOverlaps();

    // Update visual effects
    updateQuantumDisplay();
    updateQuantumEffects();
}

// Activate quantum entanglement
function activateQuantumEntanglement() {
    paiGowParadoxGame.quantum.entanglement = true;
    paiGowParadoxGame.stats.quantumEvents++;

    document.getElementById('entanglementStatus').innerHTML =
        '<div class="text-blue-400 font-bold animate-pulse">ENTANGLEMENT: ACTIVE</div>';

    // Entangle random tiles
    const entanglementCount = Math.floor(Math.random() * 4) + 2; // 2-5 entangled tiles
    for (let i = 0; i < entanglementCount; i++) {
        const randomTile = paiGowParadoxGame.tiles[Math.floor(Math.random() * paiGowParadoxGame.tiles.length)];
        randomTile.entangled = true;
        randomTile.quantumState = 'entangled';
    }
}

// Activate quantum superposition
function activateQuantumSuperposition() {
    paiGowParadoxGame.quantum.superposition = true;
    paiGowParadoxGame.stats.quantumEvents++;

    document.getElementById('superpositionStatus').innerHTML =
        '<div class="text-green-400 font-bold animate-pulse">SUPERPOSITION: ACTIVE</div>';

    // Create dual realities
    paiGowParadoxGame.quantum.dualRealities = generateDualRealities();
}

// Activate quantum paradox
function activateQuantumParadox() {
    paiGowParadoxGame.quantum.waveFunction = 'paradox';
    paiGowParadoxGame.stats.quantumEvents++;

    document.getElementById('paradoxStatus').innerHTML =
        '<div class="text-red-400 font-bold animate-pulse">PARADOX: ACTIVE</div>';

    // Show quantum event
    document.getElementById('quantumEvent').classList.remove('hidden');
    setTimeout(() => {
        document.getElementById('quantumEvent').classList.add('hidden');
    }, 3000);
}

// Generate quantum overlaps
function generateQuantumOverlaps() {
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];
    const overlapCount = Math.floor(Math.random() * 6) + 2; // 2-7 overlaps

    paiGowParadoxGame.quantum.tileOverlaps = [];

    for (let i = 0; i < overlapCount; i++) {
        const overlap = {
            x: Math.random() * 100,
            y: Math.random() * 100,
            intensity: Math.random() * paradoxData.quantumInterference,
            type: Math.random() < 0.5 ? 'constructive' : 'destructive',
            effect: Math.random() < 0.5 ? 'value_boost' : 'rank_shift'
        };
        paiGowParadoxGame.quantum.tileOverlaps.push(overlap);
    }

    // Visual representation of quantum overlaps
    displayQuantumOverlaps();
}

// Display quantum overlaps visually
function displayQuantumOverlaps() {
    const container = document.getElementById('quantumOverlaps');
    container.innerHTML = '';

    paiGowParadoxGame.quantum.tileOverlaps.forEach((overlap, index) => {
        const overlapElement = document.createElement('div');
        overlapElement.className = `absolute w-8 h-8 rounded-full border-2 ${overlap.type === 'constructive' ? 'border-green-400 bg-green-500/20' : 'border-red-400 bg-red-500/20'} animate-pulse`;
        overlapElement.style.left = `${overlap.x}%`;
        overlapElement.style.top = `${overlap.y}%`;
        overlapElement.style.transform = 'translate(-50%, -50%)';
        container.appendChild(overlapElement);
    });
}

// Generate dual realities for superposition
function generateDualRealities() {
    const realities = [];
    const realityCount = Math.floor(Math.random() * 3) + 2; // 2-4 parallel realities

    for (let i = 0; i < realityCount; i++) {
        const reality = {
            id: i,
            probability: Math.random(),
            tileArrangement: [],
            outcome: Math.random() < 0.3 ? 'favorable' : 'unfavorable' // 30% chance favorable
        };
        realities.push(reality);
    }

    return realities;
}

// Deal tiles with quantum effects
function dealTilesWithQuantum() {
    document.getElementById('gameStatus').textContent = 'Dealing quantum tiles...';

    // Deal 4 tiles to player and 4 to dealer
    for (let i = 0; i < 4; i++) {
        setTimeout(() => {
            dealTileWithQuantum('player');
        }, i * 500);

        setTimeout(() => {
            dealTileWithQuantum('dealer');
        }, (i + 4) * 500);
    }

    // Enable hand arrangement after all tiles are dealt
    setTimeout(() => {
        enableHandArrangement();
    }, 4500);
}

// Deal a single tile with quantum effects
function dealTileWithQuantum(recipient) {
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];
    let tile;

    // Apply quantum bias to tile selection
    if (Math.random() < paradoxData.tileBias) {
        tile = selectQuantumBiasedTile(recipient);
    } else {
        tile = paiGowParadoxGame.tiles.pop();
    }

    if (!tile) {
        // Fallback if tiles are empty
        initializeTileSet();
        tile = paiGowParadoxGame.tiles.pop();
    }

    // Apply quantum effects to tile
    if (paiGowParadoxGame.quantum.active) {
        applyQuantumEffectsToTile(tile);
    }

    if (recipient === 'player') {
        paiGowParadoxGame.playerTiles.push(tile);
        displayTile(tile, 'playerTiles');
    } else {
        paiGowParadoxGame.dealerTiles.push(tile);
        displayTile(tile, 'dealerTiles');
    }
}

// Select quantum-biased tile
function selectQuantumBiasedTile(recipient) {
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];
    let preferredTiles;

    if (recipient === 'player') {
        // Quantum field prefers to give player lower-ranking tiles
        preferredTiles = paiGowParadoxGame.tiles.filter(tile =>
            tile.rank > 15 || (tile.value <= 6 && Math.random() < paradoxData.tileBias)
        );
    } else {
        // Quantum field prefers to give dealer higher-ranking tiles
        preferredTiles = paiGowParadoxGame.tiles.filter(tile =>
            tile.rank <= 10 || (tile.value >= 8 && Math.random() < paradoxData.tileBias)
        );
    }

    if (preferredTiles.length === 0) {
        return paiGowParadoxGame.tiles.pop();
    }

    const selectedTile = preferredTiles[Math.floor(Math.random() * preferredTiles.length)];

    // Remove from tiles array
    const index = paiGowParadoxGame.tiles.findIndex(tile => tile.id === selectedTile.id);
    if (index !== -1) {
        paiGowParadoxGame.tiles.splice(index, 1);
    }

    return selectedTile;
}

// Apply quantum effects to tile
function applyQuantumEffectsToTile(tile) {
    const modeData = QUANTUM_MODES[paiGowParadoxGame.quantumMode];

    // Quantum entanglement effects
    if (paiGowParadoxGame.quantum.entanglement && tile.entangled) {
        tile.quantumState = 'entangled';
        // Entangled tiles may have altered values
        if (Math.random() < 0.3) {
            tile.value = (tile.value + Math.floor(Math.random() * 3) - 1) % 10;
            if (tile.value < 0) tile.value = 9;
        }
    }

    // Quantum superposition effects
    if (paiGowParadoxGame.quantum.superposition) {
        tile.superposed = true;
        tile.quantumState = 'superposed';
        // Superposed tiles exist in multiple states
        tile.dualReality = true;
    }

    // Quantum overlap effects
    paiGowParadoxGame.quantum.tileOverlaps.forEach(overlap => {
        if (Math.random() < overlap.intensity) {
            if (overlap.effect === 'value_boost' && overlap.type === 'constructive') {
                tile.value = Math.min(12, tile.value + 1);
            } else if (overlap.effect === 'value_boost' && overlap.type === 'destructive') {
                tile.value = Math.max(0, tile.value - 1);
            } else if (overlap.effect === 'rank_shift') {
                tile.rank = Math.max(1, Math.min(24, tile.rank + (Math.random() < 0.5 ? -1 : 1)));
            }
        }
    });
}

// Display tile with quantum effects
function displayTile(tile, containerId) {
    const container = document.getElementById(containerId);
    const tileElement = document.createElement('div');

    tileElement.className = 'w-12 h-16 bg-gradient-to-br from-white to-gray-200 rounded border-2 border-gray-400 flex flex-col items-center justify-center text-black text-xs font-bold shadow-lg transform transition-all duration-500 cursor-pointer';

    // Add quantum effects
    if (tile.entangled) {
        tileElement.classList.add('ring-2', 'ring-blue-400', 'animate-pulse');
    }

    if (tile.superposed) {
        tileElement.classList.add('ring-2', 'ring-green-400', 'animate-bounce');
    }

    if (tile.dualReality) {
        tileElement.classList.add('ring-2', 'ring-purple-400');
        tileElement.style.boxShadow = '0 0 10px #8000ff';
    }

    // Display tile dots
    const dotsHtml = tile.dots.map(dots => {
        return `<div class="flex justify-center">${'●'.repeat(dots)}</div>`;
    }).join('');

    tileElement.innerHTML = `
        <div class="text-xs mb-1">${tile.name}</div>
        <div class="text-xs">${dotsHtml}</div>
        <div class="text-xs mt-1">Val: ${tile.value}</div>
    `;

    // Quantum animation
    tileElement.style.transform = 'translateY(-20px) rotate(5deg) scale(1.1)';
    setTimeout(() => {
        tileElement.style.transform = 'translateY(0) rotate(0deg) scale(1)';
    }, 300);

    // Add click handler for manual arrangement
    tileElement.addEventListener('click', () => {
        if (containerId === 'playerTiles') {
            selectTileForArrangement(tile, tileElement);
        }
    });

    container.appendChild(tileElement);
}

// Enable hand arrangement
function enableHandArrangement() {
    document.getElementById('handArrangement').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Arrange your hands - High hand must be stronger than low hand';
    document.getElementById('gameMessage').textContent = 'Click tiles to arrange them into high and low hands, or use auto-arrange';
}

// Auto arrange hands (with quantum bias against player)
function autoArrangeHands() {
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];

    // Clear existing arrangements
    paiGowParadoxGame.playerHighHand = [];
    paiGowParadoxGame.playerLowHand = [];

    // Sort tiles by value (with quantum manipulation)
    const sortedTiles = [...paiGowParadoxGame.playerTiles].sort((a, b) => {
        let aValue = a.value;
        let bValue = b.value;

        // Apply quantum manipulation to sorting
        if (Math.random() < paradoxData.handManipulation) {
            // Quantum field biases arrangement against player
            aValue += (Math.random() - 0.7) * 2; // Bias toward lower values
            bValue += (Math.random() - 0.7) * 2;
        }

        return bValue - aValue;
    });

    // Arrange hands (quantum field ensures suboptimal arrangement)
    if (Math.random() < paradoxData.handManipulation) {
        // Quantum manipulation - deliberately poor arrangement
        paiGowParadoxGame.playerHighHand = [sortedTiles[2], sortedTiles[3]]; // Weaker tiles in high hand
        paiGowParadoxGame.playerLowHand = [sortedTiles[0], sortedTiles[1]]; // Stronger tiles in low hand
    } else {
        // Standard arrangement (still biased)
        paiGowParadoxGame.playerHighHand = [sortedTiles[0], sortedTiles[1]];
        paiGowParadoxGame.playerLowHand = [sortedTiles[2], sortedTiles[3]];
    }

    displayPlayerHands();
    updateHandValues();
}

// Confirm hands and proceed to dealer arrangement
function confirmHands() {
    if (paiGowParadoxGame.playerHighHand.length !== 2 || paiGowParadoxGame.playerLowHand.length !== 2) {
        alert('Please arrange all tiles into high and low hands!');
        return;
    }

    // Validate hand arrangement (high hand must be stronger)
    const highHandValue = calculateHandValue(paiGowParadoxGame.playerHighHand);
    const lowHandValue = calculateHandValue(paiGowParadoxGame.playerLowHand);

    if (highHandValue <= lowHandValue) {
        alert('High hand must be stronger than low hand!');
        return;
    }

    document.getElementById('handArrangement').classList.add('hidden');
    document.getElementById('gameStatus').textContent = 'Dealer arranging hands...';

    // Dealer arranges hands with quantum advantage
    setTimeout(() => {
        arrangeDealerHands();
        setTimeout(() => resolveHandsWithQuantum(), 2000);
    }, 2000);
}

// Arrange dealer hands with quantum advantage
function arrangeDealerHands() {
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];

    // Dealer gets optimal arrangement with quantum assistance
    const dealerTiles = [...paiGowParadoxGame.dealerTiles];

    // Apply quantum enhancement to dealer tiles
    dealerTiles.forEach(tile => {
        if (Math.random() < paradoxData.handManipulation) {
            // Quantum field enhances dealer tiles
            tile.value = Math.min(12, tile.value + 1);
            tile.rank = Math.max(1, tile.rank - 1); // Lower rank = better
        }
    });

    // Sort tiles optimally for dealer
    const sortedTiles = dealerTiles.sort((a, b) => b.value - a.value);

    // Optimal arrangement for dealer
    paiGowParadoxGame.dealerHighHand = [sortedTiles[0], sortedTiles[1]];
    paiGowParadoxGame.dealerLowHand = [sortedTiles[2], sortedTiles[3]];

    displayDealerHands();
    updateHandValues();
}

// Display player hands
function displayPlayerHands() {
    const highContainer = document.getElementById('playerHighHand');
    const lowContainer = document.getElementById('playerLowHand');

    highContainer.innerHTML = '';
    lowContainer.innerHTML = '';

    paiGowParadoxGame.playerHighHand.forEach(tile => {
        displayTileInHand(tile, highContainer);
    });

    paiGowParadoxGame.playerLowHand.forEach(tile => {
        displayTileInHand(tile, lowContainer);
    });
}

// Display dealer hands
function displayDealerHands() {
    const highContainer = document.getElementById('dealerHighHand');
    const lowContainer = document.getElementById('dealerLowHand');

    highContainer.innerHTML = '';
    lowContainer.innerHTML = '';

    paiGowParadoxGame.dealerHighHand.forEach(tile => {
        displayTileInHand(tile, highContainer);
    });

    paiGowParadoxGame.dealerLowHand.forEach(tile => {
        displayTileInHand(tile, lowContainer);
    });
}

// Display tile in hand
function displayTileInHand(tile, container) {
    const tileElement = document.createElement('div');
    tileElement.className = 'w-8 h-12 bg-gradient-to-br from-white to-gray-200 rounded border border-gray-400 flex flex-col items-center justify-center text-black text-xs font-bold shadow-md';

    // Add quantum effects
    if (tile.entangled) {
        tileElement.classList.add('ring-1', 'ring-blue-400');
    }

    if (tile.superposed) {
        tileElement.classList.add('ring-1', 'ring-green-400');
    }

    const dotsHtml = tile.dots.map(dots => `<div>${'●'.repeat(Math.min(dots, 3))}</div>`).join('');

    tileElement.innerHTML = `
        <div class="text-xs">${dotsHtml}</div>
        <div class="text-xs">${tile.value}</div>
    `;

    container.appendChild(tileElement);
}

// Calculate hand value
function calculateHandValue(hand) {
    if (hand.length !== 2) return 0;

    const tile1 = hand[0];
    const tile2 = hand[1];

    // Check for pairs first (highest value)
    if (tile1.rank === tile2.rank) {
        return 100 + tile1.rank; // Pairs are highest
    }

    // Check for special combinations
    if ((tile1.isSupreme && tile2.isSupreme)) {
        return 200; // Supreme pair (highest possible)
    }

    // Calculate point value (sum of dots mod 10, with rank tiebreaker)
    const pointValue = (tile1.value + tile2.value) % 10;
    const rankValue = Math.min(tile1.rank, tile2.rank); // Lower rank is better

    return pointValue * 10 + (25 - rankValue); // Higher points and lower rank = better
}

// Update hand values display
function updateHandValues() {
    const playerHighValue = calculateHandValue(paiGowParadoxGame.playerHighHand);
    const playerLowValue = calculateHandValue(paiGowParadoxGame.playerLowHand);
    const dealerHighValue = calculateHandValue(paiGowParadoxGame.dealerHighHand);
    const dealerLowValue = calculateHandValue(paiGowParadoxGame.dealerLowHand);

    document.getElementById('playerHighValue').textContent = playerHighValue > 0 ? playerHighValue : '-';
    document.getElementById('playerLowValue').textContent = playerLowValue > 0 ? playerLowValue : '-';
    document.getElementById('dealerHighValue').textContent = dealerHighValue > 0 ? dealerHighValue : '-';
    document.getElementById('dealerLowValue').textContent = dealerLowValue > 0 ? dealerLowValue : '-';
}

// Resolve hands with quantum effects and extreme house bias
function resolveHandsWithQuantum() {
    const modeData = QUANTUM_MODES[paiGowParadoxGame.quantumMode];
    const paradoxData = PARADOX_LEVELS[paiGowParadoxGame.paradoxLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate hand values
    const playerHighValue = calculateHandValue(paiGowParadoxGame.playerHighHand);
    const playerLowValue = calculateHandValue(paiGowParadoxGame.playerLowHand);
    const dealerHighValue = calculateHandValue(paiGowParadoxGame.dealerHighHand);
    const dealerLowValue = calculateHandValue(paiGowParadoxGame.dealerLowHand);

    // Apply quantum decoherence penalty to player hands
    let adjustedPlayerHigh = playerHighValue;
    let adjustedPlayerLow = playerLowValue;

    if (paiGowParadoxGame.quantum.active) {
        const decoherence = 1 - paiGowParadoxGame.quantum.coherence;
        adjustedPlayerHigh = Math.floor(playerHighValue * (1 - decoherence * 0.3));
        adjustedPlayerLow = Math.floor(playerLowValue * (1 - decoherence * 0.3));
    }

    // Determine hand winners
    const playerWinsHigh = adjustedPlayerHigh > dealerHighValue;
    const playerWinsLow = adjustedPlayerLow > dealerLowValue;

    // Apply quantum paradox resolution (almost always favors house)
    let finalPlayerWinsHigh = playerWinsHigh;
    let finalPlayerWinsLow = playerWinsLow;

    if (paiGowParadoxGame.quantum.waveFunction === 'paradox') {
        // Quantum paradox resolution - heavily biased against player
        if (playerWinsHigh && Math.random() < paradoxData.quantumInterference) {
            finalPlayerWinsHigh = false; // Paradox reverses player win
            paiGowParadoxGame.stats.paradoxesResolved++;
        }
        if (playerWinsLow && Math.random() < paradoxData.quantumInterference) {
            finalPlayerWinsLow = false; // Paradox reverses player win
            paiGowParadoxGame.stats.paradoxesResolved++;
        }
    }

    // Determine overall result
    let gameResult;
    if (finalPlayerWinsHigh && finalPlayerWinsLow) {
        gameResult = 'player_wins_both';
        resultMessage = 'Player wins both hands!';
    } else if (!finalPlayerWinsHigh && !finalPlayerWinsLow) {
        gameResult = 'dealer_wins_both';
        resultMessage = 'Dealer wins both hands';
    } else {
        gameResult = 'push';
        resultMessage = 'Push - One hand each';
    }

    // Calculate winnings with severe reductions
    if (gameResult === 'player_wins_both') {
        let winAmount = Math.floor(paiGowParadoxGame.betAmount * PARADOX_PAYOUTS.BOTH_HANDS_WIN);

        // Apply quantum dual reality bonus (fake - almost never applies)
        if (paiGowParadoxGame.quantum.superposition && Math.random() < 0.02) {
            const dualBonus = Math.floor(winAmount * PARADOX_PAYOUTS.DUAL_REALITY_BONUS);
            winAmount += dualBonus;
            paiGowParadoxGame.stats.dualWins++;
        }

        totalWinnings += winAmount;
    } else if (gameResult === 'push') {
        // Return bet (no profit)
        totalWinnings += paiGowParadoxGame.betAmount;
        paiGowParadoxGame.stats.pushes++;
    }
    // Dealer wins both = total loss (no winnings)

    // Calculate bonus bet winnings (terrible odds)
    totalWinnings += calculateBonusBetWinnings();

    // Calculate fortune bet winnings (almost never pays)
    totalWinnings += calculateFortuneBetWinnings();

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier);

    // Apply quantum interference penalty
    if (paiGowParadoxGame.quantum.active) {
        const interferencePenalty = paradoxData.quantumInterference * 0.4;
        totalWinnings = Math.floor(totalWinnings * (1 - interferencePenalty));
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    paiGowParadoxGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > paiGowParadoxGame.totalBet, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Quantum coherence: ${Math.floor(paiGowParadoxGame.quantum.coherence * 100)}%`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Calculate bonus bet winnings (terrible odds)
function calculateBonusBetWinnings() {
    if (paiGowParadoxGame.bonusBet === 0) return 0;

    let winnings = 0;
    const allPlayerTiles = [...paiGowParadoxGame.playerHighHand, ...paiGowParadoxGame.playerLowHand];

    // Check for bonus combinations (very rare due to quantum interference)
    if (hasFlush(allPlayerTiles) && Math.random() < 0.05) { // 5% chance even with flush
        winnings = Math.floor(paiGowParadoxGame.bonusBet * PARADOX_PAYOUTS.FLUSH);
    } else if (hasThreeOfAKind(allPlayerTiles) && Math.random() < 0.08) { // 8% chance
        winnings = Math.floor(paiGowParadoxGame.bonusBet * PARADOX_PAYOUTS.THREE_OF_A_KIND);
    }

    return winnings;
}

// Calculate fortune bet winnings (almost never pays)
function calculateFortuneBetWinnings() {
    if (paiGowParadoxGame.fortuneBet === 0) return 0;

    // Fortune bet almost never pays due to quantum interference
    if (Math.random() < 0.001) { // 0.1% chance
        return Math.floor(paiGowParadoxGame.fortuneBet * PARADOX_PAYOUTS.FOUR_OF_A_KIND_FORTUNE);
    }

    return 0;
}

// Check for flush (simplified for tiles)
function hasFlush(tiles) {
    // In Pai Gow tiles, check for specific combinations
    return tiles.some(tile => tile.value >= 10);
}

// Check for three of a kind (simplified)
function hasThreeOfAKind(tiles) {
    const values = tiles.map(tile => tile.value);
    return values.some(value => values.filter(v => v === value).length >= 3);
}

// Select tile for manual arrangement
function selectTileForArrangement(tile, element) {
    // Simple tile selection for manual arrangement
    element.classList.toggle('ring-2');
    element.classList.toggle('ring-yellow-400');
}

// Update quantum display
function updateQuantumDisplay() {
    const coherence = Math.floor(paiGowParadoxGame.quantum.coherence * 100);

    if (paiGowParadoxGame.quantum.active) {
        document.getElementById('quantumFieldStatus').innerHTML =
            `<div class="text-purple-400 font-bold animate-pulse">FIELD: ACTIVE (${coherence}%)</div>`;
        document.getElementById('quantumCoherence').textContent = `${coherence}%`;
    } else {
        document.getElementById('quantumFieldStatus').innerHTML =
            '<div class="text-purple-400 font-bold">FIELD: STABLE</div>';
        document.getElementById('quantumCoherence').textContent = '100%';
    }
}

// Update quantum effects visualization
function updateQuantumEffects() {
    // Update quantum field visualization based on current state
    const paradoxCore = document.getElementById('paradoxCore');
    if (paradoxCore) {
        if (paiGowParadoxGame.quantum.active) {
            paradoxCore.setAttribute('r', '50');
            paradoxCore.classList.add('animate-spin');
        } else {
            paradoxCore.setAttribute('r', '30');
            paradoxCore.classList.remove('animate-spin');
        }
    }
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('totalBetDisplay').textContent = `$${paiGowParadoxGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = paiGowParadoxGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${paiGowParadoxGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${paiGowParadoxGame.stats.totalWagered}`;
    document.getElementById('quantumEvents').textContent = paiGowParadoxGame.stats.quantumEvents;
    document.getElementById('dualWins').textContent = paiGowParadoxGame.stats.dualWins;

    const netResult = paiGowParadoxGame.stats.totalWon - paiGowParadoxGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    paiGowParadoxGame.stats.handsPlayed++;
    paiGowParadoxGame.stats.totalWagered += paiGowParadoxGame.totalBet;
    paiGowParadoxGame.stats.totalWon += winnings;

    if (won) {
        paiGowParadoxGame.stats.handsWon++;
        paiGowParadoxGame.stats.currentStreak++;
        paiGowParadoxGame.streakData.currentWinStreak++;
        paiGowParadoxGame.streakData.currentLossStreak = 0;

        if (paiGowParadoxGame.streakData.currentWinStreak > paiGowParadoxGame.streakData.longestWinStreak) {
            paiGowParadoxGame.streakData.longestWinStreak = paiGowParadoxGame.streakData.currentWinStreak;
        }

        if (winnings > paiGowParadoxGame.stats.biggestWin) {
            paiGowParadoxGame.stats.biggestWin = winnings;
        }
    } else {
        paiGowParadoxGame.stats.currentStreak = 0;
        paiGowParadoxGame.streakData.currentWinStreak = 0;
        paiGowParadoxGame.streakData.currentLossStreak++;

        if (paiGowParadoxGame.streakData.currentLossStreak > paiGowParadoxGame.streakData.longestLossStreak) {
            paiGowParadoxGame.streakData.longestLossStreak = paiGowParadoxGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to quantum effects)
    paiGowParadoxGame.stats.winRate = (paiGowParadoxGame.stats.handsWon / paiGowParadoxGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    paiGowParadoxGame.isPlaying = false;
    paiGowParadoxGame.betAmount = 0;
    paiGowParadoxGame.bonusBet = 0;
    paiGowParadoxGame.fortuneBet = 0;
    paiGowParadoxGame.totalBet = 0;
    paiGowParadoxGame.playerTiles = [];
    paiGowParadoxGame.playerHighHand = [];
    paiGowParadoxGame.playerLowHand = [];
    paiGowParadoxGame.dealerTiles = [];
    paiGowParadoxGame.dealerHighHand = [];
    paiGowParadoxGame.dealerLowHand = [];
    paiGowParadoxGame.gameResult = '';
    paiGowParadoxGame.totalWin = 0;

    // Reset quantum system
    paiGowParadoxGame.quantum.active = false;
    paiGowParadoxGame.quantum.entanglement = false;
    paiGowParadoxGame.quantum.superposition = false;
    paiGowParadoxGame.quantum.paradoxField = 0;
    paiGowParadoxGame.quantum.tileOverlaps = [];
    paiGowParadoxGame.quantum.dualRealities = [];
    paiGowParadoxGame.quantum.quantumStates = [];
    paiGowParadoxGame.quantum.coherence = 1.0;
    paiGowParadoxGame.quantum.decoherence = 0.0;
    paiGowParadoxGame.quantum.waveFunction = 'collapsed';

    // Clear displays
    document.getElementById('playerTiles').innerHTML = '';
    document.getElementById('dealerTiles').innerHTML = '';
    document.getElementById('playerHighHand').innerHTML = '';
    document.getElementById('playerLowHand').innerHTML = '';
    document.getElementById('dealerHighHand').innerHTML = '';
    document.getElementById('dealerLowHand').innerHTML = '';
    document.getElementById('playerHighValue').textContent = '-';
    document.getElementById('playerLowValue').textContent = '-';
    document.getElementById('dealerHighValue').textContent = '-';
    document.getElementById('dealerLowValue').textContent = '-';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('quantumEvent').classList.add('hidden');
    document.getElementById('quantumOverlaps').innerHTML = '';
    document.getElementById('handArrangement').classList.add('hidden');

    // Reset quantum status
    document.getElementById('quantumFieldStatus').innerHTML =
        '<div class="text-purple-400 font-bold">FIELD: STABLE</div>';
    document.getElementById('entanglementStatus').innerHTML =
        '<div class="text-blue-400 font-bold">ENTANGLEMENT: INACTIVE</div>';
    document.getElementById('superpositionStatus').innerHTML =
        '<div class="text-green-400 font-bold">SUPERPOSITION: COLLAPSED</div>';
    document.getElementById('paradoxStatus').innerHTML =
        '<div class="text-red-400 font-bold">PARADOX: RESOLVED</div>';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;
    document.getElementById('bonusBet').value = 0;
    document.getElementById('fortuneBet').value = 0;

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealTiles').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Quantum field stabilizing...';
    document.getElementById('gameMessage').textContent = 'Welcome to Pai Gow Paradox - Where Quantum Mechanics Meet Ancient Tiles';

    // Reinitialize systems for next hand
    initializeQuantumSystem();
    initializeTileSet();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPaiGowParadoxGame();
});