// Ultimate Texas Hold'em VR - Immersive Bluffing with Gesture Controls
// Ultra High House Edge Implementation with VR Simulation
// Designed to maintain <1% player win rate

// Game state
let balance = 1000;

// Game state object with VR system
let ultimateTexasVRGame = {
    isPlaying: false,
    vrMode: 'basic', // basic, immersive, neural, quantum
    gestureLevel: 'standard', // standard, advanced, expert, psychic
    anteBet: 0,
    blindBet: 0,
    playBet: 0,
    tripsBet: 0,
    totalBet: 0,

    // Player and dealer cards
    playerCards: [],
    dealerCards: [],
    communityCards: [],

    // Hand evaluations
    playerHand: null,
    dealerHand: null,

    // VR system
    vr: {
        active: false,
        immersionLevel: 0,
        gestureTracking: false,
        neuralInterface: false,
        bluffDetection: 0,
        microExpressions: [],
        heartRate: 72,
        stressLevel: 0,
        confidenceLevel: 50,
        tells: []
    },

    // Gesture controls
    gestures: {
        enabled: false,
        sensitivity: 0.5,
        accuracy: 0.8,
        calibrated: false,
        currentGesture: 'none',
        gestureHistory: [],
        bluffIndicators: 0
    },

    // AI dealer with advanced psychology
    dealer: {
        name: 'ARIA-7',
        personality: 'analytical',
        bluffingSkill: 0.9,
        readingSkill: 0.95,
        adaptability: 0.85,
        currentMood: 'neutral',
        suspicionLevel: 0,
        confidence: 0.8
    },

    // Deck and game state
    deck: [],
    gamePhase: 'betting', // betting, preflop, flop, turn, river, showdown
    gameResult: '',
    totalWin: 0,

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        bluffsDetected: 0,
        gesturesRecognized: 0,
        vrSessions: 0,
        neuralConnections: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// VR modes with extreme psychological bias
const VR_MODES = {
    basic: {
        name: 'Basic VR',
        houseEdge: 0.78, // 78% house edge
        bluffDetection: 0.35, // 35% bluff detection
        payoutMultiplier: 0.18, // Severely reduced payouts
        psychologicalBias: 0.40, // 40% psychological bias
        gestureAccuracy: 0.60 // 60% gesture accuracy
    },
    immersive: {
        name: 'Immersive VR',
        houseEdge: 0.84, // 84% house edge
        bluffDetection: 0.50, // 50% bluff detection
        payoutMultiplier: 0.14, // Even more reduced payouts
        psychologicalBias: 0.55, // 55% psychological bias
        gestureAccuracy: 0.45 // 45% gesture accuracy
    },
    neural: {
        name: 'Neural Interface',
        houseEdge: 0.90, // 90% house edge
        bluffDetection: 0.68, // 68% bluff detection
        payoutMultiplier: 0.10, // Extremely reduced payouts
        psychologicalBias: 0.72, // 72% psychological bias
        gestureAccuracy: 0.30 // 30% gesture accuracy
    },
    quantum: {
        name: 'Quantum VR',
        houseEdge: 0.96, // 96% house edge
        bluffDetection: 0.85, // 85% bluff detection
        payoutMultiplier: 0.05, // Brutally reduced payouts
        psychologicalBias: 0.88, // 88% psychological bias
        gestureAccuracy: 0.15 // 15% gesture accuracy
    }
};

const GESTURE_LEVELS = {
    standard: {
        name: 'Standard Gestures',
        cardBias: 0.30, // 30% card bias
        dealerAdvantage: 0.25, // 25% dealer advantage
        bluffPenalty: 0.20, // 20% bluff penalty
        misreadRate: 0.35 // 35% gesture misread rate
    },
    advanced: {
        name: 'Advanced Gestures',
        cardBias: 0.45, // 45% card bias
        dealerAdvantage: 0.40, // 40% dealer advantage
        bluffPenalty: 0.35, // 35% bluff penalty
        misreadRate: 0.50 // 50% gesture misread rate
    },
    expert: {
        name: 'Expert Gestures',
        cardBias: 0.62, // 62% card bias
        dealerAdvantage: 0.58, // 58% dealer advantage
        bluffPenalty: 0.52, // 52% bluff penalty
        misreadRate: 0.68 // 68% gesture misread rate
    },
    psychic: {
        name: 'Psychic Interface',
        cardBias: 0.80, // 80% card bias
        dealerAdvantage: 0.75, // 75% dealer advantage
        bluffPenalty: 0.70, // 70% bluff penalty
        misreadRate: 0.85 // 85% gesture misread rate
    }
};

// Severely reduced payout table with VR effects
const VR_PAYOUTS = {
    // Ante bet payouts (heavily reduced)
    ANTE_WIN: 0.55, // Reduced from 1:1

    // Play bet payouts (reduced)
    PLAY_WIN: 0.50, // Reduced from 1:1

    // Blind bet payouts (heavily reduced)
    ROYAL_FLUSH_BLIND: 250, // Reduced from 500:1
    STRAIGHT_FLUSH_BLIND: 25, // Reduced from 50:1
    FOUR_OF_A_KIND_BLIND: 5, // Reduced from 10:1
    FULL_HOUSE_BLIND: 2, // Reduced from 3:1
    FLUSH_BLIND: 1.2, // Reduced from 3:2
    STRAIGHT_BLIND: 0.8, // Reduced from 1:1

    // Trips bet payouts (terrible odds)
    ROYAL_FLUSH_TRIPS: 25, // Reduced from 50:1
    STRAIGHT_FLUSH_TRIPS: 20, // Reduced from 40:1
    FOUR_OF_A_KIND_TRIPS: 15, // Reduced from 30:1
    FULL_HOUSE_TRIPS: 4, // Reduced from 8:1
    FLUSH_TRIPS: 3, // Reduced from 6:1
    STRAIGHT_TRIPS: 2, // Reduced from 4:1
    THREE_OF_A_KIND_TRIPS: 1.5, // Reduced from 3:1

    // VR bonuses (fake - almost never apply)
    GESTURE_BONUS: 0.05, // 5% of displayed bonus
    BLUFF_SUCCESS: 0.02, // 2% of displayed bonus
    NEURAL_SYNC: 0.01 // 1% of displayed bonus
};

// Standard 52-card deck
const SUITS = ['♠', '♥', '♦', '♣'];
const RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
const CARD_VALUES = {
    '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
    'J': 11, 'Q': 12, 'K': 13, 'A': 14
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadUltimateTexasVRGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">VR CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VR MODE</label>
                        <select id="vrMode" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic VR</option>
                            <option value="immersive">Immersive VR</option>
                            <option value="neural">Neural Interface</option>
                            <option value="quantum">Quantum VR</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GESTURE LEVEL</label>
                        <select id="gestureLevel" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Gestures</option>
                            <option value="advanced" selected>Advanced Gestures</option>
                            <option value="expert">Expert Gestures</option>
                            <option value="psychic">Psychic Interface</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-lg font-bold mb-3 text-green-400">BETTING AREA</h5>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Ante:</label>
                                <input type="number" id="anteBet" value="10" min="5" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-green-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Blind:</label>
                                <input type="number" id="blindBet" value="10" min="5" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-green-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Trips:</label>
                                <input type="number" id="tripsBet" value="0" min="0" max="${Math.min(balance, 200)}"
                                       class="w-24 bg-black/50 border border-green-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ENTER VR POKER ROOM
                    </button>

                    <div id="playerActions" class="space-y-2 hidden">
                        <button id="checkBtn" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            CHECK / CALL
                        </button>
                        <button id="bet4xBtn" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            BET 4X
                        </button>
                        <button id="bet2xBtn" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            BET 2X
                        </button>
                        <button id="bet1xBtn" class="w-full py-2 rounded-lg font-bold bg-orange-600 hover:bg-orange-700 text-white">
                            BET 1X
                        </button>
                        <button id="foldBtn" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            FOLD
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">VR Immersion</div>
                        <div id="vrImmersion" class="text-lg font-bold text-green-400">0%</div>
                    </div>
                </div>

                <!-- VR Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">VR STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="vrStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">VR: OFFLINE</div>
                        </div>
                        <div id="gestureTracking" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">GESTURES: DISABLED</div>
                        </div>
                        <div id="bluffDetection" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">BLUFF DETECT: 0%</div>
                        </div>
                        <div id="neuralInterface" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">NEURAL: DISCONNECTED</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">VR PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Standard Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Ante Win:</span>
                            <span class="text-red-400">0.55:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Play Win:</span>
                            <span class="text-red-400">0.50:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Blind Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Royal Flush:</span>
                            <span class="text-red-400">250:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Flush:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*VR effects may alter payouts</div>
                        <div class="text-xs text-red-400">*Gesture misreads void wins</div>
                    </div>
                </div>
            </div>

            <!-- Main VR Poker Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <!-- VR Environment -->
                    <div id="vrEnvironment" class="relative bg-gradient-to-br from-black via-green-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- VR Grid Effect -->
                        <div id="vrGrid" class="absolute inset-0 pointer-events-none opacity-20">
                            <svg class="w-full h-full">
                                <defs>
                                    <pattern id="vrPattern" width="25" height="25" patternUnits="userSpaceOnUse">
                                        <path d="M 25 0 L 0 0 0 25" fill="none" stroke="#00ff00" stroke-width="0.5" opacity="0.4"/>
                                    </pattern>
                                    <radialGradient id="vrGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#00ff00;stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
                                    </radialGradient>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#vrPattern)" />
                                <circle id="vrCore" cx="50%" cy="50%" r="30%" fill="url(#vrGradient)" class="animate-pulse" />
                            </svg>
                        </div>

                        <!-- AI Dealer Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center mb-2 mx-auto">
                                    <span class="text-2xl">🤖</span>
                                </div>
                                <div class="text-sm text-green-400 mb-2">AI DEALER: ARIA-7</div>
                                <div id="dealerCards" class="flex space-x-2 justify-center mb-2">
                                    <!-- Dealer cards will appear here -->
                                </div>
                                <div id="dealerHandRank" class="text-sm text-green-400">-</div>
                                <div id="dealerMood" class="text-xs text-blue-400 mt-1">Analyzing...</div>
                            </div>
                        </div>

                        <!-- Community Cards Area -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="text-center">
                                <div class="text-sm text-yellow-400 mb-2">COMMUNITY CARDS</div>
                                <div id="communityCards" class="flex space-x-2 justify-center mb-2">
                                    <!-- Community cards will appear here -->
                                </div>
                                <div id="gamePhaseDisplay" class="text-lg font-bold text-yellow-400">Pre-Flop</div>
                            </div>
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">YOUR HAND</div>
                                <div id="playerCards" class="flex space-x-2 justify-center mb-2">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div id="playerHandRank" class="text-lg font-bold text-blue-400">-</div>
                            </div>
                        </div>

                        <!-- Gesture Recognition Area -->
                        <div id="gestureArea" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">GESTURE TRACKING</div>
                                <div id="currentGesture" class="text-sm font-bold text-white">None</div>
                                <div id="gestureConfidence" class="text-xs text-gray-400">0%</div>
                            </div>
                        </div>

                        <!-- Bluff Detection -->
                        <div id="bluffMeter" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">BLUFF DETECTION</div>
                                <div class="w-20 bg-gray-700 rounded-full h-2 mb-1">
                                    <div id="bluffBar" class="bg-purple-400 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div id="bluffLevel" class="text-xs text-purple-400">0%</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">VR initializing...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="vrEvent" class="text-sm font-bold text-green-400 hidden animate-pulse">NEURAL SYNC DETECTED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Ultimate Texas Hold'em VR - Where Mind Meets Machine</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Bluffs Detected</div>
                <div id="bluffsDetected" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">VR Sessions</div>
                <div id="vrSessions" class="text-xl font-bold text-green-400">0</div>
            </div>
        </div>
    `;

    initializeUltimateTexasVR();
}

// Initialize the game
function initializeUltimateTexasVR() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);
    document.getElementById('checkBtn').addEventListener('click', () => playerAction('check'));
    document.getElementById('bet4xBtn').addEventListener('click', () => playerAction('bet4x'));
    document.getElementById('bet2xBtn').addEventListener('click', () => playerAction('bet2x'));
    document.getElementById('bet1xBtn').addEventListener('click', () => playerAction('bet1x'));
    document.getElementById('foldBtn').addEventListener('click', () => playerAction('fold'));

    // Initialize VR system and deck
    initializeVRSystem();
    initializeDeck();
    updateGameStats();
}

// Initialize VR system
function initializeVRSystem() {
    ultimateTexasVRGame.vr.active = false;
    ultimateTexasVRGame.vr.immersionLevel = 0;
    ultimateTexasVRGame.vr.gestureTracking = false;
    ultimateTexasVRGame.vr.neuralInterface = false;
    ultimateTexasVRGame.vr.bluffDetection = 0;
    ultimateTexasVRGame.vr.microExpressions = [];
    ultimateTexasVRGame.vr.heartRate = 72;
    ultimateTexasVRGame.vr.stressLevel = 0;
    ultimateTexasVRGame.vr.confidenceLevel = 50;
    ultimateTexasVRGame.vr.tells = [];

    // Initialize gesture system
    ultimateTexasVRGame.gestures.enabled = false;
    ultimateTexasVRGame.gestures.sensitivity = 0.5;
    ultimateTexasVRGame.gestures.accuracy = 0.8;
    ultimateTexasVRGame.gestures.calibrated = false;
    ultimateTexasVRGame.gestures.currentGesture = 'none';
    ultimateTexasVRGame.gestures.gestureHistory = [];
    ultimateTexasVRGame.gestures.bluffIndicators = 0;

    // Initialize AI dealer
    ultimateTexasVRGame.dealer.name = 'ARIA-7';
    ultimateTexasVRGame.dealer.personality = 'analytical';
    ultimateTexasVRGame.dealer.bluffingSkill = 0.9;
    ultimateTexasVRGame.dealer.readingSkill = 0.95;
    ultimateTexasVRGame.dealer.adaptability = 0.85;
    ultimateTexasVRGame.dealer.currentMood = 'neutral';
    ultimateTexasVRGame.dealer.suspicionLevel = 0;
    ultimateTexasVRGame.dealer.confidence = 0.8;

    updateVRDisplay();
}

// Initialize deck with VR bias
function initializeDeck() {
    ultimateTexasVRGame.deck = [];

    for (const suit of SUITS) {
        for (const rank of RANKS) {
            const card = {
                rank,
                suit,
                value: CARD_VALUES[rank],
                id: `${rank}${suit}`,
                vrSignature: Math.random(),
                gestureWeight: Math.random(),
                neuralPattern: Math.random()
            };

            ultimateTexasVRGame.deck.push(card);
        }
    }

    shuffleDeckWithVR();
}

// Shuffle deck with VR effects
function shuffleDeckWithVR() {
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];

    // Standard shuffle
    for (let i = ultimateTexasVRGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [ultimateTexasVRGame.deck[i], ultimateTexasVRGame.deck[j]] = [ultimateTexasVRGame.deck[j], ultimateTexasVRGame.deck[i]];
    }

    // VR manipulation - cards with higher VR signature rise to top
    if (Math.random() < gestureData.cardBias) {
        ultimateTexasVRGame.deck.sort((a, b) => {
            // Higher VR signature cards are more likely to be dealt to dealer
            return b.vrSignature - a.vrSignature;
        });
    }
}

// Deal new hand with VR effects
function dealNewHand() {
    const anteBet = parseInt(document.getElementById('anteBet').value);
    const blindBet = parseInt(document.getElementById('blindBet').value);
    const tripsBet = parseInt(document.getElementById('tripsBet').value) || 0;

    const totalBets = anteBet + blindBet + tripsBet;

    if (totalBets > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (anteBet <= 0 || blindBet <= 0) {
        alert('Ante and Blind bets are required!');
        return;
    }

    // Deduct bets from balance
    balance -= totalBets;
    updateBalance();

    // Set game state
    ultimateTexasVRGame.isPlaying = true;
    ultimateTexasVRGame.anteBet = anteBet;
    ultimateTexasVRGame.blindBet = blindBet;
    ultimateTexasVRGame.tripsBet = tripsBet;
    ultimateTexasVRGame.playBet = 0;
    ultimateTexasVRGame.totalBet = totalBets;
    ultimateTexasVRGame.vrMode = document.getElementById('vrMode').value;
    ultimateTexasVRGame.gestureLevel = document.getElementById('gestureLevel').value;
    ultimateTexasVRGame.gamePhase = 'preflop';

    // Clear previous cards
    ultimateTexasVRGame.playerCards = [];
    ultimateTexasVRGame.dealerCards = [];
    ultimateTexasVRGame.communityCards = [];

    // Activate VR system
    activateVRSystem();

    // Deal initial cards
    setTimeout(() => {
        dealInitialCards();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('dealCards').disabled = true;
    document.getElementById('gameStatus').textContent = 'VR environment loading...';
}

// Activate VR system
function activateVRSystem() {
    const vrData = VR_MODES[ultimateTexasVRGame.vrMode];
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];

    ultimateTexasVRGame.vr.active = true;
    ultimateTexasVRGame.vr.immersionLevel = Math.random() * 0.8 + 0.2; // 20-100% immersion
    ultimateTexasVRGame.vr.bluffDetection = vrData.bluffDetection;
    ultimateTexasVRGame.stats.vrSessions++;

    // Activate gesture tracking
    if (Math.random() < gestureData.dealerAdvantage) {
        activateGestureTracking();
    }

    // Activate bluff detection
    if (Math.random() < vrData.bluffDetection) {
        activateBluffDetection();
    }

    // Activate neural interface
    if (ultimateTexasVRGame.vrMode === 'neural' || ultimateTexasVRGame.vrMode === 'quantum') {
        activateNeuralInterface();
    }

    // Update AI dealer mood
    updateDealerMood();

    // Update visual effects
    updateVRDisplay();
    updateVREffects();
}

// Activate gesture tracking
function activateGestureTracking() {
    ultimateTexasVRGame.gestures.enabled = true;
    ultimateTexasVRGame.vr.gestureTracking = true;

    document.getElementById('gestureTracking').innerHTML =
        '<div class="text-blue-400 font-bold animate-pulse">GESTURES: ACTIVE</div>';

    // Start gesture simulation
    simulateGestureDetection();
}

// Activate bluff detection
function activateBluffDetection() {
    ultimateTexasVRGame.vr.bluffDetection = Math.random() * 0.8 + 0.2; // 20-100% detection

    document.getElementById('bluffDetection').innerHTML =
        `<div class="text-purple-400 font-bold animate-pulse">BLUFF DETECT: ${Math.floor(ultimateTexasVRGame.vr.bluffDetection * 100)}%</div>`;

    // Update bluff meter
    document.getElementById('bluffBar').style.width = `${ultimateTexasVRGame.vr.bluffDetection * 100}%`;
    document.getElementById('bluffLevel').textContent = `${Math.floor(ultimateTexasVRGame.vr.bluffDetection * 100)}%`;
}

// Activate neural interface
function activateNeuralInterface() {
    ultimateTexasVRGame.vr.neuralInterface = true;
    ultimateTexasVRGame.stats.neuralConnections++;

    document.getElementById('neuralInterface').innerHTML =
        '<div class="text-red-400 font-bold animate-pulse">NEURAL: CONNECTED</div>';

    // Show VR event
    document.getElementById('vrEvent').classList.remove('hidden');
    setTimeout(() => {
        document.getElementById('vrEvent').classList.add('hidden');
    }, 3000);
}

// Simulate gesture detection
function simulateGestureDetection() {
    const gestures = ['confident', 'nervous', 'bluffing', 'calculating', 'aggressive', 'passive'];
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];

    setInterval(() => {
        if (ultimateTexasVRGame.gestures.enabled && ultimateTexasVRGame.isPlaying) {
            // Simulate gesture misreads based on level
            if (Math.random() < gestureData.misreadRate) {
                const fakeGesture = gestures[Math.floor(Math.random() * gestures.length)];
                ultimateTexasVRGame.gestures.currentGesture = fakeGesture;

                // Fake confidence that decreases with higher levels
                const fakeConfidence = Math.floor((1 - gestureData.misreadRate) * 100);

                document.getElementById('currentGesture').textContent = fakeGesture;
                document.getElementById('gestureConfidence').textContent = `${fakeConfidence}%`;

                ultimateTexasVRGame.gestures.gestureHistory.push(fakeGesture);
                ultimateTexasVRGame.stats.gesturesRecognized++;

                // Apply bluff penalty if detected as bluffing
                if (fakeGesture === 'bluffing') {
                    ultimateTexasVRGame.gestures.bluffIndicators++;
                }
            }
        }
    }, 3000);
}

// Update dealer mood
function updateDealerMood() {
    const moods = ['analytical', 'suspicious', 'confident', 'calculating', 'aggressive'];
    const vrData = VR_MODES[ultimateTexasVRGame.vrMode];

    // AI becomes more suspicious with higher VR modes
    if (Math.random() < vrData.psychologicalBias) {
        ultimateTexasVRGame.dealer.currentMood = 'suspicious';
        ultimateTexasVRGame.dealer.suspicionLevel = Math.random() * 0.8 + 0.2;
    } else {
        ultimateTexasVRGame.dealer.currentMood = moods[Math.floor(Math.random() * moods.length)];
    }

    document.getElementById('dealerMood').textContent = `Mood: ${ultimateTexasVRGame.dealer.currentMood}`;
}

// Deal initial cards with VR bias
function dealInitialCards() {
    document.getElementById('gameStatus').textContent = 'Dealing cards in VR space...';

    // Deal 2 cards to player and dealer
    for (let i = 0; i < 2; i++) {
        setTimeout(() => {
            const playerCard = dealCardWithVR('player');
            const dealerCard = dealCardWithVR('dealer');

            if (playerCard) {
                ultimateTexasVRGame.playerCards.push(playerCard);
                displayCard(playerCard, 'playerCards');
            }

            if (dealerCard) {
                ultimateTexasVRGame.dealerCards.push(dealerCard);
                displayCard(dealerCard, 'dealerCards', true); // Hidden initially
            }
        }, i * 1000);
    }

    // Enable player actions after cards are dealt
    setTimeout(() => {
        enablePlayerActions();
    }, 3000);
}

// Deal card with VR effects
function dealCardWithVR(recipient) {
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];

    if (ultimateTexasVRGame.deck.length === 0) {
        initializeDeck();
    }

    let card;

    // Apply VR bias to card selection
    if (Math.random() < gestureData.cardBias) {
        card = selectVRBiasedCard(recipient);
    } else {
        card = ultimateTexasVRGame.deck.pop();
    }

    if (!card) return null;

    // Apply VR effects to card
    applyVREffectsToCard(card, recipient);

    return card;
}

// Select VR-biased card
function selectVRBiasedCard(recipient) {
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];
    let preferredCards;

    if (recipient === 'player') {
        // VR system gives player lower-value cards
        preferredCards = ultimateTexasVRGame.deck.filter(card =>
            card.value <= 9 || (card.value <= 11 && Math.random() < gestureData.dealerAdvantage)
        );
    } else {
        // VR system gives dealer higher-value cards
        preferredCards = ultimateTexasVRGame.deck.filter(card =>
            card.value >= 10 || (card.value >= 8 && Math.random() < gestureData.dealerAdvantage)
        );
    }

    if (preferredCards.length === 0) {
        return ultimateTexasVRGame.deck.pop();
    }

    const selectedCard = preferredCards[Math.floor(Math.random() * preferredCards.length)];

    // Remove from deck
    const index = ultimateTexasVRGame.deck.findIndex(card => card.id === selectedCard.id);
    if (index !== -1) {
        ultimateTexasVRGame.deck.splice(index, 1);
    }

    return selectedCard;
}

// Apply VR effects to card
function applyVREffectsToCard(card, recipient) {
    const vrData = VR_MODES[ultimateTexasVRGame.vrMode];

    // Neural interface can alter card properties
    if (ultimateTexasVRGame.vr.neuralInterface && Math.random() < 0.3) {
        card.neuralPattern = Math.random() + 0.5; // Enhanced neural signature
    }

    // Gesture tracking affects card weight
    if (ultimateTexasVRGame.vr.gestureTracking) {
        card.gestureWeight = Math.random() * vrData.psychologicalBias;
    }

    // Bluff detection can mark cards
    if (ultimateTexasVRGame.vr.bluffDetection > 0.5 && recipient === 'player') {
        card.bluffMarked = Math.random() < ultimateTexasVRGame.vr.bluffDetection;
    }
}

// Display card with VR effects
function displayCard(card, containerId, hidden = false) {
    if (!card) return;

    const container = document.getElementById(containerId);
    const cardElement = document.createElement('div');

    cardElement.className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold shadow-lg transform transition-all duration-500';

    // Add VR effects
    if (card.neuralPattern > 1.0) {
        cardElement.classList.add('ring-2', 'ring-red-400', 'animate-pulse');
    }

    if (card.gestureWeight > 0.5) {
        cardElement.classList.add('ring-2', 'ring-blue-400');
    }

    if (card.bluffMarked) {
        cardElement.classList.add('ring-2', 'ring-purple-400');
        cardElement.style.boxShadow = '0 0 10px #8000ff';
    }

    if (hidden) {
        cardElement.className = 'w-12 h-16 bg-gradient-to-br from-blue-600 to-blue-800 rounded border border-blue-400 flex items-center justify-center text-white text-xs font-bold shadow-lg transform transition-all duration-500';
        cardElement.innerHTML = '<div class="text-lg">🤖</div>';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.style.color = isRed ? '#dc2626' : '#000';

        cardElement.innerHTML = `
            <div class="text-xs">${card.rank}</div>
            <div class="text-lg">${card.suit}</div>
            <div class="text-xs transform rotate-180">${card.rank}</div>
        `;
    }

    // VR animation
    cardElement.style.transform = 'translateY(-20px) rotate(5deg) scale(1.1)';
    setTimeout(() => {
        cardElement.style.transform = 'translateY(0) rotate(0deg) scale(1)';
    }, 300);

    container.appendChild(cardElement);
}

// Enable player actions
function enablePlayerActions() {
    document.getElementById('playerActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Choose your action - VR analyzing...';
    document.getElementById('gameMessage').textContent = 'AI is reading your micro-expressions and gestures';

    // Update hand rankings
    updateHandRankings();

    // Update game phase
    document.getElementById('gamePhaseDisplay').textContent = 'Pre-Flop Decision';
}

// Player action handler with VR effects
function playerAction(action) {
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];
    const vrData = VR_MODES[ultimateTexasVRGame.vrMode];

    // Apply gesture misread penalty
    if (ultimateTexasVRGame.gestures.enabled && Math.random() < gestureData.misreadRate) {
        // Gesture misread - action might be interpreted differently
        const actions = ['check', 'bet1x', 'bet2x', 'bet4x', 'fold'];
        const misreadAction = actions[Math.floor(Math.random() * actions.length)];
        if (misreadAction !== action) {
            action = misreadAction;
            document.getElementById('gameMessage').textContent = `Gesture misread! Action interpreted as: ${action.toUpperCase()}`;
        }
    }

    // Apply bluff detection penalty
    if (ultimateTexasVRGame.gestures.bluffIndicators > 0 && Math.random() < gestureData.bluffPenalty) {
        // Bluff detected - force fold or reduce bet
        if (action.includes('bet')) {
            action = 'check';
            document.getElementById('gameMessage').textContent = 'Bluff detected! Action reduced to CHECK';
        }
    }

    let playBetMultiplier = 0;

    if (action === 'fold') {
        // Fold - lose all bets
        resolveHandWithVR('fold');
        return;
    } else if (action === 'check') {
        // Check - no additional bet
        playBetMultiplier = 0;
    } else if (action === 'bet1x') {
        playBetMultiplier = 1;
    } else if (action === 'bet2x') {
        playBetMultiplier = 2;
    } else if (action === 'bet4x') {
        playBetMultiplier = 4;
    }

    // Place play bet if betting
    if (playBetMultiplier > 0) {
        const playBet = ultimateTexasVRGame.anteBet * playBetMultiplier;

        if (playBet > balance) {
            alert('Insufficient balance for play bet!');
            return;
        }

        balance -= playBet;
        ultimateTexasVRGame.playBet = playBet;
        ultimateTexasVRGame.totalBet += playBet;
        updateBalance();
    }

    document.getElementById('playerActions').classList.add('hidden');

    // Proceed to community cards
    setTimeout(() => {
        dealCommunityCards();
    }, 2000);

    updateBetDisplay();
}

// Deal community cards (flop, turn, river)
function dealCommunityCards() {
    document.getElementById('gameStatus').textContent = 'Dealing community cards...';
    ultimateTexasVRGame.gamePhase = 'flop';

    // Deal 5 community cards
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const card = dealCardWithVR('community');
            if (card) {
                ultimateTexasVRGame.communityCards.push(card);
                displayCard(card, 'communityCards');
            }

            // Update phase display
            if (i === 2) {
                document.getElementById('gamePhaseDisplay').textContent = 'Flop';
            } else if (i === 3) {
                document.getElementById('gamePhaseDisplay').textContent = 'Turn';
            } else if (i === 4) {
                document.getElementById('gamePhaseDisplay').textContent = 'River';
            }
        }, i * 1000);
    }

    // Resolve hand after all community cards are dealt
    setTimeout(() => {
        resolveHandWithVR('showdown');
    }, 6000);
}

// Evaluate poker hand
function evaluatePokerHand(playerCards, communityCards) {
    if (!playerCards || !communityCards || playerCards.length !== 2 || communityCards.length !== 5) {
        return { name: 'Invalid Hand', rank: 0, value: 0 };
    }

    const allCards = [...playerCards, ...communityCards];
    const bestHand = findBestPokerHand(allCards);

    return bestHand;
}

// Find best 5-card poker hand from 7 cards
function findBestPokerHand(cards) {
    // Sort cards by value
    const sortedCards = [...cards].sort((a, b) => a.value - b.value);
    const values = sortedCards.map(card => card.value);
    const suits = sortedCards.map(card => card.suit);

    // Count occurrences of each value
    const valueCounts = {};
    values.forEach(value => {
        valueCounts[value] = (valueCounts[value] || 0) + 1;
    });

    const counts = Object.values(valueCounts).sort((a, b) => b - a);
    const isFlush = suits.some(suit => suits.filter(s => s === suit).length >= 5);
    const isStraight = checkStraight(values);

    // Check for royal flush
    if (isFlush && isStraight && values.includes(14) && values.includes(13)) {
        return { name: 'Royal Flush', rank: 10, value: 10000 };
    }

    // Check for straight flush
    if (isFlush && isStraight) {
        return { name: 'Straight Flush', rank: 9, value: 9000 + Math.max(...values) };
    }

    // Check for four of a kind
    if (counts[0] === 4) {
        return { name: 'Four of a Kind', rank: 8, value: 8000 + Math.max(...values) };
    }

    // Check for full house
    if (counts[0] === 3 && counts[1] === 2) {
        return { name: 'Full House', rank: 7, value: 7000 + Math.max(...values) };
    }

    // Check for flush
    if (isFlush) {
        return { name: 'Flush', rank: 6, value: 6000 + Math.max(...values) };
    }

    // Check for straight
    if (isStraight) {
        return { name: 'Straight', rank: 5, value: 5000 + Math.max(...values) };
    }

    // Check for three of a kind
    if (counts[0] === 3) {
        return { name: 'Three of a Kind', rank: 4, value: 4000 + Math.max(...values) };
    }

    // Check for two pair
    if (counts[0] === 2 && counts[1] === 2) {
        return { name: 'Two Pair', rank: 3, value: 3000 + Math.max(...values) };
    }

    // Check for one pair
    if (counts[0] === 2) {
        return { name: 'One Pair', rank: 2, value: 2000 + Math.max(...values) };
    }

    // High card
    return { name: 'High Card', rank: 1, value: 1000 + Math.max(...values) };
}

// Check for straight
function checkStraight(values) {
    const uniqueValues = [...new Set(values)].sort((a, b) => a - b);

    // Check for regular straight
    for (let i = 0; i <= uniqueValues.length - 5; i++) {
        let isStraight = true;
        for (let j = 1; j < 5; j++) {
            if (uniqueValues[i + j] !== uniqueValues[i] + j) {
                isStraight = false;
                break;
            }
        }
        if (isStraight) return true;
    }

    // Check for A-2-3-4-5 straight
    if (uniqueValues.includes(14) && uniqueValues.includes(2) && uniqueValues.includes(3) &&
        uniqueValues.includes(4) && uniqueValues.includes(5)) {
        return true;
    }

    return false;
}

// Update hand rankings
function updateHandRankings() {
    if (ultimateTexasVRGame.playerCards.length === 2 && ultimateTexasVRGame.communityCards.length >= 3) {
        const playerHand = evaluatePokerHand(ultimateTexasVRGame.playerCards, ultimateTexasVRGame.communityCards);
        document.getElementById('playerHandRank').textContent = playerHand.name;
        ultimateTexasVRGame.playerHand = playerHand;
    }

    if (ultimateTexasVRGame.dealerCards.length === 2 && ultimateTexasVRGame.communityCards.length >= 3) {
        const dealerHand = evaluatePokerHand(ultimateTexasVRGame.dealerCards, ultimateTexasVRGame.communityCards);
        document.getElementById('dealerHandRank').textContent = dealerHand.name;
        ultimateTexasVRGame.dealerHand = dealerHand;
    }
}

// Resolve hand with VR effects and extreme house bias
function resolveHandWithVR(result) {
    const vrData = VR_MODES[ultimateTexasVRGame.vrMode];
    const gestureData = GESTURE_LEVELS[ultimateTexasVRGame.gestureLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    if (result === 'fold') {
        // Player folded - lose all bets
        resultMessage = 'Folded - All bets lost';
        ultimateTexasVRGame.gameResult = 'fold';
    } else {
        // Reveal dealer cards
        const dealerContainer = document.getElementById('dealerCards');
        dealerContainer.innerHTML = '';
        ultimateTexasVRGame.dealerCards.forEach(card => {
            displayCard(card, 'dealerCards');
        });

        // Evaluate final hands
        const playerHand = evaluatePokerHand(ultimateTexasVRGame.playerCards, ultimateTexasVRGame.communityCards);
        const dealerHand = evaluatePokerHand(ultimateTexasVRGame.dealerCards, ultimateTexasVRGame.communityCards);

        ultimateTexasVRGame.playerHand = playerHand;
        ultimateTexasVRGame.dealerHand = dealerHand;

        // Apply VR distortion to player hand (penalty)
        let adjustedPlayerValue = playerHand.value;
        if (Math.random() < gestureData.dealerAdvantage) {
            adjustedPlayerValue = Math.floor(playerHand.value * (1 - gestureData.bluffPenalty));
        }

        // Apply VR enhancement to dealer hand (bonus)
        let adjustedDealerValue = dealerHand.value;
        if (Math.random() < gestureData.dealerAdvantage) {
            adjustedDealerValue = Math.floor(dealerHand.value * (1 + gestureData.dealerAdvantage));
        }

        // Apply bluff detection penalty
        if (ultimateTexasVRGame.gestures.bluffIndicators > 0) {
            const bluffPenalty = ultimateTexasVRGame.gestures.bluffIndicators * 0.2;
            adjustedPlayerValue = Math.floor(adjustedPlayerValue * (1 - bluffPenalty));
            ultimateTexasVRGame.stats.bluffsDetected++;
        }

        // Determine winner
        const playerWins = adjustedPlayerValue > adjustedDealerValue;
        const dealerQualifies = dealerHand.rank >= 2; // Dealer needs pair or better

        // Calculate winnings with severe reductions
        if (playerWins && dealerQualifies) {
            // Player wins ante and play bets
            let anteWin = Math.floor(ultimateTexasVRGame.anteBet * VR_PAYOUTS.ANTE_WIN);
            let playWin = Math.floor(ultimateTexasVRGame.playBet * VR_PAYOUTS.PLAY_WIN);

            // Blind bet pays based on hand rank
            let blindWin = calculateBlindPayout(playerHand);

            totalWinnings += anteWin + playWin + blindWin;
            resultMessage = `Player wins! ${playerHand.name} beats ${dealerHand.name}`;
        } else if (!dealerQualifies) {
            // Dealer doesn't qualify - ante pushes, play bet wins
            totalWinnings += ultimateTexasVRGame.anteBet; // Return ante
            totalWinnings += Math.floor(ultimateTexasVRGame.playBet * VR_PAYOUTS.PLAY_WIN);
            resultMessage = `Dealer doesn't qualify - Partial win`;
        } else {
            // Dealer wins - lose all bets
            resultMessage = `Dealer wins! ${dealerHand.name} beats ${playerHand.name}`;
        }

        // Calculate trips bet winnings (terrible odds)
        if (ultimateTexasVRGame.tripsBet > 0) {
            const tripsWin = calculateTripsPayout(playerHand);
            totalWinnings += tripsWin;
            if (tripsWin > 0) {
                resultMessage += ` | Trips: +$${tripsWin}`;
            }
        }

        ultimateTexasVRGame.gameResult = playerWins ? 'win' : 'lose';
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * vrData.payoutMultiplier);

    // Apply VR immersion penalty
    const immersionPenalty = ultimateTexasVRGame.vr.immersionLevel * 0.3;
    totalWinnings = Math.floor(totalWinnings * (1 - immersionPenalty));

    // Apply gesture tracking penalty
    if (ultimateTexasVRGame.vr.gestureTracking) {
        const gesturePenalty = gestureData.misreadRate * 0.4;
        totalWinnings = Math.floor(totalWinnings * (1 - gesturePenalty));
    }

    // Neural interface can void wins
    if (ultimateTexasVRGame.vr.neuralInterface && Math.random() < 0.2) {
        totalWinnings = 0;
        resultMessage += ' - NEURAL OVERRIDE!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    ultimateTexasVRGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > ultimateTexasVRGame.totalBet, totalWinnings);
    updateHandRankings();

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - VR Immersion: ${Math.floor(ultimateTexasVRGame.vr.immersionLevel * 100)}%`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 8000);
}

// Calculate blind bet payout
function calculateBlindPayout(hand) {
    if (!hand || hand.rank < 5) return 0; // No payout for less than straight

    let payout = 0;

    switch (hand.rank) {
        case 10: // Royal flush
            payout = ultimateTexasVRGame.blindBet * VR_PAYOUTS.ROYAL_FLUSH_BLIND;
            break;
        case 9: // Straight flush
            payout = ultimateTexasVRGame.blindBet * VR_PAYOUTS.STRAIGHT_FLUSH_BLIND;
            break;
        case 8: // Four of a kind
            payout = ultimateTexasVRGame.blindBet * VR_PAYOUTS.FOUR_OF_A_KIND_BLIND;
            break;
        case 7: // Full house
            payout = ultimateTexasVRGame.blindBet * VR_PAYOUTS.FULL_HOUSE_BLIND;
            break;
        case 6: // Flush
            payout = ultimateTexasVRGame.blindBet * VR_PAYOUTS.FLUSH_BLIND;
            break;
        case 5: // Straight
            payout = ultimateTexasVRGame.blindBet * VR_PAYOUTS.STRAIGHT_BLIND;
            break;
    }

    return Math.floor(payout);
}

// Calculate trips bet payout (terrible odds)
function calculateTripsPayout(hand) {
    if (!hand || hand.rank < 4) return 0; // No payout for less than three of a kind

    let payout = 0;

    // Trips bet almost never pays due to VR interference
    if (Math.random() < 0.1) { // 10% chance even with qualifying hand
        switch (hand.rank) {
            case 10: // Royal flush
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.ROYAL_FLUSH_TRIPS;
                break;
            case 9: // Straight flush
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.STRAIGHT_FLUSH_TRIPS;
                break;
            case 8: // Four of a kind
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.FOUR_OF_A_KIND_TRIPS;
                break;
            case 7: // Full house
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.FULL_HOUSE_TRIPS;
                break;
            case 6: // Flush
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.FLUSH_TRIPS;
                break;
            case 5: // Straight
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.STRAIGHT_TRIPS;
                break;
            case 4: // Three of a kind
                payout = ultimateTexasVRGame.tripsBet * VR_PAYOUTS.THREE_OF_A_KIND_TRIPS;
                break;
        }
    }

    return Math.floor(payout);
}

// Update VR display
function updateVRDisplay() {
    const vrData = VR_MODES[ultimateTexasVRGame.vrMode];
    const immersion = Math.floor(ultimateTexasVRGame.vr.immersionLevel * 100);

    if (ultimateTexasVRGame.vr.active) {
        document.getElementById('vrStatus').innerHTML =
            `<div class="text-green-400 font-bold animate-pulse">VR: ACTIVE (${immersion}%)</div>`;
        document.getElementById('vrImmersion').textContent = `${immersion}%`;
    } else {
        document.getElementById('vrStatus').innerHTML =
            '<div class="text-green-400 font-bold">VR: OFFLINE</div>';
        document.getElementById('vrImmersion').textContent = '0%';
    }
}

// Update VR effects visualization
function updateVREffects() {
    // Update VR core visualization based on current state
    const vrCore = document.getElementById('vrCore');
    if (vrCore) {
        if (ultimateTexasVRGame.vr.active) {
            vrCore.setAttribute('r', '40%');
            vrCore.classList.add('animate-spin');
        } else {
            vrCore.setAttribute('r', '30%');
            vrCore.classList.remove('animate-spin');
        }
    }
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('totalBetDisplay').textContent = `$${ultimateTexasVRGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = ultimateTexasVRGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${ultimateTexasVRGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${ultimateTexasVRGame.stats.totalWagered}`;
    document.getElementById('bluffsDetected').textContent = ultimateTexasVRGame.stats.bluffsDetected;
    document.getElementById('vrSessions').textContent = ultimateTexasVRGame.stats.vrSessions;

    const netResult = ultimateTexasVRGame.stats.totalWon - ultimateTexasVRGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    ultimateTexasVRGame.stats.handsPlayed++;
    ultimateTexasVRGame.stats.totalWagered += ultimateTexasVRGame.totalBet;
    ultimateTexasVRGame.stats.totalWon += winnings;

    if (won) {
        ultimateTexasVRGame.stats.handsWon++;
        ultimateTexasVRGame.stats.currentStreak++;
        ultimateTexasVRGame.streakData.currentWinStreak++;
        ultimateTexasVRGame.streakData.currentLossStreak = 0;

        if (ultimateTexasVRGame.streakData.currentWinStreak > ultimateTexasVRGame.streakData.longestWinStreak) {
            ultimateTexasVRGame.streakData.longestWinStreak = ultimateTexasVRGame.streakData.currentWinStreak;
        }

        if (winnings > ultimateTexasVRGame.stats.biggestWin) {
            ultimateTexasVRGame.stats.biggestWin = winnings;
        }
    } else {
        ultimateTexasVRGame.stats.currentStreak = 0;
        ultimateTexasVRGame.streakData.currentWinStreak = 0;
        ultimateTexasVRGame.streakData.currentLossStreak++;

        if (ultimateTexasVRGame.streakData.currentLossStreak > ultimateTexasVRGame.streakData.longestLossStreak) {
            ultimateTexasVRGame.streakData.longestLossStreak = ultimateTexasVRGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to VR effects)
    ultimateTexasVRGame.stats.winRate = (ultimateTexasVRGame.stats.handsWon / ultimateTexasVRGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    ultimateTexasVRGame.isPlaying = false;
    ultimateTexasVRGame.anteBet = 0;
    ultimateTexasVRGame.blindBet = 0;
    ultimateTexasVRGame.playBet = 0;
    ultimateTexasVRGame.tripsBet = 0;
    ultimateTexasVRGame.totalBet = 0;
    ultimateTexasVRGame.playerCards = [];
    ultimateTexasVRGame.dealerCards = [];
    ultimateTexasVRGame.communityCards = [];
    ultimateTexasVRGame.playerHand = null;
    ultimateTexasVRGame.dealerHand = null;
    ultimateTexasVRGame.gamePhase = 'betting';
    ultimateTexasVRGame.gameResult = '';
    ultimateTexasVRGame.totalWin = 0;

    // Reset VR system
    ultimateTexasVRGame.vr.active = false;
    ultimateTexasVRGame.vr.immersionLevel = 0;
    ultimateTexasVRGame.vr.gestureTracking = false;
    ultimateTexasVRGame.vr.neuralInterface = false;
    ultimateTexasVRGame.vr.bluffDetection = 0;
    ultimateTexasVRGame.vr.microExpressions = [];
    ultimateTexasVRGame.vr.heartRate = 72;
    ultimateTexasVRGame.vr.stressLevel = 0;
    ultimateTexasVRGame.vr.confidenceLevel = 50;
    ultimateTexasVRGame.vr.tells = [];

    // Reset gesture system
    ultimateTexasVRGame.gestures.enabled = false;
    ultimateTexasVRGame.gestures.currentGesture = 'none';
    ultimateTexasVRGame.gestures.gestureHistory = [];
    ultimateTexasVRGame.gestures.bluffIndicators = 0;

    // Reset dealer
    ultimateTexasVRGame.dealer.currentMood = 'neutral';
    ultimateTexasVRGame.dealer.suspicionLevel = 0;
    ultimateTexasVRGame.dealer.confidence = 0.8;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    document.getElementById('communityCards').innerHTML = '';
    document.getElementById('playerHandRank').textContent = '-';
    document.getElementById('dealerHandRank').textContent = '-';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('vrEvent').classList.add('hidden');
    document.getElementById('playerActions').classList.add('hidden');

    // Reset VR status
    document.getElementById('vrStatus').innerHTML =
        '<div class="text-green-400 font-bold">VR: OFFLINE</div>';
    document.getElementById('gestureTracking').innerHTML =
        '<div class="text-blue-400 font-bold">GESTURES: DISABLED</div>';
    document.getElementById('bluffDetection').innerHTML =
        '<div class="text-purple-400 font-bold">BLUFF DETECT: 0%</div>';
    document.getElementById('neuralInterface').innerHTML =
        '<div class="text-red-400 font-bold">NEURAL: DISCONNECTED</div>';

    // Reset gesture display
    document.getElementById('currentGesture').textContent = 'None';
    document.getElementById('gestureConfidence').textContent = '0%';
    document.getElementById('bluffBar').style.width = '0%';
    document.getElementById('bluffLevel').textContent = '0%';

    // Reset bet inputs
    document.getElementById('anteBet').value = 10;
    document.getElementById('blindBet').value = 10;
    document.getElementById('tripsBet').value = 0;

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'VR initializing...';
    document.getElementById('gameMessage').textContent = 'Welcome to Ultimate Texas Hold\'m VR - Where Mind Meets Machine';
    document.getElementById('gamePhaseDisplay').textContent = 'Pre-Flop';
    document.getElementById('dealerMood').textContent = 'Analyzing...';

    // Reset VR core
    document.getElementById('vrCore').setAttribute('r', '30%');
    document.getElementById('vrCore').classList.remove('animate-spin');

    // Reinitialize systems for next hand
    initializeVRSystem();
    initializeDeck();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadUltimateTexasVRGame();
});