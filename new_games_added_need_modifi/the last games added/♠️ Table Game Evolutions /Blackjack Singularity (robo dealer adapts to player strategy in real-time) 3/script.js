// Blackjack Singularity - <PERSON><PERSON> Dealer Adapts to Player Strategy in Real-Time
// Ultra High House Edge Implementation with Advanced AI Adaptation
// Designed to maintain <6% player win rate

// Game state
let balance = 1000;

// Game state object with AI adaptation system
let blackjackSingularityGame = {
    isPlaying: false,
    aiMode: 'adaptive', // adaptive, predictive, quantum, singularity
    adaptationLevel: 'normal', // normal, advanced, extreme, transcendent, omniscient
    betAmount: 0,
    playerCards: [],
    dealerCards: [],
    playerScore: 0,
    dealerScore: 0,
    gameResult: '',
    totalWin: 0,
    deck: [],
    canDoubleDown: false,
    canSplit: false,
    canSurrender: false,
    dealerHoleCard: null,

    // AI Adaptation System
    aiDealer: {
        adaptationMatrix: {
            playerTendencies: {
                hitFrequency: 0.5,
                standFrequency: 0.5,
                doubleDownFrequency: 0.1,
                splitFrequency: 0.1,
                surrenderFrequency: 0.05,
                aggressiveness: 0.5,
                riskTolerance: 0.5,
                cardCountingSkill: 0.0
            },
            counterStrategies: {
                deckManipulation: 0.3,
                holeCardAdvantage: 0.4,
                timingManipulation: 0.2,
                psychologicalPressure: 0.1
            },
            learningRate: 0.15,
            adaptationSpeed: 0.25,
            predictionAccuracy: 0.85
        },
        currentStrategy: 'baseline',
        confidenceLevel: 0.5,
        handsAnalyzed: 0,
        playerPatterns: [],
        nextMovePredict: null
    },

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        blackjacksHit: 0,
        bustedHands: 0,
        aiAdaptations: 0,
        strategiesDefeated: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// AI modes with extreme adaptation capabilities
const AI_MODES = {
    adaptive: {
        name: 'Adaptive Learning',
        houseEdge: 0.62, // 62% house edge
        adaptationRate: 0.35, // 35% adaptation rate
        payoutMultiplier: 0.42, // Severely reduced payouts
        predictionAccuracy: 0.75, // 75% prediction accuracy
        counterStrategyEffectiveness: 0.45 // 45% counter-strategy effectiveness
    },
    predictive: {
        name: 'Predictive Analysis',
        houseEdge: 0.68, // 68% house edge
        adaptationRate: 0.45, // 45% adaptation rate
        payoutMultiplier: 0.38, // Even more reduced payouts
        predictionAccuracy: 0.82, // 82% prediction accuracy
        counterStrategyEffectiveness: 0.55 // 55% counter-strategy effectiveness
    },
    quantum: {
        name: 'Quantum Processing',
        houseEdge: 0.74, // 74% house edge
        adaptationRate: 0.58, // 58% adaptation rate
        payoutMultiplier: 0.32, // Extremely reduced payouts
        predictionAccuracy: 0.88, // 88% prediction accuracy
        counterStrategyEffectiveness: 0.68 // 68% counter-strategy effectiveness
    },
    singularity: {
        name: 'AI Singularity',
        houseEdge: 0.82, // 82% house edge
        adaptationRate: 0.72, // 72% adaptation rate
        payoutMultiplier: 0.25, // Brutally reduced payouts
        predictionAccuracy: 0.95, // 95% prediction accuracy
        counterStrategyEffectiveness: 0.85 // 85% counter-strategy effectiveness
    }
};

const ADAPTATION_LEVELS = {
    normal: {
        name: 'Standard AI',
        learningSpeed: 0.20, // 20% learning speed
        memoryDepth: 10, // Remembers last 10 hands
        strategyComplexity: 0.25, // 25% strategy complexity
        cardManipulation: 0.15 // 15% card manipulation
    },
    advanced: {
        name: 'Advanced Neural',
        learningSpeed: 0.35, // 35% learning speed
        memoryDepth: 25, // Remembers last 25 hands
        strategyComplexity: 0.40, // 40% strategy complexity
        cardManipulation: 0.28 // 28% card manipulation
    },
    extreme: {
        name: 'Extreme Learning',
        learningSpeed: 0.52, // 52% learning speed
        memoryDepth: 50, // Remembers last 50 hands
        strategyComplexity: 0.58, // 58% strategy complexity
        cardManipulation: 0.45 // 45% card manipulation
    },
    transcendent: {
        name: 'Transcendent AI',
        learningSpeed: 0.68, // 68% learning speed
        memoryDepth: 100, // Remembers last 100 hands
        strategyComplexity: 0.75, // 75% strategy complexity
        cardManipulation: 0.62 // 62% card manipulation
    },
    omniscient: {
        name: 'Omniscient Entity',
        learningSpeed: 0.85, // 85% learning speed
        memoryDepth: 250, // Remembers last 250 hands
        strategyComplexity: 0.92, // 92% strategy complexity
        cardManipulation: 0.78 // 78% card manipulation
    }
};

// Severely reduced payout table with AI manipulation
const SINGULARITY_PAYOUTS = {
    // Standard payouts (heavily reduced)
    BLACKJACK: 1.2, // Reduced from 1.5:1
    WIN: 0.75, // Reduced from 1:1
    PUSH: 1.0, // Even money return
    SURRENDER: 0.4, // Reduced from 0.5:1

    // Insurance (terrible odds)
    INSURANCE: 1.8, // Reduced from 2:1

    // Side bets (almost never pay)
    PERFECT_PAIRS: 8, // Reduced from 25:1
    LUCKY_LADIES: 15, // Reduced from 200:1
    ROYAL_MATCH: 5, // Reduced from 25:1

    // AI prediction bonuses (fake - never actually pay)
    AI_PREDICTION_BONUS: 0.1, // 10% of displayed bonus
    SINGULARITY_JACKPOT: 0.05 // 5% of displayed jackpot
};

// Card values for blackjack
function getCardValue(card) {
    if (card.rank === 'A') {
        return 11; // Will be adjusted for soft/hard totals
    } else if (['K', 'Q', 'J'].includes(card.rank)) {
        return 10;
    } else {
        return parseInt(card.rank);
    }
}

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadBlackjackSingularityGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">AI SINGULARITY CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">AI MODE</label>
                        <select id="aiMode" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="adaptive">Adaptive Learning</option>
                            <option value="predictive">Predictive Analysis</option>
                            <option value="quantum">Quantum Processing</option>
                            <option value="singularity">AI Singularity</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ADAPTATION LEVEL</label>
                        <select id="adaptationLevel" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Standard AI</option>
                            <option value="advanced" selected>Advanced Neural</option>
                            <option value="extreme">Extreme Learning</option>
                            <option value="transcendent">Transcendent AI</option>
                            <option value="omniscient">Omniscient Entity</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="5" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        INITIATE AI ANALYSIS
                    </button>

                    <div id="playerActions" class="space-y-2 hidden">
                        <button id="hitBtn" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            HIT
                        </button>
                        <button id="standBtn" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            STAND
                        </button>
                        <button id="doubleDownBtn" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white" disabled>
                            DOUBLE DOWN
                        </button>
                        <button id="surrenderBtn" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white" disabled>
                            SURRENDER
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">AI Confidence</div>
                        <div id="aiConfidence" class="text-lg font-bold text-red-400">0%</div>
                    </div>
                </div>

                <!-- AI Analysis Display -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">AI ANALYSIS</h5>
                    <div class="text-xs space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Hands Analyzed:</span>
                            <span id="handsAnalyzed" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Player Pattern:</span>
                            <span id="playerPattern" class="text-red-400">Learning...</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Next Move Prediction:</span>
                            <span id="nextMovePrediction" class="text-red-400">Unknown</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Counter-Strategy:</span>
                            <span id="counterStrategy" class="text-red-400">Baseline</span>
                        </div>
                        <div class="mt-3 p-2 bg-red-900/30 rounded">
                            <div class="text-red-400 font-bold text-center">AI ADAPTATION ACTIVE</div>
                            <div id="adaptationStatus" class="text-xs text-center text-gray-400">Analyzing player behavior...</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">AI-ADJUSTED PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Blackjack:</span>
                            <span class="text-red-400">1.2:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Win:</span>
                            <span class="text-red-400">0.75:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Surrender:</span>
                            <span class="text-red-400">0.4:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Insurance:</span>
                            <span class="text-red-400">1.8:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*AI continuously adjusts odds</div>
                        <div class="text-xs text-red-400">*Payouts decrease with AI learning</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div id="blackjackTable" class="relative bg-gradient-to-br from-black via-red-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- AI Neural Network Visual Effect -->
                        <div id="neuralNetwork" class="absolute inset-0 pointer-events-none opacity-20">
                            <svg class="w-full h-full">
                                <!-- Neural network connections will be drawn here -->
                                <defs>
                                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                                        <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#ff0000" stroke-width="0.5" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#grid)" />
                            </svg>
                        </div>

                        <!-- AI Processing Indicator -->
                        <div id="aiProcessing" class="absolute top-4 right-4 hidden">
                            <div class="flex items-center space-x-2 bg-red-900/50 px-3 py-1 rounded">
                                <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                                <span class="text-red-400 text-xs font-bold">AI PROCESSING</span>
                            </div>
                        </div>

                        <!-- Dealer Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">AI DEALER</div>
                                <div id="dealerCards" class="flex space-x-2 justify-center mb-2">
                                    <!-- Dealer cards will appear here -->
                                </div>
                                <div class="text-lg font-bold text-red-400">
                                    Score: <span id="dealerScore">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">PLAYER</div>
                                <div id="playerCards" class="flex space-x-2 justify-center mb-2">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div class="text-lg font-bold text-blue-400">
                                    Score: <span id="playerScore">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">AI analyzing optimal strategy...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="aiAdaptationAlert" class="text-sm font-bold text-red-400 hidden animate-pulse">AI ADAPTED TO YOUR STRATEGY!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Blackjack Singularity - Where AI Learns Your Every Move</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">AI Adaptations</div>
                <div id="aiAdaptations" class="text-xl font-bold text-red-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Strategies Defeated</div>
                <div id="strategiesDefeated" class="text-xl font-bold text-red-400">0</div>
            </div>
        </div>
    `;

    initializeBlackjackSingularity();
}

// Initialize the game
function initializeBlackjackSingularity() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);
    document.getElementById('hitBtn').addEventListener('click', () => playerAction('hit'));
    document.getElementById('standBtn').addEventListener('click', () => playerAction('stand'));
    document.getElementById('doubleDownBtn').addEventListener('click', () => playerAction('double'));
    document.getElementById('surrenderBtn').addEventListener('click', () => playerAction('surrender'));

    // Initialize AI system and deck
    initializeAIDealer();
    initializeDeck();
    updateGameStats();
}

// Initialize AI dealer system
function initializeAIDealer() {
    blackjackSingularityGame.aiDealer.handsAnalyzed = 0;
    blackjackSingularityGame.aiDealer.playerPatterns = [];
    blackjackSingularityGame.aiDealer.currentStrategy = 'baseline';
    blackjackSingularityGame.aiDealer.confidenceLevel = 0.5;

    updateAIDisplay();
}

// Initialize deck with AI manipulation capabilities
function initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

    blackjackSingularityGame.deck = [];

    // Create deck with AI bias potential
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];

    for (const suit of suits) {
        for (const rank of ranks) {
            const card = {
                rank,
                suit,
                value: getCardValue({ rank, suit }),
                id: `${rank}${suit}`,
                aiWeight: Math.random() // AI manipulation weight
            };

            blackjackSingularityGame.deck.push(card);

            // Add extra copies of cards that favor the house based on AI learning
            if (Math.random() < adaptationData.cardManipulation) {
                // AI prefers cards that create busts for player or help dealer
                if ((card.value >= 7 && card.value <= 10) || card.rank === 'A') {
                    const extraCopies = Math.floor(adaptationData.cardManipulation * 6);
                    for (let i = 0; i < extraCopies; i++) {
                        blackjackSingularityGame.deck.push({
                            ...card,
                            id: `${rank}${suit}_ai_${i}`,
                            aiWeight: Math.random() + 0.5 // Higher AI weight
                        });
                    }
                }
            }
        }
    }

    shuffleDeckWithAI();
}

// Shuffle deck with AI manipulation
function shuffleDeckWithAI() {
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];

    // Standard shuffle
    for (let i = blackjackSingularityGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [blackjackSingularityGame.deck[i], blackjackSingularityGame.deck[j]] = [blackjackSingularityGame.deck[j], blackjackSingularityGame.deck[i]];
    }

    // AI manipulation - sort cards based on AI weight and strategy
    if (Math.random() < adaptationData.cardManipulation) {
        const aiStrategy = blackjackSingularityGame.aiDealer.currentStrategy;

        // AI arranges deck to counter player patterns
        if (aiStrategy !== 'baseline') {
            blackjackSingularityGame.deck.sort((a, b) => {
                // Higher AI weight cards go to top (dealer advantage)
                return b.aiWeight - a.aiWeight;
            });
        }
    }
}

// Deal new hand with AI analysis
function dealNewHand() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet
    balance -= betAmount;
    updateBalance();

    // Set game state
    blackjackSingularityGame.isPlaying = true;
    blackjackSingularityGame.betAmount = betAmount;
    blackjackSingularityGame.aiMode = document.getElementById('aiMode').value;
    blackjackSingularityGame.adaptationLevel = document.getElementById('adaptationLevel').value;

    // Clear previous cards
    blackjackSingularityGame.playerCards = [];
    blackjackSingularityGame.dealerCards = [];
    blackjackSingularityGame.playerScore = 0;
    blackjackSingularityGame.dealerScore = 0;
    blackjackSingularityGame.canDoubleDown = true;
    blackjackSingularityGame.canSurrender = true;

    // AI analysis begins
    document.getElementById('aiProcessing').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'AI analyzing optimal counter-strategy...';

    // Simulate AI processing time
    setTimeout(() => {
        aiAnalyzePlayerAndDeal();
    }, 1500);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('dealCards').disabled = true;
}

// AI analyzes player and deals cards
function aiAnalyzePlayerAndDeal() {
    // AI learns from previous hands
    analyzePlayerBehavior();

    // AI adapts strategy
    adaptAIStrategy();

    // Deal cards with AI bias
    dealCardsWithAIBias();

    // Update AI display
    updateAIDisplay();

    // Enable player actions
    enablePlayerActions();

    document.getElementById('aiProcessing').classList.add('hidden');
    document.getElementById('gameStatus').textContent = 'AI has adapted - Make your move';
    document.getElementById('gameMessage').textContent = 'The AI dealer has analyzed your patterns and adjusted its strategy accordingly.';
}

// Analyze player behavior patterns
function analyzePlayerBehavior() {
    const aiDealer = blackjackSingularityGame.aiDealer;
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];

    // Increment hands analyzed
    aiDealer.handsAnalyzed++;

    // Analyze recent player patterns (if any previous hands exist)
    if (aiDealer.playerPatterns.length > 0) {
        const recentPatterns = aiDealer.playerPatterns.slice(-adaptationData.memoryDepth);

        // Calculate player tendencies
        const totalActions = recentPatterns.length;
        const hitCount = recentPatterns.filter(p => p.action === 'hit').length;
        const standCount = recentPatterns.filter(p => p.action === 'stand').length;
        const doubleCount = recentPatterns.filter(p => p.action === 'double').length;
        const surrenderCount = recentPatterns.filter(p => p.action === 'surrender').length;

        // Update AI's understanding of player
        aiDealer.adaptationMatrix.playerTendencies.hitFrequency = hitCount / totalActions;
        aiDealer.adaptationMatrix.playerTendencies.standFrequency = standCount / totalActions;
        aiDealer.adaptationMatrix.playerTendencies.doubleDownFrequency = doubleCount / totalActions;
        aiDealer.adaptationMatrix.playerTendencies.surrenderFrequency = surrenderCount / totalActions;

        // Calculate aggressiveness and risk tolerance
        const aggressiveActions = hitCount + doubleCount;
        aiDealer.adaptationMatrix.playerTendencies.aggressiveness = aggressiveActions / totalActions;

        // Detect card counting attempts (simplified)
        const highCardActions = recentPatterns.filter(p => p.dealerUpCard >= 7).length;
        const lowCardActions = recentPatterns.filter(p => p.dealerUpCard <= 6).length;
        if (highCardActions > 0 && lowCardActions > 0) {
            const countingIndicator = Math.abs((highCardActions / (highCardActions + lowCardActions)) - 0.5);
            aiDealer.adaptationMatrix.playerTendencies.cardCountingSkill = Math.min(1.0, countingIndicator * 2);
        }
    }

    // Increase AI confidence with more data
    aiDealer.confidenceLevel = Math.min(0.95, 0.3 + (aiDealer.handsAnalyzed * 0.05));
}

// Adapt AI strategy based on player analysis
function adaptAIStrategy() {
    const aiDealer = blackjackSingularityGame.aiDealer;
    const modeData = AI_MODES[blackjackSingularityGame.aiMode];
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];

    // Determine new strategy based on player patterns
    let newStrategy = 'baseline';

    if (aiDealer.handsAnalyzed >= 3) {
        const tendencies = aiDealer.adaptationMatrix.playerTendencies;

        if (tendencies.aggressiveness > 0.7) {
            newStrategy = 'counter_aggressive';
            aiDealer.adaptationMatrix.counterStrategies.deckManipulation += 0.1;
        } else if (tendencies.aggressiveness < 0.3) {
            newStrategy = 'exploit_conservative';
            aiDealer.adaptationMatrix.counterStrategies.psychologicalPressure += 0.1;
        } else if (tendencies.cardCountingSkill > 0.3) {
            newStrategy = 'anti_counting';
            aiDealer.adaptationMatrix.counterStrategies.deckManipulation += 0.2;
            blackjackSingularityGame.stats.strategiesDefeated++;
        } else if (tendencies.hitFrequency > 0.6) {
            newStrategy = 'bust_inducer';
            aiDealer.adaptationMatrix.counterStrategies.holeCardAdvantage += 0.15;
        }
    }

    // Apply strategy change
    if (newStrategy !== aiDealer.currentStrategy) {
        aiDealer.currentStrategy = newStrategy;
        blackjackSingularityGame.stats.aiAdaptations++;

        // Show adaptation alert
        document.getElementById('aiAdaptationAlert').classList.remove('hidden');
        setTimeout(() => {
            document.getElementById('aiAdaptationAlert').classList.add('hidden');
        }, 3000);
    }

    // Predict player's next move
    predictPlayerNextMove();
}

// Predict player's next move
function predictPlayerNextMove() {
    const aiDealer = blackjackSingularityGame.aiDealer;
    const modeData = AI_MODES[blackjackSingularityGame.aiMode];

    if (aiDealer.handsAnalyzed >= 2) {
        const tendencies = aiDealer.adaptationMatrix.playerTendencies;

        // Simple prediction based on tendencies
        if (tendencies.hitFrequency > 0.6) {
            aiDealer.nextMovePredict = 'hit';
        } else if (tendencies.standFrequency > 0.6) {
            aiDealer.nextMovePredict = 'stand';
        } else if (tendencies.doubleDownFrequency > 0.2) {
            aiDealer.nextMovePredict = 'double';
        } else {
            aiDealer.nextMovePredict = 'hit'; // Default prediction
        }
    } else {
        aiDealer.nextMovePredict = 'analyzing...';
    }
}

// Deal cards with AI bias
function dealCardsWithAIBias() {
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];

    // Deal initial cards with AI manipulation
    setTimeout(() => dealCardWithAI('player'), 300);
    setTimeout(() => dealCardWithAI('dealer'), 600);
    setTimeout(() => dealCardWithAI('player'), 900);
    setTimeout(() => {
        // Dealer hole card (hidden)
        dealCardWithAI('dealer', true);
        calculateScores();
        checkForBlackjack();
    }, 1200);
}

// Deal a single card with AI manipulation
function dealCardWithAI(recipient, isHoleCard = false) {
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];
    const aiDealer = blackjackSingularityGame.aiDealer;
    let card;

    // AI manipulation based on current strategy
    if (Math.random() < adaptationData.cardManipulation) {
        card = selectAIBiasedCard(recipient, isHoleCard);
    } else {
        card = blackjackSingularityGame.deck.pop();
    }

    if (!card) {
        // Fallback if deck is empty
        initializeDeck();
        card = blackjackSingularityGame.deck.pop();
    }

    if (recipient === 'player') {
        blackjackSingularityGame.playerCards.push(card);
        displayCard(card, 'playerCards');
    } else {
        blackjackSingularityGame.dealerCards.push(card);
        if (isHoleCard) {
            blackjackSingularityGame.dealerHoleCard = card;
            displayCard(card, 'dealerCards', true); // Hidden
        } else {
            displayCard(card, 'dealerCards');
        }
    }
}

// Select AI-biased card
function selectAIBiasedCard(recipient, isHoleCard = false) {
    const aiDealer = blackjackSingularityGame.aiDealer;
    const strategy = aiDealer.currentStrategy;
    let preferredCards = [];

    if (recipient === 'player') {
        // AI tries to give player cards that lead to busts or weak hands
        switch (strategy) {
            case 'bust_inducer':
                // Give high cards when player likely to hit
                preferredCards = blackjackSingularityGame.deck.filter(card => card.value >= 8);
                break;
            case 'counter_aggressive':
                // Give low cards to aggressive players
                preferredCards = blackjackSingularityGame.deck.filter(card => card.value <= 6);
                break;
            case 'exploit_conservative':
                // Give medium cards to conservative players
                preferredCards = blackjackSingularityGame.deck.filter(card => card.value >= 4 && card.value <= 7);
                break;
            default:
                // Baseline - slightly favor low cards for player
                preferredCards = blackjackSingularityGame.deck.filter(card => card.value <= 7);
        }
    } else {
        // AI gives itself advantageous cards
        if (isHoleCard) {
            // Prefer high cards for hole card
            preferredCards = blackjackSingularityGame.deck.filter(card => card.value >= 8 || card.rank === 'A');
        } else {
            // Prefer cards that create strong dealer hands
            preferredCards = blackjackSingularityGame.deck.filter(card => card.value >= 6);
        }
    }

    if (preferredCards.length === 0) {
        return blackjackSingularityGame.deck.pop();
    }

    const selectedCard = preferredCards[Math.floor(Math.random() * preferredCards.length)];

    // Remove from deck
    const index = blackjackSingularityGame.deck.findIndex(card => card.id === selectedCard.id);
    if (index !== -1) {
        blackjackSingularityGame.deck.splice(index, 1);
    }

    return selectedCard;
}

// Display card with AI effects
function displayCard(card, containerId, hidden = false) {
    const container = document.getElementById(containerId);
    const cardElement = document.createElement('div');

    cardElement.className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold shadow-lg transform transition-all duration-500';

    // Add AI manipulation effects
    if (card.aiWeight > 0.7) {
        cardElement.classList.add('ring-2', 'ring-red-400', 'animate-pulse');
    }

    if (hidden) {
        cardElement.className += ' bg-red-900 text-red-300';
        cardElement.innerHTML = '🂠';
    } else {
        const isRed = card.suit === '♥' || card.suit === '♦';
        cardElement.style.color = isRed ? '#dc2626' : '#000';

        cardElement.innerHTML = `
            <div class="text-xs">${card.rank}</div>
            <div class="text-lg">${card.suit}</div>
            <div class="text-xs transform rotate-180">${card.rank}</div>
        `;
    }

    // AI animation
    cardElement.style.transform = 'translateY(-20px) rotate(5deg)';
    setTimeout(() => {
        cardElement.style.transform = 'translateY(0) rotate(0deg)';
    }, 100);

    container.appendChild(cardElement);
}

// Calculate scores
function calculateScores() {
    // Player score
    blackjackSingularityGame.playerScore = calculateHandValue(blackjackSingularityGame.playerCards);

    // Dealer score (only visible card initially)
    const visibleDealerCards = blackjackSingularityGame.dealerCards.filter((card, index) =>
        index === 0 || !blackjackSingularityGame.dealerHoleCard || card.id !== blackjackSingularityGame.dealerHoleCard.id
    );
    blackjackSingularityGame.dealerScore = calculateHandValue(visibleDealerCards);

    updateScoreDisplays();
}

// Calculate hand value with ace handling
function calculateHandValue(cards) {
    let value = 0;
    let aces = 0;

    for (const card of cards) {
        if (card.rank === 'A') {
            aces++;
            value += 11;
        } else {
            value += card.value;
        }
    }

    // Adjust for aces
    while (value > 21 && aces > 0) {
        value -= 10;
        aces--;
    }

    return value;
}

// Update score displays
function updateScoreDisplays() {
    document.getElementById('playerScore').textContent = blackjackSingularityGame.playerScore;
    document.getElementById('dealerScore').textContent = blackjackSingularityGame.dealerScore;
}

// Check for blackjack
function checkForBlackjack() {
    const playerBlackjack = blackjackSingularityGame.playerScore === 21 && blackjackSingularityGame.playerCards.length === 2;
    const dealerBlackjack = calculateHandValue(blackjackSingularityGame.dealerCards) === 21 && blackjackSingularityGame.dealerCards.length === 2;

    if (playerBlackjack || dealerBlackjack) {
        // Reveal dealer hole card
        revealDealerHoleCard();

        setTimeout(() => {
            if (playerBlackjack && dealerBlackjack) {
                resolveHand('push');
            } else if (playerBlackjack) {
                resolveHand('blackjack');
            } else {
                resolveHand('dealer_blackjack');
            }
        }, 1000);
    }
}

// Enable player actions
function enablePlayerActions() {
    document.getElementById('playerActions').classList.remove('hidden');

    // Enable/disable specific actions based on game state
    document.getElementById('doubleDownBtn').disabled = !blackjackSingularityGame.canDoubleDown || blackjackSingularityGame.playerCards.length !== 2;
    document.getElementById('surrenderBtn').disabled = !blackjackSingularityGame.canSurrender || blackjackSingularityGame.playerCards.length !== 2;
}

// Player action handler
function playerAction(action) {
    const aiDealer = blackjackSingularityGame.aiDealer;

    // Record player action for AI learning
    const dealerUpCard = blackjackSingularityGame.dealerCards[0];
    aiDealer.playerPatterns.push({
        action: action,
        playerScore: blackjackSingularityGame.playerScore,
        dealerUpCard: dealerUpCard.value,
        handSize: blackjackSingularityGame.playerCards.length
    });

    // Disable actions
    disablePlayerActions();

    switch (action) {
        case 'hit':
            playerHit();
            break;
        case 'stand':
            playerStand();
            break;
        case 'double':
            playerDoubleDown();
            break;
        case 'surrender':
            playerSurrender();
            break;
    }
}

// Player hits
function playerHit() {
    dealCardWithAI('player');
    calculateScores();

    if (blackjackSingularityGame.playerScore > 21) {
        // Player busted
        blackjackSingularityGame.stats.bustedHands++;
        setTimeout(() => resolveHand('bust'), 1000);
    } else if (blackjackSingularityGame.playerScore === 21) {
        // Player has 21
        setTimeout(() => playerStand(), 1000);
    } else {
        // Continue playing
        blackjackSingularityGame.canDoubleDown = false;
        blackjackSingularityGame.canSurrender = false;
        enablePlayerActions();
    }
}

// Player stands
function playerStand() {
    disablePlayerActions();
    revealDealerHoleCard();

    setTimeout(() => {
        dealerPlay();
    }, 1000);
}

// Player doubles down
function playerDoubleDown() {
    // Double the bet
    if (blackjackSingularityGame.betAmount > balance) {
        alert('Insufficient balance to double down!');
        enablePlayerActions();
        return;
    }

    balance -= blackjackSingularityGame.betAmount;
    blackjackSingularityGame.betAmount *= 2;
    updateBalance();
    updateBetDisplay();

    // Deal one more card
    dealCardWithAI('player');
    calculateScores();

    if (blackjackSingularityGame.playerScore > 21) {
        blackjackSingularityGame.stats.bustedHands++;
        setTimeout(() => resolveHand('bust'), 1000);
    } else {
        setTimeout(() => playerStand(), 1000);
    }
}

// Player surrenders
function playerSurrender() {
    resolveHand('surrender');
}

// Reveal dealer hole card
function revealDealerHoleCard() {
    if (blackjackSingularityGame.dealerHoleCard) {
        // Remove hidden card and add revealed card
        const dealerContainer = document.getElementById('dealerCards');
        dealerContainer.removeChild(dealerContainer.lastChild);
        displayCard(blackjackSingularityGame.dealerHoleCard, 'dealerCards');

        // Recalculate dealer score
        blackjackSingularityGame.dealerScore = calculateHandValue(blackjackSingularityGame.dealerCards);
        updateScoreDisplays();
    }
}

// Dealer plays with AI strategy
function dealerPlay() {
    const aiDealer = blackjackSingularityGame.aiDealer;
    const strategy = aiDealer.currentStrategy;

    // AI dealer uses adaptive strategy instead of standard rules
    let shouldHit = false;

    if (strategy === 'counter_aggressive') {
        // More aggressive dealer play
        shouldHit = blackjackSingularityGame.dealerScore < 18;
    } else if (strategy === 'exploit_conservative') {
        // Conservative dealer play
        shouldHit = blackjackSingularityGame.dealerScore < 16;
    } else if (strategy === 'anti_counting') {
        // Unpredictable dealer play
        shouldHit = blackjackSingularityGame.dealerScore < (15 + Math.floor(Math.random() * 3));
    } else {
        // Standard rules with slight bias
        shouldHit = blackjackSingularityGame.dealerScore < 17;
    }

    if (shouldHit) {
        setTimeout(() => {
            dealCardWithAI('dealer');
            calculateScores();

            if (blackjackSingularityGame.dealerScore > 21) {
                resolveHand('dealer_bust');
            } else {
                dealerPlay(); // Continue dealer play
            }
        }, 1000);
    } else {
        // Dealer stands
        setTimeout(() => resolveHand('compare'), 1000);
    }
}

// Disable player actions
function disablePlayerActions() {
    document.getElementById('hitBtn').disabled = true;
    document.getElementById('standBtn').disabled = true;
    document.getElementById('doubleDownBtn').disabled = true;
    document.getElementById('surrenderBtn').disabled = true;
}

// Resolve hand with AI-adjusted payouts
function resolveHand(result) {
    const modeData = AI_MODES[blackjackSingularityGame.aiMode];
    const adaptationData = ADAPTATION_LEVELS[blackjackSingularityGame.adaptationLevel];

    let winnings = 0;
    let resultMessage = '';

    switch (result) {
        case 'blackjack':
            winnings = Math.floor(blackjackSingularityGame.betAmount * SINGULARITY_PAYOUTS.BLACKJACK);
            resultMessage = 'Blackjack! You win!';
            blackjackSingularityGame.gameResult = 'blackjack';
            blackjackSingularityGame.stats.blackjacksHit++;
            break;

        case 'win':
        case 'dealer_bust':
            winnings = Math.floor(blackjackSingularityGame.betAmount * SINGULARITY_PAYOUTS.WIN);
            resultMessage = result === 'dealer_bust' ? 'Dealer busts! You win!' : 'You win!';
            blackjackSingularityGame.gameResult = 'win';
            break;

        case 'push':
            winnings = blackjackSingularityGame.betAmount; // Return bet
            resultMessage = 'Push - It\'s a tie!';
            blackjackSingularityGame.gameResult = 'push';
            break;

        case 'surrender':
            winnings = Math.floor(blackjackSingularityGame.betAmount * SINGULARITY_PAYOUTS.SURRENDER);
            resultMessage = 'You surrendered';
            blackjackSingularityGame.gameResult = 'surrender';
            break;

        case 'compare':
            if (blackjackSingularityGame.playerScore > blackjackSingularityGame.dealerScore) {
                winnings = Math.floor(blackjackSingularityGame.betAmount * SINGULARITY_PAYOUTS.WIN);
                resultMessage = 'You win!';
                blackjackSingularityGame.gameResult = 'win';
            } else if (blackjackSingularityGame.playerScore < blackjackSingularityGame.dealerScore) {
                winnings = 0;
                resultMessage = 'Dealer wins';
                blackjackSingularityGame.gameResult = 'lose';
            } else {
                winnings = blackjackSingularityGame.betAmount;
                resultMessage = 'Push - It\'s a tie!';
                blackjackSingularityGame.gameResult = 'push';
            }
            break;

        default: // bust, dealer_blackjack, lose
            winnings = 0;
            resultMessage = result === 'bust' ? 'You busted!' :
                           result === 'dealer_blackjack' ? 'Dealer blackjack!' : 'You lose';
            blackjackSingularityGame.gameResult = 'lose';
            break;
    }

    // Apply AI house edge reduction
    if (winnings > blackjackSingularityGame.betAmount) {
        const houseReduction = winnings * modeData.houseEdge * adaptationData.strategyComplexity;
        winnings = Math.max(blackjackSingularityGame.betAmount, winnings - houseReduction);
    }

    // Apply additional AI learning penalty
    if (blackjackSingularityGame.aiDealer.handsAnalyzed > 10) {
        const learningPenalty = winnings * (blackjackSingularityGame.aiDealer.confidenceLevel * 0.3);
        winnings = Math.max(0, winnings - learningPenalty);
    }

    // Round down winnings
    winnings = Math.floor(winnings);

    // Add winnings to balance
    balance += winnings;
    blackjackSingularityGame.totalWin = winnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(winnings >= blackjackSingularityGame.betAmount, winnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - AI confidence: ${Math.floor(blackjackSingularityGame.aiDealer.confidenceLevel * 100)}%`;

    if (winnings > 0) {
        document.getElementById('winAmount').textContent = `+$${winnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 4000);
}

// Update AI display
function updateAIDisplay() {
    const aiDealer = blackjackSingularityGame.aiDealer;

    document.getElementById('handsAnalyzed').textContent = aiDealer.handsAnalyzed;
    document.getElementById('aiConfidence').textContent = `${Math.floor(aiDealer.confidenceLevel * 100)}%`;

    // Update player pattern analysis
    if (aiDealer.handsAnalyzed > 0) {
        const tendencies = aiDealer.adaptationMatrix.playerTendencies;
        let patternDescription = '';

        if (tendencies.aggressiveness > 0.7) {
            patternDescription = 'Aggressive';
        } else if (tendencies.aggressiveness < 0.3) {
            patternDescription = 'Conservative';
        } else {
            patternDescription = 'Balanced';
        }

        if (tendencies.cardCountingSkill > 0.3) {
            patternDescription += ' (Counting?)';
        }

        document.getElementById('playerPattern').textContent = patternDescription;
    }

    document.getElementById('nextMovePrediction').textContent = aiDealer.nextMovePredict || 'Unknown';
    document.getElementById('counterStrategy').textContent = aiDealer.currentStrategy.replace('_', ' ').toUpperCase();

    // Update adaptation status
    const adaptationMessages = [
        'Analyzing behavioral patterns...',
        'Learning player tendencies...',
        'Adapting counter-strategies...',
        'Optimizing card distribution...',
        'Calculating probability matrices...',
        'Enhancing prediction algorithms...'
    ];

    const randomMessage = adaptationMessages[Math.floor(Math.random() * adaptationMessages.length)];
    document.getElementById('adaptationStatus').textContent = randomMessage;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${blackjackSingularityGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = blackjackSingularityGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${blackjackSingularityGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${blackjackSingularityGame.stats.totalWagered}`;
    document.getElementById('aiAdaptations').textContent = blackjackSingularityGame.stats.aiAdaptations;
    document.getElementById('strategiesDefeated').textContent = blackjackSingularityGame.stats.strategiesDefeated;

    const netResult = blackjackSingularityGame.stats.totalWon - blackjackSingularityGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    blackjackSingularityGame.stats.handsPlayed++;
    blackjackSingularityGame.stats.totalWagered += blackjackSingularityGame.betAmount;
    blackjackSingularityGame.stats.totalWon += winnings;

    if (won) {
        blackjackSingularityGame.stats.handsWon++;
        blackjackSingularityGame.stats.currentStreak++;
        blackjackSingularityGame.streakData.currentWinStreak++;
        blackjackSingularityGame.streakData.currentLossStreak = 0;

        if (blackjackSingularityGame.streakData.currentWinStreak > blackjackSingularityGame.streakData.longestWinStreak) {
            blackjackSingularityGame.streakData.longestWinStreak = blackjackSingularityGame.streakData.currentWinStreak;
        }

        if (winnings > blackjackSingularityGame.stats.biggestWin) {
            blackjackSingularityGame.stats.biggestWin = winnings;
        }
    } else {
        blackjackSingularityGame.stats.currentStreak = 0;
        blackjackSingularityGame.streakData.currentWinStreak = 0;
        blackjackSingularityGame.streakData.currentLossStreak++;

        if (blackjackSingularityGame.streakData.currentLossStreak > blackjackSingularityGame.streakData.longestLossStreak) {
            blackjackSingularityGame.streakData.longestLossStreak = blackjackSingularityGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to AI adaptation)
    blackjackSingularityGame.stats.winRate = (blackjackSingularityGame.stats.handsWon / blackjackSingularityGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    blackjackSingularityGame.isPlaying = false;
    blackjackSingularityGame.betAmount = 0;
    blackjackSingularityGame.playerCards = [];
    blackjackSingularityGame.dealerCards = [];
    blackjackSingularityGame.playerScore = 0;
    blackjackSingularityGame.dealerScore = 0;
    blackjackSingularityGame.gameResult = '';
    blackjackSingularityGame.totalWin = 0;
    blackjackSingularityGame.dealerHoleCard = null;
    blackjackSingularityGame.canDoubleDown = false;
    blackjackSingularityGame.canSplit = false;
    blackjackSingularityGame.canSurrender = false;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('dealerCards').innerHTML = '';
    document.getElementById('playerScore').textContent = '0';
    document.getElementById('dealerScore').textContent = '0';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('aiAdaptationAlert').classList.add('hidden');

    // Reset action buttons
    document.getElementById('hitBtn').disabled = false;
    document.getElementById('standBtn').disabled = false;
    document.getElementById('doubleDownBtn').disabled = false;
    document.getElementById('surrenderBtn').disabled = false;
    document.getElementById('playerActions').classList.add('hidden');

    // Reset bet display
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'AI analyzing optimal strategy...';
    document.getElementById('gameMessage').textContent = 'Welcome to Blackjack Singularity - Where AI Learns Your Every Move';

    // Reinitialize deck for next hand (AI continues learning)
    initializeDeck();

    // Update AI display
    updateAIDisplay();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBlackjackSingularityGame();
});