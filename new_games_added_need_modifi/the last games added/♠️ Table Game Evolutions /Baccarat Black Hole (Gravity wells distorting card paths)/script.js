// Baccarat Black Hole - Gravity Wells Distorting Card Paths
// Ultra High House Edge Implementation with Quantum Physics Theme
// Designed to maintain <7% player win rate

// Game state
let balance = 1000;

// Game state object with extreme gravitational bias
let baccaratBlackHoleGame = {
    isPlaying: false,
    gameMode: 'singularity', // singularity, event_horizon, wormhole, multiverse
    gravityLevel: 'normal', // normal, intense, extreme, quantum, void
    playerBet: 0,
    bankerBet: 0,
    tieBet: 0,
    gravityWellBet: 0, // Special black hole feature
    dimensionalRiftBet: 0, // Special quantum feature
    totalBet: 0,
    playerCards: [],
    bankerCards: [],
    playerScore: 0,
    bankerScore: 0,
    gameResult: '',
    totalWin: 0,
    deck: [],
    gravityWells: [], // Active gravity wells affecting card distribution
    quantumField: false, // Quantum field distortion active
    dimensionalRift: false, // Dimensional rift affecting outcomes
    blackHoleActive: false, // Black hole consuming winning bets
    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        gravityWellsTriggered: 0,
        quantumEventsTriggered: 0,
        blackHoleConsumptions: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game modes with extreme gravitational house bias
const BLACK_HOLE_MODES = {
    singularity: {
        name: 'Singularity Core',
        houseEdge: 0.58, // 58% house edge
        gravityDistortion: 0.42, // 42% gravity distortion
        payoutMultiplier: 0.38, // Severely reduced payouts
        blackHoleChance: 0.35, // 35% chance black hole consumes wins
        quantumInterference: 0.25 // 25% quantum interference
    },
    event_horizon: {
        name: 'Event Horizon',
        houseEdge: 0.65, // 65% house edge
        gravityDistortion: 0.52, // 52% gravity distortion
        payoutMultiplier: 0.32, // Even more reduced payouts
        blackHoleChance: 0.45, // 45% chance black hole consumes wins
        quantumInterference: 0.35 // 35% quantum interference
    },
    wormhole: {
        name: 'Wormhole Transit',
        houseEdge: 0.72, // 72% house edge
        gravityDistortion: 0.62, // 62% gravity distortion
        payoutMultiplier: 0.25, // Extremely reduced payouts
        blackHoleChance: 0.55, // 55% chance black hole consumes wins
        quantumInterference: 0.45 // 45% quantum interference
    },
    multiverse: {
        name: 'Multiverse Collapse',
        houseEdge: 0.78, // 78% house edge
        gravityDistortion: 0.72, // 72% gravity distortion
        payoutMultiplier: 0.18, // Brutally reduced payouts
        blackHoleChance: 0.68, // 68% chance black hole consumes wins
        quantumInterference: 0.58 // 58% quantum interference
    }
};

const GRAVITY_LEVELS = {
    normal: {
        name: 'Normal Gravity',
        cardBias: 0.28, // 28% bias toward house-favorable cards
        scoringBias: 0.22, // 22% scoring bias
        payoutPenalty: 0.15, // 15% additional payout reduction
        wellIntensity: 0.20 // 20% gravity well intensity
    },
    intense: {
        name: 'Intense Gravity',
        cardBias: 0.38, // 38% bias toward house-favorable cards
        scoringBias: 0.32, // 32% scoring bias
        payoutPenalty: 0.25, // 25% additional payout reduction
        wellIntensity: 0.35 // 35% gravity well intensity
    },
    extreme: {
        name: 'Extreme Gravity',
        cardBias: 0.48, // 48% bias toward house-favorable cards
        scoringBias: 0.42, // 42% scoring bias
        payoutPenalty: 0.35, // 35% additional payout reduction
        wellIntensity: 0.50 // 50% gravity well intensity
    },
    quantum: {
        name: 'Quantum Gravity',
        cardBias: 0.62, // 62% bias toward house-favorable cards
        scoringBias: 0.55, // 55% scoring bias
        payoutPenalty: 0.48, // 48% additional payout reduction
        wellIntensity: 0.68 // 68% gravity well intensity
    },
    void: {
        name: 'Void Singularity',
        cardBias: 0.75, // 75% bias toward house-favorable cards
        scoringBias: 0.68, // 68% scoring bias
        payoutPenalty: 0.62, // 62% additional payout reduction
        wellIntensity: 0.85 // 85% gravity well intensity
    }
};

// Severely reduced payout table with gravitational effects
const BLACK_HOLE_PAYOUTS = {
    // Standard bets (heavily reduced)
    PLAYER_WIN: 0.75, // Reduced from 1:1
    BANKER_WIN: 0.70, // Reduced from 0.95:1 (after commission)
    TIE_WIN: 6, // Reduced from 8:1

    // Gravity well bets (terrible odds)
    GRAVITY_WELL_MINOR: 2, // Minor gravitational anomaly
    GRAVITY_WELL_MAJOR: 8, // Major gravitational distortion
    GRAVITY_WELL_SINGULARITY: 25, // Singularity formation (extremely rare)

    // Dimensional rift bets (almost never pay)
    DIMENSIONAL_RIFT_STABLE: 5, // Stable rift
    DIMENSIONAL_RIFT_UNSTABLE: 15, // Unstable rift
    DIMENSIONAL_RIFT_COLLAPSE: 50, // Rift collapse (virtually impossible)

    // Black hole consumption (negative payouts)
    BLACK_HOLE_PARTIAL: -0.5, // Partial consumption
    BLACK_HOLE_COMPLETE: -1.0, // Complete consumption
    BLACK_HOLE_SUPERMASSIVE: -2.0 // Supermassive consumption
};

// Card values for baccarat
const CARD_VALUES = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 0, 'J': 0, 'Q': 0, 'K': 0
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadBaccaratBlackHoleGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">GRAVITATIONAL CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BLACK HOLE MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="singularity">Singularity Core</option>
                            <option value="event_horizon">Event Horizon</option>
                            <option value="wormhole">Wormhole Transit</option>
                            <option value="multiverse">Multiverse Collapse</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GRAVITY LEVEL</label>
                        <select id="gravityLevel" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Normal Gravity</option>
                            <option value="intense" selected>Intense Gravity</option>
                            <option value="extreme">Extreme Gravity</option>
                            <option value="quantum">Quantum Gravity</option>
                            <option value="void">Void Singularity</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PLAYER BET</label>
                        <input type="number" id="playerBet" value="0" min="0" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BANKER BET</label>
                        <input type="number" id="bankerBet" value="0" min="0" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">TIE BET</label>
                        <input type="number" id="tieBet" value="0" min="0" max="${Math.min(balance, 200)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GRAVITY WELL BET</label>
                        <input type="number" id="gravityWellBet" value="0" min="0" max="${Math.min(balance, 100)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIMENSIONAL RIFT BET</label>
                        <input type="number" id="dimensionalRiftBet" value="0" min="0" max="${Math.min(balance, 50)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        INITIATE GRAVITATIONAL FIELD
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Gravity Wells Active</div>
                        <div id="gravityWellsActive" class="text-lg font-bold text-red-400">0</div>
                    </div>
                </div>

                <!-- Gravitational Effects Display -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">GRAVITATIONAL EFFECTS</h5>
                    <div class="text-xs space-y-2">
                        <div id="blackHoleStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">BLACK HOLE: DORMANT</div>
                        </div>
                        <div id="quantumFieldStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">QUANTUM FIELD: STABLE</div>
                        </div>
                        <div id="dimensionalRiftStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">DIMENSIONAL RIFT: CLOSED</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">GRAVITATIONAL PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Standard Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Player Win:</span>
                            <span class="text-red-400">0.75:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Banker Win:</span>
                            <span class="text-red-400">0.70:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tie:</span>
                            <span class="text-red-400">6:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Gravity Wells:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Minor Anomaly:</span>
                            <span class="text-red-400">2:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Major Distortion:</span>
                            <span class="text-red-400">8:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Singularity:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Black holes may consume winnings</div>
                        <div class="text-xs text-red-400">*Gravity distorts all outcomes</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="baccaratTable" class="relative bg-gradient-to-br from-black via-purple-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Black Hole Visual Effect -->
                        <div id="blackHoleEffect" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full bg-gradient-radial from-transparent via-purple-500/20 to-black border-2 border-purple-400 hidden animate-pulse">
                            <div class="absolute inset-4 rounded-full bg-black border border-red-500 animate-spin">
                                <div class="absolute inset-2 rounded-full bg-gradient-radial from-red-500/50 to-transparent"></div>
                            </div>
                        </div>

                        <!-- Gravity Wells -->
                        <div id="gravityWells" class="absolute inset-0 pointer-events-none">
                            <!-- Gravity wells will be dynamically generated here -->
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-8">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">PLAYER</div>
                                <div id="playerCards" class="flex space-x-2 mb-2">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div class="text-lg font-bold text-blue-400">
                                    Score: <span id="playerScore">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Banker Area -->
                        <div class="absolute bottom-4 right-8">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">BANKER</div>
                                <div id="bankerCards" class="flex space-x-2 mb-2">
                                    <!-- Banker cards will appear here -->
                                </div>
                                <div class="text-lg font-bold text-red-400">
                                    Score: <span id="bankerScore">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-8 left-1/2 transform -translate-x-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Place your bets in the gravitational field</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="blackHoleConsumption" class="text-lg font-bold text-red-400 hidden">BLACK HOLE CONSUMED: -$0</div>
                        </div>

                        <!-- Betting Areas Visual -->
                        <div class="absolute top-16 left-4">
                            <div class="text-center p-2 bg-blue-500/20 rounded border border-blue-400">
                                <div class="text-xs text-blue-400">PLAYER</div>
                                <div id="playerBetDisplay" class="text-sm font-bold text-blue-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute top-16 right-4">
                            <div class="text-center p-2 bg-red-500/20 rounded border border-red-400">
                                <div class="text-xs text-red-400">BANKER</div>
                                <div id="bankerBetDisplay" class="text-sm font-bold text-red-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute top-32 left-1/2 transform -translate-x-1/2">
                            <div class="text-center p-2 bg-yellow-500/20 rounded border border-yellow-400">
                                <div class="text-xs text-yellow-400">TIE</div>
                                <div id="tieBetDisplay" class="text-sm font-bold text-yellow-400">$0</div>
                            </div>
                        </div>

                        <!-- Quantum Effects -->
                        <div id="quantumEffects" class="absolute inset-0 pointer-events-none">
                            <!-- Quantum particle effects will be generated here -->
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Baccarat Black Hole - Where Gravity Distorts Reality</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Gravity Wells</div>
                <div id="gravityWellsTriggered" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Black Hole Events</div>
                <div id="blackHoleEvents" class="text-xl font-bold text-red-400">0</div>
            </div>
        </div>
    `;

    initializeBaccaratBlackHole();
}

// Initialize the game
function initializeBaccaratBlackHole() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);

    // Initialize deck and gravitational field
    initializeDeck();
    initializeGravitationalField();
    updateGameStats();
}

// Initialize deck with gravitational bias
function initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

    baccaratBlackHoleGame.deck = [];

    // Create deck with gravitational distortion
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    for (const suit of suits) {
        for (const rank of ranks) {
            const card = {
                rank,
                suit,
                value: CARD_VALUES[rank],
                id: `${rank}${suit}`,
                gravitationalCharge: Math.random() // Random gravitational charge
            };

            baccaratBlackHoleGame.deck.push(card);

            // Add extra copies of house-favorable cards (low values for player, high for banker)
            if (Math.random() < gravityData.cardBias) {
                // Bias toward cards that favor the house
                if (card.value <= 3 || card.value === 0) { // Low cards and face cards
                    const extraCopies = Math.floor(gravityData.cardBias * 8);
                    for (let i = 0; i < extraCopies; i++) {
                        baccaratBlackHoleGame.deck.push({
                            ...card,
                            id: `${rank}${suit}_gravity_${i}`,
                            gravitationalCharge: Math.random() + 0.5 // Higher gravitational charge
                        });
                    }
                }
            }
        }
    }

    shuffleDeckWithGravity();
}

// Shuffle deck with gravitational effects
function shuffleDeckWithGravity() {
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    // Standard shuffle
    for (let i = baccaratBlackHoleGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [baccaratBlackHoleGame.deck[i], baccaratBlackHoleGame.deck[j]] = [baccaratBlackHoleGame.deck[j], baccaratBlackHoleGame.deck[i]];
    }

    // Apply gravitational sorting - cards with higher gravitational charge sink to bottom
    if (Math.random() < gravityData.wellIntensity) {
        baccaratBlackHoleGame.deck.sort((a, b) => {
            // Higher gravitational charge cards are more likely to be drawn
            return b.gravitationalCharge - a.gravitationalCharge;
        });
    }
}

// Initialize gravitational field
function initializeGravitationalField() {
    baccaratBlackHoleGame.gravityWells = [];
    baccaratBlackHoleGame.quantumField = false;
    baccaratBlackHoleGame.dimensionalRift = false;
    baccaratBlackHoleGame.blackHoleActive = false;

    updateGravitationalEffects();
}

// Deal new hand with gravitational effects
function dealNewHand() {
    const playerBet = parseInt(document.getElementById('playerBet').value) || 0;
    const bankerBet = parseInt(document.getElementById('bankerBet').value) || 0;
    const tieBet = parseInt(document.getElementById('tieBet').value) || 0;
    const gravityWellBet = parseInt(document.getElementById('gravityWellBet').value) || 0;
    const dimensionalRiftBet = parseInt(document.getElementById('dimensionalRiftBet').value) || 0;

    const totalBets = playerBet + bankerBet + tieBet + gravityWellBet + dimensionalRiftBet;

    if (totalBets === 0) {
        alert('Please place at least one bet!');
        return;
    }

    if (totalBets > balance) {
        alert('Insufficient balance!');
        return;
    }

    // Deduct all bets
    balance -= totalBets;
    updateBalance();

    // Set game state
    baccaratBlackHoleGame.isPlaying = true;
    baccaratBlackHoleGame.playerBet = playerBet;
    baccaratBlackHoleGame.bankerBet = bankerBet;
    baccaratBlackHoleGame.tieBet = tieBet;
    baccaratBlackHoleGame.gravityWellBet = gravityWellBet;
    baccaratBlackHoleGame.dimensionalRiftBet = dimensionalRiftBet;
    baccaratBlackHoleGame.totalBet = totalBets;
    baccaratBlackHoleGame.gameMode = document.getElementById('gameMode').value;
    baccaratBlackHoleGame.gravityLevel = document.getElementById('gravityLevel').value;

    // Clear previous cards
    baccaratBlackHoleGame.playerCards = [];
    baccaratBlackHoleGame.bankerCards = [];
    baccaratBlackHoleGame.playerScore = 0;
    baccaratBlackHoleGame.bankerScore = 0;

    // Activate gravitational effects
    activateGravitationalEffects();

    // Deal cards with gravitational distortion
    dealCardsWithGravity();

    // Update display
    updateBetDisplay();
    updateGameStats();

    document.getElementById('dealCards').disabled = true;
    document.getElementById('gameStatus').textContent = 'Gravitational field active - Cards being distorted...';
    document.getElementById('gameMessage').textContent = 'Gravity wells are bending spacetime around the cards...';

    // Resolve hand after animation
    setTimeout(() => resolveHandWithGravity(), 3000);
}

// Activate gravitational effects
function activateGravitationalEffects() {
    const modeData = BLACK_HOLE_MODES[baccaratBlackHoleGame.gameMode];
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    // Generate gravity wells
    generateGravityWells();

    // Activate quantum field
    if (Math.random() < modeData.quantumInterference) {
        baccaratBlackHoleGame.quantumField = true;
        baccaratBlackHoleGame.stats.quantumEventsTriggered++;
        document.getElementById('quantumFieldStatus').innerHTML =
            '<div class="text-blue-400 font-bold animate-pulse">QUANTUM FIELD: ACTIVE</div>';
    }

    // Open dimensional rift
    if (Math.random() < gravityData.wellIntensity * 0.3) {
        baccaratBlackHoleGame.dimensionalRift = true;
        document.getElementById('dimensionalRiftStatus').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">DIMENSIONAL RIFT: OPEN</div>';
    }

    // Activate black hole
    if (Math.random() < modeData.blackHoleChance * 0.5) {
        baccaratBlackHoleGame.blackHoleActive = true;
        document.getElementById('blackHoleEffect').classList.remove('hidden');
        document.getElementById('blackHoleStatus').innerHTML =
            '<div class="text-red-400 font-bold animate-pulse">BLACK HOLE: ACTIVE</div>';
    }
}

// Generate gravity wells
function generateGravityWells() {
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];
    const wellCount = Math.floor(Math.random() * 5) + 1;

    baccaratBlackHoleGame.gravityWells = [];

    for (let i = 0; i < wellCount; i++) {
        const well = {
            x: Math.random() * 100,
            y: Math.random() * 100,
            intensity: Math.random() * gravityData.wellIntensity,
            type: Math.random() < 0.7 ? 'attractive' : 'repulsive'
        };
        baccaratBlackHoleGame.gravityWells.push(well);
    }

    baccaratBlackHoleGame.stats.gravityWellsTriggered += wellCount;
    document.getElementById('gravityWellsActive').textContent = wellCount;

    // Visual representation of gravity wells
    displayGravityWells();
}

// Display gravity wells visually
function displayGravityWells() {
    const container = document.getElementById('gravityWells');
    container.innerHTML = '';

    baccaratBlackHoleGame.gravityWells.forEach((well, index) => {
        const wellElement = document.createElement('div');
        wellElement.className = `absolute w-8 h-8 rounded-full border-2 ${well.type === 'attractive' ? 'border-red-400 bg-red-500/20' : 'border-blue-400 bg-blue-500/20'} animate-pulse`;
        wellElement.style.left = `${well.x}%`;
        wellElement.style.top = `${well.y}%`;
        wellElement.style.transform = 'translate(-50%, -50%)';
        container.appendChild(wellElement);
    });
}

// Deal cards with gravitational distortion
function dealCardsWithGravity() {
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    // Deal initial two cards to player and banker
    setTimeout(() => dealCardWithGravity('player'), 500);
    setTimeout(() => dealCardWithGravity('banker'), 1000);
    setTimeout(() => dealCardWithGravity('player'), 1500);
    setTimeout(() => dealCardWithGravity('banker'), 2000);

    // Check for third card rules with gravitational bias
    setTimeout(() => {
        applyThirdCardRulesWithGravity();
        updateScoreDisplays();
    }, 2500);
}

// Deal a single card with gravitational effects
function dealCardWithGravity(recipient) {
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];
    let card;

    // Apply gravitational bias to card selection
    if (Math.random() < gravityData.cardBias) {
        // Gravity pulls specific cards based on recipient and current game state
        card = selectGravitationallyBiasedCard(recipient);
    } else {
        card = baccaratBlackHoleGame.deck.pop();
    }

    if (!card) {
        // Fallback if deck is empty
        initializeDeck();
        card = baccaratBlackHoleGame.deck.pop();
    }

    // Apply gravitational distortion to card value
    if (baccaratBlackHoleGame.quantumField) {
        card = applyQuantumDistortion(card);
    }

    if (recipient === 'player') {
        baccaratBlackHoleGame.playerCards.push(card);
        displayCard(card, 'playerCards');
    } else {
        baccaratBlackHoleGame.bankerCards.push(card);
        displayCard(card, 'bankerCards');
    }

    calculateScores();
}

// Select gravitationally biased card
function selectGravitationallyBiasedCard(recipient) {
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];
    const modeData = BLACK_HOLE_MODES[baccaratBlackHoleGame.gameMode];

    // Filter cards based on gravitational preference
    let preferredCards;

    if (recipient === 'player') {
        // Gravity pulls low-value cards toward player (bad for player)
        preferredCards = baccaratBlackHoleGame.deck.filter(card =>
            card.value <= 3 || (card.value === 0 && Math.random() < gravityData.cardBias)
        );
    } else {
        // Gravity pulls high-value cards toward banker (good for banker)
        preferredCards = baccaratBlackHoleGame.deck.filter(card =>
            card.value >= 6 || (card.value === 0 && Math.random() < gravityData.cardBias)
        );
    }

    if (preferredCards.length === 0) {
        return baccaratBlackHoleGame.deck.pop();
    }

    const selectedCard = preferredCards[Math.floor(Math.random() * preferredCards.length)];

    // Remove from deck
    const index = baccaratBlackHoleGame.deck.findIndex(card => card.id === selectedCard.id);
    if (index !== -1) {
        baccaratBlackHoleGame.deck.splice(index, 1);
    }

    return selectedCard;
}

// Apply quantum distortion to card
function applyQuantumDistortion(card) {
    const modeData = BLACK_HOLE_MODES[baccaratBlackHoleGame.gameMode];

    // Quantum field can alter card values (usually unfavorably for player)
    if (Math.random() < modeData.quantumInterference) {
        const distortedCard = {...card};

        // Quantum distortion tends to make player cards worse
        if (Math.random() < 0.7) { // 70% chance of negative distortion
            distortedCard.value = Math.max(0, distortedCard.value - 1);
        } else { // 30% chance of positive distortion
            distortedCard.value = Math.min(9, distortedCard.value + 1);
        }

        distortedCard.quantumDistorted = true;
        return distortedCard;
    }

    return card;
}

// Display card with gravitational effects
function displayCard(card, containerId) {
    const container = document.getElementById(containerId);
    const cardElement = document.createElement('div');

    cardElement.className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold shadow-lg transform transition-all duration-500';

    // Add gravitational effects
    if (card.gravitationalCharge > 0.7) {
        cardElement.classList.add('ring-2', 'ring-red-400', 'animate-pulse');
    }

    if (card.quantumDistorted) {
        cardElement.classList.add('ring-2', 'ring-blue-400', 'animate-bounce');
    }

    const isRed = card.suit === '♥' || card.suit === '♦';
    cardElement.style.color = isRed ? '#dc2626' : '#000';

    cardElement.innerHTML = `
        <div class="text-xs">${card.rank}</div>
        <div class="text-lg">${card.suit}</div>
        <div class="text-xs transform rotate-180">${card.rank}</div>
    `;

    // Gravitational animation
    cardElement.style.transform = 'translateY(-20px) rotate(5deg)';
    setTimeout(() => {
        cardElement.style.transform = 'translateY(0) rotate(0deg)';
    }, 100);

    container.appendChild(cardElement);
}

// Apply third card rules with gravitational bias
function applyThirdCardRulesWithGravity() {
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    // Standard baccarat third card rules, but with gravitational bias
    const playerTotal = baccaratBlackHoleGame.playerScore;
    const bankerTotal = baccaratBlackHoleGame.bankerScore;

    let playerGetsThird = false;
    let bankerGetsThird = false;

    // Player third card rules (biased against player)
    if (playerTotal <= 5) {
        playerGetsThird = true;

        // Gravitational bias - sometimes deny player third card when they need it
        if (Math.random() < gravityData.scoringBias && playerTotal <= 3) {
            playerGetsThird = false; // Gravity prevents beneficial third card
        }
    }

    // Deal player third card if needed
    if (playerGetsThird) {
        setTimeout(() => dealCardWithGravity('player'), 500);
    }

    // Banker third card rules (biased toward banker)
    setTimeout(() => {
        const finalPlayerTotal = baccaratBlackHoleGame.playerScore;

        if (bankerTotal <= 5) {
            bankerGetsThird = true;

            // Gravitational bias - sometimes give banker third card when beneficial
            if (Math.random() < gravityData.scoringBias && bankerTotal >= 4) {
                bankerGetsThird = true; // Gravity provides beneficial third card
            }
        }

        if (bankerGetsThird) {
            setTimeout(() => dealCardWithGravity('banker'), 500);
        }
    }, playerGetsThird ? 1000 : 100);
}

// Calculate scores with gravitational effects
function calculateScores() {
    // Player score
    baccaratBlackHoleGame.playerScore = baccaratBlackHoleGame.playerCards.reduce((sum, card) => {
        return (sum + card.value) % 10;
    }, 0);

    // Banker score
    baccaratBlackHoleGame.bankerScore = baccaratBlackHoleGame.bankerCards.reduce((sum, card) => {
        return (sum + card.value) % 10;
    }, 0);

    // Apply gravitational scoring bias
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    if (Math.random() < gravityData.scoringBias) {
        // Gravity can slightly alter scores in favor of the house
        if (baccaratBlackHoleGame.playerScore > baccaratBlackHoleGame.bankerScore) {
            // Reduce player advantage
            baccaratBlackHoleGame.playerScore = Math.max(0, baccaratBlackHoleGame.playerScore - 1);
        } else if (baccaratBlackHoleGame.bankerScore > baccaratBlackHoleGame.playerScore) {
            // Increase banker advantage
            baccaratBlackHoleGame.bankerScore = Math.min(9, baccaratBlackHoleGame.bankerScore + 1);
        }
    }
}

// Update score displays
function updateScoreDisplays() {
    document.getElementById('playerScore').textContent = baccaratBlackHoleGame.playerScore;
    document.getElementById('bankerScore').textContent = baccaratBlackHoleGame.bankerScore;
}

// Resolve hand with gravitational effects
function resolveHandWithGravity() {
    const modeData = BLACK_HOLE_MODES[baccaratBlackHoleGame.gameMode];
    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Determine winner
    const playerScore = baccaratBlackHoleGame.playerScore;
    const bankerScore = baccaratBlackHoleGame.bankerScore;

    let winner;
    if (playerScore > bankerScore) {
        winner = 'player';
        baccaratBlackHoleGame.gameResult = 'player_win';
    } else if (bankerScore > playerScore) {
        winner = 'banker';
        baccaratBlackHoleGame.gameResult = 'banker_win';
    } else {
        winner = 'tie';
        baccaratBlackHoleGame.gameResult = 'tie';
    }

    // Calculate standard bet winnings
    if (winner === 'player' && baccaratBlackHoleGame.playerBet > 0) {
        const winAmount = Math.floor(baccaratBlackHoleGame.playerBet * BLACK_HOLE_PAYOUTS.PLAYER_WIN);
        totalWinnings += winAmount;
        resultMessage += `Player wins: +$${winAmount} `;
    }

    if (winner === 'banker' && baccaratBlackHoleGame.bankerBet > 0) {
        const winAmount = Math.floor(baccaratBlackHoleGame.bankerBet * BLACK_HOLE_PAYOUTS.BANKER_WIN);
        totalWinnings += winAmount;
        resultMessage += `Banker wins: +$${winAmount} `;
    }

    if (winner === 'tie' && baccaratBlackHoleGame.tieBet > 0) {
        const winAmount = Math.floor(baccaratBlackHoleGame.tieBet * BLACK_HOLE_PAYOUTS.TIE_WIN);
        totalWinnings += winAmount;
        resultMessage += `Tie wins: +$${winAmount} `;
    }

    // Calculate side bet winnings (terrible odds)
    totalWinnings += calculateGravityWellWinnings();
    totalWinnings += calculateDimensionalRiftWinnings();

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier * (1 - gravityData.payoutPenalty));

    // Black hole consumption (can consume winnings)
    const blackHoleConsumption = applyBlackHoleConsumption(totalWinnings);
    totalWinnings -= blackHoleConsumption;

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    baccaratBlackHoleGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > baccaratBlackHoleGame.totalBet, totalWinnings);

    document.getElementById('gameStatus').textContent = `${winner.toUpperCase()} WINS!`;
    document.getElementById('gameMessage').innerHTML = resultMessage || `${winner} wins the hand`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    if (blackHoleConsumption > 0) {
        document.getElementById('blackHoleConsumption').textContent = `BLACK HOLE CONSUMED: -$${blackHoleConsumption}`;
        document.getElementById('blackHoleConsumption').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 5000);
}

// Calculate gravity well winnings (extremely rare)
function calculateGravityWellWinnings() {
    if (baccaratBlackHoleGame.gravityWellBet === 0) return 0;

    const gravityData = GRAVITY_LEVELS[baccaratBlackHoleGame.gravityLevel];
    let winnings = 0;

    // Check for gravity well events (very rare)
    const wellIntensity = baccaratBlackHoleGame.gravityWells.reduce((sum, well) => sum + well.intensity, 0);

    if (wellIntensity > 2.5 && Math.random() < 0.01) { // 1% chance for major distortion
        winnings = Math.floor(baccaratBlackHoleGame.gravityWellBet * BLACK_HOLE_PAYOUTS.GRAVITY_WELL_MAJOR);
    } else if (wellIntensity > 1.5 && Math.random() < 0.05) { // 5% chance for minor anomaly
        winnings = Math.floor(baccaratBlackHoleGame.gravityWellBet * BLACK_HOLE_PAYOUTS.GRAVITY_WELL_MINOR);
    } else if (wellIntensity > 3.0 && Math.random() < 0.001) { // 0.1% chance for singularity
        winnings = Math.floor(baccaratBlackHoleGame.gravityWellBet * BLACK_HOLE_PAYOUTS.GRAVITY_WELL_SINGULARITY);
    }

    return winnings;
}

// Calculate dimensional rift winnings (almost never pay)
function calculateDimensionalRiftWinnings() {
    if (baccaratBlackHoleGame.dimensionalRiftBet === 0) return 0;

    let winnings = 0;

    if (baccaratBlackHoleGame.dimensionalRift) {
        // Even with rift open, very low chance of payout
        if (Math.random() < 0.02) { // 2% chance for stable rift
            winnings = Math.floor(baccaratBlackHoleGame.dimensionalRiftBet * BLACK_HOLE_PAYOUTS.DIMENSIONAL_RIFT_STABLE);
        } else if (Math.random() < 0.005) { // 0.5% chance for unstable rift
            winnings = Math.floor(baccaratBlackHoleGame.dimensionalRiftBet * BLACK_HOLE_PAYOUTS.DIMENSIONAL_RIFT_UNSTABLE);
        } else if (Math.random() < 0.0001) { // 0.01% chance for rift collapse
            winnings = Math.floor(baccaratBlackHoleGame.dimensionalRiftBet * BLACK_HOLE_PAYOUTS.DIMENSIONAL_RIFT_COLLAPSE);
        }
    }

    return winnings;
}

// Apply black hole consumption
function applyBlackHoleConsumption(winnings) {
    if (!baccaratBlackHoleGame.blackHoleActive || winnings === 0) return 0;

    const modeData = BLACK_HOLE_MODES[baccaratBlackHoleGame.gameMode];
    let consumption = 0;

    if (Math.random() < modeData.blackHoleChance) {
        // Black hole consumes part or all of winnings
        const consumptionType = Math.random();

        if (consumptionType < 0.6) { // 60% chance of partial consumption
            consumption = Math.floor(winnings * Math.abs(BLACK_HOLE_PAYOUTS.BLACK_HOLE_PARTIAL));
        } else if (consumptionType < 0.9) { // 30% chance of complete consumption
            consumption = Math.floor(winnings * Math.abs(BLACK_HOLE_PAYOUTS.BLACK_HOLE_COMPLETE));
        } else { // 10% chance of supermassive consumption
            consumption = Math.floor(winnings * Math.abs(BLACK_HOLE_PAYOUTS.BLACK_HOLE_SUPERMASSIVE));
        }

        baccaratBlackHoleGame.stats.blackHoleConsumptions++;
    }

    return Math.min(consumption, winnings);
}

// Update gravitational effects display
function updateGravitationalEffects() {
    // This function updates the visual representation of gravitational effects
    // Implementation would include particle effects, field distortions, etc.
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('playerBetDisplay').textContent = `$${baccaratBlackHoleGame.playerBet}`;
    document.getElementById('bankerBetDisplay').textContent = `$${baccaratBlackHoleGame.bankerBet}`;
    document.getElementById('tieBetDisplay').textContent = `$${baccaratBlackHoleGame.tieBet}`;
    document.getElementById('totalBetDisplay').textContent = `$${baccaratBlackHoleGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = baccaratBlackHoleGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${baccaratBlackHoleGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${baccaratBlackHoleGame.stats.totalWagered}`;
    document.getElementById('gravityWellsTriggered').textContent = baccaratBlackHoleGame.stats.gravityWellsTriggered;
    document.getElementById('blackHoleEvents').textContent = baccaratBlackHoleGame.stats.blackHoleConsumptions;

    const netResult = baccaratBlackHoleGame.stats.totalWon - baccaratBlackHoleGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    baccaratBlackHoleGame.stats.handsPlayed++;
    baccaratBlackHoleGame.stats.totalWagered += baccaratBlackHoleGame.totalBet;
    baccaratBlackHoleGame.stats.totalWon += winnings;

    if (won) {
        baccaratBlackHoleGame.stats.handsWon++;
        baccaratBlackHoleGame.stats.currentStreak++;
        baccaratBlackHoleGame.streakData.currentWinStreak++;
        baccaratBlackHoleGame.streakData.currentLossStreak = 0;

        if (baccaratBlackHoleGame.streakData.currentWinStreak > baccaratBlackHoleGame.streakData.longestWinStreak) {
            baccaratBlackHoleGame.streakData.longestWinStreak = baccaratBlackHoleGame.streakData.currentWinStreak;
        }

        if (winnings > baccaratBlackHoleGame.stats.biggestWin) {
            baccaratBlackHoleGame.stats.biggestWin = winnings;
        }
    } else {
        baccaratBlackHoleGame.stats.currentStreak = 0;
        baccaratBlackHoleGame.streakData.currentWinStreak = 0;
        baccaratBlackHoleGame.streakData.currentLossStreak++;

        if (baccaratBlackHoleGame.streakData.currentLossStreak > baccaratBlackHoleGame.streakData.longestLossStreak) {
            baccaratBlackHoleGame.streakData.longestLossStreak = baccaratBlackHoleGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to gravitational effects)
    baccaratBlackHoleGame.stats.winRate = (baccaratBlackHoleGame.stats.handsWon / baccaratBlackHoleGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    baccaratBlackHoleGame.isPlaying = false;
    baccaratBlackHoleGame.playerBet = 0;
    baccaratBlackHoleGame.bankerBet = 0;
    baccaratBlackHoleGame.tieBet = 0;
    baccaratBlackHoleGame.gravityWellBet = 0;
    baccaratBlackHoleGame.dimensionalRiftBet = 0;
    baccaratBlackHoleGame.totalBet = 0;
    baccaratBlackHoleGame.playerCards = [];
    baccaratBlackHoleGame.bankerCards = [];
    baccaratBlackHoleGame.playerScore = 0;
    baccaratBlackHoleGame.bankerScore = 0;
    baccaratBlackHoleGame.gameResult = '';
    baccaratBlackHoleGame.totalWin = 0;
    baccaratBlackHoleGame.quantumField = false;
    baccaratBlackHoleGame.dimensionalRift = false;
    baccaratBlackHoleGame.blackHoleActive = false;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('bankerCards').innerHTML = '';
    document.getElementById('playerScore').textContent = '0';
    document.getElementById('bankerScore').textContent = '0';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('blackHoleConsumption').classList.add('hidden');
    document.getElementById('gravityWells').innerHTML = '';
    document.getElementById('blackHoleEffect').classList.add('hidden');

    // Reset gravitational effects
    document.getElementById('blackHoleStatus').innerHTML =
        '<div class="text-red-400 font-bold">BLACK HOLE: DORMANT</div>';
    document.getElementById('quantumFieldStatus').innerHTML =
        '<div class="text-blue-400 font-bold">QUANTUM FIELD: STABLE</div>';
    document.getElementById('dimensionalRiftStatus').innerHTML =
        '<div class="text-purple-400 font-bold">DIMENSIONAL RIFT: CLOSED</div>';
    document.getElementById('gravityWellsActive').textContent = '0';

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Place your bets in the gravitational field';
    document.getElementById('gameMessage').textContent = 'Welcome to Baccarat Black Hole - Where Gravity Distorts Reality';

    // Reinitialize gravitational field for next hand
    initializeGravitationalField();
    initializeDeck();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBaccaratBlackHoleGame();
});