// Craps Quantum Field - Holographic Dice with Physics-Altering Bets
// Ultra High House Edge Implementation with Quantum Physics Manipulation
// Designed to maintain <5% player win rate

// Game state
let balance = 1000;

// Game state object with quantum field manipulation
let crapsQuantumGame = {
    isPlaying: false,
    gamePhase: 'come_out', // come_out, point
    quantumMode: 'standard', // standard, entangled, superposition, multiverse
    fieldIntensity: 'normal', // normal, intense, extreme, quantum, singularity
    point: null,
    rollCount: 0,

    // Betting areas
    bets: {
        passLine: 0,
        dontPass: 0,
        come: 0,
        dontCome: 0,
        field: 0,
        place: {
            4: 0, 5: 0, 6: 0, 8: 0, 9: 0, 10: 0
        },
        hardways: {
            4: 0, 6: 0, 8: 0, 10: 0
        },
        proposition: {
            any7: 0,
            any11: 0,
            anyCraps: 0,
            ace_deuce: 0,
            aces: 0,
            boxcars: 0
        },
        // Quantum bets (new physics-altering bets)
        quantum: {
            entanglement: 0,
            superposition: 0,
            waveCollapse: 0,
            parallelUniverse: 0,
            timeDistortion: 0,
            gravityWell: 0
        }
    },

    totalBet: 0,
    dice: [1, 1],
    lastRoll: 0,
    rollHistory: [],

    // Quantum field effects
    quantumField: {
        active: false,
        intensity: 0,
        entanglement: false,
        superposition: false,
        waveFunction: 'collapsed',
        parallelOutcomes: [],
        timeDistortion: 0,
        gravityWells: [],
        quantumInterference: 0
    },

    // Holographic dice system
    holographicDice: {
        dimension: '3D',
        materialState: 'solid',
        quantumState: 'classical',
        probability: [1/6, 1/6, 1/6, 1/6, 1/6, 1/6], // Normal probability
        bias: [0, 0, 0, 0, 0, 0], // Quantum bias for each face
        entangled: false,
        superposed: false
    },

    stats: {
        rollsPlayed: 0,
        rollsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        quantumEventsTriggered: 0,
        physicsAlterations: 0,
        dimensionalShifts: 0,
        sevenOuts: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Quantum modes with extreme physics manipulation
const QUANTUM_MODES = {
    standard: {
        name: 'Standard Physics',
        houseEdge: 0.58, // 58% house edge
        quantumInterference: 0.25, // 25% quantum interference
        payoutMultiplier: 0.45, // Severely reduced payouts
        physicsStability: 0.85, // 85% physics stability
        realityDistortion: 0.15 // 15% reality distortion
    },
    entangled: {
        name: 'Quantum Entanglement',
        houseEdge: 0.65, // 65% house edge
        quantumInterference: 0.38, // 38% quantum interference
        payoutMultiplier: 0.38, // Even more reduced payouts
        physicsStability: 0.70, // 70% physics stability
        realityDistortion: 0.30 // 30% reality distortion
    },
    superposition: {
        name: 'Quantum Superposition',
        houseEdge: 0.72, // 72% house edge
        quantumInterference: 0.52, // 52% quantum interference
        payoutMultiplier: 0.30, // Extremely reduced payouts
        physicsStability: 0.55, // 55% physics stability
        realityDistortion: 0.45 // 45% reality distortion
    },
    multiverse: {
        name: 'Multiverse Collapse',
        houseEdge: 0.80, // 80% house edge
        quantumInterference: 0.68, // 68% quantum interference
        payoutMultiplier: 0.22, // Brutally reduced payouts
        physicsStability: 0.35, // 35% physics stability
        realityDistortion: 0.65 // 65% reality distortion
    }
};

const FIELD_INTENSITIES = {
    normal: {
        name: 'Normal Field',
        diceBias: 0.20, // 20% dice bias
        outcomeManipulation: 0.15, // 15% outcome manipulation
        quantumFluctuation: 0.10, // 10% quantum fluctuation
        payoutPenalty: 0.12 // 12% additional payout reduction
    },
    intense: {
        name: 'Intense Field',
        diceBias: 0.35, // 35% dice bias
        outcomeManipulation: 0.28, // 28% outcome manipulation
        quantumFluctuation: 0.22, // 22% quantum fluctuation
        payoutPenalty: 0.25 // 25% additional payout reduction
    },
    extreme: {
        name: 'Extreme Field',
        diceBias: 0.50, // 50% dice bias
        outcomeManipulation: 0.42, // 42% outcome manipulation
        quantumFluctuation: 0.35, // 35% quantum fluctuation
        payoutPenalty: 0.38 // 38% additional payout reduction
    },
    quantum: {
        name: 'Quantum Field',
        diceBias: 0.68, // 68% dice bias
        outcomeManipulation: 0.58, // 58% outcome manipulation
        quantumFluctuation: 0.50, // 50% quantum fluctuation
        payoutPenalty: 0.52 // 52% additional payout reduction
    },
    singularity: {
        name: 'Singularity Field',
        diceBias: 0.85, // 85% dice bias
        outcomeManipulation: 0.75, // 75% outcome manipulation
        quantumFluctuation: 0.68, // 68% quantum fluctuation
        payoutPenalty: 0.68 // 68% additional payout reduction
    }
};

// Severely reduced payout table with quantum effects
const QUANTUM_PAYOUTS = {
    // Standard bets (heavily reduced)
    PASS_LINE: 0.75, // Reduced from 1:1
    DONT_PASS: 0.70, // Reduced from 1:1
    COME: 0.75, // Reduced from 1:1
    DONT_COME: 0.70, // Reduced from 1:1
    FIELD: 0.80, // Reduced from 1:1

    // Place bets (terrible odds)
    PLACE_4_10: 1.5, // Reduced from 9:5
    PLACE_5_9: 1.2, // Reduced from 7:5
    PLACE_6_8: 1.0, // Reduced from 7:6

    // Hardways (almost never pay)
    HARD_4_10: 6, // Reduced from 7:1
    HARD_6_8: 8, // Reduced from 9:1

    // Proposition bets (extremely poor odds)
    ANY_SEVEN: 3, // Reduced from 4:1
    ANY_ELEVEN: 12, // Reduced from 15:1
    ANY_CRAPS: 6, // Reduced from 7:1
    ACE_DEUCE: 12, // Reduced from 15:1
    ACES: 25, // Reduced from 30:1
    BOXCARS: 25, // Reduced from 30:1

    // Quantum bets (fake - almost never pay)
    ENTANGLEMENT: 50, // Extremely rare
    SUPERPOSITION: 100, // Virtually impossible
    WAVE_COLLAPSE: 25, // Very rare
    PARALLEL_UNIVERSE: 200, // Mythical
    TIME_DISTORTION: 75, // Nearly impossible
    GRAVITY_WELL: 150 // Legendary
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadCrapsQuantumFieldGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">QUANTUM FIELD CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">QUANTUM MODE</label>
                        <select id="quantumMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Physics</option>
                            <option value="entangled">Quantum Entanglement</option>
                            <option value="superposition">Quantum Superposition</option>
                            <option value="multiverse">Multiverse Collapse</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">FIELD INTENSITY</label>
                        <select id="fieldIntensity" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Normal Field</option>
                            <option value="intense" selected>Intense Field</option>
                            <option value="extreme">Extreme Field</option>
                            <option value="quantum">Quantum Field</option>
                            <option value="singularity">Singularity Field</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-lg font-bold mb-3 text-cyan-400">STANDARD BETS</h5>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Pass Line:</label>
                                <input type="number" id="passLineBet" value="0" min="0" max="${Math.min(balance, 500)}"
                                       class="w-20 bg-black/50 border border-cyan-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Don't Pass:</label>
                                <input type="number" id="dontPassBet" value="0" min="0" max="${Math.min(balance, 500)}"
                                       class="w-20 bg-black/50 border border-cyan-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Field:</label>
                                <input type="number" id="fieldBet" value="0" min="0" max="${Math.min(balance, 200)}"
                                       class="w-20 bg-black/50 border border-cyan-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-lg font-bold mb-3 text-purple-400">QUANTUM BETS</h5>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Entanglement:</label>
                                <input type="number" id="entanglementBet" value="0" min="0" max="${Math.min(balance, 100)}"
                                       class="w-20 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Superposition:</label>
                                <input type="number" id="superpositionBet" value="0" min="0" max="${Math.min(balance, 50)}"
                                       class="w-20 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Wave Collapse:</label>
                                <input type="number" id="waveCollapseBet" value="0" min="0" max="${Math.min(balance, 75)}"
                                       class="w-20 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Parallel Universe:</label>
                                <input type="number" id="parallelUniverseBet" value="0" min="0" max="${Math.min(balance, 25)}"
                                       class="w-20 bg-black/50 border border-purple-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <button id="rollDice" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ACTIVATE QUANTUM FIELD
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Game Phase</div>
                        <div id="gamePhaseDisplay" class="text-lg font-bold text-cyan-400">Come Out</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Point</div>
                        <div id="pointDisplay" class="text-lg font-bold text-green-400">-</div>
                    </div>
                </div>

                <!-- Quantum Field Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">QUANTUM FIELD STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="quantumFieldStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">FIELD: STABLE</div>
                        </div>
                        <div id="entanglementStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">ENTANGLEMENT: INACTIVE</div>
                        </div>
                        <div id="superpositionStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">SUPERPOSITION: COLLAPSED</div>
                        </div>
                        <div id="realityStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">REALITY: STABLE</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">QUANTUM PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Standard Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Pass Line:</span>
                            <span class="text-red-400">0.75:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Don't Pass:</span>
                            <span class="text-red-400">0.70:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Field:</span>
                            <span class="text-red-400">0.80:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Quantum Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Entanglement:</span>
                            <span class="text-red-400">50:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Superposition:</span>
                            <span class="text-red-400">100:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Wave Collapse:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Quantum effects may alter outcomes</div>
                        <div class="text-xs text-red-400">*Physics instability affects payouts</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div id="crapsTable" class="relative bg-gradient-to-br from-black via-cyan-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Quantum Field Visual Effect -->
                        <div id="quantumFieldEffect" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="quantumGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#00ffff;stop-opacity:0.3" />
                                        <stop offset="50%" style="stop-color:#8000ff;stop-opacity:0.2" />
                                        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
                                    </radialGradient>
                                    <pattern id="quantumGrid" width="30" height="30" patternUnits="userSpaceOnUse">
                                        <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#00ffff" stroke-width="0.5" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#quantumGrid)" />
                                <circle id="quantumCore" cx="50%" cy="50%" r="50" fill="url(#quantumGradient)" class="animate-pulse" />
                            </svg>
                        </div>

                        <!-- Holographic Dice Area -->
                        <div class="absolute top-8 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">HOLOGRAPHIC DICE</div>
                                <div id="diceContainer" class="flex space-x-4 justify-center mb-4">
                                    <div id="dice1" class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg border-2 border-cyan-300 flex items-center justify-center text-2xl font-bold text-white shadow-lg transform transition-all duration-500">
                                        1
                                    </div>
                                    <div id="dice2" class="w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg border-2 border-cyan-300 flex items-center justify-center text-2xl font-bold text-white shadow-lg transform transition-all duration-500">
                                        1
                                    </div>
                                </div>
                                <div class="text-lg font-bold text-cyan-400">
                                    Total: <span id="diceTotal">2</span>
                                </div>
                                <div id="quantumState" class="text-sm text-purple-400 mt-1">Quantum State: Classical</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Quantum field stabilizing...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="quantumEvent" class="text-sm font-bold text-purple-400 hidden animate-pulse">QUANTUM EVENT DETECTED!</div>
                        </div>

                        <!-- Betting Areas Visual -->
                        <div class="absolute bottom-4 left-4">
                            <div class="text-center p-2 bg-green-500/20 rounded border border-green-400">
                                <div class="text-xs text-green-400">PASS LINE</div>
                                <div id="passLineBetDisplay" class="text-sm font-bold text-green-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-4 right-4">
                            <div class="text-center p-2 bg-red-500/20 rounded border border-red-400">
                                <div class="text-xs text-red-400">DON'T PASS</div>
                                <div id="dontPassBetDisplay" class="text-sm font-bold text-red-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center p-2 bg-yellow-500/20 rounded border border-yellow-400">
                                <div class="text-xs text-yellow-400">FIELD</div>
                                <div id="fieldBetDisplay" class="text-sm font-bold text-yellow-400">$0</div>
                            </div>
                        </div>

                        <!-- Quantum Effects Visualization -->
                        <div id="quantumEffects" class="absolute inset-0 pointer-events-none">
                            <!-- Quantum particle effects will be generated here -->
                        </div>

                        <!-- Gravity Wells -->
                        <div id="gravityWells" class="absolute inset-0 pointer-events-none">
                            <!-- Gravity wells will be dynamically generated here -->
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Craps Quantum Field - Where Physics Bends to Probability</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Rolls Played</div>
                <div id="rollsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Quantum Events</div>
                <div id="quantumEvents" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Physics Alterations</div>
                <div id="physicsAlterations" class="text-xl font-bold text-cyan-400">0</div>
            </div>
        </div>
    `;

    initializeCrapsQuantumField();
}

// Initialize the game
function initializeCrapsQuantumField() {
    document.getElementById('rollDice').addEventListener('click', rollQuantumDice);

    // Initialize quantum field and holographic dice
    initializeQuantumField();
    initializeHolographicDice();
    updateGameStats();
}

// Initialize quantum field
function initializeQuantumField() {
    crapsQuantumGame.quantumField.active = false;
    crapsQuantumGame.quantumField.intensity = 0;
    crapsQuantumGame.quantumField.entanglement = false;
    crapsQuantumGame.quantumField.superposition = false;
    crapsQuantumGame.quantumField.waveFunction = 'collapsed';
    crapsQuantumGame.quantumField.parallelOutcomes = [];
    crapsQuantumGame.quantumField.timeDistortion = 0;
    crapsQuantumGame.quantumField.gravityWells = [];
    crapsQuantumGame.quantumField.quantumInterference = 0;

    updateQuantumFieldDisplay();
}

// Initialize holographic dice
function initializeHolographicDice() {
    crapsQuantumGame.holographicDice.dimension = '3D';
    crapsQuantumGame.holographicDice.materialState = 'solid';
    crapsQuantumGame.holographicDice.quantumState = 'classical';
    crapsQuantumGame.holographicDice.probability = [1/6, 1/6, 1/6, 1/6, 1/6, 1/6];
    crapsQuantumGame.holographicDice.bias = [0, 0, 0, 0, 0, 0];
    crapsQuantumGame.holographicDice.entangled = false;
    crapsQuantumGame.holographicDice.superposed = false;

    updateDiceDisplay();
}

// Roll quantum dice with physics manipulation
function rollQuantumDice() {
    // Collect all bets
    const bets = collectAllBets();

    if (bets.total === 0) {
        alert('Please place at least one bet!');
        return;
    }

    if (bets.total > balance) {
        alert('Insufficient balance!');
        return;
    }

    // Deduct bets from balance
    balance -= bets.total;
    updateBalance();

    // Set game state
    crapsQuantumGame.isPlaying = true;
    crapsQuantumGame.totalBet = bets.total;
    crapsQuantumGame.quantumMode = document.getElementById('quantumMode').value;
    crapsQuantumGame.fieldIntensity = document.getElementById('fieldIntensity').value;

    // Store bets
    storeBets(bets);

    // Activate quantum field
    activateQuantumField();

    // Roll dice with quantum effects
    setTimeout(() => {
        performQuantumRoll();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('rollDice').disabled = true;
    document.getElementById('gameStatus').textContent = 'Quantum field activating...';
}

// Collect all bets from inputs
function collectAllBets() {
    const bets = {
        passLine: parseInt(document.getElementById('passLineBet').value) || 0,
        dontPass: parseInt(document.getElementById('dontPassBet').value) || 0,
        field: parseInt(document.getElementById('fieldBet').value) || 0,
        entanglement: parseInt(document.getElementById('entanglementBet').value) || 0,
        superposition: parseInt(document.getElementById('superpositionBet').value) || 0,
        waveCollapse: parseInt(document.getElementById('waveCollapseBet').value) || 0,
        parallelUniverse: parseInt(document.getElementById('parallelUniverseBet').value) || 0,
        total: 0
    };

    bets.total = bets.passLine + bets.dontPass + bets.field + bets.entanglement +
                 bets.superposition + bets.waveCollapse + bets.parallelUniverse;

    return bets;
}

// Store bets in game state
function storeBets(bets) {
    crapsQuantumGame.bets.passLine = bets.passLine;
    crapsQuantumGame.bets.dontPass = bets.dontPass;
    crapsQuantumGame.bets.field = bets.field;
    crapsQuantumGame.bets.quantum.entanglement = bets.entanglement;
    crapsQuantumGame.bets.quantum.superposition = bets.superposition;
    crapsQuantumGame.bets.quantum.waveCollapse = bets.waveCollapse;
    crapsQuantumGame.bets.quantum.parallelUniverse = bets.parallelUniverse;
}

// Activate quantum field with physics effects
function activateQuantumField() {
    const modeData = QUANTUM_MODES[crapsQuantumGame.quantumMode];
    const fieldData = FIELD_INTENSITIES[crapsQuantumGame.fieldIntensity];

    crapsQuantumGame.quantumField.active = true;
    crapsQuantumGame.quantumField.intensity = fieldData.quantumFluctuation;
    crapsQuantumGame.quantumField.quantumInterference = modeData.quantumInterference;

    // Activate quantum effects based on mode
    if (Math.random() < modeData.quantumInterference) {
        activateQuantumEntanglement();
    }

    if (Math.random() < fieldData.quantumFluctuation) {
        activateQuantumSuperposition();
    }

    if (Math.random() < modeData.realityDistortion) {
        activateRealityDistortion();
    }

    // Generate gravity wells
    generateGravityWells();

    // Apply physics alterations to dice
    alterDicePhysics();

    // Update visual effects
    updateQuantumFieldDisplay();
    updateQuantumEffects();
}

// Activate quantum entanglement
function activateQuantumEntanglement() {
    crapsQuantumGame.quantumField.entanglement = true;
    crapsQuantumGame.holographicDice.entangled = true;
    crapsQuantumGame.stats.quantumEventsTriggered++;

    document.getElementById('entanglementStatus').innerHTML =
        '<div class="text-blue-400 font-bold animate-pulse">ENTANGLEMENT: ACTIVE</div>';

    // Entangled dice affect each other's outcomes
    crapsQuantumGame.holographicDice.bias = [0.15, -0.1, 0.05, 0.05, -0.1, 0.15]; // Bias toward 1 and 6
}

// Activate quantum superposition
function activateQuantumSuperposition() {
    crapsQuantumGame.quantumField.superposition = true;
    crapsQuantumGame.holographicDice.superposed = true;
    crapsQuantumGame.holographicDice.quantumState = 'superposed';
    crapsQuantumGame.stats.quantumEventsTriggered++;

    document.getElementById('superpositionStatus').innerHTML =
        '<div class="text-green-400 font-bold animate-pulse">SUPERPOSITION: ACTIVE</div>';

    // Superposed dice exist in multiple states until observed
    crapsQuantumGame.quantumField.parallelOutcomes = generateParallelOutcomes();
}

// Activate reality distortion
function activateRealityDistortion() {
    crapsQuantumGame.quantumField.timeDistortion = Math.random() * 0.5;
    crapsQuantumGame.stats.physicsAlterations++;

    document.getElementById('realityStatus').innerHTML =
        '<div class="text-yellow-400 font-bold animate-pulse">REALITY: DISTORTED</div>';

    // Reality distortion affects probability distributions
    const distortion = crapsQuantumGame.quantumField.timeDistortion;
    for (let i = 0; i < 6; i++) {
        crapsQuantumGame.holographicDice.bias[i] += (Math.random() - 0.5) * distortion;
    }
}

// Generate gravity wells
function generateGravityWells() {
    const fieldData = FIELD_INTENSITIES[crapsQuantumGame.fieldIntensity];
    const wellCount = Math.floor(Math.random() * 4) + 1;

    crapsQuantumGame.quantumField.gravityWells = [];

    for (let i = 0; i < wellCount; i++) {
        const well = {
            x: Math.random() * 100,
            y: Math.random() * 100,
            intensity: Math.random() * fieldData.outcomeManipulation,
            type: Math.random() < 0.7 ? 'attractive' : 'repulsive',
            effect: Math.random() < 0.5 ? 'low_numbers' : 'high_numbers'
        };
        crapsQuantumGame.quantumField.gravityWells.push(well);
    }

    // Visual representation of gravity wells
    displayGravityWells();
}

// Display gravity wells visually
function displayGravityWells() {
    const container = document.getElementById('gravityWells');
    container.innerHTML = '';

    crapsQuantumGame.quantumField.gravityWells.forEach((well, index) => {
        const wellElement = document.createElement('div');
        wellElement.className = `absolute w-6 h-6 rounded-full border-2 ${well.type === 'attractive' ? 'border-cyan-400 bg-cyan-500/20' : 'border-purple-400 bg-purple-500/20'} animate-pulse`;
        wellElement.style.left = `${well.x}%`;
        wellElement.style.top = `${well.y}%`;
        wellElement.style.transform = 'translate(-50%, -50%)';
        container.appendChild(wellElement);
    });
}

// Alter dice physics based on quantum effects
function alterDicePhysics() {
    const fieldData = FIELD_INTENSITIES[crapsQuantumGame.fieldIntensity];

    // Apply field intensity bias to dice
    for (let i = 0; i < 6; i++) {
        // Bias toward house-favorable outcomes
        if (i === 0 || i === 5) { // 1 and 6 (seven-out combinations)
            crapsQuantumGame.holographicDice.bias[i] += fieldData.diceBias * 0.3;
        } else if (i === 1 || i === 4) { // 2 and 5 (craps and field losses)
            crapsQuantumGame.holographicDice.bias[i] += fieldData.diceBias * 0.2;
        }
    }

    // Normalize probabilities
    const totalBias = crapsQuantumGame.holographicDice.bias.reduce((sum, bias) => sum + Math.abs(bias), 0);
    if (totalBias > 0) {
        for (let i = 0; i < 6; i++) {
            crapsQuantumGame.holographicDice.probability[i] = (1/6) + crapsQuantumGame.holographicDice.bias[i];
        }
    }
}

// Generate parallel outcomes for superposition
function generateParallelOutcomes() {
    const outcomes = [];
    const universeCount = Math.floor(Math.random() * 5) + 3; // 3-7 parallel universes

    for (let i = 0; i < universeCount; i++) {
        const outcome = {
            dice1: Math.floor(Math.random() * 6) + 1,
            dice2: Math.floor(Math.random() * 6) + 1,
            probability: Math.random(),
            universe: i
        };
        outcome.total = outcome.dice1 + outcome.dice2;
        outcomes.push(outcome);
    }

    return outcomes;
}

// Perform quantum roll with physics manipulation
function performQuantumRoll() {
    document.getElementById('gameStatus').textContent = 'Quantum dice materializing...';

    // Apply quantum effects to dice roll
    let dice1, dice2;

    if (crapsQuantumGame.holographicDice.superposed) {
        // Collapse wave function - choose from parallel outcomes
        const outcomes = crapsQuantumGame.quantumField.parallelOutcomes;
        const selectedOutcome = selectQuantumOutcome(outcomes);
        dice1 = selectedOutcome.dice1;
        dice2 = selectedOutcome.dice2;

        // Show quantum event
        document.getElementById('quantumEvent').classList.remove('hidden');
        setTimeout(() => {
            document.getElementById('quantumEvent').classList.add('hidden');
        }, 3000);
    } else {
        // Standard quantum-biased roll
        dice1 = rollQuantumDie();
        dice2 = rollQuantumDie();

        // Apply entanglement effects
        if (crapsQuantumGame.holographicDice.entangled) {
            // Entangled dice influence each other
            if (dice1 === 1) dice2 = Math.random() < 0.4 ? 6 : dice2; // Bias toward 7
            if (dice1 === 6) dice2 = Math.random() < 0.4 ? 1 : dice2; // Bias toward 7
        }
    }

    // Apply gravity well effects
    applyGravityWellEffects(dice1, dice2);

    // Store roll result
    crapsQuantumGame.dice = [dice1, dice2];
    crapsQuantumGame.lastRoll = dice1 + dice2;
    crapsQuantumGame.rollCount++;
    crapsQuantumGame.rollHistory.push(crapsQuantumGame.lastRoll);

    // Animate dice roll
    animateDiceRoll(dice1, dice2);

    // Resolve outcomes after animation
    setTimeout(() => {
        resolveQuantumOutcomes();
    }, 3000);
}

// Roll a single quantum die with bias
function rollQuantumDie() {
    const probabilities = crapsQuantumGame.holographicDice.probability;
    const random = Math.random();
    let cumulative = 0;

    for (let i = 0; i < 6; i++) {
        cumulative += probabilities[i];
        if (random <= cumulative) {
            return i + 1;
        }
    }

    return 6; // Fallback
}

// Select quantum outcome from parallel universes
function selectQuantumOutcome(outcomes) {
    const fieldData = FIELD_INTENSITIES[crapsQuantumGame.fieldIntensity];

    // Bias selection toward house-favorable outcomes
    const houseFavorableOutcomes = outcomes.filter(outcome => {
        const total = outcome.total;
        // Favor 7 (seven-out) and craps numbers
        return total === 7 || total === 2 || total === 3 || total === 12;
    });

    if (houseFavorableOutcomes.length > 0 && Math.random() < fieldData.outcomeManipulation) {
        return houseFavorableOutcomes[Math.floor(Math.random() * houseFavorableOutcomes.length)];
    }

    // Otherwise select randomly
    return outcomes[Math.floor(Math.random() * outcomes.length)];
}

// Apply gravity well effects to dice
function applyGravityWellEffects(dice1, dice2) {
    crapsQuantumGame.quantumField.gravityWells.forEach(well => {
        if (Math.random() < well.intensity) {
            if (well.effect === 'low_numbers' && well.type === 'attractive') {
                // Pull toward low numbers
                if (dice1 > 3) dice1 = Math.max(1, dice1 - 1);
                if (dice2 > 3) dice2 = Math.max(1, dice2 - 1);
            } else if (well.effect === 'high_numbers' && well.type === 'attractive') {
                // Pull toward high numbers
                if (dice1 < 4) dice1 = Math.min(6, dice1 + 1);
                if (dice2 < 4) dice2 = Math.min(6, dice2 + 1);
            }
        }
    });

    // Update dice values
    crapsQuantumGame.dice = [dice1, dice2];
}

// Animate dice roll
function animateDiceRoll(finalDice1, finalDice2) {
    const dice1Element = document.getElementById('dice1');
    const dice2Element = document.getElementById('dice2');

    // Add quantum effects to dice
    dice1Element.classList.add('animate-spin', 'scale-110');
    dice2Element.classList.add('animate-spin', 'scale-110');

    if (crapsQuantumGame.holographicDice.entangled) {
        dice1Element.classList.add('ring-4', 'ring-blue-400');
        dice2Element.classList.add('ring-4', 'ring-blue-400');
    }

    if (crapsQuantumGame.holographicDice.superposed) {
        dice1Element.classList.add('ring-4', 'ring-green-400', 'animate-pulse');
        dice2Element.classList.add('ring-4', 'ring-green-400', 'animate-pulse');
    }

    // Animate rolling effect
    let rollCount = 0;
    const rollInterval = setInterval(() => {
        dice1Element.textContent = Math.floor(Math.random() * 6) + 1;
        dice2Element.textContent = Math.floor(Math.random() * 6) + 1;
        rollCount++;

        if (rollCount >= 20) {
            clearInterval(rollInterval);

            // Show final result
            dice1Element.textContent = finalDice1;
            dice2Element.textContent = finalDice2;
            document.getElementById('diceTotal').textContent = finalDice1 + finalDice2;

            // Remove animation classes
            setTimeout(() => {
                dice1Element.classList.remove('animate-spin', 'scale-110');
                dice2Element.classList.remove('animate-spin', 'scale-110');
            }, 500);
        }
    }, 100);
}

// Resolve quantum outcomes with extreme house bias
function resolveQuantumOutcomes() {
    const modeData = QUANTUM_MODES[crapsQuantumGame.quantumMode];
    const fieldData = FIELD_INTENSITIES[crapsQuantumGame.fieldIntensity];

    let totalWinnings = 0;
    let resultMessage = '';
    const roll = crapsQuantumGame.lastRoll;

    // Resolve standard bets with quantum bias
    totalWinnings += resolveStandardBets(roll);

    // Resolve quantum bets (terrible odds)
    totalWinnings += resolveQuantumBets(roll);

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier * (1 - fieldData.payoutPenalty));

    // Apply quantum interference penalty
    if (crapsQuantumGame.quantumField.quantumInterference > 0.5) {
        totalWinnings = Math.floor(totalWinnings * 0.7); // 30% quantum penalty
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    crapsQuantumGame.totalWin = totalWinnings;

    // Update game phase
    updateGamePhase(roll);

    // Update display
    updateBalance();
    updateGameStatsAfterRoll(totalWinnings > crapsQuantumGame.totalBet, totalWinnings);

    resultMessage = `Rolled ${roll} - `;
    if (totalWinnings > 0) {
        resultMessage += `Won $${totalWinnings}`;
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    } else {
        resultMessage += `Lost $${crapsQuantumGame.totalBet}`;
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Quantum field intensity: ${Math.floor(crapsQuantumGame.quantumField.intensity * 100)}%`;

    setTimeout(() => resetGame(), 5000);
}

// Resolve standard bets with quantum bias
function resolveStandardBets(roll) {
    let winnings = 0;

    // Pass Line bet
    if (crapsQuantumGame.bets.passLine > 0) {
        if (crapsQuantumGame.gamePhase === 'come_out') {
            if (roll === 7 || roll === 11) {
                winnings += Math.floor(crapsQuantumGame.bets.passLine * QUANTUM_PAYOUTS.PASS_LINE);
            } else if (roll === 2 || roll === 3 || roll === 12) {
                // Pass line loses on craps
            } else {
                // Point established
                crapsQuantumGame.point = roll;
                crapsQuantumGame.gamePhase = 'point';
            }
        } else if (crapsQuantumGame.gamePhase === 'point') {
            if (roll === crapsQuantumGame.point) {
                winnings += Math.floor(crapsQuantumGame.bets.passLine * QUANTUM_PAYOUTS.PASS_LINE);
            } else if (roll === 7) {
                // Seven out - pass line loses
                crapsQuantumGame.stats.sevenOuts++;
            }
        }
    }

    // Don't Pass bet
    if (crapsQuantumGame.bets.dontPass > 0) {
        if (crapsQuantumGame.gamePhase === 'come_out') {
            if (roll === 2 || roll === 3) {
                winnings += Math.floor(crapsQuantumGame.bets.dontPass * QUANTUM_PAYOUTS.DONT_PASS);
            } else if (roll === 12) {
                // Push on 12
                winnings += crapsQuantumGame.bets.dontPass;
            } else if (roll === 7 || roll === 11) {
                // Don't pass loses
            }
        } else if (crapsQuantumGame.gamePhase === 'point') {
            if (roll === 7) {
                winnings += Math.floor(crapsQuantumGame.bets.dontPass * QUANTUM_PAYOUTS.DONT_PASS);
            } else if (roll === crapsQuantumGame.point) {
                // Don't pass loses
            }
        }
    }

    // Field bet (one roll bet)
    if (crapsQuantumGame.bets.field > 0) {
        if (roll === 2 || roll === 3 || roll === 4 || roll === 9 || roll === 10 || roll === 11 || roll === 12) {
            let fieldPayout = QUANTUM_PAYOUTS.FIELD;
            if (roll === 2 || roll === 12) {
                fieldPayout *= 2; // Double on 2 and 12
            }
            winnings += Math.floor(crapsQuantumGame.bets.field * fieldPayout);
        }
        // Field loses on 5, 6, 7, 8
    }

    return winnings;
}

// Resolve quantum bets (almost never pay)
function resolveQuantumBets(roll) {
    let winnings = 0;

    // Entanglement bet
    if (crapsQuantumGame.bets.quantum.entanglement > 0) {
        if (crapsQuantumGame.quantumField.entanglement && Math.random() < 0.02) { // 2% chance
            winnings += Math.floor(crapsQuantumGame.bets.quantum.entanglement * QUANTUM_PAYOUTS.ENTANGLEMENT);
        }
    }

    // Superposition bet
    if (crapsQuantumGame.bets.quantum.superposition > 0) {
        if (crapsQuantumGame.quantumField.superposition && Math.random() < 0.01) { // 1% chance
            winnings += Math.floor(crapsQuantumGame.bets.quantum.superposition * QUANTUM_PAYOUTS.SUPERPOSITION);
        }
    }

    // Wave Collapse bet
    if (crapsQuantumGame.bets.quantum.waveCollapse > 0) {
        if (crapsQuantumGame.holographicDice.quantumState === 'superposed' && Math.random() < 0.04) { // 4% chance
            winnings += Math.floor(crapsQuantumGame.bets.quantum.waveCollapse * QUANTUM_PAYOUTS.WAVE_COLLAPSE);
        }
    }

    // Parallel Universe bet
    if (crapsQuantumGame.bets.quantum.parallelUniverse > 0) {
        if (crapsQuantumGame.quantumField.parallelOutcomes.length > 5 && Math.random() < 0.005) { // 0.5% chance
            winnings += Math.floor(crapsQuantumGame.bets.quantum.parallelUniverse * QUANTUM_PAYOUTS.PARALLEL_UNIVERSE);
        }
    }

    return winnings;
}

// Update game phase
function updateGamePhase(roll) {
    if (crapsQuantumGame.gamePhase === 'come_out') {
        if (roll !== 7 && roll !== 11 && roll !== 2 && roll !== 3 && roll !== 12) {
            crapsQuantumGame.point = roll;
            crapsQuantumGame.gamePhase = 'point';
        }
    } else if (crapsQuantumGame.gamePhase === 'point') {
        if (roll === 7 || roll === crapsQuantumGame.point) {
            crapsQuantumGame.gamePhase = 'come_out';
            crapsQuantumGame.point = null;
        }
    }

    updateGamePhaseDisplay();
}

// Update quantum field display
function updateQuantumFieldDisplay() {
    const intensity = Math.floor(crapsQuantumGame.quantumField.intensity * 100);

    if (crapsQuantumGame.quantumField.active) {
        document.getElementById('quantumFieldStatus').innerHTML =
            `<div class="text-purple-400 font-bold animate-pulse">FIELD: ACTIVE (${intensity}%)</div>`;
    } else {
        document.getElementById('quantumFieldStatus').innerHTML =
            '<div class="text-purple-400 font-bold">FIELD: STABLE</div>';
    }

    // Update quantum state display
    document.getElementById('quantumState').textContent =
        `Quantum State: ${crapsQuantumGame.holographicDice.quantumState}`;
}

// Update quantum effects visualization
function updateQuantumEffects() {
    const container = document.getElementById('quantumEffects');
    container.innerHTML = '';

    // Add quantum particles
    for (let i = 0; i < 10; i++) {
        const particle = document.createElement('div');
        particle.className = 'absolute w-1 h-1 bg-cyan-400 rounded-full animate-ping';
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        particle.style.animationDelay = `${Math.random() * 2}s`;
        container.appendChild(particle);
    }
}

// Update dice display
function updateDiceDisplay() {
    document.getElementById('dice1').textContent = crapsQuantumGame.dice[0];
    document.getElementById('dice2').textContent = crapsQuantumGame.dice[1];
    document.getElementById('diceTotal').textContent = crapsQuantumGame.dice[0] + crapsQuantumGame.dice[1];
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('passLineBetDisplay').textContent = `$${crapsQuantumGame.bets.passLine}`;
    document.getElementById('dontPassBetDisplay').textContent = `$${crapsQuantumGame.bets.dontPass}`;
    document.getElementById('fieldBetDisplay').textContent = `$${crapsQuantumGame.bets.field}`;
    document.getElementById('totalBetDisplay').textContent = `$${crapsQuantumGame.totalBet}`;
}

// Update game phase display
function updateGamePhaseDisplay() {
    document.getElementById('gamePhaseDisplay').textContent =
        crapsQuantumGame.gamePhase === 'come_out' ? 'Come Out' : 'Point';
    document.getElementById('pointDisplay').textContent =
        crapsQuantumGame.point ? crapsQuantumGame.point : '-';
}

// Update game statistics
function updateGameStats() {
    document.getElementById('rollsPlayed').textContent = crapsQuantumGame.stats.rollsPlayed;
    document.getElementById('winRate').textContent = `${crapsQuantumGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${crapsQuantumGame.stats.totalWagered}`;
    document.getElementById('quantumEvents').textContent = crapsQuantumGame.stats.quantumEventsTriggered;
    document.getElementById('physicsAlterations').textContent = crapsQuantumGame.stats.physicsAlterations;

    const netResult = crapsQuantumGame.stats.totalWon - crapsQuantumGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after roll
function updateGameStatsAfterRoll(won, winnings) {
    crapsQuantumGame.stats.rollsPlayed++;
    crapsQuantumGame.stats.totalWagered += crapsQuantumGame.totalBet;
    crapsQuantumGame.stats.totalWon += winnings;

    if (won) {
        crapsQuantumGame.stats.rollsWon++;
        crapsQuantumGame.stats.currentStreak++;
        crapsQuantumGame.streakData.currentWinStreak++;
        crapsQuantumGame.streakData.currentLossStreak = 0;

        if (crapsQuantumGame.streakData.currentWinStreak > crapsQuantumGame.streakData.longestWinStreak) {
            crapsQuantumGame.streakData.longestWinStreak = crapsQuantumGame.streakData.currentWinStreak;
        }

        if (winnings > crapsQuantumGame.stats.biggestWin) {
            crapsQuantumGame.stats.biggestWin = winnings;
        }
    } else {
        crapsQuantumGame.stats.currentStreak = 0;
        crapsQuantumGame.streakData.currentWinStreak = 0;
        crapsQuantumGame.streakData.currentLossStreak++;

        if (crapsQuantumGame.streakData.currentLossStreak > crapsQuantumGame.streakData.longestLossStreak) {
            crapsQuantumGame.streakData.longestLossStreak = crapsQuantumGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to quantum effects)
    crapsQuantumGame.stats.winRate = (crapsQuantumGame.stats.rollsWon / crapsQuantumGame.stats.rollsPlayed) * 100;

    updateGameStats();
}

// Reset game for next roll
function resetGame() {
    crapsQuantumGame.isPlaying = false;
    crapsQuantumGame.totalBet = 0;

    // Reset bets
    crapsQuantumGame.bets.passLine = 0;
    crapsQuantumGame.bets.dontPass = 0;
    crapsQuantumGame.bets.field = 0;
    crapsQuantumGame.bets.quantum.entanglement = 0;
    crapsQuantumGame.bets.quantum.superposition = 0;
    crapsQuantumGame.bets.quantum.waveCollapse = 0;
    crapsQuantumGame.bets.quantum.parallelUniverse = 0;

    // Reset quantum field
    crapsQuantumGame.quantumField.active = false;
    crapsQuantumGame.quantumField.intensity = 0;
    crapsQuantumGame.quantumField.entanglement = false;
    crapsQuantumGame.quantumField.superposition = false;
    crapsQuantumGame.quantumField.waveFunction = 'collapsed';
    crapsQuantumGame.quantumField.parallelOutcomes = [];
    crapsQuantumGame.quantumField.timeDistortion = 0;
    crapsQuantumGame.quantumField.gravityWells = [];
    crapsQuantumGame.quantumField.quantumInterference = 0;

    // Reset holographic dice
    crapsQuantumGame.holographicDice.quantumState = 'classical';
    crapsQuantumGame.holographicDice.probability = [1/6, 1/6, 1/6, 1/6, 1/6, 1/6];
    crapsQuantumGame.holographicDice.bias = [0, 0, 0, 0, 0, 0];
    crapsQuantumGame.holographicDice.entangled = false;
    crapsQuantumGame.holographicDice.superposed = false;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('quantumEvent').classList.add('hidden');
    document.getElementById('gravityWells').innerHTML = '';
    document.getElementById('quantumEffects').innerHTML = '';

    // Reset dice visual effects
    const dice1Element = document.getElementById('dice1');
    const dice2Element = document.getElementById('dice2');
    dice1Element.className = 'w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg border-2 border-cyan-300 flex items-center justify-center text-2xl font-bold text-white shadow-lg transform transition-all duration-500';
    dice2Element.className = 'w-16 h-16 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg border-2 border-cyan-300 flex items-center justify-center text-2xl font-bold text-white shadow-lg transform transition-all duration-500';

    // Reset quantum field status
    document.getElementById('quantumFieldStatus').innerHTML =
        '<div class="text-purple-400 font-bold">FIELD: STABLE</div>';
    document.getElementById('entanglementStatus').innerHTML =
        '<div class="text-blue-400 font-bold">ENTANGLEMENT: INACTIVE</div>';
    document.getElementById('superpositionStatus').innerHTML =
        '<div class="text-green-400 font-bold">SUPERPOSITION: COLLAPSED</div>';
    document.getElementById('realityStatus').innerHTML =
        '<div class="text-yellow-400 font-bold">REALITY: STABLE</div>';

    // Reset bet inputs
    document.getElementById('passLineBet').value = 0;
    document.getElementById('dontPassBet').value = 0;
    document.getElementById('fieldBet').value = 0;
    document.getElementById('entanglementBet').value = 0;
    document.getElementById('superpositionBet').value = 0;
    document.getElementById('waveCollapseBet').value = 0;
    document.getElementById('parallelUniverseBet').value = 0;

    // Reset bet displays
    updateBetDisplay();

    // Enable roll button
    document.getElementById('rollDice').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Quantum field stabilizing...';
    document.getElementById('gameMessage').textContent = 'Welcome to Craps Quantum Field - Where Physics Bends to Probability';

    // Reinitialize quantum field for next roll
    initializeQuantumField();
    initializeHolographicDice();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCrapsQuantumFieldGame();
});