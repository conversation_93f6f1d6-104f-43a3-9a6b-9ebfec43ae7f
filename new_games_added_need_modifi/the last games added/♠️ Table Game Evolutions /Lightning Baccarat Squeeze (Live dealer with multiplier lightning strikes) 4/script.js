// Lightning Baccarat Squeeze - Live Dealer with Multiplier Lightning Strikes
// Ultra High House Edge Implementation with Lightning Effects
// Designed to maintain <4% player win rate

// Game state
let balance = 1000;

// Game state object with lightning system
let lightningBaccaratGame = {
    isPlaying: false,
    gameMode: 'classic', // classic, lightning, storm, tempest
    dealerMode: 'professional', // professional, expert, master, legendary
    playerBet: 0,
    bankerBet: 0,
    tieBet: 0,
    lightningBet: 0, // Special lightning side bet
    totalBet: 0,
    playerCards: [],
    bankerCards: [],
    playerScore: 0,
    bankerScore: 0,
    gameResult: '',
    totalWin: 0,
    deck: [],

    // Lightning system
    lightning: {
        active: false,
        intensity: 0,
        strikes: [],
        multipliers: {},
        chargedCards: [],
        stormLevel: 0,
        electricField: 0,
        conductivity: 0
    },

    // Live dealer simulation
    dealer: {
        name: '<PERSON>',
        mood: 'professional',
        experience: 'expert',
        squeezeTechnique: 'dramatic',
        commentary: [],
        currentAction: 'waiting',
        cardRevealSpeed: 'normal'
    },

    // Card squeeze mechanics
    squeeze: {
        active: false,
        suspenseLevel: 0,
        revealProgress: 0,
        anticipation: 0,
        dramaticPause: false
    },

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        lightningStrikes: 0,
        multiplierHits: 0,
        maxMultiplier: 0,
        squeezesWatched: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Game modes with extreme lightning bias
const LIGHTNING_MODES = {
    classic: {
        name: 'Classic Lightning',
        houseEdge: 0.62, // 62% house edge
        lightningChance: 0.25, // 25% lightning chance
        payoutMultiplier: 0.42, // Severely reduced payouts
        maxMultiplier: 8, // Maximum lightning multiplier
        strikeProbability: 0.15 // 15% strike probability
    },
    lightning: {
        name: 'Lightning Storm',
        houseEdge: 0.68, // 68% house edge
        lightningChance: 0.35, // 35% lightning chance
        payoutMultiplier: 0.38, // Even more reduced payouts
        maxMultiplier: 12, // Higher multipliers (fake)
        strikeProbability: 0.12 // 12% strike probability
    },
    storm: {
        name: 'Electric Storm',
        houseEdge: 0.75, // 75% house edge
        lightningChance: 0.45, // 45% lightning chance
        payoutMultiplier: 0.32, // Extremely reduced payouts
        maxMultiplier: 20, // Very high multipliers (almost never hit)
        strikeProbability: 0.08 // 8% strike probability
    },
    tempest: {
        name: 'Lightning Tempest',
        houseEdge: 0.82, // 82% house edge
        lightningChance: 0.55, // 55% lightning chance
        payoutMultiplier: 0.25, // Brutally reduced payouts
        maxMultiplier: 50, // Legendary multipliers (mythical)
        strikeProbability: 0.05 // 5% strike probability
    }
};

const DEALER_MODES = {
    professional: {
        name: 'Professional Dealer',
        cardBias: 0.25, // 25% card bias
        squeezeDrama: 0.20, // 20% squeeze drama
        commentaryFrequency: 0.30, // 30% commentary
        revealSpeed: 1.0 // Normal speed
    },
    expert: {
        name: 'Expert Dealer',
        cardBias: 0.35, // 35% card bias
        squeezeDrama: 0.35, // 35% squeeze drama
        commentaryFrequency: 0.45, // 45% commentary
        revealSpeed: 0.8 // Slower for drama
    },
    master: {
        name: 'Master Dealer',
        cardBias: 0.48, // 48% card bias
        squeezeDrama: 0.50, // 50% squeeze drama
        commentaryFrequency: 0.60, // 60% commentary
        revealSpeed: 0.6 // Much slower
    },
    legendary: {
        name: 'Legendary Dealer',
        cardBias: 0.65, // 65% card bias
        squeezeDrama: 0.70, // 70% squeeze drama
        commentaryFrequency: 0.80, // 80% commentary
        revealSpeed: 0.4 // Extremely slow for maximum drama
    }
};

// Severely reduced payout table with lightning effects
const LIGHTNING_PAYOUTS = {
    // Standard bets (heavily reduced)
    PLAYER_WIN: 0.70, // Reduced from 1:1
    BANKER_WIN: 0.65, // Reduced from 0.95:1 (after commission)
    TIE_WIN: 5, // Reduced from 8:1

    // Lightning multipliers (fake - almost never actually applied)
    LIGHTNING_2X: 2,
    LIGHTNING_3X: 3,
    LIGHTNING_5X: 5,
    LIGHTNING_8X: 8,
    LIGHTNING_12X: 12,
    LIGHTNING_20X: 20,
    LIGHTNING_50X: 50,

    // Lightning side bet (terrible odds)
    LIGHTNING_BET_WIN: 0.5, // Half payout even when winning

    // Squeeze bonuses (minimal)
    SQUEEZE_BONUS: 0.1 // 10% bonus for watching squeeze
};

// Card values for baccarat
const CARD_VALUES = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 0, 'J': 0, 'Q': 0, 'K': 0
};

// Dealer commentary phrases
const DEALER_COMMENTARY = {
    greeting: [
        "Welcome to Lightning Baccarat! I'm Elena, your dealer today.",
        "Good luck at the table! The lightning is charged and ready.",
        "Let's see what the cards have in store for us today!"
    ],
    betting: [
        "Place your bets, the lightning is building...",
        "Feel the electricity in the air? Time to bet!",
        "The storm is gathering, make your wagers!"
    ],
    dealing: [
        "Cards are coming out now...",
        "Let's see what fate has dealt us...",
        "The moment of truth approaches..."
    ],
    squeeze: [
        "Let's squeeze this card slowly...",
        "The suspense is building...",
        "What will this reveal?",
        "Slowly, slowly... the tension is electric!"
    ],
    lightning: [
        "Lightning strikes! The multipliers are active!",
        "Feel that electric energy!",
        "The storm has arrived!",
        "Lightning illuminates the table!"
    ],
    results: [
        "And the result is...",
        "The cards have spoken!",
        "What an exciting hand!",
        "The lightning has decided!"
    ]
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadLightningBaccaratGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">LIGHTNING CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">LIGHTNING MODE</label>
                        <select id="gameMode" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classic">Classic Lightning</option>
                            <option value="lightning">Lightning Storm</option>
                            <option value="storm">Electric Storm</option>
                            <option value="tempest">Lightning Tempest</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DEALER MODE</label>
                        <select id="dealerMode" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="professional">Professional Dealer</option>
                            <option value="expert" selected>Expert Dealer</option>
                            <option value="master">Master Dealer</option>
                            <option value="legendary">Legendary Dealer</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <h5 class="text-lg font-bold mb-3 text-yellow-400">BETTING AREA</h5>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Player:</label>
                                <input type="number" id="playerBet" value="0" min="0" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-yellow-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Banker:</label>
                                <input type="number" id="bankerBet" value="0" min="0" max="${Math.min(balance, 500)}"
                                       class="w-24 bg-black/50 border border-yellow-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Tie:</label>
                                <input type="number" id="tieBet" value="0" min="0" max="${Math.min(balance, 200)}"
                                       class="w-24 bg-black/50 border border-yellow-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                            <div class="flex justify-between items-center">
                                <label class="text-sm text-gray-300">Lightning:</label>
                                <input type="number" id="lightningBet" value="0" min="0" max="${Math.min(balance, 100)}"
                                       class="w-24 bg-black/50 border border-yellow-500/50 rounded px-2 py-1 text-white text-sm">
                            </div>
                        </div>
                    </div>

                    <button id="dealCards" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        CHARGE THE LIGHTNING
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Lightning Charge</div>
                        <div id="lightningCharge" class="text-lg font-bold text-yellow-400">0%</div>
                    </div>
                </div>

                <!-- Lightning Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">LIGHTNING STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="lightningStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LIGHTNING: DORMANT</div>
                        </div>
                        <div id="stormLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">STORM LEVEL: 0</div>
                        </div>
                        <div id="multiplierStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">MULTIPLIERS: INACTIVE</div>
                        </div>
                        <div class="mt-3">
                            <div class="text-yellow-400 font-bold text-center mb-2">ACTIVE MULTIPLIERS</div>
                            <div id="activeMultipliers" class="grid grid-cols-3 gap-1 text-xs">
                                <!-- Active multipliers will appear here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">LIGHTNING PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Standard Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Player Win:</span>
                            <span class="text-red-400">0.70:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Banker Win:</span>
                            <span class="text-red-400">0.65:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tie:</span>
                            <span class="text-red-400">5:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Lightning Multipliers:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">2x - 8x:</span>
                            <span class="text-red-400">Common</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">12x - 20x:</span>
                            <span class="text-red-400">Rare</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">50x:</span>
                            <span class="text-red-400">Legendary</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Lightning rarely strikes winning bets</div>
                        <div class="text-xs text-red-400">*Multipliers displayed may not apply</div>
                    </div>
                </div>
            </div>

            <!-- Main Game Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <!-- Live Dealer Area -->
                    <div class="mb-6 bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg p-4 border border-yellow-400/30">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 flex items-center justify-center">
                                <span class="text-2xl">👩‍💼</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-yellow-400">Elena Voltaire</h3>
                                <p class="text-sm text-gray-300">Live Lightning Dealer</p>
                                <div id="dealerMood" class="text-xs text-blue-400">Professional • Expert Level</div>
                            </div>
                            <div class="flex-1"></div>
                            <div id="dealerCommentary" class="bg-black/50 rounded-lg p-3 max-w-md">
                                <p class="text-sm text-yellow-300 italic">"Welcome to Lightning Baccarat! The storm is ready to strike!"</p>
                            </div>
                        </div>
                    </div>

                    <div id="baccaratTable" class="relative bg-gradient-to-br from-black via-yellow-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Lightning Effects -->
                        <div id="lightningEffects" class="absolute inset-0 pointer-events-none">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="lightningGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#ffff00;stop-opacity:0.4" />
                                        <stop offset="50%" style="stop-color:#ff8800;stop-opacity:0.2" />
                                        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
                                    </radialGradient>
                                    <filter id="glow">
                                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                                        <feMerge>
                                            <feMergeNode in="coloredBlur"/>
                                            <feMergeNode in="SourceGraphic"/>
                                        </feMerge>
                                    </filter>
                                </defs>
                                <g id="lightningBolts">
                                    <!-- Lightning bolts will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Player Area -->
                        <div class="absolute bottom-4 left-8">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">PLAYER</div>
                                <div id="playerCards" class="flex space-x-2 mb-2">
                                    <!-- Player cards will appear here -->
                                </div>
                                <div class="text-lg font-bold text-blue-400">
                                    Score: <span id="playerScore">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Banker Area -->
                        <div class="absolute bottom-4 right-8">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">BANKER</div>
                                <div id="bankerCards" class="flex space-x-2 mb-2">
                                    <!-- Banker cards will appear here -->
                                </div>
                                <div class="text-lg font-bold text-red-400">
                                    Score: <span id="bankerScore">0</span>
                                </div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-8 left-1/2 transform -translate-x-1/2 text-center">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Lightning is charging...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="lightningStrike" class="text-lg font-bold text-yellow-400 hidden animate-pulse">⚡ LIGHTNING STRIKE! ⚡</div>
                        </div>

                        <!-- Squeeze Area -->
                        <div id="squeezeArea" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 hidden">
                            <div class="bg-black/80 rounded-lg p-6 border-2 border-yellow-400">
                                <div class="text-center">
                                    <div class="text-yellow-400 font-bold mb-2">CARD SQUEEZE</div>
                                    <div id="squeezeCard" class="w-24 h-36 bg-blue-900 rounded-lg border-2 border-yellow-400 mx-auto mb-4 relative overflow-hidden">
                                        <div id="squeezeReveal" class="absolute inset-0 bg-white transform translate-y-full transition-transform duration-1000">
                                            <!-- Card content revealed during squeeze -->
                                        </div>
                                    </div>
                                    <div id="squeezeProgress" class="w-full bg-gray-700 rounded-full h-2 mb-2">
                                        <div id="squeezeBar" class="bg-yellow-400 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                    <div id="squeezeText" class="text-sm text-gray-300">Slowly revealing...</div>
                                </div>
                            </div>
                        </div>

                        <!-- Betting Areas Visual -->
                        <div class="absolute bottom-16 left-4">
                            <div class="text-center p-2 bg-blue-500/20 rounded border border-blue-400">
                                <div class="text-xs text-blue-400">PLAYER</div>
                                <div id="playerBetDisplay" class="text-sm font-bold text-blue-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-16 right-4">
                            <div class="text-center p-2 bg-red-500/20 rounded border border-red-400">
                                <div class="text-xs text-red-400">BANKER</div>
                                <div id="bankerBetDisplay" class="text-sm font-bold text-red-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute bottom-32 left-1/2 transform -translate-x-1/2">
                            <div class="text-center p-2 bg-yellow-500/20 rounded border border-yellow-400">
                                <div class="text-xs text-yellow-400">TIE</div>
                                <div id="tieBetDisplay" class="text-sm font-bold text-yellow-400">$0</div>
                            </div>
                        </div>

                        <div class="absolute top-16 right-4">
                            <div class="text-center p-2 bg-purple-500/20 rounded border border-purple-400">
                                <div class="text-xs text-purple-400">LIGHTNING</div>
                                <div id="lightningBetDisplay" class="text-sm font-bold text-purple-400">$0</div>
                            </div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Lightning Baccarat Squeeze - Where Every Card Tells a Story</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Lightning Strikes</div>
                <div id="lightningStrikes" class="text-xl font-bold text-yellow-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Max Multiplier</div>
                <div id="maxMultiplier" class="text-xl font-bold text-purple-400">0x</div>
            </div>
        </div>
    `;

    initializeLightningBaccarat();
}

// Initialize the game
function initializeLightningBaccarat() {
    document.getElementById('dealCards').addEventListener('click', dealNewHand);

    // Initialize lightning system and dealer
    initializeLightningSystem();
    initializeDealer();
    initializeDeck();
    updateGameStats();
}

// Initialize lightning system
function initializeLightningSystem() {
    lightningBaccaratGame.lightning.active = false;
    lightningBaccaratGame.lightning.intensity = 0;
    lightningBaccaratGame.lightning.strikes = [];
    lightningBaccaratGame.lightning.multipliers = {};
    lightningBaccaratGame.lightning.chargedCards = [];
    lightningBaccaratGame.lightning.stormLevel = 0;
    lightningBaccaratGame.lightning.electricField = 0;
    lightningBaccaratGame.lightning.conductivity = 0;

    updateLightningDisplay();
}

// Initialize dealer
function initializeDealer() {
    lightningBaccaratGame.dealer.name = 'Elena Voltaire';
    lightningBaccaratGame.dealer.mood = 'professional';
    lightningBaccaratGame.dealer.experience = 'expert';
    lightningBaccaratGame.dealer.squeezeTechnique = 'dramatic';
    lightningBaccaratGame.dealer.commentary = [];
    lightningBaccaratGame.dealer.currentAction = 'waiting';
    lightningBaccaratGame.dealer.cardRevealSpeed = 'normal';

    updateDealerDisplay();
}

// Initialize deck with lightning bias
function initializeDeck() {
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

    lightningBaccaratGame.deck = [];

    // Create deck with lightning charge potential
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    for (const suit of suits) {
        for (const rank of ranks) {
            const card = {
                rank,
                suit,
                value: CARD_VALUES[rank],
                id: `${rank}${suit}`,
                lightningCharge: Math.random(), // Lightning charge potential
                electricConductivity: Math.random()
            };

            lightningBaccaratGame.deck.push(card);

            // Add extra copies of house-favorable cards based on dealer skill
            if (Math.random() < dealerData.cardBias) {
                // Dealer prefers cards that create ties or banker wins
                if (card.value <= 3 || card.value === 0) { // Low cards and face cards
                    const extraCopies = Math.floor(dealerData.cardBias * 5);
                    for (let i = 0; i < extraCopies; i++) {
                        lightningBaccaratGame.deck.push({
                            ...card,
                            id: `${rank}${suit}_dealer_${i}`,
                            lightningCharge: Math.random() + 0.3 // Higher lightning charge
                        });
                    }
                }
            }
        }
    }

    shuffleDeckWithLightning();
}

// Shuffle deck with lightning effects
function shuffleDeckWithLightning() {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    // Standard shuffle
    for (let i = lightningBaccaratGame.deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [lightningBaccaratGame.deck[i], lightningBaccaratGame.deck[j]] = [lightningBaccaratGame.deck[j], lightningBaccaratGame.deck[i]];
    }

    // Lightning-charged shuffle - cards with higher charge rise to top
    if (Math.random() < dealerData.cardBias) {
        lightningBaccaratGame.deck.sort((a, b) => {
            // Higher lightning charge cards are more likely to be dealt
            return b.lightningCharge - a.lightningCharge;
        });
    }
}

// Deal new hand with lightning effects
function dealNewHand() {
    const playerBet = parseInt(document.getElementById('playerBet').value) || 0;
    const bankerBet = parseInt(document.getElementById('bankerBet').value) || 0;
    const tieBet = parseInt(document.getElementById('tieBet').value) || 0;
    const lightningBet = parseInt(document.getElementById('lightningBet').value) || 0;

    const totalBets = playerBet + bankerBet + tieBet + lightningBet;

    if (totalBets === 0) {
        alert('Please place at least one bet!');
        return;
    }

    if (totalBets > balance) {
        alert('Insufficient balance!');
        return;
    }

    // Deduct bets from balance
    balance -= totalBets;
    updateBalance();

    // Set game state
    lightningBaccaratGame.isPlaying = true;
    lightningBaccaratGame.playerBet = playerBet;
    lightningBaccaratGame.bankerBet = bankerBet;
    lightningBaccaratGame.tieBet = tieBet;
    lightningBaccaratGame.lightningBet = lightningBet;
    lightningBaccaratGame.totalBet = totalBets;
    lightningBaccaratGame.gameMode = document.getElementById('gameMode').value;
    lightningBaccaratGame.dealerMode = document.getElementById('dealerMode').value;

    // Clear previous cards
    lightningBaccaratGame.playerCards = [];
    lightningBaccaratGame.bankerCards = [];
    lightningBaccaratGame.playerScore = 0;
    lightningBaccaratGame.bankerScore = 0;

    // Activate lightning system
    activateLightningSystem();

    // Dealer commentary
    dealerSay(DEALER_COMMENTARY.betting[Math.floor(Math.random() * DEALER_COMMENTARY.betting.length)]);

    // Deal cards with lightning and squeeze effects
    setTimeout(() => {
        dealCardsWithLightning();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('dealCards').disabled = true;
    document.getElementById('gameStatus').textContent = 'Lightning is charging...';
}

// Activate lightning system
function activateLightningSystem() {
    const modeData = LIGHTNING_MODES[lightningBaccaratGame.gameMode];

    lightningBaccaratGame.lightning.active = true;
    lightningBaccaratGame.lightning.intensity = Math.random() * 0.8 + 0.2; // 20-100% intensity
    lightningBaccaratGame.lightning.electricField = modeData.lightningChance;
    lightningBaccaratGame.lightning.stormLevel = Math.floor(Math.random() * 5) + 1;

    // Generate lightning multipliers (mostly fake)
    generateLightningMultipliers();

    // Create lightning visual effects
    createLightningEffects();

    // Update displays
    updateLightningDisplay();

    // Dealer commentary about lightning
    if (Math.random() < 0.7) {
        dealerSay(DEALER_COMMENTARY.lightning[Math.floor(Math.random() * DEALER_COMMENTARY.lightning.length)]);
    }
}

// Generate lightning multipliers (fake system)
function generateLightningMultipliers() {
    const modeData = LIGHTNING_MODES[lightningBaccaratGame.gameMode];
    lightningBaccaratGame.lightning.multipliers = {};

    // Generate fake multipliers for display
    const multiplierCount = Math.floor(Math.random() * 8) + 4; // 4-11 multipliers
    const possibleMultipliers = [2, 3, 5, 8, 12, 20, 50];

    for (let i = 0; i < multiplierCount; i++) {
        const multiplier = possibleMultipliers[Math.floor(Math.random() * possibleMultipliers.length)];
        const cardValue = Math.floor(Math.random() * 10) + 1; // 1-10

        // Higher multipliers are rarer and almost never actually applied
        if (multiplier <= 8) {
            lightningBaccaratGame.lightning.multipliers[cardValue] = multiplier;
        } else if (multiplier <= 20 && Math.random() < 0.3) {
            lightningBaccaratGame.lightning.multipliers[cardValue] = multiplier;
        } else if (multiplier === 50 && Math.random() < 0.1) {
            lightningBaccaratGame.lightning.multipliers[cardValue] = multiplier;
        }
    }

    displayActiveMultipliers();
}

// Display active multipliers
function displayActiveMultipliers() {
    const container = document.getElementById('activeMultipliers');
    container.innerHTML = '';

    Object.entries(lightningBaccaratGame.lightning.multipliers).forEach(([value, multiplier]) => {
        const multiplierElement = document.createElement('div');
        multiplierElement.className = `text-center p-1 rounded ${multiplier >= 20 ? 'bg-purple-600' : multiplier >= 8 ? 'bg-yellow-600' : 'bg-blue-600'}`;
        multiplierElement.innerHTML = `<div class="text-xs font-bold">${value}</div><div class="text-xs">${multiplier}x</div>`;
        container.appendChild(multiplierElement);
    });
}

// Create lightning visual effects
function createLightningEffects() {
    const container = document.getElementById('lightningBolts');
    container.innerHTML = '';

    // Create lightning bolts
    for (let i = 0; i < 3; i++) {
        const bolt = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const startX = Math.random() * 100;
        const startY = 0;
        const endX = Math.random() * 100;
        const endY = 100;

        const pathData = `M ${startX} ${startY} L ${endX} ${endY}`;
        bolt.setAttribute('d', pathData);
        bolt.setAttribute('stroke', '#ffff00');
        bolt.setAttribute('stroke-width', '2');
        bolt.setAttribute('opacity', '0.7');
        bolt.setAttribute('filter', 'url(#glow)');
        bolt.classList.add('animate-pulse');

        container.appendChild(bolt);
    }
}

// Deal cards with lightning and squeeze effects
function dealCardsWithLightning() {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    dealerSay(DEALER_COMMENTARY.dealing[Math.floor(Math.random() * DEALER_COMMENTARY.dealing.length)]);

    // Deal initial cards with dramatic timing
    setTimeout(() => dealCardWithLightning('player'), 1000);
    setTimeout(() => dealCardWithLightning('banker'), 2000);
    setTimeout(() => dealCardWithLightning('player'), 3000);
    setTimeout(() => dealCardWithLightning('banker'), 4000);

    // Check for third card rules with squeeze
    setTimeout(() => {
        applyThirdCardRulesWithSqueeze();
    }, 5000);
}

// Deal a single card with lightning effects
function dealCardWithLightning(recipient) {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];
    let card;

    // Apply dealer bias to card selection
    if (Math.random() < dealerData.cardBias) {
        card = selectDealerBiasedCard(recipient);
    } else {
        card = lightningBaccaratGame.deck.pop();
    }

    if (!card) {
        // Fallback if deck is empty
        initializeDeck();
        card = lightningBaccaratGame.deck.pop();
    }

    // Apply lightning charge to card
    if (lightningBaccaratGame.lightning.active && Math.random() < lightningBaccaratGame.lightning.electricField) {
        card.lightningCharged = true;
        lightningBaccaratGame.lightning.chargedCards.push(card);
    }

    if (recipient === 'player') {
        lightningBaccaratGame.playerCards.push(card);
        displayCard(card, 'playerCards');
    } else {
        lightningBaccaratGame.bankerCards.push(card);
        displayCard(card, 'bankerCards');
    }

    calculateScores();
}

// Select dealer-biased card
function selectDealerBiasedCard(recipient) {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];
    let preferredCards;

    if (recipient === 'player') {
        // Dealer prefers to give player cards that lead to losses
        preferredCards = lightningBaccaratGame.deck.filter(card =>
            card.value <= 3 || (card.value === 0 && Math.random() < dealerData.cardBias)
        );
    } else {
        // Dealer prefers to give banker cards that lead to wins
        preferredCards = lightningBaccaratGame.deck.filter(card =>
            card.value >= 6 || (card.value === 0 && Math.random() < dealerData.cardBias)
        );
    }

    if (preferredCards.length === 0) {
        return lightningBaccaratGame.deck.pop();
    }

    const selectedCard = preferredCards[Math.floor(Math.random() * preferredCards.length)];

    // Remove from deck
    const index = lightningBaccaratGame.deck.findIndex(card => card.id === selectedCard.id);
    if (index !== -1) {
        lightningBaccaratGame.deck.splice(index, 1);
    }

    return selectedCard;
}

// Display card with lightning effects
function displayCard(card, containerId) {
    const container = document.getElementById(containerId);
    const cardElement = document.createElement('div');

    cardElement.className = 'w-12 h-16 bg-white rounded border border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold shadow-lg transform transition-all duration-500';

    // Add lightning effects
    if (card.lightningCharged) {
        cardElement.classList.add('ring-2', 'ring-yellow-400', 'animate-pulse');
        cardElement.style.boxShadow = '0 0 10px #ffff00';
    }

    if (card.lightningCharge > 0.8) {
        cardElement.classList.add('ring-2', 'ring-purple-400');
    }

    const isRed = card.suit === '♥' || card.suit === '♦';
    cardElement.style.color = isRed ? '#dc2626' : '#000';

    cardElement.innerHTML = `
        <div class="text-xs">${card.rank}</div>
        <div class="text-lg">${card.suit}</div>
        <div class="text-xs transform rotate-180">${card.rank}</div>
    `;

    // Lightning animation
    cardElement.style.transform = 'translateY(-20px) rotate(5deg) scale(1.1)';
    setTimeout(() => {
        cardElement.style.transform = 'translateY(0) rotate(0deg) scale(1)';
    }, 300);

    container.appendChild(cardElement);
}

// Apply third card rules with squeeze
function applyThirdCardRulesWithSqueeze() {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    // Standard baccarat third card rules
    const playerTotal = lightningBaccaratGame.playerScore;
    const bankerTotal = lightningBaccaratGame.bankerScore;

    let playerGetsThird = false;
    let bankerGetsThird = false;

    // Player third card rules
    if (playerTotal <= 5) {
        playerGetsThird = true;
    }

    // Deal player third card with squeeze if needed
    if (playerGetsThird) {
        setTimeout(() => {
            performCardSqueeze('player', () => {
                dealCardWithLightning('player');
            });
        }, 1000);
    }

    // Banker third card rules
    setTimeout(() => {
        const finalPlayerTotal = lightningBaccaratGame.playerScore;

        if (bankerTotal <= 5) {
            bankerGetsThird = true;
        }

        if (bankerGetsThird) {
            setTimeout(() => {
                performCardSqueeze('banker', () => {
                    dealCardWithLightning('banker');
                    setTimeout(() => resolveHandWithLightning(), 2000);
                });
            }, playerGetsThird ? 4000 : 1000);
        } else {
            setTimeout(() => resolveHandWithLightning(), 2000);
        }
    }, playerGetsThird ? 5000 : 1000);
}

// Perform card squeeze with dramatic effect
function performCardSqueeze(recipient, callback) {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    lightningBaccaratGame.squeeze.active = true;
    lightningBaccaratGame.squeeze.suspenseLevel = Math.random() * dealerData.squeezeDrama;
    lightningBaccaratGame.squeeze.revealProgress = 0;
    lightningBaccaratGame.squeeze.anticipation = 0;

    // Show squeeze area
    document.getElementById('squeezeArea').classList.remove('hidden');

    // Dealer commentary during squeeze
    dealerSay(DEALER_COMMENTARY.squeeze[Math.floor(Math.random() * DEALER_COMMENTARY.squeeze.length)]);

    // Animate squeeze progress
    const squeezeInterval = setInterval(() => {
        lightningBaccaratGame.squeeze.revealProgress += 2;
        document.getElementById('squeezeBar').style.width = `${lightningBaccaratGame.squeeze.revealProgress}%`;

        if (lightningBaccaratGame.squeeze.revealProgress >= 100) {
            clearInterval(squeezeInterval);

            // Hide squeeze area
            document.getElementById('squeezeArea').classList.add('hidden');

            // Execute callback
            callback();

            lightningBaccaratGame.squeeze.active = false;
            lightningBaccaratGame.stats.squeezesWatched++;
        }
    }, 100 / dealerData.revealSpeed);
}

// Calculate scores
function calculateScores() {
    // Player score
    lightningBaccaratGame.playerScore = lightningBaccaratGame.playerCards.reduce((sum, card) => {
        return (sum + card.value) % 10;
    }, 0);

    // Banker score
    lightningBaccaratGame.bankerScore = lightningBaccaratGame.bankerCards.reduce((sum, card) => {
        return (sum + card.value) % 10;
    }, 0);

    updateScoreDisplays();
}

// Update score displays
function updateScoreDisplays() {
    document.getElementById('playerScore').textContent = lightningBaccaratGame.playerScore;
    document.getElementById('bankerScore').textContent = lightningBaccaratGame.bankerScore;
}

// Resolve hand with lightning effects and extreme house bias
function resolveHandWithLightning() {
    const modeData = LIGHTNING_MODES[lightningBaccaratGame.gameMode];
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    let totalWinnings = 0;
    let resultMessage = '';

    // Determine winner
    const playerScore = lightningBaccaratGame.playerScore;
    const bankerScore = lightningBaccaratGame.bankerScore;

    let winner;
    if (playerScore > bankerScore) {
        winner = 'player';
        lightningBaccaratGame.gameResult = 'player_win';
    } else if (bankerScore > playerScore) {
        winner = 'banker';
        lightningBaccaratGame.gameResult = 'banker_win';
    } else {
        winner = 'tie';
        lightningBaccaratGame.gameResult = 'tie';
    }

    // Calculate standard bet winnings with severe reductions
    if (winner === 'player' && lightningBaccaratGame.playerBet > 0) {
        let winAmount = Math.floor(lightningBaccaratGame.playerBet * LIGHTNING_PAYOUTS.PLAYER_WIN);

        // Check for lightning multiplier (almost never applies)
        winAmount = applyLightningMultiplier(winAmount, 'player');

        totalWinnings += winAmount;
        resultMessage += `Player wins: +$${winAmount} `;
    }

    if (winner === 'banker' && lightningBaccaratGame.bankerBet > 0) {
        let winAmount = Math.floor(lightningBaccaratGame.bankerBet * LIGHTNING_PAYOUTS.BANKER_WIN);

        // Check for lightning multiplier (almost never applies)
        winAmount = applyLightningMultiplier(winAmount, 'banker');

        totalWinnings += winAmount;
        resultMessage += `Banker wins: +$${winAmount} `;
    }

    if (winner === 'tie' && lightningBaccaratGame.tieBet > 0) {
        let winAmount = Math.floor(lightningBaccaratGame.tieBet * LIGHTNING_PAYOUTS.TIE_WIN);

        // Check for lightning multiplier (almost never applies)
        winAmount = applyLightningMultiplier(winAmount, 'tie');

        totalWinnings += winAmount;
        resultMessage += `Tie wins: +$${winAmount} `;
    }

    // Lightning side bet (terrible odds)
    if (lightningBaccaratGame.lightningBet > 0) {
        const lightningWin = calculateLightningBetWinnings();
        totalWinnings += lightningWin;
        if (lightningWin > 0) {
            resultMessage += `Lightning: +$${lightningWin} `;
        }
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier);

    // Apply dealer skill penalty
    totalWinnings = Math.floor(totalWinnings * (1 - dealerData.squeezeDrama * 0.3));

    // Squeeze bonus (minimal)
    if (lightningBaccaratGame.stats.squeezesWatched > 0) {
        const squeezeBonus = Math.floor(totalWinnings * LIGHTNING_PAYOUTS.SQUEEZE_BONUS);
        totalWinnings += squeezeBonus;
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    lightningBaccaratGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > lightningBaccaratGame.totalBet, totalWinnings);

    // Dealer commentary
    dealerSay(DEALER_COMMENTARY.results[Math.floor(Math.random() * DEALER_COMMENTARY.results.length)]);

    document.getElementById('gameStatus').textContent = `${winner.toUpperCase()} WINS!`;
    document.getElementById('gameMessage').innerHTML = resultMessage || `${winner} wins the hand`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Apply lightning multiplier (fake system - almost never actually applies)
function applyLightningMultiplier(winAmount, betType) {
    const modeData = LIGHTNING_MODES[lightningBaccaratGame.gameMode];

    // Check if lightning strikes (very rare)
    if (Math.random() < modeData.strikeProbability) {
        // Check if any charged cards are involved
        const chargedCardsInvolved = lightningBaccaratGame.lightning.chargedCards.length > 0;

        if (chargedCardsInvolved && Math.random() < 0.1) { // 10% chance even with charged cards
            // Find applicable multiplier
            const applicableMultipliers = Object.values(lightningBaccaratGame.lightning.multipliers);
            if (applicableMultipliers.length > 0) {
                const multiplier = applicableMultipliers[Math.floor(Math.random() * applicableMultipliers.length)];

                // Show lightning strike effect
                document.getElementById('lightningStrike').classList.remove('hidden');
                setTimeout(() => {
                    document.getElementById('lightningStrike').classList.add('hidden');
                }, 3000);

                lightningBaccaratGame.stats.lightningStrikes++;
                lightningBaccaratGame.stats.multiplierHits++;

                if (multiplier > lightningBaccaratGame.stats.maxMultiplier) {
                    lightningBaccaratGame.stats.maxMultiplier = multiplier;
                }

                return Math.floor(winAmount * multiplier);
            }
        }
    }

    return winAmount;
}

// Calculate lightning bet winnings (terrible odds)
function calculateLightningBetWinnings() {
    // Lightning bet only pays if lightning actually strikes (extremely rare)
    if (lightningBaccaratGame.stats.lightningStrikes > 0) {
        return Math.floor(lightningBaccaratGame.lightningBet * LIGHTNING_PAYOUTS.LIGHTNING_BET_WIN);
    }

    return 0;
}

// Dealer says something
function dealerSay(message) {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];

    if (Math.random() < dealerData.commentaryFrequency) {
        document.getElementById('dealerCommentary').innerHTML =
            `<p class="text-sm text-yellow-300 italic">"${message}"</p>`;
    }
}

// Update lightning display
function updateLightningDisplay() {
    const intensity = Math.floor(lightningBaccaratGame.lightning.intensity * 100);

    if (lightningBaccaratGame.lightning.active) {
        document.getElementById('lightningStatus').innerHTML =
            `<div class="text-yellow-400 font-bold animate-pulse">LIGHTNING: ACTIVE (${intensity}%)</div>`;
        document.getElementById('lightningCharge').textContent = `${intensity}%`;
    } else {
        document.getElementById('lightningStatus').innerHTML =
            '<div class="text-yellow-400 font-bold">LIGHTNING: DORMANT</div>';
        document.getElementById('lightningCharge').textContent = '0%';
    }

    document.getElementById('stormLevel').innerHTML =
        `<div class="text-blue-400 font-bold">STORM LEVEL: ${lightningBaccaratGame.lightning.stormLevel}</div>`;

    if (Object.keys(lightningBaccaratGame.lightning.multipliers).length > 0) {
        document.getElementById('multiplierStatus').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">MULTIPLIERS: ACTIVE</div>';
    } else {
        document.getElementById('multiplierStatus').innerHTML =
            '<div class="text-purple-400 font-bold">MULTIPLIERS: INACTIVE</div>';
    }
}

// Update dealer display
function updateDealerDisplay() {
    const dealerData = DEALER_MODES[lightningBaccaratGame.dealerMode];
    document.getElementById('dealerMood').textContent =
        `${lightningBaccaratGame.dealer.mood} • ${dealerData.name}`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('playerBetDisplay').textContent = `$${lightningBaccaratGame.playerBet}`;
    document.getElementById('bankerBetDisplay').textContent = `$${lightningBaccaratGame.bankerBet}`;
    document.getElementById('tieBetDisplay').textContent = `$${lightningBaccaratGame.tieBet}`;
    document.getElementById('lightningBetDisplay').textContent = `$${lightningBaccaratGame.lightningBet}`;
    document.getElementById('totalBetDisplay').textContent = `$${lightningBaccaratGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = lightningBaccaratGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${lightningBaccaratGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${lightningBaccaratGame.stats.totalWagered}`;
    document.getElementById('lightningStrikes').textContent = lightningBaccaratGame.stats.lightningStrikes;
    document.getElementById('maxMultiplier').textContent = `${lightningBaccaratGame.stats.maxMultiplier}x`;

    const netResult = lightningBaccaratGame.stats.totalWon - lightningBaccaratGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    lightningBaccaratGame.stats.handsPlayed++;
    lightningBaccaratGame.stats.totalWagered += lightningBaccaratGame.totalBet;
    lightningBaccaratGame.stats.totalWon += winnings;

    if (won) {
        lightningBaccaratGame.stats.handsWon++;
        lightningBaccaratGame.stats.currentStreak++;
        lightningBaccaratGame.streakData.currentWinStreak++;
        lightningBaccaratGame.streakData.currentLossStreak = 0;

        if (lightningBaccaratGame.streakData.currentWinStreak > lightningBaccaratGame.streakData.longestWinStreak) {
            lightningBaccaratGame.streakData.longestWinStreak = lightningBaccaratGame.streakData.currentWinStreak;
        }

        if (winnings > lightningBaccaratGame.stats.biggestWin) {
            lightningBaccaratGame.stats.biggestWin = winnings;
        }
    } else {
        lightningBaccaratGame.stats.currentStreak = 0;
        lightningBaccaratGame.streakData.currentWinStreak = 0;
        lightningBaccaratGame.streakData.currentLossStreak++;

        if (lightningBaccaratGame.streakData.currentLossStreak > lightningBaccaratGame.streakData.longestLossStreak) {
            lightningBaccaratGame.streakData.longestLossStreak = lightningBaccaratGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to lightning effects)
    lightningBaccaratGame.stats.winRate = (lightningBaccaratGame.stats.handsWon / lightningBaccaratGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next hand
function resetGame() {
    lightningBaccaratGame.isPlaying = false;
    lightningBaccaratGame.playerBet = 0;
    lightningBaccaratGame.bankerBet = 0;
    lightningBaccaratGame.tieBet = 0;
    lightningBaccaratGame.lightningBet = 0;
    lightningBaccaratGame.totalBet = 0;
    lightningBaccaratGame.playerCards = [];
    lightningBaccaratGame.bankerCards = [];
    lightningBaccaratGame.playerScore = 0;
    lightningBaccaratGame.bankerScore = 0;
    lightningBaccaratGame.gameResult = '';
    lightningBaccaratGame.totalWin = 0;

    // Reset lightning system
    lightningBaccaratGame.lightning.active = false;
    lightningBaccaratGame.lightning.intensity = 0;
    lightningBaccaratGame.lightning.strikes = [];
    lightningBaccaratGame.lightning.multipliers = {};
    lightningBaccaratGame.lightning.chargedCards = [];
    lightningBaccaratGame.lightning.stormLevel = 0;
    lightningBaccaratGame.lightning.electricField = 0;
    lightningBaccaratGame.lightning.conductivity = 0;

    // Reset squeeze
    lightningBaccaratGame.squeeze.active = false;
    lightningBaccaratGame.squeeze.suspenseLevel = 0;
    lightningBaccaratGame.squeeze.revealProgress = 0;
    lightningBaccaratGame.squeeze.anticipation = 0;
    lightningBaccaratGame.squeeze.dramaticPause = false;

    // Clear displays
    document.getElementById('playerCards').innerHTML = '';
    document.getElementById('bankerCards').innerHTML = '';
    document.getElementById('playerScore').textContent = '0';
    document.getElementById('bankerScore').textContent = '0';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('lightningStrike').classList.add('hidden');
    document.getElementById('squeezeArea').classList.add('hidden');
    document.getElementById('activeMultipliers').innerHTML = '';
    document.getElementById('lightningBolts').innerHTML = '';

    // Reset bet inputs
    document.getElementById('playerBet').value = 0;
    document.getElementById('bankerBet').value = 0;
    document.getElementById('tieBet').value = 0;
    document.getElementById('lightningBet').value = 0;

    // Reset bet displays
    updateBetDisplay();

    // Enable deal button
    document.getElementById('dealCards').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Lightning is charging...';
    document.getElementById('gameMessage').textContent = 'Welcome to Lightning Baccarat Squeeze - Where Every Card Tells a Story';

    // Reset dealer commentary
    dealerSay("Ready for another electrifying hand?");

    // Reinitialize systems for next hand
    initializeLightningSystem();
    initializeDeck();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadLightningBaccaratGame();
});