// Roulette Relativity - Time-Dilation Ball Speed Mechanics
// Ultra High House Edge Implementation with Relativistic Physics
// Designed to maintain <1.5% player win rate

// Game state
let balance = 1000;

// Game state object with relativity system
let rouletteRelativityGame = {
    isPlaying: false,
    relativityMode: 'classical', // classical, special, general, quantum
    timeField: 'normal', // normal, dilated, accelerated, warped, singularity
    totalBet: 0,
    bets: {},

    // Ball physics with relativity
    ball: {
        position: 0,
        velocity: 0,
        acceleration: 0,
        mass: 1,
        timeDialation: 1.0,
        gravitationalField: 1.0,
        quantumState: 'classical',
        relativistic: false
    },

    // Wheel physics
    wheel: {
        position: 0,
        velocity: 0,
        timeField: 1.0,
        gravityWells: [],
        spacetimeCurvature: 0,
        eventHorizon: false
    },

    // Relativity system
    relativity: {
        active: false,
        lightSpeed: 299792458, // m/s (scaled for game)
        timeDialation: 1.0,
        lengthContraction: 1.0,
        gravitationalRedshift: 0,
        spacetimeCurvature: 0,
        blackHoles: [],
        wormholes: [],
        causalityViolations: 0
    },

    // Roulette numbers (European style with 0)
    numbers: [
        0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5,
        24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26
    ],

    winningNumber: null,
    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        timeDialationEvents: 0,
        causalityViolations: 0,
        blackHoleEncounters: 0,
        relativistic: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Relativity modes with extreme time-dilation bias
const RELATIVITY_MODES = {
    classical: {
        name: 'Classical Physics',
        houseEdge: 0.75, // 75% house edge
        timeDialationFactor: 0.15, // 15% time dilation
        payoutMultiplier: 0.22, // Severely reduced payouts
        gravitationalBias: 0.30, // 30% gravitational bias
        causalityStability: 0.85 // 85% causality stability
    },
    special: {
        name: 'Special Relativity',
        houseEdge: 0.82, // 82% house edge
        timeDialationFactor: 0.25, // 25% time dilation
        payoutMultiplier: 0.18, // Even more reduced payouts
        gravitationalBias: 0.42, // 42% gravitational bias
        causalityStability: 0.70 // 70% causality stability
    },
    general: {
        name: 'General Relativity',
        houseEdge: 0.88, // 88% house edge
        timeDialationFactor: 0.38, // 38% time dilation
        payoutMultiplier: 0.14, // Extremely reduced payouts
        gravitationalBias: 0.58, // 58% gravitational bias
        causalityStability: 0.55 // 55% causality stability
    },
    quantum: {
        name: 'Quantum Relativity',
        houseEdge: 0.94, // 94% house edge
        timeDialationFactor: 0.55, // 55% time dilation
        payoutMultiplier: 0.08, // Brutally reduced payouts
        gravitationalBias: 0.75, // 75% gravitational bias
        causalityStability: 0.35 // 35% causality stability
    }
};

const TIME_FIELDS = {
    normal: {
        name: 'Normal Time',
        dialationRate: 1.0, // No dilation
        ballBias: 0.25, // 25% ball bias
        numberDistortion: 0.20, // 20% number distortion
        gravityEffect: 0.15 // 15% gravity effect
    },
    dilated: {
        name: 'Time Dilation',
        dialationRate: 0.7, // 30% slower
        ballBias: 0.38, // 38% ball bias
        numberDistortion: 0.32, // 32% number distortion
        gravityEffect: 0.28 // 28% gravity effect
    },
    accelerated: {
        name: 'Time Acceleration',
        dialationRate: 1.5, // 50% faster
        ballBias: 0.52, // 52% ball bias
        numberDistortion: 0.45, // 45% number distortion
        gravityEffect: 0.42 // 42% gravity effect
    },
    warped: {
        name: 'Spacetime Warp',
        dialationRate: 0.4, // 60% slower
        ballBias: 0.68, // 68% ball bias
        numberDistortion: 0.62, // 62% number distortion
        gravityEffect: 0.58 // 58% gravity effect
    },
    singularity: {
        name: 'Event Horizon',
        dialationRate: 0.1, // 90% slower
        ballBias: 0.85, // 85% ball bias
        numberDistortion: 0.80, // 80% number distortion
        gravityEffect: 0.75 // 75% gravity effect
    }
};

// Severely reduced payout table with relativistic effects
const RELATIVITY_PAYOUTS = {
    // Straight up (single number) - heavily reduced
    STRAIGHT_UP: 20, // Reduced from 35:1

    // Split (two numbers) - reduced
    SPLIT: 10, // Reduced from 17:1

    // Street (three numbers) - reduced
    STREET: 6, // Reduced from 11:1

    // Corner (four numbers) - reduced
    CORNER: 4, // Reduced from 8:1

    // Six line (six numbers) - reduced
    SIX_LINE: 2.5, // Reduced from 5:1

    // Column/Dozen - reduced
    COLUMN: 1.2, // Reduced from 2:1
    DOZEN: 1.2, // Reduced from 2:1

    // Even money bets - heavily reduced
    RED_BLACK: 0.65, // Reduced from 1:1
    ODD_EVEN: 0.65, // Reduced from 1:1
    HIGH_LOW: 0.65, // Reduced from 1:1

    // Relativistic bonuses (fake - almost never apply)
    TIME_DILATION_BONUS: 0.05, // 5% of displayed bonus
    CAUSALITY_VIOLATION: 0.02, // 2% of displayed bonus
    BLACK_HOLE_ESCAPE: 0.01 // 1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadRouletteRelativityGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">RELATIVITY CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PHYSICS MODE</label>
                        <select id="relativityMode" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classical">Classical Physics</option>
                            <option value="special">Special Relativity</option>
                            <option value="general">General Relativity</option>
                            <option value="quantum">Quantum Relativity</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">TIME FIELD</label>
                        <select id="timeField" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="normal">Normal Time</option>
                            <option value="dilated" selected>Time Dilation</option>
                            <option value="accelerated">Time Acceleration</option>
                            <option value="warped">Spacetime Warp</option>
                            <option value="singularity">Event Horizon</option>
                        </select>
                    </div>

                    <button id="spinWheel" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        INITIATE SPACETIME SPIN
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                        <div id="totalBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Time Dilation</div>
                        <div id="timeDilation" class="text-lg font-bold text-orange-400">1.00x</div>
                    </div>
                </div>

                <!-- Relativity Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">RELATIVITY STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="relativityStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">PHYSICS: CLASSICAL</div>
                        </div>
                        <div id="timeFieldStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">TIME: NORMAL</div>
                        </div>
                        <div id="gravitationalField" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">GRAVITY: 1.0G</div>
                        </div>
                        <div id="causalityStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">CAUSALITY: STABLE</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">RELATIVISTIC PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Number Bets:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Straight Up:</span>
                            <span class="text-red-400">20:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Split:</span>
                            <span class="text-red-400">10:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Street:</span>
                            <span class="text-red-400">6:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Even Money:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Red/Black:</span>
                            <span class="text-red-400">0.65:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Odd/Even:</span>
                            <span class="text-red-400">0.65:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Time dilation affects payouts</div>
                        <div class="text-xs text-red-400">*Causality violations may void wins</div>
                    </div>
                </div>
            </div>

            <!-- Main Roulette Table -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <!-- Roulette Wheel with Relativity Effects -->
                    <div id="rouletteWheel" class="relative bg-gradient-to-br from-black via-orange-900 to-black rounded-full w-80 h-80 mx-auto mb-6 overflow-hidden">
                        <!-- Spacetime Curvature Visual Effect -->
                        <div id="spacetimeCurvature" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="relativityGradient" cx="50%" cy="50%" r="50%">
                                        <stop offset="0%" style="stop-color:#ff8000;stop-opacity:0.6" />
                                        <stop offset="50%" style="stop-color:#ff4000;stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
                                    </radialGradient>
                                    <pattern id="spacetimeGrid" width="15" height="15" patternUnits="userSpaceOnUse">
                                        <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#ff8000" stroke-width="0.3" opacity="0.5"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#spacetimeGrid)" />
                                <circle id="eventHorizon" cx="50%" cy="50%" r="40%" fill="url(#relativityGradient)" class="animate-pulse" />
                                <g id="gravityWells">
                                    <!-- Gravity wells will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Wheel Numbers -->
                        <div id="wheelNumbers" class="absolute inset-4 rounded-full border-4 border-orange-400">
                            <!-- Numbers will be generated here -->
                        </div>

                        <!-- Relativistic Ball -->
                        <div id="rouletteBall" class="absolute w-4 h-4 bg-gradient-to-br from-white to-orange-300 rounded-full shadow-lg transform transition-all duration-1000 hidden">
                            <div class="w-full h-full rounded-full animate-pulse ring-2 ring-orange-400"></div>
                        </div>

                        <!-- Time Dilation Effects -->
                        <div id="timeDilationEffects" class="absolute inset-0 pointer-events-none">
                            <!-- Time dilation visualization -->
                        </div>
                    </div>

                    <!-- Game Status -->
                    <div class="text-center mb-6">
                        <div id="gameStatus" class="text-2xl font-bold text-yellow-400 mb-2">Spacetime stabilizing...</div>
                        <div id="winningNumber" class="text-4xl font-bold text-orange-400 hidden">-</div>
                        <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                        <div id="relativisticEvent" class="text-sm font-bold text-orange-400 hidden animate-pulse">CAUSALITY VIOLATION DETECTED!</div>
                    </div>

                    <!-- Betting Board -->
                    <div id="bettingBoard" class="bg-gradient-to-br from-green-900 to-green-800 rounded-lg p-4">
                        <div class="grid grid-cols-12 gap-1 mb-4">
                            <!-- Numbers 1-36 -->
                            <div id="numberGrid" class="col-span-12 grid grid-cols-12 gap-1">
                                <!-- Number buttons will be generated here -->
                            </div>
                        </div>

                        <!-- Outside Bets -->
                        <div class="grid grid-cols-6 gap-2 mt-4">
                            <button class="bet-button bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" data-bet="red">
                                RED
                            </button>
                            <button class="bet-button bg-gray-800 hover:bg-gray-900 text-white font-bold py-2 px-4 rounded" data-bet="black">
                                BLACK
                            </button>
                            <button class="bet-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" data-bet="odd">
                                ODD
                            </button>
                            <button class="bet-button bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" data-bet="even">
                                EVEN
                            </button>
                            <button class="bet-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded" data-bet="low">
                                1-18
                            </button>
                            <button class="bet-button bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded" data-bet="high">
                                19-36
                            </button>
                        </div>

                        <!-- Bet Amount Controls -->
                        <div class="flex justify-center items-center space-x-4 mt-4">
                            <label class="text-white font-bold">Bet Amount:</label>
                            <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                                   class="w-20 bg-black/50 border border-orange-500/50 rounded px-2 py-1 text-white">
                            <button id="clearBets" class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded">
                                CLEAR
                            </button>
                        </div>
                    </div>

                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Roulette Relativity - Where Time Itself Bends Around the Wheel</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Time Dilations</div>
                <div id="timeDilationEvents" class="text-xl font-bold text-orange-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Black Holes</div>
                <div id="blackHoleEncounters" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeRouletteRelativity();
}

// Initialize the game
function initializeRouletteRelativity() {
    document.getElementById('spinWheel').addEventListener('click', spinWheel);
    document.getElementById('clearBets').addEventListener('click', clearAllBets);

    // Initialize relativity system and wheel
    initializeRelativitySystem();
    generateWheelNumbers();
    generateBettingBoard();
    updateGameStats();
}

// Initialize relativity system
function initializeRelativitySystem() {
    rouletteRelativityGame.relativity.active = false;
    rouletteRelativityGame.relativity.timeDialation = 1.0;
    rouletteRelativityGame.relativity.lengthContraction = 1.0;
    rouletteRelativityGame.relativity.gravitationalRedshift = 0;
    rouletteRelativityGame.relativity.spacetimeCurvature = 0;
    rouletteRelativityGame.relativity.blackHoles = [];
    rouletteRelativityGame.relativity.wormholes = [];
    rouletteRelativityGame.relativity.causalityViolations = 0;

    // Reset ball and wheel physics
    rouletteRelativityGame.ball.position = 0;
    rouletteRelativityGame.ball.velocity = 0;
    rouletteRelativityGame.ball.timeDialation = 1.0;
    rouletteRelativityGame.ball.gravitationalField = 1.0;
    rouletteRelativityGame.ball.quantumState = 'classical';
    rouletteRelativityGame.ball.relativistic = false;

    rouletteRelativityGame.wheel.position = 0;
    rouletteRelativityGame.wheel.velocity = 0;
    rouletteRelativityGame.wheel.timeField = 1.0;
    rouletteRelativityGame.wheel.gravityWells = [];
    rouletteRelativityGame.wheel.spacetimeCurvature = 0;
    rouletteRelativityGame.wheel.eventHorizon = false;

    updateRelativityDisplay();
}

// Generate wheel numbers with relativistic positioning
function generateWheelNumbers() {
    const container = document.getElementById('wheelNumbers');
    container.innerHTML = '';

    rouletteRelativityGame.numbers.forEach((number, index) => {
        const angle = (index / rouletteRelativityGame.numbers.length) * 360;
        const numberElement = document.createElement('div');

        numberElement.className = `absolute w-6 h-6 flex items-center justify-center text-xs font-bold rounded-full transform transition-all duration-500`;
        numberElement.style.transformOrigin = '50% 150px';
        numberElement.style.transform = `rotate(${angle}deg) translateY(-140px) rotate(-${angle}deg)`;

        // Color coding
        if (number === 0) {
            numberElement.className += ' bg-green-600 text-white';
        } else if ([1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(number)) {
            numberElement.className += ' bg-red-600 text-white';
        } else {
            numberElement.className += ' bg-black text-white border border-white';
        }

        numberElement.textContent = number;
        numberElement.dataset.number = number;

        container.appendChild(numberElement);
    });
}

// Generate betting board
function generateBettingBoard() {
    const container = document.getElementById('numberGrid');
    container.innerHTML = '';

    // Add 0 button
    const zeroButton = document.createElement('button');
    zeroButton.className = 'bet-button bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-2 rounded text-sm';
    zeroButton.textContent = '0';
    zeroButton.dataset.bet = '0';
    zeroButton.addEventListener('click', () => placeBet('0'));
    container.appendChild(zeroButton);

    // Add numbers 1-36
    for (let i = 1; i <= 36; i++) {
        const button = document.createElement('button');
        const isRed = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(i);

        button.className = `bet-button ${isRed ? 'bg-red-600 hover:bg-red-700' : 'bg-black hover:bg-gray-800'} text-white font-bold py-2 px-2 rounded text-sm border border-white`;
        button.textContent = i;
        button.dataset.bet = i.toString();
        button.addEventListener('click', () => placeBet(i.toString()));

        container.appendChild(button);
    }

    // Add event listeners for outside bets
    document.querySelectorAll('.bet-button[data-bet]').forEach(button => {
        if (!button.onclick) {
            button.addEventListener('click', () => placeBet(button.dataset.bet));
        }
    });
}

// Place bet with relativistic effects
function placeBet(betType) {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount <= 0 || betAmount > balance) {
        alert('Invalid bet amount!');
        return;
    }

    if (rouletteRelativityGame.isPlaying) {
        alert('Cannot place bets while wheel is spinning!');
        return;
    }

    // Initialize bet if not exists
    if (!rouletteRelativityGame.bets[betType]) {
        rouletteRelativityGame.bets[betType] = 0;
    }

    // Add to existing bet
    rouletteRelativityGame.bets[betType] += betAmount;
    rouletteRelativityGame.totalBet += betAmount;

    // Deduct from balance
    balance -= betAmount;
    updateBalance();
    updateBetDisplay();

    // Visual feedback
    const button = document.querySelector(`[data-bet="${betType}"]`);
    if (button) {
        button.classList.add('ring-2', 'ring-yellow-400');
        const betChip = document.createElement('div');
        betChip.className = 'absolute top-0 right-0 bg-yellow-400 text-black text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center';
        betChip.textContent = `$${rouletteRelativityGame.bets[betType]}`;
        button.style.position = 'relative';
        button.appendChild(betChip);
    }
}

// Clear all bets
function clearAllBets() {
    if (rouletteRelativityGame.isPlaying) {
        alert('Cannot clear bets while wheel is spinning!');
        return;
    }

    // Return money to balance
    balance += rouletteRelativityGame.totalBet;

    // Clear bets
    rouletteRelativityGame.bets = {};
    rouletteRelativityGame.totalBet = 0;

    // Clear visual indicators
    document.querySelectorAll('.bet-button').forEach(button => {
        button.classList.remove('ring-2', 'ring-yellow-400');
        const chip = button.querySelector('.bg-yellow-400');
        if (chip) chip.remove();
    });

    updateBalance();
    updateBetDisplay();
}

// Spin wheel with relativistic physics
function spinWheel() {
    if (rouletteRelativityGame.totalBet === 0) {
        alert('Please place at least one bet!');
        return;
    }

    if (rouletteRelativityGame.isPlaying) {
        return;
    }

    rouletteRelativityGame.isPlaying = true;
    rouletteRelativityGame.relativityMode = document.getElementById('relativityMode').value;
    rouletteRelativityGame.timeField = document.getElementById('timeField').value;

    // Activate relativity system
    activateRelativitySystem();

    // Start relativistic spin
    setTimeout(() => {
        performRelativisticSpin();
    }, 2000);

    document.getElementById('spinWheel').disabled = true;
    document.getElementById('gameStatus').textContent = 'Initiating spacetime distortion...';
    updateGameStats();
}

// Activate relativity system
function activateRelativitySystem() {
    const modeData = RELATIVITY_MODES[rouletteRelativityGame.relativityMode];
    const timeData = TIME_FIELDS[rouletteRelativityGame.timeField];

    rouletteRelativityGame.relativity.active = true;
    rouletteRelativityGame.relativity.timeDialation = timeData.dialationRate;
    rouletteRelativityGame.relativity.spacetimeCurvature = Math.random() * modeData.gravitationalBias;

    // Activate time dilation effects
    if (Math.random() < modeData.timeDialationFactor) {
        activateTimeDilation();
    }

    // Create gravity wells
    if (Math.random() < modeData.gravitationalBias) {
        createGravityWells();
    }

    // Check for causality violations
    if (Math.random() < (1 - modeData.causalityStability)) {
        activateCausalityViolation();
    }

    // Generate black holes
    if (Math.random() < modeData.gravitationalBias * 0.3) {
        generateBlackHoles();
    }

    // Update visual effects
    updateRelativityDisplay();
    updateSpacetimeEffects();
}

// Activate time dilation
function activateTimeDilation() {
    rouletteRelativityGame.ball.timeDialation = rouletteRelativityGame.relativity.timeDialation;
    rouletteRelativityGame.stats.timeDialationEvents++;

    document.getElementById('timeFieldStatus').innerHTML =
        `<div class="text-blue-400 font-bold animate-pulse">TIME: DILATED (${rouletteRelativityGame.ball.timeDialation.toFixed(2)}x)</div>`;

    // Visual time dilation effects
    const timeDilationContainer = document.getElementById('timeDilationEffects');
    timeDilationContainer.innerHTML = '';

    for (let i = 0; i < 5; i++) {
        const wave = document.createElement('div');
        wave.className = 'absolute inset-0 border-2 border-blue-400 rounded-full animate-ping opacity-20';
        wave.style.animationDelay = `${i * 0.5}s`;
        wave.style.animationDuration = `${2 / rouletteRelativityGame.ball.timeDialation}s`;
        timeDilationContainer.appendChild(wave);
    }
}

// Create gravity wells
function createGravityWells() {
    const wellCount = Math.floor(Math.random() * 4) + 2; // 2-5 gravity wells
    rouletteRelativityGame.wheel.gravityWells = [];

    const container = document.getElementById('gravityWells');
    container.innerHTML = '';

    for (let i = 0; i < wellCount; i++) {
        const well = {
            x: Math.random() * 100,
            y: Math.random() * 100,
            strength: Math.random() * 0.8 + 0.2,
            radius: Math.random() * 20 + 10
        };

        rouletteRelativityGame.wheel.gravityWells.push(well);

        // Visual representation
        const wellElement = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        wellElement.setAttribute('cx', `${well.x}%`);
        wellElement.setAttribute('cy', `${well.y}%`);
        wellElement.setAttribute('r', well.radius);
        wellElement.setAttribute('fill', 'url(#relativityGradient)');
        wellElement.setAttribute('opacity', well.strength);
        wellElement.classList.add('animate-pulse');
        container.appendChild(wellElement);
    }
}

// Activate causality violation
function activateCausalityViolation() {
    rouletteRelativityGame.relativity.causalityViolations++;
    rouletteRelativityGame.stats.causalityViolations++;

    document.getElementById('causalityStatus').innerHTML =
        '<div class="text-red-400 font-bold animate-pulse">CAUSALITY: VIOLATED</div>';

    // Show relativistic event
    document.getElementById('relativisticEvent').classList.remove('hidden');
    setTimeout(() => {
        document.getElementById('relativisticEvent').classList.add('hidden');
    }, 3000);
}

// Generate black holes
function generateBlackHoles() {
    const blackHoleCount = Math.floor(Math.random() * 2) + 1; // 1-2 black holes
    rouletteRelativityGame.relativity.blackHoles = [];
    rouletteRelativityGame.stats.blackHoleEncounters++;

    for (let i = 0; i < blackHoleCount; i++) {
        const blackHole = {
            x: Math.random() * 100,
            y: Math.random() * 100,
            mass: Math.random() * 10 + 5,
            eventHorizon: Math.random() * 30 + 20
        };

        rouletteRelativityGame.relativity.blackHoles.push(blackHole);
    }

    // Activate event horizon
    rouletteRelativityGame.wheel.eventHorizon = true;
    document.getElementById('eventHorizon').style.opacity = '0.8';
}

// Perform relativistic spin
function performRelativisticSpin() {
    const modeData = RELATIVITY_MODES[rouletteRelativityGame.relativityMode];
    const timeData = TIME_FIELDS[rouletteRelativityGame.timeField];

    document.getElementById('gameStatus').textContent = 'Ball entering relativistic trajectory...';

    // Show ball
    const ball = document.getElementById('rouletteBall');
    ball.classList.remove('hidden');

    // Calculate relativistic trajectory with extreme bias
    let targetNumber = selectRelativisticNumber();

    // Apply time dilation to spin duration
    const baseDuration = 4000; // 4 seconds
    const relativisticDuration = baseDuration * rouletteRelativityGame.ball.timeDialation;

    // Animate ball with relativistic effects
    animateRelativisticBall(targetNumber, relativisticDuration);

    // Resolve after animation
    setTimeout(() => {
        resolveSpinWithRelativity(targetNumber);
    }, relativisticDuration + 1000);
}

// Select number with relativistic bias
function selectRelativisticNumber() {
    const modeData = RELATIVITY_MODES[rouletteRelativityGame.relativityMode];
    const timeData = TIME_FIELDS[rouletteRelativityGame.timeField];

    // Apply extreme gravitational bias toward house-favorable numbers
    if (Math.random() < timeData.ballBias) {
        // Gravity wells attract ball to specific numbers
        const houseFavorableNumbers = [];

        // Check which bets player made and bias against them
        Object.keys(rouletteRelativityGame.bets).forEach(betType => {
            if (betType === 'red') {
                // If player bet red, bias toward black numbers
                houseFavorableNumbers.push(...[2,4,6,8,10,11,13,15,17,20,22,24,26,28,29,31,33,35]);
            } else if (betType === 'black') {
                // If player bet black, bias toward red numbers
                houseFavorableNumbers.push(...[1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36]);
            } else if (betType === 'odd') {
                // If player bet odd, bias toward even numbers
                houseFavorableNumbers.push(...[2,4,6,8,10,12,14,16,18,20,22,24,26,28,30,32,34,36]);
            } else if (betType === 'even') {
                // If player bet even, bias toward odd numbers
                houseFavorableNumbers.push(...[1,3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33,35]);
            } else if (betType === 'low') {
                // If player bet low, bias toward high numbers
                houseFavorableNumbers.push(...[19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36]);
            } else if (betType === 'high') {
                // If player bet high, bias toward low numbers
                houseFavorableNumbers.push(...[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18]);
            } else if (!isNaN(betType)) {
                // If player bet specific number, avoid it
                const playerNumber = parseInt(betType);
                houseFavorableNumbers.push(...rouletteRelativityGame.numbers.filter(n => n !== playerNumber));
            }
        });

        // Always include 0 as house favorable
        houseFavorableNumbers.push(0);

        if (houseFavorableNumbers.length > 0) {
            return houseFavorableNumbers[Math.floor(Math.random() * houseFavorableNumbers.length)];
        }
    }

    // Apply additional spacetime curvature bias
    if (Math.random() < timeData.numberDistortion) {
        // Spacetime curvature bends ball toward 0
        if (Math.random() < 0.4) {
            return 0;
        }
    }

    // Fallback to random (but still biased by gravity wells)
    let selectedNumber = rouletteRelativityGame.numbers[Math.floor(Math.random() * rouletteRelativityGame.numbers.length)];

    // Apply gravity well effects
    rouletteRelativityGame.wheel.gravityWells.forEach(well => {
        if (Math.random() < well.strength * 0.3) {
            // Gravity well pulls ball toward house-favorable numbers
            const nearbyNumbers = [0, 32, 15, 19, 4]; // Numbers near 0
            selectedNumber = nearbyNumbers[Math.floor(Math.random() * nearbyNumbers.length)];
        }
    });

    return selectedNumber;
}

// Animate ball with relativistic effects
function animateRelativisticBall(targetNumber, duration) {
    const ball = document.getElementById('rouletteBall');
    const wheel = document.getElementById('wheelNumbers');

    // Calculate target position
    const targetIndex = rouletteRelativityGame.numbers.indexOf(targetNumber);
    const targetAngle = (targetIndex / rouletteRelativityGame.numbers.length) * 360;

    // Apply relativistic effects to animation
    const timeDialation = rouletteRelativityGame.ball.timeDialation;
    const adjustedDuration = duration / timeDialation;

    // Start ball animation
    let currentAngle = 0;
    const totalRotations = 5 + Math.random() * 3; // 5-8 rotations
    const finalAngle = totalRotations * 360 + targetAngle;

    ball.style.transition = `transform ${adjustedDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;

    // Apply relativistic distortion to ball path
    if (rouletteRelativityGame.wheel.gravityWells.length > 0) {
        // Gravity wells affect ball trajectory
        ball.style.transform = `rotate(${finalAngle}deg) translateY(-140px) scale(${1 / Math.sqrt(timeDialation)})`;
    } else {
        ball.style.transform = `rotate(${finalAngle}deg) translateY(-140px)`;
    }

    // Add relativistic visual effects
    if (timeDialation !== 1.0) {
        ball.style.filter = `blur(${Math.abs(1 - timeDialation) * 2}px) brightness(${1 + Math.abs(1 - timeDialation)})`;
    }

    // Animate wheel counter-rotation
    wheel.style.transition = `transform ${adjustedDuration}ms ease-out`;
    wheel.style.transform = `rotate(-${finalAngle * 0.3}deg)`;
}

// Resolve spin with relativity effects and extreme house bias
function resolveSpinWithRelativity(winningNumber) {
    const modeData = RELATIVITY_MODES[rouletteRelativityGame.relativityMode];
    const timeData = TIME_FIELDS[rouletteRelativityGame.timeField];

    rouletteRelativityGame.winningNumber = winningNumber;
    let totalWinnings = 0;
    let resultMessage = '';

    // Apply causality violation (can change the winning number)
    if (rouletteRelativityGame.relativity.causalityViolations > 0 && Math.random() < 0.3) {
        // Causality violation changes the outcome
        const alternativeNumbers = rouletteRelativityGame.numbers.filter(n => n !== winningNumber);
        winningNumber = alternativeNumbers[Math.floor(Math.random() * alternativeNumbers.length)];
        rouletteRelativityGame.winningNumber = winningNumber;
        resultMessage += 'CAUSALITY VIOLATION - ';
    }

    // Calculate winnings for each bet with severe reductions
    Object.entries(rouletteRelativityGame.bets).forEach(([betType, betAmount]) => {
        let payout = 0;
        let won = false;

        if (betType === winningNumber.toString()) {
            // Straight up bet
            payout = betAmount * RELATIVITY_PAYOUTS.STRAIGHT_UP;
            won = true;
        } else if (betType === 'red' && isRedNumber(winningNumber)) {
            payout = betAmount * RELATIVITY_PAYOUTS.RED_BLACK;
            won = true;
        } else if (betType === 'black' && isBlackNumber(winningNumber)) {
            payout = betAmount * RELATIVITY_PAYOUTS.RED_BLACK;
            won = true;
        } else if (betType === 'odd' && winningNumber % 2 === 1 && winningNumber !== 0) {
            payout = betAmount * RELATIVITY_PAYOUTS.ODD_EVEN;
            won = true;
        } else if (betType === 'even' && winningNumber % 2 === 0 && winningNumber !== 0) {
            payout = betAmount * RELATIVITY_PAYOUTS.ODD_EVEN;
            won = true;
        } else if (betType === 'low' && winningNumber >= 1 && winningNumber <= 18) {
            payout = betAmount * RELATIVITY_PAYOUTS.HIGH_LOW;
            won = true;
        } else if (betType === 'high' && winningNumber >= 19 && winningNumber <= 36) {
            payout = betAmount * RELATIVITY_PAYOUTS.HIGH_LOW;
            won = true;
        }

        // Apply relativistic bonuses (fake - almost never actually applied)
        if (won && payout > 0) {
            // Time dilation bonus (almost never applies)
            if (rouletteRelativityGame.ball.timeDialation !== 1.0 && Math.random() < 0.02) {
                const dilationBonus = Math.floor(payout * RELATIVITY_PAYOUTS.TIME_DILATION_BONUS);
                payout += dilationBonus;
            }

            // Causality violation bonus (almost never applies)
            if (rouletteRelativityGame.relativity.causalityViolations > 0 && Math.random() < 0.01) {
                const causalityBonus = Math.floor(payout * RELATIVITY_PAYOUTS.CAUSALITY_VIOLATION);
                payout += causalityBonus;
            }
        }

        totalWinnings += payout;
    });

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * modeData.payoutMultiplier);

    // Apply time dilation penalty
    const timePenalty = Math.abs(1 - rouletteRelativityGame.ball.timeDialation) * 0.4;
    totalWinnings = Math.floor(totalWinnings * (1 - timePenalty));

    // Apply gravity well penalty
    if (rouletteRelativityGame.wheel.gravityWells.length > 0) {
        const gravityPenalty = rouletteRelativityGame.wheel.gravityWells.length * 0.1;
        totalWinnings = Math.floor(totalWinnings * (1 - gravityPenalty));
    }

    // Black hole event horizon (voids all wins)
    if (rouletteRelativityGame.wheel.eventHorizon && Math.random() < 0.25) {
        totalWinnings = 0;
        resultMessage += 'BLACK HOLE CONSUMED WINNINGS - ';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    rouletteRelativityGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(totalWinnings > 0, totalWinnings);

    document.getElementById('winningNumber').textContent = winningNumber;
    document.getElementById('winningNumber').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = `${resultMessage}Ball settled in spacetime`;
    document.getElementById('gameMessage').innerHTML = `Winning Number: ${winningNumber} - Time Dilation: ${rouletteRelativityGame.ball.timeDialation.toFixed(2)}x`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Check if number is red
function isRedNumber(number) {
    return [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(number);
}

// Check if number is black
function isBlackNumber(number) {
    return number !== 0 && !isRedNumber(number);
}

// Update relativity display
function updateRelativityDisplay() {
    const modeData = RELATIVITY_MODES[rouletteRelativityGame.relativityMode];
    const timeData = TIME_FIELDS[rouletteRelativityGame.timeField];

    if (rouletteRelativityGame.relativity.active) {
        document.getElementById('relativityStatus').innerHTML =
            `<div class="text-orange-400 font-bold animate-pulse">PHYSICS: ${modeData.name.toUpperCase()}</div>`;
        document.getElementById('timeDilation').textContent = `${rouletteRelativityGame.relativity.timeDialation.toFixed(2)}x`;
    } else {
        document.getElementById('relativityStatus').innerHTML =
            '<div class="text-orange-400 font-bold">PHYSICS: CLASSICAL</div>';
        document.getElementById('timeDilation').textContent = '1.00x';
    }

    document.getElementById('gravitationalField').innerHTML =
        `<div class="text-purple-400 font-bold">GRAVITY: ${rouletteRelativityGame.ball.gravitationalField.toFixed(1)}G</div>`;
}

// Update spacetime effects visualization
function updateSpacetimeEffects() {
    // Update spacetime curvature visualization based on current state
    const eventHorizon = document.getElementById('eventHorizon');
    if (eventHorizon) {
        if (rouletteRelativityGame.wheel.eventHorizon) {
            eventHorizon.setAttribute('r', '60%');
            eventHorizon.classList.add('animate-spin');
        } else {
            eventHorizon.setAttribute('r', '40%');
            eventHorizon.classList.remove('animate-spin');
        }
    }
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('totalBetDisplay').textContent = `$${rouletteRelativityGame.totalBet}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = rouletteRelativityGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${rouletteRelativityGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${rouletteRelativityGame.stats.totalWagered}`;
    document.getElementById('timeDilationEvents').textContent = rouletteRelativityGame.stats.timeDialationEvents;
    document.getElementById('blackHoleEncounters').textContent = rouletteRelativityGame.stats.blackHoleEncounters;

    const netResult = rouletteRelativityGame.stats.totalWon - rouletteRelativityGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    rouletteRelativityGame.stats.spinsPlayed++;
    rouletteRelativityGame.stats.totalWagered += rouletteRelativityGame.totalBet;
    rouletteRelativityGame.stats.totalWon += winnings;

    if (won) {
        rouletteRelativityGame.stats.spinsWon++;
        rouletteRelativityGame.stats.currentStreak++;
        rouletteRelativityGame.streakData.currentWinStreak++;
        rouletteRelativityGame.streakData.currentLossStreak = 0;

        if (rouletteRelativityGame.streakData.currentWinStreak > rouletteRelativityGame.streakData.longestWinStreak) {
            rouletteRelativityGame.streakData.longestWinStreak = rouletteRelativityGame.streakData.currentWinStreak;
        }

        if (winnings > rouletteRelativityGame.stats.biggestWin) {
            rouletteRelativityGame.stats.biggestWin = winnings;
        }
    } else {
        rouletteRelativityGame.stats.currentStreak = 0;
        rouletteRelativityGame.streakData.currentWinStreak = 0;
        rouletteRelativityGame.streakData.currentLossStreak++;

        if (rouletteRelativityGame.streakData.currentLossStreak > rouletteRelativityGame.streakData.longestLossStreak) {
            rouletteRelativityGame.streakData.longestLossStreak = rouletteRelativityGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to relativistic effects)
    rouletteRelativityGame.stats.winRate = (rouletteRelativityGame.stats.spinsWon / rouletteRelativityGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next spin
function resetGame() {
    rouletteRelativityGame.isPlaying = false;
    rouletteRelativityGame.bets = {};
    rouletteRelativityGame.totalBet = 0;
    rouletteRelativityGame.winningNumber = null;
    rouletteRelativityGame.gameResult = '';
    rouletteRelativityGame.totalWin = 0;

    // Reset relativity system
    rouletteRelativityGame.relativity.active = false;
    rouletteRelativityGame.relativity.timeDialation = 1.0;
    rouletteRelativityGame.relativity.lengthContraction = 1.0;
    rouletteRelativityGame.relativity.gravitationalRedshift = 0;
    rouletteRelativityGame.relativity.spacetimeCurvature = 0;
    rouletteRelativityGame.relativity.blackHoles = [];
    rouletteRelativityGame.relativity.wormholes = [];
    rouletteRelativityGame.relativity.causalityViolations = 0;

    // Reset ball and wheel physics
    rouletteRelativityGame.ball.position = 0;
    rouletteRelativityGame.ball.velocity = 0;
    rouletteRelativityGame.ball.timeDialation = 1.0;
    rouletteRelativityGame.ball.gravitationalField = 1.0;
    rouletteRelativityGame.ball.quantumState = 'classical';
    rouletteRelativityGame.ball.relativistic = false;

    rouletteRelativityGame.wheel.position = 0;
    rouletteRelativityGame.wheel.velocity = 0;
    rouletteRelativityGame.wheel.timeField = 1.0;
    rouletteRelativityGame.wheel.gravityWells = [];
    rouletteRelativityGame.wheel.spacetimeCurvature = 0;
    rouletteRelativityGame.wheel.eventHorizon = false;

    // Clear displays
    document.getElementById('winningNumber').classList.add('hidden');
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('relativisticEvent').classList.add('hidden');
    document.getElementById('rouletteBall').classList.add('hidden');
    document.getElementById('timeDilationEffects').innerHTML = '';
    document.getElementById('gravityWells').innerHTML = '';

    // Reset wheel and ball positions
    const ball = document.getElementById('rouletteBall');
    const wheel = document.getElementById('wheelNumbers');
    ball.style.transform = 'rotate(0deg) translateY(-140px)';
    ball.style.transition = 'none';
    ball.style.filter = 'none';
    wheel.style.transform = 'rotate(0deg)';
    wheel.style.transition = 'none';

    // Reset relativity status
    document.getElementById('relativityStatus').innerHTML =
        '<div class="text-orange-400 font-bold">PHYSICS: CLASSICAL</div>';
    document.getElementById('timeFieldStatus').innerHTML =
        '<div class="text-blue-400 font-bold">TIME: NORMAL</div>';
    document.getElementById('gravitationalField').innerHTML =
        '<div class="text-purple-400 font-bold">GRAVITY: 1.0G</div>';
    document.getElementById('causalityStatus').innerHTML =
        '<div class="text-green-400 font-bold">CAUSALITY: STABLE</div>';

    // Clear bet visual indicators
    document.querySelectorAll('.bet-button').forEach(button => {
        button.classList.remove('ring-2', 'ring-yellow-400');
        const chip = button.querySelector('.bg-yellow-400');
        if (chip) chip.remove();
    });

    // Reset bet displays
    updateBetDisplay();

    // Enable spin button
    document.getElementById('spinWheel').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Spacetime stabilizing...';
    document.getElementById('gameMessage').textContent = 'Welcome to Roulette Relativity - Where Time Itself Bends Around the Wheel';

    // Reset event horizon
    document.getElementById('eventHorizon').style.opacity = '0.4';
    document.getElementById('eventHorizon').setAttribute('r', '40%');
    document.getElementById('eventHorizon').classList.remove('animate-spin');

    // Reinitialize systems for next spin
    initializeRelativitySystem();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRouletteRelativityGame();
});