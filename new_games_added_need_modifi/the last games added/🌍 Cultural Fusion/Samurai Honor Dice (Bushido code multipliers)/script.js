// Samurai Honor Dice - Bushido Code Multipliers
// Ultra High House Edge Implementation with Japanese Samurai Theme
// Designed to maintain <0.2% player win rate

// Game state
let balance = 1000;

// Game state object with samurai honor system
let samuraiHonorGame = {
    isPlaying: false,
    bushidoPath: 'gi', // gi, rei, yu, jin, makoto, meiyo, chugi
    honorLevel: 'ashigaru', // ashigaru, samurai, hatamoto, daimyo, shogun
    betAmount: 0,
    totalBet: 0,

    // Samurai honor system
    honor: {
        currentLevel: 0,
        maxLevel: 100,
        bushidoVirtues: {
            gi: 0,      // Rectitude/Justice
            rei: 0,     // Respect/Courtesy
            yu: 0,      // Courage
            jin: 0,     // Benevolence
            makoto: 0,  // Honesty
            meiyo: 0,   // Honor
            chugi: 0    // Loyalty
        },
        karma: 0,
        dishonor: 0,
        seppuku: false,
        ancestors: 0
    },

    // Japanese elements
    japan: {
        era: 'Edo Period',
        clan: 'Tokugawa',
        daimyo: 'Tokugawa Ieyasu',
        castle: 'Edo Castle',
        season: 'Spring',
        weather: 'Cherry Blossoms',
        temple: 'Sensoji',
        mountain: 'Mount Fuji',
        philosophy: 'Zen Buddhism'
    },

    // Dice system (5 dice with Japanese symbols)
    dice: [
        { value: 1, symbol: '一', locked: false, honorBonus: false },
        { value: 1, symbol: '一', locked: false, honorBonus: false },
        { value: 1, symbol: '一', locked: false, honorBonus: false },
        { value: 1, symbol: '一', locked: false, honorBonus: false },
        { value: 1, symbol: '一', locked: false, honorBonus: false }
    ],

    // Bushido code multipliers system
    bushidoMultipliers: {
        active: false,
        currentVirtue: 'gi',
        multiplierValue: 1.0,
        honorRequirement: 50,
        virtueAlignment: 0,
        ancestralBlessing: false,
        karmaBalance: 0,
        dishonorPenalty: 0,
        seppukuRisk: 0.05 // 5% seppuku risk (fake)
    },

    // Japanese dice symbols and their honor values
    symbols: [
        { name: 'Katana', value: 6, honor: 10, virtue: 'yu', rarity: 0.08 },
        { name: 'Cherry Blossom', value: 5, honor: 8, virtue: 'meiyo', rarity: 0.12 },
        { name: 'Torii Gate', value: 4, honor: 6, virtue: 'rei', rarity: 0.16 },
        { name: 'Dragon', value: 3, honor: 4, virtue: 'jin', rarity: 0.20 },
        { name: 'Crane', value: 2, honor: 2, virtue: 'makoto', rarity: 0.24 },
        { name: 'Bamboo', value: 1, honor: 1, virtue: 'gi', rarity: 0.20 }
    ],

    rollsRemaining: 3,
    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        bushidoActivations: 0,
        honorEarned: 0,
        seppukuPerformed: 0,
        ancestralBlessings: 0,
        perfectHonor: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Bushido paths with extreme honor bias
const BUSHIDO_PATHS = {
    gi: {
        name: 'Gi (Rectitude)',
        houseEdge: 0.92, // 92% house edge
        honorRequirement: 0.60, // 60% honor requirement
        payoutMultiplier: 0.08, // Severely reduced payouts
        virtueAlignment: 0.15, // 15% virtue alignment
        dishonorRisk: 0.75 // 75% dishonor risk
    },
    rei: {
        name: 'Rei (Respect)',
        houseEdge: 0.94, // 94% house edge
        honorRequirement: 0.70, // 70% honor requirement
        payoutMultiplier: 0.06, // Even more reduced payouts
        virtueAlignment: 0.12, // 12% virtue alignment
        dishonorRisk: 0.82 // 82% dishonor risk
    },
    yu: {
        name: 'Yu (Courage)',
        houseEdge: 0.96, // 96% house edge
        honorRequirement: 0.80, // 80% honor requirement
        payoutMultiplier: 0.05, // Extremely reduced payouts
        virtueAlignment: 0.10, // 10% virtue alignment
        dishonorRisk: 0.88 // 88% dishonor risk
    },
    jin: {
        name: 'Jin (Benevolence)',
        houseEdge: 0.97, // 97% house edge
        honorRequirement: 0.85, // 85% honor requirement
        payoutMultiplier: 0.04, // Brutally reduced payouts
        virtueAlignment: 0.08, // 8% virtue alignment
        dishonorRisk: 0.92 // 92% dishonor risk
    },
    makoto: {
        name: 'Makoto (Honesty)',
        houseEdge: 0.98, // 98% house edge
        honorRequirement: 0.90, // 90% honor requirement
        payoutMultiplier: 0.03, // Impossibly reduced payouts
        virtueAlignment: 0.06, // 6% virtue alignment
        dishonorRisk: 0.95 // 95% dishonor risk
    },
    meiyo: {
        name: 'Meiyo (Honor)',
        houseEdge: 0.99, // 99% house edge
        honorRequirement: 0.95, // 95% honor requirement
        payoutMultiplier: 0.02, // Impossibly reduced payouts
        virtueAlignment: 0.04, // 4% virtue alignment
        dishonorRisk: 0.98 // 98% dishonor risk
    },
    chugi: {
        name: 'Chugi (Loyalty)',
        houseEdge: 0.995, // 99.5% house edge
        honorRequirement: 0.98, // 98% honor requirement
        payoutMultiplier: 0.01, // Impossibly reduced payouts
        virtueAlignment: 0.02, // 2% virtue alignment
        dishonorRisk: 0.99 // 99% dishonor risk
    }
};

const HONOR_LEVELS = {
    ashigaru: {
        name: 'Ashigaru (Foot Soldier)',
        multiplier: 0.8,
        honorThreshold: 0,
        dishonorPenalty: 0.30
    },
    samurai: {
        name: 'Samurai (Warrior)',
        multiplier: 0.9,
        honorThreshold: 20,
        dishonorPenalty: 0.40
    },
    hatamoto: {
        name: 'Hatamoto (Bannerman)',
        multiplier: 1.0,
        honorThreshold: 40,
        dishonorPenalty: 0.50
    },
    daimyo: {
        name: 'Daimyo (Lord)',
        multiplier: 1.1,
        honorThreshold: 70,
        dishonorPenalty: 0.60
    },
    shogun: {
        name: 'Shogun (General)',
        multiplier: 1.2,
        honorThreshold: 90,
        dishonorPenalty: 0.70
    }
};

// Severely reduced payout table with samurai theme
const SAMURAI_PAYOUTS = {
    // Dice combinations (heavily reduced)
    FIVE_KATANA: 100, // Reduced from 10000:1
    FOUR_KATANA: 50, // Reduced from 5000:1
    THREE_KATANA: 25, // Reduced from 1000:1

    FIVE_CHERRY: 80, // Reduced from 8000:1
    FOUR_CHERRY: 40, // Reduced from 4000:1
    THREE_CHERRY: 20, // Reduced from 800:1

    FIVE_TORII: 60, // Reduced from 6000:1
    FOUR_TORII: 30, // Reduced from 3000:1
    THREE_TORII: 15, // Reduced from 600:1

    FIVE_DRAGON: 40, // Reduced from 4000:1
    FOUR_DRAGON: 20, // Reduced from 2000:1
    THREE_DRAGON: 10, // Reduced from 400:1

    FIVE_CRANE: 20, // Reduced from 2000:1
    FOUR_CRANE: 10, // Reduced from 1000:1
    THREE_CRANE: 5, // Reduced from 200:1

    FIVE_BAMBOO: 10, // Reduced from 1000:1
    FOUR_BAMBOO: 5, // Reduced from 500:1
    THREE_BAMBOO: 2, // Reduced from 100:1

    // Bushido bonuses (fake - almost never apply)
    BUSHIDO_HONOR: 0.05, // 5% of displayed bonus
    ANCESTRAL_BLESSING: 0.02, // 2% of displayed bonus
    PERFECT_VIRTUE: 0.01 // 1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadSamuraiHonorGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">BUSHIDO CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BUSHIDO PATH</label>
                        <select id="bushidoPath" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="gi">Gi (Rectitude)</option>
                            <option value="rei">Rei (Respect)</option>
                            <option value="yu">Yu (Courage)</option>
                            <option value="jin">Jin (Benevolence)</option>
                            <option value="makoto">Makoto (Honesty)</option>
                            <option value="meiyo">Meiyo (Honor)</option>
                            <option value="chugi">Chugi (Loyalty)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">HONOR LEVEL</label>
                        <select id="honorLevel" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="ashigaru">Ashigaru (Foot Soldier)</option>
                            <option value="samurai" selected>Samurai (Warrior)</option>
                            <option value="hatamoto">Hatamoto (Bannerman)</option>
                            <option value="daimyo">Daimyo (Lord)</option>
                            <option value="shogun">Shogun (General)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="rollDice" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ROLL HONOR DICE
                    </button>

                    <div id="diceActions" class="space-y-2 hidden">
                        <button id="rerollAll" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            REROLL ALL
                        </button>
                        <button id="rerollSelected" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            REROLL SELECTED
                        </button>
                        <button id="holdDice" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            HOLD DICE
                        </button>
                        <button id="commitSeppuku" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            COMMIT SEPPUKU
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Honor Level</div>
                        <div id="honorLevelDisplay" class="text-lg font-bold text-red-400">0</div>
                    </div>
                </div>

                <!-- Samurai Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">SAMURAI STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="clanStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">CLAN: TOKUGAWA</div>
                        </div>
                        <div id="virtueStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">VIRTUE: GI</div>
                        </div>
                        <div id="karmaStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">KARMA: BALANCED</div>
                        </div>
                        <div id="ancestorStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">ANCESTORS: WATCHING</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">SAMURAI PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Sacred Symbols:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Katana:</span>
                            <span class="text-red-400">100:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Cherry Blossom:</span>
                            <span class="text-red-400">80:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Torii Gate:</span>
                            <span class="text-red-400">60:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Honor Combinations:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">4 Katana:</span>
                            <span class="text-red-400">50:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">3 Katana:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Honor required for multipliers</div>
                        <div class="text-xs text-red-400">*Dishonor voids all wins</div>
                    </div>
                </div>
            </div>

            <!-- Main Samurai Dojo -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <!-- Japanese Dojo with Dice -->
                    <div id="samuraiDojo" class="relative bg-gradient-to-br from-black via-red-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Japanese Garden Background -->
                        <div id="japaneseGarden" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="moonGradient" cx="80%" cy="20%" r="15%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#ffcccc;stop-opacity:0.4" />
                                        <stop offset="100%" style="stop-color:#ff0000;stop-opacity:0.2" />
                                    </radialGradient>
                                    <pattern id="tatami" width="40" height="40" patternUnits="userSpaceOnUse">
                                        <rect width="40" height="40" fill="#8B4513" opacity="0.2"/>
                                        <rect x="2" y="2" width="36" height="36" fill="none" stroke="#D2691E" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#tatami)" />
                                <circle id="moon" cx="80%" cy="20%" r="8%" fill="url(#moonGradient)" class="animate-pulse" />
                                <g id="cherryBlossoms">
                                    <!-- Cherry blossoms will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Dice Display Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">HONOR DICE</div>
                                <div id="diceDisplay" class="flex space-x-3 justify-center">
                                    <!-- 5 dice will appear here -->
                                    <div id="dice1" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">一</div>
                                    </div>
                                    <div id="dice2" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">一</div>
                                    </div>
                                    <div id="dice3" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">一</div>
                                    </div>
                                    <div id="dice4" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">一</div>
                                    </div>
                                    <div id="dice5" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">一</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Click dice to lock/unlock</div>
                            </div>
                        </div>

                        <!-- Honor Progress -->
                        <div id="honorProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">BUSHIDO HONOR</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="honorBar" class="bg-gradient-to-r from-red-400 to-yellow-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Dishonor</span>
                                    <span id="currentHonor">Honor: 0/100</span>
                                    <span>Perfect Honor</span>
                                </div>
                            </div>
                        </div>

                        <!-- Bushido Multiplier -->
                        <div id="bushidoMultiplier" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">BUSHIDO MULTIPLIER</div>
                                <div id="multiplierValue" class="text-lg font-bold text-white">1.0x</div>
                                <div class="w-20 bg-gray-700 rounded-full h-2 mt-1">
                                    <div id="multiplierBar" class="bg-red-400 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div id="virtueAlignment" class="text-xs text-red-400 mt-1">Gi: 0%</div>
                            </div>
                        </div>

                        <!-- Rolls Remaining -->
                        <div id="rollsRemaining" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">ROLLS LEFT</div>
                                <div class="text-2xl font-bold text-white text-center">3</div>
                                <div class="text-xs text-gray-400 mt-1">Honor Attempts</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Honor awaits your dice...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="samuraiEvent" class="text-sm font-bold text-red-400 hidden animate-pulse">BUSHIDO ACTIVATED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Samurai Honor Dice - Follow the Way of the Warrior</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Honor Earned</div>
                <div id="honorEarned" class="text-xl font-bold text-red-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Seppuku</div>
                <div id="seppukuPerformed" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeSamuraiHonor();
}

// Initialize the game
function initializeSamuraiHonor() {
    document.getElementById('rollDice').addEventListener('click', startHonorRoll);
    document.getElementById('rerollAll').addEventListener('click', () => rerollDice('all'));
    document.getElementById('rerollSelected').addEventListener('click', () => rerollDice('selected'));
    document.getElementById('holdDice').addEventListener('click', holdCurrentDice);
    document.getElementById('commitSeppuku').addEventListener('click', commitSeppuku);

    // Add dice click listeners
    for (let i = 1; i <= 5; i++) {
        document.getElementById(`dice${i}`).addEventListener('click', () => toggleDiceLock(i - 1));
    }

    // Initialize samurai systems
    initializeSamuraiSystems();
    generateCherryBlossoms();
    updateGameStats();
}

// Initialize samurai systems
function initializeSamuraiSystems() {
    // Reset honor system
    samuraiHonorGame.honor.currentLevel = 0;
    samuraiHonorGame.honor.bushidoVirtues = {
        gi: 0, rei: 0, yu: 0, jin: 0, makoto: 0, meiyo: 0, chugi: 0
    };
    samuraiHonorGame.honor.karma = 0;
    samuraiHonorGame.honor.dishonor = 0;
    samuraiHonorGame.honor.seppuku = false;
    samuraiHonorGame.honor.ancestors = 0;

    // Reset Japanese elements
    samuraiHonorGame.japan.era = getRandomEra();
    samuraiHonorGame.japan.clan = getRandomClan();
    samuraiHonorGame.japan.daimyo = getRandomDaimyo();
    samuraiHonorGame.japan.castle = getRandomCastle();
    samuraiHonorGame.japan.season = getRandomSeason();
    samuraiHonorGame.japan.weather = getRandomWeather();
    samuraiHonorGame.japan.temple = getRandomTemple();
    samuraiHonorGame.japan.mountain = 'Mount Fuji';
    samuraiHonorGame.japan.philosophy = getRandomPhilosophy();

    // Reset bushido multipliers
    samuraiHonorGame.bushidoMultipliers.active = false;
    samuraiHonorGame.bushidoMultipliers.currentVirtue = 'gi';
    samuraiHonorGame.bushidoMultipliers.multiplierValue = 1.0;
    samuraiHonorGame.bushidoMultipliers.honorRequirement = 50;
    samuraiHonorGame.bushidoMultipliers.virtueAlignment = 0;
    samuraiHonorGame.bushidoMultipliers.ancestralBlessing = false;
    samuraiHonorGame.bushidoMultipliers.karmaBalance = 0;
    samuraiHonorGame.bushidoMultipliers.dishonorPenalty = 0;
    samuraiHonorGame.bushidoMultipliers.seppukuRisk = 0.05;

    // Reset dice
    for (let i = 0; i < 5; i++) {
        samuraiHonorGame.dice[i] = {
            value: 1,
            symbol: '一',
            locked: false,
            honorBonus: false
        };
    }

    samuraiHonorGame.rollsRemaining = 3;

    updateSamuraiDisplay();
}

// Generate cherry blossoms
function generateCherryBlossoms() {
    const container = document.getElementById('cherryBlossoms');
    container.innerHTML = '';

    for (let i = 0; i < 12; i++) {
        const blossom = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        blossom.setAttribute('cx', `${Math.random() * 100}%`);
        blossom.setAttribute('cy', `${Math.random() * 100}%`);
        blossom.setAttribute('r', `${Math.random() * 3 + 1}%`);
        blossom.setAttribute('fill', '#ffb6c1');
        blossom.setAttribute('opacity', '0.6');
        blossom.classList.add('animate-pulse');
        blossom.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(blossom);
    }
}

// Get random Japanese elements
function getRandomEra() {
    const eras = ['Heian Period', 'Kamakura Period', 'Muromachi Period', 'Edo Period', 'Meiji Period'];
    return eras[Math.floor(Math.random() * eras.length)];
}

function getRandomClan() {
    const clans = ['Tokugawa', 'Oda', 'Takeda', 'Uesugi', 'Shimazu', 'Date', 'Mori'];
    return clans[Math.floor(Math.random() * clans.length)];
}

function getRandomDaimyo() {
    const daimyos = ['Tokugawa Ieyasu', 'Oda Nobunaga', 'Takeda Shingen', 'Uesugi Kenshin', 'Date Masamune'];
    return daimyos[Math.floor(Math.random() * daimyos.length)];
}

function getRandomCastle() {
    const castles = ['Edo Castle', 'Osaka Castle', 'Himeji Castle', 'Matsumoto Castle', 'Kumamoto Castle'];
    return castles[Math.floor(Math.random() * castles.length)];
}

function getRandomSeason() {
    const seasons = ['Spring (Cherry Blossoms)', 'Summer (Festivals)', 'Autumn (Maple Leaves)', 'Winter (Snow)'];
    return seasons[Math.floor(Math.random() * seasons.length)];
}

function getRandomWeather() {
    const weather = ['Clear Skies', 'Gentle Rain', 'Morning Mist', 'Moonlit Night', 'Cherry Blossoms'];
    return weather[Math.floor(Math.random() * weather.length)];
}

function getRandomTemple() {
    const temples = ['Sensoji', 'Kiyomizu-dera', 'Fushimi Inari', 'Todaiji', 'Kinkaku-ji'];
    return temples[Math.floor(Math.random() * temples.length)];
}

function getRandomPhilosophy() {
    const philosophies = ['Zen Buddhism', 'Shintoism', 'Confucianism', 'Bushido', 'Taoism'];
    return philosophies[Math.floor(Math.random() * philosophies.length)];
}

// Start honor roll
function startHonorRoll() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    samuraiHonorGame.isPlaying = true;
    samuraiHonorGame.betAmount = betAmount;
    samuraiHonorGame.totalBet = betAmount;
    samuraiHonorGame.bushidoPath = document.getElementById('bushidoPath').value;
    samuraiHonorGame.honorLevel = document.getElementById('honorLevel').value;
    samuraiHonorGame.rollsRemaining = 3;

    // Activate samurai systems
    activateSamuraiSystems();

    // Roll initial dice
    setTimeout(() => {
        rollHonorDice();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('rollDice').disabled = true;
    document.getElementById('gameStatus').textContent = 'Rolling honor dice...';
}

// Activate samurai systems
function activateSamuraiSystems() {
    const bushidoData = BUSHIDO_PATHS[samuraiHonorGame.bushidoPath];
    const honorData = HONOR_LEVELS[samuraiHonorGame.honorLevel];

    samuraiHonorGame.bushidoMultipliers.currentVirtue = samuraiHonorGame.bushidoPath;
    samuraiHonorGame.bushidoMultipliers.honorRequirement = bushidoData.honorRequirement * 100;

    // Check for bushido activation (very rare)
    if (Math.random() < bushidoData.virtueAlignment) {
        activateBushidoMultipliers();
    }

    // Apply dishonor risk
    if (Math.random() < bushidoData.dishonorRisk) {
        applyDishonor();
    }

    // Update samurai elements
    updateSamuraiElements();

    // Update visual effects
    updateSamuraiDisplay();
    updateHonorEffects();
}

// Activate bushido multipliers
function activateBushidoMultipliers() {
    samuraiHonorGame.bushidoMultipliers.active = true;
    samuraiHonorGame.stats.bushidoActivations++;

    document.getElementById('virtueStatus').innerHTML =
        `<div class="text-blue-400 font-bold animate-pulse">VIRTUE: ${samuraiHonorGame.bushidoPath.toUpperCase()}</div>`;

    // Show samurai event
    document.getElementById('samuraiEvent').classList.remove('hidden');
    document.getElementById('samuraiEvent').textContent = 'BUSHIDO ACTIVATED!';
    setTimeout(() => {
        document.getElementById('samuraiEvent').classList.add('hidden');
    }, 3000);
}

// Apply dishonor
function applyDishonor() {
    samuraiHonorGame.honor.dishonor += Math.random() * 20 + 10; // 10-30 dishonor
    samuraiHonorGame.bushidoMultipliers.dishonorPenalty = samuraiHonorGame.honor.dishonor / 100;

    document.getElementById('karmaStatus').innerHTML =
        '<div class="text-red-400 font-bold animate-pulse">KARMA: DISHONORED</div>';
}

// Update samurai elements
function updateSamuraiElements() {
    document.getElementById('clanStatus').innerHTML =
        `<div class="text-red-400 font-bold">CLAN: ${samuraiHonorGame.japan.clan.toUpperCase()}</div>`;
    document.getElementById('ancestorStatus').innerHTML =
        '<div class="text-purple-400 font-bold">ANCESTORS: WATCHING</div>';
}

// Roll honor dice with extreme bias
function rollHonorDice() {
    document.getElementById('gameStatus').textContent = 'Dice rolling with honor...';

    // Roll each unlocked die with extreme bias
    for (let i = 0; i < 5; i++) {
        if (!samuraiHonorGame.dice[i].locked) {
            const diceResult = generateBiasedDiceRoll();
            samuraiHonorGame.dice[i] = diceResult;
        }
    }

    // Animate dice rolling
    animateDiceRoll();

    // Enable dice actions after roll
    setTimeout(() => {
        enableDiceActions();
    }, 2000);
}

// Generate biased dice roll
function generateBiasedDiceRoll() {
    const bushidoData = BUSHIDO_PATHS[samuraiHonorGame.bushidoPath];

    // Apply extreme bias toward low-value symbols
    if (Math.random() < bushidoData.dishonorRisk) {
        // Bias toward common, low-honor symbols
        const lowHonorSymbols = samuraiHonorGame.symbols.filter(s => s.honor <= 4);
        const symbol = lowHonorSymbols[Math.floor(Math.random() * lowHonorSymbols.length)];
        return {
            value: symbol.value,
            symbol: getJapaneseSymbol(symbol.name),
            locked: false,
            honorBonus: false
        };
    } else {
        // Random selection (still biased by rarity)
        const symbol = selectSymbolByRarity();
        return {
            value: symbol.value,
            symbol: getJapaneseSymbol(symbol.name),
            locked: false,
            honorBonus: Math.random() < 0.05 // 5% honor bonus chance
        };
    }
}

// Select symbol by rarity (biased toward common)
function selectSymbolByRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Reverse order to favor common symbols
    for (let i = samuraiHonorGame.symbols.length - 1; i >= 0; i--) {
        const symbol = samuraiHonorGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to most common symbol
    return samuraiHonorGame.symbols[samuraiHonorGame.symbols.length - 1];
}

// Get Japanese symbol representation
function getJapaneseSymbol(symbolName) {
    const symbols = {
        'Katana': '⚔️',
        'Cherry Blossom': '🌸',
        'Torii Gate': '⛩️',
        'Dragon': '🐉',
        'Crane': '🕊️',
        'Bamboo': '🎋'
    };

    return symbols[symbolName] || '🎌';
}

// Animate dice roll
function animateDiceRoll() {
    for (let i = 0; i < 5; i++) {
        const diceElement = document.getElementById(`dice${i + 1}`);
        const diceFace = diceElement.querySelector('.dice-face');

        if (!samuraiHonorGame.dice[i].locked) {
            // Add rolling animation
            diceElement.classList.add('animate-spin');

            setTimeout(() => {
                diceElement.classList.remove('animate-spin');
                diceFace.textContent = samuraiHonorGame.dice[i].symbol;

                // Add honor bonus styling
                if (samuraiHonorGame.dice[i].honorBonus) {
                    diceElement.classList.add('ring-2', 'ring-yellow-400', 'animate-pulse');
                }

                // Update honor based on dice
                updateHonorFromDice(samuraiHonorGame.dice[i]);
            }, 1000 + i * 200);
        }
    }
}

// Update honor from dice
function updateHonorFromDice(dice) {
    const symbol = samuraiHonorGame.symbols.find(s => getJapaneseSymbol(s.name) === dice.symbol);
    if (symbol) {
        samuraiHonorGame.honor.currentLevel += symbol.honor;
        samuraiHonorGame.honor.bushidoVirtues[symbol.virtue] += 1;

        if (dice.honorBonus) {
            samuraiHonorGame.honor.currentLevel += 5; // Bonus honor
        }
    }

    // Cap honor at 100
    samuraiHonorGame.honor.currentLevel = Math.min(100, samuraiHonorGame.honor.currentLevel);

    updateHonorDisplay();
}

// Enable dice actions
function enableDiceActions() {
    document.getElementById('diceActions').classList.remove('hidden');
    samuraiHonorGame.rollsRemaining--;

    document.getElementById('rollsRemaining').querySelector('.text-2xl').textContent = samuraiHonorGame.rollsRemaining;

    if (samuraiHonorGame.rollsRemaining <= 0) {
        // No more rolls - resolve game
        setTimeout(() => {
            resolveSamuraiGame();
        }, 1000);
    } else {
        document.getElementById('gameStatus').textContent = 'Choose your honor path...';
        document.getElementById('gameMessage').textContent = 'Lock dice and reroll, or commit seppuku to preserve honor';
    }
}

// Toggle dice lock
function toggleDiceLock(diceIndex) {
    if (samuraiHonorGame.isPlaying && samuraiHonorGame.rollsRemaining > 0) {
        samuraiHonorGame.dice[diceIndex].locked = !samuraiHonorGame.dice[diceIndex].locked;

        const diceElement = document.getElementById(`dice${diceIndex + 1}`);
        if (samuraiHonorGame.dice[diceIndex].locked) {
            diceElement.classList.add('ring-4', 'ring-red-400', 'bg-red-900/50');
        } else {
            diceElement.classList.remove('ring-4', 'ring-red-400', 'bg-red-900/50');
        }
    }
}

// Reroll dice
function rerollDice(type) {
    if (samuraiHonorGame.rollsRemaining <= 0) {
        return;
    }

    if (type === 'all') {
        // Unlock all dice and reroll
        for (let i = 0; i < 5; i++) {
            samuraiHonorGame.dice[i].locked = false;
            const diceElement = document.getElementById(`dice${i + 1}`);
            diceElement.classList.remove('ring-4', 'ring-red-400', 'bg-red-900/50');
        }
    }

    document.getElementById('diceActions').classList.add('hidden');

    setTimeout(() => {
        rollHonorDice();
    }, 500);
}

// Hold current dice
function holdCurrentDice() {
    // Lock all dice and resolve
    for (let i = 0; i < 5; i++) {
        samuraiHonorGame.dice[i].locked = true;
    }

    document.getElementById('diceActions').classList.add('hidden');

    setTimeout(() => {
        resolveSamuraiGame();
    }, 1000);
}

// Commit seppuku
function commitSeppuku() {
    samuraiHonorGame.honor.seppuku = true;
    samuraiHonorGame.stats.seppukuPerformed++;

    document.getElementById('diceActions').classList.add('hidden');
    document.getElementById('gameStatus').textContent = 'Seppuku performed with honor...';

    // Show samurai event
    document.getElementById('samuraiEvent').classList.remove('hidden');
    document.getElementById('samuraiEvent').textContent = 'HONORABLE SEPPUKU!';
    setTimeout(() => {
        document.getElementById('samuraiEvent').classList.add('hidden');
    }, 3000);

    setTimeout(() => {
        resolveSamuraiGame('seppuku');
    }, 2000);
}

// Resolve samurai game with extreme bias
function resolveSamuraiGame(action = 'normal') {
    const bushidoData = BUSHIDO_PATHS[samuraiHonorGame.bushidoPath];
    const honorData = HONOR_LEVELS[samuraiHonorGame.honorLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    if (action === 'seppuku') {
        // Seppuku - minimal honor return
        totalWinnings = Math.floor(samuraiHonorGame.betAmount * 0.05); // 5% return
        resultMessage = 'Honorable seppuku - Ancestors pleased';
        samuraiHonorGame.gameResult = 'seppuku';
    } else {
        // Calculate dice combinations with extreme reductions
        const diceCombinations = calculateDiceCombinations();

        Object.entries(diceCombinations).forEach(([combination, count]) => {
            if (count >= 3) { // Need at least 3 of a kind
                const payoutKey = combination.toUpperCase();
                if (SAMURAI_PAYOUTS[payoutKey]) {
                    let combinationPayout = samuraiHonorGame.betAmount * (SAMURAI_PAYOUTS[payoutKey] / 1000); // Severely reduced

                    // Apply count multiplier (minimal)
                    if (count === 4) combinationPayout *= 1.2;
                    if (count === 5) combinationPayout *= 1.5;

                    totalWinnings += combinationPayout;
                }
            }
        });

        // Apply bushido multiplier (fake - almost never applies)
        if (samuraiHonorGame.bushidoMultipliers.active && Math.random() < 0.01) {
            const bushidoBonus = Math.floor(totalWinnings * SAMURAI_PAYOUTS.BUSHIDO_HONOR);
            totalWinnings += bushidoBonus;
        }

        // Apply honor requirement check
        if (samuraiHonorGame.honor.currentLevel < samuraiHonorGame.bushidoMultipliers.honorRequirement) {
            const honorPenalty = (samuraiHonorGame.bushidoMultipliers.honorRequirement - samuraiHonorGame.honor.currentLevel) / 100;
            totalWinnings = Math.floor(totalWinnings * (1 - honorPenalty));
        }

        resultMessage = `Honor level: ${samuraiHonorGame.honor.currentLevel}`;
        samuraiHonorGame.gameResult = totalWinnings > samuraiHonorGame.betAmount ? 'win' : 'lose';
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * bushidoData.payoutMultiplier);

    // Apply dishonor penalty
    if (samuraiHonorGame.honor.dishonor > 0) {
        const dishonorPenalty = samuraiHonorGame.honor.dishonor / 100;
        totalWinnings = Math.floor(totalWinnings * (1 - dishonorPenalty));
    }

    // Apply honor level multiplier
    totalWinnings = Math.floor(totalWinnings * honorData.multiplier);

    // Seppuku risk can void wins
    if (totalWinnings > 0 && Math.random() < samuraiHonorGame.bushidoMultipliers.seppukuRisk) {
        totalWinnings = 0;
        resultMessage += ' - Honor demands seppuku!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    samuraiHonorGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterGame(totalWinnings > samuraiHonorGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Bushido Path: ${BUSHIDO_PATHS[samuraiHonorGame.bushidoPath].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Calculate dice combinations
function calculateDiceCombinations() {
    const combinations = {};

    samuraiHonorGame.dice.forEach(dice => {
        const symbolName = getSymbolNameFromIcon(dice.symbol);
        combinations[symbolName] = (combinations[symbolName] || 0) + 1;
    });

    return combinations;
}

// Get symbol name from icon
function getSymbolNameFromIcon(icon) {
    const iconMap = {
        '⚔️': 'KATANA',
        '🌸': 'CHERRY',
        '⛩️': 'TORII',
        '🐉': 'DRAGON',
        '🕊️': 'CRANE',
        '🎋': 'BAMBOO'
    };

    return iconMap[icon] || 'BAMBOO';
}

// Update samurai display
function updateSamuraiDisplay() {
    const bushidoData = BUSHIDO_PATHS[samuraiHonorGame.bushidoPath];

    if (samuraiHonorGame.bushidoMultipliers.active) {
        document.getElementById('multiplierValue').textContent = `${samuraiHonorGame.bushidoMultipliers.multiplierValue.toFixed(1)}x`;
        document.getElementById('multiplierBar').style.width = `${samuraiHonorGame.bushidoMultipliers.virtueAlignment * 100}%`;
    } else {
        document.getElementById('multiplierValue').textContent = '1.0x';
        document.getElementById('multiplierBar').style.width = '0%';
    }

    document.getElementById('virtueAlignment').textContent =
        `${samuraiHonorGame.bushidoPath}: ${Math.floor(samuraiHonorGame.bushidoMultipliers.virtueAlignment * 100)}%`;
}

// Update honor effects
function updateHonorEffects() {
    // Update honor bar
    updateHonorDisplay();
}

// Update honor display
function updateHonorDisplay() {
    const honorPercentage = Math.min(100, samuraiHonorGame.honor.currentLevel);
    document.getElementById('honorBar').style.width = `${honorPercentage}%`;
    document.getElementById('currentHonor').textContent = `Honor: ${samuraiHonorGame.honor.currentLevel}/100`;
    document.getElementById('honorLevelDisplay').textContent = samuraiHonorGame.honor.currentLevel;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${samuraiHonorGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = samuraiHonorGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${samuraiHonorGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${samuraiHonorGame.stats.totalWagered}`;
    document.getElementById('honorEarned').textContent = samuraiHonorGame.stats.honorEarned;
    document.getElementById('seppukuPerformed').textContent = samuraiHonorGame.stats.seppukuPerformed;

    const netResult = samuraiHonorGame.stats.totalWon - samuraiHonorGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after game
function updateGameStatsAfterGame(won, winnings) {
    samuraiHonorGame.stats.gamesPlayed++;
    samuraiHonorGame.stats.totalWagered += samuraiHonorGame.betAmount;
    samuraiHonorGame.stats.totalWon += winnings;
    samuraiHonorGame.stats.honorEarned += samuraiHonorGame.honor.currentLevel;

    if (won) {
        samuraiHonorGame.stats.gamesWon++;
        samuraiHonorGame.stats.currentStreak++;
        samuraiHonorGame.streakData.currentWinStreak++;
        samuraiHonorGame.streakData.currentLossStreak = 0;

        if (samuraiHonorGame.streakData.currentWinStreak > samuraiHonorGame.streakData.longestWinStreak) {
            samuraiHonorGame.streakData.longestWinStreak = samuraiHonorGame.streakData.currentWinStreak;
        }

        if (winnings > samuraiHonorGame.stats.biggestWin) {
            samuraiHonorGame.stats.biggestWin = winnings;
        }
    } else {
        samuraiHonorGame.stats.currentStreak = 0;
        samuraiHonorGame.streakData.currentWinStreak = 0;
        samuraiHonorGame.streakData.currentLossStreak++;

        if (samuraiHonorGame.streakData.currentLossStreak > samuraiHonorGame.streakData.longestLossStreak) {
            samuraiHonorGame.streakData.longestLossStreak = samuraiHonorGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to samurai effects)
    samuraiHonorGame.stats.winRate = (samuraiHonorGame.stats.gamesWon / samuraiHonorGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next honor test
function resetGame() {
    samuraiHonorGame.isPlaying = false;
    samuraiHonorGame.betAmount = 0;
    samuraiHonorGame.totalBet = 0;
    samuraiHonorGame.gameResult = '';
    samuraiHonorGame.totalWin = 0;
    samuraiHonorGame.rollsRemaining = 3;

    // Reset honor system
    samuraiHonorGame.honor.currentLevel = 0;
    samuraiHonorGame.honor.bushidoVirtues = {
        gi: 0, rei: 0, yu: 0, jin: 0, makoto: 0, meiyo: 0, chugi: 0
    };
    samuraiHonorGame.honor.karma = 0;
    samuraiHonorGame.honor.dishonor = 0;
    samuraiHonorGame.honor.seppuku = false;
    samuraiHonorGame.honor.ancestors = 0;

    // Reset bushido multipliers
    samuraiHonorGame.bushidoMultipliers.active = false;
    samuraiHonorGame.bushidoMultipliers.multiplierValue = 1.0;
    samuraiHonorGame.bushidoMultipliers.virtueAlignment = 0;
    samuraiHonorGame.bushidoMultipliers.ancestralBlessing = false;
    samuraiHonorGame.bushidoMultipliers.karmaBalance = 0;
    samuraiHonorGame.bushidoMultipliers.dishonorPenalty = 0;

    // Reset dice
    for (let i = 0; i < 5; i++) {
        samuraiHonorGame.dice[i] = {
            value: 1,
            symbol: '一',
            locked: false,
            honorBonus: false
        };

        // Reset dice display
        const diceElement = document.getElementById(`dice${i + 1}`);
        const diceFace = diceElement.querySelector('.dice-face');
        diceFace.textContent = '一';
        diceElement.className = 'dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300';
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('samuraiEvent').classList.add('hidden');
    document.getElementById('diceActions').classList.add('hidden');

    // Reset samurai status
    document.getElementById('clanStatus').innerHTML =
        '<div class="text-red-400 font-bold">CLAN: TOKUGAWA</div>';
    document.getElementById('virtueStatus').innerHTML =
        '<div class="text-blue-400 font-bold">VIRTUE: GI</div>';
    document.getElementById('karmaStatus').innerHTML =
        '<div class="text-green-400 font-bold">KARMA: BALANCED</div>';
    document.getElementById('ancestorStatus').innerHTML =
        '<div class="text-purple-400 font-bold">ANCESTORS: WATCHING</div>';

    // Reset honor display
    document.getElementById('honorBar').style.width = '0%';
    document.getElementById('currentHonor').textContent = 'Honor: 0/100';
    document.getElementById('honorLevelDisplay').textContent = '0';

    // Reset multiplier display
    document.getElementById('multiplierValue').textContent = '1.0x';
    document.getElementById('multiplierBar').style.width = '0%';
    document.getElementById('virtueAlignment').textContent = 'Gi: 0%';

    // Reset rolls remaining
    document.getElementById('rollsRemaining').querySelector('.text-2xl').textContent = '3';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable roll button
    document.getElementById('rollDice').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Honor awaits your dice...';
    document.getElementById('gameMessage').textContent = 'Welcome to Samurai Honor Dice - Follow the Way of the Warrior';

    // Reinitialize systems for next game
    initializeSamuraiSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSamuraiHonorGame();
});