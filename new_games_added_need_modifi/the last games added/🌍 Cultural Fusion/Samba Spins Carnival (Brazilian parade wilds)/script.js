// Samba Spins Carnival - Brazilian Parade Wilds
// Ultra High House Edge Implementation with Brazilian Carnival Theme
// Designed to maintain <0.3% player win rate

// Game state
let balance = 1000;

// Game state object with Brazilian carnival system
let sambaCarnivalGame = {
    isPlaying: false,
    carnivalMode: 'rio', // rio, salvador, recife, olinda, manaus
    paradeLevel: 'blocos', // blocos, escolas, champions, grand_finale
    betAmount: 0,
    totalBet: 0,

    // Carnival parade system
    parade: {
        currentFloat: 0,
        totalFloats: 12,
        dancers: 0,
        musicians: 0,
        energy: 100,
        rhythm: 1.0,
        crowdExcitement: 50,
        judgesScore: 0,
        sambaDrummers: 0,
        featherCount: 0
    },

    // Brazilian carnival elements
    carnival: {
        city: 'Rio de Janeiro',
        sambaSchool: 'Mangueira',
        theme: 'Amazonia',
        queen: '<PERSON><PERSON> Bateria',
        king: '<PERSON><PERSON>',
        currentSong: 'Aquarela do Brasil',
        weather: 'Perfect',
        temperature: 35, // Celsius
        crowdSize: 2000000 // 2 million people
    },

    // Reel system (5 reels, 4 rows for carnival parade)
    reels: [
        [], [], [], [], []
    ],

    // Brazilian parade wilds system
    paradeWilds: {
        active: false,
        wildFloats: [],
        dancerWilds: 0,
        musicianWilds: 0,
        featherWilds: 0,
        drumWilds: 0,
        multiplier: 1,
        paradeLength: 0,
        wildExpansion: false,
        carnivalMagic: 0.05 // 5% wild magic (fake)
    },

    // Brazilian symbols with carnival significance
    symbols: [
        { name: 'Cristo Redentor', value: 100, rarity: 0.003, type: 'landmark' },
        { name: 'Sugarloaf Mountain', value: 95, rarity: 0.005, type: 'landmark' },
        { name: 'Copacabana Beach', value: 90, rarity: 0.007, type: 'landmark' },
        { name: 'Samba Queen', value: 85, rarity: 0.009, type: 'royalty' },
        { name: 'Rei Momo', value: 80, rarity: 0.012, type: 'royalty' },
        { name: 'Carnival Float', value: 75, rarity: 0.015, type: 'parade' },
        { name: 'Samba Dancer', value: 70, rarity: 0.018, type: 'performer' },
        { name: 'Bateria Drummer', value: 65, rarity: 0.022, type: 'musician' },
        { name: 'Feather Headdress', value: 60, rarity: 0.025, type: 'costume' },
        { name: 'Carnival Mask', value: 55, rarity: 0.030, type: 'costume' },
        { name: 'Surdo Drum', value: 50, rarity: 0.035, type: 'instrument' },
        { name: 'Cuica', value: 45, rarity: 0.040, type: 'instrument' },
        { name: 'Tamborim', value: 40, rarity: 0.050, type: 'instrument' },
        { name: 'Maracas', value: 35, rarity: 0.060, type: 'instrument' },
        { name: 'Caipirinha', value: 30, rarity: 0.080, type: 'drink' },
        { name: 'Açaí', value: 25, rarity: 0.100, type: 'food' },
        { name: 'Coconut', value: 20, rarity: 0.120, type: 'food' },
        { name: 'Toucan', value: 15, rarity: 0.150, type: 'animal' }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        paradeWildsTriggered: 0,
        carnivalBonuses: 0,
        sambaRhythms: 0,
        floatsCompleted: 0,
        crowdCheers: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Carnival modes with extreme samba bias
const CARNIVAL_MODES = {
    rio: {
        name: 'Rio Carnival',
        houseEdge: 0.88, // 88% house edge
        paradeComplexity: 0.45, // 45% parade complexity
        payoutMultiplier: 0.10, // Severely reduced payouts
        wildChance: 0.12, // 12% wild chance
        crowdBias: 0.75 // 75% crowd bias
    },
    salvador: {
        name: 'Salvador Carnival',
        houseEdge: 0.91, // 91% house edge
        paradeComplexity: 0.58, // 58% parade complexity
        payoutMultiplier: 0.08, // Even more reduced payouts
        wildChance: 0.09, // 9% wild chance
        crowdBias: 0.82 // 82% crowd bias
    },
    recife: {
        name: 'Recife Carnival',
        houseEdge: 0.94, // 94% house edge
        paradeComplexity: 0.72, // 72% parade complexity
        payoutMultiplier: 0.06, // Extremely reduced payouts
        wildChance: 0.06, // 6% wild chance
        crowdBias: 0.88 // 88% crowd bias
    },
    olinda: {
        name: 'Olinda Carnival',
        houseEdge: 0.96, // 96% house edge
        paradeComplexity: 0.85, // 85% parade complexity
        payoutMultiplier: 0.04, // Brutally reduced payouts
        wildChance: 0.04, // 4% wild chance
        crowdBias: 0.92 // 92% crowd bias
    },
    manaus: {
        name: 'Manaus Carnival',
        houseEdge: 0.99, // 99% house edge
        paradeComplexity: 0.95, // 95% parade complexity
        payoutMultiplier: 0.02, // Impossibly reduced payouts
        wildChance: 0.02, // 2% wild chance
        crowdBias: 0.98 // 98% crowd bias
    }
};

const PARADE_LEVELS = {
    blocos: {
        name: 'Street Blocos',
        difficulty: 0.30,
        wildMultiplier: 1.1,
        energyDrain: 0.15,
        crowdSupport: 0.25
    },
    escolas: {
        name: 'Samba Schools',
        difficulty: 0.55,
        wildMultiplier: 1.2,
        energyDrain: 0.30,
        crowdSupport: 0.20
    },
    champions: {
        name: 'Champions Parade',
        difficulty: 0.75,
        wildMultiplier: 1.3,
        energyDrain: 0.50,
        crowdSupport: 0.15
    },
    grand_finale: {
        name: 'Grand Finale',
        difficulty: 0.90,
        wildMultiplier: 1.5,
        energyDrain: 0.70,
        crowdSupport: 0.10
    }
};

// Severely reduced payout table with Brazilian theme
const CARNIVAL_PAYOUTS = {
    // Landmark payouts (heavily reduced)
    CRISTO_REDENTOR: 20, // Reduced from 1000:1
    SUGARLOAF_MOUNTAIN: 18, // Reduced from 800:1
    COPACABANA_BEACH: 15, // Reduced from 600:1

    // Royalty payouts (reduced)
    SAMBA_QUEEN: 12, // Reduced from 400:1
    REI_MOMO: 10, // Reduced from 300:1

    // Parade payouts (reduced)
    CARNIVAL_FLOAT: 8, // Reduced from 200:1
    SAMBA_DANCER: 6, // Reduced from 150:1
    BATERIA_DRUMMER: 5, // Reduced from 100:1

    // Costume payouts (reduced)
    FEATHER_HEADDRESS: 4, // Reduced from 75:1
    CARNIVAL_MASK: 3, // Reduced from 50:1

    // Instrument payouts (reduced)
    SURDO_DRUM: 2.5, // Reduced from 40:1
    CUICA: 2, // Reduced from 30:1
    TAMBORIM: 1.8, // Reduced from 25:1
    MARACAS: 1.5, // Reduced from 20:1

    // Food/drink payouts (reduced)
    CAIPIRINHA: 1.2, // Reduced from 15:1
    ACAI: 1, // Reduced from 10:1
    COCONUT: 0.8, // Reduced from 8:1
    TOUCAN: 0.6, // Reduced from 6:1

    // Parade wild bonuses (fake - almost never apply)
    PARADE_WILD: 0.05, // 5% of displayed bonus
    SAMBA_RHYTHM: 0.02, // 2% of displayed bonus
    CARNIVAL_MAGIC: 0.01 // 1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadSambaCarnivalGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">CARNIVAL CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CARNIVAL MODE</label>
                        <select id="carnivalMode" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="rio">Rio Carnival</option>
                            <option value="salvador">Salvador Carnival</option>
                            <option value="recife">Recife Carnival</option>
                            <option value="olinda">Olinda Carnival</option>
                            <option value="manaus">Manaus Carnival</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PARADE LEVEL</label>
                        <select id="paradeLevel" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="blocos">Street Blocos</option>
                            <option value="escolas" selected>Samba Schools</option>
                            <option value="champions">Champions Parade</option>
                            <option value="grand_finale">Grand Finale</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startParade" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START CARNIVAL PARADE
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Parade Energy</div>
                        <div id="paradeEnergyDisplay" class="text-lg font-bold text-orange-400">100%</div>
                    </div>
                </div>

                <!-- Carnival Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">CARNIVAL STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="cityStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">CITY: RIO DE JANEIRO</div>
                        </div>
                        <div id="schoolStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">SCHOOL: MANGUEIRA</div>
                        </div>
                        <div id="crowdStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">CROWD: 2M PEOPLE</div>
                        </div>
                        <div id="wildStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">WILDS: READY</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">CARNIVAL PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Brazilian Icons:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Cristo Redentor:</span>
                            <span class="text-red-400">20:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sugarloaf:</span>
                            <span class="text-red-400">18:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Copacabana:</span>
                            <span class="text-red-400">15:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Carnival Stars:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Samba Queen:</span>
                            <span class="text-red-400">12:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rei Momo:</span>
                            <span class="text-red-400">10:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Parade wilds rarely expand</div>
                        <div class="text-xs text-red-400">*Crowd bias affects payouts</div>
                    </div>
                </div>
            </div>

            <!-- Main Carnival Stage -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <!-- Brazilian Carnival Stage with Reels -->
                    <div id="carnivalStage" class="relative bg-gradient-to-br from-black via-yellow-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Carnival Lights Background -->
                        <div id="carnivalLights" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="carnivalGradient" cx="50%" cy="30%" r="40%">
                                        <stop offset="0%" style="stop-color:#ffff00;stop-opacity:0.6" />
                                        <stop offset="50%" style="stop-color:#ff8000;stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:#ff0000;stop-opacity:0.1" />
                                    </radialGradient>
                                    <pattern id="carnivalPattern" width="20" height="20" patternUnits="userSpaceOnUse">
                                        <circle cx="10" cy="10" r="3" fill="#ffff00" opacity="0.3"/>
                                        <circle cx="5" cy="5" r="2" fill="#ff8000" opacity="0.4"/>
                                        <circle cx="15" cy="15" r="2" fill="#ff0000" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#carnivalPattern)" />
                                <ellipse id="carnivalSpotlight" cx="50%" cy="30%" rx="40%" ry="25%" fill="url(#carnivalGradient)" class="animate-pulse" />
                                <g id="paradeFloats">
                                    <!-- Parade floats will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Reel Display Area (5x4 for carnival parade) -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-yellow-400 mb-2">CARNIVAL REELS</div>
                                <div id="reelsDisplay" class="grid grid-cols-5 gap-2">
                                    <!-- 5 reels, 4 rows each for parade formation -->
                                    <div id="reel1" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel2" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel3" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel4" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel5" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Parade Progress Bar -->
                        <div id="paradeProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-yellow-400 mb-2">PARADE PROGRESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="paradeBar" class="bg-gradient-to-r from-yellow-400 to-orange-500 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Float 1</span>
                                    <span id="currentFloat">Float 1/12</span>
                                    <span>Float 12</span>
                                </div>
                            </div>
                        </div>

                        <!-- Wild Parade Indicator -->
                        <div id="wildParadeIndicator" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">PARADE WILDS</div>
                                <div id="wildCount" class="text-sm font-bold text-white">0</div>
                                <div class="w-20 bg-gray-700 rounded-full h-2 mt-1">
                                    <div id="wildBar" class="bg-yellow-400 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <div id="wildMultiplier" class="text-xs text-yellow-400 mt-1">1x</div>
                            </div>
                        </div>

                        <!-- Samba Rhythm Meter -->
                        <div id="sambaRhythm" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-orange-400 mb-1">SAMBA RHYTHM</div>
                                <div class="flex space-x-1">
                                    <div class="w-2 h-8 bg-gray-700 rounded animate-pulse"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded animate-pulse" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded animate-pulse" style="animation-delay: 0.2s"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded animate-pulse" style="animation-delay: 0.3s"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded animate-pulse" style="animation-delay: 0.4s"></div>
                                </div>
                                <div id="rhythmLevel" class="text-xs text-orange-400 mt-1">Perfect</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Carnival awaits...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="carnivalEvent" class="text-sm font-bold text-orange-400 hidden animate-pulse">CARNIVAL MAGIC!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Samba Spins Carnival - Where Every Spin is a Brazilian Celebration</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Parade Wilds</div>
                <div id="paradeWildsTriggered" class="text-xl font-bold text-yellow-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Floats Completed</div>
                <div id="floatsCompleted" class="text-xl font-bold text-orange-400">0</div>
            </div>
        </div>
    `;

    initializeSambaCarnival();
}

// Initialize the game
function initializeSambaCarnival() {
    document.getElementById('startParade').addEventListener('click', startCarnivalParade);

    // Initialize carnival systems
    initializeCarnivalSystems();
    generateParadeFloats();
    updateGameStats();
}

// Initialize carnival systems
function initializeCarnivalSystems() {
    // Reset parade system
    sambaCarnivalGame.parade.currentFloat = 0;
    sambaCarnivalGame.parade.totalFloats = 12;
    sambaCarnivalGame.parade.dancers = 0;
    sambaCarnivalGame.parade.musicians = 0;
    sambaCarnivalGame.parade.energy = 100;
    sambaCarnivalGame.parade.rhythm = 1.0;
    sambaCarnivalGame.parade.crowdExcitement = 50;
    sambaCarnivalGame.parade.judgesScore = 0;
    sambaCarnivalGame.parade.sambaDrummers = 0;
    sambaCarnivalGame.parade.featherCount = 0;

    // Reset carnival elements
    sambaCarnivalGame.carnival.city = getRandomBrazilianCity();
    sambaCarnivalGame.carnival.sambaSchool = getRandomSambaSchool();
    sambaCarnivalGame.carnival.theme = getRandomCarnivalTheme();
    sambaCarnivalGame.carnival.queen = 'Rainha da Bateria';
    sambaCarnivalGame.carnival.king = 'Rei Momo';
    sambaCarnivalGame.carnival.currentSong = getRandomSambaSong();
    sambaCarnivalGame.carnival.weather = 'Perfect';
    sambaCarnivalGame.carnival.temperature = Math.floor(Math.random() * 10) + 30; // 30-40°C
    sambaCarnivalGame.carnival.crowdSize = Math.floor(Math.random() * 1000000) + 1000000; // 1-2M people

    // Reset parade wilds system
    sambaCarnivalGame.paradeWilds.active = false;
    sambaCarnivalGame.paradeWilds.wildFloats = [];
    sambaCarnivalGame.paradeWilds.dancerWilds = 0;
    sambaCarnivalGame.paradeWilds.musicianWilds = 0;
    sambaCarnivalGame.paradeWilds.featherWilds = 0;
    sambaCarnivalGame.paradeWilds.drumWilds = 0;
    sambaCarnivalGame.paradeWilds.multiplier = 1;
    sambaCarnivalGame.paradeWilds.paradeLength = 0;
    sambaCarnivalGame.paradeWilds.wildExpansion = false;
    sambaCarnivalGame.paradeWilds.carnivalMagic = 0.05;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        sambaCarnivalGame.reels[i] = [];
    }

    updateCarnivalDisplay();
}

// Generate parade floats
function generateParadeFloats() {
    const container = document.getElementById('paradeFloats');
    container.innerHTML = '';

    for (let i = 0; i < sambaCarnivalGame.parade.totalFloats; i++) {
        const float = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        float.setAttribute('x', `${5 + i * 7}%`);
        float.setAttribute('y', '70%');
        float.setAttribute('width', '5%');
        float.setAttribute('height', '15%');
        float.setAttribute('fill', i % 2 === 0 ? '#ffff00' : '#ff8000');
        float.setAttribute('opacity', '0.6');
        float.setAttribute('rx', '2');
        float.classList.add('animate-pulse');
        float.style.animationDelay = `${i * 0.1}s`;
        container.appendChild(float);
    }
}

// Get random Brazilian elements
function getRandomBrazilianCity() {
    const cities = ['Rio de Janeiro', 'Salvador', 'Recife', 'Olinda', 'Manaus', 'São Paulo', 'Brasília'];
    return cities[Math.floor(Math.random() * cities.length)];
}

function getRandomSambaSchool() {
    const schools = ['Mangueira', 'Portela', 'Beija-Flor', 'Salgueiro', 'Unidos da Tijuca', 'Imperatriz', 'Vila Isabel'];
    return schools[Math.floor(Math.random() * schools.length)];
}

function getRandomCarnivalTheme() {
    const themes = ['Amazonia', 'Capoeira', 'Bossa Nova', 'Futebol', 'Praia', 'Sertão', 'Festa Junina'];
    return themes[Math.floor(Math.random() * themes.length)];
}

function getRandomSambaSong() {
    const songs = ['Aquarela do Brasil', 'Mas Que Nada', 'Garota de Ipanema', 'Samba de Uma Nota Só', 'Corcovado'];
    return songs[Math.floor(Math.random() * songs.length)];
}

// Start carnival parade
function startCarnivalParade() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    sambaCarnivalGame.isPlaying = true;
    sambaCarnivalGame.betAmount = betAmount;
    sambaCarnivalGame.totalBet = betAmount;
    sambaCarnivalGame.carnivalMode = document.getElementById('carnivalMode').value;
    sambaCarnivalGame.paradeLevel = document.getElementById('paradeLevel').value;

    // Activate carnival systems
    activateCarnivalSystems();

    // Start reel spin with parade
    setTimeout(() => {
        spinCarnivalReels();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startParade').disabled = true;
    document.getElementById('gameStatus').textContent = 'Parade starting...';
}

// Activate carnival systems
function activateCarnivalSystems() {
    const carnivalData = CARNIVAL_MODES[sambaCarnivalGame.carnivalMode];
    const paradeData = PARADE_LEVELS[sambaCarnivalGame.paradeLevel];

    sambaCarnivalGame.parade.energy = Math.max(20, 100 - (paradeData.energyDrain * 100));
    sambaCarnivalGame.parade.crowdExcitement = Math.floor(Math.random() * 50) + 25; // 25-75%

    // Update carnival elements
    updateCarnivalElements();

    // Check for parade wilds activation
    if (Math.random() < carnivalData.wildChance) {
        activateParadeWilds();
    }

    // Update visual effects
    updateCarnivalDisplay();
    updateParadeEffects();
}

// Update carnival elements
function updateCarnivalElements() {
    document.getElementById('cityStatus').innerHTML =
        `<div class="text-yellow-400 font-bold">CITY: ${sambaCarnivalGame.carnival.city.toUpperCase()}</div>`;
    document.getElementById('schoolStatus').innerHTML =
        `<div class="text-green-400 font-bold">SCHOOL: ${sambaCarnivalGame.carnival.sambaSchool.toUpperCase()}</div>`;
    document.getElementById('crowdStatus').innerHTML =
        `<div class="text-blue-400 font-bold">CROWD: ${Math.floor(sambaCarnivalGame.carnival.crowdSize / 1000000)}M PEOPLE</div>`;
}

// Activate parade wilds
function activateParadeWilds() {
    sambaCarnivalGame.paradeWilds.active = true;
    sambaCarnivalGame.stats.paradeWildsTriggered++;

    document.getElementById('wildStatus').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">WILDS: ACTIVE!</div>';

    // Generate wild floats (very few)
    const wildCount = Math.floor(Math.random() * 3) + 1; // 1-3 wilds max
    for (let i = 0; i < wildCount; i++) {
        sambaCarnivalGame.paradeWilds.wildFloats.push({
            reel: Math.floor(Math.random() * 5),
            row: Math.floor(Math.random() * 4),
            type: 'dancer'
        });
    }

    // Show carnival event
    document.getElementById('carnivalEvent').classList.remove('hidden');
    document.getElementById('carnivalEvent').textContent = 'PARADE WILDS ACTIVATED!';
    setTimeout(() => {
        document.getElementById('carnivalEvent').classList.add('hidden');
    }, 3000);
}

// Spin carnival reels with extreme bias
function spinCarnivalReels() {
    document.getElementById('gameStatus').textContent = 'Reels spinning to samba rhythm...';

    // Generate symbols for each reel with extreme bias
    for (let reel = 0; reel < 5; reel++) {
        sambaCarnivalGame.reels[reel] = [];
        for (let row = 0; row < 4; row++) { // 4 rows for carnival parade
            let symbol = generateBiasedCarnivalSymbol();

            // Check for parade wild placement (very rare)
            const wildFloat = sambaCarnivalGame.paradeWilds.wildFloats.find(w => w.reel === reel && w.row === row);
            if (wildFloat && Math.random() < 0.1) { // Only 10% chance even with wild float
                symbol = { name: 'Wild', value: 0, rarity: 0.001, type: 'wild' };
                sambaCarnivalGame.paradeWilds.dancerWilds++;
            }

            sambaCarnivalGame.reels[reel].push(symbol);
        }
    }

    // Animate reel spinning with parade progression
    animateParadeReelSpin();

    // Resolve after reels stop
    setTimeout(() => {
        resolveCarnivalSpin();
    }, 5000);
}

// Generate biased carnival symbol
function generateBiasedCarnivalSymbol() {
    const carnivalData = CARNIVAL_MODES[sambaCarnivalGame.carnivalMode];

    // Apply extreme bias toward low-value symbols
    if (Math.random() < carnivalData.paradeComplexity) {
        // Bias toward common, low-value symbols
        const lowValueSymbols = sambaCarnivalGame.symbols.filter(s => s.value <= 35);
        return lowValueSymbols[Math.floor(Math.random() * lowValueSymbols.length)];
    } else {
        // Random selection (still biased by rarity)
        return selectSymbolByRarity();
    }
}

// Select symbol by rarity (biased toward common)
function selectSymbolByRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Reverse order to favor common symbols
    for (let i = sambaCarnivalGame.symbols.length - 1; i >= 0; i--) {
        const symbol = sambaCarnivalGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to most common symbol
    return sambaCarnivalGame.symbols[sambaCarnivalGame.symbols.length - 1];
}

// Animate parade reel spinning
function animateParadeReelSpin() {
    for (let reel = 0; reel < 5; reel++) {
        const reelElement = document.getElementById(`reel${reel + 1}`);
        const symbols = reelElement.querySelectorAll('.reel-symbol');

        // Add spinning animation
        symbols.forEach((symbol, row) => {
            symbol.classList.add('animate-spin');

            setTimeout(() => {
                symbol.classList.remove('animate-spin');
                displaySymbolInReel(sambaCarnivalGame.reels[reel][row], symbol);
            }, 1000 + reel * 500 + row * 200);
        });
    }

    // Animate parade progress
    animateParadeProgress();
}

// Display symbol in reel
function displaySymbolInReel(symbol, element) {
    const symbolIcon = getCarnivalSymbolIcon(symbol.name);

    element.innerHTML = `
        <div class="text-lg">${symbolIcon}</div>
        <div class="text-xs text-white font-bold">${symbol.name.split(' ')[0]}</div>
    `;

    // Add symbol-specific styling
    if (symbol.name === 'Wild') {
        element.classList.add('ring-2', 'ring-yellow-400', 'animate-pulse', 'bg-yellow-900/50');
    } else if (symbol.value >= 80) {
        element.classList.add('ring-2', 'ring-orange-400', 'animate-pulse');
    } else if (symbol.value >= 50) {
        element.classList.add('ring-1', 'ring-yellow-400');
    }
}

// Get carnival symbol icon
function getCarnivalSymbolIcon(symbolName) {
    const icons = {
        'Cristo Redentor': '✝️',
        'Sugarloaf Mountain': '🏔️',
        'Copacabana Beach': '🏖️',
        'Samba Queen': '👸',
        'Rei Momo': '👑',
        'Carnival Float': '🎭',
        'Samba Dancer': '💃',
        'Bateria Drummer': '🥁',
        'Feather Headdress': '🪶',
        'Carnival Mask': '🎭',
        'Surdo Drum': '🥁',
        'Cuica': '🎵',
        'Tamborim': '🎶',
        'Maracas': '🎶',
        'Caipirinha': '🍹',
        'Açaí': '🫐',
        'Coconut': '🥥',
        'Toucan': '🦜',
        'Wild': '🌟'
    };

    return icons[symbolName] || '🎪';
}

// Animate parade progress
function animateParadeProgress() {
    let currentProgress = 0;
    const targetProgress = Math.min(100, (sambaCarnivalGame.parade.currentFloat + 1) * 8.33); // 12 floats = 100%

    const progressInterval = setInterval(() => {
        currentProgress += 2;
        document.getElementById('paradeBar').style.width = `${currentProgress}%`;

        if (currentProgress >= targetProgress) {
            clearInterval(progressInterval);
            sambaCarnivalGame.parade.currentFloat++;
            document.getElementById('currentFloat').textContent =
                `Float ${sambaCarnivalGame.parade.currentFloat}/${sambaCarnivalGame.parade.totalFloats}`;
        }
    }, 50);
}

// Resolve carnival spin with extreme bias
function resolveCarnivalSpin() {
    const carnivalData = CARNIVAL_MODES[sambaCarnivalGame.carnivalMode];
    const paradeData = PARADE_LEVELS[sambaCarnivalGame.paradeLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate symbol winnings with extreme reductions
    const allSymbols = [];
    sambaCarnivalGame.reels.forEach(reel => {
        allSymbols.push(...reel);
    });

    // Count symbol occurrences and wilds
    const symbolCounts = {};
    let wildCount = 0;

    allSymbols.forEach(symbol => {
        if (symbol.name === 'Wild') {
            wildCount++;
        } else {
            symbolCounts[symbol.name] = (symbolCounts[symbol.name] || 0) + 1;
        }
    });

    // Calculate payouts (severely reduced)
    Object.entries(symbolCounts).forEach(([symbolName, count]) => {
        // Wilds can substitute (but rarely help due to bias)
        const effectiveCount = count + Math.floor(wildCount * 0.3); // Wilds only 30% effective

        if (effectiveCount >= 3) { // Need at least 3 symbols
            const symbol = sambaCarnivalGame.symbols.find(s => s.name === symbolName);
            if (symbol) {
                const payoutKey = symbolName.toUpperCase().replace(/[^A-Z]/g, '_');
                if (CARNIVAL_PAYOUTS[payoutKey]) {
                    let symbolPayout = sambaCarnivalGame.betAmount * (CARNIVAL_PAYOUTS[payoutKey] / 100);

                    // Apply count multiplier (minimal)
                    if (effectiveCount === 4) symbolPayout *= 1.2;
                    if (effectiveCount === 5) symbolPayout *= 1.5;

                    totalWinnings += symbolPayout;
                }
            }
        }
    });

    // Apply parade wild multiplier (fake - almost never applies)
    if (sambaCarnivalGame.paradeWilds.active && wildCount > 0 && Math.random() < 0.02) {
        const wildBonus = Math.floor(totalWinnings * CARNIVAL_PAYOUTS.PARADE_WILD);
        totalWinnings += wildBonus;
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * carnivalData.payoutMultiplier);

    // Apply parade energy penalty
    if (sambaCarnivalGame.parade.energy < 50) {
        const energyPenalty = (50 - sambaCarnivalGame.parade.energy) / 100;
        totalWinnings = Math.floor(totalWinnings * (1 - energyPenalty));
    }

    // Apply crowd bias penalty
    if (Math.random() < carnivalData.crowdBias) {
        const crowdPenalty = carnivalData.crowdBias * 0.4;
        totalWinnings = Math.floor(totalWinnings * (1 - crowdPenalty));
    }

    // Parade complexity can void wins
    if (totalWinnings > 0 && Math.random() < carnivalData.paradeComplexity * 0.3) {
        totalWinnings = 0;
        resultMessage = 'Parade judges disqualified performance!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    if (!resultMessage) {
        if (totalWinnings > sambaCarnivalGame.betAmount) {
            resultMessage = 'Carnival celebration pays off!';
        } else if (totalWinnings > 0) {
            resultMessage = 'Small carnival prize';
        } else {
            resultMessage = 'The parade continues...';
        }
    }

    // Add winnings to balance
    balance += totalWinnings;
    sambaCarnivalGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(totalWinnings > sambaCarnivalGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Parade Energy: ${Math.floor(sambaCarnivalGame.parade.energy)}%`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Update wild display
    if (wildCount > 0) {
        document.getElementById('wildCount').textContent = wildCount;
        document.getElementById('wildBar').style.width = `${Math.min(100, wildCount * 20)}%`;
        document.getElementById('wildMultiplier').textContent = `${paradeData.wildMultiplier}x`;
    }

    setTimeout(() => resetGame(), 6000);
}

// Update carnival display
function updateCarnivalDisplay() {
    const carnivalData = CARNIVAL_MODES[sambaCarnivalGame.carnivalMode];
    const paradeData = PARADE_LEVELS[sambaCarnivalGame.paradeLevel];

    if (sambaCarnivalGame.paradeWilds.active) {
        document.getElementById('wildStatus').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">WILDS: ACTIVE!</div>';
    } else {
        document.getElementById('wildStatus').innerHTML =
            '<div class="text-purple-400 font-bold">WILDS: READY</div>';
    }
}

// Update parade effects
function updateParadeEffects() {
    // Update samba rhythm visualization
    const rhythmBars = document.querySelectorAll('#sambaRhythm .w-2');
    rhythmBars.forEach((bar, index) => {
        if (sambaCarnivalGame.parade.rhythm > 0.8) {
            bar.classList.remove('bg-gray-700');
            bar.classList.add('bg-orange-400');
        } else {
            bar.classList.remove('bg-orange-400');
            bar.classList.add('bg-gray-700');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${sambaCarnivalGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = sambaCarnivalGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${sambaCarnivalGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${sambaCarnivalGame.stats.totalWagered}`;
    document.getElementById('paradeWildsTriggered').textContent = sambaCarnivalGame.stats.paradeWildsTriggered;
    document.getElementById('floatsCompleted').textContent = sambaCarnivalGame.stats.floatsCompleted;

    const netResult = sambaCarnivalGame.stats.totalWon - sambaCarnivalGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    sambaCarnivalGame.stats.spinsPlayed++;
    sambaCarnivalGame.stats.totalWagered += sambaCarnivalGame.betAmount;
    sambaCarnivalGame.stats.totalWon += winnings;
    sambaCarnivalGame.stats.floatsCompleted += sambaCarnivalGame.parade.currentFloat;

    if (won) {
        sambaCarnivalGame.stats.spinsWon++;
        sambaCarnivalGame.stats.currentStreak++;
        sambaCarnivalGame.streakData.currentWinStreak++;
        sambaCarnivalGame.streakData.currentLossStreak = 0;

        if (sambaCarnivalGame.streakData.currentWinStreak > sambaCarnivalGame.streakData.longestWinStreak) {
            sambaCarnivalGame.streakData.longestWinStreak = sambaCarnivalGame.streakData.currentWinStreak;
        }

        if (winnings > sambaCarnivalGame.stats.biggestWin) {
            sambaCarnivalGame.stats.biggestWin = winnings;
        }
    } else {
        sambaCarnivalGame.stats.currentStreak = 0;
        sambaCarnivalGame.streakData.currentWinStreak = 0;
        sambaCarnivalGame.streakData.currentLossStreak++;

        if (sambaCarnivalGame.streakData.currentLossStreak > sambaCarnivalGame.streakData.longestLossStreak) {
            sambaCarnivalGame.streakData.longestLossStreak = sambaCarnivalGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to carnival effects)
    sambaCarnivalGame.stats.winRate = (sambaCarnivalGame.stats.spinsWon / sambaCarnivalGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next carnival
function resetGame() {
    sambaCarnivalGame.isPlaying = false;
    sambaCarnivalGame.betAmount = 0;
    sambaCarnivalGame.totalBet = 0;
    sambaCarnivalGame.gameResult = '';
    sambaCarnivalGame.totalWin = 0;

    // Reset parade system
    sambaCarnivalGame.parade.currentFloat = 0;
    sambaCarnivalGame.parade.dancers = 0;
    sambaCarnivalGame.parade.musicians = 0;
    sambaCarnivalGame.parade.energy = 100;
    sambaCarnivalGame.parade.rhythm = 1.0;
    sambaCarnivalGame.parade.crowdExcitement = 50;
    sambaCarnivalGame.parade.judgesScore = 0;
    sambaCarnivalGame.parade.sambaDrummers = 0;
    sambaCarnivalGame.parade.featherCount = 0;

    // Reset parade wilds system
    sambaCarnivalGame.paradeWilds.active = false;
    sambaCarnivalGame.paradeWilds.wildFloats = [];
    sambaCarnivalGame.paradeWilds.dancerWilds = 0;
    sambaCarnivalGame.paradeWilds.musicianWilds = 0;
    sambaCarnivalGame.paradeWilds.featherWilds = 0;
    sambaCarnivalGame.paradeWilds.drumWilds = 0;
    sambaCarnivalGame.paradeWilds.multiplier = 1;
    sambaCarnivalGame.paradeWilds.paradeLength = 0;
    sambaCarnivalGame.paradeWilds.wildExpansion = false;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        sambaCarnivalGame.reels[i] = [];
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('carnivalEvent').classList.add('hidden');

    // Reset reel displays
    for (let reel = 1; reel <= 5; reel++) {
        const reelElement = document.getElementById(`reel${reel}`);
        const symbols = reelElement.querySelectorAll('.reel-symbol');
        symbols.forEach(symbol => {
            symbol.innerHTML = '';
            symbol.className = 'reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs';
        });
    }

    // Reset carnival status
    document.getElementById('cityStatus').innerHTML =
        '<div class="text-yellow-400 font-bold">CITY: RIO DE JANEIRO</div>';
    document.getElementById('schoolStatus').innerHTML =
        '<div class="text-green-400 font-bold">SCHOOL: MANGUEIRA</div>';
    document.getElementById('crowdStatus').innerHTML =
        '<div class="text-blue-400 font-bold">CROWD: 2M PEOPLE</div>';
    document.getElementById('wildStatus').innerHTML =
        '<div class="text-purple-400 font-bold">WILDS: READY</div>';

    // Reset parade progress
    document.getElementById('paradeBar').style.width = '0%';
    document.getElementById('currentFloat').textContent = 'Float 1/12';

    // Reset wild display
    document.getElementById('wildCount').textContent = '0';
    document.getElementById('wildBar').style.width = '0%';
    document.getElementById('wildMultiplier').textContent = '1x';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startParade').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Carnival awaits...';
    document.getElementById('gameMessage').textContent = 'Welcome to Samba Spins Carnival - Where Every Spin is a Brazilian Celebration';
    document.getElementById('paradeEnergyDisplay').textContent = '100%';
    document.getElementById('rhythmLevel').textContent = 'Perfect';

    // Reset samba rhythm bars
    const rhythmBars = document.querySelectorAll('#sambaRhythm .w-2');
    rhythmBars.forEach(bar => {
        bar.classList.remove('bg-orange-400');
        bar.classList.add('bg-gray-700');
    });

    // Reinitialize systems for next carnival
    initializeCarnivalSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSambaCarnivalGame();
});