// Aztec Sun Sacrifice - Pyramid-Climbing Volatility
// Ultra High House Edge Implementation with Ancient Aztec Theme
// Designed to maintain <0.8% player win rate

// Game state
let balance = 1000;

// Game state object with Aztec pyramid system
let aztecSunGame = {
    isPlaying: false,
    volatilityMode: 'ascending', // ascending, descending, chaotic, eclipse
    pyramidLevel: 1, // 1-13 levels (like Aztec calendar)
    betAmount: 0,
    totalBet: 0,

    // Pyramid climbing system
    pyramid: {
        currentLevel: 1,
        maxLevel: 13,
        climbed: [],
        sacrifices: 0,
        sunFavor: 0,
        bloodOffered: 0,
        godsApproval: 0,
        cursed: false
    },

    // Aztec calendar and sun system
    calendar: {
        day: 1,
        tonalpohualli: 1, // 260-day sacred calendar
        xiuhpohualli: 1, // 365-day solar calendar
        currentSun: 'Nahui-Ollin', // Fifth Sun
        eclipseRisk: 0,
        sunAnger: 0,
        sacrificeDebt: 0
    },

    // Volatility system with Aztec gods
    volatility: {
        active: false,
        level: 1,
        direction: 'up',
        godInfluence: '<PERSON><PERSON><PERSON><PERSON>', // Sun god
        divineIntervention: 0,
        cosmicAlignment: 0,
        bloodThirst: 0,
        curseStrength: 0
    },

    // Aztec symbols and their powers
    symbols: [
        { name: '<PERSON><PERSON><PERSON><PERSON>', power: 100, rarity: 0.01, blessing: false }, // Sun God
        { name: 'Quetzalcoatl', power: 80, rarity: 0.02, blessing: false }, // Feathered Serpent
        { name: 'Tezcatlipoca', power: 75, rarity: 0.03, blessing: false }, // Smoking Mirror
        { name: 'Tlaloc', power: 60, rarity: 0.05, blessing: false }, // Rain God
        { name: 'Xochiquetzal', power: 50, rarity: 0.08, blessing: false }, // Flower Goddess
        { name: 'Jaguar', power: 40, rarity: 0.12, blessing: false },
        { name: 'Eagle', power: 35, rarity: 0.15, blessing: false },
        { name: 'Serpent', power: 30, rarity: 0.18, blessing: false },
        { name: 'Obsidian', power: 25, rarity: 0.22, blessing: false },
        { name: 'Jade', power: 20, rarity: 0.25, blessing: false },
        { name: 'Cacao', power: 15, rarity: 0.30, blessing: false },
        { name: 'Maize', power: 10, rarity: 0.35, blessing: false }
    ],

    currentSymbols: [],
    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        pyramidLevelsClimbed: 0,
        sacrificesOffered: 0,
        godsAppeased: 0,
        cursesReceived: 0,
        eclipsesWitnessed: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Volatility modes with extreme Aztec bias
const VOLATILITY_MODES = {
    ascending: {
        name: 'Ascending Sun',
        houseEdge: 0.82, // 82% house edge
        pyramidBias: 0.45, // 45% pyramid bias
        payoutMultiplier: 0.15, // Severely reduced payouts
        godsFavor: 0.20, // 20% gods favor
        sacrificeRequirement: 0.60 // 60% sacrifice requirement
    },
    descending: {
        name: 'Descending Sun',
        houseEdge: 0.87, // 87% house edge
        pyramidBias: 0.58, // 58% pyramid bias
        payoutMultiplier: 0.12, // Even more reduced payouts
        godsFavor: 0.15, // 15% gods favor
        sacrificeRequirement: 0.72 // 72% sacrifice requirement
    },
    chaotic: {
        name: 'Chaotic Eclipse',
        houseEdge: 0.92, // 92% house edge
        pyramidBias: 0.72, // 72% pyramid bias
        payoutMultiplier: 0.08, // Extremely reduced payouts
        godsFavor: 0.10, // 10% gods favor
        sacrificeRequirement: 0.85 // 85% sacrifice requirement
    },
    eclipse: {
        name: 'Total Eclipse',
        houseEdge: 0.97, // 97% house edge
        pyramidBias: 0.88, // 88% pyramid bias
        payoutMultiplier: 0.04, // Brutally reduced payouts
        godsFavor: 0.05, // 5% gods favor
        sacrificeRequirement: 0.95 // 95% sacrifice requirement
    }
};

// Severely reduced payout table with Aztec theme
const AZTEC_PAYOUTS = {
    // Symbol payouts (heavily reduced)
    TONATIUH: 50, // Reduced from 1000:1
    QUETZALCOATL: 25, // Reduced from 500:1
    TEZCATLIPOCA: 20, // Reduced from 400:1
    TLALOC: 15, // Reduced from 300:1
    XOCHIQUETZAL: 10, // Reduced from 200:1
    JAGUAR: 8, // Reduced from 150:1
    EAGLE: 6, // Reduced from 100:1
    SERPENT: 4, // Reduced from 75:1
    OBSIDIAN: 3, // Reduced from 50:1
    JADE: 2, // Reduced from 25:1
    CACAO: 1.5, // Reduced from 15:1
    MAIZE: 1, // Reduced from 10:1

    // Pyramid level bonuses (fake - almost never apply)
    PYRAMID_BONUS: 0.05, // 5% of displayed bonus
    SUN_BLESSING: 0.02, // 2% of displayed bonus
    SACRIFICE_REWARD: 0.01 // 1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadAztecSunGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">AZTEC CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VOLATILITY MODE</label>
                        <select id="volatilityMode" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="ascending">Ascending Sun</option>
                            <option value="descending">Descending Sun</option>
                            <option value="chaotic">Chaotic Eclipse</option>
                            <option value="eclipse">Total Eclipse</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="climbPyramid" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        CLIMB THE PYRAMID
                    </button>

                    <div id="sacrificeActions" class="space-y-2 hidden">
                        <button id="offerSacrifice" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            OFFER SACRIFICE
                        </button>
                        <button id="seekBlessing" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            SEEK SUN BLESSING
                        </button>
                        <button id="descendPyramid" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            DESCEND SAFELY
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Pyramid Level</div>
                        <div id="pyramidLevelDisplay" class="text-lg font-bold text-orange-400">1</div>
                    </div>
                </div>

                <!-- Aztec Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">AZTEC STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="sunStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">SUN: RISING</div>
                        </div>
                        <div id="godsStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">GODS: NEUTRAL</div>
                        </div>
                        <div id="sacrificeStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">SACRIFICES: 0</div>
                        </div>
                        <div id="curseStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">CURSE: NONE</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">AZTEC PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Divine Symbols:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tonatiuh (Sun):</span>
                            <span class="text-red-400">50:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Quetzalcoatl:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tezcatlipoca:</span>
                            <span class="text-red-400">20:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Sacred Animals:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Jaguar:</span>
                            <span class="text-red-400">8:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Eagle:</span>
                            <span class="text-red-400">6:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Gods demand sacrifices</div>
                        <div class="text-xs text-red-400">*Eclipse may void all wins</div>
                    </div>
                </div>
            </div>

            <!-- Main Aztec Pyramid -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <!-- Aztec Pyramid with Sun -->
                    <div id="aztecPyramid" class="relative bg-gradient-to-br from-black via-yellow-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Sun and Sky Background -->
                        <div id="aztecSky" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="sunGradient" cx="50%" cy="20%" r="30%">
                                        <stop offset="0%" style="stop-color:#ffff00;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#ff8000;stop-opacity:0.4" />
                                        <stop offset="100%" style="stop-color:#ff0000;stop-opacity:0.2" />
                                    </radialGradient>
                                    <pattern id="aztecPattern" width="30" height="30" patternUnits="userSpaceOnUse">
                                        <path d="M 15 0 L 30 15 L 15 30 L 0 15 Z" fill="none" stroke="#ffaa00" stroke-width="0.5" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#aztecPattern)" />
                                <circle id="aztecSun" cx="50%" cy="20%" r="15%" fill="url(#sunGradient)" class="animate-pulse" />
                                <g id="sunRays">
                                    <!-- Sun rays will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Pyramid Structure -->
                        <div id="pyramidStructure" class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                            <div class="relative">
                                <!-- Pyramid levels will be generated here -->
                                <div id="pyramidLevels" class="flex flex-col-reverse items-center">
                                    <!-- Dynamic pyramid levels -->
                                </div>

                                <!-- Player position indicator -->
                                <div id="playerPosition" class="absolute w-6 h-6 bg-blue-500 rounded-full border-2 border-white transform transition-all duration-1000 hidden">
                                    <div class="w-full h-full rounded-full animate-pulse"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Symbol Display Area -->
                        <div class="absolute top-4 left-4 right-4">
                            <div class="text-center">
                                <div class="text-sm text-yellow-400 mb-2">AZTEC SYMBOLS</div>
                                <div id="symbolsDisplay" class="flex space-x-2 justify-center flex-wrap">
                                    <!-- Symbols will appear here -->
                                </div>
                            </div>
                        </div>

                        <!-- Volatility Indicator -->
                        <div id="volatilityIndicator" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">VOLATILITY</div>
                                <div id="volatilityLevel" class="text-sm font-bold text-white">Ascending</div>
                                <div class="w-20 bg-gray-700 rounded-full h-2 mt-1">
                                    <div id="volatilityBar" class="bg-yellow-400 h-2 rounded-full transition-all duration-300" style="width: 25%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Sacrifice Altar -->
                        <div id="sacrificeAltar" class="absolute bottom-4 right-4 hidden">
                            <div class="bg-red-900/70 rounded-lg p-3 border border-red-500">
                                <div class="text-xs text-red-400 mb-1">SACRIFICE ALTAR</div>
                                <div id="altarFlame" class="text-lg text-center animate-pulse">🔥</div>
                                <div id="bloodOffered" class="text-xs text-red-400">Blood: 0</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Sun awaits your climb...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="aztecEvent" class="text-sm font-bold text-orange-400 hidden animate-pulse">GODS HAVE SPOKEN!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Aztec Sun Sacrifice - Climb the Sacred Pyramid to Honor Tonatiuh</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Sacrifices</div>
                <div id="sacrificesOffered" class="text-xl font-bold text-red-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Eclipses</div>
                <div id="eclipsesWitnessed" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeAztecSun();
}

// Initialize the game
function initializeAztecSun() {
    document.getElementById('climbPyramid').addEventListener('click', startClimbing);
    document.getElementById('offerSacrifice').addEventListener('click', () => playerAction('sacrifice'));
    document.getElementById('seekBlessing').addEventListener('click', () => playerAction('blessing'));
    document.getElementById('descendPyramid').addEventListener('click', () => playerAction('descend'));

    // Initialize Aztec systems
    initializeAztecSystems();
    generatePyramidStructure();
    generateSunRays();
    updateGameStats();
}

// Initialize Aztec systems
function initializeAztecSystems() {
    // Reset pyramid
    aztecSunGame.pyramid.currentLevel = 1;
    aztecSunGame.pyramid.climbed = [];
    aztecSunGame.pyramid.sacrifices = 0;
    aztecSunGame.pyramid.sunFavor = 0;
    aztecSunGame.pyramid.bloodOffered = 0;
    aztecSunGame.pyramid.godsApproval = 0;
    aztecSunGame.pyramid.cursed = false;

    // Reset calendar
    aztecSunGame.calendar.day = Math.floor(Math.random() * 260) + 1;
    aztecSunGame.calendar.tonalpohualli = aztecSunGame.calendar.day;
    aztecSunGame.calendar.xiuhpohualli = Math.floor(Math.random() * 365) + 1;
    aztecSunGame.calendar.currentSun = 'Nahui-Ollin';
    aztecSunGame.calendar.eclipseRisk = Math.random() * 0.3;
    aztecSunGame.calendar.sunAnger = 0;
    aztecSunGame.calendar.sacrificeDebt = 0;

    // Reset volatility
    aztecSunGame.volatility.active = false;
    aztecSunGame.volatility.level = 1;
    aztecSunGame.volatility.direction = 'up';
    aztecSunGame.volatility.godInfluence = 'Tonatiuh';
    aztecSunGame.volatility.divineIntervention = 0;
    aztecSunGame.volatility.cosmicAlignment = 0;
    aztecSunGame.volatility.bloodThirst = 0;
    aztecSunGame.volatility.curseStrength = 0;

    updateAztecDisplay();
}

// Generate pyramid structure
function generatePyramidStructure() {
    const container = document.getElementById('pyramidLevels');
    container.innerHTML = '';

    for (let level = 1; level <= aztecSunGame.pyramid.maxLevel; level++) {
        const levelElement = document.createElement('div');
        const width = 20 + (aztecSunGame.pyramid.maxLevel - level) * 8; // Wider at bottom

        levelElement.className = `h-4 bg-gradient-to-r from-yellow-600 to-orange-600 border border-yellow-400 flex items-center justify-center text-xs font-bold text-white`;
        levelElement.style.width = `${width}px`;
        levelElement.textContent = level;
        levelElement.dataset.level = level;

        // Add Aztec patterns
        if (level % 3 === 0) {
            levelElement.classList.add('bg-gradient-to-r', 'from-red-600', 'to-orange-600');
        }

        container.appendChild(levelElement);
    }
}

// Generate sun rays
function generateSunRays() {
    const container = document.getElementById('sunRays');
    container.innerHTML = '';

    for (let i = 0; i < 12; i++) {
        const angle = (i * 30) - 90; // 12 rays, 30 degrees apart
        const ray = document.createElementNS('http://www.w3.org/2000/svg', 'line');

        const centerX = 50; // 50% of viewBox
        const centerY = 20; // 20% of viewBox
        const length = 8;

        const x1 = centerX + Math.cos(angle * Math.PI / 180) * 15;
        const y1 = centerY + Math.sin(angle * Math.PI / 180) * 15;
        const x2 = centerX + Math.cos(angle * Math.PI / 180) * (15 + length);
        const y2 = centerY + Math.sin(angle * Math.PI / 180) * (15 + length);

        ray.setAttribute('x1', `${x1}%`);
        ray.setAttribute('y1', `${y1}%`);
        ray.setAttribute('x2', `${x2}%`);
        ray.setAttribute('y2', `${y2}%`);
        ray.setAttribute('stroke', '#ffff00');
        ray.setAttribute('stroke-width', '2');
        ray.setAttribute('opacity', '0.8');
        ray.classList.add('animate-pulse');

        container.appendChild(ray);
    }
}

// Start climbing the pyramid
function startClimbing() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    aztecSunGame.isPlaying = true;
    aztecSunGame.betAmount = betAmount;
    aztecSunGame.totalBet = betAmount;
    aztecSunGame.volatilityMode = document.getElementById('volatilityMode').value;
    aztecSunGame.pyramidLevel = 1;

    // Clear previous symbols
    aztecSunGame.currentSymbols = [];

    // Activate Aztec systems
    activateAztecSystems();

    // Start pyramid climb
    setTimeout(() => {
        beginPyramidClimb();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('climbPyramid').disabled = true;
    document.getElementById('gameStatus').textContent = 'Invoking the gods...';
}

// Activate Aztec systems
function activateAztecSystems() {
    const volatilityData = VOLATILITY_MODES[aztecSunGame.volatilityMode];

    aztecSunGame.volatility.active = true;
    aztecSunGame.volatility.level = Math.random() * volatilityData.pyramidBias + 0.2;
    aztecSunGame.calendar.sunAnger = Math.random() * (1 - volatilityData.godsFavor);
    aztecSunGame.calendar.sacrificeDebt = volatilityData.sacrificeRequirement;

    // Activate divine systems
    if (Math.random() < volatilityData.pyramidBias) {
        activateGodsWrath();
    }

    if (Math.random() < aztecSunGame.calendar.eclipseRisk) {
        activateEclipse();
    }

    if (Math.random() < volatilityData.sacrificeRequirement) {
        demandSacrifice();
    }

    // Update visual effects
    updateAztecDisplay();
    updateVolatilityEffects();
}

// Activate gods wrath
function activateGodsWrath() {
    aztecSunGame.volatility.godInfluence = 'Tezcatlipoca'; // Angry god
    aztecSunGame.volatility.divineIntervention = Math.random() * 0.8 + 0.2;
    aztecSunGame.pyramid.cursed = true;

    document.getElementById('godsStatus').innerHTML =
        '<div class="text-red-400 font-bold animate-pulse">GODS: WRATHFUL</div>';

    document.getElementById('curseStatus').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">CURSE: ACTIVE</div>';
}

// Activate eclipse
function activateEclipse() {
    aztecSunGame.calendar.eclipseRisk = 1.0;
    aztecSunGame.stats.eclipsesWitnessed++;

    // Darken the sun
    document.getElementById('aztecSun').style.fill = 'url(#eclipseGradient)';

    // Show Aztec event
    document.getElementById('aztecEvent').classList.remove('hidden');
    document.getElementById('aztecEvent').textContent = 'SOLAR ECLIPSE - THE GODS HUNGER!';
    setTimeout(() => {
        document.getElementById('aztecEvent').classList.add('hidden');
    }, 4000);
}

// Demand sacrifice
function demandSacrifice() {
    aztecSunGame.volatility.bloodThirst = Math.random() * 0.9 + 0.1;

    document.getElementById('sacrificeAltar').classList.remove('hidden');
    document.getElementById('sacrificeStatus').innerHTML =
        '<div class="text-red-400 font-bold animate-pulse">SACRIFICES: DEMANDED</div>';
}

// Begin pyramid climb
function beginPyramidClimb() {
    document.getElementById('gameStatus').textContent = 'Climbing the sacred pyramid...';

    // Show player position
    const playerPos = document.getElementById('playerPosition');
    playerPos.classList.remove('hidden');

    // Generate initial symbols with extreme bias
    generateAztecSymbols();

    // Start climbing animation
    animateClimbing();

    // Enable sacrifice actions after initial climb
    setTimeout(() => {
        enableSacrificeActions();
    }, 3000);
}

// Generate Aztec symbols with extreme bias
function generateAztecSymbols() {
    const volatilityData = VOLATILITY_MODES[aztecSunGame.volatilityMode];
    const symbolCount = 3; // Generate 3 symbols

    aztecSunGame.currentSymbols = [];

    for (let i = 0; i < symbolCount; i++) {
        let symbol;

        // Apply extreme bias toward low-value symbols
        if (Math.random() < volatilityData.pyramidBias) {
            // Bias toward common, low-value symbols
            const lowValueSymbols = aztecSunGame.symbols.filter(s => s.power <= 25);
            symbol = lowValueSymbols[Math.floor(Math.random() * lowValueSymbols.length)];
        } else {
            // Random selection (still biased by rarity)
            symbol = selectSymbolByRarity();
        }

        // Apply curse effects
        if (aztecSunGame.pyramid.cursed && Math.random() < 0.7) {
            // Curse downgrades symbols
            const downgradedSymbols = aztecSunGame.symbols.filter(s => s.power < symbol.power);
            if (downgradedSymbols.length > 0) {
                symbol = downgradedSymbols[Math.floor(Math.random() * downgradedSymbols.length)];
            }
        }

        aztecSunGame.currentSymbols.push({...symbol});
    }

    displayAztecSymbols();
}

// Select symbol by rarity (biased toward common)
function selectSymbolByRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Reverse order to favor common symbols
    for (let i = aztecSunGame.symbols.length - 1; i >= 0; i--) {
        const symbol = aztecSunGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to most common symbol
    return aztecSunGame.symbols[aztecSunGame.symbols.length - 1];
}

// Display Aztec symbols
function displayAztecSymbols() {
    const container = document.getElementById('symbolsDisplay');
    container.innerHTML = '';

    aztecSunGame.currentSymbols.forEach((symbol, index) => {
        const symbolElement = document.createElement('div');
        symbolElement.className = 'w-16 h-16 bg-gradient-to-br from-yellow-600 to-orange-600 rounded-lg border-2 border-yellow-400 flex flex-col items-center justify-center text-white text-xs font-bold shadow-lg transform transition-all duration-500';

        // Add symbol-specific styling
        if (symbol.power >= 75) {
            symbolElement.classList.add('ring-2', 'ring-yellow-400', 'animate-pulse');
        } else if (symbol.power >= 50) {
            symbolElement.classList.add('ring-2', 'ring-orange-400');
        }

        // Symbol representation
        const symbolIcon = getSymbolIcon(symbol.name);
        symbolElement.innerHTML = `
            <div class="text-lg">${symbolIcon}</div>
            <div class="text-xs">${symbol.name}</div>
            <div class="text-xs">${symbol.power}</div>
        `;

        // Animation
        symbolElement.style.transform = 'translateY(-20px) scale(1.2)';
        setTimeout(() => {
            symbolElement.style.transform = 'translateY(0) scale(1)';
        }, index * 200 + 300);

        container.appendChild(symbolElement);
    });
}

// Get symbol icon
function getSymbolIcon(symbolName) {
    const icons = {
        'Tonatiuh': '☀️',
        'Quetzalcoatl': '🐍',
        'Tezcatlipoca': '🌙',
        'Tlaloc': '⛈️',
        'Xochiquetzal': '🌺',
        'Jaguar': '🐆',
        'Eagle': '🦅',
        'Serpent': '🐍',
        'Obsidian': '⚫',
        'Jade': '💚',
        'Cacao': '🍫',
        'Maize': '🌽'
    };

    return icons[symbolName] || '❓';
}

// Animate climbing
function animateClimbing() {
    const playerPos = document.getElementById('playerPosition');
    const currentLevel = aztecSunGame.pyramid.currentLevel;

    // Calculate position based on pyramid level
    const levelElement = document.querySelector(`[data-level="${currentLevel}"]`);
    if (levelElement) {
        const rect = levelElement.getBoundingClientRect();
        const pyramidRect = document.getElementById('pyramidStructure').getBoundingClientRect();

        const relativeX = rect.left - pyramidRect.left + rect.width / 2 - 12; // Center on level
        const relativeY = rect.top - pyramidRect.top - 24; // Above level

        playerPos.style.left = `${relativeX}px`;
        playerPos.style.top = `${relativeY}px`;
    }

    // Update pyramid level display
    document.getElementById('pyramidLevelDisplay').textContent = currentLevel;
}

// Enable sacrifice actions
function enableSacrificeActions() {
    document.getElementById('sacrificeActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Choose your offering to the gods...';
    document.getElementById('gameMessage').textContent = 'The gods watch your every move - choose wisely or face their wrath';
}

// Player action handler
function playerAction(action) {
    const volatilityData = VOLATILITY_MODES[aztecSunGame.volatilityMode];

    if (action === 'sacrifice') {
        // Offer sacrifice to appease gods
        aztecSunGame.pyramid.sacrifices++;
        aztecSunGame.pyramid.bloodOffered += Math.random() * 0.5 + 0.5;
        aztecSunGame.stats.sacrificesOffered++;

        // Update sacrifice display
        document.getElementById('bloodOffered').textContent = `Blood: ${aztecSunGame.pyramid.bloodOffered.toFixed(1)}`;
        document.getElementById('sacrificeStatus').innerHTML =
            `<div class="text-red-400 font-bold">SACRIFICES: ${aztecSunGame.pyramid.sacrifices}</div>`;

        // Sacrifice may appease gods (rarely)
        if (Math.random() < volatilityData.godsFavor * 0.3) {
            aztecSunGame.pyramid.godsApproval += 0.2;
            aztecSunGame.calendar.sunAnger = Math.max(0, aztecSunGame.calendar.sunAnger - 0.1);
        }

        // Continue climbing or resolve
        if (aztecSunGame.pyramid.currentLevel < aztecSunGame.pyramid.maxLevel && Math.random() < 0.3) {
            climbHigher();
        } else {
            resolveClimbWithSacrifice();
        }

    } else if (action === 'blessing') {
        // Seek sun blessing (risky)
        if (Math.random() < volatilityData.godsFavor) {
            // Blessing granted (rare)
            aztecSunGame.pyramid.sunFavor += 0.3;
            document.getElementById('sunStatus').innerHTML =
                '<div class="text-yellow-400 font-bold animate-pulse">SUN: BLESSED</div>';
        } else {
            // Blessing denied - anger gods
            aztecSunGame.calendar.sunAnger += 0.4;
            aztecSunGame.volatility.curseStrength += 0.3;
        }

        resolveClimbWithSacrifice();

    } else if (action === 'descend') {
        // Descend safely (coward's path)
        resolveClimbWithSacrifice('descend');
    }

    document.getElementById('sacrificeActions').classList.add('hidden');
}

// Climb higher on pyramid
function climbHigher() {
    aztecSunGame.pyramid.currentLevel = Math.min(aztecSunGame.pyramid.maxLevel, aztecSunGame.pyramid.currentLevel + 1);
    aztecSunGame.pyramid.climbed.push(aztecSunGame.pyramid.currentLevel);

    // Animate climbing
    animateClimbing();

    // Generate new symbols for higher level
    setTimeout(() => {
        generateAztecSymbols();
        enableSacrificeActions();
    }, 1500);
}

// Resolve climb with extreme Aztec bias
function resolveClimbWithSacrifice(action = 'normal') {
    const volatilityData = VOLATILITY_MODES[aztecSunGame.volatilityMode];

    let totalWinnings = 0;
    let resultMessage = '';

    if (action === 'descend') {
        // Coward's descent - minimal return
        totalWinnings = Math.floor(aztecSunGame.betAmount * 0.1); // 10% return
        resultMessage = 'Descended safely - Gods show mercy';
        aztecSunGame.gameResult = 'descend';
    } else {
        // Calculate symbol winnings with extreme reductions
        aztecSunGame.currentSymbols.forEach(symbol => {
            let symbolPayout = 0;

            // Base payout (already severely reduced)
            const payoutKey = symbol.name.toUpperCase().replace(/[^A-Z]/g, '');
            if (AZTEC_PAYOUTS[payoutKey]) {
                symbolPayout = aztecSunGame.betAmount * (AZTEC_PAYOUTS[payoutKey] / 100); // Further reduced
            }

            // Apply pyramid level bonus (fake - almost never applies)
            if (aztecSunGame.pyramid.currentLevel >= 10 && Math.random() < 0.02) {
                const pyramidBonus = Math.floor(symbolPayout * AZTEC_PAYOUTS.PYRAMID_BONUS);
                symbolPayout += pyramidBonus;
            }

            // Apply sun blessing bonus (fake - almost never applies)
            if (aztecSunGame.pyramid.sunFavor > 0.5 && Math.random() < 0.01) {
                const sunBonus = Math.floor(symbolPayout * AZTEC_PAYOUTS.SUN_BLESSING);
                symbolPayout += sunBonus;
            }

            totalWinnings += symbolPayout;
        });

        // Apply sacrifice bonus (fake - almost never applies)
        if (aztecSunGame.pyramid.sacrifices > 0 && Math.random() < 0.005) {
            const sacrificeBonus = Math.floor(totalWinnings * AZTEC_PAYOUTS.SACRIFICE_REWARD);
            totalWinnings += sacrificeBonus;
        }

        resultMessage = `Climbed to level ${aztecSunGame.pyramid.currentLevel}`;
        aztecSunGame.gameResult = totalWinnings > aztecSunGame.betAmount ? 'win' : 'lose';
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * volatilityData.payoutMultiplier);

    // Apply gods wrath penalty
    if (aztecSunGame.calendar.sunAnger > 0.5) {
        const wrathPenalty = aztecSunGame.calendar.sunAnger * 0.6;
        totalWinnings = Math.floor(totalWinnings * (1 - wrathPenalty));
    }

    // Apply curse penalty
    if (aztecSunGame.pyramid.cursed) {
        const cursePenalty = aztecSunGame.volatility.curseStrength * 0.5;
        totalWinnings = Math.floor(totalWinnings * (1 - cursePenalty));
    }

    // Eclipse voids all wins
    if (aztecSunGame.calendar.eclipseRisk >= 1.0 && Math.random() < 0.4) {
        totalWinnings = 0;
        resultMessage += ' - ECLIPSE DEVOURS ALL!';
    }

    // Sacrifice debt penalty
    if (aztecSunGame.pyramid.sacrifices < aztecSunGame.calendar.sacrificeDebt) {
        const debtPenalty = (aztecSunGame.calendar.sacrificeDebt - aztecSunGame.pyramid.sacrifices) * 0.3;
        totalWinnings = Math.floor(totalWinnings * (1 - debtPenalty));
        resultMessage += ' - Gods demand more blood!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    aztecSunGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterGame(totalWinnings > aztecSunGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Sun's favor: ${Math.floor(aztecSunGame.pyramid.sunFavor * 100)}%`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update Aztec display
function updateAztecDisplay() {
    const volatilityData = VOLATILITY_MODES[aztecSunGame.volatilityMode];

    if (aztecSunGame.volatility.active) {
        document.getElementById('volatilityLevel').textContent = volatilityData.name;
        document.getElementById('volatilityBar').style.width = `${aztecSunGame.volatility.level * 100}%`;
    } else {
        document.getElementById('volatilityLevel').textContent = 'Dormant';
        document.getElementById('volatilityBar').style.width = '0%';
    }
}

// Update volatility effects
function updateVolatilityEffects() {
    // Update sun based on current state
    const aztecSun = document.getElementById('aztecSun');
    if (aztecSun) {
        if (aztecSunGame.calendar.eclipseRisk >= 1.0) {
            aztecSun.style.fill = '#800000'; // Dark red during eclipse
        } else if (aztecSunGame.calendar.sunAnger > 0.5) {
            aztecSun.style.fill = '#ff4000'; // Angry orange
        } else {
            aztecSun.style.fill = 'url(#sunGradient)'; // Normal sun
        }
    }
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${aztecSunGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = aztecSunGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${aztecSunGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${aztecSunGame.stats.totalWagered}`;
    document.getElementById('sacrificesOffered').textContent = aztecSunGame.stats.sacrificesOffered;
    document.getElementById('eclipsesWitnessed').textContent = aztecSunGame.stats.eclipsesWitnessed;

    const netResult = aztecSunGame.stats.totalWon - aztecSunGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after game
function updateGameStatsAfterGame(won, winnings) {
    aztecSunGame.stats.gamesPlayed++;
    aztecSunGame.stats.totalWagered += aztecSunGame.betAmount;
    aztecSunGame.stats.totalWon += winnings;
    aztecSunGame.stats.pyramidLevelsClimbed += aztecSunGame.pyramid.currentLevel;

    if (won) {
        aztecSunGame.stats.gamesWon++;
        aztecSunGame.stats.currentStreak++;
        aztecSunGame.streakData.currentWinStreak++;
        aztecSunGame.streakData.currentLossStreak = 0;

        if (aztecSunGame.streakData.currentWinStreak > aztecSunGame.streakData.longestWinStreak) {
            aztecSunGame.streakData.longestWinStreak = aztecSunGame.streakData.currentWinStreak;
        }

        if (winnings > aztecSunGame.stats.biggestWin) {
            aztecSunGame.stats.biggestWin = winnings;
        }
    } else {
        aztecSunGame.stats.currentStreak = 0;
        aztecSunGame.streakData.currentWinStreak = 0;
        aztecSunGame.streakData.currentLossStreak++;

        if (aztecSunGame.streakData.currentLossStreak > aztecSunGame.streakData.longestLossStreak) {
            aztecSunGame.streakData.longestLossStreak = aztecSunGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to Aztec effects)
    aztecSunGame.stats.winRate = (aztecSunGame.stats.gamesWon / aztecSunGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next climb
function resetGame() {
    aztecSunGame.isPlaying = false;
    aztecSunGame.betAmount = 0;
    aztecSunGame.totalBet = 0;
    aztecSunGame.currentSymbols = [];
    aztecSunGame.gameResult = '';
    aztecSunGame.totalWin = 0;

    // Reset pyramid
    aztecSunGame.pyramid.currentLevel = 1;
    aztecSunGame.pyramid.climbed = [];
    aztecSunGame.pyramid.sacrifices = 0;
    aztecSunGame.pyramid.sunFavor = 0;
    aztecSunGame.pyramid.bloodOffered = 0;
    aztecSunGame.pyramid.godsApproval = 0;
    aztecSunGame.pyramid.cursed = false;

    // Reset calendar
    aztecSunGame.calendar.eclipseRisk = Math.random() * 0.3;
    aztecSunGame.calendar.sunAnger = 0;
    aztecSunGame.calendar.sacrificeDebt = 0;

    // Reset volatility
    aztecSunGame.volatility.active = false;
    aztecSunGame.volatility.level = 1;
    aztecSunGame.volatility.direction = 'up';
    aztecSunGame.volatility.godInfluence = 'Tonatiuh';
    aztecSunGame.volatility.divineIntervention = 0;
    aztecSunGame.volatility.cosmicAlignment = 0;
    aztecSunGame.volatility.bloodThirst = 0;
    aztecSunGame.volatility.curseStrength = 0;

    // Clear displays
    document.getElementById('symbolsDisplay').innerHTML = '';
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('aztecEvent').classList.add('hidden');
    document.getElementById('playerPosition').classList.add('hidden');
    document.getElementById('sacrificeActions').classList.add('hidden');
    document.getElementById('sacrificeAltar').classList.add('hidden');

    // Reset Aztec status
    document.getElementById('sunStatus').innerHTML =
        '<div class="text-yellow-400 font-bold">SUN: RISING</div>';
    document.getElementById('godsStatus').innerHTML =
        '<div class="text-orange-400 font-bold">GODS: NEUTRAL</div>';
    document.getElementById('sacrificeStatus').innerHTML =
        '<div class="text-red-400 font-bold">SACRIFICES: 0</div>';
    document.getElementById('curseStatus').innerHTML =
        '<div class="text-purple-400 font-bold">CURSE: NONE</div>';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable climb button
    document.getElementById('climbPyramid').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Sun awaits your climb...';
    document.getElementById('gameMessage').textContent = 'Welcome to Aztec Sun Sacrifice - Climb the Sacred Pyramid to Honor Tonatiuh';
    document.getElementById('pyramidLevelDisplay').textContent = '1';

    // Reset sun appearance
    document.getElementById('aztecSun').style.fill = 'url(#sunGradient)';

    // Reset volatility display
    document.getElementById('volatilityLevel').textContent = 'Dormant';
    document.getElementById('volatilityBar').style.width = '0%';

    // Reinitialize systems for next game
    initializeAztecSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadAztecSunGame();
});