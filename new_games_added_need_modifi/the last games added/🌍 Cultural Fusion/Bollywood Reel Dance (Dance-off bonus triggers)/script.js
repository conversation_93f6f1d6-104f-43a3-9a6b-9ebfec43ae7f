// Bollywood Reel Dance - Dance-off Bonus Triggers
// Ultra High House Edge Implementation with Bollywood Theme
// Designed to maintain <0.5% player win rate

// Game state
let balance = 1000;

// Game state object with Bollywood dance system
let bollywoodDanceGame = {
    isPlaying: false,
    danceStyle: 'classical', // classical, bhangra, thumri, kathak, fusion
    rhythmLevel: 'slow', // slow, medium, fast, frantic, impossible
    betAmount: 0,
    totalBet: 0,

    // Dance system
    dance: {
        currentSequence: [],
        perfectMoves: 0,
        missedMoves: 0,
        rhythm: 1.0,
        energy: 100,
        grace: 50,
        passion: 50,
        synchronization: 0,
        choreographyComplexity: 1
    },

    // Bollywood elements
    bollywood: {
        movie: 'Mughal-E-Azam',
        hero: '<PERSON>',
        heroine: '<PERSON><PERSON><PERSON>',
        director: '<PERSON><PERSON>',
        musicDirector: '<PERSON><PERSON><PERSON><PERSON>',
        playbackSinger: '<PERSON><PERSON>',
        currentSong: '<PERSON><PERSON>',
        danceStyle: 'Classical',
        emotions: ['Love', 'Joy', 'Sorrow', 'Anger', 'Romance']
    },

    // Reel system (5 reels, 3 rows)
    reels: [
        [], [], [], [], []
    ],

    // Dance-off bonus system
    danceOff: {
        active: false,
        opponent: '<PERSON><PERSON><PERSON>',
        playerScore: 0,
        opponentScore: 0,
        movesRequired: 5,
        movesCompleted: 0,
        difficulty: 'expert',
        judgesBias: 0.85, // 85% bias against player
        audienceSupport: 0.15 // 15% audience support
    },

    // Bollywood symbols with cultural significance
    symbols: [
        { name: 'Shah Rukh Khan', value: 100, rarity: 0.005, dance: 'Romance' },
        { name: 'Madhuri Dixit', value: 95, rarity: 0.008, dance: 'Classical' },
        { name: 'Amitabh Bachchan', value: 90, rarity: 0.010, dance: 'Dramatic' },
        { name: 'Aishwarya Rai', value: 85, rarity: 0.012, dance: 'Grace' },
        { name: 'Hrithik Roshan', value: 80, rarity: 0.015, dance: 'Modern' },
        { name: 'Priyanka Chopra', value: 75, rarity: 0.018, dance: 'Fusion' },
        { name: 'Tabla', value: 60, rarity: 0.025, dance: 'Rhythm' },
        { name: 'Sitar', value: 55, rarity: 0.030, dance: 'Melody' },
        { name: 'Ghungroo', value: 50, rarity: 0.035, dance: 'Classical' },
        { name: 'Dupatta', value: 45, rarity: 0.040, dance: 'Grace' },
        { name: 'Marigold', value: 40, rarity: 0.050, dance: 'Celebration' },
        { name: 'Lotus', value: 35, rarity: 0.060, dance: 'Purity' },
        { name: 'Peacock', value: 30, rarity: 0.080, dance: 'Beauty' },
        { name: 'Elephant', value: 25, rarity: 0.100, dance: 'Strength' },
        { name: 'Tiger', value: 20, rarity: 0.120, dance: 'Power' },
        { name: 'Mango', value: 15, rarity: 0.150, dance: 'Sweetness' }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        danceOffsTriggered: 0,
        danceOffsWon: 0,
        perfectDances: 0,
        movesPerformed: 0,
        bollywoodMoments: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Dance styles with extreme choreography bias
const DANCE_STYLES = {
    classical: {
        name: 'Classical Kathak',
        houseEdge: 0.85, // 85% house edge
        rhythmComplexity: 0.40, // 40% rhythm complexity
        payoutMultiplier: 0.12, // Severely reduced payouts
        danceOffChance: 0.15, // 15% dance-off chance
        judgesBias: 0.80 // 80% judges bias
    },
    bhangra: {
        name: 'Punjabi Bhangra',
        houseEdge: 0.89, // 89% house edge
        rhythmComplexity: 0.55, // 55% rhythm complexity
        payoutMultiplier: 0.09, // Even more reduced payouts
        danceOffChance: 0.12, // 12% dance-off chance
        judgesBias: 0.85 // 85% judges bias
    },
    thumri: {
        name: 'Romantic Thumri',
        houseEdge: 0.92, // 92% house edge
        rhythmComplexity: 0.68, // 68% rhythm complexity
        payoutMultiplier: 0.06, // Extremely reduced payouts
        danceOffChance: 0.08, // 8% dance-off chance
        judgesBias: 0.90 // 90% judges bias
    },
    kathak: {
        name: 'Royal Kathak',
        houseEdge: 0.95, // 95% house edge
        rhythmComplexity: 0.82, // 82% rhythm complexity
        payoutMultiplier: 0.04, // Brutally reduced payouts
        danceOffChance: 0.05, // 5% dance-off chance
        judgesBias: 0.95 // 95% judges bias
    },
    fusion: {
        name: 'Modern Fusion',
        houseEdge: 0.98, // 98% house edge
        rhythmComplexity: 0.95, // 95% rhythm complexity
        payoutMultiplier: 0.02, // Impossibly reduced payouts
        danceOffChance: 0.02, // 2% dance-off chance
        judgesBias: 0.98 // 98% judges bias
    }
};

const RHYTHM_LEVELS = {
    slow: {
        name: 'Slow Taal',
        speed: 1.0,
        complexity: 0.25,
        missChance: 0.30,
        energyDrain: 0.10
    },
    medium: {
        name: 'Medium Taal',
        speed: 1.5,
        complexity: 0.45,
        missChance: 0.50,
        energyDrain: 0.20
    },
    fast: {
        name: 'Fast Taal',
        speed: 2.0,
        complexity: 0.65,
        missChance: 0.70,
        energyDrain: 0.35
    },
    frantic: {
        name: 'Frantic Taal',
        speed: 3.0,
        complexity: 0.85,
        missChance: 0.85,
        energyDrain: 0.50
    },
    impossible: {
        name: 'Impossible Taal',
        speed: 5.0,
        complexity: 0.98,
        missChance: 0.95,
        energyDrain: 0.75
    }
};

// Severely reduced payout table with Bollywood theme
const BOLLYWOOD_PAYOUTS = {
    // Star payouts (heavily reduced)
    SHAH_RUKH_KHAN: 25, // Reduced from 500:1
    MADHURI_DIXIT: 20, // Reduced from 400:1
    AMITABH_BACHCHAN: 18, // Reduced from 350:1
    AISHWARYA_RAI: 15, // Reduced from 300:1
    HRITHIK_ROSHAN: 12, // Reduced from 250:1
    PRIYANKA_CHOPRA: 10, // Reduced from 200:1

    // Musical instruments (reduced)
    TABLA: 8, // Reduced from 150:1
    SITAR: 6, // Reduced from 100:1
    GHUNGROO: 5, // Reduced from 75:1

    // Cultural symbols (reduced)
    DUPATTA: 4, // Reduced from 50:1
    MARIGOLD: 3, // Reduced from 30:1
    LOTUS: 2.5, // Reduced from 25:1
    PEACOCK: 2, // Reduced from 20:1
    ELEPHANT: 1.5, // Reduced from 15:1
    TIGER: 1.2, // Reduced from 12:1
    MANGO: 1, // Reduced from 10:1

    // Dance-off bonuses (fake - almost never apply)
    PERFECT_DANCE: 0.05, // 5% of displayed bonus
    JUDGES_FAVOR: 0.02, // 2% of displayed bonus
    AUDIENCE_LOVE: 0.01 // 1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadBollywoodDanceGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                    <h4 class="text-xl font-bold mb-4 text-pink-400">BOLLYWOOD CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DANCE STYLE</label>
                        <select id="danceStyle" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="classical">Classical Kathak</option>
                            <option value="bhangra">Punjabi Bhangra</option>
                            <option value="thumri">Romantic Thumri</option>
                            <option value="kathak">Royal Kathak</option>
                            <option value="fusion">Modern Fusion</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">RHYTHM LEVEL</label>
                        <select id="rhythmLevel" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="slow">Slow Taal</option>
                            <option value="medium" selected>Medium Taal</option>
                            <option value="fast">Fast Taal</option>
                            <option value="frantic">Frantic Taal</option>
                            <option value="impossible">Impossible Taal</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startDance" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START BOLLYWOOD DANCE
                    </button>

                    <div id="danceActions" class="space-y-2 hidden">
                        <button id="perfectMove" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            PERFECT MOVE
                        </button>
                        <button id="gracefulMove" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            GRACEFUL MOVE
                        </button>
                        <button id="passionateMove" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            PASSIONATE MOVE
                        </button>
                        <button id="skipMove" class="w-full py-2 rounded-lg font-bold bg-gray-600 hover:bg-gray-700 text-white">
                            SKIP MOVE
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Dance Energy</div>
                        <div id="danceEnergyDisplay" class="text-lg font-bold text-pink-400">100%</div>
                    </div>
                </div>

                <!-- Bollywood Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-pink-400">BOLLYWOOD STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="movieStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-pink-400 font-bold">MOVIE: MUGHAL-E-AZAM</div>
                        </div>
                        <div id="heroStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">HERO: SHAH RUKH KHAN</div>
                        </div>
                        <div id="heroineStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">HEROINE: MADHURI DIXIT</div>
                        </div>
                        <div id="danceOffStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">DANCE-OFF: READY</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-pink-400">BOLLYWOOD PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Bollywood Stars:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Shah Rukh Khan:</span>
                            <span class="text-red-400">25:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Madhuri Dixit:</span>
                            <span class="text-red-400">20:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Amitabh Bachchan:</span>
                            <span class="text-red-400">18:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Musical Elements:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tabla:</span>
                            <span class="text-red-400">8:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sitar:</span>
                            <span class="text-red-400">6:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Dance-off required for bonuses</div>
                        <div class="text-xs text-red-400">*Judges heavily biased</div>
                    </div>
                </div>
            </div>

            <!-- Main Bollywood Stage -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                    <!-- Bollywood Stage with Reels -->
                    <div id="bollywoodStage" class="relative bg-gradient-to-br from-black via-pink-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Bollywood Lights Background -->
                        <div id="bollywoodLights" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="spotlightGradient" cx="50%" cy="30%" r="40%">
                                        <stop offset="0%" style="stop-color:#ff69b4;stop-opacity:0.6" />
                                        <stop offset="50%" style="stop-color:#ff1493;stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:#8b008b;stop-opacity:0.1" />
                                    </radialGradient>
                                    <pattern id="bollywoodPattern" width="25" height="25" patternUnits="userSpaceOnUse">
                                        <circle cx="12.5" cy="12.5" r="2" fill="#ff69b4" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#bollywoodPattern)" />
                                <ellipse id="spotlight" cx="50%" cy="30%" rx="40%" ry="25%" fill="url(#spotlightGradient)" class="animate-pulse" />
                                <g id="stageLights">
                                    <!-- Stage lights will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Reel Display Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-pink-400 mb-2">BOLLYWOOD REELS</div>
                                <div id="reelsDisplay" class="grid grid-cols-5 gap-2">
                                    <!-- 5 reels, 3 rows each -->
                                    <div id="reel1" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel2" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel3" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel4" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                    <div id="reel5" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dance Performance Area -->
                        <div id="dancePerformance" class="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-pink-400 mb-2">DANCE PERFORMANCE</div>
                                <div id="dancerAvatar" class="w-16 h-16 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center text-2xl mb-2 mx-auto animate-bounce">
                                    💃
                                </div>
                                <div id="danceSequence" class="flex space-x-1 justify-center">
                                    <!-- Dance moves will appear here -->
                                </div>
                            </div>
                        </div>

                        <!-- Dance-off Arena -->
                        <div id="danceOffArena" class="absolute inset-0 bg-black/80 hidden">
                            <div class="flex items-center justify-center h-full">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-pink-400 mb-4">BOLLYWOOD DANCE-OFF!</div>
                                    <div class="flex space-x-8 items-center">
                                        <div class="text-center">
                                            <div class="text-lg text-blue-400">PLAYER</div>
                                            <div class="text-4xl mb-2">🕺</div>
                                            <div id="playerScore" class="text-xl font-bold text-white">0</div>
                                        </div>
                                        <div class="text-6xl text-yellow-400">VS</div>
                                        <div class="text-center">
                                            <div class="text-lg text-purple-400">MADHURI</div>
                                            <div class="text-4xl mb-2">💃</div>
                                            <div id="opponentScore" class="text-xl font-bold text-white">0</div>
                                        </div>
                                    </div>
                                    <div id="danceOffMoves" class="mt-4 text-center">
                                        <!-- Dance-off moves will appear here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Rhythm Indicator -->
                        <div id="rhythmIndicator" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-pink-400 mb-1">RHYTHM</div>
                                <div id="rhythmLevel" class="text-sm font-bold text-white">Medium Taal</div>
                                <div class="w-20 bg-gray-700 rounded-full h-2 mt-1">
                                    <div id="rhythmBar" class="bg-pink-400 h-2 rounded-full transition-all duration-300 animate-pulse" style="width: 50%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Ready for Bollywood magic...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="bollywoodEvent" class="text-sm font-bold text-pink-400 hidden animate-pulse">BOLLYWOOD MOMENT!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Bollywood Reel Dance - Where Every Spin is a Musical Number</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Dance-offs</div>
                <div id="danceOffsTriggered" class="text-xl font-bold text-pink-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Perfect Dances</div>
                <div id="perfectDances" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeBollywoodDance();
}

// Initialize the game
function initializeBollywoodDance() {
    document.getElementById('startDance').addEventListener('click', startBollywoodSpin);
    document.getElementById('perfectMove').addEventListener('click', () => performDanceMove('perfect'));
    document.getElementById('gracefulMove').addEventListener('click', () => performDanceMove('graceful'));
    document.getElementById('passionateMove').addEventListener('click', () => performDanceMove('passionate'));
    document.getElementById('skipMove').addEventListener('click', () => performDanceMove('skip'));

    // Initialize Bollywood systems
    initializeBollywoodSystems();
    generateStageLights();
    updateGameStats();
}

// Initialize Bollywood systems
function initializeBollywoodSystems() {
    // Reset dance system
    bollywoodDanceGame.dance.currentSequence = [];
    bollywoodDanceGame.dance.perfectMoves = 0;
    bollywoodDanceGame.dance.missedMoves = 0;
    bollywoodDanceGame.dance.rhythm = 1.0;
    bollywoodDanceGame.dance.energy = 100;
    bollywoodDanceGame.dance.grace = 50;
    bollywoodDanceGame.dance.passion = 50;
    bollywoodDanceGame.dance.synchronization = 0;
    bollywoodDanceGame.dance.choreographyComplexity = 1;

    // Reset Bollywood elements
    bollywoodDanceGame.bollywood.movie = getRandomBollywoodMovie();
    bollywoodDanceGame.bollywood.hero = getRandomBollywoodHero();
    bollywoodDanceGame.bollywood.heroine = getRandomBollywoodHeroine();
    bollywoodDanceGame.bollywood.director = getRandomBollywoodDirector();
    bollywoodDanceGame.bollywood.musicDirector = getRandomMusicDirector();
    bollywoodDanceGame.bollywood.playbackSinger = getRandomPlaybackSinger();
    bollywoodDanceGame.bollywood.currentSong = getRandomBollywoodSong();
    bollywoodDanceGame.bollywood.danceStyle = 'Classical';

    // Reset dance-off system
    bollywoodDanceGame.danceOff.active = false;
    bollywoodDanceGame.danceOff.opponent = 'Madhuri Dixit';
    bollywoodDanceGame.danceOff.playerScore = 0;
    bollywoodDanceGame.danceOff.opponentScore = 0;
    bollywoodDanceGame.danceOff.movesRequired = 5;
    bollywoodDanceGame.danceOff.movesCompleted = 0;
    bollywoodDanceGame.danceOff.difficulty = 'expert';
    bollywoodDanceGame.danceOff.judgesBias = 0.85;
    bollywoodDanceGame.danceOff.audienceSupport = 0.15;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        bollywoodDanceGame.reels[i] = [];
    }

    updateBollywoodDisplay();
}

// Generate stage lights
function generateStageLights() {
    const container = document.getElementById('stageLights');
    container.innerHTML = '';

    for (let i = 0; i < 8; i++) {
        const light = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        light.setAttribute('cx', `${12.5 + i * 12.5}%`);
        light.setAttribute('cy', '10%');
        light.setAttribute('r', '3%');
        light.setAttribute('fill', i % 2 === 0 ? '#ff69b4' : '#ff1493');
        light.setAttribute('opacity', '0.6');
        light.classList.add('animate-pulse');
        light.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(light);
    }
}

// Get random Bollywood elements
function getRandomBollywoodMovie() {
    const movies = ['Mughal-E-Azam', 'Sholay', 'Dilwale Dulhania Le Jayenge', 'Lagaan', 'Taare Zameen Par', 'Zindagi Na Milegi Dobara', 'Queen', 'Dangal'];
    return movies[Math.floor(Math.random() * movies.length)];
}

function getRandomBollywoodHero() {
    const heroes = ['Shah Rukh Khan', 'Amitabh Bachchan', 'Aamir Khan', 'Salman Khan', 'Hrithik Roshan', 'Akshay Kumar', 'Ranbir Kapoor'];
    return heroes[Math.floor(Math.random() * heroes.length)];
}

function getRandomBollywoodHeroine() {
    const heroines = ['Madhuri Dixit', 'Aishwarya Rai', 'Priyanka Chopra', 'Deepika Padukone', 'Kareena Kapoor', 'Katrina Kaif', 'Alia Bhatt'];
    return heroines[Math.floor(Math.random() * heroines.length)];
}

function getRandomBollywoodDirector() {
    const directors = ['Yash Chopra', 'Karan Johar', 'Sanjay Leela Bhansali', 'Rajkumar Hirani', 'Zoya Akhtar', 'Imtiaz Ali'];
    return directors[Math.floor(Math.random() * directors.length)];
}

function getRandomMusicDirector() {
    const musicDirectors = ['A.R. Rahman', 'Ilaiyaraaja', 'Shankar-Ehsaan-Loy', 'Vishal-Shekhar', 'Amit Trivedi', 'Sanjay Leela Bhansali'];
    return musicDirectors[Math.floor(Math.random() * musicDirectors.length)];
}

function getRandomPlaybackSinger() {
    const singers = ['Lata Mangeshkar', 'Kishore Kumar', 'Asha Bhosle', 'Arijit Singh', 'Shreya Ghoshal', 'Sonu Nigam'];
    return singers[Math.floor(Math.random() * singers.length)];
}

function getRandomBollywoodSong() {
    const songs = ['Kal Ho Naa Ho', 'Tujhe Dekha To', 'Chaiyya Chaiyya', 'Nagada Sang Dhol', 'Malhari', 'Ghar More Pardesiya'];
    return songs[Math.floor(Math.random() * songs.length)];
}

// Start Bollywood spin
function startBollywoodSpin() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    bollywoodDanceGame.isPlaying = true;
    bollywoodDanceGame.betAmount = betAmount;
    bollywoodDanceGame.totalBet = betAmount;
    bollywoodDanceGame.danceStyle = document.getElementById('danceStyle').value;
    bollywoodDanceGame.rhythmLevel = document.getElementById('rhythmLevel').value;

    // Activate Bollywood systems
    activateBollywoodSystems();

    // Start reel spin
    setTimeout(() => {
        spinBollywoodReels();
    }, 2000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startDance').disabled = true;
    document.getElementById('gameStatus').textContent = 'Music starting...';
}

// Activate Bollywood systems
function activateBollywoodSystems() {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];
    const rhythmData = RHYTHM_LEVELS[bollywoodDanceGame.rhythmLevel];

    bollywoodDanceGame.dance.rhythm = rhythmData.speed;
    bollywoodDanceGame.dance.choreographyComplexity = danceData.rhythmComplexity;

    // Set dance energy based on rhythm
    bollywoodDanceGame.dance.energy = Math.max(20, 100 - (rhythmData.energyDrain * 100));

    // Update Bollywood elements
    updateBollywoodElements();

    // Update visual effects
    updateBollywoodDisplay();
    updateRhythmEffects();
}

// Update Bollywood elements
function updateBollywoodElements() {
    document.getElementById('movieStatus').innerHTML =
        `<div class="text-pink-400 font-bold">MOVIE: ${bollywoodDanceGame.bollywood.movie.toUpperCase()}</div>`;
    document.getElementById('heroStatus').innerHTML =
        `<div class="text-blue-400 font-bold">HERO: ${bollywoodDanceGame.bollywood.hero.toUpperCase()}</div>`;
    document.getElementById('heroineStatus').innerHTML =
        `<div class="text-purple-400 font-bold">HEROINE: ${bollywoodDanceGame.bollywood.heroine.toUpperCase()}</div>`;
}

// Spin Bollywood reels with extreme bias
function spinBollywoodReels() {
    document.getElementById('gameStatus').textContent = 'Reels spinning to the beat...';

    // Generate symbols for each reel with extreme bias
    for (let reel = 0; reel < 5; reel++) {
        bollywoodDanceGame.reels[reel] = [];
        for (let row = 0; row < 3; row++) {
            const symbol = generateBiasedBollywoodSymbol();
            bollywoodDanceGame.reels[reel].push(symbol);
        }
    }

    // Animate reel spinning
    animateReelSpin();

    // Check for dance-off trigger after reels stop
    setTimeout(() => {
        checkForDanceOffTrigger();
    }, 4000);
}

// Generate biased Bollywood symbol
function generateBiasedBollywoodSymbol() {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];

    // Apply extreme bias toward low-value symbols
    if (Math.random() < danceData.rhythmComplexity) {
        // Bias toward common, low-value symbols
        const lowValueSymbols = bollywoodDanceGame.symbols.filter(s => s.value <= 30);
        return lowValueSymbols[Math.floor(Math.random() * lowValueSymbols.length)];
    } else {
        // Random selection (still biased by rarity)
        return selectSymbolByRarity();
    }
}

// Select symbol by rarity (biased toward common)
function selectSymbolByRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Reverse order to favor common symbols
    for (let i = bollywoodDanceGame.symbols.length - 1; i >= 0; i--) {
        const symbol = bollywoodDanceGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to most common symbol
    return bollywoodDanceGame.symbols[bollywoodDanceGame.symbols.length - 1];
}

// Animate reel spinning
function animateReelSpin() {
    for (let reel = 0; reel < 5; reel++) {
        const reelElement = document.getElementById(`reel${reel + 1}`);
        const symbols = reelElement.querySelectorAll('.reel-symbol');

        // Add spinning animation
        symbols.forEach((symbol, row) => {
            symbol.classList.add('animate-spin');

            setTimeout(() => {
                symbol.classList.remove('animate-spin');
                displaySymbolInReel(bollywoodDanceGame.reels[reel][row], symbol);
            }, 1000 + reel * 500 + row * 200);
        });
    }
}

// Display symbol in reel
function displaySymbolInReel(symbol, element) {
    const symbolIcon = getBollywoodSymbolIcon(symbol.name);

    element.innerHTML = `
        <div class="text-lg">${symbolIcon}</div>
        <div class="text-xs text-white font-bold">${symbol.name.split(' ')[0]}</div>
    `;

    // Add symbol-specific styling
    if (symbol.value >= 80) {
        element.classList.add('ring-2', 'ring-pink-400', 'animate-pulse');
    } else if (symbol.value >= 50) {
        element.classList.add('ring-1', 'ring-purple-400');
    }
}

// Get Bollywood symbol icon
function getBollywoodSymbolIcon(symbolName) {
    const icons = {
        'Shah Rukh Khan': '👑',
        'Madhuri Dixit': '💃',
        'Amitabh Bachchan': '🎭',
        'Aishwarya Rai': '👸',
        'Hrithik Roshan': '🕺',
        'Priyanka Chopra': '⭐',
        'Tabla': '🥁',
        'Sitar': '🎸',
        'Ghungroo': '🔔',
        'Dupatta': '🧣',
        'Marigold': '🌼',
        'Lotus': '🪷',
        'Peacock': '🦚',
        'Elephant': '🐘',
        'Tiger': '🐅',
        'Mango': '🥭'
    };

    return icons[symbolName] || '🎬';
}

// Check for dance-off trigger
function checkForDanceOffTrigger() {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];

    // Check for dance-off trigger symbols (very rare)
    let danceOffTriggers = 0;

    bollywoodDanceGame.reels.forEach(reel => {
        reel.forEach(symbol => {
            if (symbol.value >= 75 && symbol.dance === 'Classical') {
                danceOffTriggers++;
            }
        });
    });

    // Dance-off trigger requires 3+ high-value dance symbols (almost never happens)
    if (danceOffTriggers >= 3 && Math.random() < danceData.danceOffChance) {
        triggerDanceOff();
    } else {
        // Regular resolution
        resolveBollywoodSpin();
    }
}

// Trigger dance-off bonus
function triggerDanceOff() {
    bollywoodDanceGame.danceOff.active = true;
    bollywoodDanceGame.stats.danceOffsTriggered++;

    document.getElementById('danceOffStatus').innerHTML =
        '<div class="text-orange-400 font-bold animate-pulse">DANCE-OFF: ACTIVE!</div>';

    // Show dance-off arena
    document.getElementById('danceOffArena').classList.remove('hidden');

    // Show Bollywood event
    document.getElementById('bollywoodEvent').classList.remove('hidden');
    document.getElementById('bollywoodEvent').textContent = 'DANCE-OFF CHALLENGE!';
    setTimeout(() => {
        document.getElementById('bollywoodEvent').classList.add('hidden');
    }, 3000);

    // Start dance-off sequence
    setTimeout(() => {
        startDanceOffSequence();
    }, 2000);
}

// Start dance-off sequence
function startDanceOffSequence() {
    document.getElementById('gameStatus').textContent = 'Dance-off in progress...';
    document.getElementById('danceActions').classList.remove('hidden');

    // Generate dance sequence
    generateDanceSequence();

    // Enable dance moves
    enableDanceMoves();
}

// Generate dance sequence
function generateDanceSequence() {
    const rhythmData = RHYTHM_LEVELS[bollywoodDanceGame.rhythmLevel];
    const sequenceLength = bollywoodDanceGame.danceOff.movesRequired;

    bollywoodDanceGame.dance.currentSequence = [];

    for (let i = 0; i < sequenceLength; i++) {
        const moves = ['perfect', 'graceful', 'passionate', 'dramatic'];
        const move = moves[Math.floor(Math.random() * moves.length)];
        bollywoodDanceGame.dance.currentSequence.push(move);
    }

    displayDanceSequence();
}

// Display dance sequence
function displayDanceSequence() {
    const container = document.getElementById('danceSequence');
    container.innerHTML = '';

    bollywoodDanceGame.dance.currentSequence.forEach((move, index) => {
        const moveElement = document.createElement('div');
        moveElement.className = 'w-8 h-8 bg-pink-600 rounded-full flex items-center justify-center text-white text-xs font-bold border-2 border-pink-400';

        const moveIcons = {
            'perfect': '⭐',
            'graceful': '🌟',
            'passionate': '❤️',
            'dramatic': '🎭'
        };

        moveElement.textContent = moveIcons[move] || '💫';
        moveElement.dataset.move = move;
        moveElement.dataset.index = index;

        container.appendChild(moveElement);
    });
}

// Enable dance moves
function enableDanceMoves() {
    document.getElementById('gameMessage').textContent = 'Perform the dance sequence to compete with Bollywood legends!';
}

// Perform dance move
function performDanceMove(moveType) {
    const rhythmData = RHYTHM_LEVELS[bollywoodDanceGame.rhythmLevel];
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];
    const currentMoveIndex = bollywoodDanceGame.danceOff.movesCompleted;

    if (currentMoveIndex >= bollywoodDanceGame.dance.currentSequence.length) {
        return; // All moves completed
    }

    const requiredMove = bollywoodDanceGame.dance.currentSequence[currentMoveIndex];
    let moveSuccess = false;
    let scoreGain = 0;

    // Check if move matches required move (with extreme bias against player)
    if (moveType === requiredMove) {
        // Even correct moves have high chance to fail due to rhythm complexity
        if (Math.random() > rhythmData.missChance) {
            moveSuccess = true;
            scoreGain = 10;
            bollywoodDanceGame.dance.perfectMoves++;

            // Update dance attributes
            if (moveType === 'perfect') {
                bollywoodDanceGame.dance.grace += 5;
            } else if (moveType === 'graceful') {
                bollywoodDanceGame.dance.grace += 8;
            } else if (moveType === 'passionate') {
                bollywoodDanceGame.dance.passion += 8;
            }
        } else {
            // Move failed due to rhythm complexity
            bollywoodDanceGame.dance.missedMoves++;
            scoreGain = 1; // Minimal score for attempt
        }
    } else {
        // Wrong move
        bollywoodDanceGame.dance.missedMoves++;
        scoreGain = 0;
    }

    // Apply judges bias (heavily against player)
    if (moveSuccess && Math.random() < danceData.judgesBias) {
        // Judges are biased - reduce score
        scoreGain = Math.floor(scoreGain * 0.2); // 80% reduction
    }

    // Drain energy
    bollywoodDanceGame.dance.energy = Math.max(0, bollywoodDanceGame.dance.energy - rhythmData.energyDrain * 20);

    // Update player score
    bollywoodDanceGame.danceOff.playerScore += scoreGain;
    bollywoodDanceGame.danceOff.movesCompleted++;
    bollywoodDanceGame.stats.movesPerformed++;

    // Generate opponent move (always perfect with bias)
    generateOpponentMove();

    // Update display
    updateDanceOffDisplay();

    // Check if dance-off is complete
    if (bollywoodDanceGame.danceOff.movesCompleted >= bollywoodDanceGame.danceOff.movesRequired) {
        setTimeout(() => {
            resolveDanceOff();
        }, 1500);
    }
}

// Generate opponent move (heavily biased)
function generateOpponentMove() {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];

    // Opponent (Madhuri) always performs perfectly with bias
    let opponentScore = 15; // Base perfect score

    // Opponent gets bonus from judges bias
    if (Math.random() < danceData.judgesBias) {
        opponentScore += 5; // Judges favor bonus
    }

    // Audience always loves the opponent
    if (Math.random() < 0.9) { // 90% chance
        opponentScore += 3; // Audience bonus
    }

    bollywoodDanceGame.danceOff.opponentScore += opponentScore;
}

// Update dance-off display
function updateDanceOffDisplay() {
    document.getElementById('playerScore').textContent = bollywoodDanceGame.danceOff.playerScore;
    document.getElementById('opponentScore').textContent = bollywoodDanceGame.danceOff.opponentScore;
    document.getElementById('danceEnergyDisplay').textContent = `${Math.floor(bollywoodDanceGame.dance.energy)}%`;

    // Highlight completed moves
    const moveElements = document.querySelectorAll('#danceSequence > div');
    if (moveElements[bollywoodDanceGame.danceOff.movesCompleted - 1]) {
        moveElements[bollywoodDanceGame.danceOff.movesCompleted - 1].classList.add('bg-green-600');
    }
}

// Resolve dance-off with extreme bias
function resolveDanceOff() {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];

    let playerWins = bollywoodDanceGame.danceOff.playerScore > bollywoodDanceGame.danceOff.opponentScore;

    // Apply additional bias - even if player scores higher, they can still lose
    if (playerWins && Math.random() < danceData.judgesBias) {
        playerWins = false; // Judges override player victory
    }

    // Hide dance-off arena
    document.getElementById('danceOffArena').classList.add('hidden');
    document.getElementById('danceActions').classList.add('hidden');

    if (playerWins) {
        bollywoodDanceGame.stats.danceOffsWon++;
        if (bollywoodDanceGame.dance.missedMoves === 0) {
            bollywoodDanceGame.stats.perfectDances++;
        }
        document.getElementById('gameStatus').textContent = 'Dance-off victory! Judges impressed!';
        resolveBollywoodSpin(true); // Bonus multiplier
    } else {
        document.getElementById('gameStatus').textContent = 'Dance-off lost - Madhuri remains the queen!';
        resolveBollywoodSpin(false); // No bonus
    }
}

// Resolve Bollywood spin with extreme bias
function resolveBollywoodSpin(danceOffBonus = false) {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate symbol winnings with extreme reductions
    const allSymbols = [];
    bollywoodDanceGame.reels.forEach(reel => {
        allSymbols.push(...reel);
    });

    // Count symbol occurrences
    const symbolCounts = {};
    allSymbols.forEach(symbol => {
        symbolCounts[symbol.name] = (symbolCounts[symbol.name] || 0) + 1;
    });

    // Calculate payouts (severely reduced)
    Object.entries(symbolCounts).forEach(([symbolName, count]) => {
        if (count >= 3) { // Need at least 3 symbols
            const symbol = bollywoodDanceGame.symbols.find(s => s.name === symbolName);
            if (symbol) {
                const payoutKey = symbolName.toUpperCase().replace(/[^A-Z]/g, '_');
                if (BOLLYWOOD_PAYOUTS[payoutKey]) {
                    let symbolPayout = bollywoodDanceGame.betAmount * (BOLLYWOOD_PAYOUTS[payoutKey] / 100);

                    // Apply count multiplier (minimal)
                    if (count === 4) symbolPayout *= 1.2;
                    if (count === 5) symbolPayout *= 1.5;

                    totalWinnings += symbolPayout;
                }
            }
        }
    });

    // Apply dance-off bonus (fake - almost never applies)
    if (danceOffBonus && Math.random() < 0.02) {
        const danceBonus = Math.floor(totalWinnings * BOLLYWOOD_PAYOUTS.PERFECT_DANCE);
        totalWinnings += danceBonus;
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * danceData.payoutMultiplier);

    // Apply rhythm penalty
    const rhythmData = RHYTHM_LEVELS[bollywoodDanceGame.rhythmLevel];
    const rhythmPenalty = rhythmData.complexity * 0.5;
    totalWinnings = Math.floor(totalWinnings * (1 - rhythmPenalty));

    // Apply energy penalty
    if (bollywoodDanceGame.dance.energy < 50) {
        const energyPenalty = (50 - bollywoodDanceGame.dance.energy) / 100;
        totalWinnings = Math.floor(totalWinnings * (1 - energyPenalty));
    }

    // Judges bias can void wins
    if (totalWinnings > 0 && Math.random() < danceData.judgesBias * 0.3) {
        totalWinnings = 0;
        resultMessage = 'Judges disqualified performance!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    if (!resultMessage) {
        if (totalWinnings > bollywoodDanceGame.betAmount) {
            resultMessage = 'Bollywood magic pays off!';
        } else if (totalWinnings > 0) {
            resultMessage = 'Small Bollywood moment';
        } else {
            resultMessage = 'The show must go on...';
        }
    }

    // Add winnings to balance
    balance += totalWinnings;
    bollywoodDanceGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(totalWinnings > bollywoodDanceGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Dance Energy: ${Math.floor(bollywoodDanceGame.dance.energy)}%`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update Bollywood display
function updateBollywoodDisplay() {
    const danceData = DANCE_STYLES[bollywoodDanceGame.danceStyle];
    const rhythmData = RHYTHM_LEVELS[bollywoodDanceGame.rhythmLevel];

    document.getElementById('rhythmLevel').textContent = rhythmData.name;
    document.getElementById('rhythmBar').style.width = `${rhythmData.speed * 20}%`;

    if (bollywoodDanceGame.danceOff.active) {
        document.getElementById('danceOffStatus').innerHTML =
            '<div class="text-orange-400 font-bold animate-pulse">DANCE-OFF: ACTIVE!</div>';
    } else {
        document.getElementById('danceOffStatus').innerHTML =
            '<div class="text-orange-400 font-bold">DANCE-OFF: READY</div>';
    }
}

// Update rhythm effects
function updateRhythmEffects() {
    // Update rhythm bar animation based on current rhythm
    const rhythmBar = document.getElementById('rhythmBar');
    if (rhythmBar) {
        const rhythmData = RHYTHM_LEVELS[bollywoodDanceGame.rhythmLevel];
        rhythmBar.style.animationDuration = `${2 / rhythmData.speed}s`;
    }
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${bollywoodDanceGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = bollywoodDanceGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${bollywoodDanceGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${bollywoodDanceGame.stats.totalWagered}`;
    document.getElementById('danceOffsTriggered').textContent = bollywoodDanceGame.stats.danceOffsTriggered;
    document.getElementById('perfectDances').textContent = bollywoodDanceGame.stats.perfectDances;

    const netResult = bollywoodDanceGame.stats.totalWon - bollywoodDanceGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    bollywoodDanceGame.stats.spinsPlayed++;
    bollywoodDanceGame.stats.totalWagered += bollywoodDanceGame.betAmount;
    bollywoodDanceGame.stats.totalWon += winnings;

    if (won) {
        bollywoodDanceGame.stats.spinsWon++;
        bollywoodDanceGame.stats.currentStreak++;
        bollywoodDanceGame.streakData.currentWinStreak++;
        bollywoodDanceGame.streakData.currentLossStreak = 0;

        if (bollywoodDanceGame.streakData.currentWinStreak > bollywoodDanceGame.streakData.longestWinStreak) {
            bollywoodDanceGame.streakData.longestWinStreak = bollywoodDanceGame.streakData.currentWinStreak;
        }

        if (winnings > bollywoodDanceGame.stats.biggestWin) {
            bollywoodDanceGame.stats.biggestWin = winnings;
        }
    } else {
        bollywoodDanceGame.stats.currentStreak = 0;
        bollywoodDanceGame.streakData.currentWinStreak = 0;
        bollywoodDanceGame.streakData.currentLossStreak++;

        if (bollywoodDanceGame.streakData.currentLossStreak > bollywoodDanceGame.streakData.longestLossStreak) {
            bollywoodDanceGame.streakData.longestLossStreak = bollywoodDanceGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to Bollywood effects)
    bollywoodDanceGame.stats.winRate = (bollywoodDanceGame.stats.spinsWon / bollywoodDanceGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next performance
function resetGame() {
    bollywoodDanceGame.isPlaying = false;
    bollywoodDanceGame.betAmount = 0;
    bollywoodDanceGame.totalBet = 0;
    bollywoodDanceGame.gameResult = '';
    bollywoodDanceGame.totalWin = 0;

    // Reset dance system
    bollywoodDanceGame.dance.currentSequence = [];
    bollywoodDanceGame.dance.perfectMoves = 0;
    bollywoodDanceGame.dance.missedMoves = 0;
    bollywoodDanceGame.dance.rhythm = 1.0;
    bollywoodDanceGame.dance.energy = 100;
    bollywoodDanceGame.dance.grace = 50;
    bollywoodDanceGame.dance.passion = 50;
    bollywoodDanceGame.dance.synchronization = 0;
    bollywoodDanceGame.dance.choreographyComplexity = 1;

    // Reset dance-off system
    bollywoodDanceGame.danceOff.active = false;
    bollywoodDanceGame.danceOff.playerScore = 0;
    bollywoodDanceGame.danceOff.opponentScore = 0;
    bollywoodDanceGame.danceOff.movesCompleted = 0;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        bollywoodDanceGame.reels[i] = [];
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('bollywoodEvent').classList.add('hidden');
    document.getElementById('danceOffArena').classList.add('hidden');
    document.getElementById('danceActions').classList.add('hidden');
    document.getElementById('danceSequence').innerHTML = '';

    // Reset reel displays
    for (let reel = 1; reel <= 5; reel++) {
        const reelElement = document.getElementById(`reel${reel}`);
        const symbols = reelElement.querySelectorAll('.reel-symbol');
        symbols.forEach(symbol => {
            symbol.innerHTML = '';
            symbol.className = 'reel-symbol w-12 h-12 bg-black/50 border border-pink-400 rounded flex items-center justify-center text-xs';
        });
    }

    // Reset Bollywood status
    document.getElementById('movieStatus').innerHTML =
        '<div class="text-pink-400 font-bold">MOVIE: MUGHAL-E-AZAM</div>';
    document.getElementById('heroStatus').innerHTML =
        '<div class="text-blue-400 font-bold">HERO: SHAH RUKH KHAN</div>';
    document.getElementById('heroineStatus').innerHTML =
        '<div class="text-purple-400 font-bold">HEROINE: MADHURI DIXIT</div>';
    document.getElementById('danceOffStatus').innerHTML =
        '<div class="text-orange-400 font-bold">DANCE-OFF: READY</div>';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startDance').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Ready for Bollywood magic...';
    document.getElementById('gameMessage').textContent = 'Welcome to Bollywood Reel Dance - Where Every Spin is a Musical Number';
    document.getElementById('danceEnergyDisplay').textContent = '100%';

    // Reset rhythm display
    document.getElementById('rhythmLevel').textContent = 'Medium Taal';
    document.getElementById('rhythmBar').style.width = '50%';

    // Reinitialize systems for next game
    initializeBollywoodSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBollywoodDanceGame();
});