// AI Oracle Dice - Machine-Learning Prediction for Multiplier Boosts
// Experimental Concept Implementation with AI Prediction Theme
// Designed to maintain 3-5% player win rate with ML prediction elements

// Game state
let balance = 1000;

// Game state object with AI Oracle system
let aiOracleDiceGame = {
    isPlaying: false,
    aiModel: 'neural_network', // neural_network, random_forest, svm, deep_learning, ensemble
    predictionMode: 'pattern', // pattern, probability, trend, chaos, hybrid
    betAmount: 0,
    totalBet: 0,

    // AI Oracle prediction system
    aiOracle: {
        modelAccuracy: 0.70, // 70% accuracy (improved)
        predictionConfidence: 0.65, // 65% confidence
        learningRate: 0.05, // 5% learning rate
        trainingData: [],
        predictions: [],
        modelVersion: '1.0',
        lastUpdate: Date.now(),
        predictionStreak: 0,
        correctPredictions: 0,
        totalPredictions: 0,
        neuralWeights: [],
        featureImportance: {
            historical: 0.30,
            pattern: 0.25,
            probability: 0.20,
            trend: 0.15,
            chaos: 0.10
        }
    },

    // Dice system with AI prediction
    dice: {
        count: 5, // 5 dice
        values: [1, 1, 1, 1, 1],
        targetSum: 15,
        predictedSum: 0,
        actualSum: 0,
        multiplierBoost: 1.0,
        aiPrediction: null,
        confidenceLevel: 0.65,
        patternDetected: false,
        anomalyScore: 0.0,
        entropy: 0.0
    },

    // Machine learning models
    mlModels: {
        neuralNetwork: {
            name: 'Neural Network',
            accuracy: 0.70,
            complexity: 0.85,
            trainingTime: 120, // seconds
            predictionSpeed: 0.1, // seconds
            multiplierBonus: 0.30 // 30% multiplier bonus
        },
        randomForest: {
            name: 'Random Forest',
            accuracy: 0.65,
            complexity: 0.60,
            trainingTime: 60,
            predictionSpeed: 0.05,
            multiplierBonus: 0.25 // 25% multiplier bonus
        },
        svm: {
            name: 'Support Vector Machine',
            accuracy: 0.68,
            complexity: 0.70,
            trainingTime: 90,
            predictionSpeed: 0.08,
            multiplierBonus: 0.28 // 28% multiplier bonus
        },
        deepLearning: {
            name: 'Deep Learning',
            accuracy: 0.75,
            complexity: 0.95,
            trainingTime: 180,
            predictionSpeed: 0.15,
            multiplierBonus: 0.35 // 35% multiplier bonus
        },
        ensemble: {
            name: 'Ensemble Model',
            accuracy: 0.78,
            complexity: 0.90,
            trainingTime: 150,
            predictionSpeed: 0.12,
            multiplierBonus: 0.40 // 40% multiplier bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        predictionsCorrect: 0,
        predictionsTotal: 0,
        predictionAccuracy: 0.70,
        modelUpgrades: 0,
        aiPoints: 0,
        bestMultiplier: 1.0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// AI models with balanced prediction requirements (3-5% win rate)
const AI_MODELS = {
    neural_network: {
        name: 'Neural Network',
        predictionWeight: 0.35, // 35% prediction influence (increased)
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.95, // Better payouts
        accuracyBonus: 0.30, // 30% accuracy bonus
        learningBonus: 0.25 // 25% learning bonus
    },
    random_forest: {
        name: 'Random Forest',
        predictionWeight: 0.30, // 30% prediction influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.90, // Good payouts
        accuracyBonus: 0.25, // 25% accuracy bonus
        learningBonus: 0.20 // 20% learning bonus
    },
    svm: {
        name: 'Support Vector Machine',
        predictionWeight: 0.32, // 32% prediction influence
        luckFactor: 0.68, // 68% luck factor
        payoutMultiplier: 0.92, // Good payouts
        accuracyBonus: 0.28, // 28% accuracy bonus
        learningBonus: 0.22 // 22% learning bonus
    },
    deep_learning: {
        name: 'Deep Learning',
        predictionWeight: 0.40, // 40% prediction influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.00, // Full payouts
        accuracyBonus: 0.35, // 35% accuracy bonus
        learningBonus: 0.30 // 30% learning bonus
    },
    ensemble: {
        name: 'Ensemble Model',
        predictionWeight: 0.45, // 45% prediction influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        accuracyBonus: 0.40, // 40% accuracy bonus
        learningBonus: 0.35 // 35% learning bonus
    }
};

const PREDICTION_MODES = {
    pattern: {
        name: 'Pattern Recognition',
        complexityBonus: 0.25, // 25% complexity bonus
        confidenceMultiplier: 1.20 // 20% confidence boost
    },
    probability: {
        name: 'Probability Analysis',
        complexityBonus: 0.20, // 20% complexity bonus
        confidenceMultiplier: 1.15 // 15% confidence boost
    },
    trend: {
        name: 'Trend Analysis',
        complexityBonus: 0.30, // 30% complexity bonus
        confidenceMultiplier: 1.25 // 25% confidence boost
    },
    chaos: {
        name: 'Chaos Theory',
        complexityBonus: 0.35, // 35% complexity bonus
        confidenceMultiplier: 1.30 // 30% confidence boost
    },
    hybrid: {
        name: 'Hybrid Approach',
        complexityBonus: 0.40, // 40% complexity bonus
        confidenceMultiplier: 1.35 // 35% confidence boost
    }
};

// Improved payout table with AI prediction theme (3-5% win rate)
const AI_ORACLE_PAYOUTS = {
    // Perfect AI predictions (moderately reduced)
    ORACLE_PERFECT: 1200, // Reduced from 2400:1 but still excellent
    AI_GENIUS: 800, // Reduced from 1600:1
    PREDICTION_MASTER: 500, // Reduced from 1000:1
    NEURAL_HARMONY: 300, // Reduced from 600:1

    // Dice combinations with AI bonuses
    FIVE_SIXES: 1000, // All sixes
    STRAIGHT: 500, // 1,2,3,4,5 or 2,3,4,5,6
    FOUR_KIND: 300, // Four of a kind
    FULL_HOUSE: 200, // Three of a kind + pair

    // AI prediction bonuses (actually apply more often)
    PREDICTION_BONUS: 0.65, // 65% of displayed bonus (increased)
    ACCURACY_BONUS: 0.55, // 55% of displayed bonus (increased)
    LEARNING_BONUS: 0.45, // 45% of displayed bonus (increased)
    CONFIDENCE_BONUS: 0.35 // 35% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadAIOracleDiceGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">AI ORACLE CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">AI MODEL</label>
                        <select id="aiModel" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="neural_network">Neural Network</option>
                            <option value="random_forest">Random Forest</option>
                            <option value="svm">Support Vector Machine</option>
                            <option value="deep_learning">Deep Learning</option>
                            <option value="ensemble">Ensemble Model</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">PREDICTION MODE</label>
                        <select id="predictionMode" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="pattern">Pattern Recognition</option>
                            <option value="probability">Probability Analysis</option>
                            <option value="trend">Trend Analysis</option>
                            <option value="chaos">Chaos Theory</option>
                            <option value="hybrid">Hybrid Approach</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="trainModel" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        TRAIN AI MODEL
                    </button>

                    <div id="oracleActions" class="space-y-2 hidden">
                        <button id="makePrediction" class="w-full py-2 rounded-lg font-bold bg-cyan-600 hover:bg-cyan-700 text-white">
                            MAKE PREDICTION
                        </button>
                        <button id="rollDice" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            ROLL ORACLE DICE
                        </button>
                        <button id="upgradeModel" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            UPGRADE MODEL
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">AI Points</div>
                        <div id="aiPointsDisplay" class="text-lg font-bold text-cyan-400">0</div>
                    </div>
                </div>

                <!-- AI Model Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">AI MODEL STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="modelAccuracy" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">ACCURACY: 70%</div>
                        </div>
                        <div id="predictionConfidence" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">CONFIDENCE: 65%</div>
                        </div>
                        <div id="learningRate" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">LEARNING: 5%</div>
                        </div>
                        <div id="predictionStreak" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">STREAK: 0</div>
                        </div>
                    </div>
                </div>

                <!-- Prediction Analysis -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">PREDICTION ANALYSIS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">AI Predictions:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Correct:</span>
                            <span class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total:</span>
                            <span class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Success Rate:</span>
                            <span class="text-purple-400">70%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">ML Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Prediction:</span>
                            <span class="text-cyan-400">65%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Accuracy:</span>
                            <span class="text-cyan-400">55%</span>
                        </div>
                        <div class="text-xs text-cyan-400 mt-2">*AI predictions improve odds</div>
                        <div class="text-xs text-cyan-400">*Model accuracy affects payouts</div>
                    </div>
                </div>
            </div>

            <!-- Main AI Oracle Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <!-- AI Oracle Arena -->
                    <div id="aiOracleArena" class="relative bg-gradient-to-br from-black via-cyan-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- AI Neural Background -->
                        <div id="aiNeuralBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="aiNeuralGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#0891b2;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#0e7490;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="neuralPattern" width="100" height="100" patternUnits="userSpaceOnUse">
                                        <circle cx="50" cy="50" r="25" fill="none" stroke="#06b6d4" stroke-width="2" opacity="0.3"/>
                                        <circle cx="25" cy="25" r="8" fill="#0891b2" opacity="0.4"/>
                                        <circle cx="75" cy="25" r="8" fill="#0891b2" opacity="0.4"/>
                                        <circle cx="25" cy="75" r="8" fill="#0891b2" opacity="0.4"/>
                                        <circle cx="75" cy="75" r="8" fill="#0891b2" opacity="0.4"/>
                                        <line x1="25" y1="25" x2="75" y2="75" stroke="#06b6d4" stroke-width="1" opacity="0.3"/>
                                        <line x1="75" y1="25" x2="25" y2="75" stroke="#06b6d4" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#neuralPattern)" />
                                <circle id="aiNeuralCore" cx="50%" cy="50%" r="25%" fill="url(#aiNeuralGradient)" class="animate-pulse" />
                                <g id="aiNeuralEffects">
                                    <!-- AI neural effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- AI Oracle Dice Display -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">AI ORACLE DICE</div>
                                <div id="oracleDiceDisplay" class="flex space-x-3">
                                    <!-- 5 AI-enhanced dice -->
                                    <div class="oracle-die w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-800 rounded-lg border-2 border-cyan-400 flex items-center justify-center text-2xl font-bold text-white transition-all duration-500" data-die="0">1</div>
                                    <div class="oracle-die w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-800 rounded-lg border-2 border-cyan-400 flex items-center justify-center text-2xl font-bold text-white transition-all duration-500" data-die="1">1</div>
                                    <div class="oracle-die w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-800 rounded-lg border-2 border-cyan-400 flex items-center justify-center text-2xl font-bold text-white transition-all duration-500" data-die="2">1</div>
                                    <div class="oracle-die w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-800 rounded-lg border-2 border-cyan-400 flex items-center justify-center text-2xl font-bold text-white transition-all duration-500" data-die="3">1</div>
                                    <div class="oracle-die w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-800 rounded-lg border-2 border-cyan-400 flex items-center justify-center text-2xl font-bold text-white transition-all duration-500" data-die="4">1</div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">AI-enhanced probability dice</div>
                            </div>
                        </div>

                        <!-- AI Prediction Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">AI PREDICTION</div>
                                <div id="aiPredictionDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="predictionText" class="text-lg text-white mb-3">AI model ready for training...</div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">PREDICTED SUM</div>
                                            <div id="predictedSum" class="text-2xl font-bold text-white">0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">CONFIDENCE</div>
                                            <div id="confidenceLevel" class="text-2xl font-bold text-yellow-400">65%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Machine learning predictions</div>
                            </div>
                        </div>

                        <!-- Model Training Progress -->
                        <div id="trainingProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">MODEL TRAINING</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="trainingBar" class="bg-gradient-to-r from-cyan-400 to-blue-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Initializing</span>
                                    <span id="trainingStatus">Ready</span>
                                    <span>Complete</span>
                                </div>
                            </div>
                        </div>

                        <!-- Multiplier Boost Display -->
                        <div id="multiplierBoost" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-cyan-400 mb-1">MULTIPLIER</div>
                                <div class="text-2xl font-bold text-white text-center">1.0x</div>
                                <div class="text-xs text-gray-400 mt-1">Boost</div>
                            </div>
                        </div>

                        <!-- Neural Activity Monitor -->
                        <div id="neuralActivity" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">NEURAL</div>
                                <div class="text-2xl font-bold text-white text-center">70%</div>
                                <div class="text-xs text-gray-400 mt-1">Activity</div>
                            </div>
                        </div>

                        <!-- Training Timer -->
                        <div id="trainingTimer" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">TRAINING</div>
                                <div class="text-2xl font-bold text-white text-center">120s</div>
                                <div class="text-xs text-gray-400 mt-1">Time</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">AI Oracle ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="oracleEvent" class="text-sm font-bold text-cyan-400 hidden animate-pulse">PREDICTION CORRECT!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to AI Oracle Dice - Machine Learning Predictions for Multiplier Boosts</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-cyan-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-cyan-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Prediction Accuracy</div>
                <div id="predictionAccuracyStat" class="text-xl font-bold text-green-400">70%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Best Multiplier</div>
                <div id="bestMultiplier" class="text-xl font-bold text-purple-400">1.0x</div>
            </div>
        </div>
    `;

    initializeAIOracleDice();
}

// Initialize the game
function initializeAIOracleDice() {
    document.getElementById('trainModel').addEventListener('click', trainAIModel);
    document.getElementById('makePrediction').addEventListener('click', makeAIPrediction);
    document.getElementById('rollDice').addEventListener('click', rollOracleDice);
    document.getElementById('upgradeModel').addEventListener('click', upgradeAIModel);

    // Initialize AI Oracle systems
    initializeAIOracleSystems();
    generateAINeuralEffects();
    updateGameStats();
}

// Initialize AI Oracle systems
function initializeAIOracleSystems() {
    // Reset AI Oracle system
    aiOracleDiceGame.aiOracle.modelAccuracy = Math.min(0.85, 0.70 + (aiOracleDiceGame.stats.aiPoints * 0.01));
    aiOracleDiceGame.aiOracle.predictionConfidence = Math.min(0.80, 0.65 + (aiOracleDiceGame.stats.aiPoints * 0.008));
    aiOracleDiceGame.aiOracle.learningRate = Math.min(0.10, 0.05 + (aiOracleDiceGame.stats.aiPoints * 0.002));
    aiOracleDiceGame.aiOracle.trainingData = [];
    aiOracleDiceGame.aiOracle.predictions = [];
    aiOracleDiceGame.aiOracle.modelVersion = '1.0';
    aiOracleDiceGame.aiOracle.lastUpdate = Date.now();
    aiOracleDiceGame.aiOracle.predictionStreak = 0;
    aiOracleDiceGame.aiOracle.correctPredictions = aiOracleDiceGame.stats.predictionsCorrect;
    aiOracleDiceGame.aiOracle.totalPredictions = aiOracleDiceGame.stats.predictionsTotal;

    // Initialize neural weights
    aiOracleDiceGame.aiOracle.neuralWeights = generateRandomWeights();

    // Reset dice system
    aiOracleDiceGame.dice.count = 5;
    aiOracleDiceGame.dice.values = [1, 1, 1, 1, 1];
    aiOracleDiceGame.dice.targetSum = 15;
    aiOracleDiceGame.dice.predictedSum = 0;
    aiOracleDiceGame.dice.actualSum = 0;
    aiOracleDiceGame.dice.multiplierBoost = 1.0;
    aiOracleDiceGame.dice.aiPrediction = null;
    aiOracleDiceGame.dice.confidenceLevel = aiOracleDiceGame.aiOracle.predictionConfidence;
    aiOracleDiceGame.dice.patternDetected = false;
    aiOracleDiceGame.dice.anomalyScore = 0.0;
    aiOracleDiceGame.dice.entropy = calculateEntropy([1, 1, 1, 1, 1]);

    updateAIOracleDisplay();
}

// Generate AI neural effects
function generateAINeuralEffects() {
    const container = document.getElementById('aiNeuralEffects');
    container.innerHTML = '';

    // Create neural network visualization
    for (let i = 0; i < 12; i++) {
        const neuron = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        neuron.setAttribute('cx', `${Math.random() * 100}%`);
        neuron.setAttribute('cy', `${Math.random() * 100}%`);
        neuron.setAttribute('r', `${Math.random() * 2 + 1}%`);
        neuron.setAttribute('fill', '#06b6d4');
        neuron.setAttribute('opacity', '0.6');
        neuron.classList.add('animate-pulse');
        neuron.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(neuron);

        // Add connections between neurons
        if (i > 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNeuron = container.children[i - 1];
            connection.setAttribute('x1', prevNeuron.getAttribute('cx'));
            connection.setAttribute('y1', prevNeuron.getAttribute('cy'));
            connection.setAttribute('x2', neuron.getAttribute('cx'));
            connection.setAttribute('y2', neuron.getAttribute('cy'));
            connection.setAttribute('stroke', '#0891b2');
            connection.setAttribute('stroke-width', '1');
            connection.setAttribute('opacity', '0.3');
            container.appendChild(connection);
        }
    }
}

// Generate random neural weights
function generateRandomWeights() {
    const weights = [];
    for (let i = 0; i < 25; i++) { // 5x5 weight matrix
        weights.push(Math.random() * 2 - 1); // Random weights between -1 and 1
    }
    return weights;
}

// Calculate entropy of dice values
function calculateEntropy(values) {
    const counts = {};
    values.forEach(value => {
        counts[value] = (counts[value] || 0) + 1;
    });

    let entropy = 0;
    const total = values.length;
    Object.values(counts).forEach(count => {
        const probability = count / total;
        entropy -= probability * Math.log2(probability);
    });

    return entropy;
}

// Train AI model
function trainAIModel() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    aiOracleDiceGame.isPlaying = true;
    aiOracleDiceGame.betAmount = betAmount;
    aiOracleDiceGame.totalBet = betAmount;
    aiOracleDiceGame.aiModel = document.getElementById('aiModel').value;
    aiOracleDiceGame.predictionMode = document.getElementById('predictionMode').value;

    // Start AI model training
    startAIModelTraining();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('trainModel').disabled = true;
    document.getElementById('gameStatus').textContent = 'Training AI model...';
}

// Start AI model training
function startAIModelTraining() {
    const modelData = aiOracleDiceGame.mlModels[aiOracleDiceGame.aiModel];
    const modeData = PREDICTION_MODES[aiOracleDiceGame.predictionMode];

    // Update training information
    document.getElementById('predictionText').textContent = `Training ${modelData.name} with ${modeData.name}...`;
    document.getElementById('trainingStatus').textContent = 'Training...';

    // Simulate AI training process
    simulateAITraining(modelData);

    // Update AI status
    updateAIStatus();

    // Update visual effects
    updateAIOracleDisplay();
    updateAINeuralEffects();
}

// Simulate AI training process
function simulateAITraining(modelData) {
    let progress = 0;
    const trainingTime = modelData.trainingTime;

    const trainingInterval = setInterval(() => {
        progress += 100 / (trainingTime * 10); // Update every 100ms

        // Update progress bar
        document.getElementById('trainingBar').style.width = `${Math.min(100, progress)}%`;

        // Update timer
        const timeRemaining = Math.max(0, trainingTime - Math.floor(progress / (100 / trainingTime)));
        document.getElementById('trainingTimer').querySelector('.text-2xl').textContent = `${timeRemaining}s`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('trainingStatus').textContent = 'Initializing...';
        } else if (progress < 40) {
            document.getElementById('trainingStatus').textContent = 'Loading data...';
        } else if (progress < 60) {
            document.getElementById('trainingStatus').textContent = 'Training model...';
        } else if (progress < 80) {
            document.getElementById('trainingStatus').textContent = 'Optimizing...';
        } else if (progress < 100) {
            document.getElementById('trainingStatus').textContent = 'Validating...';
        } else {
            document.getElementById('trainingStatus').textContent = 'Complete!';
            clearInterval(trainingInterval);
            completeAITraining(modelData);
        }

        // Update neural activity
        const neuralActivity = Math.min(100, 50 + progress / 2);
        document.getElementById('neuralActivity').querySelector('.text-2xl').textContent = `${Math.floor(neuralActivity)}%`;

    }, 100);
}

// Complete AI training
function completeAITraining(modelData) {
    const aiModelData = AI_MODELS[aiOracleDiceGame.aiModel];
    const modeData = PREDICTION_MODES[aiOracleDiceGame.predictionMode];

    // Apply training improvements
    const trainingBonus = modelData.accuracy * aiModelData.predictionWeight;
    const modeBonus = modeData.complexityBonus;
    const skillBonus = aiOracleDiceGame.stats.aiPoints * 0.005;

    // Update AI metrics
    aiOracleDiceGame.aiOracle.modelAccuracy = Math.min(0.90,
        aiOracleDiceGame.aiOracle.modelAccuracy + trainingBonus + modeBonus + skillBonus);
    aiOracleDiceGame.aiOracle.predictionConfidence = Math.min(0.85,
        aiOracleDiceGame.aiOracle.predictionConfidence + (trainingBonus * 0.5));

    // Update neural weights based on training
    updateNeuralWeights();

    // Award AI points
    const pointsEarned = Math.floor(modelData.complexity * 10);
    aiOracleDiceGame.stats.aiPoints += pointsEarned;

    // Enable actions
    document.getElementById('oracleActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'AI model trained! Ready for predictions.';

    // Update displays
    updateAIStatus();
    updateMultiplierDisplay();
}

// Update neural weights
function updateNeuralWeights() {
    const learningRate = aiOracleDiceGame.aiOracle.learningRate;

    // Simulate weight updates
    aiOracleDiceGame.aiOracle.neuralWeights = aiOracleDiceGame.aiOracle.neuralWeights.map(weight => {
        const adjustment = (Math.random() - 0.5) * learningRate;
        return Math.max(-1, Math.min(1, weight + adjustment));
    });
}

// Make AI prediction
function makeAIPrediction() {
    if (!aiOracleDiceGame.isPlaying) return;

    // Generate AI prediction using trained model
    const prediction = generateAIPrediction();

    // Store prediction
    aiOracleDiceGame.dice.aiPrediction = prediction;
    aiOracleDiceGame.dice.predictedSum = prediction.sum;
    aiOracleDiceGame.dice.confidenceLevel = prediction.confidence;

    // Calculate multiplier boost based on confidence
    const modelData = aiOracleDiceGame.mlModels[aiOracleDiceGame.aiModel];
    const baseMultiplier = 1.0 + (modelData.multiplierBonus * prediction.confidence);
    aiOracleDiceGame.dice.multiplierBoost = baseMultiplier;

    // Update displays
    document.getElementById('predictedSum').textContent = prediction.sum;
    document.getElementById('confidenceLevel').textContent = `${Math.floor(prediction.confidence * 100)}%`;
    document.getElementById('predictionText').textContent = prediction.reasoning;

    // Visual feedback
    document.getElementById('oracleEvent').classList.remove('hidden');
    document.getElementById('oracleEvent').textContent = 'PREDICTION MADE!';
    setTimeout(() => {
        document.getElementById('oracleEvent').classList.add('hidden');
    }, 2000);

    updateMultiplierDisplay();
    document.getElementById('gameStatus').textContent = 'AI prediction complete! Roll the dice.';
}

// Generate AI prediction using machine learning
function generateAIPrediction() {
    const modelData = aiOracleDiceGame.mlModels[aiOracleDiceGame.aiModel];
    const modeData = PREDICTION_MODES[aiOracleDiceGame.predictionMode];
    const aiModelData = AI_MODELS[aiOracleDiceGame.aiModel];

    // Analyze historical data
    const historicalPattern = analyzeHistoricalData();

    // Apply neural network prediction
    const neuralPrediction = applyNeuralNetwork();

    // Combine predictions based on mode
    let predictedSum = 0;
    let confidence = aiOracleDiceGame.aiOracle.predictionConfidence;
    let reasoning = '';

    switch (aiOracleDiceGame.predictionMode) {
        case 'pattern':
            predictedSum = Math.round(historicalPattern.averageSum);
            confidence *= modeData.confidenceMultiplier;
            reasoning = `Pattern analysis suggests sum around ${predictedSum} based on historical trends`;
            break;
        case 'probability':
            predictedSum = 17.5; // Expected value for 5 dice
            confidence *= modeData.confidenceMultiplier;
            reasoning = `Probability analysis indicates expected sum of ${predictedSum}`;
            break;
        case 'trend':
            predictedSum = Math.round(neuralPrediction + historicalPattern.trend);
            confidence *= modeData.confidenceMultiplier;
            reasoning = `Trend analysis predicts ${predictedSum} based on recent patterns`;
            break;
        case 'chaos':
            predictedSum = Math.round(15 + (Math.random() - 0.5) * 10);
            confidence *= modeData.confidenceMultiplier;
            reasoning = `Chaos theory suggests unpredictable sum around ${predictedSum}`;
            break;
        case 'hybrid':
            predictedSum = Math.round((historicalPattern.averageSum + neuralPrediction + 17.5) / 3);
            confidence *= modeData.confidenceMultiplier;
            reasoning = `Hybrid approach combines multiple models for sum ${predictedSum}`;
            break;
    }

    // Apply model accuracy influence
    confidence = Math.min(0.95, confidence * aiOracleDiceGame.aiOracle.modelAccuracy);

    // Ensure reasonable bounds
    predictedSum = Math.max(5, Math.min(30, predictedSum));

    return {
        sum: predictedSum,
        confidence: confidence,
        reasoning: reasoning,
        model: aiOracleDiceGame.aiModel,
        mode: aiOracleDiceGame.predictionMode
    };
}

// Analyze historical data
function analyzeHistoricalData() {
    const trainingData = aiOracleDiceGame.aiOracle.trainingData;

    if (trainingData.length === 0) {
        return {
            averageSum: 17.5,
            trend: 0,
            variance: 8.75
        };
    }

    const sums = trainingData.map(data => data.sum);
    const averageSum = sums.reduce((sum, val) => sum + val, 0) / sums.length;

    // Calculate trend (last 5 vs first 5)
    let trend = 0;
    if (sums.length >= 10) {
        const recent = sums.slice(-5).reduce((sum, val) => sum + val, 0) / 5;
        const early = sums.slice(0, 5).reduce((sum, val) => sum + val, 0) / 5;
        trend = recent - early;
    }

    // Calculate variance
    const variance = sums.reduce((sum, val) => sum + Math.pow(val - averageSum, 2), 0) / sums.length;

    return { averageSum, trend, variance };
}

// Apply neural network prediction
function applyNeuralNetwork() {
    const weights = aiOracleDiceGame.aiOracle.neuralWeights;
    const features = extractFeatures();

    // Simple neural network forward pass
    let prediction = 0;
    for (let i = 0; i < Math.min(weights.length, features.length); i++) {
        prediction += weights[i] * features[i];
    }

    // Apply activation function (sigmoid scaled to dice range)
    prediction = 5 + 25 * (1 / (1 + Math.exp(-prediction)));

    return prediction;
}

// Extract features for neural network
function extractFeatures() {
    const features = [];

    // Time-based features
    features.push(Math.sin(Date.now() / 10000)); // Time cycle
    features.push(Math.cos(Date.now() / 10000));

    // Historical features
    const trainingData = aiOracleDiceGame.aiOracle.trainingData;
    if (trainingData.length > 0) {
        const lastSum = trainingData[trainingData.length - 1].sum;
        features.push(lastSum / 30); // Normalized last sum
        features.push(trainingData.length / 100); // Experience factor
    } else {
        features.push(0.5); // Default values
        features.push(0.0);
    }

    // Model-specific features
    features.push(aiOracleDiceGame.aiOracle.modelAccuracy);
    features.push(aiOracleDiceGame.aiOracle.predictionConfidence);
    features.push(aiOracleDiceGame.aiOracle.learningRate);

    // Pad or truncate to match weight count
    while (features.length < 25) {
        features.push(Math.random() * 0.1); // Small random features
    }

    return features.slice(0, 25);
}

// Roll oracle dice with AI prediction influence (3-5% win rate)
function rollOracleDice() {
    if (!aiOracleDiceGame.isPlaying || !aiOracleDiceGame.dice.aiPrediction) return;

    document.getElementById('gameStatus').textContent = 'Rolling AI Oracle dice...';

    // Generate dice results with AI influence
    const diceResults = generateAIInfluencedDiceRoll();

    // Animate dice roll
    animateOracleDiceRoll(diceResults);

    // Resolve after animation
    setTimeout(() => {
        resolveOracleDiceRoll(diceResults);
    }, 3000);
}

// Generate AI-influenced dice roll (improved for 3-5% win rate)
function generateAIInfluencedDiceRoll() {
    const aiModelData = AI_MODELS[aiOracleDiceGame.aiModel];
    const prediction = aiOracleDiceGame.dice.aiPrediction;

    // Calculate AI influence on dice outcomes
    const predictionInfluence = aiModelData.predictionWeight * prediction.confidence;

    // Generate base dice rolls
    const diceValues = [];
    for (let i = 0; i < 5; i++) {
        let dieValue = Math.floor(Math.random() * 6) + 1;

        // Apply AI influence (improved)
        if (Math.random() < predictionInfluence) {
            // AI tries to influence towards predicted outcome
            const targetAverage = prediction.sum / 5;
            if (dieValue < targetAverage) {
                dieValue = Math.min(6, dieValue + 1);
            } else if (dieValue > targetAverage) {
                dieValue = Math.max(1, dieValue - 1);
            }
        }

        diceValues.push(dieValue);
    }

    const actualSum = diceValues.reduce((sum, val) => sum + val, 0);

    // Calculate prediction accuracy
    const predictionError = Math.abs(actualSum - prediction.sum);
    const isAccuratePrediction = predictionError <= 3; // Within 3 points

    return {
        values: diceValues,
        sum: actualSum,
        prediction: prediction,
        isAccurate: isAccuratePrediction,
        error: predictionError
    };
}

// Animate oracle dice roll
function animateOracleDiceRoll(results) {
    const dice = document.querySelectorAll('.oracle-die');

    // Add rolling animation
    dice.forEach((die, index) => {
        die.classList.add('animate-spin');
        die.style.transform = 'scale(1.2)';

        // Show random values during animation
        const rollInterval = setInterval(() => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        }, 100);

        // Stop animation and show final value
        setTimeout(() => {
            clearInterval(rollInterval);
            die.classList.remove('animate-spin');
            die.style.transform = 'scale(1)';
            die.textContent = results.values[index];

            // Add glow effect based on prediction accuracy
            if (results.isAccurate) {
                die.classList.add('ring-2', 'ring-green-400', 'animate-pulse');
            } else {
                die.classList.add('ring-1', 'ring-red-400');
            }
        }, 1000 + index * 200);
    });
}

// Resolve oracle dice roll with AI prediction bonuses (3-5% win rate)
function resolveOracleDiceRoll(results) {
    const aiModelData = AI_MODELS[aiOracleDiceGame.aiModel];
    const modelData = aiOracleDiceGame.mlModels[aiOracleDiceGame.aiModel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Store actual sum
    aiOracleDiceGame.dice.actualSum = results.sum;
    aiOracleDiceGame.dice.values = results.values;

    // Update training data
    aiOracleDiceGame.aiOracle.trainingData.push({
        sum: results.sum,
        values: results.values,
        prediction: results.prediction,
        accuracy: results.isAccurate,
        timestamp: Date.now()
    });

    // Update prediction statistics
    aiOracleDiceGame.aiOracle.totalPredictions++;
    aiOracleDiceGame.stats.predictionsTotal++;

    if (results.isAccurate) {
        aiOracleDiceGame.aiOracle.correctPredictions++;
        aiOracleDiceGame.aiOracle.predictionStreak++;
        aiOracleDiceGame.stats.predictionsCorrect++;
    } else {
        aiOracleDiceGame.aiOracle.predictionStreak = 0;
    }

    // Calculate prediction accuracy
    aiOracleDiceGame.stats.predictionAccuracy = aiOracleDiceGame.stats.predictionsCorrect / aiOracleDiceGame.stats.predictionsTotal;

    // Check for winning combinations
    const combination = analyzeDiceCombination(results.values);

    if (combination.type !== 'none') {
        // Base payout for combination
        totalWinnings = aiOracleDiceGame.betAmount * (combination.multiplier / 100);
        resultMessage = combination.name;

        // Apply multiplier boost from AI prediction
        if (results.isAccurate) {
            totalWinnings *= aiOracleDiceGame.dice.multiplierBoost;
            resultMessage += ' + AI Boost!';
        }
    }

    // Apply AI prediction bonuses (actually work)
    if (results.isAccurate && totalWinnings > 0) {
        const predictionBonus = Math.floor(totalWinnings * AI_ORACLE_PAYOUTS.PREDICTION_BONUS);
        totalWinnings += predictionBonus;
        resultMessage += ' + Prediction Bonus!';
    }

    // Apply accuracy bonus
    if (aiOracleDiceGame.aiOracle.modelAccuracy >= 0.80 && totalWinnings > 0) {
        const accuracyBonus = Math.floor(totalWinnings * AI_ORACLE_PAYOUTS.ACCURACY_BONUS);
        totalWinnings += accuracyBonus;
        resultMessage += ' + Accuracy Bonus!';
    }

    // Apply learning bonus
    if (aiOracleDiceGame.aiOracle.predictionStreak >= 3 && totalWinnings > 0) {
        const learningBonus = Math.floor(totalWinnings * AI_ORACLE_PAYOUTS.LEARNING_BONUS);
        totalWinnings += learningBonus;
        resultMessage += ' + Learning Bonus!';
    }

    // Apply confidence bonus
    if (results.prediction.confidence >= 0.80 && totalWinnings > 0) {
        const confidenceBonus = Math.floor(totalWinnings * AI_ORACLE_PAYOUTS.CONFIDENCE_BONUS);
        totalWinnings += confidenceBonus;
        resultMessage += ' + Confidence Bonus!';
    }

    // Check for special AI achievements
    if (results.isAccurate && aiOracleDiceGame.aiOracle.predictionStreak >= 5) {
        const oracleBonus = Math.floor(aiOracleDiceGame.betAmount * AI_ORACLE_PAYOUTS.ORACLE_PERFECT / 100);
        totalWinnings += oracleBonus;
        resultMessage += ' + Oracle Perfect!';
    }

    // Apply model multiplier
    totalWinnings = Math.floor(totalWinnings * aiModelData.payoutMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(aiOracleDiceGame.betAmount * 0.5); // 50% return
        resultMessage = 'AI learning reward';
    }

    // Award AI points
    const pointsEarned = results.isAccurate ? 3 : 1;
    aiOracleDiceGame.stats.aiPoints += pointsEarned;

    // Update best multiplier
    if (aiOracleDiceGame.dice.multiplierBoost > aiOracleDiceGame.stats.bestMultiplier) {
        aiOracleDiceGame.stats.bestMultiplier = aiOracleDiceGame.dice.multiplierBoost;
    }

    // Add winnings to balance
    balance += totalWinnings;
    aiOracleDiceGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterRoll(results.isAccurate, totalWinnings);

    if (!resultMessage) {
        if (results.isAccurate) {
            resultMessage = 'Accurate AI prediction!';
        } else {
            resultMessage = 'AI learning from outcome';
        }
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Model: ${AI_MODELS[aiOracleDiceGame.aiModel].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show prediction result
    document.getElementById('oracleEvent').classList.remove('hidden');
    document.getElementById('oracleEvent').textContent = results.isAccurate ? 'PREDICTION CORRECT!' : 'PREDICTION MISSED';
    document.getElementById('oracleEvent').className = `text-sm font-bold ${results.isAccurate ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Analyze dice combination
function analyzeDiceCombination(values) {
    const counts = {};
    values.forEach(value => {
        counts[value] = (counts[value] || 0) + 1;
    });

    const countValues = Object.values(counts).sort((a, b) => b - a);
    const uniqueValues = Object.keys(counts).map(Number).sort((a, b) => a - b);

    // Check for specific combinations
    if (values.every(v => v === 6)) {
        return { type: 'five_sixes', name: 'Five Sixes', multiplier: AI_ORACLE_PAYOUTS.FIVE_SIXES };
    }

    // Check for straights
    if (uniqueValues.length === 5) {
        if ((uniqueValues[0] === 1 && uniqueValues[4] === 5) || (uniqueValues[0] === 2 && uniqueValues[4] === 6)) {
            return { type: 'straight', name: 'Straight', multiplier: AI_ORACLE_PAYOUTS.STRAIGHT };
        }
    }

    // Check for four of a kind
    if (countValues[0] === 4) {
        return { type: 'four_kind', name: 'Four of a Kind', multiplier: AI_ORACLE_PAYOUTS.FOUR_KIND };
    }

    // Check for full house
    if (countValues[0] === 3 && countValues[1] === 2) {
        return { type: 'full_house', name: 'Full House', multiplier: AI_ORACLE_PAYOUTS.FULL_HOUSE };
    }

    // Check for three of a kind
    if (countValues[0] === 3) {
        return { type: 'three_kind', name: 'Three of a Kind', multiplier: 150 };
    }

    // Check for two pair
    if (countValues[0] === 2 && countValues[1] === 2) {
        return { type: 'two_pair', name: 'Two Pair', multiplier: 120 };
    }

    // Check for one pair
    if (countValues[0] === 2) {
        return { type: 'one_pair', name: 'One Pair', multiplier: 110 };
    }

    return { type: 'none', name: 'No Combination', multiplier: 0 };
}

// Upgrade AI model
function upgradeAIModel() {
    if (!aiOracleDiceGame.isPlaying) return;

    // Upgrade model accuracy and confidence
    aiOracleDiceGame.aiOracle.modelAccuracy = Math.min(0.95, aiOracleDiceGame.aiOracle.modelAccuracy + 0.02);
    aiOracleDiceGame.aiOracle.predictionConfidence = Math.min(0.90, aiOracleDiceGame.aiOracle.predictionConfidence + 0.015);
    aiOracleDiceGame.aiOracle.learningRate = Math.min(0.15, aiOracleDiceGame.aiOracle.learningRate + 0.005);

    // Award AI points
    aiOracleDiceGame.stats.aiPoints += 5;
    aiOracleDiceGame.stats.modelUpgrades++;

    // Visual feedback
    document.getElementById('oracleEvent').classList.remove('hidden');
    document.getElementById('oracleEvent').textContent = 'MODEL UPGRADED!';
    setTimeout(() => {
        document.getElementById('oracleEvent').classList.add('hidden');
    }, 2000);

    updateAIStatus();
}

// Update AI Oracle display
function updateAIOracleDisplay() {
    updateAIStatus();
    updateMultiplierDisplay();
}

// Update AI status
function updateAIStatus() {
    document.getElementById('modelAccuracy').innerHTML =
        `<div class="text-green-400 font-bold">ACCURACY: ${Math.floor(aiOracleDiceGame.aiOracle.modelAccuracy * 100)}%</div>`;
    document.getElementById('predictionConfidence').innerHTML =
        `<div class="text-blue-400 font-bold">CONFIDENCE: ${Math.floor(aiOracleDiceGame.aiOracle.predictionConfidence * 100)}%</div>`;
    document.getElementById('learningRate').innerHTML =
        `<div class="text-purple-400 font-bold">LEARNING: ${Math.floor(aiOracleDiceGame.aiOracle.learningRate * 100)}%</div>`;
    document.getElementById('predictionStreak').innerHTML =
        `<div class="text-yellow-400 font-bold">STREAK: ${aiOracleDiceGame.aiOracle.predictionStreak}</div>`;
}

// Update multiplier display
function updateMultiplierDisplay() {
    const multiplier = aiOracleDiceGame.dice.multiplierBoost;
    document.getElementById('multiplierBoost').querySelector('.text-2xl').textContent = `${multiplier.toFixed(1)}x`;
}

// Update AI neural effects
function updateAINeuralEffects() {
    // Update neural effects based on model accuracy
    const accuracy = aiOracleDiceGame.aiOracle.modelAccuracy;
    const effects = document.querySelectorAll('#aiNeuralEffects circle');

    effects.forEach((effect, index) => {
        if (accuracy >= 0.85) {
            effect.setAttribute('opacity', '0.9');
            effect.setAttribute('fill', '#10b981'); // Green for high accuracy
        } else if (accuracy >= 0.75) {
            effect.setAttribute('opacity', '0.7');
            effect.setAttribute('fill', '#06b6d4'); // Cyan for good accuracy
        } else {
            effect.setAttribute('opacity', '0.5');
            effect.setAttribute('fill', '#0891b2'); // Dark cyan for lower accuracy
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${aiOracleDiceGame.betAmount}`;
    document.getElementById('aiPointsDisplay').textContent = aiOracleDiceGame.stats.aiPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = aiOracleDiceGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${aiOracleDiceGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${aiOracleDiceGame.stats.totalWagered}`;
    document.getElementById('predictionAccuracyStat').textContent = `${Math.floor(aiOracleDiceGame.stats.predictionAccuracy * 100)}%`;
    document.getElementById('bestMultiplier').textContent = `${aiOracleDiceGame.stats.bestMultiplier.toFixed(1)}x`;

    const netResult = aiOracleDiceGame.stats.totalWon - aiOracleDiceGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-cyan-400' : 'text-red-400'}`;
}

// Update stats after roll
function updateGameStatsAfterRoll(won, winnings) {
    aiOracleDiceGame.stats.gamesPlayed++;
    aiOracleDiceGame.stats.totalWagered += aiOracleDiceGame.betAmount;
    aiOracleDiceGame.stats.totalWon += winnings;

    if (won) {
        aiOracleDiceGame.stats.gamesWon++;
        aiOracleDiceGame.stats.currentStreak++;
        aiOracleDiceGame.streakData.currentWinStreak++;
        aiOracleDiceGame.streakData.currentLossStreak = 0;

        if (aiOracleDiceGame.streakData.currentWinStreak > aiOracleDiceGame.streakData.longestWinStreak) {
            aiOracleDiceGame.streakData.longestWinStreak = aiOracleDiceGame.streakData.currentWinStreak;
        }

        if (winnings > aiOracleDiceGame.stats.biggestWin) {
            aiOracleDiceGame.stats.biggestWin = winnings;
        }
    } else {
        aiOracleDiceGame.stats.currentStreak = 0;
        aiOracleDiceGame.streakData.currentWinStreak = 0;
        aiOracleDiceGame.streakData.currentLossStreak++;

        if (aiOracleDiceGame.streakData.currentLossStreak > aiOracleDiceGame.streakData.longestLossStreak) {
            aiOracleDiceGame.streakData.longestLossStreak = aiOracleDiceGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to AI prediction mechanics)
    aiOracleDiceGame.stats.winRate = (aiOracleDiceGame.stats.gamesWon / aiOracleDiceGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next AI Oracle session
function resetGame() {
    aiOracleDiceGame.isPlaying = false;
    aiOracleDiceGame.betAmount = 0;
    aiOracleDiceGame.totalBet = 0;
    aiOracleDiceGame.gameResult = '';
    aiOracleDiceGame.totalWin = 0;

    // Reset dice system
    aiOracleDiceGame.dice.values = [1, 1, 1, 1, 1];
    aiOracleDiceGame.dice.predictedSum = 0;
    aiOracleDiceGame.dice.actualSum = 0;
    aiOracleDiceGame.dice.multiplierBoost = 1.0;
    aiOracleDiceGame.dice.aiPrediction = null;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('oracleEvent').classList.add('hidden');
    document.getElementById('oracleActions').classList.add('hidden');

    // Reset dice display
    const dice = document.querySelectorAll('.oracle-die');
    dice.forEach((die, index) => {
        die.textContent = '1';
        die.className = 'oracle-die w-16 h-16 bg-gradient-to-br from-cyan-600 to-blue-800 rounded-lg border-2 border-cyan-400 flex items-center justify-center text-2xl font-bold text-white transition-all duration-500';
    });

    // Reset prediction display
    document.getElementById('predictionText').textContent = 'AI model ready for training...';
    document.getElementById('predictedSum').textContent = '0';
    document.getElementById('confidenceLevel').textContent = '65%';

    // Reset training display
    document.getElementById('trainingBar').style.width = '0%';
    document.getElementById('trainingStatus').textContent = 'Ready';
    document.getElementById('trainingTimer').querySelector('.text-2xl').textContent = '120s';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable train button
    document.getElementById('trainModel').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'AI Oracle ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to AI Oracle Dice - Machine Learning Predictions for Multiplier Boosts';

    // Reinitialize systems for next session
    initializeAIOracleSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadAIOracleDiceGame();
});