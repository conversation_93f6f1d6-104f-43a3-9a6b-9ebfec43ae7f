// Web3 Wheel - Decentralized Jackpots with Community Governance
// Experimental Concept Implementation with Blockchain and DeFi Theme
// Designed to maintain 3-5% player win rate with decentralized mechanics

// Game state
let balance = 1000;

// Game state object with Web3 system
let web3WheelGame = {
    isPlaying: false,
    blockchain: 'ethereum', // ethereum, polygon, binance, solana, avalanche
    daoGovernance: 'democratic', // democratic, weighted, delegated, quadratic, liquid
    betAmount: 0,
    totalBet: 0,

    // Web3 system
    web3: {
        gasPrice: 20, // 20 gwei gas price
        blockHeight: 18500000, // Current block height
        networkCongestion: 0.35, // 35% network congestion
        stakingRewards: 0.08, // 8% staking rewards
        liquidityPool: 0.75, // 75% liquidity pool depth
        governanceTokens: 100, // 100 governance tokens
        web3Advantage: 1.0,
        transactionHistory: [],
        daoLevel: 'member', // member, delegate, validator, whale, founder
        web3Points: 0,
        portfolioValue: 0,
        decentralizationScore: 0.60, // 60% decentralization
        consensusParticipation: 0.45 // 45% consensus participation
    },

    // Wheel game state
    wheel: {
        segments: [],
        currentRotation: 0,
        gamePhase: 'spinning', // spinning, consensus, governance, finished
        gameResult: '',
        web3Bonus: 1.0,
        daoBonus: 0.0,
        spinHistory: [],
        consensusReached: false,
        governanceActive: false,
        stakingBonus: false,
        liquidityEvent: '',
        networkUpgrade: false
    },

    // Blockchain networks
    blockchains: {
        ethereum: {
            name: 'Ethereum',
            gasMultiplier: 1.5, // 1.5x gas costs
            security: 0.95, // 95% security
            decentralization: 0.90, // 90% decentralization
            web3Bonus: 0.25 // 25% web3 bonus
        },
        polygon: {
            name: 'Polygon',
            gasMultiplier: 0.1, // 0.1x gas costs
            security: 0.85, // 85% security
            decentralization: 0.70, // 70% decentralization
            web3Bonus: 0.20 // 20% web3 bonus
        },
        binance: {
            name: 'Binance Smart Chain',
            gasMultiplier: 0.2, // 0.2x gas costs
            security: 0.80, // 80% security
            decentralization: 0.60, // 60% decentralization
            web3Bonus: 0.18 // 18% web3 bonus
        },
        solana: {
            name: 'Solana',
            gasMultiplier: 0.05, // 0.05x gas costs
            security: 0.88, // 88% security
            decentralization: 0.75, // 75% decentralization
            web3Bonus: 0.22 // 22% web3 bonus
        },
        avalanche: {
            name: 'Avalanche',
            gasMultiplier: 0.3, // 0.3x gas costs
            security: 0.90, // 90% security
            decentralization: 0.80, // 80% decentralization
            web3Bonus: 0.28 // 28% web3 bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        consensusReached: 0,
        governanceVotes: 0,
        stakingRewards: 0,
        liquidityProvided: 0,
        web3Points: 0,
        daoParticipation: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Blockchain networks with balanced Web3 requirements (3-5% win rate)
const BLOCKCHAIN_NETWORKS = {
    ethereum: {
        name: 'Ethereum',
        web3Weight: 0.30, // 30% web3 influence (increased)
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        daoBonus: 0.25, // 25% DAO bonus
        stakingBonus: 0.20 // 20% staking bonus
    },
    polygon: {
        name: 'Polygon',
        web3Weight: 0.25, // 25% web3 influence
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.98, // Good payouts
        daoBonus: 0.20, // 20% DAO bonus
        stakingBonus: 0.18 // 18% staking bonus
    },
    binance: {
        name: 'Binance Smart Chain',
        web3Weight: 0.22, // 22% web3 influence
        luckFactor: 0.78, // 78% luck factor
        payoutMultiplier: 0.95, // Good payouts
        daoBonus: 0.18, // 18% DAO bonus
        stakingBonus: 0.15 // 15% staking bonus
    },
    solana: {
        name: 'Solana',
        web3Weight: 0.28, // 28% web3 influence
        luckFactor: 0.72, // 72% luck factor
        payoutMultiplier: 1.02, // Premium payouts
        daoBonus: 0.22, // 22% DAO bonus
        stakingBonus: 0.19 // 19% staking bonus
    },
    avalanche: {
        name: 'Avalanche',
        web3Weight: 0.32, // 32% web3 influence
        luckFactor: 0.68, // 68% luck factor
        payoutMultiplier: 1.08, // Premium payouts
        daoBonus: 0.28, // 28% DAO bonus
        stakingBonus: 0.25 // 25% staking bonus
    }
};

const DAO_GOVERNANCE = {
    democratic: {
        name: 'Democratic Governance',
        consensusThreshold: 0.51, // 51% consensus
        participationBonus: 0.15, // 15% participation bonus
        decisionSpeed: 0.60 // 60% decision speed
    },
    weighted: {
        name: 'Weighted Voting',
        consensusThreshold: 0.60, // 60% consensus
        participationBonus: 0.20, // 20% participation bonus
        decisionSpeed: 0.70 // 70% decision speed
    },
    delegated: {
        name: 'Delegated Governance',
        consensusThreshold: 0.40, // 40% consensus
        participationBonus: 0.25, // 25% participation bonus
        decisionSpeed: 0.80 // 80% decision speed
    },
    quadratic: {
        name: 'Quadratic Voting',
        consensusThreshold: 0.55, // 55% consensus
        participationBonus: 0.30, // 30% participation bonus
        decisionSpeed: 0.65 // 65% decision speed
    },
    liquid: {
        name: 'Liquid Democracy',
        consensusThreshold: 0.45, // 45% consensus
        participationBonus: 0.35, // 35% participation bonus
        decisionSpeed: 0.85 // 85% decision speed
    }
};

// Improved payout table with Web3 theme (3-5% win rate)
const WEB3_WHEEL_PAYOUTS = {
    // Perfect Web3 achievements (moderately reduced)
    DAO_FOUNDER: 6000, // Reduced from 12000:1 but still excellent
    WEB3_PIONEER: 4500, // Reduced from 9000:1
    DEFI_WHALE: 3000, // Reduced from 6000:1
    CONSENSUS_MASTER: 2200, // Reduced from 4400:1

    // Wheel segment payouts with Web3 bonuses
    JACKPOT: 5000, // 50:1 payout
    MEGA_WIN: 2500, // 25:1 payout
    BIG_WIN: 1000, // 10:1 payout
    MEDIUM_WIN: 500, // 5:1 payout
    SMALL_WIN: 200, // 2:1 payout
    BREAK_EVEN: 100, // 1:1 payout

    // Web3 bonuses (actually apply more often)
    DAO_BONUS: 0.85, // 85% of displayed bonus (increased)
    STAKING_BONUS: 0.75, // 75% of displayed bonus (increased)
    LIQUIDITY_BONUS: 0.65, // 65% of displayed bonus (increased)
    CONSENSUS_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// Wheel segments with Web3 theme
const WHEEL_SEGMENTS = [
    { label: 'JACKPOT', value: 5000, color: '#10b981', probability: 0.01 }, // 1%
    { label: 'MEGA WIN', value: 2500, color: '#3b82f6', probability: 0.02 }, // 2%
    { label: 'BIG WIN', value: 1000, color: '#8b5cf6', probability: 0.05 }, // 5%
    { label: 'MEDIUM WIN', value: 500, color: '#f59e0b', probability: 0.10 }, // 10%
    { label: 'SMALL WIN', value: 200, color: '#ef4444', probability: 0.15 }, // 15%
    { label: 'BREAK EVEN', value: 100, color: '#6b7280', probability: 0.20 }, // 20%
    { label: 'LOSE', value: 0, color: '#1f2937', probability: 0.47 } // 47%
];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadWeb3WheelGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">WEB3 CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BLOCKCHAIN</label>
                        <select id="blockchain" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="ethereum">Ethereum</option>
                            <option value="polygon">Polygon</option>
                            <option value="binance">Binance Smart Chain</option>
                            <option value="solana">Solana</option>
                            <option value="avalanche">Avalanche</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DAO GOVERNANCE</label>
                        <select id="daoGovernance" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="democratic">Democratic Governance</option>
                            <option value="weighted">Weighted Voting</option>
                            <option value="delegated">Delegated Governance</option>
                            <option value="quadratic">Quadratic Voting</option>
                            <option value="liquid">Liquid Democracy</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="spinWeb3Wheel" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        SPIN WEB3 WHEEL
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Web3 Points</div>
                        <div id="web3PointsDisplay" class="text-lg font-bold text-purple-400">0</div>
                    </div>
                </div>

                <!-- Web3 Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">WEB3 STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="gasPrice" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">GAS: 20 GWEI</div>
                        </div>
                        <div id="blockHeight" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">BLOCK: 18.5M</div>
                        </div>
                        <div id="stakingRewards" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">STAKING: 8%</div>
                        </div>
                        <div id="daoLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: MEMBER</div>
                        </div>
                    </div>
                </div>

                <!-- DAO Governance Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">DAO GOVERNANCE</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Governance Metrics:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Consensus:</span>
                            <span class="text-green-400">45%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Decentralization:</span>
                            <span class="text-blue-400">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Liquidity:</span>
                            <span class="text-cyan-400">75%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Web3 Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">DAO:</span>
                            <span class="text-purple-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Staking:</span>
                            <span class="text-purple-400">75%</span>
                        </div>
                        <div class="text-xs text-purple-400 mt-2">*Governance improves odds</div>
                        <div class="text-xs text-purple-400">*Consensus provides bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Web3 Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <!-- Web3 Wheel Arena -->
                    <div id="web3WheelArena" class="relative bg-gradient-to-br from-black via-purple-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Web3 Background -->
                        <div id="web3Background" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="web3Gradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="web3Pattern" width="40" height="40" patternUnits="userSpaceOnUse">
                                        <circle cx="20" cy="20" r="15" fill="none" stroke="#8b5cf6" stroke-width="1" opacity="0.4"/>
                                        <circle cx="20" cy="20" r="8" fill="none" stroke="#7c3aed" stroke-width="1" opacity="0.6"/>
                                        <circle cx="20" cy="20" r="4" fill="#6d28d9" opacity="0.8"/>
                                        <path d="M5,20 L35,20 M20,5 L20,35 M10,10 L30,30 M30,10 L10,30" stroke="#8b5cf6" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#web3Pattern)" />
                                <circle id="web3Core" cx="50%" cy="50%" r="25%" fill="url(#web3Gradient)" class="animate-pulse" />
                                <g id="web3Effects">
                                    <!-- Web3 effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Web3 Wheel -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">WEB3 DECENTRALIZED WHEEL</div>
                                <div id="web3WheelContainer" class="relative">
                                    <svg id="web3WheelSvg" width="280" height="280" class="border-4 border-purple-600 rounded-full bg-black/70">
                                        <!-- Wheel segments will be drawn here -->
                                    </svg>
                                    <!-- Wheel pointer -->
                                    <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
                                        <div class="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-purple-400"></div>
                                    </div>
                                    <!-- Center hub -->
                                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                        <div class="w-12 h-12 bg-purple-600 rounded-full border-4 border-purple-400 flex items-center justify-center">
                                            <div class="text-white font-bold text-xs">DAO</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Community-governed wheel</div>
                            </div>
                        </div>

                        <!-- Consensus Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">CONSENSUS STATUS</div>
                                <div id="consensusDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="consensusStatus" class="text-lg text-white mb-3">Building consensus...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-purple-400 mb-1">VOTES</div>
                                            <div id="consensusVotes" class="text-xl font-bold text-white">45%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-purple-400 mb-1">QUORUM</div>
                                            <div id="quorumStatus" class="text-xl font-bold text-green-400">51%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-purple-400 mb-1">TOKENS</div>
                                            <div id="governanceTokens" class="text-xl font-bold text-yellow-400">100</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Decentralized decision making</div>
                            </div>
                        </div>

                        <!-- Governance Progress -->
                        <div id="governanceProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">GOVERNANCE PROCESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="governanceBar" class="bg-gradient-to-r from-purple-400 to-pink-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Proposal</span>
                                    <span id="governanceStatus">Ready</span>
                                    <span>Consensus</span>
                                </div>
                            </div>
                        </div>

                        <!-- Gas Price -->
                        <div id="gasPriceDisplay" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">GAS</div>
                                <div class="text-2xl font-bold text-white text-center">20</div>
                                <div class="text-xs text-gray-400 mt-1">GWEI</div>
                            </div>
                        </div>

                        <!-- Network Congestion -->
                        <div id="networkCongestion" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">NETWORK</div>
                                <div class="text-2xl font-bold text-white text-center">35%</div>
                                <div class="text-xs text-gray-400 mt-1">Congestion</div>
                            </div>
                        </div>

                        <!-- Liquidity Pool -->
                        <div id="liquidityPool" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-cyan-400 mb-1">LIQUIDITY</div>
                                <div class="text-2xl font-bold text-white text-center">75%</div>
                                <div class="text-xs text-gray-400 mt-1">Pool</div>
                            </div>
                        </div>

                        <!-- Selected Blockchain -->
                        <div id="selectedBlockchain" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">CHAIN</div>
                                <div class="text-lg font-bold text-white text-center">ETH</div>
                                <div class="text-xs text-gray-400 mt-1">Selected</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Web3 systems ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="web3Event" class="text-sm font-bold text-purple-400 hidden animate-pulse">CONSENSUS REACHED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Web3 Wheel - Decentralized Jackpots with Community Governance</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-purple-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-purple-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Consensus Reached</div>
                <div id="consensusReached" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">DAO Participation</div>
                <div id="daoParticipation" class="text-xl font-bold text-blue-400">0</div>
            </div>
        </div>
    `;

    initializeWeb3Wheel();
}

// Initialize the game
function initializeWeb3Wheel() {
    document.getElementById('spinWeb3Wheel').addEventListener('click', spinWeb3WheelGame);

    // Initialize Web3 systems
    initializeWeb3Systems();
    generateWeb3Effects();
    drawWeb3Wheel();
    updateGameStats();
}

// Initialize Web3 systems
function initializeWeb3Systems() {
    // Reset Web3 system
    web3WheelGame.web3.gasPrice = Math.max(5, 20 + (Math.random() - 0.5) * 30); // 5-35 gwei range
    web3WheelGame.web3.blockHeight = 18500000 + Math.floor(Math.random() * 1000); // Recent blocks
    web3WheelGame.web3.networkCongestion = Math.min(0.90, Math.max(0.10, 0.35 + (Math.random() - 0.5) * 0.4)); // 15-75% range
    web3WheelGame.web3.stakingRewards = Math.min(0.15, Math.max(0.03, 0.08 + (Math.random() - 0.5) * 0.06)); // 5-11% range
    web3WheelGame.web3.liquidityPool = Math.min(0.95, Math.max(0.50, 0.75 + (Math.random() - 0.5) * 0.3)); // 60-90% range
    web3WheelGame.web3.governanceTokens = Math.max(50, 100 + (web3WheelGame.stats.web3Points * 2)); // Increase with points
    web3WheelGame.web3.web3Advantage = 1.0;
    web3WheelGame.web3.transactionHistory = [];
    web3WheelGame.web3.daoLevel = calculateDaoLevel();
    web3WheelGame.web3.web3Points = web3WheelGame.stats.web3Points;
    web3WheelGame.web3.portfolioValue = 0;
    web3WheelGame.web3.decentralizationScore = Math.min(0.95, 0.60 + (web3WheelGame.stats.web3Points * 0.008));
    web3WheelGame.web3.consensusParticipation = Math.min(0.80, 0.45 + (web3WheelGame.stats.web3Points * 0.01));

    // Reset wheel system
    web3WheelGame.wheel.segments = [...WHEEL_SEGMENTS];
    web3WheelGame.wheel.currentRotation = 0;
    web3WheelGame.wheel.gamePhase = 'spinning';
    web3WheelGame.wheel.gameResult = '';
    web3WheelGame.wheel.web3Bonus = 1.0;
    web3WheelGame.wheel.daoBonus = 0.0;
    web3WheelGame.wheel.spinHistory = [];
    web3WheelGame.wheel.consensusReached = false;
    web3WheelGame.wheel.governanceActive = false;
    web3WheelGame.wheel.stakingBonus = false;
    web3WheelGame.wheel.liquidityEvent = '';
    web3WheelGame.wheel.networkUpgrade = false;

    updateWeb3Display();
}

// Generate Web3 effects
function generateWeb3Effects() {
    const container = document.getElementById('web3Effects');
    container.innerHTML = '';

    // Create Web3 visualization
    for (let i = 0; i < 16; i++) {
        const web3Node = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        web3Node.setAttribute('cx', `${Math.random() * 100}%`);
        web3Node.setAttribute('cy', `${Math.random() * 100}%`);
        web3Node.setAttribute('r', `${Math.random() * 1.5 + 0.8}%`);
        web3Node.setAttribute('fill', '#8b5cf6');
        web3Node.setAttribute('opacity', '0.7');
        web3Node.classList.add('animate-pulse');
        web3Node.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(web3Node);

        // Add Web3 connections
        if (i > 0 && i % 4 === 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 4) * 2];
            if (prevNode) {
                connection.setAttribute('x1', prevNode.getAttribute('cx'));
                connection.setAttribute('y1', prevNode.getAttribute('cy'));
                connection.setAttribute('x2', web3Node.getAttribute('cx'));
                connection.setAttribute('y2', web3Node.getAttribute('cy'));
                connection.setAttribute('stroke', '#7c3aed');
                connection.setAttribute('stroke-width', '1');
                connection.setAttribute('opacity', '0.5');
                connection.setAttribute('stroke-dasharray', '4,4');
                container.appendChild(connection);
            }
        }
    }
}

// Draw Web3 wheel
function drawWeb3Wheel() {
    const svg = document.getElementById('web3WheelSvg');
    const centerX = 140;
    const centerY = 140;
    const radius = 120;

    // Clear existing segments
    svg.innerHTML = '';

    let currentAngle = 0;

    WHEEL_SEGMENTS.forEach((segment, index) => {
        const segmentAngle = 360 / WHEEL_SEGMENTS.length;
        const startAngle = currentAngle;
        const endAngle = currentAngle + segmentAngle;

        // Convert to radians
        const startRad = (startAngle * Math.PI) / 180;
        const endRad = (endAngle * Math.PI) / 180;

        // Calculate path
        const x1 = centerX + radius * Math.cos(startRad);
        const y1 = centerY + radius * Math.sin(startRad);
        const x2 = centerX + radius * Math.cos(endRad);
        const y2 = centerY + radius * Math.sin(endRad);

        const largeArcFlag = segmentAngle > 180 ? 1 : 0;

        const pathData = [
            `M ${centerX} ${centerY}`,
            `L ${x1} ${y1}`,
            `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            'Z'
        ].join(' ');

        // Create segment
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', pathData);
        path.setAttribute('fill', segment.color);
        path.setAttribute('stroke', '#ffffff');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('opacity', '0.9');
        svg.appendChild(path);

        // Add text label
        const textAngle = startAngle + segmentAngle / 2;
        const textRad = (textAngle * Math.PI) / 180;
        const textX = centerX + (radius * 0.7) * Math.cos(textRad);
        const textY = centerY + (radius * 0.7) * Math.sin(textRad);

        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', textX);
        text.setAttribute('y', textY);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('dominant-baseline', 'middle');
        text.setAttribute('fill', '#ffffff');
        text.setAttribute('font-size', '10');
        text.setAttribute('font-weight', 'bold');
        text.setAttribute('transform', `rotate(${textAngle}, ${textX}, ${textY})`);
        text.textContent = segment.label;
        svg.appendChild(text);

        currentAngle += segmentAngle;
    });
}

// Calculate DAO level
function calculateDaoLevel() {
    const points = web3WheelGame.stats.web3Points;
    if (points >= 100) return 'founder';
    if (points >= 50) return 'whale';
    if (points >= 25) return 'validator';
    if (points >= 10) return 'delegate';
    return 'member';
}

// Spin Web3 wheel with decentralized governance (3-5% win rate)
function spinWeb3WheelGame() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    web3WheelGame.isPlaying = true;
    web3WheelGame.betAmount = betAmount;
    web3WheelGame.totalBet = betAmount;
    web3WheelGame.blockchain = document.getElementById('blockchain').value;
    web3WheelGame.daoGovernance = document.getElementById('daoGovernance').value;

    // Start governance process
    startGovernanceProcess();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('spinWeb3Wheel').disabled = true;
    document.getElementById('gameStatus').textContent = 'Initiating governance process...';
}

// Start governance process
function startGovernanceProcess() {
    const blockchainData = web3WheelGame.blockchains[web3WheelGame.blockchain];
    const daoData = DAO_GOVERNANCE[web3WheelGame.daoGovernance];

    // Update governance information
    document.getElementById('consensusStatus').textContent = `Building ${daoData.name} consensus...`;
    document.getElementById('governanceStatus').textContent = 'Proposing...';

    // Simulate governance process
    simulateGovernanceProcess(blockchainData, daoData);

    // Update Web3 status
    updateWeb3Status();

    // Update visual effects
    updateWeb3Display();
}

// Simulate governance process
function simulateGovernanceProcess(blockchainData, daoData) {
    let progress = 0;
    const governanceTime = 45; // 4.5 seconds governance time

    const governanceInterval = setInterval(() => {
        progress += 100 / governanceTime; // Update every 100ms

        // Update progress bar
        document.getElementById('governanceBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('governanceStatus').textContent = 'Proposing...';
        } else if (progress < 40) {
            document.getElementById('governanceStatus').textContent = 'Voting...';
        } else if (progress < 60) {
            document.getElementById('governanceStatus').textContent = 'Counting...';
        } else if (progress < 80) {
            document.getElementById('governanceStatus').textContent = 'Validating...';
        } else if (progress < 100) {
            document.getElementById('governanceStatus').textContent = 'Finalizing...';
        } else {
            document.getElementById('governanceStatus').textContent = 'Consensus!';
            clearInterval(governanceInterval);
            completeGovernanceProcess(blockchainData, daoData);
        }

        // Update consensus votes based on progress
        const votePercentage = Math.min(80, web3WheelGame.web3.consensusParticipation * 100 + progress * 0.2);
        document.getElementById('consensusVotes').textContent = `${Math.floor(votePercentage)}%`;

    }, 100);
}

// Complete governance process
function completeGovernanceProcess(blockchainData, daoData) {
    // Apply Web3 bonuses
    const web3Bonus = calculateWeb3Bonus();
    const daoBonus = blockchainData.web3Bonus * 0.15; // Up to 15% bonus

    // Update Web3 advantage
    web3WheelGame.wheel.web3Bonus = 1.0 + web3Bonus + daoBonus;

    // Calculate DAO bonus
    web3WheelGame.wheel.daoBonus = blockchainData.web3Bonus * daoData.participationBonus;

    // Award Web3 points
    const pointsEarned = Math.floor(blockchainData.web3Bonus * 100);
    web3WheelGame.stats.web3Points += pointsEarned;

    // Check for consensus
    if (Math.random() < daoData.consensusThreshold) {
        web3WheelGame.wheel.consensusReached = true;
        web3WheelGame.stats.consensusReached++;
        document.getElementById('gameStatus').textContent = 'CONSENSUS REACHED! Spinning wheel...';
    } else {
        document.getElementById('gameStatus').textContent = 'Governance complete! Spinning wheel...';
    }

    // Spin the wheel
    setTimeout(() => spinDecentralizedWheel(), 1000);

    // Update displays
    updateWeb3Status();
    updateConsensusDisplay();

    // Update selected blockchain display
    document.getElementById('selectedBlockchain').querySelector('.text-lg').textContent =
        web3WheelGame.blockchain.toUpperCase().substring(0, 3);
}

// Calculate Web3 bonus
function calculateWeb3Bonus() {
    let bonus = 0;

    // Gas price bonus (lower is better)
    const gasOptimal = web3WheelGame.web3.gasPrice <= 25 ? 0.08 : 0.04;
    bonus += gasOptimal;

    // Network congestion bonus (lower is better)
    const congestionOptimal = web3WheelGame.web3.networkCongestion <= 0.50 ? 0.06 : 0.03;
    bonus += congestionOptimal;

    // Staking rewards bonus
    bonus += web3WheelGame.web3.stakingRewards * 0.8; // Up to 8% bonus

    // Liquidity pool bonus
    bonus += web3WheelGame.web3.liquidityPool * 0.10; // Up to 10% bonus

    // Decentralization score bonus
    bonus += web3WheelGame.web3.decentralizationScore * 0.12; // Up to 12% bonus

    // Consensus participation bonus
    bonus += web3WheelGame.web3.consensusParticipation * 0.10; // Up to 10% bonus

    // DAO level bonus
    const levelBonuses = {
        member: 0.05,
        delegate: 0.08,
        validator: 0.12,
        whale: 0.15,
        founder: 0.20
    };
    bonus += levelBonuses[web3WheelGame.web3.daoLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Spin decentralized wheel with Web3 influence (3-5% win rate)
function spinDecentralizedWheel() {
    // Calculate Web3 influence on outcome
    const web3Influence = calculateWeb3Influence();

    // Generate wheel result with Web3 influence
    const wheelResult = generateWeb3WheelResult(web3Influence);

    // Animate wheel spinning
    animateWheelSpinning(wheelResult);

    // Resolve after animation
    setTimeout(() => {
        resolveWeb3Wheel(wheelResult);
    }, 4000);
}

// Calculate Web3 influence
function calculateWeb3Influence() {
    const blockchainData = BLOCKCHAIN_NETWORKS[web3WheelGame.blockchain];
    const web3Bonus = calculateWeb3Bonus();
    const daoBonus = web3WheelGame.web3.decentralizationScore * 0.10; // Up to 10% bonus
    const stakingBonus = web3WheelGame.web3.stakingRewards * 0.8; // Up to 8% bonus
    const web3PointsBonus = web3WheelGame.stats.web3Points * 0.002; // Web3 points bonus

    return Math.min(0.35, blockchainData.web3Weight + web3Bonus + daoBonus + stakingBonus + web3PointsBonus);
}

// Generate Web3 wheel result with decentralized influence (improved for 3-5% win rate)
function generateWeb3WheelResult(web3Influence) {
    const blockchainData = web3WheelGame.blockchains[web3WheelGame.blockchain];
    const daoData = DAO_GOVERNANCE[web3WheelGame.daoGovernance];

    // Apply Web3 influence to improve odds
    const adjustedOdds = 0.035 + web3Influence; // Base 3.5% + Web3 influence

    // Create weighted segments based on probabilities
    let weightedSegments = [];
    WHEEL_SEGMENTS.forEach(segment => {
        const weight = Math.floor(segment.probability * 1000);
        for (let i = 0; i < weight; i++) {
            weightedSegments.push(segment);
        }
    });

    // Apply Web3 influence (improved)
    if (Math.random() < web3Influence) {
        // Web3 governance tries to improve player's chances
        const betterSegments = WHEEL_SEGMENTS.filter(s => s.value > 0);
        if (Math.random() < 0.6) { // 60% chance to help
            const randomBetterSegment = betterSegments[Math.floor(Math.random() * betterSegments.length)];
            // Increase weight of better segments
            for (let i = 0; i < 200; i++) {
                weightedSegments.push(randomBetterSegment);
            }
        }
    }

    // Apply consensus bonus
    if (web3WheelGame.wheel.consensusReached) {
        // Consensus can improve outcomes
        if (Math.random() < 0.4) { // 40% chance for consensus improvement
            const premiumSegments = WHEEL_SEGMENTS.filter(s => s.value >= 500);
            if (premiumSegments.length > 0) {
                const consensusSegment = premiumSegments[Math.floor(Math.random() * premiumSegments.length)];
                for (let i = 0; i < 300; i++) {
                    weightedSegments.push(consensusSegment);
                }
            }
        }
    }

    // Select final segment
    const selectedSegment = weightedSegments[Math.floor(Math.random() * weightedSegments.length)];

    // Calculate rotation angle
    const segmentIndex = WHEEL_SEGMENTS.findIndex(s => s.label === selectedSegment.label);
    const segmentAngle = 360 / WHEEL_SEGMENTS.length;
    const targetAngle = segmentIndex * segmentAngle + (segmentAngle / 2);
    const rotations = 5 + Math.random() * 3; // 5-8 full rotations
    const finalRotation = rotations * 360 + (360 - targetAngle); // Adjust for pointer at top

    // Check for special Web3 events
    let networkUpgrade = false;
    let stakingBonus = false;
    let liquidityEvent = '';

    // Network upgrade event
    if (Math.random() < blockchainData.security * 0.05) {
        networkUpgrade = true;
        web3WheelGame.wheel.networkUpgrade = true;
    }

    // Staking bonus event
    if (Math.random() < web3WheelGame.web3.stakingRewards * 2) {
        stakingBonus = true;
        web3WheelGame.wheel.stakingBonus = true;
    }

    // Liquidity event
    if (Math.random() < web3WheelGame.web3.liquidityPool * 0.1) {
        liquidityEvent = 'HIGH_LIQUIDITY';
        web3WheelGame.wheel.liquidityEvent = liquidityEvent;
    }

    return {
        segment: selectedSegment,
        rotation: finalRotation,
        web3Influence: web3Influence,
        consensusReached: web3WheelGame.wheel.consensusReached,
        networkUpgrade: networkUpgrade,
        stakingBonus: stakingBonus,
        liquidityEvent: liquidityEvent,
        decentralization: blockchainData.decentralization,
        security: blockchainData.security
    };
}

// Animate wheel spinning
function animateWheelSpinning(results) {
    // Store results for display
    web3WheelGame.wheel.gameResult = results.segment.label;
    web3WheelGame.wheel.liquidityEvent = results.liquidityEvent;

    // Get wheel SVG
    const wheelSvg = document.getElementById('web3WheelSvg');

    // Apply spinning animation
    wheelSvg.style.transition = 'transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    wheelSvg.style.transform = `rotate(${results.rotation}deg)`;

    // Update current rotation
    web3WheelGame.wheel.currentRotation = results.rotation % 360;

    // Update consensus display during spin
    setTimeout(() => updateConsensusDisplay(), 2000);
}

// Resolve Web3 wheel with decentralized bonuses (3-5% win rate)
function resolveWeb3Wheel(results) {
    const blockchainData = BLOCKCHAIN_NETWORKS[web3WheelGame.blockchain];
    const daoData = DAO_GOVERNANCE[web3WheelGame.daoGovernance];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.segment.value > 0;

    // Calculate base payout
    if (playerWon) {
        totalWinnings = Math.floor(web3WheelGame.betAmount * (results.segment.value / 100));
        resultMessage = `${results.segment.label} wins!`;
    }

    // Special event payouts
    if (results.networkUpgrade && playerWon) {
        const upgradeBonus = Math.floor(totalWinnings * 0.5); // 50% upgrade bonus
        totalWinnings += upgradeBonus;
        resultMessage += ' + Network Upgrade!';
    }

    if (results.stakingBonus && playerWon) {
        const stakingBonus = Math.floor(totalWinnings * 0.4); // 40% staking bonus
        totalWinnings += stakingBonus;
        resultMessage += ' + Staking Bonus!';
    }

    if (results.liquidityEvent && playerWon) {
        const liquidityBonus = Math.floor(totalWinnings * 0.3); // 30% liquidity bonus
        totalWinnings += liquidityBonus;
        resultMessage += ' + Liquidity Bonus!';
    }

    // Apply Web3 bonuses (actually work)
    if (web3WheelGame.web3.decentralizationScore >= 0.80 && totalWinnings > 0) {
        const daoBonus = Math.floor(totalWinnings * WEB3_WHEEL_PAYOUTS.DAO_BONUS);
        totalWinnings += daoBonus;
        resultMessage += ' + DAO Bonus!';
    }

    // Apply staking bonus
    if (web3WheelGame.web3.stakingRewards >= 0.10 && totalWinnings > 0) {
        const stakingBonus = Math.floor(totalWinnings * WEB3_WHEEL_PAYOUTS.STAKING_BONUS);
        totalWinnings += stakingBonus;
        resultMessage += ' + Staking Rewards!';
    }

    // Apply liquidity bonus
    if (web3WheelGame.web3.liquidityPool >= 0.80 && totalWinnings > 0) {
        const liquidityBonus = Math.floor(totalWinnings * WEB3_WHEEL_PAYOUTS.LIQUIDITY_BONUS);
        totalWinnings += liquidityBonus;
        resultMessage += ' + Liquidity Bonus!';
    }

    // Apply consensus bonus
    if (results.consensusReached && totalWinnings > 0) {
        const consensusBonus = Math.floor(totalWinnings * WEB3_WHEEL_PAYOUTS.CONSENSUS_BONUS);
        totalWinnings += consensusBonus;
        resultMessage += ' + Consensus Bonus!';
    }

    // Apply blockchain multiplier
    totalWinnings = Math.floor(totalWinnings * blockchainData.payoutMultiplier);

    // Apply DAO governance multiplier
    const governanceMultiplier = 1.0 + (daoData.participationBonus * 0.5);
    totalWinnings = Math.floor(totalWinnings * governanceMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(web3WheelGame.betAmount * 0.7); // 70% return
        resultMessage = 'Web3 governance reward';
    }

    // Award Web3 points
    const pointsEarned = playerWon ? (results.networkUpgrade ? 10 : 6) : 3;
    web3WheelGame.stats.web3Points += pointsEarned;

    // Update DAO participation
    web3WheelGame.stats.daoParticipation++;
    web3WheelGame.stats.governanceVotes++;

    // Add winnings to balance
    balance += totalWinnings;
    web3WheelGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? 'Web3 wheel wins!' : 'No winning segment';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Blockchain: ${BLOCKCHAIN_NETWORKS[web3WheelGame.blockchain].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show Web3 event
    document.getElementById('web3Event').classList.remove('hidden');
    document.getElementById('web3Event').textContent = results.consensusReached ? 'CONSENSUS REACHED!' :
                                                      results.networkUpgrade ? 'NETWORK UPGRADE!' : 'WHEEL SPUN!';
    document.getElementById('web3Event').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update Web3 display
function updateWeb3Display() {
    updateWeb3Status();
    updateConsensusDisplay();
}

// Update Web3 status
function updateWeb3Status() {
    document.getElementById('gasPrice').innerHTML =
        `<div class="text-purple-400 font-bold">GAS: ${Math.round(web3WheelGame.web3.gasPrice)} GWEI</div>`;
    document.getElementById('blockHeight').innerHTML =
        `<div class="text-blue-400 font-bold">BLOCK: ${(web3WheelGame.web3.blockHeight / 1000000).toFixed(1)}M</div>`;
    document.getElementById('stakingRewards').innerHTML =
        `<div class="text-green-400 font-bold">STAKING: ${Math.round(web3WheelGame.web3.stakingRewards * 100)}%</div>`;
    document.getElementById('daoLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${web3WheelGame.web3.daoLevel.toUpperCase()}</div>`;

    document.getElementById('gasPriceDisplay').querySelector('.text-2xl').textContent =
        `${Math.round(web3WheelGame.web3.gasPrice)}`;

    document.getElementById('networkCongestion').querySelector('.text-2xl').textContent =
        `${Math.round(web3WheelGame.web3.networkCongestion * 100)}%`;

    document.getElementById('liquidityPool').querySelector('.text-2xl').textContent =
        `${Math.round(web3WheelGame.web3.liquidityPool * 100)}%`;
}

// Update consensus display
function updateConsensusDisplay() {
    document.getElementById('consensusVotes').textContent =
        `${Math.round(web3WheelGame.web3.consensusParticipation * 100)}%`;

    const daoData = DAO_GOVERNANCE[web3WheelGame.daoGovernance];
    document.getElementById('quorumStatus').textContent =
        `${Math.round(daoData.consensusThreshold * 100)}%`;

    document.getElementById('governanceTokens').textContent =
        `${web3WheelGame.web3.governanceTokens}`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${web3WheelGame.betAmount}`;
    document.getElementById('web3PointsDisplay').textContent = web3WheelGame.stats.web3Points;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = web3WheelGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${web3WheelGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${web3WheelGame.stats.totalWagered}`;
    document.getElementById('consensusReached').textContent = web3WheelGame.stats.consensusReached;
    document.getElementById('daoParticipation').textContent = web3WheelGame.stats.daoParticipation;

    const netResult = web3WheelGame.stats.totalWon - web3WheelGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-purple-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    web3WheelGame.stats.spinsPlayed++;
    web3WheelGame.stats.totalWagered += web3WheelGame.betAmount;
    web3WheelGame.stats.totalWon += winnings;

    if (won) {
        web3WheelGame.stats.spinsWon++;
        web3WheelGame.stats.currentStreak++;
        web3WheelGame.streakData.currentWinStreak++;
        web3WheelGame.streakData.currentLossStreak = 0;

        if (web3WheelGame.streakData.currentWinStreak > web3WheelGame.streakData.longestWinStreak) {
            web3WheelGame.streakData.longestWinStreak = web3WheelGame.streakData.currentWinStreak;
        }

        if (winnings > web3WheelGame.stats.biggestWin) {
            web3WheelGame.stats.biggestWin = winnings;
        }
    } else {
        web3WheelGame.stats.currentStreak = 0;
        web3WheelGame.streakData.currentWinStreak = 0;
        web3WheelGame.streakData.currentLossStreak++;

        if (web3WheelGame.streakData.currentLossStreak > web3WheelGame.streakData.longestLossStreak) {
            web3WheelGame.streakData.longestLossStreak = web3WheelGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to Web3 governance)
    web3WheelGame.stats.winRate = (web3WheelGame.stats.spinsWon / web3WheelGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next Web3 session
function resetGame() {
    web3WheelGame.isPlaying = false;
    web3WheelGame.betAmount = 0;
    web3WheelGame.totalBet = 0;
    web3WheelGame.gameResult = '';
    web3WheelGame.totalWin = 0;

    // Reset wheel system
    web3WheelGame.wheel.gamePhase = 'spinning';
    web3WheelGame.wheel.gameResult = '';
    web3WheelGame.wheel.consensusReached = false;
    web3WheelGame.wheel.governanceActive = false;
    web3WheelGame.wheel.stakingBonus = false;
    web3WheelGame.wheel.liquidityEvent = '';
    web3WheelGame.wheel.networkUpgrade = false;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('web3Event').classList.add('hidden');

    // Reset wheel rotation
    const wheelSvg = document.getElementById('web3WheelSvg');
    wheelSvg.style.transition = 'none';
    wheelSvg.style.transform = 'rotate(0deg)';
    web3WheelGame.wheel.currentRotation = 0;

    // Reset governance display
    document.getElementById('consensusStatus').textContent = 'Building consensus...';
    document.getElementById('governanceBar').style.width = '0%';
    document.getElementById('governanceStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable spin button
    document.getElementById('spinWeb3Wheel').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Web3 systems ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Web3 Wheel - Decentralized Jackpots with Community Governance';

    // Reinitialize systems for next session
    initializeWeb3Systems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadWeb3WheelGame();
});