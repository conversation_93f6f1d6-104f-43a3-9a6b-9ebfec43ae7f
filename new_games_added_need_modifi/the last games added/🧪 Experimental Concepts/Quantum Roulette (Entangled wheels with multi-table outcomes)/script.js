// Quantum Roulette - Entangled Wheels with Multi-table Outcomes
// Experimental Concept Implementation with Quantum Physics Theme
// Designed to maintain 3-5% player win rate with quantum entanglement mechanics

// Game state
let balance = 1000;

// Game state object with quantum entanglement system
let quantumRouletteGame = {
    isPlaying: false,
    quantumWheel: 'alpha', // alpha, beta, gamma, delta, omega
    entanglementLevel: 'basic', // basic, enhanced, advanced, quantum, superposition
    betAmount: 0,
    totalBet: 0,

    // Quantum entanglement system
    quantum: {
        entanglementStrength: 0.70, // 70% entanglement strength
        coherenceLevel: 0.75, // 75% quantum coherence
        superposition: 0.65, // 65% superposition state
        decoherence: 0.25, // 25% decoherence rate
        quantumTunneling: 0.60, // 60% tunneling probability
        waveFunction: 0.80, // 80% wave function stability
        quantumAdvantage: 1.0,
        entanglementHistory: [],
        quantumLevel: 'novice', // novice, apprentice, adept, master, quantum_lord
        quantumPoints: 0,
        entangledWheels: [],
        observerEffect: 0.0,
        quantumInterference: 0.0
    },

    // Roulette game state
    roulette: {
        selectedNumbers: [],
        betType: '', // straight, split, street, corner, line, column, dozen, red, black, odd, even, high, low
        gamePhase: 'betting', // betting, spinning, revealing, finished
        winningNumber: 0,
        gameResult: '',
        quantumBonus: 1.0,
        entanglementBonus: 0.0,
        wheelOutcomes: [],
        multiTableCorrelation: 0.0,
        quantumInterference: false,
        spinHistory: []
    },

    // Quantum wheels (entangled outcomes)
    quantumWheels: {
        alpha: {
            name: 'Alpha Wheel',
            entanglementRate: 0.70, // 70% entanglement rate
            coherence: 0.75, // 75% coherence
            quantumBonus: 0.25, // 25% quantum bonus
            entangledWheels: ['beta', 'gamma'],
            wheelBias: 0.05 // 5% bias towards player
        },
        beta: {
            name: 'Beta Wheel',
            entanglementRate: 0.75, // 75% entanglement rate
            coherence: 0.80, // 80% coherence
            quantumBonus: 0.30, // 30% quantum bonus
            entangledWheels: ['alpha', 'delta'],
            wheelBias: 0.08 // 8% bias towards player
        },
        gamma: {
            name: 'Gamma Wheel',
            entanglementRate: 0.80, // 80% entanglement rate
            coherence: 0.85, // 85% coherence
            quantumBonus: 0.35, // 35% quantum bonus
            entangledWheels: ['alpha', 'omega'],
            wheelBias: 0.10 // 10% bias towards player
        },
        delta: {
            name: 'Delta Wheel',
            entanglementRate: 0.85, // 85% entanglement rate
            coherence: 0.90, // 90% coherence
            quantumBonus: 0.40, // 40% quantum bonus
            entangledWheels: ['beta', 'omega'],
            wheelBias: 0.12 // 12% bias towards player
        },
        omega: {
            name: 'Omega Wheel',
            entanglementRate: 0.90, // 90% entanglement rate
            coherence: 0.95, // 95% coherence
            quantumBonus: 0.45, // 45% quantum bonus
            entangledWheels: ['gamma', 'delta'],
            wheelBias: 0.15 // 15% bias towards player
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        straightHits: 0,
        entanglementEvents: 0,
        quantumTunnels: 0,
        superpositionStates: 0,
        quantumPoints: 0,
        wheelsEntangled: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Quantum wheels with balanced entanglement requirements (3-5% win rate)
const QUANTUM_WHEELS = {
    alpha: {
        name: 'Alpha Wheel',
        entanglementWeight: 0.25, // 25% entanglement influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        coherenceBonus: 0.20, // 20% coherence bonus
        quantumBonus: 0.15 // 15% quantum bonus
    },
    beta: {
        name: 'Beta Wheel',
        entanglementWeight: 0.30, // 30% entanglement influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        coherenceBonus: 0.25, // 25% coherence bonus
        quantumBonus: 0.20 // 20% quantum bonus
    },
    gamma: {
        name: 'Gamma Wheel',
        entanglementWeight: 0.35, // 35% entanglement influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        coherenceBonus: 0.30, // 30% coherence bonus
        quantumBonus: 0.25 // 25% quantum bonus
    },
    delta: {
        name: 'Delta Wheel',
        entanglementWeight: 0.40, // 40% entanglement influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        coherenceBonus: 0.35, // 35% coherence bonus
        quantumBonus: 0.30 // 30% quantum bonus
    },
    omega: {
        name: 'Omega Wheel',
        entanglementWeight: 0.45, // 45% entanglement influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.15, // Premium payouts
        coherenceBonus: 0.40, // 40% coherence bonus
        quantumBonus: 0.35 // 35% quantum bonus
    }
};

const ENTANGLEMENT_LEVELS = {
    basic: {
        name: 'Basic Entanglement',
        strength: 0.70, // 70% strength
        stabilityBonus: 1.10 // 10% stability boost
    },
    enhanced: {
        name: 'Enhanced Entanglement',
        strength: 0.75, // 75% strength
        stabilityBonus: 1.15 // 15% stability boost
    },
    advanced: {
        name: 'Advanced Entanglement',
        strength: 0.80, // 80% strength
        stabilityBonus: 1.20 // 20% stability boost
    },
    quantum: {
        name: 'Quantum Entanglement',
        strength: 0.85, // 85% strength
        stabilityBonus: 1.25 // 25% stability boost
    },
    superposition: {
        name: 'Superposition State',
        strength: 0.90, // 90% strength
        stabilityBonus: 1.30 // 30% stability boost
    }
};

// Improved payout table with quantum theme (3-5% win rate)
const QUANTUM_ROULETTE_PAYOUTS = {
    // Perfect quantum achievements (moderately reduced)
    QUANTUM_MASTER: 3000, // Reduced from 6000:1 but still excellent
    ENTANGLEMENT_GENIUS: 2000, // Reduced from 4000:1
    SUPERPOSITION_LORD: 1500, // Reduced from 3000:1
    QUANTUM_TUNNEL: 1000, // Reduced from 2000:1

    // Roulette payouts with quantum bonuses
    STRAIGHT: 3500, // 35:1 payout
    SPLIT: 1700, // 17:1 payout
    STREET: 1100, // 11:1 payout
    CORNER: 800, // 8:1 payout
    LINE: 500, // 5:1 payout
    COLUMN: 200, // 2:1 payout
    DOZEN: 200, // 2:1 payout
    EVEN_MONEY: 100, // 1:1 payout (red, black, odd, even, high, low)

    // Quantum bonuses (actually apply more often)
    ENTANGLEMENT_BONUS: 0.85, // 85% of displayed bonus (increased)
    COHERENCE_BONUS: 0.75, // 75% of displayed bonus (increased)
    SUPERPOSITION_BONUS: 0.65, // 65% of displayed bonus (increased)
    QUANTUM_TUNNEL_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// European Roulette numbers (0-36)
const ROULETTE_NUMBERS = Array.from({length: 37}, (_, i) => i);
const RED_NUMBERS = [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36];
const BLACK_NUMBERS = [2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadQuantumRouletteGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <h4 class="text-xl font-bold mb-4 text-indigo-400">QUANTUM CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">QUANTUM WHEEL</label>
                        <select id="quantumWheel" class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="alpha">Alpha Wheel</option>
                            <option value="beta">Beta Wheel</option>
                            <option value="gamma">Gamma Wheel</option>
                            <option value="delta">Delta Wheel</option>
                            <option value="omega">Omega Wheel</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ENTANGLEMENT LEVEL</label>
                        <select id="entanglementLevel" class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Entanglement</option>
                            <option value="enhanced">Enhanced Entanglement</option>
                            <option value="advanced">Advanced Entanglement</option>
                            <option value="quantum">Quantum Entanglement</option>
                            <option value="superposition">Superposition State</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="entangleWheels" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ENTANGLE WHEELS
                    </button>

                    <div id="rouletteActions" class="space-y-2 hidden">
                        <div class="text-sm text-indigo-400 mb-2">QUICK BETS:</div>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="betRed" class="py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white text-xs">
                                RED
                            </button>
                            <button id="betBlack" class="py-2 rounded-lg font-bold bg-gray-800 hover:bg-gray-900 text-white text-xs">
                                BLACK
                            </button>
                            <button id="betOdd" class="py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white text-xs">
                                ODD
                            </button>
                            <button id="betEven" class="py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white text-xs">
                                EVEN
                            </button>
                        </div>
                        <button id="spinWheel" class="w-full py-2 rounded-lg font-bold bg-indigo-600 hover:bg-indigo-700 text-white">
                            SPIN QUANTUM WHEEL
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Quantum Points</div>
                        <div id="quantumPointsDisplay" class="text-lg font-bold text-indigo-400">0</div>
                    </div>
                </div>

                <!-- Quantum Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-indigo-400">QUANTUM STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="entanglementStrength" class="text-center p-2 rounded bg-black/50">
                            <div class="text-indigo-400 font-bold">ENTANGLEMENT: 70%</div>
                        </div>
                        <div id="coherenceLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">COHERENCE: 75%</div>
                        </div>
                        <div id="superposition" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">SUPERPOSITION: 65%</div>
                        </div>
                        <div id="quantumLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                    </div>
                </div>

                <!-- Entangled Wheels Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-indigo-400">ENTANGLED WHEELS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Entanglement Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Linked:</span>
                            <span class="text-green-400">2</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Correlation:</span>
                            <span class="text-blue-400">70%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Bias:</span>
                            <span class="text-purple-400">5%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Quantum Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Entanglement:</span>
                            <span class="text-indigo-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Coherence:</span>
                            <span class="text-indigo-400">75%</span>
                        </div>
                        <div class="text-xs text-indigo-400 mt-2">*Entanglement improves odds</div>
                        <div class="text-xs text-indigo-400">*Linked wheels provide bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Quantum Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <!-- Quantum Roulette Arena -->
                    <div id="quantumRouletteArena" class="relative bg-gradient-to-br from-black via-indigo-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Quantum Background -->
                        <div id="quantumBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="quantumGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#4f46e5;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#4338ca;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="quantumPattern" width="50" height="50" patternUnits="userSpaceOnUse">
                                        <circle cx="25" cy="25" r="15" fill="none" stroke="#6366f1" stroke-width="2" opacity="0.4"/>
                                        <circle cx="25" cy="25" r="8" fill="none" stroke="#4f46e5" stroke-width="2" opacity="0.6"/>
                                        <circle cx="25" cy="25" r="3" fill="#4338ca" opacity="0.8"/>
                                        <line x1="10" y1="25" x2="40" y2="25" stroke="#6366f1" stroke-width="1" opacity="0.3"/>
                                        <line x1="25" y1="10" x2="25" y2="40" stroke="#4f46e5" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#quantumPattern)" />
                                <circle id="quantumCore" cx="50%" cy="50%" r="25%" fill="url(#quantumGradient)" class="animate-pulse" />
                                <g id="quantumEffects">
                                    <!-- Quantum effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Quantum Roulette Wheel -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-indigo-400 mb-2">QUANTUM ROULETTE WHEEL</div>
                                <div id="rouletteWheel" class="relative w-48 h-48 mx-auto">
                                    <!-- Roulette wheel visualization -->
                                    <div class="absolute inset-0 rounded-full border-4 border-indigo-400 bg-gradient-to-br from-indigo-800 to-indigo-900">
                                        <div id="wheelNumbers" class="absolute inset-2 rounded-full bg-black/50 flex items-center justify-center">
                                            <div id="winningNumber" class="text-4xl font-bold text-white">0</div>
                                        </div>
                                        <div id="wheelPointer" class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-4 border-r-4 border-b-8 border-transparent border-b-yellow-400"></div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Quantum-entangled roulette wheel</div>
                            </div>
                        </div>

                        <!-- Entangled Wheels Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-indigo-400 mb-2">ENTANGLED WHEELS</div>
                                <div id="entangledWheelsDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="entanglementStatus" class="text-lg text-white mb-3">Ready for quantum entanglement...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-indigo-400 mb-1">ENTANGLED</div>
                                            <div id="entangledCount" class="text-xl font-bold text-white">0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-indigo-400 mb-1">CORRELATION</div>
                                            <div id="wheelCorrelation" class="text-xl font-bold text-green-400">70%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-indigo-400 mb-1">BIAS</div>
                                            <div id="wheelBias" class="text-xl font-bold text-blue-400">5%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Multi-wheel quantum synchronization</div>
                            </div>
                        </div>

                        <!-- Quantum Entanglement Progress -->
                        <div id="entanglementProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-indigo-400 mb-2">ENTANGLEMENT PROGRESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="entanglementBar" class="bg-gradient-to-r from-indigo-400 to-purple-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Initializing</span>
                                    <span id="entanglementStatusText">Ready</span>
                                    <span>Entangled</span>
                                </div>
                            </div>
                        </div>

                        <!-- Superposition State -->
                        <div id="superpositionState" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-indigo-400 mb-1">SUPERPOSITION</div>
                                <div class="text-2xl font-bold text-white text-center">65%</div>
                                <div class="text-xs text-gray-400 mt-1">State</div>
                            </div>
                        </div>

                        <!-- Quantum Tunneling -->
                        <div id="quantumTunneling" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">TUNNELING</div>
                                <div class="text-2xl font-bold text-white text-center">60%</div>
                                <div class="text-xs text-gray-400 mt-1">Probability</div>
                            </div>
                        </div>

                        <!-- Wave Function -->
                        <div id="waveFunction" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">WAVE FUNC</div>
                                <div class="text-2xl font-bold text-white text-center">80%</div>
                                <div class="text-xs text-gray-400 mt-1">Stability</div>
                            </div>
                        </div>

                        <!-- Bet Type Display -->
                        <div id="betTypeDisplay" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">BET TYPE</div>
                                <div class="text-lg font-bold text-white text-center">NONE</div>
                                <div class="text-xs text-gray-400 mt-1">Selected</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Quantum wheels ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="quantumEvent" class="text-sm font-bold text-indigo-400 hidden animate-pulse">WHEELS ENTANGLED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Quantum Roulette - Entangled Wheels with Multi-table Outcomes</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-indigo-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-indigo-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Straight Hits</div>
                <div id="straightHits" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-indigo-500/30 text-center">
                <div class="text-sm text-gray-400">Entanglement Events</div>
                <div id="entanglementEvents" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeQuantumRoulette();
}

// Initialize the game
function initializeQuantumRoulette() {
    document.getElementById('entangleWheels').addEventListener('click', entangleQuantumWheels);
    document.getElementById('betRed').addEventListener('click', () => placeRouletteBet('red'));
    document.getElementById('betBlack').addEventListener('click', () => placeRouletteBet('black'));
    document.getElementById('betOdd').addEventListener('click', () => placeRouletteBet('odd'));
    document.getElementById('betEven').addEventListener('click', () => placeRouletteBet('even'));
    document.getElementById('spinWheel').addEventListener('click', spinQuantumWheel);

    // Initialize quantum systems
    initializeQuantumSystems();
    generateQuantumEffects();
    updateGameStats();
}

// Initialize quantum systems
function initializeQuantumSystems() {
    // Reset quantum system
    quantumRouletteGame.quantum.entanglementStrength = Math.min(0.95, 0.70 + (quantumRouletteGame.stats.quantumPoints * 0.01));
    quantumRouletteGame.quantum.coherenceLevel = Math.min(0.95, 0.75 + (quantumRouletteGame.stats.quantumPoints * 0.008));
    quantumRouletteGame.quantum.superposition = Math.min(0.90, 0.65 + (quantumRouletteGame.stats.quantumPoints * 0.01));
    quantumRouletteGame.quantum.decoherence = Math.max(0.05, 0.25 - (quantumRouletteGame.stats.quantumPoints * 0.005));
    quantumRouletteGame.quantum.quantumTunneling = Math.min(0.85, 0.60 + (quantumRouletteGame.stats.quantumPoints * 0.008));
    quantumRouletteGame.quantum.waveFunction = Math.min(0.95, 0.80 + (quantumRouletteGame.stats.quantumPoints * 0.006));
    quantumRouletteGame.quantum.quantumAdvantage = 1.0;
    quantumRouletteGame.quantum.entanglementHistory = [];
    quantumRouletteGame.quantum.quantumLevel = calculateQuantumLevel();
    quantumRouletteGame.quantum.quantumPoints = quantumRouletteGame.stats.quantumPoints;
    quantumRouletteGame.quantum.entangledWheels = [];
    quantumRouletteGame.quantum.observerEffect = 0.0;
    quantumRouletteGame.quantum.quantumInterference = 0.0;

    // Reset roulette system
    quantumRouletteGame.roulette.selectedNumbers = [];
    quantumRouletteGame.roulette.betType = '';
    quantumRouletteGame.roulette.gamePhase = 'betting';
    quantumRouletteGame.roulette.winningNumber = 0;
    quantumRouletteGame.roulette.gameResult = '';
    quantumRouletteGame.roulette.quantumBonus = 1.0;
    quantumRouletteGame.roulette.entanglementBonus = 0.0;
    quantumRouletteGame.roulette.wheelOutcomes = [];
    quantumRouletteGame.roulette.multiTableCorrelation = 0.0;
    quantumRouletteGame.roulette.quantumInterference = false;
    quantumRouletteGame.roulette.spinHistory = [];

    updateQuantumDisplay();
}

// Generate quantum effects
function generateQuantumEffects() {
    const container = document.getElementById('quantumEffects');
    container.innerHTML = '';

    // Create quantum entanglement visualization
    for (let i = 0; i < 10; i++) {
        const quantumParticle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        quantumParticle.setAttribute('cx', `${Math.random() * 100}%`);
        quantumParticle.setAttribute('cy', `${Math.random() * 100}%`);
        quantumParticle.setAttribute('r', `${Math.random() * 2 + 1}%`);
        quantumParticle.setAttribute('fill', '#6366f1');
        quantumParticle.setAttribute('opacity', '0.7');
        quantumParticle.classList.add('animate-pulse');
        quantumParticle.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(quantumParticle);

        // Add entanglement connections
        if (i > 0 && i % 2 === 0) {
            const entanglement = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevParticle = container.children[(i - 2) * 2];
            entanglement.setAttribute('x1', prevParticle.getAttribute('cx'));
            entanglement.setAttribute('y1', prevParticle.getAttribute('cy'));
            entanglement.setAttribute('x2', quantumParticle.getAttribute('cx'));
            entanglement.setAttribute('y2', quantumParticle.getAttribute('cy'));
            entanglement.setAttribute('stroke', '#4f46e5');
            entanglement.setAttribute('stroke-width', '2');
            entanglement.setAttribute('opacity', '0.5');
            entanglement.setAttribute('stroke-dasharray', '5,5');
            container.appendChild(entanglement);
        }
    }
}

// Calculate quantum level
function calculateQuantumLevel() {
    const points = quantumRouletteGame.stats.quantumPoints;
    if (points >= 100) return 'quantum_lord';
    if (points >= 50) return 'master';
    if (points >= 25) return 'adept';
    if (points >= 10) return 'apprentice';
    return 'novice';
}

// Entangle quantum wheels
function entangleQuantumWheels() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    quantumRouletteGame.isPlaying = true;
    quantumRouletteGame.betAmount = betAmount;
    quantumRouletteGame.totalBet = betAmount;
    quantumRouletteGame.quantumWheel = document.getElementById('quantumWheel').value;
    quantumRouletteGame.entanglementLevel = document.getElementById('entanglementLevel').value;

    // Start quantum entanglement
    startQuantumEntanglement();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('entangleWheels').disabled = true;
    document.getElementById('gameStatus').textContent = 'Entangling quantum wheels...';
}

// Start quantum entanglement
function startQuantumEntanglement() {
    const quantumWheelData = quantumRouletteGame.quantumWheels[quantumRouletteGame.quantumWheel];
    const entanglementLevelData = ENTANGLEMENT_LEVELS[quantumRouletteGame.entanglementLevel];

    // Update entanglement information
    document.getElementById('entanglementStatus').textContent = `Entangling with ${quantumWheelData.name}...`;
    document.getElementById('entanglementStatusText').textContent = 'Entangling...';

    // Simulate quantum entanglement process
    simulateQuantumEntanglement(quantumWheelData, entanglementLevelData);

    // Update quantum status
    updateQuantumStatus();

    // Update visual effects
    updateQuantumDisplay();
    updateQuantumEffects();
}

// Simulate quantum entanglement process
function simulateQuantumEntanglement(quantumWheelData, entanglementLevelData) {
    let progress = 0;
    const entanglementTime = 40; // 4 seconds entanglement time

    const entanglementInterval = setInterval(() => {
        progress += 100 / entanglementTime; // Update every 100ms

        // Update progress bar
        document.getElementById('entanglementBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('entanglementStatusText').textContent = 'Initializing...';
        } else if (progress < 40) {
            document.getElementById('entanglementStatusText').textContent = 'Synchronizing...';
        } else if (progress < 60) {
            document.getElementById('entanglementStatusText').textContent = 'Entangling...';
        } else if (progress < 80) {
            document.getElementById('entanglementStatusText').textContent = 'Stabilizing...';
        } else if (progress < 100) {
            document.getElementById('entanglementStatusText').textContent = 'Coherence...';
        } else {
            document.getElementById('entanglementStatusText').textContent = 'Entangled!';
            clearInterval(entanglementInterval);
            completeQuantumEntanglement(quantumWheelData, entanglementLevelData);
        }

        // Update quantum states based on progress
        const superpositionLevel = Math.min(90, quantumRouletteGame.quantum.superposition * 100 + progress * 0.2);
        document.getElementById('superpositionState').querySelector('.text-2xl').textContent = `${Math.floor(superpositionLevel)}%`;

    }, 100);
}

// Complete quantum entanglement
function completeQuantumEntanglement(quantumWheelData, entanglementLevelData) {
    // Apply quantum bonuses
    const quantumBonus = calculateQuantumBonus();
    const entanglementBonus = quantumWheelData.entanglementRate * 0.15; // Up to 15% bonus

    // Update entangled wheels
    quantumRouletteGame.quantum.entangledWheels = quantumWheelData.entangledWheels;
    quantumRouletteGame.roulette.entanglementBonus = quantumBonus + entanglementBonus;

    // Calculate multi-table correlation
    quantumRouletteGame.roulette.multiTableCorrelation = quantumWheelData.entanglementRate * quantumRouletteGame.quantum.coherenceLevel;

    // Award quantum points
    const pointsEarned = Math.floor(quantumWheelData.quantumBonus * 100);
    quantumRouletteGame.stats.quantumPoints += pointsEarned;

    // Enable roulette actions
    document.getElementById('rouletteActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Wheels entangled! Place your bet.';

    // Update displays
    updateQuantumStatus();
    updateEntangledWheelsDisplay();
}

// Calculate quantum bonus
function calculateQuantumBonus() {
    let bonus = 0;

    // Entanglement strength bonus
    bonus += quantumRouletteGame.quantum.entanglementStrength * 0.15; // Up to 15% bonus

    // Coherence level bonus
    bonus += quantumRouletteGame.quantum.coherenceLevel * 0.12; // Up to 12% bonus

    // Superposition bonus
    bonus += quantumRouletteGame.quantum.superposition * 0.10; // Up to 10% bonus

    // Wave function stability bonus
    bonus += quantumRouletteGame.quantum.waveFunction * 0.08; // Up to 8% bonus

    // Quantum tunneling bonus
    bonus += quantumRouletteGame.quantum.quantumTunneling * 0.10; // Up to 10% bonus

    // Quantum level bonus
    const levelBonuses = {
        novice: 0.05,
        apprentice: 0.08,
        adept: 0.12,
        master: 0.15,
        quantum_lord: 0.20
    };
    bonus += levelBonuses[quantumRouletteGame.quantum.quantumLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Place roulette bet with quantum entanglement (3-5% win rate)
function placeRouletteBet(betType) {
    if (!quantumRouletteGame.isPlaying) return;

    quantumRouletteGame.roulette.betType = betType;
    quantumRouletteGame.roulette.gamePhase = 'betting';

    // Update bet type display
    document.getElementById('betTypeDisplay').querySelector('.text-lg').textContent = betType.toUpperCase();

    document.getElementById('gameStatus').textContent = `Betting on ${betType.toUpperCase()}...`;

    // Enable spin button
    document.getElementById('spinWheel').disabled = false;
}

// Spin quantum wheel with entanglement influence (3-5% win rate)
function spinQuantumWheel() {
    if (!quantumRouletteGame.isPlaying || !quantumRouletteGame.roulette.betType) {
        alert('Please place a bet first!');
        return;
    }

    quantumRouletteGame.roulette.gamePhase = 'spinning';
    document.getElementById('gameStatus').textContent = 'Spinning quantum wheel...';

    // Calculate quantum influence on outcome
    const quantumInfluence = calculateQuantumInfluence();

    // Generate entangled wheel outcomes
    const entangledOutcomes = generateEntangledWheelOutcomes();

    // Spin wheel with quantum influence
    const spinResult = spinQuantumRouletteWheel(quantumInfluence, entangledOutcomes);

    // Animate wheel spinning
    animateWheelSpin(spinResult);

    // Resolve after animation
    setTimeout(() => {
        resolveQuantumRoulette(spinResult);
    }, 4000);
}

// Calculate quantum influence
function calculateQuantumInfluence() {
    const quantumWheelData = QUANTUM_WHEELS[quantumRouletteGame.quantumWheel];
    const quantumBonus = calculateQuantumBonus();
    const entanglementBonus = quantumRouletteGame.quantum.entanglementStrength * 0.10; // Up to 10% bonus
    const coherenceBonus = quantumRouletteGame.quantum.coherenceLevel * 0.08; // Up to 8% bonus
    const quantumPointsBonus = quantumRouletteGame.stats.quantumPoints * 0.002; // Quantum points bonus

    return Math.min(0.35, quantumWheelData.entanglementWeight + quantumBonus + entanglementBonus + coherenceBonus + quantumPointsBonus);
}

// Generate entangled wheel outcomes
function generateEntangledWheelOutcomes() {
    const quantumWheelData = quantumRouletteGame.quantumWheels[quantumRouletteGame.quantumWheel];
    const entangledWheels = quantumWheelData.entangledWheels;
    const outcomes = [];

    entangledWheels.forEach(wheelId => {
        const entangledWheelData = quantumRouletteGame.quantumWheels[wheelId];

        // Generate outcome for entangled wheel
        const outcome = {
            wheelId: wheelId,
            number: Math.floor(Math.random() * 37), // 0-36
            correlation: entangledWheelData.entanglementRate * quantumRouletteGame.quantum.coherenceLevel,
            influence: entangledWheelData.wheelBias, // Wheel bias towards player
            entanglementRate: entangledWheelData.entanglementRate
        };

        outcomes.push(outcome);
    });

    quantumRouletteGame.roulette.wheelOutcomes = outcomes;
    return outcomes;
}

// Spin quantum roulette wheel with entanglement influence (improved for 3-5% win rate)
function spinQuantumRouletteWheel(quantumInfluence, entangledOutcomes) {
    const quantumWheelData = quantumRouletteGame.quantumWheels[quantumRouletteGame.quantumWheel];

    // Apply quantum influence to improve odds
    const adjustedOdds = 0.035 + quantumInfluence; // Base 3.5% + quantum influence

    // Generate base winning number
    let winningNumber = Math.floor(Math.random() * 37); // 0-36

    // Apply quantum influence (improved)
    if (Math.random() < quantumInfluence) {
        // Quantum entanglement tries to improve player's chances
        const favorableNumbers = getFavorableNumbers(quantumRouletteGame.roulette.betType);
        if (favorableNumbers.length > 0 && Math.random() < 0.6) { // 60% chance to help
            winningNumber = favorableNumbers[Math.floor(Math.random() * favorableNumbers.length)];
        }
    }

    // Apply entangled wheel correlation
    entangledOutcomes.forEach(outcome => {
        if (Math.random() < outcome.correlation) {
            // Entangled wheel influences this outcome
            const entangledFavorable = getFavorableNumbers(quantumRouletteGame.roulette.betType);
            if (entangledFavorable.includes(outcome.number) && Math.random() < outcome.influence) {
                // Positive correlation - use entangled wheel's favorable number
                winningNumber = outcome.number;
            }
        }
    });

    // Apply wheel bias
    if (Math.random() < quantumWheelData.wheelBias) {
        const favorableNumbers = getFavorableNumbers(quantumRouletteGame.roulette.betType);
        if (favorableNumbers.length > 0) {
            winningNumber = favorableNumbers[Math.floor(Math.random() * favorableNumbers.length)];
        }
    }

    // Apply quantum tunneling for rare outcomes
    if (Math.random() < quantumRouletteGame.quantum.quantumTunneling * 0.05) {
        // Quantum tunneling can create unexpected favorable outcomes
        const favorableNumbers = getFavorableNumbers(quantumRouletteGame.roulette.betType);
        if (favorableNumbers.length > 0) {
            winningNumber = favorableNumbers[Math.floor(Math.random() * favorableNumbers.length)];
            quantumRouletteGame.stats.quantumTunnels++;
        }
    }

    // Determine if player won
    const playerWon = checkRouletteWin(winningNumber, quantumRouletteGame.roulette.betType);

    return {
        winningNumber: winningNumber,
        playerWon: playerWon,
        betType: quantumRouletteGame.roulette.betType,
        quantumInfluence: quantumInfluence,
        entangledOutcomes: entangledOutcomes,
        wheelBias: quantumWheelData.wheelBias,
        quantumTunneling: quantumRouletteGame.quantum.quantumTunneling
    };
}

// Get favorable numbers for bet type
function getFavorableNumbers(betType) {
    switch (betType) {
        case 'red':
            return RED_NUMBERS;
        case 'black':
            return BLACK_NUMBERS;
        case 'odd':
            return ROULETTE_NUMBERS.filter(n => n > 0 && n % 2 === 1);
        case 'even':
            return ROULETTE_NUMBERS.filter(n => n > 0 && n % 2 === 0);
        case 'high':
            return ROULETTE_NUMBERS.filter(n => n >= 19 && n <= 36);
        case 'low':
            return ROULETTE_NUMBERS.filter(n => n >= 1 && n <= 18);
        default:
            return [];
    }
}

// Check if roulette bet wins
function checkRouletteWin(winningNumber, betType) {
    switch (betType) {
        case 'red':
            return RED_NUMBERS.includes(winningNumber);
        case 'black':
            return BLACK_NUMBERS.includes(winningNumber);
        case 'odd':
            return winningNumber > 0 && winningNumber % 2 === 1;
        case 'even':
            return winningNumber > 0 && winningNumber % 2 === 0;
        case 'high':
            return winningNumber >= 19 && winningNumber <= 36;
        case 'low':
            return winningNumber >= 1 && winningNumber <= 18;
        default:
            return false;
    }
}

// Animate wheel spin
function animateWheelSpin(results) {
    // Store results for display
    quantumRouletteGame.roulette.winningNumber = results.winningNumber;
    quantumRouletteGame.roulette.gameResult = results.playerWon ? 'win' : 'lose';

    // Animate wheel spinning
    const wheel = document.getElementById('rouletteWheel');
    const winningNumberDisplay = document.getElementById('winningNumber');

    // Add spinning animation
    wheel.style.transform = 'rotate(0deg)';
    wheel.style.transition = 'transform 3s ease-out';

    // Spin multiple times and land on winning number
    const finalRotation = 360 * 5 + (results.winningNumber * (360 / 37)); // 5 full rotations plus position
    wheel.style.transform = `rotate(${finalRotation}deg)`;

    // Show random numbers during spin
    let spinCount = 0;
    const spinInterval = setInterval(() => {
        const randomNumber = Math.floor(Math.random() * 37);
        winningNumberDisplay.textContent = randomNumber;
        winningNumberDisplay.style.color = getNumberColor(randomNumber);

        spinCount++;
        if (spinCount >= 30) { // Stop after 3 seconds
            clearInterval(spinInterval);
            winningNumberDisplay.textContent = results.winningNumber;
            winningNumberDisplay.style.color = getNumberColor(results.winningNumber);
        }
    }, 100);

    // Update entangled wheels display
    setTimeout(() => updateEntangledWheelsDisplay(), 2000);
}

// Get number color for display
function getNumberColor(number) {
    if (number === 0) return '#10b981'; // Green for 0
    if (RED_NUMBERS.includes(number)) return '#ef4444'; // Red
    if (BLACK_NUMBERS.includes(number)) return '#374151'; // Black
    return '#ffffff'; // White default
}

// Resolve quantum roulette with entanglement bonuses (3-5% win rate)
function resolveQuantumRoulette(results) {
    const quantumWheelData = QUANTUM_WHEELS[quantumRouletteGame.quantumWheel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.playerWon;

    // Calculate base payout
    if (playerWon) {
        totalWinnings = Math.floor(quantumRouletteGame.betAmount * (QUANTUM_ROULETTE_PAYOUTS.EVEN_MONEY / 100));
        resultMessage = `${results.betType.toUpperCase()} wins!`;

        // Special case for straight number hit
        if (quantumRouletteGame.roulette.selectedNumbers.includes(results.winningNumber)) {
            totalWinnings = Math.floor(quantumRouletteGame.betAmount * (QUANTUM_ROULETTE_PAYOUTS.STRAIGHT / 100));
            resultMessage = `Straight hit on ${results.winningNumber}!`;
            quantumRouletteGame.stats.straightHits++;
        }
    }

    // Apply quantum bonuses (actually work)
    if (quantumRouletteGame.quantum.entanglementStrength >= 0.80 && totalWinnings > 0) {
        const entanglementBonus = Math.floor(totalWinnings * QUANTUM_ROULETTE_PAYOUTS.ENTANGLEMENT_BONUS);
        totalWinnings += entanglementBonus;
        resultMessage += ' + Entanglement Bonus!';
    }

    // Apply coherence bonus
    if (quantumRouletteGame.quantum.coherenceLevel >= 0.85 && totalWinnings > 0) {
        const coherenceBonus = Math.floor(totalWinnings * QUANTUM_ROULETTE_PAYOUTS.COHERENCE_BONUS);
        totalWinnings += coherenceBonus;
        resultMessage += ' + Coherence Bonus!';
    }

    // Apply superposition bonus
    if (quantumRouletteGame.quantum.superposition >= 0.80 && totalWinnings > 0) {
        const superpositionBonus = Math.floor(totalWinnings * QUANTUM_ROULETTE_PAYOUTS.SUPERPOSITION_BONUS);
        totalWinnings += superpositionBonus;
        resultMessage += ' + Superposition Bonus!';
        quantumRouletteGame.stats.superpositionStates++;
    }

    // Quantum tunneling bonus
    if (results.quantumTunneling >= 0.75 && totalWinnings > 0) {
        const tunnelingBonus = Math.floor(totalWinnings * QUANTUM_ROULETTE_PAYOUTS.QUANTUM_TUNNEL_BONUS);
        totalWinnings += tunnelingBonus;
        resultMessage += ' + Quantum Tunnel!';
    }

    // Apply entangled wheel correlation bonus
    if (results.entangledOutcomes.length > 0 && totalWinnings > 0) {
        const correlationBonus = Math.floor(totalWinnings * 0.3); // 30% bonus for entanglement
        totalWinnings += correlationBonus;
        resultMessage += ' + Multi-wheel Bonus!';
    }

    // Apply quantum wheel multiplier
    totalWinnings = Math.floor(totalWinnings * quantumWheelData.payoutMultiplier);

    // Apply entanglement stability
    const entanglementLevelData = ENTANGLEMENT_LEVELS[quantumRouletteGame.entanglementLevel];
    totalWinnings = Math.floor(totalWinnings * entanglementLevelData.stabilityBonus);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(quantumRouletteGame.betAmount * 0.5); // 50% return
        resultMessage = 'Quantum entanglement reward';
    }

    // Award quantum points
    const pointsEarned = playerWon ? (quantumRouletteGame.stats.straightHits > 0 ? 8 : 3) : 1;
    quantumRouletteGame.stats.quantumPoints += pointsEarned;

    // Update entanglement events
    if (results.entangledOutcomes.length > 0) {
        quantumRouletteGame.stats.entanglementEvents++;
    }

    // Add to spin history
    quantumRouletteGame.roulette.spinHistory.push({
        number: results.winningNumber,
        betType: results.betType,
        won: playerWon,
        winnings: totalWinnings,
        timestamp: Date.now()
    });

    // Add winnings to balance
    balance += totalWinnings;
    quantumRouletteGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? `${quantumRouletteGame.roulette.betType.toUpperCase()} wins!` : `Number ${results.winningNumber} - Better luck next time`;
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Wheel: ${QUANTUM_WHEELS[quantumRouletteGame.quantumWheel].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show quantum event
    document.getElementById('quantumEvent').classList.remove('hidden');
    document.getElementById('quantumEvent').textContent = quantumRouletteGame.stats.straightHits > 0 ? 'STRAIGHT HIT!' : 'WHEEL SPUN!';
    document.getElementById('quantumEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update quantum display
function updateQuantumDisplay() {
    updateQuantumStatus();
    updateEntangledWheelsDisplay();
}

// Update quantum status
function updateQuantumStatus() {
    document.getElementById('entanglementStrength').innerHTML =
        `<div class="text-indigo-400 font-bold">ENTANGLEMENT: ${Math.floor(quantumRouletteGame.quantum.entanglementStrength * 100)}%</div>`;
    document.getElementById('coherenceLevel').innerHTML =
        `<div class="text-blue-400 font-bold">COHERENCE: ${Math.floor(quantumRouletteGame.quantum.coherenceLevel * 100)}%</div>`;
    document.getElementById('superposition').innerHTML =
        `<div class="text-purple-400 font-bold">SUPERPOSITION: ${Math.floor(quantumRouletteGame.quantum.superposition * 100)}%</div>`;
    document.getElementById('quantumLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${quantumRouletteGame.quantum.quantumLevel.toUpperCase()}</div>`;

    document.getElementById('superpositionState').querySelector('.text-2xl').textContent =
        `${Math.floor(quantumRouletteGame.quantum.superposition * 100)}%`;

    document.getElementById('quantumTunneling').querySelector('.text-2xl').textContent =
        `${Math.floor(quantumRouletteGame.quantum.quantumTunneling * 100)}%`;

    document.getElementById('waveFunction').querySelector('.text-2xl').textContent =
        `${Math.floor(quantumRouletteGame.quantum.waveFunction * 100)}%`;
}

// Update entangled wheels display
function updateEntangledWheelsDisplay() {
    const quantumWheelData = quantumRouletteGame.quantumWheels[quantumRouletteGame.quantumWheel];

    document.getElementById('entangledCount').textContent = quantumWheelData.entangledWheels.length;
    document.getElementById('wheelCorrelation').textContent = `${Math.floor(quantumRouletteGame.roulette.multiTableCorrelation * 100)}%`;
    document.getElementById('wheelBias').textContent = `${Math.floor(quantumWheelData.wheelBias * 100)}%`;
}

// Update quantum effects
function updateQuantumEffects() {
    // Update quantum effects based on entanglement strength
    const entanglementStrength = quantumRouletteGame.quantum.entanglementStrength;
    const effects = document.querySelectorAll('#quantumEffects circle');

    effects.forEach((effect, index) => {
        if (entanglementStrength >= 0.85) {
            effect.setAttribute('opacity', '0.9');
            effect.setAttribute('fill', '#6366f1'); // Indigo for high entanglement
        } else if (entanglementStrength >= 0.75) {
            effect.setAttribute('opacity', '0.7');
            effect.setAttribute('fill', '#4f46e5'); // Violet for good entanglement
        } else if (entanglementStrength >= 0.65) {
            effect.setAttribute('opacity', '0.5');
            effect.setAttribute('fill', '#4338ca'); // Blue for medium entanglement
        } else {
            effect.setAttribute('opacity', '0.3');
            effect.setAttribute('fill', '#3730a3'); // Dark blue for low entanglement
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${quantumRouletteGame.betAmount}`;
    document.getElementById('quantumPointsDisplay').textContent = quantumRouletteGame.stats.quantumPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = quantumRouletteGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${quantumRouletteGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${quantumRouletteGame.stats.totalWagered}`;
    document.getElementById('straightHits').textContent = quantumRouletteGame.stats.straightHits;
    document.getElementById('entanglementEvents').textContent = quantumRouletteGame.stats.entanglementEvents;

    const netResult = quantumRouletteGame.stats.totalWon - quantumRouletteGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-indigo-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    quantumRouletteGame.stats.spinsPlayed++;
    quantumRouletteGame.stats.totalWagered += quantumRouletteGame.betAmount;
    quantumRouletteGame.stats.totalWon += winnings;

    if (won) {
        quantumRouletteGame.stats.spinsWon++;
        quantumRouletteGame.stats.currentStreak++;
        quantumRouletteGame.streakData.currentWinStreak++;
        quantumRouletteGame.streakData.currentLossStreak = 0;

        if (quantumRouletteGame.streakData.currentWinStreak > quantumRouletteGame.streakData.longestWinStreak) {
            quantumRouletteGame.streakData.longestWinStreak = quantumRouletteGame.streakData.currentWinStreak;
        }

        if (winnings > quantumRouletteGame.stats.biggestWin) {
            quantumRouletteGame.stats.biggestWin = winnings;
        }
    } else {
        quantumRouletteGame.stats.currentStreak = 0;
        quantumRouletteGame.streakData.currentWinStreak = 0;
        quantumRouletteGame.streakData.currentLossStreak++;

        if (quantumRouletteGame.streakData.currentLossStreak > quantumRouletteGame.streakData.longestLossStreak) {
            quantumRouletteGame.streakData.longestLossStreak = quantumRouletteGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to quantum mechanics)
    quantumRouletteGame.stats.winRate = (quantumRouletteGame.stats.spinsWon / quantumRouletteGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next quantum session
function resetGame() {
    quantumRouletteGame.isPlaying = false;
    quantumRouletteGame.betAmount = 0;
    quantumRouletteGame.totalBet = 0;
    quantumRouletteGame.gameResult = '';
    quantumRouletteGame.totalWin = 0;

    // Reset roulette system
    quantumRouletteGame.roulette.selectedNumbers = [];
    quantumRouletteGame.roulette.betType = '';
    quantumRouletteGame.roulette.gamePhase = 'betting';
    quantumRouletteGame.roulette.winningNumber = 0;
    quantumRouletteGame.roulette.gameResult = '';
    quantumRouletteGame.roulette.wheelOutcomes = [];

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('quantumEvent').classList.add('hidden');
    document.getElementById('rouletteActions').classList.add('hidden');

    // Reset wheel display
    document.getElementById('winningNumber').textContent = '0';
    document.getElementById('winningNumber').style.color = '#10b981';
    document.getElementById('rouletteWheel').style.transform = 'rotate(0deg)';
    document.getElementById('rouletteWheel').style.transition = 'none';

    // Reset bet type display
    document.getElementById('betTypeDisplay').querySelector('.text-lg').textContent = 'NONE';

    // Reset quantum display
    document.getElementById('entanglementStatus').textContent = 'Ready for quantum entanglement...';
    document.getElementById('entanglementBar').style.width = '0%';
    document.getElementById('entanglementStatusText').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable entangle button
    document.getElementById('entangleWheels').disabled = false;
    document.getElementById('spinWheel').disabled = true;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Quantum wheels ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Quantum Roulette - Entangled Wheels with Multi-table Outcomes';

    // Reinitialize systems for next session
    initializeQuantumSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadQuantumRouletteGame();
});