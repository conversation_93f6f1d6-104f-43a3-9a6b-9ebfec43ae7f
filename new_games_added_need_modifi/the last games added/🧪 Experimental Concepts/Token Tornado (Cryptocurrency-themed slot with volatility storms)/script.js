// Token Tornado - Cryptocurrency-themed Slot with Volatility Storms
// Experimental Concept Implementation with Crypto Market Theme
// Designed to maintain 3-5% player win rate with volatility storm mechanics

// Game state
let balance = 1000;

// Game state object with cryptocurrency system
let tokenTornadoGame = {
    isPlaying: false,
    cryptoMarket: 'bitcoin', // bitcoin, ethereum, altcoins, defi, memecoins
    volatilityLevel: 'moderate', // low, moderate, high, extreme, storm
    betAmount: 0,
    totalBet: 0,

    // Cryptocurrency system
    crypto: {
        marketCap: 0.75, // 75% market cap strength
        volatility: 0.80, // 80% market volatility
        adoption: 0.65, // 65% adoption rate
        sentiment: 0.70, // 70% market sentiment
        liquidity: 0.85, // 85% market liquidity
        momentum: 0.60, // 60% market momentum
        cryptoAdvantage: 1.0,
        marketHistory: [],
        cryptoLevel: 'hodler', // hodler, trader, whale, crypto_lord, satoshi
        cryptoPoints: 0,
        portfolioValue: 0,
        stormIntensity: 0.25, // 25% storm intensity
        moonPhase: 0.30 // 30% moon phase
    },

    // Slot game state
    slots: {
        reels: [[], [], [], [], []],
        paylines: [],
        gamePhase: 'spinning', // spinning, revealing, storm, finished
        gameResult: '',
        volatilityBonus: 1.0,
        stormBonus: 0.0,
        spinHistory: [],
        stormActive: false,
        moonshot: false,
        rugPull: false,
        flashCrash: false,
        pumpAndDump: false
    },

    // Crypto markets
    cryptoMarkets: {
        bitcoin: {
            name: 'Bitcoin',
            volatility: 0.70, // 70% volatility
            stability: 0.85, // 85% stability
            moonPotential: 0.60, // 60% moon potential
            stormBonus: 0.25 // 25% storm bonus
        },
        ethereum: {
            name: 'Ethereum',
            volatility: 0.75, // 75% volatility
            stability: 0.80, // 80% stability
            moonPotential: 0.70, // 70% moon potential
            stormBonus: 0.30 // 30% storm bonus
        },
        altcoins: {
            name: 'Altcoins',
            volatility: 0.85, // 85% volatility
            stability: 0.60, // 60% stability
            moonPotential: 0.80, // 80% moon potential
            stormBonus: 0.35 // 35% storm bonus
        },
        defi: {
            name: 'DeFi Tokens',
            volatility: 0.90, // 90% volatility
            stability: 0.55, // 55% stability
            moonPotential: 0.85, // 85% moon potential
            stormBonus: 0.40 // 40% storm bonus
        },
        memecoins: {
            name: 'Meme Coins',
            volatility: 0.95, // 95% volatility
            stability: 0.30, // 30% stability
            moonPotential: 0.95, // 95% moon potential
            stormBonus: 0.45 // 45% storm bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        volatilityStorms: 0,
        moonshots: 0,
        rugPulls: 0,
        flashCrashes: 0,
        cryptoPoints: 0,
        portfolioValue: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Crypto markets with balanced volatility requirements (3-5% win rate)
const CRYPTO_MARKETS = {
    bitcoin: {
        name: 'Bitcoin',
        volatilityWeight: 0.25, // 25% volatility influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        stormBonus: 0.20, // 20% storm bonus
        moonBonus: 0.15 // 15% moon bonus
    },
    ethereum: {
        name: 'Ethereum',
        volatilityWeight: 0.30, // 30% volatility influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        stormBonus: 0.25, // 25% storm bonus
        moonBonus: 0.20 // 20% moon bonus
    },
    altcoins: {
        name: 'Altcoins',
        volatilityWeight: 0.35, // 35% volatility influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        stormBonus: 0.30, // 30% storm bonus
        moonBonus: 0.25 // 25% moon bonus
    },
    defi: {
        name: 'DeFi Tokens',
        volatilityWeight: 0.40, // 40% volatility influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        stormBonus: 0.35, // 35% storm bonus
        moonBonus: 0.30 // 30% moon bonus
    },
    memecoins: {
        name: 'Meme Coins',
        volatilityWeight: 0.45, // 45% volatility influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.15, // Premium payouts
        stormBonus: 0.40, // 40% storm bonus
        moonBonus: 0.35 // 35% moon bonus
    }
};

const VOLATILITY_LEVELS = {
    low: {
        name: 'Low Volatility',
        intensity: 0.30, // 30% intensity
        stormChance: 0.05, // 5% storm chance
        multiplier: 1.10 // 10% multiplier boost
    },
    moderate: {
        name: 'Moderate Volatility',
        intensity: 0.50, // 50% intensity
        stormChance: 0.10, // 10% storm chance
        multiplier: 1.15 // 15% multiplier boost
    },
    high: {
        name: 'High Volatility',
        intensity: 0.70, // 70% intensity
        stormChance: 0.15, // 15% storm chance
        multiplier: 1.20 // 20% multiplier boost
    },
    extreme: {
        name: 'Extreme Volatility',
        intensity: 0.85, // 85% intensity
        stormChance: 0.20, // 20% storm chance
        multiplier: 1.25 // 25% multiplier boost
    },
    storm: {
        name: 'Volatility Storm',
        intensity: 0.95, // 95% intensity
        stormChance: 0.30, // 30% storm chance
        multiplier: 1.30 // 30% multiplier boost
    }
};

// Improved payout table with crypto theme (3-5% win rate)
const TOKEN_TORNADO_PAYOUTS = {
    // Perfect crypto achievements (moderately reduced)
    SATOSHI_LEVEL: 5000, // Reduced from 10000:1 but still excellent
    CRYPTO_LORD: 3500, // Reduced from 7000:1
    WHALE_STATUS: 2500, // Reduced from 5000:1
    MOON_MISSION: 1800, // Reduced from 3600:1

    // Slot payouts with crypto bonuses
    FIVE_OF_A_KIND: 2000, // 20:1 payout
    FOUR_OF_A_KIND: 1000, // 10:1 payout
    THREE_OF_A_KIND: 500, // 5:1 payout
    TWO_PAIR: 200, // 2:1 payout
    PAIR: 100, // 1:1 payout

    // Crypto event bonuses
    VOLATILITY_STORM: 1500, // 15:1 payout for storm
    MOONSHOT: 1200, // 12:1 payout for moonshot
    FLASH_CRASH: 800, // 8:1 payout for crash prediction
    PUMP_AND_DUMP: 600, // 6:1 payout for pump detection

    // Crypto bonuses (actually apply more often)
    VOLATILITY_BONUS: 0.85, // 85% of displayed bonus (increased)
    STORM_BONUS: 0.75, // 75% of displayed bonus (increased)
    MOON_BONUS: 0.65, // 65% of displayed bonus (increased)
    ADOPTION_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// Crypto symbols for slots
const CRYPTO_SYMBOLS = ['₿', 'Ξ', '🚀', '💎', '🌙', '⚡', '🔥', '💰', '📈', '🎯'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadTokenTornadoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">CRYPTO CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CRYPTO MARKET</label>
                        <select id="cryptoMarket" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="bitcoin">Bitcoin</option>
                            <option value="ethereum">Ethereum</option>
                            <option value="altcoins">Altcoins</option>
                            <option value="defi">DeFi Tokens</option>
                            <option value="memecoins">Meme Coins</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VOLATILITY LEVEL</label>
                        <select id="volatilityLevel" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="low">Low Volatility</option>
                            <option value="moderate">Moderate Volatility</option>
                            <option value="high">High Volatility</option>
                            <option value="extreme">Extreme Volatility</option>
                            <option value="storm">Volatility Storm</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="spinReels" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        SPIN TORNADO
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Crypto Points</div>
                        <div id="cryptoPointsDisplay" class="text-lg font-bold text-orange-400">0</div>
                    </div>
                </div>

                <!-- Crypto Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">CRYPTO STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="marketCap" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">MARKET CAP: 75%</div>
                        </div>
                        <div id="volatilityStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">VOLATILITY: 80%</div>
                        </div>
                        <div id="adoption" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">ADOPTION: 65%</div>
                        </div>
                        <div id="cryptoLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: HODLER</div>
                        </div>
                    </div>
                </div>

                <!-- Volatility Storm Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-orange-400">VOLATILITY STORM</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Storm Indicators:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Storm Risk:</span>
                            <span class="text-red-400">25%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Moon Phase:</span>
                            <span class="text-blue-400">30%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Liquidity:</span>
                            <span class="text-green-400">85%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Crypto Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Volatility:</span>
                            <span class="text-orange-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Storm:</span>
                            <span class="text-orange-400">75%</span>
                        </div>
                        <div class="text-xs text-orange-400 mt-2">*Volatility improves payouts</div>
                        <div class="text-xs text-orange-400">*Storms provide mega bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Crypto Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <!-- Token Tornado Arena -->
                    <div id="tokenTornadoArena" class="relative bg-gradient-to-br from-black via-orange-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Crypto Background -->
                        <div id="cryptoBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="cryptoGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#f97316;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#ea580c;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#c2410c;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="cryptoPattern" width="25" height="25" patternUnits="userSpaceOnUse">
                                        <circle cx="12.5" cy="12.5" r="8" fill="none" stroke="#f97316" stroke-width="1" opacity="0.4"/>
                                        <circle cx="12.5" cy="12.5" r="4" fill="none" stroke="#ea580c" stroke-width="1" opacity="0.6"/>
                                        <circle cx="12.5" cy="12.5" r="2" fill="#c2410c" opacity="0.8"/>
                                        <path d="M5,12.5 Q12.5,5 20,12.5 Q12.5,20 5,12.5" fill="none" stroke="#f97316" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#cryptoPattern)" />
                                <circle id="cryptoCore" cx="50%" cy="50%" r="25%" fill="url(#cryptoGradient)" class="animate-pulse" />
                                <g id="cryptoEffects">
                                    <!-- Crypto effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Slot Machine -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-orange-400 mb-2">TOKEN TORNADO SLOTS</div>
                                <div id="slotMachine" class="bg-black/70 rounded-lg p-4 w-80 border-2 border-orange-600">
                                    <div class="grid grid-cols-5 gap-2 mb-4">
                                        <!-- Slot Reels -->
                                        <div id="reel1" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">₿</div>
                                        </div>
                                        <div id="reel2" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">Ξ</div>
                                        </div>
                                        <div id="reel3" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">🚀</div>
                                        </div>
                                        <div id="reel4" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">💎</div>
                                        </div>
                                        <div id="reel5" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">🌙</div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-400">Paylines:</span>
                                        <span id="paylines" class="text-orange-400">5</span>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Crypto-powered slot machine</div>
                            </div>
                        </div>

                        <!-- Volatility Storm Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-orange-400 mb-2">VOLATILITY STORM</div>
                                <div id="volatilityStormDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="stormStatus" class="text-lg text-white mb-3">Storm brewing...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-orange-400 mb-1">INTENSITY</div>
                                            <div id="stormIntensity" class="text-xl font-bold text-white">25%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-orange-400 mb-1">MULTIPLIER</div>
                                            <div id="stormMultiplier" class="text-xl font-bold text-green-400">1.0x</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-orange-400 mb-1">MOON PHASE</div>
                                            <div id="moonPhase" class="text-xl font-bold text-blue-400">30%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Cryptocurrency volatility simulation</div>
                            </div>
                        </div>

                        <!-- Storm Progress -->
                        <div id="stormProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-orange-400 mb-2">STORM FORMATION</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="stormBar" class="bg-gradient-to-r from-orange-400 to-red-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Calm</span>
                                    <span id="stormStatusText">Building</span>
                                    <span>Storm</span>
                                </div>
                            </div>
                        </div>

                        <!-- Market Cap -->
                        <div id="marketCapDisplay" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-orange-400 mb-1">MARKET CAP</div>
                                <div class="text-2xl font-bold text-white text-center">75%</div>
                                <div class="text-xs text-gray-400 mt-1">Strength</div>
                            </div>
                        </div>

                        <!-- Volatility Level -->
                        <div id="volatilityDisplay" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">VOLATILITY</div>
                                <div class="text-2xl font-bold text-white text-center">80%</div>
                                <div class="text-xs text-gray-400 mt-1">Level</div>
                            </div>
                        </div>

                        <!-- Adoption Rate -->
                        <div id="adoptionDisplay" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">ADOPTION</div>
                                <div class="text-2xl font-bold text-white text-center">65%</div>
                                <div class="text-xs text-gray-400 mt-1">Rate</div>
                            </div>
                        </div>

                        <!-- Selected Market -->
                        <div id="selectedMarket" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">MARKET</div>
                                <div class="text-lg font-bold text-white text-center">BTC</div>
                                <div class="text-xs text-gray-400 mt-1">Selected</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Ready to spin tornado...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="cryptoEvent" class="text-sm font-bold text-orange-400 hidden animate-pulse">STORM INCOMING!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Token Tornado - Cryptocurrency-themed Slot with Volatility Storms</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-orange-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-orange-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Volatility Storms</div>
                <div id="volatilityStorms" class="text-xl font-bold text-red-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 text-center">
                <div class="text-sm text-gray-400">Moonshots</div>
                <div id="moonshots" class="text-xl font-bold text-blue-400">0</div>
            </div>
        </div>
    `;

    initializeTokenTornado();
}

// Initialize the game
function initializeTokenTornado() {
    document.getElementById('spinReels').addEventListener('click', spinTokenTornado);

    // Initialize crypto systems
    initializeCryptoSystems();
    generateCryptoEffects();
    updateGameStats();
}

// Initialize crypto systems
function initializeCryptoSystems() {
    // Reset crypto system
    tokenTornadoGame.crypto.marketCap = Math.min(0.95, 0.75 + (tokenTornadoGame.stats.cryptoPoints * 0.008));
    tokenTornadoGame.crypto.volatility = Math.min(0.98, 0.80 + (tokenTornadoGame.stats.cryptoPoints * 0.006));
    tokenTornadoGame.crypto.adoption = Math.min(0.90, 0.65 + (tokenTornadoGame.stats.cryptoPoints * 0.01));
    tokenTornadoGame.crypto.sentiment = Math.min(0.95, 0.70 + (tokenTornadoGame.stats.cryptoPoints * 0.008));
    tokenTornadoGame.crypto.liquidity = Math.min(0.98, 0.85 + (tokenTornadoGame.stats.cryptoPoints * 0.005));
    tokenTornadoGame.crypto.momentum = Math.min(0.90, 0.60 + (tokenTornadoGame.stats.cryptoPoints * 0.01));
    tokenTornadoGame.crypto.cryptoAdvantage = 1.0;
    tokenTornadoGame.crypto.marketHistory = [];
    tokenTornadoGame.crypto.cryptoLevel = calculateCryptoLevel();
    tokenTornadoGame.crypto.cryptoPoints = tokenTornadoGame.stats.cryptoPoints;
    tokenTornadoGame.crypto.portfolioValue = 0;
    tokenTornadoGame.crypto.stormIntensity = Math.max(0.15, 0.25 - (tokenTornadoGame.stats.cryptoPoints * 0.002));
    tokenTornadoGame.crypto.moonPhase = Math.min(0.80, 0.30 + (tokenTornadoGame.stats.cryptoPoints * 0.008));

    // Reset slot system
    tokenTornadoGame.slots.reels = [[], [], [], [], []];
    tokenTornadoGame.slots.paylines = [];
    tokenTornadoGame.slots.gamePhase = 'spinning';
    tokenTornadoGame.slots.gameResult = '';
    tokenTornadoGame.slots.volatilityBonus = 1.0;
    tokenTornadoGame.slots.stormBonus = 0.0;
    tokenTornadoGame.slots.spinHistory = [];
    tokenTornadoGame.slots.stormActive = false;
    tokenTornadoGame.slots.moonshot = false;
    tokenTornadoGame.slots.rugPull = false;
    tokenTornadoGame.slots.flashCrash = false;
    tokenTornadoGame.slots.pumpAndDump = false;

    updateCryptoDisplay();
}

// Generate crypto effects
function generateCryptoEffects() {
    const container = document.getElementById('cryptoEffects');
    container.innerHTML = '';

    // Create crypto visualization
    for (let i = 0; i < 15; i++) {
        const cryptoNode = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        cryptoNode.setAttribute('cx', `${Math.random() * 100}%`);
        cryptoNode.setAttribute('cy', `${Math.random() * 100}%`);
        cryptoNode.setAttribute('r', `${Math.random() * 1.5 + 0.5}%`);
        cryptoNode.setAttribute('fill', '#f97316');
        cryptoNode.setAttribute('opacity', '0.7');
        cryptoNode.classList.add('animate-pulse');
        cryptoNode.style.animationDelay = `${i * 0.15}s`;
        container.appendChild(cryptoNode);

        // Add crypto connections
        if (i > 0 && i % 4 === 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 4) * 2];
            if (prevNode) {
                connection.setAttribute('x1', prevNode.getAttribute('cx'));
                connection.setAttribute('y1', prevNode.getAttribute('cy'));
                connection.setAttribute('x2', cryptoNode.getAttribute('cx'));
                connection.setAttribute('y2', cryptoNode.getAttribute('cy'));
                connection.setAttribute('stroke', '#ea580c');
                connection.setAttribute('stroke-width', '1');
                connection.setAttribute('opacity', '0.5');
                connection.setAttribute('stroke-dasharray', '2,2');
                container.appendChild(connection);
            }
        }
    }
}

// Calculate crypto level
function calculateCryptoLevel() {
    const points = tokenTornadoGame.stats.cryptoPoints;
    if (points >= 100) return 'satoshi';
    if (points >= 50) return 'crypto_lord';
    if (points >= 25) return 'whale';
    if (points >= 10) return 'trader';
    return 'hodler';
}

// Spin token tornado with volatility storms (3-5% win rate)
function spinTokenTornado() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    tokenTornadoGame.isPlaying = true;
    tokenTornadoGame.betAmount = betAmount;
    tokenTornadoGame.totalBet = betAmount;
    tokenTornadoGame.cryptoMarket = document.getElementById('cryptoMarket').value;
    tokenTornadoGame.volatilityLevel = document.getElementById('volatilityLevel').value;

    // Start volatility storm formation
    startVolatilityStorm();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('spinReels').disabled = true;
    document.getElementById('gameStatus').textContent = 'Forming volatility storm...';
}

// Start volatility storm
function startVolatilityStorm() {
    const cryptoMarketData = tokenTornadoGame.cryptoMarkets[tokenTornadoGame.cryptoMarket];
    const volatilityLevelData = VOLATILITY_LEVELS[tokenTornadoGame.volatilityLevel];

    // Update storm information
    document.getElementById('stormStatus').textContent = `Storm forming in ${cryptoMarketData.name} market...`;
    document.getElementById('stormStatusText').textContent = 'Building...';

    // Simulate storm formation process
    simulateStormFormation(cryptoMarketData, volatilityLevelData);

    // Update crypto status
    updateCryptoStatus();

    // Update visual effects
    updateCryptoDisplay();
}

// Simulate storm formation process
function simulateStormFormation(cryptoMarketData, volatilityLevelData) {
    let progress = 0;
    const stormTime = 35; // 3.5 seconds storm formation time

    const stormInterval = setInterval(() => {
        progress += 100 / stormTime; // Update every 100ms

        // Update progress bar
        document.getElementById('stormBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('stormStatusText').textContent = 'Building...';
        } else if (progress < 40) {
            document.getElementById('stormStatusText').textContent = 'Intensifying...';
        } else if (progress < 60) {
            document.getElementById('stormStatusText').textContent = 'Swirling...';
        } else if (progress < 80) {
            document.getElementById('stormStatusText').textContent = 'Forming...';
        } else if (progress < 100) {
            document.getElementById('stormStatusText').textContent = 'Ready...';
        } else {
            document.getElementById('stormStatusText').textContent = 'Storm!';
            clearInterval(stormInterval);
            completeStormFormation(cryptoMarketData, volatilityLevelData);
        }

        // Update storm intensity based on progress
        const intensityLevel = Math.min(95, tokenTornadoGame.crypto.stormIntensity * 100 + progress * 0.3);
        document.getElementById('stormIntensity').textContent = `${Math.floor(intensityLevel)}%`;

    }, 100);
}

// Complete storm formation
function completeStormFormation(cryptoMarketData, volatilityLevelData) {
    // Apply crypto bonuses
    const cryptoBonus = calculateCryptoBonus();
    const volatilityBonus = cryptoMarketData.volatility * 0.15; // Up to 15% bonus

    // Update volatility advantage
    tokenTornadoGame.slots.volatilityBonus = 1.0 + cryptoBonus + volatilityBonus;

    // Calculate storm bonus
    tokenTornadoGame.slots.stormBonus = cryptoMarketData.stormBonus * tokenTornadoGame.crypto.volatility;

    // Award crypto points
    const pointsEarned = Math.floor(cryptoMarketData.stormBonus * 100);
    tokenTornadoGame.stats.cryptoPoints += pointsEarned;

    // Check for volatility storm
    if (Math.random() < volatilityLevelData.stormChance) {
        tokenTornadoGame.slots.stormActive = true;
        tokenTornadoGame.stats.volatilityStorms++;
        document.getElementById('gameStatus').textContent = 'VOLATILITY STORM ACTIVE!';
    } else {
        document.getElementById('gameStatus').textContent = 'Storm formed! Spinning reels...';
    }

    // Spin the reels
    setTimeout(() => spinCryptoReels(), 1000);

    // Update displays
    updateCryptoStatus();
    updateStormDisplay();

    // Update selected market display
    document.getElementById('selectedMarket').querySelector('.text-lg').textContent =
        tokenTornadoGame.cryptoMarket.toUpperCase().substring(0, 3);
}

// Calculate crypto bonus
function calculateCryptoBonus() {
    let bonus = 0;

    // Market cap bonus
    bonus += tokenTornadoGame.crypto.marketCap * 0.12; // Up to 12% bonus

    // Volatility bonus
    bonus += tokenTornadoGame.crypto.volatility * 0.15; // Up to 15% bonus

    // Adoption bonus
    bonus += tokenTornadoGame.crypto.adoption * 0.10; // Up to 10% bonus

    // Sentiment bonus
    bonus += tokenTornadoGame.crypto.sentiment * 0.08; // Up to 8% bonus

    // Liquidity bonus
    bonus += tokenTornadoGame.crypto.liquidity * 0.06; // Up to 6% bonus

    // Momentum bonus
    bonus += tokenTornadoGame.crypto.momentum * 0.10; // Up to 10% bonus

    // Crypto level bonus
    const levelBonuses = {
        hodler: 0.05,
        trader: 0.08,
        whale: 0.12,
        crypto_lord: 0.15,
        satoshi: 0.20
    };
    bonus += levelBonuses[tokenTornadoGame.crypto.cryptoLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Spin crypto reels with volatility influence (3-5% win rate)
function spinCryptoReels() {
    // Calculate crypto influence on outcome
    const cryptoInfluence = calculateCryptoInfluence();

    // Generate slot results with crypto influence
    const slotResults = generateCryptoSlotResults(cryptoInfluence);

    // Animate reel spinning
    animateReelSpinning(slotResults);

    // Resolve after animation
    setTimeout(() => {
        resolveTokenTornado(slotResults);
    }, 4000);
}

// Calculate crypto influence
function calculateCryptoInfluence() {
    const cryptoMarketData = CRYPTO_MARKETS[tokenTornadoGame.cryptoMarket];
    const cryptoBonus = calculateCryptoBonus();
    const volatilityBonus = tokenTornadoGame.crypto.volatility * 0.10; // Up to 10% bonus
    const adoptionBonus = tokenTornadoGame.crypto.adoption * 0.08; // Up to 8% bonus
    const cryptoPointsBonus = tokenTornadoGame.stats.cryptoPoints * 0.002; // Crypto points bonus

    return Math.min(0.35, cryptoMarketData.volatilityWeight + cryptoBonus + volatilityBonus + adoptionBonus + cryptoPointsBonus);
}

// Generate crypto slot results with volatility influence (improved for 3-5% win rate)
function generateCryptoSlotResults(cryptoInfluence) {
    const cryptoMarketData = tokenTornadoGame.cryptoMarkets[tokenTornadoGame.cryptoMarket];
    const volatilityLevelData = VOLATILITY_LEVELS[tokenTornadoGame.volatilityLevel];

    // Apply crypto influence to improve odds
    const adjustedOdds = 0.035 + cryptoInfluence; // Base 3.5% + crypto influence

    // Generate base reel results
    let reels = [];
    for (let i = 0; i < 5; i++) {
        reels.push(CRYPTO_SYMBOLS[Math.floor(Math.random() * CRYPTO_SYMBOLS.length)]);
    }

    // Apply crypto influence (improved)
    if (Math.random() < cryptoInfluence) {
        // Crypto analysis tries to improve player's chances
        const targetSymbol = CRYPTO_SYMBOLS[Math.floor(Math.random() * CRYPTO_SYMBOLS.length)];

        // Try to create matches
        if (Math.random() < 0.6) { // 60% chance to help
            const matchCount = Math.floor(Math.random() * 3) + 2; // 2-4 matches
            for (let i = 0; i < matchCount && i < 5; i++) {
                reels[i] = targetSymbol;
            }
        }
    }

    // Apply volatility storm effects
    if (tokenTornadoGame.slots.stormActive) {
        // Storm can create better combinations
        if (Math.random() < 0.4) { // 40% chance for storm improvement
            const stormSymbol = ['🚀', '💎', '🌙'][Math.floor(Math.random() * 3)]; // Premium symbols
            const stormMatches = Math.floor(Math.random() * 2) + 3; // 3-4 matches
            for (let i = 0; i < stormMatches; i++) {
                reels[i] = stormSymbol;
            }
        }
    }

    // Check for special crypto events
    let moonshot = false;
    let rugPull = false;
    let flashCrash = false;
    let pumpAndDump = false;

    // Moonshot event
    if (Math.random() < tokenTornadoGame.crypto.moonPhase * 0.1) {
        moonshot = true;
        reels = ['🚀', '🚀', '🚀', '🚀', '🚀']; // All rockets
        tokenTornadoGame.stats.moonshots++;
    }

    // Flash crash event (rare but can be profitable if predicted)
    if (Math.random() < 0.02 && !moonshot) {
        flashCrash = true;
        tokenTornadoGame.stats.flashCrashes++;
    }

    // Pump and dump detection
    if (Math.random() < 0.03 && !moonshot && !flashCrash) {
        pumpAndDump = true;
    }

    // Evaluate winning combinations
    const winningCombination = evaluateSlotCombination(reels);

    return {
        reels: reels,
        winningCombination: winningCombination,
        cryptoInfluence: cryptoInfluence,
        stormActive: tokenTornadoGame.slots.stormActive,
        moonshot: moonshot,
        rugPull: rugPull,
        flashCrash: flashCrash,
        pumpAndDump: pumpAndDump,
        volatility: cryptoMarketData.volatility,
        moonPhase: tokenTornadoGame.crypto.moonPhase
    };
}

// Evaluate slot combination
function evaluateSlotCombination(reels) {
    const symbolCounts = {};
    reels.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    const counts = Object.values(symbolCounts).sort((a, b) => b - a);
    const maxCount = counts[0];

    if (maxCount === 5) {
        return { type: 'FIVE_OF_A_KIND', count: 5, symbol: Object.keys(symbolCounts)[0] };
    } else if (maxCount === 4) {
        return { type: 'FOUR_OF_A_KIND', count: 4, symbol: Object.keys(symbolCounts).find(k => symbolCounts[k] === 4) };
    } else if (maxCount === 3) {
        return { type: 'THREE_OF_A_KIND', count: 3, symbol: Object.keys(symbolCounts).find(k => symbolCounts[k] === 3) };
    } else if (counts[0] === 2 && counts[1] === 2) {
        return { type: 'TWO_PAIR', count: 2, symbol: 'multiple' };
    } else if (maxCount === 2) {
        return { type: 'PAIR', count: 2, symbol: Object.keys(symbolCounts).find(k => symbolCounts[k] === 2) };
    } else {
        return { type: 'NO_WIN', count: 0, symbol: 'none' };
    }
}

// Animate reel spinning
function animateReelSpinning(results) {
    // Store results for display
    tokenTornadoGame.slots.reels = results.reels;
    tokenTornadoGame.slots.gameResult = results.winningCombination.type;
    tokenTornadoGame.slots.moonshot = results.moonshot;
    tokenTornadoGame.slots.flashCrash = results.flashCrash;
    tokenTornadoGame.slots.pumpAndDump = results.pumpAndDump;

    // Animate each reel with delays
    const reelElements = ['reel1', 'reel2', 'reel3', 'reel4', 'reel5'];

    reelElements.forEach((reelId, index) => {
        const reel = document.getElementById(reelId);
        const symbolElement = reel.querySelector('div');

        // Add spinning animation
        reel.style.transform = 'rotateX(0deg)';
        reel.style.transition = `transform ${1 + index * 0.2}s ease-out`;
        reel.style.transform = 'rotateX(720deg)'; // 2 full rotations

        // Show random symbols during spin
        let spinCount = 0;
        const spinInterval = setInterval(() => {
            const randomSymbol = CRYPTO_SYMBOLS[Math.floor(Math.random() * CRYPTO_SYMBOLS.length)];
            symbolElement.textContent = randomSymbol;

            spinCount++;
            if (spinCount >= (10 + index * 3)) { // Stop at different times
                clearInterval(spinInterval);
                symbolElement.textContent = results.reels[index];

                // Add special effects for winning symbols
                if (results.winningCombination.type !== 'NO_WIN' &&
                    (results.reels[index] === results.winningCombination.symbol || results.winningCombination.symbol === 'multiple')) {
                    reel.style.boxShadow = '0 0 20px #f97316';
                    reel.style.borderColor = '#f97316';
                }
            }
        }, 100);
    });

    // Update storm display
    setTimeout(() => updateStormDisplay(), 2000);
}

// Resolve token tornado with volatility bonuses (3-5% win rate)
function resolveTokenTornado(results) {
    const cryptoMarketData = CRYPTO_MARKETS[tokenTornadoGame.cryptoMarket];
    const volatilityLevelData = VOLATILITY_LEVELS[tokenTornadoGame.volatilityLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.winningCombination.type !== 'NO_WIN';

    // Calculate base payout based on combination
    if (playerWon) {
        const basePayout = TOKEN_TORNADO_PAYOUTS[results.winningCombination.type] || 0;
        totalWinnings = Math.floor(tokenTornadoGame.betAmount * (basePayout / 100));
        resultMessage = `${results.winningCombination.type.replace(/_/g, ' ')} wins!`;
    }

    // Special event payouts
    if (results.moonshot) {
        totalWinnings = Math.floor(tokenTornadoGame.betAmount * (TOKEN_TORNADO_PAYOUTS.MOONSHOT / 100));
        resultMessage = 'MOONSHOT! 🚀';
    } else if (results.flashCrash && playerWon) {
        const crashBonus = Math.floor(totalWinnings * 0.5); // 50% crash bonus
        totalWinnings += crashBonus;
        resultMessage += ' + Flash Crash Bonus!';
    } else if (results.pumpAndDump && playerWon) {
        const pumpBonus = Math.floor(totalWinnings * 0.3); // 30% pump bonus
        totalWinnings += pumpBonus;
        resultMessage += ' + Pump Detection!';
    }

    // Apply volatility bonuses (actually work)
    if (tokenTornadoGame.crypto.volatility >= 0.85 && totalWinnings > 0) {
        const volatilityBonus = Math.floor(totalWinnings * TOKEN_TORNADO_PAYOUTS.VOLATILITY_BONUS);
        totalWinnings += volatilityBonus;
        resultMessage += ' + Volatility Bonus!';
    }

    // Apply storm bonus
    if (tokenTornadoGame.slots.stormActive && totalWinnings > 0) {
        const stormBonus = Math.floor(totalWinnings * TOKEN_TORNADO_PAYOUTS.STORM_BONUS);
        totalWinnings += stormBonus;
        resultMessage += ' + Storm Bonus!';
    }

    // Apply moon bonus
    if (tokenTornadoGame.crypto.moonPhase >= 0.70 && totalWinnings > 0) {
        const moonBonus = Math.floor(totalWinnings * TOKEN_TORNADO_PAYOUTS.MOON_BONUS);
        totalWinnings += moonBonus;
        resultMessage += ' + Moon Bonus!';
    }

    // Apply adoption bonus
    if (tokenTornadoGame.crypto.adoption >= 0.80 && totalWinnings > 0) {
        const adoptionBonus = Math.floor(totalWinnings * TOKEN_TORNADO_PAYOUTS.ADOPTION_BONUS);
        totalWinnings += adoptionBonus;
        resultMessage += ' + Adoption Bonus!';
    }

    // Apply crypto market multiplier
    totalWinnings = Math.floor(totalWinnings * cryptoMarketData.payoutMultiplier);

    // Apply volatility level multiplier
    totalWinnings = Math.floor(totalWinnings * volatilityLevelData.multiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(tokenTornadoGame.betAmount * 0.5); // 50% return
        resultMessage = 'Crypto volatility reward';
    }

    // Award crypto points
    const pointsEarned = playerWon ? (results.moonshot ? 10 : 5) : 2;
    tokenTornadoGame.stats.cryptoPoints += pointsEarned;

    // Update portfolio value
    tokenTornadoGame.crypto.portfolioValue += totalWinnings;
    tokenTornadoGame.stats.portfolioValue = tokenTornadoGame.crypto.portfolioValue;

    // Add winnings to balance
    balance += totalWinnings;
    tokenTornadoGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? 'Crypto combination wins!' : 'No winning combination';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Market: ${CRYPTO_MARKETS[tokenTornadoGame.cryptoMarket].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show crypto event
    document.getElementById('cryptoEvent').classList.remove('hidden');
    document.getElementById('cryptoEvent').textContent = results.moonshot ? 'MOONSHOT!' :
                                                        results.stormActive ? 'STORM ACTIVE!' : 'TORNADO SPUN!';
    document.getElementById('cryptoEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update crypto display
function updateCryptoDisplay() {
    updateCryptoStatus();
    updateStormDisplay();
}

// Update crypto status
function updateCryptoStatus() {
    document.getElementById('marketCap').innerHTML =
        `<div class="text-orange-400 font-bold">MARKET CAP: ${Math.floor(tokenTornadoGame.crypto.marketCap * 100)}%</div>`;
    document.getElementById('volatilityLevel').innerHTML =
        `<div class="text-red-400 font-bold">VOLATILITY: ${Math.floor(tokenTornadoGame.crypto.volatility * 100)}%</div>`;
    document.getElementById('adoption').innerHTML =
        `<div class="text-green-400 font-bold">ADOPTION: ${Math.floor(tokenTornadoGame.crypto.adoption * 100)}%</div>`;
    document.getElementById('cryptoLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${tokenTornadoGame.crypto.cryptoLevel.toUpperCase()}</div>`;

    document.getElementById('marketCapDisplay').querySelector('.text-2xl').textContent =
        `${Math.floor(tokenTornadoGame.crypto.marketCap * 100)}%`;

    document.getElementById('volatilityDisplay').querySelector('.text-2xl').textContent =
        `${Math.floor(tokenTornadoGame.crypto.volatility * 100)}%`;

    document.getElementById('adoptionDisplay').querySelector('.text-2xl').textContent =
        `${Math.floor(tokenTornadoGame.crypto.adoption * 100)}%`;
}

// Update storm display
function updateStormDisplay() {
    document.getElementById('stormIntensity').textContent =
        `${Math.floor(tokenTornadoGame.crypto.stormIntensity * 100)}%`;

    const multiplier = tokenTornadoGame.slots.stormActive ? 2.0 : 1.0;
    document.getElementById('stormMultiplier').textContent = `${multiplier.toFixed(1)}x`;

    document.getElementById('moonPhase').textContent =
        `${Math.floor(tokenTornadoGame.crypto.moonPhase * 100)}%`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${tokenTornadoGame.betAmount}`;
    document.getElementById('cryptoPointsDisplay').textContent = tokenTornadoGame.stats.cryptoPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = tokenTornadoGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${tokenTornadoGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${tokenTornadoGame.stats.totalWagered}`;
    document.getElementById('volatilityStorms').textContent = tokenTornadoGame.stats.volatilityStorms;
    document.getElementById('moonshots').textContent = tokenTornadoGame.stats.moonshots;

    const netResult = tokenTornadoGame.stats.totalWon - tokenTornadoGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-orange-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    tokenTornadoGame.stats.spinsPlayed++;
    tokenTornadoGame.stats.totalWagered += tokenTornadoGame.betAmount;
    tokenTornadoGame.stats.totalWon += winnings;

    if (won) {
        tokenTornadoGame.stats.spinsWon++;
        tokenTornadoGame.stats.currentStreak++;
        tokenTornadoGame.streakData.currentWinStreak++;
        tokenTornadoGame.streakData.currentLossStreak = 0;

        if (tokenTornadoGame.streakData.currentWinStreak > tokenTornadoGame.streakData.longestWinStreak) {
            tokenTornadoGame.streakData.longestWinStreak = tokenTornadoGame.streakData.currentWinStreak;
        }

        if (winnings > tokenTornadoGame.stats.biggestWin) {
            tokenTornadoGame.stats.biggestWin = winnings;
        }
    } else {
        tokenTornadoGame.stats.currentStreak = 0;
        tokenTornadoGame.streakData.currentWinStreak = 0;
        tokenTornadoGame.streakData.currentLossStreak++;

        if (tokenTornadoGame.streakData.currentLossStreak > tokenTornadoGame.streakData.longestLossStreak) {
            tokenTornadoGame.streakData.longestLossStreak = tokenTornadoGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to crypto volatility)
    tokenTornadoGame.stats.winRate = (tokenTornadoGame.stats.spinsWon / tokenTornadoGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next crypto session
function resetGame() {
    tokenTornadoGame.isPlaying = false;
    tokenTornadoGame.betAmount = 0;
    tokenTornadoGame.totalBet = 0;
    tokenTornadoGame.gameResult = '';
    tokenTornadoGame.totalWin = 0;

    // Reset slot system
    tokenTornadoGame.slots.reels = [[], [], [], [], []];
    tokenTornadoGame.slots.gamePhase = 'spinning';
    tokenTornadoGame.slots.gameResult = '';
    tokenTornadoGame.slots.stormActive = false;
    tokenTornadoGame.slots.moonshot = false;
    tokenTornadoGame.slots.flashCrash = false;
    tokenTornadoGame.slots.pumpAndDump = false;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('cryptoEvent').classList.add('hidden');

    // Reset reel displays
    const reelElements = ['reel1', 'reel2', 'reel3', 'reel4', 'reel5'];
    reelElements.forEach((reelId, index) => {
        const reel = document.getElementById(reelId);
        const symbolElement = reel.querySelector('div');
        symbolElement.textContent = CRYPTO_SYMBOLS[index];
        reel.style.transform = 'rotateX(0deg)';
        reel.style.transition = 'none';
        reel.style.boxShadow = 'none';
        reel.style.borderColor = '#4b5563';
    });

    // Reset storm display
    document.getElementById('stormStatus').textContent = 'Storm brewing...';
    document.getElementById('stormBar').style.width = '0%';
    document.getElementById('stormStatusText').textContent = 'Building';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable spin button
    document.getElementById('spinReels').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Ready to spin tornado...';
    document.getElementById('gameMessage').textContent = 'Welcome to Token Tornado - Cryptocurrency-themed Slot with Volatility Storms';

    // Reinitialize systems for next session
    initializeCryptoSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadTokenTornadoGame();
});