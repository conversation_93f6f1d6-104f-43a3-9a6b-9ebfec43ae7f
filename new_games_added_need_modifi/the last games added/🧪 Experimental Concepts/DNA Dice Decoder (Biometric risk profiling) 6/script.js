// DNA Dice Decoder - Biometric Risk Profiling
// Experimental Concept Implementation with Biometric and Genetic Theme
// Designed to maintain 3-5% player win rate with DNA analysis mechanics

// Game state
let balance = 1000;

// Game state object with DNA biometric system
let dnaDiceDecoderGame = {
    isPlaying: false,
    dnaProfile: 'human', // human, enhanced, synthetic, hybrid, quantum
    riskProfile: 'balanced', // conservative, balanced, aggressive, extreme, adaptive
    betAmount: 0,
    totalBet: 0,

    // DNA biometric system
    biometrics: {
        dnaSequence: '',
        geneticMarkers: [],
        riskTolerance: 0.65, // 65% risk tolerance
        adaptability: 0.70, // 70% adaptability
        intuition: 0.75, // 75% intuition
        resilience: 0.80, // 80% resilience
        evolution: 0.60, // 60% evolution rate
        mutations: 0,
        geneticAdvantage: 1.0,
        biometricLevel: 'baseline', // baseline, enhanced, superior, transcendent, quantum
        dnaPoints: 0,
        sequenceHistory: [],
        adaptiveBonus: 0.0
    },

    // Dice system with DNA influence
    dice: {
        count: 4, // 4 dice for DNA (A, T, G, C)
        values: [1, 1, 1, 1],
        dnaMapping: ['A', 'T', 'G', 'C'], // DNA base pairs
        targetSequence: 'ATGC',
        actualSequence: '',
        geneticMatch: false,
        mutationBonus: 1.0,
        evolutionMultiplier: 1.0,
        biometricBonus: 1.0,
        adaptiveResponse: 0,
        dnaStability: 0.85, // 85% stability
        sequenceComplexity: 0.0
    },

    // DNA profiles
    dnaProfiles: {
        human: {
            name: 'Human DNA',
            stability: 0.85, // 85% stability
            adaptability: 0.70, // 70% adaptability
            mutationRate: 0.05, // 5% mutation rate
            evolutionBonus: 0.25, // 25% evolution bonus
            riskModifier: 1.0 // No risk modification
        },
        enhanced: {
            name: 'Enhanced DNA',
            stability: 0.90, // 90% stability
            adaptability: 0.80, // 80% adaptability
            mutationRate: 0.08, // 8% mutation rate
            evolutionBonus: 0.35, // 35% evolution bonus
            riskModifier: 1.10 // 10% risk increase
        },
        synthetic: {
            name: 'Synthetic DNA',
            stability: 0.95, // 95% stability
            adaptability: 0.85, // 85% adaptability
            mutationRate: 0.12, // 12% mutation rate
            evolutionBonus: 0.45, // 45% evolution bonus
            riskModifier: 1.20 // 20% risk increase
        },
        hybrid: {
            name: 'Hybrid DNA',
            stability: 0.88, // 88% stability
            adaptability: 0.90, // 90% adaptability
            mutationRate: 0.15, // 15% mutation rate
            evolutionBonus: 0.55, // 55% evolution bonus
            riskModifier: 1.30 // 30% risk increase
        },
        quantum: {
            name: 'Quantum DNA',
            stability: 0.92, // 92% stability
            adaptability: 0.95, // 95% adaptability
            mutationRate: 0.20, // 20% mutation rate
            evolutionBonus: 0.65, // 65% evolution bonus
            riskModifier: 1.40 // 40% risk increase
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        dnaSequencesAnalyzed: 0,
        mutationsTriggered: 0,
        evolutionEvents: 0,
        geneticMatches: 0,
        biometricPoints: 0,
        adaptiveResponses: 0,
        bestRiskProfile: 'balanced'
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// DNA profiles with balanced biometric requirements (3-5% win rate)
const DNA_PROFILES = {
    human: {
        name: 'Human DNA',
        biometricWeight: 0.25, // 25% biometric influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        adaptiveBonus: 0.20, // 20% adaptive bonus
        evolutionBonus: 0.15 // 15% evolution bonus
    },
    enhanced: {
        name: 'Enhanced DNA',
        biometricWeight: 0.30, // 30% biometric influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        adaptiveBonus: 0.25, // 25% adaptive bonus
        evolutionBonus: 0.20 // 20% evolution bonus
    },
    synthetic: {
        name: 'Synthetic DNA',
        biometricWeight: 0.35, // 35% biometric influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        adaptiveBonus: 0.30, // 30% adaptive bonus
        evolutionBonus: 0.25 // 25% evolution bonus
    },
    hybrid: {
        name: 'Hybrid DNA',
        biometricWeight: 0.40, // 40% biometric influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        adaptiveBonus: 0.35, // 35% adaptive bonus
        evolutionBonus: 0.30 // 30% evolution bonus
    },
    quantum: {
        name: 'Quantum DNA',
        biometricWeight: 0.45, // 45% biometric influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.15, // Premium payouts
        adaptiveBonus: 0.40, // 40% adaptive bonus
        evolutionBonus: 0.35 // 35% evolution bonus
    }
};

const RISK_PROFILES = {
    conservative: {
        name: 'Conservative',
        riskLevel: 0.20, // 20% risk
        stabilityBonus: 1.25 // 25% stability boost
    },
    balanced: {
        name: 'Balanced',
        riskLevel: 0.35, // 35% risk
        stabilityBonus: 1.15 // 15% stability boost
    },
    aggressive: {
        name: 'Aggressive',
        riskLevel: 0.50, // 50% risk
        stabilityBonus: 1.05 // 5% stability boost
    },
    extreme: {
        name: 'Extreme',
        riskLevel: 0.70, // 70% risk
        stabilityBonus: 0.95 // 5% stability reduction
    },
    adaptive: {
        name: 'Adaptive',
        riskLevel: 0.40, // 40% risk (variable)
        stabilityBonus: 1.30 // 30% stability boost
    }
};

// Improved payout table with DNA theme (3-5% win rate)
const DNA_DICE_PAYOUTS = {
    // Perfect DNA achievements (moderately reduced)
    GENETIC_PERFECTION: 3000, // Reduced from 6000:1 but still excellent
    DNA_MASTER: 2000, // Reduced from 4000:1
    EVOLUTION_GENIUS: 1200, // Reduced from 2400:1
    MUTATION_KING: 800, // Reduced from 1600:1

    // DNA sequence combinations
    PERFECT_SEQUENCE: 2500, // ATGC perfect match
    TRIPLE_MATCH: 1500, // Three matching bases
    DOUBLE_MATCH: 800, // Two matching bases
    MUTATION_BONUS: 400, // Beneficial mutation

    // Biometric bonuses (actually apply more often)
    BIOMETRIC_BONUS: 0.85, // 85% of displayed bonus (increased)
    ADAPTIVE_BONUS: 0.75, // 75% of displayed bonus (increased)
    EVOLUTION_BONUS: 0.65, // 65% of displayed bonus (increased)
    MUTATION_BONUS_RATE: 0.55 // 55% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadDNADiceDecoderGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">DNA CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DNA PROFILE</label>
                        <select id="dnaProfile" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="human">Human DNA</option>
                            <option value="enhanced">Enhanced DNA</option>
                            <option value="synthetic">Synthetic DNA</option>
                            <option value="hybrid">Hybrid DNA</option>
                            <option value="quantum">Quantum DNA</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">RISK PROFILE</label>
                        <select id="riskProfile" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="conservative">Conservative</option>
                            <option value="balanced">Balanced</option>
                            <option value="aggressive">Aggressive</option>
                            <option value="extreme">Extreme</option>
                            <option value="adaptive">Adaptive</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="analyzeDNA" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ANALYZE DNA
                    </button>

                    <div id="dnaActions" class="space-y-2 hidden">
                        <button id="rollDice" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            ROLL DNA DICE
                        </button>
                        <button id="triggerMutation" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            TRIGGER MUTATION
                        </button>
                        <button id="evolveGenetics" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            EVOLVE GENETICS
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Biometric Points</div>
                        <div id="biometricPointsDisplay" class="text-lg font-bold text-green-400">0</div>
                    </div>
                </div>

                <!-- Biometric Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">BIOMETRIC STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="riskTolerance" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">RISK: 65%</div>
                        </div>
                        <div id="adaptability" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">ADAPT: 70%</div>
                        </div>
                        <div id="intuition" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">INTUITION: 75%</div>
                        </div>
                        <div id="resilience" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">RESILIENCE: 80%</div>
                        </div>
                    </div>
                </div>

                <!-- DNA Analysis Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">DNA ANALYSIS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Genetic Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Stability:</span>
                            <span class="text-green-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Mutations:</span>
                            <span class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Evolution:</span>
                            <span class="text-purple-400">60%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">DNA Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Biometric:</span>
                            <span class="text-green-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Adaptive:</span>
                            <span class="text-green-400">75%</span>
                        </div>
                        <div class="text-xs text-green-400 mt-2">*DNA analysis improves odds</div>
                        <div class="text-xs text-green-400">*Mutations provide bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main DNA Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <!-- DNA Dice Arena -->
                    <div id="dnaDiceArena" class="relative bg-gradient-to-br from-black via-green-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- DNA Background -->
                        <div id="dnaBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="dnaGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#059669;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#047857;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="dnaPattern" width="100" height="100" patternUnits="userSpaceOnUse">
                                        <path d="M20,20 Q50,10 80,20 Q50,30 20,20" fill="none" stroke="#10b981" stroke-width="2" opacity="0.4"/>
                                        <path d="M20,80 Q50,70 80,80 Q50,90 20,80" fill="none" stroke="#10b981" stroke-width="2" opacity="0.4"/>
                                        <line x1="20" y1="20" x2="20" y2="80" stroke="#059669" stroke-width="1" opacity="0.3"/>
                                        <line x1="80" y1="20" x2="80" y2="80" stroke="#059669" stroke-width="1" opacity="0.3"/>
                                        <circle cx="20" cy="20" r="3" fill="#10b981" opacity="0.6"/>
                                        <circle cx="80" cy="20" r="3" fill="#10b981" opacity="0.6"/>
                                        <circle cx="20" cy="80" r="3" fill="#10b981" opacity="0.6"/>
                                        <circle cx="80" cy="80" r="3" fill="#10b981" opacity="0.6"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#dnaPattern)" />
                                <circle id="dnaCore" cx="50%" cy="50%" r="25%" fill="url(#dnaGradient)" class="animate-pulse" />
                                <g id="dnaEffects">
                                    <!-- DNA effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- DNA Dice Display -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">DNA DICE (A-T-G-C)</div>
                                <div id="dnaDiceDisplay" class="flex space-x-3">
                                    <!-- 4 DNA-enhanced dice -->
                                    <div class="dna-die w-16 h-16 bg-gradient-to-br from-green-600 to-green-800 rounded-lg border-2 border-green-400 flex flex-col items-center justify-center text-white transition-all duration-500" data-die="0">
                                        <div class="text-lg font-bold">A</div>
                                        <div class="text-xs">1</div>
                                    </div>
                                    <div class="dna-die w-16 h-16 bg-gradient-to-br from-green-600 to-green-800 rounded-lg border-2 border-green-400 flex flex-col items-center justify-center text-white transition-all duration-500" data-die="1">
                                        <div class="text-lg font-bold">T</div>
                                        <div class="text-xs">1</div>
                                    </div>
                                    <div class="dna-die w-16 h-16 bg-gradient-to-br from-green-600 to-green-800 rounded-lg border-2 border-green-400 flex flex-col items-center justify-center text-white transition-all duration-500" data-die="2">
                                        <div class="text-lg font-bold">G</div>
                                        <div class="text-xs">1</div>
                                    </div>
                                    <div class="dna-die w-16 h-16 bg-gradient-to-br from-green-600 to-green-800 rounded-lg border-2 border-green-400 flex flex-col items-center justify-center text-white transition-all duration-500" data-die="3">
                                        <div class="text-lg font-bold">C</div>
                                        <div class="text-xs">1</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Biometric-enhanced genetic dice</div>
                            </div>
                        </div>

                        <!-- DNA Sequence Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">DNA SEQUENCE</div>
                                <div id="dnaSequenceDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="sequenceStatus" class="text-lg text-white mb-3">Ready for DNA analysis...</div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-green-400 mb-1">TARGET</div>
                                            <div id="targetSequence" class="text-xl font-bold text-white font-mono">ATGC</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-green-400 mb-1">ACTUAL</div>
                                            <div id="actualSequence" class="text-xl font-bold text-yellow-400 font-mono">----</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Genetic sequence matching</div>
                            </div>
                        </div>

                        <!-- DNA Analysis Progress -->
                        <div id="dnaAnalysisProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">DNA ANALYSIS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="analysisBar" class="bg-gradient-to-r from-green-400 to-blue-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Scanning</span>
                                    <span id="analysisStatus">Ready</span>
                                    <span>Complete</span>
                                </div>
                            </div>
                        </div>

                        <!-- Evolution Level -->
                        <div id="evolutionLevel" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">EVOLUTION</div>
                                <div class="text-2xl font-bold text-white text-center">60%</div>
                                <div class="text-xs text-gray-400 mt-1">Level</div>
                            </div>
                        </div>

                        <!-- Mutation Count -->
                        <div id="mutationCount" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">MUTATIONS</div>
                                <div class="text-2xl font-bold text-white text-center">0</div>
                                <div class="text-xs text-gray-400 mt-1">Count</div>
                            </div>
                        </div>

                        <!-- Genetic Advantage -->
                        <div id="geneticAdvantage" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">ADVANTAGE</div>
                                <div class="text-2xl font-bold text-white text-center">1.0x</div>
                                <div class="text-xs text-gray-400 mt-1">Multiplier</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">DNA ready for analysis...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="dnaEvent" class="text-sm font-bold text-green-400 hidden animate-pulse">DNA ANALYZED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to DNA Dice Decoder - Biometric Risk Profiling</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-green-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-green-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Mutations</div>
                <div id="mutationsTriggered" class="text-xl font-bold text-red-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Evolution Events</div>
                <div id="evolutionEvents" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeDNADiceDecoder();
}

// Initialize the game
function initializeDNADiceDecoder() {
    document.getElementById('analyzeDNA').addEventListener('click', analyzeDNAProfile);
    document.getElementById('rollDice').addEventListener('click', rollDNADice);
    document.getElementById('triggerMutation').addEventListener('click', triggerGeneticMutation);
    document.getElementById('evolveGenetics').addEventListener('click', evolveGeneticProfile);

    // Initialize DNA systems
    initializeDNASystems();
    generateDNAEffects();
    updateGameStats();
}

// Initialize DNA systems
function initializeDNASystems() {
    // Reset biometric system
    dnaDiceDecoderGame.biometrics.dnaSequence = '';
    dnaDiceDecoderGame.biometrics.geneticMarkers = [];
    dnaDiceDecoderGame.biometrics.riskTolerance = Math.min(0.90, 0.65 + (dnaDiceDecoderGame.stats.biometricPoints * 0.01));
    dnaDiceDecoderGame.biometrics.adaptability = Math.min(0.95, 0.70 + (dnaDiceDecoderGame.stats.biometricPoints * 0.008));
    dnaDiceDecoderGame.biometrics.intuition = Math.min(0.90, 0.75 + (dnaDiceDecoderGame.stats.biometricPoints * 0.006));
    dnaDiceDecoderGame.biometrics.resilience = Math.min(0.95, 0.80 + (dnaDiceDecoderGame.stats.biometricPoints * 0.005));
    dnaDiceDecoderGame.biometrics.evolution = Math.min(0.85, 0.60 + (dnaDiceDecoderGame.stats.biometricPoints * 0.01));
    dnaDiceDecoderGame.biometrics.mutations = 0;
    dnaDiceDecoderGame.biometrics.geneticAdvantage = 1.0;
    dnaDiceDecoderGame.biometrics.biometricLevel = calculateBiometricLevel();
    dnaDiceDecoderGame.biometrics.dnaPoints = dnaDiceDecoderGame.stats.biometricPoints;
    dnaDiceDecoderGame.biometrics.sequenceHistory = [];
    dnaDiceDecoderGame.biometrics.adaptiveBonus = 0.0;

    // Reset dice system
    dnaDiceDecoderGame.dice.count = 4;
    dnaDiceDecoderGame.dice.values = [1, 1, 1, 1];
    dnaDiceDecoderGame.dice.dnaMapping = ['A', 'T', 'G', 'C'];
    dnaDiceDecoderGame.dice.targetSequence = generateRandomDNASequence();
    dnaDiceDecoderGame.dice.actualSequence = '';
    dnaDiceDecoderGame.dice.geneticMatch = false;
    dnaDiceDecoderGame.dice.mutationBonus = 1.0;
    dnaDiceDecoderGame.dice.evolutionMultiplier = 1.0;
    dnaDiceDecoderGame.dice.biometricBonus = 1.0;
    dnaDiceDecoderGame.dice.adaptiveResponse = 0;
    dnaDiceDecoderGame.dice.dnaStability = 0.85;
    dnaDiceDecoderGame.dice.sequenceComplexity = 0.0;

    updateDNADisplay();
}

// Generate DNA effects
function generateDNAEffects() {
    const container = document.getElementById('dnaEffects');
    container.innerHTML = '';

    // Create DNA helix visualization
    for (let i = 0; i < 10; i++) {
        const basePair = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        const angle = (i / 10) * Math.PI * 2;
        const x1 = 50 + Math.cos(angle) * 20;
        const y1 = 20 + i * 6;
        const x2 = 50 + Math.cos(angle + Math.PI) * 20;
        const y2 = 20 + i * 6;

        basePair.setAttribute('x1', `${x1}%`);
        basePair.setAttribute('y1', `${y1}%`);
        basePair.setAttribute('x2', `${x2}%`);
        basePair.setAttribute('y2', `${y2}%`);
        basePair.setAttribute('stroke', '#10b981');
        basePair.setAttribute('stroke-width', '2');
        basePair.setAttribute('opacity', '0.6');
        basePair.classList.add('animate-pulse');
        basePair.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(basePair);

        // Add base markers
        const base1 = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        base1.setAttribute('cx', `${x1}%`);
        base1.setAttribute('cy', `${y1}%`);
        base1.setAttribute('r', '2');
        base1.setAttribute('fill', '#059669');
        base1.setAttribute('opacity', '0.8');
        container.appendChild(base1);

        const base2 = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        base2.setAttribute('cx', `${x2}%`);
        base2.setAttribute('cy', `${y2}%`);
        base2.setAttribute('r', '2');
        base2.setAttribute('fill', '#047857');
        base2.setAttribute('opacity', '0.8');
        container.appendChild(base2);
    }
}

// Calculate biometric level
function calculateBiometricLevel() {
    const points = dnaDiceDecoderGame.stats.biometricPoints;
    if (points >= 100) return 'quantum';
    if (points >= 50) return 'transcendent';
    if (points >= 25) return 'superior';
    if (points >= 10) return 'enhanced';
    return 'baseline';
}

// Generate random DNA sequence
function generateRandomDNASequence() {
    const bases = ['A', 'T', 'G', 'C'];
    let sequence = '';
    for (let i = 0; i < 4; i++) {
        sequence += bases[Math.floor(Math.random() * bases.length)];
    }
    return sequence;
}

// Analyze DNA profile
function analyzeDNAProfile() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    dnaDiceDecoderGame.isPlaying = true;
    dnaDiceDecoderGame.betAmount = betAmount;
    dnaDiceDecoderGame.totalBet = betAmount;
    dnaDiceDecoderGame.dnaProfile = document.getElementById('dnaProfile').value;
    dnaDiceDecoderGame.riskProfile = document.getElementById('riskProfile').value;

    // Start DNA analysis
    startDNAAnalysis();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('analyzeDNA').disabled = true;
    document.getElementById('gameStatus').textContent = 'Analyzing DNA profile...';
}

// Start DNA analysis
function startDNAAnalysis() {
    const dnaProfileData = dnaDiceDecoderGame.dnaProfiles[dnaDiceDecoderGame.dnaProfile];
    const riskProfileData = RISK_PROFILES[dnaDiceDecoderGame.riskProfile];

    // Update analysis information
    document.getElementById('sequenceStatus').textContent = `Analyzing ${dnaProfileData.name}...`;
    document.getElementById('analysisStatus').textContent = 'Analyzing...';

    // Simulate DNA analysis process
    simulateDNAAnalysis(dnaProfileData, riskProfileData);

    // Update biometric status
    updateBiometricStatus();

    // Update visual effects
    updateDNADisplay();
    updateDNAEffects();
}

// Simulate DNA analysis process
function simulateDNAAnalysis(dnaProfileData, riskProfileData) {
    let progress = 0;
    const analysisTime = 40; // 4 seconds analysis time

    const analysisInterval = setInterval(() => {
        progress += 100 / analysisTime; // Update every 100ms

        // Update progress bar
        document.getElementById('analysisBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('analysisStatus').textContent = 'Scanning...';
        } else if (progress < 40) {
            document.getElementById('analysisStatus').textContent = 'Sequencing...';
        } else if (progress < 60) {
            document.getElementById('analysisStatus').textContent = 'Profiling...';
        } else if (progress < 80) {
            document.getElementById('analysisStatus').textContent = 'Analyzing...';
        } else if (progress < 100) {
            document.getElementById('analysisStatus').textContent = 'Calibrating...';
        } else {
            document.getElementById('analysisStatus').textContent = 'Complete!';
            clearInterval(analysisInterval);
            completeDNAAnalysis(dnaProfileData, riskProfileData);
        }

        // Update evolution level based on progress
        const evolutionLevel = Math.min(85, dnaDiceDecoderGame.biometrics.evolution * 100 + progress * 0.2);
        document.getElementById('evolutionLevel').querySelector('.text-2xl').textContent = `${Math.floor(evolutionLevel)}%`;

    }, 100);
}

// Complete DNA analysis
function completeDNAAnalysis(dnaProfileData, riskProfileData) {
    // Apply biometric bonuses
    const biometricBonus = calculateBiometricBonus();
    const adaptiveBonus = dnaDiceDecoderGame.biometrics.adaptability * 0.15; // Up to 15% bonus

    // Update DNA sequence
    dnaDiceDecoderGame.biometrics.dnaSequence = generateDNASequence();
    dnaDiceDecoderGame.dice.targetSequence = generateRandomDNASequence();

    // Calculate genetic advantage
    dnaDiceDecoderGame.biometrics.geneticAdvantage = 1.0 + biometricBonus + adaptiveBonus;

    // Award biometric points
    const pointsEarned = Math.floor(dnaProfileData.evolutionBonus * 100);
    dnaDiceDecoderGame.stats.biometricPoints += pointsEarned;

    // Enable actions
    document.getElementById('dnaActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'DNA analyzed! Roll genetic dice.';

    // Update displays
    updateBiometricStatus();
    updateDNASequenceDisplay();
}

// Generate DNA sequence
function generateDNASequence() {
    const bases = ['A', 'T', 'G', 'C'];
    let sequence = '';
    for (let i = 0; i < 16; i++) { // 16-base sequence
        sequence += bases[Math.floor(Math.random() * bases.length)];
    }
    return sequence;
}

// Calculate biometric bonus
function calculateBiometricBonus() {
    let bonus = 0;

    // Risk tolerance bonus
    bonus += dnaDiceDecoderGame.biometrics.riskTolerance * 0.15; // Up to 15% bonus

    // Adaptability bonus
    bonus += dnaDiceDecoderGame.biometrics.adaptability * 0.12; // Up to 12% bonus

    // Intuition bonus
    bonus += dnaDiceDecoderGame.biometrics.intuition * 0.10; // Up to 10% bonus

    // Resilience bonus
    bonus += dnaDiceDecoderGame.biometrics.resilience * 0.08; // Up to 8% bonus

    // Evolution bonus
    bonus += dnaDiceDecoderGame.biometrics.evolution * 0.20; // Up to 20% bonus

    // Biometric level bonus
    const levelBonuses = {
        baseline: 0.05,
        enhanced: 0.10,
        superior: 0.15,
        transcendent: 0.20,
        quantum: 0.25
    };
    bonus += levelBonuses[dnaDiceDecoderGame.biometrics.biometricLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Roll DNA dice with biometric influence (3-5% win rate)
function rollDNADice() {
    if (!dnaDiceDecoderGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Rolling DNA dice...';

    // Calculate biometric influence on dice
    const biometricInfluence = calculateBiometricInfluence();

    // Generate dice results with DNA influence
    const diceResults = generateDNADiceRoll(biometricInfluence);

    // Animate dice roll
    animateDNADiceRoll(diceResults);

    // Resolve after animation
    setTimeout(() => {
        resolveDNADiceRoll(diceResults);
    }, 3000);
}

// Calculate biometric influence
function calculateBiometricInfluence() {
    const dnaProfileData = DNA_PROFILES[dnaDiceDecoderGame.dnaProfile];
    const biometricBonus = calculateBiometricBonus();
    const adaptiveBonus = dnaDiceDecoderGame.biometrics.adaptability * 0.10; // Up to 10% bonus
    const evolutionBonus = dnaDiceDecoderGame.biometrics.evolution * 0.08; // Up to 8% bonus
    const biometricPointsBonus = dnaDiceDecoderGame.stats.biometricPoints * 0.002; // Biometric points bonus

    return Math.min(0.35, dnaProfileData.biometricWeight + biometricBonus + adaptiveBonus + evolutionBonus + biometricPointsBonus);
}

// Generate DNA dice roll with biometric influence (improved for 3-5% win rate)
function generateDNADiceRoll(biometricInfluence) {
    const dnaProfileData = DNA_PROFILES[dnaDiceDecoderGame.dnaProfile];
    const riskProfileData = RISK_PROFILES[dnaDiceDecoderGame.riskProfile];

    // Apply biometric influence to improve odds
    const adjustedOdds = 0.035 + biometricInfluence; // Base 3.5% + biometric influence

    // Generate base dice rolls (1-6 for each DNA base)
    const diceValues = [];
    const dnaSequence = [];

    for (let i = 0; i < 4; i++) {
        let dieValue = Math.floor(Math.random() * 6) + 1;

        // Apply biometric influence (improved)
        if (Math.random() < biometricInfluence) {
            // Biometrics try to improve dice outcomes
            if (dieValue <= 3) {
                dieValue = Math.min(6, dieValue + Math.floor(Math.random() * 3) + 1);
            }
        }

        diceValues.push(dieValue);
        dnaSequence.push(dnaDiceDecoderGame.dice.dnaMapping[i]);
    }

    // Check for genetic matches
    const targetSequence = dnaDiceDecoderGame.dice.targetSequence;
    const actualSequence = dnaSequence.join('');
    const geneticMatch = actualSequence === targetSequence;

    // Calculate sequence similarity
    let sequenceSimilarity = 0;
    for (let i = 0; i < 4; i++) {
        if (actualSequence[i] === targetSequence[i]) {
            sequenceSimilarity++;
        }
    }

    // Check for DNA patterns
    const dnaPattern = analyzeDNAPattern(diceValues, dnaSequence);

    return {
        values: diceValues,
        sequence: actualSequence,
        targetSequence: targetSequence,
        geneticMatch: geneticMatch,
        sequenceSimilarity: sequenceSimilarity,
        dnaPattern: dnaPattern,
        biometricInfluence: biometricInfluence,
        geneticAdvantage: dnaDiceDecoderGame.biometrics.geneticAdvantage
    };
}

// Analyze DNA pattern
function analyzeDNAPattern(values, sequence) {
    const sum = values.reduce((sum, val) => sum + val, 0);

    // Check for specific DNA patterns
    if (sequence.join('') === 'ATGC') {
        return { type: 'perfect_sequence', name: 'Perfect Sequence', multiplier: DNA_DICE_PAYOUTS.PERFECT_SEQUENCE };
    }

    // Check for triple matches
    const baseCounts = {};
    sequence.forEach(base => {
        baseCounts[base] = (baseCounts[base] || 0) + 1;
    });

    const maxCount = Math.max(...Object.values(baseCounts));
    if (maxCount >= 3) {
        return { type: 'triple_match', name: 'Triple Match', multiplier: DNA_DICE_PAYOUTS.TRIPLE_MATCH };
    }

    if (maxCount >= 2) {
        return { type: 'double_match', name: 'Double Match', multiplier: DNA_DICE_PAYOUTS.DOUBLE_MATCH };
    }

    // Check for high sum
    if (sum >= 20) {
        return { type: 'high_sum', name: 'High Genetic Expression', multiplier: 500 };
    }

    return { type: 'none', name: 'No Pattern', multiplier: 0 };
}

// Animate DNA dice roll
function animateDNADiceRoll(results) {
    const dice = document.querySelectorAll('.dna-die');

    // Add rolling animation
    dice.forEach((die, index) => {
        die.classList.add('animate-spin');
        die.style.transform = 'scale(1.2)';

        // Show random values during animation
        const rollInterval = setInterval(() => {
            const randomValue = Math.floor(Math.random() * 6) + 1;
            die.querySelector('.text-xs').textContent = randomValue;
        }, 100);

        // Stop animation and show final value
        setTimeout(() => {
            clearInterval(rollInterval);
            die.classList.remove('animate-spin');
            die.style.transform = 'scale(1)';
            die.querySelector('.text-xs').textContent = results.values[index];

            // Add glow effect for good results
            if (results.geneticMatch) {
                die.classList.add('ring-2', 'ring-green-400', 'animate-pulse');
            } else if (results.sequenceSimilarity >= 2) {
                die.classList.add('ring-1', 'ring-blue-400');
            }
        }, 1000 + index * 300);
    });

    // Update sequence display
    setTimeout(() => {
        document.getElementById('actualSequence').textContent = results.sequence;
        document.getElementById('targetSequence').textContent = results.targetSequence;
    }, 2000);
}

// Resolve DNA dice roll with biometric bonuses (3-5% win rate)
function resolveDNADiceRoll(results) {
    const dnaProfileData = DNA_PROFILES[dnaDiceDecoderGame.dnaProfile];
    const dnaProfile = dnaDiceDecoderGame.dnaProfiles[dnaDiceDecoderGame.dnaProfile];
    const riskProfileData = RISK_PROFILES[dnaDiceDecoderGame.riskProfile];

    let totalWinnings = 0;
    let resultMessage = '';

    // Store results
    dnaDiceDecoderGame.dice.values = results.values;
    dnaDiceDecoderGame.dice.actualSequence = results.sequence;
    dnaDiceDecoderGame.dice.geneticMatch = results.geneticMatch;
    dnaDiceDecoderGame.dice.sequenceComplexity = results.sequenceSimilarity / 4;

    // Update sequence history
    dnaDiceDecoderGame.biometrics.sequenceHistory.push({
        sequence: results.sequence,
        target: results.targetSequence,
        match: results.geneticMatch,
        timestamp: Date.now()
    });

    // Calculate base payout for DNA pattern
    if (results.dnaPattern.type !== 'none') {
        totalWinnings = Math.floor(dnaDiceDecoderGame.betAmount * (results.dnaPattern.multiplier / 100));
        resultMessage = results.dnaPattern.name;

        // Apply genetic advantage
        totalWinnings = Math.floor(totalWinnings * results.geneticAdvantage);
    }

    // Perfect genetic match bonus
    if (results.geneticMatch) {
        const perfectBonus = Math.floor(dnaDiceDecoderGame.betAmount * 5); // 5x bet bonus
        totalWinnings += perfectBonus;
        resultMessage += ' + Perfect Match!';
        dnaDiceDecoderGame.stats.geneticMatches++;
    }

    // Apply biometric bonuses (actually work)
    if (dnaDiceDecoderGame.biometrics.riskTolerance >= 0.80 && totalWinnings > 0) {
        const biometricBonus = Math.floor(totalWinnings * DNA_DICE_PAYOUTS.BIOMETRIC_BONUS);
        totalWinnings += biometricBonus;
        resultMessage += ' + Biometric Bonus!';
    }

    // Apply adaptive bonus
    if (dnaDiceDecoderGame.biometrics.adaptability >= 0.85 && totalWinnings > 0) {
        const adaptiveBonus = Math.floor(totalWinnings * DNA_DICE_PAYOUTS.ADAPTIVE_BONUS);
        totalWinnings += adaptiveBonus;
        resultMessage += ' + Adaptive Bonus!';
    }

    // Apply evolution bonus
    if (dnaDiceDecoderGame.biometrics.evolution >= 0.75 && totalWinnings > 0) {
        const evolutionBonus = Math.floor(totalWinnings * DNA_DICE_PAYOUTS.EVOLUTION_BONUS);
        totalWinnings += evolutionBonus;
        resultMessage += ' + Evolution Bonus!';
    }

    // Sequence similarity bonus
    if (results.sequenceSimilarity >= 3 && totalWinnings > 0) {
        const similarityBonus = Math.floor(totalWinnings * 0.5); // 50% bonus
        totalWinnings += similarityBonus;
        resultMessage += ' + Sequence Similarity!';
    }

    // Apply DNA profile multiplier
    totalWinnings = Math.floor(totalWinnings * dnaProfileData.payoutMultiplier);

    // Apply risk profile stability
    if (Math.random() > riskProfileData.riskLevel) {
        totalWinnings = Math.floor(totalWinnings * riskProfileData.stabilityBonus);
    } else {
        // Risk event - reduce winnings
        totalWinnings = Math.floor(totalWinnings * 0.8); // 20% loss
        if (totalWinnings > 0) resultMessage += ' - Risk Event!';
    }

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(dnaDiceDecoderGame.betAmount * 0.5); // 50% return
        resultMessage = 'DNA analysis reward';
    }

    // Award biometric points
    const pointsEarned = results.geneticMatch ? 5 : (results.sequenceSimilarity >= 2 ? 3 : 1);
    dnaDiceDecoderGame.stats.biometricPoints += pointsEarned;

    // Update DNA sequence analysis count
    dnaDiceDecoderGame.stats.dnaSequencesAnalyzed++;

    // Add winnings to balance
    balance += totalWinnings;
    dnaDiceDecoderGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterRoll(results.geneticMatch || results.dnaPattern.type !== 'none', totalWinnings);

    if (!resultMessage) {
        resultMessage = results.geneticMatch ? 'Perfect genetic match!' : 'DNA sequence analyzed';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Profile: ${DNA_PROFILES[dnaDiceDecoderGame.dnaProfile].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show DNA event
    document.getElementById('dnaEvent').classList.remove('hidden');
    document.getElementById('dnaEvent').textContent = results.geneticMatch ? 'GENETIC MATCH!' : 'DNA ANALYZED!';
    document.getElementById('dnaEvent').className = `text-sm font-bold ${results.geneticMatch ? 'text-green-400' : 'text-blue-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Trigger genetic mutation
function triggerGeneticMutation() {
    if (!dnaDiceDecoderGame.isPlaying) return;

    const mutationSuccess = Math.random() < dnaDiceDecoderGame.dnaProfiles[dnaDiceDecoderGame.dnaProfile].mutationRate;

    if (mutationSuccess) {
        // Beneficial mutation
        dnaDiceDecoderGame.biometrics.mutations++;
        dnaDiceDecoderGame.stats.mutationsTriggered++;

        // Improve random biometric attribute
        const attributes = ['riskTolerance', 'adaptability', 'intuition', 'resilience', 'evolution'];
        const randomAttribute = attributes[Math.floor(Math.random() * attributes.length)];
        dnaDiceDecoderGame.biometrics[randomAttribute] = Math.min(0.95, dnaDiceDecoderGame.biometrics[randomAttribute] + 0.05);

        // Increase genetic advantage
        dnaDiceDecoderGame.biometrics.geneticAdvantage = Math.min(2.0, dnaDiceDecoderGame.biometrics.geneticAdvantage + 0.1);

        // Visual feedback
        document.getElementById('dnaEvent').classList.remove('hidden');
        document.getElementById('dnaEvent').textContent = `BENEFICIAL MUTATION: ${randomAttribute.toUpperCase()} +5%!`;
        document.getElementById('dnaEvent').className = 'text-sm font-bold text-green-400 animate-pulse';

        // Update mutation count
        document.getElementById('mutationCount').querySelector('.text-2xl').textContent = dnaDiceDecoderGame.biometrics.mutations;

    } else {
        // Mutation failed
        document.getElementById('dnaEvent').classList.remove('hidden');
        document.getElementById('dnaEvent').textContent = 'MUTATION FAILED!';
        document.getElementById('dnaEvent').className = 'text-sm font-bold text-red-400 animate-pulse';
    }

    // Award biometric points
    dnaDiceDecoderGame.stats.biometricPoints += mutationSuccess ? 3 : 1;

    setTimeout(() => {
        document.getElementById('dnaEvent').classList.add('hidden');
    }, 3000);

    updateBiometricStatus();
}

// Evolve genetic profile
function evolveGeneticProfile() {
    if (!dnaDiceDecoderGame.isPlaying || dnaDiceDecoderGame.biometrics.mutations < 3) {
        alert('Need at least 3 mutations to evolve!');
        return;
    }

    // Evolution success based on evolution rate
    const evolutionSuccess = Math.random() < dnaDiceDecoderGame.biometrics.evolution;

    if (evolutionSuccess) {
        // Successful evolution
        dnaDiceDecoderGame.stats.evolutionEvents++;

        // Upgrade biometric level
        const levels = ['baseline', 'enhanced', 'superior', 'transcendent', 'quantum'];
        const currentIndex = levels.indexOf(dnaDiceDecoderGame.biometrics.biometricLevel);
        if (currentIndex < levels.length - 1) {
            dnaDiceDecoderGame.biometrics.biometricLevel = levels[currentIndex + 1];
        }

        // Improve all biometric attributes
        dnaDiceDecoderGame.biometrics.riskTolerance = Math.min(0.95, dnaDiceDecoderGame.biometrics.riskTolerance + 0.10);
        dnaDiceDecoderGame.biometrics.adaptability = Math.min(0.95, dnaDiceDecoderGame.biometrics.adaptability + 0.10);
        dnaDiceDecoderGame.biometrics.intuition = Math.min(0.95, dnaDiceDecoderGame.biometrics.intuition + 0.10);
        dnaDiceDecoderGame.biometrics.resilience = Math.min(0.95, dnaDiceDecoderGame.biometrics.resilience + 0.10);
        dnaDiceDecoderGame.biometrics.evolution = Math.min(0.95, dnaDiceDecoderGame.biometrics.evolution + 0.05);

        // Reset mutations (consumed in evolution)
        dnaDiceDecoderGame.biometrics.mutations = 0;

        // Increase genetic advantage significantly
        dnaDiceDecoderGame.biometrics.geneticAdvantage = Math.min(3.0, dnaDiceDecoderGame.biometrics.geneticAdvantage + 0.25);

        // Visual feedback
        document.getElementById('dnaEvent').classList.remove('hidden');
        document.getElementById('dnaEvent').textContent = `EVOLUTION COMPLETE: ${dnaDiceDecoderGame.biometrics.biometricLevel.toUpperCase()}!`;
        document.getElementById('dnaEvent').className = 'text-sm font-bold text-purple-400 animate-pulse';

    } else {
        // Evolution failed
        document.getElementById('dnaEvent').classList.remove('hidden');
        document.getElementById('dnaEvent').textContent = 'EVOLUTION FAILED!';
        document.getElementById('dnaEvent').className = 'text-sm font-bold text-red-400 animate-pulse';
    }

    // Award biometric points
    dnaDiceDecoderGame.stats.biometricPoints += evolutionSuccess ? 10 : 2;

    setTimeout(() => {
        document.getElementById('dnaEvent').classList.add('hidden');
    }, 3000);

    updateBiometricStatus();
    updateDNADisplay();
}

// Update DNA display
function updateDNADisplay() {
    updateBiometricStatus();
    updateDNASequenceDisplay();
}

// Update biometric status
function updateBiometricStatus() {
    document.getElementById('riskTolerance').innerHTML =
        `<div class="text-green-400 font-bold">RISK: ${Math.floor(dnaDiceDecoderGame.biometrics.riskTolerance * 100)}%</div>`;
    document.getElementById('adaptability').innerHTML =
        `<div class="text-blue-400 font-bold">ADAPT: ${Math.floor(dnaDiceDecoderGame.biometrics.adaptability * 100)}%</div>`;
    document.getElementById('intuition').innerHTML =
        `<div class="text-purple-400 font-bold">INTUITION: ${Math.floor(dnaDiceDecoderGame.biometrics.intuition * 100)}%</div>`;
    document.getElementById('resilience').innerHTML =
        `<div class="text-yellow-400 font-bold">RESILIENCE: ${Math.floor(dnaDiceDecoderGame.biometrics.resilience * 100)}%</div>`;

    document.getElementById('evolutionLevel').querySelector('.text-2xl').textContent =
        `${Math.floor(dnaDiceDecoderGame.biometrics.evolution * 100)}%`;

    document.getElementById('mutationCount').querySelector('.text-2xl').textContent =
        dnaDiceDecoderGame.biometrics.mutations;

    document.getElementById('geneticAdvantage').querySelector('.text-2xl').textContent =
        `${dnaDiceDecoderGame.biometrics.geneticAdvantage.toFixed(1)}x`;
}

// Update DNA sequence display
function updateDNASequenceDisplay() {
    document.getElementById('targetSequence').textContent = dnaDiceDecoderGame.dice.targetSequence;
    document.getElementById('actualSequence').textContent = dnaDiceDecoderGame.dice.actualSequence || '----';
}

// Update DNA effects
function updateDNAEffects() {
    // Update DNA effects based on biometric level
    const biometricLevel = dnaDiceDecoderGame.biometrics.biometricLevel;
    const effects = document.querySelectorAll('#dnaEffects line');

    effects.forEach((effect, index) => {
        switch (biometricLevel) {
            case 'quantum':
                effect.setAttribute('opacity', '0.9');
                effect.setAttribute('stroke', '#8b5cf6'); // Purple for quantum
                break;
            case 'transcendent':
                effect.setAttribute('opacity', '0.8');
                effect.setAttribute('stroke', '#06b6d4'); // Cyan for transcendent
                break;
            case 'superior':
                effect.setAttribute('opacity', '0.7');
                effect.setAttribute('stroke', '#3b82f6'); // Blue for superior
                break;
            case 'enhanced':
                effect.setAttribute('opacity', '0.6');
                effect.setAttribute('stroke', '#10b981'); // Green for enhanced
                break;
            default: // baseline
                effect.setAttribute('opacity', '0.5');
                effect.setAttribute('stroke', '#059669'); // Dark green for baseline
                break;
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${dnaDiceDecoderGame.betAmount}`;
    document.getElementById('biometricPointsDisplay').textContent = dnaDiceDecoderGame.stats.biometricPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = dnaDiceDecoderGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${dnaDiceDecoderGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${dnaDiceDecoderGame.stats.totalWagered}`;
    document.getElementById('mutationsTriggered').textContent = dnaDiceDecoderGame.stats.mutationsTriggered;
    document.getElementById('evolutionEvents').textContent = dnaDiceDecoderGame.stats.evolutionEvents;

    const netResult = dnaDiceDecoderGame.stats.totalWon - dnaDiceDecoderGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after roll
function updateGameStatsAfterRoll(won, winnings) {
    dnaDiceDecoderGame.stats.gamesPlayed++;
    dnaDiceDecoderGame.stats.totalWagered += dnaDiceDecoderGame.betAmount;
    dnaDiceDecoderGame.stats.totalWon += winnings;

    if (won) {
        dnaDiceDecoderGame.stats.gamesWon++;
        dnaDiceDecoderGame.stats.currentStreak++;
        dnaDiceDecoderGame.streakData.currentWinStreak++;
        dnaDiceDecoderGame.streakData.currentLossStreak = 0;

        if (dnaDiceDecoderGame.streakData.currentWinStreak > dnaDiceDecoderGame.streakData.longestWinStreak) {
            dnaDiceDecoderGame.streakData.longestWinStreak = dnaDiceDecoderGame.streakData.currentWinStreak;
        }

        if (winnings > dnaDiceDecoderGame.stats.biggestWin) {
            dnaDiceDecoderGame.stats.biggestWin = winnings;
        }
    } else {
        dnaDiceDecoderGame.stats.currentStreak = 0;
        dnaDiceDecoderGame.streakData.currentWinStreak = 0;
        dnaDiceDecoderGame.streakData.currentLossStreak++;

        if (dnaDiceDecoderGame.streakData.currentLossStreak > dnaDiceDecoderGame.streakData.longestLossStreak) {
            dnaDiceDecoderGame.streakData.longestLossStreak = dnaDiceDecoderGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to DNA mechanics)
    dnaDiceDecoderGame.stats.winRate = (dnaDiceDecoderGame.stats.gamesWon / dnaDiceDecoderGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next DNA analysis
function resetGame() {
    dnaDiceDecoderGame.isPlaying = false;
    dnaDiceDecoderGame.betAmount = 0;
    dnaDiceDecoderGame.totalBet = 0;
    dnaDiceDecoderGame.gameResult = '';
    dnaDiceDecoderGame.totalWin = 0;

    // Reset dice system
    dnaDiceDecoderGame.dice.values = [1, 1, 1, 1];
    dnaDiceDecoderGame.dice.actualSequence = '';
    dnaDiceDecoderGame.dice.geneticMatch = false;
    dnaDiceDecoderGame.dice.mutationBonus = 1.0;
    dnaDiceDecoderGame.dice.evolutionMultiplier = 1.0;
    dnaDiceDecoderGame.dice.biometricBonus = 1.0;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('dnaEvent').classList.add('hidden');
    document.getElementById('dnaActions').classList.add('hidden');

    // Reset dice display
    const dice = document.querySelectorAll('.dna-die');
    dice.forEach((die, index) => {
        die.querySelector('.text-xs').textContent = '1';
        die.className = 'dna-die w-16 h-16 bg-gradient-to-br from-green-600 to-green-800 rounded-lg border-2 border-green-400 flex flex-col items-center justify-center text-white transition-all duration-500';
    });

    // Reset sequence display
    document.getElementById('sequenceStatus').textContent = 'Ready for DNA analysis...';
    document.getElementById('actualSequence').textContent = '----';

    // Reset analysis display
    document.getElementById('analysisBar').style.width = '0%';
    document.getElementById('analysisStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable analyze button
    document.getElementById('analyzeDNA').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'DNA ready for analysis...';
    document.getElementById('gameMessage').textContent = 'Welcome to DNA Dice Decoder - Biometric Risk Profiling';

    // Generate new target sequence
    dnaDiceDecoderGame.dice.targetSequence = generateRandomDNASequence();
    updateDNASequenceDisplay();

    // Reinitialize systems for next analysis
    initializeDNASystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDNADiceDecoderGame();
});