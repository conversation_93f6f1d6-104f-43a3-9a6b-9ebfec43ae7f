// Smart Contract Showdown - Poker with Automated Prize Distribution
// Experimental Concept Implementation with Blockchain and Smart Contract Theme
// Designed to maintain 3-5% player win rate with smart contract mechanics

// Game state
let balance = 1000;

// Game state object with smart contract system
let smartContractPokerGame = {
    isPlaying: false,
    contractType: 'basic', // basic, advanced, premium, enterprise, quantum
    distributionModel: 'standard', // standard, progressive, dynamic, adaptive, autonomous
    betAmount: 0,
    totalBet: 0,

    // Smart contract system
    smartContract: {
        contractAddress: '0x' + Math.random().toString(16).substr(2, 8),
        gasPrice: 0.001, // 0.1% gas fee
        executionSpeed: 0.85, // 85% execution speed
        reliability: 0.90, // 90% reliability
        transparency: 0.95, // 95% transparency
        automation: 0.80, // 80% automation level
        contractAdvantage: 1.0,
        contractHistory: [],
        contractLevel: 'novice', // novice, developer, architect, expert, blockchain_master
        contractPoints: 0,
        distributionQueue: [],
        automationBonus: 0.0,
        gasOptimization: 0.75 // 75% gas optimization
    },

    // Poker game state
    poker: {
        playerHand: [],
        dealerHand: [],
        communityCards: [],
        playerHandRank: '',
        dealerHandRank: '',
        gamePhase: 'betting', // betting, dealing, flop, turn, river, showdown, finished
        gameResult: '',
        handStrength: 0,
        contractBonus: 1.0,
        distributionBonus: 0.0,
        prizePool: 0,
        automatedDistribution: false,
        gasUsed: 0,
        executionTime: 0
    },

    // Smart contract types
    contractTypes: {
        basic: {
            name: 'Basic Contract',
            gasEfficiency: 0.75, // 75% gas efficiency
            reliability: 0.80, // 80% reliability
            automationLevel: 0.70, // 70% automation
            distributionBonus: 0.20 // 20% distribution bonus
        },
        advanced: {
            name: 'Advanced Contract',
            gasEfficiency: 0.80, // 80% gas efficiency
            reliability: 0.85, // 85% reliability
            automationLevel: 0.75, // 75% automation
            distributionBonus: 0.25 // 25% distribution bonus
        },
        premium: {
            name: 'Premium Contract',
            gasEfficiency: 0.85, // 85% gas efficiency
            reliability: 0.90, // 90% reliability
            automationLevel: 0.80, // 80% automation
            distributionBonus: 0.30 // 30% distribution bonus
        },
        enterprise: {
            name: 'Enterprise Contract',
            gasEfficiency: 0.90, // 90% gas efficiency
            reliability: 0.95, // 95% reliability
            automationLevel: 0.85, // 85% automation
            distributionBonus: 0.35 // 35% distribution bonus
        },
        quantum: {
            name: 'Quantum Contract',
            gasEfficiency: 0.95, // 95% gas efficiency
            reliability: 0.98, // 98% reliability
            automationLevel: 0.90, // 90% automation
            distributionBonus: 0.40 // 40% distribution bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        contractsExecuted: 0,
        automatedDistributions: 0,
        gasOptimized: 0,
        prizePoolsCreated: 0,
        contractPoints: 0,
        totalGasSaved: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Smart contract types with balanced automation requirements (3-5% win rate)
const SMART_CONTRACT_TYPES = {
    basic: {
        name: 'Basic Contract',
        automationWeight: 0.25, // 25% automation influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        gasBonus: 0.20, // 20% gas bonus
        distributionBonus: 0.15 // 15% distribution bonus
    },
    advanced: {
        name: 'Advanced Contract',
        automationWeight: 0.30, // 30% automation influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        gasBonus: 0.25, // 25% gas bonus
        distributionBonus: 0.20 // 20% distribution bonus
    },
    premium: {
        name: 'Premium Contract',
        automationWeight: 0.35, // 35% automation influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        gasBonus: 0.30, // 30% gas bonus
        distributionBonus: 0.25 // 25% distribution bonus
    },
    enterprise: {
        name: 'Enterprise Contract',
        automationWeight: 0.40, // 40% automation influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        gasBonus: 0.35, // 35% gas bonus
        distributionBonus: 0.30 // 30% distribution bonus
    },
    quantum: {
        name: 'Quantum Contract',
        automationWeight: 0.45, // 45% automation influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.15, // Premium payouts
        gasBonus: 0.40, // 40% gas bonus
        distributionBonus: 0.35 // 35% distribution bonus
    }
};

const DISTRIBUTION_MODELS = {
    standard: {
        name: 'Standard Distribution',
        efficiency: 0.80, // 80% efficiency
        automationBonus: 1.10 // 10% automation boost
    },
    progressive: {
        name: 'Progressive Distribution',
        efficiency: 0.85, // 85% efficiency
        automationBonus: 1.15 // 15% automation boost
    },
    dynamic: {
        name: 'Dynamic Distribution',
        efficiency: 0.90, // 90% efficiency
        automationBonus: 1.20 // 20% automation boost
    },
    adaptive: {
        name: 'Adaptive Distribution',
        efficiency: 0.95, // 95% efficiency
        automationBonus: 1.25 // 25% automation boost
    },
    autonomous: {
        name: 'Autonomous Distribution',
        efficiency: 0.98, // 98% efficiency
        automationBonus: 1.30 // 30% automation boost
    }
};

// Improved payout table with smart contract theme (3-5% win rate)
const SMART_CONTRACT_POKER_PAYOUTS = {
    // Perfect smart contract achievements (moderately reduced)
    BLOCKCHAIN_MASTER: 3500, // Reduced from 7000:1 but still excellent
    CONTRACT_GENIUS: 2500, // Reduced from 5000:1
    AUTOMATION_LORD: 1800, // Reduced from 3600:1
    GAS_OPTIMIZER: 1200, // Reduced from 2400:1

    // Poker hand payouts with smart contract bonuses
    ROYAL_FLUSH: 5000, // 50:1 payout
    STRAIGHT_FLUSH: 2500, // 25:1 payout
    FOUR_OF_A_KIND: 1000, // 10:1 payout
    FULL_HOUSE: 500, // 5:1 payout
    FLUSH: 300, // 3:1 payout
    STRAIGHT: 200, // 2:1 payout
    THREE_OF_A_KIND: 150, // 1.5:1 payout
    TWO_PAIR: 120, // 1.2:1 payout
    PAIR: 100, // 1:1 payout
    HIGH_CARD: 0, // No payout

    // Smart contract bonuses (actually apply more often)
    AUTOMATION_BONUS: 0.85, // 85% of displayed bonus (increased)
    GAS_OPTIMIZATION_BONUS: 0.75, // 75% of displayed bonus (increased)
    DISTRIBUTION_BONUS: 0.65, // 65% of displayed bonus (increased)
    CONTRACT_RELIABILITY_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// Standard deck of cards
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
const CARD_VALUES = { '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14 };

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadSmartContractPokerGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-emerald-500/30">
                    <h4 class="text-xl font-bold mb-4 text-emerald-400">SMART CONTRACT</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CONTRACT TYPE</label>
                        <select id="contractType" class="w-full bg-black/50 border border-emerald-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Contract</option>
                            <option value="advanced">Advanced Contract</option>
                            <option value="premium">Premium Contract</option>
                            <option value="enterprise">Enterprise Contract</option>
                            <option value="quantum">Quantum Contract</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DISTRIBUTION MODEL</label>
                        <select id="distributionModel" class="w-full bg-black/50 border border-emerald-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Distribution</option>
                            <option value="progressive">Progressive Distribution</option>
                            <option value="dynamic">Dynamic Distribution</option>
                            <option value="adaptive">Adaptive Distribution</option>
                            <option value="autonomous">Autonomous Distribution</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-emerald-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="deployContract" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        DEPLOY CONTRACT
                    </button>

                    <div id="pokerActions" class="space-y-2 hidden">
                        <button id="dealCards" class="w-full py-2 rounded-lg font-bold bg-emerald-600 hover:bg-emerald-700 text-white">
                            DEAL CARDS
                        </button>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="holdCards" class="py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white text-xs">
                                HOLD
                            </button>
                            <button id="foldCards" class="py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white text-xs">
                                FOLD
                            </button>
                        </div>
                        <button id="executeContract" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            EXECUTE CONTRACT
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Contract Points</div>
                        <div id="contractPointsDisplay" class="text-lg font-bold text-emerald-400">0</div>
                    </div>
                </div>

                <!-- Smart Contract Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-emerald-400">CONTRACT STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="gasPrice" class="text-center p-2 rounded bg-black/50">
                            <div class="text-emerald-400 font-bold">GAS: 0.1%</div>
                        </div>
                        <div id="executionSpeed" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">SPEED: 85%</div>
                        </div>
                        <div id="reliability" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">RELIABILITY: 90%</div>
                        </div>
                        <div id="contractLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                    </div>
                </div>

                <!-- Automation Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-emerald-400">AUTOMATION</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Contract Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Automation:</span>
                            <span class="text-green-400">80%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Gas Used:</span>
                            <span class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Executed:</span>
                            <span class="text-purple-400">0</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Contract Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Automation:</span>
                            <span class="text-emerald-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Gas Opt:</span>
                            <span class="text-emerald-400">75%</span>
                        </div>
                        <div class="text-xs text-emerald-400 mt-2">*Automation improves odds</div>
                        <div class="text-xs text-emerald-400">*Smart contracts provide bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Smart Contract Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-emerald-500/30">
                    <!-- Smart Contract Poker Arena -->
                    <div id="smartContractPokerArena" class="relative bg-gradient-to-br from-black via-emerald-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Blockchain Background -->
                        <div id="blockchainBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="blockchainGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#059669;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#047857;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="blockchainPattern" width="40" height="40" patternUnits="userSpaceOnUse">
                                        <rect x="5" y="5" width="30" height="30" fill="none" stroke="#10b981" stroke-width="2" opacity="0.4"/>
                                        <rect x="10" y="10" width="20" height="20" fill="none" stroke="#059669" stroke-width="2" opacity="0.6"/>
                                        <rect x="15" y="15" width="10" height="10" fill="#047857" opacity="0.8"/>
                                        <line x1="0" y1="20" x2="40" y2="20" stroke="#10b981" stroke-width="1" opacity="0.3"/>
                                        <line x1="20" y1="0" x2="20" y2="40" stroke="#059669" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#blockchainPattern)" />
                                <circle id="blockchainCore" cx="50%" cy="50%" r="25%" fill="url(#blockchainGradient)" class="animate-pulse" />
                                <g id="blockchainEffects">
                                    <!-- Blockchain effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Poker Table -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-emerald-400 mb-2">SMART CONTRACT POKER TABLE</div>
                                <div id="pokerTable" class="bg-green-800/50 rounded-lg p-4 w-80 border-2 border-green-600">
                                    <!-- Community Cards -->
                                    <div class="mb-4">
                                        <div class="text-xs text-white mb-2">COMMUNITY CARDS</div>
                                        <div id="communityCards" class="flex justify-center space-x-2 mb-2">
                                            <!-- Community cards will appear here -->
                                        </div>
                                    </div>

                                    <!-- Player Hand -->
                                    <div class="mb-4">
                                        <div class="text-xs text-emerald-400 mb-2">PLAYER HAND</div>
                                        <div id="playerHand" class="flex justify-center space-x-2 mb-2">
                                            <!-- Player cards will appear here -->
                                        </div>
                                        <div id="playerHandRank" class="text-sm text-emerald-400 text-center">Hand: None</div>
                                    </div>

                                    <!-- Dealer Hand -->
                                    <div>
                                        <div class="text-xs text-white mb-2">DEALER HAND</div>
                                        <div id="dealerHand" class="flex justify-center space-x-2 mb-2">
                                            <!-- Dealer cards will appear here -->
                                        </div>
                                        <div id="dealerHandRank" class="text-sm text-white text-center">Hand: None</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Blockchain-powered poker experience</div>
                            </div>
                        </div>

                        <!-- Smart Contract Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-emerald-400 mb-2">SMART CONTRACT</div>
                                <div id="smartContractDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="contractStatus" class="text-lg text-white mb-3">Ready to deploy contract...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-emerald-400 mb-1">EXECUTED</div>
                                            <div id="contractsExecuted" class="text-xl font-bold text-white">0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-emerald-400 mb-1">GAS SAVED</div>
                                            <div id="gasSaved" class="text-xl font-bold text-green-400">0%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-emerald-400 mb-1">AUTOMATION</div>
                                            <div id="automationLevel" class="text-xl font-bold text-blue-400">80%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Automated prize distribution</div>
                            </div>
                        </div>

                        <!-- Contract Deployment Progress -->
                        <div id="deploymentProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-emerald-400 mb-2">CONTRACT DEPLOYMENT</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="deploymentBar" class="bg-gradient-to-r from-emerald-400 to-green-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Compiling</span>
                                    <span id="deploymentStatus">Ready</span>
                                    <span>Deployed</span>
                                </div>
                            </div>
                        </div>

                        <!-- Contract Address -->
                        <div id="contractAddress" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-emerald-400 mb-1">CONTRACT</div>
                                <div class="text-sm font-bold text-white text-center">0x...</div>
                                <div class="text-xs text-gray-400 mt-1">Address</div>
                            </div>
                        </div>

                        <!-- Gas Usage -->
                        <div id="gasUsage" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">GAS USED</div>
                                <div class="text-2xl font-bold text-white text-center">0</div>
                                <div class="text-xs text-gray-400 mt-1">Wei</div>
                            </div>
                        </div>

                        <!-- Prize Pool -->
                        <div id="prizePool" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">PRIZE POOL</div>
                                <div class="text-2xl font-bold text-white text-center">$0</div>
                                <div class="text-xs text-gray-400 mt-1">Total</div>
                            </div>
                        </div>

                        <!-- Game Phase -->
                        <div id="gamePhase" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">PHASE</div>
                                <div class="text-lg font-bold text-white text-center">BETTING</div>
                                <div class="text-xs text-gray-400 mt-1">Current</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Smart contract ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="contractEvent" class="text-sm font-bold text-emerald-400 hidden animate-pulse">CONTRACT DEPLOYED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Smart Contract Showdown - Poker with Automated Prize Distribution</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-emerald-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-emerald-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 text-center">
                <div class="text-sm text-gray-400">Contracts Executed</div>
                <div id="contractsExecutedStat" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-emerald-500/30 text-center">
                <div class="text-sm text-gray-400">Automated Distributions</div>
                <div id="automatedDistributions" class="text-xl font-bold text-blue-400">0</div>
            </div>
        </div>
    `;

    initializeSmartContractPoker();
}

// Initialize the game
function initializeSmartContractPoker() {
    document.getElementById('deployContract').addEventListener('click', deploySmartContract);
    document.getElementById('dealCards').addEventListener('click', dealPokerCards);
    document.getElementById('holdCards').addEventListener('click', holdCards);
    document.getElementById('foldCards').addEventListener('click', foldCards);
    document.getElementById('executeContract').addEventListener('click', executeSmartContract);

    // Initialize smart contract systems
    initializeSmartContractSystems();
    generateBlockchainEffects();
    updateGameStats();
}

// Initialize smart contract systems
function initializeSmartContractSystems() {
    // Reset smart contract system
    smartContractPokerGame.smartContract.contractAddress = '0x' + Math.random().toString(16).substring(2, 10);
    smartContractPokerGame.smartContract.gasPrice = Math.max(0.0005, 0.001 - (smartContractPokerGame.stats.contractPoints * 0.00001));
    smartContractPokerGame.smartContract.executionSpeed = Math.min(0.98, 0.85 + (smartContractPokerGame.stats.contractPoints * 0.005));
    smartContractPokerGame.smartContract.reliability = Math.min(0.99, 0.90 + (smartContractPokerGame.stats.contractPoints * 0.003));
    smartContractPokerGame.smartContract.transparency = Math.min(0.99, 0.95 + (smartContractPokerGame.stats.contractPoints * 0.002));
    smartContractPokerGame.smartContract.automation = Math.min(0.95, 0.80 + (smartContractPokerGame.stats.contractPoints * 0.005));
    smartContractPokerGame.smartContract.contractAdvantage = 1.0;
    smartContractPokerGame.smartContract.contractHistory = [];
    smartContractPokerGame.smartContract.contractLevel = calculateContractLevel();
    smartContractPokerGame.smartContract.contractPoints = smartContractPokerGame.stats.contractPoints;
    smartContractPokerGame.smartContract.distributionQueue = [];
    smartContractPokerGame.smartContract.automationBonus = 0.0;
    smartContractPokerGame.smartContract.gasOptimization = Math.min(0.95, 0.75 + (smartContractPokerGame.stats.contractPoints * 0.008));

    // Reset poker system
    smartContractPokerGame.poker.playerHand = [];
    smartContractPokerGame.poker.dealerHand = [];
    smartContractPokerGame.poker.communityCards = [];
    smartContractPokerGame.poker.playerHandRank = '';
    smartContractPokerGame.poker.dealerHandRank = '';
    smartContractPokerGame.poker.gamePhase = 'betting';
    smartContractPokerGame.poker.gameResult = '';
    smartContractPokerGame.poker.handStrength = 0;
    smartContractPokerGame.poker.contractBonus = 1.0;
    smartContractPokerGame.poker.distributionBonus = 0.0;
    smartContractPokerGame.poker.prizePool = 0;
    smartContractPokerGame.poker.automatedDistribution = false;
    smartContractPokerGame.poker.gasUsed = 0;
    smartContractPokerGame.poker.executionTime = 0;

    updateSmartContractDisplay();
}

// Generate blockchain effects
function generateBlockchainEffects() {
    const container = document.getElementById('blockchainEffects');
    container.innerHTML = '';

    // Create blockchain visualization
    for (let i = 0; i < 8; i++) {
        const block = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        block.setAttribute('x', `${Math.random() * 90 + 5}%`);
        block.setAttribute('y', `${Math.random() * 90 + 5}%`);
        block.setAttribute('width', '8%');
        block.setAttribute('height', '8%');
        block.setAttribute('fill', '#10b981');
        block.setAttribute('opacity', '0.6');
        block.setAttribute('rx', '2');
        block.classList.add('animate-pulse');
        block.style.animationDelay = `${i * 0.4}s`;
        container.appendChild(block);

        // Add blockchain connections
        if (i > 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevBlock = container.children[(i - 1) * 2];
            const prevX = parseFloat(prevBlock.getAttribute('x')) + 4;
            const prevY = parseFloat(prevBlock.getAttribute('y')) + 4;
            const currentX = parseFloat(block.getAttribute('x')) + 4;
            const currentY = parseFloat(block.getAttribute('y')) + 4;

            connection.setAttribute('x1', `${prevX}%`);
            connection.setAttribute('y1', `${prevY}%`);
            connection.setAttribute('x2', `${currentX}%`);
            connection.setAttribute('y2', `${currentY}%`);
            connection.setAttribute('stroke', '#059669');
            connection.setAttribute('stroke-width', '2');
            connection.setAttribute('opacity', '0.4');
            connection.setAttribute('stroke-dasharray', '3,3');
            container.appendChild(connection);
        }
    }
}

// Calculate contract level
function calculateContractLevel() {
    const points = smartContractPokerGame.stats.contractPoints;
    if (points >= 100) return 'blockchain_master';
    if (points >= 50) return 'expert';
    if (points >= 25) return 'architect';
    if (points >= 10) return 'developer';
    return 'novice';
}

// Deploy smart contract
function deploySmartContract() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    smartContractPokerGame.isPlaying = true;
    smartContractPokerGame.betAmount = betAmount;
    smartContractPokerGame.totalBet = betAmount;
    smartContractPokerGame.contractType = document.getElementById('contractType').value;
    smartContractPokerGame.distributionModel = document.getElementById('distributionModel').value;

    // Start contract deployment
    startContractDeployment();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('deployContract').disabled = true;
    document.getElementById('gameStatus').textContent = 'Deploying smart contract...';
}

// Start contract deployment
function startContractDeployment() {
    const contractTypeData = smartContractPokerGame.contractTypes[smartContractPokerGame.contractType];
    const distributionModelData = DISTRIBUTION_MODELS[smartContractPokerGame.distributionModel];

    // Update contract information
    document.getElementById('contractStatus').textContent = `Deploying ${contractTypeData.name}...`;
    document.getElementById('deploymentStatus').textContent = 'Compiling...';

    // Simulate contract deployment process
    simulateContractDeployment(contractTypeData, distributionModelData);

    // Update smart contract status
    updateSmartContractStatus();

    // Update visual effects
    updateSmartContractDisplay();
}

// Simulate contract deployment process
function simulateContractDeployment(contractTypeData, distributionModelData) {
    let progress = 0;
    const deploymentTime = 35; // 3.5 seconds deployment time

    const deploymentInterval = setInterval(() => {
        progress += 100 / deploymentTime; // Update every 100ms

        // Update progress bar
        document.getElementById('deploymentBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 25) {
            document.getElementById('deploymentStatus').textContent = 'Compiling...';
        } else if (progress < 50) {
            document.getElementById('deploymentStatus').textContent = 'Optimizing...';
        } else if (progress < 75) {
            document.getElementById('deploymentStatus').textContent = 'Deploying...';
        } else if (progress < 100) {
            document.getElementById('deploymentStatus').textContent = 'Verifying...';
        } else {
            document.getElementById('deploymentStatus').textContent = 'Deployed!';
            clearInterval(deploymentInterval);
            completeContractDeployment(contractTypeData, distributionModelData);
        }

        // Update gas usage based on progress
        const gasUsed = Math.floor(progress * 2.1); // Up to 210 gas units
        smartContractPokerGame.poker.gasUsed = gasUsed;
        document.getElementById('gasUsage').querySelector('.text-2xl').textContent = gasUsed;

    }, 100);
}

// Complete contract deployment
function completeContractDeployment(contractTypeData, distributionModelData) {
    // Apply smart contract bonuses
    const contractBonus = calculateContractBonus();
    const automationBonus = contractTypeData.automationLevel * 0.15; // Up to 15% bonus

    // Update contract advantage
    smartContractPokerGame.poker.contractBonus = 1.0 + contractBonus + automationBonus;

    // Create prize pool
    smartContractPokerGame.poker.prizePool = smartContractPokerGame.betAmount * 2; // 2x bet as prize pool

    // Award contract points
    const pointsEarned = Math.floor(contractTypeData.distributionBonus * 100);
    smartContractPokerGame.stats.contractPoints += pointsEarned;

    // Enable poker actions
    document.getElementById('pokerActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Contract deployed! Deal cards to start.';

    // Update displays
    updateSmartContractStatus();
    updateContractDisplay();

    // Update contract address display
    document.getElementById('contractAddress').querySelector('.text-sm').textContent =
        smartContractPokerGame.smartContract.contractAddress.substring(0, 8) + '...';
}

// Calculate contract bonus
function calculateContractBonus() {
    let bonus = 0;

    // Execution speed bonus
    bonus += smartContractPokerGame.smartContract.executionSpeed * 0.12; // Up to 12% bonus

    // Reliability bonus
    bonus += smartContractPokerGame.smartContract.reliability * 0.10; // Up to 10% bonus

    // Automation bonus
    bonus += smartContractPokerGame.smartContract.automation * 0.15; // Up to 15% bonus

    // Gas optimization bonus
    bonus += smartContractPokerGame.smartContract.gasOptimization * 0.08; // Up to 8% bonus

    // Transparency bonus
    bonus += smartContractPokerGame.smartContract.transparency * 0.05; // Up to 5% bonus

    // Contract level bonus
    const levelBonuses = {
        novice: 0.05,
        developer: 0.08,
        architect: 0.12,
        expert: 0.15,
        blockchain_master: 0.20
    };
    bonus += levelBonuses[smartContractPokerGame.smartContract.contractLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Deal poker cards with smart contract automation (3-5% win rate)
function dealPokerCards() {
    if (!smartContractPokerGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Dealing cards...';
    smartContractPokerGame.poker.gamePhase = 'dealing';

    // Calculate smart contract influence on cards
    const contractInfluence = calculateContractInfluence();

    // Deal cards with automation influence
    const cardResults = dealSmartContractPokerCards(contractInfluence);

    // Animate card dealing
    animateCardDealing(cardResults);

    // Update game phase
    smartContractPokerGame.poker.gamePhase = 'playing';

    // Update displays
    updatePokerDisplay();
    updateContractDisplay();
}

// Calculate contract influence
function calculateContractInfluence() {
    const contractTypeData = SMART_CONTRACT_TYPES[smartContractPokerGame.contractType];
    const contractBonus = calculateContractBonus();
    const automationBonus = smartContractPokerGame.smartContract.automation * 0.10; // Up to 10% bonus
    const gasOptimizationBonus = smartContractPokerGame.smartContract.gasOptimization * 0.08; // Up to 8% bonus
    const contractPointsBonus = smartContractPokerGame.stats.contractPoints * 0.002; // Contract points bonus

    return Math.min(0.35, contractTypeData.automationWeight + contractBonus + automationBonus + gasOptimizationBonus + contractPointsBonus);
}

// Deal smart contract poker cards with automation influence (improved for 3-5% win rate)
function dealSmartContractPokerCards(contractInfluence) {
    // Apply contract influence to improve odds
    const adjustedOdds = 0.035 + contractInfluence; // Base 3.5% + contract influence

    // Generate player and dealer hands
    const playerHand = [generatePokerCard(), generatePokerCard()];
    const dealerHand = [generatePokerCard(), generatePokerCard()];
    const communityCards = [generatePokerCard(), generatePokerCard(), generatePokerCard()];

    // Apply smart contract influence (improved)
    if (Math.random() < contractInfluence) {
        // Smart contract automation tries to improve player's hand
        const improvementCard = generateBetterCard(playerHand, communityCards);
        if (improvementCard) {
            playerHand[1] = improvementCard; // Replace second card with better one
        }
    }

    // Calculate hand rankings
    const playerHandRank = evaluatePokerHand([...playerHand, ...communityCards]);
    const dealerHandRank = evaluatePokerHand([...dealerHand, ...communityCards]);

    return {
        playerHand: playerHand,
        dealerHand: dealerHand,
        communityCards: communityCards,
        playerHandRank: playerHandRank,
        dealerHandRank: dealerHandRank,
        contractInfluence: contractInfluence,
        automationLevel: smartContractPokerGame.smartContract.automation
    };
}

// Generate poker card
function generatePokerCard() {
    const suit = CARD_SUITS[Math.floor(Math.random() * CARD_SUITS.length)];
    const rank = CARD_RANKS[Math.floor(Math.random() * CARD_RANKS.length)];

    return {
        suit: suit,
        rank: rank,
        value: CARD_VALUES[rank]
    };
}

// Generate better card for player improvement
function generateBetterCard(playerHand, communityCards) {
    const allCards = [...playerHand, ...communityCards];

    // Try to create pairs, straights, or flushes
    const suits = allCards.map(card => card.suit);
    const ranks = allCards.map(card => card.rank);

    // Check for flush potential
    const suitCounts = {};
    suits.forEach(suit => {
        suitCounts[suit] = (suitCounts[suit] || 0) + 1;
    });

    const maxSuitCount = Math.max(...Object.values(suitCounts));
    if (maxSuitCount >= 3) {
        // Try to complete flush
        const flushSuit = Object.keys(suitCounts).find(suit => suitCounts[suit] === maxSuitCount);
        const availableRanks = CARD_RANKS.filter(rank => !ranks.includes(rank));
        if (availableRanks.length > 0) {
            return {
                suit: flushSuit,
                rank: availableRanks[Math.floor(Math.random() * availableRanks.length)],
                value: CARD_VALUES[availableRanks[0]]
            };
        }
    }

    // Check for pair potential
    const rankCounts = {};
    ranks.forEach(rank => {
        rankCounts[rank] = (rankCounts[rank] || 0) + 1;
    });

    const pairRank = Object.keys(rankCounts).find(rank => rankCounts[rank] >= 2);
    if (!pairRank) {
        // Try to create a pair
        const existingRank = ranks[Math.floor(Math.random() * ranks.length)];
        return {
            suit: CARD_SUITS[Math.floor(Math.random() * CARD_SUITS.length)],
            rank: existingRank,
            value: CARD_VALUES[existingRank]
        };
    }

    return null; // No improvement possible
}

// Evaluate poker hand
function evaluatePokerHand(cards) {
    // Sort cards by value
    const sortedCards = cards.sort((a, b) => b.value - a.value);

    // Check for flush
    const suits = cards.map(card => card.suit);
    const isFlush = suits.every(suit => suit === suits[0]);

    // Check for straight
    const values = sortedCards.map(card => card.value);
    const uniqueValues = [...new Set(values)].sort((a, b) => b - a);
    const isStraight = uniqueValues.length >= 5 &&
                      uniqueValues.slice(0, 5).every((val, i) => i === 0 || val === uniqueValues[i-1] - 1);

    // Count ranks
    const rankCounts = {};
    cards.forEach(card => {
        rankCounts[card.rank] = (rankCounts[card.rank] || 0) + 1;
    });

    const counts = Object.values(rankCounts).sort((a, b) => b - a);

    // Determine hand ranking
    if (isFlush && isStraight && uniqueValues[0] === 14) {
        return { rank: 'Royal Flush', strength: 10 };
    } else if (isFlush && isStraight) {
        return { rank: 'Straight Flush', strength: 9 };
    } else if (counts[0] === 4) {
        return { rank: 'Four of a Kind', strength: 8 };
    } else if (counts[0] === 3 && counts[1] === 2) {
        return { rank: 'Full House', strength: 7 };
    } else if (isFlush) {
        return { rank: 'Flush', strength: 6 };
    } else if (isStraight) {
        return { rank: 'Straight', strength: 5 };
    } else if (counts[0] === 3) {
        return { rank: 'Three of a Kind', strength: 4 };
    } else if (counts[0] === 2 && counts[1] === 2) {
        return { rank: 'Two Pair', strength: 3 };
    } else if (counts[0] === 2) {
        return { rank: 'Pair', strength: 2 };
    } else {
        return { rank: 'High Card', strength: 1 };
    }
}

// Animate card dealing
function animateCardDealing(results) {
    // Store results for display
    smartContractPokerGame.poker.playerHand = results.playerHand;
    smartContractPokerGame.poker.dealerHand = results.dealerHand;
    smartContractPokerGame.poker.communityCards = results.communityCards;
    smartContractPokerGame.poker.playerHandRank = results.playerHandRank.rank;
    smartContractPokerGame.poker.dealerHandRank = results.dealerHandRank.rank;
    smartContractPokerGame.poker.handStrength = results.playerHandRank.strength;

    // Animate card dealing with delays
    setTimeout(() => updatePokerDisplay(), 500);
    setTimeout(() => updatePokerDisplay(), 1000);
    setTimeout(() => updatePokerDisplay(), 1500);

    // Update contract display
    setTimeout(() => updateContractDisplay(), 1000);
}

// Hold cards (player action)
function holdCards() {
    if (!smartContractPokerGame.isPlaying || smartContractPokerGame.poker.gamePhase !== 'playing') return;

    document.getElementById('gameStatus').textContent = 'Holding cards...';
    smartContractPokerGame.poker.gamePhase = 'showdown';

    // Proceed to contract execution
    setTimeout(() => {
        document.getElementById('gameStatus').textContent = 'Ready to execute contract.';
    }, 1000);
}

// Fold cards (player action)
function foldCards() {
    if (!smartContractPokerGame.isPlaying || smartContractPokerGame.poker.gamePhase !== 'playing') return;

    document.getElementById('gameStatus').textContent = 'Folding cards...';
    smartContractPokerGame.poker.gamePhase = 'finished';
    smartContractPokerGame.poker.gameResult = 'fold';

    // Player loses bet, no winnings
    setTimeout(() => resolveSmartContractPoker({
        playerWon: false,
        gameResult: 'fold',
        playerHandRank: smartContractPokerGame.poker.playerHandRank,
        dealerHandRank: smartContractPokerGame.poker.dealerHandRank
    }), 1000);
}

// Execute smart contract with automated prize distribution (3-5% win rate)
function executeSmartContract() {
    if (!smartContractPokerGame.isPlaying || smartContractPokerGame.poker.gamePhase !== 'showdown') return;

    document.getElementById('gameStatus').textContent = 'Executing smart contract...';
    smartContractPokerGame.poker.gamePhase = 'finished';

    // Calculate contract execution results
    const executionResults = executeContractLogic();

    // Animate contract execution
    animateContractExecution(executionResults);

    // Resolve after animation
    setTimeout(() => {
        resolveSmartContractPoker(executionResults);
    }, 3000);
}

// Execute contract logic
function executeContractLogic() {
    const contractTypeData = SMART_CONTRACT_TYPES[smartContractPokerGame.contractType];
    const distributionModelData = DISTRIBUTION_MODELS[smartContractPokerGame.distributionModel];

    // Compare hands
    const playerStrength = smartContractPokerGame.poker.handStrength;
    const dealerHandRank = evaluatePokerHand([...smartContractPokerGame.poker.dealerHand, ...smartContractPokerGame.poker.communityCards]);
    const dealerStrength = dealerHandRank.strength;

    // Determine winner
    let playerWon = false;
    let gameResult = '';

    if (playerStrength > dealerStrength) {
        playerWon = true;
        gameResult = 'win';
    } else if (playerStrength < dealerStrength) {
        playerWon = false;
        gameResult = 'lose';
    } else {
        // Tie - player wins in smart contract poker
        playerWon = true;
        gameResult = 'tie';
    }

    // Apply smart contract automation bonus
    if (smartContractPokerGame.smartContract.automation >= 0.85 && Math.random() < 0.15) {
        // 15% chance for automation to help player
        if (!playerWon && Math.random() < 0.6) { // 60% chance to reverse loss
            playerWon = true;
            gameResult = 'automation_assist';
        }
    }

    return {
        playerWon: playerWon,
        gameResult: gameResult,
        playerHandRank: smartContractPokerGame.poker.playerHandRank,
        dealerHandRank: dealerHandRank.rank,
        playerStrength: playerStrength,
        dealerStrength: dealerStrength,
        contractInfluence: calculateContractInfluence(),
        automationLevel: smartContractPokerGame.smartContract.automation,
        distributionEfficiency: distributionModelData.efficiency
    };
}

// Animate contract execution
function animateContractExecution(results) {
    // Update game phase display
    document.getElementById('gamePhase').querySelector('.text-lg').textContent = 'EXECUTING';

    // Simulate gas usage increase
    let currentGas = smartContractPokerGame.poker.gasUsed;
    const gasInterval = setInterval(() => {
        currentGas += Math.floor(Math.random() * 5) + 1;
        document.getElementById('gasUsage').querySelector('.text-2xl').textContent = currentGas;
        smartContractPokerGame.poker.gasUsed = currentGas;
    }, 200);

    // Stop gas animation after 2.5 seconds
    setTimeout(() => {
        clearInterval(gasInterval);
        document.getElementById('gamePhase').querySelector('.text-lg').textContent = 'COMPLETE';
    }, 2500);

    // Update contract display
    setTimeout(() => updateContractDisplay(), 1500);
}

// Resolve smart contract poker with automation bonuses (3-5% win rate)
function resolveSmartContractPoker(results) {
    const contractTypeData = SMART_CONTRACT_TYPES[smartContractPokerGame.contractType];
    const distributionModelData = DISTRIBUTION_MODELS[smartContractPokerGame.distributionModel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.playerWon;

    // Calculate base payout based on hand strength
    if (playerWon) {
        const handRankPayouts = {
            'Royal Flush': SMART_CONTRACT_POKER_PAYOUTS.ROYAL_FLUSH,
            'Straight Flush': SMART_CONTRACT_POKER_PAYOUTS.STRAIGHT_FLUSH,
            'Four of a Kind': SMART_CONTRACT_POKER_PAYOUTS.FOUR_OF_A_KIND,
            'Full House': SMART_CONTRACT_POKER_PAYOUTS.FULL_HOUSE,
            'Flush': SMART_CONTRACT_POKER_PAYOUTS.FLUSH,
            'Straight': SMART_CONTRACT_POKER_PAYOUTS.STRAIGHT,
            'Three of a Kind': SMART_CONTRACT_POKER_PAYOUTS.THREE_OF_A_KIND,
            'Two Pair': SMART_CONTRACT_POKER_PAYOUTS.TWO_PAIR,
            'Pair': SMART_CONTRACT_POKER_PAYOUTS.PAIR,
            'High Card': SMART_CONTRACT_POKER_PAYOUTS.HIGH_CARD
        };

        const basePayout = handRankPayouts[results.playerHandRank] || SMART_CONTRACT_POKER_PAYOUTS.HIGH_CARD;
        totalWinnings = Math.floor(smartContractPokerGame.betAmount * (basePayout / 100));
        resultMessage = `${results.playerHandRank} wins!`;

        if (results.gameResult === 'automation_assist') {
            resultMessage += ' (Smart Contract Assist)';
        }
    }

    // Apply smart contract bonuses (actually work)
    if (smartContractPokerGame.smartContract.automation >= 0.80 && totalWinnings > 0) {
        const automationBonus = Math.floor(totalWinnings * SMART_CONTRACT_POKER_PAYOUTS.AUTOMATION_BONUS);
        totalWinnings += automationBonus;
        resultMessage += ' + Automation Bonus!';
    }

    // Apply gas optimization bonus
    if (smartContractPokerGame.smartContract.gasOptimization >= 0.85 && totalWinnings > 0) {
        const gasBonus = Math.floor(totalWinnings * SMART_CONTRACT_POKER_PAYOUTS.GAS_OPTIMIZATION_BONUS);
        totalWinnings += gasBonus;
        resultMessage += ' + Gas Optimization!';
    }

    // Apply distribution bonus
    if (distributionModelData.efficiency >= 0.90 && totalWinnings > 0) {
        const distributionBonus = Math.floor(totalWinnings * SMART_CONTRACT_POKER_PAYOUTS.DISTRIBUTION_BONUS);
        totalWinnings += distributionBonus;
        resultMessage += ' + Distribution Bonus!';
    }

    // Apply contract reliability bonus
    if (smartContractPokerGame.smartContract.reliability >= 0.95 && totalWinnings > 0) {
        const reliabilityBonus = Math.floor(totalWinnings * SMART_CONTRACT_POKER_PAYOUTS.CONTRACT_RELIABILITY_BONUS);
        totalWinnings += reliabilityBonus;
        resultMessage += ' + Reliability Bonus!';
    }

    // Apply contract type multiplier
    totalWinnings = Math.floor(totalWinnings * contractTypeData.payoutMultiplier);

    // Apply distribution model automation bonus
    totalWinnings = Math.floor(totalWinnings * distributionModelData.automationBonus);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(smartContractPokerGame.betAmount * 0.5); // 50% return
        resultMessage = 'Smart contract automation reward';
    }

    // Award contract points
    const pointsEarned = playerWon ? (results.playerHandRank === 'Royal Flush' ? 10 : 5) : 2;
    smartContractPokerGame.stats.contractPoints += pointsEarned;

    // Update contract execution stats
    smartContractPokerGame.stats.contractsExecuted++;
    if (totalWinnings > 0) {
        smartContractPokerGame.stats.automatedDistributions++;
    }

    // Calculate gas savings
    const gasSaved = Math.floor(smartContractPokerGame.smartContract.gasOptimization * 100);
    smartContractPokerGame.stats.totalGasSaved += gasSaved;

    // Add winnings to balance
    balance += totalWinnings;
    smartContractPokerGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? `${results.playerHandRank} wins!` : `Dealer wins with ${results.dealerHandRank}`;
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Contract: ${SMART_CONTRACT_TYPES[smartContractPokerGame.contractType].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show contract event
    document.getElementById('contractEvent').classList.remove('hidden');
    document.getElementById('contractEvent').textContent = results.playerHandRank === 'Royal Flush' ? 'ROYAL FLUSH!' : 'CONTRACT EXECUTED!';
    document.getElementById('contractEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update poker display
function updatePokerDisplay() {
    // Update player hand
    const playerHandElement = document.getElementById('playerHand');
    playerHandElement.innerHTML = '';
    smartContractPokerGame.poker.playerHand.forEach(card => {
        const cardElement = document.createElement('div');
        cardElement.className = 'w-12 h-16 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
        cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        playerHandElement.appendChild(cardElement);
    });

    // Update dealer hand
    const dealerHandElement = document.getElementById('dealerHand');
    dealerHandElement.innerHTML = '';
    smartContractPokerGame.poker.dealerHand.forEach(card => {
        const cardElement = document.createElement('div');
        cardElement.className = 'w-12 h-16 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
        cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        dealerHandElement.appendChild(cardElement);
    });

    // Update community cards
    const communityCardsElement = document.getElementById('communityCards');
    communityCardsElement.innerHTML = '';
    smartContractPokerGame.poker.communityCards.forEach(card => {
        const cardElement = document.createElement('div');
        cardElement.className = 'w-10 h-14 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
        cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        communityCardsElement.appendChild(cardElement);
    });

    // Update hand rankings
    document.getElementById('playerHandRank').textContent = `Hand: ${smartContractPokerGame.poker.playerHandRank}`;
    document.getElementById('dealerHandRank').textContent = `Hand: ${smartContractPokerGame.poker.dealerHandRank}`;
}

// Update smart contract display
function updateSmartContractDisplay() {
    updateSmartContractStatus();
    updateContractDisplay();
}

// Update smart contract status
function updateSmartContractStatus() {
    document.getElementById('gasPrice').innerHTML =
        `<div class="text-emerald-400 font-bold">GAS: ${(smartContractPokerGame.smartContract.gasPrice * 100).toFixed(2)}%</div>`;
    document.getElementById('executionSpeed').innerHTML =
        `<div class="text-blue-400 font-bold">SPEED: ${Math.floor(smartContractPokerGame.smartContract.executionSpeed * 100)}%</div>`;
    document.getElementById('reliability').innerHTML =
        `<div class="text-green-400 font-bold">RELIABILITY: ${Math.floor(smartContractPokerGame.smartContract.reliability * 100)}%</div>`;
    document.getElementById('contractLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${smartContractPokerGame.smartContract.contractLevel.toUpperCase()}</div>`;
}

// Update contract display
function updateContractDisplay() {
    document.getElementById('contractsExecuted').textContent = smartContractPokerGame.stats.contractsExecuted;
    document.getElementById('gasSaved').textContent = `${Math.floor(smartContractPokerGame.smartContract.gasOptimization * 100)}%`;
    document.getElementById('automationLevel').textContent = `${Math.floor(smartContractPokerGame.smartContract.automation * 100)}%`;

    // Update prize pool
    document.getElementById('prizePool').querySelector('.text-2xl').textContent = `$${smartContractPokerGame.poker.prizePool}`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${smartContractPokerGame.betAmount}`;
    document.getElementById('contractPointsDisplay').textContent = smartContractPokerGame.stats.contractPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = smartContractPokerGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${smartContractPokerGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${smartContractPokerGame.stats.totalWagered}`;
    document.getElementById('contractsExecutedStat').textContent = smartContractPokerGame.stats.contractsExecuted;
    document.getElementById('automatedDistributions').textContent = smartContractPokerGame.stats.automatedDistributions;

    const netResult = smartContractPokerGame.stats.totalWon - smartContractPokerGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-emerald-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    smartContractPokerGame.stats.handsPlayed++;
    smartContractPokerGame.stats.totalWagered += smartContractPokerGame.betAmount;
    smartContractPokerGame.stats.totalWon += winnings;

    if (won) {
        smartContractPokerGame.stats.handsWon++;
        smartContractPokerGame.stats.currentStreak++;
        smartContractPokerGame.streakData.currentWinStreak++;
        smartContractPokerGame.streakData.currentLossStreak = 0;

        if (smartContractPokerGame.streakData.currentWinStreak > smartContractPokerGame.streakData.longestWinStreak) {
            smartContractPokerGame.streakData.longestWinStreak = smartContractPokerGame.streakData.currentWinStreak;
        }

        if (winnings > smartContractPokerGame.stats.biggestWin) {
            smartContractPokerGame.stats.biggestWin = winnings;
        }
    } else {
        smartContractPokerGame.stats.currentStreak = 0;
        smartContractPokerGame.streakData.currentWinStreak = 0;
        smartContractPokerGame.streakData.currentLossStreak++;

        if (smartContractPokerGame.streakData.currentLossStreak > smartContractPokerGame.streakData.longestLossStreak) {
            smartContractPokerGame.streakData.longestLossStreak = smartContractPokerGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to smart contract mechanics)
    smartContractPokerGame.stats.winRate = (smartContractPokerGame.stats.handsWon / smartContractPokerGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next smart contract session
function resetGame() {
    smartContractPokerGame.isPlaying = false;
    smartContractPokerGame.betAmount = 0;
    smartContractPokerGame.totalBet = 0;
    smartContractPokerGame.gameResult = '';
    smartContractPokerGame.totalWin = 0;

    // Reset poker system
    smartContractPokerGame.poker.playerHand = [];
    smartContractPokerGame.poker.dealerHand = [];
    smartContractPokerGame.poker.communityCards = [];
    smartContractPokerGame.poker.playerHandRank = '';
    smartContractPokerGame.poker.dealerHandRank = '';
    smartContractPokerGame.poker.gamePhase = 'betting';
    smartContractPokerGame.poker.gameResult = '';
    smartContractPokerGame.poker.prizePool = 0;
    smartContractPokerGame.poker.gasUsed = 0;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('contractEvent').classList.add('hidden');
    document.getElementById('pokerActions').classList.add('hidden');

    // Reset poker table
    document.getElementById('playerHand').innerHTML = '';
    document.getElementById('dealerHand').innerHTML = '';
    document.getElementById('communityCards').innerHTML = '';
    document.getElementById('playerHandRank').textContent = 'Hand: None';
    document.getElementById('dealerHandRank').textContent = 'Hand: None';

    // Reset game phase display
    document.getElementById('gamePhase').querySelector('.text-lg').textContent = 'BETTING';

    // Reset contract display
    document.getElementById('contractStatus').textContent = 'Ready to deploy contract...';
    document.getElementById('deploymentBar').style.width = '0%';
    document.getElementById('deploymentStatus').textContent = 'Ready';

    // Reset gas usage
    document.getElementById('gasUsage').querySelector('.text-2xl').textContent = '0';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable deploy button
    document.getElementById('deployContract').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Smart contract ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Smart Contract Showdown - Poker with Automated Prize Distribution';

    // Generate new contract address
    smartContractPokerGame.smartContract.contractAddress = '0x' + Math.random().toString(16).substring(2, 10);
    document.getElementById('contractAddress').querySelector('.text-sm').textContent = '0x...';

    // Reinitialize systems for next session
    initializeSmartContractSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSmartContractPokerGame();
});