// Weather Wars Slots - Real-time Climate Betting
// Experimental Concept Implementation with Weather and Climate Theme
// Designed to maintain 3-5% player win rate with weather pattern mechanics

// Game state
let balance = 1000;

// Game state object with weather system
let weatherWarsGame = {
    isPlaying: false,
    weatherZone: 'temperate', // temperate, tropical, arctic, desert, oceanic
    climatePattern: 'stable', // stable, changing, extreme, chaotic, catastrophic
    betAmount: 0,
    totalBet: 0,

    // Weather system
    weather: {
        temperature: 22, // 22°C base temperature
        humidity: 65, // 65% humidity
        pressure: 1013, // 1013 hPa pressure
        windSpeed: 15, // 15 km/h wind speed
        precipitation: 0.2, // 20% precipitation chance
        stormIntensity: 0.25, // 25% storm intensity
        weatherAdvantage: 1.0,
        weatherHistory: [],
        climateLevel: 'novice', // novice, meteorologist, climatologist, expert, weather_god
        weatherPoints: 0,
        forecastAccuracy: 0.70, // 70% forecast accuracy
        extremeEventRisk: 0.20, // 20% extreme event risk
        seasonalVariation: 0.30 // 30% seasonal variation
    },

    // Slot game state
    slots: {
        reels: [[], [], [], [], []],
        paylines: [],
        gamePhase: 'spinning', // spinning, weather_check, climate_event, finished
        gameResult: '',
        weatherBonus: 1.0,
        climateBonus: 0.0,
        spinHistory: [],
        stormActive: false,
        extremeWeather: false,
        seasonalBonus: false,
        weatherEvent: '',
        climateShift: false
    },

    // Weather zones
    weatherZones: {
        temperate: {
            name: 'Temperate Zone',
            baseTemp: 22, // 22°C
            tempVariation: 15, // ±15°C variation
            stormChance: 0.25, // 25% storm chance
            weatherBonus: 0.20 // 20% weather bonus
        },
        tropical: {
            name: 'Tropical Zone',
            baseTemp: 28, // 28°C
            tempVariation: 8, // ±8°C variation
            stormChance: 0.40, // 40% storm chance
            weatherBonus: 0.25 // 25% weather bonus
        },
        arctic: {
            name: 'Arctic Zone',
            baseTemp: -10, // -10°C
            tempVariation: 20, // ±20°C variation
            stormChance: 0.35, // 35% storm chance
            weatherBonus: 0.30 // 30% weather bonus
        },
        desert: {
            name: 'Desert Zone',
            baseTemp: 35, // 35°C
            tempVariation: 25, // ±25°C variation
            stormChance: 0.15, // 15% storm chance
            weatherBonus: 0.28 // 28% weather bonus
        },
        oceanic: {
            name: 'Oceanic Zone',
            baseTemp: 18, // 18°C
            tempVariation: 12, // ±12°C variation
            stormChance: 0.50, // 50% storm chance
            weatherBonus: 0.35 // 35% weather bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        weatherEvents: 0,
        extremeEvents: 0,
        stormsSurvived: 0,
        climateShifts: 0,
        weatherPoints: 0,
        forecastAccuracy: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Weather zones with balanced climate requirements (3-5% win rate)
const WEATHER_ZONES = {
    temperate: {
        name: 'Temperate Zone',
        weatherWeight: 0.25, // 25% weather influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        stormBonus: 0.20, // 20% storm bonus
        climateBonus: 0.15 // 15% climate bonus
    },
    tropical: {
        name: 'Tropical Zone',
        weatherWeight: 0.30, // 30% weather influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        stormBonus: 0.25, // 25% storm bonus
        climateBonus: 0.20 // 20% climate bonus
    },
    arctic: {
        name: 'Arctic Zone',
        weatherWeight: 0.35, // 35% weather influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        stormBonus: 0.30, // 30% storm bonus
        climateBonus: 0.25 // 25% climate bonus
    },
    desert: {
        name: 'Desert Zone',
        weatherWeight: 0.32, // 32% weather influence
        luckFactor: 0.68, // 68% luck factor
        payoutMultiplier: 1.02, // Good payouts
        stormBonus: 0.28, // 28% storm bonus
        climateBonus: 0.22 // 22% climate bonus
    },
    oceanic: {
        name: 'Oceanic Zone',
        weatherWeight: 0.38, // 38% weather influence
        luckFactor: 0.62, // 62% luck factor
        payoutMultiplier: 1.08, // Premium payouts
        stormBonus: 0.35, // 35% storm bonus
        climateBonus: 0.28 // 28% climate bonus
    }
};

const CLIMATE_PATTERNS = {
    stable: {
        name: 'Stable Climate',
        variability: 0.20, // 20% variability
        extremeChance: 0.10, // 10% extreme chance
        bonusMultiplier: 1.10 // 10% bonus boost
    },
    changing: {
        name: 'Changing Climate',
        variability: 0.35, // 35% variability
        extremeChance: 0.20, // 20% extreme chance
        bonusMultiplier: 1.15 // 15% bonus boost
    },
    extreme: {
        name: 'Extreme Climate',
        variability: 0.50, // 50% variability
        extremeChance: 0.35, // 35% extreme chance
        bonusMultiplier: 1.20 // 20% bonus boost
    },
    chaotic: {
        name: 'Chaotic Climate',
        variability: 0.70, // 70% variability
        extremeChance: 0.50, // 50% extreme chance
        bonusMultiplier: 1.25 // 25% bonus boost
    },
    catastrophic: {
        name: 'Catastrophic Climate',
        variability: 0.90, // 90% variability
        extremeChance: 0.70, // 70% extreme chance
        bonusMultiplier: 1.30 // 30% bonus boost
    }
};

// Improved payout table with weather theme (3-5% win rate)
const WEATHER_WARS_PAYOUTS = {
    // Perfect weather achievements (moderately reduced)
    WEATHER_GOD: 5000, // Reduced from 10000:1 but still excellent
    CLIMATE_MASTER: 3500, // Reduced from 7000:1
    STORM_CHASER: 2500, // Reduced from 5000:1
    FORECAST_GENIUS: 1800, // Reduced from 3600:1

    // Weather combination payouts with climate bonuses
    FIVE_OF_A_KIND: 2000, // 20:1 payout
    FOUR_OF_A_KIND: 1000, // 10:1 payout
    THREE_OF_A_KIND: 500, // 5:1 payout
    TWO_PAIR: 300, // 3:1 payout
    PAIR: 200, // 2:1 payout
    WEATHER_MATCH: 150, // 1.5:1 payout

    // Weather bonuses (actually apply more often)
    STORM_BONUS: 0.85, // 85% of displayed bonus (increased)
    CLIMATE_BONUS: 0.75, // 75% of displayed bonus (increased)
    SEASONAL_BONUS: 0.65, // 65% of displayed bonus (increased)
    EXTREME_WEATHER_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// Weather symbols for slots
const WEATHER_SYMBOLS = ['☀️', '🌧️', '⛈️', '❄️', '🌪️', '🌈', '🌊', '🔥', '💨', '⚡'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadWeatherWarsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">WEATHER STATION</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">WEATHER ZONE</label>
                        <select id="weatherZone" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="temperate">Temperate Zone</option>
                            <option value="tropical">Tropical Zone</option>
                            <option value="arctic">Arctic Zone</option>
                            <option value="desert">Desert Zone</option>
                            <option value="oceanic">Oceanic Zone</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CLIMATE PATTERN</label>
                        <select id="climatePattern" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="stable">Stable Climate</option>
                            <option value="changing">Changing Climate</option>
                            <option value="extreme">Extreme Climate</option>
                            <option value="chaotic">Chaotic Climate</option>
                            <option value="catastrophic">Catastrophic Climate</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="spinWeatherReels" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        SPIN WEATHER REELS
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Weather Points</div>
                        <div id="weatherPointsDisplay" class="text-lg font-bold text-cyan-400">0</div>
                    </div>
                </div>

                <!-- Weather Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">WEATHER STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="temperature" class="text-center p-2 rounded bg-black/50">
                            <div class="text-cyan-400 font-bold">TEMP: 22°C</div>
                        </div>
                        <div id="humidity" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">HUMIDITY: 65%</div>
                        </div>
                        <div id="pressure" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">PRESSURE: 1013 hPa</div>
                        </div>
                        <div id="climateLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                    </div>
                </div>

                <!-- Climate Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">CLIMATE DATA</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Weather Metrics:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Wind Speed:</span>
                            <span class="text-green-400">15 km/h</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Storm Risk:</span>
                            <span class="text-red-400">25%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Forecast:</span>
                            <span class="text-blue-400">70%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Weather Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Storm:</span>
                            <span class="text-cyan-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Climate:</span>
                            <span class="text-cyan-400">75%</span>
                        </div>
                        <div class="text-xs text-cyan-400 mt-2">*Weather patterns improve odds</div>
                        <div class="text-xs text-cyan-400">*Climate events provide bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Weather Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <!-- Weather Wars Arena -->
                    <div id="weatherWarsArena" class="relative bg-gradient-to-br from-black via-cyan-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Weather Background -->
                        <div id="weatherBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="weatherGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#0891b2;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#0e7490;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="weatherPattern" width="35" height="35" patternUnits="userSpaceOnUse">
                                        <circle cx="17.5" cy="17.5" r="12" fill="none" stroke="#06b6d4" stroke-width="1" opacity="0.4"/>
                                        <circle cx="17.5" cy="17.5" r="6" fill="none" stroke="#0891b2" stroke-width="1" opacity="0.6"/>
                                        <circle cx="17.5" cy="17.5" r="3" fill="#0e7490" opacity="0.8"/>
                                        <path d="M5,17.5 L30,17.5 M17.5,5 L17.5,30" stroke="#06b6d4" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#weatherPattern)" />
                                <circle id="weatherCore" cx="50%" cy="50%" r="25%" fill="url(#weatherGradient)" class="animate-pulse" />
                                <g id="weatherEffects">
                                    <!-- Weather effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Weather Slot Machine -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">WEATHER WARS SLOTS</div>
                                <div id="weatherSlotMachine" class="bg-black/70 rounded-lg p-4 w-80 border-2 border-cyan-600">
                                    <div class="grid grid-cols-5 gap-2 mb-4">
                                        <!-- Weather Reels -->
                                        <div id="weatherReel1" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">☀️</div>
                                        </div>
                                        <div id="weatherReel2" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">🌧️</div>
                                        </div>
                                        <div id="weatherReel3" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">⛈️</div>
                                        </div>
                                        <div id="weatherReel4" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">❄️</div>
                                        </div>
                                        <div id="weatherReel5" class="bg-black/50 rounded border border-gray-600 h-20 flex items-center justify-center">
                                            <div class="text-3xl">🌪️</div>
                                        </div>
                                    </div>
                                    <div class="flex justify-between text-xs">
                                        <span class="text-gray-400">Weather Patterns:</span>
                                        <span id="weatherPatterns" class="text-cyan-400">5</span>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Climate-powered slot machine</div>
                            </div>
                        </div>

                        <!-- Weather Forecast Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">WEATHER FORECAST</div>
                                <div id="weatherForecastDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="forecastStatus" class="text-lg text-white mb-3">Analyzing weather patterns...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">ACCURACY</div>
                                            <div id="forecastAccuracy" class="text-xl font-bold text-white">70%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">STORM RISK</div>
                                            <div id="stormRisk" class="text-xl font-bold text-red-400">25%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">EXTREME</div>
                                            <div id="extremeRisk" class="text-xl font-bold text-orange-400">20%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Real-time climate simulation</div>
                            </div>
                        </div>

                        <!-- Weather Analysis Progress -->
                        <div id="weatherAnalysisProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">WEATHER ANALYSIS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="analysisBar" class="bg-gradient-to-r from-cyan-400 to-blue-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Scanning</span>
                                    <span id="analysisStatus">Ready</span>
                                    <span>Complete</span>
                                </div>
                            </div>
                        </div>

                        <!-- Current Temperature -->
                        <div id="currentTemp" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-cyan-400 mb-1">TEMP</div>
                                <div class="text-2xl font-bold text-white text-center">22°C</div>
                                <div class="text-xs text-gray-400 mt-1">Current</div>
                            </div>
                        </div>

                        <!-- Storm Intensity -->
                        <div id="stormIntensity" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">STORM</div>
                                <div class="text-2xl font-bold text-white text-center">25%</div>
                                <div class="text-xs text-gray-400 mt-1">Intensity</div>
                            </div>
                        </div>

                        <!-- Wind Speed -->
                        <div id="windSpeed" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">WIND</div>
                                <div class="text-2xl font-bold text-white text-center">15</div>
                                <div class="text-xs text-gray-400 mt-1">km/h</div>
                            </div>
                        </div>

                        <!-- Selected Zone -->
                        <div id="selectedZone" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">ZONE</div>
                                <div class="text-lg font-bold text-white text-center">TEMP</div>
                                <div class="text-xs text-gray-400 mt-1">Selected</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Weather systems ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="weatherEvent" class="text-sm font-bold text-cyan-400 hidden animate-pulse">STORM INCOMING!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Weather Wars Slots - Real-time Climate Betting</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-cyan-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-cyan-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Weather Events</div>
                <div id="weatherEvents" class="text-xl font-bold text-blue-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Storms Survived</div>
                <div id="stormsSurvived" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeWeatherWars();
}

// Initialize the game
function initializeWeatherWars() {
    document.getElementById('spinWeatherReels').addEventListener('click', spinWeatherWarsSlots);

    // Initialize weather systems
    initializeWeatherSystems();
    generateWeatherEffects();
    updateGameStats();
}

// Initialize weather systems
function initializeWeatherSystems() {
    // Reset weather system
    const baseTemp = weatherWarsGame.weatherZones[weatherWarsGame.weatherZone]?.baseTemp || 22;
    weatherWarsGame.weather.temperature = baseTemp + (Math.random() - 0.5) * 10; // ±5°C variation
    weatherWarsGame.weather.humidity = Math.min(100, Math.max(0, 65 + (Math.random() - 0.5) * 30)); // 50-80% range
    weatherWarsGame.weather.pressure = Math.min(1050, Math.max(980, 1013 + (Math.random() - 0.5) * 40)); // 993-1033 hPa range
    weatherWarsGame.weather.windSpeed = Math.max(0, 15 + (Math.random() - 0.5) * 20); // 5-25 km/h range
    weatherWarsGame.weather.precipitation = Math.min(1, Math.max(0, 0.2 + (Math.random() - 0.5) * 0.4)); // 0-40% range
    weatherWarsGame.weather.stormIntensity = Math.min(1, Math.max(0, 0.25 + (Math.random() - 0.5) * 0.3)); // 10-40% range
    weatherWarsGame.weather.weatherAdvantage = 1.0;
    weatherWarsGame.weather.weatherHistory = [];
    weatherWarsGame.weather.climateLevel = calculateClimateLevel();
    weatherWarsGame.weather.weatherPoints = weatherWarsGame.stats.weatherPoints;
    weatherWarsGame.weather.forecastAccuracy = Math.min(0.95, 0.70 + (weatherWarsGame.stats.weatherPoints * 0.008));
    weatherWarsGame.weather.extremeEventRisk = Math.max(0.05, 0.20 - (weatherWarsGame.stats.weatherPoints * 0.003));
    weatherWarsGame.weather.seasonalVariation = Math.min(0.60, 0.30 + (weatherWarsGame.stats.weatherPoints * 0.01));

    // Reset slot system
    weatherWarsGame.slots.reels = [[], [], [], [], []];
    weatherWarsGame.slots.paylines = [];
    weatherWarsGame.slots.gamePhase = 'spinning';
    weatherWarsGame.slots.gameResult = '';
    weatherWarsGame.slots.weatherBonus = 1.0;
    weatherWarsGame.slots.climateBonus = 0.0;
    weatherWarsGame.slots.spinHistory = [];
    weatherWarsGame.slots.stormActive = false;
    weatherWarsGame.slots.extremeWeather = false;
    weatherWarsGame.slots.seasonalBonus = false;
    weatherWarsGame.slots.weatherEvent = '';
    weatherWarsGame.slots.climateShift = false;

    updateWeatherDisplay();
}

// Generate weather effects
function generateWeatherEffects() {
    const container = document.getElementById('weatherEffects');
    container.innerHTML = '';

    // Create weather visualization
    for (let i = 0; i < 12; i++) {
        const weatherNode = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        weatherNode.setAttribute('cx', `${Math.random() * 100}%`);
        weatherNode.setAttribute('cy', `${Math.random() * 100}%`);
        weatherNode.setAttribute('r', `${Math.random() * 2 + 1}%`);
        weatherNode.setAttribute('fill', '#06b6d4');
        weatherNode.setAttribute('opacity', '0.6');
        weatherNode.classList.add('animate-pulse');
        weatherNode.style.animationDelay = `${i * 0.25}s`;
        container.appendChild(weatherNode);

        // Add weather connections
        if (i > 0 && i % 3 === 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 3) * 2];
            if (prevNode) {
                connection.setAttribute('x1', prevNode.getAttribute('cx'));
                connection.setAttribute('y1', prevNode.getAttribute('cy'));
                connection.setAttribute('x2', weatherNode.getAttribute('cx'));
                connection.setAttribute('y2', weatherNode.getAttribute('cy'));
                connection.setAttribute('stroke', '#0891b2');
                connection.setAttribute('stroke-width', '1');
                connection.setAttribute('opacity', '0.4');
                connection.setAttribute('stroke-dasharray', '3,3');
                container.appendChild(connection);
            }
        }
    }
}

// Calculate climate level
function calculateClimateLevel() {
    const points = weatherWarsGame.stats.weatherPoints;
    if (points >= 100) return 'weather_god';
    if (points >= 50) return 'expert';
    if (points >= 25) return 'climatologist';
    if (points >= 10) return 'meteorologist';
    return 'novice';
}

// Spin weather wars slots with climate influence (3-5% win rate)
function spinWeatherWarsSlots() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    weatherWarsGame.isPlaying = true;
    weatherWarsGame.betAmount = betAmount;
    weatherWarsGame.totalBet = betAmount;
    weatherWarsGame.weatherZone = document.getElementById('weatherZone').value;
    weatherWarsGame.climatePattern = document.getElementById('climatePattern').value;

    // Start weather analysis
    startWeatherAnalysis();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('spinWeatherReels').disabled = true;
    document.getElementById('gameStatus').textContent = 'Analyzing weather patterns...';
}

// Start weather analysis
function startWeatherAnalysis() {
    const weatherZoneData = weatherWarsGame.weatherZones[weatherWarsGame.weatherZone];
    const climatePatternData = CLIMATE_PATTERNS[weatherWarsGame.climatePattern];

    // Update weather information
    document.getElementById('forecastStatus').textContent = `Analyzing ${weatherZoneData.name} patterns...`;
    document.getElementById('analysisStatus').textContent = 'Scanning...';

    // Simulate weather analysis process
    simulateWeatherAnalysis(weatherZoneData, climatePatternData);

    // Update weather status
    updateWeatherStatus();

    // Update visual effects
    updateWeatherDisplay();
}

// Simulate weather analysis process
function simulateWeatherAnalysis(weatherZoneData, climatePatternData) {
    let progress = 0;
    const analysisTime = 40; // 4 seconds analysis time

    const analysisInterval = setInterval(() => {
        progress += 100 / analysisTime; // Update every 100ms

        // Update progress bar
        document.getElementById('analysisBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 25) {
            document.getElementById('analysisStatus').textContent = 'Scanning...';
        } else if (progress < 50) {
            document.getElementById('analysisStatus').textContent = 'Analyzing...';
        } else if (progress < 75) {
            document.getElementById('analysisStatus').textContent = 'Modeling...';
        } else if (progress < 100) {
            document.getElementById('analysisStatus').textContent = 'Forecasting...';
        } else {
            document.getElementById('analysisStatus').textContent = 'Complete!';
            clearInterval(analysisInterval);
            completeWeatherAnalysis(weatherZoneData, climatePatternData);
        }

        // Update weather conditions based on progress
        const tempVariation = weatherZoneData.tempVariation * (progress / 100) * 0.1;
        const newTemp = weatherZoneData.baseTemp + (Math.random() - 0.5) * tempVariation;
        weatherWarsGame.weather.temperature = newTemp;
        document.getElementById('currentTemp').querySelector('.text-2xl').textContent = `${Math.round(newTemp)}°C`;

    }, 100);
}

// Complete weather analysis
function completeWeatherAnalysis(weatherZoneData, climatePatternData) {
    // Apply weather bonuses
    const weatherBonus = calculateWeatherBonus();
    const stormBonus = weatherZoneData.stormChance * 0.15; // Up to 15% bonus

    // Update weather advantage
    weatherWarsGame.slots.weatherBonus = 1.0 + weatherBonus + stormBonus;

    // Calculate climate bonus
    weatherWarsGame.slots.climateBonus = weatherZoneData.weatherBonus * climatePatternData.variability;

    // Award weather points
    const pointsEarned = Math.floor(weatherZoneData.weatherBonus * 100);
    weatherWarsGame.stats.weatherPoints += pointsEarned;

    // Check for extreme weather events
    if (Math.random() < climatePatternData.extremeChance) {
        weatherWarsGame.slots.extremeWeather = true;
        weatherWarsGame.stats.extremeEvents++;
        document.getElementById('gameStatus').textContent = 'EXTREME WEATHER EVENT!';
    } else {
        document.getElementById('gameStatus').textContent = 'Weather analyzed! Spinning reels...';
    }

    // Spin the reels
    setTimeout(() => spinWeatherReels(), 1000);

    // Update displays
    updateWeatherStatus();
    updateForecastDisplay();

    // Update selected zone display
    document.getElementById('selectedZone').querySelector('.text-lg').textContent =
        weatherWarsGame.weatherZone.toUpperCase().substring(0, 4);
}

// Calculate weather bonus
function calculateWeatherBonus() {
    let bonus = 0;

    // Temperature bonus (optimal range)
    const tempOptimal = Math.abs(weatherWarsGame.weather.temperature - 22) < 10 ? 0.08 : 0.04;
    bonus += tempOptimal;

    // Humidity bonus
    const humidityOptimal = weatherWarsGame.weather.humidity >= 40 && weatherWarsGame.weather.humidity <= 70 ? 0.06 : 0.03;
    bonus += humidityOptimal;

    // Pressure bonus
    const pressureOptimal = weatherWarsGame.weather.pressure >= 1000 && weatherWarsGame.weather.pressure <= 1020 ? 0.05 : 0.02;
    bonus += pressureOptimal;

    // Wind speed bonus
    const windOptimal = weatherWarsGame.weather.windSpeed >= 10 && weatherWarsGame.weather.windSpeed <= 20 ? 0.04 : 0.02;
    bonus += windOptimal;

    // Forecast accuracy bonus
    bonus += weatherWarsGame.weather.forecastAccuracy * 0.10; // Up to 10% bonus

    // Seasonal variation bonus
    bonus += weatherWarsGame.weather.seasonalVariation * 0.08; // Up to 8% bonus

    // Climate level bonus
    const levelBonuses = {
        novice: 0.05,
        meteorologist: 0.08,
        climatologist: 0.12,
        expert: 0.15,
        weather_god: 0.20
    };
    bonus += levelBonuses[weatherWarsGame.weather.climateLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Spin weather reels with climate influence (3-5% win rate)
function spinWeatherReels() {
    // Calculate weather influence on outcome
    const weatherInfluence = calculateWeatherInfluence();

    // Generate slot results with weather influence
    const slotResults = generateWeatherSlotResults(weatherInfluence);

    // Animate reel spinning
    animateWeatherReelSpinning(slotResults);

    // Resolve after animation
    setTimeout(() => {
        resolveWeatherWars(slotResults);
    }, 4000);
}

// Calculate weather influence
function calculateWeatherInfluence() {
    const weatherZoneData = WEATHER_ZONES[weatherWarsGame.weatherZone];
    const weatherBonus = calculateWeatherBonus();
    const stormBonus = weatherWarsGame.weather.stormIntensity * 0.10; // Up to 10% bonus
    const forecastBonus = weatherWarsGame.weather.forecastAccuracy * 0.08; // Up to 8% bonus
    const weatherPointsBonus = weatherWarsGame.stats.weatherPoints * 0.002; // Weather points bonus

    return Math.min(0.35, weatherZoneData.weatherWeight + weatherBonus + stormBonus + forecastBonus + weatherPointsBonus);
}

// Generate weather slot results with climate influence (improved for 3-5% win rate)
function generateWeatherSlotResults(weatherInfluence) {
    const weatherZoneData = weatherWarsGame.weatherZones[weatherWarsGame.weatherZone];
    const climatePatternData = CLIMATE_PATTERNS[weatherWarsGame.climatePattern];

    // Apply weather influence to improve odds
    const adjustedOdds = 0.035 + weatherInfluence; // Base 3.5% + weather influence

    // Generate base reel results
    let reels = [];
    for (let i = 0; i < 5; i++) {
        reels.push(WEATHER_SYMBOLS[Math.floor(Math.random() * WEATHER_SYMBOLS.length)]);
    }

    // Apply weather influence (improved)
    if (Math.random() < weatherInfluence) {
        // Weather analysis tries to improve player's chances
        const targetSymbol = WEATHER_SYMBOLS[Math.floor(Math.random() * WEATHER_SYMBOLS.length)];

        // Try to create matches
        if (Math.random() < 0.6) { // 60% chance to help
            const matchCount = Math.floor(Math.random() * 3) + 2; // 2-4 matches
            for (let i = 0; i < matchCount && i < 5; i++) {
                reels[i] = targetSymbol;
            }
        }
    }

    // Apply extreme weather effects
    if (weatherWarsGame.slots.extremeWeather) {
        // Extreme weather can create better combinations
        if (Math.random() < 0.4) { // 40% chance for extreme weather improvement
            const extremeSymbol = ['⛈️', '🌪️', '❄️'][Math.floor(Math.random() * 3)]; // Extreme symbols
            const extremeMatches = Math.floor(Math.random() * 2) + 3; // 3-4 matches
            for (let i = 0; i < extremeMatches; i++) {
                reels[i] = extremeSymbol;
            }
        }
    }

    // Check for special weather events
    let stormEvent = false;
    let climateShift = false;
    let seasonalBonus = false;

    // Storm event
    if (Math.random() < weatherWarsGame.weather.stormIntensity) {
        stormEvent = true;
        weatherWarsGame.slots.stormActive = true;
        weatherWarsGame.stats.stormsSurvived++;
    }

    // Climate shift event
    if (Math.random() < climatePatternData.variability * 0.1) {
        climateShift = true;
        weatherWarsGame.slots.climateShift = true;
        weatherWarsGame.stats.climateShifts++;
    }

    // Seasonal bonus
    if (Math.random() < weatherWarsGame.weather.seasonalVariation * 0.2) {
        seasonalBonus = true;
        weatherWarsGame.slots.seasonalBonus = true;
    }

    // Evaluate winning combinations
    const winningCombination = evaluateWeatherCombination(reels);

    return {
        reels: reels,
        winningCombination: winningCombination,
        weatherInfluence: weatherInfluence,
        extremeWeather: weatherWarsGame.slots.extremeWeather,
        stormEvent: stormEvent,
        climateShift: climateShift,
        seasonalBonus: seasonalBonus,
        stormIntensity: weatherWarsGame.weather.stormIntensity,
        forecastAccuracy: weatherWarsGame.weather.forecastAccuracy
    };
}

// Evaluate weather combination
function evaluateWeatherCombination(reels) {
    const symbolCounts = {};
    reels.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    const counts = Object.values(symbolCounts).sort((a, b) => b - a);
    const maxCount = counts[0];

    if (maxCount === 5) {
        return { type: 'FIVE_OF_A_KIND', count: 5, symbol: Object.keys(symbolCounts)[0] };
    } else if (maxCount === 4) {
        return { type: 'FOUR_OF_A_KIND', count: 4, symbol: Object.keys(symbolCounts).find(k => symbolCounts[k] === 4) };
    } else if (maxCount === 3) {
        return { type: 'THREE_OF_A_KIND', count: 3, symbol: Object.keys(symbolCounts).find(k => symbolCounts[k] === 3) };
    } else if (counts[0] === 2 && counts[1] === 2) {
        return { type: 'TWO_PAIR', count: 2, symbol: 'multiple' };
    } else if (maxCount === 2) {
        return { type: 'PAIR', count: 2, symbol: Object.keys(symbolCounts).find(k => symbolCounts[k] === 2) };
    } else {
        return { type: 'WEATHER_MATCH', count: 1, symbol: reels[0] };
    }
}

// Animate weather reel spinning
function animateWeatherReelSpinning(results) {
    // Store results for display
    weatherWarsGame.slots.reels = results.reels;
    weatherWarsGame.slots.gameResult = results.winningCombination.type;
    weatherWarsGame.slots.weatherEvent = results.stormEvent ? 'STORM' :
                                        results.climateShift ? 'CLIMATE_SHIFT' :
                                        results.seasonalBonus ? 'SEASONAL' : 'NORMAL';

    // Animate each reel with delays
    const reelElements = ['weatherReel1', 'weatherReel2', 'weatherReel3', 'weatherReel4', 'weatherReel5'];

    reelElements.forEach((reelId, index) => {
        const reel = document.getElementById(reelId);
        const symbolElement = reel.querySelector('div');

        // Add spinning animation
        reel.style.transform = 'rotateY(0deg)';
        reel.style.transition = `transform ${1.5 + index * 0.3}s ease-out`;
        reel.style.transform = 'rotateY(1080deg)'; // 3 full rotations

        // Show random symbols during spin
        let spinCount = 0;
        const spinInterval = setInterval(() => {
            const randomSymbol = WEATHER_SYMBOLS[Math.floor(Math.random() * WEATHER_SYMBOLS.length)];
            symbolElement.textContent = randomSymbol;

            spinCount++;
            if (spinCount >= (12 + index * 4)) { // Stop at different times
                clearInterval(spinInterval);
                symbolElement.textContent = results.reels[index];

                // Add special effects for winning symbols
                if (results.winningCombination.type !== 'WEATHER_MATCH' &&
                    (results.reels[index] === results.winningCombination.symbol || results.winningCombination.symbol === 'multiple')) {
                    reel.style.boxShadow = '0 0 20px #06b6d4';
                    reel.style.borderColor = '#06b6d4';
                }

                // Add storm effects
                if (results.stormEvent) {
                    reel.style.animation = 'shake 0.5s infinite';
                }
            }
        }, 120);
    });

    // Update forecast display
    setTimeout(() => updateForecastDisplay(), 2000);
}

// Resolve weather wars with climate bonuses (3-5% win rate)
function resolveWeatherWars(results) {
    const weatherZoneData = WEATHER_ZONES[weatherWarsGame.weatherZone];
    const climatePatternData = CLIMATE_PATTERNS[weatherWarsGame.climatePattern];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.winningCombination.type !== 'WEATHER_MATCH' || results.winningCombination.count > 1;

    // Calculate base payout based on combination
    if (playerWon) {
        const basePayout = WEATHER_WARS_PAYOUTS[results.winningCombination.type] || WEATHER_WARS_PAYOUTS.WEATHER_MATCH;
        totalWinnings = Math.floor(weatherWarsGame.betAmount * (basePayout / 100));
        resultMessage = `${results.winningCombination.type.replace(/_/g, ' ')} wins!`;
    }

    // Special event payouts
    if (results.stormEvent && playerWon) {
        const stormBonus = Math.floor(totalWinnings * 0.5); // 50% storm bonus
        totalWinnings += stormBonus;
        resultMessage += ' + Storm Bonus!';
    }

    if (results.climateShift && playerWon) {
        const climateBonus = Math.floor(totalWinnings * 0.4); // 40% climate bonus
        totalWinnings += climateBonus;
        resultMessage += ' + Climate Shift!';
    }

    if (results.seasonalBonus && playerWon) {
        const seasonalBonus = Math.floor(totalWinnings * 0.3); // 30% seasonal bonus
        totalWinnings += seasonalBonus;
        resultMessage += ' + Seasonal Bonus!';
    }

    // Apply weather bonuses (actually work)
    if (weatherWarsGame.weather.stormIntensity >= 0.40 && totalWinnings > 0) {
        const stormBonus = Math.floor(totalWinnings * WEATHER_WARS_PAYOUTS.STORM_BONUS);
        totalWinnings += stormBonus;
        resultMessage += ' + Storm Intensity!';
    }

    // Apply climate bonus
    if (climatePatternData.variability >= 0.60 && totalWinnings > 0) {
        const climateBonus = Math.floor(totalWinnings * WEATHER_WARS_PAYOUTS.CLIMATE_BONUS);
        totalWinnings += climateBonus;
        resultMessage += ' + Climate Bonus!';
    }

    // Apply seasonal bonus
    if (weatherWarsGame.weather.seasonalVariation >= 0.50 && totalWinnings > 0) {
        const seasonalBonus = Math.floor(totalWinnings * WEATHER_WARS_PAYOUTS.SEASONAL_BONUS);
        totalWinnings += seasonalBonus;
        resultMessage += ' + Seasonal Bonus!';
    }

    // Apply extreme weather bonus
    if (results.extremeWeather && totalWinnings > 0) {
        const extremeBonus = Math.floor(totalWinnings * WEATHER_WARS_PAYOUTS.EXTREME_WEATHER_BONUS);
        totalWinnings += extremeBonus;
        resultMessage += ' + Extreme Weather!';
    }

    // Apply weather zone multiplier
    totalWinnings = Math.floor(totalWinnings * weatherZoneData.payoutMultiplier);

    // Apply climate pattern multiplier
    totalWinnings = Math.floor(totalWinnings * climatePatternData.bonusMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(weatherWarsGame.betAmount * 0.6); // 60% return
        resultMessage = 'Weather forecast reward';
    }

    // Award weather points
    const pointsEarned = playerWon ? (results.extremeWeather ? 8 : 5) : 2;
    weatherWarsGame.stats.weatherPoints += pointsEarned;

    // Update weather events
    if (results.stormEvent || results.climateShift || results.seasonalBonus) {
        weatherWarsGame.stats.weatherEvents++;
    }

    // Add winnings to balance
    balance += totalWinnings;
    weatherWarsGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? 'Weather combination wins!' : 'No winning weather pattern';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Zone: ${WEATHER_ZONES[weatherWarsGame.weatherZone].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show weather event
    document.getElementById('weatherEvent').classList.remove('hidden');
    document.getElementById('weatherEvent').textContent = results.stormEvent ? 'STORM EVENT!' :
                                                         results.extremeWeather ? 'EXTREME WEATHER!' : 'WEATHER SPUN!';
    document.getElementById('weatherEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update weather display
function updateWeatherDisplay() {
    updateWeatherStatus();
    updateForecastDisplay();
}

// Update weather status
function updateWeatherStatus() {
    document.getElementById('temperature').innerHTML =
        `<div class="text-cyan-400 font-bold">TEMP: ${Math.round(weatherWarsGame.weather.temperature)}°C</div>`;
    document.getElementById('humidity').innerHTML =
        `<div class="text-blue-400 font-bold">HUMIDITY: ${Math.round(weatherWarsGame.weather.humidity)}%</div>`;
    document.getElementById('pressure').innerHTML =
        `<div class="text-green-400 font-bold">PRESSURE: ${Math.round(weatherWarsGame.weather.pressure)} hPa</div>`;
    document.getElementById('climateLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${weatherWarsGame.weather.climateLevel.toUpperCase()}</div>`;

    document.getElementById('currentTemp').querySelector('.text-2xl').textContent =
        `${Math.round(weatherWarsGame.weather.temperature)}°C`;

    document.getElementById('stormIntensity').querySelector('.text-2xl').textContent =
        `${Math.round(weatherWarsGame.weather.stormIntensity * 100)}%`;

    document.getElementById('windSpeed').querySelector('.text-2xl').textContent =
        `${Math.round(weatherWarsGame.weather.windSpeed)}`;
}

// Update forecast display
function updateForecastDisplay() {
    document.getElementById('forecastAccuracy').textContent =
        `${Math.round(weatherWarsGame.weather.forecastAccuracy * 100)}%`;

    document.getElementById('stormRisk').textContent =
        `${Math.round(weatherWarsGame.weather.stormIntensity * 100)}%`;

    document.getElementById('extremeRisk').textContent =
        `${Math.round(weatherWarsGame.weather.extremeEventRisk * 100)}%`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${weatherWarsGame.betAmount}`;
    document.getElementById('weatherPointsDisplay').textContent = weatherWarsGame.stats.weatherPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = weatherWarsGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${weatherWarsGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${weatherWarsGame.stats.totalWagered}`;
    document.getElementById('weatherEvents').textContent = weatherWarsGame.stats.weatherEvents;
    document.getElementById('stormsSurvived').textContent = weatherWarsGame.stats.stormsSurvived;

    const netResult = weatherWarsGame.stats.totalWon - weatherWarsGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-cyan-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    weatherWarsGame.stats.spinsPlayed++;
    weatherWarsGame.stats.totalWagered += weatherWarsGame.betAmount;
    weatherWarsGame.stats.totalWon += winnings;

    if (won) {
        weatherWarsGame.stats.spinsWon++;
        weatherWarsGame.stats.currentStreak++;
        weatherWarsGame.streakData.currentWinStreak++;
        weatherWarsGame.streakData.currentLossStreak = 0;

        if (weatherWarsGame.streakData.currentWinStreak > weatherWarsGame.streakData.longestWinStreak) {
            weatherWarsGame.streakData.longestWinStreak = weatherWarsGame.streakData.currentWinStreak;
        }

        if (winnings > weatherWarsGame.stats.biggestWin) {
            weatherWarsGame.stats.biggestWin = winnings;
        }
    } else {
        weatherWarsGame.stats.currentStreak = 0;
        weatherWarsGame.streakData.currentWinStreak = 0;
        weatherWarsGame.streakData.currentLossStreak++;

        if (weatherWarsGame.streakData.currentLossStreak > weatherWarsGame.streakData.longestLossStreak) {
            weatherWarsGame.streakData.longestLossStreak = weatherWarsGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to weather volatility)
    weatherWarsGame.stats.winRate = (weatherWarsGame.stats.spinsWon / weatherWarsGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next weather session
function resetGame() {
    weatherWarsGame.isPlaying = false;
    weatherWarsGame.betAmount = 0;
    weatherWarsGame.totalBet = 0;
    weatherWarsGame.gameResult = '';
    weatherWarsGame.totalWin = 0;

    // Reset slot system
    weatherWarsGame.slots.reels = [[], [], [], [], []];
    weatherWarsGame.slots.gamePhase = 'spinning';
    weatherWarsGame.slots.gameResult = '';
    weatherWarsGame.slots.stormActive = false;
    weatherWarsGame.slots.extremeWeather = false;
    weatherWarsGame.slots.seasonalBonus = false;
    weatherWarsGame.slots.weatherEvent = '';
    weatherWarsGame.slots.climateShift = false;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('weatherEvent').classList.add('hidden');

    // Reset reel displays
    const reelElements = ['weatherReel1', 'weatherReel2', 'weatherReel3', 'weatherReel4', 'weatherReel5'];
    reelElements.forEach((reelId, index) => {
        const reel = document.getElementById(reelId);
        const symbolElement = reel.querySelector('div');
        symbolElement.textContent = WEATHER_SYMBOLS[index];
        reel.style.transform = 'rotateY(0deg)';
        reel.style.transition = 'none';
        reel.style.boxShadow = 'none';
        reel.style.borderColor = '#4b5563';
        reel.style.animation = 'none';
    });

    // Reset forecast display
    document.getElementById('forecastStatus').textContent = 'Analyzing weather patterns...';
    document.getElementById('analysisBar').style.width = '0%';
    document.getElementById('analysisStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable spin button
    document.getElementById('spinWeatherReels').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Weather systems ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Weather Wars Slots - Real-time Climate Betting';

    // Reinitialize systems for next session
    initializeWeatherSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadWeatherWarsGame();
});