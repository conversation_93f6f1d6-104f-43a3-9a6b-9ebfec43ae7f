// AI Art Roulette - DALL-E Generated Wheel Designs
// Experimental Concept Implementation with AI-Generated Art Theme
// Designed to maintain 3-5% player win rate with artistic elements

// Game state
let balance = 1000;

// Game state object with AI art system
let aiArtRouletteGame = {
    isPlaying: false,
    artStyle: 'surreal', // surreal, abstract, photorealistic, impressionist, cyberpunk
    theme: 'cosmic', // cosmic, nature, urban, fantasy, steampunk
    betAmount: 0,
    totalBet: 0,

    // AI art generation system
    artGeneration: {
        currentPrompt: '',
        generatedImages: [],
        artQuality: 0.75, // 75% quality
        creativity: 0.80, // 80% creativity
        uniqueness: 0.70, // 70% uniqueness
        aestheticValue: 0.65, // 65% aesthetic value
        aiModel: 'DALL-E-3',
        generationTime: 15, // seconds
        artPoints: 0,
        masterpieces: 0,
        collections: []
    },

    // Roulette wheel with AI-generated designs
    rouletteWheel: {
        spinning: false,
        result: null,
        sectors: 37, // 0-36 European style
        currentDesign: 'cosmic_surreal',
        designHistory: [],
        artThemes: [
            'cosmic_nebula', 'digital_dreams', 'neon_cityscape', 'enchanted_forest',
            'steampunk_gears', 'abstract_geometry', 'ocean_depths', 'mountain_peaks',
            'desert_mirage', 'arctic_aurora', 'volcanic_fire', 'crystal_caves'
        ],
        visualEffects: {
            particles: true,
            glow: true,
            animation: true,
            transitions: true
        }
    },

    // AI-generated art collection
    artCollection: [
        {
            id: 1,
            name: 'Cosmic Roulette Wheel',
            style: 'surreal',
            theme: 'cosmic',
            prompt: 'A surreal cosmic roulette wheel floating in space with nebula colors',
            rarity: 'common',
            value: 50,
            unlocked: true
        },
        {
            id: 2,
            name: 'Digital Dreams Spinner',
            style: 'cyberpunk',
            theme: 'digital',
            prompt: 'A cyberpunk digital roulette wheel with neon lights and data streams',
            rarity: 'uncommon',
            value: 100,
            unlocked: false
        },
        {
            id: 3,
            name: 'Nature\'s Fortune Wheel',
            style: 'photorealistic',
            theme: 'nature',
            prompt: 'A photorealistic roulette wheel made of living wood and flowing water',
            rarity: 'rare',
            value: 250,
            unlocked: false
        },
        {
            id: 4,
            name: 'Abstract Probability',
            style: 'abstract',
            theme: 'mathematical',
            prompt: 'An abstract representation of probability as a geometric roulette wheel',
            rarity: 'epic',
            value: 500,
            unlocked: false
        },
        {
            id: 5,
            name: 'Impressionist Luck',
            style: 'impressionist',
            theme: 'artistic',
            prompt: 'An impressionist painting of a roulette wheel in Monet\'s style',
            rarity: 'legendary',
            value: 1000,
            unlocked: false
        }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        artGenerated: 0,
        masterpieces: 0,
        collectionsUnlocked: 0,
        artPoints: 0,
        uniqueDesigns: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Art styles with balanced aesthetic requirements (3-5% win rate)
const ART_STYLES = {
    surreal: {
        name: 'Surreal',
        creativityWeight: 0.40, // 40% creativity influence (increased)
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 0.95, // Better payouts
        aestheticBonus: 0.30, // 30% aesthetic bonus
        uniquenessBonus: 0.25 // 25% uniqueness bonus
    },
    abstract: {
        name: 'Abstract',
        creativityWeight: 0.45, // 45% creativity influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.00, // Full payouts
        aestheticBonus: 0.35, // 35% aesthetic bonus
        uniquenessBonus: 0.30 // 30% uniqueness bonus
    },
    photorealistic: {
        name: 'Photorealistic',
        creativityWeight: 0.30, // 30% creativity influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.90, // Good payouts
        aestheticBonus: 0.25, // 25% aesthetic bonus
        uniquenessBonus: 0.20 // 20% uniqueness bonus
    },
    impressionist: {
        name: 'Impressionist',
        creativityWeight: 0.35, // 35% creativity influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 0.95, // Better payouts
        aestheticBonus: 0.30, // 30% aesthetic bonus
        uniquenessBonus: 0.25 // 25% uniqueness bonus
    },
    cyberpunk: {
        name: 'Cyberpunk',
        creativityWeight: 0.50, // 50% creativity influence
        luckFactor: 0.50, // 50% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        aestheticBonus: 0.40, // 40% aesthetic bonus
        uniquenessBonus: 0.35 // 35% uniqueness bonus
    }
};

const ART_THEMES = {
    cosmic: {
        name: 'Cosmic',
        inspirationValue: 0.80, // 80% inspiration value
        complexityBonus: 0.25, // 25% complexity bonus
        rarityMultiplier: 1.20 // 20% rarity bonus
    },
    nature: {
        name: 'Nature',
        inspirationValue: 0.75, // 75% inspiration value
        complexityBonus: 0.20, // 20% complexity bonus
        rarityMultiplier: 1.15 // 15% rarity bonus
    },
    urban: {
        name: 'Urban',
        inspirationValue: 0.70, // 70% inspiration value
        complexityBonus: 0.30, // 30% complexity bonus
        rarityMultiplier: 1.10 // 10% rarity bonus
    },
    fantasy: {
        name: 'Fantasy',
        inspirationValue: 0.85, // 85% inspiration value
        complexityBonus: 0.35, // 35% complexity bonus
        rarityMultiplier: 1.25 // 25% rarity bonus
    },
    steampunk: {
        name: 'Steampunk',
        inspirationValue: 0.90, // 90% inspiration value
        complexityBonus: 0.40, // 40% complexity bonus
        rarityMultiplier: 1.30 // 30% rarity bonus
    }
};

// Improved payout table with AI art theme (3-5% win rate)
const AI_ART_PAYOUTS = {
    // Perfect artistic achievements (moderately reduced)
    MASTERPIECE: 1000, // Reduced from 2000:1 but still excellent
    ARTISTIC_GENIUS: 600, // Reduced from 1200:1
    CREATIVE_VISION: 400, // Reduced from 800:1
    AESTHETIC_BEAUTY: 300, // Reduced from 600:1

    // Roulette combinations with art bonuses
    STRAIGHT_UP: 3500, // Single number (35:1)
    SPLIT: 1700, // Two numbers (17:1)
    STREET: 1100, // Three numbers (11:1)
    CORNER: 800, // Four numbers (8:1)

    // Art bonuses (actually apply more often)
    CREATIVITY_BONUS: 0.60, // 60% of displayed bonus (increased)
    AESTHETIC_BONUS: 0.50, // 50% of displayed bonus (increased)
    UNIQUENESS_BONUS: 0.40, // 40% of displayed bonus (increased)
    COLLECTION_BONUS: 0.35 // 35% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadAIArtRouletteGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                    <h4 class="text-xl font-bold mb-4 text-pink-400">AI ART CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ART STYLE</label>
                        <select id="artStyle" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="surreal">Surreal</option>
                            <option value="abstract">Abstract</option>
                            <option value="photorealistic">Photorealistic</option>
                            <option value="impressionist">Impressionist</option>
                            <option value="cyberpunk">Cyberpunk</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ART THEME</label>
                        <select id="artTheme" class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="cosmic">Cosmic</option>
                            <option value="nature">Nature</option>
                            <option value="urban">Urban</option>
                            <option value="fantasy">Fantasy</option>
                            <option value="steampunk">Steampunk</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-pink-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="generateArt" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        GENERATE AI ART
                    </button>

                    <div id="artActions" class="space-y-2 hidden">
                        <button id="spinWheel" class="w-full py-2 rounded-lg font-bold bg-pink-600 hover:bg-pink-700 text-white">
                            SPIN ART WHEEL
                        </button>
                        <button id="enhanceArt" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            ENHANCE ARTWORK
                        </button>
                        <button id="saveToCollection" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            SAVE TO COLLECTION
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Art Points</div>
                        <div id="artPointsDisplay" class="text-lg font-bold text-pink-400">0</div>
                    </div>
                </div>

                <!-- AI Generation Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-pink-400">AI STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="artQuality" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">QUALITY: 75%</div>
                        </div>
                        <div id="creativity" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">CREATIVITY: 80%</div>
                        </div>
                        <div id="uniqueness" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">UNIQUENESS: 70%</div>
                        </div>
                        <div id="aestheticValue" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">AESTHETIC: 65%</div>
                        </div>
                    </div>
                </div>

                <!-- Art Collection Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-pink-400">ART COLLECTION</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Unlocked Artworks:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Common:</span>
                            <span class="text-green-400">1/2</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Uncommon:</span>
                            <span class="text-blue-400">0/1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rare:</span>
                            <span class="text-purple-400">0/1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Epic:</span>
                            <span class="text-orange-400">0/1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Legendary:</span>
                            <span class="text-red-400">0/1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">AI Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Creativity:</span>
                            <span class="text-pink-400">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Aesthetic:</span>
                            <span class="text-pink-400">50%</span>
                        </div>
                        <div class="text-xs text-pink-400 mt-2">*AI creativity improves odds</div>
                        <div class="text-xs text-pink-400">*Art collection unlocks bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main AI Art Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-pink-500/30">
                    <!-- AI Art Roulette Arena -->
                    <div id="aiArtArena" class="relative bg-gradient-to-br from-black via-pink-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- AI Art Background -->
                        <div id="aiArtBackground" class="absolute inset-0 pointer-events-none opacity-50">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="aiArtGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#ec4899;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#be185d;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#831843;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="aiPattern" width="120" height="120" patternUnits="userSpaceOnUse">
                                        <circle cx="60" cy="60" r="30" fill="none" stroke="#ec4899" stroke-width="2" opacity="0.3"/>
                                        <polygon points="45,45 75,45 60,75" fill="#be185d" opacity="0.4"/>
                                        <circle cx="30" cy="30" r="5" fill="#ec4899" opacity="0.5"/>
                                        <circle cx="90" cy="90" r="5" fill="#ec4899" opacity="0.5"/>
                                        <path d="M20,100 Q60,80 100,100" stroke="#be185d" stroke-width="2" fill="none" opacity="0.4"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#aiPattern)" />
                                <circle id="aiArtCore" cx="50%" cy="50%" r="25%" fill="url(#aiArtGradient)" class="animate-pulse" />
                                <g id="aiArtEffects">
                                    <!-- AI art effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- AI-Generated Roulette Wheel -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-pink-400 mb-2">AI-GENERATED ROULETTE WHEEL</div>
                                <div id="aiRouletteWheel" class="w-40 h-40 rounded-full border-4 border-pink-400 bg-gradient-to-br from-pink-800 via-purple-900 to-black relative mx-auto overflow-hidden">
                                    <!-- Dynamic AI art wheel design -->
                                    <div id="wheelDesign" class="absolute inset-0 bg-gradient-to-br from-pink-500 via-purple-600 to-blue-700 opacity-70"></div>
                                    <div id="wheelSectors" class="absolute inset-0">
                                        <!-- Roulette sectors will be drawn here -->
                                    </div>
                                    <div id="rouletteBall" class="absolute w-4 h-4 bg-white rounded-full top-2 left-1/2 transform -translate-x-1/2 transition-all duration-3000 shadow-lg"></div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div id="rouletteResult" class="text-white font-bold text-2xl drop-shadow-lg">?</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">AI-designed artistic roulette wheel</div>
                            </div>
                        </div>

                        <!-- Art Generation Progress -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-pink-400 mb-2">AI ART GENERATION</div>
                                <div id="artGenerationDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="currentPrompt" class="text-sm text-white mb-3">Ready to generate AI artwork...</div>
                                    <div class="w-full bg-gray-700 rounded-full h-3 mb-2">
                                        <div id="generationProgress" class="bg-gradient-to-r from-pink-400 to-purple-400 h-3 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-400">
                                        <span>Initializing</span>
                                        <span id="generationStatus">Ready</span>
                                        <span>Complete</span>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">AI creates unique wheel designs</div>
                            </div>
                        </div>

                        <!-- Creativity Meter -->
                        <div id="creativityMeter" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-pink-400 mb-1">CREATIVITY</div>
                                <div class="w-20 bg-gray-700 rounded-full h-3">
                                    <div id="creativityBar" class="bg-pink-400 h-3 rounded-full transition-all duration-300" style="width: 80%"></div>
                                </div>
                                <div class="text-xs text-pink-400 mt-1">80%</div>
                            </div>
                        </div>

                        <!-- Art Value Display -->
                        <div id="artValueDisplay" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">ART VALUE</div>
                                <div class="text-2xl font-bold text-white text-center">$0</div>
                                <div class="text-xs text-gray-400 mt-1">Current</div>
                            </div>
                        </div>

                        <!-- Generation Timer -->
                        <div id="generationTimer" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">GENERATION</div>
                                <div class="text-2xl font-bold text-white text-center">15s</div>
                                <div class="text-xs text-gray-400 mt-1">Time</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">AI ready to create...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="artEvent" class="text-sm font-bold text-pink-400 hidden animate-pulse">MASTERPIECE!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to AI Art Roulette - Generate Unique Wheel Designs with DALL-E</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-pink-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-pink-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Art Generated</div>
                <div id="artGenerated" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-pink-500/30 text-center">
                <div class="text-sm text-gray-400">Masterpieces</div>
                <div id="masterpieces" class="text-xl font-bold text-yellow-400">0</div>
            </div>
        </div>
    `;

    initializeAIArtRoulette();
}

// Initialize the game
function initializeAIArtRoulette() {
    document.getElementById('generateArt').addEventListener('click', generateAIArt);
    document.getElementById('spinWheel').addEventListener('click', spinAIRouletteWheel);
    document.getElementById('enhanceArt').addEventListener('click', enhanceArtwork);
    document.getElementById('saveToCollection').addEventListener('click', saveToArtCollection);

    // Initialize AI art systems
    initializeAIArtSystems();
    generateAIArtEffects();
    updateGameStats();
}

// Initialize AI art systems
function initializeAIArtSystems() {
    // Reset AI art generation system
    aiArtRouletteGame.artGeneration.currentPrompt = '';
    aiArtRouletteGame.artGeneration.generatedImages = [];
    aiArtRouletteGame.artGeneration.artQuality = Math.min(0.90, 0.75 + (aiArtRouletteGame.stats.artPoints * 0.01));
    aiArtRouletteGame.artGeneration.creativity = Math.min(0.95, 0.80 + (aiArtRouletteGame.stats.artPoints * 0.008));
    aiArtRouletteGame.artGeneration.uniqueness = Math.min(0.85, 0.70 + (aiArtRouletteGame.stats.artPoints * 0.01));
    aiArtRouletteGame.artGeneration.aestheticValue = Math.min(0.80, 0.65 + (aiArtRouletteGame.stats.artPoints * 0.01));
    aiArtRouletteGame.artGeneration.aiModel = 'DALL-E-3';
    aiArtRouletteGame.artGeneration.generationTime = 15;
    aiArtRouletteGame.artGeneration.artPoints = aiArtRouletteGame.stats.artPoints;
    aiArtRouletteGame.artGeneration.masterpieces = aiArtRouletteGame.stats.masterpieces;

    // Reset roulette wheel
    aiArtRouletteGame.rouletteWheel.spinning = false;
    aiArtRouletteGame.rouletteWheel.result = null;
    aiArtRouletteGame.rouletteWheel.currentDesign = generateRandomDesign();

    updateAIArtDisplay();
}

// Generate AI art effects
function generateAIArtEffects() {
    const container = document.getElementById('aiArtEffects');
    container.innerHTML = '';

    for (let i = 0; i < 10; i++) {
        const effect = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        effect.setAttribute('cx', `${Math.random() * 100}%`);
        effect.setAttribute('cy', `${Math.random() * 100}%`);
        effect.setAttribute('r', `${Math.random() * 3 + 1}%`);
        effect.setAttribute('fill', '#ec4899');
        effect.setAttribute('opacity', '0.6');
        effect.classList.add('animate-pulse');
        effect.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(effect);
    }
}

// Generate random design name
function generateRandomDesign() {
    const themes = aiArtRouletteGame.rouletteWheel.artThemes;
    return themes[Math.floor(Math.random() * themes.length)];
}

// Generate AI art
function generateAIArt() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    aiArtRouletteGame.isPlaying = true;
    aiArtRouletteGame.betAmount = betAmount;
    aiArtRouletteGame.totalBet = betAmount;
    aiArtRouletteGame.artStyle = document.getElementById('artStyle').value;
    aiArtRouletteGame.theme = document.getElementById('artTheme').value;

    // Start AI art generation
    startAIArtGeneration();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('generateArt').disabled = true;
    document.getElementById('gameStatus').textContent = 'AI generating artwork...';
}

// Start AI art generation
function startAIArtGeneration() {
    const styleData = ART_STYLES[aiArtRouletteGame.artStyle];
    const themeData = ART_THEMES[aiArtRouletteGame.theme];

    // Generate art prompt
    const prompt = generateArtPrompt();
    aiArtRouletteGame.artGeneration.currentPrompt = prompt;

    // Update prompt display
    document.getElementById('currentPrompt').textContent = prompt;
    document.getElementById('generationStatus').textContent = 'Generating...';

    // Simulate AI generation process
    simulateAIGeneration();

    // Update AI status
    updateAIStatus();

    // Update visual effects
    updateAIArtDisplay();
    updateAIArtEffects();
}

// Generate art prompt
function generateArtPrompt() {
    const style = ART_STYLES[aiArtRouletteGame.artStyle].name;
    const theme = ART_THEMES[aiArtRouletteGame.theme].name;
    const design = aiArtRouletteGame.rouletteWheel.currentDesign;

    const prompts = [
        `A ${style.toLowerCase()} ${theme.toLowerCase()} roulette wheel with ${design.replace('_', ' ')} elements`,
        `${style} interpretation of a ${theme.toLowerCase()} casino roulette wheel featuring ${design.replace('_', ' ')}`,
        `Artistic ${style.toLowerCase()} roulette wheel design inspired by ${theme.toLowerCase()} and ${design.replace('_', ' ')}`,
        `${theme} themed roulette wheel in ${style.toLowerCase()} art style with ${design.replace('_', ' ')} motifs`,
        `A ${style.toLowerCase()} masterpiece: ${theme.toLowerCase()} roulette wheel with ${design.replace('_', ' ')} aesthetics`
    ];

    return prompts[Math.floor(Math.random() * prompts.length)];
}

// Simulate AI generation process
function simulateAIGeneration() {
    let progress = 0;
    const generationTime = aiArtRouletteGame.artGeneration.generationTime;

    const progressInterval = setInterval(() => {
        progress += 100 / (generationTime * 10); // Update every 100ms

        // Update progress bar
        document.getElementById('generationProgress').style.width = `${Math.min(100, progress)}%`;

        // Update timer
        const timeRemaining = Math.max(0, generationTime - Math.floor(progress / (100 / generationTime)));
        document.getElementById('generationTimer').querySelector('.text-2xl').textContent = `${timeRemaining}s`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('generationStatus').textContent = 'Initializing AI...';
        } else if (progress < 40) {
            document.getElementById('generationStatus').textContent = 'Processing prompt...';
        } else if (progress < 60) {
            document.getElementById('generationStatus').textContent = 'Generating art...';
        } else if (progress < 80) {
            document.getElementById('generationStatus').textContent = 'Refining details...';
        } else if (progress < 100) {
            document.getElementById('generationStatus').textContent = 'Finalizing...';
        } else {
            document.getElementById('generationStatus').textContent = 'Complete!';
            clearInterval(progressInterval);
            completeAIGeneration();
        }
    }, 100);
}

// Complete AI generation
function completeAIGeneration() {
    // Calculate art quality with creativity influence
    const styleData = ART_STYLES[aiArtRouletteGame.artStyle];
    const themeData = ART_THEMES[aiArtRouletteGame.theme];

    // Apply creativity and skill bonuses
    const creativityBonus = aiArtRouletteGame.artGeneration.creativity * styleData.creativityWeight;
    const themeBonus = themeData.inspirationValue * 0.1;
    const skillBonus = aiArtRouletteGame.stats.artPoints * 0.005;

    // Update art metrics
    aiArtRouletteGame.artGeneration.artQuality = Math.min(0.95,
        aiArtRouletteGame.artGeneration.artQuality + creativityBonus + themeBonus + skillBonus);

    // Generate unique wheel design
    generateUniqueWheelDesign();

    // Award art points
    const pointsEarned = Math.floor(aiArtRouletteGame.artGeneration.artQuality * 10);
    aiArtRouletteGame.artGeneration.artPoints += pointsEarned;
    aiArtRouletteGame.stats.artPoints += pointsEarned;
    aiArtRouletteGame.stats.artGenerated++;

    // Check for masterpiece
    if (aiArtRouletteGame.artGeneration.artQuality >= 0.90) {
        aiArtRouletteGame.artGeneration.masterpieces++;
        aiArtRouletteGame.stats.masterpieces++;

        // Show masterpiece notification
        document.getElementById('artEvent').classList.remove('hidden');
        document.getElementById('artEvent').textContent = 'MASTERPIECE CREATED!';
        setTimeout(() => {
            document.getElementById('artEvent').classList.add('hidden');
        }, 3000);
    }

    // Enable actions
    document.getElementById('artActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'AI artwork generated! Ready to spin.';

    // Update displays
    updateAIStatus();
    updateArtValueDisplay();
}

// Generate unique wheel design
function generateUniqueWheelDesign() {
    const wheelDesign = document.getElementById('wheelDesign');
    const style = aiArtRouletteGame.artStyle;
    const theme = aiArtRouletteGame.theme;

    // Create unique gradient based on style and theme
    let gradient = '';

    switch (style) {
        case 'surreal':
            gradient = 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4)';
            break;
        case 'abstract':
            gradient = 'linear-gradient(135deg, #667eea, #764ba2, #f093fb, #f5576c)';
            break;
        case 'photorealistic':
            gradient = 'linear-gradient(180deg, #2c3e50, #34495e, #7f8c8d, #95a5a6)';
            break;
        case 'impressionist':
            gradient = 'linear-gradient(90deg, #ffecd2, #fcb69f, #a8edea, #fed6e3)';
            break;
        case 'cyberpunk':
            gradient = 'linear-gradient(225deg, #ff0080, #00ffff, #8000ff, #ff4000)';
            break;
    }

    // Apply theme modifications
    switch (theme) {
        case 'cosmic':
            gradient += ', radial-gradient(circle, rgba(138,43,226,0.3) 0%, transparent 70%)';
            break;
        case 'nature':
            gradient += ', radial-gradient(circle, rgba(34,139,34,0.3) 0%, transparent 70%)';
            break;
        case 'urban':
            gradient += ', radial-gradient(circle, rgba(105,105,105,0.3) 0%, transparent 70%)';
            break;
        case 'fantasy':
            gradient += ', radial-gradient(circle, rgba(255,215,0,0.3) 0%, transparent 70%)';
            break;
        case 'steampunk':
            gradient += ', radial-gradient(circle, rgba(139,69,19,0.3) 0%, transparent 70%)';
            break;
    }

    wheelDesign.style.background = gradient;

    // Add unique design ID to history
    const designId = `${style}_${theme}_${Date.now()}`;
    aiArtRouletteGame.rouletteWheel.designHistory.push(designId);
    aiArtRouletteGame.stats.uniqueDesigns++;

    // Generate wheel sectors
    generateWheelSectors();
}

// Generate wheel sectors
function generateWheelSectors() {
    const sectorsContainer = document.getElementById('wheelSectors');
    sectorsContainer.innerHTML = '';

    // Create 37 sectors (0-36 European roulette)
    for (let i = 0; i < 37; i++) {
        const sector = document.createElement('div');
        sector.className = 'absolute w-1 h-8 origin-bottom';
        sector.style.left = '50%';
        sector.style.bottom = '50%';
        sector.style.transform = `translateX(-50%) rotate(${i * (360/37)}deg)`;

        // Color sectors based on roulette rules
        if (i === 0) {
            sector.style.backgroundColor = '#00ff00'; // Green for 0
        } else if ([1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(i)) {
            sector.style.backgroundColor = '#ff0000'; // Red
        } else {
            sector.style.backgroundColor = '#000000'; // Black
        }

        sector.style.opacity = '0.7';
        sectorsContainer.appendChild(sector);
    }
}

// Spin AI roulette wheel with creativity influence (3-5% win rate)
function spinAIRouletteWheel() {
    if (!aiArtRouletteGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Spinning AI art roulette...';

    // Calculate creativity influence on odds
    const creativityBonus = calculateCreativityBonus();

    // Generate roulette result with improved odds
    const rouletteResult = generateAIRouletteResult(creativityBonus);

    // Animate wheel spin
    animateAIWheelSpin(rouletteResult);

    // Resolve after spin
    setTimeout(() => {
        resolveAIRouletteSpin(rouletteResult, creativityBonus);
    }, 4000);
}

// Calculate creativity bonus
function calculateCreativityBonus() {
    const styleData = ART_STYLES[aiArtRouletteGame.artStyle];
    const themeData = ART_THEMES[aiArtRouletteGame.theme];

    const creativityBonus = aiArtRouletteGame.artGeneration.creativity * styleData.creativityWeight * 0.15; // Up to 15% bonus
    const aestheticBonus = aiArtRouletteGame.artGeneration.aestheticValue * styleData.aestheticBonus * 0.10; // Up to 10% bonus
    const uniquenessBonus = aiArtRouletteGame.artGeneration.uniqueness * styleData.uniquenessBonus * 0.08; // Up to 8% bonus
    const themeBonus = themeData.inspirationValue * 0.05; // Up to 5% bonus
    const artPointsBonus = aiArtRouletteGame.stats.artPoints * 0.001; // Art points bonus

    return Math.min(0.25, creativityBonus + aestheticBonus + uniquenessBonus + themeBonus + artPointsBonus);
}

// Generate AI roulette result with creativity influence (improved for 3-5% win rate)
function generateAIRouletteResult(creativityBonus) {
    const styleData = ART_STYLES[aiArtRouletteGame.artStyle];

    // Apply creativity bonus to improve odds
    const adjustedOdds = 0.027 + creativityBonus; // Base 2.7% + creativity bonus

    // Generate result
    let result = {
        number: Math.floor(Math.random() * 37), // 0-36
        color: 'black',
        isWin: false,
        multiplier: 1.0,
        artBonus: 0
    };

    // Determine color
    if (result.number === 0) {
        result.color = 'green';
    } else if ([1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(result.number)) {
        result.color = 'red';
    } else {
        result.color = 'black';
    }

    // Check for win with improved odds
    if (Math.random() < adjustedOdds) {
        result.isWin = true;

        // Determine win type and multiplier
        if (result.number === aiArtRouletteGame.betAmount % 37) { // Straight up bet simulation
            result.multiplier = 35.0; // 35:1 payout
        } else if (result.color === 'red' || result.color === 'black') {
            result.multiplier = 2.0; // Even money bet
        } else if (result.color === 'green') {
            result.multiplier = 35.0; // Green pays 35:1
        } else {
            result.multiplier = 1.8; // Modest win
        }

        // Add art bonus based on quality
        if (aiArtRouletteGame.artGeneration.artQuality >= 0.90) {
            result.artBonus = 0.5; // 50% art bonus for masterpieces
        } else if (aiArtRouletteGame.artGeneration.artQuality >= 0.80) {
            result.artBonus = 0.3; // 30% art bonus for high quality
        } else if (aiArtRouletteGame.artGeneration.artQuality >= 0.70) {
            result.artBonus = 0.2; // 20% art bonus for good quality
        }
    }

    return result;
}

// Animate AI wheel spin
function animateAIWheelSpin(result) {
    const wheel = document.getElementById('aiRouletteWheel');
    const ball = document.getElementById('rouletteBall');
    const resultDisplay = document.getElementById('rouletteResult');

    // Add spinning animation with AI art effects
    wheel.style.transform = 'rotate(1800deg)'; // 5 full rotations
    wheel.style.transition = 'transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

    // Animate ball with artistic trail effect
    ball.style.transform = 'rotate(-2160deg) translateX(60px)'; // Counter-rotation with radius
    ball.style.transition = 'transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    ball.style.boxShadow = '0 0 20px #ec4899, 0 0 40px #be185d'; // AI art glow

    // Update result display
    setTimeout(() => {
        resultDisplay.textContent = result.number;
        resultDisplay.className = `text-white font-bold text-2xl drop-shadow-lg ${
            result.color === 'red' ? 'text-red-400' :
            result.color === 'green' ? 'text-green-400' : 'text-gray-300'
        }`;

        // Reset transforms
        wheel.style.transform = 'rotate(0deg)';
        wheel.style.transition = 'none';
        ball.style.transform = 'rotate(0deg) translateX(0px)';
        ball.style.transition = 'none';
        ball.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
    }, 4000);
}

// Resolve AI roulette spin with creativity bonuses (3-5% win rate)
function resolveAIRouletteSpin(result, creativityBonus) {
    const styleData = ART_STYLES[aiArtRouletteGame.artStyle];
    const themeData = ART_THEMES[aiArtRouletteGame.theme];

    let totalWinnings = 0;
    let resultMessage = '';

    if (result.isWin) {
        // Base payout
        totalWinnings = aiArtRouletteGame.betAmount * result.multiplier;

        // Apply creativity bonus (actually works)
        if (aiArtRouletteGame.artGeneration.creativity >= 0.85 && totalWinnings > 0) {
            const creativityBonusAmount = Math.floor(totalWinnings * AI_ART_PAYOUTS.CREATIVITY_BONUS);
            totalWinnings += creativityBonusAmount;
            resultMessage += ' + Creativity Bonus!';
        }

        // Apply aesthetic bonus
        if (aiArtRouletteGame.artGeneration.aestheticValue >= 0.80 && totalWinnings > 0) {
            const aestheticBonusAmount = Math.floor(totalWinnings * AI_ART_PAYOUTS.AESTHETIC_BONUS);
            totalWinnings += aestheticBonusAmount;
            resultMessage += ' + Aesthetic Bonus!';
        }

        // Apply uniqueness bonus
        if (aiArtRouletteGame.artGeneration.uniqueness >= 0.75 && totalWinnings > 0) {
            const uniquenessBonusAmount = Math.floor(totalWinnings * AI_ART_PAYOUTS.UNIQUENESS_BONUS);
            totalWinnings += uniquenessBonusAmount;
            resultMessage += ' + Uniqueness Bonus!';
        }

        // Apply art bonus from result
        if (result.artBonus > 0 && totalWinnings > 0) {
            const artBonusAmount = Math.floor(totalWinnings * result.artBonus);
            totalWinnings += artBonusAmount;
            resultMessage += ' + Art Quality Bonus!';
        }

        // Check for special achievements
        if (aiArtRouletteGame.artGeneration.artQuality >= 0.95) {
            const masterpieceBonus = Math.floor(aiArtRouletteGame.betAmount * AI_ART_PAYOUTS.MASTERPIECE / 100);
            totalWinnings += masterpieceBonus;
            resultMessage += ' + Masterpiece!';
        } else if (aiArtRouletteGame.artGeneration.artQuality >= 0.90) {
            const geniusBonus = Math.floor(aiArtRouletteGame.betAmount * AI_ART_PAYOUTS.ARTISTIC_GENIUS / 100);
            totalWinnings += geniusBonus;
            resultMessage += ' + Artistic Genius!';
        }

        resultMessage = `AI art win!${resultMessage}`;
    } else {
        resultMessage = 'Art didn\'t align with fortune';

        // Small consolation for high-quality art (improved)
        if (aiArtRouletteGame.artGeneration.artQuality >= 0.85 && Math.random() < 0.15) {
            totalWinnings = Math.floor(aiArtRouletteGame.betAmount * 0.3); // 30% return
            resultMessage += ' - Artistic consolation!';
        }
    }

    // Apply style multiplier
    totalWinnings = Math.floor(totalWinnings * styleData.payoutMultiplier);

    // Apply theme rarity multiplier
    totalWinnings = Math.floor(totalWinnings * themeData.rarityMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(aiArtRouletteGame.betAmount * 0.4); // 40% return
        resultMessage = 'AI inspiration bonus';
    }

    // Add winnings to balance
    balance += totalWinnings;
    aiArtRouletteGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(result.isWin, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Style: ${ART_STYLES[aiArtRouletteGame.artStyle].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Enhance artwork
function enhanceArtwork() {
    if (!aiArtRouletteGame.isPlaying) return;

    // Enhance art quality
    aiArtRouletteGame.artGeneration.artQuality = Math.min(0.98, aiArtRouletteGame.artGeneration.artQuality + 0.05);
    aiArtRouletteGame.artGeneration.aestheticValue = Math.min(0.95, aiArtRouletteGame.artGeneration.aestheticValue + 0.03);

    // Award art points
    aiArtRouletteGame.stats.artPoints += 2;

    // Visual feedback
    document.getElementById('artEvent').classList.remove('hidden');
    document.getElementById('artEvent').textContent = 'ARTWORK ENHANCED!';
    setTimeout(() => {
        document.getElementById('artEvent').classList.add('hidden');
    }, 2000);

    updateAIStatus();
    updateArtValueDisplay();
}

// Save to art collection
function saveToArtCollection() {
    if (!aiArtRouletteGame.isPlaying) return;

    // Create new artwork entry
    const newArtwork = {
        id: Date.now(),
        name: `${aiArtRouletteGame.artStyle} ${aiArtRouletteGame.theme} Wheel`,
        style: aiArtRouletteGame.artStyle,
        theme: aiArtRouletteGame.theme,
        prompt: aiArtRouletteGame.artGeneration.currentPrompt,
        quality: aiArtRouletteGame.artGeneration.artQuality,
        value: Math.floor(aiArtRouletteGame.artGeneration.artQuality * 1000),
        created: new Date().toISOString()
    };

    aiArtRouletteGame.artGeneration.collections.push(newArtwork);
    aiArtRouletteGame.stats.collectionsUnlocked++;

    // Award art points
    aiArtRouletteGame.stats.artPoints += 5;

    // Visual feedback
    document.getElementById('artEvent').classList.remove('hidden');
    document.getElementById('artEvent').textContent = 'SAVED TO COLLECTION!';
    setTimeout(() => {
        document.getElementById('artEvent').classList.add('hidden');
    }, 2000);

    updateAIStatus();
}

// Update AI art display
function updateAIArtDisplay() {
    updateAIStatus();
    updateArtValueDisplay();
}

// Update AI status
function updateAIStatus() {
    document.getElementById('artQuality').innerHTML =
        `<div class="text-green-400 font-bold">QUALITY: ${Math.floor(aiArtRouletteGame.artGeneration.artQuality * 100)}%</div>`;
    document.getElementById('creativity').innerHTML =
        `<div class="text-blue-400 font-bold">CREATIVITY: ${Math.floor(aiArtRouletteGame.artGeneration.creativity * 100)}%</div>`;
    document.getElementById('uniqueness').innerHTML =
        `<div class="text-purple-400 font-bold">UNIQUENESS: ${Math.floor(aiArtRouletteGame.artGeneration.uniqueness * 100)}%</div>`;
    document.getElementById('aestheticValue').innerHTML =
        `<div class="text-yellow-400 font-bold">AESTHETIC: ${Math.floor(aiArtRouletteGame.artGeneration.aestheticValue * 100)}%</div>`;

    // Update creativity meter
    const creativityPercentage = aiArtRouletteGame.artGeneration.creativity * 100;
    document.getElementById('creativityBar').style.width = `${creativityPercentage}%`;
    document.getElementById('creativityMeter').querySelector('.text-xs:last-child').textContent = `${Math.floor(creativityPercentage)}%`;
}

// Update art value display
function updateArtValueDisplay() {
    const artValue = Math.floor(aiArtRouletteGame.artGeneration.artQuality * 1000);
    document.getElementById('artValueDisplay').querySelector('.text-2xl').textContent = `$${artValue}`;
}

// Update AI art effects
function updateAIArtEffects() {
    // Update AI art effects based on creativity level
    const creativity = aiArtRouletteGame.artGeneration.creativity;
    const effects = document.querySelectorAll('#aiArtEffects circle');

    effects.forEach((effect, index) => {
        if (creativity >= 0.90) {
            effect.setAttribute('opacity', '0.9');
            effect.setAttribute('fill', '#ffd700'); // Gold for high creativity
        } else if (creativity >= 0.80) {
            effect.setAttribute('opacity', '0.7');
            effect.setAttribute('fill', '#ec4899'); // Pink for good creativity
        } else {
            effect.setAttribute('opacity', '0.5');
            effect.setAttribute('fill', '#be185d'); // Dark pink for lower creativity
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${aiArtRouletteGame.betAmount}`;
    document.getElementById('artPointsDisplay').textContent = aiArtRouletteGame.stats.artPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = aiArtRouletteGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${aiArtRouletteGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${aiArtRouletteGame.stats.totalWagered}`;
    document.getElementById('artGenerated').textContent = aiArtRouletteGame.stats.artGenerated;
    document.getElementById('masterpieces').textContent = aiArtRouletteGame.stats.masterpieces;

    const netResult = aiArtRouletteGame.stats.totalWon - aiArtRouletteGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-pink-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    aiArtRouletteGame.stats.spinsPlayed++;
    aiArtRouletteGame.stats.totalWagered += aiArtRouletteGame.betAmount;
    aiArtRouletteGame.stats.totalWon += winnings;

    if (won) {
        aiArtRouletteGame.stats.spinsWon++;
        aiArtRouletteGame.stats.currentStreak++;
        aiArtRouletteGame.streakData.currentWinStreak++;
        aiArtRouletteGame.streakData.currentLossStreak = 0;

        if (aiArtRouletteGame.streakData.currentWinStreak > aiArtRouletteGame.streakData.longestWinStreak) {
            aiArtRouletteGame.streakData.longestWinStreak = aiArtRouletteGame.streakData.currentWinStreak;
        }

        if (winnings > aiArtRouletteGame.stats.biggestWin) {
            aiArtRouletteGame.stats.biggestWin = winnings;
        }
    } else {
        aiArtRouletteGame.stats.currentStreak = 0;
        aiArtRouletteGame.streakData.currentWinStreak = 0;
        aiArtRouletteGame.streakData.currentLossStreak++;

        if (aiArtRouletteGame.streakData.currentLossStreak > aiArtRouletteGame.streakData.longestLossStreak) {
            aiArtRouletteGame.streakData.longestLossStreak = aiArtRouletteGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to AI art mechanics)
    aiArtRouletteGame.stats.winRate = (aiArtRouletteGame.stats.spinsWon / aiArtRouletteGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next AI art session
function resetGame() {
    aiArtRouletteGame.isPlaying = false;
    aiArtRouletteGame.betAmount = 0;
    aiArtRouletteGame.totalBet = 0;
    aiArtRouletteGame.gameResult = '';
    aiArtRouletteGame.totalWin = 0;

    // Reset AI generation system
    aiArtRouletteGame.artGeneration.currentPrompt = '';
    aiArtRouletteGame.artGeneration.generatedImages = [];

    // Reset roulette wheel
    aiArtRouletteGame.rouletteWheel.spinning = false;
    aiArtRouletteGame.rouletteWheel.result = null;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('artEvent').classList.add('hidden');
    document.getElementById('artActions').classList.add('hidden');

    // Reset generation display
    document.getElementById('currentPrompt').textContent = 'Ready to generate AI artwork...';
    document.getElementById('generationProgress').style.width = '0%';
    document.getElementById('generationStatus').textContent = 'Ready';
    document.getElementById('generationTimer').querySelector('.text-2xl').textContent = '15s';

    // Reset roulette display
    document.getElementById('rouletteResult').textContent = '?';
    document.getElementById('rouletteResult').className = 'text-white font-bold text-2xl drop-shadow-lg';

    // Reset wheel design
    const wheelDesign = document.getElementById('wheelDesign');
    wheelDesign.style.background = 'linear-gradient(45deg, #ec4899, #be185d, #831843)';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable generate button
    document.getElementById('generateArt').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'AI ready to create...';
    document.getElementById('gameMessage').textContent = 'Welcome to AI Art Roulette - Generate Unique Wheel Designs with DALL-E';

    // Reinitialize systems for next session
    initializeAIArtSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadAIArtRouletteGame();
});