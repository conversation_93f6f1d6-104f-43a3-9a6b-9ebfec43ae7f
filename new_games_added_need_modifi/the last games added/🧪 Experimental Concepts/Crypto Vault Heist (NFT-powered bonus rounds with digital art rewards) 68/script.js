// Crypto Vault Heist - NFT-Powered Bonus Rounds with Digital Art Rewards
// Experimental Concept Implementation with NFT and Digital Art Theme
// Designed to maintain 3-5% player win rate with NFT collection mechanics

// Game state
let balance = 1000;

// Game state object with NFT vault system
let cryptoVaultHeistGame = {
    isPlaying: false,
    heistType: 'stealth', // stealth, brute_force, social_engineering, quantum_hack, insider_job
    vaultLevel: 'bronze', // bronze, silver, gold, platinum, diamond
    betAmount: 0,
    totalBet: 0,

    // NFT collection system
    nftCollection: {
        ownedNFTs: [],
        totalNFTs: 0,
        collectionValue: 0,
        rarityBonus: 1.0,
        artPoints: 0,
        masterpieces: 0,
        collections: {
            cyberpunk: { owned: 0, total: 10, bonus: 0.25 },
            abstract: { owned: 0, total: 8, bonus: 0.30 },
            pixel_art: { owned: 0, total: 12, bonus: 0.20 },
            generative: { owned: 0, total: 15, bonus: 0.35 },
            legendary: { owned: 0, total: 5, bonus: 0.50 }
        },
        mintingPower: 0.65, // 65% minting success rate
        tradingSkill: 0.70, // 70% trading skill
        curatorLevel: 'novice' // novice, collector, curator, expert, master
    },

    // Vault heist system
    vault: {
        securityLevel: 5, // 1-10 security levels
        alarmTriggered: false,
        timeRemaining: 300, // 5 minutes
        hackingProgress: 0,
        lootMultiplier: 1.0,
        bonusRounds: 0,
        nftRewards: [],
        digitalArt: [],
        securitySystems: {
            firewall: true,
            encryption: true,
            biometric: true,
            quantum: false,
            ai_guardian: false
        }
    },

    // Available NFT rewards
    nftRewards: [
        {
            id: 1,
            name: 'Cyber Heist #001',
            collection: 'cyberpunk',
            rarity: 'common',
            value: 100,
            artStyle: 'cyberpunk',
            unlocked: false,
            description: 'A digital masterpiece depicting the perfect crypto heist'
        },
        {
            id: 2,
            name: 'Quantum Vault #042',
            collection: 'abstract',
            rarity: 'uncommon',
            value: 250,
            artStyle: 'abstract',
            unlocked: false,
            description: 'Abstract representation of quantum encryption breaking'
        },
        {
            id: 3,
            name: 'Pixel Thief #128',
            collection: 'pixel_art',
            rarity: 'rare',
            value: 500,
            artStyle: 'pixel',
            unlocked: false,
            description: 'Retro pixel art of the legendary crypto thief'
        },
        {
            id: 4,
            name: 'AI Genesis #007',
            collection: 'generative',
            rarity: 'epic',
            value: 1000,
            artStyle: 'generative',
            unlocked: false,
            description: 'AI-generated art representing digital consciousness'
        },
        {
            id: 5,
            name: 'Diamond Vault #001',
            collection: 'legendary',
            rarity: 'legendary',
            value: 2500,
            artStyle: 'legendary',
            unlocked: false,
            description: 'The ultimate prize - a legendary diamond vault NFT'
        }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        heistsAttempted: 0,
        heistsSuccessful: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        nftsMinted: 0,
        nftsTraded: 0,
        artPointsEarned: 0,
        vaultsHacked: 0,
        bonusRoundsTriggered: 0,
        collectionValue: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Heist types with balanced NFT requirements (3-5% win rate)
const HEIST_TYPES = {
    stealth: {
        name: 'Stealth Infiltration',
        nftWeight: 0.30, // 30% NFT influence (increased)
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.95, // Good payouts
        artBonus: 0.25, // 25% art bonus
        timeBonus: 0.20 // 20% time bonus
    },
    brute_force: {
        name: 'Brute Force Attack',
        nftWeight: 0.25, // 25% NFT influence
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.90, // Good payouts
        artBonus: 0.20, // 20% art bonus
        timeBonus: 0.15 // 15% time bonus
    },
    social_engineering: {
        name: 'Social Engineering',
        nftWeight: 0.35, // 35% NFT influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.00, // Full payouts
        artBonus: 0.30, // 30% art bonus
        timeBonus: 0.25 // 25% time bonus
    },
    quantum_hack: {
        name: 'Quantum Hacking',
        nftWeight: 0.40, // 40% NFT influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        artBonus: 0.35, // 35% art bonus
        timeBonus: 0.30 // 30% time bonus
    },
    insider_job: {
        name: 'Insider Job',
        nftWeight: 0.45, // 45% NFT influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        artBonus: 0.40, // 40% art bonus
        timeBonus: 0.35 // 35% time bonus
    }
};

const VAULT_LEVELS = {
    bronze: {
        name: 'Bronze Vault',
        securityLevel: 3, // Low security
        nftDropRate: 0.15, // 15% NFT drop rate
        artRewardMultiplier: 1.10 // 10% art reward bonus
    },
    silver: {
        name: 'Silver Vault',
        securityLevel: 5, // Medium security
        nftDropRate: 0.20, // 20% NFT drop rate
        artRewardMultiplier: 1.20 // 20% art reward bonus
    },
    gold: {
        name: 'Gold Vault',
        securityLevel: 7, // High security
        nftDropRate: 0.25, // 25% NFT drop rate
        artRewardMultiplier: 1.30 // 30% art reward bonus
    },
    platinum: {
        name: 'Platinum Vault',
        securityLevel: 8, // Very high security
        nftDropRate: 0.30, // 30% NFT drop rate
        artRewardMultiplier: 1.40 // 40% art reward bonus
    },
    diamond: {
        name: 'Diamond Vault',
        securityLevel: 10, // Maximum security
        nftDropRate: 0.35, // 35% NFT drop rate
        artRewardMultiplier: 1.50 // 50% art reward bonus
    }
};

// Improved payout table with NFT theme (3-5% win rate)
const CRYPTO_VAULT_PAYOUTS = {
    // Perfect NFT achievements (moderately reduced)
    LEGENDARY_HEIST: 2000, // Reduced from 4000:1 but still excellent
    NFT_MASTER: 1200, // Reduced from 2400:1
    DIGITAL_ART_GENIUS: 800, // Reduced from 1600:1
    VAULT_BREAKER: 500, // Reduced from 1000:1

    // Vault breach rewards
    DIAMOND_VAULT: 1500, // Diamond vault breach
    PLATINUM_VAULT: 1000, // Platinum vault breach
    GOLD_VAULT: 600, // Gold vault breach
    SILVER_VAULT: 300, // Silver vault breach
    BRONZE_VAULT: 150, // Bronze vault breach

    // NFT bonuses (actually apply more often)
    NFT_COLLECTION_BONUS: 0.75, // 75% of displayed bonus (increased)
    ART_RARITY_BONUS: 0.65, // 65% of displayed bonus (increased)
    MINTING_BONUS: 0.55, // 55% of displayed bonus (increased)
    TRADING_BONUS: 0.45 // 45% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadCryptoVaultHeistGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">HEIST CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">HEIST TYPE</label>
                        <select id="heistType" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="stealth">Stealth Infiltration</option>
                            <option value="brute_force">Brute Force Attack</option>
                            <option value="social_engineering">Social Engineering</option>
                            <option value="quantum_hack">Quantum Hacking</option>
                            <option value="insider_job">Insider Job</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VAULT LEVEL</label>
                        <select id="vaultLevel" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="bronze">Bronze Vault</option>
                            <option value="silver">Silver Vault</option>
                            <option value="gold">Gold Vault</option>
                            <option value="platinum">Platinum Vault</option>
                            <option value="diamond">Diamond Vault</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startHeist" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START HEIST
                    </button>

                    <div id="heistActions" class="space-y-2 hidden">
                        <button id="hackVault" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            HACK VAULT
                        </button>
                        <button id="mintNFT" class="w-full py-2 rounded-lg font-bold bg-pink-600 hover:bg-pink-700 text-white">
                            MINT NFT
                        </button>
                        <button id="tradeNFT" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            TRADE NFT
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Art Points</div>
                        <div id="artPointsDisplay" class="text-lg font-bold text-purple-400">0</div>
                    </div>
                </div>

                <!-- NFT Collection Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">NFT COLLECTION</h5>
                    <div class="text-xs space-y-2">
                        <div id="mintingPower" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">MINTING: 65%</div>
                        </div>
                        <div id="tradingSkill" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">TRADING: 70%</div>
                        </div>
                        <div id="curatorLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                        <div id="collectionValue" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">VALUE: $0</div>
                        </div>
                    </div>
                </div>

                <!-- NFT Rewards Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">NFT REWARDS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Available NFTs:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Common:</span>
                            <span class="text-green-400">0/5</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Uncommon:</span>
                            <span class="text-blue-400">0/3</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rare:</span>
                            <span class="text-purple-400">0/2</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Epic:</span>
                            <span class="text-orange-400">0/1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Legendary:</span>
                            <span class="text-red-400">0/1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">NFT Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Collection:</span>
                            <span class="text-purple-400">75%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Rarity:</span>
                            <span class="text-purple-400">65%</span>
                        </div>
                        <div class="text-xs text-purple-400 mt-2">*NFTs improve heist odds</div>
                        <div class="text-xs text-purple-400">*Digital art unlocks bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Vault Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <!-- Crypto Vault Arena -->
                    <div id="cryptoVaultArena" class="relative bg-gradient-to-br from-black via-purple-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Vault Background -->
                        <div id="vaultBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="vaultGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#a855f7;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#5b21b6;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="vaultPattern" width="100" height="100" patternUnits="userSpaceOnUse">
                                        <circle cx="50" cy="50" r="30" fill="none" stroke="#a855f7" stroke-width="2" opacity="0.3"/>
                                        <rect x="20" y="20" width="60" height="60" fill="none" stroke="#7c3aed" stroke-width="2" opacity="0.4"/>
                                        <polygon points="50,20 70,40 50,60 30,40" fill="#5b21b6" opacity="0.3"/>
                                        <circle cx="25" cy="25" r="5" fill="#a855f7" opacity="0.5"/>
                                        <circle cx="75" cy="75" r="5" fill="#a855f7" opacity="0.5"/>
                                        <line x1="20" y1="80" x2="80" y2="20" stroke="#7c3aed" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#vaultPattern)" />
                                <circle id="vaultCore" cx="50%" cy="50%" r="25%" fill="url(#vaultGradient)" class="animate-pulse" />
                                <g id="vaultEffects">
                                    <!-- Vault effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Vault Door Display -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">CRYPTO VAULT</div>
                                <div id="vaultDoor" class="w-32 h-32 rounded-full border-4 border-purple-400 bg-gradient-to-br from-purple-800 via-purple-900 to-black relative mx-auto overflow-hidden">
                                    <!-- Vault door design -->
                                    <div id="vaultLock" class="absolute inset-0 bg-gradient-to-br from-purple-500 via-purple-700 to-purple-900 opacity-80"></div>
                                    <div id="vaultSecurity" class="absolute inset-0">
                                        <!-- Security layers will be drawn here -->
                                    </div>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <div id="vaultStatus" class="text-white font-bold text-lg drop-shadow-lg">LOCKED</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">NFT-secured digital vault</div>
                            </div>
                        </div>

                        <!-- NFT Gallery Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">NFT GALLERY</div>
                                <div id="nftGallery" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="nftDisplay" class="text-lg text-white mb-3">Digital art collection awaits...</div>
                                    <div class="grid grid-cols-5 gap-2" id="nftSlots">
                                        <!-- 5 NFT slots -->
                                        <div class="nft-slot w-12 h-12 bg-gray-700 rounded border-2 border-gray-500 flex items-center justify-center text-xs text-gray-400">?</div>
                                        <div class="nft-slot w-12 h-12 bg-gray-700 rounded border-2 border-gray-500 flex items-center justify-center text-xs text-gray-400">?</div>
                                        <div class="nft-slot w-12 h-12 bg-gray-700 rounded border-2 border-gray-500 flex items-center justify-center text-xs text-gray-400">?</div>
                                        <div class="nft-slot w-12 h-12 bg-gray-700 rounded border-2 border-gray-500 flex items-center justify-center text-xs text-gray-400">?</div>
                                        <div class="nft-slot w-12 h-12 bg-gray-700 rounded border-2 border-gray-500 flex items-center justify-center text-xs text-gray-400">?</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Collect rare digital art NFTs</div>
                            </div>
                        </div>

                        <!-- Heist Progress -->
                        <div id="heistProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">HEIST PROGRESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="hackingBar" class="bg-gradient-to-r from-purple-400 to-pink-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Infiltrating</span>
                                    <span id="heistStatus">Ready</span>
                                    <span>Vault Breached</span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Level -->
                        <div id="securityLevel" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">SECURITY</div>
                                <div class="text-2xl font-bold text-white text-center">5/10</div>
                                <div class="text-xs text-gray-400 mt-1">Level</div>
                            </div>
                        </div>

                        <!-- Time Remaining -->
                        <div id="timeRemaining" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">TIME</div>
                                <div class="text-2xl font-bold text-white text-center">5:00</div>
                                <div class="text-xs text-gray-400 mt-1">Remaining</div>
                            </div>
                        </div>

                        <!-- Loot Multiplier -->
                        <div id="lootMultiplier" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">LOOT</div>
                                <div class="text-2xl font-bold text-white text-center">1.0x</div>
                                <div class="text-xs text-gray-400 mt-1">Multiplier</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Vault ready for heist...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="vaultEvent" class="text-sm font-bold text-purple-400 hidden animate-pulse">VAULT BREACHED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Crypto Vault Heist - NFT-Powered Bonus Rounds with Digital Art Rewards</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Heists Attempted</div>
                <div id="heistsAttempted" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Success Rate</div>
                <div id="successRate" class="text-xl font-bold text-purple-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-purple-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">NFTs Minted</div>
                <div id="nftsMinted" class="text-xl font-bold text-pink-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Collection Value</div>
                <div id="collectionValueStat" class="text-xl font-bold text-green-400">$0</div>
            </div>
        </div>
    `;

    initializeCryptoVaultHeist();
}

// Initialize the game
function initializeCryptoVaultHeist() {
    document.getElementById('startHeist').addEventListener('click', startCryptoHeist);
    document.getElementById('hackVault').addEventListener('click', hackCryptoVault);
    document.getElementById('mintNFT').addEventListener('click', mintDigitalNFT);
    document.getElementById('tradeNFT').addEventListener('click', tradeNFTCollection);

    // Initialize crypto vault systems
    initializeCryptoVaultSystems();
    generateVaultEffects();
    updateGameStats();
}

// Initialize crypto vault systems
function initializeCryptoVaultSystems() {
    // Reset NFT collection system
    cryptoVaultHeistGame.nftCollection.ownedNFTs = [];
    cryptoVaultHeistGame.nftCollection.totalNFTs = 0;
    cryptoVaultHeistGame.nftCollection.collectionValue = 0;
    cryptoVaultHeistGame.nftCollection.rarityBonus = 1.0;
    cryptoVaultHeistGame.nftCollection.artPoints = cryptoVaultHeistGame.stats.artPointsEarned;
    cryptoVaultHeistGame.nftCollection.masterpieces = 0;
    cryptoVaultHeistGame.nftCollection.mintingPower = Math.min(0.85, 0.65 + (cryptoVaultHeistGame.stats.artPointsEarned * 0.01));
    cryptoVaultHeistGame.nftCollection.tradingSkill = Math.min(0.90, 0.70 + (cryptoVaultHeistGame.stats.artPointsEarned * 0.008));
    cryptoVaultHeistGame.nftCollection.curatorLevel = calculateCuratorLevel();

    // Reset vault system
    cryptoVaultHeistGame.vault.securityLevel = 5;
    cryptoVaultHeistGame.vault.alarmTriggered = false;
    cryptoVaultHeistGame.vault.timeRemaining = 300;
    cryptoVaultHeistGame.vault.hackingProgress = 0;
    cryptoVaultHeistGame.vault.lootMultiplier = 1.0;
    cryptoVaultHeistGame.vault.bonusRounds = 0;
    cryptoVaultHeistGame.vault.nftRewards = [];
    cryptoVaultHeistGame.vault.digitalArt = [];

    // Reset security systems
    cryptoVaultHeistGame.vault.securitySystems = {
        firewall: true,
        encryption: true,
        biometric: true,
        quantum: false,
        ai_guardian: false
    };

    updateCryptoVaultDisplay();
}

// Generate vault effects
function generateVaultEffects() {
    const container = document.getElementById('vaultEffects');
    container.innerHTML = '';

    // Create vault security visualization
    for (let i = 0; i < 6; i++) {
        const securityNode = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        securityNode.setAttribute('cx', `${Math.random() * 100}%`);
        securityNode.setAttribute('cy', `${Math.random() * 100}%`);
        securityNode.setAttribute('r', `${Math.random() * 3 + 2}%`);
        securityNode.setAttribute('fill', '#a855f7');
        securityNode.setAttribute('opacity', '0.7');
        securityNode.classList.add('animate-pulse');
        securityNode.style.animationDelay = `${i * 0.4}s`;
        container.appendChild(securityNode);

        // Add security connections
        if (i > 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 1) * 2];
            connection.setAttribute('x1', prevNode.getAttribute('cx'));
            connection.setAttribute('y1', prevNode.getAttribute('cy'));
            connection.setAttribute('x2', securityNode.getAttribute('cx'));
            connection.setAttribute('y2', securityNode.getAttribute('cy'));
            connection.setAttribute('stroke', '#7c3aed');
            connection.setAttribute('stroke-width', '2');
            connection.setAttribute('opacity', '0.5');
            container.appendChild(connection);
        }
    }
}

// Calculate curator level
function calculateCuratorLevel() {
    const points = cryptoVaultHeistGame.stats.artPointsEarned;
    if (points >= 100) return 'master';
    if (points >= 50) return 'expert';
    if (points >= 25) return 'curator';
    if (points >= 10) return 'collector';
    return 'novice';
}

// Start crypto heist
function startCryptoHeist() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    cryptoVaultHeistGame.isPlaying = true;
    cryptoVaultHeistGame.betAmount = betAmount;
    cryptoVaultHeistGame.totalBet = betAmount;
    cryptoVaultHeistGame.heistType = document.getElementById('heistType').value;
    cryptoVaultHeistGame.vaultLevel = document.getElementById('vaultLevel').value;

    // Start heist infiltration
    startHeistInfiltration();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startHeist').disabled = true;
    document.getElementById('gameStatus').textContent = 'Infiltrating vault...';
}

// Start heist infiltration
function startHeistInfiltration() {
    const heistData = HEIST_TYPES[cryptoVaultHeistGame.heistType];
    const vaultData = VAULT_LEVELS[cryptoVaultHeistGame.vaultLevel];

    // Set vault security level
    cryptoVaultHeistGame.vault.securityLevel = vaultData.securityLevel;

    // Update infiltration information
    document.getElementById('heistStatus').textContent = 'Infiltrating...';
    document.getElementById('nftDisplay').textContent = `Preparing ${heistData.name}...`;

    // Simulate heist infiltration process
    simulateHeistInfiltration(heistData, vaultData);

    // Update vault status
    updateVaultStatus();

    // Update visual effects
    updateCryptoVaultDisplay();
    updateVaultEffects();
}

// Simulate heist infiltration process
function simulateHeistInfiltration(heistData, vaultData) {
    let progress = 0;
    const infiltrationTime = vaultData.securityLevel * 2; // 2 seconds per security level

    const infiltrationInterval = setInterval(() => {
        progress += 100 / (infiltrationTime * 10); // Update every 100ms

        // Update progress bar
        document.getElementById('hackingBar').style.width = `${Math.min(100, progress)}%`;

        // Update time remaining
        const timeRemaining = Math.max(0, cryptoVaultHeistGame.vault.timeRemaining - Math.floor(progress / 10));
        const minutes = Math.floor(timeRemaining / 60);
        const seconds = timeRemaining % 60;
        document.getElementById('timeRemaining').querySelector('.text-2xl').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        // Update status messages
        if (progress < 25) {
            document.getElementById('heistStatus').textContent = 'Scanning...';
        } else if (progress < 50) {
            document.getElementById('heistStatus').textContent = 'Bypassing...';
        } else if (progress < 75) {
            document.getElementById('heistStatus').textContent = 'Infiltrating...';
        } else if (progress < 100) {
            document.getElementById('heistStatus').textContent = 'Accessing...';
        } else {
            document.getElementById('heistStatus').textContent = 'Ready!';
            clearInterval(infiltrationInterval);
            completeHeistInfiltration(heistData, vaultData);
        }

        // Update loot multiplier based on progress
        const lootMultiplier = 1.0 + (progress / 100) * heistData.artBonus;
        cryptoVaultHeistGame.vault.lootMultiplier = lootMultiplier;
        document.getElementById('lootMultiplier').querySelector('.text-2xl').textContent = `${lootMultiplier.toFixed(1)}x`;

    }, 100);
}

// Complete heist infiltration
function completeHeistInfiltration(heistData, vaultData) {
    // Apply NFT collection bonuses
    const nftBonus = calculateNFTCollectionBonus();
    const artBonus = cryptoVaultHeistGame.nftCollection.artPoints * 0.005;

    // Update vault access
    cryptoVaultHeistGame.vault.hackingProgress = 100;

    // Award art points
    const pointsEarned = Math.floor(vaultData.securityLevel * 2);
    cryptoVaultHeistGame.nftCollection.artPoints += pointsEarned;
    cryptoVaultHeistGame.stats.artPointsEarned += pointsEarned;

    // Enable actions
    document.getElementById('heistActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Vault accessed! Begin hacking.';

    // Update displays
    updateVaultStatus();
    updateNFTCollectionDisplay();
}

// Calculate NFT collection bonus
function calculateNFTCollectionBonus() {
    let bonus = 0;

    // Collection completion bonuses
    Object.values(cryptoVaultHeistGame.nftCollection.collections).forEach(collection => {
        const completionRate = collection.owned / collection.total;
        bonus += completionRate * collection.bonus;
    });

    // Rarity bonuses
    cryptoVaultHeistGame.nftCollection.ownedNFTs.forEach(nft => {
        switch (nft.rarity) {
            case 'legendary': bonus += 0.15; break;
            case 'epic': bonus += 0.10; break;
            case 'rare': bonus += 0.08; break;
            case 'uncommon': bonus += 0.05; break;
            case 'common': bonus += 0.03; break;
        }
    });

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Hack crypto vault with NFT influence (3-5% win rate)
function hackCryptoVault() {
    if (!cryptoVaultHeistGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Hacking vault systems...';

    // Calculate NFT influence on success
    const nftInfluence = calculateNFTInfluence();

    // Generate vault hack result
    const hackResult = generateVaultHackResult(nftInfluence);

    // Animate vault breach
    animateVaultBreach(hackResult);

    // Resolve after animation
    setTimeout(() => {
        resolveVaultHeist(hackResult);
    }, 4000);
}

// Calculate NFT influence
function calculateNFTInfluence() {
    const heistData = HEIST_TYPES[cryptoVaultHeistGame.heistType];
    const collectionBonus = calculateNFTCollectionBonus();
    const mintingBonus = cryptoVaultHeistGame.nftCollection.mintingPower * 0.15; // Up to 15% bonus
    const tradingBonus = cryptoVaultHeistGame.nftCollection.tradingSkill * 0.12; // Up to 12% bonus
    const artPointsBonus = cryptoVaultHeistGame.stats.artPointsEarned * 0.002; // Art points bonus

    return Math.min(0.35, heistData.nftWeight + collectionBonus + mintingBonus + tradingBonus + artPointsBonus);
}

// Generate vault hack result with NFT influence (improved for 3-5% win rate)
function generateVaultHackResult(nftInfluence) {
    const heistData = HEIST_TYPES[cryptoVaultHeistGame.heistType];
    const vaultData = VAULT_LEVELS[cryptoVaultHeistGame.vaultLevel];

    // Apply NFT influence to improve odds
    const adjustedOdds = 0.035 + nftInfluence; // Base 3.5% + NFT influence

    // Generate result
    let result = {
        success: false,
        vaultBreached: false,
        nftReward: null,
        bonusRound: false,
        lootMultiplier: cryptoVaultHeistGame.vault.lootMultiplier,
        securityBypass: 0
    };

    // Check for successful hack
    if (Math.random() < adjustedOdds) {
        result.success = true;
        result.vaultBreached = true;

        // Calculate security bypass level
        result.securityBypass = Math.floor(Math.random() * cryptoVaultHeistGame.vault.securityLevel) + 1;

        // Check for NFT reward drop
        if (Math.random() < vaultData.nftDropRate) {
            result.nftReward = selectRandomNFTReward();
        }

        // Check for bonus round trigger
        if (Math.random() < 0.20) { // 20% chance for bonus round
            result.bonusRound = true;
            cryptoVaultHeistGame.vault.bonusRounds++;
        }

        // Apply vault level multiplier
        result.lootMultiplier *= vaultData.artRewardMultiplier;
    }

    return result;
}

// Select random NFT reward
function selectRandomNFTReward() {
    const availableNFTs = cryptoVaultHeistGame.nftRewards.filter(nft => !nft.unlocked);
    if (availableNFTs.length === 0) return null;

    // Weight selection by rarity (rarer = lower chance)
    const rarityWeights = {
        common: 0.50,
        uncommon: 0.30,
        rare: 0.15,
        epic: 0.04,
        legendary: 0.01
    };

    const weightedNFTs = [];
    availableNFTs.forEach(nft => {
        const weight = rarityWeights[nft.rarity] || 0.10;
        for (let i = 0; i < Math.floor(weight * 100); i++) {
            weightedNFTs.push(nft);
        }
    });

    return weightedNFTs[Math.floor(Math.random() * weightedNFTs.length)];
}

// Animate vault breach
function animateVaultBreach(result) {
    const vaultDoor = document.getElementById('vaultDoor');
    const vaultStatus = document.getElementById('vaultStatus');

    if (result.success) {
        // Success animation
        vaultDoor.classList.add('animate-pulse', 'ring-4', 'ring-green-400');
        vaultStatus.textContent = 'BREACHED';
        vaultStatus.className = 'text-green-400 font-bold text-lg drop-shadow-lg';

        // Add breach effects
        setTimeout(() => {
            vaultDoor.style.transform = 'scale(1.1)';
            vaultDoor.style.borderColor = '#10b981';
        }, 1000);

        // Show NFT reward if earned
        if (result.nftReward) {
            setTimeout(() => {
                const nftSlots = document.querySelectorAll('.nft-slot');
                const emptySlot = Array.from(nftSlots).find(slot => slot.textContent === '?');
                if (emptySlot) {
                    emptySlot.textContent = result.nftReward.rarity.charAt(0).toUpperCase();
                    emptySlot.className = `nft-slot w-12 h-12 bg-${getRarityColor(result.nftReward.rarity)}-600 rounded border-2 border-${getRarityColor(result.nftReward.rarity)}-400 flex items-center justify-center text-xs text-white font-bold animate-pulse`;
                }
            }, 2000);
        }
    } else {
        // Failure animation
        vaultDoor.classList.add('animate-pulse', 'ring-4', 'ring-red-400');
        vaultStatus.textContent = 'SECURED';
        vaultStatus.className = 'text-red-400 font-bold text-lg drop-shadow-lg';

        setTimeout(() => {
            vaultDoor.style.borderColor = '#ef4444';
        }, 1000);
    }
}

// Get rarity color
function getRarityColor(rarity) {
    const colors = {
        common: 'gray',
        uncommon: 'green',
        rare: 'blue',
        epic: 'purple',
        legendary: 'yellow'
    };
    return colors[rarity] || 'gray';
}

// Resolve vault heist with NFT bonuses (3-5% win rate)
function resolveVaultHeist(result) {
    const heistData = HEIST_TYPES[cryptoVaultHeistGame.heistType];
    const vaultData = VAULT_LEVELS[cryptoVaultHeistGame.vaultLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    if (result.success) {
        // Base payout for vault breach
        const basePayout = CRYPTO_VAULT_PAYOUTS[`${cryptoVaultHeistGame.vaultLevel.toUpperCase()}_VAULT`] || 150;
        totalWinnings = Math.floor(cryptoVaultHeistGame.betAmount * (basePayout / 100));

        // Apply loot multiplier
        totalWinnings = Math.floor(totalWinnings * result.lootMultiplier);

        resultMessage = `${vaultData.name} breached!`;

        // Award NFT if earned
        if (result.nftReward) {
            result.nftReward.unlocked = true;
            cryptoVaultHeistGame.nftCollection.ownedNFTs.push(result.nftReward);
            cryptoVaultHeistGame.nftCollection.totalNFTs++;
            cryptoVaultHeistGame.nftCollection.collectionValue += result.nftReward.value;
            cryptoVaultHeistGame.stats.nftsMinted++;

            // Update collection stats
            const collection = cryptoVaultHeistGame.nftCollection.collections[result.nftReward.collection];
            if (collection) {
                collection.owned++;
            }

            resultMessage += ` + ${result.nftReward.name} NFT!`;
        }

        // Bonus round rewards
        if (result.bonusRound) {
            const bonusReward = Math.floor(totalWinnings * 0.5);
            totalWinnings += bonusReward;
            resultMessage += ' + Bonus Round!';
            cryptoVaultHeistGame.stats.bonusRoundsTriggered++;
        }
    } else {
        resultMessage = 'Vault security held strong';

        // Small consolation for high NFT collection (improved)
        if (cryptoVaultHeistGame.nftCollection.totalNFTs >= 3 && Math.random() < 0.15) {
            totalWinnings = Math.floor(cryptoVaultHeistGame.betAmount * 0.4); // 40% return
            resultMessage += ' - NFT collection bonus!';
        }
    }

    // Apply NFT collection bonuses (actually work)
    if (cryptoVaultHeistGame.nftCollection.totalNFTs >= 5 && totalWinnings > 0) {
        const collectionBonus = Math.floor(totalWinnings * CRYPTO_VAULT_PAYOUTS.NFT_COLLECTION_BONUS);
        totalWinnings += collectionBonus;
        resultMessage += ' + Collection Bonus!';
    }

    // Apply rarity bonus
    if (cryptoVaultHeistGame.nftCollection.ownedNFTs.some(nft => nft.rarity === 'legendary') && totalWinnings > 0) {
        const rarityBonus = Math.floor(totalWinnings * CRYPTO_VAULT_PAYOUTS.ART_RARITY_BONUS);
        totalWinnings += rarityBonus;
        resultMessage += ' + Legendary Rarity!';
    }

    // Apply minting bonus
    if (cryptoVaultHeistGame.nftCollection.mintingPower >= 0.80 && totalWinnings > 0) {
        const mintingBonus = Math.floor(totalWinnings * CRYPTO_VAULT_PAYOUTS.MINTING_BONUS);
        totalWinnings += mintingBonus;
        resultMessage += ' + Minting Master!';
    }

    // Apply trading bonus
    if (cryptoVaultHeistGame.nftCollection.tradingSkill >= 0.85 && totalWinnings > 0) {
        const tradingBonus = Math.floor(totalWinnings * CRYPTO_VAULT_PAYOUTS.TRADING_BONUS);
        totalWinnings += tradingBonus;
        resultMessage += ' + Trading Expert!';
    }

    // Apply heist type multiplier
    totalWinnings = Math.floor(totalWinnings * heistData.payoutMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(cryptoVaultHeistGame.betAmount * 0.5); // 50% return
        resultMessage = 'Heist experience gained';
    }

    // Award art points
    const pointsEarned = result.success ? 5 : 2;
    cryptoVaultHeistGame.stats.artPointsEarned += pointsEarned;

    // Add winnings to balance
    balance += totalWinnings;
    cryptoVaultHeistGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHeist(result.success, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Heist: ${HEIST_TYPES[cryptoVaultHeistGame.heistType].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show vault event
    document.getElementById('vaultEvent').classList.remove('hidden');
    document.getElementById('vaultEvent').textContent = result.success ? 'VAULT BREACHED!' : 'HEIST FAILED!';
    document.getElementById('vaultEvent').className = `text-sm font-bold ${result.success ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Mint digital NFT
function mintDigitalNFT() {
    if (!cryptoVaultHeistGame.isPlaying) return;

    // Check minting success
    const mintingSuccess = Math.random() < cryptoVaultHeistGame.nftCollection.mintingPower;

    if (mintingSuccess) {
        // Select random NFT to mint
        const nftToMint = selectRandomNFTReward();

        if (nftToMint) {
            nftToMint.unlocked = true;
            cryptoVaultHeistGame.nftCollection.ownedNFTs.push(nftToMint);
            cryptoVaultHeistGame.nftCollection.totalNFTs++;
            cryptoVaultHeistGame.nftCollection.collectionValue += nftToMint.value;
            cryptoVaultHeistGame.stats.nftsMinted++;

            // Update collection stats
            const collection = cryptoVaultHeistGame.nftCollection.collections[nftToMint.collection];
            if (collection) {
                collection.owned++;
            }

            // Visual feedback
            document.getElementById('vaultEvent').classList.remove('hidden');
            document.getElementById('vaultEvent').textContent = `${nftToMint.name} MINTED!`;
            document.getElementById('vaultEvent').className = 'text-sm font-bold text-green-400 animate-pulse';

            // Update NFT display
            const nftSlots = document.querySelectorAll('.nft-slot');
            const emptySlot = Array.from(nftSlots).find(slot => slot.textContent === '?');
            if (emptySlot) {
                emptySlot.textContent = nftToMint.rarity.charAt(0).toUpperCase();
                emptySlot.className = `nft-slot w-12 h-12 bg-${getRarityColor(nftToMint.rarity)}-600 rounded border-2 border-${getRarityColor(nftToMint.rarity)}-400 flex items-center justify-center text-xs text-white font-bold animate-pulse`;
            }
        } else {
            document.getElementById('vaultEvent').classList.remove('hidden');
            document.getElementById('vaultEvent').textContent = 'NO NFTs AVAILABLE TO MINT!';
            document.getElementById('vaultEvent').className = 'text-sm font-bold text-yellow-400 animate-pulse';
        }
    } else {
        document.getElementById('vaultEvent').classList.remove('hidden');
        document.getElementById('vaultEvent').textContent = 'MINTING FAILED!';
        document.getElementById('vaultEvent').className = 'text-sm font-bold text-red-400 animate-pulse';
    }

    // Improve minting power slightly
    cryptoVaultHeistGame.nftCollection.mintingPower = Math.min(0.90, cryptoVaultHeistGame.nftCollection.mintingPower + 0.01);
    cryptoVaultHeistGame.stats.artPointsEarned += 2;

    setTimeout(() => {
        document.getElementById('vaultEvent').classList.add('hidden');
    }, 3000);

    updateNFTCollectionDisplay();
}

// Trade NFT collection
function tradeNFTCollection() {
    if (!cryptoVaultHeistGame.isPlaying || cryptoVaultHeistGame.nftCollection.ownedNFTs.length === 0) {
        alert('No NFTs available to trade!');
        return;
    }

    // Check trading success
    const tradingSuccess = Math.random() < cryptoVaultHeistGame.nftCollection.tradingSkill;

    if (tradingSuccess) {
        // Select random NFT to trade
        const nftToTrade = cryptoVaultHeistGame.nftCollection.ownedNFTs[Math.floor(Math.random() * cryptoVaultHeistGame.nftCollection.ownedNFTs.length)];

        // Calculate trade value (80-120% of NFT value)
        const tradeValue = Math.floor(nftToTrade.value * (0.8 + Math.random() * 0.4));

        // Add trade value to balance
        balance += tradeValue;
        updateBalance();

        // Remove NFT from collection
        const nftIndex = cryptoVaultHeistGame.nftCollection.ownedNFTs.indexOf(nftToTrade);
        cryptoVaultHeistGame.nftCollection.ownedNFTs.splice(nftIndex, 1);
        cryptoVaultHeistGame.nftCollection.totalNFTs--;
        cryptoVaultHeistGame.nftCollection.collectionValue -= nftToTrade.value;
        cryptoVaultHeistGame.stats.nftsTraded++;

        // Update collection stats
        const collection = cryptoVaultHeistGame.nftCollection.collections[nftToTrade.collection];
        if (collection) {
            collection.owned--;
        }

        // Reset NFT unlock status
        nftToTrade.unlocked = false;

        // Visual feedback
        document.getElementById('vaultEvent').classList.remove('hidden');
        document.getElementById('vaultEvent').textContent = `TRADED ${nftToTrade.name} FOR $${tradeValue}!`;
        document.getElementById('vaultEvent').className = 'text-sm font-bold text-blue-400 animate-pulse';

        // Update NFT display
        updateNFTSlotsDisplay();

    } else {
        document.getElementById('vaultEvent').classList.remove('hidden');
        document.getElementById('vaultEvent').textContent = 'TRADING FAILED!';
        document.getElementById('vaultEvent').className = 'text-sm font-bold text-red-400 animate-pulse';
    }

    // Improve trading skill slightly
    cryptoVaultHeistGame.nftCollection.tradingSkill = Math.min(0.95, cryptoVaultHeistGame.nftCollection.tradingSkill + 0.01);
    cryptoVaultHeistGame.stats.artPointsEarned += 3;

    setTimeout(() => {
        document.getElementById('vaultEvent').classList.add('hidden');
    }, 3000);

    updateNFTCollectionDisplay();
}

// Update NFT slots display
function updateNFTSlotsDisplay() {
    const nftSlots = document.querySelectorAll('.nft-slot');

    // Reset all slots
    nftSlots.forEach(slot => {
        slot.textContent = '?';
        slot.className = 'nft-slot w-12 h-12 bg-gray-700 rounded border-2 border-gray-500 flex items-center justify-center text-xs text-gray-400';
    });

    // Fill slots with owned NFTs
    cryptoVaultHeistGame.nftCollection.ownedNFTs.forEach((nft, index) => {
        if (index < nftSlots.length) {
            const slot = nftSlots[index];
            slot.textContent = nft.rarity.charAt(0).toUpperCase();
            slot.className = `nft-slot w-12 h-12 bg-${getRarityColor(nft.rarity)}-600 rounded border-2 border-${getRarityColor(nft.rarity)}-400 flex items-center justify-center text-xs text-white font-bold`;
        }
    });
}

// Update crypto vault display
function updateCryptoVaultDisplay() {
    updateVaultStatus();
    updateNFTCollectionDisplay();
}

// Update vault status
function updateVaultStatus() {
    const vaultData = VAULT_LEVELS[cryptoVaultHeistGame.vaultLevel];

    document.getElementById('securityLevel').querySelector('.text-2xl').textContent =
        `${cryptoVaultHeistGame.vault.securityLevel}/10`;

    const minutes = Math.floor(cryptoVaultHeistGame.vault.timeRemaining / 60);
    const seconds = cryptoVaultHeistGame.vault.timeRemaining % 60;
    document.getElementById('timeRemaining').querySelector('.text-2xl').textContent =
        `${minutes}:${seconds.toString().padStart(2, '0')}`;

    document.getElementById('lootMultiplier').querySelector('.text-2xl').textContent =
        `${cryptoVaultHeistGame.vault.lootMultiplier.toFixed(1)}x`;
}

// Update NFT collection display
function updateNFTCollectionDisplay() {
    document.getElementById('mintingPower').innerHTML =
        `<div class="text-green-400 font-bold">MINTING: ${Math.floor(cryptoVaultHeistGame.nftCollection.mintingPower * 100)}%</div>`;
    document.getElementById('tradingSkill').innerHTML =
        `<div class="text-blue-400 font-bold">TRADING: ${Math.floor(cryptoVaultHeistGame.nftCollection.tradingSkill * 100)}%</div>`;
    document.getElementById('curatorLevel').innerHTML =
        `<div class="text-purple-400 font-bold">LEVEL: ${cryptoVaultHeistGame.nftCollection.curatorLevel.toUpperCase()}</div>`;
    document.getElementById('collectionValue').innerHTML =
        `<div class="text-yellow-400 font-bold">VALUE: $${cryptoVaultHeistGame.nftCollection.collectionValue}</div>`;
}

// Update vault effects
function updateVaultEffects() {
    // Update vault effects based on security level
    const securityLevel = cryptoVaultHeistGame.vault.securityLevel;
    const effects = document.querySelectorAll('#vaultEffects circle');

    effects.forEach((effect, index) => {
        if (securityLevel >= 8) {
            effect.setAttribute('opacity', '0.9');
            effect.setAttribute('fill', '#ef4444'); // Red for high security
        } else if (securityLevel >= 5) {
            effect.setAttribute('opacity', '0.7');
            effect.setAttribute('fill', '#a855f7'); // Purple for medium security
        } else {
            effect.setAttribute('opacity', '0.5');
            effect.setAttribute('fill', '#10b981'); // Green for low security
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${cryptoVaultHeistGame.betAmount}`;
    document.getElementById('artPointsDisplay').textContent = cryptoVaultHeistGame.stats.artPointsEarned;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('heistsAttempted').textContent = cryptoVaultHeistGame.stats.heistsAttempted;

    const successRate = cryptoVaultHeistGame.stats.heistsAttempted > 0 ?
        (cryptoVaultHeistGame.stats.heistsSuccessful / cryptoVaultHeistGame.stats.heistsAttempted * 100) : 0;
    document.getElementById('successRate').textContent = `${successRate.toFixed(1)}%`;

    document.getElementById('totalWagered').textContent = `$${cryptoVaultHeistGame.stats.totalWagered}`;
    document.getElementById('nftsMinted').textContent = cryptoVaultHeistGame.stats.nftsMinted;
    document.getElementById('collectionValueStat').textContent = `$${cryptoVaultHeistGame.stats.collectionValue}`;

    const netResult = cryptoVaultHeistGame.stats.totalWon - cryptoVaultHeistGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-purple-400' : 'text-red-400'}`;
}

// Update stats after heist
function updateGameStatsAfterHeist(won, winnings) {
    cryptoVaultHeistGame.stats.heistsAttempted++;
    cryptoVaultHeistGame.stats.totalWagered += cryptoVaultHeistGame.betAmount;
    cryptoVaultHeistGame.stats.totalWon += winnings;
    cryptoVaultHeistGame.stats.collectionValue = cryptoVaultHeistGame.nftCollection.collectionValue;

    if (won) {
        cryptoVaultHeistGame.stats.heistsSuccessful++;
        cryptoVaultHeistGame.stats.vaultsHacked++;
        cryptoVaultHeistGame.stats.currentStreak++;
        cryptoVaultHeistGame.streakData.currentWinStreak++;
        cryptoVaultHeistGame.streakData.currentLossStreak = 0;

        if (cryptoVaultHeistGame.streakData.currentWinStreak > cryptoVaultHeistGame.streakData.longestWinStreak) {
            cryptoVaultHeistGame.streakData.longestWinStreak = cryptoVaultHeistGame.streakData.currentWinStreak;
        }

        if (winnings > cryptoVaultHeistGame.stats.biggestWin) {
            cryptoVaultHeistGame.stats.biggestWin = winnings;
        }
    } else {
        cryptoVaultHeistGame.stats.currentStreak = 0;
        cryptoVaultHeistGame.streakData.currentWinStreak = 0;
        cryptoVaultHeistGame.streakData.currentLossStreak++;

        if (cryptoVaultHeistGame.streakData.currentLossStreak > cryptoVaultHeistGame.streakData.longestLossStreak) {
            cryptoVaultHeistGame.streakData.longestLossStreak = cryptoVaultHeistGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to NFT mechanics)
    cryptoVaultHeistGame.stats.winRate = (cryptoVaultHeistGame.stats.heistsSuccessful / cryptoVaultHeistGame.stats.heistsAttempted) * 100;

    updateGameStats();
}

// Reset game for next heist
function resetGame() {
    cryptoVaultHeistGame.isPlaying = false;
    cryptoVaultHeistGame.betAmount = 0;
    cryptoVaultHeistGame.totalBet = 0;
    cryptoVaultHeistGame.gameResult = '';
    cryptoVaultHeistGame.totalWin = 0;

    // Reset vault system
    cryptoVaultHeistGame.vault.alarmTriggered = false;
    cryptoVaultHeistGame.vault.timeRemaining = 300;
    cryptoVaultHeistGame.vault.hackingProgress = 0;
    cryptoVaultHeistGame.vault.lootMultiplier = 1.0;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('vaultEvent').classList.add('hidden');
    document.getElementById('heistActions').classList.add('hidden');

    // Reset vault door
    const vaultDoor = document.getElementById('vaultDoor');
    const vaultStatus = document.getElementById('vaultStatus');
    vaultDoor.className = 'w-32 h-32 rounded-full border-4 border-purple-400 bg-gradient-to-br from-purple-800 via-purple-900 to-black relative mx-auto overflow-hidden';
    vaultDoor.style.transform = 'scale(1)';
    vaultDoor.style.borderColor = '#a855f7';
    vaultStatus.textContent = 'LOCKED';
    vaultStatus.className = 'text-white font-bold text-lg drop-shadow-lg';

    // Reset progress displays
    document.getElementById('hackingBar').style.width = '0%';
    document.getElementById('heistStatus').textContent = 'Ready';

    // Reset NFT display
    document.getElementById('nftDisplay').textContent = 'Digital art collection awaits...';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startHeist').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Vault ready for heist...';
    document.getElementById('gameMessage').textContent = 'Welcome to Crypto Vault Heist - NFT-Powered Bonus Rounds with Digital Art Rewards';

    // Reinitialize systems for next heist
    initializeCryptoVaultSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCryptoVaultHeistGame();
});