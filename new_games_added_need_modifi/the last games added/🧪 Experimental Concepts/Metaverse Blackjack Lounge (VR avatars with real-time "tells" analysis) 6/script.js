// Metaverse Blackjack Lounge - VR Avatars with Real-time "Tells" Analysis
// Experimental Concept Implementation with VR and Avatar Theme
// Designed to maintain 3-5% player win rate with tells analysis mechanics

// Game state
let balance = 1000;

// Game state object with VR avatar system
let metaverseBlackjackGame = {
    isPlaying: false,
    vrEnvironment: 'neon_city', // neon_city, space_station, underwater, forest, cyberpunk
    avatarType: 'human', // human, android, alien, cyborg, hologram
    betAmount: 0,
    totalBet: 0,

    // VR avatar system
    avatar: {
        name: 'Player',
        type: 'human',
        confidence: 0.75, // 75% confidence level
        stress: 0.30, // 30% stress level
        focus: 0.80, // 80% focus level
        experience: 0.65, // 65% experience level
        intuition: 0.70, // 70% intuition level
        tells: [],
        behaviorPattern: 'balanced', // conservative, balanced, aggressive, unpredictable, adaptive
        vrLevel: 'novice', // novice, intermediate, advanced, expert, master
        metaversePoints: 0,
        avatarHistory: [],
        emotionalState: 'neutral' // calm, excited, nervous, confident, frustrated
    },

    // Blackjack game state
    blackjack: {
        playerHand: [],
        dealerHand: [],
        playerValue: 0,
        dealerValue: 0,
        gamePhase: 'betting', // betting, dealing, playing, dealer, finished
        canHit: true,
        canStand: true,
        canDouble: true,
        canSplit: false,
        isBlackjack: false,
        isBust: false,
        dealerBust: false,
        handResult: '',
        tellsDetected: [],
        vrBonus: 1.0,
        avatarAdvantage: 0.0
    },

    // VR environments
    vrEnvironments: {
        neon_city: {
            name: 'Neon City Casino',
            atmosphere: 0.85, // 85% atmosphere bonus
            immersion: 0.80, // 80% immersion
            tellsAccuracy: 0.75, // 75% tells accuracy
            focusBonus: 0.25 // 25% focus bonus
        },
        space_station: {
            name: 'Orbital Casino Station',
            atmosphere: 0.90, // 90% atmosphere bonus
            immersion: 0.85, // 85% immersion
            tellsAccuracy: 0.80, // 80% tells accuracy
            focusBonus: 0.30 // 30% focus bonus
        },
        underwater: {
            name: 'Aquatic Gaming Dome',
            atmosphere: 0.88, // 88% atmosphere bonus
            immersion: 0.90, // 90% immersion
            tellsAccuracy: 0.85, // 85% tells accuracy
            focusBonus: 0.35 // 35% focus bonus
        },
        forest: {
            name: 'Enchanted Forest Casino',
            atmosphere: 0.82, // 82% atmosphere bonus
            immersion: 0.75, // 75% immersion
            tellsAccuracy: 0.70, // 70% tells accuracy
            focusBonus: 0.20 // 20% focus bonus
        },
        cyberpunk: {
            name: 'Cyberpunk Underground',
            atmosphere: 0.95, // 95% atmosphere bonus
            immersion: 0.95, // 95% immersion
            tellsAccuracy: 0.90, // 90% tells accuracy
            focusBonus: 0.40 // 40% focus bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        blackjacksHit: 0,
        tellsAnalyzed: 0,
        avatarInteractions: 0,
        vrSessionTime: 0,
        metaversePoints: 0,
        bestTellsAccuracy: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// VR environments with balanced tells requirements (3-5% win rate)
const VR_ENVIRONMENTS = {
    neon_city: {
        name: 'Neon City',
        tellsWeight: 0.25, // 25% tells influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        immersionBonus: 0.20, // 20% immersion bonus
        atmosphereBonus: 0.15 // 15% atmosphere bonus
    },
    space_station: {
        name: 'Space Station',
        tellsWeight: 0.30, // 30% tells influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        immersionBonus: 0.25, // 25% immersion bonus
        atmosphereBonus: 0.20 // 20% atmosphere bonus
    },
    underwater: {
        name: 'Underwater',
        tellsWeight: 0.35, // 35% tells influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        immersionBonus: 0.30, // 30% immersion bonus
        atmosphereBonus: 0.25 // 25% atmosphere bonus
    },
    forest: {
        name: 'Forest',
        tellsWeight: 0.28, // 28% tells influence
        luckFactor: 0.72, // 72% luck factor
        payoutMultiplier: 0.98, // Good payouts
        immersionBonus: 0.22, // 22% immersion bonus
        atmosphereBonus: 0.18 // 18% atmosphere bonus
    },
    cyberpunk: {
        name: 'Cyberpunk',
        tellsWeight: 0.40, // 40% tells influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        immersionBonus: 0.35, // 35% immersion bonus
        atmosphereBonus: 0.30 // 30% atmosphere bonus
    }
};

const AVATAR_TYPES = {
    human: {
        name: 'Human',
        tellsAccuracy: 0.75, // 75% tells accuracy
        emotionalRange: 1.00 // Full emotional range
    },
    android: {
        name: 'Android',
        tellsAccuracy: 0.85, // 85% tells accuracy
        emotionalRange: 0.60 // Limited emotional range
    },
    alien: {
        name: 'Alien',
        tellsAccuracy: 0.70, // 70% tells accuracy
        emotionalRange: 1.20 // Enhanced emotional range
    },
    cyborg: {
        name: 'Cyborg',
        tellsAccuracy: 0.90, // 90% tells accuracy
        emotionalRange: 0.80 // Moderate emotional range
    },
    hologram: {
        name: 'Hologram',
        tellsAccuracy: 0.95, // 95% tells accuracy
        emotionalRange: 0.40 // Minimal emotional range
    }
};

// Improved payout table with VR theme (3-5% win rate)
const METAVERSE_BLACKJACK_PAYOUTS = {
    // Perfect VR achievements (moderately reduced)
    VR_MASTER: 2000, // Reduced from 4000:1 but still excellent
    AVATAR_GENIUS: 1500, // Reduced from 3000:1
    TELLS_EXPERT: 1000, // Reduced from 2000:1
    METAVERSE_LEGEND: 800, // Reduced from 1600:1

    // Blackjack payouts with VR bonuses
    BLACKJACK: 150, // 3:2 payout (1.5:1)
    WIN: 100, // 1:1 payout
    PUSH: 0, // No win/loss

    // VR bonuses (actually apply more often)
    TELLS_BONUS: 0.80, // 80% of displayed bonus (increased)
    AVATAR_BONUS: 0.70, // 70% of displayed bonus (increased)
    IMMERSION_BONUS: 0.60, // 60% of displayed bonus (increased)
    VR_EXPERIENCE_BONUS: 0.50 // 50% of displayed bonus (increased)
};

// Standard deck of cards
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_RANKS = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadMetaverseBlackjackGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">VR CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VR ENVIRONMENT</label>
                        <select id="vrEnvironment" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="neon_city">Neon City Casino</option>
                            <option value="space_station">Orbital Casino Station</option>
                            <option value="underwater">Aquatic Gaming Dome</option>
                            <option value="forest">Enchanted Forest Casino</option>
                            <option value="cyberpunk">Cyberpunk Underground</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">AVATAR TYPE</label>
                        <select id="avatarType" class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="human">Human</option>
                            <option value="android">Android</option>
                            <option value="alien">Alien</option>
                            <option value="cyborg">Cyborg</option>
                            <option value="hologram">Hologram</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-cyan-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="enterVR" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ENTER VR LOUNGE
                    </button>

                    <div id="blackjackActions" class="space-y-2 hidden">
                        <button id="dealCards" class="w-full py-2 rounded-lg font-bold bg-cyan-600 hover:bg-cyan-700 text-white">
                            DEAL CARDS
                        </button>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="hitCard" class="py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                HIT
                            </button>
                            <button id="standCards" class="py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                                STAND
                            </button>
                        </div>
                        <button id="doubleDown" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            DOUBLE DOWN
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Metaverse Points</div>
                        <div id="metaversePointsDisplay" class="text-lg font-bold text-cyan-400">0</div>
                    </div>
                </div>

                <!-- Avatar Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">AVATAR STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="confidence" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">CONFIDENCE: 75%</div>
                        </div>
                        <div id="stress" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">STRESS: 30%</div>
                        </div>
                        <div id="focus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">FOCUS: 80%</div>
                        </div>
                        <div id="emotionalState" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">STATE: NEUTRAL</div>
                        </div>
                    </div>
                </div>

                <!-- Tells Analysis Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-cyan-400">TELLS ANALYSIS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Detection Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Accuracy:</span>
                            <span class="text-green-400">75%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Analyzed:</span>
                            <span class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">VR Level:</span>
                            <span class="text-purple-400">Novice</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">VR Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tells:</span>
                            <span class="text-cyan-400">80%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Avatar:</span>
                            <span class="text-cyan-400">70%</span>
                        </div>
                        <div class="text-xs text-cyan-400 mt-2">*Tells improve blackjack odds</div>
                        <div class="text-xs text-cyan-400">*VR immersion provides bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main VR Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <!-- VR Blackjack Arena -->
                    <div id="vrBlackjackArena" class="relative bg-gradient-to-br from-black via-cyan-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- VR Background -->
                        <div id="vrBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="vrGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#0891b2;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#0e7490;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="vrPattern" width="80" height="80" patternUnits="userSpaceOnUse">
                                        <rect x="10" y="10" width="60" height="60" fill="none" stroke="#06b6d4" stroke-width="2" opacity="0.3"/>
                                        <circle cx="40" cy="40" r="25" fill="none" stroke="#0891b2" stroke-width="2" opacity="0.4"/>
                                        <polygon points="40,15 55,35 40,55 25,35" fill="#0e7490" opacity="0.3"/>
                                        <circle cx="20" cy="20" r="5" fill="#06b6d4" opacity="0.6"/>
                                        <circle cx="60" cy="60" r="5" fill="#06b6d4" opacity="0.6"/>
                                        <line x1="15" y1="65" x2="65" y2="15" stroke="#0891b2" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#vrPattern)" />
                                <circle id="vrCore" cx="50%" cy="50%" r="25%" fill="url(#vrGradient)" class="animate-pulse" />
                                <g id="vrEffects">
                                    <!-- VR effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Blackjack Table -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">VR BLACKJACK TABLE</div>
                                <div id="blackjackTable" class="bg-green-800/50 rounded-lg p-4 w-80 border-2 border-green-600">
                                    <!-- Dealer Hand -->
                                    <div class="mb-4">
                                        <div class="text-xs text-white mb-2">DEALER</div>
                                        <div id="dealerHand" class="flex justify-center space-x-2 mb-2">
                                            <!-- Dealer cards will appear here -->
                                        </div>
                                        <div id="dealerValue" class="text-sm text-white text-center">Value: 0</div>
                                    </div>

                                    <!-- Player Hand -->
                                    <div>
                                        <div class="text-xs text-cyan-400 mb-2">PLAYER</div>
                                        <div id="playerHand" class="flex justify-center space-x-2 mb-2">
                                            <!-- Player cards will appear here -->
                                        </div>
                                        <div id="playerValue" class="text-sm text-cyan-400 text-center">Value: 0</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">VR-enhanced blackjack experience</div>
                            </div>
                        </div>

                        <!-- Avatar Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">VR AVATAR</div>
                                <div id="avatarDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="avatarStatus" class="text-lg text-white mb-3">Ready to enter VR lounge...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">TELLS</div>
                                            <div id="tellsDetected" class="text-xl font-bold text-white">0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">ACCURACY</div>
                                            <div id="tellsAccuracy" class="text-xl font-bold text-green-400">75%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-cyan-400 mb-1">IMMERSION</div>
                                            <div id="vrImmersion" class="text-xl font-bold text-purple-400">80%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Real-time tells analysis</div>
                            </div>
                        </div>

                        <!-- VR Session Progress -->
                        <div id="vrSessionProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-cyan-400 mb-2">VR SESSION</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="sessionBar" class="bg-gradient-to-r from-cyan-400 to-purple-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Connecting</span>
                                    <span id="sessionStatus">Ready</span>
                                    <span>Immersed</span>
                                </div>
                            </div>
                        </div>

                        <!-- Avatar Confidence -->
                        <div id="avatarConfidence" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-cyan-400 mb-1">CONFIDENCE</div>
                                <div class="text-2xl font-bold text-white text-center">75%</div>
                                <div class="text-xs text-gray-400 mt-1">Level</div>
                            </div>
                        </div>

                        <!-- Tells Counter -->
                        <div id="tellsCounter" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">TELLS</div>
                                <div class="text-2xl font-bold text-white text-center">0</div>
                                <div class="text-xs text-gray-400 mt-1">Detected</div>
                            </div>
                        </div>

                        <!-- VR Bonus -->
                        <div id="vrBonus" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">VR BONUS</div>
                                <div class="text-2xl font-bold text-white text-center">1.0x</div>
                                <div class="text-xs text-gray-400 mt-1">Multiplier</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">VR lounge ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="vrEvent" class="text-sm font-bold text-cyan-400 hidden animate-pulse">VR CONNECTED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Metaverse Blackjack Lounge - VR Avatars with Real-time "Tells" Analysis</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-cyan-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-cyan-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Blackjacks</div>
                <div id="blackjacksHit" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-cyan-500/30 text-center">
                <div class="text-sm text-gray-400">Tells Analyzed</div>
                <div id="tellsAnalyzedStat" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeMetaverseBlackjack();
}

// Initialize the game
function initializeMetaverseBlackjack() {
    document.getElementById('enterVR').addEventListener('click', enterVRLounge);
    document.getElementById('dealCards').addEventListener('click', dealBlackjackCards);
    document.getElementById('hitCard').addEventListener('click', hitCard);
    document.getElementById('standCards').addEventListener('click', standCards);
    document.getElementById('doubleDown').addEventListener('click', doubleDown);

    // Initialize VR systems
    initializeVRSystems();
    generateVREffects();
    updateGameStats();
}

// Initialize VR systems
function initializeVRSystems() {
    // Reset avatar system
    metaverseBlackjackGame.avatar.name = 'Player';
    metaverseBlackjackGame.avatar.type = 'human';
    metaverseBlackjackGame.avatar.confidence = Math.min(0.95, 0.75 + (metaverseBlackjackGame.stats.metaversePoints * 0.01));
    metaverseBlackjackGame.avatar.stress = Math.max(0.10, 0.30 - (metaverseBlackjackGame.stats.metaversePoints * 0.005));
    metaverseBlackjackGame.avatar.focus = Math.min(0.95, 0.80 + (metaverseBlackjackGame.stats.metaversePoints * 0.008));
    metaverseBlackjackGame.avatar.experience = Math.min(0.90, 0.65 + (metaverseBlackjackGame.stats.metaversePoints * 0.01));
    metaverseBlackjackGame.avatar.intuition = Math.min(0.90, 0.70 + (metaverseBlackjackGame.stats.metaversePoints * 0.008));
    metaverseBlackjackGame.avatar.tells = [];
    metaverseBlackjackGame.avatar.behaviorPattern = 'balanced';
    metaverseBlackjackGame.avatar.vrLevel = calculateVRLevel();
    metaverseBlackjackGame.avatar.metaversePoints = metaverseBlackjackGame.stats.metaversePoints;
    metaverseBlackjackGame.avatar.avatarHistory = [];
    metaverseBlackjackGame.avatar.emotionalState = 'neutral';

    // Reset blackjack system
    metaverseBlackjackGame.blackjack.playerHand = [];
    metaverseBlackjackGame.blackjack.dealerHand = [];
    metaverseBlackjackGame.blackjack.playerValue = 0;
    metaverseBlackjackGame.blackjack.dealerValue = 0;
    metaverseBlackjackGame.blackjack.gamePhase = 'betting';
    metaverseBlackjackGame.blackjack.canHit = true;
    metaverseBlackjackGame.blackjack.canStand = true;
    metaverseBlackjackGame.blackjack.canDouble = true;
    metaverseBlackjackGame.blackjack.canSplit = false;
    metaverseBlackjackGame.blackjack.isBlackjack = false;
    metaverseBlackjackGame.blackjack.isBust = false;
    metaverseBlackjackGame.blackjack.dealerBust = false;
    metaverseBlackjackGame.blackjack.handResult = '';
    metaverseBlackjackGame.blackjack.tellsDetected = [];
    metaverseBlackjackGame.blackjack.vrBonus = 1.0;
    metaverseBlackjackGame.blackjack.avatarAdvantage = 0.0;

    updateVRDisplay();
}

// Generate VR effects
function generateVREffects() {
    const container = document.getElementById('vrEffects');
    container.innerHTML = '';

    // Create VR environment visualization
    for (let i = 0; i < 12; i++) {
        const vrNode = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        vrNode.setAttribute('cx', `${Math.random() * 100}%`);
        vrNode.setAttribute('cy', `${Math.random() * 100}%`);
        vrNode.setAttribute('r', `${Math.random() * 3 + 2}%`);
        vrNode.setAttribute('fill', '#06b6d4');
        vrNode.setAttribute('opacity', '0.6');
        vrNode.classList.add('animate-pulse');
        vrNode.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(vrNode);

        // Add VR connections
        if (i > 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 1) * 2];
            connection.setAttribute('x1', prevNode.getAttribute('cx'));
            connection.setAttribute('y1', prevNode.getAttribute('cy'));
            connection.setAttribute('x2', vrNode.getAttribute('cx'));
            connection.setAttribute('y2', vrNode.getAttribute('cy'));
            connection.setAttribute('stroke', '#0891b2');
            connection.setAttribute('stroke-width', '2');
            connection.setAttribute('opacity', '0.4');
            container.appendChild(connection);
        }
    }
}

// Calculate VR level
function calculateVRLevel() {
    const points = metaverseBlackjackGame.stats.metaversePoints;
    if (points >= 100) return 'master';
    if (points >= 50) return 'expert';
    if (points >= 25) return 'advanced';
    if (points >= 10) return 'intermediate';
    return 'novice';
}

// Enter VR lounge
function enterVRLounge() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    metaverseBlackjackGame.isPlaying = true;
    metaverseBlackjackGame.betAmount = betAmount;
    metaverseBlackjackGame.totalBet = betAmount;
    metaverseBlackjackGame.vrEnvironment = document.getElementById('vrEnvironment').value;
    metaverseBlackjackGame.avatarType = document.getElementById('avatarType').value;

    // Start VR session
    startVRSession();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('enterVR').disabled = true;
    document.getElementById('gameStatus').textContent = 'Entering VR lounge...';
}

// Start VR session
function startVRSession() {
    const vrEnvironmentData = metaverseBlackjackGame.vrEnvironments[metaverseBlackjackGame.vrEnvironment];
    const avatarTypeData = AVATAR_TYPES[metaverseBlackjackGame.avatarType];

    // Update VR information
    metaverseBlackjackGame.avatar.type = metaverseBlackjackGame.avatarType;
    document.getElementById('avatarStatus').textContent = `Connecting to ${vrEnvironmentData.name}...`;
    document.getElementById('sessionStatus').textContent = 'Connecting...';

    // Simulate VR session startup
    simulateVRSession(vrEnvironmentData, avatarTypeData);

    // Update avatar status
    updateAvatarStatus();

    // Update visual effects
    updateVRDisplay();
    updateVREffects();
}

// Simulate VR session startup
function simulateVRSession(vrEnvironmentData, avatarTypeData) {
    let progress = 0;
    const sessionTime = 30; // 3 seconds session startup

    const sessionInterval = setInterval(() => {
        progress += 100 / sessionTime; // Update every 100ms

        // Update progress bar
        document.getElementById('sessionBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 25) {
            document.getElementById('sessionStatus').textContent = 'Connecting...';
        } else if (progress < 50) {
            document.getElementById('sessionStatus').textContent = 'Loading...';
        } else if (progress < 75) {
            document.getElementById('sessionStatus').textContent = 'Calibrating...';
        } else if (progress < 100) {
            document.getElementById('sessionStatus').textContent = 'Immersing...';
        } else {
            document.getElementById('sessionStatus').textContent = 'Connected!';
            clearInterval(sessionInterval);
            completeVRSession(vrEnvironmentData, avatarTypeData);
        }

        // Update VR bonus based on progress
        const vrBonus = 1.0 + (progress / 100) * vrEnvironmentData.focusBonus;
        metaverseBlackjackGame.blackjack.vrBonus = vrBonus;
        document.getElementById('vrBonus').querySelector('.text-2xl').textContent = `${vrBonus.toFixed(1)}x`;

    }, 100);
}

// Complete VR session
function completeVRSession(vrEnvironmentData, avatarTypeData) {
    // Apply avatar bonuses
    const avatarBonus = calculateAvatarBonus();
    const tellsBonus = avatarTypeData.tellsAccuracy * 0.15; // Up to 15% bonus

    // Update avatar advantage
    metaverseBlackjackGame.blackjack.avatarAdvantage = avatarBonus + tellsBonus;

    // Award metaverse points
    const pointsEarned = Math.floor(vrEnvironmentData.atmosphere * 10);
    metaverseBlackjackGame.stats.metaversePoints += pointsEarned;

    // Enable blackjack actions
    document.getElementById('blackjackActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'VR connected! Deal cards to start.';

    // Update displays
    updateAvatarStatus();
    updateTellsDisplay();
}

// Calculate avatar bonus
function calculateAvatarBonus() {
    let bonus = 0;

    // Confidence bonus
    bonus += metaverseBlackjackGame.avatar.confidence * 0.10; // Up to 10% bonus

    // Focus bonus (reduced by stress)
    const effectiveFocus = metaverseBlackjackGame.avatar.focus * (1 - metaverseBlackjackGame.avatar.stress);
    bonus += effectiveFocus * 0.08; // Up to 8% bonus

    // Experience bonus
    bonus += metaverseBlackjackGame.avatar.experience * 0.12; // Up to 12% bonus

    // Intuition bonus
    bonus += metaverseBlackjackGame.avatar.intuition * 0.10; // Up to 10% bonus

    // VR level bonus
    const levelBonuses = {
        novice: 0.05,
        intermediate: 0.08,
        advanced: 0.12,
        expert: 0.15,
        master: 0.20
    };
    bonus += levelBonuses[metaverseBlackjackGame.avatar.vrLevel] || 0.05;

    return Math.min(0.40, bonus); // Cap at 40% bonus
}

// Deal blackjack cards with tells analysis (3-5% win rate)
function dealBlackjackCards() {
    if (!metaverseBlackjackGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Dealing cards...';

    // Reset hands
    metaverseBlackjackGame.blackjack.playerHand = [];
    metaverseBlackjackGame.blackjack.dealerHand = [];
    metaverseBlackjackGame.blackjack.tellsDetected = [];

    // Deal initial cards
    dealCard('player');
    dealCard('dealer');
    dealCard('player');
    dealCard('dealer', true); // Dealer's second card is face down

    // Calculate hand values
    updateHandValues();

    // Check for blackjack
    checkForBlackjack();

    // Analyze tells
    analyzeTells();

    // Update game phase
    metaverseBlackjackGame.blackjack.gamePhase = 'playing';

    // Update displays
    updateBlackjackDisplay();
    updateTellsDisplay();

    // Enable/disable actions
    updateActionButtons();
}

// Deal a single card
function dealCard(recipient, faceDown = false) {
    const card = generateRandomCard();

    if (recipient === 'player') {
        metaverseBlackjackGame.blackjack.playerHand.push(card);
    } else {
        card.faceDown = faceDown;
        metaverseBlackjackGame.blackjack.dealerHand.push(card);
    }

    return card;
}

// Generate random card
function generateRandomCard() {
    const suit = CARD_SUITS[Math.floor(Math.random() * CARD_SUITS.length)];
    const rank = CARD_RANKS[Math.floor(Math.random() * CARD_RANKS.length)];

    let value = 0;
    if (rank === 'A') {
        value = 11; // Ace starts as 11, adjusted later if needed
    } else if (['J', 'Q', 'K'].includes(rank)) {
        value = 10;
    } else {
        value = parseInt(rank);
    }

    return {
        suit: suit,
        rank: rank,
        value: value,
        faceDown: false
    };
}

// Calculate hand values
function calculateHandValue(hand) {
    let value = 0;
    let aces = 0;

    hand.forEach(card => {
        if (!card.faceDown) {
            if (card.rank === 'A') {
                aces++;
                value += 11;
            } else {
                value += card.value;
            }
        }
    });

    // Adjust for aces
    while (value > 21 && aces > 0) {
        value -= 10;
        aces--;
    }

    return value;
}

// Update hand values
function updateHandValues() {
    metaverseBlackjackGame.blackjack.playerValue = calculateHandValue(metaverseBlackjackGame.blackjack.playerHand);
    metaverseBlackjackGame.blackjack.dealerValue = calculateHandValue(metaverseBlackjackGame.blackjack.dealerHand);
}

// Check for blackjack
function checkForBlackjack() {
    const playerValue = metaverseBlackjackGame.blackjack.playerValue;
    const dealerValue = metaverseBlackjackGame.blackjack.dealerValue;

    if (playerValue === 21 && metaverseBlackjackGame.blackjack.playerHand.length === 2) {
        metaverseBlackjackGame.blackjack.isBlackjack = true;
        metaverseBlackjackGame.blackjack.gamePhase = 'finished';

        // Reveal dealer's face-down card
        metaverseBlackjackGame.blackjack.dealerHand.forEach(card => card.faceDown = false);
        updateHandValues();

        // Check if dealer also has blackjack
        if (metaverseBlackjackGame.blackjack.dealerValue === 21) {
            metaverseBlackjackGame.blackjack.handResult = 'push';
        } else {
            metaverseBlackjackGame.blackjack.handResult = 'blackjack';
        }

        setTimeout(() => resolveBlackjackHand(), 2000);
    }
}

// Analyze tells with VR enhancement
function analyzeTells() {
    const vrEnvironmentData = metaverseBlackjackGame.vrEnvironments[metaverseBlackjackGame.vrEnvironment];
    const avatarTypeData = AVATAR_TYPES[metaverseBlackjackGame.avatarType];

    // Generate tells based on hand strength and avatar capabilities
    const playerValue = metaverseBlackjackGame.blackjack.playerValue;
    const tellsAccuracy = avatarTypeData.tellsAccuracy * vrEnvironmentData.tellsAccuracy;

    const possibleTells = [
        'Micro-expression detected',
        'Breathing pattern change',
        'Eye movement analysis',
        'Posture shift detected',
        'Hand gesture pattern',
        'Voice stress analysis',
        'Facial muscle tension',
        'Pupil dilation change'
    ];

    // Detect tells based on hand strength and accuracy
    const tellsToDetect = Math.floor(Math.random() * 3) + 1; // 1-3 tells

    for (let i = 0; i < tellsToDetect; i++) {
        if (Math.random() < tellsAccuracy) {
            const tell = possibleTells[Math.floor(Math.random() * possibleTells.length)];
            if (!metaverseBlackjackGame.blackjack.tellsDetected.includes(tell)) {
                metaverseBlackjackGame.blackjack.tellsDetected.push(tell);
                metaverseBlackjackGame.avatar.tells.push({
                    tell: tell,
                    handValue: playerValue,
                    timestamp: Date.now(),
                    confidence: tellsAccuracy
                });
            }
        }
    }

    // Update tells count
    metaverseBlackjackGame.stats.tellsAnalyzed += metaverseBlackjackGame.blackjack.tellsDetected.length;

    // Update emotional state based on hand
    updateEmotionalState(playerValue);
}

// Update emotional state
function updateEmotionalState(handValue) {
    if (handValue === 21) {
        metaverseBlackjackGame.avatar.emotionalState = 'confident';
        metaverseBlackjackGame.avatar.confidence = Math.min(0.95, metaverseBlackjackGame.avatar.confidence + 0.1);
        metaverseBlackjackGame.avatar.stress = Math.max(0.10, metaverseBlackjackGame.avatar.stress - 0.1);
    } else if (handValue >= 17) {
        metaverseBlackjackGame.avatar.emotionalState = 'calm';
        metaverseBlackjackGame.avatar.confidence = Math.min(0.95, metaverseBlackjackGame.avatar.confidence + 0.05);
    } else if (handValue <= 12) {
        metaverseBlackjackGame.avatar.emotionalState = 'nervous';
        metaverseBlackjackGame.avatar.stress = Math.min(0.80, metaverseBlackjackGame.avatar.stress + 0.1);
        metaverseBlackjackGame.avatar.confidence = Math.max(0.20, metaverseBlackjackGame.avatar.confidence - 0.05);
    } else {
        metaverseBlackjackGame.avatar.emotionalState = 'neutral';
    }
}

// Hit card
function hitCard() {
    if (!metaverseBlackjackGame.isPlaying || metaverseBlackjackGame.blackjack.gamePhase !== 'playing') return;

    // Deal card to player
    dealCard('player');
    updateHandValues();

    // Analyze new tells
    analyzeTells();

    // Check for bust
    if (metaverseBlackjackGame.blackjack.playerValue > 21) {
        metaverseBlackjackGame.blackjack.isBust = true;
        metaverseBlackjackGame.blackjack.gamePhase = 'finished';
        metaverseBlackjackGame.blackjack.handResult = 'bust';

        setTimeout(() => resolveBlackjackHand(), 1000);
    }

    // Update displays
    updateBlackjackDisplay();
    updateTellsDisplay();
    updateActionButtons();
}

// Stand cards
function standCards() {
    if (!metaverseBlackjackGame.isPlaying || metaverseBlackjackGame.blackjack.gamePhase !== 'playing') return;

    metaverseBlackjackGame.blackjack.gamePhase = 'dealer';

    // Reveal dealer's face-down card
    metaverseBlackjackGame.blackjack.dealerHand.forEach(card => card.faceDown = false);
    updateHandValues();

    // Dealer draws cards
    dealerPlay();
}

// Double down
function doubleDown() {
    if (!metaverseBlackjackGame.isPlaying || metaverseBlackjackGame.blackjack.gamePhase !== 'playing') return;

    // Check if player has enough balance
    if (metaverseBlackjackGame.betAmount > balance) {
        alert('Insufficient balance to double down!');
        return;
    }

    // Double the bet
    balance -= metaverseBlackjackGame.betAmount;
    metaverseBlackjackGame.betAmount *= 2;
    metaverseBlackjackGame.totalBet = metaverseBlackjackGame.betAmount;
    updateBalance();
    updateBetDisplay();

    // Deal one card and stand
    dealCard('player');
    updateHandValues();
    analyzeTells();

    // Check for bust
    if (metaverseBlackjackGame.blackjack.playerValue > 21) {
        metaverseBlackjackGame.blackjack.isBust = true;
        metaverseBlackjackGame.blackjack.gamePhase = 'finished';
        metaverseBlackjackGame.blackjack.handResult = 'bust';

        setTimeout(() => resolveBlackjackHand(), 1000);
    } else {
        // Automatically stand after double down
        standCards();
    }

    updateBlackjackDisplay();
    updateActionButtons();
}

// Dealer play
function dealerPlay() {
    const dealerPlayInterval = setInterval(() => {
        updateHandValues();

        if (metaverseBlackjackGame.blackjack.dealerValue < 17) {
            // Dealer must hit on 16 or less
            dealCard('dealer');
            updateBlackjackDisplay();
        } else {
            // Dealer stands on 17 or more
            clearInterval(dealerPlayInterval);

            // Check for dealer bust
            if (metaverseBlackjackGame.blackjack.dealerValue > 21) {
                metaverseBlackjackGame.blackjack.dealerBust = true;
                metaverseBlackjackGame.blackjack.handResult = 'dealer_bust';
            } else {
                // Compare hands
                compareHands();
            }

            metaverseBlackjackGame.blackjack.gamePhase = 'finished';
            setTimeout(() => resolveBlackjackHand(), 1000);
        }
    }, 1000);
}

// Compare hands
function compareHands() {
    const playerValue = metaverseBlackjackGame.blackjack.playerValue;
    const dealerValue = metaverseBlackjackGame.blackjack.dealerValue;

    if (playerValue > dealerValue) {
        metaverseBlackjackGame.blackjack.handResult = 'win';
    } else if (playerValue < dealerValue) {
        metaverseBlackjackGame.blackjack.handResult = 'lose';
    } else {
        metaverseBlackjackGame.blackjack.handResult = 'push';
    }
}

// Resolve blackjack hand with VR bonuses (3-5% win rate)
function resolveBlackjackHand() {
    const vrEnvironmentData = VR_ENVIRONMENTS[metaverseBlackjackGame.vrEnvironment];
    const avatarTypeData = AVATAR_TYPES[metaverseBlackjackGame.avatarType];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate base payout
    switch (metaverseBlackjackGame.blackjack.handResult) {
        case 'blackjack':
            totalWinnings = Math.floor(metaverseBlackjackGame.betAmount * (METAVERSE_BLACKJACK_PAYOUTS.BLACKJACK / 100));
            resultMessage = 'Blackjack!';
            metaverseBlackjackGame.stats.blackjacksHit++;
            break;
        case 'win':
        case 'dealer_bust':
            totalWinnings = Math.floor(metaverseBlackjackGame.betAmount * (METAVERSE_BLACKJACK_PAYOUTS.WIN / 100));
            resultMessage = metaverseBlackjackGame.blackjack.handResult === 'dealer_bust' ? 'Dealer bust!' : 'You win!';
            break;
        case 'push':
            totalWinnings = metaverseBlackjackGame.betAmount; // Return bet
            resultMessage = 'Push';
            break;
        case 'lose':
        case 'bust':
            totalWinnings = 0;
            resultMessage = metaverseBlackjackGame.blackjack.handResult === 'bust' ? 'Bust!' : 'Dealer wins';
            break;
    }

    // Apply VR bonuses (actually work)
    if (metaverseBlackjackGame.blackjack.tellsDetected.length >= 3 && totalWinnings > 0) {
        const tellsBonus = Math.floor(totalWinnings * METAVERSE_BLACKJACK_PAYOUTS.TELLS_BONUS);
        totalWinnings += tellsBonus;
        resultMessage += ' + Tells Bonus!';
    }

    // Apply avatar bonus
    if (metaverseBlackjackGame.avatar.confidence >= 0.85 && totalWinnings > 0) {
        const avatarBonus = Math.floor(totalWinnings * METAVERSE_BLACKJACK_PAYOUTS.AVATAR_BONUS);
        totalWinnings += avatarBonus;
        resultMessage += ' + Avatar Bonus!';
    }

    // Apply immersion bonus
    if (metaverseBlackjackGame.vrEnvironments[metaverseBlackjackGame.vrEnvironment].immersion >= 0.85 && totalWinnings > 0) {
        const immersionBonus = Math.floor(totalWinnings * METAVERSE_BLACKJACK_PAYOUTS.IMMERSION_BONUS);
        totalWinnings += immersionBonus;
        resultMessage += ' + Immersion Bonus!';
    }

    // Apply VR experience bonus
    if (metaverseBlackjackGame.avatar.vrLevel === 'expert' || metaverseBlackjackGame.avatar.vrLevel === 'master') {
        const experienceBonus = Math.floor(totalWinnings * METAVERSE_BLACKJACK_PAYOUTS.VR_EXPERIENCE_BONUS);
        totalWinnings += experienceBonus;
        resultMessage += ' + VR Master!';
    }

    // Apply VR environment multiplier
    totalWinnings = Math.floor(totalWinnings * vrEnvironmentData.payoutMultiplier);

    // Apply avatar advantage (improved for 3-5% win rate)
    if (metaverseBlackjackGame.blackjack.avatarAdvantage > 0.20 && Math.random() < 0.15) {
        // 15% chance for avatar advantage to provide consolation
        if (totalWinnings === 0) {
            totalWinnings = Math.floor(metaverseBlackjackGame.betAmount * 0.4); // 40% return
            resultMessage = 'Avatar intuition bonus';
        }
    }

    // Award metaverse points
    const pointsEarned = metaverseBlackjackGame.blackjack.handResult === 'blackjack' ? 5 :
                        (totalWinnings > 0 ? 3 : 1);
    metaverseBlackjackGame.stats.metaversePoints += pointsEarned;

    // Add winnings to balance
    balance += totalWinnings;
    metaverseBlackjackGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(totalWinnings > metaverseBlackjackGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Environment: ${VR_ENVIRONMENTS[metaverseBlackjackGame.vrEnvironment].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show VR event
    document.getElementById('vrEvent').classList.remove('hidden');
    document.getElementById('vrEvent').textContent = metaverseBlackjackGame.blackjack.handResult === 'blackjack' ? 'BLACKJACK!' : 'HAND COMPLETE!';
    document.getElementById('vrEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update blackjack display
function updateBlackjackDisplay() {
    // Update player hand
    const playerHandElement = document.getElementById('playerHand');
    playerHandElement.innerHTML = '';
    metaverseBlackjackGame.blackjack.playerHand.forEach(card => {
        const cardElement = document.createElement('div');
        cardElement.className = 'w-12 h-16 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
        cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        playerHandElement.appendChild(cardElement);
    });

    // Update dealer hand
    const dealerHandElement = document.getElementById('dealerHand');
    dealerHandElement.innerHTML = '';
    metaverseBlackjackGame.blackjack.dealerHand.forEach(card => {
        const cardElement = document.createElement('div');
        if (card.faceDown) {
            cardElement.className = 'w-12 h-16 bg-blue-600 rounded border-2 border-blue-400 flex items-center justify-center text-white text-xs font-bold';
            cardElement.textContent = '?';
        } else {
            cardElement.className = 'w-12 h-16 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
            cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        }
        dealerHandElement.appendChild(cardElement);
    });

    // Update hand values
    document.getElementById('playerValue').textContent = `Value: ${metaverseBlackjackGame.blackjack.playerValue}`;
    document.getElementById('dealerValue').textContent = `Value: ${metaverseBlackjackGame.blackjack.dealerValue}`;
}

// Update action buttons
function updateActionButtons() {
    const canHit = metaverseBlackjackGame.blackjack.gamePhase === 'playing' && metaverseBlackjackGame.blackjack.playerValue < 21;
    const canStand = metaverseBlackjackGame.blackjack.gamePhase === 'playing';
    const canDouble = metaverseBlackjackGame.blackjack.gamePhase === 'playing' &&
                     metaverseBlackjackGame.blackjack.playerHand.length === 2 &&
                     metaverseBlackjackGame.betAmount <= balance;

    document.getElementById('hitCard').disabled = !canHit;
    document.getElementById('standCards').disabled = !canStand;
    document.getElementById('doubleDown').disabled = !canDouble;

    if (metaverseBlackjackGame.blackjack.gamePhase === 'finished') {
        document.getElementById('dealCards').disabled = false;
    }
}

// Update VR display
function updateVRDisplay() {
    updateAvatarStatus();
    updateTellsDisplay();
}

// Update avatar status
function updateAvatarStatus() {
    document.getElementById('confidence').innerHTML =
        `<div class="text-green-400 font-bold">CONFIDENCE: ${Math.floor(metaverseBlackjackGame.avatar.confidence * 100)}%</div>`;
    document.getElementById('stress').innerHTML =
        `<div class="text-red-400 font-bold">STRESS: ${Math.floor(metaverseBlackjackGame.avatar.stress * 100)}%</div>`;
    document.getElementById('focus').innerHTML =
        `<div class="text-blue-400 font-bold">FOCUS: ${Math.floor(metaverseBlackjackGame.avatar.focus * 100)}%</div>`;
    document.getElementById('emotionalState').innerHTML =
        `<div class="text-purple-400 font-bold">STATE: ${metaverseBlackjackGame.avatar.emotionalState.toUpperCase()}</div>`;

    document.getElementById('avatarConfidence').querySelector('.text-2xl').textContent =
        `${Math.floor(metaverseBlackjackGame.avatar.confidence * 100)}%`;
}

// Update tells display
function updateTellsDisplay() {
    const vrEnvironmentData = metaverseBlackjackGame.vrEnvironments[metaverseBlackjackGame.vrEnvironment];
    const avatarTypeData = AVATAR_TYPES[metaverseBlackjackGame.avatarType];

    document.getElementById('tellsDetected').textContent = metaverseBlackjackGame.blackjack.tellsDetected.length;
    document.getElementById('tellsAccuracy').textContent = `${Math.floor(avatarTypeData.tellsAccuracy * 100)}%`;
    document.getElementById('vrImmersion').textContent = `${Math.floor(vrEnvironmentData.immersion * 100)}%`;

    document.getElementById('tellsCounter').querySelector('.text-2xl').textContent =
        metaverseBlackjackGame.blackjack.tellsDetected.length;
}

// Update VR effects
function updateVREffects() {
    // Update VR effects based on avatar confidence
    const confidence = metaverseBlackjackGame.avatar.confidence;
    const effects = document.querySelectorAll('#vrEffects circle');

    effects.forEach((effect, index) => {
        if (confidence >= 0.85) {
            effect.setAttribute('opacity', '0.9');
            effect.setAttribute('fill', '#10b981'); // Green for high confidence
        } else if (confidence >= 0.70) {
            effect.setAttribute('opacity', '0.7');
            effect.setAttribute('fill', '#06b6d4'); // Cyan for good confidence
        } else if (confidence >= 0.50) {
            effect.setAttribute('opacity', '0.5');
            effect.setAttribute('fill', '#3b82f6'); // Blue for medium confidence
        } else {
            effect.setAttribute('opacity', '0.3');
            effect.setAttribute('fill', '#6366f1'); // Indigo for low confidence
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${metaverseBlackjackGame.betAmount}`;
    document.getElementById('metaversePointsDisplay').textContent = metaverseBlackjackGame.stats.metaversePoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = metaverseBlackjackGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${metaverseBlackjackGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${metaverseBlackjackGame.stats.totalWagered}`;
    document.getElementById('blackjacksHit').textContent = metaverseBlackjackGame.stats.blackjacksHit;
    document.getElementById('tellsAnalyzedStat').textContent = metaverseBlackjackGame.stats.tellsAnalyzed;

    const netResult = metaverseBlackjackGame.stats.totalWon - metaverseBlackjackGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-cyan-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    metaverseBlackjackGame.stats.handsPlayed++;
    metaverseBlackjackGame.stats.totalWagered += metaverseBlackjackGame.betAmount;
    metaverseBlackjackGame.stats.totalWon += winnings;

    if (won) {
        metaverseBlackjackGame.stats.handsWon++;
        metaverseBlackjackGame.stats.currentStreak++;
        metaverseBlackjackGame.streakData.currentWinStreak++;
        metaverseBlackjackGame.streakData.currentLossStreak = 0;

        if (metaverseBlackjackGame.streakData.currentWinStreak > metaverseBlackjackGame.streakData.longestWinStreak) {
            metaverseBlackjackGame.streakData.longestWinStreak = metaverseBlackjackGame.streakData.currentWinStreak;
        }

        if (winnings > metaverseBlackjackGame.stats.biggestWin) {
            metaverseBlackjackGame.stats.biggestWin = winnings;
        }
    } else {
        metaverseBlackjackGame.stats.currentStreak = 0;
        metaverseBlackjackGame.streakData.currentWinStreak = 0;
        metaverseBlackjackGame.streakData.currentLossStreak++;

        if (metaverseBlackjackGame.streakData.currentLossStreak > metaverseBlackjackGame.streakData.longestLossStreak) {
            metaverseBlackjackGame.streakData.longestLossStreak = metaverseBlackjackGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to VR mechanics)
    metaverseBlackjackGame.stats.winRate = (metaverseBlackjackGame.stats.handsWon / metaverseBlackjackGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next VR session
function resetGame() {
    metaverseBlackjackGame.isPlaying = false;
    metaverseBlackjackGame.betAmount = 0;
    metaverseBlackjackGame.totalBet = 0;
    metaverseBlackjackGame.gameResult = '';
    metaverseBlackjackGame.totalWin = 0;

    // Reset blackjack system
    metaverseBlackjackGame.blackjack.playerHand = [];
    metaverseBlackjackGame.blackjack.dealerHand = [];
    metaverseBlackjackGame.blackjack.playerValue = 0;
    metaverseBlackjackGame.blackjack.dealerValue = 0;
    metaverseBlackjackGame.blackjack.gamePhase = 'betting';
    metaverseBlackjackGame.blackjack.isBlackjack = false;
    metaverseBlackjackGame.blackjack.isBust = false;
    metaverseBlackjackGame.blackjack.dealerBust = false;
    metaverseBlackjackGame.blackjack.handResult = '';
    metaverseBlackjackGame.blackjack.tellsDetected = [];

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('vrEvent').classList.add('hidden');
    document.getElementById('blackjackActions').classList.add('hidden');

    // Reset blackjack table
    document.getElementById('playerHand').innerHTML = '';
    document.getElementById('dealerHand').innerHTML = '';
    document.getElementById('playerValue').textContent = 'Value: 0';
    document.getElementById('dealerValue').textContent = 'Value: 0';

    // Reset VR session display
    document.getElementById('avatarStatus').textContent = 'Ready to enter VR lounge...';
    document.getElementById('sessionBar').style.width = '0%';
    document.getElementById('sessionStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable enter VR button
    document.getElementById('enterVR').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'VR lounge ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Metaverse Blackjack Lounge - VR Avatars with Real-time "Tells" Analysis';

    // Reinitialize systems for next session
    initializeVRSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMetaverseBlackjackGame();
});