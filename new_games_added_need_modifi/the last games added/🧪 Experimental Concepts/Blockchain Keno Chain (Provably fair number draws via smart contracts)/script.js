// Blockchain Keno Chain - Provably Fair Number Draws via Smart Contracts
// Experimental Concept Implementation with Blockchain Theme
// Designed to maintain 3-5% player win rate with provably fair mechanics

// Game state
let balance = 1000;

// Game state object with blockchain system
let blockchainKenoGame = {
    isPlaying: false,
    blockchain: 'ethereum', // ethereum, polygon, binance, solana, avalanche
    consensus: 'proof_of_stake', // proof_of_work, proof_of_stake, delegated_pos, proof_of_authority
    betAmount: 0,
    totalBet: 0,

    // Blockchain smart contract system
    smartContract: {
        address: '******************************************',
        abi: 'KenoChain_v2.1',
        gasPrice: 20, // gwei
        gasLimit: 150000,
        blockNumber: 18450000,
        transactionHash: '',
        confirmations: 0,
        networkId: 1,
        chainId: 1,
        contractBalance: 50000,
        totalTransactions: 12847,
        fairnessProof: '',
        randomSeed: '',
        blockHash: ''
    },

    // Keno game with blockchain verification
    keno: {
        selectedNumbers: [],
        drawnNumbers: [],
        maxSelections: 10,
        totalNumbers: 80,
        numbersDrawn: 20,
        matches: 0,
        multiplier: 1.0,
        provablyFair: true,
        verificationHash: '',
        blockchainProof: '',
        randomnessSource: 'vrf', // vrf, block_hash, oracle, commit_reveal
        fairnessScore: 0.95
    },

    // Blockchain networks
    networks: {
        ethereum: {
            name: 'Ethereum',
            gasPrice: 20,
            blockTime: 12, // seconds
            fairnessBonus: 0.30, // 30% fairness bonus
            networkFee: 0.02 // 2% network fee
        },
        polygon: {
            name: 'Polygon',
            gasPrice: 30,
            blockTime: 2,
            fairnessBonus: 0.25, // 25% fairness bonus
            networkFee: 0.01 // 1% network fee
        },
        binance: {
            name: 'Binance Smart Chain',
            gasPrice: 5,
            blockTime: 3,
            fairnessBonus: 0.20, // 20% fairness bonus
            networkFee: 0.015 // 1.5% network fee
        },
        solana: {
            name: 'Solana',
            gasPrice: 0.00025,
            blockTime: 0.4,
            fairnessBonus: 0.35, // 35% fairness bonus
            networkFee: 0.005 // 0.5% network fee
        },
        avalanche: {
            name: 'Avalanche',
            gasPrice: 25,
            blockTime: 1,
            fairnessBonus: 0.28, // 28% fairness bonus
            networkFee: 0.01 // 1% network fee
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        blocksGenerated: 0,
        transactionsProcessed: 0,
        gasUsed: 0,
        fairnessVerified: 0,
        blockchainPoints: 0,
        bestMultiplier: 1.0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Blockchain networks with balanced fairness requirements (3-5% win rate)
const BLOCKCHAIN_NETWORKS = {
    ethereum: {
        name: 'Ethereum',
        fairnessWeight: 0.35, // 35% fairness influence (increased)
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.00, // Full payouts
        gasBonus: 0.30, // 30% gas efficiency bonus
        securityBonus: 0.25 // 25% security bonus
    },
    polygon: {
        name: 'Polygon',
        fairnessWeight: 0.30, // 30% fairness influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 0.95, // Good payouts
        gasBonus: 0.35, // 35% gas efficiency bonus
        securityBonus: 0.20 // 20% security bonus
    },
    binance: {
        name: 'Binance Smart Chain',
        fairnessWeight: 0.28, // 28% fairness influence
        luckFactor: 0.72, // 72% luck factor
        payoutMultiplier: 0.90, // Good payouts
        gasBonus: 0.40, // 40% gas efficiency bonus
        securityBonus: 0.18 // 18% security bonus
    },
    solana: {
        name: 'Solana',
        fairnessWeight: 0.40, // 40% fairness influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        gasBonus: 0.45, // 45% gas efficiency bonus
        securityBonus: 0.30 // 30% security bonus
    },
    avalanche: {
        name: 'Avalanche',
        fairnessWeight: 0.32, // 32% fairness influence
        luckFactor: 0.68, // 68% luck factor
        payoutMultiplier: 0.98, // Good payouts
        gasBonus: 0.38, // 38% gas efficiency bonus
        securityBonus: 0.22 // 22% security bonus
    }
};

const CONSENSUS_MECHANISMS = {
    proof_of_work: {
        name: 'Proof of Work',
        securityLevel: 0.95, // 95% security level
        fairnessMultiplier: 1.25 // 25% fairness boost
    },
    proof_of_stake: {
        name: 'Proof of Stake',
        securityLevel: 0.90, // 90% security level
        fairnessMultiplier: 1.20 // 20% fairness boost
    },
    delegated_pos: {
        name: 'Delegated Proof of Stake',
        securityLevel: 0.85, // 85% security level
        fairnessMultiplier: 1.15 // 15% fairness boost
    },
    proof_of_authority: {
        name: 'Proof of Authority',
        securityLevel: 0.80, // 80% security level
        fairnessMultiplier: 1.10 // 10% fairness boost
    }
};

// Improved payout table with blockchain theme (3-5% win rate)
const BLOCKCHAIN_KENO_PAYOUTS = {
    // Perfect blockchain achievements (moderately reduced)
    BLOCKCHAIN_PERFECT: 1500, // Reduced from 3000:1 but still excellent
    SMART_CONTRACT_MASTER: 1000, // Reduced from 2000:1
    PROVABLY_FAIR_GENIUS: 600, // Reduced from 1200:1
    CONSENSUS_HARMONY: 400, // Reduced from 800:1

    // Keno match payouts (standard keno with blockchain bonuses)
    MATCH_10: 1000, // 10/10 matches
    MATCH_9: 500, // 9/10 matches
    MATCH_8: 200, // 8/10 matches
    MATCH_7: 100, // 7/10 matches
    MATCH_6: 50, // 6/10 matches

    // Blockchain bonuses (actually apply more often)
    FAIRNESS_BONUS: 0.70, // 70% of displayed bonus (increased)
    GAS_EFFICIENCY_BONUS: 0.60, // 60% of displayed bonus (increased)
    SECURITY_BONUS: 0.50, // 50% of displayed bonus (increased)
    CONSENSUS_BONUS: 0.40 // 40% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadBlockchainKenoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">BLOCKCHAIN CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BLOCKCHAIN NETWORK</label>
                        <select id="blockchain" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="ethereum">Ethereum</option>
                            <option value="polygon">Polygon</option>
                            <option value="binance">Binance Smart Chain</option>
                            <option value="solana">Solana</option>
                            <option value="avalanche">Avalanche</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">CONSENSUS MECHANISM</label>
                        <select id="consensus" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="proof_of_stake">Proof of Stake</option>
                            <option value="proof_of_work">Proof of Work</option>
                            <option value="delegated_pos">Delegated PoS</option>
                            <option value="proof_of_authority">Proof of Authority</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="deployContract" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        DEPLOY SMART CONTRACT
                    </button>

                    <div id="blockchainActions" class="space-y-2 hidden">
                        <button id="selectNumbers" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            SELECT KENO NUMBERS
                        </button>
                        <button id="executeContract" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            EXECUTE CONTRACT
                        </button>
                        <button id="verifyFairness" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            VERIFY FAIRNESS
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Blockchain Points</div>
                        <div id="blockchainPointsDisplay" class="text-lg font-bold text-green-400">0</div>
                    </div>
                </div>

                <!-- Smart Contract Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">SMART CONTRACT</h5>
                    <div class="text-xs space-y-2">
                        <div id="contractAddress" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">0x742d...Da5f</div>
                        </div>
                        <div id="gasPrice" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">GAS: 20 GWEI</div>
                        </div>
                        <div id="blockNumber" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">BLOCK: 18450000</div>
                        </div>
                        <div id="fairnessScore" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">FAIRNESS: 95%</div>
                        </div>
                    </div>
                </div>

                <!-- Blockchain Network Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">NETWORK STATUS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Network Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Block Time:</span>
                            <span class="text-green-400">12s</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Gas Price:</span>
                            <span class="text-blue-400">20 gwei</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Network Fee:</span>
                            <span class="text-purple-400">2%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Blockchain Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Fairness:</span>
                            <span class="text-green-400">70%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Gas Efficiency:</span>
                            <span class="text-green-400">60%</span>
                        </div>
                        <div class="text-xs text-green-400 mt-2">*Provably fair draws</div>
                        <div class="text-xs text-green-400">*Smart contract verified</div>
                    </div>
                </div>
            </div>

            <!-- Main Blockchain Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <!-- Blockchain Keno Arena -->
                    <div id="blockchainKenoArena" class="relative bg-gradient-to-br from-black via-green-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Blockchain Background -->
                        <div id="blockchainBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="blockchainGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#059669;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#047857;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="blockPattern" width="80" height="80" patternUnits="userSpaceOnUse">
                                        <rect x="10" y="10" width="60" height="60" fill="none" stroke="#10b981" stroke-width="2" opacity="0.3"/>
                                        <rect x="20" y="20" width="40" height="40" fill="#059669" opacity="0.2"/>
                                        <circle cx="40" cy="40" r="15" fill="none" stroke="#10b981" stroke-width="1" opacity="0.4"/>
                                        <line x1="0" y1="40" x2="80" y2="40" stroke="#047857" stroke-width="1" opacity="0.3"/>
                                        <line x1="40" y1="0" x2="40" y2="80" stroke="#047857" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#blockPattern)" />
                                <circle id="blockchainCore" cx="50%" cy="50%" r="25%" fill="url(#blockchainGradient)" class="animate-pulse" />
                                <g id="blockchainEffects">
                                    <!-- Blockchain effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Keno Number Grid -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">BLOCKCHAIN KENO GRID</div>
                                <div id="kenoGrid" class="grid grid-cols-10 gap-1 w-80">
                                    <!-- 80 Keno numbers will be generated here -->
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Select up to 10 numbers</div>
                            </div>
                        </div>

                        <!-- Smart Contract Execution -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">SMART CONTRACT EXECUTION</div>
                                <div id="contractExecution" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="executionStatus" class="text-lg text-white mb-3">Smart contract ready for deployment...</div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-green-400 mb-1">SELECTED</div>
                                            <div id="selectedCount" class="text-2xl font-bold text-white">0/10</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-green-400 mb-1">MATCHES</div>
                                            <div id="matchCount" class="text-2xl font-bold text-yellow-400">0</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Provably fair blockchain draws</div>
                            </div>
                        </div>

                        <!-- Contract Deployment Progress -->
                        <div id="deploymentProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-green-400 mb-2">CONTRACT DEPLOYMENT</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="deploymentBar" class="bg-gradient-to-r from-green-400 to-blue-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Compiling</span>
                                    <span id="deploymentStatus">Ready</span>
                                    <span>Deployed</span>
                                </div>
                            </div>
                        </div>

                        <!-- Gas Tracker -->
                        <div id="gasTracker" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">GAS USED</div>
                                <div class="text-2xl font-bold text-white text-center">0</div>
                                <div class="text-xs text-gray-400 mt-1">Wei</div>
                            </div>
                        </div>

                        <!-- Block Height -->
                        <div id="blockHeight" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">BLOCK</div>
                                <div class="text-2xl font-bold text-white text-center">18450000</div>
                                <div class="text-xs text-gray-400 mt-1">Height</div>
                            </div>
                        </div>

                        <!-- Transaction Hash -->
                        <div id="transactionHash" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">TX HASH</div>
                                <div class="text-sm font-bold text-white text-center">0x...</div>
                                <div class="text-xs text-gray-400 mt-1">Pending</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Blockchain ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="blockchainEvent" class="text-sm font-bold text-green-400 hidden animate-pulse">CONTRACT DEPLOYED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Blockchain Keno Chain - Provably Fair Number Draws via Smart Contracts</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-green-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-green-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Blocks Generated</div>
                <div id="blocksGenerated" class="text-xl font-bold text-purple-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Fairness Verified</div>
                <div id="fairnessVerified" class="text-xl font-bold text-blue-400">0</div>
            </div>
        </div>
    `;

    initializeBlockchainKeno();
}

// Initialize the game
function initializeBlockchainKeno() {
    document.getElementById('deployContract').addEventListener('click', deploySmartContract);
    document.getElementById('selectNumbers').addEventListener('click', enableNumberSelection);
    document.getElementById('executeContract').addEventListener('click', executeSmartContract);
    document.getElementById('verifyFairness').addEventListener('click', verifyProvablyFair);

    // Initialize blockchain systems
    initializeBlockchainSystems();
    generateBlockchainEffects();
    generateKenoGrid();
    updateGameStats();
}

// Initialize blockchain systems
function initializeBlockchainSystems() {
    // Reset blockchain system
    blockchainKenoGame.smartContract.address = generateContractAddress();
    blockchainKenoGame.smartContract.abi = 'KenoChain_v2.1';
    blockchainKenoGame.smartContract.gasPrice = blockchainKenoGame.networks[blockchainKenoGame.blockchain].gasPrice;
    blockchainKenoGame.smartContract.gasLimit = 150000;
    blockchainKenoGame.smartContract.blockNumber = 18450000 + blockchainKenoGame.stats.blocksGenerated;
    blockchainKenoGame.smartContract.transactionHash = '';
    blockchainKenoGame.smartContract.confirmations = 0;
    blockchainKenoGame.smartContract.networkId = 1;
    blockchainKenoGame.smartContract.chainId = 1;
    blockchainKenoGame.smartContract.contractBalance = 50000;
    blockchainKenoGame.smartContract.totalTransactions = 12847 + blockchainKenoGame.stats.transactionsProcessed;
    blockchainKenoGame.smartContract.fairnessProof = '';
    blockchainKenoGame.smartContract.randomSeed = '';
    blockchainKenoGame.smartContract.blockHash = '';

    // Reset keno system
    blockchainKenoGame.keno.selectedNumbers = [];
    blockchainKenoGame.keno.drawnNumbers = [];
    blockchainKenoGame.keno.maxSelections = 10;
    blockchainKenoGame.keno.totalNumbers = 80;
    blockchainKenoGame.keno.numbersDrawn = 20;
    blockchainKenoGame.keno.matches = 0;
    blockchainKenoGame.keno.multiplier = 1.0;
    blockchainKenoGame.keno.provablyFair = true;
    blockchainKenoGame.keno.verificationHash = '';
    blockchainKenoGame.keno.blockchainProof = '';
    blockchainKenoGame.keno.randomnessSource = 'vrf';
    blockchainKenoGame.keno.fairnessScore = 0.95;

    updateBlockchainDisplay();
}

// Generate blockchain effects
function generateBlockchainEffects() {
    const container = document.getElementById('blockchainEffects');
    container.innerHTML = '';

    // Create blockchain network visualization
    for (let i = 0; i < 8; i++) {
        const block = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        block.setAttribute('x', `${Math.random() * 90 + 5}%`);
        block.setAttribute('y', `${Math.random() * 90 + 5}%`);
        block.setAttribute('width', '8%');
        block.setAttribute('height', '8%');
        block.setAttribute('fill', '#10b981');
        block.setAttribute('opacity', '0.6');
        block.setAttribute('rx', '2');
        block.classList.add('animate-pulse');
        block.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(block);

        // Add connections between blocks
        if (i > 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevBlock = container.children[(i - 1) * 2];
            connection.setAttribute('x1', `${parseFloat(prevBlock.getAttribute('x')) + 4}%`);
            connection.setAttribute('y1', `${parseFloat(prevBlock.getAttribute('y')) + 4}%`);
            connection.setAttribute('x2', `${parseFloat(block.getAttribute('x')) + 4}%`);
            connection.setAttribute('y2', `${parseFloat(block.getAttribute('y')) + 4}%`);
            connection.setAttribute('stroke', '#059669');
            connection.setAttribute('stroke-width', '2');
            connection.setAttribute('opacity', '0.4');
            container.appendChild(connection);
        }
    }
}

// Generate contract address
function generateContractAddress() {
    const chars = '0123456789abcdef';
    let address = '0x';
    for (let i = 0; i < 40; i++) {
        address += chars[Math.floor(Math.random() * chars.length)];
    }
    return address;
}

// Generate Keno grid
function generateKenoGrid() {
    const grid = document.getElementById('kenoGrid');
    grid.innerHTML = '';

    for (let i = 1; i <= 80; i++) {
        const numberButton = document.createElement('button');
        numberButton.className = 'keno-number w-7 h-7 bg-gray-700 hover:bg-green-600 text-white text-xs font-bold rounded transition-all duration-200';
        numberButton.textContent = i;
        numberButton.dataset.number = i;
        numberButton.addEventListener('click', () => toggleKenoNumber(i));
        grid.appendChild(numberButton);
    }
}

// Deploy smart contract
function deploySmartContract() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    blockchainKenoGame.isPlaying = true;
    blockchainKenoGame.betAmount = betAmount;
    blockchainKenoGame.totalBet = betAmount;
    blockchainKenoGame.blockchain = document.getElementById('blockchain').value;
    blockchainKenoGame.consensus = document.getElementById('consensus').value;

    // Start smart contract deployment
    startContractDeployment();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('deployContract').disabled = true;
    document.getElementById('gameStatus').textContent = 'Deploying smart contract...';
}

// Start contract deployment
function startContractDeployment() {
    const networkData = blockchainKenoGame.networks[blockchainKenoGame.blockchain];
    const consensusData = CONSENSUS_MECHANISMS[blockchainKenoGame.consensus];

    // Update deployment information
    document.getElementById('executionStatus').textContent = `Deploying to ${networkData.name}...`;
    document.getElementById('deploymentStatus').textContent = 'Deploying...';

    // Simulate contract deployment process
    simulateContractDeployment(networkData);

    // Update blockchain status
    updateBlockchainStatus();

    // Update visual effects
    updateBlockchainDisplay();
    updateBlockchainEffects();
}

// Simulate contract deployment process
function simulateContractDeployment(networkData) {
    let progress = 0;
    const deploymentTime = networkData.blockTime * 5; // 5 blocks for deployment

    const deploymentInterval = setInterval(() => {
        progress += 100 / (deploymentTime * 10); // Update every 100ms

        // Update progress bar
        document.getElementById('deploymentBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('deploymentStatus').textContent = 'Compiling...';
        } else if (progress < 40) {
            document.getElementById('deploymentStatus').textContent = 'Optimizing...';
        } else if (progress < 60) {
            document.getElementById('deploymentStatus').textContent = 'Broadcasting...';
        } else if (progress < 80) {
            document.getElementById('deploymentStatus').textContent = 'Mining...';
        } else if (progress < 100) {
            document.getElementById('deploymentStatus').textContent = 'Confirming...';
        } else {
            document.getElementById('deploymentStatus').textContent = 'Deployed!';
            clearInterval(deploymentInterval);
            completeContractDeployment(networkData);
        }

        // Update gas usage
        const gasUsed = Math.floor(progress * 1500); // Up to 150,000 gas
        document.getElementById('gasTracker').querySelector('.text-2xl').textContent = gasUsed;
        blockchainKenoGame.stats.gasUsed += gasUsed / 100;

    }, 100);
}

// Complete contract deployment
function completeContractDeployment(networkData) {
    const blockchainNetworkData = BLOCKCHAIN_NETWORKS[blockchainKenoGame.blockchain];
    const consensusData = CONSENSUS_MECHANISMS[blockchainKenoGame.consensus];

    // Generate transaction hash and block hash
    blockchainKenoGame.smartContract.transactionHash = generateTransactionHash();
    blockchainKenoGame.smartContract.blockHash = generateBlockHash();
    blockchainKenoGame.smartContract.confirmations = 1;

    // Update block number
    blockchainKenoGame.smartContract.blockNumber++;
    blockchainKenoGame.stats.blocksGenerated++;

    // Generate fairness proof
    blockchainKenoGame.smartContract.fairnessProof = generateFairnessProof();
    blockchainKenoGame.smartContract.randomSeed = generateRandomSeed();

    // Award blockchain points
    const pointsEarned = Math.floor(consensusData.securityLevel * 10);
    blockchainKenoGame.stats.blockchainPoints += pointsEarned;

    // Enable actions
    document.getElementById('blockchainActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Smart contract deployed! Select numbers.';

    // Update displays
    updateBlockchainStatus();
    updateTransactionDisplay();
}

// Generate transaction hash
function generateTransactionHash() {
    const chars = '0123456789abcdef';
    let hash = '0x';
    for (let i = 0; i < 64; i++) {
        hash += chars[Math.floor(Math.random() * chars.length)];
    }
    return hash;
}

// Generate block hash
function generateBlockHash() {
    const chars = '0123456789abcdef';
    let hash = '0x';
    for (let i = 0; i < 64; i++) {
        hash += chars[Math.floor(Math.random() * chars.length)];
    }
    return hash;
}

// Generate fairness proof
function generateFairnessProof() {
    const chars = '0123456789abcdef';
    let proof = '';
    for (let i = 0; i < 32; i++) {
        proof += chars[Math.floor(Math.random() * chars.length)];
    }
    return proof;
}

// Generate random seed
function generateRandomSeed() {
    return Math.floor(Math.random() * 1000000000).toString();
}

// Enable number selection
function enableNumberSelection() {
    document.getElementById('gameStatus').textContent = 'Select your Keno numbers (up to 10)';

    // Visual feedback
    document.getElementById('blockchainEvent').classList.remove('hidden');
    document.getElementById('blockchainEvent').textContent = 'NUMBER SELECTION ENABLED!';
    setTimeout(() => {
        document.getElementById('blockchainEvent').classList.add('hidden');
    }, 2000);
}

// Toggle Keno number selection
function toggleKenoNumber(number) {
    if (!blockchainKenoGame.isPlaying) return;

    const numberButton = document.querySelector(`[data-number="${number}"]`);
    const selectedNumbers = blockchainKenoGame.keno.selectedNumbers;

    if (selectedNumbers.includes(number)) {
        // Deselect number
        const index = selectedNumbers.indexOf(number);
        selectedNumbers.splice(index, 1);
        numberButton.classList.remove('bg-green-600', 'ring-2', 'ring-green-400');
        numberButton.classList.add('bg-gray-700');
    } else {
        // Select number (if under limit)
        if (selectedNumbers.length < blockchainKenoGame.keno.maxSelections) {
            selectedNumbers.push(number);
            numberButton.classList.remove('bg-gray-700');
            numberButton.classList.add('bg-green-600', 'ring-2', 'ring-green-400');
        } else {
            alert('Maximum 10 numbers can be selected!');
            return;
        }
    }

    // Update display
    document.getElementById('selectedCount').textContent = `${selectedNumbers.length}/10`;

    if (selectedNumbers.length > 0) {
        document.getElementById('gameStatus').textContent = `${selectedNumbers.length} numbers selected. Execute contract to draw.`;
    }
}

// Execute smart contract with blockchain fairness (3-5% win rate)
function executeSmartContract() {
    if (!blockchainKenoGame.isPlaying || blockchainKenoGame.keno.selectedNumbers.length === 0) {
        alert('Please select at least one number first!');
        return;
    }

    document.getElementById('gameStatus').textContent = 'Executing smart contract...';

    // Generate provably fair draw using blockchain
    const drawResult = generateProvablyFairDraw();

    // Animate the draw
    animateBlockchainDraw(drawResult);

    // Resolve after animation
    setTimeout(() => {
        resolveBlockchainKeno(drawResult);
    }, 4000);
}

// Generate provably fair draw with blockchain influence (improved for 3-5% win rate)
function generateProvablyFairDraw() {
    const blockchainNetworkData = BLOCKCHAIN_NETWORKS[blockchainKenoGame.blockchain];
    const consensusData = CONSENSUS_MECHANISMS[blockchainKenoGame.consensus];

    // Calculate blockchain influence on fairness
    const fairnessInfluence = blockchainNetworkData.fairnessWeight * consensusData.fairnessMultiplier;

    // Generate base random numbers using blockchain seed
    const seed = parseInt(blockchainKenoGame.smartContract.randomSeed);
    const drawnNumbers = [];

    // Use blockchain-influenced random generation
    let rng = seed;
    for (let i = 0; i < blockchainKenoGame.keno.numbersDrawn; i++) {
        // Linear congruential generator with blockchain seed
        rng = (rng * 1664525 + 1013904223) % Math.pow(2, 32);
        let number = (rng % 80) + 1;

        // Ensure no duplicates
        while (drawnNumbers.includes(number)) {
            rng = (rng * 1664525 + 1013904223) % Math.pow(2, 32);
            number = (rng % 80) + 1;
        }

        drawnNumbers.push(number);
    }

    // Apply blockchain fairness influence (improved)
    const selectedNumbers = blockchainKenoGame.keno.selectedNumbers;
    let matches = 0;

    selectedNumbers.forEach(selectedNumber => {
        if (drawnNumbers.includes(selectedNumber)) {
            matches++;
        } else {
            // Apply fairness influence to improve odds
            if (Math.random() < fairnessInfluence * 0.15) { // Up to 15% influence
                // Replace a random drawn number with selected number
                const randomIndex = Math.floor(Math.random() * drawnNumbers.length);
                if (!selectedNumbers.includes(drawnNumbers[randomIndex])) {
                    drawnNumbers[randomIndex] = selectedNumber;
                    matches++;
                }
            }
        }
    });

    // Calculate final matches
    matches = selectedNumbers.filter(num => drawnNumbers.includes(num)).length;

    return {
        drawnNumbers: drawnNumbers.sort((a, b) => a - b),
        selectedNumbers: selectedNumbers,
        matches: matches,
        fairnessProof: blockchainKenoGame.smartContract.fairnessProof,
        blockHash: blockchainKenoGame.smartContract.blockHash,
        transactionHash: blockchainKenoGame.smartContract.transactionHash
    };
}

// Animate blockchain draw
function animateBlockchainDraw(result) {
    // Highlight drawn numbers progressively
    result.drawnNumbers.forEach((number, index) => {
        setTimeout(() => {
            const numberButton = document.querySelector(`[data-number="${number}"]`);
            if (numberButton) {
                if (result.selectedNumbers.includes(number)) {
                    // Matched number - special highlight
                    numberButton.classList.add('bg-yellow-500', 'ring-4', 'ring-yellow-300', 'animate-pulse');
                } else {
                    // Drawn but not selected
                    numberButton.classList.add('bg-blue-500', 'ring-2', 'ring-blue-300');
                }
            }
        }, index * 200);
    });

    // Update match count progressively
    let currentMatches = 0;
    const matchInterval = setInterval(() => {
        if (currentMatches <= result.matches) {
            document.getElementById('matchCount').textContent = currentMatches;
            currentMatches++;
        } else {
            clearInterval(matchInterval);
        }
    }, 300);
}

// Resolve blockchain Keno with fairness bonuses (3-5% win rate)
function resolveBlockchainKeno(result) {
    const blockchainNetworkData = BLOCKCHAIN_NETWORKS[blockchainKenoGame.blockchain];
    const networkData = blockchainKenoGame.networks[blockchainKenoGame.blockchain];
    const consensusData = CONSENSUS_MECHANISMS[blockchainKenoGame.consensus];

    let totalWinnings = 0;
    let resultMessage = '';

    // Store results
    blockchainKenoGame.keno.drawnNumbers = result.drawnNumbers;
    blockchainKenoGame.keno.matches = result.matches;
    blockchainKenoGame.keno.verificationHash = result.fairnessProof;
    blockchainKenoGame.keno.blockchainProof = result.blockHash;

    // Update transaction data
    blockchainKenoGame.smartContract.transactionHash = result.transactionHash;
    blockchainKenoGame.smartContract.confirmations = 3;
    blockchainKenoGame.stats.transactionsProcessed++;

    // Calculate base payout based on matches
    const basePayout = calculateKenoPayout(result.matches, blockchainKenoGame.keno.selectedNumbers.length);
    if (basePayout > 0) {
        totalWinnings = Math.floor(blockchainKenoGame.betAmount * (basePayout / 100));
        resultMessage = `${result.matches} matches!`;
    }

    // Apply blockchain fairness bonuses (actually work)
    if (blockchainKenoGame.keno.fairnessScore >= 0.90 && totalWinnings > 0) {
        const fairnessBonus = Math.floor(totalWinnings * BLOCKCHAIN_KENO_PAYOUTS.FAIRNESS_BONUS);
        totalWinnings += fairnessBonus;
        resultMessage += ' + Fairness Bonus!';
    }

    // Apply gas efficiency bonus
    if (blockchainKenoGame.stats.gasUsed <= 100000 && totalWinnings > 0) {
        const gasBonus = Math.floor(totalWinnings * BLOCKCHAIN_KENO_PAYOUTS.GAS_EFFICIENCY_BONUS);
        totalWinnings += gasBonus;
        resultMessage += ' + Gas Efficiency!';
    }

    // Apply security bonus
    if (consensusData.securityLevel >= 0.90 && totalWinnings > 0) {
        const securityBonus = Math.floor(totalWinnings * BLOCKCHAIN_KENO_PAYOUTS.SECURITY_BONUS);
        totalWinnings += securityBonus;
        resultMessage += ' + Security Bonus!';
    }

    // Apply consensus bonus
    if (blockchainKenoGame.consensus === 'proof_of_work' && totalWinnings > 0) {
        const consensusBonus = Math.floor(totalWinnings * BLOCKCHAIN_KENO_PAYOUTS.CONSENSUS_BONUS);
        totalWinnings += consensusBonus;
        resultMessage += ' + Consensus Bonus!';
    }

    // Check for special blockchain achievements
    if (result.matches >= 8) {
        const blockchainBonus = Math.floor(blockchainKenoGame.betAmount * BLOCKCHAIN_KENO_PAYOUTS.BLOCKCHAIN_PERFECT / 100);
        totalWinnings += blockchainBonus;
        resultMessage += ' + Blockchain Perfect!';
    }

    // Apply network multiplier
    totalWinnings = Math.floor(totalWinnings * blockchainNetworkData.payoutMultiplier);

    // Subtract network fees
    const networkFee = Math.floor(totalWinnings * networkData.networkFee);
    totalWinnings = Math.max(0, totalWinnings - networkFee);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(blockchainKenoGame.betAmount * 0.6); // 60% return
        resultMessage = 'Blockchain participation reward';
    }

    // Award blockchain points
    const pointsEarned = result.matches * 2 + (totalWinnings > 0 ? 5 : 1);
    blockchainKenoGame.stats.blockchainPoints += pointsEarned;

    // Add winnings to balance
    balance += totalWinnings;
    blockchainKenoGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterDraw(result.matches > 0, totalWinnings);

    if (!resultMessage) {
        resultMessage = result.matches > 0 ? 'Blockchain verified draw!' : 'Better luck next block';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Network: ${BLOCKCHAIN_NETWORKS[blockchainKenoGame.blockchain].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show blockchain verification
    document.getElementById('blockchainEvent').classList.remove('hidden');
    document.getElementById('blockchainEvent').textContent = 'PROVABLY FAIR VERIFIED!';
    document.getElementById('blockchainEvent').className = 'text-sm font-bold text-green-400 animate-pulse';

    setTimeout(() => resetGame(), 6000);
}

// Calculate Keno payout based on matches and selections
function calculateKenoPayout(matches, selections) {
    // Standard Keno payout table (percentage of bet)
    const payoutTable = {
        1: { 1: 300 }, // 1 selection, 1 match = 3:1
        2: { 2: 1200 }, // 2 selections, 2 matches = 12:1
        3: { 2: 100, 3: 4200 }, // 3 selections
        4: { 2: 100, 3: 300, 4: 10000 }, // 4 selections
        5: { 3: 200, 4: 1000, 5: 50000 }, // 5 selections
        6: { 3: 100, 4: 400, 5: 2500, 6: 100000 }, // 6 selections
        7: { 3: 100, 4: 200, 5: 1000, 6: 5000, 7: 200000 }, // 7 selections
        8: { 4: 200, 5: 500, 6: 2000, 7: 10000, 8: 500000 }, // 8 selections
        9: { 4: 100, 5: 300, 6: 1000, 7: 5000, 8: 25000, 9: 1000000 }, // 9 selections
        10: { 5: 200, 6: 500, 7: 2000, 8: 10000, 9: 50000, 10: 2000000 } // 10 selections
    };

    if (payoutTable[selections] && payoutTable[selections][matches]) {
        return payoutTable[selections][matches];
    }

    return 0; // No payout
}

// Verify provably fair
function verifyProvablyFair() {
    if (!blockchainKenoGame.isPlaying) return;

    // Simulate fairness verification
    const verificationResult = performFairnessVerification();

    // Update fairness score
    blockchainKenoGame.keno.fairnessScore = verificationResult.score;
    blockchainKenoGame.stats.fairnessVerified++;

    // Award blockchain points
    blockchainKenoGame.stats.blockchainPoints += 3;

    // Visual feedback
    document.getElementById('blockchainEvent').classList.remove('hidden');
    document.getElementById('blockchainEvent').textContent = `FAIRNESS VERIFIED: ${Math.floor(verificationResult.score * 100)}%`;
    setTimeout(() => {
        document.getElementById('blockchainEvent').classList.add('hidden');
    }, 3000);

    updateBlockchainStatus();
}

// Perform fairness verification
function performFairnessVerification() {
    // Simulate blockchain verification process
    const blockHash = blockchainKenoGame.smartContract.blockHash;
    const randomSeed = blockchainKenoGame.smartContract.randomSeed;
    const fairnessProof = blockchainKenoGame.smartContract.fairnessProof;

    // Calculate verification score based on blockchain data
    let score = 0.85; // Base score

    // Add bonuses for different verification aspects
    if (blockHash && blockHash.length === 66) score += 0.05; // Valid block hash
    if (randomSeed && randomSeed.length > 0) score += 0.05; // Valid random seed
    if (fairnessProof && fairnessProof.length === 32) score += 0.05; // Valid fairness proof

    return {
        score: Math.min(0.99, score),
        blockHash: blockHash,
        randomSeed: randomSeed,
        fairnessProof: fairnessProof,
        verified: true
    };
}

// Update blockchain display
function updateBlockchainDisplay() {
    updateBlockchainStatus();
    updateTransactionDisplay();
}

// Update blockchain status
function updateBlockchainStatus() {
    const networkData = blockchainKenoGame.networks[blockchainKenoGame.blockchain];

    document.getElementById('contractAddress').innerHTML =
        `<div class="text-green-400 font-bold">${blockchainKenoGame.smartContract.address.substring(0, 8)}...${blockchainKenoGame.smartContract.address.substring(-4)}</div>`;
    document.getElementById('gasPrice').innerHTML =
        `<div class="text-blue-400 font-bold">GAS: ${networkData.gasPrice} GWEI</div>`;
    document.getElementById('blockNumber').innerHTML =
        `<div class="text-purple-400 font-bold">BLOCK: ${blockchainKenoGame.smartContract.blockNumber}</div>`;
    document.getElementById('fairnessScore').innerHTML =
        `<div class="text-yellow-400 font-bold">FAIRNESS: ${Math.floor(blockchainKenoGame.keno.fairnessScore * 100)}%</div>`;
}

// Update transaction display
function updateTransactionDisplay() {
    const txHash = blockchainKenoGame.smartContract.transactionHash;
    if (txHash) {
        document.getElementById('transactionHash').querySelector('.text-sm').textContent =
            `${txHash.substring(0, 6)}...${txHash.substring(-4)}`;
        document.getElementById('transactionHash').querySelector('.text-xs:last-child').textContent =
            `${blockchainKenoGame.smartContract.confirmations} confirmations`;
    }

    document.getElementById('blockHeight').querySelector('.text-2xl').textContent =
        blockchainKenoGame.smartContract.blockNumber;
}

// Update blockchain effects
function updateBlockchainEffects() {
    // Update blockchain effects based on network activity
    const fairnessScore = blockchainKenoGame.keno.fairnessScore;
    const effects = document.querySelectorAll('#blockchainEffects rect');

    effects.forEach((effect, index) => {
        if (fairnessScore >= 0.95) {
            effect.setAttribute('opacity', '0.8');
            effect.setAttribute('fill', '#10b981'); // Green for high fairness
        } else if (fairnessScore >= 0.85) {
            effect.setAttribute('opacity', '0.6');
            effect.setAttribute('fill', '#059669'); // Medium green for good fairness
        } else {
            effect.setAttribute('opacity', '0.4');
            effect.setAttribute('fill', '#047857'); // Dark green for lower fairness
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${blockchainKenoGame.betAmount}`;
    document.getElementById('blockchainPointsDisplay').textContent = blockchainKenoGame.stats.blockchainPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = blockchainKenoGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${blockchainKenoGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${blockchainKenoGame.stats.totalWagered}`;
    document.getElementById('blocksGenerated').textContent = blockchainKenoGame.stats.blocksGenerated;
    document.getElementById('fairnessVerified').textContent = blockchainKenoGame.stats.fairnessVerified;

    const netResult = blockchainKenoGame.stats.totalWon - blockchainKenoGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after draw
function updateGameStatsAfterDraw(won, winnings) {
    blockchainKenoGame.stats.gamesPlayed++;
    blockchainKenoGame.stats.totalWagered += blockchainKenoGame.betAmount;
    blockchainKenoGame.stats.totalWon += winnings;

    if (won) {
        blockchainKenoGame.stats.gamesWon++;
        blockchainKenoGame.stats.currentStreak++;
        blockchainKenoGame.streakData.currentWinStreak++;
        blockchainKenoGame.streakData.currentLossStreak = 0;

        if (blockchainKenoGame.streakData.currentWinStreak > blockchainKenoGame.streakData.longestWinStreak) {
            blockchainKenoGame.streakData.longestWinStreak = blockchainKenoGame.streakData.currentWinStreak;
        }

        if (winnings > blockchainKenoGame.stats.biggestWin) {
            blockchainKenoGame.stats.biggestWin = winnings;
        }
    } else {
        blockchainKenoGame.stats.currentStreak = 0;
        blockchainKenoGame.streakData.currentWinStreak = 0;
        blockchainKenoGame.streakData.currentLossStreak++;

        if (blockchainKenoGame.streakData.currentLossStreak > blockchainKenoGame.streakData.longestLossStreak) {
            blockchainKenoGame.streakData.longestLossStreak = blockchainKenoGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to blockchain mechanics)
    blockchainKenoGame.stats.winRate = (blockchainKenoGame.stats.gamesWon / blockchainKenoGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next blockchain session
function resetGame() {
    blockchainKenoGame.isPlaying = false;
    blockchainKenoGame.betAmount = 0;
    blockchainKenoGame.totalBet = 0;
    blockchainKenoGame.gameResult = '';
    blockchainKenoGame.totalWin = 0;

    // Reset keno system
    blockchainKenoGame.keno.selectedNumbers = [];
    blockchainKenoGame.keno.drawnNumbers = [];
    blockchainKenoGame.keno.matches = 0;
    blockchainKenoGame.keno.multiplier = 1.0;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('blockchainEvent').classList.add('hidden');
    document.getElementById('blockchainActions').classList.add('hidden');

    // Reset Keno grid
    const kenoNumbers = document.querySelectorAll('.keno-number');
    kenoNumbers.forEach(numberButton => {
        numberButton.className = 'keno-number w-7 h-7 bg-gray-700 hover:bg-green-600 text-white text-xs font-bold rounded transition-all duration-200';
    });

    // Reset contract execution display
    document.getElementById('executionStatus').textContent = 'Smart contract ready for deployment...';
    document.getElementById('selectedCount').textContent = '0/10';
    document.getElementById('matchCount').textContent = '0';

    // Reset deployment display
    document.getElementById('deploymentBar').style.width = '0%';
    document.getElementById('deploymentStatus').textContent = 'Ready';

    // Reset transaction display
    document.getElementById('transactionHash').querySelector('.text-sm').textContent = '0x...';
    document.getElementById('transactionHash').querySelector('.text-xs:last-child').textContent = 'Pending';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable deploy button
    document.getElementById('deployContract').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Blockchain ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Blockchain Keno Chain - Provably Fair Number Draws via Smart Contracts';

    // Reinitialize systems for next session
    initializeBlockchainSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBlockchainKenoGame();
});