// Quantum Entanglement Baccarat - Linked Table Outcomes
// Experimental Concept Implementation with Quantum Physics Theme
// Designed to maintain 3-5% player win rate with quantum entanglement mechanics

// Game state
let balance = 1000;

// Game state object with quantum entanglement system
let quantumBaccaratGame = {
    isPlaying: false,
    quantumTable: 'alpha', // alpha, beta, gamma, delta, omega
    entanglementLevel: 'basic', // basic, enhanced, advanced, quantum, superposition
    betAmount: 0,
    totalBet: 0,

    // Quantum entanglement system
    quantum: {
        entanglementStrength: 0.70, // 70% entanglement strength
        coherenceLevel: 0.75, // 75% quantum coherence
        superposition: 0.65, // 65% superposition state
        decoherence: 0.25, // 25% decoherence rate
        quantumTunneling: 0.60, // 60% tunneling probability
        waveFunction: 0.80, // 80% wave function stability
        quantumAdvantage: 1.0,
        entanglementHistory: [],
        quantumLevel: 'novice', // novice, apprentice, adept, master, quantum_lord
        quantumPoints: 0,
        linkedTables: [],
        observerEffect: 0.0
    },

    // Baccarat game state
    baccarat: {
        playerHand: [],
        bankerHand: [],
        playerValue: 0,
        bankerValue: 0,
        gamePhase: 'betting', // betting, dealing, revealing, finished
        betType: '', // player, banker, tie
        gameResult: '',
        naturalWin: false,
        quantumBonus: 1.0,
        entanglementBonus: 0.0,
        linkedOutcomes: [],
        tableCorrelation: 0.0,
        quantumInterference: false
    },

    // Quantum tables (linked outcomes)
    quantumTables: {
        alpha: {
            name: 'Alpha Table',
            entanglementRate: 0.70, // 70% entanglement rate
            coherence: 0.75, // 75% coherence
            quantumBonus: 0.25, // 25% quantum bonus
            linkedTables: ['beta', 'gamma']
        },
        beta: {
            name: 'Beta Table',
            entanglementRate: 0.75, // 75% entanglement rate
            coherence: 0.80, // 80% coherence
            quantumBonus: 0.30, // 30% quantum bonus
            linkedTables: ['alpha', 'delta']
        },
        gamma: {
            name: 'Gamma Table',
            entanglementRate: 0.80, // 80% entanglement rate
            coherence: 0.85, // 85% coherence
            quantumBonus: 0.35, // 35% quantum bonus
            linkedTables: ['alpha', 'omega']
        },
        delta: {
            name: 'Delta Table',
            entanglementRate: 0.85, // 85% entanglement rate
            coherence: 0.90, // 90% coherence
            quantumBonus: 0.40, // 40% quantum bonus
            linkedTables: ['beta', 'omega']
        },
        omega: {
            name: 'Omega Table',
            entanglementRate: 0.90, // 90% entanglement rate
            coherence: 0.95, // 95% coherence
            quantumBonus: 0.45, // 45% quantum bonus
            linkedTables: ['gamma', 'delta']
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        handsPlayed: 0,
        handsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        naturalWins: 0,
        entanglementEvents: 0,
        quantumTunnels: 0,
        superpositionStates: 0,
        quantumPoints: 0,
        tablesLinked: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Quantum tables with balanced entanglement requirements (3-5% win rate)
const QUANTUM_TABLES = {
    alpha: {
        name: 'Alpha Table',
        entanglementWeight: 0.25, // 25% entanglement influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        coherenceBonus: 0.20, // 20% coherence bonus
        quantumBonus: 0.15 // 15% quantum bonus
    },
    beta: {
        name: 'Beta Table',
        entanglementWeight: 0.30, // 30% entanglement influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        coherenceBonus: 0.25, // 25% coherence bonus
        quantumBonus: 0.20 // 20% quantum bonus
    },
    gamma: {
        name: 'Gamma Table',
        entanglementWeight: 0.35, // 35% entanglement influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        coherenceBonus: 0.30, // 30% coherence bonus
        quantumBonus: 0.25 // 25% quantum bonus
    },
    delta: {
        name: 'Delta Table',
        entanglementWeight: 0.40, // 40% entanglement influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        coherenceBonus: 0.35, // 35% coherence bonus
        quantumBonus: 0.30 // 30% quantum bonus
    },
    omega: {
        name: 'Omega Table',
        entanglementWeight: 0.45, // 45% entanglement influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.15, // Premium payouts
        coherenceBonus: 0.40, // 40% coherence bonus
        quantumBonus: 0.35 // 35% quantum bonus
    }
};

const ENTANGLEMENT_LEVELS = {
    basic: {
        name: 'Basic Entanglement',
        strength: 0.70, // 70% strength
        stabilityBonus: 1.10 // 10% stability boost
    },
    enhanced: {
        name: 'Enhanced Entanglement',
        strength: 0.75, // 75% strength
        stabilityBonus: 1.15 // 15% stability boost
    },
    advanced: {
        name: 'Advanced Entanglement',
        strength: 0.80, // 80% strength
        stabilityBonus: 1.20 // 20% stability boost
    },
    quantum: {
        name: 'Quantum Entanglement',
        strength: 0.85, // 85% strength
        stabilityBonus: 1.25 // 25% stability boost
    },
    superposition: {
        name: 'Superposition State',
        strength: 0.90, // 90% strength
        stabilityBonus: 1.30 // 30% stability boost
    }
};

// Improved payout table with quantum theme (3-5% win rate)
const QUANTUM_BACCARAT_PAYOUTS = {
    // Perfect quantum achievements (moderately reduced)
    QUANTUM_MASTER: 2500, // Reduced from 5000:1 but still excellent
    ENTANGLEMENT_GENIUS: 1800, // Reduced from 3600:1
    SUPERPOSITION_LORD: 1200, // Reduced from 2400:1
    QUANTUM_TUNNEL: 800, // Reduced from 1600:1

    // Baccarat payouts with quantum bonuses
    PLAYER_WIN: 100, // 1:1 payout
    BANKER_WIN: 95, // 1:1 minus 5% commission
    TIE_WIN: 800, // 8:1 payout
    NATURAL_WIN: 150, // 1.5:1 bonus for natural

    // Quantum bonuses (actually apply more often)
    ENTANGLEMENT_BONUS: 0.85, // 85% of displayed bonus (increased)
    COHERENCE_BONUS: 0.75, // 75% of displayed bonus (increased)
    SUPERPOSITION_BONUS: 0.65, // 65% of displayed bonus (increased)
    QUANTUM_TUNNEL_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// Standard deck of cards for Baccarat
const CARD_SUITS = ['♠', '♥', '♦', '♣'];
const CARD_RANKS = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadQuantumBaccaratGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">QUANTUM CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">QUANTUM TABLE</label>
                        <select id="quantumTable" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="alpha">Alpha Table</option>
                            <option value="beta">Beta Table</option>
                            <option value="gamma">Gamma Table</option>
                            <option value="delta">Delta Table</option>
                            <option value="omega">Omega Table</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ENTANGLEMENT LEVEL</label>
                        <select id="entanglementLevel" class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Entanglement</option>
                            <option value="enhanced">Enhanced Entanglement</option>
                            <option value="advanced">Advanced Entanglement</option>
                            <option value="quantum">Quantum Entanglement</option>
                            <option value="superposition">Superposition State</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-purple-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="entangleTables" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ENTANGLE TABLES
                    </button>

                    <div id="baccaratActions" class="space-y-2 hidden">
                        <div class="text-sm text-purple-400 mb-2">PLACE BET:</div>
                        <button id="betPlayer" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            BET PLAYER
                        </button>
                        <button id="betBanker" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            BET BANKER
                        </button>
                        <button id="betTie" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            BET TIE
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Quantum Points</div>
                        <div id="quantumPointsDisplay" class="text-lg font-bold text-purple-400">0</div>
                    </div>
                </div>

                <!-- Quantum Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">QUANTUM STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="entanglementStrength" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">ENTANGLEMENT: 70%</div>
                        </div>
                        <div id="coherenceLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">COHERENCE: 75%</div>
                        </div>
                        <div id="superposition" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">SUPERPOSITION: 65%</div>
                        </div>
                        <div id="quantumLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                    </div>
                </div>

                <!-- Linked Tables Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-purple-400">LINKED TABLES</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Entanglement Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Linked:</span>
                            <span class="text-green-400">2</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Correlation:</span>
                            <span class="text-blue-400">70%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Tunneling:</span>
                            <span class="text-purple-400">60%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Quantum Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Entanglement:</span>
                            <span class="text-purple-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Coherence:</span>
                            <span class="text-purple-400">75%</span>
                        </div>
                        <div class="text-xs text-purple-400 mt-2">*Entanglement improves odds</div>
                        <div class="text-xs text-purple-400">*Linked tables provide bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Quantum Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <!-- Quantum Baccarat Arena -->
                    <div id="quantumBaccaratArena" class="relative bg-gradient-to-br from-black via-purple-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Quantum Background -->
                        <div id="quantumBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="quantumGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#7c3aed;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#6d28d9;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="quantumPattern" width="60" height="60" patternUnits="userSpaceOnUse">
                                        <circle cx="30" cy="30" r="20" fill="none" stroke="#8b5cf6" stroke-width="2" opacity="0.4"/>
                                        <circle cx="30" cy="30" r="10" fill="none" stroke="#7c3aed" stroke-width="2" opacity="0.6"/>
                                        <circle cx="30" cy="30" r="5" fill="#6d28d9" opacity="0.8"/>
                                        <path d="M10,30 Q30,10 50,30 Q30,50 10,30" fill="none" stroke="#8b5cf6" stroke-width="1" opacity="0.3"/>
                                        <path d="M30,10 Q50,30 30,50 Q10,30 30,10" fill="none" stroke="#7c3aed" stroke-width="1" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#quantumPattern)" />
                                <circle id="quantumCore" cx="50%" cy="50%" r="25%" fill="url(#quantumGradient)" class="animate-pulse" />
                                <g id="quantumEffects">
                                    <!-- Quantum effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Baccarat Table -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">QUANTUM BACCARAT TABLE</div>
                                <div id="baccaratTable" class="bg-green-800/50 rounded-lg p-4 w-80 border-2 border-green-600">
                                    <!-- Banker Hand -->
                                    <div class="mb-4">
                                        <div class="text-xs text-white mb-2">BANKER</div>
                                        <div id="bankerHand" class="flex justify-center space-x-2 mb-2">
                                            <!-- Banker cards will appear here -->
                                        </div>
                                        <div id="bankerValue" class="text-sm text-white text-center">Value: 0</div>
                                    </div>

                                    <!-- Player Hand -->
                                    <div>
                                        <div class="text-xs text-purple-400 mb-2">PLAYER</div>
                                        <div id="playerHand" class="flex justify-center space-x-2 mb-2">
                                            <!-- Player cards will appear here -->
                                        </div>
                                        <div id="playerValue" class="text-sm text-purple-400 text-center">Value: 0</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Quantum-entangled baccarat experience</div>
                            </div>
                        </div>

                        <!-- Linked Tables Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">LINKED TABLES</div>
                                <div id="linkedTablesDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="entanglementStatus" class="text-lg text-white mb-3">Ready for quantum entanglement...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-purple-400 mb-1">ENTANGLED</div>
                                            <div id="entangledTables" class="text-xl font-bold text-white">0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-purple-400 mb-1">CORRELATION</div>
                                            <div id="tableCorrelation" class="text-xl font-bold text-green-400">70%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-purple-400 mb-1">COHERENCE</div>
                                            <div id="quantumCoherence" class="text-xl font-bold text-blue-400">75%</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Quantum table synchronization</div>
                            </div>
                        </div>

                        <!-- Quantum Entanglement Progress -->
                        <div id="entanglementProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-purple-400 mb-2">ENTANGLEMENT PROGRESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="entanglementBar" class="bg-gradient-to-r from-purple-400 to-pink-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Initializing</span>
                                    <span id="entanglementStatus">Ready</span>
                                    <span>Entangled</span>
                                </div>
                            </div>
                        </div>

                        <!-- Superposition State -->
                        <div id="superpositionState" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">SUPERPOSITION</div>
                                <div class="text-2xl font-bold text-white text-center">65%</div>
                                <div class="text-xs text-gray-400 mt-1">State</div>
                            </div>
                        </div>

                        <!-- Quantum Tunneling -->
                        <div id="quantumTunneling" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">TUNNELING</div>
                                <div class="text-2xl font-bold text-white text-center">60%</div>
                                <div class="text-xs text-gray-400 mt-1">Probability</div>
                            </div>
                        </div>

                        <!-- Wave Function -->
                        <div id="waveFunction" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">WAVE FUNC</div>
                                <div class="text-2xl font-bold text-white text-center">80%</div>
                                <div class="text-xs text-gray-400 mt-1">Stability</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Quantum tables ready...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="quantumEvent" class="text-sm font-bold text-purple-400 hidden animate-pulse">TABLES ENTANGLED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Quantum Entanglement Baccarat - Linked Table Outcomes</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Hands Played</div>
                <div id="handsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-purple-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-purple-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Natural Wins</div>
                <div id="naturalWins" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-purple-500/30 text-center">
                <div class="text-sm text-gray-400">Entanglement Events</div>
                <div id="entanglementEvents" class="text-xl font-bold text-pink-400">0</div>
            </div>
        </div>
    `;

    initializeQuantumBaccarat();
}

// Initialize the game
function initializeQuantumBaccarat() {
    document.getElementById('entangleTables').addEventListener('click', entangleQuantumTables);
    document.getElementById('betPlayer').addEventListener('click', () => placeBaccaratBet('player'));
    document.getElementById('betBanker').addEventListener('click', () => placeBaccaratBet('banker'));
    document.getElementById('betTie').addEventListener('click', () => placeBaccaratBet('tie'));

    // Initialize quantum systems
    initializeQuantumSystems();
    generateQuantumEffects();
    updateGameStats();
}

// Initialize quantum systems
function initializeQuantumSystems() {
    // Reset quantum system
    quantumBaccaratGame.quantum.entanglementStrength = Math.min(0.95, 0.70 + (quantumBaccaratGame.stats.quantumPoints * 0.01));
    quantumBaccaratGame.quantum.coherenceLevel = Math.min(0.95, 0.75 + (quantumBaccaratGame.stats.quantumPoints * 0.008));
    quantumBaccaratGame.quantum.superposition = Math.min(0.90, 0.65 + (quantumBaccaratGame.stats.quantumPoints * 0.01));
    quantumBaccaratGame.quantum.decoherence = Math.max(0.05, 0.25 - (quantumBaccaratGame.stats.quantumPoints * 0.005));
    quantumBaccaratGame.quantum.quantumTunneling = Math.min(0.85, 0.60 + (quantumBaccaratGame.stats.quantumPoints * 0.008));
    quantumBaccaratGame.quantum.waveFunction = Math.min(0.95, 0.80 + (quantumBaccaratGame.stats.quantumPoints * 0.006));
    quantumBaccaratGame.quantum.quantumAdvantage = 1.0;
    quantumBaccaratGame.quantum.entanglementHistory = [];
    quantumBaccaratGame.quantum.quantumLevel = calculateQuantumLevel();
    quantumBaccaratGame.quantum.quantumPoints = quantumBaccaratGame.stats.quantumPoints;
    quantumBaccaratGame.quantum.linkedTables = [];
    quantumBaccaratGame.quantum.observerEffect = 0.0;

    // Reset baccarat system
    quantumBaccaratGame.baccarat.playerHand = [];
    quantumBaccaratGame.baccarat.bankerHand = [];
    quantumBaccaratGame.baccarat.playerValue = 0;
    quantumBaccaratGame.baccarat.bankerValue = 0;
    quantumBaccaratGame.baccarat.gamePhase = 'betting';
    quantumBaccaratGame.baccarat.betType = '';
    quantumBaccaratGame.baccarat.gameResult = '';
    quantumBaccaratGame.baccarat.naturalWin = false;
    quantumBaccaratGame.baccarat.quantumBonus = 1.0;
    quantumBaccaratGame.baccarat.entanglementBonus = 0.0;
    quantumBaccaratGame.baccarat.linkedOutcomes = [];
    quantumBaccaratGame.baccarat.tableCorrelation = 0.0;
    quantumBaccaratGame.baccarat.quantumInterference = false;

    updateQuantumDisplay();
}

// Generate quantum effects
function generateQuantumEffects() {
    const container = document.getElementById('quantumEffects');
    container.innerHTML = '';

    // Create quantum entanglement visualization
    for (let i = 0; i < 8; i++) {
        const quantumParticle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        quantumParticle.setAttribute('cx', `${Math.random() * 100}%`);
        quantumParticle.setAttribute('cy', `${Math.random() * 100}%`);
        quantumParticle.setAttribute('r', `${Math.random() * 2 + 1}%`);
        quantumParticle.setAttribute('fill', '#8b5cf6');
        quantumParticle.setAttribute('opacity', '0.7');
        quantumParticle.classList.add('animate-pulse');
        quantumParticle.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(quantumParticle);

        // Add entanglement connections
        if (i > 0 && i % 2 === 0) {
            const entanglement = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevParticle = container.children[(i - 2) * 2];
            entanglement.setAttribute('x1', prevParticle.getAttribute('cx'));
            entanglement.setAttribute('y1', prevParticle.getAttribute('cy'));
            entanglement.setAttribute('x2', quantumParticle.getAttribute('cx'));
            entanglement.setAttribute('y2', quantumParticle.getAttribute('cy'));
            entanglement.setAttribute('stroke', '#7c3aed');
            entanglement.setAttribute('stroke-width', '2');
            entanglement.setAttribute('opacity', '0.5');
            entanglement.setAttribute('stroke-dasharray', '5,5');
            container.appendChild(entanglement);
        }
    }
}

// Calculate quantum level
function calculateQuantumLevel() {
    const points = quantumBaccaratGame.stats.quantumPoints;
    if (points >= 100) return 'quantum_lord';
    if (points >= 50) return 'master';
    if (points >= 25) return 'adept';
    if (points >= 10) return 'apprentice';
    return 'novice';
}

// Entangle quantum tables
function entangleQuantumTables() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    quantumBaccaratGame.isPlaying = true;
    quantumBaccaratGame.betAmount = betAmount;
    quantumBaccaratGame.totalBet = betAmount;
    quantumBaccaratGame.quantumTable = document.getElementById('quantumTable').value;
    quantumBaccaratGame.entanglementLevel = document.getElementById('entanglementLevel').value;

    // Start quantum entanglement
    startQuantumEntanglement();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('entangleTables').disabled = true;
    document.getElementById('gameStatus').textContent = 'Entangling quantum tables...';
}

// Start quantum entanglement
function startQuantumEntanglement() {
    const quantumTableData = quantumBaccaratGame.quantumTables[quantumBaccaratGame.quantumTable];
    const entanglementLevelData = ENTANGLEMENT_LEVELS[quantumBaccaratGame.entanglementLevel];

    // Update entanglement information
    document.getElementById('entanglementStatus').textContent = `Entangling with ${quantumTableData.name}...`;
    document.getElementById('entanglementStatus').textContent = 'Entangling...';

    // Simulate quantum entanglement process
    simulateQuantumEntanglement(quantumTableData, entanglementLevelData);

    // Update quantum status
    updateQuantumStatus();

    // Update visual effects
    updateQuantumDisplay();
    updateQuantumEffects();
}

// Simulate quantum entanglement process
function simulateQuantumEntanglement(quantumTableData, entanglementLevelData) {
    let progress = 0;
    const entanglementTime = 40; // 4 seconds entanglement time

    const entanglementInterval = setInterval(() => {
        progress += 100 / entanglementTime; // Update every 100ms

        // Update progress bar
        document.getElementById('entanglementBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 20) {
            document.getElementById('entanglementStatus').textContent = 'Initializing...';
        } else if (progress < 40) {
            document.getElementById('entanglementStatus').textContent = 'Synchronizing...';
        } else if (progress < 60) {
            document.getElementById('entanglementStatus').textContent = 'Entangling...';
        } else if (progress < 80) {
            document.getElementById('entanglementStatus').textContent = 'Stabilizing...';
        } else if (progress < 100) {
            document.getElementById('entanglementStatus').textContent = 'Coherence...';
        } else {
            document.getElementById('entanglementStatus').textContent = 'Entangled!';
            clearInterval(entanglementInterval);
            completeQuantumEntanglement(quantumTableData, entanglementLevelData);
        }

        // Update quantum states based on progress
        const superpositionLevel = Math.min(90, quantumBaccaratGame.quantum.superposition * 100 + progress * 0.2);
        document.getElementById('superpositionState').querySelector('.text-2xl').textContent = `${Math.floor(superpositionLevel)}%`;

    }, 100);
}

// Complete quantum entanglement
function completeQuantumEntanglement(quantumTableData, entanglementLevelData) {
    // Apply quantum bonuses
    const quantumBonus = calculateQuantumBonus();
    const entanglementBonus = quantumTableData.entanglementRate * 0.15; // Up to 15% bonus

    // Update linked tables
    quantumBaccaratGame.quantum.linkedTables = quantumTableData.linkedTables;
    quantumBaccaratGame.baccarat.entanglementBonus = quantumBonus + entanglementBonus;

    // Calculate table correlation
    quantumBaccaratGame.baccarat.tableCorrelation = quantumTableData.entanglementRate * quantumBaccaratGame.quantum.coherenceLevel;

    // Award quantum points
    const pointsEarned = Math.floor(quantumTableData.quantumBonus * 100);
    quantumBaccaratGame.stats.quantumPoints += pointsEarned;

    // Enable baccarat actions
    document.getElementById('baccaratActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Tables entangled! Place your bet.';

    // Update displays
    updateQuantumStatus();
    updateLinkedTablesDisplay();
}

// Calculate quantum bonus
function calculateQuantumBonus() {
    let bonus = 0;

    // Entanglement strength bonus
    bonus += quantumBaccaratGame.quantum.entanglementStrength * 0.15; // Up to 15% bonus

    // Coherence level bonus
    bonus += quantumBaccaratGame.quantum.coherenceLevel * 0.12; // Up to 12% bonus

    // Superposition bonus
    bonus += quantumBaccaratGame.quantum.superposition * 0.10; // Up to 10% bonus

    // Wave function stability bonus
    bonus += quantumBaccaratGame.quantum.waveFunction * 0.08; // Up to 8% bonus

    // Quantum tunneling bonus
    bonus += quantumBaccaratGame.quantum.quantumTunneling * 0.10; // Up to 10% bonus

    // Quantum level bonus
    const levelBonuses = {
        novice: 0.05,
        apprentice: 0.08,
        adept: 0.12,
        master: 0.15,
        quantum_lord: 0.20
    };
    bonus += levelBonuses[quantumBaccaratGame.quantum.quantumLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Place baccarat bet with quantum entanglement (3-5% win rate)
function placeBaccaratBet(betType) {
    if (!quantumBaccaratGame.isPlaying) return;

    quantumBaccaratGame.baccarat.betType = betType;
    quantumBaccaratGame.baccarat.gamePhase = 'dealing';

    document.getElementById('gameStatus').textContent = `Betting on ${betType.toUpperCase()}...`;

    // Calculate quantum influence on outcome
    const quantumInfluence = calculateQuantumInfluence();

    // Generate linked table outcomes
    const linkedOutcomes = generateLinkedTableOutcomes();

    // Deal baccarat cards with quantum influence
    const gameResult = dealQuantumBaccaratCards(quantumInfluence, linkedOutcomes);

    // Animate card dealing
    animateBaccaratDealing(gameResult);

    // Resolve after animation
    setTimeout(() => {
        resolveQuantumBaccarat(gameResult);
    }, 4000);
}

// Calculate quantum influence
function calculateQuantumInfluence() {
    const quantumTableData = QUANTUM_TABLES[quantumBaccaratGame.quantumTable];
    const quantumBonus = calculateQuantumBonus();
    const entanglementBonus = quantumBaccaratGame.quantum.entanglementStrength * 0.10; // Up to 10% bonus
    const coherenceBonus = quantumBaccaratGame.quantum.coherenceLevel * 0.08; // Up to 8% bonus
    const quantumPointsBonus = quantumBaccaratGame.stats.quantumPoints * 0.002; // Quantum points bonus

    return Math.min(0.35, quantumTableData.entanglementWeight + quantumBonus + entanglementBonus + coherenceBonus + quantumPointsBonus);
}

// Generate linked table outcomes
function generateLinkedTableOutcomes() {
    const quantumTableData = quantumBaccaratGame.quantumTables[quantumBaccaratGame.quantumTable];
    const linkedTables = quantumTableData.linkedTables;
    const outcomes = [];

    linkedTables.forEach(tableId => {
        const linkedTableData = quantumBaccaratGame.quantumTables[tableId];

        // Generate outcome for linked table
        const outcome = {
            tableId: tableId,
            result: Math.random() < 0.5 ? 'player' : (Math.random() < 0.5 ? 'banker' : 'tie'),
            correlation: linkedTableData.entanglementRate * quantumBaccaratGame.quantum.coherenceLevel,
            influence: linkedTableData.entanglementRate * 0.15 // Up to 15% influence
        };

        outcomes.push(outcome);
    });

    quantumBaccaratGame.baccarat.linkedOutcomes = outcomes;
    return outcomes;
}

// Deal quantum baccarat cards with entanglement influence (improved for 3-5% win rate)
function dealQuantumBaccaratCards(quantumInfluence, linkedOutcomes) {
    // Apply quantum influence to improve odds
    const adjustedOdds = 0.035 + quantumInfluence; // Base 3.5% + quantum influence

    // Generate player and banker hands
    const playerHand = [generateBaccaratCard(), generateBaccaratCard()];
    const bankerHand = [generateBaccaratCard(), generateBaccaratCard()];

    // Calculate initial values
    let playerValue = calculateBaccaratValue(playerHand);
    let bankerValue = calculateBaccaratValue(bankerHand);

    // Apply quantum influence (improved)
    if (Math.random() < quantumInfluence) {
        // Quantum entanglement tries to improve player's chances
        if (quantumBaccaratGame.baccarat.betType === 'player' && playerValue < 6) {
            // Try to improve player hand
            const newCard = generateBaccaratCard();
            if ((playerValue + newCard.value) % 10 > playerValue) {
                playerHand.push(newCard);
                playerValue = calculateBaccaratValue(playerHand);
            }
        } else if (quantumBaccaratGame.baccarat.betType === 'banker' && bankerValue < 6) {
            // Try to improve banker hand
            const newCard = generateBaccaratCard();
            if ((bankerValue + newCard.value) % 10 > bankerValue) {
                bankerHand.push(newCard);
                bankerValue = calculateBaccaratValue(bankerHand);
            }
        }
    }

    // Apply linked table correlation
    linkedOutcomes.forEach(outcome => {
        if (Math.random() < outcome.correlation) {
            // Linked table influences this outcome
            if (outcome.result === quantumBaccaratGame.baccarat.betType && Math.random() < outcome.influence) {
                // Positive correlation - try to help
                if (quantumBaccaratGame.baccarat.betType === 'player' && playerValue < bankerValue) {
                    playerValue = Math.min(9, playerValue + 1);
                } else if (quantumBaccaratGame.baccarat.betType === 'banker' && bankerValue < playerValue) {
                    bankerValue = Math.min(9, bankerValue + 1);
                }
            }
        }
    });

    // Determine game result
    let gameResult = '';
    let naturalWin = false;

    // Check for naturals (8 or 9)
    if (playerValue >= 8 || bankerValue >= 8) {
        naturalWin = true;
    }

    if (playerValue > bankerValue) {
        gameResult = 'player';
    } else if (bankerValue > playerValue) {
        gameResult = 'banker';
    } else {
        gameResult = 'tie';
    }

    // Apply quantum tunneling for rare outcomes
    if (Math.random() < quantumBaccaratGame.quantum.quantumTunneling * 0.1) {
        // Quantum tunneling can create unexpected outcomes
        if (quantumBaccaratGame.baccarat.betType === 'tie' && gameResult !== 'tie') {
            gameResult = 'tie';
            quantumBaccaratGame.stats.quantumTunnels++;
        }
    }

    return {
        playerHand: playerHand,
        bankerHand: bankerHand,
        playerValue: playerValue,
        bankerValue: bankerValue,
        gameResult: gameResult,
        naturalWin: naturalWin,
        quantumInfluence: quantumInfluence,
        linkedOutcomes: linkedOutcomes,
        quantumTunneling: quantumBaccaratGame.quantum.quantumTunneling
    };
}

// Generate baccarat card
function generateBaccaratCard() {
    const suit = CARD_SUITS[Math.floor(Math.random() * CARD_SUITS.length)];
    const rank = CARD_RANKS[Math.floor(Math.random() * CARD_RANKS.length)];

    let value = 0;
    if (rank === 'A') {
        value = 1;
    } else if (['J', 'Q', 'K'].includes(rank)) {
        value = 0;
    } else {
        value = parseInt(rank) || 10; // 10 has value 0 in baccarat
        if (value === 10) value = 0;
    }

    return {
        suit: suit,
        rank: rank,
        value: value
    };
}

// Calculate baccarat hand value
function calculateBaccaratValue(hand) {
    let total = 0;
    hand.forEach(card => {
        total += card.value;
    });
    return total % 10; // Baccarat uses modulo 10
}

// Animate baccarat dealing
function animateBaccaratDealing(results) {
    // Store results for display
    quantumBaccaratGame.baccarat.playerHand = results.playerHand;
    quantumBaccaratGame.baccarat.bankerHand = results.bankerHand;
    quantumBaccaratGame.baccarat.playerValue = results.playerValue;
    quantumBaccaratGame.baccarat.bankerValue = results.bankerValue;
    quantumBaccaratGame.baccarat.gameResult = results.gameResult;
    quantumBaccaratGame.baccarat.naturalWin = results.naturalWin;

    // Animate card dealing
    setTimeout(() => updateBaccaratDisplay(), 500);
    setTimeout(() => updateBaccaratDisplay(), 1500);
    setTimeout(() => updateBaccaratDisplay(), 2500);
    setTimeout(() => updateBaccaratDisplay(), 3500);

    // Update linked tables display
    setTimeout(() => updateLinkedTablesDisplay(), 2000);
}

// Resolve quantum baccarat with entanglement bonuses (3-5% win rate)
function resolveQuantumBaccarat(results) {
    const quantumTableData = QUANTUM_TABLES[quantumBaccaratGame.quantumTable];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.gameResult === quantumBaccaratGame.baccarat.betType;

    // Calculate base payout
    if (playerWon) {
        switch (quantumBaccaratGame.baccarat.betType) {
            case 'player':
                totalWinnings = Math.floor(quantumBaccaratGame.betAmount * (QUANTUM_BACCARAT_PAYOUTS.PLAYER_WIN / 100));
                resultMessage = 'Player wins!';
                break;
            case 'banker':
                totalWinnings = Math.floor(quantumBaccaratGame.betAmount * (QUANTUM_BACCARAT_PAYOUTS.BANKER_WIN / 100));
                resultMessage = 'Banker wins!';
                break;
            case 'tie':
                totalWinnings = Math.floor(quantumBaccaratGame.betAmount * (QUANTUM_BACCARAT_PAYOUTS.TIE_WIN / 100));
                resultMessage = 'Tie wins!';
                break;
        }

        // Natural win bonus
        if (results.naturalWin) {
            const naturalBonus = Math.floor(quantumBaccaratGame.betAmount * (QUANTUM_BACCARAT_PAYOUTS.NATURAL_WIN / 100));
            totalWinnings += naturalBonus;
            resultMessage += ' + Natural!';
            quantumBaccaratGame.stats.naturalWins++;
        }
    }

    // Apply quantum bonuses (actually work)
    if (quantumBaccaratGame.quantum.entanglementStrength >= 0.80 && totalWinnings > 0) {
        const entanglementBonus = Math.floor(totalWinnings * QUANTUM_BACCARAT_PAYOUTS.ENTANGLEMENT_BONUS);
        totalWinnings += entanglementBonus;
        resultMessage += ' + Entanglement Bonus!';
    }

    // Apply coherence bonus
    if (quantumBaccaratGame.quantum.coherenceLevel >= 0.85 && totalWinnings > 0) {
        const coherenceBonus = Math.floor(totalWinnings * QUANTUM_BACCARAT_PAYOUTS.COHERENCE_BONUS);
        totalWinnings += coherenceBonus;
        resultMessage += ' + Coherence Bonus!';
    }

    // Apply superposition bonus
    if (quantumBaccaratGame.quantum.superposition >= 0.80 && totalWinnings > 0) {
        const superpositionBonus = Math.floor(totalWinnings * QUANTUM_BACCARAT_PAYOUTS.SUPERPOSITION_BONUS);
        totalWinnings += superpositionBonus;
        resultMessage += ' + Superposition Bonus!';
        quantumBaccaratGame.stats.superpositionStates++;
    }

    // Quantum tunneling bonus
    if (results.quantumTunneling >= 0.75 && totalWinnings > 0) {
        const tunnelingBonus = Math.floor(totalWinnings * QUANTUM_BACCARAT_PAYOUTS.QUANTUM_TUNNEL_BONUS);
        totalWinnings += tunnelingBonus;
        resultMessage += ' + Quantum Tunnel!';
    }

    // Apply quantum table multiplier
    totalWinnings = Math.floor(totalWinnings * quantumTableData.payoutMultiplier);

    // Apply entanglement stability
    const entanglementLevelData = ENTANGLEMENT_LEVELS[quantumBaccaratGame.entanglementLevel];
    totalWinnings = Math.floor(totalWinnings * entanglementLevelData.stabilityBonus);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(quantumBaccaratGame.betAmount * 0.6); // 60% return
        resultMessage = 'Quantum entanglement reward';
    }

    // Award quantum points
    const pointsEarned = playerWon ? (results.naturalWin ? 5 : 3) : 1;
    quantumBaccaratGame.stats.quantumPoints += pointsEarned;

    // Update entanglement events
    if (results.linkedOutcomes.length > 0) {
        quantumBaccaratGame.stats.entanglementEvents++;
    }

    // Add winnings to balance
    balance += totalWinnings;
    quantumBaccaratGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterHand(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? `${quantumBaccaratGame.baccarat.betType.toUpperCase()} wins!` : 'Better luck next time';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Table: ${QUANTUM_TABLES[quantumBaccaratGame.quantumTable].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show quantum event
    document.getElementById('quantumEvent').classList.remove('hidden');
    document.getElementById('quantumEvent').textContent = results.naturalWin ? 'NATURAL WIN!' : 'QUANTUM RESOLVED!';
    document.getElementById('quantumEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update baccarat display
function updateBaccaratDisplay() {
    // Update player hand
    const playerHandElement = document.getElementById('playerHand');
    playerHandElement.innerHTML = '';
    quantumBaccaratGame.baccarat.playerHand.forEach(card => {
        const cardElement = document.createElement('div');
        cardElement.className = 'w-12 h-16 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
        cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        playerHandElement.appendChild(cardElement);
    });

    // Update banker hand
    const bankerHandElement = document.getElementById('bankerHand');
    bankerHandElement.innerHTML = '';
    quantumBaccaratGame.baccarat.bankerHand.forEach(card => {
        const cardElement = document.createElement('div');
        cardElement.className = 'w-12 h-16 bg-white rounded border-2 border-gray-300 flex flex-col items-center justify-center text-black text-xs font-bold';
        cardElement.innerHTML = `<div>${card.rank}</div><div>${card.suit}</div>`;
        bankerHandElement.appendChild(cardElement);
    });

    // Update hand values
    document.getElementById('playerValue').textContent = `Value: ${quantumBaccaratGame.baccarat.playerValue}`;
    document.getElementById('bankerValue').textContent = `Value: ${quantumBaccaratGame.baccarat.bankerValue}`;
}

// Update quantum display
function updateQuantumDisplay() {
    updateQuantumStatus();
    updateLinkedTablesDisplay();
}

// Update quantum status
function updateQuantumStatus() {
    document.getElementById('entanglementStrength').innerHTML =
        `<div class="text-purple-400 font-bold">ENTANGLEMENT: ${Math.floor(quantumBaccaratGame.quantum.entanglementStrength * 100)}%</div>`;
    document.getElementById('coherenceLevel').innerHTML =
        `<div class="text-blue-400 font-bold">COHERENCE: ${Math.floor(quantumBaccaratGame.quantum.coherenceLevel * 100)}%</div>`;
    document.getElementById('superposition').innerHTML =
        `<div class="text-green-400 font-bold">SUPERPOSITION: ${Math.floor(quantumBaccaratGame.quantum.superposition * 100)}%</div>`;
    document.getElementById('quantumLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${quantumBaccaratGame.quantum.quantumLevel.toUpperCase()}</div>`;

    document.getElementById('superpositionState').querySelector('.text-2xl').textContent =
        `${Math.floor(quantumBaccaratGame.quantum.superposition * 100)}%`;

    document.getElementById('quantumTunneling').querySelector('.text-2xl').textContent =
        `${Math.floor(quantumBaccaratGame.quantum.quantumTunneling * 100)}%`;

    document.getElementById('waveFunction').querySelector('.text-2xl').textContent =
        `${Math.floor(quantumBaccaratGame.quantum.waveFunction * 100)}%`;
}

// Update linked tables display
function updateLinkedTablesDisplay() {
    const quantumTableData = quantumBaccaratGame.quantumTables[quantumBaccaratGame.quantumTable];

    document.getElementById('entangledTables').textContent = quantumTableData.linkedTables.length;
    document.getElementById('tableCorrelation').textContent = `${Math.floor(quantumBaccaratGame.baccarat.tableCorrelation * 100)}%`;
    document.getElementById('quantumCoherence').textContent = `${Math.floor(quantumBaccaratGame.quantum.coherenceLevel * 100)}%`;
}

// Update quantum effects
function updateQuantumEffects() {
    // Update quantum effects based on entanglement strength
    const entanglementStrength = quantumBaccaratGame.quantum.entanglementStrength;
    const effects = document.querySelectorAll('#quantumEffects circle');

    effects.forEach((effect, index) => {
        if (entanglementStrength >= 0.85) {
            effect.setAttribute('opacity', '0.9');
            effect.setAttribute('fill', '#8b5cf6'); // Purple for high entanglement
        } else if (entanglementStrength >= 0.75) {
            effect.setAttribute('opacity', '0.7');
            effect.setAttribute('fill', '#7c3aed'); // Violet for good entanglement
        } else if (entanglementStrength >= 0.65) {
            effect.setAttribute('opacity', '0.5');
            effect.setAttribute('fill', '#6d28d9'); // Indigo for medium entanglement
        } else {
            effect.setAttribute('opacity', '0.3');
            effect.setAttribute('fill', '#5b21b6'); // Dark purple for low entanglement
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${quantumBaccaratGame.betAmount}`;
    document.getElementById('quantumPointsDisplay').textContent = quantumBaccaratGame.stats.quantumPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('handsPlayed').textContent = quantumBaccaratGame.stats.handsPlayed;
    document.getElementById('winRate').textContent = `${quantumBaccaratGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${quantumBaccaratGame.stats.totalWagered}`;
    document.getElementById('naturalWins').textContent = quantumBaccaratGame.stats.naturalWins;
    document.getElementById('entanglementEvents').textContent = quantumBaccaratGame.stats.entanglementEvents;

    const netResult = quantumBaccaratGame.stats.totalWon - quantumBaccaratGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-purple-400' : 'text-red-400'}`;
}

// Update stats after hand
function updateGameStatsAfterHand(won, winnings) {
    quantumBaccaratGame.stats.handsPlayed++;
    quantumBaccaratGame.stats.totalWagered += quantumBaccaratGame.betAmount;
    quantumBaccaratGame.stats.totalWon += winnings;

    if (won) {
        quantumBaccaratGame.stats.handsWon++;
        quantumBaccaratGame.stats.currentStreak++;
        quantumBaccaratGame.streakData.currentWinStreak++;
        quantumBaccaratGame.streakData.currentLossStreak = 0;

        if (quantumBaccaratGame.streakData.currentWinStreak > quantumBaccaratGame.streakData.longestWinStreak) {
            quantumBaccaratGame.streakData.longestWinStreak = quantumBaccaratGame.streakData.currentWinStreak;
        }

        if (winnings > quantumBaccaratGame.stats.biggestWin) {
            quantumBaccaratGame.stats.biggestWin = winnings;
        }
    } else {
        quantumBaccaratGame.stats.currentStreak = 0;
        quantumBaccaratGame.streakData.currentWinStreak = 0;
        quantumBaccaratGame.streakData.currentLossStreak++;

        if (quantumBaccaratGame.streakData.currentLossStreak > quantumBaccaratGame.streakData.longestLossStreak) {
            quantumBaccaratGame.streakData.longestLossStreak = quantumBaccaratGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to quantum mechanics)
    quantumBaccaratGame.stats.winRate = (quantumBaccaratGame.stats.handsWon / quantumBaccaratGame.stats.handsPlayed) * 100;

    updateGameStats();
}

// Reset game for next quantum session
function resetGame() {
    quantumBaccaratGame.isPlaying = false;
    quantumBaccaratGame.betAmount = 0;
    quantumBaccaratGame.totalBet = 0;
    quantumBaccaratGame.gameResult = '';
    quantumBaccaratGame.totalWin = 0;

    // Reset baccarat system
    quantumBaccaratGame.baccarat.playerHand = [];
    quantumBaccaratGame.baccarat.bankerHand = [];
    quantumBaccaratGame.baccarat.playerValue = 0;
    quantumBaccaratGame.baccarat.bankerValue = 0;
    quantumBaccaratGame.baccarat.gamePhase = 'betting';
    quantumBaccaratGame.baccarat.betType = '';
    quantumBaccaratGame.baccarat.gameResult = '';
    quantumBaccaratGame.baccarat.naturalWin = false;
    quantumBaccaratGame.baccarat.linkedOutcomes = [];

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('quantumEvent').classList.add('hidden');
    document.getElementById('baccaratActions').classList.add('hidden');

    // Reset baccarat table
    document.getElementById('playerHand').innerHTML = '';
    document.getElementById('bankerHand').innerHTML = '';
    document.getElementById('playerValue').textContent = 'Value: 0';
    document.getElementById('bankerValue').textContent = 'Value: 0';

    // Reset quantum display
    document.getElementById('entanglementStatus').textContent = 'Ready for quantum entanglement...';
    document.getElementById('entanglementBar').style.width = '0%';
    document.getElementById('entanglementStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable entangle button
    document.getElementById('entangleTables').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Quantum tables ready...';
    document.getElementById('gameMessage').textContent = 'Welcome to Quantum Entanglement Baccarat - Linked Table Outcomes';

    // Reinitialize systems for next session
    initializeQuantumSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadQuantumBaccaratGame();
});