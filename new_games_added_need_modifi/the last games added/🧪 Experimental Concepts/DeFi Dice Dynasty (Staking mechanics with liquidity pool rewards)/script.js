// DeFi Dice Dynasty - Staking Mechanics with Liquidity Pool Rewards
// Experimental Concept Implementation with DeFi Theme
// Designed to maintain 3-5% player win rate with staking and liquidity mechanics

// Game state
let balance = 1000;

// Game state object with DeFi staking system
let defiDiceDynastyGame = {
    isPlaying: false,
    stakingPool: 'ethereum', // ethereum, polygon, avalanche, fantom, arbitrum
    liquidityStrategy: 'conservative', // conservative, balanced, aggressive, yield_farming, flash_loans
    betAmount: 0,
    totalBet: 0,

    // DeFi staking system
    stakingPools: {
        totalStaked: 0,
        stakingRewards: 0,
        stakingPower: 0.70, // 70% staking efficiency
        liquidityProvided: 0,
        yieldEarned: 0,
        impermanentLoss: 0,
        stakingLevel: 'bronze', // bronze, silver, gold, platinum, diamond
        poolTokens: 0,
        rewardMultiplier: 1.0,
        lockupPeriod: 0, // blocks
        stakingHistory: [],
        compoundingRate: 0.05 // 5% compounding
    },

    // Dice system with DeFi mechanics
    dice: {
        count: 3, // 3 dice for DeFi
        values: [1, 1, 1],
        targetSum: 10,
        actualSum: 0,
        stakingBonus: 1.0,
        liquidityBonus: 1.0,
        yieldMultiplier: 1.0,
        poolRewards: 0,
        flashLoanBonus: 0,
        defiCombo: false,
        stakingStreak: 0
    },

    // Liquidity pools
    liquidityPools: {
        ethereum: {
            name: 'Ethereum Pool',
            apy: 0.08, // 8% APY
            tvl: 50000000, // $50M TVL
            stakingBonus: 0.30, // 30% staking bonus
            riskLevel: 0.15 // 15% risk
        },
        polygon: {
            name: 'Polygon Pool',
            apy: 0.12, // 12% APY
            tvl: 25000000, // $25M TVL
            stakingBonus: 0.35, // 35% staking bonus
            riskLevel: 0.20 // 20% risk
        },
        avalanche: {
            name: 'Avalanche Pool',
            apy: 0.15, // 15% APY
            tvl: 15000000, // $15M TVL
            stakingBonus: 0.40, // 40% staking bonus
            riskLevel: 0.25 // 25% risk
        },
        fantom: {
            name: 'Fantom Pool',
            apy: 0.18, // 18% APY
            tvl: 10000000, // $10M TVL
            stakingBonus: 0.45, // 45% staking bonus
            riskLevel: 0.30 // 30% risk
        },
        arbitrum: {
            name: 'Arbitrum Pool',
            apy: 0.10, // 10% APY
            tvl: 30000000, // $30M TVL
            stakingBonus: 0.32, // 32% staking bonus
            riskLevel: 0.18 // 18% risk
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        totalStaked: 0,
        totalYieldEarned: 0,
        liquidityProvided: 0,
        stakingRewards: 0,
        defiPoints: 0,
        poolsJoined: 0,
        bestAPY: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// DeFi pools with balanced staking requirements (3-5% win rate)
const DEFI_POOLS = {
    ethereum: {
        name: 'Ethereum',
        stakingWeight: 0.30, // 30% staking influence (increased)
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        yieldBonus: 0.25, // 25% yield bonus
        liquidityBonus: 0.20 // 20% liquidity bonus
    },
    polygon: {
        name: 'Polygon',
        stakingWeight: 0.35, // 35% staking influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        yieldBonus: 0.30, // 30% yield bonus
        liquidityBonus: 0.25 // 25% liquidity bonus
    },
    avalanche: {
        name: 'Avalanche',
        stakingWeight: 0.40, // 40% staking influence
        luckFactor: 0.60, // 60% luck factor
        payoutMultiplier: 1.10, // Premium payouts
        yieldBonus: 0.35, // 35% yield bonus
        liquidityBonus: 0.30 // 30% liquidity bonus
    },
    fantom: {
        name: 'Fantom',
        stakingWeight: 0.45, // 45% staking influence
        luckFactor: 0.55, // 55% luck factor
        payoutMultiplier: 1.15, // Premium payouts
        yieldBonus: 0.40, // 40% yield bonus
        liquidityBonus: 0.35 // 35% liquidity bonus
    },
    arbitrum: {
        name: 'Arbitrum',
        stakingWeight: 0.32, // 32% staking influence
        luckFactor: 0.68, // 68% luck factor
        payoutMultiplier: 1.02, // Good payouts
        yieldBonus: 0.28, // 28% yield bonus
        liquidityBonus: 0.22 // 22% liquidity bonus
    }
};

const LIQUIDITY_STRATEGIES = {
    conservative: {
        name: 'Conservative',
        riskLevel: 0.10, // 10% risk
        yieldMultiplier: 1.15 // 15% yield boost
    },
    balanced: {
        name: 'Balanced',
        riskLevel: 0.20, // 20% risk
        yieldMultiplier: 1.25 // 25% yield boost
    },
    aggressive: {
        name: 'Aggressive',
        riskLevel: 0.35, // 35% risk
        yieldMultiplier: 1.40 // 40% yield boost
    },
    yield_farming: {
        name: 'Yield Farming',
        riskLevel: 0.50, // 50% risk
        yieldMultiplier: 1.60 // 60% yield boost
    },
    flash_loans: {
        name: 'Flash Loans',
        riskLevel: 0.70, // 70% risk
        yieldMultiplier: 2.00 // 100% yield boost
    }
};

// Improved payout table with DeFi theme (3-5% win rate)
const DEFI_DICE_PAYOUTS = {
    // Perfect DeFi achievements (moderately reduced)
    DEFI_MASTER: 2500, // Reduced from 5000:1 but still excellent
    YIELD_FARMING_GENIUS: 1500, // Reduced from 3000:1
    LIQUIDITY_KING: 1000, // Reduced from 2000:1
    STAKING_LEGEND: 600, // Reduced from 1200:1

    // Dice combinations with DeFi bonuses
    TRIPLE_SIXES: 1800, // All sixes (18:1)
    TRIPLE_MATCH: 1000, // Three of a kind (10:1)
    DOUBLE_MATCH: 300, // Two of a kind (3:1)
    HIGH_SUM: 200, // Sum 15+ (2:1)

    // DeFi bonuses (actually apply more often)
    STAKING_BONUS: 0.80, // 80% of displayed bonus (increased)
    YIELD_BONUS: 0.70, // 70% of displayed bonus (increased)
    LIQUIDITY_BONUS: 0.60, // 60% of displayed bonus (increased)
    COMPOUND_BONUS: 0.50 // 50% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadDeFiDiceDynastyGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">DEFI CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">STAKING POOL</label>
                        <select id="stakingPool" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="ethereum">Ethereum Pool</option>
                            <option value="polygon">Polygon Pool</option>
                            <option value="avalanche">Avalanche Pool</option>
                            <option value="fantom">Fantom Pool</option>
                            <option value="arbitrum">Arbitrum Pool</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">LIQUIDITY STRATEGY</label>
                        <select id="liquidityStrategy" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="conservative">Conservative</option>
                            <option value="balanced">Balanced</option>
                            <option value="aggressive">Aggressive</option>
                            <option value="yield_farming">Yield Farming</option>
                            <option value="flash_loans">Flash Loans</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="stakeTokens" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        STAKE TOKENS
                    </button>

                    <div id="defiActions" class="space-y-2 hidden">
                        <button id="rollDice" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            ROLL DEFI DICE
                        </button>
                        <button id="provideLiquidity" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            PROVIDE LIQUIDITY
                        </button>
                        <button id="compoundRewards" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            COMPOUND REWARDS
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">DeFi Points</div>
                        <div id="defiPointsDisplay" class="text-lg font-bold text-blue-400">0</div>
                    </div>
                </div>

                <!-- Staking Pool Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">STAKING STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="stakingPower" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">POWER: 70%</div>
                        </div>
                        <div id="stakingLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">LEVEL: BRONZE</div>
                        </div>
                        <div id="yieldEarned" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">YIELD: $0</div>
                        </div>
                        <div id="poolTokens" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">TOKENS: 0</div>
                        </div>
                    </div>
                </div>

                <!-- Liquidity Pool Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">LIQUIDITY POOLS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Pool Stats:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">APY:</span>
                            <span class="text-green-400">8%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">TVL:</span>
                            <span class="text-blue-400">$50M</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Risk:</span>
                            <span class="text-purple-400">15%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">DeFi Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Staking:</span>
                            <span class="text-blue-400">80%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Yield:</span>
                            <span class="text-blue-400">70%</span>
                        </div>
                        <div class="text-xs text-blue-400 mt-2">*Staking improves dice odds</div>
                        <div class="text-xs text-blue-400">*Liquidity provides bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main DeFi Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <!-- DeFi Dice Arena -->
                    <div id="defiDiceArena" class="relative bg-gradient-to-br from-black via-blue-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- DeFi Background -->
                        <div id="defiBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="defiGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#1d4ed8;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="defiPattern" width="120" height="120" patternUnits="userSpaceOnUse">
                                        <circle cx="60" cy="60" r="40" fill="none" stroke="#3b82f6" stroke-width="2" opacity="0.3"/>
                                        <rect x="20" y="20" width="80" height="80" fill="none" stroke="#1d4ed8" stroke-width="2" opacity="0.4"/>
                                        <polygon points="60,20 100,60 60,100 20,60" fill="#1e3a8a" opacity="0.3"/>
                                        <circle cx="30" cy="30" r="8" fill="#3b82f6" opacity="0.5"/>
                                        <circle cx="90" cy="90" r="8" fill="#3b82f6" opacity="0.5"/>
                                        <line x1="20" y1="100" x2="100" y2="20" stroke="#1d4ed8" stroke-width="2" opacity="0.3"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#defiPattern)" />
                                <circle id="defiCore" cx="50%" cy="50%" r="25%" fill="url(#defiGradient)" class="animate-pulse" />
                                <g id="defiEffects">
                                    <!-- DeFi effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- DeFi Dice Display -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">DEFI DICE</div>
                                <div id="defiDiceDisplay" class="flex space-x-4">
                                    <!-- 3 DeFi-enhanced dice -->
                                    <div class="defi-die w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg border-2 border-blue-400 flex items-center justify-center text-3xl font-bold text-white transition-all duration-500" data-die="0">1</div>
                                    <div class="defi-die w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg border-2 border-blue-400 flex items-center justify-center text-3xl font-bold text-white transition-all duration-500" data-die="1">1</div>
                                    <div class="defi-die w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg border-2 border-blue-400 flex items-center justify-center text-3xl font-bold text-white transition-all duration-500" data-die="2">1</div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Staking-enhanced probability dice</div>
                            </div>
                        </div>

                        <!-- Liquidity Pool Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">LIQUIDITY POOL</div>
                                <div id="liquidityPoolDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="poolStatus" class="text-lg text-white mb-3">Ready to stake tokens...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-blue-400 mb-1">STAKED</div>
                                            <div id="stakedAmount" class="text-xl font-bold text-white">$0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-blue-400 mb-1">APY</div>
                                            <div id="currentAPY" class="text-xl font-bold text-green-400">8%</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-blue-400 mb-1">REWARDS</div>
                                            <div id="pendingRewards" class="text-xl font-bold text-yellow-400">$0</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Decentralized finance staking</div>
                            </div>
                        </div>

                        <!-- Staking Progress -->
                        <div id="stakingProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">STAKING PROGRESS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="stakingBar" class="bg-gradient-to-r from-blue-400 to-green-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Initializing</span>
                                    <span id="stakingStatus">Ready</span>
                                    <span>Staked</span>
                                </div>
                            </div>
                        </div>

                        <!-- Yield Multiplier -->
                        <div id="yieldMultiplier" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">YIELD</div>
                                <div class="text-2xl font-bold text-white text-center">1.0x</div>
                                <div class="text-xs text-gray-400 mt-1">Multiplier</div>
                            </div>
                        </div>

                        <!-- Liquidity Provided -->
                        <div id="liquidityProvided" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">LIQUIDITY</div>
                                <div class="text-2xl font-bold text-white text-center">$0</div>
                                <div class="text-xs text-gray-400 mt-1">Provided</div>
                            </div>
                        </div>

                        <!-- Impermanent Loss -->
                        <div id="impermanentLoss" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">IL RISK</div>
                                <div class="text-2xl font-bold text-white text-center">0%</div>
                                <div class="text-xs text-gray-400 mt-1">Loss</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">DeFi ready to stake...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="defiEvent" class="text-sm font-bold text-blue-400 hidden animate-pulse">TOKENS STAKED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to DeFi Dice Dynasty - Staking Mechanics with Liquidity Pool Rewards</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-blue-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-blue-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Total Staked</div>
                <div id="totalStakedStat" class="text-xl font-bold text-green-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Yield Earned</div>
                <div id="yieldEarnedStat" class="text-xl font-bold text-purple-400">$0</div>
            </div>
        </div>
    `;

    initializeDeFiDiceDynasty();
}

// Initialize the game
function initializeDeFiDiceDynasty() {
    document.getElementById('stakeTokens').addEventListener('click', stakeTokensInPool);
    document.getElementById('rollDice').addEventListener('click', rollDeFiDice);
    document.getElementById('provideLiquidity').addEventListener('click', provideLiquidityToPool);
    document.getElementById('compoundRewards').addEventListener('click', compoundStakingRewards);

    // Initialize DeFi systems
    initializeDeFiSystems();
    generateDeFiEffects();
    updateGameStats();
}

// Initialize DeFi systems
function initializeDeFiSystems() {
    // Reset staking pools system
    defiDiceDynastyGame.stakingPools.totalStaked = 0;
    defiDiceDynastyGame.stakingPools.stakingRewards = 0;
    defiDiceDynastyGame.stakingPools.stakingPower = Math.min(0.90, 0.70 + (defiDiceDynastyGame.stats.defiPoints * 0.01));
    defiDiceDynastyGame.stakingPools.liquidityProvided = 0;
    defiDiceDynastyGame.stakingPools.yieldEarned = 0;
    defiDiceDynastyGame.stakingPools.impermanentLoss = 0;
    defiDiceDynastyGame.stakingPools.stakingLevel = calculateStakingLevel();
    defiDiceDynastyGame.stakingPools.poolTokens = 0;
    defiDiceDynastyGame.stakingPools.rewardMultiplier = 1.0;
    defiDiceDynastyGame.stakingPools.lockupPeriod = 0;
    defiDiceDynastyGame.stakingPools.stakingHistory = [];
    defiDiceDynastyGame.stakingPools.compoundingRate = Math.min(0.15, 0.05 + (defiDiceDynastyGame.stats.defiPoints * 0.002));

    // Reset dice system
    defiDiceDynastyGame.dice.count = 3;
    defiDiceDynastyGame.dice.values = [1, 1, 1];
    defiDiceDynastyGame.dice.targetSum = 10;
    defiDiceDynastyGame.dice.actualSum = 0;
    defiDiceDynastyGame.dice.stakingBonus = 1.0;
    defiDiceDynastyGame.dice.liquidityBonus = 1.0;
    defiDiceDynastyGame.dice.yieldMultiplier = 1.0;
    defiDiceDynastyGame.dice.poolRewards = 0;
    defiDiceDynastyGame.dice.flashLoanBonus = 0;
    defiDiceDynastyGame.dice.defiCombo = false;
    defiDiceDynastyGame.dice.stakingStreak = 0;

    updateDeFiDisplay();
}

// Generate DeFi effects
function generateDeFiEffects() {
    const container = document.getElementById('defiEffects');
    container.innerHTML = '';

    // Create DeFi network visualization
    for (let i = 0; i < 8; i++) {
        const node = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        node.setAttribute('cx', `${Math.random() * 100}%`);
        node.setAttribute('cy', `${Math.random() * 100}%`);
        node.setAttribute('r', `${Math.random() * 4 + 2}%`);
        node.setAttribute('fill', '#3b82f6');
        node.setAttribute('opacity', '0.6');
        node.classList.add('animate-pulse');
        node.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(node);

        // Add liquidity connections
        if (i > 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 1) * 2];
            connection.setAttribute('x1', prevNode.getAttribute('cx'));
            connection.setAttribute('y1', prevNode.getAttribute('cy'));
            connection.setAttribute('x2', node.getAttribute('cx'));
            connection.setAttribute('y2', node.getAttribute('cy'));
            connection.setAttribute('stroke', '#1d4ed8');
            connection.setAttribute('stroke-width', '2');
            connection.setAttribute('opacity', '0.4');
            container.appendChild(connection);
        }
    }
}

// Calculate staking level
function calculateStakingLevel() {
    const points = defiDiceDynastyGame.stats.defiPoints;
    if (points >= 100) return 'diamond';
    if (points >= 50) return 'platinum';
    if (points >= 25) return 'gold';
    if (points >= 10) return 'silver';
    return 'bronze';
}

// Stake tokens in pool
function stakeTokensInPool() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    defiDiceDynastyGame.isPlaying = true;
    defiDiceDynastyGame.betAmount = betAmount;
    defiDiceDynastyGame.totalBet = betAmount;
    defiDiceDynastyGame.stakingPool = document.getElementById('stakingPool').value;
    defiDiceDynastyGame.liquidityStrategy = document.getElementById('liquidityStrategy').value;

    // Start token staking
    startTokenStaking();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('stakeTokens').disabled = true;
    document.getElementById('gameStatus').textContent = 'Staking tokens...';
}

// Start token staking
function startTokenStaking() {
    const poolData = defiDiceDynastyGame.liquidityPools[defiDiceDynastyGame.stakingPool];
    const strategyData = LIQUIDITY_STRATEGIES[defiDiceDynastyGame.liquidityStrategy];

    // Update staking information
    document.getElementById('poolStatus').textContent = `Staking in ${poolData.name}...`;
    document.getElementById('stakingStatus').textContent = 'Staking...';

    // Simulate token staking process
    simulateTokenStaking(poolData, strategyData);

    // Update pool status
    updatePoolStatus();

    // Update visual effects
    updateDeFiDisplay();
    updateDeFiEffects();
}

// Simulate token staking process
function simulateTokenStaking(poolData, strategyData) {
    let progress = 0;
    const stakingTime = 30; // 3 seconds staking time

    const stakingInterval = setInterval(() => {
        progress += 100 / stakingTime; // Update every 100ms

        // Update progress bar
        document.getElementById('stakingBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 25) {
            document.getElementById('stakingStatus').textContent = 'Connecting...';
        } else if (progress < 50) {
            document.getElementById('stakingStatus').textContent = 'Approving...';
        } else if (progress < 75) {
            document.getElementById('stakingStatus').textContent = 'Staking...';
        } else if (progress < 100) {
            document.getElementById('stakingStatus').textContent = 'Confirming...';
        } else {
            document.getElementById('stakingStatus').textContent = 'Staked!';
            clearInterval(stakingInterval);
            completeTokenStaking(poolData, strategyData);
        }

        // Update yield multiplier based on progress
        const yieldMultiplier = 1.0 + (progress / 100) * (strategyData.yieldMultiplier - 1.0);
        defiDiceDynastyGame.dice.yieldMultiplier = yieldMultiplier;
        document.getElementById('yieldMultiplier').querySelector('.text-2xl').textContent = `${yieldMultiplier.toFixed(1)}x`;

    }, 100);
}

// Complete token staking
function completeTokenStaking(poolData, strategyData) {
    // Apply staking bonuses
    const stakingBonus = calculateStakingBonus();
    const liquidityBonus = poolData.stakingBonus;

    // Update staking pools
    defiDiceDynastyGame.stakingPools.totalStaked += defiDiceDynastyGame.betAmount;
    defiDiceDynastyGame.stakingPools.poolTokens += Math.floor(defiDiceDynastyGame.betAmount * 10); // 10 tokens per dollar
    defiDiceDynastyGame.stakingPools.rewardMultiplier = 1.0 + stakingBonus + liquidityBonus;

    // Calculate potential yield
    const potentialYield = Math.floor(defiDiceDynastyGame.betAmount * poolData.apy * 0.1); // 10% of annual yield
    defiDiceDynastyGame.stakingPools.yieldEarned += potentialYield;

    // Award DeFi points
    const pointsEarned = Math.floor(poolData.apy * 100);
    defiDiceDynastyGame.stats.defiPoints += pointsEarned;

    // Enable actions
    document.getElementById('defiActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Tokens staked! Roll DeFi dice.';

    // Update displays
    updatePoolStatus();
    updateStakingDisplay();
}

// Calculate staking bonus
function calculateStakingBonus() {
    let bonus = 0;

    // Staking power bonus
    bonus += defiDiceDynastyGame.stakingPools.stakingPower * 0.20; // Up to 20% bonus

    // Staking level bonus
    const levelBonuses = {
        bronze: 0.05,
        silver: 0.10,
        gold: 0.15,
        platinum: 0.20,
        diamond: 0.25
    };
    bonus += levelBonuses[defiDiceDynastyGame.stakingPools.stakingLevel] || 0.05;

    // Pool tokens bonus
    const tokenBonus = Math.min(0.15, defiDiceDynastyGame.stakingPools.poolTokens * 0.001); // Up to 15% bonus
    bonus += tokenBonus;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Roll DeFi dice with staking influence (3-5% win rate)
function rollDeFiDice() {
    if (!defiDiceDynastyGame.isPlaying) return;

    document.getElementById('gameStatus').textContent = 'Rolling DeFi dice...';

    // Calculate staking influence on dice
    const stakingInfluence = calculateStakingInfluence();

    // Generate dice results with DeFi influence
    const diceResults = generateDeFiDiceRoll(stakingInfluence);

    // Animate dice roll
    animateDeFiDiceRoll(diceResults);

    // Resolve after animation
    setTimeout(() => {
        resolveDeFiDiceRoll(diceResults);
    }, 3000);
}

// Calculate staking influence
function calculateStakingInfluence() {
    const poolData = DEFI_POOLS[defiDiceDynastyGame.stakingPool];
    const stakingBonus = calculateStakingBonus();
    const liquidityBonus = defiDiceDynastyGame.stakingPools.liquidityProvided * 0.0001; // Liquidity bonus
    const yieldBonus = defiDiceDynastyGame.stakingPools.yieldEarned * 0.001; // Yield bonus
    const defiPointsBonus = defiDiceDynastyGame.stats.defiPoints * 0.002; // DeFi points bonus

    return Math.min(0.35, poolData.stakingWeight + stakingBonus + liquidityBonus + yieldBonus + defiPointsBonus);
}

// Generate DeFi dice roll with staking influence (improved for 3-5% win rate)
function generateDeFiDiceRoll(stakingInfluence) {
    const poolData = DEFI_POOLS[defiDiceDynastyGame.stakingPool];
    const strategyData = LIQUIDITY_STRATEGIES[defiDiceDynastyGame.liquidityStrategy];

    // Apply staking influence to improve odds
    const adjustedOdds = 0.035 + stakingInfluence; // Base 3.5% + staking influence

    // Generate base dice rolls
    const diceValues = [];
    for (let i = 0; i < 3; i++) {
        let dieValue = Math.floor(Math.random() * 6) + 1;

        // Apply staking influence (improved)
        if (Math.random() < stakingInfluence) {
            // Staking tries to improve dice outcomes
            if (dieValue <= 3) {
                dieValue = Math.min(6, dieValue + Math.floor(Math.random() * 3) + 1);
            }
        }

        diceValues.push(dieValue);
    }

    const actualSum = diceValues.reduce((sum, val) => sum + val, 0);

    // Check for winning combinations
    const combination = analyzeDeFiCombination(diceValues);

    // Check for DeFi combo (all dice same or sum >= 15)
    const defiCombo = (diceValues[0] === diceValues[1] && diceValues[1] === diceValues[2]) || actualSum >= 15;

    return {
        values: diceValues,
        sum: actualSum,
        combination: combination,
        defiCombo: defiCombo,
        stakingInfluence: stakingInfluence,
        yieldMultiplier: defiDiceDynastyGame.dice.yieldMultiplier
    };
}

// Analyze DeFi combination
function analyzeDeFiCombination(values) {
    const counts = {};
    values.forEach(value => {
        counts[value] = (counts[value] || 0) + 1;
    });

    const countValues = Object.values(counts).sort((a, b) => b - a);
    const sum = values.reduce((sum, val) => sum + val, 0);

    // Check for specific combinations
    if (values.every(v => v === 6)) {
        return { type: 'triple_sixes', name: 'Triple Sixes', multiplier: DEFI_DICE_PAYOUTS.TRIPLE_SIXES };
    }

    if (countValues[0] === 3) {
        return { type: 'triple_match', name: 'Triple Match', multiplier: DEFI_DICE_PAYOUTS.TRIPLE_MATCH };
    }

    if (countValues[0] === 2) {
        return { type: 'double_match', name: 'Double Match', multiplier: DEFI_DICE_PAYOUTS.DOUBLE_MATCH };
    }

    if (sum >= 15) {
        return { type: 'high_sum', name: 'High Sum', multiplier: DEFI_DICE_PAYOUTS.HIGH_SUM };
    }

    return { type: 'none', name: 'No Combination', multiplier: 0 };
}

// Animate DeFi dice roll
function animateDeFiDiceRoll(results) {
    const dice = document.querySelectorAll('.defi-die');

    // Add rolling animation
    dice.forEach((die, index) => {
        die.classList.add('animate-spin');
        die.style.transform = 'scale(1.2)';

        // Show random values during animation
        const rollInterval = setInterval(() => {
            die.textContent = Math.floor(Math.random() * 6) + 1;
        }, 100);

        // Stop animation and show final value
        setTimeout(() => {
            clearInterval(rollInterval);
            die.classList.remove('animate-spin');
            die.style.transform = 'scale(1)';
            die.textContent = results.values[index];

            // Add glow effect for good results
            if (results.defiCombo) {
                die.classList.add('ring-2', 'ring-green-400', 'animate-pulse');
            } else if (results.combination.type !== 'none') {
                die.classList.add('ring-1', 'ring-blue-400');
            }
        }, 1000 + index * 300);
    });
}

// Resolve DeFi dice roll with staking bonuses (3-5% win rate)
function resolveDeFiDiceRoll(results) {
    const poolData = DEFI_POOLS[defiDiceDynastyGame.stakingPool];
    const liquidityPoolData = defiDiceDynastyGame.liquidityPools[defiDiceDynastyGame.stakingPool];
    const strategyData = LIQUIDITY_STRATEGIES[defiDiceDynastyGame.liquidityStrategy];

    let totalWinnings = 0;
    let resultMessage = '';

    // Store results
    defiDiceDynastyGame.dice.values = results.values;
    defiDiceDynastyGame.dice.actualSum = results.sum;
    defiDiceDynastyGame.dice.defiCombo = results.defiCombo;

    // Calculate base payout for combination
    if (results.combination.type !== 'none') {
        totalWinnings = Math.floor(defiDiceDynastyGame.betAmount * (results.combination.multiplier / 100));
        resultMessage = results.combination.name;

        // Apply yield multiplier
        totalWinnings = Math.floor(totalWinnings * results.yieldMultiplier);
    }

    // Apply DeFi staking bonuses (actually work)
    if (defiDiceDynastyGame.stakingPools.totalStaked >= defiDiceDynastyGame.betAmount && totalWinnings > 0) {
        const stakingBonus = Math.floor(totalWinnings * DEFI_DICE_PAYOUTS.STAKING_BONUS);
        totalWinnings += stakingBonus;
        resultMessage += ' + Staking Bonus!';
    }

    // Apply yield bonus
    if (defiDiceDynastyGame.stakingPools.yieldEarned >= 10 && totalWinnings > 0) {
        const yieldBonus = Math.floor(totalWinnings * DEFI_DICE_PAYOUTS.YIELD_BONUS);
        totalWinnings += yieldBonus;
        resultMessage += ' + Yield Bonus!';
    }

    // Apply liquidity bonus
    if (defiDiceDynastyGame.stakingPools.liquidityProvided >= 50 && totalWinnings > 0) {
        const liquidityBonus = Math.floor(totalWinnings * DEFI_DICE_PAYOUTS.LIQUIDITY_BONUS);
        totalWinnings += liquidityBonus;
        resultMessage += ' + Liquidity Bonus!';
    }

    // Apply compound bonus
    if (defiDiceDynastyGame.stakingPools.compoundingRate >= 0.10 && totalWinnings > 0) {
        const compoundBonus = Math.floor(totalWinnings * DEFI_DICE_PAYOUTS.COMPOUND_BONUS);
        totalWinnings += compoundBonus;
        resultMessage += ' + Compound Bonus!';
    }

    // DeFi combo bonus
    if (results.defiCombo) {
        const comboBonus = Math.floor(defiDiceDynastyGame.betAmount * 2); // 2x bet bonus
        totalWinnings += comboBonus;
        resultMessage += ' + DeFi Combo!';
        defiDiceDynastyGame.dice.stakingStreak++;
    } else {
        defiDiceDynastyGame.dice.stakingStreak = 0;
    }

    // Apply pool multiplier
    totalWinnings = Math.floor(totalWinnings * poolData.payoutMultiplier);

    // Apply strategy risk/reward
    if (Math.random() < strategyData.riskLevel) {
        // Risk event - reduce winnings
        totalWinnings = Math.floor(totalWinnings * 0.7); // 30% loss
        resultMessage += ' - Risk Event!';
    }

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(defiDiceDynastyGame.betAmount * 0.6); // 60% return
        resultMessage = 'DeFi participation reward';
    }

    // Award DeFi points
    const pointsEarned = results.defiCombo ? 5 : (results.combination.type !== 'none' ? 3 : 1);
    defiDiceDynastyGame.stats.defiPoints += pointsEarned;

    // Add winnings to balance
    balance += totalWinnings;
    defiDiceDynastyGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterRoll(results.combination.type !== 'none' || results.defiCombo, totalWinnings);

    if (!resultMessage) {
        resultMessage = results.defiCombo ? 'DeFi combo achieved!' : 'Better luck next stake';
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Pool: ${DEFI_POOLS[defiDiceDynastyGame.stakingPool].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show DeFi event
    document.getElementById('defiEvent').classList.remove('hidden');
    document.getElementById('defiEvent').textContent = results.defiCombo ? 'DEFI COMBO!' : 'DICE ROLLED!';
    document.getElementById('defiEvent').className = `text-sm font-bold ${results.defiCombo ? 'text-green-400' : 'text-blue-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Provide liquidity to pool
function provideLiquidityToPool() {
    if (!defiDiceDynastyGame.isPlaying) return;

    const liquidityAmount = Math.floor(defiDiceDynastyGame.betAmount * 0.5); // 50% of bet as liquidity

    // Add liquidity to pool
    defiDiceDynastyGame.stakingPools.liquidityProvided += liquidityAmount;
    defiDiceDynastyGame.stats.liquidityProvided += liquidityAmount;

    // Calculate impermanent loss risk
    const strategyData = LIQUIDITY_STRATEGIES[defiDiceDynastyGame.liquidityStrategy];
    const impermanentLossRisk = strategyData.riskLevel * 100;
    defiDiceDynastyGame.stakingPools.impermanentLoss = impermanentLossRisk;

    // Award DeFi points
    defiDiceDynastyGame.stats.defiPoints += 3;

    // Visual feedback
    document.getElementById('defiEvent').classList.remove('hidden');
    document.getElementById('defiEvent').textContent = `LIQUIDITY PROVIDED: $${liquidityAmount}!`;
    document.getElementById('defiEvent').className = 'text-sm font-bold text-green-400 animate-pulse';

    setTimeout(() => {
        document.getElementById('defiEvent').classList.add('hidden');
    }, 3000);

    updatePoolStatus();
}

// Compound staking rewards
function compoundStakingRewards() {
    if (!defiDiceDynastyGame.isPlaying || defiDiceDynastyGame.stakingPools.yieldEarned <= 0) {
        alert('No rewards to compound!');
        return;
    }

    // Compound rewards back into staking
    const compoundAmount = Math.floor(defiDiceDynastyGame.stakingPools.yieldEarned * defiDiceDynastyGame.stakingPools.compoundingRate);

    defiDiceDynastyGame.stakingPools.totalStaked += compoundAmount;
    defiDiceDynastyGame.stakingPools.poolTokens += compoundAmount * 10; // 10 tokens per dollar
    defiDiceDynastyGame.stakingPools.yieldEarned -= compoundAmount;

    // Improve compounding rate slightly
    defiDiceDynastyGame.stakingPools.compoundingRate = Math.min(0.20, defiDiceDynastyGame.stakingPools.compoundingRate + 0.01);

    // Award DeFi points
    defiDiceDynastyGame.stats.defiPoints += 5;

    // Visual feedback
    document.getElementById('defiEvent').classList.remove('hidden');
    document.getElementById('defiEvent').textContent = `REWARDS COMPOUNDED: $${compoundAmount}!`;
    document.getElementById('defiEvent').className = 'text-sm font-bold text-purple-400 animate-pulse';

    setTimeout(() => {
        document.getElementById('defiEvent').classList.add('hidden');
    }, 3000);

    updateStakingDisplay();
}

// Update DeFi display
function updateDeFiDisplay() {
    updatePoolStatus();
    updateStakingDisplay();
}

// Update pool status
function updatePoolStatus() {
    const poolData = defiDiceDynastyGame.liquidityPools[defiDiceDynastyGame.stakingPool];

    document.getElementById('stakedAmount').textContent = `$${defiDiceDynastyGame.stakingPools.totalStaked}`;
    document.getElementById('currentAPY').textContent = `${Math.floor(poolData.apy * 100)}%`;
    document.getElementById('pendingRewards').textContent = `$${defiDiceDynastyGame.stakingPools.yieldEarned}`;

    document.getElementById('liquidityProvided').querySelector('.text-2xl').textContent =
        `$${defiDiceDynastyGame.stakingPools.liquidityProvided}`;

    document.getElementById('impermanentLoss').querySelector('.text-2xl').textContent =
        `${Math.floor(defiDiceDynastyGame.stakingPools.impermanentLoss)}%`;
}

// Update staking display
function updateStakingDisplay() {
    document.getElementById('stakingPower').innerHTML =
        `<div class="text-green-400 font-bold">POWER: ${Math.floor(defiDiceDynastyGame.stakingPools.stakingPower * 100)}%</div>`;
    document.getElementById('stakingLevel').innerHTML =
        `<div class="text-blue-400 font-bold">LEVEL: ${defiDiceDynastyGame.stakingPools.stakingLevel.toUpperCase()}</div>`;
    document.getElementById('yieldEarned').innerHTML =
        `<div class="text-purple-400 font-bold">YIELD: $${defiDiceDynastyGame.stakingPools.yieldEarned}</div>`;
    document.getElementById('poolTokens').innerHTML =
        `<div class="text-yellow-400 font-bold">TOKENS: ${defiDiceDynastyGame.stakingPools.poolTokens}</div>`;
}

// Update DeFi effects
function updateDeFiEffects() {
    // Update DeFi effects based on staking level
    const stakingLevel = defiDiceDynastyGame.stakingPools.stakingLevel;
    const effects = document.querySelectorAll('#defiEffects circle');

    effects.forEach((effect, index) => {
        switch (stakingLevel) {
            case 'diamond':
                effect.setAttribute('opacity', '0.9');
                effect.setAttribute('fill', '#fbbf24'); // Gold for diamond
                break;
            case 'platinum':
                effect.setAttribute('opacity', '0.8');
                effect.setAttribute('fill', '#e5e7eb'); // Silver for platinum
                break;
            case 'gold':
                effect.setAttribute('opacity', '0.7');
                effect.setAttribute('fill', '#f59e0b'); // Gold
                break;
            case 'silver':
                effect.setAttribute('opacity', '0.6');
                effect.setAttribute('fill', '#6b7280'); // Silver
                break;
            default: // bronze
                effect.setAttribute('opacity', '0.5');
                effect.setAttribute('fill', '#92400e'); // Bronze
                break;
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${defiDiceDynastyGame.betAmount}`;
    document.getElementById('defiPointsDisplay').textContent = defiDiceDynastyGame.stats.defiPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = defiDiceDynastyGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${defiDiceDynastyGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${defiDiceDynastyGame.stats.totalWagered}`;
    document.getElementById('totalStakedStat').textContent = `$${defiDiceDynastyGame.stats.totalStaked}`;
    document.getElementById('yieldEarnedStat').textContent = `$${defiDiceDynastyGame.stats.totalYieldEarned}`;

    const netResult = defiDiceDynastyGame.stats.totalWon - defiDiceDynastyGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-blue-400' : 'text-red-400'}`;
}

// Update stats after roll
function updateGameStatsAfterRoll(won, winnings) {
    defiDiceDynastyGame.stats.gamesPlayed++;
    defiDiceDynastyGame.stats.totalWagered += defiDiceDynastyGame.betAmount;
    defiDiceDynastyGame.stats.totalWon += winnings;
    defiDiceDynastyGame.stats.totalStaked += defiDiceDynastyGame.stakingPools.totalStaked;
    defiDiceDynastyGame.stats.totalYieldEarned += defiDiceDynastyGame.stakingPools.yieldEarned;

    if (won) {
        defiDiceDynastyGame.stats.gamesWon++;
        defiDiceDynastyGame.stats.currentStreak++;
        defiDiceDynastyGame.streakData.currentWinStreak++;
        defiDiceDynastyGame.streakData.currentLossStreak = 0;

        if (defiDiceDynastyGame.streakData.currentWinStreak > defiDiceDynastyGame.streakData.longestWinStreak) {
            defiDiceDynastyGame.streakData.longestWinStreak = defiDiceDynastyGame.streakData.currentWinStreak;
        }

        if (winnings > defiDiceDynastyGame.stats.biggestWin) {
            defiDiceDynastyGame.stats.biggestWin = winnings;
        }
    } else {
        defiDiceDynastyGame.stats.currentStreak = 0;
        defiDiceDynastyGame.streakData.currentWinStreak = 0;
        defiDiceDynastyGame.streakData.currentLossStreak++;

        if (defiDiceDynastyGame.streakData.currentLossStreak > defiDiceDynastyGame.streakData.longestLossStreak) {
            defiDiceDynastyGame.streakData.longestLossStreak = defiDiceDynastyGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to DeFi mechanics)
    defiDiceDynastyGame.stats.winRate = (defiDiceDynastyGame.stats.gamesWon / defiDiceDynastyGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next DeFi session
function resetGame() {
    defiDiceDynastyGame.isPlaying = false;
    defiDiceDynastyGame.betAmount = 0;
    defiDiceDynastyGame.totalBet = 0;
    defiDiceDynastyGame.gameResult = '';
    defiDiceDynastyGame.totalWin = 0;

    // Reset dice system
    defiDiceDynastyGame.dice.values = [1, 1, 1];
    defiDiceDynastyGame.dice.actualSum = 0;
    defiDiceDynastyGame.dice.stakingBonus = 1.0;
    defiDiceDynastyGame.dice.liquidityBonus = 1.0;
    defiDiceDynastyGame.dice.yieldMultiplier = 1.0;
    defiDiceDynastyGame.dice.defiCombo = false;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('defiEvent').classList.add('hidden');
    document.getElementById('defiActions').classList.add('hidden');

    // Reset dice display
    const dice = document.querySelectorAll('.defi-die');
    dice.forEach((die, index) => {
        die.textContent = '1';
        die.className = 'defi-die w-20 h-20 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg border-2 border-blue-400 flex items-center justify-center text-3xl font-bold text-white transition-all duration-500';
    });

    // Reset pool display
    document.getElementById('poolStatus').textContent = 'Ready to stake tokens...';

    // Reset staking display
    document.getElementById('stakingBar').style.width = '0%';
    document.getElementById('stakingStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable stake button
    document.getElementById('stakeTokens').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'DeFi ready to stake...';
    document.getElementById('gameMessage').textContent = 'Welcome to DeFi Dice Dynasty - Staking Mechanics with Liquidity Pool Rewards';

    // Reinitialize systems for next session
    initializeDeFiSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDeFiDiceDynastyGame();
});