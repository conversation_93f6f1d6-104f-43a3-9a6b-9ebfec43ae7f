// Stock Market Crash - Wall Street Volatility Simulator
// Experimental Concept Implementation with Financial Market Theme
// Designed to maintain 3-5% player win rate with market volatility mechanics

// Game state
let balance = 1000;

// Game state object with stock market system
let stockMarketCrashGame = {
    isPlaying: false,
    marketSector: 'tech', // tech, finance, energy, healthcare, consumer
    tradingStrategy: 'conservative', // conservative, balanced, aggressive, day_trading, algorithmic
    betAmount: 0,
    totalBet: 0,

    // Stock market system
    market: {
        volatility: 0.75, // 75% market volatility
        sentiment: 0.60, // 60% market sentiment
        liquidity: 0.80, // 80% market liquidity
        momentum: 0.65, // 65% market momentum
        riskTolerance: 0.70, // 70% risk tolerance
        marketTrend: 0.55, // 55% market trend (bullish)
        tradingAdvantage: 1.0,
        marketHistory: [],
        tradingLevel: 'novice', // novice, trader, analyst, expert, wall_street_legend
        tradingPoints: 0,
        portfolioValue: 0,
        marketCrashRisk: 0.25, // 25% crash risk
        bubbleIndicator: 0.30 // 30% bubble indicator
    },

    // Trading game state
    trading: {
        selectedStocks: [],
        position: '', // long, short, straddle, hedge
        gamePhase: 'analysis', // analysis, trading, market_move, crash_check, finished
        marketMovement: 0,
        gameResult: '',
        volatilityBonus: 1.0,
        sentimentBonus: 0.0,
        priceHistory: [],
        marketEvents: [],
        crashOccurred: false,
        bubbleBurst: false,
        tradingVolume: 0
    },

    // Market sectors
    marketSectors: {
        tech: {
            name: 'Technology',
            volatility: 0.85, // 85% volatility
            growth: 0.75, // 75% growth potential
            crashRisk: 0.30, // 30% crash risk
            tradingBonus: 0.25 // 25% trading bonus
        },
        finance: {
            name: 'Financial',
            volatility: 0.70, // 70% volatility
            growth: 0.65, // 65% growth potential
            crashRisk: 0.40, // 40% crash risk
            tradingBonus: 0.30 // 30% trading bonus
        },
        energy: {
            name: 'Energy',
            volatility: 0.90, // 90% volatility
            growth: 0.60, // 60% growth potential
            crashRisk: 0.35, // 35% crash risk
            tradingBonus: 0.35 // 35% trading bonus
        },
        healthcare: {
            name: 'Healthcare',
            volatility: 0.60, // 60% volatility
            growth: 0.70, // 70% growth potential
            crashRisk: 0.20, // 20% crash risk
            tradingBonus: 0.20 // 20% trading bonus
        },
        consumer: {
            name: 'Consumer Goods',
            volatility: 0.65, // 65% volatility
            growth: 0.55, // 55% growth potential
            crashRisk: 0.25, // 25% crash risk
            tradingBonus: 0.25 // 25% trading bonus
        }
    },

    gameResult: '',
    totalWin: 0,

    stats: {
        tradesExecuted: 0,
        profitableTrades: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        marketCrashes: 0,
        bubblesBurst: 0,
        volatilityEvents: 0,
        portfolioValue: 0,
        tradingPoints: 0,
        bestReturn: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Market sectors with balanced volatility requirements (3-5% win rate)
const MARKET_SECTORS = {
    tech: {
        name: 'Technology',
        volatilityWeight: 0.25, // 25% volatility influence (increased)
        luckFactor: 0.75, // 75% luck factor
        payoutMultiplier: 0.95, // Good payouts
        sentimentBonus: 0.20, // 20% sentiment bonus
        tradingBonus: 0.15 // 15% trading bonus
    },
    finance: {
        name: 'Financial',
        volatilityWeight: 0.30, // 30% volatility influence
        luckFactor: 0.70, // 70% luck factor
        payoutMultiplier: 1.00, // Full payouts
        sentimentBonus: 0.25, // 25% sentiment bonus
        tradingBonus: 0.20 // 20% trading bonus
    },
    energy: {
        name: 'Energy',
        volatilityWeight: 0.35, // 35% volatility influence
        luckFactor: 0.65, // 65% luck factor
        payoutMultiplier: 1.05, // Premium payouts
        sentimentBonus: 0.30, // 30% sentiment bonus
        tradingBonus: 0.25 // 25% trading bonus
    },
    healthcare: {
        name: 'Healthcare',
        volatilityWeight: 0.28, // 28% volatility influence
        luckFactor: 0.72, // 72% luck factor
        payoutMultiplier: 0.98, // Good payouts
        sentimentBonus: 0.22, // 22% sentiment bonus
        tradingBonus: 0.18 // 18% trading bonus
    },
    consumer: {
        name: 'Consumer Goods',
        volatilityWeight: 0.32, // 32% volatility influence
        luckFactor: 0.68, // 68% luck factor
        payoutMultiplier: 1.02, // Premium payouts
        sentimentBonus: 0.28, // 28% sentiment bonus
        tradingBonus: 0.22 // 22% trading bonus
    }
};

const TRADING_STRATEGIES = {
    conservative: {
        name: 'Conservative Trading',
        riskLevel: 0.30, // 30% risk
        returnMultiplier: 1.10 // 10% return boost
    },
    balanced: {
        name: 'Balanced Trading',
        riskLevel: 0.50, // 50% risk
        returnMultiplier: 1.15 // 15% return boost
    },
    aggressive: {
        name: 'Aggressive Trading',
        riskLevel: 0.70, // 70% risk
        returnMultiplier: 1.20 // 20% return boost
    },
    day_trading: {
        name: 'Day Trading',
        riskLevel: 0.85, // 85% risk
        returnMultiplier: 1.25 // 25% return boost
    },
    algorithmic: {
        name: 'Algorithmic Trading',
        riskLevel: 0.60, // 60% risk
        returnMultiplier: 1.30 // 30% return boost
    }
};

// Improved payout table with stock market theme (3-5% win rate)
const STOCK_MARKET_PAYOUTS = {
    // Perfect trading achievements (moderately reduced)
    WALL_STREET_LEGEND: 4000, // Reduced from 8000:1 but still excellent
    MARKET_GENIUS: 3000, // Reduced from 6000:1
    VOLATILITY_MASTER: 2000, // Reduced from 4000:1
    CRASH_SURVIVOR: 1500, // Reduced from 3000:1

    // Market movement payouts with volatility bonuses
    BULL_RUN: 1000, // 10:1 payout for major gains
    BEAR_MARKET: 800, // 8:1 payout for shorting correctly
    SIDEWAYS_MARKET: 300, // 3:1 payout for range trading
    MARKET_CORRECTION: 500, // 5:1 payout for correction prediction
    VOLATILITY_SPIKE: 600, // 6:1 payout for volatility trading

    // Trading bonuses (actually apply more often)
    VOLATILITY_BONUS: 0.85, // 85% of displayed bonus (increased)
    SENTIMENT_BONUS: 0.75, // 75% of displayed bonus (increased)
    MOMENTUM_BONUS: 0.65, // 65% of displayed bonus (increased)
    LIQUIDITY_BONUS: 0.55 // 55% of displayed bonus (increased)
};

// Stock symbols for display
const STOCK_SYMBOLS = ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM', 'BAC', 'XOM', 'CVX', 'JNJ', 'PFE', 'KO', 'PG'];

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadStockMarketCrashGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">TRADING DESK</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">MARKET SECTOR</label>
                        <select id="marketSector" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="tech">Technology</option>
                            <option value="finance">Financial</option>
                            <option value="energy">Energy</option>
                            <option value="healthcare">Healthcare</option>
                            <option value="consumer">Consumer Goods</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">TRADING STRATEGY</label>
                        <select id="tradingStrategy" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="conservative">Conservative Trading</option>
                            <option value="balanced">Balanced Trading</option>
                            <option value="aggressive">Aggressive Trading</option>
                            <option value="day_trading">Day Trading</option>
                            <option value="algorithmic">Algorithmic Trading</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">INVESTMENT AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="enterMarket" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ENTER MARKET
                    </button>

                    <div id="tradingActions" class="space-y-2 hidden">
                        <div class="text-sm text-red-400 mb-2">TRADING POSITIONS:</div>
                        <div class="grid grid-cols-2 gap-2">
                            <button id="goLong" class="py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white text-xs">
                                LONG
                            </button>
                            <button id="goShort" class="py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white text-xs">
                                SHORT
                            </button>
                        </div>
                        <button id="executeTradeBtn" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            EXECUTE TRADE
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Investment</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Trading Points</div>
                        <div id="tradingPointsDisplay" class="text-lg font-bold text-red-400">0</div>
                    </div>
                </div>

                <!-- Market Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">MARKET STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="volatility" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">VOLATILITY: 75%</div>
                        </div>
                        <div id="sentiment" class="text-center p-2 rounded bg-black/50">
                            <div class="text-blue-400 font-bold">SENTIMENT: 60%</div>
                        </div>
                        <div id="momentum" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">MOMENTUM: 65%</div>
                        </div>
                        <div id="tradingLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LEVEL: NOVICE</div>
                        </div>
                    </div>
                </div>

                <!-- Market Risk Info -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">MARKET RISK</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Risk Indicators:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Crash Risk:</span>
                            <span class="text-red-400">25%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Bubble:</span>
                            <span class="text-orange-400">30%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Liquidity:</span>
                            <span class="text-blue-400">80%</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Trading Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Volatility:</span>
                            <span class="text-red-400">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Sentiment:</span>
                            <span class="text-red-400">75%</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*Volatility improves returns</div>
                        <div class="text-xs text-red-400">*Market timing provides bonuses</div>
                    </div>
                </div>
            </div>

            <!-- Main Trading Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <!-- Stock Market Arena -->
                    <div id="stockMarketArena" class="relative bg-gradient-to-br from-black via-red-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Market Background -->
                        <div id="marketBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="marketGradient" cx="50%" cy="50%" r="60%">
                                        <stop offset="0%" style="stop-color:#ef4444;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#dc2626;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="marketPattern" width="30" height="30" patternUnits="userSpaceOnUse">
                                        <line x1="0" y1="15" x2="30" y2="15" stroke="#ef4444" stroke-width="1" opacity="0.3"/>
                                        <line x1="15" y1="0" x2="15" y2="30" stroke="#dc2626" stroke-width="1" opacity="0.3"/>
                                        <circle cx="15" cy="15" r="8" fill="none" stroke="#b91c1c" stroke-width="2" opacity="0.4"/>
                                        <circle cx="15" cy="15" r="3" fill="#ef4444" opacity="0.6"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#marketPattern)" />
                                <circle id="marketCore" cx="50%" cy="50%" r="25%" fill="url(#marketGradient)" class="animate-pulse" />
                                <g id="marketEffects">
                                    <!-- Market effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Stock Chart -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">STOCK MARKET CHART</div>
                                <div id="stockChart" class="bg-black/70 rounded-lg p-4 w-80 border-2 border-red-600">
                                    <div class="flex justify-between items-center mb-4">
                                        <div class="text-xs text-white">MARKET INDEX</div>
                                        <div id="marketIndex" class="text-lg font-bold text-white">10,000</div>
                                    </div>
                                    <div id="priceChart" class="h-32 bg-black/50 rounded border border-gray-600 relative overflow-hidden">
                                        <!-- Price chart will be drawn here -->
                                        <canvas id="chartCanvas" width="300" height="120" class="absolute inset-0"></canvas>
                                    </div>
                                    <div class="flex justify-between mt-2 text-xs">
                                        <span class="text-gray-400">24H Change:</span>
                                        <span id="dailyChange" class="text-green-400">+0.00%</span>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Real-time market simulation</div>
                            </div>
                        </div>

                        <!-- Trading Position Display -->
                        <div class="absolute bottom-16 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">TRADING POSITION</div>
                                <div id="tradingPositionDisplay" class="bg-black/70 rounded-lg p-4 w-80">
                                    <div id="positionStatus" class="text-lg text-white mb-3">Ready to enter market...</div>
                                    <div class="grid grid-cols-3 gap-4">
                                        <div class="text-center">
                                            <div class="text-xs text-red-400 mb-1">POSITION</div>
                                            <div id="currentPosition" class="text-xl font-bold text-white">NONE</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-red-400 mb-1">P&L</div>
                                            <div id="profitLoss" class="text-xl font-bold text-green-400">$0</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-xs text-red-400 mb-1">VOLUME</div>
                                            <div id="tradingVolume" class="text-xl font-bold text-blue-400">0</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Wall Street volatility simulation</div>
                            </div>
                        </div>

                        <!-- Market Analysis Progress -->
                        <div id="analysisProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">MARKET ANALYSIS</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="analysisBar" class="bg-gradient-to-r from-red-400 to-orange-400 h-4 rounded-full transition-all duration-1000" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Analyzing</span>
                                    <span id="analysisStatus">Ready</span>
                                    <span>Complete</span>
                                </div>
                            </div>
                        </div>

                        <!-- Market Volatility -->
                        <div id="marketVolatility" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">VOLATILITY</div>
                                <div class="text-2xl font-bold text-white text-center">75%</div>
                                <div class="text-xs text-gray-400 mt-1">Index</div>
                            </div>
                        </div>

                        <!-- Crash Risk -->
                        <div id="crashRisk" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-orange-400 mb-1">CRASH RISK</div>
                                <div class="text-2xl font-bold text-white text-center">25%</div>
                                <div class="text-xs text-gray-400 mt-1">Probability</div>
                            </div>
                        </div>

                        <!-- Market Sentiment -->
                        <div id="marketSentiment" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-blue-400 mb-1">SENTIMENT</div>
                                <div class="text-2xl font-bold text-white text-center">60%</div>
                                <div class="text-xs text-gray-400 mt-1">Bullish</div>
                            </div>
                        </div>

                        <!-- Selected Sector -->
                        <div id="selectedSector" class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">SECTOR</div>
                                <div class="text-lg font-bold text-white text-center">TECH</div>
                                <div class="text-xs text-gray-400 mt-1">Selected</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Market ready for analysis...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="marketEvent" class="text-sm font-bold text-red-400 hidden animate-pulse">MARKET ENTERED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Stock Market Crash - Wall Street Volatility Simulator</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Trades Executed</div>
                <div id="tradesExecuted" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Total Invested</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Net P&L</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Market Crashes</div>
                <div id="marketCrashes" class="text-xl font-bold text-orange-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Volatility Events</div>
                <div id="volatilityEvents" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeStockMarketCrash();
}

// Initialize the game
function initializeStockMarketCrash() {
    document.getElementById('enterMarket').addEventListener('click', enterStockMarket);
    document.getElementById('goLong').addEventListener('click', () => setTradingPosition('long'));
    document.getElementById('goShort').addEventListener('click', () => setTradingPosition('short'));
    document.getElementById('executeTradeBtn').addEventListener('click', executeTrade);

    // Initialize market systems
    initializeMarketSystems();
    generateMarketEffects();
    initializeChart();
    updateGameStats();
}

// Initialize market systems
function initializeMarketSystems() {
    // Reset market system
    stockMarketCrashGame.market.volatility = Math.min(0.95, 0.75 + (stockMarketCrashGame.stats.tradingPoints * 0.008));
    stockMarketCrashGame.market.sentiment = Math.min(0.90, 0.60 + (stockMarketCrashGame.stats.tradingPoints * 0.01));
    stockMarketCrashGame.market.liquidity = Math.min(0.95, 0.80 + (stockMarketCrashGame.stats.tradingPoints * 0.006));
    stockMarketCrashGame.market.momentum = Math.min(0.90, 0.65 + (stockMarketCrashGame.stats.tradingPoints * 0.008));
    stockMarketCrashGame.market.riskTolerance = Math.min(0.85, 0.70 + (stockMarketCrashGame.stats.tradingPoints * 0.005));
    stockMarketCrashGame.market.marketTrend = Math.random() * 0.4 + 0.4; // 40-80% trend
    stockMarketCrashGame.market.tradingAdvantage = 1.0;
    stockMarketCrashGame.market.marketHistory = [];
    stockMarketCrashGame.market.tradingLevel = calculateTradingLevel();
    stockMarketCrashGame.market.tradingPoints = stockMarketCrashGame.stats.tradingPoints;
    stockMarketCrashGame.market.portfolioValue = 0;
    stockMarketCrashGame.market.marketCrashRisk = Math.max(0.10, 0.25 - (stockMarketCrashGame.stats.tradingPoints * 0.002));
    stockMarketCrashGame.market.bubbleIndicator = Math.max(0.15, 0.30 - (stockMarketCrashGame.stats.tradingPoints * 0.003));

    // Reset trading system
    stockMarketCrashGame.trading.selectedStocks = [];
    stockMarketCrashGame.trading.position = '';
    stockMarketCrashGame.trading.gamePhase = 'analysis';
    stockMarketCrashGame.trading.marketMovement = 0;
    stockMarketCrashGame.trading.gameResult = '';
    stockMarketCrashGame.trading.volatilityBonus = 1.0;
    stockMarketCrashGame.trading.sentimentBonus = 0.0;
    stockMarketCrashGame.trading.priceHistory = [];
    stockMarketCrashGame.trading.marketEvents = [];
    stockMarketCrashGame.trading.crashOccurred = false;
    stockMarketCrashGame.trading.bubbleBurst = false;
    stockMarketCrashGame.trading.tradingVolume = 0;

    updateMarketDisplay();
}

// Generate market effects
function generateMarketEffects() {
    const container = document.getElementById('marketEffects');
    container.innerHTML = '';

    // Create market visualization
    for (let i = 0; i < 12; i++) {
        const marketNode = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        marketNode.setAttribute('cx', `${Math.random() * 100}%`);
        marketNode.setAttribute('cy', `${Math.random() * 100}%`);
        marketNode.setAttribute('r', `${Math.random() * 2 + 1}%`);
        marketNode.setAttribute('fill', '#ef4444');
        marketNode.setAttribute('opacity', '0.6');
        marketNode.classList.add('animate-pulse');
        marketNode.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(marketNode);

        // Add market connections
        if (i > 0 && i % 3 === 0) {
            const connection = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            const prevNode = container.children[(i - 3) * 2];
            if (prevNode) {
                connection.setAttribute('x1', prevNode.getAttribute('cx'));
                connection.setAttribute('y1', prevNode.getAttribute('cy'));
                connection.setAttribute('x2', marketNode.getAttribute('cx'));
                connection.setAttribute('y2', marketNode.getAttribute('cy'));
                connection.setAttribute('stroke', '#dc2626');
                connection.setAttribute('stroke-width', '1');
                connection.setAttribute('opacity', '0.4');
                container.appendChild(connection);
            }
        }
    }
}

// Initialize chart
function initializeChart() {
    const canvas = document.getElementById('chartCanvas');
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw initial flat line
    ctx.strokeStyle = '#10b981';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.stroke();

    // Initialize price history
    stockMarketCrashGame.trading.priceHistory = [10000]; // Starting at 10,000
}

// Calculate trading level
function calculateTradingLevel() {
    const points = stockMarketCrashGame.stats.tradingPoints;
    if (points >= 100) return 'wall_street_legend';
    if (points >= 50) return 'expert';
    if (points >= 25) return 'analyst';
    if (points >= 10) return 'trader';
    return 'novice';
}

// Enter stock market
function enterStockMarket() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid investment amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    stockMarketCrashGame.isPlaying = true;
    stockMarketCrashGame.betAmount = betAmount;
    stockMarketCrashGame.totalBet = betAmount;
    stockMarketCrashGame.marketSector = document.getElementById('marketSector').value;
    stockMarketCrashGame.tradingStrategy = document.getElementById('tradingStrategy').value;

    // Start market analysis
    startMarketAnalysis();

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('enterMarket').disabled = true;
    document.getElementById('gameStatus').textContent = 'Analyzing market conditions...';
}

// Start market analysis
function startMarketAnalysis() {
    const marketSectorData = stockMarketCrashGame.marketSectors[stockMarketCrashGame.marketSector];
    const tradingStrategyData = TRADING_STRATEGIES[stockMarketCrashGame.tradingStrategy];

    // Update market information
    document.getElementById('positionStatus').textContent = `Analyzing ${marketSectorData.name} sector...`;
    document.getElementById('analysisStatus').textContent = 'Analyzing...';

    // Simulate market analysis process
    simulateMarketAnalysis(marketSectorData, tradingStrategyData);

    // Update market status
    updateMarketStatus();

    // Update visual effects
    updateMarketDisplay();
}

// Simulate market analysis process
function simulateMarketAnalysis(marketSectorData, tradingStrategyData) {
    let progress = 0;
    const analysisTime = 40; // 4 seconds analysis time

    const analysisInterval = setInterval(() => {
        progress += 100 / analysisTime; // Update every 100ms

        // Update progress bar
        document.getElementById('analysisBar').style.width = `${Math.min(100, progress)}%`;

        // Update status messages
        if (progress < 25) {
            document.getElementById('analysisStatus').textContent = 'Scanning...';
        } else if (progress < 50) {
            document.getElementById('analysisStatus').textContent = 'Analyzing...';
        } else if (progress < 75) {
            document.getElementById('analysisStatus').textContent = 'Calculating...';
        } else if (progress < 100) {
            document.getElementById('analysisStatus').textContent = 'Finalizing...';
        } else {
            document.getElementById('analysisStatus').textContent = 'Complete!';
            clearInterval(analysisInterval);
            completeMarketAnalysis(marketSectorData, tradingStrategyData);
        }

        // Update market volatility based on progress
        const volatilityLevel = Math.min(95, stockMarketCrashGame.market.volatility * 100 + progress * 0.1);
        document.getElementById('marketVolatility').querySelector('.text-2xl').textContent = `${Math.floor(volatilityLevel)}%`;

    }, 100);
}

// Complete market analysis
function completeMarketAnalysis(marketSectorData, tradingStrategyData) {
    // Apply market bonuses
    const marketBonus = calculateMarketBonus();
    const volatilityBonus = marketSectorData.volatility * 0.15; // Up to 15% bonus

    // Update trading advantage
    stockMarketCrashGame.trading.volatilityBonus = 1.0 + marketBonus + volatilityBonus;

    // Calculate market sentiment
    stockMarketCrashGame.trading.sentimentBonus = stockMarketCrashGame.market.sentiment * stockMarketCrashGame.market.momentum;

    // Award trading points
    const pointsEarned = Math.floor(marketSectorData.tradingBonus * 100);
    stockMarketCrashGame.stats.tradingPoints += pointsEarned;

    // Enable trading actions
    document.getElementById('tradingActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Market analyzed! Choose your position.';

    // Update displays
    updateMarketStatus();
    updateTradingDisplay();

    // Update selected sector display
    document.getElementById('selectedSector').querySelector('.text-lg').textContent =
        stockMarketCrashGame.marketSector.toUpperCase();
}

// Calculate market bonus
function calculateMarketBonus() {
    let bonus = 0;

    // Volatility bonus
    bonus += stockMarketCrashGame.market.volatility * 0.12; // Up to 12% bonus

    // Sentiment bonus
    bonus += stockMarketCrashGame.market.sentiment * 0.10; // Up to 10% bonus

    // Momentum bonus
    bonus += stockMarketCrashGame.market.momentum * 0.08; // Up to 8% bonus

    // Liquidity bonus
    bonus += stockMarketCrashGame.market.liquidity * 0.06; // Up to 6% bonus

    // Risk tolerance bonus
    bonus += stockMarketCrashGame.market.riskTolerance * 0.10; // Up to 10% bonus

    // Trading level bonus
    const levelBonuses = {
        novice: 0.05,
        trader: 0.08,
        analyst: 0.12,
        expert: 0.15,
        wall_street_legend: 0.20
    };
    bonus += levelBonuses[stockMarketCrashGame.market.tradingLevel] || 0.05;

    return Math.min(0.50, bonus); // Cap at 50% bonus
}

// Set trading position
function setTradingPosition(position) {
    if (!stockMarketCrashGame.isPlaying) return;

    stockMarketCrashGame.trading.position = position;
    stockMarketCrashGame.trading.gamePhase = 'trading';

    // Update position display
    document.getElementById('currentPosition').textContent = position.toUpperCase();

    document.getElementById('gameStatus').textContent = `Position set to ${position.toUpperCase()}. Ready to execute trade.`;

    // Enable execute button
    document.getElementById('executeTradeBtn').disabled = false;
}

// Execute trade with market volatility (3-5% win rate)
function executeTrade() {
    if (!stockMarketCrashGame.isPlaying || !stockMarketCrashGame.trading.position) {
        alert('Please select a trading position first!');
        return;
    }

    stockMarketCrashGame.trading.gamePhase = 'market_move';
    document.getElementById('gameStatus').textContent = 'Executing trade...';

    // Calculate market influence on outcome
    const marketInfluence = calculateMarketInfluence();

    // Generate market movement
    const marketResult = generateMarketMovement(marketInfluence);

    // Animate market movement
    animateMarketMovement(marketResult);

    // Resolve after animation
    setTimeout(() => {
        resolveStockMarketTrade(marketResult);
    }, 4000);
}

// Calculate market influence
function calculateMarketInfluence() {
    const marketSectorData = MARKET_SECTORS[stockMarketCrashGame.marketSector];
    const marketBonus = calculateMarketBonus();
    const volatilityBonus = stockMarketCrashGame.market.volatility * 0.10; // Up to 10% bonus
    const sentimentBonus = stockMarketCrashGame.market.sentiment * 0.08; // Up to 8% bonus
    const tradingPointsBonus = stockMarketCrashGame.stats.tradingPoints * 0.002; // Trading points bonus

    return Math.min(0.35, marketSectorData.volatilityWeight + marketBonus + volatilityBonus + sentimentBonus + tradingPointsBonus);
}

// Generate market movement with volatility influence (improved for 3-5% win rate)
function generateMarketMovement(marketInfluence) {
    const marketSectorData = stockMarketCrashGame.marketSectors[stockMarketCrashGame.marketSector];
    const tradingStrategyData = TRADING_STRATEGIES[stockMarketCrashGame.tradingStrategy];

    // Apply market influence to improve odds
    const adjustedOdds = 0.035 + marketInfluence; // Base 3.5% + market influence

    // Generate base market movement (-20% to +20%)
    let marketMovement = (Math.random() - 0.5) * 0.4; // -0.2 to +0.2

    // Apply market influence (improved)
    if (Math.random() < marketInfluence) {
        // Market analysis tries to improve player's chances
        if (stockMarketCrashGame.trading.position === 'long' && marketMovement < 0) {
            // Try to make market go up for long position
            marketMovement = Math.abs(marketMovement) * (0.5 + Math.random() * 0.5); // 0 to positive
        } else if (stockMarketCrashGame.trading.position === 'short' && marketMovement > 0) {
            // Try to make market go down for short position
            marketMovement = -Math.abs(marketMovement) * (0.5 + Math.random() * 0.5); // 0 to negative
        }
    }

    // Apply sector volatility
    marketMovement *= marketSectorData.volatility;

    // Apply trading strategy risk/reward
    marketMovement *= tradingStrategyData.returnMultiplier;

    // Check for market crash
    let crashOccurred = false;
    if (Math.random() < stockMarketCrashGame.market.marketCrashRisk) {
        crashOccurred = true;
        marketMovement = -0.15 - Math.random() * 0.15; // -15% to -30% crash
        stockMarketCrashGame.stats.marketCrashes++;
    }

    // Check for bubble burst
    let bubbleBurst = false;
    if (Math.random() < stockMarketCrashGame.market.bubbleIndicator && !crashOccurred) {
        bubbleBurst = true;
        marketMovement = Math.random() * 0.20 + 0.10; // +10% to +30% bubble
        stockMarketCrashGame.stats.bubblesBurst++;
    }

    // Apply market sentiment
    if (stockMarketCrashGame.market.sentiment > 0.75 && !crashOccurred) {
        marketMovement += 0.02; // +2% bullish sentiment bonus
    } else if (stockMarketCrashGame.market.sentiment < 0.40 && !crashOccurred) {
        marketMovement -= 0.02; // -2% bearish sentiment penalty
    }

    // Determine if player won
    let playerWon = false;
    if (stockMarketCrashGame.trading.position === 'long' && marketMovement > 0) {
        playerWon = true;
    } else if (stockMarketCrashGame.trading.position === 'short' && marketMovement < 0) {
        playerWon = true;
    }

    return {
        marketMovement: marketMovement,
        playerWon: playerWon,
        position: stockMarketCrashGame.trading.position,
        marketInfluence: marketInfluence,
        crashOccurred: crashOccurred,
        bubbleBurst: bubbleBurst,
        volatility: marketSectorData.volatility,
        sentiment: stockMarketCrashGame.market.sentiment
    };
}

// Animate market movement
function animateMarketMovement(results) {
    // Store results for display
    stockMarketCrashGame.trading.marketMovement = results.marketMovement;
    stockMarketCrashGame.trading.crashOccurred = results.crashOccurred;
    stockMarketCrashGame.trading.bubbleBurst = results.bubbleBurst;

    // Update chart with market movement
    updateMarketChart(results.marketMovement);

    // Update market index
    const currentIndex = stockMarketCrashGame.trading.priceHistory[stockMarketCrashGame.trading.priceHistory.length - 1];
    const newIndex = Math.floor(currentIndex * (1 + results.marketMovement));
    stockMarketCrashGame.trading.priceHistory.push(newIndex);

    // Animate index change
    animateIndexChange(currentIndex, newIndex);

    // Update daily change
    const dailyChange = ((newIndex - currentIndex) / currentIndex * 100).toFixed(2);
    const changeElement = document.getElementById('dailyChange');
    changeElement.textContent = `${dailyChange >= 0 ? '+' : ''}${dailyChange}%`;
    changeElement.className = dailyChange >= 0 ? 'text-green-400' : 'text-red-400';

    // Update P&L display
    const pnl = Math.floor(stockMarketCrashGame.betAmount * Math.abs(results.marketMovement) *
                          (results.playerWon ? 1 : -1));
    document.getElementById('profitLoss').textContent = `$${pnl}`;
    document.getElementById('profitLoss').className = `text-xl font-bold ${pnl >= 0 ? 'text-green-400' : 'text-red-400'}`;

    // Update trading volume
    const volume = Math.floor(Math.random() * 1000000) + 500000;
    stockMarketCrashGame.trading.tradingVolume = volume;
    document.getElementById('tradingVolume').textContent = (volume / 1000000).toFixed(1) + 'M';
}

// Update market chart
function updateMarketChart(movement) {
    const canvas = document.getElementById('chartCanvas');
    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw price history
    const history = stockMarketCrashGame.trading.priceHistory;
    if (history.length > 1) {
        const minPrice = Math.min(...history);
        const maxPrice = Math.max(...history);
        const priceRange = maxPrice - minPrice || 1;

        ctx.strokeStyle = movement >= 0 ? '#10b981' : '#ef4444';
        ctx.lineWidth = 2;
        ctx.beginPath();

        history.forEach((price, index) => {
            const x = (index / (history.length - 1)) * canvas.width;
            const y = canvas.height - ((price - minPrice) / priceRange) * canvas.height;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();
    }
}

// Animate index change
function animateIndexChange(startIndex, endIndex) {
    const indexElement = document.getElementById('marketIndex');
    const duration = 2000; // 2 seconds
    const startTime = Date.now();

    const animateIndex = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const currentIndex = Math.floor(startIndex + (endIndex - startIndex) * progress);
        indexElement.textContent = currentIndex.toLocaleString();

        if (progress < 1) {
            requestAnimationFrame(animateIndex);
        }
    };

    animateIndex();
}

// Resolve stock market trade with volatility bonuses (3-5% win rate)
function resolveStockMarketTrade(results) {
    const marketSectorData = MARKET_SECTORS[stockMarketCrashGame.marketSector];
    const tradingStrategyData = TRADING_STRATEGIES[stockMarketCrashGame.tradingStrategy];

    let totalWinnings = 0;
    let resultMessage = '';

    // Check if player won
    const playerWon = results.playerWon;
    const movementPercent = Math.abs(results.marketMovement * 100);

    // Calculate base payout based on market movement
    if (playerWon) {
        if (results.crashOccurred && stockMarketCrashGame.trading.position === 'short') {
            totalWinnings = Math.floor(stockMarketCrashGame.betAmount * (STOCK_MARKET_PAYOUTS.CRASH_SURVIVOR / 100));
            resultMessage = 'Market crash predicted!';
        } else if (results.bubbleBurst && stockMarketCrashGame.trading.position === 'long') {
            totalWinnings = Math.floor(stockMarketCrashGame.betAmount * (STOCK_MARKET_PAYOUTS.BULL_RUN / 100));
            resultMessage = 'Bubble burst profit!';
        } else if (movementPercent >= 15) {
            totalWinnings = Math.floor(stockMarketCrashGame.betAmount * (STOCK_MARKET_PAYOUTS.BULL_RUN / 100));
            resultMessage = 'Major market move!';
        } else if (movementPercent >= 10) {
            totalWinnings = Math.floor(stockMarketCrashGame.betAmount * (STOCK_MARKET_PAYOUTS.BEAR_MARKET / 100));
            resultMessage = 'Strong market move!';
        } else if (movementPercent >= 5) {
            totalWinnings = Math.floor(stockMarketCrashGame.betAmount * (STOCK_MARKET_PAYOUTS.MARKET_CORRECTION / 100));
            resultMessage = 'Market correction profit!';
        } else {
            totalWinnings = Math.floor(stockMarketCrashGame.betAmount * (STOCK_MARKET_PAYOUTS.SIDEWAYS_MARKET / 100));
            resultMessage = 'Sideways market profit!';
        }
    }

    // Apply volatility bonuses (actually work)
    if (stockMarketCrashGame.market.volatility >= 0.80 && totalWinnings > 0) {
        const volatilityBonus = Math.floor(totalWinnings * STOCK_MARKET_PAYOUTS.VOLATILITY_BONUS);
        totalWinnings += volatilityBonus;
        resultMessage += ' + Volatility Bonus!';
        stockMarketCrashGame.stats.volatilityEvents++;
    }

    // Apply sentiment bonus
    if (stockMarketCrashGame.market.sentiment >= 0.75 && totalWinnings > 0) {
        const sentimentBonus = Math.floor(totalWinnings * STOCK_MARKET_PAYOUTS.SENTIMENT_BONUS);
        totalWinnings += sentimentBonus;
        resultMessage += ' + Sentiment Bonus!';
    }

    // Apply momentum bonus
    if (stockMarketCrashGame.market.momentum >= 0.80 && totalWinnings > 0) {
        const momentumBonus = Math.floor(totalWinnings * STOCK_MARKET_PAYOUTS.MOMENTUM_BONUS);
        totalWinnings += momentumBonus;
        resultMessage += ' + Momentum Bonus!';
    }

    // Apply liquidity bonus
    if (stockMarketCrashGame.market.liquidity >= 0.85 && totalWinnings > 0) {
        const liquidityBonus = Math.floor(totalWinnings * STOCK_MARKET_PAYOUTS.LIQUIDITY_BONUS);
        totalWinnings += liquidityBonus;
        resultMessage += ' + Liquidity Bonus!';
    }

    // Apply market sector multiplier
    totalWinnings = Math.floor(totalWinnings * marketSectorData.payoutMultiplier);

    // Apply trading strategy multiplier
    totalWinnings = Math.floor(totalWinnings * tradingStrategyData.returnMultiplier);

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation
        totalWinnings = Math.floor(stockMarketCrashGame.betAmount * 0.6); // 60% return
        resultMessage = 'Market analysis reward';
    }

    // Award trading points
    const pointsEarned = playerWon ? (results.crashOccurred || results.bubbleBurst ? 8 : 5) : 2;
    stockMarketCrashGame.stats.tradingPoints += pointsEarned;

    // Update portfolio value
    stockMarketCrashGame.market.portfolioValue += totalWinnings;
    stockMarketCrashGame.stats.portfolioValue = stockMarketCrashGame.market.portfolioValue;

    // Track best return
    if (totalWinnings > stockMarketCrashGame.stats.bestReturn) {
        stockMarketCrashGame.stats.bestReturn = totalWinnings;
    }

    // Add winnings to balance
    balance += totalWinnings;
    stockMarketCrashGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterTrade(playerWon, totalWinnings);

    if (!resultMessage) {
        resultMessage = playerWon ? `${stockMarketCrashGame.trading.position.toUpperCase()} position profitable!` :
                                   `Market moved against ${stockMarketCrashGame.trading.position.toUpperCase()} position`;
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Sector: ${MARKET_SECTORS[stockMarketCrashGame.marketSector].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    // Show market event
    document.getElementById('marketEvent').classList.remove('hidden');
    document.getElementById('marketEvent').textContent = results.crashOccurred ? 'MARKET CRASH!' :
                                                        results.bubbleBurst ? 'BUBBLE BURST!' : 'TRADE EXECUTED!';
    document.getElementById('marketEvent').className = `text-sm font-bold ${totalWinnings > 0 ? 'text-green-400' : 'text-red-400'} animate-pulse`;

    setTimeout(() => resetGame(), 6000);
}

// Update market display
function updateMarketDisplay() {
    updateMarketStatus();
    updateTradingDisplay();
}

// Update market status
function updateMarketStatus() {
    document.getElementById('volatility').innerHTML =
        `<div class="text-red-400 font-bold">VOLATILITY: ${Math.floor(stockMarketCrashGame.market.volatility * 100)}%</div>`;
    document.getElementById('sentiment').innerHTML =
        `<div class="text-blue-400 font-bold">SENTIMENT: ${Math.floor(stockMarketCrashGame.market.sentiment * 100)}%</div>`;
    document.getElementById('momentum').innerHTML =
        `<div class="text-green-400 font-bold">MOMENTUM: ${Math.floor(stockMarketCrashGame.market.momentum * 100)}%</div>`;
    document.getElementById('tradingLevel').innerHTML =
        `<div class="text-yellow-400 font-bold">LEVEL: ${stockMarketCrashGame.market.tradingLevel.toUpperCase()}</div>`;

    document.getElementById('marketVolatility').querySelector('.text-2xl').textContent =
        `${Math.floor(stockMarketCrashGame.market.volatility * 100)}%`;

    document.getElementById('crashRisk').querySelector('.text-2xl').textContent =
        `${Math.floor(stockMarketCrashGame.market.marketCrashRisk * 100)}%`;

    document.getElementById('marketSentiment').querySelector('.text-2xl').textContent =
        `${Math.floor(stockMarketCrashGame.market.sentiment * 100)}%`;
}

// Update trading display
function updateTradingDisplay() {
    document.getElementById('tradingVolume').textContent =
        (stockMarketCrashGame.trading.tradingVolume / 1000000).toFixed(1) + 'M';
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${stockMarketCrashGame.betAmount}`;
    document.getElementById('tradingPointsDisplay').textContent = stockMarketCrashGame.stats.tradingPoints;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('tradesExecuted').textContent = stockMarketCrashGame.stats.tradesExecuted;
    document.getElementById('winRate').textContent = `${stockMarketCrashGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${stockMarketCrashGame.stats.totalWagered}`;
    document.getElementById('marketCrashes').textContent = stockMarketCrashGame.stats.marketCrashes;
    document.getElementById('volatilityEvents').textContent = stockMarketCrashGame.stats.volatilityEvents;

    const netResult = stockMarketCrashGame.stats.totalWon - stockMarketCrashGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-red-400' : 'text-red-400'}`;
}

// Update stats after trade
function updateGameStatsAfterTrade(won, winnings) {
    stockMarketCrashGame.stats.tradesExecuted++;
    stockMarketCrashGame.stats.totalWagered += stockMarketCrashGame.betAmount;
    stockMarketCrashGame.stats.totalWon += winnings;

    if (won) {
        stockMarketCrashGame.stats.profitableTrades++;
        stockMarketCrashGame.stats.currentStreak++;
        stockMarketCrashGame.streakData.currentWinStreak++;
        stockMarketCrashGame.streakData.currentLossStreak = 0;

        if (stockMarketCrashGame.streakData.currentWinStreak > stockMarketCrashGame.streakData.longestWinStreak) {
            stockMarketCrashGame.streakData.longestWinStreak = stockMarketCrashGame.streakData.currentWinStreak;
        }

        if (winnings > stockMarketCrashGame.stats.biggestWin) {
            stockMarketCrashGame.stats.biggestWin = winnings;
        }
    } else {
        stockMarketCrashGame.stats.currentStreak = 0;
        stockMarketCrashGame.streakData.currentWinStreak = 0;
        stockMarketCrashGame.streakData.currentLossStreak++;

        if (stockMarketCrashGame.streakData.currentLossStreak > stockMarketCrashGame.streakData.longestLossStreak) {
            stockMarketCrashGame.streakData.longestLossStreak = stockMarketCrashGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to market volatility)
    stockMarketCrashGame.stats.winRate = (stockMarketCrashGame.stats.profitableTrades / stockMarketCrashGame.stats.tradesExecuted) * 100;

    updateGameStats();
}

// Reset game for next trading session
function resetGame() {
    stockMarketCrashGame.isPlaying = false;
    stockMarketCrashGame.betAmount = 0;
    stockMarketCrashGame.totalBet = 0;
    stockMarketCrashGame.gameResult = '';
    stockMarketCrashGame.totalWin = 0;

    // Reset trading system
    stockMarketCrashGame.trading.selectedStocks = [];
    stockMarketCrashGame.trading.position = '';
    stockMarketCrashGame.trading.gamePhase = 'analysis';
    stockMarketCrashGame.trading.marketMovement = 0;
    stockMarketCrashGame.trading.gameResult = '';
    stockMarketCrashGame.trading.crashOccurred = false;
    stockMarketCrashGame.trading.bubbleBurst = false;
    stockMarketCrashGame.trading.tradingVolume = 0;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('marketEvent').classList.add('hidden');
    document.getElementById('tradingActions').classList.add('hidden');

    // Reset position display
    document.getElementById('currentPosition').textContent = 'NONE';
    document.getElementById('profitLoss').textContent = '$0';
    document.getElementById('profitLoss').className = 'text-xl font-bold text-green-400';

    // Reset analysis display
    document.getElementById('positionStatus').textContent = 'Ready to enter market...';
    document.getElementById('analysisBar').style.width = '0%';
    document.getElementById('analysisStatus').textContent = 'Ready';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable enter market button
    document.getElementById('enterMarket').disabled = false;
    document.getElementById('executeTradeBtn').disabled = true;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Market ready for analysis...';
    document.getElementById('gameMessage').textContent = 'Welcome to Stock Market Crash - Wall Street Volatility Simulator';

    // Reinitialize systems for next session
    initializeMarketSystems();
    initializeChart();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadStockMarketCrashGame();
});