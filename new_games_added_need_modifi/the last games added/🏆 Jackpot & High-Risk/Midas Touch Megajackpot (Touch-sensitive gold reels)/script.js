// Midas Touch Megajackpot - Touch-Sensitive Gold Reels
// Ultra High House Edge Implementation with King Midas Theme
// Designed to maintain <0.01% player win rate

// Game state
let balance = 1000;

// Game state object with Midas touch system
let midasTouchGame = {
    isPlaying: false,
    touchMode: 'cursed', // blessed, neutral, cursed, doomed, apocalyptic
    goldLevel: 'fool', // fool, bronze, silver, gold, midas
    betAmount: 0,
    totalBet: 0,

    // Midas touch system
    midas: {
        currentTouch: 0,
        maxTouch: 100,
        goldCurse: 0.99, // 99% curse probability
        touchSensitivity: 0.95, // 95% touch sensitivity
        greedLevel: 0.98, // 98% greed level
        hubrisPenalty: 0.90, // 90% hubris penalty
        divineWrath: false,
        goldenPrison: false,
        starvationRisk: 0.95, // 95% starvation risk
        isolationLevel: 0.88 // 88% isolation
    },

    // Mythological elements
    mythology: {
        god: 'Dionysus',
        wish: 'Golden Touch',
        consequence: 'Everything Turns to Gold',
        victim: 'Beloved Daughter',
        lesson: 'Greed Destroys Love',
        kingdom: 'Phrygia',
        river: 'Pactolus',
        redemption: 'Impossible',
        fate: 'Eternal Suffering'
    },

    // Reel system (5 reels, 5 rows for maximum gold)
    reels: [
        [], [], [], [], []
    ],

    // Touch-sensitive gold system
    touchSensitive: {
        active: false,
        touchedReels: [],
        goldTransformation: 0,
        cursedTouch: 0.98, // 98% cursed touch
        midasMultiplier: 1.0,
        touchPenalty: 0.95, // 95% touch penalty
        goldDecay: 0.90, // 90% gold decay
        touchCooldown: 0,
        divineIntervention: 0.02 // 2% divine intervention
    },

    // Golden symbols with mythological significance
    symbols: [
        { name: 'Midas Crown', value: 100, rarity: 0.001, touchEffect: 'curse', goldValue: 1000 },
        { name: 'Golden Daughter', value: 95, rarity: 0.002, touchEffect: 'tragedy', goldValue: 950 },
        { name: 'Golden Throne', value: 90, rarity: 0.003, touchEffect: 'isolation', goldValue: 900 },
        { name: 'Golden Scepter', value: 85, rarity: 0.005, touchEffect: 'power', goldValue: 850 },
        { name: 'Golden Chalice', value: 80, rarity: 0.008, touchEffect: 'thirst', goldValue: 800 },
        { name: 'Golden Food', value: 75, rarity: 0.012, touchEffect: 'starvation', goldValue: 750 },
        { name: 'Golden Rose', value: 70, rarity: 0.018, touchEffect: 'beauty_lost', goldValue: 700 },
        { name: 'Golden Coin', value: 65, rarity: 0.025, touchEffect: 'greed', goldValue: 650 },
        { name: 'Golden Ring', value: 60, rarity: 0.035, touchEffect: 'binding', goldValue: 600 },
        { name: 'Golden Apple', value: 55, rarity: 0.050, touchEffect: 'temptation', goldValue: 550 },
        { name: 'Golden Leaf', value: 50, rarity: 0.070, touchEffect: 'nature_death', goldValue: 500 },
        { name: 'Golden Feather', value: 45, rarity: 0.090, touchEffect: 'flight_lost', goldValue: 450 },
        { name: 'Golden Pebble', value: 40, rarity: 0.120, touchEffect: 'weight', goldValue: 400 },
        { name: 'Golden Dust', value: 35, rarity: 0.150, touchEffect: 'decay', goldValue: 350 },
        { name: 'Fool\'s Gold', value: 30, rarity: 0.200, touchEffect: 'deception', goldValue: 1 },
        { name: 'Cursed Touch', value: 1, rarity: 0.300, touchEffect: 'doom', goldValue: 0 }
    ],

    gameResult: '',
    totalWin: 0,

    // Megajackpot system (almost impossible to win)
    megajackpot: {
        current: 1000000, // $1M jackpot
        winChance: 0.0001, // 0.01% chance
        requirement: 'FIVE_MIDAS_CROWNS',
        cursePenalty: 0.999, // 99.9% curse penalty
        divineBlocking: 0.9999 // 99.99% divine blocking
    },

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        touchesAttempted: 0,
        goldTransformed: 0,
        cursesTriggered: 0,
        divineWrath: 0,
        megajackpotAttempts: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Touch modes with extreme curse bias
const TOUCH_MODES = {
    blessed: {
        name: 'Divine Blessing',
        curseRisk: 0.80, // 80% curse risk even when blessed
        touchPenalty: 0.70, // 70% touch penalty
        payoutMultiplier: 0.20, // Severely reduced payouts
        goldDecay: 0.60, // 60% gold decay
        divineWrath: 0.50 // 50% divine wrath
    },
    neutral: {
        name: 'Mortal Touch',
        curseRisk: 0.90, // 90% curse risk
        touchPenalty: 0.80, // 80% touch penalty
        payoutMultiplier: 0.15, // Even more reduced payouts
        goldDecay: 0.75, // 75% gold decay
        divineWrath: 0.70 // 70% divine wrath
    },
    cursed: {
        name: 'Cursed Touch',
        curseRisk: 0.95, // 95% curse risk
        touchPenalty: 0.90, // 90% touch penalty
        payoutMultiplier: 0.10, // Extremely reduced payouts
        goldDecay: 0.85, // 85% gold decay
        divineWrath: 0.85 // 85% divine wrath
    },
    doomed: {
        name: 'Doomed Touch',
        curseRisk: 0.98, // 98% curse risk
        touchPenalty: 0.95, // 95% touch penalty
        payoutMultiplier: 0.05, // Brutally reduced payouts
        goldDecay: 0.92, // 92% gold decay
        divineWrath: 0.95 // 95% divine wrath
    },
    apocalyptic: {
        name: 'Apocalyptic Touch',
        curseRisk: 0.999, // 99.9% curse risk
        touchPenalty: 0.99, // 99% touch penalty
        payoutMultiplier: 0.01, // Impossibly reduced payouts
        goldDecay: 0.98, // 98% gold decay
        divineWrath: 0.99 // 99% divine wrath
    }
};

const GOLD_LEVELS = {
    fool: {
        name: 'Fool\'s Gold',
        multiplier: 0.50,
        touchSensitivity: 0.90,
        curseResistance: 0.05
    },
    bronze: {
        name: 'Bronze Touch',
        multiplier: 0.70,
        touchSensitivity: 0.85,
        curseResistance: 0.10
    },
    silver: {
        name: 'Silver Touch',
        multiplier: 0.85,
        touchSensitivity: 0.80,
        curseResistance: 0.15
    },
    gold: {
        name: 'Golden Touch',
        multiplier: 1.00,
        touchSensitivity: 0.75,
        curseResistance: 0.20
    },
    midas: {
        name: 'Midas Touch',
        multiplier: 1.20,
        touchSensitivity: 0.70,
        curseResistance: 0.25
    }
};

// Severely reduced payout table with Midas theme
const MIDAS_PAYOUTS = {
    // Mythological combinations (heavily reduced)
    FIVE_MIDAS_CROWNS: 100000, // Reduced from 10000000:1 (Megajackpot trigger)
    FOUR_MIDAS_CROWNS: 50000, // Reduced from 5000000:1
    THREE_MIDAS_CROWNS: 10000, // Reduced from 1000000:1

    FIVE_GOLDEN_DAUGHTERS: 50000, // Reduced from 5000000:1
    FOUR_GOLDEN_DAUGHTERS: 25000, // Reduced from 2500000:1
    THREE_GOLDEN_DAUGHTERS: 5000, // Reduced from 500000:1

    FIVE_GOLDEN_THRONES: 25000, // Reduced from 2500000:1
    FOUR_GOLDEN_THRONES: 12500, // Reduced from 1250000:1
    THREE_GOLDEN_THRONES: 2500, // Reduced from 250000:1

    // Golden objects
    GOLDEN_FEAST: 1000, // Reduced from 100000:1
    GOLDEN_GARDEN: 500, // Reduced from 50000:1
    GOLDEN_PALACE: 250, // Reduced from 25000:1

    // Touch bonuses (fake - almost never apply)
    MIDAS_TOUCH_BONUS: 0.001, // 0.1% of displayed bonus
    DIVINE_BLESSING: 0.0005, // 0.05% of displayed bonus
    CURSE_REVERSAL: 0.0001 // 0.01% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadMidasTouchGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">MIDAS CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">TOUCH MODE</label>
                        <select id="touchMode" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="blessed">Divine Blessing</option>
                            <option value="neutral">Mortal Touch</option>
                            <option value="cursed" selected>Cursed Touch</option>
                            <option value="doomed">Doomed Touch</option>
                            <option value="apocalyptic">Apocalyptic Touch</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">GOLD LEVEL</label>
                        <select id="goldLevel" class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="fool">Fool's Gold</option>
                            <option value="bronze">Bronze Touch</option>
                            <option value="silver">Silver Touch</option>
                            <option value="gold" selected>Golden Touch</option>
                            <option value="midas">Midas Touch</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-yellow-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="spinGold" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        SPIN GOLDEN REELS
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Touch Sensitivity</div>
                        <div id="touchSensitivityDisplay" class="text-lg font-bold text-orange-400">95%</div>
                    </div>
                </div>

                <!-- Midas Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">MIDAS STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="curseStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">CURSE: ACTIVE</div>
                        </div>
                        <div id="greedLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">GREED: 98%</div>
                        </div>
                        <div id="divineWrath" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">WRATH: IMMINENT</div>
                        </div>
                        <div id="megajackpotStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">JACKPOT: $1M</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-yellow-400">GOLDEN PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Midas Treasures:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Midas Crowns:</span>
                            <span class="text-red-400">100000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Golden Daughters:</span>
                            <span class="text-red-400">50000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Golden Thrones:</span>
                            <span class="text-red-400">25000:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Golden Objects:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Golden Feast:</span>
                            <span class="text-red-400">1000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Golden Garden:</span>
                            <span class="text-red-400">500:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*99%+ curse probability</div>
                        <div class="text-xs text-red-400">*Divine wrath blocks wins</div>
                    </div>
                </div>
            </div>

            <!-- Main Golden Palace Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <!-- Golden Palace with Touch-Sensitive Reels -->
                    <div id="goldenPalace" class="relative bg-gradient-to-br from-black via-yellow-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Golden Background -->
                        <div id="goldenBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="goldenGradient" cx="50%" cy="30%" r="60%">
                                        <stop offset="0%" style="stop-color:#ffd700;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#ffb347;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#b8860b;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="goldenPattern" width="40" height="40" patternUnits="userSpaceOnUse">
                                        <circle cx="20" cy="20" r="8" fill="#ffd700" opacity="0.3"/>
                                        <circle cx="10" cy="10" r="4" fill="#ffb347" opacity="0.5"/>
                                        <circle cx="30" cy="30" r="4" fill="#ffb347" opacity="0.5"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#goldenPattern)" />
                                <ellipse id="goldenAura" cx="50%" cy="30%" rx="50%" ry="35%" fill="url(#goldenGradient)" class="animate-pulse" />
                                <g id="midasEffects">
                                    <!-- Midas touch effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Touch-Sensitive Reel Display Area (5x5 for maximum gold) -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-yellow-400 mb-2">TOUCH-SENSITIVE GOLDEN REELS</div>
                                <div id="reelsDisplay" class="grid grid-cols-5 gap-2">
                                    <!-- 5 reels, 5 rows each for maximum golden potential -->
                                    <div id="reel1" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="0" data-row="0"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="0" data-row="1"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="0" data-row="2"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="0" data-row="3"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="0" data-row="4"></div>
                                    </div>
                                    <div id="reel2" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="1" data-row="0"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="1" data-row="1"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="1" data-row="2"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="1" data-row="3"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="1" data-row="4"></div>
                                    </div>
                                    <div id="reel3" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="2" data-row="0"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="2" data-row="1"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="2" data-row="2"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="2" data-row="3"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="2" data-row="4"></div>
                                    </div>
                                    <div id="reel4" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="3" data-row="0"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="3" data-row="1"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="3" data-row="2"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="3" data-row="3"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="3" data-row="4"></div>
                                    </div>
                                    <div id="reel5" class="flex flex-col space-y-1">
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="4" data-row="0"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="4" data-row="1"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="4" data-row="2"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="4" data-row="3"></div>
                                        <div class="reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30" data-reel="4" data-row="4"></div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Touch symbols to activate Midas power</div>
                            </div>
                        </div>

                        <!-- Midas Touch Progress -->
                        <div id="midasProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-yellow-400 mb-2">MIDAS TOUCH POWER</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="touchBar" class="bg-gradient-to-r from-yellow-400 to-orange-500 h-4 rounded-full transition-all duration-1000 animate-pulse" style="width: 0%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Mortal</span>
                                    <span id="currentTouch">Touch: 0/100</span>
                                    <span>Divine</span>
                                </div>
                            </div>
                        </div>

                        <!-- Curse Indicator -->
                        <div id="curseIndicator" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">CURSE LEVEL</div>
                                <div class="w-20 bg-gray-700 rounded-full h-3">
                                    <div id="curseBar" class="bg-red-400 h-3 rounded-full transition-all duration-300 animate-pulse" style="width: 99%"></div>
                                </div>
                                <div class="text-xs text-red-400 mt-1">99% Cursed</div>
                            </div>
                        </div>

                        <!-- Megajackpot Display -->
                        <div id="megajackpotDisplay" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">MEGAJACKPOT</div>
                                <div class="text-lg font-bold text-yellow-400">$1,000,000</div>
                                <div class="text-xs text-gray-400 mt-1">0.01% Chance</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Golden reels await your touch...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="midasEvent" class="text-sm font-bold text-orange-400 hidden animate-pulse">MIDAS TOUCH ACTIVATED!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Midas Touch Megajackpot - Everything You Touch Turns to Gold</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Gold Transformed</div>
                <div id="goldTransformed" class="text-xl font-bold text-yellow-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 text-center">
                <div class="text-sm text-gray-400">Curses Triggered</div>
                <div id="cursesTriggered" class="text-xl font-bold text-red-400">0</div>
            </div>
        </div>
    `;

    initializeMidasTouch();
}

// Initialize the game
function initializeMidasTouch() {
    document.getElementById('spinGold').addEventListener('click', startGoldenSpin);

    // Add touch listeners to all reel symbols
    const reelSymbols = document.querySelectorAll('.reel-symbol');
    reelSymbols.forEach(symbol => {
        symbol.addEventListener('click', (e) => {
            const reel = parseInt(e.target.dataset.reel);
            const row = parseInt(e.target.dataset.row);
            activateMidasTouch(reel, row, e.target);
        });
    });

    // Initialize Midas systems
    initializeMidasSystems();
    generateMidasEffects();
    updateGameStats();
}

// Initialize Midas systems
function initializeMidasSystems() {
    // Reset Midas system
    midasTouchGame.midas.currentTouch = 0;
    midasTouchGame.midas.goldCurse = 0.99;
    midasTouchGame.midas.touchSensitivity = 0.95;
    midasTouchGame.midas.greedLevel = 0.98;
    midasTouchGame.midas.hubrisPenalty = 0.90;
    midasTouchGame.midas.divineWrath = false;
    midasTouchGame.midas.goldenPrison = false;
    midasTouchGame.midas.starvationRisk = 0.95;
    midasTouchGame.midas.isolationLevel = 0.88;

    // Reset mythology elements
    midasTouchGame.mythology.god = getRandomGod();
    midasTouchGame.mythology.wish = 'Golden Touch';
    midasTouchGame.mythology.consequence = getRandomConsequence();
    midasTouchGame.mythology.victim = getRandomVictim();
    midasTouchGame.mythology.lesson = getRandomLesson();
    midasTouchGame.mythology.kingdom = getRandomKingdom();
    midasTouchGame.mythology.river = 'Pactolus';
    midasTouchGame.mythology.redemption = 'Impossible';
    midasTouchGame.mythology.fate = getRandomFate();

    // Reset touch-sensitive system
    midasTouchGame.touchSensitive.active = false;
    midasTouchGame.touchSensitive.touchedReels = [];
    midasTouchGame.touchSensitive.goldTransformation = 0;
    midasTouchGame.touchSensitive.cursedTouch = 0.98;
    midasTouchGame.touchSensitive.midasMultiplier = 1.0;
    midasTouchGame.touchSensitive.touchPenalty = 0.95;
    midasTouchGame.touchSensitive.goldDecay = 0.90;
    midasTouchGame.touchSensitive.touchCooldown = 0;
    midasTouchGame.touchSensitive.divineIntervention = 0.02;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        midasTouchGame.reels[i] = [];
    }

    updateMidasDisplay();
}

// Generate Midas effects
function generateMidasEffects() {
    const container = document.getElementById('midasEffects');
    container.innerHTML = '';

    for (let i = 0; i < 10; i++) {
        const effect = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        effect.setAttribute('cx', `${Math.random() * 100}%`);
        effect.setAttribute('cy', `${Math.random() * 100}%`);
        effect.setAttribute('r', `${Math.random() * 5 + 2}%`);
        effect.setAttribute('fill', '#ffd700');
        effect.setAttribute('opacity', '0.4');
        effect.classList.add('animate-pulse');
        effect.style.animationDelay = `${i * 0.3}s`;
        container.appendChild(effect);
    }
}

// Get random mythological elements
function getRandomGod() {
    const gods = ['Dionysus', 'Apollo', 'Zeus', 'Hermes', 'Hades'];
    return gods[Math.floor(Math.random() * gods.length)];
}

function getRandomConsequence() {
    const consequences = ['Everything Turns to Gold', 'Loved Ones Become Statues', 'Food Becomes Inedible', 'Touch Brings Death'];
    return consequences[Math.floor(Math.random() * consequences.length)];
}

function getRandomVictim() {
    const victims = ['Beloved Daughter', 'Faithful Wife', 'Loyal Servant', 'Innocent Child'];
    return victims[Math.floor(Math.random() * victims.length)];
}

function getRandomLesson() {
    const lessons = ['Greed Destroys Love', 'Wealth Cannot Buy Happiness', 'Power Corrupts', 'Hubris Brings Downfall'];
    return lessons[Math.floor(Math.random() * lessons.length)];
}

function getRandomKingdom() {
    const kingdoms = ['Phrygia', 'Lydia', 'Gordium', 'Pessinus'];
    return kingdoms[Math.floor(Math.random() * kingdoms.length)];
}

function getRandomFate() {
    const fates = ['Eternal Suffering', 'Divine Punishment', 'Cursed Forever', 'Tragic End'];
    return fates[Math.floor(Math.random() * fates.length)];
}

// Start golden spin
function startGoldenSpin() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    midasTouchGame.isPlaying = true;
    midasTouchGame.betAmount = betAmount;
    midasTouchGame.totalBet = betAmount;
    midasTouchGame.touchMode = document.getElementById('touchMode').value;
    midasTouchGame.goldLevel = document.getElementById('goldLevel').value;

    // Activate Midas systems
    activateMidasSystems();

    // Start reel spin
    setTimeout(() => {
        spinGoldenReels();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('spinGold').disabled = true;
    document.getElementById('gameStatus').textContent = 'Golden reels spinning...';
}

// Activate Midas systems
function activateMidasSystems() {
    const touchData = TOUCH_MODES[midasTouchGame.touchMode];
    const goldData = GOLD_LEVELS[midasTouchGame.goldLevel];

    midasTouchGame.midas.goldCurse = touchData.curseRisk;
    midasTouchGame.midas.touchSensitivity = goldData.touchSensitivity;
    midasTouchGame.touchSensitive.touchPenalty = touchData.touchPenalty;

    // Check for divine wrath activation (almost always)
    if (Math.random() < touchData.divineWrath) {
        activateDivineWrath();
    }

    // Check for golden prison
    if (Math.random() < midasTouchGame.midas.greedLevel) {
        activateGoldenPrison();
    }

    // Update Midas elements
    updateMidasElements();

    // Update visual effects
    updateMidasDisplay();
    updateTouchEffects();
}

// Activate divine wrath
function activateDivineWrath() {
    midasTouchGame.midas.divineWrath = true;
    midasTouchGame.stats.divineWrath++;

    document.getElementById('divineWrath').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">WRATH: ACTIVE!</div>';

    // Show Midas event
    document.getElementById('midasEvent').classList.remove('hidden');
    document.getElementById('midasEvent').textContent = 'DIVINE WRATH ACTIVATED!';
    setTimeout(() => {
        document.getElementById('midasEvent').classList.add('hidden');
    }, 3000);
}

// Activate golden prison
function activateGoldenPrison() {
    midasTouchGame.midas.goldenPrison = true;

    document.getElementById('greedLevel').innerHTML =
        '<div class="text-orange-400 font-bold animate-pulse">GREED: IMPRISONED!</div>';
}

// Update Midas elements
function updateMidasElements() {
    document.getElementById('curseStatus').innerHTML =
        '<div class="text-red-400 font-bold">CURSE: ACTIVE</div>';
    document.getElementById('megajackpotStatus').innerHTML =
        `<div class="text-green-400 font-bold">JACKPOT: $${midasTouchGame.megajackpot.current.toLocaleString()}</div>`;
}

// Spin golden reels with extreme curse bias
function spinGoldenReels() {
    document.getElementById('gameStatus').textContent = 'Reels spinning with golden magic...';

    // Generate symbols for each reel with extreme bias toward cursed symbols
    for (let reel = 0; reel < 5; reel++) {
        midasTouchGame.reels[reel] = [];
        for (let row = 0; row < 5; row++) { // 5 rows for maximum gold potential
            const symbol = generateBiasedGoldenSymbol();
            midasTouchGame.reels[reel].push(symbol);
        }
    }

    // Animate reel spinning
    animateGoldenReelSpin();

    // Resolve after reels stop
    setTimeout(() => {
        resolveMidasSpin();
    }, 4000);
}

// Generate biased golden symbol
function generateBiasedGoldenSymbol() {
    const touchData = TOUCH_MODES[midasTouchGame.touchMode];

    // Apply extreme bias toward cursed symbols
    if (Math.random() < touchData.curseRisk) {
        // Bias toward cursed, low-value symbols
        const cursedSymbols = midasTouchGame.symbols.filter(s => s.touchEffect === 'curse' || s.touchEffect === 'doom' || s.name === 'Cursed Touch');
        if (cursedSymbols.length > 0) {
            return cursedSymbols[Math.floor(Math.random() * cursedSymbols.length)];
        }
    }

    // Random selection (still heavily biased by rarity)
    return selectSymbolByRarity();
}

// Select symbol by rarity (biased toward common/cursed)
function selectSymbolByRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Reverse order to favor common/cursed symbols
    for (let i = midasTouchGame.symbols.length - 1; i >= 0; i--) {
        const symbol = midasTouchGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to cursed touch
    return midasTouchGame.symbols[midasTouchGame.symbols.length - 1];
}

// Animate golden reel spinning
function animateGoldenReelSpin() {
    for (let reel = 0; reel < 5; reel++) {
        for (let row = 0; row < 5; row++) {
            const symbolElement = document.querySelector(`[data-reel="${reel}"][data-row="${row}"]`);

            // Add spinning animation
            symbolElement.classList.add('animate-spin');

            setTimeout(() => {
                symbolElement.classList.remove('animate-spin');
                displaySymbolInReel(midasTouchGame.reels[reel][row], symbolElement);
            }, 1000 + reel * 300 + row * 100);
        }
    }
}

// Display symbol in reel
function displaySymbolInReel(symbol, element) {
    const symbolIcon = getGoldenSymbolIcon(symbol.name);

    element.innerHTML = `
        <div class="text-lg">${symbolIcon}</div>
        <div class="text-xs text-white font-bold">${symbol.name.split(' ')[0]}</div>
    `;

    // Add symbol-specific styling
    if (symbol.name === 'Midas Crown') {
        element.classList.add('ring-2', 'ring-yellow-400', 'animate-pulse', 'bg-yellow-900/50');
    } else if (symbol.touchEffect === 'curse' || symbol.touchEffect === 'doom') {
        element.classList.add('ring-2', 'ring-red-400', 'animate-pulse', 'bg-red-900/50');
    } else if (symbol.value >= 80) {
        element.classList.add('ring-1', 'ring-yellow-400');
    }
}

// Get golden symbol icon
function getGoldenSymbolIcon(symbolName) {
    const icons = {
        'Midas Crown': '👑',
        'Golden Daughter': '👧',
        'Golden Throne': '🪑',
        'Golden Scepter': '🪄',
        'Golden Chalice': '🏆',
        'Golden Food': '🍖',
        'Golden Rose': '🌹',
        'Golden Coin': '🪙',
        'Golden Ring': '💍',
        'Golden Apple': '🍎',
        'Golden Leaf': '🍃',
        'Golden Feather': '🪶',
        'Golden Pebble': '🪨',
        'Golden Dust': '✨',
        'Fool\'s Gold': '🥉',
        'Cursed Touch': '☠️'
    };

    return icons[symbolName] || '💰';
}

// Activate Midas touch on symbol
function activateMidasTouch(reel, row, element) {
    if (!midasTouchGame.isPlaying) return;

    const touchData = TOUCH_MODES[midasTouchGame.touchMode];
    midasTouchGame.stats.touchesAttempted++;

    // Check if touch is cursed (almost always)
    if (Math.random() < midasTouchGame.touchSensitive.cursedTouch) {
        // Cursed touch - apply penalties
        applyCursedTouch(element);
        midasTouchGame.stats.cursesTriggered++;

        // Show curse effect
        document.getElementById('midasEvent').classList.remove('hidden');
        document.getElementById('midasEvent').textContent = 'CURSED TOUCH!';
        setTimeout(() => {
            document.getElementById('midasEvent').classList.add('hidden');
        }, 2000);
    } else {
        // Blessed touch (very rare)
        applyBlessedTouch(element);
        midasTouchGame.stats.goldTransformed++;
    }

    // Update touch progress
    midasTouchGame.midas.currentTouch = Math.min(100, midasTouchGame.midas.currentTouch + 1);
    updateTouchProgress();
}

// Apply cursed touch
function applyCursedTouch(element) {
    element.classList.add('ring-4', 'ring-red-600', 'bg-red-900/70', 'animate-pulse');
    element.style.filter = 'brightness(0.3) sepia(1) hue-rotate(0deg)';

    // Add cursed symbol
    const cursedIcon = document.createElement('div');
    cursedIcon.className = 'absolute top-0 right-0 text-red-400 text-xs';
    cursedIcon.textContent = '💀';
    element.style.position = 'relative';
    element.appendChild(cursedIcon);
}

// Apply blessed touch
function applyBlessedTouch(element) {
    element.classList.add('ring-4', 'ring-yellow-400', 'bg-yellow-900/70', 'animate-pulse');
    element.style.filter = 'brightness(1.5) sepia(0.5) hue-rotate(45deg)';

    // Add golden symbol
    const goldenIcon = document.createElement('div');
    goldenIcon.className = 'absolute top-0 right-0 text-yellow-400 text-xs';
    goldenIcon.textContent = '✨';
    element.style.position = 'relative';
    element.appendChild(goldenIcon);
}

// Update touch progress
function updateTouchProgress() {
    const touchPercentage = midasTouchGame.midas.currentTouch;
    document.getElementById('touchBar').style.width = `${touchPercentage}%`;
    document.getElementById('currentTouch').textContent = `Touch: ${midasTouchGame.midas.currentTouch}/100`;
    document.getElementById('touchSensitivityDisplay').textContent = `${Math.floor(midasTouchGame.midas.touchSensitivity * 100)}%`;
}

// Resolve Midas spin with extreme curse bias
function resolveMidasSpin() {
    const touchData = TOUCH_MODES[midasTouchGame.touchMode];
    const goldData = GOLD_LEVELS[midasTouchGame.goldLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    // Calculate symbol winnings with extreme reductions
    const allSymbols = [];
    midasTouchGame.reels.forEach(reel => {
        allSymbols.push(...reel);
    });

    // Count symbol occurrences
    const symbolCounts = {};
    allSymbols.forEach(symbol => {
        symbolCounts[symbol.name] = (symbolCounts[symbol.name] || 0) + 1;
    });

    // Check for megajackpot (almost impossible)
    if (symbolCounts['Midas Crown'] >= 5) {
        midasTouchGame.stats.megajackpotAttempts++;

        // Apply extreme divine blocking
        if (Math.random() > midasTouchGame.megajackpot.divineBlocking) {
            // Megajackpot won (virtually impossible)
            totalWinnings = midasTouchGame.megajackpot.current;
            resultMessage = 'MEGAJACKPOT WON!';
        } else {
            // Divine intervention blocks megajackpot
            totalWinnings = Math.floor(midasTouchGame.betAmount * 0.1); // 10% consolation
            resultMessage = 'Divine intervention blocked megajackpot!';
        }
    } else {
        // Calculate regular payouts (severely reduced)
        Object.entries(symbolCounts).forEach(([symbolName, count]) => {
            if (count >= 3) { // Need at least 3 symbols
                const symbol = midasTouchGame.symbols.find(s => s.name === symbolName);
                if (symbol) {
                    const payoutKey = symbolName.toUpperCase().replace(/[^A-Z]/g, '_');
                    if (MIDAS_PAYOUTS[payoutKey]) {
                        let symbolPayout = midasTouchGame.betAmount * (MIDAS_PAYOUTS[payoutKey] / 100000); // Severely reduced

                        // Apply count multiplier (minimal)
                        if (count === 4) symbolPayout *= 1.2;
                        if (count === 5) symbolPayout *= 1.5;

                        totalWinnings += symbolPayout;
                    }
                }
            }
        });

        // Apply Midas touch bonus (fake - almost never applies)
        if (midasTouchGame.midas.currentTouch >= 50 && Math.random() < 0.001) {
            const touchBonus = Math.floor(totalWinnings * MIDAS_PAYOUTS.MIDAS_TOUCH_BONUS);
            totalWinnings += touchBonus;
        }

        resultMessage = `Golden transformation: ${midasTouchGame.stats.goldTransformed}`;
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * touchData.payoutMultiplier);

    // Apply gold level multiplier
    totalWinnings = Math.floor(totalWinnings * goldData.multiplier);

    // Apply curse penalty
    if (midasTouchGame.stats.cursesTriggered > 0) {
        const cursePenalty = midasTouchGame.stats.cursesTriggered * 0.1; // 10% per curse
        totalWinnings = Math.floor(totalWinnings * (1 - cursePenalty));
    }

    // Apply divine wrath penalty
    if (midasTouchGame.midas.divineWrath) {
        const wrathPenalty = touchData.divineWrath * 0.5;
        totalWinnings = Math.floor(totalWinnings * (1 - wrathPenalty));
    }

    // Apply golden prison penalty
    if (midasTouchGame.midas.goldenPrison) {
        totalWinnings = Math.floor(totalWinnings * 0.1); // 90% penalty
    }

    // Hubris can void all wins
    if (totalWinnings > 0 && Math.random() < midasTouchGame.midas.hubrisPenalty) {
        totalWinnings = 0;
        resultMessage += ' - Hubris destroyed everything!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Add winnings to balance
    balance += totalWinnings;
    midasTouchGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(totalWinnings > midasTouchGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Touch Mode: ${TOUCH_MODES[midasTouchGame.touchMode].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update Midas display
function updateMidasDisplay() {
    const touchData = TOUCH_MODES[midasTouchGame.touchMode];

    // Update curse bar
    document.getElementById('curseBar').style.width = `${midasTouchGame.midas.goldCurse * 100}%`;

    if (midasTouchGame.midas.divineWrath) {
        document.getElementById('divineWrath').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">WRATH: ACTIVE!</div>';
    } else {
        document.getElementById('divineWrath').innerHTML =
            '<div class="text-purple-400 font-bold">WRATH: IMMINENT</div>';
    }
}

// Update touch effects
function updateTouchEffects() {
    // Update touch sensitivity based on current mode
    const touchSensitivity = midasTouchGame.midas.touchSensitivity;
    document.getElementById('touchSensitivityDisplay').textContent = `${Math.floor(touchSensitivity * 100)}%`;
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${midasTouchGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = midasTouchGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${midasTouchGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${midasTouchGame.stats.totalWagered}`;
    document.getElementById('goldTransformed').textContent = midasTouchGame.stats.goldTransformed;
    document.getElementById('cursesTriggered').textContent = midasTouchGame.stats.cursesTriggered;

    const netResult = midasTouchGame.stats.totalWon - midasTouchGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    midasTouchGame.stats.spinsPlayed++;
    midasTouchGame.stats.totalWagered += midasTouchGame.betAmount;
    midasTouchGame.stats.totalWon += winnings;

    if (won) {
        midasTouchGame.stats.spinsWon++;
        midasTouchGame.stats.currentStreak++;
        midasTouchGame.streakData.currentWinStreak++;
        midasTouchGame.streakData.currentLossStreak = 0;

        if (midasTouchGame.streakData.currentWinStreak > midasTouchGame.streakData.longestWinStreak) {
            midasTouchGame.streakData.longestWinStreak = midasTouchGame.streakData.currentWinStreak;
        }

        if (winnings > midasTouchGame.stats.biggestWin) {
            midasTouchGame.stats.biggestWin = winnings;
        }
    } else {
        midasTouchGame.stats.currentStreak = 0;
        midasTouchGame.streakData.currentWinStreak = 0;
        midasTouchGame.streakData.currentLossStreak++;

        if (midasTouchGame.streakData.currentLossStreak > midasTouchGame.streakData.longestLossStreak) {
            midasTouchGame.streakData.longestLossStreak = midasTouchGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to Midas curse)
    midasTouchGame.stats.winRate = (midasTouchGame.stats.spinsWon / midasTouchGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next golden spin
function resetGame() {
    midasTouchGame.isPlaying = false;
    midasTouchGame.betAmount = 0;
    midasTouchGame.totalBet = 0;
    midasTouchGame.gameResult = '';
    midasTouchGame.totalWin = 0;

    // Reset Midas system
    midasTouchGame.midas.currentTouch = 0;
    midasTouchGame.midas.divineWrath = false;
    midasTouchGame.midas.goldenPrison = false;

    // Reset touch-sensitive system
    midasTouchGame.touchSensitive.active = false;
    midasTouchGame.touchSensitive.touchedReels = [];
    midasTouchGame.touchSensitive.goldTransformation = 0;
    midasTouchGame.touchSensitive.touchCooldown = 0;

    // Reset reels
    for (let i = 0; i < 5; i++) {
        midasTouchGame.reels[i] = [];
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('midasEvent').classList.add('hidden');

    // Reset reel displays
    const reelSymbols = document.querySelectorAll('.reel-symbol');
    reelSymbols.forEach(symbol => {
        symbol.innerHTML = '';
        symbol.className = 'reel-symbol w-12 h-12 bg-black/50 border border-yellow-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-yellow-900/30';
        symbol.style.filter = '';
        symbol.style.position = '';

        // Remove any added elements
        const addedElements = symbol.querySelectorAll('.absolute');
        addedElements.forEach(el => el.remove());
    });

    // Reset Midas status
    document.getElementById('curseStatus').innerHTML =
        '<div class="text-red-400 font-bold">CURSE: ACTIVE</div>';
    document.getElementById('greedLevel').innerHTML =
        '<div class="text-orange-400 font-bold">GREED: 98%</div>';
    document.getElementById('divineWrath').innerHTML =
        '<div class="text-purple-400 font-bold">WRATH: IMMINENT</div>';
    document.getElementById('megajackpotStatus').innerHTML =
        '<div class="text-green-400 font-bold">JACKPOT: $1M</div>';

    // Reset touch progress
    document.getElementById('touchBar').style.width = '0%';
    document.getElementById('currentTouch').textContent = 'Touch: 0/100';

    // Reset curse bar
    document.getElementById('curseBar').style.width = '99%';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable spin button
    document.getElementById('spinGold').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Golden reels await your touch...';
    document.getElementById('gameMessage').textContent = 'Welcome to Midas Touch Megajackpot - Everything You Touch Turns to Gold';
    document.getElementById('touchSensitivityDisplay').textContent = '95%';

    // Reinitialize systems for next game
    initializeMidasSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMidasTouchGame();
});