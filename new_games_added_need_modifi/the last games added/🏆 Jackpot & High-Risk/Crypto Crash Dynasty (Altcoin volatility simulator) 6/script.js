// Crypto Crash Dynasty - Altcoin Volatility Simulator
// Ultra High House Edge Implementation with Cryptocurrency Theme
// Designed to maintain <0.1% player win rate

// Game state
let balance = 1000;

// Game state object with crypto crash system
let cryptoCrashGame = {
    isPlaying: false,
    tradingMode: 'spot', // spot, futures, margin, defi, nft
    volatilityLevel: 'extreme', // low, medium, high, extreme, apocalyptic
    betAmount: 0,
    totalBet: 0,

    // Crypto market system
    market: {
        currentPrice: 1.0,
        startPrice: 1.0,
        multiplier: 1.0,
        crashed: false,
        crashPoint: 1.0,
        volatility: 0.95, // 95% volatility
        manipulation: 0.85, // 85% market manipulation
        whaleActivity: 0.90, // 90% whale activity
        rugPull: false,
        liquidation: false
    },

    // Cryptocurrency elements
    crypto: {
        selectedCoin: 'SCAMCOIN',
        portfolio: ['SCAMCOIN', 'RUGTOKEN', 'PONZICOIN', 'CRASHCOIN'],
        marketCap: 1000000,
        volume: 50000,
        holders: 1000,
        liquidity: 10000,
        devWallet: 0.50, // 50% dev wallet
        burned: 0.05, // 5% burned
        staked: 0.10 // 10% staked
    },

    // Altcoin volatility simulator
    altcoins: [
        { name: 'SCAMCOIN', symbol: 'SCAM', price: 0.001, change: -99.9, marketCap: 1000, risk: 0.99 },
        { name: 'RUGTOKEN', symbol: 'RUG', price: 0.0001, change: -99.99, marketCap: 100, risk: 0.999 },
        { name: 'PONZICOIN', symbol: 'PONZI', price: 0.00001, change: -99.999, marketCap: 10, risk: 0.9999 },
        { name: 'CRASHCOIN', symbol: 'CRASH', price: 0.000001, change: -100, marketCap: 1, risk: 1.0 },
        { name: 'MOONSHOT', symbol: 'MOON', price: 0.01, change: -95, marketCap: 10000, risk: 0.95 },
        { name: 'SAFECOIN', symbol: 'SAFE', price: 0.1, change: -90, marketCap: 100000, risk: 0.90 },
        { name: 'HODLTOKEN', symbol: 'HODL', price: 1.0, change: -80, marketCap: 1000000, risk: 0.80 },
        { name: 'DIAMONDCOIN', symbol: 'DIAMOND', price: 10.0, change: -70, marketCap: 10000000, risk: 0.70 }
    ],

    // Crash prediction system
    crashPrediction: {
        active: false,
        predictedCrash: 1.0,
        confidence: 0.05, // 5% confidence
        timeRemaining: 0,
        whaleSignals: [],
        technicalAnalysis: 'BEARISH',
        sentiment: 'EXTREME FEAR',
        fudLevel: 0.95 // 95% FUD
    },

    gameResult: '',
    totalWin: 0,
    cashoutMultiplier: 1.0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        crashesSurvived: 0,
        rugPulls: 0,
        liquidations: 0,
        whaleAttacks: 0,
        portfolioValue: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Trading modes with extreme crash bias
const TRADING_MODES = {
    spot: {
        name: 'Spot Trading',
        crashRisk: 0.85, // 85% crash risk
        liquidationRisk: 0.30, // 30% liquidation risk
        payoutMultiplier: 0.15, // Severely reduced payouts
        volatilityMultiplier: 1.2,
        manipulationLevel: 0.70 // 70% manipulation
    },
    futures: {
        name: 'Futures Trading',
        crashRisk: 0.92, // 92% crash risk
        liquidationRisk: 0.60, // 60% liquidation risk
        payoutMultiplier: 0.10, // Even more reduced payouts
        volatilityMultiplier: 1.8,
        manipulationLevel: 0.85 // 85% manipulation
    },
    margin: {
        name: 'Margin Trading',
        crashRisk: 0.96, // 96% crash risk
        liquidationRisk: 0.80, // 80% liquidation risk
        payoutMultiplier: 0.08, // Extremely reduced payouts
        volatilityMultiplier: 2.5,
        manipulationLevel: 0.90 // 90% manipulation
    },
    defi: {
        name: 'DeFi Yield Farming',
        crashRisk: 0.98, // 98% crash risk
        liquidationRisk: 0.90, // 90% liquidation risk
        payoutMultiplier: 0.05, // Brutally reduced payouts
        volatilityMultiplier: 3.0,
        manipulationLevel: 0.95 // 95% manipulation
    },
    nft: {
        name: 'NFT Flipping',
        crashRisk: 0.995, // 99.5% crash risk
        liquidationRisk: 0.95, // 95% liquidation risk
        payoutMultiplier: 0.02, // Impossibly reduced payouts
        volatilityMultiplier: 5.0,
        manipulationLevel: 0.99 // 99% manipulation
    }
};

const VOLATILITY_LEVELS = {
    low: {
        name: 'Low Volatility',
        crashMultiplier: 0.80,
        priceSwing: 0.20,
        rugPullChance: 0.30
    },
    medium: {
        name: 'Medium Volatility',
        crashMultiplier: 0.85,
        priceSwing: 0.40,
        rugPullChance: 0.50
    },
    high: {
        name: 'High Volatility',
        crashMultiplier: 0.90,
        priceSwing: 0.60,
        rugPullChance: 0.70
    },
    extreme: {
        name: 'Extreme Volatility',
        crashMultiplier: 0.95,
        priceSwing: 0.80,
        rugPullChance: 0.85
    },
    apocalyptic: {
        name: 'Apocalyptic Volatility',
        crashMultiplier: 0.99,
        priceSwing: 0.95,
        rugPullChance: 0.95
    }
};

// Severely reduced payout table with crypto theme
const CRYPTO_PAYOUTS = {
    // Multiplier thresholds (heavily reduced)
    MOON_SHOT: 1000, // Reduced from 100000:1 (if you survive to 100x)
    DIAMOND_HANDS: 500, // Reduced from 50000:1 (if you survive to 50x)
    HODL_STRONG: 100, // Reduced from 10000:1 (if you survive to 10x)
    PAPER_HANDS: 10, // Reduced from 1000:1 (if you survive to 2x)

    // Crash survival bonuses (fake - almost never apply)
    CRASH_SURVIVOR: 0.05, // 5% of displayed bonus
    WHALE_RESISTANCE: 0.02, // 2% of displayed bonus
    RUG_PULL_ESCAPE: 0.01 // 1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadCryptoCrashGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">CRYPTO CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">TRADING MODE</label>
                        <select id="tradingMode" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="spot">Spot Trading</option>
                            <option value="futures">Futures Trading</option>
                            <option value="margin">Margin Trading</option>
                            <option value="defi">DeFi Yield Farming</option>
                            <option value="nft">NFT Flipping</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VOLATILITY LEVEL</label>
                        <select id="volatilityLevel" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="low">Low Volatility</option>
                            <option value="medium">Medium Volatility</option>
                            <option value="high">High Volatility</option>
                            <option value="extreme" selected>Extreme Volatility</option>
                            <option value="apocalyptic">Apocalyptic Volatility</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">ALTCOIN SELECTION</label>
                        <select id="altcoinSelection" class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="SCAMCOIN">SCAMCOIN (99% risk)</option>
                            <option value="RUGTOKEN">RUGTOKEN (99.9% risk)</option>
                            <option value="PONZICOIN">PONZICOIN (99.99% risk)</option>
                            <option value="CRASHCOIN">CRASHCOIN (100% risk)</option>
                            <option value="MOONSHOT">MOONSHOT (95% risk)</option>
                            <option value="SAFECOIN">SAFECOIN (90% risk)</option>
                            <option value="HODLTOKEN">HODLTOKEN (80% risk)</option>
                            <option value="DIAMONDCOIN">DIAMONDCOIN (70% risk)</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-green-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="startTrading" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        START CRYPTO TRADING
                    </button>

                    <button id="cashOut" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white hidden">
                        CASH OUT NOW!
                    </button>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Current Multiplier</div>
                        <div id="currentMultiplierDisplay" class="text-lg font-bold text-green-400">1.00x</div>
                    </div>
                </div>

                <!-- Crypto Market Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">MARKET STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="marketSentiment" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">SENTIMENT: EXTREME FEAR</div>
                        </div>
                        <div id="whaleActivity" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">WHALES: DUMPING</div>
                        </div>
                        <div id="rugPullRisk" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">RUG PULL: IMMINENT</div>
                        </div>
                        <div id="liquidationRisk" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">LIQUIDATION: HIGH</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-green-400">CRYPTO PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Survival Rewards:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Moon Shot (100x):</span>
                            <span class="text-red-400">1000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Diamond Hands (50x):</span>
                            <span class="text-red-400">500:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">HODL Strong (10x):</span>
                            <span class="text-red-400">100:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Early Exit:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Paper Hands (2x):</span>
                            <span class="text-red-400">10:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*99%+ crash probability</div>
                        <div class="text-xs text-red-400">*Whale manipulation active</div>
                    </div>
                </div>
            </div>

            <!-- Main Crypto Trading Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <!-- Crypto Trading Chart -->
                    <div id="cryptoChart" class="relative bg-gradient-to-br from-black via-green-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Trading Chart Background -->
                        <div id="chartBackground" class="absolute inset-0 pointer-events-none opacity-30">
                            <svg class="w-full h-full">
                                <defs>
                                    <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#00ff00;stop-opacity:0.6" />
                                        <stop offset="50%" style="stop-color:#ffff00;stop-opacity:0.3" />
                                        <stop offset="100%" style="stop-color:#ff0000;stop-opacity:0.8" />
                                    </linearGradient>
                                    <pattern id="gridPattern" width="50" height="50" patternUnits="userSpaceOnUse">
                                        <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#00ff00" stroke-width="1" opacity="0.2"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#gridPattern)" />
                                <g id="priceChart">
                                    <!-- Price chart will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Price Display -->
                        <div class="absolute top-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-green-400 mb-1">CURRENT PRICE</div>
                                <div id="currentPrice" class="text-2xl font-bold text-white">$1.00</div>
                                <div id="priceChange" class="text-sm font-bold text-red-400">-99.9%</div>
                            </div>
                        </div>

                        <!-- Multiplier Display -->
                        <div class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">MULTIPLIER</div>
                                <div id="multiplierDisplay" class="text-3xl font-bold text-yellow-400">1.00x</div>
                                <div id="potentialWin" class="text-sm text-green-400">Potential: $0</div>
                            </div>
                        </div>

                        <!-- Crash Warning -->
                        <div id="crashWarning" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 hidden">
                            <div class="bg-red-900/90 border-2 border-red-400 rounded-lg p-6 text-center animate-pulse">
                                <div class="text-2xl font-bold text-red-400 mb-2">⚠️ CRASH IMMINENT ⚠️</div>
                                <div class="text-lg text-white">Whales are dumping!</div>
                                <div class="text-sm text-red-300 mt-2">Cash out now or lose everything!</div>
                            </div>
                        </div>

                        <!-- Market Manipulation Indicator -->
                        <div id="manipulationIndicator" class="absolute bottom-4 left-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">MANIPULATION</div>
                                <div class="w-32 bg-gray-700 rounded-full h-3">
                                    <div id="manipulationBar" class="bg-purple-400 h-3 rounded-full transition-all duration-300" style="width: 85%"></div>
                                </div>
                                <div class="text-xs text-purple-400 mt-1">Whale Activity: 85%</div>
                            </div>
                        </div>

                        <!-- Volatility Meter -->
                        <div id="volatilityMeter" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-orange-400 mb-1">VOLATILITY</div>
                                <div class="flex space-x-1">
                                    <div class="w-2 h-8 bg-orange-400 rounded animate-pulse"></div>
                                    <div class="w-2 h-8 bg-orange-400 rounded animate-pulse" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-8 bg-orange-400 rounded animate-pulse" style="animation-delay: 0.2s"></div>
                                    <div class="w-2 h-8 bg-red-400 rounded animate-pulse" style="animation-delay: 0.3s"></div>
                                    <div class="w-2 h-8 bg-red-400 rounded animate-pulse" style="animation-delay: 0.4s"></div>
                                </div>
                                <div class="text-xs text-orange-400 mt-1">EXTREME</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Ready to trade crypto...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="cryptoEvent" class="text-sm font-bold text-red-400 hidden animate-pulse">MARKET CRASH!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Crypto Crash Dynasty - Survive the Altcoin Apocalypse</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Crashes Survived</div>
                <div id="crashesSurvived" class="text-xl font-bold text-green-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-green-500/30 text-center">
                <div class="text-sm text-gray-400">Rug Pulls</div>
                <div id="rugPulls" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeCryptoCrash();
}

// Initialize the game
function initializeCryptoCrash() {
    document.getElementById('startTrading').addEventListener('click', startCryptoTrading);
    document.getElementById('cashOut').addEventListener('click', cashOutPosition);

    // Initialize crypto systems
    initializeCryptoSystems();
    generatePriceChart();
    updateGameStats();
}

// Initialize crypto systems
function initializeCryptoSystems() {
    // Reset market system
    cryptoCrashGame.market.currentPrice = 1.0;
    cryptoCrashGame.market.startPrice = 1.0;
    cryptoCrashGame.market.multiplier = 1.0;
    cryptoCrashGame.market.crashed = false;
    cryptoCrashGame.market.crashPoint = generateCrashPoint();
    cryptoCrashGame.market.volatility = 0.95;
    cryptoCrashGame.market.manipulation = 0.85;
    cryptoCrashGame.market.whaleActivity = 0.90;
    cryptoCrashGame.market.rugPull = false;
    cryptoCrashGame.market.liquidation = false;

    // Reset crypto elements
    cryptoCrashGame.crypto.selectedCoin = 'SCAMCOIN';
    cryptoCrashGame.crypto.marketCap = Math.floor(Math.random() * 1000000) + 100000;
    cryptoCrashGame.crypto.volume = Math.floor(cryptoCrashGame.crypto.marketCap * 0.05);
    cryptoCrashGame.crypto.holders = Math.floor(Math.random() * 10000) + 1000;
    cryptoCrashGame.crypto.liquidity = Math.floor(cryptoCrashGame.crypto.marketCap * 0.01);
    cryptoCrashGame.crypto.devWallet = Math.random() * 0.30 + 0.40; // 40-70% dev wallet
    cryptoCrashGame.crypto.burned = Math.random() * 0.10; // 0-10% burned
    cryptoCrashGame.crypto.staked = Math.random() * 0.20; // 0-20% staked

    // Reset crash prediction
    cryptoCrashGame.crashPrediction.active = false;
    cryptoCrashGame.crashPrediction.predictedCrash = generateCrashPoint();
    cryptoCrashGame.crashPrediction.confidence = Math.random() * 0.10 + 0.02; // 2-12% confidence
    cryptoCrashGame.crashPrediction.timeRemaining = 0;
    cryptoCrashGame.crashPrediction.whaleSignals = [];
    cryptoCrashGame.crashPrediction.technicalAnalysis = getRandomTA();
    cryptoCrashGame.crashPrediction.sentiment = getRandomSentiment();
    cryptoCrashGame.crashPrediction.fudLevel = Math.random() * 0.20 + 0.80; // 80-100% FUD

    updateCryptoDisplay();
}

// Generate crash point with extreme bias
function generateCrashPoint() {
    const tradingData = TRADING_MODES[cryptoCrashGame.tradingMode];
    const volatilityData = VOLATILITY_LEVELS[cryptoCrashGame.volatilityLevel];

    // Apply extreme bias toward early crashes
    if (Math.random() < tradingData.crashRisk) {
        // Early crash between 1.01x and 1.50x
        return Math.random() * 0.49 + 1.01;
    } else if (Math.random() < volatilityData.crashMultiplier) {
        // Medium crash between 1.50x and 3.00x
        return Math.random() * 1.50 + 1.50;
    } else {
        // Rare high multiplier (almost never happens)
        return Math.random() * 7.00 + 3.00;
    }
}

// Generate price chart
function generatePriceChart() {
    const container = document.getElementById('priceChart');
    container.innerHTML = '';

    // Create initial price line
    const priceLine = document.createElementNS('http://www.w3.org/2000/svg', 'polyline');
    priceLine.setAttribute('id', 'mainPriceLine');
    priceLine.setAttribute('fill', 'none');
    priceLine.setAttribute('stroke', '#00ff00');
    priceLine.setAttribute('stroke-width', '2');
    priceLine.setAttribute('points', '0,300 100,300'); // Start flat
    container.appendChild(priceLine);

    // Add crash line indicator
    const crashLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
    crashLine.setAttribute('id', 'crashLine');
    crashLine.setAttribute('x1', '0');
    crashLine.setAttribute('y1', '0');
    crashLine.setAttribute('x2', '0');
    crashLine.setAttribute('y2', '400');
    crashLine.setAttribute('stroke', '#ff0000');
    crashLine.setAttribute('stroke-width', '3');
    crashLine.setAttribute('stroke-dasharray', '5,5');
    crashLine.setAttribute('opacity', '0');
    container.appendChild(crashLine);
}

// Get random technical analysis
function getRandomTA() {
    const analyses = ['BEARISH', 'EXTREMELY BEARISH', 'DEATH CROSS', 'FALLING KNIFE', 'CAPITULATION'];
    return analyses[Math.floor(Math.random() * analyses.length)];
}

// Get random sentiment
function getRandomSentiment() {
    const sentiments = ['EXTREME FEAR', 'PANIC', 'DESPAIR', 'CAPITULATION', 'BLOOD IN STREETS'];
    return sentiments[Math.floor(Math.random() * sentiments.length)];
}

// Start crypto trading
function startCryptoTrading() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    cryptoCrashGame.isPlaying = true;
    cryptoCrashGame.betAmount = betAmount;
    cryptoCrashGame.totalBet = betAmount;
    cryptoCrashGame.tradingMode = document.getElementById('tradingMode').value;
    cryptoCrashGame.volatilityLevel = document.getElementById('volatilityLevel').value;
    cryptoCrashGame.crypto.selectedCoin = document.getElementById('altcoinSelection').value;

    // Reset market for new trade
    cryptoCrashGame.market.currentPrice = 1.0;
    cryptoCrashGame.market.startPrice = 1.0;
    cryptoCrashGame.market.multiplier = 1.0;
    cryptoCrashGame.market.crashed = false;
    cryptoCrashGame.market.crashPoint = generateCrashPoint();

    // Activate crypto systems
    activateCryptoSystems();

    // Start price movement
    setTimeout(() => {
        startPriceMovement();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('startTrading').disabled = true;
    document.getElementById('cashOut').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Trading started...';
}

// Activate crypto systems
function activateCryptoSystems() {
    const tradingData = TRADING_MODES[cryptoCrashGame.tradingMode];
    const volatilityData = VOLATILITY_LEVELS[cryptoCrashGame.volatilityLevel];

    // Update market manipulation
    cryptoCrashGame.market.manipulation = tradingData.manipulationLevel;
    cryptoCrashGame.market.volatility = volatilityData.priceSwing;

    // Check for rug pull activation
    if (Math.random() < volatilityData.rugPullChance) {
        activateRugPull();
    }

    // Check for whale attack
    if (Math.random() < cryptoCrashGame.market.whaleActivity) {
        activateWhaleAttack();
    }

    // Update crypto elements
    updateCryptoElements();

    // Update visual effects
    updateCryptoDisplay();
    updateMarketEffects();
}

// Activate rug pull
function activateRugPull() {
    cryptoCrashGame.market.rugPull = true;
    cryptoCrashGame.stats.rugPulls++;
    cryptoCrashGame.market.crashPoint = Math.min(cryptoCrashGame.market.crashPoint, 1.05); // Crash very early

    document.getElementById('rugPullRisk').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">RUG PULL: ACTIVE!</div>';

    // Show crypto event
    document.getElementById('cryptoEvent').classList.remove('hidden');
    document.getElementById('cryptoEvent').textContent = 'RUG PULL DETECTED!';
    setTimeout(() => {
        document.getElementById('cryptoEvent').classList.add('hidden');
    }, 3000);
}

// Activate whale attack
function activateWhaleAttack() {
    cryptoCrashGame.stats.whaleAttacks++;
    cryptoCrashGame.market.crashPoint = Math.min(cryptoCrashGame.market.crashPoint, 1.20); // Reduce crash point

    document.getElementById('whaleActivity').innerHTML =
        '<div class="text-orange-400 font-bold animate-pulse">WHALES: ATTACKING!</div>';
}

// Update crypto elements
function updateCryptoElements() {
    document.getElementById('marketSentiment').innerHTML =
        `<div class="text-red-400 font-bold">SENTIMENT: ${cryptoCrashGame.crashPrediction.sentiment}</div>`;
    document.getElementById('liquidationRisk').innerHTML =
        '<div class="text-yellow-400 font-bold">LIQUIDATION: EXTREME</div>';
}

// Start price movement with extreme crash bias
function startPriceMovement() {
    document.getElementById('gameStatus').textContent = 'Price moving...';

    const priceInterval = setInterval(() => {
        if (cryptoCrashGame.market.crashed || !cryptoCrashGame.isPlaying) {
            clearInterval(priceInterval);
            return;
        }

        // Update price with extreme volatility
        updatePrice();

        // Check for crash
        if (cryptoCrashGame.market.multiplier >= cryptoCrashGame.market.crashPoint) {
            clearInterval(priceInterval);
            triggerMarketCrash();
        }

        // Update displays
        updatePriceDisplay();
        updateChartLine();

        // Show crash warning when close
        if (cryptoCrashGame.market.multiplier >= cryptoCrashGame.market.crashPoint * 0.90) {
            showCrashWarning();
        }

    }, 100); // Update every 100ms for smooth movement
}

// Update price with extreme bias
function updatePrice() {
    const tradingData = TRADING_MODES[cryptoCrashGame.tradingMode];
    const volatilityData = VOLATILITY_LEVELS[cryptoCrashGame.volatilityLevel];

    // Apply extreme manipulation bias
    if (Math.random() < cryptoCrashGame.market.manipulation) {
        // Whales manipulating - bias toward crash
        const manipulationFactor = Math.random() * 0.02 + 0.001; // Very small increases
        cryptoCrashGame.market.multiplier += manipulationFactor;
    } else {
        // Normal price movement (still biased)
        const priceChange = (Math.random() - 0.3) * 0.05; // Bias toward negative
        cryptoCrashGame.market.multiplier += priceChange;
    }

    // Apply rug pull effect
    if (cryptoCrashGame.market.rugPull && Math.random() < 0.3) {
        // Sudden price acceleration toward crash
        cryptoCrashGame.market.multiplier += Math.random() * 0.10;
    }

    // Ensure minimum progression toward crash
    cryptoCrashGame.market.multiplier = Math.max(1.0, cryptoCrashGame.market.multiplier);

    // Update current price
    cryptoCrashGame.market.currentPrice = cryptoCrashGame.market.startPrice * cryptoCrashGame.market.multiplier;
}

// Update price display
function updatePriceDisplay() {
    document.getElementById('currentPrice').textContent = `$${cryptoCrashGame.market.currentPrice.toFixed(6)}`;
    document.getElementById('multiplierDisplay').textContent = `${cryptoCrashGame.market.multiplier.toFixed(2)}x`;
    document.getElementById('currentMultiplierDisplay').textContent = `${cryptoCrashGame.market.multiplier.toFixed(2)}x`;

    const potentialWin = Math.floor(cryptoCrashGame.betAmount * cryptoCrashGame.market.multiplier);
    document.getElementById('potentialWin').textContent = `Potential: $${potentialWin}`;

    // Update price change color
    const priceChangeElement = document.getElementById('priceChange');
    if (cryptoCrashGame.market.multiplier > 1.0) {
        priceChangeElement.textContent = `+${((cryptoCrashGame.market.multiplier - 1) * 100).toFixed(2)}%`;
        priceChangeElement.className = 'text-sm font-bold text-green-400';
    } else {
        priceChangeElement.textContent = `${((cryptoCrashGame.market.multiplier - 1) * 100).toFixed(2)}%`;
        priceChangeElement.className = 'text-sm font-bold text-red-400';
    }
}

// Update chart line
function updateChartLine() {
    const priceLine = document.getElementById('mainPriceLine');
    if (!priceLine) return;

    // Calculate chart position
    const progress = Math.min(cryptoCrashGame.market.multiplier / 10, 1); // Scale to chart
    const x = progress * 400; // Chart width
    const y = 300 - ((cryptoCrashGame.market.multiplier - 1) * 200); // Invert Y axis

    // Update line points
    const currentPoints = priceLine.getAttribute('points');
    const newPoint = `${x},${y}`;

    if (currentPoints === '0,300 100,300') {
        // First update
        priceLine.setAttribute('points', `0,300 ${newPoint}`);
    } else {
        // Add new point
        priceLine.setAttribute('points', `${currentPoints} ${newPoint}`);
    }

    // Change color based on trend
    if (cryptoCrashGame.market.multiplier > 1.5) {
        priceLine.setAttribute('stroke', '#ffff00'); // Yellow for medium gains
    } else if (cryptoCrashGame.market.multiplier > 2.0) {
        priceLine.setAttribute('stroke', '#ff8000'); // Orange for high gains
    } else {
        priceLine.setAttribute('stroke', '#00ff00'); // Green for normal
    }
}

// Show crash warning
function showCrashWarning() {
    document.getElementById('crashWarning').classList.remove('hidden');

    setTimeout(() => {
        document.getElementById('crashWarning').classList.add('hidden');
    }, 2000);
}

// Trigger market crash
function triggerMarketCrash() {
    cryptoCrashGame.market.crashed = true;

    // Show crash line
    const crashLine = document.getElementById('crashLine');
    const progress = Math.min(cryptoCrashGame.market.multiplier / 10, 1);
    const x = progress * 400;
    crashLine.setAttribute('x1', x);
    crashLine.setAttribute('x2', x);
    crashLine.setAttribute('opacity', '1');

    // Show crash event
    document.getElementById('cryptoEvent').classList.remove('hidden');
    document.getElementById('cryptoEvent').textContent = 'MARKET CRASHED!';

    // Update price line to red
    const priceLine = document.getElementById('mainPriceLine');
    if (priceLine) {
        priceLine.setAttribute('stroke', '#ff0000');
    }

    setTimeout(() => {
        resolveCryptoTrade('crash');
    }, 2000);
}

// Cash out position
function cashOutPosition() {
    if (!cryptoCrashGame.isPlaying || cryptoCrashGame.market.crashed) {
        return;
    }

    cryptoCrashGame.cashoutMultiplier = cryptoCrashGame.market.multiplier;

    // Hide cash out button
    document.getElementById('cashOut').classList.add('hidden');

    setTimeout(() => {
        resolveCryptoTrade('cashout');
    }, 500);
}

// Resolve crypto trade with extreme bias
function resolveCryptoTrade(action = 'crash') {
    const tradingData = TRADING_MODES[cryptoCrashGame.tradingMode];

    let totalWinnings = 0;
    let resultMessage = '';

    if (action === 'crash') {
        // Market crashed - total loss
        totalWinnings = 0;
        resultMessage = `Crashed at ${cryptoCrashGame.market.multiplier.toFixed(2)}x`;
        cryptoCrashGame.gameResult = 'crash';

        // Check for liquidation
        if (Math.random() < tradingData.liquidationRisk) {
            cryptoCrashGame.stats.liquidations++;
            resultMessage += ' - LIQUIDATED!';
        }

    } else if (action === 'cashout') {
        // Player cashed out
        let baseWinnings = Math.floor(cryptoCrashGame.betAmount * cryptoCrashGame.cashoutMultiplier);

        // Apply extreme house edge reduction
        baseWinnings = Math.floor(baseWinnings * tradingData.payoutMultiplier);

        // Apply manipulation penalty
        if (Math.random() < cryptoCrashGame.market.manipulation) {
            const manipulationPenalty = cryptoCrashGame.market.manipulation * 0.5;
            baseWinnings = Math.floor(baseWinnings * (1 - manipulationPenalty));
        }

        // Apply whale attack penalty
        if (cryptoCrashGame.stats.whaleAttacks > 0) {
            baseWinnings = Math.floor(baseWinnings * 0.5); // 50% whale penalty
        }

        // Apply rug pull penalty
        if (cryptoCrashGame.market.rugPull) {
            baseWinnings = Math.floor(baseWinnings * 0.2); // 80% rug pull penalty
        }

        totalWinnings = Math.max(0, baseWinnings);
        resultMessage = `Cashed out at ${cryptoCrashGame.cashoutMultiplier.toFixed(2)}x`;
        cryptoCrashGame.gameResult = totalWinnings > cryptoCrashGame.betAmount ? 'win' : 'lose';

        if (totalWinnings > cryptoCrashGame.betAmount) {
            cryptoCrashGame.stats.crashesSurvived++;
        }
    }

    // Add winnings to balance
    balance += totalWinnings;
    cryptoCrashGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterTrade(totalWinnings > cryptoCrashGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Trading Mode: ${TRADING_MODES[cryptoCrashGame.tradingMode].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update crypto display
function updateCryptoDisplay() {
    const tradingData = TRADING_MODES[cryptoCrashGame.tradingMode];

    // Update manipulation bar
    document.getElementById('manipulationBar').style.width = `${cryptoCrashGame.market.manipulation * 100}%`;

    if (cryptoCrashGame.market.rugPull) {
        document.getElementById('rugPullRisk').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">RUG PULL: ACTIVE!</div>';
    } else {
        document.getElementById('rugPullRisk').innerHTML =
            '<div class="text-purple-400 font-bold">RUG PULL: IMMINENT</div>';
    }
}

// Update market effects
function updateMarketEffects() {
    // Update volatility meter based on current volatility
    const volatilityBars = document.querySelectorAll('#volatilityMeter .w-2');
    const volatilityLevel = cryptoCrashGame.market.volatility;

    volatilityBars.forEach((bar, index) => {
        if (index < volatilityLevel * 5) {
            bar.classList.remove('bg-gray-700');
            bar.classList.add(index < 3 ? 'bg-orange-400' : 'bg-red-400');
        } else {
            bar.classList.remove('bg-orange-400', 'bg-red-400');
            bar.classList.add('bg-gray-700');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${cryptoCrashGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = cryptoCrashGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${cryptoCrashGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${cryptoCrashGame.stats.totalWagered}`;
    document.getElementById('crashesSurvived').textContent = cryptoCrashGame.stats.crashesSurvived;
    document.getElementById('rugPulls').textContent = cryptoCrashGame.stats.rugPulls;

    const netResult = cryptoCrashGame.stats.totalWon - cryptoCrashGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after trade
function updateGameStatsAfterTrade(won, winnings) {
    cryptoCrashGame.stats.gamesPlayed++;
    cryptoCrashGame.stats.totalWagered += cryptoCrashGame.betAmount;
    cryptoCrashGame.stats.totalWon += winnings;

    if (won) {
        cryptoCrashGame.stats.gamesWon++;
        cryptoCrashGame.stats.currentStreak++;
        cryptoCrashGame.streakData.currentWinStreak++;
        cryptoCrashGame.streakData.currentLossStreak = 0;

        if (cryptoCrashGame.streakData.currentWinStreak > cryptoCrashGame.streakData.longestWinStreak) {
            cryptoCrashGame.streakData.longestWinStreak = cryptoCrashGame.streakData.currentWinStreak;
        }

        if (winnings > cryptoCrashGame.stats.biggestWin) {
            cryptoCrashGame.stats.biggestWin = winnings;
        }
    } else {
        cryptoCrashGame.stats.currentStreak = 0;
        cryptoCrashGame.streakData.currentWinStreak = 0;
        cryptoCrashGame.streakData.currentLossStreak++;

        if (cryptoCrashGame.streakData.currentLossStreak > cryptoCrashGame.streakData.longestLossStreak) {
            cryptoCrashGame.streakData.longestLossStreak = cryptoCrashGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to crypto effects)
    cryptoCrashGame.stats.winRate = (cryptoCrashGame.stats.gamesWon / cryptoCrashGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next trade
function resetGame() {
    cryptoCrashGame.isPlaying = false;
    cryptoCrashGame.betAmount = 0;
    cryptoCrashGame.totalBet = 0;
    cryptoCrashGame.gameResult = '';
    cryptoCrashGame.totalWin = 0;
    cryptoCrashGame.cashoutMultiplier = 1.0;

    // Reset market system
    cryptoCrashGame.market.currentPrice = 1.0;
    cryptoCrashGame.market.startPrice = 1.0;
    cryptoCrashGame.market.multiplier = 1.0;
    cryptoCrashGame.market.crashed = false;
    cryptoCrashGame.market.rugPull = false;
    cryptoCrashGame.market.liquidation = false;

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('cryptoEvent').classList.add('hidden');
    document.getElementById('cashOut').classList.add('hidden');
    document.getElementById('crashWarning').classList.add('hidden');

    // Reset price chart
    const priceLine = document.getElementById('mainPriceLine');
    if (priceLine) {
        priceLine.setAttribute('points', '0,300 100,300');
        priceLine.setAttribute('stroke', '#00ff00');
    }

    const crashLine = document.getElementById('crashLine');
    if (crashLine) {
        crashLine.setAttribute('opacity', '0');
    }

    // Reset crypto status
    document.getElementById('marketSentiment').innerHTML =
        '<div class="text-red-400 font-bold">SENTIMENT: EXTREME FEAR</div>';
    document.getElementById('whaleActivity').innerHTML =
        '<div class="text-orange-400 font-bold">WHALES: DUMPING</div>';
    document.getElementById('rugPullRisk').innerHTML =
        '<div class="text-purple-400 font-bold">RUG PULL: IMMINENT</div>';
    document.getElementById('liquidationRisk').innerHTML =
        '<div class="text-yellow-400 font-bold">LIQUIDATION: HIGH</div>';

    // Reset price displays
    document.getElementById('currentPrice').textContent = '$1.00';
    document.getElementById('multiplierDisplay').textContent = '1.00x';
    document.getElementById('currentMultiplierDisplay').textContent = '1.00x';
    document.getElementById('potentialWin').textContent = 'Potential: $0';
    document.getElementById('priceChange').textContent = '0.00%';
    document.getElementById('priceChange').className = 'text-sm font-bold text-gray-400';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable start button
    document.getElementById('startTrading').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Ready to trade crypto...';
    document.getElementById('gameMessage').textContent = 'Welcome to Crypto Crash Dynasty - Survive the Altcoin Apocalypse';

    // Reset manipulation bar
    document.getElementById('manipulationBar').style.width = '85%';

    // Reinitialize systems for next trade
    initializeCryptoSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCryptoCrashGame();
});