// Doomsday Dice - Volcano Eruption Risk Escalator
// Ultra High House Edge Implementation with Volcanic Apocalypse Theme
// Designed to maintain <0.05% player win rate

// Game state
let balance = 1000;

// Game state object with volcanic doomsday system
let doomsdayDiceGame = {
    isPlaying: false,
    volcanicThreat: 'yellowstone', // yellowstone, vesuvius, krakatoa, tambora, toba
    riskLevel: 'catastrophic', // minor, major, catastrophic, extinction, apocalyptic
    betAmount: 0,
    totalBet: 0,

    // Volcanic eruption system
    volcano: {
        currentThreat: 1,
        maxThreat: 10,
        eruptionRisk: 0.95, // 95% eruption risk
        magmaPressure: 0.90, // 90% magma pressure
        seismicActivity: 0.85, // 85% seismic activity
        gasEmissions: 0.88, // 88% toxic gas emissions
        pyroclasticFlow: false,
        laharRisk: 0.92, // 92% lahar risk
        ashFallout: 0.96, // 96% ash fallout
        nuclearWinter: false
    },

    // Apocalyptic elements
    apocalypse: {
        scenario: 'Supervolcano Eruption',
        location: 'Yellowstone National Park',
        vei: 8, // Volcanic Explosivity Index
        casualties: 2000000000, // 2 billion
        climateImpact: 'Nuclear Winter',
        duration: '10 years',
        globalTemp: -5, // -5°C global temperature drop
        cropFailure: 0.95, // 95% crop failure
        civilizationCollapse: 0.90 // 90% civilization collapse
    },

    // Dice system (6 dice representing survival chances)
    dice: [
        { value: 1, symbol: '💀', locked: false, cursed: false },
        { value: 1, symbol: '💀', locked: false, cursed: false },
        { value: 1, symbol: '💀', locked: false, cursed: false },
        { value: 1, symbol: '💀', locked: false, cursed: false },
        { value: 1, symbol: '💀', locked: false, cursed: false },
        { value: 1, symbol: '💀', locked: false, cursed: false }
    ],

    // Volcanic risk escalator system
    riskEscalator: {
        active: false,
        currentLevel: 1,
        maxLevel: 10,
        escalationRate: 0.95, // 95% escalation rate
        survivalChance: 0.05, // 5% survival chance
        evacuationTime: 0,
        shelterCapacity: 0.01, // 1% shelter capacity
        emergencySupplies: 0.02, // 2% emergency supplies
        communicationDown: 0.98 // 98% communication failure
    },

    // Doomsday symbols and their survival values
    symbols: [
        { name: 'Bunker', value: 6, survival: 15, rarity: 0.02, protection: 'radiation' },
        { name: 'Shelter', value: 5, survival: 10, rarity: 0.05, protection: 'ash' },
        { name: 'Gas Mask', value: 4, survival: 8, rarity: 0.08, protection: 'toxic_gas' },
        { name: 'Food Cache', value: 3, survival: 6, rarity: 0.12, protection: 'starvation' },
        { name: 'Water Filter', value: 2, survival: 4, rarity: 0.18, protection: 'contamination' },
        { name: 'Death Skull', value: 1, survival: 0, rarity: 0.55, protection: 'none' }
    ],

    rollsRemaining: 3,
    gameResult: '',
    totalWin: 0,

    stats: {
        gamesPlayed: 0,
        gamesWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        eruptionsSurvived: 0,
        pyroclasticEscapes: 0,
        nuclearWinters: 0,
        civilizationRebuilds: 0,
        doomsdaysPrevented: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Volcanic threats with extreme eruption bias
const VOLCANIC_THREATS = {
    yellowstone: {
        name: 'Yellowstone Supervolcano',
        eruptionRisk: 0.98, // 98% eruption risk
        vei: 8, // Volcanic Explosivity Index
        payoutMultiplier: 0.02, // Severely reduced payouts
        survivalChance: 0.01, // 1% survival chance
        globalImpact: 0.99 // 99% global impact
    },
    vesuvius: {
        name: 'Mount Vesuvius',
        eruptionRisk: 0.95, // 95% eruption risk
        vei: 6,
        payoutMultiplier: 0.05, // Extremely reduced payouts
        survivalChance: 0.03, // 3% survival chance
        globalImpact: 0.85 // 85% global impact
    },
    krakatoa: {
        name: 'Krakatoa',
        eruptionRisk: 0.92, // 92% eruption risk
        vei: 6,
        payoutMultiplier: 0.08, // Heavily reduced payouts
        survivalChance: 0.05, // 5% survival chance
        globalImpact: 0.80 // 80% global impact
    },
    tambora: {
        name: 'Mount Tambora',
        eruptionRisk: 0.90, // 90% eruption risk
        vei: 7,
        payoutMultiplier: 0.10, // Reduced payouts
        survivalChance: 0.08, // 8% survival chance
        globalImpact: 0.75 // 75% global impact
    },
    toba: {
        name: 'Toba Supervolcano',
        eruptionRisk: 0.99, // 99% eruption risk
        vei: 8,
        payoutMultiplier: 0.01, // Impossibly reduced payouts
        survivalChance: 0.005, // 0.5% survival chance
        globalImpact: 0.995 // 99.5% global impact
    }
};

const RISK_LEVELS = {
    minor: {
        name: 'Minor Eruption',
        multiplier: 0.70,
        escalationRate: 0.60,
        evacuationTime: 0.30
    },
    major: {
        name: 'Major Eruption',
        multiplier: 0.80,
        escalationRate: 0.75,
        evacuationTime: 0.20
    },
    catastrophic: {
        name: 'Catastrophic Eruption',
        multiplier: 0.90,
        escalationRate: 0.85,
        evacuationTime: 0.10
    },
    extinction: {
        name: 'Extinction Event',
        multiplier: 0.95,
        escalationRate: 0.95,
        evacuationTime: 0.05
    },
    apocalyptic: {
        name: 'Apocalyptic Eruption',
        multiplier: 0.99,
        escalationRate: 0.99,
        evacuationTime: 0.01
    }
};

// Severely reduced payout table with doomsday theme
const DOOMSDAY_PAYOUTS = {
    // Survival combinations (heavily reduced)
    SIX_BUNKERS: 10000, // Reduced from 1000000:1
    FIVE_BUNKERS: 5000, // Reduced from 500000:1
    FOUR_BUNKERS: 1000, // Reduced from 100000:1
    THREE_BUNKERS: 200, // Reduced from 20000:1

    SIX_SHELTERS: 5000, // Reduced from 500000:1
    FIVE_SHELTERS: 2500, // Reduced from 250000:1
    FOUR_SHELTERS: 500, // Reduced from 50000:1
    THREE_SHELTERS: 100, // Reduced from 10000:1

    SIX_MASKS: 2000, // Reduced from 200000:1
    FIVE_MASKS: 1000, // Reduced from 100000:1
    FOUR_MASKS: 200, // Reduced from 20000:1
    THREE_MASKS: 40, // Reduced from 4000:1

    // Mixed survival gear
    SURVIVAL_KIT: 50, // Reduced from 5000:1
    EMERGENCY_CACHE: 20, // Reduced from 2000:1
    BASIC_SUPPLIES: 10, // Reduced from 1000:1

    // Doomsday bonuses (fake - almost never apply)
    ERUPTION_SURVIVOR: 0.01, // 1% of displayed bonus
    PYROCLASTIC_ESCAPE: 0.005, // 0.5% of displayed bonus
    NUCLEAR_WINTER_SURVIVAL: 0.001 // 0.1% of displayed bonus
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadDoomsdayDiceGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">DOOMSDAY CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">VOLCANIC THREAT</label>
                        <select id="volcanicThreat" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="yellowstone">Yellowstone Supervolcano</option>
                            <option value="vesuvius">Mount Vesuvius</option>
                            <option value="krakatoa">Krakatoa</option>
                            <option value="tambora">Mount Tambora</option>
                            <option value="toba">Toba Supervolcano</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">RISK LEVEL</label>
                        <select id="riskLevel" class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="minor">Minor Eruption</option>
                            <option value="major">Major Eruption</option>
                            <option value="catastrophic" selected>Catastrophic Eruption</option>
                            <option value="extinction">Extinction Event</option>
                            <option value="apocalyptic">Apocalyptic Eruption</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-red-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="rollDoomsday" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        ROLL DOOMSDAY DICE
                    </button>

                    <div id="diceActions" class="space-y-2 hidden">
                        <button id="rerollAll" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            REROLL ALL
                        </button>
                        <button id="rerollSelected" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                            REROLL SELECTED
                        </button>
                        <button id="seekShelter" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            SEEK SHELTER
                        </button>
                        <button id="acceptDoom" class="w-full py-2 rounded-lg font-bold bg-red-600 hover:bg-red-700 text-white">
                            ACCEPT DOOM
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Survival Chance</div>
                        <div id="survivalChanceDisplay" class="text-lg font-bold text-red-400">0%</div>
                    </div>
                </div>

                <!-- Volcanic Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">VOLCANIC STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="threatLevel" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">THREAT: YELLOWSTONE</div>
                        </div>
                        <div id="eruptionRisk" class="text-center p-2 rounded bg-black/50">
                            <div class="text-orange-400 font-bold">ERUPTION: 98%</div>
                        </div>
                        <div id="magmaPressure" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">MAGMA: CRITICAL</div>
                        </div>
                        <div id="evacuationStatus" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">EVACUATION: FAILED</div>
                        </div>
                    </div>
                </div>

                <!-- Reduced Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-red-400">SURVIVAL PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Bunker Survival:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">6 Bunkers:</span>
                            <span class="text-red-400">10000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Bunkers:</span>
                            <span class="text-red-400">5000:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">4 Bunkers:</span>
                            <span class="text-red-400">1000:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Emergency Gear:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Survival Kit:</span>
                            <span class="text-red-400">50:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Basic Supplies:</span>
                            <span class="text-red-400">10:1</span>
                        </div>
                        <div class="text-xs text-red-400 mt-2">*99%+ eruption probability</div>
                        <div class="text-xs text-red-400">*Nuclear winter likely</div>
                    </div>
                </div>
            </div>

            <!-- Main Volcanic Doomsday Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <!-- Volcanic Eruption Scene -->
                    <div id="volcanicScene" class="relative bg-gradient-to-br from-black via-red-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Volcanic Background -->
                        <div id="volcanicBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="volcanoGradient" cx="50%" cy="80%" r="60%">
                                        <stop offset="0%" style="stop-color:#ff4500;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#ff0000;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#8b0000;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="lavaPattern" width="30" height="30" patternUnits="userSpaceOnUse">
                                        <circle cx="15" cy="15" r="5" fill="#ff4500" opacity="0.6"/>
                                        <circle cx="5" cy="5" r="3" fill="#ff0000" opacity="0.8"/>
                                        <circle cx="25" cy="25" r="3" fill="#ff0000" opacity="0.8"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#lavaPattern)" />
                                <ellipse id="volcanoCore" cx="50%" cy="80%" rx="40%" ry="30%" fill="url(#volcanoGradient)" class="animate-pulse" />
                                <g id="pyroclasticFlow">
                                    <!-- Pyroclastic flow will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Dice Display Area -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">SURVIVAL DICE</div>
                                <div id="diceDisplay" class="grid grid-cols-3 gap-3 justify-center">
                                    <!-- 6 dice in 2 rows -->
                                    <div id="dice1" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">💀</div>
                                    </div>
                                    <div id="dice2" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">💀</div>
                                    </div>
                                    <div id="dice3" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">💀</div>
                                    </div>
                                    <div id="dice4" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">💀</div>
                                    </div>
                                    <div id="dice5" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">💀</div>
                                    </div>
                                    <div id="dice6" class="dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300">
                                        <div class="dice-face text-2xl">💀</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Click dice to lock/unlock</div>
                            </div>
                        </div>

                        <!-- Risk Escalator -->
                        <div id="riskEscalator" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-red-400 mb-2">ERUPTION RISK ESCALATOR</div>
                                <div class="w-full bg-gray-700 rounded-full h-6 mb-2">
                                    <div id="riskBar" class="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 h-6 rounded-full transition-all duration-1000 animate-pulse" style="width: 95%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Safe</span>
                                    <span id="currentRisk">Risk: 95%</span>
                                    <span>Doomsday</span>
                                </div>
                            </div>
                        </div>

                        <!-- Volcanic Activity Meter -->
                        <div id="volcanicActivity" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-red-400 mb-1">VOLCANIC ACTIVITY</div>
                                <div class="flex space-x-1">
                                    <div class="w-2 h-8 bg-red-400 rounded animate-pulse"></div>
                                    <div class="w-2 h-8 bg-red-400 rounded animate-pulse" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-8 bg-red-400 rounded animate-pulse" style="animation-delay: 0.2s"></div>
                                    <div class="w-2 h-8 bg-red-500 rounded animate-pulse" style="animation-delay: 0.3s"></div>
                                    <div class="w-2 h-8 bg-red-600 rounded animate-pulse" style="animation-delay: 0.4s"></div>
                                </div>
                                <div class="text-xs text-red-400 mt-1">CRITICAL</div>
                            </div>
                        </div>

                        <!-- Rolls Remaining -->
                        <div id="rollsRemaining" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">ROLLS LEFT</div>
                                <div class="text-2xl font-bold text-white text-center">3</div>
                                <div class="text-xs text-gray-400 mt-1">Survival Attempts</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Doomsday approaches...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="volcanicEvent" class="text-sm font-bold text-red-400 hidden animate-pulse">ERUPTION IMMINENT!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Doomsday Dice - Survive the Volcanic Apocalypse</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Games Played</div>
                <div id="gamesPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-red-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-red-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Eruptions Survived</div>
                <div id="eruptionsSurvived" class="text-xl font-bold text-red-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-red-500/30 text-center">
                <div class="text-sm text-gray-400">Nuclear Winters</div>
                <div id="nuclearWinters" class="text-xl font-bold text-purple-400">0</div>
            </div>
        </div>
    `;

    initializeDoomsdayDice();
}

// Initialize the game
function initializeDoomsdayDice() {
    document.getElementById('rollDoomsday').addEventListener('click', startDoomsdayRoll);
    document.getElementById('rerollAll').addEventListener('click', () => rerollDice('all'));
    document.getElementById('rerollSelected').addEventListener('click', () => rerollDice('selected'));
    document.getElementById('seekShelter').addEventListener('click', seekShelter);
    document.getElementById('acceptDoom').addEventListener('click', acceptDoom);

    // Add dice click listeners
    for (let i = 1; i <= 6; i++) {
        document.getElementById(`dice${i}`).addEventListener('click', () => toggleDiceLock(i - 1));
    }

    // Initialize doomsday systems
    initializeDoomsdaySystems();
    generatePyroclasticFlow();
    updateGameStats();
}

// Initialize doomsday systems
function initializeDoomsdaySystems() {
    // Reset volcano system
    doomsdayDiceGame.volcano.currentThreat = 1;
    doomsdayDiceGame.volcano.eruptionRisk = 0.95;
    doomsdayDiceGame.volcano.magmaPressure = 0.90;
    doomsdayDiceGame.volcano.seismicActivity = 0.85;
    doomsdayDiceGame.volcano.gasEmissions = 0.88;
    doomsdayDiceGame.volcano.pyroclasticFlow = false;
    doomsdayDiceGame.volcano.laharRisk = 0.92;
    doomsdayDiceGame.volcano.ashFallout = 0.96;
    doomsdayDiceGame.volcano.nuclearWinter = false;

    // Reset apocalypse elements
    doomsdayDiceGame.apocalypse.scenario = getRandomApocalypseScenario();
    doomsdayDiceGame.apocalypse.location = getRandomVolcanicLocation();
    doomsdayDiceGame.apocalypse.vei = Math.floor(Math.random() * 3) + 6; // VEI 6-8
    doomsdayDiceGame.apocalypse.casualties = Math.floor(Math.random() * 5000000000) + 1000000000; // 1-6 billion
    doomsdayDiceGame.apocalypse.climateImpact = getRandomClimateImpact();
    doomsdayDiceGame.apocalypse.duration = getRandomDuration();
    doomsdayDiceGame.apocalypse.globalTemp = Math.floor(Math.random() * 10) - 15; // -15°C to -5°C
    doomsdayDiceGame.apocalypse.cropFailure = Math.random() * 0.20 + 0.80; // 80-100% crop failure
    doomsdayDiceGame.apocalypse.civilizationCollapse = Math.random() * 0.20 + 0.80; // 80-100% collapse

    // Reset risk escalator
    doomsdayDiceGame.riskEscalator.active = false;
    doomsdayDiceGame.riskEscalator.currentLevel = 1;
    doomsdayDiceGame.riskEscalator.escalationRate = 0.95;
    doomsdayDiceGame.riskEscalator.survivalChance = 0.05;
    doomsdayDiceGame.riskEscalator.evacuationTime = 0;
    doomsdayDiceGame.riskEscalator.shelterCapacity = 0.01;
    doomsdayDiceGame.riskEscalator.emergencySupplies = 0.02;
    doomsdayDiceGame.riskEscalator.communicationDown = 0.98;

    // Reset dice
    for (let i = 0; i < 6; i++) {
        doomsdayDiceGame.dice[i] = {
            value: 1,
            symbol: '💀',
            locked: false,
            cursed: false
        };
    }

    doomsdayDiceGame.rollsRemaining = 3;

    updateDoomsdayDisplay();
}

// Generate pyroclastic flow
function generatePyroclasticFlow() {
    const container = document.getElementById('pyroclasticFlow');
    container.innerHTML = '';

    for (let i = 0; i < 8; i++) {
        const flow = document.createElementNS('http://www.w3.org/2000/svg', 'ellipse');
        flow.setAttribute('cx', `${20 + i * 10}%`);
        flow.setAttribute('cy', `${60 + Math.random() * 20}%`);
        flow.setAttribute('rx', `${5 + Math.random() * 10}%`);
        flow.setAttribute('ry', `${3 + Math.random() * 5}%`);
        flow.setAttribute('fill', '#ff4500');
        flow.setAttribute('opacity', '0.7');
        flow.classList.add('animate-pulse');
        flow.style.animationDelay = `${i * 0.2}s`;
        container.appendChild(flow);
    }
}

// Get random apocalypse elements
function getRandomApocalypseScenario() {
    const scenarios = ['Supervolcano Eruption', 'Volcanic Winter', 'Pyroclastic Apocalypse', 'Ash Cloud Catastrophe', 'Magma Chamber Collapse'];
    return scenarios[Math.floor(Math.random() * scenarios.length)];
}

function getRandomVolcanicLocation() {
    const locations = ['Yellowstone National Park', 'Mount Vesuvius', 'Krakatoa', 'Mount Tambora', 'Toba Caldera', 'Mount Pinatubo'];
    return locations[Math.floor(Math.random() * locations.length)];
}

function getRandomClimateImpact() {
    const impacts = ['Nuclear Winter', 'Volcanic Winter', 'Global Cooling', 'Atmospheric Poisoning', 'Ozone Depletion'];
    return impacts[Math.floor(Math.random() * impacts.length)];
}

function getRandomDuration() {
    const durations = ['10 years', '20 years', '50 years', '100 years', 'Permanent'];
    return durations[Math.floor(Math.random() * durations.length)];
}

// Start doomsday roll
function startDoomsdayRoll() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    doomsdayDiceGame.isPlaying = true;
    doomsdayDiceGame.betAmount = betAmount;
    doomsdayDiceGame.totalBet = betAmount;
    doomsdayDiceGame.volcanicThreat = document.getElementById('volcanicThreat').value;
    doomsdayDiceGame.riskLevel = document.getElementById('riskLevel').value;
    doomsdayDiceGame.rollsRemaining = 3;

    // Activate doomsday systems
    activateDoomsdaySystems();

    // Roll initial dice
    setTimeout(() => {
        rollDoomsdayDice();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('rollDoomsday').disabled = true;
    document.getElementById('gameStatus').textContent = 'Rolling survival dice...';
}

// Activate doomsday systems
function activateDoomsdaySystems() {
    const volcanicData = VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat];
    const riskData = RISK_LEVELS[doomsdayDiceGame.riskLevel];

    doomsdayDiceGame.volcano.eruptionRisk = volcanicData.eruptionRisk;
    doomsdayDiceGame.riskEscalator.survivalChance = volcanicData.survivalChance;
    doomsdayDiceGame.riskEscalator.escalationRate = riskData.escalationRate;

    // Check for pyroclastic flow activation (almost always)
    if (Math.random() < volcanicData.eruptionRisk) {
        activatePyroclasticFlow();
    }

    // Check for nuclear winter risk
    if (Math.random() < volcanicData.globalImpact) {
        activateNuclearWinter();
    }

    // Update volcanic elements
    updateVolcanicElements();

    // Update visual effects
    updateDoomsdayDisplay();
    updateVolcanicEffects();
}

// Activate pyroclastic flow
function activatePyroclasticFlow() {
    doomsdayDiceGame.volcano.pyroclasticFlow = true;
    doomsdayDiceGame.stats.pyroclasticEscapes++;

    document.getElementById('magmaPressure').innerHTML =
        '<div class="text-yellow-400 font-bold animate-pulse">MAGMA: ERUPTING!</div>';

    // Show volcanic event
    document.getElementById('volcanicEvent').classList.remove('hidden');
    document.getElementById('volcanicEvent').textContent = 'PYROCLASTIC FLOW!';
    setTimeout(() => {
        document.getElementById('volcanicEvent').classList.add('hidden');
    }, 3000);
}

// Activate nuclear winter
function activateNuclearWinter() {
    doomsdayDiceGame.volcano.nuclearWinter = true;
    doomsdayDiceGame.stats.nuclearWinters++;

    document.getElementById('evacuationStatus').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">EVACUATION: IMPOSSIBLE</div>';
}

// Update volcanic elements
function updateVolcanicElements() {
    const volcanicData = VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat];

    document.getElementById('threatLevel').innerHTML =
        `<div class="text-red-400 font-bold">THREAT: ${volcanicData.name.toUpperCase()}</div>`;
    document.getElementById('eruptionRisk').innerHTML =
        `<div class="text-orange-400 font-bold">ERUPTION: ${Math.floor(volcanicData.eruptionRisk * 100)}%</div>`;
}

// Roll doomsday dice with extreme death bias
function rollDoomsdayDice() {
    document.getElementById('gameStatus').textContent = 'Dice rolling for survival...';

    // Roll each unlocked die with extreme bias toward death
    for (let i = 0; i < 6; i++) {
        if (!doomsdayDiceGame.dice[i].locked) {
            const diceResult = generateBiasedDoomsdayRoll();
            doomsdayDiceGame.dice[i] = diceResult;
        }
    }

    // Animate dice rolling
    animateDoomsdayDiceRoll();

    // Enable dice actions after roll
    setTimeout(() => {
        enableDoomsdayActions();
    }, 2000);
}

// Generate biased doomsday roll
function generateBiasedDoomsdayRoll() {
    const volcanicData = VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat];

    // Apply extreme bias toward death symbols
    if (Math.random() < volcanicData.eruptionRisk) {
        // Bias toward death skull (55% chance)
        return {
            value: 1,
            symbol: getDoomsdaySymbol('Death Skull'),
            locked: false,
            cursed: Math.random() < 0.3 // 30% curse chance
        };
    } else {
        // Random selection (still heavily biased toward low survival)
        const symbol = selectSymbolByRarity();
        return {
            value: symbol.value,
            symbol: getDoomsdaySymbol(symbol.name),
            locked: false,
            cursed: Math.random() < 0.1 // 10% curse chance
        };
    }
}

// Select symbol by rarity (biased toward death)
function selectSymbolByRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Reverse order to favor death symbols
    for (let i = doomsdayDiceGame.symbols.length - 1; i >= 0; i--) {
        const symbol = doomsdayDiceGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to death skull
    return doomsdayDiceGame.symbols[doomsdayDiceGame.symbols.length - 1];
}

// Get doomsday symbol representation
function getDoomsdaySymbol(symbolName) {
    const symbols = {
        'Bunker': '🏠',
        'Shelter': '⛺',
        'Gas Mask': '😷',
        'Food Cache': '🥫',
        'Water Filter': '💧',
        'Death Skull': '💀'
    };

    return symbols[symbolName] || '💀';
}

// Animate doomsday dice roll
function animateDoomsdayDiceRoll() {
    for (let i = 0; i < 6; i++) {
        const diceElement = document.getElementById(`dice${i + 1}`);
        const diceFace = diceElement.querySelector('.dice-face');

        if (!doomsdayDiceGame.dice[i].locked) {
            // Add rolling animation
            diceElement.classList.add('animate-spin');

            setTimeout(() => {
                diceElement.classList.remove('animate-spin');
                diceFace.textContent = doomsdayDiceGame.dice[i].symbol;

                // Add cursed styling
                if (doomsdayDiceGame.dice[i].cursed) {
                    diceElement.classList.add('ring-2', 'ring-purple-400', 'animate-pulse', 'bg-purple-900/50');
                }

                // Update survival chance
                updateSurvivalChance();
            }, 1000 + i * 200);
        }
    }
}

// Update survival chance
function updateSurvivalChance() {
    let survivalItems = 0;
    let totalSurvival = 0;

    doomsdayDiceGame.dice.forEach(dice => {
        const symbol = doomsdayDiceGame.symbols.find(s => getDoomsdaySymbol(s.name) === dice.symbol);
        if (symbol && symbol.name !== 'Death Skull') {
            survivalItems++;
            totalSurvival += symbol.survival;
        }
    });

    // Apply volcanic threat penalty
    const volcanicData = VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat];
    const survivalChance = Math.max(0, totalSurvival * volcanicData.survivalChance);

    document.getElementById('survivalChanceDisplay').textContent = `${survivalChance.toFixed(1)}%`;

    // Update risk escalator
    const riskLevel = Math.min(100, 95 + (6 - survivalItems) * 1); // 95-100% risk
    document.getElementById('riskBar').style.width = `${riskLevel}%`;
    document.getElementById('currentRisk').textContent = `Risk: ${riskLevel}%`;
}

// Enable doomsday actions
function enableDoomsdayActions() {
    document.getElementById('diceActions').classList.remove('hidden');
    doomsdayDiceGame.rollsRemaining--;

    document.getElementById('rollsRemaining').querySelector('.text-2xl').textContent = doomsdayDiceGame.rollsRemaining;

    if (doomsdayDiceGame.rollsRemaining <= 0) {
        // No more rolls - resolve game
        setTimeout(() => {
            resolveDoomsdayGame();
        }, 1000);
    } else {
        document.getElementById('gameStatus').textContent = 'Choose your survival strategy...';
        document.getElementById('gameMessage').textContent = 'Lock dice and reroll, seek shelter, or accept your doom';
    }
}

// Toggle dice lock
function toggleDiceLock(diceIndex) {
    if (doomsdayDiceGame.isPlaying && doomsdayDiceGame.rollsRemaining > 0) {
        doomsdayDiceGame.dice[diceIndex].locked = !doomsdayDiceGame.dice[diceIndex].locked;

        const diceElement = document.getElementById(`dice${diceIndex + 1}`);
        if (doomsdayDiceGame.dice[diceIndex].locked) {
            diceElement.classList.add('ring-4', 'ring-yellow-400', 'bg-yellow-900/50');
        } else {
            diceElement.classList.remove('ring-4', 'ring-yellow-400', 'bg-yellow-900/50');
        }
    }
}

// Reroll dice
function rerollDice(type) {
    if (doomsdayDiceGame.rollsRemaining <= 0) {
        return;
    }

    if (type === 'all') {
        // Unlock all dice and reroll
        for (let i = 0; i < 6; i++) {
            doomsdayDiceGame.dice[i].locked = false;
            const diceElement = document.getElementById(`dice${i + 1}`);
            diceElement.classList.remove('ring-4', 'ring-yellow-400', 'bg-yellow-900/50');
        }
    }

    document.getElementById('diceActions').classList.add('hidden');

    setTimeout(() => {
        rollDoomsdayDice();
    }, 500);
}

// Seek shelter
function seekShelter() {
    // Lock all dice and resolve with shelter bonus
    for (let i = 0; i < 6; i++) {
        doomsdayDiceGame.dice[i].locked = true;
    }

    document.getElementById('diceActions').classList.add('hidden');
    document.getElementById('gameStatus').textContent = 'Seeking emergency shelter...';

    setTimeout(() => {
        resolveDoomsdayGame('shelter');
    }, 1000);
}

// Accept doom
function acceptDoom() {
    document.getElementById('diceActions').classList.add('hidden');
    document.getElementById('gameStatus').textContent = 'Accepting inevitable doom...';

    // Show volcanic event
    document.getElementById('volcanicEvent').classList.remove('hidden');
    document.getElementById('volcanicEvent').textContent = 'DOOMSDAY ACCEPTED!';
    setTimeout(() => {
        document.getElementById('volcanicEvent').classList.add('hidden');
    }, 3000);

    setTimeout(() => {
        resolveDoomsdayGame('doom');
    }, 2000);
}

// Resolve doomsday game with extreme death bias
function resolveDoomsdayGame(action = 'normal') {
    const volcanicData = VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat];
    const riskData = RISK_LEVELS[doomsdayDiceGame.riskLevel];

    let totalWinnings = 0;
    let resultMessage = '';

    if (action === 'doom') {
        // Accepted doom - minimal return for courage
        totalWinnings = Math.floor(doomsdayDiceGame.betAmount * 0.01); // 1% return
        resultMessage = 'Faced doom with courage';
        doomsdayDiceGame.gameResult = 'doom';
    } else {
        // Calculate survival combinations with extreme reductions
        const survivalCombinations = calculateSurvivalCombinations();

        Object.entries(survivalCombinations).forEach(([combination, count]) => {
            if (count >= 3) { // Need at least 3 of a kind for survival
                const payoutKey = combination.toUpperCase();
                if (DOOMSDAY_PAYOUTS[payoutKey]) {
                    let combinationPayout = doomsdayDiceGame.betAmount * (DOOMSDAY_PAYOUTS[payoutKey] / 10000); // Severely reduced

                    // Apply count multiplier (minimal)
                    if (count === 4) combinationPayout *= 1.2;
                    if (count === 5) combinationPayout *= 1.5;
                    if (count === 6) combinationPayout *= 2.0;

                    totalWinnings += combinationPayout;
                }
            }
        });

        // Apply shelter bonus (fake - almost never applies)
        if (action === 'shelter' && Math.random() < 0.005) {
            const shelterBonus = Math.floor(totalWinnings * 0.1); // 10% shelter bonus
            totalWinnings += shelterBonus;
        }

        // Apply eruption survivor bonus (fake - almost never applies)
        if (totalWinnings > 0 && Math.random() < 0.001) {
            const survivorBonus = Math.floor(totalWinnings * DOOMSDAY_PAYOUTS.ERUPTION_SURVIVOR);
            totalWinnings += survivorBonus;
        }

        resultMessage = `Survival attempt: ${action}`;
        doomsdayDiceGame.gameResult = totalWinnings > doomsdayDiceGame.betAmount ? 'win' : 'lose';
    }

    // Apply extreme house edge reduction
    totalWinnings = Math.floor(totalWinnings * volcanicData.payoutMultiplier);

    // Apply pyroclastic flow penalty
    if (doomsdayDiceGame.volcano.pyroclasticFlow) {
        const pyroclasticPenalty = 0.8; // 80% penalty
        totalWinnings = Math.floor(totalWinnings * (1 - pyroclasticPenalty));
    }

    // Apply nuclear winter penalty
    if (doomsdayDiceGame.volcano.nuclearWinter) {
        const nuclearPenalty = 0.9; // 90% penalty
        totalWinnings = Math.floor(totalWinnings * (1 - nuclearPenalty));
    }

    // Apply risk level multiplier
    totalWinnings = Math.floor(totalWinnings * riskData.multiplier);

    // Volcanic eruption can void all wins
    if (totalWinnings > 0 && Math.random() < volcanicData.eruptionRisk) {
        totalWinnings = 0;
        resultMessage += ' - Eruption destroyed everything!';
    }

    // Ensure minimum loss
    totalWinnings = Math.max(0, totalWinnings);

    // Track survival statistics
    if (totalWinnings > doomsdayDiceGame.betAmount) {
        doomsdayDiceGame.stats.eruptionsSurvived++;
    }

    // Add winnings to balance
    balance += totalWinnings;
    doomsdayDiceGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterGame(totalWinnings > doomsdayDiceGame.betAmount, totalWinnings);

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Volcanic Threat: ${VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Calculate survival combinations
function calculateSurvivalCombinations() {
    const combinations = {};

    doomsdayDiceGame.dice.forEach(dice => {
        const symbolName = getSymbolNameFromIcon(dice.symbol);
        combinations[symbolName] = (combinations[symbolName] || 0) + 1;
    });

    return combinations;
}

// Get symbol name from icon
function getSymbolNameFromIcon(icon) {
    const iconMap = {
        '🏠': 'BUNKERS',
        '⛺': 'SHELTERS',
        '😷': 'MASKS',
        '🥫': 'FOOD',
        '💧': 'WATER',
        '💀': 'DEATH'
    };

    return iconMap[icon] || 'DEATH';
}

// Update doomsday display
function updateDoomsdayDisplay() {
    const volcanicData = VOLCANIC_THREATS[doomsdayDiceGame.volcanicThreat];

    if (doomsdayDiceGame.volcano.pyroclasticFlow) {
        document.getElementById('magmaPressure').innerHTML =
            '<div class="text-yellow-400 font-bold animate-pulse">MAGMA: ERUPTING!</div>';
    } else {
        document.getElementById('magmaPressure').innerHTML =
            '<div class="text-yellow-400 font-bold">MAGMA: CRITICAL</div>';
    }

    if (doomsdayDiceGame.volcano.nuclearWinter) {
        document.getElementById('evacuationStatus').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">EVACUATION: IMPOSSIBLE</div>';
    } else {
        document.getElementById('evacuationStatus').innerHTML =
            '<div class="text-purple-400 font-bold">EVACUATION: FAILED</div>';
    }
}

// Update volcanic effects
function updateVolcanicEffects() {
    // Update volcanic activity meter based on current threat
    const activityBars = document.querySelectorAll('#volcanicActivity .w-2');
    const threatLevel = doomsdayDiceGame.volcano.eruptionRisk;

    activityBars.forEach((bar, index) => {
        if (index < threatLevel * 5) {
            bar.classList.remove('bg-gray-700');
            bar.classList.add(index < 3 ? 'bg-red-400' : 'bg-red-600');
        } else {
            bar.classList.remove('bg-red-400', 'bg-red-600');
            bar.classList.add('bg-gray-700');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${doomsdayDiceGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('gamesPlayed').textContent = doomsdayDiceGame.stats.gamesPlayed;
    document.getElementById('winRate').textContent = `${doomsdayDiceGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${doomsdayDiceGame.stats.totalWagered}`;
    document.getElementById('eruptionsSurvived').textContent = doomsdayDiceGame.stats.eruptionsSurvived;
    document.getElementById('nuclearWinters').textContent = doomsdayDiceGame.stats.nuclearWinters;

    const netResult = doomsdayDiceGame.stats.totalWon - doomsdayDiceGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-green-400' : 'text-red-400'}`;
}

// Update stats after game
function updateGameStatsAfterGame(won, winnings) {
    doomsdayDiceGame.stats.gamesPlayed++;
    doomsdayDiceGame.stats.totalWagered += doomsdayDiceGame.betAmount;
    doomsdayDiceGame.stats.totalWon += winnings;

    if (won) {
        doomsdayDiceGame.stats.gamesWon++;
        doomsdayDiceGame.stats.currentStreak++;
        doomsdayDiceGame.streakData.currentWinStreak++;
        doomsdayDiceGame.streakData.currentLossStreak = 0;

        if (doomsdayDiceGame.streakData.currentWinStreak > doomsdayDiceGame.streakData.longestWinStreak) {
            doomsdayDiceGame.streakData.longestWinStreak = doomsdayDiceGame.streakData.currentWinStreak;
        }

        if (winnings > doomsdayDiceGame.stats.biggestWin) {
            doomsdayDiceGame.stats.biggestWin = winnings;
        }
    } else {
        doomsdayDiceGame.stats.currentStreak = 0;
        doomsdayDiceGame.streakData.currentWinStreak = 0;
        doomsdayDiceGame.streakData.currentLossStreak++;

        if (doomsdayDiceGame.streakData.currentLossStreak > doomsdayDiceGame.streakData.longestLossStreak) {
            doomsdayDiceGame.streakData.longestLossStreak = doomsdayDiceGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be extremely low due to volcanic effects)
    doomsdayDiceGame.stats.winRate = (doomsdayDiceGame.stats.gamesWon / doomsdayDiceGame.stats.gamesPlayed) * 100;

    updateGameStats();
}

// Reset game for next doomsday scenario
function resetGame() {
    doomsdayDiceGame.isPlaying = false;
    doomsdayDiceGame.betAmount = 0;
    doomsdayDiceGame.totalBet = 0;
    doomsdayDiceGame.gameResult = '';
    doomsdayDiceGame.totalWin = 0;
    doomsdayDiceGame.rollsRemaining = 3;

    // Reset volcano system
    doomsdayDiceGame.volcano.currentThreat = 1;
    doomsdayDiceGame.volcano.pyroclasticFlow = false;
    doomsdayDiceGame.volcano.nuclearWinter = false;

    // Reset risk escalator
    doomsdayDiceGame.riskEscalator.active = false;
    doomsdayDiceGame.riskEscalator.currentLevel = 1;

    // Reset dice
    for (let i = 0; i < 6; i++) {
        doomsdayDiceGame.dice[i] = {
            value: 1,
            symbol: '💀',
            locked: false,
            cursed: false
        };

        // Reset dice display
        const diceElement = document.getElementById(`dice${i + 1}`);
        const diceFace = diceElement.querySelector('.dice-face');
        diceFace.textContent = '💀';
        diceElement.className = 'dice-container w-16 h-16 bg-black/70 border-2 border-red-400 rounded-lg flex items-center justify-center cursor-pointer transition-all duration-300';
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('volcanicEvent').classList.add('hidden');
    document.getElementById('diceActions').classList.add('hidden');

    // Reset volcanic status
    document.getElementById('threatLevel').innerHTML =
        '<div class="text-red-400 font-bold">THREAT: YELLOWSTONE</div>';
    document.getElementById('eruptionRisk').innerHTML =
        '<div class="text-orange-400 font-bold">ERUPTION: 98%</div>';
    document.getElementById('magmaPressure').innerHTML =
        '<div class="text-yellow-400 font-bold">MAGMA: CRITICAL</div>';
    document.getElementById('evacuationStatus').innerHTML =
        '<div class="text-purple-400 font-bold">EVACUATION: FAILED</div>';

    // Reset survival chance
    document.getElementById('survivalChanceDisplay').textContent = '0%';

    // Reset risk escalator
    document.getElementById('riskBar').style.width = '95%';
    document.getElementById('currentRisk').textContent = 'Risk: 95%';

    // Reset rolls remaining
    document.getElementById('rollsRemaining').querySelector('.text-2xl').textContent = '3';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable roll button
    document.getElementById('rollDoomsday').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Doomsday approaches...';
    document.getElementById('gameMessage').textContent = 'Welcome to Doomsday Dice - Survive the Volcanic Apocalypse';

    // Reset volcanic activity bars
    const activityBars = document.querySelectorAll('#volcanicActivity .w-2');
    activityBars.forEach(bar => {
        bar.classList.remove('bg-gray-700');
        bar.classList.add('bg-red-400');
    });

    // Reinitialize systems for next game
    initializeDoomsdaySystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDoomsdayDiceGame();
});