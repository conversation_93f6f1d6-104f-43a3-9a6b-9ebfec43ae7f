// Zeus' Thunder Vault - Lightning-Bolt Instant Wins
// Moderate House Edge Implementation with Greek Mythology Theme
// Designed to maintain 3-5% player win rate

// Game state
let balance = 1000;

// Game state object with <PERSON>' divine power system
let zeusThunderGame = {
    isPlaying: false,
    divineMode: 'mortal', // mortal, hero, demigod, olympian, zeus
    stormIntensity: 'moderate', // calm, moderate, fierce, tempest, apocalyptic
    betAmount: 0,
    totalBet: 0,

    // Zeus' divine power system
    zeus: {
        thunderPower: 50, // 0-100 divine power
        lightningBolts: 3, // Available lightning bolts
        divineWrath: 0.25, // 25% divine wrath (reduced)
        olympianFavor: 0.60, // 60% olympian favor (increased)
        stormClouds: 0.40, // 40% storm coverage (moderate)
        thunderstorm: false,
        divineIntervention: false,
        godlyBlessing: 0.30, // 30% blessing chance (increased)
        mortalsAwe: 0.75 // 75% mortal awe (high)
    },

    // Greek mythology elements
    mythology: {
        god: '<PERSON>',
        domain: 'Sky and Thunder',
        weapon: 'Thunderbolt',
        mount: 'Mount Olympus',
        wife: '<PERSON><PERSON>',
        symbol: '<PERSON>',
        temple: 'Temple of Zeus',
        oracle: 'Oracle of Dodona',
        prophecy: 'Lightning shall bring fortune'
    },

    // Vault system (5 reels, 4 rows for divine grid)
    vault: [
        [], [], [], [], []
    ],

    // Lightning-bolt instant win system
    lightningBolts: {
        active: false,
        availableBolts: 3,
        maxBolts: 10,
        strikeChance: 0.25, // 25% strike chance (increased)
        instantWinChance: 0.15, // 15% instant win (increased)
        divineMultiplier: 2.5, // 2.5x divine multiplier
        chainLightning: false,
        thunderstormBonus: 0.40, // 40% thunderstorm bonus (increased)
        zeusBlessing: 0.20, // 20% Zeus blessing (increased)
        lightningCooldown: 0
    },

    // Divine symbols with mythological significance
    symbols: [
        { name: 'Zeus Crown', value: 100, rarity: 0.08, divine: true, lightning: 3.0 },
        { name: 'Thunderbolt', value: 95, rarity: 0.10, divine: true, lightning: 2.8 },
        { name: 'Golden Eagle', value: 90, rarity: 0.12, divine: true, lightning: 2.5 },
        { name: 'Mount Olympus', value: 85, rarity: 0.15, divine: true, lightning: 2.2 },
        { name: 'Storm Cloud', value: 80, rarity: 0.18, divine: false, lightning: 2.0 },
        { name: 'Lightning Rod', value: 75, rarity: 0.20, divine: false, lightning: 1.8 },
        { name: 'Thunder Hammer', value: 70, rarity: 0.25, divine: false, lightning: 1.5 },
        { name: 'Divine Chalice', value: 65, rarity: 0.30, divine: false, lightning: 1.3 },
        { name: 'Golden Laurel', value: 60, rarity: 0.35, divine: false, lightning: 1.2 },
        { name: 'Sacred Olive', value: 55, rarity: 0.40, divine: false, lightning: 1.1 },
        { name: 'Temple Pillar', value: 50, rarity: 0.45, divine: false, lightning: 1.0 },
        { name: 'Mortal Coin', value: 45, rarity: 0.50, divine: false, lightning: 0.9 }
    ],

    gameResult: '',
    totalWin: 0,

    stats: {
        spinsPlayed: 0,
        spinsWon: 0,
        totalWagered: 0,
        totalWon: 0,
        winRate: 0,
        biggestWin: 0,
        currentStreak: 0,
        longestWinStreak: 0,
        longestLossStreak: 0,
        lightningStrikes: 0,
        instantWins: 0,
        divineInterventions: 0,
        thunderstorms: 0,
        zeusAppearances: 0
    },
    streakData: {
        currentLossStreak: 0,
        longestLossStreak: 0,
        currentWinStreak: 0,
        longestWinStreak: 0
    }
};

// Divine modes with moderate bias (3-5% win rate)
const DIVINE_MODES = {
    mortal: {
        name: 'Mortal Realm',
        wrathRisk: 0.20, // 20% wrath risk (reduced)
        favorBonus: 0.60, // 60% favor bonus (increased)
        payoutMultiplier: 0.85, // Good payouts
        lightningChance: 0.20, // 20% lightning chance
        divineBlessing: 0.25 // 25% blessing chance
    },
    hero: {
        name: 'Heroic Favor',
        wrathRisk: 0.25, // 25% wrath risk
        favorBonus: 0.70, // 70% favor bonus
        payoutMultiplier: 0.90, // Better payouts
        lightningChance: 0.25, // 25% lightning chance
        divineBlessing: 0.30 // 30% blessing chance
    },
    demigod: {
        name: 'Demigod Status',
        wrathRisk: 0.30, // 30% wrath risk
        favorBonus: 0.80, // 80% favor bonus
        payoutMultiplier: 0.95, // Excellent payouts
        lightningChance: 0.30, // 30% lightning chance
        divineBlessing: 0.35 // 35% blessing chance
    },
    olympian: {
        name: 'Olympian Power',
        wrathRisk: 0.35, // 35% wrath risk
        favorBonus: 0.90, // 90% favor bonus
        payoutMultiplier: 1.00, // Full payouts
        lightningChance: 0.35, // 35% lightning chance
        divineBlessing: 0.40 // 40% blessing chance
    },
    zeus: {
        name: 'Zeus Incarnate',
        wrathRisk: 0.40, // 40% wrath risk (high risk, high reward)
        favorBonus: 1.00, // 100% favor bonus
        payoutMultiplier: 1.10, // Premium payouts
        lightningChance: 0.45, // 45% lightning chance
        divineBlessing: 0.50 // 50% blessing chance
    }
};

const STORM_INTENSITIES = {
    calm: {
        name: 'Calm Skies',
        lightningMultiplier: 1.10, // 10% lightning bonus
        stormBonus: 0.05, // 5% storm bonus
        thunderChance: 0.10 // 10% thunder chance
    },
    moderate: {
        name: 'Moderate Storm',
        lightningMultiplier: 1.25, // 25% lightning bonus
        stormBonus: 0.15, // 15% storm bonus
        thunderChance: 0.20 // 20% thunder chance
    },
    fierce: {
        name: 'Fierce Tempest',
        lightningMultiplier: 1.40, // 40% lightning bonus
        stormBonus: 0.25, // 25% storm bonus
        thunderChance: 0.35 // 35% thunder chance
    },
    tempest: {
        name: 'Divine Tempest',
        lightningMultiplier: 1.60, // 60% lightning bonus
        stormBonus: 0.40, // 40% storm bonus
        thunderChance: 0.50 // 50% thunder chance
    },
    apocalyptic: {
        name: 'Apocalyptic Storm',
        lightningMultiplier: 2.00, // 100% lightning bonus
        stormBonus: 0.60, // 60% storm bonus
        thunderChance: 0.75 // 75% thunder chance
    }
};

// Improved payout table with Zeus theme (3-5% win rate)
const ZEUS_PAYOUTS = {
    // Divine symbols (moderately reduced)
    FIVE_ZEUS_CROWNS: 800, // Reduced from 1500:1 but still excellent
    FOUR_ZEUS_CROWNS: 400, // Reduced from 750:1
    THREE_ZEUS_CROWNS: 150, // Reduced from 300:1

    FIVE_THUNDERBOLTS: 600, // Reduced from 1200:1
    FOUR_THUNDERBOLTS: 300, // Reduced from 600:1
    THREE_THUNDERBOLTS: 120, // Reduced from 240:1

    FIVE_GOLDEN_EAGLES: 500, // Reduced from 1000:1
    FOUR_GOLDEN_EAGLES: 250, // Reduced from 500:1
    THREE_GOLDEN_EAGLES: 100, // Reduced from 200:1

    // Olympian symbols
    FIVE_MOUNT_OLYMPUS: 400, // Reduced from 800:1
    FOUR_MOUNT_OLYMPUS: 200, // Reduced from 400:1
    THREE_MOUNT_OLYMPUS: 80, // Reduced from 160:1

    // Lightning bonuses (actually apply more often)
    LIGHTNING_STRIKE: 0.60, // 60% of displayed bonus (increased)
    DIVINE_INTERVENTION: 0.45, // 45% of displayed bonus (increased)
    ZEUS_BLESSING: 0.35, // 35% of displayed bonus (increased)
    THUNDERSTORM_BONUS: 0.25 // 25% of displayed bonus (increased)
};

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Load the main game interface
function loadZeusThunderGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- Game Controls -->
            <div class="xl:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">DIVINE CONTROLS</h4>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">DIVINE MODE</label>
                        <select id="divineMode" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="mortal">Mortal Realm</option>
                            <option value="hero">Heroic Favor</option>
                            <option value="demigod">Demigod Status</option>
                            <option value="olympian">Olympian Power</option>
                            <option value="zeus">Zeus Incarnate</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">STORM INTENSITY</label>
                        <select id="stormIntensity" class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                            <option value="calm">Calm Skies</option>
                            <option value="moderate" selected>Moderate Storm</option>
                            <option value="fierce">Fierce Tempest</option>
                            <option value="tempest">Divine Tempest</option>
                            <option value="apocalyptic">Apocalyptic Storm</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="betAmount" value="10" min="1" max="${Math.min(balance, 500)}"
                               class="w-full bg-black/50 border border-blue-500/50 rounded-lg px-3 py-2 text-white">
                    </div>

                    <button id="openVault" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                        OPEN THUNDER VAULT
                    </button>

                    <div id="lightningActions" class="space-y-2 hidden">
                        <button id="strikeLightning" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                            STRIKE LIGHTNING
                        </button>
                        <button id="callThunderstorm" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                            CALL THUNDERSTORM
                        </button>
                        <button id="divineIntervention" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                            DIVINE INTERVENTION
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <div class="text-sm text-gray-400 mb-1">Current Bet</div>
                        <div id="currentBetDisplay" class="text-xl font-bold text-yellow-400">$0</div>
                        <div class="text-sm text-gray-400 mb-1 mt-2">Lightning Bolts</div>
                        <div id="lightningBoltsDisplay" class="text-lg font-bold text-blue-400">3</div>
                    </div>
                </div>

                <!-- Zeus Status -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">ZEUS STATUS</h5>
                    <div class="text-xs space-y-2">
                        <div id="thunderPower" class="text-center p-2 rounded bg-black/50">
                            <div class="text-yellow-400 font-bold">POWER: 50%</div>
                        </div>
                        <div id="divineWrath" class="text-center p-2 rounded bg-black/50">
                            <div class="text-red-400 font-bold">WRATH: 25%</div>
                        </div>
                        <div id="olympianFavor" class="text-center p-2 rounded bg-black/50">
                            <div class="text-green-400 font-bold">FAVOR: 60%</div>
                        </div>
                        <div id="stormClouds" class="text-center p-2 rounded bg-black/50">
                            <div class="text-purple-400 font-bold">STORM: 40%</div>
                        </div>
                    </div>
                </div>

                <!-- Improved Payout Table -->
                <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 mt-4">
                    <h5 class="text-lg font-bold mb-3 text-blue-400">DIVINE PAYOUTS</h5>
                    <div class="text-xs space-y-1">
                        <div class="text-yellow-400 font-bold mb-2">Zeus' Treasures:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Zeus Crowns:</span>
                            <span class="text-blue-400">800:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Thunderbolts:</span>
                            <span class="text-blue-400">600:1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">5 Golden Eagles:</span>
                            <span class="text-blue-400">500:1</span>
                        </div>
                        <div class="text-yellow-400 font-bold mb-2 mt-3">Lightning Bonuses:</div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Lightning Strike:</span>
                            <span class="text-blue-400">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Divine Intervention:</span>
                            <span class="text-blue-400">45%</span>
                        </div>
                        <div class="text-xs text-blue-400 mt-2">*Lightning bolts boost wins</div>
                        <div class="text-xs text-blue-400">*Divine favor increases payouts</div>
                    </div>
                </div>
            </div>

            <!-- Main Olympian Vault Interface -->
            <div class="xl:col-span-3">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <!-- Zeus' Thunder Vault -->
                    <div id="thunderVault" class="relative bg-gradient-to-br from-black via-blue-900 to-black rounded-lg p-6 h-96 overflow-hidden">
                        <!-- Divine Background -->
                        <div id="divineBackground" class="absolute inset-0 pointer-events-none opacity-40">
                            <svg class="w-full h-full">
                                <defs>
                                    <radialGradient id="divineGradient" cx="50%" cy="30%" r="60%">
                                        <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8" />
                                        <stop offset="50%" style="stop-color:#4169e1;stop-opacity:0.6" />
                                        <stop offset="100%" style="stop-color:#000080;stop-opacity:0.4" />
                                    </radialGradient>
                                    <pattern id="lightningPattern" width="80" height="80" patternUnits="userSpaceOnUse">
                                        <path d="M40,10 L30,40 L50,40 L35,70" stroke="#ffff00" stroke-width="2" fill="none" opacity="0.3"/>
                                        <circle cx="20" cy="20" r="3" fill="#ffffff" opacity="0.5"/>
                                        <circle cx="60" cy="60" r="3" fill="#ffffff" opacity="0.5"/>
                                    </pattern>
                                </defs>
                                <rect width="100%" height="100%" fill="url(#lightningPattern)" />
                                <ellipse id="divineAura" cx="50%" cy="30%" rx="50%" ry="35%" fill="url(#divineGradient)" class="animate-pulse" />
                                <g id="lightningEffects">
                                    <!-- Lightning effects will be drawn here -->
                                </g>
                            </svg>
                        </div>

                        <!-- Divine Vault Display Area (5x4 for olympian grid) -->
                        <div class="absolute top-4 left-1/2 transform -translate-x-1/2">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">ZEUS' THUNDER VAULT</div>
                                <div id="vaultDisplay" class="grid grid-cols-5 gap-2">
                                    <!-- 5 reels, 4 rows each for divine structure -->
                                    <div id="reel1" class="flex flex-col space-y-1">
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="0" data-row="0"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="0" data-row="1"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="0" data-row="2"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="0" data-row="3"></div>
                                    </div>
                                    <div id="reel2" class="flex flex-col space-y-1">
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="1" data-row="0"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="1" data-row="1"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="1" data-row="2"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="1" data-row="3"></div>
                                    </div>
                                    <div id="reel3" class="flex flex-col space-y-1">
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="2" data-row="0"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="2" data-row="1"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="2" data-row="2"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="2" data-row="3"></div>
                                    </div>
                                    <div id="reel4" class="flex flex-col space-y-1">
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="3" data-row="0"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="3" data-row="1"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="3" data-row="2"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="3" data-row="3"></div>
                                    </div>
                                    <div id="reel5" class="flex flex-col space-y-1">
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="4" data-row="0"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="4" data-row="1"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="4" data-row="2"></div>
                                        <div class="vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30" data-reel="4" data-row="3"></div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400 mt-2">Click symbols to strike with lightning</div>
                            </div>
                        </div>

                        <!-- Thunder Power Progress -->
                        <div id="thunderProgress" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-80">
                            <div class="text-center">
                                <div class="text-sm text-blue-400 mb-2">THUNDER POWER</div>
                                <div class="w-full bg-gray-700 rounded-full h-4 mb-2">
                                    <div id="thunderBar" class="bg-gradient-to-r from-blue-400 to-yellow-400 h-4 rounded-full transition-all duration-1000 animate-pulse" style="width: 50%"></div>
                                </div>
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>Mortal</span>
                                    <span id="currentThunder">Power: 50%</span>
                                    <span>Divine</span>
                                </div>
                            </div>
                        </div>

                        <!-- Lightning Strike Counter -->
                        <div id="lightningCounter" class="absolute top-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-yellow-400 mb-1">LIGHTNING BOLTS</div>
                                <div class="text-2xl font-bold text-white text-center">3</div>
                                <div class="text-xs text-gray-400 mt-1">Available</div>
                            </div>
                        </div>

                        <!-- Storm Intensity Meter -->
                        <div id="stormMeter" class="absolute bottom-4 right-4">
                            <div class="bg-black/70 rounded-lg p-3">
                                <div class="text-xs text-purple-400 mb-1">STORM INTENSITY</div>
                                <div class="flex space-x-1">
                                    <div class="w-2 h-8 bg-purple-400 rounded animate-pulse"></div>
                                    <div class="w-2 h-8 bg-purple-400 rounded animate-pulse" style="animation-delay: 0.1s"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded"></div>
                                    <div class="w-2 h-8 bg-gray-700 rounded"></div>
                                </div>
                                <div class="text-xs text-purple-400 mt-1">MODERATE</div>
                            </div>
                        </div>

                        <!-- Game Status -->
                        <div class="absolute top-1/2 left-4 transform -translate-y-1/2">
                            <div id="gameStatus" class="text-xl font-bold text-yellow-400 mb-2">Thunder vault awaits...</div>
                            <div id="winAmount" class="text-lg font-bold text-green-400 hidden">+$0</div>
                            <div id="zeusEvent" class="text-sm font-bold text-blue-400 hidden animate-pulse">ZEUS APPEARS!</div>
                        </div>
                    </div>
                    <div id="gameMessage" class="text-center mt-4 text-lg font-semibold">Welcome to Zeus' Thunder Vault - Command the Power of Lightning</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics -->
        <div class="mt-8 grid grid-cols-2 md:grid-cols-6 gap-4">
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Spins Played</div>
                <div id="spinsPlayed" class="text-xl font-bold text-white">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Win Rate</div>
                <div id="winRate" class="text-xl font-bold text-blue-400">0%</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Total Wagered</div>
                <div id="totalWagered" class="text-xl font-bold text-yellow-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Net Result</div>
                <div id="netResult" class="text-xl font-bold text-blue-400">$0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Lightning Strikes</div>
                <div id="lightningStrikes" class="text-xl font-bold text-yellow-400">0</div>
            </div>
            <div class="bg-black/30 p-4 rounded-xl border border-blue-500/30 text-center">
                <div class="text-sm text-gray-400">Instant Wins</div>
                <div id="instantWins" class="text-xl font-bold text-green-400">0</div>
            </div>
        </div>
    `;

    initializeZeusThunder();
}

// Initialize the game
function initializeZeusThunder() {
    document.getElementById('openVault').addEventListener('click', openThunderVault);
    document.getElementById('strikeLightning').addEventListener('click', strikeLightning);
    document.getElementById('callThunderstorm').addEventListener('click', callThunderstorm);
    document.getElementById('divineIntervention').addEventListener('click', activateDivineIntervention);

    // Add lightning listeners to all vault symbols
    const vaultSymbols = document.querySelectorAll('.vault-symbol');
    vaultSymbols.forEach(symbol => {
        symbol.addEventListener('click', (e) => {
            const reel = parseInt(e.target.dataset.reel);
            const row = parseInt(e.target.dataset.row);
            strikeSymbolWithLightning(reel, row, e.target);
        });
    });

    // Initialize Zeus systems
    initializeZeusSystems();
    generateLightningEffects();
    updateGameStats();
}

// Initialize Zeus systems
function initializeZeusSystems() {
    // Reset Zeus system
    zeusThunderGame.zeus.thunderPower = 50;
    zeusThunderGame.zeus.lightningBolts = 3;
    zeusThunderGame.zeus.divineWrath = 0.25;
    zeusThunderGame.zeus.olympianFavor = 0.60;
    zeusThunderGame.zeus.stormClouds = 0.40;
    zeusThunderGame.zeus.thunderstorm = false;
    zeusThunderGame.zeus.divineIntervention = false;
    zeusThunderGame.zeus.godlyBlessing = 0.30;
    zeusThunderGame.zeus.mortalsAwe = 0.75;

    // Reset mythology elements
    zeusThunderGame.mythology.god = 'Zeus';
    zeusThunderGame.mythology.domain = getRandomDomain();
    zeusThunderGame.mythology.weapon = 'Thunderbolt';
    zeusThunderGame.mythology.mount = 'Mount Olympus';
    zeusThunderGame.mythology.wife = getRandomGoddess();
    zeusThunderGame.mythology.symbol = getRandomSymbol();
    zeusThunderGame.mythology.temple = getRandomTemple();
    zeusThunderGame.mythology.oracle = getRandomOracle();
    zeusThunderGame.mythology.prophecy = getRandomProphecy();

    // Reset lightning-bolt system
    zeusThunderGame.lightningBolts.active = false;
    zeusThunderGame.lightningBolts.availableBolts = 3;
    zeusThunderGame.lightningBolts.strikeChance = 0.25;
    zeusThunderGame.lightningBolts.instantWinChance = 0.15;
    zeusThunderGame.lightningBolts.divineMultiplier = 2.5;
    zeusThunderGame.lightningBolts.chainLightning = false;
    zeusThunderGame.lightningBolts.thunderstormBonus = 0.40;
    zeusThunderGame.lightningBolts.zeusBlessing = 0.20;
    zeusThunderGame.lightningBolts.lightningCooldown = 0;

    // Reset vault
    for (let i = 0; i < 5; i++) {
        zeusThunderGame.vault[i] = [];
    }

    updateZeusDisplay();
}

// Generate lightning effects
function generateLightningEffects() {
    const container = document.getElementById('lightningEffects');
    container.innerHTML = '';

    for (let i = 0; i < 6; i++) {
        const lightning = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const startX = Math.random() * 100;
        const startY = Math.random() * 30;
        const endX = startX + (Math.random() - 0.5) * 40;
        const endY = startY + Math.random() * 70 + 30;

        lightning.setAttribute('d', `M${startX},${startY} L${endX},${endY} L${endX-5},${endY-10} M${endX},${endY} L${endX+5},${endY-10}`);
        lightning.setAttribute('stroke', '#ffff00');
        lightning.setAttribute('stroke-width', '2');
        lightning.setAttribute('fill', 'none');
        lightning.setAttribute('opacity', '0.7');
        lightning.classList.add('animate-pulse');
        lightning.style.animationDelay = `${i * 0.4}s`;
        container.appendChild(lightning);
    }
}

// Get random mythological elements
function getRandomDomain() {
    const domains = ['Sky and Thunder', 'King of Gods', 'Divine Justice', 'Weather Control', 'Olympian Ruler'];
    return domains[Math.floor(Math.random() * domains.length)];
}

function getRandomGoddess() {
    const goddesses = ['Hera', 'Metis', 'Themis', 'Eurynome', 'Demeter'];
    return goddesses[Math.floor(Math.random() * goddesses.length)];
}

function getRandomSymbol() {
    const symbols = ['Eagle', 'Thunderbolt', 'Oak Tree', 'Bull', 'Aegis'];
    return symbols[Math.floor(Math.random() * symbols.length)];
}

function getRandomTemple() {
    const temples = ['Temple of Zeus at Olympia', 'Temple of Zeus at Dodona', 'Temple of Zeus at Athens'];
    return temples[Math.floor(Math.random() * temples.length)];
}

function getRandomOracle() {
    const oracles = ['Oracle of Dodona', 'Oracle of Delphi', 'Oracle of Ammon'];
    return oracles[Math.floor(Math.random() * oracles.length)];
}

function getRandomProphecy() {
    const prophecies = ['Lightning shall bring fortune', 'Thunder heralds victory', 'Divine favor awaits the worthy'];
    return prophecies[Math.floor(Math.random() * prophecies.length)];
}

// Open thunder vault
function openThunderVault() {
    const betAmount = parseInt(document.getElementById('betAmount').value);

    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }

    if (betAmount <= 0) {
        alert('Please enter a valid bet amount!');
        return;
    }

    // Deduct bet from balance
    balance -= betAmount;
    updateBalance();

    // Set game state
    zeusThunderGame.isPlaying = true;
    zeusThunderGame.betAmount = betAmount;
    zeusThunderGame.totalBet = betAmount;
    zeusThunderGame.divineMode = document.getElementById('divineMode').value;
    zeusThunderGame.stormIntensity = document.getElementById('stormIntensity').value;

    // Activate Zeus systems
    activateZeusSystems();

    // Start vault opening with divine power
    setTimeout(() => {
        openDivineVault();
    }, 1000);

    // Update display
    updateBetDisplay();
    updateGameStats();
    document.getElementById('openVault').disabled = true;
    document.getElementById('lightningActions').classList.remove('hidden');
    document.getElementById('gameStatus').textContent = 'Thunder vault opening...';
}

// Activate Zeus systems
function activateZeusSystems() {
    const divineData = DIVINE_MODES[zeusThunderGame.divineMode];
    const stormData = STORM_INTENSITIES[zeusThunderGame.stormIntensity];

    zeusThunderGame.zeus.divineWrath = divineData.wrathRisk;
    zeusThunderGame.zeus.olympianFavor = divineData.favorBonus;
    zeusThunderGame.lightningBolts.strikeChance = divineData.lightningChance;

    // Check for divine intervention activation (increased chance)
    if (Math.random() < divineData.divineBlessing) {
        activateDivineIntervention();
    }

    // Check for thunderstorm
    if (Math.random() < stormData.thunderChance) {
        activateThunderstorm();
    }

    // Update Zeus elements
    updateZeusElements();

    // Update visual effects
    updateZeusDisplay();
    updateStormEffects();
}

// Activate divine intervention
function activateDivineIntervention() {
    zeusThunderGame.zeus.divineIntervention = true;
    zeusThunderGame.stats.divineInterventions++;
    zeusThunderGame.lightningBolts.active = true;

    document.getElementById('olympianFavor').innerHTML =
        '<div class="text-green-400 font-bold animate-pulse">FAVOR: DIVINE!</div>';

    // Show Zeus event
    document.getElementById('zeusEvent').classList.remove('hidden');
    document.getElementById('zeusEvent').textContent = 'DIVINE INTERVENTION!';
    setTimeout(() => {
        document.getElementById('zeusEvent').classList.add('hidden');
    }, 3000);
}

// Activate thunderstorm
function activateThunderstorm() {
    zeusThunderGame.zeus.thunderstorm = true;
    zeusThunderGame.stats.thunderstorms++;
    zeusThunderGame.zeus.thunderPower = Math.min(100, zeusThunderGame.zeus.thunderPower + 25);

    document.getElementById('stormClouds').innerHTML =
        '<div class="text-purple-400 font-bold animate-pulse">STORM: RAGING!</div>';
}

// Update Zeus elements
function updateZeusElements() {
    document.getElementById('thunderPower').innerHTML =
        `<div class="text-yellow-400 font-bold">POWER: ${zeusThunderGame.zeus.thunderPower}%</div>`;
    document.getElementById('divineWrath').innerHTML =
        `<div class="text-red-400 font-bold">WRATH: ${Math.floor(zeusThunderGame.zeus.divineWrath * 100)}%</div>`;
    document.getElementById('olympianFavor').innerHTML =
        `<div class="text-green-400 font-bold">FAVOR: ${Math.floor(zeusThunderGame.zeus.olympianFavor * 100)}%</div>`;
    document.getElementById('stormClouds').innerHTML =
        `<div class="text-purple-400 font-bold">STORM: ${Math.floor(zeusThunderGame.zeus.stormClouds * 100)}%</div>`;
}

// Open divine vault with improved win rate (3-5%)
function openDivineVault() {
    document.getElementById('gameStatus').textContent = 'Divine symbols materializing...';

    // Generate symbols for each reel with improved balance
    for (let reel = 0; reel < 5; reel++) {
        zeusThunderGame.vault[reel] = [];
        for (let row = 0; row < 4; row++) { // 4 rows for divine structure
            const symbol = generateBalancedDivineSymbol();
            zeusThunderGame.vault[reel].push(symbol);
        }
    }

    // Animate vault opening with divine effects
    animateDivineVaultOpening();

    // Resolve after vault opens
    setTimeout(() => {
        resolveZeusSpin();
    }, 4000);
}

// Generate balanced divine symbol (3-5% win rate)
function generateBalancedDivineSymbol() {
    const divineData = DIVINE_MODES[zeusThunderGame.divineMode];

    // Apply moderate bias - better chance for divine symbols
    if (Math.random() < 0.30) { // 30% chance for divine symbols
        const divineSymbols = zeusThunderGame.symbols.filter(s => s.divine === true);
        return divineSymbols[Math.floor(Math.random() * divineSymbols.length)];
    } else if (Math.random() < 0.60) { // 60% chance for high-value symbols
        const highValueSymbols = zeusThunderGame.symbols.filter(s => s.value >= 70);
        return highValueSymbols[Math.floor(Math.random() * highValueSymbols.length)];
    } else {
        // Random selection from all symbols
        return selectSymbolByBalancedRarity();
    }
}

// Select symbol by balanced rarity (improved distribution)
function selectSymbolByBalancedRarity() {
    const random = Math.random();
    let cumulativeRarity = 0;

    // Normal order to give fair distribution
    for (let i = 0; i < zeusThunderGame.symbols.length; i++) {
        const symbol = zeusThunderGame.symbols[i];
        cumulativeRarity += symbol.rarity;

        if (random <= cumulativeRarity) {
            return symbol;
        }
    }

    // Fallback to random symbol
    return zeusThunderGame.symbols[Math.floor(Math.random() * zeusThunderGame.symbols.length)];
}

// Animate divine vault opening
function animateDivineVaultOpening() {
    for (let reel = 0; reel < 5; reel++) {
        for (let row = 0; row < 4; row++) {
            const symbolElement = document.querySelector(`[data-reel="${reel}"][data-row="${row}"]`);

            // Add divine opening animation
            symbolElement.classList.add('animate-pulse');

            setTimeout(() => {
                symbolElement.classList.remove('animate-pulse');
                displaySymbolInVault(zeusThunderGame.vault[reel][row], symbolElement);

                // Update thunder power during opening
                updateThunderPower();
            }, 1000 + reel * 300 + row * 100);
        }
    }
}

// Display symbol in vault
function displaySymbolInVault(symbol, element) {
    const symbolIcon = getDivineSymbolIcon(symbol.name);

    element.innerHTML = `
        <div class="text-lg">${symbolIcon}</div>
        <div class="text-xs text-white font-bold">${symbol.name.split(' ')[0]}</div>
    `;

    // Add symbol-specific styling
    if (symbol.divine) {
        element.classList.add('ring-2', 'ring-blue-400', 'animate-pulse', 'bg-blue-900/50');
    } else if (symbol.value >= 80) {
        element.classList.add('ring-1', 'ring-yellow-400');
    } else if (symbol.name.includes('Lightning') || symbol.name.includes('Thunder')) {
        element.classList.add('ring-1', 'ring-purple-400');
    }
}

// Get divine symbol icon
function getDivineSymbolIcon(symbolName) {
    const icons = {
        'Zeus Crown': '👑',
        'Thunderbolt': '⚡',
        'Golden Eagle': '🦅',
        'Mount Olympus': '🏔️',
        'Storm Cloud': '⛈️',
        'Lightning Rod': '🔱',
        'Thunder Hammer': '🔨',
        'Divine Chalice': '🏆',
        'Golden Laurel': '🏅',
        'Sacred Olive': '🫒',
        'Temple Pillar': '🏛️',
        'Mortal Coin': '🪙'
    };

    return icons[symbolName] || '⚡';
}

// Update thunder power
function updateThunderPower() {
    if (zeusThunderGame.lightningBolts.active) {
        const stormData = STORM_INTENSITIES[zeusThunderGame.stormIntensity];

        // Grow thunder power based on storm intensity
        const growth = 5 * stormData.lightningMultiplier;
        zeusThunderGame.zeus.thunderPower = Math.min(100, zeusThunderGame.zeus.thunderPower + growth);
    }

    updateThunderDisplay();
}

// Update thunder display
function updateThunderDisplay() {
    const power = zeusThunderGame.zeus.thunderPower;

    document.getElementById('thunderBar').style.width = `${power}%`;
    document.getElementById('currentThunder').textContent = `Power: ${power}%`;
    document.getElementById('lightningBoltsDisplay').textContent = zeusThunderGame.zeus.lightningBolts;
    document.getElementById('lightningCounter').querySelector('.text-2xl').textContent = zeusThunderGame.zeus.lightningBolts;
}

// Strike symbol with lightning
function strikeSymbolWithLightning(reel, row, element) {
    if (!zeusThunderGame.isPlaying || zeusThunderGame.zeus.lightningBolts <= 0) return;

    zeusThunderGame.stats.lightningStrikes++;
    zeusThunderGame.zeus.lightningBolts--;

    // Check for instant win (increased chance)
    if (Math.random() < zeusThunderGame.lightningBolts.instantWinChance) {
        // Lightning instant win!
        triggerInstantWin(element);
        zeusThunderGame.stats.instantWins++;

        // Show Zeus event
        document.getElementById('zeusEvent').classList.remove('hidden');
        document.getElementById('zeusEvent').textContent = 'LIGHTNING INSTANT WIN!';
        setTimeout(() => {
            document.getElementById('zeusEvent').classList.add('hidden');
        }, 2000);
    } else {
        // Regular lightning strike
        applyLightningStrike(element);
    }

    // Update lightning display
    updateThunderDisplay();
}

// Trigger instant win
function triggerInstantWin(element) {
    element.classList.add('ring-4', 'ring-yellow-400', 'bg-yellow-900/70', 'animate-pulse');
    element.style.filter = 'brightness(2) saturate(2)';

    // Add lightning symbol
    const lightningIcon = document.createElement('div');
    lightningIcon.className = 'absolute top-0 right-0 text-yellow-400 text-lg animate-bounce';
    lightningIcon.textContent = '⚡';
    element.style.position = 'relative';
    element.appendChild(lightningIcon);

    // Instant win amount (based on bet)
    const instantWinAmount = Math.floor(zeusThunderGame.betAmount * (2 + Math.random() * 3)); // 2-5x bet
    zeusThunderGame.totalWin += instantWinAmount;
}

// Apply lightning strike
function applyLightningStrike(element) {
    element.classList.add('ring-2', 'ring-blue-400', 'bg-blue-900/50', 'animate-pulse');
    element.style.filter = 'brightness(1.5) hue-rotate(45deg)';

    // Add strike symbol
    const strikeIcon = document.createElement('div');
    strikeIcon.className = 'absolute top-0 right-0 text-blue-400 text-sm';
    strikeIcon.textContent = '⚡';
    element.style.position = 'relative';
    element.appendChild(strikeIcon);
}

// Strike lightning (button action)
function strikeLightning() {
    if (zeusThunderGame.zeus.lightningBolts <= 0) return;

    // Random lightning strike on vault
    const randomReel = Math.floor(Math.random() * 5);
    const randomRow = Math.floor(Math.random() * 4);
    const element = document.querySelector(`[data-reel="${randomReel}"][data-row="${randomRow}"]`);

    strikeSymbolWithLightning(randomReel, randomRow, element);
}

// Call thunderstorm
function callThunderstorm() {
    if (zeusThunderGame.zeus.thunderPower < 50) return;

    zeusThunderGame.zeus.thunderstorm = true;
    zeusThunderGame.zeus.thunderPower -= 30; // Cost thunder power
    zeusThunderGame.stats.thunderstorms++;

    // Multiple lightning strikes
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            strikeLightning();
        }, i * 500);
    }

    updateThunderDisplay();
}

// Resolve Zeus spin with improved win rate (3-5%)
function resolveZeusSpin() {
    const divineData = DIVINE_MODES[zeusThunderGame.divineMode];
    const stormData = STORM_INTENSITIES[zeusThunderGame.stormIntensity];

    let totalWinnings = zeusThunderGame.totalWin; // Include instant wins
    let resultMessage = '';

    // Calculate symbol winnings with improved payouts
    const allSymbols = [];
    zeusThunderGame.vault.forEach(reel => {
        allSymbols.push(...reel);
    });

    // Count symbol occurrences
    const symbolCounts = {};
    allSymbols.forEach(symbol => {
        symbolCounts[symbol.name] = (symbolCounts[symbol.name] || 0) + 1;
    });

    // Calculate payouts (improved rates)
    Object.entries(symbolCounts).forEach(([symbolName, count]) => {
        if (count >= 3) { // Need at least 3 symbols
            const symbol = zeusThunderGame.symbols.find(s => s.name === symbolName);
            if (symbol) {
                const payoutKey = symbolName.toUpperCase().replace(/[^A-Z]/g, '_');
                if (ZEUS_PAYOUTS[payoutKey]) {
                    let symbolPayout = zeusThunderGame.betAmount * (ZEUS_PAYOUTS[payoutKey] / 100); // Better conversion

                    // Apply count multiplier (improved)
                    if (count === 4) symbolPayout *= 1.5; // Better multiplier
                    if (count === 5) symbolPayout *= 2.0; // Better multiplier

                    totalWinnings += symbolPayout;
                }
            }
        }
    });

    // Apply lightning strike bonus (actually applies more often)
    if (zeusThunderGame.stats.lightningStrikes > 0 && totalWinnings > 0) {
        const lightningBonus = Math.floor(totalWinnings * ZEUS_PAYOUTS.LIGHTNING_STRIKE);
        totalWinnings += lightningBonus;
        resultMessage += ' + Lightning Bonus!';
    }

    // Apply divine intervention bonus
    if (zeusThunderGame.zeus.divineIntervention && totalWinnings > 0) {
        const divineBonus = Math.floor(totalWinnings * ZEUS_PAYOUTS.DIVINE_INTERVENTION);
        totalWinnings += divineBonus;
        resultMessage += ' + Divine Intervention!';
    }

    // Apply thunderstorm bonus
    if (zeusThunderGame.zeus.thunderstorm && totalWinnings > 0) {
        const stormBonus = Math.floor(totalWinnings * ZEUS_PAYOUTS.THUNDERSTORM_BONUS);
        totalWinnings += stormBonus;
        resultMessage += ' + Thunderstorm Bonus!';
    }

    // Apply divine favor multiplier (actually works)
    if (zeusThunderGame.zeus.olympianFavor > 0.5 && totalWinnings > 0) {
        totalWinnings = Math.floor(totalWinnings * (1 + zeusThunderGame.zeus.olympianFavor * 0.5));
        resultMessage += ' + Divine Favor!';
    }

    // Apply divine mode multiplier (improved)
    totalWinnings = Math.floor(totalWinnings * divineData.payoutMultiplier);

    // Check for divine wrath (reduced impact)
    if (Math.random() < divineData.wrathRisk) {
        totalWinnings = Math.floor(totalWinnings * 0.7); // Only 30% reduction instead of total loss
        resultMessage += ' - Divine Wrath!';
    }

    // Ensure reasonable minimum win rate (3-5%)
    if (totalWinnings === 0 && Math.random() < 0.04) { // 4% chance for consolation win
        totalWinnings = Math.floor(zeusThunderGame.betAmount * 0.6); // 60% return
        resultMessage = 'Zeus shows mercy';
    }

    // Add winnings to balance
    balance += totalWinnings;
    zeusThunderGame.totalWin = totalWinnings;

    // Update display
    updateBalance();
    updateGameStatsAfterSpin(totalWinnings > zeusThunderGame.betAmount, totalWinnings);

    if (!resultMessage) {
        if (totalWinnings > zeusThunderGame.betAmount) {
            resultMessage = 'Zeus blesses your vault!';
        } else if (totalWinnings > 0) {
            resultMessage = 'Divine favor granted';
        } else {
            resultMessage = 'The gods test your resolve';
        }
    }

    document.getElementById('gameStatus').textContent = resultMessage;
    document.getElementById('gameMessage').innerHTML = `${resultMessage} - Divine Mode: ${DIVINE_MODES[zeusThunderGame.divineMode].name}`;

    if (totalWinnings > 0) {
        document.getElementById('winAmount').textContent = `+$${totalWinnings}`;
        document.getElementById('winAmount').classList.remove('hidden');
    }

    setTimeout(() => resetGame(), 6000);
}

// Update Zeus display
function updateZeusDisplay() {
    const divineData = DIVINE_MODES[zeusThunderGame.divineMode];

    if (zeusThunderGame.zeus.divineIntervention) {
        document.getElementById('olympianFavor').innerHTML =
            '<div class="text-green-400 font-bold animate-pulse">FAVOR: DIVINE!</div>';
    } else {
        document.getElementById('olympianFavor').innerHTML =
            `<div class="text-green-400 font-bold">FAVOR: ${Math.floor(zeusThunderGame.zeus.olympianFavor * 100)}%</div>`;
    }

    if (zeusThunderGame.zeus.thunderstorm) {
        document.getElementById('stormClouds').innerHTML =
            '<div class="text-purple-400 font-bold animate-pulse">STORM: RAGING!</div>';
    } else {
        document.getElementById('stormClouds').innerHTML =
            `<div class="text-purple-400 font-bold">STORM: ${Math.floor(zeusThunderGame.zeus.stormClouds * 100)}%</div>`;
    }
}

// Update storm effects
function updateStormEffects() {
    // Update storm intensity meter based on current storm
    const stormBars = document.querySelectorAll('#stormMeter .w-2');
    const stormLevel = zeusThunderGame.zeus.stormClouds;

    stormBars.forEach((bar, index) => {
        if (index < stormLevel * 5) {
            bar.classList.remove('bg-gray-700');
            bar.classList.add('bg-purple-400');
        } else {
            bar.classList.remove('bg-purple-400');
            bar.classList.add('bg-gray-700');
        }
    });
}

// Update bet display
function updateBetDisplay() {
    document.getElementById('currentBetDisplay').textContent = `$${zeusThunderGame.betAmount}`;
}

// Update game statistics
function updateGameStats() {
    document.getElementById('spinsPlayed').textContent = zeusThunderGame.stats.spinsPlayed;
    document.getElementById('winRate').textContent = `${zeusThunderGame.stats.winRate.toFixed(1)}%`;
    document.getElementById('totalWagered').textContent = `$${zeusThunderGame.stats.totalWagered}`;
    document.getElementById('lightningStrikes').textContent = zeusThunderGame.stats.lightningStrikes;
    document.getElementById('instantWins').textContent = zeusThunderGame.stats.instantWins;

    const netResult = zeusThunderGame.stats.totalWon - zeusThunderGame.stats.totalWagered;
    document.getElementById('netResult').textContent = `$${netResult}`;
    document.getElementById('netResult').className = `text-xl font-bold ${netResult >= 0 ? 'text-blue-400' : 'text-red-400'}`;
}

// Update stats after spin
function updateGameStatsAfterSpin(won, winnings) {
    zeusThunderGame.stats.spinsPlayed++;
    zeusThunderGame.stats.totalWagered += zeusThunderGame.betAmount;
    zeusThunderGame.stats.totalWon += winnings;

    if (won) {
        zeusThunderGame.stats.spinsWon++;
        zeusThunderGame.stats.currentStreak++;
        zeusThunderGame.streakData.currentWinStreak++;
        zeusThunderGame.streakData.currentLossStreak = 0;

        if (zeusThunderGame.streakData.currentWinStreak > zeusThunderGame.streakData.longestWinStreak) {
            zeusThunderGame.streakData.longestWinStreak = zeusThunderGame.streakData.currentWinStreak;
        }

        if (winnings > zeusThunderGame.stats.biggestWin) {
            zeusThunderGame.stats.biggestWin = winnings;
        }
    } else {
        zeusThunderGame.stats.currentStreak = 0;
        zeusThunderGame.streakData.currentWinStreak = 0;
        zeusThunderGame.streakData.currentLossStreak++;

        if (zeusThunderGame.streakData.currentLossStreak > zeusThunderGame.streakData.longestLossStreak) {
            zeusThunderGame.streakData.longestLossStreak = zeusThunderGame.streakData.currentLossStreak;
        }
    }

    // Calculate win rate (should be 3-5% due to improved mechanics)
    zeusThunderGame.stats.winRate = (zeusThunderGame.stats.spinsWon / zeusThunderGame.stats.spinsPlayed) * 100;

    updateGameStats();
}

// Reset game for next divine vault
function resetGame() {
    zeusThunderGame.isPlaying = false;
    zeusThunderGame.betAmount = 0;
    zeusThunderGame.totalBet = 0;
    zeusThunderGame.gameResult = '';
    zeusThunderGame.totalWin = 0;

    // Reset Zeus system
    zeusThunderGame.zeus.thunderstorm = false;
    zeusThunderGame.zeus.divineIntervention = false;
    zeusThunderGame.zeus.lightningBolts = 3;
    zeusThunderGame.zeus.thunderPower = 50;

    // Reset lightning-bolt system
    zeusThunderGame.lightningBolts.active = false;
    zeusThunderGame.lightningBolts.chainLightning = false;
    zeusThunderGame.lightningBolts.lightningCooldown = 0;

    // Reset vault
    for (let i = 0; i < 5; i++) {
        zeusThunderGame.vault[i] = [];
    }

    // Clear displays
    document.getElementById('winAmount').classList.add('hidden');
    document.getElementById('zeusEvent').classList.add('hidden');
    document.getElementById('lightningActions').classList.add('hidden');

    // Reset vault displays
    const vaultSymbols = document.querySelectorAll('.vault-symbol');
    vaultSymbols.forEach(symbol => {
        symbol.innerHTML = '';
        symbol.className = 'vault-symbol w-12 h-12 bg-black/50 border border-blue-400 rounded flex items-center justify-center text-xs cursor-pointer transition-all duration-300 hover:bg-blue-900/30';
        symbol.style.filter = '';
        symbol.style.position = '';

        // Remove any added elements
        const addedElements = symbol.querySelectorAll('.absolute');
        addedElements.forEach(el => el.remove());
    });

    // Reset Zeus status
    document.getElementById('thunderPower').innerHTML =
        '<div class="text-yellow-400 font-bold">POWER: 50%</div>';
    document.getElementById('divineWrath').innerHTML =
        '<div class="text-red-400 font-bold">WRATH: 25%</div>';
    document.getElementById('olympianFavor').innerHTML =
        '<div class="text-green-400 font-bold">FAVOR: 60%</div>';
    document.getElementById('stormClouds').innerHTML =
        '<div class="text-purple-400 font-bold">STORM: 40%</div>';

    // Reset thunder display
    document.getElementById('thunderBar').style.width = '50%';
    document.getElementById('currentThunder').textContent = 'Power: 50%';
    document.getElementById('lightningBoltsDisplay').textContent = '3';
    document.getElementById('lightningCounter').querySelector('.text-2xl').textContent = '3';

    // Reset bet inputs
    document.getElementById('betAmount').value = 10;

    // Reset bet displays
    updateBetDisplay();

    // Enable open button
    document.getElementById('openVault').disabled = false;

    // Reset status
    document.getElementById('gameStatus').textContent = 'Thunder vault awaits...';
    document.getElementById('gameMessage').textContent = 'Welcome to Zeus\' Thunder Vault - Command the Power of Lightning';

    // Reset storm bars
    const stormBars = document.querySelectorAll('#stormMeter .w-2');
    stormBars.forEach((bar, index) => {
        if (index < 2) {
            bar.classList.remove('bg-gray-700');
            bar.classList.add('bg-purple-400');
        } else {
            bar.classList.remove('bg-purple-400');
            bar.classList.add('bg-gray-700');
        }
    });

    // Reinitialize systems for next vault
    initializeZeusSystems();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadZeusThunderGame();
});