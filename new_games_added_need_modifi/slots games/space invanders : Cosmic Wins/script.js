// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Space Invaders Game Implementation
const SPACE_SYMBOLS = {
    MOTHERSHIP: '🛸',
    FIGHTER: '🚀',
    ALIEN: '👽',
    LASER: '⚡',
    SHIELD: '🛡️',
    PLANET: '🪐',
    STAR: '⭐',
    ASTEROID: '☄️',
    CRYSTAL: '💎',
    SCATTER: 'S'
};

const SPACE_REEL_SYMBOLS = [
    SPACE_SYMBOLS.MOTHERSHIP, SPACE_SYMBOLS.FIGHTER, SPACE_SYMBOLS.ALIEN, 
    SPACE_SYMBOLS.LASER, SPACE_SYMBOLS.SHIELD, SPACE_SYMBOLS.PLANET,
    SPACE_SYMBOLS.STAR, SPACE_SYMBOLS.ASTEROID, SPACE_SYMBOLS.CRYSTAL, SPACE_SYMBOLS.SCATTER
];

let spaceInvadersGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    waveLevel: 1,
    invasionMode: false,
    shieldPower: 100,
    alienCount: 0
};

function loadSpaceInvadersGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400 font-mono">SPACE COMMAND</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">MISSION BUDGET</label>
                        <input type="number" id="spaceBet" value="40" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startSpaceMission" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        LAUNCH MISSION
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Missions:</span>
                            <span id="spaceFreeSpins" class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Reward:</span>
                            <span id="spaceLastWin" class="text-cyan-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Wave Level:</span>
                            <span id="waveLevel" class="text-yellow-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Shield Power:</span>
                            <span id="shieldPower" class="text-blue-400">100%</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-green-900/20 rounded-lg border border-green-500/20">
                        <h5 class="text-sm font-bold mb-2 text-green-300 font-mono">ALIEN INTEL</h5>
                        <div class="text-xs space-y-1">
                            <div>🛸 <span class="text-purple-400">Mothership:</span> 80x bet</div>
                            <div>🚀 <span class="text-blue-400">Fighter:</span> 50x bet</div>
                            <div>👽 <span class="text-green-400">Alien:</span> 30x bet</div>
                            <div>⚡ <span class="text-yellow-400">Laser:</span> 20x bet</div>
                            <div>🛡️ <span class="text-cyan-400">Shield:</span> Wild symbol</div>
                            <div>S <span class="text-red-400">Signal:</span> Scatter bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <div class="relative mb-6">
                        <div id="spaceReels" class="grid grid-cols-5 gap-2 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-green-900/20 rounded-lg flex items-center justify-center text-4xl border border-green-500/20 transition-all duration-300">🛸</div>`
                            ).join('')}
                        </div>
                        <div id="invasionEffect" class="absolute inset-0 bg-gradient-to-b from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                    </div>
                    
                    <div id="spaceGameStatus" class="text-center text-lg font-semibold text-green-300 mb-4 h-8 font-mono">
                        Scanning for alien activity...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-green-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">MISSION STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Threat Level:</span>
                                    <span id="threatLevel" class="text-orange-400">MODERATE</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Invasion Mode:</span>
                                    <span id="invasionStatus" class="text-red-400">INACTIVE</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-green-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">SHIELD STATUS</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="shieldBar" class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupSpaceInvadersGame();
}

function setupSpaceInvadersGame() {
    document.getElementById('startSpaceMission').addEventListener('click', startSpaceMission);
    const reelsContainer = document.getElementById('spaceReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-green-900/20 rounded-lg p-2 text-center text-4xl border border-green-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = SPACE_REEL_SYMBOLS[i % SPACE_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateSpaceDisplay();
}

function startSpaceMission() {
    if (spaceInvadersGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('spaceBet').value);

    if (spaceInvadersGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('spaceGameStatus').textContent = 'INSUFFICIENT MISSION FUNDS';
            return;
        }
        balance -= totalBet;
    } else {
        spaceInvadersGame.freeSpins--;
    }

    spaceInvadersGame.isSpinning = true;
    spaceInvadersGame.lastWin = 0;
    updateBalance();
    updateSpaceDisplay();
    document.getElementById('spaceGameStatus').textContent = 'Engaging alien forces...';

    const slots = document.querySelectorAll('#spaceReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1800;
    let spinInterval = 60;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = SPACE_REEL_SYMBOLS[Math.floor(Math.random() * SPACE_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishSpaceMission(totalBet);
        }
    }, spinInterval);
}

function finishSpaceMission(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#spaceReels .slot');
    slots.forEach(slot => {
        const symbol = SPACE_REEL_SYMBOLS[Math.floor(Math.random() * SPACE_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkSpaceWins(finalSymbols, totalBet);
}

function checkSpaceWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for space victories
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === SPACE_SYMBOLS.MOTHERSHIP && count >= 3) {
            multiplier = count >= 5 ? 80 : count >= 4 ? 50 : 25;
            statusMessage = 'MOTHERSHIP DESTROYED! Maximum rewards!';
            spaceInvadersGame.invasionMode = true;
        } else if (symbol === SPACE_SYMBOLS.FIGHTER && count >= 3) {
            multiplier = count >= 5 ? 50 : count >= 4 ? 30 : 15;
            statusMessage = 'Fighter squadron eliminated!';
        } else if (symbol === SPACE_SYMBOLS.ALIEN && count >= 3) {
            multiplier = count >= 5 ? 30 : count >= 4 ? 20 : 10;
            statusMessage = 'Alien forces defeated!';
        } else if (symbol === SPACE_SYMBOLS.LASER && count >= 3) {
            multiplier = count >= 5 ? 20 : count >= 4 ? 12 : 6;
            statusMessage = 'Laser weapons secured!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Shield wild bonus
    const shieldCount = symbols.filter(s => s === SPACE_SYMBOLS.SHIELD).length;
    if (shieldCount > 0 && totalWin > 0) {
        const shieldMultiplier = 1 + (shieldCount * 0.5);
        totalWin *= shieldMultiplier;
        statusMessage += ` Shield boost: ${shieldMultiplier}x!`;
        spaceInvadersGame.shieldPower = Math.min(100, spaceInvadersGame.shieldPower + 20);
    }

    // Scatter bonus (S Signal symbols)
    const scatterCount = symbols.filter(s => s === SPACE_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 12 + (scatterCount - 3) * 6;
        spaceInvadersGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` ALIEN SIGNAL INTERCEPTED! +${freeSpinsAwarded} free missions!`;
        spaceInvadersGame.waveLevel++;
        
        // Trigger invasion effect
        document.getElementById('invasionEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('invasionEffect').style.opacity = '0';
        }, 2000);
    }

    // Invasion mode bonus
    if (spaceInvadersGame.invasionMode && totalWin > 0) {
        totalWin *= 2;
        statusMessage += ' INVASION REPELLED - DOUBLE REWARDS!';
        setTimeout(() => { spaceInvadersGame.invasionMode = false; }, 3000);
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        spaceInvadersGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#spaceReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Mission unsuccessful. Regrouping...';
        spaceInvadersGame.shieldPower = Math.max(0, spaceInvadersGame.shieldPower - 10);
    }

    document.getElementById('spaceGameStatus').textContent = statusMessage;
    spaceInvadersGame.isSpinning = false;
    updateSpaceDisplay();
}

function updateSpaceDisplay() {
    const spinButton = document.getElementById('startSpaceMission');
    spinButton.disabled = spaceInvadersGame.isSpinning;
    spinButton.textContent = spaceInvadersGame.isSpinning ? 'ENGAGING...' : 'LAUNCH MISSION';

    document.getElementById('spaceFreeSpins').textContent = spaceInvadersGame.freeSpins;
    document.getElementById('spaceLastWin').textContent = `${spaceInvadersGame.lastWin} GA`;
    document.getElementById('waveLevel').textContent = spaceInvadersGame.waveLevel;
    document.getElementById('shieldPower').textContent = `${spaceInvadersGame.shieldPower}%`;
    document.getElementById('shieldBar').style.width = `${spaceInvadersGame.shieldPower}%`;
    document.getElementById('invasionStatus').textContent = spaceInvadersGame.invasionMode ? 'ACTIVE!' : 'INACTIVE';
    
    // Update threat level based on wave
    const threatLevels = ['LOW', 'MODERATE', 'HIGH', 'CRITICAL', 'EXTREME'];
    const threatIndex = Math.min(spaceInvadersGame.waveLevel - 1, threatLevels.length - 1);
    document.getElementById('threatLevel').textContent = threatLevels[threatIndex];
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSpaceInvadersGame();
});

