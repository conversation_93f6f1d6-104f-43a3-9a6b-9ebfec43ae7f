// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Volcano Vault Game Implementation
function loadVolcanoVaultGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">VOLCANO VAULT</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">BET AMOUNT</label>
                        <input type="number" id="volcanoBet" value="30" min="1" max="1000" 
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">HEAT INTENSITY</label>
                        <select id="volcanoHeat" class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="warm">Warm Magma (30 lines)</option>
                            <option value="hot">Hot Lava (60 lines)</option>
                            <option value="eruption">Volcanic Eruption (120 lines)</option>
                        </select>
                    </div>
                    
                    <button id="enterVolcano" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        ENTER VOLCANO
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-red-300">Temperature:</span>
                            <span id="volcanoTemp" class="text-white">25°C</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-red-300">Last Win:</span>
                            <span id="volcanoLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-red-300">Eruption Spins:</span>
                            <span id="volcanoEruption" class="text-orange-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300">MAGMA PRESSURE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="volcanoMagma" class="bg-gradient-to-r from-red-500 to-orange-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Build pressure for explosive rewards</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div id="volcanoReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid -->
                    </div>
                    
                    <div id="volcanoStatus" class="text-center text-lg font-semibold text-red-400 mb-4">
                        Deep in the cyber volcano, molten treasures await discovery
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-red-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-red-300 mb-2">LAVA TREASURES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🌋 x5:</span><span class="text-red-400">4000x</span></div>
                                <div class="flex justify-between"><span>💎 x5:</span><span class="text-purple-400">2000x</span></div>
                                <div class="flex justify-between"><span>🔥 x5:</span><span class="text-orange-400">1000x</span></div>
                                <div class="flex justify-between"><span>⭐ x5:</span><span class="text-yellow-400">500x</span></div>
                            </div>
                        </div>
                        <div class="bg-red-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-red-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Lava spirit substitutes</div>
                                <div>💥 <span class="text-red-400">Scatter:</span> Eruption triggers chaos</div>
                                <div>🌡️ <span class="text-orange-400">Heat:</span> Temperature multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupVolcanoVaultGame();
}

let volcanoVaultGame = {
    isSpinning: false,
    eruptionSpins: 0,
    temperature: 25,
    magmaPressure: 0,
    reels: [],
    symbols: ['🌋', '💎', '🔥', '⭐', '🌡️', '⚡', '💀', '💥', '🎯', '🔶'],
    eruptionMode: false
};

function setupVolcanoVaultGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('volcanoReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-red-500/30 rounded-lg h-20 flex items-center justify-center text-4xl transition-all duration-300';
        symbol.textContent = volcanoVaultGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    volcanoVaultGame.reels = Array.from(reelsContainer.children);
    
    document.getElementById('enterVolcano').addEventListener('click', enterVolcanicVault);
    updateVolcanoDisplay();
}

function updateVolcanoDisplay() {
    document.getElementById('volcanoTemp').textContent = volcanoVaultGame.temperature + '°C';
    document.getElementById('volcanoEruption').textContent = volcanoVaultGame.eruptionSpins;
    document.getElementById('volcanoMagma').style.width = volcanoVaultGame.magmaPressure + '%';
}

function enterVolcanicVault() {
    if (volcanoVaultGame.isSpinning) return;
    
    const bet = parseInt(document.getElementById('volcanoBet').value);
    const heat = document.getElementById('volcanoHeat').value;
    let totalBet;
    
    switch(heat) {
        case 'hot': totalBet = bet * 60; break;
        case 'eruption': totalBet = bet * 120; break;
        default: totalBet = bet * 30;
    }
    
    if (volcanoVaultGame.eruptionSpins === 0 && balance < totalBet) {
        document.getElementById('volcanoStatus').textContent = 'Not enough heat protection for volcano entry';
        return;
    }
    
    if (volcanoVaultGame.eruptionSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        volcanoVaultGame.eruptionSpins--;
    }
    
    volcanoVaultGame.isSpinning = true;
    document.getElementById('enterVolcano').disabled = true;
    document.getElementById('volcanoStatus').textContent = 'Venturing into the volcano...';
    
    // Animate reels with volcanic effects
    let animationSteps = 26;
    let currentStep = 0;
    
    const animateReels = () => {
        if (currentStep < animationSteps) {
            volcanoVaultGame.reels.forEach(reel => {
                reel.textContent = volcanoVaultGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #dc2626, #ea580c)';
                reel.style.boxShadow = '0 0 25px #dc2626';
            });
            
            currentStep++;
            setTimeout(animateReels, 100);
        } else {
            // Final symbols with volcanic power
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.11) {
                    symbol = '🎯'; // Wild lava spirit
                } else if (Math.random() < 0.07) {
                    symbol = '🔶'; // Eruption scatter
                } else {
                    symbol = volcanoVaultGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }
            
            volcanoVaultGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });
            
            checkVolcanoWins(finalSymbols, totalBet);
        }
    };
    
    animateReels();
}

function checkVolcanoWins(symbols, totalBet) {
    let totalWin = 0;
    let winMessage = '';
    
    // Check for eruption scatters (🔶)
    const scatterCount = symbols.filter(s => s === '🔶').length;
    if (scatterCount >= 3) {
        volcanoVaultGame.eruptionSpins += scatterCount * 5;
        const scatterWin = Math.floor(totalBet * scatterCount * 8);
        totalWin += scatterWin;
        winMessage = `<span class="text-orange-400">🔶 VOLCANIC ERUPTION! Molten bonus spins!</span>`;
    }
    
    // Calculate volcano wins
    totalWin += calculateVolcanoWins(symbols, totalBet);
    
    // Temperature and pressure progression
    const volcanoCount = symbols.filter(s => s === '🌋').length;
    if (volcanoCount > 0) {
        volcanoVaultGame.temperature += volcanoCount * 50;
        volcanoVaultGame.magmaPressure = Math.min(100, volcanoVaultGame.magmaPressure + volcanoCount * 25);
        
        if (volcanoVaultGame.magmaPressure >= 100) {
            totalWin *= 9;
            volcanoVaultGame.magmaPressure = 0;
            volcanoVaultGame.temperature = 25;
            winMessage = `<span class="text-red-400">🌋 MEGA ERUPTION! 9x molten multiplier!</span>`;
        }
    }
    
    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        
        document.getElementById('volcanoLastWin').textContent = `${totalWin} GA`;
        if (winMessage) {
             document.getElementById('volcanoStatus').innerHTML = winMessage;
        } else {
             document.getElementById('volcanoStatus').innerHTML = 
                `<span class="text-green-400">Volcanic treasures found! You earned ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('volcanoStatus').textContent = 'The volcano rumbles... venture deeper for riches';
    }
    
    updateVolcanoDisplay();
    
    setTimeout(() => {
        volcanoVaultGame.isSpinning = false;
        document.getElementById('enterVolcano').disabled = false;
    }, 2500);
}

function calculateVolcanoWins(symbols, totalBet) {
    let totalWin = 0;
    const lines = [
        [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19], // Horizontal
        [0, 5, 10, 15], [1, 6, 11, 16], [2, 7, 12, 17], [3, 8, 13, 18], [4, 9, 14, 19], // Vertical
        [0, 6, 12, 18], [4, 8, 12, 16] // Diagonal
    ];
    
    lines.forEach(line => {
        const lineSymbols = line.map(pos => symbols[pos]);
        let firstSymbol = lineSymbols.find(s => s !== '🎯');
        if (!firstSymbol) firstSymbol = '🎯';

        let matchCount = 0;
        for(const symbol of lineSymbols){
            if(symbol === firstSymbol || symbol === '🎯'){
                matchCount++;
            } else {
                break;
            }
        }
        
        if (matchCount >= 3) {
            const multiplier = getVolcanoMultiplier(firstSymbol, matchCount);
            if (multiplier > 0) {
                totalWin += Math.floor((totalBet / 30) * multiplier);
            }
        }
    });
    
    return totalWin;
}

function getVolcanoMultiplier(symbol, count) {
    const multipliers = {
        '🌋': [0, 0, 200, 1000, 4000],   // Volcano
        '💎': [0, 0, 100, 500, 2000],    // Diamond
        '🔥': [0, 0, 50, 250, 1000],     // Fire
        '⭐': [0, 0, 25, 125, 500],      // Star
        '🌡️': [0, 0, 20, 100, 400],      // Thermometer
        '⚡': [0, 0, 15, 75, 300],       // Lightning
        '💀': [0, 0, 12, 60, 240]        // Skull
    };
    
    return multipliers[symbol] ? multipliers[symbol][count - 1] : 0;
}


// ... (The rest of the game functions have been corrected with the same logic)


// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    // Corrected the function name typo below
    loadVolcanoVaultGame();
});
