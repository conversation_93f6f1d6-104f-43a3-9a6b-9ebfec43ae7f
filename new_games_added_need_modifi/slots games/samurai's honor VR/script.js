// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Samurai's Honor VR Game Implementation
const SAMURAI_SYMBOLS = {
    SHOGUN: '👺',
    KATANA: '⚔️',
    CHERRY_BLOSSOM: '🌸',
    DRAGON: '🐉',
    TEMPLE: '⛩️',
    HONOR_SCROLL: '📜',
    SAKE_CUP: '🍶',
    BAMBOO: '🎋',
    WILD: '🌟',
    SCATTER: '🎌'
};

const SAMURAI_REEL_SYMBOLS = [
    SAMURAI_SYMBOLS.SHOGUN, SAMURAI_SYMBOLS.KATANA, SAMURAI_SYMBOLS.CHERRY_BLOSSOM,
    SAMURAI_SYMBOLS.DRAGON, SAMURAI_SYMBOLS.TEMPLE, SAMURAI_SYMBOLS.HONOR_SCROLL,
    SAMURAI_SYMBOLS.SAKE_CUP, SAMURAI_SYMBOLS.BAMBOO, SAMURAI_SYMBOLS.WILD, SAMURAI_SYMBOLS.SCATTER
];

let samuraiHonorGame = {
    isSpinning: false,
    honorSpins: 0,
    lastWin: 0,
    bushidoLevel: 1,
    honorPoints: 0,
    spiritPower: 100,
    vrImmersion: 100,
    dragonBlessing: false,
    shogunFavor: false,
    cherryBlossomSeason: false,
    perfectHarmony: false,
    ancestralSpirit: false,
    meditationBonus: 0
};

function loadSamuraiHonorGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400 font-mono">SAMURAI'S HONOR VR</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">HONOR WAGER</label>
                        <input type="number" id="samuraiBet" value="45" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">VR EXPERIENCE</label>
                        <select id="vrExperience" class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="dojo">Dojo Training (5x)</option>
                            <option value="battlefield">Battlefield (10x)</option>
                            <option value="spiritual">Spiritual Realm (20x)</option>
                        </select>
                    </div>
                    
                    <button id="beginHonorQuest" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        BEGIN HONOR QUEST
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Honor Spins:</span>
                            <span id="samuraiHonorSpins" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Victory:</span>
                            <span id="samuraiLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Bushido Level:</span>
                            <span id="bushidoLevel" class="text-yellow-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Honor Points:</span>
                            <span id="honorPoints" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">VR Immersion:</span>
                            <span id="vrImmersion" class="text-blue-400">100%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Meditation:</span>
                            <span id="meditationBonus" class="text-cyan-400">0x</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300 font-mono">BUSHIDO CODE</h5>
                        <div class="text-xs space-y-1">
                            <div>👺 <span class="text-red-400">Shogun:</span> 777x bet</div>
                            <div>⚔️ <span class="text-gray-400">Katana:</span> 555x bet</div>
                            <div>🐉 <span class="text-green-400">Dragon:</span> 444x bet</div>
                            <div>🌸 <span class="text-pink-400">Cherry Blossom:</span> 333x bet</div>
                            <div>⛩️ <span class="text-orange-400">Temple:</span> 250x bet</div>
                            <div>🌟 <span class="text-white">Wild:</span> Substitutes all</div>
                            <div>🎌 <span class="text-red-400">Scatter:</span> Honor bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div class="relative mb-6">
                        <div id="samuraiReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-red-900/20 rounded-lg flex items-center justify-center text-3xl border border-red-500/20 transition-all duration-300">👺</div>`
                            ).join('')}
                        </div>
                        <div id="dragonBlessingEffect" class="absolute inset-0 bg-gradient-to-t from-green-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="shogunFavorEffect" class="absolute inset-0 bg-gradient-to-b from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="cherryBlossomEffect" class="absolute inset-0 bg-gradient-radial from-pink-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="spiritualRealmEffect" class="absolute inset-0 bg-gradient-conic from-purple-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="ancestralSpiritEffect" class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-white/10 to-blue-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                    </div>
                    
                    <div id="samuraiGameStatus" class="text-center text-lg font-semibold text-red-300 mb-4 h-8 font-mono">
                        The path of honor awaits your virtual journey...
                    </div>
                    
                    <div class="grid grid-cols-5 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">SPIRIT POWER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="spiritMeter" class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500" style="width: 100%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="spiritLevel" class="text-blue-400">100%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DRAGON BLESSING</h6>
                            <div class="text-center">
                                <span id="dragonStatus" class="text-green-400">DORMANT</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">SHOGUN FAVOR</h6>
                            <div class="text-center">
                                <span id="shogunStatus" class="text-red-400">NEUTRAL</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CHERRY SEASON</h6>
                            <div class="text-center">
                                <span id="cherryStatus" class="text-pink-400">WAITING</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">HARMONY</h6>
                            <div class="text-center">
                                <span id="harmonyStatus" class="text-purple-400">SEEKING</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupSamuraiHonorGame();
}

function setupSamuraiHonorGame() {
    document.getElementById('beginHonorQuest').addEventListener('click', beginHonorQuest);
    const reelsContainer = document.getElementById('samuraiReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-red-900/20 rounded-lg p-2 text-center text-3xl border border-red-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = SAMURAI_REEL_SYMBOLS[i % SAMURAI_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateSamuraiDisplay();
}

function beginHonorQuest() {
    if (samuraiHonorGame.isSpinning) return;

    const bet = parseInt(document.getElementById('samuraiBet').value);
    const experience = document.getElementById('vrExperience').value;
    let totalBet;
    
    switch(experience) {
        case 'battlefield': totalBet = bet * 10; break;
        case 'spiritual': totalBet = bet * 20; break;
        default: totalBet = bet * 5;
    }

    if (samuraiHonorGame.honorSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('samuraiGameStatus').textContent = 'INSUFFICIENT HONOR TO BEGIN QUEST';
            return;
        }
        balance -= totalBet;
    } else {
        samuraiHonorGame.honorSpins--;
    }

    samuraiHonorGame.isSpinning = true;
    samuraiHonorGame.lastWin = 0;
    updateBalance();
    updateSamuraiDisplay();
    document.getElementById('samuraiGameStatus').textContent = 'Walking the path of bushido in virtual reality...';

    const slots = document.querySelectorAll('#samuraiReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'honor-highlight'));

    // Spiritual realm effect during spin
    document.getElementById('spiritualRealmEffect').style.opacity = '0.4';

    let spinDuration = 3000;
    let spinInterval = 130;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = SAMURAI_REEL_SYMBOLS[Math.floor(Math.random() * SAMURAI_REEL_SYMBOLS.length)];
            slot.style.transform = `rotateY(${Math.random() * 360}deg) scale(${0.9 + Math.random() * 0.2})`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('spiritualRealmEffect').style.opacity = '0';
            slots.forEach(slot => slot.style.transform = 'rotateY(0deg) scale(1)');
            finishHonorQuest(totalBet);
        }
    }, spinInterval);
}

function finishHonorQuest(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#samuraiReels .slot');
    
    // Enhanced symbol generation with VR experience bonuses
    const experience = document.getElementById('vrExperience').value;
    let wildChance = 0.13;
    let scatterChance = 0.09;
    let shogunChance = 0.17;
    
    if (experience === 'battlefield') {
        wildChance = 0.17;
        scatterChance = 0.13;
        shogunChance = 0.23;
    } else if (experience === 'spiritual') {
        wildChance = 0.26;
        scatterChance = 0.19;
        shogunChance = 0.32;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = SAMURAI_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = SAMURAI_SYMBOLS.SCATTER;
        } else if (Math.random() < shogunChance) {
            symbol = SAMURAI_SYMBOLS.SHOGUN;
        } else {
            symbol = SAMURAI_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkSamuraiHonor(finalSymbols, totalBet);
}

function checkSamuraiHonor(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (legendary honor)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== SAMURAI_SYMBOLS.WILD && symbol !== SAMURAI_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === SAMURAI_SYMBOLS.SHOGUN) {
                multiplier = 777;
                statusMessage = 'THE SHOGUN GRANTS ULTIMATE HONOR!';
                samuraiHonorGame.honorPoints += 250;
                samuraiHonorGame.shogunFavor = true;
            } else if (symbol === SAMURAI_SYMBOLS.KATANA) {
                multiplier = 555;
                statusMessage = 'LEGENDARY KATANA MASTERY ACHIEVED!';
                samuraiHonorGame.honorPoints += 200;
            } else if (symbol === SAMURAI_SYMBOLS.DRAGON) {
                multiplier = 444;
                statusMessage = 'DRAGON SPIRIT AWAKENS!';
                samuraiHonorGame.honorPoints += 180;
                samuraiHonorGame.dragonBlessing = true;
            } else if (symbol === SAMURAI_SYMBOLS.CHERRY_BLOSSOM) {
                multiplier = 333;
                statusMessage = 'ETERNAL CHERRY BLOSSOM SEASON!';
                samuraiHonorGame.honorPoints += 150;
                samuraiHonorGame.cherryBlossomSeason = true;
            } else if (symbol === SAMURAI_SYMBOLS.TEMPLE) {
                multiplier = 250;
                statusMessage = 'SACRED TEMPLE BLESSING!';
                samuraiHonorGame.honorPoints += 120;
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind and 3-of-a-kind with similar logic...
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== SAMURAI_SYMBOLS.WILD && symbol !== SAMURAI_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === SAMURAI_SYMBOLS.SHOGUN) multiplier = 250;
            else if (symbol === SAMURAI_SYMBOLS.KATANA) multiplier = 180;
            else if (symbol === SAMURAI_SYMBOLS.DRAGON) multiplier = 150;
            else if (symbol === SAMURAI_SYMBOLS.CHERRY_BLOSSOM) multiplier = 120;
            else if (symbol === SAMURAI_SYMBOLS.TEMPLE) multiplier = 100;
            else if (symbol === SAMURAI_SYMBOLS.HONOR_SCROLL) multiplier = 80;
            else if (symbol === SAMURAI_SYMBOLS.SAKE_CUP) multiplier = 70;
            else if (symbol === SAMURAI_SYMBOLS.BAMBOO) multiplier = 60;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== SAMURAI_SYMBOLS.WILD && symbol !== SAMURAI_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === SAMURAI_SYMBOLS.SHOGUN) multiplier = 100;
            else if (symbol === SAMURAI_SYMBOLS.KATANA) multiplier = 75;
            else if (symbol === SAMURAI_SYMBOLS.DRAGON) multiplier = 65;
            else if (symbol === SAMURAI_SYMBOLS.CHERRY_BLOSSOM) multiplier = 55;
            else if (symbol === SAMURAI_SYMBOLS.TEMPLE) multiplier = 45;
            else if (symbol === SAMURAI_SYMBOLS.HONOR_SCROLL) multiplier = 40;
            else if (symbol === SAMURAI_SYMBOLS.SAKE_CUP) multiplier = 35;
            else if (symbol === SAMURAI_SYMBOLS.BAMBOO) multiplier = 30;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === SAMURAI_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.8);
        totalWin *= wildMultiplier;
        statusMessage += ` Spiritual energy: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter honor bonus
    const scatterCount = symbols.filter(s => s === SAMURAI_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const honorSpinsAwarded = 20 + (scatterCount - 3) * 10;
        samuraiHonorGame.honorSpins += honorSpinsAwarded;
        samuraiHonorGame.bushidoLevel = Math.min(samuraiHonorGame.bushidoLevel + 1, 10);
        statusMessage += ` 🎌 HONOR QUEST! ${honorSpinsAwarded} honor spins!`;
        
        // Increase VR immersion
        samuraiHonorGame.vrImmersion = Math.min(100, samuraiHonorGame.vrImmersion + 20);
    }

    // Meditation bonus system
    if (totalWin > 0) {
        samuraiHonorGame.meditationBonus++;
        const meditationMultiplier = 1 + (samuraiHonorGame.meditationBonus * 0.15);
        totalWin *= meditationMultiplier;
    } else {
        samuraiHonorGame.meditationBonus = Math.max(0, samuraiHonorGame.meditationBonus - 1);
        samuraiHonorGame.spiritPower = Math.max(0, samuraiHonorGame.spiritPower - 15);
    }

    // Dragon blessing activation
    if (samuraiHonorGame.dragonBlessing && samuraiHonorGame.honorPoints >= 500) {
        const dragonMultiplier = 15 + samuraiHonorGame.bushidoLevel;
        totalWin *= dragonMultiplier;
        statusMessage = `🐉 DRAGON'S ETERNAL BLESSING! ${dragonMultiplier}x DIVINE POWER!`;
        samuraiHonorGame.honorPoints = 0;
        
        // Trigger dragon blessing effect
        document.getElementById('dragonBlessingEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('dragonBlessingEffect').style.opacity = '0';
            samuraiHonorGame.dragonBlessing = false;
        }, 6000);
    }

    // Shogun favor activation
    if (samuraiHonorGame.shogunFavor && samuraiHonorGame.honorPoints >= 400) {
        const shogunMultiplier = 12 + samuraiHonorGame.bushidoLevel;
        totalWin *= shogunMultiplier;
        statusMessage = `👺 SHOGUN'S IMPERIAL FAVOR! ${shogunMultiplier}x ROYAL HONOR!`;
        samuraiHonorGame.honorPoints += 100; // Shogun grants additional honor
        
        // Trigger shogun favor effect
        document.getElementById('shogunFavorEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('shogunFavorEffect').style.opacity = '0';
            samuraiHonorGame.shogunFavor = false;
        }, 5000);
    }

    // Cherry blossom season
    if (samuraiHonorGame.cherryBlossomSeason && samuraiHonorGame.spiritPower >= 80) {
        const blossomMultiplier = 8 + samuraiHonorGame.bushidoLevel;
        totalWin *= blossomMultiplier;
        statusMessage = `🌸 CHERRY BLOSSOM ENLIGHTENMENT! ${blossomMultiplier}x NATURAL HARMONY!`;
        samuraiHonorGame.spiritPower = 100; // Restore full spirit
        
        // Trigger cherry blossom effect
        document.getElementById('cherryBlossomEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('cherryBlossomEffect').style.opacity = '0';
            samuraiHonorGame.cherryBlossomSeason = false;
        }, 4000);
    }

    // Perfect harmony achievement
    if (samuraiHonorGame.spiritPower === 100 && samuraiHonorGame.honorPoints >= 300 && samuraiHonorGame.vrImmersion === 100) {
        samuraiHonorGame.perfectHarmony = true;
        totalWin *= 10;
        statusMessage = '☯️ PERFECT BUSHIDO HARMONY ACHIEVED! 10x ENLIGHTENMENT!';
        samuraiHonorGame.ancestralSpirit = true;
    }

    // Ancestral spirit guidance
    if (samuraiHonorGame.ancestralSpirit && Math.random() < 0.35) {
        totalWin += totalBet * 200;
        statusMessage += ' 👻 ANCESTRAL SPIRITS GUIDE YOU!';
        
        // Trigger ancestral spirit effect
        document.getElementById('ancestralSpiritEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('ancestralSpiritEffect').style.opacity = '0';
        }, 8000);
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        samuraiHonorGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#samuraiReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('honor-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The path of honor requires patience and perseverance...';
    }

    document.getElementById('samuraiGameStatus').textContent = statusMessage;
    samuraiHonorGame.isSpinning = false;
    updateSamuraiDisplay();
}

function updateSamuraiDisplay() {
    const spinButton = document.getElementById('beginHonorQuest');
    spinButton.disabled = samuraiHonorGame.isSpinning;
    spinButton.textContent = samuraiHonorGame.isSpinning ? 'QUESTING...' : 'BEGIN HONOR QUEST';

    document.getElementById('samuraiHonorSpins').textContent = samuraiHonorGame.honorSpins;
    document.getElementById('samuraiLastWin').textContent = `${samuraiHonorGame.lastWin} GA`;
    document.getElementById('bushidoLevel').textContent = samuraiHonorGame.bushidoLevel;
    document.getElementById('honorPoints').textContent = samuraiHonorGame.honorPoints;
    document.getElementById('vrImmersion').textContent = `${samuraiHonorGame.vrImmersion}%`;
    document.getElementById('meditationBonus').textContent = `${samuraiHonorGame.meditationBonus}x`;
    document.getElementById('spiritLevel').textContent = `${samuraiHonorGame.spiritPower}%`;
    
    document.getElementById('dragonStatus').textContent = samuraiHonorGame.dragonBlessing ? 'AWAKENED!' : 'DORMANT';
    document.getElementById('shogunStatus').textContent = samuraiHonorGame.shogunFavor ? 'FAVORED!' : 'NEUTRAL';
    document.getElementById('cherryStatus').textContent = samuraiHonorGame.cherryBlossomSeason ? 'BLOOMING!' : 'WAITING';
    document.getElementById('harmonyStatus').textContent = samuraiHonorGame.perfectHarmony ? 'ACHIEVED!' : 'SEEKING';
    
    // Update spirit meter
    document.getElementById('spiritMeter').style.width = `${samuraiHonorGame.spiritPower}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSamuraiHonorGame();
});

