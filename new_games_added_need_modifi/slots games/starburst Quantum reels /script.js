// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Starburst Quantum Reels Game Implementation
const QUANTUM_SYMBOLS = {
    QUANTUM_WILD: '🌌',
    NEUTRON_STAR: '⭐',
    PLASMA_CRYSTAL: '💎',
    COSMIC_ENERGY: '⚡',
    DARK_MATTER: '🌑',
    PHOTON_BEAM: '🔆',
    ANTIMATTER: '⚛️',
    QUASAR: '🌠',
    WILD: '🌟',
    SCATTER: '🔮'
};

const QUANTUM_REEL_SYMBOLS = [
    QUANTUM_SYMBOLS.QUANTUM_WILD, QUANTUM_SYMBOLS.NEUTRON_STAR, QUANTUM_SYMBOLS.PLASMA_CRYSTAL,
    QUANTUM_SYMBOLS.COSMIC_ENERGY, QUANTUM_SYMBOLS.DARK_MATTER, QUANTUM_SYMBOLS.PHOTON_BEAM,
    QUANTUM_SYMBOLS.ANTIMATTER, QUANTUM_SYMBOLS.QUASAR, QUANTUM_SYMBOLS.WILD, QUANTUM_SYMBOLS.SCATTER
];

let quantumStarburstGame = {
    isSpinning: false,
    quantumSpins: 0,
    lastWin: 0,
    quantumLevel: 1,
    energyCharge: 0,
    quantumMultiplier: 1,
    expandedWilds: [],
    respinCount: 0,
    quantumField: false,
    starburstMode: false,
    cosmicAlignment: false,
    dimensionalRift: false,
    quantumEntanglement: 0,
    particleAccelerator: false
};

function loadQuantumStarburstGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400 font-mono">STARBURST QUANTUM REELS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">QUANTUM ENERGY</label>
                        <input type="number" id="quantumBet" value="40" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">QUANTUM MODE</label>
                        <select id="quantumMode" class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="standard">Standard Field (6x)</option>
                            <option value="enhanced">Enhanced Field (12x)</option>
                            <option value="quantum">Quantum Field (24x)</option>
                        </select>
                    </div>
                    
                    <button id="activateQuantumSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        ACTIVATE QUANTUM SPIN
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Quantum Spins:</span>
                            <span id="quantumSpins" class="text-cyan-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Burst:</span>
                            <span id="quantumLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Quantum Level:</span>
                            <span id="quantumLevel" class="text-purple-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Energy Charge:</span>
                            <span id="energyCharge" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Quantum Multi:</span>
                            <span id="quantumMultiplier" class="text-orange-400">1x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Entanglement:</span>
                            <span id="quantumEntanglement" class="text-pink-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-cyan-900/20 rounded-lg border border-cyan-500/20">
                        <h5 class="text-sm font-bold mb-2 text-cyan-300 font-mono">QUANTUM PARTICLES</h5>
                        <div class="text-xs space-y-1">
                            <div>🌌 <span class="text-cyan-400">Quantum Wild:</span> 888x bet</div>
                            <div>⭐ <span class="text-yellow-400">Neutron Star:</span> 666x bet</div>
                            <div>💎 <span class="text-blue-400">Plasma Crystal:</span> 555x bet</div>
                            <div>⚡ <span class="text-white">Cosmic Energy:</span> 444x bet</div>
                            <div>🌑 <span class="text-gray-400">Dark Matter:</span> 333x bet</div>
                            <div>🌟 <span class="text-cyan-400">Wild:</span> Expands & re-spins</div>
                            <div>🔮 <span class="text-purple-400">Scatter:</span> Quantum bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div class="relative mb-6">
                        <div id="quantumReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(15).fill(0).map((_, i) => 
                                `<div class="slot bg-cyan-900/20 rounded-lg flex items-center justify-center text-3xl border border-cyan-500/20 transition-all duration-300">🌌</div>`
                            ).join('')}
                        </div>
                        <div id="quantumFieldEffect" class="absolute inset-0 bg-gradient-to-t from-cyan-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="starburstEffect" class="absolute inset-0 bg-gradient-radial from-white/30 via-cyan-500/20 to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="cosmicAlignmentEffect" class="absolute inset-0 bg-gradient-to-b from-purple-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="dimensionalRiftEffect" class="absolute inset-0 bg-gradient-conic from-pink-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="particleAcceleratorEffect" class="absolute inset-0 bg-gradient-to-r from-yellow-500/10 via-white/10 to-yellow-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                    </div>
                    
                    <div id="quantumGameStatus" class="text-center text-lg font-semibold text-cyan-300 mb-4 h-8 font-mono">
                        Quantum field stabilized, ready for starburst activation...
                    </div>
                    
                    <div class="grid grid-cols-5 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">ENERGY METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="energyMeter" class="bg-gradient-to-r from-cyan-500 to-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="energyLevel" class="text-cyan-400">0%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">QUANTUM FIELD</h6>
                            <div class="text-center">
                                <span id="quantumFieldStatus" class="text-cyan-400">STABLE</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">STARBURST</h6>
                            <div class="text-center">
                                <span id="starburstStatus" class="text-white-400">DORMANT</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">COSMIC ALIGN</h6>
                            <div class="text-center">
                                <span id="cosmicStatus" class="text-purple-400">WAITING</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">ACCELERATOR</h6>
                            <div class="text-center">
                                <span id="acceleratorStatus" class="text-yellow-400">OFFLINE</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupQuantumStarburstGame();
}

function setupQuantumStarburstGame() {
    document.getElementById('activateQuantumSpin').addEventListener('click', activateQuantumSpin);
    const reelsContainer = document.getElementById('quantumReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 15; i++) { // 5x3 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-cyan-900/20 rounded-lg p-2 text-center text-3xl border border-cyan-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = QUANTUM_REEL_SYMBOLS[i % QUANTUM_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateQuantumDisplay();
}

function activateQuantumSpin() {
    if (quantumStarburstGame.isSpinning) return;

    const bet = parseInt(document.getElementById('quantumBet').value);
    const mode = document.getElementById('quantumMode').value;
    let totalBet;
    
    switch(mode) {
        case 'enhanced': totalBet = bet * 12; break;
        case 'quantum': totalBet = bet * 24; break;
        default: totalBet = bet * 6;
    }

    if (quantumStarburstGame.quantumSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('quantumGameStatus').textContent = 'INSUFFICIENT QUANTUM ENERGY FOR ACTIVATION';
            return;
        }
        balance -= totalBet;
    } else {
        quantumStarburstGame.quantumSpins--;
    }

    quantumStarburstGame.isSpinning = true;
    quantumStarburstGame.lastWin = 0;
    updateBalance();
    updateQuantumDisplay();
    document.getElementById('quantumGameStatus').textContent = 'Quantum particles colliding in dimensional space...';

    const slots = document.querySelectorAll('#quantumReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'quantum-highlight', 'expanded-wild'));

    // Quantum field effect during spin
    document.getElementById('quantumFieldEffect').style.opacity = '0.6';

    let spinDuration = 2200;
    let spinInterval = 100;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = QUANTUM_REEL_SYMBOLS[Math.floor(Math.random() * QUANTUM_REEL_SYMBOLS.length)];
            slot.style.boxShadow = `0 0 ${Math.random() * 30}px cyan`;
            slot.style.transform = `scale(${0.9 + Math.random() * 0.2}) rotateZ(${Math.random() * 10 - 5}deg)`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('quantumFieldEffect').style.opacity = '0';
            slots.forEach(slot => {
                slot.style.boxShadow = '';
                slot.style.transform = 'scale(1) rotateZ(0deg)';
            });
            finishQuantumSpin(totalBet);
        }
    }, spinInterval);
}

function finishQuantumSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#quantumReels .slot');
    
    // Enhanced symbol generation with quantum mode bonuses
    const mode = document.getElementById('quantumMode').value;
    let wildChance = 0.15;
    let scatterChance = 0.10;
    let quantumWildChance = 0.18;
    
    if (mode === 'enhanced') {
        wildChance = 0.20;
        scatterChance = 0.15;
        quantumWildChance = 0.25;
    } else if (mode === 'quantum') {
        wildChance = 0.30;
        scatterChance = 0.22;
        quantumWildChance = 0.35;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = QUANTUM_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = QUANTUM_SYMBOLS.SCATTER;
        } else if (Math.random() < quantumWildChance) {
            symbol = QUANTUM_SYMBOLS.QUANTUM_WILD;
        } else {
            symbol = QUANTUM_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkQuantumWins(finalSymbols, totalBet);
}

function checkQuantumWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (quantum bursts)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== QUANTUM_SYMBOLS.WILD && symbol !== QUANTUM_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === QUANTUM_SYMBOLS.QUANTUM_WILD) {
                multiplier = 888;
                statusMessage = 'QUANTUM WILD SINGULARITY ACHIEVED!';
                quantumStarburstGame.energyCharge += 300;
                quantumStarburstGame.quantumField = true;
            } else if (symbol === QUANTUM_SYMBOLS.NEUTRON_STAR) {
                multiplier = 666;
                statusMessage = 'NEUTRON STAR COLLAPSE!';
                quantumStarburstGame.energyCharge += 250;
                quantumStarburstGame.starburstMode = true;
            } else if (symbol === QUANTUM_SYMBOLS.PLASMA_CRYSTAL) {
                multiplier = 555;
                statusMessage = 'PLASMA CRYSTAL RESONANCE!';
                quantumStarburstGame.energyCharge += 200;
            } else if (symbol === QUANTUM_SYMBOLS.COSMIC_ENERGY) {
                multiplier = 444;
                statusMessage = 'COSMIC ENERGY OVERLOAD!';
                quantumStarburstGame.energyCharge += 180;
            } else if (symbol === QUANTUM_SYMBOLS.DARK_MATTER) {
                multiplier = 333;
                statusMessage = 'DARK MATTER CONVERGENCE!';
                quantumStarburstGame.energyCharge += 150;
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * quantumStarburstGame.quantumMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind and 3-of-a-kind with similar logic...
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== QUANTUM_SYMBOLS.WILD && symbol !== QUANTUM_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === QUANTUM_SYMBOLS.QUANTUM_WILD) multiplier = 300;
            else if (symbol === QUANTUM_SYMBOLS.NEUTRON_STAR) multiplier = 220;
            else if (symbol === QUANTUM_SYMBOLS.PLASMA_CRYSTAL) multiplier = 180;
            else if (symbol === QUANTUM_SYMBOLS.COSMIC_ENERGY) multiplier = 150;
            else if (symbol === QUANTUM_SYMBOLS.DARK_MATTER) multiplier = 120;
            else if (symbol === QUANTUM_SYMBOLS.PHOTON_BEAM) multiplier = 100;
            else if (symbol === QUANTUM_SYMBOLS.ANTIMATTER) multiplier = 80;
            else if (symbol === QUANTUM_SYMBOLS.QUASAR) multiplier = 70;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * quantumStarburstGame.quantumMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== QUANTUM_SYMBOLS.WILD && symbol !== QUANTUM_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === QUANTUM_SYMBOLS.QUANTUM_WILD) multiplier = 120;
            else if (symbol === QUANTUM_SYMBOLS.NEUTRON_STAR) multiplier = 90;
            else if (symbol === QUANTUM_SYMBOLS.PLASMA_CRYSTAL) multiplier = 75;
            else if (symbol === QUANTUM_SYMBOLS.COSMIC_ENERGY) multiplier = 60;
            else if (symbol === QUANTUM_SYMBOLS.DARK_MATTER) multiplier = 50;
            else if (symbol === QUANTUM_SYMBOLS.PHOTON_BEAM) multiplier = 40;
            else if (symbol === QUANTUM_SYMBOLS.ANTIMATTER) multiplier = 35;
            else if (symbol === QUANTUM_SYMBOLS.QUASAR) multiplier = 30;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * quantumStarburstGame.quantumMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild expansion and re-spin feature
    const wildPositions = [];
    symbols.forEach((symbol, index) => {
        if (symbol === QUANTUM_SYMBOLS.WILD || symbol === QUANTUM_SYMBOLS.QUANTUM_WILD) {
            wildPositions.push(index);
        }
    });

    if (wildPositions.length > 0) {
        quantumStarburstGame.expandedWilds = [...wildPositions];
        quantumStarburstGame.respinCount++;
        
        // Expand wilds vertically
        wildPositions.forEach(pos => {
            const reel = Math.floor(pos / 3);
            for (let row = 0; row < 3; row++) {
                const expandedPos = reel * 3 + row;
                const slot = document.querySelectorAll('#quantumReels .slot')[expandedPos];
                slot.classList.add('expanded-wild');
                slot.textContent = QUANTUM_SYMBOLS.WILD;
            }
        });

        if (quantumStarburstGame.respinCount <= 3) {
            statusMessage += ' QUANTUM WILD EXPANSION! Re-spin activated!';
            setTimeout(() => {
                activateQuantumSpin();
            }, 2000);
        }
    } else {
        quantumStarburstGame.respinCount = 0;
        quantumStarburstGame.expandedWilds = [];
    }

    // Scatter quantum bonus
    const scatterCount = symbols.filter(s => s === QUANTUM_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const quantumSpinsAwarded = 25 + (scatterCount - 3) * 12;
        quantumStarburstGame.quantumSpins += quantumSpinsAwarded;
        quantumStarburstGame.quantumLevel = Math.min(quantumStarburstGame.quantumLevel + 1, 10);
        statusMessage += ` 🔮 QUANTUM FIELD ACTIVATED! ${quantumSpinsAwarded} quantum spins!`;
        
        // Increase quantum multiplier
        quantumStarburstGame.quantumMultiplier += 1.2;
    }

    // Quantum field activation
    if (quantumStarburstGame.quantumField && quantumStarburstGame.energyCharge >= 500) {
        const quantumMultiplier = 18 + quantumStarburstGame.quantumLevel;
        totalWin *= quantumMultiplier;
        statusMessage = `🌌 QUANTUM FIELD SINGULARITY! ${quantumMultiplier}x DIMENSIONAL POWER!`;
        quantumStarburstGame.energyCharge = 0;
        
        // Trigger quantum field effect
        document.getElementById('quantumFieldEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('quantumFieldEffect').style.opacity = '0';
            quantumStarburstGame.quantumField = false;
        }, 6000);
    }

    // Starburst mode activation
    if (quantumStarburstGame.starburstMode && quantumStarburstGame.energyCharge >= 400) {
        const starburstMultiplier = 15 + quantumStarburstGame.quantumLevel;
        totalWin *= starburstMultiplier;
        statusMessage = `⭐ STARBURST QUANTUM EXPLOSION! ${starburstMultiplier}x STELLAR ENERGY!`;
        quantumStarburstGame.energyCharge += 100; // Starburst feeds energy back
        
        // Trigger starburst effect
        document.getElementById('starburstEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('starburstEffect').style.opacity = '0';
            quantumStarburstGame.starburstMode = false;
        }, 5000);
    }

    // Cosmic alignment (random mega bonus)
    if (quantumStarburstGame.energyCharge >= 300 && Math.random() < 0.4) {
        quantumStarburstGame.cosmicAlignment = true;
        const cosmicMultiplier = 12 + quantumStarburstGame.quantumLevel;
        totalWin *= cosmicMultiplier;
        statusMessage = `💫 COSMIC ALIGNMENT ACHIEVED! ${cosmicMultiplier}x UNIVERSAL HARMONY!`;
        
        // Trigger cosmic alignment effect
        document.getElementById('cosmicAlignmentEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('cosmicAlignmentEffect').style.opacity = '0';
            quantumStarburstGame.cosmicAlignment = false;
        }, 4000);
    }

    // Dimensional rift (ultra rare bonus)
    if (quantumStarburstGame.quantumSpins > 0 && Math.random() < 0.25) {
        quantumStarburstGame.dimensionalRift = true;
        totalWin += totalBet * 200;
        statusMessage += ' 🌀 DIMENSIONAL RIFT OPENED!';
        quantumStarburstGame.quantumEntanglement += 3;
        
        // Trigger dimensional rift effect
        document.getElementById('dimensionalRiftEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('dimensionalRiftEffect').style.opacity = '0';
            quantumStarburstGame.dimensionalRift = false;
        }, 7000);
    }

    // Particle accelerator discovery
    if (quantumStarburstGame.quantumEntanglement >= 15) {
        quantumStarburstGame.particleAccelerator = true;
        totalWin += totalBet * 500;
        statusMessage += ' ⚛️ PARTICLE ACCELERATOR ONLINE! INFINITE ENERGY!';
        quantumStarburstGame.quantumEntanglement = 0; // Reset but keep accelerator
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        quantumStarburstGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#quantumReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('quantum-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Quantum field remains stable, awaiting particle collision...';
    }

    document.getElementById('quantumGameStatus').textContent = statusMessage;
    quantumStarburstGame.isSpinning = false;
    updateQuantumDisplay();
}

function updateQuantumDisplay() {
    const spinButton = document.getElementById('activateQuantumSpin');
    spinButton.disabled = quantumStarburstGame.isSpinning;
    spinButton.textContent = quantumStarburstGame.isSpinning ? 'SPINNING...' : 'ACTIVATE QUANTUM SPIN';

    document.getElementById('quantumSpins').textContent = quantumStarburstGame.quantumSpins;
    document.getElementById('quantumLastWin').textContent = `${quantumStarburstGame.lastWin} GA`;
    document.getElementById('quantumLevel').textContent = quantumStarburstGame.quantumLevel;
    document.getElementById('energyCharge').textContent = quantumStarburstGame.energyCharge;
    document.getElementById('quantumMultiplier').textContent = `${quantumStarburstGame.quantumMultiplier.toFixed(1)}x`;
    document.getElementById('quantumEntanglement').textContent = quantumStarburstGame.quantumEntanglement;
    
    // Update energy meter
    const energyPercentage = Math.min(100, (quantumStarburstGame.energyCharge / 500) * 100);
    document.getElementById('energyMeter').style.width = `${energyPercentage}%`;
    document.getElementById('energyLevel').textContent = `${Math.round(energyPercentage)}%`;
    
    document.getElementById('quantumFieldStatus').textContent = quantumStarburstGame.quantumField ? 'ACTIVE!' : 'STABLE';
    document.getElementById('starburstStatus').textContent = quantumStarburstGame.starburstMode ? 'BURSTING!' : 'DORMANT';
    document.getElementById('cosmicStatus').textContent = quantumStarburstGame.cosmicAlignment ? 'ALIGNED!' : 'WAITING';
    document.getElementById('acceleratorStatus').textContent = quantumStarburstGame.particleAccelerator ? 'ONLINE!' : 'OFFLINE';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadQuantumStarburstGame();
});

