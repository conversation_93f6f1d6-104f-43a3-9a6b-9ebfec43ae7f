// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}
window.updateBalance = updateBalance;

// Make the game loader function globally accessible.
window.loadAtlantisARGame = loadAtlantisARGame;


// --- ATLANTIS AR GAME ---

// Custom SVG Icons for Atlantis AR
const atlantisIcons = {
    trident: `<svg data-symbol="trident" class="w-10 h-10 text-yellow-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2v10m0 0a2 2 0 100 4 2 2 0 000-4zM8 12a2 2 0 100 4 2 2 0 000-4zm8 0a2 2 0 100 4 2 2 0 000-4zM5 9h14M5 15h14"/></svg>`,
    helmet: `<svg data-symbol="helmet" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2a10 10 0 00-10 10v2c0 2.2 1.8 4 4 4h12c2.2 0 4-1.8 4-4v-2a10 10 0 00-10-10z"/><path d="M12 12a4 4 0 00-4 4h8a4 4 0 00-4-4zM8 12V8m8 4V8"/></svg>`,
    amulet: `<svg data-symbol="amulet" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2l4 4-4 4-4-4 4-4zM12 12l4 4-4 4-4-4 4-4z"/><path d="M2 12l4-4-4-4"/><path d="M22 12l-4 4 4 4"/></svg>`,
    column: `<svg data-symbol="column" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M5 22h14M6 6h12M6 10h12M6 14h12M6 18h12M9 2v4m6-4v4"/></svg>`,
    jellyfish: `<svg data-symbol="jellyfish" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0110 10v0a10 10 0 0110-10 10 10 0 01-10-10v0A10 10 0 012 12z"/><path d="M12 12v6m-4-2v2m8-2v2"/></svg>`,
    turtle: `<svg data-symbol="turtle" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path d="M21 12c-1.9 2.8-4.5 4.8-7.5 5.5S6.4 16.2 4.5 14"/><path d="M3 12c1.9-2.8 4.5-4.8 7.5-5.5S17.6 7.8 19.5 10"/><path d="M6.5 7.5L3 10m14.5 6.5L21 14"/></svg>`,
    wild: `<svg data-symbol="wild" class="w-10 h-10 text-cyan-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2a10 10 0 00-10 10c0 3.8 2.1 7.1 5.1 8.9L12 22l4.9-1.1c3-1.8 5.1-5.1 5.1-8.9A10 10 0 0012 2z"/><path d="M12 12a3 3 0 100-6 3 3 0 000 6z"/></svg>`,
    scatter: `<svg data-symbol="scatter" class="w-10 h-10 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 6a6 6 0 016 6"/><path d="M12 18a6 6 0 00-6-6"/><path d="M6 12a6 6 0 016-6"/><path d="M18 12a6 6 0 00-6 6"/></svg>`,
    sonar: `<svg data-symbol="sonar" class="w-10 h-10 text-pink-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M4.93 4.93a10 10 0 0114.14 0"/><path d="M7.76 7.76a6 6 0 018.48 0"/><path d="M12 12a2 2 0 012-2"/><path d="M12 2v2"/><path d="M22 12h-2"/><path d="M12 22v-2"/><path d="M2 12H4"/></svg>`
};

// Game state for Atlantis AR
let atlantisARGame = {
    isSpinning: false,
    hydroSpins: 0,
    reactorCharge: 0,
    reels: [],
    symbols: ['trident', 'helmet', 'amulet', 'column', 'jellyfish', 'turtle'],
    wildSymbol: 'wild',
    scatterSymbol: 'scatter',
    sonarSymbol: 'sonar'
};


function loadAtlantisARGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">ATLANTIS AR</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">ENERGY WAGER</label>
                        <input type="number" id="atlantisBet" value="25" min="1" max="1000"
                               class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">DIVE PROTOCOL</label>
                        <select id="atlantisProtocol" class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="reef_scan">Reef Scan (25 lines)</option>
                            <option value="abyss_dive">Abyss Dive (50 lines)</option>
                            <option value="trench_explore">Trench Explore (100 lines)</option>
                        </select>
                    </div>

                    <button id="startAtlantisDive" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        INITIATE DIVE
                    </button>

                    <div class="space-y-2 text-sm">
                         <div class="flex justify-between">
                            <span class="text-blue-300">Last Win:</span>
                            <span id="atlantisLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-300">Hydro-Spins:</span>
                            <span id="atlantisHydroSpins" class="text-purple-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/20">
                        <h5 class="text-sm font-bold mb-2 text-blue-300">REACTOR CORE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="atlantisReactor" class="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Charge the core for a multiplier surge.</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <div id="atlantisReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="atlantisStatus" class="text-center text-lg font-semibold text-blue-400 mb-4">
                        Access the deep sea network to uncover Atlantean riches.
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-blue-300 mb-2">ARTEFACT VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex items-center justify-between"><span>${atlantisIcons.trident} x5:</span><span class="text-yellow-400">4000x</span></div>
                                <div class="flex items-center justify-between"><span>${atlantisIcons.helmet} x5:</span><span class="text-gray-300">2000x</span></div>
                                <div class="flex items-center justify-between"><span>${atlantisIcons.amulet} x5:</span><span class="text-purple-400">1000x</span></div>
                                <div class="flex items-center justify-between"><span>${atlantisIcons.column} x5:</span><span class="text-white">500x</span></div>
                            </div>
                        </div>
                        <div class="bg-blue-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-blue-300 mb-2">AR FEATURES</h6>
                            <div class="space-y-2 text-xs">
                                <div class="flex items-center">${atlantisIcons.wild} <span class="ml-2"><span class="text-cyan-400">Holo-Dolphin:</span> Wild signal substitutes.</span></div>
                                <div class="flex items-center">${atlantisIcons.scatter} <span class="ml-2"><span class="text-purple-400">Vortex:</span> Triggers Hydro-Spins.</span></div>
                                <div class="flex items-center">${atlantisIcons.sonar} <span class="ml-2"><span class="text-pink-500">Sonar:</span> Scans for instant rewards.</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupAtlantisARGame();
}

function setupAtlantisARGame() {
    const reelsContainer = document.getElementById('atlantisReels');
    reelsContainer.innerHTML = ''; // Clear existing reels
    
    // Initialize the visual display of the reels with random symbols.
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbolDiv = document.createElement('div');
        symbolDiv.className = 'bg-black/50 border border-blue-500/30 rounded-lg h-20 flex items-center justify-center text-3xl transition-all duration-300';
        const randomSymbolName = atlantisARGame.symbols[Math.floor(Math.random() * atlantisARGame.symbols.length)];
        symbolDiv.innerHTML = atlantisIcons[randomSymbolName];
        reelsContainer.appendChild(symbolDiv);
    }
    atlantisARGame.reels = Array.from(reelsContainer.children);

    document.getElementById('startAtlantisDive').addEventListener('click', beginAtlantisDive);
    updateAtlantisDisplay();
}

function updateAtlantisDisplay() {
    document.getElementById('atlantisHydroSpins').textContent = atlantisARGame.hydroSpins;
    document.getElementById('atlantisReactor').style.width = atlantisARGame.reactorCharge + '%';
}

function beginAtlantisDive() {
    if (atlantisARGame.isSpinning) return;

    const bet = parseInt(document.getElementById('atlantisBet').value);
    const protocol = document.getElementById('atlantisProtocol').value;
    let totalBet;

    switch(protocol) {
        case 'abyss_dive': totalBet = bet * 50; break;
        case 'trench_explore': totalBet = bet * 100; break;
        default: totalBet = bet * 25;
    }

    if (atlantisARGame.hydroSpins === 0 && balance < totalBet) {
        document.getElementById('atlantisStatus').textContent = 'Insufficient energy for dive protocol.';
        return;
    }

    if (atlantisARGame.hydroSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        atlantisARGame.hydroSpins--;
    }

    atlantisARGame.isSpinning = true;
    document.getElementById('startAtlantisDive').disabled = true;
    document.getElementById('atlantisStatus').textContent = 'Descending into the abyss...';

    // Animate the reels spinning
    let animationSteps = 24;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            atlantisARGame.reels.forEach(reel => {
                const randomSymbolName = atlantisARGame.symbols[Math.floor(Math.random() * atlantisARGame.symbols.length)];
                reel.innerHTML = atlantisIcons[randomSymbolName];
                reel.style.transform = 'scale(0.9)';
                reel.style.filter = 'blur(2px)';
            });

            currentStep++;
            setTimeout(animateReels, 95);
        } else {
            // Determine the final symbols that land on the reels.
            const finalSymbolNames = [];
            for (let i = 0; i < 20; i++) {
                let symbolName;
                const rand = Math.random();
                if (rand < 0.12) {
                    symbolName = atlantisARGame.wildSymbol;
                } else if (rand < 0.20) {
                    symbolName = atlantisARGame.scatterSymbol;
                } else if (rand < 0.28) {
                    symbolName = atlantisARGame.sonarSymbol;
                }
                else {
                    symbolName = atlantisARGame.symbols[Math.floor(Math.random() * atlantisARGame.symbols.length)];
                }
                finalSymbolNames.push(symbolName);
            }

            // Display the final symbols.
            atlantisARGame.reels.forEach((reel, index) => {
                reel.innerHTML = atlantisIcons[finalSymbolNames[index]];
                reel.style.transform = 'scale(1)';
                reel.style.filter = 'blur(0px)';
            });

            checkAtlantisWins(finalSymbolNames, totalBet);
        }
    };

    animateReels();
}

function checkAtlantisWins(symbolNames, totalBet) {
    let totalWin = 0;
    let statusMessage = '';
    
    // Check for scatter symbols to award Hydro-Spins.
    const scatterCount = symbolNames.filter(s => s === atlantisARGame.scatterSymbol).length;
    if (scatterCount >= 3) {
        const spinsWon = scatterCount * 5;
        atlantisARGame.hydroSpins += spinsWon;
        totalWin += Math.floor(totalBet * scatterCount * 5);
        statusMessage = `<span class="text-purple-400">VORTEX OPENED! ${spinsWon} Hydro-Spins awarded!</span>`;
    }

    // Check for Sonar symbols for instant wins
    const sonarCount = symbolNames.filter(s => s === atlantisARGame.sonarSymbol).length;
    if (sonarCount > 0) {
        const sonarWin = Math.floor(totalBet * sonarCount * 2.5);
        totalWin += sonarWin;
        statusMessage = `<span class="text-pink-500">SONAR PULSE! Found instant treasure of ${sonarWin} GA!</span>`;
    }

    // Calculate wins from matching paylines.
    let lineWin = calculateAtlantisWins(symbolNames, totalBet);

    // Update Reactor Core charge
    const tridentCount = symbolNames.filter(s => s === 'trident').length;
    if (tridentCount > 0) {
        atlantisARGame.reactorCharge = Math.min(100, atlantisARGame.reactorCharge + tridentCount * 25);
    }
    
    // Check for Reactor Core overload
    if (atlantisARGame.reactorCharge >= 100) {
        lineWin *= 10;
        atlantisARGame.reactorCharge = 0;
        statusMessage = `<span class="text-cyan-400">REACTOR CORE OVERLOAD! 10x multiplier surge!</span>`;
    }
    
    totalWin += lineWin;

    // Update balance and UI based on win amount.
    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        document.getElementById('atlantisLastWin').textContent = totalWin + ' GA';
        
        if (statusMessage) {
            document.getElementById('atlantisStatus').innerHTML = statusMessage;
        } else {
            document.getElementById('atlantisStatus').innerHTML =
                `<span class="text-green-400">Data recovered! You retrieved ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('atlantisStatus').textContent = 'No signal... scan the depths again.';
    }

    updateAtlantisDisplay();

    // Re-enable the spin button after a short delay.
    setTimeout(() => {
        atlantisARGame.isSpinning = false;
        document.getElementById('startAtlantisDive').disabled = false;
        if(statusMessage.includes('REACTOR')) { // Reset status after major win
             document.getElementById('atlantisStatus').textContent = 'Access the deep sea network to uncover Atlantean riches.';
        }
    }, 2500);
}

function calculateAtlantisWins(symbolNames, totalBet) {
    let totalWin = 0;
    // Check each of the 4 rows for winning combinations.
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbolNames[row * 5 + col]);
        }

        let matchCount = 1;
        let symbolToMatch = lineSymbols[0];
        
        // If the line starts with a wild, find the first non-wild to determine the payline symbol.
        if (symbolToMatch === atlantisARGame.wildSymbol) {
            let nextSymbolIndex = 1;
            while(nextSymbolIndex < 5 && lineSymbols[nextSymbolIndex] === atlantisARGame.wildSymbol) {
                matchCount++;
                nextSymbolIndex++;
            }
            if(nextSymbolIndex < 5) {
                 symbolToMatch = lineSymbols[nextSymbolIndex];
            }
        }

        // Count matches from left to right.
        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbolToMatch || lineSymbols[i] === atlantisARGame.wildSymbol) {
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getAtlantisMultiplier(symbolToMatch, matchCount);
            totalWin += Math.floor((totalBet / 25) * multiplier);
        }
    }

    return totalWin;
}

function getAtlantisMultiplier(symbolName, count) {
    // Payout multipliers mapped by symbol name.
    const multipliers = {
        'trident':    [0, 0, 200, 1000, 4000],
        'helmet':     [0, 0, 100, 500, 2000],
        'amulet':     [0, 0, 50, 250, 1000],
        'column':     [0, 0, 25, 125, 500],
        'jellyfish':  [0, 0, 20, 100, 400],
        'turtle':     [0, 0, 15, 75, 300],
        'wild':       [0, 0, 200, 1000, 4000], // Wild pays same as top symbol
    };

    return multipliers[symbolName] ? multipliers[symbolName][count-1] : 0;
}

// Initialize the game when the page loads.
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    // Load the Atlantis AR game by default.
    loadAtlantisARGame();
});