// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

const GALACTIC_SYMBOLS = ['🐟', '🐠', '🦈', '🐙', '🦑', '🐋', '⭐', '🌟', '💎', 'B'];

let galacticBassGame = {
    isSpinning: false,
    zone: 'asteroid',
    betAmount: 15,
    catchValue: 0,
    multiplier: 1,
    freeSpins: 0,
    symbols: GALACTIC_SYMBOLS,
    reels: [[], [], [], [], []],
    caughtFish: []
};

function loadGalacticBassGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">GALACTIC BASS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">FISHING LICENSE</label>
                        <input type="number" id="bassBet" value="20" min="5" max="1000"
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">FISHING ZONE</label>
                        <select id="fishingZone" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="asteroid">Asteroid Belt</option>
                            <option value="nebula">Cosmic Nebula</option>
                            <option value="blackhole">Black Hole Edge</option>
                        </select>
                    </div>
                    
                    <button id="castLine" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        CAST COSMIC LINE
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Casts:</span>
                            <span id="bassFreeSpins" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Catch:</span>
                            <span id="bassLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Zone Multiplier:</span>
                            <span id="zoneMultiplier" class="text-yellow-400">1x</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300">COSMIC FISH GUIDE</h5>
                        <div class="text-xs space-y-1">
                            <div>🐋 <span class="text-blue-400">Space Whale:</span> 100x bet</div>
                            <div>🦈 <span class="text-gray-400">Void Shark:</span> 50x bet</div>
                            <div>🐙 <span class="text-purple-400">Nebula Octopus:</span> 30x bet</div>
                            <div>🐠 <span class="text-cyan-400">Star Fish:</span> 15x bet</div>
                            <div>💎 <span class="text-white-400">Cosmic Gem:</span> 25x bet</div>
                            <div>B <span class="text-yellow-400">Bass:</span> Scatter bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="relative mb-6">
                        <div id="bassReels" class="grid grid-cols-5 gap-4 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-purple-900/20 rounded-lg flex items-center justify-center text-4xl border border-purple-500/20 transition-all duration-300">🐟</div>`
                            ).join('')}
                        </div>
                        <div id="fishingLine" class="absolute top-0 left-1/2 w-1 bg-yellow-400 opacity-0 transition-all duration-2000" style="height: 0%"></div>
                        <div id="spaceEffects" class="absolute inset-0 pointer-events-none"></div>
                    </div>
                    <div id="bassStatus" class="text-center text-lg font-semibold text-purple-300 h-8">
                        Cast your line into the cosmic depths
                    </div>
                </div>
            </div>
        </div>
    `;
    
    initializeGalacticBass();
}

function initializeGalacticBass() {
    document.getElementById('castLine').addEventListener('click', castGalacticLine);
    document.getElementById('fishingZone').addEventListener('change', updateFishingZone);
    updateBassDisplay();
}

function updateFishingZone() {
    const zone = document.getElementById('fishingZone').value;
    galacticBassGame.zone = zone;
    
    // Update zone multiplier
    const multipliers = { asteroid: 1, nebula: 1.5, blackhole: 2 };
    galacticBassGame.multiplier = multipliers[zone];
    updateBassDisplay();
}

function castGalacticLine() {
    if (galacticBassGame.isSpinning) return;
    
    const betAmount = parseInt(document.getElementById('bassBet').value);
    
    if (betAmount > balance) {
        alert('Insufficient balance!');
        return;
    }
    
    if (galacticBassGame.freeSpins === 0) {
        balance -= betAmount;
        updateBalance();
    } else {
        galacticBassGame.freeSpins--;
    }
    
    galacticBassGame.isSpinning = true;
    galacticBassGame.betAmount = betAmount;
    galacticBassGame.catchValue = 0;
    
    document.getElementById('castLine').disabled = true;
    document.getElementById('bassStatus').textContent = 'Casting line into galactic waters...';
    
    animateFishingLine().then(() => {
        spinBassReels().then(() => {
            checkBassCatch();
        });
    });
}

function animateFishingLine() {
    return new Promise(resolve => {
        const line = document.getElementById('fishingLine');
        line.style.opacity = '1';
        line.style.height = '100%';
        
        setTimeout(() => {
            line.style.opacity = '0';
            line.style.height = '0%';
            resolve();
        }, 2000);
    });
}

function spinBassReels() {
    return new Promise(resolve => {
        const slots = document.querySelectorAll('#bassReels .slot');
        let spinDuration = 1500;
        let spinInterval = 60;
        let elapsed = 0;

        const spinAnimation = setInterval(() => {
            elapsed += spinInterval;
            slots.forEach(slot => {
                slot.textContent = GALACTIC_SYMBOLS[Math.floor(Math.random() * GALACTIC_SYMBOLS.length)];
            });

            if (elapsed >= spinDuration) {
                clearInterval(spinAnimation);
                resolve();
            }
        }, spinInterval);
    });
}

function checkBassCatch() {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#bassReels .slot');
    slots.forEach(slot => {
        const symbol = GALACTIC_SYMBOLS[Math.floor(Math.random() * GALACTIC_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    finalSymbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for cosmic catches
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === '🐋' && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 60 : 30;
            statusMessage = 'LEGENDARY SPACE WHALE CAUGHT!';
        } else if (symbol === '🦈' && count >= 3) {
            multiplier = count >= 5 ? 50 : count >= 4 ? 30 : 15;
            statusMessage = 'Void shark reeled in from the depths!';
        } else if (symbol === '🐙' && count >= 3) {
            multiplier = count >= 5 ? 30 : count >= 4 ? 20 : 10;
            statusMessage = 'Nebula octopus caught in cosmic net!';
        } else if (symbol === '🐠' && count >= 3) {
            multiplier = count >= 5 ? 15 : count >= 4 ? 10 : 5;
            statusMessage = 'School of star fish netted!';
        } else if (symbol === '💎' && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 15 : 8;
            statusMessage = 'Cosmic gems discovered in the catch!';
        }

        if (multiplier > 0) {
            totalWin += galacticBassGame.betAmount * multiplier * galacticBassGame.multiplier;
            finalSymbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Scatter bonus (B Bass symbols)
    const scatterCount = finalSymbols.filter(s => s === 'B').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 10 + (scatterCount - 3) * 5;
        galacticBassGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` Galactic Bass school found! +${freeSpinsAwarded} free casts!`;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        galacticBassGame.catchValue = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#bassReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'No fish biting in this cosmic sector.';
    }

    document.getElementById('bassStatus').textContent = statusMessage;
    galacticBassGame.isSpinning = false;
    document.getElementById('castLine').disabled = false;
    updateBassDisplay();
}

function updateBassDisplay() {
    document.getElementById('bassFreeSpins').textContent = galacticBassGame.freeSpins;
    document.getElementById('bassLastWin').textContent = `${galacticBassGame.catchValue} GA`;
    document.getElementById('zoneMultiplier').textContent = `${galacticBassGame.multiplier}x`;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadGalacticBassGame();
});
