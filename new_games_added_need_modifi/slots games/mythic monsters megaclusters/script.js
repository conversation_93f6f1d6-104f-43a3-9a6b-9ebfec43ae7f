
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Mythic Monsters Megaclusters Game Implementation
const MYTHIC_SYMBOLS = {
    DRAGON: '🐉',
    PHOENIX: '🔥',
    KRAKEN: '🐙',
    GRIFFIN: '🦅',
    UNICORN: '🦄',
    HYDRA: '🐍',
    BASILISK: '🦎',
    LEVIATHAN: '🐋',
    WILD: '⚡',
    SCATTER: '🌟'
};

const MYTHIC_REEL_SYMBOLS = [
    MYTHIC_SYMBOLS.DRAGON, MYTHIC_SYMBOLS.PHOENIX, MYTHIC_SYMBOLS.KRAKEN,
    MYTHIC_SYMBOLS.GRIFFIN, MYTHIC_SYMBOLS.UNICORN, MYTHIC_SYMBOLS.HYDRA,
    MYTH<PERSON>_SYMBOLS.BASILISK, MYTH<PERSON>_SYMBOLS.LEVIATHAN, MYTHIC_SYMBOLS.WILD, MYTHIC_SYMBOLS.SCATTER
];

let mythicMonstersGame = {
    isSpinning: false,
    megaSpins: 0,
    lastWin: 0,
    monsterPower: 0,
    evolutionStage: 1,
    clusterCount: 0,
    megaMultiplier: 1,
    dragonRage: false,
    phoenixRising: false,
    krakenAwakened: false,
    gridSize: 6 // 6x6 megaclusters grid
};

function loadMythicMonstersGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400 font-mono">MYTHIC MONSTERS MEGACLUSTERS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">SUMMONING POWER</label>
                        <input type="number" id="mythicBet" value="50" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">MONSTER REALM</label>
                        <select id="monsterRealm" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="mortal">Mortal Realm (4x)</option>
                            <option value="legendary">Legendary Realm (8x)</option>
                            <option value="mythical">Mythical Realm (16x)</option>
                        </select>
                    </div>
                    
                    <button id="summonMonsters" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        SUMMON MONSTERS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Mega Spins:</span>
                            <span id="mythicMegaSpins" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Victory:</span>
                            <span id="mythicLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Monster Power:</span>
                            <span id="monsterPower" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Evolution Stage:</span>
                            <span id="evolutionStage" class="text-orange-400">I</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Cluster Count:</span>
                            <span id="clusterCount" class="text-yellow-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300 font-mono">MONSTER POWERS</h5>
                        <div class="text-xs space-y-1">
                            <div>🐉 <span class="text-red-400">Dragon:</span> 300x cluster</div>
                            <div>🔥 <span class="text-orange-400">Phoenix:</span> 250x cluster</div>
                            <div>🐙 <span class="text-purple-400">Kraken:</span> 200x cluster</div>
                            <div>🦅 <span class="text-yellow-400">Griffin:</span> 180x cluster</div>
                            <div>🦄 <span class="text-pink-400">Unicorn:</span> 150x cluster</div>
                            <div>⚡ <span class="text-cyan-400">Wild:</span> Substitutes all</div>
                            <div>🌟 <span class="text-white">Scatter:</span> Mega bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="relative mb-6">
                        <div id="mythicReels" class="grid grid-cols-6 gap-1 h-80">
                            ${Array(36).fill(0).map((_, i) => 
                                `<div class="slot bg-purple-900/20 rounded-lg flex items-center justify-content text-3xl border border-purple-500/20 transition-all duration-300">🐉</div>`
                            ).join('')}
                        </div>
                        <div id="dragonRageEffect" class="absolute inset-0 bg-gradient-to-t from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="phoenixRisingEffect" class="absolute inset-0 bg-gradient-to-b from-orange-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="krakenAwakeEffect" class="absolute inset-0 bg-gradient-radial from-purple-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="megaClusterEffect" class="absolute inset-0 bg-gradient-conic from-violet-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                    </div>
                    
                    <div id="mythicGameStatus" class="text-center text-lg font-semibold text-purple-300 mb-4 h-8 font-mono">
                        Ancient monsters await your summoning call...
                    </div>
                    
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DRAGON STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Rage:</span>
                                    <span id="dragonRageStatus" class="text-red-400">DORMANT</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Phoenix:</span>
                                    <span id="phoenixStatus" class="text-orange-400">SLEEPING</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">POWER METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="powerMeter" class="bg-gradient-to-r from-purple-500 to-red-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">MEGA MULTIPLIER</h6>
                            <div class="text-center">
                                <span id="megaMultiplier" class="text-2xl text-purple-400">1x</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">KRAKEN</h6>
                            <div class="text-center">
                                <span id="krakenStatus" class="text-purple-400">DEEP</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupMythicMonstersGame();
}

function setupMythicMonstersGame() {
    document.getElementById('summonMonsters').addEventListener('click', summonMonsters);
    const reelsContainer = document.getElementById('mythicReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 36; i++) { // 6x6 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-purple-900/20 rounded-lg p-1 text-center text-2xl border border-purple-500/20 flex items-center justify-center h-12 transition-all duration-300';
        slot.textContent = MYTHIC_REEL_SYMBOLS[i % MYTHIC_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateMythicDisplay();
}

function summonMonsters() {
    if (mythicMonstersGame.isSpinning) return;

    const bet = parseInt(document.getElementById('mythicBet').value);
    const realm = document.getElementById('monsterRealm').value;
    let totalBet;
    
    switch(realm) {
        case 'legendary': totalBet = bet * 8; break;
        case 'mythical': totalBet = bet * 16; break;
        default: totalBet = bet * 4;
    }

    if (mythicMonstersGame.megaSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('mythicGameStatus').textContent = 'INSUFFICIENT MANA TO SUMMON MONSTERS';
            return;
        }
        balance -= totalBet;
    } else {
        mythicMonstersGame.megaSpins--;
    }

    mythicMonstersGame.isSpinning = true;
    mythicMonstersGame.lastWin = 0;
    updateBalance();
    updateMythicDisplay();
    document.getElementById('mythicGameStatus').textContent = 'Summoning ancient monsters from the void...';

    const slots = document.querySelectorAll('#mythicReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'cluster-highlight'));

    // Mega cluster effect during spin
    document.getElementById('megaClusterEffect').style.opacity = '0.3';

    let spinDuration = 2800;
    let spinInterval = 120;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = MYTHIC_REEL_SYMBOLS[Math.floor(Math.random() * MYTHIC_REEL_SYMBOLS.length)];
            slot.style.transform = `scale(${0.9 + Math.random() * 0.2})`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('megaClusterEffect').style.opacity = '0';
            slots.forEach(slot => slot.style.transform = 'scale(1)');
            finishMonsterSummoning(totalBet);
        }
    }, spinInterval);
}

function finishMonsterSummoning(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#mythicReels .slot');
    
    // Enhanced symbol generation with realm bonuses
    const realm = document.getElementById('monsterRealm').value;
    let wildChance = 0.12;
    let scatterChance = 0.08;
    let dragonChance = 0.15;
    
    if (realm === 'legendary') {
        wildChance = 0.16;
        scatterChance = 0.11;
        dragonChance = 0.20;
    } else if (realm === 'mythical') {
        wildChance = 0.22;
        scatterChance = 0.15;
        dragonChance = 0.25;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = MYTHIC_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = MYTHIC_SYMBOLS.SCATTER;
        } else if (Math.random() < dragonChance) {
            symbol = MYTHIC_SYMBOLS.DRAGON;
        } else {
            symbol = MYTHIC_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular monsters
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkMythicClusters(finalSymbols, totalBet);
}

function checkMythicClusters(symbols, totalBet) {
    let totalWin = 0;
    const winningClusters = new Set();
    let statusMessage = '';

    // Find clusters (connected groups of 5+ same symbols)
    const clusters = findClusters(symbols);
    mythicMonstersGame.clusterCount = clusters.length;

    for (const cluster of clusters) {
        const symbol = symbols[cluster[0]];
        const clusterSize = cluster.length;
        
        if (clusterSize >= 5) {
            let multiplier = 0;
            
            if (symbol === MYTHIC_SYMBOLS.DRAGON) {
                multiplier = clusterSize >= 12 ? 300 : clusterSize >= 8 ? 200 : 100;
                statusMessage = 'ANCIENT DRAGON AWAKENS WITH FURY!';
                mythicMonstersGame.dragonRage = true;
                mythicMonstersGame.monsterPower += clusterSize * 15;
            } else if (symbol === MYTHIC_SYMBOLS.PHOENIX) {
                multiplier = clusterSize >= 12 ? 250 : clusterSize >= 8 ? 170 : 85;
                statusMessage = 'PHOENIX RISES FROM ETERNAL FLAMES!';
                mythicMonstersGame.phoenixRising = true;
                mythicMonstersGame.monsterPower += clusterSize * 12;
            } else if (symbol === MYTHIC_SYMBOLS.KRAKEN) {
                multiplier = clusterSize >= 12 ? 200 : clusterSize >= 8 ? 140 : 70;
                statusMessage = 'KRAKEN EMERGES FROM THE ABYSS!';
                mythicMonstersGame.krakenAwakened = true;
                mythicMonstersGame.monsterPower += clusterSize * 10;
            } else if (symbol === MYTHIC_SYMBOLS.GRIFFIN) {
                multiplier = clusterSize >= 12 ? 180 : clusterSize >= 8 ? 120 : 60;
                statusMessage = 'GRIFFIN SOARS WITH DIVINE POWER!';
            } else if (symbol === MYTHIC_SYMBOLS.UNICORN) {
                multiplier = clusterSize >= 12 ? 150 : clusterSize >= 8 ? 100 : 50;
                statusMessage = 'UNICORN BLESSES WITH PURE MAGIC!';
            } else if (symbol === MYTHIC_SYMBOLS.HYDRA) {
                multiplier = clusterSize >= 12 ? 120 : clusterSize >= 8 ? 80 : 40;
                statusMessage = 'HYDRA REGENERATES WITH VENGEANCE!';
            } else if (symbol === MYTHIC_SYMBOLS.BASILISK) {
                multiplier = clusterSize >= 12 ? 100 : clusterSize >= 8 ? 65 : 35;
                statusMessage = 'BASILISK PETRIFIES WITH TERROR!';
            } else if (symbol === MYTHIC_SYMBOLS.LEVIATHAN) {
                multiplier = clusterSize >= 12 ? 90 : clusterSize >= 8 ? 60 : 30;
                statusMessage = 'LEVIATHAN COMMANDS THE SEAS!';
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * mythicMonstersGame.megaMultiplier;
                cluster.forEach(index => winningClusters.add(index));
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === MYTHIC_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.5);
        totalWin *= wildMultiplier;
        statusMessage += ` Lightning strikes: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter mega bonus
    const scatterCount = symbols.filter(s => s === MYTHIC_SYMBOLS.SCATTER).length;
    if (scatterCount >= 4) {
        const megaSpinsAwarded = 15 + (scatterCount - 4) * 8;
        mythicMonstersGame.megaSpins += megaSpinsAwarded;
        mythicMonstersGame.evolutionStage = Math.min(mythicMonstersGame.evolutionStage + 1, 6);
        statusMessage += ` 🌟 COSMIC ALIGNMENT! ${megaSpinsAwarded} mega spins!`;
        
        // Increase mega multiplier
        mythicMonstersGame.megaMultiplier += 0.5;
    }

    // Dragon rage activation
    if (mythicMonstersGame.dragonRage && mythicMonstersGame.monsterPower >= 200) {
        const rageMultiplier = 8 + mythicMonstersGame.evolutionStage;
        totalWin *= rageMultiplier;
        statusMessage = `🐉 DRAGON'S APOCALYPTIC RAGE! ${rageMultiplier}x DESTRUCTION!`;
        mythicMonstersGame.monsterPower = 0;
        
        // Trigger dragon rage effect
        document.getElementById('dragonRageEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('dragonRageEffect').style.opacity = '0';
            mythicMonstersGame.dragonRage = false;
        }, 4000);
    }

    // Phoenix rising activation
    if (mythicMonstersGame.phoenixRising && mythicMonstersGame.monsterPower >= 150) {
        const phoenixMultiplier = 6 + mythicMonstersGame.evolutionStage;
        totalWin *= phoenixMultiplier;
        statusMessage = `🔥 PHOENIX REBIRTH INFERNO! ${phoenixMultiplier}x RESURRECTION!`;
        mythicMonstersGame.monsterPower += 50; // Phoenix regenerates power
        
        // Trigger phoenix effect
        document.getElementById('phoenixRisingEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('phoenixRisingEffect').style.opacity = '0';
            mythicMonstersGame.phoenixRising = false;
        }, 5000);
    }

    // Kraken awakening
    if (mythicMonstersGame.krakenAwakened && clusters.length >= 3) {
        const krakenMultiplier = 10 + clusters.length;
        totalWin *= krakenMultiplier;
        statusMessage = `🐙 KRAKEN'S TENTACLE STORM! ${krakenMultiplier}x CHAOS!`;
        
        // Trigger kraken effect
        document.getElementById('krakenAwakeEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('krakenAwakeEffect').style.opacity = '0';
            mythicMonstersGame.krakenAwakened = false;
        }, 3500);
    }

    // Mega cluster bonus
    if (clusters.length >= 5) {
        totalWin += totalBet * 100;
        statusMessage += ' MEGA CLUSTER CONVERGENCE!';
        mythicMonstersGame.megaMultiplier += 1;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        mythicMonstersGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#mythicReels .slot');
        winningClusters.forEach(index => {
            slots[index].classList.add('cluster-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The monsters slumber, awaiting greater power...';
    }

    document.getElementById('mythicGameStatus').textContent = statusMessage;
    mythicMonstersGame.isSpinning = false;
    updateMythicDisplay();
}

function findClusters(symbols) {
    const clusters = [];
    const visited = new Set();
    const gridSize = 6;

    for (let i = 0; i < symbols.length; i++) {
        if (!visited.has(i)) {
            const cluster = [];
            const symbol = symbols[i];
            const stack = [i];

            while (stack.length > 0) {
                const current = stack.pop();
                if (visited.has(current)) continue;
                
                visited.add(current);
                cluster.push(current);

                // Check adjacent cells (up, down, left, right)
                const row = Math.floor(current / gridSize);
                const col = current % gridSize;
                
                const neighbors = [
                    current - gridSize, // up
                    current + gridSize, // down
                    col > 0 ? current - 1 : -1, // left
                    col < gridSize - 1 ? current + 1 : -1 // right
                ];

                neighbors.forEach(neighbor => {
                    if (neighbor >= 0 && neighbor < symbols.length && 
                        !visited.has(neighbor) && 
                        (symbols[neighbor] === symbol || symbols[neighbor] === MYTHIC_SYMBOLS.WILD)) {
                        stack.push(neighbor);
                    }
                });
            }

            if (cluster.length >= 5) {
                clusters.push(cluster);
            }
        }
    }

    return clusters;
}

function updateMythicDisplay() {
    const spinButton = document.getElementById('summonMonsters');
    spinButton.disabled = mythicMonstersGame.isSpinning;
    spinButton.textContent = mythicMonstersGame.isSpinning ? 'SUMMONING...' : 'SUMMON MONSTERS';

    document.getElementById('mythicMegaSpins').textContent = mythicMonstersGame.megaSpins;
    document.getElementById('mythicLastWin').textContent = `${mythicMonstersGame.lastWin} GA`;
    document.getElementById('monsterPower').textContent = mythicMonstersGame.monsterPower;
    
    const evolutionStages = ['I', 'II', 'III', 'IV', 'V', 'OMEGA'];
    document.getElementById('evolutionStage').textContent = evolutionStages[mythicMonstersGame.evolutionStage - 1] || 'I';
    
    document.getElementById('clusterCount').textContent = mythicMonstersGame.clusterCount;
    document.getElementById('dragonRageStatus').textContent = mythicMonstersGame.dragonRage ? 'RAGING!' : 'DORMANT';
    document.getElementById('phoenixStatus').textContent = mythicMonstersGame.phoenixRising ? 'RISING!' : 'SLEEPING';
    document.getElementById('krakenStatus').textContent = mythicMonstersGame.krakenAwakened ? 'AWAKENED!' : 'DEEP';
    document.getElementById('megaMultiplier').textContent = `${mythicMonstersGame.megaMultiplier.toFixed(1)}x`;
    
    // Update power meter
    const powerLevel = (mythicMonstersGame.monsterPower / 200) * 100;
    document.getElementById('powerMeter').style.width = `${Math.min(powerLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMythicMonstersGame();
});

