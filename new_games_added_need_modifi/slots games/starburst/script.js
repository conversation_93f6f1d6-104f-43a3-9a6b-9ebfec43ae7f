// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadStarburstGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                            <h4 class="text-xl font-bold mb-4 text-cyan-400">STARBURST INFINITY</h4>
                            <p class="text-xs text-cyan-300 mb-4">NetEnt Evolution</p>
                            
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2 text-cyan-300">BET LEVEL</label>
                                <input type="range" id="starburstLevel" min="1" max="10" value="5" 
                                       class="w-full mb-2">
                                <div class="flex justify-between text-xs text-gray-400">
                                    <span>1</span><span>10</span>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm font-medium mb-2 text-cyan-300">COIN VALUE</label>
                                <select id="starburstCoin" class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                                    <option value="1">1 GA</option>
                                    <option value="2">2 GA</option>
                                    <option value="5" selected>5 GA</option>
                                    <option value="10">10 GA</option>
                                    <option value="20">20 GA</option>
                                </select>
                            </div>
                            
                            <button id="spinStarburst" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                                QUANTUM SPIN
                            </button>
                            
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-cyan-300">Total Bet:</span>
                                    <span id="starburstTotalBet" class="text-white">25 GA</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-cyan-300">Last Win:</span>
                                    <span id="starburstLastWin" class="text-green-400">0 GA</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-cyan-300">Re-spins:</span>
                                    <span id="starburstRespins" class="text-purple-400">0</span>
                                </div>
                            </div>
                            
                            <div class="mt-6 p-4 bg-cyan-900/20 rounded-lg border border-cyan-500/20">
                                <h5 class="text-sm font-bold mb-2 text-cyan-300">QUANTUM ENERGY</h5>
                                <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                                    <div id="starburstQuantum" class="bg-gradient-to-r from-cyan-400 to-blue-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                                </div>
                                <p class="text-xs text-gray-400">Expanding wilds charge quantum field</p>
                            </div>
                        </div>
                    </div>

                    <!-- Game Display -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                            <div id="starburstReels" class="grid grid-cols-5 gap-2 mb-6">
                                <!-- 5x3 reel grid -->
                            </div>
                            
                            <div id="starburstStatus" class="text-center text-lg font-semibold text-cyan-400 mb-4">
                                Quantum gems await your discovery
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div class="bg-cyan-900/20 p-3 rounded-lg">
                                    <h6 class="font-bold text-cyan-300 mb-2">PAYTABLE</h6>
                                    <div class="space-y-1 text-xs">
                                        <div class="flex justify-between"><span>x5:</span><span class="text-purple-400">250x</span></div>
                                        <div class="flex justify-between"><span>x5:</span><span class="text-purple-400">60x</span></div>
                                        <div class="flex justify-between"><span>x5:</span><span class="text-blue-400">50x</span></div>
                                        <div class="flex justify-between"><span>x5:</span><span class="text-green-400">40x</span></div>
                                        <div class="flex justify-between"><span>x5:</span><span class="text-yellow-400">30x</span></div>
                                    </div>
                                </div>
                                <div class="bg-cyan-900/20 p-3 rounded-lg">
                                    <h6 class="font-bold text-cyan-300 mb-2">FEATURES</h6>
                                    <div class="space-y-1 text-xs">
                                        <div> <span class="text-cyan-400">Wild:</span> Expands & re-spins</div>
                                        <div> <span class="text-purple-400">Quantum:</span> Random multipliers</div>
                                        <div> <span class="text-blue-400">Both Ways:</span> Left-to-right & right-to-left</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            setupStarburstGame();
        }

        let starburstGame = {
            isSpinning: false,
            respinCount: 0,
            quantumEnergy: 0,
            reels: [],
            symbols: ['', '', '', '', '', '', '', ''],
            expandedWilds: []
        };

        function setupStarburstGame() {
            // Initialize reels display
            const reelsContainer = document.getElementById('starburstReels');
            for (let i = 0; i < 15; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'bg-black/50 border border-cyan-500/30 rounded-lg h-20 flex items-center justify-center text-4xl transition-all duration-300';
                symbol.textContent = starburstGame.symbols[Math.floor(Math.random() * 7)]; // Exclude wild initially
                reelsContainer.appendChild(symbol);
            }
            starburstGame.reels = Array.from(reelsContainer.children);
            
            document.getElementById('spinStarburst').addEventListener('click', spinStarburstReels);
            document.getElementById('starburstLevel').addEventListener('input', updateStarburstDisplay);
            document.getElementById('starburstCoin').addEventListener('change', updateStarburstDisplay);
            updateStarburstDisplay();
        }

        function updateStarburstDisplay() {
            const level = parseInt(document.getElementById('starburstLevel').value);
            const coinValue = parseInt(document.getElementById('starburstCoin').value);
            const totalBet = level * coinValue * 10; // 10 fixed paylines
            
            document.getElementById('starburstTotalBet').textContent = '25 GA';
            document.getElementById('starburstRespins').textContent = starburstGame.respinCount;
            document.getElementById('starburstQuantum').style.width = starburstGame.quantumEnergy + '%';
        }

        function spinStarburstReels() {
            if (starburstGame.isSpinning) return;
            
            const level = parseInt(document.getElementById('starburstLevel').value);
            const coinValue = parseInt(document.getElementById('starburstCoin').value);
            const totalBet = level * coinValue * 10;
            
            if (balance < totalBet) {
                document.getElementById('starburstStatus').textContent = 'Insufficient quantum energy for this spin';
                return;
            }
            
            balance -= totalBet;
            updateBalance();
            
            starburstGame.isSpinning = true;
            document.getElementById('spinStarburst').disabled = true;
            
            // Clear previous expanded wilds if not re-spinning
            if (starburstGame.respinCount === 0) {
                starburstGame.expandedWilds = [];
            }
            
            // Animate reels with quantum effects
            let animationSteps = 20;
            let currentStep = 0;
            
            const animateReels = () => {
                if (currentStep < animationSteps) {
                    starburstGame.reels.forEach((reel, index) => {
                        if (!starburstGame.expandedWilds.includes(index)) {
                            reel.textContent = starburstGame.symbols[Math.floor(Math.random() * 7)];
                            reel.style.transform = 'scale(1.1) rotate(5deg)';
                            reel.style.background = 'linear-gradient(45deg, #00d4ff, #3b82f6)';
                            reel.style.boxShadow = '0 0 20px #00d4ff';
                        }
                    });
                    
                    currentStep++;
                    setTimeout(animateReels, 100);
                } else {
                    // Final symbols
                    const finalSymbols = [];
                    for (let i = 0; i < 15; i++) {
                        if (starburstGame.expandedWilds.includes(i)) {
                            finalSymbols.push(''); // Keep expanded wild
                        } else {
                            let symbol;
                            if (Math.random() < 0.05 && [6, 7, 8].includes(i)) { // Wilds only in middle reels
                                symbol = '';
                            } else {
                                symbol = starburstGame.symbols[Math.floor(Math.random() * 7)];
                            }
                            finalSymbols.push(symbol);
                        }
                    }
                    
                    starburstGame.reels.forEach((reel, index) => {
                        if (!starburstGame.expandedWilds.includes(index)) {
                            reel.textContent = finalSymbols[index];
                            reel.style.transform = 'scale(1)';
                            reel.style.background = '';
                            reel.style.boxShadow = '';
                        }
                    });
                    
                    checkStarburstWins(finalSymbols, totalBet);
                }
            };
            
            animateReels();
        }

        function checkStarburstWins(symbols, totalBet) {
            let totalWin = 0;
            let newWilds = [];
            
            // Check for new wilds in middle reels (reels 1, 2, 3)
            [1, 2, 3].forEach(reel => {
                const positions = [reel * 3, reel * 3 + 1, reel * 3 + 2];
                if (positions.some(pos => symbols[pos] === '' && !starburstGame.expandedWilds.includes(pos))) {
                    // Expand wild on entire reel
                    positions.forEach(pos => {
                        if (!starburstGame.expandedWilds.includes(pos)) {
                            starburstGame.expandedWilds.push(pos);
                            newWilds.push(pos);
                            starburstGame.reels[pos].textContent = '';
                            starburstGame.reels[pos].style.background = 'linear-gradient(45deg, #fbbf24, #f59e0b)';
                            starburstGame.reels[pos].style.boxShadow = '0 0 30px #fbbf24';
                            starburstGame.reels[pos].style.animation = 'pulse 1s infinite';
                        }
                    });
                    
                    // Increase quantum energy
                    starburstGame.quantumEnergy = Math.min(100, starburstGame.quantumEnergy + 25);
                }
            });
            
            // Calculate line wins (both ways)
            totalWin += calculateStarburstWins(symbols, totalBet, true);  // Left to right
            totalWin += calculateStarburstWins(symbols, totalBet, false); // Right to left
            
            // Quantum bonus
            if (starburstGame.quantumEnergy >= 100) {
                const quantumMultiplier = 2 + Math.floor(Math.random() * 4); // 2x to 5x
                totalWin *= quantumMultiplier;
                starburstGame.quantumEnergy = 0;
                document.getElementById('starburstStatus').innerHTML = 
                    `<span class="text-yellow-400"> QUANTUM BURST! ${quantumMultiplier}x multiplier applied!</span>`;
            }
            
            if (totalWin > 0) {
                balance += totalWin;
                updateBalance();
                document.getElementById('starburstLastWin').textContent = '0 GA';
                
                if (!document.getElementById('starburstStatus').innerHTML.includes('QUANTUM BURST')) {
                    document.getElementById('starburstStatus').innerHTML = 
                        `<span class="text-green-400">Stellar alignment! You won  GA!</span>`;
                }
            }
            
            // Check for re-spin
            if (newWilds.length > 0) {
                starburstGame.respinCount++;
                document.getElementById('starburstStatus').innerHTML = 
                    `<span class="text-cyan-400">  STARBURST RE-SPIN ${starburstGame.respinCount}!</span>`;
                
                setTimeout(() => {
                    starburstGame.isSpinning = false;
                    document.getElementById('spinStarburst').disabled = false;
                    updateStarburstDisplay();
                }, 2000);
            } else {
                // No more re-spins
                starburstGame.respinCount = 0;
                starburstGame.expandedWilds = [];
                
                setTimeout(() => {
                    starburstGame.reels.forEach(reel => {
                        reel.style.background = '';
                        reel.style.boxShadow = '';
                        reel.style.animation = '';
                    });
                    starburstGame.isSpinning = false;
                    document.getElementById('spinStarburst').disabled = false;
                    
                    if (totalWin === 0) {
                        document.getElementById('starburstStatus').textContent = 'The cosmic dance continues...';
                    }
                    updateStarburstDisplay();
                }, 2000);
            }
        }

        function calculateStarburstWins(symbols, totalBet, leftToRight) {
            let totalWin = 0;
            const lines = [
                [0,3,6,9,12], [1,4,7,10,13], [2,5,8,11,14], // Horizontal lines
                [0,4,8,10,14], [2,4,6,10,12], [1,3,7,11,13], // Diagonal lines
                [0,4,6,10,14], [2,4,8,10,12], [1,5,7,9,13], [0,3,8,11,14] // Additional lines
            ];
            
            lines.forEach(line => {
                const lineSymbols = leftToRight ? 
                    line.map(pos => symbols[pos]) : 
                    line.slice().reverse().map(pos => symbols[pos]);
                
                let matchCount = 0;
                let symbol = lineSymbols[0];
                
                for (let i = 0; i < lineSymbols.length; i++) {
                    if (lineSymbols[i] === symbol || lineSymbols[i] === '' || symbol === '') {
                        if (symbol === '' && lineSymbols[i] !== '') {
                            symbol = lineSymbols[i];
                        }
                        matchCount++;
                    } else {
                        break;
                    }
                }
                
                if (matchCount >= 3) {
                    const multiplier = getStarburstMultiplier(symbol, matchCount);
                    totalWin += Math.floor((totalBet / 10) * multiplier);
                }
            });
            
            return totalWin;
        }

        function getStarburstMultiplier(symbol, count) {
            const multipliers = {
                '': [0, 0, 25, 120, 250],   // Diamond
                '': [0, 0, 12, 30, 60],     // Purple gem
                '': [0, 0, 10, 25, 50],     // Blue gem
                '': [0, 0, 8, 20, 40],      // Green gem
                '': [0, 0, 6, 15, 30],      // Yellow gem
                '': [0, 0, 4, 10, 20],      // Orange gem
                '': [0, 0, 3, 8, 15]        // Red gem
            };
            
            return multipliers[symbol] ? multipliers[symbol][count] : 0;
        }

        // Mahjong Riches Deluxe Game Implementation
        

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadStarburstGame();
});