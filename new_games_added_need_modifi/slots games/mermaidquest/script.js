// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Mermaid Quest Game Implementation
function loadMermaidQuestGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">MERMAID QUEST</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">BET AMOUNT</label>
                        <input type="number" id="mermaidBet" value="30" min="1" max="1000" 
                               class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">DIVE DEPTH</label>
                        <select id="mermaidDepth" class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="shallow">Shallow Reef (30 lines)</option>
                            <option value="medium">Coral Garden (60 lines)</option>
                            <option value="deep">Ocean Abyss (90 lines)</option>
                        </select>
                    </div>
                    
                    <button id="diveForPearls" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        DIVE FOR PEARLS
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-blue-300">Pearl Collection:</span>
                            <span id="mermaidPearlCount" class="text-white">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-300">Last Win:</span>
                            <span id="mermaidLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-300">Oceanic Spins:</span>
                            <span id="mermaidOceanic" class="text-purple-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/20">
                        <h5 class="text-sm font-bold mb-2 text-blue-300">MERMAID'S TREASURE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="pearlMeter" class="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-1000" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Collect pearls for oceanic rewards</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <div id="mermaidReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>
                    
                    <div id="mermaidStatus" class="text-center text-lg font-semibold text-blue-400 mb-4">
                        Dive into the underwater kingdom to find mermaid treasures
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-blue-300 mb-2">PAYTABLE</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🧜‍♀️ x5:</span><span class="text-cyan-400">2500x</span></div>
                                <div class="flex justify-between"><span>🐚 x5:</span><span class="text-blue-400">1000x</span></div>
                                <div class="flex justify-between"><span>🌊 x5:</span><span class="text-aqua-400">500x</span></div>
                                <div class="flex justify-between"><span>🐠 x5:</span><span class="text-teal-400">250x</span></div>
                            </div>
                        </div>
                        <div class="bg-blue-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-blue-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🛡️ <span class="text-blue-400">Wild:</span> Trident substitutes all</div>
                                <div>💎 <span class="text-purple-400">Scatter:</span> 3+ triggers Oceanic Spins</div>
                                <div>🦪 <span class="text-cyan-400">Bonus:</span> Pearl meter multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupMermaidQuestGame();
}

let mermaidQuestGame = {
    isSpinning: false,
    oceanicSpins: 0,
    pearlCollection: 0,
    lastWin: 0
};

// --- FIXED: Added a comprehensive list of 30 paylines for the 5x4 grid ---
const allPaylines = [
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19], // Horizontals
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19], // V-Shapes
    [5, 1, 2, 3, 9], [10, 16, 17, 18, 14],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4],
    [5, 6, 2, 8, 9], [10, 11, 17, 13, 14],
    [0, 5, 10, 15, 16], [1, 6, 11, 16, 17], [2, 7, 12, 17, 18], [3, 8, 13, 18, 19], // Column-like
    [4, 9, 14, 19, 18],
    [0, 6, 7, 8, 2], [15, 11, 12, 13, 17],
    [5, 10, 11, 12, 7], [10, 5, 6, 7, 12],
    [1, 2, 8, 14, 18], [3, 4, 8, 12, 16],
    [0, 5, 11, 17, 15], [4, 9, 13, 17, 19],
    [1, 7, 13, 9, 3], [16, 12, 8, 2, 6],
    [5, 11, 12, 13, 9], [10, 6, 7, 8, 14]
];


function setupMermaidQuestGame() {
    document.getElementById('diveForPearls').addEventListener('click', () => diveForOceanPearls(false));
    updateMermaidDisplay();
}

function updateMermaidDisplay() {
    document.getElementById('diveForPearls').disabled = mermaidQuestGame.isSpinning;
    document.getElementById('mermaidOceanic').textContent = mermaidQuestGame.oceanicSpins;
}

// --- FIXED: Added isFreeSpin parameter to prevent deducting balance during Oceanic Spins ---
function diveForOceanPearls(isFreeSpin = false) {
    const betAmount = parseInt(document.getElementById('mermaidBet').value);
    
    if (mermaidQuestGame.isSpinning) return;
    if (!isFreeSpin && balance < betAmount) {
        document.getElementById('mermaidStatus').textContent = 'Not enough balance!';
        return;
    }
    
    if (!isFreeSpin) {
        balance -= betAmount;
    } else {
        mermaidQuestGame.oceanicSpins--;
    }
    
    updateBalance();
    updateMermaidDisplay();
    mermaidQuestGame.isSpinning = true;
    document.getElementById('diveForPearls').disabled = true;
    document.getElementById('mermaidStatus').textContent = 'Diving for ocean treasures...';
    document.getElementById('mermaidLastWin').textContent = `0 GA`;
    
    // Clear previous win highlights
    const slots = document.querySelectorAll('#mermaidReels div');
    slots.forEach(slot => slot.classList.remove('bg-blue-500/30'));
    
    const reels = document.getElementById('mermaidReels');
    reels.innerHTML = '';
    const symbols = [];
    const mermaidSymbols = ['🧜‍♀️', '🐚', '🌊', '🐠', '🦞', '🪸', '🐙', '🛡️', '💎', '🦪'];
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = mermaidSymbols[Math.floor(Math.random() * mermaidSymbols.length)];
        symbols.push(symbol);
        const slot = document.createElement('div');
        slot.className = 'bg-black/50 rounded-lg p-2 text-center text-3xl border border-blue-500/20';
        slot.textContent = symbol;
        reels.appendChild(slot);
    }
    
    setTimeout(() => {
        checkMermaidWins(symbols, betAmount);
    }, 1500);
}

// --- FIXED: Complete rewrite of the win calculation logic ---
function checkMermaidWins(symbols, totalBet) {
    let winningLineIndices = new Set();
    let totalWin = 0;
    
    const diveDepth = document.getElementById('mermaidDepth').value;
    const lines = diveDepth === 'shallow' ? 30 : diveDepth === 'medium' ? 60 : 90;
    const lineBet = totalBet / lines;

    allPaylines.forEach(line => {
        const firstSymbol = symbols[line[0]];
        // A paying line cannot start with a scatter or bonus symbol
        if (firstSymbol === '💎' || firstSymbol === '🦪') return;

        let paySymbol = firstSymbol;
        let wildCount = 0;
        if(paySymbol === '🛡️') wildCount++;

        // Determine the symbol to be paid for lines starting with Wilds
        for (let i = 1; i < line.length; i++) {
            if (symbols[line[i]] !== '🛡️') {
                paySymbol = symbols[line[i]];
                break;
            }
        }
         // If a line is all wilds, it pays as the highest symbol
        if (paySymbol === '🛡️') paySymbol = '🧜‍♀️';

        let matchCount = 0;
        for (let i = 0; i < line.length; i++) {
            if (symbols[line[i]] === paySymbol || symbols[line[i]] === '🛡️') {
                matchCount++;
            } else {
                break; // Sequence broken
            }
        }
        
        if (matchCount >= 3) {
            const win = calculateMermaidWins(lineBet, paySymbol, matchCount);
            if (win > 0) {
                totalWin += win;
                // Add winning symbol indices to the set to be highlighted
                for(let i = 0; i < matchCount; i++) {
                    winningLineIndices.add(line[i]);
                }
            }
        }
    });
    
    if (totalWin > 0) {
        balance += totalWin;
        mermaidQuestGame.lastWin = totalWin;
        updateBalance();
        document.getElementById('mermaidStatus').textContent = `Treasure found! You won ${totalWin.toFixed(2)} GA!`;
        document.getElementById('mermaidLastWin').textContent = `${totalWin.toFixed(2)} GA`;
        
        // Highlight winning symbols
        const reelSlots = document.querySelectorAll('#mermaidReels div');
        winningLineIndices.forEach(idx => {
            reelSlots[idx].classList.add('bg-blue-500/30');
        });
    } else {
        document.getElementById('mermaidStatus').textContent = 'No treasure this time. Keep diving!';
    }
    
    let statusText = document.getElementById('mermaidStatus').textContent;

    // Check for scatter bonus (3 or more 💎)
    const scatterCount = symbols.filter(s => s === '💎').length;
    if (scatterCount >= 3) {
        const spinsWon = scatterCount === 3 ? 5 : scatterCount === 4 ? 10 : 15;
        mermaidQuestGame.oceanicSpins += spinsWon;
        statusText += ` + ${spinsWon} OCEANIC SPINS!`;
    }
    
    // Check for pearl bonus (count 🦪 symbols)
    const pearlCount = symbols.filter(s => s === '🦪').length;
    if (pearlCount > 0) {
        mermaidQuestGame.pearlCollection = Math.min(100, mermaidQuestGame.pearlCollection + pearlCount);
        document.getElementById('pearlMeter').style.width = `${mermaidQuestGame.pearlCollection}%`;
        document.getElementById('mermaidPearlCount').textContent = mermaidQuestGame.pearlCollection;
        statusText += ` + ${pearlCount} Pearls!`;
    }
    
    document.getElementById('mermaidStatus').textContent = statusText;
    mermaidQuestGame.isSpinning = false;
    updateMermaidDisplay();
    
    if (mermaidQuestGame.oceanicSpins > 0 && !mermaidQuestGame.isSpinning) {
        setTimeout(() => diveForOceanPearls(true), 2000);
    }
}

// --- FIXED: Function now calculates win based on line bet ---
function calculateMermaidWins(lineBet, symbol, count) {
    const multiplier = getMermaidMultiplier(symbol, count);
    return lineBet * multiplier;
}

// --- FIXED: Multipliers now match the HTML paytable and are more balanced ---
function getMermaidMultiplier(symbol, count) {
    const multipliers = {
        '🧜‍♀️': {3: 100, 4: 500, 5: 2500},
        '🐚': {3: 50, 4: 200, 5: 1000},
        '🌊': {3: 20, 4: 100, 5: 500},
        '🐠': {3: 10, 4: 50, 5: 250},
        '🦞': {3: 5, 4: 25, 5: 120},
        '🪸': {3: 4, 4: 20, 5: 100},
        '🐙': {3: 2, 4: 10, 5: 50}
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMermaidQuestGame();
});