// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// RoboReels 3000 Game Implementation
const ROBOT_SYMBOLS = {
    MECH: '🤖',
    CIRCUIT: '🔌',
    GEAR: '⚙️',
    BATTERY: '🔋',
    CHIP: '💾',
    LASER: '🔴',
    ANTENNA: '📡',
    BOLT: '🔩',
    CRYSTAL: '💎',
    SCATTER: 'R'
};

const ROBOT_REEL_SYMBOLS = [
    ROBOT_SYMBOLS.MECH, ROBOT_SYMBOLS.CIRCUIT, ROBOT_SYMBOLS.GEAR,
    ROBOT_SYMBOLS.BATTERY, ROBOT_SYMBOLS.CHIP, ROBOT_SYMBOLS.LASER,
    ROBOT_SYMBOLS.ANTENNA, ROBOT_SYMBOLS.BOLT, ROBOT_SYMBOLS.CRYSTAL, ROBOT_SYMBOLS.SCATTER
];

let roboReelsGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    factoryLevel: 1,
    overdriveMode: false,
    energyLevel: 0,
    robotsBuilt: 0
};

function loadRoboReelsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400 font-mono">ROBOREELS 3000</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-orange-300">PRODUCTION COST</label>
                        <input type="number" id="robotBet" value="45" min="15" max="1000" step="15"
                               class="w-full bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startRobotProduction" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        START PRODUCTION
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Cycles:</span>
                            <span id="robotFreeSpins" class="text-orange-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Output:</span>
                            <span id="robotLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Factory Level:</span>
                            <span id="factoryLevel" class="text-blue-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Robots Built:</span>
                            <span id="robotsBuilt" class="text-purple-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-orange-900/20 rounded-lg border border-orange-500/20">
                        <h5 class="text-sm font-bold mb-2 text-orange-300 font-mono">ENERGY CORE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="energyMeter" class="bg-gradient-to-r from-orange-500 to-red-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="energyStatus" class="text-xs text-center text-gray-400">Energy: 0%</p>
                    </div>
                    
                    <div class="mt-4 p-4 bg-gray-900/20 rounded-lg border border-orange-500/20">
                        <h5 class="text-sm font-bold mb-2 text-orange-300 font-mono">COMPONENT VALUES</h5>
                        <div class="text-xs space-y-1">
                            <div>🤖 <span class="text-orange-400">Mech:</span> 100x bet</div>
                            <div>🔌 <span class="text-yellow-400">Circuit:</span> 60x bet</div>
                            <div>⚙️ <span class="text-gray-400">Gear:</span> 40x bet</div>
                            <div>🔋 <span class="text-green-400">Battery:</span> 30x bet</div>
                            <div>💾 <span class="text-blue-400">Chip:</span> 25x bet</div>
                            <div>R <span class="text-red-400">Reboot:</span> Scatter bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <div class="relative mb-6">
                        <div id="robotReels" class="grid grid-cols-5 gap-2 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-orange-900/20 rounded-lg flex items-center justify-center text-4xl border border-orange-500/20 transition-all duration-300">🤖</div>`
                            ).join('')}
                        </div>
                        <div id="overdriveEffect" class="absolute inset-0 bg-gradient-to-t from-orange-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="sparkEffect" class="absolute inset-0 pointer-events-none"></div>
                    </div>
                    
                    <div id="robotGameStatus" class="text-center text-lg font-semibold text-orange-300 mb-4 h-8 font-mono">
                        Factory ready for production...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-orange-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">FACTORY STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Efficiency:</span>
                                    <span id="efficiency" class="text-green-400">OPTIMAL</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Overdrive:</span>
                                    <span id="overdriveStatus" class="text-red-400">OFFLINE</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Assembly Line:</span>
                                    <span class="text-blue-400">ACTIVE</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-orange-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PRODUCTION STATS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Quality:</span>
                                    <span class="text-yellow-400">PREMIUM</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Output Rate:</span>
                                    <span class="text-cyan-400">HIGH</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupRoboReelsGame();
}

function setupRoboReelsGame() {
    document.getElementById('startRobotProduction').addEventListener('click', startRobotProduction);
    const reelsContainer = document.getElementById('robotReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-orange-900/20 rounded-lg p-2 text-center text-4xl border border-orange-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = ROBOT_REEL_SYMBOLS[i % ROBOT_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateRobotDisplay();
}

function startRobotProduction() {
    if (roboReelsGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('robotBet').value);

    if (roboReelsGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('robotGameStatus').textContent = 'INSUFFICIENT PRODUCTION FUNDS';
            return;
        }
        balance -= totalBet;
    } else {
        roboReelsGame.freeSpins--;
    }

    roboReelsGame.isSpinning = true;
    roboReelsGame.lastWin = 0;
    updateBalance();
    updateRobotDisplay();
    document.getElementById('robotGameStatus').textContent = 'Assembly line activated...';

    const slots = document.querySelectorAll('#robotReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Add sparking effect during spin
    createSparkEffect();

    let spinDuration = 2000;
    let spinInterval = 70;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = ROBOT_REEL_SYMBOLS[Math.floor(Math.random() * ROBOT_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishRobotProduction(totalBet);
        }
    }, spinInterval);
}

function createSparkEffect() {
    const sparkContainer = document.getElementById('sparkEffect');
    sparkContainer.innerHTML = '';
    
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const spark = document.createElement('div');
            spark.className = 'absolute w-2 h-2 bg-orange-400 rounded-full animate-ping';
            spark.style.left = Math.random() * 100 + '%';
            spark.style.top = Math.random() * 100 + '%';
            sparkContainer.appendChild(spark);
            
            setTimeout(() => spark.remove(), 1000);
        }, i * 200);
    }
}

function finishRobotProduction(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#robotReels .slot');
    slots.forEach(slot => {
        const symbol = ROBOT_REEL_SYMBOLS[Math.floor(Math.random() * ROBOT_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkRobotWins(finalSymbols, totalBet);
}

function checkRobotWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for robot productions
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === ROBOT_SYMBOLS.MECH && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 60 : 30;
            statusMessage = 'PREMIUM MECH UNIT PRODUCED!';
            roboReelsGame.overdriveMode = true;
            roboReelsGame.robotsBuilt += count;
        } else if (symbol === ROBOT_SYMBOLS.CIRCUIT && count >= 3) {
            multiplier = count >= 5 ? 60 : count >= 4 ? 35 : 18;
            statusMessage = 'Advanced circuits manufactured!';
            roboReelsGame.robotsBuilt += Math.floor(count / 2);
        } else if (symbol === ROBOT_SYMBOLS.GEAR && count >= 3) {
            multiplier = count >= 5 ? 40 : count >= 4 ? 25 : 12;
            statusMessage = 'Precision gears assembled!';
        } else if (symbol === ROBOT_SYMBOLS.BATTERY && count >= 3) {
            multiplier = count >= 5 ? 30 : count >= 4 ? 18 : 9;
            statusMessage = 'Power cells charged and ready!';
            roboReelsGame.energyLevel = Math.min(100, roboReelsGame.energyLevel + 25);
        } else if (symbol === ROBOT_SYMBOLS.CHIP && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 15 : 8;
            statusMessage = 'AI chips programmed successfully!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Energy boost from laser symbols
    const laserCount = symbols.filter(s => s === ROBOT_SYMBOLS.LASER).length;
    if (laserCount > 0) {
        roboReelsGame.energyLevel = Math.min(100, roboReelsGame.energyLevel + (laserCount * 15));
        if (totalWin > 0) {
            statusMessage += ' Laser boost activated!';
        }
    }

    // Scatter bonus (R Reboot symbols)
    const scatterCount = symbols.filter(s => s === ROBOT_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 10 + (scatterCount - 3) * 5;
        roboReelsGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` SYSTEM REBOOT! +${freeSpinsAwarded} free cycles!`;
        roboReelsGame.factoryLevel++;
        
        // Trigger overdrive effect
        document.getElementById('overdriveEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('overdriveEffect').style.opacity = '0';
        }, 2000);
    }

    // Overdrive mode bonus
    if (roboReelsGame.overdriveMode && totalWin > 0) {
        totalWin *= 2.5;
        statusMessage += ' OVERDRIVE MODE - MASSIVE PRODUCTION BONUS!';
        setTimeout(() => { roboReelsGame.overdriveMode = false; }, 4000);
    }

    // Energy level bonus
    if (roboReelsGame.energyLevel >= 100 && totalWin > 0) {
        totalWin += totalBet * 20;
        statusMessage += ' FULL ENERGY - BONUS PRODUCTION!';
        roboReelsGame.energyLevel = 0;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        roboReelsGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#robotReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Production cycle incomplete. Recalibrating...';
        roboReelsGame.energyLevel = Math.max(0, roboReelsGame.energyLevel - 5);
    }

    document.getElementById('robotGameStatus').textContent = statusMessage;
    roboReelsGame.isSpinning = false;
    updateRobotDisplay();
}

function updateRobotDisplay() {
    const spinButton = document.getElementById('startRobotProduction');
    spinButton.disabled = roboReelsGame.isSpinning;
    spinButton.textContent = roboReelsGame.isSpinning ? 'PRODUCING...' : 'START PRODUCTION';

    document.getElementById('robotFreeSpins').textContent = roboReelsGame.freeSpins;
    document.getElementById('robotLastWin').textContent = `${roboReelsGame.lastWin} GA`;
    document.getElementById('factoryLevel').textContent = roboReelsGame.factoryLevel;
    document.getElementById('robotsBuilt').textContent = roboReelsGame.robotsBuilt;
    document.getElementById('energyMeter').style.width = `${roboReelsGame.energyLevel}%`;
    document.getElementById('energyStatus').textContent = `Energy: ${roboReelsGame.energyLevel}%`;
    document.getElementById('overdriveStatus').textContent = roboReelsGame.overdriveMode ? 'ACTIVE!' : 'OFFLINE';
    
    // Update efficiency based on factory level
    const efficiencyLevels = ['BASIC', 'GOOD', 'OPTIMAL', 'SUPERIOR', 'MAXIMUM'];
    const efficiencyIndex = Math.min(roboReelsGame.factoryLevel - 1, efficiencyLevels.length - 1);
    document.getElementById('efficiency').textContent = efficiencyLevels[efficiencyIndex];
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRoboReelsGame();
});

