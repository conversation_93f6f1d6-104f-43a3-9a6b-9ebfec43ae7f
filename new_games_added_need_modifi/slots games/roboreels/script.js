// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- <PERSON><PERSON>els Game Implementation ---

// Game constants
const ROBO_SYMBOLS = {
    WILD: '🔧',      // Wrench
    SCATTER: '💎',    // Gem
    POWER: '⚡',      // Power Bolt
    ROBOT: '🤖',
    ARM: '🦾',
    GEAR: '⚙️',
    BATTERY: '🔋',
    CPU: '🖥️',
    PLUG: '🔌',
};

const ROBO_REEL_SYMBOLS = [
    ROBO_SYMBOLS.ROBOT, ROBO_SYMBOLS.ARM, ROBO_SYMBOLS.GEAR, ROBO_SYMBOLS.BATTERY,
    ROBO_SYMBOLS.CPU, ROBO_SYMBOLS.PLUG, ROBO_SYMBOLS.WILD, ROBO_SYMBOLS.SCATTER, ROBO_SYMBOLS.POWER
];

// Define a comprehensive set of paylines for the 5x4 grid.
const ROBO_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes & Chevrons
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    // Diagonals
    [0, 6, 12, 18], [4, 8, 12, 16],
    // Other patterns
    [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4]
];

// Game state object
let roboReelsGame = {
    isSpinning: false,
    turboSpins: 0,
    powerLevel: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadRoboReelsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h4 class="text-xl font-bold mb-4 text-gray-400">ROBO REELS 5000</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">TOTAL BET</label>
                        <input type="number" id="roboBet" value="40" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-gray-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="activateRobots" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        ACTIVATE ROBOTS
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Win:</span>
                            <span id="roboLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Turbo Spins:</span>
                            <span id="roboTurbo" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-gray-500/20">
                        <h5 class="text-sm font-bold mb-2 text-gray-300">ROBOTIC POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="powerMeter" class="bg-gradient-to-r from-blue-500 to-cyan-400 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="roboPowerStatus" class="text-xs text-center text-gray-400">Charge to 100% for a power surge! (0%)</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <div id="roboReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid will be generated here -->
                    </div>
                    
                    <div id="roboStatus" class="text-center text-lg font-semibold text-gray-400 mb-4 h-8">
                        Assemble futuristic robots for high-tech rewards.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${ROBO_SYMBOLS.ROBOT} x5:</span><span class="text-cyan-400">100x</span></div>
                                <div class="flex justify-between"><span>${ROBO_SYMBOLS.ARM} x5:</span><span class="text-blue-400">50x</span></div>
                                <div class="flex justify-between"><span>${ROBO_SYMBOLS.GEAR} x5:</span><span class="text-gray-400">25x</span></div>
                                <div class="flex justify-between"><span>${ROBO_SYMBOLS.BATTERY} x5:</span><span class="text-green-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">${ROBO_SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter.</div>
                                <div><span class="text-purple-400">${ROBO_SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Turbo Spins.</div>
                                <div><span class="text-blue-400">${ROBO_SYMBOLS.POWER} Power:</span> Charges the meter for a win multiplier.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupRoboReelsGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupRoboReelsGame() {
    document.getElementById('activateRobots').addEventListener('click', activateRobotAssembly);
    const reelsContainer = document.getElementById('roboReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-gray-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = ROBO_REEL_SYMBOLS[i % ROBO_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateRoboDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateRoboDisplay() {
    const spinButton = document.getElementById('activateRobots');
    spinButton.disabled = roboReelsGame.isSpinning;
    spinButton.textContent = roboReelsGame.isSpinning ? 'ASSEMBLING...' : 'ACTIVATE ROBOTS';

    document.getElementById('roboTurbo').textContent = roboReelsGame.turboSpins;
    document.getElementById('powerMeter').style.width = `${roboReelsGame.powerLevel}%`;
    document.getElementById('roboPowerStatus').textContent = `Charge to 100% for a power surge! (${roboReelsGame.powerLevel}%)`;
    document.getElementById('roboLastWin').textContent = `${roboReelsGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function activateRobotAssembly() {
    if (roboReelsGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('roboBet').value);

    if (roboReelsGame.turboSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('roboStatus').textContent = 'Insufficient power!';
            return;
        }
        balance -= totalBet;
    } else {
        roboReelsGame.turboSpins--;
    }

    roboReelsGame.isSpinning = true;
    roboReelsGame.lastWin = 0;
    updateBalance();
    updateRoboDisplay();
    document.getElementById('roboStatus').textContent = 'Assembling robotic components...';

    const slots = document.querySelectorAll('#roboReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = ROBO_REEL_SYMBOLS[Math.floor(Math.random() * ROBO_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishRoboSpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishRoboSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#roboReels .slot');
    slots.forEach(slot => {
        const symbol = ROBO_REEL_SYMBOLS[Math.floor(Math.random() * ROBO_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkRoboWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and special symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkRoboWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // 1. Check for payline wins
    ROBO_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === ROBO_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== ROBO_SYMBOLS.WILD && symbols[index] !== ROBO_SYMBOLS.SCATTER) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        if (lineSymbol === ROBO_SYMBOLS.SCATTER) return;

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === ROBO_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getRoboMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 20; // Standardize line bet
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins (Turbo Spins)
    const scatterCount = symbols.filter(s => s === ROBO_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 5 + (scatterCount - 3) * 2;
        roboReelsGame.turboSpins += freeSpinsWon;
        statusMessage = `💎 ${freeSpinsWon} Turbo Spins activated!`;
    }
    
    // 3. Collect Power symbols
    const powerCount = symbols.filter(s => s === ROBO_SYMBOLS.POWER).length;
    if (powerCount > 0) {
        roboReelsGame.powerLevel = Math.min(100, roboReelsGame.powerLevel + powerCount * 10);
    }

    // 4. Check for Power Surge bonus
    if (roboReelsGame.powerLevel >= 100 && totalWin > 0) {
        const powerMultiplier = Math.floor(Math.random() * 4) + 2; // 2x to 5x multiplier
        totalWin *= powerMultiplier;
        roboReelsGame.powerLevel = 0; // Reset power
        statusMessage = `⚡ POWER SURGE! ${powerMultiplier}x WIN MULTIPLIER!`;
    }

    // 5. Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        roboReelsGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('POWER SURGE')) {
            statusMessage = statusMessage ? statusMessage : `Robotic victory! You won ${totalWin} GA!`;
        }

        const slots = document.querySelectorAll('#roboReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Assembly failed. Re-calibrating...';
    }
    
    document.getElementById('roboStatus').textContent = statusMessage;
    roboReelsGame.isSpinning = false;
    updateRoboDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getRoboMultiplier(symbol, count) {
    const multipliers = {
        [ROBO_SYMBOLS.ROBOT]:   { 3: 20, 4: 50, 5: 100 },
        [ROBO_SYMBOLS.ARM]:     { 3: 10, 4: 25, 5: 50 },
        [ROBO_SYMBOLS.GEAR]:    { 3: 8,  4: 20, 5: 25 },
        [ROBO_SYMBOLS.BATTERY]: { 3: 5,  4: 10, 5: 15 },
        [ROBO_SYMBOLS.CPU]:     { 3: 3,  4: 8,  5: 12 },
        [ROBO_SYMBOLS.PLUG]:    { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadRoboReelsGame();
});
