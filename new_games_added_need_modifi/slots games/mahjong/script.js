// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

/**
 * Loads the initial HTML for the game into the page.
 */
function loadMahjongGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">MAHJONG RICHES DELUXE</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">BET AMOUNT</label>
                        <input type="number" id="mahjongBet" value="32" min="16" max="1600" step="16"
                               class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">GAME MODE</label>
                        <select id="mahjongMode" class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="classic">Classic Match</option>
                            <option value="cascade" disabled>Cascade (Coming Soon)</option>
                        </select>
                    </div>
                    
                    <button id="startMahjong" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        START MATCH
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-green-300">Current Bet:</span>
                            <span id="mahjongCurrentBet" class="text-white">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-green-300">Total Win:</span>
                            <span id="mahjongTotalWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-green-300">Matches:</span>
                            <span id="mahjongMatches" class="text-yellow-400">0 / 16</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-green-900/20 rounded-lg border border-green-500/20">
                        <h5 class="text-sm font-bold mb-2 text-green-300">HARMONY METER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="mahjongHarmony" class="bg-gradient-to-r from-green-500 to-emerald-400 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Fill for a 2x win multiplier!</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <div id="mahjongBoard" class="grid grid-cols-8 gap-2 mb-6 min-h-[350px]">
                        <!-- Dynamic mahjong tile grid -->
                    </div>
                    
                    <div id="mahjongStatus" class="text-center text-lg font-semibold text-green-400 mb-4 h-8">
                        Find matching pairs to achieve harmony.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-green-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-green-300 mb-2">TILE PAYOUT (Multiplier)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🀄 Dragon:</span><span class="text-red-400">100x</span></div>
                                <div class="flex justify-between"><span>🀅 Wind:</span><span class="text-blue-400">50x</span></div>
                                <div class="flex justify-between"><span>🀇 Circle:</span><span class="text-green-400">25x</span></div>
                                <div class="flex justify-between"><span>🀐 Bamboo:</span><span class="text-yellow-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-green-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-green-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">Match Pairs:</span> Win a prize for each match.</div>
                                <div><span class="text-purple-400">Harmony Bonus:</span> Fill the meter to double your total win!</div>
                                <div><span class="text-green-400">Clear the Board:</span> Win a bonus for clearing all tiles.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupMahjongGame();
}

// Game state object
let mahjongGame = {
    isPlaying: false,
    selectedTiles: [],
    matches: 0,
    harmony: 0,
    board: [],
    tiles: ['🀄', '🀅', '🀆', '🀇', '🀈', '🀉', '🀊', '🀋', '🀌', '🀍', '🀎', '🀏', '🀐', '🀑', '🀒', '🀓'],
    currentWin: 0
};

/**
 * Sets up the game event listeners and initial display.
 */
function setupMahjongGame() {
    document.getElementById('startMahjong').addEventListener('click', startMahjongGame);
    document.getElementById('mahjongBet').addEventListener('input', updateMahjongDisplay);
    updateMahjongDisplay(); // Initial call
}

/**
 * Updates all UI elements based on the current game state.
 */
function updateMahjongDisplay() {
    const bet = parseInt(document.getElementById('mahjongBet').value) || 0;
    document.getElementById('mahjongCurrentBet').textContent = `${bet} GA`;
    document.getElementById('mahjongTotalWin').textContent = `${mahjongGame.currentWin} GA`;
    document.getElementById('mahjongMatches').textContent = `${mahjongGame.matches} / 16`;
    document.getElementById('mahjongHarmony').style.width = `${mahjongGame.harmony}%`;
}

/**
 * Starts a new round of the Mahjong game.
 */
function startMahjongGame() {
    if (mahjongGame.isPlaying) return;

    const bet = parseInt(document.getElementById('mahjongBet').value);
    if (balance < bet) {
        document.getElementById('mahjongStatus').textContent = 'Insufficient balance for this journey.';
        return;
    }

    balance -= bet;
    updateBalance();

    mahjongGame.isPlaying = true;
    mahjongGame.selectedTiles = [];
    mahjongGame.matches = 0;
    mahjongGame.harmony = 0;
    mahjongGame.currentWin = 0;
    updateMahjongDisplay();

    generateMahjongBoard();
    document.getElementById('startMahjong').disabled = true;
    document.getElementById('mahjongStatus').textContent = 'Find matching pairs to unlock ancient wealth.';
}

/**
 * Generates and shuffles the tiles for the game board.
 */
function generateMahjongBoard() {
    const boardContainer = document.getElementById('mahjongBoard');
    boardContainer.innerHTML = '';
    mahjongGame.board = [];

    // Create a set of 16 pairs (32 tiles total)
    let tileSet = [];
    mahjongGame.tiles.forEach(tile => tileSet.push(tile, tile));

    // Shuffle the tiles using Fisher-Yates algorithm
    for (let i = tileSet.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [tileSet[i], tileSet[j]] = [tileSet[j], tileSet[i]];
    }

    // Create and display each tile element
    for (let i = 0; i < tileSet.length; i++) {
        const tileElement = document.createElement('div');
        tileElement.className = 'mahjong-tile';
        tileElement.textContent = tileSet[i];
        tileElement.dataset.index = i;
        tileElement.addEventListener('click', () => selectMahjongTile(i));

        boardContainer.appendChild(tileElement);
        mahjongGame.board.push({
            element: tileElement,
            tile: tileSet[i],
            matched: false,
            selected: false
        });
    }
}

/**
 * Handles the logic when a player clicks on a tile.
 * @param {number} index - The index of the clicked tile in the board array.
 */
function selectMahjongTile(index) {
    const tile = mahjongGame.board[index];
    if (!mahjongGame.isPlaying || tile.matched || tile.selected || mahjongGame.selectedTiles.length >= 2) {
        return;
    }

    tile.selected = true;
    tile.element.classList.add('selected');
    mahjongGame.selectedTiles.push(index);

    if (mahjongGame.selectedTiles.length === 2) {
        setTimeout(checkMahjongMatch, 500);
    }
}

/**
 * Checks if the two selected tiles are a match.
 */
function checkMahjongMatch() {
    const [index1, index2] = mahjongGame.selectedTiles;
    const tile1 = mahjongGame.board[index1];
    const tile2 = mahjongGame.board[index2];

    if (tile1.tile === tile2.tile) {
        // Match found
        tile1.matched = true;
        tile2.matched = true;
        tile1.element.classList.add('matched');
        tile2.element.classList.add('matched');

        mahjongGame.matches++;
        mahjongGame.harmony = Math.min(100, mahjongGame.harmony + 6.25); // Fills up after 16 matches

        // Calculate win for this specific pair
        const bet = parseInt(document.getElementById('mahjongBet').value);
        const betPerPair = bet / 16; // Divide total bet by number of pairs
        const multiplier = getMahjongTileValue(tile1.tile);
        const winAmount = Math.round(betPerPair * multiplier);
        mahjongGame.currentWin += winAmount;
        document.getElementById('mahjongStatus').textContent = `Match! You won ${winAmount} GA!`;

        if (mahjongGame.matches === 16) {
            endMahjongGame();
        }
    } else {
        // No match
        tile1.element.classList.add('unmatched');
        tile2.element.classList.add('unmatched');
        document.getElementById('mahjongStatus').textContent = 'Not a match. Try again.';
        
        // Reset the styles after a short delay
        setTimeout(() => {
            tile1.element.classList.remove('selected', 'unmatched');
            tile2.element.classList.remove('selected', 'unmatched');
            tile1.selected = false;
            tile2.selected = false;
        }, 800);
    }

    mahjongGame.selectedTiles = [];
    updateMahjongDisplay();
}

/**
 * Gets the payout multiplier for a given tile.
 * @param {string} tile - The mahjong tile character.
 * @returns {number} The multiplier for the win.
 */
function getMahjongTileValue(tile) {
    const values = {
        '🀄': 100, // Dragon
        '🀅': 50,  // Wind
        '🀆': 50,  // Wind
        '🀇': 25,  // Circle
        '🀈': 25, '🀉': 25, '🀊': 25, '🀋': 25, '🀌': 25, '🀍': 25, '🀎': 25, '🀏': 25,
        '🀐': 15,  // Bamboo
        '🀑': 15, '🀒': 15, '🀓': 15, '🀔': 15, '🀕': 15,
    };
    return values[tile] || 10; // Default value for any other tiles
}

/**
 * Ends the game, calculates final winnings, and updates the UI.
 */
function endMahjongGame() {
    mahjongGame.isPlaying = false;
    let finalWin = mahjongGame.currentWin;
    let statusMessage = '';

    // Apply Harmony Bonus if meter is full
    if (mahjongGame.harmony >= 100) {
        finalWin *= 2;
        statusMessage = `🌟 PERFECT HARMONY! Your win is doubled to ${finalWin} GA!`;
    } else {
        statusMessage = `🏆 MAHJONG MASTERY! Total win: ${finalWin} GA!`;
    }

    if (finalWin > 0) {
        balance += finalWin;
        updateBalance();
    }
    
    document.getElementById('mahjongStatus').innerHTML = `<span class="text-green-400">${statusMessage}</span>`;

    setTimeout(() => {
        document.getElementById('startMahjong').disabled = false;
        document.getElementById('mahjongStatus').textContent = 'Ready for another round of harmony?';
        mahjongGame.currentWin = 0;
        updateMahjongDisplay();
    }, 4000);
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMahjongGame();
});
