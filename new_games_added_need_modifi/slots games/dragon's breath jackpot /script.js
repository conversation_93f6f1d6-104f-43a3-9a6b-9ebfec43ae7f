// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Dragon's Breath Game Implementation ---

// Game constants for Dragon's Breath
const DRAGON_SYMBOLS = {
    WILD: '🐉',      // Dragon Head (Wild)
    SCATTER: '🔥',    // Fire Breath (Scatter)
    BONUS: '💎',      // Crystal (Bonus)
    DRAGON: '🐲',
    SWORD: '⚔️',
    SHIELD: '🛡️',
    RED_ORB: '🔴',
    BLUE_ORB: '🔵',
    GREEN_ORB: '🟢',
};

const DRAGON_REEL_SYMBOLS = [
    DRAGON_SYMBOLS.DRAGON, DRAGON_SYMBOLS.SWORD, DRAGON_SYMBOLS.SHIELD, DRAGON_SYMBOLS.RED_ORB,
    DRAGON_SYMBOLS.BLUE_ORB, DRAGON_SYMBOLS.GREEN_ORB, DRAGON_SYMBOLS.WILD, DRAGON_SYMBOLS.SCATTER, DRAGON_SYMBOLS.BONUS
];

// Paylines for the 5x4 grid.
const DRAGON_PAYLINES = [
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19], [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    [0, 6, 12, 18], [4, 8, 12, 16], [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
];

// Game state object
let dragonsBreathGame = {
    isSpinning: false,
    infernoSpins: 0,
    furyLevel: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadDragonsBreathGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h4 class="text-xl font-bold mb-4 text-gray-400 font-mono">DRAGON'S LAIR</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">TRIBUTE</label>
                        <input type="number" id="dragonBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-gray-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        AWAKEN THE DRAGON
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Haul:</span>
                            <span id="dragonLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Inferno Spins:</span>
                            <span id="infernoSpins" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-gray-500/20">
                        <h5 class="text-sm font-bold mb-2 text-gray-300 font-mono">DRAGON'S FURY</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="furyMeter" class="bg-gradient-to-r from-red-500 to-orange-400 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="furyStatus" class="text-xs text-center text-gray-400">Collect 💎 to unleash its power! (0%)</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <div id="dragonReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>
                    
                    <div id="dragonStatus" class="text-center text-lg font-semibold text-purple-300 mb-4 h-8 font-mono">
                        The beast slumbers... will you claim its treasure?
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm font-mono">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.DRAGON} x5:</span><span class="text-red-400">1000x</span></div>
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.SWORD} x5:</span><span class="text-purple-400">500x</span></div>
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.SHIELD} x5:</span><span class="text-blue-400">250x</span></div>
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.RED_ORB} x5:</span><span class="text-orange-400">100x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">LEGENDS</h6>
                             <div class="space-y-1 text-xs">
                                <div><span class="text-green-400">${DRAGON_SYMBOLS.WILD} Wild Dragon:</span> Substitutes for all except Scatter.</div>
                                <div><span class="text-orange-400">${DRAGON_SYMBOLS.SCATTER} Scatter Fire:</span> 3+ unleashes Inferno Spins.</div>
                                <div><span class="text-cyan-400">${DRAGON_SYMBOLS.BONUS} Bonus Crystal:</span> Charges the Dragon's Fury meter.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupDragonsBreathGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupDragonsBreathGame() {
    document.getElementById('startSpin').addEventListener('click', startDragonSpin);
    const reelsContainer = document.getElementById('dragonReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-purple-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = DRAGON_REEL_SYMBOLS[i % DRAGON_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateDragonsBreathDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateDragonsBreathDisplay() {
    const spinButton = document.getElementById('startSpin');
    spinButton.disabled = dragonsBreathGame.isSpinning;
    spinButton.textContent = dragonsBreathGame.isSpinning ? 'SUMMONING...' : 'AWAKEN THE DRAGON';

    document.getElementById('infernoSpins').textContent = dragonsBreathGame.infernoSpins;
    document.getElementById('furyMeter').style.width = `${dragonsBreathGame.furyLevel}%`;
    document.getElementById('furyStatus').textContent = `Collect 💎 to unleash its power! (${dragonsBreathGame.furyLevel}%)`;
    document.getElementById('dragonLastWin').textContent = `${dragonsBreathGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function startDragonSpin() {
    if (dragonsBreathGame.isSpinning) return;
    const totalBet = parseInt(document.getElementById('dragonBet').value);

    if (dragonsBreathGame.infernoSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('dragonStatus').textContent = 'Insufficient tribute!';
            return;
        }
        balance -= totalBet;
    } else {
        dragonsBreathGame.infernoSpins--;
    }

    dragonsBreathGame.isSpinning = true;
    dragonsBreathGame.lastWin = 0;
    updateBalance();
    updateDragonsBreathDisplay();
    document.getElementById('dragonStatus').textContent = 'The dragon stirs...';

    const slots = document.querySelectorAll('#dragonReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    const spinAnimation = setInterval(() => {
        slots.forEach(slot => {
            slot.textContent = DRAGON_REEL_SYMBOLS[Math.floor(Math.random() * DRAGON_REEL_SYMBOLS.length)];
        });
    }, 100);

    setTimeout(() => {
        clearInterval(spinAnimation);
        finishDragonSpin(totalBet);
    }, 1500);
}

/**
 * Generates the final reel results and checks for wins.
 */
function finishDragonSpin(totalBet) {
    const finalSymbols = Array.from({ length: 20 }, () => DRAGON_REEL_SYMBOLS[Math.floor(Math.random() * DRAGON_REEL_SYMBOLS.length)]);
    document.querySelectorAll('#dragonReels .slot').forEach((slot, i) => slot.textContent = finalSymbols[i]);
    checkDragonWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and special symbols.
 */
function checkDragonWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for payline wins
    DRAGON_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === DRAGON_SYMBOLS.WILD) {
            lineSymbol = line.map(i => symbols[i]).find(s => s !== DRAGON_SYMBOLS.WILD && s !== DRAGON_SYMBOLS.SCATTER) || lineSymbol;
        }
        if (lineSymbol === DRAGON_SYMBOLS.SCATTER) return;

        let count = line.findIndex(i => symbols[i] !== lineSymbol && symbols[i] !== DRAGON_SYMBOLS.WILD);
        count = count === -1 ? line.length : count;

        if (count >= 3) {
            const multiplier = getDragonMultiplier(lineSymbol, count);
            totalWin += (totalBet / 20) * multiplier;
            line.slice(0, count).forEach(i => winningLines.add(i));
        }
    });

    // Check for scatter wins (Inferno Spins)
    const scatterCount = symbols.filter(s => s === DRAGON_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 8 + (scatterCount - 3) * 4;
        dragonsBreathGame.infernoSpins += freeSpinsWon;
        statusMessage = `🔥 INFERNO! ${freeSpinsWon} free spins ignited!`;
    }
    
    // Collect Bonus symbols
    const bonusCount = symbols.filter(s => s === DRAGON_SYMBOLS.BONUS).length;
    dragonsBreathGame.furyLevel = Math.min(100, dragonsBreathGame.furyLevel + bonusCount * 12);

    // Check for Dragon's Fury bonus
    if (dragonsBreathGame.furyLevel >= 100 && totalWin > 0) {
        const multiplier = Math.floor(Math.random() * 10) + 2; // 2x to 11x
        totalWin *= multiplier;
        dragonsBreathGame.furyLevel = 0;
        statusMessage = `🐲 DRAGON'S FURY! Your treasure is multiplied by ${multiplier}x!`;
    }

    // Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        dragonsBreathGame.lastWin = totalWin;
        if (!statusMessage) statusMessage = `Treasure secured! You claimed ${totalWin} GA!`;
        winningLines.forEach(i => document.querySelectorAll('#dragonReels .slot')[i].classList.add('win-highlight'));
    } else if (!statusMessage) {
        statusMessage = 'The dragon slumbers... try again.';
    }
    
    document.getElementById('dragonStatus').textContent = statusMessage;
    dragonsBreathGame.isSpinning = false;
    updateBalance();
    updateDragonsBreathDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 */
function getDragonMultiplier(symbol, count) {
    const multipliers = {
        [DRAGON_SYMBOLS.DRAGON]:    { 3: 200, 4: 500,  5: 1000 },
        [DRAGON_SYMBOLS.SWORD]:     { 3: 100, 4: 250,  5: 500 },
        [DRAGON_SYMBOLS.SHIELD]:    { 3: 50,  4: 125,  5: 250 },
        [DRAGON_SYMBOLS.RED_ORB]:   { 3: 20,  4: 50,   5: 100 },
        [DRAGON_SYMBOLS.BLUE_ORB]:  { 3: 10,  4: 25,   5: 50 },
        [DRAGON_SYMBOLS.GREEN_ORB]: { 3: 5,   4: 10,   5: 25 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game
document.addEventListener('DOMContentLoaded', () => {
    updateBalance();
    loadDragonsBreathGame();
});