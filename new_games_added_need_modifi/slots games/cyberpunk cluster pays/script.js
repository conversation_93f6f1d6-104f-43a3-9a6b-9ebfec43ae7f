// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Cyberpunk Cluster Game Implementation ---

// Game constants for Cyberpunk Cluster
const CYBER_SYMBOLS = {
    WILD: '💠',      // Glitching Core
    SCATTER: '📡',   // Satellite
    BONUS: '🔋',     // Battery
    ROBOT: '🤖',
    BRAIN: '🧠',
    EYE: '👁️',
    DISK: '💾',
    GEAR: '⚙️',
    LINK: '🔗',
};

const CYBER_REEL_SYMBOLS = [
    CYBER_SYMBOLS.ROBOT, CYBER_SYMBOLS.BRAIN, CYBER_SYMBOLS.EYE, CYBER_SYMBOLS.DISK,
    CYBER_SYMBOLS.GEAR, CYBER_SYMBOLS.LINK, CYBER_SYMBOLS.WILD, CYBER_SYMBOLS.SCATTER, CYBER_SYMBOLS.BONUS
];

// Define a comprehensive set of paylines for the 5x4 grid.
const CYBER_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes & Chevrons
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    // Diagonals
    [0, 6, 12, 18], [4, 8, 12, 16],
    // Other patterns
    [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4]
];

// Game state object
let cyberClusterGame = {
    isSpinning: false,
    glitchSpins: 0,
    overchargeLevel: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadCyberClusterGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h4 class="text-xl font-bold mb-4 text-gray-400 font-mono">SYSTEM CONTROL</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">COMPUTE UNITS (BET)</label>
                        <input type="number" id="cyberBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-gray-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        INITIATE CLUSTER
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Packet:</span>
                            <span id="cyberLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Glitch Spins:</span>
                            <span id="glitchSpins" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-gray-500/20">
                        <h5 class="text-sm font-bold mb-2 text-gray-300 font-mono">OVERCHARGE METER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="overchargeMeter" class="bg-gradient-to-r from-purple-500 to-cyan-400 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="overchargeStatus" class="text-xs text-center text-gray-400">Charge: 0%</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <div id="cyberReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>
                    
                    <div id="cyberStatus" class="text-center text-lg font-semibold text-cyan-300 mb-4 h-8 font-mono">
                        Awaiting input...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm font-mono">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${CYBER_SYMBOLS.ROBOT} x5:</span><span class="text-cyan-400">200x</span></div>
                                <div class="flex justify-between"><span>${CYBER_SYMBOLS.BRAIN} x5:</span><span class="text-pink-400">100x</span></div>
                                <div class="flex justify-between"><span>${CYBER_SYMBOLS.EYE} x5:</span><span class="text-green-400">50x</span></div>
                                <div class="flex justify-between"><span>${CYBER_SYMBOLS.DISK} x5:</span><span class="text-yellow-400">25x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PROTOCOLS</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">${CYBER_SYMBOLS.WILD} Wild Core:</span> Substitutes all but Scatter.</div>
                                <div><span class="text-purple-400">${CYBER_SYMBOLS.SCATTER} Scatter:</span> 3+ initiates Glitch Spins.</div>
                                <div><span class="text-green-400">${CYBER_SYMBOLS.BONUS} Power:</span> Charges the Overcharge meter for a multiplier.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupCyberClusterGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupCyberClusterGame() {
    document.getElementById('startSpin').addEventListener('click', startCyberSpin);
    const reelsContainer = document.getElementById('cyberReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-purple-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = CYBER_REEL_SYMBOLS[i % CYBER_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateCyberDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateCyberDisplay() {
    const spinButton = document.getElementById('startSpin');
    spinButton.disabled = cyberClusterGame.isSpinning;
    spinButton.textContent = cyberClusterGame.isSpinning ? 'COMPILING...' : 'INITIATE CLUSTER';

    document.getElementById('glitchSpins').textContent = cyberClusterGame.glitchSpins;
    document.getElementById('overchargeMeter').style.width = `${cyberClusterGame.overchargeLevel}%`;
    document.getElementById('overchargeStatus').textContent = `Charge: ${cyberClusterGame.overchargeLevel}%`;
    document.getElementById('cyberLastWin').textContent = `${cyberClusterGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function startCyberSpin() {
    if (cyberClusterGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('cyberBet').value);

    if (cyberClusterGame.glitchSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('cyberStatus').textContent = 'INSUFFICIENT UNITS';
            return;
        }
        balance -= totalBet;
    } else {
        cyberClusterGame.glitchSpins--;
    }

    cyberClusterGame.isSpinning = true;
    cyberClusterGame.lastWin = 0;
    updateBalance();
    updateCyberDisplay();
    document.getElementById('cyberStatus').textContent = 'Compiling data streams...';

    const slots = document.querySelectorAll('#cyberReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = CYBER_REEL_SYMBOLS[Math.floor(Math.random() * CYBER_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishCyberSpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishCyberSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#cyberReels .slot');
    slots.forEach(slot => {
        const symbol = CYBER_REEL_SYMBOLS[Math.floor(Math.random() * CYBER_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkCyberWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and special symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkCyberWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // 1. Check for payline wins
    CYBER_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === CYBER_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== CYBER_SYMBOLS.WILD && symbols[index] !== CYBER_SYMBOLS.SCATTER) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        if (lineSymbol === CYBER_SYMBOLS.SCATTER) return;

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === CYBER_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getCyberMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 25; 
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins (Glitch Spins)
    const scatterCount = symbols.filter(s => s === CYBER_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 8 + (scatterCount - 3) * 2;
        cyberClusterGame.glitchSpins += freeSpinsWon;
        statusMessage = `📡 Glitch Protocol: ${freeSpinsWon} free spins initiated!`;
    }
    
    // 3. Collect Bonus symbols (Power)
    const bonusCount = symbols.filter(s => s === CYBER_SYMBOLS.BONUS).length;
    if (bonusCount > 0) {
        cyberClusterGame.overchargeLevel = Math.min(100, cyberClusterGame.overchargeLevel + bonusCount * 8);
    }

    // 4. Check for Overcharge bonus
    if (cyberClusterGame.overchargeLevel >= 100 && totalWin > 0) {
        const powerMultiplier = Math.floor(Math.random() * 8) + 2; // 2x to 9x multiplier
        totalWin *= powerMultiplier;
        cyberClusterGame.overchargeLevel = 0; // Reset meter
        statusMessage = `🔋 SYSTEM OVERLOAD! Packet multiplied by ${powerMultiplier}x!`;
    }

    // 5. Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        cyberClusterGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('OVERLOAD')) {
            statusMessage = statusMessage ? statusMessage : `Data packet secured: +${totalWin} GA`;
        }

        const slots = document.querySelectorAll('#cyberReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Connection failed. Null pointer.';
    }
    
    document.getElementById('cyberStatus').textContent = statusMessage;
    cyberClusterGame.isSpinning = false;
    updateCyberDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getCyberMultiplier(symbol, count) {
    const multipliers = {
        [CYBER_SYMBOLS.ROBOT]: { 3: 40, 4: 100, 5: 200 },
        [CYBER_SYMBOLS.BRAIN]: { 3: 20, 4: 50,  5: 100 },
        [CYBER_SYMBOLS.EYE]:   { 3: 10, 4: 25,  5: 50 },
        [CYBER_SYMBOLS.DISK]:  { 3: 8,  4: 15,  5: 25 },
        [CYBER_SYMBOLS.GEAR]:  { 3: 5,  4: 10,  5: 20 },
        [CYBER_SYMBOLS.LINK]:  { 3: 2,  4: 5,   5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCyberClusterGame();
});