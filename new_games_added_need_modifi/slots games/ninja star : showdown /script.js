
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Ninja Star Showdown Game Implementation
const NINJA_SYMBOLS = {
    NINJA_MASTER: '🥷',
    SHURIKEN: '⭐',
    KATANA: '⚔️',
    SMOKE_BOMB: '💨',
    DRAGON_SCROLL: '📜',
    KUNAI: '🗡️',
    TEMPLE: '🏯',
    MOON: '🌙',
    WILD: '🌟',
    SCATTER: '🎯'
};

const NINJA_REEL_SYMBOLS = [
    NINJA_SYMBOLS.NINJA_MASTER, NINJA_SYMBOLS.SHURIKEN, NINJA_SYMBOLS.KATANA,
    NINJA_SYMBOLS.SMOKE_BOMB, NINJA_SYMBOLS.DRAGON_SCROLL, NINJA_SYMBOLS.KUNAI,
    NINJA_SYMBOLS.TEMPLE, NINJA_SYMBOLS.MOON, NINJA_SYMBOLS.WILD, NINJA_SYMBOLS.SCATTER
];

let ninjaShowdownGame = {
    isSpinning: false,
    shadowSpins: 0,
    lastWin: 0,
    ninjaLevel: 1,
    stealth: 100,
    honor: 0,
    combo: 0,
    shadowMode: false,
    dragonRage: false,
    perfectBalance: false,
    moonlightBonus: false
};

function loadNinjaShowdownGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400 font-mono">NINJA STAR SHOWDOWN</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">NINJA POWER</label>
                        <input type="number" id="ninjaBet" value="40" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">FIGHTING STYLE</label>
                        <select id="fightingStyle" class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="stealth">Stealth Mode (3x)</option>
                            <option value="combat">Combat Mode (6x)</option>
                            <option value="shadow">Shadow Mode (12x)</option>
                        </select>
                    </div>
                    
                    <button id="startShowdown" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        BEGIN SHOWDOWN
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Shadow Spins:</span>
                            <span id="ninjaShadowSpins" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Victory:</span>
                            <span id="ninjaLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Ninja Level:</span>
                            <span id="ninjaLevel" class="text-yellow-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Honor:</span>
                            <span id="ninjaHonor" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Combo:</span>
                            <span id="ninjaCombo" class="text-orange-400">0x</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300 font-mono">NINJA ARSENAL</h5>
                        <div class="text-xs space-y-1">
                            <div>🥷 <span class="text-red-400">Ninja Master:</span> 500x bet</div>
                            <div>⭐ <span class="text-yellow-400">Shuriken:</span> 300x bet</div>
                            <div>⚔️ <span class="text-gray-400">Katana:</span> 250x bet</div>
                            <div>💨 <span class="text-blue-400">Smoke Bomb:</span> 200x bet</div>
                            <div>📜 <span class="text-purple-400">Dragon Scroll:</span> 180x bet</div>
                            <div>🌟 <span class="text-white">Wild:</span> Substitutes all</div>
                            <div>🎯 <span class="text-red-400">Scatter:</span> Shadow bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div class="relative mb-6">
                        <div id="ninjaReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-red-900/20 rounded-lg flex items-center justify-center text-3xl border border-red-500/20 transition-all duration-300">🥷</div>`
                            ).join('')}
                        </div>
                        <div id="shadowEffect" class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="dragonRageEffect" class="absolute inset-0 bg-gradient-to-b from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="moonlightEffect" class="absolute inset-0 bg-gradient-radial from-blue-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="comboEffect" class="absolute inset-0 bg-gradient-conic from-orange-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                    </div>
                    
                    <div id="ninjaGameStatus" class="text-center text-lg font-semibold text-red-300 mb-4 h-8 font-mono">
                        The dojo awaits your legendary showdown...
                    </div>
                    
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">STEALTH METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="stealthMeter" class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: 100%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="stealthLevel" class="text-blue-400">100%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">SHADOW MODE</h6>
                            <div class="text-center">
                                <span id="shadowStatus" class="text-purple-400">INACTIVE</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DRAGON RAGE</h6>
                            <div class="text-center">
                                <span id="dragonStatus" class="text-red-400">CALM</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">BALANCE</h6>
                            <div class="text-center">
                                <span id="balanceStatus" class="text-yellow-400">SEEKING</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupNinjaShowdownGame();
}

function setupNinjaShowdownGame() {
    document.getElementById('startShowdown').addEventListener('click', startNinjaShowdown);
    const reelsContainer = document.getElementById('ninjaReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-red-900/20 rounded-lg p-2 text-center text-3xl border border-red-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = NINJA_REEL_SYMBOLS[i % NINJA_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateNinjaDisplay();
}

function startNinjaShowdown() {
    if (ninjaShowdownGame.isSpinning) return;

    const bet = parseInt(document.getElementById('ninjaBet').value);
    const style = document.getElementById('fightingStyle').value;
    let totalBet;
    
    switch(style) {
        case 'combat': totalBet = bet * 6; break;
        case 'shadow': totalBet = bet * 12; break;
        default: totalBet = bet * 3;
    }

    if (ninjaShowdownGame.shadowSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('ninjaGameStatus').textContent = 'INSUFFICIENT CHI TO BEGIN SHOWDOWN';
            return;
        }
        balance -= totalBet;
    } else {
        ninjaShowdownGame.shadowSpins--;
    }

    ninjaShowdownGame.isSpinning = true;
    ninjaShowdownGame.lastWin = 0;
    updateBalance();
    updateNinjaDisplay();
    document.getElementById('ninjaGameStatus').textContent = 'Ninja stars fly through the moonlit dojo...';

    const slots = document.querySelectorAll('#ninjaReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'combo-highlight'));

    // Shadow effect during spin
    document.getElementById('shadowEffect').style.opacity = '0.4';

    let spinDuration = 2400;
    let spinInterval = 100;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = NINJA_REEL_SYMBOLS[Math.floor(Math.random() * NINJA_REEL_SYMBOLS.length)];
            slot.style.transform = `rotate(${Math.random() * 360}deg) scale(${0.9 + Math.random() * 0.2})`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('shadowEffect').style.opacity = '0';
            slots.forEach(slot => slot.style.transform = 'rotate(0deg) scale(1)');
            finishNinjaShowdown(totalBet);
        }
    }, spinInterval);
}

function finishNinjaShowdown(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#ninjaReels .slot');
    
    // Enhanced symbol generation with style bonuses
    const style = document.getElementById('fightingStyle').value;
    let wildChance = 0.12;
    let scatterChance = 0.08;
    let masterChance = 0.15;
    
    if (style === 'combat') {
        wildChance = 0.16;
        scatterChance = 0.11;
        masterChance = 0.20;
    } else if (style === 'shadow') {
        wildChance = 0.22;
        scatterChance = 0.15;
        masterChance = 0.25;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = NINJA_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = NINJA_SYMBOLS.SCATTER;
        } else if (Math.random() < masterChance) {
            symbol = NINJA_SYMBOLS.NINJA_MASTER;
        } else {
            symbol = NINJA_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkNinjaWins(finalSymbols, totalBet);
}

function checkNinjaWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (legendary techniques)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== NINJA_SYMBOLS.WILD && symbol !== NINJA_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === NINJA_SYMBOLS.NINJA_MASTER) {
                multiplier = 500;
                statusMessage = 'LEGENDARY NINJA MASTER TECHNIQUE!';
                ninjaShowdownGame.honor += 100;
                ninjaShowdownGame.dragonRage = true;
            } else if (symbol === NINJA_SYMBOLS.SHURIKEN) {
                multiplier = 300;
                statusMessage = 'PERFECT SHURIKEN STORM!';
                ninjaShowdownGame.honor += 75;
            } else if (symbol === NINJA_SYMBOLS.KATANA) {
                multiplier = 250;
                statusMessage = 'FLAWLESS KATANA MASTERY!';
                ninjaShowdownGame.honor += 60;
            } else if (symbol === NINJA_SYMBOLS.SMOKE_BOMB) {
                multiplier = 200;
                statusMessage = 'VANISHING SMOKE TECHNIQUE!';
                ninjaShowdownGame.stealth = Math.min(100, ninjaShowdownGame.stealth + 20);
            } else if (symbol === NINJA_SYMBOLS.DRAGON_SCROLL) {
                multiplier = 180;
                statusMessage = 'ANCIENT DRAGON SCROLL WISDOM!';
                ninjaShowdownGame.ninjaLevel = Math.min(ninjaShowdownGame.ninjaLevel + 1, 10);
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind (advanced techniques)
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== NINJA_SYMBOLS.WILD && symbol !== NINJA_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === NINJA_SYMBOLS.NINJA_MASTER) multiplier = 150;
            else if (symbol === NINJA_SYMBOLS.SHURIKEN) multiplier = 100;
            else if (symbol === NINJA_SYMBOLS.KATANA) multiplier = 80;
            else if (symbol === NINJA_SYMBOLS.SMOKE_BOMB) multiplier = 60;
            else if (symbol === NINJA_SYMBOLS.DRAGON_SCROLL) multiplier = 50;
            else if (symbol === NINJA_SYMBOLS.KUNAI) multiplier = 40;
            else if (symbol === NINJA_SYMBOLS.TEMPLE) multiplier = 35;
            else if (symbol === NINJA_SYMBOLS.MOON) multiplier = 30;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 3-of-a-kind (basic techniques)
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== NINJA_SYMBOLS.WILD && symbol !== NINJA_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === NINJA_SYMBOLS.NINJA_MASTER) multiplier = 50;
            else if (symbol === NINJA_SYMBOLS.SHURIKEN) multiplier = 35;
            else if (symbol === NINJA_SYMBOLS.KATANA) multiplier = 30;
            else if (symbol === NINJA_SYMBOLS.SMOKE_BOMB) multiplier = 25;
            else if (symbol === NINJA_SYMBOLS.DRAGON_SCROLL) multiplier = 20;
            else if (symbol === NINJA_SYMBOLS.KUNAI) multiplier = 15;
            else if (symbol === NINJA_SYMBOLS.TEMPLE) multiplier = 12;
            else if (symbol === NINJA_SYMBOLS.MOON) multiplier = 10;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === NINJA_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.5);
        totalWin *= wildMultiplier;
        statusMessage += ` Shadow clone: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter shadow bonus
    const scatterCount = symbols.filter(s => s === NINJA_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const shadowSpinsAwarded = 12 + (scatterCount - 3) * 6;
        ninjaShowdownGame.shadowSpins += shadowSpinsAwarded;
        ninjaShowdownGame.shadowMode = true;
        statusMessage += ` 🎯 SHADOW REALM! ${shadowSpinsAwarded} shadow spins!`;
        
        // Trigger shadow effect
        document.getElementById('shadowEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('shadowEffect').style.opacity = '0';
            ninjaShowdownGame.shadowMode = false;
        }, 5000);
    }

    // Combo system
    if (totalWin > 0) {
        ninjaShowdownGame.combo++;
        const comboMultiplier = 1 + (ninjaShowdownGame.combo * 0.2);
        totalWin *= comboMultiplier;
        
        // Trigger combo effect
        document.getElementById('comboEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('comboEffect').style.opacity = '0';
        }, 2000);
    } else {
        ninjaShowdownGame.combo = 0;
        ninjaShowdownGame.stealth = Math.max(0, ninjaShowdownGame.stealth - 10);
    }

    // Dragon rage activation
    if (ninjaShowdownGame.dragonRage && ninjaShowdownGame.honor >= 200) {
        const rageMultiplier = 8 + ninjaShowdownGame.ninjaLevel;
        totalWin *= rageMultiplier;
        statusMessage = `🐉 DRAGON'S FURY UNLEASHED! ${rageMultiplier}x DEVASTATION!`;
        ninjaShowdownGame.honor = 0;
        
        // Trigger dragon rage effect
        document.getElementById('dragonRageEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('dragonRageEffect').style.opacity = '0';
            ninjaShowdownGame.dragonRage = false;
        }, 4000);
    }

    // Perfect balance achievement
    if (ninjaShowdownGame.stealth === 100 && ninjaShowdownGame.honor >= 100) {
        ninjaShowdownGame.perfectBalance = true;
        totalWin *= 5;
        statusMessage = '☯️ PERFECT NINJA BALANCE ACHIEVED! 5x HARMONY!';
        ninjaShowdownGame.honor = 50; // Reset but keep some
    }

    // Moonlight bonus (random chance during shadow mode)
    if (ninjaShowdownGame.shadowMode && Math.random() < 0.3) {
        ninjaShowdownGame.moonlightBonus = true;
        totalWin += totalBet * 25;
        statusMessage += ' 🌙 MOONLIGHT BLESSING!';
        
        // Trigger moonlight effect
        document.getElementById('moonlightEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('moonlightEffect').style.opacity = '0';
            ninjaShowdownGame.moonlightBonus = false;
        }, 3000);
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        ninjaShowdownGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#ninjaReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('combo-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The shadows conceal their secrets...';
    }

    document.getElementById('ninjaGameStatus').textContent = statusMessage;
    ninjaShowdownGame.isSpinning = false;
    updateNinjaDisplay();
}

function updateNinjaDisplay() {
    const spinButton = document.getElementById('startShowdown');
    spinButton.disabled = ninjaShowdownGame.isSpinning;
    spinButton.textContent = ninjaShowdownGame.isSpinning ? 'FIGHTING...' : 'BEGIN SHOWDOWN';

    document.getElementById('ninjaShadowSpins').textContent = ninjaShowdownGame.shadowSpins;
    document.getElementById('ninjaLastWin').textContent = `${ninjaShowdownGame.lastWin} GA`;
    document.getElementById('ninjaLevel').textContent = ninjaShowdownGame.ninjaLevel;
    document.getElementById('ninjaHonor').textContent = ninjaShowdownGame.honor;
    document.getElementById('ninjaCombo').textContent = `${ninjaShowdownGame.combo}x`;
    
    document.getElementById('shadowStatus').textContent = ninjaShowdownGame.shadowMode ? 'ACTIVE!' : 'INACTIVE';
    document.getElementById('dragonStatus').textContent = ninjaShowdownGame.dragonRage ? 'RAGING!' : 'CALM';
    document.getElementById('balanceStatus').textContent = ninjaShowdownGame.perfectBalance ? 'ACHIEVED!' : 'SEEKING';
    document.getElementById('stealthLevel').textContent = `${ninjaShowdownGame.stealth}%`;
    
    // Update stealth meter
    document.getElementById('stealthMeter').style.width = `${ninjaShowdownGame.stealth}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadNinjaShowdownGame();
});

