// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Lucky Lunar Spins Game Implementation ---

// Game constants
const LUNAR_SYMBOLS = {
    WILD: '👨‍🚀', // Astronaut is the Wild
    SCATTER: '🌌', // Galaxy is the Scatter
    MOON: '🌙',
    ROCKET: '🚀',
    STAR: '🌟',
    SATELLITE: '🛰️',
    EARTH: '🌍',
    COMET: '☄️',
};

const LUNAR_REEL_SYMBOLS = [
    LUNAR_SYMBOLS.MOON, LUNAR_SYMBOLS.ROCKET, LUNAR_SYMBOLS.STAR,
    LUNAR_SYMBOLS.SATELLITE, LUNAR_SYMBOLS.EARTH, LUNAR_SYMBOLS.COMET,
    LUNAR_SYMBOLS.WILD, LUNAR_SYMBOLS.SCATTER
];

// Define the 5x4 grid of paylines. Each array is a list of indices (0-19).
const LUNAR_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    // Diagonals
    [0, 5, 10, 15], [1, 6, 11, 16], [2, 7, 12, 17], [3, 8, 13, 18], [4, 9, 14, 19],
    [0, 6, 12, 18], [4, 8, 12, 16],
    // W-shapes
    [0, 5, 2, 8, 4], [15, 10, 17, 11, 19],
    // More patterns
    [5, 1, 7, 3, 9], [10, 6, 12, 8, 14]
];

// Game state object
let lunarSpinsGame = {
    isSpinning: false,
    cosmicSpins: 0, // Free spins
    cosmicEnergy: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadLunarSpinsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">LUCKY LUNAR SPINS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">TOTAL BET</label>
                        <input type="number" id="lunarBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="launchToMoon" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        LAUNCH TO MOON
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-blue-300">Last Win:</span>
                            <span id="lunarLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-300">Cosmic Spins:</span>
                            <span id="lunarCosmic" class="text-cyan-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300">COSMIC ENERGY</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="lunarCosmicEnergy" class="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="lunarEnergyStatus" class="text-xs text-center text-gray-400">Collect energy for a win multiplier!</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="lunarReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid will be generated here -->
                    </div>
                    
                    <div id="lunarStatus" class="text-center text-lg font-semibold text-purple-400 mb-4 h-8">
                        Journey to the cyber moon for cosmic fortunes.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${LUNAR_SYMBOLS.MOON} x5:</span><span class="text-purple-300">100x</span></div>
                                <div class="flex justify-between"><span>${LUNAR_SYMBOLS.ROCKET} x5:</span><span class="text-pink-400">50x</span></div>
                                <div class="flex justify-between"><span>${LUNAR_SYMBOLS.STAR} x5:</span><span class="text-cyan-400">25x</span></div>
                                <div class="flex justify-between"><span>${LUNAR_SYMBOLS.SATELLITE} x5:</span><span class="text-gray-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">${LUNAR_SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter.</div>
                                <div><span class="text-pink-400">${LUNAR_SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Cosmic Spins.</div>
                                <div><span class="text-cyan-400">Energy:</span> Fills to grant a win multiplier.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupLunarSpinsGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupLunarSpinsGame() {
    document.getElementById('launchToMoon').addEventListener('click', launchLunarMission);
    const reelsContainer = document.getElementById('lunarReels');
    reelsContainer.innerHTML = ''; // Clear previous content
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-purple-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = LUNAR_REEL_SYMBOLS[i % LUNAR_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateLunarDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateLunarDisplay() {
    const spinButton = document.getElementById('launchToMoon');
    spinButton.disabled = lunarSpinsGame.isSpinning;
    spinButton.textContent = lunarSpinsGame.isSpinning ? 'IN ORBIT...' : 'LAUNCH TO MOON';

    document.getElementById('lunarCosmic').textContent = lunarSpinsGame.cosmicSpins;
    document.getElementById('lunarCosmicEnergy').style.width = `${lunarSpinsGame.cosmicEnergy}%`;
    document.getElementById('lunarLastWin').textContent = `${lunarSpinsGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function launchLunarMission() {
    if (lunarSpinsGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('lunarBet').value);

    if (lunarSpinsGame.cosmicSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('lunarStatus').textContent = 'Insufficient rocket fuel!';
            return;
        }
        balance -= totalBet;
    } else {
        lunarSpinsGame.cosmicSpins--;
    }

    lunarSpinsGame.isSpinning = true;
    lunarSpinsGame.lastWin = 0;
    updateBalance();
    updateLunarDisplay();
    document.getElementById('lunarStatus').textContent = 'Exploring the cosmos...';

    const slots = document.querySelectorAll('#lunarReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = LUNAR_REEL_SYMBOLS[Math.floor(Math.random() * LUNAR_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishLunarSpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishLunarSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#lunarReels .slot');
    slots.forEach(slot => {
        const symbol = LUNAR_REEL_SYMBOLS[Math.floor(Math.random() * LUNAR_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkLunarWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and scatter symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkLunarWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();

    // 1. Check for payline wins
    LUNAR_PAYLINES.forEach(line => {
        let firstSymbolOnLine = symbols[line[0]];
        let lineSymbol = firstSymbolOnLine;
        
        // If the first symbol is WILD, determine the line's symbol from the next non-wild symbol
        if (lineSymbol === LUNAR_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== LUNAR_SYMBOLS.WILD) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === LUNAR_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break; // Chain is broken
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getLunarMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                // Bet per line is total bet divided by a standard number of lines (e.g., 20)
                const lineBet = totalBet / 20; 
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins
    const scatterCount = symbols.filter(s => s === LUNAR_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 5 + (scatterCount - 3) * 5;
        lunarSpinsGame.cosmicSpins += freeSpinsWon;
        document.getElementById('lunarStatus').textContent = `Galaxy Portal! Won ${freeSpinsWon} Cosmic Spins!`;
    }

    // 3. Apply Cosmic Energy multiplier
    let energyMultiplier = 1;
    if (lunarSpinsGame.cosmicEnergy >= 100) {
        energyMultiplier = Math.floor(Math.random() * 4) + 2; // 2x to 5x multiplier
        totalWin *= energyMultiplier;
        lunarSpinsGame.cosmicEnergy = 0; // Reset energy
        document.getElementById('lunarStatus').textContent = `COSMIC SURGE! ${energyMultiplier}x WIN MULTIPLIER!`;
    } else {
         lunarSpinsGame.cosmicEnergy = Math.min(100, lunarSpinsGame.cosmicEnergy + Math.floor(Math.random() * 5) + 1);
    }

    // 4. Process results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        lunarSpinsGame.lastWin = totalWin;
        updateBalance();
        
        if (!document.getElementById('lunarStatus').textContent.includes('COSMIC SURGE')) {
             document.getElementById('lunarStatus').textContent = `Cosmic treasure found! You won ${totalWin} GA!`;
        }

        const slots = document.querySelectorAll('#lunarReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (scatterCount < 3) {
        document.getElementById('lunarStatus').textContent = 'The cosmos remains quiet...';
    }
    
    lunarSpinsGame.isSpinning = false;
    updateLunarDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getLunarMultiplier(symbol, count) {
    const multipliers = {
        [LUNAR_SYMBOLS.MOON]:      { 3: 20, 4: 50, 5: 100 },
        [LUNAR_SYMBOLS.ROCKET]:    { 3: 10, 4: 25, 5: 50 },
        [LUNAR_SYMBOLS.STAR]:      { 3: 8,  4: 20, 5: 25 },
        [LUNAR_SYMBOLS.SATELLITE]: { 3: 5,  4: 10, 5: 15 },
        [LUNAR_SYMBOLS.EARTH]:     { 3: 3,  4: 8,  5: 12 },
        [LUNAR_SYMBOLS.COMET]:     { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadLunarSpinsGame();
});
