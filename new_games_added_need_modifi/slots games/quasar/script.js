// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadQuasarGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                            <h4 class="text-xl font-bold mb-4 text-indigo-400">QUANTUM QUASAR MEGAWAYS</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="quasarBet" value="20" min="5" max="${balance}" 
                                       class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">QUANTUM MODE</label>
                                <select id="quasarMode" class="w-full bg-black/50 border border-indigo-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="standard">Standard (1x)</option>
                                    <option value="turbo">Turbo (1.5x)</option>
                                    <option value="quantum">Quantum (2x)</option>
                                </select>
                            </div>
                            
                            <button id="spinQuasar" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                SPIN MEGAWAYS
                            </button>
                            
                            <div class="text-center mb-4">
                                <div class="text-sm text-gray-400 mb-1">Ways to Win</div>
                                <div id="quasarWays" class="text-xl font-bold text-indigo-400 neon-glow">324</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Win Multiplier</div>
                                <div id="quasarMultiplier" class="text-2xl font-bold text-purple-400">1x</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Total Win</div>
                                <div id="quasarWin" class="text-xl font-bold text-yellow-400">$0</div>
                            </div>
                            
                            <div class="bg-black/30 p-3 rounded-lg border border-indigo-500/30">
                                <h5 class="text-sm font-bold mb-2 text-indigo-400">QUANTUM FEATURES</h5>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Free Spins:</span>
                                        <span id="quasarFreeSpins" class="text-indigo-400">0</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Cascade Level:</span>
                                        <span id="quasarCascade" class="text-purple-400">0</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Quantum Bonus:</span>
                                        <span id="quasarBonus" class="text-cyan-400">Ready</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Megaways Reels -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                            <div id="quasarReels" class="relative bg-gradient-to-br from-indigo-900 to-purple-900 rounded-lg p-4 h-96 overflow-hidden">
                                <!-- Dynamic Megaways Grid -->
                                <div class="grid grid-cols-6 gap-1 h-full">
                                    <div id="reel0" class="flex flex-col space-y-1"></div>
                                    <div id="reel1" class="flex flex-col space-y-1"></div>
                                    <div id="reel2" class="flex flex-col space-y-1"></div>
                                    <div id="reel3" class="flex flex-col space-y-1"></div>
                                    <div id="reel4" class="flex flex-col space-y-1"></div>
                                    <div id="reel5" class="flex flex-col space-y-1"></div>
                                </div>
                                
                                <!-- Quantum Effects Overlay -->
                                <div id="quantumEffects" class="absolute inset-0 pointer-events-none"></div>
                            </div>
                            <div id="quasarStatus" class="text-center mt-4 text-lg font-semibold">Prepare for quantum megaways experience</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeQuasar();
        }
        
        let quasarGame = {
            isSpinning: false,
            mode: 'standard',
            betAmount: 20,
            reelHeights: [2, 3, 4, 4, 3, 2], // Variable reel heights
            symbols: ['🌟', '💎', '🔮', '⚡', '🌌', '💫', '🌠', '⭐', '🔯', 'W'],
            reels: [[], [], [], [], [], []],
            waysToWin: 0,
            multiplier: 1,
            cascadeLevel: 0,
            freeSpins: 0,
            inFreeSpins: false
        };
        
        function initializeQuasar() {
            document.getElementById('spinQuasar').addEventListener('click', spinQuasar);
            document.getElementById('quasarMode').addEventListener('change', updateQuasarMode);
            
            generateInitialQuasarReels();
            updateQuasarMode();
        }
        
        function updateQuasarMode() {
            quasarGame.mode = document.getElementById('quasarMode').value;
        }
        
        function generateInitialQuasarReels() {
            // Generate random reel heights (2-7 symbols per reel)
            quasarGame.reelHeights = [
                Math.floor(Math.random() * 6) + 2,
                Math.floor(Math.random() * 6) + 2,
                Math.floor(Math.random() * 6) + 2,
                Math.floor(Math.random() * 6) + 2,
                Math.floor(Math.random() * 6) + 2,
                Math.floor(Math.random() * 6) + 2
            ];
            
            // Fill reels with random symbols
            for (let reel = 0; reel < 6; reel++) {
                quasarGame.reels[reel] = [];
                for (let pos = 0; pos < quasarGame.reelHeights[reel]; pos++) {
                    const symbol = quasarGame.symbols[Math.floor(Math.random() * quasarGame.symbols.length)];
                    quasarGame.reels[reel].push(symbol);
                }
            }
            
            calculateQuasarWays();
            displayQuasarReels();
        }
        
        function calculateQuasarWays() {
            quasarGame.waysToWin = quasarGame.reelHeights.reduce((total, height) => total * height, 1);
            document.getElementById('quasarWays').textContent = quasarGame.waysToWin.toLocaleString();
        }
        
        function displayQuasarReels() {
            for (let reel = 0; reel < 6; reel++) {
                const reelElement = document.getElementById(`reel${reel}`);
                reelElement.innerHTML = '';
                
                quasarGame.reels[reel].forEach((symbol, pos) => {
                    const symbolElement = document.createElement('div');
                    symbolElement.className = 'bg-black/40 border border-indigo-400/30 rounded flex items-center justify-center text-2xl font-bold transition-all duration-300 hover:scale-105';
                    symbolElement.style.height = `${100 / quasarGame.reelHeights[reel]}%`;
                    symbolElement.textContent = symbol;
                    
                    if (symbol === 'W') {
                        symbolElement.classList.add('bg-gradient-to-br', 'from-yellow-500', 'to-orange-500', 'text-white');
                    }
                    
                    reelElement.appendChild(symbolElement);
                });
            }
        }
        
        function spinQuasar() {
            if (quasarGame.isSpinning) return;
            
            const totalBet = quasarGame.betAmount * getModeMultiplier();
            
            if (totalBet > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            if (quasarGame.freeSpins === 0) {
                balance -= totalBet;
                updateBalance();
            } else {
                quasarGame.freeSpins--;
                document.getElementById('quasarFreeSpins').textContent = quasarGame.freeSpins;
            }
            
            quasarGame.isSpinning = true;
            document.getElementById('spinQuasar').disabled = true;
            document.getElementById('quasarStatus').textContent = 'Quantum reels spinning...';
            
            // Animate spin
            animateQuasarSpin().then(() => {
                checkQuasarWins();
            });
        }
        
        function getModeMultiplier() {
            const multipliers = { 'standard': 1, 'turbo': 1.5, 'quantum': 2 };
            return multipliers[quasarGame.mode];
        }
        
        function animateQuasarSpin() {
            return new Promise((resolve) => {
                // Generate new reel configuration
                quasarGame.reelHeights = [
                    Math.floor(Math.random() * 6) + 2,
                    Math.floor(Math.random() * 6) + 2,
                    Math.floor(Math.random() * 6) + 2,
                    Math.floor(Math.random() * 6) + 2,
                    Math.floor(Math.random() * 6) + 2,
                    Math.floor(Math.random() * 6) + 2
                ];
                
                // Add quantum effects
                createQuantumEffects();
                
                let reelDelay = 0;
                for (let reel = 0; reel < 6; reel++) {
                    setTimeout(() => {
                        // Generate new symbols for this reel
                        quasarGame.reels[reel] = [];
                        for (let pos = 0; pos < quasarGame.reelHeights[reel]; pos++) {
                            let symbol;
                            // Increase wild chance in quantum mode
                            if (quasarGame.mode === 'quantum' && Math.random() < 0.15) {
                                symbol = 'W';
                            } else if (Math.random() < 0.05) {
                                symbol = 'W';
                            } else {
                                symbol = quasarGame.symbols[Math.floor(Math.random() * (quasarGame.symbols.length - 1))];
                            }
                            quasarGame.reels[reel].push(symbol);
                        }
                        
                        displayQuasarReels();
                        
                        if (reel === 5) {
                            setTimeout(() => {
                                calculateQuasarWays();
                                resolve();
                            }, 300);
                        }
                    }, reelDelay);
                    reelDelay += 200;
                }
            });
        }
        
        function createQuantumEffects() {
            const effectsContainer = document.getElementById('quantumEffects');
            effectsContainer.innerHTML = '';
            
            // Create sparkle effects
            for (let i = 0; i < 20; i++) {
                const sparkle = document.createElement('div');
                sparkle.className = 'absolute w-2 h-2 bg-white rounded-full opacity-0 animate-pulse';
                sparkle.style.left = Math.random() * 100 + '%';
                sparkle.style.top = Math.random() * 100 + '%';
                sparkle.style.animationDelay = Math.random() * 2 + 's';
                effectsContainer.appendChild(sparkle);
                
                setTimeout(() => {
                    sparkle.style.opacity = '1';
                    setTimeout(() => {
                        sparkle.style.opacity = '0';
                    }, 1000);
                }, Math.random() * 1000);
            }
        }
        
        function checkQuasarWins() {
            const wins = findQuasarWins();
            const totalWin = calculateQuasarPayout(wins);
            
            if (wins.length > 0) {
                highlightQuasarWins(wins);
                quasarGame.cascadeLevel++;
                
                // Update multiplier based on cascade
                quasarGame.multiplier = 1 + (quasarGame.cascadeLevel * 0.5);
                document.getElementById('quasarMultiplier').textContent = quasarGame.multiplier.toFixed(1) + 'x';
                document.getElementById('quasarCascade').textContent = quasarGame.cascadeLevel;
                
                const finalWin = Math.floor(totalWin * quasarGame.multiplier);
                document.getElementById('quasarWin').textContent = '$' + finalWin;
                
                balance += finalWin;
                updateBalance();
                
                document.getElementById('quasarStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Megaways Win! ${wins.length} combinations - $${finalWin}</span>`;
                
                // Cascade after delay
                setTimeout(() => {
                    cascadeQuasarSymbols(wins);
                }, 2000);
            } else {
                // Check for free spins
                if (checkQuasarFreeSpins()) {
                    return;
                }
                
                endQuasarSpin();
            }
        }
        
        function findQuasarWins() {
            const wins = [];
            
            // Check for winning combinations (3+ matching symbols from left to right)
            for (let symbol of quasarGame.symbols) {
                if (symbol === 'W') continue; // Skip wilds for base check
                
                const positions = [];
                for (let reel = 0; reel < 6; reel++) {
                    const reelPositions = [];
                    quasarGame.reels[reel].forEach((reelSymbol, pos) => {
                        if (reelSymbol === symbol || reelSymbol === 'W') {
                            reelPositions.push(pos);
                        }
                    });
                    
                    if (reelPositions.length > 0) {
                        positions.push(reelPositions);
                    } else {
                        break; // Must be consecutive from left
                    }
                }
                
                if (positions.length >= 3) {
                    wins.push({ symbol, reels: positions.length, positions });
                }
            }
            
            return wins;
        }
        
        function calculateQuasarPayout(wins) {
            const payouts = {
                '🌟': { 3: 5, 4: 15, 5: 50, 6: 200 },
                '💎': { 3: 8, 4: 25, 5: 80, 6: 300 },
                '🔮': { 3: 10, 4: 30, 5: 100, 6: 400 },
                '⚡': { 3: 12, 4: 40, 5: 120, 6: 500 },
                '🌌': { 3: 15, 4: 50, 5: 150, 6: 600 },
                '💫': { 3: 3, 4: 10, 5: 25, 6: 100 },
                '🌠': { 3: 3, 4: 10, 5: 25, 6: 100 },
                '⭐': { 3: 2, 4: 8, 5: 20, 6: 80 },
                '🔯': { 3: 2, 4: 8, 5: 20, 6: 80 }
            };
            
            let totalPayout = 0;
            wins.forEach(win => {
                const symbolPayout = payouts[win.symbol];
                if (symbolPayout && symbolPayout[win.reels]) {
                    totalPayout += symbolPayout[win.reels] * quasarGame.betAmount * getModeMultiplier();
                }
            });
            
            return totalPayout;
        }
        
        function highlightQuasarWins(wins) {
            // Reset highlights
            document.querySelectorAll('#quasarReels .bg-green-400').forEach(el => {
                el.classList.remove('bg-green-400', 'ring-2', 'ring-yellow-400');
            });
            
            // Highlight winning symbols
            wins.forEach(win => {
                win.positions.forEach((reelPositions, reelIndex) => {
                    reelPositions.forEach(pos => {
                        const reelElement = document.getElementById(`reel${reelIndex}`);
                        const symbolElement = reelElement.children[pos];
                        symbolElement.classList.add('bg-green-400', 'ring-2', 'ring-yellow-400');
                    });
                });
            });
        }
        
        function cascadeQuasarSymbols(wins) {
            // Remove winning symbols and drop remaining symbols
            wins.forEach(win => {
                win.positions.forEach((reelPositions, reelIndex) => {
                    // Remove symbols from bottom to top
                    const sortedPositions = [...reelPositions].sort((a, b) => b - a);
                    sortedPositions.forEach(pos => {
                        quasarGame.reels[reelIndex].splice(pos, 1);
                    });
                });
            });
            
            // Fill empty spaces with new symbols
            for (let reel = 0; reel < 6; reel++) {
                while (quasarGame.reels[reel].length < quasarGame.reelHeights[reel]) {
                    let symbol;
                    if (quasarGame.mode === 'quantum' && Math.random() < 0.1) {
                        symbol = 'W';
                    } else {
                        symbol = quasarGame.symbols[Math.floor(Math.random() * (quasarGame.symbols.length - 1))];
                    }
                    quasarGame.reels[reel].unshift(symbol);
                }
            }
            
            displayQuasarReels();
            
            setTimeout(() => {
                checkQuasarWins();
            }, 1000);
        }
        
        function checkQuasarFreeSpins() {
            // Count scatter symbols (🌌)
            let scatterCount = 0;
            quasarGame.reels.forEach(reel => {
                reel.forEach(symbol => {
                    if (symbol === '🌌') scatterCount++;
                });
            });
            
            if (scatterCount >= 3) {
                const freeSpinsAwarded = 10 + (scatterCount - 3) * 5;
                quasarGame.freeSpins += freeSpinsAwarded;
                quasarGame.inFreeSpins = true;
                
                document.getElementById('quasarFreeSpins').textContent = quasarGame.freeSpins;
                document.getElementById('quasarStatus').innerHTML = 
                    `<span class="text-purple-400 neon-glow">FREE SPINS! ${freeSpinsAwarded} spins awarded!</span>`;
                
                setTimeout(() => {
                    endQuasarSpin();
                }, 3000);
                return true;
            }
            
            return false;
        }
        
        function endQuasarSpin() {
            quasarGame.isSpinning = false;
            quasarGame.cascadeLevel = 0;
            quasarGame.multiplier = 1;
            
            document.getElementById('spinQuasar').disabled = false;
            document.getElementById('quasarMultiplier').textContent = '1x';
            document.getElementById('quasarCascade').textContent = '0';
            
            if (quasarGame.freeSpins > 0) {
                document.getElementById('quasarStatus').textContent = `Free spins remaining: ${quasarGame.freeSpins}`;
            } else {
                document.getElementById('quasarStatus').textContent = 'Prepare for quantum megaways experience';
                quasarGame.inFreeSpins = false;
            }
            
            // Clear quantum effects
            document.getElementById('quantumEffects').innerHTML = '';
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadQuasarGame();
});