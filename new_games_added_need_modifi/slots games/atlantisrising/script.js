// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Atlantis Rising Game Implementation
const ATLANTIS_SYMBOLS = ['🔱', '🐚', '🐙', '🦈', '🐠', '💎', '⚓', '🌊', '🏛️', 'A'];

let atlantisRisingGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    depthLevel: 1,
    tidalWave: false,
    displayedSymbols: [[], [], [], [], []],
    atlantisRising: false
};

function loadAtlantisRisingGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">ATLANTIS RISING</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">DIVE INVESTMENT</label>
                        <input type="number" id="atlantisBet" value="25" min="5" max="1000"
                               class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startAtlantisSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        DIVE TO ATLANTIS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Dives:</span>
                            <span id="atlantisFreeSpins" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Discovery:</span>
                            <span id="atlantisLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Ocean Depth:</span>
                            <span id="depthLevel" class="text-cyan-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">City Status:</span>
                            <span id="cityStatus" class="text-purple-400">Sunken</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/20">
                        <h5 class="text-sm font-bold mb-2 text-blue-300">ATLANTEAN TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div> Trident: 60x bet</div>
                            <div> Temple: 45x bet</div>
                            <div> Kraken: 35x bet</div>
                            <div> Shark: 25x bet</div>
                            <div> Crystal: 20x bet</div>
                            <div> Atlantis: Scatter bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <div class="relative mb-6">
                        <div id="atlantisReels" class="grid grid-cols-5 gap-4 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-blue-900/20 rounded-lg flex items-center justify-center text-4xl border border-blue-500/20 transition-all duration-300"> Trident</div>`
                            ).join('')}
                        </div>
                        <div id="tidalEffect" class="absolute inset-0 bg-gradient-to-t from-blue-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                    </div>
                    <div id="atlantisStatus" class="text-center text-lg font-semibold text-blue-300 h-8">
                        Descend to the lost city of Atlantis!
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupAtlantisRisingGame();
}

function setupAtlantisRisingGame() {
    document.getElementById('startAtlantisSpin').addEventListener('click', startAtlantisSpin);
    updateAtlantisDisplay();
}

function startAtlantisSpin() {
    if (atlantisRisingGame.isSpinning) return;

    const betAmount = parseInt(document.getElementById('atlantisBet').value);

    if (atlantisRisingGame.freeSpins === 0) {
        if (balance < betAmount) {
            document.getElementById('atlantisStatus').textContent = 'Insufficient funds for deep dive!';
            return;
        }
        balance -= betAmount;
    } else {
        atlantisRisingGame.freeSpins--;
    }

    atlantisRisingGame.isSpinning = true;
    atlantisRisingGame.lastWin = 0;
    updateBalance();
    updateAtlantisDisplay();
    document.getElementById('atlantisStatus').textContent = 'Diving to the ocean depths...';

    const slots = document.querySelectorAll('#atlantisReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Animate reels with staggered timing
    let currentReel = 0;
    const spinInterval = setInterval(() => {
        if (currentReel < 5) {
            const reelSlots = Array.from(slots).slice(currentReel * 4, (currentReel + 1) * 4);
            
            // Spin this reel for 1 second
            const reelSpinInterval = setInterval(() => {
                reelSlots.forEach(slot => {
                    slot.textContent = ATLANTIS_SYMBOLS[Math.floor(Math.random() * ATLANTIS_SYMBOLS.length)];
                });
            }, 50);

            setTimeout(() => {
                clearInterval(reelSpinInterval);
                
                // Set final symbols for this reel
                const symbolsForReel = [];
                reelSlots.forEach(slot => {
                    const symbol = ATLANTIS_SYMBOLS[Math.floor(Math.random() * ATLANTIS_SYMBOLS.length)];
                    symbolsForReel.push(symbol);
                    slot.textContent = symbol;
                });
                
                atlantisRisingGame.displayedSymbols[currentReel] = symbolsForReel;

                currentReel++;
                if (currentReel >= 5) {
                    clearInterval(spinInterval);
                    setTimeout(processAtlantisResults, 500);
                }
            }, 1000 + (currentReel * 200));
        }
    }, 300);
}

function processAtlantisResults() {
    const displayed = atlantisRisingGame.displayedSymbols;
    let totalWin = 0;
    let scatterCount = 0;
    const winningPositions = new Set();
    let statusMessage = '';

    // Flatten the 2D array to 1D for easier processing
    const allSymbols = displayed.flat();

    // Count symbol occurrences
    const symbolCounts = {};
    allSymbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    const betAmount = parseInt(document.getElementById('atlantisBet').value);

    // Check for Atlantean treasures
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === ' Trident' && count >= 3) {
            multiplier = count >= 5 ? 60 : count >= 4 ? 35 : 18;
            statusMessage = 'POSEIDON\'S TRIDENT DISCOVERED!';
            atlantisRisingGame.atlantisRising = true;
        } else if (symbol === ' Temple' && count >= 3) {
            multiplier = count >= 5 ? 45 : count >= 4 ? 28 : 15;
            statusMessage = 'Ancient Atlantean temple found!';
        } else if (symbol === ' Kraken' && count >= 3) {
            multiplier = count >= 5 ? 35 : count >= 4 ? 22 : 12;
            statusMessage = 'Guardian kraken yields its treasure!';
        } else if (symbol === ' Shark' && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 16 : 8;
            statusMessage = 'Shark-guarded treasures claimed!';
        } else if (symbol === ' Crystal' && count >= 3) {
            multiplier = count >= 5 ? 20 : count >= 4 ? 12 : 6;
            statusMessage = 'Atlantean crystals illuminate the depths!';
        }

        if (multiplier > 0) {
            totalWin += betAmount * multiplier;
            allSymbols.forEach((s, i) => {
                if (s === symbol) winningPositions.add(i);
            });
        }
    }

    // Check for Atlantis scatter (A symbols)
    scatterCount = allSymbols.filter(s => s === ' Atlantis').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 15 + (scatterCount - 3) * 8;
        atlantisRisingGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` ATLANTIS RISES! ${freeSpinsAwarded} free dives granted!`;
        atlantisRisingGame.depthLevel++;
        
        // Trigger tidal wave effect
        document.getElementById('tidalEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('tidalEffect').style.opacity = '0';
        }, 2000);
    }

    // Atlantis Rising bonus
    if (atlantisRisingGame.atlantisRising && totalWin > 0) {
        totalWin *= 2;
        statusMessage += ' THE CITY RISES - ALL WINS DOUBLED!';
        atlantisRisingGame.atlantisRising = false;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        atlantisRisingGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#atlantisReels .slot');
        winningPositions.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The ocean depths keep their secrets.';
    }

    document.getElementById('atlantisStatus').textContent = statusMessage;
    atlantisRisingGame.isSpinning = false;
    updateAtlantisDisplay();
}

function updateAtlantisDisplay() {
    document.getElementById('atlantisFreeSpins').textContent = atlantisRisingGame.freeSpins;
    document.getElementById('atlantisLastWin').textContent = `${atlantisRisingGame.lastWin} GA`;
    document.getElementById('depthLevel').textContent = atlantisRisingGame.depthLevel;
    document.getElementById('cityStatus').textContent = atlantisRisingGame.atlantisRising ? 'RISING!' : 'Sunken';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadAtlantisRisingGame();
});
