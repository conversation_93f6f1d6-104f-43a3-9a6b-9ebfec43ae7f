
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Neon Nexus Megaways Game Implementation
const NEON_SYMBOLS = {
    CYBER_SKULL: '💀',
    NEON_HEART: '💖',
    DIGITAL_BRAIN: '🧠',
    CYBER_EYE: '👁️',
    NEON_DIAMOND: '💎',
    CIRCUIT_BOARD: '🔌',
    LASER_BEAM: '🔫',
    HOLOGRAM: '👾',
    WILD: '⚡',
    SCATTER: '🌐'
};

const NEON_REEL_SYMBOLS = [
    NEON_SYMBOLS.CYBER_SKULL, NEON_SYMBOLS.NEON_HEART, NEON_SYMBOLS.DIGITAL_BRAIN,
    NEON_SYMBOLS.CYBER_EYE, NEON_SYMBOLS.NEON_DIAMOND, NEON_SYMBOLS.CIRCUIT_BOARD,
    NEON_SYMBOLS.LASER_BEAM, NEON_SYMBOLS.HOLOGRAM, NEON_SYMBOLS.WILD, NEON_SYMBOLS.SCATTER
];

let neonNexusGame = {
    isSpinning: false,
    megaSpins: 0,
    lastWin: 0,
    neonCharge: 0,
    hackLevel: 1,
    megaways: 117649, // Max megaways (7^6)
    currentMegaways: 0,
    cascadeMultiplier: 1,
    systemOverload: false,
    matrixMode: false,
    cyberpunkRage: false
};

function loadNeonNexusGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400 font-mono">NEON NEXUS MEGAWAYS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">HACK POWER</label>
                        <input type="number" id="neonBet" value="60" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">CYBER PROTOCOL</label>
                        <select id="cyberProtocol" class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Protocol (5x)</option>
                            <option value="advanced">Advanced Protocol (10x)</option>
                            <option value="quantum">Quantum Protocol (20x)</option>
                        </select>
                    </div>
                    
                    <button id="hackMatrix" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        HACK THE MATRIX
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Mega Spins:</span>
                            <span id="neonMegaSpins" class="text-cyan-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Hack:</span>
                            <span id="neonLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Neon Charge:</span>
                            <span id="neonCharge" class="text-pink-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Hack Level:</span>
                            <span id="hackLevel" class="text-purple-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Megaways:</span>
                            <span id="currentMegaways" class="text-yellow-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-cyan-900/20 rounded-lg border border-cyan-500/20">
                        <h5 class="text-sm font-bold mb-2 text-cyan-300 font-mono">CYBER PAYOUTS</h5>
                        <div class="text-xs space-y-1">
                            <div>💀 <span class="text-red-400">Cyber Skull:</span> 400x bet</div>
                            <div>💖 <span class="text-pink-400">Neon Heart:</span> 300x bet</div>
                            <div>🧠 <span class="text-purple-400">Digital Brain:</span> 250x bet</div>
                            <div>👁️ <span class="text-blue-400">Cyber Eye:</span> 200x bet</div>
                            <div>💎 <span class="text-white">Neon Diamond:</span> 180x bet</div>
                            <div>⚡ <span class="text-yellow-400">Wild:</span> Substitutes all</div>
                            <div>🌐 <span class="text-cyan-400">Scatter:</span> Matrix bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div class="relative mb-6">
                        <div id="neonReels" class="grid grid-cols-6 gap-1 h-96">
                            ${Array(42).fill(0).map((_, i) => 
                                `<div class="slot bg-cyan-900/20 rounded-lg flex items-center justify-center text-2xl border border-cyan-500/20 transition-all duration-300">💀</div>`
                            ).join('')}
                        </div>
                        <div id="matrixEffect" class="absolute inset-0 bg-gradient-to-t from-green-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="overloadEffect" class="absolute inset-0 bg-gradient-to-b from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="cascadeEffect" class="absolute inset-0 bg-gradient-radial from-cyan-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="quantumEffect" class="absolute inset-0 bg-gradient-conic from-purple-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                    </div>
                    
                    <div id="neonGameStatus" class="text-center text-lg font-semibold text-cyan-300 mb-4 h-8 font-mono">
                        The digital nexus awaits your infiltration...
                    </div>
                    
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">SYSTEM STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Overload:</span>
                                    <span id="overloadStatus" class="text-red-400">STABLE</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Matrix:</span>
                                    <span id="matrixStatus" class="text-green-400">OFFLINE</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CHARGE METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="chargeMeter" class="bg-gradient-to-r from-cyan-500 to-pink-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CASCADE</h6>
                            <div class="text-center">
                                <span id="cascadeMultiplier" class="text-2xl text-cyan-400">1x</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CYBERPUNK</h6>
                            <div class="text-center">
                                <span id="cyberpunkStatus" class="text-purple-400">CALM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupNeonNexusGame();
}

function setupNeonNexusGame() {
    document.getElementById('hackMatrix').addEventListener('click', hackMatrix);
    generateMegawaysGrid();
    updateNeonDisplay();
}

function generateMegawaysGrid() {
    const reelsContainer = document.getElementById('neonReels');
    reelsContainer.innerHTML = '';
    
    // Generate variable reel heights (2-7 symbols per reel)
    const reelHeights = [];
    let totalWays = 1;
    
    for (let reel = 0; reel < 6; reel++) {
        const height = Math.floor(Math.random() * 6) + 2; // 2-7 symbols
        reelHeights.push(height);
        totalWays *= height;
    }
    
    neonNexusGame.currentMegaways = totalWays;
    
    // Create grid with variable heights
    let slotIndex = 0;
    for (let reel = 0; reel < 6; reel++) {
        for (let row = 0; row < 7; row++) {
            const slot = document.createElement('div');
            if (row < reelHeights[reel]) {
                slot.className = 'slot bg-cyan-900/20 rounded-lg p-1 text-center text-xl border border-cyan-500/20 flex items-center justify-center h-12 transition-all duration-300';
                slot.textContent = NEON_REEL_SYMBOLS[slotIndex % NEON_REEL_SYMBOLS.length];
                slot.style.gridColumn = reel + 1;
                slot.style.gridRow = row + 1;
            } else {
                slot.className = 'invisible';
            }
            reelsContainer.appendChild(slot);
            slotIndex++;
        }
    }
}

function hackMatrix() {
    if (neonNexusGame.isSpinning) return;

    const bet = parseInt(document.getElementById('neonBet').value);
    const protocol = document.getElementById('cyberProtocol').value;
    let totalBet;
    
    switch(protocol) {
        case 'advanced': totalBet = bet * 10; break;
        case 'quantum': totalBet = bet * 20; break;
        default: totalBet = bet * 5;
    }

    if (neonNexusGame.megaSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('neonGameStatus').textContent = 'INSUFFICIENT CREDITS TO HACK MATRIX';
            return;
        }
        balance -= totalBet;
    } else {
        neonNexusGame.megaSpins--;
    }

    neonNexusGame.isSpinning = true;
    neonNexusGame.lastWin = 0;
    neonNexusGame.cascadeMultiplier = 1;
    updateBalance();
    updateNeonDisplay();
    document.getElementById('neonGameStatus').textContent = 'Infiltrating the neon nexus matrix...';

    const slots = document.querySelectorAll('#neonReels .slot:not(.invisible)');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'cascade-highlight'));

    // Quantum effect during spin
    document.getElementById('quantumEffect').style.opacity = '0.3';

    let spinDuration = 3200;
    let spinInterval = 140;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = NEON_REEL_SYMBOLS[Math.floor(Math.random() * NEON_REEL_SYMBOLS.length)];
            slot.style.boxShadow = `0 0 ${Math.random() * 20}px cyan`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('quantumEffect').style.opacity = '0';
            slots.forEach(slot => slot.style.boxShadow = '');
            finishMatrixHack(totalBet);
        }
    }, spinInterval);
}

function finishMatrixHack(totalBet) {
    generateMegawaysGrid(); // Regenerate with new megaways
    
    const finalSymbols = [];
    const slots = document.querySelectorAll('#neonReels .slot:not(.invisible)');
    
    // Enhanced symbol generation with protocol bonuses
    const protocol = document.getElementById('cyberProtocol').value;
    let wildChance = 0.14;
    let scatterChance = 0.09;
    let premiumChance = 0.25;
    
    if (protocol === 'advanced') {
        wildChance = 0.18;
        scatterChance = 0.12;
        premiumChance = 0.35;
    } else if (protocol === 'quantum') {
        wildChance = 0.25;
        scatterChance = 0.16;
        premiumChance = 0.45;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = NEON_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = NEON_SYMBOLS.SCATTER;
        } else if (Math.random() < premiumChance) {
            // Premium symbols
            const premiums = [NEON_SYMBOLS.CYBER_SKULL, NEON_SYMBOLS.NEON_HEART, NEON_SYMBOLS.DIGITAL_BRAIN];
            symbol = premiums[Math.floor(Math.random() * premiums.length)];
        } else {
            symbol = NEON_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkNeonMegaways(finalSymbols, totalBet);
}

function checkNeonMegaways(symbols, totalBet) {
    let totalWin = 0;
    const winningPositions = new Set();
    let statusMessage = '';

    // Check megaways wins (adjacent symbols from left to right)
    const megawaysWins = findMegawaysWins(symbols);
    
    for (const win of megawaysWins) {
        const symbol = win.symbol;
        const count = win.count;
        let multiplier = 0;
        
        if (symbol === NEON_SYMBOLS.CYBER_SKULL && count >= 3) {
            multiplier = count >= 6 ? 400 : count >= 5 ? 250 : count >= 4 ? 150 : 75;
            statusMessage = 'CYBER SKULL DOMINATES THE MATRIX!';
            neonNexusGame.neonCharge += count * 20;
            neonNexusGame.cyberpunkRage = true;
        } else if (symbol === NEON_SYMBOLS.NEON_HEART && count >= 3) {
            multiplier = count >= 6 ? 300 : count >= 5 ? 200 : count >= 4 ? 120 : 60;
            statusMessage = 'NEON HEART PULSES WITH POWER!';
            neonNexusGame.neonCharge += count * 15;
        } else if (symbol === NEON_SYMBOLS.DIGITAL_BRAIN && count >= 3) {
            multiplier = count >= 6 ? 250 : count >= 5 ? 170 : count >= 4 ? 100 : 50;
            statusMessage = 'DIGITAL BRAIN PROCESSES VICTORY!';
            neonNexusGame.hackLevel = Math.min(neonNexusGame.hackLevel + 1, 10);
        } else if (symbol === NEON_SYMBOLS.CYBER_EYE && count >= 3) {
            multiplier = count >= 6 ? 200 : count >= 5 ? 140 : count >= 4 ? 80 : 40;
            statusMessage = 'CYBER EYE SEES ALL PROFITS!';
        } else if (symbol === NEON_SYMBOLS.NEON_DIAMOND && count >= 3) {
            multiplier = count >= 6 ? 180 : count >= 5 ? 120 : count >= 4 ? 70 : 35;
            statusMessage = 'NEON DIAMOND SHINES BRIGHT!';
        } else if (symbol === NEON_SYMBOLS.CIRCUIT_BOARD && count >= 3) {
            multiplier = count >= 6 ? 150 : count >= 5 ? 100 : count >= 4 ? 60 : 30;
            statusMessage = 'CIRCUIT BOARD OVERLOADS!';
        } else if (symbol === NEON_SYMBOLS.LASER_BEAM && count >= 3) {
            multiplier = count >= 6 ? 120 : count >= 5 ? 80 : count >= 4 ? 50 : 25;
            statusMessage = 'LASER BEAM CUTS THROUGH!';
        } else if (symbol === NEON_SYMBOLS.HOLOGRAM && count >= 3) {
            multiplier = count >= 6 ? 100 : count >= 5 ? 65 : count >= 4 ? 40 : 20;
            statusMessage = 'HOLOGRAM MATERIALIZES WINS!';
        }

        if (multiplier > 0) {
            const megawaysBonus = Math.floor(neonNexusGame.currentMegaways / 10000);
            totalWin += totalBet * multiplier * neonNexusGame.cascadeMultiplier * (1 + megawaysBonus);
            win.positions.forEach(pos => winningPositions.add(pos));
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === NEON_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.6);
        totalWin *= wildMultiplier;
        statusMessage += ` Lightning hack: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter matrix bonus
    const scatterCount = symbols.filter(s => s === NEON_SYMBOLS.SCATTER).length;
    if (scatterCount >= 4) {
        const megaSpinsAwarded = 20 + (scatterCount - 4) * 10;
        neonNexusGame.megaSpins += megaSpinsAwarded;
        neonNexusGame.hackLevel = Math.min(neonNexusGame.hackLevel + 2, 10);
        statusMessage += ` 🌐 MATRIX BREACH! ${megaSpinsAwarded} mega spins!`;
        neonNexusGame.matrixMode = true;
        
        // Trigger matrix effect
        document.getElementById('matrixEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('matrixEffect').style.opacity = '0';
            neonNexusGame.matrixMode = false;
        }, 6000);
    }

    // System overload activation
    if (neonNexusGame.neonCharge >= 250) {
        const overloadMultiplier = 12 + neonNexusGame.hackLevel;
        totalWin *= overloadMultiplier;
        statusMessage = `💥 SYSTEM OVERLOAD! ${overloadMultiplier}x CRITICAL ERROR!`;
        neonNexusGame.neonCharge = 0;
        neonNexusGame.systemOverload = true;
        
        // Trigger overload effect
        document.getElementById('overloadEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('overloadEffect').style.opacity = '0';
            neonNexusGame.systemOverload = false;
        }, 4000);
    }

    // Cyberpunk rage activation
    if (neonNexusGame.cyberpunkRage && neonNexusGame.hackLevel >= 5) {
        const rageMultiplier = 8 + neonNexusGame.hackLevel;
        totalWin *= rageMultiplier;
        statusMessage = `🤖 CYBERPUNK RAGE! ${rageMultiplier}x DIGITAL FURY!`;
        neonNexusGame.cyberpunkRage = false;
    }

    // Cascade feature (if wins occurred)
    if (totalWin > 0) {
        neonNexusGame.cascadeMultiplier += 0.5;
        
        // Trigger cascade effect
        document.getElementById('cascadeEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('cascadeEffect').style.opacity = '0';
        }, 2000);
    }

    // Megaways bonus
    if (neonNexusGame.currentMegaways >= 100000) {
        totalWin += totalBet * 50;
        statusMessage += ' MAXIMUM MEGAWAYS ACHIEVED!';
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        neonNexusGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#neonReels .slot:not(.invisible)');
        winningPositions.forEach(index => {
            if (slots[index]) slots[index].classList.add('cascade-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The matrix resists your infiltration attempt...';
    }

    document.getElementById('neonGameStatus').textContent = statusMessage;
    neonNexusGame.isSpinning = false;
    updateNeonDisplay();
}

function findMegawaysWins(symbols) {
    // Simplified megaways win detection
    const wins = [];
    const slots = document.querySelectorAll('#neonReels .slot:not(.invisible)');
    
    // Group symbols by reel
    const reels = [[], [], [], [], [], []];
    slots.forEach((slot, index) => {
        const reel = Math.floor(index / 7);
        if (reel < 6) {
            reels[reel].push({symbol: slot.textContent, position: index});
        }
    });

    // Check for adjacent wins from left to right
    for (const startSymbol of NEON_REEL_SYMBOLS) {
        if (startSymbol === NEON_SYMBOLS.WILD || startSymbol === NEON_SYMBOLS.SCATTER) continue;
        
        let consecutiveReels = 0;
        const positions = [];
        
        for (let reel = 0; reel < 6; reel++) {
            const hasSymbol = reels[reel].some(item => 
                item.symbol === startSymbol || item.symbol === NEON_SYMBOLS.WILD
            );
            
            if (hasSymbol) {
                consecutiveReels++;
                reels[reel].forEach(item => {
                    if (item.symbol === startSymbol || item.symbol === NEON_SYMBOLS.WILD) {
                        positions.push(item.position);
                    }
                });
            } else {
                break;
            }
        }
        
        if (consecutiveReels >= 3) {
            wins.push({
                symbol: startSymbol,
                count: consecutiveReels,
                positions: positions
            });
        }
    }
    
    return wins;
}

function updateNeonDisplay() {
    const spinButton = document.getElementById('hackMatrix');
    spinButton.disabled = neonNexusGame.isSpinning;
    spinButton.textContent = neonNexusGame.isSpinning ? 'HACKING...' : 'HACK THE MATRIX';

    document.getElementById('neonMegaSpins').textContent = neonNexusGame.megaSpins;
    document.getElementById('neonLastWin').textContent = `${neonNexusGame.lastWin} GA`;
    document.getElementById('neonCharge').textContent = neonNexusGame.neonCharge;
    document.getElementById('hackLevel').textContent = neonNexusGame.hackLevel;
    document.getElementById('currentMegaways').textContent = neonNexusGame.currentMegaways.toLocaleString();
    document.getElementById('overloadStatus').textContent = neonNexusGame.systemOverload ? 'CRITICAL!' : 'STABLE';
    document.getElementById('matrixStatus').textContent = neonNexusGame.matrixMode ? 'BREACHED!' : 'OFFLINE';
    document.getElementById('cyberpunkStatus').textContent = neonNexusGame.cyberpunkRage ? 'RAGING!' : 'CALM';
    document.getElementById('cascadeMultiplier').textContent = `${neonNexusGame.cascadeMultiplier.toFixed(1)}x`;
    
    // Update charge meter
    const chargeLevel = (neonNexusGame.neonCharge / 250) * 100;
    document.getElementById('chargeMeter').style.width = `${Math.min(chargeLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadNeonNexusGame();
});

