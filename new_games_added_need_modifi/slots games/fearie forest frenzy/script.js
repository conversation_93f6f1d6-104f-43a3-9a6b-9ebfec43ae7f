// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- <PERSON>'s Hoard Game Implementation ---

const DRAGON_SYMBOLS = {
    WILD: '🐉',      // Dragon
    SCATTER: '�',   // Castle
    BONUS: '�',     // Diamond
    KNIGHT: '⚔️',
    TREASURE: '💰',
    CROWN: '👑',
    SHIELD: '�️',
    FIRE: '�',
    SWORD: '�️',
};

const DRAGON_REEL_SYMBOLS = [
    DRAGON_SYMBOLS.KNIGHT, DRAGON_SYMBOLS.TREASURE, DRAGON_SYMBOLS.CROWN, DRAGON_SYMBOLS.SHIELD,
    DRAGON_SYMBOLS.FIRE, DRAGON_SYMBOLS.SWORD, DRAGON_SYMBOLS.WILD, DRAGON_SYMBOLS.SCATTER, DRAGON_SYMBOLS.BONUS
];

const DRAGON_PAYLINES = [
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    [0, 6, 12, 18], [4, 8, 12, 16],
    [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4]
];

let dragonGame = {
    isSpinning: false,
    freeSpins: 0,
    dragonRage: 0,
    lastWin: 0
};

function loadDragonHoardGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-300 font-mono">DRAGON'S LAIR</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">TRIBUTE (BET)</label>
                        <input type="number" id="dragonBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startDragonSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        AWAKEN DRAGON
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Hoard:</span>
                            <span id="dragonLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Dragon Spins:</span>
                            <span id="dragonFreeSpins" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300 font-mono">DRAGON'S RAGE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="rageMeter" class="bg-gradient-to-r from-red-500 to-orange-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="rageStatus" class="text-xs text-center text-gray-400">Fury: 0%</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div id="dragonReels" class="grid grid-cols-5 gap-2 mb-6"></div>
                    
                    <div id="dragonStatus" class="text-center text-lg font-semibold text-red-300 mb-4 h-8 font-mono">
                        The dragon slumbers...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm font-mono">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.KNIGHT} x5:</span><span class="text-red-400">300x</span></div>
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.TREASURE} x5:</span><span class="text-yellow-400">200x</span></div>
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.CROWN} x5:</span><span class="text-purple-400">150x</span></div>
                                <div class="flex justify-between"><span>${DRAGON_SYMBOLS.SHIELD} x5:</span><span class="text-blue-400">100x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">DRAGON LORE</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-red-400">${DRAGON_SYMBOLS.WILD} Dragon:</span> Substitutes all but Castle.</div>
                                <div><span class="text-gray-400">${DRAGON_SYMBOLS.SCATTER} Castle:</span> 3+ grants Dragon Free Spins.</div>
                                <div><span class="text-cyan-400">${DRAGON_SYMBOLS.BONUS} Diamond:</span> Builds Dragon's Rage for fury.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupDragonGame();
}

function setupDragonGame() {
    document.getElementById('startDragonSpin').addEventListener('click', startDragonSpin);
    const reelsContainer = document.getElementById('dragonReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-red-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = DRAGON_REEL_SYMBOLS[i % DRAGON_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateDragonDisplay();
}

function updateDragonDisplay() {
    const spinButton = document.getElementById('startDragonSpin');
    spinButton.disabled = dragonGame.isSpinning;
    spinButton.textContent = dragonGame.isSpinning ? 'DRAGON ROARS...' : 'AWAKEN DRAGON';

    document.getElementById('dragonFreeSpins').textContent = dragonGame.freeSpins;
    document.getElementById('rageMeter').style.width = `${dragonGame.dragonRage}%`;
    document.getElementById('rageStatus').textContent = `Fury: ${dragonGame.dragonRage}%`;
    document.getElementById('dragonLastWin').textContent = `${dragonGame.lastWin} GA`;
}

function startDragonSpin() {
    if (dragonGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('dragonBet').value);

    if (dragonGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('dragonStatus').textContent = 'INSUFFICIENT TRIBUTE';
            return;
        }
        balance -= totalBet;
    } else {
        dragonGame.freeSpins--;
    }

    dragonGame.isSpinning = true;
    dragonGame.lastWin = 0;
    updateBalance();
    updateDragonDisplay();
    document.getElementById('dragonStatus').textContent = 'The dragon stirs...';

    const slots = document.querySelectorAll('#dragonReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = DRAGON_REEL_SYMBOLS[Math.floor(Math.random() * DRAGON_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishDragonSpin(totalBet);
        }
    }, spinInterval);
}

function finishDragonSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#dragonReels .slot');
    slots.forEach(slot => {
        const symbol = DRAGON_REEL_SYMBOLS[Math.floor(Math.random() * DRAGON_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkDragonWins(finalSymbols, totalBet);
}

function checkDragonWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    DRAGON_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === DRAGON_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== DRAGON_SYMBOLS.WILD && symbols[index] !== DRAGON_SYMBOLS.SCATTER) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        if (lineSymbol === DRAGON_SYMBOLS.SCATTER) return;

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === DRAGON_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getDragonMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 25;
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    const scatterCount = symbols.filter(s => s === DRAGON_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 12 + (scatterCount - 3) * 4;
        dragonGame.freeSpins += freeSpinsWon;
        statusMessage = `� Dragon's Castle Awakens! ${freeSpinsWon} dragon spins granted!`;
    }
    
    const bonusCount = symbols.filter(s => s === DRAGON_SYMBOLS.BONUS).length;
    if (bonusCount > 0) {
        dragonGame.dragonRage = Math.min(100, dragonGame.dragonRage + bonusCount * 15);
    }

    if (dragonGame.dragonRage >= 100 && totalWin > 0) {
        const powerMultiplier = Math.floor(Math.random() * 12) + 4;
        totalWin *= powerMultiplier;
        dragonGame.dragonRage = 0;
        statusMessage = `🔥 DRAGON'S FURY! Hoard multiplied by ${powerMultiplier}x!`;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        dragonGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('FURY')) {
            statusMessage = statusMessage ? statusMessage : `Dragon's hoard grows: +${totalWin} GA`;
        }

        const slots = document.querySelectorAll('#dragonReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The dragon guards its treasure.';
    }
    
    document.getElementById('dragonStatus').textContent = statusMessage;
    dragonGame.isSpinning = false;
    updateDragonDisplay();
}

function getDragonMultiplier(symbol, count) {
    const multipliers = {
        [DRAGON_SYMBOLS.KNIGHT]: { 3: 60, 4: 150, 5: 300 },
        [DRAGON_SYMBOLS.TREASURE]: { 3: 40, 4: 100, 5: 200 },
        [DRAGON_SYMBOLS.CROWN]: { 3: 30, 4: 75, 5: 150 },
        [DRAGON_SYMBOLS.SHIELD]: { 3: 20, 4: 50, 5: 100 },
        [DRAGON_SYMBOLS.FIRE]: { 3: 15, 4: 30, 5: 60 },
        [DRAGON_SYMBOLS.SWORD]: { 3: 10, 4: 20, 5: 40 },
    };
    return multipliers[symbol]?.[count] || 0;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDragonHoardGame();
});
