// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Wild Safari Game Implementation
const SAFARI_SYMBOLS = ['🦁', '🦅', '🦒', '🦓', '🦛', '🦘', '🐘', '🌿', '🐾', '🗺️'];

let wildSafariGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    wildMultiplier: 1,
    safariLevel: 1
};

function loadWildSafariGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <h4 class="text-xl font-bold mb-4 text-amber-400">WILD SAFARI</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-amber-300">EXPEDITION COST</label>
                        <input type="number" id="safariBet" value="30" min="5" max="1000"
                               class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startSafariSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        START EXPEDITION
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Expeditions:</span>
                            <span id="safariFreeSpin" class="text-amber-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Discovery:</span>
                            <span id="safariLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Safari Level:</span>
                            <span id="safariLevel" class="text-yellow-400">1</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-amber-900/20 rounded-lg border border-amber-500/20">
                        <h5 class="text-sm font-bold mb-2 text-amber-300">ANIMAL GUIDE</h5>
                        <div class="text-xs space-y-1">
                            <div>🦁 <span class="text-yellow-400">Lion King:</span> 50x bet</div>
                            <div>🐘 <span class="text-gray-400">Elephant:</span> 25x bet</div>
                            <div>🦒 <span class="text-orange-400">Giraffe:</span> 15x bet</div>
                            <div>🗺️ <span class="text-blue-400">Map:</span> Scatter bonus</div>
                            <div>🌿 <span class="text-green-400">Wild:</span> Substitutes all</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <div id="safariReels" class="grid grid-cols-5 gap-4 mb-6 h-64">
                        ${Array(20).fill(0).map((_, i) => 
                            `<div class="slot bg-amber-900/20 rounded-lg flex items-center justify-center text-4xl border border-amber-500/20 transition-all duration-300">🦁</div>`
                        ).join('')}
                    </div>
                    <div id="safariStatus" class="text-center text-lg font-semibold text-amber-300 h-8">
                        Begin your wild safari adventure!
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupWildSafariGame();
}

function setupWildSafariGame() {
    document.getElementById('startSafariSpin').addEventListener('click', startSafariSpin);
    updateSafariDisplay();
}

function startSafariSpin() {
    if (wildSafariGame.isSpinning) return;

    const betAmount = parseInt(document.getElementById('safariBet').value);

    if (wildSafariGame.freeSpins === 0) {
        if (balance < betAmount) {
            document.getElementById('safariStatus').textContent = 'Insufficient funds for expedition!';
            return;
        }
        balance -= betAmount;
    } else {
        wildSafariGame.freeSpins--;
    }

    wildSafariGame.isSpinning = true;
    wildSafariGame.lastWin = 0;
    updateBalance();
    updateSafariDisplay();
    document.getElementById('safariStatus').textContent = 'Tracking wildlife...';

    const slots = document.querySelectorAll('#safariReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = SAFARI_SYMBOLS[Math.floor(Math.random() * SAFARI_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishSafariSpin(betAmount);
        }
    }, spinInterval);
}

function finishSafariSpin(betAmount) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#safariReels .slot');
    slots.forEach(slot => {
        const symbol = SAFARI_SYMBOLS[Math.floor(Math.random() * SAFARI_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkSafariWins(finalSymbols, betAmount);
}

function checkSafariWins(symbols, betAmount) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for wins
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === '🦁' && count >= 3) {
            multiplier = count >= 5 ? 50 : count >= 4 ? 25 : 10;
            statusMessage = 'The Lion King roars with victory!';
        } else if (symbol === '🐘' && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 15 : 8;
            statusMessage = 'Elephant stampede brings fortune!';
        } else if (symbol === '🦒' && count >= 3) {
            multiplier = count >= 5 ? 15 : count >= 4 ? 10 : 5;
            statusMessage = 'Giraffe spots treasure from above!';
        } else if ((symbol === '🦓' || symbol === '🦛' || symbol === '🦘') && count >= 3) {
            multiplier = count >= 5 ? 10 : count >= 4 ? 6 : 3;
            statusMessage = 'Wildlife gathering brings rewards!';
        }

        if (multiplier > 0) {
            totalWin += betAmount * multiplier * wildSafariGame.wildMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Check for scatter bonus (🗺️ Map symbols)
    const scatterCount = symbols.filter(s => s === '🗺️').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 5 + (scatterCount - 3) * 3;
        wildSafariGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` Found ancient map! +${freeSpinsAwarded} free expeditions!`;
        wildSafariGame.safariLevel++;
    }

    // Wild substitution bonus (🌿 symbols)
    const wildCount = symbols.filter(s => s === '🌿').length;
    if (wildCount > 0) {
        wildSafariGame.wildMultiplier = 1 + (wildCount * 0.5);
        if (totalWin > 0) {
            statusMessage += ` Wild nature multiplies your discovery!`;
        }
    } else {
        wildSafariGame.wildMultiplier = 1;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        wildSafariGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#safariReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'No wildlife spotted this expedition.';
    }

    document.getElementById('safariStatus').textContent = statusMessage;
    wildSafariGame.isSpinning = false;
    updateSafariDisplay();
}

function updateSafariDisplay() {
    document.getElementById('safariFreeSpin').textContent = wildSafariGame.freeSpins;
    document.getElementById('safariLastWin').textContent = `${wildSafariGame.lastWin} GA`;
    document.getElementById('safariLevel').textContent = wildSafariGame.safariLevel;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadWildSafariGame();
});
