// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Volcano Vault Progressive Game Implementation
const PROGRESSIVE_VOLCANO_SYMBOLS = {
    VOLCANO_KING: '👑',
    LAVA_TITAN: '🔥',
    MAGMA_DRAGON: '🐲',
    CRYSTAL_CORE: '💎',
    MOL<PERSON>N_GOLD: '🏆',
    PRESSURE_GAUGE: '🌡️',
    ERUPTION: '🌋',
    THERMAL_VENT: '💨',
    WILD: '⚡',
    SCATTER: '💥'
};

const PROGRESSIVE_VOLCANO_REEL_SYMBOLS = [
    PROGRESSIVE_VOLCANO_SYMBOLS.VOLCANO_KING, PROGRESSIVE_VOLCANO_SYMBOLS.LAVA_TITAN, PROGRESSIVE_VOLCANO_SYMBOLS.MAGMA_DRAGON,
    PROGRESSIVE_VOLCANO_SYMBOLS.CRYSTAL_CORE, PROGRESSIVE_VOLCANO_SYMBOLS.MOLTEN_GOLD, PROGRESSIVE_VOLCANO_SYMBOLS.PRESSURE_GAUGE,
    PROGRESSIVE_VOLCANO_SYMBOLS.ERUPTION, PROGRESSIVE_VOLCANO_SYMBOLS.THERMAL_VENT, PROGRESSIVE_VOLCANO_SYMBOLS.WILD, PROGRESSIVE_VOLCANO_SYMBOLS.SCATTER
];

let progressiveVolcanoGame = {
    isSpinning: false,
    eruptionSpins: 0,
    lastWin: 0,
    volcanoLevel: 1,
    heatIntensity: 0,
    progressiveMultiplier: 1,
    megaJackpot: 50000,
    majorJackpot: 15000,
    minorJackpot: 5000,
    miniJackpot: 1000,
    vaultUnlocked: false,
    thermalOverload: false,
    magmaStorm: false,
    crystalResonance: false,
    volcanoKingPresent: false,
    progressiveLevel: 1,
    heatAccumulator: 0
};

function loadProgressiveVolcanoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400 font-mono">VOLCANO VAULT PROGRESSIVE</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">THERMAL INVESTMENT</label>
                        <input type="number" id="progressiveVolcanoBet" value="50" min="20" max="1000" step="10"
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">ERUPTION INTENSITY</label>
                        <select id="progressiveEruptionLevel" class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="mild">Mild Eruption (10x)</option>
                            <option value="moderate">Moderate Eruption (20x)</option>
                            <option value="explosive">Explosive Eruption (40x)</option>
                        </select>
                    </div>
                    
                    <button id="triggerProgressiveEruption" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        TRIGGER PROGRESSIVE ERUPTION
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Eruption Spins:</span>
                            <span id="progressiveEruptionSpins" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Eruption:</span>
                            <span id="progressiveLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Volcano Level:</span>
                            <span id="progressiveVolcanoLevel" class="text-orange-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Heat Intensity:</span>
                            <span id="progressiveHeatIntensity" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Progressive Multi:</span>
                            <span id="progressiveMultiplier" class="text-purple-400">1x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Heat Accumulator:</span>
                            <span id="heatAccumulator" class="text-orange-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300 font-mono">PROGRESSIVE JACKPOTS</h5>
                        <div class="text-xs space-y-1">
                            <div class="flex justify-between">
                                <span class="text-yellow-400">MEGA:</span>
                                <span id="megaJackpot" class="text-yellow-400">50,000 GA</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-orange-400">MAJOR:</span>
                                <span id="majorJackpot" class="text-orange-400">15,000 GA</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-red-400">MINOR:</span>
                                <span id="minorJackpot" class="text-red-400">5,000 GA</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-pink-400">MINI:</span>
                                <span id="miniJackpot" class="text-pink-400">1,000 GA</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-4 bg-orange-900/20 rounded-lg border border-orange-500/20">
                        <h5 class="text-sm font-bold mb-2 text-orange-300 font-mono">THERMAL FEATURES</h5>
                        <div class="text-xs space-y-1">
                            <div>👑 <span class="text-yellow-400">Volcano King:</span> <span id="volcanoKingStatus" class="text-gray-400">Dormant</span></div>
                            <div>🔥 <span class="text-red-400">Thermal Overload:</span> <span id="thermalStatus" class="text-gray-400">Stable</span></div>
                            <div>🐲 <span class="text-green-400">Magma Storm:</span> <span id="magmaStormStatus" class="text-gray-400">Calm</span></div>
                            <div>💎 <span class="text-blue-400">Crystal Resonance:</span> <span id="crystalStatus" class="text-gray-400">Silent</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div class="relative mb-6">
                        <div id="progressiveVolcanoReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(25).fill(0).map((_, i) => 
                                `<div class="slot bg-red-900/20 rounded-lg flex items-center justify-center text-2xl border border-red-500/20 transition-all duration-300">👑</div>`
                            ).join('')}
                        </div>
                        <div id="volcanoKingEffect" class="absolute inset-0 bg-gradient-to-t from-yellow-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="thermalOverloadEffect" class="absolute inset-0 bg-gradient-to-b from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="magmaStormEffect" class="absolute inset-0 bg-gradient-radial from-orange-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="crystalResonanceEffect" class="absolute inset-0 bg-gradient-conic from-blue-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="progressiveJackpotEffect" class="absolute inset-0 bg-gradient-to-r from-gold-500/10 via-white/10 to-gold-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                    </div>
                    
                    <div id="progressiveVolcanoStatus" class="text-center text-lg font-semibold text-red-300 mb-4 h-8 font-mono">
                        Progressive volcano systems ready for thermal eruption...
                    </div>
                    
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">HEAT GAUGE</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="progressiveHeatMeter" class="bg-gradient-to-r from-red-500 to-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="progressiveHeatLevel" class="text-red-400">0%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">VAULT STATUS</h6>
                            <div class="text-center">
                                <span id="vaultStatus" class="text-red-400">LOCKED</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PROGRESSIVE</h6>
                            <div class="text-center">
                                <span id="progressiveStatus" class="text-purple-400">LEVEL 1</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-red-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">NEXT JACKPOT</h6>
                            <div class="text-center">
                                <span id="nextJackpot" class="text-yellow-400">MINI</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupProgressiveVolcanoGame();
}

function setupProgressiveVolcanoGame() {
    document.getElementById('triggerProgressiveEruption').addEventListener('click', triggerProgressiveEruption);
    const reelsContainer = document.getElementById('progressiveVolcanoReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 25; i++) { // 5x5 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-red-900/20 rounded-lg p-2 text-center text-2xl border border-red-500/20 flex items-center justify-center h-12 transition-all duration-300';
        slot.textContent = PROGRESSIVE_VOLCANO_REEL_SYMBOLS[i % PROGRESSIVE_VOLCANO_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateProgressiveVolcanoDisplay();
    updateProgressiveJackpots();
}

function triggerProgressiveEruption() {
    if (progressiveVolcanoGame.isSpinning) return;

    const bet = parseInt(document.getElementById('progressiveVolcanoBet').value);
    const intensity = document.getElementById('progressiveEruptionLevel').value;
    let totalBet;
    
    switch(intensity) {
        case 'moderate': totalBet = bet * 20; break;
        case 'explosive': totalBet = bet * 40; break;
        default: totalBet = bet * 10;
    }

    if (progressiveVolcanoGame.eruptionSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('progressiveVolcanoStatus').textContent = 'INSUFFICIENT THERMAL ENERGY FOR ERUPTION';
            return;
        }
        balance -= totalBet;
        
        // Contribute to progressive jackpots
        progressiveVolcanoGame.megaJackpot += Math.floor(totalBet * 0.02);
        progressiveVolcanoGame.majorJackpot += Math.floor(totalBet * 0.015);
        progressiveVolcanoGame.minorJackpot += Math.floor(totalBet * 0.01);
        progressiveVolcanoGame.miniJackpot += Math.floor(totalBet * 0.005);
    } else {
        progressiveVolcanoGame.eruptionSpins--;
    }

    progressiveVolcanoGame.isSpinning = true;
    progressiveVolcanoGame.lastWin = 0;
    updateBalance();
    updateProgressiveVolcanoDisplay();
    updateProgressiveJackpots();
    document.getElementById('progressiveVolcanoStatus').textContent = 'Progressive volcanic eruption in progress...';

    const slots = document.querySelectorAll('#progressiveVolcanoReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'progressive-highlight'));

    // Thermal overload effect during spin
    document.getElementById('thermalOverloadEffect').style.opacity = '0.4';

    let spinDuration = 2800;
    let spinInterval = 120;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = PROGRESSIVE_VOLCANO_REEL_SYMBOLS[Math.floor(Math.random() * PROGRESSIVE_VOLCANO_REEL_SYMBOLS.length)];
            slot.style.background = `linear-gradient(45deg, #${Math.floor(Math.random()*16777215).toString(16)}, #dc2626)`;
            slot.style.transform = `scale(${0.9 + Math.random() * 0.2}) rotateZ(${Math.random() * 8 - 4}deg)`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('thermalOverloadEffect').style.opacity = '0';
            slots.forEach(slot => {
                slot.style.background = '';
                slot.style.transform = 'scale(1) rotateZ(0deg)';
            });
            finishProgressiveEruption(totalBet);
        }
    }, spinInterval);
}

function finishProgressiveEruption(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#progressiveVolcanoReels .slot');
    
    // Enhanced symbol generation with intensity bonuses
    const intensity = document.getElementById('progressiveEruptionLevel').value;
    let wildChance = 0.16;
    let scatterChance = 0.12;
    let volcanoKingChance = 0.18;
    
    if (intensity === 'moderate') {
        wildChance = 0.22;
        scatterChance = 0.18;
        volcanoKingChance = 0.25;
    } else if (intensity === 'explosive') {
        wildChance = 0.32;
        scatterChance = 0.26;
        volcanoKingChance = 0.35;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = PROGRESSIVE_VOLCANO_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = PROGRESSIVE_VOLCANO_SYMBOLS.SCATTER;
        } else if (Math.random() < volcanoKingChance) {
            symbol = PROGRESSIVE_VOLCANO_SYMBOLS.VOLCANO_KING;
        } else {
            symbol = PROGRESSIVE_VOLCANO_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkProgressiveVolcanoWins(finalSymbols, totalBet);
}

function checkProgressiveVolcanoWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (progressive eruptions)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== PROGRESSIVE_VOLCANO_SYMBOLS.WILD && symbol !== PROGRESSIVE_VOLCANO_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.VOLCANO_KING) {
                multiplier = 1200;
                statusMessage = 'VOLCANO KING SUPREME ERUPTION!';
                progressiveVolcanoGame.heatIntensity += 500;
                progressiveVolcanoGame.volcanoKingPresent = true;
                checkProgressiveJackpot('mega');
            } else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.LAVA_TITAN) {
                multiplier = 900;
                statusMessage = 'LAVA TITAN AWAKENS!';
                progressiveVolcanoGame.heatIntensity += 400;
                progressiveVolcanoGame.thermalOverload = true;
                checkProgressiveJackpot('major');
            } else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.MAGMA_DRAGON) {
                multiplier = 750;
                statusMessage = 'MAGMA DRAGON RISES!';
                progressiveVolcanoGame.heatIntensity += 350;
                progressiveVolcanoGame.magmaStorm = true;
                checkProgressiveJackpot('minor');
            } else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.CRYSTAL_CORE) {
                multiplier = 600;
                statusMessage = 'CRYSTAL CORE RESONANCE!';
                progressiveVolcanoGame.heatIntensity += 300;
                progressiveVolcanoGame.crystalResonance = true;
            } else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.MOLTEN_GOLD) {
                multiplier = 500;
                statusMessage = 'MOLTEN GOLD DISCOVERY!';
                progressiveVolcanoGame.heatIntensity += 250;
                checkProgressiveJackpot('mini');
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * progressiveVolcanoGame.progressiveMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind and 3-of-a-kind with similar logic...
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== PROGRESSIVE_VOLCANO_SYMBOLS.WILD && symbol !== PROGRESSIVE_VOLCANO_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.VOLCANO_KING) multiplier = 400;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.LAVA_TITAN) multiplier = 320;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.MAGMA_DRAGON) multiplier = 260;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.CRYSTAL_CORE) multiplier = 200;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.MOLTEN_GOLD) multiplier = 160;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.PRESSURE_GAUGE) multiplier = 130;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.ERUPTION) multiplier = 100;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.THERMAL_VENT) multiplier = 80;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * progressiveVolcanoGame.progressiveMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== PROGRESSIVE_VOLCANO_SYMBOLS.WILD && symbol !== PROGRESSIVE_VOLCANO_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.VOLCANO_KING) multiplier = 150;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.LAVA_TITAN) multiplier = 120;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.MAGMA_DRAGON) multiplier = 100;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.CRYSTAL_CORE) multiplier = 80;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.MOLTEN_GOLD) multiplier = 65;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.PRESSURE_GAUGE) multiplier = 50;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.ERUPTION) multiplier = 40;
            else if (symbol === PROGRESSIVE_VOLCANO_SYMBOLS.THERMAL_VENT) multiplier = 35;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * progressiveVolcanoGame.progressiveMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === PROGRESSIVE_VOLCANO_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 1.2);
        totalWin *= wildMultiplier;
        statusMessage += ` Thermal energy: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter eruption bonus
    const scatterCount = symbols.filter(s => s === PROGRESSIVE_VOLCANO_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const eruptionSpinsAwarded = 30 + (scatterCount - 3) * 15;
        progressiveVolcanoGame.eruptionSpins += eruptionSpinsAwarded;
        progressiveVolcanoGame.volcanoLevel = Math.min(progressiveVolcanoGame.volcanoLevel + 1, 10);
        progressiveVolcanoGame.vaultUnlocked = true;
        statusMessage += ` 💥 PROGRESSIVE VAULT UNLOCKED! ${eruptionSpinsAwarded} eruption spins!`;
        
        // Increase progressive multiplier
        progressiveVolcanoGame.progressiveMultiplier += 2.0;
        progressiveVolcanoGame.progressiveLevel++;
    }

    // Progressive feature activations
    checkProgressiveFeatures(totalBet);

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        progressiveVolcanoGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#progressiveVolcanoReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('progressive-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Progressive volcanic systems require more thermal energy...';
    }

    document.getElementById('progressiveVolcanoStatus').textContent = statusMessage;
    progressiveVolcanoGame.isSpinning = false;
    updateProgressiveVolcanoDisplay();
    updateProgressiveJackpots();
}

function checkProgressiveJackpot(type) {
    let jackpotWon = 0;
    let jackpotName = '';
    
    if (type === 'mega' && Math.random() < 0.001) {
        jackpotWon = progressiveVolcanoGame.megaJackpot;
        jackpotName = 'MEGA JACKPOT';
        progressiveVolcanoGame.megaJackpot = 50000;
    } else if (type === 'major' && Math.random() < 0.005) {
        jackpotWon = progressiveVolcanoGame.majorJackpot;
        jackpotName = 'MAJOR JACKPOT';
        progressiveVolcanoGame.majorJackpot = 15000;
    } else if (type === 'minor' && Math.random() < 0.02) {
        jackpotWon = progressiveVolcanoGame.minorJackpot;
        jackpotName = 'MINOR JACKPOT';
        progressiveVolcanoGame.minorJackpot = 5000;
    } else if (type === 'mini' && Math.random() < 0.05) {
        jackpotWon = progressiveVolcanoGame.miniJackpot;
        jackpotName = 'MINI JACKPOT';
        progressiveVolcanoGame.miniJackpot = 1000;
    }
    
    if (jackpotWon > 0) {
        balance += jackpotWon;
        progressiveVolcanoGame.lastWin += jackpotWon;
        updateBalance();
        
        document.getElementById('progressiveJackpotEffect').style.opacity = '1';
        document.getElementById('progressiveVolcanoStatus').innerHTML = 
            `<span class="text-yellow-400 neon-glow">🎉 ${jackpotName} WON! +${jackpotWon} GA! 🎉</span>`;
        
        setTimeout(() => {
            document.getElementById('progressiveJackpotEffect').style.opacity = '0';
        }, 8000);
    }
}

function checkProgressiveFeatures(totalBet) {
    // Volcano King activation
    if (progressiveVolcanoGame.volcanoKingPresent && progressiveVolcanoGame.heatIntensity >= 800) {
        const kingMultiplier = 30 + progressiveVolcanoGame.volcanoLevel;
        const kingBonus = totalBet * kingMultiplier;
        balance += kingBonus;
        progressiveVolcanoGame.lastWin += kingBonus;
        updateBalance();
        
        document.getElementById('volcanoKingEffect').style.opacity = '1';
        document.getElementById('volcanoKingStatus').textContent = 'REIGNING';
        setTimeout(() => {
            document.getElementById('volcanoKingEffect').style.opacity = '0';
            document.getElementById('volcanoKingStatus').textContent = 'Dormant';
            progressiveVolcanoGame.volcanoKingPresent = false;
        }, 6000);
    }
    
    // Thermal Overload activation
    if (progressiveVolcanoGame.thermalOverload && progressiveVolcanoGame.heatIntensity >= 600) {
        progressiveVolcanoGame.progressiveMultiplier *= 5;
        document.getElementById('thermalOverloadEffect').style.opacity = '1';
        document.getElementById('thermalStatus').textContent = 'OVERLOADING';
        setTimeout(() => {
            document.getElementById('thermalOverloadEffect').style.opacity = '0';
            document.getElementById('thermalStatus').textContent = 'Stable';
            progressiveVolcanoGame.thermalOverload = false;
        }, 5000);
    }
    
    // Magma Storm activation
    if (progressiveVolcanoGame.magmaStorm && progressiveVolcanoGame.heatIntensity >= 500) {
        const stormBonus = totalBet * 150;
        balance += stormBonus;
        progressiveVolcanoGame.lastWin += stormBonus;
        updateBalance();
        
        document.getElementById('magmaStormEffect').style.opacity = '1';
        document.getElementById('magmaStormStatus').textContent = 'RAGING';
        setTimeout(() => {
            document.getElementById('magmaStormEffect').style.opacity = '0';
            document.getElementById('magmaStormStatus').textContent = 'Calm';
            progressiveVolcanoGame.magmaStorm = false;
        }, 4000);
    }
    
    // Crystal Resonance activation
    if (progressiveVolcanoGame.crystalResonance && progressiveVolcanoGame.heatIntensity >= 400) {
        progressiveVolcanoGame.heatAccumulator += 200;
        document.getElementById('crystalResonanceEffect').style.opacity = '1';
        document.getElementById('crystalStatus').textContent = 'RESONATING';
        setTimeout(() => {
            document.getElementById('crystalResonanceEffect').style.opacity = '0';
            document.getElementById('crystalStatus').textContent = 'Silent';
            progressiveVolcanoGame.crystalResonance = false;
        }, 3500);
    }
}

function updateProgressiveVolcanoDisplay() {
    const spinButton = document.getElementById('triggerProgressiveEruption');
    spinButton.disabled = progressiveVolcanoGame.isSpinning;
    spinButton.textContent = progressiveVolcanoGame.isSpinning ? 'ERUPTING...' : 'TRIGGER PROGRESSIVE ERUPTION';

    document.getElementById('progressiveEruptionSpins').textContent = progressiveVolcanoGame.eruptionSpins;
    document.getElementById('progressiveLastWin').textContent = `${progressiveVolcanoGame.lastWin} GA`;
    document.getElementById('progressiveVolcanoLevel').textContent = progressiveVolcanoGame.volcanoLevel;
    document.getElementById('progressiveHeatIntensity').textContent = progressiveVolcanoGame.heatIntensity;
    document.getElementById('progressiveMultiplier').textContent = `${progressiveVolcanoGame.progressiveMultiplier.toFixed(1)}x`;
    document.getElementById('heatAccumulator').textContent = progressiveVolcanoGame.heatAccumulator;
    
    // Update heat meter
    const heatPercentage = Math.min(100, (progressiveVolcanoGame.heatIntensity / 800) * 100);
    document.getElementById('progressiveHeatMeter').style.width = `${heatPercentage}%`;
    document.getElementById('progressiveHeatLevel').textContent = `${Math.round(heatPercentage)}%`;
    
    document.getElementById('vaultStatus').textContent = progressiveVolcanoGame.vaultUnlocked ? 'UNLOCKED' : 'LOCKED';
    document.getElementById('progressiveStatus').textContent = `LEVEL ${progressiveVolcanoGame.progressiveLevel}`;
    
    // Determine next jackpot based on heat intensity
    let nextJackpot = 'MINI';
    if (progressiveVolcanoGame.heatIntensity >= 600) nextJackpot = 'MEGA';
    else if (progressiveVolcanoGame.heatIntensity >= 400) nextJackpot = 'MAJOR';
    else if (progressiveVolcanoGame.heatIntensity >= 200) nextJackpot = 'MINOR';
    
    document.getElementById('nextJackpot').textContent = nextJackpot;
}

function updateProgressiveJackpots() {
    document.getElementById('megaJackpot').textContent = `${progressiveVolcanoGame.megaJackpot.toLocaleString()} GA`;
    document.getElementById('majorJackpot').textContent = `${progressiveVolcanoGame.majorJackpot.toLocaleString()} GA`;
    document.getElementById('minorJackpot').textContent = `${progressiveVolcanoGame.minorJackpot.toLocaleString()} GA`;
    document.getElementById('miniJackpot').textContent = `${progressiveVolcanoGame.miniJackpot.toLocaleString()} GA`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadProgressiveVolcanoGame();
});
