// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Jungle Jinx Game Implementation
const JUNGLE_SYMBOLS = {
    JAGUAR: '🐆',
    TEMPLE: '🏛️',
    PARROT: '🦜',
    FLOWER: '🌺',
    SNAKE: '🐍',
    SPIDER: '🕷️',
    LIZARD: '🦎',
    TOTEM: '🗿',
    SHAMAN: '🎯',
    JINX: '💀'
};

const JUNGLE_REEL_SYMBOLS = [
    JUNGLE_SYMBOLS.JAGUAR, JUNGLE_SYMBOLS.TEMPLE, JUNGLE_SYMBOLS.PARROT,
    JUNGLE_SYMBOLS.FLOWER, JUNGLE_SYMBOLS.SNAKE, JUNGLE_SYMBOLS.SPIDER,
    JUNGLE_SYMBOLS.LIZARD, JUNGLE_SYMBOLS.TOTEM, JUNG<PERSON>_SYMBOLS.SHAMAN, JUNGLE_SYMBOLS.JINX
];

let jungleJinxGame = {
    isSpinning: false,
    tribalSpins: 0,
    lastWin: 0,
    curseLevel: 0,
    wildernessEnergy: 0,
    shamanPower: false,
    jinxMode: false,
    totemCount: 0,
    rainforestMultiplier: 1
};

function loadJungleJinxGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400 font-mono">JUNGLE JINX</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">TRIBAL OFFERING</label>
                        <input type="number" id="jungleBet" value="25" min="5" max="1000" step="5"
                               class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">EXPEDITION DEPTH</label>
                        <select id="jungleDepth" class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="shallow">Shallow (2x)</option>
                            <option value="deep">Deep Jungle (4x)</option>
                            <option value="heart">Heart of Darkness (8x)</option>
                        </select>
                    </div>
                    
                    <button id="enterJungle" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        ENTER THE JUNGLE
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Tribal Spins:</span>
                            <span id="jungleTribalSpins" class="text-green-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Treasure:</span>
                            <span id="jungleLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Curse Level:</span>
                            <span id="curseLevel" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Totems Found:</span>
                            <span id="totemCount" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Shaman Power:</span>
                            <span id="shamanStatus" class="text-yellow-400">DORMANT</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-green-900/20 rounded-lg border border-green-500/20">
                        <h5 class="text-sm font-bold mb-2 text-green-300 font-mono">JUNGLE TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div>🐆 <span class="text-orange-400">Jaguar:</span> 150x bet</div>
                            <div>🏛️ <span class="text-gray-400">Temple:</span> 100x bet</div>
                            <div>🦜 <span class="text-blue-400">Parrot:</span> 80x bet</div>
                            <div>🌺 <span class="text-pink-400">Flower:</span> 60x bet</div>
                            <div>🎯 <span class="text-yellow-400">Shaman:</span> Wild substitute</div>
                            <div>🗿 <span class="text-purple-400">Totem:</span> Scatter bonus</div>
                            <div>💀 <span class="text-red-400">Jinx:</span> Curse multiplier</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <div class="relative mb-6">
                        <div id="jungleReels" class="grid grid-cols-5 gap-2 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-green-900/20 rounded-lg flex items-center justify-center text-4xl border border-green-500/20 transition-all duration-300">🐆</div>`
                            ).join('')}
                        </div>
                        <div id="jinxEffect" class="absolute inset-0 bg-gradient-to-t from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="shamanEffect" class="absolute inset-0 bg-gradient-to-b from-yellow-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="jungleEffect" class="absolute inset-0 bg-gradient-radial from-green-400/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                    </div>
                    
                    <div id="jungleStatus" class="text-center text-lg font-semibold text-green-300 mb-4 h-8 font-mono">
                        The ancient jungle calls to you...
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-green-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">JUNGLE STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Jinx Mode:</span>
                                    <span id="jinxMode" class="text-red-400">INACTIVE</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Energy:</span>
                                    <span id="wildernessEnergy" class="text-green-400">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-green-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CURSE METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="curseMeter" class="bg-gradient-to-r from-red-500 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-green-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">MULTIPLIER</h6>
                            <div class="text-center">
                                <span id="rainforestMultiplier" class="text-2xl text-green-400">1x</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupJungleJinxGame();
}

function setupJungleJinxGame() {
    document.getElementById('enterJungle').addEventListener('click', enterJungleExpedition);
    const reelsContainer = document.getElementById('jungleReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-green-900/20 rounded-lg p-2 text-center text-4xl border border-green-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = JUNGLE_REEL_SYMBOLS[i % JUNGLE_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateJungleDisplay();
}

function enterJungleExpedition() {
    if (jungleJinxGame.isSpinning) return;

    const bet = parseInt(document.getElementById('jungleBet').value);
    const depth = document.getElementById('jungleDepth').value;
    let totalBet;
    
    switch(depth) {
        case 'deep': totalBet = bet * 4; break;
        case 'heart': totalBet = bet * 8; break;
        default: totalBet = bet * 2;
    }

    if (jungleJinxGame.tribalSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('jungleStatus').textContent = 'INSUFFICIENT TRIBAL OFFERING';
            return;
        }
        balance -= totalBet;
    } else {
        jungleJinxGame.tribalSpins--;
    }

    jungleJinxGame.isSpinning = true;
    jungleJinxGame.lastWin = 0;
    updateBalance();
    updateJungleDisplay();
    document.getElementById('jungleStatus').textContent = 'Venturing into the wild jungle...';

    const slots = document.querySelectorAll('#jungleReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Jungle effect during spin
    document.getElementById('jungleEffect').style.opacity = '0.3';

    let spinDuration = 2000;
    let spinInterval = 80;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = JUNGLE_REEL_SYMBOLS[Math.floor(Math.random() * JUNGLE_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('jungleEffect').style.opacity = '0';
            finishJungleExpedition(totalBet);
        }
    }, spinInterval);
}

function finishJungleExpedition(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#jungleReels .slot');
    
    // Enhanced symbol generation with depth bonuses
    const depth = document.getElementById('jungleDepth').value;
    let wildChance = 0.08;
    let scatterChance = 0.06;
    
    if (depth === 'deep') {
        wildChance = 0.12;
        scatterChance = 0.08;
    } else if (depth === 'heart') {
        wildChance = 0.15;
        scatterChance = 0.10;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = JUNGLE_SYMBOLS.SHAMAN; // Wild
        } else if (Math.random() < scatterChance) {
            symbol = JUNGLE_SYMBOLS.TOTEM; // Scatter
        } else if (Math.random() < 0.05) {
            symbol = JUNGLE_SYMBOLS.JINX; // Jinx curse
        } else {
            symbol = JUNGLE_REEL_SYMBOLS[Math.floor(Math.random() * 7)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkJungleWins(finalSymbols, totalBet);
}

function checkJungleWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for jungle treasures
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === JUNGLE_SYMBOLS.JAGUAR && count >= 3) {
            multiplier = count >= 5 ? 150 : count >= 4 ? 100 : 50;
            statusMessage = 'THE JAGUAR KING GRANTS HIS BLESSING!';
            jungleJinxGame.wildernessEnergy += count * 20;
        } else if (symbol === JUNGLE_SYMBOLS.TEMPLE && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 70 : 35;
            statusMessage = 'Ancient temple reveals its secrets!';
            jungleJinxGame.wildernessEnergy += count * 15;
        } else if (symbol === JUNGLE_SYMBOLS.PARROT && count >= 3) {
            multiplier = count >= 5 ? 80 : count >= 4 ? 55 : 28;
            statusMessage = 'Colorful parrots guide you to treasure!';
        } else if (symbol === JUNGLE_SYMBOLS.FLOWER && count >= 3) {
            multiplier = count >= 5 ? 60 : count >= 4 ? 40 : 20;
            statusMessage = 'Exotic flowers bloom with fortune!';
        } else if (symbol === JUNGLE_SYMBOLS.SNAKE && count >= 3) {
            multiplier = count >= 5 ? 45 : count >= 4 ? 30 : 15;
            statusMessage = 'Sacred serpents slither with luck!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier * jungleJinxGame.rainforestMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Shaman wild substitution bonus
    const shamanCount = symbols.filter(s => s === JUNGLE_SYMBOLS.SHAMAN).length;
    if (shamanCount > 0 && totalWin > 0) {
        const shamanMultiplier = 1 + (shamanCount * 0.5);
        totalWin *= shamanMultiplier;
        statusMessage += ` Shaman magic: ${shamanMultiplier}x!`;
        jungleJinxGame.shamanPower = true;
        
        // Trigger shaman effect
        document.getElementById('shamanEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('shamanEffect').style.opacity = '0';
            jungleJinxGame.shamanPower = false;
        }, 3000);
    }

    // Totem scatter bonus
    const totemCount = symbols.filter(s => s === JUNGLE_SYMBOLS.TOTEM).length;
    if (totemCount >= 3) {
        const freeSpinsAwarded = 10 + (totemCount - 3) * 5;
        jungleJinxGame.tribalSpins += freeSpinsAwarded;
        jungleJinxGame.totemCount += totemCount;
        statusMessage += ` ANCIENT TOTEMS AWAKEN! ${freeSpinsAwarded} tribal spins!`;
        
        // Increase multiplier based on totems found
        if (jungleJinxGame.totemCount >= 15) {
            jungleJinxGame.rainforestMultiplier += 1;
            jungleJinxGame.totemCount = 0;
            statusMessage += ' TOTEM MASTERY ACHIEVED!';
        }
    }

    // Jinx curse mechanics
    const jinxCount = symbols.filter(s => s === JUNGLE_SYMBOLS.JINX).length;
    if (jinxCount > 0) {
        jungleJinxGame.curseLevel += jinxCount;
        
        if (jinxCount >= 3) {
            // Jinx mode activation
            jungleJinxGame.jinxMode = true;
            totalWin *= (2 + jinxCount);
            statusMessage = `💀 JUNGLE JINX ACTIVATED! ${2 + jinxCount}x CURSE MULTIPLIER!`;
            
            // Trigger jinx effect
            document.getElementById('jinxEffect').style.opacity = '1';
            setTimeout(() => {
                document.getElementById('jinxEffect').style.opacity = '0';
                jungleJinxGame.jinxMode = false;
            }, 4000);
        } else if (totalWin > 0) {
            // Minor jinx bonus
            const jinxBonus = 1 + (jinxCount * 0.3);
            totalWin *= jinxBonus;
            statusMessage += ` Jinx spirits: ${jinxBonus.toFixed(1)}x!`;
        }
    }

    // Wilderness energy bonus
    if (jungleJinxGame.wildernessEnergy >= 100) {
        totalWin += totalBet * 25;
        statusMessage += ' WILDERNESS MASTERY BONUS!';
        jungleJinxGame.wildernessEnergy = 0;
    }

    // Curse level progression
    if (jungleJinxGame.curseLevel >= 20) {
        totalWin *= 5;
        statusMessage += ' ANCIENT CURSE UNLEASHED! 5x POWER!';
        jungleJinxGame.curseLevel = 0;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        jungleJinxGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#jungleReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The jungle keeps its secrets hidden...';
    }

    document.getElementById('jungleStatus').textContent = statusMessage;
    jungleJinxGame.isSpinning = false;
    updateJungleDisplay();
}

function updateJungleDisplay() {
    const spinButton = document.getElementById('enterJungle');
    spinButton.disabled = jungleJinxGame.isSpinning;
    spinButton.textContent = jungleJinxGame.isSpinning ? 'EXPLORING...' : 'ENTER THE JUNGLE';

    document.getElementById('jungleTribalSpins').textContent = jungleJinxGame.tribalSpins;
    document.getElementById('jungleLastWin').textContent = `${jungleJinxGame.lastWin} GA`;
    document.getElementById('curseLevel').textContent = jungleJinxGame.curseLevel;
    document.getElementById('totemCount').textContent = jungleJinxGame.totemCount;
    document.getElementById('shamanStatus').textContent = jungleJinxGame.shamanPower ? 'ACTIVE!' : 'DORMANT';
    document.getElementById('jinxMode').textContent = jungleJinxGame.jinxMode ? 'ACTIVE!' : 'INACTIVE';
    document.getElementById('wildernessEnergy').textContent = jungleJinxGame.wildernessEnergy;
    document.getElementById('rainforestMultiplier').textContent = `${jungleJinxGame.rainforestMultiplier}x`;
    
    // Update curse meter
    const curseLevel = (jungleJinxGame.curseLevel / 20) * 100;
    document.getElementById('curseMeter').style.width = `${Math.min(curseLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadJungleJinxGame();
});

