// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Viking Fury Game Implementation
const VIKING_SYMBOLS = ['⚔️', '🛡️', '⛵', '🪓', '🗡️', '🍻', '🦸', '🔨', '⚡', '🗺️'];

let vikingFuryGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    rageMode: false,
    battleLevel: 1,
    berserkerMultiplier: 1
};

function loadVikingFuryGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">VIKING FURY</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">BATTLE WAGER</label>
                        <input type="number" id="vikingBet" value="35" min="5" max="1000"
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startVikingBattle" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        CHARGE INTO BATTLE
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Battles:</span>
                            <span id="vikingFreeSpins" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Plunder:</span>
                            <span id="vikingLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Battle Level:</span>
                            <span id="battleLevel" class="text-yellow-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Berserker Mode:</span>
                            <span id="berserkerMode" class="text-orange-400">Calm</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300">WARRIOR'S GUIDE</h5>
                        <div class="text-xs space-y-1">
                            <div>⚔️ <span class="text-red-400">Sword:</span> 40x bet</div>
                            <div>🪓 <span class="text-orange-400">Axe:</span> 30x bet</div>
                            <div>🛡️ <span class="text-gray-400">Shield:</span> 20x bet</div>
                            <div>⛵ <span class="text-blue-400">Longship:</span> 15x bet</div>
                            <div>🗺️ <span class="text-yellow-400">Map:</span> Scatter bonus</div>
                            <div>⚡ <span class="text-purple-400">Thunder:</span> Wild symbol</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div id="vikingReels" class="grid grid-cols-5 gap-4 mb-6 h-64">
                        ${Array(20).fill(0).map((_, i) => 
                            `<div class="slot bg-red-900/20 rounded-lg flex items-center justify-center text-4xl border border-red-500/20 transition-all duration-300">⚔️</div>`
                        ).join('')}
                    </div>
                    <div id="vikingStatus" class="text-center text-lg font-semibold text-red-300 h-8">
                        Prepare for glorious battle!
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupVikingFuryGame();
}

function setupVikingFuryGame() {
    document.getElementById('startVikingBattle').addEventListener('click', startVikingBattle);
    updateVikingDisplay();
}

function startVikingBattle() {
    if (vikingFuryGame.isSpinning) return;

    const betAmount = parseInt(document.getElementById('vikingBet').value);

    if (vikingFuryGame.freeSpins === 0) {
        if (balance < betAmount) {
            document.getElementById('vikingStatus').textContent = 'Insufficient gold for battle!';
            return;
        }
        balance -= betAmount;
    } else {
        vikingFuryGame.freeSpins--;
    }

    vikingFuryGame.isSpinning = true;
    vikingFuryGame.lastWin = 0;
    updateBalance();
    updateVikingDisplay();
    document.getElementById('vikingStatus').textContent = 'Warriors charge into battle!';

    const slots = document.querySelectorAll('#vikingReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1800;
    let spinInterval = 60;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = VIKING_SYMBOLS[Math.floor(Math.random() * VIKING_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishVikingBattle(betAmount);
        }
    }, spinInterval);
}

function finishVikingBattle(betAmount) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#vikingReels .slot');
    slots.forEach(slot => {
        const symbol = VIKING_SYMBOLS[Math.floor(Math.random() * VIKING_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkVikingWins(finalSymbols, betAmount);
}

function checkVikingWins(symbols, betAmount) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for warrior combinations
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === '⚔️' && count >= 3) {
            multiplier = count >= 5 ? 40 : count >= 4 ? 20 : 10;
            statusMessage = 'LEGENDARY SWORD STRIKE! Victory is yours!';
            vikingFuryGame.rageMode = true;
        } else if (symbol === '🪓' && count >= 3) {
            multiplier = count >= 5 ? 30 : count >= 4 ? 15 : 8;
            statusMessage = 'Mighty axe cleaves through enemies!';
        } else if (symbol === '🛡️' && count >= 3) {
            multiplier = count >= 5 ? 20 : count >= 4 ? 12 : 6;
            statusMessage = 'Shield wall holds strong!';
        } else if (symbol === '⛵' && count >= 3) {
            multiplier = count >= 5 ? 15 : count >= 4 ? 10 : 5;
            statusMessage = 'Longship raids bring treasure!';
        } else if ((symbol === '🗡️' || symbol === '🔨') && count >= 3) {
            multiplier = count >= 5 ? 12 : count >= 4 ? 8 : 4;
            statusMessage = 'Warrior weapons prove their worth!';
        }

        if (multiplier > 0) {
            totalWin += betAmount * multiplier * vikingFuryGame.berserkerMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Thunder wild bonus (⚡ symbols)
    const wildCount = symbols.filter(s => s === '⚡').length;
    if (wildCount > 0) {
        vikingFuryGame.berserkerMultiplier = 1 + (wildCount * 0.5);
        if (totalWin > 0) {
            statusMessage += ' Thor\'s thunder amplifies your victory!';
        }
    } else {
        vikingFuryGame.berserkerMultiplier = 1;
    }

    // Scatter bonus (🗺️ Treasure Map)
    const scatterCount = symbols.filter(s => s === '🗺️').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 8 + (scatterCount - 3) * 4;
        vikingFuryGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` Treasure map found! +${freeSpinsAwarded} free raids!`;
        vikingFuryGame.battleLevel++;
    }

    // Berserker rage bonus
    if (vikingFuryGame.rageMode && totalWin > 0) {
        totalWin *= 1.5;
        statusMessage += ' BERSERKER RAGE INCREASES PLUNDER!';
        setTimeout(() => { vikingFuryGame.rageMode = false; }, 3000);
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        vikingFuryGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#vikingReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The battle yields no treasure this time.';
    }

    document.getElementById('vikingStatus').textContent = statusMessage;
    vikingFuryGame.isSpinning = false;
    updateVikingDisplay();
}

function updateVikingDisplay() {
    document.getElementById('vikingFreeSpins').textContent = vikingFuryGame.freeSpins;
    document.getElementById('vikingLastWin').textContent = `${vikingFuryGame.lastWin} GA`;
    document.getElementById('battleLevel').textContent = vikingFuryGame.battleLevel;
    document.getElementById('berserkerMode').textContent = vikingFuryGame.rageMode ? 'ENRAGED!' : 'Calm';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadVikingFuryGame();
});
