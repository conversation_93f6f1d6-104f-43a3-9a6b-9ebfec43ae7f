
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Kraken's Deep Game Implementation
const KRAKEN_SYMBOLS = {
    KRAKEN: '🐙',
    JELLYFISH: '🪼',
    ANCHOR: '⚓',
    SQUID: '🦑',
    WAVE: '🌊',
    FISH: '🐠',
    SHARK: '🦈',
    SKULL: '💀',
    DIVER: '🎯',
    LIGHTNING: '⚡'
};

const KRAKEN_REEL_SYMBOLS = [
    KRAKEN_SYMBOLS.KRAKEN, KRAKEN_SYMBOLS.JELLYFISH, KRAKEN_SYMBOLS.ANCHOR,
    KRAKEN_SYMBOLS.SQUID, KRAKEN_SYMBOLS.WAVE, KRAKEN_SYMBOLS.FISH,
    KRAKEN_SYMBOLS.SHARK, KRAKEN_SYMBOLS.SKULL, KRAKEN_SYMBOLS.DIVER, KRAKEN_SYMBOLS.LIGHTNING
];

let krakenDiveGame = {
    isSpinning: false,
    abyssSpins: 0,
    lastWin: 0,
    tentaclePower: 0,
    krakenRage: 0,
    depthLevel: 1,
    treasureChests: 0,
    leviathan: false,
    whirlpoolActive: false,
    pressureMultiplier: 1
};

function loadKrakenDiveGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <h4 class="text-xl font-bold mb-4 text-indigo-400 font-mono">KRAKEN'S DEEP</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-indigo-300">DIVE EQUIPMENT</label>
                        <input type="number" id="krakenBet" value="45" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-indigo-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-indigo-300">DIVE DEPTH</label>
                        <select id="krakenDepth" class="w-full bg-black/50 border border-indigo-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="shallow">Shallow Waters (3x)</option>
                            <option value="deep">Deep Abyss (6x)</option>
                            <option value="trench">Mariana Trench (12x)</option>
                        </select>
                    </div>
                    
                    <button id="diveToKraken" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        DIVE TO THE DEPTHS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Abyss Spins:</span>
                            <span id="krakenAbyssSpins" class="text-indigo-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Treasure:</span>
                            <span id="krakenLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Depth Level:</span>
                            <span id="depthLevel" class="text-blue-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Tentacle Power:</span>
                            <span id="tentaclePower" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Kraken Status:</span>
                            <span id="krakenStatus" class="text-red-400">SLEEPING</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-indigo-900/20 rounded-lg border border-indigo-500/20">
                        <h5 class="text-sm font-bold mb-2 text-indigo-300 font-mono">DEEP SEA TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div>🐙 <span class="text-purple-400">Kraken:</span> 200x bet</div>
                            <div>🦑 <span class="text-pink-400">Squid:</span> 120x bet</div>
                            <div>🦈 <span class="text-gray-400">Shark:</span> 100x bet</div>
                            <div>🪼 <span class="text-blue-400">Jellyfish:</span> 80x bet</div>
                            <div>🎯 <span class="text-yellow-400">Diver:</span> Wild substitute</div>
                            <div>⚡ <span class="text-cyan-400">Lightning:</span> Scatter bonus</div>
                            <div>💀 <span class="text-red-400">Skull:</span> Rage multiplier</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <div class="relative mb-6">
                        <div id="krakenReels" class="grid grid-cols-5 gap-2 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-indigo-900/20 rounded-lg flex items-center justify-center text-4xl border border-indigo-500/20 transition-all duration-300">🐙</div>`
                            ).join('')}
                        </div>
                        <div id="rageEffect" class="absolute inset-0 bg-gradient-to-t from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="leviathanEffect" class="absolute inset-0 bg-gradient-to-b from-purple-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="whirlpoolEffect" class="absolute inset-0 bg-gradient-radial from-indigo-400/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                    </div>
                    
                    <div id="krakenGameStatus" class="text-center text-lg font-semibold text-indigo-300 mb-4 h-8 font-mono">
                        The abyss calls from the deepest depths...
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-indigo-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">ABYSS STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Leviathan:</span>
                                    <span id="leviathanStatus" class="text-purple-400">DORMANT</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Whirlpool:</span>
                                    <span id="whirlpoolStatus" class="text-blue-400">CALM</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-indigo-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">RAGE METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="rageMeter" class="bg-gradient-to-r from-red-500 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-indigo-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PRESSURE</h6>
                            <div class="text-center">
                                <span id="pressureMultiplier" class="text-2xl text-indigo-400">1x</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupKrakenDiveGame();
}

function setupKrakenDiveGame() {
    document.getElementById('diveToKraken').addEventListener('click', diveToKrakenDepths);
    const reelsContainer = document.getElementById('krakenReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-indigo-900/20 rounded-lg p-2 text-center text-4xl border border-indigo-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = KRAKEN_REEL_SYMBOLS[i % KRAKEN_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateKrakenDisplay();
}

function diveToKrakenDepths() {
    if (krakenDiveGame.isSpinning) return;

    const bet = parseInt(document.getElementById('krakenBet').value);
    const depth = document.getElementById('krakenDepth').value;
    let totalBet;
    
    switch(depth) {
        case 'deep': totalBet = bet * 6; break;
        case 'trench': totalBet = bet * 12; break;
        default: totalBet = bet * 3;
    }

    if (krakenDiveGame.abyssSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('krakenGameStatus').textContent = 'INSUFFICIENT DIVING EQUIPMENT';
            return;
        }
        balance -= totalBet;
    } else {
        krakenDiveGame.abyssSpins--;
    }

    krakenDiveGame.isSpinning = true;
    krakenDiveGame.lastWin = 0;
    updateBalance();
    updateKrakenDisplay();
    document.getElementById('krakenGameStatus').textContent = 'Descending into the crushing depths...';

    const slots = document.querySelectorAll('#krakenReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Whirlpool effect during spin
    document.getElementById('whirlpoolEffect').style.opacity = '0.3';

    let spinDuration = 2500;
    let spinInterval = 100;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = KRAKEN_REEL_SYMBOLS[Math.floor(Math.random() * KRAKEN_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('whirlpoolEffect').style.opacity = '0';
            finishKrakenDive(totalBet);
        }
    }, spinInterval);
}

function finishKrakenDive(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#krakenReels .slot');
    
    // Enhanced symbol generation with depth bonuses
    const depth = document.getElementById('krakenDepth').value;
    let wildChance = 0.10;
    let scatterChance = 0.06;
    
    if (depth === 'deep') {
        wildChance = 0.14;
        scatterChance = 0.08;
    } else if (depth === 'trench') {
        wildChance = 0.18;
        scatterChance = 0.12;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = KRAKEN_SYMBOLS.DIVER; // Wild
        } else if (Math.random() < scatterChance) {
            symbol = KRAKEN_SYMBOLS.LIGHTNING; // Scatter
        } else if (Math.random() < 0.06) {
            symbol = KRAKEN_SYMBOLS.SKULL; // Rage trigger
        } else {
            symbol = KRAKEN_REEL_SYMBOLS[Math.floor(Math.random() * 7)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkKrakenWins(finalSymbols, totalBet);
}

function checkKrakenWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for deep sea treasures
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === KRAKEN_SYMBOLS.KRAKEN && count >= 3) {
            multiplier = count >= 5 ? 200 : count >= 4 ? 130 : 65;
            statusMessage = 'THE KRAKEN AWAKENS FROM ITS SLUMBER!';
            krakenDiveGame.tentaclePower += count * 25;
            krakenDiveGame.leviathan = true;
        } else if (symbol === KRAKEN_SYMBOLS.SQUID && count >= 3) {
            multiplier = count >= 5 ? 120 : count >= 4 ? 80 : 40;
            statusMessage = 'Giant squid guards ancient treasures!';
            krakenDiveGame.tentaclePower += count * 15;
        } else if (symbol === KRAKEN_SYMBOLS.SHARK && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 70 : 35;
            statusMessage = 'Apex predators circle the treasure!';
        } else if (symbol === KRAKEN_SYMBOLS.JELLYFISH && count >= 3) {
            multiplier = count >= 5 ? 80 : count >= 4 ? 55 : 28;
            statusMessage = 'Bioluminescent jellies light the way!';
        } else if (symbol === KRAKEN_SYMBOLS.ANCHOR && count >= 3) {
            multiplier = count >= 5 ? 60 : count >= 4 ? 40 : 20;
            statusMessage = 'Sunken anchors reveal shipwreck gold!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier * krakenDiveGame.pressureMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Diver wild substitution bonus
    const diverCount = symbols.filter(s => s === KRAKEN_SYMBOLS.DIVER).length;
    if (diverCount > 0 && totalWin > 0) {
        const diverMultiplier = 1 + (diverCount * 0.6);
        totalWin *= diverMultiplier;
        statusMessage += ` Deep sea diver: ${diverMultiplier.toFixed(1)}x!`;
    }

    // Lightning scatter bonus
    const lightningCount = symbols.filter(s => s === KRAKEN_SYMBOLS.LIGHTNING).length;
    if (lightningCount >= 3) {
        const freeSpinsAwarded = 15 + (lightningCount - 3) * 8;
        krakenDiveGame.abyssSpins += freeSpinsAwarded;
        krakenDiveGame.depthLevel++;
        statusMessage += ` ⚡ KRAKEN'S WRATH! ${freeSpinsAwarded} abyss spins!`;
        
        // Increase pressure multiplier
        krakenDiveGame.pressureMultiplier += 0.5;
    }

    // Skull rage mechanics
    const skullCount = symbols.filter(s => s === KRAKEN_SYMBOLS.SKULL).length;
    if (skullCount > 0) {
        krakenDiveGame.krakenRage += skullCount * 20;
        
        if (skullCount >= 3) {
            // Full rage mode
            totalWin *= (3 + skullCount);
            statusMessage = `💀 KRAKEN'S FURY UNLEASHED! ${3 + skullCount}x DEVASTATION!`;
            
            // Trigger rage effect
            document.getElementById('rageEffect').style.opacity = '1';
            setTimeout(() => {
                document.getElementById('rageEffect').style.opacity = '0';
            }, 4000);
        } else if (totalWin > 0) {
            // Minor rage bonus
            const rageBonus = 1 + (skullCount * 0.4);
            totalWin *= rageBonus;
            statusMessage += ` Kraken stirs: ${rageBonus.toFixed(1)}x!`;
        }
    }

    // Leviathan awakening bonus
    if (krakenDiveGame.leviathan && krakenDiveGame.tentaclePower >= 100) {
        const leviathanMultiplier = 8 + krakenDiveGame.depthLevel;
        totalWin *= leviathanMultiplier;
        statusMessage = `🐙 LEVIATHAN RISES! ${leviathanMultiplier}x TENTACLE DEVASTATION!`;
        krakenDiveGame.tentaclePower = 0;
        
        // Trigger leviathan effect
        document.getElementById('leviathanEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('leviathanEffect').style.opacity = '0';
            krakenDiveGame.leviathan = false;
        }, 5000);
    }

    // Whirlpool activation
    if (krakenDiveGame.krakenRage >= 100) {
        krakenDiveGame.whirlpoolActive = true;
        totalWin += totalBet * 40;
        statusMessage += ' ABYSSAL WHIRLPOOL ACTIVATED!';
        krakenDiveGame.krakenRage = 0;
        
        setTimeout(() => {
            krakenDiveGame.whirlpoolActive = false;
        }, 3000);
    }

    // Treasure chest collection
    const waveCount = symbols.filter(s => s === KRAKEN_SYMBOLS.WAVE).length;
    if (waveCount >= 4) {
        krakenDiveGame.treasureChests++;
        totalWin += totalBet * 15;
        statusMessage += ' TREASURE CHEST DISCOVERED!';
        
        if (krakenDiveGame.treasureChests >= 10) {
            totalWin *= 6;
            statusMessage += ' SUNKEN GALLEON FOUND! 6x MULTIPLIER!';
            krakenDiveGame.treasureChests = 0;
        }
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        krakenDiveGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#krakenReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The abyss guards its secrets jealously...';
    }

    document.getElementById('krakenGameStatus').textContent = statusMessage;
    krakenDiveGame.isSpinning = false;
    updateKrakenDisplay();
}

function updateKrakenDisplay() {
    const spinButton = document.getElementById('diveToKraken');
    spinButton.disabled = krakenDiveGame.isSpinning;
    spinButton.textContent = krakenDiveGame.isSpinning ? 'DIVING...' : 'DIVE TO THE DEPTHS';

    document.getElementById('krakenAbyssSpins').textContent = krakenDiveGame.abyssSpins;
    document.getElementById('krakenLastWin').textContent = `${krakenDiveGame.lastWin} GA`;
    document.getElementById('depthLevel').textContent = krakenDiveGame.depthLevel;
    document.getElementById('tentaclePower').textContent = krakenDiveGame.tentaclePower;
    document.getElementById('krakenStatus').textContent = krakenDiveGame.leviathan ? 'AWAKENED!' : 'SLEEPING';
    document.getElementById('leviathanStatus').textContent = krakenDiveGame.leviathan ? 'ACTIVE!' : 'DORMANT';
    document.getElementById('whirlpoolStatus').textContent = krakenDiveGame.whirlpoolActive ? 'RAGING!' : 'CALM';
    document.getElementById('pressureMultiplier').textContent = `${krakenDiveGame.pressureMultiplier}x`;
    
    // Update rage meter
    const rageLevel = (krakenDiveGame.krakenRage / 100) * 100;
    document.getElementById('rageMeter').style.width = `${Math.min(rageLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadKrakenDiveGame();
});

