// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadCyberHeistGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Mission Control -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                            <h4 class="text-xl font-bold mb-4 text-gray-300">NEON NOIR: CYBER HEIST</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">OPERATION STAKE</label>
                                <input type="number" id="heistBet" value="50" min="20" max="${balance}" 
                                       class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">HEIST TYPE</label>
                                <select id="heistType" class="w-full bg-black/50 border border-gray-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="data">Data Theft (Low Risk)</option>
                                    <option value="credits">Credit Heist (Medium Risk)</option>
                                    <option value="corporate">Corporate Vault (High Risk)</option>
                                    <option value="quantum">Quantum Bank (Extreme Risk)</option>
                                </select>
                            </div>
                            
                            <button id="startHeist" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                INITIATE HEIST
                            </button>
                            
                            <div class="text-center mb-4">
                                <div class="text-sm text-gray-400 mb-1">Stolen Data Value</div>
                                <div id="heistLoot" class="text-2xl font-bold text-green-400 neon-glow">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Security Alert Level</div>
                                <div id="heistAlert" class="text-xl font-bold text-yellow-400">0%</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Mission Progress</div>
                                <div id="heistProgress" class="text-lg font-bold text-blue-400">Ready</div>
                            </div>
                            
                            <!-- Mission Intel -->
                            <div class="bg-black/30 p-3 rounded-lg border border-gray-500/30 mb-4">
                                <h5 class="text-sm font-bold mb-2 text-gray-300">MISSION INTEL</h5>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Security Systems:</span>
                                        <span id="heistSecurity" class="text-red-400">Unknown</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Escape Route:</span>
                                        <span id="heistEscape" class="text-green-400">Secured</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Payout Multiplier:</span>
                                        <span id="heistMultiplier" class="text-purple-400">1.0x</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="heistActions" class="space-y-2 hidden">
                                <button id="heistHack" class="w-full py-2 rounded-lg font-bold bg-blue-600 hover:bg-blue-700 text-white">
                                    💻 HACK SYSTEM
                                </button>
                                <button id="heistSteal" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                    💰 STEAL DATA
                                </button>
                                <button id="heistEscapeBtn" class="w-full py-2 rounded-lg font-bold bg-purple-600 hover:bg-purple-700 text-white">
                                    🏃 ESCAPE NOW
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Heist Interface -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                            <div id="heistInterface" class="relative bg-gradient-to-br from-gray-900 to-slate-900 rounded-lg p-6 h-96 overflow-hidden">
                                <!-- Security Grid -->
                                <div class="absolute inset-4">
                                    <div class="grid grid-cols-10 grid-rows-8 gap-1 h-full" id="securityGrid">
                                        <!-- Security systems will be generated here -->
                                    </div>
                                </div>
                                
                                <!-- Player Avatar -->
                                <div id="playerAvatar" class="absolute bottom-4 left-4 text-2xl transition-all duration-500">
                                    🕵️
                                </div>
                                
                                <!-- Target Vault -->
                                <div id="targetVault" class="absolute top-4 right-4 text-3xl animate-pulse">
                                    🏦
                                </div>
                                
                                <!-- Security Alerts -->
                                <div id="securityAlerts" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 transition-opacity duration-1000">
                                    <div class="text-4xl text-red-500 animate-bounce">🚨</div>
                                </div>
                                
                                <!-- Data Stream Effects -->
                                <div id="dataStream" class="absolute inset-0 pointer-events-none opacity-0 transition-opacity duration-1000">
                                    <!-- Matrix-style data rain will be added here -->
                                </div>
                            </div>
                            <div id="heistStatus" class="text-center mt-4 text-lg font-semibold">Plan your heist... Every move matters in the digital shadows</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeCyberHeist();
        }
        
        let cyberHeistGame = {
            isActive: false,
            heistType: 'data',
            betAmount: 50,
            lootValue: 0,
            alertLevel: 0,
            maxAlert: 100,
            progress: 0,
            securitySystems: [],
            playerPosition: { x: 0, y: 7 },
            targetPosition: { x: 9, y: 0 },
            multiplier: 1.0,
            hackedSystems: 0
        };
        
        function initializeCyberHeist() {
            document.getElementById('startHeist').addEventListener('click', startCyberHeist);
            document.getElementById('heistHack').addEventListener('click', hackSecuritySystem);
            document.getElementById('heistSteal').addEventListener('click', stealHeistData);
            document.getElementById('heistEscapeBtn').addEventListener('click', escapeHeist);
            document.getElementById('heistType').addEventListener('change', updateHeistType);
            
            generateSecurityGrid();
            updateHeistType();
        }
        
        function updateHeistType() {
            cyberHeistGame.heistType = document.getElementById('heistType').value;
            
            const typeInfo = {
                'data': { security: 'Light', multiplier: 1.5 },
                'credits': { security: 'Moderate', multiplier: 2.5 },
                'corporate': { security: 'Heavy', multiplier: 4.0 },
                'quantum': { security: 'Maximum', multiplier: 8.0 }
            };
            
            const info = typeInfo[cyberHeistGame.heistType];
            document.getElementById('heistSecurity').textContent = info.security;
            document.getElementById('heistMultiplier').textContent = info.multiplier + 'x';
            cyberHeistGame.multiplier = info.multiplier;
        }
        
        function generateSecurityGrid() {
            const grid = document.getElementById('securityGrid');
            grid.innerHTML = '';
            
            const securityTypes = ['🔒', '📹', '🚨', '⚡', '🛡️'];
            cyberHeistGame.securitySystems = [];
            
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 10; col++) {
                    const cell = document.createElement('div');
                    cell.className = 'bg-black/40 border border-gray-400/30 rounded flex items-center justify-center text-sm transition-all duration-300';
                    cell.dataset.row = row;
                    cell.dataset.col = col;
                    
                    // Add security systems randomly
                    if (Math.random() < 0.3 && !(row === 7 && col === 0) && !(row === 0 && col === 9)) {
                        const securityType = securityTypes[Math.floor(Math.random() * securityTypes.length)];
                        cell.textContent = securityType;
                        cell.classList.add('bg-red-900/30', 'border-red-400/50');
                        
                        cyberHeistGame.securitySystems.push({
                            row, col, type: securityType, active: true, hacked: false
                        });
                    }
                    
                    grid.appendChild(cell);
                }
            }
        }
        
        function startCyberHeist() {
            const betAmount = parseInt(document.getElementById('heistBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            balance -= betAmount;
            updateBalance();
            
            cyberHeistGame.isActive = true;
            cyberHeistGame.betAmount = betAmount;
            cyberHeistGame.lootValue = 0;
            cyberHeistGame.alertLevel = 0;
            cyberHeistGame.progress = 0;
            cyberHeistGame.hackedSystems = 0;
            cyberHeistGame.playerPosition = { x: 0, y: 7 };
            
            document.getElementById('startHeist').disabled = true;
            document.getElementById('heistActions').classList.remove('hidden');
            document.getElementById('heistStatus').textContent = 'Heist initiated... Infiltrating the digital fortress';
            
            activateDataStream();
            updateHeistDisplay();
        }
        
        function activateDataStream() {
            const dataStream = document.getElementById('dataStream');
            dataStream.innerHTML = '';
            
            // Create matrix-style falling characters
            for (let i = 0; i < 20; i++) {
                const stream = document.createElement('div');
                stream.className = 'absolute text-green-400 text-xs font-mono opacity-50 animate-pulse';
                stream.style.left = Math.random() * 100 + '%';
                stream.style.top = Math.random() * 100 + '%';
                stream.style.animationDelay = Math.random() * 2 + 's';
                stream.textContent = Math.random().toString(36).substring(2, 8);
                dataStream.appendChild(stream);
            }
            
            dataStream.style.opacity = '1';
        }
        
        function hackSecuritySystem() {
            if (!cyberHeistGame.isActive) return;
            
            // Find nearby security systems
            const nearbySystem = findNearestSecuritySystem();
            
            if (nearbySystem) {
                nearbySystem.hacked = true;
                cyberHeistGame.hackedSystems++;
                
                // Reduce alert risk for hacked systems
                cyberHeistGame.alertLevel = Math.max(0, cyberHeistGame.alertLevel - 5);
                
                // Mark as hacked in grid
                const cell = document.querySelector(`[data-row="${nearbySystem.row}"][data-col="${nearbySystem.col}"]`);
                cell.classList.remove('bg-red-900/30', 'border-red-400/50');
                cell.classList.add('bg-green-900/30', 'border-green-400/50');
                
                const hackValue = cyberHeistGame.betAmount * 0.2 * cyberHeistGame.multiplier;
                cyberHeistGame.lootValue += hackValue;
                
                document.getElementById('heistStatus').textContent = `Security system hacked! +$${Math.floor(hackValue)} bonus`;
            } else {
                cyberHeistGame.alertLevel += 10;
                document.getElementById('heistStatus').textContent = 'No nearby systems to hack... Security alert increased!';
            }
            
            updateHeistDisplay();
            checkHeistStatus();
        }
        
        function findNearestSecuritySystem() {
            const playerPos = cyberHeistGame.playerPosition;
            
            return cyberHeistGame.securitySystems.find(system => {
                if (system.hacked) return false;
                
                const distance = Math.abs(system.row - playerPos.y) + Math.abs(system.col - playerPos.x);
                return distance <= 2; // Within 2 cells
            });
        }
        
        function stealHeistData() {
            if (!cyberHeistGame.isActive) return;
            
            cyberHeistGame.progress += 25;
            
            // Calculate risk based on heist type
            const riskFactors = { 'data': 8, 'credits': 15, 'corporate': 25, 'quantum': 40 };
            const baseRisk = riskFactors[cyberHeistGame.heistType];
            const hackBonus = cyberHeistGame.hackedSystems * 2;
            const totalRisk = Math.max(5, baseRisk - hackBonus);
            
            cyberHeistGame.alertLevel += totalRisk;
            
            // Calculate data value
            const dataValue = cyberHeistGame.betAmount * 0.5 * cyberHeistGame.multiplier;
            cyberHeistGame.lootValue += dataValue;
            
            // Move player towards target
            movePlayerTowardsTarget();
            
            document.getElementById('heistStatus').textContent = `Data packet stolen! +$${Math.floor(dataValue)} (Security alert: +${totalRisk}%)`;
            
            updateHeistDisplay();
            checkHeistStatus();
        }
        
        function movePlayerTowardsTarget() {
            const player = cyberHeistGame.playerPosition;
            const target = cyberHeistGame.targetPosition;
            
            if (player.x < target.x) player.x++;
            else if (player.y > target.y) player.y--;
            
            // Update player visual position
            const playerAvatar = document.getElementById('playerAvatar');
            const cellSize = 100 / 10; // 10 columns
            const rowSize = 100 / 8;   // 8 rows
            
            playerAvatar.style.left = (player.x * cellSize + 1) + '%';
            playerAvatar.style.bottom = ((7 - player.y) * rowSize + 1) + '%';
            
            // Check if reached target
            if (player.x === target.x && player.y === target.y) {
                cyberHeistGame.progress = 100;
                document.getElementById('heistStatus').textContent = 'Target reached! Primary objective complete!';
                
                // Bonus for reaching target
                const bonusValue = cyberHeistGame.betAmount * cyberHeistGame.multiplier;
                cyberHeistGame.lootValue += bonusValue;
            }
        }
        
        function escapeHeist() {
            if (cyberHeistGame.lootValue > 0) {
                balance += cyberHeistGame.lootValue;
                updateBalance();
                
                document.getElementById('heistStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Successful escape! Stole $${Math.floor(cyberHeistGame.lootValue)}!</span>`;
            } else {
                document.getElementById('heistStatus').innerHTML = 
                    `<span class="text-gray-400">Clean escape... but no data stolen</span>`;
            }
            
            setTimeout(() => {
                endCyberHeist();
            }, 3000);
        }
        
        function checkHeistStatus() {
            if (cyberHeistGame.alertLevel >= cyberHeistGame.maxAlert) {
                // Security breach - caught!
                const securityAlerts = document.getElementById('securityAlerts');
                securityAlerts.style.opacity = '1';
                
                document.getElementById('heistStatus').innerHTML = 
                    `<span class="text-red-400">🚨 SECURITY BREACH! You've been caught! Lost all stolen data! 🚨</span>`;
                
                setTimeout(() => {
                    endCyberHeist();
                }, 4000);
            } else if (cyberHeistGame.progress >= 100) {
                // Mission complete bonus
                const completionBonus = cyberHeistGame.betAmount * cyberHeistGame.multiplier * 2;
                cyberHeistGame.lootValue += completionBonus;
                
                document.getElementById('heistStatus').innerHTML = 
                    `<span class="text-cyan-400">MISSION COMPLETE! +$${Math.floor(completionBonus)} completion bonus!</span>`;
            }
        }
        
        function updateHeistDisplay() {
            document.getElementById('heistLoot').textContent = '$' + Math.floor(cyberHeistGame.lootValue);
            document.getElementById('heistAlert').textContent = Math.floor(cyberHeistGame.alertLevel) + '%';
            document.getElementById('heistProgress').textContent = cyberHeistGame.progress + '%';
            
            // Update escape route status based on alert level
            if (cyberHeistGame.alertLevel < 30) {
                document.getElementById('heistEscape').textContent = 'Clear';
                document.getElementById('heistEscape').className = 'text-green-400';
            } else if (cyberHeistGame.alertLevel < 70) {
                document.getElementById('heistEscape').textContent = 'Compromised';
                document.getElementById('heistEscape').className = 'text-yellow-400';
            } else {
                document.getElementById('heistEscape').textContent = 'Blocked';
                document.getElementById('heistEscape').className = 'text-red-400';
            }
        }
        
        function endCyberHeist() {
            cyberHeistGame.isActive = false;
            
            // Reset UI
            document.getElementById('startHeist').disabled = false;
            document.getElementById('heistActions').classList.add('hidden');
            document.getElementById('heistLoot').textContent = '0 GA';
            document.getElementById('heistAlert').textContent = '0%';
            document.getElementById('heistProgress').textContent = 'Ready';
            document.getElementById('heistEscape').textContent = 'Secured';
            document.getElementById('heistEscape').className = 'text-green-400';
            document.getElementById('securityAlerts').style.opacity = '0';
            document.getElementById('dataStream').style.opacity = '0';
            
            // Reset player position
            cyberHeistGame.playerPosition = { x: 0, y: 7 };
            const playerAvatar = document.getElementById('playerAvatar');
            playerAvatar.style.left = '1%';
            playerAvatar.style.bottom = '1%';
            
            document.getElementById('heistStatus').textContent = 'Plan your heist... Every move matters in the digital shadows';
            
            // Generate new security layout
            generateSecurityGrid();
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCyberHeistGame();
});