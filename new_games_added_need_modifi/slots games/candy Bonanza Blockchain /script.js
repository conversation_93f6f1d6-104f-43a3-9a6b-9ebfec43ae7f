// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Candy Bonanza Game Implementation ---

// Game constants for Candy Bonanza
const CANDY_SYMBOLS = {
    WILD: '🍭',      // Lollipop
    SCATTER: '🌟',    // Star Candy
    BONUS: '🍬',      // Wrapped Candy
    CHOCOLATE: '🍫',
    DOUGHNUT: '🍩',
    CAKE: '🍰',
    CUPCAKE: '🧁',
    CUSTARD: '🍮',
    COOKIE: '🍪',
};

const CANDY_REEL_SYMBOLS = [
    CANDY_SYMBOLS.CHOCOLATE, CANDY_SYMBOLS.DOUGHNUT, CANDY_SYMBOLS.CAKE, CANDY_SYMBOLS.CUPCAKE,
    CANDY_SYMBOLS.CUSTARD, CANDY_SYMBOLS.COOKIE, CANDY_SYMBOLS.WILD, CANDY_SYMBOLS.SCATTER, CANDY_SYMBOLS.BONUS
];

// Define a comprehensive set of paylines for the 5x4 grid.
const CANDY_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes & Chevrons
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    // Diagonals
    [0, 6, 12, 18], [4, 8, 12, 16],
    // Other patterns
    [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4]
];

// Game state object
let candyBonanzaGame = {
    isSpinning: false,
    sugarRushSpins: 0,
    sugarHighLevel: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadCandyBonanzaGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h4 class="text-xl font-bold mb-4 text-gray-400">CANDY BONANZA</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">TOTAL BET</label>
                        <input type="number" id="candyBet" value="40" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-gray-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        SPIN THE REELS
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Win:</span>
                            <span id="candyLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Sugar Rush Spins:</span>
                            <span id="sugarRush" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-gray-500/20">
                        <h5 class="text-sm font-bold mb-2 text-gray-300">SUGAR HIGH</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="sugarMeter" class="bg-gradient-to-r from-pink-500 to-yellow-400 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="candyPowerStatus" class="text-xs text-center text-gray-400">Fill the meter for a sweet bonus! (0%)</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <div id="candyReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>
                    
                    <div id="candyStatus" class="text-center text-lg font-semibold text-gray-400 mb-4 h-8">
                        Match delicious candies for sweet rewards.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${CANDY_SYMBOLS.CHOCOLATE} x5:</span><span class="text-cyan-400">100x</span></div>
                                <div class="flex justify-between"><span>${CANDY_SYMBOLS.DOUGHNUT} x5:</span><span class="text-blue-400">50x</span></div>
                                <div class="flex justify-between"><span>${CANDY_SYMBOLS.CAKE} x5:</span><span class="text-gray-400">25x</span></div>
                                <div class="flex justify-between"><span>${CANDY_SYMBOLS.CUPCAKE} x5:</span><span class="text-green-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">${CANDY_SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter.</div>
                                <div><span class="text-purple-400">${CANDY_SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Sugar Rush Spins.</div>
                                <div><span class="text-pink-400">${CANDY_SYMBOLS.BONUS} Bonus:</span> Charges the Sugar High meter for a win multiplier.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupCandyBonanzaGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupCandyBonanzaGame() {
    document.getElementById('startSpin').addEventListener('click', startCandySpin);
    const reelsContainer = document.getElementById('candyReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-gray-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = CANDY_REEL_SYMBOLS[i % CANDY_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateCandyDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateCandyDisplay() {
    const spinButton = document.getElementById('startSpin');
    spinButton.disabled = candyBonanzaGame.isSpinning;
    spinButton.textContent = candyBonanzaGame.isSpinning ? 'SPINNING...' : 'SPIN THE REELS';

    document.getElementById('sugarRush').textContent = candyBonanzaGame.sugarRushSpins;
    document.getElementById('sugarMeter').style.width = `${candyBonanzaGame.sugarHighLevel}%`;
    document.getElementById('candyPowerStatus').textContent = `Fill the meter for a sweet bonus! (${candyBonanzaGame.sugarHighLevel}%)`;
    document.getElementById('candyLastWin').textContent = `${candyBonanzaGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function startCandySpin() {
    if (candyBonanzaGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('candyBet').value);

    if (candyBonanzaGame.sugarRushSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('candyStatus').textContent = 'Not enough funds!';
            return;
        }
        balance -= totalBet;
    } else {
        candyBonanzaGame.sugarRushSpins--;
    }

    candyBonanzaGame.isSpinning = true;
    candyBonanzaGame.lastWin = 0;
    updateBalance();
    updateCandyDisplay();
    document.getElementById('candyStatus').textContent = 'Spinning for sweet wins...';

    const slots = document.querySelectorAll('#candyReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = CANDY_REEL_SYMBOLS[Math.floor(Math.random() * CANDY_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishCandySpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishCandySpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#candyReels .slot');
    slots.forEach(slot => {
        const symbol = CANDY_REEL_SYMBOLS[Math.floor(Math.random() * CANDY_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkCandyWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and special symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkCandyWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // 1. Check for payline wins
    CANDY_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === CANDY_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== CANDY_SYMBOLS.WILD && symbols[index] !== CANDY_SYMBOLS.SCATTER) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        if (lineSymbol === CANDY_SYMBOLS.SCATTER) return;

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === CANDY_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getCandyMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 20; // Standardize line bet
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins (Sugar Rush Spins)
    const scatterCount = symbols.filter(s => s === CANDY_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 5 + (scatterCount - 3) * 2;
        candyBonanzaGame.sugarRushSpins += freeSpinsWon;
        statusMessage = `🌟 ${freeSpinsWon} Sugar Rush spins awarded!`;
    }
    
    // 3. Collect Bonus symbols
    const bonusCount = symbols.filter(s => s === CANDY_SYMBOLS.BONUS).length;
    if (bonusCount > 0) {
        candyBonanzaGame.sugarHighLevel = Math.min(100, candyBonanzaGame.sugarHighLevel + bonusCount * 10);
    }

    // 4. Check for Sugar High bonus
    if (candyBonanzaGame.sugarHighLevel >= 100 && totalWin > 0) {
        const powerMultiplier = Math.floor(Math.random() * 4) + 2; // 2x to 5x multiplier
        totalWin *= powerMultiplier;
        candyBonanzaGame.sugarHighLevel = 0; // Reset meter
        statusMessage = `🍬 SUGAR HIGH! Your win is multiplied by ${powerMultiplier}x!`;
    }

    // 5. Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        candyBonanzaGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('SUGAR HIGH')) {
            statusMessage = statusMessage ? statusMessage : `Sweet win! You won ${totalWin} GA!`;
        }

        const slots = document.querySelectorAll('#candyReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'No win this time. Try again!';
    }
    
    document.getElementById('candyStatus').textContent = statusMessage;
    candyBonanzaGame.isSpinning = false;
    updateCandyDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getCandyMultiplier(symbol, count) {
    const multipliers = {
        [CANDY_SYMBOLS.CHOCOLATE]:   { 3: 20, 4: 50, 5: 100 },
        [CANDY_SYMBOLS.DOUGHNUT]:     { 3: 10, 4: 25, 5: 50 },
        [CANDY_SYMBOLS.CAKE]:    { 3: 8,  4: 20, 5: 25 },
        [CANDY_SYMBOLS.CUPCAKE]: { 3: 5,  4: 10, 5: 15 },
        [CANDY_SYMBOLS.CUSTARD]:     { 3: 3,  4: 8,  5: 12 },
        [CANDY_SYMBOLS.COOKIE]:    { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCandyBonanzaGame();
});