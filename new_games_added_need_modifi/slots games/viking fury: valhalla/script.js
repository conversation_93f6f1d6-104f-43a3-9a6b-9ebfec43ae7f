// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Viking Fury: Valhalla Game Implementation
const VALHALLA_SYMBOLS = {
    ODIN: '👁️',
    THOR: '🔨',
    LOKI: '🐍',
    VALKYRIE: '👸',
    FENRIR: '🐺',
    MJOLNIR: '⚡',
    RAVEN: '🦅',
    RUNE_STONE: '🗿',
    WILD: '⚔️',
    SCATTER: '🌈'
};

const VALHALLA_REEL_SYMBOLS = [
    VALHALLA_SYMBOLS.ODIN, VALHALLA_SYMBOLS.THOR, VALHALLA_SYMBOLS.LOKI, VALHALLA_SYMBOLS.VALKYRIE,
    VALHALLA_SYMBOLS.FENRIR, VALHALLA_SYMBOLS.MJOLNIR, VALHALLA_SYMBOLS.RAVEN, VALHALLA_SYMBOLS.RUNE_STONE,
    VALHALLA_SYMBOLS.WILD, VALHALLA_SYMBOLS.SCATTER
];

let valhallaGame = {
    isSpinning: false,
    realm: 'midgard',
    betAmount: 30,
    spoils: 0,
    cascadeMultiplier: 1,
    divineFavor: 0,
    symbols: VALHALLA_REEL_SYMBOLS,
    reels: [[], [], [], [], [], []],
    cascadeLevel: 0,
    ragnarokActive: false,
    odinsPower: false,
    thorsMight: false,
    lokisTrickery: false,
    valkyrieBlessing: false,
    fenrirRage: false,
    runicMagic: 0
};

function loadValhallaGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400 font-mono">VIKING FURY: VALHALLA</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">WARRIOR'S TRIBUTE</label>
                        <input type="number" id="valhallaBet" value="30" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">REALM OF BATTLE</label>
                        <select id="valhallaRealm" class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="midgard">Midgard (8x)</option>
                            <option value="asgard">Asgard (16x)</option>
                            <option value="ragnarok">Ragnarok (32x)</option>
                        </select>
                    </div>
                    
                    <button id="enterValhalla" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        ENTER VALHALLA
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Divine Favor:</span>
                            <span id="valhallaDivineFavor" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">War Spoils:</span>
                            <span id="valhallaSpoils" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Cascade Level:</span>
                            <span id="valhallaCascade" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Runic Magic:</span>
                            <span id="valhallaRunic" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Cascade Multi:</span>
                            <span id="valhallaCascadeMulti" class="text-orange-400">1x</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/20">
                        <h5 class="text-sm font-bold mb-2 text-blue-300 font-mono">DIVINE POWERS</h5>
                        <div class="text-xs space-y-1">
                            <div>👁️ <span class="text-gray-400">Odin:</span> <span id="valhallaOdin" class="text-blue-400">Ready</span></div>
                            <div>🔨 <span class="text-yellow-400">Thor:</span> <span id="valhallaThor" class="text-blue-400">Ready</span></div>
                            <div>🐍 <span class="text-green-400">Loki:</span> <span id="valhallaLoki" class="text-blue-400">Ready</span></div>
                            <div>👸 <span class="text-pink-400">Valkyrie:</span> <span id="valhallaValkyrie" class="text-blue-400">Ready</span></div>
                            <div>🐺 <span class="text-red-400">Fenrir:</span> <span id="valhallaFenrir" class="text-blue-400">Ready</span></div>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300 font-mono">RAGNAROK STATUS</h5>
                        <div class="text-center">
                            <span id="valhallaRagnarok" class="text-red-400">DORMANT</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <div class="relative mb-6">
                        <div id="valhallaReels" class="grid grid-cols-6 gap-2 h-80">
                            ${Array(30).fill(0).map((_, i) => 
                                `<div class="slot bg-blue-900/20 rounded-lg flex items-center justify-content text-2xl border border-blue-500/20 transition-all duration-300">👁️</div>`
                            ).join('')}
                        </div>
                        <div id="odinsPowerEffect" class="absolute inset-0 bg-gradient-to-t from-gray-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="thorsMightEffect" class="absolute inset-0 bg-gradient-to-b from-yellow-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="lokisTrickeryEffect" class="absolute inset-0 bg-gradient-radial from-green-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="valkyrieEffect" class="absolute inset-0 bg-gradient-conic from-pink-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="fenrirRageEffect" class="absolute inset-0 bg-gradient-to-r from-red-500/10 via-white/10 to-red-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                        <div id="ragnarokMode" class="absolute inset-0 bg-gradient-conic from-red-500/40 via-orange-500/30 to-yellow-500/40 rounded-lg opacity-0 transition-all duration-5000"></div>
                    </div>
                    
                    <div id="valhallaStatus" class="text-center text-lg font-semibold text-blue-300 mb-4 h-8 font-mono">
                        The gates await worthy warriors...
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-blue-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DIVINE FAVOR</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="divineFavorMeter" class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="favorLevel" class="text-blue-400">0%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-blue-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">REALM POWER</h6>
                            <div class="text-center">
                                <span id="realmPower" class="text-blue-400">MIDGARD</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-blue-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CASCADES</h6>
                            <div class="text-center">
                                <span id="cascadeCount" class="text-purple-400">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupValhallaGame();
}

function setupValhallaGame() {
    document.getElementById('enterValhalla').addEventListener('click', enterValhalla);
    document.getElementById('valhallaRealm').addEventListener('change', updateValhallaRealm);
    
    generateValhallaReels();
    updateValhallaRealm();
}

function updateValhallaRealm() {
    valhallaGame.realm = document.getElementById('valhallaRealm').value;
    document.getElementById('realmPower').textContent = valhallaGame.realm.toUpperCase();
}

function generateValhallaReels() {
    const reelsContainer = document.getElementById('valhallaReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 30; i++) { // 6x5 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-blue-900/20 rounded-lg p-1 text-center text-2xl border border-blue-500/20 flex items-center justify-center h-12 transition-all duration-300';
        slot.textContent = VALHALLA_REEL_SYMBOLS[i % VALHALLA_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateValhallaDisplay();
}

function enterValhalla() {
    if (valhallaGame.isSpinning) return;
    
    const betAmount = parseInt(document.getElementById('valhallaBet').value);
    const realm = document.getElementById('valhallaRealm').value;
    let totalBet;
    
    switch(realm) {
        case 'asgard': totalBet = betAmount * 16; break;
        case 'ragnarok': totalBet = betAmount * 32; break;
        default: totalBet = betAmount * 8;
    }
    
    if (totalBet > balance) {
        document.getElementById('valhallaStatus').textContent = 'Insufficient tribute for the gods!';
        return;
    }
    
    balance -= totalBet;
    updateBalance();
    
    valhallaGame.isSpinning = true;
    valhallaGame.betAmount = totalBet;
    valhallaGame.spoils = 0;
    valhallaGame.cascadeLevel = 0;
    valhallaGame.cascadeMultiplier = 1;
    
    document.getElementById('enterValhalla').disabled = true;
    document.getElementById('valhallaStatus').textContent = 'The gates of Valhalla open...';
    
    animateValhallaEntry().then(() => {
        checkValhallaWins();
    });
}

function animateValhallaEntry() {
    return new Promise((resolve) => {
        const realmEffects = {
            'midgard': { wildChance: 0.15, scatterChance: 0.10 },
            'asgard': { wildChance: 0.22, scatterChance: 0.16 },
            'ragnarok': { wildChance: 0.35, scatterChance: 0.25 }
        };
        
        const effects = realmEffects[valhallaGame.realm];
        
        let reelDelay = 0;
        for (let reel = 0; reel < 6; reel++) {
            setTimeout(() => {
                valhallaGame.reels[reel] = [];
                for (let row = 0; row < 5; row++) {
                    let symbol;
                    if (Math.random() < effects.wildChance) {
                        symbol = VALHALLA_SYMBOLS.WILD;
                    } else if (Math.random() < effects.scatterChance) {
                        symbol = VALHALLA_SYMBOLS.SCATTER;
                    } else {
                        symbol = valhallaGame.symbols[Math.floor(Math.random() * (valhallaGame.symbols.length - 2))];
                    }
                    valhallaGame.reels[reel].push(symbol);
                }
                
                displayValhallaReels();
                
                if (reel === 5) {
                    setTimeout(resolve, 300);
                }
            }, reelDelay);
            reelDelay += 150;
        }
    });
}

function displayValhallaReels() {
    const slots = document.querySelectorAll('#valhallaReels .slot');
    let index = 0;
    
    for (let reel = 0; reel < 6; reel++) {
        for (let row = 0; row < 5; row++) {
            if (valhallaGame.reels[reel] && valhallaGame.reels[reel][row]) {
                slots[index].textContent = valhallaGame.reels[reel][row];
            }
            index++;
        }
    }
}

function checkValhallaWins() {
    const wins = findValhallaWins();
    
    if (wins.length > 0) {
        valhallaGame.cascadeLevel++;
        const realmBonus = { 'midgard': 1.2, 'asgard': 1.5, 'ragnarok': 2.0 }[valhallaGame.realm];
        valhallaGame.cascadeMultiplier = 1 + (valhallaGame.cascadeLevel * 0.5 * realmBonus);
        
        const payout = calculateValhallaPayout(wins);
        valhallaGame.spoils += payout;
        valhallaGame.divineFavor += wins.length;
        
        balance += payout;
        updateBalance();
        
        updateValhallaDisplay();
        highlightValhallaWins(wins);
        
        // Check for divine features
        checkDivineFeatures();
        
        document.getElementById('valhallaStatus').innerHTML = 
            `<span class="text-yellow-400 neon-glow">Victory! ${wins.length} combinations - ${payout} GA</span>`;
        
        // Cascade after delay
        setTimeout(() => {
            cascadeValhallaSymbols(wins);
        }, 2000);
    } else {
        endValhallaSpin();
    }
}

function findValhallaWins() {
    const wins = [];
    const symbolCounts = {};
    
    // Count all symbols
    for (let reel = 0; reel < 6; reel++) {
        for (let row = 0; row < 5; row++) {
            const symbol = valhallaGame.reels[reel][row];
            if (!symbolCounts[symbol]) {
                symbolCounts[symbol] = [];
            }
            symbolCounts[symbol].push({ reel, row });
        }
    }
    
    // Check for winning combinations (8+ matching symbols)
    for (const [symbol, positions] of Object.entries(symbolCounts)) {
        if (symbol !== VALHALLA_SYMBOLS.WILD && symbol !== VALHALLA_SYMBOLS.SCATTER && positions.length >= 8) {
            wins.push({ symbol, positions, count: positions.length });
        }
    }
    
    return wins;
}

function calculateValhallaPayout(wins) {
    let totalPayout = 0;
    
    wins.forEach(win => {
        let multiplier = 0;
        
        if (win.symbol === VALHALLA_SYMBOLS.ODIN) {
            multiplier = win.count >= 15 ? 1000 : win.count >= 12 ? 500 : win.count >= 8 ? 200 : 0;
        } else if (win.symbol === VALHALLA_SYMBOLS.THOR) {
            multiplier = win.count >= 15 ? 800 : win.count >= 12 ? 400 : win.count >= 8 ? 150 : 0;
        } else if (win.symbol === VALHALLA_SYMBOLS.LOKI) {
            multiplier = win.count >= 15 ? 600 : win.count >= 12 ? 300 : win.count >= 8 ? 120 : 0;
        } else if (win.symbol === VALHALLA_SYMBOLS.VALKYRIE) {
            multiplier = win.count >= 15 ? 500 : win.count >= 12 ? 250 : win.count >= 8 ? 100 : 0;
        } else if (win.symbol === VALHALLA_SYMBOLS.FENRIR) {
            multiplier = win.count >= 15 ? 400 : win.count >= 12 ? 200 : win.count >= 8 ? 80 : 0;
        } else {
            multiplier = win.count >= 15 ? 300 : win.count >= 12 ? 150 : win.count >= 8 ? 60 : 0;
        }
        
        totalPayout += valhallaGame.betAmount * multiplier * valhallaGame.cascadeMultiplier;
    });
    
    return Math.round(totalPayout);
}

function highlightValhallaWins(wins) {
    const slots = document.querySelectorAll('#valhallaReels .slot');
    
    wins.forEach(win => {
        win.positions.forEach(pos => {
            const index = pos.reel * 5 + pos.row;
            slots[index].classList.add('win-highlight');
        });
    });
}

function checkDivineFeatures() {
    // Odin's Power (All-seeing wisdom)
    if (valhallaGame.divineFavor >= 20 && Math.random() < 0.4) {
        valhallaGame.odinsPower = true;
        triggerOdinsPower();
    }
    
    // Thor's Might (Thunder strike)
    if (valhallaGame.divineFavor >= 15 && Math.random() < 0.35) {
        valhallaGame.thorsMight = true;
        triggerThorsMight();
    }
    
    // Loki's Trickery (Symbol transformation)
    if (valhallaGame.divineFavor >= 12 && Math.random() < 0.3) {
        valhallaGame.lokisTrickery = true;
        triggerLokisTrickery();
    }
    
    // Valkyrie's Blessing (Multiplier boost)
    if (valhallaGame.divineFavor >= 10 && Math.random() < 0.25) {
        valhallaGame.valkyrieBlessing = true;
        triggerValkyrieBlessing();
    }
    
    // Fenrir's Rage (Wild expansion)
    if (valhallaGame.divineFavor >= 8 && Math.random() < 0.2) {
        valhallaGame.fenrirRage = true;
        triggerFenrirRage();
    }
    
    // Ragnarok (Ultimate bonus)
    if (valhallaGame.divineFavor >= 50) {
        triggerRagnarok();
    }
}

function triggerOdinsPower() {
    document.getElementById('valhallaOdin').textContent = 'ACTIVE';
    const bonus = valhallaGame.betAmount * 100;
    valhallaGame.spoils += bonus;
    balance += bonus;
    updateBalance();
    
    document.getElementById('odinsPowerEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('odinsPowerEffect').style.opacity = '0';
        document.getElementById('valhallaOdin').textContent = 'Ready';
        valhallaGame.odinsPower = false;
    }, 3000);
}

function triggerThorsMight() {
    document.getElementById('valhallaThor').textContent = 'STRIKING';
    valhallaGame.cascadeMultiplier *= 3;
    
    document.getElementById('thorsMightEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('thorsMightEffect').style.opacity = '0';
        document.getElementById('valhallaThor').textContent = 'Ready';
        valhallaGame.thorsMight = false;
    }, 4000);
}

function triggerLokisTrickery() {
    document.getElementById('valhallaLoki').textContent = 'SCHEMING';
    
    // Transform random symbols to wilds
    for (let reel = 0; reel < 6; reel++) {
        for (let row = 0; row < 5; row++) {
            if (Math.random() < 0.3) {
                valhallaGame.reels[reel][row] = VALHALLA_SYMBOLS.WILD;
            }
        }
    }
    
    displayValhallaReels();
    
    document.getElementById('lokisTrickeryEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('lokisTrickeryEffect').style.opacity = '0';
        document.getElementById('valhallaLoki').textContent = 'Ready';
        valhallaGame.lokisTrickery = false;
    }, 3500);
}

function triggerValkyrieBlessing() {
    document.getElementById('valhallaValkyrie').textContent = 'BLESSING';
    valhallaGame.cascadeMultiplier *= 2.5;
    valhallaGame.runicMagic += 5;
    
    document.getElementById('valkyrieEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('valkyrieEffect').style.opacity = '0';
        document.getElementById('valhallaValkyrie').textContent = 'Ready';
        valhallaGame.valkyrieBlessing = false;
    }, 4500);
}

function triggerFenrirRage() {
    document.getElementById('valhallaFenrir').textContent = 'RAGING';
    
    // Add wilds randomly
    const wildPositions = Math.floor(Math.random() * 8) + 5;
    for (let i = 0; i < wildPositions; i++) {
        const reel = Math.floor(Math.random() * 6);
        const row = Math.floor(Math.random() * 5);
        valhallaGame.reels[reel][row] = VALHALLA_SYMBOLS.WILD;
    }
    
    displayValhallaReels();
    
    document.getElementById('fenrirRageEffect').style.opacity = '1';
    setTimeout(() => {
        document.getElementById('fenrirRageEffect').style.opacity = '0';
        document.getElementById('valhallaFenrir').textContent = 'Ready';
        valhallaGame.fenrirRage = false;
    }, 5000);
}

function triggerRagnarok() {
    valhallaGame.ragnarokActive = true;
    document.getElementById('valhallaRagnarok').textContent = 'ACTIVE';
    
    const ragnarokElement = document.getElementById('ragnarokMode');
    ragnarokElement.style.opacity = '1';
    
    // Massive payout bonus
    const ragnarokBonus = valhallaGame.betAmount * 50;
    valhallaGame.spoils += ragnarokBonus;
    balance += ragnarokBonus;
    updateBalance();
    
    document.getElementById('valhallaStatus').innerHTML = 
        `<span class="text-red-400 neon-glow">🔥 RAGNAROK UNLEASHED! +${ragnarokBonus} GA DIVINE BONUS! 🔥</span>`;
    
    setTimeout(() => {
        ragnarokElement.style.opacity = '0';
        document.getElementById('valhallaRagnarok').textContent = 'DORMANT';
        valhallaGame.ragnarokActive = false;
    }, 8000);
}

function cascadeValhallaSymbols(wins) {
    // Remove winning symbols
    wins.forEach(win => {
        win.positions.forEach(pos => {
            valhallaGame.reels[pos.reel][pos.row] = null;
        });
    });
    
    // Drop remaining symbols
    for (let reel = 0; reel < 6; reel++) {
        valhallaGame.reels[reel] = valhallaGame.reels[reel].filter(symbol => symbol !== null);
        
        // Fill with new symbols
        while (valhallaGame.reels[reel].length < 5) {
            const symbol = valhallaGame.symbols[Math.floor(Math.random() * valhallaGame.symbols.length)];
            valhallaGame.reels[reel].unshift(symbol);
        }
    }
    
    displayValhallaReels();
    
    setTimeout(() => {
        checkValhallaWins();
    }, 1000);
}

function endValhallaSpin() {
    valhallaGame.isSpinning = false;
    valhallaGame.cascadeLevel = 0;
    valhallaGame.cascadeMultiplier = 1;
    
    document.getElementById('enterValhalla').disabled = false;
    document.getElementById('valhallaStatus').textContent = 'The gates await worthy warriors';
    
    // Reset divine features
    if (valhallaGame.divineFavor < 10) {
        document.getElementById('valhallaOdin').textContent = 'Ready';
        document.getElementById('valhallaThor').textContent = 'Ready';
        document.getElementById('valhallaLoki').textContent = 'Ready';
        document.getElementById('valhallaValkyrie').textContent = 'Ready';
        document.getElementById('valhallaFenrir').textContent = 'Ready';
    }
}

function updateValhallaDisplay() {
    document.getElementById('valhallaDivineFavor').textContent = valhallaGame.divineFavor;
    document.getElementById('valhallaSpoils').textContent = `${valhallaGame.spoils} GA`;
    document.getElementById('valhallaCascade').textContent = valhallaGame.cascadeLevel;
    document.getElementById('valhallaRunic').textContent = valhallaGame.runicMagic;
    document.getElementById('valhallaCascadeMulti').textContent = `${valhallaGame.cascadeMultiplier.toFixed(1)}x`;
    document.getElementById('cascadeCount').textContent = valhallaGame.cascadeLevel;
    
    // Update divine favor meter
    const favorPercentage = Math.min(100, (valhallaGame.divineFavor / 50) * 100);
    document.getElementById('divineFavorMeter').style.width = `${favorPercentage}%`;
    document.getElementById('favorLevel').textContent = `${Math.round(favorPercentage)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadValhallaGame();
});