// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}
window.updateBalance = updateBalance;

// Make the game loader function globally accessible.
window.loadBookOfDeadGame = loadBookOfDeadGame;


// --- BOOK OF DEAD GAME ---

// Custom SVG Icons for Book of Dead
const bookOfDeadIcons = {
    explorer: `<svg data-symbol="explorer" class="w-12 h-12 text-yellow-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect><line x1="12" y1="6" x2="12" y2="6"></line><line x1="12" y1="10" x2="12" y2="10"></line><line x1="12" y1="14" x2="12" y2="14"></line><line x1="8" y1="18" x2="16" y2="18"></line></svg>`,
    anubis: `<svg data-symbol="anubis" class="w-12 h-12 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L6 8v12h12V8L12 2z"/><path d="M12 12l4 4m-4-4l-4 4"/></svg>`,
    horus: `<svg data-symbol="horus" class="w-12 h-12 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2c-5.523 0-10 4.477-10 10s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2z"/><path d="M12 12a4 4 0 100-8 4 4 0 000 8z"/><path d="M12 12l4 4"/></svg>`,
    scarab: `<svg data-symbol="scarab" class="w-12 h-12 text-green-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20a8 8 0 100-16 8 8 0 000 16z"/><path d="M12 12l-4-4m8 0l-4 4m0 0v8"/><path d="M4 12h16"/></svg>`,
    A: `<div data-symbol="A" class="text-4xl font-bold text-red-500">A</div>`,
    K: `<div data-symbol="K" class="text-4xl font-bold text-orange-500">K</div>`,
    Q: `<div data-symbol="Q" class="text-4xl font-bold text-yellow-500">Q</div>`,
    J: `<div data-symbol="J" class="text-4xl font-bold text-green-500">J</div>`,
    '10': `<div data-symbol="10" class="text-4xl font-bold text-blue-500">10</div>`,
    book: `<svg data-symbol="book" class="w-12 h-12 text-yellow-300 neon-glow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg>`
};

// Game state for Book of Dead
let bookOfDeadGame = {
    isSpinning: false,
    freeSpins: 0,
    isBonusActive: false,
    expandingSymbol: null,
    reels: [],
    baseSymbols: ['explorer', 'anubis', 'horus', 'scarab', 'A', 'K', 'Q', 'J', '10'],
    specialSymbol: 'book'
};


function loadBookOfDeadGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div id="bookOfDeadModal" class="fixed inset-0 bg-black/80 z-50 hidden items-center justify-center">
            <div id="modalContent" class="text-center p-8 bg-black border-2 border-yellow-400 rounded-xl neon-border">
                </div>
        </div>
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div class="lg:col-span-3 bg-black/30 p-4 rounded-xl border border-yellow-500/30">
                <div id="bookOfDeadReels" class="grid grid-cols-5 gap-2 mb-4">
                    </div>
                <div id="bookOfDeadStatus" class="text-center text-xl font-semibold text-yellow-400 neon-glow mb-4">
                    Uncover the secrets of the Pharaohs...
                </div>
            </div>

            <div class="lg:col-span-1">
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30 mb-4">
                    <h4 class="text-lg font-bold mb-4 text-yellow-300">CONTROLS</h4>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-200">BET AMOUNT</label>
                        <input type="number" id="bookOfDeadBet" value="10" min="1" max="100"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    <button id="startSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-2">SPIN</button>
                    <div class="flex justify-between text-sm">
                        <span class="text-yellow-200">Free Spins:</span>
                        <span id="freeSpinsDisplay" class="text-purple-400 font-bold">0</span>
                    </div>
                     <div class="flex justify-between text-sm">
                        <span class="text-yellow-200">Last Win:</span>
                        <span id="lastWinDisplay" class="text-green-400 font-bold">0 GA</span>
                    </div>
                </div>
                <div class="bg-black/30 p-4 rounded-xl border border-yellow-500/30">
                    <h4 class="text-lg font-bold mb-2 text-yellow-300">PAYTABLE (x5)</h4>
                    <div class="space-y-1 text-xs">
                        <div class="flex items-center justify-between"><span>${bookOfDeadIcons.explorer}</span><span class="text-yellow-400">5000x</span></div>
                        <div class="flex items-center justify-between"><span>${bookOfDeadIcons.anubis}</span><span class="text-purple-400">2000x</span></div>
                        <div class="flex items-center justify-between"><span>${bookOfDeadIcons.horus}</span><span class="text-blue-400">750x</span></div>
                        <div class="flex items-center justify-between"><span>${bookOfDeadIcons.scarab}</span><span class="text-green-400">750x</span></div>
                        <div class="flex items-center justify-between"><span>${bookOfDeadIcons.A}${bookOfDeadIcons.K}</span><span class="text-gray-300">150x</span></div>
                        <div class="flex items-center justify-between"><span>${bookOfDeadIcons.Q}${bookOfDeadIcons.J}${bookOfDeadIcons['10']}</span><span class="text-gray-400">100x</span></div>
                        <hr class="border-yellow-500/30 my-2">
                        <div class="flex items-center">${bookOfDeadIcons.book} <span class="ml-2 font-bold">WILD & SCATTER</span></div>
                        <p class="text-gray-400">3+ books trigger 10 Free Spins with a Special Expanding Symbol!</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupBookOfDeadGame();
}

function setupBookOfDeadGame() {
    const reelsContainer = document.getElementById('bookOfDeadReels');
    reelsContainer.innerHTML = ''; // Clear existing reels
    
    // 5x3 grid for classic "Book of" style
    for (let i = 0; i < 15; i++) {
        const symbolDiv = document.createElement('div');
        symbolDiv.className = 'bg-black/50 border border-yellow-800/60 rounded-lg h-24 flex items-center justify-center text-3xl transition-all duration-300';
        const randomSymbolName = bookOfDeadGame.baseSymbols[Math.floor(Math.random() * bookOfDeadGame.baseSymbols.length)];
        symbolDiv.innerHTML = bookOfDeadIcons[randomSymbolName];
        reelsContainer.appendChild(symbolDiv);
    }
    bookOfDeadGame.reels = Array.from(reelsContainer.children);

    document.getElementById('startSpin').addEventListener('click', startSpin);
    updateBookOfDeadDisplay();
}

function updateBookOfDeadDisplay() {
    document.getElementById('freeSpinsDisplay').textContent = bookOfDeadGame.freeSpins;
}

function startSpin() {
    if (bookOfDeadGame.isSpinning) return;

    const bet = parseInt(document.getElementById('bookOfDeadBet').value);

    if (bookOfDeadGame.freeSpins === 0) {
        if (balance < bet) {
            document.getElementById('bookOfDeadStatus').textContent = 'Insufficient funds.';
            return;
        }
        balance -= bet;
        updateBalance();
    } else {
        bookOfDeadGame.freeSpins--;
    }

    bookOfDeadGame.isSpinning = true;
    document.getElementById('startSpin').disabled = true;
    document.getElementById('bookOfDeadStatus').textContent = 'The sands of time are shifting...';

    let animationSteps = 20;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            bookOfDeadGame.reels.forEach(reel => {
                const randomSymbolName = bookOfDeadGame.baseSymbols[Math.floor(Math.random() * bookOfDeadGame.baseSymbols.length)];
                reel.innerHTML = bookOfDeadIcons[randomSymbolName];
                reel.style.transform = 'scale(0.9)';
                reel.style.filter = 'blur(3px)';
            });
            currentStep++;
            setTimeout(animateReels, 100);
        } else {
            // Determine final symbols
            const finalSymbolNames = [];
            for (let i = 0; i < 15; i++) {
                const rand = Math.random();
                let symbolName;
                if (rand < 0.1) { // Chance for special symbol
                    symbolName = bookOfDeadGame.specialSymbol;
                } else {
                    symbolName = bookOfDeadGame.baseSymbols[Math.floor(Math.random() * bookOfDeadGame.baseSymbols.length)];
                }
                finalSymbolNames.push(symbolName);
            }

            // Display final symbols
            bookOfDeadGame.reels.forEach((reel, index) => {
                reel.innerHTML = bookOfDeadIcons[finalSymbolNames[index]];
                reel.style.transform = 'scale(1)';
                reel.style.filter = 'blur(0px)';
            });
            checkWins(finalSymbolNames, bet);
        }
    };
    animateReels();
}

function checkWins(symbolNames, bet) {
    let totalWin = 0;
    
    // Check for 3+ books first, as this triggers the bonus round.
    const bookCount = symbolNames.filter(s => s === bookOfDeadGame.specialSymbol).length;
    if (bookCount >= 3) {
        if (!bookOfDeadGame.isBonusActive) {
            triggerFreeSpins();
            return; // Stop further win processing for this spin to show the modal.
        } else {
            // Re-trigger during bonus
            bookOfDeadGame.freeSpins += 10;
             document.getElementById('bookOfDeadStatus').innerHTML = `<span class="text-yellow-300 font-bold">The Book grants you 10 more spins!</span>`;
        }
    }
    
    // If in bonus round, check for expanding symbol win
    if (bookOfDeadGame.isBonusActive) {
        const expanderCount = symbolNames.filter(s => s === bookOfDeadGame.expandingSymbol).length;
        const highTierSymbols = ['explorer', 'anubis', 'horus', 'scarab'];
        const minForExpansion = highTierSymbols.includes(bookOfDeadGame.expandingSymbol) ? 2 : 3;

        if (expanderCount >= minForExpansion) {
            const expanderWin = getMultiplier(bookOfDeadGame.expandingSymbol, expanderCount) * bet * 10; // Pays on all 10 lines
            totalWin += expanderWin;
             document.getElementById('bookOfDeadStatus').innerHTML = `<span class="text-green-300 font-bold">EXPANDING SYMBOL WIN! ${expanderWin} GA!</span>`;
        }
    }
    
    // Calculate standard line wins (books are wild)
    totalWin += calculateLineWins(symbolNames, bet);

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        document.getElementById('lastWinDisplay').textContent = `${totalWin} GA`;
        if(!document.getElementById('bookOfDeadStatus').textContent.includes('EXPANDING')) {
            document.getElementById('bookOfDeadStatus').innerHTML = `<span class="text-green-400">You found treasure! ${totalWin} GA won!</span>`;
        }
    } else if (!bookOfDeadGame.isBonusActive) {
        document.getElementById('bookOfDeadStatus').textContent = 'The tomb remains sealed...';
    }

    updateBookOfDeadDisplay();

    setTimeout(() => {
        bookOfDeadGame.isSpinning = false;
        document.getElementById('startSpin').disabled = false;
    }, 1500);
}

function triggerFreeSpins() {
    bookOfDeadGame.isSpinning = true;
    const modal = document.getElementById('bookOfDeadModal');
    const modalContent = document.getElementById('modalContent');
    
    // Choose a random expanding symbol
    bookOfDeadGame.expandingSymbol = bookOfDeadGame.baseSymbols[Math.floor(Math.random() * bookOfDeadGame.baseSymbols.length)];
    
    modalContent.innerHTML = `
        <h2 class="text-4xl font-bold text-yellow-400 mb-4 neon-glow">THE BOOK HAS OPENED!</h2>
        <p class="text-xl mb-6">Your special expanding symbol for 10 Free Spins is...</p>
        <div class="inline-block p-4 bg-black border-2 border-yellow-400 rounded-lg">
            ${bookOfDeadIcons[bookOfDeadGame.expandingSymbol]}
        </div>
        <p class="text-2xl font-bold mt-4">${bookOfDeadGame.expandingSymbol.toUpperCase()}</p>
    `;
    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // Hide modal and start free spins after a delay
    setTimeout(() => {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        bookOfDeadGame.isBonusActive = true;
        bookOfDeadGame.freeSpins = 10;
        bookOfDeadGame.isSpinning = false;
        updateBookOfDeadDisplay();
        document.getElementById('startSpin').disabled = false;
        document.getElementById('bookOfDeadStatus').innerHTML = `<span class="font-bold text-purple-400">Bonus Round Active! Good luck!</span>`;
    }, 4000);
}


function calculateLineWins(symbolNames, bet) {
    let totalWin = 0;
    const lines = [
        [0, 1, 2, 3, 4],    // Top Row
        [5, 6, 7, 8, 9],    // Middle Row
        [10, 11, 12, 13, 14], // Bottom Row
        [0, 6, 12, 8, 4],   // V-shape
        [10, 6, 2, 8, 14],  // Inverted V
        // Add more paylines here for a 10-line game
        [0, 5, 10, 5, 0],
        [4, 9, 14, 9, 4],
        [0, 6, 7, 8, 4],
        [10, 6, 7, 8, 14],
        [5, 1, 2, 3, 9]
    ];

    lines.forEach(line => {
        const lineSymbols = line.map(index => symbolNames[index]);
        let symbolToMatch = lineSymbols[0];
        let matchCount = 0;

        if (symbolToMatch === bookOfDeadGame.specialSymbol) {
             let firstRealSymbolIndex = lineSymbols.findIndex(s => s !== bookOfDeadGame.specialSymbol);
             if (firstRealSymbolIndex !== -1) symbolToMatch = lineSymbols[firstRealSymbolIndex];
        }

        for (let symbol of lineSymbols) {
            if (symbol === symbolToMatch || symbol === bookOfDeadGame.specialSymbol) {
                matchCount++;
            } else {
                break;
            }
        }
        
        const highTierSymbols = ['explorer', 'anubis', 'horus', 'scarab'];
        const minForWin = highTierSymbols.includes(symbolToMatch) ? 2 : 3;

        if (matchCount >= minForWin) {
            totalWin += getMultiplier(symbolToMatch, matchCount) * bet;
        }
    });

    return totalWin;
}

function getMultiplier(symbolName, count) {
    const multipliers = {
        'explorer': [0, 2, 10, 100, 500],
        'anubis':   [0, 1, 5, 40, 200],
        'horus':    [0, 1, 3, 10, 75],
        'scarab':   [0, 1, 3, 10, 75],
        'A':        [0, 0, 1, 4, 15],
        'K':        [0, 0, 1, 4, 15],
        'Q':        [0, 0, 0.5, 2.5, 10],
        'J':        [0, 0, 0.5, 2.5, 10],
        '10':       [0, 0, 0.5, 2.5, 10],
        'book':     [0, 0, 2, 20, 200] // Scatter payout
    };
    return multipliers[symbolName] ? multipliers[symbolName][count - 1] : 0;
}

// Initialize the game when the page loads.
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBookOfDeadGame();
});