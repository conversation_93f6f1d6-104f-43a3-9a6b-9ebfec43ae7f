// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadDragonHoardGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                            <h4 class="text-xl font-bold mb-4 text-orange-400">DRAGON'S HOARD JACKPOT</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">QUEST BET</label>
                                <input type="number" id="dragonBet" value="25" min="10" max="${balance}" 
                                       class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">EXPLORATION MODE</label>
                                <select id="dragonMode" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="cautious">Cautious (Safe)</option>
                                    <option value="bold">Bold (Risky)</option>
                                    <option value="legendary">Legendary (Max Risk)</option>
                                </select>
                            </div>
                            
                            <button id="startDragonQuest" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                ENTER DRAGON'S LAIR
                            </button>
                            
                            <div class="text-center mb-4">
                                <div class="text-sm text-gray-400 mb-1">Hoard Value</div>
                                <div id="dragonHoardValue" class="text-2xl font-bold text-yellow-400 neon-glow">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Exploration Depth</div>
                                <div id="dragonDepth" class="text-xl font-bold text-orange-400">0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Dragon Awakeness</div>
                                <div id="dragonAwakeness" class="text-lg font-bold text-red-400">0%</div>
                            </div>
                            
                            <!-- Progressive Jackpots -->
                            <div class="bg-black/30 p-3 rounded-lg border border-orange-500/30 mb-4">
                                <h5 class="text-sm font-bold mb-2 text-orange-400">PROGRESSIVE JACKPOTS</h5>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Minor:</span>
                                        <span id="dragonMinorJackpot" class="text-green-400">$2,500</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Major:</span>
                                        <span id="dragonMajorJackpot" class="text-blue-400">$10,000</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Mega:</span>
                                        <span id="dragonMegaJackpot" class="text-purple-400">$50,000</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Ultimate:</span>
                                        <span id="dragonUltimateJackpot" class="text-red-400">$250,000</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="dragonActions" class="space-y-2 hidden">
                                <button id="dragonExplore" class="w-full py-2 rounded-lg font-bold bg-green-600 hover:bg-green-700 text-white">
                                    🔍 EXPLORE DEEPER
                                </button>
                                <button id="dragonCollect" class="w-full py-2 rounded-lg font-bold bg-yellow-600 hover:bg-yellow-700 text-white">
                                    💰 COLLECT HOARD
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dragon's Lair -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                            <div id="dragonLair" class="relative bg-gradient-to-br from-orange-900 to-red-900 rounded-lg p-6 h-96 overflow-hidden">
                                <!-- Dragon Visual -->
                                <div id="dragonVisual" class="absolute top-4 right-4 text-6xl transition-all duration-1000 transform">
                                    😴
                                </div>
                                
                                <!-- Treasure Grid -->
                                <div class="absolute inset-4">
                                    <div class="grid grid-cols-8 grid-rows-6 gap-1 h-full" id="treasureGrid">
                                        <!-- Treasure items will be generated here -->
                                    </div>
                                </div>
                                
                                <!-- Fire Effects -->
                                <div id="fireEffects" class="absolute inset-0 pointer-events-none opacity-0 transition-opacity duration-1000"></div>
                                
                                <!-- Jackpot Trigger -->
                                <div id="jackpotTrigger" class="absolute inset-0 bg-gradient-to-r from-yellow-500 to-orange-500 opacity-0 flex items-center justify-center transition-opacity duration-1000">
                                    <div class="text-4xl font-bold text-white animate-pulse">JACKPOT!</div>
                                </div>
                            </div>
                            <div id="dragonStatus" class="text-center mt-4 text-lg font-semibold">The dragon slumbers... Enter quietly to claim its hoard</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeDragonHoard();
        }
        
        let dragonHoardGame = {
            isExploring: false,
            mode: 'cautious',
            betAmount: 25,
            hoardValue: 0,
            depth: 0,
            awakeness: 0,
            maxAwakeness: 100,
            treasuresFound: [],
            jackpots: {
                minor: 2500,
                major: 10000,
                mega: 50000,
                ultimate: 250000
            }
        };
        
        function initializeDragonHoard() {
            document.getElementById('startDragonQuest').addEventListener('click', startDragonQuest);
            document.getElementById('dragonExplore').addEventListener('click', exploreDragon);
            document.getElementById('dragonCollect').addEventListener('click', collectDragonHoard);
            document.getElementById('dragonMode').addEventListener('change', updateDragonMode);
            
            generateTreasureGrid();
            updateDragonJackpots();
            updateDragonMode();
        }
        
        function updateDragonMode() {
            dragonHoardGame.mode = document.getElementById('dragonMode').value;
        }
        
        function generateTreasureGrid() {
            const grid = document.getElementById('treasureGrid');
            grid.innerHTML = '';
            
            const treasures = ['💰', '💎', '👑', '⚱️', '🏺', '💍', '🔱', '⚔️'];
            
            for (let i = 0; i < 48; i++) {
                const cell = document.createElement('div');
                cell.className = 'bg-black/40 border border-orange-400/30 rounded flex items-center justify-center text-lg opacity-50 transition-all duration-300';
                cell.dataset.index = i;
                
                if (Math.random() < 0.7) {
                    const treasure = treasures[Math.floor(Math.random() * treasures.length)];
                    cell.textContent = treasure;
                    cell.dataset.treasure = treasure;
                }
                
                grid.appendChild(cell);
            }
        }
        
        function updateDragonJackpots() {
            // Simulate growing jackpots
            const growth = Math.random() * 100;
            dragonHoardGame.jackpots.minor += growth * 0.1;
            dragonHoardGame.jackpots.major += growth * 0.5;
            dragonHoardGame.jackpots.mega += growth * 2;
            dragonHoardGame.jackpots.ultimate += growth * 10;
            
            document.getElementById('dragonMinorJackpot').textContent = '$' + Math.floor(dragonHoardGame.jackpots.minor).toLocaleString();
            document.getElementById('dragonMajorJackpot').textContent = '$' + Math.floor(dragonHoardGame.jackpots.major).toLocaleString();
            document.getElementById('dragonMegaJackpot').textContent = '$' + Math.floor(dragonHoardGame.jackpots.mega).toLocaleString();
            document.getElementById('dragonUltimateJackpot').textContent = '$' + Math.floor(dragonHoardGame.jackpots.ultimate).toLocaleString();
        }
        
        function startDragonQuest() {
            const betAmount = parseInt(document.getElementById('dragonBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            balance -= betAmount;
            updateBalance();
            
            dragonHoardGame.isExploring = true;
            dragonHoardGame.betAmount = betAmount;
            dragonHoardGame.hoardValue = 0;
            dragonHoardGame.depth = 0;
            dragonHoardGame.awakeness = 0;
            dragonHoardGame.treasuresFound = [];
            
            document.getElementById('startDragonQuest').disabled = true;
            document.getElementById('dragonActions').classList.remove('hidden');
            document.getElementById('dragonStatus').textContent = 'You enter the dragon\'s lair... begin your exploration carefully';
            
            updateDragonDisplay();
        }
        
        function exploreDragon() {
            if (!dragonHoardGame.isExploring) return;
            
            dragonHoardGame.depth++;
            
            // Risk calculation based on mode
            const riskFactors = { 'cautious': 5, 'bold': 15, 'legendary': 30 };
            const baseRisk = riskFactors[dragonHoardGame.mode];
            const depthRisk = dragonHoardGame.depth * 2;
            const totalRisk = baseRisk + depthRisk;
            
            dragonHoardGame.awakeness += totalRisk;
            
            // Find treasure
            const treasureValue = findDragonTreasure();
            dragonHoardGame.hoardValue += treasureValue;
            
            // Check for jackpot trigger
            if (Math.random() < 0.02 * dragonHoardGame.depth) {
                triggerDragonJackpot();
                return;
            }
            
            // Check if dragon wakes up
            if (dragonHoardGame.awakeness >= dragonHoardGame.maxAwakeness) {
                dragonAwakens();
                return;
            }
            
            updateDragonDisplay();
            animateTreasureDiscovery();
            
            document.getElementById('dragonStatus').textContent = 
                `Found treasure worth $${treasureValue}! The dragon stirs... (Risk: ${Math.floor(dragonHoardGame.awakeness)}%)`;
        }
        
        function findDragonTreasure() {
            const baseValue = dragonHoardGame.betAmount;
            const modeMultipliers = { 'cautious': 0.5, 'bold': 1.5, 'legendary': 3 };
            const depthMultiplier = 1 + (dragonHoardGame.depth * 0.3);
            
            const treasureValue = Math.floor(baseValue * modeMultipliers[dragonHoardGame.mode] * depthMultiplier * (0.5 + Math.random()));
            
            return treasureValue;
        }
        
        function animateTreasureDiscovery() {
            const cells = document.querySelectorAll('#treasureGrid div');
            const availableCells = Array.from(cells).filter(cell => cell.dataset.treasure && !cell.classList.contains('opacity-100'));
            
            if (availableCells.length > 0) {
                const randomCell = availableCells[Math.floor(Math.random() * availableCells.length)];
                randomCell.classList.remove('opacity-50');
                randomCell.classList.add('opacity-100', 'bg-yellow-400/20', 'ring-2', 'ring-yellow-400');
                
                // Add sparkle effect
                setTimeout(() => {
                    randomCell.classList.add('animate-pulse');
                }, 100);
            }
        }
        
        function triggerDragonJackpot() {
            const jackpotChances = [
                { type: 'minor', chance: 0.5 },
                { type: 'major', chance: 0.3 },
                { type: 'mega', chance: 0.15 },
                { type: 'ultimate', chance: 0.05 }
            ];
            
            const random = Math.random();
            let cumulativeChance = 0;
            let wonJackpot = 'minor';
            
            for (const jackpot of jackpotChances) {
                cumulativeChance += jackpot.chance;
                if (random < cumulativeChance) {
                    wonJackpot = jackpot.type;
                    break;
                }
            }
            
            const jackpotAmount = dragonHoardGame.jackpots[wonJackpot];
            dragonHoardGame.hoardValue += jackpotAmount;
            
            // Show jackpot animation
            const jackpotElement = document.getElementById('jackpotTrigger');
            jackpotElement.style.opacity = '1';
            jackpotElement.innerHTML = `
                <div class="text-center">
                    <div class="text-6xl mb-4">🎉</div>
                    <div class="text-4xl font-bold text-white animate-pulse">${wonJackpot.toUpperCase()} JACKPOT!</div>
                    <div class="text-2xl text-yellow-300">$${Math.floor(jackpotAmount).toLocaleString()}</div>
                </div>
            `;
            
            balance += dragonHoardGame.hoardValue;
            updateBalance();
            
            document.getElementById('dragonStatus').innerHTML = 
                `<span class="text-yellow-400 neon-glow">🎉 ${wonJackpot.toUpperCase()} JACKPOT! Won $${Math.floor(dragonHoardGame.hoardValue).toLocaleString()}! 🎉</span>`;
            
            setTimeout(() => {
                endDragonQuest();
            }, 5000);
        }
        
        function collectDragonHoard() {
            if (dragonHoardGame.hoardValue > 0) {
                balance += dragonHoardGame.hoardValue;
                updateBalance();
                
                document.getElementById('dragonStatus').innerHTML = 
                    `<span class="text-green-400 neon-glow">Successfully collected $${dragonHoardGame.hoardValue}! You escape with the treasure!</span>`;
            } else {
                document.getElementById('dragonStatus').innerHTML = 
                    `<span class="text-gray-400">No treasure to collect... explore deeper first!</span>`;
            }
            
            setTimeout(() => {
                endDragonQuest();
            }, 3000);
        }
        
        function dragonAwakens() {
            // Dragon awakens and player loses everything
            document.getElementById('dragonVisual').textContent = '🐉';
            document.getElementById('dragonVisual').classList.add('animate-bounce');
            
            // Add fire effects
            const fireEffects = document.getElementById('fireEffects');
            fireEffects.style.opacity = '1';
            fireEffects.innerHTML = `
                <div class="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-500 opacity-50 animate-pulse"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-8xl animate-bounce">🔥</div>
                </div>
            `;
            
            document.getElementById('dragonStatus').innerHTML = 
                `<span class="text-red-400">🐉 THE DRAGON AWAKENS! Your greed has cost you everything! Lost $${dragonHoardGame.hoardValue}! 🔥</span>`;
            
            setTimeout(() => {
                endDragonQuest();
            }, 4000);
        }
        
        function updateDragonDisplay() {
            document.getElementById('dragonHoardValue').textContent = '$' + dragonHoardGame.hoardValue;
            document.getElementById('dragonDepth').textContent = dragonHoardGame.depth;
            document.getElementById('dragonAwakeness').textContent = Math.floor(dragonHoardGame.awakeness) + '%';
            
            // Update dragon visual based on awakeness
            const awakeness = dragonHoardGame.awakeness;
            if (awakeness < 30) {
                document.getElementById('dragonVisual').textContent = '😴';
            } else if (awakeness < 60) {
                document.getElementById('dragonVisual').textContent = '😪';
            } else if (awakeness < 90) {
                document.getElementById('dragonVisual').textContent = '😑';
            } else {
                document.getElementById('dragonVisual').textContent = '😠';
            }
        }
        
        function endDragonQuest() {
            dragonHoardGame.isExploring = false;
            
            // Reset UI
            document.getElementById('startDragonQuest').disabled = false;
            document.getElementById('dragonActions').classList.add('hidden');
            document.getElementById('dragonHoardValue').textContent = '0 GA';
            document.getElementById('dragonDepth').textContent = '0';
            document.getElementById('dragonAwakeness').textContent = '0%';
            document.getElementById('dragonVisual').textContent = '😴';
            document.getElementById('dragonVisual').classList.remove('animate-bounce');
            document.getElementById('fireEffects').style.opacity = '0';
            document.getElementById('jackpotTrigger').style.opacity = '0';
            
            // Reset treasure grid
            generateTreasureGrid();
            
            document.getElementById('dragonStatus').textContent = 'The dragon slumbers... Enter quietly to claim its hoard';
            
            // Update jackpots for next round
            updateDragonJackpots();
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDragonHoardGame();
});