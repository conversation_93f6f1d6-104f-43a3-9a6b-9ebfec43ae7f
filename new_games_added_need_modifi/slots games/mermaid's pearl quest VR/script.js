// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Mermaid's Pearl Quest VR Game Implementation
const MERMAID_SYMBOLS = {
    MERMAID: '🧜‍♀️',
    PEARL: '🦪',
    SEAHORSE: '🐴',
    STARFISH: '⭐',
    CORAL: '🪸',
    TREASURE: '💎',
    OCTOPUS: '🐙',
    SHELL: '🐚',
    TRIDENT: '🔱',
    BUBBLE: '💧'
};

const MERMAID_REEL_SYMBOLS = [
    MERMAID_SYMBOLS.MERMAID, MERMAID_SYMBOLS.PEARL, MERMAID_SYMBOLS.SEAHORSE,
    MERMAID_SYMBOLS.STARFISH, MERMAID_SYMBOLS.CORAL, MERMAID_SYMBOLS.TREASURE,
    MERMAID_SYMBOLS.OCTOPUS, MERMAID_SYMBOLS.SHELL, MERMAID_SYMBOLS.TRIDENT, MERMAID_SYMBOLS.BUBBLE
];

let mermaidQuestGame = {
    isSpinning: false,
    oceanSpins: 0,
    lastWin: 0,
    pearlPower: 0,
    depthLevel: 1,
    mermaidBlessings: 0,
    treasureChests: 0,
    atlantisMode: false,
    sirenSong: false,
    tideMultiplier: 1,
    vrImmersion: 100
};

function loadMermaidQuestGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400 font-mono">MERMAID'S PEARL QUEST VR</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">DIVING GEAR</label>
                        <input type="number" id="mermaidBet" value="40" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">VR IMMERSION</label>
                        <select id="vrMode" class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="shallow">Shallow Reef (3x)</option>
                            <option value="deep">Deep Ocean (6x)</option>
                            <option value="atlantis">Lost Atlantis (12x)</option>
                        </select>
                    </div>
                    
                    <button id="diveForPearls" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        DIVE FOR PEARLS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Ocean Spins:</span>
                            <span id="mermaidOceanSpins" class="text-cyan-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Treasure:</span>
                            <span id="mermaidLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Pearl Power:</span>
                            <span id="pearlPower" class="text-white">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Depth Level:</span>
                            <span id="depthLevel" class="text-blue-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">VR Immersion:</span>
                            <span id="vrImmersion" class="text-purple-400">100%</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-cyan-900/20 rounded-lg border border-cyan-500/20">
                        <h5 class="text-sm font-bold mb-2 text-cyan-300 font-mono">OCEAN TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div>🧜‍♀️ <span class="text-cyan-400">Mermaid:</span> 200x bet</div>
                            <div>💎 <span class="text-purple-400">Treasure:</span> 150x bet</div>
                            <div>🦪 <span class="text-white">Pearl:</span> 120x bet</div>
                            <div>🐴 <span class="text-yellow-400">Seahorse:</span> 100x bet</div>
                            <div>🔱 <span class="text-gold-400">Trident:</span> Wild substitute</div>
                            <div>💧 <span class="text-blue-400">Bubble:</span> Scatter bonus</div>
                            <div>🐙 <span class="text-red-400">Octopus:</span> Depth multiplier</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div class="relative mb-6">
                        <div id="mermaidReels" class="grid grid-cols-5 gap-2 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-cyan-900/20 rounded-lg flex items-center justify-center text-4xl border border-cyan-500/20 transition-all duration-300">🧜‍♀️</div>`
                            ).join('')}
                        </div>
                        <div id="atlantisEffect" class="absolute inset-0 bg-gradient-to-t from-purple-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="sirenEffect" class="absolute inset-0 bg-gradient-to-b from-cyan-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="tideEffect" class="absolute inset-0 bg-gradient-radial from-blue-400/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                    </div>
                    
                    <div id="mermaidGameStatus" class="text-center text-lg font-semibold text-cyan-300 mb-4 h-8 font-mono">
                        The ocean depths call to your VR adventure...
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">OCEAN STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Atlantis:</span>
                                    <span id="atlantisStatus" class="text-purple-400">HIDDEN</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Siren Song:</span>
                                    <span id="sirenStatus" class="text-cyan-400">SILENT</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PEARL METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="pearlMeter" class="bg-gradient-to-r from-white to-cyan-400 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">TIDE POWER</h6>
                            <div class="text-center">
                                <span id="tideMultiplier" class="text-2xl text-cyan-400">1x</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupMermaidQuestGame();
}

function setupMermaidQuestGame() {
    document.getElementById('diveForPearls').addEventListener('click', diveForPearls);
    const reelsContainer = document.getElementById('mermaidReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-cyan-900/20 rounded-lg p-2 text-center text-4xl border border-cyan-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = MERMAID_REEL_SYMBOLS[i % MERMAID_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateMermaidDisplay();
}

function diveForPearls() {
    if (mermaidQuestGame.isSpinning) return;

    const bet = parseInt(document.getElementById('mermaidBet').value);
    const vrMode = document.getElementById('vrMode').value;
    let totalBet;
    
    switch(vrMode) {
        case 'deep': totalBet = bet * 6; break;
        case 'atlantis': totalBet = bet * 12; break;
        default: totalBet = bet * 3;
    }

    if (mermaidQuestGame.oceanSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('mermaidGameStatus').textContent = 'INSUFFICIENT DIVING EQUIPMENT';
            return;
        }
        balance -= totalBet;
    } else {
        mermaidQuestGame.oceanSpins--;
    }

    mermaidQuestGame.isSpinning = true;
    mermaidQuestGame.lastWin = 0;
    updateBalance();
    updateMermaidDisplay();
    document.getElementById('mermaidGameStatus').textContent = 'Diving into the mystical ocean depths...';

    const slots = document.querySelectorAll('#mermaidReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Tide effect during spin
    document.getElementById('tideEffect').style.opacity = '0.3';

    let spinDuration = 2400;
    let spinInterval = 95;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = MERMAID_REEL_SYMBOLS[Math.floor(Math.random() * MERMAID_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('tideEffect').style.opacity = '0';
            finishPearlDive(totalBet);
        }
    }, spinInterval);
}

function finishPearlDive(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#mermaidReels .slot');
    
    // Enhanced symbol generation with VR mode bonuses
    const vrMode = document.getElementById('vrMode').value;
    let wildChance = 0.11;
    let scatterChance = 0.08;
    
    if (vrMode === 'deep') {
        wildChance = 0.15;
        scatterChance = 0.10;
    } else if (vrMode === 'atlantis') {
        wildChance = 0.20;
        scatterChance = 0.14;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = MERMAID_SYMBOLS.TRIDENT; // Wild
        } else if (Math.random() < scatterChance) {
            symbol = MERMAID_SYMBOLS.BUBBLE; // Scatter
        } else if (Math.random() < 0.07) {
            symbol = MERMAID_SYMBOLS.OCTOPUS; // Depth multiplier
        } else {
            symbol = MERMAID_REEL_SYMBOLS[Math.floor(Math.random() * 7)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkMermaidWins(finalSymbols, totalBet);
}

function checkMermaidWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for ocean treasures
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === MERMAID_SYMBOLS.MERMAID && count >= 3) {
            multiplier = count >= 5 ? 200 : count >= 4 ? 135 : 68;
            statusMessage = 'THE MERMAID QUEEN GRANTS HER BLESSING!';
            mermaidQuestGame.pearlPower += count * 35;
            mermaidQuestGame.sirenSong = true;
            mermaidQuestGame.mermaidBlessings++;
        } else if (symbol === MERMAID_SYMBOLS.TREASURE && count >= 3) {
            multiplier = count >= 5 ? 150 : count >= 4 ? 100 : 50;
            statusMessage = 'Sunken treasure chest discovered!';
            mermaidQuestGame.treasureChests++;
        } else if (symbol === MERMAID_SYMBOLS.PEARL && count >= 3) {
            multiplier = count >= 5 ? 120 : count >= 4 ? 80 : 40;
            statusMessage = 'Lustrous pearls shine with fortune!';
            mermaidQuestGame.pearlPower += count * 25;
        } else if (symbol === MERMAID_SYMBOLS.SEAHORSE && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 70 : 35;
            statusMessage = 'Magical seahorses guide to riches!';
        } else if (symbol === MERMAID_SYMBOLS.STARFISH && count >= 3) {
            multiplier = count >= 5 ? 80 : count >= 4 ? 55 : 28;
            statusMessage = 'Starfish constellation aligns!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier * mermaidQuestGame.tideMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Trident wild substitution bonus
    const tridentCount = symbols.filter(s => s === MERMAID_SYMBOLS.TRIDENT).length;
    if (tridentCount > 0 && totalWin > 0) {
        const tridentMultiplier = 1 + (tridentCount * 0.8);
        totalWin *= tridentMultiplier;
        statusMessage += ` Poseidon's trident: ${tridentMultiplier.toFixed(1)}x!`;
    }

    // Bubble scatter bonus
    const bubbleCount = symbols.filter(s => s === MERMAID_SYMBOLS.BUBBLE).length;
    if (bubbleCount >= 3) {
        const freeSpinsAwarded = 18 + (bubbleCount - 3) * 9;
        mermaidQuestGame.oceanSpins += freeSpinsAwarded;
        mermaidQuestGame.depthLevel++;
        statusMessage += ` 💧 OCEAN CURRENT SURGE! ${freeSpinsAwarded} ocean spins!`;
        
        // Increase tide multiplier
        mermaidQuestGame.tideMultiplier += 0.4;
    }

    // Octopus depth mechanics
    const octopusCount = symbols.filter(s => s === MERMAID_SYMBOLS.OCTOPUS).length;
    if (octopusCount > 0) {
        if (octopusCount >= 3) {
            // Deep sea exploration
            const depthMultiplier = 5 + octopusCount;
            totalWin *= depthMultiplier;
            statusMessage = `🐙 DEEP SEA EXPLORATION! ${depthMultiplier}x DEPTH MULTIPLIER!`;
            mermaidQuestGame.depthLevel += 2;
        } else if (totalWin > 0) {
            // Minor depth bonus
            const depthBonus = 1 + (octopusCount * 0.6);
            totalWin *= depthBonus;
            statusMessage += ` Ocean depths: ${depthBonus.toFixed(1)}x!`;
        }
    }

    // Siren song activation
    if (mermaidQuestGame.sirenSong && mermaidQuestGame.pearlPower >= 200) {
        const sirenMultiplier = 12 + mermaidQuestGame.depthLevel;
        totalWin *= sirenMultiplier;
        statusMessage = `🧜‍♀️ SIREN'S ENCHANTING SONG! ${sirenMultiplier}x MYSTICAL POWER!`;
        mermaidQuestGame.pearlPower = 0;
        
        // Trigger siren effect
        document.getElementById('sirenEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('sirenEffect').style.opacity = '0';
            mermaidQuestGame.sirenSong = false;
        }, 5000);
    }

    // Atlantis mode activation
    if (mermaidQuestGame.mermaidBlessings >= 5 && mermaidQuestGame.treasureChests >= 8) {
        mermaidQuestGame.atlantisMode = true;
        totalWin *= 15;
        statusMessage = '🏛️ LOST ATLANTIS DISCOVERED! 15x ANCIENT CIVILIZATION!';
        mermaidQuestGame.mermaidBlessings = 0;
        mermaidQuestGame.treasureChests = 0;
        
        // Trigger atlantis effect
        document.getElementById('atlantisEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('atlantisEffect').style.opacity = '0';
            mermaidQuestGame.atlantisMode = false;
        }, 6000);
    }

    // Coral reef bonus
    const coralCount = symbols.filter(s => s === MERMAID_SYMBOLS.CORAL).length;
    if (coralCount >= 4) {
        totalWin += totalBet * 30;
        statusMessage += ' CORAL REEF SANCTUARY BONUS!';
        mermaidQuestGame.vrImmersion = Math.min(mermaidQuestGame.vrImmersion + 10, 150);
    }

    // Shell collection bonus
    const shellCount = symbols.filter(s => s === MERMAID_SYMBOLS.SHELL).length;
    if (shellCount >= 5) {
        const shellMultiplier = 3 + (mermaidQuestGame.depthLevel * 0.5);
        totalWin *= shellMultiplier;
        statusMessage += ` Shell collection: ${shellMultiplier.toFixed(1)}x!`;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        mermaidQuestGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#mermaidReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The ocean keeps its pearls hidden...';
    }

    document.getElementById('mermaidGameStatus').textContent = statusMessage;
    mermaidQuestGame.isSpinning = false;
    updateMermaidDisplay();
}

function updateMermaidDisplay() {
    const spinButton = document.getElementById('diveForPearls');
    spinButton.disabled = mermaidQuestGame.isSpinning;
    spinButton.textContent = mermaidQuestGame.isSpinning ? 'DIVING...' : 'DIVE FOR PEARLS';

    document.getElementById('mermaidOceanSpins').textContent = mermaidQuestGame.oceanSpins;
    document.getElementById('mermaidLastWin').textContent = `${mermaidQuestGame.lastWin} GA`;
    document.getElementById('pearlPower').textContent = mermaidQuestGame.pearlPower;
    document.getElementById('depthLevel').textContent = mermaidQuestGame.depthLevel;
    document.getElementById('vrImmersion').textContent = `${mermaidQuestGame.vrImmersion}%`;
    document.getElementById('atlantisStatus').textContent = mermaidQuestGame.atlantisMode ? 'DISCOVERED!' : 'HIDDEN';
    document.getElementById('sirenStatus').textContent = mermaidQuestGame.sirenSong ? 'SINGING!' : 'SILENT';
    document.getElementById('tideMultiplier').textContent = `${mermaidQuestGame.tideMultiplier.toFixed(1)}x`;
    
    // Update pearl meter
    const pearlLevel = (mermaidQuestGame.pearlPower / 200) * 100;
    document.getElementById('pearlMeter').style.width = `${Math.min(pearlLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMermaidQuestGame();
});

