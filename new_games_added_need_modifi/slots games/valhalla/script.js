// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadValhallaGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Valhalla Control -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                            <h4 class="text-xl font-bold mb-4 text-amber-400">GATES OF VALHALLA</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">WARRIOR STAKE</label>
                                <input type="number" id="valhallaBet" value="30" min="10" max="${balance}" 
                                       class="w-full bg-black/50 border border-amber-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">NORSE REALM</label>
                                <select id="valhallaRealm" class="w-full bg-black/50 border border-amber-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="midgard">Midgard (Balanced)</option>
                                    <option value="asgard">Asgard (High Volatility)</option>
                                    <option value="ragnarok">Ragnarok (Maximum Chaos)</option>
                                </select>
                            </div>
                            
                            <button id="enterValhalla" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                ⚡ ENTER VALHALLA
                            </button>
                            
                            <div class="text-center mb-4">
                                <div class="text-sm text-gray-400 mb-1">Victory Spoils</div>
                                <div id="valhallaSpoils" class="text-2xl font-bold text-yellow-400 neon-glow">$0</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Cascade Multiplier</div>
                                <div id="valhallaCascade" class="text-xl font-bold text-amber-400">1x</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Divine Favor</div>
                                <div id="valhallaFavor" class="text-lg font-bold text-purple-400">0</div>
                            </div>
                            
                            <!-- Norse Pantheon -->
                            <div class="bg-black/30 p-3 rounded-lg border border-amber-500/30">
                                <h5 class="text-sm font-bold mb-2 text-amber-400">NORSE PANTHEON</h5>
                                <div class="text-xs space-y-1">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Odin's Blessing:</span>
                                        <span id="valhallaOdin" class="text-blue-400">Ready</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Thor's Power:</span>
                                        <span id="valhallaThor" class="text-red-400">Ready</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">Ragnarok Mode:</span>
                                        <span id="valhallaRagnarok" class="text-purple-400">Dormant</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Valhalla Gates -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                            <div id="valhallaGates" class="relative bg-gradient-to-br from-amber-900 to-yellow-900 rounded-lg p-4 h-96 overflow-hidden">
                                <!-- Norse Reel Grid -->
                                <div class="grid grid-cols-6 gap-1 h-full" id="valhallaReels">
                                    <div id="valhallaReel0" class="flex flex-col space-y-1"></div>
                                    <div id="valhallaReel1" class="flex flex-col space-y-1"></div>
                                    <div id="valhallaReel2" class="flex flex-col space-y-1"></div>
                                    <div id="valhallaReel3" class="flex flex-col space-y-1"></div>
                                    <div id="valhallaReel4" class="flex flex-col space-y-1"></div>
                                    <div id="valhallaReel5" class="flex flex-col space-y-1"></div>
                                </div>
                                
                                <!-- Lightning Effects -->
                                <div id="lightningEffects" class="absolute inset-0 pointer-events-none opacity-0 transition-opacity duration-1000"></div>
                                
                                <!-- Ragnarok Mode -->
                                <div id="ragnarokMode" class="absolute inset-0 bg-gradient-to-r from-red-600 to-orange-600 opacity-0 flex items-center justify-center transition-opacity duration-1000">
                                    <div class="text-4xl font-bold text-white animate-pulse">RAGNAROK!</div>
                                </div>
                            </div>
                            <div id="valhallaStatus" class="text-center mt-4 text-lg font-semibold">The gates await worthy warriors</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeValhalla();
        }
        
        let valhallaGame = {
            isSpinning: false,
            realm: 'midgard',
            betAmount: 30,
            spoils: 0,
            cascadeMultiplier: 1,
            divineFavor: 0,
            symbols: ['⚔️', '🛡️', '👑', '⚡', '🔨', '🐺', '🦅', '💎', '🌟', 'O'],
            reels: [[], [], [], [], [], []],
            cascadeLevel: 0,
            ragnarokActive: false
        };
        
        function initializeValhalla() {
            document.getElementById('enterValhalla').addEventListener('click', enterValhalla);
            document.getElementById('valhallaRealm').addEventListener('change', updateValhallaRealm);
            
            generateValhallaReels();
            updateValhallaRealm();
        }
        
        function updateValhallaRealm() {
            valhallaGame.realm = document.getElementById('valhallaRealm').value;
        }
        
        function generateValhallaReels() {
            for (let reel = 0; reel < 6; reel++) {
                valhallaGame.reels[reel] = [];
                for (let row = 0; row < 5; row++) {
                    const symbol = valhallaGame.symbols[Math.floor(Math.random() * valhallaGame.symbols.length)];
                    valhallaGame.reels[reel].push(symbol);
                }
            }
            
            displayValhallaReels();
        }
        
        function displayValhallaReels() {
            for (let reel = 0; reel < 6; reel++) {
                const reelElement = document.getElementById(`valhallaReel${reel}`);
                reelElement.innerHTML = '';
                
                valhallaGame.reels[reel].forEach(symbol => {
                    const symbolElement = document.createElement('div');
                    symbolElement.className = 'bg-black/40 border border-amber-400/30 rounded flex items-center justify-center text-lg font-bold transition-all duration-300';
                    symbolElement.style.height = '20%';
                    symbolElement.textContent = symbol;
                    
                    if (symbol === 'O') {
                        symbolElement.classList.add('bg-gradient-to-br', 'from-blue-500', 'to-purple-500', 'text-white', 'animate-pulse');
                    }
                    
                    reelElement.appendChild(symbolElement);
                });
            }
        }
        
        function enterValhalla() {
            if (valhallaGame.isSpinning) return;
            
            const betAmount = parseInt(document.getElementById('valhallaBet').value);
            
            if (betAmount > balance) {
                alert('Insufficient balance!');
                return;
            }
            
            balance -= betAmount;
            updateBalance();
            
            valhallaGame.isSpinning = true;
            valhallaGame.betAmount = betAmount;
            valhallaGame.spoils = 0;
            valhallaGame.cascadeLevel = 0;
            valhallaGame.cascadeMultiplier = 1;
            
            document.getElementById('enterValhalla').disabled = true;
            document.getElementById('valhallaStatus').textContent = 'The gates of Valhalla open...';
            
            animateValhallaEntry().then(() => {
                checkValhallaWins();
            });
        }
        
        function animateValhallaEntry() {
            return new Promise((resolve) => {
                createLightningEffects();
                
                // Generate new symbols with realm-based mechanics
                const realmEffects = {
                    'midgard': { wildChance: 0.1, cascadeBonus: 1.2 },
                    'asgard': { wildChance: 0.15, cascadeBonus: 1.5 },
                    'ragnarok': { wildChance: 0.25, cascadeBonus: 2.0 }
                };
                
                const effects = realmEffects[valhallaGame.realm];
                
                let reelDelay = 0;
                for (let reel = 0; reel < 6; reel++) {
                    setTimeout(() => {
                        valhallaGame.reels[reel] = [];
                        for (let row = 0; row < 5; row++) {
                            let symbol;
                            if (Math.random() < effects.wildChance) {
                                symbol = 'O'; // Odin wild
                            } else {
                                symbol = valhallaGame.symbols[Math.floor(Math.random() * (valhallaGame.symbols.length - 1))];
                            }
                            valhallaGame.reels[reel].push(symbol);
                        }
                        
                        displayValhallaReels();
                        
                        if (reel === 5) {
                            setTimeout(resolve, 300);
                        }
                    }, reelDelay);
                    reelDelay += 150;
                }
            });
        }
        
        function createLightningEffects() {
            const effectsContainer = document.getElementById('lightningEffects');
            effectsContainer.innerHTML = '';
            
            // Create lightning bolts
            for (let i = 0; i < 5; i++) {
                const lightning = document.createElement('div');
                lightning.className = 'absolute w-1 bg-yellow-400 opacity-80 animate-pulse';
                lightning.style.left = Math.random() * 100 + '%';
                lightning.style.height = '100%';
                lightning.style.animationDelay = Math.random() * 1 + 's';
                effectsContainer.appendChild(lightning);
            }
            
            effectsContainer.style.opacity = '1';
            setTimeout(() => {
                effectsContainer.style.opacity = '0';
            }, 2000);
        }
        
        function checkValhallaWins() {
            const wins = findValhallaWins();
            
            if (wins.length > 0) {
                valhallaGame.cascadeLevel++;
                const realmBonus = { 'midgard': 1.2, 'asgard': 1.5, 'ragnarok': 2.0 }[valhallaGame.realm];
                valhallaGame.cascadeMultiplier = 1 + (valhallaGame.cascadeLevel * 0.5 * realmBonus);
                
                const payout = calculateValhallaPayout(wins);
                valhallaGame.spoils += payout;
                valhallaGame.divineFavor += wins.length;
                
                balance += payout;
                updateBalance();
                
                updateValhallaDisplay();
                highlightValhallaWins(wins);
                
                // Check for divine features
                checkDivineFeatures();
                
                document.getElementById('valhallaStatus').innerHTML = 
                    `<span class="text-yellow-400 neon-glow">Victory! ${wins.length} combinations - $${payout}</span>`;
                
                // Cascade after delay
                setTimeout(() => {
                    cascadeValhallaSymbols(wins);
                }, 2000);
            } else {
                endValhallaSpin();
            }
        }
        
        function findValhallaWins() {
            const wins = [];
            
            // Check for winning combinations (cascading style)
            for (let symbol of valhallaGame.symbols) {
                if (symbol === 'O') continue; // Skip wilds for base check
                
                const clusters = findSymbolClusters(symbol);
                if (clusters.length >= 8) { // Minimum 8 symbols for win
                    wins.push({ symbol, count: clusters.length, positions: clusters });
                }
            }
            
            return wins;
        }
        
        function findSymbolClusters(targetSymbol) {
            const clusters = [];
            const visited = new Set();
            
            for (let reel = 0; reel < 6; reel++) {
                for (let row = 0; row < 5; row++) {
                    const symbol = valhallaGame.reels[reel][row];
                    const key = `${reel}-${row}`;
                    
                    if (!visited.has(key) && (symbol === targetSymbol || symbol === 'O')) {
                        const cluster = exploreCluster(reel, row, targetSymbol, visited);
                        clusters.push(...cluster);
                    }
                }
            }
            
            return clusters;
        }
        
        function exploreCluster(reel, row, targetSymbol, visited) {
            const cluster = [];
            const stack = [[reel, row]];
            
            while (stack.length > 0) {
                const [currentReel, currentRow] = stack.pop();
                const key = `${currentReel}-${currentRow}`;
                
                if (visited.has(key)) continue;
                if (currentReel < 0 || currentReel >= 6 || currentRow < 0 || currentRow >= 5) continue;
                
                const symbol = valhallaGame.reels[currentReel][currentRow];
                if (symbol !== targetSymbol && symbol !== 'O') continue;
                
                visited.add(key);
                cluster.push({ reel: currentReel, row: currentRow });
                
                // Check adjacent cells
                const adjacent = [
                    [currentReel - 1, currentRow], [currentReel + 1, currentRow],
                    [currentReel, currentRow - 1], [currentReel, currentRow + 1]
                ];
                
                adjacent.forEach(([adjReel, adjRow]) => {
                    stack.push([adjReel, adjRow]);
                });
            }
            
            return cluster;
        }
        
        function calculateValhallaPayout(wins) {
            const symbolValues = {
                '⚔️': 15, '🛡️': 12, '👑': 20, '⚡': 25, '🔨': 18,
                '🐺': 10, '🦅': 8, '💎': 30, '🌟': 6
            };
            
            let totalPayout = 0;
            wins.forEach(win => {
                const symbolValue = symbolValues[win.symbol] || 5;
                const clusterBonus = Math.floor(win.count / 8); // Bonus for larger clusters
                totalPayout += symbolValue * valhallaGame.betAmount * valhallaGame.cascadeMultiplier * (1 + clusterBonus);
            });
            
            return Math.floor(totalPayout);
        }
        
        function highlightValhallaWins(wins) {
            // Reset highlights
            document.querySelectorAll('#valhallaReels .bg-yellow-400').forEach(el => {
                el.classList.remove('bg-yellow-400', 'ring-2', 'ring-amber-400');
            });
            
            // Highlight winning clusters
            wins.forEach(win => {
                win.positions.forEach(pos => {
                    const reelElement = document.getElementById(`valhallaReel${pos.reel}`);
                    const symbolElement = reelElement.children[pos.row];
                    symbolElement.classList.add('bg-yellow-400', 'ring-2', 'ring-amber-400');
                });
            });
        }
        
        function cascadeValhallaSymbols(wins) {
            // Remove winning symbols
            wins.forEach(win => {
                win.positions.forEach(pos => {
                    valhallaGame.reels[pos.reel][pos.row] = null;
                });
            });
            
            // Drop remaining symbols
            for (let reel = 0; reel < 6; reel++) {
                valhallaGame.reels[reel] = valhallaGame.reels[reel].filter(symbol => symbol !== null);
                
                // Fill with new symbols
                while (valhallaGame.reels[reel].length < 5) {
                    const symbol = valhallaGame.symbols[Math.floor(Math.random() * valhallaGame.symbols.length)];
                    valhallaGame.reels[reel].unshift(symbol);
                }
            }
            
            displayValhallaReels();
            
            setTimeout(() => {
                checkValhallaWins();
            }, 1000);
        }
        
        function checkDivineFeatures() {
            // Odin's Blessing (10+ divine favor)
            if (valhallaGame.divineFavor >= 10) {
                document.getElementById('valhallaOdin').textContent = 'Active';
                // Extra wilds next spin
            }
            
            // Thor's Power (15+ divine favor)
            if (valhallaGame.divineFavor >= 15) {
                document.getElementById('valhallaThor').textContent = 'Active';
                // Lightning strikes destroy random symbols
            }
            
            // Ragnarok Mode (25+ divine favor)
            if (valhallaGame.divineFavor >= 25 && !valhallaGame.ragnarokActive) {
                triggerRagnarok();
            }
        }
        
        function triggerRagnarok() {
            valhallaGame.ragnarokActive = true;
            document.getElementById('valhallaRagnarok').textContent = 'ACTIVE';
            
            const ragnarokElement = document.getElementById('ragnarokMode');
            ragnarokElement.style.opacity = '1';
            
            // Massive payout bonus
            const ragnarokBonus = valhallaGame.betAmount * 50;
            valhallaGame.spoils += ragnarokBonus;
            balance += ragnarokBonus;
            updateBalance();
            
            document.getElementById('valhallaStatus').innerHTML = 
                `<span class="text-red-400 neon-glow">🔥 RAGNAROK UNLEASHED! +$${ragnarokBonus} DIVINE BONUS! 🔥</span>`;
            
            setTimeout(() => {
                ragnarokElement.style.opacity = '0';
            }, 5000);
        }
        
        function updateValhallaDisplay() {
            document.getElementById('valhallaSpoils').textContent = '$' + valhallaGame.spoils;
            document.getElementById('valhallaCascade').textContent = valhallaGame.cascadeMultiplier.toFixed(1) + 'x';
            document.getElementById('valhallaFavor').textContent = valhallaGame.divineFavor;
        }
        
        function endValhallaSpin() {
            valhallaGame.isSpinning = false;
            valhallaGame.cascadeLevel = 0;
            valhallaGame.cascadeMultiplier = 1;
            
            document.getElementById('enterValhalla').disabled = false;
            document.getElementById('valhallaStatus').textContent = 'The gates await worthy warriors';
            
            // Reset divine features
            if (valhallaGame.divineFavor < 10) {
                document.getElementById('valhallaOdin').textContent = 'Ready';
                document.getElementById('valhallaThor').textContent = 'Ready';
            }
        }
        
        // Initialize
        updateBalance();

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadValhallaGame();
});