// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Egyptian Eclipse Game Implementation
const EGYPT_REEL_SYMBOLS = ['𓂀', '𓃭', '𓆣', '𓊪', '𓋹', '𓌻', '𓍯', '𓎡', '💎', 'E'];

let scarabSpinGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    eclipseMode: false,
    pharaohBlessing: 1,
    dynastyLevel: 1
};

function loadEgyptianEclipseGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">EGYPTIAN ECLIPSE</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">OFFERING AMOUNT</label>
                        <input type="number" id="egyptBet" value="30" min="5" max="1000"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startEgyptSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        INVOKE THE GODS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Divine Spins:</span>
                            <span id="egyptFreeSpins" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Blessing:</span>
                            <span id="egyptLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Dynasty Level:</span>
                            <span id="dynastyLevel" class="text-blue-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Eclipse Mode:</span>
                            <span id="eclipseStatus" class="text-purple-400">Dormant</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-900/20 rounded-lg border border-yellow-500/20">
                        <h5 class="text-sm font-bold mb-2 text-yellow-300">HIEROGLYPH GUIDE</h5>
                        <div class="text-xs space-y-1">
                            <div>𓂀 <span class="text-yellow-400">Ankh:</span> 50x bet</div>
                            <div>𓃭 <span class="text-orange-400">Lion:</span> 40x bet</div>
                            <div>𓆣 <span class="text-green-400">Scarab:</span> 30x bet</div>
                            <div>𓊪 <span class="text-blue-400">Eye of Ra:</span> 25x bet</div>
                            <div>𓋹 <span class="text-purple-400">Pyramid:</span> 20x bet</div>
                            <div>E <span class="text-red-400">Eclipse:</span> Scatter bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <div class="relative mb-6">
                        <div id="egyptReels" class="grid grid-cols-5 gap-4 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-yellow-900/20 rounded-lg flex items-center justify-center text-4xl border border-yellow-500/20 transition-all duration-300">𓂀</div>`
                            ).join('')}
                        </div>
                        <div id="eclipseEffect" class="absolute inset-0 bg-gradient-radial from-transparent via-purple-500/20 to-black/50 rounded-lg opacity-0 transition-all duration-1000"></div>
                    </div>
                    <div id="egyptStatus" class="text-center text-lg font-semibold text-yellow-300 h-8">
                        The gods are watching...
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupEgyptianEclipseGame();
}

function setupEgyptianEclipseGame() {
    document.getElementById('startEgyptSpin').addEventListener('click', startEgyptSpin);
    updateEgyptDisplay();
}

function startEgyptSpin() {
    if (scarabSpinGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('egyptBet').value);

    if (scarabSpinGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('egyptStatus').textContent = 'Insufficient offerings for the gods!';
            return;
        }
        balance -= totalBet;
    } else {
        scarabSpinGame.freeSpins--;
    }

    scarabSpinGame.isSpinning = true;
    scarabSpinGame.lastWin = 0;
    updateBalance();
    updateEgyptDisplay();
    document.getElementById('egyptStatus').textContent = 'The gods are watching...';

    const slots = document.querySelectorAll('#egyptReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = EGYPT_REEL_SYMBOLS[Math.floor(Math.random() * EGYPT_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishEgyptSpin(totalBet);
        }
    }, spinInterval);
}

function finishEgyptSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#egyptReels .slot');
    slots.forEach(slot => {
        const symbol = EGYPT_REEL_SYMBOLS[Math.floor(Math.random() * EGYPT_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkEgyptWins(finalSymbols, totalBet);
}

function checkEgyptWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for divine combinations
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === '𓂀' && count >= 3) {
            multiplier = count >= 5 ? 50 : count >= 4 ? 30 : 15;
            statusMessage = 'The Ankh grants eternal life and fortune!';
        } else if (symbol === '𓃭' && count >= 3) {
            multiplier = count >= 5 ? 40 : count >= 4 ? 25 : 12;
            statusMessage = 'The sacred lion roars with divine power!';
        } else if (symbol === '𓆣' && count >= 3) {
            multiplier = count >= 5 ? 30 : count >= 4 ? 20 : 10;
            statusMessage = 'Sacred scarabs bring ancient wealth!';
        } else if (symbol === '𓊪' && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 15 : 8;
            statusMessage = 'The Eye of Ra sees all treasures!';
        } else if (symbol === '𓋹' && count >= 3) {
            multiplier = count >= 5 ? 20 : count >= 4 ? 12 : 6;
            statusMessage = 'Pyramid secrets reveal hidden gold!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier * scarabSpinGame.pharaohBlessing;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Eclipse scatter bonus (E symbols)
    const scatterCount = symbols.filter(s => s === 'E').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 12 + (scatterCount - 3) * 6;
        scarabSpinGame.freeSpins += freeSpinsAwarded;
        scarabSpinGame.eclipseMode = true;
        scarabSpinGame.pharaohBlessing = 2;
        statusMessage += ` SOLAR ECLIPSE! ${freeSpinsAwarded} divine spins granted!`;
        scarabSpinGame.dynastyLevel++;
        
        // Trigger eclipse visual effect
        document.getElementById('eclipseEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('eclipseEffect').style.opacity = '0';
        }, 3000);
    }

    // Reset eclipse mode if no more free spins
    if (scarabSpinGame.freeSpins === 0 && scarabSpinGame.eclipseMode) {
        scarabSpinGame.eclipseMode = false;
        scarabSpinGame.pharaohBlessing = 1;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        scarabSpinGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#egyptReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The gods remain silent this time.';
    }

    document.getElementById('egyptStatus').textContent = statusMessage;
    scarabSpinGame.isSpinning = false;
    updateEgyptDisplay();
}

function updateEgyptDisplay() {
    document.getElementById('egyptFreeSpins').textContent = scarabSpinGame.freeSpins;
    document.getElementById('egyptLastWin').textContent = `${scarabSpinGame.lastWin} GA`;
    document.getElementById('dynastyLevel').textContent = scarabSpinGame.dynastyLevel;
    document.getElementById('eclipseStatus').textContent = scarabSpinGame.eclipseMode ? 'ACTIVE!' : 'Dormant';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadEgyptianEclipseGame();
});
