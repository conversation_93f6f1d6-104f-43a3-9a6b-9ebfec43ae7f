// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Kraken Dive Game Implementation
const KRAKEN_SYMBOLS = ['🐙', '🦑', '🐠', '🦈', '🐟', '💎', '⚓', '🌊', '💰', '🗝️'];

let krakenDiveGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    depthLevel: 1,
    krakenRage: false,
    treasureMultiplier: 1
};

function loadKrakenDiveGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">KRAKEN DIVE</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">DIVE COST</label>
                        <input type="number" id="krakenBet" value="40" min="10" max="1000"
                               class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startKrakenDive" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        DIVE DEEPER
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Dives:</span>
                            <span id="krakenFreeSpins" class="text-cyan-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Treasure:</span>
                            <span id="krakenLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Depth Level:</span>
                            <span id="depthLevel" class="text-blue-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Kraken Status:</span>
                            <span id="krakenStatus" class="text-purple-400">Sleeping</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-cyan-900/20 rounded-lg border border-cyan-500/20">
                        <h5 class="text-sm font-bold mb-2 text-cyan-300">DEEP SEA GUIDE</h5>
                        <div class="text-xs space-y-1">
                            <div>🐙 <span class="text-purple-400">Kraken:</span> 100x bet</div>
                            <div>🦑 <span class="text-red-400">Squid:</span> 50x bet</div>
                            <div>🦈 <span class="text-gray-400">Shark:</span> 25x bet</div>
                            <div>💎 <span class="text-blue-400">Diamond:</span> 15x bet</div>
                            <div>🗝️ <span class="text-yellow-400">Key:</span> Scatter bonus</div>
                            <div>🌊 <span class="text-cyan-400">Wave:</span> Wild symbol</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div class="relative mb-6">
                        <div id="krakenReels" class="grid grid-cols-5 gap-4 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-cyan-900/20 rounded-lg flex items-center justify-center text-4xl border border-cyan-500/20 transition-all duration-300">🐙</div>`
                            ).join('')}
                        </div>
                        <div id="waterEffect" class="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-500/10 to-cyan-500/20 rounded-lg pointer-events-none"></div>
                    </div>
                    <div id="krakenGameStatus" class="text-center text-lg font-semibold text-cyan-300 h-8">
                        Descend into the abyss to find the Kraken's treasure!
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupKrakenDiveGame();
}

function setupKrakenDiveGame() {
    document.getElementById('startKrakenDive').addEventListener('click', startKrakenDive);
    updateKrakenDisplay();
}

function startKrakenDive() {
    if (krakenDiveGame.isSpinning) return;

    const betAmount = parseInt(document.getElementById('krakenBet').value);

    if (krakenDiveGame.freeSpins === 0) {
        if (balance < betAmount) {
            document.getElementById('krakenGameStatus').textContent = 'Insufficient funds for deep dive!';
            return;
        }
        balance -= betAmount;
    } else {
        krakenDiveGame.freeSpins--;
    }

    krakenDiveGame.isSpinning = true;
    krakenDiveGame.lastWin = 0;
    updateBalance();
    updateKrakenDisplay();
    document.getElementById('krakenGameStatus').textContent = 'Diving into the deep abyss...';

    const slots = document.querySelectorAll('#krakenReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 2000;
    let spinInterval = 60;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = KRAKEN_SYMBOLS[Math.floor(Math.random() * KRAKEN_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishKrakenDive(betAmount);
        }
    }, spinInterval);
}

function finishKrakenDive(betAmount) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#krakenReels .slot');
    slots.forEach(slot => {
        const symbol = KRAKEN_SYMBOLS[Math.floor(Math.random() * KRAKEN_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkKrakenWins(finalSymbols, betAmount);
}

function checkKrakenWins(symbols, betAmount) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for legendary sea creature wins
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === '🐙' && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 50 : 20;
            statusMessage = 'THE KRAKEN AWAKENS! LEGENDARY TREASURE FOUND!';
            krakenDiveGame.krakenRage = true;
        } else if (symbol === '🦑' && count >= 3) {
            multiplier = count >= 5 ? 50 : count >= 4 ? 25 : 12;
            statusMessage = 'Giant squid guards ancient treasure!';
        } else if (symbol === '🦈' && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 15 : 8;
            statusMessage = 'Shark-infested waters yield rewards!';
        } else if (symbol === '💎' && count >= 3) {
            multiplier = count >= 5 ? 15 : count >= 4 ? 10 : 5;
            statusMessage = 'Precious gems discovered in the depths!';
        } else if ((symbol === '🐠' || symbol === '🐟') && count >= 3) {
            multiplier = count >= 5 ? 8 : count >= 4 ? 5 : 3;
            statusMessage = 'School of treasure fish found!';
        }

        if (multiplier > 0) {
            totalWin += betAmount * multiplier * krakenDiveGame.treasureMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Wild wave bonus (🌊 symbols)
    const wildCount = symbols.filter(s => s === '🌊').length;
    if (wildCount > 0) {
        krakenDiveGame.treasureMultiplier = 1 + (wildCount * 0.3);
        if (totalWin > 0) {
            statusMessage += ' Tidal waves multiply your treasure!';
        }
    } else {
        krakenDiveGame.treasureMultiplier = 1;
    }

    // Scatter bonus (🗝️ Ancient Key)
    const scatterCount = symbols.filter(s => s === '🗝️').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 8 + (scatterCount - 3) * 4;
        krakenDiveGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` Ancient key unlocks ${freeSpinsAwarded} free dives!`;
        krakenDiveGame.depthLevel++;
    }

    // Kraken rage bonus
    if (krakenDiveGame.krakenRage && totalWin > 0) {
        totalWin *= 2;
        statusMessage += ' KRAKEN RAGE DOUBLES ALL WINS!';
        krakenDiveGame.krakenRage = false;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        krakenDiveGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#krakenReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The depths remain silent... dive deeper.';
    }

    document.getElementById('krakenGameStatus').textContent = statusMessage;
    krakenDiveGame.isSpinning = false;
    updateKrakenDisplay();
}

function updateKrakenDisplay() {
    document.getElementById('krakenFreeSpins').textContent = krakenDiveGame.freeSpins;
    document.getElementById('krakenLastWin').textContent = `${krakenDiveGame.lastWin} GA`;
    document.getElementById('depthLevel').textContent = krakenDiveGame.depthLevel;
    document.getElementById('krakenStatus').textContent = krakenDiveGame.krakenRage ? 'ENRAGED!' : 'Sleeping';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadKrakenDiveGame();
});
