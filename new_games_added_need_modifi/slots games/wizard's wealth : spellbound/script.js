// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- <PERSON>'s Wealth: Spellbound Game Implementation ---

const WIZARD_SYMBOLS = {
    WILD: '🧙‍♂️',    // Wizard
    SCATTER: '📚',   // Spellbook
    BONUS: '🔮',     // Crystal Ball
    STAFF: '🪄',
    POTION: '🧪',
    CAULDRON: '⚗️',
    RAVEN: '🐦‍⬛',
    MOON: '🌙',
    STAR: '⭐',
};

const WIZARD_REEL_SYMBOLS = [
    WIZARD_SYMBOLS.STAFF, WIZARD_SYMBOLS.POTION, WIZARD_SYMBOLS.CAULDRON, WIZARD_SYMBOLS.RAVEN,
    WIZARD_SYMBOLS.MOON, WIZARD_SYMBOLS.STAR, WIZARD_SYMBOLS.WILD, WIZARD_SYMBOLS.SCATTER, WIZARD_SYMBOLS.BONUS
];

const WIZARD_PAYLINES = [
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    [0, 6, 12, 18], [4, 8, 12, 16],
    [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4]
];

let wizardGame = {
    isSpinning: false,
    freeSpins: 0,
    magicPower: 0,
    lastWin: 0
};

function loadWizardWealthGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-300 font-mono">ARCANE CHAMBER</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">MANA (BET)</label>
                        <input type="number" id="wizardBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startWizardSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        CAST SPELL
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Enchantment:</span>
                            <span id="wizardLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Mystic Spins:</span>
                            <span id="wizardFreeSpins" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300 font-mono">MAGIC POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="magicMeter" class="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="magicStatus" class="text-xs text-center text-gray-400">Energy: 0%</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="wizardReels" class="grid grid-cols-5 gap-2 mb-6"></div>
                    
                    <div id="wizardGameStatus" class="text-center text-lg font-semibold text-purple-300 mb-4 h-8 font-mono">
                        The ancient magic stirs...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm font-mono">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${WIZARD_SYMBOLS.STAFF} x5:</span><span class="text-purple-400">220x</span></div>
                                <div class="flex justify-between"><span>${WIZARD_SYMBOLS.POTION} x5:</span><span class="text-green-400">160x</span></div>
                                <div class="flex justify-between"><span>${WIZARD_SYMBOLS.CAULDRON} x5:</span><span class="text-blue-400">120x</span></div>
                                <div class="flex justify-between"><span>${WIZARD_SYMBOLS.RAVEN} x5:</span><span class="text-gray-400">90x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">ARCANE LORE</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-purple-400">${WIZARD_SYMBOLS.WILD} Wizard:</span> Substitutes all but Spellbook.</div>
                                <div><span class="text-brown-400">${WIZARD_SYMBOLS.SCATTER} Spellbook:</span> 3+ grants Mystic Free Spins.</div>
                                <div><span class="text-cyan-400">${WIZARD_SYMBOLS.BONUS} Crystal:</span> Channels Magic Power energy.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupWizardGame();
}

function setupWizardGame() {
    document.getElementById('startWizardSpin').addEventListener('click', startWizardSpin);
    const reelsContainer = document.getElementById('wizardReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-purple-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = WIZARD_REEL_SYMBOLS[i % WIZARD_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateWizardDisplay();
}

function updateWizardDisplay() {
    const spinButton = document.getElementById('startWizardSpin');
    spinButton.disabled = wizardGame.isSpinning;
    spinButton.textContent = wizardGame.isSpinning ? 'CASTING...' : 'CAST SPELL';

    document.getElementById('wizardFreeSpins').textContent = wizardGame.freeSpins;
    document.getElementById('magicMeter').style.width = `${wizardGame.magicPower}%`;
    document.getElementById('magicStatus').textContent = `Energy: ${wizardGame.magicPower}%`;
    document.getElementById('wizardLastWin').textContent = `${wizardGame.lastWin} GA`;
}

function startWizardSpin() {
    if (wizardGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('wizardBet').value);

    if (wizardGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('wizardGameStatus').textContent = 'INSUFFICIENT MANA';
            return;
        }
        balance -= totalBet;
    } else {
        wizardGame.freeSpins--;
    }

    wizardGame.isSpinning = true;
    wizardGame.lastWin = 0;
    updateBalance();
    updateWizardDisplay();
    document.getElementById('wizardGameStatus').textContent = 'Weaving ancient magic...';

    const slots = document.querySelectorAll('#wizardReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = WIZARD_REEL_SYMBOLS[Math.floor(Math.random() * WIZARD_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishWizardSpin(totalBet);
        }
    }, spinInterval);
}

function finishWizardSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#wizardReels .slot');
    slots.forEach(slot => {
        const symbol = WIZARD_REEL_SYMBOLS[Math.floor(Math.random() * WIZARD_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkWizardWins(finalSymbols, totalBet);
}

function checkWizardWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    WIZARD_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === WIZARD_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== WIZARD_SYMBOLS.WILD && symbols[index] !== WIZARD_SYMBOLS.SCATTER) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        if (lineSymbol === WIZARD_SYMBOLS.SCATTER) return;

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === WIZARD_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getWizardMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 25;
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    const scatterCount = symbols.filter(s => s === WIZARD_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 12 + (scatterCount - 3) * 6;
        wizardGame.freeSpins += freeSpinsWon;
        statusMessage = `📚 Ancient Spellbook Opened! ${freeSpinsWon} mystic spins granted!`;
    }
    
    const bonusCount = symbols.filter(s => s === WIZARD_SYMBOLS.BONUS).length;
    if (bonusCount > 0) {
        wizardGame.magicPower = Math.min(100, wizardGame.magicPower + bonusCount * 25);
    }

    if (wizardGame.magicPower >= 100 && totalWin > 0) {
        const powerMultiplier = Math.floor(Math.random() * 20) + 5;
        totalWin *= powerMultiplier;
        wizardGame.magicPower = 0;
        statusMessage = `🔮 ARCANE MASTERY! Wealth multiplied by ${powerMultiplier}x!`;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        wizardGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('ARCANE MASTERY')) {
            statusMessage = statusMessage ? statusMessage : `Spell successful: +${totalWin} GA`;
        }

        const slots = document.querySelectorAll('#wizardReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The magic fades away.';
    }
    
    document.getElementById('wizardGameStatus').textContent = statusMessage;
    wizardGame.isSpinning = false;
    updateWizardDisplay();
}

function getWizardMultiplier(symbol, count) {
    const multipliers = {
        [WIZARD_SYMBOLS.STAFF]: { 3: 44, 4: 110, 5: 220 },
        [WIZARD_SYMBOLS.POTION]: { 3: 32, 4: 80, 5: 160 },
        [WIZARD_SYMBOLS.CAULDRON]: { 3: 24, 4: 60, 5: 120 },
        [WIZARD_SYMBOLS.RAVEN]: { 3: 18, 4: 45, 5: 90 },
        [WIZARD_SYMBOLS.MOON]: { 3: 12, 4: 30, 5: 60 },
        [WIZARD_SYMBOLS.STAR]: { 3: 8, 4: 20, 5: 40 },
    };
    return multipliers[symbol]?.[count] || 0;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadWizardWealthGame();
});
