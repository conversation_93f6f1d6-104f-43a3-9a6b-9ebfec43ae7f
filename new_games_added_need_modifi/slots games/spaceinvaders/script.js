// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Space Invaders Game Implementation
function loadSpaceInvadersGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <h4 class="text-xl font-bold mb-4 text-blue-400">SPACE INVADERS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">BET AMOUNT</label>
                        <input type="number" id="spaceBet" value="25" min="1" max="1000" 
                               class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-blue-300">DIFFICULTY</label>
                        <select id="spaceDifficulty" class="w-full bg-black/50 border border-blue-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Easy (25 enemies)</option>
                            <option value="medium">Medium (50 enemies)</option>
                            <option value="hard">Hard (75 enemies)</option>
                        </select>
                    </div>
                    
                    <button id="startInvasion", class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        START INVASION
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-blue-300">Galactic Map Progress:</span>
                            <span id="spaceMapProgress" class="text-white">0%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-300">Last Win:</span>
                            <span id="spaceLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-blue-300">Power-Ups:</span>
                            <span id="spacePowerUps" class="text-red-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-blue-900/20 rounded-lg border border-blue-500/20">
                        <h5 class="text-sm font-bold mb-2 text-blue-300">GALACTIC MAP</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="galacticMap", class="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-1000", style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Navigate the galaxy for cosmic rewards</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-blue-500/30">
                    <div id="spaceGameArea", class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 game grid -->
                    </div>
                    
                    <div id="spaceStatus", class="text-center text-lg font-semibold text-blue-400 mb-4">
                        Defend Earth from waves of alien invaders
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-blue-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-blue-300 mb-2">PAYTABLE</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>👾 x5:</span><span class="text-cyan-400">2000x</span></div>
                                <div class="flex justify-between"><span>🛸 x5:</span><span class="text-blue-400">800x</span></div>
                                <div class="flex justify-between"><span>🚀 x5:</span><span class="text-indigo-400">400x</span></div>
                                <div class="flex justify-between"><span>🌌 x5:</span><span class="text-violet-400">200x</span></div>
                            </div>
                        </div>
                        <div class="bg-blue-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-blue-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🛡️ <span class="text-blue-400">Wild:</span> Shield substitutes all</div>
                                <div>💥 <span class="text-purple-400">Scatter:</span> 3+ triggers Power-Ups</div>
                                <div>🗺️ <span class="text-cyan-400">Bonus:</span> Galactic map progress</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupSpaceInvadersGame();
}

const SPACE_SYMBOLS = ['👾', '🛸', '🚀', '🌌', '🌠', '☄️', '🌎', '🛡️', '💥', '🗺️'];

let spaceInvadersGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    invasionLevel: 1,
    shieldPower: 100
};

function setupSpaceInvadersGame() {
    document.getElementById('startInvasion').addEventListener('click', startSpaceInvasion);
    document.getElementById('spaceDifficulty').addEventListener('change', function() {
        updateSpaceDisplay();
    });
    updateSpaceDisplay();
}

function updateSpaceDisplay() {
    document.getElementById('startInvasion').disabled = spaceInvadersGame.isSpinning;
    document.getElementById('spaceFreeSpins').textContent = spaceInvadersGame.freeSpins;
    document.getElementById('spaceLastWin').textContent = `${spaceInvadersGame.lastWin} GA`;
    document.getElementById('invasionLevel').textContent = spaceInvadersGame.invasionLevel;
    document.getElementById('shieldPower').textContent = `${spaceInvadersGame.shieldPower}%`;
}

function startSpaceInvasion() {
    if (spaceInvadersGame.isSpinning) return;

    const betAmount = parseInt(document.getElementById('spaceBet').value);

    if (spaceInvadersGame.freeSpins === 0) {
        if (balance < betAmount) {
            document.getElementById('spaceStatus').textContent = 'Insufficient energy for defense!';
            return;
        }
        balance -= betAmount;
    } else {
        spaceInvadersGame.freeSpins--;
    }

    spaceInvadersGame.isSpinning = true;
    updateBalance();
    updateSpaceDisplay();
    document.getElementById('spaceStatus').textContent = 'Scanning for alien activity...';

    // Generate random symbols based on difficulty/enemies
    const gameArea = document.getElementById('spaceGameArea');
    gameArea.innerHTML = '';
    const symbols = [];
    
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = SPACE_SYMBOLS[Math.floor(Math.random() * SPACE_SYMBOLS.length)];
        symbols.push(symbol);
        const slot = document.createElement('div');
        slot.className = 'bg-black/50 rounded-lg p-2 text-center text-3xl border border-blue-500/20 transition-all duration-300';
        slot.textContent = symbol;
        gameArea.appendChild(slot);
    }
    
    setTimeout(() => {
        checkSpaceWins(symbols, betAmount);
    }, 1500);
}

function checkSpaceWins(symbols, betAmount) {
    let totalWin = 0;
    const winningPositions = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for invasion patterns
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === '👾' && count >= 3) {
            multiplier = count >= 5 ? 30 : count >= 4 ? 15 : 8;
            statusMessage = 'Alien invasion detected! Defense successful!';
        } else if (symbol === '🛸' && count >= 3) {
            multiplier = count >= 5 ? 25 : count >= 4 ? 12 : 6;
            statusMessage = 'UFO squadron neutralized!';
        } else if (symbol === '🚀' && count >= 3) {
            multiplier = count >= 5 ? 20 : count >= 4 ? 10 : 5;
            statusMessage = 'Counter-attack successful!';
        } else if (symbol === '💥' && count >= 3) {
            multiplier = count >= 5 ? 40 : count >= 4 ? 20 : 10;
            statusMessage = 'Massive explosion chain reaction!';
        }

        if (multiplier > 0) {
            totalWin += betAmount * multiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningPositions.add(i);
            });
        }
    }

    // Shield bonus (🛡️ symbols)
    const shieldCount = symbols.filter(s => s === '🛡️').length;
    if (shieldCount >= 2) {
        spaceInvadersGame.shieldPower = Math.min(100, spaceInvadersGame.shieldPower + (shieldCount * 10));
        if (totalWin > 0) {
            totalWin *= (1 + shieldCount * 0.2); // Shield multiplier
            statusMessage += ' Shields amplify victory!';
        }
    }

    // Scatter bonus (🗺️ Star Map)
    const scatterCount = symbols.filter(s => s === '🗺️').length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 5 + (scatterCount - 3) * 3;
        spaceInvadersGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` Star map discovered! +${freeSpinsAwarded} free battles!`;
        spaceInvadersGame.invasionLevel++;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        spaceInvadersGame.lastWin = totalWin;
        updateBalance();

        // Highlight winning positions
        const slots = document.querySelectorAll('#spaceGameArea > div');
        winningPositions.forEach(index => {
            slots[index].classList.add('win-highlight', 'animate-pulse');
        });
    } else if (!statusMessage) {
        statusMessage = 'No alien activity detected.';
        spaceInvadersGame.shieldPower = Math.max(0, spaceInvadersGame.shieldPower - 5);
    }

    document.getElementById('spaceStatus').textContent = statusMessage;
    spaceInvadersGame.isSpinning = false;
    updateSpaceDisplay();
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadSpaceInvadersGame();
});
