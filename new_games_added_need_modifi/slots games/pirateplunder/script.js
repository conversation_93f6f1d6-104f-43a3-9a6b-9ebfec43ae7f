// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Pirate Plunder Game Implementation ---

// Game constants
const PIRATE_SYMBOLS = {
    WILD: '🗡️',      // Cutlass
    SCATTER: '🛢️',    // Gunpowder Barrel
    MAP: '🗺️',        // Treasure Map
    SKULL: '🏴‍☠️',
    SHIP: '🚢',
    CHEST: '💰',
    SWORDS: '⚔️',
    COMPASS: '🧭',
    BOTTLE: '🍾',
};

const PIRATE_REEL_SYMBOLS = [
    PIRATE_SYMBOLS.SKULL, PIRATE_SYMBOLS.SHIP, PIRATE_SYMBOLS.CHEST,
    PIRATE_SYMBOLS.SWORDS, PIRATE_SYMBOLS.COMPASS, PIRATE_SYMBOLS.BOTTLE,
    PIRATE_SYMBOLS.WILD, PIRATE_SYMBOLS.SCATTER, PIRATE_SYMBOLS.MAP
];

// Define the 5x4 grid of paylines.
const PIRATE_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    // Diagonals
    [0, 5, 10, 15], [1, 6, 11, 16], [2, 7, 12, 17], [3, 8, 13, 18], [4, 9, 14, 19],
    [0, 6, 12, 18], [4, 8, 12, 16],
    // W-shapes & other patterns
    [0, 5, 2, 8, 4], [15, 10, 17, 11, 19],
    [5, 1, 7, 3, 9], [10, 6, 12, 8, 14]
];

// Game state object
let piratePlunderGame = {
    isSpinning: false,
    pirateRaids: 0, // Free spins
    mapProgress: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadPiratePlunderGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <h4 class="text-xl font-bold mb-4 text-amber-400">PIRATE PLUNDER</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-amber-300">TOTAL BET</label>
                        <input type="number" id="pirateBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="sailForTreasure" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        SAIL FOR TREASURE
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-amber-300">Last Win:</span>
                            <span id="pirateLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-amber-300">Pirate Raids:</span>
                            <span id="pirateRaids" class="text-red-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-amber-900/20 rounded-lg border border-amber-500/20">
                        <h5 class="text-sm font-bold mb-2 text-amber-300">TREASURE MAP</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="treasureMap" class="bg-gradient-to-r from-amber-500 to-yellow-400 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="pirateMapProgress" class="text-xs text-center text-gray-400">Collect map pieces for a bonus! (0%)</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <div id="pirateReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid will be generated here -->
                    </div>
                    
                    <div id="pirateStatus" class="text-center text-lg font-semibold text-amber-400 mb-4 h-8">
                        Set sail on the high seas to plunder hidden treasures.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-amber-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-amber-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${PIRATE_SYMBOLS.SKULL} x5:</span><span class="text-yellow-300">100x</span></div>
                                <div class="flex justify-between"><span>${PIRATE_SYMBOLS.SHIP} x5:</span><span class="text-amber-400">50x</span></div>
                                <div class="flex justify-between"><span>${PIRATE_SYMBOLS.CHEST} x5:</span><span class="text-yellow-500">25x</span></div>
                                <div class="flex justify-between"><span>${PIRATE_SYMBOLS.SWORDS} x5:</span><span class="text-gray-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-amber-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-amber-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-gray-300">${PIRATE_SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter & Map.</div>
                                <div><span class="text-red-400">${PIRATE_SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Pirate Raids.</div>
                                <div><span class="text-yellow-400">${PIRATE_SYMBOLS.MAP} Map:</span> Complete the map for a treasure bonus!</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupPiratePlunderGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupPiratePlunderGame() {
    document.getElementById('sailForTreasure').addEventListener('click', sailForTreasureHunt);
    const reelsContainer = document.getElementById('pirateReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-amber-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = PIRATE_REEL_SYMBOLS[i % PIRATE_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updatePirateDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updatePirateDisplay() {
    const spinButton = document.getElementById('sailForTreasure');
    spinButton.disabled = piratePlunderGame.isSpinning;
    spinButton.textContent = piratePlunderGame.isSpinning ? 'SAILING...' : 'SAIL FOR TREASURE';

    document.getElementById('pirateRaids').textContent = piratePlunderGame.pirateRaids;
    document.getElementById('treasureMap').style.width = `${piratePlunderGame.mapProgress}%`;
    document.getElementById('pirateMapProgress').textContent = `Collect map pieces for a bonus! (${piratePlunderGame.mapProgress}%)`;
    document.getElementById('pirateLastWin').textContent = `${piratePlunderGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function sailForTreasureHunt() {
    if (piratePlunderGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('pirateBet').value);

    if (piratePlunderGame.pirateRaids === 0) {
        if (balance < totalBet) {
            document.getElementById('pirateStatus').textContent = 'Not enough doubloons!';
            return;
        }
        balance -= totalBet;
    } else {
        piratePlunderGame.pirateRaids--;
    }

    piratePlunderGame.isSpinning = true;
    piratePlunderGame.lastWin = 0;
    updateBalance();
    updatePirateDisplay();
    document.getElementById('pirateStatus').textContent = 'Searching for treasure...';

    const slots = document.querySelectorAll('#pirateReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = PIRATE_REEL_SYMBOLS[Math.floor(Math.random() * PIRATE_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishPirateSpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishPirateSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#pirateReels .slot');
    slots.forEach(slot => {
        const symbol = PIRATE_REEL_SYMBOLS[Math.floor(Math.random() * PIRATE_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkPirateWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and special symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkPirateWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // 1. Check for payline wins
    PIRATE_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === PIRATE_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== PIRATE_SYMBOLS.WILD) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === PIRATE_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getPirateMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 20; // Standardize line bet
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins (Pirate Raids)
    const scatterCount = symbols.filter(s => s === PIRATE_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 5 + (scatterCount - 3) * 2;
        piratePlunderGame.pirateRaids += freeSpinsWon;
        statusMessage = `💣 ${freeSpinsWon} Pirate Raids triggered!`;
    }

    // 3. Check for map pieces
    const mapCount = symbols.filter(s => s === PIRATE_SYMBOLS.MAP).length;
    if (mapCount > 0) {
        piratePlunderGame.mapProgress = Math.min(100, piratePlunderGame.mapProgress + mapCount * 10);
        if (statusMessage) statusMessage += ` +${mapCount} map piece(s)!`;
        else statusMessage = `Found ${mapCount} map piece(s)!`;
    }

    // 4. Check for Treasure Map bonus
    if (piratePlunderGame.mapProgress >= 100) {
        const bonusWin = totalBet * (Math.floor(Math.random() * 11) + 10); // 10x to 20x bet bonus
        totalWin += bonusWin;
        piratePlunderGame.mapProgress = 0; // Reset map
        statusMessage = `🗺️ TREASURE FOUND! You won a ${bonusWin} GA bonus!`;
    }

    // 5. Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        piratePlunderGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('TREASURE FOUND')) {
            statusMessage = statusMessage ? statusMessage : `Treasure plundered! You won ${totalWin} GA!`;
        }

        const slots = document.querySelectorAll('#pirateReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Empty waters... Keep searching!';
    }
    
    document.getElementById('pirateStatus').textContent = statusMessage;
    piratePlunderGame.isSpinning = false;
    updatePirateDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getPirateMultiplier(symbol, count) {
    const multipliers = {
        [PIRATE_SYMBOLS.SKULL]:   { 3: 20, 4: 50, 5: 100 },
        [PIRATE_SYMBOLS.SHIP]:    { 3: 10, 4: 25, 5: 50 },
        [PIRATE_SYMBOLS.CHEST]:   { 3: 8,  4: 20, 5: 25 },
        [PIRATE_SYMBOLS.SWORDS]:  { 3: 5,  4: 10, 5: 15 },
        [PIRATE_SYMBOLS.COMPASS]: { 3: 3,  4: 8,  5: 12 },
        [PIRATE_SYMBOLS.BOTTLE]:  { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPiratePlunderGame();
});
