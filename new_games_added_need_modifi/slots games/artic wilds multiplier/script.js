// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}
window.updateBalance = updateBalance;

// Make all game loader functions globally accessible so they can be called from other scripts if needed.
window.loadArcticAdventureGame = loadArcticAdventureGame;
window.loadEgyptianEclipseGame = loadEgyptianEclipseGame;
window.loadWildSafariGame = loadWildSafariGame;
window.loadRoboReelsGame = loadRoboReelsGame;
window.loadMermaidQuestGame = loadMermaidQuestGame;
window.loadDinoGoldGame = loadDinoGoldGame;
window.loadWizardWealthGame = loadWizardWealthGame;
window.loadKrakenDiveGame = loadKrakenDiveGame;
window.loadLunarSpinsGame = loadLunarSpinsGame;


// --- ARCTIC ADVENTURE GAME ---

// Custom SVG Icons for Arctic Adventure
// These icons are designed to fit the game's futuristic, neon aesthetic.
const arcticIcons = {
    penguin: `<svg data-symbol="penguin" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M16 14.5a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0zM12 12V4a2 2 0 0 1 2-2h0a2 2 0 0 1 2 2v2"/><path d="M12 12L8 6"/><path d="M18 14l2 4"/><path d="M6 14l-2 4"/></svg>`,
    mountain: `<svg data-symbol="mountain" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="m8 3 4 8 5-5 5 15H2L8 3z"/></svg>`,
    snowflake: `<svg data-symbol="snowflake" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2v20M19.07 4.93l-1.41 1.41M4.93 19.07l1.41-1.41M22 12H2M19.07 19.07l-1.41-1.41M4.93 4.93l1.41 1.41"/></svg>`,
    ice_cube: `<svg data-symbol="ice_cube" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M20.93 17.52l-8.46 4.92a2 2 0 0 1-1.94 0l-8.46-4.92A2 2 0 0 1 1 15.7V8.3a2 2 0 0 1 1.07-1.78l8.46-4.92a2 2 0 0 1 1.94 0l8.46 4.92A2 2 0 0 1 23 8.3v7.4a2 2 0 0 1-1.07 1.78zM12.5 2.5L4 7.5l8.5 5 8.5-5L12.5 2.5zM4 16.5l8.5-5v10L4 16.5z"/></svg>`,
    polar_bear: `<svg data-symbol="polar_bear" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M18 13.5c0-2.5-1.8-4.5-4-4.5s-4 2-4 4.5c0 .8.2 1.5.6 2.1l-2.6 2.6c-1.2 1.2-1.2 3.1 0 4.2.8.8 2 .8 2.8 0l2.6-2.6c.6.4 1.3.6 2.1.6 2.2 0 4-2 4-4.5zM12 11V9"/><path d="M15 8c0-1.7-1.3-3-3-3S9 6.3 9 8"/></svg>`,
    seal: `<svg data-symbol="seal" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M11.7 13.5c-1.6 0-3.3.3-4.2.9-1.8 1.2-2.5 3.6-1.5 5.6s3.6 2.5 5.6 1.5c.9-.4 1.6-.9 2.1-1.5l3-3c.6-.6.6-1.5 0-2.1l-3-3c-.5-.6-1.2-1-2-1z"/><path d="M19 9a3 3 0 0 0-3-3h-1c-1.7 0-3 1.3-3 3v1c0 1.7 1.3 3 3 3h1a3 3 0 0 0 3-3V9z"/></svg>`,
    skis: `<svg data-symbol="skis" class="w-10 h-10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="m22 2-20 20"/><path d="m22 12-4.8 4.8"/><path d="m14 2-4.8 4.8"/><path d="m7.2 12.2 2.6 2.6"/><path d="M2 22l20-20"/></svg>`,
    wild: `<svg data-symbol="wild" class="w-10 h-10 text-yellow-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2L14.5 9.5 22 12 14.5 14.5 12 22 9.5 14.5 2 12 9.5 9.5 12 2z"/><circle cx="12" cy="12" r="3"/></svg>`,
    scatter: `<svg data-symbol="scatter" class="w-10 h-10 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6c3-3 6-3 9 0s6 3 9 0"/><path d="M3 12c3-3 6-3 9 0s6 3 9 0"/><path d="M3 18c3-3 6-3 9 0s6 3 9 0"/></svg>`,
    freeze: `<svg data-symbol="freeze" class="w-10 h-10 text-blue-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5V2"/><path d="M12 12v-2"/><path d="M12 19v-2"/><path d="M12 22v-1"/><path d="M19 12h-2"/><path d="M7 12H5"/><path d="M4.2 19.8l1.4-1.4"/><path d="M18.4 5.6l-1.4 1.4"/><path d="M19.8 4.2l-1.4 1.4"/><path d="M5.6 18.4l1.4-1.4"/><path d="M5.6 5.6l1.4 1.4"/><path d="M18.4 18.4l-1.4-1.4"/></svg>`
};

// Game state for Arctic Adventure
let arcticAdventureGame = {
    isSpinning: false,
    auroraSpins: 0,
    iceThickness: 5,
    frostPower: 0,
    reels: [],
    // We now use symbol names, which map to our SVG icons.
    symbols: ['penguin', 'mountain', 'snowflake', 'ice_cube', 'polar_bear', 'seal', 'skis'],
    wildSymbol: 'wild',
    scatterSymbol: 'scatter',
    freezeSymbol: 'freeze',
    blizzardMode: false
};


function loadArcticAdventureGame() {
    const gameContent = document.getElementById('gameContent');
    // The game's HTML structure, now with SVG icons in the paytable.
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-400">ARCTIC ADVENTURE</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">BET AMOUNT</label>
                        <input type="number" id="arcticBet" value="25" min="1" max="1000"
                               class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-cyan-300">EXPEDITION TYPE</label>
                        <select id="arcticExpedition" class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="tundra">Tundra Trek (25 lines)</option>
                            <option value="glacier">Glacier Crossing (50 lines)</option>
                            <option value="blizzard">Blizzard Storm (100 lines)</option>
                        </select>
                    </div>

                    <button id="startArctic" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        BEGIN EXPEDITION
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-cyan-300">Ice Thickness:</span>
                            <span id="arcticIce" class="text-white">5cm</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-cyan-300">Last Win:</span>
                            <span id="arcticLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-cyan-300">Aurora Spins:</span>
                            <span id="arcticAurora" class="text-purple-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-cyan-900/20 rounded-lg border border-cyan-500/20">
                        <h5 class="text-sm font-bold mb-2 text-cyan-300">FROST POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="arcticFrost" class="bg-gradient-to-r from-cyan-500 to-blue-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Harness the power of eternal winter</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div id="arcticReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="arcticStatus" class="text-center text-lg font-semibold text-cyan-400 mb-4">
                        Journey into the frozen cyber wasteland for icy treasures
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-cyan-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-cyan-300 mb-2">ICE TREASURES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex items-center justify-between"><span>${arcticIcons.penguin} x5:</span><span class="text-cyan-400">3500x</span></div>
                                <div class="flex items-center justify-between"><span>${arcticIcons.mountain} x5:</span><span class="text-gray-400">1750x</span></div>
                                <div class="flex items-center justify-between"><span>${arcticIcons.snowflake} x5:</span><span class="text-blue-400">875x</span></div>
                                <div class="flex items-center justify-between"><span>${arcticIcons.ice_cube} x5:</span><span class="text-white">425x</span></div>
                            </div>
                        </div>
                        <div class="bg-cyan-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-cyan-300 mb-2">FEATURES</h6>
                            <div class="space-y-2 text-xs">
                                <div class="flex items-center">${arcticIcons.wild} <span class="ml-2"><span class="text-yellow-400">Wild:</span> Arctic explorer substitutes</span></div>
                                <div class="flex items-center">${arcticIcons.scatter} <span class="ml-2"><span class="text-purple-400">Scatter:</span> Aurora borealis appears</span></div>
                                <div class="flex items-center">${arcticIcons.freeze} <span class="ml-2"><span class="text-blue-400">Freeze:</span> Ice break multipliers</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupArcticAdventureGame();
}

function setupArcticAdventureGame() {
    const reelsContainer = document.getElementById('arcticReels');
    reelsContainer.innerHTML = ''; // Clear existing reels
    
    // Initialize the visual display of the reels with random symbols.
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbolDiv = document.createElement('div');
        symbolDiv.className = 'bg-black/50 border border-cyan-500/30 rounded-lg h-20 flex items-center justify-center text-3xl transition-all duration-300';
        const randomSymbolName = arcticAdventureGame.symbols[Math.floor(Math.random() * arcticAdventureGame.symbols.length)];
        symbolDiv.innerHTML = arcticIcons[randomSymbolName];
        reelsContainer.appendChild(symbolDiv);
    }
    arcticAdventureGame.reels = Array.from(reelsContainer.children);

    document.getElementById('startArctic').addEventListener('click', beginArcticExpedition);
    updateArcticDisplay();
}

function updateArcticDisplay() {
    document.getElementById('arcticIce').textContent = arcticAdventureGame.iceThickness + 'cm';
    document.getElementById('arcticAurora').textContent = arcticAdventureGame.auroraSpins;
    document.getElementById('arcticFrost').style.width = arcticAdventureGame.frostPower + '%';
}

function beginArcticExpedition() {
    if (arcticAdventureGame.isSpinning) return;

    const bet = parseInt(document.getElementById('arcticBet').value);
    const expedition = document.getElementById('arcticExpedition').value;
    let totalBet;

    switch(expedition) {
        case 'glacier': totalBet = bet * 50; break;
        case 'blizzard': totalBet = bet * 100; break;
        default: totalBet = bet * 25;
    }

    if (arcticAdventureGame.auroraSpins === 0 && balance < totalBet) {
        document.getElementById('arcticStatus').textContent = 'Insufficient winter gear for expedition';
        return;
    }

    if (arcticAdventureGame.auroraSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        arcticAdventureGame.auroraSpins--;
    }

    arcticAdventureGame.isSpinning = true;
    document.getElementById('startArctic').disabled = true;
    document.getElementById('arcticStatus').textContent = 'Expedition in progress...';

    // Animate the reels spinning
    let animationSteps = 24;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            arcticAdventureGame.reels.forEach(reel => {
                const randomSymbolName = arcticAdventureGame.symbols[Math.floor(Math.random() * arcticAdventureGame.symbols.length)];
                reel.innerHTML = arcticIcons[randomSymbolName];
                reel.style.transform = 'scale(0.9)';
                reel.style.filter = 'blur(2px)';
            });

            currentStep++;
            setTimeout(animateReels, 95);
        } else {
            // Determine the final symbols that land on the reels.
            const finalSymbolNames = [];
            for (let i = 0; i < 20; i++) {
                let symbolName;
                const rand = Math.random();
                if (rand < 0.12) {
                    symbolName = arcticAdventureGame.wildSymbol;
                } else if (rand < 0.20) { // 8% chance for scatter
                    symbolName = arcticAdventureGame.scatterSymbol;
                } else {
                    symbolName = arcticAdventureGame.symbols[Math.floor(Math.random() * arcticAdventureGame.symbols.length)];
                }
                finalSymbolNames.push(symbolName);
            }

            // Display the final symbols.
            arcticAdventureGame.reels.forEach((reel, index) => {
                reel.innerHTML = arcticIcons[finalSymbolNames[index]];
                reel.style.transform = 'scale(1)';
                reel.style.filter = 'blur(0px)';
            });

            checkArcticWins(finalSymbolNames, totalBet);
        }
    };

    animateReels();
}

function checkArcticWins(symbolNames, totalBet) {
    let totalWin = 0;
    
    // Check for scatter symbols to award free spins.
    // This logic is now corrected to use the 'scatter' symbol ('🌌').
    const scatterCount = symbolNames.filter(s => s === arcticAdventureGame.scatterSymbol).length;
    if (scatterCount >= 3) {
        const spinsWon = scatterCount * 6;
        arcticAdventureGame.auroraSpins += spinsWon;
        const scatterWin = Math.floor(totalBet * scatterCount * 7);
        totalWin += scatterWin;

        document.getElementById('arcticStatus').innerHTML =
            `<span class="text-purple-400">🌌 AURORA BOREALIS! ${spinsWon} free spins won!</span>`;
    }

    // Calculate wins from matching paylines.
    totalWin += calculateArcticWins(symbolNames, totalBet);

    // Update special features like Ice Thickness and Frost Power.
    const penguinCount = symbolNames.filter(s => s === 'penguin').length;
    if (penguinCount > 0) {
        arcticAdventureGame.iceThickness += penguinCount * 15;
        arcticAdventureGame.frostPower = Math.min(100, arcticAdventureGame.frostPower + penguinCount * 20);

        if (arcticAdventureGame.frostPower >= 100) {
            totalWin *= 7;
            arcticAdventureGame.frostPower = 0;
            arcticAdventureGame.iceThickness = 5;
            document.getElementById('arcticStatus').innerHTML =
                `<span class="text-cyan-400">🧊 ICE AGE COMETH! 7x frozen multiplier!</span>`;
        }
    }

    // Update balance and UI based on win amount.
    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('arcticLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('arcticStatus').innerHTML.includes('ICE AGE') && !document.getElementById('arcticStatus').innerHTML.includes('AURORA')) {
            document.getElementById('arcticStatus').innerHTML =
                `<span class="text-green-400">Arctic treasures discovered! You found ${totalWin} GA!</span>`;
        }
    } else if (!document.getElementById('arcticStatus').innerHTML.includes('AURORA')) {
        document.getElementById('arcticStatus').textContent = 'The ice holds secrets... explore deeper!';
    }

    updateArcticDisplay();

    // Re-enable the spin button after a short delay.
    setTimeout(() => {
        arcticAdventureGame.isSpinning = false;
        document.getElementById('startArctic').disabled = false;
    }, 2000);
}

function calculateArcticWins(symbolNames, totalBet) {
    let totalWin = 0;
    // Check each of the 4 rows for winning combinations.
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbolNames[row * 5 + col]);
        }

        let matchCount = 1;
        let symbolToMatch = lineSymbols[0];
        
        // Wild symbol can start a line. If so, the next non-wild determines the symbol type.
        if (symbolToMatch === arcticAdventureGame.wildSymbol) {
            let nextSymbolIndex = 1;
            while(nextSymbolIndex < 5 && lineSymbols[nextSymbolIndex] === arcticAdventureGame.wildSymbol) {
                matchCount++;
                nextSymbolIndex++;
            }
            if(nextSymbolIndex < 5) {
                 symbolToMatch = lineSymbols[nextSymbolIndex];
            }
        }


        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbolToMatch || lineSymbols[i] === arcticAdventureGame.wildSymbol) {
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getArcticMultiplier(symbolToMatch, matchCount);
            totalWin += Math.floor((totalBet / 25) * multiplier);
        }
    }

    return totalWin;
}

function getArcticMultiplier(symbolName, count) {
    // Payout multipliers are now mapped by symbol name.
    const multipliers = {
        'penguin': [0, 0, 175, 875, 3500],
        'mountain': [0, 0, 87, 437, 1750],
        'snowflake': [0, 0, 43, 218, 875],
        'ice_cube': [0, 0, 21, 106, 425],
        'polar_bear': [0, 0, 18, 90, 360],
        'seal': [0, 0, 15, 75, 300],
        'skis': [0, 0, 12, 60, 240],
        'wild': [0, 0, 175, 875, 3500], // Wild pays same as top symbol
    };

    return multipliers[symbolName] ? multipliers[symbolName][count-1] : 0;
}


// --- OTHER GAME IMPLEMENTATIONS (Unchanged) ---
// The code for the other games remains here, using emojis as before.
// You can follow the pattern above to update them with custom icons as well.

// Egyptian Eclipse Game Implementation
function loadEgyptianEclipseGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400">EGYPTIAN ECLIPSE</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">BET AMOUNT</label>
                        <input type="number" id="egyptianBet" value="35" min="1" max="1000"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">PHARAOH POWER</label>
                        <select id="egyptianPower" class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="temple">Temple Guardian (35 lines)</option>
                            <option value="pyramid">Pyramid Master (70 lines)</option>
                            <option value="pharaoh">Pharaoh Supreme (140 lines)</option>
                        </select>
                    </div>

                    <button id="invokeEclipse" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        INVOKE ECLIPSE
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-yellow-300">Solar Phase:</span>
                            <span id="egyptianSolar" class="text-white">New Moon</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-yellow-300">Last Win:</span>
                            <span id="egyptianLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-yellow-300">Sacred Spins:</span>
                            <span id="egyptianSacred" class="text-purple-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-yellow-900/20 rounded-lg border border-yellow-500/20">
                        <h5 class="text-sm font-bold mb-2 text-yellow-300">ECLIPSE POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="egyptianEclipse" class="bg-gradient-to-r from-yellow-500 to-amber-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Harness the power of solar eclipse</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <div id="egyptianReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="egyptianStatus" class="text-center text-lg font-semibold text-yellow-400 mb-4">
                        When the sun meets shadow, ancient pharaoh power awakens
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-yellow-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-yellow-300 mb-2">PHARAOH VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🔆 x5:</span><span class="text-yellow-400">4500x</span></div>
                                <div class="flex justify-between"><span>🏺 x5:</span><span class="text-brown-400">2250x</span></div>
                                <div class="flex justify-between"><span>👑 x5:</span><span class="text-gold-400">1125x</span></div>
                                <div class="flex justify-between"><span>🐍 x5:</span><span class="text-green-400">562x</span></div>
                            </div>
                        </div>
                        <div class="bg-yellow-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-yellow-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Pharaoh's spirit substitutes</div>
                                <div>🌑 <span class="text-purple-400">Scatter:</span> Eclipse shadow triggers</div>
                                <div>☀️ <span class="text-orange-400">Solar:</span> Sun god multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupEgyptianEclipseGame();
}

let egyptianEclipseGame = {
    isSpinning: false,
    sacredSpins: 0,
    solarPhase: 0,
    eclipsePower: 0,
    reels: [],
    symbols: ['🔆', '🏺', '👑', '🐍', '🪬', '🔱', '📜', '🌑', '🎯', '☀️'],
    solarPhases: ['New Moon', 'Crescent', 'Half Moon', 'Gibbous', 'Full Moon', 'Eclipse'],
    eclipseMode: false
};

function setupEgyptianEclipseGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('egyptianReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-yellow-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = egyptianEclipseGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    egyptianEclipseGame.reels = Array.from(reelsContainer.children);

    document.getElementById('invokeEclipse').addEventListener('click', invokeEgyptianEclipse);
    updateEgyptianDisplay();
}

function updateEgyptianDisplay() {
    document.getElementById('egyptianSolar').textContent = egyptianEclipseGame.solarPhases[egyptianEclipseGame.solarPhase];
    document.getElementById('egyptianSacred').textContent = egyptianEclipseGame.sacredSpins;
    document.getElementById('egyptianEclipse').style.width = egyptianEclipseGame.eclipsePower + '%';
}

function invokeEgyptianEclipse() {
    if (egyptianEclipseGame.isSpinning) return;

    const bet = parseInt(document.getElementById('egyptianBet').value);
    const power = document.getElementById('egyptianPower').value;
    let totalBet;

    switch(power) {
        case 'pyramid': totalBet = bet * 70; break;
        case 'pharaoh': totalBet = bet * 140; break;
        default: totalBet = bet * 35;
    }

    if (egyptianEclipseGame.sacredSpins === 0 && balance < totalBet) {
        document.getElementById('egyptianStatus').textContent = 'Insufficient offerings for the pharaoh gods';
        return;
    }

    if (egyptianEclipseGame.sacredSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        egyptianEclipseGame.sacredSpins--;
    }

    egyptianEclipseGame.isSpinning = true;
    document.getElementById('invokeEclipse').disabled = true;

    // Animate reels with Egyptian mystique
    let animationSteps = 28;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            egyptianEclipseGame.reels.forEach(reel => {
                reel.textContent = egyptianEclipseGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #eab308, #d97706)';
                reel.style.boxShadow = '0 0 25px #eab308';
            });

            currentStep++;
            setTimeout(animateReels, 105);
        } else {
            // Final symbols with pharaoh power
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.13) {
                    symbol = '🎯'; // Wild pharaoh spirit
                } else if (Math.random() < 0.08) {
                    symbol = '☀️'; // Solar scatter
                } else {
                    symbol = egyptianEclipseGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            egyptianEclipseGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkEgyptianWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkEgyptianWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for solar scatters (☀️)
    const scatterCount = symbols.filter(s => s === '☀️').length;
    if (scatterCount >= 3) {
        egyptianEclipseGame.sacredSpins += scatterCount * 7;
        const scatterWin = Math.floor(totalBet * scatterCount * 10);
        totalWin += scatterWin;

        document.getElementById('egyptianStatus').innerHTML =
            `<span class="text-orange-400">☀️ RA'S BLESSING! Sacred sun spins granted!</span>`;
    }

    // Calculate Egyptian wins
    totalWin += calculateEgyptianWins(symbols, totalBet);

    // Solar phase and eclipse progression
    const sunCount = symbols.filter(s => s === '🔆').length;
    if (sunCount > 0) {
        egyptianEclipseGame.solarPhase = Math.min(5, egyptianEclipseGame.solarPhase + 1);
        egyptianEclipseGame.eclipsePower = Math.min(100, egyptianEclipseGame.eclipsePower + sunCount * 20);

        if (egyptianEclipseGame.eclipsePower >= 100 && egyptianEclipseGame.solarPhase === 5) {
            totalWin *= 10;
            egyptianEclipseGame.eclipsePower = 0;
            egyptianEclipseGame.solarPhase = 0;
            document.getElementById('egyptianStatus').innerHTML =
                `<span class="text-yellow-400">🌑 TOTAL SOLAR ECLIPSE! 10x pharaoh power!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('egyptianLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('egyptianStatus').innerHTML.includes('ECLIPSE')) {
            document.getElementById('egyptianStatus').innerHTML =
                `<span class="text-green-400">Pharaoh's treasures revealed! You earned ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('egyptianStatus').textContent = 'The gods await your offering... invoke again!';
    }

    updateEgyptianDisplay();

    setTimeout(() => {
        egyptianEclipseGame.isSpinning = false;
        document.getElementById('invokeEclipse').disabled = false;
    }, 2500);
}

function calculateEgyptianWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getEgyptianMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 35) * multiplier * (1 + egyptianEclipseGame.solarPhase * 0.2));
        }
    }

    return totalWin;
}

function getEgyptianMultiplier(symbol, count) {
    const multipliers = {
        '🔆': [0, 0, 225, 1125, 4500],   // Sun
        '🏺': [0, 0, 112, 562, 2250],    // Urn
        '👑': [0, 0, 56, 281, 1125],     // Crown
        '🐍': [0, 0, 28, 140, 562],      // Snake
        '🪬': [0, 0, 22, 112, 450],      // Hamsa
        '🔱': [0, 0, 18, 90, 360],       // Trident
        '📜': [0, 0, 15, 75, 300]        // Scroll
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Wild Safari Stampede Game Implementation
function loadWildSafariGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">WILD SAFARI STAMPEDE</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">BET AMOUNT</label>
                        <input type="number" id="safariBet" value="30" min="1" max="1000"
                               class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">SAFARI TYPE</label>
                        <select id="safariType" class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="savanna">Savanna Trek (30 lines)</option>
                            <option value="jungle">Jungle Expedition (60 lines)</option>
                            <option value="migration">Great Migration (120 lines)</option>
                        </select>
                    </div>

                    <button id="startSafari" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        BEGIN SAFARI
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-green-300">Migration Level:</span>
                            <span id="safariMigration" class="text-white">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-green-300">Last Win:</span>
                            <span id="safariLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-green-300">Wild Spins:</span>
                            <span id="safariWild" class="text-yellow-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-green-900/20 rounded-lg border border-green-500/20">
                        <h5 class="text-sm font-bold mb-2 text-green-300">STAMPEDE POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="safariStampede" class="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Build stampede energy for massive wins</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <div id="safariReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="safariStatus" class="text-center text-lg font-semibold text-green-400 mb-4">
                        Journey into the cyber safari where digital wildlife roams free
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-green-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-green-300 mb-2">ANIMAL VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🦁 x5:</span><span class="text-yellow-400">4000x</span></div>
                                <div class="flex justify-between"><span>🐘 x5:</span><span class="text-gray-400">2000x</span></div>
                                <div class="flex justify-between"><span>🦏 x5:</span><span class="text-brown-400">1000x</span></div>
                                <div class="flex justify-between"><span>🦓 x5:</span><span class="text-white">500x</span></div>
                            </div>
                        </div>
                        <div class="bg-green-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-green-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Safari guide substitutes</div>
                                <div>🌿 <span class="text-green-400">Scatter:</span> Jungle bonus rounds</div>
                                <div>🏃 <span class="text-orange-400">Stampede:</span> Migration multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupWildSafariGame();
}

let wildSafariGame = {
    isSpinning: false,
    wildSpins: 0,
    migrationLevel: 1,
    stampedePower: 0,
    reels: [],
    symbols: ['🦁', '🐘', '🦏', '🦓', '🦒', '🐆', '🐃', '🌿', '🎯', '🏃'],
    migrationMode: false
};

function setupWildSafariGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('safariReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-green-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = wildSafariGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    wildSafariGame.reels = Array.from(reelsContainer.children);

    document.getElementById('startSafari').addEventListener('click', beginSafariExpedition);
    updateSafariDisplay();
}

function updateSafariDisplay() {
    document.getElementById('safariMigration').textContent = wildSafariGame.migrationLevel;
    document.getElementById('safariWild').textContent = wildSafariGame.wildSpins;
    document.getElementById('safariStampede').style.width = wildSafariGame.stampedePower + '%';
}

function beginSafariExpedition() {
    if (wildSafariGame.isSpinning) return;

    const bet = parseInt(document.getElementById('safariBet').value);
    const type = document.getElementById('safariType').value;
    let totalBet;

    switch(type) {
        case 'jungle': totalBet = bet * 60; break;
        case 'migration': totalBet = bet * 120; break;
        default: totalBet = bet * 30;
    }

    if (wildSafariGame.wildSpins === 0 && balance < totalBet) {
        document.getElementById('safariStatus').textContent = 'Insufficient safari permits for expedition';
        return;
    }

    if (wildSafariGame.wildSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        wildSafariGame.wildSpins--;
    }

    wildSafariGame.isSpinning = true;
    document.getElementById('startSafari').disabled = true;

    // Animate reels with safari effects
    let animationSteps = 25;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            wildSafariGame.reels.forEach(reel => {
                reel.textContent = wildSafariGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #22c55e, #16a34a)';
                reel.style.boxShadow = '0 0 20px #22c55e';
            });

            currentStep++;
            setTimeout(animateReels, 100);
        } else {
            // Final symbols with safari wildlife
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.12) {
                    symbol = '🎯'; // Wild safari guide
                } else if (Math.random() < 0.08) {
                    symbol = '🏃'; // Stampede scatter
                } else {
                    symbol = wildSafariGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            wildSafariGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkSafariWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkSafariWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for stampede scatters (🏃)
    const scatterCount = symbols.filter(s => s === '🏃').length;
    if (scatterCount >= 3) {
        wildSafariGame.wildSpins += scatterCount * 5;
        const scatterWin = Math.floor(totalBet * scatterCount * 8);
        totalWin += scatterWin;

        document.getElementById('safariStatus').innerHTML =
            `<span class="text-orange-400">🏃 GREAT MIGRATION! Wild stampede begins!</span>`;
    }

    // Calculate safari wins
    totalWin += calculateSafariWins(symbols, totalBet);

    // Migration and stampede progression
    const lionCount = symbols.filter(s => s === '🦁').length;
    if (lionCount > 0) {
        wildSafariGame.migrationLevel = Math.min(8, wildSafariGame.migrationLevel + 1);
        wildSafariGame.stampedePower = Math.min(100, wildSafariGame.stampedePower + lionCount * 25);

        if (wildSafariGame.stampedePower >= 100) {
            const migrationMultiplier = wildSafariGame.migrationLevel + 3;
            totalWin *= migrationMultiplier;
            wildSafariGame.stampedePower = 0;
            document.getElementById('safariStatus').innerHTML =
                `<span class="text-green-400">🦁 KING OF THE JUNGLE! ${migrationMultiplier}x safari power!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('safariLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('safariStatus').innerHTML.includes('KING')) {
            document.getElementById('safariStatus').innerHTML =
                `<span class="text-green-400">Safari treasures found! You earned ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('safariStatus').textContent = 'The wild calls... venture deeper into nature!';
    }

    updateSafariDisplay();

    setTimeout(() => {
        wildSafariGame.isSpinning = false;
        document.getElementById('startSafari').disabled = false;
    }, 2000);
}

function calculateSafariWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getSafariMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 30) * multiplier);
        }
    }

    return totalWin;
}

function getSafariMultiplier(symbol, count) {
    const multipliers = {
        '🦁': [0, 0, 200, 1000, 4000],   // Lion
        '🐘': [0, 0, 100, 500, 2000],    // Elephant
        '🦏': [0, 0, 50, 250, 1000],     // Rhino
        '🦓': [0, 0, 25, 125, 500],      // Zebra
        '🦒': [0, 0, 20, 100, 400],      // Giraffe
        '🐆': [0, 0, 18, 90, 360],       // Cheetah
        '🐃': [0, 0, 15, 75, 300]        // Buffalo
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// RoboReels 5000 Game Implementation
function loadRoboReelsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-slate-500/30">
                    <h4 class="text-xl font-bold mb-4 text-slate-400">ROBOREELS 5000</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-slate-300">BET AMOUNT</label>
                        <input type="number" id="roboBet" value="40" min="1" max="1000"
                               class="w-full bg-black/50 border border-slate-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-slate-300">ASSEMBLY MODE</label>
                        <select id="roboMode" class="w-full bg-black/50 border border-slate-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Assembly (40 lines)</option>
                            <option value="advanced">Advanced Production (80 lines)</option>
                            <option value="quantum">Quantum Manufacturing (160 lines)</option>
                        </select>
                    </div>

                    <button id="activateRobots" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        ACTIVATE ASSEMBLY
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-slate-300">Production Line:</span>
                            <span id="roboProduction" class="text-white">Level 1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-300">Last Win:</span>
                            <span id="roboLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-300">Turbo Spins:</span>
                            <span id="roboTurbo" class="text-blue-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-slate-900/20 rounded-lg border border-slate-500/20">
                        <h5 class="text-sm font-bold mb-2 text-slate-300">ASSEMBLY EFFICIENCY</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="roboEfficiency" class="bg-gradient-to-r from-slate-500 to-gray-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Optimize robots for maximum output</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-slate-500/30">
                    <div id="roboReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="roboStatus" class="text-center text-lg font-semibold text-slate-400 mb-4">
                        Welcome to the cyber factory where robots craft digital fortunes
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-slate-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-slate-300 mb-2">ROBOT VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🤖 x5:</span><span class="text-blue-400">3800x</span></div>
                                <div class="flex justify-between"><span>⚙️ x5:</span><span class="text-gray-400">1900x</span></div>
                                <div class="flex justify-between"><span>🔧 x5:</span><span class="text-yellow-400">950x</span></div>
                                <div class="flex justify-between"><span>🔩 x5:</span><span class="text-white">475x</span></div>
                            </div>
                        </div>
                        <div class="bg-slate-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-slate-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Master AI substitutes</div>
                                <div>⚡ <span class="text-blue-400">Scatter:</span> Turbo mode activation</div>
                                <div>🏭 <span class="text-orange-400">Factory:</span> Assembly line bonuses</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupRoboReelsGame();
}

let roboReelsGame = {
    isSpinning: false,
    turboSpins: 0,
    productionLevel: 1,
    assemblyEfficiency: 0,
    reels: [],
    symbols: ['🤖', '⚙️', '🔧', '🔩', '🏭', '💻', '📡', '⚡', '🎯', '🔋'],
    turboMode: false
};

function setupRoboReelsGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('roboReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-slate-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = roboReelsGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    roboReelsGame.reels = Array.from(reelsContainer.children);

    document.getElementById('activateRobots').addEventListener('click', activateRobotAssembly);
    updateRoboDisplay();
}

function updateRoboDisplay() {
    document.getElementById('roboProduction').textContent = 'Level ' + roboReelsGame.productionLevel;
    document.getElementById('roboTurbo').textContent = roboReelsGame.turboSpins;
    document.getElementById('roboEfficiency').style.width = roboReelsGame.assemblyEfficiency + '%';
}

function activateRobotAssembly() {
    if (roboReelsGame.isSpinning) return;

    const bet = parseInt(document.getElementById('roboBet').value);
    const mode = document.getElementById('roboMode').value;
    let totalBet;

    switch(mode) {
        case 'advanced': totalBet = bet * 80; break;
        case 'quantum': totalBet = bet * 160; break;
        default: totalBet = bet * 40;
    }

    if (roboReelsGame.turboSpins === 0 && balance < totalBet) {
        document.getElementById('roboStatus').textContent = 'Insufficient power cells for robot activation';
        return;
    }

    if (roboReelsGame.turboSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        roboReelsGame.turboSpins--;
    }

    roboReelsGame.isSpinning = true;
    document.getElementById('activateRobots').disabled = true;

    // Animate reels with robotic precision
    let animationSteps = 20;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            roboReelsGame.reels.forEach(reel => {
                reel.textContent = roboReelsGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #64748b, #475569)';
                reel.style.boxShadow = '0 0 20px #64748b';
            });

            currentStep++;
            setTimeout(animateReels, 85);
        } else {
            // Final symbols with mechanical precision
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.14) {
                    symbol = '🎯'; // Wild master AI
                } else if (Math.random() < 0.09) {
                    symbol = '🔋'; // Turbo scatter
                } else {
                    symbol = roboReelsGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            roboReelsGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkRoboWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkRoboWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for turbo scatters (🔋)
    const scatterCount = symbols.filter(s => s === '🔋').length;
    if (scatterCount >= 3) {
        roboReelsGame.turboSpins += scatterCount * 6;
        const scatterWin = Math.floor(totalBet * scatterCount * 9);
        totalWin += scatterWin;

        document.getElementById('roboStatus').innerHTML =
            `<span class="text-blue-400">⚡ TURBO MODE ENGAGED! Assembly line accelerated!</span>`;
    }

    // Calculate robot wins
    totalWin += calculateRoboWins(symbols, totalBet);

    // Production and efficiency progression
    const robotCount = symbols.filter(s => s === '🤖').length;
    if (robotCount > 0) {
        roboReelsGame.productionLevel = Math.min(10, roboReelsGame.productionLevel + 1);
        roboReelsGame.assemblyEfficiency = Math.min(100, roboReelsGame.assemblyEfficiency + robotCount * 20);

        if (roboReelsGame.assemblyEfficiency >= 100) {
            const productionMultiplier = roboReelsGame.productionLevel + 1;
            totalWin *= productionMultiplier;
            roboReelsGame.assemblyEfficiency = 0;
            document.getElementById('roboStatus').innerHTML =
                `<span class="text-slate-400">🏭 MAXIMUM EFFICIENCY! ${productionMultiplier}x robot power!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('roboLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('roboStatus').innerHTML.includes('EFFICIENCY')) {
            document.getElementById('roboStatus').innerHTML =
                `<span class="text-green-400">Assembly successful! You manufactured ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('roboStatus').textContent = 'The factory hums... activate more robots!';
    }

    updateRoboDisplay();

    setTimeout(() => {
        roboReelsGame.isSpinning = false;
        document.getElementById('activateRobots').disabled = false;
    }, 2000);
}

function calculateRoboWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getRoboMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 40) * multiplier);
        }
    }

    return totalWin;
}

function getRoboMultiplier(symbol, count) {
    const multipliers = {
        '🤖': [0, 0, 190, 950, 3800],    // Robot
        '⚙️': [0, 0, 95, 475, 1900],     // Gear
        '🔧': [0, 0, 47, 237, 950],      // Wrench
        '🔩': [0, 0, 23, 118, 475],      // Bolt
        '🏭': [0, 0, 20, 100, 400],      // Factory
        '💻': [0, 0, 18, 90, 360],       // Computer
        '📡': [0, 0, 15, 75, 300]        // Antenna
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Mermaid's Pearl Quest Game Implementation
function loadMermaidQuestGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-teal-500/30">
                    <h4 class="text-xl font-bold mb-4 text-teal-400">MERMAID'S PEARL QUEST</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-teal-300">BET AMOUNT</label>
                        <input type="number" id="mermaidBet" value="35" min="1" max="1000"
                               class="w-full bg-black/50 border border-teal-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-teal-300">OCEAN DEPTH</label>
                        <select id="mermaidDepth" class="w-full bg-black/50 border border-teal-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="reef">Coral Reef (35 lines)</option>
                            <option value="deep">Deep Ocean (70 lines)</option>
                            <option value="trench">Mariana Trench (140 lines)</option>
                        </select>
                    </div>

                    <button id="diveForPearls" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        DIVE FOR PEARLS
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-teal-300">Pearl Collection:</span>
                            <span id="mermaidPearls" class="text-white">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-teal-300">Last Win:</span>
                            <span id="mermaidLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-teal-300">Oceanic Spins:</span>
                            <span id="mermaidOceanic" class="text-blue-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-teal-900/20 rounded-lg border border-teal-500/20">
                        <h5 class="text-sm font-bold mb-2 text-teal-300">TIDE POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="mermaidTide" class="bg-gradient-to-r from-teal-500 to-cyan-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Harness the power of ocean tides</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-teal-500/30">
                    <div id="mermaidReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="mermaidStatus" class="text-center text-lg font-semibold text-teal-400 mb-4">
                        Dive into the cyber ocean where mermaids guard digital pearls
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-teal-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-teal-300 mb-2">OCEAN TREASURES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🧜‍♀️ x5:</span><span class="text-teal-400">4200x</span></div>
                                <div class="flex justify-between"><span>🐚 x5:</span><span class="text-white">2100x</span></div>
                                <div class="flex justify-between"><span>🐠 x5:</span><span class="text-blue-400">1050x</span></div>
                                <div class="flex justify-between"><span>🪸 x5:</span><span class="text-pink-400">525x</span></div>
                            </div>
                        </div>
                        <div class="bg-teal-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-teal-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Sea goddess substitutes</div>
                                <div>🌊 <span class="text-blue-400">Scatter:</span> Oceanic current spins</div>
                                <div>💎 <span class="text-purple-400">Pearl:</span> Treasure multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupMermaidQuestGame();
}

let mermaidQuestGame = {
    isSpinning: false,
    oceanicSpins: 0,
    pearlCollection: 0,
    tidePower: 0,
    reels: [],
    symbols: ['🧜‍♀️', '🐚', '🐠', '🪸', '🐙', '🦈', '⭐', '🌊', '🎯', '💎'],
    questMode: false
};

function setupMermaidQuestGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('mermaidReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-teal-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = mermaidQuestGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    mermaidQuestGame.reels = Array.from(reelsContainer.children);

    document.getElementById('diveForPearls').addEventListener('click', diveForOceanPearls);
    updateMermaidDisplay();
}

function updateMermaidDisplay() {
    document.getElementById('mermaidPearls').textContent = mermaidQuestGame.pearlCollection;
    document.getElementById('mermaidOceanic').textContent = mermaidQuestGame.oceanicSpins;
    document.getElementById('mermaidTide').style.width = mermaidQuestGame.tidePower + '%';
}

function diveForOceanPearls() {
    if (mermaidQuestGame.isSpinning) return;

    const bet = parseInt(document.getElementById('mermaidBet').value);
    const depth = document.getElementById('mermaidDepth').value;
    let totalBet;

    switch(depth) {
        case 'deep': totalBet = bet * 70; break;
        case 'trench': totalBet = bet * 140; break;
        default: totalBet = bet * 35;
    }

    if (mermaidQuestGame.oceanicSpins === 0 && balance < totalBet) {
        document.getElementById('mermaidStatus').textContent = 'Insufficient diving equipment for ocean quest';
        return;
    }

    if (mermaidQuestGame.oceanicSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        mermaidQuestGame.oceanicSpins--;
    }

    mermaidQuestGame.isSpinning = true;
    document.getElementById('diveForPearls').disabled = true;

    // Animate reels with oceanic flow
    let animationSteps = 23;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            mermaidQuestGame.reels.forEach(reel => {
                reel.textContent = mermaidQuestGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #14b8a6, #0891b2)';
                reel.style.boxShadow = '0 0 20px #14b8a6';
            });

            currentStep++;
            setTimeout(animateReels, 90);
        } else {
            // Final symbols with oceanic treasures
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.13) {
                    symbol = '🎯'; // Wild sea goddess
                } else if (Math.random() < 0.08) {
                    symbol = '💎'; // Pearl scatter
                } else {
                    symbol = mermaidQuestGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            mermaidQuestGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkMermaidWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkMermaidWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for pearl scatters (💎)
    const scatterCount = symbols.filter(s => s === '💎').length;
    if (scatterCount >= 3) {
        mermaidQuestGame.oceanicSpins += scatterCount * 7;
        mermaidQuestGame.pearlCollection += scatterCount;
        const scatterWin = Math.floor(totalBet * scatterCount * 10);
        totalWin += scatterWin;

        document.getElementById('mermaidStatus').innerHTML =
            `<span class="text-purple-400">💎 PEARL TREASURE FOUND! Oceanic blessing granted!</span>`;
    }

    // Calculate mermaid wins
    totalWin += calculateMermaidWins(symbols, totalBet);

    // Tide power and pearl progression
    const mermaidCount = symbols.filter(s => s === '🧜‍♀️').length;
    if (mermaidCount > 0) {
        mermaidQuestGame.tidePower = Math.min(100, mermaidQuestGame.tidePower + mermaidCount * 25);

        // Pearl collection bonus
        const pearlBonus = 1 + (mermaidQuestGame.pearlCollection * 0.3);
        totalWin = Math.floor(totalWin * pearlBonus);

        if (mermaidQuestGame.tidePower >= 100) {
            totalWin *= 8;
            mermaidQuestGame.tidePower = 0;
            document.getElementById('mermaidStatus').innerHTML =
                `<span class="text-teal-400">🌊 TSUNAMI TIDE! 8x oceanic power!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('mermaidLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('mermaidStatus').innerHTML.includes('TSUNAMI')) {
            document.getElementById('mermaidStatus').innerHTML =
                `<span class="text-green-400">Ocean treasures discovered! You found ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('mermaidStatus').textContent = 'The ocean depths call... dive deeper for pearls!';
    }

    updateMermaidDisplay();

    setTimeout(() => {
        mermaidQuestGame.isSpinning = false;
        document.getElementById('diveForPearls').disabled = false;
    }, 2000);
}

function calculateMermaidWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getMermaidMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 35) * multiplier);
        }
    }

    return totalWin;
}

function getMermaidMultiplier(symbol, count) {
    const multipliers = {
        '🧜‍♀️': [0, 0, 210, 1050, 4200],  // Mermaid
        '🐚': [0, 0, 105, 525, 2100],     // Shell
        '🐠': [0, 0, 52, 262, 1050],      // Fish
        '🪸': [0, 0, 26, 131, 525],       // Coral
        '🐙': [0, 0, 22, 110, 440],       // Octopus
        '🦈': [0, 0, 18, 90, 360],        // Shark
        '⭐': [0, 0, 15, 75, 300]         // Starfish
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Dino Gold Digger Game Implementation
function loadDinoGoldGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <h4 class="text-xl font-bold mb-4 text-orange-400">DINO GOLD DIGGER</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-orange-300">BET AMOUNT</label>
                        <input type="number" id="dinoBet" value="30" min="1" max="1000"
                               class="w-full bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-orange-300">EXCAVATION ERA</label>
                        <select id="dinoEra" class="w-full bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="triassic">Triassic Period (30 lines)</option>
                            <option value="jurassic">Jurassic Period (60 lines)</option>
                            <option value="cretaceous">Cretaceous Period (120 lines)</option>
                        </select>
                    </div>

                    <button id="excavateFossils" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        EXCAVATE FOSSILS
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-orange-300">Fossil Depth:</span>
                            <span id="dinoDepth" class="text-white">1m</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-orange-300">Last Win:</span>
                            <span id="dinoLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-orange-300">Amber Spins:</span>
                            <span id="dinoAmber" class="text-yellow-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-orange-900/20 rounded-lg border border-orange-500/20">
                        <h5 class="text-sm font-bold mb-2 text-orange-300">PREHISTORIC POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="dinoPrehistoric" class="bg-gradient-to-r from-orange-500 to-amber-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Uncover ancient powers hidden in time</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                    <div id="dinoReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="dinoStatus" class="text-center text-lg font-semibold text-orange-400 mb-4">
                        Journey back to prehistoric times for cyber dinosaur treasures
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-orange-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-orange-300 mb-2">FOSSIL VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🦕 x5:</span><span class="text-green-400">4500x</span></div>
                                <div class="flex justify-between"><span>🦖 x5:</span><span class="text-red-400">2250x</span></div>
                                <div class="flex justify-between"><span>🥚 x5:</span><span class="text-white">1125x</span></div>
                                <div class="flex justify-between"><span>🦴 x5:</span><span class="text-gray-400">562x</span></div>
                            </div>
                        </div>
                        <div class="bg-orange-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-orange-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Paleontologist substitutes</div>
                                <div>💎 <span class="text-yellow-400">Scatter:</span> Amber preservation spins</div>
                                <div>🌋 <span class="text-red-400">Extinction:</span> Meteor multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupDinoGoldGame();
}

let dinoGoldGame = {
    isSpinning: false,
    amberSpins: 0,
    fossilDepth: 1,
    prehistoricPower: 0,
    reels: [],
    symbols: ['🦕', '🦖', '🥚', '🦴', '🌿', '🌋', '🪨', '💎', '🎯', '🔥'],
    extinctionMode: false
};

function setupDinoGoldGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('dinoReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-orange-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = dinoGoldGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    dinoGoldGame.reels = Array.from(reelsContainer.children);

    document.getElementById('excavateFossils').addEventListener('click', excavatePrehistoricFossils);
    updateDinoDisplay();
}

function updateDinoDisplay() {
    document.getElementById('dinoDepth').textContent = dinoGoldGame.fossilDepth + 'm';
    document.getElementById('dinoAmber').textContent = dinoGoldGame.amberSpins;
    document.getElementById('dinoPrehistoric').style.width = dinoGoldGame.prehistoricPower + '%';
}

function excavatePrehistoricFossils() {
    if (dinoGoldGame.isSpinning) return;

    const bet = parseInt(document.getElementById('dinoBet').value);
    const era = document.getElementById('dinoEra').value;
    let totalBet;

    switch(era) {
        case 'jurassic': totalBet = bet * 60; break;
        case 'cretaceous': totalBet = bet * 120; break;
        default: totalBet = bet * 30;
    }

    if (dinoGoldGame.amberSpins === 0 && balance < totalBet) {
        document.getElementById('dinoStatus').textContent = 'Insufficient excavation equipment for fossil dig';
        return;
    }

    if (dinoGoldGame.amberSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        dinoGoldGame.amberSpins--;
    }

    dinoGoldGame.isSpinning = true;
    document.getElementById('excavateFossils').disabled = true;

    // Animate reels with prehistoric energy
    let animationSteps = 26;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            dinoGoldGame.reels.forEach(reel => {
                reel.textContent = dinoGoldGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #ea580c, #d97706)';
                reel.style.boxShadow = '0 0 20px #ea580c';
            });

            currentStep++;
            setTimeout(animateReels, 95);
        } else {
            // Final symbols with dinosaur power
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.12) {
                    symbol = '🎯'; // Wild paleontologist
                } else if (Math.random() < 0.08) {
                    symbol = '🔥'; // Extinction scatter
                } else {
                    symbol = dinoGoldGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            dinoGoldGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkDinoWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkDinoWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for extinction scatters (🔥)
    const scatterCount = symbols.filter(s => s === '🔥').length;
    if (scatterCount >= 3) {
        dinoGoldGame.amberSpins += scatterCount * 8;
        const scatterWin = Math.floor(totalBet * scatterCount * 12);
        totalWin += scatterWin;

        document.getElementById('dinoStatus').innerHTML =
            `<span class="text-red-400">🌋 METEOR IMPACT! Extinction event triggered!</span>`;
    }

    // Calculate dino wins
    totalWin += calculateDinoWins(symbols, totalBet);

    // Fossil depth and prehistoric progression
    const dinoCount = symbols.filter(s => ['🦕', '🦖'].includes(s)).length;
    if (dinoCount > 0) {
        dinoGoldGame.fossilDepth += dinoCount * 5;
        dinoGoldGame.prehistoricPower = Math.min(100, dinoGoldGame.prehistoricPower + dinoCount * 25);

        if (dinoGoldGame.prehistoricPower >= 100) {
            totalWin *= 9;
            dinoGoldGame.prehistoricPower = 0;
            dinoGoldGame.fossilDepth = 1;
            document.getElementById('dinoStatus').innerHTML =
                `<span class="text-orange-400">🦕 JURASSIC JACKPOT! 9x prehistoric power!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('dinoLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('dinoStatus').innerHTML.includes('JACKPOT')) {
            document.getElementById('dinoStatus').innerHTML =
                `<span class="text-green-400">Fossil treasures unearthed! You discovered ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('dinoStatus').textContent = 'Ancient secrets buried deep... dig deeper!';
    }

    updateDinoDisplay();

    setTimeout(() => {
        dinoGoldGame.isSpinning = false;
        document.getElementById('excavateFossils').disabled = false;
    }, 2500);
}

function calculateDinoWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getDinoMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 30) * multiplier);
        }
    }

    return totalWin;
}

function getDinoMultiplier(symbol, count) {
    const multipliers = {
        '🦕': [0, 0, 225, 1125, 4500],   // Brontosaurus
        '🦖': [0, 0, 112, 562, 2250],    // T-Rex
        '🥚': [0, 0, 56, 281, 1125],     // Egg
        '🦴': [0, 0, 28, 140, 562],      // Bone
        '🌿': [0, 0, 22, 112, 450],      // Fern
        '🌋': [0, 0, 18, 90, 360],       // Volcano
        '🪨': [0, 0, 15, 75, 300]        // Rock
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Wizard's Wealth Game Implementation
function loadWizardWealthGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">WIZARD'S WEALTH</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">BET AMOUNT</label>
                        <input type="number" id="wizardBet" value="40" min="1" max="1000"
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">MAGIC SCHOOL</label>
                        <select id="wizardSchool" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="apprentice">Apprentice Academy (40 lines)</option>
                            <option value="master">Master's Guild (80 lines)</option>
                            <option value="archmage">Archmage Tower (160 lines)</option>
                        </select>
                    </div>

                    <button id="castSpells" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        CAST SPELLS
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-purple-300">Spell Level:</span>
                            <span id="wizardLevel" class="text-white">Novice</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-purple-300">Last Win:</span>
                            <span id="wizardLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-purple-300">Mystic Spins:</span>
                            <span id="wizardMystic" class="text-blue-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300">MAGICAL POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="wizardMagic" class="bg-gradient-to-r from-purple-500 to-violet-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Accumulate magical energy for ultimate spells</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="wizardReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="wizardStatus" class="text-center text-lg font-semibold text-purple-400 mb-4">
                        Enter the cyber realm where digital wizards weave spells of fortune
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">SPELL VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🧙‍♂️ x5:</span><span class="text-purple-400">5000x</span></div>
                                <div class="flex justify-between"><span>🔮 x5:</span><span class="text-blue-400">2500x</span></div>
                                <div class="flex justify-between"><span>📖 x5:</span><span class="text-yellow-400">1250x</span></div>
                                <div class="flex justify-between"><span>🪄 x5:</span><span class="text-pink-400">625x</span></div>
                            </div>
                        </div>
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Grand wizard substitutes</div>
                                <div>✨ <span class="text-blue-400">Scatter:</span> Mystic portal spins</div>
                                <div>🌟 <span class="text-purple-400">Enchant:</span> Spell multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupWizardWealthGame();
}

let wizardWealthGame = {
    isSpinning: false,
    mysticSpins: 0,
    spellLevel: 0,
    magicalPower: 0,
    reels: [],
    symbols: ['🧙‍♂️', '🔮', '📖', '🪄', '🌙', '⭐', '🕯️', '✨', '🎯', '🌟'],
    spellLevels: ['Novice', 'Adept', 'Expert', 'Master', 'Archmage', 'Legendary'],
    enchantmentMode: false
};

function setupWizardWealthGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('wizardReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-purple-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = wizardWealthGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    wizardWealthGame.reels = Array.from(reelsContainer.children);

    document.getElementById('castSpells').addEventListener('click', castMagicalSpells);
    updateWizardDisplay();
}

function updateWizardDisplay() {
    document.getElementById('wizardLevel').textContent = wizardWealthGame.spellLevels[wizardWealthGame.spellLevel];
    document.getElementById('wizardMystic').textContent = wizardWealthGame.mysticSpins;
    document.getElementById('wizardMagic').style.width = wizardWealthGame.magicalPower + '%';
}

function castMagicalSpells() {
    if (wizardWealthGame.isSpinning) return;

    const bet = parseInt(document.getElementById('wizardBet').value);
    const school = document.getElementById('wizardSchool').value;
    let totalBet;

    switch(school) {
        case 'master': totalBet = bet * 80; break;
        case 'archmage': totalBet = bet * 160; break;
        default: totalBet = bet * 40;
    }

    if (wizardWealthGame.mysticSpins === 0 && balance < totalBet) {
        document.getElementById('wizardStatus').textContent = 'Insufficient mana crystals for spell casting';
        return;
    }

    if (wizardWealthGame.mysticSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        wizardWealthGame.mysticSpins--;
    }

    wizardWealthGame.isSpinning = true;
    document.getElementById('castSpells').disabled = true;

    // Animate reels with magical energy
    let animationSteps = 30;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            wizardWealthGame.reels.forEach(reel => {
                reel.textContent = wizardWealthGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #8b5cf6, #7c3aed)';
                reel.style.boxShadow = '0 0 25px #8b5cf6';
            });

            currentStep++;
            setTimeout(animateReels, 100);
        } else {
            // Final symbols with magical power
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.15) {
                    symbol = '🎯'; // Wild grand wizard
                } else if (Math.random() < 0.09) {
                    symbol = '🌟'; // Enchantment scatter
                } else {
                    symbol = wizardWealthGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            wizardWealthGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkWizardWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkWizardWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for enchantment scatters (🌟)
    const scatterCount = symbols.filter(s => s === '🌟').length;
    if (scatterCount >= 3) {
        wizardWealthGame.mysticSpins += scatterCount * 10;
        const scatterWin = Math.floor(totalBet * scatterCount * 15);
        totalWin += scatterWin;

        document.getElementById('wizardStatus').innerHTML =
            `<span class="text-blue-400">✨ MYSTIC PORTAL OPENED! Ancient magic flows!</span>`;
    }

    // Calculate wizard wins
    totalWin += calculateWizardWins(symbols, totalBet);

    // Spell level and magical progression
    const wizardCount = symbols.filter(s => s === '🧙‍♂️').length;
    if (wizardCount > 0) {
        wizardWealthGame.spellLevel = Math.min(5, wizardWealthGame.spellLevel + 1);
        wizardWealthGame.magicalPower = Math.min(100, wizardWealthGame.magicalPower + wizardCount * 20);

        if (wizardWealthGame.magicalPower >= 100 && wizardWealthGame.spellLevel === 5) {
            totalWin *= 12;
            wizardWealthGame.magicalPower = 0;
            wizardWealthGame.spellLevel = 0;
            document.getElementById('wizardStatus').innerHTML =
                `<span class="text-purple-400">🧙‍♂️ LEGENDARY SPELL MASTERY! 12x archmage power!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('wizardLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('wizardStatus').innerHTML.includes('LEGENDARY')) {
            document.getElementById('wizardStatus').innerHTML =
                `<span class="text-green-400">Magical fortune granted! You conjured ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('wizardStatus').textContent = 'The magic awaits... cast more powerful spells!';
    }

    updateWizardDisplay();

    setTimeout(() => {
        wizardWealthGame.isSpinning = false;
        document.getElementById('castSpells').disabled = false;
    }, 2500);
}

function calculateWizardWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getWizardMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 40) * multiplier * (1 + wizardWealthGame.spellLevel * 0.25));
        }
    }

    return totalWin;
}

function getWizardMultiplier(symbol, count) {
    const multipliers = {
        '🧙‍♂️': [0, 0, 250, 1250, 5000],  // Wizard
        '🔮': [0, 0, 125, 625, 2500],     // Crystal Ball
        '📖': [0, 0, 62, 312, 1250],      // Spellbook
        '🪄': [0, 0, 31, 156, 625],       // Wand
        '🌙': [0, 0, 25, 125, 500],       // Moon
        '⭐': [0, 0, 20, 100, 400],       // Star
        '🕯️': [0, 0, 15, 75, 300]         // Candle
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Kraken's Deep Dive Game Implementation
function loadKrakenDiveGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <h4 class="text-xl font-bold mb-4 text-indigo-400">KRAKEN'S DEEP DIVE</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-indigo-300">BET AMOUNT</label>
                        <input type="number" id="krakenBet" value="45" min="1" max="1000"
                               class="w-full bg-black/50 border border-indigo-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-indigo-300">DIVE DEPTH</label>
                        <select id="krakenDepth" class="w-full bg-black/50 border border-indigo-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="shallow">Shallow Waters (45 lines)</option>
                            <option value="deep">Deep Abyss (90 lines)</option>
                            <option value="trench">Mariana Trench (180 lines)</option>
                        </select>
                    </div>

                    <button id="diveToKraken" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        DIVE TO KRAKEN
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-indigo-300">Tentacle Power:</span>
                            <span id="krakenTentacles" class="text-white">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-indigo-300">Last Win:</span>
                            <span id="krakenLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-indigo-300">Abyss Spins:</span>
                            <span id="krakenAbyss" class="text-purple-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-indigo-900/20 rounded-lg border border-indigo-500/20">
                        <h5 class="text-sm font-bold mb-2 text-indigo-300">KRAKEN RAGE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="krakenRage" class="bg-gradient-to-r from-indigo-500 to-purple-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Awaken the ancient sea beast's fury</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <div id="krakenReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="krakenStatus" class="text-center text-lg font-semibold text-indigo-400 mb-4">
                        Descend into the cyber abyss where the digital kraken guards ancient treasures
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-indigo-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-indigo-300 mb-2">ABYSS TREASURES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🐙 x5:</span><span class="text-indigo-400">5500x</span></div>
                                <div class="flex justify-between"><span>🪼 x5:</span><span class="text-purple-400">2750x</span></div>
                                <div class="flex justify-between"><span>⚓ x5:</span><span class="text-gray-400">1375x</span></div>
                                <div class="flex justify-between"><span>🦑 x5:</span><span class="text-blue-400">687x</span></div>
                            </div>
                        </div>
                        <div class="bg-indigo-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-indigo-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Deep sea diver substitutes</div>
                                <div>💀 <span class="text-red-400">Scatter:</span> Kraken's wrath unleashed</div>
                                <div>🌊 <span class="text-blue-400">Tentacle:</span> Crushing grip multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupKrakenDiveGame();
}

let krakenDiveGame = {
    isSpinning: false,
    abyssSpins: 0,
    tentaclePower: 0,
    krakenRage: 0,
    reels: [],
    symbols: ['🐙', '🪼', '⚓', '🦑', '🌊', '🐠', '🦈', '💀', '🎯', '⚡'],
    rageMode: false
};

function setupKrakenDiveGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('krakenReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-indigo-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = krakenDiveGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    krakenDiveGame.reels = Array.from(reelsContainer.children);

    document.getElementById('diveToKraken').addEventListener('click', diveToKrakenDepths);
    updateKrakenDisplay();
}

function updateKrakenDisplay() {
    document.getElementById('krakenTentacles').textContent = krakenDiveGame.tentaclePower;
    document.getElementById('krakenAbyss').textContent = krakenDiveGame.abyssSpins;
    document.getElementById('krakenRage').style.width = krakenDiveGame.krakenRage + '%';
}

function diveToKrakenDepths() {
    if (krakenDiveGame.isSpinning) return;

    const bet = parseInt(document.getElementById('krakenBet').value);
    const depth = document.getElementById('krakenDepth').value;
    let totalBet;

    switch(depth) {
        case 'deep': totalBet = bet * 90; break;
        case 'trench': totalBet = bet * 180; break;
        default: totalBet = bet * 45;
    }

    if (krakenDiveGame.abyssSpins === 0 && balance < totalBet) {
        document.getElementById('krakenStatus').textContent = 'Insufficient diving gear for kraken encounter';
        return;
    }

    if (krakenDiveGame.abyssSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        krakenDiveGame.abyssSpins--;
    }

    krakenDiveGame.isSpinning = true;
    document.getElementById('diveToKraken').disabled = true;

    // Animate reels with abyssal energy
    let animationSteps = 28;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            krakenDiveGame.reels.forEach(reel => {
                reel.textContent = krakenDiveGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #6366f1, #8b5cf6)';
                reel.style.boxShadow = '0 0 25px #6366f1';
            });

            currentStep++;
            setTimeout(animateReels, 100);
        } else {
            // Final symbols with kraken power
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.14) {
                    symbol = '🎯'; // Wild deep sea diver
                } else if (Math.random() < 0.08) {
                    symbol = '⚡'; // Kraken rage scatter
                } else {
                    symbol = krakenDiveGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            krakenDiveGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkKrakenWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkKrakenWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for kraken rage scatters (⚡)
    const scatterCount = symbols.filter(s => s === '⚡').length;
    if (scatterCount >= 3) {
        krakenDiveGame.abyssSpins += scatterCount * 12;
        const scatterWin = Math.floor(totalBet * scatterCount * 18);
        totalWin += scatterWin;

        document.getElementById('krakenStatus').innerHTML =
            `<span class="text-purple-400">⚡ KRAKEN'S WRATH! Ancient fury unleashed!</span>`;
    }

    // Calculate kraken wins
    totalWin += calculateKrakenWins(symbols, totalBet);

    // Tentacle power and rage progression
    const krakenCount = symbols.filter(s => s === '🐙').length;
    if (krakenCount > 0) {
        krakenDiveGame.tentaclePower += krakenCount * 3;
        krakenDiveGame.krakenRage = Math.min(100, krakenDiveGame.krakenRage + krakenCount * 30);

        if (krakenDiveGame.krakenRage >= 100) {
            const tentacleMultiplier = 10 + krakenDiveGame.tentaclePower;
            totalWin *= tentacleMultiplier;
            krakenDiveGame.krakenRage = 0;
            document.getElementById('krakenStatus').innerHTML =
                `<span class="text-indigo-400">🐙 LEVIATHAN AWAKENS! ${tentacleMultiplier}x tentacle devastation!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('krakenLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('krakenStatus').innerHTML.includes('LEVIATHAN')) {
            document.getElementById('krakenStatus').innerHTML =
                `<span class="text-green-400">Abyssal treasures claimed! You earned ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('krakenStatus').textContent = 'The kraken stirs in the depths... dive deeper!';
    }

    updateKrakenDisplay();

    setTimeout(() => {
        krakenDiveGame.isSpinning = false;
        document.getElementById('diveToKraken').disabled = false;
    }, 2500);
}

function calculateKrakenWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getKrakenMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 45) * multiplier);
        }
    }

    return totalWin;
}

function getKrakenMultiplier(symbol, count) {
    const multipliers = {
        '🐙': [0, 0, 275, 1375, 5500],   // Kraken
        '🪼': [0, 0, 137, 687, 2750],    // Jellyfish
        '⚓': [0, 0, 68, 343, 1375],     // Anchor
        '🦑': [0, 0, 34, 171, 687],      // Squid
        '🌊': [0, 0, 27, 137, 550],      // Wave
        '🐠': [0, 0, 22, 110, 440],      // Fish
        '🦈': [0, 0, 18, 90, 360]        // Shark
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Lucky Lunar Spins Game Implementation
function loadLunarSpinsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <h4 class="text-xl font-bold mb-4 text-gray-400">LUCKY LUNAR SPINS</h4>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">BET AMOUNT</label>
                        <input type="number" id="lunarBet" value="35" min="1" max="1000"
                               class="w-full bg-black/50 border border-gray-500/30 rounded-lg px-3 py-2 text-white">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">LUNAR MISSION</label>
                        <select id="lunarMission" class="w-full bg-black/50 border border-gray-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="orbit">Moon Orbit (35 lines)</option>
                            <option value="landing">Moon Landing (70 lines)</option>
                            <option value="exploration">Deep Space (140 lines)</option>
                        </select>
                    </div>

                    <button id="launchToMoon" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        LAUNCH TO MOON
                    </button>

                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Lunar Phase:</span>
                            <span id="lunarPhase" class="text-white">New Moon</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Win:</span>
                            <span id="lunarLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Cosmic Spins:</span>
                            <span id="lunarCosmic" class="text-blue-400">0</span>
                        </div>
                    </div>

                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-gray-500/20">
                        <h5 class="text-sm font-bold mb-2 text-gray-300">COSMIC ENERGY</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="lunarCosticEnergy" class="bg-gradient-to-r from-gray-500 to-slate-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Collect stardust for lunar bonuses</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-gray-500/30">
                    <div id="lunarReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>

                    <div id="lunarStatus" class="text-center text-lg font-semibold text-gray-400 mb-4">
                        Journey to the cyber moon where digital astronauts seek cosmic fortunes
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">COSMIC VALUES</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🌙 x5:</span><span class="text-gray-400">4800x</span></div>
                                <div class="flex justify-between"><span>🚀 x5:</span><span class="text-red-400">2400x</span></div>
                                <div class="flex justify-between"><span>👨‍🚀 x5:</span><span class="text-blue-400">1200x</span></div>
                                <div class="flex justify-between"><span>🌟 x5:</span><span class="text-yellow-400">600x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Space commander substitutes</div>
                                <div>🌌 <span class="text-purple-400">Scatter:</span> Galaxy portal opens</div>
                                <div>⭐ <span class="text-blue-400">Stellar:</span> Constellation multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupLunarSpinsGame();
}

let lunarSpinsGame = {
    isSpinning: false,
    cosmicSpins: 0,
    lunarPhase: 0,
    cosmicEnergy: 0,
    reels: [],
    symbols: ['🌙', '🚀', '👨‍🚀', '🌟', '🛰️', '🌍', '☄️', '🌌', '🎯', '⭐'],
    lunarPhases: ['New Moon', 'Crescent', 'Quarter', 'Gibbous', 'Full Moon', 'Super Moon'],
    eclipseMode: false
};

function setupLunarSpinsGame() {
    // Initialize reels display
    const reelsContainer = document.getElementById('lunarReels');
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-gray-500/30 rounded-lg h-18 flex items-center justify-center text-3xl transition-all duration-300';
        symbol.textContent = lunarSpinsGame.symbols[Math.floor(Math.random() * 8)];
        reelsContainer.appendChild(symbol);
    }
    lunarSpinsGame.reels = Array.from(reelsContainer.children);

    document.getElementById('launchToMoon').addEventListener('click', launchLunarMission);
    updateLunarDisplay();
}

function updateLunarDisplay() {
    document.getElementById('lunarPhase').textContent = lunarSpinsGame.lunarPhases[lunarSpinsGame.lunarPhase];
    document.getElementById('lunarCosmic').textContent = lunarSpinsGame.cosmicSpins;
    document.getElementById('lunarCosticEnergy').style.width = lunarSpinsGame.cosmicEnergy + '%';
}

function launchLunarMission() {
    if (lunarSpinsGame.isSpinning) return;

    const bet = parseInt(document.getElementById('lunarBet').value);
    const mission = document.getElementById('lunarMission').value;
    let totalBet;

    switch(mission) {
        case 'landing': totalBet = bet * 70; break;
        case 'exploration': totalBet = bet * 140; break;
        default: totalBet = bet * 35;
    }

    if (lunarSpinsGame.cosmicSpins === 0 && balance < totalBet) {
        document.getElementById('lunarStatus').textContent = 'Insufficient rocket fuel for lunar mission';
        return;
    }

    if (lunarSpinsGame.cosmicSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        lunarSpinsGame.cosmicSpins--;
    }

    lunarSpinsGame.isSpinning = true;
    document.getElementById('launchToMoon').disabled = true;

    // Animate reels with cosmic energy
    let animationSteps = 24;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            lunarSpinsGame.reels.forEach(reel => {
                reel.textContent = lunarSpinsGame.symbols[Math.floor(Math.random() * 8)];
                reel.style.transform = 'scale(1.05)';
                reel.style.background = 'linear-gradient(45deg, #64748b, #475569)';
                reel.style.boxShadow = '0 0 20px #64748b';
            });

            currentStep++;
            setTimeout(animateReels, 95);
        } else {
            // Final symbols with lunar power
            const finalSymbols = [];
            for (let i = 0; i < 20; i++) {
                let symbol;
                if (Math.random() < 0.13) {
                    symbol = '🎯'; // Wild space commander
                } else if (Math.random() < 0.08) {
                    symbol = '⭐'; // Stellar scatter
                } else {
                    symbol = lunarSpinsGame.symbols[Math.floor(Math.random() * 8)];
                }
                finalSymbols.push(symbol);
            }

            lunarSpinsGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkLunarWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkLunarWins(symbols, totalBet) {
    let totalWin = 0;

    // Check for stellar scatters (⭐)
    const scatterCount = symbols.filter(s => s === '⭐').length;
    if (scatterCount >= 3) {
        lunarSpinsGame.cosmicSpins += scatterCount * 9;
        const scatterWin = Math.floor(totalBet * scatterCount * 14);
        totalWin += scatterWin;

        document.getElementById('lunarStatus').innerHTML =
            `<span class="text-blue-400">🌌 GALAXY PORTAL! Cosmic pathway opened!</span>`;
    }

    // Calculate lunar wins
    totalWin += calculateLunarWins(symbols, totalBet);

    // Lunar phase and cosmic progression
    const moonCount = symbols.filter(s => s === '🌙').length;
    if (moonCount > 0) {
        lunarSpinsGame.lunarPhase = Math.min(5, lunarSpinsGame.lunarPhase + 1);
        lunarSpinsGame.cosmicEnergy = Math.min(100, lunarSpinsGame.cosmicEnergy + moonCount * 25);

        if (lunarSpinsGame.cosmicEnergy >= 100 && lunarSpinsGame.lunarPhase === 5) {
            totalWin *= 15;
            lunarSpinsGame.cosmicEnergy = 0;
            lunarSpinsGame.lunarPhase = 0;
            document.getElementById('lunarStatus').innerHTML =
                `<span class="text-gray-400">🌙 SUPER MOON ECLIPSE! 15x cosmic convergence!</span>`;
        }
    }

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();

        document.getElementById('lunarLastWin').textContent = totalWin + ' GA';
        if (!document.getElementById('lunarStatus').innerHTML.includes('ECLIPSE')) {
            document.getElementById('lunarStatus').innerHTML =
                `<span class="text-green-400">Cosmic treasures discovered! You found ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('lunarStatus').textContent = 'The cosmos beckons... venture into deep space!';
    }

    updateLunarDisplay();

    setTimeout(() => {
        lunarSpinsGame.isSpinning = false;
        document.getElementById('launchToMoon').disabled = false;
    }, 2500);
}

function calculateLunarWins(symbols, totalBet) {
    let totalWin = 0;
    // Calculate wins for 5x4 grid
    for (let row = 0; row < 4; row++) {
        let lineSymbols = [];
        for (let col = 0; col < 5; col++) {
            lineSymbols.push(symbols[row * 5 + col]);
        }

        let matchCount = 1;
        let symbol = lineSymbols[0];

        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i] === symbol || lineSymbols[i] === '🎯' || symbol === '🎯') {
                if (symbol === '🎯') symbol = lineSymbols[i];
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getLunarMultiplier(symbol, matchCount);
            totalWin += Math.floor((totalBet / 35) * multiplier * (1 + lunarSpinsGame.lunarPhase * 0.3));
        }
    }

    return totalWin;
}

function getLunarMultiplier(symbol, count) {
    const multipliers = {
        '🌙': [0, 0, 240, 1200, 4800],   // Moon
        '🚀': [0, 0, 120, 600, 2400],    // Rocket
        '👨‍🚀': [0, 0, 60, 300, 1200],     // Astronaut
        '🌟': [0, 0, 30, 150, 600],      // Star
        '🛰️': [0, 0, 24, 120, 480],      // Satellite
        '🌍': [0, 0, 20, 100, 400],      // Earth
        '☄️': [0, 0, 15, 75, 300]        // Comet
    };

    return multipliers[symbol] ? multipliers[symbol][count-1] : 0;
}

// Initialize the game when the page loads.
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    // Load the main game by default.
    loadArcticAdventureGame();
});
