// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-yellow-400 font-bold">${balance} GA</span>`;
}

// --- Dino Gold Game Implementation ---

// Game constants for Dino Gold
const DINO_SYMBOLS = {
    WILD: '🌋',      // Volcano
    SCATTER: '🥚',    // Dino Egg
    BONUS: '🪙',      // Gold Coin
    TREX: '🦖',
    BRONTO: '🦕',
    TRICERATOPS: '🦎', // Using lizard as placeholder
    FERN: '🌿',
    FOSSIL: '🦴',
    ROCK: '🪨',
};

const DINO_REEL_SYMBOLS = [
    DINO_SYMBOLS.TREX, DINO_SYMBOLS.BRONTO, DINO_SYMBOLS.TRICERATOPS, DINO_SYMBOLS.FERN,
    DINO_SYMBOLS.FOSSIL, DINO_SYMBOLS.ROCK, DINO_SYMBOLS.WILD, DINO_SYMBOLS.SCATTER, DINO_SYMBOLS.BONUS
];

// Paylines for the 5x4 grid.
const DINO_PAYLINES = [
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19], [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    [0, 6, 12, 18], [4, 8, 12, 16], [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
];

// Game state object
let dinoGoldGame = {
    isSpinning: false,
    stampedeSpins: 0,
    goldFeverLevel: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadDinoGoldGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-stone-800/50 p-6 rounded-xl border border-yellow-700/30">
                    <h4 class="text-xl font-bold mb-4 text-amber-300">EXPLORER'S PACK</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-stone-300">WAGER (GOLD)</label>
                        <input type="number" id="dinoBet" value="25" min="5" max="500" step="5"
                               class="w-full bg-stone-900/50 border border-stone-600/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startSpin" class="w-full dino-button py-3 rounded-lg font-extrabold mb-4 text-stone-800">
                        START DIG
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-stone-300">Last Find:</span>
                            <span id="dinoLastWin" class="text-yellow-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-stone-300">Stampede Spins:</span>
                            <span id="stampedeSpins" class="text-green-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-stone-900/30 rounded-lg border border-stone-700/20">
                        <h5 class="text-sm font-bold mb-2 text-amber-300">GOLD FEVER</h5>
                        <div class="w-full bg-stone-700/50 rounded-full h-3 mb-2">
                            <div id="goldFeverMeter" class="bg-gradient-to-r from-amber-500 to-yellow-300 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="goldFeverStatus" class="text-xs text-center text-stone-400">Find gold to fill the meter! (0%)</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-stone-800/50 p-6 rounded-xl border border-yellow-700/30">
                    <div id="dinoReels" class="grid grid-cols-5 gap-2 mb-6">
                        </div>
                    
                    <div id="dinoStatus" class="text-center text-lg font-semibold text-amber-200 mb-4 h-8">
                        Unearth treasures from a lost world!
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-stone-900/30 p-3 rounded-lg">
                            <h6 class="font-bold text-amber-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs text-stone-300">
                                <div class="flex justify-between"><span>${DINO_SYMBOLS.TREX} x5:</span><span class="text-red-400">500x</span></div>
                                <div class="flex justify-between"><span>${DINO_SYMBOLS.BRONTO} x5:</span><span class="text-blue-400">250x</span></div>
                                <div class="flex justify-between"><span>${DINO_SYMBOLS.TRICERATOPS} x5:</span><span class="text-green-400">125x</span></div>
                                <div class="flex justify-between"><span>${DINO_SYMBOLS.FERN} x5:</span><span class="text-lime-400">75x</span></div>
                            </div>
                        </div>
                        <div class="bg-stone-900/30 p-3 rounded-lg">
                            <h6 class="font-bold text-amber-300 mb-2">DISCOVERIES</h6>
                             <div class="space-y-1 text-xs text-stone-300">
                                <div><span class="text-orange-400">${DINO_SYMBOLS.WILD} Wild Volcano:</span> Substitutes for any dino or plant.</div>
                                <div><span class="text-fuchsia-400">${DINO_SYMBOLS.SCATTER} Scatter Egg:</span> 3+ hatches Stampede Spins.</div>
                                <div><span class="text-yellow-400">${DINO_SYMBOLS.BONUS} Gold Coin:</span> Triggers Gold Fever for a bonus.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupDinoGoldGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupDinoGoldGame() {
    document.getElementById('startSpin').addEventListener('click', startDinoSpin);
    const reelsContainer = document.getElementById('dinoReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-stone-900/50 rounded-lg p-2 text-center text-4xl border border-stone-700/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = DINO_REEL_SYMBOLS[i % DINO_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateDinoDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateDinoDisplay() {
    const spinButton = document.getElementById('startSpin');
    spinButton.disabled = dinoGoldGame.isSpinning;
    spinButton.textContent = dinoGoldGame.isSpinning ? 'DIGGING...' : 'START DIG';

    document.getElementById('stampedeSpins').textContent = dinoGoldGame.stampedeSpins;
    document.getElementById('goldFeverMeter').style.width = `${dinoGoldGame.goldFeverLevel}%`;
    document.getElementById('goldFeverStatus').textContent = `Find gold to fill the meter! (${dinoGoldGame.goldFeverLevel}%)`;
    document.getElementById('dinoLastWin').textContent = `${dinoGoldGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function startDinoSpin() {
    if (dinoGoldGame.isSpinning) return;
    const totalBet = parseInt(document.getElementById('dinoBet').value);

    if (dinoGoldGame.stampedeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('dinoStatus').textContent = 'Not enough gold!';
            return;
        }
        balance -= totalBet;
    } else {
        dinoGoldGame.stampedeSpins--;
    }

    dinoGoldGame.isSpinning = true;
    dinoGoldGame.lastWin = 0;
    updateBalance();
    updateDinoDisplay();
    document.getElementById('dinoStatus').textContent = 'Searching for fossils...';

    const slots = document.querySelectorAll('#dinoReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    const spinAnimation = setInterval(() => {
        slots.forEach(slot => {
            slot.textContent = DINO_REEL_SYMBOLS[Math.floor(Math.random() * DINO_REEL_SYMBOLS.length)];
        });
    }, 100);

    setTimeout(() => {
        clearInterval(spinAnimation);
        finishDinoSpin(totalBet);
    }, 1500);
}

/**
 * Generates the final reel results and checks for wins.
 */
function finishDinoSpin(totalBet) {
    const finalSymbols = Array.from({ length: 20 }, () => DINO_REEL_SYMBOLS[Math.floor(Math.random() * DINO_REEL_SYMBOLS.length)]);
    document.querySelectorAll('#dinoReels .slot').forEach((slot, i) => slot.textContent = finalSymbols[i]);
    checkDinoWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and special symbols.
 */
function checkDinoWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for payline wins
    DINO_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === DINO_SYMBOLS.WILD) {
            lineSymbol = line.map(i => symbols[i]).find(s => s !== DINO_SYMBOLS.WILD && s !== DINO_SYMBOLS.SCATTER) || lineSymbol;
        }
        if (lineSymbol === DINO_SYMBOLS.SCATTER) return;

        let count = line.findIndex(i => symbols[i] !== lineSymbol && symbols[i] !== DINO_SYMBOLS.WILD);
        count = count === -1 ? line.length : count;

        if (count >= 3) {
            const multiplier = getDinoMultiplier(lineSymbol, count);
            totalWin += (totalBet / 20) * multiplier;
            line.slice(0, count).forEach(i => winningLines.add(i));
        }
    });

    // Check for scatter wins (Stampede Spins)
    const scatterCount = symbols.filter(s => s === DINO_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 10 + (scatterCount - 3) * 5;
        dinoGoldGame.stampedeSpins += freeSpinsWon;
        statusMessage = `🥚 A STAMPEDE! ${freeSpinsWon} free spins awarded!`;
    }
    
    // Collect Bonus symbols
    const bonusCount = symbols.filter(s => s === DINO_SYMBOLS.BONUS).length;
    dinoGoldGame.goldFeverLevel = Math.min(100, dinoGoldGame.goldFeverLevel + bonusCount * 10);

    // Check for Gold Fever bonus
    if (dinoGoldGame.goldFeverLevel >= 100 && totalWin > 0) {
        const multiplier = Math.floor(Math.random() * 9) + 2; // 2x to 10x
        totalWin *= multiplier;
        dinoGoldGame.goldFeverLevel = 0;
        statusMessage = `🪙 GOLD FEVER! Your find is worth ${multiplier}x more!`;
    }

    // Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        dinoGoldGame.lastWin = totalWin;
        if (!statusMessage) statusMessage = `Great find! You dug up ${totalWin} GA!`;
        winningLines.forEach(i => document.querySelectorAll('#dinoReels .slot')[i].classList.add('win-highlight'));
    } else if (!statusMessage) {
        statusMessage = 'Nothing found this time. Keep digging!';
    }
    
    document.getElementById('dinoStatus').textContent = statusMessage;
    dinoGoldGame.isSpinning = false;
    updateBalance();
    updateDinoDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 */
function getDinoMultiplier(symbol, count) {
    const multipliers = {
        [DINO_SYMBOLS.TREX]:       { 3: 100, 4: 250, 5: 500 },
        [DINO_SYMBOLS.BRONTO]:     { 3: 50,  4: 125, 5: 250 },
        [DINO_SYMBOLS.TRICERATOPS]:{ 3: 25,  4: 60,  5: 125 },
        [DINO_SYMBOLS.FERN]:       { 3: 15,  4: 40,  5: 75 },
        [DINO_SYMBOLS.FOSSIL]:     { 3: 10,  4: 20,  5: 40 },
        [DINO_SYMBOLS.ROCK]:       { 3: 5,   4: 10,  5: 20 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game
document.addEventListener('DOMContentLoaded', () => {
    updateBalance();
    loadDinoGoldGame();
});