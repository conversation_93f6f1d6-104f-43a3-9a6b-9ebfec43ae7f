// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Mythic Monsters Game Implementation ---

// Game constants
const MYTHIC_SYMBOLS = {
    WILD: '🎯',      // Ancient Summoner
    SCATTER: '⚡',    // Evolution Scatter
    DRAGON: '🐉',
    UNICORN: '🦄',
    WOLF: '🐺',
    EAGLE: '🦅',
    SNAKE: '🐍',
    LION: '🦁',
};

const MYTHIC_REEL_SYMBOLS = [
    MYTHIC_SYMBOLS.DRAGON, MYTHIC_SYMBOLS.UNICORN, MYTHIC_SYMBOLS.WOLF,
    MYTHIC_SYMBOLS.EAGLE, MYTHIC_SYMBOLS.SNAKE, MYTHIC_SYMBOLS.LION,
    MYTHIC_SYMBOLS.WILD, MYTHIC_SYMBOLS.SCATTER
];

// Define the 5x4 grid of paylines. Each array is a list of indices (0-19).
const MYTHIC_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    // Diagonals
    [0, 6, 12, 18], [4, 8, 12, 16],
    // Zig-zags
    [0, 5, 11, 17], [4, 9, 13, 19],
    [5, 1, 7, 13, 9], [10, 6, 12, 8, 14],
    [0, 1, 7, 8, 14], [15, 16, 12, 13, 19]
];

// Game state object
let mythicMonstersGame = {
    isSpinning: false,
    battleSpins: 0, // Free spins
    evolutionStage: 1,
    creaturePower: 0,
    lastWin: 0
};

/**
 * Loads the initial HTML for the game into the page.
 */
function loadMythicMonstersGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-violet-500/30">
                    <h4 class="text-xl font-bold mb-4 text-violet-400">MYTHIC MONSTERS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-violet-300">TOTAL BET</label>
                        <input type="number" id="mythicBet" value="40" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-violet-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="summonMythic" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        SUMMON CREATURES
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-violet-300">Last Win:</span>
                            <span id="mythicLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-violet-300">Battle Spins:</span>
                            <span id="mythicBattles" class="text-red-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-violet-900/20 rounded-lg border border-violet-500/20">
                        <h5 class="text-sm font-bold mb-2 text-violet-300">CREATURE POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="mythicPower" class="bg-gradient-to-r from-violet-600 to-purple-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="mythicPowerStatus" class="text-xs text-center text-gray-400">Fill to evolve for a multiplier!</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-violet-500/30">
                    <div id="mythicReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid will be generated here -->
                    </div>
                    
                    <div id="mythicStatus" class="text-center text-lg font-semibold text-violet-400 mb-4 h-8">
                        Ancient creatures await your digital summoning.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-violet-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-violet-300 mb-2">CREATURE VALUES (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${MYTHIC_SYMBOLS.DRAGON} x5:</span><span class="text-red-400">100x</span></div>
                                <div class="flex justify-between"><span>${MYTHIC_SYMBOLS.UNICORN} x5:</span><span class="text-purple-400">50x</span></div>
                                <div class="flex justify-between"><span>${MYTHIC_SYMBOLS.WOLF} x5:</span><span class="text-blue-400">25x</span></div>
                                <div class="flex justify-between"><span>${MYTHIC_SYMBOLS.EAGLE} x5:</span><span class="text-yellow-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-violet-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-violet-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">${MYTHIC_SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter.</div>
                                <div><span class="text-orange-400">${MYTHIC_SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Battle Spins.</div>
                                <div><span class="text-purple-400">Power:</span> Fill meter for a win multiplier.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupMythicMonstersGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupMythicMonstersGame() {
    document.getElementById('summonMythic').addEventListener('click', summonMythicCreatures);
    const reelsContainer = document.getElementById('mythicReels');
    reelsContainer.innerHTML = ''; // Clear previous content
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-violet-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = MYTHIC_REEL_SYMBOLS[i % MYTHIC_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateMythicDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateMythicDisplay() {
    const spinButton = document.getElementById('summonMythic');
    spinButton.disabled = mythicMonstersGame.isSpinning;
    spinButton.textContent = mythicMonstersGame.isSpinning ? 'SUMMONING...' : 'SUMMON CREATURES';

    document.getElementById('mythicBattles').textContent = mythicMonstersGame.battleSpins;
    document.getElementById('mythicPower').style.width = `${mythicMonstersGame.creaturePower}%`;
    document.getElementById('mythicLastWin').textContent = `${mythicMonstersGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function summonMythicCreatures() {
    if (mythicMonstersGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('mythicBet').value);

    if (mythicMonstersGame.battleSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('mythicStatus').textContent = 'Insufficient mana to summon!';
            return;
        }
        balance -= totalBet;
    } else {
        mythicMonstersGame.battleSpins--;
    }

    mythicMonstersGame.isSpinning = true;
    mythicMonstersGame.lastWin = 0;
    updateBalance();
    updateMythicDisplay();
    document.getElementById('mythicStatus').textContent = 'The realms are shifting...';

    const slots = document.querySelectorAll('#mythicReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = MYTHIC_REEL_SYMBOLS[Math.floor(Math.random() * MYTHIC_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishMythicSpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishMythicSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#mythicReels .slot');
    slots.forEach(slot => {
        const symbol = MYTHIC_REEL_SYMBOLS[Math.floor(Math.random() * MYTHIC_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkMythicWins(finalSymbols, totalBet);
}

/**
 * Calculates wins based on paylines and scatter symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkMythicWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // 1. Check for payline wins
    MYTHIC_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === MYTHIC_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== MYTHIC_SYMBOLS.WILD) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === MYTHIC_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getMythicMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 20; // Standardize line bet
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins
    const scatterCount = symbols.filter(s => s === MYTHIC_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 5 + (scatterCount - 3) * 3;
        mythicMonstersGame.battleSpins += freeSpinsWon;
        statusMessage = `⚡ EVOLUTION! Won ${freeSpinsWon} Battle Spins!`;
    }

    // 3. Apply Creature Power multiplier
    let powerMultiplier = 1;
    if (mythicMonstersGame.creaturePower >= 100) {
        powerMultiplier = Math.floor(Math.random() * 4) + 2; // 2x to 5x multiplier
        totalWin *= powerMultiplier;
        mythicMonstersGame.creaturePower = 0;
        statusMessage = `🐉 CREATURE EVOLVED! ${powerMultiplier}x WIN MULTIPLIER!`;
    } else {
        mythicMonstersGame.creaturePower = Math.min(100, mythicMonstersGame.creaturePower + Math.floor(Math.random() * 5) + 1);
    }

    // 4. Process results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        mythicMonstersGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage) {
            statusMessage = `Mythic victory! You won ${totalWin} GA!`;
        }

        const slots = document.querySelectorAll('#mythicReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The creatures slumber...';
    }
    
    document.getElementById('mythicStatus').textContent = statusMessage;
    mythicMonstersGame.isSpinning = false;
    updateMythicDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getMythicMultiplier(symbol, count) {
    const multipliers = {
        [MYTHIC_SYMBOLS.DRAGON]:  { 3: 20, 4: 50, 5: 100 },
        [MYTHIC_SYMBOLS.UNICORN]: { 3: 10, 4: 25, 5: 50 },
        [MYTHIC_SYMBOLS.WOLF]:    { 3: 8,  4: 20, 5: 25 },
        [MYTHIC_SYMBOLS.EAGLE]:   { 3: 5,  4: 10, 5: 15 },
        [MYTHIC_SYMBOLS.SNAKE]:   { 3: 3,  4: 8,  5: 12 },
        [MYTHIC_SYMBOLS.LION]:    { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadMythicMonstersGame();
});
