
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Gates of Olympus 2.0 Game Implementation
const OLYMPUS_SYMBOLS = {
    ZEUS: '⚡',
    POSEIDON: '🔱',
    HADES: '💀',
    ATHENA: '🦉',
    APOLLO: '☀️',
    ARTEMIS: '🌙',
    CROWN: '👑',
    CHALICE: '🏆',
    CRYSTAL: '💎',
    SCATTER: 'O'
};

const OLYMPUS_REEL_SYMBOLS = [
    OLYMPUS_SYMBOLS.ZEUS, OLYMPUS_SYMBOLS.POSEIDON, OLYMPUS_SYMBOLS.HADES,
    OLYMPUS_SYMBOLS.ATHENA, OLYMPUS_SYMBOLS.APOLLO, OLYMPUS_SYMBOLS.ARTEMIS,
    OLYMPUS_SYMBOLS.CROWN, OLYMPUS_SYMBOLS.CHALICE, OLYMPUS_SYMBOLS.CRYSTAL, OLYMPUS_SYMBOLS.SCATTER
];

let olympusGame = {
    isSpinning: false,
    freeSpins: 0,
    lastWin: 0,
    divineLevel: 1,
    godMode: false,
    multiplierActive: 1,
    thunderStrikes: 0,
    gatesOpen: false
};

function loadGatesOfOlympusGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400 font-mono">GATES OF OLYMPUS 2.0</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">DIVINE OFFERING</label>
                        <input type="number" id="olympusBet" value="60" min="20" max="1000" step="20"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startOlympusSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        INVOKE THE GODS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Divine Spins:</span>
                            <span id="olympusFreeSpins" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Blessing:</span>
                            <span id="olympusLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Divine Level:</span>
                            <span id="divineLevel" class="text-blue-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Multiplier:</span>
                            <span id="multiplierDisplay" class="text-purple-400">1x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Thunder Strikes:</span>
                            <span id="thunderStrikes" class="text-cyan-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-900/20 rounded-lg border border-yellow-500/20">
                        <h5 class="text-sm font-bold mb-2 text-yellow-300 font-mono">DIVINE PANTHEON</h5>
                        <div class="text-xs space-y-1">
                            <div>⚡ <span class="text-yellow-400">Zeus:</span> 150x bet</div>
                            <div>🔱 <span class="text-blue-400">Poseidon:</span> 100x bet</div>
                            <div>💀 <span class="text-purple-400">Hades:</span> 80x bet</div>
                            <div>🦉 <span class="text-gray-400">Athena:</span> 60x bet</div>
                            <div>☀️ <span class="text-orange-400">Apollo:</span> 50x bet</div>
                            <div>🌙 <span class="text-silver-400">Artemis:</span> 40x bet</div>
                            <div>O <span class="text-gold-400">Olympus:</span> Scatter bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <div class="relative mb-6">
                        <div id="olympusReels" class="grid grid-cols-6 gap-2 h-80">
                            ${Array(24).fill(0).map((_, i) => 
                                `<div class="slot bg-yellow-900/20 rounded-lg flex items-center justify-center text-4xl border border-yellow-500/20 transition-all duration-300">⚡</div>`
                            ).join('')}
                        </div>
                        <div id="lightningEffect" class="absolute inset-0 bg-gradient-radial from-yellow-400/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-500"></div>
                        <div id="gatesEffect" class="absolute inset-0 bg-gradient-to-t from-gold-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                    </div>
                    
                    <div id="olympusGameStatus" class="text-center text-lg font-semibold text-yellow-300 mb-4 h-8 font-mono">
                        The gods await your offering...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DIVINE STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>God Mode:</span>
                                    <span id="godModeStatus" class="text-gold-400">DORMANT</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Gates:</span>
                                    <span id="gatesStatus" class="text-blue-400">CLOSED</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Divine Favor:</span>
                                    <span class="text-green-400">BLESSED</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">POWER METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                                <div id="powerMeter" class="bg-gradient-to-r from-yellow-500 to-gold-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                            <p class="text-xs text-center text-gray-400">Divine Power</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupGatesOfOlympusGame();
}

function setupGatesOfOlympusGame() {
    document.getElementById('startOlympusSpin').addEventListener('click', startOlympusSpin);
    const reelsContainer = document.getElementById('olympusReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 24; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-yellow-900/20 rounded-lg p-2 text-center text-3xl border border-yellow-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = OLYMPUS_REEL_SYMBOLS[i % OLYMPUS_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateOlympusDisplay();
}

function startOlympusSpin() {
    if (olympusGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('olympusBet').value);

    if (olympusGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('olympusGameStatus').textContent = 'INSUFFICIENT DIVINE OFFERING';
            return;
        }
        balance -= totalBet;
    } else {
        olympusGame.freeSpins--;
    }

    olympusGame.isSpinning = true;
    olympusGame.lastWin = 0;
    updateBalance();
    updateOlympusDisplay();
    document.getElementById('olympusGameStatus').textContent = 'The gods are stirring...';

    const slots = document.querySelectorAll('#olympusReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Lightning effect during spin
    document.getElementById('lightningEffect').style.opacity = '0.5';

    let spinDuration = 2500;
    let spinInterval = 80;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = OLYMPUS_REEL_SYMBOLS[Math.floor(Math.random() * OLYMPUS_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('lightningEffect').style.opacity = '0';
            finishOlympusSpin(totalBet);
        }
    }, spinInterval);
}

function finishOlympusSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#olympusReels .slot');
    slots.forEach(slot => {
        const symbol = OLYMPUS_REEL_SYMBOLS[Math.floor(Math.random() * OLYMPUS_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkOlympusWins(finalSymbols, totalBet);
}

function checkOlympusWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for divine wins
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === OLYMPUS_SYMBOLS.ZEUS && count >= 4) {
            multiplier = count >= 8 ? 150 : count >= 6 ? 100 : 50;
            statusMessage = 'ZEUS UNLEASHES HIS THUNDER!';
            olympusGame.godMode = true;
            olympusGame.thunderStrikes += count;
            olympusGame.multiplierActive = 3;
        } else if (symbol === OLYMPUS_SYMBOLS.POSEIDON && count >= 4) {
            multiplier = count >= 8 ? 100 : count >= 6 ? 70 : 35;
            statusMessage = 'Poseidon\'s trident commands the seas!';
            olympusGame.multiplierActive = Math.max(olympusGame.multiplierActive, 2);
        } else if (symbol === OLYMPUS_SYMBOLS.HADES && count >= 4) {
            multiplier = count >= 8 ? 80 : count >= 6 ? 55 : 28;
            statusMessage = 'Hades opens the gates of the underworld!';
        } else if (symbol === OLYMPUS_SYMBOLS.ATHENA && count >= 4) {
            multiplier = count >= 8 ? 60 : count >= 6 ? 40 : 20;
            statusMessage = 'Athena\'s wisdom guides your fortune!';
        } else if (symbol === OLYMPUS_SYMBOLS.APOLLO && count >= 4) {
            multiplier = count >= 8 ? 50 : count >= 6 ? 35 : 18;
            statusMessage = 'Apollo\'s light illuminates great wealth!';
        } else if (symbol === OLYMPUS_SYMBOLS.ARTEMIS && count >= 4) {
            multiplier = count >= 8 ? 40 : count >= 6 ? 28 : 15;
            statusMessage = 'Artemis hunts down divine treasures!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier * olympusGame.multiplierActive;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Scatter bonus (O Olympus symbols)
    const scatterCount = symbols.filter(s => s === OLYMPUS_SYMBOLS.SCATTER).length;
    if (scatterCount >= 4) {
        const freeSpinsAwarded = 15 + (scatterCount - 4) * 10;
        olympusGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` THE GATES OF OLYMPUS OPEN! ${freeSpinsAwarded} divine spins!`;
        olympusGame.divineLevel++;
        olympusGame.gatesOpen = true;
        
        // Trigger gates effect
        document.getElementById('gatesEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('gatesEffect').style.opacity = '0';
        }, 3000);
    }

    // God Mode bonus
    if (olympusGame.godMode && totalWin > 0) {
        totalWin *= 5;
        statusMessage += ' GOD MODE ACTIVATED - DIVINE MULTIPLIER!';
        setTimeout(() => { 
            olympusGame.godMode = false;
            olympusGame.multiplierActive = 1;
        }, 5000);
    }

    // Thunder strikes bonus
    if (olympusGame.thunderStrikes >= 10) {
        totalWin += totalBet * 100;
        statusMessage += ' LIGHTNING STORM BONUS!';
        olympusGame.thunderStrikes = 0;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        olympusGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#olympusReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The gods withhold their blessings.';
        olympusGame.multiplierActive = 1;
    }

    document.getElementById('olympusGameStatus').textContent = statusMessage;
    olympusGame.isSpinning = false;
    updateOlympusDisplay();
}

function updateOlympusDisplay() {
    const spinButton = document.getElementById('startOlympusSpin');
    spinButton.disabled = olympusGame.isSpinning;
    spinButton.textContent = olympusGame.isSpinning ? 'INVOKING...' : 'INVOKE THE GODS';

    document.getElementById('olympusFreeSpins').textContent = olympusGame.freeSpins;
    document.getElementById('olympusLastWin').textContent = `${olympusGame.lastWin} GA`;
    document.getElementById('divineLevel').textContent = olympusGame.divineLevel;
    document.getElementById('multiplierDisplay').textContent = `${olympusGame.multiplierActive}x`;
    document.getElementById('thunderStrikes').textContent = olympusGame.thunderStrikes;
    document.getElementById('godModeStatus').textContent = olympusGame.godMode ? 'ACTIVE!' : 'DORMANT';
    document.getElementById('gatesStatus').textContent = olympusGame.gatesOpen ? 'OPEN!' : 'CLOSED';
    
    // Update power meter based on thunder strikes
    const powerLevel = (olympusGame.thunderStrikes / 10) * 100;
    document.getElementById('powerMeter').style.width = `${Math.min(powerLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadGatesOfOlympusGame();
});
