
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Steampunk Spinners Deluxe Game Implementation
const DELUXE_STEAMPUNK_SYMBOLS = {
    MASTER_ENGINEER: '👨‍🔬',
    STEAM_TITAN: '🤖',
    CLOCKWORK_DRAGON: '🐲',
    GOLDEN_GEARS: '⚙️',
    PRESSURE_VALVE: '🔧',
    STEAM_ENGINE: '🚂',
    COPPER_PIPES: '🔩',
    COAL_FURNACE: '🔥',
    WILD: '🎯',
    SCATTER: '💨'
};

const DELUXE_STEAMPUNK_REEL_SYMBOLS = [
    DELUXE_STEAMPUNK_SYMBOLS.MASTER_ENGINEER, DELUXE_STEAMPUNK_SYMBOLS.STEAM_TITAN, DELUXE_STEAMPUNK_SYMBOLS.CLOCKWORK_DRAGON,
    DELUXE_STEAMPUNK_SYMBOLS.GOLDEN_GEARS, DELUXE_STEAMPUNK_SYMBOLS.PRESSURE_VALVE, DELUXE_STEAMPUNK_SYMBOLS.STEAM_ENGINE,
    DELUXE_STEAMPUNK_SYMBOLS.COPPER_PIPES, DELUXE_STEAMPUNK_SYMBOLS.COAL_FURNACE, DELUXE_STEAMPUNK_SYMBOLS.WILD, DELUXE_STEAMPUNK_SYMBOLS.SCATTER
];

let deluxeSteampunkGame = {
    isSpinning: false,
    gearSpins: 0,
    lastWin: 0,
    engineLevel: 1,
    steamPressure: 0,
    industrialMultiplier: 1,
    factoryMode: false,
    steamTitanActive: false,
    clockworkDragonAwake: false,
    masterEngineerPresent: false,
    goldenGearsCollected: 0,
    steamOverdrive: false,
    mechanicalPrecision: 0
};

function loadDeluxeSteampunkGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <h4 class="text-xl font-bold mb-4 text-amber-400 font-mono">STEAMPUNK SPINNERS DELUXE</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-amber-300">COAL INVESTMENT</label>
                        <input type="number" id="deluxeSteampunkBet" value="35" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-amber-300">ENGINE TYPE</label>
                        <select id="deluxeEngineType" class="w-full bg-black/50 border border-amber-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="basic">Basic Steam (7x)</option>
                            <option value="advanced">Advanced Gear (14x)</option>
                            <option value="industrial">Industrial Titan (28x)</option>
                        </select>
                    </div>
                    
                    <button id="engageDeluxeEngines" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        ENGAGE DELUXE ENGINES
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Gear Spins:</span>
                            <span id="deluxeGearSpins" class="text-amber-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Production:</span>
                            <span id="deluxeLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Engine Level:</span>
                            <span id="deluxeEngineLevel" class="text-orange-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Steam Pressure:</span>
                            <span id="deluxeSteamPressure" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Industrial Multi:</span>
                            <span id="deluxeIndustrialMultiplier" class="text-yellow-400">1x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Golden Gears:</span>
                            <span id="goldenGearsCount" class="text-yellow-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-amber-900/20 rounded-lg border border-amber-500/20">
                        <h5 class="text-sm font-bold mb-2 text-amber-300 font-mono">INDUSTRIAL MACHINERY</h5>
                        <div class="text-xs space-y-1">
                            <div>👨‍🔬 <span class="text-blue-400">Master Engineer:</span> 999x bet</div>
                            <div>🤖 <span class="text-gray-400">Steam Titan:</span> 777x bet</div>
                            <div>🐲 <span class="text-green-400">Clockwork Dragon:</span> 666x bet</div>
                            <div>⚙️ <span class="text-yellow-400">Golden Gears:</span> 555x bet</div>
                            <div>🔧 <span class="text-orange-400">Pressure Valve:</span> 444x bet</div>
                            <div>🎯 <span class="text-cyan-400">Wild:</span> Master substitutes</div>
                            <div>💨 <span class="text-gray-400">Scatter:</span> Steam pressure</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-amber-500/30">
                    <div class="relative mb-6">
                        <div id="deluxeSteampunkReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-amber-900/20 rounded-lg flex items-center justify-center text-3xl border border-amber-500/20 transition-all duration-300">👨‍🔬</div>`
                            ).join('')}
                        </div>
                        <div id="factoryModeEffect" class="absolute inset-0 bg-gradient-to-t from-orange-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="steamTitanEffect" class="absolute inset-0 bg-gradient-to-b from-gray-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="clockworkDragonEffect" class="absolute inset-0 bg-gradient-radial from-green-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="steamOverdriveEffect" class="absolute inset-0 bg-gradient-conic from-red-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="masterEngineerEffect" class="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-white/10 to-blue-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                    </div>
                    
                    <div id="deluxeSteampunkStatus" class="text-center text-lg font-semibold text-amber-300 mb-4 h-8 font-mono">
                        Deluxe industrial machinery ready for maximum production...
                    </div>
                    
                    <div class="grid grid-cols-5 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-amber-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PRESSURE GAUGE</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="pressureMeter" class="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="pressureLevel" class="text-red-400">0%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-amber-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">FACTORY MODE</h6>
                            <div class="text-center">
                                <span id="factoryStatus" class="text-orange-400">OFFLINE</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-amber-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">STEAM TITAN</h6>
                            <div class="text-center">
                                <span id="titanStatus" class="text-gray-400">DORMANT</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-amber-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CLOCKWORK</h6>
                            <div class="text-center">
                                <span id="clockworkStatus" class="text-green-400">SLEEPING</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-amber-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">ENGINEER</h6>
                            <div class="text-center">
                                <span id="engineerStatus" class="text-blue-400">ABSENT</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupDeluxeSteampunkGame();
}

function setupDeluxeSteampunkGame() {
    document.getElementById('engageDeluxeEngines').addEventListener('click', engageDeluxeEngines);
    const reelsContainer = document.getElementById('deluxeSteampunkReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-amber-900/20 rounded-lg p-2 text-center text-3xl border border-amber-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = DELUXE_STEAMPUNK_REEL_SYMBOLS[i % DELUXE_STEAMPUNK_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateDeluxeSteampunkDisplay();
}

function engageDeluxeEngines() {
    if (deluxeSteampunkGame.isSpinning) return;

    const bet = parseInt(document.getElementById('deluxeSteampunkBet').value);
    const engineType = document.getElementById('deluxeEngineType').value;
    let totalBet;
    
    switch(engineType) {
        case 'advanced': totalBet = bet * 14; break;
        case 'industrial': totalBet = bet * 28; break;
        default: totalBet = bet * 7;
    }

    if (deluxeSteampunkGame.gearSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('deluxeSteampunkStatus').textContent = 'INSUFFICIENT COAL FOR DELUXE ENGINES';
            return;
        }
        balance -= totalBet;
    } else {
        deluxeSteampunkGame.gearSpins--;
    }

    deluxeSteampunkGame.isSpinning = true;
    deluxeSteampunkGame.lastWin = 0;
    updateBalance();
    updateDeluxeSteampunkDisplay();
    document.getElementById('deluxeSteampunkStatus').textContent = 'Deluxe industrial machinery operating at maximum efficiency...';

    const slots = document.querySelectorAll('#deluxeSteampunkReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'industrial-highlight'));

    // Factory mode effect during spin
    document.getElementById('factoryModeEffect').style.opacity = '0.5';

    let spinDuration = 2500;
    let spinInterval = 110;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = DELUXE_STEAMPUNK_REEL_SYMBOLS[Math.floor(Math.random() * DELUXE_STEAMPUNK_REEL_SYMBOLS.length)];
            slot.style.background = `linear-gradient(45deg, #${Math.floor(Math.random()*16777215).toString(16)}, #6b7280)`;
            slot.style.transform = `scale(${0.95 + Math.random() * 0.1}) rotateZ(${Math.random() * 6 - 3}deg)`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('factoryModeEffect').style.opacity = '0';
            slots.forEach(slot => {
                slot.style.background = '';
                slot.style.transform = 'scale(1) rotateZ(0deg)';
            });
            finishDeluxeProduction(totalBet);
        }
    }, spinInterval);
}

function finishDeluxeProduction(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#deluxeSteampunkReels .slot');
    
    // Enhanced symbol generation with engine type bonuses
    const engineType = document.getElementById('deluxeEngineType').value;
    let wildChance = 0.14;
    let scatterChance = 0.09;
    let masterEngineerChance = 0.16;
    
    if (engineType === 'advanced') {
        wildChance = 0.19;
        scatterChance = 0.14;
        masterEngineerChance = 0.22;
    } else if (engineType === 'industrial') {
        wildChance = 0.28;
        scatterChance = 0.20;
        masterEngineerChance = 0.32;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = DELUXE_STEAMPUNK_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = DELUXE_STEAMPUNK_SYMBOLS.SCATTER;
        } else if (Math.random() < masterEngineerChance) {
            symbol = DELUXE_STEAMPUNK_SYMBOLS.MASTER_ENGINEER;
        } else {
            symbol = DELUXE_STEAMPUNK_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkDeluxeIndustrialWins(finalSymbols, totalBet);
}

function checkDeluxeIndustrialWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (industrial mastery)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== DELUXE_STEAMPUNK_SYMBOLS.WILD && symbol !== DELUXE_STEAMPUNK_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === DELUXE_STEAMPUNK_SYMBOLS.MASTER_ENGINEER) {
                multiplier = 999;
                statusMessage = 'MASTER ENGINEER PERFECTION ACHIEVED!';
                deluxeSteampunkGame.steamPressure += 400;
                deluxeSteampunkGame.masterEngineerPresent = true;
            } else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.STEAM_TITAN) {
                multiplier = 777;
                statusMessage = 'STEAM TITAN AWAKENS!';
                deluxeSteampunkGame.steamPressure += 350;
                deluxeSteampunkGame.steamTitanActive = true;
            } else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.CLOCKWORK_DRAGON) {
                multiplier = 666;
                statusMessage = 'CLOCKWORK DRAGON RISES!';
                deluxeSteampunkGame.steamPressure += 300;
                deluxeSteampunkGame.clockworkDragonAwake = true;
            } else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.GOLDEN_GEARS) {
                multiplier = 555;
                statusMessage = 'GOLDEN GEARS PERFECTION!';
                deluxeSteampunkGame.goldenGearsCollected += 10;
                deluxeSteampunkGame.steamPressure += 250;
            } else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.PRESSURE_VALVE) {
                multiplier = 444;
                statusMessage = 'PRESSURE VALVE MASTERY!';
                deluxeSteampunkGame.steamPressure += 200;
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * deluxeSteampunkGame.industrialMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind and 3-of-a-kind with similar logic...
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== DELUXE_STEAMPUNK_SYMBOLS.WILD && symbol !== DELUXE_STEAMPUNK_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === DELUXE_STEAMPUNK_SYMBOLS.MASTER_ENGINEER) multiplier = 350;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.STEAM_TITAN) multiplier = 280;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.CLOCKWORK_DRAGON) multiplier = 220;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.GOLDEN_GEARS) multiplier = 180;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.PRESSURE_VALVE) multiplier = 150;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.STEAM_ENGINE) multiplier = 120;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.COPPER_PIPES) multiplier = 100;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.COAL_FURNACE) multiplier = 80;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * deluxeSteampunkGame.industrialMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== DELUXE_STEAMPUNK_SYMBOLS.WILD && symbol !== DELUXE_STEAMPUNK_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === DELUXE_STEAMPUNK_SYMBOLS.MASTER_ENGINEER) multiplier = 140;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.STEAM_TITAN) multiplier = 110;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.CLOCKWORK_DRAGON) multiplier = 90;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.GOLDEN_GEARS) multiplier = 75;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.PRESSURE_VALVE) multiplier = 60;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.STEAM_ENGINE) multiplier = 50;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.COPPER_PIPES) multiplier = 40;
            else if (symbol === DELUXE_STEAMPUNK_SYMBOLS.COAL_FURNACE) multiplier = 35;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * deluxeSteampunkGame.industrialMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === DELUXE_STEAMPUNK_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.9);
        totalWin *= wildMultiplier;
        statusMessage += ` Master engineering: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter steam pressure bonus
    const scatterCount = symbols.filter(s => s === DELUXE_STEAMPUNK_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const gearSpinsAwarded = 22 + (scatterCount - 3) * 11;
        deluxeSteampunkGame.gearSpins += gearSpinsAwarded;
        deluxeSteampunkGame.engineLevel = Math.min(deluxeSteampunkGame.engineLevel + 1, 10);
        statusMessage += ` 💨 STEAM PRESSURE OVERLOAD! ${gearSpinsAwarded} gear spins!`;
        
        // Increase industrial multiplier
        deluxeSteampunkGame.industrialMultiplier += 1.5;
    }

    // Master engineer activation
    if (deluxeSteampunkGame.masterEngineerPresent && deluxeSteampunkGame.steamPressure >= 600) {
        const engineerMultiplier = 25 + deluxeSteampunkGame.engineLevel;
        totalWin *= engineerMultiplier;
        statusMessage = `👨‍🔬 MASTER ENGINEER'S ULTIMATE DESIGN! ${engineerMultiplier}x PRECISION!`;
        deluxeSteampunkGame.steamPressure = 0;
        deluxeSteampunkGame.mechanicalPrecision += 5;
        
        // Trigger master engineer effect
        document.getElementById('masterEngineerEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('masterEngineerEffect').style.opacity = '0';
            deluxeSteampunkGame.masterEngineerPresent = false;
        }, 6000);
    }

    // Steam titan activation
    if (deluxeSteampunkGame.steamTitanActive && deluxeSteampunkGame.steamPressure >= 500) {
        const titanMultiplier = 20 + deluxeSteampunkGame.engineLevel;
        totalWin *= titanMultiplier;
        statusMessage = `🤖 STEAM TITAN'S INDUSTRIAL MIGHT! ${titanMultiplier}x MECHANICAL POWER!`;
        deluxeSteampunkGame.steamPressure += 150; // Titan generates more steam
        
        // Trigger steam titan effect
        document.getElementById('steamTitanEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('steamTitanEffect').style.opacity = '0';
            deluxeSteampunkGame.steamTitanActive = false;
        }, 5000);
    }

    // Clockwork dragon activation
    if (deluxeSteampunkGame.clockworkDragonAwake && deluxeSteampunkGame.steamPressure >= 400) {
        const dragonMultiplier = 15 + deluxeSteampunkGame.engineLevel;
        totalWin *= dragonMultiplier;
        statusMessage = `🐲 CLOCKWORK DRAGON'S ANCIENT WISDOM! ${dragonMultiplier}x MYSTICAL ENGINEERING!`;
        deluxeSteampunkGame.steamPressure = Math.min(1000, deluxeSteampunkGame.steamPressure + 200);
        
        // Trigger clockwork dragon effect
        document.getElementById('clockworkDragonEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('clockworkDragonEffect').style.opacity = '0';
            deluxeSteampunkGame.clockworkDragonAwake = false;
        }, 4000);
    }

    // Factory mode (mega production bonus)
    if (deluxeSteampunkGame.steamPressure >= 350 && Math.random() < 0.45) {
        deluxeSteampunkGame.factoryMode = true;
        const factoryMultiplier = 12 + deluxeSteampunkGame.engineLevel;
        totalWin *= factoryMultiplier;
        statusMessage = `🏭 FACTORY MODE ACTIVATED! ${factoryMultiplier}x MASS PRODUCTION!`;
        
        // Trigger factory mode effect
        document.getElementById('factoryModeEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('factoryModeEffect').style.opacity = '0';
            deluxeSteampunkGame.factoryMode = false;
        }, 7000);
    }

    // Steam overdrive (ultra rare bonus)
    if (deluxeSteampunkGame.gearSpins > 0 && Math.random() < 0.3) {
        deluxeSteampunkGame.steamOverdrive = true;
        totalWin += totalBet * 300;
        statusMessage += ' ⚡ STEAM OVERDRIVE ENGAGED!';
        
        // Trigger steam overdrive effect
        document.getElementById('steamOverdriveEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('steamOverdriveEffect').style.opacity = '0';
            deluxeSteampunkGame.steamOverdrive = false;
        }, 8000);
    }

    // Golden gears mastery
    if (deluxeSteampunkGame.goldenGearsCollected >= 25) {
        totalWin += totalBet * 750;
        statusMessage += ' ⚙️ GOLDEN GEARS MASTERY! INFINITE PRECISION!';
        deluxeSteampunkGame.goldenGearsCollected = 0; // Reset but keep mastery bonus
        deluxeSteampunkGame.mechanicalPrecision += 10;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        deluxeSteampunkGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#deluxeSteampunkReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('industrial-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Deluxe machinery requires fine-tuning for optimal production...';
    }

    document.getElementById('deluxeSteampunkStatus').textContent = statusMessage;
    deluxeSteampunkGame.isSpinning = false;
    updateDeluxeSteampunkDisplay();
}

function updateDeluxeSteampunkDisplay() {
    const spinButton = document.getElementById('engageDeluxeEngines');
    spinButton.disabled = deluxeSteampunkGame.isSpinning;
    spinButton.textContent = deluxeSteampunkGame.isSpinning ? 'PRODUCING...' : 'ENGAGE DELUXE ENGINES';

    document.getElementById('deluxeGearSpins').textContent = deluxeSteampunkGame.gearSpins;
    document.getElementById('deluxeLastWin').textContent = `${deluxeSteampunkGame.lastWin} GA`;
    document.getElementById('deluxeEngineLevel').textContent = deluxeSteampunkGame.engineLevel;
    document.getElementById('deluxeSteamPressure').textContent = deluxeSteampunkGame.steamPressure;
    document.getElementById('deluxeIndustrialMultiplier').textContent = `${deluxeSteampunkGame.industrialMultiplier.toFixed(1)}x`;
    document.getElementById('goldenGearsCount').textContent = deluxeSteampunkGame.goldenGearsCollected;
    
    // Update pressure meter
    const pressurePercentage = Math.min(100, (deluxeSteampunkGame.steamPressure / 600) * 100);
    document.getElementById('pressureMeter').style.width = `${pressurePercentage}%`;
    document.getElementById('pressureLevel').textContent = `${Math.round(pressurePercentage)}%`;
    
    document.getElementById('factoryStatus').textContent = deluxeSteampunkGame.factoryMode ? 'ACTIVE!' : 'OFFLINE';
    document.getElementById('titanStatus').textContent = deluxeSteampunkGame.steamTitanActive ? 'ACTIVE!' : 'DORMANT';
    document.getElementById('clockworkStatus').textContent = deluxeSteampunkGame.clockworkDragonAwake ? 'AWAKE!' : 'SLEEPING';
    document.getElementById('engineerStatus').textContent = deluxeSteampunkGame.masterEngineerPresent ? 'PRESENT!' : 'ABSENT';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDeluxeSteampunkGame();
});

