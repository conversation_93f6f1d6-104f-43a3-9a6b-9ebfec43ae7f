// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Wizard Wealth Game Implementation
function loadWizardWealthGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">WIZARD WEALTH</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">BET AMOUNT</label>
                        <input type="number" id="wizardBet" value="30" min="1" max="1000" 
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">SPELL LEVEL</label>
                        <select id="wizardLevel" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="apprentice">Apprentice (25 lines)</option>
                            <option value="adept">Adept (50 lines)</option>
                            <option value="master">Master (100 lines)</option>
                        </select>
                    </div>
                    
                    <button id="castSpell" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        CAST SPELL
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-purple-300">Spell Power:</span>
                            <span id="wizardPower" class="text-white">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-purple-300">Last Win:</span>
                            <span id="wizardLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-purple-300">Mystic Spins:</span>
                            <span id="wizardSpins" class="text-blue-400">0</span>
                        </div>
                    </div>

                     <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300">MANA CRYSTAL</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="wizardMana" class="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Gather mana for a powerful spell bonus</p>
                    </div>
                </div>
            </div>
            
            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="wizardReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x3 reel grid -->
                    </div>
                    
                    <div id="wizardStatus" class="text-center text-lg font-semibold text-purple-400 mb-4">
                        The grimoire is open. Cast a spell to reveal your fortune.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">ARCANE SYMBOLS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>🧙 x5:</span><span class="text-purple-400">2500x</span></div>
                                <div class="flex justify-between"><span>🔮 x5:</span><span class="text-blue-400">1000x</span></div>
                                <div class="flex justify-between"><span>📜 x5:</span><span class="text-green-400">500x</span></div>
                                <div class="flex justify-between"><span>🧪 x5:</span><span class="text-yellow-400">250x</span></div>
                            </div>
                        </div>
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">ENCHANTMENTS</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Grand Wizard substitutes</div>
                                <div>✨ <span class="text-blue-400">Scatter:</span> 3+ grants Mystic Spins</div>
                                <div>📖 <span class="text-purple-400">Bonus:</span> Fill the Mana Crystal</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupWizardWealthGame();
}

let wizardWealthGame = {
    isSpinning: false,
    mysticSpins: 0,
    spellPower: 0,
    mana: 0,
    reels: [],
    symbols: ['🧙', '🔮', '📜', '🧪', '📖', '🦉', '🔑', '✨', '🎯'],
};

function setupWizardWealthGame() {
    const reelsContainer = document.getElementById('wizardReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 15; i++) { // 5x3 grid
        const symbol = document.createElement('div');
        symbol.className = 'bg-black/50 border border-purple-500/30 rounded-lg h-24 flex items-center justify-center text-4xl transition-all duration-300';
        symbol.textContent = wizardWealthGame.symbols[Math.floor(Math.random() * 7)];
        reelsContainer.appendChild(symbol);
    }
    wizardWealthGame.reels = Array.from(reelsContainer.children);
    
    document.getElementById('castSpell').addEventListener('click', castWizardSpell);
    updateWizardDisplay();
}

function updateWizardDisplay() {
    document.getElementById('wizardPower').textContent = wizardWealthGame.spellPower;
    document.getElementById('wizardSpins').textContent = wizardWealthGame.mysticSpins;
    document.getElementById('wizardMana').style.width = wizardWealthGame.mana + '%';
}

function castWizardSpell() {
    if (wizardWealthGame.isSpinning) return;

    const bet = parseInt(document.getElementById('wizardBet').value);
    const level = document.getElementById('wizardLevel').value;
    let totalBet;

    switch(level) {
        case 'adept': totalBet = bet * 50; break;
        case 'master': totalBet = bet * 100; break;
        default: totalBet = bet * 25;
    }

    if (wizardWealthGame.mysticSpins === 0 && balance < totalBet) {
        document.getElementById('wizardStatus').textContent = 'Not enough mana (balance) to cast!';
        return;
    }

    if (wizardWealthGame.mysticSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        wizardWealthGame.mysticSpins--;
    }

    wizardWealthGame.isSpinning = true;
    document.getElementById('castSpell').disabled = true;
    document.getElementById('wizardStatus').textContent = 'Casting the spell...';

    let animationSteps = 30;
    let currentStep = 0;

    const animateReels = () => {
        if (currentStep < animationSteps) {
            wizardWealthGame.reels.forEach(reel => {
                reel.textContent = wizardWealthGame.symbols[Math.floor(Math.random() * 7)];
                reel.style.transform = `rotateY(${currentStep * 24}deg) scale(1.1)`;
                reel.style.background = 'linear-gradient(45deg, #6d28d9, #4f46e5)';
                reel.style.boxShadow = '0 0 25px #8b5cf6';
            });
            currentStep++;
            setTimeout(animateReels, 100);
        } else {
            const finalSymbols = [];
            for (let i = 0; i < 15; i++) {
                let symbol;
                const rand = Math.random();
                if (rand < 0.08) symbol = '🎯'; // Wild
                else if (rand < 0.15) symbol = '✨'; // Scatter
                else if (rand < 0.22) symbol = '📖'; // Bonus
                else symbol = wizardWealthGame.symbols[Math.floor(Math.random() * 4)]; // High value symbols
                finalSymbols.push(symbol);
            }

            wizardWealthGame.reels.forEach((reel, index) => {
                reel.textContent = finalSymbols[index];
                reel.style.transform = 'rotateY(0deg) scale(1)';
                reel.style.background = '';
                reel.style.boxShadow = '';
            });

            checkWizardWins(finalSymbols, totalBet);
        }
    };

    animateReels();
}

function checkWizardWins(symbols, totalBet) {
    let totalWin = 0;
    let winMessage = '';

    // Check for scatters (✨)
    const scatterCount = symbols.filter(s => s === '✨').length;
    if (scatterCount >= 3) {
        wizardWealthGame.mysticSpins += scatterCount * 5;
        const scatterWin = Math.floor(totalBet * scatterCount * 2);
        totalWin += scatterWin;
        winMessage = `<span class="text-blue-400">✨ Mystic energy grants ${scatterCount * 5} free spins!</span>`;
    }

    // Check for bonus symbols (📖)
    const bonusCount = symbols.filter(s => s === '📖').length;
    if (bonusCount > 0) {
        wizardWealthGame.mana = Math.min(100, wizardWealthGame.mana + bonusCount * 10);
        if (wizardWealthGame.mana >= 100) {
            const manaBonus = totalBet * 10;
            totalWin += manaBonus;
            wizardWealthGame.mana = 0;
            winMessage = `<span class="text-purple-400">📖 Mana Crystal is full! A powerful ${manaBonus} GA bonus is yours!</span>`;
        }
    }

    // Calculate line wins
    totalWin += calculateWizardWins(symbols, totalBet);
    wizardWealthGame.spellPower += Math.floor(totalWin / 100);

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        document.getElementById('wizardLastWin').textContent = `${totalWin} GA`;
        document.getElementById('wizardStatus').innerHTML = winMessage || `<span class="text-green-400">Spell successful! You won ${totalWin} GA!</span>`;
    } else {
        document.getElementById('wizardStatus').textContent = 'The spell fizzled. Try again!';
    }

    updateWizardDisplay();

    setTimeout(() => {
        wizardWealthGame.isSpinning = false;
        document.getElementById('castSpell').disabled = false;
    }, 2000);
}

function calculateWizardWins(symbols, totalBet) {
    let totalWin = 0;
    const lines = [
        [0,1,2,3,4], [5,6,7,8,9], [10,11,12,13,14], // Horizontal
        [0,5,10,6,1], [4,9,14,8,3], // W-shapes
        [0,6,12,8,2], [4,8,12,6,10] // V-shapes
    ];

    lines.forEach(line => {
        const lineSymbols = line.map(pos => symbols[pos]);
        let firstSymbol = lineSymbols.find(s => s !== '🎯');
        if (!firstSymbol) firstSymbol = '🎯'; 

        let matchCount = 0;
        for (const symbol of lineSymbols) {
            if (symbol === firstSymbol || symbol === '🎯') {
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3) {
            const multiplier = getWizardMultiplier(firstSymbol, matchCount);
            totalWin += Math.floor((totalBet / 25) * multiplier);
        }
    });

    return totalWin;
}

function getWizardMultiplier(symbol, count) {
    const multipliers = {
        '🧙': [0, 0, 100, 500, 2500],
        '🔮': [0, 0, 50, 200, 1000],
        '📜': [0, 0, 25, 100, 500],
        '🧪': [0, 0, 15, 75, 250],
        '🦉': [0, 0, 10, 50, 200],
        '🔑': [0, 0, 5, 25, 100],
    };
    return multipliers[symbol] ? multipliers[symbol][count - 1] : 0;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadWizardWealthGame();
});
