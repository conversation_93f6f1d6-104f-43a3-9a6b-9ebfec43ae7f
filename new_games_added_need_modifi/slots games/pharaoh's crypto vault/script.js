// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// <PERSON><PERSON><PERSON>'s Crypto Vault Game Implementation
const PHARAOH_SYMBOLS = {
    PHARAOH: '👑',
    ANUBIS: '🐺',
    PYRAMID: '🔺',
    SCARAB: '🪲',
    ANKH: '☥',
    EYE_OF_HORUS: '👁️',
    SPHINX: '🦁',
    CRYPTO_COIN: '🪙',
    WILD: '⚡',
    SCATTER: '💎'
};

const PHARAOH_REEL_SYMBOLS = [
    PHARAOH_SYMBOLS.PHARAOH, PHARAOH_SYMBOLS.ANUBIS, PHARAOH_SYMBOLS.PYRAMID,
    PHARAOH_SYMBOLS.SCARAB, PHARAOH_SYMBOLS.ANKH, PHARAOH_SYMBOLS.EYE_OF_HORUS,
    PHARAOH_SYMBOLS.SPHINX, PHARAOH_SYMBOLS.CRYPTO_COIN, PHARAOH_SYMBOLS.WILD, PHARAOH_SYMBOLS.SCATTER
];

let pharaohCryptoGame = {
    isSpinning: false,
    vaultSpins: 0,
    lastWin: 0,
    cryptoLevel: 1,
    ancientPower: 0,
    treasureMultiplier: 1,
    pharaohsBlessing: false,
    anubisGuardian: false,
    pyramidPower: false,
    vaultUnlocked: false,
    cryptoStorm: false
};

function loadPharaohCryptoGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400 font-mono">PHARAOH'S CRYPTO VAULT</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">CRYPTO POWER</label>
                        <input type="number" id="pharaohBet" value="50" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">VAULT ACCESS</label>
                        <select id="vaultAccess" class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="bronze">Bronze Vault (4x)</option>
                            <option value="gold">Gold Vault (8x)</option>
                            <option value="divine">Divine Vault (16x)</option>
                        </select>
                    </div>
                    
                    <button id="unlockVault" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        UNLOCK VAULT
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Vault Spins:</span>
                            <span id="pharaohVaultSpins" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Treasure:</span>
                            <span id="pharaohLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Crypto Level:</span>
                            <span id="cryptoLevel" class="text-purple-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Ancient Power:</span>
                            <span id="ancientPower" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Treasure Multi:</span>
                            <span id="treasureMultiplier" class="text-orange-400">1x</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-900/20 rounded-lg border border-yellow-500/20">
                        <h5 class="text-sm font-bold mb-2 text-yellow-300 font-mono">ANCIENT TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div>👑 <span class="text-yellow-400">Pharaoh:</span> 600x bet</div>
                            <div>🐺 <span class="text-gray-400">Anubis:</span> 400x bet</div>
                            <div>🔺 <span class="text-orange-400">Pyramid:</span> 300x bet</div>
                            <div>🪲 <span class="text-green-400">Scarab:</span> 250x bet</div>
                            <div>☥ <span class="text-purple-400">Ankh:</span> 200x bet</div>
                            <div>⚡ <span class="text-white">Wild:</span> Substitutes all</div>
                            <div>💎 <span class="text-cyan-400">Scatter:</span> Vault bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <div class="relative mb-6">
                        <div id="pharaohReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(25).fill(0).map((_, i) => 
                                `<div class="slot bg-yellow-900/20 rounded-lg flex items-center justify-center text-2xl border border-yellow-500/20 transition-all duration-300">👑</div>`
                            ).join('')}
                        </div>
                        <div id="pharaohBlessingEffect" class="absolute inset-0 bg-gradient-to-t from-yellow-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="anubisGuardianEffect" class="absolute inset-0 bg-gradient-to-b from-gray-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="pyramidPowerEffect" class="absolute inset-0 bg-gradient-radial from-orange-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="cryptoStormEffect" class="absolute inset-0 bg-gradient-conic from-purple-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                    </div>
                    
                    <div id="pharaohGameStatus" class="text-center text-lg font-semibold text-yellow-300 mb-4 h-8 font-mono">
                        Ancient crypto vaults await your discovery...
                    </div>
                    
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PHARAOH'S BLESSING</h6>
                            <div class="text-center">
                                <span id="pharaohBlessingStatus" class="text-yellow-400">DORMANT</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">ANUBIS GUARDIAN</h6>
                            <div class="text-center">
                                <span id="anubisStatus" class="text-gray-400">SLEEPING</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">PYRAMID POWER</h6>
                            <div class="text-center">
                                <span id="pyramidStatus" class="text-orange-400">INACTIVE</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">VAULT STATUS</h6>
                            <div class="text-center">
                                <span id="vaultStatus" class="text-purple-400">LOCKED</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupPharaohCryptoGame();
}

function setupPharaohCryptoGame() {
    document.getElementById('unlockVault').addEventListener('click', unlockCryptoVault);
    const reelsContainer = document.getElementById('pharaohReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 25; i++) { // 5x5 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-yellow-900/20 rounded-lg p-1 text-center text-2xl border border-yellow-500/20 flex items-center justify-center h-12 transition-all duration-300';
        slot.textContent = PHARAOH_REEL_SYMBOLS[i % PHARAOH_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updatePharaohDisplay();
}

function unlockCryptoVault() {
    if (pharaohCryptoGame.isSpinning) return;

    const bet = parseInt(document.getElementById('pharaohBet').value);
    const access = document.getElementById('vaultAccess').value;
    let totalBet;
    
    switch(access) {
        case 'gold': totalBet = bet * 8; break;
        case 'divine': totalBet = bet * 16; break;
        default: totalBet = bet * 4;
    }

    if (pharaohCryptoGame.vaultSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('pharaohGameStatus').textContent = 'INSUFFICIENT CRYPTO TO UNLOCK VAULT';
            return;
        }
        balance -= totalBet;
    } else {
        pharaohCryptoGame.vaultSpins--;
    }

    pharaohCryptoGame.isSpinning = true;
    pharaohCryptoGame.lastWin = 0;
    updateBalance();
    updatePharaohDisplay();
    document.getElementById('pharaohGameStatus').textContent = 'Decrypting ancient pharaoh algorithms...';

    const slots = document.querySelectorAll('#pharaohReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'treasure-highlight'));

    // Crypto storm effect during spin
    document.getElementById('cryptoStormEffect').style.opacity = '0.3';

    let spinDuration = 2600;
    let spinInterval = 110;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = PHARAOH_REEL_SYMBOLS[Math.floor(Math.random() * PHARAOH_REEL_SYMBOLS.length)];
            slot.style.boxShadow = `0 0 ${Math.random() * 15}px gold`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('cryptoStormEffect').style.opacity = '0';
            slots.forEach(slot => slot.style.boxShadow = '');
            finishVaultUnlock(totalBet);
        }
    }, spinInterval);
}

function finishVaultUnlock(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#pharaohReels .slot');
    
    // Enhanced symbol generation with vault access bonuses
    const access = document.getElementById('vaultAccess').value;
    let wildChance = 0.12;
    let scatterChance = 0.08;
    let pharaohChance = 0.15;
    
    if (access === 'gold') {
        wildChance = 0.16;
        scatterChance = 0.12;
        pharaohChance = 0.22;
    } else if (access === 'divine') {
        wildChance = 0.24;
        scatterChance = 0.16;
        pharaohChance = 0.30;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = PHARAOH_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = PHARAOH_SYMBOLS.SCATTER;
        } else if (Math.random() < pharaohChance) {
            symbol = PHARAOH_SYMBOLS.PHARAOH;
        } else {
            symbol = PHARAOH_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkPharaohTreasures(finalSymbols, totalBet);
}

function checkPharaohTreasures(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (divine treasures)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== PHARAOH_SYMBOLS.WILD && symbol !== PHARAOH_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === PHARAOH_SYMBOLS.PHARAOH) {
                multiplier = 600;
                statusMessage = 'DIVINE PHARAOH RULES THE CRYPTO REALM!';
                pharaohCryptoGame.ancientPower += 150;
                pharaohCryptoGame.pharaohsBlessing = true;
            } else if (symbol === PHARAOH_SYMBOLS.ANUBIS) {
                multiplier = 400;
                statusMessage = 'ANUBIS GUARDS THE ETERNAL VAULT!';
                pharaohCryptoGame.ancientPower += 120;
                pharaohCryptoGame.anubisGuardian = true;
            } else if (symbol === PHARAOH_SYMBOLS.PYRAMID) {
                multiplier = 300;
                statusMessage = 'PYRAMID POWER UNLOCKS ANCIENT CRYPTO!';
                pharaohCryptoGame.ancientPower += 100;
                pharaohCryptoGame.pyramidPower = true;
            } else if (symbol === PHARAOH_SYMBOLS.SCARAB) {
                multiplier = 250;
                statusMessage = 'SACRED SCARAB BRINGS FORTUNE!';
                pharaohCryptoGame.ancientPower += 80;
            } else if (symbol === PHARAOH_SYMBOLS.ANKH) {
                multiplier = 200;
                statusMessage = 'ANKH OF LIFE GRANTS ETERNAL WEALTH!';
                pharaohCryptoGame.ancientPower += 70;
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * pharaohCryptoGame.treasureMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind (royal treasures)
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== PHARAOH_SYMBOLS.WILD && symbol !== PHARAOH_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === PHARAOH_SYMBOLS.PHARAOH) multiplier = 200;
            else if (symbol === PHARAOH_SYMBOLS.ANUBIS) multiplier = 150;
            else if (symbol === PHARAOH_SYMBOLS.PYRAMID) multiplier = 120;
            else if (symbol === PHARAOH_SYMBOLS.SCARAB) multiplier = 100;
            else if (symbol === PHARAOH_SYMBOLS.ANKH) multiplier = 80;
            else if (symbol === PHARAOH_SYMBOLS.EYE_OF_HORUS) multiplier = 70;
            else if (symbol === PHARAOH_SYMBOLS.SPHINX) multiplier = 60;
            else if (symbol === PHARAOH_SYMBOLS.CRYPTO_COIN) multiplier = 50;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * pharaohCryptoGame.treasureMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 3-of-a-kind (common treasures)
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== PHARAOH_SYMBOLS.WILD && symbol !== PHARAOH_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === PHARAOH_SYMBOLS.PHARAOH) multiplier = 75;
            else if (symbol === PHARAOH_SYMBOLS.ANUBIS) multiplier = 60;
            else if (symbol === PHARAOH_SYMBOLS.PYRAMID) multiplier = 50;
            else if (symbol === PHARAOH_SYMBOLS.SCARAB) multiplier = 40;
            else if (symbol === PHARAOH_SYMBOLS.ANKH) multiplier = 35;
            else if (symbol === PHARAOH_SYMBOLS.EYE_OF_HORUS) multiplier = 30;
            else if (symbol === PHARAOH_SYMBOLS.SPHINX) multiplier = 25;
            else if (symbol === PHARAOH_SYMBOLS.CRYPTO_COIN) multiplier = 20;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * pharaohCryptoGame.treasureMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === PHARAOH_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.6);
        totalWin *= wildMultiplier;
        statusMessage += ` Divine lightning: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter vault bonus
    const scatterCount = symbols.filter(s => s === PHARAOH_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const vaultSpinsAwarded = 15 + (scatterCount - 3) * 8;
        pharaohCryptoGame.vaultSpins += vaultSpinsAwarded;
        pharaohCryptoGame.cryptoLevel = Math.min(pharaohCryptoGame.cryptoLevel + 1, 10);
        pharaohCryptoGame.vaultUnlocked = true;
        statusMessage += ` 💎 VAULT UNLOCKED! ${vaultSpinsAwarded} vault spins!`;
        
        // Increase treasure multiplier
        pharaohCryptoGame.treasureMultiplier += 0.5;
    }

    // Pharaoh's blessing activation
    if (pharaohCryptoGame.pharaohsBlessing && pharaohCryptoGame.ancientPower >= 300) {
        const blessingMultiplier = 10 + pharaohCryptoGame.cryptoLevel;
        totalWin *= blessingMultiplier;
        statusMessage = `👑 PHARAOH'S DIVINE BLESSING! ${blessingMultiplier}x ROYAL FORTUNE!`;
        pharaohCryptoGame.ancientPower = 0;
        
        // Trigger pharaoh blessing effect
        document.getElementById('pharaohBlessingEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('pharaohBlessingEffect').style.opacity = '0';
            pharaohCryptoGame.pharaohsBlessing = false;
        }, 5000);
    }

    // Anubis guardian activation
    if (pharaohCryptoGame.anubisGuardian && pharaohCryptoGame.ancientPower >= 200) {
        const guardianMultiplier = 8 + pharaohCryptoGame.cryptoLevel;
        totalWin *= guardianMultiplier;
        statusMessage = `🐺 ANUBIS GUARDIAN PROTECTION! ${guardianMultiplier}x ETERNAL GUARD!`;
        pharaohCryptoGame.ancientPower += 50; // Anubis regenerates some power
        
        // Trigger anubis effect
        document.getElementById('anubisGuardianEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('anubisGuardianEffect').style.opacity = '0';
            pharaohCryptoGame.anubisGuardian = false;
        }, 4000);
    }

    // Pyramid power activation
    if (pharaohCryptoGame.pyramidPower && pharaohCryptoGame.ancientPower >= 150) {
        const pyramidMultiplier = 6 + pharaohCryptoGame.cryptoLevel;
        totalWin *= pyramidMultiplier;
        statusMessage = `🔺 PYRAMID POWER SURGE! ${pyramidMultiplier}x ANCIENT ENERGY!`;
        pharaohCryptoGame.treasureMultiplier += 1;
        
        // Trigger pyramid effect
        document.getElementById('pyramidPowerEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('pyramidPowerEffect').style.opacity = '0';
            pharaohCryptoGame.pyramidPower = false;
        }, 3500);
    }

    // Crypto storm (random mega bonus)
    if (pharaohCryptoGame.vaultUnlocked && Math.random() < 0.25) {
        pharaohCryptoGame.cryptoStorm = true;
        totalWin += totalBet * 100;
        statusMessage += ' ⚡ CRYPTO STORM SURGE!';
        
        // Trigger crypto storm effect
        document.getElementById('cryptoStormEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('cryptoStormEffect').style.opacity = '0';
            pharaohCryptoGame.cryptoStorm = false;
        }, 6000);
    }

    // Divine vault bonus
    if (pharaohCryptoGame.treasureMultiplier >= 5) {
        totalWin += totalBet * 200;
        statusMessage += ' DIVINE VAULT MASTERY!';
        pharaohCryptoGame.treasureMultiplier = 2; // Reset but keep bonus
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        pharaohCryptoGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#pharaohReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('treasure-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The ancient vault remains sealed...';
    }

    document.getElementById('pharaohGameStatus').textContent = statusMessage;
    pharaohCryptoGame.isSpinning = false;
    updatePharaohDisplay();
}

function updatePharaohDisplay() {
    const spinButton = document.getElementById('unlockVault');
    spinButton.disabled = pharaohCryptoGame.isSpinning;
    spinButton.textContent = pharaohCryptoGame.isSpinning ? 'UNLOCKING...' : 'UNLOCK VAULT';

    document.getElementById('pharaohVaultSpins').textContent = pharaohCryptoGame.vaultSpins;
    document.getElementById('pharaohLastWin').textContent = `${pharaohCryptoGame.lastWin} GA`;
    document.getElementById('cryptoLevel').textContent = pharaohCryptoGame.cryptoLevel;
    document.getElementById('ancientPower').textContent = pharaohCryptoGame.ancientPower;
    document.getElementById('treasureMultiplier').textContent = `${pharaohCryptoGame.treasureMultiplier.toFixed(1)}x`;
    
    document.getElementById('pharaohBlessingStatus').textContent = pharaohCryptoGame.pharaohsBlessing ? 'ACTIVE!' : 'DORMANT';
    document.getElementById('anubisStatus').textContent = pharaohCryptoGame.anubisGuardian ? 'GUARDING!' : 'SLEEPING';
    document.getElementById('pyramidStatus').textContent = pharaohCryptoGame.pyramidPower ? 'CHARGED!' : 'INACTIVE';
    document.getElementById('vaultStatus').textContent = pharaohCryptoGame.vaultUnlocked ? 'UNLOCKED!' : 'LOCKED';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadPharaohCryptoGame();
});

