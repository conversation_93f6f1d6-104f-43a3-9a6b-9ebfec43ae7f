
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Lucky Lunar Game Implementation
const LUNAR_SYMBOLS = {
    DRAGON: '🐉',
    LANTERN: '🏮',
    FIREWORKS: '🎆',
    COIN: '🪙',
    TIGER: '🐅',
    PHOENIX: '🔥',
    BAMBOO: '🎋',
    MOON: '🌙',
    FORTUNE: '🧧',
    STAR: '⭐'
};

const LUNAR_REEL_SYMBOLS = [
    LUNAR_SYMBOLS.DRAGON, LUNAR_SYMBOLS.LANTERN, LUNAR_SYMBOLS.FIREWORKS,
    LUNAR_SYMBOLS.COIN, LUNAR_SYMBOLS.TIGER, LUNAR_SYMBOLS.PHOENIX,
    LUNAR_SYMBOLS.BAMBOO, LUNAR_SYMBOLS.MOON, LUNAR_SYMBOLS.FORTUNE, LUNAR_SYMBOLS.STAR
];

let luckyLunarGame = {
    isSpinning: false,
    festivalSpins: 0,
    lastWin: 0,
    dragonPower: 0,
    lunarPhase: 0,
    fortuneLevel: 1,
    fireworksCount: 0,
    newYearBonus: false,
    goldenDragon: false,
    celebrationMultiplier: 1
};

function loadLuckyLunarGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400 font-mono">LUCKY LUNAR</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">FESTIVAL OFFERING</label>
                        <input type="number" id="lunarBet" value="30" min="8" max="1000" step="2"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">CELEBRATION LEVEL</label>
                        <select id="lunarCelebration" class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="local">Local Festival (2x)</option>
                            <option value="city">City Celebration (4x)</option>
                            <option value="imperial">Imperial New Year (8x)</option>
                        </select>
                    </div>
                    
                    <button id="joinFestival" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        JOIN THE FESTIVAL
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Festival Spins:</span>
                            <span id="lunarFestivalSpins" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Fortune:</span>
                            <span id="lunarLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Dragon Power:</span>
                            <span id="dragonPower" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Lunar Phase:</span>
                            <span id="lunarPhase" class="text-blue-400">New Moon</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Fortune Level:</span>
                            <span id="fortuneLevel" class="text-purple-400">1</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-900/20 rounded-lg border border-yellow-500/20">
                        <h5 class="text-sm font-bold mb-2 text-yellow-300 font-mono">FESTIVAL TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div>🐉 <span class="text-red-400">Dragon:</span> 180x bet</div>
                            <div>🔥 <span class="text-orange-400">Phoenix:</span> 140x bet</div>
                            <div>🐅 <span class="text-orange-600">Tiger:</span> 120x bet</div>
                            <div>🏮 <span class="text-red-300">Lantern:</span> 100x bet</div>
                            <div>🧧 <span class="text-red-500">Fortune:</span> Wild substitute</div>
                            <div>⭐ <span class="text-yellow-400">Star:</span> Scatter bonus</div>
                            <div>🎆 <span class="text-purple-400">Fireworks:</span> Celebration multiplier</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <div class="relative mb-6">
                        <div id="lunarReels" class="grid grid-cols-5 gap-2 h-64">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-yellow-900/20 rounded-lg flex items-center justify-center text-4xl border border-yellow-500/20 transition-all duration-300">🐉</div>`
                            ).join('')}
                        </div>
                        <div id="dragonEffect" class="absolute inset-0 bg-gradient-to-t from-red-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="phoenixEffect" class="absolute inset-0 bg-gradient-to-b from-orange-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="fireworksEffect" class="absolute inset-0 bg-gradient-radial from-purple-400/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                    </div>
                    
                    <div id="lunarGameStatus" class="text-center text-lg font-semibold text-yellow-300 mb-4 h-8 font-mono">
                        The lunar festival awaits your participation...
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">FESTIVAL STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Golden Dragon:</span>
                                    <span id="goldenDragonStatus" class="text-gold-400">DORMANT</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>New Year:</span>
                                    <span id="newYearStatus" class="text-red-400">WAITING</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DRAGON METER</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="dragonMeter" class="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CELEBRATION</h6>
                            <div class="text-center">
                                <span id="celebrationMultiplier" class="text-2xl text-yellow-400">1x</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupLuckyLunarGame();
}

function setupLuckyLunarGame() {
    document.getElementById('joinFestival').addEventListener('click', joinLunarFestival);
    const reelsContainer = document.getElementById('lunarReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-yellow-900/20 rounded-lg p-2 text-center text-4xl border border-yellow-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = LUNAR_REEL_SYMBOLS[i % LUNAR_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateLunarDisplay();
}

function joinLunarFestival() {
    if (luckyLunarGame.isSpinning) return;

    const bet = parseInt(document.getElementById('lunarBet').value);
    const celebration = document.getElementById('lunarCelebration').value;
    let totalBet;
    
    switch(celebration) {
        case 'city': totalBet = bet * 4; break;
        case 'imperial': totalBet = bet * 8; break;
        default: totalBet = bet * 2;
    }

    if (luckyLunarGame.festivalSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('lunarGameStatus').textContent = 'INSUFFICIENT FESTIVAL OFFERING';
            return;
        }
        balance -= totalBet;
    } else {
        luckyLunarGame.festivalSpins--;
    }

    luckyLunarGame.isSpinning = true;
    luckyLunarGame.lastWin = 0;
    updateBalance();
    updateLunarDisplay();
    document.getElementById('lunarGameStatus').textContent = 'Celebrating the lunar new year...';

    const slots = document.querySelectorAll('#lunarReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    // Fireworks effect during spin
    document.getElementById('fireworksEffect').style.opacity = '0.3';

    let spinDuration = 2200;
    let spinInterval = 90;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = LUNAR_REEL_SYMBOLS[Math.floor(Math.random() * LUNAR_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('fireworksEffect').style.opacity = '0';
            finishLunarFestival(totalBet);
        }
    }, spinInterval);
}

function finishLunarFestival(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#lunarReels .slot');
    
    // Enhanced symbol generation with celebration bonuses
    const celebration = document.getElementById('lunarCelebration').value;
    let wildChance = 0.09;
    let scatterChance = 0.07;
    
    if (celebration === 'city') {
        wildChance = 0.13;
        scatterChance = 0.09;
    } else if (celebration === 'imperial') {
        wildChance = 0.16;
        scatterChance = 0.12;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = LUNAR_SYMBOLS.FORTUNE; // Wild
        } else if (Math.random() < scatterChance) {
            symbol = LUNAR_SYMBOLS.STAR; // Scatter
        } else if (Math.random() < 0.08) {
            symbol = LUNAR_SYMBOLS.FIREWORKS; // Celebration multiplier
        } else {
            symbol = LUNAR_REEL_SYMBOLS[Math.floor(Math.random() * 7)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkLunarWins(finalSymbols, totalBet);
}

function checkLunarWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Count symbol occurrences
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    // Check for festival treasures
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        let multiplier = 0;
        
        if (symbol === LUNAR_SYMBOLS.DRAGON && count >= 3) {
            multiplier = count >= 5 ? 180 : count >= 4 ? 120 : 60;
            statusMessage = 'THE GOLDEN DRAGON DANCES WITH FORTUNE!';
            luckyLunarGame.dragonPower += count * 30;
            luckyLunarGame.goldenDragon = true;
        } else if (symbol === LUNAR_SYMBOLS.PHOENIX && count >= 3) {
            multiplier = count >= 5 ? 140 : count >= 4 ? 95 : 48;
            statusMessage = 'Phoenix rises with new year blessings!';
            luckyLunarGame.dragonPower += count * 20;
        } else if (symbol === LUNAR_SYMBOLS.TIGER && count >= 3) {
            multiplier = count >= 5 ? 120 : count >= 4 ? 80 : 40;
            statusMessage = 'Year of the Tiger brings courage!';
        } else if (symbol === LUNAR_SYMBOLS.LANTERN && count >= 3) {
            multiplier = count >= 5 ? 100 : count >= 4 ? 70 : 35;
            statusMessage = 'Red lanterns illuminate prosperity!';
        } else if (symbol === LUNAR_SYMBOLS.COIN && count >= 3) {
            multiplier = count >= 5 ? 80 : count >= 4 ? 55 : 28;
            statusMessage = 'Ancient coins bring wealth!';
        }

        if (multiplier > 0) {
            totalWin += totalBet * multiplier * luckyLunarGame.celebrationMultiplier;
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Fortune wild substitution bonus
    const fortuneCount = symbols.filter(s => s === LUNAR_SYMBOLS.FORTUNE).length;
    if (fortuneCount > 0 && totalWin > 0) {
        const fortuneMultiplier = 1 + (fortuneCount * 0.7);
        totalWin *= fortuneMultiplier;
        statusMessage += ` Fortune smiles: ${fortuneMultiplier.toFixed(1)}x!`;
        luckyLunarGame.fortuneLevel++;
    }

    // Star scatter bonus
    const starCount = symbols.filter(s => s === LUNAR_SYMBOLS.STAR).length;
    if (starCount >= 3) {
        const freeSpinsAwarded = 12 + (starCount - 3) * 6;
        luckyLunarGame.festivalSpins += freeSpinsAwarded;
        luckyLunarGame.lunarPhase = Math.min(luckyLunarGame.lunarPhase + 1, 5);
        statusMessage += ` ⭐ STELLAR ALIGNMENT! ${freeSpinsAwarded} festival spins!`;
        
        // Increase celebration multiplier
        luckyLunarGame.celebrationMultiplier += 0.3;
    }

    // Fireworks celebration mechanics
    const fireworksCount = symbols.filter(s => s === LUNAR_SYMBOLS.FIREWORKS).length;
    if (fireworksCount > 0) {
        luckyLunarGame.fireworksCount += fireworksCount;
        
        if (fireworksCount >= 3) {
            // Grand fireworks display
            const fireworksMultiplier = 4 + fireworksCount;
            totalWin *= fireworksMultiplier;
            statusMessage = `🎆 GRAND FIREWORKS FINALE! ${fireworksMultiplier}x CELEBRATION!`;
            luckyLunarGame.newYearBonus = true;
            
            // Trigger fireworks effect
            document.getElementById('fireworksEffect').style.opacity = '1';
            setTimeout(() => {
                document.getElementById('fireworksEffect').style.opacity = '0';
            }, 4000);
        } else if (totalWin > 0) {
            // Minor fireworks bonus
            const fireworksBonus = 1 + (fireworksCount * 0.5);
            totalWin *= fireworksBonus;
            statusMessage += ` Fireworks burst: ${fireworksBonus.toFixed(1)}x!`;
        }
    }

    // Golden Dragon awakening bonus
    if (luckyLunarGame.goldenDragon && luckyLunarGame.dragonPower >= 150) {
        const dragonMultiplier = 10 + luckyLunarGame.fortuneLevel;
        totalWin *= dragonMultiplier;
        statusMessage = `🐉 GOLDEN DRAGON AWAKENS! ${dragonMultiplier}x DIVINE FORTUNE!`;
        luckyLunarGame.dragonPower = 0;
        
        // Trigger dragon effect
        document.getElementById('dragonEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('dragonEffect').style.opacity = '0';
            luckyLunarGame.goldenDragon = false;
        }, 5000);
    }

    // New Year bonus activation
    if (luckyLunarGame.newYearBonus && luckyLunarGame.fireworksCount >= 20) {
        totalWin += totalBet * 60;
        statusMessage += ' NEW YEAR MIDNIGHT BONUS!';
        luckyLunarGame.fireworksCount = 0;
        
        // Trigger phoenix effect
        document.getElementById('phoenixEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('phoenixEffect').style.opacity = '0';
            luckyLunarGame.newYearBonus = false;
        }, 3000);
    }

    // Lunar phase progression bonus
    const moonCount = symbols.filter(s => s === LUNAR_SYMBOLS.MOON).length;
    if (moonCount >= 4) {
        luckyLunarGame.lunarPhase = Math.min(luckyLunarGame.lunarPhase + 2, 5);
        totalWin += totalBet * (20 + luckyLunarGame.lunarPhase * 5);
        statusMessage += ' LUNAR ECLIPSE BLESSING!';
    }

    // Bamboo prosperity bonus
    const bambooCount = symbols.filter(s => s === LUNAR_SYMBOLS.BAMBOO).length;
    if (bambooCount >= 5) {
        const bambooMultiplier = 2 + (luckyLunarGame.fortuneLevel * 0.5);
        totalWin *= bambooMultiplier;
        statusMessage += ` Bamboo prosperity: ${bambooMultiplier.toFixed(1)}x!`;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        luckyLunarGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#lunarReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The festival continues, fortune awaits...';
    }

    document.getElementById('lunarGameStatus').textContent = statusMessage;
    luckyLunarGame.isSpinning = false;
    updateLunarDisplay();
}

function updateLunarDisplay() {
    const spinButton = document.getElementById('joinFestival');
    spinButton.disabled = luckyLunarGame.isSpinning;
    spinButton.textContent = luckyLunarGame.isSpinning ? 'CELEBRATING...' : 'JOIN THE FESTIVAL';

    document.getElementById('lunarFestivalSpins').textContent = luckyLunarGame.festivalSpins;
    document.getElementById('lunarLastWin').textContent = `${luckyLunarGame.lastWin} GA`;
    document.getElementById('dragonPower').textContent = luckyLunarGame.dragonPower;
    document.getElementById('fortuneLevel').textContent = luckyLunarGame.fortuneLevel;
    document.getElementById('goldenDragonStatus').textContent = luckyLunarGame.goldenDragon ? 'AWAKENED!' : 'DORMANT';
    document.getElementById('newYearStatus').textContent = luckyLunarGame.newYearBonus ? 'ACTIVE!' : 'WAITING';
    document.getElementById('celebrationMultiplier').textContent = `${luckyLunarGame.celebrationMultiplier.toFixed(1)}x`;
    
    // Update lunar phase display
    const phases = ['New Moon', 'Waxing', 'First Quarter', 'Waxing Gibbous', 'Full Moon', 'Lunar Eclipse'];
    document.getElementById('lunarPhase').textContent = phases[luckyLunarGame.lunarPhase] || 'New Moon';
    
    // Update dragon meter
    const dragonLevel = (luckyLunarGame.dragonPower / 150) * 100;
    document.getElementById('dragonMeter').style.width = `${Math.min(dragonLevel, 100)}%`;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadLuckyLunarGame();
});
