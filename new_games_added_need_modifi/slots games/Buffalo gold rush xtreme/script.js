// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}
window.updateBalance = updateBalance;

// --- BUFFALO GOLD RUSH GAME ---

// Custom SVG Icons for the game
const buffaloIcons = {
    buffalo: `<svg data-symbol="buffalo" class="w-12 h-12 text-orange-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M16 16a4 4 0 0 1-8 0m-6-4h16m-2-4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h0a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2zm-10 0a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h0a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2z"></path><path d="M4 12v4a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4v-4"></path></svg>`,
    goldBuffalo: `<svg data-symbol="buffalo" class="w-12 h-12 text-yellow-400 neon-glow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M16 16a4 4 0 0 1-8 0m-6-4h16m-2-4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h0a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2zm-10 0a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h0a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2z"></path><path d="M4 12v4a4 4 0 0 0 4 4h8a4 4 0 0 0 4-4v-4"></path></svg>`,
    eagle: `<svg data-symbol="eagle" class="w-12 h-12 text-purple-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path><path d="M12 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zm-2-2h4"></path></svg>`,
    wolf: `<svg data-symbol="wolf" class="w-12 h-12 text-blue-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M21 21L12 12 3 21"></path><path d="M3 3l9 9 9-9"></path></svg>`,
    puma: `<svg data-symbol="puma" class="w-12 h-12 text-pink-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><circle cx="12" cy="12" r="10"></circle><path d="M12 6v6l4 2"></path></svg>`,
    wild: `<svg data-symbol="wild" class="w-12 h-12 text-red-500" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"><path d="M12 2l2.5 5L20 8l-4 4 1.5 6L12 15l-5.5 3L8 12 4 8l5.5-1z"></path></svg>`,
    scatter: `<svg data-symbol="scatter" class="w-12 h-12 text-yellow-500 neon-glow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><path d="M12 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 4h.01"></path></svg>`,
    A: `<div data-symbol="A" class="text-4xl font-bold text-red-400">A</div>`,
    K: `<div data-symbol="K" class="text-4xl font-bold text-orange-400">K</div>`,
    Q: `<div data-symbol="Q" class="text-4xl font-bold text-yellow-400">Q</div>`,
    J: `<div data-symbol="J" class="text-4xl font-bold text-green-400">J</div>`,
    '10': `<div data-symbol="10" class="text-4xl font-bold text-blue-400">10</div>`,
    '9': `<div data-symbol="9" class="text-4xl font-bold text-purple-400">9</div>`,
};

// Game state for Buffalo Gold
let buffaloGame = {
    isSpinning: false,
    freeSpins: 0,
    isBonusActive: false,
    goldHeadsCollected: 0,
    upgradedSymbols: [],
    reels: [],
    baseSymbols: ['buffalo', 'eagle', 'wolf', 'puma', 'A', 'K', 'Q', 'J', '10', '9'],
    wildSymbol: 'wild',
    scatterSymbol: 'scatter'
};

function loadBuffaloGoldGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1">
            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 w-full mx-auto">
                <div id="buffaloBonusBar" class="hidden h-16 bg-yellow-900/30 rounded-lg mb-4 p-2 flex items-center justify-around">
                    </div>
                <div id="buffaloReels" class="grid grid-cols-5 gap-2 mb-4">
                    </div>
                <div id="buffaloStatus" class="text-center text-xl font-semibold text-orange-300 neon-glow mb-4">
                    The wild frontier awaits...
                </div>
            </div>

            <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4 flex items-center justify-between">
                 <div class="flex items-center space-x-4">
                    <div class="text-center">
                        <label class="block text-sm font-medium mb-1 text-orange-200">TOTAL BET</label>
                        <input type="number" id="buffaloBet" value="40" min="40" step="40" max="400"
                               class="w-32 bg-black/50 border border-orange-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    <div class="text-center">
                        <span class="block text-sm font-medium mb-1 text-orange-200">Free Spins</span>
                        <span id="freeSpinsDisplay" class="text-purple-400 font-bold text-2xl">0</span>
                    </div>
                     <div class="text-center">
                        <span class="block text-sm font-medium mb-1 text-orange-200">Last Win</span>
                        <span id="lastWinDisplay" class="text-green-400 font-bold text-2xl">0 GA</span>
                    </div>
                 </div>
                 <button id="startSpin" class="cyber-button px-10 py-5 rounded-lg font-semibold text-2xl">SPIN</button>
            </div>
        </div>
    `;
    setupBuffaloGoldGame();
}

function setupBuffaloGoldGame() {
    const reelsContainer = document.getElementById('buffaloReels');
    reelsContainer.innerHTML = ''; // Clear existing reels
    
    // 5x4 grid for "ways" style games
    for (let i = 0; i < 20; i++) {
        const symbolDiv = document.createElement('div');
        symbolDiv.className = 'bg-black/50 border border-orange-800/60 rounded-lg h-24 flex items-center justify-center text-3xl transition-all duration-300';
        const randomSymbolName = buffaloGame.baseSymbols[Math.floor(Math.random() * buffaloGame.baseSymbols.length)];
        symbolDiv.innerHTML = buffaloIcons[randomSymbolName];
        reelsContainer.appendChild(symbolDiv);
    }
    buffaloGame.reels = Array.from(reelsContainer.children);

    document.getElementById('startSpin').addEventListener('click', startSpin);
}


function startSpin() {
    if (buffaloGame.isSpinning) return;

    const bet = parseInt(document.getElementById('buffaloBet').value);

    if (buffaloGame.freeSpins === 0) {
        if (balance < bet) {
            document.getElementById('buffaloStatus').textContent = 'Insufficient funds for the wager.';
            return;
        }
        balance -= bet;
        updateBalance();
    } else {
        buffaloGame.freeSpins--;
        document.getElementById('freeSpinsDisplay').textContent = buffaloGame.freeSpins;
    }

    buffaloGame.isSpinning = true;
    document.getElementById('startSpin').disabled = true;
    document.getElementById('buffaloStatus').textContent = 'The ground trembles... a stampede is coming!';

    // Animate Reels
    let animationComplete = false;
    setTimeout(() => animationComplete = true, 2000);
    const spinInterval = setInterval(() => {
        buffaloGame.reels.forEach(reel => {
            const randomSymbolName = buffaloGame.baseSymbols[Math.floor(Math.random() * buffaloGame.baseSymbols.length)];
            reel.innerHTML = buffaloIcons[randomSymbolName];
        });
        if (animationComplete) {
            clearInterval(spinInterval);
            landFinalSymbols(bet);
        }
    }, 100);
}

function landFinalSymbols(bet) {
    const finalSymbolsGrid = [[], [], [], [], []];
    const finalSymbolNames = [];

    for (let col = 0; col < 5; col++) {
        for (let row = 0; row < 4; row++) {
            let symbolName;
            const rand = Math.random();
            // Wilds only on reels 2, 3, 4
            if (col > 0 && col < 4 && rand < 0.15) {
                symbolName = buffaloGame.wildSymbol;
            } else if (rand < 0.1) {
                // Use gold buffalo in bonus, regular scatter outside
                symbolName = buffaloGame.isBonusActive ? 'goldBuffalo' : buffaloGame.scatterSymbol;
            } else {
                let currentSymbols = [...buffaloGame.baseSymbols];
                // In bonus, replace upgraded symbols with buffalo
                buffaloGame.upgradedSymbols.forEach(upg => {
                    currentSymbols = currentSymbols.map(s => s === upg ? 'buffalo' : s);
                });
                symbolName = currentSymbols[Math.floor(Math.random() * currentSymbols.length)];
            }
            finalSymbolsGrid[col].push(symbolName);
            finalSymbolNames.push(symbolName);
        }
    }
    
    // Display final symbols
    buffaloGame.reels.forEach((reel, index) => {
        const col = index % 5;
        const row = Math.floor(index / 5);
        reel.innerHTML = buffaloIcons[finalSymbolsGrid[col][row]];
    });

    checkWins(finalSymbolsGrid, bet);
}

function checkWins(grid, bet) {
    let totalWin = 0;
    
    // Check for scatter wins / bonus trigger
    const scatterCount = grid.flat().filter(s => s === buffaloGame.scatterSymbol || s === 'goldBuffalo').length;
    
    if (buffaloGame.isBonusActive) {
        if (scatterCount >= 2) {
            const spinsWon = scatterCount === 2 ? 5 : (scatterCount === 3 ? 8 : (scatterCount === 4 ? 15 : 20));
            buffaloGame.freeSpins += spinsWon;
            document.getElementById('buffaloStatus').innerHTML = `<span class="text-yellow-400 font-bold">${spinsWon} MORE SPINS!</span>`;
        }
    } else {
        if (scatterCount >= 3) {
            const spinsWon = scatterCount === 3 ? 8 : (scatterCount === 4 ? 15 : 20);
            triggerFreeSpins(spinsWon);
            return; // Stop processing to show bonus entry
        }
    }
    
    // Calculate "Ways to Win"
    totalWin = calculateWaysWins(grid, bet);

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        document.getElementById('lastWinDisplay').textContent = `${totalWin} GA`;
        if(!document.getElementById('buffaloStatus').textContent.includes('SPINS')) {
             document.getElementById('buffaloStatus').innerHTML = `<span class="text-green-400">STAMPEDE! You won ${totalWin} GA!</span>`;
        }
    } else if(!document.getElementById('buffaloStatus').textContent.includes('SPINS')) {
        document.getElementById('buffaloStatus').textContent = 'The plains are quiet...';
    }
    
    // In bonus, check for gold head collection
    if(buffaloGame.isBonusActive) {
        const headsThisSpin = grid.flat().filter(s => s === 'goldBuffalo').length;
        if(headsThisSpin > 0) {
            buffaloGame.goldHeadsCollected += headsThisSpin;
            updateBonusProgress();
        }
    }

    document.getElementById('freeSpinsDisplay').textContent = buffaloGame.freeSpins;

    setTimeout(() => {
        buffaloGame.isSpinning = false;
        document.getElementById('startSpin').disabled = false;
        if (buffaloGame.freeSpins === 0 && buffaloGame.isBonusActive) {
            endFreeSpins();
        }
    }, 1500);
}

function calculateWaysWins(grid, bet) {
    let win = 0;
    const symbolsOnFirstReel = [...new Set(grid[0])]; // Unique symbols on reel 1

    symbolsOnFirstReel.forEach(symbol => {
        if(symbol === buffaloGame.wildSymbol) return; // Wilds don't start a win

        let ways = grid[0].filter(s => s === symbol || s === buffaloGame.wildSymbol).length;
        let wildMultiplier = 1;
        
        for(let col = 1; col < 5; col++) {
            const matchingSymbols = grid[col].filter(s => s === symbol || s === buffaloGame.wildSymbol).length;
            if (matchingSymbols === 0) break; // Chain is broken
            
            ways *= matchingSymbols;

            // Apply wild multipliers during bonus round
            if (buffaloGame.isBonusActive) {
                const wildsInCol = grid[col].filter(s => s === buffaloGame.wildSymbol).length;
                if(wildsInCol > 0) {
                    wildMultiplier *= (Math.random() > 0.5 ? 3 : 2) * wildsInCol;
                }
            }
        }
        
        const symbolValue = getSymbolValue(symbol);
        win += ways * symbolValue * (bet/40) * wildMultiplier;
    });

    return Math.floor(win);
}

function getSymbolValue(symbol) {
    const values = {
        'buffalo': 3, 'eagle': 1.5, 'wolf': 1.2, 'puma': 1,
        'A': 1, 'K': 1, 'Q': 0.8, 'J': 0.8, '10': 0.5, '9': 0.5
    };
    return values[symbol] || 0;
}


function triggerFreeSpins(spins) {
    buffaloGame.isBonusActive = true;
    buffaloGame.freeSpins = spins;
    buffaloGame.goldHeadsCollected = 0;
    buffaloGame.upgradedSymbols = [];
    
    document.getElementById('freeSpinsDisplay').textContent = spins;
    document.getElementById('buffaloStatus').innerHTML = `<span class="font-bold text-yellow-300 text-3xl">!!! FREE SPINS BONUS !!!</span>`;
    document.getElementById('buffaloBonusBar').classList.remove('hidden');
    
    updateBonusProgress();
    
    setTimeout(() => {
         document.getElementById('startSpin').disabled = false;
         buffaloGame.isSpinning = false;
    }, 2000);
}

function endFreeSpins() {
    buffaloGame.isBonusActive = false;
    document.getElementById('buffaloStatus').textContent = 'The stampede has passed. What treasures remain?';
    document.getElementById('buffaloBonusBar').classList.add('hidden');
}

function updateBonusProgress() {
    const bar = document.getElementById('buffaloBonusBar');
    const upgradeTargets = { 4: 'puma', 7: 'wolf', 13: 'eagle' };
    
    Object.values(upgradeTargets).forEach(s => {
       if(buffaloGame.goldHeadsCollected >= Object.keys(upgradeTargets).find(key => upgradeTargets[key] === s) && !buffaloGame.upgradedSymbols.includes(s)) {
           buffaloGame.upgradedSymbols.push(s);
           document.getElementById('buffaloStatus').innerHTML = `<span class="font-bold text-green-400">${s.toUpperCase()} symbols are now BUFFALO!</span>`;
       }
    });

    bar.innerHTML = `
        <div class="flex items-center">${buffaloIcons.puma} -> ${buffaloGame.upgradedSymbols.includes('puma') ? buffaloIcons.buffalo : '❔'}</div>
        <div class="flex items-center">${buffaloIcons.wolf} -> ${buffaloGame.upgradedSymbols.includes('wolf') ? buffaloIcons.buffalo : '❔'}</div>
        <div class="flex items-center">${buffaloIcons.eagle} -> ${buffaloGame.upgradedSymbols.includes('eagle') ? buffaloIcons.buffalo : '❔'}</div>
        <div class="flex items-center text-xl font-bold">${buffaloIcons.goldBuffalo} <span class="ml-2">${buffaloGame.goldHeadsCollected}</span></div>
    `;
}

// Initialize the game when the page loads.
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadBuffaloGoldGame();
});