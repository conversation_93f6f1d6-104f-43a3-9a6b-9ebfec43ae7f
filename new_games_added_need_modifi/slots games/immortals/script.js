// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

function loadImmortalsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400">BOOK OF IMMORTALS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">BET AMOUNT</label>
                        <input type="number" id="immortalsBet" value="20" min="1" max="1000" 
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">PAYLINES</label>
                        <select id="immortalsPaylines" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="10">10 Lines</option>
                            <option value="25" selected>25 Lines</option>
                            <option value="50">50 Lines</option>
                        </select>
                    </div>
                    
                    <button id="spinImmortals" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4">
                        SPIN REELS
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-purple-300">Total Bet:</span>
                            <span id="immortalsTotalBet" class="text-white">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-purple-300">Last Win:</span>
                            <span id="immortalsLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-purple-300">Free Spins:</span>
                            <span id="immortalsFreeSpins" class="text-yellow-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300">DIVINE POWER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="immortalsPower" class="bg-gradient-to-r from-purple-600 to-pink-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p class="text-xs text-gray-400">Collect ancient symbols to unlock immortal features</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div id="immortalsReels" class="grid grid-cols-5 gap-2 mb-6 slot-reels-container" style="min-height: 250px;">
                        </div>
                    
                    <div id="immortalsStatus" class="text-center text-lg font-semibold text-purple-400 mb-4">
                        Seek the ancient knowledge of immortality
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">PAYTABLE</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>📜 x5:</span><span class="text-yellow-400">500x</span></div>
                                <div class="flex justify-between"><span>⚡ x5:</span><span class="text-purple-400">200x</span></div>
                                <div class="flex justify-between"><span>🔮 x5:</span><span class="text-blue-400">100x</span></div>
                                <div class="flex justify-between"><span>🗿 x5:</span><span class="text-gray-400">50x</span></div>
                            </div>
                        </div>
                        <div class="bg-purple-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-purple-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div>🎯 <span class="text-yellow-400">Wild:</span> Substitutes all symbols</div>
                                <div>💫 <span class="text-purple-400">Scatter:</span> 3+ triggers free spins</div>
                                <div>🏆 <span class="text-green-400">Bonus:</span> Expanding wilds</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupImmortalsGame();
}

let immortalsGame = {
    isSpinning: false,
    freeSpins: 0,
    divinePower: 0,
    // Define reels as arrays of symbols that can appear on each reel
    reelsData: [
        ['📜', '⚡', '🔮', '🗿', '👁️', '⚔️', '🏺', '💎'], // Reel 1 symbols
        ['⚡', '🔮', '🗿', '👁️', '⚔️', '🏺', '💎', '📜'], // Reel 2 symbols
        ['🔮', '🗿', '👁️', '⚔️', '🏺', '💎', '📜', '⚡'], // Reel 3 symbols
        ['🗿', '👁️', '⚔️', '🏺', '💎', '📜', '⚡', '🔮'], // Reel 4 symbols
        ['👁️', '⚔️', '🏺', '💎', '📜', '⚡', '🔮', '🗿']  // Reel 5 symbols
    ],
    // The actual symbols displayed on the grid (5 reels x 3 rows = 15 symbols)
    displayedSymbols: Array(15).fill(''),
    symbols: {
        '📜': { base: 500, type: 'normal' }, // Book of Immortals - high value
        '⚡': { base: 200, type: 'normal' }, // Lightning
        '🔮': { base: 100, type: 'normal' }, // Crystal ball
        '🗿': { base: 50, type: 'normal' },  // Ancient statue
        '👁️': { base: 40, type: 'normal' },  // Eye of wisdom
        '⚔️': { base: 30, type: 'normal' },  // Sacred sword
        '🏺': { base: 25, type: 'normal' },  // Ancient urn
        '💎': { base: 20, type: 'normal' },  // Mystic gem
        '🎯': { base: 0, type: 'wild' },     // Wild symbol
        '💫': { base: 0, type: 'scatter' }   // Scatter symbol
    },
    paylines: [
        [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], // Horizontal lines
        [0, 6, 12, 8, 4], [10, 6, 2, 8, 14], // Diagonal lines
        [5, 1, 7, 13, 9],
        [0, 11, 7, 3, 4], [10, 1, 7, 13, 14],
        [0, 5, 10, 11, 12], [4, 9, 14, 13, 12], // Zig-zag and V shapes
        [0, 1, 7, 13, 14], [10, 11, 7, 3, 4],
        [0, 6, 7, 8, 4], [10, 6, 7, 8, 14],
        [0, 5, 2, 9, 4], [10, 5, 8, 3, 14]
    ]
};

function setupImmortalsGame() {
    document.getElementById('spinImmortals').addEventListener('click', spinImmortalsReels);
    document.getElementById('immortalsBet').addEventListener('input', updateImmortalsDisplay);
    document.getElementById('immortalsPaylines').addEventListener('change', updateImmortalsDisplay);

    renderInitialReels();
    updateImmortalsDisplay();
    updateBetMax(); // Set max bet based on balance
}

function updateBetMax() {
    const betInput = document.getElementById('immortalsBet');
    betInput.max = balance;
    let betValue = parseInt(betInput.value);
    if (isNaN(betValue) || betValue < 1) {
        betValue = 1;
    }
    if (betValue > balance) {
        betValue = balance;
    }
    betInput.value = betValue;
}

function renderInitialReels() {
    const reelsContainer = document.getElementById('immortalsReels');
    reelsContainer.innerHTML = ''; // Clear existing reels

    immortalsGame.displayedSymbols = [];
    for (let i = 0; i < 5; i++) { // 5 reels
        for (let j = 0; j < 3; j++) { // 3 symbols per reel
            const reelSymbolIndex = Math.floor(Math.random() * immortalsGame.reelsData[i].length);
            const symbol = immortalsGame.reelsData[i][reelSymbolIndex];
            immortalsGame.displayedSymbols.push(symbol);

            const symbolElement = document.createElement('div');
            symbolElement.className = 'reel-symbol bg-black/50 border border-purple-500/30 rounded-lg h-20 flex items-center justify-center text-3xl transition-all duration-300';
            symbolElement.textContent = symbol;
            reelsContainer.appendChild(symbolElement);
        }
    }
}

function updateImmortalsDisplay() {
    const bet = parseInt(document.getElementById('immortalsBet').value);
    const paylines = parseInt(document.getElementById('immortalsPaylines').value);
    const totalBet = bet * paylines;

    document.getElementById('immortalsTotalBet').textContent = totalBet + ' GA';
    document.getElementById('immortalsFreeSpins').textContent = immortalsGame.freeSpins;
    document.getElementById('immortalsPower').style.width = immortalsGame.divinePower + '%';
    updateBetMax(); // Re-adjust max bet if balance changed
}

function spinImmortalsReels() {
    if (immortalsGame.isSpinning) return;

    const bet = parseInt(document.getElementById('immortalsBet').value);
    const paylines = parseInt(document.getElementById('immortalsPaylines').value);
    const totalBet = bet * paylines;

    if (immortalsGame.freeSpins === 0 && balance < totalBet) {
        document.getElementById('immortalsStatus').textContent = 'Insufficient balance for this divine quest';
        return;
    }

    // Deduct bet or use free spin
    if (immortalsGame.freeSpins === 0) {
        balance -= totalBet;
        updateBalance();
    } else {
        immortalsGame.freeSpins--;
    }

    immortalsGame.isSpinning = true;
    document.getElementById('spinImmortals').disabled = true;
    document.getElementById('immortalsBet').disabled = true;
    document.getElementById('immortalsPaylines').disabled = true;
    document.getElementById('immortalsStatus').textContent = 'Reels spinning... Unraveling destiny...';
    document.getElementById('immortalsLastWin').textContent = '0 GA'; // Reset last win display

    const reelsContainer = document.getElementById('immortalsReels');
    reelsContainer.innerHTML = ''; // Clear current symbols for animation

    // Generate and animate reels
    let finalSymbols = [];
    const reelElements = [];

    for (let r = 0; r < 5; r++) { // For each of the 5 reels
        const reelColumn = document.createElement('div');
        reelColumn.classList.add('reel-column', 'flex', 'flex-col', 'justify-around', 'h-full', 'p-1', 'bg-black/20', 'rounded', 'shadow-inner', 'animate-spin-immortals-reel');
        
        // Add multiple placeholder symbols for a smoother spin effect
        for (let i = 0; i < 9; i++) { // More symbols for visual spin
            const randomSymbolIndex = Math.floor(Math.random() * immortalsGame.reelsData[r].length);
            const symbol = immortalsGame.reelsData[r][randomSymbolIndex];
            const symbolElement = document.createElement('div');
            symbolElement.classList.add('reel-symbol', 'text-4xl', 'text-white', 'text-center', 'py-2');
            symbolElement.textContent = symbol;
            reelColumn.appendChild(symbolElement);
        }
        reelsContainer.appendChild(reelColumn);
        reelElements.push(reelColumn);

        // Determine final symbols for this reel
        const reelFinalSymbols = [];
        for (let i = 0; i < 3; i++) {
            let symbol;
            const randomChance = Math.random();
            if (randomChance < 0.05) { // 5% chance for scatter
                symbol = '💫';
            } else if (randomChance < 0.15) { // 10% chance for wild
                symbol = '🎯';
            } else if (randomChance < 0.25) { // 10% chance for high value book
                symbol = '📜';
            } else {
                symbol = immortalsGame.reelsData[r][Math.floor(Math.random() * immortalsGame.reelsData[r].length)];
            }
            reelFinalSymbols.push(symbol);
        }
        finalSymbols.push(reelFinalSymbols);
    }

    immortalsGame.displayedSymbols = []; // Flatten final symbols for easy access by position
    finalSymbols.forEach(col => col.forEach(symbol => immortalsGame.displayedSymbols.push(symbol)));

    let currentReelStopping = 0;
    const stopInterval = setInterval(() => {
        if (currentReelStopping < reelElements.length) {
            reelElements[currentReelStopping].classList.remove('animate-spin-immortals-reel');
            reelElements[currentReelStopping].innerHTML = ''; // Clear spinning symbols

            // Display the final symbols for this reel
            finalSymbols[currentReelStopping].forEach(symbol => {
                const symbolElement = document.createElement('div');
                symbolElement.classList.add('reel-symbol', 'text-4xl', 'text-white', 'text-center', 'py-2');
                symbolElement.textContent = symbol;
                reelElements[currentReelStopping].appendChild(symbolElement);
            });

            currentReelStopping++;
        } else {
            clearInterval(stopInterval);
            setTimeout(() => {
                checkImmortalsWins(immortalsGame.displayedSymbols, totalBet);
            }, 500); // Small delay before checking wins
        }
    }, 250); // Time between each reel stopping
}


function checkImmortalsWins(symbols, totalBet) {
    let totalWin = 0;
    let winningLinesCount = 0;
    const paylinesActive = parseInt(document.getElementById('immortalsPaylines').value);

    // Array to store indices of winning symbols for highlighting
    const highlightSymbols = new Set();

    // Check each active payline
    for (let i = 0; i < paylinesActive; i++) {
        if (i >= immortalsGame.paylines.length) break; // Ensure payline exists
        const line = immortalsGame.paylines[i];
        const lineSymbols = line.map(pos => symbols[pos]);

        if (lineSymbols.length === 0) continue;

        let firstSymbol = lineSymbols[0];
        // Determine the base symbol for matching, considering wilds
        for (let j = 0; j < lineSymbols.length; j++) {
            if (immortalsGame.symbols[lineSymbols[j]] && immortalsGame.symbols[lineSymbols[j]].type === 'normal') {
                firstSymbol = lineSymbols[j];
                break;
            }
        }

        if (!firstSymbol || immortalsGame.symbols[firstSymbol].type === 'wild' || immortalsGame.symbols[firstSymbol].type === 'scatter') {
            firstSymbol = lineSymbols.find(s => immortalsGame.symbols[s] && immortalsGame.symbols[s].type === 'normal') || lineSymbols[0];
            if (!firstSymbol) continue; // If line is all wilds/scatters, no normal win
        }


        let matchCount = 0;
        for (let j = 0; j < lineSymbols.length; j++) {
            const currentSymbol = lineSymbols[j];
            if (currentSymbol === firstSymbol || (immortalsGame.symbols[currentSymbol] && immortalsGame.symbols[currentSymbol].type === 'wild')) {
                matchCount++;
            } else {
                break;
            }
        }

        if (matchCount >= 3 && immortalsGame.symbols[firstSymbol].type === 'normal') {
            const multiplier = getImmortalsMultiplier(firstSymbol, matchCount);
            const lineWin = (totalBet / paylinesActive) * multiplier; // Calculate win per line
            totalWin += lineWin;
            winningLinesCount++;

            // Add winning symbol positions to highlight set
            for (let k = 0; k < matchCount; k++) {
                highlightSymbols.add(line[k]);
            }
        }
    }

    // Check for scatters (💫) - these pay anywhere, not just on paylines
    const scatterCount = symbols.filter(s => s === '💫').length;
    if (scatterCount >= 3) {
        const scatterPayoutMultiplier = (scatterCount === 3) ? 2 : ((scatterCount === 4) ? 5 : 10); // Example multipliers
        const scatterWin = totalBet * scatterPayoutMultiplier;
        totalWin += scatterWin;
        immortalsGame.freeSpins += scatterCount * 3; // Grant free spins

        // Increase divine power for scatters
        immortalsGame.divinePower = Math.min(100, immortalsGame.divinePower + (scatterCount * 10));

        // Add scatter symbol positions to highlight set
        symbols.forEach((symbol, index) => {
            if (symbol === '💫') {
                highlightSymbols.add(index);
            }
        });
    }

    // Apply Divine Power bonus
    if (immortalsGame.divinePower >= 100) {
        totalWin *= 2; // Double all wins
        immortalsGame.divinePower = 0; // Reset divine power
        document.getElementById('immortalsStatus').innerHTML =
            `<span class="text-yellow-400">🏆 IMMORTAL ASCENSION! All wins doubled!</span>`;
    }

    totalWin = Math.floor(totalWin); // Ensure win is an integer

    if (totalWin > 0) {
        balance += totalWin;
        updateBalance();
        document.getElementById('immortalsLastWin').textContent = `${totalWin} GA`;
        if (!document.getElementById('immortalsStatus').innerHTML.includes('IMMORTAL ASCENSION')) {
            document.getElementById('immortalsStatus').innerHTML =
                `<span class="text-green-400">Ancient wisdom grants you ${totalWin} GA!</span>`;
        }
    } else {
        document.getElementById('immortalsStatus').textContent = 'The gods remain silent... seek further';
        document.getElementById('immortalsLastWin').textContent = '0 GA';
    }

    // Apply highlighting to winning symbols
    const reelDisplayElements = document.querySelectorAll('#immortalsReels .reel-symbol');
    highlightSymbols.forEach(index => {
        if (reelDisplayElements[index]) {
            reelDisplayElements[index].classList.add('winning-symbol');
        }
    });


    updateImmortalsDisplay(); // Update free spins, total bet, etc.

    // Reset game state and re-enable button after a delay
    setTimeout(() => {
        // Clear highlights
        highlightSymbols.forEach(index => {
            if (reelDisplayElements[index]) {
                reelDisplayElements[index].classList.remove('winning-symbol');
            }
        });
        immortalsGame.isSpinning = false;
        document.getElementById('spinImmortals').disabled = false;
        document.getElementById('immortalsBet').disabled = false;
        document.getElementById('immortalsPaylines').disabled = false;
        if (!document.getElementById('immortalsStatus').innerHTML.includes('IMMORTAL ASCENSION')) {
            // Only reset status if it wasn't the Ascension message
            document.getElementById('immortalsStatus').textContent = 'Seek the ancient knowledge of immortality';
        }
    }, 2500); // Keep highlights for 2.5 seconds
}


function getImmortalsMultiplier(symbol, count) {
    // Multipliers for 3, 4, and 5 matches
    const multipliers = {
        '📜': [0, 0, 20, 100, 500],  // Book of Immortals
        '⚡': [0, 0, 10, 50, 200],   // Lightning
        '🔮': [0, 0, 8, 30, 100],    // Crystal ball
        '🗿': [0, 0, 5, 20, 50],     // Ancient statue
        '👁️': [0, 0, 4, 15, 40],     // Eye of wisdom
        '⚔️': [0, 0, 3, 12, 30],     // Sacred sword
        '🏺': [0, 0, 3, 10, 25],     // Ancient urn
        '💎': [0, 0, 2, 8, 20]       // Mystic gem
    };

    return (multipliers[symbol] && multipliers[symbol][count]) ? multipliers[symbol][count] : 0;
}

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadImmortalsGame();
});