// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Wild West Crypto Heist Game Implementation
const CRYPTO_HEIST_SYMBOLS = {
    CRYPTO_SHERIFF: '🤠',
    DIGITAL_BANDIT: '🏴‍☠️',
    BLOCKCHAIN_VAULT: '🏦',
    BITCOIN_GOLD: '₿',
    ETHEREUM_SILVER: 'Ξ',
    CRYPTO_HORSE: '🐎',
    SALOON_DOORS: '🚪',
    WANTED_POSTER: '📜',
    WILD: '🔫',
    SCATTER: '💰'
};

const CRYPTO_HEIST_REEL_SYMBOLS = [
    CRYPTO_HEIST_SYMBOLS.CRYPTO_SHERIFF, CRYPTO_HEIST_SYMBOLS.DIGITAL_BANDIT, CRYPTO_HEIST_SYMBOLS.BLOCKCHAIN_VAULT,
    CRYPTO_HEIST_SYMBOLS.BITCOIN_GOLD, CRYPTO_HEIST_SYMBOLS.ETHEREUM_SILVER, CRYPTO_HEIST_SYMBOLS.CRYPTO_HORSE,
    CRYPTO_HEIST_SYMBOLS.SALOON_DOORS, CRYPTO_HEIST_SYMBOLS.WANTED_POSTER, CRYPTO_HEIST_SYMBOLS.WILD, CRYPTO_HEIST_SYMBOLS.SCATTER
];

let cryptoHeistGame = {
    isSpinning: false,
    heistSpins: 0,
    lastHeist: 0,
    notorietyLevel: 1,
    cryptoStash: 0,
    heistMultiplier: 1,
    sheriffPursuit: false,
    vaultCracked: false,
    banditGang: false,
    cryptoRush: false,
    blockchainHack: false,
    wantedLevel: 0,
    digitalLoot: 0,
    heistStreak: 0,
    saloonBonus: false
};

function loadCryptoHeistGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <h4 class="text-xl font-bold mb-4 text-yellow-400 font-mono">WILD WEST CRYPTO HEIST</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">HEIST STAKE</label>
                        <input type="number" id="cryptoHeistBet" value="40" min="15" max="1000" step="5"
                               class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-yellow-300">HEIST DIFFICULTY</label>
                        <select id="heistDifficulty" class="w-full bg-black/50 border border-yellow-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="easy">Easy Heist (12x)</option>
                            <option value="medium">Medium Heist (25x)</option>
                            <option value="hard">Hard Heist (50x)</option>
                        </select>
                    </div>
                    
                    <button id="startCryptoHeist" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        START CRYPTO HEIST
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Heist Spins:</span>
                            <span id="cryptoHeistSpins" class="text-yellow-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Heist:</span>
                            <span id="cryptoLastHeist" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Notoriety Level:</span>
                            <span id="cryptoNotorietyLevel" class="text-red-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Crypto Stash:</span>
                            <span id="cryptoStash" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Heist Multiplier:</span>
                            <span id="cryptoHeistMultiplier" class="text-purple-400">1x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Wanted Level:</span>
                            <span id="wantedLevel" class="text-red-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Digital Loot:</span>
                            <span id="digitalLoot" class="text-cyan-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Heist Streak:</span>
                            <span id="heistStreak" class="text-orange-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-yellow-900/20 rounded-lg border border-yellow-500/20">
                        <h5 class="text-sm font-bold mb-2 text-yellow-300 font-mono">OUTLAW FEATURES</h5>
                        <div class="text-xs space-y-1">
                            <div>🤠 <span class="text-blue-400">Sheriff Pursuit:</span> <span id="sheriffStatus" class="text-gray-400">Clear</span></div>
                            <div>🏦 <span class="text-green-400">Vault Status:</span> <span id="vaultStatus" class="text-gray-400">Locked</span></div>
                            <div>🏴‍☠️ <span class="text-red-400">Bandit Gang:</span> <span id="banditGangStatus" class="text-gray-400">Solo</span></div>
                            <div>₿ <span class="text-yellow-400">Crypto Rush:</span> <span id="cryptoRushStatus" class="text-gray-400">Stable</span></div>
                            <div>🔗 <span class="text-purple-400">Blockchain Hack:</span> <span id="blockchainStatus" class="text-gray-400">Secure</span></div>
                            <div>🚪 <span class="text-brown-400">Saloon Bonus:</span> <span id="saloonStatus" class="text-gray-400">Closed</span></div>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300 font-mono">WANTED POSTER</h5>
                        <div class="text-center">
                            <div id="wantedPoster" class="text-red-400">UNKNOWN OUTLAW</div>
                            <div class="text-xs mt-1">
                                <span id="bountyAmount" class="text-yellow-400">Reward: 0 GA</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-yellow-500/30">
                    <div class="relative mb-6">
                        <div id="cryptoHeistReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(25).fill(0).map((_, i) => 
                                `<div class="slot bg-yellow-900/20 rounded-lg flex items-center justify-center text-2xl border border-yellow-500/20 transition-all duration-300">🤠</div>`
                            ).join('')}
                        </div>
                        <div id="sheriffPursuitEffect" class="absolute inset-0 bg-gradient-to-t from-blue-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="vaultCrackedEffect" class="absolute inset-0 bg-gradient-to-b from-green-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="banditGangEffect" class="absolute inset-0 bg-gradient-radial from-red-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="cryptoRushEffect" class="absolute inset-0 bg-gradient-conic from-yellow-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="blockchainHackEffect" class="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-white/10 to-purple-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                        <div id="saloonBonusEffect" class="absolute inset-0 bg-gradient-conic from-brown-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2500"></div>
                    </div>
                    
                    <div id="cryptoHeistStatus" class="text-center text-lg font-semibold text-yellow-300 mb-4 h-8 font-mono">
                        The frontier awaits digital outlaws...
                    </div>
                    
                    <div class="grid grid-cols-4 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">NOTORIETY</h6>
                            <div class="w-full bg-black/50 rounded-full h-2 mb-2">
                                <div id="notorietyMeter" class="bg-gradient-to-r from-yellow-500 to-red-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                            </div>
                            <div class="text-xs text-center">
                                <span id="notorietyLevel" class="text-yellow-400">0%</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">HEIST MODE</h6>
                            <div class="text-center">
                                <span id="heistMode" class="text-yellow-400">PLANNING</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">GANG SIZE</h6>
                            <div class="text-center">
                                <span id="gangSize" class="text-red-400">1</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-yellow-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">ESCAPE ROUTE</h6>
                            <div class="text-center">
                                <span id="escapeRoute" class="text-green-400">CLEAR</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupCryptoHeistGame();
}

function setupCryptoHeistGame() {
    document.getElementById('startCryptoHeist').addEventListener('click', startCryptoHeist);
    const reelsContainer = document.getElementById('cryptoHeistReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 25; i++) { // 5x5 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-yellow-900/20 rounded-lg p-2 text-center text-2xl border border-yellow-500/20 flex items-center justify-center h-12 transition-all duration-300';
        slot.textContent = CRYPTO_HEIST_REEL_SYMBOLS[i % CRYPTO_HEIST_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateCryptoHeistDisplay();
}

function startCryptoHeist() {
    if (cryptoHeistGame.isSpinning) return;

    const bet = parseInt(document.getElementById('cryptoHeistBet').value);
    const difficulty = document.getElementById('heistDifficulty').value;
    let totalBet;
    
    switch(difficulty) {
        case 'medium': totalBet = bet * 25; break;
        case 'hard': totalBet = bet * 50; break;
        default: totalBet = bet * 12;
    }

    if (cryptoHeistGame.heistSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('cryptoHeistStatus').textContent = 'INSUFFICIENT FUNDS FOR HEIST OPERATION';
            return;
        }
        balance -= totalBet;
    } else {
        cryptoHeistGame.heistSpins--;
    }

    cryptoHeistGame.isSpinning = true;
    cryptoHeistGame.lastHeist = 0;
    updateBalance();
    updateCryptoHeistDisplay();
    document.getElementById('cryptoHeistStatus').textContent = 'Crypto heist in progress...';

    const slots = document.querySelectorAll('#cryptoHeistReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'heist-highlight'));

    // Sheriff pursuit effect during spin
    document.getElementById('sheriffPursuitEffect').style.opacity = '0.3';

    let spinDuration = 2600;
    let spinInterval = 110;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = CRYPTO_HEIST_REEL_SYMBOLS[Math.floor(Math.random() * CRYPTO_HEIST_REEL_SYMBOLS.length)];
            slot.style.background = `linear-gradient(45deg, #${Math.floor(Math.random()*16777215).toString(16)}, #eab308)`;
            slot.style.transform = `scale(${0.9 + Math.random() * 0.2}) rotateZ(${Math.random() * 6 - 3}deg)`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('sheriffPursuitEffect').style.opacity = '0';
            slots.forEach(slot => {
                slot.style.background = '';
                slot.style.transform = 'scale(1) rotateZ(0deg)';
            });
            finishCryptoHeist(totalBet);
        }
    }, spinInterval);
}

function finishCryptoHeist(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#cryptoHeistReels .slot');
    
    // Enhanced symbol generation with difficulty bonuses
    const difficulty = document.getElementById('heistDifficulty').value;
    let wildChance = 0.14;
    let scatterChance = 0.11;
    let sheriffChance = 0.16;
    let vaultChance = 0.13;
    
    if (difficulty === 'medium') {
        wildChance = 0.20;
        scatterChance = 0.17;
        sheriffChance = 0.22;
        vaultChance = 0.19;
    } else if (difficulty === 'hard') {
        wildChance = 0.28;
        scatterChance = 0.24;
        sheriffChance = 0.30;
        vaultChance = 0.26;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = CRYPTO_HEIST_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = CRYPTO_HEIST_SYMBOLS.SCATTER;
        } else if (Math.random() < sheriffChance) {
            symbol = CRYPTO_HEIST_SYMBOLS.CRYPTO_SHERIFF;
        } else if (Math.random() < vaultChance) {
            symbol = CRYPTO_HEIST_SYMBOLS.BLOCKCHAIN_VAULT;
        } else {
            symbol = CRYPTO_HEIST_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkCryptoHeistWins(finalSymbols, totalBet);
}

function checkCryptoHeistWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (major heists)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== CRYPTO_HEIST_SYMBOLS.WILD && symbol !== CRYPTO_HEIST_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === CRYPTO_HEIST_SYMBOLS.CRYPTO_SHERIFF) {
                multiplier = 1000;
                statusMessage = 'CRYPTO SHERIFF SHOWDOWN!';
                cryptoHeistGame.notorietyLevel += 3;
                cryptoHeistGame.sheriffPursuit = true;
                cryptoHeistGame.wantedLevel += 5;
            } else if (symbol === CRYPTO_HEIST_SYMBOLS.DIGITAL_BANDIT) {
                multiplier = 850;
                statusMessage = 'DIGITAL BANDIT GANG ASSEMBLED!';
                cryptoHeistGame.notorietyLevel += 2;
                cryptoHeistGame.banditGang = true;
                cryptoHeistGame.wantedLevel += 4;
            } else if (symbol === CRYPTO_HEIST_SYMBOLS.BLOCKCHAIN_VAULT) {
                multiplier = 700;
                statusMessage = 'BLOCKCHAIN VAULT CRACKED!';
                cryptoHeistGame.notorietyLevel += 2;
                cryptoHeistGame.vaultCracked = true;
                cryptoHeistGame.digitalLoot += 500;
            } else if (symbol === CRYPTO_HEIST_SYMBOLS.BITCOIN_GOLD) {
                multiplier = 600;
                statusMessage = 'BITCOIN GOLD RUSH!';
                cryptoHeistGame.cryptoStash += 300;
                cryptoHeistGame.cryptoRush = true;
            } else if (symbol === CRYPTO_HEIST_SYMBOLS.ETHEREUM_SILVER) {
                multiplier = 500;
                statusMessage = 'ETHEREUM SILVER STRIKE!';
                cryptoHeistGame.cryptoStash += 250;
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * cryptoHeistGame.heistMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind and 3-of-a-kind
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== CRYPTO_HEIST_SYMBOLS.WILD && symbol !== CRYPTO_HEIST_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === CRYPTO_HEIST_SYMBOLS.CRYPTO_SHERIFF) multiplier = 350;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.DIGITAL_BANDIT) multiplier = 280;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.BLOCKCHAIN_VAULT) multiplier = 220;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.BITCOIN_GOLD) multiplier = 180;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.ETHEREUM_SILVER) multiplier = 150;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.CRYPTO_HORSE) multiplier = 120;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.SALOON_DOORS) multiplier = 100;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.WANTED_POSTER) multiplier = 80;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * cryptoHeistGame.heistMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== CRYPTO_HEIST_SYMBOLS.WILD && symbol !== CRYPTO_HEIST_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === CRYPTO_HEIST_SYMBOLS.CRYPTO_SHERIFF) multiplier = 120;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.DIGITAL_BANDIT) multiplier = 100;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.BLOCKCHAIN_VAULT) multiplier = 85;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.BITCOIN_GOLD) multiplier = 70;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.ETHEREUM_SILVER) multiplier = 60;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.CRYPTO_HORSE) multiplier = 50;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.SALOON_DOORS) multiplier = 40;
            else if (symbol === CRYPTO_HEIST_SYMBOLS.WANTED_POSTER) multiplier = 35;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * cryptoHeistGame.heistMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild gunslinger bonus
    const wildCount = symbols.filter(s => s === CRYPTO_HEIST_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 1.5);
        totalWin *= wildMultiplier;
        statusMessage += ` Gunslinger bonus: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter heist bonus
    const scatterCount = symbols.filter(s => s === CRYPTO_HEIST_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const heistSpinsAwarded = 25 + (scatterCount - 3) * 12;
        cryptoHeistGame.heistSpins += heistSpinsAwarded;
        cryptoHeistGame.notorietyLevel = Math.min(cryptoHeistGame.notorietyLevel + 1, 10);
        statusMessage += ` 💰 HEIST SPINS TRIGGERED! ${heistSpinsAwarded} spins!`;
        
        // Increase heist multiplier
        cryptoHeistGame.heistMultiplier += 1.5;
        cryptoHeistGame.heistStreak++;
    }

    // Special feature activations
    checkHeistFeatures(totalBet);

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        cryptoHeistGame.lastHeist = totalWin;
        cryptoHeistGame.heistStreak++;
        updateBalance();

        const slots = document.querySelectorAll('#cryptoHeistReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('heist-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Heist failed. Plan your next move carefully...';
        cryptoHeistGame.heistStreak = 0;
    }

    document.getElementById('cryptoHeistStatus').textContent = statusMessage;
    cryptoHeistGame.isSpinning = false;
    updateCryptoHeistDisplay();
}

function checkHeistFeatures(totalBet) {
    // Sheriff Pursuit activation
    if (cryptoHeistGame.sheriffPursuit && cryptoHeistGame.wantedLevel >= 10) {
        const pursuitMultiplier = 20 + cryptoHeistGame.notorietyLevel;
        const pursuitBonus = totalBet * pursuitMultiplier;
        balance += pursuitBonus;
        cryptoHeistGame.lastHeist += pursuitBonus;
        updateBalance();
        
        document.getElementById('sheriffPursuitEffect').style.opacity = '1';
        document.getElementById('sheriffStatus').textContent = 'PURSUING';
        setTimeout(() => {
            document.getElementById('sheriffPursuitEffect').style.opacity = '0';
            document.getElementById('sheriffStatus').textContent = 'Clear';
            cryptoHeistGame.sheriffPursuit = false;
        }, 5000);
    }
    
    // Vault Cracked activation
    if (cryptoHeistGame.vaultCracked && cryptoHeistGame.digitalLoot >= 300) {
        cryptoHeistGame.heistMultiplier *= 4;
        document.getElementById('vaultCrackedEffect').style.opacity = '1';
        document.getElementById('vaultStatus').textContent = 'CRACKED';
        setTimeout(() => {
            document.getElementById('vaultCrackedEffect').style.opacity = '0';
            document.getElementById('vaultStatus').textContent = 'Locked';
            cryptoHeistGame.vaultCracked = false;
        }, 4500);
    }
    
    // Bandit Gang activation
    if (cryptoHeistGame.banditGang && cryptoHeistGame.heistStreak >= 3) {
        const gangBonus = totalBet * 100;
        balance += gangBonus;
        cryptoHeistGame.lastHeist += gangBonus;
        updateBalance();
        
        document.getElementById('banditGangEffect').style.opacity = '1';
        document.getElementById('banditGangStatus').textContent = 'ASSEMBLED';
        setTimeout(() => {
            document.getElementById('banditGangEffect').style.opacity = '0';
            document.getElementById('banditGangStatus').textContent = 'Solo';
            cryptoHeistGame.banditGang = false;
        }, 4000);
    }
    
    // Crypto Rush activation
    if (cryptoHeistGame.cryptoRush && cryptoHeistGame.cryptoStash >= 500) {
        cryptoHeistGame.heistMultiplier *= 3;
        document.getElementById('cryptoRushEffect').style.opacity = '1';
        document.getElementById('cryptoRushStatus').textContent = 'RUSHING';
        setTimeout(() => {
            document.getElementById('cryptoRushEffect').style.opacity = '0';
            document.getElementById('cryptoRushStatus').textContent = 'Stable';
            cryptoHeistGame.cryptoRush = false;
        }, 3500);
    }
    
    // Blockchain Hack activation
    if (cryptoHeistGame.digitalLoot >= 400 && Math.random() < 0.3) {
        cryptoHeistGame.blockchainHack = true;
        const hackBonus = totalBet * 75;
        balance += hackBonus;
        cryptoHeistGame.lastHeist += hackBonus;
        updateBalance();
        
        document.getElementById('blockchainHackEffect').style.opacity = '1';
        document.getElementById('blockchainStatus').textContent = 'HACKED';
        setTimeout(() => {
            document.getElementById('blockchainHackEffect').style.opacity = '0';
            document.getElementById('blockchainStatus').textContent = 'Secure';
            cryptoHeistGame.blockchainHack = false;
        }, 4500);
    }
    
    // Saloon Bonus activation
    const saloonCount = symbols.filter(s => s === CRYPTO_HEIST_SYMBOLS.SALOON_DOORS).length;
    if (saloonCount >= 2) {
        cryptoHeistGame.saloonBonus = true;
        const saloonMultiplier = 1 + (saloonCount * 0.8);
        cryptoHeistGame.heistMultiplier *= saloonMultiplier;
        
        document.getElementById('saloonBonusEffect').style.opacity = '1';
        document.getElementById('saloonStatus').textContent = 'OPEN';
        setTimeout(() => {
            document.getElementById('saloonBonusEffect').style.opacity = '0';
            document.getElementById('saloonStatus').textContent = 'Closed';
            cryptoHeistGame.saloonBonus = false;
        }, 3000);
    }
}

function updateCryptoHeistDisplay() {
    const spinButton = document.getElementById('startCryptoHeist');
    spinButton.disabled = cryptoHeistGame.isSpinning;
    spinButton.textContent = cryptoHeistGame.isSpinning ? 'HEIST IN PROGRESS...' : 'START CRYPTO HEIST';

    document.getElementById('cryptoHeistSpins').textContent = cryptoHeistGame.heistSpins;
    document.getElementById('cryptoLastHeist').textContent = `${cryptoHeistGame.lastHeist} GA`;
    document.getElementById('cryptoNotorietyLevel').textContent = cryptoHeistGame.notorietyLevel;
    document.getElementById('cryptoStash').textContent = cryptoHeistGame.cryptoStash;
    document.getElementById('cryptoHeistMultiplier').textContent = `${cryptoHeistGame.heistMultiplier.toFixed(1)}x`;
    document.getElementById('wantedLevel').textContent = cryptoHeistGame.wantedLevel;
    document.getElementById('digitalLoot').textContent = cryptoHeistGame.digitalLoot;
    document.getElementById('heistStreak').textContent = cryptoHeistGame.heistStreak;
    
    // Update notoriety meter
    const notorietyPercentage = Math.min(100, (cryptoHeistGame.notorietyLevel / 10) * 100);
    document.getElementById('notorietyMeter').style.width = `${notorietyPercentage}%`;
    document.getElementById('notorietyLevel').textContent = `${Math.round(notorietyPercentage)}%`;
    
    // Update wanted poster
    let wantedName = 'UNKNOWN OUTLAW';
    let bounty = 0;
    
    if (cryptoHeistGame.wantedLevel >= 20) {
        wantedName = 'LEGENDARY CRYPTO BANDIT';
        bounty = 10000;
    } else if (cryptoHeistGame.wantedLevel >= 15) {
        wantedName = 'NOTORIOUS DIGITAL OUTLAW';
        bounty = 5000;
    } else if (cryptoHeistGame.wantedLevel >= 10) {
        wantedName = 'WANTED CRYPTO THIEF';
        bounty = 2500;
    } else if (cryptoHeistGame.wantedLevel >= 5) {
        wantedName = 'SUSPECTED HACKER';
        bounty = 1000;
    }
    
    document.getElementById('wantedPoster').textContent = wantedName;
    document.getElementById('bountyAmount').textContent = `Reward: ${bounty} GA`;
    
    // Update status displays
    document.getElementById('heistMode').textContent = cryptoHeistGame.isSpinning ? 'ACTIVE' : 'PLANNING';
    document.getElementById('gangSize').textContent = cryptoHeistGame.banditGang ? '5' : '1';
    document.getElementById('escapeRoute').textContent = cryptoHeistGame.sheriffPursuit ? 'BLOCKED' : 'CLEAR';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadCryptoHeistGame();
});

