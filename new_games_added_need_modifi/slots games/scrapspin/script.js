// Game state
let balance = 1000;

// Update balance display
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML = 
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

        function loadScrapSpinGame() {
            const gameContent = document.getElementById('gameContent');
            gameContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Game Controls -->
                    <div class="lg:col-span-1">
                        <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                            <h4 class="text-xl font-bold mb-4 text-orange-400">SCRAP SPIN</h4>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">BET AMOUNT</label>
                                <input type="number" id="scrapBet" value="10" min="1" max="${balance}" 
                                       class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                            </div>
                            
                            <div class="mb-4">
                                <label class="block text-sm mb-2 text-gray-300">PAYLINES</label>
                                <select id="scrapPaylines" class="w-full bg-black/50 border border-orange-500/50 rounded-lg px-3 py-2 text-white">
                                    <option value="1">1 Line</option>
                                    <option value="3">3 Lines</option>
                                    <option value="5" selected>5 Lines</option>
                                    <option value="9">9 Lines</option>
                                </select>
                            </div>
                            
                            <button id="spinScrap" class="cyber-button w-full py-3 rounded-lg font-bold text-white mb-4">
                                SPIN SCRAP
                            </button>
                            
                            <button id="maxBetScrap" class="w-full py-3 rounded-lg font-bold bg-orange-600 hover:bg-orange-700 text-white mb-4">
                                MAX BET
                            </button>
                            
                            <div class="text-center">
                                <div class="text-sm text-gray-400 mb-1">Total Bet</div>
                                <div id="scrapTotalBet" class="text-xl font-bold text-orange-400">$50</div>
                                <div class="text-sm text-gray-400 mb-1 mt-2">Last Win</div>
                                <div id="scrapLastWin" class="text-xl font-bold text-green-400">$0</div>
                            </div>
                        </div>
                        
                        <!-- Paytable -->
                        <div class="bg-black/30 p-4 rounded-xl border border-orange-500/30 mt-4">
                            <h5 class="text-lg font-bold mb-3 text-orange-400">PAYTABLE</h5>
                            <div id="scrapPaytable" class="text-sm space-y-1">
                                <div class="flex justify-between">
                                    <span>🔧🔧🔧</span>
                                    <span class="text-orange-400">5x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>⚙️⚙️⚙️</span>
                                    <span class="text-orange-400">10x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>🔩🔩🔩</span>
                                    <span class="text-orange-400">15x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>💾💾💾</span>
                                    <span class="text-orange-400">25x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>⚡⚡⚡</span>
                                    <span class="text-orange-400">50x</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>💎💎💎</span>
                                    <span class="text-orange-400">100x</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Slot Machine -->
                    <div class="lg:col-span-2">
                        <div class="bg-black/30 p-6 rounded-xl border border-orange-500/30">
                            <div id="scrapMachine" class="relative bg-black/50 rounded-lg p-6 h-96">
                                <!-- Reels -->
                                <div class="grid grid-cols-5 gap-4 h-full">
                                    <div class="reel-container">
                                        <div id="reel0" class="reel">
                                            <div class="symbol">🔧</div>
                                            <div class="symbol">⚙️</div>
                                            <div class="symbol">🔩</div>
                                        </div>
                                    </div>
                                    <div class="reel-container">
                                        <div id="reel1" class="reel">
                                            <div class="symbol">⚙️</div>
                                            <div class="symbol">🔩</div>
                                            <div class="symbol">💾</div>
                                        </div>
                                    </div>
                                    <div class="reel-container">
                                        <div id="reel2" class="reel">
                                            <div class="symbol">🔩</div>
                                            <div class="symbol">💾</div>
                                            <div class="symbol">⚡</div>
                                        </div>
                                    </div>
                                    <div class="reel-container">
                                        <div id="reel3" class="reel">
                                            <div class="symbol">💾</div>
                                            <div class="symbol">⚡</div>
                                            <div class="symbol">💎</div>
                                        </div>
                                    </div>
                                    <div class="reel-container">
                                        <div id="reel4" class="reel">
                                            <div class="symbol">⚡</div>
                                            <div class="symbol">💎</div>
                                            <div class="symbol">🔧</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Paylines -->
                                <div class="absolute inset-0 pointer-events-none">
                                    <svg id="paylinesSvg" class="w-full h-full">
                                        <!-- Paylines will be drawn here -->
                                    </svg>
                                </div>
                            </div>
                            <div id="scrapStatus" class="text-center mt-4 text-lg font-semibold">Set your bet and spin the scrap reels</div>
                        </div>
                    </div>
                </div>
            `;
            
            initializeScrapSpin();
        }
        
        const SCRAP_SYMBOLS = ['🔧', '⚙️', '🔩', '💾', '⚡', '🔋', '🛠️', '⚗️', '💰', 'S'];

        let scrapSpinGame = {
            isSpinning: false,
            freeSpins: 0,
            lastWin: 0,
            scrapLevel: 1,
            energyBonus: 1
        };

        function initializeScrapSpin() {
            document.getElementById('spinScrap').addEventListener('click', spinScrapReels);
            document.getElementById('maxBetScrap').addEventListener('click', setMaxBetScrap);
            document.getElementById('scrapBet').addEventListener('input', updateScrapTotalBet);
            document.getElementById('scrapPaylines').addEventListener('change', updateScrapTotalBet);
            
            // Add CSS for reels
            const scrapCSS = `
                .reel-container {
                    overflow: hidden;
                    border: 2px solid #ff7f50;
                    border-radius: 8px;
                    background: #111;
                }
                .reel {
                    display: flex;
                    flex-direction: column;
                    transition: transform 0.5s ease;
                }
                .symbol {
                    height: 80px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 2rem;
                    border-bottom: 1px solid #333;
                    background: linear-gradient(45deg, #222, #111);
                }
                .winning-symbol {
                    background: linear-gradient(45deg, #ff7f50, #ff4500) !important;
                    box-shadow: 0 0 20px #ff7f50;
                    animation: glow 0.5s ease-in-out infinite alternate;
                }
                @keyframes glow {
                    0% { box-shadow: 0 0 20px #ff7f50; }
                    100% { box-shadow: 0 0 30px #ff7f50, 0 0 40px #ff4500; }
                }
            `;
            
            if (!document.getElementById('scrap-style')) {
                const style = document.createElement('style');
                style.id = 'scrap-style';
                style.textContent = scrapCSS;
                document.head.appendChild(style);
            }
            
            generateInitialReels();
            updateScrapTotalBet();
        }
        
        function generateInitialReels() {
            for (let reel = 0; reel < 5; reel++) {
                scrapSpinGame.reels[reel] = [];
                for (let i = 0; i < 3; i++) {
                    scrapSpinGame.reels[reel].push(
                        scrapSpinGame.symbols[Math.floor(Math.random() * scrapSpinGame.symbols.length)]
                    );
                }
            }
        }
        
        function updateScrapTotalBet() {
            const bet = parseInt(document.getElementById('scrapBet').value) || 0;
            const paylines = parseInt(document.getElementById('scrapPaylines').value);
            const totalBet = bet * paylines;
            
            document.getElementById('scrapTotalBet').textContent = '$' + totalBet;
        }
        
        function setMaxBetScrap() {
            const maxSingleBet = Math.floor(balance / 9); // Max for 9 paylines
            document.getElementById('scrapBet').value = maxSingleBet;
            document.getElementById('scrapPaylines').value = '9';
            updateScrapTotalBet();
        }
        
        function spinScrapReels() {
            const bet = parseInt(document.getElementById('scrapBet').value);
            const paylines = parseInt(document.getElementById('scrapPaylines').value);
            const totalBet = bet * paylines;
            
            if (totalBet > balance || scrapSpinGame.isSpinning) {
                if (totalBet > balance) alert('Insufficient balance!');
                return;
            }
            
            // Deduct bet
            balance -= totalBet;
            updateBalance();
            scrapSpinGame.isSpinning = true;
            
            // Update UI
            document.getElementById('spinScrap').disabled = true;
            document.getElementById('scrapStatus').textContent = 'Spinning the scrap reels...';
            
            // Generate new reels
            generateNewReels();
            
            // Animate reels
            animateScrapReels();
        }
        
        function generateNewReels() {
            const reels = document.querySelectorAll('.reel');
            reels.forEach(reel => {
                reel.innerHTML = '';
                for (let i = 0; i < 3; i++) {
                    const symbol = document.createElement('div');
                    symbol.className = 'symbol';
                    symbol.textContent = SCRAP_SYMBOLS[Math.floor(Math.random() * SCRAP_SYMBOLS.length)];
                    reel.appendChild(symbol);
                }
            });
        }
        
        function animateScrapReels() {
            const reels = document.querySelectorAll('.reel');
            let completedReels = 0;
            
            reels.forEach((reel, index) => {
                setTimeout(() => {
                    reel.style.animation = 'spin 1s ease-out';
                    setTimeout(() => {
                        reel.style.animation = '';
                        completedReels++;
                        if (completedReels === reels.length) {
                            checkScrapWins();
                        }
                    }, 1000);
                }, index * 200);
            });
        }
        
        function checkScrapWins() {
            const symbols = [];
            const reels = document.querySelectorAll('.reel');
            
            reels.forEach(reel => {
                const middleSymbol = reel.children[1].textContent; // Middle symbol
                symbols.push(middleSymbol);
            });

            let totalWin = 0;
            const winningPositions = new Set();
            let statusMessage = '';

            // Count symbol occurrences
            const symbolCounts = {};
            symbols.forEach(symbol => {
                symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
            });

            const bet = parseInt(document.getElementById('scrapBet').value);
            const paylines = parseInt(document.getElementById('scrapPaylines').value);
            const totalBet = bet * paylines;

            // Check for scrap combinations
            for (const [symbol, count] of Object.entries(symbolCounts)) {
                let multiplier = 0;
                
                if (symbol === '⚡' && count >= 3) {
                    multiplier = count >= 5 ? 50 : count >= 4 ? 25 : 10;
                    statusMessage = 'ENERGY SURGE! Maximum power achieved!';
                } else if (symbol === '🔧' && count >= 3) {
                    multiplier = count >= 5 ? 30 : count >= 4 ? 15 : 8;
                    statusMessage = 'Perfect tool combination found!';
                } else if (symbol === '⚙️' && count >= 3) {
                    multiplier = count >= 5 ? 25 : count >= 4 ? 12 : 6;
                    statusMessage = 'Mechanical precision pays off!';
                } else if (symbol === '💾' && count >= 3) {
                    multiplier = count >= 5 ? 20 : count >= 4 ? 10 : 5;
                    statusMessage = 'Data recovery successful!';
                }

                if (multiplier > 0) {
                    totalWin += totalBet * multiplier * scrapSpinGame.energyBonus;
                    symbols.forEach((s, i) => {
                        if (s === symbol) winningPositions.add(i);
                    });
                }
            }

            // Scatter bonus (S symbols)
            const scatterCount = symbols.filter(s => s === 'S').length;
            if (scatterCount >= 3) {
                const freeSpinsAwarded = 10 + (scatterCount - 3) * 5;
                scrapSpinGame.freeSpins += freeSpinsAwarded;
                statusMessage += ` Scrap bonus! +${freeSpinsAwarded} free spins!`;
                scrapSpinGame.scrapLevel++;
            }

            if (totalWin > 0) {
                totalWin = Math.round(totalWin);
                balance += totalWin;
                scrapSpinGame.lastWin = totalWin;
                updateBalance();
            } else if (!statusMessage) {
                statusMessage = 'No valuable scrap found this time.';
            }

            document.getElementById('scrapStatus').textContent = statusMessage;
            scrapSpinGame.isSpinning = false;
            document.getElementById('spinScrap').disabled = false;
            updateScrapDisplay();
        }
        
        function updateScrapDisplay() {
            document.getElementById('scrapFreeSpins').textContent = scrapSpinGame.freeSpins;
            document.getElementById('scrapLastWin').textContent = `${scrapSpinGame.lastWin} GA`;
            document.getElementById('scrapLevel').textContent = scrapSpinGame.scrapLevel;
        }
        
        function highlightWinningSymbols(winningLines) {
            // Clear previous highlights
            clearWinHighlights();
            
            winningLines.forEach(({ line }) => {
                const payline = scrapSpinGame.paylines[line];
                
                for (let reel = 0; reel < 5; reel++) {
                    const reelElement = document.getElementById(`reel${reel}`);
                    const symbolIndex = payline[reel];
                    const symbolElements = reelElement.querySelectorAll('.symbol');
                    
                    if (symbolElements[symbolIndex + 3]) { // +3 because of offset
                        symbolElements[symbolIndex + 3].classList.add('winning-symbol');
                    }
                }
            });
        }
        
        function clearWinHighlights() {
            document.querySelectorAll('.winning-symbol').forEach(symbol => {
                symbol.classList.remove('winning-symbol');
            });
        }

// Initialize the game when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadScrapSpinGame();
});
