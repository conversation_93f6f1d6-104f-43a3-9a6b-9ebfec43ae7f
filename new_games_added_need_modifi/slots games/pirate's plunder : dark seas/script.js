
// Game state
let balance = 1000;

function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// Pirate's Plunder: Dark Seas Game Implementation
const DARK_PIRATE_SYMBOLS = {
    GHOST_CAPTAIN: '👻',
    KRAKEN: '🐙',
    CURSED_SHIP: '🚢',
    SKULL_CROSSBONES: '☠️',
    DARK_TREASURE: '💀',
    CURSED_COMPASS: '🧭',
    BLOOD_MOON: '🌙',
    STORM_BOTTLE: '⚡',
    WILD: '🌊',
    SCATTER: '🏴‍☠️'
};

const DARK_PIRATE_REEL_SYMBOLS = [
    DARK_PIRATE_SYMBOLS.GHOST_CAPTAIN, DARK_PIRATE_SYMBOLS.KRAKEN, DARK_PIRATE_SYMBOLS.CURSED_SHIP,
    DARK_PIRATE_SYMBOLS.SKULL_CROSSBONES, DARK_PIRATE_SYMBOLS.DARK_TREASURE, DARK_PIRATE_SYMBOLS.CURSED_COMPASS,
    DARK_PIRATE_SYMBOLS.BLOOD_MOON, DARK_PIRATE_SYMBOLS.STORM_BOTTLE, DARK_PIRATE_SYMBOLS.WILD, DARK_PIRATE_SYMBOLS.SCATTER
];

let darkSeasGame = {
    isSpinning: false,
    cursedSpins: 0,
    lastWin: 0,
    darkLevel: 1,
    soulPower: 0,
    cursedMultiplier: 1,
    ghostMode: false,
    krakenRage: false,
    bloodMoonRising: false,
    stormFury: false,
    cursedTreasure: 0,
    darkCompass: false
};

function loadDarkSeasGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <h4 class="text-xl font-bold mb-4 text-purple-400 font-mono">PIRATE'S PLUNDER: DARK SEAS</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">CURSED DOUBLOONS</label>
                        <input type="number" id="darkPirateBet" value="50" min="10" max="1000" step="5"
                               class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-purple-300">DARK VOYAGE</label>
                        <select id="darkVoyage" class="w-full bg-black/50 border border-purple-500/30 rounded-lg px-3 py-2 text-white">
                            <option value="shallow">Shallow Waters (4x)</option>
                            <option value="deep">Deep Abyss (8x)</option>
                            <option value="cursed">Cursed Depths (16x)</option>
                        </select>
                    </div>
                    
                    <button id="sailDarkSeas" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        SAIL DARK SEAS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Cursed Spins:</span>
                            <span id="darkCursedSpins" class="text-purple-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Plunder:</span>
                            <span id="darkLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Dark Level:</span>
                            <span id="darkLevel" class="text-red-400">1</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Soul Power:</span>
                            <span id="soulPower" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Cursed Multi:</span>
                            <span id="cursedMultiplier" class="text-orange-400">1x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Dark Treasure:</span>
                            <span id="cursedTreasure" class="text-yellow-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-purple-900/20 rounded-lg border border-purple-500/20">
                        <h5 class="text-sm font-bold mb-2 text-purple-300 font-mono">CURSED TREASURES</h5>
                        <div class="text-xs space-y-1">
                            <div>👻 <span class="text-gray-400">Ghost Captain:</span> 666x bet</div>
                            <div>🐙 <span class="text-purple-400">Kraken:</span> 500x bet</div>
                            <div>🚢 <span class="text-blue-400">Cursed Ship:</span> 400x bet</div>
                            <div>☠️ <span class="text-white">Skull:</span> 350x bet</div>
                            <div>💀 <span class="text-red-400">Dark Treasure:</span> 300x bet</div>
                            <div>🌊 <span class="text-cyan-400">Wild:</span> Substitutes all</div>
                            <div>🏴‍☠️ <span class="text-black">Scatter:</span> Cursed bonus</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-purple-500/30">
                    <div class="relative mb-6">
                        <div id="darkPirateReels" class="grid grid-cols-5 gap-2 h-80">
                            ${Array(20).fill(0).map((_, i) => 
                                `<div class="slot bg-purple-900/20 rounded-lg flex items-center justify-center text-3xl border border-purple-500/20 transition-all duration-300">👻</div>`
                            ).join('')}
                        </div>
                        <div id="ghostEffect" class="absolute inset-0 bg-gradient-to-t from-gray-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1000"></div>
                        <div id="krakenEffect" class="absolute inset-0 bg-gradient-to-b from-purple-500/30 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-2000"></div>
                        <div id="bloodMoonEffect" class="absolute inset-0 bg-gradient-radial from-red-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-1500"></div>
                        <div id="stormEffect" class="absolute inset-0 bg-gradient-conic from-blue-500/20 via-transparent to-transparent rounded-lg opacity-0 transition-all duration-3000"></div>
                        <div id="cursedAuraEffect" class="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-black/20 to-purple-500/10 rounded-lg opacity-0 transition-all duration-4000"></div>
                    </div>
                    
                    <div id="darkPirateStatus" class="text-center text-lg font-semibold text-purple-300 mb-4 h-8 font-mono">
                        The cursed seas whisper of forbidden treasures...
                    </div>
                    
                    <div class="grid grid-cols-5 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">GHOST MODE</h6>
                            <div class="text-center">
                                <span id="ghostStatus" class="text-gray-400">DORMANT</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">KRAKEN RAGE</h6>
                            <div class="text-center">
                                <span id="krakenStatus" class="text-purple-400">SLEEPING</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">BLOOD MOON</h6>
                            <div class="text-center">
                                <span id="bloodMoonStatus" class="text-red-400">HIDDEN</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">STORM FURY</h6>
                            <div class="text-center">
                                <span id="stormStatus" class="text-blue-400">CALM</span>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-purple-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">DARK COMPASS</h6>
                            <div class="text-center">
                                <span id="compassStatus" class="text-yellow-400">LOST</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    setupDarkSeasGame();
}

function setupDarkSeasGame() {
    document.getElementById('sailDarkSeas').addEventListener('click', sailDarkSeas);
    const reelsContainer = document.getElementById('darkPirateReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) { // 5x4 grid
        const slot = document.createElement('div');
        slot.className = 'slot bg-purple-900/20 rounded-lg p-2 text-center text-3xl border border-purple-500/20 flex items-center justify-center h-16 transition-all duration-300';
        slot.textContent = DARK_PIRATE_REEL_SYMBOLS[i % DARK_PIRATE_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateDarkSeasDisplay();
}

function sailDarkSeas() {
    if (darkSeasGame.isSpinning) return;

    const bet = parseInt(document.getElementById('darkPirateBet').value);
    const voyage = document.getElementById('darkVoyage').value;
    let totalBet;
    
    switch(voyage) {
        case 'deep': totalBet = bet * 8; break;
        case 'cursed': totalBet = bet * 16; break;
        default: totalBet = bet * 4;
    }

    if (darkSeasGame.cursedSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('darkPirateStatus').textContent = 'INSUFFICIENT CURSED DOUBLOONS FOR VOYAGE';
            return;
        }
        balance -= totalBet;
    } else {
        darkSeasGame.cursedSpins--;
    }

    darkSeasGame.isSpinning = true;
    darkSeasGame.lastWin = 0;
    updateBalance();
    updateDarkSeasDisplay();
    document.getElementById('darkPirateStatus').textContent = 'Sailing through cursed waters under blood moon...';

    const slots = document.querySelectorAll('#darkPirateReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight', 'cursed-highlight'));

    // Cursed aura effect during spin
    document.getElementById('cursedAuraEffect').style.opacity = '0.5';

    let spinDuration = 2800;
    let spinInterval = 120;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = DARK_PIRATE_REEL_SYMBOLS[Math.floor(Math.random() * DARK_PIRATE_REEL_SYMBOLS.length)];
            slot.style.filter = `hue-rotate(${Math.random() * 360}deg) brightness(${0.8 + Math.random() * 0.4})`;
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            document.getElementById('cursedAuraEffect').style.opacity = '0';
            slots.forEach(slot => slot.style.filter = '');
            finishDarkVoyage(totalBet);
        }
    }, spinInterval);
}

function finishDarkVoyage(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#darkPirateReels .slot');
    
    // Enhanced symbol generation with voyage bonuses
    const voyage = document.getElementById('darkVoyage').value;
    let wildChance = 0.14;
    let scatterChance = 0.09;
    let ghostChance = 0.16;
    
    if (voyage === 'deep') {
        wildChance = 0.18;
        scatterChance = 0.13;
        ghostChance = 0.22;
    } else if (voyage === 'cursed') {
        wildChance = 0.25;
        scatterChance = 0.18;
        ghostChance = 0.30;
    }

    slots.forEach(slot => {
        let symbol;
        if (Math.random() < wildChance) {
            symbol = DARK_PIRATE_SYMBOLS.WILD;
        } else if (Math.random() < scatterChance) {
            symbol = DARK_PIRATE_SYMBOLS.SCATTER;
        } else if (Math.random() < ghostChance) {
            symbol = DARK_PIRATE_SYMBOLS.GHOST_CAPTAIN;
        } else {
            symbol = DARK_PIRATE_REEL_SYMBOLS[Math.floor(Math.random() * 8)]; // Regular symbols
        }
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkDarkTreasures(finalSymbols, totalBet);
}

function checkDarkTreasures(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for 5-of-a-kind (legendary curses)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5 && symbol !== DARK_PIRATE_SYMBOLS.WILD && symbol !== DARK_PIRATE_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === DARK_PIRATE_SYMBOLS.GHOST_CAPTAIN) {
                multiplier = 666;
                statusMessage = 'THE GHOST CAPTAIN CLAIMS YOUR SOUL!';
                darkSeasGame.soulPower += 200;
                darkSeasGame.ghostMode = true;
            } else if (symbol === DARK_PIRATE_SYMBOLS.KRAKEN) {
                multiplier = 500;
                statusMessage = 'KRAKEN RISES FROM THE ABYSS!';
                darkSeasGame.soulPower += 180;
                darkSeasGame.krakenRage = true;
            } else if (symbol === DARK_PIRATE_SYMBOLS.CURSED_SHIP) {
                multiplier = 400;
                statusMessage = 'THE CURSED SHIP SAILS ETERNAL!';
                darkSeasGame.soulPower += 150;
            } else if (symbol === DARK_PIRATE_SYMBOLS.SKULL_CROSSBONES) {
                multiplier = 350;
                statusMessage = 'DEATH MARKS YOUR TREASURE!';
                darkSeasGame.soulPower += 120;
            } else if (symbol === DARK_PIRATE_SYMBOLS.DARK_TREASURE) {
                multiplier = 300;
                statusMessage = 'CURSED TREASURE BEYOND MORTAL GREED!';
                darkSeasGame.cursedTreasure += 5;
            }

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * darkSeasGame.cursedMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Check for 4-of-a-kind and 3-of-a-kind with similar logic...
    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 4 && symbol !== DARK_PIRATE_SYMBOLS.WILD && symbol !== DARK_PIRATE_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === DARK_PIRATE_SYMBOLS.GHOST_CAPTAIN) multiplier = 200;
            else if (symbol === DARK_PIRATE_SYMBOLS.KRAKEN) multiplier = 150;
            else if (symbol === DARK_PIRATE_SYMBOLS.CURSED_SHIP) multiplier = 120;
            else if (symbol === DARK_PIRATE_SYMBOLS.SKULL_CROSSBONES) multiplier = 100;
            else if (symbol === DARK_PIRATE_SYMBOLS.DARK_TREASURE) multiplier = 80;
            else if (symbol === DARK_PIRATE_SYMBOLS.CURSED_COMPASS) multiplier = 70;
            else if (symbol === DARK_PIRATE_SYMBOLS.BLOOD_MOON) multiplier = 60;
            else if (symbol === DARK_PIRATE_SYMBOLS.STORM_BOTTLE) multiplier = 50;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * darkSeasGame.cursedMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count === 3 && symbol !== DARK_PIRATE_SYMBOLS.WILD && symbol !== DARK_PIRATE_SYMBOLS.SCATTER) {
            let multiplier = 0;
            
            if (symbol === DARK_PIRATE_SYMBOLS.GHOST_CAPTAIN) multiplier = 80;
            else if (symbol === DARK_PIRATE_SYMBOLS.KRAKEN) multiplier = 65;
            else if (symbol === DARK_PIRATE_SYMBOLS.CURSED_SHIP) multiplier = 55;
            else if (symbol === DARK_PIRATE_SYMBOLS.SKULL_CROSSBONES) multiplier = 45;
            else if (symbol === DARK_PIRATE_SYMBOLS.DARK_TREASURE) multiplier = 40;
            else if (symbol === DARK_PIRATE_SYMBOLS.CURSED_COMPASS) multiplier = 35;
            else if (symbol === DARK_PIRATE_SYMBOLS.BLOOD_MOON) multiplier = 30;
            else if (symbol === DARK_PIRATE_SYMBOLS.STORM_BOTTLE) multiplier = 25;

            if (multiplier > 0) {
                totalWin += totalBet * multiplier * darkSeasGame.cursedMultiplier;
                symbols.forEach((s, index) => {
                    if (s === symbol) winningLines.add(index);
                });
            }
        }
    }

    // Wild substitution bonus
    const wildCount = symbols.filter(s => s === DARK_PIRATE_SYMBOLS.WILD).length;
    if (wildCount > 0 && totalWin > 0) {
        const wildMultiplier = 1 + (wildCount * 0.7);
        totalWin *= wildMultiplier;
        statusMessage += ` Cursed waves: ${wildMultiplier.toFixed(1)}x!`;
    }

    // Scatter cursed bonus
    const scatterCount = symbols.filter(s => s === DARK_PIRATE_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const cursedSpinsAwarded = 18 + (scatterCount - 3) * 9;
        darkSeasGame.cursedSpins += cursedSpinsAwarded;
        darkSeasGame.darkLevel = Math.min(darkSeasGame.darkLevel + 1, 10);
        statusMessage += ` 🏴‍☠️ CURSED VOYAGE! ${cursedSpinsAwarded} cursed spins!`;
        
        // Increase cursed multiplier
        darkSeasGame.cursedMultiplier += 0.8;
    }

    // Ghost mode activation
    if (darkSeasGame.ghostMode && darkSeasGame.soulPower >= 400) {
        const ghostMultiplier = 13 + darkSeasGame.darkLevel;
        totalWin *= ghostMultiplier;
        statusMessage = `👻 GHOST CAPTAIN'S ETERNAL CURSE! ${ghostMultiplier}x SPECTRAL FORTUNE!`;
        darkSeasGame.soulPower = 0;
        
        // Trigger ghost effect
        document.getElementById('ghostEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('ghostEffect').style.opacity = '0';
            darkSeasGame.ghostMode = false;
        }, 6000);
    }

    // Kraken rage activation
    if (darkSeasGame.krakenRage && darkSeasGame.soulPower >= 300) {
        const krakenMultiplier = 10 + darkSeasGame.darkLevel;
        totalWin *= krakenMultiplier;
        statusMessage = `🐙 KRAKEN'S ABYSSAL FURY! ${krakenMultiplier}x TENTACLE TERROR!`;
        darkSeasGame.soulPower += 100; // Kraken feeds on souls
        
        // Trigger kraken effect
        document.getElementById('krakenEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('krakenEffect').style.opacity = '0';
            darkSeasGame.krakenRage = false;
        }, 5000);
    }

    // Blood moon rising
    if (darkSeasGame.soulPower >= 250 && Math.random() < 0.4) {
        darkSeasGame.bloodMoonRising = true;
        const moonMultiplier = 7 + darkSeasGame.darkLevel;
        totalWin *= moonMultiplier;
        statusMessage = `🌙 BLOOD MOON RISES! ${moonMultiplier}x CRIMSON CURSE!`;
        
        // Trigger blood moon effect
        document.getElementById('bloodMoonEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('bloodMoonEffect').style.opacity = '0';
            darkSeasGame.bloodMoonRising = false;
        }, 4000);
    }

    // Storm fury (random mega bonus)
    if (darkSeasGame.cursedSpins > 0 && Math.random() < 0.3) {
        darkSeasGame.stormFury = true;
        totalWin += totalBet * 150;
        statusMessage += ' ⚡ CURSED STORM UNLEASHED!';
        
        // Trigger storm effect
        document.getElementById('stormEffect').style.opacity = '1';
        setTimeout(() => {
            document.getElementById('stormEffect').style.opacity = '0';
            darkSeasGame.stormFury = false;
        }, 7000);
    }

    // Dark compass discovery
    if (darkSeasGame.cursedTreasure >= 10) {
        darkSeasGame.darkCompass = true;
        totalWin += totalBet * 300;
        statusMessage += ' 🧭 DARK COMPASS FOUND! ETERNAL NAVIGATION!';
        darkSeasGame.cursedTreasure = 0; // Reset but keep compass
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        darkSeasGame.lastWin = totalWin;
        updateBalance();

        const slots = document.querySelectorAll('#darkPirateReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('cursed-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'The cursed seas keep their dark secrets...';
    }

    document.getElementById('darkPirateStatus').textContent = statusMessage;
    darkSeasGame.isSpinning = false;
    updateDarkSeasDisplay();
}

function updateDarkSeasDisplay() {
    const spinButton = document.getElementById('sailDarkSeas');
    spinButton.disabled = darkSeasGame.isSpinning;
    spinButton.textContent = darkSeasGame.isSpinning ? 'SAILING...' : 'SAIL DARK SEAS';

    document.getElementById('darkCursedSpins').textContent = darkSeasGame.cursedSpins;
    document.getElementById('darkLastWin').textContent = `${darkSeasGame.lastWin} GA`;
    document.getElementById('darkLevel').textContent = darkSeasGame.darkLevel;
    document.getElementById('soulPower').textContent = darkSeasGame.soulPower;
    document.getElementById('cursedMultiplier').textContent = `${darkSeasGame.cursedMultiplier.toFixed(1)}x`;
    document.getElementById('cursedTreasure').textContent = darkSeasGame.cursedTreasure;
    
    document.getElementById('ghostStatus').textContent = darkSeasGame.ghostMode ? 'HAUNTING!' : 'DORMANT';
    document.getElementById('krakenStatus').textContent = darkSeasGame.krakenRage ? 'RAGING!' : 'SLEEPING';
    document.getElementById('bloodMoonStatus').textContent = darkSeasGame.bloodMoonRising ? 'RISING!' : 'HIDDEN';
    document.getElementById('stormStatus').textContent = darkSeasGame.stormFury ? 'RAGING!' : 'CALM';
    document.getElementById('compassStatus').textContent = darkSeasGame.darkCompass ? 'FOUND!' : 'LOST';
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadDarkSeasGame();
});

