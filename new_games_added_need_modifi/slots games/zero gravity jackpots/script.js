// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Zero Gravity Jackpots Game Implementation ---

const ZERO_G_SYMBOLS = {
    WILD: '🌌',      // Galaxy
    SCATTER: '🛸',   // UFO
    BONUS: '⚡',     // Energy
    ASTRONAUT: '👨‍🚀',
    SATELLITE: '🛰️',
    PLANET: '🪐',
    COMET: '☄️',
    ROCKET: '🚀',
    STAR: '⭐',
};

const ZERO_G_REEL_SYMBOLS = [
    ZERO_G_SYMBOLS.ASTRONAUT, ZERO_G_SYMBOLS.SATELLITE, ZERO_G_SYMBOLS.PLANET, ZERO_G_SYMBOLS.COMET,
    ZERO_G_SYMBOLS.ROCKET, ZERO_G_SYMBOLS.STAR, ZERO_G_SYMBOLS.WILD, ZERO_G_SYMBOLS.SCATTER, ZERO_G_SYMBOLS.BONUS
];

const ZERO_G_PAYLINES = [
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    [5, 1, 7, 13, 9], [10, 16, 12, 8, 14],
    [0, 6, 12, 18], [4, 8, 12, 16],
    [0, 5, 11, 16, 17], [4, 9, 13, 18, 19],
    [0, 1, 7, 13, 19], [15, 16, 12, 8, 4]
];

let zeroGGame = {
    isSpinning: false,
    freeSpins: 0,
    gravityField: 0,
    lastWin: 0,
    jackpotLevel: 1
};

function loadZeroGravityGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <h4 class="text-xl font-bold mb-4 text-indigo-300 font-mono">SPACE STATION</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">FUEL (BET)</label>
                        <input type="number" id="zeroGBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-indigo-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startZeroGSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        ENGAGE THRUSTERS
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Discovery:</span>
                            <span id="zeroGLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Zero-G Spins:</span>
                            <span id="zeroGFreeSpins" class="text-blue-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Jackpot Level:</span>
                            <span id="jackpotLevel" class="text-yellow-400">1</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-indigo-500/20">
                        <h5 class="text-sm font-bold mb-2 text-indigo-300 font-mono">GRAVITY FIELD</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="gravityMeter" class="bg-gradient-to-r from-indigo-500 to-purple-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="gravityStatus" class="text-xs text-center text-gray-400">Field: 0%</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-indigo-500/30">
                    <div id="zeroGReels" class="grid grid-cols-5 gap-2 mb-6"></div>
                    
                    <div id="zeroGGameStatus" class="text-center text-lg font-semibold text-indigo-300 mb-4 h-8 font-mono">
                        Floating in zero gravity...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm font-mono">
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${ZERO_G_SYMBOLS.ASTRONAUT} x5:</span><span class="text-indigo-400">300x</span></div>
                                <div class="flex justify-between"><span>${ZERO_G_SYMBOLS.SATELLITE} x5:</span><span class="text-blue-400">200x</span></div>
                                <div class="flex justify-between"><span>${ZERO_G_SYMBOLS.PLANET} x5:</span><span class="text-purple-400">150x</span></div>
                                <div class="flex justify-between"><span>${ZERO_G_SYMBOLS.COMET} x5:</span><span class="text-cyan-400">120x</span></div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-gray-300 mb-2">SPACE LORE</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-purple-400">${ZERO_G_SYMBOLS.WILD} Galaxy:</span> Substitutes all but UFO.</div>
                                <div><span class="text-green-400">${ZERO_G_SYMBOLS.SCATTER} UFO:</span> 3+ grants Zero-G Free Spins.</div>
                                <div><span class="text-yellow-400">${ZERO_G_SYMBOLS.BONUS} Energy:</span> Powers Gravity Field for jackpots.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupZeroGGame();
}

function setupZeroGGame() {
    document.getElementById('startZeroGSpin').addEventListener('click', startZeroGSpin);
    const reelsContainer = document.getElementById('zeroGReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-indigo-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = ZERO_G_REEL_SYMBOLS[i % ZERO_G_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateZeroGDisplay();
}

function updateZeroGDisplay() {
    const spinButton = document.getElementById('startZeroGSpin');
    spinButton.disabled = zeroGGame.isSpinning;
    spinButton.textContent = zeroGGame.isSpinning ? 'DRIFTING...' : 'ENGAGE THRUSTERS';

    document.getElementById('zeroGFreeSpins').textContent = zeroGGame.freeSpins;
    document.getElementById('gravityMeter').style.width = `${zeroGGame.gravityField}%`;
    document.getElementById('gravityStatus').textContent = `Field: ${zeroGGame.gravityField}%`;
    document.getElementById('zeroGLastWin').textContent = `${zeroGGame.lastWin} GA`;
    document.getElementById('jackpotLevel').textContent = zeroGGame.jackpotLevel;
}

function startZeroGSpin() {
    if (zeroGGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('zeroGBet').value);

    if (zeroGGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('zeroGGameStatus').textContent = 'INSUFFICIENT FUEL';
            return;
        }
        balance -= totalBet;
    } else {
        zeroGGame.freeSpins--;
    }

    zeroGGame.isSpinning = true;
    zeroGGame.lastWin = 0;
    updateBalance();
    updateZeroGDisplay();
    document.getElementById('zeroGGameStatus').textContent = 'Navigating through space...';

    const slots = document.querySelectorAll('#zeroGReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = ZERO_G_REEL_SYMBOLS[Math.floor(Math.random() * ZERO_G_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishZeroGSpin(totalBet);
        }
    }, spinInterval);
}

function finishZeroGSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#zeroGReels .slot');
    slots.forEach(slot => {
        const symbol = ZERO_G_REEL_SYMBOLS[Math.floor(Math.random() * ZERO_G_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkZeroGWins(finalSymbols, totalBet);
}

function checkZeroGWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    ZERO_G_PAYLINES.forEach(line => {
        let lineSymbol = symbols[line[0]];
        if (lineSymbol === ZERO_G_SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== ZERO_G_SYMBOLS.WILD && symbols[index] !== ZERO_G_SYMBOLS.SCATTER) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        if (lineSymbol === ZERO_G_SYMBOLS.SCATTER) return;

        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === ZERO_G_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getZeroGMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 25;
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    const scatterCount = symbols.filter(s => s === ZERO_G_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 15 + (scatterCount - 3) * 8;
        zeroGGame.freeSpins += freeSpinsWon;
        statusMessage = `🛸 UFO Encounter! ${freeSpinsWon} zero-g spins granted!`;
    }
    
    const bonusCount = symbols.filter(s => s === ZERO_G_SYMBOLS.BONUS).length;
    if (bonusCount > 0) {
        zeroGGame.gravityField = Math.min(100, zeroGGame.gravityField + bonusCount * 30);
    }

    if (zeroGGame.gravityField >= 100 && totalWin > 0) {
        const jackpotMultiplier = zeroGGame.jackpotLevel * (Math.floor(Math.random() * 25) + 10);
        totalWin *= jackpotMultiplier;
        zeroGGame.gravityField = 0;
        zeroGGame.jackpotLevel = Math.min(5, zeroGGame.jackpotLevel + 1);
        statusMessage = `⚡ ZERO GRAVITY JACKPOT! Discovery multiplied by ${jackpotMultiplier}x!`;
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        zeroGGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('JACKPOT')) {
            statusMessage = statusMessage ? statusMessage : `Space discovery: +${totalWin} GA`;
        }

        const slots = document.querySelectorAll('#zeroGReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Drifting through empty space.';
    }
    
    document.getElementById('zeroGGameStatus').textContent = statusMessage;
    zeroGGame.isSpinning = false;
    updateZeroGDisplay();
}

function getZeroGMultiplier(symbol, count) {
    const multipliers = {
        [ZERO_G_SYMBOLS.ASTRONAUT]: { 3: 60, 4: 150, 5: 300 },
        [ZERO_G_SYMBOLS.SATELLITE]: { 3: 40, 4: 100, 5: 200 },
        [ZERO_G_SYMBOLS.PLANET]: { 3: 30, 4: 75, 5: 150 },
        [ZERO_G_SYMBOLS.COMET]: { 3: 24, 4: 60, 5: 120 },
        [ZERO_G_SYMBOLS.ROCKET]: { 3: 18, 4: 45, 5: 90 },
        [ZERO_G_SYMBOLS.STAR]: { 3: 12, 4: 30, 5: 60 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// --- Neon Nights Game Implementation ---

const NEON_SYMBOLS = {
    WILD: '💎',      // Diamond
    SCATTER: '🌃',   // City Night
    BONUS: '⚡',     // Electric
    CYBER: '🤖',
    NEON: '🔮',
    CIRCUIT: '💻',
    LASER: '🔫',
    HOLO: '👾',
    MATRIX: '🕹️',
};

const NEON_REEL_SYMBOLS = [
    NEON_SYMBOLS.CYBER, NEON_SYMBOLS.NEON, NEON_SYMBOLS.CIRCUIT, NEON_SYMBOLS.LASER,
    NEON_SYMBOLS.HOLO, NEON_SYMBOLS.MATRIX, NEON_SYMBOLS.WILD, NEON_SYMBOLS.SCATTER, NEON_SYMBOLS.BONUS
];

let neonGame = {
    isSpinning: false,
    freeSpins: 0,
    neonCharge: 0,
    lastWin: 0,
    hackLevel: 1
};

function loadNeonNightsGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <h4 class="text-xl font-bold mb-4 text-cyan-300 font-mono">CYBER TERMINAL</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-gray-300">CREDITS (BET)</label>
                        <input type="number" id="neonBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-cyan-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="startNeonSpin" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        JACK IN
                    </button>
                    
                    <div class="space-y-2 text-sm font-mono">
                        <div class="flex justify-between">
                            <span class="text-gray-300">Free Hacks:</span>
                            <span id="neonFreeSpins" class="text-cyan-400">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Last Score:</span>
                            <span id="neonLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-300">Hack Level:</span>
                            <span id="hackLevel" class="text-purple-400">1</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-gray-900/20 rounded-lg border border-cyan-500/20">
                        <h5 class="text-sm font-bold mb-2 text-cyan-300 font-mono">NEON CHARGE</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="neonMeter" class="bg-gradient-to-r from-cyan-500 to-pink-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="neonStatus" class="text-xs text-center text-gray-400">Charge: 0%</p>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-cyan-500/30">
                    <div id="neonReels" class="grid grid-cols-5 gap-2 mb-6"></div>
                    
                    <div id="neonGameStatus" class="text-center text-lg font-semibold text-cyan-300 mb-4 h-8 font-mono">
                        Ready to jack into the matrix...
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">SYSTEM STATUS</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between">
                                    <span>Security Level:</span>
                                    <span class="text-red-400">HIGH</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Firewall:</span>
                                    <span class="text-yellow-400">ACTIVE</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Access:</span>
                                    <span class="text-green-400">GRANTED</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-900/20 p-4 rounded-lg border border-cyan-500/20">
                            <h6 class="font-bold text-gray-300 mb-2">CYBER LORE</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-cyan-400">${NEON_SYMBOLS.WILD} City:</span> Substitutes all but Pill.</div>
                                <div><span class="text-pink-400">${NEON_SYMBOLS.SCATTER} Pill:</span> 3+ grants Matrix Free Runs.</div>
                                <div><span class="text-green-400">${NEON_SYMBOLS.BONUS} Robot:</span> Charges Neon for system breach.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupNeonGame();
}

function setupNeonGame() {
    document.getElementById('startNeonSpin').addEventListener('click', startNeonSpin);
    const reelsContainer = document.getElementById('neonReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-cyan-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = NEON_REEL_SYMBOLS[i % NEON_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateNeonDisplay();
}

function updateNeonDisplay() {
    const spinButton = document.getElementById('startNeonSpin');
    spinButton.disabled = neonGame.isSpinning;
    spinButton.textContent = neonGame.isSpinning ? 'HACKING...' : 'JACK IN';

    document.getElementById('neonFreeSpins').textContent = neonGame.freeSpins;
    document.getElementById('neonMeter').style.width = `${neonGame.neonCharge}%`;
    document.getElementById('neonStatus').textContent = `Charge: ${neonGame.neonCharge}%`;
    document.getElementById('neonLastWin').textContent = `${neonGame.lastWin} GA`;
    document.getElementById('hackLevel').textContent = neonGame.hackLevel;
}

function startNeonSpin() {
    if (neonGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('neonBet').value);

    if (neonGame.freeSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('neonGameStatus').textContent = 'INSUFFICIENT CREDITS';
            return;
        }
        balance -= totalBet;
    } else {
        neonGame.freeSpins--;
    }

    neonGame.isSpinning = true;
    neonGame.lastWin = 0;
    updateBalance();
    updateNeonDisplay();
    document.getElementById('neonGameStatus').textContent = 'Infiltrating the mainframe...';

    const slots = document.querySelectorAll('#neonReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = NEON_REEL_SYMBOLS[Math.floor(Math.random() * NEON_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishNeonSpin(totalBet);
        }
    }, spinInterval);
}

function finishNeonSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#neonReels .slot');
    slots.forEach(slot => {
        const symbol = NEON_REEL_SYMBOLS[Math.floor(Math.random() * NEON_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkNeonWins(finalSymbols, totalBet);
}

function checkNeonWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // Check for system overload (5 matching symbols)
    const symbolCounts = {};
    symbols.forEach(symbol => {
        symbolCounts[symbol] = (symbolCounts[symbol] || 0) + 1;
    });

    for (const [symbol, count] of Object.entries(symbolCounts)) {
        if (count >= 5) {
            neonGame.systemOverload = true;
            totalWin += totalBet * 50;
            statusMessage = '🔥 SYSTEM OVERLOAD! MAXIMUM PAYOUT! 🔥';
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
            break;
        } else if (count >= 4) {
            totalWin += totalBet * 10;
            statusMessage = 'Major system breach detected!';
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        } else if (count >= 3) {
            totalWin += totalBet * 3;
            if (!statusMessage) statusMessage = 'Security protocol bypassed!';
            symbols.forEach((s, i) => {
                if (s === symbol) winningLines.add(i);
            });
        }
    }

    // Check for scatter bonus (💊 symbols) - Fixed
    const scatterCount = symbols.filter(s => s === NEON_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsAwarded = 5 + (scatterCount - 3) * 3;
        neonGame.freeSpins += freeSpinsAwarded;
        statusMessage += ` +${freeSpinsAwarded} free hacks!`;
    }

    // Neon charge system
    const robotCount = symbols.filter(s => s === NEON_SYMBOLS.BONUS).length;
    if (robotCount > 0) {
        neonGame.neonCharge = Math.min(100, neonGame.neonCharge + (robotCount * 20));
        if (neonGame.neonCharge >= 100) {
            totalWin += totalBet * 25;
            statusMessage += ' NEON OVERCHARGE! Massive bonus!';
            neonGame.neonCharge = 0;
        }
    }

    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        neonGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('OVERLOAD')) {
            statusMessage = statusMessage ? statusMessage : `Successful hack: +${totalWin} GA`;
        }

        const slots = document.querySelectorAll('#neonReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = 'Access denied.';
    }
    
    document.getElementById('neonGameStatus').textContent = statusMessage;
    neonGame.isSpinning = false;
    updateNeonDisplay();
}

function getNeonMultiplier(symbol, count) {
    const multipliers = {
        [NEON_SYMBOLS.CYBER]: { 3: 56, 4: 140, 5: 280 },
        [NEON_SYMBOLS.NEON]: { 3: 38, 4: 95, 5: 190 },
        [NEON_SYMBOLS.CIRCUIT]: { 3: 28, 4: 70, 5: 140 },
        [NEON_SYMBOLS.LASER]: { 3: 22, 4: 55, 5: 110 },
        [NEON_SYMBOLS.HOLO]: { 3: 16, 4: 40, 5: 80 },
        [NEON_SYMBOLS.MATRIX]: { 3: 10, 4: 25, 5: 50 },
    };
    return multipliers[symbol]?.[count] || 0;
}

document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadZeroGravityGame();
});
