// Game state
let balance = 1000;

// Game constants
const SYMBOLS = {
    WILD: '🍀',
    SCATTER: '💰',
    LEPRECHAUN: '🧑‍🦰',
    RAINBOW: '🌈',
    HAT: '🎩',
    BEER: '🍺',
    HARP: '🎻',
    COIN: '🪙',
};

// All symbols that can appear on the reels during a normal spin.
const REEL_SYMBOLS = [
    SYMBOLS.LEPRECHAUN, SYMBOLS.RAINBOW, SYMBOLS.HAT, SYMBOLS.BEER,
    SYMBOLS.HARP, SYMBOLS.COIN, SYMBOLS.WILD, SYMBOLS.SCATTER
];

// Define the 5x4 grid of paylines. Each array is a list of indices (0-19).
const PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], 
    [5, 6, 7, 8, 9], 
    [10, 11, 12, 13, 14], 
    [15, 16, 17, 18, 19],
    // Diagonals & V-shapes
    [0, 6, 12, 8, 4], 
    [15, 11, 7, 3, 19],
    [0, 1, 7, 13, 19],
    [15, 16, 12, 8, 4],
    [5, 1, 2, 3, 9],
    [10, 6, 7, 8, 14]
];

// Game state object
let leprechaunLegacyGame = {
    isSpinning: false,
    rainbowSpins: 0,
    luckMeter: 0,
    lastWin: 0
};

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

/**
 * Loads the initial HTML for the game into the page.
 */
function loadLeprechaunLegacyGame() {
    const gameContent = document.getElementById('gameContent');
    // Updated HTML with correct paytable references
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <h4 class="text-xl font-bold mb-4 text-green-400">LEPRECHAUN LEGACY</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-green-300">BET AMOUNT</label>
                        <input type="number" id="leprechaunBet" value="20" min="1" max="1000" 
                               class="w-full bg-black/50 border border-green-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="spinLeprechaun" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        SPIN FOR GOLD
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-green-300">Last Win:</span>
                            <span id="leprechaunLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-green-300">Rainbow Spins:</span>
                            <span id="leprechaunRainbow" class="text-blue-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-green-900/20 rounded-lg border border-green-500/20">
                        <h5 class="text-sm font-bold mb-2 text-green-300">LUCK OF THE IRISH</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="luckMeter" class="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full transition-all duration-1000" style="width: 0%"></div>
                        </div>
                        <p id="leprechaunLuckMeter" class="text-xs text-center text-gray-400">Fill the meter for Irish blessings (0%)</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-green-500/30">
                    <div id="leprechaunReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid will be generated here -->
                    </div>
                    
                    <div id="leprechaunStatus" class="text-center text-lg font-semibold text-green-400 mb-4 h-8">
                        Hunt for the Leprechaun's gold!
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-green-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-green-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${SYMBOLS.LEPRECHAUN} x5:</span><span class="text-emerald-400">100x</span></div>
                                <div class="flex justify-between"><span>${SYMBOLS.RAINBOW} x5:</span><span class="text-green-400">50x</span></div>
                                <div class="flex justify-between"><span>${SYMBOLS.HAT} x5:</span><span class="text-yellow-400">25x</span></div>
                                <div class="flex justify-between"><span>${SYMBOLS.BEER} x5:</span><span class="text-amber-400">15x</span></div>
                            </div>
                        </div>
                        <div class="bg-green-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-green-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-green-400">${SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter</div>
                                <div><span class="text-purple-400">${SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Rainbow Spins</div>
                                <div><span class="text-emerald-400">Bonus:</span> Luck meter adds multipliers</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    setupLeprechaunLegacyGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupLeprechaunLegacyGame() {
    document.getElementById('spinLeprechaun').addEventListener('click', spinLeprechaunReels);
    const reels = document.getElementById('leprechaunReels');
    // Create the initial 20 slots for the 5x4 grid
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-green-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = REEL_SYMBOLS[i % REEL_SYMBOLS.length];
        reels.appendChild(slot);
    }
    updateLeprechaunDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateLeprechaunDisplay() {
    const spinButton = document.getElementById('spinLeprechaun');
    spinButton.disabled = leprechaunLegacyGame.isSpinning;
    spinButton.textContent = leprechaunLegacyGame.isSpinning ? 'SPINNING...' : 'SPIN FOR GOLD';
}

/**
 * Starts the reel spinning process.
 */
function spinLeprechaunReels() {
    const betAmount = parseInt(document.getElementById('leprechaunBet').value);
    if (balance < betAmount && leprechaunLegacyGame.rainbowSpins === 0) {
        document.getElementById('leprechaunStatus').textContent = 'Not enough balance!';
        return;
    }
    if (leprechaunLegacyGame.isSpinning) return;

    // Deduct bet only if it's not a free spin
    if (leprechaunLegacyGame.rainbowSpins === 0) {
        balance -= betAmount;
    } else {
        leprechaunLegacyGame.rainbowSpins--;
    }
    
    updateBalance();
    document.getElementById('leprechaunRainbow').textContent = leprechaunLegacyGame.rainbowSpins;

    leprechaunLegacyGame.isSpinning = true;
    updateLeprechaunDisplay();
    document.getElementById('leprechaunStatus').textContent = 'Searching for Leprechaun gold...';
    document.getElementById('leprechaunLastWin').textContent = `0 GA`;


    const slots = document.querySelectorAll('#leprechaunReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = REEL_SYMBOLS[Math.floor(Math.random() * REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishSpin(betAmount);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} betAmount - The amount bet on this spin.
 */
function finishSpin(betAmount) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#leprechaunReels .slot');
    slots.forEach(slot => {
        const symbol = REEL_SYMBOLS[Math.floor(Math.random() * REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkLeprechaunWins(finalSymbols, betAmount);
}


/**
 * Calculates wins based on paylines and scatter symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} betAmount - The amount bet for the spin.
 */
function checkLeprechaunWins(symbols, betAmount) {
    let totalWin = 0;
    const winningLines = new Set();

    // 1. Check for payline wins
    PAYLINES.forEach(line => {
        const firstSymbol = symbols[line[0]];
        let lineSymbol = firstSymbol;
        let consecutiveCount = 0;

        // If the first symbol is a WILD, find the first non-wild to determine the line's symbol
        if (lineSymbol === SYMBOLS.WILD) {
            for (const index of line) {
                if (symbols[index] !== SYMBOLS.WILD) {
                    lineSymbol = symbols[index];
                    break;
                }
            }
        }
        
        // Count consecutive symbols from the left
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break; // Stop counting once the chain is broken
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getLeprechaunMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                totalWin += (betAmount / 20) * multiplier; // Win is per line bet
                // Add winning indices to the set for highlighting
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins
    const scatterCount = symbols.filter(s => s === SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 5 + (scatterCount - 3) * 2;
        leprechaunLegacyGame.rainbowSpins += freeSpinsWon;
        document.getElementById('leprechaunRainbow').textContent = leprechaunLegacyGame.rainbowSpins;
        document.getElementById('leprechaunStatus').textContent = `You won ${freeSpinsWon} Rainbow Spins!`;
    }

    // 3. Process results
    if (totalWin > 0) {
        balance += totalWin;
        leprechaunLegacyGame.lastWin = totalWin;
        updateBalance();
        document.getElementById('leprechaunStatus').textContent = `Gold found! You won ${Math.round(totalWin)} GA!`;
        document.getElementById('leprechaunLastWin').textContent = `${Math.round(totalWin)} GA`;
        
        // Highlight winning slots
        const slots = document.querySelectorAll('#leprechaunReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (scatterCount < 3) {
        document.getElementById('leprechaunStatus').textContent = 'No gold this time. Keep hunting!';
    }
    
    // Update luck meter
    leprechaunLegacyGame.luckMeter += Math.floor(Math.random() * 5) + 1;
    if (leprechaunLegacyGame.luckMeter > 100) leprechaunLegacyGame.luckMeter = 100;
    document.getElementById('luckMeter').style.width = `${leprechaunLegacyGame.luckMeter}%`;
    document.getElementById('leprechaunLuckMeter').textContent = `Fill the meter for Irish blessings (${leprechaunLegacyGame.luckMeter}%)`;

    leprechaunLegacyGame.isSpinning = false;
    updateLeprechaunDisplay();
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getLeprechaunMultiplier(symbol, count) {
    const multipliers = {
        [SYMBOLS.LEPRECHAUN]: { 3: 20, 4: 50, 5: 100 },
        [SYMBOLS.RAINBOW]:   { 3: 10, 4: 25, 5: 50 },
        [SYMBOLS.HAT]:       { 3: 8,  4: 20, 5: 25 },
        [SYMBOLS.BEER]:      { 3: 5,  4: 10, 5: 15 },
        [SYMBOLS.HARP]:      { 3: 3,  4: 8,  5: 12 },
        [SYMBOLS.COIN]:      { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    loadLeprechaunLegacyGame();
});
