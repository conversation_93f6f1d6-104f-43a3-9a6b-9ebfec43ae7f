// Game state
let balance = 1000;

/**
 * Updates the main balance display in the header.
 */
function updateBalance() {
    document.getElementById('balanceDisplay').innerHTML =
        `Balance: <span class="text-green-400 neon-glow">${balance} GA</span>`;
}

// --- Samurai Honor Game Implementation ---

// Game constants
const SAMURAI_SYMBOLS = {
    WILD: '👹',      // <PERSON>i <PERSON> (Wild)
    SCATTER: '🏯',    // Castle (Scatter)
    SAMURAI: '👺',
    KATANA: '⚔️',
    CHERRY_BLOSSOM: '🌸',
    KOI: '🎏',
    FAN: '🏮',
    TORII: '⛩️',
};

const SAMURAI_REEL_SYMBOLS = [
    SAMURAI_SYMBOLS.SAMURAI, SAMURAI_SYMBOLS.KATANA, SAMURAI_SYMBOLS.CHERRY_BLOSSOM,
    SAMURAI_SYMBOLS.KOI, SAMURAI_SYMBOLS.FAN, SAMURAI_SYMBOLS.TORII,
    SAMURAI_SYMBOLS.WILD, SAMURAI_SYMBOLS.SCATTER
];

// Define the 5x4 grid of paylines.
const SAMURAI_PAYLINES = [
    // Horizontal
    [0, 1, 2, 3, 4], [5, 6, 7, 8, 9], [10, 11, 12, 13, 14], [15, 16, 17, 18, 19],
    // V-shapes
    [0, 6, 12, 8, 4], [15, 11, 7, 3, 19],
    // <PERSON>agonals
    [0, 6, 12, 18], [4, 8, 12, 16],
    // Zig-zags
    [0, 5, 11, 17], [4, 9, 13, 19],
    [5, 1, 7, 13, 9], [10, 6, 12, 8, 14],
    [0, 1, 7, 8, 14], [15, 16, 12, 13, 19]
];

// Game state object
let samuraiHonorGame = {
    isSpinning: false,
    shogunSpins: 0, // Free spins
    honorMeter: 0,
    lastWin: 0
};

/**
 * Loads the game's HTML structure into the page.
 */
function loadSamuraiHonorGame() {
    const gameContent = document.getElementById('gameContent');
    gameContent.innerHTML = `
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Game Controls -->
            <div class="lg:col-span-1">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <h4 class="text-xl font-bold mb-4 text-red-400">SAMURAI HONOR</h4>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2 text-red-300">TOTAL BET</label>
                        <input type="number" id="samuraiBet" value="50" min="10" max="1000" step="10"
                               class="w-full bg-black/50 border border-red-500/30 rounded-lg px-3 py-2 text-white">
                    </div>
                    
                    <button id="drawSword" class="w-full cyber-button py-3 rounded-lg font-semibold mb-4 text-white">
                        DRAW SWORD
                    </button>
                    
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-red-300">Last Win:</span>
                            <span id="samuraiLastWin" class="text-green-400">0 GA</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-red-300">Shogun Spins:</span>
                            <span id="samuraiShogunSpins" class="text-yellow-400">0</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-red-900/20 rounded-lg border border-red-500/20">
                        <h5 class="text-sm font-bold mb-2 text-red-300">HONOR METER</h5>
                        <div class="w-full bg-black/50 rounded-full h-3 mb-2">
                            <div id="honorMeter" class="bg-gradient-to-r from-red-600 to-orange-500 h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <p id="samuraiHonorStatus" class="text-xs text-center text-gray-400">Gain honor for a Bushido Bonus! (0%)</p>
                    </div>
                </div>
            </div>

            <!-- Game Display -->
            <div class="lg:col-span-2">
                <div class="bg-black/30 p-6 rounded-xl border border-red-500/30">
                    <div id="samuraiReels" class="grid grid-cols-5 gap-2 mb-6">
                        <!-- 5x4 reel grid will be generated here -->
                    </div>
                    
                    <div id="samuraiStatus" class="text-center text-lg font-semibold text-red-400 mb-4 h-8">
                        Follow the path of the warrior to find great fortune.
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="bg-red-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-red-300 mb-2">PAYTABLE (x5)</h6>
                            <div class="space-y-1 text-xs">
                                <div class="flex justify-between"><span>${SAMURAI_SYMBOLS.WILD} (Wild) x5:</span><span class="text-yellow-300">120x</span></div>
                                <div class="flex justify-between"><span>${SAMURAI_SYMBOLS.SAMURAI} x5:</span><span class="text-red-400">100x</span></div>
                                <div class="flex justify-between"><span>${SAMURAI_SYMBOLS.KATANA} x5:</span><span class="text-gray-300">50x</span></div>
                                <div class="flex justify-between"><span>${SAMURAI_SYMBOLS.CHERRY_BLOSSOM} x5:</span><span class="text-pink-400">25x</span></div>
                            </div>
                        </div>
                        <div class="bg-red-900/20 p-3 rounded-lg">
                            <h6 class="font-bold text-red-300 mb-2">FEATURES</h6>
                            <div class="space-y-1 text-xs">
                                <div><span class="text-yellow-400">${SAMURAI_SYMBOLS.WILD} Wild:</span> Substitutes all except Scatter.</div>
                                <div><span class="text-purple-400">${SAMURAI_SYMBOLS.SCATTER} Scatter:</span> 3+ triggers Shogun Spins.</div>
                                <div><span class="text-orange-400">Honor:</span> Fill meter for a win multiplier.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    setupSamuraiHonorGame();
}

/**
 * Sets up the game event listeners and initial reel state.
 */
function setupSamuraiHonorGame() {
    document.getElementById('drawSword').addEventListener('click', strikeWithHonor);
    const reelsContainer = document.getElementById('samuraiReels');
    reelsContainer.innerHTML = '';
    for (let i = 0; i < 20; i++) {
        const slot = document.createElement('div');
        slot.className = 'slot bg-black/50 rounded-lg p-2 text-center text-4xl border border-red-500/20 flex items-center justify-center h-20 transition-all duration-300';
        slot.textContent = SAMURAI_REEL_SYMBOLS[i % SAMURAI_REEL_SYMBOLS.length];
        reelsContainer.appendChild(slot);
    }
    updateSamuraiDisplay();
}

/**
 * Updates the UI elements based on the current game state.
 */
function updateSamuraiDisplay() {
    const spinButton = document.getElementById('drawSword');
    spinButton.disabled = samuraiHonorGame.isSpinning;
    spinButton.textContent = samuraiHonorGame.isSpinning ? 'STRIKING...' : 'DRAW SWORD';

    document.getElementById('samuraiShogunSpins').textContent = samuraiHonorGame.shogunSpins;
    document.getElementById('honorMeter').style.width = `${samuraiHonorGame.honorMeter}%`;
    document.getElementById('samuraiHonorStatus').textContent = `Gain honor for a Bushido Bonus! (${samuraiHonorGame.honorMeter}%)`;
    document.getElementById('samuraiLastWin').textContent = `${samuraiHonorGame.lastWin} GA`;
}

/**
 * Starts the reel spinning process.
 */
function strikeWithHonor() {
    if (samuraiHonorGame.isSpinning) return;

    const totalBet = parseInt(document.getElementById('samuraiBet').value);

    if (samuraiHonorGame.shogunSpins === 0) {
        if (balance < totalBet) {
            document.getElementById('samuraiStatus').textContent = 'Not enough Ryo to proceed!';
            return;
        }
        balance -= totalBet;
    } else {
        samuraiHonorGame.shogunSpins--;
    }

    samuraiHonorGame.isSpinning = true;
    samuraiHonorGame.lastWin = 0;
    updateBalance();
    updateSamuraiDisplay();
    document.getElementById('samuraiStatus').textContent = 'The cherry blossoms fall...';

    const slots = document.querySelectorAll('#samuraiReels .slot');
    slots.forEach(slot => slot.classList.remove('win-highlight'));

    let spinDuration = 1500;
    let spinInterval = 50;
    let elapsed = 0;

    const spinAnimation = setInterval(() => {
        elapsed += spinInterval;
        slots.forEach(slot => {
            slot.textContent = SAMURAI_REEL_SYMBOLS[Math.floor(Math.random() * SAMURAI_REEL_SYMBOLS.length)];
        });

        if (elapsed >= spinDuration) {
            clearInterval(spinAnimation);
            finishSamuraiSpin(totalBet);
        }
    }, spinInterval);
}

/**
 * Generates the final reel results and checks for wins.
 * @param {number} totalBet - The amount bet on this spin.
 */
function finishSamuraiSpin(totalBet) {
    const finalSymbols = [];
    const slots = document.querySelectorAll('#samuraiReels .slot');
    slots.forEach(slot => {
        const symbol = SAMURAI_REEL_SYMBOLS[Math.floor(Math.random() * SAMURAI_REEL_SYMBOLS.length)];
        finalSymbols.push(symbol);
        slot.textContent = symbol;
    });

    checkSamuraiWins(finalSymbols, totalBet);
}

/**
 * Gets the payout multiplier for a given symbol and count.
 * @param {string} symbol - The winning symbol.
 * @param {number} count - The number of consecutive symbols.
 * @returns {number} The multiplier for the win.
 */
function getSamuraiMultiplier(symbol, count) {
    const multipliers = {
        [SAMURAI_SYMBOLS.WILD]:           { 3: 25, 4: 60, 5: 120 },
        [SAMURAI_SYMBOLS.SAMURAI]:        { 3: 20, 4: 50, 5: 100 },
        [SAMURAI_SYMBOLS.KATANA]:         { 3: 10, 4: 25, 5: 50 },
        [SAMURAI_SYMBOLS.CHERRY_BLOSSOM]: { 3: 8,  4: 20, 5: 25 },
        [SAMURAI_SYMBOLS.KOI]:            { 3: 5,  4: 10, 5: 15 },
        [SAMURAI_SYMBOLS.FAN]:            { 3: 3,  4: 8,  5: 12 },
        [SAMURAI_SYMBOLS.TORII]:          { 3: 2,  4: 5,  5: 10 },
    };
    return multipliers[symbol]?.[count] || 0;
}

/**
 * Calculates wins based on paylines and special symbols.
 * @param {string[]} symbols - The array of 20 symbols on the grid.
 * @param {number} totalBet - The total amount bet for the spin.
 */
function checkSamuraiWins(symbols, totalBet) {
    let totalWin = 0;
    const winningLines = new Set();
    let statusMessage = '';

    // 1. Check for payline wins
    SAMURAI_PAYLINES.forEach(line => {
        const firstSymbolOnLine = symbols[line[0]];
        if (firstSymbolOnLine === SAMURAI_SYMBOLS.SCATTER) return;

        let lineSymbol = firstSymbolOnLine;
        if (lineSymbol === SAMURAI_SYMBOLS.WILD) {
            for (const index of line) {
                const symbol = symbols[index];
                if (symbol !== SAMURAI_SYMBOLS.WILD && symbol !== SAMURAI_SYMBOLS.SCATTER) {
                    lineSymbol = symbol;
                    break;
                }
            }
        }
        
        let consecutiveCount = 0;
        for (const index of line) {
            if (symbols[index] === lineSymbol || symbols[index] === SAMURAI_SYMBOLS.WILD) {
                consecutiveCount++;
            } else {
                break;
            }
        }

        if (consecutiveCount >= 3) {
            const multiplier = getSamuraiMultiplier(lineSymbol, consecutiveCount);
            if (multiplier > 0) {
                const lineBet = totalBet / 20;
                totalWin += lineBet * multiplier;
                for (let i = 0; i < consecutiveCount; i++) {
                    winningLines.add(line[i]);
                }
            }
        }
    });

    // 2. Check for scatter wins (Shogun Spins)
    const scatterCount = symbols.filter(s => s === SAMURAI_SYMBOLS.SCATTER).length;
    if (scatterCount >= 3) {
        const freeSpinsWon = 10 + (scatterCount - 3) * 5;
        samuraiHonorGame.shogunSpins += freeSpinsWon;
        statusMessage = `🏯 Shogun's Decree! Won ${freeSpinsWon} Shogun Spins!`;
    }
    
    // 3. Update Honor Meter
    samuraiHonorGame.honorMeter = Math.min(100, samuraiHonorGame.honorMeter + Math.floor(Math.random() * 5) + 2);

    // 4. Check for Bushido Bonus
    if (samuraiHonorGame.honorMeter >= 100 && totalWin > 0) {
        const honorMultiplier = Math.floor(Math.random() * 4) + 2;
        totalWin *= honorMultiplier;
        samuraiHonorGame.honorMeter = 0;
        statusMessage = `👹 BUSHIDO BONUS! ${honorMultiplier}x WIN MULTIPLIER!`;
    }

    // 5. Process final results
    if (totalWin > 0) {
        totalWin = Math.round(totalWin);
        balance += totalWin;
        samuraiHonorGame.lastWin = totalWin;
        updateBalance();
        
        if (!statusMessage.includes('BUSHIDO BONUS')) {
            statusMessage = statusMessage || `An honorable victory! You won ${totalWin} GA!`;
        }

        const slots = document.querySelectorAll('#samuraiReels .slot');
        winningLines.forEach(index => {
            slots[index].classList.add('win-highlight');
        });
    } else if (!statusMessage) {
        statusMessage = "A warrior's patience is key...";
    }
    
    document.getElementById('samuraiStatus').textContent = statusMessage;
    samuraiHonorGame.isSpinning = false;
    updateSamuraiDisplay();
}

/**
 * This function is called by the DOMContentLoaded listener and starts the game.
 * It replaces the old placeholder function.
 */
function initializeGame() {
    loadSamuraiHonorGame();
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', function() {
    updateBalance();
    initializeGame();
});
