<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPS Showdown - Provably Fair Rock Paper Scissors</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    <style>
        :root {
            --rps-blue: #3498DB;
            --rps-red: #E74C3C;
            --rps-green: #2ECC71;
            --rps-purple: #9B59B6;
            --rps-yellow: #F1C40F;
            --rps-grey: #7F8C8D;
            --rps-dark: #2C3E50;
            --rps-light: #ECF0F1;
            
            --rock-color: var(--rps-red);
            --paper-color: var(--rps-blue);
            --scissors-color: var(--rps-green);
            
            --win-color: #2ECC71;
            --lose-color: #E74C3C;
            --tie-color: #F1C40F;
            
            --background-gradient: linear-gradient(135deg, #2C3E50 0%, #1E293B 100%);
            --card-gradient: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        }

        body {
            background: var(--background-gradient);
            color: white;
            font-family: 'Poppins', sans-serif;
        }

        .rps-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
        }

        .game-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .game-title {
            font-size: 3rem;
            background: linear-gradient(45deg, var(--rps-red), var(--rps-blue), var(--rps-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 0.5rem;
        }

        .game-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stats-panel, .fairness-panel {
            background: var(--card-gradient);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .panel-title {
            font-size: 1.2rem;
            margin-bottom: 1.5rem;
            text-align: center;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .game-area {
            background: var(--card-gradient);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .scoreboard {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 2rem;
        }

        .score-display {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            min-width: 120px;
        }

        .score-display.player {
            border-left: 4px solid var(--rps-blue);
        }

        .score-display.opponent {
            border-left: 4px solid var(--rps-red);
        }

        .score-label {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .score-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: white;
        }

        .best-of {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            min-width: 120px;
            border-left: 4px solid var(--rps-purple);
        }

        .battle-area {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            margin: 2rem 0;
        }

        .player-side, .opponent-side {
            text-align: center;
            flex: 1;
        }

        .player-label, .opponent-label {
            margin-bottom: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .player-label {
            color: var(--rps-blue);
        }

        .opponent-label {
            color: var(--rps-red);
        }

        .hand-display {
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .versus {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 1rem;
            color: var(--rps-yellow);
        }

        .result-display {
            text-align: center;
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 10px;
            min-height: 60px;
            font-size: 1.5rem;
            font-weight: bold;
            transition: all 0.3s ease;
            background: rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .choice-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        .choice-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 10px;
            padding: 1rem;
            width: 100px;
            height: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-weight: 600;
        }

        .choice-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .choice-btn .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .choice-btn.rock {
            background: linear-gradient(45deg, var(--rock-color), rgba(231, 76, 60, 0.7));
        }

        .choice-btn.paper {
            background: linear-gradient(45deg, var(--paper-color), rgba(52, 152, 219, 0.7));
        }

        .choice-btn.scissors {
            background: linear-gradient(45deg, var(--scissors-color), rgba(46, 204, 113, 0.7));
        }

        .choice-btn:disabled {
            opacity: 0.5;
            transform: none;
            cursor: not-allowed;
        }

        .game-controls {
            margin-top: 2rem;
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .control-btn {
            background: var(--rps-purple);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .fairness-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .seed-display {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            background: rgba(0, 0, 0, 0.2);
            padding: 0.75rem;
            border-radius: 6px;
            margin: 0.5rem 0;
            word-break: break-all;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .verification-tool {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .verify-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 0.75rem;
            color: white;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .verify-btn {
            background: var(--rps-green);
            border: none;
            border-radius: 6px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .history-container {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .history-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .rules-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .rules-toggle {
            background: none;
            border: none;
            color: var(--rps-blue);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            padding: 0;
            text-decoration: underline;
        }

        .rules-content {
            margin-top: 1rem;
            line-height: 1.6;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--rps-blue);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            z-index: 1000;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .win-animation {
            animation: pulse 1s ease-in-out;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .shake-animation {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .battle-area {
                flex-direction: column;
                gap: 2rem;
            }
            
            .versus {
                margin: 1rem 0;
            }
            
            .game-title {
                font-size: 2rem;
            }
            
            .choice-btn {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <div class="rps-container">
        <div class="game-header">
            <h1 class="game-title">RPS SHOWDOWN</h1>
            <p class="game-subtitle">Provably Fair • Transparent • Best of 5</p>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats -->
            <div class="stats-panel">
                <h3 class="panel-title">Player Stats</h3>
                
                <div class="stat-card">
                    <div class="stat-label">Total Games</div>
                    <div class="stat-value" id="totalGames">0</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Games Won</div>
                    <div class="stat-value" id="gamesWon">0</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Win Rate</div>
                    <div class="stat-value" id="winRate">0%</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Balance</div>
                    <div class="stat-value" id="playerBalance">1000 GA</div>
                </div>

                <div class="rules-section">
                    <button class="rules-toggle" onclick="toggleRules()">
                        <i class="fas fa-info-circle"></i> How to Play & Fairness
                    </button>
                    <div class="rules-content" id="rulesContent" style="display: none;">
                        <p><strong>Game Rules:</strong></p>
                        <ul>
                            <li>Rock beats Scissors</li>
                            <li>Scissors beats Paper</li>
                            <li>Paper beats Rock</li>
                            <li>First to win 3 rounds wins the game</li>
                        </ul>
                        <p><strong>Provably Fair:</strong></p>
                        <ul>
                            <li>Each game uses cryptographic seeds to ensure fairness</li>
                            <li>Client seed: generated by your browser</li>
                            <li>Server seed: pre-committed and hashed</li>
                            <li>Verify any game outcome using the verification tool</li>
                            <li>AI opponent has exactly 1/3 probability for each choice</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Game Area -->
            <div class="game-area">
                <div class="scoreboard">
                    <div class="score-display player">
                        <div class="score-label">YOU</div>
                        <div class="score-value" id="playerScore">0</div>
                    </div>
                    
                    <div class="best-of">
                        <div class="score-label">BEST OF</div>
                        <div class="score-value">5</div>
                    </div>
                    
                    <div class="score-display opponent">
                        <div class="score-label">AI</div>
                        <div class="score-value" id="opponentScore">0</div>
                    </div>
                </div>
                
                <div class="battle-area">
                    <div class="player-side">
                        <div class="player-label">YOUR CHOICE</div>
                        <div class="hand-display" id="playerHand">
                            <i class="fas fa-question"></i>
                        </div>
                    </div>
                    
                    <div class="versus">VS</div>
                    
                    <div class="opponent-side">
                        <div class="opponent-label">AI CHOICE</div>
                        <div class="hand-display" id="opponentHand">
                            <i class="fas fa-question"></i>
                        </div>
                    </div>
                </div>
                
                <div class="result-display" id="resultDisplay">
                    Make your choice to start the game!
                </div>
                
                <div class="bet-controls" style="display: flex; justify-content: center; margin: 1rem 0; gap: 1rem;">
                    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 0.5rem 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <button style="background: rgba(0, 0, 0, 0.3); border: none; color: white; border-radius: 5px; width: 30px; height: 30px; cursor: pointer;" onclick="adjustBet(-10)">-</button>
                        <span id="betAmount" style="font-weight: bold; color: var(--rps-yellow);">50 GA</span>
                        <button style="background: rgba(0, 0, 0, 0.3); border: none; color: white; border-radius: 5px; width: 30px; height: 30px; cursor: pointer;" onclick="adjustBet(10)">+</button>
                    </div>
                    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 10px; padding: 0.5rem 1rem; display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 0.9rem;">Potential Win:</span>
                        <span id="potentialWin" style="font-weight: bold; color: var(--win-color);">100 GA</span>
                    </div>
                </div>
                
                <div class="choice-buttons">
                    <button class="choice-btn rock" id="rockBtn" onclick="makeChoice('rock')">
                        <div class="icon">✊</div>
                        <div>Rock</div>
                    </button>
                    
                    <button class="choice-btn paper" id="paperBtn" onclick="makeChoice('paper')">
                        <div class="icon">✋</div>
                        <div>Paper</div>
                    </button>
                    
                    <button class="choice-btn scissors" id="scissorsBtn" onclick="makeChoice('scissors')">
                        <div class="icon">✌️</div>
                        <div>Scissors</div>
                    </button>
                </div>
                
                <div class="game-controls">
                    <button class="control-btn" id="resetBtn" onclick="resetGame()">
                        <i class="fas fa-redo"></i> New Game
                    </button>
                </div>
            </div>

            <!-- Fairness & Verification -->
            <div class="fairness-panel">
                <h3 class="panel-title">Provably Fair System</h3>
                
                <div class="fairness-info">
                    <h4 style="color: white; margin-bottom: 1rem; font-size: 1rem;">Current Round Seeds</h4>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--rps-blue);">Client Seed:</strong>
                        <div class="seed-display" id="clientSeed">--</div>
                    </div>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--rps-purple);">Server Seed (Hashed):</strong>
                        <div class="seed-display" id="hashedServerSeed">--</div>
                    </div>
                    
                    <div>
                        <strong style="color: var(--rps-green);">Nonce:</strong>
                        <div class="seed-display" id="nonce">0</div>
                    </div>
                </div>

                <div class="verification-tool">
                    <h4 style="color: white; margin-bottom: 1rem; font-size: 1rem;">Verify Past Round</h4>
                    
                    <input type="text" class="verify-input" id="verifyClientSeed" placeholder="Enter client seed">
                    <input type="text" class="verify-input" id="verifyServerSeed" placeholder="Enter revealed server seed">
                    <input type="number" class="verify-input" id="verifyNonce" placeholder="Enter nonce">
                    
                    <button class="verify-btn" onclick="verifyResult()">
                        <i class="fas fa-check-circle"></i> Verify Result
                    </button>
                    
                    <div id="verificationResult" style="margin-top: 1rem; padding: 1rem; background: rgba(0,0,0,0.2); border-radius: 8px; display: none;">
                        <!-- Verification result will be displayed here -->
                    </div>
                </div>

                <div class="history-container">
                    <h4 style="color: white; margin-bottom: 1rem; font-size: 1rem;">Round History</h4>
                    <div id="roundHistory">
                        <!-- Round history will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script src="assets/js/script.js"></script>    <script>
        // Game State
        let gameState = {
            playerScore: 0,
            opponentScore: 0,
            totalGames: 0,
            gamesWon: 0,
            roundsPlayed: 0,
            playerChoice: null,
            opponentChoice: null,
            gameActive: true,
            clientSeed: '',
            serverSeed: '',
            hashedServerSeed: '',
            nonce: 0,
            roundHistory: [],
            revealedSeeds: {}, // Map of nonce to revealed server seed
            balance: 1000, // Player's starting balance in GA currency
            currentBet: 50, // Default bet amount
            betMultiplier: 2 // Standard payout multiplier
        };

        // Constants for game
        const CHOICES = ['rock', 'paper', 'scissors'];
        const ICONS = {
            'rock': '✊',
            'paper': '✋',
            'scissors': '✌️',
            'unknown': '<i class="fas fa-question"></i>'
        };
        const OUTCOMES = {
            WIN: 'win',
            LOSE: 'lose',
            TIE: 'tie'
        };
        const BEST_OF = 5; // First to 3 wins
        const WIN_THRESHOLD = Math.ceil(BEST_OF / 2);

        // Initialize the game
        document.addEventListener('DOMContentLoaded', function() {
            generateNewSeeds();
            updateDisplay();
            populateRoundHistory();
            updateBetDisplay();
        });

        // Generate new cryptographic seeds
        function generateNewSeeds() {
            // Generate client seed (random string)
            gameState.clientSeed = generateRandomString(32);
            
            // Generate server seed and hash it
            gameState.serverSeed = generateRandomString(32);
            gameState.hashedServerSeed = sha256(gameState.serverSeed);
            
            // Update display
            document.getElementById('clientSeed').textContent = gameState.clientSeed;
            document.getElementById('hashedServerSeed').textContent = gameState.hashedServerSeed;
            document.getElementById('nonce').textContent = gameState.nonce;
        }

        // Generate random string for seeds
        function generateRandomString(length) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }

        // Simple SHA-256 implementation (for demo purposes)
        function sha256(ascii) {
            function rightRotate(value, amount) {
                return (value >>> amount) | (value << (32 - amount));
            }
            
            let mathPow = Math.pow;
            let maxWord = mathPow(2, 32);
            let lengthProperty = 'length';
            let i, j;
            let result = '';

            let words = [];
            let asciiBitLength = ascii[lengthProperty] * 8;
            
            let hash = sha256.h = sha256.h || [];
            let k = sha256.k = sha256.k || [];
            let primeCounter = k[lengthProperty];

            let isComposite = {};
            for (let candidate = 2; primeCounter < 64; candidate++) {
                if (!isComposite[candidate]) {
                    for (i = 0; i < 313; i += candidate) {
                        isComposite[i] = candidate;
                    }
                    hash[primeCounter] = (mathPow(candidate, .5) * maxWord) | 0;
                    k[primeCounter++] = (mathPow(candidate, 1/3) * maxWord) | 0;
                }
            }
            
            ascii += '\x80';
            while (ascii[lengthProperty] % 64 - 56) ascii += '\x00';
            for (i = 0; i < ascii[lengthProperty]; i++) {
                j = ascii.charCodeAt(i);
                if (j >> 8) return;
                words[i >> 2] |= j << ((3 - i) % 4) * 8;
            }
            words[words[lengthProperty]] = ((asciiBitLength / maxWord) | 0);
            words[words[lengthProperty]] = (asciiBitLength);
            
            for (j = 0; j < words[lengthProperty];) {
                let w = words.slice(j, j += 16);
                let oldHash = hash;
                hash = hash.slice(0, 8);
                
                for (i = 0; i < 64; i++) {
                    let w15 = w[i - 15], w2 = w[i - 2];
                    let a = hash[0], e = hash[4];
                    let temp1 = hash[7]
                        + (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25))
                        + ((e & hash[5]) ^ ((~e) & hash[6]))
                        + k[i]
                        + (w[i] = (i < 16) ? w[i] : (
                            w[i - 16]
                            + (rightRotate(w15, 7) ^ rightRotate(w15, 18) ^ (w15 >>> 3))
                            + w[i - 7]
                            + (rightRotate(w2, 17) ^ rightRotate(w2, 19) ^ (w2 >>> 10))
                        ) | 0
                    );
                    let temp2 = (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22))
                        + ((a & hash[1]) ^ (a & hash[2]) ^ (hash[1] & hash[2]));
                    
                    hash = [(temp1 + temp2) | 0].concat(hash);
                    hash[4] = (hash[4] + temp1) | 0;
                }
                
                for (i = 0; i < 8; i++) {
                    hash[i] = (hash[i] + oldHash[i]) | 0;
                }
            }
            
            for (i = 0; i < 8; i++) {
                for (j = 3; j + 1; j--) {
                    let b = (hash[i] >> (j * 8)) & 255;
                    result += ((b < 16) ? 0 : '') + b.toString(16);
                }
            }
            return result;
        }

        // Player makes a choice
        function makeChoice(choice) {
            if (!gameState.gameActive) {
                showNotification("Please start a new game", "info");
                return;
            }
            
            // Check if player has enough balance to place the bet
            if (gameState.balance < gameState.currentBet) {
                showNotification("Insufficient balance to place bet", "error");
                return;
            }
            
            // Deduct bet amount from balance
            gameState.balance -= gameState.currentBet;
            updateDisplay();
            
            // Set player choice
            gameState.playerChoice = choice;
            
            // Calculate opponent choice using provably fair algorithm
            const opponentChoice = calculateOpponentChoice();
            gameState.opponentChoice = opponentChoice;
            
            // Update visuals
            updateHandDisplays();
            
            // Determine winner and update scores
            const outcome = determineOutcome(gameState.playerChoice, gameState.opponentChoice);
            updateScores(outcome);
            
            // Add to round history with bet information
            gameState.roundHistory.unshift({
                nonce: gameState.nonce,
                playerChoice: gameState.playerChoice,
                opponentChoice: gameState.opponentChoice,
                outcome: outcome,
                betAmount: gameState.currentBet,
                winAmount: outcome === OUTCOMES.WIN ? gameState.currentBet * gameState.betMultiplier : 0,
                clientSeed: gameState.clientSeed,
                hashedServerSeed: gameState.hashedServerSeed,
                revealedServerSeed: gameState.serverSeed,
                timestamp: new Date().toLocaleTimeString()
            });
            
            // Store revealed server seed for verification
            gameState.revealedSeeds[gameState.nonce] = gameState.serverSeed;
            
            // Update round history display
            populateRoundHistory();
            
            // Check if game is over
            if (gameState.playerScore >= WIN_THRESHOLD || gameState.opponentScore >= WIN_THRESHOLD) {
                endGame();
            } else {
                // Prepare for next round
                gameState.nonce++;
                generateNewSeeds();
                
                // Reset choices after a delay
                setTimeout(() => {
                    gameState.playerChoice = null;
                    gameState.opponentChoice = null;
                    updateHandDisplays();
                    updateDisplay();
                }, 2000);
            }
            
            gameState.roundsPlayed++;
            updateDisplay();
        }

        // Calculate opponent's choice using provably fair algorithm
        function calculateOpponentChoice() {
            // Combine seeds and nonce
            const combinedSeed = gameState.clientSeed + gameState.serverSeed + gameState.nonce;
            const hash = sha256(combinedSeed);
            
            // Convert first 8 characters of hash to integer
            const hexSubstring = hash.substring(0, 8);
            const decimal = parseInt(hexSubstring, 16);
            
            // Map to one of three options (rock, paper, scissors)
            // This ensures exactly 1/3 probability for each option
            const index = decimal % 3;
            return CHOICES[index];
        }

        // Update hand displays with current choices
        function updateHandDisplays() {
            const playerHand = document.getElementById('playerHand');
            const opponentHand = document.getElementById('opponentHand');
            
            // Update player hand
            if (gameState.playerChoice) {
                playerHand.innerHTML = ICONS[gameState.playerChoice];
                playerHand.className = 'hand-display';
                playerHand.classList.add(gameState.playerChoice);
            } else {
                playerHand.innerHTML = ICONS['unknown'];
                playerHand.className = 'hand-display';
            }
            
            // Update opponent hand
            if (gameState.opponentChoice) {
                opponentHand.innerHTML = ICONS[gameState.opponentChoice];
                opponentHand.className = 'hand-display';
                opponentHand.classList.add(gameState.opponentChoice);
                
                // Add shake animation
                opponentHand.classList.add('shake-animation');
                setTimeout(() => {
                    opponentHand.classList.remove('shake-animation');
                }, 500);
            } else {
                opponentHand.innerHTML = ICONS['unknown'];
                opponentHand.className = 'hand-display';
            }
        }

        // Determine outcome of the round
        function determineOutcome(playerChoice, opponentChoice) {
            if (playerChoice === opponentChoice) {
                return OUTCOMES.TIE;
            }
            
            if (
                (playerChoice === 'rock' && opponentChoice === 'scissors') ||
                (playerChoice === 'paper' && opponentChoice === 'rock') ||
                (playerChoice === 'scissors' && opponentChoice === 'paper')
            ) {
                return OUTCOMES.WIN;
            }
            
            return OUTCOMES.LOSE;
        }

        // Update scores based on outcome
        function updateScores(outcome) {
            const resultDisplay = document.getElementById('resultDisplay');
            
            switch (outcome) {
                case OUTCOMES.WIN:
                    gameState.playerScore++;
                    resultDisplay.style.color = 'var(--win-color)';
                    // Calculate winnings
                    const winAmount = gameState.currentBet * gameState.betMultiplier;
                    gameState.balance += winAmount;
                    resultDisplay.innerHTML = `<i class="fas fa-trophy"></i> You Win This Round! +${winAmount} GA`;
                    resultDisplay.classList.add('win-animation');
                    playSound('win');
                    break;
                
                case OUTCOMES.LOSE:
                    gameState.opponentScore++;
                    resultDisplay.style.color = 'var(--lose-color)';
                    resultDisplay.innerHTML = `<i class="fas fa-times-circle"></i> AI Wins This Round. -${gameState.currentBet} GA`;
                    playSound('lose');
                    break;
                
                case OUTCOMES.TIE:
                    resultDisplay.style.color = 'var(--tie-color)';
                    // Return the bet amount on a tie
                    gameState.balance += gameState.currentBet;
                    resultDisplay.innerHTML = '<i class="fas fa-equals"></i> Tie Round! Bet Returned';
                    playSound('tie');
                    break;
            }
            
            // Remove animation class after animation completes
            setTimeout(() => {
                resultDisplay.classList.remove('win-animation');
            }, 1000);
            
            // Update score display
            document.getElementById('playerScore').textContent = gameState.playerScore;
            document.getElementById('opponentScore').textContent = gameState.opponentScore;
            updateDisplay();
        }

        // End the current game
        function endGame() {
            gameState.gameActive = false;
            const resultDisplay = document.getElementById('resultDisplay');
            
            if (gameState.playerScore > gameState.opponentScore) {
                resultDisplay.style.color = 'var(--win-color)';
                // Award game victory bonus (50% of current bet)
                const bonus = Math.round(gameState.currentBet * 0.5);
                gameState.balance += bonus;
                resultDisplay.innerHTML = `<i class="fas fa-crown"></i> VICTORY! You Won The Game! +${bonus} GA Bonus`;
                gameState.gamesWon++;
                playSound('gameWin');
                showNotification(`Victory bonus: +${bonus} GA!`, 'success');
            } else {
                resultDisplay.style.color = 'var(--lose-color)';
                resultDisplay.innerHTML = '<i class="fas fa-skull"></i> DEFEAT! AI Won The Game';
                playSound('gameLose');
            }
            
            gameState.totalGames++;
            updateDisplay();
            
            // Highlight the New Game button
            const resetBtn = document.getElementById('resetBtn');
            resetBtn.style.animation = 'pulse 1s infinite';
        }

        // Reset game for a new match
        function resetGame() {
            gameState.playerScore = 0;
            gameState.opponentScore = 0;
            gameState.playerChoice = null;
            gameState.opponentChoice = null;
            gameState.gameActive = true;
            gameState.nonce++;
            
            // Generate new seeds
            generateNewSeeds();
            
            // Reset displays
            document.getElementById('playerScore').textContent = 0;
            document.getElementById('opponentScore').textContent = 0;
            
            const resultDisplay = document.getElementById('resultDisplay');
            resultDisplay.style.color = 'white';
            resultDisplay.innerHTML = 'Make your choice to start the game!';
            
            updateHandDisplays();
            updateDisplay();
            
            // Remove animation from reset button
            const resetBtn = document.getElementById('resetBtn');
            resetBtn.style.animation = '';
            
            playSound('newGame');
            
            // If player is broke, give them a small amount to continue
            if (gameState.balance < gameState.currentBet) {
                const refill = Math.max(100, gameState.currentBet * 2);
                gameState.balance += refill;
                showNotification(`Balance refilled with ${refill} GA`, 'info');
                updateDisplay();
            }
        }

        // Verify a previous round result
        function verifyResult() {
            const clientSeed = document.getElementById('verifyClientSeed').value;
            const serverSeed = document.getElementById('verifyServerSeed').value;
            const nonce = parseInt(document.getElementById('verifyNonce').value);

            if (!clientSeed || !serverSeed || isNaN(nonce)) {
                showNotification('Please fill in all verification fields', 'error');
                return;
            }

            // Verify server seed hash matches the stored hash
            const storedHash = gameState.roundHistory.find(round => round.nonce === nonce)?.hashedServerSeed;
            const calculatedHash = sha256(serverSeed);
            
            if (storedHash && calculatedHash !== storedHash) {
                showVerificationResult(false, 'Server seed hash does not match the stored hash');
                return;
            }

            // Recreate the calculation
            const combinedSeed = clientSeed + serverSeed + nonce;
            const hash = sha256(combinedSeed);
            const hexSubstring = hash.substring(0, 8);
            const decimal = parseInt(hexSubstring, 16);
            const opponentChoice = CHOICES[decimal % 3];

            // Get the actual recorded choices for this round
            const historyEntry = gameState.roundHistory.find(round => round.nonce === nonce);
            
            if (!historyEntry) {
                showVerificationResult(false, 'No round found with this nonce');
                return;
            }

            const verified = opponentChoice === historyEntry.opponentChoice;

            // Display verification result
            showVerificationResult(verified, verified ? 
                `Verification successful! AI's choice (${opponentChoice}) matches the recorded choice.` : 
                `Verification failed! Calculated choice (${opponentChoice}) does not match recorded choice (${historyEntry.opponentChoice}).`
            );
        }

        // Show verification result
        function showVerificationResult(success, message) {
            const verificationResult = document.getElementById('verificationResult');
            verificationResult.style.display = 'block';
            verificationResult.style.borderLeft = success ? 
                '4px solid var(--win-color)' : '4px solid var(--lose-color)';
            
            verificationResult.innerHTML = `
                <h5 style="color: ${success ? 'var(--win-color)' : 'var(--lose-color)'}; margin-bottom: 1rem;">
                    ${success ? '✅ Verification Successful' : '❌ Verification Failed'}
                </h5>
                <p>${message}</p>
            `;
        }

        // Populate round history display
        function populateRoundHistory() {
            const historyContainer = document.getElementById('roundHistory');
            historyContainer.innerHTML = '';

            gameState.roundHistory.slice(0, 10).forEach(round => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                
                let outcomeIcon, outcomeColor, betResult;
                switch (round.outcome) {
                    case OUTCOMES.WIN:
                        outcomeIcon = '<i class="fas fa-trophy"></i>';
                        outcomeColor = 'var(--win-color)';
                        betResult = `+${round.winAmount} GA`;
                        break;
                    case OUTCOMES.LOSE:
                        outcomeIcon = '<i class="fas fa-times-circle"></i>';
                        outcomeColor = 'var(--lose-color)';
                        betResult = `-${round.betAmount} GA`;
                        break;
                    case OUTCOMES.TIE:
                        outcomeIcon = '<i class="fas fa-equals"></i>';
                        outcomeColor = 'var(--tie-color)';
                        betResult = `Bet Returned`;
                        break;
                }
                
                historyItem.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <strong>Round #${round.nonce}</strong>
                        <span style="color: ${outcomeColor};">${outcomeIcon} ${round.outcome.toUpperCase()}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>You: ${ICONS[round.playerChoice]} ${round.playerChoice}</span>
                        <span>AI: ${ICONS[round.opponentChoice]} ${round.opponentChoice}</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                        <span>Bet: ${round.betAmount} GA</span>
                        <span style="color: ${outcomeColor};">${betResult}</span>
                    </div>
                    <div style="font-size: 0.8rem; color: rgba(255,255,255,0.6);">
                        <button class="rules-toggle" style="font-size: 0.8rem; margin-top: 0.5rem;" 
                                onclick="revealSeed(${round.nonce})">
                            Reveal Server Seed
                        </button>
                        <div id="seed-${round.nonce}" style="display: none; margin-top: 0.5rem;">
                            <div class="seed-display" style="font-size: 0.7rem;">
                                ${round.revealedServerSeed}
                            </div>
                        </div>
                    </div>
                `;
                historyContainer.appendChild(historyItem);
            });
        }

        // Reveal server seed for a round
        function revealSeed(nonce) {
            const seedElement = document.getElementById(`seed-${nonce}`);
            if (seedElement) {
                seedElement.style.display = seedElement.style.display === 'none' ? 'block' : 'none';
            }
        }

        // Toggle rules display
        function toggleRules() {
            const rulesContent = document.getElementById('rulesContent');
            if (rulesContent.style.display === 'none') {
                rulesContent.style.display = 'block';
            } else {
                rulesContent.style.display = 'none';
            }
        }

        // Update all display elements
        function updateDisplay() {
            document.getElementById('totalGames').textContent = gameState.totalGames;
            document.getElementById('gamesWon').textContent = gameState.gamesWon;
            
            const winRate = gameState.totalGames > 0 ? 
                Math.round(gameState.gamesWon / gameState.totalGames * 100) : 0;
            document.getElementById('winRate').textContent = winRate + '%';
            
            document.getElementById('nonce').textContent = gameState.nonce;
            
            // Update currency display
            document.getElementById('playerBalance').textContent = `${gameState.balance} GA`;
            document.getElementById('betAmount').textContent = `${gameState.currentBet} GA`;
            document.getElementById('potentialWin').textContent = `${Math.round(gameState.currentBet * gameState.betMultiplier)} GA`;
        }
        
        // Adjust bet amount
        function adjustBet(amount) {
            const newBet = gameState.currentBet + amount;
            // Ensure bet is at least 10 and at most the player's balance
            if (newBet >= 10 && newBet <= gameState.balance) {
                gameState.currentBet = newBet;
                updateBetDisplay();
            } else if (newBet < 10) {
                showNotification("Minimum bet is 10 GA", "info");
            } else {
                showNotification("Bet cannot exceed your balance", "info");
            }
        }
        
        // Update bet display
        function updateBetDisplay() {
            document.getElementById('betAmount').textContent = `${gameState.currentBet} GA`;
            document.getElementById('potentialWin').textContent = `${Math.round(gameState.currentBet * gameState.betMultiplier)} GA`;
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            
            // Set color based on type
            if (type === 'error') {
                notification.style.background = 'var(--lose-color)';
            } else if (type === 'success') {
                notification.style.background = 'var(--win-color)';
            } else {
                notification.style.background = 'var(--rps-blue)';
            }
            
            notification.className = 'notification show';
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Play sound effects
        function playSound(type) {
            // Simple beep sounds using Web Audio API
            const context = new (window.AudioContext || window.webkitAudioContext)();
            
            let oscillator = context.createOscillator();
            let gainNode = context.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(context.destination);
            
            switch (type) {
                case 'win':
                    oscillator.frequency.setValueAtTime(880, context.currentTime);
                    gainNode.gain.setValueAtTime(0.3, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    
                    setTimeout(() => {
                        let osc2 = context.createOscillator();
                        osc2.connect(gainNode);
                        osc2.frequency.setValueAtTime(1320, context.currentTime);
                        osc2.start(context.currentTime);
                        osc2.stop(context.currentTime + 0.1);
                    }, 150);
                    break;
                    
                case 'lose':
                    oscillator.frequency.setValueAtTime(440, context.currentTime);
                    gainNode.gain.setValueAtTime(0.3, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.2);
                    
                    setTimeout(() => {
                        let osc2 = context.createOscillator();
                        osc2.connect(gainNode);
                        osc2.frequency.setValueAtTime(330, context.currentTime);
                        osc2.start(context.currentTime);
                        osc2.stop(context.currentTime + 0.2);
                    }, 250);
                    break;
                    
                case 'tie':
                    oscillator.frequency.setValueAtTime(660, context.currentTime);
                    gainNode.gain.setValueAtTime(0.2, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    break;
                    
                case 'gameWin':
                    playArpeggio(context, [440, 554, 659, 880], 0.1, 0.2);
                    break;
                    
                case 'gameLose':
                    playArpeggio(context, [440, 392, 349, 330], 0.1, 0.2);
                    break;
                    
                case 'newGame':
                    oscillator.frequency.setValueAtTime(523, context.currentTime);
                    gainNode.gain.setValueAtTime(0.2, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    break;
            }
        }

        // Helper function to play an arpeggio
        function playArpeggio(context, frequencies, duration, spacing) {
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    let osc = context.createOscillator();
                    let gain = context.createGain();
                    osc.connect(gain);
                    gain.connect(context.destination);
                    
                    osc.frequency.setValueAtTime(freq, context.currentTime);
                    gain.gain.setValueAtTime(0.2, context.currentTime);
                    gain.gain.exponentialRampToValueAtTime(0.01, context.currentTime + duration);
                    
                    osc.start(context.currentTime);
                    osc.stop(context.currentTime + duration);
                }, index * spacing * 1000);
            });
        }
    </script>
</body>
</html>