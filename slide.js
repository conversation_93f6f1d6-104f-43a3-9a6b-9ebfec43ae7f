// Cosmic Ascent - Enhanced Mobile & Pro View Implementation
// Following YOUWARE platform standards

class CosmicAscent {
    constructor() {
        // Game state management
        this.gameState = 'waiting'; // waiting, betting, playing, crashed
        this.multiplier = 1.0;
        this.crashPoint = 0;
        this.roundStartTime = 0;
        this.lastUpdateTime = 0;
        this.animationFrameId = null;
        this.currentRound = 0;
        this.betPlaced = false;
        this.cashedOut = false;
        this.autoCashOutActive = false;
        this.autoCashOutValue = 2.0;
        
        // Player data
        this.gaBalance = 10000;
        this.currentBet = 0;
        
        // UI state
        this.viewMode = 'standard', // 'standard' or 'pro'
        
        // Game statistics
        this.gameStats = {
            totalRounds: 0,
            wins: 0,
            losses: 0,
            totalProfit: 0,
            totalBetAmount: 0,
            biggestWin: 0,
            longestWinStreak: 0,
            currentWinStreak: 0,
            avgCashOutMultiplier: 0,
            avgCrashMultiplier: 0
        };
        
        // Pro view analytics
        this.analytics = {
            crashPatterns: {},
            timingData: [],
            riskAssessment: {
                currentRisk: 'medium',
                recommendedMultiplier: 2.0,
                confidence: 85
            },
            sessionData: {
                startTime: Date.now(),
                peakMultiplier: 1.0,
                totalPlayTime: 0,
                cashoutsAtMultiplier: {}
            }
        };
        
        // Game constants
        this.BETTING_PHASE_DURATION = 10000; // 10 seconds
        this.RESULT_DISPLAY_DURATION = 3000; // 3 seconds
        this.MIN_CRASH_VALUE = 1.01;
        this.MAX_CRASH_VALUE = 100.0;
        this.GROWTH_FACTOR = 0.00005;
        
        // History and activity
        this.roundHistory = [];
        this.activityFeed = [];
        this.maxHistoryItems = 20;
        this.maxActivityItems = 10;
        
        // Provably fair system
        this.serverSeed = '';
        this.serverSeedHash = '';
        this.clientSeed = this.generateRandomString(16);
        this.nonce = 0;
        
        // Initialize the game
        this.initialize();
    }

    initialize() {
        this.loadGameState();
        this.initializeElements();
        this.createViewModeToggle();
        this.createProViewPanel();
        this.attachEventListeners();
        this.startGameLoop();
        this.generateFakeHistory();
        this.generateFakeActivity();
        this.updateDisplay();
        this.updateProViewStats();
        
        // Auto-save periodically
        setInterval(() => this.saveGameState(), 5000);
    }

    // Create view mode toggle
    createViewModeToggle() {
        if (document.querySelector('.view-mode-toggle')) return;
        
        const toggle = document.createElement('div');
        toggle.className = 'view-mode-toggle';
        toggle.innerHTML = `
            <button class="view-toggle-btn ${this.viewMode === 'standard' ? 'active' : ''}" 
                    data-mode="standard">STD</button>
            <button class="view-toggle-btn ${this.viewMode === 'pro' ? 'active' : ''}" 
                    data-mode="pro">PRO</button>
        `;
        
        document.body.appendChild(toggle);
        
        // Add event listeners
        toggle.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.getAttribute('data-mode');
                this.setViewMode(mode);
            });
        });
    }

    // Create Pro View panel
    createProViewPanel() {
        if (document.querySelector('.pro-view-panel')) return;
        
        const proPanel = document.createElement('div');
        proPanel.className = 'pro-view-panel';
        proPanel.innerHTML = `
            <h3 class="pro-panel-title">
                <i class="fas fa-chart-line"></i> Pro Analytics
            </h3>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Avg Cashout</div>
                    <div class="pro-stat-value" id="proAvgCashout">0.00x</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Win Rate</div>
                    <div class="pro-stat-value" id="proWinRate">0%</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Profit/Loss</div>
                    <div class="pro-stat-value" id="proProfitLoss">0 GA</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Peak Multiplier</div>
                    <div class="pro-stat-value" id="proPeakMultiplier">1.00x</div>
                </div>
            </div>
            
            <div class="timing-analysis">
                <h4><i class="fas fa-clock"></i> Timing Analysis</h4>
                <div class="timing-chart" id="timingChart">
                    <!-- Timing bars will be generated here -->
                </div>
                <div style="margin-top: 0.5rem; font-size: 0.8rem; text-align: center;">
                    <span id="timingRecommendation">Analyzing cashout patterns...</span>
                </div>
            </div>
            
            <div class="crash-patterns" style="background: rgba(255, 255, 255, 0.05); border-radius: 15px; padding: 1rem; margin-top: 1rem; border: 1px solid rgba(255, 255, 255, 0.1);">
                <h4 style="color: var(--success-color); margin-bottom: 1rem; font-size: 0.9rem; text-align: center; text-transform: uppercase; letter-spacing: 0.5px;">
                    <i class="fas fa-eye"></i> Pattern Recognition
                </h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.8rem;">
                    <div>Recent Crashes Below 2x: <span id="lowCrashes">0</span></div>
                    <div>Recent Crashes Above 5x: <span id="highCrashes">0</span></div>
                    <div>Avg Recent Crash: <span id="avgRecentCrash">0.00x</span></div>
                    <div>Recommended Target: <span id="recommendedTarget">2.00x</span></div>
                </div>
            </div>
        `;
        
        // Insert before main game area
        const mainGameArea = document.querySelector('.main-game-area');
        mainGameArea.parentNode.insertBefore(proPanel, mainGameArea);
    }

    // Set view mode
    setViewMode(mode) {
        this.viewMode = mode;
        localStorage.setItem('cosmicAscentViewMode', mode);
        
        // Update toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-mode') === mode) {
                btn.classList.add('active');
            }
        });
        
        // Toggle body class for CSS styling
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
        } else {
            document.body.classList.add('pro-view-active');
            this.updateProViewStats();
        }
        
        this.showNotification(`Switched to ${mode.toUpperCase()} view`, 'info');
    }

    // Load game state from localStorage
    loadGameState() {
        const saved = localStorage.getItem('cosmicAscentGameState');
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                this.gaBalance = parsed.gaBalance || 10000;
                this.gameStats = { ...this.gameStats, ...(parsed.gameStats || {}) };
                this.analytics = { ...this.analytics, ...(parsed.analytics || {}) };
                this.roundHistory = parsed.roundHistory || [];
            } catch (e) {
                console.warn('Failed to load game state:', e);
            }
        }
        
        // Load view mode
        const savedViewMode = localStorage.getItem('cosmicAscentViewMode');
        if (savedViewMode) {
            this.viewMode = savedViewMode;
        }
        
        // Initialize analytics if empty
        if (Object.keys(this.analytics.crashPatterns).length === 0) {
            for (let i = 1; i <= 10; i++) {
                this.analytics.crashPatterns[i] = 0;
            }
            this.analytics.crashPatterns['10+'] = 0;
        }
    }

    // Save game state to localStorage
    saveGameState() {
        try {
            const gameState = {
                gaBalance: this.gaBalance,
                gameStats: this.gameStats,
                analytics: this.analytics,
                roundHistory: this.roundHistory.slice(0, 50)
            };
            localStorage.setItem('cosmicAscentGameState', JSON.stringify(gameState));
        } catch (e) {
            console.warn('Failed to save game state:', e);
        }
    }
    
    initializeElements() {
        // Game canvas
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Set canvas size for mobile
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
        
        // Game status elements
        this.multiplierDisplay = document.getElementById('multiplierValue');
        this.statusText = document.getElementById('statusText');
        this.countdown = document.getElementById('countdown');
        this.crashOverlay = document.getElementById('crashOverlay');
        this.crashMultiplierDisplay = document.getElementById('crashMultiplier');
        this.rocketTrail = document.getElementById('rocketTrail');
        
        // Control elements
        this.stakeInput = document.getElementById('stakeInput');
        this.halfStakeBtn = document.getElementById('halfStakeBtn');
        this.doubleStakeBtn = document.getElementById('doubleStakeBtn');
        this.maxStakeBtn = document.getElementById('maxStakeBtn');
        this.autoCashOutToggle = document.getElementById('autoCashOutToggle');
        this.autoCashOutInput = document.getElementById('autoCashOutInput');
        this.placeBetBtn = document.getElementById('placeBetBtn');
        this.cashOutBtn = document.getElementById('cashOutBtn');
        this.cancelBetBtn = document.getElementById('cancelBetBtn');
        
        // Stats elements
        this.balanceValue = document.getElementById('balanceValue');
        this.currentBetValue = document.getElementById('currentBetValue');
        this.potentialWinValue = document.getElementById('potentialWinValue');
        this.totalRoundsValue = document.getElementById('totalRoundsValue');
        this.winRateValue = document.getElementById('winRateValue');
        this.profitLossValue = document.getElementById('profitLossValue');
        
        // Provably fair elements
        this.serverSeedHashDisplay = document.getElementById('serverSeedHash');
        this.clientSeedInput = document.getElementById('clientSeedInput');
        this.roundNonceDisplay = document.getElementById('roundNonce');
        this.randomizeSeedBtn = document.getElementById('randomizeSeedBtn');
        this.verifyBtn = document.getElementById('verifyBtn');
        
        // History and activity elements
        this.historyList = document.getElementById('historyList');
        this.activityFeedElement = document.getElementById('activityFeed');
        
        // Modal elements
        this.howToPlayModal = document.getElementById('howToPlayModal');
        this.howToPlayBtn = document.getElementById('howToPlayBtn');
        this.closeModalBtn = document.getElementById('closeModalBtn');
        this.fairnessBtn = document.getElementById('fairnessBtn');
        
        // Initialize display values
        this.updateBalance(this.gaBalance);
        this.updateCurrentBet(0);
        this.updatePotentialWin(0);
        this.updateMultiplierDisplay(1.00);
        
        this.clientSeedInput.value = this.clientSeed;
    }

    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        // Set canvas size to match container
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        // Set CSS size to prevent stretching
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
    }
    
    attachEventListeners() {
        // Bet controls
        this.halfStakeBtn.addEventListener('click', () => this.adjustStake(0.5));
        this.doubleStakeBtn.addEventListener('click', () => this.adjustStake(2));
        this.maxStakeBtn.addEventListener('click', () => this.setMaxStake());
        
        this.autoCashOutToggle.addEventListener('change', () => {
            this.autoCashOutInput.disabled = !this.autoCashOutToggle.checked;
            this.autoCashOutActive = this.autoCashOutToggle.checked;
        });
        
        this.placeBetBtn.addEventListener('click', () => this.placeBet());
        this.cashOutBtn.addEventListener('click', () => this.cashOut());
        this.cancelBetBtn.addEventListener('click', () => this.cancelBet());
        
        // Provably fair controls
        this.randomizeSeedBtn.addEventListener('click', () => this.randomizeClientSeed());
        this.verifyBtn.addEventListener('click', () => this.openVerificationModal());
        
        // Modal controls
        this.howToPlayBtn.addEventListener('click', () => this.openHowToPlayModal());
        this.closeModalBtn.addEventListener('click', () => this.closeModal());
        this.fairnessBtn.addEventListener('click', () => this.openFairnessModal());
        
        // Close modal when clicking outside
        this.howToPlayModal.addEventListener('click', (e) => {
            if (e.target === this.howToPlayModal) {
                this.closeModal();
            }
        });
        
        // Client seed input
        this.clientSeedInput.addEventListener('change', () => {
            this.clientSeed = this.clientSeedInput.value;
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && this.gameState === 'betting' && !this.betPlaced) {
                e.preventDefault();
                this.placeBet();
            } else if (e.code === 'Space' && this.gameState === 'playing' && this.betPlaced && !this.cashedOut) {
                e.preventDefault();
                this.cashOut();
            } else if (e.code === 'KeyP') {
                this.setViewMode(this.viewMode === 'standard' ? 'pro' : 'standard');
            }
        });

        // Touch events for mobile optimization
        this.attachTouchEvents();
    }

    // Attach touch events for mobile optimization
    attachTouchEvents() {
        let touchStartTime = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        });
        
        document.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            
            // Long press detection for quick cash out
            if (touchDuration > 500) {
                const target = e.target.closest('.game-canvas-container');
                if (target && this.gameState === 'playing' && this.betPlaced && !this.cashedOut) {
                    this.cashOut();
                    this.showNotification('Quick cash out activated!', 'success');
                }
            }
        });
        
        // Prevent double-tap zoom on game elements
        document.querySelectorAll('.btn, .game-canvas-container').forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
    }
    
    startGameLoop() {
        this.generateNewServerSeed();
        this.gameLoop();
    }
    
    gameLoop() {
        const now = Date.now();
        
        switch (this.gameState) {
            case 'waiting':
                this.startBettingPhase();
                break;
                
            case 'betting':
                const bettingElapsed = now - this.roundStartTime;
                const bettingRemaining = this.BETTING_PHASE_DURATION - bettingElapsed;
                
                this.placeBetBtn.classList.remove('hidden');
                this.placeBetBtn.disabled = false;
                
                if (bettingRemaining <= 0) {
                    this.startPlayingPhase();
                } else {
                    const seconds = Math.ceil(bettingRemaining / 1000);
                    this.countdown.textContent = seconds;
                }
                break;
                
            case 'playing':
                const playingElapsed = now - this.roundStartTime;
                this.updateGameMultiplier(playingElapsed);
                
                // Update peak multiplier for analytics
                if (this.multiplier > this.analytics.sessionData.peakMultiplier) {
                    this.analytics.sessionData.peakMultiplier = this.multiplier;
                }
                
                if (this.multiplier >= this.crashPoint) {
                    this.triggerCrash();
                }
                
                if (this.betPlaced && this.autoCashOutActive && !this.cashedOut) {
                    if (this.multiplier >= this.autoCashOutValue) {
                        this.cashOut();
                    }
                }
                break;
                
            case 'crashed':
                const crashedElapsed = now - this.roundStartTime;
                
                if (crashedElapsed > this.RESULT_DISPLAY_DURATION) {
                    this.resetRound();
                }
                break;
        }
        
        this.drawGame();
        this.animationFrameId = requestAnimationFrame(() => this.gameLoop());
    }
    
    startBettingPhase() {
        this.gameState = 'betting';
        this.roundStartTime = Date.now();
        this.currentRound++;
        this.nonce = this.currentRound;
        
        this.roundNonceDisplay.textContent = this.nonce;
        this.multiplier = 1.0;
        this.updateMultiplierDisplay(this.multiplier);
        this.statusText.textContent = 'Placing bets...';
        
        this.placeBetBtn.classList.remove('hidden');
        this.cashOutBtn.classList.add('hidden');
        this.cancelBetBtn.classList.add('hidden');
        
        this.betPlaced = false;
        this.cashedOut = false;
        
        this.crashOverlay.classList.add('hidden');
        
        this.stakeInput.disabled = false;
        this.autoCashOutToggle.disabled = false;
        this.autoCashOutInput.disabled = !this.autoCashOutToggle.checked;
        
        this.placeBetBtn.disabled = false;
        
        this.crashPoint = this.generateCrashPoint();
        
        // Update analytics
        this.updateAnalytics();
    }
    
    startPlayingPhase() {
        this.gameState = 'playing';
        this.roundStartTime = Date.now();
        this.statusText.textContent = 'Launching...';
        
        if (this.betPlaced) {
            this.placeBetBtn.classList.add('hidden');
            this.cashOutBtn.classList.remove('hidden');
            this.cashOutBtn.disabled = false;
            this.cashOutBtn.textContent = 'Cash Out';
            this.cancelBetBtn.classList.add('hidden');
            
            this.autoCashOutToggle.disabled = true;
            this.autoCashOutInput.disabled = true;
        } else {
            this.placeBetBtn.classList.add('hidden');
            this.cashOutBtn.classList.add('hidden');
            this.cancelBetBtn.classList.add('hidden');
        }
        
        this.stakeInput.disabled = true;
        this.generateNewServerSeed();
        
        // Haptic feedback for game start
        if (navigator.vibrate) {
            navigator.vibrate([100, 50, 100]);
        }
    }
    
    triggerCrash() {
        this.gameState = 'crashed';
        this.roundStartTime = Date.now();
        this.statusText.textContent = 'Crashed!';
        
        this.crashOverlay.classList.remove('hidden');
        this.crashMultiplierDisplay.textContent = this.multiplier.toFixed(2) + '×';
        
        this.cashOutBtn.disabled = true;
        this.cashOutBtn.classList.add('hidden');
        
        this.addToHistory({
            multiplier: this.multiplier,
            crashPoint: this.crashPoint,
            serverSeed: this.serverSeed,
            clientSeed: this.clientSeed,
            nonce: this.nonce
        });
        
        if (this.betPlaced && !this.cashedOut) {
            this.gameStats.totalRounds++;
            this.gameStats.losses++;
            this.gameStats.totalProfit -= this.currentBet;
            this.gameStats.totalBetAmount += this.currentBet;
            this.gameStats.currentWinStreak = 0;
            
            this.addToActivityFeed({
                type: 'lose',
                player: 'You',
                bet: this.currentBet,
                multiplier: this.crashPoint
            });
            
            this.updateStats();
            
            // Strong haptic feedback for loss
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200, 100, 300]);
            }
        }
        
        // Update crash pattern analytics
        this.updateCrashPatterns(this.crashPoint);
        this.revealServerSeed();
    }
    
    resetRound() {
        this.gameState = 'waiting';
        
        this.updateMultiplierDisplay(1.00);
        this.updateCurrentBet(0);
        this.updatePotentialWin(0);
        
        this.rocketTrail.style.height = '0';
        
        this.betPlaced = false;
        this.cashedOut = false;
        
        this.stakeInput.disabled = false;
        this.autoCashOutToggle.disabled = false;
        this.autoCashOutInput.disabled = !this.autoCashOutToggle.checked;
        
        this.placeBetBtn.disabled = false;
        this.placeBetBtn.classList.remove('hidden');
        this.cashOutBtn.classList.add('hidden');
        this.cancelBetBtn.classList.add('hidden');
    }
    
    updateGameMultiplier(elapsed) {
        const elapsedSeconds = elapsed / 1000;
        this.multiplier = 1 + Math.pow(Math.E, this.GROWTH_FACTOR * elapsedSeconds * 1000);
        this.multiplier = Math.min(this.multiplier, this.MAX_CRASH_VALUE);
        
        this.updateMultiplierDisplay(this.multiplier);
        
        if (this.betPlaced && !this.cashedOut) {
            this.updatePotentialWin(this.currentBet * this.multiplier);
        }
    }
    
    drawGame() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (this.gameState === 'playing' || this.gameState === 'crashed') {
            this.drawStars();
            this.drawRocketTrajectory();
            
            if (this.gameState === 'playing') {
                this.drawRocket();
                this.updateRocketTrail();
            } else if (this.gameState === 'crashed') {
                this.drawExplosion();
            }
        }
    }
    
    drawStars() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // Use deterministic star positions
        const starSeed = this.nonce || 1;
        let seed = starSeed * 9999;
        
        const random = () => {
            seed = (seed * 9301 + 49297) % 233280;
            return seed / 233280;
        };
        
        for (let i = 0; i < 50; i++) {
            const x = random() * this.canvas.width;
            const y = random() * this.canvas.height;
            const size = random() * 1.5 + 0.5;
            
            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }
    
    drawRocketTrajectory() {
        const height = this.canvas.height;
        const width = this.canvas.width;
        
        // Draw grid lines
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        // Horizontal grid lines
        for (let y = height - 30; y > 30; y -= 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(30, y);
            this.ctx.lineTo(width - 30, y);
            this.ctx.stroke();
        }
        
        // Vertical grid lines
        for (let x = 30; x < width; x += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 30);
            this.ctx.lineTo(x, height - 30);
            this.ctx.stroke();
        }
        
        // Draw multiplier labels
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        this.ctx.font = '10px Arial';
        this.ctx.textAlign = 'right';
        
        const multipliers = [1.5, 2, 3, 5, 10];
        multipliers.forEach(mult => {
            const y = this.getYPositionFromMultiplier(mult);
            if (y > 20 && y < height - 20) {
                this.ctx.fillText(mult.toFixed(1) + 'x', 25, y + 3);
            }
        });
    }
    
    drawRocket() {
        const x = this.canvas.width / 2;
        const y = this.getYPositionFromMultiplier(this.multiplier);
        
        this.ctx.save();
        this.ctx.translate(x, y);
        
        // Add oscillation for mobile visual appeal
        const oscillation = Math.sin(Date.now() / 200) * 2;
        this.ctx.rotate(Math.PI / 180 * oscillation);
        
        // Rocket body
        this.ctx.fillStyle = '#ff4081';
        this.ctx.beginPath();
        this.ctx.moveTo(0, -15);
        this.ctx.lineTo(8, 8);
        this.ctx.lineTo(-8, 8);
        this.ctx.closePath();
        this.ctx.fill();
        
        // Rocket window
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        this.ctx.beginPath();
        this.ctx.arc(0, -2, 3, 0, Math.PI * 2);
        this.ctx.fill();
        
        // Rocket flame
        const flameSize = Math.random() * 4 + 8;
        this.ctx.fillStyle = 'orange';
        this.ctx.beginPath();
        this.ctx.moveTo(-6, 8);
        this.ctx.lineTo(6, 8);
        this.ctx.lineTo(0, 8 + flameSize);
        this.ctx.closePath();
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    drawExplosion() {
        const x = this.canvas.width / 2;
        const y = this.getYPositionFromMultiplier(this.crashPoint);
        
        this.ctx.save();
        this.ctx.translate(x, y);
        
        // Draw explosion rays
        this.ctx.strokeStyle = '#ff4081';
        this.ctx.lineWidth = 2;
        
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const length = Math.random() * 15 + 15;
            
            this.ctx.beginPath();
            this.ctx.moveTo(0, 0);
            this.ctx.lineTo(Math.cos(angle) * length, Math.sin(angle) * length);
            this.ctx.stroke();
        }
        
        // Draw explosion center
        this.ctx.fillStyle = '#ff4081';
        this.ctx.beginPath();
        this.ctx.arc(0, 0, 10, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.restore();
    }
    
    updateRocketTrail() {
        const heightPercentage = Math.min(((this.multiplier - 1) / 4) * 100, 80);
        this.rocketTrail.style.height = `${heightPercentage}%`;
    }
    
    getYPositionFromMultiplier(multiplier) {
        const height = this.canvas.height;
        const logMultiplier = Math.log(multiplier) / Math.log(1.5);
        return height - 30 - (logMultiplier * 40);
    }
    
    // Player actions
    placeBet() {
        if (this.gameState !== 'betting') return;
        
        const stakeAmount = parseInt(this.stakeInput.value);
        
        if (isNaN(stakeAmount) || stakeAmount <= 0) {
            this.showNotification('Please enter a valid GA amount', 'error');
            return;
        }
        
        if (stakeAmount > this.gaBalance) {
            this.showNotification('Insufficient GA balance', 'error');
            return;
        }
        
        this.betPlaced = true;
        this.cashedOut = false;
        
        if (this.autoCashOutToggle.checked) {
            this.autoCashOutActive = true;
            this.autoCashOutValue = parseFloat(this.autoCashOutInput.value);
        } else {
            this.autoCashOutActive = false;
        }
        
        this.updateBalance(this.gaBalance - stakeAmount);
        this.updateCurrentBet(stakeAmount);
        
        this.placeBetBtn.classList.add('hidden');
        this.cashOutBtn.classList.remove('hidden');
        this.cancelBetBtn.classList.remove('hidden');
        
        this.addToActivityFeed({
            type: 'bet',
            player: 'You',
            bet: stakeAmount
        });
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
        
        this.showNotification(`Bet placed: ${stakeAmount} GA`, 'success');
    }
    
    cashOut() {
        if (!this.betPlaced || this.cashedOut || this.gameState !== 'playing') return;
        
        this.cashedOut = true;
        const winnings = Math.floor(this.currentBet * this.multiplier);
        
        this.updateBalance(this.gaBalance + winnings);
        this.gameStats.totalRounds++;
        this.gameStats.wins++;
        this.gameStats.totalProfit += (winnings - this.currentBet);
        this.gameStats.totalBetAmount += this.currentBet;
        this.gameStats.currentWinStreak++;
        
        if (this.gameStats.currentWinStreak > this.gameStats.longestWinStreak) {
            this.gameStats.longestWinStreak = this.gameStats.currentWinStreak;
        }
        
        const profit = winnings - this.currentBet;
        if (profit > this.gameStats.biggestWin) {
            this.gameStats.biggestWin = profit;
        }
        
        // Update cashout analytics
        const multiplierKey = Math.floor(this.multiplier * 100) / 100;
        this.analytics.sessionData.cashoutsAtMultiplier[multiplierKey] = 
            (this.analytics.sessionData.cashoutsAtMultiplier[multiplierKey] || 0) + 1;
        
        this.updateStats();
        this.updateProViewStats();
        
        this.cashOutBtn.disabled = true;
        this.cashOutBtn.textContent = 'Cashed Out!';
        
        this.addToActivityFeed({
            type: 'win',
            player: 'You',
            bet: this.currentBet,
            multiplier: this.multiplier,
            winnings: winnings
        });
        
        // Celebration haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate([100, 50, 100, 50, 200]);
        }
        
        this.showNotification(`🎉 Cashed out at ${this.multiplier.toFixed(2)}x! Won ${winnings} GA`, 'success');
        this.generateFakeCashOuts(1);
    }
    
    cancelBet() {
        if (!this.betPlaced || this.gameState !== 'betting') return;
        
        this.updateBalance(this.gaBalance + this.currentBet);
        this.updateCurrentBet(0);
        
        this.betPlaced = false;
        this.cashedOut = false;
        
        this.placeBetBtn.classList.remove('hidden');
        this.cashOutBtn.classList.add('hidden');
        this.cancelBetBtn.classList.add('hidden');
        
        this.stakeInput.disabled = false;
        this.autoCashOutToggle.disabled = false;
        this.autoCashOutInput.disabled = !this.autoCashOutToggle.checked;
        
        this.showNotification('Bet cancelled', 'info');
    }
    
    // UI Updates
    updateMultiplierDisplay(value) {
        this.multiplierDisplay.textContent = value.toFixed(2);
    }
    
    updateBalance(value) {
        this.gaBalance = value;
        this.balanceValue.textContent = `${value} GA`;
    }
    
    updateCurrentBet(value) {
        this.currentBet = value;
        this.currentBetValue.textContent = `${value} GA`;
        this.updatePotentialWin(value * this.multiplier);
    }
    
    updatePotentialWin(value) {
        this.potentialWinValue.textContent = `${Math.floor(value)} GA`;
    }
    
    updateDisplay() {
        this.updateBalance(this.gaBalance);
        this.updateStats();
    }
    
    updateStats() {
        this.totalRoundsValue.textContent = this.gameStats.totalRounds;
        
        const winRate = this.gameStats.totalRounds > 0 
            ? (this.gameStats.wins / this.gameStats.totalRounds * 100).toFixed(1) 
            : '0.0';
        this.winRateValue.textContent = winRate + '%';
        
        this.profitLossValue.textContent = `${this.gameStats.totalProfit} GA`;
        if (this.gameStats.totalProfit > 0) {
            this.profitLossValue.style.color = 'var(--success-color)';
        } else if (this.gameStats.totalProfit < 0) {
            this.profitLossValue.style.color = 'var(--danger-color)';
        } else {
            this.profitLossValue.style.color = '';
        }
    }

    // Update analytics and Pro View stats
    updateAnalytics() {
        // Calculate timing data
        if (this.roundHistory.length > 5) {
            this.analytics.timingData = this.roundHistory.slice(0, 10).map(round => round.crashPoint);
            this.updateTimingChart();
        }
    }

    updateCrashPatterns(crashPoint) {
        const bucket = crashPoint < 10 ? Math.floor(crashPoint) : '10+';
        this.analytics.crashPatterns[bucket] = (this.analytics.crashPatterns[bucket] || 0) + 1;
    }

    updateProViewStats() {
        if (this.viewMode !== 'pro') return;
        
        // Calculate average cashout multiplier
        const wins = this.gameStats.wins;
        const avgCashout = wins > 0 ? 
            Object.entries(this.analytics.sessionData.cashoutsAtMultiplier)
                .reduce((sum, [mult, count]) => sum + (parseFloat(mult) * count), 0) / wins 
            : 0;
        
        // Calculate win rate
        const winRate = this.gameStats.totalRounds > 0 ? 
            (this.gameStats.wins / this.gameStats.totalRounds * 100) : 0;
        
        // Calculate profit/loss
        const profitLoss = this.gameStats.totalProfit;
        
        // Update pro stats display
        const elements = {
            proAvgCashout: `${avgCashout.toFixed(2)}x`,
            proWinRate: `${winRate.toFixed(1)}%`,
            proProfitLoss: `${profitLoss >= 0 ? '+' : ''}${profitLoss} GA`,
            proPeakMultiplier: `${this.analytics.sessionData.peakMultiplier.toFixed(2)}x`
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                
                // Color profit/loss
                if (id === 'proProfitLoss') {
                    element.style.color = profitLoss >= 0 ? 
                        'var(--success-color)' : 'var(--danger-color)';
                }
            }
        });
        
        this.updateCrashAnalysis();
        this.updateTimingRecommendation();
    }

    updateCrashAnalysis() {
        const recent = this.roundHistory.slice(0, 10);
        const lowCrashes = recent.filter(r => r.crashPoint < 2).length;
        const highCrashes = recent.filter(r => r.crashPoint > 5).length;
        const avgCrash = recent.length > 0 ? 
            recent.reduce((sum, r) => sum + r.crashPoint, 0) / recent.length : 0;
        
        // Calculate recommended target based on recent patterns
        let recommendedTarget = 2.0;
        if (lowCrashes > 6) {
            recommendedTarget = 1.5; // Recent low crashes, play safe
        } else if (highCrashes > 3) {
            recommendedTarget = 3.0; // Recent high crashes, play bolder
        }
        
        // Update display
        const elements = {
            lowCrashes: lowCrashes,
            highCrashes: highCrashes,
            avgRecentCrash: `${avgCrash.toFixed(2)}x`,
            recommendedTarget: `${recommendedTarget.toFixed(2)}x`
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    updateTimingChart() {
        const timingChart = document.getElementById('timingChart');
        if (!timingChart) return;
        
        timingChart.innerHTML = '';
        
        const recentData = this.analytics.timingData.slice(0, 10);
        const maxValue = Math.max(...recentData, 5);
        
        recentData.forEach((value, index) => {
            const bar = document.createElement('div');
            bar.className = 'timing-bar';
            const height = (value / maxValue) * 100;
            bar.style.height = `${height}%`;
            
            if (index === 0) {
                bar.classList.add('active');
            }
            
            timingChart.appendChild(bar);
        });
    }

    updateTimingRecommendation() {
        const element = document.getElementById('timingRecommendation');
        if (!element) return;
        
        const recentWins = this.gameStats.wins;
        const recentLosses = this.gameStats.losses;
        
        let recommendation = 'Play conservatively around 2.0x';
        
        if (recentWins > recentLosses * 2) {
            recommendation = 'You\'re on a roll! Consider 2.5x targets';
        } else if (recentLosses > recentWins * 2) {
            recommendation = 'Play safer with 1.5x targets';
        }
        
        element.textContent = recommendation;
    }
    
    // Stake Adjustments
    adjustStake(factor) {
        const currentValue = parseInt(this.stakeInput.value) || 0;
        const newValue = Math.floor(currentValue * factor);
        this.stakeInput.value = Math.min(Math.max(newValue, 10), this.gaBalance);
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(30);
        }
    }
    
    setMaxStake() {
        this.stakeInput.value = this.gaBalance;
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }
    
    // Provably Fair Functions
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    generateNewServerSeed() {
        this.serverSeed = this.generateRandomString(32);
        this.serverSeedHash = this.hashString(this.serverSeed);
        this.serverSeedHashDisplay.textContent = this.serverSeedHash.substring(0, 16) + '...';
    }
    
    revealServerSeed() {
        console.log(`Server Seed Revealed: ${this.serverSeed}`);
    }
    
    randomizeClientSeed() {
        this.clientSeed = this.generateRandomString(16);
        this.clientSeedInput.value = this.clientSeed;
        this.showNotification('Client seed randomized', 'info');
    }
    
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(8, '0') + this.generateRandomString(8);
    }
    
    generateCrashPoint() {
        const combinedSeed = this.serverSeed + this.clientSeed + this.nonce;
        const hash = this.hashString(combinedSeed);
        
        const randomValue = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
        
        const houseEdge = 0.01;
        let crashPoint = 1 / (randomValue * (1 - houseEdge));
        
        return Math.max(Math.min(crashPoint, this.MAX_CRASH_VALUE), this.MIN_CRASH_VALUE);
    }
    
    // Modal functions
    openHowToPlayModal() {
        this.howToPlayModal.classList.remove('hidden');
    }
    
    openFairnessModal() {
        this.showNotification('Provably Fair: Each crash point is determined by server seed (hashed before betting), your client seed, and round number. Verify after each round!', 'info');
    }
    
    openVerificationModal() {
        this.showNotification('Verification tool: Enter previous round\'s seeds and nonce to verify the crash point was fair and predetermined.', 'info');
    }
    
    closeModal() {
        this.howToPlayModal.classList.add('hidden');
    }

    // Show notification with mobile optimization
    showNotification(message, type = 'info') {
        // Remove existing notification
        const existing = document.querySelector('.notification');
        if (existing) {
            existing.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        
        const colors = {
            error: 'linear-gradient(45deg, #F94144, #E63946)',
            success: 'linear-gradient(45deg, #90BE6D, #0B6E4F)',
            warning: 'linear-gradient(45deg, #F9C74F, #F8961E)',
            info: 'linear-gradient(45deg, #457B9D, #1D3557)'
        };
        
        notification.style.background = colors[type] || colors.info;
        notification.classList.add('show');
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    // Fake data generation for demo purposes
    generateFakeHistory() {
        for (let i = 0; i < 20; i++) {
            let crashPoint;
            const roll = Math.random();
            
            if (roll < 0.7) {
                crashPoint = (Math.random() * 3) + 1;
            } else if (roll < 0.9) {
                crashPoint = (Math.random() * 5) + 3;
            } else {
                crashPoint = (Math.random() * 20) + 8;
            }
            
            this.addToHistory({
                multiplier: crashPoint,
                crashPoint: crashPoint,
                serverSeed: this.generateRandomString(32),
                clientSeed: this.generateRandomString(16),
                nonce: i + 1
            });
        }
    }
    
    generateFakeActivity() {
        const actions = ['bet', 'win', 'lose'];
        
        for (let i = 0; i < 10; i++) {
            const action = actions[Math.floor(Math.random() * actions.length)];
            const bet = Math.floor(Math.random() * 900) + 100;
            const multiplier = (Math.random() * 4) + 1;
            
            this.addToActivityFeed({
                type: action,
                player: this.generateRandomUsername(),
                bet: bet,
                multiplier: action !== 'bet' ? multiplier : null,
                winnings: action === 'win' ? Math.floor(bet * multiplier) : null
            });
        }
    }
    
    generateFakeCashOuts(count) {
        for (let i = 0; i < count; i++) {
            setTimeout(() => {
                if (this.gameState === 'playing') {
                    const bet = Math.floor(Math.random() * 500) + 50;
                    const randomMultiplier = Math.min(this.multiplier * (0.7 + Math.random() * 0.6), this.crashPoint * 0.9);
                    
                    this.addToActivityFeed({
                        type: 'win',
                        player: this.generateRandomUsername(),
                        bet: bet,
                        multiplier: randomMultiplier,
                        winnings: Math.floor(bet * randomMultiplier)
                    });
                }
            }, Math.random() * 2000);
        }
    }
    
    generateRandomUsername() {
        const prefixes = ['Cosmic', 'Star', 'Moon', 'Rocket', 'Astro', 'Galaxy', 'Nebula', 'Meteor'];
        const suffixes = ['Rider', 'Hunter', 'Pilot', 'Explorer', 'Master', 'Player', 'King', 'Queen'];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
        const number = Math.floor(Math.random() * 1000);
        return `${prefix}${suffix}${number}`;
    }
    
    // History and Activity management
    addToHistory(data) {
        this.roundHistory.unshift(data);
        
        if (this.roundHistory.length > this.maxHistoryItems) {
            this.roundHistory.pop();
        }
        
        this.updateHistoryDisplay();
    }
    
    updateHistoryDisplay() {
        this.historyList.innerHTML = '';
        
        this.roundHistory.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            if (item.crashPoint < 2) {
                historyItem.classList.add('low');
            } else if (item.crashPoint < 5) {
                historyItem.classList.add('medium');
            } else {
                historyItem.classList.add('high');
            }
            
            historyItem.textContent = item.crashPoint.toFixed(2);
            historyItem.title = `Crash Point: ${item.crashPoint.toFixed(2)}x\nNonce: ${item.nonce}`;
            
            this.historyList.appendChild(historyItem);
        });
    }
    
    addToActivityFeed(data) {
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        
        switch (data.type) {
            case 'bet':
                activityItem.innerHTML = `<span class="activity-player">${data.player}</span> placed a bet of <strong>${data.bet} GA</strong>`;
                break;
                
            case 'win':
                activityItem.innerHTML = `<span class="activity-player">${data.player}</span> cashed out at <span class="activity-multiplier win">${data.multiplier.toFixed(2)}x</span> and won <strong class="win">${data.winnings} GA</strong>`;
                break;
                
            case 'lose':
                activityItem.innerHTML = `<span class="activity-player">${data.player}</span> lost <strong class="lose">${data.bet} GA</strong> at <span class="activity-multiplier lose">${data.multiplier.toFixed(2)}x</span>`;
                break;
        }
        
        this.activityFeedElement.insertBefore(activityItem, this.activityFeedElement.firstChild);
        
        if (this.activityFeedElement.children.length > this.maxActivityItems) {
            this.activityFeedElement.removeChild(this.activityFeedElement.lastChild);
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new CosmicAscent();
});