/* Punto Banco Elite - Provably Fair Baccarat */

:root {
    --elegant-dark: #0b1426;
    --elegant-blue: #1a2f50;
    --elegant-gold: #d4af37;
    --elegant-red: #8b1538;
    --elegant-green: #0f4c3a;
    --table-felt: #1b5e20;
    --table-felt-dark: #0d4016;
    --card-white: #fafafa;
    --text-light: #f5f5f5;
    --text-gold: #ffecb3;
    --text-red: #ffcdd2;
    --text-muted: #b0bec5;
    --border-radius: 12px;
    --box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, var(--elegant-dark) 0%, var(--elegant-blue) 100%);
    color: var(--text-light);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    touch-action: manipulation;
}

.game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 10px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Pro View Mode Styling */
body.pro-view-active .pro-stats-bar,
body.pro-view-active .pro-analysis-section,
body.pro-view-active .pro-advanced-stats,
body.pro-view-active .pro-betting-history,
body.pro-view-active .pro-bet-win-rate {
    display: block;
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 0;
    border-bottom: 2px solid var(--elegant-gold);
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    flex-wrap: wrap;
}

.header-left, .header-right {
    flex: 1;
}

.header-center {
    flex: 2;
    text-align: center;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 16px;
    font-weight: 400;
    transition: var(--transition);
    padding: 10px 15px;
    border-radius: var(--border-radius);
}

.back-link i {
    margin-right: 8px;
}

.back-link:hover {
    color: var(--elegant-gold);
    background: rgba(212, 175, 55, 0.1);
}

.game-title {
    font-family: 'Playfair Display', serif;
    font-size: 36px;
    font-weight: 700;
    color: var(--elegant-gold);
    margin-bottom: 8px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.7);
    letter-spacing: 2px;
}

.game-subtitle {
    font-size: 16px;
    font-style: italic;
    color: var(--text-gold);
    opacity: 0.9;
}

.view-toggle {
    display: flex;
    margin-bottom: 10px;
    background: var(--elegant-dark);
    border-radius: var(--border-radius);
    padding: 2px;
    border: 1px solid var(--elegant-blue);
    max-width: 200px;
    margin-left: auto;
}

.view-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-muted);
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    border-radius: calc(var(--border-radius) - 2px);
}

.view-btn.active {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
    font-weight: 500;
}

.info-btn {
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    color: var(--text-light);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    margin-left: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.info-btn i {
    margin-right: 8px;
}

.info-btn:hover {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    gap: 20px;
    align-items: flex-start;
}

/* Pro Stats Bar */
.pro-stats-bar {
    display: none;
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    margin-bottom: 15px;
    box-shadow: var(--box-shadow);
    overflow-x: auto;
    white-space: nowrap;
}

.pro-stat {
    display: inline-block;
    padding: 0 15px;
    border-right: 1px solid rgba(212, 175, 55, 0.3);
}

.pro-stat:last-child {
    border-right: none;
}

.pro-stat-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
}

.pro-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--elegant-gold);
}

/* Game Section */
.game-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Status */
.game-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    padding: 12px 20px;
    box-shadow: var(--box-shadow);
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.status-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--elegant-gold);
}

/* Baccarat Table */
.baccarat-table {
    background: linear-gradient(135deg, var(--table-felt) 0%, var(--table-felt-dark) 100%);
    border: 3px solid var(--elegant-gold);
    border-radius: 20px;
    padding: 20px;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
    background-image: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><g fill="%23134e13" fill-opacity="0.05"><circle cx="20" cy="20" r="1"/></g></svg>');
}

.baccarat-table::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

/* Cards Display */
.cards-display {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
    gap: 15px;
}

.hand {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
    border: 2px solid rgba(212, 175, 55, 0.3);
    min-width: 180px;
    flex: 1;
}

.hand-label {
    font-family: 'Playfair Display', serif;
    font-size: 20px;
    font-weight: 600;
    color: var(--elegant-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
}

.hand-cards {
    display: flex;
    gap: 8px;
    min-height: 120px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.card {
    width: 70px;
    height: 105px;
    background: var(--card-white);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 8px;
    transition: var(--transition);
    border: 1px solid #ddd;
}

.card.dealing {
    animation: dealCard 0.6s ease-out;
}

.card.winning {
    animation: highlightCard 1s infinite alternate;
}

@keyframes dealCard {
    0% {
        transform: translateY(-100px) rotateY(180deg) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translateY(-50px) rotateY(90deg) scale(0.9);
        opacity: 0.5;
    }
    100% {
        transform: translateY(0) rotateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes highlightCard {
    0% { 
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 8px 30px rgba(212, 175, 55, 0.8);
        transform: scale(1.05);
    }
}

.card-value {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.card-suit {
    font-size: 18px;
    color: #333;
}

.card.red .card-value,
.card.red .card-suit {
    color: #d32f2f;
}

.card-top-left {
    position: absolute;
    top: 5px;
    left: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1;
}

.card-bottom-right {
    position: absolute;
    bottom: 5px;
    right: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1;
    transform: rotate(180deg);
}

.card-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 28px;
}

.hand-score {
    font-size: 28px;
    font-weight: 700;
    color: var(--elegant-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    background: rgba(0, 0, 0, 0.4);
    padding: 8px 16px;
    border-radius: 50px;
    border: 2px solid var(--elegant-gold);
    min-width: 60px;
    text-align: center;
}

/* Betting Areas */
.betting-areas {
    display: flex;
    flex-direction: column;
    gap: 15px;
    position: relative;
    z-index: 2;
}

.main-bets {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    flex-wrap: wrap;
}

.side-bets {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.bet-area {
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
    border: 3px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    padding: 15px 10px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    flex: 1;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    min-width: 90px;
}

.bet-area.small {
    flex: 0 1 120px;
}

.bet-area:hover {
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.bet-area.selected {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
    border-color: #fff;
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
}

.bet-area.winning {
    animation: winningBet 1s infinite alternate;
}

@keyframes winningBet {
    0% { 
        background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
        box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
        transform: scale(1);
    }
    100% { 
        background: linear-gradient(145deg, #ffeb3b, var(--elegant-gold));
        box-shadow: 0 12px 35px rgba(255, 235, 59, 0.7);
        transform: scale(1.02);
    }
}

.bet-label {
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    font-weight: 600;
    color: var(--elegant-gold);
    margin-bottom: 4px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    letter-spacing: 1px;
}

.bet-area.selected .bet-label {
    color: var(--elegant-dark);
    text-shadow: none;
}

.bet-payout {
    font-size: 11px;
    color: var(--text-muted);
    margin-bottom: 6px;
}

.bet-area.selected .bet-payout {
    color: rgba(11, 20, 38, 0.7);
}

.bet-amount {
    font-size: 18px;
    font-weight: 700;
    color: var(--elegant-gold);
    background: rgba(0, 0, 0, 0.3);
    padding: 6px 10px;
    border-radius: 6px;
    display: inline-block;
    min-width: 50px;
}

.bet-area.selected .bet-amount {
    color: var(--elegant-dark);
    background: rgba(255, 255, 255, 0.2);
}

/* Pro bet win rate */
.pro-bet-win-rate {
    display: none;
    font-size: 11px;
    color: var(--text-light);
    margin-top: 8px;
    background: rgba(0, 0, 0, 0.2);
    padding: 3px 6px;
    border-radius: 4px;
}

/* Outcome Display */
.outcome-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, var(--elegant-dark), var(--elegant-blue));
    border: 3px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    z-index: 10;
    box-shadow: var(--box-shadow);
    animation: resultShow 0.5s ease-out;
    backdrop-filter: blur(10px);
    width: 80%;
    max-width: 320px;
}

@keyframes resultShow {
    0% {
        transform: translate(-50%, -50%) scale(0.7);
        opacity: 0;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

.outcome-message {
    font-family: 'Playfair Display', serif;
    font-size: 22px;
    font-weight: 600;
    color: var(--elegant-gold);
    margin-bottom: 10px;
}

.winnings {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-light);
}

/* Game Controls */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--box-shadow);
    position: relative;
    z-index: 2;
}

.chip-selection {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.chip {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    border: 3px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    position: relative;
}

.chip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.chip[data-value="1"] {
    background: linear-gradient(145deg, #f5f5f5, #e0e0e0);
    color: #333;
    text-shadow: none;
}

.chip[data-value="5"] {
    background: linear-gradient(145deg, #f44336, #d32f2f);
}

.chip[data-value="25"] {
    background: linear-gradient(145deg, #4caf50, #388e3c);
}

.chip[data-value="100"] {
    background: linear-gradient(145deg, #2196f3, #1976d2);
}

.chip[data-value="500"] {
    background: linear-gradient(145deg, #9c27b0, #7b1fa2);
}

.chip:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.chip.selected {
    transform: translateY(-3px) scale(1.1);
    border-color: var(--elegant-gold);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.action-btn {
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    color: var(--text-light);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    flex: 1;
    max-width: 150px;
}

.action-btn:hover {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.action-btn.primary {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
    font-weight: 600;
}

.action-btn.primary:hover {
    background: linear-gradient(145deg, #ffeb3b, var(--elegant-gold));
}

/* Credits Display */
.credits-display {
    display: flex;
    justify-content: space-between;
    background: linear-gradient(145deg, var(--elegant-dark), var(--elegant-blue));
    border: 2px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    padding: 12px 20px;
    box-shadow: var(--box-shadow);
    flex-wrap: wrap;
    gap: 10px;
}

.credits-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.credits-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.credits-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--elegant-gold);
}

/* Mobile Info Toggle */
.mobile-info-toggle {
    display: none;
    margin-bottom: 15px;
}

.mobile-toggle-btn {
    width: 100%;
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    color: var(--text-light);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mobile-toggle-btn i {
    font-size: 18px;
}

.mobile-toggle-btn.active {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
}

/* Info Panel */
.info-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 400px;
}

.info-section {
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.info-section h3 {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
    padding: 12px 15px;
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    border-bottom: 2px solid var(--elegant-gold);
}

/* Baccarat Roads */
.roads-section {
    height: 260px;
}

.tabs {
    display: flex;
    background: var(--elegant-dark);
}

.tab-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-muted);
    padding: 8px 5px;
    font-size: 11px;
    cursor: pointer;
    transition: var(--transition);
    border-right: 1px solid var(--elegant-blue);
}

.tab-btn:last-child {
    border-right: none;
}

.tab-btn.active {
    color: var(--elegant-gold);
    background: var(--elegant-blue);
}

.tab-btn:hover {
    color: var(--elegant-gold);
}

.tab-content {
    padding: 15px;
    min-height: 180px;
    overflow: hidden;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.bead-grid,
.big-road-grid,
.derived-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 2px;
    max-height: 160px;
    overflow-y: auto;
}

.bead-grid div,
.big-road-grid div,
.derived-grid div {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.bead-player,
.big-road-player {
    background: linear-gradient(145deg, #2196f3, #1976d2);
    color: white;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.bead-banker,
.big-road-banker {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.bead-tie,
.big-road-tie {
    background: linear-gradient(145deg, #4caf50, #388e3c);
    color: white;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

.derived-red {
    background: linear-gradient(145deg, #f44336, #d32f2f);
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
}

.derived-blue {
    background: linear-gradient(145deg, #2196f3, #1976d2);
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

/* Pro Analysis Section */
.pro-analysis-section {
    display: none;
    height: 260px;
}

.pro-analysis-content {
    padding: 15px;
    height: 210px;
}

.pro-chart {
    height: 120px;
    margin-bottom: 15px;
    background: var(--elegant-dark);
    border-radius: 8px;
    overflow: hidden;
}

.chart-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    color: var(--text-muted);
    font-style: italic;
    font-size: 14px;
}

.trend-stats {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.trend-stat {
    flex: 1;
    min-width: 100px;
    padding: 8px;
    background: var(--elegant-dark);
    border-radius: 6px;
    margin: 5px;
    text-align: center;
}

.trend-label {
    font-size: 11px;
    color: var(--text-muted);
    display: block;
    margin-bottom: 4px;
}

.trend-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--elegant-gold);
}

/* Provably Fair */
.fair-section {
    height: 240px;
}

.fair-info {
    padding: 15px;
    height: 180px;
    overflow-y: auto;
}

.seed-display {
    margin-bottom: 15px;
}

.seed-item {
    margin-bottom: 15px;
}

.seed-label {
    display: block;
    font-size: 11px;
    color: var(--text-muted);
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.seed-value {
    display: block;
    padding: 8px 12px;
    background: var(--elegant-dark);
    border: 1px solid var(--elegant-blue);
    border-radius: 6px;
    font-size: 11px;
    word-break: break-all;
    font-family: 'Courier New', monospace;
    color: var(--text-light);
}

#clientSeedInput {
    width: 100%;
    background: var(--elegant-dark);
    border: 1px solid var(--elegant-blue);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-family: 'Courier New', monospace;
}

#clientSeedInput:focus {
    outline: none;
    border-color: var(--elegant-gold);
}

.seed-btn {
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    border: 2px solid var(--elegant-gold);
    color: var(--text-light);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
}

.seed-btn:hover {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    color: var(--elegant-dark);
}

/* Hand History */
.history-section {
    height: 340px;
}

.history-list {
    padding: 15px;
    max-height: 280px;
    overflow-y: auto;
}

.history-item {
    padding: 10px;
    margin-bottom: 10px;
    background: var(--elegant-dark);
    border-radius: 8px;
    font-size: 12px;
    border-left: 3px solid var(--elegant-gold);
    transition: var(--transition);
}

.history-item:hover {
    background: rgba(26, 47, 80, 0.5);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-round {
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    color: var(--elegant-gold);
}

.history-cards {
    margin-bottom: 6px;
    color: var(--text-muted);
}

.history-result {
    display: flex;
    justify-content: space-between;
    color: var(--text-light);
    font-weight: 500;
}

.no-history {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 20px;
}

/* Statistics */
.stats-section {
    height: 260px;
}

.stats-grid {
    padding: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    height: 200px;
    overflow-y: auto;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--elegant-gold);
}

/* Pro Advanced Stats */
.pro-advanced-stats {
    display: none;
    height: 340px;
}

.pro-stats-grid {
    padding: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    height: 280px;
    overflow-y: auto;
}

.pro-stat-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 10px;
    background: var(--elegant-dark);
    border-radius: 8px;
}

.pro-stat-label {
    font-size: 11px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--elegant-gold);
}

/* Pro Betting History */
.pro-betting-history {
    display: none;
    height: 260px;
}

.pro-betting-chart {
    padding: 15px;
    height: 210px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--elegant-dark);
    margin: 15px;
    border-radius: 8px;
}

/* Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
    animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
    0% {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    100% {
        opacity: 1;
        backdrop-filter: blur(5px);
    }
}

.modal-content {
    background: linear-gradient(145deg, var(--elegant-blue), var(--elegant-dark));
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 2px solid var(--elegant-gold);
    animation: modalContentShow 0.3s ease-out;
}

@keyframes modalContentShow {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: var(--elegant-dark);
    font-family: 'Playfair Display', serif;
    font-size: 20px;
    font-weight: 600;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--elegant-dark);
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: var(--transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

.rules-section,
.verify-section {
    margin-bottom: 20px;
}

.rules-section h3,
.verify-section h3 {
    color: var(--elegant-gold);
    font-family: 'Playfair Display', serif;
    font-size: 18px;
    margin-bottom: 12px;
}

.rules-section p,
.rules-section li,
.verify-section p,
.verify-section li {
    font-size: 13px;
    margin-bottom: 8px;
    line-height: 1.6;
}

.rules-section ul,
.rules-section ol,
.verify-section ul,
.verify-section ol {
    padding-left: 25px;
    margin-bottom: 15px;
}

.rules-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: var(--elegant-dark);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    font-size: 12px;
}

.rules-table th,
.rules-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid var(--elegant-blue);
}

.rules-table th {
    background: var(--elegant-gold);
    color: var(--elegant-dark);
    font-weight: 600;
}

/* Verify Modal */
.verify-inputs {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input-group label {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 500;
}

.input-group input {
    padding: 10px 12px;
    border-radius: 8px;
    border: 2px solid var(--elegant-blue);
    background: var(--elegant-dark);
    color: var(--text-light);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    transition: var(--transition);
}

.input-group input:focus {
    outline: none;
    border-color: var(--elegant-gold);
}

.verify-btn {
    background: linear-gradient(145deg, var(--elegant-gold), #b8941f);
    border: none;
    color: var(--elegant-dark);
    padding: 12px 25px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition);
    width: 100%;
    margin: 20px 0;
}

.verify-btn:hover {
    background: linear-gradient(145deg, #ffeb3b, var(--elegant-gold));
    transform: translateY(-2px);
}

.verify-result,
.current-shoe-info {
    padding: 15px;
    background: var(--elegant-dark);
    border-radius: 8px;
    font-size: 13px;
    line-height: 1.6;
    border: 1px solid var(--elegant-blue);
}

.verify-result h4 {
    color: var(--elegant-gold);
    margin-bottom: 15px;
}

.verify-result .error {
    color: var(--text-red);
}

.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .info-panel {
        max-width: none;
    }
}

@media (max-width: 900px) {
    .header-right {
        flex-basis: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 15px;
    }
    
    .view-toggle {
        margin-left: 0;
    }
}

@media (max-width: 768px) {
    .game-container {
        padding: 10px 5px;
    }
    
    .game-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
        padding: 10px 0;
    }
    
    .header-left,
    .header-right {
        flex: none;
        width: 100%;
    }
    
    .header-right {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        margin-top: 10px;
    }
    
    .view-toggle {
        margin: 0 auto;
    }
    
    .back-link {
        margin: 0 auto;
        display: inline-flex;
    }
    
    .info-btn {
        margin: 0 5px;
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .game-title {
        font-size: 26px;
    }
    
    .game-subtitle {
        font-size: 14px;
    }
    
    .cards-display {
        flex-direction: column;
        gap: 15px;
    }
    
    .hand {
        min-width: auto;
        width: 100%;
    }
    
    .bet-area, .bet-area.small {
        flex: 1 0 calc(33.33% - 10px);
        min-width: 80px;
    }
    
    .main-bets, .side-bets {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .bet-label {
        font-size: 14px;
    }
    
    .bet-amount {
        font-size: 16px;
    }
    
    .game-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .chip-selection {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .credits-display {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .status-item {
        flex: 1;
    }
    
    .game-status {
        padding: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .bead-grid,
    .big-road-grid,
    .derived-grid {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .bead-grid div,
    .big-road-grid div,
    .derived-grid div {
        width: 18px;
        height: 18px;
        font-size: 9px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    /* Show mobile info toggle */
    .mobile-info-toggle {
        display: block;
    }
    
    /* Hide info panel on mobile by default */
    .info-panel > .info-section {
        display: none;
    }
    
    .info-panel.active > .info-section {
        display: block;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 22px;
    }
    
    .game-subtitle {
        font-size: 12px;
    }
    
    .main-content {
        gap: 10px;
    }
    
    .game-section {
        gap: 10px;
    }
    
    .baccarat-table {
        padding: 15px 10px;
    }
    
    .chip {
        width: 40px;
        height: 40px;
        font-size: 12px;
    }
    
    .chip::before {
        width: 24px;
        height: 24px;
    }
    
    .action-btn {
        padding: 10px 15px;
        font-size: 14px;
        flex: 1;
    }
    
    .bet-label {
        font-size: 13px;
    }
    
    .bet-payout {
        font-size: 9px;
    }
    
    .bet-amount {
        font-size: 14px;
        padding: 4px 8px;
    }
    
    .hand-label {
        font-size: 16px;
    }
    
    .hand-score {
        font-size: 22px;
        padding: 6px 12px;
    }
    
    .card {
        width: 60px;
        height: 90px;
    }
    
    .card-value,
    .card-suit {
        font-size: 12px;
    }
    
    .card-center {
        font-size: 24px;
    }
    
    .btn-text, .back-text {
        display: none;
    }
    
    .info-btn i {
        margin-right: 0;
    }
    
    .info-btn {
        padding: 8px;
    }
    
    .outcome-display {
        padding: 15px;
        width: 90%;
    }
    
    .outcome-message {
        font-size: 18px;
    }
    
    .winnings {
        font-size: 16px;
    }
    
    .pro-stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Special Styles for small phones */
@media (max-width: 375px) {
    .bet-area, .bet-area.small {
        padding: 10px 5px;
    }
    
    .bet-label {
        font-size: 11px;
    }
    
    .bet-amount {
        font-size: 12px;
        min-width: 40px;
    }
    
    .hand-cards {
        min-height: 100px;
    }
    
    .card {
        width: 50px;
        height: 75px;
    }
    
    .hand-score {
        font-size: 18px;
        padding: 5px 10px;
        min-width: 50px;
    }
    
    .action-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .credits-display {
        padding: 10px;
    }
    
    .credits-value {
        font-size: 16px;
    }
}