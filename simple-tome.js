// Simple implementation of Tome of Life
document.addEventListener('DOMContentLoaded', function() {
    console.log("Simple Tome of Life initializing...");
    
    // Game elements
    const revealBtn = document.getElementById('revealBtn');
    const stakeInput = document.getElementById('stakeAmount');
    const halfStakeBtn = document.getElementById('halfStakeBtn');
    const doubleStakeBtn = document.getElementById('doubleStakeBtn');
    const maxStakeBtn = document.getElementById('maxStakeBtn');
    const balanceValue = document.getElementById('balanceValue');
    const currentStakeValue = document.getElementById('currentStakeValue');
    const symbolGrid = document.getElementById('symbolGrid');
    const symbolCells = document.querySelectorAll('.symbol-cell');
    const lastWinValue = document.getElementById('lastWinValue');
    const totalPlaysValue = document.getElementById('totalPlaysValue');
    
    // Game state
    let gaBalance = 1000; // GA currency balance
    let currentStake = 0;
    let isRevealing = false;
    let totalPlays = 0;
    
    // GA Currency settings
    const gaCurrencyName = "GA Coins";
    const gaCurrencySymbol = "GA";
    
    // Symbols
    const symbols = [
        { id: 'sun', icon: '☀️', name: 'Sun', value: 2 },
        { id: 'moon', icon: '🌙', name: 'Moon', value: 2 },
        { id: 'star', icon: '⭐', name: 'Star', value: 3 },
        { id: 'heart', icon: '❤️', name: 'Heart', value: 3 },
        { id: 'tree', icon: '🌳', name: 'Tree', value: 5 },
        { id: 'phoenix', icon: '🔥', name: 'Phoenix', value: 10 },
        { id: 'eye', icon: '👁️', name: 'Eye', value: 15 },
        { id: 'skull', icon: '💀', name: 'Skull', value: 0, negative: true }
    ];
    
    // Winning lines
    const winLines = [
        // Rows
        [0, 1, 2], [3, 4, 5], [6, 7, 8],
        // Columns
        [0, 3, 6], [1, 4, 7], [2, 5, 8],
        // Diagonals
        [0, 4, 8], [2, 4, 6]
    ];
    
    // Update UI with GA currency
    updateUI();
    
    // Initialize balance display
    balanceValue.textContent = `${gaBalance} ${gaCurrencySymbol}`;
    
    // Event listeners
    revealBtn.addEventListener('click', startReveal);
    halfStakeBtn.addEventListener('click', () => adjustStake(0.5));
    doubleStakeBtn.addEventListener('click', () => adjustStake(2));
    maxStakeBtn.addEventListener('click', () => setMaxStake());
    
    // Initialize symbol grid with empty cells
    initializeSymbolGrid();
    
    function updateUI() {
        // Update game subtitle
        if (document.querySelector('.game-subtitle')) {
            document.querySelector('.game-subtitle').textContent = 
                `Unlock ancient symbols to reveal the secrets of existence - Play with ${gaCurrencyName}`;
        }
        
        // Update balance display label
        if (document.querySelectorAll('.stat-label')) {
            const balanceLabels = document.querySelectorAll('.stat-label');
            balanceLabels.forEach(label => {
                if (label.textContent === 'Balance') {
                    label.textContent = `${gaCurrencyName}`;
                } else if (label.textContent === 'Current Stake') {
                    label.textContent = `Current Stake (${gaCurrencySymbol})`;
                } else if (label.textContent === 'Last Win') {
                    label.textContent = `Last Win (${gaCurrencySymbol})`;
                }
            });
        }
        
        // Update stake input label
        if (document.querySelector('label[for="stakeAmount"]')) {
            document.querySelector('label[for="stakeAmount"]').textContent = `Offering (${gaCurrencySymbol}):`;
        }
    }
    
    function initializeSymbolGrid() {
        symbolCells.forEach(cell => {
            const symbolElement = cell.querySelector('.symbol-inner');
            symbolElement.textContent = '';
            symbolElement.style.fontSize = '36px'; // Make symbols larger
            symbolElement.style.display = 'flex';
            symbolElement.style.justifyContent = 'center';
            symbolElement.style.alignItems = 'center';
            symbolElement.style.height = '100%';
            cell.classList.remove('revealed', 'highlighted');
        });
    }
    
    function adjustStake(factor) {
        const currentValue = parseInt(stakeInput.value) || 0;
        const newValue = Math.floor(currentValue * factor);
        stakeInput.value = Math.max(10, Math.min(newValue, gaBalance));
    }
    
    function setMaxStake() {
        stakeInput.value = gaBalance;
    }
    
    function startReveal() {
        if (isRevealing) return;
        
        const stakeAmount = parseInt(stakeInput.value);
        
        // Validate stake
        if (isNaN(stakeAmount) || stakeAmount <= 0) {
            alert(`Please enter a valid ${gaCurrencyName} amount`);
            return;
        }
        
        if (stakeAmount > gaBalance) {
            alert(`Insufficient ${gaCurrencyName} balance`);
            return;
        }
        
        // Update game state
        isRevealing = true;
        currentStake = stakeAmount;
        gaBalance -= stakeAmount;
        totalPlays++;
        
        // Update UI
        balanceValue.textContent = `${gaBalance} ${gaCurrencySymbol}`;
        currentStakeValue.textContent = `${stakeAmount} ${gaCurrencySymbol}`;
        totalPlaysValue.textContent = totalPlays;
        revealBtn.disabled = true;
        
        // Clear previous symbols
        clearSymbols();
        
        // Generate random symbols
        const grid = generateRandomSymbols();
        
        // Reveal symbols one by one
        revealSymbols(grid).then(() => {
            // Check for wins
            const result = checkWinCondition(grid);
            
            if (result.win) {
                // Player won
                const winAmount = currentStake * result.multiplier;
                gaBalance += winAmount;
                balanceValue.textContent = `${gaBalance} ${gaCurrencySymbol}`;
                lastWinValue.textContent = `${winAmount} ${gaCurrencySymbol}`;
                
                // Highlight winning lines
                result.lines.forEach(line => {
                    highlightWinningLine(line);
                });
                
                alert(`You won ${winAmount} ${gaCurrencySymbol}! (${result.multiplier}x multiplier)`);
            } else if (result.hasSkull) {
                // Player lost due to skull
                lastWinValue.textContent = `0 ${gaCurrencySymbol}`;
                alert('You found a Skull! Better luck next time.');
            } else {
                // No win
                lastWinValue.textContent = `0 ${gaCurrencySymbol}`;
                alert('No winning combinations. Try again!');
            }
            
            // Reset for next round
            isRevealing = false;
            revealBtn.disabled = false;
        });
    }
    
    function clearSymbols() {
        symbolCells.forEach(cell => {
            const symbolElement = cell.querySelector('.symbol-inner');
            symbolElement.textContent = '';
            cell.classList.remove('revealed', 'highlighted');
        });
    }
    
    function generateRandomSymbols() {
        const grid = [];
        
        for (let i = 0; i < 9; i++) {
            const randomIndex = Math.floor(Math.random() * symbols.length);
            grid.push(symbols[randomIndex]);
        }
        
        return grid;
    }
    
    function revealSymbols(grid) {
        return new Promise(resolve => {
            symbolCells.forEach((cell, index) => {
                setTimeout(() => {
                    const symbol = grid[index];
                    const symbolElement = cell.querySelector('.symbol-inner');
                    symbolElement.textContent = symbol.icon;
                    cell.classList.add('revealed');
                    
                    // If this is the last symbol, resolve after a delay
                    if (index === symbolCells.length - 1) {
                        setTimeout(resolve, 500);
                    }
                }, index * 200);
            });
        });
    }
    
    function checkWinCondition(grid) {
        let hasSkull = false;
        let winningLines = [];
        let highestMultiplier = 0;
        
        // Check for skulls
        grid.forEach(symbol => {
            if (symbol.id === 'skull') {
                hasSkull = true;
            }
        });
        
        if (hasSkull) {
            return { win: false, hasSkull: true };
        }
        
        // Check winning lines
        winLines.forEach(line => {
            const [a, b, c] = line;
            const symbolA = grid[a];
            const symbolB = grid[b];
            const symbolC = grid[c];
            
            if (symbolA.id === symbolB.id && symbolB.id === symbolC.id) {
                winningLines.push(line);
                
                if (symbolA.value > highestMultiplier) {
                    highestMultiplier = symbolA.value;
                }
            }
        });
        
        return {
            win: winningLines.length > 0,
            lines: winningLines,
            multiplier: highestMultiplier,
            hasSkull: false
        };
    }
    
    function highlightWinningLine(line) {
        line.forEach(index => {
            symbolCells[index].classList.add('highlighted');
        });
    }
});