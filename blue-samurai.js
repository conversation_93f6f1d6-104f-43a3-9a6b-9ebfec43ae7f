// Blue Samurai - Mobile Enhanced Bushido Strategy Game
document.addEventListener('DOMContentLoaded', function() {
    console.log("Blue Samurai Mobile Enhanced initializing...");
    
    // Game elements
    const viewModeToggle = document.getElementById('viewModeToggle');
    const viewModeText = document.getElementById('viewModeText');
    const tutorialBtn = document.getElementById('tutorialBtn');
    const verifyBtn = document.getElementById('verifyBtn');
    const swiftStrikeBtn = document.getElementById('swiftStrikeBtn');
    const preciseCutBtn = document.getElementById('preciseCutBtn');
    const mightyBlowBtn = document.getElementById('mightyBlowBtn');
    
    // Display elements
    const honorFill = document.getElementById('honorFill');
    const honorText = document.getElementById('honorText');
    const playerStanceIndicator = document.getElementById('playerStanceIndicator');
    const opponentStanceIndicator = document.getElementById('opponentStanceIndicator');
    const battleEffects = document.getElementById('battleEffects');
    const battleParticles = document.getElementById('battleParticles');
    const outcomeDisplay = document.getElementById('outcomeDisplay');
    const outcomeText = document.getElementById('outcomeText');
    const outcomeDetail = document.getElementById('outcomeDetail');
    const logEntries = document.getElementById('logEntries');
    const roundCounter = document.getElementById('roundCounter');
    
    // Pro View elements
    const winRate = document.getElementById('winRate');
    const avgHonorGain = document.getElementById('avgHonorGain');
    const combatEfficiency = document.getElementById('combatEfficiency');
    const streakRecord = document.getElementById('streakRecord');
    const bushidoRating = document.getElementById('bushidoRating');
    const playerAdvantage = document.getElementById('playerAdvantage');
    const opponentThreat = document.getElementById('opponentThreat');
    const strategyTip = document.getElementById('strategyTip');
    const nextRank = document.getElementById('nextRank');
    
    // Statistics elements
    const currentStreakDisplay = document.getElementById('currentStreakDisplay');
    const highestStreak = document.getElementById('highestStreak');
    const totalWins = document.getElementById('totalWins');
    const totalLosses = document.getElementById('totalLosses');
    const totalBattles = document.getElementById('totalBattles');
    const victories = document.getElementById('victories');
    const defeats = document.getElementById('defeats');
    const honorGained = document.getElementById('honorGained');
    const currentStreak = document.getElementById('currentStreak');
    const perfectRounds = document.getElementById('perfectRounds');
    const avgHonorPerBattle = document.getElementById('avgHonorPerBattle');
    const successRate = document.getElementById('successRate');
    const personalRTP = document.getElementById('personalRTP');
    
    // Fairness elements
    const serverSeedHash = document.getElementById('serverSeedHash');
    const clientSeed = document.getElementById('clientSeed');
    const roundId = document.getElementById('roundId');
    const verificationHash = document.getElementById('verificationHash');
    
    // Modal elements
    const tutorialModal = document.getElementById('tutorialModal');
    const closeTutorialBtn = document.getElementById('closeTutorialBtn');
    const verifyModal = document.getElementById('verifyModal');
    const closeVerifyBtn = document.getElementById('closeVerifyBtn');
    const outcomeModal = document.getElementById('outcomeModal');
    const continueBtn = document.getElementById('continueBtn');
    const choiceModal = document.getElementById('choiceModal');
    const closeChoiceBtn = document.getElementById('closeChoiceBtn');
    const encyclopediaModal = document.getElementById('encyclopediaModal');
    const closeEncyclopediaBtn = document.getElementById('closeEncyclopediaBtn');
    
    // Game state
    let gameState = {
        // Player data
        honor: 50,
        rank: "Ronin",
        currentStreakCount: 0,
        highestStreakCount: 0,
        totalWinsCount: 0,
        totalLossesCount: 0,
        totalBattlesCount: 0,
        totalHonorGained: 0,
        perfectRoundsCount: 0,
        roundNumber: 0,
        
        // View mode
        viewMode: 'standard', // 'standard' or 'pro'
        
        // Current battle state
        playerStance: null,
        opponentStance: null,
        battleInProgress: false,
        lastBattleResult: null,
        lastHonorChange: 0,
        
        // Performance tracking
        winHistory: [],
        battleResults: [],
        strategyEffectiveness: {
            swift: { attempts: 0, wins: 0, honorGained: 0 },
            precise: { attempts: 0, wins: 0, honorGained: 0 },
            mighty: { attempts: 0, wins: 0, honorGained: 0 }
        },
        
        // Provably fair data
        serverSeed: null,
        clientSeedValue: null,
        currentRoundId: 0,
        pastRounds: []
    };
    
    // Mobile-specific configuration
    const mobileConfig = {
        enableTouch: true,
        hapticFeedback: 'ontouchstart' in window,
        doubleClickSpeed: 300,
        longPressDelay: 500,
        swipeThreshold: 50
    };
    
    // Touch handling
    let touchState = {
        startX: 0,
        startY: 0,
        startTime: 0,
        isLongPress: false,
        longPressTimer: null
    };
    
    // Action definitions
    const actions = {
        swift: {
            name: "Swift Strike",
            kanji: "迅",
            winChance: 28,
            outcomes: [
                { type: "Glancing Blow", multiplier: 2, chance: 15, honor: 5 },
                { type: "Clean Hit", multiplier: 4, chance: 10, honor: 10 },
                { type: "Critical Strike", multiplier: 8, chance: 3, honor: 20 }
            ]
        },
        precise: {
            name: "Precise Cut",
            kanji: "精",
            winChance: 22,
            outcomes: [
                { type: "Glancing Blow", multiplier: 3, chance: 10, honor: 8 },
                { type: "Clean Hit", multiplier: 6, chance: 9, honor: 15 },
                { type: "Critical Strike", multiplier: 15, chance: 3, honor: 30 }
            ]
        },
        mighty: {
            name: "Mighty Blow",
            kanji: "強",
            winChance: 15,
            outcomes: [
                { type: "Glancing Blow", multiplier: 5, chance: 6, honor: 12 },
                { type: "Clean Hit", multiplier: 10, chance: 6, honor: 25 },
                { type: "Critical Strike", multiplier: 100, chance: 3, honor: 50 }
            ]
        }
    };
    
    // Opponent stances
    const stances = ["Moon", "River", "Mountain"];
    
    // Educational content for encyclopedia
    const encyclopedia = {
        bushido: {
            title: "The Way of Bushido",
            content: `<p>Bushido (武士道) represents the moral code and way of life of the samurai warrior class in feudal Japan. This philosophy emphasized eight primary virtues that guided a samurai's conduct both in battle and in daily life.</p>
                     
                     <h3>The Eight Virtues of Bushido</h3>
                     <p><strong>Rectitude (義 - Gi):</strong> The ability to make morally right decisions with courage and determination. This was considered the most important virtue, as it provided the foundation for all other aspects of the samurai way.</p>
                     
                     <p><strong>Courage (勇 - Yu):</strong> Bravery in the face of danger, but tempered with wisdom and righteousness. True courage was not reckless behavior, but the strength to do what is right despite personal risk.</p>
                     
                     <p><strong>Benevolence (仁 - Jin):</strong> Compassion and mercy toward others, especially those weaker than oneself. This virtue balanced the warrior's capacity for violence with humanity and kindness.</p>
                     
                     <p><strong>Respect (礼 - Rei):</strong> Proper courtesy and etiquette in all interactions. This included respect for superiors, peers, and even enemies, maintaining dignity in all circumstances.</p>
                     
                     <p><strong>Honesty (誠 - Makoto):</strong> Truthfulness and sincerity in word and deed. A samurai's word was considered sacred, and breaking it brought great shame.</p>
                     
                     <p><strong>Honor (名誉 - Meiyo):</strong> Personal dignity and reputation earned through virtuous conduct. Honor was considered more valuable than life itself.</p>
                     
                     <p><strong>Loyalty (忠義 - Chugi):</strong> Unwavering devotion to one's lord, family, and principles. This loyalty was absolute and formed the cornerstone of samurai relationships.</p>
                     
                     <p><strong>Self-Control (自制 - Jisei):</strong> Mastery over one's emotions and desires. This virtue enabled samurai to remain calm and focused in the heat of battle.</p>
                     
                     <p>These principles influenced every aspect of samurai culture, from martial training to tea ceremony, poetry, and governance. The way of Bushido emphasized that true strength came not just from martial prowess, but from moral and spiritual development.</p>`
        },
        combat: {
            title: "Samurai Combat Techniques",
            content: `<p>Samurai combat was a sophisticated art that combined physical skill, mental discipline, and strategic thinking. The three primary techniques in Blue Samurai represent different approaches to engagement.</p>
                     
                     <h3>Swift Strike (迅)</h3>
                     <p>Based on the principle of "Sen no Sen" (先の先) - attacking at the precise moment the opponent begins their attack. This technique prioritizes speed and timing over raw power, allowing the samurai to interrupt enemy attacks and create openings.</p>
                     <p>Historically, swift strikes were favored by samurai who relied on superior reflexes and battle experience. The technique requires exceptional awareness and the ability to read an opponent's intentions.</p>
                     
                     <h3>Precise Cut (精)</h3>
                     <p>Embodying the concept of "Ichigeki Hissatsu" (一撃必殺) - one strike, one kill. This technique focuses on finding the perfect moment and angle to deliver a decisive blow that ends the combat quickly and efficiently.</p>
                     <p>Precise cuts were the hallmark of master swordsmen who had spent years perfecting their form and understanding of anatomy. These warriors could end a duel with a single, perfectly placed strike.</p>
                     
                     <h3>Mighty Blow (強)</h3>
                     <p>Representing "Rikishi no Kokoro" (力士の心) - the heart of strength. This approach uses overwhelming force and power to break through an opponent's defenses, relying on superior strength and endurance.</p>
                     <p>Mighty blows were often employed by samurai who wore heavy armor or wielded larger weapons. While riskier, successful execution could devastate multiple opponents or break through seemingly impenetrable defenses.</p>
                     
                     <h3>The Mental Aspect</h3>
                     <p>Beyond physical technique, samurai combat emphasized mental preparation. The concept of "Mushin" (無心) - no mind - taught warriors to act without hesitation or emotional interference, achieving a state of perfect focus where technique flowed naturally.</p>
                     
                     <p>Training included meditation, calligraphy, and poetry to develop the mental discipline necessary for combat. A samurai's greatest weapon was considered to be their mind, with the sword merely an extension of their will.</p>`
        },
        history: {
            title: "The Samurai Era",
            content: `<p>The samurai class dominated Japanese society for over 700 years, from the late Heian period (794-1185) through the Meiji Restoration (1868). Their influence shaped Japanese culture, politics, and philosophy in ways that persist to this day.</p>
                     
                     <h3>Origins (Late Heian Period)</h3>
                     <p>The samurai emerged as provincial warriors in the 10th and 11th centuries, initially serving as military retainers to wealthy landowners. As central government power weakened, these warrior bands gained influence and eventually established their own military government.</p>
                     
                     <h3>The Age of War (1185-1603)</h3>
                     <p>During the Kamakura, Muromachi, and Sengoku periods, Japan experienced almost constant warfare. This era saw the development of sophisticated martial arts, military tactics, and the refinement of the samurai code of honor.</p>
                     <p>Notable periods include:</p>
                     <ul>
                        <li><strong>Genpei War (1180-1185):</strong> Established the first military government under Minamoto no Yoritomo</li>
                        <li><strong>Mongol Invasions (1274, 1281):</strong> Tested samurai resolve against foreign invaders</li>
                        <li><strong>Sengoku Period (1467-1615):</strong> Era of constant warfare that produced legendary figures like Oda Nobunaga and Tokugawa Ieyasu</li>
                     </ul>
                     
                     <h3>The Peace of Edo (1603-1868)</h3>
                     <p>Under Tokugawa rule, Japan enjoyed unprecedented peace. Samurai transformed from warriors to administrators, scholars, and artists. This period saw the codification of Bushido philosophy and the development of many traditional arts.</p>
                     
                     <h3>The Meiji Restoration (1868)</h3>
                     <p>The modernization of Japan led to the abolition of the samurai class. Many former samurai became leaders in the new government, military, and industry, helping Japan rapidly modernize while preserving important cultural traditions.</p>
                     
                     <h3>Legacy</h3>
                     <p>While the samurai class no longer exists, their principles of honor, dedication, and self-improvement continue to influence Japanese culture. The concept of Bushido has evolved beyond its military origins to encompass ideals of ethical leadership and personal excellence.</p>`
        }
    };
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing Blue Samurai Mobile Enhanced...");
        
        // Load saved game state
        loadGameState();
        
        // Set up event listeners
        setupEventListeners();
        
        // Setup mobile-specific features
        setupMobileFeatures();
        
        // Generate initial game state
        generateNewRound();
        
        // Update UI
        updateAllUI();
        updateProViewDisplay();
        
        console.log("Blue Samurai Mobile Enhanced initialized successfully!");
    }
    
    function setupEventListeners() {
        // View mode toggle
        if (viewModeToggle) {
            viewModeToggle.addEventListener('click', toggleViewMode);
        }
        
        // Action buttons
        if (swiftStrikeBtn) {
            swiftStrikeBtn.addEventListener('click', () => executeAction('swift'));
        }
        if (preciseCutBtn) {
            preciseCutBtn.addEventListener('click', () => executeAction('precise'));
        }
        if (mightyBlowBtn) {
            mightyBlowBtn.addEventListener('click', () => executeAction('mighty'));
        }
        
        // Modal controls
        if (tutorialBtn) {
            tutorialBtn.addEventListener('click', () => toggleModal(tutorialModal, true));
        }
        if (closeTutorialBtn) {
            closeTutorialBtn.addEventListener('click', () => toggleModal(tutorialModal, false));
        }
        if (verifyBtn) {
            verifyBtn.addEventListener('click', () => toggleModal(verifyModal, true));
        }
        if (closeVerifyBtn) {
            closeVerifyBtn.addEventListener('click', () => toggleModal(verifyModal, false));
        }
        if (continueBtn) {
            continueBtn.addEventListener('click', () => {
                toggleModal(outcomeModal, false);
                generateNewRound();
            });
        }
        if (closeChoiceBtn) {
            closeChoiceBtn.addEventListener('click', () => toggleModal(choiceModal, false));
        }
        if (closeEncyclopediaBtn) {
            closeEncyclopediaBtn.addEventListener('click', () => toggleModal(encyclopediaModal, false));
        }
        
        // Keyboard accessibility
        document.addEventListener('keydown', handleKeyboard);
        
        // Window resize handling
        window.addEventListener('resize', debounce(handleResize, 250));
        window.addEventListener('orientationchange', debounce(handleOrientationChange, 300));
    }
    
    function setupMobileFeatures() {
        // Touch event handling
        if (mobileConfig.enableTouch) {
            setupTouchEvents();
        }
        
        // Prevent zoom on double tap
        document.addEventListener('touchstart', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
        
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(e) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Improve touch feedback
        document.body.addEventListener('touchstart', function() {}, { passive: true });
        
        // Setup accessibility
        setupAccessibility();
    }
    
    function setupTouchEvents() {
        const actionButtons = [swiftStrikeBtn, preciseCutBtn, mightyBlowBtn];
        
        actionButtons.forEach(button => {
            if (!button) return;
            
            let touchStartTime = 0;
            let longPressTimer = null;
            
            button.addEventListener('touchstart', (e) => {
                touchStartTime = Date.now();
                touchState.startX = e.touches[0].clientX;
                touchState.startY = e.touches[0].clientY;
                touchState.isLongPress = false;
                
                // Visual feedback
                button.style.transform = 'scale(0.95)';
                
                // Start long press timer for action preview
                longPressTimer = setTimeout(() => {
                    touchState.isLongPress = true;
                    showActionPreview(button);
                    if (navigator.vibrate) {
                        navigator.vibrate(100);
                    }
                }, mobileConfig.longPressDelay);
            }, { passive: true });
            
            button.addEventListener('touchmove', (e) => {
                const deltaX = Math.abs(e.touches[0].clientX - touchState.startX);
                const deltaY = Math.abs(e.touches[0].clientY - touchState.startY);
                
                // Cancel long press if finger moves too much
                if (deltaX > 10 || deltaY > 10) {
                    clearTimeout(longPressTimer);
                    button.style.transform = '';
                }
            }, { passive: true });
            
            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                clearTimeout(longPressTimer);
                button.style.transform = '';
                
                const touchDuration = Date.now() - touchStartTime;
                
                if (!touchState.isLongPress && touchDuration < mobileConfig.doubleClickSpeed) {
                    // Regular tap - execute action
                    const actionType = button.id.replace('Btn', '').replace('Strike', '').replace('Cut', '').replace('Blow', '');
                    const actionMap = {
                        'swift': 'swift',
                        'precise': 'precise',
                        'mighty': 'mighty'
                    };
                    if (actionMap[actionType]) {
                        executeAction(actionMap[actionType]);
                    }
                }
            });
        });
    }
    
    function setupAccessibility() {
        // Add ARIA labels and keyboard navigation
        const actionButtons = [swiftStrikeBtn, preciseCutBtn, mightyBlowBtn];
        
        actionButtons.forEach((button, index) => {
            if (!button) return;
            
            button.setAttribute('tabindex', '0');
            button.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    button.click();
                }
            });
        });
    }
    
    function handleKeyboard(e) {
        // Keyboard shortcuts
        switch(e.key) {
            case '1':
                if (e.target === document.body) {
                    executeAction('swift');
                }
                break;
            case '2':
                if (e.target === document.body) {
                    executeAction('precise');
                }
                break;
            case '3':
                if (e.target === document.body) {
                    executeAction('mighty');
                }
                break;
            case 'h':
                if (e.target === document.body) {
                    toggleModal(tutorialModal, true);
                }
                break;
            case 'p':
                if (e.target === document.body) {
                    toggleViewMode();
                }
                break;
            case 'Escape':
                // Close any open modals
                document.querySelectorAll('.modal-overlay:not(.hidden)').forEach(modal => {
                    modal.classList.add('hidden');
                });
                break;
        }
    }
    
    function handleResize() {
        // Adjust layout for different screen sizes
        updateLayoutForScreenSize();
    }
    
    function handleOrientationChange() {
        // Wait for orientation change to complete
        setTimeout(() => {
            updateLayoutForScreenSize();
        }, 100);
    }
    
    function updateLayoutForScreenSize() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        // Adjust touch target sizes based on screen size
        const touchTargetSize = Math.max(44, Math.min(56, screenHeight / 15));
        document.documentElement.style.setProperty('--mobile-touch-target', `${touchTargetSize}px`);
        
        // Adjust font sizes
        const fontSize = Math.max(12, Math.min(16, screenWidth / 25));
        document.documentElement.style.setProperty('--mobile-font-size', `${fontSize}px`);
    }
    
    function toggleViewMode() {
        gameState.viewMode = gameState.viewMode === 'standard' ? 'pro' : 'standard';
        
        // Update UI
        updateProViewDisplay();
        
        // Save state
        saveGameState();
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(75);
        }
        
        // Show notification
        showNotification(`Switched to ${gameState.viewMode === 'pro' ? 'Pro' : 'Standard'} View`, 'info');
    }
    
    function updateProViewDisplay() {
        const isProView = gameState.viewMode === 'pro';
        
        // Update body class
        document.body.classList.toggle('pro-view-active', isProView);
        
        // Update button text
        if (viewModeText) {
            viewModeText.textContent = isProView ? 'Standard View' : 'Pro View';
        }
        
        // Update toggle icon
        const icon = viewModeToggle?.querySelector('i');
        if (icon) {
            icon.className = isProView ? 'fas fa-eye-slash' : 'fas fa-eye';
        }
        
        // Update pro-specific content
        if (isProView) {
            updateProAnalytics();
            updateStrategyTips();
            updateProbabilityDisplays();
        }
    }
    
    function updateProAnalytics() {
        if (gameState.viewMode !== 'pro') return;
        
        // Calculate win rate
        const winRateCalc = gameState.totalBattlesCount > 0 
            ? (gameState.totalWinsCount / gameState.totalBattlesCount * 100).toFixed(1)
            : 0;
        if (winRate) winRate.textContent = `${winRateCalc}%`;
        
        // Calculate average honor gain
        const avgHonor = gameState.totalBattlesCount > 0
            ? (gameState.totalHonorGained / gameState.totalBattlesCount).toFixed(1)
            : 0;
        if (avgHonorGain) avgHonorGain.textContent = avgHonor;
        
        // Calculate combat efficiency
        const efficiency = gameState.totalBattlesCount > 0
            ? Math.min(100, (gameState.totalWinsCount / gameState.totalBattlesCount * 100 + 
                            gameState.perfectRoundsCount / gameState.totalBattlesCount * 50)).toFixed(0)
            : 0;
        if (combatEfficiency) combatEfficiency.textContent = `${efficiency}%`;
        
        // Update streak record
        if (streakRecord) streakRecord.textContent = gameState.highestStreakCount;
        
        // Update bushido rating
        let rating = "Novice";
        if (gameState.honor >= 80) rating = "Master";
        else if (gameState.honor >= 60) rating = "Adept";
        else if (gameState.honor >= 40) rating = "Student";
        if (bushidoRating) bushidoRating.textContent = rating;
        
        // Update personal RTP
        const personalRTPCalc = gameState.totalBattlesCount > 0
            ? Math.min(100, (gameState.totalHonorGained / (gameState.totalBattlesCount * 15)) * 100).toFixed(1)
            : 0;
        if (personalRTP) personalRTP.textContent = `${personalRTPCalc}%`;
        
        // Update performance metrics
        if (totalBattles) totalBattles.textContent = gameState.totalBattlesCount;
        if (victories) victories.textContent = gameState.totalWinsCount;
        if (defeats) defeats.textContent = gameState.totalLossesCount;
        if (honorGained) honorGained.textContent = gameState.totalHonorGained;
        if (currentStreak) currentStreak.textContent = gameState.currentStreakCount;
        if (perfectRounds) perfectRounds.textContent = gameState.perfectRoundsCount;
        if (avgHonorPerBattle) avgHonorPerBattle.textContent = avgHonor;
        if (successRate) successRate.textContent = `${winRateCalc}%`;
    }
    
    function updateStrategyTips() {
        if (gameState.viewMode !== 'pro' || !strategyTip) return;
        
        let tip = "Analyze your opponent's stance to determine the best action.";
        
        // Generate specific strategy based on current state
        if (gameState.currentStreakCount >= 3) {
            tip = "🔥 You're on a winning streak! Consider taking calculated risks with Mighty Blow for maximum honor gain.";
        } else if (gameState.currentStreakCount === 0 && gameState.totalLossesCount > 0) {
            tip = "🛡️ Build confidence with Swift Strikes before attempting riskier techniques.";
        } else if (gameState.honor < 30) {
            tip = "⚠️ Low honor detected. Focus on consistent wins with Swift Strike to rebuild your reputation.";
        } else if (gameState.honor > 70) {
            tip = "👑 High honor unlocks advanced techniques. Use Precise Cut for balanced risk/reward.";
        }
        
        // Add opponent-specific advice if available
        if (gameState.opponentStance) {
            const stanceAdvice = {
                "Moon": "Moon stance opponents favor fluid attacks. Counter with Precise Cut.",
                "River": "River stance warriors use overwhelming force. Swift Strike works well.",
                "Mountain": "Mountain stance defenders are solid. Try Mighty Blow to break through."
            };
            
            if (stanceAdvice[gameState.opponentStance]) {
                tip += ` ${stanceAdvice[gameState.opponentStance]}`;
            }
        }
        
        strategyTip.textContent = tip;
    }
    
    function updateProbabilityDisplays() {
        if (gameState.viewMode !== 'pro') return;
        
        // Update action probability displays
        const swiftProbEl = document.getElementById('swiftProb');
        const preciseProbEl = document.getElementById('preciseProb');
        const mightyProbEl = document.getElementById('mightyProb');
        
        if (swiftProbEl) swiftProbEl.textContent = `${actions.swift.winChance}%`;
        if (preciseProbEl) preciseProbEl.textContent = `${actions.precise.winChance}%`;
        if (mightyProbEl) mightyProbEl.textContent = `${actions.mighty.winChance}%`;
        
        // Update detailed action info
        const swiftDetailsEl = document.getElementById('swiftDetails');
        const preciseDetailsEl = document.getElementById('preciseDetails');
        const mightyDetailsEl = document.getElementById('mightyDetails');
        
        if (swiftDetailsEl) swiftDetailsEl.textContent = `${actions.swift.winChance}% | 2.4x`;
        if (preciseDetailsEl) preciseDetailsEl.textContent = `${actions.precise.winChance}% | 4.8x`;
        if (mightyDetailsEl) mightyDetailsEl.textContent = `${actions.mighty.winChance}% | 20x`;
    }
    
    function generateNewRound() {
        gameState.roundNumber++;
        gameState.currentRoundId++;
        gameState.battleInProgress = false;
        
        // Generate new seeds for provably fair
        gameState.serverSeed = generateRandomSeed();
        gameState.clientSeedValue = generateRandomSeed();
        
        // Generate opponent stance
        gameState.opponentStance = stances[Math.floor(Math.random() * stances.length)];
        
        // Update UI
        updateFairnessDisplay();
        updateOpponentDisplay();
        updateRoundCounter();
        
        // Log new round
        appendToBattleLog(`Round ${gameState.roundNumber} begins. Your opponent assumes ${gameState.opponentStance} stance.`);
        
        // Update strategy tips for pro view
        if (gameState.viewMode === 'pro') {
            updateStrategyTips();
            updateProAnalytics();
        }
    }
    
    function executeAction(actionType) {
        if (gameState.battleInProgress) {
            showNotification("Battle in progress. Please wait.", 'warning');
            return;
        }
        
        gameState.battleInProgress = true;
        const action = actions[actionType];
        
        if (!action) {
            console.error("Invalid action type:", actionType);
            return;
        }
        
        // Disable action buttons
        setActionButtonsEnabled(false);
        
        // Update player stance indicator
        if (playerStanceIndicator) {
            playerStanceIndicator.textContent = `${action.name} prepared`;
        }
        
        // Log action selection
        appendToBattleLog(`You prepare ${action.name} (${action.kanji})...`);
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
        
        // Simulate battle with delay for dramatic effect
        setTimeout(() => {
            resolveBattle(actionType);
        }, 1000);
    }
    
    function resolveBattle(actionType) {
        const action = actions[actionType];
        
        // Track strategy usage
        gameState.strategyEffectiveness[actionType].attempts++;
        
        // Determine outcome using provably fair system
        const battleResult = calculateBattleOutcome(actionType);
        
        // Update battle statistics
        gameState.totalBattlesCount++;
        gameState.lastBattleResult = battleResult;
        
        if (battleResult.success) {
            gameState.totalWinsCount++;
            gameState.currentStreakCount++;
            gameState.highestStreakCount = Math.max(gameState.highestStreakCount, gameState.currentStreakCount);
            gameState.strategyEffectiveness[actionType].wins++;
            gameState.strategyEffectiveness[actionType].honorGained += battleResult.honorGain;
            
            if (battleResult.outcome.type === "Critical Strike") {
                gameState.perfectRoundsCount++;
            }
        } else {
            gameState.totalLossesCount++;
            gameState.currentStreakCount = 0;
        }
        
        // Apply honor changes
        const previousHonor = gameState.honor;
        if (battleResult.success) {
            gameState.honor += battleResult.honorGain;
            gameState.totalHonorGained += battleResult.honorGain;
            gameState.lastHonorChange = battleResult.honorGain;
        } else {
            gameState.honor = Math.max(0, gameState.honor - 5);
            gameState.lastHonorChange = -5;
        }
        
        // Cap honor at 100
        gameState.honor = Math.min(100, gameState.honor);
        
        // Store battle result for history
        gameState.battleResults.push({
            round: gameState.roundNumber,
            action: actionType,
            opponent: gameState.opponentStance,
            result: battleResult,
            honorBefore: previousHonor,
            honorAfter: gameState.honor
        });
        
        // Add to past rounds for verification
        gameState.pastRounds.push({
            roundId: gameState.currentRoundId,
            serverSeed: gameState.serverSeed,
            clientSeed: gameState.clientSeedValue,
            action: actionType,
            result: battleResult
        });
        
        // Keep only last 10 rounds for performance
        if (gameState.pastRounds.length > 10) {
            gameState.pastRounds.shift();
        }
        
        // Show battle animation
        showBattleAnimation(battleResult);
        
        // Update UI after animation
        setTimeout(() => {
            updateAllUI();
            updateProViewDisplay();
            
            // Log battle result
            logBattleResult(actionType, battleResult);
            
            // Show outcome modal
            showOutcomeModal(battleResult);
            
            // Re-enable action buttons
            setActionButtonsEnabled(true);
            gameState.battleInProgress = false;
            
            // Save game state
            saveGameState();
        }, 2000);
    }
    
    function calculateBattleOutcome(actionType) {
        const action = actions[actionType];
        
        // Generate deterministic random number using seeds
        const combinedSeed = gameState.serverSeed + gameState.clientSeedValue + gameState.currentRoundId;
        const hash = simpleHash(combinedSeed);
        const randomValue = (hash % 10000) / 10000; // 0-1 range
        
        // Determine if action succeeds
        const success = randomValue < (action.winChance / 100);
        
        if (!success) {
            return {
                success: false,
                outcome: null,
                honorGain: 0,
                multiplier: 0
            };
        }
        
        // Determine specific outcome
        let cumulativeChance = 0;
        for (const outcome of action.outcomes) {
            cumulativeChance += outcome.chance;
            if ((randomValue * 100) <= cumulativeChance) {
                return {
                    success: true,
                    outcome: outcome,
                    honorGain: outcome.honor,
                    multiplier: outcome.multiplier
                };
            }
        }
        
        // Fallback to first outcome
        return {
            success: true,
            outcome: action.outcomes[0],
            honorGain: action.outcomes[0].honor,
            multiplier: action.outcomes[0].multiplier
        };
    }
    
    function simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash);
    }
    
    function showBattleAnimation(battleResult) {
        if (!battleEffects) return;
        
        // Show battle effects
        battleEffects.classList.add('active');
        
        if (battleResult.success) {
            battleEffects.textContent = '⚔️';
            battleEffects.style.color = '#4caf50';
            
            // Add particles for successful hits
            createBattleParticles(true);
            
            // Haptic feedback for success
            if (navigator.vibrate) {
                if (battleResult.outcome.type === "Critical Strike") {
                    navigator.vibrate([200, 100, 200, 100, 200]); // Critical hit
                } else {
                    navigator.vibrate([100, 50, 100]); // Regular hit
                }
            }
        } else {
            battleEffects.textContent = '🛡️';
            battleEffects.style.color = '#f44336';
            
            // Add particles for blocked attacks
            createBattleParticles(false);
            
            // Haptic feedback for failure
            if (navigator.vibrate) {
                navigator.vibrate([500]); // Miss/block
            }
        }
        
        // Remove effects after animation
        setTimeout(() => {
            battleEffects.classList.remove('active');
        }, 1500);
    }
    
    function createBattleParticles(success) {
        if (!battleParticles) return;
        
        const particleCount = success ? 20 : 10;
        const color = success ? '#4caf50' : '#f44336';
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random position around center
            const angle = (i / particleCount) * 2 * Math.PI;
            const distance = Math.random() * 50 + 20;
            const x = 50 + Math.cos(angle) * distance;
            const y = 50 + Math.sin(angle) * distance;
            
            particle.style.cssText = `
                position: absolute;
                left: ${x}%;
                top: ${y}%;
                width: 4px;
                height: 4px;
                background: ${color};
                border-radius: 50%;
                opacity: 1;
                animation: particleExplosion 1s ease-out forwards;
                animation-delay: ${i * 0.05}s;
            `;
            
            battleParticles.appendChild(particle);
            
            // Remove particle after animation
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1000 + (i * 50));
        }
        
        // Add explosion animation if not exists
        if (!document.querySelector('#particle-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'particle-animation-styles';
            style.textContent = `
                @keyframes particleExplosion {
                    0% { transform: scale(1) translate(0, 0); opacity: 1; }
                    100% { transform: scale(0) translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    function showActionPreview(button) {
        // Show quick tooltip with action details
        const actionType = button.id.replace('Btn', '').replace('Strike', '').replace('Cut', '').replace('Blow', '');
        const actionMap = {
            'swift': 'swift',
            'precise': 'precise',
            'mighty': 'mighty'
        };
        
        const action = actions[actionMap[actionType]];
        if (!action) return;
        
        const preview = document.createElement('div');
        preview.className = 'action-preview';
        preview.innerHTML = `
            <div class="preview-title">${action.name}</div>
            <div class="preview-chance">Win Chance: ${action.winChance}%</div>
            <div class="preview-outcomes">
                ${action.outcomes.map(outcome => 
                    `<div class="outcome-line">${outcome.type}: ${outcome.multiplier}x (${outcome.chance}%)</div>`
                ).join('')}
            </div>
        `;
        
        preview.style.cssText = `
            position: absolute;
            top: -150px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-dark);
            color: var(--text-light);
            padding: 10px;
            border-radius: 6px;
            border: 1px solid var(--accent-blue);
            font-size: 12px;
            z-index: 1000;
            pointer-events: none;
            min-width: 200px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        button.style.position = 'relative';
        button.appendChild(preview);
        
        // Remove preview after delay
        setTimeout(() => {
            if (preview.parentNode) {
                preview.parentNode.removeChild(preview);
            }
        }, 3000);
    }
    
    function logBattleResult(actionType, battleResult) {
        const action = actions[actionType];
        let logMessage = `${action.name} executed. `;
        
        if (battleResult.success) {
            logMessage += `${battleResult.outcome.type}! `;
            logMessage += `Honor gained: +${battleResult.honorGain}. `;
            logMessage += `Multiplier: ${battleResult.multiplier}x`;
            
            if (battleResult.outcome.type === "Critical Strike") {
                logMessage += " 🌟 Perfect technique!";
            }
        } else {
            logMessage += "Attack blocked by opponent's defense. Honor lost: -5";
        }
        
        appendToBattleLog(logMessage);
        
        // Add streak information
        if (gameState.currentStreakCount > 1) {
            appendToBattleLog(`🔥 Winning streak: ${gameState.currentStreakCount}!`);
        } else if (gameState.currentStreakCount === 0 && battleResult.success) {
            appendToBattleLog("Streak broken. Focus and rebuild your momentum.");
        }
    }
    
    function showOutcomeModal(battleResult) {
        if (!outcomeModal) return;
        
        const titleEl = document.getElementById('outcomeAnimTitle');
        const imageEl = document.getElementById('outcomeAnimImage');
        const messageEl = document.getElementById('outcomeAnimMessage');
        
        if (battleResult.success) {
            if (titleEl) titleEl.textContent = `${battleResult.outcome.type}!`;
            if (imageEl) imageEl.textContent = battleResult.outcome.type === "Critical Strike" ? '🌟' : '⚔️';
            if (messageEl) {
                messageEl.innerHTML = `
                    <p>Your technique was successful!</p>
                    <p>Honor gained: <strong>+${battleResult.honorGain}</strong></p>
                    <p>Multiplier: <strong>${battleResult.multiplier}x</strong></p>
                    ${gameState.currentStreakCount > 1 ? `<p>🔥 Streak: ${gameState.currentStreakCount}</p>` : ''}
                `;
            }
        } else {
            if (titleEl) titleEl.textContent = "Blocked!";
            if (imageEl) imageEl.textContent = '🛡️';
            if (messageEl) {
                messageEl.innerHTML = `
                    <p>Your opponent successfully defended.</p>
                    <p>Honor lost: <strong>-5</strong></p>
                    <p>Analyze their stance and try again.</p>
                `;
            }
        }
        
        toggleModal(outcomeModal, true);
    }
    
    function setActionButtonsEnabled(enabled) {
        const buttons = [swiftStrikeBtn, preciseCutBtn, mightyBlowBtn];
        
        buttons.forEach(button => {
            if (button) {
                button.disabled = !enabled;
                button.style.opacity = enabled ? 1 : 0.6;
                button.style.pointerEvents = enabled ? 'auto' : 'none';
            }
        });
    }
    
    function updateAllUI() {
        // Update honor display
        if (honorFill) {
            honorFill.style.width = `${gameState.honor}%`;
        }
        if (honorText) {
            honorText.textContent = `${gameState.honor}/100`;
        }
        
        // Add honor change animation
        if (gameState.lastHonorChange !== 0) {
            const honorContainer = document.querySelector('.honor-meter');
            if (honorContainer) {
                honorContainer.classList.add('honor-gain');
                setTimeout(() => {
                    honorContainer.classList.remove('honor-gain');
                }, 1000);
            }
        }
        
        // Update status displays
        if (currentStreakDisplay) currentStreakDisplay.textContent = gameState.currentStreakCount;
        if (highestStreak) highestStreak.textContent = gameState.highestStreakCount;
        if (totalWins) totalWins.textContent = gameState.totalWinsCount;
        if (totalLosses) totalLosses.textContent = gameState.totalLossesCount;
        
        // Update next rank indicator
        if (nextRank) {
            let next = "Samurai";
            if (gameState.honor >= 80) next = "Master";
            else if (gameState.honor >= 60) next = "Daimyo";
            else if (gameState.honor >= 40) next = "Hatamoto";
            nextRank.textContent = next;
        }
    }
    
    function updateFairnessDisplay() {
        // Hash the server seed for display
        const hashedSeed = simpleHash(gameState.serverSeed).toString(16);
        
        if (serverSeedHash) {
            serverSeedHash.textContent = hashedSeed.substring(0, 16) + "...";
        }
        if (clientSeed) {
            clientSeed.textContent = gameState.clientSeedValue.substring(0, 16) + "...";
        }
        if (roundId) {
            roundId.textContent = gameState.currentRoundId;
        }
        if (verificationHash) {
            const verifyHash = simpleHash(gameState.serverSeed + gameState.clientSeedValue + gameState.currentRoundId);
            verificationHash.textContent = verifyHash.toString(16).substring(0, 16) + "...";
        }
    }
    
    function updateOpponentDisplay() {
        if (opponentStanceIndicator) {
            opponentStanceIndicator.textContent = `${gameState.opponentStance} stance`;
        }
        
        // Pro View opponent analysis
        if (gameState.viewMode === 'pro') {
            if (playerAdvantage) {
                // Calculate advantage based on historical data
                const stanceHistory = gameState.battleResults.filter(r => r.opponent === gameState.opponentStance);
                const winRate = stanceHistory.length > 0 
                    ? (stanceHistory.filter(r => r.result.success).length / stanceHistory.length * 100).toFixed(0)
                    : 50;
                
                if (winRate > 60) {
                    playerAdvantage.textContent = "Advantage";
                    playerAdvantage.style.color = '#4caf50';
                } else if (winRate < 40) {
                    playerAdvantage.textContent = "Disadvantage";
                    playerAdvantage.style.color = '#f44336';
                } else {
                    playerAdvantage.textContent = "Neutral";
                    playerAdvantage.style.color = '#ffc107';
                }
            }
            
            if (opponentThreat) {
                const threatLevels = ["Low", "Medium", "High"];
                const threatLevel = threatLevels[Math.floor(Math.random() * threatLevels.length)];
                opponentThreat.textContent = threatLevel;
                
                const colors = { "Low": "#4caf50", "Medium": "#ffc107", "High": "#f44336" };
                opponentThreat.style.color = colors[threatLevel];
            }
        }
    }
    
    function updateRoundCounter() {
        if (roundCounter) {
            roundCounter.textContent = `Round: ${gameState.roundNumber}`;
        }
    }
    
    function appendToBattleLog(message) {
        if (!logEntries) return;
        
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.textContent = message;
        
        // Add timestamp for pro view
        if (gameState.viewMode === 'pro') {
            const timestamp = new Date().toLocaleTimeString();
            logEntry.innerHTML = `<span style="opacity: 0.7; font-size: 11px;">[${timestamp}]</span> ${message}`;
        }
        
        logEntries.appendChild(logEntry);
        
        // Scroll to bottom
        logEntries.scrollTop = logEntries.scrollHeight;
        
        // Limit log entries to prevent memory issues
        while (logEntries.children.length > 50) {
            logEntries.removeChild(logEntries.firstChild);
        }
    }
    
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        const colors = {
            success: '#4caf50',
            warning: '#ffc107',
            error: '#f44336',
            info: '#2196f3'
        };
        
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            z-index: 2000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
        
        // Add animation styles if not exists
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    function toggleModal(modal, show) {
        if (!modal) return;
        
        if (show) {
            modal.classList.remove('hidden');
            // Focus management for accessibility
            const closeButton = modal.querySelector('.close-modal-btn');
            if (closeButton) {
                closeButton.focus();
            }
        } else {
            modal.classList.add('hidden');
        }
    }
    
    function generateRandomSeed() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    
    function saveGameState() {
        try {
            localStorage.setItem('blueSamuraiGameState', JSON.stringify(gameState));
        } catch (e) {
            console.warn('Could not save game state:', e);
        }
    }
    
    function loadGameState() {
        try {
            const saved = localStorage.getItem('blueSamuraiGameState');
            if (saved) {
                const parsedState = JSON.parse(saved);
                // Merge saved state with current state, keeping structure intact
                Object.assign(gameState, parsedState);
                
                // Reset certain values for new session
                gameState.battleInProgress = false;
                gameState.roundNumber = parsedState.roundNumber || 0;
                gameState.currentRoundId = parsedState.currentRoundId || 0;
            }
        } catch (e) {
            console.warn('Could not load game state:', e);
        }
    }
    
    // Utility functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Initialize layout on load
    updateLayoutForScreenSize();
    
    // Auto-save every 30 seconds
    setInterval(saveGameState, 30000);
    
    // Check for wake from sleep and regenerate round if needed
    let lastActive = Date.now();
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
            const now = Date.now();
            if (now - lastActive > 300000) { // 5 minutes
                // Long pause detected, refresh round
                generateNewRound();
                showNotification("Welcome back, samurai! A new challenge awaits.", 'info');
            }
            lastActive = now;
        }
    });
});