// Mobile-First Responsive Craps Game with Pro View
class CrapsGame {
    constructor() {
        this.gameState = {
            // Player stats
            totalGames: 0,
            gamesWon: 0,
            
            // Game phase
            phase: 'come-out', // 'come-out' or 'point'
            point: null,
            
            // Bets
            selectedBetType: 'pass-line',
            activeBets: {},
            betAmounts: {}, // Map of bet type to bet amount
            
            // Dice
            dice: [1, 1],
            rolling: false,
            
            // Fairness
            clientSeed: '',
            serverSeed: '',
            hashedServerSeed: '',
            nonce: 0,
            rollHistory: [],
            revealedSeeds: {},
            
            // Currency
            balance: 1000,
            defaultBetAmount: 50,
            
            // View mode
            viewMode: 'standard', // 'standard' or 'pro'
            
            // Payouts for different bet types
            payouts: {
                'pass-line': 1,      // 1:1 payout
                'dont-pass': 1,      // 1:1 payout
                'any-seven': 4,      // 4:1 payout
                'any-craps': 7,      // 7:1 payout
                'hard-4': 7,         // 7:1 payout
                'hard-6': 9,         // 9:1 payout
                'hard-8': 9,         // 9:1 payout
                'hard-10': 7         // 7:1 payout
            }
        };

        // Constants
        this.BET_TYPES = {
            'pass-line': { name: 'Pass Line', winChance: 0.4929, houseEdge: 0.0141 },
            'dont-pass': { name: 'Don\'t Pass', winChance: 0.4793, houseEdge: 0.0136 },
            'any-seven': { name: 'Any Seven', winChance: 0.1667, houseEdge: 0.1667 },
            'any-craps': { name: 'Any Craps', winChance: 0.1111, houseEdge: 0.1111 },
            'hard-4': { name: 'Hard 4', winChance: 0.0278, houseEdge: 0.1111 },
            'hard-6': { name: 'Hard 6', winChance: 0.0417, houseEdge: 0.0909 },
            'hard-8': { name: 'Hard 8', winChance: 0.0417, houseEdge: 0.0909 },
            'hard-10': { name: 'Hard 10', winChance: 0.0278, houseEdge: 0.1111 }
        };

        this.init();
    }

    init() {
        // Load saved data
        this.loadGameData();
        
        // Generate new seeds
        this.generateNewSeeds();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Create view mode toggle
        this.createViewModeToggle();
        
        // Update display
        this.updateDisplay();
        this.renderDice();
        this.populateRollHistory();
        this.updateCurrentBetDisplay();
        
        // Set initial view mode
        this.setViewMode(this.gameState.viewMode);
        
        // Mobile-specific setup
        this.setupMobileFeatures();
    }

    setupEventListeners() {
        // Bet type selectors
        document.querySelectorAll('.bet-type').forEach(element => {
            element.addEventListener('click', (e) => {
                const betType = e.target.getAttribute('data-bet');
                this.selectBetType(betType);
            });
        });

        // Prop bets - ensure they're always clickable
        document.querySelectorAll('.prop-bet').forEach(element => {
            element.addEventListener('click', (e) => {
                e.stopPropagation();
                const betType = element.getAttribute('data-bet');
                this.placeBet(betType);
            });
        });

        // Pass/Don't Pass lines
        document.getElementById('passLine')?.addEventListener('click', () => {
            this.placeBet('pass-line');
        });

        document.getElementById('dontPass')?.addEventListener('click', () => {
            this.placeBet('dont-pass');
        });

        // Game controls
        document.getElementById('rollBtn')?.addEventListener('click', () => {
            this.rollDice();
        });

        document.getElementById('newGameBtn')?.addEventListener('click', () => {
            this.newGame();
        });

        // Bet amount controls
        this.setupBetControls();

        // Touch and gesture support
        this.setupTouchSupport();
    }

    setupBetControls() {
        // Create bet controls if they don't exist
        const existingControls = document.querySelector('.bet-controls');
        if (!existingControls) {
            const betSelector = document.querySelector('.bet-selector');
            if (betSelector) {
                const betControls = document.createElement('div');
                betControls.className = 'bet-controls';
                betControls.innerHTML = `
                    <div class="bet-amount-control">
                        <button class="bet-adjust-btn" id="decreaseBet">-</button>
                        <span class="current-bet" id="currentBetAmount">${this.gameState.defaultBetAmount} GA</span>
                        <button class="bet-adjust-btn" id="increaseBet">+</button>
                    </div>
                    <div class="total-bets">
                        <span>Active Bets: </span>
                        <span class="total-bet-amount" id="totalBetAmount">0 GA</span>
                    </div>
                `;
                betSelector.parentNode.insertBefore(betControls, betSelector.nextSibling);
            }
        }

        // Bet adjustment buttons
        document.getElementById('decreaseBet')?.addEventListener('click', () => {
            this.adjustBetAmount(-10);
        });

        document.getElementById('increaseBet')?.addEventListener('click', () => {
            this.adjustBetAmount(10);
        });
    }

    setupTouchSupport() {
        // Add touch feedback for all interactive elements
        const interactiveElements = document.querySelectorAll(
            '.bet-type, .prop-bet, .pass-line, .dont-pass, .roll-btn, .new-game-btn, .bet-adjust-btn'
        );

        interactiveElements.forEach(element => {
            // Add touch start/end effects
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.95)';
                element.style.transition = 'transform 0.1s ease';
            });

            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = '';
                }, 100);
            });

            element.addEventListener('touchcancel', (e) => {
                element.style.transform = '';
                element.style.transition = '';
            });
        });

        // Prevent double-tap zoom on game elements
        document.querySelectorAll('.craps-table, .dice-area, .game-controls').forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
    }

    setupMobileFeatures() {
        // Mobile-specific optimizations
        if (this.isMobile()) {
            // Adjust dice size for mobile
            const dice = document.querySelectorAll('.die');
            dice.forEach(die => {
                die.style.width = '50px';
                die.style.height = '50px';
                die.style.fontSize = '1.2rem';
            });

            // Optimize prop bets for mobile
            const propBets = document.querySelectorAll('.prop-bet');
            propBets.forEach(bet => {
                bet.style.fontSize = '0.6rem';
                bet.style.padding = '0.25rem';
            });

            // Add haptic feedback if available
            this.setupHapticFeedback();
        }
    }

    setupHapticFeedback() {
        // Vibration API for mobile devices
        if ('vibrate' in navigator) {
            this.vibrate = (pattern) => {
                navigator.vibrate(pattern);
            };
        } else {
            this.vibrate = () => {}; // No-op for devices without vibration
        }
    }

    isMobile() {
        return window.innerWidth <= 768 || 'ontouchstart' in window;
    }

    createViewModeToggle() {
        // Create view mode toggle if it doesn't exist
        let toggle = document.querySelector('.view-mode-toggle');
        if (!toggle) {
            toggle = document.createElement('div');
            toggle.className = 'view-mode-toggle';
            toggle.innerHTML = `
                <button class="view-toggle-btn ${this.gameState.viewMode === 'standard' ? 'active' : ''}" data-mode="standard">
                    Standard
                </button>
                <button class="view-toggle-btn ${this.gameState.viewMode === 'pro' ? 'active' : ''}" data-mode="pro">
                    Pro View
                </button>
            `;
            document.body.appendChild(toggle);

            // Add event listeners
            toggle.querySelectorAll('.view-toggle-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const mode = e.target.getAttribute('data-mode');
                    this.setViewMode(mode);
                });
            });
        }
    }

    setViewMode(mode) {
        this.gameState.viewMode = mode;
        
        // Update toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-mode') === mode) {
                btn.classList.add('active');
            }
        });

        // Update body class
        document.body.classList.remove('standard-view', 'pro-view-active');
        if (mode === 'pro') {
            document.body.classList.add('pro-view-active');
        } else {
            document.body.classList.add('standard-view');
        }

        // Save preference
        localStorage.setItem('crapsViewMode', mode);
        
        // Show notification
        this.showNotification(`Switched to ${mode === 'pro' ? 'Pro' : 'Standard'} view`, 'info');
    }

    loadGameData() {
        // Load saved game data from localStorage
        const savedData = localStorage.getItem('crapsGameData');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                this.gameState = { ...this.gameState, ...data };
            } catch (e) {
                console.warn('Failed to load saved game data');
            }
        }

        // Load view mode preference
        const savedViewMode = localStorage.getItem('crapsViewMode');
        if (savedViewMode) {
            this.gameState.viewMode = savedViewMode;
        }
    }

    saveGameData() {
        // Save game data to localStorage
        const dataToSave = {
            totalGames: this.gameState.totalGames,
            gamesWon: this.gameState.gamesWon,
            balance: this.gameState.balance,
            rollHistory: this.gameState.rollHistory.slice(0, 10), // Keep only last 10 rolls
            viewMode: this.gameState.viewMode
        };
        localStorage.setItem('crapsGameData', JSON.stringify(dataToSave));
    }

    generateNewSeeds() {
        // Generate client seed (random string)
        this.gameState.clientSeed = this.generateRandomString(32);
        
        // Generate server seed and hash it
        this.gameState.serverSeed = this.generateRandomString(32);
        this.gameState.hashedServerSeed = this.sha256(this.gameState.serverSeed);
        
        // Update display
        this.updateSeedDisplay();
    }

    updateSeedDisplay() {
        const clientSeedEl = document.getElementById('clientSeed');
        const hashedServerSeedEl = document.getElementById('hashedServerSeed');
        const nonceEl = document.getElementById('nonce');

        if (clientSeedEl) clientSeedEl.textContent = this.gameState.clientSeed;
        if (hashedServerSeedEl) hashedServerSeedEl.textContent = this.gameState.hashedServerSeed;
        if (nonceEl) nonceEl.textContent = this.gameState.nonce;
    }

    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    sha256(ascii) {
        // Simple SHA-256 implementation for demo purposes
        function rightRotate(value, amount) {
            return (value >>> amount) | (value << (32 - amount));
        }
        
        let mathPow = Math.pow;
        let maxWord = mathPow(2, 32);
        let lengthProperty = 'length';
        let i, j;
        let result = '';

        let words = [];
        let asciiBitLength = ascii[lengthProperty] * 8;
        
        let hash = this.sha256.h = this.sha256.h || [];
        let k = this.sha256.k = this.sha256.k || [];
        let primeCounter = k[lengthProperty];

        let isComposite = {};
        for (let candidate = 2; primeCounter < 64; candidate++) {
            if (!isComposite[candidate]) {
                for (i = 0; i < 313; i += candidate) {
                    isComposite[i] = candidate;
                }
                hash[primeCounter] = (mathPow(candidate, .5) * maxWord) | 0;
                k[primeCounter++] = (mathPow(candidate, 1/3) * maxWord) | 0;
            }
        }
        
        ascii += '\x80';
        while (ascii[lengthProperty] % 64 - 56) ascii += '\x00';
        for (i = 0; i < ascii[lengthProperty]; i++) {
            j = ascii.charCodeAt(i);
            if (j >> 8) return;
            words[i >> 2] |= j << ((3 - i) % 4) * 8;
        }
        words[words[lengthProperty]] = ((asciiBitLength / maxWord) | 0);
        words[words[lengthProperty]] = (asciiBitLength);
        
        for (j = 0; j < words[lengthProperty];) {
            let w = words.slice(j, j += 16);
            let oldHash = hash;
            hash = hash.slice(0, 8);
            
            for (i = 0; i < 64; i++) {
                let w15 = w[i - 15], w2 = w[i - 2];
                let a = hash[0], e = hash[4];
                let temp1 = hash[7]
                    + (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25))
                    + ((e & hash[5]) ^ ((~e) & hash[6]))
                    + k[i]
                    + (w[i] = (i < 16) ? w[i] : (
                        w[i - 16]
                        + (rightRotate(w15, 7) ^ rightRotate(w15, 18) ^ (w15 >>> 3))
                        + w[i - 7]
                        + (rightRotate(w2, 17) ^ rightRotate(w2, 19) ^ (w2 >>> 10))
                    ) | 0
                );
                let temp2 = (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22))
                    + ((a & hash[1]) ^ (a & hash[2]) ^ (hash[1] & hash[2]));
                
                hash = [(temp1 + temp2) | 0].concat(hash);
                hash[4] = (hash[4] + temp1) | 0;
            }
            
            for (i = 0; i < 8; i++) {
                hash[i] = (hash[i] + oldHash[i]) | 0;
            }
        }
        
        for (i = 0; i < 8; i++) {
            for (j = 3; j + 1; j--) {
                let b = (hash[i] >> (j * 8)) & 255;
                result += ((b < 16) ? 0 : '') + b.toString(16);
            }
        }
        return result;
    }

    selectBetType(betType) {
        // Update selected bet type
        this.gameState.selectedBetType = betType;
        
        // Update UI
        document.querySelectorAll('.bet-type').forEach(element => {
            element.classList.remove('selected');
        });
        const selectedElement = document.querySelector(`.bet-type[data-bet="${betType}"]`);
        if (selectedElement) {
            selectedElement.classList.add('selected');
        }
        
        // Show potential payout based on selected bet type
        const payout = this.gameState.payouts[betType];
        const betAmount = this.gameState.defaultBetAmount;
        const potentialWin = betAmount * payout;
        
        // Show notification
        this.showNotification(`Selected ${this.BET_TYPES[betType].name} bet (${payout}:1 payout, potential win: ${potentialWin} GA)`);
        
        // Haptic feedback
        this.vibrate([50]);
    }

    adjustBetAmount(amount) {
        const newBet = this.gameState.defaultBetAmount + amount;
        
        // Ensure bet is at least 10 and not more than balance
        if (newBet >= 10 && newBet <= this.gameState.balance) {
            this.gameState.defaultBetAmount = newBet;
            this.updateCurrentBetDisplay();
            this.vibrate([30]);
        } else if (newBet < 10) {
            this.showNotification("Minimum bet is 10 GA", "info");
            this.vibrate([100, 50, 100]);
        } else {
            this.showNotification("Bet cannot exceed your balance", "info");
            this.vibrate([100, 50, 100]);
        }
    }

    updateCurrentBetDisplay() {
        const currentBetEl = document.getElementById('currentBetAmount');
        if (currentBetEl) {
            currentBetEl.textContent = `${this.gameState.defaultBetAmount} GA`;
        }
        
        // Calculate and update total bet amount
        let totalBet = 0;
        for (const betType in this.gameState.betAmounts) {
            totalBet += this.gameState.betAmounts[betType];
        }
        const totalBetEl = document.getElementById('totalBetAmount');
        if (totalBetEl) {
            totalBetEl.textContent = `${totalBet} GA`;
        }
    }

    placeBet(betType) {
        // Can't place bets while dice are rolling
        if (this.gameState.rolling) {
            return;
        }
        
        // Update selected bet type if clicked directly on table
        if (betType) {
            this.gameState.selectedBetType = betType;
            document.querySelectorAll('.bet-type').forEach(element => {
                element.classList.remove('selected');
            });
            
            const betTypeElement = document.querySelector(`.bet-type[data-bet="${betType}"]`);
            if (betTypeElement) {
                betTypeElement.classList.add('selected');
            }
        } else {
            betType = this.gameState.selectedBetType;
        }
        
        // Check if player has enough balance
        const betAmount = this.gameState.defaultBetAmount;
        
        // Toggle bet
        if (this.gameState.activeBets[betType]) {
            // Remove bet
            delete this.gameState.activeBets[betType];
            
            // Return bet amount to balance
            if (this.gameState.betAmounts[betType]) {
                this.gameState.balance += this.gameState.betAmounts[betType];
                delete this.gameState.betAmounts[betType];
            }
            
            this.showNotification(`Removed ${this.BET_TYPES[betType].name} bet`, "info");
            this.vibrate([50]);
        } else {
            // Check if player has enough balance for new bet
            if (this.gameState.balance < betAmount) {
                this.showNotification("Insufficient balance for this bet", "error");
                this.vibrate([100, 50, 100]);
                return;
            }
            
            // Place new bet
            this.gameState.activeBets[betType] = true;
            this.gameState.betAmounts[betType] = betAmount;
            this.gameState.balance -= betAmount;
            
            // Calculate potential win
            const payout = this.gameState.payouts[betType];
            const potentialWin = betAmount * payout;
            
            this.showNotification(`Placed ${this.BET_TYPES[betType].name} bet: ${betAmount} GA (potential win: ${potentialWin} GA)`, "info");
            this.vibrate([50, 50, 50]);
        }
        
        // Update UI
        this.updateBetDisplay();
        this.updateDisplay();
        this.updateCurrentBetDisplay();
    }

    updateBetDisplay() {
        // Reset all visual states
        document.querySelectorAll('.pass-line, .dont-pass, .prop-bet').forEach(element => {
            element.classList.remove('selected');
        });
        
        // Remove any existing bet chip elements
        document.querySelectorAll('.bet-chip').forEach(chip => {
            chip.remove();
        });
        
        // Highlight active bets and add bet chips
        for (const betType in this.gameState.activeBets) {
            let betElement;
            
            if (betType === 'pass-line') {
                betElement = document.getElementById('passLine');
                if (betElement) betElement.classList.add('selected');
            } else if (betType === 'dont-pass') {
                betElement = document.getElementById('dontPass');
                if (betElement) betElement.classList.add('selected');
            } else {
                betElement = document.querySelector(`.prop-bet[data-bet="${betType}"]`);
                if (betElement) {
                    betElement.classList.add('selected');
                }
            }
            
            // Add bet chip showing amount if there's a bet amount
            if (betElement && this.gameState.betAmounts[betType]) {
                const betChip = document.createElement('div');
                betChip.className = 'bet-chip';
                betChip.textContent = this.gameState.betAmounts[betType];
                
                betElement.style.position = 'relative';
                betElement.appendChild(betChip);
            }
        }
    }

    rollDice() {
        // Must have at least one bet to roll
        if (Object.keys(this.gameState.activeBets).length === 0) {
            this.showNotification('Please place at least one bet', 'error');
            this.vibrate([100, 50, 100]);
            return;
        }
        
        // Start rolling animation
        this.gameState.rolling = true;
        const rollBtn = document.getElementById('rollBtn');
        if (rollBtn) rollBtn.disabled = true;
        
        // Animate dice
        const die1 = document.getElementById('die1');
        const die2 = document.getElementById('die2');
        if (die1) die1.classList.add('rolling');
        if (die2) die2.classList.add('rolling');
        
        // Play rolling sound and vibration
        this.playSound('rolling');
        this.vibrate([50, 50, 50, 50, 50]);
        
        // Generate dice outcome using provably fair algorithm
        const diceOutcome = this.calculateDiceOutcome();
        
        // Update game state
        this.gameState.dice = diceOutcome;
        
        // Stop rolling after animation
        setTimeout(() => {
            if (die1) die1.classList.remove('rolling');
            if (die2) die2.classList.remove('rolling');
            
            // Render final dice values
            this.renderDice();
            
            // Add to roll history before processing outcome
            this.addToHistory(diceOutcome);
            
            // Process roll outcome
            this.processRollOutcome();
            
            // Update game state
            this.gameState.rolling = false;
            if (rollBtn) rollBtn.disabled = false;
            
            // Increment nonce and generate new seeds for next roll
            this.gameState.nonce++;
            this.generateNewSeeds();
            
            // Update display
            this.updateDisplay();
            this.populateRollHistory();
            
            // Clear active bets after roll is processed
            this.gameState.activeBets = {};
            this.gameState.betAmounts = {};
            this.updateBetDisplay();
            this.updateCurrentBetDisplay();
            
            // Save game data
            this.saveGameData();
            
            // Auto-refill if broke
            if (this.gameState.balance < this.gameState.defaultBetAmount) {
                const refill = Math.max(100, this.gameState.defaultBetAmount * 3);
                this.gameState.balance += refill;
                this.showNotification(`Balance refilled with ${refill} GA`, 'info');
                this.updateDisplay();
            }
        }, 1500);
    }

    calculateDiceOutcome() {
        // Combine seeds and nonce
        const combinedSeed = this.gameState.clientSeed + this.gameState.serverSeed + this.gameState.nonce;
        const hash = this.sha256(combinedSeed);
        
        // Use first 8 characters of hash for die 1
        const hexSubstring1 = hash.substring(0, 8);
        const decimal1 = parseInt(hexSubstring1, 16);
        const die1 = (decimal1 % 6) + 1; // 1-6
        
        // Use next 8 characters of hash for die 2
        const hexSubstring2 = hash.substring(8, 16);
        const decimal2 = parseInt(hexSubstring2, 16);
        const die2 = (decimal2 % 6) + 1; // 1-6
        
        return [die1, die2];
    }

    processRollOutcome() {
        const diceSum = this.gameState.dice[0] + this.gameState.dice[1];
        const diceIsHardway = this.gameState.dice[0] === this.gameState.dice[1] && [4, 6, 8, 10].includes(diceSum);
        
        // Process based on game phase
        if (this.gameState.phase === 'come-out') {
            this.processComeOutRoll(diceSum);
        } else {
            this.processPointRoll(diceSum);
        }
        
        // Process proposition bets
        this.processPropositionBets(diceSum, diceIsHardway);
    }

    processComeOutRoll(diceSum) {
        const gameMessage = document.getElementById('gameMessage');
        
        if (diceSum === 7 || diceSum === 11) {
            // Natural - Pass Line wins, Don't Pass loses
            if (this.gameState.activeBets['pass-line']) {
                const betAmount = this.gameState.betAmounts['pass-line'];
                const winAmount = betAmount * this.gameState.payouts['pass-line'];
                this.gameState.balance += (betAmount + winAmount);
                
                if (gameMessage) {
                    gameMessage.textContent = `Natural! Pass Line wins! +${winAmount} GA`;
                    gameMessage.style.color = 'var(--win-color)';
                }
                this.gameState.gamesWon++;
                this.playSound('win');
                this.vibrate([200, 100, 200]);
                
                // Record win in history
                if (this.gameState.rollHistory[0]) {
                    this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                    this.gameState.rollHistory[0].winningBets.push({
                        type: 'pass-line',
                        betAmount: betAmount,
                        winAmount: winAmount
                    });
                }
            } else if (this.gameState.activeBets['dont-pass']) {
                if (gameMessage) {
                    gameMessage.textContent = `Natural! Don't Pass loses. -${this.gameState.betAmounts['dont-pass']} GA`;
                    gameMessage.style.color = 'var(--lose-color)';
                }
                this.playSound('lose');
                this.vibrate([500]);
            } else {
                if (gameMessage) {
                    gameMessage.textContent = `Natural ${diceSum}!`;
                    gameMessage.style.color = 'white';
                }
                this.playSound('neutral');
                this.vibrate([100]);
            }
            
            this.gameState.totalGames++;
            
        } else if (diceSum === 2 || diceSum === 3 || diceSum === 12) {
            // Craps - Pass Line loses, Don't Pass wins (except 12 is a push for Don't Pass)
            if (this.gameState.activeBets['pass-line']) {
                if (gameMessage) {
                    gameMessage.textContent = `Craps! Pass Line loses. -${this.gameState.betAmounts['pass-line']} GA`;
                    gameMessage.style.color = 'var(--lose-color)';
                }
                this.playSound('lose');
                this.vibrate([500]);
            } else if (this.gameState.activeBets['dont-pass']) {
                if (diceSum === 12) {
                    // Push - return bet
                    const betAmount = this.gameState.betAmounts['dont-pass'];
                    this.gameState.balance += betAmount;
                    
                    if (gameMessage) {
                        gameMessage.textContent = `Craps 12! Don't Pass pushes. Bet returned.`;
                        gameMessage.style.color = 'var(--craps-yellow)';
                    }
                    this.playSound('neutral');
                    this.vibrate([100, 100]);
                } else {
                    // Don't Pass wins
                    const betAmount = this.gameState.betAmounts['dont-pass'];
                    const winAmount = betAmount * this.gameState.payouts['dont-pass'];
                    this.gameState.balance += (betAmount + winAmount);
                    
                    if (gameMessage) {
                        gameMessage.textContent = `Craps! Don't Pass wins! +${winAmount} GA`;
                        gameMessage.style.color = 'var(--win-color)';
                    }
                    this.gameState.gamesWon++;
                    this.playSound('win');
                    this.vibrate([200, 100, 200]);
                    
                    // Record win in history
                    if (this.gameState.rollHistory[0]) {
                        this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                        this.gameState.rollHistory[0].winningBets.push({
                            type: 'dont-pass',
                            betAmount: betAmount,
                            winAmount: winAmount
                        });
                    }
                }
            } else {
                if (gameMessage) {
                    gameMessage.textContent = `Craps ${diceSum}!`;
                    gameMessage.style.color = 'white';
                }
                this.playSound('neutral');
                this.vibrate([100]);
            }
            
            this.gameState.totalGames++;
            
        } else {
            // Point established
            this.gameState.phase = 'point';
            this.gameState.point = diceSum;
            
            // Update UI
            const gamePhaseEl = document.getElementById('gamePhase');
            const pointBoxEl = document.getElementById('pointBox');
            
            if (gamePhaseEl) gamePhaseEl.textContent = 'Point Phase';
            if (pointBoxEl) {
                pointBoxEl.textContent = diceSum;
                pointBoxEl.classList.add('active');
            }
            
            if (gameMessage) {
                gameMessage.textContent = `Point ${diceSum} established. Roll ${diceSum} to win Pass Line or 7 to win Don't Pass.`;
                gameMessage.style.color = 'white';
            }
            
            this.playSound('point');
            this.vibrate([100, 100, 100]);
        }
        
        this.updateDisplay();
    }

    processPointRoll(diceSum) {
        const gameMessage = document.getElementById('gameMessage');
        
        if (diceSum === this.gameState.point) {
            // Point hit - Pass Line wins, Don't Pass loses
            if (this.gameState.activeBets['pass-line']) {
                const betAmount = this.gameState.betAmounts['pass-line'];
                const winAmount = betAmount * this.gameState.payouts['pass-line'];
                this.gameState.balance += (betAmount + winAmount);
                
                if (gameMessage) {
                    gameMessage.textContent = `Point ${diceSum} hit! Pass Line wins! +${winAmount} GA`;
                    gameMessage.style.color = 'var(--win-color)';
                }
                this.gameState.gamesWon++;
                this.playSound('win');
                this.vibrate([200, 100, 200]);
                
                // Record win in history
                if (this.gameState.rollHistory[0]) {
                    this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                    this.gameState.rollHistory[0].winningBets.push({
                        type: 'pass-line',
                        betAmount: betAmount,
                        winAmount: winAmount
                    });
                }
            } else if (this.gameState.activeBets['dont-pass']) {
                if (gameMessage) {
                    gameMessage.textContent = `Point ${diceSum} hit! Don't Pass loses. -${this.gameState.betAmounts['dont-pass']} GA`;
                    gameMessage.style.color = 'var(--lose-color)';
                }
                this.playSound('lose');
                this.vibrate([500]);
            } else {
                if (gameMessage) {
                    gameMessage.textContent = `Point ${diceSum} hit!`;
                    gameMessage.style.color = 'white';
                }
                this.playSound('neutral');
                this.vibrate([100]);
            }
            
            this.resetToComOutPhase();
            this.gameState.totalGames++;
            
        } else if (diceSum === 7) {
            // Seven out - Pass Line loses, Don't Pass wins
            if (this.gameState.activeBets['pass-line']) {
                if (gameMessage) {
                    gameMessage.textContent = `Seven out! Pass Line loses. -${this.gameState.betAmounts['pass-line']} GA`;
                    gameMessage.style.color = 'var(--lose-color)';
                }
                this.playSound('lose');
                this.vibrate([500]);
            } else if (this.gameState.activeBets['dont-pass']) {
                const betAmount = this.gameState.betAmounts['dont-pass'];
                const winAmount = betAmount * this.gameState.payouts['dont-pass'];
                this.gameState.balance += (betAmount + winAmount);
                
                if (gameMessage) {
                    gameMessage.textContent = `Seven out! Don't Pass wins! +${winAmount} GA`;
                    gameMessage.style.color = 'var(--win-color)';
                }
                this.gameState.gamesWon++;
                this.playSound('win');
                this.vibrate([200, 100, 200]);
                
                // Record win in history
                if (this.gameState.rollHistory[0]) {
                    this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                    this.gameState.rollHistory[0].winningBets.push({
                        type: 'dont-pass',
                        betAmount: betAmount,
                        winAmount: winAmount
                    });
                }
            } else {
                if (gameMessage) {
                    gameMessage.textContent = 'Seven out!';
                    gameMessage.style.color = 'white';
                }
                this.playSound('neutral');
                this.vibrate([100]);
            }
            
            this.resetToComOutPhase();
            this.gameState.totalGames++;
            
        } else {
            // No effect on Pass/Don't Pass
            if (gameMessage) {
                gameMessage.textContent = `Rolled ${diceSum}. Continue rolling for Point ${this.gameState.point} or 7.`;
                gameMessage.style.color = 'white';
            }
            this.playSound('neutral');
            this.vibrate([50]);
        }
        
        this.updateDisplay();
    }

    resetToComOutPhase() {
        // Reset to come-out phase
        this.gameState.phase = 'come-out';
        this.gameState.point = null;
        
        const gamePhaseEl = document.getElementById('gamePhase');
        const pointBoxEl = document.getElementById('pointBox');
        
        if (gamePhaseEl) gamePhaseEl.textContent = 'Come Out Roll';
        if (pointBoxEl) {
            pointBoxEl.textContent = '-';
            pointBoxEl.classList.remove('active');
        }
    }

    processPropositionBets(diceSum, isHardway) {
        // Any Seven
        if (this.gameState.activeBets['any-seven'] && diceSum === 7) {
            const betAmount = this.gameState.betAmounts['any-seven'];
            const winAmount = betAmount * this.gameState.payouts['any-seven'];
            this.gameState.balance += (betAmount + winAmount);
            
            this.showNotification(`Any Seven bet wins! +${winAmount} GA`, 'success');
            this.gameState.gamesWon++;
            
            if (this.gameState.rollHistory[0]) {
                this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                this.gameState.rollHistory[0].winningBets.push({
                    type: 'any-seven',
                    betAmount: betAmount,
                    winAmount: winAmount
                });
            }
        }
        
        // Any Craps
        if (this.gameState.activeBets['any-craps'] && (diceSum === 2 || diceSum === 3 || diceSum === 12)) {
            const betAmount = this.gameState.betAmounts['any-craps'];
            const winAmount = betAmount * this.gameState.payouts['any-craps'];
            this.gameState.balance += (betAmount + winAmount);
            
            this.showNotification(`Any Craps bet wins! +${winAmount} GA`, 'success');
            this.gameState.gamesWon++;
            
            if (this.gameState.rollHistory[0]) {
                this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                this.gameState.rollHistory[0].winningBets.push({
                    type: 'any-craps',
                    betAmount: betAmount,
                    winAmount: winAmount
                });
            }
        }
        
        // Hard Way bets
        if (isHardway) {
            const hardWayBet = `hard-${diceSum}`;
            if (this.gameState.activeBets[hardWayBet]) {
                const betAmount = this.gameState.betAmounts[hardWayBet];
                const winAmount = betAmount * this.gameState.payouts[hardWayBet];
                this.gameState.balance += (betAmount + winAmount);
                
                this.showNotification(`Hard ${diceSum} bet wins! +${winAmount} GA`, 'success');
                this.gameState.gamesWon++;
                
                if (this.gameState.rollHistory[0]) {
                    this.gameState.rollHistory[0].winningBets = this.gameState.rollHistory[0].winningBets || [];
                    this.gameState.rollHistory[0].winningBets.push({
                        type: hardWayBet,
                        betAmount: betAmount,
                        winAmount: winAmount
                    });
                }
            }
        }
        
        this.updateDisplay();
    }

    addToHistory(diceOutcome) {
        const diceSum = diceOutcome[0] + diceOutcome[1];
        const isHardway = diceOutcome[0] === diceOutcome[1] && [4, 6, 8, 10].includes(diceSum);
        
        // Collect all active bets and amounts
        const activeBets = {};
        let totalBetAmount = 0;
        
        for (const betType in this.gameState.activeBets) {
            const betAmount = this.gameState.betAmounts[betType] || 0;
            activeBets[betType] = betAmount;
            totalBetAmount += betAmount;
        }
        
        this.gameState.rollHistory.unshift({
            nonce: this.gameState.nonce,
            dice: diceOutcome,
            sum: diceSum,
            phase: this.gameState.phase,
            point: this.gameState.point,
            isHardway: isHardway,
            activeBets: activeBets,
            totalBetAmount: totalBetAmount,
            winningBets: [],
            clientSeed: this.gameState.clientSeed,
            hashedServerSeed: this.gameState.hashedServerSeed,
            revealedServerSeed: this.gameState.serverSeed,
            timestamp: new Date().toLocaleTimeString()
        });
        
        // Store revealed server seed for verification
        this.gameState.revealedSeeds[this.gameState.nonce] = this.gameState.serverSeed;
        
        // Limit history size
        if (this.gameState.rollHistory.length > 10) {
            this.gameState.rollHistory.pop();
        }
    }

    renderDice() {
        this.renderDie('die1Dots', this.gameState.dice[0]);
        this.renderDie('die2Dots', this.gameState.dice[1]);
    }

    renderDie(elementId, value) {
        const dotsContainer = document.getElementById(elementId);
        if (!dotsContainer) return;
        
        dotsContainer.innerHTML = '';
        
        // Define dot positions for each value (1-6)
        const dotPositions = {
            1: [5],
            2: [1, 9],
            3: [1, 5, 9],
            4: [1, 3, 7, 9],
            5: [1, 3, 5, 7, 9],
            6: [1, 3, 4, 6, 7, 9]
        };
        
        // Create a 3x3 grid for dots
        for (let i = 1; i <= 9; i++) {
            const dot = document.createElement('div');
            
            // Show dot if it's in the positions for this value
            if (dotPositions[value].includes(i)) {
                dot.className = 'dot';
            }
            
            dotsContainer.appendChild(dot);
        }
    }

    newGame() {
        // Reset game state
        this.gameState.phase = 'come-out';
        this.gameState.point = null;
        this.gameState.activeBets = {};
        this.gameState.betAmounts = {};
        
        // Reset UI
        const gamePhaseEl = document.getElementById('gamePhase');
        const gameMessageEl = document.getElementById('gameMessage');
        const pointBoxEl = document.getElementById('pointBox');
        
        if (gamePhaseEl) gamePhaseEl.textContent = 'Come Out Roll';
        if (gameMessageEl) {
            gameMessageEl.textContent = 'Place your bets and roll the dice!';
            gameMessageEl.style.color = 'white';
        }
        if (pointBoxEl) {
            pointBoxEl.textContent = '-';
            pointBoxEl.classList.remove('active');
        }
        
        // Reset bet display
        this.updateBetDisplay();
        this.updateCurrentBetDisplay();
        
        // Show notification
        this.showNotification('New game started');
        this.vibrate([100, 50, 100]);
        
        // Generate new seeds
        this.gameState.nonce++;
        this.generateNewSeeds();
        this.updateDisplay();
        
        // Auto-refill if broke
        if (this.gameState.balance < this.gameState.defaultBetAmount) {
            const refill = Math.max(100, this.gameState.defaultBetAmount * 3);
            this.gameState.balance += refill;
            this.showNotification(`Balance refilled with ${refill} GA`, 'info');
            this.updateDisplay();
        }
        
        // Save game data
        this.saveGameData();
    }

    populateRollHistory() {
        const historyContainer = document.getElementById('rollHistory');
        if (!historyContainer) return;
        
        historyContainer.innerHTML = '';

        this.gameState.rollHistory.forEach(roll => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            let resultText, resultColor;
            const diceSum = roll.sum;
            
            if (roll.phase === 'come-out') {
                if (diceSum === 7 || diceSum === 11) {
                    resultText = 'Natural';
                    resultColor = 'var(--win-color)';
                } else if (diceSum === 2 || diceSum === 3 || diceSum === 12) {
                    resultText = 'Craps';
                    resultColor = 'var(--lose-color)';
                } else {
                    resultText = 'Point';
                    resultColor = 'var(--point-color)';
                }
            } else {
                if (diceSum === roll.point) {
                    resultText = 'Point Hit';
                    resultColor = 'var(--win-color)';
                } else if (diceSum === 7) {
                    resultText = 'Seven Out';
                    resultColor = 'var(--lose-color)';
                } else {
                    resultText = `Roll ${diceSum}`;
                    resultColor = 'white';
                }
            }
            
            // Format winning bets information
            let winningBetsHTML = '';
            if (roll.winningBets && roll.winningBets.length > 0) {
                winningBetsHTML = '<div style="margin: 0.5rem 0; padding-top: 0.5rem; border-top: 1px solid rgba(255,255,255,0.1);">';
                winningBetsHTML += '<span style="color: var(--win-color);">Winning bets:</span>';
                winningBetsHTML += '<ul style="margin: 0.25rem 0 0 1rem; padding: 0;">';
                
                roll.winningBets.forEach(bet => {
                    winningBetsHTML += `<li>${this.BET_TYPES[bet.type].name}: +${bet.winAmount} GA</li>`;
                });
                
                winningBetsHTML += '</ul></div>';
            }
            
            historyItem.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                    <strong>Roll #${roll.nonce}</strong>
                    <span style="color: ${resultColor};">${resultText}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                    <span>Dice: ${roll.dice[0]}, ${roll.dice[1]} = ${roll.sum}</span>
                    <span>${roll.isHardway ? 'Hard Way' : ''}</span>
                </div>
                <div style="margin-bottom: 0.5rem;">
                    <span>Total Bet: ${roll.totalBetAmount || 0} GA</span>
                </div>
                ${winningBetsHTML}
                <div style="font-size: 0.8rem; color: rgba(255,255,255,0.6);">
                    <button class="rules-toggle" style="font-size: 0.8rem; margin-top: 0.5rem;" 
                            onclick="game.revealSeed(${roll.nonce})">
                        Reveal Server Seed
                    </button>
                    <div id="seed-${roll.nonce}" style="display: none; margin-top: 0.5rem;">
                        <div class="seed-display" style="font-size: 0.7rem;">
                            ${roll.revealedServerSeed}
                        </div>
                    </div>
                </div>
            `;
            historyContainer.appendChild(historyItem);
        });
    }

    revealSeed(nonce) {
        const seedElement = document.getElementById(`seed-${nonce}`);
        if (seedElement) {
            seedElement.style.display = seedElement.style.display === 'none' ? 'block' : 'none';
        }
    }

    updateDisplay() {
        // Update player stats
        const totalGamesEl = document.getElementById('totalGames');
        const gamesWonEl = document.getElementById('gamesWon');
        const winRateEl = document.getElementById('winRate');
        const balanceEl = document.getElementById('playerBalance');
        
        if (totalGamesEl) totalGamesEl.textContent = this.gameState.totalGames;
        if (gamesWonEl) gamesWonEl.textContent = this.gameState.gamesWon;
        
        const winRate = this.gameState.totalGames > 0 ? 
            Math.round(this.gameState.gamesWon / this.gameState.totalGames * 100) : 0;
        if (winRateEl) winRateEl.textContent = winRate + '%';
        
        // Update nonce
        const nonceEl = document.getElementById('nonce');
        if (nonceEl) nonceEl.textContent = this.gameState.nonce;
        
        // Update balance
        if (balanceEl) balanceEl.textContent = `${this.gameState.balance} GA`;
    }

    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        if (!notification) return;
        
        notification.textContent = message;
        
        // Set color based on type
        if (type === 'error') {
            notification.style.background = 'var(--lose-color)';
        } else if (type === 'success') {
            notification.style.background = 'var(--win-color)';
        } else {
            notification.style.background = 'var(--craps-blue)';
        }
        
        notification.className = 'notification show';
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }

    playSound(type) {
        try {
            // Simple beep sounds using Web Audio API
            const context = new (window.AudioContext || window.webkitAudioContext)();
            
            let oscillator = context.createOscillator();
            let gainNode = context.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(context.destination);
            
            gainNode.gain.setValueAtTime(0.3, context.currentTime);
            
            switch (type) {
                case 'win':
                    oscillator.frequency.setValueAtTime(880, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    
                    setTimeout(() => {
                        let osc2 = context.createOscillator();
                        let gain2 = context.createGain();
                        osc2.connect(gain2);
                        gain2.connect(context.destination);
                        osc2.frequency.setValueAtTime(1320, context.currentTime);
                        gain2.gain.setValueAtTime(0.3, context.currentTime);
                        osc2.start(context.currentTime);
                        osc2.stop(context.currentTime + 0.1);
                    }, 100);
                    break;
                case 'lose':
                    oscillator.frequency.setValueAtTime(220, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.3);
                    break;
                case 'point':
                    oscillator.frequency.setValueAtTime(440, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.2);
                    break;
                case 'rolling':
                    for (let i = 0; i < 5; i++) {
                        setTimeout(() => {
                            let osc = context.createOscillator();
                            let gain = context.createGain();
                            osc.connect(gain);
                            gain.connect(context.destination);
                            osc.frequency.setValueAtTime(300 + Math.random() * 200, context.currentTime);
                            gain.gain.setValueAtTime(0.1, context.currentTime);
                            osc.start(context.currentTime);
                            osc.stop(context.currentTime + 0.05);
                        }, i * 100);
                    }
                    break;
                default:
                    oscillator.frequency.setValueAtTime(440, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
            }
        } catch (e) {
            // Audio API not available or failed
            console.warn('Audio playback failed:', e);
        }
    }
}

// Global functions for HTML event handlers
function toggleRules() {
    const rulesContent = document.getElementById('rulesContent');
    if (rulesContent) {
        rulesContent.style.display = rulesContent.style.display === 'none' ? 'block' : 'none';
    }
}

function verifyResult() {
    const clientSeed = document.getElementById('verifyClientSeed')?.value;
    const serverSeed = document.getElementById('verifyServerSeed')?.value;
    const nonce = parseInt(document.getElementById('verifyNonce')?.value);

    if (!clientSeed || !serverSeed || isNaN(nonce)) {
        game.showNotification('Please fill in all verification fields', 'error');
        return;
    }

    // Find the corresponding roll in history
    const historyEntry = game.gameState.rollHistory.find(roll => roll.nonce === nonce);
    if (!historyEntry) {
        showVerificationResult(false, 'No roll found with this nonce');
        return;
    }
    
    // Verify server seed hash matches the stored hash
    const storedHash = historyEntry.hashedServerSeed;
    const calculatedHash = game.sha256(serverSeed);
    
    if (calculatedHash !== storedHash) {
        showVerificationResult(false, 'Server seed hash does not match the stored hash');
        return;
    }

    // Recreate the calculation
    const combinedSeed = clientSeed + serverSeed + nonce;
    const hash = game.sha256(combinedSeed);
    
    // Calculate die 1
    const hexSubstring1 = hash.substring(0, 8);
    const decimal1 = parseInt(hexSubstring1, 16);
    const die1 = (decimal1 % 6) + 1;
    
    // Calculate die 2
    const hexSubstring2 = hash.substring(8, 16);
    const decimal2 = parseInt(hexSubstring2, 16);
    const die2 = (decimal2 % 6) + 1;
    
    // Check if calculated dice match recorded dice
    const verified = die1 === historyEntry.dice[0] && die2 === historyEntry.dice[1];

    // Display verification result
    showVerificationResult(verified, verified ? 
        `Verification successful! Calculated dice (${die1}, ${die2}) match the recorded dice.` : 
        `Verification failed! Calculated dice (${die1}, ${die2}) do not match recorded dice (${historyEntry.dice[0]}, ${historyEntry.dice[1]}).`
    );
}

function showVerificationResult(success, message) {
    const verificationResult = document.getElementById('verificationResult');
    if (!verificationResult) return;
    
    verificationResult.style.display = 'block';
    verificationResult.style.borderLeft = success ? 
        '4px solid var(--win-color)' : '4px solid var(--lose-color)';
    
    verificationResult.innerHTML = `
        <h5 style="color: ${success ? 'var(--win-color)' : 'var(--lose-color)'}; margin-bottom: 1rem;">
            ${success ? '✅ Verification Successful' : '❌ Verification Failed'}
        </h5>
        <p>${message}</p>
    `;
}

// Initialize the game when DOM is loaded
let game;
document.addEventListener('DOMContentLoaded', function() {
    game = new CrapsGame();
});

// Export for global access
window.CrapsGame = CrapsGame;