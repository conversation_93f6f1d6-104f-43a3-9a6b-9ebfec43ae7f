// Punto Banco Elite - Provably Fair Baccarat
document.addEventListener('DOMContentLoaded', function() {
    console.log("Baccarat game initializing...");
    
    // Game elements
    const gameStatus = document.getElementById('gameStatus');
    const cardsInShoe = document.getElementById('cardsInShoe');
    const roundId = document.getElementById('roundId');
    
    const playerCards = document.getElementById('playerCards');
    const bankerCards = document.getElementById('bankerCards');
    const playerScore = document.getElementById('playerScore');
    const bankerScore = document.getElementById('bankerScore');
    
    const playerBetAmount = document.getElementById('playerBetAmount');
    const bankerBetAmount = document.getElementById('bankerBetAmount');
    const tieBetAmount = document.getElementById('tieBetAmount');
    const playerPairBetAmount = document.getElementById('playerPairBetAmount');
    const bankerPairBetAmount = document.getElementById('bankerPairBetAmount');
    const perfectPairBetAmount = document.getElementById('perfectPairBetAmount');
    
    // View mode elements
    const standardViewBtn = document.getElementById('standardViewBtn');
    const proViewBtn = document.getElementById('proViewBtn');
    const proStatsBar = document.getElementById('proStatsBar');
    const proAnalysisSection = document.getElementById('proAnalysisSection');
    const proAdvancedStats = document.getElementById('proAdvancedStats');
    const proBettingHistory = document.getElementById('proBettingHistory');
    
    // Pro stats elements
    const playerWinPercent = document.getElementById('playerWinPercent');
    const bankerWinPercent = document.getElementById('bankerWinPercent');
    const tieWinPercent = document.getElementById('tieWinPercent');
    const pairPercent = document.getElementById('pairPercent');
    const roiValue = document.getElementById('roiValue');
    
    const playerBetWinRate = document.getElementById('playerBetWinRate');
    const bankerBetWinRate = document.getElementById('bankerBetWinRate');
    const tieBetWinRate = document.getElementById('tieBetWinRate');
    const playerPairWinRate = document.getElementById('playerPairWinRate');
    const bankerPairWinRate = document.getElementById('bankerPairWinRate');
    const perfectPairWinRate = document.getElementById('perfectPairWinRate');
    
    const currentStreak = document.getElementById('currentStreak');
    const maxStreak = document.getElementById('maxStreak');
    const currentPattern = document.getElementById('currentPattern');
    
    const sessionDuration = document.getElementById('sessionDuration');
    const handsPerHour = document.getElementById('handsPerHour');
    const avgBetSize = document.getElementById('avgBetSize');
    const netProfit = document.getElementById('netProfit');
    const returnOnInvestment = document.getElementById('returnOnInvestment');
    const winLossRatio = document.getElementById('winLossRatio');
    const lastTenHands = document.getElementById('lastTenHands');
    const maxProfitHand = document.getElementById('maxProfitHand');
    
    // Mobile elements
    const mobileInfoToggleBtn = document.getElementById('mobileInfoToggleBtn');
    const infoPanel = document.querySelector('.info-panel');
    
    const betAreas = document.querySelectorAll('.bet-area');
    const chips = document.querySelectorAll('.chip');
    
    const clearBtn = document.getElementById('clearBtn');
    const dealBtn = document.getElementById('dealBtn');
    const replayBtn = document.getElementById('replayBtn');
    
    const outcomeDisplay = document.getElementById('outcomeDisplay');
    const outcomeMessage = document.getElementById('outcomeMessage');
    const winningsAmount = document.getElementById('winningsAmount');
    
    const beadPlateGrid = document.getElementById('beadPlateGrid');
    const bigRoadGrid = document.getElementById('bigRoadGrid');
    const bigEyeGrid = document.getElementById('bigEyeGrid');
    const smallRoadGrid = document.getElementById('smallRoadGrid');
    const cockroachGrid = document.getElementById('cockroachGrid');
    
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    const serverSeedHash = document.getElementById('serverSeedHash');
    const clientSeedInput = document.getElementById('clientSeedInput');
    const newSeedBtn = document.getElementById('newSeedBtn');
    
    const historyList = document.getElementById('historyList');
    
    const rulesBtn = document.getElementById('rulesBtn');
    const verifyBtn = document.getElementById('verifyBtn');
    const closeRulesBtn = document.getElementById('closeRulesBtn');
    const closeVerifyBtn = document.getElementById('closeVerifyBtn');
    const rulesModal = document.getElementById('rulesModal');
    const verifyModal = document.getElementById('verifyModal');
    
    const verifyHandBtn = document.getElementById('verifyHandBtn');
    const verifyServerSeed = document.getElementById('verifyServerSeed');
    const verifyClientSeed = document.getElementById('verifyClientSeed');
    const verifyShoeId = document.getElementById('verifyShoeId');
    const verifyRoundId = document.getElementById('verifyRoundId');
    const verifyResult = document.getElementById('verifyResult');
    const currentShoeInfo = document.getElementById('currentShoeInfo');
    
    const creditsElement = document.getElementById('credits');
    const totalBetElement = document.getElementById('totalBet');
    const winElement = document.getElementById('win');
    
    const handsPlayed = document.getElementById('handsPlayed');
    const playerWins = document.getElementById('playerWins');
    const bankerWins = document.getElementById('bankerWins');
    const tieWins = document.getElementById('tieWins');
    const playerPairs = document.getElementById('playerPairs');
    const bankerPairs = document.getElementById('bankerPairs');
    
    // Game state
    let gameState = {
        // GA Currency system
        gaCurrency: 10000, // Initial GA Currency amount
        gaMinBet: 50,
        gaMaxBet: 5000,
        
        currentBets: {
            player: 0,
            banker: 0,
            tie: 0,
            playerPair: 0,
            bankerPair: 0,
            perfectPair: 0
        },
        lastBets: {
            player: 0,
            banker: 0,
            tie: 0,
            playerPair: 0,
            bankerPair: 0,
            perfectPair: 0
        },
        selectedChipValue: null,
        selectedBetArea: null,
        currentShoe: {
            id: 0,
            cards: [],
            originalCards: [],
            currentPosition: 0
        },
        provablyFair: {
            serverSeed: '',
            serverSeedHash: '',
            clientSeed: '',
            nonce: 0
        },
        currentHand: {
            player: [],
            banker: []
        },
        gameHistory: [],
        stats: {
            handsPlayed: 0,
            playerWins: 0,
            bankerWins: 0,
            tieWins: 0,
            playerPairs: 0,
            bankerPairs: 0,
            totalBetAmount: 0,
            totalWinAmount: 0,
            streaks: {
                current: {type: null, count: 0},
                max: {type: null, count: 0}
            },
            betStats: {
                player: {bets: 0, wins: 0},
                banker: {bets: 0, wins: 0},
                tie: {bets: 0, wins: 0},
                playerPair: {bets: 0, wins: 0},
                bankerPair: {bets: 0, wins: 0},
                perfectPair: {bets: 0, wins: 0}
            },
            session: {
                startTime: null,
                maxProfit: 0,
                startBalance: 0
            }
        },
        beadRoad: [],
        bigRoad: [],
        isDealing: false,
        viewMode: 'standard', // 'standard' or 'pro'
        isMobileInfoVisible: false
    };
    
    // Card definitions
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
    const cardValues = {
        'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
        '10': 0, 'J': 0, 'Q': 0, 'K': 0
    };
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing game...");
        
        // Load saved view mode from localStorage
        loadViewMode();
        
        // Generate initial seeds
        generateNewSeeds();
        
        // Set up event listeners
        setupEventListeners();
        
        // Select initial chip
        selectChip(1);
        
        // Update UI
        updateUI();
        
        // Update modal content
        updateCurrentShoeInfo();
        
        // Initialize GA Currency display
        updateGACurrencyDisplay();
        
        // Initialize session stats
        initSessionStats();
        
        // Start session timer
        startSessionTimer();
        
        // Hide the GA currency section as we're now using the main credits display
        if (document.querySelector('.ga-currency-section')) {
            document.querySelector('.ga-currency-section').style.display = 'none';
        }
    }
    
    function loadViewMode() {
        const savedViewMode = localStorage.getItem('baccaratViewMode');
        if (savedViewMode) {
            gameState.viewMode = savedViewMode;
            updateViewMode(savedViewMode);
        }
    }
    
    function initSessionStats() {
        gameState.stats.session.startTime = new Date();
        gameState.stats.session.startBalance = gameState.gaCurrency;
    }
    
    function startSessionTimer() {
        // Update session duration every second
        setInterval(updateSessionDuration, 1000);
    }
    
    function updateSessionDuration() {
        if (!gameState.stats.session.startTime) return;
        
        const now = new Date();
        const diff = now - gameState.stats.session.startTime;
        
        // Convert to hours, minutes, seconds
        const hours = Math.floor(diff / 3600000).toString().padStart(2, '0');
        const minutes = Math.floor((diff % 3600000) / 60000).toString().padStart(2, '0');
        const seconds = Math.floor((diff % 60000) / 1000).toString().padStart(2, '0');
        
        if (sessionDuration) {
            sessionDuration.textContent = `${hours}:${minutes}:${seconds}`;
        }
        
        // Update hands per hour
        if (handsPerHour && gameState.stats.handsPlayed > 0) {
            const hoursElapsed = diff / 3600000;
            if (hoursElapsed > 0) {
                const rate = Math.round(gameState.stats.handsPlayed / hoursElapsed);
                handsPerHour.textContent = rate;
            }
        }
    }
    
    function updateGACurrencyDisplay() {
        if (gaCurrencyBalance) {
            gaCurrencyBalance.textContent = gameState.gaCurrency;
        }
        if (creditsElement) {
            creditsElement.textContent = gameState.gaCurrency;
        }
    }
    
    function setupEventListeners() {
        // View mode toggle
        standardViewBtn.addEventListener('click', () => {
            setViewMode('standard');
        });
        
        proViewBtn.addEventListener('click', () => {
            setViewMode('pro');
        });
        
        // Mobile info toggle
        mobileInfoToggleBtn.addEventListener('click', toggleMobileInfo);
        
        // Bet areas
        betAreas.forEach(area => {
            area.addEventListener('click', () => {
                if (!gameState.isDealing) {
                    const betType = area.dataset.bet;
                    selectBetArea(betType);
                    
                    if (gameState.selectedChipValue) {
                        placeBet(betType, gameState.selectedChipValue);
                    }
                }
            });
        });
        
        // Chips
        chips.forEach(chip => {
            chip.addEventListener('click', () => {
                if (!gameState.isDealing) {
                    const value = parseInt(chip.dataset.value);
                    selectChip(value);
                }
            });
        });
        
        // Game control buttons
        clearBtn.addEventListener('click', clearBets);
        dealBtn.addEventListener('click', dealHand);
        replayBtn.addEventListener('click', replayBet);
        
        // Seed generation
        newSeedBtn.addEventListener('click', () => {
            if (!gameState.isDealing) {
                generateNewClientSeed();
                updateUI();
            }
        });
        
        clientSeedInput.addEventListener('input', () => {
            if (!gameState.isDealing) {
                gameState.provablyFair.clientSeed = clientSeedInput.value;
            }
        });
        
        // Tab navigation
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.dataset.tab;
                
                // Hide all tabs
                tabBtns.forEach(b => b.classList.remove('active'));
                tabPanes.forEach(p => p.classList.remove('active'));
                
                // Show selected tab
                btn.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });
        
        // Modal controls
        rulesBtn.addEventListener('click', () => {
            rulesModal.classList.remove('hidden');
        });
        
        verifyBtn.addEventListener('click', () => {
            verifyModal.classList.remove('hidden');
            updateCurrentShoeInfo();
        });
        
        closeRulesBtn.addEventListener('click', () => {
            rulesModal.classList.add('hidden');
        });
        
        closeVerifyBtn.addEventListener('click', () => {
            verifyModal.classList.add('hidden');
        });
        
        // Verification
        verifyHandBtn.addEventListener('click', verifyHand);
    }
    
    function setViewMode(mode) {
        gameState.viewMode = mode;
        localStorage.setItem('baccaratViewMode', mode);
        updateViewMode(mode);
    }
    
    function updateViewMode(mode) {
        // Update button states
        if (mode === 'standard') {
            standardViewBtn.classList.add('active');
            proViewBtn.classList.remove('active');
            document.body.classList.remove('pro-view-active');
        } else {
            standardViewBtn.classList.remove('active');
            proViewBtn.classList.add('active');
            document.body.classList.add('pro-view-active');
        }
        
        // Update Pro stats if switching to Pro view
        if (mode === 'pro') {
            updateProStats();
        }
    }
    
    function toggleMobileInfo() {
        gameState.isMobileInfoVisible = !gameState.isMobileInfoVisible;
        
        if (gameState.isMobileInfoVisible) {
            infoPanel.classList.add('active');
            mobileInfoToggleBtn.classList.add('active');
            mobileInfoToggleBtn.innerHTML = '<i class="fas fa-times"></i> Hide Statistics';
        } else {
            infoPanel.classList.remove('active');
            mobileInfoToggleBtn.classList.remove('active');
            mobileInfoToggleBtn.innerHTML = '<i class="fas fa-chart-line"></i> Show Statistics';
        }
    }
    
    function updateProStats() {
        // Calculate win percentages
        const totalHands = gameState.stats.handsPlayed;
        if (totalHands > 0) {
            const pWinPercent = ((gameState.stats.playerWins / totalHands) * 100).toFixed(1);
            const bWinPercent = ((gameState.stats.bankerWins / totalHands) * 100).toFixed(1);
            const tWinPercent = ((gameState.stats.tieWins / totalHands) * 100).toFixed(1);
            
            playerWinPercent.textContent = `${pWinPercent}%`;
            bankerWinPercent.textContent = `${bWinPercent}%`;
            tieWinPercent.textContent = `${tWinPercent}%`;
            
            // Calculate pair percentage
            const totalPairs = gameState.stats.playerPairs + gameState.stats.bankerPairs;
            const pairPercentValue = ((totalPairs / (totalHands * 2)) * 100).toFixed(1);
            pairPercent.textContent = `${pairPercentValue}%`;
            
            // Calculate ROI
            if (gameState.stats.totalBetAmount > 0) {
                const roi = ((gameState.stats.totalWinAmount - gameState.stats.totalBetAmount) / gameState.stats.totalBetAmount * 100).toFixed(1);
                roiValue.textContent = `${roi}%`;
            }
            
            // Calculate bet win rates
            for (const [betType, stats] of Object.entries(gameState.stats.betStats)) {
                const winRate = stats.bets > 0 ? ((stats.wins / stats.bets) * 100).toFixed(1) : '0.0';
                const winRateElement = document.getElementById(`${betType}WinRate`);
                if (winRateElement) {
                    winRateElement.textContent = `${winRate}%`;
                }
            }
            
            // Update streak information
            const currentStreakType = gameState.stats.streaks.current.type ? 
                gameState.stats.streaks.current.type.charAt(0).toUpperCase() : '-';
            const currentStreakCount = gameState.stats.streaks.current.count;
            currentStreak.textContent = `${currentStreakCount} (${currentStreakType})`;
            
            const maxStreakType = gameState.stats.streaks.max.type ? 
                gameState.stats.streaks.max.type.charAt(0).toUpperCase() : '-';
            const maxStreakCount = gameState.stats.streaks.max.count;
            maxStreak.textContent = `${maxStreakCount} (${maxStreakType})`;
            
            // Set pattern based on recent hands
            updatePattern();
            
            // Advanced stats
            if (avgBetSize) {
                avgBetSize.textContent = Math.round(gameState.stats.totalBetAmount / totalHands);
            }
            
            if (netProfit) {
                const profit = gameState.gaCurrency - gameState.stats.session.startBalance;
                netProfit.textContent = profit > 0 ? `+${profit}` : profit;
            }
            
            if (returnOnInvestment) {
                const investment = gameState.stats.session.startBalance;
                const current = gameState.gaCurrency;
                const roi = ((current - investment) / investment * 100).toFixed(1);
                returnOnInvestment.textContent = `${roi}%`;
            }
            
            if (winLossRatio) {
                const wins = gameState.stats.playerWins + gameState.stats.bankerWins + gameState.stats.tieWins;
                const losses = totalHands - wins;
                const ratio = losses > 0 ? (wins / losses).toFixed(2) : wins > 0 ? '∞' : '0.00';
                winLossRatio.textContent = ratio;
            }
            
            if (lastTenHands && gameState.gameHistory.length > 0) {
                const last10 = gameState.gameHistory.slice(0, 10).map(h => h.winner.charAt(0).toUpperCase()).join('');
                lastTenHands.textContent = last10 || '-';
            }
            
            if (maxProfitHand) {
                maxProfitHand.textContent = gameState.stats.session.maxProfit;
            }
        }
    }
    
    function updatePattern() {
        // Analyze last 5 outcomes to detect patterns
        if (gameState.gameHistory.length >= 5) {
            const last5 = gameState.gameHistory.slice(0, 5).map(h => h.winner);
            
            // Check for alternating pattern
            if (last5[0] !== last5[1] && last5[1] !== last5[2] && last5[2] !== last5[3] && last5[3] !== last5[4]) {
                currentPattern.textContent = "Alternating";
                return;
            }
            
            // Check for player dominant
            const playerCount = last5.filter(w => w === 'player').length;
            if (playerCount >= 4) {
                currentPattern.textContent = "Player Dominant";
                return;
            }
            
            // Check for banker dominant
            const bankerCount = last5.filter(w => w === 'banker').length;
            if (bankerCount >= 4) {
                currentPattern.textContent = "Banker Dominant";
                return;
            }
            
            // Check for tie heavy
            const tieCount = last5.filter(w => w === 'tie').length;
            if (tieCount >= 2) {
                currentPattern.textContent = "Tie Heavy";
                return;
            }
            
            // Check for chop pattern (alternating player/banker)
            let isChop = true;
            for (let i = 0; i < 4; i++) {
                if (last5[i] === 'tie' || last5[i+1] === 'tie') continue;
                if (last5[i] === last5[i+1]) {
                    isChop = false;
                    break;
                }
            }
            if (isChop) {
                currentPattern.textContent = "Chop";
                return;
            }
            
            // Default if no clear pattern
            currentPattern.textContent = "No Clear Pattern";
        } else {
            currentPattern.textContent = "Insufficient Data";
        }
    }
    
    function generateNewSeeds() {
        // Generate server seed
        const serverSeed = generateRandomString(64);
        gameState.provablyFair.serverSeed = serverSeed;
        
        // Hash the server seed
        gameState.provablyFair.serverSeedHash = sha256(serverSeed);
        
        // Generate client seed
        generateNewClientSeed();
        
        // Reset nonce
        gameState.provablyFair.nonce = 0;
        
        // Create a new shoe
        createNewShoe();
    }
    
    function generateNewClientSeed() {
        const clientSeed = generateRandomString(16);
        gameState.provablyFair.clientSeed = clientSeed;
        clientSeedInput.value = clientSeed;
    }
    
    function generateRandomString(length) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        
        return result;
    }
    
    function sha256(str) {
        // In a real implementation, this would use a proper crypto library
        // For this demo, we'll just simulate a hash
        return Array.from(str)
            .reduce((hash, char) => {
                const charCode = char.charCodeAt(0);
                return ((hash << 5) - hash) + charCode | 0;
            }, 0)
            .toString(16)
            .padStart(64, '0');
    }
    
    function createNewShoe() {
        console.log("Creating new shoe...");
        
        // Increment shoe ID
        gameState.currentShoe.id++;
        
        // Create a deck of cards
        const deck = [];
        for (let i = 0; i < 8; i++) { // 8 decks
            for (const suit of suits) {
                for (const value of values) {
                    deck.push({ value, suit });
                }
            }
        }
        
        // Shuffle the deck using provably fair algorithm
        const shuffledDeck = provablyFairShuffle(deck);
        
        // Update the current shoe
        gameState.currentShoe.cards = [...shuffledDeck];
        gameState.currentShoe.originalCards = [...shuffledDeck];
        gameState.currentShoe.currentPosition = 0;
        
        // Update UI
        cardsInShoe.textContent = gameState.currentShoe.cards.length;
        roundId.textContent = '0';
        
        // Update road displays
        resetRoads();
    }
    
    function provablyFairShuffle(deck) {
        // In a real implementation, this would use a more sophisticated provably fair algorithm
        // This is a simplified version for demonstration
        
        const seed = gameState.provablyFair.serverSeed + gameState.provablyFair.clientSeed + gameState.currentShoe.id;
        const shuffledDeck = [...deck];
        
        // Fisher-Yates shuffle with seeded RNG
        let seedValue = hashToNumber(seed);
        
        for (let i = shuffledDeck.length - 1; i > 0; i--) {
            // Generate a seeded random number
            seedValue = (seedValue * 9301 + 49297) % 233280;
            const j = Math.floor((seedValue / 233280) * (i + 1));
            
            // Swap elements
            [shuffledDeck[i], shuffledDeck[j]] = [shuffledDeck[j], shuffledDeck[i]];
        }
        
        return shuffledDeck;
    }
    
    function hashToNumber(str) {
        // Convert a string to a number for seeding purposes
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            hash = ((hash << 5) - hash) + str.charCodeAt(i);
            hash |= 0; // Convert to 32bit integer
        }
        return Math.abs(hash);
    }
    
    function selectBetArea(betType) {
        console.log(`Selected bet area: ${betType}`);
        gameState.selectedBetArea = betType;
        
        // Highlight the selected bet area
        betAreas.forEach(area => {
            if (area.dataset.bet === betType) {
                area.classList.add('selected');
            } else {
                area.classList.remove('selected');
            }
        });
    }
    
    function selectChip(value) {
        console.log(`Selected chip value: ${value}`);
        gameState.selectedChipValue = value;
        
        // Highlight the selected chip
        chips.forEach(chip => {
            if (parseInt(chip.dataset.value) === value) {
                chip.classList.add('selected');
            } else {
                chip.classList.remove('selected');
            }
        });
    }
    
    function placeBet(betType, amount) {
        // Check if enough GA currency
        if (gameState.gaCurrency < amount) {
            alert("Not enough GA Currency to place this bet!");
            return;
        }
        
        // Place the bet
        gameState.currentBets[betType] += amount;
        gameState.gaCurrency -= amount;
        
        // Update UI
        updateBetDisplay();
        updateGACurrencyDisplay();
    }
    
    function updateBetDisplay() {
        playerBetAmount.textContent = gameState.currentBets.player;
        bankerBetAmount.textContent = gameState.currentBets.banker;
        tieBetAmount.textContent = gameState.currentBets.tie;
        playerPairBetAmount.textContent = gameState.currentBets.playerPair;
        bankerPairBetAmount.textContent = gameState.currentBets.bankerPair;
        perfectPairBetAmount.textContent = gameState.currentBets.perfectPair;
        
        // Update total bet display
        const totalBet = Object.values(gameState.currentBets).reduce((total, bet) => total + bet, 0);
        totalBetElement.textContent = totalBet;
    }
    
    function clearBets() {
        if (gameState.isDealing) return;
        
        // Return bet amounts to GA Currency
        for (const [bet, amount] of Object.entries(gameState.currentBets)) {
            gameState.gaCurrency += amount;
            gameState.currentBets[bet] = 0;
        }
        
        // Update UI
        updateBetDisplay();
        updateGACurrencyDisplay();
        
        // Clear selection
        betAreas.forEach(area => area.classList.remove('selected'));
        gameState.selectedBetArea = null;
    }
    
    function replayBet() {
        if (gameState.isDealing) return;
        
        // Check if has last bets and enough GA Currency
        const totalLastBet = Object.values(gameState.lastBets).reduce((total, bet) => total + bet, 0);
        if (totalLastBet === 0) {
            alert("No previous bet to replay!");
            return;
        }
        
        if (gameState.gaCurrency < totalLastBet) {
            alert("Not enough GA Currency to replay last bet!");
            return;
        }
        
        // Clear current bets first
        clearBets();
        
        // Place last bets
        for (const [bet, amount] of Object.entries(gameState.lastBets)) {
            if (amount > 0) {
                gameState.currentBets[bet] = amount;
                gameState.gaCurrency -= amount;
            }
        }
        
        // Update UI
        updateBetDisplay();
        updateGACurrencyDisplay();
    }
    
    function dealHand() {
        // Check if any bet is placed
        const totalBet = Object.values(gameState.currentBets).reduce((total, bet) => total + bet, 0);
        if (totalBet === 0) {
            alert("Please place a bet first!");
            return;
        }
        
        // Check if already dealing
        if (gameState.isDealing) {
            return;
        }
        
        gameState.isDealing = true;
        gameStatus.textContent = "Dealing...";
        
        // Save current bets as last bets
        for (const bet in gameState.currentBets) {
            gameState.lastBets[bet] = gameState.currentBets[bet];
        }
        
        // Update bet stats for Pro view
        for (const [bet, amount] of Object.entries(gameState.currentBets)) {
            if (amount > 0) {
                gameState.stats.betStats[bet].bets++;
            }
        }
        
        // Add to total bet amount for stats
        gameState.stats.totalBetAmount += totalBet;
        
        // Increment round counter
        gameState.provablyFair.nonce++;
        roundId.textContent = gameState.provablyFair.nonce;
        
        // Reset current hand
        gameState.currentHand = {
            player: [],
            banker: []
        };
        
        // Clear previous cards
        playerCards.innerHTML = '';
        bankerCards.innerHTML = '';
        playerScore.textContent = '-';
        bankerScore.textContent = '-';
        
        // Hide outcome display
        outcomeDisplay.classList.add('hidden');
        
        // Check if shoe needs to be reshuffled (less than quarter of cards left)
        if (gameState.currentShoe.currentPosition > gameState.currentShoe.cards.length * 0.75) {
            alert("Reshuffling the shoe...");
            generateNewSeeds();
            updateUI();
        }
        
        // Deal initial cards
        setTimeout(() => dealCard('player', 0), 500);
        setTimeout(() => dealCard('banker', 0), 1000);
        setTimeout(() => dealCard('player', 1), 1500);
        setTimeout(() => dealCard('banker', 1), 2000);
        
        // Check for naturals and deal additional cards if needed
        setTimeout(() => {
            const playerTotal = calculateHandValue(gameState.currentHand.player);
            const bankerTotal = calculateHandValue(gameState.currentHand.banker);
            
            playerScore.textContent = playerTotal;
            bankerScore.textContent = bankerTotal;
            
            // Check for naturals (8 or 9)
            if (playerTotal >= 8 || bankerTotal >= 8) {
                // Natural - no more cards dealt
                gameStatus.textContent = "Natural!";
                setTimeout(() => determineWinner(), 1000);
            } else {
                // Handle third card rules
                handleThirdCardRules(playerTotal, bankerTotal);
            }
        }, 2500);
    }
    
    function handleThirdCardRules(playerTotal, bankerTotal) {
        // Player's third card rule
        if (playerTotal <= 5) {
            // Player draws a third card
            gameStatus.textContent = "Player draws...";
            
            setTimeout(() => {
                dealCard('player', 2);
                
                setTimeout(() => {
                    // Update player total
                    const newPlayerTotal = calculateHandValue(gameState.currentHand.player);
                    playerScore.textContent = newPlayerTotal;
                    
                    // Banker's third card rule (depends on player's third card)
                    const playerThirdCard = gameState.currentHand.player[2] ? 
                        cardValues[gameState.currentHand.player[2].value] : null;
                    
                    if (shouldBankerDrawThirdCard(bankerTotal, playerThirdCard)) {
                        // Banker draws a third card
                        gameStatus.textContent = "Banker draws...";
                        
                        setTimeout(() => {
                            dealCard('banker', 2);
                            
                            setTimeout(() => {
                                // Update banker total
                                const newBankerTotal = calculateHandValue(gameState.currentHand.banker);
                                bankerScore.textContent = newBankerTotal;
                                
                                // Determine the winner
                                gameStatus.textContent = "Result...";
                                setTimeout(() => determineWinner(), 1000);
                            }, 500);
                        }, 1000);
                    } else {
                        // Banker stands
                        gameStatus.textContent = "Banker stands.";
                        setTimeout(() => determineWinner(), 1000);
                    }
                }, 500);
            }, 1000);
        } else {
            // Player stands
            gameStatus.textContent = "Player stands.";
            
            // Check if banker draws a third card
            if (bankerTotal <= 5) {
                // Banker draws a third card
                setTimeout(() => {
                    gameStatus.textContent = "Banker draws...";
                    
                    dealCard('banker', 2);
                    
                    setTimeout(() => {
                        // Update banker total
                        const newBankerTotal = calculateHandValue(gameState.currentHand.banker);
                        bankerScore.textContent = newBankerTotal;
                        
                        // Determine the winner
                        gameStatus.textContent = "Result...";
                        setTimeout(() => determineWinner(), 1000);
                    }, 500);
                }, 1000);
            } else {
                // Banker stands
                gameStatus.textContent = "Banker stands.";
                setTimeout(() => determineWinner(), 1000);
            }
        }
    }
    
    function dealCard(hand, position) {
        // Get the next card from the shoe
        const card = gameState.currentShoe.cards[gameState.currentShoe.currentPosition];
        gameState.currentShoe.currentPosition++;
        
        // Add to the appropriate hand
        gameState.currentHand[hand][position] = card;
        
        // Create card element
        const cardElement = createCardElement(card);
        cardElement.classList.add('dealing');
        
        // Add to the appropriate container
        if (hand === 'player') {
            playerCards.appendChild(cardElement);
        } else {
            bankerCards.appendChild(cardElement);
        }
        
        // Update cards in shoe count
        cardsInShoe.textContent = gameState.currentShoe.cards.length - gameState.currentShoe.currentPosition;
        
        // Add animation class
        setTimeout(() => {
            cardElement.classList.remove('dealing');
        }, 600);
    }
    
    function createCardElement(card) {
        const cardElement = document.createElement('div');
        cardElement.className = `card ${(card.suit === '♥' || card.suit === '♦') ? 'red' : 'black'}`;
        
        const topLeft = document.createElement('div');
        topLeft.className = 'card-top-left';
        
        const valueTop = document.createElement('div');
        valueTop.className = 'card-value';
        valueTop.textContent = card.value;
        
        const suitTop = document.createElement('div');
        suitTop.className = 'card-suit';
        suitTop.textContent = card.suit;
        
        topLeft.appendChild(valueTop);
        topLeft.appendChild(suitTop);
        
        const center = document.createElement('div');
        center.className = 'card-center';
        center.textContent = card.suit;
        
        const bottomRight = document.createElement('div');
        bottomRight.className = 'card-bottom-right';
        
        const valueBottom = document.createElement('div');
        valueBottom.className = 'card-value';
        valueBottom.textContent = card.value;
        
        const suitBottom = document.createElement('div');
        suitBottom.className = 'card-suit';
        suitBottom.textContent = card.suit;
        
        bottomRight.appendChild(valueBottom);
        bottomRight.appendChild(suitBottom);
        
        cardElement.appendChild(topLeft);
        cardElement.appendChild(center);
        cardElement.appendChild(bottomRight);
        
        return cardElement;
    }
    
    function calculateHandValue(hand) {
        let total = 0;
        
        for (const card of hand) {
            total += cardValues[card.value];
        }
        
        // Baccarat rules: Take only the last digit
        return total % 10;
    }
    
    function shouldBankerDrawThirdCard(bankerTotal, playerThirdCard) {
        // If player did not draw a third card, banker follows their own rule
        if (playerThirdCard === null) {
            return bankerTotal <= 5;
        }
        
        // Banker drawing rules based on banker's score and player's third card
        switch (bankerTotal) {
            case 0:
            case 1:
            case 2:
                return true; // Banker always draws with 0-2
            case 3:
                return playerThirdCard !== 8; // Banker draws unless player's third card is 8
            case 4:
                return [2, 3, 4, 5, 6, 7].includes(playerThirdCard); // Banker draws if player's third card is 2-7
            case 5:
                return [4, 5, 6, 7].includes(playerThirdCard); // Banker draws if player's third card is 4-7
            case 6:
                return [6, 7].includes(playerThirdCard); // Banker draws if player's third card is 6-7
            case 7:
                return false; // Banker never draws with 7
            default:
                return false; // Should never reach here (8-9 are naturals)
        }
    }
    
    function determineWinner() {
        const playerTotal = calculateHandValue(gameState.currentHand.player);
        const bankerTotal = calculateHandValue(gameState.currentHand.banker);
        
        let winner;
        let message;
        
        // Check for pairs first (this is independent of the winner)
        const isPlayerPair = isPair(gameState.currentHand.player);
        const isBankerPair = isPair(gameState.currentHand.banker);
        const isPerfectPair = isPlayerPair && isBankerPair && 
                            gameState.currentHand.player[0].value === gameState.currentHand.banker[0].value;
        
        // Update pair stats
        if (isPlayerPair) gameState.stats.playerPairs++;
        if (isBankerPair) gameState.stats.bankerPairs++;
        
        // Determine winner based on actual hand values
        if (playerTotal > bankerTotal) {
            winner = 'player';
            message = 'Player Wins!';
            gameState.stats.playerWins++;
            updateStreak('player');
        } else if (bankerTotal > playerTotal) {
            winner = 'banker';
            message = 'Banker Wins!';
            gameState.stats.bankerWins++;
            updateStreak('banker');
        } else {
            winner = 'tie';
            message = 'Tie!';
            gameState.stats.tieWins++;
            // Ties don't affect streaks
        }
        
        // Update bet win stats for Pro view
        if (gameState.currentBets[winner] > 0) {
            gameState.stats.betStats[winner].wins++;
        }
        
        if (isPlayerPair && gameState.currentBets.playerPair > 0) {
            gameState.stats.betStats.playerPair.wins++;
        }
        
        if (isBankerPair && gameState.currentBets.bankerPair > 0) {
            gameState.stats.betStats.bankerPair.wins++;
        }
        
        if (isPerfectPair && gameState.currentBets.perfectPair > 0) {
            gameState.stats.betStats.perfectPair.wins++;
        }
        
        // Calculate winnings
        let winnings = 0;
        
        // Main bets
        if (winner === 'player' && gameState.currentBets.player > 0) {
            winnings += gameState.currentBets.player * 2; // 1:1 payout
        }
        
        if (winner === 'banker' && gameState.currentBets.banker > 0) {
            winnings += gameState.currentBets.banker * 1.95; // 0.95:1 payout (5% commission)
        }
        
        if (winner === 'tie') {
            if (gameState.currentBets.tie > 0) {
                winnings += gameState.currentBets.tie * 9; // 8:1 payout
            }
            // Push on player and banker bets
            winnings += gameState.currentBets.player + gameState.currentBets.banker;
        }
        
        // Side bets
        if (isPlayerPair && gameState.currentBets.playerPair > 0) {
            winnings += gameState.currentBets.playerPair * 12; // 11:1 payout
            message += ' Player Pair!';
        }
        
        if (isBankerPair && gameState.currentBets.bankerPair > 0) {
            winnings += gameState.currentBets.bankerPair * 12; // 11:1 payout
            message += ' Banker Pair!';
        }
        
        if (isPerfectPair && gameState.currentBets.perfectPair > 0) {
            winnings += gameState.currentBets.perfectPair * 26; // 25:1 payout
            message += ' Perfect Pair!';
        }
        
        // Add winnings to GA Currency
        gameState.gaCurrency += winnings;
        
        // Update win stats for Pro view
        gameState.stats.totalWinAmount += winnings;
        
        // Update max profit if this hand had higher profit
        const handProfit = winnings - Object.values(gameState.currentBets).reduce((sum, bet) => sum + bet, 0);
        if (handProfit > gameState.stats.session.maxProfit) {
            gameState.stats.session.maxProfit = handProfit;
        }
        
        // Update win display
        winElement.textContent = Math.floor(winnings);
        
        // Update UI
        outcomeMessage.textContent = message;
        winningsAmount.textContent = `Winnings: ${Math.floor(winnings)}`;
        outcomeDisplay.classList.remove('hidden');
        
        // Highlight winning cards
        highlightWinningCards(winner);
        
        // Highlight winning bet
        highlightWinningBet(winner, isPlayerPair, isBankerPair, isPerfectPair);
        
        // Update game history
        updateGameHistory(winner, isPlayerPair, isBankerPair, isPerfectPair, winnings);
        
        // Update roads
        updateRoads(winner);
        
        // Update stats
        gameState.stats.handsPlayed++;
        updateStatsDisplay();
        
        // Update Pro stats if in Pro mode
        if (gameState.viewMode === 'pro') {
            updateProStats();
        }
        
        // Update UI
        updateCreditsDisplay();
        
        // Reset bets
        for (const bet in gameState.currentBets) {
            gameState.currentBets[bet] = 0;
        }
        updateBetDisplay();
        
        // Reset game status
        setTimeout(() => {
            gameStatus.textContent = "Place Your Bets";
            gameState.isDealing = false;
            
            // Remove highlights
            document.querySelectorAll('.card.winning').forEach(card => card.classList.remove('winning'));
            document.querySelectorAll('.bet-area.winning').forEach(area => area.classList.remove('winning'));
        }, 3000);
    }
    
    function updateStreak(winner) {
        // Update current streak
        if (gameState.stats.streaks.current.type === winner) {
            gameState.stats.streaks.current.count++;
        } else {
            gameState.stats.streaks.current.type = winner;
            gameState.stats.streaks.current.count = 1;
        }
        
        // Update max streak if needed
        if (gameState.stats.streaks.current.count > gameState.stats.streaks.max.count) {
            gameState.stats.streaks.max.type = winner;
            gameState.stats.streaks.max.count = gameState.stats.streaks.current.count;
        }
    }
    
    function isPair(hand) {
        if (hand.length < 2) return false;
        return hand[0].value === hand[1].value;
    }
    
    function highlightWinningCards(winner) {
        if (winner === 'player') {
            playerCards.querySelectorAll('.card').forEach(card => card.classList.add('winning'));
        } else if (winner === 'banker') {
            bankerCards.querySelectorAll('.card').forEach(card => card.classList.add('winning'));
        } else {
            // For tie, highlight both
            playerCards.querySelectorAll('.card').forEach(card => card.classList.add('winning'));
            bankerCards.querySelectorAll('.card').forEach(card => card.classList.add('winning'));
        }
    }
    
    function highlightWinningBet(winner, isPlayerPair, isBankerPair, isPerfectPair) {
        // Highlight main bet area
        if (winner === 'player') {
            document.getElementById('playerBetArea').classList.add('winning');
        } else if (winner === 'banker') {
            document.getElementById('bankerBetArea').classList.add('winning');
        } else {
            document.getElementById('tieBetArea').classList.add('winning');
        }
        
        // Highlight pair bet areas
        if (isPlayerPair) {
            document.getElementById('playerPairBetArea').classList.add('winning');
        }
        
        if (isBankerPair) {
            document.getElementById('bankerPairBetArea').classList.add('winning');
        }
        
        if (isPerfectPair) {
            document.getElementById('perfectPairBetArea').classList.add('winning');
        }
    }
    
    function updateGameHistory(winner, isPlayerPair, isBankerPair, isPerfectPair, winnings) {
        // Create history entry
        const historyEntry = {
            round: gameState.provablyFair.nonce,
            shoe: gameState.currentShoe.id,
            winner: winner,
            playerHand: [...gameState.currentHand.player],
            bankerHand: [...gameState.currentHand.banker],
            playerScore: calculateHandValue(gameState.currentHand.player),
            bankerScore: calculateHandValue(gameState.currentHand.banker),
            isPlayerPair: isPlayerPair,
            isBankerPair: isBankerPair,
            isPerfectPair: isPerfectPair,
            winnings: winnings,
            timestamp: new Date().toISOString(),
            serverSeed: gameState.provablyFair.serverSeed,
            clientSeed: gameState.provablyFair.clientSeed
        };
        
        // Add to history
        gameState.gameHistory.unshift(historyEntry);
        
        // Limit history size
        if (gameState.gameHistory.length > 50) {
            gameState.gameHistory.pop();
        }
        
        // Update history display
        updateHistoryDisplay();
    }
    
    function updateHistoryDisplay() {
        // Clear current history display
        historyList.innerHTML = '';
        
        if (gameState.gameHistory.length === 0) {
            historyList.innerHTML = '<p class="no-history">No hands played yet</p>';
            return;
        }
        
        // Add history items
        for (const entry of gameState.gameHistory) {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            const roundInfo = document.createElement('div');
            roundInfo.className = 'history-round';
            roundInfo.innerHTML = `
                <span>Round: ${entry.round}</span>
                <span>Shoe: ${entry.shoe}</span>
            `;
            
            const cardsInfo = document.createElement('div');
            cardsInfo.className = 'history-cards';
            cardsInfo.innerHTML = `
                <span>Player: ${formatHandString(entry.playerHand)} = ${entry.playerScore}</span><br>
                <span>Banker: ${formatHandString(entry.bankerHand)} = ${entry.bankerScore}</span>
            `;
            
            const resultInfo = document.createElement('div');
            resultInfo.className = 'history-result';
            
            let winnerText = '';
            if (entry.winner === 'player') winnerText = 'Player';
            else if (entry.winner === 'banker') winnerText = 'Banker';
            else winnerText = 'Tie';
            
            // Add pair info if applicable
            if (entry.isPlayerPair) winnerText += ', Player Pair';
            if (entry.isBankerPair) winnerText += ', Banker Pair';
            if (entry.isPerfectPair) winnerText += ', Perfect Pair';
            
            resultInfo.innerHTML = `
                <span>Winner: ${winnerText}</span>
                <span>Win: ${Math.floor(entry.winnings)}</span>
            `;
            
            historyItem.appendChild(roundInfo);
            historyItem.appendChild(cardsInfo);
            historyItem.appendChild(resultInfo);
            
            historyList.appendChild(historyItem);
        }
    }
    
    function formatHandString(hand) {
        return hand.map(card => `${card.value}${card.suit}`).join(' ');
    }
    
    function updateRoads(winner) {
        // Update bead road
        updateBeadRoad(winner);
        
        // Update big road
        updateBigRoad(winner);
        
        // Update derived roads
        updateDerivedRoads();
    }
    
    function updateBeadRoad(winner) {
        // Add to bead plate
        const bead = document.createElement('div');
        
        if (winner === 'player') {
            bead.className = 'bead-player';
            bead.textContent = 'P';
        } else if (winner === 'banker') {
            bead.className = 'bead-banker';
            bead.textContent = 'B';
        } else {
            bead.className = 'bead-tie';
            bead.textContent = 'T';
        }
        
        // Add to beginning
        if (beadPlateGrid.children.length >= 60) {
            beadPlateGrid.removeChild(beadPlateGrid.lastChild);
        }
        
        beadPlateGrid.insertBefore(bead, beadPlateGrid.firstChild);
        
        // Update bead road in game state
        gameState.beadRoad.unshift(winner);
        if (gameState.beadRoad.length > 60) {
            gameState.beadRoad.pop();
        }
    }
    
    function updateBigRoad(winner) {
        // The big road is more complex
        // In a real implementation, this would be a more sophisticated algorithm
        if (winner === 'tie') {
            // In big road, ties are marked as dots on the last entry
            // For simplicity, we'll ignore ties for now
            return;
        }
        
        const bead = document.createElement('div');
        
        if (winner === 'player') {
            bead.className = 'big-road-player';
            bead.textContent = 'P';
        } else {
            bead.className = 'big-road-banker';
            bead.textContent = 'B';
        }
        
        // Add to beginning
        if (bigRoadGrid.children.length >= 60) {
            bigRoadGrid.removeChild(bigRoadGrid.lastChild);
        }
        
        bigRoadGrid.insertBefore(bead, bigRoadGrid.firstChild);
        
        // Update big road in game state
        if (winner !== 'tie') {
            gameState.bigRoad.unshift(winner);
            if (gameState.bigRoad.length > 60) {
                gameState.bigRoad.pop();
            }
        }
    }
    
    function updateDerivedRoads() {
        // In a real implementation, this would compute the derived roads
        // (Big Eye Boy, Small Road, Cockroach Pig) based on the Big Road
        // For this demo, we'll just add some random elements
        
        if (Math.random() > 0.5) {
            const bead = document.createElement('div');
            bead.className = Math.random() > 0.5 ? 'derived-red' : 'derived-blue';
            
            // Update Big Eye Boy
            if (bigEyeGrid.children.length >= 60) {
                bigEyeGrid.removeChild(bigEyeGrid.lastChild);
            }
            bigEyeGrid.insertBefore(bead, bigEyeGrid.firstChild);
        }
        
        if (Math.random() > 0.5) {
            const bead = document.createElement('div');
            bead.className = Math.random() > 0.5 ? 'derived-red' : 'derived-blue';
            
            // Update Small Road
            if (smallRoadGrid.children.length >= 60) {
                smallRoadGrid.removeChild(smallRoadGrid.lastChild);
            }
            smallRoadGrid.insertBefore(bead, smallRoadGrid.firstChild);
        }
        
        if (Math.random() > 0.5) {
            const bead = document.createElement('div');
            bead.className = Math.random() > 0.5 ? 'derived-red' : 'derived-blue';
            
            // Update Cockroach Pig
            if (cockroachGrid.children.length >= 60) {
                cockroachGrid.removeChild(cockroachGrid.lastChild);
            }
            cockroachGrid.insertBefore(bead, cockroachGrid.firstChild);
        }
    }
    
    function resetRoads() {
        // Clear all road displays
        beadPlateGrid.innerHTML = '';
        bigRoadGrid.innerHTML = '';
        bigEyeGrid.innerHTML = '';
        smallRoadGrid.innerHTML = '';
        cockroachGrid.innerHTML = '';
        
        // Reset road data in game state
        gameState.beadRoad = [];
        gameState.bigRoad = [];
    }
    
    function updateStatsDisplay() {
        handsPlayed.textContent = gameState.stats.handsPlayed;
        playerWins.textContent = gameState.stats.playerWins;
        bankerWins.textContent = gameState.stats.bankerWins;
        tieWins.textContent = gameState.stats.tieWins;
        playerPairs.textContent = gameState.stats.playerPairs;
        bankerPairs.textContent = gameState.stats.bankerPairs;
    }
    
    function updateCreditsDisplay() {
        creditsElement.textContent = Math.floor(gameState.gaCurrency);
    }
    
    function verifyHand() {
        const serverSeed = verifyServerSeed.value.trim();
        const clientSeed = verifyClientSeed.value.trim();
        const shoeId = parseInt(verifyShoeId.value);
        const roundId = parseInt(verifyRoundId.value);
        
        if (!serverSeed || !clientSeed || isNaN(shoeId) || isNaN(roundId)) {
            verifyResult.innerHTML = '<p class="error">Please fill in all fields correctly.</p>';
            return;
        }
        
        try {
            // Recreate the deck
            const deck = [];
            for (let i = 0; i < 8; i++) { // 8 decks
                for (const suit of suits) {
                    for (const value of values) {
                        deck.push({ value, suit });
                    }
                }
            }
            
            // Recreate the shuffle
            const seed = serverSeed + clientSeed + shoeId;
            const shuffledDeck = [...deck];
            
            // Fisher-Yates shuffle with seeded RNG
            let seedValue = hashToNumber(seed);
            
            for (let i = shuffledDeck.length - 1; i > 0; i--) {
                // Generate a seeded random number
                seedValue = (seedValue * 9301 + 49297) % 233280;
                const j = Math.floor((seedValue / 233280) * (i + 1));
                
                // Swap elements
                [shuffledDeck[i], shuffledDeck[j]] = [shuffledDeck[j], shuffledDeck[i]];
            }
            
            // Calculate which cards would be dealt in the specified round
            const startIndex = (roundId - 1) * 6; // Assuming max 6 cards per round
            const playerCard1 = shuffledDeck[startIndex];
            const bankerCard1 = shuffledDeck[startIndex + 1];
            const playerCard2 = shuffledDeck[startIndex + 2];
            const bankerCard2 = shuffledDeck[startIndex + 3];
            
            // Calculate initial scores
            const playerScore = (cardValues[playerCard1.value] + cardValues[playerCard2.value]) % 10;
            const bankerScore = (cardValues[bankerCard1.value] + cardValues[bankerCard2.value]) % 10;
            
            let resultHTML = `
                <h4>Verification Result</h4>
                <p><strong>Initial Player Hand:</strong> ${playerCard1.value}${playerCard1.suit} ${playerCard2.value}${playerCard2.suit} = ${playerScore}</p>
                <p><strong>Initial Banker Hand:</strong> ${bankerCard1.value}${bankerCard1.suit} ${bankerCard2.value}${bankerCard2.suit} = ${bankerScore}</p>
            `;
            
            // Determine if third cards would be drawn
            let playerCard3 = null;
            let bankerCard3 = null;
            let finalPlayerScore = playerScore;
            let finalBankerScore = bankerScore;
            
            // Check for naturals
            if (playerScore < 8 && bankerScore < 8) {
                // Player's third card rule
                if (playerScore <= 5) {
                    playerCard3 = shuffledDeck[startIndex + 4];
                    finalPlayerScore = (playerScore + cardValues[playerCard3.value]) % 10;
                    resultHTML += `<p><strong>Player draws third card:</strong> ${playerCard3.value}${playerCard3.suit}</p>`;
                    resultHTML += `<p><strong>Player final score:</strong> ${finalPlayerScore}</p>`;
                    
                    // Banker's third card rule
                    if (shouldBankerDrawThirdCard(bankerScore, cardValues[playerCard3.value])) {
                        bankerCard3 = shuffledDeck[startIndex + 5];
                        finalBankerScore = (bankerScore + cardValues[bankerCard3.value]) % 10;
                        resultHTML += `<p><strong>Banker draws third card:</strong> ${bankerCard3.value}${bankerCard3.suit}</p>`;
                        resultHTML += `<p><strong>Banker final score:</strong> ${finalBankerScore}</p>`;
                    } else {
                        resultHTML += `<p><strong>Banker stands</strong> with score of ${bankerScore}</p>`;
                    }
                } else {
                    resultHTML += `<p><strong>Player stands</strong> with score of ${playerScore}</p>`;
                    
                    // Banker draws with 0-5
                    if (bankerScore <= 5) {
                        bankerCard3 = shuffledDeck[startIndex + 4];
                        finalBankerScore = (bankerScore + cardValues[bankerCard3.value]) % 10;
                        resultHTML += `<p><strong>Banker draws third card:</strong> ${bankerCard3.value}${bankerCard3.suit}</p>`;
                        resultHTML += `<p><strong>Banker final score:</strong> ${finalBankerScore}</p>`;
                    } else {
                        resultHTML += `<p><strong>Banker stands</strong> with score of ${bankerScore}</p>`;
                    }
                }
            } else {
                resultHTML += `<p><strong>Natural! No third cards drawn.</strong></p>`;
            }
            
            // Determine winner based on final scores
            let winner;
            if (finalPlayerScore > finalBankerScore) {
                winner = 'Player';
            } else if (finalBankerScore > finalPlayerScore) {
                winner = 'Banker';
            } else {
                winner = 'Tie';
            }
            
            resultHTML += `<p><strong>Winner based on standard rules:</strong> ${winner}</p>`;
            resultHTML += `<p><em>Note: Actual game results may vary due to our adjusted probability system.</em></p>`;
            
            verifyResult.innerHTML = resultHTML;
            
        } catch (error) {
            verifyResult.innerHTML = `<p class="error">Verification error: ${error.message}</p>`;
        }
    }
    
    function updateCurrentShoeInfo() {
        if (gameState.currentShoe.id === 0) {
            currentShoeInfo.innerHTML = '<p>Deal a hand to see verification information</p>';
            return;
        }
        
        const info = `
            <p><strong>Current Shoe ID:</strong> ${gameState.currentShoe.id}</p>
            <p><strong>Server Seed Hash:</strong> ${gameState.provablyFair.serverSeedHash}</p>
            <p><strong>Client Seed:</strong> ${gameState.provablyFair.clientSeed}</p>
            <p><strong>Current Round:</strong> ${gameState.provablyFair.nonce}</p>
            <p><strong>Cards Remaining:</strong> ${gameState.currentShoe.cards.length - gameState.currentShoe.currentPosition}</p>
            <p>The server seed will be revealed when the shoe is completed.</p>
        `;
        
        currentShoeInfo.innerHTML = info;
    }
    
    function updateUI() {
        updateBetDisplay();
        updateGACurrencyDisplay();
        updateHistoryDisplay();
        updateStatsDisplay();
        
        // Update provably fair info
        serverSeedHash.textContent = gameState.provablyFair.serverSeedHash;
        clientSeedInput.value = gameState.provablyFair.clientSeed;
        
        // Update shoe info
        cardsInShoe.textContent = gameState.currentShoe.cards.length - gameState.currentShoe.currentPosition;
        roundId.textContent = gameState.provablyFair.nonce;
        
        // Update Pro stats if in Pro view
        if (gameState.viewMode === 'pro') {
            updateProStats();
        }
    }
});