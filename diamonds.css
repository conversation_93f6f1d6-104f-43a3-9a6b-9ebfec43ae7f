/* Diamonds Game - Mobile-First Responsive Design */

/* CSS Variables for theming and responsive design */
:root {
    --primary-color: #4527a0;
    --secondary-color: #7e57c2;
    --accent-color: #ff7043;
    --background-color: #0B0E1A;
    --card-color: #1A1F2E;
    --text-color: #ffffff;
    --border-color: #3a3a5a;
    --diamond-color: #00bcd4;
    --bomb-color: #ff5252;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --gold-gradient: linear-gradient(135deg, #f9d423, #f6b02b);
    
    /* Mobile-first grid sizes */
    --grid-tile-size: 50px;
    --grid-gap: 8px;
    --mobile-padding: 1rem;
    
    /* Z-index layers */
    --z-index-background: 1;
    --z-index-content: 10;
    --z-index-modal: 100;
    --z-index-notification: 200;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--background-color) 0%, #1A1F2E 50%, #2C1810 100%);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Button base styles */
button {
    cursor: pointer;
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-family: inherit;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-height: 44px; /* Touch target size */
    min-width: 44px;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

button:active {
    transform: scale(0.95);
}

.hidden {
    display: none !important;
}

/* View Mode Toggle */
.view-mode-toggle {
    position: fixed;
    top: 100px;
    right: 1rem;
    z-index: var(--z-index-notification);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.3rem;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.view-toggle-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    min-width: 60px;
    text-transform: uppercase;
    font-weight: 600;
}

.view-toggle-btn.active {
    background: var(--gold-gradient);
    color: var(--background-color);
    transform: scale(1.05);
}

/* Main Game Container */
.game-container {
    max-width: 100vw;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
}

/* Header Styles */
.game-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 1rem 0;
}

.game-header h1 {
    font-size: 2rem;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    font-weight: 700;
    line-height: 1.2;
}

.tagline {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    opacity: 0.9;
}

/* Setup Panel - Mobile First */
.setup-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.setup-section {
    margin-bottom: 1.5rem;
}

.setup-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--diamond-color);
    font-size: 0.9rem;
}

/* Mobile-optimized bomb selector */
.bomb-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bomb-btn {
    background: var(--secondary-color);
    color: white;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.bomb-btn:hover {
    background: var(--primary-color);
    transform: scale(1.05);
}

.bomb-btn:active {
    transform: scale(0.95);
}

#bombDisplay, #betDisplay {
    font-size: 1.8rem;
    font-weight: bold;
    min-width: 80px;
    text-align: center;
    color: var(--diamond-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Probability Display */
.probability-display {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.probability-display p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#diamondProb, #multiplierPreview {
    font-weight: bold;
    color: var(--diamond-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Bet Controls */
.bet-controls {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.bet-controls label {
    margin-bottom: 0.8rem;
}

.bet-amount-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 1rem 0;
}

/* Balance Display */
.balance-display {
    margin-top: 1rem;
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.balance-display p {
    margin: 0;
    font-size: 1rem;
}

#balanceDisplay {
    font-weight: bold;
    color: var(--diamond-color);
    font-size: 1.2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Fairness Section */
.fairness-section {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.fairness-section h3 {
    margin-bottom: 1rem;
    color: var(--diamond-color);
    font-size: 1rem;
    text-align: center;
}

.seed-info {
    margin: 1rem 0;
}

.seed-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.seed-row label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.3rem;
}

.seed-value {
    font-family: 'Courier New', monospace;
    background: rgba(0, 0, 0, 0.3);
    padding: 0.5rem;
    border-radius: 8px;
    word-break: break-all;
    font-size: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#clientSeed {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.75rem;
    border-radius: 8px;
    width: 100%;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.seed-input-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#randomizeSeed {
    background: var(--secondary-color);
    color: white;
    min-width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    padding: 0;
    font-size: 1.2rem;
}

/* Start Button */
.start-btn {
    background: var(--accent-color);
    color: white;
    padding: 1.2rem 2rem;
    font-size: 1.1rem;
    border-radius: 15px;
    width: 100%;
    margin-top: 1.5rem;
    box-shadow: 0 4px 16px rgba(255, 112, 67, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.start-btn:hover {
    background: #ff5722;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 112, 67, 0.4);
}

/* Game Panel */
.game-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Mobile-optimized game stats */
.game-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    background: rgba(0, 0, 0, 0.2);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.stat-item label {
    font-size: 0.7rem;
    opacity: 0.8;
    display: block;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-item span {
    font-size: 1.1rem;
    font-weight: bold;
    display: block;
}

#currentMultiplier, #potentialWinnings {
    color: var(--diamond-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Mobile-First Game Grid */
.game-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: var(--grid-gap);
    margin: 1.5rem 0;
    justify-content: center;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.grid-square {
    width: var(--grid-tile-size);
    height: var(--grid-tile-size);
    background: var(--secondary-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.grid-square:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.grid-square:active {
    transform: scale(0.95);
}

.grid-square.revealed {
    cursor: default;
    transform: scale(1);
}

.grid-square.diamond {
    background: var(--diamond-color);
    color: white;
    animation: diamondPulse 0.6s ease-out;
}

.grid-square.bomb {
    background: var(--bomb-color);
    color: white;
    animation: bombShake 0.6s ease-out;
}

/* Animations */
@keyframes diamondPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); box-shadow: 0 0 20px var(--diamond-color); }
    100% { transform: scale(1); }
}

@keyframes bombShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Game Controls */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.cash-out-btn {
    background: var(--success-color);
    color: white;
    padding: 1.2rem;
    font-size: 1.1rem;
    border-radius: 15px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
}

.cash-out-btn:hover:not(:disabled) {
    background: #43a047;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.new-game-btn {
    background: var(--secondary-color);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.new-game-btn:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* Pro View Panel */
.pro-view-panel {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

body.pro-view-active .pro-view-panel {
    display: block;
}

.pro-panel-title {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--diamond-color);
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
}

.pro-stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pro-stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.pro-stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-stat-value {
    color: var(--diamond-color);
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Risk Analysis */
.risk-analysis {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.risk-analysis h4 {
    color: var(--warning-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-level {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
}

.risk-indicator {
    display: flex;
    gap: 2px;
}

.risk-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
}

.risk-dot.active {
    background: var(--warning-color);
}

.risk-dot.high {
    background: var(--bomb-color);
}

/* Recent Games History */
.recent-games {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.recent-games h4 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.game-history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    font-size: 0.8rem;
}

.game-result {
    font-weight: bold;
}

.game-result.win {
    color: var(--success-color);
}

.game-result.loss {
    color: var(--bomb-color);
}

/* Results Panel */
.results-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.result-content {
    max-width: 100%;
    margin: 0 auto;
}

#resultTitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.result-stats {
    margin: 1.5rem 0;
    font-size: 1rem;
}

.result-stats p {
    margin-bottom: 0.8rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#finalMultiplier, #finalResult {
    font-weight: bold;
    color: var(--diamond-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.verification-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verification-section h3 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.seed-reveal {
    margin-bottom: 1rem;
}

.seed-reveal label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

#verifyRound {
    background: var(--secondary-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.play-again-btn {
    background: var(--accent-color);
    color: white;
    padding: 1.2rem 2rem;
    font-size: 1.1rem;
    margin-top: 1.5rem;
    border-radius: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 16px rgba(255, 112, 67, 0.3);
}

.play-again-btn:hover {
    background: #ff5722;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 112, 67, 0.4);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-modal);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-color);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.modal-content h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--diamond-color);
    font-size: 1.3rem;
}

.rules-content {
    margin-bottom: 1.5rem;
}

.rules-content h3 {
    margin: 1rem 0 0.8rem;
    color: var(--secondary-color);
    font-size: 1rem;
}

.rules-content ul {
    padding-left: 1.2rem;
    margin-bottom: 1rem;
}

.rules-content li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.close-modal-btn {
    background: var(--accent-color);
    color: white;
    padding: 1rem 2rem;
    display: block;
    margin: 1.5rem auto 0;
    border-radius: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Footer */
.game-footer {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.info-btn {
    background: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.8rem 1.5rem;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.info-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, var(--secondary-color), var(--diamond-color));
    color: white;
    padding: 1rem;
    border-radius: 10px;
    z-index: var(--z-index-notification);
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: calc(100vw - 40px);
    font-size: 0.9rem;
}

.notification.show {
    transform: translateX(0);
}

/* Tablet Landscape (768px+) */
@media (min-width: 768px) and (orientation: landscape) {
    :root {
        --grid-tile-size: 60px;
        --grid-gap: 10px;
        --mobile-padding: 2rem;
    }
    
    .game-container {
        max-width: 1000px;
        padding: var(--mobile-padding);
    }
    
    .game-header h1 {
        font-size: 2.5rem;
    }
    
    .tagline {
        font-size: 1rem;
    }
    
    .setup-panel {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: start;
    }
    
    .game-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .game-grid {
        max-width: 350px;
    }
    
    .game-controls {
        flex-direction: row;
        gap: 1.5rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .seed-row {
        flex-direction: row;
        align-items: center;
    }
    
    .seed-row label {
        min-width: 150px;
    }
    
    .seed-input-container {
        flex: 1;
    }
}

/* Tablet Portrait (768px+) */
@media (min-width: 768px) and (orientation: portrait) {
    :root {
        --grid-tile-size: 65px;
    }
    
    .game-container {
        max-width: 600px;
    }
    
    .game-grid {
        max-width: 380px;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    :root {
        --grid-tile-size: 70px;
        --grid-gap: 12px;
        --mobile-padding: 2.5rem;
    }
    
    .game-container {
        max-width: 1200px;
        padding: var(--mobile-padding);
    }
    
    .game-header h1 {
        font-size: 3rem;
    }
    
    .tagline {
        font-size: 1.2rem;
    }
    
    .setup-panel {
        padding: 2rem;
    }
    
    .game-grid {
        max-width: 410px;
        gap: var(--grid-gap);
    }
    
    .grid-square {
        font-size: 2rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .modal-content {
        max-width: 700px;
        padding: 2.5rem;
    }
    
    .game-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }
    
    .stat-item {
        padding: 1.5rem;
    }
    
    .stat-item span {
        font-size: 1.3rem;
    }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
    :root {
        --grid-tile-size: 80px;
    }
    
    .game-container {
        max-width: 1400px;
    }
    
    .game-grid {
        max-width: 480px;
    }
    
    .grid-square {
        font-size: 2.2rem;
    }
}

/* Touch optimization for mobile devices */
@media (hover: none) {
    .grid-square:hover,
    .bomb-btn:hover,
    .start-btn:hover,
    .cash-out-btn:hover,
    .new-game-btn:hover,
    .play-again-btn:hover,
    .info-btn:hover,
    .stat-item:hover,
    .pro-stat-card:hover {
        transform: none;
        box-shadow: initial;
    }
    
    /* Ensure all interactive elements meet touch target requirements */
    .bomb-btn,
    .grid-square,
    button {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Prevent zoom on input focus (iOS Safari) */
@media (max-width: 767px) {
    #clientSeed {
        font-size: 16px;
    }
}

/* Dark mode optimization */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #000000 0%, #1A1A1A 50%, #2C1810 100%);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .grid-square,
    .bomb-btn,
    .stat-item,
    .pro-stat-card {
        border: 2px solid white;
    }
    
    .setup-panel,
    .game-panel,
    .results-panel,
    .pro-view-panel {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .grid-square,
    .bomb-btn,
    .start-btn,
    .cash-out-btn,
    .new-game-btn,
    .play-again-btn,
    .info-btn,
    .stat-item,
    .pro-stat-card {
        transition: none;
        animation: none;
    }
    
    @keyframes diamondPulse {
        0%, 100% { transform: scale(1); }
    }
    
    @keyframes bombShake {
        0%, 100% { transform: translateX(0); }
    }
}