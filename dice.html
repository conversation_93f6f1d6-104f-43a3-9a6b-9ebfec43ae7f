<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dice <PERSON> - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dice.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div id="winLossIndicator" class="win-loss-indicator">
                    <!-- Will be filled by JS when win/loss occurs -->
                </div>
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="dice-rush-container">
            <!-- Game Title and Back Button -->
            <div class="dice-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">DICE RUSH</h1>
                <div class="view-toggle">
                    <button id="standardView" class="view-btn active">Standard</button>
                    <button id="proView" class="view-btn">Pro View</button>
                </div>
            </div>

            <!-- Mobile Wallet Display -->
            <div class="mobile-wallet">
                <div class="wallet-balance">
                    <i class="fas fa-wallet"></i>
                    <span id="mobileBalanceValue">1000 GA</span>
                </div>
                <div class="mobile-score">
                    <i class="fas fa-trophy"></i>
                    <span id="mobileScoreValue">Score: 0</span>
                </div>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <!-- Left Column: Controls and Stats -->
                <div class="dashboard-left">
                    <!-- Game Stats -->
                    <div class="game-stats">
                        <div class="stat-item balance">
                            <span class="stat-label">Balance</span>
                            <span class="stat-value" id="balanceValue">1000 GA</span>
                        </div>
                        <div class="stat-item score">
                            <span class="stat-label">Score</span>
                            <span class="stat-value" id="scoreValue">0</span>
                        </div>
                        <div class="stat-item multiplier">
                            <span class="stat-label">Multiplier</span>
                            <span class="stat-value" id="multiplierValue">1.0×</span>
                        </div>
                        <div class="stat-item turns">
                            <span class="stat-label">Turns</span>
                            <span class="stat-value" id="turnsValue">1/10</span>
                        </div>
                    </div>
                    
                    <!-- Betting Controls -->
                    <div class="betting-controls">
                        <div class="bet-amount-control">
                            <label for="betAmount">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <button class="bet-btn" id="decreaseBet">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="betAmount" class="bet-input" value="10" min="10" step="10">
                                <button class="bet-btn" id="increaseBet">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Bet Presets -->
                        <div class="bet-presets">
                            <button class="preset-btn" data-amount="10">10</button>
                            <button class="preset-btn" data-amount="50">50</button>
                            <button class="preset-btn" data-amount="100">100</button>
                            <button class="preset-btn" data-amount="500">500</button>
                            <button class="preset-btn max" data-amount="max">MAX</button>
                        </div>
                    </div>
                    
                    <!-- Game Configuration -->
                    <div class="game-config">
                        <div class="config-group">
                            <label for="diceCount">Dice Count</label>
                            <select id="diceCount" class="config-select">
                                <option value="2">2 Dice</option>
                                <option value="3" selected>3 Dice</option>
                                <option value="4">4 Dice</option>
                                <option value="5">5 Dice</option>
                            </select>
                        </div>
                        
                        <div class="config-group">
                            <label for="difficulty">Difficulty</label>
                            <select id="difficulty" class="config-select">
                                <option value="easy">Easy (1000 pts)</option>
                                <option value="medium" selected>Medium (1500 pts)</option>
                                <option value="hard">Hard (2000 pts)</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="game-actions">
                        <button id="startGameBtn" class="btn btn-primary action-btn">
                            <i class="fas fa-play"></i> New Game
                        </button>
                        <button id="autoRollBtn" class="btn btn-secondary action-btn">
                            <i class="fas fa-magic"></i> Auto Roll
                        </button>
                        <button id="soundToggle" class="btn btn-icon">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Right Column: Game Board -->
                <div class="dashboard-right">
                    <!-- Game Status -->
                    <div id="gameStatus" class="game-status">
                        Roll the dice to start the game!
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="progress-container">
                        <div class="progress-label">
                            <span>Progress to Win</span>
                            <span id="progressPercent">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div id="progressFill" class="progress-fill"></div>
                        </div>
                    </div>
                    
                    <!-- Dice Rolling Area -->
                    <div class="dice-table">
                        <div id="diceContainer" class="dice-container">
                            <!-- Dice will be generated here by JavaScript -->
                            <div class="dice-placeholder">
                                <i class="fas fa-dice"></i>
                                <span>Roll to start!</span>
                            </div>
                        </div>
                        
                        <div class="dice-controls">
                            <button id="rollBtn" class="btn btn-primary action-btn roll-btn">
                                <i class="fas fa-dice"></i> Roll Dice
                            </button>
                            <button id="continuousRollBtn" class="btn btn-secondary action-btn continuous-roll-btn">
                                <i class="fas fa-dice-d20"></i> Auto Roll
                            </button>
                            <div id="rollsCounter" class="rolls-counter">Roll #1</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Game Controls -->
            <div class="mobile-controls">
                <div class="mobile-actions">
                    <button id="mobileRollBtn" class="btn btn-primary mobile-btn">
                        <i class="fas fa-dice"></i> Roll Dice
                    </button>
                    <button id="mobileAutoRollBtn" class="btn btn-secondary mobile-btn">
                        <i class="fas fa-magic"></i> Auto Roll
                    </button>
                </div>
                <div class="mobile-quick-actions">
                    <button id="mobileClaimBtn" class="btn btn-success mobile-btn" disabled>
                        <i class="fas fa-check"></i> Claim
                    </button>
                    <button id="mobileRerollBtn" class="btn btn-warning mobile-btn" disabled>
                        <i class="fas fa-redo"></i> Reroll
                    </button>
                </div>
            </div>
            
            <!-- Target Cards -->
            <div class="target-section">
                <h2 class="section-title">
                    Available Targets
                    <button id="targetToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="target-content">
                    <div id="targetCards" class="target-cards">
                        <!-- Target cards will be generated here by JavaScript -->
                    </div>
                </div>
            </div>
            
            <!-- Game Controls -->
            <div class="game-controls">
                <div class="control-info">
                    <p><i class="fas fa-info-circle"></i> Match dice to targets and score points. Risk rerolling for higher multipliers!</p>
                </div>
                <div class="control-buttons">
                    <button id="rerollBtn" class="btn btn-warning action-btn" disabled>
                        <i class="fas fa-redo"></i> Risk Reroll
                    </button>
                    <button id="claimBtn" class="btn btn-success action-btn" disabled>
                        <i class="fas fa-check"></i> Claim Target
                    </button>
                    <button id="rulesBtn" class="btn btn-info action-btn">
                        <i class="fas fa-question-circle"></i> Rules
                    </button>
                </div>
            </div>
            
            <!-- Game Statistics (Pro View) -->
            <div class="game-statistics">
                <h2 class="stats-title">
                    Game Statistics
                    <button id="statsToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="stats-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-card-title">Games Played</div>
                            <div class="stat-card-value" id="gamesPlayed">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card-title">Win Rate</div>
                            <div class="stat-card-value" id="winRate">0%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card-title">Avg Score</div>
                            <div class="stat-card-value" id="avgScore">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card-title">Best Score</div>
                            <div class="stat-card-value" id="bestScore">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card-title">Total Winnings</div>
                            <div class="stat-card-value" id="totalWinnings">0 GA</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-card-title">Highest Multiplier</div>
                            <div class="stat-card-value" id="highestMultiplier">1.0×</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Game History -->
            <div class="game-history">
                <h2 class="history-title">
                    Game History
                    <button id="historyToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="history-content">
                    <div class="history-table-container">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Game</th>
                                    <th>Score</th>
                                    <th>Turns</th>
                                    <th>Bet</th>
                                    <th>Result</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- History entries will be added here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Rules Modal -->
            <div id="rulesModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>How to Play Dice Rush</h2>
                    <div class="rules-content">
                        <h3>Game Objective</h3>
                        <p>Roll dice to match target patterns and score points. Reach the target score within 10 turns to win and earn GA currency!</p>
                        <p>Place a bet with your GA currency and win up to 3x your bet if you score high enough!</p>
                        <p>You can roll dice multiple times per turn until you find a pattern you want to claim, or up to 10 rolls maximum.</p>
                        
                        <h3>Scoring Targets</h3>
                        <ul>
                            <li><strong>Pairs/Triples:</strong> Match 2+ of the same number (e.g., two 5s = 50 pts)</li>
                            <li><strong>Sequences:</strong> Get consecutive numbers (e.g., 3-4-5 = 100 pts)</li>
                            <li><strong>Combinations:</strong> Special sets like all even numbers (200 pts)</li>
                        </ul>
                        
                        <h3>Risk & Reward</h3>
                        <p>After each roll, you can either:</p>
                        <ul>
                            <li><strong>Claim a Target:</strong> Score points and end your turn</li>
                            <li><strong>Risk Reroll:</strong> Try for better targets with a higher multiplier</li>
                        </ul>
                        <p>Careful! If you reroll and get no valid targets, you lose unclaimed points!</p>
                        
                        <h3>Difficulty Levels</h3>
                        <ul>
                            <li><strong>Easy:</strong> Win at 1000 points - 3× payout</li>
                            <li><strong>Medium:</strong> Win at 1500 points - 5× payout</li>
                            <li><strong>Hard:</strong> Win at 2000 points - 8× payout</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="assets/js/dice.js"></script>
</body>
</html>