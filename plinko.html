<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plinko - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/plinko.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div id="headerWinLossIndicator" class="header-win-loss-indicator">
                    <!-- Will be filled by JS when win/loss occurs -->
                </div>
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="plinko-container">
            <!-- Game Title and Back Button -->
            <div class="plinko-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">PLINKO</h1>
                <div class="view-toggle">
                    <button id="standardView" class="view-btn active">Standard</button>
                    <button id="proView" class="view-btn">Pro View</button>
                </div>
            </div>

            <!-- Mobile Wallet Display -->
            <div class="mobile-wallet">
                <div class="wallet-balance">
                    <i class="fas fa-wallet"></i>
                    <span id="mobileBalanceValue">1000 GA</span>
                </div>
                <div class="mobile-last-win">
                    <i class="fas fa-trophy"></i>
                    <span id="mobileLastWin">Last: --</span>
                </div>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <!-- Left Column: Controls and Stats -->
                <div class="dashboard-left">
                    <!-- Balance Display -->
                    <div class="balance-display">
                        <div class="balance-label">Balance</div>
                        <div id="balanceValue" class="balance-value">1000 GA</div>
                        <div id="winLossIndicator" class="win-loss-indicator"></div>
                    </div>
                    
                    <!-- Betting Controls -->
                    <div class="betting-controls">
                        <div class="bet-amount-control">
                            <label for="betAmount">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <button class="bet-btn" id="decreaseBet">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="betAmount" class="bet-input" value="10" min="1" step="1">
                                <button class="bet-btn" id="increaseBet">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Bet Presets -->
                        <div class="bet-presets">
                            <button class="preset-btn" data-amount="10">10</button>
                            <button class="preset-btn" data-amount="50">50</button>
                            <button class="preset-btn" data-amount="100">100</button>
                            <button class="preset-btn" data-amount="500">500</button>
                            <button class="preset-btn max" data-amount="max">MAX</button>
                        </div>
                    </div>
                    
                    <!-- Board Configuration -->
                    <div class="board-config">
                        <div class="config-group">
                            <label for="boardSize">Board Size</label>
                            <select id="boardSize" class="config-select">
                                <option value="small">Small (8 rows)</option>
                                <option value="medium" selected>Medium (10 rows)</option>
                                <option value="large">Large (12 rows)</option>
                                <option value="extreme">Extreme (14 rows)</option>
                            </select>
                        </div>
                        
                        <div class="config-group">
                            <label for="riskLevel">Risk Level</label>
                            <select id="riskLevel" class="config-select">
                                <option value="low">Low (More winners)</option>
                                <option value="medium" selected>Medium (Balanced)</option>
                                <option value="high">High (Higher jackpots)</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Auto Drop Controls -->
                    <div class="auto-drop-controls">
                        <div class="auto-drop-header">
                            <label for="autoDrop">Auto Drop</label>
                            <label class="switch">
                                <input type="checkbox" id="autoDrop">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        
                        <div class="auto-drop-settings">
                            <div class="auto-drop-group">
                                <label for="autoDropCount">Drops</label>
                                <div class="count-input-group">
                                    <button class="count-btn" id="decreaseCount">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" id="autoDropCount" class="count-input" value="5" min="1" max="100" step="1">
                                    <button class="count-btn" id="increaseCount">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="auto-drop-presets">
                                <button class="drop-preset-btn" data-count="5">5</button>
                                <button class="drop-preset-btn" data-count="10">10</button>
                                <button class="drop-preset-btn" data-count="25">25</button>
                                <button class="drop-preset-btn" data-count="50">50</button>
                                <button class="drop-preset-btn" data-count="100">100</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="game-actions">
                        <button id="dropBallBtn" class="btn btn-primary play-btn">
                            <i class="fas fa-circle"></i> Drop Ball
                        </button>
                        <button id="resetBtn" class="btn btn-secondary reset-btn">
                            <i class="fas fa-sync-alt"></i> Reset
                        </button>
                        <button id="soundToggle" class="btn btn-icon">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Right Column: Game Board -->
                <div class="dashboard-right">
                    <!-- Game Status -->
                    <div id="gameStatus" class="game-status">
                        Select a drop position and click "Drop Ball" to start!
                    </div>
                    
                    <!-- Plinko Board -->
                    <div class="plinko-board-container">
                        <!-- Drop Zone -->
                        <div class="drop-zone" id="dropZone">
                            <!-- Drop slots will be generated here -->
                        </div>
                        
                        <!-- Board Canvas -->
                        <canvas id="plinkoCanvas" class="plinko-canvas"></canvas>
                        
                        <!-- Slot Zone -->
                        <div class="slot-zone" id="slotZone">
                            <!-- Slot values will be generated here -->
                        </div>
                    </div>
                    
                    <!-- Auto Drop Progress -->
                    <div class="auto-drop-progress">
                        <div class="progress-info">
                            <span id="autoDropStatus">Auto Drop: Off</span>
                            <span id="autoDropRemaining"></span>
                        </div>
                        <div class="progress-bar">
                            <div id="autoDropProgressFill" class="progress-fill"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Controls -->
            <div class="mobile-controls">
                <div class="mobile-ball-controls">
                    <button id="mobileDropBallBtn" class="btn btn-primary mobile-btn">
                        <i class="fas fa-circle"></i> Drop Ball
                    </button>
                    <button id="mobileAutoBallBtn" class="btn btn-secondary mobile-btn">
                        <i class="fas fa-magic"></i> Auto Drop
                    </button>
                </div>
                <div class="mobile-settings">
                    <button id="mobileBetBtn" class="btn btn-icon mobile-icon-btn">
                        <i class="fas fa-coins"></i>
                    </button>
                    <button id="mobileBoardBtn" class="btn btn-icon mobile-icon-btn">
                        <i class="fas fa-th"></i>
                    </button>
                    <button id="mobileSoundBtn" class="btn btn-icon mobile-icon-btn">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
            </div>
            
            <!-- Drop Statistics Panel -->
            <div class="drop-statistics">
                <h2 class="stats-title">
                    Drop Statistics
                    <button id="statsToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="stats-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">Balls Dropped</div>
                            <div class="stat-value" id="ballsDropped">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Last Win</div>
                            <div class="stat-value" id="lastPoints">--</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Best Win</div>
                            <div class="stat-value" id="bestPoints">--</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Total Wagered</div>
                            <div class="stat-value" id="totalWagered">0 GA</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Win Rate</div>
                            <div class="stat-value" id="winRate">0%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Profit/Loss</div>
                            <div class="stat-value" id="profitLoss">0 GA</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Slot Statistics Panel (Pro View) -->
            <div class="slot-statistics">
                <h2 class="slot-stats-title">
                    Slot Analytics
                    <button id="slotStatsToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="slot-stats-content">
                    <div class="slot-stats-chart-container">
                        <canvas id="slotStatsChart" class="slot-stats-chart"></canvas>
                    </div>
                    <div class="slot-stats-table-container">
                        <table class="slot-stats-table">
                            <thead>
                                <tr>
                                    <th>Slot</th>
                                    <th>Value</th>
                                    <th>Hits</th>
                                    <th>Win %</th>
                                    <th>Total Won</th>
                                </tr>
                            </thead>
                            <tbody id="slotStatsTableBody">
                                <!-- Slot stats will be added here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Game History -->
            <div class="game-history">
                <h2 class="history-title">
                    Game History
                    <button id="historyToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="history-content">
                    <div class="history-table-container">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Game #</th>
                                    <th>Bet</th>
                                    <th>Board</th>
                                    <th>Position</th>
                                    <th>Slot</th>
                                    <th>Result</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- History entries will be added here -->
                            </tbody>
                        </table>
                    </div>
                    <div class="history-placeholder" id="historyPlaceholder">
                        No games played yet
                    </div>
                </div>
            </div>
            
            <!-- Mobile Bet Settings Modal -->
            <div id="betSettingsModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>Bet Settings</h2>
                    <div class="modal-body">
                        <div class="modal-form-group">
                            <label for="modalBetAmount">Bet Amount (GA)</label>
                            <div class="modal-input-group">
                                <button class="modal-btn-minus" id="modalDecreaseBet">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="modalBetAmount" class="modal-input" value="10" min="1" step="1">
                                <button class="modal-btn-plus" id="modalIncreaseBet">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="modal-preset-group">
                            <button class="modal-preset-btn" data-amount="10">10</button>
                            <button class="modal-preset-btn" data-amount="50">50</button>
                            <button class="modal-preset-btn" data-amount="100">100</button>
                            <button class="modal-preset-btn" data-amount="500">500</button>
                            <button class="modal-preset-btn max" data-amount="max">MAX</button>
                        </div>
                        
                        <div class="modal-form-group">
                            <label for="modalAutoDropCount">Auto Drop Count</label>
                            <div class="modal-input-group">
                                <button class="modal-btn-minus" id="modalDecreaseCount">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="modalAutoDropCount" class="modal-input" value="5" min="1" max="100" step="1">
                                <button class="modal-btn-plus" id="modalIncreaseCount">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="modal-auto-toggle">
                            <label for="modalAutoDrop">Auto Drop</label>
                            <label class="modal-switch">
                                <input type="checkbox" id="modalAutoDrop">
                                <span class="modal-slider round"></span>
                            </label>
                        </div>
                        
                        <button id="modalApplyBtn" class="btn btn-primary modal-apply-btn">Apply Settings</button>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Board Settings Modal -->
            <div id="boardSettingsModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>Board Settings</h2>
                    <div class="modal-body">
                        <div class="modal-form-group">
                            <label for="modalBoardSize">Board Size</label>
                            <select id="modalBoardSize" class="modal-select">
                                <option value="small">Small (8 rows)</option>
                                <option value="medium" selected>Medium (10 rows)</option>
                                <option value="large">Large (12 rows)</option>
                                <option value="extreme">Extreme (14 rows)</option>
                            </select>
                        </div>
                        
                        <div class="modal-form-group">
                            <label for="modalRiskLevel">Risk Level</label>
                            <select id="modalRiskLevel" class="modal-select">
                                <option value="low">Low (More winners)</option>
                                <option value="medium" selected>Medium (Balanced)</option>
                                <option value="high">High (Higher jackpots)</option>
                            </select>
                        </div>
                        
                        <button id="modalApplyBoardBtn" class="btn btn-primary modal-apply-btn">Apply Settings</button>
                    </div>
                </div>
            </div>
            
            <!-- Rules Modal -->
            <div id="rulesModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>How to Play Plinko</h2>
                    <div class="rules-content">
                        <h3>Game Objective</h3>
                        <p>Drop a ball from the top of the board and watch it bounce through a series of pegs to land in a scoring slot at the bottom. Win GA currency when your ball lands in a winning slot!</p>
                        
                        <h3>How to Play</h3>
                        <ol>
                            <li>Enter your bet amount in GA (Golden Aura currency)</li>
                            <li>Click one of the drop zones at the top of the board to select your starting position</li>
                            <li>Click "Drop Ball" to drop a single ball or enable "Auto Drop" for multiple balls</li>
                            <li>Watch as the ball bounces unpredictably through the pegs</li>
                            <li>Win GA based on your bet amount multiplied by the slot value it lands in</li>
                        </ol>
                        
                        <h3>Board Sizes</h3>
                        <ul>
                            <li><strong>Small:</strong> 8 rows, fewer pegs, less variance</li>
                            <li><strong>Medium:</strong> 10 rows, balanced gameplay</li>
                            <li><strong>Large:</strong> 12 rows, more variance and unpredictability</li>
                            <li><strong>Extreme:</strong> 14 rows, maximum unpredictability</li>
                        </ul>
                        
                        <h3>Risk Levels</h3>
                        <ul>
                            <li><strong>Low:</strong> More winning slots with smaller payouts</li>
                            <li><strong>Medium:</strong> Balanced winning and losing slots</li>
                            <li><strong>High:</strong> Fewer winning slots but higher payouts</li>
                        </ul>
                        
                        <h3>Pro View Features</h3>
                        <p>Enable Pro View mode to access advanced statistics, slot analytics, and detailed game history for deeper insights into your gameplay.</p>
                        
                        <h3>Physics</h3>
                        <p>The ball bounces realistically through the pegs with physics-based collisions, creating an exciting and unpredictable gameplay experience!</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Audio Elements (Hidden) -->
    <audio id="pegHitSound" src="assets/sounds/peg_hit.mp3" preload="auto"></audio>
    <audio id="slotSound" src="assets/sounds/slot_win.mp3" preload="auto"></audio>
    <audio id="winSound" src="assets/sounds/win.mp3" preload="auto"></audio>
    <audio id="loseSound" src="assets/sounds/lose.mp3" preload="auto"></audio>
    <audio id="clickSound" src="assets/sounds/click.mp3" preload="auto"></audio>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/plinko.js"></script>
</body>
</html>