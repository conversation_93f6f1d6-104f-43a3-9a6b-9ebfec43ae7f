/* Mobile-First Responsive Design for Craps Game */

:root {
    /* Color Scheme */
    --craps-green: #0B6E4F;
    --craps-dark-green: #033E34;
    --craps-red: #E63946;
    --craps-blue: #1D3557;
    --craps-light-blue: #457B9D;
    --craps-yellow: #F9C74F;
    --craps-orange: #F4A261;
    --craps-dark: #2F3E46;
    --craps-gray: #A8DADC;
    --craps-light: #F1FAEE;
    
    /* Game Colors */
    --win-color: #4CAF50;
    --lose-color: #E63946;
    --pass-line-color: #1D3557;
    --dont-pass-color: #E63946;
    --point-color: #F9C74F;
    
    /* Gradients */
    --background-gradient: linear-gradient(135deg, #0B6E4F 0%, #044A39 100%);
    --table-gradient: linear-gradient(135deg, #033E34 0%, #0B6E4F 100%);
    --card-gradient: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Z-index hierarchy for mobile touch */
    --z-background: 1;
    --z-table: 10;
    --z-pass-line: 20;
    --z-dont-pass: 20;
    --z-point-box: 30;
    --z-prop-bets: 100;
    --z-notifications: 1000;
    --z-modal: 2000;
}

/* Global Mobile-First Styles */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    background: var(--background-gradient);
    color: white;
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile Container */
.craps-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
}

/* Game Header - Mobile First */
.game-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.game-title {
    font-size: 2rem;
    background: linear-gradient(45deg, var(--craps-yellow), var(--craps-orange));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.game-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Mobile Dashboard Grid */
.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Mobile Panels */
.stats-panel, .fairness-panel {
    background: var(--card-gradient);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: var(--z-background);
}

.panel-title {
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Mobile Stat Cards */
.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    touch-action: manipulation;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
}

/* Mobile Craps Table */
.craps-table {
    background: var(--table-gradient);
    border-radius: 15px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: var(--z-table);
    order: -1; /* Put table first on mobile */
}

/* Mobile Bet Selector */
.bet-selector {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    position: relative;
    z-index: var(--z-background);
    flex-wrap: wrap;
}

.bet-type {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    touch-action: manipulation;
    font-size: 0.8rem;
    white-space: nowrap;
}

.bet-type:hover, .bet-type:active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.bet-type.selected {
    background: var(--craps-yellow);
    color: var(--craps-dark);
    border-color: var(--craps-orange);
}

/* Mobile Bet Controls */
.bet-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bet-amount-control {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 150px;
}

.bet-adjust-btn {
    background: rgba(0, 0, 0, 0.3);
    border: none;
    color: white;
    border-radius: 5px;
    width: 35px;
    height: 35px;
    cursor: pointer;
    font-size: 1.2rem;
    touch-action: manipulation;
    transition: all 0.2s ease;
}

.bet-adjust-btn:hover, .bet-adjust-btn:active {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(0.95);
}

.current-bet {
    font-weight: bold;
    color: var(--craps-yellow);
    min-width: 60px;
    text-align: center;
}

.total-bets {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.total-bet-amount {
    font-weight: bold;
    color: var(--craps-orange);
}

/* Mobile Table Layout */
.table-layout {
    width: 100%;
    position: relative;
    margin-bottom: 1.5rem;
    border: 3px solid var(--craps-light);
    border-radius: 10px;
    padding: 1rem;
    background: var(--craps-dark-green);
    min-height: 250px;
    touch-action: manipulation;
}

/* Mobile Pass Line */
.pass-line {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    height: 50px;
    background: rgba(29, 53, 87, 0.3);
    border: 2px solid var(--pass-line-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: var(--z-pass-line);
    touch-action: manipulation;
    font-size: 0.9rem;
    font-weight: 600;
}

.pass-line:hover, .pass-line:active {
    background: rgba(29, 53, 87, 0.5);
}

.pass-line.selected {
    background: rgba(29, 53, 87, 0.7);
    box-shadow: 0 0 10px rgba(29, 53, 87, 0.7);
}

/* Mobile Don't Pass */
.dont-pass {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
    height: 50px;
    background: rgba(230, 57, 70, 0.3);
    border: 2px solid var(--dont-pass-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: var(--z-dont-pass);
    touch-action: manipulation;
    font-size: 0.9rem;
    font-weight: 600;
}

.dont-pass:hover, .dont-pass:active {
    background: rgba(230, 57, 70, 0.5);
}

.dont-pass.selected {
    background: rgba(230, 57, 70, 0.7);
    box-shadow: 0 0 10px rgba(230, 57, 70, 0.7);
}

/* Mobile Point Box */
.point-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: rgba(249, 199, 79, 0.2);
    border: 3px solid var(--point-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: var(--point-color);
    z-index: var(--z-point-box);
}

.point-box.active {
    background: rgba(249, 199, 79, 0.4);
    box-shadow: 0 0 20px rgba(249, 199, 79, 0.4);
    animation: pointPulse 2s infinite;
}

@keyframes pointPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

/* Mobile Prop Bets */
.prop-bets {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-top: 60px;
    z-index: var(--z-prop-bets);
}

.prop-bet {
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.7rem;
    text-align: center;
    position: relative;
    z-index: var(--z-prop-bets);
    touch-action: manipulation;
}

.prop-bet:hover, .prop-bet:active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.prop-bet.selected {
    background: rgba(255, 255, 255, 0.3);
    border-color: var(--craps-yellow);
    box-shadow: 0 0 10px rgba(249, 199, 79, 0.5);
}

.prop-bet-value {
    font-weight: bold;
    font-size: 1rem;
    margin-bottom: 0.1rem;
    z-index: var(--z-prop-bets);
    position: relative;
}

/* Mobile Bet Chips */
.bet-chip {
    position: absolute;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background: var(--craps-yellow);
    color: var(--craps-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.7rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: calc(var(--z-prop-bets) + 1);
    top: -8px;
    right: -8px;
    pointer-events: none;
}

/* Mobile Dice Area */
.dice-area {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 1rem 0;
    position: relative;
    z-index: var(--z-background);
}

.die {
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--craps-dark);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    position: relative;
    transition: all 0.3s ease;
}

.die.rolling {
    animation: diceRoll 0.5s linear infinite;
}

@keyframes diceRoll {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    25% { transform: rotateX(90deg) rotateY(45deg); }
    50% { transform: rotateX(180deg) rotateY(90deg); }
    75% { transform: rotateX(270deg) rotateY(135deg); }
    100% { transform: rotateX(360deg) rotateY(180deg); }
}

/* Die Dots */
.die-dots {
    position: absolute;
    inset: 0;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    padding: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    background-color: var(--craps-dark);
    border-radius: 50%;
    align-self: center;
    justify-self: center;
}

/* Mobile Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1rem;
    position: relative;
    z-index: var(--z-background);
    flex-wrap: wrap;
}

.roll-btn {
    background: linear-gradient(45deg, var(--craps-yellow), var(--craps-orange));
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    color: var(--craps-dark);
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    touch-action: manipulation;
    min-height: 50px;
}

.roll-btn:hover, .roll-btn:active {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.roll-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.new-game-btn {
    background: linear-gradient(45deg, var(--craps-light-blue), var(--craps-blue));
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    touch-action: manipulation;
    min-height: 50px;
}

.new-game-btn:hover, .new-game-btn:active {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

/* Mobile Game Info */
.game-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: var(--z-background);
}

.game-phase {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.game-message {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

/* Pro View Toggle */
.view-mode-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: var(--z-notifications);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 25px;
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.view-toggle-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    font-weight: 500;
    touch-action: manipulation;
}

.view-toggle-btn.active {
    background: var(--craps-yellow);
    color: var(--craps-dark);
}

/* Pro View Enhancements */
.pro-view-active .stats-panel {
    display: block !important;
}

.pro-view-active .fairness-panel {
    display: block !important;
}

.pro-view-active .dashboard-grid {
    display: grid !important;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.pro-view-active .advanced-stats {
    display: block !important;
}

.pro-view-active .detailed-history {
    display: block !important;
}

/* Standard view hides pro features on mobile */
.standard-view .stats-panel,
.standard-view .fairness-panel {
    display: none;
}

.advanced-stats,
.detailed-history {
    display: none;
}

/* Mobile Notifications */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    background: var(--craps-blue);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    z-index: var(--z-notifications);
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: translateY(-100px);
    transition: transform 0.3s ease;
    text-align: center;
}

.notification.show {
    transform: translateY(0);
}

/* History and Rules Accordion */
.rules-section, .fairness-info, .verification-tool {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.rules-toggle {
    background: none;
    border: none;
    color: var(--craps-yellow);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
    width: 100%;
    text-align: left;
    touch-action: manipulation;
}

.rules-content {
    margin-top: 1rem;
    line-height: 1.6;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

.seed-display {
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.5rem;
    border-radius: 6px;
    margin: 0.5rem 0;
    word-break: break-all;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verify-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.75rem;
    color: white;
    margin: 0.5rem 0;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.verify-btn {
    background: var(--craps-green);
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 1rem;
    touch-action: manipulation;
}

.verify-btn:hover, .verify-btn:active {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.history-container {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 1rem;
}

.history-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.8rem;
}

.odds-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem;
    font-size: 0.7rem;
}

.odds-table th, .odds-table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.odds-table th {
    color: var(--craps-yellow);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Tablet Styles */
@media (min-width: 768px) {
    .craps-container {
        padding: 1.5rem;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 1.5rem;
    }
    
    .craps-table {
        order: 0;
        padding: 1.5rem;
    }
    
    .stats-panel, .fairness-panel {
        padding: 1.25rem;
    }
    
    .table-layout {
        min-height: 300px;
        padding: 1.25rem;
    }
    
    .pass-line, .dont-pass {
        height: 55px;
        font-size: 1rem;
    }
    
    .point-box {
        width: 90px;
        height: 90px;
        font-size: 2.2rem;
    }
    
    .prop-bets {
        grid-template-columns: repeat(6, 1fr);
        width: 85%;
        margin-top: 70px;
    }
    
    .prop-bet {
        height: 65px;
        font-size: 0.75rem;
    }
    
    .die {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }
    
    .bet-chip {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
    
    .notification {
        left: auto;
        right: 20px;
        max-width: 350px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .craps-container {
        max-width: 1400px;
        padding: 2rem;
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .game-subtitle {
        font-size: 1.2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 2rem;
    }
    
    .craps-table {
        padding: 2rem;
    }
    
    .stats-panel, .fairness-panel {
        padding: 1.5rem;
    }
    
    .table-layout {
        min-height: 350px;
        padding: 1.5rem;
    }
    
    .pass-line, .dont-pass {
        height: 60px;
        font-size: 1.1rem;
    }
    
    .point-box {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
    
    .prop-bets {
        width: 80%;
        margin-top: 80px;
    }
    
    .prop-bet {
        width: 70px;
        height: 70px;
        font-size: 0.8rem;
    }
    
    .die {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    
    .bet-chip {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .dot {
        width: 10px;
        height: 10px;
    }
    
    .die-dots {
        padding: 10px;
    }
    
    /* Show panels on desktop */
    .standard-view .stats-panel,
    .standard-view .fairness-panel {
        display: block;
    }
}

/* Large Desktop */
@media (min-width: 1440px) {
    .prop-bets {
        grid-template-columns: repeat(6, 1fr);
        gap: 0.75rem;
    }
    
    .prop-bet {
        width: 80px;
        height: 80px;
        font-size: 0.9rem;
    }
    
    .prop-bet-value {
        font-size: 1.1rem;
    }
}

/* Touch and Interaction Enhancements */
@media (hover: none) and (pointer: coarse) {
    /* Mobile touch enhancements */
    .bet-type,
    .prop-bet,
    .pass-line,
    .dont-pass,
    .roll-btn,
    .new-game-btn,
    .bet-adjust-btn {
        min-height: 44px; /* Minimum touch target size */
        min-width: 44px;
    }
    
    .prop-bet {
        min-height: 50px;
    }
    
    /* Increase touch feedback */
    .bet-type:active,
    .prop-bet:active,
    .pass-line:active,
    .dont-pass:active {
        transform: scale(0.95);
        background-color: rgba(255, 255, 255, 0.3) !important;
    }
    
    .roll-btn:active,
    .new-game-btn:active {
        transform: scale(0.95);
    }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .die {
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    }
    
    .bet-chip {
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .die.rolling {
        animation: none;
    }
    
    .point-box.active {
        animation: none;
    }
    
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Dark mode support (if system prefers dark) */
@media (prefers-color-scheme: dark) {
    /* Already dark themed, but can add adjustments */
}

/* Print styles */
@media print {
    .craps-container {
        background: white;
        color: black;
    }
    
    .notification,
    .view-mode-toggle {
        display: none;
    }
}