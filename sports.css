/* Enhanced Sports Betting - Mobile-First Responsive Design */
:root {
    /* Sports-specific color palette */
    --sports-primary: #1565c0;
    --sports-primary-hover: #0d47a1;
    --sports-secondary: #1976d2;
    --sports-accent: #2196f3;
    --sports-success: #4caf50;
    --sports-warning: #ff9800;
    --sports-danger: #f44336;
    
    /* Live event colors */
    --live-color: #f44336;
    --live-glow: rgba(244, 67, 54, 0.4);
    
    /* Odds styling */
    --odds-bg: rgba(33, 150, 243, 0.1);
    --odds-border: rgba(33, 150, 243, 0.3);
    --odds-hover: rgba(33, 150, 243, 0.2);
    
    /* Mobile-specific variables */
    --mobile-sports-padding: 12px;
    --mobile-sports-gap: 8px;
    --mobile-event-height: 80px;
    --mobile-odds-width: 60px;
    
    /* Animation durations */
    --transition-fast: 0.2s;
    --transition-medium: 0.3s;
    --transition-slow: 0.5s;
}

/* Mobile-first base styles */
.sports-betting-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--mobile-sports-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
}

.sports-betting-container.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.sports-betting-container.loading::after {
    content: '';
    position: fixed;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid var(--sports-accent);
    border-top: 3px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.loading-message {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: var(--text-color-muted);
    font-size: 14px;
}

/* Enhanced Sports Header - Mobile First */
.sports-header {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-sports-gap);
    margin-bottom: 20px;
    padding: var(--mobile-sports-padding);
    background: var(--glass-bg);
    border-radius: var(--mobile-border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.sports-header h1 {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--sports-primary), var(--sports-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 4px;
}

.sports-header .subtitle {
    font-size: 12px;
    color: var(--text-color-muted);
    margin-bottom: 8px;
}

.balance-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.balance-label {
    font-size: 11px;
    color: var(--text-color-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-amount {
    font-size: 16px;
    font-weight: 700;
    color: var(--accent-color);
}

/* Enhanced Promotions Banner */
.promotions-banner {
    margin-bottom: 16px;
    padding: 12px;
    background: linear-gradient(135deg, var(--sports-primary), var(--sports-secondary));
    border-radius: var(--mobile-border-radius);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.promotions-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    to { left: 100%; }
}

.promotion-text {
    font-size: 12px;
    font-weight: 600;
    color: white;
    margin-bottom: 4px;
    position: relative;
    z-index: 2;
}

.promotion-amount {
    font-size: 16px;
    font-weight: 700;
    color: var(--accent-color);
    position: relative;
    z-index: 2;
}

.promotion-status {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 2;
}

/* Enhanced Search and Filters */
.search-filters-container {
    margin-bottom: 16px;
    background: var(--glass-bg);
    border-radius: var(--mobile-border-radius);
    padding: var(--mobile-sports-padding);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
}

.search-section {
    margin-bottom: 12px;
}

.search-section input {
    width: 100%;
    height: var(--touch-target);
    padding: 0 var(--mobile-sports-padding);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 14px;
    transition: var(--transition);
}

.search-section input:focus {
    outline: none;
    border-color: var(--sports-accent);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Enhanced Betting Tabs */
.betting-tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 12px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 4px;
}

.betting-tab {
    flex: 1;
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: var(--text-color-muted);
    border-radius: calc(var(--border-radius) - 2px);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.betting-tab.active {
    background: var(--sports-accent);
    color: white;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.betting-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

/* Enhanced Sports Navigation */
.sports-nav {
    margin-bottom: 16px;
}

.sports-nav h2 {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sports-nav h2 i {
    color: var(--sports-accent);
}

.sports-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.sport-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.sport-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--sports-accent);
    transition: width 0.3s ease;
}

.sport-item:hover::before {
    width: 3px;
}

.sport-item:hover {
    background: rgba(33, 150, 243, 0.1);
    border-color: var(--sports-accent);
    transform: translateX(2px);
}

.sport-item.active {
    background: rgba(33, 150, 243, 0.15);
    border-color: var(--sports-accent);
    color: var(--sports-accent);
}

.sport-item.active::before {
    width: 3px;
}

.sport-emoji {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.sport-name {
    font-size: 14px;
    font-weight: 600;
    flex: 1;
}

.sport-count {
    font-size: 11px;
    color: var(--text-color-muted);
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
}

/* Enhanced Live Events Section */
.live-events {
    margin-bottom: 20px;
}

.live-events-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 0 4px;
}

.live-events h2 {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.live-indicator {
    width: 8px;
    height: 8px;
    background: var(--live-color);
    border-radius: 50%;
    animation: live-pulse 2s infinite;
    box-shadow: 0 0 8px var(--live-glow);
}

@keyframes live-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

.live-count {
    font-size: 12px;
    color: var(--text-color-muted);
    background: rgba(244, 67, 54, 0.1);
    padding: 4px 8px;
    border-radius: var(--border-radius);
    border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Enhanced Live Sports Scroll */
.live-sports-scroll {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 8px 0;
    margin-bottom: 12px;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.live-sports-scroll::-webkit-scrollbar {
    display: none;
}

.live-sport-item {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(244, 67, 54, 0.3);
    cursor: pointer;
    transition: var(--transition);
    min-width: 60px;
}

.live-sport-item:hover {
    background: rgba(244, 67, 54, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2);
}

.live-sport-emoji {
    font-size: 20px;
}

.live-sport-name {
    font-size: 9px;
    font-weight: 600;
    color: var(--text-color);
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Enhanced Event Controls */
.event-controls {
    display: flex;
    gap: 6px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.event-dropdown {
    flex: 1;
    min-width: 80px;
    height: 36px;
    padding: 0 8px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a0a0a0' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 6px center;
    background-repeat: no-repeat;
    background-size: 12px;
    padding-right: 24px;
}

.event-dropdown:focus {
    outline: none;
    border-color: var(--sports-accent);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Enhanced Currency Info */
.currency-info {
    text-align: center;
    font-size: 10px;
    color: var(--text-color-muted);
    padding: 8px;
    background: rgba(255, 215, 0, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 215, 0, 0.2);
    margin-bottom: 16px;
}

/* Enhanced Matches Container */
.matches-container {
    background: var(--glass-bg);
    border-radius: var(--mobile-border-radius);
    padding: var(--mobile-sports-padding);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    min-height: 300px;
}

.matches-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.match-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 12px;
    border: 1px solid var(--glass-border);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.match-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--sports-accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.match-item.live {
    border-left: 4px solid var(--live-color);
    background: rgba(244, 67, 54, 0.05);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.match-time {
    font-size: 11px;
    color: var(--text-color-muted);
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.match-time.live {
    background: var(--live-color);
    color: white;
    animation: live-pulse 2s infinite;
}

.match-league {
    font-size: 10px;
    color: var(--text-color-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.team-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.team-name {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-color);
}

.team-record {
    font-size: 10px;
    color: var(--text-color-muted);
}

.match-vs {
    padding: 0 12px;
    font-size: 12px;
    color: var(--text-color-muted);
    font-weight: 600;
}

.match-odds {
    display: flex;
    gap: 6px;
    justify-content: center;
}

.odds-button {
    flex: 1;
    max-width: var(--mobile-odds-width);
    height: 32px;
    background: var(--odds-bg);
    border: 1px solid var(--odds-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 11px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.odds-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.odds-button:hover::before {
    left: 100%;
}

.odds-button:hover {
    background: var(--odds-hover);
    border-color: var(--sports-accent);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
}

.odds-button:active {
    transform: translateY(0);
}

.odds-button.selected {
    background: var(--sports-accent);
    border-color: var(--sports-accent);
    color: white;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.4);
}

.odds-value {
    font-size: 12px;
    font-weight: 700;
}

.odds-label {
    font-size: 8px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    opacity: 0.8;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-color-muted);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-color);
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}

/* Bet Slip (Mobile First) */
.bet-slip {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--glass-border);
    padding: var(--mobile-sports-padding);
    transform: translateY(100%);
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    z-index: 1000;
    max-height: 50vh;
    overflow-y: auto;
}

.bet-slip.active {
    transform: translateY(0);
}

.bet-slip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--glass-border);
}

.bet-slip-title {
    font-size: 14px;
    font-weight: 700;
    color: var(--text-color);
}

.bet-slip-close {
    background: none;
    border: none;
    color: var(--text-color-muted);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.bet-slip-close:hover {
    color: var(--text-color);
    background: rgba(255, 255, 255, 0.1);
}

.bet-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 8px;
    border: 1px solid var(--glass-border);
}

.bet-match {
    font-size: 12px;
    color: var(--text-color);
    margin-bottom: 4px;
}

.bet-selection {
    font-size: 14px;
    font-weight: 600;
    color: var(--sports-accent);
    margin-bottom: 8px;
}

.bet-stake-input {
    width: 100%;
    height: 36px;
    padding: 0 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 14px;
}

.bet-potential {
    text-align: center;
    margin-top: 8px;
    font-size: 12px;
    color: var(--success-green);
}

.place-bet-btn {
    width: 100%;
    height: var(--touch-target);
    background: var(--sports-success);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.place-bet-btn:hover {
    background: var(--success-green-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.place-bet-btn:disabled {
    background: var(--text-color-muted);
    cursor: not-allowed;
    transform: none;
}

/* Responsive Breakpoints */

/* Tablet Styles */
@media (min-width: 768px) {
    .sports-betting-container {
        padding: 20px;
    }
    
    .sports-header {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }
    
    .sports-header h1 {
        font-size: 24px;
        margin-bottom: 0;
    }
    
    .betting-tabs {
        margin-bottom: 16px;
    }
    
    .betting-tab {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .sports-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .live-sports-scroll {
        justify-content: center;
        flex-wrap: wrap;
        overflow: visible;
    }
    
    .event-controls {
        justify-content: center;
        max-width: 400px;
        margin: 0 auto 16px;
    }
    
    .match-odds {
        gap: 8px;
    }
    
    .odds-button {
        max-width: 80px;
        height: 36px;
    }
    
    .bet-slip {
        position: fixed;
        right: 20px;
        bottom: 20px;
        left: auto;
        width: 300px;
        max-height: 400px;
        border-radius: var(--mobile-border-radius);
        border: 1px solid var(--glass-border);
        transform: translateX(100%);
    }
    
    .bet-slip.active {
        transform: translateX(0);
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .sports-betting-container {
        padding: 2rem;
        display: grid;
        grid-template-columns: 300px 1fr 320px;
        gap: 2rem;
        align-items: start;
    }
    
    .sports-sidebar {
        order: 1;
    }
    
    .sports-main {
        order: 2;
    }
    
    .sports-aside {
        order: 3;
    }
    
    .sports-header h1 {
        font-size: 28px;
    }
    
    .sports-list {
        grid-template-columns: 1fr;
    }
    
    .live-sports-scroll {
        grid-template-columns: repeat(4, 1fr);
        display: grid;
        gap: 12px;
    }
    
    .matches-container {
        min-height: 500px;
    }
    
    .match-item {
        padding: 16px;
    }
    
    .match-odds {
        gap: 12px;
    }
    
    .odds-button {
        max-width: 100px;
        height: 40px;
    }
    
    .odds-value {
        font-size: 14px;
    }
    
    .bet-slip {
        position: sticky;
        top: 100px;
        right: 0;
        bottom: auto;
        left: auto;
        width: 100%;
        max-height: 600px;
        transform: none;
        margin-top: 0;
    }
    
    .bet-slip.active {
        transform: none;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .sports-betting-container {
        grid-template-columns: 350px 1fr 350px;
        gap: 2.5rem;
    }
    
    .sports-header h1 {
        font-size: 32px;
    }
    
    .live-sports-scroll {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .sport-emoji,
    .live-sport-emoji {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .live-indicator,
    .match-time.live {
        animation: none !important;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .odds-button {
        background: rgba(33, 150, 243, 0.05);
    }
    
    .match-item {
        background: rgba(255, 255, 255, 0.03);
    }
}

/* Accessibility improvements */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.sport-item:focus,
.odds-button:focus,
.betting-tab:focus {
    outline: 2px solid var(--sports-accent);
    outline-offset: 2px;
}

/* Loading skeletons */
.skeleton {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.1) 25%, 
        rgba(255, 255, 255, 0.2) 50%, 
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.match-skeleton {
    height: 120px;
    border-radius: var(--border-radius);
    margin-bottom: 8px;
}

/* Custom scrollbar for bet slip */
.bet-slip::-webkit-scrollbar {
    width: 4px;
}

.bet-slip::-webkit-scrollbar-track {
    background: transparent;
}

.bet-slip::-webkit-scrollbar-thumb {
    background: var(--sports-accent);
    border-radius: 2px;
}

/* Toast notifications */
.toast {
    position: fixed;
    top: calc(var(--mobile-header-height) + 20px);
    right: 20px;
    background: var(--sports-success);
    color: white;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.error {
    background: var(--sports-danger);
}

.toast.warning {
    background: var(--sports-warning);
}

/* Print styles */
@media print {
    .sidebar,
    .header,
    .bet-slip,
    .promotions-banner {
        display: none !important;
    }
    
    .sports-betting-container {
        margin-left: 0 !important;
        padding: 0 !important;
    }
}