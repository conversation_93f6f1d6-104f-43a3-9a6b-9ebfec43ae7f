<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Poker Palace - Texas Hold'em</title>
    <link rel="stylesheet" href="poker-palace.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span class="back-text">Back to Games</span>
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">Poker Palace</h1>
                <p class="game-subtitle">Texas Hold'em • Strategic Play • Learn & Master</p>
            </div>
            <div class="header-right">
                <div class="view-mode-toggle">
                    <button id="standardViewBtn" class="view-btn active">
                        <i class="fas fa-mobile-alt"></i>
                        <span class="view-text">Standard</span>
                    </button>
                    <button id="proViewBtn" class="view-btn">
                        <i class="fas fa-chart-line"></i>
                        <span class="view-text">Pro</span>
                    </button>
                </div>
                <button id="tutorialBtn" class="info-btn">
                    <i class="fas fa-circle-info"></i>
                    <span class="btn-text">Tutorial</span>
                </button>
                <button id="rulesBtn" class="verify-btn">
                    <i class="fas fa-book"></i>
                    <span class="btn-text">Rules</span>
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- Mobile Status Bar -->
            <div class="mobile-status-bar">
                <div class="status-item">
                    <i class="fas fa-coins"></i>
                    <span id="mobileBalance">1000</span> GA
                </div>
                <div class="status-item">
                    <i class="fas fa-users"></i>
                    <span id="mobilePlayersCount">6</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-trophy"></i>
                    <span id="mobilePotSize">0</span> GA
                </div>
                <div class="status-item">
                    <i class="fas fa-percentage"></i>
                    <span id="mobileWinChance">0%</span>
                </div>
            </div>

            <!-- Game Area -->
            <div class="game-section">
                <!-- Poker Table -->
                <div class="poker-table">
                    <!-- Community Cards Area -->
                    <div class="community-cards">
                        <div class="community-cards-label">Community Cards</div>
                        <div class="cards-container">
                            <div class="card-slot community-card" id="flop1">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="flop2">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="flop3">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="turn">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="river">
                                <div class="card-back"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pot Display -->
                    <div class="pot-display">
                        <div class="pot-label">Pot</div>
                        <div class="pot-amount" id="potAmount">0 GA</div>
                        <div class="pot-chips">
                            <div class="chip chip-red"></div>
                            <div class="chip chip-blue"></div>
                            <div class="chip chip-green"></div>
                        </div>
                    </div>

                    <!-- Players Positions -->
                    <div class="players-grid">
                        <!-- Player 1 (Bottom - Human Player) -->
                        <div class="player-seat player-human" id="player0">
                            <div class="player-info">
                                <div class="player-name">You</div>
                                <div class="player-chips">1000 GA</div>
                                <div class="player-status">Waiting</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card" id="playerCard1">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card" id="playerCard2">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                            <div class="player-actions" id="playerActions">
                                <button class="action-btn fold-btn" id="foldBtn">
                                    <i class="fas fa-times"></i>
                                    Fold
                                </button>
                                <button class="action-btn call-btn" id="callBtn">
                                    <i class="fas fa-handshake"></i>
                                    Call <span id="callAmount">0</span>
                                </button>
                                <button class="action-btn raise-btn" id="raiseBtn">
                                    <i class="fas fa-arrow-up"></i>
                                    Raise
                                </button>
                                <button class="action-btn check-btn" id="checkBtn">
                                    <i class="fas fa-check"></i>
                                    Check
                                </button>
                            </div>
                        </div>

                        <!-- AI Players (Positions 2-6) -->
                        <div class="player-seat player-ai" id="player1">
                            <div class="player-info">
                                <div class="player-name">Alex</div>
                                <div class="player-chips">950 GA</div>
                                <div class="player-status">Thinking...</div>
                                <div class="ai-difficulty">Novice</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player2">
                            <div class="player-info">
                                <div class="player-name">Sarah</div>
                                <div class="player-chips">1200 GA</div>
                                <div class="player-status">Folded</div>
                                <div class="ai-difficulty">Pro</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player3">
                            <div class="player-info">
                                <div class="player-name">Mike</div>
                                <div class="player-chips">800 GA</div>
                                <div class="player-status">All-in</div>
                                <div class="ai-difficulty">Shark</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player4">
                            <div class="player-info">
                                <div class="player-name">Emma</div>
                                <div class="player-chips">1100 GA</div>
                                <div class="player-status">Active</div>
                                <div class="ai-difficulty">Pro</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player5">
                            <div class="player-info">
                                <div class="player-name">Jake</div>
                                <div class="player-chips">900 GA</div>
                                <div class="player-status">Waiting</div>
                                <div class="ai-difficulty">Novice</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dealer Button -->
                    <div class="dealer-button" id="dealerButton">
                        <i class="fas fa-crown"></i>
                        <span>D</span>
                    </div>
                </div>

                <!-- Game Controls -->
                <div class="game-controls">
                    <!-- Betting Controls -->
                    <div class="betting-controls">
                        <div class="bet-amount-container">
                            <label for="betAmount">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <input type="range" id="betSlider" min="10" max="1000" value="50" class="bet-slider">
                                <input type="number" id="betAmount" min="10" max="1000" value="50" class="bet-input">
                            </div>
                            <div class="quick-bet-buttons">
                                <button class="quick-bet" data-amount="10">10</button>
                                <button class="quick-bet" data-amount="25">25</button>
                                <button class="quick-bet" data-amount="50">50</button>
                                <button class="quick-bet" data-amount="100">100</button>
                                <button class="quick-bet" data-amount="250">250</button>
                                <button class="quick-bet" data-amount="500">All-in</button>
                            </div>
                        </div>
                    </div>

                    <!-- Game Info Panel -->
                    <div class="game-info-panel">
                        <div class="info-section">
                            <h3>Current Hand</h3>
                            <div class="hand-strength" id="handStrength">
                                <div class="strength-label">Hand: High Card</div>
                                <div class="strength-bars">
                                    <div class="strength-bar"></div>
                                    <div class="strength-bar"></div>
                                    <div class="strength-bar active"></div>
                                    <div class="strength-bar"></div>
                                    <div class="strength-bar"></div>
                                </div>
                            </div>
                            <div class="win-probability" id="winProbability">
                                <span class="prob-label">Win Chance:</span>
                                <span class="prob-value">32%</span>
                            </div>
                        </div>

                        <div class="info-section blind-info">
                            <h3>Blinds</h3>
                            <div class="blind-amounts">
                                <div class="blind-item">
                                    <span class="blind-label">Small:</span>
                                    <span class="blind-value">5 GA</span>
                                </div>
                                <div class="blind-item">
                                    <span class="blind-label">Big:</span>
                                    <span class="blind-value">10 GA</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-section game-round">
                            <h3>Game Status</h3>
                            <div class="round-info">
                                <div class="round-phase" id="roundPhase">Pre-Flop</div>
                                <div class="players-active" id="playersActive">6 players active</div>
                                <div class="next-action" id="nextAction">Your turn</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pro Analytics (Hidden by default) -->
                <!-- Pro Analytics (Enhanced for Mobile) -->
                <div class="pro-analytics" id="proAnalytics">
                    <div class="analytics-section">
                        <h3><i class="fas fa-chart-bar"></i> Advanced Statistics</h3>
                        <div class="analytics-grid">
                            <div class="analytics-item">
                                <div class="analytics-label">VPIP</div>
                                <div class="analytics-value" id="vpipStat">24%</div>
                                <div class="analytics-help">Voluntary Put in Pot</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">PFR</div>
                                <div class="analytics-value" id="pfrStat">18%</div>
                                <div class="analytics-help">Pre-Flop Raise</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">AGG</div>
                                <div class="analytics-value" id="aggressionStat">2.1</div>
                                <div class="analytics-help">Aggression Factor</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">H/Hr</div>
                                <div class="analytics-value" id="handsPerHour">45</div>
                                <div class="analytics-help">Hands per Hour</div>
                            </div>
                        </div>
                    </div>

                    <div class="opponent-analysis">
                        <h3><i class="fas fa-users"></i> Opponent Analysis</h3>
                        <div class="opponent-stats">
                            <div class="opponent-item">
                                <div class="opponent-header">
                                    <div class="opponent-name">Alex (Novice)</div>
                                    <div class="opponent-tendency tight-passive">Tight-Passive</div>
                                </div>
                                <div class="opponent-notes">Folds often, rarely bluffs, predictable</div>
                                <div class="opponent-stats-mini">
                                    <span>VPIP: 15%</span> • <span>PFR: 8%</span>
                                </div>
                            </div>
                            <div class="opponent-item">
                                <div class="opponent-header">
                                    <div class="opponent-name">Sarah (Pro)</div>
                                    <div class="opponent-tendency loose-aggressive">Loose-Aggressive</div>
                                </div>
                                <div class="opponent-notes">Frequent bets, skilled bluffer, dangerous</div>
                                <div class="opponent-stats-mini">
                                    <span>VPIP: 35%</span> • <span>PFR: 28%</span>
                                </div>
                            </div>
                            <div class="opponent-item">
                                <div class="opponent-header">
                                    <div class="opponent-name">Mike (Shark)</div>
                                    <div class="opponent-tendency tight-aggressive">Tight-Aggressive</div>
                                </div>
                                <div class="opponent-notes">Expert player, calculates odds, avoid bluffs</div>
                                <div class="opponent-stats-mini">
                                    <span>VPIP: 20%</span> • <span>PFR: 18%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="hand-history">
                        <h3><i class="fas fa-history"></i> Recent Hands</h3>
                        <div class="history-list" id="handHistoryList">
                            <div class="history-item won">
                                <div class="history-details">
                                    <div class="history-hand">A♠ K♦</div>
                                    <div class="history-result">+150 GA</div>
                                </div>
                                <div class="history-action">Pair of Aces</div>
                                <div class="history-time">2 min ago</div>
                            </div>
                            <div class="history-item folded">
                                <div class="history-details">
                                    <div class="history-hand">7♣ 2♥</div>
                                    <div class="history-result">Folded</div>
                                </div>
                                <div class="history-action">Pre-flop</div>
                                <div class="history-time">4 min ago</div>
                            </div>
                            <div class="history-item lost">
                                <div class="history-details">
                                    <div class="history-hand">Q♦ J♠</div>
                                    <div class="history-result">-75 GA</div>
                                </div>
                                <div class="history-action">Lost to Full House</div>
                                <div class="history-time">6 min ago</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Game Actions Panel -->
                <div class="actions-panel">
                    <div class="game-actions">
                        <button class="action-btn deal-btn" id="dealBtn">
                            <i class="fas fa-play"></i>
                            Deal New Hand
                        </button>
                        <button class="action-btn auto-fold-btn" id="autoFoldBtn">
                            <i class="fas fa-fast-forward"></i>
                            Auto-Fold
                        </button>
                        <button class="action-btn sit-out-btn" id="sitOutBtn">
                            <i class="fas fa-pause"></i>
                            Sit Out
                        </button>
                        <button class="action-btn leave-btn" id="leaveBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Leave Table
                        </button>
                    </div>

                    <div class="difficulty-selector">
                        <label for="difficultySelect">AI Difficulty:</label>
                        <select id="difficultySelect" class="difficulty-select">
                            <option value="novice">Novice (Easy)</option>
                            <option value="pro" selected>Pro (Medium)</option>
                            <option value="shark">Shark (Hard)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Information Panel -->
            <div class="info-panel">
                <!-- Hand Rankings -->
                <div class="info-section hand-rankings">
                    <h3>Hand Rankings</h3>
                    <div class="ranking-list">
                        <div class="ranking-item">
                            <div class="ranking-number">1</div>
                            <div class="ranking-name">Royal Flush</div>
                            <div class="ranking-example">A♠ K♠ Q♠ J♠ 10♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">2</div>
                            <div class="ranking-name">Straight Flush</div>
                            <div class="ranking-example">9♥ 8♥ 7♥ 6♥ 5♥</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">3</div>
                            <div class="ranking-name">Four of a Kind</div>
                            <div class="ranking-example">K♠ K♥ K♦ K♣ 5♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">4</div>
                            <div class="ranking-name">Full House</div>
                            <div class="ranking-example">A♠ A♥ A♦ 8♣ 8♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">5</div>
                            <div class="ranking-name">Flush</div>
                            <div class="ranking-example">K♠ 10♠ 7♠ 6♠ 2♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">6</div>
                            <div class="ranking-name">Straight</div>
                            <div class="ranking-example">A♠ K♥ Q♦ J♣ 10♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">7</div>
                            <div class="ranking-name">Three of a Kind</div>
                            <div class="ranking-example">7♠ 7♥ 7♦ K♣ 2♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">8</div>
                            <div class="ranking-name">Two Pair</div>
                            <div class="ranking-example">A♠ A♥ 8♦ 8♣ K♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">9</div>
                            <div class="ranking-name">One Pair</div>
                            <div class="ranking-example">A♠ A♥ K♦ Q♣ J♠</div>
                        </div>
                        <div class="ranking-item">
                            <div class="ranking-number">10</div>
                            <div class="ranking-name">High Card</div>
                            <div class="ranking-example">A♠ K♥ Q♦ J♣ 9♠</div>
                        </div>
                    </div>
                </div>

                <!-- Position Guide -->
                <div class="info-section position-guide">
                    <h3>Position Strategy</h3>
                    <div class="position-tips">
                        <div class="position-tip">
                            <div class="position-name">Early Position</div>
                            <div class="position-advice">Play tight, strong hands only</div>
                        </div>
                        <div class="position-tip">
                            <div class="position-name">Middle Position</div>
                            <div class="position-advice">Moderate range, observe opponents</div>
                        </div>
                        <div class="position-tip">
                            <div class="position-name">Late Position</div>
                            <div class="position-advice">Play wider range, use position advantage</div>
                        </div>
                        <div class="position-tip">
                            <div class="position-name">Button</div>
                            <div class="position-advice">Most profitable position, play aggressively</div>
                        </div>
                    </div>
                </div>

                <!-- Session Stats -->
                <div class="info-section session-stats">
                    <h3>Session Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">Hands Played</div>
                            <div class="stat-value" id="handsPlayed">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Hands Won</div>
                            <div class="stat-value" id="handsWon">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Win Rate</div>
                            <div class="stat-value" id="winRate">0%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Net Profit</div>
                            <div class="stat-value" id="netProfit">0 GA</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Biggest Pot</div>
                            <div class="stat-value" id="biggestPot">0 GA</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Session Time</div>
                            <div class="stat-value" id="sessionTime">0:00</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tutorial Modal -->
        <div class="modal-overlay hidden" id="tutorialModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Poker Palace Tutorial</h2>
                    <button class="close-modal-btn" id="closeTutorialBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="tutorial-section">
                        <h3>Game Objective</h3>
                        <p>Win chips by having the best five-card poker hand using your two hole cards and five community cards, or by making all other players fold.</p>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Game Flow</h3>
                        <ol>
                            <li><strong>Pre-flop:</strong> Each player receives 2 hole cards</li>
                            <li><strong>Flop:</strong> 3 community cards are revealed</li>
                            <li><strong>Turn:</strong> 4th community card is revealed</li>
                            <li><strong>River:</strong> 5th community card is revealed</li>
                            <li><strong>Showdown:</strong> Remaining players reveal hands</li>
                        </ol>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Betting Actions</h3>
                        <ul>
                            <li><strong>Fold:</strong> Give up your hand and forfeit the pot</li>
                            <li><strong>Check:</strong> Pass the action without betting (if no bet to call)</li>
                            <li><strong>Call:</strong> Match the current bet</li>
                            <li><strong>Raise:</strong> Increase the current bet</li>
                            <li><strong>All-in:</strong> Bet all your remaining chips</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>AI Difficulty Levels</h3>
                        <ul>
                            <li><strong>Novice:</strong> Basic strategy, predictable play</li>
                            <li><strong>Pro:</strong> Advanced tactics, moderate bluffing</li>
                            <li><strong>Shark:</strong> Expert play, complex strategies</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Pro View Features</h3>
                        <ul>
                            <li><strong>VPIP:</strong> Voluntarily Put $ In Pot percentage</li>
                            <li><strong>PFR:</strong> Pre-Flop Raise percentage</li>
                            <li><strong>Aggression:</strong> Betting/calling ratio</li>
                            <li><strong>Opponent Analysis:</strong> Playing style indicators</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div class="modal-overlay hidden" id="rulesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Texas Hold'em Rules</h2>
                    <button class="close-modal-btn" id="closeRulesBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="rules-section">
                        <h3>Basic Rules</h3>
                        <p>Texas Hold'em is played with a standard 52-card deck. Each player receives two private cards (hole cards) and shares five community cards to make the best possible five-card poker hand.</p>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Betting Structure</h3>
                        <ul>
                            <li>Small Blind: Forced bet by player to dealer's left</li>
                            <li>Big Blind: Forced bet by player to small blind's left (usually 2x small blind)</li>
                            <li>Minimum raise: Must be at least equal to the previous bet or raise</li>
                            <li>Maximum bet: All-in (all remaining chips)</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Showdown Rules</h3>
                        <ul>
                            <li>Players must use exactly 5 cards to make their best hand</li>
                            <li>Can use 0, 1, or 2 hole cards with community cards</li>
                            <li>Best hand wins the entire pot</li>
                            <li>In case of tie, pot is split equally</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Side Pots</h3>
                        <p>When a player goes all-in with less than the current bet, a side pot is created for the remaining players. Players can only win an amount equal to what they contributed.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="poker-palace.js"></script>
</body>
</html>