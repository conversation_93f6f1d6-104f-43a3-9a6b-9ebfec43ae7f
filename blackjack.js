// BlackJack - Strategic card game with provably fair outcomes
class BlackjackGame {
    constructor() {
        // Game state
        this.gameState = 'ready'; // ready, playing, result
        this.balance = 10000; // GA currency
        this.currentBet = 0;
        this.lastWin = 0;
        this.handsPlayed = 0;
        this.handsWon = 0;
        this.nonce = 0;
        
        // Card definitions
        this.suits = ['♠', '♥', '♦', '♣'];
        this.values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.cardValues = {
            'A': 11, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, 
            '7': 7, '8': 8, '9': 9, '10': 10, 'J': 10, 'Q': 10, 'K': 10
        };
        
        // Game elements
        this.deck = [];
        this.playerHand = [];
        this.dealerHand = [];
        this.totalDecks = 6;
        this.deckPenetration = 0;
        
        // Provably fair
        this.serverSeed = '';
        this.clientSeed = this.generateRandomString(16);
        this.serverSeedHash = '';
        
        // Game history
        this.gameHistory = [];
        this.achievements = new Set();
        this.difficulty = 'novice';
        this.sessionStartTime = Date.now();
        
        // Initialize DOM elements
        this.initElements();
        this.attachEventListeners();
        
        // Create initial deck
        this.deck = this.createDeck();
        
        // Generate initial server seed
        this.generateNewServerSeed();
        
        // Update displays
        this.updateDisplay();
        this.updateButtonStates();
        
        // Start session timer
        this.startSessionTimer();
    }
    
    initElements() {
        // Game areas
        this.dealerCardsContainer = document.getElementById('dealerCards');
        this.playerCardsContainer = document.getElementById('playerCards');
        this.dealerTotalElement = document.getElementById('dealerTotal');
        this.playerTotalElement = document.getElementById('playerTotal');
        
        // Controls
        this.dealBtn = document.getElementById('dealBtn');
        this.hitBtn = document.getElementById('hitBtn');
        this.standBtn = document.getElementById('standBtn');
        this.doubleBtn = document.getElementById('doubleBtn');
        this.splitBtn = document.getElementById('splitBtn');
        this.surrenderBtn = document.getElementById('surrenderBtn');
        this.betInput = document.getElementById('betAmount');
        
        // Stats displays
        this.bankrollDisplay = document.getElementById('bankroll');
        this.currentBetDisplay = document.getElementById('currentBet');
        this.handsWonDisplay = document.getElementById('handsWon');
        this.winRateDisplay = document.getElementById('winRate');
        this.cardsRemainingDisplay = document.getElementById('cardsRemaining');
        this.penetrationFillDisplay = document.getElementById('penetrationFill');
        this.penetrationPercentDisplay = document.getElementById('penetrationPercent');
        
        // Results
        this.resultModal = document.getElementById('resultModal');
        this.resultTitle = document.getElementById('resultTitle');
        this.resultMessage = document.getElementById('resultMessage');
        this.resultDetails = document.getElementById('resultDetails');
        this.achievementsDisplay = document.getElementById('achievements');
        
        // Strategy elements
        this.strategyRecommendation = document.getElementById('strategyRecommendation');
        this.winProbDisplay = document.getElementById('winProb');
        this.bustProbDisplay = document.getElementById('bustProb');
        this.expectedValueDisplay = document.getElementById('expectedValue');
        this.riskIndicator = document.getElementById('riskIndicator');
        
        // Timer
        this.sessionTimer = document.getElementById('sessionTimer');
        this.sessionTimeDisplay = document.getElementById('sessionTime');
    }
    
    attachEventListeners() {
        // Main game controls (these are attached via HTML onclick)
        
        // Bet chip controls
        document.querySelectorAll('.bet-chip').forEach(chip => {
            chip.addEventListener('click', () => {
                const amount = parseInt(chip.dataset.amount);
                const maxBet = Math.min(amount, this.balance * 0.05);
                this.betInput.value = Math.max(100, maxBet);
            });
        });
        
        // Difficulty selector
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.difficulty = btn.dataset.level;
                
                // Update deck count based on difficulty
                const deckCounts = { novice: 1, pro: 6, expert: 8 };
                this.totalDecks = deckCounts[this.difficulty];
                this.deck = this.createDeck();
                this.updateDisplay();
                
                this.showNotification(`Difficulty set to ${this.difficulty.toUpperCase()}`);
            });
        });
        
        // Bet input validation
        this.betInput.addEventListener('input', () => {
            const value = parseInt(this.betInput.value);
            const max = this.balance * 0.05;
            if (value > max) {
                this.betInput.value = Math.floor(max);
                this.showNotification(`Maximum bet is 5% of bankroll: ${Math.floor(max)}`);
            }
        });
    }
    
    // Deck and card management
    createDeck() {
        const deck = [];
        for (let d = 0; d < this.totalDecks; d++) {
            for (let suit of this.suits) {
                for (let value of this.values) {
                    deck.push({
                        suit: suit,
                        value: value,
                        numericValue: this.cardValues[value],
                        isRed: suit === '♥' || suit === '♦'
                    });
                }
            }
        }
        return this.shuffleDeck(deck);
    }
    
    shuffleDeck(deck) {
        // Use provably fair shuffle using the server and client seeds
        const combinedSeed = this.serverSeed + this.clientSeed + this.nonce.toString();
        let hash = this.hashCode(combinedSeed);
        
        const shuffled = [...deck];
        for (let i = shuffled.length - 1; i > 0; i--) {
            hash = this.hashCode(hash.toString());
            const j = Math.abs(hash) % (i + 1);
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        
        return shuffled;
    }
    
    drawCard() {
        if (this.deck.length < 20 || this.deckPenetration > 75) {
            this.reshuffleShoe();
        }
        return this.deck.pop();
    }
    
    reshuffleShoe() {
        this.deck = this.createDeck();
        this.deckPenetration = 0;
        this.showNotification('🔄 Deck reshuffled at 75% penetration');
    }
    
    // Hand calculation
    calculateHandValue(hand) {
        let total = 0;
        let aces = 0;
        
        for (let card of hand) {
            if (card.value === 'A') {
                aces++;
                total += 11;
            } else {
                total += card.numericValue;
            }
        }
        
        // Adjust for aces
        while (total > 21 && aces > 0) {
            total -= 10;
            aces--;
        }
        
        return total;
    }
    
    isBlackjack(hand) {
        return hand.length === 2 && this.calculateHandValue(hand) === 21;
    }
    
    isSoft(hand) {
        let hasAce = false;
        let total = 0;
        
        for (let card of hand) {
            if (card.value === 'A') hasAce = true;
            total += card.numericValue;
        }
        
        return hasAce && total <= 21 && total - 10 < 21;
    }
    
    canSplit(hand) {
        return hand.length === 2 && hand[0].value === hand[1].value;
    }
    
    // Strategy recommendations
    getBasicStrategyAction(playerHand, dealerUpcard) {
        const playerTotal = this.calculateHandValue(playerHand);
        const dealerIndex = this.values.indexOf(dealerUpcard.value);
        
        // Basic Strategy Chart (simplified)
        const BASIC_STRATEGY = {
            hard: {
                // Player total: [2,3,4,5,6,7,8,9,10,A] dealer upcards
                5: ['H','H','H','H','H','H','H','H','H','H'],
                6: ['H','H','H','H','H','H','H','H','H','H'],
                7: ['H','H','H','H','H','H','H','H','H','H'],
                8: ['H','H','H','H','H','H','H','H','H','H'],
                9: ['H','D','D','D','D','H','H','H','H','H'],
                10: ['D','D','D','D','D','D','D','D','H','H'],
                11: ['D','D','D','D','D','D','D','D','D','H'],
                12: ['H','H','S','S','S','H','H','H','H','H'],
                13: ['S','S','S','S','S','H','H','H','H','H'],
                14: ['S','S','S','S','S','H','H','H','H','H'],
                15: ['S','S','S','S','S','H','H','H','R','H'],
                16: ['S','S','S','S','S','H','H','R','R','R'],
                17: ['S','S','S','S','S','S','S','S','S','S'],
                18: ['S','S','S','S','S','S','S','S','S','S'],
                19: ['S','S','S','S','S','S','S','S','S','S'],
                20: ['S','S','S','S','S','S','S','S','S','S'],
                21: ['S','S','S','S','S','S','S','S','S','S']
            },
            soft: {
                // Soft hands (Ace + other card)
                13: ['H','H','H','D','D','H','H','H','H','H'], // A,2
                14: ['H','H','H','D','D','H','H','H','H','H'], // A,3
                15: ['H','H','D','D','D','H','H','H','H','H'], // A,4
                16: ['H','H','D','D','D','H','H','H','H','H'], // A,5
                17: ['H','D','D','D','D','H','H','H','H','H'], // A,6
                18: ['S','D','D','D','D','S','S','H','H','H'], // A,7
                19: ['S','S','S','S','S','S','S','S','S','S'], // A,8
                20: ['S','S','S','S','S','S','S','S','S','S'], // A,9
                21: ['S','S','S','S','S','S','S','S','S','S']  // A,10
            },
            pairs: {
                // Pairs
                '2': ['P','P','P','P','P','P','H','H','H','H'], // 2,2
                '3': ['P','P','P','P','P','P','H','H','H','H'], // 3,3
                '4': ['H','H','H','P','P','H','H','H','H','H'], // 4,4
                '5': ['D','D','D','D','D','D','D','D','H','H'], // 5,5
                '6': ['P','P','P','P','P','H','H','H','H','H'], // 6,6
                '7': ['P','P','P','P','P','P','H','H','H','H'], // 7,7
                '8': ['P','P','P','P','P','P','P','P','P','P'], // 8,8
                '9': ['P','P','P','P','P','S','P','P','S','S'], // 9,9
                '10': ['S','S','S','S','S','S','S','S','S','S'], // 10,10
                'A': ['P','P','P','P','P','P','P','P','P','P']  // A,A
            }
        };
        
        if (this.canSplit(playerHand)) {
            const pairValue = playerHand[0].value;
            if (BASIC_STRATEGY.pairs[pairValue] && dealerIndex < 10) {
                return BASIC_STRATEGY.pairs[pairValue][dealerIndex];
            }
        }
        
        const isSoftHand = this.isSoft(playerHand);
        const strategyTable = isSoftHand ? BASIC_STRATEGY.soft : BASIC_STRATEGY.hard;
        
        if (strategyTable[playerTotal] && dealerIndex < 10) {
            return strategyTable[playerTotal][dealerIndex];
        }
        
        return playerTotal < 17 ? 'H' : 'S';
    }
    
    getActionExplanation(action, playerTotal, dealerUpcard) {
        const explanations = {
            'H': `Hit: With ${playerTotal} vs dealer ${dealerUpcard.value}, you need to improve your hand. Statistical advantage favors hitting.`,
            'S': `Stand: With ${playerTotal} vs dealer ${dealerUpcard.value}, standing gives you the best expected value. Dealer likely to bust.`,
            'D': `Double: With ${playerTotal} vs dealer ${dealerUpcard.value}, doubling maximizes your expected return on this favorable situation.`,
            'P': `Split: This pair should be split vs dealer ${dealerUpcard.value} for optimal long-term results.`,
            'R': `Surrender: With ${playerTotal} vs dealer ${dealerUpcard.value}, surrendering minimizes your losses in this unfavorable situation.`
        };
        return explanations[action] || 'Follow basic strategy for optimal play.';
    }
    
    // Probability calculations
    calculateProbabilities() {
        if (!this.gameState === 'playing' || this.playerHand.length === 0 || this.dealerHand.length === 0) {
            return {
                bustProb: 0,
                winProb: 0,
                expectedValue: 0
            };
        }
        
        const playerTotal = this.calculateHandValue(this.playerHand);
        const dealerUpcard = this.dealerHand[0];
        const remainingCards = this.deck.length;
        
        // Simplified probability calculations
        let bustProb = 0;
        let winProb = 0.48; // Base probability
        
        // Calculate bust probability for hitting
        if (playerTotal > 11) {
            const cardsOver21 = 13 - (21 - playerTotal);
            if (cardsOver21 > 0) {
                bustProb = Math.min(cardsOver21 / 13, 1) * 100;
            }
        }
        
        // Adjust win probability based on dealer upcard
        const dealerValue = dealerUpcard.numericValue;
        if (dealerValue >= 2 && dealerValue <= 6) {
            winProb += 0.1; // Dealer more likely to bust
        } else if (dealerValue >= 9) {
            winProb -= 0.1; // Dealer has strong upcard
        }
        
        // Adjust for player total
        if (playerTotal >= 17) winProb += 0.1;
        else if (playerTotal <= 11) winProb -= 0.05;
        
        winProb = Math.max(0, Math.min(1, winProb)) * 100;
        
        return {
            bustProb: Math.round(bustProb),
            winProb: Math.round(winProb),
            expectedValue: ((winProb / 100) * this.currentBet - ((100 - winProb) / 100) * this.currentBet).toFixed(0)
        };
    }
    
    // Risk assessment
    calculateRiskLevel() {
        if (!this.gameState === 'playing' || this.playerHand.length === 0 || this.dealerHand.length === 0) {
            return 50; // Default middle risk
        }
        
        const playerTotal = this.calculateHandValue(this.playerHand);
        const dealerUpcard = this.dealerHand[0];
        const dealerValue = dealerUpcard.numericValue;
        
        let risk = 50; // Base risk level (0-100)
        
        // Player total risk
        if (playerTotal <= 11) risk = 10; // Very safe
        else if (playerTotal <= 16) risk = 30 + (playerTotal - 12) * 5; // Increasing risk
        else if (playerTotal <= 20) risk = 20; // Safe
        else risk = 10; // Very safe (21)
        
        // Dealer upcard adjustment
        if (dealerValue >= 2 && dealerValue <= 6) risk -= 20; // Dealer weak
        else if (dealerValue >= 9) risk += 20; // Dealer strong
        
        return Math.max(0, Math.min(100, risk));
    }
    
    // Game actions
    dealNewHand() {
        const betAmount = parseInt(this.betInput.value);
        
        // Validate bet
        if (isNaN(betAmount) || betAmount <= 0) {
            this.showNotification('❌ Please enter a valid bet amount');
            return;
        }
        
        if (betAmount > this.balance) {
            this.showNotification('❌ Insufficient balance');
            return;
        }
        
        if (betAmount < 100 || betAmount > this.balance * 0.05) {
            this.showNotification('❌ Bet must be between 100 and 5% of bankroll');
            return;
        }
        
        // Initialize new hand
        this.gameState = 'playing';
        this.currentBet = betAmount;
        this.balance -= betAmount;
        this.playerHand = [];
        this.dealerHand = [];
        
        // Deal initial cards
        this.playerHand.push(this.drawCard());
        this.dealerHand.push(this.drawCard());
        this.playerHand.push(this.drawCard());
        this.dealerHand.push(this.drawCard());
        
        // Increment nonce for provably fair
        this.nonce++;
        
        this.renderHands();
        this.updateDisplay();
        this.updateStrategyDisplay();
        this.updateButtonStates();
        
        // Check for blackjack
        if (this.isBlackjack(this.playerHand)) {
            setTimeout(() => {
                if (this.isBlackjack(this.dealerHand)) {
                    this.endHand('push', 'Both have blackjack - Push!');
                } else {
                    this.endHand('blackjack', 'Blackjack! 3:2 payout');
                }
            }, 1000);
        }
    }
    
    playerHit() {
        if (this.gameState !== 'playing') return;
        
        this.playerHand.push(this.drawCard());
        this.renderHands();
        
        const playerTotal = this.calculateHandValue(this.playerHand);
        if (playerTotal > 21) {
            this.endHand('bust', 'Player busted!');
        } else {
            this.updateStrategyDisplay();
            this.updateButtonStates();
        }
    }
    
    playerStand() {
        if (this.gameState !== 'playing') return;
        
        this.dealerPlay();
    }
    
    playerDouble() {
        if (this.gameState !== 'playing') return;
        
        if (this.balance < this.currentBet) {
            this.showNotification('❌ Insufficient funds to double');
            return;
        }
        
        this.balance -= this.currentBet;
        this.currentBet *= 2;
        
        this.playerHand.push(this.drawCard());
        this.renderHands();
        this.updateDisplay();
        
        const playerTotal = this.calculateHandValue(this.playerHand);
        if (playerTotal > 21) {
            this.endHand('bust', 'Player busted after doubling!');
        } else {
            this.dealerPlay();
        }
    }
    
    playerSplit() {
        // Placeholder - will be implemented in future version
        this.showNotification('ℹ️ Split feature coming soon!');
    }
    
    playerSurrender() {
        if (this.gameState !== 'playing' || this.playerHand.length > 2) return;
        
        this.balance += this.currentBet / 2;
        this.endHand('surrender', 'Player surrendered - Half bet returned');
    }
    
    dealerPlay() {
        this.gameState = 'result';
        this.renderHands(); // Show hole card
        
        const dealerHitLoop = () => {
            if (this.shouldDealerHit()) {
                setTimeout(() => {
                    this.dealerHand.push(this.drawCard());
                    this.renderHands();
                    
                    const newTotal = this.calculateHandValue(this.dealerHand);
                    if (newTotal > 21) {
                        this.endHand('win', 'Dealer busted - Player wins!');
                    } else {
                        dealerHitLoop();
                    }
                }, 1000);
            } else {
                // Determine winner
                const playerTotal = this.calculateHandValue(this.playerHand);
                const dealerTotal = this.calculateHandValue(this.dealerHand);
                
                if (playerTotal > dealerTotal) {
                    this.endHand('win', `Player wins ${playerTotal} vs ${dealerTotal}!`);
                } else if (dealerTotal > playerTotal) {
                    this.endHand('lose', `Dealer wins ${dealerTotal} vs ${playerTotal}`);
                } else {
                    this.endHand('push', `Push - Both have ${playerTotal}`);
                }
            }
        };
        
        dealerHitLoop();
    }
    
    shouldDealerHit() {
        const total = this.calculateHandValue(this.dealerHand);
        
        // Basic rules: dealer hits on 16 and below, stands on 17+
        if (total < 17) return true;
        
        // Expert mode: dealer hits on soft 17
        if (total === 17 && this.difficulty === 'expert' && this.isSoft(this.dealerHand)) return true;
        
        return false;
    }
    
    endHand(result, message) {
        this.gameState = 'ready';
        this.handsPlayed++;
        
        let payout = 0;
        let achievements = [];
        
        switch (result) {
            case 'win':
                payout = this.currentBet * 2;
                this.handsWon++;
                break;
            case 'blackjack':
                payout = this.currentBet * 2.5; // 3:2 payout
                this.handsWon++;
                achievements.push('Blackjack Master');
                break;
            case 'push':
                payout = this.currentBet; // Return bet
                break;
            case 'surrender':
                // Already handled in playerSurrender
                break;
            default: // lose or bust
                payout = 0;
                break;
        }
        
        this.balance += payout;
        this.lastWin = payout > 0 ? payout - this.currentBet : -this.currentBet;
        
        // Check for achievements
        if (this.handsWon >= 5 && !this.achievements.has('streak5')) {
            achievements.push('Risk Manager');
            this.achievements.add('streak5');
        }
        
        if (this.balance > 11000 && !this.achievements.has('profit10')) {
            achievements.push('Bankroll Builder');
            this.achievements.add('profit10');
        }
        
        // Save to game history
        this.saveToHistory(result);
        
        // Show result modal
        this.showResult(result, message, this.lastWin, achievements);
        
        // Update displays
        this.updateDisplay();
        this.updateButtonStates();
        
        // Generate new server seed for next round
        this.generateNewServerSeed();
    }
    
    // Display methods
    renderHands() {
        const dealerCards = this.dealerCardsContainer;
        const playerCards = this.playerCardsContainer;
        
        // Clear existing cards
        dealerCards.innerHTML = '';
        playerCards.innerHTML = '';
        
        // Render dealer hand (hide hole card if game is active)
        this.dealerHand.forEach((card, index) => {
            const hidden = this.gameState === 'playing' && index === 1;
            dealerCards.appendChild(this.createCardElement(card, hidden));
        });
        
        // Render player hand
        this.playerHand.forEach(card => {
            playerCards.appendChild(this.createCardElement(card));
        });
        
        // Update totals
        const dealerTotal = this.gameState === 'playing' ? 
            (this.dealerHand.length > 0 ? this.dealerHand[0].numericValue + '+ ?' : '0') :
            this.calculateHandValue(this.dealerHand);
        
        this.dealerTotalElement.textContent = dealerTotal;
        this.playerTotalElement.textContent = this.calculateHandValue(this.playerHand);
    }
    
    createCardElement(card, hidden = false) {
        const cardEl = document.createElement('div');
        cardEl.className = 'card' + (hidden ? ' hidden' : '');
        
        if (hidden) {
            cardEl.innerHTML = `
                <div class="card-value">?</div>
                <div class="card-suit">🂠</div>
            `;
        } else {
            cardEl.innerHTML = `
                <div class="card-value ${card.isRed ? 'red-suit' : 'black-suit'}">${card.value}</div>
                <div class="card-suit ${card.isRed ? 'red-suit' : 'black-suit'}">${card.suit}</div>
            `;
        }
        
        return cardEl;
    }
    
    updateDisplay() {
        // Update stats
        this.bankrollDisplay.textContent = this.balance.toLocaleString();
        this.currentBetDisplay.textContent = this.currentBet.toLocaleString();
        this.handsWonDisplay.textContent = this.handsWon;
        
        const winRate = this.handsPlayed > 0 ? (this.handsWon / this.handsPlayed * 100).toFixed(1) : 0;
        this.winRateDisplay.textContent = winRate + '%';
        
        // Update deck info
        const cardsUsed = (this.totalDecks * 52) - this.deck.length;
        const penetration = (cardsUsed / (this.totalDecks * 52)) * 100;
        this.deckPenetration = penetration;
        
        this.cardsRemainingDisplay.textContent = this.deck.length;
        this.penetrationFillDisplay.style.width = penetration + '%';
        this.penetrationPercentDisplay.textContent = penetration.toFixed(1);
    }
    
    updateStrategyDisplay() {
        if (this.gameState !== 'playing' || this.playerHand.length === 0 || this.dealerHand.length === 0) {
            this.strategyRecommendation.innerHTML = `
                <div class="recommendation-action">Waiting for hand...</div>
                <div class="recommendation-reason">Place your bet and deal cards to begin</div>
            `;
            
            this.winProbDisplay.textContent = '--';
            this.bustProbDisplay.textContent = '--';
            this.expectedValueDisplay.textContent = '--';
            
            // Reset risk meter
            this.riskIndicator.style.left = '50%';
            
            return;
        }
        
        // Get strategy recommendation
        const action = this.getBasicStrategyAction(this.playerHand, this.dealerHand[0]);
        const playerTotal = this.calculateHandValue(this.playerHand);
        const explanation = this.getActionExplanation(action, playerTotal, this.dealerHand[0]);
        
        const actionNames = {
            'H': 'HIT',
            'S': 'STAND', 
            'D': 'DOUBLE DOWN',
            'P': 'SPLIT',
            'R': 'SURRENDER'
        };
        
        this.strategyRecommendation.innerHTML = `
            <div class="recommendation-action">Recommended: ${actionNames[action] || 'HIT'}</div>
            <div class="recommendation-reason">${explanation}</div>
        `;
        
        // Update probabilities
        const probs = this.calculateProbabilities();
        this.winProbDisplay.textContent = probs.winProb + '%';
        this.bustProbDisplay.textContent = probs.bustProb + '%';
        this.expectedValueDisplay.textContent = (probs.expectedValue >= 0 ? '+' : '') + probs.expectedValue;
        
        // Update risk meter
        const risk = this.calculateRiskLevel();
        this.riskIndicator.style.left = risk + '%';
        
        // Color code probabilities
        this.winProbDisplay.className = 'prob-value ' + (probs.winProb > 50 ? 'prob-positive' : 'prob-negative');
        this.bustProbDisplay.className = 'prob-value ' + (probs.bustProb < 30 ? 'prob-positive' : 'prob-negative');
        this.expectedValueDisplay.className = 'prob-value ' + (probs.expectedValue > 0 ? 'prob-positive' : 'prob-negative');
    }
    
    updateButtonStates() {
        if (this.gameState === 'playing' && this.playerHand.length > 0) {
            this.dealBtn.disabled = true;
            this.hitBtn.disabled = false;
            this.standBtn.disabled = false;
            this.doubleBtn.disabled = this.playerHand.length > 2 || this.balance < this.currentBet;
            this.splitBtn.disabled = !this.canSplit(this.playerHand) || this.balance < this.currentBet;
            this.surrenderBtn.disabled = this.playerHand.length > 2;
        } else {
            this.dealBtn.disabled = false;
            this.hitBtn.disabled = true;
            this.standBtn.disabled = true;
            this.doubleBtn.disabled = true;
            this.splitBtn.disabled = true;
            this.surrenderBtn.disabled = true;
        }
    }
    
    showResult(result, message, netWin, achievements) {
        // Set modal content
        this.resultTitle.textContent = result === 'win' || result === 'blackjack' ? 'You Win!' : 
                                      result === 'push' ? 'Push' : 'You Lose';
        this.resultMessage.textContent = message;
        
        this.resultDetails.innerHTML = `
            <p>Net Result: ${netWin >= 0 ? '+' : ''}${netWin} GA</p>
            <p>Current Balance: ${this.balance.toLocaleString()} GA</p>
        `;
        
        if (achievements.length > 0) {
            this.achievementsDisplay.innerHTML = achievements.map(achievement => 
                `<div class="achievement-badge">${achievement}</div>`
            ).join('');
        } else {
            this.achievementsDisplay.innerHTML = '';
        }
        
        // Display modal
        this.resultModal.style.display = 'block';
    }
    
    closeResult() {
        this.resultModal.style.display = 'none';
    }
    
    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
            padding: 1rem;
            border-radius: 8px;
            z-index: 10000;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideIn 0.3s ease reverse';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }
    
    // Session timer
    startSessionTimer() {
        setInterval(() => this.updateSessionTimer(), 1000);
    }
    
    updateSessionTimer() {
        const elapsed = Date.now() - this.sessionStartTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        
        this.sessionTimeDisplay.textContent = 
            String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
        
        if (minutes >= 30) {
            this.sessionTimer.classList.add('timer-warning');
            if (minutes === 30 && seconds === 0) {
                this.showNotification('⏰ 30-minute play session reached. Consider taking a break!');
            }
        }
    }
    
    // Provably fair methods
    generateNewServerSeed() {
        this.serverSeed = this.generateRandomString(32);
        this.serverSeedHash = this.hashString(this.serverSeed);
    }
    
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    hashString(str) {
        // Simple hash function for demo purposes
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(8, '0');
    }
    
    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash;
    }
    
    // Game history
    saveToHistory(result) {
        const historyItem = {
            nonce: this.nonce,
            playerHand: [...this.playerHand],
            dealerHand: [...this.dealerHand],
            bet: this.currentBet,
            result: result,
            winAmount: this.lastWin,
            timestamp: new Date()
        };
        
        this.gameHistory.unshift(historyItem);
        
        // Limit history size
        if (this.gameHistory.length > 10) {
            this.gameHistory.pop();
        }
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM loaded, initializing Blackjack game...");
    try {
        window.blackjackGame = new BlackjackGame();
        console.log("Blackjack game initialized successfully!");
    } catch (error) {
        console.error("Error initializing Blackjack game:", error);
    }
});