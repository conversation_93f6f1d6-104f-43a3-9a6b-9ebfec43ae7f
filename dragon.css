/* Dragon Tower Game CSS */
:root {
    --primary-color: #4a8c3f;
    --secondary-color: #37474f;
    --danger-color: #ff4d4d;
    --gold-color: #ffd700;
    --dragon-red: #d32f2f;
    --dragon-blue: #1976d2;
    --dragon-green: #388e3c;
}

.dragon-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1rem;
}

.dragon-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.back-link {
    display: flex;
    align-items: center;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.back-link:hover {
    color: #007bff;
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-size: 1.8rem;
    text-align: center;
    margin: 0;
    background: linear-gradient(45deg, #4a8c3f, #388e3c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-toggle {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-left: auto;
}

.view-mode-toggle button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
}

.view-mode-toggle button.active {
    background: #4a8c3f;
    color: white;
}

.game-dashboard {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.game-stats {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    flex: 1;
    flex-wrap: wrap;
    min-width: 280px;
}

.stat-item {
    flex: 1;
    min-width: 80px;
    text-align: center;
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    font-weight: 500;
    color: #555;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
}

.stat-value.danger {
    color: var(--danger-color);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.game-controls {
    flex: 1;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 280px;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #4361ee;
    color: white;
    box-shadow: 0 4px 6px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover {
    background: #3a56d4;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(67, 97, 238, 0.4);
}

.btn-success {
    background: #32CD32;
    color: white;
    box-shadow: 0 4px 6px rgba(50, 205, 50, 0.3);
}

.btn-success:hover {
    background: #28a428;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(50, 205, 50, 0.4);
}

.btn-danger {
    background: #FF5252;
    color: white;
    box-shadow: 0 4px 6px rgba(255, 82, 82, 0.3);
}

.btn-danger:hover {
    background: #ff3939;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(255, 82, 82, 0.4);
}

.btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;
}

.game-area {
    position: relative;
    width: 100%;
    height: 500px;
    background: linear-gradient(to bottom, #1e3a8a, #3b82f6, #7dd3fc);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    background-size: 400% 400%;
    animation: gradientBG 30s ease infinite;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.star {
    position: absolute;
    background: white;
    border-radius: 50%;
    opacity: 0.8;
    animation: twinkle 3s infinite alternate;
}

@keyframes twinkle {
    0% { opacity: 0.2; }
    100% { opacity: 0.8; }
}

.tower-container {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 400px;
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    perspective: 1000px;
}

.floor-indicator {
    position: absolute;
    left: 10px;
    top: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 1.2rem;
    z-index: 10;
}

.progress-bar {
    position: absolute;
    left: 10px;
    bottom: 10px;
    width: 20px;
    height: 80%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    z-index: 10;
}

.progress-fill {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 0%;
    background: linear-gradient(to top, #4CAF50, #8BC34A);
    border-radius: 10px;
    transition: height 0.3s ease;
}

.progress-fake {
    position: absolute;
    top: 0;
    width: 100%;
    height: 10%;
    background: #FFD700;
    border-radius: 10px;
    opacity: 0.7;
}

.tower-block {
    width: 120px;
    height: 35px;
    background: linear-gradient(to bottom, #f06292, #ec407a);
    border-radius: 5px;
    position: relative;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: bold;
    margin-bottom: 2px;
    border: 2px solid rgba(255,255,255,0.3);
    font-size: 0.9rem;
}

.tower-block.gold {
    background: linear-gradient(to bottom, #FFD700, #FFC107);
    box-shadow: 0 0 10px #FFD700;
}

.tower-block.cursed {
    background: linear-gradient(to bottom, #7b1fa2, #6a1b9a);
    box-shadow: 0 0 10px #9c27b0;
}

.tower-block.weak {
    opacity: 0.8;
    border: 2px dashed rgba(255,0,0,0.5);
}

.tower-block.falling {
    animation: fall 1s forwards;
}

.tower-block.burning {
    animation: burn 2s forwards;
}

@keyframes fall {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(500px) rotate(45deg); opacity: 0; }
}

@keyframes burn {
    0% { background: linear-gradient(to bottom, #f06292, #ec407a); }
    50% { background: linear-gradient(to bottom, #FF5722, #FF9800); }
    100% { background: linear-gradient(to bottom, #212121, #424242); opacity: 0.5; }
}

.ground {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: linear-gradient(to bottom, #57534e, #44403c);
    z-index: 5;
    box-shadow: 0 -5px 15px rgba(0,0,0,0.2);
}

.dragon {
    position: absolute;
    width: 120px;
    height: 100px;
    z-index: 20;
    transition: all 0.5s ease;
    filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.5));
    background-size: contain !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

.dragon.red {
    top: 20%;
    right: 5%;
    background: url("data:image/svg+xml,%3Csvg width='400' height='300' viewBox='0 0 400 300' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M280 80C320 40 360 80 380 60C390 50 380 120 400 100C420 80 360 140 340 160C320 180 300 130 240 140C200 80 120 120 80 100C60 90 40 80 80 60C20 40 60 20 100 40C140 20 160 60 180 80C200 60 240 60 280 80Z' fill='%23B91C1C'/%3E%3Cpath d='M260 60C280 40 340 60 360 40C380 20 360 100 380 80C400 60 340 120 320 140C300 160 280 110 220 120C180 60 100 100 60 80C40 70 20 60 60 40C0 20 40 0 80 20C120 0 140 40 160 60C180 40 220 40 260 60Z' fill='%23EF4444'/%3E%3Cpath d='M250 90C290 50 330 90 350 70C360 60 350 130 370 110C390 90 330 150 310 170C290 190 270 140 210 150C170 90 90 130 50 110C30 100 10 90 50 70C-10 50 30 30 70 50C110 30 130 70 150 90C170 70 210 70 250 90Z' fill='%23B91C1C'/%3E%3Cpath d='M260 100C280 80 300 110 310 100C315 95 310 130 320 120C330 110 300 140 290 150C280 160 270 135 240 140C220 110 180 130 160 120C150 115 140 110 160 100C130 90 150 80 170 90C190 80 200 100 210 110C220 100 240 90 260 100Z' fill='%23EF4444'/%3E%3Cpath d='M290 60C310 40 340 70 350 60C355 55 350 90 360 80C370 70 340 100 330 110C320 120 310 95 280 100C260 70 220 90 200 80C190 75 180 70 200 60C170 50 190 40 210 50C230 40 240 60 250 70C260 60 280 50 290 60Z' fill='%23B91C1C'/%3E%3Cellipse cx='300' cy='70' rx='10' ry='10' fill='white'/%3E%3Cellipse cx='300' cy='70' rx='5' ry='5' fill='black'/%3E%3Cpath d='M340 90L360 70M360 70L380 90M360 70L360 50' stroke='%23FCA5A5' stroke-width='2'/%3E%3Cpath d='M230 120L250 100M250 100L270 120M250 100L250 80' stroke='%23FCA5A5' stroke-width='2'/%3E%3Cpath d='M290 190L330 170L290 150L330 130' stroke='%23B91C1C' stroke-width='5'/%3E%3C/svg%3E");
    transform: scaleX(-1);
    filter: drop-shadow(0 0 15px rgba(255, 0, 0, 0.3));
}

.dragon.blue {
    top: 40%;
    left: 5%;
    background: url("data:image/svg+xml,%3Csvg width='400' height='300' viewBox='0 0 400 300' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M280 80C320 40 360 80 380 60C390 50 380 120 400 100C420 80 360 140 340 160C320 180 300 130 240 140C200 80 120 120 80 100C60 90 40 80 80 60C20 40 60 20 100 40C140 20 160 60 180 80C200 60 240 60 280 80Z' fill='%231E40AF'/%3E%3Cpath d='M260 60C280 40 340 60 360 40C380 20 360 100 380 80C400 60 340 120 320 140C300 160 280 110 220 120C180 60 100 100 60 80C40 70 20 60 60 40C0 20 40 0 80 20C120 0 140 40 160 60C180 40 220 40 260 60Z' fill='%233B82F6'/%3E%3Cpath d='M250 90C290 50 330 90 350 70C360 60 350 130 370 110C390 90 330 150 310 170C290 190 270 140 210 150C170 90 90 130 50 110C30 100 10 90 50 70C-10 50 30 30 70 50C110 30 130 70 150 90C170 70 210 70 250 90Z' fill='%231E40AF'/%3E%3Cpath d='M260 100C280 80 300 110 310 100C315 95 310 130 320 120C330 110 300 140 290 150C280 160 270 135 240 140C220 110 180 130 160 120C150 115 140 110 160 100C130 90 150 80 170 90C190 80 200 100 210 110C220 100 240 90 260 100Z' fill='%233B82F6'/%3E%3Cpath d='M290 60C310 40 340 70 350 60C355 55 350 90 360 80C370 70 340 100 330 110C320 120 310 95 280 100C260 70 220 90 200 80C190 75 180 70 200 60C170 50 190 40 210 50C230 40 240 60 250 70C260 60 280 50 290 60Z' fill='%231E40AF'/%3E%3Cellipse cx='300' cy='70' rx='10' ry='10' fill='white'/%3E%3Cellipse cx='300' cy='70' rx='5' ry='5' fill='black'/%3E%3Cpath d='M340 90L360 70M360 70L380 90M360 70L360 50' stroke='%2393C5FD' stroke-width='2'/%3E%3Cpath d='M230 120L250 100M250 100L270 120M250 100L250 80' stroke='%2393C5FD' stroke-width='2'/%3E%3Cpath d='M290 190L330 170L290 150L330 130' stroke='%231E40AF' stroke-width='5'/%3E%3C/svg%3E");
    filter: drop-shadow(0 0 15px rgba(0, 0, 255, 0.3));
}

.dragon.green {
    bottom: 20%;
    right: 10%;
    background: url("data:image/svg+xml,%3Csvg width='400' height='300' viewBox='0 0 400 300' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M280 80C320 40 360 80 380 60C390 50 380 120 400 100C420 80 360 140 340 160C320 180 300 130 240 140C200 80 120 120 80 100C60 90 40 80 80 60C20 40 60 20 100 40C140 20 160 60 180 80C200 60 240 60 280 80Z' fill='%23166534'/%3E%3Cpath d='M260 60C280 40 340 60 360 40C380 20 360 100 380 80C400 60 340 120 320 140C300 160 280 110 220 120C180 60 100 100 60 80C40 70 20 60 60 40C0 20 40 0 80 20C120 0 140 40 160 60C180 40 220 40 260 60Z' fill='%2322C55E'/%3E%3Cpath d='M250 90C290 50 330 90 350 70C360 60 350 130 370 110C390 90 330 150 310 170C290 190 270 140 210 150C170 90 90 130 50 110C30 100 10 90 50 70C-10 50 30 30 70 50C110 30 130 70 150 90C170 70 210 70 250 90Z' fill='%23166534'/%3E%3Cpath d='M260 100C280 80 300 110 310 100C315 95 310 130 320 120C330 110 300 140 290 150C280 160 270 135 240 140C220 110 180 130 160 120C150 115 140 110 160 100C130 90 150 80 170 90C190 80 200 100 210 110C220 100 240 90 260 100Z' fill='%2322C55E'/%3E%3Cpath d='M290 60C310 40 340 70 350 60C355 55 350 90 360 80C370 70 340 100 330 110C320 120 310 95 280 100C260 70 220 90 200 80C190 75 180 70 200 60C170 50 190 40 210 50C230 40 240 60 250 70C260 60 280 50 290 60Z' fill='%23166534'/%3E%3Cellipse cx='300' cy='70' rx='10' ry='10' fill='white'/%3E%3Cellipse cx='300' cy='70' rx='5' ry='5' fill='black'/%3E%3Cpath d='M340 90L360 70M360 70L380 90M360 70L360 50' stroke='%2386EFAC' stroke-width='2'/%3E%3Cpath d='M230 120L250 100M250 100L270 120M250 100L250 80' stroke='%2386EFAC' stroke-width='2'/%3E%3Cpath d='M290 190L330 170L290 150L330 130' stroke='%23166534' stroke-width='5'/%3E%3C/svg%3E");
    transform: scaleX(-1);
    filter: drop-shadow(0 0 15px rgba(0, 255, 0, 0.3));
}

.dragon.attacking {
    animation: dragonAttack 2s;
    filter: brightness(1.5) drop-shadow(0 0 20px rgba(255, 255, 0, 0.5));
}

@keyframes dragonAttack {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.2) rotate(-5deg); }
    50% { transform: scale(1.4) rotate(5deg); }
    75% { transform: scale(1.2) rotate(-5deg); }
    100% { transform: scale(1) rotate(0deg); }
}

.attack-indicator {
    position: absolute;
    padding: 0.3rem 0.8rem;
    background: rgba(255, 0, 0, 0.7);
    color: white;
    border-radius: 15px;
    font-weight: bold;
    animation: fadeInOut 2s forwards;
    z-index: 25;
    font-size: 0.9rem;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: scale(0.8); }
    20% { opacity: 1; transform: scale(1.1); }
    80% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0.8); }
}

.rage-meter-container {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 150px;
    height: 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    z-index: 10;
}

.rage-meter {
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, #FF9800, #FF5722);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.rage-meter.danger {
    animation: rage-pulse 0.5s infinite alternate;
}

@keyframes rage-pulse {
    0% { background: linear-gradient(to right, #FF9800, #FF5722); }
    100% { background: linear-gradient(to right, #FF5722, #D32F2F); }
}

.tilt-indicator {
    position: absolute;
    top: 35px;
    right: 10px;
    width: 40px;
    height: 80px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    overflow: hidden;
}

.tilt-meter {
    width: 80%;
    height: 0%;
    background: linear-gradient(to top, #4CAF50, #FFEB3B, #FF5722);
    transition: height 0.3s ease;
}

.tilt-marker {
    position: absolute;
    width: 100%;
    height: 2px;
    background: rgba(255, 255, 255, 0.7);
}

.tilt-marker.danger {
    background: rgba(255, 0, 0, 0.7);
}

.doom-timer {
    position: absolute;
    top: 125px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.4rem;
    border-radius: 10px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: bold;
}

.doom-timer-label {
    font-size: 0.7rem;
    margin-bottom: 0.2rem;
}

.doom-timer-value {
    font-size: 1.1rem;
    color: #FF5252;
}

.stable-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(76, 175, 80, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    z-index: 30;
    display: none;
    animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.game-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    text-align: center;
    z-index: 40;
    display: none;
    width: 85%;
    max-width: 350px;
}

.game-message h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #FF5252;
}

.game-message p {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.ghost-towers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.ghost-tower {
    position: absolute;
    width: 80px;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    left: calc(20% + (60% * var(--position-x)));
    bottom: calc(20% + (60% * var(--position-y)));
}

.ghost-tower::after {
    content: attr(data-height);
    position: absolute;
    top: -15px;
    right: -20px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.8rem;
}

.clouds {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40%;
    z-index: 2;
    pointer-events: none;
}

.cloud {
    position: absolute;
    background: rgba(255, 255, 255, 0);
    border-radius: 50%;
    opacity: 0.9;
    background-image: url("data:image/svg+xml,%3Csvg width='200' height='120' viewBox='0 0 200 120' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M168.5 78.5C181.5 78.5 192 68 192 55C192 42 181.5 31.5 168.5 31.5C167.5 31.5 166.5 31.5 165.5 31.7C159.8 13.8 142.9 1 122.9 1C99 1 80 20 80 43.9C80 45.3 80.1 46.6 80.2 48C78.2 47.7 76.1 47.5 74 47.5C57.4 47.5 44 60.9 44 77.5C44 94.1 57.4 107.5 74 107.5H168.5C181.5 107.5 192 97 192 84C192 71 181.5 60.5 168.5 60.5C167.5 60.5 166.5 60.5 165.5 60.7C159.8 42.8 153.9 78.5 168.5 78.5Z' fill='white' fill-opacity='0.9'/%3E%3Cpath d='M75 97C88 97 98.5 86.5 98.5 73.5C98.5 60.5 88 50 75 50C74 50 73 50 72 50.2C66.3 32.3 49.4 19.5 29.4 19.5C5.5 19.5 -13.5 38.5 -13.5 62.4C-13.5 63.8 -13.4 65.1 -13.3 66.5C-15.3 66.2 -17.4 66 -19.5 66C-36.1 66 -49.5 79.4 -49.5 96C-49.5 112.6 -36.1 126 -19.5 126H75C88 126 98.5 115.5 98.5 102.5C98.5 89.5 88 79 75 79C74 79 73 79 72 79.2C66.3 61.3 60.4 97 75 97Z' fill='white' fill-opacity='0.7'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
}

@keyframes cloudMove {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100vw); }
}

.mountain {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 150px;
    background-image: url("data:image/svg+xml,%3Csvg width='1000' height='150' viewBox='0 0 1000 150' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 150H1000V110L900 80L850 90L800 70L750 80L650 50L600 70L500 30L450 50L400 30L300 70L250 50L200 70L150 60L50 100L0 80V150Z' fill='%233f3f46'/%3E%3Cpath d='M0 150H1000V120L900 90L850 100L800 80L750 90L650 60L600 80L500 40L450 60L400 40L300 80L250 60L200 80L150 70L50 110L0 90V150Z' fill='%2752525b'/%3E%3C/svg%3E");
    background-size: cover;
    background-repeat: no-repeat;
    z-index: 3;
}

.tower-info {
    margin-top: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.info-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.tower-records {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.record-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 0.75rem;
    text-align: center;
}

.record-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.3rem;
}

.record-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
}

.record-value.highlight {
    color: var(--primary-color);
}

.record-value.lowlight {
    color: var(--danger-color);
}

.tower-tips {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 0.75rem;
}

.tips-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 0.9rem;
}

.tips-list {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.tips-list li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
    font-size: 0.85rem;
}

.tips-list li::before {
    content: "🐉";
    position: absolute;
    left: 0;
    top: 0;
}

.probability-note {
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    margin-top: 1rem;
    font-style: italic;
}

/* Pro View Styles */
.pro-view-active .game-area {
    height: 550px;
}

.pro-view-active .tower-container {
    height: 450px;
}

.pro-view-active .stat-dashboard {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1rem;
    justify-content: center;
}

.pro-view-stats {
    display: none;
}

.pro-view-active .pro-view-stats {
    display: block;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.pro-view-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.pro-view-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
}

.pro-stat-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.pro-stat-label {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 0.2rem;
}

.pro-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #333;
}

.risk-level {
    font-weight: bold;
}

.risk-low {
    color: #4CAF50;
}

.risk-medium {
    color: #FFC107;
}

.risk-high {
    color: #FF5722;
}

.risk-extreme {
    color: #F44336;
}

.probability-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.probability-table th, 
.probability-table td {
    padding: 0.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.probability-table th {
    background: rgba(0,0,0,0.05);
    font-weight: 600;
}

.probability-table tr:last-child td {
    border-bottom: none;
}

.dragon-attack-log {
    max-height: 150px;
    overflow-y: auto;
    background: rgba(0,0,0,0.05);
    border-radius: 6px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.attack-log-entry {
    margin-bottom: 0.25rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
}

.attack-log-entry:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.attack-type {
    font-weight: 600;
}

.attack-time {
    color: #666;
}

.block-properties {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.block-property {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: rgba(0,0,0,0.05);
}

.tilt-history-chart {
    width: 100%;
    height: 100px;
    background: rgba(255,255,255,0.5);
    border-radius: 6px;
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
}

.tilt-graph-line {
    position: absolute;
    bottom: 50px;
    left: 0;
    width: 100%;
    height: 1px;
    background: rgba(0,0,0,0.2);
}

.tilt-graph-point {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--primary-color);
    transform: translate(-50%, -50%);
}

.pro-controls {
    display: none;
    margin-top: 0.75rem;
}

.pro-view-active .pro-controls {
    display: flex;
    gap: 0.5rem;
}

.pro-btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    background: rgba(0,0,0,0.05);
    color: #333;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pro-btn:hover {
    background: rgba(0,0,0,0.1);
}

.pro-btn i {
    margin-right: 0.25rem;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .dragon-container {
        padding: 1rem;
    }
    
    .game-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .dragon-header {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .game-title {
        font-size: 1.5rem;
        margin: 0 auto;
        order: 2;
    }
    
    .back-link {
        order: 1;
    }
    
    .view-mode-toggle {
        order: 3;
        margin-top: 0.5rem;
        margin-left: auto;
    }
    
    .game-dashboard {
        flex-direction: column;
    }
    
    .game-area {
        height: 450px;
    }
    
    .tower-container {
        width: 250px;
        height: 350px;
    }
    
    .tower-block {
        width: 100px;
        height: 30px;
        font-size: 0.8rem;
    }
    
    .dragon {
        width: 80px;
        height: 70px;
    }
    
    .tower-records {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pro-view-active .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .dragon-container {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1.2rem;
    }
    
    .game-area {
        height: 400px;
    }
    
    .tower-container {
        width: 200px;
        height: 320px;
    }
    
    .tower-block {
        width: 90px;
        height: 25px;
        font-size: 0.75rem;
    }
    
    .dragon {
        width: 60px;
        height: 50px;
    }
    
    .floor-indicator {
        font-size: 1rem;
        padding: 0.4rem 0.8rem;
    }
    
    .rage-meter-container {
        width: 120px;
        height: 12px;
    }
    
    .tilt-indicator {
        width: 30px;
        height: 60px;
    }
    
    .doom-timer {
        top: 105px;
        padding: 0.3rem;
    }
    
    .doom-timer-label {
        font-size: 0.6rem;
    }
    
    .doom-timer-value {
        font-size: 0.9rem;
    }
    
    .pro-view-active .game-area {
        height: 450px;
    }
    
    .pro-view-active .tower-container {
        height: 370px;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 0.4rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .tower-records {
        gap: 0.5rem;
    }
    
    .record-item {
        padding: 0.5rem;
    }
    
    .record-label {
        font-size: 0.7rem;
    }
    
    .record-value {
        font-size: 1.1rem;
    }
    
    .tower-tips {
        padding: 0.5rem;
    }
    
    .tips-title {
        font-size: 0.85rem;
    }
    
    .tips-list li {
        font-size: 0.8rem;
        margin-bottom: 0.4rem;
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .btn {
        padding: 0.8rem 1.5rem;
        min-height: 44px;
    }
    
    .view-mode-toggle button {
        min-height: 36px;
        padding: 0.5rem 0.75rem;
    }
    
    .pro-btn {
        min-height: 40px;
    }
    
    input[type="number"] {
        min-height: 44px;
        font-size: 16px; /* Prevents iOS zoom */
    }
    
    .game-area {
        cursor: pointer;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .back-link {
        color: #ddd;
    }
    
    .game-stats,
    .game-controls,
    .tower-info,
    .pro-view-stats {
        background: rgba(30, 30, 30, 0.8);
    }
    
    .stat-item,
    .record-item,
    .tower-tips,
    .pro-stat-item {
        background: rgba(50, 50, 50, 0.5);
    }
    
    .stat-label,
    .record-label,
    .tips-title,
    .info-title,
    .pro-stat-label,
    .pro-view-title {
        color: #aaa;
    }
    
    .stat-value,
    .record-value,
    .pro-stat-value {
        color: #eee;
    }
    
    .tips-list li,
    .attack-log-entry,
    .probability-table {
        color: #ddd;
    }
    
    .attack-time {
        color: #aaa;
    }
    
    .block-property {
        background: rgba(255,255,255,0.1);
        color: #ddd;
    }
    
    .pro-btn {
        background: rgba(255,255,255,0.1);
        color: #ddd;
    }
    
    .pro-btn:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .tilt-history-chart {
        background: rgba(50, 50, 50, 0.5);
    }
    
    .probability-table th {
        background: rgba(255,255,255,0.1);
    }
    
    .dragon-attack-log {
        background: rgba(255,255,255,0.1);
        color: #ddd;
    }
}