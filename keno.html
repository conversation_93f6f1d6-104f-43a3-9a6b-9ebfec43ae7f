<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keno - Mathematical Playground</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="keno.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <div class="keno-container">
        <div class="game-header">
            <a href="index.html" class="back-link">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Games</span>
            </a>
            <div>
                <h1 class="game-title">MATHEMATICAL KENO</h1>
                <p class="game-subtitle">Transparent odds • Pattern strategies • Educational probability</p>
            </div>
            <div class="view-mode-toggle">
                <button id="standardViewBtn" class="active">Standard</button>
                <button id="proViewBtn">Pro View</button>
            </div>
        </div>

        <!-- Pro View Stats -->
        <div class="pro-view-stats">
            <div class="pro-view-title">
                <i class="fas fa-chart-line"></i> Advanced Analytics
            </div>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Pick Efficiency</div>
                    <div class="pro-stat-value" id="pickEfficiency">0%</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Pattern Score</div>
                    <div class="pro-stat-value" id="patternScore">0.0</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">ROI</div>
                    <div class="pro-stat-value" id="returnsValue">0%</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Numbers Hit</div>
                    <div class="pro-stat-value" id="numbersHitValue">0</div>
                </div>
            </div>

            <div class="pro-view-sections">
                <div class="pro-section pro-pattern-analysis">
                    <div class="pro-section-title">
                        <i class="fas fa-puzzle-piece"></i> Pattern Analysis
                    </div>
                    
                    <div class="pattern-score">
                        <div class="pattern-name">
                            <i class="fas fa-arrow-right" style="color: var(--pattern-blue);"></i>
                            <span>Sequence</span>
                        </div>
                        <div class="pattern-value" id="sequenceValue">0.0</div>
                    </div>
                    
                    <div class="pattern-score">
                        <div class="pattern-name">
                            <i class="fas fa-balance-scale" style="color: var(--pattern-gold);"></i>
                            <span>Balance</span>
                        </div>
                        <div class="pattern-value" id="balanceValue">0.0</div>
                    </div>
                    
                    <div class="pattern-score">
                        <div class="pattern-name">
                            <i class="fas fa-th" style="color: var(--pattern-purple);"></i>
                            <span>Clustering</span>
                        </div>
                        <div class="pattern-value" id="clusterValue">0.0</div>
                    </div>
                    
                    <div class="pattern-simulator">
                        <div class="simulator-control">
                            <span class="simulator-label">Trials</span>
                            <span class="simulator-value">1000</span>
                        </div>
                        <button class="simulator-btn" id="simulateBtn">
                            <i class="fas fa-play"></i> Simulate
                        </button>
                        <div class="simulator-control">
                            <span class="simulator-label">Win Rate</span>
                            <span class="simulator-value" id="simulationResult">--</span>
                        </div>
                    </div>
                </div>
                
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-history"></i> Game History
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-bar">
                            <span class="chart-label">Matches</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="matchesChart" style="width: 0%"></div>
                            </div>
                            <span class="chart-value" id="matchesRate">0.0</span>
                        </div>
                        <div class="chart-bar">
                            <span class="chart-label">Returns</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="returnsChart" style="width: 0%"></div>
                            </div>
                            <span class="chart-value" id="returnsRate">0.0</span>
                        </div>
                    </div>
                    
                    <div class="game-history" id="gameHistory">
                        <div class="history-entry">
                            <span class="history-detail">No games played yet</span>
                            <span class="history-result">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-temperature-high"></i> Number Coverage
                    </div>
                    
                    <div class="number-coverage-grid" id="numberCoverageGrid">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
            
            <div class="pro-betting-section">
                <div class="pro-betting-title">Betting Strategies</div>
                
                <div class="betting-strategies">
                    <div class="strategy-option" data-strategy="balanced">
                        <div class="strategy-name">Balanced</div>
                        <div class="strategy-desc">Even distribution across quadrants</div>
                    </div>
                    <div class="strategy-option" data-strategy="sequential">
                        <div class="strategy-name">Sequential</div>
                        <div class="strategy-desc">Consecutive numbers for higher payouts</div>
                    </div>
                    <div class="strategy-option" data-strategy="hot">
                        <div class="strategy-name">Hot Numbers</div>
                        <div class="strategy-desc">Focus on frequently drawn numbers</div>
                    </div>
                    <div class="strategy-option" data-strategy="cold">
                        <div class="strategy-name">Cold Numbers</div>
                        <div class="strategy-desc">Focus on rarely drawn numbers</div>
                    </div>
                </div>
            </div>
            
            <div class="pro-controls">
                <button class="pro-btn" id="analyzeSelectionBtn">
                    <i class="fas fa-calculator"></i> Analyze Selection
                </button>
                <button class="pro-btn" id="optimizePicksBtn">
                    <i class="fas fa-magic"></i> Optimize Picks
                </button>
                <button class="pro-btn" id="numberStatisticsBtn">
                    <i class="fas fa-chart-pie"></i> Number Statistics
                </button>
                <button class="pro-btn" id="exportDataBtn">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats -->
            <div class="stats-panel">
                <h3 class="panel-title">Player Stats</h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">GA Balance</div>
                        <div class="stat-value" id="bankroll">10,000</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Current Bet</div>
                        <div class="stat-value" id="currentBet">50</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Rounds Played</div>
                        <div class="stat-value" id="roundsPlayed">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Win Rate</div>
                        <div class="stat-value" id="winRate">0%</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-label">Knowledge Points</div>
                    <div class="stat-value" id="knowledgePoints">0</div>
                </div>

                <div class="paytable-container">
                    <h4 style="color: white; margin-bottom: 0.5rem; text-align: center;">Paytable</h4>
                    <table class="paytable">
                        <thead>
                            <tr>
                                <th>Matches</th>
                                <th>Payout (x Bet)</th>
                                <th>Probability</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>0</td>
                                <td>0</td>
                                <td>3.5%</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>1</td>
                                <td>15.7%</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>5</td>
                                <td>8.6%</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>25</td>
                                <td>2.4%</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>250</td>
                                <td>0.11%</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>10,000</td>
                                <td>0.0000003%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Keno Grid -->
            <div class="keno-grid-container">
                <div class="grid-controls">
                    <div class="betting-control">
                        <span class="bet-label">Your Bet:</span>
                        <input type="number" class="bet-input" id="betAmount" min="1" max="100" value="50" step="1">
                        <div class="number-limit">
                            <span class="bet-label">Selected:</span>
                            <div class="limit-badge" id="numberCounter">0/15</div>
                        </div>
                    </div>
                    
                    <div class="quick-bet-chips">
                        <div class="bet-chip chip-10" data-amount="10">10</div>
                        <div class="bet-chip chip-25" data-amount="25">25</div>
                        <div class="bet-chip chip-50" data-amount="50">50</div>
                        <div class="bet-chip chip-100" data-amount="100">100</div>
                    </div>
                </div>

                <div class="pattern-indicators">
                    <div class="pattern-indicator" id="sequencePattern">
                        <div class="pattern-icon"><i class="fas fa-arrow-right"></i></div>
                        <div>Sequence (1.5x)</div>
                    </div>
                    <div class="pattern-indicator" id="balancedPattern">
                        <div class="pattern-icon"><i class="fas fa-balance-scale"></i></div>
                        <div>Balanced (1.3x)</div>
                    </div>
                    <div class="pattern-indicator" id="clusterPattern">
                        <div class="pattern-icon"><i class="fas fa-th"></i></div>
                        <div>Cluster (0.8x)</div>
                    </div>
                </div>

                <div class="keno-grid" id="kenoGrid">
                    <!-- Grid will be generated by JavaScript -->
                </div>

                <div class="draw-animation" id="drawAnimation">
                    <h2 class="draw-title">Drawing Numbers...</h2>
                    <div class="ball-container" id="ballContainer">
                        <!-- Drawn balls will be added here -->
                    </div>
                </div>

                <div class="game-controls">
                    <button class="control-btn btn-clear" id="clearBtn">Clear</button>
                    <button class="control-btn btn-auto" id="quickPickBtn">Quick Pick</button>
                    <button class="control-btn btn-play" id="playBtn">Draw Numbers</button>
                    <button class="control-btn btn-info" id="infoBtn">Math Lab</button>
                </div>
            </div>

            <!-- Strategy & Analysis -->
            <div class="strategy-panel">
                <h3 class="panel-title">Probability Coach</h3>
                
                <div class="probability-display">
                    <div class="stat-label">Your Selection Odds</div>
                    
                    <div class="prob-grid">
                        <div class="prob-card">
                            <div class="prob-label">3+ Matches</div>
                            <div class="prob-value" id="odds3plus">--</div>
                        </div>
                        <div class="prob-card">
                            <div class="prob-label">5+ Matches</div>
                            <div class="prob-value" id="odds5plus">--</div>
                        </div>
                        <div class="prob-card">
                            <div class="prob-label">7+ Matches</div>
                            <div class="prob-value" id="odds7plus">--</div>
                        </div>
                        <div class="prob-card">
                            <div class="prob-label">Expected Value</div>
                            <div class="prob-value" id="expectedValue">--</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 0.75rem">
                        <div class="stat-label">Win Probability</div>
                        <div class="probability-meter">
                            <div class="probability-indicator" id="probabilityIndicator" style="left: 10%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-size: 0.7rem; margin-top: 0.3rem;">
                            <span>Low</span>
                            <span>Medium</span>
                            <span>High</span>
                        </div>
                    </div>
                </div>

                <div class="strategy-coach">
                    <div class="coach-title">Strategy Recommendation</div>
                    <div class="coach-recommendation" id="strategyTips">
                        Select 1-15 numbers from the grid to receive strategy tips.
                    </div>
                </div>

                <div class="historical-data">
                    <div class="history-title">Hot & Cold Numbers</div>
                    <div class="history-numbers" id="hotNumbers">
                        <div class="history-number" style="background: var(--math-red);">27</div>
                        <div class="history-number" style="background: var(--math-red);">42</div>
                        <div class="history-number" style="background: var(--math-red);">8</div>
                        <div class="history-number" style="background: var(--math-red);">63</div>
                        <div class="history-number" style="background: var(--math-red);">15</div>
                    </div>
                    <div class="stat-label">Cold Numbers</div>
                    <div class="history-numbers" id="coldNumbers">
                        <div class="history-number" style="background: var(--math-blue);">71</div>
                        <div class="history-number" style="background: var(--math-blue);">32</div>
                        <div class="history-number" style="background: var(--math-blue);">49</div>
                        <div class="history-number" style="background: var(--math-blue);">17</div>
                        <div class="history-number" style="background: var(--math-blue);">56</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tooltip for educational info -->
    <div class="tooltip" id="tooltip"></div>

    <!-- Result Modal -->
    <div class="result-modal" id="resultModal">
        <h2 id="resultTitle">Round Result</h2>
        <p id="resultMessage"></p>
        <div id="resultDetails"></div>
        <div id="achievements"></div>
        <button class="control-btn btn-play" onclick="closeResult()">Continue Playing</button>
    </div>

    <script src="assets/js/script.js"></script>
    <script src="keno.js"></script>
    <script>
        // Enhanced Keno Game with Pro View Features
        class EnhancedKenoGame extends KenoGame {
            constructor() {
                super();
                
                // Pro View variables
                this.viewMode = localStorage.getItem('kenoViewMode') || 'standard';
                this.pickEfficiency = 0;
                this.patternScore = 0;
                this.returnsPercentage = 0;
                this.numbersHit = 0;
                this.totalSpent = 0;
                this.totalWon = 0;
                
                // Pattern analysis
                this.sequenceScore = 0;
                this.balanceScore = 0;
                this.clusterScore = 0;
                
                // Number coverage data
                this.coverageData = Array(80).fill(0);
                
                // Initialize Pro View elements
                this.initProViewElements();
                this.attachProViewEventListeners();
                this.setViewMode(this.viewMode);
                this.createNumberCoverageGrid();
            }
            
            initProViewElements() {
                // View mode toggle buttons
                this.standardViewBtn = document.getElementById('standardViewBtn');
                this.proViewBtn = document.getElementById('proViewBtn');
                
                // Pro View statistics displays
                this.pickEfficiencyDisplay = document.getElementById('pickEfficiency');
                this.patternScoreDisplay = document.getElementById('patternScore');
                this.returnsValueDisplay = document.getElementById('returnsValue');
                this.numbersHitValueDisplay = document.getElementById('numbersHitValue');
                
                // Pattern analysis displays
                this.sequenceValueDisplay = document.getElementById('sequenceValue');
                this.balanceValueDisplay = document.getElementById('balanceValue');
                this.clusterValueDisplay = document.getElementById('clusterValue');
                this.simulationResultDisplay = document.getElementById('simulationResult');
                
                // Game history charts
                this.matchesChartDisplay = document.getElementById('matchesChart');
                this.returnsChartDisplay = document.getElementById('returnsChart');
                this.matchesRateDisplay = document.getElementById('matchesRate');
                this.returnsRateDisplay = document.getElementById('returnsRate');
                this.gameHistoryDisplay = document.getElementById('gameHistory');
                
                // Number coverage grid
                this.numberCoverageGrid = document.getElementById('numberCoverageGrid');
                
                // Betting strategy selectors
                this.strategyOptions = document.querySelectorAll('.strategy-option');
                
                // Pro View buttons
                this.analyzeSelectionBtn = document.getElementById('analyzeSelectionBtn');
                this.optimizePicksBtn = document.getElementById('optimizePicksBtn');
                this.numberStatisticsBtn = document.getElementById('numberStatisticsBtn');
                this.exportDataBtn = document.getElementById('exportDataBtn');
                this.simulateBtn = document.getElementById('simulateBtn');
                
                // Quick bet chips
                this.betChips = document.querySelectorAll('.bet-chip');
            }
            
            attachProViewEventListeners() {
                // View mode toggle
                if (this.standardViewBtn) {
                    this.standardViewBtn.addEventListener('click', () => this.setViewMode('standard'));
                }
                
                if (this.proViewBtn) {
                    this.proViewBtn.addEventListener('click', () => this.setViewMode('pro'));
                }
                
                // Pro View buttons
                if (this.analyzeSelectionBtn) {
                    this.analyzeSelectionBtn.addEventListener('click', () => this.analyzeSelection());
                }
                
                if (this.optimizePicksBtn) {
                    this.optimizePicksBtn.addEventListener('click', () => this.optimizePicks());
                }
                
                if (this.numberStatisticsBtn) {
                    this.numberStatisticsBtn.addEventListener('click', () => this.showNumberStatistics());
                }
                
                if (this.exportDataBtn) {
                    this.exportDataBtn.addEventListener('click', () => this.exportGameData());
                }
                
                if (this.simulateBtn) {
                    this.simulateBtn.addEventListener('click', () => this.simulateCurrentSelection());
                }
                
                // Betting strategies
                this.strategyOptions.forEach(option => {
                    option.addEventListener('click', () => this.applyBettingStrategy(option.dataset.strategy));
                });
                
                // Quick bet chips
                this.betChips.forEach(chip => {
                    chip.addEventListener('click', () => {
                        if (this.betInput) {
                            this.betInput.value = chip.dataset.amount;
                        }
                    });
                });
            }
            
            setViewMode(mode) {
                this.viewMode = mode;
                localStorage.setItem('kenoViewMode', mode);
                
                if (mode === 'standard') {
                    document.body.classList.remove('pro-view-active');
                    this.standardViewBtn.classList.add('active');
                    this.proViewBtn.classList.remove('active');
                } else {
                    document.body.classList.add('pro-view-active');
                    this.standardViewBtn.classList.remove('active');
                    this.proViewBtn.classList.add('active');
                    
                    // Update Pro View displays
                    this.updateProViewDisplay();
                }
            }
            
            // Create number coverage grid for heat map
            createNumberCoverageGrid() {
                if (!this.numberCoverageGrid) return;
                
                this.numberCoverageGrid.innerHTML = '';
                
                for (let i = 1; i <= 80; i++) {
                    const cell = document.createElement('div');
                    cell.className = 'coverage-cell';
                    cell.textContent = i;
                    cell.dataset.number = i;
                    
                    this.numberCoverageGrid.appendChild(cell);
                }
            }
            
            // Update number coverage display
            updateNumberCoverageDisplay() {
                if (!this.numberCoverageGrid) return;
                
                // Calculate max frequency for normalization
                const maxFreq = Math.max(...this.frequency.slice(1));
                
                // Update each cell
                for (let i = 1; i <= 80; i++) {
                    const cell = this.numberCoverageGrid.querySelector(`[data-number="${i}"]`);
                    if (!cell) continue;
                    
                    const freq = this.frequency[i];
                    
                    // Reset classes
                    cell.className = 'coverage-cell';
                    
                    // Add hot/cold classes
                    if (maxFreq > 0 && freq > 0) {
                        const ratio = freq / maxFreq;
                        if (ratio > 0.8) {
                            cell.classList.add('hot');
                        } else if (ratio < 0.2) {
                            cell.classList.add('cold');
                        }
                    }
                    
                    // Mark selected numbers
                    if (this.selectedNumbers.includes(i)) {
                        cell.classList.add('selected');
                    }
                }
            }
            
            // Override playRound to track additional stats
            playRound() {
                if (this.selectedNumbers.length === 0) {
                    this.showNotification('Please select at least 1 number');
                    return;
                }
                
                const bet = parseInt(this.betInput.value);
                if (isNaN(bet) || bet < 1 || bet > 100) {
                    this.showNotification('Bet must be between 1 and 100 GA');
                    return;
                }
                
                if (bet > this.balance) {
                    this.showNotification('Insufficient GA balance');
                    return;
                }
                
                // Track total spent for Pro View stats
                this.totalSpent += bet;
                
                // Call parent method
                super.playRound();
                
                // Update pattern analysis scores for Pro View
                this.updatePatternScores();
            }
            
            // Calculate pattern analysis scores
            updatePatternScores() {
                if (this.selectedNumbers.length === 0) {
                    this.sequenceScore = 0;
                    this.balanceScore = 0;
                    this.clusterScore = 0;
                    this.patternScore = 0;
                    return;
                }
                
                // Calculate sequence score
                const sortedNumbers = [...this.selectedNumbers].sort((a, b) => a - b);
                let maxSequence = 1;
                let currentSequence = 1;
                
                for (let i = 1; i < sortedNumbers.length; i++) {
                    if (sortedNumbers[i] === sortedNumbers[i-1] + 1) {
                        currentSequence++;
                        maxSequence = Math.max(maxSequence, currentSequence);
                    } else {
                        currentSequence = 1;
                    }
                }
                
                this.sequenceScore = maxSequence / this.selectedNumbers.length;
                
                // Calculate balance score
                const highCount = this.selectedNumbers.filter(num => num > 40).length;
                const lowCount = this.selectedNumbers.filter(num => num <= 40).length;
                const balanceRatio = Math.min(highCount, lowCount) / Math.max(highCount, lowCount);
                this.balanceScore = balanceRatio || 0;
                
                // Calculate cluster score
                const quadrants = [0, 0, 0, 0]; // Top-left, top-right, bottom-left, bottom-right
                
                for (const num of this.selectedNumbers) {
                    const row = Math.floor((num - 1) / 10);
                    const col = (num - 1) % 10;
                    
                    const quadrantIdx = (row < 4 ? 0 : 2) + (col < 5 ? 0 : 1);
                    quadrants[quadrantIdx]++;
                }
                
                const maxCluster = Math.max(...quadrants);
                this.clusterScore = maxCluster / this.selectedNumbers.length;
                
                // Overall pattern score - custom weighting
                this.patternScore = (this.sequenceScore * 0.35) + 
                                   (this.balanceScore * 0.35) + 
                                   (this.clusterScore * 0.3);
            }
            
            // Override calculateResults to update Pro View stats
            calculateResults() {
                // Call parent method first
                super.calculateResults();
                
                // Get most recent result
                if (this.lastResults.length > 0) {
                    const lastResult = this.lastResults[0];
                    
                    // Update Pro View stats
                    this.numbersHit += lastResult.matches;
                    this.totalWon += lastResult.payout;
                    
                    // Calculate pick efficiency (matches / selections)
                    if (lastResult.selected > 0) {
                        const efficiency = lastResult.matches / lastResult.selected;
                        // Use exponential moving average for smoother updates
                        this.pickEfficiency = (this.pickEfficiency * 0.7) + (efficiency * 0.3);
                    }
                    
                    // Calculate returns percentage
                    if (this.totalSpent > 0) {
                        this.returnsPercentage = (this.totalWon / this.totalSpent) * 100;
                    }
                    
                    // Update Pro View displays
                    this.updateProViewDisplay();
                    
                    // Update game history display
                    this.updateGameHistory(lastResult);
                }
                
                // Update number coverage display
                this.updateNumberCoverageDisplay();
            }
            
            // Update Pro View displays
            updateProViewDisplay() {
                if (this.viewMode !== 'pro') return;
                
                // Update main stats
                if (this.pickEfficiencyDisplay) {
                    this.pickEfficiencyDisplay.textContent = (this.pickEfficiency * 100).toFixed(1) + '%';
                }
                
                if (this.patternScoreDisplay) {
                    this.patternScoreDisplay.textContent = this.patternScore.toFixed(2);
                }
                
                if (this.returnsValueDisplay) {
                    this.returnsValueDisplay.textContent = this.returnsPercentage.toFixed(1) + '%';
                }
                
                if (this.numbersHitValueDisplay) {
                    this.numbersHitValueDisplay.textContent = this.numbersHit;
                }
                
                // Update pattern analysis
                if (this.sequenceValueDisplay) {
                    this.sequenceValueDisplay.textContent = this.sequenceScore.toFixed(2);
                }
                
                if (this.balanceValueDisplay) {
                    this.balanceValueDisplay.textContent = this.balanceScore.toFixed(2);
                }
                
                if (this.clusterValueDisplay) {
                    this.clusterValueDisplay.textContent = this.clusterScore.toFixed(2);
                }
                
                // Update charts
                const matchRate = this.roundsPlayed > 0 ? 
                    this.numbersHit / (this.roundsPlayed * this.selectedNumbers.length) : 0;
                
                if (this.matchesChartDisplay) {
                    this.matchesChartDisplay.style.width = (matchRate * 100) + '%';
                }
                
                if (this.matchesRateDisplay) {
                    this.matchesRateDisplay.textContent = (matchRate * 100).toFixed(1) + '%';
                }
                
                // Return rate chart (clip at 100% for display purposes)
                const displayReturnRate = Math.min(this.returnsPercentage, 100) / 100;
                
                if (this.returnsChartDisplay) {
                    this.returnsChartDisplay.style.width = (displayReturnRate * 100) + '%';
                }
                
                if (this.returnsRateDisplay) {
                    this.returnsRateDisplay.textContent = this.returnsPercentage.toFixed(1) + '%';
                }
            }
            
            // Update game history display
            updateGameHistory(gameResult) {
                if (!this.gameHistoryDisplay) return;
                
                const entry = document.createElement('div');
                entry.className = 'history-entry';
                
                const isWin = gameResult.payout > 0;
                
                entry.innerHTML = `
                    <span class="history-detail">${gameResult.matches}/${gameResult.selected} matches</span>
                    <span class="history-result ${isWin ? 'history-win' : 'history-loss'}">
                        ${isWin ? '+' + gameResult.payout : '0'} GA
                    </span>
                `;
                
                this.gameHistoryDisplay.prepend(entry);
                
                // Limit history entries
                const entries = this.gameHistoryDisplay.querySelectorAll('.history-entry');
                if (entries.length > 10) {
                    entries[entries.length - 1].remove();
                }
            }
            
            // Pro View button functions
            analyzeSelection() {
                if (this.selectedNumbers.length === 0) {
                    this.showNotification('Please select numbers to analyze');
                    return;
                }
                
                this.updatePatternScores();
                
                // Create detailed analysis text
                let analysisText = `Selection Analysis (${this.selectedNumbers.length} numbers)\n\n`;
                
                analysisText += `Pattern Score: ${this.patternScore.toFixed(2)}\n`;
                analysisText += `Sequence: ${(this.sequenceScore * 100).toFixed(1)}%\n`;
                analysisText += `Balance: ${(this.balanceScore * 100).toFixed(1)}%\n`;
                analysisText += `Clustering: ${(this.clusterScore * 100).toFixed(1)}%\n\n`;
                
                // Expected value
                const ev = this.calculateExpectedValue(this.selectedNumbers.length);
                analysisText += `Expected Value: ${ev.toFixed(2)}\n`;
                
                // Win probability
                const winProb = this.calculateProbability(this.selectedNumbers.length, 3) * 100;
                analysisText += `Win Probability: ${winProb.toFixed(1)}%\n\n`;
                
                // Improvement suggestions
                if (this.sequenceScore < 0.3 && this.balanceScore < 0.5) {
                    analysisText += 'Suggestion: Add more sequential numbers or balance high/low distribution for better pattern bonuses.';
                } else if (this.patternScore > 0.7) {
                    analysisText += 'Your selection has excellent pattern characteristics with good multiplier potential.';
                } else {
                    analysisText += 'Suggestion: Your selection is average. Consider optimizing for better pattern recognition.';
                }
                
                this.showNotification(analysisText);
                
                // Update Pro View displays
                this.updateProViewDisplay();
            }
            
            optimizePicks() {
                // Clear current selection
                this.clearSelection();
                
                // Find optimal pick strategy based on past results
                const optimalCount = this.roundsPlayed > 5 ? 8 : 10; // Default to 10 picks if not enough data
                
                // Find hot numbers with good return
                const numsByFrequency = Array.from({length: 80}, (_, i) => i + 1)
                    .sort((a, b) => this.frequency[b] - this.frequency[a]);
                
                // Select top numbers, with some randomization
                const hotNumbers = numsByFrequency.slice(0, Math.min(20, numsByFrequency.length));
                shuffleArray(hotNumbers); // Need to define this utility function
                
                // Select a balanced set
                const toSelect = Math.min(optimalCount, hotNumbers.length);
                const selectedNumbers = hotNumbers.slice(0, toSelect);
                
                // Ensure some sequential numbers for pattern bonus
                selectedNumbers.sort((a, b) => a - b);
                
                // Add numbers to selection
                for (const num of selectedNumbers) {
                    this.selectedNumbers.push(num);
                    const cell = document.querySelector(`.keno-number[data-number="${num}"]`);
                    if (cell) {
                        cell.classList.add('selected');
                    }
                }
                
                // Update displays
                this.updateNumberCounter();
                this.updatePatternIndicators();
                this.updateProbabilityDisplay();
                this.updateStrategyTips();
                this.updatePatternScores();
                this.updateProViewDisplay();
                this.updateNumberCoverageDisplay();
                
                this.showNotification(`Optimized selection with ${selectedNumbers.length} numbers based on historical performance.`);
                
                // Utility function for shuffle
                function shuffleArray(array) {
                    for (let i = array.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [array[i], array[j]] = [array[j], array[i]];
                    }
                }
            }
            
            // Show number statistics
            showNumberStatistics() {
                // Calculate stats for each number
                const numberStats = Array.from({length: 80}, (_, i) => {
                    const number = i + 1;
                    const frequency = this.frequency[number] || 0;
                    const hitRate = this.roundsPlayed > 0 ? frequency / this.roundsPlayed : 0;
                    
                    return {
                        number,
                        frequency,
                        hitRate
                    };
                });
                
                // Sort by frequency
                numberStats.sort((a, b) => b.frequency - a.frequency);
                
                // Generate statistics report
                let statsText = 'Number Statistics\n\n';
                
                // Top 5 hot numbers
                statsText += 'Hot Numbers:\n';
                for (let i = 0; i < 5 && i < numberStats.length; i++) {
                    const stat = numberStats[i];
                    statsText += `${stat.number}: ${stat.frequency} draws (${(stat.hitRate * 100).toFixed(1)}%)\n`;
                }
                
                statsText += '\nCold Numbers:\n';
                for (let i = numberStats.length - 5; i < numberStats.length; i++) {
                    if (i >= 0) {
                        const stat = numberStats[i];
                        statsText += `${stat.number}: ${stat.frequency} draws (${(stat.hitRate * 100).toFixed(1)}%)\n`;
                    }
                }
                
                // Overall metrics
                const totalDraws = this.roundsPlayed * 20; // 20 numbers per draw
                const expectedFrequency = totalDraws / 80; // Expected frequency if completely random
                
                statsText += '\nDraws Analysis:\n';
                statsText += `Total Rounds: ${this.roundsPlayed}\n`;
                statsText += `Total Numbers Drawn: ${totalDraws}\n`;
                statsText += `Expected Frequency: ${expectedFrequency.toFixed(2)} per number\n`;
                
                this.showNotification(statsText);
            }
            
            // Simulate current selection 
            simulateCurrentSelection() {
                if (this.selectedNumbers.length === 0) {
                    this.showNotification('Please select numbers to simulate');
                    return;
                }
                
                // Show "calculating" state
                if (this.simulationResultDisplay) {
                    this.simulationResultDisplay.textContent = '...';
                }
                
                // Simulate 1000 rounds with current selection
                const trials = 1000;
                let wins = 0;
                let totalReturn = 0;
                const bet = 50; // Fixed bet for simulation
                
                for (let i = 0; i < trials; i++) {
                    // Generate 20 random draws
                    const draws = [];
                    const availableNumbers = Array.from({length: 80}, (_, i) => i + 1);
                    
                    for (let j = 0; j < 20; j++) {
                        const randomIndex = Math.floor(Math.random() * availableNumbers.length);
                        draws.push(availableNumbers.splice(randomIndex, 1)[0]);
                    }
                    
                    // Count matches
                    const matches = this.selectedNumbers.filter(num => draws.includes(num)).length;
                    
                    // Calculate payout
                    let payout = 0;
                    for (const [tier, multiplier] of Object.entries(this.PAYTABLE)) {
                        if (matches >= parseInt(tier) && parseInt(tier) <= this.selectedNumbers.length) {
                            payout = multiplier * bet;
                        }
                    }
                    
                    // Apply pattern multiplier
                    for (const pattern of Object.values(this.PATTERNS)) {
                        if (pattern.check(this.selectedNumbers)) {
                            payout *= pattern.multiplier;
                            break;
                        }
                    }
                    
                    // Track wins and returns
                    if (payout > 0) {
                        wins++;
                        totalReturn += payout;
                    }
                }
                
                // Calculate win rate and ROI
                const winRate = (wins / trials) * 100;
                const roi = (totalReturn / (trials * bet) - 1) * 100;
                
                // Update display
                if (this.simulationResultDisplay) {
                    this.simulationResultDisplay.textContent = winRate.toFixed(1) + '%';
                }
                
                // Show detailed results
                this.showNotification(`Simulation Results (${trials} trials):\n\nWin Rate: ${winRate.toFixed(1)}%\nROI: ${roi.toFixed(1)}%\nAverage Return: ${(totalReturn / trials).toFixed(2)} GA`);
            }
            
            // Apply betting strategy
            applyBettingStrategy(strategy) {
                // Clear current selection
                this.clearSelection();
                
                let numbersToSelect = [];
                
                switch (strategy) {
                    case 'balanced':
                        // Select equal numbers from each quadrant
                        for (let q = 0; q < 4; q++) {
                            const quadrantStart = (q % 2 === 0 ? 1 : 41) + (Math.floor(q / 2) * 20);
                            for (let i = 0; i < 2; i++) {
                                const offset = Math.floor(Math.random() * 19);
                                numbersToSelect.push(quadrantStart + offset);
                            }
                        }
                        break;
                        
                    case 'sequential':
                        // Find a sequence of 8 numbers
                        const startNumber = Math.floor(Math.random() * 73) + 1;
                        for (let i = 0; i < 8; i++) {
                            numbersToSelect.push(startNumber + i);
                        }
                        break;
                        
                    case 'hot':
                        // Select hot numbers
                        const numsByFrequency = Array.from({length: 80}, (_, i) => i + 1)
                            .sort((a, b) => this.frequency[b] - this.frequency[a]);
                        numbersToSelect = numsByFrequency.slice(0, 8);
                        break;
                        
                    case 'cold':
                        // Select cold numbers
                        const coldNums = Array.from({length: 80}, (_, i) => i + 1)
                            .sort((a, b) => this.frequency[a] - this.frequency[b]);
                        numbersToSelect = coldNums.slice(0, 8);
                        break;
                }
                
                // Apply the selection
                for (const num of numbersToSelect) {
                    this.selectedNumbers.push(num);
                    const cell = document.querySelector(`.keno-number[data-number="${num}"]`);
                    if (cell) {
                        cell.classList.add('selected');
                    }
                }
                
                // Update displays
                this.updateNumberCounter();
                this.updatePatternIndicators();
                this.updateProbabilityDisplay();
                this.updateStrategyTips();
                this.updatePatternScores();
                this.updateProViewDisplay();
                this.updateNumberCoverageDisplay();
                
                // Show success message
                this.showNotification(`Applied ${strategy} betting strategy with ${numbersToSelect.length} numbers.`);
                
                // Highlight the active strategy
                this.strategyOptions.forEach(option => {
                    option.classList.toggle('active', option.dataset.strategy === strategy);
                });
            }
            
            // Export game data
            exportGameData() {
                const gameData = {
                    timestamp: new Date().toISOString(),
                    session: {
                        roundsPlayed: this.roundsPlayed,
                        roundsWon: this.roundsWon,
                        currentBalance: this.balance,
                        knowledgePoints: this.knowledgePoints
                    },
                    statistics: {
                        totalSpent: this.totalSpent,
                        totalWon: this.totalWon,
                        returnsPercentage: this.returnsPercentage,
                        pickEfficiency: this.pickEfficiency,
                        numbersHit: this.numbersHit
                    },
                    patterns: {
                        patternScore: this.patternScore,
                        sequenceScore: this.sequenceScore,
                        balanceScore: this.balanceScore,
                        clusterScore: this.clusterScore
                    },
                    gameHistory: this.lastResults,
                    numberFrequency: this.frequency.slice(1) // Skip index 0
                };
                
                // Create downloadable JSON file
                const dataStr = JSON.stringify(gameData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = 'keno-game-data.json';
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                this.showNotification('Game data exported successfully');
            }
            
            // Override showNotification for longer Pro View notifications
            showNotification(message) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    background: linear-gradient(45deg, var(--math-blue), var(--math-purple));
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    z-index: 10000;
                    font-weight: bold;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    animation: slideIn 0.3s ease;
                    max-width: 90%;
                    width: 350px;
                    max-height: 80vh;
                    overflow-y: auto;
                    white-space: pre-line;
                    font-size: 0.9rem;
                `;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                // Longer display time for pro view detailed messages
                const displayTime = message.length > 100 ? 8000 : 4000;
                
                setTimeout(() => {
                    notification.style.animation = 'slideIn 0.3s ease reverse';
                    setTimeout(() => notification.remove(), 300);
                }, displayTime);
            }
        }

        // Initialize the enhanced game when the DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log("DOM loaded, initializing Enhanced Keno game...");
            try {
                // If game already exists, replace it with enhanced version
                if (window.kenoGame) {
                    // Save current state if needed
                    const balance = window.kenoGame.balance;
                    const rounds = window.kenoGame.roundsPlayed;
                    
                    // Create enhanced game
                    window.kenoGame = new EnhancedKenoGame();
                    
                    // Restore state if needed
                    window.kenoGame.balance = balance;
                    window.kenoGame.roundsPlayed = rounds;
                    window.kenoGame.updateDisplay();
                } else {
                    window.kenoGame = new EnhancedKenoGame();
                }
                
                // Global functions for HTML onclick attributes
                window.closeResult = function() {
                    if (window.kenoGame) {
                        window.kenoGame.closeResult();
                    }
                };
                
                console.log("Enhanced Keno game initialized successfully!");
            } catch (error) {
                console.error("Error initializing Enhanced Keno game:", error);
            }
        });
    </script>
</body>
</html>