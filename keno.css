/* Keno Game CSS - Mobile First with Pro View */
:root {
    --math-blue: #1E88E5;
    --math-purple: #7B1FA2;
    --math-green: #43A047;
    --math-red: #E53935;
    --math-orange: #FB8C00;
    --math-teal: #009688;
    --math-yellow: #FDD835;
    --neutral-gray: #757575;
    --bg-gradient-start: #0D1930;
    --bg-gradient-end: #253555;
    --keno-grid-bg: rgba(255, 255, 255, 0.05);
    --pattern-blue: #2196F3;
    --pattern-gold: #FFC107;
    --pattern-purple: #9C27B0;
}

/* Base Container */
.keno-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    min-height: 100vh;
    position: relative;
    color: white;
    font-family: 'Poppins', sans-serif;
}

/* Game Header */
.game-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
}

.back-link {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
    align-self: flex-start;
    margin-bottom: 0.5rem;
}

.back-link:hover {
    color: var(--math-yellow);
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-size: 2rem;
    background: linear-gradient(45deg, var(--math-blue), var(--math-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
    text-align: center;
}

.game-subtitle {
    color: #fff;
    font-size: 0.9rem;
    opacity: 0.8;
    text-align: center;
}

.view-mode-toggle {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 0.75rem;
}

.view-mode-toggle button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: #fff;
    min-height: 36px;
}

.view-mode-toggle button.active {
    background: var(--math-yellow);
    color: #333;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Panel Styles */
.stats-panel, 
.strategy-panel,
.pro-view-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-title,
.pro-view-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    text-align: center;
    color: white;
    font-weight: 600;
}

.pro-view-title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pro-view-title i {
    margin-right: 0.5rem;
    color: var(--math-yellow);
}

/* Stat Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    text-align: center;
}

.stat-label {
    color: #fff;
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    opacity: 0.8;
}

.stat-value {
    color: var(--math-yellow);
    font-size: 1.2rem;
    font-weight: bold;
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.pro-stat-item {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.pro-stat-label {
    font-size: 0.75rem;
    color: #fff;
    margin-bottom: 0.3rem;
    opacity: 0.8;
}

.pro-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--math-yellow);
}

/* Keno Grid Container */
.keno-grid-container {
    background: rgba(30, 41, 59, 0.8);
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
}

.grid-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.betting-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bet-input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid var(--math-blue);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    font-size: 1rem;
    width: 80px;
    text-align: center;
    min-height: 44px;
}

.bet-label {
    font-size: 0.85rem;
    color: white;
}

.number-limit {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.limit-badge {
    background: var(--math-purple);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.quick-bet-chips {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.bet-chip {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 2px solid var(--math-yellow);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.bet-chip:hover {
    transform: scale(1.1);
}

.chip-10 { background: var(--math-blue); color: white; }
.chip-25 { background: var(--math-green); color: white; }
.chip-50 { background: var(--math-purple); color: white; }
.chip-100 { background: var(--math-red); color: white; }

/* Pattern Indicators */
.pattern-indicators {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.pattern-indicator {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    min-width: 120px;
}

.pattern-icon {
    font-size: 1.1rem;
    opacity: 0.7;
}

.pattern-active {
    background: var(--math-green);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Keno Grid */
.keno-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.4rem;
    margin-bottom: 1rem;
}

.keno-number {
    aspect-ratio: 1;
    background: var(--keno-grid-bg);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
    min-height: 44px;
}

.keno-number:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.keno-number.selected {
    background: var(--math-blue);
    color: white;
    border-color: white;
    animation: pulse 2s infinite;
}

.keno-number.drawn {
    background: var(--math-green);
    color: white;
    transform: scale(1.05);
}

.keno-number.matched {
    background: var(--math-orange);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 0 15px var(--math-orange);
    border-color: white;
}

.keno-number .heat-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--math-blue), var(--math-red));
    transform: scaleX(0.5);
    transform-origin: left;
}

.keno-number .frequency {
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 0.6rem;
    opacity: 0.8;
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.control-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 44px;
    min-height: 44px;
}

.btn-play {
    background: linear-gradient(45deg, var(--math-green), #66BB6A);
    color: white;
}

.btn-clear {
    background: linear-gradient(45deg, var(--neutral-gray), #9E9E9E);
    color: white;
}

.btn-auto {
    background: linear-gradient(45deg, var(--math-purple), #9C27B0);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, var(--math-blue), #42A5F5);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Probability Display */
.probability-display {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.prob-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.prob-card {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.prob-label {
    font-size: 0.75rem;
    margin-bottom: 0.3rem;
    opacity: 0.8;
}

.prob-value {
    font-size: 1rem;
    font-weight: bold;
    color: var(--math-yellow);
}

.probability-meter {
    height: 16px;
    background: linear-gradient(90deg, var(--math-red) 0%, var(--math-orange) 50%, var(--math-green) 100%);
    border-radius: 8px;
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
}

.probability-indicator {
    position: absolute;
    top: -3px;
    width: 3px;
    height: 22px;
    background: white;
    border-radius: 2px;
    transition: left 0.3s ease;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Strategy Coach */
.strategy-coach {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.coach-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: white;
}

.coach-recommendation {
    font-size: 0.85rem;
    line-height: 1.4;
    opacity: 0.9;
}

/* Historical Data */
.historical-data {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
}

.history-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: white;
}

.history-numbers {
    display: flex;
    flex-wrap: wrap;
    gap: 0.4rem;
    margin-bottom: 0.5rem;
}

.history-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--math-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Paytable */
.paytable-container {
    margin-top: 1rem;
}

.paytable {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.8rem;
}

.paytable th, .paytable td {
    padding: 0.4rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.paytable th {
    background: rgba(0, 0, 0, 0.3);
    color: var(--math-yellow);
}

.paytable tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.05);
}

/* Tooltip */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    font-size: 0.8rem;
    max-width: 200px;
    z-index: 100;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Draw Animation */
.draw-animation {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-radius: 15px;
    display: none;
}

.draw-title {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.ball-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    max-width: 280px;
    margin-bottom: 1.5rem;
}

.drawn-ball {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--math-yellow), var(--math-orange));
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 700;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    opacity: 0;
    transform: scale(0);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Result Modal */
.result-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    border: 2px solid var(--math-blue);
    text-align: center;
    z-index: 1000;
    display: none;
    min-width: 280px;
    max-width: 90vw;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
}

.achievement-badge {
    background: var(--math-yellow);
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    margin: 0.4rem;
    display: inline-block;
}

/* Pro View Specific Styles */
.pro-view-stats {
    display: none;
}

.pro-view-active .pro-view-stats {
    display: block;
    margin-bottom: 1rem;
}

.pro-view-sections {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pro-section {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
}

.pro-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.pro-section-title i {
    margin-right: 0.5rem;
    color: var(--math-yellow);
}

.pro-pattern-analysis .pattern-score {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.2);
}

.pattern-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.pattern-value {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--math-yellow);
}

.chart-container {
    margin: 0.75rem 0;
}

.chart-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
}

.chart-label {
    width: 60px;
    font-size: 0.75rem;
    opacity: 0.8;
}

.chart-fill {
    flex: 1;
    height: 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
    margin: 0 0.5rem;
    overflow: hidden;
}

.chart-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--math-green), var(--math-yellow));
    transition: width 0.3s ease;
}

.chart-value {
    font-size: 0.75rem;
    color: var(--math-yellow);
    font-weight: 600;
    min-width: 35px;
    text-align: right;
}

.number-coverage-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 2px;
}

.coverage-cell {
    width: 100%;
    aspect-ratio: 1;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 2px;
}

.coverage-cell.hot {
    background: rgba(229, 57, 53, 0.5); /* math-red */
}

.coverage-cell.cold {
    background: rgba(30, 136, 229, 0.5); /* math-blue */
}

.coverage-cell.selected {
    border: 1px solid white;
}

.game-history {
    max-height: 120px;
    overflow-y: auto;
    margin-top: 0.5rem;
}

.history-entry {
    display: flex;
    justify-content: space-between;
    padding: 0.4rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 0.8rem;
}

.history-entry:last-child {
    border-bottom: none;
}

.history-detail {
    opacity: 0.8;
}

.history-result {
    font-weight: 600;
}

.history-win {
    color: var(--math-green);
}

.history-loss {
    color: var(--math-red);
}

.pro-controls {
    display: none;
    margin-top: 1rem;
}

.pro-view-active .pro-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pro-btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    background: rgba(255,255,255,0.1);
    color: #fff;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    min-width: 100px;
}

.pro-btn:hover {
    background: rgba(255,255,255,0.2);
}

.pro-btn i {
    margin-right: 0.25rem;
}

/* Enhanced betting section for Pro View */
.pro-betting-section {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.pro-betting-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-align: center;
}

.betting-strategies {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.strategy-option {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.strategy-option:hover {
    background: rgba(0, 0, 0, 0.3);
}

.strategy-option.active {
    background: var(--math-blue);
}

.strategy-name {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.strategy-desc {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* Pattern Simulator */
.pattern-simulator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.simulator-control {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.simulator-label {
    font-size: 0.75rem;
    opacity: 0.8;
}

.simulator-value {
    font-weight: 600;
    color: var(--math-yellow);
}

.simulator-btn {
    background: var(--math-blue);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    min-height: 40px;
}

/* Responsive Styles */
@media (min-width: 768px) {
    .keno-container {
        padding: 1.5rem;
    }
    
    .game-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .game-title {
        font-size: 2.5rem;
        margin: 0 auto;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .view-mode-toggle {
        margin-top: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 1.5rem;
    }
    
    .grid-controls {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .keno-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: 0.5rem;
    }
    
    .keno-number {
        font-size: 1rem;
    }
    
    .ball-container {
        max-width: 500px;
    }
    
    .drawn-ball {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .pro-view-sections {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .pro-section {
        flex: 1;
        min-width: 250px;
    }
}

@media (min-width: 1024px) {
    .keno-container {
        padding: 2rem;
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .keno-grid {
        grid-template-columns: repeat(10, 1fr);
    }
    
    .stats-grid,
    .pro-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .pro-controls {
        justify-content: center;
    }
    
    .pro-btn {
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .keno-container {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-subtitle {
        font-size: 0.8rem;
    }
    
    .keno-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.3rem;
    }
    
    .keno-number {
        font-size: 0.8rem;
    }
    
    .probability-indicator {
        width: 2px;
        height: 20px;
    }
    
    .pattern-indicators {
        gap: 0.3rem;
    }
    
    .pattern-indicator {
        padding: 0.4rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .control-btn {
        padding: 0.6rem 0.8rem;
        font-size: 0.8rem;
    }
    
    .ball-container {
        gap: 0.5rem;
        max-width: 250px;
    }
    
    .drawn-ball {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .stats-grid,
    .pro-stats-grid,
    .prob-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.4rem;
    }
    
    .stat-card,
    .pro-stat-item,
    .prob-card {
        padding: 0.5rem;
    }
    
    .stat-value,
    .pro-stat-value,
    .prob-value {
        font-size: 1rem;
    }
    
    .paytable th,
    .paytable td {
        padding: 0.3rem;
        font-size: 0.7rem;
    }
    
    .chart-label {
        width: 50px;
        font-size: 0.7rem;
    }
    
    .chart-value {
        font-size: 0.7rem;
        min-width: 30px;
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .control-btn,
    .keno-number,
    .bet-input,
    .pro-btn,
    .simulator-btn {
        min-height: 48px;
    }
    
    .view-mode-toggle button {
        min-height: 40px;
        padding: 0.5rem 0.75rem;
    }
    
    .bet-chip {
        min-height: 48px;
        min-width: 48px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .keno-grid-container {
        background: rgba(20, 30, 45, 0.8);
    }
    
    .stats-panel, 
    .strategy-panel,
    .pro-view-stats {
        background: rgba(20, 20, 30, 0.7);
    }
    
    .stat-card,
    .pro-stat-item,
    .probability-display,
    .strategy-coach,
    .historical-data,
    .pro-section {
        background: rgba(30, 30, 40, 0.5);
    }
    
    .prob-card,
    .pattern-score,
    .strategy-option {
        background: rgba(10, 10, 20, 0.4);
    }
    
    .pattern-indicator {
        background: rgba(30, 30, 40, 0.5);
    }
}