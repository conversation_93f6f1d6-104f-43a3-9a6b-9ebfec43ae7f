/* Mobile-First Responsive Design for Cases Game */

:root {
    /* Color Scheme */
    --treasure-gold: #FFD700;
    --treasure-bronze: #CD7F32;
    --treasure-dark-gold: #B8860B;
    --adventure-blue: #1E90FF;
    --adventure-dark-blue: #1873CC;
    --success-green: #32CD32;
    --success-dark-green: #228B22;
    --neutral-gray: #708090;
    --timer-red: #FF4500;
    --timer-orange: #FF6347;
    --mystical-purple: #8A2BE2;
    --mystical-indigo: #4B0082;
    
    /* Gradients */
    --background-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a3a 50%, #2a2a4a 100%);
    --card-gradient: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    --treasure-gradient: linear-gradient(135deg, var(--treasure-gold), var(--treasure-bronze));
    --adventure-gradient: linear-gradient(45deg, var(--adventure-blue), var(--adventure-dark-blue));
    --success-gradient: linear-gradient(45deg, var(--success-green), var(--success-dark-green));
    
    /* Z-index hierarchy */
    --z-background: 1;
    --z-cases: 10;
    --z-modal: 1000;
    --z-notification: 2000;
    --z-floating: 3000;
}

/* Global Mobile-First Styles */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    background: var(--background-gradient);
    color: white;
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile Container */
.cases-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* Animated Background */
.treasure-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.05;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="30">💎</text></svg>');
    animation: float 20s infinite linear;
    pointer-events: none;
    z-index: var(--z-background);
}

@keyframes float {
    0% { transform: translateY(100vh) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

/* Game Header - Mobile First */
.game-header {
    text-align: center;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: var(--z-background);
}

.game-title {
    font-size: 2rem;
    background: var(--treasure-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.game-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.4;
}

/* Mobile Dashboard Grid */
.game-dashboard {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
    background: var(--card-gradient);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile Stats Panel */
.stats-panel {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
    touch-action: manipulation;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: var(--treasure-gold);
    font-size: 1.2rem;
    font-weight: bold;
}

/* Mobile Timer Display */
.timer-display {
    text-align: center;
    position: relative;
    margin: 1rem 0;
}

.timer-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(var(--adventure-blue) 0deg, transparent 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    transition: background 0.1s ease;
}

.timer-inner {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.1rem;
    font-weight: bold;
}

.timer-warning {
    background: conic-gradient(var(--timer-red) 0deg, transparent 0deg);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Mobile Power-ups */
.power-ups {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
}

.power-up {
    background: var(--adventure-gradient);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    touch-action: manipulation;
    font-size: 0.9rem;
    min-height: 50px;
}

.power-up:hover, .power-up:active {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(30, 144, 255, 0.4);
}

.power-up:disabled {
    background: var(--neutral-gray);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
}

.power-up-cooldown {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.5);
    transition: width 0.1s ease;
    z-index: 1;
}

/* Mobile Cases Grid */
.cases-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin: 1.5rem 0;
    max-width: 100%;
}

.case {
    aspect-ratio: 1;
    background: var(--treasure-gradient);
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 2px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    overflow: hidden;
    touch-action: manipulation;
    min-height: 60px;
}

.case::before {
    content: '📦';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
    transition: all 0.3s ease;
    z-index: 1;
}

.case:hover, .case:active {
    transform: translateY(-3px) rotateY(5deg);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
    border-color: var(--treasure-gold);
}

.case.opened {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--success-green);
    transform: rotateY(180deg);
}

.case.opened::before {
    opacity: 0;
    z-index: -1;
}

.case-content {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease 0.2s;
    z-index: 2;
    font-size: 0.7rem;
}

.case.opened .case-content {
    opacity: 1;
}

.case-value {
    font-size: 0.6rem;
    margin-top: 0.1rem;
}

.case.spotlight {
    animation: highlight 2s ease;
    border-color: var(--adventure-blue);
}

@keyframes highlight {
    0%, 100% { box-shadow: 0 0 15px rgba(30, 144, 255, 0.5); }
    50% { box-shadow: 0 0 30px rgba(30, 144, 255, 0.8); }
}

/* Mobile Puzzle Tracker */
.puzzle-tracker {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin: 1rem 0;
}

.puzzle-piece {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.puzzle-piece.collected {
    background: var(--success-gradient);
    border-color: var(--success-green);
    transform: scale(1.1);
    animation: collectPulse 0.5s ease;
}

@keyframes collectPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1.1); }
}

/* Mobile Streak Indicator */
.streak-indicator {
    text-align: center;
    margin: 1rem 0;
    font-size: 1rem;
    color: var(--treasure-gold);
    font-weight: bold;
    padding: 0.5rem;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

/* Mobile Bet Controls */
.bet-controls {
    margin-bottom: 1rem;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bet-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    touch-action: manipulation;
    min-height: 44px;
    min-width: 60px;
}

.bet-btn:hover, .bet-btn:active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Mobile Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    touch-action: manipulation;
    min-height: 50px;
}

.btn-primary {
    background: var(--adventure-gradient);
    color: white;
}

.btn-primary:hover, .btn-primary:active {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(30, 144, 255, 0.4);
}

.btn-secondary {
    background: var(--neutral-gray);
    color: white;
}

.btn-secondary:hover, .btn-secondary:active {
    background: #5a6a7a;
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Pro View Toggle */
.view-mode-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: var(--z-notification);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 25px;
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.view-toggle-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    font-weight: 500;
    touch-action: manipulation;
}

.view-toggle-btn.active {
    background: var(--treasure-gold);
    color: var(--mystical-indigo);
}

/* Pro View Enhancements */
.pro-view-active .advanced-stats {
    display: block !important;
}

.pro-view-active .detailed-analytics {
    display: block !important;
}

.pro-view-active .strategy-hints {
    display: block !important;
}

.pro-view-active .game-dashboard {
    grid-template-columns: 1fr;
    gap: 1rem;
}

/* Standard view hides pro features on mobile */
.standard-view .advanced-stats,
.standard-view .detailed-analytics,
.standard-view .strategy-hints {
    display: none;
}

.advanced-stats,
.detailed-analytics,
.strategy-hints {
    display: none;
}

/* Advanced Stats Panel (Pro View) */
.advanced-stats {
    background: rgba(138, 43, 226, 0.1);
    border: 1px solid rgba(138, 43, 226, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.advanced-stats .panel-title {
    color: var(--mystical-purple);
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.advanced-stats .stat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.advanced-stats .stat-card {
    background: rgba(138, 43, 226, 0.15);
    border-color: rgba(138, 43, 226, 0.3);
    padding: 0.5rem;
}

/* Strategy Hints Panel (Pro View) */
.strategy-hints {
    background: rgba(30, 144, 255, 0.1);
    border: 1px solid rgba(30, 144, 255, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

.strategy-hints .panel-title {
    color: var(--adventure-blue);
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
}

.hint-item {
    background: rgba(30, 144, 255, 0.15);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Modal Dialogs */
.round-complete {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--treasure-gradient);
    color: #000;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    z-index: var(--z-modal);
    display: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    max-width: 90vw;
    width: 300px;
}

.round-complete h2 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
}

.round-complete p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Particle Effects */
.particle-effect {
    position: absolute;
    pointer-events: none;
    font-size: 1.2rem;
    animation: particle-float 2s ease-out forwards;
    z-index: var(--z-floating);
}

@keyframes particle-float {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-80px) scale(0.5) rotate(360deg);
    }
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    background: var(--treasure-gradient);
    color: #000;
    padding: 1rem;
    border-radius: 8px;
    z-index: var(--z-notification);
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateY(-100px);
    transition: transform 0.3s ease;
    text-align: center;
    font-size: 0.9rem;
}

.notification.show {
    transform: translateY(0);
}

/* Tablet Styles */
@media (min-width: 768px) {
    .cases-container {
        padding: 1.5rem;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .game-dashboard {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }
    
    .stats-panel {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .timer-circle {
        width: 100px;
        height: 100px;
    }
    
    .timer-inner {
        width: 75px;
        height: 75px;
        font-size: 1.3rem;
    }
    
    .power-ups {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .cases-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 0.75rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .case {
        font-size: 1.75rem;
        border-radius: 10px;
    }
    
    .case::before {
        font-size: 1.75rem;
    }
    
    .case-content {
        font-size: 0.8rem;
    }
    
    .case-value {
        font-size: 0.7rem;
    }
    
    .puzzle-piece {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
    
    .streak-indicator {
        font-size: 1.1rem;
    }
    
    .notification {
        left: auto;
        right: 20px;
        max-width: 350px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .cases-container {
        max-width: 1200px;
        padding: 2rem;
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .game-subtitle {
        font-size: 1.2rem;
    }
    
    .game-dashboard {
        gap: 2rem;
        padding: 2rem;
    }
    
    .timer-circle {
        width: 120px;
        height: 120px;
    }
    
    .timer-inner {
        width: 90px;
        height: 90px;
        font-size: 1.5rem;
    }
    
    .cases-grid {
        gap: 1rem;
        max-width: 800px;
    }
    
    .case {
        font-size: 2rem;
        border-radius: 12px;
    }
    
    .case::before {
        font-size: 2rem;
    }
    
    .case-content {
        font-size: 0.9rem;
    }
    
    .case-value {
        font-size: 0.8rem;
    }
    
    .puzzle-piece {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .streak-indicator {
        font-size: 1.2rem;
    }
    
    /* Show advanced features on desktop */
    .standard-view .advanced-stats,
    .standard-view .detailed-analytics {
        display: block;
    }
}

/* Touch and Interaction Enhancements */
@media (hover: none) and (pointer: coarse) {
    /* Mobile touch enhancements */
    .case,
    .power-up,
    .btn,
    .bet-btn,
    .view-toggle-btn {
        min-height: 44px; /* Minimum touch target size */
        min-width: 44px;
    }
    
    /* Increase touch feedback */
    .case:active,
    .power-up:active,
    .btn:active,
    .bet-btn:active {
        transform: scale(0.95);
    }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .case {
        box-shadow: 0 6px 20px rgba(255, 215, 0, 0.3);
    }
    
    .notification {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .treasure-background {
        animation: none;
    }
    
    .timer-warning {
        animation: none;
    }
    
    .case.spotlight {
        animation: none;
    }
    
    .particle-effect {
        animation: none;
    }
    
    * {
        transition: none !important;
    }
}

/* Dark mode support (if system prefers dark) */
@media (prefers-color-scheme: dark) {
    /* Already dark themed, but can add adjustments */
}

/* Print styles */
@media print {
    .cases-container {
        background: white;
        color: black;
    }
    
    .notification,
    .view-mode-toggle,
    .treasure-background {
        display: none;
    }
}