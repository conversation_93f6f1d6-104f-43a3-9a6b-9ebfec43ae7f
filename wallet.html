<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/wallet.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
     <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                    <span class="logo-text">GoldenAura</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-balance" style="display: none;">
                    <span class="balance-label">Balance:</span>
                    <span class="balance-amount" data-balance>0.00 GA</span>
                </div>
                <div class="user-profile" style="display: none;" data-user-element="profile">
                    <span class="user-email" data-user-element="email"></span>
                    <button class="btn btn-logout" data-auth-action="logout" style="display: none;">Logout</button>
                </div>
                <div class="live-support-indicator">
                    <div class="support-dot"></div>
                    <span>Live Support</span>
                </div>
                <button class="btn btn-login" onclick="showSimpleLogin()">Login</button>
                <button class="btn btn-register" onclick="window.open('https://t.me/Goldenaura_PY_bot?start=register', '_blank')">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="sports.html" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item active">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <section class="wallet-page">
            <div class="wallet-header">
                <h1 class="wallet-title">Your Wallet</h1>
            </div>
            
            <!-- Balance Card -->
            <div class="balance-card">
                <div class="balance-header">
                    <span class="balance-label">Current Balance</span>
                    <button class="balance-visibility" id="toggleBalance" aria-label="Toggle balance visibility">
                        <i class="fas fa-eye-slash"></i>
                    </button>
                </div>
                <div class="balance-amount" id="balanceAmount">50,000 GA</div>
                <div class="balance-usd">≈ $5,000 USD</div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="quick-action" id="addFunds">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="quick-action-text">Add Funds</div>
                </div>
                
                <div class="quick-action" id="sendMoney">
                    <div class="quick-action-icon">
                        <i class="fas fa-paper-plane"></i>
                    </div>
                    <div class="quick-action-text">Send</div>
                </div>
                
                <div class="quick-action" id="receiveMoney">
                    <div class="quick-action-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="quick-action-text">Receive</div>
                </div>
                
                <div class="quick-action" id="withdrawFunds">
                    <div class="quick-action-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="quick-action-text">Withdraw</div>
                </div>
                
                <div class="quick-action" id="telegramBot">
                    <div class="quick-action-icon">
                        <i class="fab fa-telegram"></i>
                    </div>
                    <div class="quick-action-text">Wallet Support</div>
                </div>
            </div>
            
            <!-- Currency Selector -->
            <div class="currency-selector">
                <h2 class="currency-selector-header">Your Currencies</h2>
                <div class="currency-list">
                    <div class="currency-item active">
                        <div class="currency-icon">GA</div>
                        <div class="currency-info">
                            <div class="currency-name">GoldenAura</div>
                            <div class="currency-balance">50,000 GA</div>
                        </div>
                    </div>
                    
                    <div class="currency-item">
                        <div class="currency-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="currency-info">
                            <div class="currency-name">USD</div>
                            <div class="currency-balance">$5,000</div>
                        </div>
                    </div>
                    
                    <div class="currency-item">
                        <div class="currency-icon">
                            <i class="fas fa-euro-sign"></i>
                        </div>
                        <div class="currency-info">
                            <div class="currency-name">EUR</div>
                            <div class="currency-balance">€4,500</div>
                        </div>
                    </div>
                    
                    <div class="currency-item coming-soon">
                        <div class="currency-icon">
                            <i class="fab fa-bitcoin"></i>
                        </div>
                        <div class="currency-info">
                            <div class="currency-name">Bitcoin</div>
                            <div class="currency-balance">0.0 BTC</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notifications -->
            <div class="notifications-section">
                <div class="notifications-header">
                    <h2 class="notifications-title">Notifications</h2>
                    <button class="clear-notifications">Clear All</button>
                </div>
                
                <div class="notification success">
                    <div class="notification-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">Deposit Successful</div>
                        <div class="notification-text">Your deposit of 5,000 GA has been completed successfully.</div>
                    </div>
                    <div class="notification-time">1 hour ago</div>
                </div>
                
                <div class="notification warning">
                    <div class="notification-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">Enable 2FA</div>
                        <div class="notification-text">Enhance your account security by enabling two-factor authentication.</div>
                    </div>
                    <div class="notification-time">2 days ago</div>
                </div>
                
                <div class="notification">
                    <div class="notification-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">Bonus Credited</div>
                        <div class="notification-text">A welcome bonus of 1,000 GA has been credited to your account.</div>
                    </div>
                    <div class="notification-time">3 days ago</div>
                </div>
            </div>
            
            <!-- Transaction History -->
            <div class="transaction-history">
                <div class="transaction-header">
                    <h2 class="transaction-title">Transaction History</h2>
                    <div class="transaction-filters">
                        <select class="filter-select" id="typeFilter">
                            <option>All Types</option>
                            <option>Deposits</option>
                            <option>Withdrawals</option>
                            <option>Transfers</option>
                            <option>Bets</option>
                            <option>Winnings</option>
                        </select>
                        
                        <select class="filter-select" id="timeFilter">
                            <option>All Time</option>
                            <option>Last 24 Hours</option>
                            <option>Last 7 Days</option>
                            <option>Last 30 Days</option>
                            <option>Custom Range</option>
                        </select>
                        
                        <select class="filter-select" id="statusFilter">
                            <option>All Status</option>
                            <option>Completed</option>
                            <option>Pending</option>
                            <option>Failed</option>
                        </select>
                    </div>
                </div>
                
                <div class="transaction-search">
                    <input type="text" class="transaction-search-input" placeholder="Search transactions...">
                    <i class="fas fa-search transaction-search-icon"></i>
                </div>
                
                <div class="transaction-list">
                    <div class="transaction-item">
                        <div class="transaction-left">
                            <div class="transaction-icon incoming">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Deposit</div>
                                <div class="transaction-desc">Credit Card</div>
                                <div class="transaction-date">June 4, 2025 • 10:15 AM</div>
                            </div>
                        </div>
                        <div class="transaction-right">
                            <div class="transaction-amount incoming">+5,000 GA</div>
                            <div class="transaction-status completed">Completed</div>
                        </div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-left">
                            <div class="transaction-icon outgoing">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Bet Placed</div>
                                <div class="transaction-desc">Crash Game</div>
                                <div class="transaction-date">June 3, 2025 • 8:30 PM</div>
                            </div>
                        </div>
                        <div class="transaction-right">
                            <div class="transaction-amount outgoing">-1,000 GA</div>
                            <div class="transaction-status completed">Completed</div>
                        </div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-left">
                            <div class="transaction-icon incoming">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Bet Won</div>
                                <div class="transaction-desc">Crash Game</div>
                                <div class="transaction-date">June 3, 2025 • 8:30 PM</div>
                            </div>
                        </div>
                        <div class="transaction-right">
                            <div class="transaction-amount incoming">+2,500 GA</div>
                            <div class="transaction-status completed">Completed</div>
                        </div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-left">
                            <div class="transaction-icon outgoing">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Transfer</div>
                                <div class="transaction-desc">To: JohnDoe123</div>
                                <div class="transaction-date">June 2, 2025 • 3:45 PM</div>
                            </div>
                        </div>
                        <div class="transaction-right">
                            <div class="transaction-amount outgoing">-500 GA</div>
                            <div class="transaction-status completed">Completed</div>
                        </div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-left">
                            <div class="transaction-icon incoming">
                                <i class="fas fa-gift"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Bonus</div>
                                <div class="transaction-desc">Welcome Bonus</div>
                                <div class="transaction-date">June 1, 2025 • 9:00 AM</div>
                            </div>
                        </div>
                        <div class="transaction-right">
                            <div class="transaction-amount incoming">+1,000 GA</div>
                            <div class="transaction-status completed">Completed</div>
                        </div>
                    </div>
                    
                    <div class="transaction-item">
                        <div class="transaction-left">
                            <div class="transaction-icon outgoing">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="transaction-details">
                                <div class="transaction-type">Withdrawal</div>
                                <div class="transaction-desc">To Bank Account</div>
                                <div class="transaction-date">May 28, 2025 • 2:20 PM</div>
                            </div>
                        </div>
                        <div class="transaction-right">
                            <div class="transaction-amount outgoing">-2,000 GA</div>
                            <div class="transaction-status pending">Pending</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment Methods -->
            <div class="payment-methods">
                <div class="payment-methods-header">
                    <h2 class="payment-methods-title">Payment Methods</h2>
                    <button class="add-payment-method">+ Add via Telegram</button>
                </div>
                
                <div class="payment-method">
                    <div class="payment-method-left">
                        <div class="payment-method-icon">
                            <i class="far fa-credit-card"></i>
                        </div>
                        <div class="payment-method-info">
                            <div class="payment-method-name">Visa Card</div>
                            <div class="payment-method-details">•••• •••• •••• 4567 | Expires 09/26</div>
                        </div>
                    </div>
                    <div class="payment-method-actions">
                        <button class="payment-method-btn">Set as Default</button>
                        <button class="payment-method-btn remove">Remove</button>
                    </div>
                </div>
                
                <div class="payment-method">
                    <div class="payment-method-left">
                        <div class="payment-method-icon">
                            <i class="fab fa-paypal"></i>
                        </div>
                        <div class="payment-method-info">
                            <div class="payment-method-name">PayPal</div>
                            <div class="payment-method-details"><EMAIL></div>
                        </div>
                    </div>
                    <div class="payment-method-actions">
                        <button class="payment-method-btn">Set as Default</button>
                        <button class="payment-method-btn remove">Remove</button>
                    </div>
                </div>
                
                <div class="payment-method">
                    <div class="payment-method-left">
                        <div class="payment-method-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div class="payment-method-info">
                            <div class="payment-method-name">Bank Account</div>
                            <div class="payment-method-details">•••• 5678 | Chase Bank</div>
                        </div>
                    </div>
                    <div class="payment-method-actions">
                        <button class="payment-method-btn">Set as Default</button>
                        <button class="payment-method-btn remove">Remove</button>
                    </div>
                </div>
            </div>
            
            <!-- Security Section -->
            <div class="security-section">
                <h2 class="security-title">Security Settings</h2>
                
                <div class="security-item">
                    <div class="security-item-left">
                        <div class="security-item-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="security-item-info">
                            <div class="security-item-name">Two-Factor Authentication</div>
                            <div class="security-item-desc">Add an extra layer of security to your account.</div>
                        </div>
                    </div>
                    <div class="security-toggle" id="2faToggle"></div>
                </div>
                
                <div class="security-item">
                    <div class="security-item-left">
                        <div class="security-item-icon">
                            <i class="fas fa-fingerprint"></i>
                        </div>
                        <div class="security-item-info">
                            <div class="security-item-name">Biometric Authentication</div>
                            <div class="security-item-desc">Use fingerprint or face ID to log in.</div>
                        </div>
                    </div>
                    <div class="security-toggle active" id="bioToggle"></div>
                </div>
                
                <div class="security-item">
                    <div class="security-item-left">
                        <div class="security-item-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="security-item-info">
                            <div class="security-item-name">Transaction PIN</div>
                            <div class="security-item-desc">Require PIN for all transactions.</div>
                        </div>
                    </div>
                    <div class="security-toggle active" id="pinToggle"></div>
                </div>
                
                <div class="security-item">
                    <div class="security-item-left">
                        <div class="security-item-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="security-item-info">
                            <div class="security-item-name">Transaction Notifications</div>
                            <div class="security-item-desc">Get notified for all wallet activities.</div>
                        </div>
                    </div>
                    <div class="security-toggle active" id="notifToggle"></div>
                </div>
            </div>
        </section>
    </main>

    <script src="assets/js/script.js"></script>
    
    <!-- Load API integrations -->
    <script src="config.js"></script>
    <script src="api-service.js"></script>
    <script src="mock-api.js"></script>
    <script src="assets/js/wallet.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Telegram Bot URL
            const telegramBotURL = 'https://t.me/Goldenaura_PY_bot';
            
            // Function to redirect to Telegram bot with message
            function redirectToTelegramBot(action) {
                const messages = {
                    'deposit': 'Hi! I want to deposit funds to my GoldenAura account.',
                    'send': 'Hi! I want to send money from my GoldenAura account.',
                    'receive': 'Hi! I want to receive money to my GoldenAura account.',
                    'withdraw': 'Hi! I want to withdraw funds from my GoldenAura account.',
                    'general': 'Hi! I need help with my GoldenAura wallet.'
                };
                
                const message = encodeURIComponent(messages[action] || messages['general']);
                window.open(`${telegramBotURL}?start=${action}&text=${message}`, '_blank');
            }
            
            // Update all wallet action buttons to redirect to Telegram bot
            document.getElementById('addFunds').addEventListener('click', function() {
                redirectToTelegramBot('deposit');
            });
            
            document.getElementById('sendMoney').addEventListener('click', function() {
                redirectToTelegramBot('send');
            });
            
            document.getElementById('receiveMoney').addEventListener('click', function() {
                redirectToTelegramBot('receive');
            });
            
            document.getElementById('withdrawFunds').addEventListener('click', function() {
                redirectToTelegramBot('withdraw');
            });
            
            document.getElementById('telegramBot').addEventListener('click', function() {
                redirectToTelegramBot('general');
            });
            
            // Update "Add New" payment method button
            document.querySelector('.add-payment-method').addEventListener('click', function() {
                redirectToTelegramBot('general');
            });
        });
    </script>
</body>
</html>