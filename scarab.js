// Scarab Spin - Ancient Egyptian Archaeology Game - Mobile Enhanced with Pro View
document.addEventListener('DOMContentLoaded', function() {
    console.log("Scarab Spin Mobile Enhanced initializing...");
    
    // Game elements
    const spinBtn = document.getElementById('spinBtn');
    const excavateBtn = document.getElementById('excavateBtn');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const reels = document.getElementById('reels');
    const holdBtns = document.querySelectorAll('.hold-btn');
    const symbolContainers = document.querySelectorAll('.symbol-container');
    const archaeologyFill = document.getElementById('archaeologyFill');
    const archaeologyText = document.getElementById('archaeologyText');
    const patternList = document.getElementById('patternList');
    const multiplierValue = document.getElementById('multiplierValue');
    const totalScore = document.getElementById('totalScore');
    const lastSpinScore = document.getElementById('lastSpinScore');
    const bestPattern = document.getElementById('bestPattern');
    const spinCount = document.getElementById('spinCount');
    const knowledgePoints = document.getElementById('knowledgePoints');
    const puzzlePieces = document.querySelectorAll('.puzzle-piece');
    const solvePuzzleBtn = document.getElementById('solvePuzzleBtn');
    const puzzleCount = document.getElementById('puzzleCount');
    const heatmapToggle = document.getElementById('heatmapToggle');
    const sandParticles = document.getElementById('sandParticles');
    
    // Pro View elements
    const viewModeToggle = document.getElementById('viewModeToggle');
    const viewModeText = document.getElementById('viewModeText');
    const winRate = document.getElementById('winRate');
    const avgMultiplier = document.getElementById('avgMultiplier');
    const patternEfficiency = document.getElementById('patternEfficiency');
    const totalSpins = document.getElementById('totalSpins');
    const totalWins = document.getElementById('totalWins');
    const biggestWin = document.getElementById('biggestWin');
    const streakCount = document.getElementById('streakCount');
    const sessionScore = document.getElementById('sessionScore');
    const expectedValue = document.getElementById('expectedValue');
    const personalRTP = document.getElementById('personalRTP');
    const avgScorePerSpin = document.getElementById('avgScorePerSpin');
    const successRate = document.getElementById('successRate');
    const learningProgress = document.getElementById('learningProgress');
    const efficiency = document.getElementById('efficiency');
    const strategyTip = document.getElementById('strategyTip');
    const modulesCompleted = document.getElementById('modulesCompleted');
    const nextReward = document.getElementById('nextReward');
    
    // Modal elements
    const tutorialBtn = document.getElementById('tutorialBtn');
    const tutorialModal = document.getElementById('tutorialModal');
    const closeTutorialBtn = document.getElementById('closeTutorialBtn');
    const educationalModal = document.getElementById('educationalModal');
    const closeEducationalBtn = document.getElementById('closeEducationalBtn');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const puzzleModal = document.getElementById('puzzleModal');
    const closePuzzleBtn = document.getElementById('closePuzzleBtn');
    const puzzleGame = document.getElementById('puzzleGame');
    
    // Game state
    let gameState = {
        // Player data
        score: 0,
        lastWin: 0,
        spinsToday: 0,
        knowledge: 0,
        archaeologyProgress: 0,
        piecesCollected: 0,
        
        // Game mechanics
        currentPatterns: [],
        totalMultiplier: 1.0,
        bestPatternFound: "None",
        excavationMode: false,
        heldReels: new Set(),
        
        // Pro View mode
        viewMode: 'standard', // 'standard' or 'pro'
        
        // Performance tracking
        totalSpinsCount: 0,
        totalWinsCount: 0,
        totalPointsEarned: 0,
        biggestWinAmount: 0,
        currentStreak: 0,
        winHistory: [],
        patternHistory: [],
        avgMultiplierSum: 0,
        
        // Learning tracking
        modulesCompletedCount: 0,
        difficultyLevel: "NOVICE", // NOVICE, HISTORIAN
        
        // Upgrades
        upgradesUnlocked: {
            hieroglyphDecoder: false, // +1 held reel
            probabilityLens: false,   // See hidden odds
            patternMastery: false    // +0.5x multipliers
        }
    };
    
    // Symbol definitions with probabilities
    const symbols = [
        { id: "scarab", name: "Scarab", icon: "🪲", weight: 45, value: 10 },
        { id: "hieroglyph", name: "Hieroglyph", icon: "𓂀", weight: 30, value: 25 },
        { id: "pyramid", name: "Pyramid", icon: "🏔️", weight: 15, value: 50 },
        { id: "ankh", name: "Ankh", icon: "☥", weight: 8, value: 0, multiplier: 2 },
        { id: "sphinx", name: "Sphinx", icon: "🦅", weight: 2, value: 100, puzzle: true }
    ];
    
    // Mobile-specific configuration
    const mobileConfig = {
        enableTouch: true,
        hapticFeedback: 'ontouchstart' in window,
        doubleClickSpeed: 300,
        longPressDelay: 500,
        swipeThreshold: 50
    };
    
    // Touch handling
    let touchState = {
        startX: 0,
        startY: 0,
        startTime: 0,
        isLongPress: false,
        longPressTimer: null
    };
    
    // Educational content
    const symbolLore = {
        scarab: {
            title: "The Sacred Scarab",
            content: `<p>The scarab beetle was one of the most important symbols in ancient Egypt, representing the sun god Ra and the cycle of rebirth and regeneration.</p>
                     <p>The Egyptian word for scarab, <em>kheper</em>, also means "to come into being" or "to transform." Egyptians observed how scarab beetles roll balls of dung across the ground, which reminded them of the sun being pushed across the sky.</p>
                     <p><strong>Mathematical Connection:</strong> Scarabs were often used in groupings that related to astronomical calculations. Patterns of scarabs could represent lunar cycles of 29.5 days.</p>
                     <div class="pro-info" style="margin-top: 15px; padding: 10px; background: rgba(212, 175, 55, 0.1); border-radius: 4px;">
                        <h4>Pro View Insights:</h4>
                        <p>Scarab symbols have a 45% appearance rate with the highest frequency. They're optimal for cluster patterns but have the lowest individual value.</p>
                        <p><strong>Strategy:</strong> Hold scarabs when you have 2+ on the board to aim for the 2.0x cluster multiplier.</p>
                     </div>`
        },
        hieroglyph: {
            title: "Egyptian Hieroglyphs",
            content: `<p>Hieroglyphs were the formal writing system of ancient Egypt, used for nearly 4,000 years until the rise of Christianity in the 4th century CE.</p>
                     <p>There are over 1,000 distinct hieroglyphs. They could be written in rows or columns and could be read either from left to right or right to left.</p>
                     <p><strong>Mathematical Connection:</strong> Hieroglyphs included specific symbols for numbers. The Egyptians used a base-10 system with unique symbols for powers of ten:</p>
                     <ul>
                        <li>𓏺 = 1</li>
                        <li>𓎆 = 10</li>
                        <li>𓍢 = 100</li>
                        <li>𓆼 = 1,000</li>
                     </ul>
                     <div class="pro-info" style="margin-top: 15px; padding: 10px; background: rgba(212, 175, 55, 0.1); border-radius: 4px;">
                        <h4>Pro View Insights:</h4>
                        <p>Hieroglyph symbols appear 30% of the time with medium value (25 pts). Best for sequence patterns.</p>
                        <p><strong>Strategy:</strong> Consecutive hieroglyphs trigger the highest multiplier (3.0x). Priority hold when you have 2+ in sequence.</p>
                     </div>`
        },
        pyramid: {
            title: "The Great Pyramids",
            content: `<p>The pyramids of Egypt are among the most recognizable and impressive structures ever built by human hands. The Great Pyramid of Giza was the tallest man-made structure for over 3,800 years.</p>
                     <p>Constructed during the Old and Middle Kingdom periods, these monuments served as tombs for pharaohs and their consorts.</p>
                     <p><strong>Mathematical Connection:</strong> The Great Pyramid demonstrates remarkable mathematical precision:</p>
                     <ul>
                        <li>The ratio of the perimeter to height is approximately 2π</li>
                        <li>The pyramid is aligned to true north with an accuracy of 0.05 degrees</li>
                        <li>The base is a near-perfect square with only 8 cm difference between the longest and shortest sides</li>
                     </ul>
                     <div class="pro-info" style="margin-top: 15px; padding: 10px; background: rgba(212, 175, 55, 0.1); border-radius: 4px;">
                        <h4>Pro View Insights:</h4>
                        <p>Pyramid symbols have 15% appearance rate but offer high value (50 pts each). Excellent hold candidates.</p>
                        <p><strong>Strategy:</strong> Always hold pyramids due to their rarity and high individual value. They don't form special patterns but provide consistent scoring.</p>
                     </div>`
        },
        ankh: {
            title: "The Ankh Symbol",
            content: `<p>The ankh (☥) represents life and immortality in ancient Egyptian culture. It was often carried by deities as a symbol of their eternal power.</p>
                     <p>The loop at the top symbolizes the eternal soul, while the cross below represents the material world or earthly existence.</p>
                     <p><strong>Mathematical Connection:</strong> The ankh's proportions often followed the golden ratio (approximately 1:1.618), a mathematical relationship found in many natural phenomena and considered aesthetically pleasing.</p>
                     <div class="pro-info" style="margin-top: 15px; padding: 10px; background: rgba(212, 175, 55, 0.1); border-radius: 4px;">
                        <h4>Pro View Insights:</h4>
                        <p>Ankh symbols appear 8% of the time and provide 2x multipliers instead of points. Critical for large wins.</p>
                        <p><strong>Strategy:</strong> Always hold ankhs as they multiply your total score. Multiple ankhs stack multiplicatively (2x, 4x, 8x, etc.).</p>
                     </div>`
        },
        sphinx: {
            title: "The Great Sphinx",
            content: `<p>The Great Sphinx of Giza is a limestone statue depicting a mythical creature with the body of a lion and the head of a human (likely representing Pharaoh Khafre).</p>
                     <p>It is one of the oldest and largest monumental sculptures in the world, measuring 73 meters long and 20 meters high.</p>
                     <p><strong>Mathematical Connection:</strong> The Sphinx was constructed with precise alignment to celestial bodies. It faces directly east, toward the rising sun on the equinox, demonstrating the Egyptians' advanced understanding of astronomy and mathematical alignment.</p>
                     <div class="pro-info" style="margin-top: 15px; padding: 10px; background: rgba(212, 175, 55, 0.1); border-radius: 4px;">
                        <h4>Pro View Insights:</h4>
                        <p>Sphinx symbols are ultra-rare (2% chance) but provide massive value (100 pts) plus puzzle pieces for upgrades.</p>
                        <p><strong>Strategy:</strong> Always hold sphinx symbols. They're the highest value in the game and unlock educational content upgrades.</p>
                     </div>`
        }
    };
    
    // Egyptian mathematics lessons
    const mathLessons = [
        {
            title: "Egyptian Multiplication",
            content: `<p>The ancient Egyptians used a multiplication method based on doubling and addition. To multiply two numbers:</p>
                     <ol>
                        <li>Create two columns</li>
                        <li>Start with 1 in the left column and the multiplier in the right column</li>
                        <li>Double each column until you reach or exceed the multiplicand</li>
                        <li>Mark the rows in the left column that sum to the multiplicand</li>
                        <li>Add the corresponding values in the right column</li>
                     </ol>
                     <p><strong>Example:</strong> 13 × 24</p>
                     <table style="border-collapse: collapse; margin: 10px 0; width: 100%;">
                        <tr><td style="border: 1px solid #ccc; padding: 5px;">1</td><td style="border: 1px solid #ccc; padding: 5px;">24</td><td style="border: 1px solid #ccc; padding: 5px;">✓</td></tr>
                        <tr><td style="border: 1px solid #ccc; padding: 5px;">2</td><td style="border: 1px solid #ccc; padding: 5px;">48</td></tr>
                        <tr><td style="border: 1px solid #ccc; padding: 5px;">4</td><td style="border: 1px solid #ccc; padding: 5px;">96</td><td style="border: 1px solid #ccc; padding: 5px;">✓</td></tr>
                        <tr><td style="border: 1px solid #ccc; padding: 5px;">8</td><td style="border: 1px solid #ccc; padding: 5px;">192</td><td style="border: 1px solid #ccc; padding: 5px;">✓</td></tr>
                     </table>
                     <p>We mark rows 1, 4, and 8 because 1 + 4 + 8 = 13</p>
                     <p>The answer is 24 + 96 + 192 = 312</p>
                     <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 4px;">
                        <h4>Interactive Practice:</h4>
                        <p>Try this method with the symbol values in Scarab Spin. If you have 3 pyramids (50 pts each), calculate: 3 × 50</p>
                        <p>Answer: 1×50 + 2×50 = 50 + 100 = 150 points!</p>
                     </div>`
        },
        {
            title: "Egyptian Fractions",
            content: `<p>The ancient Egyptians expressed fractions as sums of unit fractions (fractions with 1 as the numerator). The only exception was the fraction 2/3.</p>
                     <p>To represent a fraction like 3/4, they would write it as 1/2 + 1/4.</p>
                     <p>The hieroglyph for a fraction was the mouth symbol '𓂋' placed over a number:</p>
                     <div style="font-size: 18px; text-align: center; margin: 10px 0;">
                        𓂋<br>
                        —<br>
                        𓏺𓏺𓏺
                     </div>
                     <p>This represents 1/3 (the mouth symbol over three vertical strokes).</p>
                     <p><strong>Example Problem:</strong> Express 4/5 as a sum of unit fractions.</p>
                     <p>4/5 = 1/2 + 1/4 + 1/20</p>
                     <p>You can verify this by adding: 1/2 + 1/4 + 1/20 = 10/20 + 5/20 + 1/20 = 16/20 = 4/5</p>
                     <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 4px;">
                        <h4>Game Connection:</h4>
                        <p>Pattern multipliers in Scarab Spin work similarly! A 1.5x diagonal pattern can be thought of as 1 + 1/2.</p>
                        <p>When you get multiple patterns, you're essentially adding Egyptian fractions together!</p>
                     </div>`
        },
        {
            title: "The Rhind Mathematical Papyrus",
            content: `<p>The Rhind Mathematical Papyrus is one of our primary sources of knowledge about ancient Egyptian mathematics. It was written by the scribe Ahmes around 1650 BCE.</p>
                     <p>The papyrus contains 84 problems with solutions, covering:</p>
                     <ul>
                        <li>Arithmetic operations</li>
                        <li>Linear equations</li>
                        <li>Division of food rations</li>
                        <li>Geometry calculations</li>
                        <li>Pyramid slope calculations</li>
                     </ul>
                     <p><strong>Notable Problem:</strong> Problem 50 describes how to calculate the area of a circle:</p>
                     <p>"A circular field with diameter 9 khet has the same area as a square with side 8 khet."</p>
                     <p>This gives a value for π of approximately (8/9)² × 4 ≈ 3.16, which is remarkably close to the true value.</p>
                     <div style="margin-top: 15px; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 4px;">
                        <h4>Archaeological Connection:</h4>
                        <p>The archaeology meter in Scarab Spin represents the careful, methodical approach of ancient mathematicians.</p>
                        <p>Each spin is like solving one of the 84 problems - building knowledge step by step!</p>
                     </div>`
        }
    ];
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing Scarab Spin Mobile Enhanced...");
        
        // Load saved game state
        loadGameState();
        
        // Set up event listeners
        setupEventListeners();
        
        // Setup mobile-specific features
        setupMobileFeatures();
        
        // Initialize reels with random symbols
        generateInitialReels();
        
        // Create sand particles
        createSandParticles();
        
        // Update UI
        updateAllStats();
        updateProViewDisplay();
        
        console.log("Scarab Spin Mobile Enhanced initialized successfully!");
    }
    
    function setupEventListeners() {
        // Core game controls
        spinBtn.addEventListener('click', spin);
        excavateBtn.addEventListener('click', toggleExcavationMode);
        analyzeBtn.addEventListener('click', analyzePatterns);
        
        // View mode toggle
        viewModeToggle.addEventListener('click', toggleViewMode);
        
        // Modal controls
        tutorialBtn.addEventListener('click', showTutorial);
        closeTutorialBtn.addEventListener('click', () => toggleModal(tutorialModal, false));
        closeEducationalBtn.addEventListener('click', () => toggleModal(educationalModal, false));
        closePuzzleBtn.addEventListener('click', () => toggleModal(puzzleModal, false));
        solvePuzzleBtn.addEventListener('click', showPuzzleSolving);
        heatmapToggle.addEventListener('change', toggleHeatmap);
        
        // Hold buttons
        holdBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const reelId = btn.dataset.reel;
                toggleHold(reelId);
                
                // Haptic feedback on mobile
                if (mobileConfig.hapticFeedback && navigator.vibrate) {
                    navigator.vibrate(50);
                }
            });
        });
        
        // Symbol containers for educational content
        symbolContainers.forEach(container => {
            container.addEventListener('click', (e) => {
                e.preventDefault();
                handleSymbolClick(container);
            });
        });
        
        // Keyboard accessibility
        document.addEventListener('keydown', handleKeyboard);
        
        // Window resize handling
        window.addEventListener('resize', debounce(handleResize, 250));
        window.addEventListener('orientationchange', debounce(handleOrientationChange, 300));
    }
    
    function setupMobileFeatures() {
        // Touch event handling
        if (mobileConfig.enableTouch) {
            setupTouchEvents();
        }
        
        // Prevent zoom on double tap
        document.addEventListener('touchstart', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
        
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(e) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // Improve touch feedback
        document.body.addEventListener('touchstart', function() {}, { passive: true });
        
        // Setup accessibility
        setupAccessibility();
    }
    
    function setupTouchEvents() {
        symbolContainers.forEach(container => {
            let touchStartTime = 0;
            let longPressTimer = null;
            
            container.addEventListener('touchstart', (e) => {
                touchStartTime = Date.now();
                touchState.startX = e.touches[0].clientX;
                touchState.startY = e.touches[0].clientY;
                touchState.isLongPress = false;
                
                // Start long press timer
                longPressTimer = setTimeout(() => {
                    touchState.isLongPress = true;
                    handleLongPress(container);
                    if (navigator.vibrate) {
                        navigator.vibrate(100);
                    }
                }, mobileConfig.longPressDelay);
            }, { passive: true });
            
            container.addEventListener('touchmove', (e) => {
                const deltaX = Math.abs(e.touches[0].clientX - touchState.startX);
                const deltaY = Math.abs(e.touches[0].clientY - touchState.startY);
                
                // Cancel long press if finger moves too much
                if (deltaX > 10 || deltaY > 10) {
                    clearTimeout(longPressTimer);
                }
            }, { passive: true });
            
            container.addEventListener('touchend', (e) => {
                e.preventDefault();
                clearTimeout(longPressTimer);
                
                const touchDuration = Date.now() - touchStartTime;
                
                if (!touchState.isLongPress && touchDuration < mobileConfig.doubleClickSpeed) {
                    handleSymbolClick(container);
                }
            });
        });
        
        // Touch events for buttons
        const touchButtons = [spinBtn, excavateBtn, analyzeBtn, ...holdBtns];
        touchButtons.forEach(button => {
            button.addEventListener('touchstart', () => {
                button.style.transform = 'scale(0.95)';
            }, { passive: true });
            
            button.addEventListener('touchend', () => {
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }, { passive: true });
        });
    }
    
    function setupAccessibility() {
        // Add ARIA labels
        symbolContainers.forEach((container, index) => {
            container.setAttribute('role', 'button');
            container.setAttribute('tabindex', '0');
            container.setAttribute('aria-label', `Symbol position ${index + 1}, tap to learn more`);
        });
        
        // Keyboard navigation
        symbolContainers.forEach(container => {
            container.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleSymbolClick(container);
                }
            });
        });
    }
    
    function handleKeyboard(e) {
        // Keyboard shortcuts
        switch(e.key) {
            case ' ':
                if (e.target === document.body) {
                    e.preventDefault();
                    spin();
                }
                break;
            case 'h':
                if (e.target === document.body) {
                    showTutorial();
                }
                break;
            case 'p':
                if (e.target === document.body) {
                    toggleViewMode();
                }
                break;
            case 'Escape':
                // Close any open modals
                document.querySelectorAll('.modal-overlay:not(.hidden)').forEach(modal => {
                    modal.classList.add('hidden');
                });
                break;
        }
    }
    
    function handleResize() {
        // Adjust layout for different screen sizes
        updateLayoutForScreenSize();
        
        // Recreate sand particles for new dimensions
        createSandParticles();
    }
    
    function handleOrientationChange() {
        // Wait for orientation change to complete
        setTimeout(() => {
            updateLayoutForScreenSize();
            createSandParticles();
        }, 100);
    }
    
    function updateLayoutForScreenSize() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        // Adjust symbol sizes based on screen size
        const symbolSize = Math.max(20, Math.min(40, screenWidth / 15));
        document.documentElement.style.setProperty('--mobile-symbol-size', `${symbolSize}px`);
        
        // Adjust button heights for touch-friendly interaction
        const buttonHeight = Math.max(40, Math.min(50, screenHeight / 15));
        document.documentElement.style.setProperty('--mobile-button-height', `${buttonHeight}px`);
        
        // Adjust font sizes
        const fontSize = Math.max(12, Math.min(16, screenWidth / 25));
        document.documentElement.style.setProperty('--mobile-font-size', `${fontSize}px`);
    }
    
    function handleSymbolClick(container) {
        const position = container.dataset.position;
        const symbolElement = container.querySelector('.symbol');
        const symbolClasses = symbolElement.className.split(' ');
        const symbolId = symbolClasses.find(cls => cls !== 'symbol');
        
        if (symbolId && symbolLore[symbolId]) {
            const content = gameState.viewMode === 'pro' 
                ? symbolLore[symbolId].content 
                : symbolLore[symbolId].content.replace(/<div class="pro-info">.*?<\/div>/s, '');
            
            showEducationalContent(symbolLore[symbolId].title, content);
            
            // Award knowledge points for learning
            gameState.knowledge += 1;
            updateAllStats();
        }
    }
    
    function handleLongPress(container) {
        // Long press on symbol container - show quick info
        const position = container.dataset.position;
        const symbolElement = container.querySelector('.symbol');
        const symbolClasses = symbolElement.className.split(' ');
        const symbolId = symbolClasses.find(cls => cls !== 'symbol');
        
        if (symbolId) {
            const symbol = symbols.find(s => s.id === symbolId);
            if (symbol) {
                // Show quick tooltip or action
                showQuickTooltip(container, `${symbol.icon} ${symbol.name}: ${symbol.value || symbol.multiplier + 'x'} pts`);
            }
        }
    }
    
    function showQuickTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'quick-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--primary-dark);
            color: var(--text-light);
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            border: 1px solid var(--accent-gold);
            z-index: 1000;
            pointer-events: none;
            white-space: nowrap;
        `;
        
        element.appendChild(tooltip);
        
        // Position tooltip
        const rect = element.getBoundingClientRect();
        tooltip.style.top = `${rect.height + 5}px`;
        tooltip.style.left = '50%';
        tooltip.style.transform = 'translateX(-50%)';
        
        // Remove tooltip after delay
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 2000);
    }
    
    function toggleViewMode() {
        gameState.viewMode = gameState.viewMode === 'standard' ? 'pro' : 'standard';
        
        // Update UI
        updateProViewDisplay();
        
        // Save state
        saveGameState();
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(75);
        }
    }
    
    function updateProViewDisplay() {
        const isProView = gameState.viewMode === 'pro';
        
        // Update body class
        document.body.classList.toggle('pro-view-active', isProView);
        
        // Update button text
        viewModeText.textContent = isProView ? 'Standard View' : 'Pro View';
        
        // Update toggle icon
        const icon = viewModeToggle.querySelector('i');
        icon.className = isProView ? 'fas fa-eye-slash' : 'fas fa-eye';
        
        // Update pro-specific content
        if (isProView) {
            updateProStats();
            updateStrategyTip();
            updateExpectedValue();
            updateSymbolProbabilities();
        }
    }
    
    function updateProStats() {
        if (gameState.viewMode !== 'pro') return;
        
        // Calculate win rate
        const winRateCalc = gameState.totalSpinsCount > 0 
            ? (gameState.totalWinsCount / gameState.totalSpinsCount * 100).toFixed(1)
            : 0;
        winRate.textContent = `${winRateCalc}%`;
        
        // Calculate average multiplier
        const avgMulti = gameState.totalSpinsCount > 0
            ? (gameState.avgMultiplierSum / gameState.totalSpinsCount).toFixed(1)
            : 1.0;
        avgMultiplier.textContent = `${avgMulti}x`;
        
        // Calculate pattern efficiency
        const patternEff = gameState.totalSpinsCount > 0
            ? (gameState.patternHistory.length / gameState.totalSpinsCount * 100).toFixed(1)
            : 0;
        patternEfficiency.textContent = `${patternEff}%`;
        
        // Update performance metrics
        totalSpins.textContent = gameState.totalSpinsCount;
        totalWins.textContent = gameState.totalWinsCount;
        biggestWin.textContent = gameState.biggestWinAmount;
        streakCount.textContent = gameState.currentStreak;
        
        // Calculate personal RTP
        const personalRTPCalc = gameState.totalSpinsCount > 0
            ? ((gameState.totalPointsEarned / (gameState.totalSpinsCount * 25)) * 100).toFixed(1)
            : 0;
        personalRTP.textContent = `${personalRTPCalc}%`;
        
        // Calculate average score per spin
        const avgScore = gameState.totalSpinsCount > 0
            ? (gameState.totalPointsEarned / gameState.totalSpinsCount).toFixed(1)
            : 0;
        avgScorePerSpin.textContent = avgScore;
        
        // Calculate success rate (wins with patterns)
        const successRateCalc = gameState.totalWinsCount > 0
            ? (gameState.patternHistory.length / gameState.totalWinsCount * 100).toFixed(1)
            : 0;
        successRate.textContent = `${successRateCalc}%`;
        
        // Update learning progress
        const learningProgressCalc = (gameState.modulesCompletedCount / 12 * 100).toFixed(0);
        learningProgress.textContent = `${learningProgressCalc}%`;
        
        // Update efficiency (combination of pattern recognition and optimal holds)
        const efficiencyCalc = Math.min(100, (winRateCalc * 0.4 + patternEff * 0.6)).toFixed(0);
        efficiency.textContent = `${efficiencyCalc}%`;
        
        // Update modules completed
        modulesCompleted.textContent = gameState.modulesCompletedCount;
    }
    
    function updateStrategyTip() {
        if (gameState.viewMode !== 'pro') return;
        
        // Get current symbols
        const currentSymbols = getCurrentSymbols();
        const symbolCounts = getSymbolCounts(currentSymbols);
        
        let tip = "Hold highest value symbols and aim for pattern formations.";
        
        // Generate specific strategy based on current board state
        if (symbolCounts['sphinx'] > 0) {
            tip = "🦅 PRIORITY: Hold all Sphinx symbols! Ultra-rare with highest value.";
        } else if (symbolCounts['ankh'] > 0) {
            tip = "☥ Hold Ankh symbols! They multiply your final score exponentially.";
        } else if (symbolCounts['scarab'] >= 2) {
            tip = "🪲 Hold Scarab symbols for potential cluster pattern (2.0x multiplier).";
        } else if (symbolCounts['hieroglyph'] >= 2) {
            tip = "𓂀 Hold Hieroglyph symbols for sequence pattern (3.0x multiplier).";
        } else if (symbolCounts['pyramid'] > 0) {
            tip = "🏔️ Hold Pyramid symbols for their high individual value (50 pts each).";
        }
        
        strategyTip.textContent = tip;
    }
    
    function updateExpectedValue() {
        if (gameState.viewMode !== 'pro') return;
        
        const currentSymbols = getCurrentSymbols();
        let expectedVal = 0;
        
        currentSymbols.forEach(symbol => {
            expectedVal += symbol.value || 0;
        });
        
        expectedVal *= gameState.totalMultiplier;
        expectedValue.textContent = `${Math.floor(expectedVal)} pts`;
    }
    
    function updateSymbolProbabilities() {
        if (gameState.viewMode !== 'pro') return;
        
        // Update individual symbol probability displays
        for (let i = 0; i < 5; i++) {
            const probElement = document.getElementById(`symbolProb_${i}`);
            if (probElement) {
                const symbolElement = document.getElementById(`symbol_${i}_0`);
                const symbolClasses = symbolElement.className.split(' ');
                const symbolId = symbolClasses.find(cls => cls !== 'symbol');
                
                if (symbolId) {
                    const symbol = symbols.find(s => s.id === symbolId);
                    if (symbol) {
                        probElement.textContent = `${symbol.weight}%`;
                    }
                }
            }
        }
    }
    
    function getCurrentSymbols() {
        const currentSymbols = [];
        
        for (let reel = 0; reel < 5; reel++) {
            const symbolElement = document.getElementById(`symbol_${reel}_0`);
            const symbolClass = symbolElement.className.split(' ').find(cls => cls !== 'symbol');
            const symbol = symbols.find(s => s.id === symbolClass);
            
            if (symbol) {
                currentSymbols.push(symbol);
            }
        }
        
        return currentSymbols;
    }
    
    function getSymbolCounts(symbolArray) {
        const counts = {};
        symbolArray.forEach(symbol => {
            counts[symbol.id] = (counts[symbol.id] || 0) + 1;
        });
        return counts;
    }
    
    function generateInitialReels() {
        for (let reel = 0; reel < 5; reel++) {
            const symbol = weightedRandom(symbols);
            const symbolElement = document.getElementById(`symbol_${reel}_0`);
            
            symbolElement.textContent = symbol.icon;
            symbolElement.className = `symbol ${symbol.id}`;
        }
        
        // Update pro view displays
        if (gameState.viewMode === 'pro') {
            updateSymbolProbabilities();
            updateExpectedValue();
            updateStrategyTip();
        }
    }
    
    function spin() {
        // Check if max spins reached
        if (gameState.spinsToday >= 50) {
            showNotification("You've reached the daily limit of 50 spins. Come back tomorrow to continue learning!", 'info');
            return;
        }
        
        // Disable buttons during animation
        setButtonsEnabled(false);
        
        // Reset values from previous spin
        gameState.currentPatterns = [];
        gameState.totalMultiplier = 1.0;
        gameState.lastWin = 0;
        
        // Track spin
        gameState.totalSpinsCount++;
        
        // Animation: Add spinning class to symbols
        const symbolElements = document.querySelectorAll('.symbol');
        symbolElements.forEach(element => {
            const reelId = element.id.split('_')[1];
            if (!gameState.heldReels.has(reelId)) {
                element.classList.add('spin');
            }
        });
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate([50, 30, 50]);
        }
        
        // Generate new symbols after animation delay
        setTimeout(() => {
            completeSpinAnimation(symbolElements);
        }, 1000);
    }
    
    function completeSpinAnimation(symbolElements) {
        // Remove spinning class
        symbolElements.forEach(element => element.classList.remove('spin'));
        
        // Generate new symbols for non-held reels
        const newSymbols = [];
        
        for (let reel = 0; reel < 5; reel++) {
            if (!gameState.heldReels.has(reel.toString())) {
                const symbol = weightedRandom(symbols);
                const symbolElement = document.getElementById(`symbol_${reel}_0`);
                
                symbolElement.textContent = symbol.icon;
                symbolElement.className = `symbol ${symbol.id}`;
                
                newSymbols.push(symbol);
            } else {
                // Get current symbol for held reel
                const symbolElement = document.getElementById(`symbol_${reel}_0`);
                const symbolClass = symbolElement.className.split(' ').find(cls => cls !== 'symbol');
                const symbol = symbols.find(s => s.id === symbolClass);
                
                newSymbols.push(symbol);
            }
        }
        
        // Process spin results
        processSpinResults(newSymbols);
        
        // Re-enable buttons
        setButtonsEnabled(true);
        
        // Create sand particles effect
        createSandParticles();
        
        // Save game state
        saveGameState();
    }
    
    function processSpinResults(symbols) {
        // Check for patterns
        detectPatterns(symbols);
        
        // Calculate score
        calculateScore(symbols);
        
        // Update archaeology progress
        incrementArchaeologyProgress();
        
        // Increment spin count
        gameState.spinsToday++;
        
        // Check for sphinx symbol (puzzle piece)
        if (symbols.some(s => s.id === 'sphinx') && gameState.piecesCollected < 5) {
            collectPuzzlePiece();
        }
        
        // Track statistics
        updateStatistics();
        
        // Update UI
        updateAllStats();
        updatePatternDisplay();
        
        if (gameState.viewMode === 'pro') {
            updateProStats();
            updateExpectedValue();
            updateStrategyTip();
            updateSymbolProbabilities();
        }
        
        // Show win notification if applicable
        if (gameState.lastWin > 0) {
            showWinAnimation();
        }
    }
    
    function updateStatistics() {
        // Track performance metrics
        gameState.avgMultiplierSum += gameState.totalMultiplier;
        
        if (gameState.lastWin > 0) {
            gameState.totalWinsCount++;
            gameState.currentStreak++;
            gameState.winHistory.push(gameState.lastWin);
            
            if (gameState.lastWin > gameState.biggestWinAmount) {
                gameState.biggestWinAmount = gameState.lastWin;
            }
        } else {
            gameState.currentStreak = 0;
        }
        
        if (gameState.currentPatterns.length > 0) {
            gameState.patternHistory.push({
                patterns: [...gameState.currentPatterns],
                multiplier: gameState.totalMultiplier,
                score: gameState.lastWin
            });
        }
        
        gameState.totalPointsEarned += gameState.lastWin;
    }
    
    function setButtonsEnabled(enabled) {
        spinBtn.disabled = !enabled;
        excavateBtn.disabled = !enabled;
        analyzeBtn.disabled = !enabled;
        
        // Update button opacity
        const opacity = enabled ? 1 : 0.6;
        spinBtn.style.opacity = opacity;
        excavateBtn.style.opacity = opacity;
        analyzeBtn.style.opacity = opacity;
    }
    
    function weightedRandom(items) {
        const total = items.reduce((sum, item) => sum + item.weight, 0);
        let random = Math.random() * total;
        let cumulative = 0;
        
        for (const item of items) {
            cumulative += item.weight;
            if (random <= cumulative) {
                return item;
            }
        }
        
        return items[0];
    }
    
    function detectPatterns(symbols) {
        gameState.currentPatterns = [];
        gameState.totalMultiplier = 1.0;
        
        // Check for horizontal line (all same symbol)
        if (symbols.every(s => s.id === symbols[0].id)) {
            gameState.currentPatterns.push("Horizontal Line");
            gameState.totalMultiplier *= 1.0;
        }
        
        // Check for diagonal pattern (alternating symbols)
        let hasAlternatingPattern = true;
        for (let i = 2; i < symbols.length; i++) {
            if (symbols[i].id !== symbols[i % 2].id) {
                hasAlternatingPattern = false;
                break;
            }
        }
        
        if (hasAlternatingPattern && symbols[0].id !== symbols[1].id) {
            gameState.currentPatterns.push("Diagonal Pattern");
            gameState.totalMultiplier *= 1.5;
        }
        
        // Check for scarab cluster (3+ scarabs)
        const scarabCount = symbols.filter(s => s.id === 'scarab').length;
        if (scarabCount >= 3) {
            gameState.currentPatterns.push("Scarab Cluster");
            gameState.totalMultiplier *= 2.0;
        }
        
        // Check for hieroglyph sequence (3+ hieroglyphs in sequence)
        let hieroglyphSequence = 0;
        let maxSequence = 0;
        
        for (const symbol of symbols) {
            if (symbol.id === 'hieroglyph') {
                hieroglyphSequence++;
                maxSequence = Math.max(maxSequence, hieroglyphSequence);
            } else {
                hieroglyphSequence = 0;
            }
        }
        
        if (maxSequence >= 3) {
            gameState.currentPatterns.push("Hieroglyph Sequence");
            gameState.totalMultiplier *= 3.0;
        }
        
        // Apply pattern mastery upgrade if unlocked
        if (gameState.upgradesUnlocked.patternMastery && gameState.currentPatterns.length > 0) {
            gameState.totalMultiplier += 0.5;
        }
        
        // Update best pattern if this is better
        if (gameState.currentPatterns.length > 0 && gameState.totalMultiplier > 1.0) {
            gameState.bestPatternFound = gameState.currentPatterns.join(", ");
        }
    }
    
    function calculateScore(symbols) {
        let baseScore = 0;
        let ankhs = 0;
        
        for (const symbol of symbols) {
            if (symbol.id === 'ankh') {
                ankhs++;
            } else {
                baseScore += symbol.value;
            }
        }
        
        // Apply ankh multipliers
        let ankhMultiplier = ankhs > 0 ? Math.pow(2, ankhs) : 1;
        
        // Apply pattern multiplier
        let finalScore = baseScore * gameState.totalMultiplier * ankhMultiplier;
        
        // Update score
        gameState.lastWin = Math.floor(finalScore);
        gameState.score += gameState.lastWin;
        
        // Award knowledge points for pattern recognition
        if (gameState.currentPatterns.length > 0) {
            const knowledgeGain = gameState.currentPatterns.length * 5;
            gameState.knowledge += knowledgeGain;
        }
    }
    
    function toggleHold(reelId) {
        const btn = document.getElementById(`holdBtn${reelId}`);
        const holdText = btn.querySelector('.hold-text');
        
        const maxHolds = gameState.upgradesUnlocked.hieroglyphDecoder ? 2 : 1;
        
        if (gameState.heldReels.has(reelId)) {
            // Release hold
            gameState.heldReels.delete(reelId);
            btn.classList.remove('active');
            btn.querySelector('i').className = 'fas fa-lock-open';
            if (holdText) holdText.textContent = 'Hold';
        } else if (gameState.heldReels.size < maxHolds) {
            // Apply hold
            gameState.heldReels.add(reelId);
            btn.classList.add('active');
            btn.querySelector('i').className = 'fas fa-lock';
            if (holdText) holdText.textContent = 'Held';
        } else {
            showNotification(`You can only hold ${maxHolds} reel${maxHolds > 1 ? 's' : ''}. Unlock upgrades to hold more.`, 'warning');
        }
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }
    
    function toggleExcavationMode() {
        gameState.excavationMode = !gameState.excavationMode;
        
        if (gameState.excavationMode) {
            excavateBtn.classList.add('active');
            excavateBtn.querySelector('span').textContent = 'Exit Excavation';
            
            document.body.classList.add('excavation-mode');
            
            if (gameState.upgradesUnlocked.probabilityLens) {
                heatmapToggle.checked = true;
                toggleHeatmap();
            }
        } else {
            excavateBtn.classList.remove('active');
            excavateBtn.querySelector('span').textContent = 'Excavate Mode';
            
            document.body.classList.remove('excavation-mode');
            
            heatmapToggle.checked = false;
            toggleHeatmap();
        }
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(75);
        }
    }
    
    function analyzePatterns() {
        const currentSymbols = getCurrentSymbols();
        const symbolCounts = getSymbolCounts(currentSymbols);
        
        let analysis = '<div class="analysis-content">';
        analysis += '<h3>Pattern Analysis</h3>';
        
        // Current symbols analysis
        analysis += '<div class="analysis-section"><h4>Current Symbols:</h4><ul>';
        for (const [id, count] of Object.entries(symbolCounts)) {
            const symbol = symbols.find(s => s.id === id);
            analysis += `<li>${symbol.icon} ${symbol.name}: ${count}</li>`;
        }
        analysis += '</ul></div>';
        
        // Pattern potential analysis
        analysis += '<div class="analysis-section"><h4>Pattern Potential:</h4><ul>';
        
        if (symbolCounts['scarab'] >= 2) {
            analysis += `<li>🪲 Scarab Cluster potential (need ${3 - symbolCounts['scarab']} more)</li>`;
        }
        
        if (currentSymbols[0].id === currentSymbols[2].id && currentSymbols[2].id === currentSymbols[4].id) {
            analysis += '<li>📐 Diagonal pattern detected</li>';
        }
        
        if (symbolCounts['hieroglyph'] >= 2) {
            analysis += `<li>𓂀 Hieroglyph Sequence potential (need ${3 - symbolCounts['hieroglyph']} more)</li>`;
        }
        
        if (Object.keys(symbolCounts).length === 1) {
            analysis += '<li>➖ Horizontal line potential</li>';
        }
        
        if (symbolCounts['ankh'] > 0) {
            analysis += `<li>☥ ${symbolCounts['ankh']} Ankh(s) = ${Math.pow(2, symbolCounts['ankh'])}x multiplier</li>`;
        }
        
        analysis += '</ul></div>';
        
        // Mobile-optimized strategy section
        analysis += '<div class="analysis-section"><h4>Recommended Strategy:</h4>';
        
        if (symbolCounts['sphinx'] > 0) {
            analysis += '<p>🎯 <strong>PRIORITY:</strong> Hold all Sphinx symbols! Ultra-rare with highest value.</p>';
        } else if (symbolCounts['ankh'] > 0) {
            analysis += '<p>🎯 <strong>HIGH PRIORITY:</strong> Hold Ankh symbols to maximize multipliers.</p>';
        } else if (symbolCounts['scarab'] >= 2) {
            analysis += '<p>🎯 <strong>GOOD STRATEGY:</strong> Hold Scarab symbols for cluster pattern (2.0x).</p>';
        } else if (symbolCounts['hieroglyph'] >= 2) {
            analysis += '<p>🎯 <strong>GOOD STRATEGY:</strong> Hold Hieroglyph symbols for sequence pattern (3.0x).</p>';
        } else if (symbolCounts['pyramid'] > 0) {
            analysis += '<p>🎯 <strong>SAFE PLAY:</strong> Hold Pyramid symbols for guaranteed high value.</p>';
        } else {
            analysis += '<p>🎯 <strong>SUGGESTION:</strong> No strong patterns detected. Try a fresh spin.</p>';
        }
        
        analysis += '</div>';
        
        // Pro View additional analysis
        if (gameState.viewMode === 'pro') {
            analysis += '<div class="analysis-section pro-analysis">';
            analysis += '<h4>Advanced Metrics:</h4>';
            
            const expectedValue = currentSymbols.reduce((sum, symbol) => sum + (symbol.value || 0), 0);
            analysis += `<p><strong>Expected Base Value:</strong> ${expectedValue} points</p>`;
            
            const holdValue = Math.max(...currentSymbols.map(s => s.value || (s.multiplier ? 50 : 0)));
            analysis += `<p><strong>Optimal Hold Value:</strong> ${holdValue} points</p>`;
            
            const riskLevel = symbolCounts['sphinx'] > 0 ? 'Very Low' : 
                             symbolCounts['ankh'] > 0 ? 'Low' :
                             symbolCounts['pyramid'] > 0 ? 'Medium' : 'High';
            analysis += `<p><strong>Risk Level:</strong> ${riskLevel}</p>`;
            
            analysis += '</div>';
        }
        
        analysis += '</div>';
        
        showEducationalContent('Pattern Analysis', analysis);
        
        // Award knowledge points
        gameState.knowledge += 2;
        updateAllStats();
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }
    
    function collectPuzzlePiece() {
        if (gameState.piecesCollected < 5) {
            const piece = document.getElementById(`piece${gameState.piecesCollected}`);
            piece.classList.add('collected');
            gameState.piecesCollected++;
            
            puzzleCount.textContent = gameState.piecesCollected;
            
            if (gameState.piecesCollected === 5) {
                solvePuzzleBtn.disabled = false;
            }
            
            // Show collection notification
            showNotification(`Puzzle piece collected! ${gameState.piecesCollected}/5`, 'success');
            
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100]);
            }
        }
    }
    
    function showPuzzleSolving() {
        const puzzleIndex = Math.floor(Math.random() * mathLessons.length);
        const puzzle = mathLessons[puzzleIndex];
        
        // Generate random math question based on the lesson
        const questions = [
            {
                question: "Calculate 1/4 + 1/8 using Egyptian fractions:",
                answers: ["3/8", "1/2", "2/5", "5/12"],
                correct: 0,
                explanation: "1/4 + 1/8 = 2/8 + 1/8 = 3/8"
            },
            {
                question: "Using Egyptian multiplication, what is 7 × 12?",
                answers: ["84", "72", "96", "78"],
                correct: 0,
                explanation: "7 = 4 + 2 + 1, so 7 × 12 = (4×12) + (2×12) + (1×12) = 48 + 24 + 12 = 84"
            },
            {
                question: "Express 5/6 as a sum of unit fractions:",
                answers: ["1/2 + 1/3", "1/3 + 1/2", "1/4 + 1/12", "2/3 + 1/6"],
                correct: 0,
                explanation: "5/6 = 3/6 + 2/6 = 1/2 + 1/3"
            }
        ];
        
        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
        
        puzzleGame.innerHTML = `
            <div class="puzzle-challenge">
                <h3>${puzzle.title}</h3>
                <div class="puzzle-content" style="max-height: 200px; overflow-y: auto; margin: 15px 0;">
                    ${puzzle.content}
                </div>
                <div class="puzzle-question" style="margin-top: 20px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.2);">
                    <p><strong>Solve this to gain an upgrade:</strong></p>
                    <div class="math-question" style="margin: 15px 0;">
                        <p style="font-size: 16px; margin-bottom: 15px;">${randomQuestion.question}</p>
                        <div class="answer-options" style="display: grid; gap: 10px;">
                            ${randomQuestion.answers.map((answer, index) => 
                                `<button class="answer-btn" data-correct="${index === randomQuestion.correct}" 
                                 style="padding: 12px; background: var(--primary-medium); border: 1px solid var(--accent-gold-dark); 
                                 color: var(--text-light); border-radius: 6px; cursor: pointer; transition: all 0.3s ease;"
                                 onmouseover="this.style.background='var(--primary-light)'"
                                 onmouseout="this.style.background='var(--primary-medium)'">${answer}</button>`
                            ).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Add event listeners to answer buttons
        const answerBtns = puzzleGame.querySelectorAll('.answer-btn');
        answerBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const isCorrect = e.target.dataset.correct === 'true';
                
                if (isCorrect) {
                    unlockRandomUpgrade();
                    
                    puzzleGame.innerHTML = `
                        <div class="puzzle-success" style="text-align: center; padding: 20px;">
                            <h3 style="color: var(--success-color); margin-bottom: 15px;">🎉 Puzzle Solved!</h3>
                            <p style="margin-bottom: 10px;">You've successfully solved the ancient Egyptian mathematical puzzle.</p>
                            <p style="margin-bottom: 15px;">Your knowledge of Egyptian mathematics has unlocked a new ability!</p>
                            <div style="padding: 15px; background: rgba(76, 175, 80, 0.1); border-radius: 6px; margin: 15px 0;">
                                <p class="upgrade-notification" style="font-weight: bold; color: var(--accent-gold);">
                                    Upgrade unlocked: <span id="upgradeUnlocked">Loading...</span>
                                </p>
                            </div>
                            <p style="font-size: 14px; opacity: 0.8;">Continue collecting puzzle pieces to unlock more upgrades!</p>
                        </div>
                    `;
                    
                    // Reset puzzle collection
                    resetPuzzleCollection();
                    
                    // Haptic feedback for success
                    if (navigator.vibrate) {
                        navigator.vibrate([200, 100, 200, 100, 200]);
                    }
                    
                } else {
                    puzzleGame.innerHTML = `
                        <div class="puzzle-failure" style="text-align: center; padding: 20px;">
                            <h3 style="color: var(--danger-color); margin-bottom: 15px;">❌ Incorrect Solution</h3>
                            <p style="margin-bottom: 10px;">Your understanding of Egyptian mathematics still needs improvement.</p>
                            <div style="padding: 15px; background: rgba(244, 67, 54, 0.1); border-radius: 6px; margin: 15px 0;">
                                <p style="font-weight: bold;">Correct Answer: ${randomQuestion.answers[randomQuestion.correct]}</p>
                                <p style="font-size: 14px; margin-top: 8px;">${randomQuestion.explanation}</p>
                            </div>
                            <p style="font-size: 14px; opacity: 0.8;">Continue collecting puzzle pieces and try again.</p>
                        </div>
                    `;
                    
                    // Remove one puzzle piece
                    if (gameState.piecesCollected > 0) {
                        gameState.piecesCollected--;
                        updatePuzzleDisplay();
                    }
                    
                    // Haptic feedback for failure
                    if (navigator.vibrate) {
                        navigator.vibrate([500]);
                    }
                }
            });
        });
        
        toggleModal(puzzleModal, true);
    }
    
    function unlockRandomUpgrade() {
        const lockedUpgrades = Object.entries(gameState.upgradesUnlocked)
            .filter(([_, unlocked]) => !unlocked)
            .map(([name, _]) => name);
        
        if (lockedUpgrades.length > 0) {
            const upgradeIndex = Math.floor(Math.random() * lockedUpgrades.length);
            const upgradeName = lockedUpgrades[upgradeIndex];
            
            gameState.upgradesUnlocked[upgradeName] = true;
            
            const upgradeNameDisplay = {
                hieroglyphDecoder: "Hieroglyph Decoder: Can now hold 2 reels",
                probabilityLens: "Probability Lens: Can now see symbol odds in Pro View",
                patternMastery: "Pattern Mastery: +0.5x to all multipliers"
            };
            
            setTimeout(() => {
                const upgradeUnlockedElement = document.getElementById('upgradeUnlocked');
                if (upgradeUnlockedElement) {
                    upgradeUnlockedElement.textContent = upgradeNameDisplay[upgradeName];
                }
            }, 100);
            
            // Update modules completed count
            gameState.modulesCompletedCount++;
        }
    }
    
    function resetPuzzleCollection() {
        gameState.piecesCollected = 0;
        puzzleCount.textContent = gameState.piecesCollected;
        solvePuzzleBtn.disabled = true;
        
        puzzlePieces.forEach(piece => {
            piece.classList.remove('collected');
        });
    }
    
    function updatePuzzleDisplay() {
        puzzleCount.textContent = gameState.piecesCollected;
        
        puzzlePieces.forEach((piece, index) => {
            if (index >= gameState.piecesCollected) {
                piece.classList.remove('collected');
            }
        });
        
        solvePuzzleBtn.disabled = gameState.piecesCollected < 5;
    }
    
    function incrementArchaeologyProgress() {
        gameState.archaeologyProgress += 5;
        
        if (gameState.archaeologyProgress > 100) {
            gameState.archaeologyProgress = 100;
        }
        
        archaeologyFill.style.width = `${gameState.archaeologyProgress}%`;
        archaeologyText.textContent = `${gameState.archaeologyProgress}%`;
        
        if (gameState.archaeologyProgress === 100) {
            const randomIndex = Math.floor(Math.random() * mathLessons.length);
            showEducationalContent(mathLessons[randomIndex].title, mathLessons[randomIndex].content);
            
            gameState.archaeologyProgress = 0;
            archaeologyFill.style.width = '0%';
            archaeologyText.textContent = '0%';
            
            gameState.knowledge += 20;
            updateAllStats();
            
            // Show completion notification
            showNotification("Archaeological discovery complete! Educational content unlocked.", 'success');
        }
    }
    
    function createSandParticles() {
        sandParticles.innerHTML = '';
        
        const particleCount = window.innerWidth < 768 ? 15 : 30;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'sand-particle';
            
            const left = Math.random() * 100;
            const top = Math.random() * 100;
            const size = Math.random() * 3 + 1;
            const duration = Math.random() * 3 + 2;
            const opacity = Math.random() * 0.7 + 0.3;
            
            particle.style.cssText = `
                position: absolute;
                left: ${left}%;
                top: ${top}%;
                width: ${size}px;
                height: ${size}px;
                background-color: rgba(218, 185, 130, ${opacity});
                border-radius: 50%;
                opacity: ${opacity};
                animation: float ${duration}s infinite ease-in-out;
                animation-delay: -${Math.random() * 2}s;
            `;
            
            sandParticles.appendChild(particle);
        }
        
        // Add floating animation keyframes if not exists
        if (!document.querySelector('#sand-animation-styles')) {
            const style = document.createElement('style');
            style.id = 'sand-animation-styles';
            style.textContent = `
                @keyframes float {
                    0%, 100% { transform: translateY(0px) rotate(0deg); }
                    33% { transform: translateY(-10px) rotate(120deg); }
                    66% { transform: translateY(5px) rotate(240deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    function toggleHeatmap() {
        const showHeatmap = heatmapToggle.checked;
        
        if (showHeatmap && (gameState.excavationMode || gameState.upgradesUnlocked.probabilityLens)) {
            symbolContainers.forEach((container, index) => {
                let overlay = container.querySelector('.probability-heat');
                
                if (!overlay) {
                    overlay = document.createElement('div');
                    overlay.className = 'probability-heat';
                    container.appendChild(overlay);
                }
                
                let probabilities = '';
                symbols.forEach(symbol => {
                    probabilities += `<div class="prob-indicator">
                        <span class="prob-symbol">${symbol.icon}</span>
                        <span class="prob-percent">${symbol.weight}%</span>
                    </div>`;
                });
                
                overlay.innerHTML = `<div class="prob-tooltip">${probabilities}</div>`;
                
                overlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.2);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 3;
                    backdrop-filter: blur(1px);
                `;
                
                const tooltip = overlay.querySelector('.prob-tooltip');
                tooltip.style.display = 'none';
                
                // Touch-friendly tooltip display
                overlay.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    tooltip.style.display = 'block';
                }, { passive: false });
                
                overlay.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    setTimeout(() => {
                        tooltip.style.display = 'none';
                    }, 2000);
                }, { passive: false });
                
                // Mouse events for desktop
                overlay.addEventListener('mouseenter', () => {
                    tooltip.style.display = 'block';
                });
                
                overlay.addEventListener('mouseleave', () => {
                    tooltip.style.display = 'none';
                });
            });
        } else {
            document.querySelectorAll('.probability-heat').forEach(overlay => {
                overlay.remove();
            });
        }
    }
    
    function updateAllStats() {
        totalScore.textContent = gameState.score;
        lastSpinScore.textContent = gameState.lastWin;
        bestPattern.textContent = gameState.bestPatternFound;
        spinCount.textContent = gameState.spinsToday;
        knowledgePoints.textContent = gameState.knowledge;
        
        if (sessionScore) {
            sessionScore.textContent = gameState.score;
        }
    }
    
    function updatePatternDisplay() {
        if (gameState.currentPatterns.length > 0) {
            patternList.textContent = gameState.currentPatterns.join(', ');
        } else {
            patternList.textContent = 'None';
        }
        
        multiplierValue.textContent = `${gameState.totalMultiplier.toFixed(1)}x`;
    }
    
    function showWinAnimation() {
        // Create win notification
        const winAmount = gameState.lastWin;
        showNotification(`🎉 Win! +${winAmount} points`, 'success');
        
        // Add glow effect to multiplier
        multiplierValue.style.animation = 'glow 1s ease-in-out';
        setTimeout(() => {
            multiplierValue.style.animation = '';
        }, 1000);
        
        // Haptic feedback for wins
        if (navigator.vibrate) {
            if (winAmount > 100) {
                navigator.vibrate([100, 50, 100, 50, 200]); // Big win
            } else {
                navigator.vibrate([100, 100]); // Regular win
            }
        }
    }
    
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        const colors = {
            success: 'var(--success-color)',
            warning: 'var(--accent-gold)',
            error: 'var(--danger-color)',
            info: 'var(--info-color)'
        };
        
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            z-index: 2000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
        
        // Add animation styles if not exists
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    function showTutorial() {
        toggleModal(tutorialModal, true);
    }
    
    function showEducationalContent(title, content) {
        modalTitle.textContent = title;
        modalBody.innerHTML = content;
        toggleModal(educationalModal, true);
    }
    
    function toggleModal(modal, show) {
        if (show) {
            modal.classList.remove('hidden');
            // Focus management for accessibility
            const closeButton = modal.querySelector('.close-modal-btn');
            if (closeButton) {
                closeButton.focus();
            }
        } else {
            modal.classList.add('hidden');
        }
    }
    
    function saveGameState() {
        try {
            localStorage.setItem('scarabSpinGameState', JSON.stringify(gameState));
        } catch (e) {
            console.warn('Could not save game state:', e);
        }
    }
    
    function loadGameState() {
        try {
            const saved = localStorage.getItem('scarabSpinGameState');
            if (saved) {
                const parsedState = JSON.parse(saved);
                // Merge saved state with current state, keeping structure intact
                Object.assign(gameState, parsedState);
                
                // Reset daily spins if it's a new day
                const today = new Date().toDateString();
                const lastPlayDate = localStorage.getItem('scarabSpinLastPlayDate');
                if (lastPlayDate !== today) {
                    gameState.spinsToday = 0;
                    localStorage.setItem('scarabSpinLastPlayDate', today);
                }
            }
        } catch (e) {
            console.warn('Could not load game state:', e);
        }
    }
    
    // Utility functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Initialize layout on load
    updateLayoutForScreenSize();
    
    // Auto-save every 30 seconds
    setInterval(saveGameState, 30000);
});