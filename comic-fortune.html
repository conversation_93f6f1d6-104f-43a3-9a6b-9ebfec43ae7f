<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comic Fortune - Superhero Slots</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="comic-fortune.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <div class="comic-fortune-container">
        <div class="game-header">
            <a href="index.html" class="back-link">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Games</span>
            </a>
            <div>
                <h1 class="game-title">Comic Fortune</h1>
                <p class="game-subtitle">🦸‍♂️ Superhero slots • Comic book adventure • Epic jackpots</p>
            </div>
            <div class="view-mode-toggle">
                <button id="standardViewBtn" class="active">Standard</button>
                <button id="proViewBtn">Pro View</button>
            </div>
        </div>

        <!-- Pro View Stats -->
        <div class="pro-view-stats">
            <div class="pro-view-title">
                <i class="fas fa-mask"></i> Superhero Analytics
            </div>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Hit Rate</div>
                    <div class="pro-stat-value" id="hitRate">0%</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Avg Win</div>
                    <div class="pro-stat-value" id="avgWin">0 GA</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Best Combo</div>
                    <div class="pro-stat-value" id="bestCombo">---</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Total Spins</div>
                    <div class="pro-stat-value" id="totalSpins">0</div>
                </div>
            </div>

            <div class="pro-view-sections">
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-history"></i> Spin History
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-bar">
                            <span class="chart-label">Wins</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="winsChart" style="width: 0%"></div>
                            </div>
                            <span class="chart-value" id="winsPercent">0%</span>
                        </div>
                        <div class="chart-bar">
                            <span class="chart-label">Bonus</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="bonusChart" style="width: 0%"></div>
                            </div>
                            <span class="chart-value" id="bonusPercent">0%</span>
                        </div>
                        <div class="chart-bar">
                            <span class="chart-label">ROI</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="roiChart" style="width: 50%"></div>
                            </div>
                            <span class="chart-value" id="roiPercent">0%</span>
                        </div>
                    </div>
                    
                    <div class="spin-history" id="spinHistory">
                        <div class="history-entry">
                            <span class="history-symbols">No spins yet</span>
                            <span class="history-result">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-chart-pie"></i> Symbol Frequency
                    </div>
                    
                    <div class="symbol-frequency" id="symbolFrequency">
                        <div class="symbol-stat">
                            <div class="symbol-icon">🍒</div>
                            <div class="symbol-count" id="cherryCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">🍋</div>
                            <div class="symbol-count" id="lemonCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">🍊</div>
                            <div class="symbol-count" id="orangeCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">🔔</div>
                            <div class="symbol-count" id="bellCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">📊</div>
                            <div class="symbol-count" id="barCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">7️⃣</div>
                            <div class="symbol-count" id="sevenCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">💎</div>
                            <div class="symbol-count" id="diamondCount">0</div>
                        </div>
                        <div class="symbol-stat">
                            <div class="symbol-icon">⭐</div>
                            <div class="symbol-count" id="starCount">0</div>
                        </div>
                    </div>
                </div>
                
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-robot"></i> Auto Spin
                    </div>
                    
                    <div class="auto-spin-controls">
                        <div class="auto-spin-title">Auto Spin Settings</div>
                        
                        <div class="auto-controls">
                            <div class="auto-control">
                                <span class="auto-label">Spins</span>
                                <input type="number" class="auto-input" id="autoSpinCount" min="1" max="100" value="10">
                            </div>
                            <div class="auto-control">
                                <span class="auto-label">Stop Win</span>
                                <input type="number" class="auto-input" id="autoStopWin" min="0" value="1000">
                            </div>
                            <div class="auto-control">
                                <span class="auto-label">Stop Loss</span>
                                <input type="number" class="auto-input" id="autoStopLoss" min="0" value="500">
                            </div>
                            <div class="auto-control">
                                <span class="auto-label">Status</span>
                                <span class="auto-input" id="autoStatus">Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="pro-controls">
                <button class="pro-btn" id="analyzeSpinsBtn">
                    <i class="fas fa-search"></i> Analyze Spins
                </button>
                <button class="pro-btn" id="optimizeStrategyBtn">
                    <i class="fas fa-magic"></i> Optimize Strategy
                </button>
                <button class="pro-btn" id="symbolStatsBtn">
                    <i class="fas fa-chart-bar"></i> Symbol Stats
                </button>
                <button class="pro-btn" id="exportDataBtn">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats -->
            <div class="stats-panel">
                <h3 class="panel-title">🎮 Player Stats</h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">Balance</div>
                        <div class="stat-value" id="balance">5,000</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Current Bet</div>
                        <div class="stat-value" id="currentBet">25</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Total Won</div>
                        <div class="stat-value" id="totalWon">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Biggest Win</div>
                        <div class="stat-value" id="biggestWin">0</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-label">Hero Level</div>
                    <div class="stat-value" id="heroLevel">1</div>
                </div>

                <div class="paytable-container">
                    <h4 style="color: white; margin-bottom: 0.5rem; text-align: center;">🏆 Paytable</h4>
                    <table class="paytable">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>3x</th>
                                <th>4x</th>
                                <th>5x</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>⭐</td>
                                <td>500x</td>
                                <td>2000x</td>
                                <td>10000x</td>
                            </tr>
                            <tr>
                                <td>💎</td>
                                <td>200x</td>
                                <td>1000x</td>
                                <td>5000x</td>
                            </tr>
                            <tr>
                                <td>7️⃣</td>
                                <td>100x</td>
                                <td>500x</td>
                                <td>2500x</td>
                            </tr>
                            <tr>
                                <td>🔔</td>
                                <td>50x</td>
                                <td>200x</td>
                                <td>1000x</td>
                            </tr>
                            <tr>
                                <td>📊</td>
                                <td>25x</td>
                                <td>100x</td>
                                <td>500x</td>
                            </tr>
                            <tr>
                                <td>🍊</td>
                                <td>10x</td>
                                <td>50x</td>
                                <td>200x</td>
                            </tr>
                            <tr>
                                <td>🍋</td>
                                <td>5x</td>
                                <td>25x</td>
                                <td>100x</td>
                            </tr>
                            <tr>
                                <td>🍒</td>
                                <td>2x</td>
                                <td>10x</td>
                                <td>50x</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Slot Machine -->
            <div class="slot-machine-container">
                <div class="slot-controls">
                    <div class="betting-section">
                        <div class="bet-control">
                            <span class="bet-label">Bet:</span>
                            <input type="number" class="bet-input" id="betAmount" min="5" max="100" value="25" step="5">
                            <div class="lines-selector">
                                <span class="bet-label">Lines:</span>
                                <div class="lines-badge" id="paylines">5</div>
                            </div>
                        </div>
                        
                        <div class="quick-bet-chips">
                            <div class="bet-chip chip-5" data-amount="5">5</div>
                            <div class="bet-chip chip-10" data-amount="10">10</div>
                            <div class="bet-chip chip-25" data-amount="25">25</div>
                            <div class="bet-chip chip-50" data-amount="50">50</div>
                        </div>
                    </div>
                </div>

                <div class="win-display" id="winDisplay">
                    <div>
                        <div class="win-amount" id="winAmount">Ready to Spin!</div>
                        <div class="win-message" id="winMessage">Select your bet and spin the reels</div>
                    </div>
                </div>

                <div class="slot-reels" id="slotReels">
                    <div class="paylines-display" id="paylinesDisplay">
                        <div class="payline payline-1"></div>
                        <div class="payline payline-2"></div>
                        <div class="payline payline-3"></div>
                    </div>
                    
                    <div class="reel" id="reel1">
                        <div class="reel-content">
                            <!-- Reel symbols will be generated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="reel" id="reel2">
                        <div class="reel-content">
                            <!-- Reel symbols will be generated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="reel" id="reel3">
                        <div class="reel-content">
                            <!-- Reel symbols will be generated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="reel" id="reel4">
                        <div class="reel-content">
                            <!-- Reel symbols will be generated by JavaScript -->
                        </div>
                    </div>
                    
                    <div class="reel" id="reel5">
                        <div class="reel-content">
                            <!-- Reel symbols will be generated by JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="game-controls">
                    <button class="control-btn btn-max-bet" id="maxBetBtn">Max Bet</button>
                    <button class="control-btn btn-spin" id="spinBtn">🎲 SPIN</button>
                    <button class="control-btn btn-auto" id="autoSpinBtn">Auto Spin</button>
                    <button class="control-btn btn-info" id="infoBtn">📖 Rules</button>
                </div>
            </div>

            <!-- Strategy Panel -->
            <div class="strategy-panel">
                <h3 class="panel-title">🧠 Strategy Center</h3>
                
                <div class="stat-card">
                    <div class="stat-label">Current Session</div>
                    <div class="stat-value" id="sessionTime">00:00</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Session P&L</div>
                    <div class="stat-value" id="sessionPL">0 GA</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Spin Rate</div>
                    <div class="stat-value" id="spinRate">0/min</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Volatility</div>
                    <div class="stat-value" id="volatility">Medium</div>
                </div>

                <div style="margin-top: 1rem; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h4 style="color: var(--comic-yellow); margin-bottom: 0.5rem; text-align: center;">🎯 Strategy Tips</h4>
                    <div id="strategyTips" style="font-size: 0.9rem; line-height: 1.4;">
                        <p>• Start with smaller bets to understand the game rhythm</p>
                        <p>• Look for symbol patterns and frequency</p>
                        <p>• Use auto-spin for consistent betting strategy</p>
                        <p>• Set win/loss limits before playing</p>
                    </div>
                </div>

                <div style="margin-top: 1rem; padding: 1rem; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h4 style="color: var(--comic-yellow); margin-bottom: 0.5rem; text-align: center;">🏅 Achievements</h4>
                    <div id="achievementsList" style="font-size: 0.8rem;">
                        <div style="opacity: 0.6;">🎰 First Spin - Not achieved</div>
                        <div style="opacity: 0.6;">💰 Big Winner - Not achieved</div>
                        <div style="opacity: 0.6;">🔥 Hot Streak - Not achieved</div>
                        <div style="opacity: 0.6;">🎯 Jackpot Hunter - Not achieved</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Result Modal -->
    <div class="result-modal" id="resultModal">
        <h2 id="resultTitle">🎉 Big Win!</h2>
        <p id="resultMessage"></p>
        <div id="resultDetails"></div>
        <div id="achievements"></div>
        <button class="control-btn btn-spin" onclick="closeResult()">Continue Playing</button>
    </div>

    <script src="assets/js/script.js"></script>
    <script src="comic-fortune.js"></script>
    <script>
        // Enhanced Comic Fortune Game with Pro View Features
        class EnhancedComicFortuneGame extends ComicFortuneGame {
            constructor() {
                super();
                
                // Pro View variables
                this.viewMode = localStorage.getItem('comicFortuneViewMode') || 'standard';
                this.hitRate = 0;
                this.avgWin = 0;
                this.bestCombo = '---';
                this.totalSpins = 0;
                this.sessionStartTime = Date.now();
                this.sessionPL = 0;
                this.lastSessionBalance = this.balance;
                
                // Spin statistics
                this.winsCount = 0;
                this.bonusCount = 0;
                this.totalWinAmount = 0;
                this.totalBetAmount = 0;
                this.biggestWinAmount = 0;
                this.spinTimes = [];
                
                // Symbol frequency tracking
                this.symbolFrequency = {
                    cherry: 0, lemon: 0, orange: 0, bell: 0,
                    bar: 0, seven: 0, diamond: 0, star: 0
                };
                
                // Auto spin settings
                this.autoSpinActive = false;
                this.autoSpinRemaining = 0;
                this.autoStopWin = 1000;
                this.autoStopLoss = 500;
                
                // Initialize Pro View elements
                this.initProViewElements();
                this.attachProViewEventListeners();
                this.setViewMode(this.viewMode);
                this.startSessionTimer();
            }
            
            initProViewElements() {
                // View mode toggle buttons
                this.standardViewBtn = document.getElementById('standardViewBtn');
                this.proViewBtn = document.getElementById('proViewBtn');
                
                // Pro View statistics displays
                this.hitRateDisplay = document.getElementById('hitRate');
                this.avgWinDisplay = document.getElementById('avgWin');
                this.bestComboDisplay = document.getElementById('bestCombo');
                this.totalSpinsDisplay = document.getElementById('totalSpins');
                
                // Chart displays
                this.winsChartDisplay = document.getElementById('winsChart');
                this.bonusChartDisplay = document.getElementById('bonusChart');
                this.roiChartDisplay = document.getElementById('roiChart');
                this.winsPercentDisplay = document.getElementById('winsPercent');
                this.bonusPercentDisplay = document.getElementById('bonusPercent');
                this.roiPercentDisplay = document.getElementById('roiPercent');
                
                // History and frequency displays
                this.spinHistoryDisplay = document.getElementById('spinHistory');
                this.symbolFrequencyDisplays = {
                    cherry: document.getElementById('cherryCount'),
                    lemon: document.getElementById('lemonCount'),
                    orange: document.getElementById('orangeCount'),
                    bell: document.getElementById('bellCount'),
                    bar: document.getElementById('barCount'),
                    seven: document.getElementById('sevenCount'),
                    diamond: document.getElementById('diamondCount'),
                    star: document.getElementById('starCount')
                };
                
                // Session tracking displays
                this.sessionTimeDisplay = document.getElementById('sessionTime');
                this.sessionPLDisplay = document.getElementById('sessionPL');
                this.spinRateDisplay = document.getElementById('spinRate');
                this.volatilityDisplay = document.getElementById('volatility');
                
                // Auto spin controls
                this.autoSpinCountInput = document.getElementById('autoSpinCount');
                this.autoStopWinInput = document.getElementById('autoStopWin');
                this.autoStopLossInput = document.getElementById('autoStopLoss');
                this.autoStatusDisplay = document.getElementById('autoStatus');
                
                // Pro View buttons
                this.analyzeSpinsBtn = document.getElementById('analyzeSpinsBtn');
                this.optimizeStrategyBtn = document.getElementById('optimizeStrategyBtn');
                this.symbolStatsBtn = document.getElementById('symbolStatsBtn');
                this.exportDataBtn = document.getElementById('exportDataBtn');
            }
            
            attachProViewEventListeners() {
                // View mode toggle
                if (this.standardViewBtn) {
                    this.standardViewBtn.addEventListener('click', () => this.setViewMode('standard'));
                }
                
                if (this.proViewBtn) {
                    this.proViewBtn.addEventListener('click', () => this.setViewMode('pro'));
                }
                
                // Pro View buttons
                if (this.analyzeSpinsBtn) {
                    this.analyzeSpinsBtn.addEventListener('click', () => this.analyzeSpins());
                }
                
                if (this.optimizeStrategyBtn) {
                    this.optimizeStrategyBtn.addEventListener('click', () => this.optimizeStrategy());
                }
                
                if (this.symbolStatsBtn) {
                    this.symbolStatsBtn.addEventListener('click', () => this.showSymbolStats());
                }
                
                if (this.exportDataBtn) {
                    this.exportDataBtn.addEventListener('click', () => this.exportGameData());
                }
                
                // Auto spin input changes
                if (this.autoSpinCountInput) {
                    this.autoSpinCountInput.addEventListener('change', () => {
                        this.autoSpinRemaining = parseInt(this.autoSpinCountInput.value) || 10;
                    });
                }
                
                if (this.autoStopWinInput) {
                    this.autoStopWinInput.addEventListener('change', () => {
                        this.autoStopWin = parseInt(this.autoStopWinInput.value) || 1000;
                    });
                }
                
                if (this.autoStopLossInput) {
                    this.autoStopLossInput.addEventListener('change', () => {
                        this.autoStopLoss = parseInt(this.autoStopLossInput.value) || 500;
                    });
                }
            }
            
            setViewMode(mode) {
                this.viewMode = mode;
                localStorage.setItem('comicFortuneViewMode', mode);
                
                if (mode === 'standard') {
                    document.body.classList.remove('pro-view-active');
                    this.standardViewBtn.classList.add('active');
                    this.proViewBtn.classList.remove('active');
                } else {
                    document.body.classList.add('pro-view-active');
                    this.standardViewBtn.classList.remove('active');
                    this.proViewBtn.classList.add('active');
                    
                    // Update Pro View displays
                    this.updateProViewDisplay();
                }
            }
            
            // Override spin method to track statistics
            spin() {
                if (this.balance < this.currentBet) {
                    this.showNotification('Insufficient balance for this bet!');
                    return;
                }
                
                // Record spin time for rate calculation
                this.spinTimes.push(Date.now());
                if (this.spinTimes.length > 10) {
                    this.spinTimes.shift(); // Keep only last 10 spins
                }
                
                // Track total bets
                this.totalBetAmount += this.currentBet;
                this.totalSpins++;
                
                // Call parent spin method
                const result = super.spin();
                
                // Track symbol frequency
                if (result && result.finalSymbols) {
                    result.finalSymbols.forEach(row => {
                        row.forEach(symbol => {
                            if (this.symbolFrequency[symbol] !== undefined) {
                                this.symbolFrequency[symbol]++;
                            }
                        });
                    });
                }
                
                // Track wins
                if (result && result.winAmount > 0) {
                    this.winsCount++;
                    this.totalWinAmount += result.winAmount;
                    
                    if (result.winAmount > this.biggestWinAmount) {
                        this.biggestWinAmount = result.winAmount;
                        this.bestCombo = result.winningCombination || '⭐⭐⭐';
                    }
                    
                    // Check for bonus features
                    if (result.winAmount >= this.currentBet * 50) {
                        this.bonusCount++;
                    }
                }
                
                // Update calculated statistics
                this.hitRate = this.totalSpins > 0 ? (this.winsCount / this.totalSpins) * 100 : 0;
                this.avgWin = this.winsCount > 0 ? this.totalWinAmount / this.winsCount : 0;
                
                // Update Pro View displays
                this.updateProViewDisplay();
                this.addToSpinHistory(result);
                this.updateSessionStats();
                
                // Handle auto spin
                if (this.autoSpinActive) {
                    this.handleAutoSpin();
                }
                
                return result;
            }
            
            // Start session timer
            startSessionTimer() {
                setInterval(() => this.updateSessionTimer(), 1000);
            }
            
            // Update session timer display
            updateSessionTimer() {
                const elapsed = Date.now() - this.sessionStartTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                
                if (this.sessionTimeDisplay) {
                    this.sessionTimeDisplay.textContent = 
                        String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
                }
            }
            
            // Update session statistics
            updateSessionStats() {
                // Calculate session P&L
                this.sessionPL = this.balance - this.lastSessionBalance;
                
                if (this.sessionPLDisplay) {
                    this.sessionPLDisplay.textContent = (this.sessionPL >= 0 ? '+' : '') + this.sessionPL + ' GA';
                    this.sessionPLDisplay.style.color = this.sessionPL >= 0 ? 'var(--comic-green)' : 'var(--comic-red)';
                }
                
                // Calculate spin rate (spins per minute)
                if (this.spinTimes.length >= 2) {
                    const timeSpan = this.spinTimes[this.spinTimes.length - 1] - this.spinTimes[0];
                    const spinRate = this.spinTimes.length / (timeSpan / 60000);
                    
                    if (this.spinRateDisplay) {
                        this.spinRateDisplay.textContent = spinRate.toFixed(1) + '/min';
                    }
                }
                
                // Calculate volatility based on win variance
                let volatility = 'Low';
                if (this.hitRate > 30) volatility = 'Medium';
                if (this.hitRate > 50) volatility = 'High';
                
                if (this.volatilityDisplay) {
                    this.volatilityDisplay.textContent = volatility;
                }
            }
            
            // Update Pro View displays
            updateProViewDisplay() {
                if (this.viewMode !== 'pro') return;
                
                // Update main stats
                if (this.hitRateDisplay) {
                    this.hitRateDisplay.textContent = this.hitRate.toFixed(1) + '%';
                }
                
                if (this.avgWinDisplay) {
                    this.avgWinDisplay.textContent = Math.round(this.avgWin) + ' GA';
                }
                
                if (this.bestComboDisplay) {
                    this.bestComboDisplay.textContent = this.bestCombo;
                }
                
                if (this.totalSpinsDisplay) {
                    this.totalSpinsDisplay.textContent = this.totalSpins;
                }
                
                // Update charts
                const winPercent = this.totalSpins > 0 ? (this.winsCount / this.totalSpins) * 100 : 0;
                const bonusPercent = this.totalSpins > 0 ? (this.bonusCount / this.totalSpins) * 100 : 0;
                const roiPercent = this.totalBetAmount > 0 ? ((this.totalWinAmount / this.totalBetAmount) - 1) * 100 : 0;
                
                if (this.winsChartDisplay) {
                    this.winsChartDisplay.style.width = Math.min(winPercent, 100) + '%';
                }
                
                if (this.winsPercentDisplay) {
                    this.winsPercentDisplay.textContent = winPercent.toFixed(1) + '%';
                }
                
                if (this.bonusChartDisplay) {
                    this.bonusChartDisplay.style.width = Math.min(bonusPercent, 100) + '%';
                }
                
                if (this.bonusPercentDisplay) {
                    this.bonusPercentDisplay.textContent = bonusPercent.toFixed(1) + '%';
                }
                
                if (this.roiChartDisplay) {
                    const roiDisplay = Math.max(0, Math.min((roiPercent + 100) / 2, 100));
                    this.roiChartDisplay.style.width = roiDisplay + '%';
                }
                
                if (this.roiPercentDisplay) {
                    this.roiPercentDisplay.textContent = (roiPercent >= 0 ? '+' : '') + roiPercent.toFixed(1) + '%';
                    this.roiPercentDisplay.style.color = roiPercent >= 0 ? 'var(--comic-green)' : 'var(--comic-red)';
                }
                
                // Update symbol frequency displays
                Object.keys(this.symbolFrequency).forEach(symbol => {
                    const display = this.symbolFrequencyDisplays[symbol];
                    if (display) {
                        display.textContent = this.symbolFrequency[symbol];
                    }
                });
                
                // Update auto spin status
                if (this.autoStatusDisplay) {
                    if (this.autoSpinActive) {
                        this.autoStatusDisplay.textContent = this.autoSpinRemaining + ' left';
                        this.autoStatusDisplay.style.color = 'var(--comic-yellow)';
                    } else {
                        this.autoStatusDisplay.textContent = 'Ready';
                        this.autoStatusDisplay.style.color = 'var(--comic-green)';
                    }
                }
            }
            
            // Add spin to history display
            addToSpinHistory(result) {
                if (!this.spinHistoryDisplay || this.viewMode !== 'pro') return;
                
                const entry = document.createElement('div');
                entry.className = 'history-entry';
                
                const symbols = result.finalSymbols ? 
                    result.finalSymbols[1].join('') : '🍒🍒🍒'; // Middle row symbols
                
                const isWin = result.winAmount > 0;
                
                entry.innerHTML = `
                    <span class="history-symbols">${symbols}</span>
                    <span class="history-result ${isWin ? 'history-win' : 'history-loss'}">
                        ${isWin ? '+' + result.winAmount : '0'} GA
                    </span>
                `;
                
                this.spinHistoryDisplay.prepend(entry);
                
                // Limit history entries
                const entries = this.spinHistoryDisplay.querySelectorAll('.history-entry');
                if (entries.length > 15) {
                    entries[entries.length - 1].remove();
                }
            }
            
            // Handle auto spin logic
            handleAutoSpin() {
                this.autoSpinRemaining--;
                
                // Check stop conditions
                const currentWin = this.balance - this.lastSessionBalance;
                const currentLoss = this.lastSessionBalance - this.balance;
                
                if (this.autoSpinRemaining <= 0 || 
                    currentWin >= this.autoStopWin || 
                    currentLoss >= this.autoStopLoss ||
                    this.balance < this.currentBet) {
                    
                    this.stopAutoSpin();
                    
                    // Show stop reason
                    let stopReason = 'Auto spin completed';
                    if (currentWin >= this.autoStopWin) stopReason = 'Win target reached!';
                    if (currentLoss >= this.autoStopLoss) stopReason = 'Loss limit reached';
                    if (this.balance < this.currentBet) stopReason = 'Insufficient balance';
                    
                    this.showNotification(stopReason);
                } else {
                    // Continue auto spinning after a delay
                    setTimeout(() => {
                        if (this.autoSpinActive) {
                            this.spin();
                        }
                    }, 1500);
                }
                
                this.updateProViewDisplay();
            }
            
            // Start auto spin
            startAutoSpin() {
                this.autoSpinActive = true;
                this.autoSpinRemaining = parseInt(this.autoSpinCountInput.value) || 10;
                this.autoStopWin = parseInt(this.autoStopWinInput.value) || 1000;
                this.autoStopLoss = parseInt(this.autoStopLossInput.value) || 500;
                this.lastSessionBalance = this.balance;
                
                // Disable manual spin button
                if (this.spinBtn) {
                    this.spinBtn.disabled = true;
                    this.spinBtn.textContent = 'AUTO SPINNING...';
                }
                
                // Start first auto spin
                this.spin();
            }
            
            // Stop auto spin
            stopAutoSpin() {
                this.autoSpinActive = false;
                this.autoSpinRemaining = 0;
                
                // Re-enable manual spin button
                if (this.spinBtn) {
                    this.spinBtn.disabled = false;
                    this.spinBtn.textContent = '🎲 SPIN';
                }
                
                this.updateProViewDisplay();
            }
            
            // Pro View button functions
            analyzeSpins() {
                if (this.totalSpins === 0) {
                    this.showNotification('No spins to analyze yet. Start playing to gather data!');
                    return;
                }
                
                let analysis = `Spin Analysis Report\n\n`;
                analysis += `Total Spins: ${this.totalSpins}\n`;
                analysis += `Hit Rate: ${this.hitRate.toFixed(1)}%\n`;
                analysis += `Average Win: ${this.avgWin.toFixed(0)} GA\n`;
                analysis += `ROI: ${this.totalBetAmount > 0 ? (((this.totalWinAmount / this.totalBetAmount) - 1) * 100).toFixed(1) : 0}%\n\n`;
                
                analysis += `Symbol Performance:\n`;
                const totalSymbols = Object.values(this.symbolFrequency).reduce((a, b) => a + b, 0);
                Object.entries(this.symbolFrequency).forEach(([symbol, count]) => {
                    const percentage = totalSymbols > 0 ? (count / totalSymbols * 100).toFixed(1) : 0;
                    const symbolMap = {
                        cherry: '🍒', lemon: '🍋', orange: '🍊', bell: '🔔',
                        bar: '📊', seven: '7️⃣', diamond: '💎', star: '⭐'
                    };
                    analysis += `${symbolMap[symbol]}: ${count} (${percentage}%)\n`;
                });
                
                analysis += `\nRecommendations:\n`;
                if (this.hitRate < 25) {
                    analysis += `• Consider reducing bet size - low hit rate detected\n`;
                } else if (this.hitRate > 40) {
                    analysis += `• Good hit rate - consider increasing bet for higher returns\n`;
                }
                
                if (this.avgWin < this.currentBet) {
                    analysis += `• Average wins are below current bet - adjust strategy\n`;
                }
                
                analysis += `• Session time: ${Math.floor((Date.now() - this.sessionStartTime) / 60000)} minutes\n`;
                analysis += `• Session P&L: ${this.sessionPL >= 0 ? '+' : ''}${this.sessionPL} GA`;
                
                this.showNotification(analysis);
            }
            
            optimizeStrategy() {
                let optimization = `Strategy Optimization\n\n`;
                
                // Analyze current performance
                const roi = this.totalBetAmount > 0 ? ((this.totalWinAmount / this.totalBetAmount) - 1) * 100 : 0;
                
                optimization += `Current Performance:\n`;
                optimization += `• ROI: ${roi.toFixed(1)}%\n`;
                optimization += `• Hit Rate: ${this.hitRate.toFixed(1)}%\n`;
                optimization += `• Average Bet: ${this.totalSpins > 0 ? (this.totalBetAmount / this.totalSpins).toFixed(0) : 0} GA\n\n`;
                
                optimization += `Optimal Strategy Recommendations:\n`;
                
                if (roi < -10) {
                    optimization += `• REDUCE bet size by 50% - significant losses detected\n`;
                    optimization += `• Set strict loss limits (${Math.floor(this.balance * 0.1)} GA max)\n`;
                } else if (roi > 20) {
                    optimization += `• INCREASE bet size by 25% - profitable session\n`;
                    optimization += `• Set win targets to lock in profits\n`;
                } else {
                    optimization += `• MAINTAIN current bet size - balanced performance\n`;
                    optimization += `• Use auto-spin for consistent betting\n`;
                }
                
                optimization += `\nBetting Strategy:\n`;
                const optimalBet = Math.max(5, Math.min(50, Math.floor(this.balance * 0.02)));
                optimization += `• Recommended bet size: ${optimalBet} GA (2% of bankroll)\n`;
                optimization += `• Auto-spin settings: 20 spins, stop at +${Math.floor(this.balance * 0.1)} GA win\n`;
                optimization += `• Loss limit: ${Math.floor(this.balance * 0.05)} GA\n\n`;
                
                optimization += `Pro Tips:\n`;
                optimization += `• Play during peak performance times\n`;
                optimization += `• Track symbol patterns for hot/cold streaks\n`;
                optimization += `• Use the paytable to identify high-value symbols\n`;
                optimization += `• Take regular breaks to maintain focus`;
                
                this.showNotification(optimization);
                
                // Auto-apply recommended bet if user wants
                if (this.betInput) {
                    this.betInput.value = optimalBet;
                    this.currentBet = optimalBet;
                    this.updateDisplay();
                }
            }
            
            showSymbolStats() {
                let stats = `Symbol Statistics\n\n`;
                
                const totalSymbols = Object.values(this.symbolFrequency).reduce((a, b) => a + b, 0);
                const symbolMap = {
                    cherry: '🍒 Cherry', lemon: '🍋 Lemon', orange: '🍊 Orange', bell: '🔔 Bell',
                    bar: '📊 Bar', seven: '7️⃣ Seven', diamond: '💎 Diamond', star: '⭐ Star'
                };
                
                // Sort symbols by frequency
                const sortedSymbols = Object.entries(this.symbolFrequency)
                    .sort(([,a], [,b]) => b - a);
                
                stats += `Total Symbols Tracked: ${totalSymbols}\n\n`;
                
                stats += `Frequency Ranking:\n`;
                sortedSymbols.forEach(([symbol, count], index) => {
                    const percentage = totalSymbols > 0 ? (count / totalSymbols * 100).toFixed(1) : 0;
                    stats += `${index + 1}. ${symbolMap[symbol]}: ${count} (${percentage}%)\n`;
                });
                
                stats += `\nPaytable Analysis:\n`;
                stats += `High Value Symbols (💎⭐7️⃣):\n`;
                const highValueCount = this.symbolFrequency.diamond + this.symbolFrequency.star + this.symbolFrequency.seven;
                const highValuePercent = totalSymbols > 0 ? (highValueCount / totalSymbols * 100).toFixed(1) : 0;
                stats += `  Frequency: ${highValueCount} (${highValuePercent}%)\n`;
                
                stats += `Medium Value Symbols (🔔📊🍊):\n`;
                const mediumValueCount = this.symbolFrequency.bell + this.symbolFrequency.bar + this.symbolFrequency.orange;
                const mediumValuePercent = totalSymbols > 0 ? (mediumValueCount / totalSymbols * 100).toFixed(1) : 0;
                stats += `  Frequency: ${mediumValueCount} (${mediumValuePercent}%)\n`;
                
                stats += `Low Value Symbols (🍋🍒):\n`;
                const lowValueCount = this.symbolFrequency.lemon + this.symbolFrequency.cherry;
                const lowValuePercent = totalSymbols > 0 ? (lowValueCount / totalSymbols * 100).toFixed(1) : 0;
                stats += `  Frequency: ${lowValueCount} (${lowValuePercent}%)\n\n`;
                
                stats += `Insights:\n`;
                if (highValuePercent > 25) {
                    stats += `• High frequency of premium symbols detected!\n`;
                    stats += `• Consider increasing bet size for better returns\n`;
                } else if (highValuePercent < 15) {
                    stats += `• Low premium symbol frequency\n`;
                    stats += `• Focus on volume with smaller bets\n`;
                } else {
                    stats += `• Balanced symbol distribution\n`;
                    stats += `• Current strategy appears optimal\n`;
                }
                
                this.showNotification(stats);
            }
            
            exportGameData() {
                const gameData = {
                    timestamp: new Date().toISOString(),
                    session: {
                        totalSpins: this.totalSpins,
                        sessionTime: Date.now() - this.sessionStartTime,
                        balance: this.balance,
                        sessionPL: this.sessionPL
                    },
                    statistics: {
                        hitRate: this.hitRate,
                        avgWin: this.avgWin,
                        bestCombo: this.bestCombo,
                        biggestWin: this.biggestWinAmount,
                        totalWinAmount: this.totalWinAmount,
                        totalBetAmount: this.totalBetAmount,
                        winsCount: this.winsCount,
                        bonusCount: this.bonusCount
                    },
                    symbolFrequency: this.symbolFrequency,
                    settings: {
                        viewMode: this.viewMode,
                        autoStopWin: this.autoStopWin,
                        autoStopLoss: this.autoStopLoss
                    }
                };
                
                // Create downloadable JSON file
                const dataStr = JSON.stringify(gameData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = `comic-fortune-data-${Date.now()}.json`;
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                this.showNotification('Game data exported successfully! 📊');
            }
            
            // Override showNotification for enhanced Pro View notifications
            showNotification(message) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    background: linear-gradient(45deg, var(--comic-red), var(--comic-yellow));
                    color: var(--comic-dark);
                    padding: 1rem;
                    border-radius: 10px;
                    border: 3px solid var(--comic-gold);
                    z-index: 10000;
                    font-weight: bold;
                    box-shadow: 0 8px 16px rgba(0,0,0,0.4);
                    animation: slideIn 0.3s ease;
                    max-width: 90%;
                    width: 400px;
                    max-height: 80vh;
                    overflow-y: auto;
                    white-space: pre-line;
                    font-size: 0.9rem;
                    font-family: 'Poppins', sans-serif;
                `;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                // Longer display time for detailed messages
                const displayTime = message.length > 100 ? 10000 : 5000;
                
                setTimeout(() => {
                    notification.style.animation = 'slideIn 0.3s ease reverse';
                    setTimeout(() => notification.remove(), 300);
                }, displayTime);
            }
        }

        // Initialize the enhanced game when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log("DOM loaded, initializing Enhanced Comic Fortune game...");
            try {
                window.comicFortuneGame = new EnhancedComicFortuneGame();
                
                // Global functions for onclick attributes
                window.closeResult = function() {
                    if (window.comicFortuneGame) {
                        window.comicFortuneGame.closeResult();
                    }
                };
                
                console.log("Enhanced Comic Fortune game initialized successfully!");
            } catch (error) {
                console.error("Error initializing Enhanced Comic Fortune game:", error);
            }
        });
    </script>
</body>
</html>