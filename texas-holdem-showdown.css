/* Texas Hold'em Showdown - Tournament Edition */

:root {
    /* Main color palette */
    --poker-green: #0d5016;
    --poker-green-light: #1a7527;
    --poker-green-dark: #062208;
    --felt-color: #1a7527;
    --felt-gradient: radial-gradient(ellipse at center, var(--poker-green) 0%, var(--poker-green-dark) 100%);
    
    /* Tournament theme colors */
    --tournament-primary: #1a237e;
    --tournament-secondary: #4a148c;
    --tournament-accent: #ff6d00;
    --tournament-gold: #ffc107;
    --tournament-silver: #9e9e9e;
    --tournament-bronze: #a1887f;
    
    /* Chip colors */
    --chip-red: #d32f2f;
    --chip-blue: #1976d2;
    --chip-green: #388e3c;
    --chip-black: #424242;
    --chip-white: #fafafa;
    --chip-purple: #7b1fa2;
    
    /* Card colors */
    --card-bg: #ffffff;
    --card-red: #d32f2f;
    --card-black: #212529;
    
    /* UI theme colors */
    --primary-dark: #0f1728;
    --primary-medium: #16213e;
    --primary-light: #1a1a2e;
    --accent-blue: #0097e6;
    --accent-gold: #ffc107;
    --accent-purple: #9c27b0;
    --text-light: #ffffff;
    --text-medium: #e9ecef;
    --text-tertiary: #adb5bd;
    --text-muted: #6c757d;
    --border-color: rgba(255, 255, 255, 0.1);
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    
    /* Status colors */
    --win-color: #4caf50;
    --lose-color: #f44336;
    --fold-color: #757575;
    --allin-color: #ff9800;
    --showdown-color: #e91e63;
    
    /* Glassmorphism effects */
    --glass-bg: rgba(22, 33, 62, 0.8);
    --glass-border: rgba(255, 255, 255, 0.08);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    
    /* Tournament cards */
    --card-featured: linear-gradient(135deg, #4a148c, #7b1fa2);
    --card-premium: linear-gradient(135deg, #ff6f00, #ff9800);
    --card-standard: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    
    /* Spacing and layout */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    
    /* Mobile-specific variables */
    --mobile-padding: 12px;
    --mobile-gap: 8px;
    --touch-target: 44px;
    --card-width: 60px;
    --card-height: 84px;
    --card-width-mobile: 40px;
    --card-height-mobile: 56px;
    
    /* Tournament-specific variables */
    --tournament-card-height: 240px;
    --tournament-grid-gap: 16px;
    --table-card-height: 180px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Montserrat', sans-serif;
    background: linear-gradient(135deg, var(--tournament-primary), var(--tournament-secondary));
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Pro View Mode Toggle */
body.pro-view-active .pro-analytics {
    display: block !important;
}

.game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.hidden {
    display: none !important;
}

/* ===== HEADER ===== */
.game-header {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-gap);
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(0deg, transparent, rgba(74, 20, 140, 0.1), transparent);
}

.header-left {
    order: 1;
}

.header-center {
    order: 2;
    text-align: center;
}

.header-right {
    order: 3;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--mobile-gap);
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
    padding: 8px;
    border-radius: var(--border-radius);
}

.back-link i {
    margin-right: 6px;
}

.back-link:hover {
    color: var(--accent-blue);
    background: rgba(33, 150, 243, 0.1);
}

.back-text {
    display: none;
}

.game-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--tournament-gold);
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: 14px;
    font-weight: 300;
    color: var(--text-light);
    opacity: 0.8;
}

/* View Mode Toggle */
.view-mode-toggle {
    display: flex;
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 2px;
    border: 1px solid var(--border-color);
}

.view-btn {
    background: transparent;
    border: none;
    color: var(--text-medium);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: var(--transition-fast);
    min-height: var(--touch-target);
}

.view-btn.active {
    background: var(--accent-blue);
    color: var(--text-light);
}

.view-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
}

.view-text {
    display: none;
}

.info-btn, .verify-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    transition: var(--transition);
    min-height: var(--touch-target);
    gap: 4px;
}

.info-btn:hover, .verify-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-blue);
}

.btn-text {
    display: none;
}

/* ===== TOURNAMENT SELECTION ===== */
.tournament-selection {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.tournament-header {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px;
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.tournament-header h2 {
    font-size: 20px;
    color: var(--tournament-gold);
    text-align: center;
}

.player-stats {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.stat-item i {
    color: var(--tournament-accent);
}

.tournament-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--tournament-grid-gap);
}

.tournament-card {
    background: var(--card-standard);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    box-shadow: var(--glass-shadow);
    position: relative;
    height: var(--tournament-card-height);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.tournament-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.5);
}

.tournament-card.featured {
    background: var(--card-featured);
    border: 2px solid var(--tournament-gold);
}

.tournament-card.premium {
    background: var(--card-premium);
    border: 2px solid var(--tournament-gold);
}

.featured-badge, .premium-badge {
    position: absolute;
    top: 10px;
    right: 0;
    background: var(--tournament-gold);
    color: var(--primary-dark);
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 4px 0 0 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.premium-badge {
    background: linear-gradient(to right, var(--tournament-gold), #ff9800);
}

.tournament-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.tournament-card-header h3 {
    font-size: 18px;
    color: var(--text-light);
}

.tournament-status {
    font-size: 12px;
    color: var(--text-tertiary);
}

.tournament-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.detail-label {
    font-size: 12px;
    color: var(--text-tertiary);
}

.detail-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
}

.tournament-footer {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.players-registered {
    display: flex;
    flex-direction: column;
    gap: 8px;
    font-size: 12px;
    color: var(--text-tertiary);
}

.player-avatars {
    display: flex;
    gap: 4px;
}

.player-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-dark);
    border: 1px solid var(--border-color);
}

.player-avatar.filled {
    background: var(--accent-blue);
    border-color: var(--text-light);
}

.progress-bar {
    height: 8px;
    background: var(--primary-dark);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 4px;
}

.progress {
    height: 100%;
    background: var(--accent-blue);
    transition: width 0.3s ease;
}

.register-btn {
    background: var(--tournament-accent);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    padding: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.register-btn:hover {
    background: #e65100;
    transform: translateY(-2px);
}

/* Achievements Section */
.achievements-section {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.achievements-section h3 {
    font-size: 18px;
    color: var(--tournament-gold);
    margin-bottom: 12px;
    text-align: center;
}

.badges-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
}

.badge-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    text-align: center;
    filter: grayscale(100%) opacity(0.6);
    transition: var(--transition);
}

.badge-item.earned {
    filter: none;
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.badge-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.badge-item.earned .badge-icon {
    background: var(--tournament-gold);
    color: var(--primary-dark);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.badge-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
}

.badge-description {
    font-size: 10px;
    color: var(--text-tertiary);
}

/* ===== TOURNAMENT LOBBY ===== */
.tournament-lobby {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.tournament-info-bar {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    background: var(--tournament-primary);
    border-radius: var(--border-radius);
    padding: 12px;
    gap: 10px;
}

.tournament-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--tournament-gold);
}

.tournament-time, .player-count, .blind-level {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
}

.tournament-time i, .player-count i, .blind-level i {
    color: var(--tournament-accent);
}

.tournament-tables, .tournament-standings {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.tables-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.tables-header h3 {
    font-size: 18px;
    color: var(--tournament-gold);
}

.tables-actions {
    display: flex;
    gap: 8px;
}

.tables-filter-btn, .tables-refresh-btn {
    background: var(--primary-medium);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.tables-filter-btn:hover, .tables-refresh-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-blue);
}

.tables-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.table-card {
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 12px;
    height: var(--table-card-height);
    display: flex;
    flex-direction: column;
    gap: 10px;
    transition: var(--transition);
}

.table-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.table-card.your-table {
    border: 2px solid var(--tournament-gold);
    background: linear-gradient(135deg, var(--primary-medium), var(--tournament-primary));
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h4 {
    font-size: 16px;
    color: var(--text-light);
}

.table-status {
    font-size: 12px;
    color: var(--text-tertiary);
    padding: 2px 8px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12px;
}

.your-table-label {
    background: var(--tournament-gold);
    color: var(--primary-dark);
}

.table-preview {
    position: relative;
    height: 80px;
    background: var(--felt-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--felt-gradient);
    border-radius: var(--border-radius);
}

.table-players {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
    width: 90%;
}

.player-seat {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-dark);
    border: 1px solid var(--border-color);
}

.player-seat.filled {
    background: var(--accent-blue);
    border-color: var(--text-light);
}

.player-seat.you {
    background: var(--tournament-gold);
    border-color: white;
    transform: scale(1.2);
    box-shadow: 0 0 10px var(--tournament-gold);
}

.table-details {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-tertiary);
}

.spectate-btn, .join-table-btn {
    background: var(--accent-blue);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    padding: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: auto;
}

.spectate-btn:hover {
    background: #0277bd;
}

.join-table-btn {
    background: var(--tournament-accent);
}

.join-table-btn:hover {
    background: #e65100;
}

/* Tournament Standings */
.tournament-standings h3 {
    font-size: 18px;
    color: var(--tournament-gold);
    margin-bottom: 12px;
}

.standings-table {
    width: 100%;
    border-collapse: collapse;
}

.standings-header {
    display: grid;
    grid-template-columns: 0.5fr 2fr 1fr 1fr;
    background: var(--primary-dark);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 8px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-tertiary);
}

.standings-row {
    display: grid;
    grid-template-columns: 0.5fr 2fr 1fr 1fr;
    padding: 8px;
    font-size: 13px;
    border-bottom: 1px solid var(--border-color);
}

.standings-row:last-child {
    border-bottom: none;
}

.standings-row.you {
    background: rgba(255, 215, 0, 0.1);
    border-left: 3px solid var(--tournament-gold);
    font-weight: 600;
}

.rank-col, .player-col, .chips-col, .table-col {
    display: flex;
    align-items: center;
}

.tournament-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.leave-tournament-btn, .tournament-rules-btn {
    flex: 1;
    padding: 12px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.leave-tournament-btn {
    background: var(--danger-color);
    color: white;
    border: none;
}

.tournament-rules-btn {
    background: var(--primary-medium);
    color: var(--text-light);
    border: 1px solid var(--border-color);
}

.leave-tournament-btn:hover {
    background: #c62828;
}

.tournament-rules-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-blue);
}

/* ===== GAME AREA ===== */
.main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 16px;
}

/* Mobile Status Bar */
.mobile-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--tournament-primary);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-item i {
    color: var(--tournament-accent);
}

/* Tournament Sidebar */
.tournament-sidebar {
    display: none;
}

/* Table Info Bar */
.table-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--primary-dark);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    font-size: 12px;
    margin-bottom: 8px;
}

.table-name {
    font-weight: 600;
    color: var(--tournament-gold);
}

.blind-info {
    display: flex;
    gap: 8px;
}

.small-blind, .big-blind {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 12px;
}

.hand-number {
    color: var(--text-tertiary);
}

/* Poker Table */
.poker-table {
    background: var(--felt-gradient);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.3), 0 4px 15px rgba(0, 0, 0, 0.4);
    position: relative;
    min-height: 400px;
    border: 3px solid #8B4513;
    overflow: hidden;
}

.poker-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none"/><path d="M0,50 Q50,0 100,50 Q50,100 0,50" stroke="rgba(255,255,255,0.03)" stroke-width="1" fill="none"/></svg>') repeat;
    pointer-events: none;
}

/* Community Cards */
.community-cards {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.community-cards-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.cards-container {
    display: flex;
    gap: 6px;
    justify-content: center;
}

.card-slot {
    width: var(--card-width-mobile);
    height: var(--card-height-mobile);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: var(--transition);
}

.card-slot:hover {
    border-color: var(--tournament-gold);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
}

.card {
    width: 100%;
    height: 100%;
    background: var(--card-bg);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px;
    position: relative;
    border: 1px solid #ddd;
}

.card-back {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--tournament-primary), var(--tournament-secondary));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    position: relative;
    overflow: hidden;
}

.card-back::before {
    content: '♠';
    position: absolute;
    font-size: 18px;
    opacity: 0.3;
}

.card-value {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
}

.card-suit {
    font-size: 10px;
    line-height: 1;
}

.card-red .card-value,
.card-red .card-suit {
    color: var(--card-red);
}

.card-black .card-value,
.card-black .card-suit {
    color: var(--card-black);
}

/* Pot Display */
.pot-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.pot-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.pot-amount {
    font-size: 18px;
    font-weight: 700;
    color: var(--tournament-gold);
    margin-bottom: 8px;
}

.pot-chips {
    display: flex;
    justify-content: center;
    gap: 4px;
}

.chip {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.chip-red {
    background: var(--chip-red);
}

.chip-blue {
    background: var(--chip-blue);
}

.chip-green {
    background: var(--chip-green);
}

/* Players Grid */
.players-grid {
    position: relative;
    width: 100%;
    height: 100%;
}

.player-seat {
    position: absolute;
    width: 100px;
    background: var(--glass-bg);
    border-radius: var(--border-radius);
    padding: 8px;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    transition: var(--transition);
}

.player-seat:hover {
    border-color: var(--tournament-gold);
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.2);
}

/* Player positioning around table */
.player-seat#player0 {
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
}

.player-seat#player1 {
    bottom: 30%;
    right: 10px;
}

.player-seat#player2 {
    top: 30%;
    right: 10px;
}

.player-seat#player3 {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
}

.player-seat#player4 {
    top: 30%;
    left: 10px;
}

.player-seat#player5 {
    bottom: 30%;
    left: 10px;
}

.player-info {
    text-align: center;
    margin-bottom: 6px;
}

.player-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 2px;
}

.player-chips {
    font-size: 10px;
    color: var(--tournament-gold);
    margin-bottom: 2px;
}

.player-status {
    font-size: 9px;
    color: var(--text-medium);
    margin-bottom: 2px;
}

.ai-difficulty {
    font-size: 8px;
    color: var(--accent-blue);
    background: rgba(33, 150, 243, 0.1);
    padding: 1px 4px;
    border-radius: 3px;
}

.player-cards {
    display: flex;
    gap: 3px;
    justify-content: center;
    margin-bottom: 6px;
}

.player-cards .card-slot {
    width: 24px;
    height: 34px;
}

.player-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.action-btn {
    padding: 6px 8px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    min-height: 32px;
    flex: 1 0 calc(50% - 4px);
}

.fold-btn {
    background: var(--danger-color);
    color: white;
}

.fold-btn:hover {
    background: #d32f2f;
    transform: translateY(-1px);
}

.call-btn {
    background: var(--accent-blue);
    color: white;
}

.call-btn:hover {
    background: #1976d2;
    transform: translateY(-1px);
}

.raise-btn {
    background: var(--success-color);
    color: white;
}

.raise-btn:hover {
    background: #388e3c;
    transform: translateY(-1px);
}

.check-btn {
    background: var(--warning-color);
    color: white;
}

.check-btn:hover {
    background: #f57c00;
    transform: translateY(-1px);
}

.showdown-btn {
    background: var(--showdown-color);
    color: white;
    flex: 1 0 100%;
}

.showdown-btn:hover {
    background: #c2185b;
    transform: translateY(-1px);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Dealer Button */
.dealer-button {
    position: absolute;
    width: 30px;
    height: 30px;
    background: var(--tournament-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-dark);
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    top: 60%;
    right: 15%;
    border: 2px solid white;
}

/* Game Controls */
.game-controls {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.betting-controls {
    margin-bottom: 16px;
}

.bet-amount-container label {
    display: block;
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 8px;
    font-weight: 600;
}

.bet-input-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.bet-slider {
    flex: 1;
    height: 6px;
    background: var(--primary-dark);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.bet-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: var(--tournament-gold);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bet-input {
    width: 80px;
    padding: 8px;
    background: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-light);
    text-align: center;
    font-size: 14px;
}

.quick-bet-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.quick-bet {
    padding: 6px 12px;
    background: var(--primary-medium);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-bet:hover {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
}

/* Game Info Panel */
.game-info-panel {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.info-section {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 12px;
}

.info-section h3 {
    font-size: 14px;
    color: var(--tournament-gold);
    margin-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 4px;
}

.hand-strength {
    margin-bottom: 8px;
}

.strength-label {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 4px;
}

.strength-bars {
    display: flex;
    gap: 2px;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: var(--primary-dark);
    border-radius: 2px;
}

.strength-bar.active {
    background: var(--tournament-gold);
}

.win-probability {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prob-label {
    font-size: 12px;
    color: var(--text-medium);
}

.prob-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--success-color);
}

.blind-amounts {
    display: flex;
    justify-content: space-between;
}

.blind-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.blind-label {
    font-size: 11px;
    color: var(--text-medium);
}

.blind-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--tournament-gold);
}

.round-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.round-phase {
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-blue);
}

.players-active, .next-action {
    font-size: 12px;
    color: var(--text-medium);
}

/* Pro Analytics - Hidden by default */
.pro-analytics {
    display: none;
    background: linear-gradient(135deg, var(--tournament-secondary), #4a148c);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    margin-top: 16px;
    box-shadow: var(--glass-shadow);
}

.analytics-section h3,
.opponent-analysis h3,
.hand-history h3 {
    color: var(--text-light);
    font-size: 16px;
    margin-bottom: 12px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.analytics-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
    text-align: center;
}

.analytics-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.analytics-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
}

.opponent-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.opponent-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
}

.opponent-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 2px;
}

.opponent-tendency {
    font-size: 11px;
    color: var(--tournament-gold);
    margin-bottom: 2px;
}

.opponent-notes {
    font-size: 10px;
    color: var(--text-medium);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.history-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-hand {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
}

.history-result {
    font-size: 11px;
    color: var(--success-color);
}

.history-action {
    font-size: 10px;
    color: var(--text-medium);
}

/* Actions Panel */
.actions-panel {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.game-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.fast-fold-btn {
    background: var(--danger-color);
    color: white;
}

.auto-check-btn {
    background: var(--warning-color);
    color: white;
}

.sit-out-btn {
    background: var(--accent-blue);
    color: white;
}

.leave-btn {
    background: var(--primary-dark);
    color: white;
}

.table-options {
    display: flex;
    justify-content: space-between;
}

.table-option-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background: var(--primary-medium);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.table-option-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-blue);
    transform: translateY(-2px);
}

/* Showdown Overlay */
.showdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 16px;
}

.showdown-container {
    background: linear-gradient(135deg, var(--tournament-primary), var(--tournament-secondary));
    width: 100%;
    max-width: 500px;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 0 30px rgba(255, 109, 0, 0.5);
    border: 2px solid var(--tournament-gold);
}

.showdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--tournament-gold);
    padding: 12px 20px;
}

.showdown-header h2 {
    color: var(--primary-dark);
    font-size: 22px;
    font-weight: 700;
}

.bonus-multiplier {
    font-size: 24px;
    font-weight: 800;
    color: var(--primary-dark);
    background: var(--tournament-accent);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border: 2px solid white;
}

.showdown-cards {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.your-hand, .community-hand {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 12px;
}

.your-hand h3, .community-hand h3 {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 10px;
    text-align: center;
}

.showdown-hand-cards, .showdown-community-cards {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 10px;
}

.hand-name {
    text-align: center;
    font-size: 14px;
    color: var(--tournament-gold);
    font-weight: 600;
}

.showdown-result {
    padding: 0 20px 20px;
    text-align: center;
}

.result-text {
    font-size: 18px;
    color: var(--text-light);
    margin-bottom: 8px;
}

.bonus-amount {
    font-size: 32px;
    font-weight: 700;
    color: var(--tournament-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.continue-btn {
    width: 100%;
    padding: 16px;
    background: var(--tournament-accent);
    color: white;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.continue-btn:hover {
    background: #e65100;
}

/* Tournament Elimination Overlay */
.elimination-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 16px;
}

.elimination-container {
    background: linear-gradient(135deg, var(--tournament-primary), var(--tournament-secondary));
    width: 100%;
    max-width: 500px;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.elimination-header {
    background: var(--primary-dark);
    padding: 16px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.elimination-header h2 {
    color: var(--tournament-gold);
    font-size: 22px;
}

.elimination-info {
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.position-indicator {
    text-align: center;
}

.position-number {
    font-size: 36px;
    font-weight: 700;
    color: var(--tournament-gold);
}

.position-text {
    font-size: 14px;
    color: var(--text-medium);
}

.elimination-details {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.last-hand {
    padding: 0 20px 20px;
}

.last-hand h3 {
    font-size: 16px;
    color: var(--tournament-gold);
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 4px;
}

.last-hand-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;
}

.your-last-cards, .opponent-last-cards {
    display: flex;
    align-items: center;
    gap: 8px;
}

.your-last-cards span, .opponent-last-cards span {
    font-size: 12px;
    color: var(--text-tertiary);
}

.last-hand-board {
    display: flex;
    align-items: center;
    gap: 8px;
}

.last-hand-board span {
    font-size: 12px;
    color: var(--text-tertiary);
}

.elimination-achievements {
    padding: 0 20px 20px;
}

.elimination-achievements h3 {
    font-size: 16px;
    color: var(--tournament-gold);
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 4px;
}

.achievement-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 10px;
}

.achievement-icon {
    width: 40px;
    height: 40px;
    background: var(--tournament-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-dark);
    font-size: 18px;
}

.achievement-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
}

.achievement-description {
    font-size: 12px;
    color: var(--text-tertiary);
}

.elimination-buttons {
    display: flex;
    border-top: 1px solid var(--border-color);
}

.restart-btn, .lobby-btn {
    flex: 1;
    padding: 16px;
    border: none;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.restart-btn {
    background: var(--tournament-accent);
    color: white;
}

.lobby-btn {
    background: var(--primary-dark);
    color: var(--text-light);
}

.restart-btn:hover {
    background: #e65100;
}

.lobby-btn:hover {
    background: var(--primary-medium);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 16px;
}

.modal-content {
    background: var(--primary-medium);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--accent-blue);
}

.modal-header {
    background: var(--primary-dark);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--tournament-gold);
    font-size: 18px;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    width: var(--touch-target);
    height: var(--touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal-btn:hover {
    color: var(--accent-blue);
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 16px;
    overflow-y: auto;
    max-height: calc(90vh - 60px);
}

.tutorial-section,
.rules-section {
    margin-bottom: 16px;
}

.tutorial-section h3,
.rules-section h3 {
    color: var(--accent-blue);
    font-size: 16px;
    margin-bottom: 8px;
}

.tutorial-section p,
.tutorial-section li,
.rules-section p,
.rules-section li {
    font-size: 13px;
    margin-bottom: 4px;
    color: var(--text-light);
}

.tutorial-section ul,
.tutorial-section ol,
.rules-section ul,
.rules-section ol {
    padding-left: 16px;
    margin-top: 4px;
}

.rules-table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 12px;
}

.rules-table th {
    background: var(--primary-dark);
    padding: 8px;
    text-align: left;
    color: var(--text-medium);
}

.rules-table td {
    padding: 6px 8px;
    border-bottom: 1px solid var(--border-color);
}

/* Player status indicators */
.player-seat.folded {
    opacity: 0.5;
}

.player-seat.all-in .player-info {
    border: 2px solid var(--warning-color);
    border-radius: var(--border-radius);
}

.player-seat.winner .player-info {
    border: 2px solid var(--success-color);
    border-radius: var(--border-radius);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
}

/* Enhanced Mobile Styles for Tournament Edition */
@media (max-width: 767px) {
    :root {
        --mobile-padding: 8px;
        --mobile-gap: 6px;
        --card-width-mobile: 32px;
        --card-height-mobile: 45px;
        --mobile-header-height: 50px;
        --mobile-tournament-panel-height: 200px;
        --mobile-pro-panel-height: 240px;
        --touch-target-min: 44px;
    }
    
    /* Enhanced Mobile Header */
    .game-header {
        position: sticky;
        top: 0;
        z-index: 100;
        background: linear-gradient(135deg, var(--tournament-primary), var(--tournament-secondary));
        border-radius: 0 0 var(--border-radius) var(--border-radius);
        margin-bottom: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    .header-left, .header-right {
        flex: none;
    }
    
    .header-center {
        flex: 1;
        padding: 0 8px;
    }
    
    .game-title {
        font-size: 18px;
        margin-bottom: 2px;
    }
    
    .game-subtitle {
        font-size: 11px;
    }
    
    .view-mode-toggle {
        position: relative;
    }
    
    .view-btn {
        padding: 6px 8px;
        font-size: 10px;
        min-width: 50px;
        min-height: var(--touch-target-min);
    }
    
    .info-btn, .verify-btn {
        padding: 6px 8px;
        font-size: 10px;
        min-height: var(--touch-target-min);
    }
    
    /* Enhanced Mobile Tournament Selection */
    .tournament-selection {
        padding: 0 var(--mobile-padding);
    }
    
    .tournament-header {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .tournament-header h2 {
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    .player-stats {
        gap: 8px;
    }
    
    .stat-item {
        font-size: 12px;
        min-height: var(--touch-target-min);
        padding: 4px 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        justify-content: center;
    }
    
    .tournament-grid {
        gap: 12px;
    }
    
    .tournament-card {
        height: auto;
        min-height: 200px;
        padding: 12px;
        touch-action: manipulation;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .tournament-card:active {
        transform: scale(0.98);
    }
    
    .tournament-card-header h3 {
        font-size: 16px;
    }
    
    .tournament-status {
        font-size: 11px;
    }
    
    .tournament-details {
        gap: 8px;
        margin-bottom: 12px;
    }
    
    .detail-label {
        font-size: 10px;
    }
    
    .detail-value {
        font-size: 12px;
    }
    
    .register-btn {
        min-height: var(--touch-target-min);
        font-size: 14px;
        font-weight: 600;
        border-radius: var(--border-radius);
        transition: all 0.2s ease;
    }
    
    .register-btn:active {
        transform: scale(0.96);
    }
    
    /* Enhanced Tournament Lobby Mobile */
    .tournament-lobby {
        padding: 0 var(--mobile-padding);
    }
    
    .tournament-info-bar {
        flex-direction: column;
        gap: 8px;
        padding: 10px;
        text-align: center;
    }
    
    .tournament-name {
        font-size: 14px;
        order: 1;
    }
    
    .tournament-time, .player-count, .blind-level {
        font-size: 11px;
        order: 2;
    }
    
    .tables-grid {
        gap: 10px;
    }
    
    .table-card {
        height: 160px;
        padding: 10px;
        border-radius: var(--border-radius);
        touch-action: manipulation;
    }
    
    .table-card:active {
        transform: scale(0.98);
    }
    
    .table-header h4 {
        font-size: 14px;
    }
    
    .table-status {
        font-size: 10px;
    }
    
    .table-preview {
        height: 60px;
        margin: 8px 0;
    }
    
    .player-seat {
        width: 16px;
        height: 16px;
    }
    
    .table-details {
        font-size: 10px;
        margin-bottom: 8px;
    }
    
    .spectate-btn, .join-table-btn {
        min-height: var(--touch-target-min);
        font-size: 12px;
        border-radius: var(--border-radius);
    }
    
    .spectate-btn:active, .join-table-btn:active {
        transform: scale(0.96);
    }
    
    /* Enhanced Mobile Standings */
    .standings-header, .standings-row {
        grid-template-columns: 0.4fr 1.8fr 0.8fr 1fr;
        padding: 6px;
        font-size: 11px;
    }
    
    .standings-row.you {
        border-left-width: 4px;
        background: rgba(255, 215, 0, 0.15);
    }
    
    /* Enhanced Mobile Game Area */
    .main-content {
        padding: 0 var(--mobile-padding);
        gap: 8px;
    }
    
    .mobile-status-bar {
        padding: 8px 12px;
        border-radius: var(--border-radius);
        margin-bottom: 8px;
        position: sticky;
        top: calc(var(--mobile-header-height) + 8px);
        z-index: 90;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .status-item {
        font-size: 11px;
        font-weight: 600;
    }
    
    .status-item i {
        font-size: 12px;
    }
    
    /* Enhanced Mobile Table Info Bar */
    .table-info-bar {
        padding: 6px 10px;
        font-size: 11px;
        border-radius: var(--border-radius);
        margin-bottom: 6px;
    }
    
    .table-name {
        font-size: 12px;
    }
    
    .hand-number {
        font-size: 10px;
    }
    
    /* Enhanced Mobile Poker Table */
    .poker-table {
        min-height: 300px;
        padding: 12px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 8px;
        position: relative;
        overflow: visible;
    }
    
    .community-cards {
        top: 25%;
    }
    
    .community-cards-label {
        font-size: 10px;
        margin-bottom: 6px;
        font-weight: 600;
    }
    
    .cards-container {
        gap: 3px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .card-slot {
        width: var(--card-width-mobile);
        height: var(--card-height-mobile);
        border-width: 1px;
        border-radius: 4px;
    }
    
    .community-card {
        width: 28px;
        height: 40px;
    }
    
    /* Enhanced Mobile Pot Display */
    .pot-display {
        padding: 8px 12px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        border-radius: var(--border-radius);
    }
    
    .pot-label {
        font-size: 10px;
        font-weight: 600;
    }
    
    .pot-amount {
        font-size: 16px;
        font-weight: 700;
    }
    
    .chip {
        width: 16px;
        height: 16px;
        border-width: 1px;
    }
    
    /* Enhanced Mobile Player Seats */
    .player-seat {
        width: 80px;
        padding: 6px;
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
        border: 1px solid var(--glass-border);
    }
    
    .player-seat#player0 {
        width: 100px;
        bottom: 8px;
    }
    
    .player-name {
        font-size: 10px;
        font-weight: 700;
        margin-bottom: 2px;
    }
    
    .player-chips {
        font-size: 9px;
        font-weight: 600;
        margin-bottom: 2px;
    }
    
    .player-status {
        font-size: 8px;
        margin-bottom: 2px;
    }
    
    .ai-difficulty {
        font-size: 7px;
        padding: 1px 3px;
        border-radius: 2px;
    }
    
    .player-cards .card-slot {
        width: 18px;
        height: 25px;
    }
    
    /* Enhanced Mobile Action Buttons */
    .player-actions {
        gap: 3px;
        margin-top: 4px;
    }
    
    .action-btn {
        font-size: 8px;
        padding: 4px 6px;
        min-height: 28px;
        border-radius: 4px;
        font-weight: 600;
        transition: all 0.2s ease;
        flex: 1 0 calc(50% - 3px);
        touch-action: manipulation;
    }
    
    .action-btn:active {
        transform: scale(0.95);
    }
    
    .action-btn i {
        font-size: 7px;
        margin-right: 2px;
    }
    
    .showdown-btn {
        flex: 1 0 100%;
        background: linear-gradient(135deg, var(--showdown-color), #ad1457);
        position: relative;
        overflow: hidden;
    }
    
    .showdown-btn::after {
        content: '✨';
        position: absolute;
        top: 2px;
        right: 4px;
        font-size: 8px;
        animation: sparkle 1.5s ease-in-out infinite;
    }
    
    @keyframes sparkle {
        0%, 100% { opacity: 0.5; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.2); }
    }
    
    /* Enhanced Mobile Dealer Button */
    .dealer-button {
        width: 24px;
        height: 24px;
        font-size: 10px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
        border: 2px solid white;
    }
    
    /* Enhanced Mobile Game Controls */
    .game-controls {
        padding: 12px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 8px;
    }
    
    .bet-amount-container label {
        font-size: 12px;
        margin-bottom: 6px;
        font-weight: 600;
    }
    
    .bet-input-group {
        gap: 8px;
        margin-bottom: 8px;
    }
    
    .bet-slider {
        height: 8px;
        background: linear-gradient(to right, var(--primary-dark), var(--tournament-accent));
        border-radius: 4px;
        outline: none;
        -webkit-appearance: none;
    }
    
    .bet-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 24px;
        height: 24px;
        background: var(--tournament-gold);
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        border: 2px solid white;
        transition: all 0.2s ease;
    }
    
    .bet-slider::-webkit-slider-thumb:active {
        transform: scale(1.1);
        box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
    }
    
    .bet-input {
        width: 70px;
        padding: 6px;
        font-size: 12px;
        border-radius: 4px;
        background: var(--primary-dark);
        border: 1px solid var(--border-color);
        color: var(--text-light);
        text-align: center;
    }
    
    .quick-bet-buttons {
        gap: 4px;
        flex-wrap: wrap;
    }
    
    .quick-bet {
        padding: 4px 8px;
        font-size: 10px;
        min-height: 32px;
        border-radius: 4px;
        flex: 1 0 calc(33.33% - 4px);
        background: var(--primary-medium);
        border: 1px solid var(--border-color);
        color: var(--text-light);
        transition: all 0.2s ease;
        touch-action: manipulation;
    }
    
    .quick-bet:active {
        transform: scale(0.95);
        background: var(--tournament-accent);
    }
    
    /* Enhanced Mobile Info Panel */
    .game-info-panel {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .info-section {
        padding: 8px;
        border-radius: var(--border-radius);
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(4px);
    }
    
    .info-section h3 {
        font-size: 12px;
        margin-bottom: 6px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 3px;
    }
    
    .strength-label {
        font-size: 10px;
        margin-bottom: 4px;
    }
    
    .strength-bars {
        gap: 2px;
        margin-bottom: 6px;
    }
    
    .strength-bar {
        height: 4px;
        border-radius: 2px;
    }
    
    .prob-label, .prob-value {
        font-size: 11px;
    }
    
    .blind-amounts {
        gap: 8px;
    }
    
    .blind-label {
        font-size: 9px;
    }
    
    .blind-value {
        font-size: 10px;
    }
    
    .round-phase {
        font-size: 12px;
        font-weight: 600;
    }
    
    .players-active, .next-action {
        font-size: 10px;
    }
    
    /* Enhanced Mobile Actions Panel */
    .actions-panel {
        padding: 12px;
        border-radius: var(--border-radius-lg);
        margin-bottom: 8px;
    }
    
    .game-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        margin-bottom: 12px;
    }
    
    .game-actions .action-btn {
        min-height: var(--touch-target-min);
        font-size: 10px;
        padding: 8px 10px;
        border-radius: var(--border-radius);
        font-weight: 600;
    }
    
    .table-options {
        justify-content: space-between;
        gap: 8px;
    }
    
    .table-option-btn {
        width: var(--touch-target-min);
        height: var(--touch-target-min);
        border-radius: 50%;
        border: 1px solid var(--border-color);
        background: var(--primary-medium);
        color: var(--text-light);
        display: flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
        transition: all 0.2s ease;
    }
    
    .table-option-btn:active {
        transform: scale(0.9);
        background: var(--tournament-accent);
    }
}

/* Enhanced Pro View for Mobile Tournament Edition */
@media (max-width: 767px) {
    body.pro-view-active .pro-analytics {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: var(--mobile-pro-panel-height);
        background: linear-gradient(135deg, var(--tournament-secondary), var(--tournament-primary));
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        z-index: 1000;
        overflow-y: auto;
        padding: 16px 12px 12px;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
        border-top: 3px solid var(--tournament-gold);
        animation: slideUpMobile 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        backdrop-filter: blur(10px);
    }
    
    @keyframes slideUpMobile {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    body.pro-view-active .main-content {
        padding-bottom: calc(var(--mobile-pro-panel-height) + 20px);
    }
    
    /* Pro View Handle */
    .pro-analytics::before {
        content: '';
        position: absolute;
        top: 6px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 2px;
    }
    
    .pro-analytics::after {
        content: '👆 Swipe down to close';
        position: absolute;
        top: 15px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
    }
    
    .pro-analytics h3 {
        font-size: 14px;
        margin: 25px 0 10px 0;
        color: var(--text-light);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 6px;
    }
    
    .pro-analytics h3 i {
        color: var(--tournament-gold);
        font-size: 12px;
    }
    
    /* Enhanced Mobile Tournament Analytics */
    .analytics-section {
        margin-bottom: 12px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        margin-bottom: 12px;
    }
    
    .analytics-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 8px;
        text-align: center;
        transition: all 0.2s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .analytics-item:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 0.15);
    }
    
    .analytics-label {
        font-size: 9px;
        margin-bottom: 3px;
        font-weight: 600;
        color: var(--text-medium);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .analytics-value {
        font-size: 13px;
        font-weight: 700;
        color: var(--text-light);
        margin-bottom: 2px;
    }
    
    .analytics-help {
        font-size: 7px;
        color: var(--text-tertiary);
        opacity: 0.8;
        line-height: 1.2;
    }
    
    /* Enhanced Mobile Opponent Analysis */
    .opponent-analysis {
        margin-bottom: 12px;
    }
    
    .opponent-stats {
        display: flex;
        flex-direction: column;
        gap: 6px;
        margin-bottom: 12px;
    }
    
    .opponent-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 8px;
        border-left: 3px solid var(--tournament-accent);
        transition: all 0.2s ease;
    }
    
    .opponent-item:active {
        transform: translateX(2px);
        background: rgba(255, 255, 255, 0.15);
    }
    
    .opponent-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }
    
    .opponent-name {
        font-size: 11px;
        font-weight: 700;
        color: var(--text-light);
    }
    
    .opponent-tendency {
        font-size: 8px;
        padding: 2px 5px;
        border-radius: 3px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }
    
    .opponent-tendency.tight-passive {
        background: rgba(76, 175, 80, 0.2);
        color: var(--success-color);
        border: 1px solid rgba(76, 175, 80, 0.3);
    }
    
    .opponent-tendency.loose-aggressive {
        background: rgba(244, 67, 54, 0.2);
        color: var(--danger-color);
        border: 1px solid rgba(244, 67, 54, 0.3);
    }
    
    .opponent-tendency.tight-aggressive {
        background: rgba(255, 152, 0, 0.2);
        color: var(--warning-color);
        border: 1px solid rgba(255, 152, 0, 0.3);
    }
    
    .opponent-notes {
        font-size: 9px;
        color: var(--text-medium);
        margin-bottom: 3px;
        line-height: 1.3;
    }
    
    .opponent-stats-mini {
        font-size: 7px;
        color: var(--text-tertiary);
        display: flex;
        gap: 8px;
    }
    
    /* Enhanced Mobile Hand History */
    .hand-history {
        margin-bottom: 8px;
    }
    
    .history-list {
        display: flex;
        flex-direction: column;
        gap: 4px;
        max-height: 100px;
        overflow-y: auto;
    }
    
    .history-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        padding: 6px;
        font-size: 9px;
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
    }
    
    .history-item.won {
        border-left-color: var(--success-color);
        background: rgba(76, 175, 80, 0.1);
    }
    
    .history-item.lost {
        border-left-color: var(--danger-color);
        background: rgba(244, 67, 54, 0.1);
    }
    
    .history-item.folded {
        border-left-color: var(--text-muted);
        background: rgba(108, 117, 125, 0.1);
    }
    
    .history-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2px;
    }
    
    .history-hand {
        font-size: 10px;
        font-weight: 600;
        color: var(--text-light);
    }
    
    .history-result {
        font-size: 9px;
        font-weight: 600;
    }
    
    .history-action {
        font-size: 8px;
        color: var(--text-medium);
        margin-bottom: 1px;
    }
    
    .history-time {
        font-size: 7px;
        color: var(--text-tertiary);
    }
    
    /* Pro View Status Indicators */
    body.pro-view-active .mobile-status-bar {
        border: 2px solid var(--tournament-gold);
        box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);
        animation: pro-pulse 3s ease-in-out infinite;
    }
    
    @keyframes pro-pulse {
        0%, 100% {
            border-color: var(--tournament-gold);
            box-shadow: 0 0 15px rgba(255, 193, 7, 0.3);
        }
        50% {
            border-color: var(--tournament-accent);
            box-shadow: 0 0 20px rgba(255, 109, 0, 0.4);
        }
    }
    
    /* Pro View Gesture Hints */
    body:not(.pro-view-active) .mobile-status-bar::after {
        content: '👆 Swipe up for Pro Analytics';
        position: absolute;
        bottom: -18px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 9px;
        color: var(--text-tertiary);
        opacity: 0.7;
        animation: swipe-hint 4s ease-in-out infinite;
        white-space: nowrap;
    }
    
    @keyframes swipe-hint {
        0%, 80%, 100% {
            opacity: 0.7;
            transform: translateX(-50%) translateY(0);
        }
        10%, 70% {
            opacity: 0.9;
            transform: translateX(-50%) translateY(-2px);
        }
    }
    
    /* Hide hint after first use */
    body.has-used-pro-view .mobile-status-bar::after {
        display: none;
    }
    
    /* Enhanced mobile button states */
    .action-btn:disabled {
        opacity: 0.4;
        transform: none !important;
        background: var(--primary-dark) !important;
    }
    
    .action-btn.pulse {
        animation: button-pulse 1.5s ease-in-out infinite;
    }
    
    @keyframes button-pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; box-shadow: 0 0 10px rgba(255, 255, 255, 0.3); }
    }
}

/* Tablet Styles */
@media (min-width: 768px) {
    .game-header {
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
    }
    
    .header-left, .header-right {
        flex: 1;
        order: initial;
    }
    
    .header-right {
        justify-content: flex-end;
    }
    
    .header-center {
        flex: 2;
        order: initial;
    }
    
    .game-title {
        font-size: 36px;
        margin-bottom: 5px;
    }
    
    .game-subtitle {
        font-size: 16px;
    }
    
    .btn-text {
        display: inline;
    }
    
    .view-text {
        display: inline;
    }
    
    .back-text {
        display: inline;
    }
    
    .tournament-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tables-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .badges-container {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .mobile-status-bar {
        display: none;
    }
    
    .main-content {
        flex-direction: row;
        gap: 20px;
    }
    
    .tournament-sidebar {
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 280px;
    }
    
    .tournament-stats, .payout-structure, .player-notes {
        background: var(--glass-bg);
        border-radius: var(--border-radius);
        padding: 12px;
        backdrop-filter: blur(8px);
        border: 1px solid var(--glass-border);
    }
    
    .tournament-stats h3, .payout-structure h3, .player-notes h3 {
        font-size: 16px;
        color: var(--tournament-gold);
        margin-bottom: 12px;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 4px;
    }
    
    .tournament-stat-item {
        display: flex;
        justify-content: space-between;
        font-size: 13px;
        margin-bottom: 8px;
    }
    
    .stat-label {
        color: var(--text-tertiary);
    }
    
    .stat-value {
        color: var(--text-light);
        font-weight: 600;
    }
    
    .payout-item {
        display: flex;
        justify-content: space-between;
        font-size: 13px;
        margin-bottom: 8px;
    }
    
    .position {
        color: var(--text-tertiary);
    }
    
    .amount {
        color: var(--tournament-gold);
        font-weight: 600;
    }
    
    .notes-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 12px;
    }
    
    .note-item {
        background: rgba(0, 0, 0, 0.2);
        border-radius: var(--border-radius);
        padding: 8px;
    }
    
    .note-player {
        font-size: 12px;
        font-weight: 600;
        color: var(--accent-blue);
        margin-bottom: 4px;
    }
    
    .note-text {
        font-size: 11px;
        color: var(--text-light);
    }
    
    .add-note-btn {
        background: var(--primary-medium);
        color: var(--text-light);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: var(--transition-fast);
    }
    
    .add-note-btn:hover {
        background: var(--primary-light);
        border-color: var(--accent-blue);
    }
    
    .game-section {
        flex: 1;
    }
    
    .poker-table {
        min-height: 500px;
        padding: 30px;
    }
    
    .pot-display {
        padding: 16px 20px;
    }
    
    .pot-amount {
        font-size: 24px;
    }
    
    .community-cards-label {
        font-size: 14px;
        margin-bottom: 12px;
    }
    
    .player-seat {
        width: 140px;
        padding: 12px;
    }
    
    .player-seat#player0 {
        width: 160px;
    }
    
    .player-name {
        font-size: 14px;
    }
    
    .player-chips {
        font-size: 12px;
    }
    
    .player-status {
        font-size: 11px;
    }
    
    .ai-difficulty {
        font-size: 10px;
    }
    
    .action-btn {
        font-size: 12px;
        padding: 8px 12px;
        min-height: 36px;
    }
    
    .dealer-button {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
    
    .game-controls {
        padding: 20px;
    }
    
    .game-info-panel {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .analytics-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .game-actions {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .modal-content {
        max-width: 700px;
    }
    
    .elimination-container, .showdown-container {
        max-width: 700px;
    }
    
    .card-slot {
        width: var(--card-width);
        height: var(--card-height);
    }
    
    .player-cards .card-slot {
        width: 32px;
        height: 45px;
    }
    
    .cards-container {
        gap: 8px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .tournament-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .tables-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    body.pro-view-active .main-content {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 20px;
    }
    
    body.pro-view-active .tournament-sidebar {
        grid-column: 1;
        grid-row: 1;
    }
    
    body.pro-view-active .game-section {
        grid-column: 2;
        grid-row: 1;
    }
    
    .pro-analytics {
        margin-top: 0;
    }
}

/* Animations */
@keyframes dealCard {
    from {
        transform: translateY(-100px) rotate(-180deg);
        opacity: 0;
    }
    to {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
}

@keyframes chipStack {
    from {
        transform: scale(0) rotate(180deg);
        opacity: 0;
    }
    to {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes showdownPulse {
    0%, 100% {
        box-shadow: 0 0 30px rgba(255, 109, 0, 0.5);
    }
    50% {
        box-shadow: 0 0 50px rgba(255, 109, 0, 0.8);
    }
}

.card {
    animation: dealCard 0.5s ease-out;
}

.chip {
    animation: chipStack 0.3s ease-out;
}

.player-seat.active {
    animation: pulse 1.5s infinite;
}

.showdown-container {
    animation: showdownPulse 2s infinite;
}

/* Showdown button glow effect */
.showdown-btn {
    position: relative;
    overflow: hidden;
}

.showdown-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
        top: -100%;
    }
    100% {
        left: 100%;
        top: 100%;
    }
}