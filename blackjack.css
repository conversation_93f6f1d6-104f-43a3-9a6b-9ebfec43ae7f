/* Blackjack Game CSS */
:root {
    --casino-red: #DC143C;
    --casino-green: #228B22;
    --casino-gold: #FFD700;
    --casino-blue: #1E90FF;
    --warning-orange: #FF8C00;
    --neutral-gray: #708090;
    --table-felt: #0B7D40;
    --card-white: #FFFEF7;
}

.blackjack-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    position: relative;
}

.game-header {
    text-align: center;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.back-link {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.back-link:hover {
    color: var(--casino-gold);
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-size: 2rem;
    background: linear-gradient(45deg, var(--casino-gold), #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
    margin: 0;
    text-align: center;
    flex: 1;
}

.game-subtitle {
    color: #fff;
    font-size: 1rem;
    opacity: 0.8;
    text-align: center;
    width: 100%;
    margin-top: 0.5rem;
}

.view-mode-toggle {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-toggle button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: #fff;
}

.view-mode-toggle button.active {
    background: var(--casino-gold);
    color: #333;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stats-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    text-align: center;
}

.stat-label {
    color: #fff;
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    opacity: 0.8;
}

.stat-value {
    color: var(--casino-gold);
    font-size: 1.3rem;
    font-weight: bold;
}

.deck-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.deck-penetration {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    position: relative;
    overflow: hidden;
}

.penetration-bar {
    height: 8px;
    background: var(--neutral-gray);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.penetration-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--casino-green), var(--warning-orange), var(--casino-red));
    transition: width 0.3s ease;
}

.difficulty-selector {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.difficulty-btn {
    flex: 1;
    padding: 0.5rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    min-height: 44px;
}

.difficulty-btn.active {
    background: var(--casino-blue);
}

.game-table {
    background: var(--table-felt);
    border-radius: 20px;
    padding: 1.5rem;
    border: 3px solid var(--casino-gold);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
}

.betting-area {
    text-align: center;
    margin-bottom: 1rem;
}

.bet-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid var(--casino-gold);
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1.1rem;
    font-weight: bold;
    text-align: center;
    width: 120px;
    margin: 0 0.5rem;
}

.bet-suggestions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
}

.bet-chip {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid var(--casino-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    min-height: 44px;
}

.bet-chip:hover {
    transform: scale(1.1);
}

.chip-100 { background: var(--casino-red); color: white; }
.chip-500 { background: var(--casino-blue); color: white; }
.chip-1000 { background: var(--casino-green); color: white; }

.cards-area {
    display: grid;
    grid-template-rows: 1fr 1fr;
    gap: 2rem;
    min-height: 250px;
}

.dealer-area,
.player-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
}

.hand-label {
    color: white;
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.cards-container {
    display: flex;
    gap: 0.5rem;
    min-height: 100px;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
}

.card {
    width: 70px;
    height: 100px;
    background: var(--card-white);
    border-radius: 8px;
    border: 2px solid #333;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.4rem;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.card.hidden {
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: white;
}

.card-value {
    font-size: 0.9rem;
    font-weight: bold;
    text-align: center;
}

.card-suit {
    font-size: 1.3rem;
    text-align: center;
    margin: auto;
}

.red-suit {
    color: var(--casino-red);
}

.black-suit {
    color: #333;
}

.hand-total {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: bold;
}

.strategy-panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
    margin-top: 1rem;
}

.strategy-coach {
    margin-bottom: 1rem;
}

.coach-recommendation {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
}

.recommendation-action {
    color: var(--casino-gold);
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.3rem;
}

.recommendation-reason {
    color: #fff;
    font-size: 0.85rem;
    opacity: 0.8;
}

.probability-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
}

.prob-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.prob-label {
    color: #fff;
    font-size: 0.75rem;
    margin-bottom: 0.3rem;
}

.prob-value {
    font-size: 1rem;
    font-weight: bold;
}

.prob-positive {
    color: var(--casino-green);
}

.prob-negative {
    color: var(--casino-red);
}

.prob-neutral {
    color: var(--casino-gold);
}

.risk-meter {
    margin: 0.75rem 0;
}

.risk-bar {
    height: 16px;
    background: linear-gradient(90deg, var(--casino-green) 0%, var(--warning-orange) 50%, var(--casino-red) 100%);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.risk-indicator {
    position: absolute;
    top: -3px;
    width: 3px;
    height: 22px;
    background: white;
    border-radius: 2px;
    transition: left 0.3s ease;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.game-controls {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.control-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 100px;
    min-height: 44px;
}

.btn-hit {
    background: linear-gradient(45deg, var(--casino-red), #FF6B6B);
    color: white;
}

.btn-stand {
    background: linear-gradient(45deg, var(--casino-green), #51CF66);
    color: white;
}

.btn-double {
    background: linear-gradient(45deg, var(--casino-blue), #4DABF7);
    color: white;
}

.btn-split {
    background: linear-gradient(45deg, #9C27B0, #CE93D8);
    color: white;
}

.btn-surrender {
    background: linear-gradient(45deg, var(--neutral-gray), #ADB5BD);
    color: white;
}

.btn-deal {
    background: linear-gradient(45deg, var(--casino-gold), #FFA500);
    color: #333;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.session-timer {
    position: fixed;
    top: 80px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.75rem;
    border-radius: 10px;
    border: 2px solid var(--casino-gold);
    z-index: 1000;
    font-size: 0.9rem;
}

.timer-warning {
    border-color: var(--warning-orange);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.result-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(30, 30, 60, 0.95));
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    border: 2px solid var(--casino-gold);
    text-align: center;
    z-index: 1000;
    display: none;
    min-width: 280px;
    max-width: 90vw;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
}

.achievement-badge {
    background: var(--casino-gold);
    color: #333;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    margin: 0.3rem;
    display: inline-block;
}

/* Pro View Styles */
.pro-view-stats {
    display: none;
}

.pro-view-active .pro-view-stats {
    display: block;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.3);
    margin-bottom: 1rem;
}

.pro-view-title {
    font-size: 1rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.pro-view-title i {
    margin-right: 0.5rem;
    color: var(--casino-gold);
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.pro-stat-item {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.pro-stat-label {
    font-size: 0.75rem;
    color: #fff;
    margin-bottom: 0.3rem;
    opacity: 0.8;
}

.pro-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--casino-gold);
}

.card-count-tracker {
    margin-bottom: 1rem;
}

.count-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.count-label {
    color: #fff;
    font-size: 0.9rem;
}

.count-value {
    font-size: 1.2rem;
    font-weight: bold;
}

.count-positive {
    color: var(--casino-green);
}

.count-negative {
    color: var(--casino-red);
}

.count-neutral {
    color: var(--casino-gold);
}

.hand-history {
    max-height: 150px;
    overflow-y: auto;
    background: rgba(0,0,0,0.2);
    border-radius: 8px;
    padding: 0.5rem;
    margin-top: 0.75rem;
}

.history-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 0.8rem;
}

.history-entry:last-child {
    border-bottom: none;
}

.history-cards {
    color: #fff;
    opacity: 0.8;
}

.history-result {
    font-weight: 600;
}

.history-win {
    color: var(--casino-green);
}

.history-loss {
    color: var(--casino-red);
}

.history-push {
    color: var(--casino-gold);
}

.strategy-matrix {
    margin-top: 1rem;
}

.matrix-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.7rem;
    background: rgba(0,0,0,0.2);
    border-radius: 8px;
    overflow: hidden;
}

.matrix-table th,
.matrix-table td {
    padding: 0.3rem;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.1);
    color: #fff;
}

.matrix-table th {
    background: rgba(255,215,0,0.2);
    font-weight: 600;
}

.matrix-hit {
    background: rgba(220, 20, 60, 0.3);
}

.matrix-stand {
    background: rgba(34, 139, 34, 0.3);
}

.matrix-double {
    background: rgba(30, 144, 255, 0.3);
}

.matrix-split {
    background: rgba(156, 39, 176, 0.3);
}

.advanced-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.stats-chart {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.75rem;
}

.chart-title {
    color: #fff;
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.chart-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
}

.chart-label {
    width: 60px;
    font-size: 0.75rem;
    color: #fff;
    opacity: 0.8;
}

.chart-fill {
    flex: 1;
    height: 12px;
    background: rgba(255,255,255,0.2);
    border-radius: 6px;
    margin: 0 0.5rem;
    overflow: hidden;
}

.chart-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--casino-green), var(--casino-gold));
    transition: width 0.3s ease;
}

.chart-value {
    font-size: 0.75rem;
    color: var(--casino-gold);
    font-weight: 600;
    min-width: 35px;
    text-align: right;
}

.pro-controls {
    display: none;
    margin-top: 1rem;
}

.pro-view-active .pro-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pro-btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    background: rgba(255,255,255,0.1);
    color: #fff;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
}

.pro-btn:hover {
    background: rgba(255,255,255,0.2);
}

.pro-btn i {
    margin-right: 0.25rem;
}

/* Responsive Styles */
@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 1.5rem;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .game-table {
        padding: 2rem;
    }
    
    .cards-area {
        gap: 3rem;
        min-height: 300px;
    }
    
    .card {
        width: 80px;
        height: 120px;
    }
    
    .card-value {
        font-size: 1rem;
    }
    
    .card-suit {
        font-size: 1.5rem;
    }
    
    .control-btn {
        min-width: 120px;
        padding: 1rem 2rem;
        font-size: 1rem;
    }
    
    .bet-chip {
        width: 40px;
        height: 40px;
    }
    
    .strategy-panel {
        margin-top: 0;
    }
    
    .advanced-stats {
        grid-template-columns: 1fr 1fr;
    }
}

@media (min-width: 1024px) {
    .game-title {
        font-size: 3rem;
    }
    
    .blackjack-container {
        padding: 2rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .advanced-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        text-align: center;
    }
    
    .back-link {
        align-self: flex-start;
    }
    
    .view-mode-toggle {
        align-self: center;
        margin-top: 0.5rem;
    }
    
    .cards-container {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .game-controls {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .control-btn {
        min-width: auto;
        padding: 0.75rem;
        font-size: 0.85rem;
    }
    
    .session-timer {
        top: 70px;
        right: 10px;
        font-size: 0.8rem;
        padding: 0.5rem;
    }
    
    .probability-display {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .advanced-stats {
        grid-template-columns: 1fr;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .blackjack-container {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-subtitle {
        font-size: 0.9rem;
    }
    
    .card {
        width: 60px;
        height: 85px;
        padding: 0.3rem;
    }
    
    .card-value {
        font-size: 0.8rem;
    }
    
    .card-suit {
        font-size: 1.1rem;
    }
    
    .hand-total {
        font-size: 1rem;
        padding: 0.4rem 0.8rem;
    }
    
    .control-btn {
        padding: 0.6rem;
        font-size: 0.8rem;
        min-height: 40px;
    }
    
    .bet-chip {
        width: 45px;
        height: 45px;
        font-size: 0.75rem;
    }
    
    .bet-input {
        width: 100px;
        padding: 0.6rem;
        font-size: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-card {
        padding: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1.1rem;
    }
    
    .probability-display {
        grid-template-columns: 1fr;
        gap: 0.4rem;
    }
    
    .prob-card {
        padding: 0.5rem;
    }
    
    .prob-label {
        font-size: 0.7rem;
    }
    
    .prob-value {
        font-size: 0.9rem;
    }
    
    .risk-bar {
        height: 12px;
    }
    
    .risk-indicator {
        width: 2px;
        height: 16px;
        top: -2px;
    }
    
    .matrix-table {
        font-size: 0.6rem;
    }
    
    .matrix-table th,
    .matrix-table td {
        padding: 0.2rem;
    }
    
    .chart-bar {
        margin-bottom: 0.3rem;
    }
    
    .chart-label {
        width: 50px;
        font-size: 0.7rem;
    }
    
    .chart-fill {
        height: 10px;
    }
    
    .chart-value {
        font-size: 0.7rem;
        min-width: 30px;
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .control-btn {
        min-height: 48px;
        padding: 0.8rem 1rem;
    }
    
    .view-mode-toggle button {
        min-height: 40px;
        padding: 0.5rem 0.75rem;
    }
    
    .bet-chip {
        min-height: 48px;
        min-width: 48px;
    }
    
    .difficulty-btn {
        min-height: 48px;
    }
    
    .pro-btn {
        min-height: 44px;
    }
    
    input[type="number"] {
        min-height: 44px;
        font-size: 16px; /* Prevents iOS zoom */
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .back-link {
        color: #ddd;
    }
    
    .stats-panel,
    .strategy-panel,
    .pro-view-stats {
        background: rgba(20, 20, 20, 0.8);
    }
    
    .stat-card,
    .prob-card,
    .pro-stat-item,
    .deck-penetration,
    .coach-recommendation {
        background: rgba(40, 40, 40, 0.5);
    }
    
    .stat-label,
    .prob-label,
    .pro-stat-label {
        color: #aaa;
    }
    
    .stat-value,
    .prob-value,
    .pro-stat-value {
        color: #eee;
    }
    
    .recommendation-reason,
    .history-cards {
        color: #ccc;
    }
    
    .pro-btn {
        background: rgba(255,255,255,0.1);
        color: #ddd;
    }
    
    .pro-btn:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .hand-history {
        background: rgba(255,255,255,0.05);
    }
    
    .matrix-table th,
    .matrix-table td {
        color: #ddd;
    }
    
    .chart-label {
        color: #aaa;
    }
}