// Diamond Hunter Game - Enhanced Mobile & Pro View Implementation
// Following YOUWARE platform standards

class DiamondHunter {
    constructor() {
        // Game configuration
        this.gridSize = 5;
        this.totalSquares = this.gridSize * this.gridSize;
        this.bombCount = 3;
        this.gameState = 'setup'; // setup, playing, finished
        this.grid = [];
        this.revealedSquares = [];
        this.diamondsFound = 0;
        this.currentMultiplier = 0;
        
        // Provably fair system
        this.serverSeed = null;
        this.serverSeedHash = null;
        this.clientSeed = null;
        this.nonce = 0;
        
        // Game state management
        this.balance = 10000;
        this.betAmount = 50;
        this.currentBet = 0;
        
        // UI state
        this.viewMode = 'standard', // 'standard' or 'pro'
        
        // Game history and analytics
        this.gameHistory = [];
        this.sessionStats = {
            startTime: Date.now(),
            totalGames: 0,
            totalBets: 0,
            totalWinnings: 0,
            biggestWin: 0,
            longestWinStreak: 0,
            currentWinStreak: 0,
            averageMultiplier: 0,
            riskLevel: 'medium'
        };
        
        // Pro view analytics
        this.analytics = {
            bombCountFrequency: {},
            outcomesByBombCount: {},
            recentResults: [],
            riskAssessment: {
                currentRisk: 'medium',
                recommendedAction: 'continue',
                confidence: 85
            }
        };
        
        // Initialize sound effects with graceful fallback
        this.initializeSounds();
        
        // Initialize the game
        this.initialize();
    }

    initialize() {
        this.loadGameState();
        this.initializeElements();
        this.createViewModeToggle();
        this.createProViewPanel();
        this.attachEventListeners();
        this.generateSeeds();
        this.updateDisplay();
        this.updateProViewStats();
        
        // Auto-save periodically
        setInterval(() => this.saveGameState(), 5000);
    }

    // Create view mode toggle
    createViewModeToggle() {
        if (document.querySelector('.view-mode-toggle')) return;
        
        const toggle = document.createElement('div');
        toggle.className = 'view-mode-toggle';
        toggle.innerHTML = `
            <button class="view-toggle-btn ${this.viewMode === 'standard' ? 'active' : ''}" 
                    data-mode="standard">STD</button>
            <button class="view-toggle-btn ${this.viewMode === 'pro' ? 'active' : ''}" 
                    data-mode="pro">PRO</button>
        `;
        
        document.body.appendChild(toggle);
        
        // Add event listeners
        toggle.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.getAttribute('data-mode');
                this.setViewMode(mode);
            });
        });
    }

    // Create Pro View panel
    createProViewPanel() {
        if (document.querySelector('.pro-view-panel')) return;
        
        const proPanel = document.createElement('div');
        proPanel.className = 'pro-view-panel';
        proPanel.innerHTML = `
            <h3 class="pro-panel-title">
                <i class="fas fa-chart-line"></i> Pro Analytics
            </h3>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Win Rate</div>
                    <div class="pro-stat-value" id="proWinRate">0%</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Avg Multiplier</div>
                    <div class="pro-stat-value" id="proAvgMultiplier">0.00x</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Profit/Loss</div>
                    <div class="pro-stat-value" id="proProfitLoss">0 GA</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Session Games</div>
                    <div class="pro-stat-value" id="proSessionGames">0</div>
                </div>
            </div>
            
            <div class="risk-analysis">
                <h4><i class="fas fa-exclamation-triangle"></i> Risk Analysis</h4>
                <div class="risk-level">
                    <span>Current Risk:</span>
                    <div class="risk-indicator" id="riskIndicator">
                        <div class="risk-dot"></div>
                        <div class="risk-dot"></div>
                        <div class="risk-dot"></div>
                        <div class="risk-dot"></div>
                        <div class="risk-dot"></div>
                    </div>
                </div>
                <div style="margin-top: 0.5rem; font-size: 0.8rem; text-align: center;">
                    <span id="riskAssessment">Analyzing risk patterns...</span>
                </div>
            </div>
            
            <div class="recent-games">
                <h4><i class="fas fa-history"></i> Recent Results</h4>
                <div id="recentResultsList">
                    <div style="text-align: center; opacity: 0.6; font-size: 0.8rem;">
                        No recent games to display
                    </div>
                </div>
            </div>
        `;
        
        // Insert before setup panel
        const setupPanel = document.querySelector('.setup-panel');
        setupPanel.parentNode.insertBefore(proPanel, setupPanel);
    }

    // Set view mode
    setViewMode(mode) {
        this.viewMode = mode;
        localStorage.setItem('diamondHunterViewMode', mode);
        
        // Update toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-mode') === mode) {
                btn.classList.add('active');
            }
        });
        
        // Toggle body class for CSS styling
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
        } else {
            document.body.classList.add('pro-view-active');
            this.updateProViewStats();
        }
        
        this.showNotification(`Switched to ${mode.toUpperCase()} view`, 'info');
    }

    // Load game state from localStorage
    loadGameState() {
        const saved = localStorage.getItem('diamondHunterGameState');
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                this.balance = parsed.balance || 10000;
                this.gameHistory = parsed.gameHistory || [];
                this.sessionStats = { ...this.sessionStats, ...(parsed.sessionStats || {}) };
                this.analytics = { ...this.analytics, ...(parsed.analytics || {}) };
            } catch (e) {
                console.warn('Failed to load game state:', e);
            }
        }
        
        // Load view mode
        const savedViewMode = localStorage.getItem('diamondHunterViewMode');
        if (savedViewMode) {
            this.viewMode = savedViewMode;
        }
        
        // Initialize analytics if empty
        if (Object.keys(this.analytics.bombCountFrequency).length === 0) {
            for (let i = 1; i <= 24; i++) {
                this.analytics.bombCountFrequency[i] = 0;
                this.analytics.outcomesByBombCount[i] = { wins: 0, losses: 0 };
            }
        }
    }

    // Save game state to localStorage
    saveGameState() {
        try {
            const gameState = {
                balance: this.balance,
                gameHistory: this.gameHistory.slice(0, 50), // Keep last 50 games
                sessionStats: this.sessionStats,
                analytics: this.analytics
            };
            localStorage.setItem('diamondHunterGameState', JSON.stringify(gameState));
        } catch (e) {
            console.warn('Failed to save game state:', e);
        }
    }

    initializeSounds() {
        this.sounds = {
            click: new Audio(),
            diamond: new Audio(),
            bomb: new Audio(),
            cashout: new Audio()
        };
        
        // Set sources with error handling
        try {
            this.sounds.click.src = 'assets/sounds/click.mp3';
            this.sounds.diamond.src = 'assets/sounds/diamond.mp3';
            this.sounds.bomb.src = 'assets/sounds/bomb.mp3';
            this.sounds.cashout.src = 'assets/sounds/cashout.mp3';
            
            // Set volume and preload
            Object.values(this.sounds).forEach(sound => {
                sound.volume = 0.3;
                sound.preload = 'auto';
            });
        } catch (e) {
            console.log("Sound initialization error:", e);
        }
    }

    initializeElements() {
        // Setup panel elements
        this.setupPanel = document.getElementById('setupPanel');
        this.bombDecreaseBtn = document.getElementById('bombDecrease');
        this.bombIncreaseBtn = document.getElementById('bombIncrease');
        this.bombDisplay = document.getElementById('bombDisplay');
        this.diamondProbDisplay = document.getElementById('diamondProb');
        this.multiplierPreviewDisplay = document.getElementById('multiplierPreview');
        this.serverSeedHashDisplay = document.getElementById('serverSeedHash');
        this.clientSeedInput = document.getElementById('clientSeed');
        this.randomizeSeedBtn = document.getElementById('randomizeSeed');
        this.startGameBtn = document.getElementById('startGame');
        
        // Bet controls
        this.betDecreaseBtn = document.getElementById('betDecrease');
        this.betIncreaseBtn = document.getElementById('betIncrease');
        this.betDisplay = document.getElementById('betDisplay');
        this.balanceDisplay = document.getElementById('balanceDisplay');

        // Game panel elements
        this.gamePanel = document.getElementById('gamePanel');
        this.currentBombsDisplay = document.getElementById('currentBombs');
        this.diamondsFoundDisplay = document.getElementById('diamondsFound');
        this.currentMultiplierDisplay = document.getElementById('currentMultiplier');
        this.potentialWinningsDisplay = document.getElementById('potentialWinnings');
        this.gameGrid = document.getElementById('gameGrid');
        this.cashOutBtn = document.getElementById('cashOut');
        this.newGameBtn = document.getElementById('newGame');

        // Results panel elements
        this.resultsPanel = document.getElementById('resultsPanel');
        this.resultTitle = document.getElementById('resultTitle');
        this.finalDiamonds = document.getElementById('finalDiamonds');
        this.finalMultiplier = document.getElementById('finalMultiplier');
        this.finalResult = document.getElementById('finalResult');
        this.revealedServerSeedDisplay = document.getElementById('revealedServerSeed');
        this.verifyRoundBtn = document.getElementById('verifyRound');
        this.playAgainBtn = document.getElementById('playAgain');

        // Modal elements
        this.howToPlayModal = document.getElementById('howToPlayModal');
        this.showRulesBtn = document.getElementById('showRules');
        this.closeModalBtn = document.getElementById('closeModal');
        this.gameHistoryBtn = document.getElementById('gameHistory');
    }

    attachEventListeners() {
        // Setup controls
        this.bombDecreaseBtn.addEventListener('click', () => this.adjustBombCount(-1));
        this.bombIncreaseBtn.addEventListener('click', () => this.adjustBombCount(1));
        this.randomizeSeedBtn.addEventListener('click', () => this.randomizeClientSeed());
        this.startGameBtn.addEventListener('click', () => this.startGame());
        
        // Bet controls
        this.betDecreaseBtn.addEventListener('click', () => this.adjustBetAmount(-10));
        this.betIncreaseBtn.addEventListener('click', () => this.adjustBetAmount(10));

        // Game controls
        this.cashOutBtn.addEventListener('click', () => this.cashOut());
        this.newGameBtn.addEventListener('click', () => this.resetToSetup());

        // Results controls
        this.verifyRoundBtn.addEventListener('click', () => this.verifyRound());
        this.playAgainBtn.addEventListener('click', () => this.resetToSetup());

        // Modal controls
        this.showRulesBtn.addEventListener('click', () => this.showModal());
        this.closeModalBtn.addEventListener('click', () => this.hideModal());
        this.gameHistoryBtn.addEventListener('click', () => this.showGameHistory());

        // Click outside modal to close
        this.howToPlayModal.addEventListener('click', (e) => {
            if (e.target === this.howToPlayModal) {
                this.hideModal();
            }
        });

        // Client seed input
        this.clientSeedInput.addEventListener('input', () => {
            this.clientSeed = this.clientSeedInput.value || this.generateRandomString(16);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && this.gameState === 'setup') {
                e.preventDefault();
                this.startGame();
            } else if (e.code === 'KeyC' && this.gameState === 'playing') {
                this.cashOut();
            } else if (e.code === 'KeyP') {
                this.setViewMode(this.viewMode === 'standard' ? 'pro' : 'standard');
            }
        });

        // Touch event optimization
        this.attachTouchEvents();
    }

    // Attach touch events for mobile optimization
    attachTouchEvents() {
        let touchStartTime = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        });
        
        document.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            
            // Long press detection for quick actions
            if (touchDuration > 500) {
                const target = e.target.closest('.grid-square');
                if (target && this.gameState === 'playing') {
                    // Long press shows probability for that square (if implemented)
                    this.showNotification('🎯 Keep exploring!', 'info');
                }
            }
        });
        
        // Prevent double-tap zoom on game elements
        document.querySelectorAll('.grid-square, .bomb-btn, button').forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
    }

    generateSeeds() {
        this.serverSeed = this.generateRandomString(32);
        this.serverSeedHash = this.hashString(this.serverSeed);
        this.clientSeed = this.generateRandomString(16);
        
        this.serverSeedHashDisplay.textContent = this.serverSeedHash.substring(0, 16) + '...';
        this.clientSeedInput.value = this.clientSeed;
        
        this.updateProbabilityDisplay();
    }

    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // Enhanced hash function for better security
    hashString(str) {
        let hash = 0;
        if (str.length === 0) return hash.toString(16);
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        
        // Make hash more random-looking
        hash = Math.abs(hash);
        return hash.toString(16).padStart(8, '0') + this.generateRandomString(8);
    }

    adjustBombCount(delta) {
        const newCount = this.bombCount + delta;
        if (newCount >= 1 && newCount <= this.totalSquares - 1) {
            this.bombCount = newCount;
            this.bombDisplay.textContent = this.bombCount;
            this.updateProbabilityDisplay();
            this.updateRiskAssessment();
            
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }
    }

    updateProbabilityDisplay() {
        const diamondCount = this.totalSquares - this.bombCount;
        const diamondProbability = Math.round((diamondCount / this.totalSquares) * 100);
        const multiplierPerDiamond = this.calculateMultiplierPerDiamond();
        
        this.diamondProbDisplay.textContent = diamondProbability + '%';
        this.multiplierPreviewDisplay.textContent = multiplierPerDiamond.toFixed(2) + 'x';
    }
    
    adjustBetAmount(delta) {
        const newBet = this.betAmount + delta;
        if (newBet >= 10 && newBet <= this.balance) {
            this.betAmount = newBet;
            this.betDisplay.textContent = this.betAmount;
            
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        } else if (newBet < 10) {
            this.showNotification('Minimum bet is 10 GA', 'warning');
        } else {
            this.showNotification('Bet cannot exceed your balance', 'error');
        }
    }

    calculateMultiplierPerDiamond() {
        const diamondCount = this.totalSquares - this.bombCount;
        const houseEdge = 0.01; // 1% house edge
        const baseMultiplier = (this.totalSquares / diamondCount) * (1 - houseEdge);
        return baseMultiplier;
    }

    randomizeClientSeed() {
        this.clientSeed = this.generateRandomString(16);
        this.clientSeedInput.value = this.clientSeed;
        this.showNotification('Client seed randomized', 'info');
    }

    startGame() {
        if (this.balance < this.betAmount) {
            this.showNotification('Insufficient balance for this bet', 'error');
            return;
        }
        
        this.balance -= this.betAmount;
        this.currentBet = this.betAmount;
        
        this.gameState = 'playing';
        this.nonce = 0;
        this.diamondsFound = 0;
        this.currentMultiplier = 0;
        this.revealedSquares = [];
        
        this.clientSeed = this.clientSeedInput.value || this.generateRandomString(16);
        
        this.generateBombPositions();
        this.createGameGrid();
        this.updateGameDisplays();
        this.updateDisplay();
        
        // Show game panel
        this.setupPanel.classList.add('hidden');
        this.gamePanel.classList.remove('hidden');
        this.resultsPanel.classList.add('hidden');
        
        // Update analytics
        this.sessionStats.totalGames++;
        this.sessionStats.totalBets += this.currentBet;
        this.analytics.bombCountFrequency[this.bombCount]++;
        
        this.showNotification('Game started! Find the diamonds! 💎', 'success');
    }

    generateBombPositions() {
        this.grid = new Array(this.totalSquares).fill('diamond');
        const bombPositions = [];
        
        const combinedSeed = this.serverSeed + this.clientSeed + this.nonce;
        let seedValue = this.hashString(combinedSeed);
        let randomSeed = parseInt(seedValue.substring(0, 8), 16);
        
        const random = () => {
            randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
            return randomSeed / 0x7fffffff;
        };
        
        const positions = Array.from({length: this.totalSquares}, (_, i) => i);
        
        for (let i = 0; i < this.bombCount; i++) {
            const randomIndex = Math.floor(random() * (this.totalSquares - i));
            const bombPosition = positions.splice(randomIndex, 1)[0];
            this.grid[bombPosition] = 'bomb';
            bombPositions.push(bombPosition);
        }
        
        console.log('Bomb positions (for verification):', bombPositions);
    }

    createGameGrid() {
        this.gameGrid.innerHTML = '';
        
        for (let i = 0; i < this.totalSquares; i++) {
            const square = document.createElement('div');
            square.className = 'grid-square';
            square.dataset.index = i;
            square.addEventListener('click', () => this.clickSquare(i));
            this.gameGrid.appendChild(square);
        }
    }

    clickSquare(index) {
        if (this.gameState !== 'playing' || this.revealedSquares.includes(index)) {
            return;
        }
        
        this.nonce++;
        this.revealedSquares.push(index);
        
        const square = this.gameGrid.children[index];
        const content = this.grid[index];
        
        square.classList.add('revealed');
        
        if (content === 'diamond') {
            this.playSound('diamond');
            
            square.classList.add('diamond');
            square.textContent = '💎';
            this.diamondsFound++;
            this.updateMultiplier();
            this.enableCashOut();
            this.updateGameDisplays();
            
            // Haptic feedback for diamond
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100]);
            }
            
            this.showNotification(`💎 Diamond found! Multiplier: ${this.currentMultiplier.toFixed(2)}x`, 'success');
        } else {
            this.playSound('bomb');
            
            square.classList.add('bomb');
            square.textContent = '💣';
            
            // Strong haptic feedback for bomb
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200, 100, 300]);
            }
            
            this.gameOver(false);
        }
    }

    playSound(soundType) {
        try {
            if (this.sounds[soundType]) {
                this.sounds[soundType].currentTime = 0;
                this.sounds[soundType].play().catch(e => console.log("Sound play error:", e));
            }
        } catch (e) {
            console.log("Sound error:", e);
        }
    }

    updateMultiplier() {
        const baseMultiplier = this.calculateMultiplierPerDiamond();
        this.currentMultiplier = Math.pow(baseMultiplier, this.diamondsFound);
    }

    enableCashOut() {
        this.cashOutBtn.disabled = false;
    }

    updateGameDisplays() {
        this.currentBombsDisplay.textContent = this.bombCount;
        this.diamondsFoundDisplay.textContent = this.diamondsFound;
        this.currentMultiplierDisplay.textContent = this.currentMultiplier.toFixed(2) + 'x';
        
        const potentialWinnings = this.currentMultiplier * this.currentBet;
        this.potentialWinningsDisplay.textContent = potentialWinnings.toFixed(2) + ' GA';
    }

    cashOut() {
        this.playSound('cashout');
        
        const winnings = this.currentBet * this.currentMultiplier;
        this.balance += winnings;
        
        // Celebration haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate([200, 100, 200, 100, 300, 200, 400]);
        }
        
        this.showNotification(`🎉 Cashed out ${winnings.toFixed(2)} GA!`, 'success');
        this.gameOver(true);
    }

    gameOver(cashed) {
        this.gameState = 'finished';
        
        // Reveal all bombs
        for (let i = 0; i < this.totalSquares; i++) {
            if (this.grid[i] === 'bomb' && !this.revealedSquares.includes(i)) {
                const square = this.gameGrid.children[i];
                square.classList.add('revealed', 'bomb');
                square.textContent = '💣';
                square.style.opacity = '0.6';
            }
        }
        
        this.showResults(cashed);
        this.updateAnalytics(cashed);
        this.saveToHistory(cashed);
        this.updateProViewStats();
    }

    updateAnalytics(cashed) {
        const result = cashed ? this.currentMultiplier * this.currentBet : 0;
        
        // Update session stats
        if (cashed) {
            this.sessionStats.totalWinnings += result;
            this.sessionStats.currentWinStreak++;
            if (this.sessionStats.currentWinStreak > this.sessionStats.longestWinStreak) {
                this.sessionStats.longestWinStreak = this.sessionStats.currentWinStreak;
            }
            if (result > this.sessionStats.biggestWin) {
                this.sessionStats.biggestWin = result;
            }
            
            // Update outcome tracking
            this.analytics.outcomesByBombCount[this.bombCount].wins++;
        } else {
            this.sessionStats.currentWinStreak = 0;
            this.analytics.outcomesByBombCount[this.bombCount].losses++;
        }
        
        // Update recent results
        this.analytics.recentResults.unshift({
            cashed: cashed,
            bombCount: this.bombCount,
            diamondsFound: this.diamondsFound,
            multiplier: this.currentMultiplier,
            result: result,
            timestamp: Date.now()
        });
        
        if (this.analytics.recentResults.length > 20) {
            this.analytics.recentResults = this.analytics.recentResults.slice(0, 20);
        }
        
        // Calculate average multiplier
        const cashedGames = this.analytics.recentResults.filter(r => r.cashed);
        if (cashedGames.length > 0) {
            this.sessionStats.averageMultiplier = 
                cashedGames.reduce((sum, game) => sum + game.multiplier, 0) / cashedGames.length;
        }
        
        this.updateRiskAssessment();
    }

    updateRiskAssessment() {
        const recentGames = this.analytics.recentResults.slice(0, 10);
        const recentLosses = recentGames.filter(g => !g.cashed).length;
        const lossRate = recentGames.length > 0 ? recentLosses / recentGames.length : 0;
        
        // Calculate risk based on bomb count and recent performance
        let riskScore = 0;
        
        // Bomb count contribution (0-40 points)
        riskScore += (this.bombCount / 24) * 40;
        
        // Recent loss rate contribution (0-30 points)
        riskScore += lossRate * 30;
        
        // Bet size relative to balance (0-30 points)
        riskScore += (this.betAmount / this.balance) * 30;
        
        // Determine risk level
        let riskLevel = 'low';
        let recommendedAction = 'continue';
        
        if (riskScore > 60) {
            riskLevel = 'high';
            recommendedAction = 'reduce bombs or bet size';
        } else if (riskScore > 35) {
            riskLevel = 'medium';
            recommendedAction = 'proceed with caution';
        } else {
            riskLevel = 'low';
            recommendedAction = 'good to continue';
        }
        
        this.analytics.riskAssessment = {
            currentRisk: riskLevel,
            recommendedAction: recommendedAction,
            confidence: Math.round(85 + (Math.random() * 10))
        };
        
        this.sessionStats.riskLevel = riskLevel;
    }

    showResults(cashed) {
        this.gamePanel.classList.add('hidden');
        this.resultsPanel.classList.remove('hidden');
        
        const finalResult = cashed ? this.currentMultiplier * this.currentBet : 0;
        
        if (cashed) {
            this.resultTitle.textContent = '🎉 Successful Cash Out!';
            this.resultTitle.style.color = 'var(--success-color)';
        } else {
            this.resultTitle.textContent = '💥 Bomb Hit!';
            this.resultTitle.style.color = 'var(--bomb-color)';
        }
        
        this.finalDiamonds.textContent = this.diamondsFound;
        this.finalMultiplier.textContent = this.currentMultiplier.toFixed(2) + 'x';
        this.finalResult.textContent = finalResult.toFixed(2) + ' GA';
        
        this.revealedServerSeedDisplay.textContent = this.serverSeed;
    }

    saveToHistory(cashed) {
        const gameRecord = {
            timestamp: new Date().toISOString(),
            bombCount: this.bombCount,
            diamondsFound: this.diamondsFound,
            betAmount: this.currentBet,
            finalMultiplier: this.currentMultiplier,
            cashed: cashed,
            result: cashed ? this.currentMultiplier * this.currentBet : 0,
            serverSeed: this.serverSeed,
            serverSeedHash: this.serverSeedHash,
            clientSeed: this.clientSeed,
            nonce: this.nonce
        };
        
        this.gameHistory.unshift(gameRecord);
        if (this.gameHistory.length > 100) {
            this.gameHistory = this.gameHistory.slice(0, 100);
        }
        
        this.saveGameState();
    }

    // Update Pro View statistics
    updateProViewStats() {
        if (this.viewMode !== 'pro') return;
        
        const totalGames = this.sessionStats.totalGames;
        const winRate = totalGames > 0 ? 
            ((this.sessionStats.totalWinnings / this.sessionStats.totalBets) * 100) : 0;
        const profitLoss = this.balance - 10000; // Assuming starting balance is 10000
        
        // Update pro stats display
        const elements = {
            proWinRate: `${winRate.toFixed(1)}%`,
            proAvgMultiplier: `${this.sessionStats.averageMultiplier.toFixed(2)}x`,
            proProfitLoss: `${profitLoss >= 0 ? '+' : ''}${profitLoss.toFixed(0)} GA`,
            proSessionGames: totalGames
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                
                // Color profit/loss
                if (id === 'proProfitLoss') {
                    element.style.color = profitLoss >= 0 ? 
                        'var(--success-color)' : 'var(--bomb-color)';
                }
            }
        });
        
        this.updateRiskIndicator();
        this.updateRecentResults();
    }

    updateRiskIndicator() {
        const riskIndicator = document.getElementById('riskIndicator');
        const riskAssessment = document.getElementById('riskAssessment');
        
        if (!riskIndicator || !riskAssessment) return;
        
        const dots = riskIndicator.querySelectorAll('.risk-dot');
        const risk = this.analytics.riskAssessment.currentRisk;
        
        // Reset all dots
        dots.forEach(dot => {
            dot.classList.remove('active', 'high');
        });
        
        // Set active dots based on risk level
        let activeDots = 1;
        if (risk === 'medium') activeDots = 3;
        else if (risk === 'high') activeDots = 5;
        
        for (let i = 0; i < activeDots; i++) {
            dots[i].classList.add('active');
            if (risk === 'high') {
                dots[i].classList.add('high');
            }
        }
        
        riskAssessment.textContent = `${risk.toUpperCase()}: ${this.analytics.riskAssessment.recommendedAction}`;
    }

    updateRecentResults() {
        const container = document.getElementById('recentResultsList');
        if (!container) return;
        
        if (this.analytics.recentResults.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; opacity: 0.6; font-size: 0.8rem;">
                    No recent games to display
                </div>
            `;
            return;
        }
        
        container.innerHTML = '';
        
        this.analytics.recentResults.slice(0, 8).forEach((game, index) => {
            const item = document.createElement('div');
            item.className = 'game-history-item';
            
            const resultText = game.cashed ? 
                `+${game.result.toFixed(0)} GA` : 
                `-${game.result || 'Bet'} GA`;
            const resultClass = game.cashed ? 'win' : 'loss';
            
            item.innerHTML = `
                <div>
                    <span style="font-size: 0.7rem; opacity: 0.8;">
                        ${game.bombCount} bombs, ${game.diamondsFound} 💎
                    </span>
                </div>
                <div class="game-result ${resultClass}">
                    ${resultText}
                </div>
            `;
            
            container.appendChild(item);
        });
    }

    verifyRound() {
        const verification = this.verifyBombPlacement(
            this.serverSeed,
            this.clientSeed,
            0, // Use nonce 0 for initial placement
            this.bombCount
        );
        
        if (verification.valid) {
            this.showNotification('✅ Round verified successfully! The bomb placement was fair and predetermined.', 'success');
        } else {
            this.showNotification('❌ Verification failed. This should not happen in a fair game.', 'error');
        }
    }

    verifyBombPlacement(serverSeed, clientSeed, nonce, bombCount) {
        const grid = new Array(this.totalSquares).fill('diamond');
        const combinedSeed = serverSeed + clientSeed + nonce;
        let seedValue = this.hashString(combinedSeed);
        let randomSeed = parseInt(seedValue.substring(0, 8), 16);
        
        const random = () => {
            randomSeed = (randomSeed * 1103515245 + 12345) & 0x7fffffff;
            return randomSeed / 0x7fffffff;
        };
        
        const positions = Array.from({length: this.totalSquares}, (_, i) => i);
        const verificationBombs = [];
        
        for (let i = 0; i < bombCount; i++) {
            const randomIndex = Math.floor(random() * (this.totalSquares - i));
            const bombPosition = positions.splice(randomIndex, 1)[0];
            verificationBombs.push(bombPosition);
        }
        
        const actualBombs = [];
        for (let i = 0; i < this.totalSquares; i++) {
            if (this.grid[i] === 'bomb') {
                actualBombs.push(i);
            }
        }
        
        const valid = JSON.stringify(verificationBombs.sort()) === JSON.stringify(actualBombs.sort());
        
        return {
            valid: valid,
            verificationBombs: verificationBombs,
            actualBombs: actualBombs
        };
    }

    resetToSetup() {
        this.gameState = 'setup';
        this.generateSeeds();
        
        this.gamePanel.classList.add('hidden');
        this.resultsPanel.classList.add('hidden');
        this.setupPanel.classList.remove('hidden');
        
        this.cashOutBtn.disabled = true;
        this.updateDisplay();
    }

    updateDisplay() {
        this.balanceDisplay.textContent = this.balance.toFixed(0) + ' GA';
        this.betDisplay.textContent = this.betAmount;
    }

    showModal() {
        this.howToPlayModal.classList.remove('hidden');
    }

    hideModal() {
        this.howToPlayModal.classList.add('hidden');
    }

    showGameHistory() {
        if (this.gameHistory.length === 0) {
            this.showNotification('No game history found. Play some games first!', 'info');
            return;
        }
        
        let historyText = 'Recent Game History:\n\n';
        this.gameHistory.slice(0, 10).forEach((game, index) => {
            const date = new Date(game.timestamp).toLocaleString();
            const result = game.cashed ? 
                `Won ${game.result.toFixed(2)} GA` : 
                `Lost ${game.betAmount} GA (Hit Bomb)`;
            historyText += `${index + 1}. ${date}\n`;
            historyText += `   Bet: ${game.betAmount} GA, Bombs: ${game.bombCount}, Diamonds: ${game.diamondsFound}\n`;
            historyText += `   Result: ${result}\n\n`;
        });
        
        alert(historyText);
    }

    // Show notification with mobile optimization
    showNotification(message, type = 'info') {
        // Remove existing notification
        const existing = document.querySelector('.notification');
        if (existing) {
            existing.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        
        const colors = {
            error: 'linear-gradient(45deg, #F94144, #E63946)',
            success: 'linear-gradient(45deg, #90BE6D, #0B6E4F)',
            warning: 'linear-gradient(45deg, #F9C74F, #F8961E)',
            info: 'linear-gradient(45deg, #457B9D, #1D3557)'
        };
        
        notification.style.background = colors[type] || colors.info;
        notification.classList.add('show');
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.diamondHunter = new DiamondHunter();
});