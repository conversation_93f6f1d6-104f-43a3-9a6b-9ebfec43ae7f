// Samurai's Strike - Provably Fair Duel Game
document.addEventListener('DOMContentLoaded', function() {
    console.log("Samurai's Strike initializing...");
    
    // Game elements
    const elements = {
        // Header buttons
        tutorialBtn: document.getElementById('tutorialBtn'),
        verifyBtn: document.getElementById('verifyBtn'),
        
        // Modals
        tutorialModal: document.getElementById('tutorialModal'),
        closeTutorialBtn: document.getElementById('closeTutorialBtn'),
        verifyModal: document.getElementById('verifyModal'),
        closeVerifyBtn: document.getElementById('closeVerifyBtn'),
        outcomeModal: document.getElementById('outcomeModal'),
        continueBtn: document.getElementById('continueBtn'),
        
        // Action buttons
        swiftStrikeBtn: document.getElementById('swiftStrikeBtn'),
        preciseCutBtn: document.getElementById('preciseCutBtn'),
        mightyBlowBtn: document.getElementById('mightyBlowBtn'),
        
        // Display elements
        playerStanceIndicator: document.getElementById('playerStanceIndicator'),
        opponentStanceIndicator: document.getElementById('opponentStanceIndicator'),
        battleEffects: document.getElementById('battleEffects'),
        outcomeDisplay: document.getElementById('outcomeDisplay'),
        outcomeText: document.getElementById('outcomeText'),
        outcomeDetail: document.getElementById('outcomeDetail'),
        
        // Status elements
        currentStreak: document.getElementById('currentStreak'),
        highestStreak: document.getElementById('highestStreak'),
        totalWins: document.getElementById('totalWins'),
        totalLosses: document.getElementById('totalLosses'),
        roundCounter: document.getElementById('roundCounter'),
        
        // Fairness elements
        serverSeedHash: document.getElementById('serverSeedHash'),
        clientSeed: document.getElementById('clientSeed'),
        roundId: document.getElementById('roundId'),
        
        // Log
        logEntries: document.getElementById('logEntries'),
        
        // Verification elements
        verifyServerSeed: document.getElementById('verifyServerSeed'),
        verifyClientSeed: document.getElementById('verifyClientSeed'),
        verifyRoundId: document.getElementById('verifyRoundId'),
        verifyAction: document.getElementById('verifyAction'),
        verifySubmitBtn: document.getElementById('verifySubmitBtn'),
        verificationResult: document.getElementById('verificationResult'),
        pastRounds: document.getElementById('pastRounds'),
        
        // Outcome animation elements
        outcomeAnimTitle: document.getElementById('outcomeAnimTitle'),
        outcomeAnimImage: document.getElementById('outcomeAnimImage'),
        outcomeAnimMessage: document.getElementById('outcomeAnimMessage')
    };
    
    // Game state
    let gameState = {
        currentStreak: 0,
        highestStreak: 0,
        totalWins: 0,
        totalLosses: 0,
        roundNumber: 0,
        serverSeed: null,
        serverSeedHash: null,
        clientSeed: null,
        isRoundActive: false,
        roundHistory: []
    };
    
    // Action configuration with probabilities and multipliers
    const actions = {
        swift: {
            name: "Swift Strike",
            kanji: "迅",
            winProbability: 0.28,
            outcomes: [
                { type: "Glancing Blow", multiplier: 2, probability: 0.15 },
                { type: "Clean Hit", multiplier: 4, probability: 0.10 },
                { type: "Critical Strike", multiplier: 8, probability: 0.03 }
            ]
        },
        precise: {
            name: "Precise Cut",
            kanji: "精",
            winProbability: 0.22,
            outcomes: [
                { type: "Glancing Blow", multiplier: 3, probability: 0.10 },
                { type: "Clean Hit", multiplier: 6, probability: 0.09 },
                { type: "Critical Strike", multiplier: 15, probability: 0.03 }
            ]
        },
        mighty: {
            name: "Mighty Blow",
            kanji: "強",
            winProbability: 0.15,
            outcomes: [
                { type: "Glancing Blow", multiplier: 5, probability: 0.06 },
                { type: "Clean Hit", multiplier: 10, probability: 0.06 },
                { type: "Critical Strike", multiplier: 100, probability: 0.03 }
            ]
        }
    };
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing Samurai's Strike...");
        
        setupEventListeners();
        prepareNewRound();
        updateDisplay();
        
        // Log welcome message
        addLogEntry("Welcome, honorable samurai. The dojo awaits your first strike.", "info");
    }
    
    function setupEventListeners() {
        // Modal controls
        elements.tutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, true));
        elements.closeTutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, false));
        elements.verifyBtn?.addEventListener('click', () => toggleModal(elements.verifyModal, true));
        elements.closeVerifyBtn?.addEventListener('click', () => toggleModal(elements.verifyModal, false));
        elements.continueBtn?.addEventListener('click', () => {
            toggleModal(elements.outcomeModal, false);
            prepareNewRound();
        });
        
        // Action buttons
        elements.swiftStrikeBtn?.addEventListener('click', () => performAction('swift'));
        elements.preciseCutBtn?.addEventListener('click', () => performAction('precise'));
        elements.mightyBlowBtn?.addEventListener('click', () => performAction('mighty'));
        
        // Verification
        elements.verifySubmitBtn?.addEventListener('click', verifyRound);
    }
    
    function prepareNewRound() {
        if (gameState.isRoundActive) return;
        
        gameState.roundNumber++;
        gameState.isRoundActive = false;
        
        // Generate server seed and its hash
        gameState.serverSeed = generateServerSeed();
        gameState.serverSeedHash = sha256(gameState.serverSeed);
        
        // Generate client seed (or use user-provided)
        gameState.clientSeed = generateClientSeed();
        
        // Update display
        updateFairnessDisplay();
        updateRoundDisplay();
        enableActionButtons();
        
        // Clear previous round effects
        clearBattleEffects();
        clearOutcomeDisplay();
        
        // Reset stance indicators
        if (elements.playerStanceIndicator) elements.playerStanceIndicator.textContent = "Choose your action";
        if (elements.opponentStanceIndicator) elements.opponentStanceIndicator.textContent = "Preparing defense...";
        
        addLogEntry(`Round ${gameState.roundNumber} begins. Seeds generated and secured.`, "info");
    }
    
    async function performAction(actionType) {
        if (gameState.isRoundActive) return;
        
        gameState.isRoundActive = true;
        disableActionButtons();
        
        const action = actions[actionType];
        
        // Update stance indicators
        if (elements.playerStanceIndicator) elements.playerStanceIndicator.textContent = `Executing ${action.name}`;
        if (elements.opponentStanceIndicator) elements.opponentStanceIndicator.textContent = "Counter-attacking...";
        
        // Log action selection
        addLogEntry(`You execute ${action.name} (${action.kanji})!`, "action");
        
        // Calculate outcome using provably fair system
        const outcome = calculateOutcome(actionType);
        
        // Animate battle
        await animateBattle(actionType, outcome);
        
        // Process outcome
        processOutcome(outcome);
        
        // Store round for history
        storeRoundHistory(actionType, outcome);
        
        // Show animated outcome
        showOutcomeAnimation(outcome);
    }
    
    function calculateOutcome(actionType) {
        const action = actions[actionType];
        
        // Generate random number using provably fair system
        const combinedSeed = gameState.serverSeed + gameState.clientSeed + gameState.roundNumber;
        const hash = sha256(combinedSeed);
        const randomValue = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
        
        // Determine if the action is successful
        const isSuccess = randomValue < action.winProbability;
        
        let result = {
            action: actionType,
            actionName: action.name,
            isSuccess: isSuccess,
            randomValue: randomValue,
            serverSeed: gameState.serverSeed,
            serverSeedHash: gameState.serverSeedHash,
            clientSeed: gameState.clientSeed,
            roundNumber: gameState.roundNumber
        };
        
        if (isSuccess) {
            // Determine success type using separate random value
            const outcomeHash = sha256(combinedSeed + "outcome");
            const outcomeValue = parseInt(outcomeHash.substring(0, 8), 16) / 0xffffffff;
            
            let cumulativeProbability = 0;
            for (let outcome of action.outcomes) {
                cumulativeProbability += outcome.probability;
                if (outcomeValue < cumulativeProbability) {
                    result.outcomeType = outcome.type;
                    result.multiplier = outcome.multiplier;
                    break;
                }
            }
            
            // Default to first outcome if none matched (safety)
            if (!result.outcomeType) {
                result.outcomeType = action.outcomes[0].type;
                result.multiplier = action.outcomes[0].multiplier;
            }
        } else {
            // Failure outcomes
            const failureOutcomes = ["Miss", "Blocked", "Countered"];
            const failureIndex = Math.floor(randomValue * 3 * 3) % 3;
            result.outcomeType = failureOutcomes[failureIndex];
            result.multiplier = 0;
        }
        
        return result;
    }
    
    async function animateBattle(actionType, outcome) {
        const playerSamurai = document.querySelector('.player-samurai .samurai-avatar');
        const opponentSamurai = document.querySelector('.opponent-samurai .samurai-avatar');
        
        // Player attack animation
        if (playerSamurai) {
            playerSamurai.style.animation = 'samurai-attack 1s ease-in-out';
        }
        
        // Wait for attack animation
        await delay(500);
        
        // Create battle effect
        createBattleEffect(outcome.isSuccess, outcome.outcomeType === "Critical Strike");
        
        // Opponent reaction
        if (opponentSamurai) {
            if (outcome.isSuccess) {
                opponentSamurai.style.animation = 'samurai-hit 0.8s ease-out';
            } else {
                opponentSamurai.style.animation = 'samurai-defend 0.8s ease-out';
            }
        }
        
        // Wait for animations to complete
        await delay(800);
        
        // Reset animations
        if (playerSamurai) playerSamurai.style.animation = '';
        if (opponentSamurai) opponentSamurai.style.animation = '';
    }
    
    function createBattleEffect(isSuccess, isCritical) {
        const effect = document.createElement('div');
        effect.className = `strike-effect ${isCritical ? 'critical' : ''}`;
        
        if (elements.battleEffects) {
            elements.battleEffects.appendChild(effect);
            
            // Remove effect after animation
            setTimeout(() => {
                effect.remove();
            }, 800);
        }
    }
    
    function processOutcome(outcome) {
        if (outcome.isSuccess) {
            gameState.totalWins++;
            gameState.currentStreak++;
            
            if (gameState.currentStreak > gameState.highestStreak) {
                gameState.highestStreak = gameState.currentStreak;
            }
            
            // Show outcome in arena
            showOutcomeInArena(outcome.outcomeType, true, outcome.multiplier);
            
            // Log success
            addLogEntry(`${outcome.outcomeType}! Multiplier: x${outcome.multiplier}`, "success");
            
            // Check for streak bonuses
            if (gameState.currentStreak >= 5) {
                addLogEntry(`Honor streak of ${gameState.currentStreak}! You fight with exceptional skill!`, "success");
            }
        } else {
            gameState.totalLosses++;
            gameState.currentStreak = 0;
            
            // Show failure outcome
            showOutcomeInArena(outcome.outcomeType, false);
            
            // Log failure
            addLogEntry(`${outcome.outcomeType}. Your opponent's defense holds.`, "failure");
        }
        
        updateDisplay();
    }
    
    function showOutcomeInArena(outcomeType, isSuccess, multiplier = null) {
        if (!elements.outcomeDisplay || !elements.outcomeText || !elements.outcomeDetail) return;
        
        elements.outcomeText.textContent = isSuccess ? "Victory!" : "Defeat";
        elements.outcomeText.className = `outcome-text ${isSuccess ? 'success' : 'failure'}`;
        
        let detailText = outcomeType;
        if (multiplier) {
            detailText += ` (x${multiplier})`;
        }
        elements.outcomeDetail.textContent = detailText;
        
        elements.outcomeDisplay.classList.add('visible');
        
        // Hide after delay
        setTimeout(() => {
            elements.outcomeDisplay.classList.remove('visible');
        }, 3000);
    }
    
    function showOutcomeAnimation(outcome) {
        if (!elements.outcomeModal) return;
        
        // Set title
        if (elements.outcomeAnimTitle) {
            elements.outcomeAnimTitle.textContent = outcome.isSuccess ? "Victory!" : "Defeat";
            elements.outcomeAnimTitle.className = `outcome-title ${outcome.isSuccess ? 'success' : 'failure'}`;
        }
        
        // Set message
        if (elements.outcomeAnimMessage) {
            let message = `${outcome.outcomeType}`;
            if (outcome.isSuccess && outcome.multiplier) {
                message += `\n\nConceptual reward multiplier: x${outcome.multiplier}`;
                if (gameState.currentStreak > 1) {
                    message += `\nCurrent honor streak: ${gameState.currentStreak}`;
                }
            }
            elements.outcomeAnimMessage.textContent = message;
        }
        
        // Show modal
        toggleModal(elements.outcomeModal, true);
    }
    
    function storeRoundHistory(actionType, outcome) {
        const roundData = {
            roundNumber: outcome.roundNumber,
            action: actionType,
            actionName: outcome.actionName,
            isSuccess: outcome.isSuccess,
            outcomeType: outcome.outcomeType,
            multiplier: outcome.multiplier || 0,
            serverSeed: outcome.serverSeed,
            serverSeedHash: outcome.serverSeedHash,
            clientSeed: outcome.clientSeed,
            randomValue: outcome.randomValue
        };
        
        gameState.roundHistory.push(roundData);
        
        // Update past rounds display
        updatePastRoundsDisplay();
        
        addLogEntry(`Round ${outcome.roundNumber} completed. Server seed revealed: ${outcome.serverSeed.substring(0, 8)}...`, "info");
    }
    
    function updatePastRoundsDisplay() {
        if (!elements.pastRounds) return;
        
        if (gameState.roundHistory.length === 0) {
            elements.pastRounds.innerHTML = '<div class="no-rounds">Complete a duel to see your round history</div>';
            return;
        }
        
        let html = '';
        gameState.roundHistory.slice(-10).reverse().forEach(round => {
            html += `
                <div class="round-item ${round.isSuccess ? 'win' : 'loss'}">
                    <div class="round-info">
                        <div class="round-id">Round ${round.roundNumber}</div>
                        <div class="round-details">${round.actionName} → ${round.outcomeType} ${round.multiplier ? `(x${round.multiplier})` : ''}</div>
                    </div>
                    <button class="verify-round-btn" onclick="fillVerificationForm(${round.roundNumber})">Verify</button>
                </div>
            `;
        });
        
        elements.pastRounds.innerHTML = html;
    }
    
    // Global function for verification form filling
    window.fillVerificationForm = function(roundNumber) {
        const round = gameState.roundHistory.find(r => r.roundNumber === roundNumber);
        if (!round) return;
        
        if (elements.verifyServerSeed) elements.verifyServerSeed.value = round.serverSeed;
        if (elements.verifyClientSeed) elements.verifyClientSeed.value = round.clientSeed;
        if (elements.verifyRoundId) elements.verifyRoundId.value = round.roundNumber;
        if (elements.verifyAction) elements.verifyAction.value = round.action;
    };
    
    function verifyRound() {
        const serverSeed = elements.verifyServerSeed?.value.trim();
        const clientSeed = elements.verifyClientSeed?.value.trim();
        const roundId = elements.verifyRoundId?.value.trim();
        const actionType = elements.verifyAction?.value;
        
        if (!serverSeed || !clientSeed || !roundId || !actionType) {
            showVerificationResult("Please fill in all fields", false);
            return;
        }
        
        try {
            // Recreate the outcome calculation
            const action = actions[actionType];
            const combinedSeed = serverSeed + clientSeed + roundId;
            const hash = sha256(combinedSeed);
            const randomValue = parseInt(hash.substring(0, 8), 16) / 0xffffffff;
            
            const isSuccess = randomValue < action.winProbability;
            
            let outcomeType = "Failed";
            let multiplier = 0;
            
            if (isSuccess) {
                const outcomeHash = sha256(combinedSeed + "outcome");
                const outcomeValue = parseInt(outcomeHash.substring(0, 8), 16) / 0xffffffff;
                
                let cumulativeProbability = 0;
                for (let outcome of action.outcomes) {
                    cumulativeProbability += outcome.probability;
                    if (outcomeValue < cumulativeProbability) {
                        outcomeType = outcome.type;
                        multiplier = outcome.multiplier;
                        break;
                    }
                }
            } else {
                const failureOutcomes = ["Miss", "Blocked", "Countered"];
                const failureIndex = Math.floor(randomValue * 3 * 3) % 3;
                outcomeType = failureOutcomes[failureIndex];
            }
            
            // Verify server seed hash matches
            const expectedHash = sha256(serverSeed);
            const round = gameState.roundHistory.find(r => r.roundNumber == roundId);
            
            let result = `Round ${roundId} Verification:\n`;
            result += `Action: ${action.name}\n`;
            result += `Outcome: ${outcomeType}`;
            if (multiplier > 0) result += ` (x${multiplier})`;
            result += `\nRandom Value: ${randomValue.toFixed(6)}\n`;
            result += `Success Threshold: ${action.winProbability}\n`;
            
            if (round && round.serverSeedHash === expectedHash) {
                result += `\n✓ Server seed hash verified`;
                showVerificationResult(result, true);
            } else {
                result += `\n⚠ Server seed hash mismatch`;
                showVerificationResult(result, false);
            }
            
        } catch (error) {
            showVerificationResult("Verification failed: Invalid data", false);
        }
    }
    
    function showVerificationResult(message, isValid) {
        if (!elements.verificationResult) return;
        
        const resultValue = elements.verificationResult.querySelector('.result-value');
        if (resultValue) {
            resultValue.textContent = message;
            resultValue.style.color = isValid ? 'var(--success-green)' : 'var(--accent-red)';
        }
    }
    
    function updateDisplay() {
        // Update status values
        if (elements.currentStreak) elements.currentStreak.textContent = gameState.currentStreak;
        if (elements.highestStreak) elements.highestStreak.textContent = gameState.highestStreak;
        if (elements.totalWins) elements.totalWins.textContent = gameState.totalWins;
        if (elements.totalLosses) elements.totalLosses.textContent = gameState.totalLosses;
    }
    
    function updateRoundDisplay() {
        if (elements.roundCounter) {
            elements.roundCounter.textContent = `Round: ${gameState.roundNumber}`;
        }
    }
    
    function updateFairnessDisplay() {
        if (elements.serverSeedHash) {
            elements.serverSeedHash.textContent = gameState.serverSeedHash;
        }
        if (elements.clientSeed) {
            elements.clientSeed.textContent = gameState.clientSeed;
        }
        if (elements.roundId) {
            elements.roundId.textContent = gameState.roundNumber;
        }
    }
    
    function enableActionButtons() {
        [elements.swiftStrikeBtn, elements.preciseCutBtn, elements.mightyBlowBtn].forEach(btn => {
            if (btn) {
                btn.disabled = false;
                btn.style.opacity = '1';
                btn.style.pointerEvents = 'auto';
            }
        });
    }
    
    function disableActionButtons() {
        [elements.swiftStrikeBtn, elements.preciseCutBtn, elements.mightyBlowBtn].forEach(btn => {
            if (btn) {
                btn.disabled = true;
                btn.style.opacity = '0.6';
                btn.style.pointerEvents = 'none';
            }
        });
    }
    
    function clearBattleEffects() {
        if (elements.battleEffects) {
            elements.battleEffects.innerHTML = '';
        }
    }
    
    function clearOutcomeDisplay() {
        if (elements.outcomeDisplay) {
            elements.outcomeDisplay.classList.remove('visible');
        }
    }
    
    function addLogEntry(message, type = "info") {
        if (!elements.logEntries) return;
        
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        entry.textContent = message;
        
        elements.logEntries.appendChild(entry);
        elements.logEntries.scrollTop = elements.logEntries.scrollHeight;
        
        // Keep only last 20 entries
        while (elements.logEntries.children.length > 20) {
            elements.logEntries.removeChild(elements.logEntries.firstChild);
        }
    }
    
    function toggleModal(modal, show) {
        if (!modal) return;
        
        if (show) {
            modal.classList.remove('hidden');
        } else {
            modal.classList.add('hidden');
        }
    }
    
    function generateServerSeed() {
        // Generate a cryptographically secure random seed
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
    
    function generateClientSeed() {
        // Generate a simple client seed (user can modify this if desired)
        return Math.random().toString(36).substring(2, 15);
    }
    
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // Simple SHA-256 implementation for fairness verification
    function sha256(str) {
        const encoder = new TextEncoder();
        const data = encoder.encode(str);
        return crypto.subtle.digest('SHA-256', data).then(buffer => {
            const hashArray = Array.from(new Uint8Array(buffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        });
    }
    
    // Synchronous SHA-256 for immediate use (simplified)
    function sha256(message) {
        // This is a simplified hash function for demo purposes
        // In production, use crypto.subtle.digest for true SHA-256
        let hash = 0;
        for (let i = 0; i < message.length; i++) {
            const char = message.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16).padStart(8, '0').repeat(8);
    }
    
    console.log("Samurai's Strike initialized successfully");
});