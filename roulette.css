/* Roulette Game - Mobile-First Responsive Design */

:root {
    --roulette-red: #E63946;
    --roulette-black: #1D3557;
    --roulette-green: #0B6E4F;
    --roulette-gold: #F9C74F;
    --roulette-blue: #457B9D;
    --roulette-dark: #2D3436;
    --roulette-light: #F1FAEE;
    
    --table-green: #0B6E4F;
    --table-dark-green: #033E34;
    --gold-gradient: linear-gradient(135deg, #f9d423, #f6b02b);
    --gold-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
    
    --win-color: #4CAF50;
    --lose-color: #E63946;
    
    --z-index-background: 1;
    --z-index-table: 10;
    --z-index-chips: 20;
    --z-index-notification: 100;
    --z-index-modal: 200;
}

/* Mobile-First Base Styles */
body {
    background: linear-gradient(135deg, #0B0E1A 0%, #1A1F2E 50%, #2C1810 100%);
    color: white;
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

.roulette-container {
    max-width: 100vw;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
}

/* Game Header */
.game-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding: 1rem 0;
}

.game-title {
    font-size: 2rem;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
    font-weight: 700;
    line-height: 1.2;
}

.game-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    opacity: 0.9;
}

/* View Mode Toggle */
.view-mode-toggle {
    position: fixed;
    top: 100px;
    right: 1rem;
    z-index: var(--z-index-notification);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.3rem;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.view-toggle-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    min-width: 60px;
    text-transform: uppercase;
    font-weight: 600;
}

.view-toggle-btn.active {
    background: var(--gold-gradient);
    color: var(--roulette-dark);
    transform: scale(1.05);
}

/* Mobile Layout - Stack everything vertically */
.dashboard-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Stats Panel */
.stats-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.panel-title {
    font-size: 1rem;
    margin-bottom: 1rem;
    text-align: center;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Mobile Stats Layout */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: var(--roulette-gold);
    font-size: 1.2rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Roulette Game Area */
.roulette-area {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 1rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: var(--z-index-table);
}

.game-area-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

/* Mobile Wheel Container */
.wheel-container {
    position: relative;
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.roulette-wheel {
    width: 280px;
    height: 280px;
    border-radius: 50%;
    position: relative;
    background-color: var(--table-dark-green);
    border: 6px solid #6E4823;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5), 
                inset 0 0 20px rgba(0, 0, 0, 0.5);
    transition: transform 5s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: center;
    overflow: hidden;
    touch-action: none;
}

.wheel-outer-rim {
    position: absolute;
    inset: 0;
    border-radius: 50%;
    border: 4px solid rgba(255, 215, 0, 0.7);
    pointer-events: none;
}

.wheel-inner-rim {
    position: absolute;
    top: 15%;
    left: 15%;
    right: 15%;
    bottom: 15%;
    border-radius: 50%;
    border: 2px solid rgba(255, 215, 0, 0.7);
    pointer-events: none;
}

.wheel-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, var(--roulette-gold), #6E4823);
    border-radius: 50%;
    border: 2px solid rgba(255, 215, 0, 0.8);
    z-index: 3;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
    pointer-events: none;
}

.wheel-number {
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center;
    transform-origin: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    overflow: hidden;
}

.number-content {
    position: absolute;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    top: 15%;
    transform-origin: center 130%;
    font-size: 0.8rem;
    font-weight: bold;
}

.wheel-ball {
    position: absolute;
    width: 10px;
    height: 10px;
    background: radial-gradient(circle, #F1FAEE, #A8DADC);
    border-radius: 50%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    margin-top: -5px;
    z-index: 2;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
    transition: transform 5s cubic-bezier(0.23, 1, 0.32, 1);
}

.wheel-pointer {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 15px;
    height: 20px;
    background-color: rgba(255, 215, 0, 0.8);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    z-index: 4;
}

/* Mobile Betting Table */
.betting-table {
    width: 100%;
    max-width: 100%;
    background-color: var(--table-green);
    border-radius: 15px;
    padding: 1rem;
    position: relative;
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.5);
    z-index: var(--z-index-table);
    overflow-x: auto;
}

/* Mobile Betting Grid */
.betting-grid {
    display: grid;
    grid-template-columns: 40px repeat(12, 1fr);
    grid-template-rows: repeat(3, 45px);
    gap: 1px;
    margin-bottom: 1rem;
    min-width: 320px;
}

.number-cell {
    background-color: var(--roulette-red);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    z-index: var(--z-index-table);
    font-size: 0.8rem;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.number-cell.black {
    background-color: var(--roulette-black);
}

.number-cell.green {
    background-color: var(--roulette-green);
}

.number-cell:active {
    transform: scale(0.95);
    background-color: rgba(255, 215, 0, 0.3);
}

.zero-cell {
    grid-row: 1 / span 3;
    border-radius: 3px 0 0 3px;
    writing-mode: vertical-lr;
    text-orientation: mixed;
}

/* Mobile Outside Bets */
.outside-bets {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1px;
    margin-top: 1rem;
}

.outside-bet {
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
    text-transform: uppercase;
    font-size: 0.7rem;
    z-index: var(--z-index-table);
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    text-align: center;
    padding: 0.25rem;
}

.outside-bet:active {
    transform: scale(0.95);
    background-color: rgba(255, 215, 0, 0.3);
}

/* Mobile Betting Controls */
.betting-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
    width: 100%;
}

.chip-selector {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chip {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px dashed transparent;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: var(--z-index-chips);
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    font-size: 0.9rem;
}

.chip.selected {
    border: 2px dashed white;
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
}

.chip:active {
    transform: scale(0.9);
}

.chip-1 {
    background: radial-gradient(circle, #A8DADC, #457B9D);
}

.chip-5 {
    background: radial-gradient(circle, #F94144, #E63946);
}

.chip-10 {
    background: radial-gradient(circle, #90BE6D, #0B6E4F);
}

.chip-25 {
    background: radial-gradient(circle, #F9C74F, #F8961E);
}

.chip-100 {
    background: radial-gradient(circle, #C88EA7, #6C5CE7);
}

.bet-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.action-btn {
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 120px;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.spin-btn {
    background: var(--gold-gradient);
    color: var(--roulette-dark);
    box-shadow: var(--gold-shadow);
    flex: 1;
}

.spin-btn:active {
    transform: scale(0.95);
}

.spin-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.clear-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.clear-btn:active {
    transform: scale(0.95);
    background-color: rgba(255, 255, 255, 0.3);
}

/* Mobile Last Numbers */
.last-numbers {
    display: flex;
    gap: 0.3rem;
    margin: 1rem 0;
    justify-content: center;
    flex-wrap: wrap;
    max-width: 100%;
    overflow-x: auto;
    padding: 0.5rem;
}

.last-number {
    min-width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    flex-shrink: 0;
}

.last-number.red {
    background-color: var(--roulette-red);
}

.last-number.black {
    background-color: var(--roulette-black);
}

.last-number.green {
    background-color: var(--roulette-green);
}

/* Mobile Result Display */
.result-display {
    text-align: center;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
}

.result-text {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--roulette-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.4;
}

/* Placed Chips */
.placed-chip {
    position: absolute;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.6rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    z-index: var(--z-index-chips);
    pointer-events: none;
}

/* Pro View Styles */
.pro-view-panel {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    margin-bottom: 1rem;
}

body.pro-view-active .pro-view-panel {
    display: block;
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
    margin-bottom: 1rem;
}

.pro-stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.8rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.pro-stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.6rem;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-stat-value {
    color: var(--roulette-blue);
    font-size: 1rem;
    font-weight: bold;
}

/* Hot/Cold Numbers */
.hot-cold-numbers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.hot-numbers, .cold-numbers {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hot-numbers h4, .cold-numbers h4 {
    margin: 0 0 0.5rem 0;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hot-numbers h4 {
    color: var(--roulette-red);
}

.cold-numbers h4 {
    color: var(--roulette-blue);
}

.number-frequency {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
}

.frequency-number {
    min-width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.7rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    position: relative;
}

.frequency-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: rgba(255, 255, 255, 0.8);
    color: black;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.5rem;
    font-weight: bold;
}

/* Fairness Panel - Mobile */
.fairness-panel {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.fairness-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.seed-display {
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
    background: rgba(0, 0, 0, 0.3);
    padding: 0.5rem;
    border-radius: 5px;
    margin: 0.3rem 0;
    word-break: break-all;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verification-tool {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verify-input {
    width: calc(100% - 1rem);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 0.5rem;
    color: white;
    margin: 0.3rem 0;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.verify-btn {
    background: linear-gradient(45deg, var(--roulette-green), var(--roulette-blue));
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 0.5rem;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.verify-btn:active {
    transform: scale(0.95);
}

.history-container {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 1rem;
}

.history-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.8rem;
}

/* Rules Section */
.rules-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.rules-toggle {
    background: none;
    border: none;
    color: var(--roulette-blue);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.rules-content {
    margin-top: 1rem;
    line-height: 1.5;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

.odds-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.5rem;
    font-size: 0.7rem;
}

.odds-table th, .odds-table td {
    padding: 0.4rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.odds-table th {
    color: var(--roulette-gold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, var(--roulette-blue), var(--roulette-green));
    color: white;
    padding: 1rem;
    border-radius: 10px;
    z-index: var(--z-index-notification);
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: calc(100vw - 40px);
    font-size: 0.9rem;
}

.notification.show {
    transform: translateX(0);
}

/* Tablet Landscape (768px+) */
@media (min-width: 768px) and (orientation: landscape) {
    .roulette-container {
        padding: 1.5rem;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 1.5rem;
    }
    
    .stats-panel {
        order: 1;
    }
    
    .roulette-area {
        order: 2;
    }
    
    .fairness-panel {
        order: 3;
        grid-column: span 2;
    }
    
    .pro-view-panel {
        order: 4;
        grid-column: span 2;
    }
    
    .roulette-wheel {
        width: 350px;
        height: 350px;
    }
    
    .number-content {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .betting-grid {
        grid-template-columns: 50px repeat(12, 1fr);
        grid-template-rows: repeat(3, 50px);
    }
    
    .outside-bet {
        height: 45px;
        font-size: 0.8rem;
    }
    
    .chip {
        width: 55px;
        height: 55px;
        font-size: 1rem;
    }
    
    .action-btn {
        font-size: 1.1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1.2rem;
    }
    
    .stat-value {
        font-size: 1.4rem;
    }
}

/* Tablet Portrait (768px+) */
@media (min-width: 768px) and (orientation: portrait) {
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }
    
    .roulette-area {
        grid-column: span 2;
    }
    
    .fairness-panel {
        grid-column: span 2;
    }
    
    .pro-view-panel {
        grid-column: span 2;
    }
    
    .stats-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .roulette-wheel {
        width: 320px;
        height: 320px;
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    .roulette-container {
        max-width: 1400px;
        padding: 2rem;
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .game-subtitle {
        font-size: 1.1rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 2rem;
    }
    
    .stats-panel {
        order: 1;
    }
    
    .roulette-area {
        order: 2;
    }
    
    .fairness-panel {
        order: 3;
        grid-column: auto;
    }
    
    .pro-view-panel {
        order: 4;
        grid-column: span 3;
    }
    
    .stats-panel, .fairness-panel {
        padding: 2rem;
    }
    
    .panel-title {
        font-size: 1.2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stat-card {
        padding: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
    }
    
    .stat-value {
        font-size: 1.6rem;
    }
    
    .roulette-wheel {
        width: 400px;
        height: 400px;
    }
    
    .wheel-center {
        width: 50px;
        height: 50px;
    }
    
    .number-content {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
    
    .wheel-ball {
        width: 12px;
        height: 12px;
    }
    
    .wheel-pointer {
        width: 20px;
        height: 25px;
    }
    
    .betting-grid {
        grid-template-columns: 60px repeat(12, 1fr);
        grid-template-rows: repeat(3, 60px);
        gap: 2px;
    }
    
    .number-cell {
        font-size: 1rem;
    }
    
    .outside-bets {
        grid-template-columns: repeat(6, 1fr);
    }
    
    .outside-bet {
        height: 50px;
        font-size: 0.9rem;
    }
    
    .betting-controls {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .chip {
        width: 60px;
        height: 60px;
        font-size: 1.1rem;
    }
    
    .action-btn {
        min-width: 140px;
        font-size: 1.2rem;
        padding: 1.2rem 2rem;
    }
    
    .bet-actions {
        justify-content: flex-end;
    }
    
    .result-text {
        font-size: 1.3rem;
    }
    
    .placed-chip {
        width: 30px;
        height: 30px;
        font-size: 0.7rem;
    }
    
    .last-number {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .hot-cold-numbers {
        grid-template-columns: 1fr 1fr;
    }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
    .roulette-wheel {
        width: 450px;
        height: 450px;
    }
    
    .number-content {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }
    
    .betting-grid {
        grid-template-columns: 70px repeat(12, 1fr);
        grid-template-rows: repeat(3, 70px);
    }
    
    .number-cell {
        font-size: 1.2rem;
    }
    
    .outside-bet {
        height: 55px;
        font-size: 1rem;
    }
    
    .chip {
        width: 65px;
        height: 65px;
        font-size: 1.2rem;
    }
}

/* Touch optimization for mobile devices */
@media (hover: none) {
    .number-cell:hover,
    .outside-bet:hover,
    .chip:hover,
    .action-btn:hover,
    .stat-card:hover {
        transform: none;
        box-shadow: initial;
    }
    
    /* Ensure touch targets are at least 44px */
    .number-cell,
    .outside-bet,
    .chip,
    .action-btn {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Prevent zoom on input focus (iOS Safari) */
@media (max-width: 767px) {
    .verify-input {
        font-size: 16px;
    }
}

/* Dark mode optimization */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #000000 0%, #1A1A1A 50%, #2C1810 100%);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .number-cell,
    .outside-bet,
    .chip {
        border: 2px solid white;
    }
    
    .stat-card,
    .fairness-info,
    .verification-tool {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .roulette-wheel,
    .wheel-ball,
    .chip,
    .action-btn,
    .number-cell,
    .outside-bet,
    .stat-card {
        transition: none;
        animation: none;
    }
}