# YOUWARE - GoldenAura Casino Platform

## Project Overview

This is a web-based casino gaming platform called "GoldenAura" featuring multiple casino games with a virtual currency system (GA - Golden Aura). The platform includes various games like Dice Dynasty, Limbo, Plinko, and others, each with their own mechanics and designs.

## Architecture

The platform follows a simple frontend-focused architecture:

- **HTML**: Individual pages for each game (e.g., `dice-dynasty.html`, `limbo.html`, etc.)
- **CSS**: Both shared styles (`assets/css/style.css`) and game-specific styles (e.g., `dice-dynasty.css`)
- **JavaScript**: Shared utilities and game-specific logic files

### Key Design Patterns

1. **Game State Management**: Each game maintains its own state object that tracks everything from player balance to game progress
2. **Provably Fair Gaming**: Implementation of cryptographic mechanisms to ensure fair gameplay
3. **Responsive Design**: Mobile-first approach with Pro View modes for advanced users

## Responsive Features

The platform implements multiple responsive design approaches:

- **Mobile-first media queries** ensuring proper display on devices from 320px up to desktop
- **Touch-friendly controls** for mobile users with appropriate sizing
- **View mode toggles** between Standard and Pro views for advanced features
- **Device-specific layouts** that adapt to screen sizes and orientations

## Local Development

The project is a static website that can be run by simply opening the HTML files in a browser. No build process is required.

### Running Locally

1. Clone the repository
2. Open any game HTML file in a browser (e.g., `limbo.html`, `dice-dynasty.html`)

### Adding New Games

When adding a new game, follow these conventions:

1. Create HTML file in the root directory (e.g., `new-game.html`)
2. Create CSS file in the root for game-specific styles
3. Create JavaScript file with game logic
4. Link to shared assets like `style.css` and `script.js`
5. Follow the mobile-first responsive approach with media queries
6. Implement Pro View mode as necessary

## Common Features to Implement

When enhancing existing games or adding new ones, include these standard features:

1. **Pro View Mode**: Enhanced analytics and statistics for serious players
2. **Mobile Responsiveness**: Ensure good UX on screens from 320px to desktop
3. **Game History**: Track recent games and outcomes
4. **Provably Fair Mechanics**: Allow users to verify randomness
5. **Persistent Storage**: Store game state and player balance in localStorage
6. **Touch-optimized Controls**: Ensure good touch experience on mobile

## Common Code Patterns

### Responsive Design Pattern

```css
/* Base styles for mobile */
.element {
  width: 100%;
}

/* Tablet */
@media (min-width: 768px) {
  .element {
    width: 50%;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .element {
    width: 33.33%;
  }
}
```

### Game State Pattern

```javascript
const gameState = {
  // Player data
  balance: 1000,
  
  // Game mechanics
  currentBet: 10,
  
  // UI state
  viewMode: 'standard',  // or 'pro'
  
  // Game history
  history: []
};
```

### Pro View Toggle Pattern

```javascript
function setViewMode(mode) {
  gameState.viewMode = mode;
  localStorage.setItem('gameViewMode', mode);
  
  if (mode === 'standard') {
    document.body.classList.remove('pro-view-active');
  } else {
    document.body.classList.add('pro-view-active');
  }
  
  // Update UI elements for the selected mode
}
```

## Implementation Notes

- Games use localStorage to maintain player balance and settings between sessions
- All games use a fictional "GA" (Golden Aura) currency
- Provably fair mechanics are implemented using client/server seeds and cryptographic hashing
- The platform is designed to be statically hosted with no backend requirements