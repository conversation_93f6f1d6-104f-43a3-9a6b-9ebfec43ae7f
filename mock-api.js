/**
 * Mock API Service for Sports Betting System
 * 
 * This service simulates API calls when in demo mode.
 * It uses the mock data from the configuration file and is only used
 * when the system is in demo mode or during development.
 */
class MockSportsBettingAPI {
    constructor(options = {}) {
        this.config = {
            baseUrl: options.baseUrl || '/api',
            responseDelay: options.responseDelay || 500, // Simulate network delay
            ...options
        };
        
        // Mock data storage
        this.mockData = {
            sports: [
                { id: 'football', name: 'Football', slug: 'football', is_active: true, icon: 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/casino-6480120_1280.jpg' },
                { id: 'basketball', name: 'Basketball', slug: 'basketball', is_active: true, icon: 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/casino-6480120_1280.jpg' },
                { id: 'tennis', name: 'Tennis', slug: 'tennis', is_active: true, icon: 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/casino-6480120_1280.jpg' },
                { id: 'baseball', name: 'Baseball', slug: 'baseball', is_active: true, icon: 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/casino-6480120_1280.jpg' },
                { id: 'soccer', name: 'Soccer', slug: 'soccer', is_active: true, icon: 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/casino-6480120_1280.jpg' }
            ],
            matches: {
                football: [
                    {
                        id: 'fb_1',
                        sport: { id: 'football', name: 'Football', slug: 'football' },
                        home_team: 'Kansas City Chiefs',
                        away_team: 'Buffalo Bills',
                        commence_time: new Date(Date.now() + 86400000), // Tomorrow
                        status: 'upcoming',
                        home_odds: 1.85,
                        away_odds: 2.10,
                        draw_odds: null
                    },
                    {
                        id: 'fb_2',
                        sport: { id: 'football', name: 'Football', slug: 'football' },
                        home_team: 'Dallas Cowboys',
                        away_team: 'Philadelphia Eagles',
                        commence_time: new Date(Date.now() + 172800000), // Day after tomorrow
                        status: 'upcoming',
                        home_odds: 2.05,
                        away_odds: 1.75,
                        draw_odds: null
                    }
                ],
                basketball: [
                    {
                        id: 'bb_1',
                        sport: { id: 'basketball', name: 'Basketball', slug: 'basketball' },
                        home_team: 'Los Angeles Lakers',
                        away_team: 'Golden State Warriors',
                        commence_time: new Date(Date.now() + 86400000), // Tomorrow
                        status: 'upcoming',
                        home_odds: 1.95,
                        away_odds: 1.90,
                        draw_odds: null
                    }
                ],
                tennis: [
                    {
                        id: 'tn_1',
                        sport: { id: 'tennis', name: 'Tennis', slug: 'tennis' },
                        home_team: 'Novak Djokovic',
                        away_team: 'Rafael Nadal',
                        commence_time: new Date(Date.now() + 86400000), // Tomorrow
                        status: 'upcoming',
                        home_odds: 1.60,
                        away_odds: 2.30,
                        draw_odds: null
                    }
                ],
                baseball: [
                    {
                        id: 'bs_1',
                        sport: { id: 'baseball', name: 'Baseball', slug: 'baseball' },
                        home_team: 'New York Yankees',
                        away_team: 'Boston Red Sox',
                        commence_time: new Date(Date.now() + 86400000), // Tomorrow
                        status: 'upcoming',
                        home_odds: 1.70,
                        away_odds: 2.25,
                        draw_odds: null
                    }
                ],
                soccer: [
                    {
                        id: 'sc_1',
                        sport: { id: 'soccer', name: 'Soccer', slug: 'soccer' },
                        home_team: 'Barcelona',
                        away_team: 'Real Madrid',
                        commence_time: new Date(Date.now() + 86400000), // Tomorrow
                        status: 'upcoming',
                        home_odds: 2.20,
                        away_odds: 3.10,
                        draw_odds: 3.00
                    },
                    {
                        id: 'sc_2',
                        sport: { id: 'soccer', name: 'Soccer', slug: 'soccer' },
                        home_team: 'Manchester United',
                        away_team: 'Liverpool',
                        commence_time: new Date(Date.now() + 172800000), // Day after tomorrow
                        status: 'upcoming',
                        home_odds: 2.30,
                        away_odds: 2.10,
                        draw_odds: 3.40
                    }
                ]
            },
            bets: [],
            user: {
                id: 1,
                name: 'Demo User',
                email: '<EMAIL>',
                balance: 1000
            }
        };
        
        // Auth token (for mock authentication)
        this.authToken = localStorage.getItem('auth_token');
    }
    
    /**
     * Simulate an API call with delay
     */
    async simulateApiCall(data) {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(JSON.parse(JSON.stringify(data))); // Deep clone to avoid reference issues
            }, this.config.responseDelay);
        });
    }
    
    /**
     * Set auth token
     */
    setAuthToken(token) {
        this.authToken = token;
        if (token) {
            localStorage.setItem('auth_token', token);
        } else {
            localStorage.removeItem('auth_token');
        }
    }
    
    /**
     * Get all available sports
     */
    async getSports() {
        return this.simulateApiCall({
            status: 'success',
            sports: this.mockData.sports
        }).then(response => response.sports);
    }
    
    /**
     * Get matches for a specific sport
     */
    async getMatches(sportId, options = {}) {
        const matches = this.mockData.matches[sportId] || [];
        
        // Apply filters based on options
        let filteredMatches = [...matches];
        
        if (options.status && options.status !== 'all') {
            filteredMatches = filteredMatches.filter(match => match.status === options.status);
        }
        
        // Apply pagination
        const page = options.page || 1;
        const limit = options.limit || 20;
        const start = (page - 1) * limit;
        const end = start + limit;
        const paginatedMatches = filteredMatches.slice(start, end);
        
        return this.simulateApiCall({
            status: 'success',
            matches: {
                data: paginatedMatches,
                current_page: page,
                per_page: limit,
                total: filteredMatches.length
            }
        }).then(response => this.formatMatchData(response.matches.data));
    }
    
    /**
     * Format match data to ensure consistent structure
     */
    formatMatchData(matches) {
        return matches.map(match => ({
            id: match.id,
            sportKey: match.sport.slug,
            sportName: match.sport.name,
            commenceTime: new Date(match.commence_time),
            homeTeam: match.home_team,
            awayTeam: match.away_team,
            status: match.status,
            odds: {
                home: match.home_odds,
                away: match.away_odds,
                draw: match.draw_odds
            }
        }));
    }
    
    /**
     * Get live matches across all sports
     */
    async getLiveMatches() {
        // Collect live matches from all sports
        let liveMatches = [];
        
        for (const sportId in this.mockData.matches) {
            const matches = this.mockData.matches[sportId];
            const liveSportMatches = matches.filter(match => match.status === 'live');
            liveMatches = [...liveMatches, ...liveSportMatches];
        }
        
        return this.simulateApiCall({
            status: 'success',
            live_matches: liveMatches
        }).then(response => this.formatMatchData(response.live_matches));
    }
    
    /**
     * Get featured matches
     */
    async getFeaturedMatches(limit = 10) {
        // Collect matches from all sports and sort by commence time
        let allMatches = [];
        
        for (const sportId in this.mockData.matches) {
            const matches = this.mockData.matches[sportId];
            allMatches = [...allMatches, ...matches];
        }
        
        // Sort by commence time and take the specified limit
        const featuredMatches = allMatches
            .sort((a, b) => new Date(a.commence_time) - new Date(b.commence_time))
            .slice(0, limit);
        
        return this.simulateApiCall({
            status: 'success',
            featured_matches: featuredMatches
        }).then(response => this.formatMatchData(response.featured_matches));
    }
    
    /**
     * Get bet history
     */
    async getBetHistory(options = {}) {
        const page = options.page || 1;
        const perPage = options.per_page || 15;
        const start = (page - 1) * perPage;
        const end = start + perPage;
        
        // Apply filters if specified
        let filteredBets = [...this.mockData.bets];
        
        if (options.status && options.status !== 'all') {
            filteredBets = filteredBets.filter(bet => bet.status === options.status);
        }
        
        if (options.type && options.type !== 'all') {
            filteredBets = filteredBets.filter(bet => bet.type === options.type);
        }
        
        // Sort by timestamp (newest first)
        filteredBets.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        // Apply pagination
        const paginatedBets = filteredBets.slice(start, end);
        
        return this.simulateApiCall({
            status: 'success',
            bets: {
                data: paginatedBets,
                current_page: page,
                per_page: perPage,
                total: filteredBets.length
            }
        });
    }
    
    /**
     * Place a bet
     */
    async placeBet(betData) {
        // Validate bet data
        if (!betData.amount || !betData.selections || betData.selections.length === 0) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Invalid bet data'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        // Check if user has sufficient balance
        if (betData.amount > this.mockData.user.balance) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Insufficient balance'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        // Create bet record
        const bet = {
            id: 'bet_' + Date.now(),
            user_id: this.mockData.user.id,
            amount: betData.amount,
            type: betData.bet_type,
            selections: betData.selections.map(selection => {
                // Find match for this selection
                let match = null;
                for (const sportId in this.mockData.matches) {
                    const matchFound = this.mockData.matches[sportId].find(m => m.id === selection.match_id);
                    if (matchFound) {
                        match = matchFound;
                        break;
                    }
                }
                
                if (!match) {
                    throw new Error(`Match not found: ${selection.match_id}`);
                }
                
                // Format selection data
                return {
                    match_id: selection.match_id,
                    match_name: `${match.home_team} vs ${match.away_team}`,
                    bet_type: selection.bet_type,
                    odds: selection.odds,
                    team_name: this.getTeamName(match, selection.bet_type)
                };
            }),
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        
        // Calculate potential payout
        if (betData.bet_type === 'single') {
            bet.odds = bet.selections[0].odds;
            bet.potential_payout = betData.amount * bet.odds;
        } else {
            const totalOdds = bet.selections.reduce((acc, selection) => acc * selection.odds, 1);
            bet.odds = totalOdds;
            bet.potential_payout = betData.amount * totalOdds;
        }
        
        // Update user balance
        this.mockData.user.balance -= betData.amount;
        
        // Add to bet history
        this.mockData.bets.push(bet);
        
        return this.simulateApiCall({
            status: 'success',
            message: 'Bet placed successfully',
            bet: bet,
            balance: this.mockData.user.balance
        });
    }
    
    /**
     * Cancel a bet
     */
    async cancelBet(betId) {
        // Find the bet
        const betIndex = this.mockData.bets.findIndex(bet => bet.id === betId);
        
        if (betIndex === -1) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Bet not found'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        const bet = this.mockData.bets[betIndex];
        
        // Check if bet can be canceled
        if (bet.status !== 'pending') {
            return this.simulateApiCall({
                status: 'error',
                message: 'Only pending bets can be canceled'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        // Update bet status
        bet.status = 'canceled';
        bet.updated_at = new Date().toISOString();
        
        // Refund the bet amount
        this.mockData.user.balance += bet.amount;
        
        return this.simulateApiCall({
            status: 'success',
            message: 'Bet canceled successfully',
            bet: bet,
            balance: this.mockData.user.balance
        });
    }
    
    /**
     * Get wallet balance
     */
    async getWalletBalance() {
        return this.simulateApiCall({
            status: 'success',
            balance: this.mockData.user.balance
        }).then(response => response.balance);
    }
    
    /**
     * Get current user
     */
    async getCurrentUser() {
        if (!this.authToken) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Not authenticated'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        return this.simulateApiCall({
            status: 'success',
            user: this.mockData.user
        }).then(response => response.user);
    }
    
    /**
     * Get team name for a bet type
     */
    getTeamName(match, betType) {
        switch (betType) {
            case 'home':
                return match.home_team;
            case 'away':
                return match.away_team;
            case 'draw':
                return 'Draw';
            default:
                return 'Unknown';
        }
    }
    
    /**
     * User login
     */
    async login(credentials) {
        // Simple mock login - in a real app this would validate credentials
        if (credentials.email && credentials.password) {
            const token = 'mock_token_' + Date.now();
            this.setAuthToken(token);
            
            return this.simulateApiCall({
                status: 'success',
                token: token,
                user: this.mockData.user
            });
        }
        
        return this.simulateApiCall({
            status: 'error',
            message: 'Invalid credentials'
        }).then(response => {
            throw new Error(response.message);
        });
    }
    
    /**
     * User registration
     */
    async register(userData) {
        console.log('Mock register called with:', userData);
        
        // Validate required fields
        if (!userData.email) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Email is required'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        if (!userData.password) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Password is required'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        if (!userData.name) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Name is required'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        if (!userData.username) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Username is required'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        // Check password confirmation
        if (userData.password !== userData.password_confirmation) {
            return this.simulateApiCall({
                status: 'error',
                message: 'Password confirmation does not match'
            }).then(response => {
                throw new Error(response.message);
            });
        }
        
        // Create token and update user data
        const token = 'mock_token_' + Date.now();
        this.setAuthToken(token);
        
        // Update mock user data
        this.mockData.user = {
            ...this.mockData.user,
            name: userData.name,
            email: userData.email,
            username: userData.username,
            balance: 1000 // Starting balance
        };
        
        console.log('Mock registration successful:', this.mockData.user);
        
        return this.simulateApiCall({
            status: 'success',
            token: token,
            user: this.mockData.user,
            message: 'Registration successful'
        });
    }
    
    /**
     * User logout
     */
    async logout() {
        this.setAuthToken(null);
        
        return this.simulateApiCall({
            status: 'success',
            message: 'Logged out successfully'
        });
    }
}

// If in demo mode, replace the real API with the mock
if (window.SPORTS_BETTING_CONFIG?.demoMode) {
    window.SportsBettingAPI = MockSportsBettingAPI;
}