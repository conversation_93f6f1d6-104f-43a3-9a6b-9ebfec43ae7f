<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crash - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/crash.css">
    <style>
        /* Emergency inline styles to ensure game UI is visible */
        .chart-container {
            width: 100%;
            height: 400px;
            background-color: #1a1a1a;
            border-radius: 10px;
            position: relative;
            margin-bottom: 20px;
            border: 1px solid #383838;
        }
        
        .game-main {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .controls-panel {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .betting-controls, .player-stats {
            background-color: #262626;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 768px) {
            .game-main {
                grid-template-columns: 1fr;
            }
        }
        
        /* Game Animations */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes glowPulse {
            0% { box-shadow: 0 0 5px rgba(255, 75, 75, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 75, 75, 0.8); }
            100% { box-shadow: 0 0 5px rgba(255, 75, 75, 0.5); }
        }
        
        @keyframes slideInRight {
            from { transform: translateX(50px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); opacity: 0.9; }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        /* Animated elements */
        .countdown-animation {
            animation: pulse 1s infinite;
        }
        
        .crash-animation {
            animation: shake 0.5s ease-in-out;
        }
        
        .bet-placed-animation {
            animation: bounceIn 0.5s;
        }
        
        .cashout-animation {
            animation: fadeInUp 0.5s;
        }
        
        .win-animation {
            animation: glowPulse 1.5s infinite;
        }
        
        .multiplier-growing {
            animation: slideInRight 0.3s;
            transition: color 0.3s;
        }
        
        /* Particles for crash animation */
        .particle {
            position: absolute;
            background-color: #ff4b4b;
            border-radius: 50%;
            pointer-events: none;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="crash-container">
            <!-- Game Title and Back Button -->
            <div class="crash-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">CRASH</h1>
            </div>

            <!-- Game Status Bar -->
            <div class="game-status-bar">
                <div class="status-info">
                    <span class="status-label">Round Status:</span>
                    <span id="gameState" class="status-value">WAITING</span>
                    <div id="phaseIndicator" style="font-size: 0.8rem; margin-top: 5px; color: #00c853;">
                        <span style="display: inline-block; width: 8px; height: 8px; background-color: #00c853; border-radius: 50%; margin-right: 4px;"></span>
                        <span id="phaseText" class="countdown-animation">Betting Phase: Bet now!</span>
                    </div>
                </div>
                <div class="timer-info">
                    <span class="timer-label">Time Remaining:</span>
                    <span id="timeRemaining" class="timer-value countdown-animation">15s</span>
                </div>
                <div class="multiplier-info">
                    <span class="multiplier-label">Current Multiplier:</span>
                    <span id="currentMultiplier" class="multiplier-value">1.00x</span>
                </div>
            </div>

            <!-- Main Game Area -->
            <div class="game-main" style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 20px;">
                <!-- Multiplier Chart -->
                <div class="chart-container" id="chartContainer" style="position: relative; background-color: #1a1a1a; border-radius: 10px; padding: 20px; height: 400px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); overflow: hidden;">
                    <!-- Betting phase display -->
                    <div id="bettingDisplay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 1;">
                        <div id="mainTitle" style="font-size: 3rem; font-weight: 700; color: #ff4b4b; animation: pulse 2s infinite;">CRASH GAME</div>
                        <div style="font-size: 1.5rem; color: #999; margin-top: 15px;">Watch the multiplier grow!</div>
                        <div style="display: flex; gap: 20px; margin-top: 30px;">
                            <div style="background-color: rgba(0, 200, 83, 0.2); color: #00c853; padding: 10px 20px; border-radius: 5px; font-weight: 600; animation: fadeInUp 1s;">WIN BIG</div>
                            <div style="background-color: rgba(255, 75, 75, 0.2); color: #ff4b4b; padding: 10px 20px; border-radius: 5px; font-weight: 600; animation: fadeInUp 1s 0.2s both;">CASH OUT EARLY</div>
                        </div>
                    </div>
                    
                    <!-- Multiplier display during game -->
                    <div id="multiplierDisplay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 2; display: none;">
                        <div id="bigMultiplier" style="font-size: 6rem; font-weight: 700; color: #00c853; text-shadow: 0 0 20px rgba(0, 200, 83, 0.5); text-align: center;">1.00x</div>
                    </div>
                    
                    <!-- Canvas for chart -->
                    <canvas id="multiplierChart" class="multiplier-chart" style="width: 100%; height: 100%; display: block; background-color: transparent; border: none; position: relative; z-index: 0;"></canvas>
                    
                    <!-- Crash overlay -->
                    <div class="crash-overlay" id="crashOverlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.8); display: flex; flex-direction: column; justify-content: center; align-items: center; opacity: 0; visibility: hidden; transition: all 0.3s ease; z-index: 3;">
                        <div class="crash-text" style="font-size: 4rem; font-weight: 700; color: #ff4b4b; margin-bottom: 20px; text-shadow: 0 0 20px rgba(255, 75, 75, 0.8); animation: shake 0.5s;">CRASHED!</div>
                        <div class="crash-multiplier" id="crashMultiplier" style="font-size: 3rem; font-weight: 700; color: white; animation: bounceIn 0.7s 0.3s both;">0.00x</div>
                        <!-- Crash particles container -->
                        <div id="particlesContainer" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;"></div>
                    </div>
                    
                    <!-- Success cashout overlay -->
                    <div id="cashoutSuccessOverlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 4; display: none;">
                        <div style="background-color: rgba(0, 200, 83, 0.9); color: white; padding: 20px 40px; border-radius: 10px; text-align: center; animation: bounceIn 0.5s;">
                            <div style="font-size: 2rem; font-weight: 700; margin-bottom: 10px;">CASHED OUT!</div>
                            <div id="cashoutAmount" style="font-size: 1.5rem;">+0.00</div>
                        </div>
                    </div>
                </div>

                <!-- Controls Panel -->
                <div class="controls-panel" style="display: flex; flex-direction: column; gap: 20px;">
                    <!-- Betting Controls -->
                    <div class="betting-controls" style="background-color: #262626; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                        <h3 style="font-size: 1.2rem; margin-bottom: 15px; color: white; border-bottom: 1px solid #383838; padding-bottom: 8px;">Place Your Bet</h3>
                        <div class="bet-input-group" style="margin-bottom: 15px;">
                            <label for="betAmount" style="display: block; font-size: 0.9rem; margin-bottom: 5px; color: #9e9e9e;">Bet Amount:</label>
                            <input type="number" id="betAmount" min="1" value="10" step="1" style="width: 100%; padding: 10px; border-radius: 5px; border: 1px solid #383838; background-color: #1a1a1a; color: white; font-size: 1rem;">
                        </div>
                        <div class="auto-cashout-group" style="margin-bottom: 15px; position: relative;">
                            <label for="autoCashout" style="display: block; font-size: 0.9rem; margin-bottom: 5px; color: #9e9e9e;">Auto Cash Out at:</label>
                            <input type="number" id="autoCashout" min="1.01" value="2.00" step="0.01" style="width: 100%; padding: 10px; border-radius: 5px; border: 1px solid #383838; background-color: #1a1a1a; color: white; font-size: 1rem;">
                            <span class="auto-suffix" style="position: absolute; right: 15px; top: 35px; color: #9e9e9e;">x</span>
                        </div>
                        <div class="action-buttons" style="display: flex; gap: 10px;">
                            <button id="placeBetBtn" class="btn btn-primary" style="background: linear-gradient(135deg, #00c853, #009624); color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; flex: 1; font-weight: 600; transition: all 0.2s ease; position: relative; overflow: hidden;" onclick="placeBet()">
                                Place Bet
                                <span id="betBtnRipple" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 70%); transform: scale(0); opacity: 0; transform-origin: center; transition: all 0.5s ease;"></span>
                            </button>
                            <button id="cashOutBtn" class="btn btn-secondary" disabled style="background: linear-gradient(135deg, #ff4b4b, #d10000); color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; flex: 1; font-weight: 600; transition: all 0.2s ease; opacity: 0.7; position: relative; overflow: hidden;" onclick="cashOut()">
                                Cash Out
                                <span id="cashoutBtnRipple" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle, rgba(255,255,255,0.7) 0%, rgba(255,255,255,0) 70%); transform: scale(0); opacity: 0; transform-origin: center; transition: all 0.5s ease;"></span>
                            </button>
                        </div>
                    </div>

                    <!-- Player Stats -->
                    <div class="player-stats" style="background-color: #262626; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                        <h3 style="font-size: 1.2rem; margin-bottom: 15px; color: white; border-bottom: 1px solid #383838; padding-bottom: 8px;">Your Stats</h3>
                        <div class="stat-item" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #383838;">
                            <span class="stat-label" style="color: #9e9e9e;">Current Bet:</span>
                            <span id="currentBet" class="stat-value" style="font-weight: 600; color: white;">0</span>
                        </div>
                        <div class="stat-item" style="display: flex; justify-content: space-between; margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #383838;">
                            <span class="stat-label" style="color: #9e9e9e;">Potential Win:</span>
                            <span id="potentialWin" class="stat-value" style="font-weight: 600; color: white;">0</span>
                        </div>
                        <div class="stat-item" style="display: flex; justify-content: space-between; margin-bottom: 0; padding-bottom: 0; border-bottom: none;">
                            <span class="stat-label" style="color: #9e9e9e;">Last Win:</span>
                            <span id="lastWin" class="stat-value" style="font-weight: 600; color: white;">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Players -->
            <div class="active-players" style="background-color: #262626; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); margin-bottom: 20px;">
                <h3 style="font-size: 1.2rem; margin-bottom: 15px; color: white; border-bottom: 1px solid #383838; padding-bottom: 8px;">Active Players</h3>
                <div id="playersList" class="players-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px;">
                    <!-- Dynamic player list -->
                    <div class="player-card" style="background-color: #1a1a1a; border-radius: 5px; padding: 15px; position: relative;">
                        <div class="player-name" style="font-weight: 600; margin-bottom: 5px; color: white;">Alice427</div>
                        <div class="player-bet" style="font-size: 0.9rem; color: #9e9e9e; margin-bottom: 5px;">Bet: 50</div>
                        <div class="player-target" style="font-size: 0.8rem; color: #ffd600;">Target: 2.50x</div>
                        <div style="position: absolute; top: 10px; right: 10px; width: 12px; height: 12px; border-radius: 50%; background-color: #00c853;"></div>
                    </div>
                    <div class="player-card" style="background-color: #1a1a1a; border-radius: 5px; padding: 15px; position: relative;">
                        <div class="player-name" style="font-weight: 600; margin-bottom: 5px; color: white;">Bob834</div>
                        <div class="player-bet" style="font-size: 0.9rem; color: #9e9e9e; margin-bottom: 5px;">Bet: 25</div>
                        <div class="player-target" style="font-size: 0.8rem; color: #ffd600;">Target: 1.75x</div>
                        <div style="position: absolute; top: 10px; right: 10px; width: 12px; height: 12px; border-radius: 50%; background-color: #00c853;"></div>
                    </div>
                    <div class="player-card" style="background-color: #1a1a1a; border-radius: 5px; padding: 15px; position: relative;">
                        <div class="player-name" style="font-weight: 600; margin-bottom: 5px; color: white;">Charlie112</div>
                        <div class="player-bet" style="font-size: 0.9rem; color: #9e9e9e; margin-bottom: 5px;">Bet: 100</div>
                        <div class="player-target" style="font-size: 0.8rem; color: #ffd600;">Target: 3.20x</div>
                        <div style="position: absolute; top: 10px; right: 10px; width: 12px; height: 12px; border-radius: 50%; background-color: #ff4b4b;"></div>
                    </div>
                </div>
            </div>

            <!-- Game History -->
            <div class="game-history" style="background-color: #262626; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); margin-bottom: 20px;">
                <h3 style="font-size: 1.2rem; margin-bottom: 15px; color: white; border-bottom: 1px solid #383838; padding-bottom: 8px;">Recent Crashes</h3>
                <div id="crashHistory" class="crash-history-list" style="display: flex; flex-wrap: wrap; gap: 10px;">
                    <!-- Dynamic crash history -->
                    <div class="crash-history-item" style="padding: 8px 12px; border-radius: 5px; font-weight: 600; font-size: 0.9rem; background-color: rgba(0, 200, 83, 0.2); color: #00c853;">2.34x</div>
                    <div class="crash-history-item" style="padding: 8px 12px; border-radius: 5px; font-weight: 600; font-size: 0.9rem; background-color: rgba(255, 75, 75, 0.2); color: #ff4b4b;">1.23x</div>
                    <div class="crash-history-item" style="padding: 8px 12px; border-radius: 5px; font-weight: 600; font-size: 0.9rem; background-color: rgba(0, 200, 83, 0.2); color: #00c853;">15.67x</div>
                    <div class="crash-history-item" style="padding: 8px 12px; border-radius: 5px; font-weight: 600; font-size: 0.9rem; background-color: rgba(255, 75, 75, 0.2); color: #ff4b4b;">1.05x</div>
                    <div class="crash-history-item" style="padding: 8px 12px; border-radius: 5px; font-weight: 600; font-size: 0.9rem; background-color: rgba(255, 214, 0, 0.2); color: #ffd600;">3.21x</div>
                </div>
            </div>

            <!-- Provably Fair -->
            <div class="provably-fair" style="background-color: #262626; border-radius: 10px; padding: 20px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                <h3 style="font-size: 1.2rem; margin-bottom: 15px; color: white; border-bottom: 1px solid #383838; padding-bottom: 8px;">Provably Fair</h3>
                <div class="fair-info" style="display: flex; flex-direction: column; gap: 10px; margin-bottom: 15px;">
                    <div class="seed-info" style="display: flex; flex-direction: column; background-color: #1a1a1a; padding: 10px; border-radius: 5px;">
                        <label style="font-size: 0.8rem; color: #9e9e9e; margin-bottom: 5px;">Server Seed Hash:</label>
                        <span id="serverSeedHash" class="seed-value" style="font-family: monospace; word-break: break-all; font-size: 0.9rem; color: white;">Loading...</span>
                    </div>
                    <div class="seed-info" style="display: flex; flex-direction: column; background-color: #1a1a1a; padding: 10px; border-radius: 5px;">
                        <label style="font-size: 0.8rem; color: #9e9e9e; margin-bottom: 5px;">Client Seed:</label>
                        <span id="clientSeed" class="seed-value" style="font-family: monospace; word-break: break-all; font-size: 0.9rem; color: white;">Loading...</span>
                    </div>
                    <div class="seed-info" style="display: flex; flex-direction: column; background-color: #1a1a1a; padding: 10px; border-radius: 5px;">
                        <label style="font-size: 0.8rem; color: #9e9e9e; margin-bottom: 5px;">Round ID:</label>
                        <span id="roundId" class="seed-value" style="font-family: monospace; word-break: break-all; font-size: 0.9rem; color: white;">0</span>
                    </div>
                </div>
                <button id="newSeedBtn" class="btn btn-text" style="background: none; border: none; color: #00c853; cursor: pointer; text-align: center; text-decoration: underline; font-size: 0.9rem; transition: color 0.2s ease;">Generate New Client Seed</button>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="assets/js/crash.js"></script>
    <script>
        // Debug functions to ensure buttons work
        function testPlaceBet() {
            console.log('Place bet button clicked');
            window.placeBet();
        }
        
        function testCashOut() {
            console.log('Cash out button clicked');
            window.cashOut();
        }
        
        // Add backup listeners once page is fully loaded
        window.addEventListener('load', function() {
            console.log('Adding backup button listeners');
            document.getElementById('placeBetBtn').onclick = testPlaceBet;
            document.getElementById('cashOutBtn').onclick = testCashOut;
            
            // Animation test functions - for debugging
            window.testAnimation = {
                betPlaced: function() {
                    triggerBetPlacedAnimation();
                },
                cashout: function(amount) {
                    triggerCashoutAnimation(amount || 100);
                },
                crash: function() {
                    createCrashParticles();
                    document.getElementById('chartContainer').classList.add('crash-animation');
                    setTimeout(() => {
                        document.getElementById('chartContainer').classList.remove('crash-animation');
                    }, 500);
                }
            };
        });
    </script>
</body>
</html>