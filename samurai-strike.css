/* Samurai's Strike - Provably Fair Duel Game */

:root {
    --primary-dark: #1a1a2e;
    --primary-medium: #16213e;
    --primary-light: #0f3460;
    --accent-red: #9d0208;
    --accent-red-light: #e63946;
    --accent-gold: #bc6c25;
    --accent-gold-light: #dda15e;
    --accent-beige: #f5ebe0;
    --text-light: #f8f9fa;
    --text-medium: #e9ecef;
    --text-dark: #212529;
    --paper-light: #f5f3e6;
    --paper-medium: #e0ded3;
    --wood-light: #a98467;
    --wood-medium: #6c584c;
    --wood-dark: #483c32;
    --success-green: #38b000;
    --border-radius: 8px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', serif;
    background-color: var(--primary-dark);
    color: var(--text-light);
    line-height: 1.6;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%231a1a2e"/><path d="M0,20 L100,20 M0,40 L100,40 M0,60 L100,60 M0,80 L100,80" stroke="%2316213e" stroke-width="0.5" opacity="0.15"/></svg>');
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--primary-light);
    background: linear-gradient(0deg, transparent, rgba(188, 108, 37, 0.1), transparent);
}

.header-left, .header-right {
    flex: 1;
}

.header-right {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.header-center {
    flex: 2;
    text-align: center;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
}

.back-link i {
    margin-right: 8px;
}

.back-link:hover {
    color: var(--accent-gold-light);
}

.game-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-title span {
    font-size: 24px;
    margin-left: 8px;
    font-weight: 400;
}

.game-subtitle {
    font-size: 16px;
    font-style: italic;
    color: var(--text-light);
    opacity: 0.8;
}

.info-btn, .verify-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--wood-medium);
    color: var(--text-light);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    transition: var(--transition);
}

.info-btn i, .verify-btn i {
    margin-right: 8px;
}

.info-btn:hover, .verify-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-gold);
}

.verify-btn {
    border-color: var(--accent-gold-light);
    color: var(--accent-gold-light);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    gap: 20px;
}

@media (max-width: 992px) {
    .main-content {
        flex-direction: column;
    }
}

/* Game Section */
.game-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Duel Arena */
.duel-arena {
    position: relative;
    height: 400px;
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 2px solid var(--wood-medium);
}

.arena-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/gcec471ee6b1469e463265f64d6a8b0db6624dc049098a21dd2d62107f9f00b987294d50dd1d49e47d35ee67d2a585964c77e1ac2c4e94db240a97a90a5acdff3_640.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.6;
    z-index: 1;
}

.player-samurai, .opponent-samurai {
    position: absolute;
    bottom: 20px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.player-samurai {
    left: 20%;
}

.opponent-samurai {
    right: 20%;
}

.samurai-avatar {
    width: 120px;
    height: 120px;
    border-radius: 60px;
    overflow: hidden;
    border: 3px solid var(--accent-gold);
    box-shadow: 0 0 15px rgba(188, 108, 37, 0.6);
    position: relative;
}

.samurai-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.samurai-avatar.opponent {
    border-color: var(--accent-red);
    box-shadow: 0 0 15px rgba(157, 2, 8, 0.6);
}

.samurai-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(transparent 50%, rgba(0, 0, 0, 0.5));
}

.stance-indicator {
    margin-top: 10px;
    padding: 5px 10px;
    background: rgba(26, 26, 46, 0.7);
    border: 1px solid var(--accent-gold);
    border-radius: var(--border-radius);
    font-size: 14px;
    color: var(--text-light);
    text-align: center;
}

#opponentStanceIndicator {
    border-color: var(--accent-red);
}

.battle-effects {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.outcome-display {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 4;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.outcome-display.visible {
    opacity: 1;
}

.outcome-text {
    font-size: 42px;
    font-weight: 700;
    color: var(--accent-gold);
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.5);
    margin-bottom: 10px;
}

.outcome-text.success {
    color: var(--success-green);
}

.outcome-text.failure {
    color: var(--accent-red);
}

.outcome-detail {
    font-size: 18px;
    color: var(--text-light);
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
}

/* Action Selection */
.action-selection {
    background: linear-gradient(to bottom, var(--wood-medium), var(--wood-dark));
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--box-shadow);
}

.action-selection h3 {
    font-size: 18px;
    color: var(--accent-beige);
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.action-buttons {
    display: flex;
    gap: 15px;
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 15px;
    background: var(--primary-dark);
    border: 2px solid var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
}

.action-btn:hover {
    transform: translateY(-3px);
}

.action-btn.swift-strike {
    border-color: var(--accent-beige);
}

.action-btn.swift-strike:hover {
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-medium));
    box-shadow: 0 5px 15px rgba(245, 235, 224, 0.2);
}

.action-btn.precise-cut {
    border-color: var(--accent-gold-light);
}

.action-btn.precise-cut:hover {
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-medium));
    box-shadow: 0 5px 15px rgba(221, 161, 94, 0.2);
}

.action-btn.mighty-blow {
    border-color: var(--accent-red-light);
}

.action-btn.mighty-blow:hover {
    background: linear-gradient(to bottom, var(--primary-dark), var(--primary-medium));
    box-shadow: 0 5px 15px rgba(230, 57, 70, 0.2);
}

.action-icon {
    font-size: 24px;
    color: var(--accent-gold);
    margin-right: 15px;
    padding: 10px;
    background: var(--primary-medium);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.action-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 5px;
}

.action-desc {
    font-size: 12px;
    color: var(--text-medium);
    opacity: 0.8;
}

.action-prob {
    font-size: 14px;
    color: var(--accent-gold-light);
}

.swift-strike .action-icon {
    color: var(--accent-beige);
}

.swift-strike .action-prob {
    color: var(--accent-beige);
}

.precise-cut .action-icon {
    color: var(--accent-gold-light);
}

.mighty-blow .action-icon {
    color: var(--accent-red-light);
}

.mighty-blow .action-prob {
    color: var(--accent-red-light);
}

/* Battle Log */
.battle-log {
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--primary-light);
    max-height: 200px;
    display: flex;
    flex-direction: column;
}

.battle-log h3 {
    font-size: 16px;
    color: var(--accent-gold);
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#roundCounter {
    font-size: 14px;
    color: var(--text-medium);
}

.log-entries {
    flex: 1;
    overflow-y: auto;
    padding-right: 5px;
}

.log-entry {
    padding: 8px 10px;
    margin-bottom: 8px;
    background: rgba(26, 26, 46, 0.5);
    border-radius: var(--border-radius);
    font-size: 14px;
    border-left: 3px solid var(--wood-medium);
}

.log-entry.success {
    border-left-color: var(--success-green);
}

.log-entry.failure {
    border-left-color: var(--accent-red);
}

/* Info Panel */
.info-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 350px;
}

.info-section {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--primary-light);
}

.info-section h3 {
    background: var(--primary-dark);
    color: var(--accent-gold);
    padding: 10px 15px;
    font-size: 16px;
    border-bottom: 1px solid var(--primary-light);
    text-align: center;
}

/* Status Section */
.status-section {
    padding-bottom: 15px;
}

.status-row {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-row:last-child {
    border-bottom: none;
}

.status-label {
    color: var(--text-medium);
}

.status-value {
    font-weight: 500;
    color: var(--accent-gold-light);
}

/* Fairness Section */
.fairness-section {
    padding-bottom: 15px;
}

.fairness-info {
    padding: 10px 15px;
}

.fairness-info p {
    font-size: 14px;
    margin-bottom: 10px;
    color: var(--text-medium);
}

.seed-info {
    display: flex;
    flex-direction: column;
    margin-bottom: 8px;
    background: rgba(26, 26, 46, 0.5);
    padding: 8px;
    border-radius: var(--border-radius);
}

.seed-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 5px;
}

.seed-value {
    font-size: 12px;
    color: var(--accent-gold-light);
    word-break: break-all;
    font-family: monospace;
}

.seed-value.hash {
    color: var(--accent-beige);
}

.fairness-note {
    font-size: 12px !important;
    font-style: italic;
    margin-top: 5px !important;
}

/* Outcome Table */
.outcome-table-section {
    padding-bottom: 15px;
}

.outcome-table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 13px;
}

.outcome-table th, .outcome-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.outcome-table th {
    color: var(--accent-beige);
    background: rgba(26, 26, 46, 0.5);
    font-weight: 500;
}

.outcome-table td {
    color: var(--text-medium);
}

.outcome-table tr:last-child td {
    border-bottom: none;
}

/* Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--primary-medium);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--accent-gold-light);
}

.modal-header {
    background: var(--primary-dark);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--accent-gold-light);
}

.modal-header h2 {
    color: var(--accent-gold);
    font-size: 20px;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
}

.close-modal-btn:hover {
    color: var(--accent-gold);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 60px);
}

.tutorial-section {
    margin-bottom: 20px;
}

.tutorial-section h3 {
    color: var(--accent-gold-light);
    font-size: 18px;
    margin-bottom: 10px;
    border: none;
    padding: 0;
    text-align: left;
}

.tutorial-section p, .tutorial-section li {
    font-size: 14px;
    margin-bottom: 5px;
    color: var(--text-light);
}

.tutorial-section ul, .tutorial-section ol {
    padding-left: 20px;
    margin-top: 5px;
}

/* Verification Modal */
.verify-section {
    margin-bottom: 25px;
}

.verify-section h3 {
    color: var(--accent-gold-light);
    font-size: 18px;
    margin-bottom: 10px;
    border: none;
    padding: 0;
    text-align: left;
}

.verify-section p {
    font-size: 14px;
    margin-bottom: 10px;
}

.verify-section ol {
    padding-left: 20px;
    margin-bottom: 15px;
}

.verify-section li {
    margin-bottom: 5px;
}

.verification-tool {
    background: rgba(26, 26, 46, 0.5);
    padding: 15px;
    border-radius: var(--border-radius);
    margin-top: 20px;
}

.verification-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 5px;
}

.input-group input, .input-group select {
    padding: 8px 10px;
    background: var(--primary-dark);
    border: 1px solid var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 14px;
    font-family: monospace;
}

.verify-submit-btn {
    grid-column: span 2;
    padding: 10px;
    background: var(--accent-gold);
    color: var(--primary-dark);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.verify-submit-btn:hover {
    background: var(--accent-gold-light);
}

.verification-result {
    margin-top: 15px;
    padding: 15px;
    background: var(--primary-dark);
    border-radius: var(--border-radius);
}

.result-label {
    font-size: 14px;
    color: var(--text-medium);
    margin-bottom: 5px;
}

.result-value {
    font-size: 14px;
    color: var(--accent-gold-light);
}

.past-rounds {
    max-height: 200px;
    overflow-y: auto;
}

.round-item {
    padding: 10px;
    margin-bottom: 8px;
    background: rgba(26, 26, 46, 0.5);
    border-radius: var(--border-radius);
    font-size: 13px;
    border-left: 3px solid var(--wood-medium);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.round-item.win {
    border-left-color: var(--success-green);
}

.round-item.loss {
    border-left-color: var(--accent-red);
}

.round-info {
    flex: 1;
}

.round-id {
    font-weight: 500;
    color: var(--accent-gold-light);
    margin-bottom: 3px;
}

.round-details {
    font-size: 12px;
    color: var(--text-medium);
}

.verify-round-btn {
    padding: 5px 10px;
    background: var(--primary-dark);
    border: 1px solid var(--accent-gold);
    border-radius: var(--border-radius);
    color: var(--accent-gold);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.verify-round-btn:hover {
    background: var(--primary-light);
}

.no-rounds {
    text-align: center;
    padding: 20px;
    color: var(--text-medium);
    font-style: italic;
}

/* Outcome Animation Modal */
.outcome-animation {
    background: rgba(26, 26, 46, 0.9);
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    max-width: 500px;
    border: 2px solid var(--accent-gold);
    box-shadow: 0 0 30px rgba(188, 108, 37, 0.4);
}

.outcome-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(188, 108, 37, 0.6);
}

.outcome-title.success {
    color: var(--success-green);
    text-shadow: 0 0 10px rgba(56, 176, 0, 0.6);
}

.outcome-title.failure {
    color: var(--accent-red);
    text-shadow: 0 0 10px rgba(157, 2, 8, 0.6);
}

.outcome-image {
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.outcome-message {
    font-size: 18px;
    color: var(--text-light);
    margin-bottom: 30px;
    line-height: 1.5;
}

.continue-btn {
    padding: 10px 30px;
    background: var(--accent-gold);
    color: var(--primary-dark);
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.continue-btn:hover {
    background: var(--accent-gold-light);
    transform: translateY(-3px);
}

/* Battle Effects */
@keyframes strike-effect {
    0% { transform: translateX(-50%) scale(0); opacity: 0; }
    50% { transform: translateX(-50%) scale(1); opacity: 1; }
    100% { transform: translateX(-50%) scale(1.5); opacity: 0; }
}

.strike-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    transform: translateX(-50%) scale(0);
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="50,0 63,38 100,50 63,62 50,100 37,62 0,50 37,38" fill="%23dda15e" stroke="%23bc6c25" stroke-width="2"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    animation: strike-effect 0.8s ease-out forwards;
}

.strike-effect.critical {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="50,0 63,38 100,50 63,62 50,100 37,62 0,50 37,38" fill="%23e63946" stroke="%239d0208" stroke-width="3"/></svg>');
}

@keyframes samurai-attack {
    0% { transform: translateX(0); }
    25% { transform: translateX(-30px); }
    50% { transform: translateX(100px); }
    75% { transform: translateX(100px); }
    100% { transform: translateX(0); }
}

@keyframes samurai-defend {
    0% { transform: translateX(0); }
    25% { transform: translateX(30px); }
    50% { transform: translateX(-50px); }
    75% { transform: translateX(-50px); }
    100% { transform: translateX(0); }
}

@keyframes samurai-hit {
    0% { transform: translateX(0); filter: brightness(1); }
    25% { transform: translateX(20px); filter: brightness(3); }
    50% { transform: translateX(-10px); filter: brightness(3); }
    75% { transform: translateX(5px); filter: brightness(2); }
    100% { transform: translateX(0); filter: brightness(1); }
}

.hidden {
    display: none;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .info-panel {
        max-width: none;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .header-left, .header-center, .header-right {
        width: 100%;
        flex: auto;
    }
    
    .header-right {
        justify-content: center;
    }
    
    .duel-arena {
        height: 300px;
    }
    
    .samurai-avatar {
        width: 80px;
        height: 80px;
    }
    
    .player-samurai {
        left: 10%;
    }
    
    .opponent-samurai {
        right: 10%;
    }
    
    .verification-inputs {
        grid-template-columns: 1fr;
    }
    
    .verify-submit-btn {
        grid-column: span 1;
    }
}