/* Cosmic Ascent - Mobile-First Responsive Design */

/* CSS Variables for theming and responsive design */
:root {
    --primary-color: #512da8;
    --primary-light: #8559da;
    --primary-dark: #140078;
    --accent-color: #ff4081;
    --accent-light: #ff79b0;
    --accent-dark: #c60055;
    --background-color: #0B0E1A;
    --card-color: #1A1F2E;
    --text-color: #ffffff;
    --text-secondary: #b0bec5;
    --success-color: #00c853;
    --warning-color: #ffd600;
    --danger-color: #ff1744;
    --border-color: #2a3144;
    --gold-gradient: linear-gradient(135deg, #f9d423, #f6b02b);
    
    /* Mobile-first variables */
    --mobile-padding: 1rem;
    --canvas-height: 250px;
    --transition-speed: 0.3s;
    
    /* Z-index layers */
    --z-index-background: 1;
    --z-index-content: 10;
    --z-index-modal: 100;
    --z-index-notification: 200;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Orbitron', sans-serif;
    background: linear-gradient(135deg, var(--background-color) 0%, #1A1F2E 50%, #2C1810 100%);
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* View Mode Toggle */
.view-mode-toggle {
    position: fixed;
    top: 100px;
    right: 1rem;
    z-index: var(--z-index-notification);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.3rem;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.view-toggle-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    min-width: 60px;
    text-transform: uppercase;
    font-weight: 600;
}

.view-toggle-btn.active {
    background: var(--gold-gradient);
    color: var(--background-color);
    transform: scale(1.05);
}

/* Main Container - Mobile First */
.game-container {
    max-width: 100vw;
    margin: 0 auto;
    padding: var(--mobile-padding);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    min-height: 100vh;
}

/* Header Styles - Mobile Optimized */
.game-header {
    text-align: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.back-link {
    color: var(--text-secondary);
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: color var(--transition-speed);
    font-size: 0.9rem;
    padding: 0.5rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    order: -1;
    align-self: flex-start;
}

.back-link:hover {
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.8rem;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.3rem;
    text-shadow: 0 0 10px rgba(133, 89, 218, 0.6);
    letter-spacing: 1px;
    font-weight: 700;
}

.game-subtitle {
    color: var(--text-secondary);
    font-size: 0.8rem;
    text-align: center;
    line-height: 1.4;
}

/* Main Game Area - Mobile Stack Layout */
.main-game-area {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    flex: 1;
}

/* Game Display - Mobile Canvas */
.game-display {
    background: var(--card-color);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.game-canvas-container {
    position: relative;
    width: 100%;
    height: var(--canvas-height);
    background: linear-gradient(to bottom, var(--background-color), var(--card-color));
    overflow: hidden;
    touch-action: none;
}

#gameCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
}

.rocket-trail {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 2px;
    height: 0;
    background: linear-gradient(to top, transparent, var(--accent-color));
    pointer-events: none;
    transform-origin: bottom center;
    z-index: var(--z-index-background);
    transition: height 0.1s ease-out;
}

/* Mobile-optimized multiplier display */
.multiplier-display {
    position: absolute;
    top: 1rem;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.8rem 1.5rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-content);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.multiplier-value {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--accent-color);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.multiplier-suffix {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.4rem;
    margin-left: 0.2rem;
    color: var(--accent-color);
}

.round-status {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.6rem 1.2rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-index-content);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 150px;
}

.status-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
}

.countdown {
    margin-left: 0.5rem;
    font-weight: 700;
    color: var(--accent-color);
    font-size: 1rem;
}

.crash-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 23, 68, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-index-content);
    animation: fadeIn 0.5s ease-out;
    backdrop-filter: blur(5px);
}

.crash-message {
    text-align: center;
    animation: scaleIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.crash-message h2 {
    font-family: 'Orbitron', sans-serif;
    font-size: 2rem;
    color: var(--danger-color);
    text-shadow: 0 0 20px rgba(255, 23, 68, 0.8);
    margin-bottom: 0.5rem;
}

.crash-multiplier {
    font-family: 'Orbitron', sans-serif;
    font-size: 1.5rem;
    color: var(--text-color);
}

/* Mobile-First Game Controls */
.game-controls {
    background: var(--card-color);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-label {
    display: block;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Input Groups */
.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.stake-input, .auto-input {
    background: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: all var(--transition-speed);
    width: 100%;
    text-align: center;
    font-weight: 600;
}

.stake-input:focus, .auto-input:focus {
    outline: none;
    border-color: var(--primary-light);
    box-shadow: 0 0 0 2px rgba(133, 89, 218, 0.3);
}

.stake-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* Mobile Auto Section */
.auto-section {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.auto-section label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.auto-input-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.auto-input {
    flex: 1;
    padding: 0.8rem;
    font-size: 0.9rem;
}

.auto-suffix {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.checkbox {
    margin: 0;
    accent-color: var(--primary-color);
    transform: scale(1.2);
}

/* Mobile Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Button Styles - Mobile Optimized */
.btn {
    cursor: pointer;
    padding: 1.2rem 2rem;
    border: none;
    border-radius: 15px;
    font-weight: 600;
    transition: all var(--transition-speed);
    text-align: center;
    font-size: 1rem;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:active {
    transform: scale(0.95);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(81, 45, 168, 0.3);
}

.btn-primary:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
}

.btn-success {
    background: var(--success-color);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 200, 83, 0.3);
}

.btn-success:hover {
    background: #00e676;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 23, 68, 0.3);
}

.btn-danger:hover {
    background: #ff5252;
    transform: translateY(-2px);
}

.btn-small {
    padding: 0.8rem 1rem;
    font-size: 0.8rem;
    min-height: 44px;
    border-radius: 10px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Mobile Sidebar - Now Below Main Game */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.panel {
    background: var(--card-color);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.stat-label {
    display: block;
    font-size: 0.7rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Pro View Panel */
.pro-view-panel {
    display: none;
    background: var(--card-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

body.pro-view-active .pro-view-panel {
    display: block;
}

.pro-panel-title {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
}

.pro-stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pro-stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.pro-stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-stat-value {
    color: var(--accent-color);
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Timing Analysis */
.timing-analysis {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.timing-analysis h4 {
    color: var(--warning-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timing-chart {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 60px;
    margin: 1rem 0;
    padding: 0 1rem;
}

.timing-bar {
    width: 12px;
    background: var(--primary-color);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.timing-bar.active {
    background: var(--accent-color);
}

/* Fairness Section - Mobile */
.fair-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.seed-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.seed-row label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.seed-value {
    background: var(--background-color);
    padding: 0.8rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.seed-input-group {
    display: flex;
    gap: 0.5rem;
}

.seed-input {
    flex: 1;
    background: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.8rem;
    font-size: 0.8rem;
    font-family: 'Courier New', monospace;
}

/* Mobile History List */
.history-list {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
}

.history-item {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-family: 'Orbitron', sans-serif;
    font-size: 0.8rem;
    font-weight: 700;
    cursor: pointer;
    transition: transform var(--transition-speed);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.history-item:active {
    transform: scale(0.9);
}

.history-item.low {
    background: var(--danger-color);
}

.history-item.medium {
    background: var(--warning-color);
    color: black;
}

.history-item.high {
    background: var(--success-color);
}

/* Activity Feed - Mobile */
.activity-feed {
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.activity-item {
    padding: 0.8rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    font-size: 0.8rem;
    line-height: 1.4;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-player {
    color: var(--accent-color);
    font-weight: 600;
}

.activity-multiplier {
    font-weight: 600;
}

.win {
    color: var(--success-color);
}

.lose {
    color: var(--danger-color);
}

/* Footer - Mobile */
.game-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem 0;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.footer-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.info-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-speed);
    min-height: 44px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-btn:hover {
    color: var(--text-color);
    border-color: var(--primary-light);
}

.rtp-info {
    color: var(--text-secondary);
    font-size: 0.8rem;
    text-align: center;
}

/* Modal - Mobile Optimized */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-index-modal);
    backdrop-filter: blur(5px);
    padding: 1rem;
}

.modal-content {
    background: var(--card-color);
    border-radius: 15px;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.3rem;
    color: var(--primary-light);
    font-family: 'Orbitron', sans-serif;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: color var(--transition-speed);
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: var(--accent-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h3 {
    font-size: 1.1rem;
    color: var(--accent-color);
    margin-top: 1.5rem;
    margin-bottom: 0.8rem;
}

.modal-body p, .modal-body li {
    margin-bottom: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.modal-body ul, .modal-body ol {
    margin-left: 1.2rem;
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 1rem;
    border-radius: 10px;
    z-index: var(--z-index-notification);
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: calc(100vw - 40px);
    font-size: 0.9rem;
}

.notification.show {
    transform: translateX(0);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Tablet Landscape (768px+) */
@media (min-width: 768px) and (orientation: landscape) {
    :root {
        --canvas-height: 300px;
        --mobile-padding: 1.5rem;
    }
    
    .game-container {
        display: grid;
        grid-template-columns: 2fr 1fr;
        grid-template-areas:
            "header header"
            "main sidebar"
            "footer footer";
        gap: 1.5rem;
        max-width: 1200px;
    }
    
    .game-header {
        grid-area: header;
    }
    
    .main-game-area {
        grid-area: main;
    }
    
    .sidebar {
        grid-area: sidebar;
    }
    
    .game-footer {
        grid-area: footer;
        flex-direction: row;
        justify-content: space-between;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .header-content {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .back-link {
        order: 0;
        align-self: center;
    }
    
    .input-group {
        flex-direction: row;
    }
    
    .stake-buttons {
        grid-template-columns: repeat(3, 1fr);
        width: auto;
        margin-top: 0;
        margin-left: 1rem;
    }
    
    .auto-section {
        flex-direction: row;
        align-items: center;
    }
    
    .action-buttons {
        flex-direction: row;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .history-list {
        grid-template-columns: repeat(8, 1fr);
    }
}

/* Tablet Portrait (768px+) */
@media (min-width: 768px) and (orientation: portrait) {
    :root {
        --canvas-height: 350px;
    }
    
    .game-container {
        max-width: 600px;
    }
    
    .game-title {
        font-size: 2.2rem;
    }
    
    .multiplier-value {
        font-size: 2.2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .history-list {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    :root {
        --canvas-height: 400px;
        --mobile-padding: 2rem;
    }
    
    .game-container {
        max-width: 1400px;
        padding: var(--mobile-padding);
        gap: 2rem;
    }
    
    .game-title {
        font-size: 3rem;
        letter-spacing: 2px;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .multiplier-value {
        font-size: 2.5rem;
    }
    
    .multiplier-suffix {
        font-size: 2rem;
    }
    
    .crash-message h2 {
        font-size: 3rem;
    }
    
    .crash-multiplier {
        font-size: 2rem;
    }
    
    .game-controls {
        padding: 2rem;
    }
    
    .panel {
        padding: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    
    .stat-item {
        padding: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.4rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .history-list {
        grid-template-columns: repeat(10, 1fr);
    }
    
    .modal-content {
        max-width: 700px;
        padding: 0;
    }
    
    .modal-header {
        padding: 2rem;
    }
    
    .modal-body {
        padding: 2rem;
    }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
    :root {
        --canvas-height: 500px;
    }
    
    .game-container {
        max-width: 1600px;
    }
    
    .game-title {
        font-size: 3.5rem;
    }
    
    .multiplier-value {
        font-size: 3rem;
    }
}

/* Touch optimization for mobile devices */
@media (hover: none) {
    .btn:hover,
    .info-btn:hover,
    .close-btn:hover,
    .back-link:hover,
    .stat-item:hover,
    .pro-stat-card:hover,
    .history-item:hover {
        transform: none;
        background: initial;
        color: initial;
        border-color: initial;
    }
    
    /* Ensure all interactive elements meet touch target requirements */
    .btn,
    .info-btn,
    .close-btn,
    .back-link,
    .history-item,
    .seed-input,
    .stake-input,
    .auto-input {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Prevent zoom on input focus (iOS Safari) */
@media (max-width: 767px) {
    .stake-input,
    .auto-input,
    .seed-input {
        font-size: 16px;
    }
}

/* Dark mode optimization */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #000000 0%, #1A1A1A 50%, #2C1810 100%);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn,
    .panel,
    .game-display,
    .game-controls {
        border: 2px solid white;
    }
    
    .stat-item,
    .pro-stat-card,
    .history-item {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .btn,
    .info-btn,
    .stat-item,
    .pro-stat-card,
    .history-item,
    .rocket-trail,
    .multiplier-display,
    .round-status {
        transition: none;
        animation: none;
    }
    
    @keyframes fadeIn {
        from, to { opacity: 1; }
    }
    
    @keyframes scaleIn {
        from, to { transform: scale(1); opacity: 1; }
    }
    
    @keyframes slideUp {
        from, to { transform: translateY(0); opacity: 1; }
    }
}