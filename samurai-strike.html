<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Samurai's Strike - A Provably Fair Duel</title>
    <link rel="stylesheet" href="samurai-strike.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="assets/diamond_favicon.svg" type="image/svg+xml">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Games
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">侍打 <span>Samurai's Strike</span></h1>
                <p class="game-subtitle">Master your technique • Choose your moment • Witness honor in action</p>
            </div>
            <div class="header-right">
                <button id="tutorialBtn" class="info-btn">
                    <i class="fas fa-scroll"></i>
                    Bushido Guide
                </button>
                <button id="verifyBtn" class="verify-btn">
                    <i class="fas fa-shield-check"></i>
                    Verify Fairness
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- Game Area -->
            <div class="game-section">
                <!-- Duel Arena -->
                <div class="duel-arena">
                    <div class="arena-background"></div>
                    
                    <!-- Player Samurai -->
                    <div class="player-samurai">
                        <div class="samurai-avatar">
                            <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/g98ae4aacaae37e80eb9698684352c47fa1d96422fb121411658201598d454ab9091e2cd79f525c261b6f060695bce14bdbf3d24aaabf29540b132622af98888b_640.png" alt="Player Samurai">
                        </div>
                        <div class="stance-indicator" id="playerStanceIndicator">Prepare for battle</div>
                    </div>
                    
                    <!-- Battle Effects -->
                    <div class="battle-effects" id="battleEffects"></div>
                    
                    <!-- Opponent Samurai -->
                    <div class="opponent-samurai">
                        <div class="samurai-avatar opponent">
                            <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/g39217e257cf9cee29c2b39aed900eb94546f17c126eb6139a20a909c55ecbcd45c857951e79959a3b9155eca49f7b86e1fb5fd5bd276142ccd8077020b6df842_640.png" alt="Opponent Samurai">
                        </div>
                        <div class="stance-indicator" id="opponentStanceIndicator">...</div>
                    </div>
                    
                    <!-- Outcome Display -->
                    <div class="outcome-display" id="outcomeDisplay">
                        <div class="outcome-text" id="outcomeText"></div>
                        <div class="outcome-detail" id="outcomeDetail"></div>
                    </div>
                </div>

                <!-- Action Selection -->
                <div class="action-selection">
                    <h3>Choose Your Action</h3>
                    <div class="action-buttons">
                        <button class="action-btn swift-strike" id="swiftStrikeBtn">
                            <div class="action-icon">迅</div>
                            <div class="action-info">
                                <div class="action-name">Swift Strike</div>
                                <div class="action-desc">Higher hit chance, lower reward</div>
                            </div>
                            <div class="action-prob">Win: 28%</div>
                        </button>
                        
                        <button class="action-btn precise-cut" id="preciseCutBtn">
                            <div class="action-icon">精</div>
                            <div class="action-info">
                                <div class="action-name">Precise Cut</div>
                                <div class="action-desc">Balanced chance and reward</div>
                            </div>
                            <div class="action-prob">Win: 22%</div>
                        </button>
                        
                        <button class="action-btn mighty-blow" id="mightyBlowBtn">
                            <div class="action-icon">強</div>
                            <div class="action-info">
                                <div class="action-name">Mighty Blow</div>
                                <div class="action-desc">Lower hit chance, higher reward</div>
                            </div>
                            <div class="action-prob">Win: 15%</div>
                        </button>
                    </div>
                </div>
                
                <!-- Battle Log -->
                <div class="battle-log">
                    <h3>Battle Chronicle <span id="roundCounter">Round: 0</span></h3>
                    <div class="log-entries" id="logEntries">
                        <div class="log-entry">Your journey begins. Choose your strike wisely, samurai.</div>
                    </div>
                </div>
            </div>

            <!-- Information Panel -->
            <div class="info-panel">
                <!-- Current Status -->
                <div class="info-section status-section">
                    <h3>Samurai Status</h3>
                    <div class="status-row">
                        <div class="status-label">Current Streak:</div>
                        <div class="status-value" id="currentStreak">0</div>
                    </div>
                    <div class="status-row">
                        <div class="status-label">Highest Streak:</div>
                        <div class="status-value" id="highestStreak">0</div>
                    </div>
                    <div class="status-row">
                        <div class="status-label">Victories:</div>
                        <div class="status-value" id="totalWins">0</div>
                    </div>
                    <div class="status-row">
                        <div class="status-label">Defeats:</div>
                        <div class="status-value" id="totalLosses">0</div>
                    </div>
                </div>
                
                <!-- Fairness Information -->
                <div class="info-section fairness-section">
                    <h3>Provably Fair System</h3>
                    <div class="fairness-info">
                        <p>Every duel outcome is determined by a provably fair system:</p>
                        <div class="seed-info">
                            <div class="seed-label">Server Seed Hash:</div>
                            <div class="seed-value hash" id="serverSeedHash">...</div>
                        </div>
                        <div class="seed-info">
                            <div class="seed-label">Client Seed:</div>
                            <div class="seed-value" id="clientSeed">...</div>
                        </div>
                        <div class="seed-info">
                            <div class="seed-label">Round ID:</div>
                            <div class="seed-value" id="roundId">...</div>
                        </div>
                        <p class="fairness-note">The server seed will be revealed after each round for verification.</p>
                    </div>
                </div>

                <!-- Outcome Table -->
                <div class="info-section outcome-table-section">
                    <h3>Outcome Table</h3>
                    <table class="outcome-table">
                        <thead>
                            <tr>
                                <th>Action</th>
                                <th>Success Type</th>
                                <th>Probability</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td rowspan="3">Swift Strike</td>
                                <td>Glancing Blow (x2)</td>
                                <td>15%</td>
                            </tr>
                            <tr>
                                <td>Clean Hit (x4)</td>
                                <td>10%</td>
                            </tr>
                            <tr>
                                <td>Critical Strike (x8)</td>
                                <td>3%</td>
                            </tr>
                            <tr>
                                <td rowspan="3">Precise Cut</td>
                                <td>Glancing Blow (x3)</td>
                                <td>10%</td>
                            </tr>
                            <tr>
                                <td>Clean Hit (x6)</td>
                                <td>9%</td>
                            </tr>
                            <tr>
                                <td>Critical Strike (x15)</td>
                                <td>3%</td>
                            </tr>
                            <tr>
                                <td rowspan="3">Mighty Blow</td>
                                <td>Glancing Blow (x5)</td>
                                <td>6%</td>
                            </tr>
                            <tr>
                                <td>Clean Hit (x10)</td>
                                <td>6%</td>
                            </tr>
                            <tr>
                                <td>Critical Strike (x100)</td>
                                <td>3%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Tutorial Modal -->
        <div class="modal-overlay hidden" id="tutorialModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Bushido: The Way of the Warrior</h2>
                    <button class="close-modal-btn" id="closeTutorialBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="tutorial-section">
                        <h3>Welcome to Samurai's Strike</h3>
                        <p>Enter the dojo and test your mettle as a true samurai. In this game of skill and strategy, every decision and every strike is a moment of truth.</p>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Game Rules</h3>
                        <p>Samurai's Strike is a duel-based game where you choose one of three strikes against an AI opponent:</p>
                        <ul>
                            <li><strong>Swift Strike:</strong> Higher chance of success (65%) with lower potential rewards</li>
                            <li><strong>Precise Cut:</strong> Balanced chance (45%) and medium rewards</li>
                            <li><strong>Mighty Blow:</strong> Lower chance (25%) but potential for massive rewards</li>
                        </ul>
                        <p>Each successful strike has three possible outcomes (Glancing Blow, Clean Hit, or Critical Strike) with increasing rewards.</p>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Provably Fair System</h3>
                        <p>Every duel outcome is determined through a transparent and verifiable system:</p>
                        <ol>
                            <li>Before each round, the server generates a random seed and shows you its cryptographic hash</li>
                            <li>You receive a client seed that combines with the server seed to determine the outcome</li>
                            <li>After the round, the server reveals its seed, allowing you to verify the result was predetermined and fair</li>
                        </ol>
                        <p>This system ensures outcomes cannot be manipulated and are truly random based on the action you select.</p>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Strategy Tips</h3>
                        <ul>
                            <li>Build streaks to access bonus multipliers</li>
                            <li>Balance risk and reward based on your playing style</li>
                            <li>Watch the outcome table carefully to understand the probability structure</li>
                            <li>Consider your streak status when choosing between safe and risky options</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verification Modal -->
        <div class="modal-overlay hidden" id="verifyModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Verify Duel Fairness</h2>
                    <button class="close-modal-btn" id="closeVerifyBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="verify-section">
                        <h3>How Our Provably Fair System Works</h3>
                        <p>The outcome of each duel is predetermined before you choose your action, yet is completely random and verifiable:</p>
                        
                        <ol>
                            <li>The server generates a random seed for each round</li>
                            <li>You're shown the SHA-256 hash of this seed before making your choice</li>
                            <li>Your client seed and the round ID combine with the server seed to generate the result</li>
                            <li>After the round, the server reveals its seed so you can verify the outcome</li>
                        </ol>
                        
                        <div class="verification-tool">
                            <h3>Verify Past Rounds</h3>
                            <div class="verification-inputs">
                                <div class="input-group">
                                    <label for="verifyServerSeed">Server Seed:</label>
                                    <input type="text" id="verifyServerSeed" placeholder="Revealed server seed">
                                </div>
                                <div class="input-group">
                                    <label for="verifyClientSeed">Client Seed:</label>
                                    <input type="text" id="verifyClientSeed" placeholder="Your client seed">
                                </div>
                                <div class="input-group">
                                    <label for="verifyRoundId">Round ID:</label>
                                    <input type="text" id="verifyRoundId" placeholder="Round number">
                                </div>
                                <div class="input-group">
                                    <label for="verifyAction">Selected Action:</label>
                                    <select id="verifyAction">
                                        <option value="swift">Swift Strike</option>
                                        <option value="precise">Precise Cut</option>
                                        <option value="mighty">Mighty Blow</option>
                                    </select>
                                </div>
                                <button class="verify-submit-btn" id="verifySubmitBtn">Verify Outcome</button>
                            </div>
                            <div class="verification-result" id="verificationResult">
                                <div class="result-label">Verification Result:</div>
                                <div class="result-value">Select a round to verify</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="verify-section">
                        <h3>Past Rounds</h3>
                        <div class="past-rounds" id="pastRounds">
                            <div class="no-rounds">Complete a duel to see your round history</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Outcome Animation Modal -->
        <div class="modal-overlay hidden" id="outcomeModal">
            <div class="outcome-animation">
                <div class="outcome-title" id="outcomeAnimTitle"></div>
                <div class="outcome-image" id="outcomeAnimImage"></div>
                <div class="outcome-message" id="outcomeAnimMessage"></div>
                <button class="continue-btn" id="continueBtn">Continue</button>
            </div>
        </div>
    </div>

    <script src="samurai-strike.js"></script>
</body>
</html>