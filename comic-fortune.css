/* Comic Fortune Game CSS - Mobile First with Pro View */
:root {
    --comic-red: #FF4444;
    --comic-blue: #4488FF;
    --comic-yellow: #FFDD44;
    --comic-green: #44DD44;
    --comic-purple: #DD44DD;
    --comic-orange: #FF8844;
    --comic-dark: #2A2A2A;
    --comic-light: #F8F8F8;
    --comic-border: #333333;
    --comic-gold: #FFD700;
    --comic-silver: #C0C0C0;
    --comic-bronze: #CD7F32;
    --bg-gradient-start: #1a1a2e;
    --bg-gradient-end: #16213e;
}

/* Base Container */
.comic-fortune-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
    min-height: 100vh;
    position: relative;
    color: white;
    font-family: 'Poppins', sans-serif;
}

/* Game Header */
.game-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
}

.back-link {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
    align-self: flex-start;
    margin-bottom: 0.5rem;
}

.back-link:hover {
    color: var(--comic-yellow);
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-size: 2.2rem;
    background: linear-gradient(45deg, var(--comic-red), var(--comic-yellow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 0.5rem;
    text-align: center;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.game-subtitle {
    color: #fff;
    font-size: 0.9rem;
    opacity: 0.8;
    text-align: center;
}

.view-mode-toggle {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 0.75rem;
}

.view-mode-toggle button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: #fff;
    min-height: 36px;
}

.view-mode-toggle button.active {
    background: var(--comic-yellow);
    color: var(--comic-dark);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Panel Styles */
.stats-panel, 
.strategy-panel,
.pro-view-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 2px solid var(--comic-border);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.panel-title,
.pro-view-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    text-align: center;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-view-title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pro-view-title i {
    margin-right: 0.5rem;
    color: var(--comic-yellow);
}

/* Stat Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 2px solid var(--comic-border);
    border-radius: 10px;
    padding: 0.75rem;
    text-align: center;
    box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.1);
}

.stat-label {
    color: #fff;
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    opacity: 0.8;
    text-transform: uppercase;
    font-weight: 500;
}

.stat-value {
    color: var(--comic-yellow);
    font-size: 1.2rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.pro-stat-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 2px solid var(--comic-border);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.pro-stat-label {
    font-size: 0.75rem;
    color: #fff;
    margin-bottom: 0.3rem;
    opacity: 0.8;
    text-transform: uppercase;
    font-weight: 500;
}

.pro-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--comic-yellow);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Slot Machine Container */
.slot-machine-container {
    background: linear-gradient(135deg, var(--comic-dark), #3a3a3a);
    border: 4px solid var(--comic-gold);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3), inset 0 0 20px rgba(0,0,0,0.3);
    position: relative;
    overflow: hidden;
}

.slot-machine-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255, 215, 0, 0.1) 2px,
        rgba(255, 215, 0, 0.1) 4px
    );
    pointer-events: none;
}

.slot-controls {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.betting-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.bet-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bet-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid var(--comic-gold);
    border-radius: 8px;
    padding: 0.5rem;
    color: var(--comic-dark);
    font-size: 1rem;
    font-weight: bold;
    width: 80px;
    text-align: center;
    min-height: 44px;
}

.bet-label {
    font-size: 0.85rem;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
}

.quick-bet-chips {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.bet-chip {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 3px solid var(--comic-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: 700;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.bet-chip:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0,0,0,0.4);
}

.chip-5 { background: var(--comic-red); color: white; }
.chip-10 { background: var(--comic-blue); color: white; }
.chip-25 { background: var(--comic-green); color: white; }
.chip-50 { background: var(--comic-purple); color: white; }

.lines-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lines-badge {
    background: var(--comic-purple);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Slot Reels */
.slot-reels {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    background: rgba(0,0,0,0.3);
    padding: 1rem;
    border-radius: 15px;
    border: 3px solid var(--comic-silver);
}

.reel {
    background: linear-gradient(180deg, #fff, #f0f0f0);
    border: 3px solid var(--comic-dark);
    border-radius: 10px;
    height: 120px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.2);
}

.reel-content {
    display: flex;
    flex-direction: column;
    position: absolute;
    width: 100%;
    transition: transform 0.1s ease;
}

.symbol {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    background: linear-gradient(135deg, #fff, #f8f8f8);
}

.symbol.winning {
    background: linear-gradient(135deg, var(--comic-yellow), var(--comic-gold));
    animation: flash 0.5s ease-in-out infinite alternate;
    border-color: var(--comic-orange);
}

@keyframes flash {
    0% { box-shadow: 0 0 5px var(--comic-yellow); }
    100% { box-shadow: 0 0 15px var(--comic-gold), 0 0 25px var(--comic-orange); }
}

.symbol.cherry { color: var(--comic-red); }
.symbol.lemon { color: var(--comic-yellow); }
.symbol.orange { color: var(--comic-orange); }
.symbol.bell { color: var(--comic-gold); }
.symbol.bar { color: var(--comic-dark); }
.symbol.seven { color: var(--comic-red); text-shadow: 2px 2px 4px rgba(0,0,0,0.5); }
.symbol.diamond { color: var(--comic-blue); }
.symbol.star { color: var(--comic-purple); }

/* Paylines */
.paylines-display {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.payline {
    position: absolute;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, var(--comic-yellow), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.payline.active {
    opacity: 0.8;
    animation: pulse 1s ease-in-out infinite;
}

.payline-1 { top: 20px; }
.payline-2 { top: 60px; }
.payline-3 { top: 100px; }

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.control-btn {
    padding: 0.75rem 1.5rem;
    border: 3px solid;
    border-radius: 15px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-width: 44px;
    min-height: 44px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.control-btn:hover::before {
    left: 100%;
}

.btn-spin {
    background: linear-gradient(45deg, var(--comic-green), #66DD66);
    color: white;
    border-color: var(--comic-green);
    box-shadow: 0 4px 8px rgba(68, 221, 68, 0.3);
}

.btn-max-bet {
    background: linear-gradient(45deg, var(--comic-red), #FF6666);
    color: white;
    border-color: var(--comic-red);
    box-shadow: 0 4px 8px rgba(255, 68, 68, 0.3);
}

.btn-auto {
    background: linear-gradient(45deg, var(--comic-blue), #6699FF);
    color: white;
    border-color: var(--comic-blue);
    box-shadow: 0 4px 8px rgba(68, 136, 255, 0.3);
}

.btn-info {
    background: linear-gradient(45deg, var(--comic-purple), #FF66FF);
    color: white;
    border-color: var(--comic-purple);
    box-shadow: 0 4px 8px rgba(221, 68, 221, 0.3);
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Win Display */
.win-display {
    text-align: center;
    margin: 1rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 140, 0, 0.2));
    border: 2px solid var(--comic-gold);
    border-radius: 15px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.win-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--comic-gold);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    margin: 0;
}

.win-message {
    font-size: 0.9rem;
    color: white;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Paytable */
.paytable-container {
    margin-top: 1rem;
}

.paytable {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.8rem;
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
    overflow: hidden;
}

.paytable th, .paytable td {
    padding: 0.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.paytable th {
    background: linear-gradient(135deg, var(--comic-gold), var(--comic-orange));
    color: var(--comic-dark);
    font-weight: 700;
    text-transform: uppercase;
}

.paytable tr:nth-child(even) {
    background: rgba(255, 255, 255, 0.05);
}

.paytable tr:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Result Modal */
.result-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, var(--bg-gradient-start), var(--bg-gradient-end));
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    border: 3px solid var(--comic-gold);
    text-align: center;
    z-index: 1000;
    display: none;
    min-width: 280px;
    max-width: 90vw;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.achievement-badge {
    background: linear-gradient(45deg, var(--comic-yellow), var(--comic-gold));
    color: var(--comic-dark);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    margin: 0.4rem;
    display: inline-block;
    border: 2px solid var(--comic-orange);
}

/* Pro View Specific Styles */
.pro-view-stats {
    display: none;
}

.pro-view-active .pro-view-stats {
    display: block;
    margin-bottom: 1rem;
}

.pro-view-sections {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pro-section {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
    border: 2px solid var(--comic-border);
    border-radius: 10px;
    padding: 0.75rem;
}

.pro-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    color: var(--comic-yellow);
}

.pro-section-title i {
    margin-right: 0.5rem;
    color: var(--comic-gold);
}

.spin-history {
    max-height: 120px;
    overflow-y: auto;
    margin-top: 0.5rem;
}

.history-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 0.8rem;
}

.history-entry:last-child {
    border-bottom: none;
}

.history-symbols {
    font-size: 1rem;
    opacity: 0.8;
}

.history-result {
    font-weight: 600;
}

.history-win {
    color: var(--comic-green);
}

.history-loss {
    color: var(--comic-red);
}

.chart-container {
    margin: 0.75rem 0;
}

.chart-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
}

.chart-label {
    width: 60px;
    font-size: 0.75rem;
    opacity: 0.8;
    text-transform: uppercase;
}

.chart-fill {
    flex: 1;
    height: 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
    margin: 0 0.5rem;
    overflow: hidden;
    border: 1px solid var(--comic-border);
}

.chart-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--comic-green), var(--comic-yellow));
    transition: width 0.3s ease;
}

.chart-value {
    font-size: 0.75rem;
    color: var(--comic-yellow);
    font-weight: 600;
    min-width: 35px;
    text-align: right;
}

.symbol-frequency {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.symbol-stat {
    background: rgba(0,0,0,0.2);
    border: 1px solid var(--comic-border);
    border-radius: 6px;
    padding: 0.4rem;
    text-align: center;
}

.symbol-icon {
    font-size: 1.2rem;
    margin-bottom: 0.2rem;
}

.symbol-count {
    font-size: 0.7rem;
    color: var(--comic-yellow);
    font-weight: 600;
}

.auto-spin-controls {
    background: rgba(0,0,0,0.2);
    border: 2px solid var(--comic-border);
    border-radius: 10px;
    padding: 0.75rem;
    margin-top: 1rem;
}

.auto-spin-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-align: center;
    color: var(--comic-yellow);
    text-transform: uppercase;
}

.auto-controls {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.auto-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.auto-label {
    font-size: 0.75rem;
    opacity: 0.8;
    text-transform: uppercase;
}

.auto-input {
    background: rgba(255,255,255,0.1);
    border: 1px solid var(--comic-border);
    border-radius: 6px;
    padding: 0.4rem;
    color: white;
    font-size: 0.8rem;
    width: 60px;
    text-align: center;
}

.pro-controls {
    display: none;
    margin-top: 1rem;
}

.pro-view-active .pro-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pro-btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 8px;
    font-weight: 600;
    border: 2px solid var(--comic-border);
    cursor: pointer;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    color: #fff;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    min-width: 100px;
    text-transform: uppercase;
}

.pro-btn:hover {
    background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    transform: translateY(-1px);
}

.pro-btn i {
    margin-right: 0.25rem;
}

/* Responsive Styles */
@media (min-width: 768px) {
    .comic-fortune-container {
        padding: 1.5rem;
    }
    
    .game-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .game-title {
        font-size: 2.8rem;
        margin: 0 auto;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .view-mode-toggle {
        margin-top: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 1.5rem;
    }
    
    .slot-controls {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .betting-section {
        flex-direction: row;
        align-items: center;
        gap: 1rem;
    }
    
    .slot-reels {
        grid-template-columns: repeat(5, 1fr);
    }
    
    .reel {
        height: 150px;
    }
    
    .symbol {
        height: 50px;
        font-size: 1.8rem;
    }
    
    .pro-view-sections {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .pro-section {
        flex: 1;
        min-width: 250px;
    }
}

@media (min-width: 1024px) {
    .comic-fortune-container {
        padding: 2rem;
    }
    
    .game-title {
        font-size: 3.2rem;
    }
    
    .stats-grid,
    .pro-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .auto-controls {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .symbol-frequency {
        grid-template-columns: repeat(8, 1fr);
    }
    
    .pro-controls {
        justify-content: center;
    }
    
    .pro-btn {
        max-width: 180px;
    }
}

@media (max-width: 480px) {
    .comic-fortune-container {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1.8rem;
    }
    
    .game-subtitle {
        font-size: 0.8rem;
    }
    
    .slot-reels {
        gap: 0.3rem;
        padding: 0.75rem;
    }
    
    .reel {
        height: 100px;
    }
    
    .symbol {
        height: 33px;
        font-size: 1.2rem;
    }
    
    .control-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }
    
    .stats-grid,
    .pro-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.4rem;
    }
    
    .stat-card,
    .pro-stat-item {
        padding: 0.5rem;
    }
    
    .stat-value,
    .pro-stat-value {
        font-size: 1rem;
    }
    
    .paytable th,
    .paytable td {
        padding: 0.3rem;
        font-size: 0.7rem;
    }
    
    .chart-label {
        width: 50px;
        font-size: 0.7rem;
    }
    
    .chart-value {
        font-size: 0.7rem;
        min-width: 30px;
    }
    
    .auto-controls {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .symbol-frequency {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .control-btn,
    .bet-input,
    .pro-btn,
    .auto-input {
        min-height: 48px;
    }
    
    .view-mode-toggle button {
        min-height: 40px;
        padding: 0.5rem 0.75rem;
    }
    
    .bet-chip {
        min-height: 48px;
        min-width: 48px;
    }
}

/* Animation classes */
.spinning {
    animation: spin 0.1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(0); }
    100% { transform: translateY(-40px); }
}

.slot-win-celebration {
    animation: celebrate 2s ease-in-out;
}

@keyframes celebrate {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.05) rotate(1deg); }
    75% { transform: scale(1.05) rotate(-1deg); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .slot-machine-container {
        background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    }
    
    .stats-panel, 
    .strategy-panel,
    .pro-view-stats {
        background: rgba(20, 20, 30, 0.8);
    }
    
    .stat-card,
    .pro-stat-item,
    .pro-section {
        background: linear-gradient(135deg, rgba(40, 40, 50, 0.5), rgba(30, 30, 40, 0.5));
    }
    
    .paytable {
        background: rgba(10,10,20,0.5);
    }
    
    .reel {
        background: linear-gradient(180deg, #f0f0f0, #e0e0e0);
    }
}