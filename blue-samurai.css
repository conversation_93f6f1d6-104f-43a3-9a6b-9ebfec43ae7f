/* Blue Samurai - Mobile Enhanced Bushido Strategy Game */

:root {
    --primary-dark: #1a1a2e;
    --primary-medium: #16213e;
    --primary-light: #0f3460;
    --accent-blue: #4361ee;
    --accent-blue-light: #4895ef;
    --accent-blue-dark: #3f37c9;
    --accent-red: #e63946;
    --accent-gold: #f1c453;
    --text-light: #f8f9fa;
    --text-medium: #e9ecef;
    --text-dark: #212529;
    --paper-light: #f5f3e6;
    --paper-medium: #e0ded3;
    --paper-dark: #c4c1b4;
    --wood-light: #a98467;
    --wood-medium: #6c584c;
    --wood-dark: #483c32;
    --border-radius: 8px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
    
    /* Mobile responsive sizes */
    --mobile-padding: 10px;
    --mobile-font-size: 14px;
    --mobile-button-height: 44px;
    --mobile-touch-target: 48px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Serif JP', serif;
    background-color: var(--primary-dark);
    color: var(--text-light);
    line-height: 1.6;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%231a1a2e"/><path d="M20,0 L20,100 M40,0 L40,100 M60,0 L60,100 M80,0 L80,100" stroke="%2316213e" stroke-width="0.5" opacity="0.1"/><path d="M0,20 L100,20 M0,40 L100,40 M0,60 L100,60 M0,80 L100,80" stroke="%2316213e" stroke-width="0.5" opacity="0.1"/></svg>');
    overflow-x: hidden;
}

/* Pro View Mode Toggle */
.view-mode-toggle {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1001;
    background: linear-gradient(to bottom, var(--accent-blue-dark), var(--primary-medium));
    border: none;
    border-radius: var(--border-radius);
    padding: 8px 12px;
    color: var(--text-light);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
    min-height: var(--mobile-touch-target);
}

.view-mode-toggle:hover {
    background: linear-gradient(to bottom, var(--accent-blue), var(--accent-blue-dark));
}

.view-mode-toggle i {
    font-size: 14px;
}

/* Pro View Specific Styles */
body.pro-view-active .pro-only {
    display: block !important;
}

body:not(.pro-view-active) .pro-only {
    display: none !important;
}

body.pro-view-active .standard-only {
    display: none !important;
}

body:not(.pro-view-active) .standard-only {
    display: block !important;
}

/* Advanced Analytics Panel (Pro View Only) */
.advanced-analytics {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--accent-blue-dark);
    box-shadow: var(--box-shadow);
}

.advanced-analytics h3 {
    color: var(--accent-blue);
    margin-bottom: 15px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
}

.analytics-item {
    text-align: center;
    padding: 10px 8px;
    background: rgba(0,0,0,0.2);
    border-radius: 4px;
    border: 1px solid var(--primary-light);
}

.analytics-value {
    display: block;
    font-size: clamp(16px, 4vw, 20px);
    font-weight: bold;
    color: var(--accent-blue-light);
    margin-bottom: 5px;
}

.analytics-label {
    font-size: 11px;
    opacity: 0.8;
    line-height: 1.2;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header - Mobile First */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--primary-light);
    background: linear-gradient(0deg, transparent, rgba(67, 97, 238, 0.1), transparent);
    flex-wrap: wrap;
    gap: 10px;
}

.header-left, .header-right {
    flex: 1;
    min-width: 120px;
}

.header-center {
    flex: 2;
    text-align: center;
    min-width: 200px;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: var(--mobile-font-size);
    transition: var(--transition);
    padding: 8px;
    border-radius: 4px;
    min-height: var(--mobile-button-height);
}

.back-link i {
    margin-right: 8px;
}

.back-link:hover {
    color: var(--accent-blue);
    background: rgba(255,255,255,0.1);
}

.game-title {
    font-size: clamp(20px, 5vw, 32px);
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-title span {
    font-size: clamp(16px, 4vw, 24px);
    margin-left: 8px;
    font-weight: 400;
}

.game-subtitle {
    font-size: clamp(12px, 3vw, 16px);
    font-style: italic;
    color: var(--text-light);
    opacity: 0.8;
}

.info-btn, .verify-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--accent-blue-dark);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: var(--mobile-font-size);
    transition: var(--transition);
    min-height: var(--mobile-button-height);
    margin: 2px;
}

.info-btn i, .verify-btn i {
    margin-right: 8px;
}

.info-btn:hover, .verify-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-blue);
}

/* Main Content - Mobile First */
.main-content {
    display: flex;
    flex: 1;
    gap: 15px;
}

/* Game Section - Mobile Optimized */
.game-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Honor Meter - Mobile Enhanced */
.honor-meter {
    background: linear-gradient(to right, var(--primary-dark), var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    padding: 10px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--wood-medium);
}

.meter-label {
    font-size: var(--mobile-font-size);
    color: var(--accent-gold);
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.meter-label i {
    margin-right: 8px;
}

.meter-container {
    height: 20px;
    background: var(--wood-dark);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.meter-fill {
    height: 100%;
    width: 50%;
    background: linear-gradient(to right, var(--accent-red), var(--accent-gold), var(--accent-blue));
    transition: width 0.5s ease;
}

.meter-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: var(--text-light);
    font-weight: bold;
}

/* Duel Arena - Mobile Enhanced */
.duel-arena {
    position: relative;
    background: var(--paper-medium);
    border-radius: var(--border-radius);
    min-height: 250px;
    overflow: hidden;
    border: 2px solid var(--wood-medium);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
}

.arena-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="400" height="250" viewBox="0 0 400 250"><rect width="400" height="250" fill="%23e0ded3"/><path d="M0,50 Q100,30 200,50 Q300,70 400,50" stroke="%23c4c1b4" stroke-width="2" fill="none" opacity="0.5"/><path d="M0,150 Q100,130 200,150 Q300,170 400,150" stroke="%23c4c1b4" stroke-width="2" fill="none" opacity="0.5"/><circle cx="100" cy="100" r="20" fill="%23a98467" opacity="0.3"/><circle cx="300" cy="100" r="20" fill="%23a98467" opacity="0.3"/></svg>');
    background-size: cover;
    z-index: 1;
}

.player-samurai, .opponent-samurai {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    flex: 1;
    max-width: 120px;
}

.samurai-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--accent-blue);
    background: var(--paper-light);
    display: flex;
    align-items: center;
    justify-content: center;
}

.samurai-avatar.opponent {
    border-color: var(--accent-red);
}

.samurai-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.stance-indicator {
    background: var(--wood-dark);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    text-align: center;
    font-size: 12px;
    color: var(--accent-gold);
    font-weight: 500;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.battle-effects {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    font-size: 48px;
    opacity: 0;
    transition: all 0.5s ease;
}

.battle-effects.active {
    opacity: 1;
    animation: battleFlash 1s ease-in-out;
}

@keyframes battleFlash {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.outcome-display {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: var(--text-light);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    text-align: center;
    min-width: 200px;
    z-index: 4;
}

.outcome-text {
    font-weight: bold;
    margin-bottom: 5px;
}

.outcome-detail {
    font-size: 12px;
    opacity: 0.8;
}

/* Action Selection - Mobile Enhanced */
.action-selection {
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--primary-light);
}

.action-selection h3 {
    color: var(--accent-gold);
    margin-bottom: 15px;
    text-align: center;
    font-size: 16px;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.action-btn {
    background: var(--primary-dark);
    border: 2px solid var(--primary-light);
    border-radius: var(--border-radius);
    padding: 15px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: var(--mobile-touch-target);
}

.action-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-blue);
    transform: translateY(-2px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn.swift-strike {
    border-color: var(--accent-blue-light);
}

.action-btn.precise-cut {
    border-color: var(--accent-gold);
}

.action-btn.mighty-blow {
    border-color: var(--accent-red);
}

.action-icon {
    font-size: 36px;
    color: var(--accent-gold);
    min-width: 50px;
    text-align: center;
}

.action-info {
    flex: 1;
}

.action-name {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--accent-blue-light);
}

.action-desc {
    font-size: 12px;
    color: var(--text-medium);
    line-height: 1.3;
}

.action-prob {
    font-size: 14px;
    color: var(--accent-gold);
    font-weight: bold;
    text-align: center;
    min-width: 60px;
}

/* Battle Log - Mobile Enhanced */
.battle-log {
    background: var(--paper-light);
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 2px solid var(--wood-medium);
    max-height: 200px;
}

.battle-log h3 {
    background: var(--wood-dark);
    color: var(--accent-gold);
    padding: 10px 15px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#roundCounter {
    font-size: 14px;
    color: var(--text-light);
}

.log-entries {
    padding: 10px;
    overflow-y: auto;
    max-height: 150px;
    color: var(--text-dark);
    font-size: var(--mobile-font-size);
}

.log-entry {
    margin-bottom: 8px;
    line-height: 1.4;
    padding: 5px;
    border-radius: 4px;
    background: rgba(255,255,255,0.1);
}

/* Info Panel - Mobile Enhanced */
.info-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: none;
}

.info-section {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--primary-light);
}

.info-section h3 {
    background: var(--primary-dark);
    color: var(--accent-gold);
    padding: 10px 15px;
    font-size: 16px;
    border-bottom: 1px solid var(--primary-light);
    text-align: center;
}

/* Status Section - Mobile Enhanced */
.status-section .info-section-content {
    padding: 10px;
}

.status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 5px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.status-row:last-child {
    border-bottom: none;
}

.status-label {
    font-size: var(--mobile-font-size);
    color: var(--text-medium);
}

.status-value {
    font-size: var(--mobile-font-size);
    color: var(--accent-blue-light);
    font-weight: bold;
}

/* Provably Fair Section - Mobile Enhanced */
.fairness-info {
    padding: 10px;
    font-size: var(--mobile-font-size);
}

.seed-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin: 10px 0;
    padding: 8px;
    background: rgba(0,0,0,0.2);
    border-radius: 4px;
}

.seed-label {
    font-weight: bold;
    color: var(--accent-blue-light);
    font-size: 12px;
}

.seed-value {
    font-family: 'Courier New', monospace;
    font-size: 11px;
    word-break: break-all;
    color: var(--text-light);
    background: rgba(0,0,0,0.3);
    padding: 5px;
    border-radius: 3px;
}

.seed-value.hash {
    color: var(--accent-gold);
}

.fairness-note {
    font-size: 12px;
    font-style: italic;
    color: var(--text-medium);
    margin-top: 10px;
}

/* Outcome Table - Mobile Enhanced */
.outcome-table-section {
    overflow-x: auto;
}

.outcome-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    margin: 10px 0;
}

.outcome-table th,
.outcome-table td {
    padding: 8px 6px;
    border: 1px solid var(--primary-light);
    text-align: center;
}

.outcome-table th {
    background: var(--primary-dark);
    color: var(--accent-gold);
    font-weight: bold;
}

.outcome-table td {
    color: var(--text-light);
}

.outcome-table tr:nth-child(even) {
    background: rgba(255,255,255,0.05);
}

/* Performance Metrics (Pro View Only) */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 8px;
    margin-top: 10px;
}

.metric-card {
    background: rgba(0,0,0,0.3);
    padding: 8px;
    border-radius: 4px;
    text-align: center;
    border: 1px solid var(--primary-light);
}

.metric-value {
    font-size: clamp(14px, 3vw, 16px);
    font-weight: bold;
    color: var(--accent-blue-light);
    display: block;
    margin-bottom: 3px;
}

.metric-label {
    font-size: 10px;
    opacity: 0.8;
    line-height: 1.2;
}

/* Strategy Panel (Pro View Only) */
.strategy-panel {
    background: rgba(0,0,0,0.2);
    padding: 12px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 3px solid var(--accent-blue);
}

.strategy-title {
    color: var(--accent-blue);
    font-weight: bold;
    margin-bottom: 8px;
    font-size: var(--mobile-font-size);
}

.strategy-tip {
    font-size: 12px;
    line-height: 1.4;
    color: var(--text-light);
}

.probability-display {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin: 10px 0;
}

.prob-item {
    text-align: center;
    padding: 8px;
    background: rgba(0,0,0,0.2);
    border-radius: 4px;
}

.prob-action {
    font-size: 12px;
    color: var(--accent-gold);
    margin-bottom: 5px;
}

.prob-percentage {
    font-size: 16px;
    font-weight: bold;
    color: var(--accent-blue-light);
}

/* Modal Styling - Mobile Enhanced */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 10px;
}

.modal-content {
    background: var(--primary-medium);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--accent-blue-dark);
}

.modal-header {
    background: var(--primary-dark);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--accent-blue-dark);
    flex-wrap: wrap;
    gap: 10px;
}

.modal-header h2 {
    color: var(--accent-gold);
    font-size: clamp(16px, 4vw, 20px);
    flex: 1;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.close-modal-btn:hover {
    color: var(--accent-blue);
    background: rgba(255,255,255,0.1);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

.tutorial-section {
    margin-bottom: 20px;
}

.tutorial-section h3 {
    color: var(--accent-blue);
    font-size: clamp(16px, 4vw, 18px);
    margin-bottom: 10px;
    border: none;
    padding: 0;
    text-align: left;
}

.tutorial-section p, .tutorial-section li {
    font-size: var(--mobile-font-size);
    margin-bottom: 5px;
    color: var(--text-light);
    line-height: 1.5;
}

.tutorial-section ul, .tutorial-section ol {
    padding-left: 20px;
    margin-top: 5px;
}

/* Outcome Animation Modal */
.outcome-animation {
    background: var(--primary-dark);
    padding: 40px 20px;
    border-radius: var(--border-radius);
    text-align: center;
    max-width: 400px;
    border: 2px solid var(--accent-gold);
}

.outcome-title {
    font-size: clamp(20px, 5vw, 28px);
    color: var(--accent-gold);
    margin-bottom: 20px;
    font-weight: bold;
}

.outcome-image {
    font-size: 60px;
    margin: 20px 0;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.outcome-message {
    font-size: var(--mobile-font-size);
    color: var(--text-light);
    margin-bottom: 20px;
    line-height: 1.5;
}

.continue-btn {
    background: var(--accent-blue);
    color: var(--text-light);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition);
    min-height: var(--mobile-button-height);
}

.continue-btn:hover {
    background: var(--accent-blue-light);
}

/* Choice Modal */
.choice-description {
    margin-bottom: 20px;
    line-height: 1.6;
    font-size: var(--mobile-font-size);
}

.choice-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.choice-btn {
    padding: 15px;
    background: var(--primary-dark);
    border: 1px solid var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
    font-size: var(--mobile-font-size);
    line-height: 1.4;
    min-height: var(--mobile-touch-target);
}

.choice-btn.merciful {
    border-color: var(--accent-blue-dark);
}

.choice-btn.merciful:hover {
    background: var(--primary-light);
    border-color: var(--accent-blue);
}

.choice-btn.ruthless {
    border-color: var(--accent-red);
}

.choice-btn.ruthless:hover {
    background: var(--primary-light);
    border-color: var(--accent-red);
}

/* Verification Tools */
.verification-tool {
    margin: 20px 0;
    padding: 15px;
    background: rgba(0,0,0,0.2);
    border-radius: var(--border-radius);
}

.verification-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.input-group label {
    font-size: 12px;
    color: var(--accent-blue-light);
    font-weight: bold;
}

.input-group input,
.input-group select {
    padding: 8px;
    border: 1px solid var(--primary-light);
    border-radius: 4px;
    background: var(--primary-dark);
    color: var(--text-light);
    font-size: var(--mobile-font-size);
    min-height: var(--mobile-button-height);
}

.verify-submit-btn {
    background: var(--accent-blue);
    color: var(--text-light);
    border: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: var(--mobile-font-size);
    transition: var(--transition);
    min-height: var(--mobile-button-height);
}

.verify-submit-btn:hover {
    background: var(--accent-blue-light);
}

.verification-result {
    margin-top: 15px;
    padding: 10px;
    background: rgba(0,0,0,0.3);
    border-radius: 4px;
}

.result-label {
    font-weight: bold;
    color: var(--accent-gold);
    margin-bottom: 5px;
}

.result-value {
    color: var(--text-light);
    font-size: var(--mobile-font-size);
}

.past-rounds {
    max-height: 200px;
    overflow-y: auto;
}

.round-item {
    padding: 10px;
    margin: 5px 0;
    background: rgba(0,0,0,0.2);
    border-radius: 4px;
    font-size: 12px;
    border-left: 3px solid var(--accent-blue);
}

.hidden {
    display: none;
}

/* Responsive Breakpoints */

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
    .game-container {
        padding: 8px;
    }
    
    .main-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .info-panel {
        order: -1;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .action-btn {
        flex-direction: column;
        text-align: center;
        gap: 10px;
        padding: 10px;
    }
    
    .action-icon {
        min-width: auto;
        font-size: 28px;
    }
    
    .duel-arena {
        min-height: 200px;
        padding: 15px 10px;
    }
    
    .player-samurai, .opponent-samurai {
        max-width: 80px;
    }
    
    .samurai-avatar {
        width: 60px;
        height: 60px;
    }
    
    .header-center {
        order: -1;
        width: 100%;
        flex: 1 1 100%;
    }
    
    .game-title {
        font-size: 18px;
    }
    
    .game-title span {
        font-size: 14px;
    }
    
    .game-subtitle {
        font-size: 12px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .outcome-table {
        font-size: 10px;
    }
    
    .outcome-table th,
    .outcome-table td {
        padding: 4px 2px;
    }
    
    .modal-body {
        padding: 15px;
    }
}

/* Medium Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .action-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .duel-arena {
        min-height: 220px;
        padding: 15px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .main-content {
        gap: 20px;
    }
    
    .info-panel {
        max-width: 300px;
    }
    
    .action-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .duel-arena {
        min-height: 280px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Desktop (1025px+) */
@media (min-width: 1025px) {
    .game-container {
        padding: 20px;
    }
    
    .main-content {
        gap: 25px;
    }
    
    .info-panel {
        max-width: 350px;
    }
    
    .duel-arena {
        min-height: 300px;
        padding: 25px;
    }
    
    .action-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
    
    :root {
        --mobile-padding: 20px;
        --mobile-font-size: 16px;
        --mobile-button-height: 48px;
        --mobile-touch-target: 48px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Landscape Mobile Orientation */
@media (max-height: 500px) and (orientation: landscape) {
    .game-header {
        margin-bottom: 10px;
        padding-bottom: 8px;
    }
    
    .game-title {
        font-size: 16px;
    }
    
    .game-title span {
        font-size: 14px;
    }
    
    .game-subtitle {
        display: none;
    }
    
    .main-content {
        flex-direction: row;
        gap: 15px;
    }
    
    .info-panel {
        max-width: 280px;
        order: 2;
    }
    
    .game-section {
        order: 1;
        gap: 10px;
    }
    
    .duel-arena {
        min-height: 180px;
        padding: 10px;
    }
    
    .battle-log {
        max-height: 120px;
    }
    
    .modal-content {
        max-height: 80vh;
    }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .action-icon, .samurai-avatar, .battle-effects {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .game-title {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Focus and Accessibility */
button:focus,
input:focus,
select:focus,
.action-btn:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .battle-effects {
        animation: none;
    }
    
    .outcome-image {
        animation: none;
    }
}

/* Touch Enhancements */
@media (hover: none) and (pointer: coarse) {
    .action-btn:hover {
        transform: none;
        background: var(--primary-dark);
    }
    
    .action-btn:active {
        transform: scale(0.95);
        background: var(--primary-light);
    }
    
    button:hover {
        transform: none;
    }
    
    button:active {
        transform: scale(0.95);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-light: #ffffff;
        --primary-dark: #0f0f1e;
        --primary-medium: #12182e;
    }
}

/* Custom Animations */
@keyframes floatUp {
    0% { transform: translateY(0); opacity: 0.8; }
    100% { transform: translateY(-50px); opacity: 0; }
}

@keyframes honorGlow {
    0%, 100% { text-shadow: 0 0 5px var(--accent-gold); }
    50% { text-shadow: 0 0 20px var(--accent-gold), 0 0 30px var(--accent-blue); }
}

.honor-gain {
    animation: honorGlow 1s ease-in-out;
}

.ki-particle {
    animation: floatUp 2s forwards ease-out;
}

/* Particle Effects */
.battle-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--accent-blue);
    border-radius: 50%;
    opacity: 0.7;
}

/* Honor meter specific styles */
.honor-meter-advanced {
    position: relative;
    overflow: hidden;
}

.honor-meter-advanced::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(241, 196, 83, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}