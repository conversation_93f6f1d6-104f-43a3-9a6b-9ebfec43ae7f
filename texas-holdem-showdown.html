<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Texas Hold'em Showdown - Tournament Edition</title>
    <link rel="stylesheet" href="texas-holdem-showdown.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <div class="game-container">
        <!-- Header with Tournament Info -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span class="back-text">Back to Games</span>
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">Texas Hold'em Showdown</h1>
                <p class="game-subtitle">Tournament Edition • Fast-Paced • 2x Showdown Bonus</p>
            </div>
            <div class="header-right">
                <div class="view-mode-toggle">
                    <button id="standardViewBtn" class="view-btn active">
                        <i class="fas fa-mobile-alt"></i>
                        <span class="view-text">Standard</span>
                    </button>
                    <button id="proViewBtn" class="view-btn">
                        <i class="fas fa-chart-line"></i>
                        <span class="view-text">Pro</span>
                    </button>
                </div>
                <button id="tutorialBtn" class="info-btn">
                    <i class="fas fa-circle-info"></i>
                    <span class="btn-text">Tutorial</span>
                </button>
                <button id="rulesBtn" class="verify-btn">
                    <i class="fas fa-book"></i>
                    <span class="btn-text">Rules</span>
                </button>
            </div>
        </header>

        <!-- Tournament Selection Screen (initial view) -->
        <div class="tournament-selection" id="tournamentSelection">
            <div class="tournament-header">
                <h2>Select Tournament Type</h2>
                <div class="player-stats">
                    <div class="stat-item">
                        <i class="fas fa-coins"></i>
                        <span id="playerBalance">10000</span> GA
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-medal"></i>
                        <span id="playerRank">Silver</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-trophy"></i>
                        <span id="tournamentsWon">2</span> Wins
                    </div>
                </div>
            </div>
            
            <div class="tournament-grid">
                <!-- Sit & Go Tournament -->
                <div class="tournament-card" data-tournament="sitandgo">
                    <div class="tournament-card-header">
                        <h3>Sit & Go</h3>
                        <span class="tournament-status">Starting Soon</span>
                    </div>
                    <div class="tournament-details">
                        <div class="detail-item">
                            <span class="detail-label">Players</span>
                            <span class="detail-value">6</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Buy-in</span>
                            <span class="detail-value">200 GA</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Blinds</span>
                            <span class="detail-value">15 min levels</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Prize Pool</span>
                            <span class="detail-value">1,200 GA</span>
                        </div>
                    </div>
                    <div class="tournament-footer">
                        <div class="players-registered">
                            <div class="player-avatars">
                                <div class="player-avatar filled"></div>
                                <div class="player-avatar filled"></div>
                                <div class="player-avatar filled"></div>
                                <div class="player-avatar"></div>
                                <div class="player-avatar"></div>
                                <div class="player-avatar"></div>
                            </div>
                            <span>3/6 Players</span>
                        </div>
                        <button class="register-btn">Register</button>
                    </div>
                </div>
                
                <!-- Daily Showdown Tournament -->
                <div class="tournament-card featured" data-tournament="daily">
                    <div class="featured-badge">Featured</div>
                    <div class="tournament-card-header">
                        <h3>Daily Showdown</h3>
                        <span class="tournament-status">Starts in 45 min</span>
                    </div>
                    <div class="tournament-details">
                        <div class="detail-item">
                            <span class="detail-label">Players</span>
                            <span class="detail-value">100</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Buy-in</span>
                            <span class="detail-value">500 GA</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Blinds</span>
                            <span class="detail-value">15 min levels</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Prize Pool</span>
                            <span class="detail-value">50,000 GA</span>
                        </div>
                    </div>
                    <div class="tournament-footer">
                        <div class="players-registered">
                            <div class="progress-bar">
                                <div class="progress" style="width: 65%;"></div>
                            </div>
                            <span>65/100 Players</span>
                        </div>
                        <button class="register-btn">Register</button>
                    </div>
                </div>
                
                <!-- Weekly Championship Tournament -->
                <div class="tournament-card premium" data-tournament="weekly">
                    <div class="premium-badge">Premium</div>
                    <div class="tournament-card-header">
                        <h3>Weekly Championship</h3>
                        <span class="tournament-status">Sunday, 8:00 PM</span>
                    </div>
                    <div class="tournament-details">
                        <div class="detail-item">
                            <span class="detail-label">Players</span>
                            <span class="detail-value">1,000</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Buy-in</span>
                            <span class="detail-value">1,000 GA</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Blinds</span>
                            <span class="detail-value">20 min levels</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Prize Pool</span>
                            <span class="detail-value">1,000,000 GA</span>
                        </div>
                    </div>
                    <div class="tournament-footer">
                        <div class="players-registered">
                            <div class="progress-bar">
                                <div class="progress" style="width: 32%;"></div>
                            </div>
                            <span>320/1000 Players</span>
                        </div>
                        <button class="register-btn">Register</button>
                    </div>
                </div>
            </div>
            
            <div class="achievements-section">
                <h3>Your Achievements</h3>
                <div class="badges-container">
                    <div class="badge-item earned" data-badge="first-tournament">
                        <div class="badge-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="badge-info">
                            <div class="badge-name">First Timer</div>
                            <div class="badge-description">Completed your first tournament</div>
                        </div>
                    </div>
                    <div class="badge-item earned" data-badge="final-table">
                        <div class="badge-icon">
                            <i class="fas fa-table"></i>
                        </div>
                        <div class="badge-info">
                            <div class="badge-name">Final Table</div>
                            <div class="badge-description">Reached the final table</div>
                        </div>
                    </div>
                    <div class="badge-item" data-badge="showdown-specialist">
                        <div class="badge-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="badge-info">
                            <div class="badge-name">Showdown Specialist</div>
                            <div class="badge-description">Win 10 hands with showdown bonus</div>
                        </div>
                    </div>
                    <div class="badge-item" data-badge="tournament-champion">
                        <div class="badge-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="badge-info">
                            <div class="badge-name">Tournament Champion</div>
                            <div class="badge-description">Win a Daily or Weekly tournament</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tournament Lobby (hidden initially) -->
        <div class="tournament-lobby hidden" id="tournamentLobby">
            <div class="tournament-info-bar">
                <div class="tournament-name" id="lobbyTournamentName">Daily Showdown</div>
                <div class="tournament-time">
                    <i class="fas fa-clock"></i>
                    <span class="time-left" id="tournamentTimeLeft">Starting in 2:45</span>
                </div>
                <div class="player-count">
                    <i class="fas fa-users"></i>
                    <span id="lobbyPlayerCount">65/100</span>
                </div>
                <div class="blind-level">
                    <i class="fas fa-layer-group"></i>
                    <span id="lobbyBlindLevel">Starting: 10/20</span>
                </div>
            </div>
            
            <div class="tournament-tables">
                <div class="tables-header">
                    <h3>Tournament Tables</h3>
                    <div class="tables-actions">
                        <button class="tables-filter-btn">
                            <i class="fas fa-filter"></i>
                            <span>Filter</span>
                        </button>
                        <button class="tables-refresh-btn">
                            <i class="fas fa-sync-alt"></i>
                            <span>Refresh</span>
                        </button>
                    </div>
                </div>
                
                <div class="tables-grid">
                    <div class="table-card" data-table="1">
                        <div class="table-header">
                            <h4>Table #1</h4>
                            <span class="table-status">Active</span>
                        </div>
                        <div class="table-preview">
                            <div class="table-image"></div>
                            <div class="table-players">
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                            </div>
                        </div>
                        <div class="table-details">
                            <div class="table-blinds">Blinds: 10/20</div>
                            <div class="table-seats">Seats: 6/6</div>
                        </div>
                        <button class="spectate-btn">Spectate</button>
                    </div>
                    
                    <div class="table-card" data-table="2">
                        <div class="table-header">
                            <h4>Table #2</h4>
                            <span class="table-status">Active</span>
                        </div>
                        <div class="table-preview">
                            <div class="table-image"></div>
                            <div class="table-players">
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat"></div>
                            </div>
                        </div>
                        <div class="table-details">
                            <div class="table-blinds">Blinds: 10/20</div>
                            <div class="table-seats">Seats: 5/6</div>
                        </div>
                        <button class="spectate-btn">Spectate</button>
                    </div>
                    
                    <div class="table-card your-table" data-table="3">
                        <div class="table-header">
                            <h4>Table #3</h4>
                            <span class="table-status your-table-label">Your Table</span>
                        </div>
                        <div class="table-preview">
                            <div class="table-image"></div>
                            <div class="table-players">
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled you"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                                <div class="player-seat filled"></div>
                            </div>
                        </div>
                        <div class="table-details">
                            <div class="table-blinds">Blinds: 10/20</div>
                            <div class="table-seats">Seats: 6/6</div>
                        </div>
                        <button class="join-table-btn">Join Table</button>
                    </div>
                </div>
            </div>
            
            <div class="tournament-standings">
                <h3>Tournament Standings</h3>
                <div class="standings-table">
                    <div class="standings-header">
                        <div class="rank-col">Rank</div>
                        <div class="player-col">Player</div>
                        <div class="chips-col">Chips</div>
                        <div class="table-col">Table</div>
                    </div>
                    <div class="standings-body">
                        <div class="standings-row">
                            <div class="rank-col">1</div>
                            <div class="player-col">PokerKing92</div>
                            <div class="chips-col">12,500</div>
                            <div class="table-col">Table #5</div>
                        </div>
                        <div class="standings-row">
                            <div class="rank-col">2</div>
                            <div class="player-col">CardShark</div>
                            <div class="chips-col">10,800</div>
                            <div class="table-col">Table #8</div>
                        </div>
                        <div class="standings-row you">
                            <div class="rank-col">15</div>
                            <div class="player-col">You</div>
                            <div class="chips-col">5,200</div>
                            <div class="table-col">Table #3</div>
                        </div>
                        <div class="standings-row">
                            <div class="rank-col">16</div>
                            <div class="player-col">BluffMaster</div>
                            <div class="chips-col">5,100</div>
                            <div class="table-col">Table #1</div>
                        </div>
                        <div class="standings-row">
                            <div class="rank-col">17</div>
                            <div class="player-col">AcesHigh</div>
                            <div class="chips-col">4,950</div>
                            <div class="table-col">Table #4</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tournament-actions">
                <button class="leave-tournament-btn">Leave Tournament</button>
                <button class="tournament-rules-btn">Rules</button>
            </div>
        </div>

        <!-- Main Game Area (hidden initially) -->
        <div class="main-content hidden" id="gameArea">
            <!-- Mobile Status Bar -->
            <div class="mobile-status-bar">
                <div class="status-item">
                    <i class="fas fa-coins"></i>
                    <span id="mobileBalance">5200</span> GA
                </div>
                <div class="status-item">
                    <i class="fas fa-users"></i>
                    <span id="mobilePlayersCount">15</span>/100
                </div>
                <div class="status-item">
                    <i class="fas fa-trophy"></i>
                    <span id="mobilePotSize">0</span> GA
                </div>
                <div class="status-item">
                    <i class="fas fa-percentage"></i>
                    <span id="mobileWinChance">0%</span>
                </div>
            </div>

            <!-- Tournament Sidebar (shown on desktop) -->
            <div class="tournament-sidebar">
                <div class="tournament-stats">
                    <h3>Tournament Info</h3>
                    <div class="tournament-stat-item">
                        <div class="stat-label">Level</div>
                        <div class="stat-value" id="tournamentLevel">1</div>
                    </div>
                    <div class="tournament-stat-item">
                        <div class="stat-label">Blinds</div>
                        <div class="stat-value" id="tournamentBlinds">10/20</div>
                    </div>
                    <div class="tournament-stat-item">
                        <div class="stat-label">Next Level</div>
                        <div class="stat-value" id="nextLevelTime">12:45</div>
                    </div>
                    <div class="tournament-stat-item">
                        <div class="stat-label">Players Left</div>
                        <div class="stat-value" id="playersLeft">65/100</div>
                    </div>
                    <div class="tournament-stat-item">
                        <div class="stat-label">Avg Stack</div>
                        <div class="stat-value" id="avgStack">5,325</div>
                    </div>
                    <div class="tournament-stat-item">
                        <div class="stat-label">Your Stack</div>
                        <div class="stat-value" id="yourStack">5,200</div>
                    </div>
                </div>
                
                <div class="payout-structure">
                    <h3>Payouts</h3>
                    <div class="payout-item">
                        <div class="position">1st</div>
                        <div class="amount">15,000 GA</div>
                    </div>
                    <div class="payout-item">
                        <div class="position">2nd</div>
                        <div class="amount">10,000 GA</div>
                    </div>
                    <div class="payout-item">
                        <div class="position">3rd</div>
                        <div class="amount">7,500 GA</div>
                    </div>
                    <div class="payout-item">
                        <div class="position">4-6th</div>
                        <div class="amount">5,000 GA</div>
                    </div>
                    <div class="payout-item">
                        <div class="position">7-9th</div>
                        <div class="amount">2,500 GA</div>
                    </div>
                    <div class="payout-item">
                        <div class="position">10-15th</div>
                        <div class="amount">1,000 GA</div>
                    </div>
                </div>
                
                <div class="player-notes">
                    <h3>Player Notes</h3>
                    <div class="notes-container" id="playerNotes">
                        <div class="note-item">
                            <div class="note-player">CardShark</div>
                            <div class="note-text">Aggressive pre-flop, folds to re-raises</div>
                        </div>
                        <div class="note-item">
                            <div class="note-player">BluffMaster</div>
                            <div class="note-text">Bluffs frequently on river</div>
                        </div>
                    </div>
                    <button class="add-note-btn">+ Add Note</button>
                </div>
            </div>

            <!-- Game Section -->
            <div class="game-section">
                <!-- Tournament Table Info Bar -->
                <div class="table-info-bar">
                    <div class="table-name">Table #3</div>
                    <div class="blind-info">
                        <span class="small-blind">SB: 10</span>
                        <span class="big-blind">BB: 20</span>
                    </div>
                    <div class="hand-number">Hand #8</div>
                </div>
                
                <!-- Poker Table -->
                <div class="poker-table">
                    <!-- Community Cards Area -->
                    <div class="community-cards">
                        <div class="community-cards-label">Community Cards</div>
                        <div class="cards-container">
                            <div class="card-slot community-card" id="flop1">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="flop2">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="flop3">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="turn">
                                <div class="card-back"></div>
                            </div>
                            <div class="card-slot community-card" id="river">
                                <div class="card-back"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Pot Display -->
                    <div class="pot-display">
                        <div class="pot-label">Pot</div>
                        <div class="pot-amount" id="potAmount">0 GA</div>
                        <div class="pot-chips">
                            <div class="chip chip-red"></div>
                            <div class="chip chip-blue"></div>
                            <div class="chip chip-green"></div>
                        </div>
                    </div>

                    <!-- Players Positions -->
                    <div class="players-grid">
                        <!-- Player 1 (Bottom - Human Player) -->
                        <div class="player-seat player-human" id="player0">
                            <div class="player-info">
                                <div class="player-name">You</div>
                                <div class="player-chips">5,200 GA</div>
                                <div class="player-status">Waiting</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card" id="playerCard1">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card" id="playerCard2">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                            <div class="player-actions" id="playerActions">
                                <button class="action-btn fold-btn" id="foldBtn">
                                    <i class="fas fa-times"></i>
                                    Fold
                                </button>
                                <button class="action-btn call-btn" id="callBtn">
                                    <i class="fas fa-handshake"></i>
                                    Call <span id="callAmount">0</span>
                                </button>
                                <button class="action-btn raise-btn" id="raiseBtn">
                                    <i class="fas fa-arrow-up"></i>
                                    Raise
                                </button>
                                <button class="action-btn check-btn" id="checkBtn">
                                    <i class="fas fa-check"></i>
                                    Check
                                </button>
                                <button class="action-btn showdown-btn" id="showdownBtn">
                                    <i class="fas fa-eye"></i>
                                    Showdown
                                </button>
                            </div>
                        </div>

                        <!-- AI Players (Positions 2-6) -->
                        <div class="player-seat player-ai" id="player1">
                            <div class="player-info">
                                <div class="player-name">CardShark</div>
                                <div class="player-chips">10,800 GA</div>
                                <div class="player-status">Thinking...</div>
                                <div class="ai-difficulty">Pro</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player2">
                            <div class="player-info">
                                <div class="player-name">PokerKing92</div>
                                <div class="player-chips">12,500 GA</div>
                                <div class="player-status">Folded</div>
                                <div class="ai-difficulty">Shark</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player3">
                            <div class="player-info">
                                <div class="player-name">BluffMaster</div>
                                <div class="player-chips">5,100 GA</div>
                                <div class="player-status">All-in</div>
                                <div class="ai-difficulty">Pro</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player4">
                            <div class="player-info">
                                <div class="player-name">AcesHigh</div>
                                <div class="player-chips">4,950 GA</div>
                                <div class="player-status">Active</div>
                                <div class="ai-difficulty">Novice</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>

                        <div class="player-seat player-ai" id="player5">
                            <div class="player-info">
                                <div class="player-name">RiverRat</div>
                                <div class="player-chips">3,750 GA</div>
                                <div class="player-status">Waiting</div>
                                <div class="ai-difficulty">Novice</div>
                            </div>
                            <div class="player-cards">
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                                <div class="card-slot player-card">
                                    <div class="card-back"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dealer Button -->
                    <div class="dealer-button" id="dealerButton">
                        <i class="fas fa-crown"></i>
                        <span>D</span>
                    </div>
                </div>

                <!-- Game Controls -->
                <div class="game-controls">
                    <!-- Betting Controls -->
                    <div class="betting-controls">
                        <div class="bet-amount-container">
                            <label for="betAmount">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <input type="range" id="betSlider" min="40" max="5200" value="100" class="bet-slider">
                                <input type="number" id="betAmount" min="40" max="5200" value="100" class="bet-input">
                            </div>
                            <div class="quick-bet-buttons">
                                <button class="quick-bet" data-amount="40">40</button>
                                <button class="quick-bet" data-amount="100">100</button>
                                <button class="quick-bet" data-amount="250">250</button>
                                <button class="quick-bet" data-amount="500">500</button>
                                <button class="quick-bet" data-amount="1000">1000</button>
                                <button class="quick-bet" data-amount="all-in">All-in</button>
                            </div>
                        </div>
                    </div>

                    <!-- Game Info Panel -->
                    <div class="game-info-panel">
                        <div class="info-section">
                            <h3>Current Hand</h3>
                            <div class="hand-strength" id="handStrength">
                                <div class="strength-label">Hand: High Card</div>
                                <div class="strength-bars">
                                    <div class="strength-bar"></div>
                                    <div class="strength-bar"></div>
                                    <div class="strength-bar active"></div>
                                    <div class="strength-bar"></div>
                                    <div class="strength-bar"></div>
                                </div>
                            </div>
                            <div class="win-probability" id="winProbability">
                                <span class="prob-label">Win Chance:</span>
                                <span class="prob-value">32%</span>
                            </div>
                        </div>

                        <div class="info-section blind-info">
                            <h3>Blinds</h3>
                            <div class="blind-amounts">
                                <div class="blind-item">
                                    <span class="blind-label">Small:</span>
                                    <span class="blind-value">10 GA</span>
                                </div>
                                <div class="blind-item">
                                    <span class="blind-label">Big:</span>
                                    <span class="blind-value">20 GA</span>
                                </div>
                            </div>
                        </div>

                        <div class="info-section game-round">
                            <h3>Game Status</h3>
                            <div class="round-info">
                                <div class="round-phase" id="roundPhase">Pre-Flop</div>
                                <div class="players-active" id="playersActive">6 players active</div>
                                <div class="next-action" id="nextAction">Your turn</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pro Analytics (Hidden by default) -->
                <div class="pro-analytics">
                    <div class="analytics-section">
                        <h3>Tournament Stats</h3>
                        <div class="analytics-grid">
                            <div class="analytics-item">
                                <div class="analytics-label">Position</div>
                                <div class="analytics-value" id="tournamentPosition">15/100</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">Time Left</div>
                                <div class="analytics-value" id="levelTimeLeft">12:45</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">M-Zone</div>
                                <div class="analytics-value" id="mZoneValue">13.0</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">ICM Value</div>
                                <div class="analytics-value" id="icmValue">$4,250</div>
                            </div>
                        </div>
                    </div>

                    <div class="opponent-analysis">
                        <h3>Table Dynamics</h3>
                        <div class="opponent-stats">
                            <div class="opponent-item">
                                <div class="opponent-name">CardShark (Pro)</div>
                                <div class="opponent-tendency">Loose-Aggressive</div>
                                <div class="opponent-notes">Aggressive pre-flop, likely to re-raise</div>
                            </div>
                            <div class="opponent-item">
                                <div class="opponent-name">BluffMaster (Pro)</div>
                                <div class="opponent-tendency">Bluffer</div>
                                <div class="opponent-notes">Frequently bluffs on river, weak on turn</div>
                            </div>
                            <div class="opponent-item">
                                <div class="opponent-name">AcesHigh (Novice)</div>
                                <div class="opponent-tendency">Tight-Passive</div>
                                <div class="opponent-notes">Only plays premium hands, rarely bluffs</div>
                            </div>
                        </div>
                    </div>

                    <div class="hand-history">
                        <h3>Recent Hands</h3>
                        <div class="history-list" id="handHistoryList">
                            <div class="history-item">
                                <div class="history-hand">A♠ K♦</div>
                                <div class="history-result">Won 750 GA</div>
                                <div class="history-action">Pair of Aces</div>
                            </div>
                            <div class="history-item">
                                <div class="history-hand">J♣ 10♥</div>
                                <div class="history-result">Folded</div>
                                <div class="history-action">Pre-flop</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Game Actions Panel -->
                <div class="actions-panel">
                    <div class="game-actions">
                        <button class="action-btn fast-fold-btn" id="fastFoldBtn">
                            <i class="fas fa-forward"></i>
                            Fast Fold
                        </button>
                        <button class="action-btn auto-check-btn" id="autoCheckBtn">
                            <i class="fas fa-magic"></i>
                            Auto-Check
                        </button>
                        <button class="action-btn sit-out-btn" id="sitOutBtn">
                            <i class="fas fa-pause"></i>
                            Sit Out
                        </button>
                        <button class="action-btn leave-btn" id="leaveBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Leave Table
                        </button>
                    </div>

                    <div class="table-options">
                        <button class="table-option-btn" id="chatBtn">
                            <i class="fas fa-comments"></i>
                        </button>
                        <button class="table-option-btn" id="noteBtn">
                            <i class="fas fa-sticky-note"></i>
                        </button>
                        <button class="table-option-btn" id="statsBtn">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                        <button class="table-option-btn" id="settingsBtn">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Showdown Bonus Animation (hidden until triggered) -->
        <div class="showdown-overlay hidden" id="showdownOverlay">
            <div class="showdown-container">
                <div class="showdown-header">
                    <h2>Showdown Bonus!</h2>
                    <div class="bonus-multiplier">2x</div>
                </div>
                <div class="showdown-cards">
                    <div class="your-hand">
                        <h3>Your Hand</h3>
                        <div class="showdown-hand-cards">
                            <div class="card" id="showdownCard1"></div>
                            <div class="card" id="showdownCard2"></div>
                        </div>
                        <div class="hand-name" id="showdownHandName">Two Pair</div>
                    </div>
                    <div class="community-hand">
                        <h3>Community Cards</h3>
                        <div class="showdown-community-cards">
                            <div class="card" id="showdownCommunity1"></div>
                            <div class="card" id="showdownCommunity2"></div>
                            <div class="card" id="showdownCommunity3"></div>
                            <div class="card" id="showdownCommunity4"></div>
                            <div class="card" id="showdownCommunity5"></div>
                        </div>
                    </div>
                </div>
                <div class="showdown-result">
                    <div class="result-text" id="showdownResultText">You win with Two Pair!</div>
                    <div class="bonus-amount" id="bonusAmount">+1,240 GA</div>
                </div>
                <button class="continue-btn" id="showdownContinueBtn">Continue</button>
            </div>
        </div>

        <!-- Tournament Elimination Animation (hidden until triggered) -->
        <div class="elimination-overlay hidden" id="eliminationOverlay">
            <div class="elimination-container">
                <div class="elimination-header">
                    <h2>Tournament Result</h2>
                </div>
                <div class="elimination-info">
                    <div class="position-indicator">
                        <div class="position-number" id="eliminationPosition">15th</div>
                        <div class="position-text">Place</div>
                    </div>
                    <div class="elimination-details">
                        <div class="detail-item">
                            <span class="detail-label">Players</span>
                            <span class="detail-value">100</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Prize</span>
                            <span class="detail-value" id="eliminationPrize">1,000 GA</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Duration</span>
                            <span class="detail-value" id="tournamentDuration">45 min</span>
                        </div>
                    </div>
                </div>
                <div class="last-hand">
                    <h3>Final Hand</h3>
                    <div class="last-hand-cards">
                        <div class="your-last-cards">
                            <span>Your Hand:</span>
                            <div class="card" id="lastHandCard1"></div>
                            <div class="card" id="lastHandCard2"></div>
                        </div>
                        <div class="opponent-last-cards">
                            <span>Opponent's Hand:</span>
                            <div class="card" id="opponentLastCard1"></div>
                            <div class="card" id="opponentLastCard2"></div>
                        </div>
                    </div>
                    <div class="last-hand-board">
                        <span>Board:</span>
                        <div class="card" id="lastHandBoard1"></div>
                        <div class="card" id="lastHandBoard2"></div>
                        <div class="card" id="lastHandBoard3"></div>
                        <div class="card" id="lastHandBoard4"></div>
                        <div class="card" id="lastHandBoard5"></div>
                    </div>
                </div>
                <div class="elimination-achievements">
                    <h3>Achievements Earned</h3>
                    <div class="achievement-list" id="achievementsList">
                        <div class="achievement-item">
                            <div class="achievement-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="achievement-info">
                                <div class="achievement-name">In The Money</div>
                                <div class="achievement-description">Finished in a paid position</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="elimination-buttons">
                    <button class="restart-btn" id="playAgainBtn">Play Again</button>
                    <button class="lobby-btn" id="returnToLobbyBtn">Return to Lobby</button>
                </div>
            </div>
        </div>

        <!-- Tutorial Modal -->
        <div class="modal-overlay hidden" id="tutorialModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Texas Hold'em Showdown Tutorial</h2>
                    <button class="close-modal-btn" id="closeTutorialBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="tutorial-section">
                        <h3>Tournament Play</h3>
                        <p>Texas Hold'em Showdown is a tournament-style poker game where players compete until one player has all the chips. Blinds increase every 15 minutes to force action.</p>
                        <ul>
                            <li><strong>Sit & Go:</strong> 6 players, starts when full</li>
                            <li><strong>Daily Showdown:</strong> 100 players, scheduled daily</li>
                            <li><strong>Weekly Championship:</strong> 1,000 players, bigger prizes</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Special Features</h3>
                        <ul>
                            <li><strong>Showdown Bonus:</strong> Reveal your cards before showdown for 2x pot bonus if you win</li>
                            <li><strong>Fast-Fold:</strong> Instantly move to a new hand when you fold</li>
                            <li><strong>All-in Protection:</strong> 3-hand grace period when short-stacked</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Basic Poker Rules</h3>
                        <ol>
                            <li>Each player receives 2 hole cards</li>
                            <li>5 community cards are dealt (Flop, Turn, River)</li>
                            <li>Best 5-card poker hand wins</li>
                            <li>Players must call, raise, or fold when betting</li>
                        </ol>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Tournament Strategy</h3>
                        <ul>
                            <li><strong>Early Game:</strong> Play tight, preserve chips</li>
                            <li><strong>Middle Game:</strong> Adjust to blind increases</li>
                            <li><strong>Late Game:</strong> Aggressive play as blinds get high</li>
                            <li><strong>Final Table:</strong> Consider ICM implications</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Achievement System</h3>
                        <p>Earn badges and achievements for various poker accomplishments:</p>
                        <ul>
                            <li>First Timer: Complete your first tournament</li>
                            <li>Final Table: Reach the final table</li>
                            <li>Showdown Specialist: Win 10 hands with showdown bonus</li>
                            <li>Tournament Champion: Win a Daily or Weekly tournament</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div class="modal-overlay hidden" id="rulesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Tournament Rules</h2>
                    <button class="close-modal-btn" id="closeRulesBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="rules-section">
                        <h3>General Rules</h3>
                        <ul>
                            <li>Texas Hold'em rules apply (best 5-card hand wins)</li>
                            <li>All tournaments use a freezeout format (no re-buys)</li>
                            <li>Blinds increase every 15 minutes (20 minutes for Weekly)</li>
                            <li>Players must act within 30 seconds or hands are auto-folded</li>
                            <li>Disconnected players are automatically put in sit-out mode</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Blind Structure</h3>
                        <table class="rules-table">
                            <tr>
                                <th>Level</th>
                                <th>Small Blind</th>
                                <th>Big Blind</th>
                                <th>Duration</th>
                            </tr>
                            <tr>
                                <td>1</td>
                                <td>10</td>
                                <td>20</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>15</td>
                                <td>30</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>25</td>
                                <td>50</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>50</td>
                                <td>100</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>75</td>
                                <td>150</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>100</td>
                                <td>200</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>150</td>
                                <td>300</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>200</td>
                                <td>400</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>300</td>
                                <td>600</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>400</td>
                                <td>800</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td>500</td>
                                <td>1000</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>750</td>
                                <td>1500</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>13</td>
                                <td>1000</td>
                                <td>2000</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>14</td>
                                <td>1500</td>
                                <td>3000</td>
                                <td>15 min</td>
                            </tr>
                            <tr>
                                <td>15</td>
                                <td>2000</td>
                                <td>4000</td>
                                <td>15 min</td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Showdown Bonus Rules</h3>
                        <ul>
                            <li>Player must reveal cards before the showdown (after the river betting round)</li>
                            <li>Player must win the hand to receive the bonus</li>
                            <li>The bonus is 2x the pot amount (excluding the player's own contributions)</li>
                            <li>If multiple players use the Showdown feature, only the winner receives the bonus</li>
                            <li>Showdown option is only available on the river betting round</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>All-in Protection</h3>
                        <ul>
                            <li>When a player's stack falls below 5 big blinds, they receive 3 hands of All-in Protection</li>
                            <li>During protection, they cannot be eliminated unless they voluntarily enter a pot</li>
                            <li>Protection is lost if the player's stack goes above 5 big blinds</li>
                            <li>This feature prevents players from being blinded out immediately when short-stacked</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Fair Play Policy</h3>
                        <ul>
                            <li>Collusion between players is strictly prohibited</li>
                            <li>Table assignments are random to prevent team play</li>
                            <li>All hands are monitored for unusual patterns</li>
                            <li>RNG certification ensures fair card distribution</li>
                            <li>Players found violating fair play rules will be disqualified</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="texas-holdem-showdown.js"></script>
</body>
</html>