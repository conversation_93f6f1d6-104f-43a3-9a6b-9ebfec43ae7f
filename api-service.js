/**
 * API Service for Sports Betting System
 * 
 * This service handles all API interactions for sports data and odds.
 * It provides a unified interface to interact with the backend API.
 */
class SportsBettingAPI {
    constructor(options = {}) {
        // Configuration options
        this.config = {
            baseUrl: options.baseUrl || '/api',
            cacheTime: options.cacheTime || 15 * 60 * 1000, // 15 minutes in milliseconds
            requestInterval: options.requestInterval || 500, // 500ms between requests
            ...options
        };
        
        // Cache storage
        this.cache = {
            sports: {
                data: null,
                timestamp: 0
            },
            odds: {
                data: {},
                timestamp: {}
            },
            fixtures: {
                data: {},
                timestamp: {}
            }
        };
        
        // Last request timestamp to manage rate limiting
        this.lastRequestTime = 0;
        
        // Auth token
        this.authToken = localStorage.getItem('auth_token');
        
        // API endpoints
        this.endpoints = {
            sports: '/sports',
            featured: '/sports/featured',
            liveMatches: '/sports/live',
            sportDetails: '/sports/{sport}',
            matches: '/sports/{sport}/matches',
            bets: '/sports/bets',
            bet: '/sports/bets/{betId}',
            cancelBet: '/sports/bets/{betId}/cancel',
            login: '/login',
            register: '/register',
            user: '/user',
            wallet: '/wallet',
            walletTransactions: '/wallet/transactions',
            walletDeposit: '/wallet/deposit',
            walletWithdraw: '/wallet/withdraw'
        };
    }
    
    /**
     * Make an API request with rate limiting and caching
     */
    async makeRequest(endpoint, options = {}) {
        // Construct full URL
        let url = this.config.baseUrl + endpoint;
        
        // Replace URL parameters
        if (options.params) {
            for (const [key, value] of Object.entries(options.params)) {
                url = url.replace(`{${key}}`, value);
            }
        }
        
        // Add query parameters
        if (options.query) {
            const queryParams = new URLSearchParams(options.query);
            url += (url.includes('?') ? '&' : '?') + queryParams.toString();
        }
        
        // Rate limiting
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < this.config.requestInterval) {
            await new Promise(resolve => 
                setTimeout(resolve, this.config.requestInterval - timeSinceLastRequest)
            );
        }
        
        // Make the request
        try {
            // Default request options
            const fetchOptions = {
                method: options.method || 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    ...options.headers
                }
            };
            
            // Add auth token if available
            if (this.authToken && !options.skipAuth) {
                fetchOptions.headers['Authorization'] = `Bearer ${this.authToken}`;
            }
            
            // Add body for POST, PUT, PATCH requests
            if (['POST', 'PUT', 'PATCH'].includes(fetchOptions.method) && options.data) {
                fetchOptions.body = JSON.stringify(options.data);
            }
            
            // Update last request time
            this.lastRequestTime = Date.now();
            
            // Fetch data
            const response = await fetch(url, fetchOptions);
            
            // Handle authentication errors
            if (response.status === 401 && !options.skipAuth) {
                this.handleAuthError();
                throw new Error('Authentication failed');
            }
            
            // Parse response
            const data = await response.json();
            
            // Check for API errors
            if (!response.ok) {
                throw new Error(data.message || `API request failed with status ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }
    
    /**
     * Handle authentication errors
     */
    handleAuthError() {
        // Clear auth token
        this.authToken = null;
        localStorage.removeItem('auth_token');
        
        // Redirect to login page or show login modal
        // This would be implemented based on your app's design
        console.warn('Authentication required. Please log in.');
        
        // Trigger auth event that app can listen for
        const authEvent = new CustomEvent('auth:required', {
            detail: { message: 'Please login to continue' }
        });
        window.dispatchEvent(authEvent);
    }
    
    /**
     * Set auth token
     */
    setAuthToken(token) {
        this.authToken = token;
        if (token) {
            localStorage.setItem('auth_token', token);
        } else {
            localStorage.removeItem('auth_token');
        }
    }
    
    /**
     * Get all available sports
     */
    async getSports() {
        // Check cache first
        if (
            this.cache.sports.data && 
            Date.now() - this.cache.sports.timestamp < this.config.cacheTime
        ) {
            return this.cache.sports.data;
        }
        
        // If no cached data or cache expired, fetch new data
        try {
            const response = await this.makeRequest(this.endpoints.sports);
            
            if (response.status === 'success' && response.sports) {
                // Update cache
                this.cache.sports.data = response.sports;
                this.cache.sports.timestamp = Date.now();
                
                return response.sports;
            }
            
            throw new Error('Failed to parse sports data');
        } catch (error) {
            console.error('Failed to fetch sports:', error);
            
            // Return cached data if available, even if expired
            if (this.cache.sports.data) {
                return this.cache.sports.data;
            }
            
            throw error;
        }
    }
    
    /**
     * Get matches for a specific sport
     */
    async getMatches(sportId, options = {}) {
        const endpoint = this.endpoints.matches;
        const cacheKey = `${sportId}-${JSON.stringify(options)}`;
        
        // Check cache first
        if (
            this.cache.fixtures.data[cacheKey] && 
            Date.now() - this.cache.fixtures.timestamp[cacheKey] < this.config.cacheTime
        ) {
            return this.cache.fixtures.data[cacheKey];
        }
        
        // If no cached data or cache expired, fetch new data
        try {
            const response = await this.makeRequest(endpoint, {
                params: {
                    sport: sportId
                },
                query: options
            });
            
            if (response.status === 'success' && response.matches) {
                // Process match data for consistency with frontend expected format
                const formattedMatches = this.formatMatchData(response.matches.data);
                
                // Update cache
                this.cache.fixtures.data[cacheKey] = formattedMatches;
                this.cache.fixtures.timestamp[cacheKey] = Date.now();
                
                return formattedMatches;
            }
            
            throw new Error('Failed to parse match data');
        } catch (error) {
            console.error(`Failed to fetch matches for sport ${sportId}:`, error);
            
            // Return cached data if available, even if expired
            if (this.cache.fixtures.data[cacheKey]) {
                return this.cache.fixtures.data[cacheKey];
            }
            
            throw error;
        }
    }
    
    /**
     * Format match data to ensure consistent structure
     */
    formatMatchData(matches) {
        return matches.map(match => ({
            id: match.id,
            sportKey: match.sport.slug,
            sportName: match.sport.name,
            commenceTime: new Date(match.commence_time),
            homeTeam: match.home_team,
            awayTeam: match.away_team,
            status: match.status,
            odds: {
                home: match.home_odds,
                away: match.away_odds,
                draw: match.draw_odds
            }
        }));
    }
    
    /**
     * Get live matches across all sports
     */
    async getLiveMatches() {
        try {
            const response = await this.makeRequest(this.endpoints.liveMatches);
            
            if (response.status === 'success' && response.live_matches) {
                return this.formatMatchData(response.live_matches);
            }
            
            throw new Error('Failed to parse live match data');
        } catch (error) {
            console.error('Failed to fetch live matches:', error);
            throw error;
        }
    }
    
    /**
     * Get featured matches
     */
    async getFeaturedMatches(limit = 10) {
        try {
            const response = await this.makeRequest(this.endpoints.featured, {
                query: { limit }
            });
            
            if (response.status === 'success' && response.featured_matches) {
                return this.formatMatchData(response.featured_matches);
            }
            
            throw new Error('Failed to parse featured match data');
        } catch (error) {
            console.error('Failed to fetch featured matches:', error);
            throw error;
        }
    }
    
    /**
     * Get user's betting history
     */
    async getBetHistory(options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.bets, {
                query: options
            });
            
            if (response.status === 'success' && response.bets) {
                return response.bets;
            }
            
            throw new Error('Failed to parse bet history data');
        } catch (error) {
            console.error('Failed to fetch bet history:', error);
            throw error;
        }
    }
    
    /**
     * Place a bet
     */
    async placeBet(betData) {
        try {
            const response = await this.makeRequest(this.endpoints.bets, {
                method: 'POST',
                data: betData
            });
            
            if (response.status === 'success' && response.bet) {
                return response.bet;
            }
            
            throw new Error(response.message || 'Failed to place bet');
        } catch (error) {
            console.error('Failed to place bet:', error);
            throw error;
        }
    }
    
    /**
     * Cancel a bet
     */
    async cancelBet(betId) {
        try {
            const response = await this.makeRequest(this.endpoints.cancelBet, {
                method: 'POST',
                params: {
                    betId
                }
            });
            
            if (response.status === 'success') {
                return response.bet;
            }
            
            throw new Error(response.message || 'Failed to cancel bet');
        } catch (error) {
            console.error(`Failed to cancel bet ${betId}:`, error);
            throw error;
        }
    }
    
    /**
     * Get user wallet balance
     */
    async getWalletBalance() {
        try {
            const response = await this.makeRequest(this.endpoints.wallet);
            
            if (response.status === 'success') {
                return response.balance;
            }
            
            throw new Error('Failed to get wallet balance');
        } catch (error) {
            console.error('Failed to fetch wallet balance:', error);
            throw error;
        }
    }
    
    /**
     * Get user wallet transactions
     */
    async getWalletTransactions(options = {}) {
        try {
            const response = await this.makeRequest(this.endpoints.walletTransactions, {
                query: options
            });
            
            if (response.status === 'success') {
                return response.transactions;
            }
            
            throw new Error('Failed to get wallet transactions');
        } catch (error) {
            console.error('Failed to fetch wallet transactions:', error);
            throw error;
        }
    }
    
    /**
     * User login
     */
    async login(credentials) {
        try {
            const response = await this.makeRequest(this.endpoints.login, {
                method: 'POST',
                data: credentials,
                skipAuth: true
            });
            
            if (response.status === 'success' && response.token) {
                this.setAuthToken(response.token);
                return response.user;
            }
            
            throw new Error(response.message || 'Login failed');
        } catch (error) {
            console.error('Login failed:', error);
            throw error;
        }
    }
    
    /**
     * User registration
     */
    async register(userData) {
        try {
            const response = await this.makeRequest(this.endpoints.register, {
                method: 'POST',
                data: userData,
                skipAuth: true
            });
            
            if (response.status === 'success' && response.token) {
                this.setAuthToken(response.token);
                return response.user;
            }
            
            throw new Error(response.message || 'Registration failed');
        } catch (error) {
            console.error('Registration failed:', error);
            throw error;
        }
    }
    
    /**
     * User logout
     */
    async logout() {
        try {
            await this.makeRequest('/logout', {
                method: 'POST'
            });
            
            // Clear auth token regardless of response
            this.setAuthToken(null);
            
            return true;
        } catch (error) {
            console.error('Logout failed:', error);
            
            // Clear auth token even if API call fails
            this.setAuthToken(null);
            
            throw error;
        }
    }
    
    /**
     * Get current user profile
     */
    async getCurrentUser() {
        try {
            const response = await this.makeRequest(this.endpoints.user);
            
            if (response.status === 'success') {
                return response.user;
            }
            
            throw new Error('Failed to get user profile');
        } catch (error) {
            console.error('Failed to fetch user profile:', error);
            throw error;
        }
    }
}

// Export the API service
window.SportsBettingAPI = SportsBettingAPI;