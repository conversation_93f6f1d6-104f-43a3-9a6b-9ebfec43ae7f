// Keno - Mathematical Playground with GA Currency System
class KenoGame {
    constructor() {
        // Game state
        this.gameState = 'ready'; // ready, drawing, result
        this.balance = 10000; // GA currency
        this.currentBet = 50;
        this.selectedNumbers = [];
        this.drawnNumbers = [];
        this.roundsPlayed = 0;
        this.roundsWon = 0;
        this.knowledgePoints = 0;
        this.maxSelections = 15;
        this.gameActive = false;
        
        // Provably fair
        this.serverSeed = '';
        this.clientSeed = this.generateRandomString(16);
        this.serverSeedHash = '';
        this.nonce = 0;
        
        // Game data
        this.frequency = Array(81).fill(0); // For numbers 1-80
        this.lastResults = [];
        this.achievements = new Set();
        this.difficulty = 'standard';
        
        // Initialize DOM elements
        this.initElements();
        this.attachEventListeners();
        
        // Setup game
        this.generateKenoGrid();
        this.generateNewServerSeed();
        this.updateDisplay();
        this.addEducationalTooltips();
    }
    
    initElements() {
        // Game grid
        this.kenoGrid = document.getElementById('kenoGrid');
        this.numberCounter = document.getElementById('numberCounter');
        this.betInput = document.getElementById('betAmount');
        
        // Control buttons
        this.playBtn = document.getElementById('playBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.quickPickBtn = document.getElementById('quickPickBtn');
        this.infoBtn = document.getElementById('infoBtn');
        
        // Stats displays
        this.bankrollDisplay = document.getElementById('bankroll');
        this.currentBetDisplay = document.getElementById('currentBet');
        this.roundsPlayedDisplay = document.getElementById('roundsPlayed');
        this.winRateDisplay = document.getElementById('winRate');
        this.knowledgePointsDisplay = document.getElementById('knowledgePoints');
        
        // Probability displays
        this.odds3plusDisplay = document.getElementById('odds3plus');
        this.odds5plusDisplay = document.getElementById('odds5plus');
        this.odds7plusDisplay = document.getElementById('odds7plus');
        this.expectedValueDisplay = document.getElementById('expectedValue');
        this.probabilityIndicator = document.getElementById('probabilityIndicator');
        
        // Strategy elements
        this.strategyTips = document.getElementById('strategyTips');
        this.hotNumbers = document.getElementById('hotNumbers');
        this.coldNumbers = document.getElementById('coldNumbers');
        
        // Pattern indicators
        this.sequencePattern = document.getElementById('sequencePattern');
        this.balancedPattern = document.getElementById('balancedPattern');
        this.clusterPattern = document.getElementById('clusterPattern');
        
        // Modal
        this.resultModal = document.getElementById('resultModal');
        this.resultTitle = document.getElementById('resultTitle');
        this.resultMessage = document.getElementById('resultMessage');
        this.resultDetails = document.getElementById('resultDetails');
        this.achievementsDisplay = document.getElementById('achievements');
    }
    
    // Pattern definitions
    get PATTERNS() {
        return {
            SEQUENCE: {
                id: 'sequencePattern',
                name: 'Sequence',
                multiplier: 1.5,
                check: (numbers) => {
                    // Check for at least 3 consecutive numbers
                    const sorted = [...numbers].sort((a, b) => a - b);
                    let maxSequence = 1;
                    let currentSequence = 1;
                    
                    for (let i = 1; i < sorted.length; i++) {
                        if (sorted[i] === sorted[i-1] + 1) {
                            currentSequence++;
                            maxSequence = Math.max(maxSequence, currentSequence);
                        } else {
                            currentSequence = 1;
                        }
                    }
                    
                    return maxSequence >= 3;
                }
            },
            BALANCED: {
                id: 'balancedPattern',
                name: 'Balanced',
                multiplier: 1.3,
                check: (numbers) => {
                    // Check for balanced high/low distribution (40 is the midpoint of 1-80)
                    const highCount = numbers.filter(num => num > 40).length;
                    const lowCount = numbers.filter(num => num <= 40).length;
                    
                    // Within 1 number of perfect balance
                    return Math.abs(highCount - lowCount) <= 1;
                }
            },
            CLUSTER: {
                id: 'clusterPattern',
                name: 'Cluster',
                multiplier: 0.8,
                check: (numbers) => {
                    // Check for cluster in a quadrant (at least 3 numbers in same quadrant)
                    const quadrants = [0, 0, 0, 0]; // Top-left, top-right, bottom-left, bottom-right
                    
                    for (const num of numbers) {
                        const row = Math.floor((num - 1) / 10);
                        const col = (num - 1) % 10;
                        
                        const quadrantIdx = (row < 4 ? 0 : 2) + (col < 5 ? 0 : 1);
                        quadrants[quadrantIdx]++;
                    }
                    
                    return Math.max(...quadrants) >= 3;
                }
            }
        };
    }
    
    // Paytable
    get PAYTABLE() {
        return {
            0: 0,
            3: 1,
            5: 5,
            7: 25,
            10: 250,
            15: 10000
        };
    }
    
    // Generate the Keno grid
    generateKenoGrid() {
        if (!this.kenoGrid) return;
        
        this.kenoGrid.innerHTML = '';
        
        for (let i = 1; i <= 80; i++) {
            const numberCell = document.createElement('div');
            numberCell.className = 'keno-number';
            numberCell.dataset.number = i;
            numberCell.textContent = i;
            
            // Add heat indicator
            const heatIndicator = document.createElement('div');
            heatIndicator.className = 'heat-indicator';
            numberCell.appendChild(heatIndicator);
            
            // Add frequency counter
            const frequency = document.createElement('div');
            frequency.className = 'frequency';
            frequency.textContent = this.frequency[i];
            numberCell.appendChild(frequency);
            
            this.kenoGrid.appendChild(numberCell);
        }
    }
    
    // Handle number selection
    toggleNumberSelection(numberCell) {
        const number = parseInt(numberCell.dataset.number);
        
        if (numberCell.classList.contains('selected')) {
            // Deselect the number
            numberCell.classList.remove('selected');
            this.selectedNumbers = this.selectedNumbers.filter(num => num !== number);
        } else {
            // Select the number
            if (this.selectedNumbers.length < this.maxSelections) {
                numberCell.classList.add('selected');
                this.selectedNumbers.push(number);
            } else {
                this.showNotification(`Maximum ${this.maxSelections} numbers allowed`);
                return;
            }
        }
        
        this.updateNumberCounter();
        this.updatePatternIndicators();
        this.updateProbabilityDisplay();
        this.updateStrategyTips();
    }
    
    // Update the counter showing selected numbers
    updateNumberCounter() {
        if (this.numberCounter) {
            this.numberCounter.textContent = `${this.selectedNumbers.length}/${this.maxSelections}`;
        }
    }
    
    // Update pattern indicators based on selected numbers
    updatePatternIndicators() {
        for (const pattern of Object.values(this.PATTERNS)) {
            const patternElement = document.getElementById(pattern.id);
            if (patternElement) {
                const isActive = pattern.check(this.selectedNumbers);
                
                if (isActive) {
                    patternElement.classList.add('pattern-active');
                } else {
                    patternElement.classList.remove('pattern-active');
                }
            }
        }
    }
    
    // Calculate and update the probability display
    updateProbabilityDisplay() {
        const selected = this.selectedNumbers.length;
        
        if (selected === 0) {
            if (this.odds3plusDisplay) this.odds3plusDisplay.textContent = '--';
            if (this.odds5plusDisplay) this.odds5plusDisplay.textContent = '--';
            if (this.odds7plusDisplay) this.odds7plusDisplay.textContent = '--';
            if (this.expectedValueDisplay) this.expectedValueDisplay.textContent = '--';
            if (this.probabilityIndicator) this.probabilityIndicator.style.left = '10%';
            return;
        }
        
        // Calculate probabilities using hypergeometric distribution
        const odds3plus = this.calculateProbability(selected, 3);
        const odds5plus = this.calculateProbability(selected, 5);
        const odds7plus = this.calculateProbability(selected, 7);
        
        // Calculate expected value
        const ev = this.calculateExpectedValue(selected);
        
        // Update UI
        if (this.odds3plusDisplay) this.odds3plusDisplay.textContent = (odds3plus * 100).toFixed(1) + '%';
        if (this.odds5plusDisplay) this.odds5plusDisplay.textContent = (odds5plus * 100).toFixed(1) + '%';
        if (this.odds7plusDisplay) this.odds7plusDisplay.textContent = (odds7plus * 100).toFixed(1) + '%';
        if (this.expectedValueDisplay) this.expectedValueDisplay.textContent = ev.toFixed(2);
        
        // Update probability meter
        let meterPosition;
        if (selected <= 3) meterPosition = 10;
        else if (selected <= 7) meterPosition = 30;
        else if (selected <= 10) meterPosition = 50;
        else if (selected <= 12) meterPosition = 70;
        else meterPosition = 90;
        
        if (this.probabilityIndicator) {
            this.probabilityIndicator.style.left = `${meterPosition}%`;
        }
    }
    
    // Calculate probability of getting at least k matches
    calculateProbability(selected, k) {
        if (selected === 0) return 0;
        
        // Simplified probability calculation based on hypergeometric distribution
        const probMap = {
            1: {3: 0.03, 5: 0.001, 7: 0},
            2: {3: 0.05, 5: 0.002, 7: 0},
            3: {3: 0.08, 5: 0.004, 7: 0.0001},
            4: {3: 0.11, 5: 0.008, 7: 0.0005},
            5: {3: 0.14, 5: 0.015, 7: 0.001},
            6: {3: 0.18, 5: 0.025, 7: 0.003},
            7: {3: 0.22, 5: 0.035, 7: 0.006},
            8: {3: 0.26, 5: 0.048, 7: 0.010},
            9: {3: 0.30, 5: 0.065, 7: 0.015},
            10: {3: 0.35, 5: 0.086, 7: 0.024},
            11: {3: 0.40, 5: 0.100, 7: 0.035},
            12: {3: 0.45, 5: 0.120, 7: 0.050},
            13: {3: 0.50, 5: 0.140, 7: 0.065},
            14: {3: 0.55, 5: 0.160, 7: 0.080},
            15: {3: 0.60, 5: 0.180, 7: 0.100}
        };
        
        return probMap[selected][k] || 0;
    }
    
    // Calculate expected value of the bet
    calculateExpectedValue(selected) {
        if (selected === 0) return 0;
        
        // Calculate EV based on paytable and probabilities
        let ev = -1; // Start with -1 (the bet)
        
        for (const [matches, payout] of Object.entries(this.PAYTABLE)) {
            if (matches <= selected) {
                const prob = this.calculateExactMatchProbability(selected, parseInt(matches));
                ev += prob * payout;
            }
        }
        
        // Apply pattern multipliers
        for (const pattern of Object.values(this.PATTERNS)) {
            if (pattern.check(this.selectedNumbers)) {
                ev *= pattern.multiplier;
                break; // Only apply one pattern multiplier
            }
        }
        
        return ev;
    }
    
    // Calculate probability of exactly k matches
    calculateExactMatchProbability(selected, k) {
        // Simplified probability calculation for UI purposes
        const exactProbMap = {
            1: {0: 0.75, 1: 0.25},
            2: {0: 0.56, 1: 0.35, 2: 0.09},
            3: {0: 0.42, 1: 0.40, 2: 0.15, 3: 0.03},
            4: {0: 0.31, 1: 0.41, 2: 0.22, 3: 0.05, 4: 0.01},
            5: {0: 0.23, 1: 0.38, 3: 0.10, 5: 0.015},
            6: {0: 0.18, 3: 0.15, 5: 0.025, 6: 0.01},
            7: {0: 0.13, 3: 0.18, 5: 0.035, 7: 0.006},
            8: {0: 0.10, 3: 0.20, 5: 0.048, 7: 0.010},
            9: {0: 0.07, 3: 0.22, 5: 0.065, 7: 0.015},
            10: {0: 0.05, 3: 0.25, 5: 0.086, 7: 0.024, 10: 0.0011},
            15: {0: 0.01, 3: 0.30, 5: 0.18, 7: 0.10, 10: 0.02, 15: 0.0000003}
        };
        
        return (exactProbMap[selected] && exactProbMap[selected][k]) || 0;
    }
    
    // Update strategy tips based on selection
    updateStrategyTips() {
        if (!this.strategyTips) return;
        
        const selected = this.selectedNumbers.length;
        
        if (selected === 0) {
            this.strategyTips.textContent = 'Select 1-15 numbers from the grid to receive strategy tips.';
            return;
        }
        
        let tips = [];
        
        // Optimal selection count
        if (selected < 4) {
            tips.push('Consider selecting more numbers to increase your chances of winning.');
        } else if (selected > 12) {
            tips.push('Large selections have higher jackpot potential but lower overall win probability.');
        } else {
            tips.push('Your selection count is in the optimal range for balanced risk/reward.');
        }
        
        // Pattern advice
        const hasSequence = this.PATTERNS.SEQUENCE.check(this.selectedNumbers);
        const hasBalance = this.PATTERNS.BALANCED.check(this.selectedNumbers);
        const hasCluster = this.PATTERNS.CLUSTER.check(this.selectedNumbers);
        
        if (!hasSequence && !hasBalance && !hasCluster) {
            tips.push('Try creating a pattern (sequence, balanced, or cluster) for multiplier bonuses.');
        } else {
            if (hasSequence) {
                tips.push('Sequence pattern active! Your payout will be multiplied by 1.5x.');
            }
            if (hasBalance) {
                tips.push('Balanced pattern active! Your payout will be multiplied by 1.3x.');
            }
            if (hasCluster) {
                tips.push('Cluster pattern detected: This reduces your multiplier to 0.8x.');
            }
        }
        
        this.strategyTips.innerHTML = tips.join('<br><br>');
    }
    
    // Play a round of Keno
    playRound() {
        const selectedCount = this.selectedNumbers.length;
        
        // Validate selection and bet
        if (selectedCount === 0) {
            this.showNotification('Please select at least 1 number');
            return;
        }
        
        const bet = parseInt(this.betInput.value);
        if (isNaN(bet) || bet < 1 || bet > 100) {
            this.showNotification('Bet must be between 1 and 100 GA');
            return;
        }
        
        if (bet > this.balance) {
            this.showNotification('Insufficient GA balance');
            return;
        }
        
        // Deduct bet and update state
        this.currentBet = bet;
        this.balance -= bet;
        this.gameActive = true;
        this.gameState = 'drawing';
        this.roundsPlayed++;
        this.nonce++;
        
        // Draw random numbers
        this.drawNumbers();
    }
    
    // Draw 20 random numbers using provably fair algorithm
    drawNumbers() {
        // Generate provably fair results
        const combinedSeed = this.serverSeed + this.clientSeed + this.nonce.toString();
        let hash = this.hashCode(combinedSeed);
        
        // Generate 20 unique random numbers between 1-80
        const availableNumbers = Array.from({length: 80}, (_, i) => i + 1);
        this.drawnNumbers = [];
        
        for (let i = 0; i < 20; i++) {
            hash = this.hashCode(hash.toString());
            const randomIndex = Math.abs(hash) % availableNumbers.length;
            const drawnNumber = availableNumbers.splice(randomIndex, 1)[0];
            this.drawnNumbers.push(drawnNumber);
            
            // Update frequency
            this.frequency[drawnNumber]++;
        }
        
        // Show drawing animation
        this.animateDrawing();
    }
    
    // Animate the drawing process
    animateDrawing() {
        // For now, just calculate results directly
        // In a full implementation, this would show the drawing animation
        setTimeout(() => this.calculateResults(), 1000);
    }
    
    // Calculate match results
    calculateResults() {
        this.gameState = 'result';
        
        // Find matches
        const matches = this.selectedNumbers.filter(num => 
            this.drawnNumbers.includes(num));
        
        // Highlight matches on the grid
        this.drawnNumbers.forEach(number => {
            const cell = document.querySelector(`.keno-number[data-number="${number}"]`);
            if (cell) {
                cell.classList.add('drawn');
                
                if (this.selectedNumbers.includes(number)) {
                    cell.classList.add('matched');
                }
            }
        });
        
        // Calculate payout
        const matchCount = matches.length;
        let payout = 0;
        
        // Find the highest matching payout tier
        for (const [tier, multiplier] of Object.entries(this.PAYTABLE)) {
            if (matchCount >= parseInt(tier) && parseInt(tier) <= this.selectedNumbers.length) {
                payout = multiplier * this.currentBet;
            }
        }
        
        // Apply pattern multiplier
        for (const pattern of Object.values(this.PATTERNS)) {
            if (pattern.check(this.selectedNumbers)) {
                payout *= pattern.multiplier;
                break; // Only apply one pattern multiplier
            }
        }
        
        // Round to whole number
        payout = Math.floor(payout);
        
        // Update balance and stats
        this.balance += payout;
        if (payout > 0) {
            this.roundsWon++;
        }
        
        // Check for achievements
        const achievements = this.checkAchievements(matchCount, payout);
        
        // Save result for history
        this.lastResults.unshift({
            selected: this.selectedNumbers.length,
            matches: matchCount,
            payout: payout,
            nonce: this.nonce,
            drawnNumbers: [...this.drawnNumbers],
            selectedNumbers: [...this.selectedNumbers]
        });
        
        // Keep only last 10 results
        if (this.lastResults.length > 10) {
            this.lastResults.pop();
        }
        
        // Show result
        this.showResult(matchCount, payout, achievements);
        
        // Update displays
        this.updateDisplay();
        this.updateHotColdNumbers();
        
        // Reset game state
        this.gameActive = false;
        this.gameState = 'ready';
        
        // Generate new server seed for next round
        this.generateNewServerSeed();
    }
    
    // Check for achievements
    checkAchievements(matchCount, payout) {
        const achievements = [];
        
        // Check for specific achievements
        if (matchCount >= 5 && !this.achievements.has('fiveMatches')) {
            achievements.push('Probability Pro');
            this.achievements.add('fiveMatches');
            this.knowledgePoints += 25;
        }
        
        if (payout >= this.currentBet * 10 && !this.achievements.has('bigWin')) {
            achievements.push('Pattern Master');
            this.achievements.add('bigWin');
            this.knowledgePoints += 50;
        }
        
        // Award knowledge points for pattern usage
        for (const pattern of Object.values(this.PATTERNS)) {
            if (pattern.check(this.selectedNumbers)) {
                this.knowledgePoints += 10;
                break;
            }
        }
        
        return achievements;
    }
    
    // Show result modal
    showResult(matchCount, payout, achievements) {
        if (!this.resultModal) return;
        
        this.resultTitle.textContent = payout > 0 ? 'You Won!' : 'No Win';
        this.resultMessage.textContent = `You matched ${matchCount} out of ${this.selectedNumbers.length} selected numbers.`;
        
        this.resultDetails.innerHTML = `
            <p>Payout: ${payout} GA</p>
            <p>Current Balance: ${this.balance.toLocaleString()} GA</p>
        `;
        
        if (achievements.length > 0) {
            this.achievementsDisplay.innerHTML = achievements.map(achievement => 
                `<div class="achievement-badge">${achievement}</div>`
            ).join('');
        } else {
            this.achievementsDisplay.innerHTML = '';
        }
        
        this.resultModal.style.display = 'block';
    }
    
    // Close result modal
    closeResult() {
        if (this.resultModal) {
            this.resultModal.style.display = 'none';
        }
        
        // Reset drawn numbers on grid
        document.querySelectorAll('.keno-number').forEach(cell => {
            cell.classList.remove('drawn');
            cell.classList.remove('matched');
        });
    }
    
    // Quick pick random numbers
    quickPick() {
        // Clear current selection
        this.clearSelection();
        
        // Determine how many numbers to pick (random between 4-10)
        const numbersToPick = Math.floor(Math.random() * 7) + 4;
        
        // Randomly pick numbers
        const availableNumbers = Array.from({length: 80}, (_, i) => i + 1);
        
        for (let i = 0; i < numbersToPick; i++) {
            const randomIndex = Math.floor(Math.random() * availableNumbers.length);
            const selectedNumber = availableNumbers.splice(randomIndex, 1)[0];
            
            this.selectedNumbers.push(selectedNumber);
            const cell = document.querySelector(`.keno-number[data-number="${selectedNumber}"]`);
            if (cell) {
                cell.classList.add('selected');
            }
        }
        
        // Update displays
        this.updateNumberCounter();
        this.updatePatternIndicators();
        this.updateProbabilityDisplay();
        this.updateStrategyTips();
    }
    
    // Clear selection
    clearSelection() {
        this.selectedNumbers = [];
        
        document.querySelectorAll('.keno-number').forEach(cell => {
            cell.classList.remove('selected');
        });
        
        this.updateNumberCounter();
        this.updatePatternIndicators();
        this.updateProbabilityDisplay();
        this.updateStrategyTips();
    }
    
    // Update hot/cold numbers display
    updateHotColdNumbers() {
        // Only update after some rounds played
        if (this.roundsPlayed < 3) return;
        
        // Get frequency counts
        const frequencyCounts = this.frequency.map((count, index) => ({
            number: index,
            count: count
        })).filter(item => item.number > 0); // Skip index 0
        
        // Sort by frequency
        frequencyCounts.sort((a, b) => b.count - a.count);
        
        // Update hot numbers (top 5)
        if (this.hotNumbers) {
            this.hotNumbers.innerHTML = '';
            
            for (let i = 0; i < 5; i++) {
                if (i < frequencyCounts.length) {
                    const hotNumber = document.createElement('div');
                    hotNumber.className = 'history-number';
                    hotNumber.style.background = 'var(--math-red)';
                    hotNumber.textContent = frequencyCounts[i].number;
                    this.hotNumbers.appendChild(hotNumber);
                }
            }
        }
        
        // Update cold numbers (bottom 5)
        if (this.coldNumbers) {
            this.coldNumbers.innerHTML = '';
            
            const totalNumbers = frequencyCounts.length;
            for (let i = totalNumbers - 5; i < totalNumbers; i++) {
                if (i >= 0) {
                    const coldNumber = document.createElement('div');
                    coldNumber.className = 'history-number';
                    coldNumber.style.background = 'var(--math-blue)';
                    coldNumber.textContent = frequencyCounts[i].number;
                    this.coldNumbers.appendChild(coldNumber);
                }
            }
        }
        
        // Update frequency indicators on grid
        document.querySelectorAll('.keno-number').forEach(cell => {
            const number = parseInt(cell.dataset.number);
            const frequency = this.frequency[number];
            
            // Update frequency display
            const frequencyEl = cell.querySelector('.frequency');
            if (frequencyEl && frequency > 0) {
                frequencyEl.textContent = frequency;
                frequencyEl.style.opacity = 0.8;
            }
            
            // Update heat indicator
            const heatIndicator = cell.querySelector('.heat-indicator');
            if (heatIndicator) {
                const maxFreq = Math.max(...this.frequency);
                if (maxFreq > 0) {
                    const heatScale = frequency / maxFreq;
                    heatIndicator.style.transform = `scaleX(${heatScale})`;
                }
            }
        });
    }
    
    // Update all display elements
    updateDisplay() {
        if (this.bankrollDisplay) this.bankrollDisplay.textContent = this.balance.toLocaleString();
        if (this.currentBetDisplay) this.currentBetDisplay.textContent = this.currentBet;
        if (this.roundsPlayedDisplay) this.roundsPlayedDisplay.textContent = this.roundsPlayed;
        if (this.knowledgePointsDisplay) this.knowledgePointsDisplay.textContent = this.knowledgePoints;
        
        const winRate = this.roundsPlayed > 0 ? 
            (this.roundsWon / this.roundsPlayed * 100).toFixed(1) : 0;
        if (this.winRateDisplay) this.winRateDisplay.textContent = winRate + '%';
    }
    
    // Show notification
    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(45deg, var(--math-blue), var(--math-purple));
            color: white;
            padding: 1rem;
            border-radius: 8px;
            z-index: 10000;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideIn 0.3s ease reverse';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }
    
    // Add educational tooltips to elements
    addEducationalTooltips() {
        // Implementation would add hover tooltips for educational content
        // For brevity, this is simplified
    }
    
    // Show Math Lab (educational info)
    showMathLab() {
        this.showNotification('Math Lab mode coming soon!');
        // In full implementation, this would show an educational modal
        // with probability experiments and tutorials
    }
    
    // Attach all event listeners
    attachEventListeners() {
        // Number selection
        if (this.kenoGrid) {
            this.kenoGrid.addEventListener('click', (e) => {
                if (e.target.classList.contains('keno-number')) {
                    this.toggleNumberSelection(e.target);
                }
            });
        }
        
        // Control buttons
        if (this.playBtn) this.playBtn.addEventListener('click', () => this.playRound());
        if (this.clearBtn) this.clearBtn.addEventListener('click', () => this.clearSelection());
        if (this.quickPickBtn) this.quickPickBtn.addEventListener('click', () => this.quickPick());
        if (this.infoBtn) this.infoBtn.addEventListener('click', () => this.showMathLab());
        
        // Validate bet input
        if (this.betInput) {
            this.betInput.addEventListener('input', () => {
                const value = parseInt(this.betInput.value);
                const max = Math.min(100, this.balance);
                if (value > max) {
                    this.betInput.value = max;
                    this.showNotification(`Maximum bet is ${max} GA`);
                }
            });
        }
    }
    
    // Provably fair methods
    generateNewServerSeed() {
        this.serverSeed = this.generateRandomString(32);
        this.serverSeedHash = this.hashString(this.serverSeed);
    }
    
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    hashString(str) {
        // Simple hash function for demo purposes
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(8, '0');
    }
    
    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash;
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM loaded, initializing Keno game...");
    try {
        window.kenoGame = new KenoGame();
        
        // Global functions for HTML onclick attributes
        window.closeResult = function() {
            if (window.kenoGame) {
                window.kenoGame.closeResult();
            }
        };
        
        console.log("Keno game initialized successfully!");
    } catch (error) {
        console.error("Error initializing Keno game:", error);
    }
});