<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/settings.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
     <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                    <span class="logo-text">GoldenAura</span>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div class="user-balance" style="display: none;">
                    <span class="balance-label">Balance:</span>
                    <span class="balance-amount" data-balance>0.00 GA</span>
                </div>
                <div class="user-profile" style="display: none;" data-user-element="profile">
                    <span class="user-email" data-user-element="email"></span>
                    <button class="btn btn-logout" data-auth-action="logout" style="display: none;">Logout</button>
                </div>
                <div class="live-support-indicator">
                    <div class="support-dot"></div>
                    <span>Live Support</span>
                </div>
                <button class="btn btn-login" onclick="showSimpleLogin()">Login</button>
                <button class="btn btn-register" onclick="window.open('https://t.me/Goldenaura_PY_bot?start=register', '_blank')">Register</button>
            </div>
        </div>
    </header>

    <!-- Settings Container -->
    <div class="settings-container">
        <!-- Settings Sidebar -->
        <aside class="settings-sidebar">
            <div class="settings-nav">
                <h2><i class="fas fa-cog"></i> Settings</h2>
                <div class="back-to-app">
                    <a href="index.html" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to App</span>
                    </a>
                </div>
                <nav class="settings-menu">
                    <a href="#account" class="menu-item active" data-section="account">
                        <i class="fas fa-user"></i>
                        <span>Account & Profile</span>
                    </a>
                    <a href="#security" class="menu-item" data-section="security">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security & Privacy</span>
                    </a>
                    <a href="#notifications" class="menu-item" data-section="notifications">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                    </a>
                    <a href="#preferences" class="menu-item" data-section="preferences">
                        <i class="fas fa-palette"></i>
                        <span>Preferences</span>
                    </a>
                    <a href="#integrations" class="menu-item" data-section="integrations">
                        <i class="fas fa-plug"></i>
                        <span>Integrations</span>
                    </a>
                    <a href="#billing" class="menu-item" data-section="billing">
                        <i class="fas fa-credit-card"></i>
                        <span>Billing & Subscription</span>
                    </a>
                </nav>
                
                <!-- Mobile Overlay -->
                <div class="settings-overlay" id="settingsOverlay"></div>
            </div>
        </aside>

        <!-- Settings Content -->
        <main class="settings-content">
            <!-- Breadcrumb -->
            <div class="breadcrumb">
                <span><i class="fas fa-home"></i> Home</span>
                <span class="separator">/</span>
                <span id="currentSection">Account & Profile</span>
            </div>

            <!-- Account & Profile Section -->
            <section id="account" class="settings-section active">
                <div class="section-header">
                    <h1>Account & Profile</h1>
                    <p>Manage your personal information and account details</p>
                </div>

                <div class="settings-card">
                    <h3>Profile Information</h3>
                    <form class="settings-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name</label>
                                <input type="text" id="firstName" value="John" class="form-input">
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name</label>
                                <input type="text" id="lastName" value="Doe" class="form-input">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" value="<EMAIL>" class="form-input">
                            <small class="form-hint">This email will be used for login and important notifications</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" value="+****************" class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" value="johndoe123" class="form-input">
                            <small class="form-hint">Your unique identifier for the platform</small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                            <button type="button" class="btn btn-secondary">Cancel</button>
                        </div>
                    </form>
                </div>

                <div class="settings-card">
                    <h3>Profile Picture</h3>
                    <div class="profile-picture-section">
                        <div class="current-picture">
                            <img src="https://via.placeholder.com/120x120/4F46E5/FFFFFF?text=JD" alt="Profile Picture" class="profile-img">
                        </div>
                        <div class="picture-actions">
                            <button class="btn btn-outline">Upload New Photo</button>
                            <button class="btn btn-text">Remove Photo</button>
                            <small class="form-hint">Recommended: 400x400px, max 2MB, JPG or PNG</small>
                        </div>
                    </div>
                </div>

                <div class="settings-card danger-zone">
                    <h3>Account Actions</h3>
                    <div class="danger-actions">
                        <div class="action-item">
                            <div class="action-info">
                                <h4>Deactivate Account</h4>
                                <p>Temporarily disable your account. You can reactivate it anytime by logging in.</p>
                            </div>
                            <button class="btn btn-warning">Deactivate</button>
                        </div>
                        <div class="action-item">
                            <div class="action-info">
                                <h4>Delete Account</h4>
                                <p>Permanently delete your account and all associated data. This action cannot be undone.</p>
                            </div>
                            <button class="btn btn-danger">Delete Account</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Security & Privacy Section -->
            <section id="security" class="settings-section">
                <div class="section-header">
                    <h1>Security & Privacy</h1>
                    <p>Manage your account security and privacy preferences</p>
                </div>

                <div class="settings-card">
                    <h3>Password & Authentication</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label for="currentPassword">Current Password</label>
                            <input type="password" id="currentPassword" class="form-input">
                        </div>
                        
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" id="newPassword" class="form-input">
                            <div class="password-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <small class="strength-text">Password strength: <span id="strengthLevel">Enter a password</span></small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword">Confirm New Password</label>
                            <input type="password" id="confirmPassword" class="form-input">
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Update Password</button>
                        </div>
                    </form>
                </div>

                <div class="settings-card">
                    <h3>Two-Factor Authentication</h3>
                    <div class="mfa-section">
                        <div class="mfa-status">
                            <div class="status-indicator disabled">
                                <i class="fas fa-shield-alt"></i>
                                <span>Two-factor authentication is currently disabled</span>
                            </div>
                            <button class="btn btn-primary">Enable 2FA</button>
                        </div>
                        <p class="mfa-description">
                            Add an extra layer of security to your account by requiring a code from your phone in addition to your password.
                        </p>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Active Sessions</h3>
                    <div class="sessions-list">
                        <div class="session-item current">
                            <div class="session-info">
                                <div class="device-info">
                                    <i class="fas fa-desktop"></i>
                                    <span class="device-name">Chrome on Windows</span>
                                    <span class="current-label">Current Session</span>
                                </div>
                                <div class="session-details">
                                    <span class="location">New York, US</span>
                                    <span class="time">Active now</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="session-item">
                            <div class="session-info">
                                <div class="device-info">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span class="device-name">Safari on iPhone</span>
                                </div>
                                <div class="session-details">
                                    <span class="location">New York, US</span>
                                    <span class="time">2 hours ago</span>
                                </div>
                            </div>
                            <button class="btn btn-text btn-revoke">Revoke</button>
                        </div>
                    </div>
                    
                    <div class="sessions-actions">
                        <button class="btn btn-outline">Revoke All Other Sessions</button>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Privacy Settings</h3>
                    <div class="privacy-controls">
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Profile Visibility</h4>
                                <p>Control who can see your profile information</p>
                            </div>
                            <select class="form-select">
                                <option value="public">Public</option>
                                <option value="friends" selected>Friends Only</option>
                                <option value="private">Private</option>
                            </select>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Data Analytics</h4>
                                <p>Allow us to collect anonymous usage data to improve our service</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Marketing Communications</h4>
                                <p>Receive promotional emails and marketing messages</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="legal-documents">
                            <h4>Legal Documents</h4>
                            <ul class="document-links">
                                <li><a href="terms.html" target="_blank">Terms of Service</a> - Read our terms and conditions</li>
                                <li><a href="privacy.html" target="_blank">Privacy Policy</a> - Learn how we protect your data</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Data Export & Deletion</h3>
                    <div class="data-actions">
                        <div class="action-item">
                            <div class="action-info">
                                <h4>Download Your Data</h4>
                                <p>Export all your account data in a portable format</p>
                            </div>
                            <button class="btn btn-outline">Request Export</button>
                        </div>
                        
                        <div class="action-item">
                            <div class="action-info">
                                <h4>Delete All Data</h4>
                                <p>Permanently remove all your data from our servers</p>
                            </div>
                            <button class="btn btn-danger">Delete Data</button>
                        </div>
                    </div>
                </div>
            </section>            <!-- Notifications Section -->
            <section id="notifications" class="settings-section">
                <div class="section-header">
                    <h1>Notifications</h1>
                    <p>Customize how and when you receive notifications</p>
                </div>

                <div class="settings-card">
                    <h3>Email Notifications</h3>
                    <div class="notification-controls">
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Account Security</h4>
                                <p>Login alerts, password changes, and security updates</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked disabled>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Promotions & Offers</h4>
                                <p>Special bonuses, tournaments, and promotional campaigns</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Game Updates</h4>
                                <p>New games, features, and platform updates</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Weekly Summary</h4>
                                <p>Your gaming activity and performance summary</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Push Notifications</h3>
                    <div class="notification-controls">
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Live Sports Updates</h4>
                                <p>Real-time updates on your active bets and favorite teams</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Game Invitations</h4>
                                <p>Invites to multiplayer games and tournaments</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Bonus Reminders</h4>
                                <p>Notifications when you have unclaimed bonuses</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Notification Schedule</h3>
                    <div class="schedule-controls">
                        <div class="form-group">
                            <label>Quiet Hours</label>
                            <p class="form-hint">Disable non-urgent notifications during these hours</p>
                            <div class="time-range">
                                <input type="time" value="22:00" class="form-input time-input">
                                <span>to</span>
                                <input type="time" value="08:00" class="form-input time-input">
                            </div>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Weekend Notifications</h4>
                                <p>Receive notifications on weekends</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Preferences Section -->
            <section id="preferences" class="settings-section">
                <div class="section-header">
                    <h1>Preferences</h1>
                    <p>Customize your gaming experience and interface</p>
                </div>

                <div class="settings-card">
                    <h3>Appearance</h3>
                    <div class="appearance-controls">
                        <div class="form-group">
                            <label>Theme</label>
                            <div class="theme-options">
                                <label class="theme-option">
                                    <input type="radio" name="theme" value="light">
                                    <div class="theme-preview light">
                                        <div class="preview-header"></div>
                                        <div class="preview-content"></div>
                                    </div>
                                    <span>Light</span>
                                </label>
                                
                                <label class="theme-option">
                                    <input type="radio" name="theme" value="dark" checked>
                                    <div class="theme-preview dark">
                                        <div class="preview-header"></div>
                                        <div class="preview-content"></div>
                                    </div>
                                    <span>Dark</span>
                                </label>
                                
                                <label class="theme-option">
                                    <input type="radio" name="theme" value="auto">
                                    <div class="theme-preview auto">
                                        <div class="preview-header"></div>
                                        <div class="preview-content"></div>
                                    </div>
                                    <span>Auto</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="accentColor">Accent Color</label>
                            <div class="color-options">
                                <label class="color-option">
                                    <input type="radio" name="accent" value="blue" checked>
                                    <span class="color-swatch blue"></span>
                                </label>
                                <label class="color-option">
                                    <input type="radio" name="accent" value="purple">
                                    <span class="color-swatch purple"></span>
                                </label>
                                <label class="color-option">
                                    <input type="radio" name="accent" value="green">
                                    <span class="color-swatch green"></span>
                                </label>
                                <label class="color-option">
                                    <input type="radio" name="accent" value="orange">
                                    <span class="color-swatch orange"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Language & Region</h3>
                    <div class="locale-controls">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="language">Language</label>
                                <select id="language" class="form-select">
                                    <option value="en" selected>English</option>
                                    <option value="es">Español</option>
                                    <option value="fr">Français</option>
                                    <option value="de">Deutsch</option>
                                    <option value="pt">Português</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="timezone">Timezone</label>
                                <select id="timezone" class="form-select">
                                    <option value="UTC-5" selected>Eastern Time (UTC-5)</option>
                                    <option value="UTC-6">Central Time (UTC-6)</option>
                                    <option value="UTC-7">Mountain Time (UTC-7)</option>
                                    <option value="UTC-8">Pacific Time (UTC-8)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="currency">Currency</label>
                                <select id="currency" class="form-select">
                                    <option value="USD" selected>USD ($)</option>
                                    <option value="EUR">EUR (€)</option>
                                    <option value="GBP">GBP (£)</option>
                                    <option value="CAD">CAD ($)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="dateFormat">Date Format</label>
                                <select id="dateFormat" class="form-select">
                                    <option value="MM/DD/YYYY" selected>MM/DD/YYYY</option>
                                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Gaming Preferences</h3>
                    <div class="gaming-controls">
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Sound Effects</h4>
                                <p>Enable audio feedback for games and actions</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Animations</h4>
                                <p>Enable smooth transitions and game animations</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Auto-play</h4>
                                <p>Automatically start next game round</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label for="defaultBet">Default Bet Amount</label>
                            <select id="defaultBet" class="form-select">
                                <option value="1">$1</option>
                                <option value="5" selected>$5</option>
                                <option value="10">$10</option>
                                <option value="25">$25</option>
                                <option value="50">$50</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Responsible Gaming</h3>
                    <div class="responsible-gaming-controls">
                        <div class="form-group">
                            <label for="dailyLimit">Daily Spending Limit</label>
                            <input type="number" id="dailyLimit" value="100" class="form-input" min="0" step="10">
                            <small class="form-hint">Set a daily limit to help manage your spending</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="sessionReminder">Session Reminder</label>
                            <select id="sessionReminder" class="form-select">
                                <option value="0">Disabled</option>
                                <option value="30">Every 30 minutes</option>
                                <option value="60" selected>Every hour</option>
                                <option value="120">Every 2 hours</option>
                            </select>
                            <small class="form-hint">Get reminded about your gaming session duration</small>
                        </div>
                        
                        <div class="control-item">
                            <div class="control-info">
                                <h4>Reality Check</h4>
                                <p>Show pop-up reminders with session time and spending</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>            <!-- Integrations Section -->
            <section id="integrations" class="settings-section">
                <div class="section-header">
                    <h1>Integrations</h1>
                    <p>Connect and manage third-party services and applications</p>
                </div>

                <div class="settings-card">
                    <h3>Connected Accounts</h3>
                    <div class="connections-list">
                        <div class="connection-item">
                            <div class="connection-info">
                                <div class="connection-logo">
                                    <i class="fab fa-google"></i>
                                </div>
                                <div class="connection-details">
                                    <h4>Google</h4>
                                    <p><NAME_EMAIL></p>
                                </div>
                            </div>
                            <button class="btn btn-outline btn-sm">Disconnect</button>
                        </div>
                        
                        <div class="connection-item disconnected">
                            <div class="connection-info">
                                <div class="connection-logo">
                                    <i class="fab fa-facebook-f"></i>
                                </div>
                                <div class="connection-details">
                                    <h4>Facebook</h4>
                                    <p>Not connected</p>
                                </div>
                            </div>
                            <button class="btn btn-primary btn-sm">Connect</button>
                        </div>
                        
                        <div class="connection-item disconnected">
                            <div class="connection-info">
                                <div class="connection-logo">
                                    <i class="fab fa-apple"></i>
                                </div>
                                <div class="connection-details">
                                    <h4>Apple</h4>
                                    <p>Not connected</p>
                                </div>
                            </div>
                            <button class="btn btn-primary btn-sm">Connect</button>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Payment Methods</h3>
                    <div class="connections-list">
                        <div class="connection-item">
                            <div class="connection-info">
                                <div class="connection-logo">
                                    <i class="fab fa-paypal"></i>
                                </div>
                                <div class="connection-details">
                                    <h4>PayPal</h4>
                                    <p>Connected as j********@gmail.com</p>
                                </div>
                            </div>
                            <button class="btn btn-outline btn-sm">Disconnect</button>
                        </div>
                        
                        <div class="connection-item disconnected">
                            <div class="connection-info">
                                <div class="connection-logo">
                                    <i class="fab fa-cc-stripe"></i>
                                </div>
                                <div class="connection-details">
                                    <h4>Stripe</h4>
                                    <p>Not connected</p>
                                </div>
                            </div>
                            <button class="btn btn-primary btn-sm">Connect</button>
                        </div>
                        
                        <div class="connection-item disconnected">
                            <div class="connection-info">
                                <div class="connection-logo crypto">
                                    <i class="fab fa-bitcoin"></i>
                                </div>
                                <div class="connection-details">
                                    <h4>Cryptocurrency</h4>
                                    <p>Not connected</p>
                                </div>
                            </div>
                            <button class="btn btn-primary btn-sm">Connect</button>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>API Access</h3>
                    <div class="api-section">
                        <div class="api-info">
                            <p>Generate API keys to integrate GoldenAura with your applications or services</p>
                            <div class="form-actions">
                                <button class="btn btn-primary">Create New API Key</button>
                            </div>
                        </div>
                        
                        <div class="api-keys">
                            <div class="key-item">
                                <div class="key-info">
                                    <h4>Development Key</h4>
                                    <p>Created: Feb 15, 2025</p>
                                    <p>Last used: 2 days ago</p>
                                </div>
                                <div class="key-actions">
                                    <button class="btn btn-text">View Key</button>
                                    <button class="btn btn-text">Revoke</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="api-documentation">
                            <a href="#" class="btn btn-link">
                                <i class="fas fa-book"></i>
                                <span>View API Documentation</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Webhooks</h3>
                    <div class="webhooks-section">
                        <div class="webhooks-info">
                            <p>Configure webhooks to receive real-time notifications of events in your account</p>
                            <div class="form-actions">
                                <button class="btn btn-primary">Add Webhook URL</button>
                            </div>
                        </div>
                        
                        <div class="webhook-list">
                            <div class="webhook-item">
                                <div class="webhook-info">
                                    <h4>https://myapp.example.com/webhooks/goldenaura</h4>
                                    <p>Events: Account Updates, Payment Notifications</p>
                                </div>
                                <div class="webhook-actions">
                                    <button class="btn btn-text">Edit</button>
                                    <button class="btn btn-text">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Billing & Subscription Section -->
            <section id="billing" class="settings-section">
                <div class="section-header">
                    <h1>Billing & Subscription</h1>
                    <p>Manage your payment methods and subscription details</p>
                </div>

                <div class="settings-card">
                    <h3>Current Plan</h3>
                    <div class="plan-details">
                        <div class="plan-info">
                            <div class="plan-badge premium">
                                <i class="fas fa-crown"></i>
                                <span>Premium</span>
                            </div>
                            <div class="plan-pricing">
                                <h4>$19.99/month</h4>
                                <p>Renewed on July 15, 2025</p>
                            </div>
                        </div>
                        <div class="plan-features">
                            <ul>
                                <li><i class="fas fa-check"></i> Enhanced odds on all sports bets</li>
                                <li><i class="fas fa-check"></i> Exclusive monthly bonuses</li>
                                <li><i class="fas fa-check"></i> Priority customer support</li>
                                <li><i class="fas fa-check"></i> VIP tournaments access</li>
                            </ul>
                        </div>
                        <div class="plan-actions">
                            <button class="btn btn-outline">Change Plan</button>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Payment Methods</h3>
                    <div class="payment-methods">
                        <div class="payment-item primary">
                            <div class="payment-info">
                                <div class="card-icon">
                                    <i class="far fa-credit-card"></i>
                                </div>
                                <div class="card-details">
                                    <h4>•••• •••• •••• 4567</h4>
                                    <p>Visa ending in 4567 • Expires 03/27</p>
                                    <span class="primary-badge">Primary</span>
                                </div>
                            </div>
                            <div class="payment-actions">
                                <button class="btn btn-text">Edit</button>
                                <button class="btn btn-text">Remove</button>
                            </div>
                        </div>
                        
                        <div class="payment-item">
                            <div class="payment-info">
                                <div class="card-icon paypal">
                                    <i class="fab fa-paypal"></i>
                                </div>
                                <div class="card-details">
                                    <h4>PayPal</h4>
                                    <p>j********@gmail.com</p>
                                </div>
                            </div>
                            <div class="payment-actions">
                                <button class="btn btn-text">Make Primary</button>
                                <button class="btn btn-text">Remove</button>
                            </div>
                        </div>
                        
                        <div class="add-payment">
                            <button class="btn btn-outline">
                                <i class="fas fa-plus"></i>
                                <span>Add Payment Method</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Billing History</h3>
                    <div class="billing-history">
                        <div class="billing-table">
                            <div class="table-header">
                                <div class="header-cell">Date</div>
                                <div class="header-cell">Description</div>
                                <div class="header-cell">Amount</div>
                                <div class="header-cell">Status</div>
                                <div class="header-cell">Receipt</div>
                            </div>
                            
                            <div class="table-row">
                                <div class="table-cell">Jun 15, 2025</div>
                                <div class="table-cell">Premium Subscription</div>
                                <div class="table-cell">$19.99</div>
                                <div class="table-cell">
                                    <span class="status-badge success">Paid</span>
                                </div>
                                <div class="table-cell">
                                    <a href="#" class="btn btn-text">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="table-row">
                                <div class="table-cell">May 15, 2025</div>
                                <div class="table-cell">Premium Subscription</div>
                                <div class="table-cell">$19.99</div>
                                <div class="table-cell">
                                    <span class="status-badge success">Paid</span>
                                </div>
                                <div class="table-cell">
                                    <a href="#" class="btn btn-text">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="table-row">
                                <div class="table-cell">Apr 15, 2025</div>
                                <div class="table-cell">Premium Subscription</div>
                                <div class="table-cell">$19.99</div>
                                <div class="table-cell">
                                    <span class="status-badge success">Paid</span>
                                </div>
                                <div class="table-cell">
                                    <a href="#" class="btn btn-text">
                                        <i class="fas fa-download"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-actions">
                            <button class="btn btn-text">View All Transactions</button>
                        </div>
                    </div>
                </div>

                <div class="settings-card">
                    <h3>Subscription Management</h3>
                    <div class="subscription-actions">
                        <div class="action-item">
                            <div class="action-info">
                                <h4>Pause Subscription</h4>
                                <p>Temporarily pause your subscription. You won't be charged during this period.</p>
                            </div>
                            <button class="btn btn-outline">Pause</button>
                        </div>
                        
                        <div class="action-item">
                            <div class="action-info">
                                <h4>Cancel Subscription</h4>
                                <p>Cancel your subscription. You'll still have access until the end of your billing period.</p>
                            </div>
                            <button class="btn btn-warning">Cancel</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script>
        // Enhanced Settings page JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile detection
            const isMobile = window.innerWidth <= 767;
            const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
            const isDesktop = window.innerWidth >= 1024;
            
            // Cache DOM elements
            const elements = {
                menuToggle: document.querySelector('.menu-toggle'),
                sidebar: document.querySelector('.settings-sidebar'),
                overlay: document.getElementById('settingsOverlay'),
                menuItems: document.querySelectorAll('.settings-menu .menu-item'),
                sections: document.querySelectorAll('.settings-section'),
                breadcrumbSection: document.getElementById('currentSection'),
                searchInput: document.getElementById('searchInput'),
                passwordInput: document.getElementById('newPassword'),
                strengthLevel: document.getElementById('strengthLevel'),
                strengthFill: document.querySelector('.strength-fill'),
                toggleSwitches: document.querySelectorAll('.toggle-switch input'),
                forms: document.querySelectorAll('.settings-form')
            };
            
            // Initialize
            init();
            
            function init() {
                setupMobileMenu();
                setupNavigation();
                setupPasswordStrength();
                setupToggles();
                setupForms();
                setupSearch();
                handleInitialHash();
                
                // Handle hash changes
                window.addEventListener('hashchange', handleHashChange);
                window.addEventListener('resize', handleResize);
            }
            
            function setupMobileMenu() {
                if (elements.menuToggle) {
                    elements.menuToggle.addEventListener('click', toggleMobileMenu);
                }
                
                if (elements.overlay) {
                    elements.overlay.addEventListener('click', closeMobileMenu);
                }
                
                // Close menu on escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && elements.sidebar.classList.contains('active')) {
                        closeMobileMenu();
                    }
                });
            }
            
            function toggleMobileMenu() {
                const isOpen = elements.sidebar.classList.contains('active');
                if (isOpen) {
                    closeMobileMenu();
                } else {
                    openMobileMenu();
                }
            }
            
            function openMobileMenu() {
                elements.sidebar.classList.add('active');
                elements.overlay.classList.add('active');
                document.body.style.overflow = 'hidden';
                triggerHapticFeedback('medium');
            }
            
            function closeMobileMenu() {
                elements.sidebar.classList.remove('active');
                elements.overlay.classList.remove('active');
                document.body.style.overflow = '';
                triggerHapticFeedback('light');
            }
            
            function setupNavigation() {
                elements.menuItems.forEach(item => {
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        const targetSection = this.getAttribute('data-section');
                        switchToSection(targetSection);
                        
                        // Close mobile menu after navigation
                        if (window.innerWidth < 1024) {
                            setTimeout(closeMobileMenu, 300);
                        }
                        
                        triggerHapticFeedback('medium');
                    });
                });
            }
            
            function switchToSection(targetSection) {
                // Update active menu item
                elements.menuItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.getAttribute('data-section') === targetSection) {
                        item.classList.add('active');
                        elements.breadcrumbSection.textContent = item.querySelector('span').textContent;
                    }
                });
                
                // Show the selected section with animation
                elements.sections.forEach(section => {
                    section.classList.remove('active');
                    if (section.id === targetSection) {
                        section.classList.add('active');
                        section.style.animation = 'fadeIn 0.3s ease-out';
                    }
                });
                
                // Update URL hash
                window.location.hash = targetSection;
                
                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
            
            function setupPasswordStrength() {
                if (!elements.passwordInput) return;
                
                elements.passwordInput.addEventListener('input', function() {
                    const value = this.value;
                    let strength = 0;
                    
                    // Calculate strength
                    if (value.length >= 8) strength += 1;
                    if (value.match(/[A-Z]/)) strength += 1;
                    if (value.match(/[0-9]/)) strength += 1;
                    if (value.match(/[^A-Za-z0-9]/)) strength += 1;
                    
                    // Update UI
                    updatePasswordStrength(value, strength);
                });
            }
            
            function updatePasswordStrength(value, strength) {
                if (value.length === 0) {
                    elements.strengthLevel.textContent = 'Enter a password';
                    elements.strengthFill.style.width = '0%';
                    elements.strengthFill.className = 'strength-fill';
                } else if (strength < 2) {
                    elements.strengthLevel.textContent = 'Weak';
                    elements.strengthFill.style.width = '25%';
                    elements.strengthFill.className = 'strength-fill weak';
                } else if (strength < 3) {
                    elements.strengthLevel.textContent = 'Medium';
                    elements.strengthFill.style.width = '50%';
                    elements.strengthFill.className = 'strength-fill medium';
                } else if (strength < 4) {
                    elements.strengthLevel.textContent = 'Strong';
                    elements.strengthFill.style.width = '75%';
                    elements.strengthFill.className = 'strength-fill strong';
                } else {
                    elements.strengthLevel.textContent = 'Very Strong';
                    elements.strengthFill.style.width = '100%';
                    elements.strengthFill.className = 'strength-fill very-strong';
                }
            }
            
            function setupToggles() {
                elements.toggleSwitches.forEach(toggle => {
                    toggle.addEventListener('change', function() {
                        const setting = this.closest('.control-item')?.querySelector('h4')?.textContent;
                        const enabled = this.checked;
                        
                        console.log(`Setting "${setting}" ${enabled ? 'enabled' : 'disabled'}`);
                        
                        // Save to localStorage
                        if (setting) {
                            localStorage.setItem(`setting_${setting.toLowerCase().replace(/\s+/g, '_')}`, enabled);
                        }
                        
                        triggerHapticFeedback('light');
                        showToast(`${setting} ${enabled ? 'enabled' : 'disabled'}`, 'info');
                    });
                });
            }
            
            function setupForms() {
                elements.forms.forEach(form => {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        handleFormSubmit(this);
                    });
                });
            }
            
            function handleFormSubmit(form) {
                const formData = new FormData(form);
                const formType = form.closest('.settings-section')?.id || 'unknown';
                
                // Add loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Saving...';
                submitBtn.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    
                    showToast('Settings saved successfully!', 'success');
                    triggerHapticFeedback('success');
                    
                    console.log(`Form submitted: ${formType}`, Object.fromEntries(formData));
                }, 1000);
            }
            
            function setupSearch() {
                if (!elements.searchInput) return;
                
                let searchTimeout;
                elements.searchInput.addEventListener('input', function(e) {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        performSearch(e.target.value);
                    }, 300);
                });
            }
            
            function performSearch(query) {
                if (!query.trim()) {
                    clearSearchHighlights();
                    return;
                }
                
                const searchQuery = query.toLowerCase();
                let foundResults = false;
                
                // Search through all sections
                elements.sections.forEach(section => {
                    const content = section.textContent.toLowerCase();
                    if (content.includes(searchQuery)) {
                        foundResults = true;
                        highlightSearchResults(section, searchQuery);
                    }
                });
                
                if (foundResults) {
                    showToast(`Found results for "${query}"`, 'info');
                } else {
                    showToast(`No results found for "${query}"`, 'warning');
                }
            }
            
            function highlightSearchResults(section, query) {
                // This is a simplified search highlighting
                // In a real implementation, you'd want more sophisticated text highlighting
                console.log(`Search results found in section: ${section.id} for query: ${query}`);
            }
            
            function clearSearchHighlights() {
                // Clear any search highlights
                console.log('Clearing search highlights');
            }
            
            function handleInitialHash() {
                const hash = window.location.hash.substring(1);
                if (hash && document.getElementById(hash)) {
                    switchToSection(hash);
                }
            }
            
            function handleHashChange() {
                const hash = window.location.hash.substring(1);
                if (hash && document.getElementById(hash)) {
                    switchToSection(hash);
                }
            }
            
            function handleResize() {
                const newIsMobile = window.innerWidth < 1024;
                
                // Close mobile menu if switching to desktop
                if (!newIsMobile && elements.sidebar.classList.contains('active')) {
                    closeMobileMenu();
                }
            }
            
            function triggerHapticFeedback(type = 'light') {
                if (!('vibrate' in navigator) || window.innerWidth > 767) return;
                
                const patterns = {
                    light: [10],
                    medium: [20],
                    heavy: [30],
                    success: [10, 50, 10],
                    error: [50, 50, 50]
                };
                
                try {
                    navigator.vibrate(patterns[type] || patterns.light);
                } catch (e) {
                    console.log('Haptic feedback not supported');
                }
            }
            
            function showToast(message, type = 'info') {
                // Create toast notification
                const toast = document.createElement('div');
                toast.className = `settings-toast ${type}`;
                toast.textContent = message;
                
                // Add to DOM
                document.body.appendChild(toast);
                
                // Show toast
                setTimeout(() => toast.classList.add('show'), 100);
                
                // Hide and remove toast
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }
            
            // Add CSS for toast notifications
            const toastStyle = document.createElement('style');
            toastStyle.textContent = `
                .settings-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: var(--settings-primary);
                    color: white;
                    padding: 12px 16px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    z-index: 10000;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                    font-size: 14px;
                    font-weight: 500;
                    max-width: 300px;
                }
                
                .settings-toast.show {
                    transform: translateX(0);
                }
                
                .settings-toast.error {
                    background: var(--settings-danger);
                }
                
                .settings-toast.warning {
                    background: var(--settings-warning);
                }
                
                .settings-toast.info {
                    background: var(--settings-info);
                }
                
                .settings-toast.success {
                    background: var(--settings-success);
                }
                
                @media (max-width: 767px) {
                    .settings-toast {
                        right: 16px;
                        left: 16px;
                        max-width: none;
                    }
                }
            `;
            document.head.appendChild(toastStyle);
        });
    </script>
</body>
</html>