// Jacks or Better Pro - Provably Fair Video Poker
document.addEventListener('DOMContentLoaded', function() {
    console.log("Video Poker initializing...");
    
    // Game elements
    const rulesBtn = document.getElementById('rulesBtn');
    const closeRulesBtn = document.getElementById('closeRulesBtn');
    const rulesModal = document.getElementById('rulesModal');
    
    const verifyBtn = document.getElementById('verifyBtn');
    const closeVerifyBtn = document.getElementById('closeVerifyBtn');
    const verifyModal = document.getElementById('verifyModal');
    
    // View mode elements
    const standardViewBtn = document.getElementById('standardViewBtn');
    const rushProViewBtn = document.getElementById('rushProViewBtn');
    const rushStatsBar = document.getElementById('rushStatsBar');
    const rushAnalyticsSection = document.getElementById('rushAnalyticsSection');
    const rushStrategySection = document.getElementById('rushStrategySection');
    const rushQuickActions = document.getElementById('rushQuickActions');
    
    // Mobile elements
    const mobileInfoToggleBtn = document.getElementById('mobileInfoToggleBtn');
    const mobilePaytableToggleBtn = document.getElementById('mobilePaytableToggleBtn');
    const infoPanel = document.querySelector('.info-panel');
    const paytableSection = document.getElementById('paytableSection');
    
    // Rush Pro stats elements
    const rushWinRate = document.getElementById('rushWinRate');
    const rushROI = document.getElementById('rushROI');
    const rushBestHand = document.getElementById('rushBestHand');
    const rushSessionTime = document.getElementById('rushSessionTime');
    const rushHandsPerMin = document.getElementById('rushHandsPerMin');
    
    // Rush analytics elements
    const sessionProfit = document.getElementById('sessionProfit');
    const handSpeed = document.getElementById('handSpeed');
    const optimalPlays = document.getElementById('optimalPlays');
    const playEfficiency = document.getElementById('playEfficiency');
    const expectedValue = document.getElementById('expectedValue');
    const sessionVariance = document.getElementById('sessionVariance');
    
    // Strategy elements
    const currentTip = document.getElementById('currentTip');
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    const suggestedPlay = document.getElementById('suggestedPlay');
    
    // Quick action buttons
    const quickDealBtn = document.getElementById('quickDealBtn');
    const maxSpeedBtn = document.getElementById('maxSpeedBtn');
    const optimalHoldBtn = document.getElementById('optimalHoldBtn');
    const turboModeBtn = document.getElementById('turboModeBtn');
    const autoPlayBtn = document.getElementById('autoPlayBtn');
    const quickBetBtn = document.getElementById('quickBetBtn');
    
    const creditsElement = document.getElementById('credits');
    const currentBetElement = document.getElementById('currentBet');
    const lastWinElement = document.getElementById('lastWin');
    
    const betOneBtn = document.getElementById('betOneBtn');
    const betMaxBtn = document.getElementById('betMaxBtn');
    
    const cards = [
        document.getElementById('card0'),
        document.getElementById('card1'),
        document.getElementById('card2'),
        document.getElementById('card3'),
        document.getElementById('card4')
    ];
    
    const holdBtns = [
        document.getElementById('hold0'),
        document.getElementById('hold1'),
        document.getElementById('hold2'),
        document.getElementById('hold3'),
        document.getElementById('hold4')
    ];
    
    const handResult = document.getElementById('handResult');
    const dealBtn = document.getElementById('dealBtn');
    const drawBtn = document.getElementById('drawBtn');
    
    const serverSeedHash = document.getElementById('serverSeedHash');
    const clientSeedInput = document.getElementById('clientSeedInput');
    const roundId = document.getElementById('roundId');
    const newSeedBtn = document.getElementById('newSeedBtn');
    
    const historyList = document.getElementById('historyList');
    
    const handsPlayed = document.getElementById('handsPlayed');
    const handsWon = document.getElementById('handsWon');
    const winRate = document.getElementById('winRate');
    const totalWon = document.getElementById('totalWon');
    const bestHand = document.getElementById('bestHand');
    
    const paytableRows = document.querySelectorAll('.pay-row');
    
    const verifyHandBtn = document.getElementById('verifyHandBtn');
    const verifyResult = document.getElementById('verifyResult');
    const currentHandInfo = document.getElementById('currentHandInfo');
    
    // Game state
    let gameState = {
        currentHand: [],
        heldCards: [false, false, false, false, false],
        isDealing: false,
        isDrawing: false,
        roundsPlayed: 0,
        roundsWon: 0,
        totalWinnings: 0,
        bestHandRank: null,
        serverSeed: null,
        clientSeed: generateRandomSeed(),
        revealedServerSeed: null,
        deckOrder: [],
        handHistory: [],
        
        // GA Currency system
        gaCurrency: 1000,
        currentBet: 50,
        lastWin: 0,
        gaMinBet: 50,
        gaMaxBet: 5000,
        
        // View mode and mobile state
        viewMode: 'standard', // 'standard' or 'rushPro'
        isMobileInfoVisible: false,
        isMobilePaytableVisible: false,
        
        // Rush Pro features
        session: {
            startTime: null,
            startBalance: 1000,
            handsPlayed: 0,
            totalBet: 0,
            totalWon: 0,
            optimalPlays: 0,
            efficiency: 100,
            maxWin: 0,
            maxLoss: 0,
            variance: 0,
            profitHistory: []
        },
        
        // Auto play settings
        autoPlay: {
            active: false,
            handsRemaining: 0,
            betAmount: 50,
            stopOnWin: false,
            stopOnLoss: false,
            winThreshold: 0,
            lossThreshold: 0
        },
        
        // Turbo mode
        turboMode: false,
        maxSpeed: false,
        
        // Strategy tracking
        strategy: {
            suggestions: 0,
            followed: 0,
            ignored: 0,
            currentSuggestion: null
        }
    };
    
    // Paytable (multipliers for GA currency)
    const paytable = {
        royalFlush: [250, 500, 750, 1000, 4000],
        straightFlush: [50, 100, 150, 200, 250],
        fourOfAKind: [25, 50, 75, 100, 125],
        fullHouse: [9, 18, 27, 36, 45],
        flush: [6, 12, 18, 24, 30],
        straight: [4, 8, 12, 16, 20],
        threeOfAKind: [3, 6, 9, 12, 15],
        twoPair: [2, 4, 6, 8, 10],
        jacksOrBetter: [1, 2, 3, 4, 5]
    };
    
    // Card definitions
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing game...");
        
        // Load saved view mode
        loadViewMode();
        
        // Generate initial server seed and hash
        generateNewServerSeed();
        
        // Set initial client seed
        clientSeedInput.value = gameState.clientSeed;
        
        // Initialize session
        initSession();
        
        // Set event listeners
        setupEventListeners();
        
        // Update UI
        updateUI();
        updateRushProStats();
    }
    
    function loadViewMode() {
        const savedViewMode = localStorage.getItem('videoPokerViewMode');
        if (savedViewMode) {
            gameState.viewMode = savedViewMode;
            updateViewMode(savedViewMode);
        }
    }
    
    function initSession() {
        gameState.session.startTime = new Date();
        gameState.session.startBalance = gameState.gaCurrency;
        startSessionTimer();
    }
    
    function startSessionTimer() {
        setInterval(updateSessionTime, 1000);
    }
    
    function updateSessionTime() {
        if (!gameState.session.startTime) return;
        
        const now = new Date();
        const diff = now - gameState.session.startTime;
        const minutes = Math.floor(diff / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        
        if (rushSessionTime) {
            rushSessionTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // Calculate hands per minute
        if (minutes > 0 && rushHandsPerMin) {
            const handsPerMinute = Math.round(gameState.session.handsPlayed / minutes);
            rushHandsPerMin.textContent = handsPerMinute;
            
            if (handSpeed) {
                handSpeed.textContent = `${handsPerMinute}/min`;
            }
        }
    }
    
    function setupEventListeners() {
        // View mode toggle
        standardViewBtn.addEventListener('click', () => setViewMode('standard'));
        rushProViewBtn.addEventListener('click', () => setViewMode('rushPro'));
        
        // Mobile toggles
        mobileInfoToggleBtn.addEventListener('click', toggleMobileInfo);
        mobilePaytableToggleBtn.addEventListener('click', toggleMobilePaytable);
        
        // Rush Pro quick actions
        if (quickDealBtn) quickDealBtn.addEventListener('click', quickDeal);
        if (maxSpeedBtn) maxSpeedBtn.addEventListener('click', toggleMaxSpeed);
        if (optimalHoldBtn) optimalHoldBtn.addEventListener('click', applyOptimalHold);
        if (turboModeBtn) turboModeBtn.addEventListener('click', toggleTurboMode);
        if (autoPlayBtn) autoPlayBtn.addEventListener('click', toggleAutoPlay);
        if (quickBetBtn) quickBetBtn.addEventListener('click', quickBet);
        
        // Standard event listeners
        rulesBtn.addEventListener('click', () => toggleModal(rulesModal, true));
        closeRulesBtn.addEventListener('click', () => toggleModal(rulesModal, false));
        
        verifyBtn.addEventListener('click', () => toggleModal(verifyModal, true));
        closeVerifyBtn.addEventListener('click', () => toggleModal(verifyModal, false));
        
        betOneBtn.addEventListener('click', increaseBet);
        betMaxBtn.addEventListener('click', maxBet);
        
        dealBtn.addEventListener('click', dealHand);
        drawBtn.addEventListener('click', drawCards);
        
        newSeedBtn.addEventListener('click', generateNewClientSeed);
        
        // Set up hold buttons and card click events
        for (let i = 0; i < 5; i++) {
            holdBtns[i].addEventListener('click', () => toggleHold(i));
            cards[i].addEventListener('click', () => toggleHold(i));
        }
        
        // Set up verification button
        verifyHandBtn.addEventListener('click', verifyHand);
    }
    
    function setViewMode(mode) {
        gameState.viewMode = mode;
        localStorage.setItem('videoPokerViewMode', mode);
        updateViewMode(mode);
    }
    
    function updateViewMode(mode) {
        // Update button states
        if (mode === 'standard') {
            standardViewBtn.classList.add('active');
            rushProViewBtn.classList.remove('active');
            document.body.classList.remove('rush-pro-view-active');
        } else {
            standardViewBtn.classList.remove('active');
            rushProViewBtn.classList.add('active');
            document.body.classList.add('rush-pro-view-active');
        }
        
        // Update Rush Pro stats if switching to Rush Pro view
        if (mode === 'rushPro') {
            updateRushProStats();
            updateStrategyDisplay();
        }
    }
    
    function toggleMobileInfo() {
        gameState.isMobileInfoVisible = !gameState.isMobileInfoVisible;
        
        if (gameState.isMobileInfoVisible) {
            infoPanel.classList.add('active');
            mobileInfoToggleBtn.classList.add('active');
            mobileInfoToggleBtn.innerHTML = '<i class="fas fa-times"></i> Hide Statistics';
        } else {
            infoPanel.classList.remove('active');
            mobileInfoToggleBtn.classList.remove('active');
            mobileInfoToggleBtn.innerHTML = '<i class="fas fa-chart-line"></i> Show Statistics';
        }
    }
    
    function toggleMobilePaytable() {
        gameState.isMobilePaytableVisible = !gameState.isMobilePaytableVisible;
        
        if (gameState.isMobilePaytableVisible) {
            paytableSection.classList.add('active');
            mobilePaytableToggleBtn.classList.add('active');
            mobilePaytableToggleBtn.innerHTML = '<i class="fas fa-times"></i> Hide Paytable';
        } else {
            paytableSection.classList.remove('active');
            mobilePaytableToggleBtn.classList.remove('active');
            mobilePaytableToggleBtn.innerHTML = '<i class="fas fa-table"></i> Show Paytable';
        }
    }
    
    function quickDeal() {
        if (!gameState.isDealing && !gameState.isDrawing) {
            dealHand();
        }
    }
    
    function toggleMaxSpeed() {
        gameState.maxSpeed = !gameState.maxSpeed;
        if (maxSpeedBtn) {
            if (gameState.maxSpeed) {
                maxSpeedBtn.classList.add('active');
                maxSpeedBtn.innerHTML = '<i class="fas fa-tachometer-alt"></i><span>Max Speed ON</span>';
            } else {
                maxSpeedBtn.classList.remove('active');
                maxSpeedBtn.innerHTML = '<i class="fas fa-tachometer-alt"></i><span>Max Speed</span>';
            }
        }
    }
    
    function applyOptimalHold() {
        if (gameState.currentHand.length === 5 && !dealBtn.classList.contains('hidden')) {
            const optimalHolds = calculateOptimalHold(gameState.currentHand);
            
            // Apply the optimal hold pattern
            for (let i = 0; i < 5; i++) {
                gameState.heldCards[i] = optimalHolds[i];
                if (optimalHolds[i]) {
                    holdBtns[i].classList.add('active');
                } else {
                    holdBtns[i].classList.remove('active');
                }
            }
            
            // Track that player used optimal suggestion
            gameState.strategy.suggestions++;
            gameState.strategy.followed++;
            updateRushProStats();
        }
    }
    
    function toggleTurboMode() {
        gameState.turboMode = !gameState.turboMode;
        if (turboModeBtn) {
            if (gameState.turboMode) {
                turboModeBtn.classList.add('active');
                turboModeBtn.innerHTML = '<i class="fas fa-rocket"></i><span>Turbo ON</span>';
            } else {
                turboModeBtn.classList.remove('active');
                turboModeBtn.innerHTML = '<i class="fas fa-rocket"></i><span>Turbo Mode</span>';
            }
        }
    }
    
    function toggleAutoPlay() {
        gameState.autoPlay.active = !gameState.autoPlay.active;
        
        if (gameState.autoPlay.active) {
            // Start auto play
            const handsToPlay = prompt("How many hands to play automatically?", "10");
            const hands = parseInt(handsToPlay);
            
            if (hands && hands > 0) {
                gameState.autoPlay.handsRemaining = hands;
                gameState.autoPlay.betAmount = gameState.currentBet;
                
                if (autoPlayBtn) {
                    autoPlayBtn.classList.add('active');
                    autoPlayBtn.innerHTML = '<i class="fas fa-stop"></i><span>STOP AUTO</span>';
                }
                
                // Start auto play sequence
                setTimeout(autoPlayNextHand, 1000);
            } else {
                gameState.autoPlay.active = false;
            }
        } else {
            // Stop auto play
            gameState.autoPlay.handsRemaining = 0;
            if (autoPlayBtn) {
                autoPlayBtn.classList.remove('active');
                autoPlayBtn.innerHTML = '<i class="fas fa-forward"></i><span>AUTO PLAY</span>';
            }
        }
    }
    
    function autoPlayNextHand() {
        if (!gameState.autoPlay.active || gameState.autoPlay.handsRemaining <= 0) {
            toggleAutoPlay();
            return;
        }
        
        if (!gameState.isDealing && !gameState.isDrawing) {
            // Set bet amount
            gameState.currentBet = gameState.autoPlay.betAmount;
            
            // Deal hand
            dealHand();
            
            // Wait for deal, then apply optimal hold and draw
            setTimeout(() => {
                if (gameState.currentHand.length === 5) {
                    applyOptimalHold();
                    
                    setTimeout(() => {
                        drawCards();
                        gameState.autoPlay.handsRemaining--;
                        
                        // Schedule next hand
                        const delay = gameState.turboMode ? 500 : 2000;
                        setTimeout(autoPlayNextHand, delay);
                    }, gameState.maxSpeed ? 300 : 1000);
                }
            }, gameState.maxSpeed ? 500 : 1500);
        }
    }
    
    function quickBet() {
        // Cycle through common bet amounts quickly
        const quickBets = [50, 100, 250, 500, 1000];
        const currentIndex = quickBets.indexOf(gameState.currentBet);
        const nextIndex = (currentIndex + 1) % quickBets.length;
        
        if (gameState.gaCurrency >= quickBets[nextIndex]) {
            gameState.currentBet = quickBets[nextIndex];
            updateUI();
        }
    }
    
    function calculateOptimalHold(hand) {
        // Simplified optimal hold strategy for Jacks or Better
        const holds = [false, false, false, false, false];
        
        // Convert hand to analysis format
        const ranks = hand.map(card => card.value);
        const suits = hand.map(card => card.suit);
        
        // Check for pat hands (keep everything)
        if (isRoyalFlush(hand) || isStraightFlush(hand) || isFourOfAKind(hand) || 
            isFullHouse(hand) || isFlush(hand) || isStraight(hand)) {
            return [true, true, true, true, true];
        }
        
        // Check for three of a kind
        const rankCounts = {};
        ranks.forEach((rank, index) => {
            if (!rankCounts[rank]) rankCounts[rank] = [];
            rankCounts[rank].push(index);
        });
        
        // Three of a kind - keep the three
        for (const rank in rankCounts) {
            if (rankCounts[rank].length === 3) {
                rankCounts[rank].forEach(index => holds[index] = true);
                return holds;
            }
        }
        
        // Check for pairs
        let highPairs = [];
        let lowPairs = [];
        
        for (const rank in rankCounts) {
            if (rankCounts[rank].length === 2) {
                if (['J', 'Q', 'K', 'A'].includes(rank)) {
                    highPairs.push(rankCounts[rank]);
                } else {
                    lowPairs.push(rankCounts[rank]);
                }
            }
        }
        
        // Two pair - keep both pairs
        if (highPairs.length + lowPairs.length === 2) {
            [...highPairs, ...lowPairs].forEach(pair => {
                pair.forEach(index => holds[index] = true);
            });
            return holds;
        }
        
        // High pair (Jacks or better) - keep the pair
        if (highPairs.length === 1) {
            highPairs[0].forEach(index => holds[index] = true);
            return holds;
        }
        
        // Check for four to a straight flush
        if (hasFourToStraightFlush(hand)) {
            const indices = getFourToStraightFlushIndices(hand);
            indices.forEach(index => holds[index] = true);
            return holds;
        }
        
        // Check for four to a flush
        const suitCounts = {};
        suits.forEach((suit, index) => {
            if (!suitCounts[suit]) suitCounts[suit] = [];
            suitCounts[suit].push(index);
        });
        
        for (const suit in suitCounts) {
            if (suitCounts[suit].length === 4) {
                suitCounts[suit].forEach(index => holds[index] = true);
                return holds;
            }
        }
        
        // Check for four to an outside straight
        if (hasFourToOutsideStraight(hand)) {
            const indices = getFourToOutsideStraightIndices(hand);
            indices.forEach(index => holds[index] = true);
            return holds;
        }
        
        // Low pair - keep the pair
        if (lowPairs.length === 1) {
            lowPairs[0].forEach(index => holds[index] = true);
            return holds;
        }
        
        // Check for three to a royal flush
        if (hasThreeToRoyalFlush(hand)) {
            const indices = getThreeToRoyalFlushIndices(hand);
            indices.forEach(index => holds[index] = true);
            return holds;
        }
        
        // Check for two high cards of the same suit
        const highCardsBySuit = {};
        hand.forEach((card, index) => {
            if (['J', 'Q', 'K', 'A'].includes(card.value)) {
                if (!highCardsBySuit[card.suit]) highCardsBySuit[card.suit] = [];
                highCardsBySuit[card.suit].push(index);
            }
        });
        
        for (const suit in highCardsBySuit) {
            if (highCardsBySuit[suit].length >= 2) {
                highCardsBySuit[suit].slice(0, 2).forEach(index => holds[index] = true);
                return holds;
            }
        }
        
        // Keep any high cards
        hand.forEach((card, index) => {
            if (['J', 'Q', 'K', 'A'].includes(card.value)) {
                holds[index] = true;
            }
        });
        
        return holds;
    }
    
    // Helper functions for optimal strategy
    function isRoyalFlush(hand) {
        return isFlush(hand) && hand.every(card => ['10', 'J', 'Q', 'K', 'A'].includes(card.value));
    }
    
    function isStraightFlush(hand) {
        return isFlush(hand) && isStraight(hand);
    }
    
    function isFourOfAKind(hand) {
        const rankCounts = {};
        hand.forEach(card => rankCounts[card.value] = (rankCounts[card.value] || 0) + 1);
        return Object.values(rankCounts).includes(4);
    }
    
    function isFullHouse(hand) {
        const rankCounts = {};
        hand.forEach(card => rankCounts[card.value] = (rankCounts[card.value] || 0) + 1);
        const counts = Object.values(rankCounts).sort();
        return counts.length === 2 && counts[0] === 2 && counts[1] === 3;
    }
    
    function isFlush(hand) {
        return hand.every(card => card.suit === hand[0].suit);
    }
    
    function isStraight(hand) {
        const ranks = hand.map(card => values.indexOf(card.value)).sort((a, b) => a - b);
        
        // Check for normal straight
        if (ranks[4] - ranks[0] === 4 && new Set(ranks).size === 5) {
            return true;
        }
        
        // Check for A-2-3-4-5 straight
        if (ranks.join(',') === '0,1,2,3,12') {
            return true;
        }
        
        return false;
    }
    
    function hasFourToStraightFlush(hand) {
        // Simplified check - would need more complex logic for real implementation
        return false;
    }
    
    function getFourToStraightFlushIndices(hand) {
        return [];
    }
    
    function hasFourToOutsideStraight(hand) {
        // Simplified check - would need more complex logic for real implementation
        return false;
    }
    
    function getFourToOutsideStraightIndices(hand) {
        return [];
    }
    
    function hasThreeToRoyalFlush(hand) {
        // Check for three cards to royal flush in same suit
        const royalCards = ['10', 'J', 'Q', 'K', 'A'];
        const suitGroups = {};
        
        hand.forEach((card, index) => {
            if (royalCards.includes(card.value)) {
                if (!suitGroups[card.suit]) suitGroups[card.suit] = [];
                suitGroups[card.suit].push(index);
            }
        });
        
        return Object.values(suitGroups).some(group => group.length >= 3);
    }
    
    function getThreeToRoyalFlushIndices(hand) {
        const royalCards = ['10', 'J', 'Q', 'K', 'A'];
        const suitGroups = {};
        
        hand.forEach((card, index) => {
            if (royalCards.includes(card.value)) {
                if (!suitGroups[card.suit]) suitGroups[card.suit] = [];
                suitGroups[card.suit].push(index);
            }
        });
        
        for (const suit in suitGroups) {
            if (suitGroups[suit].length >= 3) {
                return suitGroups[suit].slice(0, 3);
            }
        }
        
        return [];
    }
    
    function updateRushProStats() {
        if (gameState.viewMode !== 'rushPro') return;
        
        // Update top stats bar
        const winRateValue = gameState.session.handsPlayed > 0 ? 
            ((gameState.roundsWon / gameState.session.handsPlayed) * 100).toFixed(1) : '0.0';
        if (rushWinRate) rushWinRate.textContent = `${winRateValue}%`;
        
        const roi = gameState.session.startBalance > 0 ? 
            (((gameState.gaCurrency - gameState.session.startBalance) / gameState.session.startBalance) * 100).toFixed(1) : '0.0';
        if (rushROI) rushROI.textContent = `${roi}%`;
        
        if (rushBestHand) {
            rushBestHand.textContent = gameState.bestHandRank ? 
                getHandRankDisplay(gameState.bestHandRank) : 'None';
        }
        
        // Update analytics
        const profit = gameState.gaCurrency - gameState.session.startBalance;
        if (sessionProfit) {
            sessionProfit.textContent = profit >= 0 ? `+${profit} GA` : `${profit} GA`;
        }
        
        if (optimalPlays && gameState.strategy.suggestions > 0) {
            const optimalRate = ((gameState.strategy.followed / gameState.strategy.suggestions) * 100).toFixed(1);
            optimalPlays.textContent = `${optimalRate}%`;
        }
        
        if (playEfficiency) {
            playEfficiency.textContent = `${gameState.session.efficiency.toFixed(1)}%`;
        }
        
        if (expectedValue) {
            const ev = calculateExpectedValue();
            expectedValue.textContent = ev >= 0 ? `+${ev} GA` : `${ev} GA`;
        }
        
        if (sessionVariance) {
            sessionVariance.textContent = gameState.session.variance.toFixed(2);
        }
    }
    
    function calculateExpectedValue() {
        // Simplified EV calculation based on current bet and RTP
        const rtp = 0.9954; // 99.54% RTP for optimal play
        return Math.round(gameState.currentBet * (rtp - 1));
    }
    
    function updateStrategyDisplay() {
        if (gameState.viewMode !== 'rushPro' || !gameState.currentHand.length) return;
        
        // Update strategy tip
        const tip = generateStrategyTip(gameState.currentHand);
        if (currentTip) {
            currentTip.innerHTML = `<i class="fas fa-lightbulb"></i><span>${tip}</span>`;
        }
        
        // Update hand strength
        const strength = calculateHandStrength(gameState.currentHand);
        if (strengthFill) {
            strengthFill.style.width = `${strength}%`;
        }
        
        if (strengthText) {
            if (strength < 20) strengthText.textContent = 'Weak';
            else if (strength < 40) strengthText.textContent = 'Fair';
            else if (strength < 60) strengthText.textContent = 'Good';
            else if (strength < 80) strengthText.textContent = 'Strong';
            else strengthText.textContent = 'Excellent';
        }
        
        // Update suggested play
        if (gameState.currentHand.length === 5) {
            const optimalHolds = calculateOptimalHold(gameState.currentHand);
            const suggestion = formatOptimalHoldSuggestion(optimalHolds);
            if (suggestedPlay) {
                suggestedPlay.textContent = suggestion;
            }
        }
    }
    
    function generateStrategyTip(hand) {
        if (!hand || hand.length === 0) return "Deal a hand to get strategy tips";
        
        if (hand.length === 5) {
            const optimalHolds = calculateOptimalHold(hand);
            const holdCount = optimalHolds.filter(h => h).length;
            
            if (holdCount === 5) return "You have a winning hand! Keep all cards.";
            if (holdCount === 0) return "No cards worth keeping. Draw 5 new cards.";
            if (holdCount === 1) return "Keep your high card or draw to improve.";
            if (holdCount === 2) return "You have a pair or two suited high cards.";
            if (holdCount === 3) return "You have three of a kind or three to a royal.";
            if (holdCount === 4) return "You have four to a straight, flush, or straight flush.";
        }
        
        return "Select cards to hold and draw for the best hand.";
    }
    
    function calculateHandStrength(hand) {
        if (!hand || hand.length !== 5) return 0;
        
        // Simple hand strength calculation (0-100)
        if (isRoyalFlush(hand)) return 100;
        if (isStraightFlush(hand)) return 95;
        if (isFourOfAKind(hand)) return 90;
        if (isFullHouse(hand)) return 85;
        if (isFlush(hand)) return 75;
        if (isStraight(hand)) return 65;
        
        const rankCounts = {};
        hand.forEach(card => rankCounts[card.value] = (rankCounts[card.value] || 0) + 1);
        const counts = Object.values(rankCounts).sort((a, b) => b - a);
        
        if (counts[0] === 3) return 55; // Three of a kind
        if (counts[0] === 2 && counts[1] === 2) return 45; // Two pair
        if (counts[0] === 2) {
            const pairRank = Object.keys(rankCounts).find(rank => rankCounts[rank] === 2);
            if (['J', 'Q', 'K', 'A'].includes(pairRank)) return 35; // High pair
            return 20; // Low pair
        }
        
        // High cards
        const highCards = hand.filter(card => ['J', 'Q', 'K', 'A'].includes(card.value));
        return Math.min(highCards.length * 8, 30);
    }
    
    function formatOptimalHoldSuggestion(optimalHolds) {
        const positions = [];
        optimalHolds.forEach((hold, index) => {
            if (hold) positions.push(index + 1);
        });
        
        if (positions.length === 0) return "Draw all new cards";
        if (positions.length === 5) return "Hold all cards";
        return `Hold cards: ${positions.join(', ')}`;
    }
    
    function dealHand() {
        // Check if player has enough GA currency
        if (gameState.gaCurrency < gameState.currentBet) {
            alert("Not enough GA currency! Current bet: " + gameState.currentBet);
            return;
        }
        
        // Deduct bet from balance
        gameState.gaCurrency -= gameState.currentBet;
        gameState.session.totalBet += gameState.currentBet;
        gameState.session.handsPlayed++;
        
        // Reset state
        gameState.heldCards = [false, false, false, false, false];
        handResult.querySelector('.result-text').textContent = '';
        gameState.lastWin = 0;
        
        // Generate new server seed if it's a new game
        if (gameState.revealedServerSeed) {
            generateNewServerSeed();
        }
        
        // Generate deck order
        const clientSeed = clientSeedInput.value || gameState.clientSeed;
        gameState.clientSeed = clientSeed;
        gameState.roundsPlayed++;
        roundId.textContent = gameState.roundsPlayed;
        
        // Generate full deck order (provably fair)
        gameState.deckOrder = generateShuffledDeck(gameState.serverSeed, clientSeed, gameState.roundsPlayed);
        
        // Deal initial 5 cards
        gameState.currentHand = gameState.deckOrder.slice(0, 5);
        
        // Show cards with animation
        const dealDelay = gameState.maxSpeed ? 50 : 200;
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                renderCard(i, gameState.currentHand[i]);
                
                // Enable hold buttons after all cards are dealt
                if (i === 4) {
                    enableHoldButtons();
                    
                    // Switch from DEAL to DRAW button
                    dealBtn.classList.add('hidden');
                    drawBtn.classList.remove('hidden');
                    
                    // Update strategy display for Rush Pro
                    if (gameState.viewMode === 'rushPro') {
                        updateStrategyDisplay();
                    }
                    
                    // Update the current hand info in verify modal
                    updateCurrentHandInfo();
                }
            }, i * dealDelay);
        }
        
        // Update UI
        updateUI();
        updateRushProStats();
    }
    
    function drawCards() {
        // Disable hold buttons during draw
        disableHoldButtons();
        
        // Get the held cards and positions for new cards
        const newCardPositions = [];
        
        for (let i = 0; i < 5; i++) {
            if (!gameState.heldCards[i]) {
                newCardPositions.push(i);
            }
        }
        
        // Draw new cards with animation
        let newCardIndex = 5; // Start after the initial 5 cards
        const drawDelay = gameState.maxSpeed ? 50 : 200;
        
        for (let i = 0; i < newCardPositions.length; i++) {
            const pos = newCardPositions[i];
            
            setTimeout(() => {
                const newCard = gameState.deckOrder[newCardIndex];
                gameState.currentHand[pos] = newCard;
                renderCard(pos, newCard);
                
                // After the last card is drawn, evaluate hand
                if (i === newCardPositions.length - 1) {
                    const evaluateDelay = gameState.turboMode ? 100 : 500;
                    setTimeout(() => {
                        evaluateHand();
                        
                        // Switch back to DEAL button
                        drawBtn.classList.add('hidden');
                        dealBtn.classList.remove('hidden');
                        
                        // Reveal server seed
                        revealServerSeed();
                        
                        // Update the current hand info in verify modal
                        updateCurrentHandInfo(true);
                        
                        // Update strategy display
                        if (gameState.viewMode === 'rushPro') {
                            updateStrategyDisplay();
                        }
                    }, evaluateDelay);
                }
                
                newCardIndex++;
            }, i * drawDelay);
        }
        
        // If all cards are held, just evaluate the hand
        if (newCardPositions.length === 0) {
            evaluateHand();
            
            // Switch back to DEAL button
            drawBtn.classList.add('hidden');
            dealBtn.classList.remove('hidden');
            
            // Reveal server seed
            revealServerSeed();
            
            // Update the current hand info in verify modal
            updateCurrentHandInfo(true);
            
            // Update strategy display
            if (gameState.viewMode === 'rushPro') {
                updateStrategyDisplay();
            }
        }
    }
    
    function evaluateHand() {
        // Sort hand for easier evaluation
        const sortedHand = [...gameState.currentHand].sort((a, b) => {
            return values.indexOf(a.value) - values.indexOf(b.value);
        });
        
        // Convert to ranks and suits arrays for easier checking
        const ranks = sortedHand.map(card => card.value);
        const suits = sortedHand.map(card => card.suit);
        
        // Check for flush (all same suit)
        const isFlush = suits.every(suit => suit === suits[0]);
        
        // Check for straight
        let isStraight = false;
        // Regular straight
        if (
            ranks.length === new Set(ranks).size && // All ranks are different
            values.indexOf(ranks[4]) - values.indexOf(ranks[0]) === 4
        ) {
            isStraight = true;
        }
        // Special case: A-2-3-4-5
        else if (
            new Set(ranks).size === 5 &&
            ranks.includes('A') &&
            ranks.includes('2') &&
            ranks.includes('3') &&
            ranks.includes('4') &&
            ranks.includes('5')
        ) {
            isStraight = true;
        }
        
        // Count frequencies of each rank
        const rankCounts = {};
        for (const rank of ranks) {
            rankCounts[rank] = (rankCounts[rank] || 0) + 1;
        }
        
        // Get pairs, three of a kind, four of a kind
        const pairs = Object.entries(rankCounts).filter(([rank, count]) => count === 2);
        const threeOfAKind = Object.entries(rankCounts).filter(([rank, count]) => count === 3);
        const fourOfAKind = Object.entries(rankCounts).filter(([rank, count]) => count === 4);
        
        // Determine hand rank
        let handRank = null;
        let multiplier = 0;
        
        // Royal Flush: A, K, Q, J, 10, all the same suit
        if (
            isFlush &&
            ranks.includes('10') &&
            ranks.includes('J') &&
            ranks.includes('Q') &&
            ranks.includes('K') &&
            ranks.includes('A')
        ) {
            handRank = 'royalFlush';
            multiplier = getPayoutMultiplier('royalFlush');
        }
        // Straight Flush: Five cards in sequence, all in the same suit
        else if (isFlush && isStraight) {
            handRank = 'straightFlush';
            multiplier = getPayoutMultiplier('straightFlush');
        }
        // Four of a Kind: Four cards of the same rank
        else if (fourOfAKind.length === 1) {
            handRank = 'fourOfAKind';
            multiplier = getPayoutMultiplier('fourOfAKind');
        }
        // Full House: Three of a kind and a pair
        else if (threeOfAKind.length === 1 && pairs.length === 1) {
            handRank = 'fullHouse';
            multiplier = getPayoutMultiplier('fullHouse');
        }
        // Flush: Five cards of the same suit, not in sequence
        else if (isFlush) {
            handRank = 'flush';
            multiplier = getPayoutMultiplier('flush');
        }
        // Straight: Five cards in a sequence, not of the same suit
        else if (isStraight) {
            handRank = 'straight';
            multiplier = getPayoutMultiplier('straight');
        }
        // Three of a Kind: Three cards of the same rank
        else if (threeOfAKind.length === 1) {
            handRank = 'threeOfAKind';
            multiplier = getPayoutMultiplier('threeOfAKind');
        }
        // Two Pair: Two different pairs
        else if (pairs.length === 2) {
            handRank = 'twoPair';
            multiplier = getPayoutMultiplier('twoPair');
        }
        // Jacks or Better: A pair of Jacks, Queens, Kings, or Aces
        else if (
            pairs.length === 1 &&
            ['J', 'Q', 'K', 'A'].includes(pairs[0][0])
        ) {
            handRank = 'jacksOrBetter';
            multiplier = getPayoutMultiplier('jacksOrBetter');
        }
        
        // Update game state and display result
        if (handRank) {
            // Calculate winnings
            const winnings = gameState.currentBet * multiplier;
            gameState.gaCurrency += winnings;
            gameState.lastWin = winnings;
            gameState.session.totalWon += winnings;
            
            // Update stats
            gameState.roundsWon++;
            
            // Update best hand if this is better
            if (!gameState.bestHandRank || getHandRankValue(handRank) > getHandRankValue(gameState.bestHandRank)) {
                gameState.bestHandRank = handRank;
            }
            
            // Track session stats
            if (winnings > gameState.session.maxWin) {
                gameState.session.maxWin = winnings;
            }
            
            // Display result
            const resultText = getHandRankDisplay(handRank);
            handResult.querySelector('.result-text').textContent = resultText + '! You win ' + winnings + ' GA';
            
            // Highlight winning cards
            highlightWinningCards(handRank, sortedHand);
            
            // Highlight winning paytable row
            highlightPaytableRow(handRank);
        } else {
            // Display result
            handResult.querySelector('.result-text').textContent = 'No Win!';
            
            // Track loss
            if (gameState.currentBet > gameState.session.maxLoss) {
                gameState.session.maxLoss = gameState.currentBet;
            }
        }
        
        // Add to history
        addToHistory(handRank, gameState.lastWin);
        
        // Update session profit history for variance calculation
        const currentProfit = gameState.gaCurrency - gameState.session.startBalance;
        gameState.session.profitHistory.push(currentProfit);
        if (gameState.session.profitHistory.length > 100) {
            gameState.session.profitHistory.shift();
        }
        
        // Calculate variance
        if (gameState.session.profitHistory.length > 1) {
            const mean = gameState.session.profitHistory.reduce((a, b) => a + b, 0) / gameState.session.profitHistory.length;
            const variance = gameState.session.profitHistory.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / gameState.session.profitHistory.length;
            gameState.session.variance = Math.sqrt(variance);
        }
        
        // Update UI
        updateUI();
        updateRushProStats();
    }
    
    function getPayoutMultiplier(handRank) {
        const betLevel = Math.min(5, Math.ceil(gameState.currentBet / 50));
        const levelIndex = betLevel - 1;
        return paytable[handRank] ? paytable[handRank][levelIndex] || paytable[handRank][0] : 0;
    }
    
    function highlightPaytableRow(handRank) {
        // Remove previous highlights
        document.querySelectorAll('.pay-row').forEach(row => row.classList.remove('winning'));
        
        // Highlight the winning row
        const winningRow = document.querySelector(`[data-hand="${handRank}"]`);
        if (winningRow) {
            winningRow.classList.add('winning');
            
            // Remove highlight after animation
            setTimeout(() => {
                winningRow.classList.remove('winning');
            }, 3000);
        }
    }
    
    function highlightWinningCards(handRank, sortedHand) {
        // First clear any highlights
        for (let i = 0; i < 5; i++) {
            cards[i].classList.remove('highlighted');
        }
        
        // Based on hand rank, highlight appropriate cards
        switch (handRank) {
            case 'royalFlush':
            case 'straightFlush':
            case 'flush':
            case 'straight':
                // Highlight all cards
                for (let i = 0; i < 5; i++) {
                    cards[i].classList.add('highlighted');
                }
                break;
                
            case 'fourOfAKind': {
                // Find the rank with 4 cards
                const ranks = sortedHand.map(card => card.value);
                const rankCounts = {};
                for (const rank of ranks) {
                    rankCounts[rank] = (rankCounts[rank] || 0) + 1;
                }
                const fourOfAKindRank = Object.entries(rankCounts)
                    .filter(([rank, count]) => count === 4)[0][0];
                
                // Highlight the 4 cards with that rank
                for (let i = 0; i < 5; i++) {
                    if (gameState.currentHand[i].value === fourOfAKindRank) {
                        cards[i].classList.add('highlighted');
                    }
                }
                break;
            }
                
            case 'fullHouse': {
                // Highlight all cards
                for (let i = 0; i < 5; i++) {
                    cards[i].classList.add('highlighted');
                }
                break;
            }
                
            case 'threeOfAKind': {
                // Find the rank with 3 cards
                const ranks = sortedHand.map(card => card.value);
                const rankCounts = {};
                for (const rank of ranks) {
                    rankCounts[rank] = (rankCounts[rank] || 0) + 1;
                }
                const threeOfAKindRank = Object.entries(rankCounts)
                    .filter(([rank, count]) => count === 3)[0][0];
                
                // Highlight the 3 cards with that rank
                for (let i = 0; i < 5; i++) {
                    if (gameState.currentHand[i].value === threeOfAKindRank) {
                        cards[i].classList.add('highlighted');
                    }
                }
                break;
            }
                
            case 'twoPair': {
                // Find the two ranks with 2 cards each
                const ranks = sortedHand.map(card => card.value);
                const rankCounts = {};
                for (const rank of ranks) {
                    rankCounts[rank] = (rankCounts[rank] || 0) + 1;
                }
                const pairRanks = Object.entries(rankCounts)
                    .filter(([rank, count]) => count === 2)
                    .map(([rank, count]) => rank);
                
                // Highlight the 4 cards in the two pairs
                for (let i = 0; i < 5; i++) {
                    if (pairRanks.includes(gameState.currentHand[i].value)) {
                        cards[i].classList.add('highlighted');
                    }
                }
                break;
            }
                
            case 'jacksOrBetter': {
                // Find the rank with 2 cards
                const ranks = sortedHand.map(card => card.value);
                const rankCounts = {};
                for (const rank of ranks) {
                    rankCounts[rank] = (rankCounts[rank] || 0) + 1;
                }
                const pairRank = Object.entries(rankCounts)
                    .filter(([rank, count]) => count === 2)[0][0];
                
                // Highlight the 2 cards with that rank
                for (let i = 0; i < 5; i++) {
                    if (gameState.currentHand[i].value === pairRank) {
                        cards[i].classList.add('highlighted');
                    }
                }
                break;
            }
        }
    }
    
    function getHandRankValue(handRank) {
        const ranks = [
            'jacksOrBetter',
            'twoPair',
            'threeOfAKind',
            'straight',
            'flush',
            'fullHouse',
            'fourOfAKind',
            'straightFlush',
            'royalFlush'
        ];
        
        return ranks.indexOf(handRank);
    }
    
    function getHandRankDisplay(handRank) {
        const displayNames = {
            'royalFlush': 'Royal Flush',
            'straightFlush': 'Straight Flush',
            'fourOfAKind': 'Four of a Kind',
            'fullHouse': 'Full House',
            'flush': 'Flush',
            'straight': 'Straight',
            'threeOfAKind': 'Three of a Kind',
            'twoPair': 'Two Pair',
            'jacksOrBetter': 'Jacks or Better'
        };
        
        return displayNames[handRank] || handRank;
    }
    
    function renderCard(index, card) {
        // Clear any existing content
        cards[index].innerHTML = '';
        cards[index].className = 'card';
        
        if (!card) {
            cards[index].classList.remove('dealt');
            return;
        }
        
        // Create card elements
        const cardInner = document.createElement('div');
        cardInner.className = 'card-inner';
        
        const topLeft = document.createElement('div');
        topLeft.className = 'card-top-left';
        
        const value = document.createElement('div');
        value.className = 'card-value';
        value.textContent = card.value;
        
        const suit = document.createElement('div');
        suit.className = 'card-suit';
        suit.textContent = card.suit;
        
        topLeft.appendChild(value);
        topLeft.appendChild(suit);
        
        const center = document.createElement('div');
        center.className = 'card-center';
        center.textContent = card.suit;
        
        const bottomRight = document.createElement('div');
        bottomRight.className = 'card-bottom-right';
        
        const valueBottom = document.createElement('div');
        valueBottom.className = 'card-value';
        valueBottom.textContent = card.value;
        
        const suitBottom = document.createElement('div');
        suitBottom.className = 'card-suit';
        suitBottom.textContent = card.suit;
        
        bottomRight.appendChild(valueBottom);
        bottomRight.appendChild(suitBottom);
        
        cardInner.appendChild(topLeft);
        cardInner.appendChild(center);
        cardInner.appendChild(bottomRight);
        
        cards[index].appendChild(cardInner);
        
        // Add color class
        if (card.suit === '♥' || card.suit === '♦') {
            cards[index].classList.add('red');
        } else {
            cards[index].classList.add('black');
        }
        
        // Add dealt class
        cards[index].classList.add('dealt');
        
        // Add animation
        if (!gameState.maxSpeed) {
            cards[index].style.animation = 'cardFlip 0.3s';
            setTimeout(() => {
                cards[index].style.animation = '';
            }, 300);
        }
    }
    
    function toggleHold(index) {
        // Only allow toggling during the hold phase
        if (!dealBtn.classList.contains('hidden')) {
            return;
        }
        
        gameState.heldCards[index] = !gameState.heldCards[index];
        
        if (gameState.heldCards[index]) {
            holdBtns[index].classList.add('active');
        } else {
            holdBtns[index].classList.remove('active');
        }
        
        // Update strategy display for Rush Pro
        if (gameState.viewMode === 'rushPro') {
            updateStrategyDisplay();
        }
    }
    
    function enableHoldButtons() {
        for (let i = 0; i < 5; i++) {
            holdBtns[i].classList.remove('hidden');
        }
    }
    
    function disableHoldButtons() {
        for (let i = 0; i < 5; i++) {
            holdBtns[i].classList.remove('active');
            holdBtns[i].classList.add('hidden');
        }
    }
    
    function increaseBet() {
        const betTiers = [50, 100, 250, 500, 1000];
        const currentIndex = betTiers.indexOf(gameState.currentBet);
        const nextIndex = (currentIndex + 1) % betTiers.length;
        
        if (gameState.gaCurrency >= betTiers[nextIndex]) {
            gameState.currentBet = betTiers[nextIndex];
            updateUI();
        } else {
            alert("Not enough GA Currency for this bet");
        }
    }
    
    function maxBet() {
        if (gameState.gaCurrency >= 1000) {
            gameState.currentBet = 1000;
            updateUI();
        } else {
            alert("Not enough GA Currency for maximum bet");
        }
    }
    
    function generateNewServerSeed() {
        gameState.serverSeed = generateRandomSeed();
        gameState.revealedServerSeed = null;
        const hash = sha256(gameState.serverSeed);
        serverSeedHash.textContent = hash;
    }
    
    function generateNewClientSeed() {
        gameState.clientSeed = generateRandomSeed();
        clientSeedInput.value = gameState.clientSeed;
    }
    
    function revealServerSeed() {
        gameState.revealedServerSeed = gameState.serverSeed;
    }
    
    function generateRandomSeed() {
        // Generate a random 32-character hex string
        let result = '';
        const characters = '0123456789abcdef';
        for (let i = 0; i < 32; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }
    
    function generateShuffledDeck(serverSeed, clientSeed, nonce) {
        // Create a standard deck
        const deck = [];
        for (const suit of suits) {
            for (const value of values) {
                deck.push({ suit, value });
            }
        }
        
        // Use the combined seeds to shuffle the deck
        const combinedSeed = `${serverSeed}-${clientSeed}-${nonce}`;
        const shuffledDeck = fisherYatesShuffle(deck, combinedSeed);
        
        return shuffledDeck;
    }
    
    function fisherYatesShuffle(array, seed) {
        // Create a copy of the array
        const result = [...array];
        
        // Use the seed to generate a deterministic shuffle
        let seedHash = sha256(seed);
        
        // Fisher-Yates shuffle algorithm
        for (let i = result.length - 1; i > 0; i--) {
            // Use the next 8 characters from the hash as a number
            const segment = seedHash.substr((i * 2) % (seedHash.length - 8), 8);
            const value = parseInt(segment, 16);
            
            // Deterministic random index based on the hash segment
            const j = value % (i + 1);
            
            // Swap elements
            [result[i], result[j]] = [result[j], result[i]];
            
            // If we're running out of hash characters, regenerate
            if (i % 16 === 0) {
                seedHash = sha256(seedHash);
            }
        }
        
        return result;
    }
    
    function addToHistory(handRank, winnings) {
        // Create history item data
        const historyItem = {
            roundId: gameState.roundsPlayed,
            initialHand: [...gameState.currentHand.slice(0, 5)],
            heldCards: [...gameState.heldCards],
            finalHand: [...gameState.currentHand],
            handRank: handRank,
            winnings: winnings,
            bet: gameState.currentBet,
            serverSeed: gameState.revealedServerSeed,
            clientSeed: gameState.clientSeed
        };
        
        // Add to game state history
        gameState.handHistory.unshift(historyItem);
        
        // Only keep last 10 hands
        if (gameState.handHistory.length > 10) {
            gameState.handHistory.pop();
        }
        
        // Update history display
        updateHistoryDisplay();
    }
    
    function updateHistoryDisplay() {
        // Clear existing history
        historyList.innerHTML = '';
        
        // If no history, show message
        if (gameState.handHistory.length === 0) {
            const noHistory = document.createElement('p');
            noHistory.className = 'no-history';
            noHistory.textContent = 'No hands played yet';
            historyList.appendChild(noHistory);
            return;
        }
        
        // Add each history item
        for (const item of gameState.handHistory) {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            const historyRound = document.createElement('div');
            historyRound.className = 'history-round';
            historyRound.innerHTML = `
                <span>Round #${item.roundId}</span>
                <span>Bet: ${item.bet} GA</span>
            `;
            
            const historyHand = document.createElement('div');
            historyHand.className = 'history-hand';
            
            // Create a simplified representation of the hand
            const handText = item.finalHand.map(card => `${card.value}${card.suit}`).join(' ');
            historyHand.textContent = handText;
            
            const historyResult = document.createElement('div');
            historyResult.className = 'history-result';
            
            if (item.handRank) {
                historyResult.innerHTML = `
                    <span>${getHandRankDisplay(item.handRank)}</span>
                    <span>+${item.winnings} GA</span>
                `;
            } else {
                historyResult.innerHTML = `
                    <span>No Win</span>
                    <span>+0 GA</span>
                `;
            }
            
            historyItem.appendChild(historyRound);
            historyItem.appendChild(historyHand);
            historyItem.appendChild(historyResult);
            
            historyList.appendChild(historyItem);
        }
    }
    
    function updateCurrentHandInfo(revealed = false) {
        if (!gameState.currentHand || gameState.currentHand.length === 0) {
            currentHandInfo.innerHTML = '<p>Deal a hand to see verification information</p>';
            return;
        }
        
        let html = '<h4>Current Hand</h4>';
        
        html += `<p><strong>Round ID:</strong> ${gameState.roundsPlayed}</p>`;
        html += `<p><strong>Client Seed:</strong> ${gameState.clientSeed}</p>`;
        html += `<p><strong>Bet:</strong> ${gameState.currentBet} GA</p>`;
        
        if (revealed && gameState.revealedServerSeed) {
            html += `<p><strong>Server Seed (Revealed):</strong> ${gameState.revealedServerSeed}</p>`;
            html += `<p><strong>Server Seed Hash:</strong> ${sha256(gameState.revealedServerSeed)}</p>`;
            
            // Show initial and final hand
            html += '<p><strong>Initial Hand:</strong> ';
            for (let i = 0; i < 5; i++) {
                html += `${gameState.currentHand[i].value}${gameState.currentHand[i].suit} `;
            }
            html += '</p>';
            
            html += '<p><strong>Held Cards:</strong> ';
            for (let i = 0; i < 5; i++) {
                if (gameState.heldCards[i]) {
                    html += `${i+1} `;
                }
            }
            html += '</p>';
            
            // Add a verification button
            html += `<button id="verifyCurrent" class="verify-btn">Verify This Hand</button>`;
            
            currentHandInfo.innerHTML = html;
            
            // Add event listener to the verification button
            document.getElementById('verifyCurrent').addEventListener('click', () => {
                document.getElementById('verifyServerSeed').value = gameState.revealedServerSeed;
                document.getElementById('verifyClientSeed').value = gameState.clientSeed;
                document.getElementById('verifyRoundId').value = gameState.roundsPlayed;
                verifyHand();
            });
        } else {
            html += `<p><strong>Server Seed Hash:</strong> ${serverSeedHash.textContent}</p>`;
            html += '<p>The server seed will be revealed after the hand is complete.</p>';
            
            currentHandInfo.innerHTML = html;
        }
    }
    
    function verifyHand() {
        const serverSeed = document.getElementById('verifyServerSeed').value;
        const clientSeed = document.getElementById('verifyClientSeed').value;
        const roundIdValue = document.getElementById('verifyRoundId').value;
        
        if (!serverSeed || !clientSeed || !roundIdValue) {
            verifyResult.innerHTML = '<p>Please fill in all fields to verify a hand.</p>';
            return;
        }
        
        // Generate the deck order
        const deckOrder = generateShuffledDeck(serverSeed, clientSeed, roundIdValue);
        
        // Format the output
        let result = '<h4>Verification Result</h4>';
        
        // Check hash
        const calculatedHash = sha256(serverSeed);
        result += `<p><strong>Server Seed:</strong> ${serverSeed}</p>`;
        result += `<p><strong>Calculated Hash:</strong> ${calculatedHash}</p>`;
        
        // First 5 cards (initial deal)
        result += '<p><strong>Initial Deal (First 5 Cards):</strong></p><ul>';
        for (let i = 0; i < 5; i++) {
            result += `<li>Card ${i+1}: ${deckOrder[i].value}${deckOrder[i].suit}</li>`;
        }
        result += '</ul>';
        
        // Get history item if available
        const historyItem = gameState.handHistory.find(item => item.roundId.toString() === roundIdValue);
        
        if (historyItem) {
            // Show held cards and draw cards
            result += '<p><strong>Held Cards:</strong> ';
            for (let i = 0; i < 5; i++) {
                if (historyItem.heldCards[i]) {
                    result += `${i+1} `;
                }
            }
            result += '</p>';
            
            // Calculate which cards were drawn
            let drawIndex = 5; // Start from the 6th card
            result += '<p><strong>Drawn Cards:</strong></p><ul>';
            for (let i = 0; i < 5; i++) {
                if (!historyItem.heldCards[i]) {
                    result += `<li>Position ${i+1}: Card ${drawIndex+1} in deck: ${deckOrder[drawIndex].value}${deckOrder[drawIndex].suit}</li>`;
                    drawIndex++;
                }
            }
            result += '</ul>';
            
            // Final hand
            result += '<p><strong>Final Hand:</strong> ';
            for (const card of historyItem.finalHand) {
                result += `${card.value}${card.suit} `;
            }
            result += '</p>';
            
            // Hand result
            if (historyItem.handRank) {
                result += `<p><strong>Hand Rank:</strong> ${getHandRankDisplay(historyItem.handRank)}</p>`;
                result += `<p><strong>Bet:</strong> ${historyItem.bet} GA</p>`;
                result += `<p><strong>Winnings:</strong> ${historyItem.winnings} GA</p>`;
            } else {
                result += '<p><strong>Result:</strong> No Win</p>';
            }
        } else {
            result += '<p>No history information available for this round ID.</p>';
        }
        
        verifyResult.innerHTML = result;
    }
    
    function updateUI() {
        // Update credits and bet displays
        creditsElement.textContent = gameState.gaCurrency;
        currentBetElement.textContent = gameState.currentBet;
        lastWinElement.textContent = gameState.lastWin;
        
        // Update statistics
        handsPlayed.textContent = gameState.roundsPlayed;
        handsWon.textContent = gameState.roundsWon;
        
        if (gameState.roundsPlayed > 0) {
            winRate.textContent = ((gameState.roundsWon / gameState.roundsPlayed) * 100).toFixed(1) + '%';
        } else {
            winRate.textContent = '0%';
        }
        
        totalWon.textContent = gameState.session.totalWon;
        
        if (gameState.bestHandRank) {
            bestHand.textContent = getHandRankDisplay(gameState.bestHandRank);
        } else {
            bestHand.textContent = 'None';
        }
        
        // Update paytable highlight
        updatePaytableHighlight();
    }
    
    function updatePaytableHighlight() {
        // Remove all active classes
        document.querySelectorAll('.pay-1, .pay-2, .pay-3, .pay-4, .pay-5').forEach(el => {
            el.classList.remove('active');
        });
        
        // Add active class to current bet column
        const betLevel = Math.min(5, Math.ceil(gameState.currentBet / 50));
        document.querySelectorAll(`.pay-${betLevel}`).forEach(el => {
            el.classList.add('active');
        });
    }
    
    function toggleModal(modal, show) {
        if (show) {
            modal.classList.remove('hidden');
        } else {
            modal.classList.add('hidden');
        }
    }
    
    // Simple SHA-256 implementation for browser
    function sha256(str) {
        // For real implementation, use a proper crypto library
        // This is a placeholder that simulates a hash
        let hash = 0;
        if (str.length === 0) return hash.toString(16).padStart(64, '0');
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        
        // In a real implementation, would return an actual SHA-256 hash
        // For this demo, we'll generate a 64-character hex string
        const randomPart = generateRandomSeed();
        return randomPart.substring(0, 32) + Math.abs(hash).toString(16).padStart(32, '0').substring(0, 32);
    }
});