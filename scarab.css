/* Scarab Spin - Ancient Egyptian Archaeology Game - Mobile Enhanced */

:root {
    --primary-dark: #271f16;
    --primary-medium: #533e2d;
    --primary-light: #7a5a40;
    --accent-gold: #d4af37;
    --accent-gold-light: #f8e9b7;
    --accent-gold-dark: #9c7b1d;
    --accent-teal: #10847e;
    --accent-red: #a63a3a;
    --text-light: #f8f5f0;
    --text-dark: #2c2316;
    --papyrus: #e8ddb5;
    --papyrus-dark: #d5c89b;
    --stone-light: #ccc5aa;
    --stone-medium: #9b957f;
    --stone-dark: #5c5a4c;
    --success-color: #4caf50;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --border-radius: 8px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
    
    /* Mobile responsive sizes */
    --mobile-padding: 10px;
    --mobile-font-size: 14px;
    --mobile-button-height: 44px;
    --mobile-symbol-size: 24px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cinzel', serif;
    background-color: var(--primary-dark);
    color: var(--text-light);
    line-height: 1.6;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23271f16"/><path d="M0,20 L100,20 M0,40 L100,40 M0,60 L100,60 M0,80 L100,80" stroke="%23533e2d" stroke-width="0.5" opacity="0.2"/><path d="M20,0 L20,100 M40,0 L40,100 M60,0 L60,100 M80,0 L80,100" stroke="%23533e2d" stroke-width="0.5" opacity="0.2"/></svg>');
    overflow-x: hidden;
}

/* Pro View Mode Toggle */
.view-mode-toggle {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1001;
    background: linear-gradient(to bottom, var(--accent-gold-dark), #7e601b);
    border: none;
    border-radius: var(--border-radius);
    padding: 8px 12px;
    color: var(--text-light);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-mode-toggle:hover {
    background: linear-gradient(to bottom, var(--accent-gold), var(--accent-gold-dark));
}

.view-mode-toggle i {
    font-size: 14px;
}

/* Pro View Specific Styles */
body.pro-view-active .pro-only {
    display: block !important;
}

body:not(.pro-view-active) .pro-only {
    display: none !important;
}

body.pro-view-active .standard-only {
    display: none !important;
}

body:not(.pro-view-active) .standard-only {
    display: block !important;
}

/* Advanced Stats Panel (Pro View Only) */
.advanced-stats {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--accent-gold-dark);
    box-shadow: var(--box-shadow);
}

.advanced-stats h3 {
    color: var(--accent-gold);
    margin-bottom: 10px;
    font-size: 16px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-item {
    text-align: center;
    padding: 8px;
    background: rgba(0,0,0,0.2);
    border-radius: 4px;
}

.stat-value {
    display: block;
    font-size: 18px;
    font-weight: bold;
    color: var(--accent-gold-light);
}

.stat-label {
    font-size: 11px;
    opacity: 0.8;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header - Mobile First */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--primary-light);
    background: linear-gradient(0deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    flex-wrap: wrap;
    gap: 10px;
}

.header-left, .header-right {
    flex: 1;
    min-width: 120px;
}

.header-center {
    flex: 2;
    text-align: center;
    min-width: 200px;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: var(--mobile-font-size);
    transition: var(--transition);
    padding: 8px;
    border-radius: 4px;
}

.back-link i {
    margin-right: 8px;
}

.back-link:hover {
    color: var(--accent-gold);
    background: rgba(255,255,255,0.1);
}

.game-title {
    font-size: clamp(20px, 5vw, 32px);
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: clamp(12px, 3vw, 16px);
    font-style: italic;
    color: var(--text-light);
    opacity: 0.8;
}

.info-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--accent-gold-dark);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: var(--mobile-font-size);
    transition: var(--transition);
    min-height: var(--mobile-button-height);
}

.info-btn i {
    margin-right: 8px;
}

.info-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-gold);
}

/* Main Content - Mobile First */
.main-content {
    display: flex;
    flex: 1;
    gap: 15px;
}

/* Game Section - Mobile Optimized */
.game-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Archaeology Meter - Mobile Optimized */
.archaeology-meter {
    background: linear-gradient(to right, var(--primary-dark), var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    padding: 10px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--stone-medium);
}

.meter-label {
    font-size: var(--mobile-font-size);
    color: var(--accent-gold);
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.meter-label i {
    margin-right: 8px;
}

.meter-container {
    height: 20px;
    background: var(--stone-dark);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.meter-fill {
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, var(--accent-gold-dark), var(--accent-gold), var(--accent-gold-light));
    transition: width 0.5s ease;
}

.meter-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    color: var(--text-dark);
    font-weight: bold;
}

/* Reel Container - Mobile Enhanced */
.reel-container {
    position: relative;
    background: var(--stone-medium);
    border-radius: var(--border-radius);
    padding: 15px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    border: 1px solid var(--accent-gold-dark);
}

.sand-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.reels {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
    justify-content: center;
}

.reel {
    flex: 1;
    max-width: 80px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    background: var(--stone-light);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 3px 6px rgba(0,0,0,0.3);
}

.reel-header {
    padding: 5px;
    background: var(--stone-dark);
    display: flex;
    justify-content: center;
}

.hold-btn {
    background: none;
    border: 1px solid var(--text-light);
    color: var(--text-light);
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    min-height: 28px;
    white-space: nowrap;
}

.hold-btn i {
    font-size: 8px;
}

.hold-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.hold-btn.active {
    background: var(--accent-gold-dark);
    border-color: var(--accent-gold);
    color: var(--text-dark);
}

.symbol-container {
    aspect-ratio: 1/1;
    background: var(--papyrus);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    border: 2px solid var(--stone-dark);
    cursor: pointer;
    transition: var(--transition);
    min-height: 60px;
}

.symbol-container:hover {
    background: var(--papyrus-dark);
    transform: scale(1.05);
}

.symbol {
    font-size: clamp(24px, 6vw, 32px);
    transition: transform 0.3s ease;
    user-select: none;
}

.symbol.scarab {
    color: #5c8d34;
}

.symbol.hieroglyph {
    color: #8b4513;
}

.symbol.pyramid {
    color: #cd853f;
}

.symbol.ankh {
    color: #4169e1;
}

.symbol.sphinx {
    color: var(--accent-gold);
    text-shadow: 0 0 5px var(--accent-gold-dark);
    animation: glow 1.5s infinite alternate;
}

.symbol.spin {
    animation: spin 0.5s linear infinite;
}

@keyframes spin {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
}

@keyframes glow {
    0% { text-shadow: 0 0 5px var(--accent-gold-dark); }
    100% { text-shadow: 0 0 15px var(--accent-gold); }
}

/* Pattern Display - Mobile Enhanced */
.pattern-display {
    background: var(--stone-dark);
    padding: 10px;
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.pattern-info, .multiplier-info {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

.pattern-label, .multiplier-label {
    color: var(--text-light);
    font-size: var(--mobile-font-size);
    opacity: 0.8;
    display: block;
}

.pattern-list {
    color: var(--accent-gold);
    font-weight: bold;
    font-size: var(--mobile-font-size);
}

.multiplier-value {
    color: var(--accent-gold);
    font-size: 20px;
    font-weight: bold;
}

/* Controls - Mobile Enhanced */
.controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-row {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.spin-btn, .excavate-btn, .analyze-btn {
    flex: 1;
    min-width: 100px;
    min-height: var(--mobile-button-height);
    padding: 12px 8px;
    border-radius: var(--border-radius);
    font-size: var(--mobile-font-size);
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    color: var(--text-light);
    text-align: center;
}

.spin-btn {
    background: linear-gradient(to bottom, var(--accent-teal), #086963);
}

.excavate-btn {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
}

.analyze-btn {
    background: linear-gradient(to bottom, var(--accent-gold-dark), #7e601b);
}

.spin-btn:hover, .excavate-btn:hover, .analyze-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.spin-btn:active, .excavate-btn:active, .analyze-btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.spin-counter {
    text-align: center;
    font-size: var(--mobile-font-size);
    color: var(--text-light);
    opacity: 0.8;
    padding: 8px;
}

.separator {
    margin: 0 8px;
}

/* Probability Overlay */
.probability-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 8px;
    z-index: 3;
}

.heatmap-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-light);
    font-size: 12px;
}

.heatmap-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Info Panel - Mobile Enhanced */
.info-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: none;
}

.info-section {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--stone-medium);
}

.info-section h3 {
    background: var(--primary-dark);
    color: var(--accent-gold);
    padding: 10px 15px;
    font-size: 16px;
    border-bottom: 1px solid var(--stone-medium);
}

/* Probability Table - Mobile Enhanced */
.probability-table {
    padding: 10px;
}

.prob-row {
    display: grid;
    grid-template-columns: 30px 1fr auto auto;
    align-items: center;
    padding: 8px 5px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    gap: 8px;
}

.prob-row:last-child {
    border-bottom: none;
}

.prob-row.special {
    background-color: rgba(212, 175, 55, 0.1);
}

.symbol-icon {
    font-size: 18px;
    text-align: center;
}

.symbol-name {
    font-size: var(--mobile-font-size);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.probability, .base-value {
    font-size: 12px;
    text-align: center;
    color: var(--accent-gold-light);
    white-space: nowrap;
}

/* Pattern Guide - Mobile Enhanced */
.pattern-guide {
    padding: 10px;
}

.pattern-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 5px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    gap: 10px;
}

.pattern-item:last-child {
    border-bottom: none;
}

.pattern-type {
    font-size: var(--mobile-font-size);
    flex: 1;
}

.pattern-multi {
    font-size: var(--mobile-font-size);
    color: var(--accent-gold-light);
    font-weight: bold;
    white-space: nowrap;
}

/* Score Display - Mobile Enhanced */
.score-display {
    padding: 10px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 5px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    gap: 10px;
}

.score-item:last-child {
    border-bottom: none;
}

.score-label {
    font-size: var(--mobile-font-size);
    flex: 1;
}

.score-value {
    font-size: var(--mobile-font-size);
    color: var(--accent-gold-light);
    font-weight: bold;
    white-space: nowrap;
}

/* Puzzle Collection - Mobile Enhanced */
.puzzle-progress {
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.puzzle-pieces {
    display: flex;
    gap: 5px;
    justify-content: center;
    flex-wrap: wrap;
}

.puzzle-piece {
    width: 35px;
    height: 35px;
    background: var(--stone-dark);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    position: relative;
    border: 1px dashed var(--stone-medium);
    transition: var(--transition);
}

.puzzle-piece.collected {
    background: var(--stone-medium);
    border: 1px solid var(--accent-gold);
    color: var(--accent-gold);
    transform: scale(1.1);
}

.puzzle-piece.collected::after {
    content: '🧩';
    font-size: 20px;
}

.puzzle-status {
    font-size: var(--mobile-font-size);
    color: var(--text-light);
    text-align: center;
}

.solve-puzzle-btn {
    background: linear-gradient(to bottom, var(--accent-gold-dark), #7e601b);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    padding: 10px 12px;
    font-size: var(--mobile-font-size);
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
    min-height: var(--mobile-button-height);
}

.solve-puzzle-btn:hover:not(:disabled) {
    background: linear-gradient(to bottom, var(--accent-gold), var(--accent-gold-dark));
}

.solve-puzzle-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal Styling - Mobile Enhanced */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 10px;
}

.modal-content {
    background: var(--primary-medium);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--accent-gold-dark);
}

.modal-header {
    background: var(--primary-dark);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--accent-gold-dark);
    flex-wrap: wrap;
    gap: 10px;
}

.modal-header h2 {
    color: var(--accent-gold);
    font-size: clamp(16px, 4vw, 20px);
    flex: 1;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal-btn:hover {
    color: var(--accent-gold);
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

.tutorial-section {
    margin-bottom: 20px;
}

.tutorial-section h3 {
    color: var(--accent-gold);
    font-size: clamp(16px, 4vw, 18px);
    margin-bottom: 10px;
}

.tutorial-section p, .tutorial-section li {
    font-size: var(--mobile-font-size);
    margin-bottom: 5px;
    line-height: 1.5;
}

.tutorial-section ul, .tutorial-section ol {
    padding-left: 20px;
    margin-top: 5px;
}

.hidden {
    display: none;
}

/* Pro View Advanced Features */
.probability-heat {
    background: rgba(0,0,0,0.7);
    backdrop-filter: blur(2px);
}

.prob-tooltip {
    background: var(--primary-dark);
    border: 1px solid var(--accent-gold);
    border-radius: 4px;
    padding: 8px;
    font-size: 10px;
    max-width: 200px;
    pointer-events: none;
}

.prob-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
}

.prob-symbol {
    font-size: 12px;
}

.prob-percent {
    color: var(--accent-gold-light);
}

/* Performance Metrics (Pro View Only) */
.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.metric-card {
    background: rgba(0,0,0,0.3);
    padding: 10px;
    border-radius: 4px;
    text-align: center;
    border: 1px solid var(--stone-medium);
}

.metric-value {
    font-size: 16px;
    font-weight: bold;
    color: var(--accent-gold-light);
    display: block;
}

.metric-label {
    font-size: 10px;
    opacity: 0.8;
}

/* Responsive Breakpoints */

/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
    .game-container {
        padding: 8px;
    }
    
    .main-content {
        flex-direction: column;
        gap: 10px;
    }
    
    .info-panel {
        order: -1;
    }
    
    .reels {
        gap: 4px;
    }
    
    .reel {
        max-width: 60px;
    }
    
    .symbol {
        font-size: 20px;
    }
    
    .symbol-container {
        min-height: 50px;
    }
    
    .control-row {
        flex-direction: column;
    }
    
    .control-row button {
        min-width: 100%;
    }
    
    .pattern-display {
        flex-direction: column;
        text-align: center;
    }
    
    .pattern-info, .multiplier-info {
        width: 100%;
    }
    
    .header-center {
        order: -1;
        width: 100%;
        flex: 1 1 100%;
    }
    
    .game-title {
        font-size: 18px;
    }
    
    .game-subtitle {
        font-size: 12px;
    }
    
    .prob-row {
        grid-template-columns: 25px 1fr auto;
        gap: 5px;
    }
    
    .base-value {
        display: none;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Medium Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .reels {
        gap: 6px;
        justify-content: space-between;
    }
    
    .reel {
        max-width: 70px;
    }
    
    .symbol {
        font-size: 26px;
    }
    
    .control-row {
        justify-content: space-between;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .main-content {
        gap: 20px;
    }
    
    .info-panel {
        max-width: 300px;
    }
    
    .reels {
        gap: 10px;
    }
    
    .reel {
        max-width: 85px;
    }
    
    .symbol {
        font-size: 30px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Desktop (1025px+) */
@media (min-width: 1025px) {
    .game-container {
        padding: 20px;
    }
    
    .main-content {
        gap: 25px;
    }
    
    .info-panel {
        max-width: 350px;
    }
    
    .reels {
        gap: 12px;
    }
    
    .reel {
        max-width: 90px;
    }
    
    .symbol {
        font-size: 32px;
    }
    
    .symbol-container {
        min-height: 70px;
    }
    
    :root {
        --mobile-padding: 20px;
        --mobile-font-size: 16px;
        --mobile-button-height: 48px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Landscape Mobile Orientation */
@media (max-height: 500px) and (orientation: landscape) {
    .game-header {
        margin-bottom: 10px;
        padding-bottom: 8px;
    }
    
    .game-title {
        font-size: 16px;
    }
    
    .game-subtitle {
        display: none;
    }
    
    .main-content {
        flex-direction: row;
        gap: 15px;
    }
    
    .info-panel {
        max-width: 280px;
        order: 2;
    }
    
    .game-section {
        order: 1;
        gap: 10px;
    }
    
    .archaeology-meter {
        padding: 8px;
    }
    
    .meter-container {
        height: 15px;
    }
    
    .reel-container {
        padding: 10px;
    }
    
    .symbol {
        font-size: 20px;
    }
    
    .symbol-container {
        min-height: 45px;
    }
    
    .modal-content {
        max-height: 80vh;
    }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .symbol {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    .game-title {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Focus and Accessibility */
button:focus,
input:focus,
.symbol-container:focus {
    outline: 2px solid var(--accent-gold);
    outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .symbol.spin {
        animation: none;
    }
    
    .symbol.sphinx {
        animation: none;
    }
}

/* Touch Enhancements */
@media (hover: none) and (pointer: coarse) {
    .symbol-container:hover {
        transform: none;
        background: var(--papyrus);
    }
    
    .symbol-container:active {
        transform: scale(0.95);
        background: var(--papyrus-dark);
    }
    
    button:hover {
        transform: none;
    }
    
    button:active {
        transform: scale(0.95);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-light: #ffffff;
        --primary-dark: #1a1511;
        --primary-medium: #2d231a;
    }
}