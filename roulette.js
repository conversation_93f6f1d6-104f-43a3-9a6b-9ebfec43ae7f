// Roulette Game - Enhanced Mobile & Pro View Implementation
// Following YOUWARE platform standards

// Game State Management
let gameState = {
    // Player data
    balance: 10000,
    totalSpins: 0,
    totalWon: 0,
    biggestWin: 0,
    
    // Game mechanics
    selectedChipValue: 1,
    placedBets: {},
    totalBetAmount: 0,
    isSpinning: false,
    
    // Provably fair system
    clientSeed: '',
    serverSeed: '',
    hashedServerSeed: '',
    nonce: 0,
    
    // Game history
    lastNumbers: [],
    spinHistory: [],
    
    // UI state
    viewMode: 'standard', // 'standard' or 'pro'
    
    // Pro view analytics
    numberFrequency: {},
    hotNumbers: [],
    coldNumbers: [],
    streaks: {
        red: 0,
        black: 0,
        odd: 0,
        even: 0,
        currentRedStreak: 0,
        currentBlackStreak: 0,
        maxRedStreak: 0,
        maxBlackStreak: 0
    },
    sessionStats: {
        startTime: Date.now(),
        totalBets: 0,
        totalWins: 0,
        winRate: 0,
        avgBetSize: 0,
        avgWinSize: 0,
        profitLoss: 0
    }
};

// European Roulette Configuration
const ROULETTE_NUMBERS = [
    { number: 0, color: 'green' },
    { number: 32, color: 'red' }, { number: 15, color: 'black' }, { number: 19, color: 'red' }, { number: 4, color: 'black' },
    { number: 21, color: 'red' }, { number: 2, color: 'black' }, { number: 25, color: 'red' }, { number: 17, color: 'black' },
    { number: 34, color: 'red' }, { number: 6, color: 'black' }, { number: 27, color: 'red' }, { number: 13, color: 'black' },
    { number: 36, color: 'red' }, { number: 11, color: 'black' }, { number: 30, color: 'red' }, { number: 8, color: 'black' },
    { number: 23, color: 'red' }, { number: 10, color: 'black' }, { number: 5, color: 'red' }, { number: 24, color: 'black' },
    { number: 16, color: 'red' }, { number: 33, color: 'black' }, { number: 1, color: 'red' }, { number: 20, color: 'black' },
    { number: 14, color: 'red' }, { number: 31, color: 'black' }, { number: 9, color: 'red' }, { number: 22, color: 'black' },
    { number: 18, color: 'red' }, { number: 29, color: 'black' }, { number: 7, color: 'red' }, { number: 28, color: 'black' },
    { number: 12, color: 'red' }, { number: 35, color: 'black' }, { number: 3, color: 'red' }, { number: 26, color: 'black' }
];

// Bet Types and Payouts
const BET_TYPES = {
    'straight': { name: 'Straight Up', payout: 35, description: 'Bet on a single number' },
    'split': { name: 'Split', payout: 17, description: 'Bet on 2 adjacent numbers' },
    'street': { name: 'Street', payout: 11, description: 'Bet on 3 numbers in a row' },
    'corner': { name: 'Corner', payout: 8, description: 'Bet on 4 numbers in a square' },
    'line': { name: 'Six Line', payout: 5, description: 'Bet on 6 numbers (2 rows)' },
    'dozen': { name: 'Dozen', payout: 2, description: 'Bet on 12 numbers (1-12, 13-24, 25-36)' },
    'column': { name: 'Column', payout: 2, description: 'Bet on 12 numbers in a column' },
    'red': { name: 'Red', payout: 1, description: 'Bet on all red numbers' },
    'black': { name: 'Black', payout: 1, description: 'Bet on all black numbers' },
    'odd': { name: 'Odd', payout: 1, description: 'Bet on all odd numbers' },
    'even': { name: 'Even', payout: 1, description: 'Bet on all even numbers' },
    'low': { name: 'Low (1-18)', payout: 1, description: 'Bet on numbers 1-18' },
    'high': { name: 'High (19-36)', payout: 1, description: 'Bet on numbers 19-36' }
};

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeGame();
});

// Initialize game
function initializeGame() {
    loadGameState();
    createRouletteWheel();
    createBettingTable();
    createOutsideBets();
    createViewModeToggle();
    createProViewPanel();
    generateNewSeeds();
    attachEventListeners();
    updateDisplay();
    updateProViewStats();
    
    // Auto-save game state periodically
    setInterval(saveGameState, 5000);
}

// Create view mode toggle
function createViewModeToggle() {
    const toggle = document.createElement('div');
    toggle.className = 'view-mode-toggle';
    toggle.innerHTML = `
        <button class="view-toggle-btn ${gameState.viewMode === 'standard' ? 'active' : ''}" 
                data-mode="standard">STD</button>
        <button class="view-toggle-btn ${gameState.viewMode === 'pro' ? 'active' : ''}" 
                data-mode="pro">PRO</button>
    `;
    
    document.body.appendChild(toggle);
    
    // Add event listeners
    toggle.querySelectorAll('.view-toggle-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const mode = btn.getAttribute('data-mode');
            setViewMode(mode);
        });
    });
}

// Create Pro View panel
function createProViewPanel() {
    const proPanel = document.createElement('div');
    proPanel.className = 'pro-view-panel';
    proPanel.innerHTML = `
        <h3 class="panel-title">Pro Analytics</h3>
        
        <div class="pro-stats-grid">
            <div class="pro-stat-card">
                <div class="pro-stat-label">Win Rate</div>
                <div class="pro-stat-value" id="proWinRate">0%</div>
            </div>
            <div class="pro-stat-card">
                <div class="pro-stat-label">Avg Bet</div>
                <div class="pro-stat-value" id="proAvgBet">0 GA</div>
            </div>
            <div class="pro-stat-card">
                <div class="pro-stat-label">Profit/Loss</div>
                <div class="pro-stat-value" id="proProfitLoss">0 GA</div>
            </div>
            <div class="pro-stat-card">
                <div class="pro-stat-label">Session Time</div>
                <div class="pro-stat-value" id="proSessionTime">0m</div>
            </div>
        </div>
        
        <div class="hot-cold-numbers">
            <div class="hot-numbers">
                <h4><i class="fas fa-fire"></i> Hot Numbers</h4>
                <div class="number-frequency" id="hotNumbersList">
                    <!-- Hot numbers will be populated here -->
                </div>
            </div>
            <div class="cold-numbers">
                <h4><i class="fas fa-snowflake"></i> Cold Numbers</h4>
                <div class="number-frequency" id="coldNumbersList">
                    <!-- Cold numbers will be populated here -->
                </div>
            </div>
        </div>
        
        <div class="streak-info" style="margin-top: 1rem;">
            <h4 style="color: white; margin-bottom: 0.5rem;">Current Streaks</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.8rem;">
                <div>Red Streak: <span id="redStreak">0</span></div>
                <div>Black Streak: <span id="blackStreak">0</span></div>
                <div>Max Red: <span id="maxRedStreak">0</span></div>
                <div>Max Black: <span id="maxBlackStreak">0</span></div>
            </div>
        </div>
    `;
    
    // Insert before roulette area
    const rouletteArea = document.querySelector('.roulette-area');
    rouletteArea.parentNode.insertBefore(proPanel, rouletteArea);
}

// Set view mode
function setViewMode(mode) {
    gameState.viewMode = mode;
    localStorage.setItem('rouletteViewMode', mode);
    
    // Update toggle buttons
    document.querySelectorAll('.view-toggle-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-mode') === mode) {
            btn.classList.add('active');
        }
    });
    
    // Toggle body class for CSS styling
    if (mode === 'standard') {
        document.body.classList.remove('pro-view-active');
    } else {
        document.body.classList.add('pro-view-active');
        updateProViewStats();
    }
    
    showNotification(`Switched to ${mode.toUpperCase()} view`, 'info');
}

// Load game state from localStorage
function loadGameState() {
    const saved = localStorage.getItem('rouletteGameState');
    if (saved) {
        try {
            const parsed = JSON.parse(saved);
            gameState = { ...gameState, ...parsed };
        } catch (e) {
            console.warn('Failed to load game state:', e);
        }
    }
    
    // Load view mode
    const savedViewMode = localStorage.getItem('rouletteViewMode');
    if (savedViewMode) {
        gameState.viewMode = savedViewMode;
    }
    
    // Initialize number frequency if empty
    if (Object.keys(gameState.numberFrequency).length === 0) {
        for (let i = 0; i <= 36; i++) {
            gameState.numberFrequency[i] = 0;
        }
    }
}

// Save game state to localStorage
function saveGameState() {
    try {
        localStorage.setItem('rouletteGameState', JSON.stringify(gameState));
    } catch (e) {
        console.warn('Failed to save game state:', e);
    }
}

// Generate new cryptographic seeds
function generateNewSeeds() {
    gameState.clientSeed = generateRandomString(32);
    gameState.serverSeed = generateRandomString(32);
    gameState.hashedServerSeed = sha256(gameState.serverSeed);
    
    updateSeedDisplay();
}

// Generate random string for seeds
function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Simple SHA-256 implementation
function sha256(ascii) {
    function rightRotate(value, amount) {
        return (value >>> amount) | (value << (32 - amount));
    }
    
    let mathPow = Math.pow;
    let maxWord = mathPow(2, 32);
    let lengthProperty = 'length';
    let i, j;
    let result = '';

    let words = [];
    let asciiBitLength = ascii[lengthProperty] * 8;
    
    let hash = sha256.h = sha256.h || [];
    let k = sha256.k = sha256.k || [];
    let primeCounter = k[lengthProperty];

    let isComposite = {};
    for (let candidate = 2; primeCounter < 64; candidate++) {
        if (!isComposite[candidate]) {
            for (i = 0; i < 313; i += candidate) {
                isComposite[i] = candidate;
            }
            hash[primeCounter] = (mathPow(candidate, .5) * maxWord) | 0;
            k[primeCounter++] = (mathPow(candidate, 1/3) * maxWord) | 0;
        }
    }
    
    ascii += '\x80';
    while (ascii[lengthProperty] % 64 - 56) ascii += '\x00';
    for (i = 0; i < ascii[lengthProperty]; i++) {
        j = ascii.charCodeAt(i);
        if (j >> 8) return;
        words[i >> 2] |= j << ((3 - i) % 4) * 8;
    }
    words[words[lengthProperty]] = ((asciiBitLength / maxWord) | 0);
    words[words[lengthProperty]] = (asciiBitLength);
    
    for (j = 0; j < words[lengthProperty];) {
        let w = words.slice(j, j += 16);
        let oldHash = hash;
        hash = hash.slice(0, 8);
        
        for (i = 0; i < 64; i++) {
            let w15 = w[i - 15], w2 = w[i - 2];
            let a = hash[0], e = hash[4];
            let temp1 = hash[7]
                + (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25))
                + ((e & hash[5]) ^ ((~e) & hash[6]))
                + k[i]
                + (w[i] = (i < 16) ? w[i] : (
                    w[i - 16]
                    + (rightRotate(w15, 7) ^ rightRotate(w15, 18) ^ (w15 >>> 3))
                    + w[i - 7]
                    + (rightRotate(w2, 17) ^ rightRotate(w2, 19) ^ (w2 >>> 10))
                ) | 0
            );
            let temp2 = (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22))
                + ((a & hash[1]) ^ (a & hash[2]) ^ (hash[1] & hash[2]));
            
            hash = [(temp1 + temp2) | 0].concat(hash);
            hash[4] = (hash[4] + temp1) | 0;
        }
        
        for (i = 0; i < 8; i++) {
            hash[i] = (hash[i] + oldHash[i]) | 0;
        }
    }
    
    for (i = 0; i < 8; i++) {
        for (j = 3; j + 1; j--) {
            let b = (hash[i] >> (j * 8)) & 255;
            result += ((b < 16) ? 0 : '') + b.toString(16);
        }
    }
    return result;
}

// Create roulette wheel with numbers
function createRouletteWheel() {
    const wheel = document.getElementById('rouletteWheel');
    if (!wheel) return;
    
    // Clear existing numbers
    wheel.querySelectorAll('.wheel-number').forEach(el => el.remove());
    
    ROULETTE_NUMBERS.forEach((item, index) => {
        const segmentAngle = (360 / ROULETTE_NUMBERS.length) * index;
        const numberElement = document.createElement('div');
        numberElement.className = 'wheel-number';
        numberElement.style.transform = `rotate(${segmentAngle}deg)`;
        
        const numberContent = document.createElement('div');
        numberContent.className = `number-content ${item.color}`;
        numberContent.style.transform = `rotate(${90}deg)`;
        numberContent.textContent = item.number;
        numberContent.style.backgroundColor = 
            item.color === 'red' ? '#E63946' : 
            item.color === 'black' ? '#1D3557' : '#0B6E4F';
        
        numberElement.appendChild(numberContent);
        wheel.appendChild(numberElement);
    });
}

// Create betting table with numbers
function createBettingTable() {
    const grid = document.getElementById('bettingGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    // Create zero cell
    const zeroCell = document.createElement('div');
    zeroCell.className = 'number-cell green zero-cell';
    zeroCell.textContent = '0';
    zeroCell.setAttribute('data-number', '0');
    zeroCell.addEventListener('click', () => placeBet('straight', [0]));
    grid.appendChild(zeroCell);
    
    // Create number cells (1-36)
    for (let row = 0; row < 3; row++) {
        for (let col = 0; col < 12; col++) {
            const number = (col * 3) + (3 - row);
            if (number >= 1 && number <= 36) {
                const numberInfo = ROULETTE_NUMBERS.find(item => item.number === number);
                const cell = document.createElement('div');
                cell.className = `number-cell ${numberInfo.color}`;
                cell.textContent = number;
                cell.setAttribute('data-number', number);
                cell.addEventListener('click', () => placeBet('straight', [number]));
                grid.appendChild(cell);
            }
        }
    }
}

// Create outside bet options
function createOutsideBets() {
    const outsideBetsContainer = document.getElementById('outsideBets');
    if (!outsideBetsContainer) return;
    
    outsideBetsContainer.innerHTML = '';
    
    const bets = [
        { text: 'RED', type: 'red', style: 'background-color: rgba(230, 57, 70, 0.8);' },
        { text: 'BLACK', type: 'black', style: 'background-color: rgba(29, 53, 87, 0.8);' },
        { text: 'ODD', type: 'odd' },
        { text: 'EVEN', type: 'even' },
        { text: '1-18', type: 'low' },
        { text: '19-36', type: 'high' },
        { text: '1st 12', type: 'dozen', params: [1] },
        { text: '2nd 12', type: 'dozen', params: [2] },
        { text: '3rd 12', type: 'dozen', params: [3] },
        { text: '1st COL', type: 'column', params: [1] },
        { text: '2nd COL', type: 'column', params: [2] },
        { text: '3rd COL', type: 'column', params: [3] }
    ];
    
    bets.forEach(bet => {
        const betElement = document.createElement('div');
        betElement.className = 'outside-bet';
        betElement.textContent = bet.text;
        if (bet.style) {
            betElement.style.cssText = bet.style;
        }
        betElement.addEventListener('click', () => placeBet(bet.type, bet.params || []));
        outsideBetsContainer.appendChild(betElement);
    });
}

// Place a bet
function placeBet(betType, params) {
    if (gameState.isSpinning) {
        showNotification('Wheel is spinning, cannot place bets now', 'warning');
        return;
    }
    
    const chipValue = gameState.selectedChipValue;
    
    if (gameState.balance < chipValue) {
        showNotification('Insufficient balance', 'error');
        return;
    }
    
    let betKey = betType;
    if (params && params.length > 0) {
        betKey += ':' + params.join(',');
    }
    
    if (gameState.placedBets[betKey]) {
        gameState.placedBets[betKey].amount += chipValue;
    } else {
        const payoutMultiplier = BET_TYPES[betType].payout;
        gameState.placedBets[betKey] = {
            type: betType,
            params: params,
            amount: chipValue,
            payout: payoutMultiplier
        };
    }
    
    gameState.balance -= chipValue;
    gameState.totalBetAmount += chipValue;
    gameState.sessionStats.totalBets += chipValue;
    
    updateDisplay();
    renderPlacedBets();
    updateProViewStats();
    
    // Haptic feedback on mobile
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }
    
    showNotification(`Placed ${chipValue} on ${getBetDescription(betType, params)}`, 'success');
}

// Get human-readable bet description
function getBetDescription(betType, params) {
    switch(betType) {
        case 'straight':
            return `Number ${params[0]}`;
        case 'dozen':
            return `${params[0]}${getOrdinalSuffix(params[0])} Dozen`;
        case 'column':
            return `${params[0]}${getOrdinalSuffix(params[0])} Column`;
        case 'red':
            return 'Red';
        case 'black':
            return 'Black';
        case 'odd':
            return 'Odd';
        case 'even':
            return 'Even';
        case 'low':
            return 'Low (1-18)';
        case 'high':
            return 'High (19-36)';
        default:
            return betType;
    }
}

// Helper function to get ordinal suffix
function getOrdinalSuffix(num) {
    const j = num % 10,
          k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
}

// Render placed bets on the table
function renderPlacedBets() {
    document.querySelectorAll('.placed-chip').forEach(chip => chip.remove());
    
    for (const betKey in gameState.placedBets) {
        const bet = gameState.placedBets[betKey];
        let targetElement;
        
        if (bet.type === 'straight') {
            targetElement = document.querySelector(`.number-cell[data-number="${bet.params[0]}"]`);
        } else {
            const outsideBets = document.querySelectorAll('.outside-bet');
            for (const outsideBet of outsideBets) {
                const text = outsideBet.textContent.toLowerCase();
                if (
                    (bet.type === 'red' && text === 'red') ||
                    (bet.type === 'black' && text === 'black') ||
                    (bet.type === 'odd' && text === 'odd') ||
                    (bet.type === 'even' && text === 'even') ||
                    (bet.type === 'low' && text === '1-18') ||
                    (bet.type === 'high' && text === '19-36') ||
                    (bet.type === 'dozen' && text === `${bet.params[0]}${getOrdinalSuffix(bet.params[0])} 12`.toLowerCase()) ||
                    (bet.type === 'column' && text === `${bet.params[0]}${getOrdinalSuffix(bet.params[0])} col`.toLowerCase())
                ) {
                    targetElement = outsideBet;
                    break;
                }
            }
        }
        
        if (targetElement) {
            const chip = document.createElement('div');
            chip.className = `placed-chip chip-${getChipClassByValue(bet.amount)}`;
            chip.textContent = bet.amount;
            chip.style.background = getChipColorByValue(bet.amount);
            
            const rect = targetElement.getBoundingClientRect();
            const offsetX = (Math.random() - 0.5) * 10;
            const offsetY = (Math.random() - 0.5) * 10;
            
            chip.style.left = `calc(50% + ${offsetX}px)`;
            chip.style.top = `calc(50% + ${offsetY}px)`;
            chip.style.transform = 'translate(-50%, -50%)';
            
            targetElement.appendChild(chip);
        }
    }
}

// Helper functions for chip styling
function getChipClassByValue(value) {
    if (value >= 100) return '100';
    if (value >= 25) return '25';
    if (value >= 10) return '10';
    if (value >= 5) return '5';
    return '1';
}

function getChipColorByValue(value) {
    if (value >= 100) return 'radial-gradient(circle, #C88EA7, #6C5CE7)';
    if (value >= 25) return 'radial-gradient(circle, #F9C74F, #F8961E)';
    if (value >= 10) return 'radial-gradient(circle, #90BE6D, #0B6E4F)';
    if (value >= 5) return 'radial-gradient(circle, #F94144, #E63946)';
    return 'radial-gradient(circle, #A8DADC, #457B9D)';
}

// Clear all bets
function clearBets() {
    if (gameState.isSpinning) {
        showNotification('Cannot clear bets while wheel is spinning', 'warning');
        return;
    }
    
    let refundAmount = 0;
    for (const betKey in gameState.placedBets) {
        refundAmount += gameState.placedBets[betKey].amount;
    }
    
    gameState.balance += refundAmount;
    gameState.totalBetAmount = 0;
    gameState.placedBets = {};
    gameState.sessionStats.totalBets -= refundAmount;
    
    updateDisplay();
    renderPlacedBets();
    updateProViewStats();
    showNotification('All bets cleared', 'info');
}

// Spin the roulette wheel
function spinWheel() {
    if (gameState.isSpinning) return;
    
    if (Object.keys(gameState.placedBets).length === 0) {
        showNotification('Please place at least one bet', 'warning');
        return;
    }
    
    gameState.isSpinning = true;
    const spinBtn = document.getElementById('spinBtn');
    if (spinBtn) {
        spinBtn.disabled = true;
        spinBtn.textContent = 'SPINNING...';
    }
    
    document.getElementById('resultDisplay').innerHTML = 
        '<div class="result-text">🎰 Spinning...</div>';
    
    // Calculate result using provably fair algorithm
    const result = calculateProvablyFairResult();
    const winningNumber = result.number;
    const winningColor = result.color;
    
    // Animate wheel spin
    animateWheelSpin(winningNumber, () => {
        processSpinResult(winningNumber, winningColor);
        addToHistory(winningNumber, winningColor);
        updateAnalytics(winningNumber, winningColor);
        
        gameState.nonce++;
        generateNewSeeds();
        
        updateDisplay();
        updateLastNumbers();
        updateSpinHistory();
        updateProViewStats();
        
        gameState.isSpinning = false;
        if (spinBtn) {
            spinBtn.disabled = false;
            spinBtn.innerHTML = '<i class="fas fa-play"></i> SPIN';
        }
    });
}

// Calculate provably fair result
function calculateProvablyFairResult() {
    const combinedSeed = gameState.clientSeed + gameState.serverSeed + gameState.nonce;
    const hash = sha256(combinedSeed);
    
    const hexSubstring = hash.substring(0, 8);
    const decimal = parseInt(hexSubstring, 16);
    const resultIndex = decimal % 37;
    
    let numberInfo;
    if (resultIndex === 0) {
        numberInfo = { number: 0, color: 'green' };
    } else {
        numberInfo = ROULETTE_NUMBERS.find(item => item.number === resultIndex);
    }
    
    return {
        number: numberInfo.number,
        color: numberInfo.color,
        hash: hash,
        combinedSeed: combinedSeed
    };
}

// Animate wheel spin with mobile optimization
function animateWheelSpin(winningNumber, callback) {
    const wheel = document.getElementById('rouletteWheel');
    const ball = document.getElementById('wheelBall');
    
    if (!wheel || !ball) {
        callback();
        return;
    }
    
    const winningIndex = ROULETTE_NUMBERS.findIndex(item => item.number === winningNumber);
    const segmentAngle = 360 / ROULETTE_NUMBERS.length;
    const targetAngle = -(winningIndex * segmentAngle);
    const extraRotations = 5 * 360;
    const totalRotation = extraRotations + targetAngle;
    
    // Add spinning class for additional effects
    wheel.classList.add('spinning');
    
    wheel.style.transform = `rotate(${totalRotation}deg)`;
    
    setTimeout(() => {
        ball.style.transform = `rotate(${-totalRotation + 900}deg)`;
    }, 500);
    
    // Haptic feedback during spin
    if (navigator.vibrate) {
        navigator.vibrate([100, 50, 100, 50, 200]);
    }
    
    setTimeout(() => {
        wheel.classList.remove('spinning');
        callback();
    }, 5000);
}

// Process spin result
function processSpinResult(winningNumber, winningColor) {
    const winningBets = [];
    let totalWinAmount = 0;
    
    for (const betKey in gameState.placedBets) {
        const bet = gameState.placedBets[betKey];
        let isWin = false;
        
        switch (bet.type) {
            case 'straight':
                isWin = bet.params[0] === winningNumber;
                break;
            case 'red':
                isWin = winningColor === 'red';
                break;
            case 'black':
                isWin = winningColor === 'black';
                break;
            case 'odd':
                isWin = winningNumber > 0 && winningNumber % 2 === 1;
                break;
            case 'even':
                isWin = winningNumber > 0 && winningNumber % 2 === 0;
                break;
            case 'low':
                isWin = winningNumber >= 1 && winningNumber <= 18;
                break;
            case 'high':
                isWin = winningNumber >= 19 && winningNumber <= 36;
                break;
            case 'dozen':
                const dozenStart = (bet.params[0] - 1) * 12 + 1;
                const dozenEnd = bet.params[0] * 12;
                isWin = winningNumber >= dozenStart && winningNumber <= dozenEnd;
                break;
            case 'column':
                isWin = winningNumber > 0 && winningNumber % 3 === (bet.params[0] === 1 ? 1 : bet.params[0] === 2 ? 2 : 0);
                break;
        }
        
        if (isWin) {
            const winAmount = bet.amount + (bet.amount * bet.payout);
            winningBets.push({
                description: getBetDescription(bet.type, bet.params),
                amount: bet.amount,
                winAmount: winAmount
            });
            totalWinAmount += winAmount;
            bet.win = true;
            bet.winAmount = winAmount;
        } else {
            bet.win = false;
        }
    }
    
    gameState.balance += totalWinAmount;
    gameState.totalWon += totalWinAmount;
    gameState.sessionStats.totalWins += totalWinAmount;
    
    if (totalWinAmount > gameState.biggestWin) {
        gameState.biggestWin = totalWinAmount;
    }
    
    showSpinResult(winningNumber, winningColor, winningBets, totalWinAmount);
    
    gameState.placedBets = {};
    gameState.totalBetAmount = 0;
    renderPlacedBets();
}

// Show spin result with mobile-optimized display
function showSpinResult(number, color, winningBets, totalWinAmount) {
    const resultDisplay = document.getElementById('resultDisplay');
    if (!resultDisplay) return;
    
    const numberClass = color === 'red' ? 'var(--roulette-red)' : 
                       color === 'black' ? 'var(--roulette-black)' : 
                       'var(--roulette-green)';
    
    let resultHTML = `
        <div class="result-text">
            Ball landed on <span style="color: ${numberClass}; font-size: 1.4rem; font-weight: bold;">${number}</span>
        </div>
    `;
    
    if (winningBets.length > 0) {
        resultHTML += `<div style="margin-top: 0.5rem; color: var(--win-color); font-size: 0.9rem;">`;
        resultHTML += `<strong>🎉 You won ${totalWinAmount} GA!</strong><br>`;
        if (winningBets.length <= 3) {
            winningBets.forEach(bet => {
                resultHTML += `${bet.description}: ${bet.amount} → ${bet.winAmount} GA<br>`;
            });
        } else {
            resultHTML += `${winningBets.length} winning bets`;
        }
        resultHTML += `</div>`;
        
        showNotification(`🎉 You won ${totalWinAmount} GA!`, 'success');
        
        // Celebration haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate([200, 100, 200, 100, 300]);
        }
    } else {
        resultHTML += `<div style="margin-top: 0.5rem; color: var(--lose-color); font-size: 0.9rem;">No winning bets this time.</div>`;
        showNotification('No win this time. Try again!', 'info');
    }
    
    resultDisplay.innerHTML = resultHTML;
}

// Add spin to history and update analytics
function addToHistory(number, color) {
    gameState.lastNumbers.unshift({ number, color });
    if (gameState.lastNumbers.length > 10) {
        gameState.lastNumbers.pop();
    }
    
    const totalBetAmount = gameState.totalBetAmount;
    let winAmount = 0;
    Object.values(gameState.placedBets).forEach(bet => {
        if (bet.win) {
            winAmount += bet.winAmount;
        }
    });
    
    gameState.spinHistory.unshift({
        nonce: gameState.nonce,
        number: number,
        color: color,
        totalBetAmount: totalBetAmount,
        winAmount: winAmount,
        clientSeed: gameState.clientSeed,
        serverSeed: gameState.serverSeed,
        hashedServerSeed: gameState.hashedServerSeed,
        timestamp: new Date().toLocaleTimeString()
    });
    
    if (gameState.spinHistory.length > 20) {
        gameState.spinHistory.pop();
    }
    
    gameState.totalSpins++;
}

// Update analytics for pro view
function updateAnalytics(number, color) {
    // Update number frequency
    gameState.numberFrequency[number]++;
    
    // Update streaks
    if (color === 'red') {
        gameState.streaks.currentRedStreak++;
        gameState.streaks.currentBlackStreak = 0;
        if (gameState.streaks.currentRedStreak > gameState.streaks.maxRedStreak) {
            gameState.streaks.maxRedStreak = gameState.streaks.currentRedStreak;
        }
    } else if (color === 'black') {
        gameState.streaks.currentBlackStreak++;
        gameState.streaks.currentRedStreak = 0;
        if (gameState.streaks.currentBlackStreak > gameState.streaks.maxBlackStreak) {
            gameState.streaks.maxBlackStreak = gameState.streaks.currentBlackStreak;
        }
    } else {
        gameState.streaks.currentRedStreak = 0;
        gameState.streaks.currentBlackStreak = 0;
    }
    
    // Calculate hot and cold numbers
    const frequencies = Object.entries(gameState.numberFrequency)
        .map(([num, freq]) => ({ number: parseInt(num), frequency: freq }))
        .sort((a, b) => b.frequency - a.frequency);
    
    gameState.hotNumbers = frequencies.slice(0, 6);
    gameState.coldNumbers = frequencies.slice(-6).reverse();
}

// Update Pro View statistics
function updateProViewStats() {
    if (gameState.viewMode !== 'pro') return;
    
    // Calculate session stats
    const sessionTime = Math.floor((Date.now() - gameState.sessionStats.startTime) / 60000);
    gameState.sessionStats.winRate = gameState.totalSpins > 0 ? 
        ((gameState.sessionStats.totalWins / gameState.sessionStats.totalBets) * 100).toFixed(1) : 0;
    gameState.sessionStats.avgBetSize = gameState.totalSpins > 0 ? 
        Math.round(gameState.sessionStats.totalBets / gameState.totalSpins) : 0;
    gameState.sessionStats.profitLoss = gameState.balance - 10000; // Assuming starting balance is 10000
    
    // Update pro stats display
    const elements = {
        proWinRate: `${gameState.sessionStats.winRate}%`,
        proAvgBet: `${gameState.sessionStats.avgBetSize} GA`,
        proProfitLoss: `${gameState.sessionStats.profitLoss >= 0 ? '+' : ''}${gameState.sessionStats.profitLoss} GA`,
        proSessionTime: `${sessionTime}m`,
        redStreak: gameState.streaks.currentRedStreak,
        blackStreak: gameState.streaks.currentBlackStreak,
        maxRedStreak: gameState.streaks.maxRedStreak,
        maxBlackStreak: gameState.streaks.maxBlackStreak
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
            
            // Color profit/loss
            if (id === 'proProfitLoss') {
                element.style.color = gameState.sessionStats.profitLoss >= 0 ? 
                    'var(--win-color)' : 'var(--lose-color)';
            }
        }
    });
    
    // Update hot/cold numbers
    updateHotColdNumbers();
}

// Update hot and cold numbers display
function updateHotColdNumbers() {
    const hotContainer = document.getElementById('hotNumbersList');
    const coldContainer = document.getElementById('coldNumbersList');
    
    if (!hotContainer || !coldContainer) return;
    
    // Hot numbers
    hotContainer.innerHTML = '';
    gameState.hotNumbers.forEach(item => {
        if (item.frequency > 0) {
            const numberInfo = item.number === 0 ? 
                { color: 'green' } : 
                ROULETTE_NUMBERS.find(n => n.number === item.number);
            
            const element = document.createElement('div');
            element.className = `frequency-number ${numberInfo.color}`;
            element.style.backgroundColor = 
                numberInfo.color === 'red' ? '#E63946' : 
                numberInfo.color === 'black' ? '#1D3557' : '#0B6E4F';
            element.textContent = item.number;
            
            const count = document.createElement('div');
            count.className = 'frequency-count';
            count.textContent = item.frequency;
            element.appendChild(count);
            
            hotContainer.appendChild(element);
        }
    });
    
    // Cold numbers
    coldContainer.innerHTML = '';
    gameState.coldNumbers.forEach(item => {
        const numberInfo = item.number === 0 ? 
            { color: 'green' } : 
            ROULETTE_NUMBERS.find(n => n.number === item.number);
        
        const element = document.createElement('div');
        element.className = `frequency-number ${numberInfo.color}`;
        element.style.backgroundColor = 
            numberInfo.color === 'red' ? '#E63946' : 
            numberInfo.color === 'black' ? '#1D3557' : '#0B6E4F';
        element.textContent = item.number;
        
        const count = document.createElement('div');
        count.className = 'frequency-count';
        count.textContent = item.frequency;
        element.appendChild(count);
        
        coldContainer.appendChild(element);
    });
}

// Update last numbers display
function updateLastNumbers() {
    const container = document.getElementById('lastNumbers');
    if (!container) return;
    
    container.innerHTML = '';
    
    gameState.lastNumbers.forEach(item => {
        const numberElement = document.createElement('div');
        numberElement.className = `last-number ${item.color}`;
        numberElement.textContent = item.number;
        container.appendChild(numberElement);
    });
}

// Update spin history display
function updateSpinHistory() {
    const historyContainer = document.getElementById('spinHistory');
    if (!historyContainer) return;
    
    historyContainer.innerHTML = '';

    gameState.spinHistory.forEach(spin => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        
        const colorClass = spin.color === 'red' ? 'var(--roulette-red)' : 
                          spin.color === 'black' ? 'var(--roulette-black)' : 
                          'var(--roulette-green)';
        
        let betInfo = '';
        if (spin.totalBetAmount) {
            betInfo = `<div style="margin-bottom: 0.3rem; font-size: 0.8rem;">Bet: ${spin.totalBetAmount} GA`;
            if (spin.winAmount && spin.winAmount > 0) {
                betInfo += ` | Won: ${spin.winAmount} GA`;
            }
            betInfo += `</div>`;
        }
        
        historyItem.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                <strong>Spin #${spin.nonce}</strong>
                <span style="color: ${colorClass}; font-weight: bold;">${spin.number}</span>
            </div>
            <div style="font-size: 0.8rem; color: rgba(255,255,255,0.6);">
                <div style="margin-bottom: 0.3rem;">${spin.timestamp}</div>
                ${betInfo}
                <button class="rules-toggle" style="font-size: 0.8rem;" 
                        onclick="revealSeed(${spin.nonce})">
                    Show Server Seed
                </button>
                <div id="seed-${spin.nonce}" style="display: none; margin-top: 0.5rem; font-family: 'Courier New', monospace; font-size: 0.7rem; word-break: break-all;">
                    ${spin.serverSeed}
                </div>
            </div>
        `;
        historyContainer.appendChild(historyItem);
    });
}

// Update seed display
function updateSeedDisplay() {
    const elements = {
        clientSeed: gameState.clientSeed,
        hashedServerSeed: gameState.hashedServerSeed,
        nonce: gameState.nonce
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// Update all display elements
function updateDisplay() {
    const elements = {
        balance: `${gameState.balance.toLocaleString()} GA`,
        totalSpins: gameState.totalSpins,
        totalWon: `${gameState.totalWon.toLocaleString()} GA`,
        biggestWin: `${gameState.biggestWin.toLocaleString()} GA`
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
    
    updateSeedDisplay();
}

// Show notification with mobile optimization
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    if (!notification) return;
    
    notification.textContent = message;
    
    const colors = {
        error: 'linear-gradient(45deg, #F94144, #E63946)',
        success: 'linear-gradient(45deg, #90BE6D, #0B6E4F)',
        warning: 'linear-gradient(45deg, #F9C74F, #F8961E)',
        info: 'linear-gradient(45deg, #457B9D, #1D3557)'
    };
    
    notification.style.background = colors[type] || colors.info;
    notification.className = 'notification show';
    
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

// Attach event listeners
function attachEventListeners() {
    // Chip selector
    document.querySelectorAll('.chip').forEach(chip => {
        chip.addEventListener('click', function() {
            document.querySelectorAll('.chip').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            gameState.selectedChipValue = parseInt(this.getAttribute('data-value'));
            showNotification(`Selected ${gameState.selectedChipValue} chip`, 'info');
        });
    });
    
    // Action buttons
    const clearBtn = document.getElementById('clearBtn');
    if (clearBtn) {
        clearBtn.addEventListener('click', clearBets);
    }
    
    const spinBtn = document.getElementById('spinBtn');
    if (spinBtn) {
        spinBtn.addEventListener('click', spinWheel);
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.code === 'Space' && !gameState.isSpinning) {
            e.preventDefault();
            spinWheel();
        } else if (e.code === 'KeyC') {
            clearBets();
        } else if (e.code === 'KeyP') {
            setViewMode(gameState.viewMode === 'standard' ? 'pro' : 'standard');
        }
    });
    
    // Touch events for mobile optimization
    let touchStartTime = 0;
    
    document.addEventListener('touchstart', function(e) {
        touchStartTime = Date.now();
    });
    
    document.addEventListener('touchend', function(e) {
        const touchDuration = Date.now() - touchStartTime;
        
        // Long press on betting cells for quick bet
        if (touchDuration > 500) {
            const target = e.target.closest('.number-cell, .outside-bet');
            if (target && !gameState.isSpinning) {
                // Quick bet with current chip value
                const number = target.getAttribute('data-number');
                if (number) {
                    placeBet('straight', [parseInt(number)]);
                }
            }
        }
    });
    
    // Prevent double-tap zoom on buttons
    document.querySelectorAll('.chip, .action-btn, .number-cell, .outside-bet').forEach(element => {
        element.addEventListener('touchend', function(e) {
            e.preventDefault();
        });
    });
}

// Global functions for HTML onclick handlers
window.revealSeed = function(nonce) {
    const seedElement = document.getElementById(`seed-${nonce}`);
    if (seedElement) {
        seedElement.style.display = seedElement.style.display === 'none' ? 'block' : 'none';
    }
};

window.verifyResult = function() {
    const clientSeed = document.getElementById('verifyClientSeed')?.value;
    const serverSeed = document.getElementById('verifyServerSeed')?.value;
    const nonce = parseInt(document.getElementById('verifyNonce')?.value);

    if (!clientSeed || !serverSeed || isNaN(nonce)) {
        showNotification('Please fill in all verification fields', 'error');
        return;
    }

    const combinedSeed = clientSeed + serverSeed + nonce;
    const hash = sha256(combinedSeed);
    const hexSubstring = hash.substring(0, 8);
    const decimal = parseInt(hexSubstring, 16);
    const resultIndex = decimal % 37;
    
    let numberInfo;
    if (resultIndex === 0) {
        numberInfo = { number: 0, color: 'green' };
    } else {
        numberInfo = ROULETTE_NUMBERS.find(item => item.number === resultIndex);
    }

    const verificationResult = document.getElementById('verificationResult');
    if (verificationResult) {
        verificationResult.style.display = 'block';
        verificationResult.innerHTML = `
            <h5 style="color: var(--roulette-green); margin-bottom: 1rem;">✅ Verification Successful</h5>
            <p><strong>Result:</strong> <span style="color: ${numberInfo.color === 'red' ? 'var(--roulette-red)' : numberInfo.color === 'black' ? 'var(--roulette-black)' : 'var(--roulette-green)'}; font-weight: bold;">${numberInfo.number} (${numberInfo.color})</span></p>
            <p><strong>Hash:</strong> ${hash}</p>
            <p><strong>Decimal:</strong> ${decimal}</p>
        `;
    }
};

window.toggleRules = function() {
    const rulesContent = document.getElementById('rulesContent');
    if (rulesContent) {
        rulesContent.style.display = rulesContent.style.display === 'none' ? 'block' : 'none';
    }
};

// Export for potential use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { gameState, initializeGame };
}