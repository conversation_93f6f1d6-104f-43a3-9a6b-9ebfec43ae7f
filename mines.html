<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mines - GoldenAura</title>
    <link rel="stylesheet" href="../../../../assets/css/style.css">
    <link rel="stylesheet" href="assets/css/mines.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="mines-container">
            <!-- Game Title and Back Button -->
            <div class="mines-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">MINES</h1>
                <div class="view-toggle">
                    <button id="standardView" class="view-btn active">Standard</button>
                    <button id="proView" class="view-btn">Pro View</button>
                </div>
            </div>

            <!-- Mobile Wallet Display -->
            <div class="mobile-wallet">
                <div class="wallet-balance">
                    <i class="fas fa-wallet"></i>
                    <span id="mobileWalletValue">1000 GA</span>
                </div>
            </div>

            <!-- Game Dashboard and Board Container -->
            <div class="game-dashboard">
                <!-- Left Column: Game Controls -->
                <div class="dashboard-left">
                    <!-- Game Stats -->
                    <div class="game-stats">
                        <div class="stat-item wallet">
                            <span class="stat-label">Balance</span>
                            <span class="stat-value" id="walletValue">1000 GA</span>
                        </div>
                        <div class="stat-item multiplier">
                            <span class="stat-label">Multiplier</span>
                            <span class="stat-value" id="multiplierValue">1.0×</span>
                        </div>
                        <div class="stat-item payout">
                            <span class="stat-label">Potential Win</span>
                            <span class="stat-value" id="payoutValue">0 GA</span>
                        </div>
                    </div>
                    
                    <!-- Bet Controls -->
                    <div class="betting-controls">
                        <div class="bet-amount-control">
                            <label for="betAmount">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <button class="bet-btn" id="decreaseBet">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="betAmount" class="bet-input" value="10" min="10" step="10">
                                <button class="bet-btn" id="increaseBet">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Bet Presets -->
                        <div class="bet-presets">
                            <button class="preset-btn" data-amount="10">10</button>
                            <button class="preset-btn" data-amount="50">50</button>
                            <button class="preset-btn" data-amount="100">100</button>
                            <button class="preset-btn" data-amount="500">500</button>
                            <button class="preset-btn max" data-amount="max">MAX</button>
                        </div>
                    </div>
                    
                    <!-- Game Configuration -->
                    <div class="game-config">
                        <div class="config-group">
                            <label for="gridSize">Grid Size</label>
                            <select id="gridSize" class="config-select">
                                <option value="5">5×5</option>
                                <option value="8" selected>8×8</option>
                                <option value="10">10×10</option>
                            </select>
                        </div>
                        
                        <div class="config-group">
                            <label for="minesCount">Mines</label>
                            <div class="mines-count-control">
                                <button id="decreaseMines" class="mines-btn">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="minesCount" class="mines-input" value="12" min="1" max="24">
                                <button id="increaseMines" class="mines-btn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Mines Presets -->
                        <div class="mines-presets">
                            <button class="mines-preset-btn" data-mines="5">5</button>
                            <button class="mines-preset-btn" data-mines="10">10</button>
                            <button class="mines-preset-btn active" data-mines="12">12</button>
                            <button class="mines-preset-btn" data-mines="24">24</button>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="game-actions">
                        <div class="action-mode">
                            <button id="revealMode" class="mode-btn active">
                                <i class="fas fa-eye"></i> Reveal
                            </button>
                            <button id="flagMode" class="mode-btn">
                                <i class="fas fa-flag"></i> Flag
                            </button>
                        </div>
                        
                        <button id="startGame" class="btn btn-primary action-btn">Start Game</button>
                        <button id="cashoutBtn" class="btn btn-success action-btn" disabled>Cashout</button>
                        <button id="soundToggle" class="btn btn-icon">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Right Column: Game Board and Status -->
                <div class="dashboard-right">
                    <!-- Game Status -->
                    <div id="gameStatus" class="game-status">
                        <span>Select mines count and bet amount, then click Start Game!</span>
                    </div>
                    
                    <!-- Flags Counter -->
                    <div class="flags-container">
                        <div class="flags-counter">
                            <i class="fas fa-bomb"></i>
                            <span id="flagsValue">0/12</span>
                        </div>
                    </div>
                    
                    <!-- Game Board -->
                    <div id="gameBoard" class="game-board">
                        <!-- Grid will be generated here by JavaScript -->
                        <div class="loading-placeholder">
                            <i class="fas fa-dice fa-spin"></i>
                            <p>Preparing game board...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Game Controls (shows in mobile view) -->
            <div class="mobile-controls">
                <div class="mobile-actions">
                    <button id="mobileStartGame" class="btn btn-primary mobile-btn">Start Game</button>
                    <button id="mobileCashoutBtn" class="btn btn-success mobile-btn" disabled>Cashout</button>
                </div>
                <div class="mobile-mode-toggle">
                    <button id="mobileRevealMode" class="mode-btn active">Reveal</button>
                    <button id="mobileFlagMode" class="mode-btn">Flag</button>
                </div>
            </div>
            
            <!-- Game History Section -->
            <div class="game-history">
                <h2 class="history-title">
                    Game History
                    <button id="historyToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="history-content">
                    <div class="history-table-container">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Game</th>
                                    <th>Mines</th>
                                    <th>Bet</th>
                                    <th>Multiplier</th>
                                    <th>Profit</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- History entries will be added here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="assets/js/mines.js"></script>
</body>
</html>