/**
 * Main Sports Betting Application
 * 
 * This is the main application controller that connects:
 * - API Service
 * - Data Manager
 * - Bet Manager
 * - UI Components
 */
class SportsBettingApp {
    constructor(options = {}) {
        // Configuration
        this.config = {
            baseUrl: options.baseUrl || '/api',
            defaultSport: options.defaultSport || 'football',
            containerSelector: options.containerSelector || '.sports-betting-container',
            ...options
        };
        
        // Initialize components
        this.dataManager = new SportsBettingDataManager({
            baseUrl: this.config.baseUrl,
            defaultSport: this.config.defaultSport
        });
        
        this.betManager = new SportsBettingBetManager({
            baseUrl: this.config.baseUrl
        });
        
        // DOM elements
        this.elements = {
            container: document.querySelector(this.config.containerSelector),
            sportTabs: null,
            matchesList: null,
            selectedBets: null,
            betAmount: null,
            placeBetButton: null,
            payoutAmount: null,
            userBalance: null,
            betHistoryList: null
        };
        
        // State
        this.isInitialized = false;
        this.isLoading = false;
    }
    
    /**
     * Initialize the application
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            this.isLoading = true;
            this.updateUIState('loading');
            
            // Get DOM elements
            this.cacheElements();
            
            // Register event listeners
            this.registerEventListeners();
            
            // Initialize data manager
            const initialized = await this.dataManager.initialize();
            
            if (!initialized) {
                throw new Error('Failed to initialize data manager');
            }
            
            this.isInitialized = true;
            this.isLoading = false;
            this.updateUIState('ready');
            
            return true;
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.isLoading = false;
            this.updateUIState('error');
            return false;
        }
    }
    
    /**
     * Cache DOM elements
     */
    cacheElements() {
        const container = this.elements.container;
        
        if (!container) {
            throw new Error('Container element not found');
        }
        
        this.elements.sportTabs = container.querySelectorAll('.sport-tab');
        this.elements.matchesList = container.querySelector('#matches-list');
        this.elements.selectedBets = container.querySelector('#selected-bets');
        this.elements.betAmount = container.querySelector('#bet-amount');
        this.elements.placeBetButton = container.querySelector('#place-bet');
        this.elements.payoutAmount = container.querySelector('#payout-amount');
        this.elements.totalOdds = container.querySelector('#total-odds');
        this.elements.userBalance = container.querySelector('#user-balance');
        this.elements.betHistoryList = container.querySelector('#bet-history-list');
        this.elements.betTypeDescription = container.querySelector('#bet-type-description');
    }
    
    /**
     * Register event listeners
     */
    registerEventListeners() {
        // Data manager events
        this.dataManager.addEventListener('data-updated', event => {
            if (event.type === 'matches') {
                this.renderMatches(event.data);
            } else if (event.type === 'sports') {
                this.renderSportTabs(event.data);
            }
        });
        
        // Bet manager events
        this.betManager.addEventListener('balance-updated', event => {
            this.updateBalanceDisplay(event.newBalance);
        });
        
        this.betManager.addEventListener('bets-updated', event => {
            this.renderBetSlip(event.selections);
            this.updateTotalOdds();
            this.updatePotentialPayout();
            this.updatePlaceBetButton();
        });
        
        this.betManager.addEventListener('bet-placed', event => {
            this.renderBetHistory();
            // Show success notification
            alert('Bet placed successfully!');
        });
        
        this.betManager.addEventListener('bet-resolved', event => {
            this.renderBetHistory();
            // Show notification about the bet result
            alert(`Your bet has been resolved: ${event.bet.status.toUpperCase()}`);
        });
        
        this.betManager.addEventListener('bet-mode-changed', event => {
            this.updateBetTypeUI(event.mode);
            this.updateTotalOdds();
            this.updatePotentialPayout();
            this.updatePlaceBetButton();
        });
        
        this.betManager.addEventListener('error', event => {
            alert(`Error: ${event.message}`);
        });
        
        // UI events
        // Sport tabs
        this.elements.sportTabs.forEach(tab => {
            tab.addEventListener('click', e => {
                const sportId = e.target.dataset.sport;
                this.changeActiveSport(sportId);
            });
        });
        
        // Bet type tabs
        const betTypeTabs = document.querySelectorAll('.bet-type-tab');
        betTypeTabs.forEach(tab => {
            tab.addEventListener('click', e => {
                betTypeTabs.forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
                const betType = e.target.dataset.betType;
                this.betManager.setBetMode(betType);
            });
        });
        
        // Bet amount input
        this.elements.betAmount.addEventListener('input', () => {
            this.updatePotentialPayout();
            this.updatePlaceBetButton();
        });
        
        // Place bet button
        this.elements.placeBetButton.addEventListener('click', () => {
            this.placeBet();
        });
    }
    
    /**
     * Render sport tabs
     */
    renderSportTabs(sports) {
        // This implementation assumes static sport tabs in HTML
        // In a more dynamic app, you would generate these tabs based on the sports data
        
        // Find tab for current sport and activate it
        const currentSport = this.dataManager.currentSport;
        this.elements.sportTabs.forEach(tab => {
            if (tab.dataset.sport === currentSport) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });
    }
    
    /**
     * Render matches
     */
    renderMatches(matches) {
        const matchesList = this.elements.matchesList;
        
        if (!matchesList) return;
        
        // Clear current matches
        matchesList.innerHTML = '';
        
        if (!matches || matches.length === 0) {
            matchesList.innerHTML = '<p class="no-matches">No upcoming matches found</p>';
            return;
        }
        
        // Generate HTML for each match
        matches.forEach(match => {
            const matchCard = document.createElement('div');
            matchCard.className = 'match-card';
            
            // Format match time
            const matchTime = match.commenceTime.toLocaleString();
            
            // Check if draw option should be available (like in soccer)
            const hasDrawOption = match.odds.draw !== null;
            
            matchCard.innerHTML = `
                <div class="match-header">
                    <span class="match-time">${matchTime}</span>
                </div>
                <div class="teams">
                    <span class="team">${match.homeTeam}</span>
                    <span class="vs">VS</span>
                    <span class="team">${match.awayTeam}</span>
                </div>
                <div class="betting-options">
                    <div class="bet-option" 
                         data-match-id="${match.id}" 
                         data-bet-type="home" 
                         data-odds="${match.odds.home}">
                        ${match.homeTeam} Win<br>
                        <strong>${match.odds.home?.toFixed(2) || 'N/A'}</strong>
                    </div>
                    ${hasDrawOption ? `
                        <div class="bet-option" 
                             data-match-id="${match.id}" 
                             data-bet-type="draw" 
                             data-odds="${match.odds.draw}">
                            Draw<br>
                            <strong>${match.odds.draw?.toFixed(2) || 'N/A'}</strong>
                        </div>
                    ` : ''}
                    <div class="bet-option" 
                         data-match-id="${match.id}" 
                         data-bet-type="away" 
                         data-odds="${match.odds.away}">
                        ${match.awayTeam} Win<br>
                        <strong>${match.odds.away?.toFixed(2) || 'N/A'}</strong>
                    </div>
                </div>
            `;
            
            // Add to matches list
            matchesList.appendChild(matchCard);
        });
        
        // Add event listeners to betting options
        document.querySelectorAll('.bet-option').forEach(option => {
            option.addEventListener('click', e => {
                this.selectBet(e.target);
            });
        });
    }
    
    /**
     * Handle bet selection
     */
    selectBet(element) {
        const matchId = element.dataset.matchId;
        const betType = element.dataset.betType;
        const odds = parseFloat(element.dataset.odds);
        
        if (isNaN(odds) || !matchId || !betType) {
            console.error('Invalid bet data:', { matchId, betType, odds });
            return;
        }
        
        // Toggle selection UI
        element.classList.toggle('selected');
        
        // Find the match in current data
        const match = this.dataManager.getMatchById(matchId);
        
        if (!match) {
            console.error('Match not found:', matchId);
            return;
        }
        
        // Add or remove bet selection
        if (element.classList.contains('selected')) {
            this.betManager.addBetSelection(matchId, betType, odds, match);
        } else {
            this.betManager.removeBetSelection(matchId, betType);
        }
    }
    
    /**
     * Render bet slip
     */
    renderBetSlip(selections) {
        const betSlipContainer = this.elements.selectedBets;
        
        if (!betSlipContainer) return;
        
        if (!selections || selections.length === 0) {
            betSlipContainer.innerHTML = '<p class="empty-slip">No bets selected</p>';
            return;
        }
        
        const betMode = this.betManager.getBetMode();
        
        // Generate HTML for each selection
        betSlipContainer.innerHTML = selections.map((bet, index) => {
            // For single mode, highlight the first bet
            const isActive = betMode === 'single' && index === 0;
            const activeClass = isActive ? 'active-bet' : '';
            
            return `
                <div class="selected-bet ${activeClass}">
                    <div class="bet-header">
                        <span>${bet.description}</span>
                        <button class="remove-bet" data-index="${index}">×</button>
                    </div>
                    <div>Odds: <strong>${bet.odds.toFixed(2)}</strong></div>
                    ${betMode === 'single' && index === 0 ? '<div class="active-indicator">ACTIVE BET</div>' : ''}
                </div>
            `;
        }).join('');
        
        // Add event listeners for remove buttons
        betSlipContainer.querySelectorAll('.remove-bet').forEach(button => {
            button.addEventListener('click', e => {
                e.stopPropagation(); // Prevent event bubbling
                const index = parseInt(e.target.dataset.index);
                if (!isNaN(index) && index >= 0 && index < selections.length) {
                    const bet = selections[index];
                    this.betManager.removeBetSelection(bet.matchId, bet.betType);
                }
            });
        });
    }
    
    /**
     * Update bet type UI
     */
    updateBetTypeUI(mode) {
        const singleInfo = this.elements.betTypeDescription.querySelector('.bet-type-single');
        const parlayInfo = this.elements.betTypeDescription.querySelector('.bet-type-parlay');
        
        if (mode === 'single') {
            singleInfo.classList.remove('hidden');
            parlayInfo.classList.add('hidden');
        } else {
            singleInfo.classList.add('hidden');
            parlayInfo.classList.remove('hidden');
        }
    }
    
    /**
     * Update total odds display
     */
    updateTotalOdds() {
        const totalOdds = this.betManager.calculateTotalOdds();
        this.elements.totalOdds.textContent = totalOdds.toFixed(2);
    }
    
    /**
     * Update potential payout
     */
    updatePotentialPayout() {
        const betAmount = parseFloat(this.elements.betAmount.value) || 0;
        const payout = this.betManager.calculatePotentialPayout(betAmount);
        
        this.elements.payoutAmount.textContent = payout.toFixed(2);
    }
    
    /**
     * Update place bet button state
     */
    updatePlaceBetButton() {
        const button = this.elements.placeBetButton;
        const betAmount = parseFloat(this.elements.betAmount.value) || 0;
        const hasSelectedBets = this.betManager.getSelectedBets().length > 0;
        const hasSufficientBalance = betAmount <= this.betManager.getBalance();
        const betMode = this.betManager.getBetMode();
        
        // Additional check for parlay mode - need at least 2 bets
        const validForParlay = betMode !== 'parlay' || (betMode === 'parlay' && hasSelectedBets && this.betManager.getSelectedBets().length >= 2);
        
        button.disabled = !(hasSelectedBets && betAmount > 0 && hasSufficientBalance && validForParlay);
        
        // Update button text based on mode
        if (betMode === 'single') {
            button.textContent = hasSelectedBets && this.betManager.getSelectedBets().length > 1 
                ? `Place Bet (${this.betManager.getSelectedBets().length} remaining)` 
                : 'Place Bet';
        } else {
            button.textContent = 'Place Parlay Bet';
        }
    }
    
    /**
     * Place a bet
     */
    async placeBet() {
        const betAmount = parseFloat(this.elements.betAmount.value);
        
        try {
            // Show loading state
            this.elements.placeBetButton.disabled = true;
            this.elements.placeBetButton.textContent = 'Placing Bet...';
            
            // Place the bet
            const bet = await this.betManager.placeBet(betAmount);
            
            // Reset bet amount input
            this.elements.betAmount.value = '';
            this.elements.placeBetButton.textContent = 'Place Bet';
            
            // Reset loading state
            this.elements.placeBetButton.disabled = false;
            
        } catch (error) {
            // Reset loading state
            this.elements.placeBetButton.disabled = false;
            this.elements.placeBetButton.textContent = 'Place Bet';
            
            // Show error
            alert(`Failed to place bet: ${error.message}`);
        }
    }
    
    /**
     * Render bet history
     */
    async renderBetHistory() {
        const historyContainer = this.elements.betHistoryList;
        
        if (!historyContainer) return;
        
        try {
            // Get bet history
            const betHistory = await this.betManager.getBetHistory(5);
            
            if (!betHistory || betHistory.data.length === 0) {
                historyContainer.innerHTML = '<p style="color: #ccc; text-align: center;">No bets yet</p>';
                return;
            }
            
            // Generate HTML for each bet
            historyContainer.innerHTML = betHistory.data.map(bet => {
                const isParlay = bet.type === 'parlay' || bet.selections.length > 1;
                const betTypeLabel = isParlay ? `<span class="bet-type-label">PARLAY</span>` : '';
                
                return `
                    <div class="bet-history-item">
                        <div>
                            ${betTypeLabel}
                            <div>${bet.selections.map(b => b.description || b.team_name).join(', ')}</div>
                            <div style="font-size: 0.9rem; color: #ccc;">
                                $${bet.amount.toFixed(2)} → $${bet.potential_payout.toFixed(2)}
                            </div>
                        </div>
                        <span class="bet-status ${bet.status}">${bet.status.toUpperCase()}</span>
                    </div>
                `;
            }).join('');
        } catch (error) {
            console.error('Failed to render bet history:', error);
            historyContainer.innerHTML = '<p style="color: #ccc; text-align: center;">Failed to load bet history</p>';
        }
    }
    
    /**
     * Update balance display
     */
    updateBalanceDisplay(balance) {
        if (this.elements.userBalance) {
            this.elements.userBalance.textContent = balance.toFixed(2);
        }
    }
    
    /**
     * Change active sport
     */
    async changeActiveSport(sportId) {
        try {
            this.updateUIState('loading');
            
            // Update active tab
            this.elements.sportTabs.forEach(tab => {
                if (tab.dataset.sport === sportId) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });
            
            // Load matches for this sport
            await this.dataManager.changeSport(sportId);
            
            this.updateUIState('ready');
        } catch (error) {
            console.error(`Failed to change sport to ${sportId}:`, error);
            this.updateUIState('error');
        }
    }
    
    /**
     * Update UI state based on application state
     */
    updateUIState(state) {
        const container = this.elements.container;
        
        if (!container) return;
        
        // Remove previous states
        container.classList.remove('loading', 'error');
        
        // Add new state
        if (state === 'loading') {
            container.classList.add('loading');
            this.elements.matchesList.innerHTML = '<p class="loading-message">Loading...</p>';
        } else if (state === 'error') {
            container.classList.add('error');
            this.elements.matchesList.innerHTML = '<p class="error-message">Failed to load data. Please try again.</p>';
        }
        
        // Update initial display
        if (state === 'ready') {
            this.updateBalanceDisplay(this.betManager.getBalance());
            this.renderBetHistory();
        }
    }
}

// Initialize the app when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const config = window.SPORTS_BETTING_CONFIG;
    
    const app = new SportsBettingApp({
        baseUrl: config.api.baseUrl,
        defaultSport: config.sports.default,
        containerSelector: config.ui.containerSelector
    });
    
    app.initialize().then(initialized => {
        if (initialized) {
            console.log('Sports betting app initialized successfully');
        } else {
            console.error('Failed to initialize sports betting app');
        }
    });
    
    // Initialize sidebar functionality from main site
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
        });
        
        // Initial responsive check
        if (window.innerWidth >= 992) {
            sidebar.classList.add('open');
        }
    }
});