<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Balloon Pump - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/pump.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div id="headerWinLossIndicator" class="header-win-loss-indicator">
                    <!-- Will be filled by JS when win/loss occurs -->
                </div>
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="pump-container">
            <!-- Game Header -->
            <div class="game-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">BALLOON PUMP</h1>
                <div class="view-toggle">
                    <button id="standardView" class="view-btn active">Standard</button>
                    <button id="proView" class="view-btn">Pro View</button>
                </div>
            </div>

            <!-- Mobile Wallet Display -->
            <div class="mobile-wallet">
                <div class="wallet-balance">
                    <i class="fas fa-coins"></i>
                    <span id="mobileBalance">1000 GA</span>
                </div>
                <div class="wallet-multiplier">
                    <i class="fas fa-chart-line"></i>
                    <span id="mobileMultiplier">1.0×</span>
                </div>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <!-- Left Column: Stats & Controls -->
                <div class="dashboard-left">
                    <!-- Game Stats -->
                    <div class="game-stats">
                        <div class="stat-item">
                            <span class="stat-label">GA Balance</span>
                            <span class="stat-value" id="balanceValue">1000</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Current Bet (GA)</span>
                            <span class="stat-value" id="betValue">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Multiplier</span>
                            <span class="stat-value" id="multiplierValue">1.0×</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Potential Win (GA)</span>
                            <span class="stat-value" id="potentialWinValue">0</span>
                        </div>
                    </div>
                    
                    <!-- Bet Controls -->
                    <div class="bet-controls">
                        <div class="bet-input">
                            <label for="betInput">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <button class="bet-control-button" id="decreaseBet">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="betInput" min="10" max="1000" step="10" value="50" placeholder="Enter bet amount">
                                <button class="bet-control-button" id="increaseBet">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="bet-shortcuts">
                            <button class="bet-shortcut-btn" id="minBetBtn">Min</button>
                            <button class="bet-shortcut-btn" id="halfBetBtn">50%</button>
                            <button class="bet-shortcut-btn" id="doubleBetBtn">2×</button>
                            <button class="bet-shortcut-btn" id="maxBetBtn">Max</button>
                        </div>
                        <button class="place-bet-btn" id="placeBetBtn">
                            <i class="fas fa-play"></i> Place Bet
                        </button>
                    </div>
                    
                    <!-- Game History Section -->
                    <div class="history-section">
                        <h3 class="section-title">
                            <i class="fas fa-history"></i> Recent Activity
                            <button class="toggle-btn" id="historyToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </h3>
                        <div class="history-content" id="historyContent">
                            <div class="history-list" id="historyList">
                                <!-- Will be populated by JS -->
                                <div class="history-placeholder">No recent activity to show</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pro View - Risk Analysis -->
                    <div class="pro-view-section risk-analysis-section">
                        <h3 class="section-title">
                            <i class="fas fa-chart-pie"></i> Risk Analysis
                            <button class="toggle-btn" id="riskToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </h3>
                        <div class="risk-content" id="riskContent">
                            <div class="risk-zones">
                                <div class="risk-zone green">
                                    <div class="zone-header">
                                        <div class="zone-name">Green Zone</div>
                                        <div class="zone-multiplier">1.0× - 2.0×</div>
                                    </div>
                                    <div class="zone-stats">
                                        <div class="zone-stat">
                                            <div class="zone-stat-label">Burst Rate</div>
                                            <div class="zone-stat-value" id="greenBurstRate">0%</div>
                                        </div>
                                        <div class="zone-stat">
                                            <div class="zone-stat-label">Avg. Cashout</div>
                                            <div class="zone-stat-value" id="greenAvgCashout">0×</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="risk-zone yellow">
                                    <div class="zone-header">
                                        <div class="zone-name">Yellow Zone</div>
                                        <div class="zone-multiplier">2.1× - 5.0×</div>
                                    </div>
                                    <div class="zone-stats">
                                        <div class="zone-stat">
                                            <div class="zone-stat-label">Burst Rate</div>
                                            <div class="zone-stat-value" id="yellowBurstRate">0%</div>
                                        </div>
                                        <div class="zone-stat">
                                            <div class="zone-stat-label">Avg. Cashout</div>
                                            <div class="zone-stat-value" id="yellowAvgCashout">0×</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="risk-zone red">
                                    <div class="zone-header">
                                        <div class="zone-name">Red Zone</div>
                                        <div class="zone-multiplier">5.1× - 10.0×</div>
                                    </div>
                                    <div class="zone-stats">
                                        <div class="zone-stat">
                                            <div class="zone-stat-label">Burst Rate</div>
                                            <div class="zone-stat-value" id="redBurstRate">0%</div>
                                        </div>
                                        <div class="zone-stat">
                                            <div class="zone-stat-label">Avg. Cashout</div>
                                            <div class="zone-stat-value" id="redAvgCashout">0×</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="optimal-strategy">
                                <h4 class="strategy-title">Optimal Strategy</h4>
                                <div class="strategy-text" id="strategyText">
                                    Based on your play style, your optimal cashout point is <strong>2.5×</strong>.
                                    Cashouts above 3.5× have resulted in 85% burst rate.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column: Game Area -->
                <div class="dashboard-right">
                    <!-- Game Area -->
                    <div class="game-area">
                        <div class="game-background" id="gameBackground"></div>
                        
                        <div class="ghost-balloons" id="ghostBalloons">
                            <!-- Ghost balloons will be added here dynamically -->
                        </div>

                        <div class="balloon-container">
                            <div class="balloon" id="balloon">
                                <div class="balloon-face" id="balloonFace">😊</div>
                            </div>
                            <div class="balloon-string"></div>
                        </div>

                        <div class="progress-container">
                            <div class="progress-bar" id="progressBar"></div>
                            <div class="progress-markers">
                                <div class="marker green-yellow" data-label="2.0×"></div>
                                <div class="marker yellow-red" data-label="5.0×"></div>
                            </div>
                        </div>

                        <div class="congrats-banner" id="congratsBanner">
                            You're so close to a BIG WIN! Keep pumping!
                        </div>

                        <div class="game-controls">
                            <button class="pump-btn" id="pumpBtn" disabled>
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="cashout-btn" id="cashoutBtn" disabled>
                                <i class="fas fa-money-bill-wave"></i> Cash Out
                            </button>
                        </div>
                        
                        <!-- Mobile Pump Controls -->
                        <div class="mobile-pump-controls">
                            <button class="mobile-pump-btn" id="mobilePumpBtn" disabled>
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="mobile-cashout-btn" id="mobileCashoutBtn" disabled>
                                <i class="fas fa-money-bill-wave"></i> Cash Out
                            </button>
                        </div>
                    </div>

                    <div class="game-feedback" id="gameFeedback">
                        Place a bet to start pumping!
                    </div>
                </div>
            </div>
            
            <!-- Stats Panel -->
            <div class="stats-panel">
                <div class="stats-header">
                    <div class="stats-title">Your Statistics</div>
                </div>
                <div class="stats-grid">
                    <div class="stats-item">
                        <span class="stat-label">Total Games</span>
                        <span class="stat-value" id="totalGamesValue">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stat-label">Win Rate</span>
                        <span class="stat-value" id="winRateValue">0%</span>
                    </div>
                    <div class="stats-item">
                        <span class="stat-label">Biggest Win (GA)</span>
                        <span class="stat-value" id="biggestWinValue">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stat-label">Biggest Loss (GA)</span>
                        <span class="stat-value" id="biggestLossValue">0</span>
                    </div>
                    <div class="stats-item">
                        <span class="stat-label">Avg. Multiplier</span>
                        <span class="stat-value" id="avgMultiplierValue">0×</span>
                    </div>
                    <div class="stats-item">
                        <span class="stat-label">Net Profit</span>
                        <span class="stat-value" id="netProfitValue">0 GA</span>
                    </div>
                </div>
            </div>

            <!-- Pro View - Performance Analytics -->
            <div class="pro-view-section performance-section">
                <div class="performance-header">
                    <div class="performance-title">
                        <i class="fas fa-chart-line"></i> Performance Analytics
                    </div>
                </div>
                <div class="performance-content">
                    <div class="chart-container">
                        <canvas id="performanceChart"></canvas>
                    </div>
                    <div class="analytics-grid">
                        <div class="analytics-item">
                            <span class="analytics-label">Total Wagered</span>
                            <span class="analytics-value" id="totalWagered">0 GA</span>
                        </div>
                        <div class="analytics-item">
                            <span class="analytics-label">Total Cashouts</span>
                            <span class="analytics-value" id="totalCashouts">0</span>
                        </div>
                        <div class="analytics-item">
                            <span class="analytics-label">Total Bursts</span>
                            <span class="analytics-value" id="totalBursts">0</span>
                        </div>
                        <div class="analytics-item">
                            <span class="analytics-label">Average Bet</span>
                            <span class="analytics-value" id="avgBet">0 GA</span>
                        </div>
                        <div class="analytics-item">
                            <span class="analytics-label">ROI</span>
                            <span class="analytics-value" id="roiValue">0%</span>
                        </div>
                        <div class="analytics-item">
                            <span class="analytics-label">Pump Efficiency</span>
                            <span class="analytics-value" id="pumpEfficiency">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pro View - Burst Patterns -->
            <div class="pro-view-section burst-patterns-section">
                <div class="burst-patterns-header">
                    <div class="burst-patterns-title">
                        <i class="fas fa-bomb"></i> Burst Patterns
                    </div>
                </div>
                <div class="burst-patterns-content">
                    <div class="burst-heatmap">
                        <div class="heatmap-header">
                            <span>Burst Probability by Pump Count</span>
                        </div>
                        <div class="heatmap-grid" id="burstHeatmap">
                            <!-- Will be populated by JS -->
                        </div>
                    </div>
                    
                    <div class="optimal-cashout">
                        <h4>Optimal Cashout Points</h4>
                        <div class="cashout-recommendation">
                            <div class="recommendation-label">Conservative</div>
                            <div class="recommendation-value" id="conservativeRec">2.0×</div>
                        </div>
                        <div class="cashout-recommendation">
                            <div class="recommendation-label">Balanced</div>
                            <div class="recommendation-value" id="balancedRec">3.5×</div>
                        </div>
                        <div class="cashout-recommendation">
                            <div class="recommendation-label">Aggressive</div>
                            <div class="recommendation-value" id="aggressiveRec">5.0×</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loss Leaderboard -->
            <div class="loss-leaderboard">
                <div class="leaderboard-header">
                    <div class="leaderboard-title">
                        <i class="fas fa-trophy"></i> Biggest Losses Leaderboard (GA)
                    </div>
                </div>
                <ul class="leaderboard-list" id="lossLeaderboard">
                    <li class="leaderboard-item">
                        <span class="rank">1</span>
                        <span class="player">Player7463</span>
                        <span class="loss-amount">-4,562 GA</span>
                    </li>
                    <li class="leaderboard-item">
                        <span class="rank">2</span>
                        <span class="player">BigRisker</span>
                        <span class="loss-amount">-3,891 GA</span>
                    </li>
                    <li class="leaderboard-item">
                        <span class="rank">3</span>
                        <span class="player">LuckyCharm</span>
                        <span class="loss-amount">-2,750 GA</span>
                    </li>
                    <li class="leaderboard-item">
                        <span class="rank">4</span>
                        <span class="player">PumpMaster</span>
                        <span class="loss-amount">-2,347 GA</span>
                    </li>
                    <li class="leaderboard-item">
                        <span class="rank">5</span>
                        <span class="player">BalloonKing</span>
                        <span class="loss-amount">-1,985 GA</span>
                    </li>
                </ul>
                <div class="safety-message">
                    <i class="fas fa-info-circle"></i> 98% of players fail to win consistently. Know your limits.
                </div>
            </div>
            
            <!-- Mobile Stats Toggle -->
            <div class="mobile-stats-toggle">
                <button id="mobileStatsToggle" class="mobile-panel-toggle">
                    <i class="fas fa-chart-bar"></i>
                    <span>Show Stats</span>
                </button>
            </div>
            
            <!-- Mobile Stats Panel -->
            <div class="mobile-stats-panel" id="mobileStatsPanel">
                <div class="mobile-panel-header">
                    <h3>Game Statistics</h3>
                    <button id="closeStatsPanel" class="mobile-panel-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="mobile-panel-content">
                    <div class="mobile-stats-grid">
                        <div class="mobile-stat-card">
                            <div class="stat-label">Total Games</div>
                            <div class="stat-value" id="mobileTotalGames">0</div>
                        </div>
                        <div class="mobile-stat-card">
                            <div class="stat-label">Win Rate</div>
                            <div class="stat-value" id="mobileWinRate">0%</div>
                        </div>
                        <div class="mobile-stat-card">
                            <div class="stat-label">Best Win</div>
                            <div class="stat-value" id="mobileBestWin">0 GA</div>
                        </div>
                        <div class="mobile-stat-card">
                            <div class="stat-label">Net Profit</div>
                            <div class="stat-value" id="mobileNetProfit">0 GA</div>
                        </div>
                    </div>
                    
                    <h4 class="mobile-section-title">Recent Activity</h4>
                    <div class="mobile-history-list" id="mobileHistoryList">
                        <!-- Will be populated by JS -->
                    </div>
                    
                    <h4 class="mobile-section-title">Strategy Tips</h4>
                    <div class="mobile-strategy-tips" id="mobileStrategyTips">
                        <div class="mobile-tip">
                            <i class="fas fa-lightbulb"></i>
                            <span>Your ideal cashout point is around <strong id="mobileCashoutPoint">2.5×</strong></span>
                        </div>
                        <div class="mobile-tip">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Red zone has <strong id="mobileRedZoneRate">95%</strong> burst chance</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Burst Overlay -->
    <div class="burst-overlay" id="burstOverlay">
        <div class="burst-message">POP! Try Again?</div>
        <button class="try-again-btn" id="tryAgainBtn">
            <i class="fas fa-redo"></i> Try Again
        </button>
    </div>

    <!-- Special Item Popup -->
    <div class="overlay" id="popupOverlay"></div>
    <div class="popup" id="specialItemPopup">
        <div class="popup-title">Lucky Find!</div>
        <div class="popup-content" id="popupContent">
            You found a special item! This will add +3 pumps, but will double your loss penalty if the balloon bursts!
        </div>
        <div class="popup-buttons">
            <button class="popup-btn popup-accept" id="acceptSpecialBtn">Accept</button>
            <button class="popup-btn popup-reject" id="rejectSpecialBtn">Reject</button>
        </div>
    </div>

    <!-- Explosion Container for Particles -->
    <div class="explosion-container" id="explosionContainer"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/script.js"></script>
    <script src="assets/js/pump.js"></script>
</body>
</html>