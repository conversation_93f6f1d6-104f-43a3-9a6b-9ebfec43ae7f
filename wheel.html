<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cosmic Fortune - Provably Fair Wheel Game</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    <style>
        :root {
            --astro-purple: #6C5CE7;
            --astro-blue: #74B9FF;
            --astro-pink: #FD79A8;
            --astro-green: #00B894;
            --astro-orange: #FDCB6E;
            --astro-red: #E17055;
            --astro-dark: #2D3436;
            --astro-gold: #FDCB6E;
            --cosmic-bg: radial-gradient(ellipse at center, #0F1419 0%, #1A1F2E 35%, #2C1810 100%);
            --neon-glow: rgba(116, 185, 255, 0.3);
        }

        .wheel-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            background: var(--cosmic-bg);
            min-height: 100vh;
            position: relative;
            color: white;
            font-family: 'Poppins', sans-serif;
            overflow-x: hidden;
        }

        .game-header {
            text-align: center;
            margin-bottom: 2rem;
            position: relative;
        }

        .game-title {
            font-size: 3.5rem;
            background: linear-gradient(45deg, var(--astro-purple), var(--astro-blue), var(--astro-pink));
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .game-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .stars-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.6;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stats-panel, .fairness-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .panel-title {
            font-size: 1.3rem;
            margin-bottom: 1.5rem;
            text-align: center;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .stat-value {
            color: var(--astro-gold);
            font-size: 1.8rem;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .wheel-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            padding: 2rem;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .wheel-container-inner {
            position: relative;
            margin-bottom: 2rem;
        }

        .wheel {
            width: 350px;
            height: 350px;
            border-radius: 50%;
            position: relative;
            background: conic-gradient(
                var(--astro-red) 0deg 18deg,
                var(--astro-orange) 18deg 36deg,
                var(--astro-green) 36deg 54deg,
                var(--astro-blue) 54deg 72deg,
                var(--astro-purple) 72deg 90deg,
                var(--astro-pink) 90deg 108deg,
                var(--astro-red) 108deg 126deg,
                var(--astro-orange) 126deg 144deg,
                var(--astro-green) 144deg 162deg,
                var(--astro-blue) 162deg 180deg,
                var(--astro-purple) 180deg 198deg,
                var(--astro-pink) 198deg 216deg,
                var(--astro-red) 216deg 234deg,
                var(--astro-orange) 234deg 252deg,
                var(--astro-green) 252deg 270deg,
                var(--astro-blue) 270deg 288deg,
                var(--astro-purple) 288deg 306deg,
                var(--astro-pink) 306deg 324deg,
                var(--astro-red) 324deg 342deg,
                var(--astro-orange) 342deg 360deg
            );
            border: 8px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 30px var(--neon-glow), 
                        inset 0 0 30px rgba(0, 0, 0, 0.3);
            transition: transform 4s cubic-bezier(0.23, 1, 0.32, 1);
            transform-origin: center;
        }

        .wheel-segments {
            position: absolute;
            inset: 0;
            border-radius: 50%;
        }

        .segment {
            position: absolute;
            width: 50%;
            height: 50%;
            transform-origin: right bottom;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1rem;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7);
        }

        .wheel-pointer {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 25px solid var(--astro-gold);
            z-index: 10;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
        }

        .wheel-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, var(--astro-gold), var(--astro-orange));
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            z-index: 5;
            box-shadow: 0 0 20px rgba(253, 203, 110, 0.5);
        }

        .spin-controls {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
        }

        .bet-area {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .bet-input {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid var(--astro-blue);
            border-radius: 10px;
            padding: 0.75rem 1rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
            width: 120px;
        }

        .bet-input:focus {
            outline: none;
            border-color: var(--astro-purple);
            box-shadow: 0 0 15px var(--neon-glow);
        }

        .spin-btn {
            background: linear-gradient(45deg, var(--astro-purple), var(--astro-blue));
            border: none;
            border-radius: 50%;
            width: 100px;
            height: 100px;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
            position: relative;
            overflow: hidden;
        }

        .spin-btn:before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }

        .spin-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(108, 92, 231, 0.6);
        }

        .spin-btn:hover:before {
            opacity: 1;
            animation: shimmer 0.6s ease-out;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .spin-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .result-display {
            text-align: center;
            margin-top: 1.5rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .result-text {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--astro-gold);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .fairness-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .seed-display {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            background: rgba(0, 0, 0, 0.3);
            padding: 0.75rem;
            border-radius: 8px;
            margin: 0.5rem 0;
            word-break: break-all;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .verification-tool {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .verify-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.75rem;
            color: white;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .verify-btn {
            background: linear-gradient(45deg, var(--astro-green), var(--astro-blue));
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .verify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 185, 148, 0.4);
        }

        .history-container {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .history-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .probability-display {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .prob-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .prob-item {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .prob-multiplier {
            font-size: 1.2rem;
            font-weight: bold;
            color: var(--astro-gold);
            margin-bottom: 0.5rem;
        }

        .prob-chance {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .rules-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .rules-toggle {
            background: none;
            border: none;
            color: var(--astro-blue);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            padding: 0;
            text-decoration: underline;
        }

        .rules-content {
            margin-top: 1rem;
            line-height: 1.6;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, var(--astro-purple), var(--astro-blue));
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            z-index: 1000;
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .wheel {
                width: 280px;
                height: 280px;
            }
            
            .game-title {
                font-size: 2.5rem;
            }
            
            .segment {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <div class="wheel-container">
        <!-- Animated Stars Background -->
        <div class="stars-bg" id="starsBg"></div>
        
        <div class="game-header">
            <h1 class="game-title">COSMIC FORTUNE</h1>
            <p class="game-subtitle">Provably Fair • Transparent • Test Your Cosmic Luck</p>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats -->
            <div class="stats-panel">
                <h3 class="panel-title">Player Stats</h3>
                
                <div class="stat-card">
                    <div class="stat-label">GA Balance</div>
                    <div class="stat-value" id="balance">10,000</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Total Spins</div>
                    <div class="stat-value" id="totalSpins">0</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Total Won</div>
                    <div class="stat-value" id="totalWon">0</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Biggest Win</div>
                    <div class="stat-value" id="biggestWin">0</div>
                </div>

                <div class="probability-display">
                    <h4 style="color: white; margin-bottom: 1rem; text-align: center;">Segment Probabilities</h4>
                    <div class="prob-grid">
                        <div class="prob-item">
                            <div class="prob-multiplier">×100</div>
                            <div class="prob-chance">3.3% (1/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="prob-multiplier">×10</div>
                            <div class="prob-chance">3.3% (1/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="prob-multiplier">×5</div>
                            <div class="prob-chance">6.7% (2/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="prob-multiplier">×2</div>
                            <div class="prob-chance">13.3% (4/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="prob-multiplier">×1</div>
                            <div class="prob-chance">16.7% (5/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="prob-multiplier">×0.5</div>
                            <div class="prob-chance">23.3% (7/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="prob-multiplier">×0</div>
                            <div class="prob-chance">33.3% (10/30)</div>
                        </div>
                        <div class="prob-item">
                            <div class="stat-label">House Edge</div>
                            <div class="prob-value" style="font-size: 1.1rem; color: var(--astro-red);">25.35%</div>
                        </div>
                    </div>
                </div>

                <div class="rules-section">
                    <button class="rules-toggle" onclick="toggleRules()">
                        <i class="fas fa-info-circle"></i> How to Play & Fairness
                    </button>
                    <div class="rules-content" id="rulesContent" style="display: none;">
                        <p><strong>How to Play:</strong></p>
                        <ul>
                            <li>Set your bet amount (1-500 GA)</li>
                            <li>Click "SPIN" to start the wheel</li>
                            <li>Win based on where the pointer lands</li>
                        </ul>
                        <p><strong>Provably Fair:</strong></p>
                        <ul>
                            <li>Each spin uses cryptographic seeds</li>
                            <li>Client seed: generated by your browser</li>
                            <li>Server seed: pre-committed and hashed</li>
                            <li>Verify any spin using the verification tool</li>
                        </ul>
                        <p><strong>Expected RTP:</strong> ~74.65%</p>
                        <p><strong>House Edge:</strong> 25.35%</p>
                    </div>
                </div>
            </div>

            <!-- Wheel Game Area -->
            <div class="wheel-area">
                <div class="wheel-container-inner">
                    <div class="wheel-pointer"></div>
                    <div class="wheel" id="wheel">
                        <div class="wheel-segments" id="wheelSegments">
                            <!-- Segments will be generated by JavaScript -->
                        </div>
                        <div class="wheel-center"></div>
                    </div>
                </div>

                <div class="spin-controls">
                    <div class="bet-area">
                        <label style="color: white; font-weight: 600;">Bet Amount:</label>
                        <input type="number" class="bet-input" id="betAmount" min="1" max="500" value="50" step="1">
                        <span style="color: rgba(255,255,255,0.7);">GA</span>
                    </div>
                    
                    <button class="spin-btn" id="spinBtn" onclick="spinWheel()">
                        <i class="fas fa-play"></i>
                        <div>SPIN</div>
                    </button>
                    
                    <div class="result-display" id="resultDisplay">
                        <div class="result-text">Place your bet and spin to win!</div>
                    </div>
                </div>
            </div>

            <!-- Fairness & Verification -->
            <div class="fairness-panel">
                <h3 class="panel-title">Provably Fair System</h3>
                
                <div class="fairness-info">
                    <h4 style="color: white; margin-bottom: 1rem;">Current Round Seeds</h4>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--astro-blue);">Client Seed:</strong>
                        <div class="seed-display" id="clientSeed">--</div>
                    </div>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--astro-purple);">Server Seed (Hashed):</strong>
                        <div class="seed-display" id="hashedServerSeed">--</div>
                    </div>
                    
                    <div>
                        <strong style="color: var(--astro-green);">Nonce:</strong>
                        <div class="seed-display" id="nonce">0</div>
                    </div>
                </div>

                <div class="verification-tool">
                    <h4 style="color: white; margin-bottom: 1rem;">Verify Past Spin</h4>
                    
                    <input type="text" class="verify-input" id="verifyClientSeed" placeholder="Enter client seed">
                    <input type="text" class="verify-input" id="verifyServerSeed" placeholder="Enter revealed server seed">
                    <input type="number" class="verify-input" id="verifyNonce" placeholder="Enter nonce">
                    
                    <button class="verify-btn" onclick="verifyResult()">
                        <i class="fas fa-check-circle"></i> Verify Result
                    </button>
                    
                    <div id="verificationResult" style="margin-top: 1rem; padding: 1rem; background: rgba(0,0,0,0.2); border-radius: 8px; display: none;">
                        <!-- Verification result will be displayed here -->
                    </div>
                </div>

                <div class="history-container">
                    <h4 style="color: white; margin-bottom: 1rem;">Spin History</h4>
                    <div id="spinHistory">
                        <!-- Spin history will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script src="assets/js/script.js"></script>
    <script src="wheel.js"></script>
</body>
</html>