// Tome of Life - Enhanced Mobile & Pro View Implementation
// Following YOUWARE platform standards

class TomeOfLife {
    constructor() {
        // Game state management
        this.gameState = 'ready'; // ready, revealing, result
        this.gaBalance = 10000;
        this.currentStake = 0;
        this.lastWin = 0;
        this.totalPlays = 0;
        this.nonce = 0;
        
        // UI state
        this.viewMode = 'standard', // 'standard' or 'pro'
        
        // Game statistics
        this.gameStats = {
            totalGames: 0,
            totalWins: 0,
            totalLosses: 0,
            totalWagered: 0,
            totalWon: 0,
            biggestWin: 0,
            longestWinStreak: 0,
            currentWinStreak: 0,
            avgMultiplier: 0,
            favoriteSymbol: null
        };
        
        // Pro view analytics
        this.analytics = {
            symbolFrequency: {},
            winPatterns: {
                rows: 0,
                columns: 0,
                diagonals: 0
            },
            symbolWins: {},
            recentResults: [],
            sessionData: {
                startTime: Date.now(),
                gamesPlayed: 0,
                sessionProfit: 0,
                peakBalance: 10000,
                lowestBalance: 10000
            },
            riskAssessment: {
                currentRisk: 'medium',
                recommendedStake: 100,
                confidence: 85
            }
        };
        
        // Symbol definitions with enhanced data
        this.symbols = [
            { id: 'sun', icon: '☀️', name: 'Sun', value: 2, weight: 20, rarity: 'common' },
            { id: 'moon', icon: '🌙', name: 'Moon', value: 2, weight: 20, rarity: 'common' },
            { id: 'star', icon: '⭐', name: 'Star', value: 3, weight: 15, rarity: 'uncommon' },
            { id: 'heart', icon: '❤️', name: 'Heart', value: 3, weight: 15, rarity: 'uncommon' },
            { id: 'tree', icon: '🌳', name: 'Tree', value: 5, weight: 10, rarity: 'rare' },
            { id: 'phoenix', icon: '🔥', name: 'Phoenix', value: 10, weight: 5, rarity: 'epic' },
            { id: 'eye', icon: '👁️', name: 'Eye', value: 15, weight: 3, rarity: 'legendary' },
            { id: 'skull', icon: '💀', name: 'Skull', value: 0, weight: 12, negative: true, rarity: 'cursed' }
        ];
        
        // Win lines (rows, columns, diagonals)
        this.winLines = [
            // Rows
            [0, 1, 2],
            [3, 4, 5],
            [6, 7, 8],
            // Columns
            [0, 3, 6],
            [1, 4, 7],
            [2, 5, 8],
            // Diagonals
            [0, 4, 8],
            [2, 4, 6]
        ];
        
        // Game grid and results
        this.grid = Array(9).fill(null);
        this.winningLines = [];
        
        // Provably fair system
        this.serverSeed = '';
        this.clientSeed = this.generateRandomString(16);
        this.serverSeedHash = '';
        
        // Game history
        this.gameHistory = [];
        
        // Initialize the game
        this.initialize();
    }

    initialize() {
        this.loadGameState();
        this.initializeElements();
        this.createViewModeToggle();
        this.createProViewPanel();
        this.attachEventListeners();
        this.generateNewServerSeed();
        this.updateAllDisplays();
        this.updateProViewStats();
        
        // Auto-save periodically
        setInterval(() => this.saveGameState(), 5000);
    }

    // Create view mode toggle
    createViewModeToggle() {
        if (document.querySelector('.view-mode-toggle')) return;
        
        const toggle = document.createElement('div');
        toggle.className = 'view-mode-toggle';
        toggle.innerHTML = `
            <button class="view-toggle-btn ${this.viewMode === 'standard' ? 'active' : ''}" 
                    data-mode="standard">STD</button>
            <button class="view-toggle-btn ${this.viewMode === 'pro' ? 'active' : ''}" 
                    data-mode="pro">PRO</button>
        `;
        
        document.body.appendChild(toggle);
        
        // Add event listeners
        toggle.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const mode = btn.getAttribute('data-mode');
                this.setViewMode(mode);
            });
        });
    }

    // Create Pro View panel
    createProViewPanel() {
        if (document.querySelector('.pro-view-panel')) return;
        
        const proPanel = document.createElement('div');
        proPanel.className = 'pro-view-panel';
        proPanel.innerHTML = `
            <h3 class="pro-panel-title">
                <i class="fas fa-eye"></i> Mystical Analytics
            </h3>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Win Rate</div>
                    <div class="pro-stat-value" id="proWinRate">0%</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Avg Multiplier</div>
                    <div class="pro-stat-value" id="proAvgMultiplier">0.00x</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Session Profit</div>
                    <div class="pro-stat-value" id="proSessionProfit">0 GA</div>
                </div>
                <div class="pro-stat-card">
                    <div class="pro-stat-label">Peak Balance</div>
                    <div class="pro-stat-value" id="proPeakBalance">10000 GA</div>
                </div>
            </div>
            
            <div class="symbol-analytics">
                <h4><i class="fas fa-chart-bar"></i> Symbol Frequency</h4>
                <div class="symbol-frequency-grid" id="symbolFrequencyGrid">
                    <!-- Symbol frequency data will be populated here -->
                </div>
            </div>
            
            <div class="win-patterns">
                <h4><i class="fas fa-crosshairs"></i> Win Pattern Analysis</h4>
                <div class="pattern-stats">
                    <div class="pattern-item">
                        <div class="pattern-label">Row Wins</div>
                        <div class="pattern-value" id="rowWins">0</div>
                    </div>
                    <div class="pattern-item">
                        <div class="pattern-label">Column Wins</div>
                        <div class="pattern-value" id="columnWins">0</div>
                    </div>
                    <div class="pattern-item">
                        <div class="pattern-label">Diagonal Wins</div>
                        <div class="pattern-value" id="diagonalWins">0</div>
                    </div>
                    <div class="pattern-item">
                        <div class="pattern-label">Favorite Symbol</div>
                        <div class="pattern-value" id="favoriteSymbol">-</div>
                    </div>
                </div>
            </div>
        `;
        
        // Insert before book section
        const bookSection = document.querySelector('.book-section');
        bookSection.parentNode.insertBefore(proPanel, bookSection);
    }

    // Set view mode
    setViewMode(mode) {
        this.viewMode = mode;
        localStorage.setItem('tomeViewMode', mode);
        
        // Update toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-mode') === mode) {
                btn.classList.add('active');
            }
        });
        
        // Toggle body class for CSS styling
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
        } else {
            document.body.classList.add('pro-view-active');
            this.updateProViewStats();
        }
        
        this.showNotification(`Switched to ${mode.toUpperCase()} view`, 'info');
    }

    // Load game state from localStorage
    loadGameState() {
        const saved = localStorage.getItem('tomeGameState');
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                this.gaBalance = parsed.gaBalance || 10000;
                this.gameStats = { ...this.gameStats, ...(parsed.gameStats || {}) };
                this.analytics = { ...this.analytics, ...(parsed.analytics || {}) };
                this.gameHistory = parsed.gameHistory || [];
                this.totalPlays = parsed.totalPlays || 0;
                this.lastWin = parsed.lastWin || 0;
            } catch (e) {
                console.warn('Failed to load game state:', e);
            }
        }
        
        // Load view mode
        const savedViewMode = localStorage.getItem('tomeViewMode');
        if (savedViewMode) {
            this.viewMode = savedViewMode;
        }
        
        // Initialize analytics if empty
        if (Object.keys(this.analytics.symbolFrequency).length === 0) {
            this.symbols.forEach(symbol => {
                this.analytics.symbolFrequency[symbol.id] = 0;
                this.analytics.symbolWins[symbol.id] = 0;
            });
        }
    }

    // Save game state to localStorage
    saveGameState() {
        try {
            const gameState = {
                gaBalance: this.gaBalance,
                gameStats: this.gameStats,
                analytics: this.analytics,
                gameHistory: this.gameHistory.slice(0, 50),
                totalPlays: this.totalPlays,
                lastWin: this.lastWin
            };
            localStorage.setItem('tomeGameState', JSON.stringify(gameState));
        } catch (e) {
            console.warn('Failed to save game state:', e);
        }
    }
    
    initializeElements() {
        // Tome and pages
        this.tome = document.getElementById('tome');
        this.symbolGrid = document.getElementById('symbolGrid');
        this.symbolCells = Array.from(document.querySelectorAll('.symbol-cell'));
        
        // Outcome displays
        this.winMessage = document.getElementById('winMessage');
        this.loseMessage = document.getElementById('loseMessage');
        this.winAmount = document.getElementById('winAmount');
        this.winMultiplier = document.getElementById('winMultiplier');
        this.winLines = document.getElementById('winLines');
        
        // Controls
        this.stakeInput = document.getElementById('stakeAmount');
        this.halfStakeBtn = document.getElementById('halfStakeBtn');
        this.doubleStakeBtn = document.getElementById('doubleStakeBtn');
        this.maxStakeBtn = document.getElementById('maxStakeBtn');
        this.revealBtn = document.getElementById('revealBtn');
        
        // Stats
        this.balanceValue = document.getElementById('balanceValue');
        this.currentStakeValue = document.getElementById('currentStakeValue');
        this.lastWinValue = document.getElementById('lastWinValue');
        this.totalPlaysValue = document.getElementById('totalPlaysValue');
        
        // Provably fair
        this.serverSeedHashDisplay = document.getElementById('serverSeedHash');
        this.clientSeedInput = document.getElementById('clientSeedInput');
        this.nonceValue = document.getElementById('nonceValue');
        this.randomizeSeedBtn = document.getElementById('randomizeSeedBtn');
        this.verifyBtn = document.getElementById('verifyBtn');
        
        // History
        this.historyList = document.getElementById('historyList');
        
        // Modals
        this.rulesModal = document.getElementById('rulesModal');
        this.verificationModal = document.getElementById('verificationModal');
        this.rulesBtn = document.getElementById('rulesBtn');
        this.closeRulesBtn = document.getElementById('closeRulesBtn');
        this.closeVerifyBtn = document.getElementById('closeVerifyBtn');
        this.verifyServerSeed = document.getElementById('verifyServerSeed');
        this.verifyClientSeed = document.getElementById('verifyClientSeed');
        this.verifyNonce = document.getElementById('verifyNonce');
        this.runVerificationBtn = document.getElementById('runVerificationBtn');
        this.verificationResult = document.getElementById('verificationResult');
        this.verificationGrid = document.getElementById('verificationGrid');
        this.verificationMessage = document.getElementById('verificationMessage');
        
        // Update labels for GA currency
        this.updateCurrencyLabels();
    }

    updateCurrencyLabels() {
        // Update subtitle
        if (document.querySelector('.game-subtitle')) {
            document.querySelector('.game-subtitle').textContent = 
                'Unlock ancient symbols to reveal the secrets of existence - Play with GA Coins';
        }
        
        // Update stat labels
        const statLabels = document.querySelectorAll('.stat-label');
        statLabels.forEach(label => {
            if (label.textContent.includes('Balance')) {
                label.textContent = 'GA Balance';
            } else if (label.textContent.includes('Stake')) {
                label.textContent = 'Current Stake (GA)';
            } else if (label.textContent.includes('Win')) {
                label.textContent = 'Last Win (GA)';
            }
        });
        
        // Update stake control label
        const stakeLabel = document.querySelector('label[for="stakeAmount"]');
        if (stakeLabel) {
            stakeLabel.textContent = 'Offering (GA):';
        }
    }
    
    attachEventListeners() {
        // Stake controls
        this.halfStakeBtn.addEventListener('click', () => this.adjustStake(0.5));
        this.doubleStakeBtn.addEventListener('click', () => this.adjustStake(2));
        this.maxStakeBtn.addEventListener('click', () => this.setMaxStake());
        
        // Reveal button
        this.revealBtn.addEventListener('click', () => this.startReveal());
        
        // Provably fair controls
        this.clientSeedInput.addEventListener('input', () => {
            this.clientSeed = this.clientSeedInput.value;
        });
        this.randomizeSeedBtn.addEventListener('click', () => this.randomizeClientSeed());
        this.verifyBtn.addEventListener('click', () => this.openVerificationModal());
        
        // Modal controls
        this.rulesBtn.addEventListener('click', () => this.openRulesModal());
        this.closeRulesBtn.addEventListener('click', () => this.closeModal(this.rulesModal));
        this.closeVerifyBtn.addEventListener('click', () => this.closeModal(this.verificationModal));
        this.runVerificationBtn.addEventListener('click', () => this.runVerification());
        
        // Click outside to close modals
        window.addEventListener('click', (e) => {
            if (e.target === this.rulesModal) this.closeModal(this.rulesModal);
            if (e.target === this.verificationModal) this.closeModal(this.verificationModal);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && this.gameState === 'ready') {
                e.preventDefault();
                this.startReveal();
            } else if (e.code === 'KeyP') {
                this.setViewMode(this.viewMode === 'standard' ? 'pro' : 'standard');
            }
        });

        // Touch events for mobile optimization
        this.attachTouchEvents();
    }

    // Attach touch events for mobile optimization
    attachTouchEvents() {
        let touchStartTime = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        });
        
        document.addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            
            // Long press detection for quick actions
            if (touchDuration > 500) {
                const target = e.target.closest('.tome');
                if (target && this.gameState === 'ready') {
                    this.startReveal();
                    this.showNotification('✨ Quick reveal activated!', 'success');
                }
            }
        });
        
        // Prevent double-tap zoom on game elements
        document.querySelectorAll('.tome, .stake-btn, .reveal-btn').forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
    }
    
    // Game flow methods
    startReveal() {
        if (this.gameState !== 'ready') return;
        
        const stakeAmount = parseInt(this.stakeInput.value);
        
        // Validate stake
        if (isNaN(stakeAmount) || stakeAmount <= 0) {
            this.showNotification('Please enter a valid GA amount', 'error');
            return;
        }
        
        if (stakeAmount > this.gaBalance) {
            this.showNotification('Insufficient GA balance', 'error');
            return;
        }
        
        // Update game state
        this.gameState = 'revealing';
        this.currentStake = stakeAmount;
        this.nonce++;
        this.updateBalance(this.gaBalance - stakeAmount);
        this.updateCurrentStake(stakeAmount);
        if (this.nonceValue) {
            this.nonceValue.textContent = this.nonce;
        }
        
        // Update analytics
        this.analytics.sessionData.gamesPlayed++;
        this.gameStats.totalGames++;
        this.gameStats.totalWagered += stakeAmount;
        
        // Disable controls during animation
        this.revealBtn.disabled = true;
        this.stakeInput.disabled = true;
        
        // Reset grid and results
        this.grid = Array(9).fill(null);
        this.winningLines = [];
        this.clearSymbolGrid();
        this.hideOutcomeMessages();
        
        // Generate symbols based on provably fair algorithm
        this.generateSymbols();
        
        // Animate opening the tome
        this.animateOpenTome().then(() => {
            // Reveal symbols one by one
            this.revealSymbols().then(() => {
                // Check win condition
                this.checkWinCondition();
                
                // Save game to history
                this.saveToHistory();
                
                // Update game state
                this.gameState = 'ready';
                
                // Enable controls
                this.revealBtn.disabled = false;
                this.stakeInput.disabled = false;
                
                // Generate new server seed for next round
                this.generateNewServerSeed();
                
                // Update analytics
                this.updateAnalytics();
                this.updateProViewStats();
            });
        });
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate([100, 50, 100]);
        }
    }
    
    animateOpenTome() {
        return new Promise(resolve => {
            // Add opening animation class
            if (this.tome) {
                this.tome.style.transform = 'scale(1.02)';
                this.tome.style.boxShadow = '0 8px 25px rgba(255, 215, 0, 0.3)';
            }
            
            // Make pages visible with animation
            const pages = this.tome.querySelector('.tome-pages');
            if (pages) {
                pages.style.opacity = '1';
                pages.style.transform = 'translateY(0)';
            }
            
            // Resolve after animation completes
            setTimeout(() => {
                if (this.tome) {
                    this.tome.style.transform = 'scale(1)';
                    this.tome.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.5)';
                }
                resolve();
            }, 800);
        });
    }
    
    revealSymbols() {
        return new Promise(resolve => {
            // Reveal symbols one by one with delay
            this.symbolCells.forEach((cell, index) => {
                setTimeout(() => {
                    const symbol = this.grid[index];
                    const symbolElement = cell.querySelector('.symbol-inner');
                    
                    if (symbolElement && symbol) {
                        symbolElement.textContent = symbol.icon;
                        cell.classList.add('revealed');
                        
                        // Add special effect for rare symbols
                        if (symbol.rarity === 'legendary' || symbol.rarity === 'epic') {
                            cell.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.8)';
                        } else if (symbol.negative) {
                            cell.style.boxShadow = '0 0 20px rgba(244, 67, 54, 0.8)';
                        }
                        
                        // Update symbol frequency for analytics
                        this.analytics.symbolFrequency[symbol.id]++;
                        
                        // Haptic feedback for each symbol
                        if (navigator.vibrate) {
                            navigator.vibrate(30);
                        }
                    }
                    
                    // If this is the last symbol, resolve after a delay
                    if (index === this.symbolCells.length - 1) {
                        setTimeout(resolve, 500);
                    }
                }, index * 150);
            });
        });
    }
    
    checkWinCondition() {
        let hasSkull = false;
        let totalWin = 0;
        let highestMultiplier = 0;
        let winningSymbol = null;
        
        // Check for skull first
        this.grid.forEach(symbol => {
            if (symbol.id === 'skull') {
                hasSkull = true;
            }
        });
        
        if (hasSkull) {
            // Show lose message
            this.showLoseMessage();
            this.gameStats.totalLosses++;
            this.gameStats.currentWinStreak = 0;
            
            // Strong haptic feedback for loss
            if (navigator.vibrate) {
                navigator.vibrate([200, 100, 200, 100, 300]);
            }
            return;
        }
        
        // Check winning lines
        this.winLines.forEach((line, lineIndex) => {
            const [a, b, c] = line;
            const symbolA = this.grid[a];
            const symbolB = this.grid[b];
            const symbolC = this.grid[c];
            
            // Check if all symbols in the line match
            if (symbolA.id === symbolB.id && symbolB.id === symbolC.id) {
                // Found a winning line
                this.winningLines.push(line);
                
                // Get multiplier for this symbol
                const multiplier = symbolA.value;
                if (multiplier > highestMultiplier) {
                    highestMultiplier = multiplier;
                    winningSymbol = symbolA;
                }
                
                // Update analytics for win patterns
                if (lineIndex < 3) {
                    this.analytics.winPatterns.rows++;
                } else if (lineIndex < 6) {
                    this.analytics.winPatterns.columns++;
                } else {
                    this.analytics.winPatterns.diagonals++;
                }
                
                // Highlight winning cells
                this.highlightWinningLine(line);
                
                // Update symbol wins
                this.analytics.symbolWins[symbolA.id]++;
            }
        });
        
        if (this.winningLines.length > 0) {
            // Calculate win amount
            totalWin = this.currentStake * highestMultiplier;
            
            // Update balance and stats
            this.updateBalance(this.gaBalance + totalWin);
            this.lastWin = totalWin;
            this.updateLastWin(totalWin);
            
            this.gameStats.totalWins++;
            this.gameStats.totalWon += totalWin;
            this.gameStats.currentWinStreak++;
            
            if (this.gameStats.currentWinStreak > this.gameStats.longestWinStreak) {
                this.gameStats.longestWinStreak = this.gameStats.currentWinStreak;
            }
            
            if (totalWin > this.gameStats.biggestWin) {
                this.gameStats.biggestWin = totalWin;
            }
            
            // Update session profit
            this.analytics.sessionData.sessionProfit += (totalWin - this.currentStake);
            
            // Show win message
            this.showWinMessage(totalWin, highestMultiplier);
            
            // Celebration haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate([100, 50, 100, 50, 200, 100, 300]);
            }
            
            this.showNotification(`🎉 Mystical alignment! Won ${totalWin} GA with ${winningSymbol.name}!`, 'success');
        } else {
            // No winning lines, show lose message
            this.showLoseMessage();
            this.gameStats.totalLosses++;
            this.gameStats.currentWinStreak = 0;
            
            // Update session profit
            this.analytics.sessionData.sessionProfit -= this.currentStake;
        }
        
        this.totalPlays++;
        this.updateTotalPlays(this.totalPlays);
        
        // Update peak/lowest balance tracking
        if (this.gaBalance > this.analytics.sessionData.peakBalance) {
            this.analytics.sessionData.peakBalance = this.gaBalance;
        }
        if (this.gaBalance < this.analytics.sessionData.lowestBalance) {
            this.analytics.sessionData.lowestBalance = this.gaBalance;
        }
    }
    
    highlightWinningLine(line) {
        line.forEach((index, i) => {
            setTimeout(() => {
                this.symbolCells[index].classList.add('highlighted');
            }, i * 100);
        });
    }

    // Update analytics and Pro View stats
    updateAnalytics() {
        // Calculate average multiplier
        if (this.gameStats.totalWins > 0) {
            this.gameStats.avgMultiplier = this.gameStats.totalWon / this.gameStats.totalWagered;
        }
        
        // Find favorite symbol (most frequent winner)
        let maxWins = 0;
        let favoriteSymbol = null;
        Object.entries(this.analytics.symbolWins).forEach(([symbolId, wins]) => {
            if (wins > maxWins) {
                maxWins = wins;
                favoriteSymbol = this.symbols.find(s => s.id === symbolId);
            }
        });
        this.gameStats.favoriteSymbol = favoriteSymbol;
        
        // Update recent results
        const result = {
            timestamp: Date.now(),
            stake: this.currentStake,
            outcome: this.winningLines.length > 0 ? 'win' : 'loss',
            winAmount: this.lastWin,
            symbols: [...this.grid]
        };
        
        this.analytics.recentResults.unshift(result);
        if (this.analytics.recentResults.length > 20) {
            this.analytics.recentResults = this.analytics.recentResults.slice(0, 20);
        }
        
        // Risk assessment
        this.updateRiskAssessment();
    }

    updateRiskAssessment() {
        const recentGames = this.analytics.recentResults.slice(0, 10);
        const recentLosses = recentGames.filter(g => g.outcome === 'loss').length;
        const lossRate = recentGames.length > 0 ? recentLosses / recentGames.length : 0;
        
        // Calculate risk based on recent performance and balance
        let riskScore = 0;
        
        // Loss rate contribution (0-40 points)
        riskScore += lossRate * 40;
        
        // Balance ratio contribution (0-30 points)
        const balanceRatio = this.gaBalance / 10000; // Assuming 10000 starting balance
        if (balanceRatio < 0.5) {
            riskScore += 30;
        } else if (balanceRatio < 0.8) {
            riskScore += 15;
        }
        
        // Stake size relative to balance (0-30 points)
        const stakeRatio = this.currentStake / this.gaBalance;
        riskScore += stakeRatio * 30;
        
        // Determine risk level and recommendations
        let riskLevel = 'low';
        let recommendedStake = Math.min(this.gaBalance * 0.05, 100);
        
        if (riskScore > 50) {
            riskLevel = 'high';
            recommendedStake = Math.min(this.gaBalance * 0.02, 50);
        } else if (riskScore > 30) {
            riskLevel = 'medium';
            recommendedStake = Math.min(this.gaBalance * 0.03, 75);
        }
        
        this.analytics.riskAssessment = {
            currentRisk: riskLevel,
            recommendedStake: Math.round(recommendedStake),
            confidence: Math.round(85 + (Math.random() * 10))
        };
    }

    updateProViewStats() {
        if (this.viewMode !== 'pro') return;
        
        // Calculate win rate
        const winRate = this.gameStats.totalGames > 0 ? 
            (this.gameStats.totalWins / this.gameStats.totalGames * 100) : 0;
        
        // Calculate average multiplier
        const avgMultiplier = this.gameStats.totalGames > 0 ? 
            this.gameStats.avgMultiplier : 0;
        
        // Session profit
        const sessionProfit = this.analytics.sessionData.sessionProfit;
        
        // Peak balance
        const peakBalance = this.analytics.sessionData.peakBalance;
        
        // Update pro stats display
        const elements = {
            proWinRate: `${winRate.toFixed(1)}%`,
            proAvgMultiplier: `${avgMultiplier.toFixed(2)}x`,
            proSessionProfit: `${sessionProfit >= 0 ? '+' : ''}${sessionProfit} GA`,
            proPeakBalance: `${peakBalance} GA`
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                
                // Color session profit
                if (id === 'proSessionProfit') {
                    element.style.color = sessionProfit >= 0 ? 
                        'var(--success-color)' : 'var(--danger-color)';
                }
            }
        });
        
        this.updateSymbolFrequencyDisplay();
        this.updateWinPatternsDisplay();
    }

    updateSymbolFrequencyDisplay() {
        const container = document.getElementById('symbolFrequencyGrid');
        if (!container) return;
        
        container.innerHTML = '';
        
        // Get top 4 most frequent symbols
        const sortedSymbols = Object.entries(this.analytics.symbolFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 4);
        
        sortedSymbols.forEach(([symbolId, frequency]) => {
            const symbol = this.symbols.find(s => s.id === symbolId);
            if (symbol) {
                const item = document.createElement('div');
                item.className = 'symbol-freq-item';
                item.innerHTML = `
                    <span class="symbol-icon">${symbol.icon}</span>
                    <span>${frequency}</span>
                `;
                container.appendChild(item);
            }
        });
    }

    updateWinPatternsDisplay() {
        const elements = {
            rowWins: this.analytics.winPatterns.rows,
            columnWins: this.analytics.winPatterns.columns,
            diagonalWins: this.analytics.winPatterns.diagonals,
            favoriteSymbol: this.gameStats.favoriteSymbol ? 
                this.gameStats.favoriteSymbol.icon : '-'
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }
    
    // Symbol generation using provably fair algorithm
    generateSymbols() {
        // Create weighted array of symbols based on their weights
        let weightedSymbols = [];
        this.symbols.forEach(symbol => {
            for (let i = 0; i < symbol.weight; i++) {
                weightedSymbols.push(symbol);
            }
        });
        
        // Generate deterministic random numbers using hash of server seed + client seed + nonce
        const combinedSeed = this.serverSeed + this.clientSeed + this.nonce.toString();
        let hash = this.hashCode(combinedSeed);
        
        // Select 9 symbols based on the hash
        for (let i = 0; i < 9; i++) {
            // Generate next hash value
            hash = this.hashCode(hash.toString());
            
            // Use the hash to pick a symbol from the weighted array
            const index = Math.abs(hash) % weightedSymbols.length;
            this.grid[i] = weightedSymbols[index];
        }
    }
    
    // Provably fair methods
    generateNewServerSeed() {
        this.serverSeed = this.generateRandomString(32);
        this.serverSeedHash = this.hashString(this.serverSeed);
        if (this.serverSeedHashDisplay) {
            this.serverSeedHashDisplay.textContent = this.serverSeedHash.substring(0, 16) + '...';
        }
    }
    
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(8, '0') + this.generateRandomString(8);
    }
    
    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash;
    }
    
    randomizeClientSeed() {
        this.clientSeed = this.generateRandomString(16);
        this.updateClientSeedDisplay();
        this.showNotification('Client seed randomized', 'info');
    }
    
    updateClientSeedDisplay() {
        this.clientSeedInput.value = this.clientSeed;
    }
    
    runVerification() {
        const serverSeed = this.verifyServerSeed.value;
        const clientSeed = this.verifyClientSeed.value;
        const nonce = parseInt(this.verifyNonce.value);
        
        if (!serverSeed || !clientSeed || isNaN(nonce)) {
            this.showNotification('Please enter all verification details', 'error');
            return;
        }
        
        // Generate symbols using the provided seeds
        const symbols = this.verifySymbols(serverSeed, clientSeed, nonce);
        
        // Display verification result
        this.verificationResult.classList.remove('hidden');
        this.verificationGrid.innerHTML = '';
        
        symbols.forEach(symbol => {
            const symbolElement = document.createElement('div');
            symbolElement.className = 'verification-cell';
            symbolElement.textContent = symbol.icon;
            this.verificationGrid.appendChild(symbolElement);
        });
        
        // Check win conditions for verification
        let hasSkull = symbols.some(symbol => symbol.id === 'skull');
        let winningLines = [];
        
        if (!hasSkull) {
            // Check winning lines
            this.winLines.forEach(line => {
                const [a, b, c] = line;
                const symbolA = symbols[a];
                const symbolB = symbols[b];
                const symbolC = symbols[c];
                
                if (symbolA.id === symbolB.id && symbolB.id === symbolC.id) {
                    winningLines.push(line);
                }
            });
        }
        
        // Set verification message
        let message = '';
        let messageClass = '';
        
        if (hasSkull) {
            message = '💀 Game Result: Loss (Skull symbol present)';
            messageClass = 'verification-failure';
        } else if (winningLines.length > 0) {
            let highestMultiplier = 0;
            winningLines.forEach(line => {
                const symbolValue = symbols[line[0]].value;
                if (symbolValue > highestMultiplier) {
                    highestMultiplier = symbolValue;
                }
            });
            message = `✨ Game Result: Win (Multiplier: x${highestMultiplier})`;
            messageClass = 'verification-success';
        } else {
            message = '🌀 Game Result: Loss (No winning combinations)';
            messageClass = 'verification-failure';
        }
        
        this.verificationMessage.textContent = message;
        this.verificationMessage.className = `verification-message ${messageClass}`;
        
        this.showNotification('Verification completed successfully', 'success');
    }
    
    verifySymbols(serverSeed, clientSeed, nonce) {
        // Create weighted array of symbols
        let weightedSymbols = [];
        this.symbols.forEach(symbol => {
            for (let i = 0; i < symbol.weight; i++) {
                weightedSymbols.push(symbol);
            }
        });
        
        // Generate deterministic random numbers
        const combinedSeed = serverSeed + clientSeed + nonce.toString();
        let hash = this.hashCode(combinedSeed);
        
        // Select 9 symbols
        let symbols = [];
        for (let i = 0; i < 9; i++) {
            hash = this.hashCode(hash.toString());
            const index = Math.abs(hash) % weightedSymbols.length;
            symbols.push(weightedSymbols[index]);
        }
        
        return symbols;
    }
    
    // UI update methods
    updateBalance(value) {
        this.gaBalance = value;
        this.balanceValue.textContent = `${value} GA`;
    }
    
    updateCurrentStake(value) {
        this.currentStakeValue.textContent = `${value} GA`;
    }
    
    updateLastWin(value) {
        this.lastWinValue.textContent = `${value} GA`;
    }
    
    updateTotalPlays(value) {
        this.totalPlaysValue.textContent = value;
    }

    updateAllDisplays() {
        this.updateBalance(this.gaBalance);
        this.updateCurrentStake(0);
        this.updateLastWin(this.lastWin);
        this.updateTotalPlays(this.totalPlays);
        this.updateClientSeedDisplay();
    }
    
    clearSymbolGrid() {
        this.symbolCells.forEach(cell => {
            const symbolElement = cell.querySelector('.symbol-inner');
            symbolElement.textContent = '';
            cell.classList.remove('revealed', 'highlighted');
            cell.style.boxShadow = '';
        });
    }
    
    hideOutcomeMessages() {
        this.winMessage.classList.add('hidden');
        this.loseMessage.classList.add('hidden');
        this.winMessage.classList.remove('visible');
        this.loseMessage.classList.remove('visible');
    }
    
    showWinMessage(amount, multiplier) {
        this.winAmount.textContent = `${amount} GA`;
        this.winMultiplier.textContent = `x${multiplier}`;
        this.winMessage.classList.remove('hidden');
        this.winMessage.classList.add('visible');
    }
    
    showLoseMessage() {
        this.loseMessage.classList.remove('hidden');
        this.loseMessage.classList.add('visible');
    }
    
    // Controls methods
    adjustStake(factor) {
        const currentValue = parseInt(this.stakeInput.value) || 0;
        const newValue = Math.floor(currentValue * factor);
        this.stakeInput.value = Math.max(10, Math.min(newValue, this.gaBalance));
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(50);
        }
    }
    
    setMaxStake() {
        this.stakeInput.value = this.gaBalance;
        
        // Haptic feedback
        if (navigator.vibrate) {
            navigator.vibrate(100);
        }
    }
    
    // Modal methods
    openRulesModal() {
        this.rulesModal.classList.remove('hidden');
        
        // Update modal text to include GA currency info
        const modalBody = this.rulesModal.querySelector('.modal-body');
        if (modalBody) {
            const howToPlayList = modalBody.querySelector('ol');
            if (howToPlayList && howToPlayList.children[0]) {
                howToPlayList.children[0].textContent = 'Set your offering amount (10-10000 GA)';
            }
        }
    }
    
    openVerificationModal() {
        this.verificationModal.classList.remove('hidden');
        this.verificationResult.classList.add('hidden');
    }
    
    closeModal(modal) {
        modal.classList.add('hidden');
    }

    // Show notification with mobile optimization
    showNotification(message, type = 'info') {
        // Remove existing notification
        const existing = document.querySelector('.notification');
        if (existing) {
            existing.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        
        const colors = {
            error: 'linear-gradient(45deg, #F94144, #E63946)',
            success: 'linear-gradient(45deg, #90BE6D, #0B6E4F)',
            warning: 'linear-gradient(45deg, #F9C74F, #F8961E)',
            info: 'linear-gradient(45deg, #6a1b9a, #4a148c)'
        };
        
        notification.style.background = colors[type] || colors.info;
        notification.classList.add('show');
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
    
    // History methods
    saveToHistory() {
        const historyItem = {
            nonce: this.nonce,
            symbols: [...this.grid],
            stake: this.currentStake,
            outcome: this.winningLines.length > 0 ? 'win' : 'loss',
            multiplier: this.winningLines.length > 0 ? 
                Math.max(...this.winningLines.map(line => this.grid[line[0]].value)) : 0,
            winAmount: this.lastWin,
            serverSeed: this.serverSeed,
            clientSeed: this.clientSeed,
            timestamp: Date.now()
        };
        
        this.gameHistory.unshift(historyItem);
        if (this.gameHistory.length > 50) {
            this.gameHistory.pop();
        }
        
        this.updateHistoryDisplay();
    }
    
    updateHistoryDisplay() {
        this.historyList.innerHTML = '';
        
        this.gameHistory.slice(0, 10).forEach(item => {
            const historyElement = document.createElement('div');
            historyElement.className = 'history-item';
            
            const resultElement = document.createElement('div');
            resultElement.className = 'history-result';
            
            const outcomeElement = document.createElement('div');
            outcomeElement.className = `history-outcome ${item.outcome === 'win' ? 'history-win' : 'history-lose'}`;
            outcomeElement.textContent = item.outcome === 'win' ? 
                `Won ${item.winAmount} GA` : 'Lost';
            
            resultElement.appendChild(outcomeElement);
            
            if (item.outcome === 'win') {
                const multiplierElement = document.createElement('div');
                multiplierElement.className = 'history-multiplier';
                multiplierElement.textContent = `x${item.multiplier}`;
                resultElement.appendChild(multiplierElement);
            }
            
            const symbolsElement = document.createElement('div');
            symbolsElement.className = 'history-symbols';
            
            // Show first 3 symbols to save space
            for (let i = 0; i < 3; i++) {
                const symbolElement = document.createElement('div');
                symbolElement.className = 'history-symbol';
                symbolElement.textContent = item.symbols[i].icon;
                symbolsElement.appendChild(symbolElement);
            }
            
            historyElement.appendChild(resultElement);
            historyElement.appendChild(symbolsElement);
            
            // Add click handler to view details
            historyElement.addEventListener('click', () => {
                this.openVerificationModal();
                this.verifyServerSeed.value = item.serverSeed;
                this.verifyClientSeed.value = item.clientSeed;
                this.verifyNonce.value = item.nonce;
            });
            
            this.historyList.appendChild(historyElement);
        });
        
        // Add empty state if no history
        if (this.gameHistory.length === 0) {
            const emptyElement = document.createElement('div');
            emptyElement.style.textAlign = 'center';
            emptyElement.style.opacity = '0.6';
            emptyElement.style.padding = '1rem';
            emptyElement.textContent = 'No revelations yet. Begin your mystical journey!';
            this.historyList.appendChild(emptyElement);
        }
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.tomeGame = new TomeOfLife();
        console.log("Tome of Life game initialized successfully!");
    } catch (error) {
        console.error("Error initializing Tome of Life game:", error);
    }
});