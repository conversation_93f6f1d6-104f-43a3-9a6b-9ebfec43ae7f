// Poker Palace - Texas Hold'em Game Engine
document.addEventListener('DOMContentLoaded', function() {
    console.log("Poker Palace initializing...");
    
    // Game elements
    const elements = {
        // Header controls
        standardViewBtn: document.getElementById('standardViewBtn'),
        proViewBtn: document.getElementById('proViewBtn'),
        tutorialBtn: document.getElementById('tutorialBtn'),
        rulesBtn: document.getElementById('rulesBtn'),
        
        // Modals
        tutorialModal: document.getElementById('tutorialModal'),
        closeTutorialBtn: document.getElementById('closeTutorialBtn'),
        rulesModal: document.getElementById('rulesModal'),
        closeRulesBtn: document.getElementById('closeRulesBtn'),
        
        // Mobile status
        mobileBalance: document.getElementById('mobileBalance'),
        mobilePlayersCount: document.getElementById('mobilePlayersCount'),
        mobilePotSize: document.getElementById('mobilePotSize'),
        mobileWinChance: document.getElementById('mobileWinChance'),
        
        // Game table
        communityCards: ['flop1', 'flop2', 'flop3', 'turn', 'river'].map(id => document.getElementById(id)),
        potAmount: document.getElementById('potAmount'),
        dealerButton: document.getElementById('dealerButton'),
        
        // Player actions
        playerActions: document.getElementById('playerActions'),
        foldBtn: document.getElementById('foldBtn'),
        callBtn: document.getElementById('callBtn'),
        raiseBtn: document.getElementById('raiseBtn'),
        checkBtn: document.getElementById('checkBtn'),
        callAmount: document.getElementById('callAmount'),
        
        // Betting controls
        betSlider: document.getElementById('betSlider'),
        betAmount: document.getElementById('betAmount'),
        quickBets: document.querySelectorAll('.quick-bet'),
        
        // Game info
        handStrength: document.getElementById('handStrength'),
        winProbability: document.getElementById('winProbability'),
        roundPhase: document.getElementById('roundPhase'),
        playersActive: document.getElementById('playersActive'),
        nextAction: document.getElementById('nextAction'),
        
        // Game actions
        dealBtn: document.getElementById('dealBtn'),
        autoFoldBtn: document.getElementById('autoFoldBtn'),
        sitOutBtn: document.getElementById('sitOutBtn'),
        leaveBtn: document.getElementById('leaveBtn'),
        difficultySelect: document.getElementById('difficultySelect'),
        
        // Pro analytics
        vpipStat: document.getElementById('vpipStat'),
        pfrStat: document.getElementById('pfrStat'),
        aggressionStat: document.getElementById('aggressionStat'),
        handsPerHour: document.getElementById('handsPerHour'),
        handHistoryList: document.getElementById('handHistoryList'),
        
        // Session stats
        handsPlayed: document.getElementById('handsPlayed'),
        handsWon: document.getElementById('handsWon'),
        winRate: document.getElementById('winRate'),
        netProfit: document.getElementById('netProfit'),
        biggestPot: document.getElementById('biggestPot'),
        sessionTime: document.getElementById('sessionTime')
    };
    
    // Game state
    let gameState = {
        // Basic game state
        balance: 1000,
        currentBet: 10,
        pot: 0,
        currentPhase: 'pre-flop', // pre-flop, flop, turn, river, showdown
        dealerPosition: 0,
        activePlayer: 0,
        currentBetAmount: 0,
        minRaise: 10,
        
        // Blinds
        smallBlind: 5,
        bigBlind: 10,
        
        // Cards
        deck: [],
        communityCards: [],
        playerHands: [],
        
        // Players
        players: [
            { id: 0, name: 'You', chips: 1000, status: 'active', isHuman: true, cards: [], bet: 0, folded: false },
            { id: 1, name: 'Alex', chips: 950, status: 'active', isHuman: false, difficulty: 'novice', cards: [], bet: 0, folded: false },
            { id: 2, name: 'Sarah', chips: 1200, status: 'active', isHuman: false, difficulty: 'pro', cards: [], bet: 0, folded: false },
            { id: 3, name: 'Mike', chips: 800, status: 'active', isHuman: false, difficulty: 'shark', cards: [], bet: 0, folded: false },
            { id: 4, name: 'Emma', chips: 1100, status: 'active', isHuman: false, difficulty: 'pro', cards: [], bet: 0, folded: false },
            { id: 5, name: 'Jake', chips: 900, status: 'active', isHuman: false, difficulty: 'novice', cards: [], bet: 0, folded: false }
        ],
        
        // Game mode
        viewMode: 'standard',
        autoFold: false,
        sittingOut: false,
        hasHapticSupport: false,
        optimizedUpdate: null,
        
        // Statistics
        statistics: {
            handsPlayed: 0,
            handsWon: 0,
            totalWagered: 0,
            totalWon: 0,
            netProfit: 0,
            biggestPot: 0,
            vpip: 0,
            pfr: 0,
            aggression: 0,
            sessionStartTime: Date.now()
        },
        
        // Hand history
        handHistory: [],
        
        // AI personalities
        aiPersonalities: {
            novice: { bluffFreq: 10, tightness: 80, aggression: 20 },
            pro: { bluffFreq: 35, tightness: 60, aggression: 60 },
            shark: { bluffFreq: 60, tightness: 40, aggression: 80 }
        }
    };
    
    // Card and hand evaluation
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const rankValues = { '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14 };
    
    // Hand rankings
    const handRankings = [
        'High Card', 'One Pair', 'Two Pair', 'Three of a Kind', 'Straight', 
        'Flush', 'Full House', 'Four of a Kind', 'Straight Flush', 'Royal Flush'
    ];
    
    // Mobile and touch support
    let isMobile = window.innerWidth <= 480;
    let touchStartY = 0;
    let touchEndY = 0;
    let lastTouchTime = 0;
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing Poker Palace...");
        detectMobile();
        setupEventListeners();
        setupMobileFeatures();
        loadGameState();
        updateDisplay();
        initializeNewGame();
        
        // Setup responsive listener
        window.addEventListener('resize', debounce(handleResize, 250));
    }
    
    function detectMobile() {
        isMobile = window.innerWidth <= 480;
        document.body.classList.toggle('mobile-device', isMobile);
        
        // Add mobile-specific viewport meta tag behavior
        if (isMobile) {
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }
        }
    }
    
    function handleResize() {
        const wasMobile = isMobile;
        detectMobile();
        
        if (wasMobile !== isMobile) {
            // Device type changed, update layout
            updateMobileLayout();
            if (gameState.viewMode === 'pro') {
                updateProAnalytics();
            }
        }
    }
    
    function setupMobileFeatures() {
        if (!isMobile) return;
        
        // Setup touch gestures for Pro View
        setupProViewGestures();
        
        // Setup haptic feedback for supported devices
        setupHapticFeedback();
        
        // Optimize for mobile performance
        optimizeMobilePerformance();
        
        // Setup mobile-specific interactions
        setupMobileInteractions();
    }
    
    function setupProViewGestures() {
        const proAnalytics = document.querySelector('.pro-analytics');
        if (!proAnalytics) return;
        
        // Swipe up to open Pro View
        document.addEventListener('touchstart', handleTouchStart, { passive: true });
        document.addEventListener('touchend', handleTouchEnd, { passive: true });
        
        // Swipe down to close Pro View (when in Pro View)
        proAnalytics.addEventListener('touchstart', handleProViewTouchStart, { passive: true });
        proAnalytics.addEventListener('touchend', handleProViewTouchEnd, { passive: true });
    }
    
    function handleTouchStart(e) {
        touchStartY = e.touches[0].clientY;
        lastTouchTime = Date.now();
    }
    
    function handleTouchEnd(e) {
        touchEndY = e.changedTouches[0].clientY;
        const touchDuration = Date.now() - lastTouchTime;
        const swipeDistance = touchStartY - touchEndY;
        
        // Swipe up gesture (at least 50px up, within 300ms)
        if (swipeDistance > 50 && touchDuration < 300 && gameState.viewMode === 'standard') {
            setViewMode('pro');
            triggerHapticFeedback('light');
        }
    }
    
    function handleProViewTouchStart(e) {
        e.stopPropagation();
        touchStartY = e.touches[0].clientY;
        lastTouchTime = Date.now();
    }
    
    function handleProViewTouchEnd(e) {
        e.stopPropagation();
        touchEndY = e.changedTouches[0].clientY;
        const touchDuration = Date.now() - lastTouchTime;
        const swipeDistance = touchEndY - touchStartY;
        
        // Swipe down gesture (at least 30px down, within 300ms)
        if (swipeDistance > 30 && touchDuration < 300) {
            setViewMode('standard');
            triggerHapticFeedback('light');
        }
    }
    
    function setupHapticFeedback() {
        // Check if device supports haptic feedback
        gameState.hasHapticSupport = 'vibrate' in navigator;
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!gameState.hasHapticSupport || !isMobile) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function optimizeMobilePerformance() {
        // Reduce animation complexity on mobile
        if (isMobile) {
            document.documentElement.style.setProperty('--transition', 'all 0.2s ease');
            document.documentElement.style.setProperty('--transition-fast', 'all 0.1s ease');
        }
        
        // Use requestAnimationFrame for smooth animations
        let animationFrame;
        function optimizedUpdate() {
            if (animationFrame) return;
            
            animationFrame = requestAnimationFrame(() => {
                updateMobileStatusBar();
                animationFrame = null;
            });
        }
        
        // Replace direct calls with optimized version for mobile
        if (isMobile) {
            gameState.optimizedUpdate = optimizedUpdate;
        }
    }
    
    function setupMobileInteractions() {
        // Enhanced touch feedback for buttons
        const actionButtons = document.querySelectorAll('.action-btn, .quick-bet, .view-btn');
        actionButtons.forEach(button => {
            button.addEventListener('touchstart', () => {
                button.style.transform = 'scale(0.95)';
                triggerHapticFeedback('light');
            }, { passive: true });
            
            button.addEventListener('touchend', () => {
                setTimeout(() => {
                    button.style.transform = '';
                }, 100);
            }, { passive: true });
        });
        
        // Prevent double-tap zoom on game elements
        const gameElements = document.querySelectorAll('.poker-table, .game-controls, .actions-panel');
        gameElements.forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
        
        // Enhanced mobile slider interaction
        if (elements.betSlider && isMobile) {
            let isSliding = false;
            
            elements.betSlider.addEventListener('touchstart', () => {
                isSliding = true;
                triggerHapticFeedback('light');
            });
            
            elements.betSlider.addEventListener('touchmove', debounce(() => {
                if (isSliding) {
                    triggerHapticFeedback('light');
                }
            }, 100));
            
            elements.betSlider.addEventListener('touchend', () => {
                isSliding = false;
                triggerHapticFeedback('medium');
            });
        }
    }
    
    function updateMobileLayout() {
        if (isMobile) {
            // Adjust table size based on orientation
            const isLandscape = window.innerWidth > window.innerHeight;
            const table = document.querySelector('.poker-table');
            
            if (table) {
                if (isLandscape) {
                    table.style.height = '300px';
                    table.style.minHeight = '300px';
                } else {
                    table.style.height = '350px';
                    table.style.minHeight = '350px';
                }
            }
            
            // Adjust Pro View height for landscape
            const proAnalytics = document.querySelector('.pro-analytics');
            if (proAnalytics && gameState.viewMode === 'pro') {
                if (isLandscape) {
                    proAnalytics.style.height = '140px';
                } else {
                    proAnalytics.style.height = '180px';
                }
            }
        }
    }
    
    function setupEventListeners() {
        // View mode toggle
        elements.standardViewBtn?.addEventListener('click', () => setViewMode('standard'));
        elements.proViewBtn?.addEventListener('click', () => setViewMode('pro'));
        
        // Modal controls
        elements.tutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, true));
        elements.closeTutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, false));
        elements.rulesBtn?.addEventListener('click', () => toggleModal(elements.rulesModal, true));
        elements.closeRulesBtn?.addEventListener('click', () => toggleModal(elements.rulesModal, false));
        
        // Player actions
        elements.foldBtn?.addEventListener('click', () => playerAction('fold'));
        elements.callBtn?.addEventListener('click', () => playerAction('call'));
        elements.raiseBtn?.addEventListener('click', () => playerAction('raise'));
        elements.checkBtn?.addEventListener('click', () => playerAction('check'));
        
        // Betting controls
        elements.betSlider?.addEventListener('input', updateBetAmount);
        elements.betAmount?.addEventListener('change', updateBetSlider);
        elements.quickBets?.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const amount = e.target.dataset.amount;
                if (amount === 'All-in') {
                    elements.betAmount.value = gameState.balance;
                } else {
                    elements.betAmount.value = amount;
                }
                updateBetSlider();
            });
        });
        
        // Game actions
        elements.dealBtn?.addEventListener('click', startNewHand);
        elements.autoFoldBtn?.addEventListener('click', toggleAutoFold);
        elements.sitOutBtn?.addEventListener('click', toggleSitOut);
        elements.leaveBtn?.addEventListener('click', leaveTable);
        elements.difficultySelect?.addEventListener('change', changeDifficulty);
        
        // Close modals on overlay click
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    toggleModal(modal, false);
                }
            });
        });
    }
    
    function setViewMode(mode) {
        const previousMode = gameState.viewMode;
        gameState.viewMode = mode;
        localStorage.setItem('pokerViewMode', mode);
        
        // Update button states
        if (elements.standardViewBtn && elements.proViewBtn) {
            elements.standardViewBtn.classList.toggle('active', mode === 'standard');
            elements.proViewBtn.classList.toggle('active', mode === 'pro');
        }
        
        // Toggle pro view styling with mobile-specific handling
        if (mode === 'pro') {
            document.body.classList.add('pro-view-active');
            
            // Mobile-specific Pro View setup
            if (isMobile) {
                setupMobileProView();
                triggerHapticFeedback('success');
            }
            
            updateProAnalytics();
            
            // Scroll to show Pro View on mobile
            if (isMobile) {
                setTimeout(() => {
                    const proAnalytics = document.querySelector('.pro-analytics');
                    if (proAnalytics) {
                        proAnalytics.scrollIntoView({ behavior: 'smooth', block: 'end' });
                    }
                }, 300);
            }
        } else {
            document.body.classList.remove('pro-view-active');
            
            if (isMobile && previousMode === 'pro') {
                triggerHapticFeedback('light');
                
                // Scroll back to table view
                setTimeout(() => {
                    const table = document.querySelector('.poker-table');
                    if (table) {
                        table.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }, 100);
            }
        }
        
        // Update mobile layout
        updateMobileLayout();
    }
    
    function setupMobileProView() {
        const proAnalytics = document.querySelector('.pro-analytics');
        if (!proAnalytics) return;
        
        // Add swipe indicator
        if (!proAnalytics.querySelector('.swipe-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'swipe-indicator';
            indicator.style.cssText = `
                position: absolute;
                top: 6px;
                left: 50%;
                transform: translateX(-50%);
                width: 40px;
                height: 4px;
                background: rgba(255, 255, 255, 0.5);
                border-radius: 2px;
                z-index: 10;
            `;
            proAnalytics.appendChild(indicator);
        }
        
        // Add touch feedback
        proAnalytics.style.touchAction = 'pan-y';
        
        // Auto-hide after 10 seconds of inactivity on mobile
        if (gameState.proViewTimer) {
            clearTimeout(gameState.proViewTimer);
        }
        
        gameState.proViewTimer = setTimeout(() => {
            if (gameState.viewMode === 'pro' && isMobile) {
                setViewMode('standard');
            }
        }, 10000);
    }
    
    function initializeNewGame() {
        gameState.deck = createDeck();
        shuffleDeck();
        setBlindPositions();
        startNewHand();
    }
    
    function createDeck() {
        const deck = [];
        suits.forEach(suit => {
            ranks.forEach(rank => {
                deck.push({
                    rank: rank,
                    suit: suit,
                    value: rankValues[rank],
                    isRed: suit === '♥' || suit === '♦'
                });
            });
        });
        return deck;
    }
    
    function shuffleDeck() {
        for (let i = gameState.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
        }
    }
    
    function setBlindPositions() {
        // Find small blind and big blind positions relative to dealer
        const activePlayers = gameState.players.filter(p => !p.folded);
        if (activePlayers.length < 2) return;
        
        const dealerIndex = gameState.dealerPosition;
        const smallBlindIndex = (dealerIndex + 1) % gameState.players.length;
        const bigBlindIndex = (dealerIndex + 2) % gameState.players.length;
        
        // Post blinds
        gameState.players[smallBlindIndex].bet = gameState.smallBlind;
        gameState.players[smallBlindIndex].chips -= gameState.smallBlind;
        gameState.players[bigBlindIndex].bet = gameState.bigBlind;
        gameState.players[bigBlindIndex].chips -= gameState.bigBlind;
        
        gameState.pot = gameState.smallBlind + gameState.bigBlind;
        gameState.currentBetAmount = gameState.bigBlind;
        gameState.activePlayer = (bigBlindIndex + 1) % gameState.players.length;
    }
    
    function startNewHand() {
        // Reset hand state
        gameState.communityCards = [];
        gameState.currentPhase = 'pre-flop';
        gameState.pot = 0;
        gameState.currentBetAmount = 0;
        
        // Reset player states
        gameState.players.forEach(player => {
            player.cards = [];
            player.bet = 0;
            player.folded = false;
            player.status = 'active';
        });
        
        // Create new deck and shuffle
        gameState.deck = createDeck();
        shuffleDeck();
        
        // Deal hole cards
        dealHoleCards();
        
        // Set blinds
        setBlindPositions();
        
        // Update displays
        updateDisplay();
        updatePlayerCards();
        
        // Start betting round
        if (!gameState.sittingOut) {
            startBettingRound();
        }
        
        gameState.statistics.handsPlayed++;
    }
    
    function dealHoleCards() {
        // Deal 2 cards to each active player
        for (let i = 0; i < 2; i++) {
            gameState.players.forEach(player => {
                if (!player.folded) {
                    player.cards.push(gameState.deck.pop());
                }
            });
        }
    }
    
    function startBettingRound() {
        // Enable appropriate actions for human player
        updatePlayerActions();
        
        // Start AI decision making for current player if not human
        if (!gameState.players[gameState.activePlayer].isHuman) {
            setTimeout(() => processAIAction(), 1000);
        }
    }
    
    function playerAction(action) {
        const player = gameState.players[0]; // Human player is always index 0
        
        // Add haptic feedback for mobile
        if (isMobile) {
            const feedbackType = action === 'fold' ? 'error' : 
                                action === 'raise' ? 'heavy' : 'medium';
            triggerHapticFeedback(feedbackType);
        }
        
        // Visual feedback for action
        const actionElement = document.querySelector(`#${action}Btn`);
        if (actionElement) {
            actionElement.style.transform = 'scale(0.95)';
            setTimeout(() => {
                actionElement.style.transform = '';
            }, 150);
        }
        
        switch (action) {
            case 'fold':
                player.folded = true;
                player.status = 'folded';
                addToHandHistory(`You folded`);
                
                // Visual feedback for folding
                if (isMobile) {
                    const playerSeat = document.getElementById('player0');
                    if (playerSeat) {
                        playerSeat.style.opacity = '0.5';
                        playerSeat.style.filter = 'grayscale(100%)';
                    }
                }
                break;
                
            case 'call':
                const callAmount = gameState.currentBetAmount - player.bet;
                if (player.chips >= callAmount) {
                    const oldChips = player.chips;
                    player.bet += callAmount;
                    player.chips -= callAmount;
                    gameState.pot += callAmount;
                    addToHandHistory(`You called ${callAmount} GA`);
                    
                    // Animate chip movement on mobile
                    if (isMobile) {
                        animateChipMovement(oldChips, player.chips, callAmount);
                    }
                }
                break;
                
            case 'raise':
                const raiseAmount = parseInt(elements.betAmount?.value || gameState.minRaise);
                const totalBet = gameState.currentBetAmount + raiseAmount;
                if (player.chips >= totalBet - player.bet) {
                    const oldChips = player.chips;
                    const addAmount = totalBet - player.bet;
                    player.chips -= addAmount;
                    player.bet = totalBet;
                    gameState.pot += addAmount;
                    gameState.currentBetAmount = totalBet;
                    addToHandHistory(`You raised to ${totalBet} GA`);
                    
                    // Animate chip movement and pot increase
                    if (isMobile) {
                        animateChipMovement(oldChips, player.chips, addAmount);
                        animatePotIncrease(addAmount);
                    }
                }
                break;
                
            case 'check':
                if (gameState.currentBetAmount === player.bet) {
                    addToHandHistory(`You checked`);
                }
                break;
        }
        
        // Update mobile display immediately
        if (isMobile && gameState.optimizedUpdate) {
            gameState.optimizedUpdate();
        } else {
            updateDisplay();
        }
        
        nextPlayer();
    }
    
    function animateChipMovement(fromAmount, toAmount, betAmount) {
        // Create visual chip animation for mobile
        const playerSeat = document.getElementById('player0');
        const potDisplay = document.querySelector('.pot-display');
        
        if (playerSeat && potDisplay) {
            const chip = document.createElement('div');
            chip.style.cssText = `
                position: absolute;
                width: 20px;
                height: 20px;
                background: var(--chip-blue);
                border-radius: 50%;
                border: 2px solid white;
                z-index: 1000;
                transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                font-size: 8px;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
            `;
            
            chip.textContent = betAmount;
            
            // Position at player seat
            const playerRect = playerSeat.getBoundingClientRect();
            const potRect = potDisplay.getBoundingClientRect();
            
            chip.style.left = playerRect.left + 'px';
            chip.style.top = playerRect.top + 'px';
            
            document.body.appendChild(chip);
            
            // Animate to pot
            setTimeout(() => {
                chip.style.left = potRect.left + potRect.width / 2 - 10 + 'px';
                chip.style.top = potRect.top + potRect.height / 2 - 10 + 'px';
                chip.style.transform = 'scale(0.5)';
                chip.style.opacity = '0';
            }, 100);
            
            // Remove after animation
            setTimeout(() => {
                if (chip.parentNode) {
                    chip.parentNode.removeChild(chip);
                }
            }, 700);
        }
    }
    
    function animatePotIncrease(amount) {
        const potAmount = document.getElementById('potAmount');
        if (potAmount) {
            // Create floating amount indicator
            const floater = document.createElement('div');
            floater.style.cssText = `
                position: absolute;
                color: var(--success-color);
                font-weight: bold;
                font-size: 14px;
                z-index: 1000;
                pointer-events: none;
                transition: all 0.8s ease-out;
            `;
            floater.textContent = `+${amount} GA`;
            
            const potRect = potAmount.getBoundingClientRect();
            floater.style.left = potRect.left + potRect.width / 2 + 'px';
            floater.style.top = potRect.top - 20 + 'px';
            
            document.body.appendChild(floater);
            
            setTimeout(() => {
                floater.style.transform = 'translateY(-30px)';
                floater.style.opacity = '0';
            }, 100);
            
            setTimeout(() => {
                if (floater.parentNode) {
                    floater.parentNode.removeChild(floater);
                }
            }, 900);
        }
    }
    
    function processAIAction() {
        const player = gameState.players[gameState.activePlayer];
        if (!player || player.folded || player.isHuman) return;
        
        const personality = gameState.aiPersonalities[player.difficulty];
        const handStrength = evaluateHandStrength(player.cards, gameState.communityCards);
        const action = decideAIAction(player, handStrength, personality);
        
        executeAIAction(player, action);
        
        setTimeout(() => {
            nextPlayer();
        }, 1500);
    }
    
    function decideAIAction(player, handStrength, personality) {
        const random = Math.random() * 100;
        const callAmount = gameState.currentBetAmount - player.bet;
        
        // Simple AI decision logic based on hand strength and personality
        if (handStrength > 0.8 || (random < personality.bluffFreq && handStrength > 0.3)) {
            // Strong hand or bluff - raise
            return { action: 'raise', amount: gameState.bigBlind * 2 };
        } else if (handStrength > 0.5 || random < personality.tightness) {
            // Decent hand - call or check
            return callAmount > 0 ? { action: 'call' } : { action: 'check' };
        } else {
            // Weak hand - fold
            return { action: 'fold' };
        }
    }
    
    function executeAIAction(player, action) {
        switch (action.action) {
            case 'fold':
                player.folded = true;
                player.status = 'folded';
                addToHandHistory(`${player.name} folded`);
                break;
                
            case 'call':
                const callAmount = gameState.currentBetAmount - player.bet;
                if (player.chips >= callAmount) {
                    player.bet += callAmount;
                    player.chips -= callAmount;
                    gameState.pot += callAmount;
                    addToHandHistory(`${player.name} called ${callAmount} GA`);
                }
                break;
                
            case 'raise':
                const raiseAmount = action.amount || gameState.bigBlind * 2;
                const totalBet = gameState.currentBetAmount + raiseAmount;
                if (player.chips >= totalBet - player.bet) {
                    const addAmount = totalBet - player.bet;
                    player.chips -= addAmount;
                    player.bet = totalBet;
                    gameState.pot += addAmount;
                    gameState.currentBetAmount = totalBet;
                    addToHandHistory(`${player.name} raised to ${totalBet} GA`);
                }
                break;
                
            case 'check':
                addToHandHistory(`${player.name} checked`);
                break;
        }
        
        updateDisplay();
    }
    
    function nextPlayer() {
        // Find next active player
        let nextIndex = (gameState.activePlayer + 1) % gameState.players.length;
        let found = false;
        
        while (!found) {
            if (!gameState.players[nextIndex].folded) {
                gameState.activePlayer = nextIndex;
                found = true;
            } else {
                nextIndex = (nextIndex + 1) % gameState.players.length;
                // Prevent infinite loop
                if (nextIndex === gameState.activePlayer) break;
            }
        }
        
        // Check if betting round is complete
        if (isBettingRoundComplete()) {
            advancePhase();
        } else {
            updatePlayerActions();
            if (!gameState.players[gameState.activePlayer].isHuman) {
                setTimeout(() => processAIAction(), 1000);
            }
        }
    }
    
    function isBettingRoundComplete() {
        const activePlayers = gameState.players.filter(p => !p.folded);
        return activePlayers.every(p => p.bet === gameState.currentBetAmount);
    }
    
    function advancePhase() {
        switch (gameState.currentPhase) {
            case 'pre-flop':
                dealFlop();
                gameState.currentPhase = 'flop';
                break;
            case 'flop':
                dealTurn();
                gameState.currentPhase = 'turn';
                break;
            case 'turn':
                dealRiver();
                gameState.currentPhase = 'river';
                break;
            case 'river':
                showdown();
                return;
        }
        
        // Reset betting for new phase
        gameState.currentBetAmount = 0;
        gameState.players.forEach(p => p.bet = 0);
        gameState.activePlayer = (gameState.dealerPosition + 1) % gameState.players.length;
        
        updateDisplay();
        startBettingRound();
    }
    
    function dealFlop() {
        // Burn one card, then deal 3 community cards
        gameState.deck.pop(); // Burn card
        gameState.communityCards.push(gameState.deck.pop());
        gameState.communityCards.push(gameState.deck.pop());
        gameState.communityCards.push(gameState.deck.pop());
        updateCommunityCards();
    }
    
    function dealTurn() {
        // Burn one card, then deal 1 community card
        gameState.deck.pop(); // Burn card
        gameState.communityCards.push(gameState.deck.pop());
        updateCommunityCards();
    }
    
    function dealRiver() {
        // Burn one card, then deal 1 community card
        gameState.deck.pop(); // Burn card
        gameState.communityCards.push(gameState.deck.pop());
        updateCommunityCards();
    }
    
    function showdown() {
        const activePlayers = gameState.players.filter(p => !p.folded);
        const hands = activePlayers.map(player => ({
            player: player,
            hand: evaluateBestHand(player.cards, gameState.communityCards),
            strength: evaluateHandStrength(player.cards, gameState.communityCards)
        }));
        
        // Sort by hand strength (highest first)
        hands.sort((a, b) => b.strength - a.strength);
        
        // Award pot to winner(s)
        const winners = hands.filter(h => h.strength === hands[0].strength);
        const winAmount = Math.floor(gameState.pot / winners.length);
        
        winners.forEach(winner => {
            winner.player.chips += winAmount;
            if (winner.player.isHuman) {
                gameState.statistics.handsWon++;
                gameState.statistics.totalWon += winAmount;
            }
        });
        
        // Update statistics
        if (gameState.pot > gameState.statistics.biggestPot) {
            gameState.statistics.biggestPot = gameState.pot;
        }
        
        // Show results
        const winnerNames = winners.map(w => w.player.name).join(', ');
        addToHandHistory(`${winnerNames} won ${gameState.pot} GA with ${hands[0].hand.name}`);
        
        // Reset for next hand
        gameState.pot = 0;
        gameState.dealerPosition = (gameState.dealerPosition + 1) % gameState.players.length;
        
        setTimeout(() => {
            if (!gameState.sittingOut) {
                startNewHand();
            }
        }, 3000);
    }
    
    function evaluateBestHand(holeCards, communityCards) {
        // Combine hole cards with community cards
        const allCards = [...holeCards, ...communityCards];
        
        // Generate all possible 5-card combinations
        const combinations = getCombinations(allCards, 5);
        
        // Evaluate each combination and return the best one
        let bestHand = null;
        let bestRank = -1;
        
        combinations.forEach(combo => {
            const hand = evaluateHand(combo);
            if (hand.rank > bestRank) {
                bestRank = hand.rank;
                bestHand = hand;
            }
        });
        
        return bestHand;
    }
    
    function evaluateHand(cards) {
        // Sort cards by value (highest first)
        cards.sort((a, b) => b.value - a.value);
        
        const ranks = cards.map(c => c.value);
        const suits = cards.map(c => c.suit);
        
        // Check for flush
        const isFlush = suits.every(suit => suit === suits[0]);
        
        // Check for straight
        const isStraight = ranks.every((rank, i) => i === 0 || rank === ranks[i-1] - 1) ||
                          (ranks[0] === 14 && ranks[1] === 5 && ranks[2] === 4 && ranks[3] === 3 && ranks[4] === 2); // A-5 straight
        
        // Count rank frequencies
        const rankCounts = {};
        ranks.forEach(rank => {
            rankCounts[rank] = (rankCounts[rank] || 0) + 1;
        });
        
        const counts = Object.values(rankCounts).sort((a, b) => b - a);
        
        // Determine hand type
        if (isFlush && isStraight && ranks[0] === 14) {
            return { rank: 9, name: 'Royal Flush', cards };
        } else if (isFlush && isStraight) {
            return { rank: 8, name: 'Straight Flush', cards };
        } else if (counts[0] === 4) {
            return { rank: 7, name: 'Four of a Kind', cards };
        } else if (counts[0] === 3 && counts[1] === 2) {
            return { rank: 6, name: 'Full House', cards };
        } else if (isFlush) {
            return { rank: 5, name: 'Flush', cards };
        } else if (isStraight) {
            return { rank: 4, name: 'Straight', cards };
        } else if (counts[0] === 3) {
            return { rank: 3, name: 'Three of a Kind', cards };
        } else if (counts[0] === 2 && counts[1] === 2) {
            return { rank: 2, name: 'Two Pair', cards };
        } else if (counts[0] === 2) {
            return { rank: 1, name: 'One Pair', cards };
        } else {
            return { rank: 0, name: 'High Card', cards };
        }
    }
    
    function evaluateHandStrength(holeCards, communityCards) {
        if (holeCards.length < 2) return 0;
        
        const bestHand = evaluateBestHand(holeCards, communityCards);
        
        // Normalize hand strength to 0-1 scale
        return (bestHand.rank + 1) / 10;
    }
    
    function getCombinations(arr, size) {
        if (size > arr.length) return [];
        if (size === 1) return arr.map(el => [el]);
        
        const combinations = [];
        for (let i = 0; i < arr.length - size + 1; i++) {
            const head = arr[i];
            const tailCombinations = getCombinations(arr.slice(i + 1), size - 1);
            tailCombinations.forEach(combination => {
                combinations.push([head, ...combination]);
            });
        }
        return combinations;
    }
    
    function calculateWinProbability(holeCards, communityCards) {
        if (holeCards.length < 2) return 0;
        
        // Simple approximation based on hand strength
        const strength = evaluateHandStrength(holeCards, communityCards);
        const phase = gameState.currentPhase;
        
        // Adjust probability based on game phase
        let baseProbability = strength * 100;
        
        switch (phase) {
            case 'pre-flop':
                baseProbability *= 0.6; // More uncertainty pre-flop
                break;
            case 'flop':
                baseProbability *= 0.8;
                break;
            case 'turn':
                baseProbability *= 0.9;
                break;
            case 'river':
                baseProbability *= 1.0; // Most certainty at river
                break;
        }
        
        return Math.min(Math.max(baseProbability, 5), 95); // Keep between 5-95%
    }
    
    function updateDisplay() {
        // Update pot
        if (elements.potAmount) {
            elements.potAmount.textContent = `${gameState.pot} GA`;
        }
        
        // Update mobile status bar
        updateMobileStatusBar();
        
        // Update player information
        updatePlayerInfo();
        
        // Update game phase
        if (elements.roundPhase) {
            elements.roundPhase.textContent = gameState.currentPhase.charAt(0).toUpperCase() + gameState.currentPhase.slice(1);
        }
        
        // Update active players count
        const activePlayers = gameState.players.filter(p => !p.folded).length;
        if (elements.playersActive) {
            elements.playersActive.textContent = `${activePlayers} players active`;
        }
        
        // Update hand strength and win probability for human player
        const humanPlayer = gameState.players[0];
        if (humanPlayer.cards.length > 0) {
            const strength = evaluateHandStrength(humanPlayer.cards, gameState.communityCards);
            const winProb = calculateWinProbability(humanPlayer.cards, gameState.communityCards);
            
            updateHandStrengthDisplay(strength);
            updateWinProbabilityDisplay(winProb);
        }
        
        // Update session statistics
        updateSessionStats();
        
        // Update pro analytics if enabled
        if (gameState.viewMode === 'pro') {
            updateProAnalytics();
        }
    }
    
    function updateMobileStatusBar() {
        // Update balance with animation on change
        if (elements.mobileBalance) {
            const currentBalance = parseInt(elements.mobileBalance.textContent) || 0;
            if (currentBalance !== gameState.balance) {
                animateNumberChange(elements.mobileBalance, currentBalance, gameState.balance);
            }
        }
        
        if (elements.mobilePlayersCount) {
            const activePlayers = gameState.players.filter(p => !p.folded).length;
            elements.mobilePlayersCount.textContent = activePlayers;
        }
        
        // Update pot with animation
        if (elements.mobilePotSize) {
            const currentPot = parseInt(elements.mobilePotSize.textContent) || 0;
            if (currentPot !== gameState.pot) {
                animateNumberChange(elements.mobilePotSize, currentPot, gameState.pot);
            }
        }
        
        const humanPlayer = gameState.players[0];
        if (elements.mobileWinChance && humanPlayer.cards.length > 0) {
            const winProb = calculateWinProbability(humanPlayer.cards, gameState.communityCards);
            const currentProb = parseInt(elements.mobileWinChance.textContent) || 0;
            const newProb = Math.round(winProb);
            
            if (currentProb !== newProb) {
                animateNumberChange(elements.mobileWinChance, currentProb, newProb, '%');
                
                // Add color coding for mobile
                if (newProb >= 70) {
                    elements.mobileWinChance.style.color = 'var(--success-color)';
                } else if (newProb >= 40) {
                    elements.mobileWinChance.style.color = 'var(--warning-color)';
                } else {
                    elements.mobileWinChance.style.color = 'var(--danger-color)';
                }
            }
        }
    }
    
    function animateNumberChange(element, from, to, suffix = '') {
        if (!element || from === to) return;
        
        const duration = 500;
        const steps = 20;
        const stepValue = (to - from) / steps;
        const stepDuration = duration / steps;
        
        let currentStep = 0;
        const timer = setInterval(() => {
            currentStep++;
            const currentValue = from + (stepValue * currentStep);
            
            if (currentStep >= steps) {
                clearInterval(timer);
                element.textContent = to + suffix;
            } else {
                element.textContent = Math.round(currentValue) + suffix;
            }
        }, stepDuration);
        
        // Add visual feedback for significant changes
        if (Math.abs(to - from) > 50) {
            element.style.transform = 'scale(1.1)';
            setTimeout(() => {
                element.style.transform = '';
            }, 200);
        }
    }
    
    function updatePlayerInfo() {
        gameState.players.forEach((player, index) => {
            const playerElement = document.getElementById(`player${index}`);
            if (playerElement) {
                const nameEl = playerElement.querySelector('.player-name');
                const chipsEl = playerElement.querySelector('.player-chips');
                const statusEl = playerElement.querySelector('.player-status');
                
                if (chipsEl) chipsEl.textContent = `${player.chips} GA`;
                if (statusEl) statusEl.textContent = player.status;
                
                // Update visual states
                playerElement.classList.toggle('folded', player.folded);
                playerElement.classList.toggle('active', gameState.activePlayer === index);
            }
        });
    }
    
    function updatePlayerCards() {
        const humanPlayer = gameState.players[0];
        const playerCard1 = document.getElementById('playerCard1');
        const playerCard2 = document.getElementById('playerCard2');
        
        if (humanPlayer.cards.length >= 2) {
            displayCardInSlot(playerCard1, humanPlayer.cards[0]);
            displayCardInSlot(playerCard2, humanPlayer.cards[1]);
        }
    }
    
    function updateCommunityCards() {
        gameState.communityCards.forEach((card, index) => {
            if (elements.communityCards[index]) {
                displayCardInSlot(elements.communityCards[index], card);
            }
        });
    }
    
    function displayCardInSlot(slot, card) {
        if (!slot || !card) return;
        
        slot.innerHTML = `
            <div class="card ${card.isRed ? 'card-red' : 'card-black'}">
                <div class="card-value">${card.rank}</div>
                <div class="card-suit">${card.suit}</div>
            </div>
        `;
    }
    
    function updateHandStrengthDisplay(strength) {
        if (!elements.handStrength) return;
        
        const strengthBars = elements.handStrength.querySelectorAll('.strength-bar');
        const strengthLabel = elements.handStrength.querySelector('.strength-label');
        
        // Update strength bars
        strengthBars.forEach((bar, index) => {
            bar.classList.toggle('active', index < Math.ceil(strength * 5));
        });
        
        // Update hand name
        const humanPlayer = gameState.players[0];
        if (humanPlayer.cards.length > 0) {
            const bestHand = evaluateBestHand(humanPlayer.cards, gameState.communityCards);
            if (strengthLabel) {
                strengthLabel.textContent = `Hand: ${bestHand.name}`;
            }
        }
    }
    
    function updateWinProbabilityDisplay(probability) {
        if (elements.winProbability) {
            const probValue = elements.winProbability.querySelector('.prob-value');
            if (probValue) {
                probValue.textContent = `${Math.round(probability)}%`;
                
                // Color code based on probability
                if (probability >= 70) {
                    probValue.style.color = 'var(--success-color)';
                } else if (probability >= 40) {
                    probValue.style.color = 'var(--warning-color)';
                } else {
                    probValue.style.color = 'var(--danger-color)';
                }
            }
        }
    }
    
    function updatePlayerActions() {
        const humanPlayer = gameState.players[0];
        const isPlayerTurn = gameState.activePlayer === 0;
        const callAmount = gameState.currentBetAmount - humanPlayer.bet;
        
        // Enable/disable action buttons
        if (elements.foldBtn) elements.foldBtn.disabled = !isPlayerTurn || humanPlayer.folded;
        if (elements.callBtn) {
            elements.callBtn.disabled = !isPlayerTurn || humanPlayer.folded || callAmount <= 0;
            if (elements.callAmount) elements.callAmount.textContent = callAmount;
        }
        if (elements.raiseBtn) elements.raiseBtn.disabled = !isPlayerTurn || humanPlayer.folded;
        if (elements.checkBtn) elements.checkBtn.disabled = !isPlayerTurn || humanPlayer.folded || callAmount > 0;
        
        // Update next action display
        if (elements.nextAction) {
            if (isPlayerTurn && !humanPlayer.folded) {
                elements.nextAction.textContent = 'Your turn';
            } else if (!humanPlayer.folded) {
                elements.nextAction.textContent = `${gameState.players[gameState.activePlayer].name}'s turn`;
            } else {
                elements.nextAction.textContent = 'You folded';
            }
        }
    }
    
    function updateBetAmount() {
        if (elements.betSlider && elements.betAmount) {
            elements.betAmount.value = elements.betSlider.value;
        }
    }
    
    function updateBetSlider() {
        if (elements.betSlider && elements.betAmount) {
            elements.betSlider.value = elements.betAmount.value;
        }
    }
    
    function updateSessionStats() {
        const stats = gameState.statistics;
        const sessionTime = Math.floor((Date.now() - stats.sessionStartTime) / 1000);
        const minutes = Math.floor(sessionTime / 60);
        const seconds = sessionTime % 60;
        
        if (elements.handsPlayed) elements.handsPlayed.textContent = stats.handsPlayed;
        if (elements.handsWon) elements.handsWon.textContent = stats.handsWon;
        if (elements.winRate) {
            const winRate = stats.handsPlayed > 0 ? (stats.handsWon / stats.handsPlayed * 100) : 0;
            elements.winRate.textContent = `${winRate.toFixed(1)}%`;
        }
        if (elements.netProfit) elements.netProfit.textContent = `${stats.netProfit} GA`;
        if (elements.biggestPot) elements.biggestPot.textContent = `${stats.biggestPot} GA`;
        if (elements.sessionTime) elements.sessionTime.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    function updateProAnalytics() {
        // Update pro statistics (simplified calculations)
        const stats = gameState.statistics;
        
        if (elements.vpipStat) {
            // VPIP: Voluntarily Put $ In Pot
            const vpip = stats.handsPlayed > 0 ? (stats.handsWon / stats.handsPlayed * 50) : 0;
            elements.vpipStat.textContent = `${Math.round(vpip)}%`;
        }
        
        if (elements.pfrStat) {
            // PFR: Pre-Flop Raise
            const pfr = stats.handsPlayed > 0 ? (stats.handsWon / stats.handsPlayed * 30) : 0;
            elements.pfrStat.textContent = `${Math.round(pfr)}%`;
        }
        
        if (elements.aggressionStat) {
            // Aggression factor
            const aggression = stats.handsPlayed > 0 ? (stats.totalWon / Math.max(stats.totalWagered, 1)) : 0;
            elements.aggressionStat.textContent = aggression.toFixed(1);
        }
        
        if (elements.handsPerHour) {
            const sessionTime = (Date.now() - stats.sessionStartTime) / (1000 * 60 * 60); // hours
            const handsPerHour = sessionTime > 0 ? Math.round(stats.handsPlayed / sessionTime) : 0;
            elements.handsPerHour.textContent = handsPerHour;
        }
    }
    
    function addToHandHistory(message) {
        gameState.handHistory.unshift({
            message: message,
            timestamp: Date.now(),
            phase: gameState.currentPhase
        });
        
        // Keep only last 20 entries
        if (gameState.handHistory.length > 20) {
            gameState.handHistory = gameState.handHistory.slice(0, 20);
        }
        
        // Update pro view hand history display
        if (elements.handHistoryList && gameState.viewMode === 'pro') {
            updateHandHistoryDisplay();
        }
    }
    
    function updateHandHistoryDisplay() {
        if (!elements.handHistoryList) return;
        
        elements.handHistoryList.innerHTML = '';
        gameState.handHistory.slice(0, 5).forEach(entry => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div class="history-hand">${entry.phase}</div>
                <div class="history-result">${entry.message}</div>
                <div class="history-action">${new Date(entry.timestamp).toLocaleTimeString()}</div>
            `;
            elements.handHistoryList.appendChild(historyItem);
        });
    }
    
    function toggleAutoFold() {
        gameState.autoFold = !gameState.autoFold;
        if (elements.autoFoldBtn) {
            elements.autoFoldBtn.textContent = gameState.autoFold ? 'Auto-Fold: ON' : 'Auto-Fold: OFF';
            elements.autoFoldBtn.style.background = gameState.autoFold ? 'var(--danger-color)' : 'var(--warning-color)';
        }
    }
    
    function toggleSitOut() {
        gameState.sittingOut = !gameState.sittingOut;
        if (elements.sitOutBtn) {
            elements.sitOutBtn.textContent = gameState.sittingOut ? 'Sitting Out' : 'Sit Out';
            elements.sitOutBtn.style.background = gameState.sittingOut ? 'var(--danger-color)' : 'var(--accent-blue)';
        }
        
        if (gameState.sittingOut) {
            gameState.players[0].status = 'sitting out';
        } else {
            gameState.players[0].status = 'active';
        }
    }
    
    function leaveTable() {
        if (confirm('Are you sure you want to leave the table?')) {
            // In a real implementation, this would return to the lobby
            window.location.href = 'index.html';
        }
    }
    
    function changeDifficulty() {
        const difficulty = elements.difficultySelect?.value;
        if (difficulty) {
            // Update AI players with new difficulty
            gameState.players.slice(1).forEach(player => {
                player.difficulty = difficulty;
            });
        }
    }
    
    function toggleModal(modal, show) {
        if (!modal) return;
        modal.classList.toggle('hidden', !show);
    }
    
    function saveGameState() {
        try {
            const saveData = {
                balance: gameState.balance,
                statistics: gameState.statistics,
                viewMode: gameState.viewMode
            };
            localStorage.setItem('pokerPalaceGameState', JSON.stringify(saveData));
        } catch (e) {
            console.warn('Could not save game state:', e);
        }
    }
    
    function loadGameState() {
        try {
            const saved = localStorage.getItem('pokerPalaceGameState');
            if (saved) {
                const saveData = JSON.parse(saved);
                if (saveData.balance !== undefined) gameState.balance = saveData.balance;
                if (saveData.statistics) gameState.statistics = { ...gameState.statistics, ...saveData.statistics };
                if (saveData.viewMode) gameState.viewMode = saveData.viewMode;
                
                // Apply loaded view mode
                setViewMode(gameState.viewMode);
            }
        } catch (e) {
            console.warn('Could not load game state:', e);
        }
    }
    
    // Auto-save game state periodically
    setInterval(saveGameState, 30000); // Save every 30 seconds
    
    // Update display every second for timers
    setInterval(() => {
        updateSessionStats();
    }, 1000);
});