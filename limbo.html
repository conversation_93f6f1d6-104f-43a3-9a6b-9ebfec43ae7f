<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limbo - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/limbo.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div id="headerWinLossIndicator" class="header-win-loss-indicator">
                    <!-- Will be filled by JS when win/loss occurs -->
                </div>
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="limbo-container">
            <!-- Game Title and Back Button -->
            <div class="limbo-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">LIMBO</h1>
                <div class="view-toggle">
                    <button id="standardView" class="view-btn active">Standard</button>
                    <button id="proView" class="view-btn">Pro View</button>
                </div>
            </div>

            <!-- Mobile Wallet Display -->
            <div class="mobile-wallet">
                <div class="wallet-balance">
                    <i class="fas fa-wallet"></i>
                    <span id="mobileBalanceValue">1000 GA</span>
                </div>
                <div class="mobile-last-win">
                    <i class="fas fa-trophy"></i>
                    <span id="mobileLastWin">Last: --</span>
                </div>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <!-- Left Column: Controls and Stats -->
                <div class="dashboard-left">
                    <!-- Balance Display -->
                    <div class="balance-display">
                        <div class="balance-label">Balance</div>
                        <div id="balanceValue" class="balance-value">1000 GA</div>
                        <div id="winLossIndicator" class="win-loss-indicator"></div>
                    </div>
                    
                    <!-- Betting Controls -->
                    <div class="betting-controls">
                        <div class="bet-amount-control">
                            <label for="betAmount">Bet Amount (GA)</label>
                            <div class="bet-input-group">
                                <button class="bet-btn" id="decreaseBet">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="betAmount" class="bet-input" value="10" min="1" step="1">
                                <button class="bet-btn" id="increaseBet">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Bet Presets -->
                        <div class="bet-presets">
                            <button class="preset-btn" data-amount="10">10</button>
                            <button class="preset-btn" data-amount="50">50</button>
                            <button class="preset-btn" data-amount="100">100</button>
                            <button class="preset-btn" data-amount="500">500</button>
                            <button class="preset-btn max" data-amount="max">MAX</button>
                        </div>
                    </div>
                    
                    <!-- Target Multiplier */
                    <div class="target-controls">
                        <div class="target-input-group">
                            <label for="targetMultiplier">Target Multiplier</label>
                            <div class="multiplier-input-group">
                                <button class="multiplier-btn" id="decreaseMultiplier">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="targetMultiplier" class="multiplier-input" 
                                       min="1.00" step="0.01" value="2.00">
                                <button class="multiplier-btn" id="increaseMultiplier">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Multiplier Presets -->
                        <div class="multiplier-presets">
                            <button class="multiplier-preset-btn" data-multiplier="1.5">1.5×</button>
                            <button class="multiplier-preset-btn active" data-multiplier="2.0">2.0×</button>
                            <button class="multiplier-preset-btn" data-multiplier="5.0">5.0×</button>
                            <button class="multiplier-preset-btn" data-multiplier="10.0">10×</button>
                        </div>
                    </div>
                    
                    <!-- Direction Selection */
                    <div class="direction-controls">
                        <label style="color: #f5f5f5; font-weight: 600; margin-bottom: 8px; display: block;">Direction</label>
                    <div class="direction-selection">
                        <button id="overBtn" class="direction-btn over-btn active" data-direction="over">
                            <div class="direction-icon">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="direction-label">OVER</div>
                            <div class="direction-info">
                                <div class="probability" id="overProbability">50.00%</div>
                                <div class="payout" id="overPayout">2.00× payout</div>
                            </div>
                        </button>
                        <button id="underBtn" class="direction-btn under-btn" data-direction="under">
                            <div class="direction-icon">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="direction-label">UNDER</div>
                            <div class="direction-info">
                                <div class="probability" id="underProbability">50.00%</div>
                                <div class="payout" id="underPayout">1.00× payout</div>
                            </div>
                        </button>
                    </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="game-actions">
                        <button id="playBtn" class="btn btn-primary play-btn">
                            <i class="fas fa-play"></i> PLAY
                        </button>
                        <button id="autoBetBtn" class="btn btn-secondary auto-btn">
                            <i class="fas fa-magic"></i> Auto
                        </button>
                        <button id="soundToggle" class="btn btn-icon">
                            <i class="fas fa-volume-up"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Right Column: Game Area -->
                <div class="dashboard-right">
                    <!-- Game Status -->
                    <div id="gameStatus" class="game-status">
                        Select your target multiplier and direction, then click PLAY!
                    </div>
                    
                    <!-- Result Display -->
                    <div class="result-display">
                        <div class="outcome-container">
                            <div class="outcome-label">Outcome</div>
                            <div id="outcomeValue" class="outcome-value">
                                <span class="outcome-text">--</span>
                            </div>
                            <div id="outcomeStatus" class="outcome-status">
                                Ready to play
                            </div>
                        </div>
                        
                        <!-- Visual Multiplier Display -->
                        <div class="multiplier-visual">
                            <div class="multiplier-scale">
                                <div class="scale-line"></div>
                                <div id="targetMarker" class="target-marker">
                                    <span>2.00×</span>
                                </div>
                                <div id="resultMarker" class="result-marker">
                                    <span>--</span>
                                </div>
                            </div>
                            <div class="scale-labels">
                                <span>1×</span>
                                <span>10×</span>
                                <span>100×</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Mobile Game Controls -->
            <div class="mobile-controls">
                <div class="mobile-direction-buttons">
                    <button id="mobileOverBtn" class="mobile-direction-btn over-btn active">
                        <i class="fas fa-arrow-up"></i>
                        <span>OVER</span>
                        <small id="mobileOverInfo">50% • 2.00×</small>
                    </button>
                    <button id="mobileUnderBtn" class="mobile-direction-btn under-btn">
                        <i class="fas fa-arrow-down"></i>
                        <span>UNDER</span>
                        <small id="mobileUnderInfo">50% • 1.00×</small>
                    </button>
                </div>
                <div class="mobile-play-actions">
                    <button id="mobilePlayBtn" class="btn btn-primary mobile-play-btn">
                        <i class="fas fa-play"></i> PLAY
                    </button>
                    <button id="mobileAutoBetBtn" class="btn btn-secondary mobile-auto-btn">
                        <i class="fas fa-magic"></i> Auto
                    </button>
                </div>
            </div>
            
            <!-- Statistics Panel (Pro View) -->
            <div class="statistics-panel">
                <h2 class="stats-title">
                    Statistics
                    <button id="statsToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="stats-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">Games Played</div>
                            <div class="stat-value" id="gamesPlayed">0</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Win Rate</div>
                            <div class="stat-value" id="winRate">0%</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Total Wagered</div>
                            <div class="stat-value" id="totalWagered">0 GA</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Net Profit</div>
                            <div class="stat-value" id="netProfit">0 GA</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Biggest Win</div>
                            <div class="stat-value" id="biggestWin">0 GA</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">Avg Multiplier</div>
                            <div class="stat-value" id="avgMultiplier">0.00×</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Provably Fair Section -->
            <div class="provably-fair">
                <h2 class="fair-title">
                    <i class="fas fa-shield-alt"></i>
                    Provably Fair
                    <button id="fairToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="fair-content">
                    <div class="fair-details">
                        <div class="fair-item">
                            <label>Client Seed:</label>
                            <span id="clientSeed" class="seed-value">Loading...</span>
                            <button id="regenerateClientSeed" class="seed-btn">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                        <div class="fair-item">
                            <label>Server Seed (Hash):</label>
                            <span id="serverSeedHash" class="seed-value">Loading...</span>
                        </div>
                        <div class="fair-item">
                            <label>Nonce:</label>
                            <span id="nonce" class="seed-value">1</span>
                        </div>
                    </div>
                    <button id="verifyBtn" class="verify-button" disabled>
                        <i class="fas fa-check-circle"></i>
                        Verify Result
                    </button>
                </div>
            </div>
            
            <!-- Game History -->
            <div class="game-history">
                <h2 class="history-title">
                    Game History
                    <button id="historyToggle" class="toggle-btn">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h2>
                <div class="history-content">
                    <div class="history-table-container">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Game</th>
                                    <th>Bet</th>
                                    <th>Target</th>
                                    <th>Result</th>
                                    <th>Direction</th>
                                    <th>Profit</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- History entries will be added here -->
                            </tbody>
                        </table>
                    </div>
                    <div class="history-placeholder" id="historyPlaceholder">
                        No games played yet
                    </div>
                </div>
            </div>
            
            <!-- Rules Modal -->
            <div id="rulesModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>How to Play Limbo</h2>
                    <div class="rules-content">
                        <h3>Game Objective</h3>
                        <p>Predict whether a randomly generated multiplier will be above or below your chosen target.</p>
                        
                        <h3>How to Play</h3>
                        <ol>
                            <li>Enter your bet amount in GA (Golden Aura currency)</li>
                            <li>Set your target multiplier (×) between 1.00 and 999.99</li>
                            <li>Choose your prediction: OVER (result > ×) or UNDER (result < ×)</li>
                            <li>Click PLAY to generate the random outcome</li>
                        </ol>
                        
                        <h3>Winning Conditions</h3>
                        <ul>
                            <li><strong>OVER Bet:</strong> Win if outcome > × with payout multiplier of ×</li>
                            <li><strong>UNDER Bet:</strong> Win if outcome < × with payout multiplier based on probability</li>
                            <li><strong>Exact Match:</strong> If outcome equals × exactly, you lose</li>
                            <li><strong>Payouts:</strong> Winnings are calculated as your bet amount × payout multiplier</li>
                            <li><strong>Currency:</strong> All bets and winnings are in GA currency</li>
                        </ul>
                        
                        <h3>Auto Betting</h3>
                        <p>Use Auto mode to place multiple bets automatically with your current settings. You can stop auto betting at any time.</p>
                        
                        <h3>Probability & Fairness</h3>
                        <p>The game uses advanced randomization to create balanced and unpredictable outcomes.</p>
                        <p>All results are generated using cryptographically secure randomness and are provably fair.</p>
                        
                        <h3>Random Generation</h3>
                        <p>Outcomes are calculated using enhanced algorithms with additional variability mechanisms to ensure a balanced distribution of results across the full range.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="assets/js/limbo.js"></script>
</body>
</html>