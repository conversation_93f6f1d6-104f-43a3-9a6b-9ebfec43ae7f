// Texas Hold'em Showdown - Tournament Edition
document.addEventListener('DOMContentLoaded', function() {
    console.log("Texas Hold'em Showdown initializing...");
    
    // UI Elements
    const elements = {
        // Screens
        tournamentSelection: document.getElementById('tournamentSelection'),
        tournamentLobby: document.getElementById('tournamentLobby'),
        gameArea: document.getElementById('gameArea'),
        
        // Header controls
        standardViewBtn: document.getElementById('standardViewBtn'),
        proViewBtn: document.getElementById('proViewBtn'),
        tutorialBtn: document.getElementById('tutorialBtn'),
        rulesBtn: document.getElementById('rulesBtn'),
        
        // Modals
        tutorialModal: document.getElementById('tutorialModal'),
        closeTutorialBtn: document.getElementById('closeTutorialBtn'),
        rulesModal: document.getElementById('rulesModal'),
        closeRulesBtn: document.getElementById('closeRulesBtn'),
        
        // Tournament selection
        tournamentCards: document.querySelectorAll('.tournament-card'),
        registerButtons: document.querySelectorAll('.register-btn'),
        playerBalance: document.getElementById('playerBalance'),
        playerRank: document.getElementById('playerRank'),
        tournamentsWon: document.getElementById('tournamentsWon'),
        
        // Tournament lobby
        lobbyTournamentName: document.getElementById('lobbyTournamentName'),
        tournamentTimeLeft: document.getElementById('tournamentTimeLeft'),
        lobbyPlayerCount: document.getElementById('lobbyPlayerCount'),
        lobbyBlindLevel: document.getElementById('lobbyBlindLevel'),
        tableCards: document.querySelectorAll('.table-card'),
        joinTableBtn: document.querySelector('.join-table-btn'),
        spectateBtns: document.querySelectorAll('.spectate-btn'),
        leaveTournamentBtn: document.querySelector('.leave-tournament-btn'),
        tournamentRulesBtn: document.querySelector('.tournament-rules-btn'),
        
        // Mobile status
        mobileBalance: document.getElementById('mobileBalance'),
        mobilePlayersCount: document.getElementById('mobilePlayersCount'),
        mobilePotSize: document.getElementById('mobilePotSize'),
        mobileWinChance: document.getElementById('mobileWinChance'),
        
        // Tournament sidebar
        tournamentLevel: document.getElementById('tournamentLevel'),
        tournamentBlinds: document.getElementById('tournamentBlinds'),
        nextLevelTime: document.getElementById('nextLevelTime'),
        playersLeft: document.getElementById('playersLeft'),
        avgStack: document.getElementById('avgStack'),
        yourStack: document.getElementById('yourStack'),
        
        // Game table
        communityCards: ['flop1', 'flop2', 'flop3', 'turn', 'river'].map(id => document.getElementById(id)),
        potAmount: document.getElementById('potAmount'),
        dealerButton: document.getElementById('dealerButton'),
        
        // Player actions
        playerActions: document.getElementById('playerActions'),
        foldBtn: document.getElementById('foldBtn'),
        callBtn: document.getElementById('callBtn'),
        raiseBtn: document.getElementById('raiseBtn'),
        checkBtn: document.getElementById('checkBtn'),
        showdownBtn: document.getElementById('showdownBtn'),
        callAmount: document.getElementById('callAmount'),
        
        // Betting controls
        betSlider: document.getElementById('betSlider'),
        betAmount: document.getElementById('betAmount'),
        quickBets: document.querySelectorAll('.quick-bet'),
        
        // Game info
        handStrength: document.getElementById('handStrength'),
        winProbability: document.getElementById('winProbability'),
        roundPhase: document.getElementById('roundPhase'),
        playersActive: document.getElementById('playersActive'),
        nextAction: document.getElementById('nextAction'),
        
        // Game actions
        fastFoldBtn: document.getElementById('fastFoldBtn'),
        autoCheckBtn: document.getElementById('autoCheckBtn'),
        sitOutBtn: document.getElementById('sitOutBtn'),
        leaveBtn: document.getElementById('leaveBtn'),
        
        // Table options
        chatBtn: document.getElementById('chatBtn'),
        noteBtn: document.getElementById('noteBtn'),
        statsBtn: document.getElementById('statsBtn'),
        settingsBtn: document.getElementById('settingsBtn'),
        
        // Pro analytics
        tournamentPosition: document.getElementById('tournamentPosition'),
        levelTimeLeft: document.getElementById('levelTimeLeft'),
        mZoneValue: document.getElementById('mZoneValue'),
        icmValue: document.getElementById('icmValue'),
        
        // Showdown overlay
        showdownOverlay: document.getElementById('showdownOverlay'),
        showdownCard1: document.getElementById('showdownCard1'),
        showdownCard2: document.getElementById('showdownCard2'),
        showdownCommunity1: document.getElementById('showdownCommunity1'),
        showdownCommunity2: document.getElementById('showdownCommunity2'),
        showdownCommunity3: document.getElementById('showdownCommunity3'),
        showdownCommunity4: document.getElementById('showdownCommunity4'),
        showdownCommunity5: document.getElementById('showdownCommunity5'),
        showdownHandName: document.getElementById('showdownHandName'),
        showdownResultText: document.getElementById('showdownResultText'),
        bonusAmount: document.getElementById('bonusAmount'),
        showdownContinueBtn: document.getElementById('showdownContinueBtn'),
        
        // Elimination overlay
        eliminationOverlay: document.getElementById('eliminationOverlay'),
        eliminationPosition: document.getElementById('eliminationPosition'),
        eliminationPrize: document.getElementById('eliminationPrize'),
        tournamentDuration: document.getElementById('tournamentDuration'),
        lastHandCard1: document.getElementById('lastHandCard1'),
        lastHandCard2: document.getElementById('lastHandCard2'),
        opponentLastCard1: document.getElementById('opponentLastCard1'),
        opponentLastCard2: document.getElementById('opponentLastCard2'),
        lastHandBoard1: document.getElementById('lastHandBoard1'),
        lastHandBoard2: document.getElementById('lastHandBoard2'),
        lastHandBoard3: document.getElementById('lastHandBoard3'),
        lastHandBoard4: document.getElementById('lastHandBoard4'),
        lastHandBoard5: document.getElementById('lastHandBoard5'),
        achievementsList: document.getElementById('achievementsList'),
        playAgainBtn: document.getElementById('playAgainBtn'),
        returnToLobbyBtn: document.getElementById('returnToLobbyBtn')
    };
    
    // Tournament configuration
    const tournaments = {
        sitandgo: {
            name: "Sit & Go Tournament",
            players: 6,
            buyIn: 200,
            prizePool: 1200,
            blindLevels: [
                { small: 10, big: 20, duration: 15 * 60 }, // 15 minutes
                { small: 15, big: 30, duration: 15 * 60 },
                { small: 25, big: 50, duration: 15 * 60 },
                { small: 50, big: 100, duration: 15 * 60 },
                { small: 75, big: 150, duration: 15 * 60 },
                { small: 100, big: 200, duration: 15 * 60 },
                { small: 150, big: 300, duration: 15 * 60 },
                { small: 200, big: 400, duration: 15 * 60 },
                { small: 300, big: 600, duration: 15 * 60 },
                { small: 400, big: 800, duration: 15 * 60 },
                { small: 500, big: 1000, duration: 15 * 60 }
            ],
            startingChips: 1500,
            payouts: [
                { position: "1st", percentage: 0.5, amount: 600 },
                { position: "2nd", percentage: 0.3, amount: 360 },
                { position: "3rd", percentage: 0.2, amount: 240 }
            ],
            tables: 1,
            status: "starting soon",
            currentPlayers: 3,
            timeToStart: "On Registration"
        },
        daily: {
            name: "Daily Showdown",
            players: 100,
            buyIn: 500,
            prizePool: 50000,
            blindLevels: [
                { small: 10, big: 20, duration: 15 * 60 },
                { small: 15, big: 30, duration: 15 * 60 },
                { small: 25, big: 50, duration: 15 * 60 },
                { small: 50, big: 100, duration: 15 * 60 },
                { small: 75, big: 150, duration: 15 * 60 },
                { small: 100, big: 200, duration: 15 * 60 },
                { small: 150, big: 300, duration: 15 * 60 },
                { small: 200, big: 400, duration: 15 * 60 },
                { small: 300, big: 600, duration: 15 * 60 },
                { small: 400, big: 800, duration: 15 * 60 },
                { small: 500, big: 1000, duration: 15 * 60 },
                { small: 750, big: 1500, duration: 15 * 60 },
                { small: 1000, big: 2000, duration: 15 * 60 },
                { small: 1500, big: 3000, duration: 15 * 60 },
                { small: 2000, big: 4000, duration: 15 * 60 }
            ],
            startingChips: 5000,
            payouts: [
                { position: "1st", percentage: 0.3, amount: 15000 },
                { position: "2nd", percentage: 0.2, amount: 10000 },
                { position: "3rd", percentage: 0.15, amount: 7500 },
                { position: "4-6th", percentage: 0.1, amount: 5000 },
                { position: "7-9th", percentage: 0.05, amount: 2500 },
                { position: "10-15th", percentage: 0.02, amount: 1000 }
            ],
            tables: 17,
            status: "starting soon",
            currentPlayers: 65,
            timeToStart: "45 min"
        },
        weekly: {
            name: "Weekly Championship",
            players: 1000,
            buyIn: 1000,
            prizePool: 1000000,
            blindLevels: [
                { small: 10, big: 20, duration: 20 * 60 },
                { small: 15, big: 30, duration: 20 * 60 },
                { small: 25, big: 50, duration: 20 * 60 },
                { small: 50, big: 100, duration: 20 * 60 },
                { small: 75, big: 150, duration: 20 * 60 },
                { small: 100, big: 200, duration: 20 * 60 },
                { small: 150, big: 300, duration: 20 * 60 },
                { small: 200, big: 400, duration: 20 * 60 },
                { small: 300, big: 600, duration: 20 * 60 },
                { small: 400, big: 800, duration: 20 * 60 },
                { small: 500, big: 1000, duration: 20 * 60 },
                { small: 750, big: 1500, duration: 20 * 60 },
                { small: 1000, big: 2000, duration: 20 * 60 },
                { small: 1500, big: 3000, duration: 20 * 60 },
                { small: 2000, big: 4000, duration: 20 * 60 },
                { small: 3000, big: 6000, duration: 20 * 60 },
                { small: 4000, big: 8000, duration: 20 * 60 },
                { small: 5000, big: 10000, duration: 20 * 60 },
                { small: 7500, big: 15000, duration: 20 * 60 },
                { small: 10000, big: 20000, duration: 20 * 60 }
            ],
            startingChips: 10000,
            payouts: [
                { position: "1st", percentage: 0.25, amount: 250000 },
                { position: "2nd", percentage: 0.15, amount: 150000 },
                { position: "3rd", percentage: 0.1, amount: 100000 },
                { position: "4-6th", percentage: 0.05, amount: 50000 },
                { position: "7-9th", percentage: 0.03, amount: 30000 },
                { position: "10-18th", percentage: 0.01, amount: 10000 },
                { position: "19-27th", percentage: 0.005, amount: 5000 },
                { position: "28-45th", percentage: 0.003, amount: 3000 },
                { position: "46-100th", percentage: 0.001, amount: 1000 }
            ],
            tables: 100,
            status: "scheduled",
            currentPlayers: 320,
            timeToStart: "Sunday, 8:00 PM"
        }
    };
    
    // Achievement data
    const achievements = [
        {
            id: "first-tournament",
            name: "First Timer",
            description: "Completed your first tournament",
            icon: "fas fa-star",
            earned: true
        },
        {
            id: "final-table",
            name: "Final Table",
            description: "Reached the final table",
            icon: "fas fa-table",
            earned: true
        },
        {
            id: "showdown-specialist",
            name: "Showdown Specialist",
            description: "Win 10 hands with showdown bonus",
            icon: "fas fa-eye",
            earned: false,
            progress: 3,
            required: 10
        },
        {
            id: "tournament-champion",
            name: "Tournament Champion",
            description: "Win a Daily or Weekly tournament",
            icon: "fas fa-crown",
            earned: false
        },
        {
            id: "in-the-money",
            name: "In The Money",
            description: "Finished in a paid position",
            icon: "fas fa-trophy",
            earned: false
        }
    ];
    
    // Game state
    let gameState = {
        // Player data
        playerName: "Player",
        balance: 10000,
        tournamentPosition: 15,
        
        // Current tournament
        currentTournament: null,
        tournamentPhase: "selection", // selection, lobby, playing, eliminated
        
        // Tournament tracking
        tournamentStartTime: null,
        currentLevel: 1,
        levelStartTime: null,
        levelTimeRemaining: null,
        
        // Game state
        currentTable: null,
        currentBet: 10,
        pot: 0,
        currentPhase: 'pre-flop', // pre-flop, flop, turn, river, showdown
        dealerPosition: 0,
        activePlayer: 0,
        currentBetAmount: 0,
        minRaise: 20,
        smallBlind: 10,
        bigBlind: 20,
        
        // Cards
        deck: [],
        communityCards: [],
        playerHands: [],
        
        // Players at table
        tablePlayers: [
            { id: 0, name: "You", chips: 5200, status: "active", isHuman: true, cards: [], bet: 0, folded: false, showdown: false, position: 15 },
            { id: 1, name: "CardShark", chips: 10800, status: "active", isHuman: false, difficulty: "pro", cards: [], bet: 0, folded: false, showdown: false, position: 2 },
            { id: 2, name: "PokerKing92", chips: 12500, status: "active", isHuman: false, difficulty: "shark", cards: [], bet: 0, folded: false, showdown: false, position: 1 },
            { id: 3, name: "BluffMaster", chips: 5100, status: "active", isHuman: false, difficulty: "pro", cards: [], bet: 0, folded: false, showdown: false, position: 16 },
            { id: 4, name: "AcesHigh", chips: 4950, status: "active", isHuman: false, difficulty: "novice", cards: [], bet: 0, folded: false, showdown: false, position: 17 },
            { id: 5, name: "RiverRat", chips: 3750, status: "active", isHuman: false, difficulty: "novice", cards: [], bet: 0, folded: false, showdown: false, position: 23 }
        ],
        
        // Game mode
        viewMode: 'pro', // standard, pro
        autoCheck: false,
        sittingOut: false,
        fastFold: false,
        
        // Statistics
        statistics: {
            handsPlayed: 0,
            handsWon: 0,
            showdownsWon: 0,
            totalWagered: 0,
            totalWon: 0,
            netProfit: 0,
            biggestPot: 0,
            vpip: 24,
            pfr: 18,
            aggression: 2.1,
            handsPerHour: 45,
            mZone: 13,
            icm: 4250,
            sessionStartTime: Date.now()
        },
        
        // Hand history
        handHistory: [],
        
        // AI personalities
        aiPersonalities: {
            novice: { bluffFreq: 10, tightness: 80, aggression: 20, showdownFreq: 5 },
            pro: { bluffFreq: 35, tightness: 60, aggression: 60, showdownFreq: 20 },
            shark: { bluffFreq: 60, tightness: 40, aggression: 80, showdownFreq: 40 }
        },
        
        // All-in protection
        allInProtection: {
            active: false,
            handsRemaining: 0,
            threshold: 5 // 5 big blinds
        },
        
        // Mobile detection and touch support
        isMobile: false,
        hasHapticSupport: false,
        touchStartY: 0,
        touchEndY: 0,
        lastTouchTime: 0,
        proViewTimer: null,
        
        // Showdown bonus feature
        showdownBonus: {
            active: false,
            bonusMultiplier: 2,
            eligiblePlayers: [],
            potSize: 0
        }
    };
    
    // Card and hand evaluation
    const suits = ['♠', '♥', '♦', '♣'];
    const ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const rankValues = { '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10, 'J': 11, 'Q': 12, 'K': 13, 'A': 14 };
    
    // Hand rankings
    const handRankings = [
        'High Card', 'One Pair', 'Two Pair', 'Three of a Kind', 'Straight', 
        'Flush', 'Full House', 'Four of a Kind', 'Straight Flush', 'Royal Flush'
    ];
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing Texas Hold'em Showdown...");
        detectMobile();
        setupEventListeners();
        loadGameState();
        updateDisplay();
        
        // Setup responsive listener
        window.addEventListener('resize', debounce(handleResize, 250));
    }
    
    function detectMobile() {
        gameState.isMobile = window.innerWidth <= 767;
        document.body.classList.toggle('mobile-device', gameState.isMobile);
        
        // Detect haptic support
        gameState.hasHapticSupport = 'vibrate' in navigator && gameState.isMobile;
        
        // Add mobile-specific viewport meta tag behavior
        if (gameState.isMobile) {
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }
        }
    }
    
    function handleResize() {
        const wasMobile = gameState.isMobile;
        detectMobile();
        
        if (wasMobile !== gameState.isMobile) {
            // Device type changed, update layout and features
            if (gameState.isMobile) {
                setupMobileFeatures();
            }
            updateDisplay();
            
            // Adjust Pro View if active
            if (gameState.viewMode === 'pro') {
                updateProAnalytics();
            }
        }
        
        // Update mobile layout for orientation changes
        if (gameState.isMobile) {
            updateMobileLayout();
        }
    }
    
    function updateMobileLayout() {
        if (!gameState.isMobile) return;
        
        // Adjust layout based on orientation
        const isLandscape = window.innerWidth > window.innerHeight;
        const table = document.querySelector('.poker-table');
        
        if (table) {
            if (isLandscape) {
                table.style.minHeight = '280px';
                table.style.padding = '10px';
            } else {
                table.style.minHeight = '300px';
                table.style.padding = '12px';
            }
        }
        
        // Adjust Pro View height for landscape on mobile
        const proAnalytics = document.querySelector('.pro-analytics');
        if (proAnalytics && gameState.viewMode === 'pro') {
            if (isLandscape) {
                proAnalytics.style.height = '160px';
            } else {
                proAnalytics.style.height = '240px';
            }
        }
        
        // Adjust action panel layout
        const gameActions = document.querySelector('.game-actions');
        if (gameActions) {
            if (isLandscape) {
                gameActions.style.gridTemplateColumns = 'repeat(4, 1fr)';
            } else {
                gameActions.style.gridTemplateColumns = 'repeat(2, 1fr)';
            }
        }
    }
    
    function setupEventListeners() {
        // Setup mobile features first
        if (gameState.isMobile) {
            setupMobileFeatures();
        }
        
        // View mode toggle
        elements.standardViewBtn?.addEventListener('click', () => setViewMode('standard'));
        elements.proViewBtn?.addEventListener('click', () => setViewMode('pro'));
        
        // Modal controls
        elements.tutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, true));
        elements.closeTutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, false));
        elements.rulesBtn?.addEventListener('click', () => toggleModal(elements.rulesModal, true));
        elements.closeRulesBtn?.addEventListener('click', () => toggleModal(elements.rulesModal, false));
        
        // Tournament selection
        elements.tournamentCards?.forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.classList.contains('register-btn')) {
                    selectTournament(card.dataset.tournament);
                }
            });
        });
        
        elements.registerButtons?.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const tournamentType = e.target.closest('.tournament-card').dataset.tournament;
                registerForTournament(tournamentType);
            });
        });
        
        // Tournament lobby
        elements.joinTableBtn?.addEventListener('click', joinTable);
        elements.spectateBtns?.forEach(btn => {
            btn.addEventListener('click', () => spectateTable(btn.closest('.table-card').dataset.table));
        });
        elements.leaveTournamentBtn?.addEventListener('click', leaveTournament);
        elements.tournamentRulesBtn?.addEventListener('click', () => toggleModal(elements.rulesModal, true));
        
        // Player actions
        elements.foldBtn?.addEventListener('click', () => playerAction('fold'));
        elements.callBtn?.addEventListener('click', () => playerAction('call'));
        elements.raiseBtn?.addEventListener('click', () => playerAction('raise'));
        elements.checkBtn?.addEventListener('click', () => playerAction('check'));
        elements.showdownBtn?.addEventListener('click', () => playerAction('showdown'));
        
        // Betting controls
        elements.betSlider?.addEventListener('input', updateBetAmount);
        elements.betAmount?.addEventListener('change', updateBetSlider);
        elements.quickBets?.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const amount = e.target.dataset.amount;
                if (amount === 'all-in') {
                    elements.betAmount.value = gameState.tablePlayers[0].chips;
                } else {
                    elements.betAmount.value = amount;
                }
                updateBetSlider();
            });
        });
        
        // Game actions
        elements.fastFoldBtn?.addEventListener('click', toggleFastFold);
        elements.autoCheckBtn?.addEventListener('click', toggleAutoCheck);
        elements.sitOutBtn?.addEventListener('click', toggleSitOut);
        elements.leaveBtn?.addEventListener('click', leaveTable);
        
        // Showdown overlay
        elements.showdownContinueBtn?.addEventListener('click', () => toggleModal(elements.showdownOverlay, false));
        
        // Elimination overlay
        elements.playAgainBtn?.addEventListener('click', playAgain);
        elements.returnToLobbyBtn?.addEventListener('click', returnToLobby);
        
        // Table options
        elements.chatBtn?.addEventListener('click', toggleChat);
        elements.noteBtn?.addEventListener('click', addNote);
        elements.statsBtn?.addEventListener('click', showStats);
        elements.settingsBtn?.addEventListener('click', showSettings);
        
        // Close modals on overlay click
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    toggleModal(modal, false);
                }
            });
        });
    }
    
    function setViewMode(mode) {
        const previousMode = gameState.viewMode;
        gameState.viewMode = mode;
        localStorage.setItem('txHoldemViewMode', mode);
        
        // Update button states
        if (elements.standardViewBtn && elements.proViewBtn) {
            elements.standardViewBtn.classList.toggle('active', mode === 'standard');
            elements.proViewBtn.classList.toggle('active', mode === 'pro');
        }
        
        // Toggle pro view styling with mobile-specific handling
        if (mode === 'pro') {
            document.body.classList.add('pro-view-active');
            document.body.classList.add('has-used-pro-view');
            
            // Mobile-specific Pro View setup
            if (gameState.isMobile) {
                setupMobileProView();
                triggerHapticFeedback('success');
                
                // Scroll to show Pro View indicator
                setTimeout(() => {
                    const proAnalytics = document.querySelector('.pro-analytics');
                    if (proAnalytics) {
                        // Subtle scroll effect to indicate the panel is available
                        window.scrollTo({ 
                            top: document.body.scrollHeight - window.innerHeight,
                            behavior: 'smooth' 
                        });
                    }
                }, 300);
            }
            
            updateProAnalytics();
        } else {
            document.body.classList.remove('pro-view-active');
            
            if (gameState.isMobile && previousMode === 'pro') {
                triggerHapticFeedback('light');
                
                // Clear pro view timer
                if (gameState.proViewTimer) {
                    clearTimeout(gameState.proViewTimer);
                    gameState.proViewTimer = null;
                }
                
                // Scroll back to game area
                setTimeout(() => {
                    const table = document.querySelector('.poker-table');
                    if (table) {
                        table.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }, 100);
            }
        }
    }
    
    function setupMobileFeatures() {
        console.log("Setting up mobile features...");
        
        // Setup touch gestures for Pro View
        setupProViewGestures();
        
        // Setup enhanced touch feedback
        setupTouchFeedback();
        
        // Optimize for mobile performance
        optimizeMobilePerformance();
        
        // Setup mobile-specific interactions
        setupMobileInteractions();
    }
    
    function setupProViewGestures() {
        if (!gameState.isMobile) return;
        
        // Main swipe up gesture for Pro View activation
        document.addEventListener('touchstart', handleTouchStart, { passive: true });
        document.addEventListener('touchend', handleTouchEnd, { passive: true });
        
        // Pro View panel swipe down to close
        const proAnalytics = document.querySelector('.pro-analytics');
        if (proAnalytics) {
            proAnalytics.addEventListener('touchstart', handleProViewTouchStart, { passive: true });
            proAnalytics.addEventListener('touchend', handleProViewTouchEnd, { passive: true });
        }
    }
    
    function handleTouchStart(e) {
        gameState.touchStartY = e.touches[0].clientY;
        gameState.lastTouchTime = Date.now();
    }
    
    function handleTouchEnd(e) {
        gameState.touchEndY = e.changedTouches[0].clientY;
        const touchDuration = Date.now() - gameState.lastTouchTime;
        const swipeDistance = gameState.touchStartY - gameState.touchEndY;
        
        // Swipe up gesture (at least 60px up, within 400ms)
        if (swipeDistance > 60 && touchDuration < 400 && gameState.viewMode === 'standard') {
            // Check if we're in game area (not in tournament selection/lobby)
            if (gameState.tournamentPhase === 'playing') {
                setViewMode('pro');
                triggerHapticFeedback('medium');
            }
        }
    }
    
    function handleProViewTouchStart(e) {
        e.stopPropagation();
        gameState.touchStartY = e.touches[0].clientY;
        gameState.lastTouchTime = Date.now();
    }
    
    function handleProViewTouchEnd(e) {
        e.stopPropagation();
        gameState.touchEndY = e.changedTouches[0].clientY;
        const touchDuration = Date.now() - gameState.lastTouchTime;
        const swipeDistance = gameState.touchEndY - gameState.touchStartY;
        
        // Swipe down gesture (at least 40px down, within 400ms)
        if (swipeDistance > 40 && touchDuration < 400) {
            setViewMode('standard');
            triggerHapticFeedback('light');
        }
    }
    
    function setupMobileProView() {
        if (!gameState.isMobile) return;
        
        const proAnalytics = document.querySelector('.pro-analytics');
        if (!proAnalytics) return;
        
        // Ensure touch handling is enabled
        proAnalytics.style.touchAction = 'pan-y';
        
        // Auto-hide Pro View after inactivity (mobile only)
        if (gameState.proViewTimer) {
            clearTimeout(gameState.proViewTimer);
        }
        
        gameState.proViewTimer = setTimeout(() => {
            if (gameState.viewMode === 'pro' && gameState.isMobile) {
                setViewMode('standard');
                triggerHapticFeedback('light');
            }
        }, 15000); // 15 seconds auto-hide
        
        // Reset timer on any touch interaction
        proAnalytics.addEventListener('touchstart', () => {
            if (gameState.proViewTimer) {
                clearTimeout(gameState.proViewTimer);
                gameState.proViewTimer = setTimeout(() => {
                    if (gameState.viewMode === 'pro' && gameState.isMobile) {
                        setViewMode('standard');
                    }
                }, 15000);
            }
        });
    }
    
    function setupTouchFeedback() {
        if (!gameState.isMobile) return;
        
        // Enhanced touch feedback for all interactive elements
        const interactiveElements = document.querySelectorAll(`
            .action-btn, .quick-bet, .view-btn, .register-btn, .spectate-btn, 
            .join-table-btn, .table-card, .tournament-card, .table-option-btn
        `);
        
        interactiveElements.forEach(element => {
            // Touch start feedback
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.95)';
                element.style.transition = 'transform 0.1s ease';
                triggerHapticFeedback('light');
            }, { passive: true });
            
            // Touch end feedback
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = 'transform 0.2s ease';
                }, 100);
            }, { passive: true });
            
            // Touch cancel feedback
            element.addEventListener('touchcancel', (e) => {
                element.style.transform = '';
                element.style.transition = 'transform 0.2s ease';
            }, { passive: true });
        });
        
        // Enhanced slider interaction for mobile
        if (elements.betSlider) {
            let isSliding = false;
            let slideStartTime = 0;
            
            elements.betSlider.addEventListener('touchstart', () => {
                isSliding = true;
                slideStartTime = Date.now();
                triggerHapticFeedback('light');
            }, { passive: true });
            
            elements.betSlider.addEventListener('touchmove', debounce(() => {
                if (isSliding && Date.now() - slideStartTime > 100) {
                    triggerHapticFeedback('light');
                    slideStartTime = Date.now();
                }
            }, 150), { passive: true });
            
            elements.betSlider.addEventListener('touchend', () => {
                if (isSliding) {
                    isSliding = false;
                    triggerHapticFeedback('medium');
                }
            }, { passive: true });
        }
    }
    
    function setupMobileInteractions() {
        if (!gameState.isMobile) return;
        
        // Prevent double-tap zoom on game elements
        const noZoomElements = document.querySelectorAll(`
            .poker-table, .game-controls, .actions-panel, .tournament-selection,
            .tournament-lobby, .pro-analytics
        `);
        
        noZoomElements.forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
        
        // Enhanced mobile card interactions
        const cards = document.querySelectorAll('.card, .card-slot');
        cards.forEach(card => {
            card.addEventListener('touchstart', () => {
                card.style.transform = 'scale(1.05) translateY(-2px)';
                card.style.transition = 'transform 0.2s ease';
                triggerHapticFeedback('light');
            }, { passive: true });
            
            card.addEventListener('touchend', () => {
                setTimeout(() => {
                    card.style.transform = '';
                }, 200);
            }, { passive: true });
        });
        
        // Mobile-specific action button enhancements
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Add pulse effect for successful actions
                button.classList.add('pulse');
                setTimeout(() => {
                    button.classList.remove('pulse');
                }, 1500);
            });
        });
    }
    
    function optimizeMobilePerformance() {
        if (!gameState.isMobile) return;
        
        // Reduce animation complexity on mobile
        document.documentElement.style.setProperty('--transition', 'all 0.2s ease');
        document.documentElement.style.setProperty('--transition-fast', 'all 0.1s ease');
        
        // Use requestAnimationFrame for smooth mobile updates
        let animationFrame;
        function optimizedMobileUpdate() {
            if (animationFrame) return;
            
            animationFrame = requestAnimationFrame(() => {
                updateMobileStatusBar();
                if (gameState.viewMode === 'pro') {
                    updateProAnalytics();
                }
                animationFrame = null;
            });
        }
        
        // Replace direct calls with optimized version for mobile
        gameState.optimizedMobileUpdate = optimizedMobileUpdate;
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!gameState.hasHapticSupport || !gameState.isMobile) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50],
            double: [10, 30, 10]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function updateProAnalytics() {
        if (gameState.viewMode !== 'pro') return;
        
        // Update tournament position
        if (elements.tournamentPosition) {
            elements.tournamentPosition.textContent = `${gameState.tournamentPosition}/${gameState.currentTournament?.players || 100}`;
        }
        
        // Update level time remaining
        if (elements.levelTimeLeft && gameState.levelTimeRemaining) {
            const minutes = Math.floor(gameState.levelTimeRemaining / 60);
            const seconds = gameState.levelTimeRemaining % 60;
            elements.levelTimeLeft.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // Update M-Zone (chip stack relative to blinds)
        if (elements.mZoneValue) {
            const mZone = calculateMZone();
            elements.mZoneValue.textContent = mZone.toFixed(1);
            
            // Color-code M-Zone
            if (mZone < 5) {
                elements.mZoneValue.style.color = 'var(--danger-color)';
            } else if (mZone < 10) {
                elements.mZoneValue.style.color = 'var(--warning-color)';
            } else {
                elements.mZoneValue.style.color = 'var(--success-color)';
            }
        }
        
        // Update ICM value
        if (elements.icmValue) {
            elements.icmValue.textContent = `${gameState.statistics.icm} GA`;
        }
    }
    
    function calculateMZone() {
        // M-Zone is your chip stack divided by current blinds + antes
        const humanPlayer = gameState.tablePlayers[0];
        const totalBlindsPerOrbit = (gameState.smallBlind + gameState.bigBlind) * gameState.tablePlayers.length;
        return humanPlayer.chips / totalBlindsPerOrbit;
    }
    
    // Tournament selection
    function selectTournament(tournamentType) {
        console.log(`Selecting tournament: ${tournamentType}`);
        
        const tournament = tournaments[tournamentType];
        if (!tournament) return;
        
        // Show tournament details or registration confirmation
        if (confirm(`Would you like to view details for the ${tournament.name}?`)) {
            alert(`
                ${tournament.name}
                Players: ${tournament.currentPlayers}/${tournament.players}
                Buy-in: ${tournament.buyIn} GA
                Prize Pool: ${tournament.prizePool} GA
                Starting Chips: ${tournament.startingChips} GA
                Starts: ${tournament.timeToStart}
            `);
        }
    }
    
    function registerForTournament(tournamentType) {
        console.log(`Registering for tournament: ${tournamentType}`);
        
        const tournament = tournaments[tournamentType];
        if (!tournament) return;
        
        // Check if player has enough balance
        if (gameState.balance < tournament.buyIn) {
            alert(`You don't have enough balance to register for this tournament. Required: ${tournament.buyIn} GA`);
            return;
        }
        
        // Confirm registration
        if (confirm(`Register for ${tournament.name} for ${tournament.buyIn} GA?`)) {
            // Deduct buy-in from balance
            gameState.balance -= tournament.buyIn;
            gameState.currentTournament = tournament;
            gameState.tournamentPhase = "lobby";
            
            // Set tournament start time (for demo, we'll start immediately)
            gameState.tournamentStartTime = Date.now();
            
            // Setup tournament state
            gameState.currentLevel = 1;
            gameState.levelStartTime = Date.now();
            gameState.levelTimeRemaining = tournament.blindLevels[0].duration;
            gameState.smallBlind = tournament.blindLevels[0].small;
            gameState.bigBlind = tournament.blindLevels[0].big;
            
            // Set starting chips for all players
            gameState.tablePlayers.forEach(player => {
                player.chips = player.id === 0 ? tournament.startingChips : 
                    Math.floor(tournament.startingChips * (0.8 + Math.random() * 0.4));
            });
            
            // Start blind level timer
            startBlindLevelTimer();
            
            // Show tournament lobby
            updateTournamentLobby();
            showScreen('lobby');
        }
    }
    
    function updateTournamentLobby() {
        if (!gameState.currentTournament) return;
        
        const tournament = gameState.currentTournament;
        
        // Update tournament info
        if (elements.lobbyTournamentName) {
            elements.lobbyTournamentName.textContent = tournament.name;
        }
        
        if (elements.tournamentTimeLeft) {
            const currentBlindLevel = tournament.blindLevels[gameState.currentLevel - 1];
            const minutes = Math.floor(gameState.levelTimeRemaining / 60);
            const seconds = gameState.levelTimeRemaining % 60;
            elements.tournamentTimeLeft.textContent = `Level ${gameState.currentLevel}: ${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        if (elements.lobbyPlayerCount) {
            elements.lobbyPlayerCount.textContent = `${tournament.currentPlayers}/${tournament.players}`;
        }
        
        if (elements.lobbyBlindLevel) {
            elements.lobbyBlindLevel.textContent = `${gameState.smallBlind}/${gameState.bigBlind}`;
        }
    }
    
    function startBlindLevelTimer() {
        if (!gameState.currentTournament) return;
        
        const blindLevelTimer = setInterval(() => {
            if (gameState.levelTimeRemaining <= 0) {
                // Time for next blind level
                gameState.currentLevel++;
                
                // Check if there are more blind levels
                if (gameState.currentLevel <= gameState.currentTournament.blindLevels.length) {
                    const newLevel = gameState.currentTournament.blindLevels[gameState.currentLevel - 1];
                    gameState.smallBlind = newLevel.small;
                    gameState.bigBlind = newLevel.big;
                    gameState.levelTimeRemaining = newLevel.duration;
                    gameState.levelStartTime = Date.now();
                    
                    // Announce blind increase
                    if (gameState.tournamentPhase === 'playing') {
                        alert(`Blinds increased to ${gameState.smallBlind}/${gameState.bigBlind}`);
                    }
                } else {
                    // No more blind levels, stop timer
                    clearInterval(blindLevelTimer);
                }
            } else {
                gameState.levelTimeRemaining--;
            }
            
            // Update UI if in lobby or playing
            if (gameState.tournamentPhase === 'lobby') {
                updateTournamentLobby();
            } else if (gameState.tournamentPhase === 'playing') {
                updateTournamentInfo();
            }
        }, 1000);
    }
    
    function updateTournamentInfo() {
        // Update tournament sidebar
        if (elements.tournamentLevel) {
            elements.tournamentLevel.textContent = gameState.currentLevel;
        }
        
        if (elements.tournamentBlinds) {
            elements.tournamentBlinds.textContent = `${gameState.smallBlind}/${gameState.bigBlind}`;
        }
        
        if (elements.nextLevelTime) {
            const minutes = Math.floor(gameState.levelTimeRemaining / 60);
            const seconds = gameState.levelTimeRemaining % 60;
            elements.nextLevelTime.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        if (elements.playersLeft) {
            elements.playersLeft.textContent = `${gameState.currentTournament.currentPlayers}/${gameState.currentTournament.players}`;
        }
        
        if (elements.avgStack) {
            // Calculate average stack
            const totalChips = gameState.currentTournament.startingChips * gameState.currentTournament.players;
            const avgChips = Math.floor(totalChips / gameState.currentTournament.currentPlayers);
            elements.avgStack.textContent = avgChips.toLocaleString();
        }
        
        if (elements.yourStack) {
            elements.yourStack.textContent = gameState.tablePlayers[0].chips.toLocaleString();
        }
        
        // Update pro analytics if active
        if (gameState.viewMode === 'pro') {
            updateProAnalytics();
        }
    }
    
    function joinTable() {
        console.log("Joining tournament table");
        
        gameState.tournamentPhase = "playing";
        showScreen('game');
        
        // Initialize poker game
        initializePokerGame();
    }
    
    function spectateTable(tableId) {
        alert(`Spectating Table #${tableId} - This feature is not fully implemented in the demo.`);
    }
    
    function leaveTournament() {
        if (confirm("Are you sure you want to leave the tournament? You will forfeit your position and any potential winnings.")) {
            gameState.tournamentPhase = "selection";
            gameState.currentTournament = null;
            showScreen('selection');
        }
    }
    
    // Poker game logic
    function initializePokerGame() {
        console.log("Initializing poker game");
        
        // Create new deck and shuffle
        gameState.deck = createDeck();
        shuffleDeck();
        
        // Reset game state
        gameState.pot = 0;
        gameState.currentPhase = 'pre-flop';
        gameState.communityCards = [];
        gameState.dealerPosition = 0;
        gameState.currentBetAmount = 0;
        gameState.showdownBonus.active = false;
        gameState.showdownBonus.eligiblePlayers = [];
        
        // Reset player states
        gameState.tablePlayers.forEach(player => {
            player.cards = [];
            player.bet = 0;
            player.folded = false;
            player.showdown = false;
            player.status = 'active';
        });
        
        // Check for all-in protection
        checkAllInProtection();
        
        // Deal cards and set blinds
        dealHoleCards();
        setBlindPositions();
        
        // Update UI
        updateDisplay();
        updatePlayerCards();
        
        // Start first betting round
        startBettingRound();
        
        // Track statistics
        gameState.statistics.handsPlayed++;
    }
    
    function createDeck() {
        const deck = [];
        suits.forEach(suit => {
            ranks.forEach(rank => {
                deck.push({
                    rank: rank,
                    suit: suit,
                    value: rankValues[rank],
                    isRed: suit === '♥' || suit === '♦'
                });
            });
        });
        return deck;
    }
    
    function shuffleDeck() {
        for (let i = gameState.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
        }
    }
    
    function dealHoleCards() {
        // Deal 2 cards to each active player
        for (let i = 0; i < 2; i++) {
            gameState.tablePlayers.forEach(player => {
                if (!player.folded) {
                    player.cards.push(gameState.deck.pop());
                }
            });
        }
    }
    
    function setBlindPositions() {
        // Find small blind and big blind positions relative to dealer
        const activePlayers = gameState.tablePlayers.filter(p => !p.folded);
        if (activePlayers.length < 2) return;
        
        const dealerIndex = gameState.dealerPosition;
        const smallBlindIndex = (dealerIndex + 1) % gameState.tablePlayers.length;
        const bigBlindIndex = (dealerIndex + 2) % gameState.tablePlayers.length;
        
        // Post blinds
        const smallBlindPlayer = gameState.tablePlayers[smallBlindIndex];
        const bigBlindPlayer = gameState.tablePlayers[bigBlindIndex];
        
        smallBlindPlayer.bet = gameState.smallBlind;
        smallBlindPlayer.chips -= gameState.smallBlind;
        smallBlindPlayer.status = 'small blind';
        
        bigBlindPlayer.bet = gameState.bigBlind;
        bigBlindPlayer.chips -= gameState.bigBlind;
        bigBlindPlayer.status = 'big blind';
        
        gameState.pot = gameState.smallBlind + gameState.bigBlind;
        gameState.currentBetAmount = gameState.bigBlind;
        gameState.activePlayer = (bigBlindIndex + 1) % gameState.tablePlayers.length;
        gameState.minRaise = gameState.bigBlind;
    }
    
    function startBettingRound() {
        // Enable appropriate actions for human player
        updatePlayerActions();
        
        // Find first active player who hasn't folded
        while (gameState.tablePlayers[gameState.activePlayer].folded) {
            gameState.activePlayer = (gameState.activePlayer + 1) % gameState.tablePlayers.length;
        }
        
        // Start AI decision making for current player if not human
        if (!gameState.tablePlayers[gameState.activePlayer].isHuman) {
            setTimeout(() => processAIAction(), 1000);
        }
    }
    
    function playerAction(action) {
        const player = gameState.tablePlayers[0]; // Human player is always index 0
        
        // Add haptic feedback and visual feedback for mobile
        if (gameState.isMobile) {
            const feedbackType = action === 'fold' ? 'error' : 
                                action === 'raise' ? 'heavy' : 
                                action === 'showdown' ? 'success' : 'medium';
            triggerHapticFeedback(feedbackType);
        }
        
        // Visual feedback for action
        const actionElement = document.querySelector(`#${action}Btn`);
        if (actionElement && gameState.isMobile) {
            actionElement.style.transform = 'scale(0.9)';
            actionElement.classList.add('pulse');
            setTimeout(() => {
                actionElement.style.transform = '';
                actionElement.classList.remove('pulse');
            }, 200);
        }
        
        switch (action) {
            case 'fold':
                player.folded = true;
                player.status = 'folded';
                addToHandHistory(`You folded`);
                break;
                
            case 'call':
                const callAmount = gameState.currentBetAmount - player.bet;
                if (player.chips >= callAmount) {
                    player.bet += callAmount;
                    player.chips -= callAmount;
                    gameState.pot += callAmount;
                    player.status = 'called';
                    addToHandHistory(`You called ${callAmount} GA`);
                }
                break;
                
            case 'raise':
                const raiseAmount = parseInt(elements.betAmount?.value || gameState.minRaise);
                const totalBet = gameState.currentBetAmount + raiseAmount;
                if (player.chips >= totalBet - player.bet) {
                    const addAmount = totalBet - player.bet;
                    player.chips -= addAmount;
                    player.bet = totalBet;
                    gameState.pot += addAmount;
                    gameState.currentBetAmount = totalBet;
                    gameState.minRaise = raiseAmount;
                    player.status = 'raised';
                    addToHandHistory(`You raised to ${totalBet} GA`);
                }
                break;
                
            case 'check':
                if (gameState.currentBetAmount === player.bet) {
                    player.status = 'checked';
                    addToHandHistory(`You checked`);
                }
                break;
                
            case 'showdown':
                // Can only use showdown on the river
                if (gameState.currentPhase === 'river') {
                    player.showdown = true;
                    player.status = 'showdown';
                    gameState.showdownBonus.eligiblePlayers.push(player.id);
                    addToHandHistory(`You revealed your cards for showdown bonus`);
                }
                break;
        }
        
        // Update mobile display immediately
        if (gameState.isMobile && gameState.optimizedMobileUpdate) {
            gameState.optimizedMobileUpdate();
        } else {
            updateDisplay();
        }
        
        nextPlayer();
    }
    
    function processAIAction() {
        const player = gameState.tablePlayers[gameState.activePlayer];
        if (!player || player.folded || player.isHuman) return;
        
        const personality = gameState.aiPersonalities[player.difficulty];
        const handStrength = evaluateHandStrength(player.cards, gameState.communityCards);
        const action = decideAIAction(player, handStrength, personality);
        
        executeAIAction(player, action);
        
        setTimeout(() => {
            nextPlayer();
        }, 1500);
    }
    
    function decideAIAction(player, handStrength, personality) {
        const random = Math.random() * 100;
        const callAmount = gameState.currentBetAmount - player.bet;
        
        // Check if river phase and consider showdown option
        if (gameState.currentPhase === 'river' && random < personality.showdownFreq && handStrength > 0.5) {
            return { action: 'showdown' };
        }
        
        // Strong hand or bluff - raise
        if (handStrength > 0.8 || (random < personality.bluffFreq && handStrength > 0.3)) {
            // Calculate raise amount based on hand strength and aggression
            const baseRaise = gameState.minRaise;
            const aggressionFactor = personality.aggression / 100;
            const raiseMultiplier = 1 + (handStrength * 2 * aggressionFactor);
            const raiseAmount = Math.floor(baseRaise * raiseMultiplier);
            
            // Limit raise to player's stack
            const maxRaise = Math.min(raiseAmount, player.chips);
            return { action: 'raise', amount: maxRaise };
        } 
        // Decent hand - call or check
        else if (handStrength > 0.5 || random < personality.tightness) {
            return callAmount > 0 ? { action: 'call' } : { action: 'check' };
        } 
        // Weak hand - fold
        else {
            return { action: 'fold' };
        }
    }
    
    function executeAIAction(player, action) {
        switch (action.action) {
            case 'fold':
                player.folded = true;
                player.status = 'folded';
                addToHandHistory(`${player.name} folded`);
                break;
                
            case 'call':
                const callAmount = gameState.currentBetAmount - player.bet;
                if (player.chips >= callAmount) {
                    player.bet += callAmount;
                    player.chips -= callAmount;
                    gameState.pot += callAmount;
                    player.status = 'called';
                    addToHandHistory(`${player.name} called ${callAmount} GA`);
                } else {
                    // Not enough chips, go all-in
                    gameState.pot += player.chips;
                    player.bet += player.chips;
                    player.chips = 0;
                    player.status = 'all-in';
                    addToHandHistory(`${player.name} is all-in with ${player.bet} GA`);
                }
                break;
                
            case 'raise':
                const raiseAmount = action.amount || gameState.minRaise;
                const totalBet = gameState.currentBetAmount + raiseAmount;
                if (player.chips >= totalBet - player.bet) {
                    const addAmount = totalBet - player.bet;
                    player.chips -= addAmount;
                    player.bet = totalBet;
                    gameState.pot += addAmount;
                    gameState.currentBetAmount = totalBet;
                    gameState.minRaise = raiseAmount;
                    player.status = 'raised';
                    addToHandHistory(`${player.name} raised to ${totalBet} GA`);
                } else {
                    // Not enough chips, go all-in
                    gameState.pot += player.chips;
                    player.bet += player.chips;
                    player.chips = 0;
                    player.status = 'all-in';
                    addToHandHistory(`${player.name} is all-in with ${player.bet} GA`);
                }
                break;
                
            case 'check':
                player.status = 'checked';
                addToHandHistory(`${player.name} checked`);
                break;
                
            case 'showdown':
                player.showdown = true;
                player.status = 'showdown';
                gameState.showdownBonus.eligiblePlayers.push(player.id);
                addToHandHistory(`${player.name} revealed cards for showdown bonus`);
                break;
        }
        
        updateDisplay();
    }
    
    function nextPlayer() {
        // Find next active player
        let nextIndex = (gameState.activePlayer + 1) % gameState.tablePlayers.length;
        let found = false;
        let loopCount = 0;
        
        while (!found && loopCount < gameState.tablePlayers.length) {
            if (!gameState.tablePlayers[nextIndex].folded) {
                gameState.activePlayer = nextIndex;
                found = true;
            } else {
                nextIndex = (nextIndex + 1) % gameState.tablePlayers.length;
                loopCount++;
            }
        }
        
        // Check if betting round is complete
        if (isBettingRoundComplete()) {
            advancePhase();
        } else {
            updatePlayerActions();
            if (!gameState.tablePlayers[gameState.activePlayer].isHuman) {
                setTimeout(() => processAIAction(), 1000);
            }
        }
    }
    
    function isBettingRoundComplete() {
        const activePlayers = gameState.tablePlayers.filter(p => !p.folded);
        return activePlayers.every(p => p.bet === gameState.currentBetAmount || p.chips === 0);
    }
    
    function advancePhase() {
        switch (gameState.currentPhase) {
            case 'pre-flop':
                dealFlop();
                gameState.currentPhase = 'flop';
                break;
            case 'flop':
                dealTurn();
                gameState.currentPhase = 'turn';
                break;
            case 'turn':
                dealRiver();
                gameState.currentPhase = 'river';
                break;
            case 'river':
                showdown();
                return;
        }
        
        // Reset betting for new phase
        gameState.currentBetAmount = 0;
        gameState.tablePlayers.forEach(p => {
            if (p.status !== 'folded' && p.status !== 'all-in') {
                p.bet = 0;
                p.status = 'active';
            }
        });
        gameState.activePlayer = (gameState.dealerPosition + 1) % gameState.tablePlayers.length;
        
        // Skip players who have folded or are all-in
        while (gameState.tablePlayers[gameState.activePlayer].folded || 
               gameState.tablePlayers[gameState.activePlayer].status === 'all-in') {
            gameState.activePlayer = (gameState.activePlayer + 1) % gameState.tablePlayers.length;
        }
        
        updateDisplay();
        startBettingRound();
    }
    
    function dealFlop() {
        // Burn one card, then deal 3 community cards
        gameState.deck.pop(); // Burn card
        gameState.communityCards.push(gameState.deck.pop());
        gameState.communityCards.push(gameState.deck.pop());
        gameState.communityCards.push(gameState.deck.pop());
        updateCommunityCards();
    }
    
    function dealTurn() {
        // Burn one card, then deal 1 community card
        gameState.deck.pop(); // Burn card
        gameState.communityCards.push(gameState.deck.pop());
        updateCommunityCards();
    }
    
    function dealRiver() {
        // Burn one card, then deal 1 community card
        gameState.deck.pop(); // Burn card
        gameState.communityCards.push(gameState.deck.pop());
        updateCommunityCards();
        
        // Enable showdown button on river
        if (elements.showdownBtn && !gameState.tablePlayers[0].folded) {
            elements.showdownBtn.disabled = false;
        }
    }
    
    function showdown() {
        const activePlayers = gameState.tablePlayers.filter(p => !p.folded);
        const hands = activePlayers.map(player => ({
            player: player,
            hand: evaluateBestHand(player.cards, gameState.communityCards),
            strength: evaluateHandStrength(player.cards, gameState.communityCards)
        }));
        
        // Sort by hand strength (highest first)
        hands.sort((a, b) => b.strength - a.strength);
        
        // Store pot size for showdown bonus calculation
        gameState.showdownBonus.potSize = gameState.pot;
        
        // Award pot to winner(s)
        const winners = hands.filter(h => h.strength === hands[0].strength);
        const winAmount = Math.floor(gameState.pot / winners.length);
        
        winners.forEach(winner => {
            // Award base pot
            winner.player.chips += winAmount;
            
            // Apply showdown bonus if eligible
            if (gameState.showdownBonus.eligiblePlayers.includes(winner.player.id)) {
                // Calculate bonus (2x the pot excluding the player's contribution)
                const playerContribution = winner.player.bet;
                const otherContributions = gameState.showdownBonus.potSize - playerContribution;
                const bonusAmount = Math.floor(otherContributions * gameState.showdownBonus.bonusMultiplier);
                
                winner.player.chips += bonusAmount;
                
                // Show showdown bonus animation for winner
                if (winner.player.isHuman) {
                    showShowdownBonus(winner.player, winner.hand, bonusAmount);
                    gameState.statistics.showdownsWon++;
                    
                    // Check for showdown achievement
                    checkShowdownAchievement();
                } else {
                    addToHandHistory(`${winner.player.name} won showdown bonus of ${bonusAmount} GA!`);
                }
            }
            
            // Track statistics for human player
            if (winner.player.isHuman) {
                gameState.statistics.handsWon++;
                gameState.statistics.totalWon += winAmount;
            }
        });
        
        // Update statistics
        if (gameState.pot > gameState.statistics.biggestPot) {
            gameState.statistics.biggestPot = gameState.pot;
        }
        
        // Show results
        const winnerNames = winners.map(w => w.player.name).join(', ');
        addToHandHistory(`${winnerNames} won ${gameState.pot} GA with ${hands[0].hand.name}`);
        
        // Check for tournament elimination
        const humanPlayer = gameState.tablePlayers[0];
        if (humanPlayer.chips <= 0) {
            // Player is eliminated from tournament
            setTimeout(() => showTournamentElimination(), 2000);
            return;
        }
        
        // Reset for next hand
        gameState.pot = 0;
        gameState.dealerPosition = (gameState.dealerPosition + 1) % gameState.tablePlayers.length;
        gameState.showdownBonus.active = false;
        gameState.showdownBonus.eligiblePlayers = [];
        
        setTimeout(() => {
            if (!gameState.sittingOut) {
                initializePokerGame();
            }
        }, winners.some(w => w.player.isHuman) ? 3000 : 2000);
    }
    
    function showShowdownBonus(player, hand, bonusAmount) {
        console.log(`Showing showdown bonus: ${bonusAmount} GA`);
        
        // Populate showdown overlay with hand information
        if (elements.showdownCard1 && elements.showdownCard2 && player.cards.length >= 2) {
            elements.showdownCard1.innerHTML = createCardHTML(player.cards[0]);
            elements.showdownCard2.innerHTML = createCardHTML(player.cards[1]);
        }
        
        if (elements.showdownHandName) {
            elements.showdownHandName.textContent = hand.name;
        }
        
        // Populate community cards
        const communityElements = [
            elements.showdownCommunity1,
            elements.showdownCommunity2,
            elements.showdownCommunity3,
            elements.showdownCommunity4,
            elements.showdownCommunity5
        ];
        
        gameState.communityCards.forEach((card, index) => {
            if (communityElements[index]) {
                communityElements[index].innerHTML = createCardHTML(card);
            }
        });
        
        // Update result text
        if (elements.showdownResultText) {
            elements.showdownResultText.textContent = `You win with ${hand.name}!`;
        }
        
        // Update bonus amount
        if (elements.bonusAmount) {
            elements.bonusAmount.textContent = `+${bonusAmount.toLocaleString()} GA`;
        }
        
        // Show the overlay
        toggleModal(elements.showdownOverlay, true);
    }
    
    function createCardHTML(card) {
        return `
            <div class="card ${card.isRed ? 'card-red' : 'card-black'}">
                <div class="card-value">${card.rank}</div>
                <div class="card-suit">${card.suit}</div>
            </div>
        `;
    }
    
    function showTournamentElimination() {
        console.log("Player eliminated from tournament");
        
        // Update elimination position
        if (elements.eliminationPosition) {
            elements.eliminationPosition.textContent = `${gameState.tournamentPosition}${getOrdinalSuffix(gameState.tournamentPosition)}`;
        }
        
        // Calculate prize based on position
        let prize = 0;
        if (gameState.currentTournament) {
            const payouts = gameState.currentTournament.payouts;
            for (const payout of payouts) {
                if (typeof payout.position === 'string') {
                    const positions = payout.position.replace(/[a-z]/g, '').split('-').map(Number);
                    if (positions.length === 1 && gameState.tournamentPosition === positions[0]) {
                        prize = payout.amount;
                        break;
                    } else if (positions.length === 2 && 
                              gameState.tournamentPosition >= positions[0] && 
                              gameState.tournamentPosition <= positions[1]) {
                        prize = payout.amount;
                        break;
                    }
                }
            }
        }
        
        // Update prize amount
        if (elements.eliminationPrize) {
            elements.eliminationPrize.textContent = `${prize.toLocaleString()} GA`;
        }
        
        // Update tournament duration
        if (elements.tournamentDuration && gameState.tournamentStartTime) {
            const durationMs = Date.now() - gameState.tournamentStartTime;
            const minutes = Math.floor(durationMs / 60000);
            elements.tournamentDuration.textContent = `${minutes} min`;
        }
        
        // Show last hand cards
        const humanPlayer = gameState.tablePlayers[0];
        if (elements.lastHandCard1 && elements.lastHandCard2 && humanPlayer.cards.length >= 2) {
            elements.lastHandCard1.innerHTML = createCardHTML(humanPlayer.cards[0]);
            elements.lastHandCard2.innerHTML = createCardHTML(humanPlayer.cards[1]);
        }
        
        // Find opponent who won the hand
        const opponents = gameState.tablePlayers.filter(p => !p.folded && !p.isHuman);
        if (opponents.length > 0 && opponents[0].cards.length >= 2) {
            if (elements.opponentLastCard1 && elements.opponentLastCard2) {
                elements.opponentLastCard1.innerHTML = createCardHTML(opponents[0].cards[0]);
                elements.opponentLastCard2.innerHTML = createCardHTML(opponents[0].cards[1]);
            }
        }
        
        // Show community cards
        const boardElements = [
            elements.lastHandBoard1,
            elements.lastHandBoard2,
            elements.lastHandBoard3,
            elements.lastHandBoard4,
            elements.lastHandBoard5
        ];
        
        gameState.communityCards.forEach((card, index) => {
            if (boardElements[index]) {
                boardElements[index].innerHTML = createCardHTML(card);
            }
        });
        
        // Check for achievements
        const earnedAchievements = [];
        
        // In the money achievement
        if (prize > 0) {
            const inTheMoneyAchievement = achievements.find(a => a.id === 'in-the-money');
            if (inTheMoneyAchievement && !inTheMoneyAchievement.earned) {
                inTheMoneyAchievement.earned = true;
                earnedAchievements.push(inTheMoneyAchievement);
            }
        }
        
        // Update achievements list
        if (elements.achievementsList) {
            elements.achievementsList.innerHTML = '';
            
            earnedAchievements.forEach(achievement => {
                const achievementItem = document.createElement('div');
                achievementItem.className = 'achievement-item';
                achievementItem.innerHTML = `
                    <div class="achievement-icon">
                        <i class="${achievement.icon}"></i>
                    </div>
                    <div class="achievement-info">
                        <div class="achievement-name">${achievement.name}</div>
                        <div class="achievement-description">${achievement.description}</div>
                    </div>
                `;
                elements.achievementsList.appendChild(achievementItem);
            });
            
            if (earnedAchievements.length === 0) {
                const noAchievements = document.createElement('div');
                noAchievements.className = 'no-achievements';
                noAchievements.textContent = 'No achievements earned this tournament';
                elements.achievementsList.appendChild(noAchievements);
            }
        }
        
        // Add prize to balance
        if (prize > 0) {
            gameState.balance += prize;
        }
        
        // Update tournament phase
        gameState.tournamentPhase = "eliminated";
        
        // Show elimination overlay
        toggleModal(elements.eliminationOverlay, true);
    }
    
    function getOrdinalSuffix(num) {
        const j = num % 10,
              k = num % 100;
        if (j == 1 && k != 11) {
            return "st";
        }
        if (j == 2 && k != 12) {
            return "nd";
        }
        if (j == 3 && k != 13) {
            return "rd";
        }
        return "th";
    }
    
    function checkShowdownAchievement() {
        const showdownAchievement = achievements.find(a => a.id === 'showdown-specialist');
        if (showdownAchievement && !showdownAchievement.earned) {
            showdownAchievement.progress++;
            if (showdownAchievement.progress >= showdownAchievement.required) {
                showdownAchievement.earned = true;
                alert(`Achievement Unlocked: ${showdownAchievement.name}`);
            }
        }
    }
    
    function checkAllInProtection() {
        const humanPlayer = gameState.tablePlayers[0];
        const allInThreshold = gameState.bigBlind * gameState.allInProtection.threshold;
        
        if (humanPlayer.chips <= allInThreshold && !gameState.allInProtection.active) {
            // Activate all-in protection
            gameState.allInProtection.active = true;
            gameState.allInProtection.handsRemaining = 3;
            alert(`All-in protection activated: You have ${gameState.allInProtection.handsRemaining} hands of protection.`);
        } else if (gameState.allInProtection.active) {
            if (humanPlayer.chips > allInThreshold) {
                // Deactivate protection as player has recovered
                gameState.allInProtection.active = false;
                gameState.allInProtection.handsRemaining = 0;
            } else {
                // Decrease protection counter
                gameState.allInProtection.handsRemaining--;
                if (gameState.allInProtection.handsRemaining <= 0) {
                    gameState.allInProtection.active = false;
                }
            }
        }
    }
    
    function playAgain() {
        // Reset tournament
        gameState.tournamentPhase = "selection";
        gameState.currentTournament = null;
        
        // Hide elimination overlay
        toggleModal(elements.eliminationOverlay, false);
        
        // Show tournament selection
        showScreen('selection');
    }
    
    function returnToLobby() {
        // Hide elimination overlay
        toggleModal(elements.eliminationOverlay, false);
        
        // Show tournament selection
        showScreen('selection');
    }
    
    function evaluateBestHand(holeCards, communityCards) {
        // Combine hole cards with community cards
        const allCards = [...holeCards, ...communityCards];
        
        // Generate all possible 5-card combinations
        const combinations = getCombinations(allCards, 5);
        
        // Evaluate each combination and return the best one
        let bestHand = null;
        let bestRank = -1;
        
        combinations.forEach(combo => {
            const hand = evaluateHand(combo);
            if (hand.rank > bestRank) {
                bestRank = hand.rank;
                bestHand = hand;
            }
        });
        
        return bestHand;
    }
    
    function evaluateHand(cards) {
        // Sort cards by value (highest first)
        cards.sort((a, b) => b.value - a.value);
        
        const ranks = cards.map(c => c.value);
        const suits = cards.map(c => c.suit);
        
        // Check for flush
        const isFlush = suits.every(suit => suit === suits[0]);
        
        // Check for straight
        const isStraight = ranks.every((rank, i) => i === 0 || rank === ranks[i-1] - 1) ||
                          (ranks[0] === 14 && ranks[1] === 5 && ranks[2] === 4 && ranks[3] === 3 && ranks[4] === 2); // A-5 straight
        
        // Count rank frequencies
        const rankCounts = {};
        ranks.forEach(rank => {
            rankCounts[rank] = (rankCounts[rank] || 0) + 1;
        });
        
        const counts = Object.values(rankCounts).sort((a, b) => b - a);
        
        // Determine hand type
        if (isFlush && isStraight && ranks[0] === 14) {
            return { rank: 9, name: 'Royal Flush', cards };
        } else if (isFlush && isStraight) {
            return { rank: 8, name: 'Straight Flush', cards };
        } else if (counts[0] === 4) {
            return { rank: 7, name: 'Four of a Kind', cards };
        } else if (counts[0] === 3 && counts[1] === 2) {
            return { rank: 6, name: 'Full House', cards };
        } else if (isFlush) {
            return { rank: 5, name: 'Flush', cards };
        } else if (isStraight) {
            return { rank: 4, name: 'Straight', cards };
        } else if (counts[0] === 3) {
            return { rank: 3, name: 'Three of a Kind', cards };
        } else if (counts[0] === 2 && counts[1] === 2) {
            return { rank: 2, name: 'Two Pair', cards };
        } else if (counts[0] === 2) {
            return { rank: 1, name: 'One Pair', cards };
        } else {
            return { rank: 0, name: 'High Card', cards };
        }
    }
    
    function evaluateHandStrength(holeCards, communityCards) {
        if (holeCards.length < 2) return 0;
        
        const bestHand = evaluateBestHand(holeCards, communityCards);
        
        // Normalize hand strength to 0-1 scale
        return (bestHand.rank + 1) / 10;
    }
    
    function getCombinations(arr, size) {
        if (size > arr.length) return [];
        if (size === 1) return arr.map(el => [el]);
        
        const combinations = [];
        for (let i = 0; i < arr.length - size + 1; i++) {
            const head = arr[i];
            const tailCombinations = getCombinations(arr.slice(i + 1), size - 1);
            tailCombinations.forEach(combination => {
                combinations.push([head, ...combination]);
            });
        }
        return combinations;
    }
    
    function calculateWinProbability(holeCards, communityCards) {
        if (holeCards.length < 2) return 0;
        
        // Simple approximation based on hand strength
        const strength = evaluateHandStrength(holeCards, communityCards);
        const phase = gameState.currentPhase;
        
        // Adjust probability based on game phase
        let baseProbability = strength * 100;
        
        switch (phase) {
            case 'pre-flop':
                baseProbability *= 0.6; // More uncertainty pre-flop
                break;
            case 'flop':
                baseProbability *= 0.8;
                break;
            case 'turn':
                baseProbability *= 0.9;
                break;
            case 'river':
                baseProbability *= 1.0; // Most certainty at river
                break;
        }
        
        return Math.min(Math.max(baseProbability, 5), 95); // Keep between 5-95%
    }
    
    function updateDisplay() {
        // Update based on current tournament phase
        if (gameState.tournamentPhase === 'selection') {
            updateSelectionDisplay();
        } else if (gameState.tournamentPhase === 'lobby') {
            updateTournamentLobby();
        } else if (gameState.tournamentPhase === 'playing') {
            updateGameDisplay();
        }
    }
    
    function updateSelectionDisplay() {
        // Update player balance
        if (elements.playerBalance) {
            elements.playerBalance.textContent = gameState.balance.toLocaleString();
        }
    }
    
    function updateGameDisplay() {
        // Update pot
        if (elements.potAmount) {
            elements.potAmount.textContent = `${gameState.pot} GA`;
        }
        
        // Update mobile status bar
        if (gameState.isMobile) {
            updateMobileStatusBar();
        }
        
        // Update player information
        updatePlayerInfo();
        
        // Update tournament info
        updateTournamentInfo();
        
        // Update game phase
        if (elements.roundPhase) {
            elements.roundPhase.textContent = gameState.currentPhase.charAt(0).toUpperCase() + gameState.currentPhase.slice(1);
        }
        
        // Update active players count
        const activePlayers = gameState.tablePlayers.filter(p => !p.folded).length;
        if (elements.playersActive) {
            elements.playersActive.textContent = `${activePlayers} players active`;
        }
        
        // Update hand strength and win probability for human player
        const humanPlayer = gameState.tablePlayers[0];
        if (humanPlayer.cards.length > 0) {
            const strength = evaluateHandStrength(humanPlayer.cards, gameState.communityCards);
            const winProb = calculateWinProbability(humanPlayer.cards, gameState.communityCards);
            
            updateHandStrengthDisplay(strength);
            updateWinProbabilityDisplay(winProb);
        }
        
        // Update pro analytics if enabled
        if (gameState.viewMode === 'pro') {
            updateProAnalytics();
        }
    }
    
    function updateMobileStatusBar() {
        // Update balance with animation on mobile
        if (elements.mobileBalance) {
            const currentBalance = parseInt(elements.mobileBalance.textContent.replace(/,/g, '')) || 0;
            const newBalance = gameState.tablePlayers[0].chips;
            if (currentBalance !== newBalance) {
                animateNumberChange(elements.mobileBalance, currentBalance, newBalance);
            }
        }
        
        if (elements.mobilePlayersCount) {
            const currentPlayers = gameState.currentTournament?.currentPlayers || 0;
            elements.mobilePlayersCount.textContent = `${gameState.tournamentPosition}/${currentPlayers}`;
        }
        
        // Update pot with animation
        if (elements.mobilePotSize) {
            const currentPot = parseInt(elements.mobilePotSize.textContent.replace(/,/g, '')) || 0;
            if (currentPot !== gameState.pot) {
                animateNumberChange(elements.mobilePotSize, currentPot, gameState.pot);
            }
        }
        
        const humanPlayer = gameState.tablePlayers[0];
        if (elements.mobileWinChance && humanPlayer.cards.length > 0) {
            const winProb = calculateWinProbability(humanPlayer.cards, gameState.communityCards);
            const currentProb = parseInt(elements.mobileWinChance.textContent) || 0;
            const newProb = Math.round(winProb);
            
            if (currentProb !== newProb) {
                animateNumberChange(elements.mobileWinChance, currentProb, newProb, '%');
                
                // Add color coding and haptic feedback for mobile
                if (gameState.isMobile) {
                    if (newProb >= 70) {
                        elements.mobileWinChance.style.color = 'var(--success-color)';
                        if (newProb > currentProb + 10) triggerHapticFeedback('success');
                    } else if (newProb >= 40) {
                        elements.mobileWinChance.style.color = 'var(--warning-color)';
                    } else {
                        elements.mobileWinChance.style.color = 'var(--danger-color)';
                        if (newProb < currentProb - 10) triggerHapticFeedback('error');
                    }
                }
            }
        }
    }
    
    function animateNumberChange(element, from, to, suffix = '') {
        if (!element || from === to) return;
        
        const duration = gameState.isMobile ? 400 : 500;
        const steps = gameState.isMobile ? 15 : 20;
        const stepValue = (to - from) / steps;
        const stepDuration = duration / steps;
        
        let currentStep = 0;
        const timer = setInterval(() => {
            currentStep++;
            const currentValue = from + (stepValue * currentStep);
            
            if (currentStep >= steps) {
                clearInterval(timer);
                element.textContent = to.toLocaleString() + suffix;
            } else {
                element.textContent = Math.round(currentValue).toLocaleString() + suffix;
            }
        }, stepDuration);
        
        // Add visual feedback for significant changes on mobile
        if (gameState.isMobile && Math.abs(to - from) > 100) {
            element.style.transform = 'scale(1.1)';
            element.style.transition = 'transform 0.3s ease';
            setTimeout(() => {
                element.style.transform = '';
            }, 300);
        }
    }
    
    function updatePlayerInfo() {
        gameState.tablePlayers.forEach((player, index) => {
            const playerElement = document.getElementById(`player${index}`);
            if (playerElement) {
                const nameEl = playerElement.querySelector('.player-name');
                const chipsEl = playerElement.querySelector('.player-chips');
                const statusEl = playerElement.querySelector('.player-status');
                
                if (chipsEl) chipsEl.textContent = `${player.chips.toLocaleString()} GA`;
                if (statusEl) statusEl.textContent = player.status;
                
                // Update visual states
                playerElement.classList.toggle('folded', player.folded);
                playerElement.classList.toggle('active', gameState.activePlayer === index);
                playerElement.classList.toggle('all-in', player.chips === 0);
                
                // Update showdown state
                if (player.showdown) {
                    playerElement.classList.add('showdown');
                }
            }
        });
    }
    
    function updatePlayerCards() {
        const humanPlayer = gameState.tablePlayers[0];
        const playerCard1 = document.getElementById('playerCard1');
        const playerCard2 = document.getElementById('playerCard2');
        
        if (humanPlayer.cards.length >= 2 && playerCard1 && playerCard2) {
            playerCard1.innerHTML = `
                <div class="card ${humanPlayer.cards[0].isRed ? 'card-red' : 'card-black'}">
                    <div class="card-value">${humanPlayer.cards[0].rank}</div>
                    <div class="card-suit">${humanPlayer.cards[0].suit}</div>
                </div>
            `;
            
            playerCard2.innerHTML = `
                <div class="card ${humanPlayer.cards[1].isRed ? 'card-red' : 'card-black'}">
                    <div class="card-value">${humanPlayer.cards[1].rank}</div>
                    <div class="card-suit">${humanPlayer.cards[1].suit}</div>
                </div>
            `;
        }
    }
    
    function updateCommunityCards() {
        gameState.communityCards.forEach((card, index) => {
            if (elements.communityCards[index]) {
                elements.communityCards[index].innerHTML = `
                    <div class="card ${card.isRed ? 'card-red' : 'card-black'}">
                        <div class="card-value">${card.rank}</div>
                        <div class="card-suit">${card.suit}</div>
                    </div>
                `;
            }
        });
    }
    
    function updateHandStrengthDisplay(strength) {
        if (!elements.handStrength) return;
        
        const strengthBars = elements.handStrength.querySelectorAll('.strength-bar');
        const strengthLabel = elements.handStrength.querySelector('.strength-label');
        
        // Update strength bars
        strengthBars.forEach((bar, index) => {
            bar.classList.toggle('active', index < Math.ceil(strength * 5));
        });
        
        // Update hand name
        const humanPlayer = gameState.tablePlayers[0];
        if (humanPlayer.cards.length > 0) {
            const bestHand = evaluateBestHand(humanPlayer.cards, gameState.communityCards);
            if (strengthLabel) {
                strengthLabel.textContent = `Hand: ${bestHand.name}`;
            }
        }
    }
    
    function updateWinProbabilityDisplay(probability) {
        if (elements.winProbability) {
            const probValue = elements.winProbability.querySelector('.prob-value');
            if (probValue) {
                probValue.textContent = `${Math.round(probability)}%`;
                
                // Color code based on probability
                if (probability >= 70) {
                    probValue.style.color = 'var(--success-color)';
                } else if (probability >= 40) {
                    probValue.style.color = 'var(--warning-color)';
                } else {
                    probValue.style.color = 'var(--danger-color)';
                }
            }
        }
    }
    
    function updatePlayerActions() {
        const humanPlayer = gameState.tablePlayers[0];
        const isPlayerTurn = gameState.activePlayer === 0;
        const callAmount = gameState.currentBetAmount - humanPlayer.bet;
        
        // Enable/disable action buttons
        if (elements.foldBtn) elements.foldBtn.disabled = !isPlayerTurn || humanPlayer.folded;
        if (elements.callBtn) {
            elements.callBtn.disabled = !isPlayerTurn || humanPlayer.folded || callAmount <= 0;
            if (elements.callAmount) elements.callAmount.textContent = callAmount;
        }
        if (elements.raiseBtn) elements.raiseBtn.disabled = !isPlayerTurn || humanPlayer.folded;
        if (elements.checkBtn) elements.checkBtn.disabled = !isPlayerTurn || humanPlayer.folded || callAmount > 0;
        
        // Showdown button only available on river
        if (elements.showdownBtn) {
            elements.showdownBtn.disabled = !isPlayerTurn || humanPlayer.folded || 
                                          gameState.currentPhase !== 'river' ||
                                          humanPlayer.showdown;
        }
        
        // Update next action display
        if (elements.nextAction) {
            if (isPlayerTurn && !humanPlayer.folded) {
                elements.nextAction.textContent = 'Your turn';
            } else if (!humanPlayer.folded) {
                elements.nextAction.textContent = `${gameState.tablePlayers[gameState.activePlayer].name}'s turn`;
            } else {
                elements.nextAction.textContent = 'You folded';
            }
        }
    }
    
    function updateBetAmount() {
        if (elements.betSlider && elements.betAmount) {
            elements.betAmount.value = elements.betSlider.value;
        }
    }
    
    function updateBetSlider() {
        if (elements.betSlider && elements.betAmount) {
            elements.betSlider.value = elements.betAmount.value;
        }
    }
    
    function addToHandHistory(message) {
        gameState.handHistory.unshift({
            message: message,
            timestamp: Date.now(),
            phase: gameState.currentPhase
        });
        
        // Keep only last 20 entries
        if (gameState.handHistory.length > 20) {
            gameState.handHistory = gameState.handHistory.slice(0, 20);
        }
    }
    
    function toggleFastFold() {
        gameState.fastFold = !gameState.fastFold;
        if (elements.fastFoldBtn) {
            elements.fastFoldBtn.textContent = gameState.fastFold ? 'Fast Fold: ON' : 'Fast Fold';
            elements.fastFoldBtn.style.background = gameState.fastFold ? 'var(--danger-color)' : null;
        }
    }
    
    function toggleAutoCheck() {
        gameState.autoCheck = !gameState.autoCheck;
        if (elements.autoCheckBtn) {
            elements.autoCheckBtn.textContent = gameState.autoCheck ? 'Auto-Check: ON' : 'Auto-Check';
            elements.autoCheckBtn.style.background = gameState.autoCheck ? 'var(--warning-color)' : null;
        }
    }
    
    function toggleSitOut() {
        gameState.sittingOut = !gameState.sittingOut;
        if (elements.sitOutBtn) {
            elements.sitOutBtn.textContent = gameState.sittingOut ? 'Sitting Out' : 'Sit Out';
            elements.sitOutBtn.style.background = gameState.sittingOut ? 'var(--danger-color)' : null;
        }
        
        if (gameState.sittingOut) {
            gameState.tablePlayers[0].status = 'sitting out';
        } else {
            if (gameState.tablePlayers[0].status === 'sitting out') {
                gameState.tablePlayers[0].status = 'active';
            }
        }
    }
    
    function leaveTable() {
        if (confirm('Are you sure you want to leave the table? This will forfeit your tournament position.')) {
            showTournamentElimination();
        }
    }
    
    function toggleChat() {
        alert('Chat functionality is not implemented in this demo.');
    }
    
    function addNote() {
        const playerName = prompt('Which player would you like to add a note for?');
        if (!playerName) return;
        
        const note = prompt('Enter your note:');
        if (!note) return;
        
        alert(`Note added for ${playerName}: "${note}"`);
    }
    
    function showStats() {
        alert('Player statistics functionality is not fully implemented in this demo.');
    }
    
    function showSettings() {
        alert('Settings functionality is not fully implemented in this demo.');
    }
    
    function toggleModal(modal, show) {
        if (!modal) return;
        modal.classList.toggle('hidden', !show);
    }
    
    function showScreen(screen) {
        // Hide all screens
        if (elements.tournamentSelection) elements.tournamentSelection.classList.add('hidden');
        if (elements.tournamentLobby) elements.tournamentLobby.classList.add('hidden');
        if (elements.gameArea) elements.gameArea.classList.add('hidden');
        
        // Show selected screen
        switch (screen) {
            case 'selection':
                if (elements.tournamentSelection) elements.tournamentSelection.classList.remove('hidden');
                break;
            case 'lobby':
                if (elements.tournamentLobby) elements.tournamentLobby.classList.remove('hidden');
                break;
            case 'game':
                if (elements.gameArea) elements.gameArea.classList.remove('hidden');
                break;
        }
    }
    
    // For demonstration purposes, show tournament selection first
    window.addEventListener('load', function() {
        if (elements.tournamentSelection) {
            elements.tournamentSelection.classList.remove('hidden');
            elements.tournamentLobby.classList.add('hidden');
            elements.gameArea.classList.add('hidden');
            updateDisplay();
        }
    });
    
    function saveGameState() {
        try {
            const saveData = {
                balance: gameState.balance,
                statistics: gameState.statistics,
                viewMode: gameState.viewMode,
                achievements: achievements
            };
            localStorage.setItem('txHoldemShowdownState', JSON.stringify(saveData));
        } catch (e) {
            console.warn('Could not save game state:', e);
        }
    }
    
    function loadGameState() {
        try {
            const saved = localStorage.getItem('txHoldemShowdownState');
            if (saved) {
                const saveData = JSON.parse(saved);
                if (saveData.balance !== undefined) gameState.balance = saveData.balance;
                if (saveData.statistics) gameState.statistics = { ...gameState.statistics, ...saveData.statistics };
                if (saveData.viewMode) gameState.viewMode = saveData.viewMode;
                if (saveData.achievements) {
                    saveData.achievements.forEach((savedAchievement, index) => {
                        if (index < achievements.length) {
                            achievements[index].earned = savedAchievement.earned;
                            if (savedAchievement.progress) {
                                achievements[index].progress = savedAchievement.progress;
                            }
                        }
                    });
                }
                
                // Apply loaded view mode
                setViewMode(gameState.viewMode);
            }
        } catch (e) {
            console.warn('Could not load game state:', e);
        }
    }
    
    // Utility function for throttling function calls
    function debounce(func, wait) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }
    
    // Auto-save game state periodically
    setInterval(saveGameState, 30000); // Save every 30 seconds
});