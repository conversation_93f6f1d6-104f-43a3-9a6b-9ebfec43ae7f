/**
 * DICE DYNASTY Game
 * A strategic dice game with imperial theme and provably fair mechanics
 * Enhanced with responsive design and Pro View mode
 */

// Game State
const gameState = {
    // Player stats
    roundsPlayed: 0,
    roundsWon: 0,
    bestHand: null,
    
    // Current game state
    dice: [1, 1, 1, 1, 1],
    heldDice: [false, false, false, false, false],
    rollCount: 0,
    maxRolls: 3, // Initial roll + 2 re-rolls
    
    // Betting and currency
    balance: 1000,  // Starting balance in GA
    currentBet: 10, // Default bet amount in GA
    
    // Fairness variables
    clientSeed: '',
    serverSeed: '',
    serverSeedHash: '',
    nonce: 0,
    
    // Game history
    history: [],
    
    // Pro View analytics
    analytics: {
        totalWagered: 0,
        totalWon: 0,
        bestWin: 0,
        handCounts: {
            "None": 0,
            "Pair of Scholars": 0,
            "Two Imperial Seals": 0,
            "Three Generals": 0,
            "Dynasty Straight": 0,
            "Full Imperial Court": 0,
            "Four Immortals": 0,
            "Five Emperors": 0
        }
    },
    
    // UI state
    viewMode: 'standard',  // 'standard' or 'pro'
    isMobileStatsOpen: false
};

// Dynasty Hand definitions
const DYNASTY_HANDS = {
    PAIR_OF_SCHOLARS: { name: "Pair of Scholars", multiplier: 1.5 },
    TWO_IMPERIAL_SEALS: { name: "Two Imperial Seals", multiplier: 2.5 },
    THREE_GENERALS: { name: "Three Generals", multiplier: 3 },
    DYNASTY_STRAIGHT: { name: "Dynasty Straight", multiplier: 4 },
    FULL_IMPERIAL_COURT: { name: "Full Imperial Court", multiplier: 6 },
    FOUR_IMMORTALS: { name: "Four Immortals", multiplier: 10 },
    FIVE_EMPERORS: { name: "Five Emperors", multiplier: 30 }
};

// Imperial symbols for dice faces
const IMPERIAL_SYMBOLS = [
    '1', // 1
    '2', // 2
    '3', // 3
    '4', // 4
    '5', // 5
    '6'  // 6
];

// Chart instance for Pro View
let handsChart = null;

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Set up event listeners
    document.getElementById('rollButton').addEventListener('click', rollDice);
    document.getElementById('mobileRollButton').addEventListener('click', rollDice);
    document.getElementById('newGameButton').addEventListener('click', startNewGame);
    document.getElementById('mobileNewGameButton').addEventListener('click', startNewGame);
    document.getElementById('toggleHelp').addEventListener('click', toggleHelp);
    document.getElementById('toggleFairness').addEventListener('click', toggleFairness);
    document.getElementById('verifyButton').addEventListener('click', verifyRoll);
    
    // Mobile-specific buttons
    document.getElementById('mobileBetButton').addEventListener('click', showBetSettingsModal);
    document.getElementById('mobileStatsToggle').addEventListener('click', toggleMobileStatsPanel);
    document.getElementById('closeStatsPanel').addEventListener('click', closeMobileStatsPanel);
    
    // View mode toggle
    document.getElementById('standardView').addEventListener('click', () => setViewMode('standard'));
    document.getElementById('proView').addEventListener('click', () => setViewMode('pro'));
    
    // Set up betting controls
    document.getElementById('decreaseBet').addEventListener('click', () => adjustBet(-5));
    document.getElementById('increaseBet').addEventListener('click', () => adjustBet(5));
    document.getElementById('betAmount').addEventListener('change', updateBetFromInput);
    
    // Set up quick bet buttons
    const quickBetButtons = document.querySelectorAll('.quick-bet-button');
    quickBetButtons.forEach(button => {
        button.addEventListener('click', () => {
            const value = button.dataset.value;
            if (value === 'max') {
                setMaxBet();
            } else {
                setBet(parseInt(value));
            }
        });
    });
    
    // Set up mobile modal
    document.getElementById('closeBetModal').addEventListener('click', closeBetSettingsModal);
    document.getElementById('modalDecreaseBet').addEventListener('click', () => adjustModalBet(-5));
    document.getElementById('modalIncreaseBet').addEventListener('click', () => adjustModalBet(5));
    document.getElementById('modalApplyBtn').addEventListener('click', applyModalSettings);
    
    // Set up modal preset buttons
    const modalPresetBtns = document.querySelectorAll('.modal-preset-btn');
    modalPresetBtns.forEach(button => {
        button.addEventListener('click', () => {
            const amount = button.dataset.amount;
            if (amount === 'max') {
                document.getElementById('modalBetAmount').value = gameState.balance;
            } else {
                document.getElementById('modalBetAmount').value = Math.min(gameState.balance, parseInt(amount));
            }
        });
    });
    
    // Initialize dice area
    initializeDiceArea();
    
    // Load saved balance from localStorage if available
    loadUserBalance();
    
    // Generate initial seeds
    generateNewSeeds();
    
    // Check if Pro View was active before
    const savedViewMode = localStorage.getItem('diceDynastyViewMode');
    if (savedViewMode === 'pro') {
        setViewMode('pro');
    }
    
    // Update displays
    updateStats();
    updateBalanceDisplay();
    updateProAnalytics();
    
    // Start a new game
    startNewGame();
    
    // Initialize Chart if in Pro View mode
    if (gameState.viewMode === 'pro') {
        initHandsChart();
    }
    
    // Handle window resize
    window.addEventListener('resize', handleResize);
});

/**
 * Handle window resize events
 */
function handleResize() {
    // Reinitialize the dice display on resize
    adjustDiceSize();
    
    // Update Pro View chart if active
    if (gameState.viewMode === 'pro' && handsChart) {
        handsChart.resize();
    }
}

/**
 * Adjust dice size based on screen width
 */
function adjustDiceSize() {
    const screenWidth = window.innerWidth;
    const dieElements = document.querySelectorAll('.die');
    const diceWrappers = document.querySelectorAll('.dice-wrapper');
    const holdButtons = document.querySelectorAll('.hold-button');
    
    if (screenWidth < 576) {
        // Small mobile
        dieElements.forEach(die => {
            die.style.width = '40px';
            die.style.height = '40px';
        });
        holdButtons.forEach(btn => {
            btn.style.bottom = '-20px';
            btn.style.fontSize = '0.6rem';
        });
    } else if (screenWidth < 768) {
        // Mobile
        dieElements.forEach(die => {
            die.style.width = '50px';
            die.style.height = '50px';
        });
        holdButtons.forEach(btn => {
            btn.style.bottom = '-22px';
            btn.style.fontSize = '0.7rem';
        });
    } else if (screenWidth < 992) {
        // Tablet
        dieElements.forEach(die => {
            die.style.width = '60px';
            die.style.height = '60px';
        });
        holdButtons.forEach(btn => {
            btn.style.bottom = '-25px';
            btn.style.fontSize = '0.75rem';
        });
    } else {
        // Desktop
        dieElements.forEach(die => {
            die.style.width = '70px';
            die.style.height = '70px';
        });
        holdButtons.forEach(btn => {
            btn.style.bottom = '-30px';
            btn.style.fontSize = '0.8rem';
        });
    }
    
    // Update die face positions
    dieElements.forEach((die, index) => {
        const size = parseInt(die.style.width) / 2;
        const faces = die.querySelectorAll('.die-face');
        faces.forEach((face, faceIndex) => {
            switch(faceIndex) {
                case 0: face.style.transform = `rotateY(0deg) translateZ(${size}px)`; break;
                case 1: face.style.transform = `rotateY(180deg) translateZ(${size}px)`; break;
                case 2: face.style.transform = `rotateY(90deg) translateZ(${size}px)`; break;
                case 3: face.style.transform = `rotateY(-90deg) translateZ(${size}px)`; break;
                case 4: face.style.transform = `rotateX(90deg) translateZ(${size}px)`; break;
                case 5: face.style.transform = `rotateX(-90deg) translateZ(${size}px)`; break;
            }
        });
    });
}

/**
 * Set the view mode (standard or pro)
 */
function setViewMode(mode) {
    gameState.viewMode = mode;
    localStorage.setItem('diceDynastyViewMode', mode);
    
    if (mode === 'standard') {
        document.body.classList.remove('pro-view-active');
        document.getElementById('standardView').classList.add('active');
        document.getElementById('proView').classList.remove('active');
    } else {
        document.body.classList.add('pro-view-active');
        document.getElementById('proView').classList.add('active');
        document.getElementById('standardView').classList.remove('active');
        
        // Initialize Pro View chart if not exists
        if (!handsChart) {
            initHandsChart();
        } else {
            updateHandsChart();
        }
    }
}

/**
 * Initialize the hands distribution chart for Pro View
 */
function initHandsChart() {
    const ctx = document.getElementById('handsChart').getContext('2d');
    
    if (handsChart) {
        handsChart.destroy();
    }
    
    const handNames = Object.keys(gameState.analytics.handCounts);
    const handCounts = Object.values(gameState.analytics.handCounts);
    
    handsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: handNames,
            datasets: [{
                label: 'Hand Distribution',
                data: handCounts,
                backgroundColor: [
                    '#e74c3c', // None
                    '#f39c12', // Pair of Scholars
                    '#d35400', // Two Imperial Seals
                    '#27ae60', // Three Generals
                    '#2980b9', // Dynasty Straight
                    '#8e44ad', // Full Imperial Court
                    '#d4af37', // Four Immortals
                    '#f1c40f'  // Five Emperors
                ],
                borderColor: '#1a0a0c',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: '#1a0a0c',
                    titleColor: '#d4af37',
                    bodyColor: '#ffffff',
                    borderColor: '#d4af37',
                    borderWidth: 1,
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            const value = context.parsed.y || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(212, 175, 55, 0.1)'
                    },
                    ticks: {
                        color: '#cccccc'
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#cccccc',
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            }
        }
    });
}

/**
 * Update the hands chart data
 */
function updateHandsChart() {
    if (!handsChart) return;
    
    const handCounts = Object.values(gameState.analytics.handCounts);
    handsChart.data.datasets[0].data = handCounts;
    handsChart.update();
}

/**
 * Initialize the dice area with dice and hold buttons
 */
function initializeDiceArea() {
    const diceArea = document.getElementById('diceArea');
    diceArea.innerHTML = '';
    
    for (let i = 0; i < 5; i++) {
        // Create dice wrapper
        const diceWrapper = document.createElement('div');
        diceWrapper.className = 'dice-wrapper';
        diceWrapper.id = `dice-wrapper-${i}`;
        
        // Create die
        const die = document.createElement('div');
        die.className = 'die';
        die.id = `die-${i}`;
        
        // Add die faces
        for (let j = 1; j <= 6; j++) {
            const face = document.createElement('div');
            face.className = 'die-face';
            face.dataset.value = j;
            
            const symbol = document.createElement('div');
            symbol.className = 'die-symbol';
            symbol.textContent = IMPERIAL_SYMBOLS[j-1];
            
            face.appendChild(symbol);
            die.appendChild(face);
        }
        
        // Add hold button
        const holdButton = document.createElement('button');
        holdButton.className = 'hold-button';
        holdButton.id = `hold-${i}`;
        holdButton.textContent = 'HOLD';
        holdButton.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleHold(i);
        });
        
        // Add die click handler
        die.addEventListener('click', () => toggleHold(i));
        
        // Add to wrapper
        diceWrapper.appendChild(die);
        diceWrapper.appendChild(holdButton);
        
        // Add to dice area
        diceArea.appendChild(diceWrapper);
    }
    
    // Adjust dice size based on screen
    adjustDiceSize();
}

/**
 * Start a new game round
 */
function startNewGame() {
    // Reset game state
    gameState.dice = [1, 1, 1, 1, 1];
    gameState.heldDice = [false, false, false, false, false];
    gameState.rollCount = 0;
    
    // Clear hold button states
    const holdButtons = document.querySelectorAll('.hold-button');
    holdButtons.forEach(button => {
        button.classList.remove('held');
    });
    
    // Clear dice wrapper states
    const diceWrappers = document.querySelectorAll('.dice-wrapper');
    diceWrappers.forEach(wrapper => {
        wrapper.classList.remove('held');
    });
    
    // Reset dice rotation
    const dice = document.querySelectorAll('.die');
    dice.forEach((die, index) => {
        die.style.transform = 'rotateX(0deg) rotateY(0deg)';
        die.classList.remove('rolling');
    });
    
    // Hide any result display
    const handResult = document.getElementById('handResult');
    handResult.style.display = 'none';
    
    // Update game phase and message
    document.getElementById('gamePhase').textContent = 'Initial Roll';
    document.getElementById('gameMessage').textContent = 'Roll the sacred imperial dice to begin your dynasty!';
    
    // Update roll button
    const betButtonAmount = document.getElementById('betButtonAmount');
    betButtonAmount.textContent = gameState.currentBet;
    
    // Enable roll button
    document.getElementById('rollButton').disabled = false;
    document.getElementById('mobileRollButton').disabled = false;
}

/**
 * Roll the dice
 */
function rollDice() {
    // Check if we have enough balance
    if (gameState.balance < gameState.currentBet) {
        showNotification('Not enough balance to place this bet!', 'error');
        return;
    }
    
    // If this is the first roll of a new game, deduct the bet
    if (gameState.rollCount === 0) {
        // Deduct bet from balance
        gameState.balance -= gameState.currentBet;
        updateBalanceDisplay();
        
        // Update analytics
        gameState.analytics.totalWagered += gameState.currentBet;
        
        // Increment played rounds
        gameState.roundsPlayed++;
    }
    
    // Update roll count
    gameState.rollCount++;
    
    // Update game phase
    const gamePhase = document.getElementById('gamePhase');
    const gameMessage = document.getElementById('gameMessage');
    
    if (gameState.rollCount === 1) {
        gamePhase.textContent = 'First Roll';
        gameMessage.textContent = 'Select dice to hold, then roll again or evaluate your hand';
    } else if (gameState.rollCount === 2) {
        gamePhase.textContent = 'Second Roll';
        gameMessage.textContent = 'Last chance to improve your hand!';
    } else {
        gamePhase.textContent = 'Final Roll';
        gameMessage.textContent = 'Your dynasty hand is complete';
    }
    
    // Disable buttons during animation
    document.getElementById('rollButton').disabled = true;
    document.getElementById('mobileRollButton').disabled = true;
    
    // Roll the dice (unheld ones only)
    const dice = document.querySelectorAll('.die');
    dice.forEach((die, index) => {
        if (!gameState.heldDice[index]) {
            die.classList.add('rolling');
            
            // Generate new value using provably fair algorithm
            const newValue = generateDieValue(index);
            gameState.dice[index] = newValue;
            
            // After animation, show the result
            setTimeout(() => {
                die.classList.remove('rolling');
                showDieValue(die, newValue);
            }, 1000); // Animation duration
        }
    });
    
    // After all dice have rolled, evaluate the hand
    setTimeout(() => {
        // Enable buttons after animation
        document.getElementById('rollButton').disabled = false;
        document.getElementById('mobileRollButton').disabled = false;
        
        // Check if we've reached max rolls
        if (gameState.rollCount >= gameState.maxRolls) {
            evaluateHand();
        }
    }, 1100); // Slightly longer than animation to ensure all dice have settled
}

/**
 * Show the die value by rotating the 3D die
 */
function showDieValue(dieElement, value) {
    // Rotate the die to show the correct face
    let transform = '';
    
    switch(value) {
        case 1: // Front face (default)
            transform = 'rotateX(0deg) rotateY(0deg)';
            break;
        case 2: // Back face
            transform = 'rotateX(0deg) rotateY(180deg)';
            break;
        case 3: // Right face
            transform = 'rotateX(0deg) rotateY(90deg)';
            break;
        case 4: // Left face
            transform = 'rotateX(0deg) rotateY(-90deg)';
            break;
        case 5: // Top face
            transform = 'rotateX(-90deg) rotateY(0deg)';
            break;
        case 6: // Bottom face
            transform = 'rotateX(90deg) rotateY(0deg)';
            break;
    }
    
    dieElement.style.transform = transform;
}

/**
 * Generate a die value using the provably fair algorithm
 */
function generateDieValue(dieIndex) {
    // Generate hash from seeds and nonce
    const dataToHash = gameState.clientSeed + gameState.serverSeed + gameState.nonce + dieIndex + gameState.rollCount;
    const hash = CryptoJS.SHA256(dataToHash).toString();
    
    // Use first 8 characters of hash to get a random number
    const rand = parseInt(hash.substr(0, 8), 16);
    
    // Map to 1-6 range for dice
    return (rand % 6) + 1;
}

/**
 * Toggle hold state for a die
 */
function toggleHold(dieIndex) {
    // Only allow holding after first roll and before final roll
    if (gameState.rollCount > 0 && gameState.rollCount < gameState.maxRolls) {
        gameState.heldDice[dieIndex] = !gameState.heldDice[dieIndex];
        
        // Update visual state
        const diceWrapper = document.getElementById(`dice-wrapper-${dieIndex}`);
        const holdButton = document.getElementById(`hold-${dieIndex}`);
        
        if (gameState.heldDice[dieIndex]) {
            diceWrapper.classList.add('held');
            holdButton.classList.add('held');
        } else {
            diceWrapper.classList.remove('held');
            holdButton.classList.remove('held');
        }
    }
}

/**
 * Evaluate the hand and determine winnings
 */
function evaluateHand() {
    // Get the hand combination and multiplier
    const handResult = getHandResult(gameState.dice);
    const handName = handResult.name;
    const multiplier = handResult.multiplier;
    
    // Display result
    const resultTitle = document.getElementById('resultTitle');
    const resultMultiplier = document.getElementById('resultMultiplier');
    const handResultElement = document.getElementById('handResult');
    
    resultTitle.textContent = handName;
    resultMultiplier.textContent = multiplier > 0 ? `Multiplier: ${multiplier}×` : 'No winning hand';
    handResultElement.style.display = 'block';
    
    // Determine if won
    let winAmount = 0;
    let isWin = false;
    
    if (multiplier > 0) {
        winAmount = Math.floor(gameState.currentBet * multiplier);
        isWin = true;
        
        // Add to balance
        gameState.balance += winAmount;
        
        // Update analytics
        gameState.analytics.totalWon += winAmount;
        if (winAmount > gameState.analytics.bestWin) {
            gameState.analytics.bestWin = winAmount;
        }
        
        // Update rounds won
        gameState.roundsWon++;
        
        // Update best hand if applicable
        if (!gameState.bestHand || getHandRank(handName) > getHandRank(gameState.bestHand)) {
            gameState.bestHand = handName;
        }
        
        // Show success message
        gameMessage.textContent = `Congratulations! You won ${winAmount} GA with a ${handName}!`;
        handResultElement.classList.add('winning');
        resultTitle.classList.add('winning');
        
        // Show win indicator
        showWinLossIndicator(true, winAmount - gameState.currentBet);
    } else {
        // Show loss message
        gameMessage.textContent = `No winning hand. You lost ${gameState.currentBet} GA.`;
        handResultElement.classList.remove('winning');
        resultTitle.classList.remove('winning');
        
        // Show loss indicator
        showWinLossIndicator(false, gameState.currentBet);
    }
    
    // Update hand counts for analytics
    if (handName === 'No Winning Hand') {
        gameState.analytics.handCounts['None']++;
    } else {
        gameState.analytics.handCounts[handName]++;
    }
    
    // Add to history
    addToHistory({
        round: gameState.roundsPlayed,
        dice: [...gameState.dice],
        hand: handName,
        bet: gameState.currentBet,
        win: winAmount,
        isWin: isWin,
        profit: isWin ? winAmount - gameState.currentBet : -gameState.currentBet,
        time: new Date()
    });
    
    // Update stats and display
    updateBalanceDisplay();
    updateStats();
    updateProAnalytics();
    
    // Update Pro View charts
    if (gameState.viewMode === 'pro') {
        updateHandsChart();
    }
    
    // Increment nonce for next game
    gameState.nonce++;
    document.getElementById('nonce').textContent = gameState.nonce;
    
    // Save state
    saveUserBalance();
}

/**
 * Get the hand result based on dice values
 */
function getHandResult(dice) {
    // Sort dice for easier pattern matching
    const sortedDice = [...dice].sort((a, b) => a - b);
    
    // Count occurrences of each value
    const valueCounts = {};
    for (const die of sortedDice) {
        valueCounts[die] = (valueCounts[die] || 0) + 1;
    }
    
    // Check for Five Emperors (five of a kind)
    if (Object.values(valueCounts).includes(5)) {
        return { name: DYNASTY_HANDS.FIVE_EMPERORS.name, multiplier: DYNASTY_HANDS.FIVE_EMPERORS.multiplier };
    }
    
    // Check for Four Immortals (four of a kind)
    if (Object.values(valueCounts).includes(4)) {
        return { name: DYNASTY_HANDS.FOUR_IMMORTALS.name, multiplier: DYNASTY_HANDS.FOUR_IMMORTALS.multiplier };
    }
    
    // Check for Full Imperial Court (full house: three of a kind + pair)
    const hasThreeOfAKind = Object.values(valueCounts).includes(3);
    const hasPair = Object.values(valueCounts).includes(2);
    if (hasThreeOfAKind && hasPair) {
        return { name: DYNASTY_HANDS.FULL_IMPERIAL_COURT.name, multiplier: DYNASTY_HANDS.FULL_IMPERIAL_COURT.multiplier };
    }
    
    // Check for Dynasty Straight (1-2-3-4-5 or 2-3-4-5-6)
    const isSmallStraight = JSON.stringify(sortedDice) === JSON.stringify([1, 2, 3, 4, 5]);
    const isLargeStraight = JSON.stringify(sortedDice) === JSON.stringify([2, 3, 4, 5, 6]);
    if (isSmallStraight || isLargeStraight) {
        return { name: DYNASTY_HANDS.DYNASTY_STRAIGHT.name, multiplier: DYNASTY_HANDS.DYNASTY_STRAIGHT.multiplier };
    }
    
    // Check for Three Generals (three of a kind)
    if (hasThreeOfAKind) {
        return { name: DYNASTY_HANDS.THREE_GENERALS.name, multiplier: DYNASTY_HANDS.THREE_GENERALS.multiplier };
    }
    
    // Check for Two Imperial Seals (two pairs)
    const pairCount = Object.values(valueCounts).filter(count => count === 2).length;
    if (pairCount === 2) {
        return { name: DYNASTY_HANDS.TWO_IMPERIAL_SEALS.name, multiplier: DYNASTY_HANDS.TWO_IMPERIAL_SEALS.multiplier };
    }
    
    // Check for Pair of Scholars (one pair)
    if (pairCount === 1) {
        return { name: DYNASTY_HANDS.PAIR_OF_SCHOLARS.name, multiplier: DYNASTY_HANDS.PAIR_OF_SCHOLARS.multiplier };
    }
    
    // No winning hand
    return { name: "No Winning Hand", multiplier: 0 };
}

/**
 * Get the numerical rank of a hand (for comparing hand strength)
 */
function getHandRank(handName) {
    const ranks = {
        "No Winning Hand": 0,
        "Pair of Scholars": 1,
        "Two Imperial Seals": 2,
        "Three Generals": 3,
        "Dynasty Straight": 4,
        "Full Imperial Court": 5,
        "Four Immortals": 6,
        "Five Emperors": 7
    };
    
    return ranks[handName] || 0;
}

/**
 * Show win/loss indicator
 */
function showWinLossIndicator(isWin, amount) {
    // Create indicator for sidebar
    const indicator = document.createElement('div');
    indicator.className = isWin ? 'win-indicator' : 'loss-indicator';
    indicator.textContent = isWin ? `+${amount} GA` : `-${amount} GA`;
    
    // Add to both desktop and mobile displays
    document.getElementById('winLossIndicator').innerHTML = '';
    document.getElementById('winLossIndicator').appendChild(indicator);
    
    // Create header indicator
    const headerIndicator = document.createElement('div');
    headerIndicator.className = isWin ? 'win-indicator' : 'loss-indicator';
    headerIndicator.innerHTML = `
        <i class="${isWin ? 'fas fa-trophy' : 'fas fa-times-circle'} indicator-icon"></i>
        <span>${isWin ? '+' : '-'}${Math.abs(amount)} GA</span>
    `;
    
    document.getElementById('headerWinLossIndicator').innerHTML = '';
    document.getElementById('headerWinLossIndicator').appendChild(headerIndicator);
    
    // Remove after animation finishes
    setTimeout(() => {
        if (document.getElementById('winLossIndicator').contains(indicator)) {
            document.getElementById('winLossIndicator').removeChild(indicator);
        }
        if (document.getElementById('headerWinLossIndicator').contains(headerIndicator)) {
            document.getElementById('headerWinLossIndicator').removeChild(headerIndicator);
        }
    }, 3000);
}

/**
 * Add a game to the history
 */
function addToHistory(game) {
    // Add to state
    gameState.history.unshift(game);
    if (gameState.history.length > 50) {
        gameState.history.pop();
    }
    
    // Update history display
    updateHistoryDisplay();
}

/**
 * Update the history display
 */
function updateHistoryDisplay() {
    // Update main history list
    const historyList = document.getElementById('historyList');
    historyList.innerHTML = '';
    
    // Update mobile history list
    const mobileHistoryList = document.getElementById('mobileHistoryList');
    mobileHistoryList.innerHTML = '';
    
    gameState.history.forEach(game => {
        // Format dice display
        const diceDisplay = game.dice.join(', ');
        const profitDisplay = game.profit >= 0 ? `+${game.profit}` : `${game.profit}`;
        const resultClass = game.profit >= 0 ? 'profit' : 'loss';
        
        // Time formatting
        const time = new Date(game.time);
        const timeString = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;
        
        // Create main history item
        const historyItem = document.createElement('div');
        historyItem.className = `history-item ${resultClass}`;
        historyItem.innerHTML = `
            <div class="history-round">Round #${game.round}</div>
            <div class="history-hand">${game.hand}</div>
            <div class="history-dice">${diceDisplay}</div>
            <div class="history-bet">${profitDisplay} GA</div>
        `;
        historyList.appendChild(historyItem);
        
        // Create mobile history item (simplified)
        const mobileHistoryItem = document.createElement('div');
        mobileHistoryItem.className = `history-item ${resultClass}`;
        mobileHistoryItem.innerHTML = `
            <div class="history-round">${game.hand}</div>
            <div class="history-bet">${profitDisplay} GA</div>
        `;
        mobileHistoryList.appendChild(mobileHistoryItem);
    });
}

/**
 * Update game statistics display
 */
function updateStats() {
    // Calculate win rate
    const winRate = gameState.roundsPlayed > 0 ? 
        Math.round((gameState.roundsWon / gameState.roundsPlayed) * 100) : 0;
    
    // Update desktop stats
    document.getElementById('roundsPlayed').textContent = gameState.roundsPlayed;
    document.getElementById('roundsWon').textContent = gameState.roundsWon;
    document.getElementById('bestHand').textContent = gameState.bestHand || '-';
    document.getElementById('winRate').textContent = `${winRate}%`;
    
    // Update mobile stats
    document.getElementById('mobileRoundsPlayed').textContent = gameState.roundsPlayed;
    document.getElementById('mobileRoundsWon').textContent = gameState.roundsWon;
    document.getElementById('mobileBestHand').textContent = gameState.bestHand || '-';
    document.getElementById('mobileWinRate').textContent = `${winRate}%`;
    document.getElementById('mobileWins').textContent = `Wins: ${gameState.roundsWon}`;
}

/**
 * Update Pro View analytics display
 */
function updateProAnalytics() {
    // Calculate derived metrics
    const netProfit = gameState.analytics.totalWon - gameState.analytics.totalWagered;
    const avgBet = gameState.roundsPlayed > 0 ? 
        Math.round(gameState.analytics.totalWagered / gameState.roundsPlayed) : 0;
    const roi = gameState.analytics.totalWagered > 0 ? 
        Math.round((netProfit / gameState.analytics.totalWagered) * 100) : 0;
    
    // Update display
    document.getElementById('totalWagered').textContent = `${gameState.analytics.totalWagered} GA`;
    document.getElementById('totalWon').textContent = `${gameState.analytics.totalWon} GA`;
    document.getElementById('netProfit').textContent = `${netProfit >= 0 ? '+' : ''}${netProfit} GA`;
    document.getElementById('avgBet').textContent = `${avgBet} GA`;
    document.getElementById('roi').textContent = `${roi}%`;
    document.getElementById('bestWin').textContent = `${gameState.analytics.bestWin} GA`;
    
    // Add appropriate classes based on profit/loss
    const netProfitEl = document.getElementById('netProfit');
    if (netProfit > 0) {
        netProfitEl.classList.add('positive');
        netProfitEl.classList.remove('negative');
    } else if (netProfit < 0) {
        netProfitEl.classList.add('negative');
        netProfitEl.classList.remove('positive');
    } else {
        netProfitEl.classList.remove('positive', 'negative');
    }
    
    // Update hands table
    updateHandsTable();
}

/**
 * Update the hands analytics table
 */
function updateHandsTable() {
    const tableBody = document.getElementById('handsTableBody');
    tableBody.innerHTML = '';
    
    const handNames = Object.keys(gameState.analytics.handCounts);
    const totalHands = Object.values(gameState.analytics.handCounts).reduce((sum, count) => sum + count, 0);
    
    handNames.forEach(hand => {
        const count = gameState.analytics.handCounts[hand];
        const winPercent = totalHands > 0 ? Math.round((count / totalHands) * 100) : 0;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${hand}</td>
            <td>${count}</td>
            <td>${winPercent}%</td>
        `;
        tableBody.appendChild(row);
    });
}

/**
 * Show the bet settings modal
 */
function showBetSettingsModal() {
    const modal = document.getElementById('betSettingsModal');
    modal.style.display = 'block';
    
    // Sync bet amount
    document.getElementById('modalBetAmount').value = gameState.currentBet;
}

/**
 * Close the bet settings modal
 */
function closeBetSettingsModal() {
    const modal = document.getElementById('betSettingsModal');
    modal.style.display = 'none';
}

/**
 * Apply settings from the modal
 */
function applyModalSettings() {
    const betAmount = parseInt(document.getElementById('modalBetAmount').value);
    
    // Validate and set bet amount
    if (betAmount > 0 && betAmount <= gameState.balance) {
        setBet(betAmount);
    }
    
    // Close modal
    closeBetSettingsModal();
}

/**
 * Adjust bet amount in the modal
 */
function adjustModalBet(amount) {
    const modalBetAmount = document.getElementById('modalBetAmount');
    let newBet = parseInt(modalBetAmount.value) + amount;
    
    // Ensure bet is within valid range
    newBet = Math.max(1, Math.min(newBet, gameState.balance));
    
    // Update input
    modalBetAmount.value = newBet;
}

/**
 * Toggle the mobile stats panel
 */
function toggleMobileStatsPanel() {
    const panel = document.getElementById('mobileStatsPanel');
    if (panel.classList.contains('active')) {
        closeMobileStatsPanel();
    } else {
        panel.classList.add('active');
        gameState.isMobileStatsOpen = true;
    }
}

/**
 * Close the mobile stats panel
 */
function closeMobileStatsPanel() {
    const panel = document.getElementById('mobileStatsPanel');
    panel.classList.remove('active');
    gameState.isMobileStatsOpen = false;
}

/**
 * Set the bet amount
 */
function setBet(amount) {
    // Validate bet amount
    amount = Math.max(1, Math.min(amount, gameState.balance));
    
    // Update game state
    gameState.currentBet = amount;
    
    // Update UI
    document.getElementById('betAmount').value = amount;
    document.getElementById('betButtonAmount').textContent = amount;
}

/**
 * Set bet to maximum available balance
 */
function setMaxBet() {
    setBet(gameState.balance);
}

/**
 * Adjust bet amount by increment/decrement
 */
function adjustBet(amount) {
    const newBet = gameState.currentBet + amount;
    setBet(newBet);
}

/**
 * Update bet amount from input field
 */
function updateBetFromInput() {
    const inputValue = parseInt(document.getElementById('betAmount').value);
    setBet(inputValue);
}

/**
 * Update balance display
 */
function updateBalanceDisplay() {
    document.getElementById('userBalance').textContent = gameState.balance;
    document.getElementById('mobileBalance').textContent = `${gameState.balance} GA`;
}

/**
 * Generate cryptographically secure client and server seeds
 */
function generateNewSeeds() {
    // Generate client seed
    const clientArray = new Uint8Array(16);
    crypto.getRandomValues(clientArray);
    gameState.clientSeed = Array.from(clientArray, byte => byte.toString(16).padStart(2, '0')).join('');
    
    // Generate server seed
    const serverArray = new Uint8Array(32);
    crypto.getRandomValues(serverArray);
    gameState.serverSeed = Array.from(serverArray, byte => byte.toString(16).padStart(2, '0')).join('');
    
    // Create hash of server seed
    gameState.serverSeedHash = CryptoJS.SHA256(gameState.serverSeed).toString();
    
    // Reset nonce
    gameState.nonce = 0;
    
    // Update display
    document.getElementById('clientSeed').textContent = gameState.clientSeed;
    document.getElementById('serverSeedHash').textContent = gameState.serverSeedHash.substring(0, 16) + '...';
    document.getElementById('nonce').textContent = gameState.nonce;
}

/**
 * Verify a roll using provided seeds and nonce
 */
function verifyRoll() {
    const clientSeed = document.getElementById('verifyClientSeed').value;
    const serverSeed = document.getElementById('verifyServerSeed').value;
    const nonce = parseInt(document.getElementById('verifyNonce').value);
    
    const verifyResult = document.getElementById('verifyResult');
    
    if (!clientSeed || !serverSeed || isNaN(nonce)) {
        verifyResult.textContent = 'Please fill in all fields';
        verifyResult.className = 'verify-result error';
        verifyResult.style.display = 'block';
        return;
    }
    
    // Verify that server seed hash matches
    const calculatedHash = CryptoJS.SHA256(serverSeed).toString();
    
    // Simulate the roll
    const simulated = [];
    for (let i = 0; i < 5; i++) {
        // Generate hash for each die for all rolls (initial + 2 re-rolls)
        for (let roll = 1; roll <= 3; roll++) {
            const dataToHash = clientSeed + serverSeed + nonce + i + roll;
            const hash = CryptoJS.SHA256(dataToHash).toString();
            const rand = parseInt(hash.substr(0, 8), 16);
            const dieValue = (rand % 6) + 1;
            simulated.push(`Die ${i+1}, Roll ${roll}: ${dieValue}`);
        }
    }
    
    // Display verification result
    verifyResult.innerHTML = `
        <p>Server seed hash: ${calculatedHash.substring(0, 16) + '...'}</p>
        <p>Simulated dice rolls:</p>
        <div style="font-family: monospace; font-size: 0.8rem;">${simulated.join('<br>')}</div>
    `;
    verifyResult.className = 'verify-result success';
    verifyResult.style.display = 'block';
}

/**
 * Toggle help content
 */
function toggleHelp() {
    const helpContent = document.getElementById('helpContent');
    const toggleButton = document.getElementById('toggleHelp');
    
    if (helpContent.style.display === 'block') {
        helpContent.style.display = 'none';
        toggleButton.textContent = 'Show Rules & Strategy';
    } else {
        helpContent.style.display = 'block';
        toggleButton.textContent = 'Hide Rules & Strategy';
    }
}

/**
 * Toggle fairness content
 */
function toggleFairness() {
    const fairnessContent = document.getElementById('fairnessContent');
    const toggleButton = document.getElementById('toggleFairness');
    
    if (fairnessContent.style.display === 'block') {
        fairnessContent.style.display = 'none';
        toggleButton.textContent = 'Learn How It Works';
    } else {
        fairnessContent.style.display = 'block';
        toggleButton.textContent = 'Hide Details';
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element if it doesn't exist
    let notification = document.querySelector('.notification');
    if (!notification) {
        notification = document.createElement('div');
        notification.className = 'notification';
        document.body.appendChild(notification);
    }
    
    // Set message and type
    notification.textContent = message;
    notification.className = `notification notification-${type}`;
    
    // Show notification
    notification.style.display = 'block';
    
    // Hide after 3 seconds
    setTimeout(() => {
        notification.style.display = 'none';
    }, 3000);
}

/**
 * Load user balance from localStorage
 */
function loadUserBalance() {
    const savedBalance = localStorage.getItem('diceDynastyBalance');
    if (savedBalance) {
        gameState.balance = parseInt(savedBalance);
    }
    
    const savedStats = localStorage.getItem('diceDynastyStats');
    if (savedStats) {
        const stats = JSON.parse(savedStats);
        gameState.roundsPlayed = stats.roundsPlayed || 0;
        gameState.roundsWon = stats.roundsWon || 0;
        gameState.bestHand = stats.bestHand || null;
        gameState.analytics = stats.analytics || gameState.analytics;
    }
    
    // Update UI
    updateBalanceDisplay();
}

/**
 * Save user balance to localStorage
 */
function saveUserBalance() {
    localStorage.setItem('diceDynastyBalance', gameState.balance.toString());
    
    // Save stats
    const stats = {
        roundsPlayed: gameState.roundsPlayed,
        roundsWon: gameState.roundsWon,
        bestHand: gameState.bestHand,
        analytics: gameState.analytics
    };
    
    localStorage.setItem('diceDynastyStats', JSON.stringify(stats));
}