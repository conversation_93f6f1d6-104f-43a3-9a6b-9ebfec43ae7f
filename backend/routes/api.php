<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\BlackjackController;
use App\Http\Controllers\SportController;
use App\Http\Controllers\BetController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\AnalyticsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Sports public data (no auth required)
Route::prefix('sports')->group(function () {
    Route::get('/', [SportController::class, 'index']);
    Route::get('/featured', [SportController::class, 'featured']);
    Route::get('/live', [SportController::class, 'liveMatches']);
    Route::get('/{sport}', [SportController::class, 'show']);
    Route::get('/{sport}/matches', [SportController::class, 'matches']);
});

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User profile & authentication
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::put('/user', [AuthController::class, 'update']);
    
    // Wallet & Transactions
    Route::prefix('wallet')->group(function () {
        Route::get('/', [WalletController::class, 'index']);
        Route::post('/deposit', [WalletController::class, 'deposit']);
        Route::post('/withdraw', [WalletController::class, 'withdraw']);
        Route::get('/transactions', [WalletController::class, 'transactions']);
    });
    
    // Casino Games
    Route::prefix('games')->group(function () {
        // General game endpoints
        Route::get('/', [GameController::class, 'index']);
        Route::get('/{game}', [GameController::class, 'show']);
        Route::get('/{game}/history', [GameController::class, 'history']);
        Route::post('/transaction', [GameController::class, 'recordTransaction']);

        // Blackjack specific endpoints
        Route::prefix('blackjack')->group(function () {
            Route::post('/bet', [BlackjackController::class, 'placeBet']);
            Route::post('/hit', [BlackjackController::class, 'hit']);
            Route::post('/stand', [BlackjackController::class, 'stand']);
            Route::post('/double', [BlackjackController::class, 'double']);
            Route::post('/split', [BlackjackController::class, 'split']);
            Route::post('/surrender', [BlackjackController::class, 'surrender']);
        });

        // Add other game controllers here
        // Route::prefix('dice')->group(function () { ... });
        // Route::prefix('mines')->group(function () { ... });
        // Route::prefix('crash')->group(function () { ... });
    });
    
    // Sports Betting
    Route::prefix('sports')->group(function () {
        Route::post('/bets', [BetController::class, 'place']);
        Route::get('/bets', [BetController::class, 'index']);
        Route::get('/bets/{bet}', [BetController::class, 'show']);
        Route::post('/bets/{bet}/cancel', [BetController::class, 'cancel']);
    });
    
    // Notifications
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::post('/{notification}/read', [NotificationController::class, 'markAsRead']);
        Route::post('/read-all', [NotificationController::class, 'markAllAsRead']);
        Route::delete('/{notification}', [NotificationController::class, 'destroy']);
    });
    
    // Analytics & Statistics (for user dashboard)
    Route::prefix('analytics')->group(function () {
        Route::get('/dashboard', [AnalyticsController::class, 'dashboard']);
        Route::get('/gaming-stats', [AnalyticsController::class, 'gamingStats']);
        Route::get('/betting-stats', [AnalyticsController::class, 'bettingStats']);
        Route::get('/recent-activity', [AnalyticsController::class, 'recentActivity']);
    });
});

// Admin routes (require admin role)
Route::middleware(['auth:sanctum', 'role:admin'])->prefix('admin')->group(function () {
    // User management
    Route::prefix('users')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\UserController::class, 'index']);
        Route::get('/{user}', [App\Http\Controllers\Admin\UserController::class, 'show']);
        Route::put('/{user}', [App\Http\Controllers\Admin\UserController::class, 'update']);
        Route::post('/{user}/suspend', [App\Http\Controllers\Admin\UserController::class, 'suspend']);
        Route::post('/{user}/activate', [App\Http\Controllers\Admin\UserController::class, 'activate']);
    });
    
    // Game management
    Route::prefix('games')->group(function () {
        Route::get('/sessions', [App\Http\Controllers\Admin\GameController::class, 'sessions']);
        Route::get('/sessions/{session}', [App\Http\Controllers\Admin\GameController::class, 'sessionDetails']);
        Route::post('/sessions/{session}/verify', [App\Http\Controllers\Admin\GameController::class, 'verifySession']);
    });
    
    // Sports management
    Route::prefix('sports')->group(function () {
        Route::post('/sync', [App\Http\Controllers\Admin\SportController::class, 'syncSports']);
        Route::post('/{sport}/sync-matches', [App\Http\Controllers\Admin\SportController::class, 'syncMatches']);
        Route::put('/{sport}', [App\Http\Controllers\Admin\SportController::class, 'update']);
        Route::post('/{sport}/toggle', [App\Http\Controllers\Admin\SportController::class, 'toggle']);
    });
    
    // Bet management
    Route::prefix('bets')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\BetController::class, 'index']);
        Route::get('/{bet}', [App\Http\Controllers\Admin\BetController::class, 'show']);
        Route::post('/{bet}/settle', [App\Http\Controllers\Admin\BetController::class, 'settle']);
        Route::post('/{bet}/void', [App\Http\Controllers\Admin\BetController::class, 'void']);
    });
    
    // Transaction management
    Route::prefix('transactions')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\TransactionController::class, 'index']);
        Route::get('/{transaction}', [App\Http\Controllers\Admin\TransactionController::class, 'show']);
        Route::post('/{transaction}/approve', [App\Http\Controllers\Admin\TransactionController::class, 'approve']);
        Route::post('/{transaction}/reject', [App\Http\Controllers\Admin\TransactionController::class, 'reject']);
    });
    
    // Analytics & Reports
    Route::prefix('analytics')->group(function () {
        Route::get('/overview', [App\Http\Controllers\Admin\AnalyticsController::class, 'overview']);
        Route::get('/revenue', [App\Http\Controllers\Admin\AnalyticsController::class, 'revenue']);
        Route::get('/users', [App\Http\Controllers\Admin\AnalyticsController::class, 'users']);
        Route::get('/games', [App\Http\Controllers\Admin\AnalyticsController::class, 'games']);
        Route::get('/sports', [App\Http\Controllers\Admin\AnalyticsController::class, 'sports']);
    });
    
    // System management
    Route::prefix('system')->group(function () {
        Route::get('/status', [App\Http\Controllers\Admin\SystemController::class, 'status']);
        Route::get('/logs', [App\Http\Controllers\Admin\SystemController::class, 'logs']);
        Route::post('/maintenance', [App\Http\Controllers\Admin\SystemController::class, 'maintenance']);
        Route::post('/cache/clear', [App\Http\Controllers\Admin\SystemController::class, 'clearCache']);
    });
});