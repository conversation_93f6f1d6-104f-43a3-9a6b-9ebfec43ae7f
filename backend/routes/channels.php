<?php

use Illuminate\Support\Facades\Broadcast;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// User private channel - for personal notifications and updates
Broadcast::channel('user.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Game session private channel - for real-time game updates
Broadcast::channel('game.{gameSessionId}', function ($user, $gameSessionId) {
    // Check if user owns this game session
    return \App\Models\GameSession::where('id', $gameSessionId)
        ->where('user_id', $user->id)
        ->exists();
});

// Match channel - for live match updates (public)
Broadcast::channel('match.{matchId}', function () {
    return true; // Public channel for match updates
});

// Sport channel - for sport-specific updates (public)
Broadcast::channel('sport.{sportSlug}', function () {
    return true; // Public channel for sport updates
});

// Bets channel - for general betting activity (public)
Broadcast::channel('bets', function () {
    return true; // Public channel for betting activity
});

// Live games channel - for live casino games (public)
Broadcast::channel('live-games', function () {
    return true; // Public channel for live game updates
});

// Admin channels (require admin role)
Broadcast::channel('admin.system', function ($user) {
    return $user->hasRole('admin');
});

Broadcast::channel('admin.users', function ($user) {
    return $user->hasRole('admin');
});

Broadcast::channel('admin.transactions', function ($user) {
    return $user->hasRole('admin');
});

// Presence channel for online users count
Broadcast::channel('online-users', function ($user) {
    return [
        'id' => $user->id,
        'username' => $user->username,
    ];
});