{"name": "goldenaura/backend", "type": "project", "description": "Backend API for GoldenAura Gaming Platform", "keywords": ["laravel", "framework", "api", "gaming", "betting"], "license": "MIT", "require": {"php": "^8.2", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.8", "pusher/pusher-php-server": "^7.2", "beyondcode/laravel-websockets": "^1.14", "predis/predis": "^2.0", "laravel/horizon": "^5.0", "laravel/telescope": "^4.0", "spatie/laravel-permission": "^5.0", "spatie/laravel-activitylog": "^4.0"}, "require-dev": {"fakerphp/faker": "^1.23.0", "laravel/pint": "^1.13.6", "laravel/sail": "^1.26.0", "mockery/mockery": "^1.6.6", "nunomaduro/collision": "^8.1.0", "phpunit/phpunit": "^10.5.0", "spatie/laravel-ignition": "^2.4.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}