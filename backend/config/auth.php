<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Defaults
    |--------------------------------------------------------------------------
    |
    | This option controls the default authentication "guard" and password
    | reset options for your application. You may change these defaults
    | as required, but they're a perfect start for most applications.
    |
    */

    'defaults' => [
        'guard' => 'web',
        'passwords' => 'users',
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Guards
    |--------------------------------------------------------------------------
    |
    | Next, you may define every authentication guard for your application.
    | Of course, a great default configuration has been defined for you
    | here which uses session storage and the Eloquent user provider.
    |
    | All authentication drivers have a user provider. This defines how the
    | users are actually retrieved out of your database or other storage
    | mechanisms used by this application to persist your user's data.
    |
    | Supported: "session", "token"
    |
    */

    'guards' => [
        'web' => [
            'driver' => 'session',
            'provider' => 'users',
        ],

        'api' => [
            'driver' => 'sanctum',
            'provider' => 'users',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Providers
    |--------------------------------------------------------------------------
    |
    | All authentication drivers have a user provider. This defines how the
    | users are actually retrieved out of your database or other storage
    | mechanisms used by this application to persist your user's data.
    |
    | If you have multiple user tables or models you may configure multiple
    | sources which represent each model / table. These sources may then
    | be assigned to any extra authentication guards you have defined.
    |
    | Supported: "database", "eloquent"
    |
    */

    'providers' => [
        'users' => [
            'driver' => 'eloquent',
            'model' => App\Models\User::class,
        ],

        // 'users' => [
        //     'driver' => 'database',
        //     'table' => 'users',
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Resetting Passwords
    |--------------------------------------------------------------------------
    |
    | You may specify multiple password reset configurations if you have more
    | than one user table or model in the application and you want to have
    | separate password reset settings based on the specific user types.
    |
    | The expire time is the number of minutes that the reset token should be
    | considered valid. This security feature keeps tokens short-lived so
    | they have less time to be guessed. You may change this as needed.
    |
    */

    'passwords' => [
        'users' => [
            'provider' => 'users',
            'table' => 'password_reset_tokens',
            'expire' => 60,
            'throttle' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Confirmation Timeout
    |--------------------------------------------------------------------------
    |
    | Here you may define the amount of seconds before a password confirmation
    | times out and the user is prompted to re-enter their password via the
    | confirmation screen. By default, the timeout lasts for three hours.
    |
    */

    'password_timeout' => 10800,

    /*
    |--------------------------------------------------------------------------
    | JWT Configuration
    |--------------------------------------------------------------------------
    |
    | Although Sanctum is used by default, we maintain JWT configuration
    | for compatibility with other systems and integration flexibility.
    |
    */

    'jwt' => [
        'secret' => env('JWT_SECRET'),
        'ttl' => env('JWT_TTL', 60), // Time to live in minutes
        'refresh_ttl' => env('JWT_REFRESH_TTL', 20160), // 2 weeks
        'algo' => 'HS256',
        'blacklist_enabled' => true,
        'blacklist_grace_period' => 30, // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Two Factor Authentication
    |--------------------------------------------------------------------------
    |
    | Here you may configure two-factor authentication options.
    |
    */

    'two_factor' => [
        'enabled' => true,
        'timeout' => 300, // seconds
        'qr_size' => 200, // pixels
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Authentication
    |--------------------------------------------------------------------------
    |
    | Configure social authentication providers
    |
    */

    'social' => [
        'google' => [
            'enabled' => env('SOCIAL_AUTH_GOOGLE_ENABLED', false),
            'client_id' => env('SOCIAL_AUTH_GOOGLE_CLIENT_ID'),
            'client_secret' => env('SOCIAL_AUTH_GOOGLE_CLIENT_SECRET'),
            'redirect' => env('SOCIAL_AUTH_GOOGLE_REDIRECT'),
        ],
        'facebook' => [
            'enabled' => env('SOCIAL_AUTH_FACEBOOK_ENABLED', false),
            'client_id' => env('SOCIAL_AUTH_FACEBOOK_CLIENT_ID'),
            'client_secret' => env('SOCIAL_AUTH_FACEBOOK_CLIENT_SECRET'),
            'redirect' => env('SOCIAL_AUTH_FACEBOOK_REDIRECT'),
        ],
        'apple' => [
            'enabled' => env('SOCIAL_AUTH_APPLE_ENABLED', false),
            'client_id' => env('SOCIAL_AUTH_APPLE_CLIENT_ID'),
            'client_secret' => env('SOCIAL_AUTH_APPLE_CLIENT_SECRET'),
            'redirect' => env('SOCIAL_AUTH_APPLE_REDIRECT'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security options for authentication
    |
    */

    'security' => [
        'password_history' => 5, // Remember last 5 passwords
        'max_login_attempts' => 5, // Maximum allowed failed login attempts
        'lockout_time' => 15, // Lockout time in minutes after max attempts
        'password_age' => 90, // Password expiry in days
        'password_min_length' => 8,
        'password_requires_uppercase' => true,
        'password_requires_lowercase' => true,
        'password_requires_numeric' => true,
        'password_requires_special_char' => true,
        'session_timeout' => 120, // minutes
        'ip_tracking' => true,
        'device_tracking' => true,
    ],

];