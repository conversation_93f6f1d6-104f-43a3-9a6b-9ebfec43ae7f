<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Dashboard Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure the dashboard settings for Laravel WebSockets.
    |
    */

    'dashboard' => [
        'port' => env('LARAVEL_WEBSOCKETS_PORT', 6001),
    ],

    /*
    |--------------------------------------------------------------------------
    | WebSocket Apps
    |--------------------------------------------------------------------------
    |
    | Here we can configure the WebSocket apps that can connect to this
    | server. Each app can have its own key and secret for authentication.
    |
    */

    'apps' => [
        [
            'id' => env('PUSHER_APP_ID', 'goldenaura-app'),
            'name' => env('APP_NAME', 'GoldenAura'),
            'key' => env('PUSHER_APP_KEY', 'goldenaura-key'),
            'secret' => env('PUSHER_APP_SECRET', 'goldenaura-secret'),
            'path' => env('PUSHER_APP_PATH'),
            'capacity' => null,
            'enable_client_messages' => false,
            'enable_statistics' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | WebSockets Class Binding
    |--------------------------------------------------------------------------
    |
    | This array defines the class to use for each WebSocket concern.
    |
    */

    'managers' => [
        'app' => BeyondCode\LaravelWebSockets\Apps\AppManager::class,
        'channel' => BeyondCode\LaravelWebSockets\WebSockets\Channels\ChannelManager::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Statistics
    |--------------------------------------------------------------------------
    |
    | Here you can specify the interval in seconds at which statistics should
    | be logged.
    |
    */

    'statistics' => [
        'model' => \BeyondCode\LaravelWebSockets\Statistics\Models\WebSocketsStatisticsEntry::class,
        'logger' => BeyondCode\LaravelWebSockets\Statistics\Logger\HttpStatisticsLogger::class,
        'interval_in_seconds' => 60,
        'delete_statistics_older_than_days' => 60,
        'perform_dns_lookup' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | SSL
    |--------------------------------------------------------------------------
    |
    | By setting the SSL configuration, you can set up a secured WebSocket
    | server. Using a reverse proxy to handle SSL connections is
    | recommended instead.
    |
    */

    'ssl' => [
        'local_cert' => env('LARAVEL_WEBSOCKETS_SSL_LOCAL_CERT', null),
        'local_pk' => env('LARAVEL_WEBSOCKETS_SSL_LOCAL_PK', null),
        'passphrase' => env('LARAVEL_WEBSOCKETS_SSL_PASSPHRASE', null),
    ],

    /*
    |--------------------------------------------------------------------------
    | Channel Manager
    |--------------------------------------------------------------------------
    |
    | This configuration is passed to the channel manager specified above.
    |
    */

    'channel_manager' => [
        'cache_prefix' => 'laravel_websockets',
        'ttl' => 3600,
    ],

    /*
    |--------------------------------------------------------------------------
    | Replication
    |--------------------------------------------------------------------------
    |
    | You can run the WebSocket server in multiple locations/servers and use
    | a Redis instance to communicate between them.
    |
    */

    'replication' => [
        'mode' => env('LARAVEL_WEBSOCKETS_REPLICATION_MODE', 'local'),
        'modes' => [
            'local' => [
                'type' => 'local',
            ],
            'redis' => [
                'type' => 'redis',
                'connection' => env('LARAVEL_WEBSOCKETS_REDIS_REPLICATION_CONNECTION', 'default'),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Max Request Size
    |--------------------------------------------------------------------------
    |
    | The maximum request size in kilobytes that is allowed for an incoming
    | WebSocket request.
    |
    */

    'max_request_size_in_kb' => 250,

];