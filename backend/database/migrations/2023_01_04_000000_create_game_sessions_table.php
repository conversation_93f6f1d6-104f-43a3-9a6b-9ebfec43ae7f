<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('game_type');
            $table->string('server_seed');
            $table->string('server_seed_hash');
            $table->string('client_seed');
            $table->integer('nonce')->default(0);
            $table->string('status'); // active, completed, canceled
            $table->json('result')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'game_type']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_sessions');
    }
};