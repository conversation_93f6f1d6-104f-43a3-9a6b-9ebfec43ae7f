<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_security', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('two_factor_enabled')->default(false);
            $table->string('two_factor_secret')->nullable();
            $table->string('two_factor_recovery_codes')->nullable();
            $table->string('two_factor_confirmed_at')->nullable();
            $table->integer('failed_login_attempts')->default(0);
            $table->timestamp('locked_until')->nullable();
            $table->json('known_devices')->nullable();
            $table->json('known_ips')->nullable();
            $table->timestamp('password_changed_at')->nullable();
            $table->json('previous_passwords')->nullable();
            $table->json('security_questions')->nullable();
            $table->string('recovery_email')->nullable();
            $table->timestamps();
        });

        Schema::create('user_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('device_name');
            $table->string('device_type');
            $table->string('ip_address');
            $table->string('user_agent');
            $table->string('geolocation')->nullable();
            $table->boolean('is_current')->default(false);
            $table->timestamp('last_active_at');
            $table->timestamps();
        });

        Schema::create('login_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('ip_address');
            $table->string('user_agent');
            $table->boolean('success');
            $table->string('failure_reason')->nullable();
            $table->string('geolocation')->nullable();
            $table->timestamps();
        });

        Schema::create('suspicious_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('activity_type');
            $table->string('ip_address');
            $table->string('user_agent');
            $table->text('description');
            $table->json('additional_data')->nullable();
            $table->boolean('is_resolved')->default(false);
            $table->timestamp('resolved_at')->nullable();
            $table->string('resolved_by')->nullable();
            $table->text('resolution_notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suspicious_activities');
        Schema::dropIfExists('login_history');
        Schema::dropIfExists('user_sessions');
        Schema::dropIfExists('user_security');
    }
};