<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('game_session_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('match_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('amount', 14, 2);
            $table->string('type'); // single, parlay, casino
            $table->decimal('odds', 8, 2)->nullable();
            $table->decimal('potential_payout', 14, 2);
            $table->string('status'); // pending, won, lost, push, canceled
            $table->string('result')->nullable();
            $table->json('selections')->nullable(); // For sports betting multiple selections
            $table->json('meta')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bets');
    }
};