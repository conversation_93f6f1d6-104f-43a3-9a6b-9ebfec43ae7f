<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bonuses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique()->nullable();
            $table->enum('type', ['deposit', 'free_bet', 'cashback', 'free_spin', 'loyalty', 'welcome']);
            $table->decimal('amount', 14, 2);
            $table->decimal('percentage', 8, 2)->nullable();
            $table->decimal('min_deposit', 14, 2)->nullable();
            $table->decimal('max_bonus', 14, 2)->nullable();
            $table->integer('wagering_requirement')->default(0); // multiplier
            $table->integer('validity_days')->default(30);
            $table->json('eligible_games')->nullable();
            $table->json('game_contribution_percentages')->nullable();
            $table->boolean('is_first_deposit_only')->default(false);
            $table->timestamp('start_date');
            $table->timestamp('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('max_uses_per_user')->default(1);
            $table->integer('max_total_uses')->nullable();
            $table->integer('uses_count')->default(0);
            $table->text('description')->nullable();
            $table->text('terms_conditions')->nullable();
            $table->timestamps();
        });

        Schema::create('user_bonuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('bonus_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 14, 2);
            $table->decimal('wagering_requirement_amount', 14, 2);
            $table->decimal('wagered_amount', 14, 2)->default(0);
            $table->decimal('remaining_amount', 14, 2);
            $table->enum('status', ['active', 'completed', 'expired', 'cancelled']);
            $table->timestamp('activated_at');
            $table->timestamp('expires_at');
            $table->timestamp('completed_at')->nullable();
            $table->foreignId('transaction_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamps();
        });

        Schema::create('loyalty_programs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('levels_count');
            $table->json('levels_config');
            $table->json('benefits_config');
            $table->timestamps();
        });

        Schema::create('user_loyalty', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('loyalty_program_id')->constrained()->onDelete('cascade');
            $table->integer('current_level');
            $table->integer('points');
            $table->decimal('total_wagered', 14, 2)->default(0);
            $table->integer('days_active');
            $table->timestamp('last_level_up')->nullable();
            $table->timestamps();
        });

        Schema::create('user_bonus_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('bonus_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_bonus_id')->constrained()->onDelete('cascade');
            $table->string('action_type');
            $table->decimal('amount', 14, 2);
            $table->json('details')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_bonus_history');
        Schema::dropIfExists('user_loyalty');
        Schema::dropIfExists('loyalty_programs');
        Schema::dropIfExists('user_bonuses');
        Schema::dropIfExists('bonuses');
    }
};