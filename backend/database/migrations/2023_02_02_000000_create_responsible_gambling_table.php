<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('responsible_gambling_limits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('deposit_daily_limit', 14, 2)->nullable();
            $table->decimal('deposit_weekly_limit', 14, 2)->nullable();
            $table->decimal('deposit_monthly_limit', 14, 2)->nullable();
            $table->decimal('loss_daily_limit', 14, 2)->nullable();
            $table->decimal('loss_weekly_limit', 14, 2)->nullable();
            $table->decimal('loss_monthly_limit', 14, 2)->nullable();
            $table->integer('session_time_limit')->nullable(); // in minutes
            $table->boolean('reality_check_enabled')->default(false);
            $table->integer('reality_check_interval')->nullable(); // in minutes
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_updated')->nullable();
            $table->timestamp('effective_from')->nullable();
            $table->timestamps();
        });

        Schema::create('self_exclusions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['temporary', 'permanent']);
            $table->timestamp('start_date');
            $table->timestamp('end_date')->nullable();
            $table->text('reason')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('reality_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->timestamp('check_time');
            $table->timestamp('acknowledged_at')->nullable();
            $table->json('session_summary')->nullable();
            $table->timestamps();
        });

        Schema::create('gambling_behavior_patterns', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('average_bet_amount', 14, 2);
            $table->integer('bets_per_hour');
            $table->decimal('deposit_frequency', 8, 2); // deposits per day
            $table->decimal('loss_percentage', 8, 2);
            $table->integer('chasing_losses_pattern'); // scale 1-10
            $table->integer('risk_score'); // 1-100 scale
            $table->json('additional_metrics')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('gambling_behavior_patterns');
        Schema::dropIfExists('reality_checks');
        Schema::dropIfExists('self_exclusions');
        Schema::dropIfExists('responsible_gambling_limits');
    }
};