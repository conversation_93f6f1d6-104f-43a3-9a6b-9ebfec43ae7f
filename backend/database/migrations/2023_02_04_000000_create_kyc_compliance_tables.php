<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('kyc_verification', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['not_started', 'pending', 'in_progress', 'verified', 'rejected'])->default('not_started');
            $table->enum('identity_verification', ['not_submitted', 'pending', 'verified', 'rejected'])->default('not_submitted');
            $table->enum('address_verification', ['not_submitted', 'pending', 'verified', 'rejected'])->default('not_submitted');
            $table->enum('age_verification', ['not_verified', 'verified'])->default('not_verified');
            $table->timestamp('identity_submitted_at')->nullable();
            $table->timestamp('address_submitted_at')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->string('rejection_reason')->nullable();
            $table->timestamps();
        });

        Schema::create('kyc_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('document_type', ['id_card', 'passport', 'driving_license', 'utility_bill', 'bank_statement', 'other']);
            $table->string('document_number')->nullable();
            $table->string('issuing_country')->nullable();
            $table->date('issue_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->string('file_path');
            $table->enum('status', ['pending', 'verified', 'rejected'])->default('pending');
            $table->string('rejection_reason')->nullable();
            $table->string('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();
        });

        Schema::create('aml_checks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('check_type', ['pep', 'sanctions', 'adverse_media', 'source_of_funds', 'transaction_monitoring']);
            $table->enum('result', ['passed', 'flagged', 'failed'])->default('passed');
            $table->text('details')->nullable();
            $table->string('checked_by')->nullable();
            $table->timestamps();
        });

        Schema::create('suspicious_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('transaction_id')->nullable()->constrained()->onDelete('set null');
            $table->string('transaction_type');
            $table->decimal('amount', 14, 2);
            $table->string('detection_method'); // algorithm, manual, etc.
            $table->integer('risk_score'); // 1-100
            $table->text('reason');
            $table->enum('status', ['detected', 'investigating', 'reported', 'resolved', 'false_positive'])->default('detected');
            $table->text('investigation_notes')->nullable();
            $table->string('investigated_by')->nullable();
            $table->string('reported_to')->nullable();
            $table->timestamp('reported_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suspicious_transactions');
        Schema::dropIfExists('aml_checks');
        Schema::dropIfExists('kyc_documents');
        Schema::dropIfExists('kyc_verification');
    }
};