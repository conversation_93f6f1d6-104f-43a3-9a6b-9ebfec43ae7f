<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('matches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sport_id')->constrained()->onDelete('cascade');
            $table->string('external_id')->nullable(); // ID from external API
            $table->string('home_team');
            $table->string('away_team');
            $table->timestamp('commence_time');
            $table->string('status')->default('upcoming'); // upcoming, live, completed, cancelled
            $table->json('scores')->nullable();
            $table->decimal('home_odds', 8, 2)->nullable();
            $table->decimal('draw_odds', 8, 2)->nullable();
            $table->decimal('away_odds', 8, 2)->nullable();
            $table->json('additional_markets')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
            
            $table->index(['sport_id', 'status']);
            $table->index('commence_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('matches');
    }
};