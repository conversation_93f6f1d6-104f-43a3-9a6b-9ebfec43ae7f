<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->decimal('total_deposits', 14, 2)->default(0);
            $table->decimal('total_withdrawals', 14, 2)->default(0);
            $table->decimal('total_bets', 14, 2)->default(0);
            $table->decimal('total_wins', 14, 2)->default(0);
            $table->integer('total_games_played')->default(0);
            $table->integer('login_count')->default(0);
            $table->timestamp('first_deposit_date')->nullable();
            $table->timestamp('last_deposit_date')->nullable();
            $table->timestamp('first_bet_date')->nullable();
            $table->timestamp('last_bet_date')->nullable();
            $table->json('favorite_games')->nullable();
            $table->json('favorite_sports')->nullable();
            $table->integer('avg_session_minutes')->default(0);
            $table->decimal('avg_bet_amount', 14, 2)->default(0);
            $table->decimal('largest_win', 14, 2)->default(0);
            $table->decimal('net_profit_loss', 14, 2)->default(0);
            $table->decimal('conversion_rate', 8, 2)->default(0);
            $table->decimal('churn_probability', 8, 2)->default(0);
            $table->integer('customer_value')->default(0);
            $table->string('acquisition_source')->nullable();
            $table->string('acquisition_campaign')->nullable();
            $table->timestamps();
        });

        Schema::create('daily_metrics', function (Blueprint $table) {
            $table->id();
            $table->date('date');
            $table->integer('new_users')->default(0);
            $table->integer('active_users')->default(0);
            $table->decimal('total_deposits', 14, 2)->default(0);
            $table->decimal('total_withdrawals', 14, 2)->default(0);
            $table->decimal('total_bets', 14, 2)->default(0);
            $table->decimal('total_wins', 14, 2)->default(0);
            $table->decimal('gross_gaming_revenue', 14, 2)->default(0);
            $table->decimal('net_gaming_revenue', 14, 2)->default(0);
            $table->integer('casino_games_played')->default(0);
            $table->integer('sports_bets_placed')->default(0);
            $table->decimal('casino_bets', 14, 2)->default(0);
            $table->decimal('sports_bets', 14, 2)->default(0);
            $table->decimal('casino_wins', 14, 2)->default(0);
            $table->decimal('sports_wins', 14, 2)->default(0);
            $table->decimal('bonus_amount_issued', 14, 2)->default(0);
            $table->decimal('bonus_amount_wagered', 14, 2)->default(0);
            $table->decimal('conversion_rate', 8, 2)->default(0);
            $table->decimal('retention_rate', 8, 2)->default(0);
            $table->json('top_games')->nullable();
            $table->json('top_sports')->nullable();
            $table->json('geographic_distribution')->nullable();
            $table->timestamps();
        });

        Schema::create('game_metrics', function (Blueprint $table) {
            $table->id();
            $table->string('game_type');
            $table->date('date');
            $table->integer('unique_players')->default(0);
            $table->integer('sessions')->default(0);
            $table->integer('rounds_played')->default(0);
            $table->decimal('total_bets', 14, 2)->default(0);
            $table->decimal('total_wins', 14, 2)->default(0);
            $table->decimal('house_edge', 8, 4)->default(0);
            $table->decimal('rtp_percentage', 8, 4)->default(0);
            $table->decimal('average_bet', 14, 2)->default(0);
            $table->integer('average_session_length')->default(0); // seconds
            $table->decimal('profit_per_player', 14, 2)->default(0);
            $table->json('bet_distribution')->nullable();
            $table->json('win_distribution')->nullable();
            $table->timestamps();
        });

        Schema::create('funnel_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_name');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id')->nullable();
            $table->string('device_type')->nullable();
            $table->string('browser')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('country')->nullable();
            $table->string('referrer')->nullable();
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->json('event_properties')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('funnel_events');
        Schema::dropIfExists('game_metrics');
        Schema::dropIfExists('daily_metrics');
        Schema::dropIfExists('user_metrics');
    }
};