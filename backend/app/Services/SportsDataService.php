<?php

namespace App\Services;

use App\Models\Sport;
use App\Models\Match;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SportsDataService
{
    protected $apiProvider;
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiProvider = config('app.sports_api_provider', 'the-odds-api');
        $this->setApiConfiguration();
    }

    /**
     * Set API configuration based on provider
     */
    protected function setApiConfiguration()
    {
        switch ($this->apiProvider) {
            case 'the-odds-api':
                $this->apiKey = config('app.the_odds_api_key');
                $this->baseUrl = 'https://api.the-odds-api.com/v4';
                break;
            case 'api-football':
                $this->apiKey = config('app.api_football_key');
                $this->baseUrl = 'https://v3.football.api-sports.io';
                break;
            default:
                throw new \Exception('Unsupported sports API provider');
        }
    }

    /**
     * Sync sports from external API
     */
    public function syncSports()
    {
        try {
            if ($this->apiProvider === 'the-odds-api') {
                return $this->syncSportsFromOddsApi();
            } elseif ($this->apiProvider === 'api-football') {
                return $this->syncSportsFromApiFootball();
            }
        } catch (\Exception $e) {
            Log::error('Failed to sync sports: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync sports from The Odds API
     */
    protected function syncSportsFromOddsApi()
    {
        $response = Http::get($this->baseUrl . '/sports', [
            'apiKey' => $this->apiKey,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch sports from The Odds API');
        }

        $sports = $response->json();
        $syncedCount = 0;

        foreach ($sports as $sportData) {
            $sport = Sport::updateOrCreate(
                ['external_id' => $sportData['key']],
                [
                    'name' => $sportData['title'],
                    'slug' => Str::slug($sportData['title']),
                    'category' => $sportData['group'] ?? 'Other',
                    'has_live_betting' => $sportData['has_outrights'] ?? false,
                    'is_active' => true,
                    'meta' => [
                        'provider' => 'the-odds-api',
                        'original_data' => $sportData,
                    ],
                ]
            );

            $syncedCount++;
        }

        Log::info("Synced {$syncedCount} sports from The Odds API");
        return $syncedCount;
    }

    /**
     * Sync sports from API Football
     */
    protected function syncSportsFromApiFootball()
    {
        $response = Http::withHeaders([
            'x-rapidapi-host' => 'v3.football.api-sports.io',
            'x-rapidapi-key' => $this->apiKey,
        ])->get($this->baseUrl . '/leagues');

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch sports from API Football');
        }

        $data = $response->json();
        $leagues = $data['response'] ?? [];
        $syncedCount = 0;

        foreach ($leagues as $leagueData) {
            $league = $leagueData['league'];
            $country = $leagueData['country'];

            $sport = Sport::updateOrCreate(
                ['external_id' => $league['id']],
                [
                    'name' => $league['name'],
                    'slug' => Str::slug($league['name']),
                    'category' => 'Football',
                    'has_live_betting' => true,
                    'is_active' => true,
                    'meta' => [
                        'provider' => 'api-football',
                        'country' => $country['name'],
                        'original_data' => $leagueData,
                    ],
                ]
            );

            $syncedCount++;
        }

        Log::info("Synced {$syncedCount} leagues from API Football");
        return $syncedCount;
    }

    /**
     * Sync matches for a specific sport
     */
    public function syncMatches($sportId)
    {
        $sport = Sport::find($sportId);
        if (!$sport) {
            throw new \Exception('Sport not found');
        }

        try {
            if ($this->apiProvider === 'the-odds-api') {
                return $this->syncMatchesFromOddsApi($sport);
            } elseif ($this->apiProvider === 'api-football') {
                return $this->syncMatchesFromApiFootball($sport);
            }
        } catch (\Exception $e) {
            Log::error("Failed to sync matches for sport {$sport->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Sync matches from The Odds API
     */
    protected function syncMatchesFromOddsApi($sport)
    {
        $response = Http::get($this->baseUrl . '/sports/' . $sport->external_id . '/odds', [
            'apiKey' => $this->apiKey,
            'regions' => 'us,uk',
            'markets' => 'h2h',
            'dateFormat' => 'iso',
            'oddsFormat' => 'decimal',
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch matches from The Odds API');
        }

        $matches = $response->json();
        $syncedCount = 0;

        foreach ($matches as $matchData) {
            // Extract odds from the first bookmaker
            $homeOdds = null;
            $awayOdds = null;
            $drawOdds = null;

            if (isset($matchData['bookmakers']) && count($matchData['bookmakers']) > 0) {
                $bookmaker = $matchData['bookmakers'][0];
                if (isset($bookmaker['markets']) && count($bookmaker['markets']) > 0) {
                    $market = $bookmaker['markets'][0];
                    foreach ($market['outcomes'] as $outcome) {
                        if ($outcome['name'] === $matchData['home_team']) {
                            $homeOdds = $outcome['price'];
                        } elseif ($outcome['name'] === $matchData['away_team']) {
                            $awayOdds = $outcome['price'];
                        } elseif ($outcome['name'] === 'Draw') {
                            $drawOdds = $outcome['price'];
                        }
                    }
                }
            }

            $match = Match::updateOrCreate(
                ['external_id' => $matchData['id']],
                [
                    'sport_id' => $sport->id,
                    'home_team' => $matchData['home_team'],
                    'away_team' => $matchData['away_team'],
                    'commence_time' => $matchData['commence_time'],
                    'status' => now()->gt($matchData['commence_time']) ? 'live' : 'upcoming',
                    'home_odds' => $homeOdds,
                    'away_odds' => $awayOdds,
                    'draw_odds' => $drawOdds,
                    'meta' => [
                        'provider' => 'the-odds-api',
                        'original_data' => $matchData,
                    ],
                ]
            );

            $syncedCount++;
        }

        Log::info("Synced {$syncedCount} matches for sport {$sport->name}");
        return $syncedCount;
    }

    /**
     * Sync matches from API Football
     */
    protected function syncMatchesFromApiFootball($sport)
    {
        $response = Http::withHeaders([
            'x-rapidapi-host' => 'v3.football.api-sports.io',
            'x-rapidapi-key' => $this->apiKey,
        ])->get($this->baseUrl . '/fixtures', [
            'league' => $sport->external_id,
            'season' => date('Y'),
            'next' => 50,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch matches from API Football');
        }

        $data = $response->json();
        $fixtures = $data['response'] ?? [];
        $syncedCount = 0;

        foreach ($fixtures as $fixtureData) {
            $fixture = $fixtureData['fixture'];
            $teams = $fixtureData['teams'];

            $status = 'upcoming';
            if ($fixture['status']['short'] === 'LIVE') {
                $status = 'live';
            } elseif (in_array($fixture['status']['short'], ['FT', 'AET', 'PEN'])) {
                $status = 'completed';
            } elseif (in_array($fixture['status']['short'], ['CANC', 'PST'])) {
                $status = 'cancelled';
            }

            $match = Match::updateOrCreate(
                ['external_id' => $fixture['id']],
                [
                    'sport_id' => $sport->id,
                    'home_team' => $teams['home']['name'],
                    'away_team' => $teams['away']['name'],
                    'commence_time' => $fixture['date'],
                    'status' => $status,
                    'scores' => $fixtureData['goals'] ?? null,
                    'meta' => [
                        'provider' => 'api-football',
                        'venue' => $fixture['venue']['name'] ?? null,
                        'referee' => $fixture['referee'] ?? null,
                        'original_data' => $fixtureData,
                    ],
                ]
            );

            $syncedCount++;
        }

        Log::info("Synced {$syncedCount} matches for sport {$sport->name}");
        return $syncedCount;
    }

    /**
     * Update live scores for active matches
     */
    public function updateLiveScores()
    {
        $liveMatches = Match::where('status', 'live')->get();

        foreach ($liveMatches as $match) {
            try {
                $this->updateMatchScores($match);
            } catch (\Exception $e) {
                Log::error("Failed to update scores for match {$match->id}: " . $e->getMessage());
            }
        }
    }

    /**
     * Update scores for a specific match
     */
    protected function updateMatchScores($match)
    {
        if ($this->apiProvider === 'the-odds-api') {
            $response = Http::get($this->baseUrl . '/sports/' . $match->sport->external_id . '/scores', [
                'apiKey' => $this->apiKey,
                'daysFrom' => 1,
            ]);

            if ($response->successful()) {
                $scores = $response->json();
                foreach ($scores as $scoreData) {
                    if ($scoreData['id'] === $match->external_id) {
                        $match->update([
                            'scores' => $scoreData['scores'] ?? null,
                            'status' => $scoreData['completed'] ? 'completed' : 'live',
                        ]);
                        break;
                    }
                }
            }
        }
    }
}