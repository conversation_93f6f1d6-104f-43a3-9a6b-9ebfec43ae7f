<?php

namespace App\Services;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ProvenFairService
{
    /**
     * Generate a random server seed.
     *
     * @param int $length
     * @return string
     */
    public function generateServerSeed($length = 64)
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Generate a server seed hash.
     *
     * @param string $serverSeed
     * @return string
     */
    public function generateServerSeedHash($serverSeed)
    {
        return hash('sha256', $serverSeed);
    }

    /**
     * Generate a client seed.
     *
     * @param int $length
     * @return string
     */
    public function generateClientSeed($length = 16)
    {
        return Str::random($length);
    }

    /**
     * Verify that a result was generated fairly.
     *
     * @param string $serverSeed
     * @param string $clientSeed
     * @param int $nonce
     * @param array $result
     * @param string $gameType
     * @return bool
     */
    public function verifyResult($serverSeed, $clientSeed, $nonce, $result, $gameType)
    {
        $calculatedResult = $this->calculateResult($serverSeed, $clientSeed, $nonce, $gameType);
        
        // Game-specific result verification
        switch ($gameType) {
            case 'dice':
                return $calculatedResult['roll'] === $result['roll'];
            case 'mines':
                return $this->arraysAreEqual($calculatedResult['mines'], $result['mines']);
            case 'crash':
                return $calculatedResult['crash_point'] === $result['crash_point'];
            case 'blackjack':
            case 'baccarat':
                return $this->verifyCardGame($calculatedResult, $result);
            case 'roulette':
                return $calculatedResult['number'] === $result['number'];
            case 'hilo':
                return $calculatedResult['card'] === $result['card'];
            default:
                return false;
        }
    }

    /**
     * Calculate a game result based on seeds and nonce.
     *
     * @param string $serverSeed
     * @param string $clientSeed
     * @param int $nonce
     * @param string $gameType
     * @return array
     */
    public function calculateResult($serverSeed, $clientSeed, $nonce, $gameType)
    {
        // Generate a combined seed
        $combinedSeed = $serverSeed . $clientSeed . $nonce;
        $hash = hash('sha512', $combinedSeed);
        
        // Game-specific result generation
        switch ($gameType) {
            case 'dice':
                return $this->calculateDiceResult($hash);
            case 'mines':
                return $this->calculateMinesResult($hash);
            case 'crash':
                return $this->calculateCrashResult($hash);
            case 'blackjack':
            case 'baccarat':
                return $this->calculateCardGameResult($hash, $gameType);
            case 'roulette':
                return $this->calculateRouletteResult($hash);
            case 'hilo':
                return $this->calculateHiloResult($hash);
            default:
                throw new \InvalidArgumentException("Unsupported game type: {$gameType}");
        }
    }

    /**
     * Calculate result for Dice game.
     *
     * @param string $hash
     * @return array
     */
    private function calculateDiceResult($hash)
    {
        // Use first 8 characters of hash to generate a number between 0 and 10000
        $hexString = substr($hash, 0, 8);
        $decimalValue = hexdec($hexString);
        $roll = $decimalValue % 10001 / 100; // 0.00 to 100.00
        
        return [
            'roll' => $roll,
        ];
    }

    /**
     * Calculate result for Mines game.
     *
     * @param string $hash
     * @return array
     */
    private function calculateMinesResult($hash)
    {
        $totalCells = 25; // 5x5 grid
        $minesCount = 5; // Standard mines count
        
        // Use hash to create a shuffle order
        $order = [];
        for ($i = 0; $i < $totalCells; $i++) {
            $order[$i] = $i;
        }
        
        // Fisher-Yates shuffle based on hash
        for ($i = $totalCells - 1; $i > 0; $i--) {
            $j = hexdec(substr($hash, ($totalCells - $i) * 2 % strlen($hash), 2)) % ($i + 1);
            [$order[$i], $order[$j]] = [$order[$j], $order[$i]];
        }
        
        // First N elements in shuffled array are mine positions
        $mines = array_slice($order, 0, $minesCount);
        sort($mines);
        
        return [
            'mines' => $mines,
            'total_cells' => $totalCells,
            'mines_count' => $minesCount,
        ];
    }

    /**
     * Calculate result for Crash game.
     *
     * @param string $hash
     * @return array
     */
    private function calculateCrashResult($hash)
    {
        // Algorithm based on Bustabit's provably fair system
        $hex = substr($hash, 0, 8);
        $int = hexdec($hex);
        $float = $int / 0xFFFFFFFF;
        
        // Calculate crash point with house edge of 1%
        if ($float >= 0.01) {
            $crashPoint = 99 / ($float * 100);
        } else {
            $crashPoint = 100; // Max multiplier
        }
        
        return [
            'crash_point' => round($crashPoint, 2),
        ];
    }

    /**
     * Calculate result for card games like Blackjack or Baccarat.
     *
     * @param string $hash
     * @param string $gameType
     * @return array
     */
    private function calculateCardGameResult($hash, $gameType)
    {
        // Generate a deck of cards
        $suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        $values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        
        $deck = [];
        foreach ($suits as $suit) {
            foreach ($values as $value) {
                $deck[] = [
                    'suit' => $suit,
                    'value' => $value,
                ];
            }
        }
        
        // Shuffle the deck based on the hash
        $shuffledDeck = $this->shuffleDeckWithHash($deck, $hash);
        
        // Deal cards
        if ($gameType === 'blackjack') {
            $playerHand = [$shuffledDeck[0], $shuffledDeck[2]];
            $dealerHand = [$shuffledDeck[1], $shuffledDeck[3]];
            
            return [
                'player_hand' => $playerHand,
                'dealer_hand' => $dealerHand,
                'deck' => array_slice($shuffledDeck, 4),
            ];
        } else { // Baccarat
            $playerHand = [$shuffledDeck[0], $shuffledDeck[2]];
            $bankerHand = [$shuffledDeck[1], $shuffledDeck[3]];
            
            return [
                'player_hand' => $playerHand,
                'banker_hand' => $bankerHand,
                'deck' => array_slice($shuffledDeck, 4),
            ];
        }
    }

    /**
     * Calculate result for Roulette game.
     *
     * @param string $hash
     * @return array
     */
    private function calculateRouletteResult($hash)
    {
        // European roulette has numbers 0-36
        $hexString = substr($hash, 0, 8);
        $decimalValue = hexdec($hexString);
        $number = $decimalValue % 37; // 0-36
        
        $color = $number === 0 ? 'green' : (in_array($number, [1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36]) ? 'red' : 'black');
        
        return [
            'number' => $number,
            'color' => $color,
            'is_even' => $number !== 0 && $number % 2 === 0,
            'is_low' => $number !== 0 && $number <= 18,
        ];
    }

    /**
     * Calculate result for Hilo game.
     *
     * @param string $hash
     * @return array
     */
    private function calculateHiloResult($hash)
    {
        // Generate a deck of cards
        $suits = ['hearts', 'diamonds', 'clubs', 'spades'];
        $values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        
        $deck = [];
        foreach ($suits as $suit) {
            foreach ($values as $value) {
                $deck[] = [
                    'suit' => $suit,
                    'value' => $value,
                ];
            }
        }
        
        // Shuffle the deck based on the hash
        $shuffledDeck = $this->shuffleDeckWithHash($deck, $hash);
        
        // Draw the first card
        $card = $shuffledDeck[0];
        
        return [
            'card' => $card,
            'deck' => array_slice($shuffledDeck, 1),
        ];
    }

    /**
     * Shuffle a deck of cards using a hash.
     *
     * @param array $deck
     * @param string $hash
     * @return array
     */
    private function shuffleDeckWithHash($deck, $hash)
    {
        $shuffled = $deck;
        $deckSize = count($deck);
        
        // Fisher-Yates shuffle based on hash
        for ($i = $deckSize - 1; $i > 0; $i--) {
            $j = hexdec(substr($hash, ($deckSize - $i) * 2 % strlen($hash), 2)) % ($i + 1);
            [$shuffled[$i], $shuffled[$j]] = [$shuffled[$j], $shuffled[$i]];
        }
        
        return $shuffled;
    }

    /**
     * Verify card game results match.
     *
     * @param array $calculated
     * @param array $result
     * @return bool
     */
    private function verifyCardGame($calculated, $result)
    {
        // Check if player hands match
        if (!$this->cardsAreEqual($calculated['player_hand'], $result['player_hand'])) {
            return false;
        }
        
        // Check if dealer/banker hands match
        $dealerKey = isset($calculated['dealer_hand']) ? 'dealer_hand' : 'banker_hand';
        $resultDealerKey = isset($result['dealer_hand']) ? 'dealer_hand' : 'banker_hand';
        
        if (!$this->cardsAreEqual($calculated[$dealerKey], $result[$resultDealerKey])) {
            return false;
        }
        
        return true;
    }

    /**
     * Check if two card arrays are equal.
     *
     * @param array $cards1
     * @param array $cards2
     * @return bool
     */
    private function cardsAreEqual($cards1, $cards2)
    {
        if (count($cards1) !== count($cards2)) {
            return false;
        }
        
        for ($i = 0; $i < count($cards1); $i++) {
            if ($cards1[$i]['suit'] !== $cards2[$i]['suit'] || $cards1[$i]['value'] !== $cards2[$i]['value']) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Check if two arrays are equal.
     *
     * @param array $array1
     * @param array $array2
     * @return bool
     */
    private function arraysAreEqual($array1, $array2)
    {
        if (count($array1) !== count($array2)) {
            return false;
        }
        
        sort($array1);
        sort($array2);
        
        return $array1 === $array2;
    }

    /**
     * Log a provably fair result for auditing.
     *
     * @param string $serverSeed
     * @param string $serverSeedHash
     * @param string $clientSeed
     * @param int $nonce
     * @param array $result
     * @param string $gameType
     * @param int $userId
     * @return void
     */
    public function logProvenFairResult($serverSeed, $serverSeedHash, $clientSeed, $nonce, $result, $gameType, $userId)
    {
        try {
            // In a production environment, this would write to a secure audit log
            Log::channel('provably_fair')->info('Provably Fair Result', [
                'user_id' => $userId,
                'game_type' => $gameType,
                'server_seed' => $serverSeed,
                'server_seed_hash' => $serverSeedHash,
                'client_seed' => $clientSeed,
                'nonce' => $nonce,
                'result' => json_encode($result),
                'timestamp' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log provably fair result: ' . $e->getMessage());
        }
    }

    /**
     * Generate a verification URL for a game result.
     *
     * @param string $serverSeed
     * @param string $clientSeed
     * @param int $nonce
     * @param string $gameType
     * @return string
     */
    public function generateVerificationUrl($serverSeed, $clientSeed, $nonce, $gameType)
    {
        $params = http_build_query([
            'server_seed' => $serverSeed,
            'client_seed' => $clientSeed,
            'nonce' => $nonce,
            'game' => $gameType,
        ]);
        
        return config('app.url') . '/fairness/verify?' . $params;
    }
}