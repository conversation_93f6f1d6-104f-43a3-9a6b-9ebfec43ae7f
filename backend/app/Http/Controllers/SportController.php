<?php

namespace App\Http\Controllers;

use App\Models\Sport;
use App\Models\Match;
use Illuminate\Http\Request;

class SportController extends Controller
{
    /**
     * Get all active sports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $sports = Sport::where('is_active', true)
            ->withCount(['activeMatches', 'liveMatches'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'status' => 'success',
            'sports' => $sports,
        ]);
    }

    /**
     * Get a specific sport with its matches.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $slug
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $slug)
    {
        $sport = Sport::where('slug', $slug)
            ->where('is_active', true)
            ->with(['activeMatches' => function ($query) {
                $query->orderBy('commence_time', 'asc');
            }])
            ->first();

        if (!$sport) {
            return response()->json([
                'status' => 'error',
                'message' => 'Sport not found',
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'sport' => $sport,
        ]);
    }

    /**
     * Get matches for a specific sport.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $slug
     * @return \Illuminate\Http\Response
     */
    public function matches(Request $request, $slug)
    {
        $request->validate([
            'status' => 'sometimes|string|in:upcoming,live,completed,all',
            'limit' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
        ]);

        $sport = Sport::where('slug', $slug)
            ->where('is_active', true)
            ->first();

        if (!$sport) {
            return response()->json([
                'status' => 'error',
                'message' => 'Sport not found',
            ], 404);
        }

        $query = $sport->matches();

        // Filter by status
        $status = $request->status ?? 'upcoming';
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Order matches
        if ($status === 'upcoming') {
            $query->orderBy('commence_time', 'asc');
        } elseif ($status === 'live') {
            $query->orderBy('commence_time', 'desc');
        } else {
            $query->orderBy('commence_time', 'desc');
        }

        $limit = $request->limit ?? 20;
        $matches = $query->paginate($limit);

        return response()->json([
            'status' => 'success',
            'sport' => $sport->only(['id', 'name', 'slug']),
            'matches' => $matches,
        ]);
    }

    /**
     * Get live matches across all sports.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function liveMatches(Request $request)
    {
        $liveMatches = Match::where('status', Match::STATUS_LIVE)
            ->with('sport')
            ->orderBy('commence_time', 'desc')
            ->get();

        return response()->json([
            'status' => 'success',
            'live_matches' => $liveMatches,
        ]);
    }

    /**
     * Get featured/popular matches.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function featured(Request $request)
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:50',
        ]);

        $limit = $request->limit ?? 10;

        // Get featured matches (upcoming matches with high betting volume or popular sports)
        $featuredMatches = Match::whereIn('status', [Match::STATUS_UPCOMING, Match::STATUS_LIVE])
            ->with('sport')
            ->withCount('bets')
            ->orderBy('bets_count', 'desc')
            ->orderBy('commence_time', 'asc')
            ->take($limit)
            ->get();

        return response()->json([
            'status' => 'success',
            'featured_matches' => $featuredMatches,
        ]);
    }
}