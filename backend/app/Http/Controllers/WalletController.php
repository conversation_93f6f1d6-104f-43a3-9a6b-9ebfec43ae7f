<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class WalletController extends Controller
{
    /**
     * Get user wallet information.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        return response()->json([
            'balance' => $user->balance,
            'currency' => 'GA', // GoldenAura currency
            'recent_transactions' => $user->transactions()
                ->latest()
                ->take(5)
                ->get(),
        ]);
    }

    /**
     * Process a deposit.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deposit(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:10|max:100000',
            'payment_method' => 'required|string',
        ]);

        $user = $request->user();
        $amount = $request->amount;

        // In a real application, we would process payment through a payment gateway here
        // For demo purposes, we'll just add the amount to the user's balance

        $balanceBefore = $user->balance;
        
        if ($user->updateBalance($amount, 'credit')) {
            // Create transaction record
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_DEPOSIT,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->balance,
                'status' => Transaction::STATUS_COMPLETED,
                'reference' => 'DEP-' . Str::random(10),
                'meta' => [
                    'payment_method' => $request->payment_method,
                    'description' => 'Deposit via ' . $request->payment_method,
                ],
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Deposit processed successfully',
                'transaction' => $transaction,
                'new_balance' => $user->balance,
            ]);
        }

        throw ValidationException::withMessages([
            'amount' => ['Failed to process deposit. Please try again.'],
        ]);
    }

    /**
     * Process a withdrawal.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function withdraw(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:10|max:100000',
            'withdrawal_method' => 'required|string',
            'withdrawal_address' => 'required|string',
        ]);

        $user = $request->user();
        $amount = $request->amount;

        if (!$user->hasSufficientBalance($amount)) {
            throw ValidationException::withMessages([
                'amount' => ['Insufficient balance for withdrawal.'],
            ]);
        }

        $balanceBefore = $user->balance;
        
        // In a real application, we would process withdrawal through a payment gateway
        // For demo purposes, we'll just subtract the amount from the user's balance
        
        if ($user->updateBalance($amount, 'debit')) {
            // Create transaction record
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_WITHDRAW,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->balance,
                'status' => Transaction::STATUS_PENDING, // Withdrawals typically start as pending
                'reference' => 'WITH-' . Str::random(10),
                'meta' => [
                    'withdrawal_method' => $request->withdrawal_method,
                    'withdrawal_address' => $request->withdrawal_address,
                    'description' => 'Withdrawal via ' . $request->withdrawal_method,
                ],
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Withdrawal request submitted successfully',
                'transaction' => $transaction,
                'new_balance' => $user->balance,
            ]);
        }

        throw ValidationException::withMessages([
            'amount' => ['Failed to process withdrawal. Please try again.'],
        ]);
    }

    /**
     * Get user transaction history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function transactions(Request $request)
    {
        $request->validate([
            'type' => 'sometimes|string|in:deposit,withdraw,bet,win,bonus,all',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:5|max:100',
        ]);

        $perPage = $request->per_page ?? 15;
        $type = $request->type ?? 'all';
        
        $query = $request->user()->transactions()->latest();
        
        if ($type !== 'all') {
            $query->where('type', $type);
        }
        
        $transactions = $query->paginate($perPage);
        
        return response()->json($transactions);
    }
}