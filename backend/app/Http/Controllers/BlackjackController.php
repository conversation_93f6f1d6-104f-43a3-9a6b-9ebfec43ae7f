<?php

namespace App\Http\Controllers;

use App\Models\Bet;
use App\Models\GameSession;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class BlackjackController extends Controller
{
    /**
     * Generate a random server seed
     *
     * @param int $length
     * @return string
     */
    protected function generateServerSeed($length = 32)
    {
        return Str::random($length);
    }

    /**
     * Hash a string
     *
     * @param string $string
     * @return string
     */
    protected function hashString($string)
    {
        return hash('sha256', $string);
    }

    /**
     * Shuffle deck using provably fair algorithm
     *
     * @param array $deck
     * @param string $serverSeed
     * @param string $clientSeed
     * @param int $nonce
     * @return array
     */
    protected function shuffleDeck($deck, $serverSeed, $clientSeed, $nonce)
    {
        // Create combined seed
        $combinedSeed = $serverSeed . $clientSeed . $nonce;
        
        // Use hash to seed the PHP random number generator
        mt_srand(crc32($combinedSeed));
        
        // Fisher-Yates shuffle
        $shuffled = $deck;
        $count = count($shuffled);
        
        for ($i = $count - 1; $i > 0; $i--) {
            $j = mt_rand(0, $i);
            [$shuffled[$i], $shuffled[$j]] = [$shuffled[$j], $shuffled[$i]];
        }
        
        return $shuffled;
    }

    /**
     * Generate a standard deck of cards
     *
     * @param int $decks Number of decks to generate
     * @return array
     */
    protected function generateDeck($decks = 1)
    {
        $suits = ['♠', '♥', '♦', '♣'];
        $values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        $cardValues = [
            'A' => 11, '2' => 2, '3' => 3, '4' => 4, '5' => 5, '6' => 6, 
            '7' => 7, '8' => 8, '9' => 9, '10' => 10, 'J' => 10, 'Q' => 10, 'K' => 10
        ];
        
        $deck = [];
        
        for ($d = 0; $d < $decks; $d++) {
            foreach ($suits as $suit) {
                foreach ($values as $value) {
                    $deck[] = [
                        'suit' => $suit,
                        'value' => $value,
                        'numericValue' => $cardValues[$value],
                        'isRed' => $suit === '♥' || $suit === '♦'
                    ];
                }
            }
        }
        
        return $deck;
    }

    /**
     * Calculate hand value, accounting for aces
     *
     * @param array $hand
     * @return int
     */
    protected function calculateHandValue($hand)
    {
        $total = 0;
        $aces = 0;
        
        foreach ($hand as $card) {
            if ($card['value'] === 'A') {
                $aces++;
                $total += 11;
            } else {
                $total += $card['numericValue'];
            }
        }
        
        // Adjust for aces
        while ($total > 21 && $aces > 0) {
            $total -= 10;
            $aces--;
        }
        
        return $total;
    }

    /**
     * Check if hand is blackjack
     *
     * @param array $hand
     * @return bool
     */
    protected function isBlackjack($hand)
    {
        return count($hand) === 2 && $this->calculateHandValue($hand) === 21;
    }

    /**
     * Place a bet to start a blackjack game
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function placeBet(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1|max:100000',
            'client_seed' => 'sometimes|string',
        ]);

        $user = $request->user();
        $amount = $request->amount;

        // Check if user has sufficient balance
        if (!$user->hasSufficientBalance($amount)) {
            throw ValidationException::withMessages([
                'amount' => ['Insufficient balance'],
            ]);
        }

        // Generate server seed and hash
        $serverSeed = $this->generateServerSeed();
        $serverSeedHash = $this->hashString($serverSeed);
        
        // Use provided client seed or generate one
        $clientSeed = $request->client_seed ?? Str::random(16);
        
        // Create game session
        $gameSession = GameSession::create([
            'user_id' => $user->id,
            'game_type' => GameSession::GAME_BLACKJACK,
            'server_seed' => $serverSeed,
            'server_seed_hash' => $serverSeedHash,
            'client_seed' => $clientSeed,
            'nonce' => 0,
            'status' => GameSession::STATUS_ACTIVE,
            'meta' => [
                'difficulty' => $request->difficulty ?? 'novice',
                'totalDecks' => $request->difficulty === 'expert' ? 8 : ($request->difficulty === 'pro' ? 6 : 1),
            ],
        ]);

        // Generate and shuffle deck
        $deck = $this->generateDeck($gameSession->meta['totalDecks']);
        $shuffledDeck = $this->shuffleDeck($deck, $serverSeed, $clientSeed, 0);
        
        // Deal initial cards
        $playerHand = [
            array_pop($shuffledDeck),
            array_pop($shuffledDeck),
        ];
        
        $dealerHand = [
            array_pop($shuffledDeck),
            array_pop($shuffledDeck),
        ];
        
        // Update game session with initial state
        $gameSession->result = [
            'deck' => $shuffledDeck,
            'playerHand' => $playerHand,
            'dealerHand' => $dealerHand,
            'state' => 'playing',
            'nonce' => 0,
        ];
        $gameSession->save();

        // Deduct bet amount from user balance
        $balanceBefore = $user->balance;
        $user->updateBalance($amount, 'debit');
        
        // Create transaction record
        $transaction = Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_BET,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $user->balance,
            'status' => Transaction::STATUS_COMPLETED,
            'reference' => 'BJ-BET-' . Str::random(8),
            'meta' => [
                'game_session_id' => $gameSession->id,
                'game_type' => GameSession::GAME_BLACKJACK,
            ],
        ]);
        
        // Create bet record
        $bet = Bet::create([
            'user_id' => $user->id,
            'game_session_id' => $gameSession->id,
            'amount' => $amount,
            'type' => Bet::TYPE_SINGLE,
            'odds' => 2.0, // Standard blackjack payout is 1:1
            'potential_payout' => $amount * 2.0,
            'status' => Bet::STATUS_PENDING,
            'meta' => [
                'game_type' => GameSession::GAME_BLACKJACK,
            ],
        ]);

        // Check for instant blackjack
        $playerBlackjack = $this->isBlackjack($playerHand);
        $dealerBlackjack = $this->isBlackjack($dealerHand);
        
        if ($playerBlackjack || $dealerBlackjack) {
            // Update game state
            $gameSession->result['state'] = 'completed';
            
            // Determine outcome
            if ($playerBlackjack && $dealerBlackjack) {
                // Push - return bet amount
                $outcome = 'push';
                $payout = $amount;
                $bet->status = Bet::STATUS_PUSH;
                $bet->result = 'push';
                $bet->save();
                
                // Return bet amount
                $user->updateBalance($payout, 'credit');
                
                // Create transaction for returned bet
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => Transaction::TYPE_WIN,
                    'amount' => $payout,
                    'balance_before' => $user->balance - $payout,
                    'balance_after' => $user->balance,
                    'status' => Transaction::STATUS_COMPLETED,
                    'reference' => 'BJ-PUSH-' . Str::random(8),
                    'meta' => [
                        'game_session_id' => $gameSession->id,
                        'game_type' => GameSession::GAME_BLACKJACK,
                    ],
                ]);
                
            } elseif ($playerBlackjack) {
                // Player wins with blackjack (3:2 payout)
                $outcome = 'win';
                $payout = $amount * 2.5; // Blackjack pays 3:2
                $bet->status = Bet::STATUS_WON;
                $bet->result = 'blackjack';
                $bet->potential_payout = $payout;
                $bet->save();
                
                // Add winnings to balance
                $user->updateBalance($payout, 'credit');
                
                // Create transaction for winnings
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => Transaction::TYPE_WIN,
                    'amount' => $payout,
                    'balance_before' => $user->balance - $payout,
                    'balance_after' => $user->balance,
                    'status' => Transaction::STATUS_COMPLETED,
                    'reference' => 'BJ-WIN-' . Str::random(8),
                    'meta' => [
                        'game_session_id' => $gameSession->id,
                        'game_type' => GameSession::GAME_BLACKJACK,
                        'result' => 'blackjack',
                    ],
                ]);
                
            } else {
                // Dealer wins with blackjack
                $outcome = 'lose';
                $bet->status = Bet::STATUS_LOST;
                $bet->result = 'dealer_blackjack';
                $bet->save();
            }
            
            // Update game session status
            $gameSession->status = GameSession::STATUS_COMPLETED;
            $gameSession->result['outcome'] = $outcome;
            $gameSession->save();
            
            return response()->json([
                'status' => 'success',
                'game_session' => $gameSession,
                'player_hand' => $playerHand,
                'dealer_hand' => $dealerHand,
                'player_value' => $this->calculateHandValue($playerHand),
                'dealer_value' => $this->calculateHandValue($dealerHand),
                'outcome' => $outcome,
                'payout' => $outcome === 'lose' ? 0 : $payout,
                'balance' => $user->balance,
                'is_final' => true,
            ]);
        }

        return response()->json([
            'status' => 'success',
            'game_session_id' => $gameSession->id,
            'player_hand' => $playerHand,
            'dealer_hand' => [$dealerHand[0], ['hidden' => true]], // Hide dealer's second card
            'player_value' => $this->calculateHandValue($playerHand),
            'dealer_value' => $dealerHand[0]['numericValue'],
            'balance' => $user->balance,
            'is_final' => false,
            'server_seed_hash' => $serverSeedHash,
            'client_seed' => $clientSeed,
            'nonce' => 0,
        ]);
    }

    /**
     * Hit - take another card
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function hit(Request $request)
    {
        $request->validate([
            'game_session_id' => 'required|exists:game_sessions,id',
        ]);

        $user = $request->user();
        $gameSessionId = $request->game_session_id;
        
        // Get game session
        $gameSession = GameSession::where('id', $gameSessionId)
            ->where('user_id', $user->id)
            ->where('status', GameSession::STATUS_ACTIVE)
            ->first();
            
        if (!$gameSession) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or completed game session',
            ], 400);
        }
        
        // Get current game state
        $result = $gameSession->result;
        
        if ($result['state'] !== 'playing') {
            return response()->json([
                'status' => 'error',
                'message' => 'This game is already completed',
            ], 400);
        }
        
        // Get current deck and hands
        $deck = $result['deck'];
        $playerHand = $result['playerHand'];
        $dealerHand = $result['dealerHand'];
        $nonce = $result['nonce'] + 1;
        
        // Deal a new card to player
        $newCard = array_pop($deck);
        $playerHand[] = $newCard;
        
        // Calculate new hand value
        $playerValue = $this->calculateHandValue($playerHand);
        
        // Update game state
        $gameSession->result = [
            'deck' => $deck,
            'playerHand' => $playerHand,
            'dealerHand' => $dealerHand,
            'state' => $playerValue > 21 ? 'busted' : 'playing',
            'nonce' => $nonce,
        ];
        
        // Check if player busted
        if ($playerValue > 21) {
            // Player busted, game over
            $gameSession->status = GameSession::STATUS_COMPLETED;
            $gameSession->result['outcome'] = 'lose';
            $gameSession->result['state'] = 'completed';
            
            // Update bet status
            $bet = Bet::where('game_session_id', $gameSession->id)
                ->where('status', Bet::STATUS_PENDING)
                ->first();
                
            if ($bet) {
                $bet->status = Bet::STATUS_LOST;
                $bet->result = 'player_bust';
                $bet->save();
            }
        }
        
        $gameSession->save();
        
        return response()->json([
            'status' => 'success',
            'game_session_id' => $gameSession->id,
            'player_hand' => $playerHand,
            'dealer_hand' => $gameSession->status === GameSession::STATUS_COMPLETED ? $dealerHand : [$dealerHand[0], ['hidden' => true]],
            'player_value' => $playerValue,
            'dealer_value' => $gameSession->status === GameSession::STATUS_COMPLETED ? $this->calculateHandValue($dealerHand) : $dealerHand[0]['numericValue'],
            'balance' => $user->balance,
            'is_final' => $playerValue > 21,
            'nonce' => $nonce,
        ]);
    }

    /**
     * Stand - end player's turn
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function stand(Request $request)
    {
        $request->validate([
            'game_session_id' => 'required|exists:game_sessions,id',
        ]);

        $user = $request->user();
        $gameSessionId = $request->game_session_id;
        
        // Get game session
        $gameSession = GameSession::where('id', $gameSessionId)
            ->where('user_id', $user->id)
            ->where('status', GameSession::STATUS_ACTIVE)
            ->first();
            
        if (!$gameSession) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or completed game session',
            ], 400);
        }
        
        // Get current game state
        $result = $gameSession->result;
        
        if ($result['state'] !== 'playing') {
            return response()->json([
                'status' => 'error',
                'message' => 'This game is already completed',
            ], 400);
        }
        
        // Get current deck and hands
        $deck = $result['deck'];
        $playerHand = $result['playerHand'];
        $dealerHand = $result['dealerHand'];
        $nonce = $result['nonce'];
        
        // Calculate hand values
        $playerValue = $this->calculateHandValue($playerHand);
        $dealerValue = $this->calculateHandValue($dealerHand);
        
        // Dealer plays - dealer must hit on 16 or less, stand on 17 or more
        while ($dealerValue < 17) {
            $nonce++;
            $newCard = array_pop($deck);
            $dealerHand[] = $newCard;
            $dealerValue = $this->calculateHandValue($dealerHand);
        }
        
        // Determine outcome
        $outcome = 'lose'; // Default
        $payout = 0;
        
        if ($dealerValue > 21) {
            // Dealer busted, player wins
            $outcome = 'win';
            $betResult = 'dealer_bust';
        } elseif ($playerValue > $dealerValue) {
            // Player wins
            $outcome = 'win';
            $betResult = 'player_higher';
        } elseif ($playerValue < $dealerValue) {
            // Dealer wins
            $outcome = 'lose';
            $betResult = 'dealer_higher';
        } else {
            // Push (tie)
            $outcome = 'push';
            $betResult = 'push';
        }
        
        // Update game session
        $gameSession->status = GameSession::STATUS_COMPLETED;
        $gameSession->result = [
            'deck' => $deck,
            'playerHand' => $playerHand,
            'dealerHand' => $dealerHand,
            'state' => 'completed',
            'outcome' => $outcome,
            'nonce' => $nonce,
        ];
        $gameSession->save();
        
        // Get bet and update status
        $bet = Bet::where('game_session_id', $gameSession->id)
            ->where('status', Bet::STATUS_PENDING)
            ->first();
            
        if ($bet) {
            if ($outcome === 'win') {
                $bet->status = Bet::STATUS_WON;
                $payout = $bet->potential_payout;
                
                // Add winnings to balance
                $user->updateBalance($payout, 'credit');
                
                // Create transaction for winnings
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => Transaction::TYPE_WIN,
                    'amount' => $payout,
                    'balance_before' => $user->balance - $payout,
                    'balance_after' => $user->balance,
                    'status' => Transaction::STATUS_COMPLETED,
                    'reference' => 'BJ-WIN-' . Str::random(8),
                    'meta' => [
                        'game_session_id' => $gameSession->id,
                        'game_type' => GameSession::GAME_BLACKJACK,
                        'result' => $betResult,
                    ],
                ]);
                
            } elseif ($outcome === 'push') {
                $bet->status = Bet::STATUS_PUSH;
                $payout = $bet->amount;
                
                // Return bet amount
                $user->updateBalance($payout, 'credit');
                
                // Create transaction for returned bet
                Transaction::create([
                    'user_id' => $user->id,
                    'type' => Transaction::TYPE_WIN,
                    'amount' => $payout,
                    'balance_before' => $user->balance - $payout,
                    'balance_after' => $user->balance,
                    'status' => Transaction::STATUS_COMPLETED,
                    'reference' => 'BJ-PUSH-' . Str::random(8),
                    'meta' => [
                        'game_session_id' => $gameSession->id,
                        'game_type' => GameSession::GAME_BLACKJACK,
                    ],
                ]);
                
            } else {
                $bet->status = Bet::STATUS_LOST;
            }
            
            $bet->result = $betResult;
            $bet->save();
        }
        
        return response()->json([
            'status' => 'success',
            'game_session_id' => $gameSession->id,
            'player_hand' => $playerHand,
            'dealer_hand' => $dealerHand,
            'player_value' => $playerValue,
            'dealer_value' => $dealerValue,
            'outcome' => $outcome,
            'payout' => $payout,
            'balance' => $user->balance,
            'is_final' => true,
            'nonce' => $nonce,
        ]);
    }

    /**
     * Double down - double bet and take one more card
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function double(Request $request)
    {
        $request->validate([
            'game_session_id' => 'required|exists:game_sessions,id',
        ]);

        $user = $request->user();
        $gameSessionId = $request->game_session_id;
        
        // Get game session
        $gameSession = GameSession::where('id', $gameSessionId)
            ->where('user_id', $user->id)
            ->where('status', GameSession::STATUS_ACTIVE)
            ->first();
            
        if (!$gameSession) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or completed game session',
            ], 400);
        }
        
        // Get current game state
        $result = $gameSession->result;
        
        if ($result['state'] !== 'playing') {
            return response()->json([
                'status' => 'error',
                'message' => 'This game is already completed',
            ], 400);
        }
        
        // Get current bet
        $bet = Bet::where('game_session_id', $gameSession->id)
            ->where('status', Bet::STATUS_PENDING)
            ->first();
            
        if (!$bet) {
            return response()->json([
                'status' => 'error',
                'message' => 'No active bet found for this game session',
            ], 400);
        }
        
        // Check if player can double down (only allowed on first two cards)
        if (count($result['playerHand']) !== 2) {
            return response()->json([
                'status' => 'error',
                'message' => 'Double down is only allowed on the first two cards',
            ], 400);
        }
        
        // Check if user has sufficient balance for doubling
        if (!$user->hasSufficientBalance($bet->amount)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Insufficient balance to double down',
            ], 400);
        }
        
        // Get current deck and hands
        $deck = $result['deck'];
        $playerHand = $result['playerHand'];
        $dealerHand = $result['dealerHand'];
        $nonce = $result['nonce'] + 1;
        
        // Double the bet amount
        $additionalBetAmount = $bet->amount;
        $bet->amount = $bet->amount * 2;
        $bet->potential_payout = $bet->amount * $bet->odds;
        $bet->save();
        
        // Deduct additional bet amount from user balance
        $balanceBefore = $user->balance;
        $user->updateBalance($additionalBetAmount, 'debit');
        
        // Create transaction record for additional bet
        Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_BET,
            'amount' => $additionalBetAmount,
            'balance_before' => $balanceBefore,
            'balance_after' => $user->balance,
            'status' => Transaction::STATUS_COMPLETED,
            'reference' => 'BJ-DOUBLE-' . Str::random(8),
            'meta' => [
                'game_session_id' => $gameSession->id,
                'game_type' => GameSession::GAME_BLACKJACK,
            ],
        ]);
        
        // Deal one more card to player
        $newCard = array_pop($deck);
        $playerHand[] = $newCard;
        
        // Calculate hand values
        $playerValue = $this->calculateHandValue($playerHand);
        
        // Check if player busted
        if ($playerValue > 21) {
            // Player busted, game over
            $gameSession->status = GameSession::STATUS_COMPLETED;
            $gameSession->result = [
                'deck' => $deck,
                'playerHand' => $playerHand,
                'dealerHand' => $dealerHand,
                'state' => 'completed',
                'outcome' => 'lose',
                'nonce' => $nonce,
            ];
            
            $bet->status = Bet::STATUS_LOST;
            $bet->result = 'player_bust';
            $bet->save();
            
            $gameSession->save();
            
            return response()->json([
                'status' => 'success',
                'game_session_id' => $gameSession->id,
                'player_hand' => $playerHand,
                'dealer_hand' => $dealerHand,
                'player_value' => $playerValue,
                'dealer_value' => $this->calculateHandValue($dealerHand),
                'outcome' => 'lose',
                'payout' => 0,
                'balance' => $user->balance,
                'is_final' => true,
                'nonce' => $nonce,
            ]);
        }
        
        // Player didn't bust, dealer plays
        $dealerValue = $this->calculateHandValue($dealerHand);
        
        // Dealer hits until 17 or more
        while ($dealerValue < 17) {
            $nonce++;
            $newDealerCard = array_pop($deck);
            $dealerHand[] = $newDealerCard;
            $dealerValue = $this->calculateHandValue($dealerHand);
        }
        
        // Determine outcome
        $outcome = 'lose'; // Default
        $payout = 0;
        
        if ($dealerValue > 21) {
            // Dealer busted, player wins
            $outcome = 'win';
            $betResult = 'dealer_bust';
        } elseif ($playerValue > $dealerValue) {
            // Player wins
            $outcome = 'win';
            $betResult = 'player_higher';
        } elseif ($playerValue < $dealerValue) {
            // Dealer wins
            $outcome = 'lose';
            $betResult = 'dealer_higher';
        } else {
            // Push (tie)
            $outcome = 'push';
            $betResult = 'push';
        }
        
        // Update game session
        $gameSession->status = GameSession::STATUS_COMPLETED;
        $gameSession->result = [
            'deck' => $deck,
            'playerHand' => $playerHand,
            'dealerHand' => $dealerHand,
            'state' => 'completed',
            'outcome' => $outcome,
            'nonce' => $nonce,
        ];
        $gameSession->save();
        
        // Update bet status and process payout
        if ($outcome === 'win') {
            $bet->status = Bet::STATUS_WON;
            $payout = $bet->potential_payout;
            
            // Add winnings to balance
            $user->updateBalance($payout, 'credit');
            
            // Create transaction for winnings
            Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_WIN,
                'amount' => $payout,
                'balance_before' => $user->balance - $payout,
                'balance_after' => $user->balance,
                'status' => Transaction::STATUS_COMPLETED,
                'reference' => 'BJ-WIN-' . Str::random(8),
                'meta' => [
                    'game_session_id' => $gameSession->id,
                    'game_type' => GameSession::GAME_BLACKJACK,
                    'result' => $betResult,
                ],
            ]);
            
        } elseif ($outcome === 'push') {
            $bet->status = Bet::STATUS_PUSH;
            $payout = $bet->amount;
            
            // Return bet amount
            $user->updateBalance($payout, 'credit');
            
            // Create transaction for returned bet
            Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_WIN,
                'amount' => $payout,
                'balance_before' => $user->balance - $payout,
                'balance_after' => $user->balance,
                'status' => Transaction::STATUS_COMPLETED,
                'reference' => 'BJ-PUSH-' . Str::random(8),
                'meta' => [
                    'game_session_id' => $gameSession->id,
                    'game_type' => GameSession::GAME_BLACKJACK,
                ],
            ]);
            
        } else {
            $bet->status = Bet::STATUS_LOST;
        }
        
        $bet->result = $betResult;
        $bet->save();
        
        return response()->json([
            'status' => 'success',
            'game_session_id' => $gameSession->id,
            'player_hand' => $playerHand,
            'dealer_hand' => $dealerHand,
            'player_value' => $playerValue,
            'dealer_value' => $dealerValue,
            'outcome' => $outcome,
            'payout' => $payout,
            'balance' => $user->balance,
            'is_final' => true,
            'nonce' => $nonce,
        ]);
    }
}