<?php

namespace App\Http\Controllers;

use App\Models\KycVerification;
use App\Models\KycDocument;
use App\Models\AmlCheck;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class KycController extends Controller
{
    /**
     * Get the KYC status for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function status(Request $request)
    {
        $user = $request->user();
        
        $kycVerification = KycVerification::firstOrCreate(
            ['user_id' => $user->id],
            ['status' => 'not_started']
        );
        
        $documents = KycDocument::where('user_id', $user->id)->get();
        
        return response()->json([
            'status' => 'success',
            'kyc_status' => $kycVerification->status,
            'identity_verification' => $kycVerification->identity_verification,
            'address_verification' => $kycVerification->address_verification,
            'age_verification' => $kycVerification->age_verification,
            'documents' => $documents,
            'is_verified' => $kycVerification->status === 'verified',
        ]);
    }

    /**
     * Upload a KYC document.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function uploadDocument(Request $request)
    {
        $request->validate([
            'document_type' => 'required|in:id_card,passport,driving_license,utility_bill,bank_statement,other',
            'document_file' => 'required|file|mimes:jpeg,jpg,png,pdf|max:10240', // 10MB max
            'document_number' => 'nullable|string|max:50',
            'issuing_country' => 'nullable|string|max:100',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:issue_date',
        ]);
        
        $user = $request->user();
        
        // Store the document
        $fileName = Str::uuid() . '.' . $request->file('document_file')->extension();
        $filePath = $request->file('document_file')->storeAs(
            'kyc_documents/' . $user->id,
            $fileName,
            'private'
        );
        
        // Create document record
        $document = new KycDocument();
        $document->user_id = $user->id;
        $document->document_type = $request->document_type;
        $document->document_number = $request->document_number;
        $document->issuing_country = $request->issuing_country;
        $document->issue_date = $request->issue_date;
        $document->expiry_date = $request->expiry_date;
        $document->file_path = $filePath;
        $document->status = 'pending';
        $document->save();
        
        // Update KYC verification status
        $kycVerification = KycVerification::firstOrCreate(
            ['user_id' => $user->id],
            ['status' => 'not_started']
        );
        
        // Update document type verification status
        if (in_array($request->document_type, ['id_card', 'passport', 'driving_license'])) {
            $kycVerification->identity_verification = 'pending';
            $kycVerification->identity_submitted_at = now();
        } elseif (in_array($request->document_type, ['utility_bill', 'bank_statement'])) {
            $kycVerification->address_verification = 'pending';
            $kycVerification->address_submitted_at = now();
        }
        
        // Update overall status
        if ($kycVerification->status === 'not_started') {
            $kycVerification->status = 'pending';
        }
        
        $kycVerification->save();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Document uploaded successfully and pending verification',
            'document' => $document,
        ]);
    }

    /**
     * Complete KYC process.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function completeKyc(Request $request)
    {
        $user = $request->user();
        
        $kycVerification = KycVerification::where('user_id', $user->id)->first();
        
        if (!$kycVerification) {
            return response()->json([
                'status' => 'error',
                'message' => 'KYC process not started yet',
            ], 400);
        }
        
        // Check if all required documents are submitted
        $identityDocumentCount = KycDocument::where('user_id', $user->id)
            ->whereIn('document_type', ['id_card', 'passport', 'driving_license'])
            ->where('status', '!=', 'rejected')
            ->count();
            
        $addressDocumentCount = KycDocument::where('user_id', $user->id)
            ->whereIn('document_type', ['utility_bill', 'bank_statement'])
            ->where('status', '!=', 'rejected')
            ->count();
            
        if ($identityDocumentCount === 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Please upload an identity document (ID card, passport, or driving license)',
            ], 400);
        }
        
        if ($addressDocumentCount === 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Please upload a proof of address (utility bill or bank statement)',
            ], 400);
        }
        
        // Update status to in_progress
        $kycVerification->status = 'in_progress';
        $kycVerification->save();
        
        // In a real application, this would trigger a background job to process the KYC verification
        // For now, we'll just return a success message
        
        return response()->json([
            'status' => 'success',
            'message' => 'KYC verification process has been initiated. We will review your documents and update your status shortly.',
            'estimated_time' => '24-48 hours',
        ]);
    }

    /**
     * Get KYC requirements.
     *
     * @return \Illuminate\Http\Response
     */
    public function getRequirements()
    {
        return response()->json([
            'identity_documents' => [
                [
                    'type' => 'id_card',
                    'name' => 'ID Card',
                    'requirements' => [
                        'Must be valid and not expired',
                        'Must clearly show your full name, date of birth, and photo',
                        'Both sides of the ID card are required',
                    ],
                    'accepted_formats' => ['jpeg', 'jpg', 'png', 'pdf'],
                    'max_file_size' => '10MB',
                ],
                [
                    'type' => 'passport',
                    'name' => 'Passport',
                    'requirements' => [
                        'Must be valid and not expired',
                        'Must clearly show your full name, date of birth, and photo',
                        'The main page with your details must be visible',
                    ],
                    'accepted_formats' => ['jpeg', 'jpg', 'png', 'pdf'],
                    'max_file_size' => '10MB',
                ],
                [
                    'type' => 'driving_license',
                    'name' => 'Driving License',
                    'requirements' => [
                        'Must be valid and not expired',
                        'Must clearly show your full name, date of birth, and photo',
                        'Both sides of the driving license are required',
                    ],
                    'accepted_formats' => ['jpeg', 'jpg', 'png', 'pdf'],
                    'max_file_size' => '10MB',
                ],
            ],
            'address_documents' => [
                [
                    'type' => 'utility_bill',
                    'name' => 'Utility Bill',
                    'requirements' => [
                        'Must be issued within the last 3 months',
                        'Must clearly show your full name and current address',
                        'Acceptable utility bills: electricity, gas, water, internet, landline phone',
                    ],
                    'accepted_formats' => ['jpeg', 'jpg', 'png', 'pdf'],
                    'max_file_size' => '10MB',
                ],
                [
                    'type' => 'bank_statement',
                    'name' => 'Bank Statement',
                    'requirements' => [
                        'Must be issued within the last 3 months',
                        'Must clearly show your full name and current address',
                        'Must be from a regulated financial institution',
                    ],
                    'accepted_formats' => ['jpeg', 'jpg', 'png', 'pdf'],
                    'max_file_size' => '10MB',
                ],
            ],
            'general_requirements' => [
                'All documents must be in color',
                'All documents must be clearly legible',
                'Documents must not be manipulated or edited',
                'Partial or blurry documents will be rejected',
                'Maximum file size for all documents is 10MB',
            ],
        ]);
    }
}