<?php

namespace App\Http\Controllers;

use App\Models\Bet;
use App\Models\Match;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class BetController extends Controller
{
    /**
     * Place a sports bet.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function place(Request $request)
    {
        $request->validate([
            'selections' => 'required|array|min:1|max:10',
            'selections.*.match_id' => 'required|exists:matches,id',
            'selections.*.bet_type' => 'required|string|in:home,away,draw',
            'selections.*.odds' => 'required|numeric|min:1.01|max:1000',
            'amount' => 'required|numeric|min:1|max:100000',
            'bet_type' => 'required|string|in:single,parlay',
        ]);

        $user = $request->user();
        $amount = $request->amount;
        $betType = $request->bet_type;
        $selections = $request->selections;

        // Check if user has sufficient balance
        if (!$user->hasSufficientBalance($amount)) {
            throw ValidationException::withMessages([
                'amount' => ['Insufficient balance'],
            ]);
        }

        // Validate selections
        $validatedSelections = [];
        $totalOdds = 1.0;

        foreach ($selections as $selection) {
            $match = Match::find($selection['match_id']);
            
            if (!$match) {
                throw ValidationException::withMessages([
                    'selections' => ['One or more matches not found'],
                ]);
            }

            if (!$match->acceptsBets()) {
                throw ValidationException::withMessages([
                    'selections' => ['Match ' . $match->home_team . ' vs ' . $match->away_team . ' is no longer accepting bets'],
                ]);
            }

            // Validate odds against current odds (with 5% tolerance)
            $currentOdds = $this->getCurrentOdds($match, $selection['bet_type']);
            if (!$currentOdds || abs($currentOdds - $selection['odds']) > ($currentOdds * 0.05)) {
                throw ValidationException::withMessages([
                    'selections' => ['Odds have changed for ' . $match->home_team . ' vs ' . $match->away_team],
                ]);
            }

            $validatedSelections[] = [
                'match_id' => $match->id,
                'match_name' => $match->home_team . ' vs ' . $match->away_team,
                'bet_type' => $selection['bet_type'],
                'odds' => $currentOdds,
                'team_name' => $this->getTeamName($match, $selection['bet_type']),
            ];

            // Calculate total odds for parlay
            if ($betType === 'parlay') {
                $totalOdds *= $currentOdds;
            }
        }

        // For single bets, only allow one selection
        if ($betType === 'single' && count($validatedSelections) > 1) {
            throw ValidationException::withMessages([
                'bet_type' => ['Single bets can only have one selection'],
            ]);
        }

        // Calculate potential payout
        if ($betType === 'single') {
            $potentialPayout = $amount * $validatedSelections[0]['odds'];
        } else {
            $potentialPayout = $amount * $totalOdds;
        }

        // Deduct bet amount from user balance
        $balanceBefore = $user->balance;
        $user->updateBalance($amount, 'debit');

        // Create transaction record
        $transaction = Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_BET,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $user->balance,
            'status' => Transaction::STATUS_COMPLETED,
            'reference' => 'SPORTS-BET-' . Str::random(8),
            'meta' => [
                'bet_type' => $betType,
                'selections_count' => count($validatedSelections),
            ],
        ]);

        // Create bet record
        $bet = Bet::create([
            'user_id' => $user->id,
            'amount' => $amount,
            'type' => $betType,
            'odds' => $betType === 'single' ? $validatedSelections[0]['odds'] : $totalOdds,
            'potential_payout' => $potentialPayout,
            'status' => Bet::STATUS_PENDING,
            'selections' => $validatedSelections,
            'meta' => [
                'transaction_id' => $transaction->id,
                'placed_at' => now(),
            ],
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Bet placed successfully',
            'bet' => $bet,
            'balance' => $user->balance,
        ]);
    }

    /**
     * Get user's betting history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $request->validate([
            'status' => 'sometimes|string|in:pending,won,lost,push,canceled,all',
            'type' => 'sometimes|string|in:single,parlay,casino,all',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:5|max:100',
        ]);

        $perPage = $request->per_page ?? 15;
        $status = $request->status ?? 'all';
        $type = $request->type ?? 'all';
        
        $query = $request->user()->bets()->latest();
        
        if ($status !== 'all') {
            $query->where('status', $status);
        }
        
        if ($type !== 'all') {
            $query->where('type', $type);
        }
        
        $bets = $query->paginate($perPage);
        
        return response()->json([
            'status' => 'success',
            'bets' => $bets,
        ]);
    }

    /**
     * Get a specific bet.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        $bet = $request->user()->bets()->find($id);

        if (!$bet) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bet not found',
            ], 404);
        }

        // Load related data for sports bets
        if ($bet->type !== 'casino' && $bet->selections) {
            $matchIds = collect($bet->selections)->pluck('match_id')->toArray();
            $matches = Match::whereIn('id', $matchIds)->with('sport')->get()->keyBy('id');
            
            // Add match details to selections
            $selectionsWithMatches = collect($bet->selections)->map(function ($selection) use ($matches) {
                $match = $matches->get($selection['match_id']);
                if ($match) {
                    $selection['match'] = $match;
                }
                return $selection;
            });
            
            $bet->selections_with_matches = $selectionsWithMatches;
        }

        return response()->json([
            'status' => 'success',
            'bet' => $bet,
        ]);
    }

    /**
     * Cancel a pending bet (if allowed).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request, $id)
    {
        $bet = $request->user()->bets()->find($id);

        if (!$bet) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bet not found',
            ], 404);
        }

        if ($bet->status !== Bet::STATUS_PENDING) {
            return response()->json([
                'status' => 'error',
                'message' => 'Only pending bets can be canceled',
            ], 400);
        }

        // Check if any matches have started (for sports bets)
        if ($bet->type !== 'casino' && $bet->selections) {
            $matchIds = collect($bet->selections)->pluck('match_id')->toArray();
            $startedMatches = Match::whereIn('id', $matchIds)
                ->where('status', '!=', Match::STATUS_UPCOMING)
                ->exists();
                
            if ($startedMatches) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot cancel bet - one or more matches have already started',
                ], 400);
            }
        }

        $user = $request->user();

        // Update bet status
        $bet->status = Bet::STATUS_CANCELED;
        $bet->result = 'canceled';
        $bet->save();

        // Refund the bet amount
        $user->updateBalance($bet->amount, 'credit');

        // Create refund transaction
        Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WIN, // Using WIN type for refunds
            'amount' => $bet->amount,
            'balance_before' => $user->balance - $bet->amount,
            'balance_after' => $user->balance,
            'status' => Transaction::STATUS_COMPLETED,
            'reference' => 'BET-CANCEL-' . Str::random(8),
            'meta' => [
                'bet_id' => $bet->id,
                'reason' => 'bet_canceled',
            ],
        ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Bet canceled successfully',
            'bet' => $bet,
            'balance' => $user->balance,
        ]);
    }

    /**
     * Get current odds for a match and bet type.
     *
     * @param  \App\Models\Match  $match
     * @param  string  $betType
     * @return float|null
     */
    protected function getCurrentOdds($match, $betType)
    {
        switch ($betType) {
            case 'home':
                return $match->home_odds;
            case 'away':
                return $match->away_odds;
            case 'draw':
                return $match->draw_odds;
            default:
                return null;
        }
    }

    /**
     * Get team name for a bet type.
     *
     * @param  \App\Models\Match  $match
     * @param  string  $betType
     * @return string
     */
    protected function getTeamName($match, $betType)
    {
        switch ($betType) {
            case 'home':
                return $match->home_team;
            case 'away':
                return $match->away_team;
            case 'draw':
                return 'Draw';
            default:
                return 'Unknown';
        }
    }
}