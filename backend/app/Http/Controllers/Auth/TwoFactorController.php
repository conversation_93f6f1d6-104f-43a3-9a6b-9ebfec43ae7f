<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\UserSecurity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use <PERSON>ragmaRX\Google2FA\Google2FA;

class TwoFactorController extends Controller
{
    protected $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * Enable 2FA for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function enable(Request $request)
    {
        $user = $request->user();
        
        // Check if 2FA is already enabled
        $security = UserSecurity::firstOrCreate(['user_id' => $user->id]);
        
        if ($security->two_factor_enabled) {
            return response()->json([
                'status' => 'error',
                'message' => 'Two-factor authentication is already enabled',
            ], 400);
        }
        
        // Generate a new secret
        $secret = $this->google2fa->generateSecretKey();
        
        // Store the secret
        $security->two_factor_secret = encrypt($secret);
        $security->save();
        
        // Generate the QR code URL
        $qrCodeUrl = $this->google2fa->getQRCodeUrl(
            config('app.name'),
            $user->email,
            $secret
        );
        
        // Generate recovery codes
        $recoveryCodes = $this->generateRecoveryCodes();
        $security->two_factor_recovery_codes = encrypt(json_encode($recoveryCodes));
        $security->save();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Two-factor authentication has been enabled. Please scan the QR code with your authenticator app.',
            'data' => [
                'qr_code' => $qrCodeUrl,
                'secret_key' => $secret, // For manual entry
                'recovery_codes' => $recoveryCodes,
            ],
        ]);
    }

    /**
     * Confirm and activate 2FA for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function confirm(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
        ]);
        
        $user = $request->user();
        $security = UserSecurity::where('user_id', $user->id)->first();
        
        if (!$security) {
            return response()->json([
                'status' => 'error',
                'message' => 'Two-factor authentication is not set up',
            ], 400);
        }
        
        $secret = decrypt($security->two_factor_secret);
        
        // Verify the code
        if (!$this->google2fa->verifyKey($secret, $request->code)) {
            throw ValidationException::withMessages([
                'code' => ['The verification code is invalid.'],
            ]);
        }
        
        // Activate 2FA
        $security->two_factor_enabled = true;
        $security->two_factor_confirmed_at = now();
        $security->save();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Two-factor authentication has been activated.',
        ]);
    }

    /**
     * Disable 2FA for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function disable(Request $request)
    {
        $request->validate([
            'password' => 'required|string',
            'code' => 'required|string|size:6',
        ]);
        
        $user = $request->user();
        
        // Verify password
        if (!Auth::guard('web')->validate([
            'email' => $user->email,
            'password' => $request->password,
        ])) {
            throw ValidationException::withMessages([
                'password' => ['The password is incorrect.'],
            ]);
        }
        
        $security = UserSecurity::where('user_id', $user->id)->first();
        
        if (!$security || !$security->two_factor_enabled) {
            return response()->json([
                'status' => 'error',
                'message' => 'Two-factor authentication is not enabled',
            ], 400);
        }
        
        $secret = decrypt($security->two_factor_secret);
        
        // Verify the code
        if (!$this->google2fa->verifyKey($secret, $request->code)) {
            throw ValidationException::withMessages([
                'code' => ['The verification code is invalid.'],
            ]);
        }
        
        // Disable 2FA
        $security->two_factor_enabled = false;
        $security->two_factor_secret = null;
        $security->two_factor_recovery_codes = null;
        $security->two_factor_confirmed_at = null;
        $security->save();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Two-factor authentication has been disabled.',
        ]);
    }

    /**
     * Verify 2FA code during login.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verify(Request $request)
    {
        $request->validate([
            'code' => 'required|string|size:6',
            'user_id' => 'required|exists:users,id',
        ]);
        
        $user = User::find($request->user_id);
        $security = UserSecurity::where('user_id', $user->id)->first();
        
        if (!$security || !$security->two_factor_enabled) {
            return response()->json([
                'status' => 'error',
                'message' => 'Two-factor authentication is not enabled for this user',
            ], 400);
        }
        
        $secret = decrypt($security->two_factor_secret);
        
        // Verify the code
        if (!$this->google2fa->verifyKey($secret, $request->code)) {
            // Check if it's a recovery code
            $recoveryCodes = json_decode(decrypt($security->two_factor_recovery_codes), true);
            if (in_array($request->code, $recoveryCodes)) {
                // Remove the used recovery code
                $recoveryCodes = array_diff($recoveryCodes, [$request->code]);
                $security->two_factor_recovery_codes = encrypt(json_encode($recoveryCodes));
                $security->save();
            } else {
                throw ValidationException::withMessages([
                    'code' => ['The verification code is invalid.'],
                ]);
            }
        }
        
        // Log in the user
        Auth::login($user);
        
        // Generate token
        $token = $user->createToken('auth_token')->plainTextToken;
        
        return response()->json([
            'status' => 'success',
            'message' => 'Two-factor authentication verified',
            'user' => $user,
            'token' => $token,
        ]);
    }

    /**
     * Generate recovery codes.
     *
     * @return array
     */
    private function generateRecoveryCodes()
    {
        $recoveryCodes = [];
        
        for ($i = 0; $i < 8; $i++) {
            $recoveryCodes[] = strtoupper(Str::random(10));
        }
        
        return $recoveryCodes;
    }

    /**
     * Regenerate recovery codes.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function regenerateRecoveryCodes(Request $request)
    {
        $request->validate([
            'password' => 'required|string',
            'code' => 'required|string|size:6',
        ]);
        
        $user = $request->user();
        
        // Verify password
        if (!Auth::guard('web')->validate([
            'email' => $user->email,
            'password' => $request->password,
        ])) {
            throw ValidationException::withMessages([
                'password' => ['The password is incorrect.'],
            ]);
        }
        
        $security = UserSecurity::where('user_id', $user->id)->first();
        
        if (!$security || !$security->two_factor_enabled) {
            return response()->json([
                'status' => 'error',
                'message' => 'Two-factor authentication is not enabled',
            ], 400);
        }
        
        $secret = decrypt($security->two_factor_secret);
        
        // Verify the code
        if (!$this->google2fa->verifyKey($secret, $request->code)) {
            throw ValidationException::withMessages([
                'code' => ['The verification code is invalid.'],
            ]);
        }
        
        // Generate new recovery codes
        $recoveryCodes = $this->generateRecoveryCodes();
        $security->two_factor_recovery_codes = encrypt(json_encode($recoveryCodes));
        $security->save();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Recovery codes have been regenerated.',
            'data' => [
                'recovery_codes' => $recoveryCodes,
            ],
        ]);
    }
}