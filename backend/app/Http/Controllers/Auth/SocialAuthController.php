<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialAuthController extends Controller
{
    /**
     * Redirect the user to the Provider authentication page.
     *
     * @param string $provider
     * @return \Illuminate\Http\Response
     */
    public function redirectToProvider($provider)
    {
        if (!$this->isProviderEnabled($provider)) {
            return response()->json([
                'status' => 'error',
                'message' => 'This social login provider is not supported'
            ], 400);
        }

        try {
            return Socialite::driver($provider)->stateless()->redirect();
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Could not connect to ' . ucfirst($provider) . '. Please try again.'
            ], 500);
        }
    }

    /**
     * Obtain the user information from Provider.
     *
     * @param string $provider
     * @return \Illuminate\Http\Response
     */
    public function handleProviderCallback($provider)
    {
        if (!$this->isProviderEnabled($provider)) {
            return response()->json([
                'status' => 'error',
                'message' => 'This social login provider is not supported'
            ], 400);
        }

        try {
            $socialUser = Socialite::driver($provider)->stateless()->user();
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Could not retrieve user information from ' . ucfirst($provider)
            ], 500);
        }

        // Check if this social account is already registered
        $user = User::where('email', $socialUser->getEmail())->first();

        // If not registered, create a new user
        if (!$user) {
            // Create new user
            $user = User::create([
                'name' => $socialUser->getName(),
                'email' => $socialUser->getEmail(),
                'username' => $this->generateUniqueUsername($socialUser->getNickname() ?? $socialUser->getName()),
                'password' => Hash::make(Str::random(16)), // Random password
                'avatar' => $socialUser->getAvatar(),
                'balance' => 1000.00, // Starting balance for new users
                'email_verified_at' => now(), // Social login implies email is verified
            ]);

            // Log the social registration
            activity()
                ->causedBy($user)
                ->withProperties([
                    'provider' => $provider,
                    'provider_id' => $socialUser->getId(),
                    'ip_address' => request()->ip(),
                    'user_agent' => request()->userAgent(),
                ])
                ->log('registered_via_social');
        }

        // Log the user's social login
        activity()
            ->causedBy($user)
            ->withProperties([
                'provider' => $provider,
                'provider_id' => $socialUser->getId(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ])
            ->log('social_login');

        // Store social provider information
        $user->socialAccounts()->updateOrCreate(
            [
                'provider' => $provider,
                'provider_id' => $socialUser->getId(),
            ],
            [
                'token' => $socialUser->token,
                'refresh_token' => $socialUser->refreshToken ?? null,
                'expires_at' => $socialUser->expiresIn ? now()->addSeconds($socialUser->expiresIn) : null,
                'avatar' => $socialUser->getAvatar(),
            ]
        );

        // Auto login the user
        Auth::login($user);
        
        // Generate API token for the user
        $token = $user->createToken('social_auth_token')->plainTextToken;

        // Return token and user info
        return response()->json([
            'status' => 'success',
            'message' => 'Successfully logged in with ' . ucfirst($provider),
            'user' => $user,
            'token' => $token,
        ]);
    }

    /**
     * Check if the social provider is enabled.
     *
     * @param string $provider
     * @return bool
     */
    private function isProviderEnabled($provider)
    {
        return in_array($provider, ['google', 'facebook', 'apple']) && 
               config("auth.social.{$provider}.enabled") === true;
    }

    /**
     * Generate a unique username.
     *
     * @param string $baseUsername
     * @return string
     */
    private function generateUniqueUsername($baseUsername)
    {
        $baseUsername = Str::slug($baseUsername);
        $username = $baseUsername;
        $counter = 1;
        
        // Keep checking until we find a unique username
        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }
        
        return $username;
    }

    /**
     * Unlink a social account from the user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $provider
     * @return \Illuminate\Http\Response
     */
    public function unlinkSocialAccount(Request $request, $provider)
    {
        $user = $request->user();
        
        // Check if user has a password before unlinking
        if (!$user->password && $user->socialAccounts()->count() <= 1) {
            return response()->json([
                'status' => 'error',
                'message' => 'You must set a password before unlinking your only social account'
            ], 400);
        }
        
        $socialAccount = $user->socialAccounts()->where('provider', $provider)->first();
        
        if (!$socialAccount) {
            return response()->json([
                'status' => 'error',
                'message' => 'No linked ' . ucfirst($provider) . ' account found'
            ], 404);
        }
        
        $socialAccount->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => ucfirst($provider) . ' account successfully unlinked'
        ]);
    }
}