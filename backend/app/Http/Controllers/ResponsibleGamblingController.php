<?php

namespace App\Http\Controllers;

use App\Models\ResponsibleGamblingLimit;
use App\Models\SelfExclusion;
use App\Models\RealityCheck;
use App\Models\GamblingBehaviorPattern;
use App\Events\RealityCheckDue;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ResponsibleGamblingController extends Controller
{
    /**
     * Get the responsible gambling settings for the authenticated user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getSettings(Request $request)
    {
        $user = $request->user();
        
        $limits = ResponsibleGamblingLimit::where('user_id', $user->id)
            ->where('is_active', true)
            ->first();
            
        $selfExclusion = SelfExclusion::where('user_id', $user->id)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>', now());
            })
            ->first();
            
        return response()->json([
            'limits' => $limits,
            'self_exclusion' => $selfExclusion,
            'has_active_restrictions' => ($limits || $selfExclusion) ? true : false,
        ]);
    }

    /**
     * Set deposit limits.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function setDepositLimits(Request $request)
    {
        $request->validate([
            'daily_limit' => 'nullable|numeric|min:0',
            'weekly_limit' => 'nullable|numeric|min:0',
            'monthly_limit' => 'nullable|numeric|min:0',
        ]);
        
        $user = $request->user();
        
        // Get existing limits or create new
        $limits = ResponsibleGamblingLimit::firstOrCreate(
            ['user_id' => $user->id],
            ['is_active' => true]
        );
        
        // Check if decreasing limits (which take effect immediately)
        // or increasing limits (which have a cooling-off period)
        $newLimits = [];
        $pendingLimits = [];
        
        if ($request->has('daily_limit')) {
            if (!$limits->deposit_daily_limit || $request->daily_limit < $limits->deposit_daily_limit) {
                $newLimits['deposit_daily_limit'] = $request->daily_limit;
            } else {
                // Increasing limit requires cooling-off period
                $pendingLimits['deposit_daily_limit'] = $request->daily_limit;
            }
        }
        
        if ($request->has('weekly_limit')) {
            if (!$limits->deposit_weekly_limit || $request->weekly_limit < $limits->deposit_weekly_limit) {
                $newLimits['deposit_weekly_limit'] = $request->weekly_limit;
            } else {
                // Increasing limit requires cooling-off period
                $pendingLimits['deposit_weekly_limit'] = $request->weekly_limit;
            }
        }
        
        if ($request->has('monthly_limit')) {
            if (!$limits->deposit_monthly_limit || $request->monthly_limit < $limits->deposit_monthly_limit) {
                $newLimits['deposit_monthly_limit'] = $request->monthly_limit;
            } else {
                // Increasing limit requires cooling-off period
                $pendingLimits['deposit_monthly_limit'] = $request->monthly_limit;
            }
        }
        
        // Apply immediate changes
        if (!empty($newLimits)) {
            $limits->update($newLimits);
        }
        
        // Store pending changes with effective date (3 days from now)
        if (!empty($pendingLimits)) {
            $limits->update([
                'pending_changes' => json_encode($pendingLimits),
                'pending_effective_date' => now()->addDays(3),
            ]);
        }
        
        return response()->json([
            'status' => 'success',
            'message' => 'Deposit limits updated successfully',
            'limits' => $limits->fresh(),
            'pending_changes' => !empty($pendingLimits) ? [
                'changes' => $pendingLimits,
                'effective_date' => now()->addDays(3),
            ] : null,
        ]);
    }

    /**
     * Set loss limits.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function setLossLimits(Request $request)
    {
        $request->validate([
            'daily_limit' => 'nullable|numeric|min:0',
            'weekly_limit' => 'nullable|numeric|min:0',
            'monthly_limit' => 'nullable|numeric|min:0',
        ]);
        
        $user = $request->user();
        
        // Get existing limits or create new
        $limits = ResponsibleGamblingLimit::firstOrCreate(
            ['user_id' => $user->id],
            ['is_active' => true]
        );
        
        // Check if decreasing limits (which take effect immediately)
        // or increasing limits (which have a cooling-off period)
        $newLimits = [];
        $pendingLimits = [];
        
        if ($request->has('daily_limit')) {
            if (!$limits->loss_daily_limit || $request->daily_limit < $limits->loss_daily_limit) {
                $newLimits['loss_daily_limit'] = $request->daily_limit;
            } else {
                // Increasing limit requires cooling-off period
                $pendingLimits['loss_daily_limit'] = $request->daily_limit;
            }
        }
        
        if ($request->has('weekly_limit')) {
            if (!$limits->loss_weekly_limit || $request->weekly_limit < $limits->loss_weekly_limit) {
                $newLimits['loss_weekly_limit'] = $request->weekly_limit;
            } else {
                // Increasing limit requires cooling-off period
                $pendingLimits['loss_weekly_limit'] = $request->weekly_limit;
            }
        }
        
        if ($request->has('monthly_limit')) {
            if (!$limits->loss_monthly_limit || $request->monthly_limit < $limits->loss_monthly_limit) {
                $newLimits['loss_monthly_limit'] = $request->monthly_limit;
            } else {
                // Increasing limit requires cooling-off period
                $pendingLimits['loss_monthly_limit'] = $request->monthly_limit;
            }
        }
        
        // Apply immediate changes
        if (!empty($newLimits)) {
            $limits->update($newLimits);
        }
        
        // Store pending changes with effective date (3 days from now)
        if (!empty($pendingLimits)) {
            $limits->update([
                'pending_changes' => json_encode($pendingLimits),
                'pending_effective_date' => now()->addDays(3),
            ]);
        }
        
        return response()->json([
            'status' => 'success',
            'message' => 'Loss limits updated successfully',
            'limits' => $limits->fresh(),
            'pending_changes' => !empty($pendingLimits) ? [
                'changes' => $pendingLimits,
                'effective_date' => now()->addDays(3),
            ] : null,
        ]);
    }

    /**
     * Set session time limits.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function setSessionTimeLimit(Request $request)
    {
        $request->validate([
            'session_time_limit' => 'required|integer|min:15|max:480', // 15 min to 8 hours
        ]);
        
        $user = $request->user();
        
        // Get existing limits or create new
        $limits = ResponsibleGamblingLimit::firstOrCreate(
            ['user_id' => $user->id],
            ['is_active' => true]
        );
        
        $limits->update([
            'session_time_limit' => $request->session_time_limit,
        ]);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Session time limit updated successfully',
            'limits' => $limits->fresh(),
        ]);
    }

    /**
     * Set reality check settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function setRealityCheck(Request $request)
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'interval' => 'required_if:enabled,true|nullable|integer|min:15|max:120',
        ]);
        
        $user = $request->user();
        
        // Get existing limits or create new
        $limits = ResponsibleGamblingLimit::firstOrCreate(
            ['user_id' => $user->id],
            ['is_active' => true]
        );
        
        $limits->update([
            'reality_check_enabled' => $request->enabled,
            'reality_check_interval' => $request->interval,
        ]);
        
        return response()->json([
            'status' => 'success',
            'message' => 'Reality check settings updated successfully',
            'limits' => $limits->fresh(),
        ]);
    }

    /**
     * Set self-exclusion.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function setSelfExclusion(Request $request)
    {
        $request->validate([
            'type' => 'required|in:temporary,permanent',
            'duration_days' => 'required_if:type,temporary|nullable|integer|min:1|max:365',
            'reason' => 'nullable|string|max:500',
        ]);
        
        $user = $request->user();
        
        // Check if already has active self-exclusion
        $existingSelfExclusion = SelfExclusion::where('user_id', $user->id)
            ->where('is_active', true)
            ->first();
            
        if ($existingSelfExclusion) {
            throw ValidationException::withMessages([
                'type' => ['You already have an active self-exclusion period.'],
            ]);
        }
        
        // Create self-exclusion
        $selfExclusion = new SelfExclusion();
        $selfExclusion->user_id = $user->id;
        $selfExclusion->type = $request->type;
        $selfExclusion->start_date = now();
        
        if ($request->type === 'temporary') {
            $selfExclusion->end_date = now()->addDays($request->duration_days);
        }
        
        $selfExclusion->reason = $request->reason;
        $selfExclusion->is_active = true;
        $selfExclusion->save();
        
        // Revoke all tokens to force logout
        $user->tokens()->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Self-exclusion set successfully',
            'self_exclusion' => $selfExclusion,
        ]);
    }

    /**
     * Acknowledge a reality check.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function acknowledgeRealityCheck(Request $request)
    {
        $request->validate([
            'reality_check_id' => 'required|exists:reality_checks,id',
            'action' => 'required|in:continue,end',
        ]);
        
        $user = $request->user();
        
        $realityCheck = RealityCheck::where('id', $request->reality_check_id)
            ->where('user_id', $user->id)
            ->whereNull('acknowledged_at')
            ->firstOrFail();
            
        $realityCheck->acknowledged_at = now();
        $realityCheck->save();
        
        if ($request->action === 'end') {
            // End session by logging out
            $user->currentAccessToken()->delete();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Session ended successfully',
                'logout' => true,
            ]);
        }
        
        return response()->json([
            'status' => 'success',
            'message' => 'Reality check acknowledged',
        ]);
    }

    /**
     * Get responsible gambling resources.
     *
     * @return \Illuminate\Http\Response
     */
    public function getResources()
    {
        // This would typically come from a database, but for now we'll hardcode
        return response()->json([
            'helplines' => [
                [
                    'name' => 'National Problem Gambling Helpline',
                    'phone' => '**************',
                    'website' => 'https://www.ncpgambling.org/help-treatment/national-helpline-**************/',
                ],
                [
                    'name' => 'Gamblers Anonymous',
                    'phone' => '**************',
                    'website' => 'http://www.gamblersanonymous.org',
                ],
            ],
            'organizations' => [
                [
                    'name' => 'National Council on Problem Gambling',
                    'website' => 'https://www.ncpgambling.org/',
                    'description' => 'The National Council on Problem Gambling is the national advocate for programs and services to assist problem gamblers and their families.',
                ],
                [
                    'name' => 'GamCare',
                    'website' => 'https://www.gamcare.org.uk/',
                    'description' => 'GamCare provides support, information and advice to anyone suffering through a gambling problem.',
                ],
                [
                    'name' => 'Gamblers Anonymous',
                    'website' => 'http://www.gamblersanonymous.org',
                    'description' => 'Gamblers Anonymous is a fellowship of men and women who share their experience, strength and hope with each other that they may solve their common problem and help others to recover from a gambling problem.',
                ],
            ],
            'self_assessment' => [
                'url' => '/responsible-gambling/self-assessment',
                'description' => 'Take our self-assessment test to evaluate your gambling behavior.',
            ],
            'articles' => [
                [
                    'title' => 'Understanding Problem Gambling',
                    'url' => '/responsible-gambling/articles/understanding-problem-gambling',
                ],
                [
                    'title' => 'Setting Healthy Gambling Limits',
                    'url' => '/responsible-gambling/articles/setting-healthy-gambling-limits',
                ],
                [
                    'title' => 'Signs of Problem Gambling',
                    'url' => '/responsible-gambling/articles/signs-of-problem-gambling',
                ],
            ],
        ]);
    }
}