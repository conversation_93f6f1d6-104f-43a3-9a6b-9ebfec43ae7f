<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class GameController extends Controller
{
    /**
     * Get available games.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response()->json([
            'status' => 'success',
            'games' => [
                'blackjack' => [
                    'name' => 'Blackjack',
                    'type' => 'card',
                    'min_bet' => 1,
                    'max_bet' => 10000,
                    'house_edge' => 0.5,
                ],
                'baccarat' => [
                    'name' => 'Baccarat',
                    'type' => 'card',
                    'min_bet' => 1,
                    'max_bet' => 10000,
                    'house_edge' => 1.06,
                ],
                'roulette' => [
                    'name' => 'Roulette',
                    'type' => 'wheel',
                    'min_bet' => 1,
                    'max_bet' => 5000,
                    'house_edge' => 2.7,
                ],
            ],
        ]);
    }

    /**
     * Get specific game information.
     *
     * @param  string  $game
     * @return \Illuminate\Http\Response
     */
    public function show($game)
    {
        $games = [
            'blackjack' => [
                'name' => 'Blackjack',
                'type' => 'card',
                'min_bet' => 1,
                'max_bet' => 10000,
                'house_edge' => 0.5,
                'rules' => 'Beat the dealer by getting 21 or as close as possible without going over.',
            ],
            'baccarat' => [
                'name' => 'Baccarat',
                'type' => 'card',
                'min_bet' => 1,
                'max_bet' => 10000,
                'house_edge' => 1.06,
                'rules' => 'Bet on Player, Banker, or Tie. Closest to 9 wins.',
            ],
        ];

        if (!isset($games[$game])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Game not found',
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'game' => $games[$game],
        ]);
    }

    /**
     * Get user's game history.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $game
     * @return \Illuminate\Http\Response
     */
    public function history(Request $request, $game)
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:5|max:100',
        ]);

        $perPage = $request->per_page ?? 15;
        
        $transactions = $request->user()
            ->transactions()
            ->where('type', 'bet')
            ->whereJsonContains('meta->game', $game)
            ->latest()
            ->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'transactions' => $transactions,
        ]);
    }

    /**
     * Record a game transaction (bet or win).
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function recordTransaction(Request $request)
    {
        $request->validate([
            'type' => 'required|string|in:bet,win',
            'amount' => 'required|numeric|min:0.01|max:100000',
            'game' => 'required|string|in:blackjack,baccarat,roulette,slots,dice,crash,mines',
            'metadata' => 'sometimes|array',
        ]);

        $user = $request->user();
        $type = $request->type;
        $amount = $request->amount;
        $game = $request->game;
        $metadata = $request->metadata ?? [];

        // For bet transactions, check if user has sufficient balance
        if ($type === 'bet') {
            if (!$user->hasSufficientBalance($amount)) {
                throw ValidationException::withMessages([
                    'amount' => ['Insufficient balance for this bet.'],
                ]);
            }

            $balanceBefore = $user->balance;
            
            // Deduct bet amount
            if (!$user->updateBalance($amount, 'debit')) {
                throw ValidationException::withMessages([
                    'amount' => ['Failed to process bet. Please try again.'],
                ]);
            }

            // Create bet transaction
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_BET,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->balance,
                'status' => Transaction::STATUS_COMPLETED,
                'reference' => 'GAME-BET-' . Str::random(10),
                'meta' => array_merge([
                    'game' => $game,
                    'game_type' => 'casino',
                    'description' => ucfirst($game) . ' bet',
                ], $metadata),
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Bet recorded successfully',
                'transaction' => $transaction,
                'new_balance' => $user->balance,
            ]);
        }

        // For win transactions, add to balance
        if ($type === 'win') {
            $balanceBefore = $user->balance;
            
            // Add winnings
            if (!$user->updateBalance($amount, 'credit')) {
                throw ValidationException::withMessages([
                    'amount' => ['Failed to process winnings. Please contact support.'],
                ]);
            }

            // Create win transaction
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_WIN,
                'amount' => $amount,
                'balance_before' => $balanceBefore,
                'balance_after' => $user->balance,
                'status' => Transaction::STATUS_COMPLETED,
                'reference' => 'GAME-WIN-' . Str::random(10),
                'meta' => array_merge([
                    'game' => $game,
                    'game_type' => 'casino',
                    'description' => ucfirst($game) . ' win',
                ], $metadata),
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Win recorded successfully',
                'transaction' => $transaction,
                'new_balance' => $user->balance,
            ]);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Invalid transaction type',
        ], 400);
    }
}
