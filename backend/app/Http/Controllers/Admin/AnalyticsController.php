<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use App\Models\User;
use App\Models\Transaction;
use App\Models\Bet;
use App\Models\GameSession;
use App\Models\Match;
use App\Models\Sport;
use App\Models\DailyMetric;
use App\Models\UserMetric;
use App\Models\GameMetric;

class AnalyticsController extends Controller
{
    /**
     * Get platform overview statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function overview(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:day,week,month,year,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);
        
        $period = $request->period ?? 'month';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : $this->getStartDateFromPeriod($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
        
        // Get user statistics
        $totalUsers = User::count();
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeUsers = User::whereHas('gameSessions', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->orWhereHas('bets', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();
        
        // Get financial statistics
        $totalDeposits = Transaction::where('type', 'deposit')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
            
        $totalWithdrawals = Transaction::where('type', 'withdraw')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
            
        $totalBets = Bet::whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
            
        $totalWins = Transaction::where('type', 'win')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('amount');
            
        $grossGamingRevenue = $totalBets - $totalWins;
        
        // Get game statistics
        $totalCasinoGames = GameSession::whereBetween('created_at', [$startDate, $endDate])
            ->count();
            
        $totalSportsBets = Bet::whereNotNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();
            
        $popularCasinoGames = GameSession::whereBetween('created_at', [$startDate, $endDate])
            ->select('game_type', DB::raw('count(*) as total'))
            ->groupBy('game_type')
            ->orderByDesc('total')
            ->take(5)
            ->get();
            
        $popularSports = Bet::whereNotNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->join('matches', 'bets.match_id', '=', 'matches.id')
            ->join('sports', 'matches.sport_id', '=', 'sports.id')
            ->select('sports.name', DB::raw('count(*) as total'))
            ->groupBy('sports.name')
            ->orderByDesc('total')
            ->take(5)
            ->get();
            
        // Get daily metrics
        $dailyMetrics = DailyMetric::whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->orderBy('date')
            ->get();
            
        // Calculate trend metrics
        $userGrowthRate = $this->calculateGrowthRate($totalUsers - $newUsers, $totalUsers);
        $revenueGrowthRate = $this->calculateGrowthRate(
            $this->getPreviousPeriodRevenue($startDate, $endDate),
            $grossGamingRevenue
        );
        
        return response()->json([
            'status' => 'success',
            'data' => [
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'name' => $period,
                ],
                'users' => [
                    'total' => $totalUsers,
                    'new' => $newUsers,
                    'active' => $activeUsers,
                    'growth_rate' => $userGrowthRate,
                ],
                'financial' => [
                    'deposits' => $totalDeposits,
                    'withdrawals' => $totalWithdrawals,
                    'bets' => $totalBets,
                    'wins' => $totalWins,
                    'ggr' => $grossGamingRevenue,
                    'growth_rate' => $revenueGrowthRate,
                ],
                'games' => [
                    'casino_games_played' => $totalCasinoGames,
                    'sports_bets_placed' => $totalSportsBets,
                    'popular_casino_games' => $popularCasinoGames,
                    'popular_sports' => $popularSports,
                ],
                'daily_metrics' => $dailyMetrics,
            ],
        ]);
    }

    /**
     * Get revenue statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function revenue(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:day,week,month,year,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month',
        ]);
        
        $period = $request->period ?? 'month';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : $this->getStartDateFromPeriod($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
        $groupBy = $request->group_by ?? 'day';
        
        // Get grouped revenue data
        $dateFormat = $this->getDateFormatForGrouping($groupBy);
        
        $casinoRevenue = Bet::whereNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw('SUM(amount) as bets'),
                DB::raw('SUM(CASE WHEN status = "won" THEN potential_payout ELSE 0 END) as wins')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                $item->revenue = $item->bets - $item->wins;
                return $item;
            });
            
        $sportsRevenue = Bet::whereNotNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw('SUM(amount) as bets'),
                DB::raw('SUM(CASE WHEN status = "won" THEN potential_payout ELSE 0 END) as wins')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function ($item) {
                $item->revenue = $item->bets - $item->wins;
                return $item;
            });
            
        // Get deposits and withdrawals
        $deposits = Transaction::where('type', 'deposit')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw('SUM(amount) as amount')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $withdrawals = Transaction::where('type', 'withdraw')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw('SUM(amount) as amount')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        // Get bonus issuance data
        $bonuses = Transaction::where('type', 'bonus')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw('SUM(amount) as amount')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        // Calculate totals
        $totalRevenue = ($casinoRevenue->sum('revenue') + $sportsRevenue->sum('revenue'));
        $totalBets = ($casinoRevenue->sum('bets') + $sportsRevenue->sum('bets'));
        $totalWins = ($casinoRevenue->sum('wins') + $sportsRevenue->sum('wins'));
        $totalDeposits = $deposits->sum('amount');
        $totalWithdrawals = $withdrawals->sum('amount');
        $totalBonuses = $bonuses->sum('amount');
        
        // Calculate metrics
        $betWinRatio = $totalBets > 0 ? ($totalWins / $totalBets) * 100 : 0;
        $houseEdge = $totalBets > 0 ? ($totalRevenue / $totalBets) * 100 : 0;
        $returnToPlayer = 100 - $houseEdge;
        
        return response()->json([
            'status' => 'success',
            'data' => [
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'name' => $period,
                    'group_by' => $groupBy,
                ],
                'totals' => [
                    'revenue' => $totalRevenue,
                    'bets' => $totalBets,
                    'wins' => $totalWins,
                    'deposits' => $totalDeposits,
                    'withdrawals' => $totalWithdrawals,
                    'bonuses' => $totalBonuses,
                ],
                'metrics' => [
                    'bet_win_ratio' => round($betWinRatio, 2),
                    'house_edge' => round($houseEdge, 2),
                    'return_to_player' => round($returnToPlayer, 2),
                ],
                'time_series' => [
                    'casino_revenue' => $casinoRevenue,
                    'sports_revenue' => $sportsRevenue,
                    'deposits' => $deposits,
                    'withdrawals' => $withdrawals,
                    'bonuses' => $bonuses,
                ],
            ],
        ]);
    }

    /**
     * Get user statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function users(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:day,week,month,year,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month',
        ]);
        
        $period = $request->period ?? 'month';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : $this->getStartDateFromPeriod($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
        $groupBy = $request->group_by ?? 'day';
        
        // Get new users over time
        $dateFormat = $this->getDateFormatForGrouping($groupBy);
        
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date"),
                DB::raw('COUNT(*) as count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        // Get active users over time
        $activeUsers = DB::table(function ($query) use ($startDate, $endDate, $dateFormat) {
            $query->select(
                'user_id',
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date")
            )
            ->from('game_sessions')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->union(
                DB::table('bets')
                ->select(
                    'user_id',
                    DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as date")
                )
                ->whereBetween('created_at', [$startDate, $endDate])
            );
        }, 'user_activities')
        ->select('date', DB::raw('COUNT(DISTINCT user_id) as count'))
        ->groupBy('date')
        ->orderBy('date')
        ->get();
        
        // Get retention data
        $cohorts = User::whereBetween('created_at', [$startDate, $endDate])
            ->select(
                DB::raw("DATE_FORMAT(created_at, '{$dateFormat}') as cohort_date"),
                DB::raw('COUNT(*) as users')
            )
            ->groupBy('cohort_date')
            ->orderBy('cohort_date')
            ->get();
            
        // Get user KPIs
        $avgSessionsPerUser = GameSession::whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id', DB::raw('COUNT(*) as sessions'))
            ->groupBy('user_id')
            ->get()
            ->avg('sessions') ?? 0;
            
        $avgBetAmountPerUser = Bet::whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id', DB::raw('AVG(amount) as avg_bet'))
            ->groupBy('user_id')
            ->get()
            ->avg('avg_bet') ?? 0;
            
        $avgDepositAmountPerUser = Transaction::where('type', 'deposit')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id', DB::raw('AVG(amount) as avg_deposit'))
            ->groupBy('user_id')
            ->get()
            ->avg('avg_deposit') ?? 0;
            
        // Get top users by bet amount
        $topUsersByBets = Bet::whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id', DB::raw('SUM(amount) as total_bets'))
            ->groupBy('user_id')
            ->orderByDesc('total_bets')
            ->take(10)
            ->get()
            ->map(function ($item) {
                $user = User::find($item->user_id);
                return [
                    'user_id' => $item->user_id,
                    'username' => $user ? $user->username : 'Unknown',
                    'total_bets' => $item->total_bets,
                ];
            });
            
        // Get top users by deposits
        $topUsersByDeposits = Transaction::where('type', 'deposit')
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('user_id', DB::raw('SUM(amount) as total_deposits'))
            ->groupBy('user_id')
            ->orderByDesc('total_deposits')
            ->take(10)
            ->get()
            ->map(function ($item) {
                $user = User::find($item->user_id);
                return [
                    'user_id' => $item->user_id,
                    'username' => $user ? $user->username : 'Unknown',
                    'total_deposits' => $item->total_deposits,
                ];
            });
            
        return response()->json([
            'status' => 'success',
            'data' => [
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'name' => $period,
                    'group_by' => $groupBy,
                ],
                'user_growth' => [
                    'new_users' => $newUsers,
                    'active_users' => $activeUsers,
                ],
                'user_cohorts' => $cohorts,
                'user_kpis' => [
                    'avg_sessions_per_user' => round($avgSessionsPerUser, 2),
                    'avg_bet_amount_per_user' => round($avgBetAmountPerUser, 2),
                    'avg_deposit_amount_per_user' => round($avgDepositAmountPerUser, 2),
                ],
                'top_users' => [
                    'by_bets' => $topUsersByBets,
                    'by_deposits' => $topUsersByDeposits,
                ],
            ],
        ]);
    }

    /**
     * Get game statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function games(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:day,week,month,year,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'game_type' => 'nullable|string',
        ]);
        
        $period = $request->period ?? 'month';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : $this->getStartDateFromPeriod($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
        $gameType = $request->game_type;
        
        // Base query
        $query = GameSession::whereBetween('created_at', [$startDate, $endDate]);
        
        // Filter by game type if provided
        if ($gameType) {
            $query->where('game_type', $gameType);
        }
        
        // Get game popularity
        $gamePopularity = $query->clone()
            ->select('game_type', DB::raw('COUNT(*) as sessions'))
            ->groupBy('game_type')
            ->orderByDesc('sessions')
            ->get();
            
        // Get game revenue
        $gameRevenue = Bet::whereNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->when($gameType, function ($query) use ($gameType) {
                $query->whereHas('gameSession', function ($query) use ($gameType) {
                    $query->where('game_type', $gameType);
                });
            })
            ->select(
                'game_session_id',
                DB::raw('SUM(amount) as bets'),
                DB::raw('SUM(CASE WHEN status = "won" THEN potential_payout ELSE 0 END) as wins')
            )
            ->groupBy('game_session_id')
            ->get()
            ->map(function ($item) {
                $item->revenue = $item->bets - $item->wins;
                return $item;
            });
            
        // Calculate game metrics
        $gameMetrics = GameSession::whereBetween('created_at', [$startDate, $endDate])
            ->when($gameType, function ($query) use ($gameType) {
                $query->where('game_type', $gameType);
            })
            ->select('game_type')
            ->selectRaw('COUNT(DISTINCT user_id) as unique_players')
            ->selectRaw('COUNT(*) as sessions')
            ->groupBy('game_type')
            ->get()
            ->map(function ($item) use ($gameRevenue) {
                $gameSessions = $gameRevenue->whereIn('game_session_id', 
                    GameSession::where('game_type', $item->game_type)->pluck('id')
                );
                
                $bets = $gameSessions->sum('bets');
                $wins = $gameSessions->sum('wins');
                $revenue = $bets - $wins;
                
                return [
                    'game_type' => $item->game_type,
                    'unique_players' => $item->unique_players,
                    'sessions' => $item->sessions,
                    'bets' => $bets,
                    'wins' => $wins,
                    'revenue' => $revenue,
                    'rtp_percentage' => $bets > 0 ? round(($wins / $bets) * 100, 2) : 0,
                    'average_bet' => $bets > 0 ? round($bets / $item->sessions, 2) : 0,
                    'revenue_per_player' => $item->unique_players > 0 ? round($revenue / $item->unique_players, 2) : 0,
                ];
            });
            
        return response()->json([
            'status' => 'success',
            'data' => [
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'name' => $period,
                ],
                'game_popularity' => $gamePopularity,
                'game_metrics' => $gameMetrics,
                'total_sessions' => $gamePopularity->sum('sessions'),
                'total_revenue' => $gameRevenue->sum('revenue'),
                'average_rtp' => $gameMetrics->avg('rtp_percentage'),
            ],
        ]);
    }

    /**
     * Get sports betting statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function sports(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:day,week,month,year,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'sport_id' => 'nullable|exists:sports,id',
        ]);
        
        $period = $request->period ?? 'month';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : $this->getStartDateFromPeriod($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
        $sportId = $request->sport_id;
        
        // Base query
        $query = Bet::whereNotNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->when($sportId, function ($query) use ($sportId) {
                $query->whereHas('match', function ($query) use ($sportId) {
                    $query->where('sport_id', $sportId);
                });
            });
            
        // Get sports popularity
        $sportsPopularity = Bet::whereNotNull('match_id')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->join('matches', 'bets.match_id', '=', 'matches.id')
            ->join('sports', 'matches.sport_id', '=', 'sports.id')
            ->select('sports.id', 'sports.name', DB::raw('COUNT(*) as bets'))
            ->groupBy('sports.id', 'sports.name')
            ->orderByDesc('bets')
            ->get();
            
        // Get sports revenue
        $sportsRevenue = $query->clone()
            ->join('matches', 'bets.match_id', '=', 'matches.id')
            ->join('sports', 'matches.sport_id', '=', 'sports.id')
            ->select(
                'sports.id',
                'sports.name',
                DB::raw('SUM(bets.amount) as bets'),
                DB::raw('SUM(CASE WHEN bets.status = "won" THEN bets.potential_payout ELSE 0 END) as wins')
            )
            ->groupBy('sports.id', 'sports.name')
            ->get()
            ->map(function ($item) {
                $item->revenue = $item->bets - $item->wins;
                return $item;
            });
            
        // Get bet types popularity
        $betTypesPopularity = $query->clone()
            ->select('type', DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->orderByDesc('count')
            ->get();
            
        // Get popular matches
        $popularMatches = $query->clone()
            ->join('matches', 'bets.match_id', '=', 'matches.id')
            ->select(
                'matches.id',
                'matches.home_team',
                'matches.away_team',
                DB::raw('COUNT(*) as bets_count'),
                DB::raw('SUM(bets.amount) as total_bet_amount')
            )
            ->groupBy('matches.id', 'matches.home_team', 'matches.away_team')
            ->orderByDesc('bets_count')
            ->take(10)
            ->get();
            
        return response()->json([
            'status' => 'success',
            'data' => [
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'name' => $period,
                ],
                'sports_popularity' => $sportsPopularity,
                'sports_revenue' => $sportsRevenue,
                'bet_types_popularity' => $betTypesPopularity,
                'popular_matches' => $popularMatches,
                'total_bets' => $query->count(),
                'total_bet_amount' => $query->sum('amount'),
                'total_revenue' => $sportsRevenue->sum('revenue'),
            ],
        ]);
    }

    /**
     * Get fraud analysis statistics.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function fraudAnalysis(Request $request)
    {
        $request->validate([
            'period' => 'nullable|in:day,week,month,year,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);
        
        $period = $request->period ?? 'month';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : $this->getStartDateFromPeriod($period);
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
        
        // Get suspicious transactions
        $suspiciousTransactions = DB::table('suspicious_transactions')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();
            
        // Get suspicious activity by type
        $suspiciousActivitiesByType = DB::table('suspicious_activities')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select('activity_type', DB::raw('COUNT(*) as count'))
            ->groupBy('activity_type')
            ->orderByDesc('count')
            ->get();
            
        // Get AML check results
        $amlChecks = AmlCheck::whereBetween('created_at', [$startDate, $endDate])
            ->select('check_type', 'result', DB::raw('COUNT(*) as count'))
            ->groupBy('check_type', 'result')
            ->get()
            ->groupBy('check_type')
            ->map(function ($items) {
                $results = [];
                foreach ($items as $item) {
                    $results[$item->result] = $item->count;
                }
                return $results;
            });
            
        // Get users with multiple accounts (potential fraud)
        $multipleAccountIps = DB::table('user_sessions')
            ->select('ip_address', DB::raw('COUNT(DISTINCT user_id) as user_count'))
            ->groupBy('ip_address')
            ->having('user_count', '>', 3)
            ->orderByDesc('user_count')
            ->take(10)
            ->get();
            
        return response()->json([
            'status' => 'success',
            'data' => [
                'period' => [
                    'start_date' => $startDate->format('Y-m-d'),
                    'end_date' => $endDate->format('Y-m-d'),
                    'name' => $period,
                ],
                'suspicious_transactions' => $suspiciousTransactions,
                'suspicious_activities_by_type' => $suspiciousActivitiesByType,
                'aml_checks' => $amlChecks,
                'multiple_account_ips' => $multipleAccountIps,
            ],
        ]);
    }

    /**
     * Get the start date based on the specified period.
     *
     * @param string $period
     * @return \Carbon\Carbon
     */
    private function getStartDateFromPeriod($period)
    {
        switch ($period) {
            case 'day':
                return Carbon::now()->startOfDay();
            case 'week':
                return Carbon::now()->subWeek()->startOfDay();
            case 'month':
                return Carbon::now()->subMonth()->startOfDay();
            case 'year':
                return Carbon::now()->subYear()->startOfDay();
            case 'all':
                return Carbon::createFromTimestamp(0);
            default:
                return Carbon::now()->subMonth()->startOfDay();
        }
    }

    /**
     * Get the date format for SQL grouping.
     *
     * @param string $groupBy
     * @return string
     */
    private function getDateFormatForGrouping($groupBy)
    {
        switch ($groupBy) {
            case 'day':
                return '%Y-%m-%d';
            case 'week':
                return '%x-W%v';
            case 'month':
                return '%Y-%m';
            default:
                return '%Y-%m-%d';
        }
    }

    /**
     * Calculate the growth rate between two values.
     *
     * @param float $old
     * @param float $new
     * @return float
     */
    private function calculateGrowthRate($old, $new)
    {
        if ($old == 0) {
            return $new > 0 ? 100 : 0;
        }
        
        return round((($new - $old) / $old) * 100, 2);
    }

    /**
     * Get the revenue from the previous period.
     *
     * @param \Carbon\Carbon $startDate
     * @param \Carbon\Carbon $endDate
     * @return float
     */
    private function getPreviousPeriodRevenue($startDate, $endDate)
    {
        $periodDays = $endDate->diffInDays($startDate);
        $previousPeriodStart = $startDate->copy()->subDays($periodDays);
        $previousPeriodEnd = $startDate->copy()->subDay();
        
        $totalBets = Bet::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->sum('amount');
            
        $totalWins = Transaction::where('type', 'win')
            ->whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])
            ->sum('amount');
            
        return $totalBets - $totalWins;
    }
}