<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SystemController extends Controller
{
    /**
     * Get system status.
     *
     * @return \Illuminate\Http\Response
     */
    public function status()
    {
        // Check database connectivity
        $dbStatus = true;
        try {
            DB::connection()->getPdo();
        } catch (\Exception $e) {
            $dbStatus = false;
            Log::error('Database connection error: ' . $e->getMessage());
        }
        
        // Check Redis connectivity
        $redisStatus = true;
        try {
            Redis::ping();
        } catch (\Exception $e) {
            $redisStatus = false;
            Log::error('Redis connection error: ' . $e->getMessage());
        }
        
        // Check storage access
        $storageStatus = true;
        try {
            Storage::disk('local')->put('test.txt', 'test');
            Storage::disk('local')->delete('test.txt');
        } catch (\Exception $e) {
            $storageStatus = false;
            Log::error('Storage access error: ' . $e->getMessage());
        }
        
        // Get disk usage
        $diskTotal = disk_total_space(storage_path());
        $diskFree = disk_free_space(storage_path());
        $diskUsed = $diskTotal - $diskFree;
        $diskPercentage = round(($diskUsed / $diskTotal) * 100, 2);
        
        // Get memory usage
        $memoryLimit = ini_get('memory_limit');
        $memoryUsage = memory_get_usage(true);
        
        // Get system load
        $load = sys_getloadavg();
        
        // Get server info
        $serverInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'os' => PHP_OS,
        ];
        
        // Get queue status
        $pendingJobs = 0;
        try {
            // This is simplified and would need to be adjusted based on your queue configuration
            $pendingJobs = DB::table('jobs')->count();
        } catch (\Exception $e) {
            Log::error('Queue check error: ' . $e->getMessage());
        }
        
        // Get cache stats
        $cacheStats = [
            'driver' => config('cache.default'),
        ];
        
        // Get scheduled tasks
        $scheduledTasks = [
            'rng_validation' => '0 0 * * *', // Daily at midnight
            'clean_expired_sessions' => '0 */2 * * *', // Every 2 hours
            'sync_sports_data' => '*/10 * * * *', // Every 10 minutes
            'process_pending_withdrawals' => '*/15 * * * *', // Every 15 minutes
            'calculate_daily_metrics' => '5 0 * * *', // 5 minutes after midnight
            'update_user_loyalty_points' => '10 0 * * *', // 10 minutes after midnight
        ];
        
        // Return the system status
        return response()->json([
            'status' => 'success',
            'data' => [
                'services' => [
                    'database' => $dbStatus,
                    'redis' => $redisStatus,
                    'storage' => $storageStatus,
                    'websockets' => true, // This would need a real check in production
                ],
                'resources' => [
                    'disk' => [
                        'total' => $diskTotal,
                        'free' => $diskFree,
                        'used' => $diskUsed,
                        'percentage' => $diskPercentage,
                    ],
                    'memory' => [
                        'limit' => $memoryLimit,
                        'usage' => $memoryUsage,
                    ],
                    'load' => $load,
                ],
                'server' => $serverInfo,
                'queue' => [
                    'pending_jobs' => $pendingJobs,
                ],
                'cache' => $cacheStats,
                'scheduled_tasks' => $scheduledTasks,
                'timestamp' => now(),
            ],
        ]);
    }

    /**
     * Get system logs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logs(Request $request)
    {
        $request->validate([
            'type' => 'nullable|in:error,access,all',
            'date' => 'nullable|date_format:Y-m-d',
            'lines' => 'nullable|integer|min:10|max:1000',
        ]);
        
        $type = $request->type ?? 'error';
        $date = $request->date ?? now()->format('Y-m-d');
        $lines = $request->lines ?? 100;
        
        $logFile = $type === 'access' ? 'laravel-access.log' : 'laravel.log';
        
        if ($type === 'all') {
            $logFile = 'laravel.log';
        }
        
        // Check if date-specific log exists
        $dateSpecificLog = str_replace('.log', "-{$date}.log", $logFile);
        $logPath = storage_path("logs/{$dateSpecificLog}");
        
        if (!file_exists($logPath)) {
            $logPath = storage_path("logs/{$logFile}");
        }
        
        if (!file_exists($logPath)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Log file not found',
            ], 404);
        }
        
        // Get the last N lines of the log file
        $logContent = $this->tailFile($logPath, $lines);
        
        return response()->json([
            'status' => 'success',
            'data' => [
                'type' => $type,
                'date' => $date,
                'lines' => $lines,
                'content' => $logContent,
            ],
        ]);
    }

    /**
     * Toggle maintenance mode.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function maintenance(Request $request)
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'secret' => 'nullable|string',
            'message' => 'nullable|string',
        ]);
        
        if ($request->enabled) {
            // Enable maintenance mode
            $command = 'down';
            $params = [];
            
            if ($request->has('secret')) {
                $params['--secret'] = $request->secret;
            }
            
            if ($request->has('message')) {
                $params['--message'] = $request->message;
            }
            
            Artisan::call($command, $params);
            
            return response()->json([
                'status' => 'success',
                'message' => 'Maintenance mode enabled',
                'secret_path' => $request->has('secret') ? '/'. $request->secret : null,
            ]);
        } else {
            // Disable maintenance mode
            Artisan::call('up');
            
            return response()->json([
                'status' => 'success',
                'message' => 'Maintenance mode disabled',
            ]);
        }
    }

    /**
     * Clear various caches.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function clearCache(Request $request)
    {
        $request->validate([
            'type' => 'required|in:all,config,view,route,application,event,sessions',
        ]);
        
        $type = $request->type;
        $cleared = [];
        
        if ($type === 'all' || $type === 'application') {
            Artisan::call('cache:clear');
            $cleared[] = 'application cache';
        }
        
        if ($type === 'all' || $type === 'config') {
            Artisan::call('config:clear');
            $cleared[] = 'configuration cache';
        }
        
        if ($type === 'all' || $type === 'view') {
            Artisan::call('view:clear');
            $cleared[] = 'compiled views';
        }
        
        if ($type === 'all' || $type === 'route') {
            Artisan::call('route:clear');
            $cleared[] = 'route cache';
        }
        
        if ($type === 'all' || $type === 'event') {
            Artisan::call('event:clear');
            $cleared[] = 'event cache';
        }
        
        if ($type === 'all' || $type === 'sessions') {
            // This is a custom method to clear sessions
            // You would need to implement the actual session clearing logic
            // based on your session storage configuration
            DB::table('sessions')->truncate();
            $cleared[] = 'user sessions';
        }
        
        return response()->json([
            'status' => 'success',
            'message' => 'Successfully cleared ' . implode(', ', $cleared),
        ]);
    }

    /**
     * Run database migrations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function runMigrations(Request $request)
    {
        $request->validate([
            'force' => 'nullable|boolean',
            'seed' => 'nullable|boolean',
        ]);
        
        $params = [];
        
        if ($request->force) {
            $params['--force'] = true;
        }
        
        try {
            Artisan::call('migrate', $params);
            $migrationOutput = Artisan::output();
            
            if ($request->seed) {
                Artisan::call('db:seed', $params);
                $seedOutput = Artisan::output();
            }
            
            return response()->json([
                'status' => 'success',
                'message' => 'Migrations completed successfully',
                'migration_output' => $migrationOutput,
                'seed_output' => $request->seed ? $seedOutput : null,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Migration failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get the last N lines of a file.
     *
     * @param string $filePath
     * @param int $lines
     * @return string
     */
    private function tailFile($filePath, $lines)
    {
        $file = new \SplFileObject($filePath, 'r');
        $file->seek(PHP_INT_MAX);
        $lastLine = $file->key();
        
        $output = [];
        $startLine = max(0, $lastLine - $lines);
        
        for ($i = $startLine; $i <= $lastLine; $i++) {
            $file->seek($i);
            $output[] = $file->current();
        }
        
        return implode('', $output);
    }
}