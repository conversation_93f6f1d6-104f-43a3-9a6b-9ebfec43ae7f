<?php

namespace App\Http\Middleware;

use Illuminate\Routing\Pipeline;
use Illuminate\Support\Str;
use <PERSON>vel\Sanctum\Sanctum;

class EnsureFrontendRequestsAreStateful
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  callable  $next
     * @return \Illuminate\Http\Response
     */
    public function handle($request, $next)
    {
        $this->configureSecureCookies($request);

        return (new Pipeline(app()))
                ->send($request)
                ->through(static::fromFrontend($request) ? [
                    config('sanctum.middleware.encrypt_cookies', \App\Http\Middleware\EncryptCookies::class),
                    \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
                    \Illuminate\Session\Middleware\StartSession::class,
                    config('sanctum.middleware.verify_csrf_token', \App\Http\Middleware\VerifyCsrfToken::class),
                ] : [])
                ->then(function ($request) use ($next) {
                    return $next($request);
                });
    }

    /**
     * Configure secure cookies.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function configureSecureCookies($request)
    {
        if (! $request->secure()) {
            return;
        }

        config([
            'session.secure' => true,
            'session.same_site' => 'none',
        ]);
    }

    /**
     * Determine if the given request is from the first-party application frontend.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    public static function fromFrontend($request)
    {
        $domain = $request->headers->get('referer') ?: $request->headers->get('origin');

        if (is_null($domain)) {
            return false;
        }

        $domain = parse_url($domain, PHP_URL_HOST);

        $stateful = array_filter(config('sanctum.stateful', []));

        return Str::endsWith($domain, $stateful) ||
               in_array($domain, $stateful);
    }
}