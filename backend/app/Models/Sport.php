<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sport extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'external_id',
        'category',
        'has_live_betting',
        'is_active',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'has_live_betting' => 'boolean',
        'is_active' => 'boolean',
        'meta' => 'json',
    ];

    /**
     * Get the matches for the sport.
     */
    public function matches()
    {
        return $this->hasMany(Match::class);
    }

    /**
     * Get active matches for the sport.
     */
    public function activeMatches()
    {
        return $this->hasMany(Match::class)->whereIn('status', ['upcoming', 'live']);
    }

    /**
     * Get upcoming matches for the sport.
     */
    public function upcomingMatches()
    {
        return $this->hasMany(Match::class)->where('status', 'upcoming');
    }

    /**
     * Get live matches for the sport.
     */
    public function liveMatches()
    {
        return $this->hasMany(Match::class)->where('status', 'live');
    }
}