<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bet extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'game_session_id',
        'amount',
        'type',
        'odds',
        'potential_payout',
        'status',
        'result',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'odds' => 'decimal:2',
        'potential_payout' => 'decimal:2',
        'meta' => 'json',
    ];

    /**
     * Get the user that owns the bet.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the game session that the bet belongs to.
     */
    public function gameSession()
    {
        return $this->belongsTo(GameSession::class);
    }

    /**
     * Bet statuses
     */
    const STATUS_PENDING = 'pending';
    const STATUS_WON = 'won';
    const STATUS_LOST = 'lost';
    const STATUS_PUSH = 'push';
    const STATUS_CANCELED = 'canceled';

    /**
     * Bet types
     */
    const TYPE_SINGLE = 'single';
    const TYPE_PARLAY = 'parlay';
}