<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'username',
        'balance',
        'avatar',
        'is_verified',
        'last_login',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'balance' => 'decimal:2',
        'last_login' => 'datetime',
    ];

    /**
     * Get the transactions for the user.
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the bets for the user.
     */
    public function bets()
    {
        return $this->hasMany(Bet::class);
    }

    /**
     * Get the game sessions for the user.
     */
    public function gameSessions()
    {
        return $this->hasMany(GameSession::class);
    }

    /**
     * Check if user has sufficient balance for a transaction.
     *
     * @param float $amount
     * @return bool
     */
    public function hasSufficientBalance($amount)
    {
        return $this->balance >= $amount;
    }

    /**
     * Update user balance.
     *
     * @param float $amount
     * @param string $type
     * @return bool
     */
    public function updateBalance($amount, $type = 'credit')
    {
        if ($type === 'debit' && !$this->hasSufficientBalance(abs($amount))) {
            return false;
        }

        $this->balance = $type === 'credit' 
            ? $this->balance + $amount 
            : $this->balance - abs($amount);
            
        return $this->save();
    }
}