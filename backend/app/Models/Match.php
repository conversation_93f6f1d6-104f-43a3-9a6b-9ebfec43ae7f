<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Match extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sport_id',
        'external_id',
        'home_team',
        'away_team',
        'commence_time',
        'status',
        'scores',
        'home_odds',
        'draw_odds',
        'away_odds',
        'additional_markets',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'commence_time' => 'datetime',
        'scores' => 'json',
        'home_odds' => 'decimal:2',
        'draw_odds' => 'decimal:2',
        'away_odds' => 'decimal:2',
        'additional_markets' => 'json',
        'meta' => 'json',
    ];

    /**
     * Get the sport that the match belongs to.
     */
    public function sport()
    {
        return $this->belongsTo(Sport::class);
    }

    /**
     * Get the bets for the match.
     */
    public function bets()
    {
        return $this->hasMany(Bet::class);
    }

    /**
     * Match statuses
     */
    const STATUS_UPCOMING = 'upcoming';
    const STATUS_LIVE = 'live';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Check if match is live
     */
    public function isLive()
    {
        return $this->status === self::STATUS_LIVE;
    }

    /**
     * Check if match is upcoming
     */
    public function isUpcoming()
    {
        return $this->status === self::STATUS_UPCOMING;
    }

    /**
     * Check if match is completed
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if match accepts bets
     */
    public function acceptsBets()
    {
        return in_array($this->status, [self::STATUS_UPCOMING, self::STATUS_LIVE]);
    }
}