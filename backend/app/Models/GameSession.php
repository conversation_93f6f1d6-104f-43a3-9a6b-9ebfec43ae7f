<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameSession extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'game_type',
        'server_seed',
        'server_seed_hash',
        'client_seed',
        'nonce',
        'status',
        'result',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'result' => 'json',
        'meta' => 'json',
    ];

    /**
     * Get the user that owns the game session.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the bets for the game session.
     */
    public function bets()
    {
        return $this->hasMany(Bet::class);
    }

    /**
     * Game session statuses
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELED = 'canceled';

    /**
     * Game types
     */
    const GAME_BLACKJACK = 'blackjack';
    const GAME_BACCARAT = 'baccarat';
    const GAME_DICE = 'dice';
    const GAME_MINES = 'mines';
    const GAME_CRASH = 'crash';
    const GAME_PLINKO = 'plinko';
    const GAME_ROULETTE = 'roulette';
    const GAME_HILO = 'hilo';
    const GAME_WHEEL = 'wheel';
}