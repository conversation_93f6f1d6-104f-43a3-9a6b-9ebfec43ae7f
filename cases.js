// Mobile-First Responsive Cases Game with Pro View
class CasesGame {
    constructor() {
        this.gameState = {
            // Game status
            isPlaying: false,
            score: 0,
            round: 1,
            timeLeft: 90,
            streak: 0,
            
            // Game mechanics
            cases: [],
            openedCases: 0,
            puzzlePieces: [false, false, false],
            lockedValues: false,
            lockEndTime: 0,
            
            // Power-ups
            powerUps: {
                spotlight: { available: true, cooldown: 0 },
                lockdown: { available: true, cooldown: 0 },
                timeCapsule: { available: true, cooldown: 0 }
            },
            
            // Currency system
            balance: 1000,
            betAmount: 50,
            winnings: 0,
            
            // Pro view mode
            viewMode: 'standard', // 'standard' or 'pro'
            
            // Advanced statistics (Pro View)
            totalGamesPlayed: 0,
            totalWinnings: 0,
            totalLosses: 0,
            averageScore: 0,
            bestStreak: 0,
            puzzlesCompleted: 0,
            powerUpsUsed: 0,
            
            // Game history
            gameHistory: []
        };

        // Game timers
        this.gameTimer = null;
        this.powerUpTimers = {};

        // Case types and their properties
        this.CASE_TYPES = {
            POINTS: { icon: '💰', baseValue: 100, color: '#FFD700' },
            MULTIPLIER: { icon: '✖️', color: '#32CD32' },
            POWERUP: { icon: '⚡', value: 'powerup', color: '#1E90FF' },
            PUZZLE: { icon: '🧩', value: 'puzzle', color: '#FF69B4' },
            EMPTY: { icon: '❌', value: 0, color: '#708090' }
        };

        this.init();
    }

    init() {
        // Load saved data
        this.loadGameData();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Create view mode toggle
        this.createViewModeToggle();
        
        // Update display
        this.updateDisplay();
        this.updateTimerDisplay();
        this.updatePowerUpDisplay();
        
        // Set initial view mode
        this.setViewMode(this.gameState.viewMode);
        
        // Mobile-specific setup
        this.setupMobileFeatures();
        
        // Setup advanced analytics for pro view
        this.setupAdvancedAnalytics();
    }

    setupEventListeners() {
        // Bet adjustment buttons
        document.querySelectorAll('[onclick*="adjustBet"]').forEach(btn => {
            const match = btn.getAttribute('onclick').match(/adjustBet\(([^)]+)\)/);
            if (match) {
                const amount = parseInt(match[1]);
                btn.removeAttribute('onclick');
                btn.addEventListener('click', () => this.adjustBet(amount));
            }
        });

        // Game control buttons
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            startBtn.removeAttribute('onclick');
            startBtn.addEventListener('click', () => this.startGame());
        }

        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.removeAttribute('onclick');
            resetBtn.addEventListener('click', () => this.resetGame());
        }

        // Power-up buttons
        ['spotlight', 'lockdown', 'timeCapsule'].forEach(powerUp => {
            const btn = document.getElementById(`${powerUp}Btn`);
            if (btn) {
                btn.removeAttribute('onclick');
                btn.addEventListener('click', () => this.usePowerUp(powerUp));
            }
        });

        // Touch and gesture support
        this.setupTouchSupport();
    }

    setupTouchSupport() {
        // Add touch feedback for all interactive elements
        const interactiveElements = document.querySelectorAll(
            '.case, .power-up, .btn, .bet-btn, .view-toggle-btn'
        );

        interactiveElements.forEach(element => {
            // Add touch start/end effects
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.95)';
                element.style.transition = 'transform 0.1s ease';
                this.vibrate([30]);
            });

            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = '';
                }, 100);
            });

            element.addEventListener('touchcancel', (e) => {
                element.style.transform = '';
                element.style.transition = '';
            });
        });

        // Prevent double-tap zoom on game elements
        document.querySelectorAll('.cases-grid, .game-dashboard, .game-controls').forEach(element => {
            element.addEventListener('touchend', (e) => {
                e.preventDefault();
            });
        });
    }

    setupMobileFeatures() {
        // Mobile-specific optimizations
        if (this.isMobile()) {
            // Adjust case grid for very small screens
            if (window.innerWidth <= 480) {
                const casesGrid = document.querySelector('.cases-grid');
                if (casesGrid) {
                    casesGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';
                    casesGrid.style.gap = '0.4rem';
                }
            }

            // Add haptic feedback if available
            this.setupHapticFeedback();
        }
    }

    setupHapticFeedback() {
        // Vibration API for mobile devices
        if ('vibrate' in navigator) {
            this.vibrate = (pattern) => {
                navigator.vibrate(pattern);
            };
        } else {
            this.vibrate = () => {}; // No-op for devices without vibration
        }
    }

    setupAdvancedAnalytics() {
        // Create advanced analytics panels for pro view
        this.createAdvancedStatsPanel();
        this.createStrategyHintsPanel();
    }

    isMobile() {
        return window.innerWidth <= 768 || 'ontouchstart' in window;
    }

    createViewModeToggle() {
        let toggle = document.querySelector('.view-mode-toggle');
        if (!toggle) {
            toggle = document.createElement('div');
            toggle.className = 'view-mode-toggle';
            toggle.innerHTML = `
                <button class="view-toggle-btn ${this.gameState.viewMode === 'standard' ? 'active' : ''}" data-mode="standard">
                    Standard
                </button>
                <button class="view-toggle-btn ${this.gameState.viewMode === 'pro' ? 'active' : ''}" data-mode="pro">
                    Pro View
                </button>
            `;
            document.body.appendChild(toggle);

            // Add event listeners
            toggle.querySelectorAll('.view-toggle-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const mode = e.target.getAttribute('data-mode');
                    this.setViewMode(mode);
                });
            });
        }
    }

    setViewMode(mode) {
        this.gameState.viewMode = mode;
        
        // Update toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.getAttribute('data-mode') === mode) {
                btn.classList.add('active');
            }
        });

        // Update body class
        document.body.classList.remove('standard-view', 'pro-view-active');
        if (mode === 'pro') {
            document.body.classList.add('pro-view-active');
        } else {
            document.body.classList.add('standard-view');
        }

        // Save preference
        localStorage.setItem('casesViewMode', mode);
        
        // Show notification
        this.showNotification(`Switched to ${mode === 'pro' ? 'Pro' : 'Standard'} view`);
        
        // Update analytics if in pro mode
        if (mode === 'pro') {
            this.updateAdvancedStats();
        }
    }

    createAdvancedStatsPanel() {
        // Check if panel already exists
        if (document.querySelector('.advanced-stats')) return;

        const statsPanel = document.createElement('div');
        statsPanel.className = 'advanced-stats';
        statsPanel.innerHTML = `
            <div class="panel-title">
                <i class="fas fa-chart-line"></i>
                Advanced Analytics
            </div>
            <div class="stat-grid">
                <div class="stat-card">
                    <div class="stat-label">Total Games</div>
                    <div class="stat-value" id="totalGamesPlayed">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Total Winnings</div>
                    <div class="stat-value" id="totalWinnings">0 GA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Win Rate</div>
                    <div class="stat-value" id="winRate">0%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Average Score</div>
                    <div class="stat-value" id="averageScore">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Best Streak</div>
                    <div class="stat-value" id="bestStreak">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Puzzles Completed</div>
                    <div class="stat-value" id="puzzlesCompleted">0</div>
                </div>
            </div>
        `;

        // Insert after stats panel
        const existingStatsPanel = document.querySelector('.stats-panel');
        if (existingStatsPanel && existingStatsPanel.parentNode) {
            existingStatsPanel.parentNode.insertBefore(statsPanel, existingStatsPanel.nextSibling);
        } else {
            // Fallback: append to dashboard
            const dashboard = document.querySelector('.game-dashboard');
            if (dashboard) {
                dashboard.appendChild(statsPanel);
            }
        }
    }

    createStrategyHintsPanel() {
        // Check if panel already exists
        if (document.querySelector('.strategy-hints')) return;

        const hintsPanel = document.createElement('div');
        hintsPanel.className = 'strategy-hints';
        hintsPanel.innerHTML = `
            <div class="panel-title">
                <i class="fas fa-lightbulb"></i>
                Strategy Hints
            </div>
            <div id="strategyHintsList">
                <div class="hint-item">💡 Use Spotlight when you have few cases left</div>
                <div class="hint-item">🔒 Lockdown is powerful when you find good values</div>
                <div class="hint-item">⏰ Save Time Capsule for challenging rounds</div>
                <div class="hint-item">🧩 Puzzle pieces give huge bonuses - prioritize them!</div>
            </div>
        `;

        // Insert after advanced stats or stats panel
        const advancedStats = document.querySelector('.advanced-stats');
        const statsPanel = document.querySelector('.stats-panel');
        const insertAfter = advancedStats || statsPanel;
        
        if (insertAfter && insertAfter.parentNode) {
            insertAfter.parentNode.insertBefore(hintsPanel, insertAfter.nextSibling);
        } else {
            // Fallback: append to dashboard
            const dashboard = document.querySelector('.game-dashboard');
            if (dashboard) {
                dashboard.appendChild(hintsPanel);
            }
        }
    }

    generateCases() {
        const cases = [];
        
        // Add reward cases (4 total)
        cases.push(
            { type: 'POINTS', value: 100 },
            { type: 'MULTIPLIER', value: 1.5 + Math.random() * 8.5 }, // 1.5x to 10x
            { type: 'POWERUP', value: 'random' },
            { type: 'PUZZLE', value: Math.floor(Math.random() * 3) + 1 } // Piece 1, 2, or 3
        );
        
        // Add empty cases (16 total)
        for (let i = 0; i < 16; i++) {
            cases.push({ type: 'EMPTY', value: 0 });
        }
        
        // Shuffle cases
        return this.shuffleArray(cases);
    }

    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    adjustDifficulty() {
        const difficultyMultiplier = 1 + (this.gameState.round - 1) * 0.1;
        
        this.gameState.cases = this.gameState.cases.map(caseItem => {
            if (caseItem.type === 'MULTIPLIER') {
                return {
                    ...caseItem,
                    value: caseItem.value * difficultyMultiplier
                };
            }
            return caseItem;
        });
    }

    renderCases() {
        const grid = document.getElementById('casesGrid');
        if (!grid) return;
        
        grid.innerHTML = '';
        
        this.gameState.cases.forEach((caseItem, index) => {
            const caseElement = document.createElement('div');
            caseElement.className = 'case';
            caseElement.dataset.index = index;
            
            const content = document.createElement('div');
            content.className = 'case-content';
            
            if (caseItem.opened) {
                caseElement.classList.add('opened');
                const caseType = this.CASE_TYPES[caseItem.type];
                content.innerHTML = `
                    <div style="font-size: 1.5rem;">${caseType.icon}</div>
                    <div class="case-value">${this.formatCaseValue(caseItem)}</div>
                `;
                content.style.opacity = '1';
                caseElement.style.background = `linear-gradient(135deg, ${caseType.color}, ${caseType.color}aa)`;
            }
            
            caseElement.appendChild(content);
            caseElement.addEventListener('click', () => this.selectCase(index));
            grid.appendChild(caseElement);
        });
    }

    formatCaseValue(caseItem) {
        switch (caseItem.type) {
            case 'POINTS':
                return `+${caseItem.value}pts`;
            case 'MULTIPLIER':
                return `${caseItem.value.toFixed(1)}x`;
            case 'POWERUP':
                return 'Power-Up!';
            case 'PUZZLE':
                return `Piece ${caseItem.value}`;
            case 'EMPTY':
                return 'Empty';
            default:
                return '';
        }
    }

    selectCase(index) {
        if (!this.gameState.isPlaying || this.gameState.cases[index].opened) return;
        
        const caseItem = this.gameState.cases[index];
        caseItem.opened = true;
        this.gameState.openedCases++;
        
        // Apply case effect
        this.applyCaseEffect(caseItem);
        
        // Create particle effect
        this.createParticleEffect(index);
        
        // Haptic feedback
        this.vibrate([50]);
        
        // Re-render cases
        this.renderCases();
        
        // Update display
        this.updateDisplay();
        
        // Check round completion
        if (this.gameState.openedCases >= 5 || this.gameState.timeLeft <= 0) {
            this.endRound();
        }
        
        // Reshuffle values every 3 selections (if not locked)
        if (this.gameState.openedCases % 3 === 0 && !this.gameState.lockedValues) {
            this.reshuffleValues();
        }
    }

    applyCaseEffect(caseItem) {
        switch (caseItem.type) {
            case 'POINTS':
                const streakBonus = 1 + (this.gameState.streak * 0.1);
                const pointsValue = Math.floor(caseItem.value * streakBonus);
                this.gameState.score += pointsValue;
                
                // Add winnings based on points
                const pointsWinnings = Math.floor(pointsValue * 0.1 * (this.gameState.betAmount / 50));
                this.gameState.winnings += pointsWinnings;
                this.showNotification(`+${pointsWinnings} GA from points!`);
                this.gameState.streak++;
                break;
                
            case 'MULTIPLIER':
                const prevScore = this.gameState.score;
                this.gameState.score = Math.floor(this.gameState.score * caseItem.value);
                const scoreIncrease = this.gameState.score - prevScore;
                
                // Add winnings based on multiplier
                const multiplierWinnings = Math.floor(scoreIncrease * 0.05 * (this.gameState.betAmount / 50));
                this.gameState.winnings += multiplierWinnings;
                this.showNotification(`+${multiplierWinnings} GA from multiplier!`);
                this.gameState.streak++;
                break;
                
            case 'POWERUP':
                this.grantRandomPowerUp();
                // Small bonus for finding power-up
                const powerupBonus = Math.floor(this.gameState.betAmount * 0.2);
                this.gameState.winnings += powerupBonus;
                this.showNotification(`+${powerupBonus} GA from power-up!`);
                this.gameState.streak++;
                this.gameState.powerUpsUsed++;
                break;
                
            case 'PUZZLE':
                this.collectPuzzlePiece(caseItem.value);
                // Bonus for finding puzzle piece
                const puzzleBonus = Math.floor(this.gameState.betAmount * 0.5);
                this.gameState.winnings += puzzleBonus;
                this.showNotification(`+${puzzleBonus} GA from puzzle piece!`);
                this.gameState.streak++;
                break;
                
            case 'EMPTY':
                this.gameState.streak = 0;
                this.vibrate([100, 50, 100]);
                break;
        }
        
        // Update best streak
        if (this.gameState.streak > this.gameState.bestStreak) {
            this.gameState.bestStreak = this.gameState.streak;
        }
    }

    grantRandomPowerUp() {
        const powerUps = ['spotlight', 'lockdown', 'timeCapsule'];
        const randomPowerUp = powerUps[Math.floor(Math.random() * powerUps.length)];
        this.gameState.powerUps[randomPowerUp].available = true;
        this.gameState.powerUps[randomPowerUp].cooldown = 0;
        this.updatePowerUpDisplay();
        this.vibrate([50, 50, 50]);
    }

    collectPuzzlePiece(pieceNumber) {
        this.gameState.puzzlePieces[pieceNumber - 1] = true;
        const pieceElement = document.getElementById(`piece${pieceNumber}`);
        if (pieceElement) {
            pieceElement.classList.add('collected');
        }
        
        // Check if all pieces collected
        if (this.gameState.puzzlePieces.every(piece => piece)) {
            // Score bonus
            this.gameState.score += this.gameState.score * 4; // 5x total (current + 4x bonus)
            
            // Huge winnings bonus for completing the puzzle
            const puzzleCompleteBonus = this.gameState.betAmount * 5;
            this.gameState.winnings += puzzleCompleteBonus;
            this.gameState.puzzlesCompleted++;
            
            this.showNotification(`🎉 Puzzle Complete! 5x Score Bonus! +${puzzleCompleteBonus} GA!`);
            this.vibrate([200, 100, 200, 100, 200]);
            
            // Reset puzzle for next opportunity
            this.gameState.puzzlePieces = [false, false, false];
            document.querySelectorAll('.puzzle-piece').forEach(piece => {
                piece.classList.remove('collected');
            });
        }
    }

    createParticleEffect(caseIndex) {
        const caseElement = document.querySelector(`[data-index="${caseIndex}"]`);
        if (!caseElement) return;
        
        const rect = caseElement.getBoundingClientRect();
        
        for (let i = 0; i < 5; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle-effect';
            particle.textContent = ['✨', '💫', '🌟', '⭐', '💎'][i];
            particle.style.left = rect.left + rect.width / 2 + (Math.random() - 0.5) * 50 + 'px';
            particle.style.top = rect.top + rect.height / 2 + 'px';
            document.body.appendChild(particle);
            
            setTimeout(() => particle.remove(), 2000);
        }
    }

    usePowerUp(type) {
        if (!this.gameState.isPlaying || !this.gameState.powerUps[type].available || this.gameState.powerUps[type].cooldown > 0) {
            return;
        }
        
        switch (type) {
            case 'spotlight':
                this.revealRandomCases(3);
                this.gameState.powerUps[type].cooldown = 30;
                this.vibrate([100, 50, 100]);
                break;
                
            case 'lockdown':
                this.gameState.lockedValues = true;
                this.gameState.lockEndTime = Date.now() + 10000;
                this.showNotification('🔒 Values locked for 10 seconds!');
                setTimeout(() => {
                    this.gameState.lockedValues = false;
                }, 10000);
                this.gameState.powerUps[type].cooldown = 45;
                this.vibrate([200]);
                break;
                
            case 'timeCapsule':
                this.gameState.timeLeft += 15;
                this.showNotification('⏰ +15 seconds added!');
                this.gameState.powerUps[type].cooldown = 60;
                this.vibrate([50, 100, 50]);
                break;
        }
        
        this.gameState.powerUps[type].available = false;
        this.startPowerUpCooldown(type);
        this.updatePowerUpDisplay();
        this.gameState.powerUpsUsed++;
    }

    revealRandomCases(count) {
        const unopenedCases = this.gameState.cases
            .map((caseItem, index) => ({ caseItem, index }))
            .filter(({ caseItem }) => !caseItem.opened);
        
        const shuffled = this.shuffleArray(unopenedCases);
        const toReveal = shuffled.slice(0, count);
        
        toReveal.forEach(({ index }) => {
            const caseElement = document.querySelector(`[data-index="${index}"]`);
            if (!caseElement) return;
            
            caseElement.classList.add('spotlight');
            
            // Show brief preview of contents
            const cardContent = caseElement.querySelector('.case-content');
            const caseItem = this.gameState.cases[index];
            const caseType = this.CASE_TYPES[caseItem.type];
            
            cardContent.innerHTML = `
                <div style="font-size: 1.5rem;">${caseType.icon}</div>
                <div class="case-value">${this.formatCaseValue(caseItem)}</div>
            `;
            cardContent.style.opacity = '1';
            
            setTimeout(() => {
                caseElement.classList.remove('spotlight');
                cardContent.style.opacity = '0';
            }, 2000);
        });
    }

    startPowerUpCooldown(type) {
        const cooldownElement = document.getElementById(`${type}Cooldown`);
        const button = document.getElementById(`${type}Btn`);
        
        if (!cooldownElement || !button) return;
        
        button.disabled = true;
        
        const cooldownTime = this.gameState.powerUps[type].cooldown;
        let timeLeft = cooldownTime;
        
        this.powerUpTimers[type] = setInterval(() => {
            timeLeft--;
            const percentage = ((cooldownTime - timeLeft) / cooldownTime) * 100;
            cooldownElement.style.width = percentage + '%';
            
            if (timeLeft <= 0) {
                clearInterval(this.powerUpTimers[type]);
                this.gameState.powerUps[type].available = true;
                this.gameState.powerUps[type].cooldown = 0;
                button.disabled = false;
                cooldownElement.style.width = '0%';
            }
        }, 1000);
    }

    updatePowerUpDisplay() {
        Object.keys(this.gameState.powerUps).forEach(type => {
            const button = document.getElementById(`${type}Btn`);
            const powerUp = this.gameState.powerUps[type];
            
            if (button) {
                button.disabled = !powerUp.available || powerUp.cooldown > 0 || !this.gameState.isPlaying;
            }
        });
    }

    reshuffleValues() {
        const unopenedCases = this.gameState.cases.filter(caseItem => !caseItem.opened);
        const values = unopenedCases.map(caseItem => ({ type: caseItem.type, value: caseItem.value }));
        const shuffledValues = this.shuffleArray(values);
        
        let valueIndex = 0;
        this.gameState.cases.forEach(caseItem => {
            if (!caseItem.opened) {
                const newValue = shuffledValues[valueIndex++];
                caseItem.type = newValue.type;
                caseItem.value = newValue.value;
            }
        });
        
        this.showNotification('🔄 Case values reshuffled!');
    }

    adjustBet(amount) {
        const newBet = this.gameState.betAmount + amount;
        
        // Ensure bet is at least 10 and not more than player's balance
        if (newBet >= 10 && newBet <= this.gameState.balance) {
            this.gameState.betAmount = newBet;
            this.updateDisplay();
            this.vibrate([30]);
        } else if (newBet < 10) {
            this.showNotification('Minimum bet is 10 GA');
            this.vibrate([100, 50, 100]);
        } else {
            this.showNotification('Bet cannot exceed your balance');
            this.vibrate([100, 50, 100]);
        }
    }

    startGame() {
        // Check if player has enough balance for the bet
        if (this.gameState.balance < this.gameState.betAmount) {
            this.showNotification('Insufficient balance for this bet');
            this.vibrate([100, 50, 100]);
            return;
        }
        
        // Deduct bet amount from balance
        this.gameState.balance -= this.gameState.betAmount;
        
        // Reset winnings for this round
        this.gameState.winnings = 0;
        
        this.gameState.isPlaying = true;
        this.gameState.cases = this.generateCases();
        this.adjustDifficulty();
        this.gameState.openedCases = 0;
        
        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        
        if (startBtn) startBtn.disabled = true;
        if (resetBtn) resetBtn.disabled = false;
        
        this.renderCases();
        this.startTimer();
        this.updateDisplay();
        this.updatePowerUpDisplay();
        
        this.vibrate([50, 50]);
    }

    startTimer() {
        this.gameTimer = setInterval(() => {
            this.gameState.timeLeft--;
            this.updateTimerDisplay();
            
            if (this.gameState.timeLeft <= 0) {
                this.endRound();
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const timerValue = document.getElementById('timerValue');
        const timerCircle = document.getElementById('timerCircle');
        
        if (timerValue) timerValue.textContent = this.gameState.timeLeft;
        
        if (timerCircle) {
            const maxTime = 90 - (this.gameState.round - 1) * 15; // Decrease max time each round
            const percentage = (this.gameState.timeLeft / maxTime) * 360;
            
            if (this.gameState.timeLeft <= 15) {
                timerCircle.classList.add('timer-warning');
            }
            
            timerCircle.style.background = `conic-gradient(var(--adventure-blue) ${percentage}deg, transparent ${percentage}deg)`;
        }
    }

    updateDisplay() {
        // Update basic stats
        const elements = {
            balance: this.gameState.balance + ' GA',
            betAmount: this.gameState.betAmount + ' GA',
            score: this.gameState.score.toLocaleString(),
            round: this.gameState.round,
            streak: this.gameState.streak
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
        
        // Update streak indicator
        const streakIndicator = document.getElementById('streakIndicator');
        if (streakIndicator) {
            if (this.gameState.streak > 0) {
                streakIndicator.textContent = `🔥 ${this.gameState.streak} Streak! (+${this.gameState.streak * 10}% bonus)`;
                streakIndicator.style.display = 'block';
            } else {
                streakIndicator.style.display = 'none';
            }
        }
        
        // Update advanced stats in pro view
        if (this.gameState.viewMode === 'pro') {
            this.updateAdvancedStats();
        }
    }

    updateAdvancedStats() {
        const advancedElements = {
            totalGamesPlayed: this.gameState.totalGamesPlayed,
            totalWinnings: this.gameState.totalWinnings + ' GA',
            winRate: this.gameState.totalGamesPlayed > 0 ? 
                Math.round((this.gameState.totalWinnings > 0 ? 1 : 0) / this.gameState.totalGamesPlayed * 100) + '%' : '0%',
            averageScore: this.gameState.totalGamesPlayed > 0 ? 
                Math.round(this.gameState.averageScore / this.gameState.totalGamesPlayed).toLocaleString() : '0',
            bestStreak: this.gameState.bestStreak,
            puzzlesCompleted: this.gameState.puzzlesCompleted
        };

        Object.entries(advancedElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) element.textContent = value;
        });
    }

    endRound() {
        clearInterval(this.gameTimer);
        this.gameState.isPlaying = false;
        
        // Add winnings to balance
        this.gameState.balance += this.gameState.winnings;
        
        // Update statistics
        this.gameState.totalGamesPlayed++;
        this.gameState.totalWinnings += this.gameState.winnings;
        this.gameState.averageScore += this.gameState.score;
        
        // Add to game history
        this.gameState.gameHistory.unshift({
            round: this.gameState.round,
            score: this.gameState.score,
            winnings: this.gameState.winnings,
            casesOpened: this.gameState.openedCases,
            streak: this.gameState.streak,
            timestamp: new Date().toLocaleTimeString()
        });
        
        // Limit history size
        if (this.gameState.gameHistory.length > 10) {
            this.gameState.gameHistory.pop();
        }
        
        const roundComplete = document.getElementById('roundComplete');
        const roundResult = document.getElementById('roundResult');
        const roundWinnings = document.getElementById('roundWinnings');
        
        // Update round result text
        let resultText = `Round ${this.gameState.round} Complete!\n`;
        resultText += `Score: ${this.gameState.score.toLocaleString()}\n`;
        resultText += `Cases Opened: ${this.gameState.openedCases}/5\n`;
        
        if (this.gameState.puzzlePieces.some(piece => piece)) {
            const collectedPieces = this.gameState.puzzlePieces.filter(piece => piece).length;
            resultText += `Puzzle Pieces Collected: ${collectedPieces}/3`;
        }
        
        if (roundResult) roundResult.textContent = resultText;
        if (roundWinnings) roundWinnings.textContent = this.gameState.winnings + ' GA';
        if (roundComplete) roundComplete.style.display = 'block';
        
        // Save game data
        this.saveGameData();
        this.updateDisplay();
        
        this.vibrate([200, 100, 200]);
    }

    nextRound() {
        this.gameState.round++;
        this.gameState.timeLeft = Math.max(45, 90 - (this.gameState.round - 1) * 15); // Minimum 45 seconds
        this.gameState.openedCases = 0;
        this.gameState.winnings = 0; // Reset winnings for new round
        
        // Reset round state
        const roundComplete = document.getElementById('roundComplete');
        const timerCircle = document.getElementById('timerCircle');
        
        if (roundComplete) roundComplete.style.display = 'none';
        if (timerCircle) timerCircle.classList.remove('timer-warning');
        
        // Check if player has enough balance for the bet
        if (this.gameState.balance < this.gameState.betAmount) {
            this.showNotification('Insufficient balance for this bet. Reducing bet amount.');
            this.gameState.betAmount = Math.max(10, Math.floor(this.gameState.balance / 2));
        }
        
        // Deduct bet amount from balance
        this.gameState.balance -= this.gameState.betAmount;
        
        // Generate new cases
        this.gameState.cases = this.generateCases();
        this.adjustDifficulty();
        
        this.gameState.isPlaying = true;
        this.renderCases();
        this.startTimer();
        this.updateDisplay();
        this.updateTimerDisplay();
        
        this.vibrate([50, 50]);
    }

    resetGame() {
        clearInterval(this.gameTimer);
        Object.values(this.powerUpTimers).forEach(timer => clearInterval(timer));
        
        // Preserve balance and statistics when resetting
        const preservedData = {
            balance: this.gameState.balance,
            totalGamesPlayed: this.gameState.totalGamesPlayed,
            totalWinnings: this.gameState.totalWinnings,
            averageScore: this.gameState.averageScore,
            bestStreak: this.gameState.bestStreak,
            puzzlesCompleted: this.gameState.puzzlesCompleted,
            powerUpsUsed: this.gameState.powerUpsUsed,
            gameHistory: this.gameState.gameHistory,
            viewMode: this.gameState.viewMode
        };
        
        this.gameState = {
            isPlaying: false,
            score: 0,
            round: 1,
            timeLeft: 90,
            streak: 0,
            cases: [],
            openedCases: 0,
            puzzlePieces: [false, false, false],
            powerUps: {
                spotlight: { available: true, cooldown: 0 },
                lockdown: { available: true, cooldown: 0 },
                timeCapsule: { available: true, cooldown: 0 }
            },
            lockedValues: false,
            lockEndTime: 0,
            betAmount: 50,
            winnings: 0,
            ...preservedData
        };
        
        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        const roundComplete = document.getElementById('roundComplete');
        const casesGrid = document.getElementById('casesGrid');
        const timerCircle = document.getElementById('timerCircle');
        
        if (startBtn) startBtn.disabled = false;
        if (resetBtn) resetBtn.disabled = true;
        if (roundComplete) roundComplete.style.display = 'none';
        if (casesGrid) casesGrid.innerHTML = '';
        if (timerCircle) timerCircle.classList.remove('timer-warning');
        
        // Reset puzzle pieces
        document.querySelectorAll('.puzzle-piece').forEach(piece => {
            piece.classList.remove('collected');
        });
        
        this.updateDisplay();
        this.updateTimerDisplay();
        this.updatePowerUpDisplay();
        
        this.vibrate([100]);
    }

    loadGameData() {
        const savedData = localStorage.getItem('casesGameData');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                Object.assign(this.gameState, data);
            } catch (e) {
                console.warn('Failed to load saved game data');
            }
        }

        const savedViewMode = localStorage.getItem('casesViewMode');
        if (savedViewMode) {
            this.gameState.viewMode = savedViewMode;
        }
    }

    saveGameData() {
        const dataToSave = {
            balance: this.gameState.balance,
            betAmount: this.gameState.betAmount,
            totalGamesPlayed: this.gameState.totalGamesPlayed,
            totalWinnings: this.gameState.totalWinnings,
            averageScore: this.gameState.averageScore,
            bestStreak: this.gameState.bestStreak,
            puzzlesCompleted: this.gameState.puzzlesCompleted,
            powerUpsUsed: this.gameState.powerUpsUsed,
            gameHistory: this.gameState.gameHistory.slice(0, 10),
            viewMode: this.gameState.viewMode
        };
        localStorage.setItem('casesGameData', JSON.stringify(dataToSave));
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Trigger animation
        setTimeout(() => notification.classList.add('show'), 10);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Global functions for HTML event handlers
function nextRound() {
    if (window.casesGame) {
        window.casesGame.nextRound();
    }
}

// Initialize the game when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.casesGame = new CasesGame();
});

// Export for global access
window.CasesGame = CasesGame;