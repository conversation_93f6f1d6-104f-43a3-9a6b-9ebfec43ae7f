<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snacks of Doom - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/snacks.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Creepster&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <div id="headerWinLossIndicator" class="header-win-loss-indicator">
                    <!-- Will be filled by JS when win/loss occurs -->
                </div>
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="snacks-container">
            <!-- Game Header -->
            <div class="game-header">
                <a href="index.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">🍎 SNACKS OF DOOM 🍎</h1>
                <div class="view-toggle">
                    <button id="standardView" class="view-btn active">Standard</button>
                    <button id="proView" class="view-btn">Pro View</button>
                </div>
            </div>
            
            <div class="subtitle">Where hope goes to die</div>

            <!-- Mobile Wallet Display -->
            <div class="mobile-wallet">
                <div class="wallet-balance">
                    <i class="fas fa-coins"></i>
                    <span id="mobileBalance">1000 GA</span>
                </div>
                <div class="mobile-timer">
                    <i class="fas fa-clock"></i>
                    <span id="mobileTimer">5.0s</span>
                </div>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <!-- Left Column: Stats & Controls -->
                <div class="dashboard-left">
                    <!-- Game Stats -->
                    <div class="game-stats">
                        <div class="stat-item">
                            <div class="stat-label">GA Balance</div>
                            <div class="stat-value" id="points">1000</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Multiplier</div>
                            <div class="stat-value" id="multiplier">1.0×</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Debt Interest</div>
                            <div class="stat-value" id="debt">0%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Losses</div>
                            <div class="stat-value" id="losses">0</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="game-controls">
                        <button class="control-btn" id="newGameBtn">New Suffering (1000 GA)</button>
                        <button class="control-btn" id="gambleBtn" disabled>Forced Gamble</button>
                    </div>
                    
                    <!-- Game Rules Section -->
                    <div class="rules-section">
                        <h3 class="section-title">
                            <i class="fas fa-scroll"></i> Rules of Despair
                            <button class="toggle-btn" id="rulesToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </h3>
                        <div class="rules-content" id="rulesContent">
                            <p>Match 3 or more snacks to win (or lose) GA points:</p>
                            <ul>
                                <li><span class="good-snack">🍪 🥨 🏆 💎</span> - Good snacks bring joy (4+ matches only)</li>
                                <li><span class="bad-snack">☠️ 🤢 🦠 ☣️</span> - Bad snacks cause suffering (3+ matches)</li>
                                <li><span class="special-snack">🍎 🥚 🏛️</span> - Special penalty items (avoid at all costs)</li>
                            </ul>
                            <p>Penalties:</p>
                            <ul>
                                <li>Time runs out = GA penalty</li>
                                <li>Match bad snacks = GA loss + multiplier drop</li>
                                <li>Mandatory gambles after good matches (85% chance to lose everything)</li>
                                <li>Debt spiral adds interest on every move</li>
                            </ul>
                            <p class="warning-text">Remember: You are meant to suffer. This game is rigged against you.</p>
                        </div>
                    </div>
                    
                    <!-- Pro View - Snack Values -->
                    <div class="pro-view-section snack-values-section">
                        <h3 class="section-title">
                            <i class="fas fa-tags"></i> Snack Values
                            <button class="toggle-btn" id="valuesToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </h3>
                        <div class="snack-values-content" id="snackValuesContent">
                            <div class="snack-table">
                                <div class="snack-row">
                                    <div class="snack-cell">🍪</div>
                                    <div class="snack-cell">Cracker</div>
                                    <div class="snack-cell">+10 GA</div>
                                </div>
                                <div class="snack-row">
                                    <div class="snack-cell">🥨</div>
                                    <div class="snack-cell">Chip</div>
                                    <div class="snack-cell">+20 GA</div>
                                </div>
                                <div class="snack-row">
                                    <div class="snack-cell">🏆</div>
                                    <div class="snack-cell">Golden</div>
                                    <div class="snack-cell">+50 GA</div>
                                </div>
                                <div class="snack-row">
                                    <div class="snack-cell">💎</div>
                                    <div class="snack-cell">Diamond</div>
                                    <div class="snack-cell">+100 GA</div>
                                </div>
                                <div class="snack-row bad">
                                    <div class="snack-cell">☠️</div>
                                    <div class="snack-cell">Poison</div>
                                    <div class="snack-cell">-50 GA</div>
                                </div>
                                <div class="snack-row bad">
                                    <div class="snack-cell">🤢</div>
                                    <div class="snack-cell">Rotten</div>
                                    <div class="snack-cell">-30 GA</div>
                                </div>
                                <div class="snack-row bad">
                                    <div class="snack-cell">🦠</div>
                                    <div class="snack-cell">Moldy</div>
                                    <div class="snack-cell">-25 GA</div>
                                </div>
                                <div class="snack-row bad">
                                    <div class="snack-cell">☣️</div>
                                    <div class="snack-cell">Toxic</div>
                                    <div class="snack-cell">-40 GA</div>
                                </div>
                                <div class="snack-row special">
                                    <div class="snack-cell">🍎</div>
                                    <div class="snack-cell">Poison Apple</div>
                                    <div class="snack-cell">-100 GA</div>
                                </div>
                                <div class="snack-row special">
                                    <div class="snack-cell">🥚</div>
                                    <div class="snack-cell">Rotten Egg</div>
                                    <div class="snack-cell">-25 GA</div>
                                </div>
                                <div class="snack-row special">
                                    <div class="snack-cell">🏛️</div>
                                    <div class="snack-cell">Tax Collector</div>
                                    <div class="snack-cell">Debt +20%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pro View - Game Analytics -->
                    <div class="pro-view-section analytics-section">
                        <h3 class="section-title">
                            <i class="fas fa-chart-bar"></i> Game Analytics
                            <button class="toggle-btn" id="analyticsToggle">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </h3>
                        <div class="analytics-content" id="analyticsContent">
                            <div class="analytics-grid">
                                <div class="analytics-item">
                                    <div class="analytics-label">Total Wagered</div>
                                    <div class="analytics-value" id="totalWagered">0 GA</div>
                                </div>
                                <div class="analytics-item">
                                    <div class="analytics-label">Total Won</div>
                                    <div class="analytics-value" id="totalWon">0 GA</div>
                                </div>
                                <div class="analytics-item">
                                    <div class="analytics-label">Net Profit</div>
                                    <div class="analytics-value" id="netProfit">0 GA</div>
                                </div>
                                <div class="analytics-item">
                                    <div class="analytics-label">Highest Debt</div>
                                    <div class="analytics-value" id="highestDebt">0%</div>
                                </div>
                                <div class="analytics-item">
                                    <div class="analytics-label">Win Rate</div>
                                    <div class="analytics-value" id="winRate">0%</div>
                                </div>
                                <div class="analytics-item">
                                    <div class="analytics-label">Gamble Success</div>
                                    <div class="analytics-value" id="gambleSuccess">0%</div>
                                </div>
                            </div>
                            
                            <div class="match-stats">
                                <h4 class="sub-section-title">Match Statistics</h4>
                                <div class="match-stats-table">
                                    <div class="match-stat-row header">
                                        <div class="match-stat-cell">Snack Type</div>
                                        <div class="match-stat-cell">Matches</div>
                                        <div class="match-stat-cell">Impact</div>
                                    </div>
                                    <div class="match-stat-row" id="matchStatsBody">
                                        <!-- Will be populated by JS -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Column: Game Board -->
                <div class="dashboard-right">
                    <div class="game-status">
                        <div class="move-timer" id="moveTimer">Time: 5.0s</div>
                        <div class="debt-indicator" id="debtIndicator">
                            <i class="fas fa-skull"></i> DEBT SPIRAL ACTIVE
                        </div>
                    </div>
                    
                    <div class="game-board" id="gameBoard">
                        <!-- Grid cells will be generated by JavaScript -->
                    </div>
                    
                    <!-- Mobile Game Controls -->
                    <div class="mobile-game-controls">
                        <button class="mobile-control-btn" id="mobileNewGameBtn">New Suffering</button>
                        <button class="mobile-control-btn" id="mobileGambleBtn" disabled>Forced Gamble</button>
                    </div>
                </div>
            </div>
            
            <!-- Pro View - Game History -->
            <div class="pro-view-section history-section">
                <h3 class="section-title">
                    <i class="fas fa-history"></i> Game History
                    <button class="toggle-btn" id="historyToggle">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </h3>
                <div class="history-content" id="historyContent">
                    <div class="history-table-container">
                        <table class="history-table">
                            <thead>
                                <tr>
                                    <th>Game #</th>
                                    <th>Action</th>
                                    <th>Result</th>
                                    <th>Balance</th>
                                    <th>Time</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- Will be populated by JS -->
                            </tbody>
                        </table>
                    </div>
                    <div class="history-placeholder" id="historyPlaceholder">
                        No actions recorded yet. Your suffering awaits.
                    </div>
                </div>
            </div>
            
            <!-- Mobile Stats Toggle Button -->
            <div class="mobile-stats-toggle">
                <button id="mobileStatsToggle" class="mobile-panel-toggle">
                    <i class="fas fa-chart-line"></i>
                    <span>Show Stats</span>
                </button>
            </div>
            
            <!-- Mobile Stats Panel -->
            <div class="mobile-stats-panel" id="mobileStatsPanel">
                <div class="mobile-panel-header">
                    <h3>Game Statistics</h3>
                    <button id="closeStatsPanel" class="mobile-panel-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="mobile-panel-content">
                    <div class="mobile-stats-grid">
                        <div class="mobile-stat-card">
                            <div class="stat-label">Balance</div>
                            <div class="stat-value" id="mobilePoints">1000 GA</div>
                        </div>
                        <div class="mobile-stat-card">
                            <div class="stat-label">Multiplier</div>
                            <div class="stat-value" id="mobileMultiplier">1.0×</div>
                        </div>
                        <div class="mobile-stat-card">
                            <div class="stat-label">Debt</div>
                            <div class="stat-value" id="mobileDebt">0%</div>
                        </div>
                        <div class="mobile-stat-card">
                            <div class="stat-label">Losses</div>
                            <div class="stat-value" id="mobileLosses">0</div>
                        </div>
                    </div>
                    
                    <h4 class="mobile-section-title">Recent Actions</h4>
                    <div class="mobile-history-list" id="mobileHistoryList">
                        <!-- Will be populated by JS -->
                    </div>
                    
                    <h4 class="mobile-section-title">Achievements</h4>
                    <div class="mobile-achievements-list" id="mobileAchievementsList">
                        <!-- Will be populated by JS -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Gamble Modal -->
    <div class="gamble-overlay" id="gambleOverlay">
        <div class="gamble-modal">
            <h3>MANDATORY GAMBLE</h3>
            <p>Risk ALL your GA!</p>
            <p>85% chance to lose everything</p>
            <div class="countdown" id="countdown">3</div>
            <button class="modal-btn" id="acceptGamble">Accept Fate</button>
        </div>
    </div>

    <!-- Message Overlay -->
    <div class="message-overlay" id="messageOverlay"></div>
    
    <!-- Achievement Notification -->
    <div class="achievement" id="achievement"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/script.js"></script>
    <script src="assets/js/snacks.js"></script>
</body>
</html>