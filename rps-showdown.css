/* RPS Showdown CSS - Mobile First with Pro View */
:root {
    --rps-blue: #3498DB;
    --rps-red: #E74C3C;
    --rps-green: #2ECC71;
    --rps-purple: #9B59B6;
    --rps-yellow: #F1C40F;
    --rps-grey: #7F8C8D;
    --rps-dark: #2C3E50;
    --rps-light: #ECF0F1;
    --rps-orange: #E67E22;
    
    --rock-color: var(--rps-red);
    --paper-color: var(--rps-blue);
    --scissors-color: var(--rps-green);
    
    --win-color: #2ECC71;
    --lose-color: #E74C3C;
    --tie-color: #F1C40F;
    
    --background-gradient: linear-gradient(135deg, #2C3E50 0%, #1E293B 100%);
    --card-gradient: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}

/* Base Container */
.rps-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
    background: var(--background-gradient);
    color: white;
    font-family: 'Poppins', sans-serif;
}

/* Game Header */
.game-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
    text-align: center;
}

.back-link {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
    align-self: flex-start;
    margin-bottom: 0.5rem;
}

.back-link:hover {
    color: var(--rps-yellow);
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-size: 2rem;
    background: linear-gradient(45deg, var(--rps-red), var(--rps-blue), var(--rps-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.game-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.view-mode-toggle {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-toggle button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
    color: #fff;
    min-height: 36px;
}

.view-mode-toggle button.active {
    background: var(--rps-yellow);
    color: var(--rps-dark);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Panel Styles */
.stats-panel, 
.fairness-panel,
.pro-view-stats {
    background: var(--card-gradient);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.panel-title,
.pro-view-title {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    text-align: center;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-view-title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pro-view-title i {
    margin-right: 0.5rem;
    color: var(--rps-yellow);
}

/* Stat Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.75rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.pro-stat-item {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.pro-stat-label {
    font-size: 0.75rem;
    color: #fff;
    margin-bottom: 0.3rem;
    opacity: 0.8;
    text-transform: uppercase;
}

.pro-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--rps-yellow);
}

/* Game Area */
.game-area {
    background: var(--card-gradient);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Scoreboard */
.scoreboard {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 1.5rem;
    gap: 0.5rem;
}

.score-display {
    text-align: center;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    flex: 1;
    min-width: 80px;
}

.score-display.player {
    border-left: 4px solid var(--rps-blue);
}

.score-display.opponent {
    border-left: 4px solid var(--rps-red);
}

.best-of {
    text-align: center;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    flex: 1;
    min-width: 80px;
    border-left: 4px solid var(--rps-purple);
}

.score-label {
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
}

.score-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
}

/* Battle Area */
.battle-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin: 1.5rem 0;
    gap: 1rem;
}

.player-side, .opponent-side {
    text-align: center;
    width: 100%;
}

.player-label, .opponent-label {
    margin-bottom: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
}

.player-label {
    color: var(--rps-blue);
}

.opponent-label {
    color: var(--rps-red);
}

.hand-display {
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.versus {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--rps-yellow);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Result Display */
.result-display {
    text-align: center;
    margin: 1rem 0;
    padding: 0.75rem;
    border-radius: 10px;
    min-height: 50px;
    font-size: 1.1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Bet Controls */
.bet-controls {
    display: flex;
    justify-content: center;
    margin: 1rem 0;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.bet-control-group {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.5rem 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bet-adjust-btn {
    background: rgba(0, 0, 0, 0.3);
    border: none;
    color: white;
    border-radius: 5px;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s ease;
}

.bet-adjust-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

.bet-amount {
    font-weight: bold;
    color: var(--rps-yellow);
    min-width: 60px;
    text-align: center;
}

.potential-win {
    font-weight: bold;
    color: var(--win-color);
}

/* Choice Buttons */
.choice-buttons {
    display: flex;
    gap: 0.75rem;
    margin-top: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.choice-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 10px;
    padding: 0.75rem;
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.choice-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.choice-btn:hover::before {
    left: 100%;
}

.choice-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.choice-btn .icon {
    font-size: 1.8rem;
    margin-bottom: 0.25rem;
}

.choice-btn.rock {
    background: linear-gradient(45deg, var(--rock-color), rgba(231, 76, 60, 0.7));
}

.choice-btn.paper {
    background: linear-gradient(45deg, var(--paper-color), rgba(52, 152, 219, 0.7));
}

.choice-btn.scissors {
    background: linear-gradient(45deg, var(--scissors-color), rgba(46, 204, 113, 0.7));
}

.choice-btn:disabled {
    opacity: 0.5;
    transform: none;
    cursor: not-allowed;
}

.choice-btn:disabled::before {
    display: none;
}

/* Game Controls */
.game-controls {
    margin-top: 1.5rem;
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.control-btn {
    background: var(--rps-purple);
    border: none;
    border-radius: 8px;
    padding: 0.6rem 1rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    min-height: 44px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Fairness Info */
.fairness-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.seed-display {
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.5rem;
    border-radius: 6px;
    margin: 0.3rem 0;
    word-break: break-all;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verification-tool {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verify-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.5rem;
    color: white;
    margin: 0.3rem 0;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    box-sizing: border-box;
}

.verify-btn {
    background: var(--rps-green);
    border: none;
    border-radius: 6px;
    padding: 0.6rem 1rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 0.75rem;
    min-height: 44px;
}

.verify-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* History */
.history-container {
    max-height: 150px;
    overflow-y: auto;
    margin-top: 0.75rem;
}

.history-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.8rem;
}

/* Rules Section */
.rules-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-top: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.rules-toggle {
    background: none;
    border: none;
    color: var(--rps-blue);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
}

.rules-content {
    margin-top: 0.75rem;
    line-height: 1.5;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Pro View Specific Styles */
.pro-view-stats {
    display: none;
}

.pro-view-active .pro-view-stats {
    display: block;
    margin-bottom: 1rem;
}

.pro-view-sections {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pro-section {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 0.75rem;
}

.pro-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    text-transform: uppercase;
    color: var(--rps-yellow);
}

.pro-section-title i {
    margin-right: 0.5rem;
    color: var(--rps-orange);
}

/* Pattern Analysis */
.pattern-analysis {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.pattern-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 0.5rem;
    text-align: center;
}

.pattern-icon {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

.pattern-label {
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
}

.pattern-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--rps-yellow);
}

/* Strategy Matrix */
.strategy-matrix {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.4rem;
    margin-top: 0.5rem;
}

.matrix-cell {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    padding: 0.4rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.matrix-choice {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.matrix-percentage {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--rps-blue);
}

/* Chart Styles */
.chart-container {
    margin: 0.75rem 0;
}

.chart-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
}

.chart-label {
    width: 50px;
    font-size: 0.75rem;
    opacity: 0.8;
    text-transform: uppercase;
}

.chart-fill {
    flex: 1;
    height: 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
    margin: 0 0.5rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--rps-green), var(--rps-yellow));
    transition: width 0.3s ease;
}

.chart-value {
    font-size: 0.75rem;
    color: var(--rps-yellow);
    font-weight: 600;
    min-width: 35px;
    text-align: right;
}

/* Game History Enhanced */
.game-history {
    max-height: 120px;
    overflow-y: auto;
    margin-top: 0.5rem;
}

.history-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.4rem 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 0.8rem;
}

.history-entry:last-child {
    border-bottom: none;
}

.history-choices {
    font-size: 1rem;
}

.history-result {
    font-weight: 600;
}

.history-win {
    color: var(--win-color);
}

.history-loss {
    color: var(--lose-color);
}

.history-tie {
    color: var(--tie-color);
}

/* Pro Controls */
.pro-controls {
    display: none;
    margin-top: 1rem;
}

.pro-view-active .pro-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pro-btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    background: rgba(255,255,255,0.1);
    color: #fff;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
    min-width: 80px;
    text-transform: uppercase;
}

.pro-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
}

.pro-btn i {
    margin-right: 0.25rem;
}

/* AI Strategy Predictor */
.ai-predictor {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.5rem;
}

.predictor-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--rps-orange);
    text-transform: uppercase;
}

.prediction-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.4rem;
}

.prediction-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 0.4rem;
    text-align: center;
}

.prediction-choice {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
}

.prediction-probability {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--rps-yellow);
}

/* Animations */
.win-animation {
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.shake-animation {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.choice-btn:hover {
    animation: bounce 0.3s ease;
}

@keyframes bounce {
    0%, 100% { transform: translateY(-5px); }
    50% { transform: translateY(-8px); }
}

/* Notification */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: var(--rps-blue);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    z-index: 1000;
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: 300px;
    font-size: 0.9rem;
}

.notification.show {
    transform: translateX(0);
}

/* Responsive Styles */
@media (min-width: 768px) {
    .rps-container {
        padding: 1.5rem;
    }
    
    .game-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .game-title {
        font-size: 2.5rem;
        margin: 0 auto;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .view-mode-toggle {
        margin-top: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 2fr 1fr;
        gap: 1.5rem;
    }
    
    .battle-area {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }
    
    .hand-display {
        width: 120px;
        height: 120px;
        font-size: 3rem;
    }
    
    .choice-btn {
        width: 90px;
        height: 90px;
    }
    
    .choice-btn .icon {
        font-size: 2rem;
    }
    
    .versus {
        font-size: 1.5rem;
        margin: 0 1rem;
    }
    
    .pro-view-sections {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .pro-section {
        flex: 1;
        min-width: 200px;
    }
}

@media (min-width: 1024px) {
    .rps-container {
        padding: 2rem;
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .stats-grid,
    .pro-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .choice-btn {
        width: 100px;
        height: 100px;
    }
    
    .hand-display {
        width: 150px;
        height: 150px;
        font-size: 3.5rem;
    }
    
    .pro-controls {
        justify-content: center;
    }
    
    .pro-btn {
        max-width: 150px;
    }
}

@media (max-width: 480px) {
    .rps-container {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-subtitle {
        font-size: 0.8rem;
    }
    
    .choice-btn {
        width: 70px;
        height: 70px;
        gap: 0.5rem;
    }
    
    .choice-btn .icon {
        font-size: 1.5rem;
    }
    
    .hand-display {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    
    .score-value {
        font-size: 1.2rem;
    }
    
    .stats-grid,
    .pro-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .stat-card,
    .pro-stat-item {
        padding: 0.5rem;
    }
    
    .result-display {
        font-size: 1rem;
        padding: 0.6rem;
    }
    
    .bet-controls {
        gap: 0.5rem;
    }
    
    .bet-control-group {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
    
    .pattern-analysis {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .strategy-matrix {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-label {
        width: 40px;
        font-size: 0.7rem;
    }
    
    .chart-value {
        font-size: 0.7rem;
        min-width: 30px;
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .control-btn,
    .choice-btn,
    .pro-btn,
    .verify-btn,
    .bet-adjust-btn {
        min-height: 48px;
    }
    
    .view-mode-toggle button {
        min-height: 40px;
        padding: 0.5rem 0.75rem;
    }
    
    .verify-input {
        min-height: 44px;
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .stats-panel, 
    .fairness-panel,
    .pro-view-stats {
        background: rgba(20, 20, 30, 0.8);
    }
    
    .stat-card,
    .pro-stat-item,
    .pro-section {
        background: rgba(30, 30, 40, 0.5);
    }
    
    .fairness-info,
    .verification-tool,
    .rules-section {
        background: rgba(40, 40, 50, 0.5);
    }
    
    .seed-display,
    .verify-input {
        background: rgba(10, 10, 20, 0.5);
    }
    
    .history-item {
        background: rgba(20, 20, 30, 0.5);
    }
}