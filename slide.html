<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Cosmic Ascent - Provably Fair Crash Game | GoldenAura Casino</title>
    <meta name="description" content="Play the most advanced mobile-optimized Cosmic Ascent crash game with Pro analytics, provably fair gaming, and full responsive design. Watch the rocket soar!">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="slide.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#512da8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Cosmic Ascent">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="slide.js" as="script">
    <link rel="preload" href="slide.css" as="style">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Main Game Container -->
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-content">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Games
                </a>
                <h1 class="game-title">🚀 COSMIC ASCENT</h1>
                <p class="game-subtitle">🎯 Watch the multiplier rise, cash out before the crash! • Mobile Optimized • Pro Analytics</p>
            </div>
        </header>

        <!-- Main Game Area -->
        <div class="main-game-area">
            <!-- Game Display -->
            <div class="game-display">
                <div class="game-canvas-container">
                    <canvas id="gameCanvas" aria-label="Cosmic Ascent game display"></canvas>
                    <div class="rocket-trail" id="rocketTrail" aria-hidden="true"></div>
                    
                    <!-- Multiplier Display -->
                    <div class="multiplier-display" id="multiplierDisplay">
                        <span class="multiplier-value" id="multiplierValue" aria-live="polite">1.00</span>
                        <span class="multiplier-suffix">×</span>
                    </div>
                    
                    <!-- Round Status -->
                    <div class="round-status" id="roundStatus">
                        <span class="status-text" id="statusText" aria-live="polite">Waiting for next round...</span>
                        <div class="countdown" id="countdown" aria-live="polite"></div>
                    </div>
                    
                    <!-- Crash Animation -->
                    <div class="crash-overlay hidden" id="crashOverlay" role="alert">
                        <div class="crash-message">
                            <h2>CRASHED!</h2>
                            <p class="crash-multiplier" id="crashMultiplier">0.00×</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Controls -->
            <div class="game-controls">
                <!-- Stake Input -->
                <div class="stake-section">
                    <label for="stakeInput" class="control-label">
                        <i class="fas fa-coins"></i> Stake Amount (GA)
                    </label>
                    <div class="input-group">
                        <input type="number" id="stakeInput" min="10" max="10000" step="10" value="100" 
                               class="stake-input" aria-describedby="stakeHelp">
                        <div class="stake-buttons">
                            <button class="btn btn-small" id="halfStakeBtn" aria-label="Half stake">½</button>
                            <button class="btn btn-small" id="doubleStakeBtn" aria-label="Double stake">2×</button>
                            <button class="btn btn-small" id="maxStakeBtn" aria-label="Maximum stake">MAX</button>
                        </div>
                    </div>
                    <div id="stakeHelp" class="sr-only">Enter your bet amount in GA currency</div>
                </div>

                <!-- Auto Cash Out -->
                <div class="auto-section">
                    <label class="control-label">
                        <input type="checkbox" id="autoCashOutToggle" class="checkbox" aria-describedby="autoHelp">
                        <i class="fas fa-robot"></i> Auto Cash Out
                    </label>
                    <div class="auto-input-container">
                        <input type="number" id="autoCashOutInput" min="1.01" max="100" step="0.01" value="2.00" 
                               class="auto-input" disabled aria-label="Auto cash out multiplier">
                        <span class="auto-suffix">×</span>
                    </div>
                    <div id="autoHelp" class="sr-only">Automatically cash out when multiplier reaches this value</div>
                </div>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-primary" id="placeBetBtn" aria-describedby="betHelp">
                        <i class="fas fa-rocket"></i> Place Bet
                    </button>
                    <button class="btn btn-success hidden" id="cashOutBtn">
                        <i class="fas fa-hand-holding-usd"></i> Cash Out
                    </button>
                    <button class="btn btn-danger hidden" id="cancelBetBtn">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <div id="betHelp" class="sr-only">Place your bet to join the current round</div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Player Stats -->
            <div class="panel player-stats">
                <h3 class="panel-title">
                    <i class="fas fa-user"></i> Your Stats
                </h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Balance</span>
                        <span class="stat-value" id="balanceValue">10000 GA</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Current Bet</span>
                        <span class="stat-value" id="currentBetValue">0 GA</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Potential Win</span>
                        <span class="stat-value" id="potentialWinValue">0 GA</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Rounds</span>
                        <span class="stat-value" id="totalRoundsValue">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Win Rate</span>
                        <span class="stat-value" id="winRateValue">0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Profit/Loss</span>
                        <span class="stat-value" id="profitLossValue">0 GA</span>
                    </div>
                </div>
            </div>

            <!-- Provably Fair -->
            <div class="panel provably-fair">
                <h3 class="panel-title">
                    <i class="fas fa-shield-alt"></i> Provably Fair
                </h3>
                <div class="fair-info">
                    <div class="seed-row">
                        <label>🔒 Server Seed Hash:</label>
                        <span class="seed-value" id="serverSeedHash">-</span>
                    </div>
                    <div class="seed-row">
                        <label>🎲 Client Seed:</label>
                        <div class="seed-input-group">
                            <input type="text" id="clientSeedInput" class="seed-input" maxlength="32" 
                                   aria-label="Your client seed for randomness">
                            <button class="btn btn-small" id="randomizeSeedBtn" aria-label="Generate new random seed">🎲</button>
                        </div>
                    </div>
                    <div class="seed-row">
                        <label>🔢 Round Nonce:</label>
                        <span class="seed-value" id="roundNonce">0</span>
                    </div>
                    <button class="btn btn-small" id="verifyBtn">
                        <i class="fas fa-check-circle"></i> Verify Round
                    </button>
                </div>
            </div>

            <!-- Round History -->
            <div class="panel round-history">
                <h3 class="panel-title">
                    <i class="fas fa-history"></i> Recent Rounds
                </h3>
                <div class="history-list" id="historyList" role="list" aria-label="Recent crash multipliers">
                    <!-- History items will be added here -->
                </div>
            </div>

            <!-- Live Activity -->
            <div class="panel live-activity">
                <h3 class="panel-title">
                    <i class="fas fa-broadcast-tower"></i> Live Activity
                </h3>
                <div class="activity-feed" id="activityFeed" role="log" aria-label="Live player activity">
                    <!-- Activity items will be added here -->
                </div>
            </div>
        </div>

        <!-- How to Play Modal -->
        <div class="modal-overlay hidden" id="howToPlayModal" role="dialog" aria-labelledby="modalTitle" aria-modal="true">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">🚀 How to Play Cosmic Ascent</h2>
                    <button class="close-btn" id="closeModalBtn" aria-label="Close modal">×</button>
                </div>
                <div class="modal-body">
                    <h3>🎯 Game Objective</h3>
                    <p>Watch the rocket ascend and the multiplier increase from 1.00×. Cash out before it crashes to secure your winnings! The longer you wait, the higher the potential reward, but the greater the risk.</p>
                    
                    <h3>🎮 How to Play</h3>
                    <ol>
                        <li>Set your stake amount (10-10000 GA)</li>
                        <li>Optionally set an auto cash-out multiplier for automated wins</li>
                        <li>Click "Place Bet" during the 10-second betting phase</li>
                        <li>Watch the rocket launch and multiplier increase exponentially</li>
                        <li>Click "Cash Out" before the crash to secure your winnings</li>
                        <li>If you don't cash out before the crash, you lose your entire stake</li>
                    </ol>
                    
                    <h3>📱 Mobile Controls</h3>
                    <ul>
                        <li><strong>Tap "Place Bet"</strong> during betting phase to join the round</li>
                        <li><strong>Tap "Cash Out"</strong> while rocket is flying to secure winnings</li>
                        <li><strong>Long press game area</strong> for quick cash out during flight</li>
                        <li><strong>Use Pro view toggle</strong> for advanced analytics and patterns</li>
                        <li><strong>Haptic feedback</strong> provides game state information on supported devices</li>
                    </ul>
                    
                    <h3>🔒 Provably Fair System</h3>
                    <p>Every round's crash point is determined before the round starts using cryptographic seeds:</p>
                    <ul>
                        <li><strong>Server Seed:</strong> Generated and hashed before you place bets</li>
                        <li><strong>Client Seed:</strong> Your customizable input for additional randomness</li>
                        <li><strong>Round Nonce:</strong> Incremental number ensuring uniqueness</li>
                        <li><strong>Verification:</strong> After each round, verify the crash point was predetermined</li>
                    </ul>
                    
                    <h3>📊 Pro View Features</h3>
                    <ul>
                        <li><strong>Timing Analysis:</strong> Visual chart of recent crash patterns</li>
                        <li><strong>Pattern Recognition:</strong> Statistics on low vs high crashes</li>
                        <li><strong>Performance Tracking:</strong> Win rate, average cashout, profit/loss</li>
                        <li><strong>Smart Recommendations:</strong> AI-suggested targets based on patterns</li>
                        <li><strong>Session Analytics:</strong> Peak multiplier and timing insights</li>
                    </ul>
                    
                    <h3>🎯 Strategy Tips</h3>
                    <ul>
                        <li><strong>Conservative:</strong> Target 1.5x-2.0x for safer, consistent wins</li>
                        <li><strong>Moderate:</strong> Aim for 2.0x-3.0x for balanced risk/reward</li>
                        <li><strong>Aggressive:</strong> Go for 3.0x+ for high-risk, high-reward plays</li>
                        <li><strong>Auto Cash Out:</strong> Use to stick to your strategy and avoid emotional decisions</li>
                        <li><strong>Pattern Analysis:</strong> Use Pro view to identify favorable betting opportunities</li>
                    </ul>
                    
                    <h3>⌨️ Keyboard Shortcuts</h3>
                    <ul>
                        <li><strong>Space:</strong> Place bet (during betting) or Cash out (during flight)</li>
                        <li><strong>P:</strong> Toggle between Standard and Pro view modes</li>
                    </ul>
                    
                    <h3>🚨 Important Notes</h3>
                    <ul>
                        <li>The crash point is truly random and cannot be predicted</li>
                        <li>Each round is independent - past results don't affect future outcomes</li>
                        <li>House edge is approximately 1% for fair gameplay</li>
                        <li>Theoretical RTP (Return to Player) is 99%</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="game-footer">
            <div class="footer-buttons">
                <button class="info-btn" id="howToPlayBtn">
                    <i class="fas fa-question-circle"></i> How to Play
                </button>
                <button class="info-btn" id="fairnessBtn">
                    <i class="fas fa-shield-alt"></i> Provably Fair
                </button>
            </div>
            <span class="rtp-info">
                <i class="fas fa-info-circle"></i> Theoretical RTP: 99% • House Edge: 1%
            </span>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="slide.js"></script>

    <!-- Service Worker for PWA (Optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Prevent zoom on iOS Safari
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });
        
        // Optimize viewport for mobile
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        window.addEventListener('resize', setViewportHeight);
        setViewportHeight();
        
        // Disable context menu on long press for game elements
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.game-canvas-container, .btn, .stake-input')) {
                e.preventDefault();
            }
        });
        
        // Preload critical resources
        const criticalImages = [
            'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png'
        ];
        
        criticalImages.forEach(src => {
            const img = new Image();
            img.src = src;
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && perfData.loadEventEnd > 0) {
                    console.log('Page load time:', Math.round(perfData.loadEventEnd), 'ms');
                }
            }, 0);
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            console.log('Connection restored');
        });
        
        window.addEventListener('offline', function() {
            console.log('Connection lost - game will continue offline');
        });
        
        // Focus management for accessibility
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Orientation change handling
        window.addEventListener('orientationchange', function() {
            setTimeout(setViewportHeight, 100);
            // Also trigger canvas resize
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 200);
        });
        
        // Battery optimization
        let isVisible = true;
        document.addEventListener('visibilitychange', function() {
            isVisible = !document.hidden;
            if (window.game) {
                if (!isVisible) {
                    console.log('Page hidden - optimizing performance');
                } else {
                    console.log('Page visible - resuming normal operation');
                }
            }
        });
        
        // Prevent pull-to-refresh on mobile
        let startY = 0;
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].pageY;
        });
        
        document.addEventListener('touchmove', function(e) {
            if (e.touches[0].pageY > startY && window.pageYOffset === 0) {
                e.preventDefault();
            }
        }, { passive: false });
        
        // Enhance game canvas interaction for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('gameCanvas');
            if (canvas) {
                // Prevent canvas from being draggable
                canvas.style.touchAction = 'none';
                canvas.style.userSelect = 'none';
                canvas.style.webkitUserSelect = 'none';
                
                // Add touch feedback
                canvas.addEventListener('touchstart', function() {
                    canvas.style.opacity = '0.9';
                });
                
                canvas.addEventListener('touchend', function() {
                    canvas.style.opacity = '1';
                });
            }
        });
    </script>
</body>
</html>