<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Royal Roulette - Provably Fair European Roulette | GoldenAura Casino</title>
    <meta name="description" content="Play the most advanced mobile-optimized Roulette game with Pro analytics, provably fair gaming, and full responsive design. European Roulette with real-time statistics.">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="roulette.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#F9C74F">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Royal Roulette">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="roulette.js" as="script">
    <link rel="preload" href="roulette.css" as="style">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Main Game Container -->
    <div class="roulette-container">
        <!-- Game Header -->
        <div class="game-header">
            <h1 class="game-title">ROYAL ROULETTE</h1>
            <p class="game-subtitle">🎰 Provably Fair • European Roulette • Mobile Optimized • Pro Analytics</p>
        </div>

        <!-- Dashboard Grid - Mobile-First Layout -->
        <div class="dashboard-grid">
            <!-- Player Stats Panel -->
            <div class="stats-panel">
                <h3 class="panel-title">
                    <i class="fas fa-chart-line"></i> Player Stats
                </h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">Balance</div>
                        <div class="stat-value" id="balance">10,000 GA</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Total Spins</div>
                        <div class="stat-value" id="totalSpins">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Total Won</div>
                        <div class="stat-value" id="totalWon">0 GA</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Biggest Win</div>
                        <div class="stat-value" id="biggestWin">0 GA</div>
                    </div>
                </div>

                <!-- Rules Section -->
                <div class="rules-section">
                    <button class="rules-toggle" onclick="toggleRules()">
                        <i class="fas fa-info-circle"></i> How to Play & Payouts
                    </button>
                    <div class="rules-content" id="rulesContent" style="display: none;">
                        <p><strong>🎯 How to Play:</strong></p>
                        <ol>
                            <li>Select chip value from chip selector</li>
                            <li>Tap numbers or betting areas to place bets</li>
                            <li>Press "SPIN" to start the wheel</li>
                            <li>Win based on where the ball lands</li>
                        </ol>
                        
                        <p><strong>💰 Bet Types & Payouts:</strong></p>
                        <div class="bet-odds">
                            <table class="odds-table">
                                <tr>
                                    <th>Bet Type</th>
                                    <th>Payout</th>
                                    <th>Chance</th>
                                </tr>
                                <tr>
                                    <td>Single Number</td>
                                    <td>35:1</td>
                                    <td>2.7%</td>
                                </tr>
                                <tr>
                                    <td>Red/Black</td>
                                    <td>1:1</td>
                                    <td>48.6%</td>
                                </tr>
                                <tr>
                                    <td>Odd/Even</td>
                                    <td>1:1</td>
                                    <td>48.6%</td>
                                </tr>
                                <tr>
                                    <td>Dozen/Column</td>
                                    <td>2:1</td>
                                    <td>32.4%</td>
                                </tr>
                                <tr>
                                    <td>Low/High</td>
                                    <td>1:1</td>
                                    <td>48.6%</td>
                                </tr>
                            </table>
                        </div>
                        
                        <p><strong>🔒 Provably Fair:</strong></p>
                        <ul>
                            <li>Cryptographic seeds ensure fairness</li>
                            <li>Verify any spin using verification tool</li>
                            <li>Complete transparency in randomness</li>
                        </ul>
                        
                        <p><strong>📱 Mobile Controls:</strong></p>
                        <ul>
                            <li>Long press betting areas for quick bets</li>
                            <li>Space bar to spin (keyboard)</li>
                            <li>C key to clear bets</li>
                            <li>P key to toggle Pro view</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Roulette Game Area -->
            <div class="roulette-area">
                <div class="game-area-layout">
                    <!-- Roulette Wheel -->
                    <div class="wheel-container">
                        <div class="wheel-pointer"></div>
                        <div class="roulette-wheel" id="rouletteWheel">
                            <div class="wheel-outer-rim"></div>
                            <div class="wheel-inner-rim"></div>
                            <div class="wheel-center"></div>
                            <div class="wheel-ball" id="wheelBall"></div>
                            <!-- Wheel numbers will be generated by JavaScript -->
                        </div>
                    </div>
                    
                    <!-- Last Numbers Display -->
                    <div class="last-numbers" id="lastNumbers">
                        <!-- Last numbers will be populated by JavaScript -->
                    </div>
                    
                    <!-- Betting Table -->
                    <div class="betting-table">
                        <!-- Number Grid -->
                        <div class="betting-grid" id="bettingGrid">
                            <!-- Betting grid will be generated by JavaScript -->
                        </div>
                        
                        <!-- Outside Bets -->
                        <div class="outside-bets" id="outsideBets">
                            <!-- Outside betting options will be generated by JavaScript -->
                        </div>
                        
                        <!-- Betting Controls -->
                        <div class="betting-controls">
                            <!-- Chip Selector -->
                            <div class="chip-selector" id="chipSelector">
                                <div class="chip chip-1 selected" data-value="1">1</div>
                                <div class="chip chip-5" data-value="5">5</div>
                                <div class="chip chip-10" data-value="10">10</div>
                                <div class="chip chip-25" data-value="25">25</div>
                                <div class="chip chip-100" data-value="100">100</div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="bet-actions">
                                <button class="action-btn clear-btn" id="clearBtn">
                                    <i class="fas fa-trash"></i> Clear
                                </button>
                                <button class="action-btn spin-btn" id="spinBtn">
                                    <i class="fas fa-play"></i> SPIN
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Result Display -->
                    <div class="result-display" id="resultDisplay">
                        <div class="result-text">🎯 Place your bets and spin to win!</div>
                    </div>
                </div>
            </div>

            <!-- Fairness & Verification Panel -->
            <div class="fairness-panel">
                <h3 class="panel-title">
                    <i class="fas fa-shield-alt"></i> Provably Fair System
                </h3>
                
                <!-- Current Round Seeds -->
                <div class="fairness-info">
                    <h4 style="color: white; margin-bottom: 1rem;">🔐 Current Round Seeds</h4>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--roulette-blue);">Client Seed:</strong>
                        <div class="seed-display" id="clientSeed">--</div>
                    </div>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--roulette-gold);">Server Seed (Hashed):</strong>
                        <div class="seed-display" id="hashedServerSeed">--</div>
                    </div>
                    
                    <div>
                        <strong style="color: var(--roulette-green);">Nonce:</strong>
                        <div class="seed-display" id="nonce">0</div>
                    </div>
                </div>

                <!-- Verification Tool -->
                <div class="verification-tool">
                    <h4 style="color: white; margin-bottom: 1rem;">🔍 Verify Past Spin</h4>
                    
                    <input type="text" class="verify-input" id="verifyClientSeed" placeholder="Enter client seed">
                    <input type="text" class="verify-input" id="verifyServerSeed" placeholder="Enter revealed server seed">
                    <input type="number" class="verify-input" id="verifyNonce" placeholder="Enter nonce">
                    
                    <button class="verify-btn" onclick="verifyResult()">
                        <i class="fas fa-check-circle"></i> Verify Result
                    </button>
                    
                    <div id="verificationResult" style="margin-top: 1rem; padding: 1rem; background: rgba(0,0,0,0.2); border-radius: 8px; display: none;">
                        <!-- Verification result will be displayed here -->
                    </div>
                </div>

                <!-- Spin History -->
                <div class="history-container">
                    <h4 style="color: white; margin-bottom: 1rem;">📊 Spin History</h4>
                    <div id="spinHistory">
                        <!-- Spin history will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification System -->
    <div class="notification" id="notification"></div>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="roulette.js"></script>

    <!-- Service Worker for PWA (Optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Prevent zoom on iOS Safari
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });
        
        // Optimize viewport for mobile
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        window.addEventListener('resize', setViewportHeight);
        setViewportHeight();
        
        // Disable context menu on long press for touch devices
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.betting-table, .chip, .action-btn')) {
                e.preventDefault();
            }
        });
        
        // Preload critical images
        const criticalImages = [
            'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png'
        ];
        
        criticalImages.forEach(src => {
            const img = new Image();
            img.src = src;
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && perfData.loadEventEnd > 0) {
                    console.log('Page load time:', Math.round(perfData.loadEventEnd), 'ms');
                }
            }, 0);
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            console.log('Connection restored');
        });
        
        window.addEventListener('offline', function() {
            console.log('Connection lost - game will continue offline');
        });
    </script>
</body>
</html>