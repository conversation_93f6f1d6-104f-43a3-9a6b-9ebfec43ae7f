/* Tome of Life - Mobile-First Responsive Design */

/* CSS Variables for theming and responsive design */
:root {
    --primary-dark: #2a0845;
    --primary-medium: #4a148c;
    --primary-light: #6a1b9a;
    --accent-gold: #ffd700;
    --accent-copper: #b87333;
    --text-light: #f8f5f0;
    --text-dark: #2c2316;
    --parchment: #f4f1ea;
    --parchment-dark: #e8e0d0;
    --leather: #8b4513;
    --leather-dark: #5e2605;
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --gold-gradient: linear-gradient(135deg, #ffd700, #f6b02b);
    
    /* Mobile-first variables */
    --mobile-padding: 1rem;
    --tome-size: 300px;
    --symbol-size: 60px;
    --border-radius: 8px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
    
    /* Z-index layers */
    --z-index-background: 1;
    --z-index-content: 10;
    --z-index-modal: 100;
    --z-index-notification: 200;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cormorant Garamond', 'Cinzel', serif;
    background: linear-gradient(135deg, var(--primary-dark) 0%, #4a148c 50%, #6a1b9a 100%);
    color: var(--text-light);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%232a0845"/><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.3"/><circle cx="50" cy="30" r="0.5" fill="%23ffffff" opacity="0.3"/><circle cx="70" cy="70" r="0.8" fill="%23ffffff" opacity="0.3"/><circle cx="30" cy="50" r="0.6" fill="%23ffffff" opacity="0.3"/><circle cx="90" cy="20" r="0.7" fill="%23ffffff" opacity="0.3"/></svg>');
}

/* View Mode Toggle */
.view-mode-toggle {
    position: fixed;
    top: 100px;
    right: 1rem;
    z-index: var(--z-index-notification);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.3rem;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.view-toggle-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.5rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    min-width: 60px;
    text-transform: uppercase;
    font-weight: 600;
}

.view-toggle-btn.active {
    background: var(--gold-gradient);
    color: var(--primary-dark);
    transform: scale(1.05);
}

/* Main Container - Mobile First */
.game-container {
    max-width: 100vw;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles - Mobile Optimized */
.game-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--primary-light);
    gap: 1rem;
    text-align: center;
}

.header-left, .header-right {
    order: 2;
}

.header-center {
    order: 1;
    width: 100%;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-family: 'Cinzel', serif;
    font-size: 0.9rem;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary-light);
    border-radius: 20px;
    min-height: 44px;
}

.back-link i {
    margin-right: 0.5rem;
}

.back-link:hover {
    color: var(--accent-gold);
    border-color: var(--accent-gold);
}

.game-title {
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    font-weight: 700;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.game-subtitle {
    font-size: 0.9rem;
    font-style: italic;
    color: var(--text-light);
    opacity: 0.8;
    text-align: center;
    line-height: 1.4;
}

.info-btn {
    background: transparent;
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 0.8rem 1.5rem;
    border-radius: 20px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-family: 'Cinzel', serif;
    font-size: 0.8rem;
    transition: var(--transition);
    min-height: 44px;
    gap: 0.5rem;
}

.info-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-gold);
    transform: translateY(-2px);
}

/* Main Content - Mobile Stack Layout */
.main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 1.5rem;
}

/* Book Section - Mobile Optimized */
.book-section {
    order: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.tome-container {
    width: 100%;
    max-width: var(--tome-size);
    aspect-ratio: 4 / 3;
    position: relative;
}

.tome-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.candlelight {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(
        rgba(255, 204, 0, 0.2),
        rgba(255, 153, 0, 0.1),
        transparent 70%
    );
    filter: blur(8px);
}

.candlelight.left {
    top: -10px;
    left: -10px;
    animation: flicker 3s infinite alternate;
}

.candlelight.right {
    top: -10px;
    right: -10px;
    animation: flicker 4s infinite alternate;
}

@keyframes flicker {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.9; }
}

/* Mobile-First Tome Styles */
.tome {
    width: 100%;
    height: 100%;
    background: var(--leather);
    border-radius: 15px;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tome-cover {
    height: 50px;
    background: var(--leather-dark);
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 2px solid var(--accent-copper);
}

.tome-title {
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--accent-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.tome-pages {
    flex: 1;
    display: flex;
    background: var(--parchment-dark);
}

.page {
    flex: 1;
    padding: 1rem;
    position: relative;
    min-height: 250px;
}

.page::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1px;
    background: rgba(0, 0, 0, 0.1);
}

.left-page {
    background: var(--parchment);
}

.left-page::after {
    right: 0;
}

.right-page {
    background: var(--parchment-dark);
}

.right-page::after {
    left: 0;
}

.page-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--text-dark);
}

/* Mobile-Optimized Symbol Grid */
.symbol-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 0.5rem;
    width: 100%;
    height: 100%;
    max-height: 200px;
    max-width: 200px;
}

.symbol-cell {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(139, 69, 19, 0.3);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: all 0.3s ease;
    cursor: default;
    min-height: 44px;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.symbol-inner {
    font-size: 2rem;
    transition: transform 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    transform: scale(0.5);
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.symbol-cell.revealed .symbol-inner {
    opacity: 1;
    transform: scale(1);
    animation: symbolReveal 0.5s ease-out;
}

.symbol-cell.highlighted {
    box-shadow: 0 0 15px var(--accent-gold);
    background: rgba(255, 215, 0, 0.3);
    border-color: var(--accent-gold);
    z-index: var(--z-index-content);
    animation: highlight 0.6s ease-in-out;
}

@keyframes symbolReveal {
    0% { transform: scale(0.3) rotate(-180deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(0deg); opacity: 0.8; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

@keyframes highlight {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 25px var(--accent-gold); }
}

/* Mobile Outcome Display */
.outcome-display {
    padding: 1rem;
    text-align: center;
    width: 100%;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.win-message, .lose-message {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.win-message.visible, .lose-message.visible {
    opacity: 1;
    transform: translateY(0);
}

.win-message h3 {
    color: var(--success-color);
    font-family: 'Cinzel', serif;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
}

.lose-message h3 {
    color: var(--danger-color);
    font-family: 'Cinzel', serif;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
}

.win-amount {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.win-multiplier {
    font-size: 1.2rem;
    color: var(--accent-copper);
    margin-top: 0.3rem;
}

/* Mobile-First Controls */
.controls {
    padding: 1rem;
    background: var(--leather-dark);
    border-top: 2px solid var(--accent-copper);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stake-control {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.stake-control label {
    font-family: 'Cinzel', serif;
    color: var(--text-light);
    font-size: 0.9rem;
    text-align: center;
}

.stake-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stake-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--primary-light);
    border-radius: var(--border-radius);
    padding: 1rem;
    color: var(--text-light);
    font-family: 'Cormorant Garamond', serif;
    font-size: 1.2rem;
    text-align: center;
    min-height: 44px;
}

.stake-input:focus {
    outline: none;
    border-color: var(--accent-gold);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
}

.stake-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.stake-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--text-light);
    padding: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
    min-height: 44px;
    font-weight: 600;
}

.stake-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-gold);
    transform: translateY(-2px);
}

.stake-btn:active {
    transform: scale(0.95);
}

.reveal-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-light));
    border: 1px solid var(--accent-copper);
    border-radius: 15px;
    padding: 1.2rem;
    color: var(--text-light);
    font-family: 'Cinzel', serif;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.8rem;
    min-height: 56px;
    box-shadow: 0 4px 15px rgba(74, 20, 140, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.reveal-btn:hover {
    background: linear-gradient(135deg, var(--primary-light), var(--accent-gold));
    border-color: var(--accent-gold);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 20, 140, 0.4);
}

.reveal-btn:active {
    transform: scale(0.98);
}

.reveal-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    font-size: 1.3rem;
}

/* Pro View Panel */
.pro-view-panel {
    display: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

body.pro-view-active .pro-view-panel {
    display: block;
}

.pro-panel-title {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--accent-gold);
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-family: 'Cinzel', serif;
}

.pro-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
    margin-bottom: 1.5rem;
}

.pro-stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pro-stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.pro-stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pro-stat-value {
    color: var(--accent-gold);
    font-size: 1.1rem;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Cinzel', serif;
}

/* Symbol Analytics */
.symbol-analytics {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.symbol-analytics h4 {
    color: var(--accent-copper);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Cinzel', serif;
}

.symbol-frequency-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    font-size: 0.8rem;
}

.symbol-freq-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.symbol-icon {
    font-size: 1.2rem;
}

/* Win Pattern Analysis */
.win-patterns {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1rem;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.win-patterns h4 {
    color: var(--success-color);
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: 'Cinzel', serif;
}

.pattern-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    font-size: 0.8rem;
}

.pattern-item {
    text-align: center;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

.pattern-label {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0.3rem;
}

.pattern-value {
    color: var(--success-color);
    font-weight: bold;
    font-family: 'Cinzel', serif;
}

/* Info Section - Mobile Stack */
.info-section {
    order: 2;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.panel {
    background: rgba(74, 20, 140, 0.5);
    border-radius: 15px;
    border: 1px solid var(--primary-light);
    overflow: hidden;
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.panel-title {
    font-family: 'Cinzel', serif;
    font-size: 1rem;
    font-weight: 600;
    padding: 1rem 1.5rem;
    background: var(--primary-medium);
    color: var(--accent-gold);
    border-bottom: 1px solid var(--primary-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Mobile Stats Panel */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-light);
    opacity: 0.8;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--accent-gold);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Mobile Provably Fair Panel */
.fair-info {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.seed-row {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.seed-row label {
    font-size: 0.8rem;
    color: var(--text-light);
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.seed-value {
    font-family: 'Courier New', monospace;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.8rem;
    border-radius: 8px;
    font-size: 0.8rem;
    overflow-x: auto;
    white-space: nowrap;
    border: 1px solid rgba(255, 255, 255, 0.1);
    word-break: break-all;
}

.seed-input-group {
    display: flex;
    gap: 0.5rem;
}

.seed-input {
    flex: 1;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--primary-light);
    border-radius: 8px;
    padding: 0.8rem;
    color: var(--text-light);
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.seed-input:focus {
    outline: none;
    border-color: var(--accent-gold);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
}

.seed-btn {
    background: var(--primary-medium);
    border: 1px solid var(--primary-light);
    border-radius: 8px;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    min-width: 50px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.seed-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-gold);
}

.verify-btn {
    margin-top: 1rem;
    background: var(--primary-medium);
    border: 1px solid var(--primary-light);
    border-radius: 10px;
    padding: 1rem;
    color: var(--text-light);
    cursor: pointer;
    font-family: 'Cormorant Garamond', serif;
    transition: var(--transition);
    min-height: 44px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.verify-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-copper);
    transform: translateY(-2px);
}

/* Mobile Payout Panel */
.payout-table {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.payout-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    transition: var(--transition);
    border-radius: 8px;
    border: 1px solid transparent;
}

.payout-row:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.symbol-display {
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.payout-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.combo {
    font-size: 0.9rem;
    color: var(--text-light);
}

.multiplier {
    font-family: 'Cinzel', serif;
    font-weight: 600;
    color: var(--accent-gold);
    font-size: 1rem;
}

.payout-row.negative .multiplier {
    color: var(--danger-color);
}

/* Mobile History Panel */
.history-list {
    padding: 1.5rem;
    max-height: 300px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-item:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.history-symbols {
    display: flex;
    gap: 0.3rem;
}

.history-symbol {
    font-size: 1.2rem;
}

.history-outcome {
    font-family: 'Cinzel', serif;
    font-size: 0.9rem;
    font-weight: 600;
}

.history-win {
    color: var(--success-color);
}

.history-lose {
    color: var(--danger-color);
}

/* Modal - Mobile Optimized */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-index-modal);
    opacity: 1;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(5px);
    padding: 1rem;
}

.modal-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    background: var(--primary-medium);
    border-radius: 15px;
    border: 1px solid var(--accent-copper);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.modal-overlay.hidden .modal-content {
    transform: translateY(20px);
}

.modal-header {
    padding: 1.5rem;
    background: var(--primary-dark);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--accent-copper);
}

.modal-header h2 {
    font-family: 'Cinzel', serif;
    color: var(--accent-gold);
    font-size: 1.3rem;
    font-weight: 600;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal-btn:hover {
    color: var(--accent-gold);
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-body h3 {
    font-family: 'Cinzel', serif;
    color: var(--accent-gold);
    margin: 1.5rem 0 0.8rem;
    font-size: 1.1rem;
}

.modal-body p {
    margin-bottom: 1rem;
    line-height: 1.5;
}

.modal-body ul, .modal-body ol {
    margin-left: 1.2rem;
    margin-bottom: 1rem;
}

.modal-body li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.verification-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.9rem;
    color: var(--text-light);
    font-weight: 600;
}

.verify-input {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--primary-light);
    border-radius: 8px;
    padding: 1rem;
    color: var(--text-light);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.verify-input:focus {
    outline: none;
    border-color: var(--accent-gold);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.3);
}

.run-verify-btn {
    margin-top: 1rem;
    background: var(--primary-dark);
    border: 1px solid var(--primary-light);
    border-radius: 10px;
    padding: 1rem;
    color: var(--text-light);
    cursor: pointer;
    transition: var(--transition);
    font-family: 'Cinzel', serif;
    min-height: 44px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.run-verify-btn:hover {
    background: var(--primary-light);
    border-color: var(--accent-copper);
    transform: translateY(-2px);
}

.verification-result {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--primary-light);
}

.verification-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.verification-cell {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verification-message {
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    font-family: 'Cinzel', serif;
}

.verification-success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.verification-failure {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, var(--primary-medium), var(--accent-gold));
    color: white;
    padding: 1rem;
    border-radius: 10px;
    z-index: var(--z-index-notification);
    font-weight: 600;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    max-width: calc(100vw - 40px);
    font-size: 0.9rem;
}

.notification.show {
    transform: translateX(0);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Tablet Landscape (768px+) */
@media (min-width: 768px) and (orientation: landscape) {
    :root {
        --tome-size: 400px;
        --symbol-size: 80px;
        --mobile-padding: 1.5rem;
    }
    
    .game-header {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
    
    .header-left, .header-center, .header-right {
        order: initial;
        width: auto;
    }
    
    .header-center {
        text-align: center;
    }
    
    .main-content {
        flex-direction: row;
        gap: 2rem;
    }
    
    .book-section {
        order: 1;
        flex: 2;
    }
    
    .info-section {
        order: 2;
        flex: 1;
    }
    
    .game-title {
        font-size: 2.5rem;
    }
    
    .symbol-grid {
        max-height: 250px;
        max-width: 250px;
        gap: 0.8rem;
    }
    
    .symbol-inner {
        font-size: 2.5rem;
    }
    
    .controls {
        flex-direction: row;
        align-items: center;
    }
    
    .stake-control {
        flex-direction: row;
        align-items: center;
        flex: 1;
    }
    
    .stake-input-group {
        flex-direction: row;
    }
    
    .stake-buttons {
        grid-template-columns: repeat(3, 1fr);
        width: 150px;
        margin-left: 0.5rem;
    }
    
    .reveal-btn {
        width: auto;
        min-width: 200px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Tablet Portrait (768px+) */
@media (min-width: 768px) and (orientation: portrait) {
    :root {
        --tome-size: 450px;
    }
    
    .game-container {
        max-width: 600px;
    }
    
    .game-title {
        font-size: 2.3rem;
    }
    
    .symbol-grid {
        max-height: 280px;
        max-width: 280px;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    :root {
        --tome-size: 500px;
        --symbol-size: 100px;
        --mobile-padding: 2rem;
    }
    
    .game-container {
        max-width: 1200px;
        padding: var(--mobile-padding);
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .symbol-grid {
        max-height: 320px;
        max-width: 320px;
        gap: 1rem;
    }
    
    .symbol-inner {
        font-size: 3rem;
    }
    
    .controls {
        padding: 1.5rem;
        gap: 1.5rem;
    }
    
    .panel {
        margin-bottom: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .stat-item {
        padding: 1.5rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .pro-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .modal-content {
        max-width: 700px;
    }
    
    .modal-header {
        padding: 2rem;
    }
    
    .modal-body {
        padding: 2rem;
    }
}

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
    :root {
        --tome-size: 600px;
    }
    
    .game-container {
        max-width: 1400px;
    }
    
    .game-title {
        font-size: 3.5rem;
    }
    
    .symbol-grid {
        max-height: 380px;
        max-width: 380px;
    }
    
    .symbol-inner {
        font-size: 3.5rem;
    }
}

/* Touch optimization for mobile devices */
@media (hover: none) {
    .stake-btn:hover,
    .reveal-btn:hover,
    .verify-btn:hover,
    .info-btn:hover,
    .back-link:hover,
    .stat-item:hover,
    .pro-stat-card:hover,
    .payout-row:hover,
    .history-item:hover,
    .close-modal-btn:hover,
    .run-verify-btn:hover {
        transform: none;
        background: initial;
        color: initial;
        border-color: initial;
        box-shadow: initial;
    }
    
    /* Ensure all interactive elements meet touch target requirements */
    .stake-btn,
    .reveal-btn,
    .verify-btn,
    .info-btn,
    .back-link,
    .seed-btn,
    .close-modal-btn,
    .stake-input,
    .seed-input,
    .verify-input {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Prevent zoom on input focus (iOS Safari) */
@media (max-width: 767px) {
    .stake-input,
    .seed-input,
    .verify-input {
        font-size: 16px;
    }
}

/* Dark mode optimization */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #000000 0%, #2a0845 50%, #4a148c 100%);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .tome,
    .panel,
    .stake-btn,
    .reveal-btn,
    .symbol-cell {
        border: 2px solid white;
    }
    
    .stat-item,
    .pro-stat-card,
    .payout-row,
    .history-item {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .stake-btn,
    .reveal-btn,
    .verify-btn,
    .info-btn,
    .stat-item,
    .pro-stat-card,
    .symbol-cell,
    .candlelight {
        transition: none;
        animation: none;
    }
    
    @keyframes flicker {
        0%, 100% { opacity: 0.7; }
    }
    
    @keyframes symbolReveal {
        0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    }
    
    @keyframes highlight {
        0%, 100% { transform: scale(1); }
    }
}