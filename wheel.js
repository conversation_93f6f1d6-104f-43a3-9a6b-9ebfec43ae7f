// Cosmic Fortune - Provably Fair Wheel Game with GA Currency System
class CosmicFortuneGame {
    constructor() {
        // Game State
        this.gameState = 'ready'; // ready, spinning, result
        this.gaBalance = 10000; // GA currency
        this.gaCurrencyName = "GA Coins";
        this.gaCurrencySymbol = "GA";
        this.totalSpins = 0;
        this.totalWon = 0;
        this.biggestWin = 0;
        this.currentBet = 50;
        this.isSpinning = false;
        
        // Provably fair
        this.clientSeed = '';
        this.serverSeed = '';
        this.hashedServerSeed = '';
        this.nonce = 0;
        this.spinHistory = [];
        this.segments = [];
        
        // Initialize DOM elements
        this.initElements();
        this.attachEventListeners();
        
        // Setup game
        this.generateNewSeeds();
        this.createWheelSegments();
        this.createStarField();
        this.updateDisplay();
    }
    
    // Wheel Configuration (30 segments) - House-Favorable Distribution
    get WHEEL_CONFIG() {
        return [
            { multiplier: 100, color: '#FFD700', probability: 0.033, count: 1 },    // 3.3% - Jackpot (very rare)
            { multiplier: 10, color: '#FF6B35', probability: 0.033, count: 1 },     // 3.3% - Big win (very rare)
            { multiplier: 5, color: '#F7931E', probability: 0.067, count: 2 },      // 6.7% - Good win (rare)
            { multiplier: 2, color: '#00B894', probability: 0.133, count: 4 },      // 13.3% - Small win (uncommon)
            { multiplier: 1, color: '#6C5CE7', probability: 0.167, count: 5 },      // 16.7% - Break even (somewhat rare)
            { multiplier: 0.5, color: '#E17055', probability: 0.233, count: 7 },    // 23.3% - Partial loss (common)
            { multiplier: 0, color: '#2D3436', probability: 0.333, count: 10 }      // 33.3% - Total loss (most common)
        ];
    }
    
    initElements() {
        // Wheel elements
        this.wheel = document.getElementById('wheel');
        this.wheelSegments = document.getElementById('wheelSegments');
        this.spinBtn = document.getElementById('spinBtn');
        this.betInput = document.getElementById('betAmount');
        this.resultDisplay = document.getElementById('resultDisplay');
        
        // Stats displays
        this.balanceDisplay = document.getElementById('balance');
        this.totalSpinsDisplay = document.getElementById('totalSpins');
        this.totalWonDisplay = document.getElementById('totalWon');
        this.biggestWinDisplay = document.getElementById('biggestWin');
        
        // Update currency name in UI
        if (document.querySelector('.game-subtitle')) {
            document.querySelector('.game-subtitle').textContent = 
                `Provably Fair • Transparent • Test Your Cosmic Luck • Play with ${this.gaCurrencyName}`;
        }
        
        // Update labels for GA currency
        if (document.querySelector('.stat-label')) {
            const statLabels = document.querySelectorAll('.stat-label');
            statLabels.forEach(label => {
                if (label.textContent.includes("Balance")) {
                    label.textContent = this.gaCurrencyName;
                } else if (label.textContent.includes("Total Won")) {
                    label.textContent = `Total Won (${this.gaCurrencySymbol})`;
                } else if (label.textContent.includes("Biggest Win")) {
                    label.textContent = `Biggest Win (${this.gaCurrencySymbol})`;
                }
            });
        }
        
        // Fairness displays
        this.clientSeedDisplay = document.getElementById('clientSeed');
        this.hashedServerSeedDisplay = document.getElementById('hashedServerSeed');
        this.nonceDisplay = document.getElementById('nonce');
        this.spinHistoryDisplay = document.getElementById('spinHistory');
        
        // Verification inputs
        this.verifyClientSeed = document.getElementById('verifyClientSeed');
        this.verifyServerSeed = document.getElementById('verifyServerSeed');
        this.verifyNonce = document.getElementById('verifyNonce');
        this.verificationResult = document.getElementById('verificationResult');
        
        // Other elements
        this.starsBg = document.getElementById('starsBg');
        this.notification = document.getElementById('notification');
    }
    
    // Generate new cryptographic seeds
    generateNewSeeds() {
        // Generate client seed (random string)
        this.clientSeed = this.generateRandomString(32);
        
        // Generate server seed and hash it
        this.serverSeed = this.generateRandomString(32);
        this.hashedServerSeed = this.sha256(this.serverSeed);
        
        // Update display
        this.updateSeedDisplays();
    }
    
    updateSeedDisplays() {
        if (this.clientSeedDisplay) this.clientSeedDisplay.textContent = this.clientSeed;
        if (this.hashedServerSeedDisplay) this.hashedServerSeedDisplay.textContent = this.hashedServerSeed;
        if (this.nonceDisplay) this.nonceDisplay.textContent = this.nonce;
    }
    
    // Generate random string for seeds
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    // Simple SHA-256 implementation (for demo purposes)
    sha256(ascii) {
        function rightRotate(value, amount) {
            return (value >>> amount) | (value << (32 - amount));
        }
        
        let mathPow = Math.pow;
        let maxWord = mathPow(2, 32);
        let lengthProperty = 'length';
        let i, j;
        let result = '';

        let words = [];
        let asciiBitLength = ascii[lengthProperty] * 8;
        
        let hash = this.sha256.h = this.sha256.h || [];
        let k = this.sha256.k = this.sha256.k || [];
        let primeCounter = k[lengthProperty];

        let isComposite = {};
        for (let candidate = 2; primeCounter < 64; candidate++) {
            if (!isComposite[candidate]) {
                for (i = 0; i < 313; i += candidate) {
                    isComposite[i] = candidate;
                }
                hash[primeCounter] = (mathPow(candidate, .5) * maxWord) | 0;
                k[primeCounter++] = (mathPow(candidate, 1/3) * maxWord) | 0;
            }
        }
        
        ascii += '\x80';
        while (ascii[lengthProperty] % 64 - 56) ascii += '\x00';
        for (i = 0; i < ascii[lengthProperty]; i++) {
            j = ascii.charCodeAt(i);
            if (j >> 8) return;
            words[i >> 2] |= j << ((3 - i) % 4) * 8;
        }
        words[words[lengthProperty]] = ((asciiBitLength / maxWord) | 0);
        words[words[lengthProperty]] = (asciiBitLength);
        
        for (j = 0; j < words[lengthProperty];) {
            let w = words.slice(j, j += 16);
            let oldHash = hash;
            hash = hash.slice(0, 8);
            
            for (i = 0; i < 64; i++) {
                let w15 = w[i - 15], w2 = w[i - 2];
                let a = hash[0], e = hash[4];
                let temp1 = hash[7]
                    + (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25))
                    + ((e & hash[5]) ^ ((~e) & hash[6]))
                    + k[i]
                    + (w[i] = (i < 16) ? w[i] : (
                        w[i - 16]
                        + (rightRotate(w15, 7) ^ rightRotate(w15, 18) ^ (w15 >>> 3))
                        + w[i - 7]
                        + (rightRotate(w2, 17) ^ rightRotate(w2, 19) ^ (w2 >>> 10))
                    ) | 0
                );
                let temp2 = (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22))
                    + ((a & hash[1]) ^ (a & hash[2]) ^ (hash[1] & hash[2]));
                
                hash = [(temp1 + temp2) | 0].concat(hash);
                hash[4] = (hash[4] + temp1) | 0;
            }
            
            for (i = 0; i < 8; i++) {
                hash[i] = (hash[i] + oldHash[i]) | 0;
            }
        }
        
        for (i = 0; i < 8; i++) {
            for (j = 3; j + 1; j--) {
                let b = (hash[i] >> (j * 8)) & 255;
                result += ((b < 16) ? 0 : '') + b.toString(16);
            }
        }
        return result;
    }
    
    // Create wheel segments based on configuration
    createWheelSegments() {
        this.segments = [];
        
        // Create segments array
        this.WHEEL_CONFIG.forEach(config => {
            for (let i = 0; i < config.count; i++) {
                this.segments.push({
                    multiplier: config.multiplier,
                    color: config.color
                });
            }
        });

        // Shuffle segments for randomness
        this.segments = this.shuffleArray(this.segments);

        // Create visual segments
        if (this.wheelSegments) {
            this.wheelSegments.innerHTML = '';

            const segmentAngle = 360 / this.segments.length;

            this.segments.forEach((segment, index) => {
                const segmentElement = document.createElement('div');
                segmentElement.className = 'segment';
                segmentElement.style.transform = `rotate(${index * segmentAngle}deg)`;
                segmentElement.style.backgroundColor = segment.color;
                segmentElement.textContent = segment.multiplier === 0 ? '💀' : `×${segment.multiplier}`;
                
                // Add border between segments
                segmentElement.style.borderRight = '2px solid rgba(255, 255, 255, 0.3)';
                
                this.wheelSegments.appendChild(segmentElement);
            });
        }
    }
    
    // Shuffle array utility
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    // Create animated star field
    createStarField() {
        if (!this.starsBg) return;
        
        for (let i = 0; i < 100; i++) {
            const star = document.createElement('div');
            star.className = 'star';
            star.style.left = Math.random() * 100 + '%';
            star.style.top = Math.random() * 100 + '%';
            star.style.width = Math.random() * 3 + 1 + 'px';
            star.style.height = star.style.width;
            star.style.animationDelay = Math.random() * 2 + 's';
            star.style.animationDuration = (Math.random() * 3 + 2) + 's';
            this.starsBg.appendChild(star);
        }
    }
    
    // Main spin function
    spinWheel() {
        const betAmount = parseInt(this.betInput.value);
        
        // Validate bet
        if (isNaN(betAmount) || betAmount < 1 || betAmount > 500) {
            this.showNotification(`Bet must be between 1 and 500 ${this.gaCurrencySymbol}`, 'error');
            return;
        }
        
        if (betAmount > this.gaBalance) {
            this.showNotification(`Insufficient ${this.gaCurrencyName} balance`, 'error');
            return;
        }
        
        if (this.isSpinning) {
            return;
        }

        // Start spinning
        this.isSpinning = true;
        this.gameState = 'spinning';
        this.currentBet = betAmount;
        this.gaBalance -= betAmount;
        this.totalSpins++;
        this.nonce++;

        // Update UI
        if (this.spinBtn) this.spinBtn.disabled = true;
        if (this.resultDisplay) {
            this.resultDisplay.innerHTML = '<div class="result-text">Spinning...</div>';
        }
        this.updateDisplay();

        // Calculate result using provably fair algorithm
        const result = this.calculateProvablyFairResult();
        
        // Animate wheel spin
        this.animateWheelSpin(result.segmentIndex, () => {
            // Handle spin result
            this.handleSpinResult(result);
        });
    }
    
    // Calculate provably fair result
    calculateProvablyFairResult() {
        // Combine seeds and nonce
        const combinedSeed = this.clientSeed + this.serverSeed + this.nonce;
        const hash = this.sha256(combinedSeed);
        
        // Convert first 8 characters of hash to integer
        const hexSubstring = hash.substring(0, 8);
        const decimal = parseInt(hexSubstring, 16);
        
        // Map to segment index
        const segmentIndex = decimal % this.segments.length;
        const segment = this.segments[segmentIndex];
        
        return {
            segmentIndex: segmentIndex,
            multiplier: segment.multiplier,
            hash: hash,
            combinedSeed: combinedSeed
        };
    }
    
    // Animate wheel spin
    animateWheelSpin(targetSegment, callback) {
        if (!this.wheel) return;
        
        const segmentAngle = 360 / this.segments.length;
        
        // Calculate target rotation
        const targetAngle = -(targetSegment * segmentAngle) + (segmentAngle / 2);
        
        // Add multiple full rotations for effect
        const fullRotations = 5;
        const finalRotation = (fullRotations * 360) + targetAngle;
        
        // Apply rotation
        this.wheel.style.transform = `rotate(${finalRotation}deg)`;
        
        // Add spinning sound effect (simulated with vibration on mobile)
        if (navigator.vibrate) {
            navigator.vibrate([100, 50, 100, 50, 100]);
        }
        
        // Wait for animation to complete
        setTimeout(() => {
            callback();
        }, 4000);
    }
    
    // Handle spin result
    handleSpinResult(result) {
        this.gameState = 'result';
        
        const winAmount = Math.floor(this.currentBet * result.multiplier);
        this.gaBalance += winAmount;
        this.totalWon += winAmount;
        
        if (winAmount > this.biggestWin) {
            this.biggestWin = winAmount;
        }

        // Add to history
        this.spinHistory.unshift({
            nonce: this.nonce,
            clientSeed: this.clientSeed,
            serverSeed: this.serverSeed,
            bet: this.currentBet,
            multiplier: result.multiplier,
            winAmount: winAmount,
            hash: result.hash,
            timestamp: new Date().toLocaleTimeString()
        });

        // Keep only last 10 spins in history
        if (this.spinHistory.length > 10) {
            this.spinHistory.pop();
        }

        // Update displays
        this.updateDisplay();
        this.updateSpinHistory();
        this.showSpinResult(result.multiplier, winAmount);

        // Generate new seeds for next spin
        this.generateNewSeeds();

        // Re-enable spin button
        this.isSpinning = false;
        this.gameState = 'ready';
        if (this.spinBtn) this.spinBtn.disabled = false;
    }
    
    // Show spin result
    showSpinResult(multiplier, winAmount) {
        if (!this.resultDisplay) return;
        
        if (multiplier === 0) {
            this.resultDisplay.innerHTML = `
                <div class="result-text" style="color: var(--astro-red);">
                    <i class="fas fa-skull"></i> Try Again!
                </div>
            `;
            this.showNotification('Better luck next time!', 'info');
        } else {
            this.resultDisplay.innerHTML = `
                <div class="result-text" style="color: var(--astro-green);">
                    <i class="fas fa-star"></i> You won ${winAmount} ${this.gaCurrencySymbol}! (×${multiplier})
                </div>
            `;
            
            if (multiplier >= 10) {
                this.showNotification(`Amazing! ${multiplier}x multiplier win!`, 'success');
            } else {
                this.showNotification(`You won ${winAmount} ${this.gaCurrencySymbol}!`, 'success');
            }
        }
    }
    
    // Update all display elements
    updateDisplay() {
        if (this.balanceDisplay) this.balanceDisplay.textContent = `${this.gaBalance.toLocaleString()} ${this.gaCurrencySymbol}`;
        if (this.totalSpinsDisplay) this.totalSpinsDisplay.textContent = this.totalSpins;
        if (this.totalWonDisplay) this.totalWonDisplay.textContent = `${this.totalWon.toLocaleString()} ${this.gaCurrencySymbol}`;
        if (this.biggestWinDisplay) this.biggestWinDisplay.textContent = `${this.biggestWin.toLocaleString()} ${this.gaCurrencySymbol}`;
        this.updateSeedDisplays();
    }
    
    // Update spin history display
    updateSpinHistory() {
        if (!this.spinHistoryDisplay) return;
        
        this.spinHistoryDisplay.innerHTML = '';

        this.spinHistory.forEach(spin => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                    <strong>Spin #${spin.nonce}</strong>
                    <span style="color: ${spin.multiplier === 0 ? 'var(--astro-red)' : 'var(--astro-green)'};">
                        ×${spin.multiplier} ${spin.winAmount > 0 ? `(+${spin.winAmount} ${this.gaCurrencySymbol})` : ''}
                    </span>
                </div>
                <div style="font-size: 0.8rem; color: rgba(255,255,255,0.6); font-family: 'Courier New', monospace;">
                    Client: ${spin.clientSeed.substring(0, 16)}...<br>
                    Server: ${spin.serverSeed.substring(0, 16)}...<br>
                    Hash: ${spin.hash.substring(0, 16)}...
                </div>
            `;
            this.spinHistoryDisplay.appendChild(historyItem);
        });
    }
    
    // Verify result function
    verifyResult() {
        if (!this.verifyClientSeed || !this.verifyServerSeed || !this.verifyNonce) return;
        
        const clientSeed = this.verifyClientSeed.value;
        const serverSeed = this.verifyServerSeed.value;
        const nonce = parseInt(this.verifyNonce.value);

        if (!clientSeed || !serverSeed || isNaN(nonce)) {
            this.showNotification('Please fill in all verification fields', 'error');
            return;
        }

        // Recreate the calculation
        const combinedSeed = clientSeed + serverSeed + nonce;
        const hash = this.sha256(combinedSeed);
        const hexSubstring = hash.substring(0, 8);
        const decimal = parseInt(hexSubstring, 16);
        const segmentIndex = decimal % this.segments.length;
        const resultMultiplier = this.segments[segmentIndex].multiplier;

        // Display verification result
        if (this.verificationResult) {
            this.verificationResult.style.display = 'block';
            this.verificationResult.innerHTML = `
                <h5 style="color: var(--astro-green); margin-bottom: 1rem;">✅ Verification Successful</h5>
                <p><strong>Combined Seed:</strong> ${combinedSeed}</p>
                <p><strong>SHA-256 Hash:</strong> ${hash}</p>
                <p><strong>Hex Substring:</strong> ${hexSubstring}</p>
                <p><strong>Decimal Value:</strong> ${decimal}</p>
                <p><strong>Segment Index:</strong> ${segmentIndex}</p>
                <p><strong>Result Multiplier:</strong> ×${resultMultiplier}</p>
            `;
        }
    }
    
    // Toggle rules display
    toggleRules() {
        const rulesContent = document.getElementById('rulesContent');
        if (rulesContent) {
            if (rulesContent.style.display === 'none') {
                rulesContent.style.display = 'block';
            } else {
                rulesContent.style.display = 'none';
            }
        }
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        if (!this.notification) return;
        
        this.notification.textContent = message;
        this.notification.className = 'notification show';
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            this.notification.classList.remove('show');
        }, 3000);
    }
    
    // Attach event listeners
    attachEventListeners() {
        // Bet input validation
        if (this.betInput) {
            this.betInput.addEventListener('input', () => {
                const value = parseInt(this.betInput.value);
                const max = Math.min(500, this.gaBalance);
                if (value > max) {
                    this.betInput.value = max;
                    this.showNotification(`Maximum bet is ${max} ${this.gaCurrencySymbol}`, 'warning');
                }
                if (value < 1) {
                    this.betInput.value = 1;
                }
            });
        }

        // Keyboard support for spinning (spacebar)
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !this.isSpinning) {
                e.preventDefault();
                this.spinWheel();
            }
        });

        // Allow clicking on wheel segments for visual feedback
        if (this.wheelSegments) {
            this.wheelSegments.addEventListener('click', (e) => {
                if (e.target.classList.contains('segment') && !this.isSpinning) {
                    // Find segment index
                    const segments = Array.from(this.wheelSegments.children);
                    const index = segments.indexOf(e.target);
                    if (index >= 0 && this.segments[index]) {
                        const segmentData = this.segments[index];
                        this.showNotification(`Segment ${index + 1}: ×${segmentData.multiplier}`, 'info');
                    }
                }
            });
        }
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM loaded, initializing Cosmic Fortune game...");
    try {
        window.cosmicFortuneGame = new CosmicFortuneGame();
        
        // Global functions for HTML onclick attributes
        window.spinWheel = function() {
            if (window.cosmicFortuneGame) {
                window.cosmicFortuneGame.spinWheel();
            }
        };
        
        window.verifyResult = function() {
            if (window.cosmicFortuneGame) {
                window.cosmicFortuneGame.verifyResult();
            }
        };
        
        window.toggleRules = function() {
            if (window.cosmicFortuneGame) {
                window.cosmicFortuneGame.toggleRules();
            }
        };
        
        console.log("Cosmic Fortune game initialized successfully!");
    } catch (error) {
        console.error("Error initializing Cosmic Fortune game:", error);
    }
});