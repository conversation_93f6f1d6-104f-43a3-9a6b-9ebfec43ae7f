<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dragon Tower - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="dragon.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="dragon-container">
            <!-- Game Title and Back Button -->
            <div class="dragon-header">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Back to Games</span>
                </a>
                <h1 class="game-title">DRAGON TOWER</h1>
                <div class="view-mode-toggle">
                    <button id="standardViewBtn" class="active">Standard</button>
                    <button id="proViewBtn">Pro View</button>
                </div>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <div class="game-stats">
                    <div class="stat-item">
                        <span class="stat-label">GA Balance</span>
                        <span class="stat-value" id="balanceValue">1000</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Current Bet (GA)</span>
                        <span class="stat-value" id="betValue">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Potential Win (GA)</span>
                        <span class="stat-value" id="potentialWinValue">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Tower Tilt</span>
                        <span class="stat-value" id="tiltValue">0°</span>
                    </div>
                </div>
                
                <div class="game-controls">
                    <div class="bet-controls" style="margin-bottom: 0.75rem;">
                        <input type="number" id="betInput" min="10" max="1000" step="10" value="50" placeholder="Bet amount" style="width: 100%; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 0.5rem;">
                        <button class="btn btn-primary" id="startGameBtn" style="width: 100%;">
                            <i class="fas fa-play"></i> Place Bet & Start
                        </button>
                    </div>
                    <button class="btn btn-success" id="cashoutBtn" disabled style="width: 100%; margin-bottom: 0.5rem;">
                        <i class="fas fa-money-bill-wave"></i> Cash Out
                    </button>
                    <button class="btn btn-danger" id="glueBtn" disabled style="width: 100%;">
                        <i class="fas fa-magic"></i> Magic Glue (-50 GA)
                    </button>
                    
                    <div class="pro-controls">
                        <button class="pro-btn" id="analyzeBtn">
                            <i class="fas fa-chart-line"></i> Analyze
                        </button>
                        <button class="pro-btn" id="stabilizeBtn">
                            <i class="fas fa-balance-scale"></i> Stabilize
                        </button>
                        <button class="pro-btn" id="predictBtn">
                            <i class="fas fa-brain"></i> Predict
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Pro View Stats -->
            <div class="pro-view-stats">
                <div class="pro-view-title">
                    <i class="fas fa-chart-bar"></i> Pro View Statistics
                </div>
                
                <div class="stats-grid">
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Current Risk</span>
                        <span class="pro-stat-value" id="riskValue">
                            <span class="risk-level risk-low">Low</span>
                        </span>
                    </div>
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Tilt Direction</span>
                        <span class="pro-stat-value" id="tiltDirectionValue">Neutral</span>
                    </div>
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Tilt Delta</span>
                        <span class="pro-stat-value" id="tiltDeltaValue">0.0°/s</span>
                    </div>
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Stability Score</span>
                        <span class="pro-stat-value" id="stabilityValue">100%</span>
                    </div>
                </div>
                
                <div class="stat-dashboard">
                    <div style="margin-top: 0.75rem;">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-fire"></i> Dragon Rage Analytics
                        </div>
                        <div class="dragon-attack-log" id="attackLog">
                            <div class="attack-log-entry">
                                <span class="attack-type">Game started</span>
                                <span class="attack-time">Now</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 0.75rem;">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-tower-observation"></i> Tower Stability
                        </div>
                        <div class="block-properties">
                            <span class="block-property">Normal: <span id="normalBlockCount">0</span></span>
                            <span class="block-property">Gold: <span id="goldBlockCount">0</span></span>
                            <span class="block-property">Cursed: <span id="cursedBlockCount">0</span></span>
                            <span class="block-property">Weak: <span id="weakBlockCount">0</span></span>
                        </div>
                        <div class="tilt-history-chart" id="tiltChart">
                            <div class="tilt-graph-line"></div>
                            <!-- Tilt graph points will be added dynamically -->
                        </div>
                    </div>
                    
                    <div style="margin-top: 0.75rem;">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-calculator"></i> Win Probabilities
                        </div>
                        <table class="probability-table">
                            <thead>
                                <tr>
                                    <th>Target Floor</th>
                                    <th>Probability</th>
                                    <th>Payout</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>10</td>
                                    <td id="prob10">85%</td>
                                    <td id="payout10">3.0x</td>
                                </tr>
                                <tr>
                                    <td>20</td>
                                    <td id="prob20">40%</td>
                                    <td id="payout20">5.0x</td>
                                </tr>
                                <tr>
                                    <td>30</td>
                                    <td id="prob30">15%</td>
                                    <td id="payout30">7.0x</td>
                                </tr>
                                <tr>
                                    <td>50</td>
                                    <td id="prob50">1%</td>
                                    <td id="payout50">11.0x</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Game Area -->
            <div class="game-area">
                <div class="floor-indicator">Floor: <span id="currentFloor">0</span></div>
                
                <div class="rage-meter-container">
                    <div class="rage-meter" id="rageMeter"></div>
                </div>
                
                <div class="tilt-indicator">
                    <div class="tilt-marker" style="bottom: 75%;"></div>
                    <div class="tilt-marker danger" style="bottom: 25%;"></div>
                    <div class="tilt-meter" id="tiltMeter"></div>
                </div>
                
                <div class="doom-timer">
                    <div class="doom-timer-label">Next Attack</div>
                    <div class="doom-timer-value" id="doomTimer">8s</div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                    <div class="progress-fake"></div>
                </div>
                
                <div class="dragon red" id="dragonRed"></div>
                <div class="dragon blue" id="dragonBlue"></div>
                <div class="dragon green" id="dragonGreen"></div>
                
                <div class="ghost-towers" id="ghostTowers">
                    <!-- Ghost towers will be added here dynamically -->
                </div>
                
                <div class="clouds" id="clouds">
                    <!-- Clouds will be added here dynamically -->
                </div>
                
                <div class="tower-container" id="towerContainer">
                    <!-- Tower blocks will be added here dynamically -->
                </div>
                
                <div class="ground"></div>
                
                <div class="stable-indicator" id="stableIndicator">STABLE!</div>
                
                <div class="game-message" id="gameMessage">
                    <h2>TOWER CRUMBLED!</h2>
                    <p>Your tower collapsed at floor <span id="finalFloor">0</span>.</p>
                    <p>Final score: <span id="finalScore">0</span></p>
                    <button class="btn btn-primary" id="retryBtn">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </div>

            <!-- Tower Info Section -->
            <div class="tower-info">
                <div class="info-header">
                    <div class="info-title">Your Tower Records</div>
                </div>
                
                <div class="tower-records">
                    <div class="record-item">
                        <div class="record-label">Highest Floor</div>
                        <div class="record-value highlight" id="highestFloorValue">0</div>
                    </div>
                    <div class="record-item">
                        <div class="record-label">Biggest Win (GA)</div>
                        <div class="record-value highlight" id="biggestWinValue">0</div>
                    </div>
                    <div class="record-item">
                        <div class="record-label">Total GA Won</div>
                        <div class="record-value highlight" id="totalWonValue">0</div>
                    </div>
                    <div class="record-item">
                        <div class="record-label">Win Rate</div>
                        <div class="record-value lowlight" id="winRateValue">0%</div>
                    </div>
                </div>
                
                <div class="tower-tips">
                    <div class="tips-title">Dragon Tower Tips</div>
                    <ul class="tips-list">
                        <li>Stack blocks carefully to avoid dangerous tilts</li>
                        <li>Gold blocks give extra points but increase dragon rage!</li>
                        <li>Use Magic Glue strategically to stabilize your tower</li>
                        <li>The Ground Shaker attacks the foundation, be ready!</li>
                        <li>Try to reach Floor 50 for legendary status</li>
                    </ul>
                </div>
                
                <div class="probability-note">
                    <i class="fas fa-info-circle"></i> Top 0.5% of players reach floor 30+. Will you be one of them?
                </div>
            </div>
        </div>
    </main>

    <script src="assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game elements
            const towerContainer = document.getElementById('towerContainer');
            const startGameBtn = document.getElementById('startGameBtn');
            const betInput = document.getElementById('betInput');
            const cashoutBtn = document.getElementById('cashoutBtn');
            const glueBtn = document.getElementById('glueBtn');
            const balanceValue = document.getElementById('balanceValue');
            const betValue = document.getElementById('betValue');
            const potentialWinValue = document.getElementById('potentialWinValue');
            const tiltValue = document.getElementById('tiltValue');
            const currentFloor = document.getElementById('currentFloor');
            const rageMeter = document.getElementById('rageMeter');
            const tiltMeter = document.getElementById('tiltMeter');
            const doomTimer = document.getElementById('doomTimer');
            const progressFill = document.getElementById('progressFill');
            const stableIndicator = document.getElementById('stableIndicator');
            const gameMessage = document.getElementById('gameMessage');
            const finalFloor = document.getElementById('finalFloor');
            const finalScore = document.getElementById('finalScore');
            const retryBtn = document.getElementById('retryBtn');
            const dragonRed = document.getElementById('dragonRed');
            const dragonBlue = document.getElementById('dragonBlue');
            const dragonGreen = document.getElementById('dragonGreen');
            const ghostTowers = document.getElementById('ghostTowers');
            const clouds = document.getElementById('clouds');
            
            // Pro View elements
            const standardViewBtn = document.getElementById('standardViewBtn');
            const proViewBtn = document.getElementById('proViewBtn');
            const riskValue = document.getElementById('riskValue');
            const tiltDirectionValue = document.getElementById('tiltDirectionValue');
            const tiltDeltaValue = document.getElementById('tiltDeltaValue');
            const stabilityValue = document.getElementById('stabilityValue');
            const attackLog = document.getElementById('attackLog');
            const normalBlockCount = document.getElementById('normalBlockCount');
            const goldBlockCount = document.getElementById('goldBlockCount');
            const cursedBlockCount = document.getElementById('cursedBlockCount');
            const weakBlockCount = document.getElementById('weakBlockCount');
            const tiltChart = document.getElementById('tiltChart');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const stabilizeBtn = document.getElementById('stabilizeBtn');
            const predictBtn = document.getElementById('predictBtn');
            
            // Stats elements
            const highestFloorValue = document.getElementById('highestFloorValue');
            const biggestWinValue = document.getElementById('biggestWinValue');
            const totalWonValue = document.getElementById('totalWonValue');
            const winRateValue = document.getElementById('winRateValue');
            
            // Game variables
            let isPlaying = false;
            let balance = localStorage.getItem('dragonTowerBalance') ? parseInt(localStorage.getItem('dragonTowerBalance')) : 1000;
            let currentBet = 0;
            let floor = 0;
            let tilt = 0;
            let prevTilt = 0;
            let tiltDirection = 'neutral';
            let tiltDelta = 0;
            let dragonRage = 0;
            let groundShakeInterval = 8; // seconds
            let intervalTimer = null;
            let doomTimerValue = groundShakeInterval;
            let doomTimerInterval = null;
            let lastBlockTime = 0;
            let placementCooldown = 800; // 0.8s cooldown between blocks
            let glueActive = false;
            let glueBlocksLeft = 0;
            let blockCounts = {
                normal: 0,
                gold: 0,
                cursed: 0,
                weak: 0
            };
            let tiltHistory = [];
            let attackHistory = [];
            let viewMode = localStorage.getItem('dragonTowerViewMode') || 'standard';
            
            // Game stats
            let gameStats = {
                highestFloor: 0,
                biggestWin: 0,
                totalWon: 0,
                totalGames: 0,
                wins: 0
            };
            
            // Load game stats from localStorage
            function loadGameStats() {
                const savedStats = localStorage.getItem('dragonTowerStats');
                if (savedStats) {
                    gameStats = JSON.parse(savedStats);
                }
                updateStatsDisplay();
            }
            
            // Save game stats to localStorage
            function saveGameStats() {
                localStorage.setItem('dragonTowerStats', JSON.stringify(gameStats));
            }
            
            // Update stats display
            function updateStatsDisplay() {
                balanceValue.textContent = balance;
                highestFloorValue.textContent = gameStats.highestFloor;
                biggestWinValue.textContent = gameStats.biggestWin;
                totalWonValue.textContent = gameStats.totalWon;
                winRateValue.textContent = gameStats.totalGames > 0 
                    ? Math.round((gameStats.wins / gameStats.totalGames) * 100) + '%'
                    : '0%';
            }
            
            // Save balance to localStorage
            function saveBalance() {
                localStorage.setItem('dragonTowerBalance', balance);
            }
            
            // Initialize game
            function initGame() {
                loadGameStats();
                createGhostTowers();
                createClouds();
                createStars();
                setViewMode(viewMode);
                
                // Event listeners
                startGameBtn.addEventListener('click', startGame);
                cashoutBtn.addEventListener('click', cashOut);
                glueBtn.addEventListener('click', useGlue);
                retryBtn.addEventListener('click', resetGame);
                standardViewBtn.addEventListener('click', () => setViewMode('standard'));
                proViewBtn.addEventListener('click', () => setViewMode('pro'));
                analyzeBtn.addEventListener('click', analyzeGame);
                stabilizeBtn.addEventListener('click', stabilizeTower);
                predictBtn.addEventListener('click', predictFailure);
                
                // Add keyboard support
                document.addEventListener('keydown', function(e) {
                    if (e.code === 'Space' && isPlaying) {
                        addBlock();
                    }
                });
                
                // Make the game area clickable to add blocks
                document.querySelector('.game-area').addEventListener('click', function() {
                    if (isPlaying) {
                        addBlock();
                    }
                });
                
                // Update probabilities based on current bet
                updateProbabilities();
            }
            
            // Set view mode (standard or pro)
            function setViewMode(mode) {
                viewMode = mode;
                localStorage.setItem('dragonTowerViewMode', mode);
                
                if (mode === 'standard') {
                    document.body.classList.remove('pro-view-active');
                    standardViewBtn.classList.add('active');
                    proViewBtn.classList.remove('active');
                } else {
                    document.body.classList.add('pro-view-active');
                    standardViewBtn.classList.remove('active');
                    proViewBtn.classList.add('active');
                }
            }
            
            function createStars() {
                const starsContainer = document.createElement('div');
                starsContainer.className = 'stars';
                
                for (let i = 0; i < 50; i++) {
                    const star = document.createElement('div');
                    star.className = 'star';
                    
                    // Random size and position
                    const size = Math.random() * 3 + 1;
                    star.style.width = `${size}px`;
                    star.style.height = `${size}px`;
                    star.style.top = `${Math.random() * 70}%`;  // Keep stars in top 70% of sky
                    star.style.left = `${Math.random() * 100}%`;
                    
                    // Random twinkle animation delay
                    star.style.animationDelay = `${Math.random() * 3}s`;
                    
                    starsContainer.appendChild(star);
                }
                
                document.querySelector('.game-area').appendChild(starsContainer);
            }
            
            // Start the game
            function startGame() {
                if (isPlaying) return;
                
                const betAmount = parseInt(betInput.value);
                
                if (isNaN(betAmount) || betAmount <= 0) {
                    alert('Please enter a valid bet amount');
                    return;
                }
                
                if (betAmount > balance) {
                    alert('Not enough GA for this bet');
                    return;
                }
                
                // Place bet
                currentBet = betAmount;
                balance -= betAmount;
                
                resetGameState();
                isPlaying = true;
                
                // Update UI
                updateStatsDisplay();
                betValue.textContent = currentBet;
                updatePotentialWin();
                
                // Enable game controls
                cashoutBtn.disabled = false;
                glueBtn.disabled = false;
                startGameBtn.disabled = true;
                betInput.disabled = true;
                
                // Start doom timer
                startDoomTimer();
                
                // Add base block
                addBaseBlock();
                
                // Log game start in attack log
                addAttackLogEntry('Game started', 'Now');
                
                // Update probabilities
                updateProbabilities();
            }
            
            // Reset game state
            function resetGameState() {
                floor = 0;
                tilt = 0;
                prevTilt = 0;
                tiltDirection = 'neutral';
                tiltDelta = 0;
                dragonRage = 0;
                groundShakeInterval = 8;
                lastBlockTime = 0;
                glueActive = false;
                glueBlocksLeft = 0;
                blockCounts = {
                    normal: 0,
                    gold: 0,
                    cursed: 0,
                    weak: 0
                };
                tiltHistory = [];
                attackHistory = [];
                
                // Clear tower
                towerContainer.innerHTML = '';
                
                // Clear tilt chart
                tiltChart.innerHTML = '<div class="tilt-graph-line"></div>';
                
                // Update UI
                updateFloorDisplay();
                updateTiltDisplay();
                updateRageMeter();
                updateTiltMeter();
                updateProgressFill();
                updateBlockCounts();
                
                // Hide game message
                gameMessage.style.display = 'none';
                
                // Reset Pro View stats
                updateRiskLevel(0);
                tiltDirectionValue.textContent = 'Neutral';
                tiltDeltaValue.textContent = '0.0°/s';
                stabilityValue.textContent = '100%';
                
                // Reset attack log
                attackLog.innerHTML = '';
            }
            
            // Reset game to start over
            function resetGame() {
                clearInterval(intervalTimer);
                clearInterval(doomTimerInterval);
                
                gameMessage.style.display = 'none';
                startGameBtn.disabled = false;
                betInput.disabled = false;
                cashoutBtn.disabled = true;
                glueBtn.disabled = true;
                currentBet = 0;
                betValue.textContent = '0';
                potentialWinValue.textContent = '0';
                
                resetGameState();
            }
            
            // Add base block to start the tower
            function addBaseBlock() {
                const block = document.createElement('div');
                block.className = 'tower-block';
                block.textContent = 'BASE';
                block.dataset.type = 'normal';
                block.style.width = '140px'; // Base block is wider
                
                towerContainer.appendChild(block);
                
                // Update counts
                blockCounts.normal++;
                updateBlockCounts();
                
                // Update floor
                floor = 1;
                updateFloorDisplay();
                updateProgressFill();
                
                // Add first tilt history point
                addTiltHistoryPoint(0);
                
                // Update Pro View stats
                updateRiskLevel(0);
            }
            
            // Cash out function
            function cashOut() {
                if (!isPlaying || floor < 1) return;
                
                // Calculate winnings based on floor reached
                const multiplier = Math.max(1.0, 1.0 + (floor * 0.2)); // 1.2x per floor
                const winnings = Math.floor(currentBet * multiplier);
                
                balance += winnings;
                
                // Update stats
                gameStats.totalGames++;
                gameStats.wins++;
                gameStats.totalWon += winnings;
                
                if (winnings > gameStats.biggestWin) {
                    gameStats.biggestWin = winnings;
                }
                
                if (floor > gameStats.highestFloor) {
                    gameStats.highestFloor = floor;
                }
                
                saveGameStats();
                saveBalance();
                
                // End game
                isPlaying = false;
                clearInterval(intervalTimer);
                clearInterval(doomTimerInterval);
                
                // Show success message
                alert(`Cashed out! You won ${winnings} GA (${multiplier.toFixed(1)}x multiplier)`);
                
                // Reset game
                resetGame();
            }
            
            // Update potential win display
            function updatePotentialWin() {
                if (currentBet > 0) {
                    const multiplier = Math.max(1.0, 1.0 + (floor * 0.2));
                    const potential = Math.floor(currentBet * multiplier);
                    potentialWinValue.textContent = potential;
                } else {
                    potentialWinValue.textContent = '0';
                }
            }
            
            // Add a new block to the tower
            function addBlock() {
                if (!isPlaying) return;
                
                // Check for cooldown
                const now = Date.now();
                if (now - lastBlockTime < placementCooldown) {
                    return;
                }
                lastBlockTime = now;
                
                // 25% chance of mis-tap placing blocks off-center (increased from 15%)
                const misTap = Math.random() < 0.25;
                
                // Create new block
                const block = document.createElement('div');
                block.className = 'tower-block';
                
                // Determine block type (increased weak blocks for more failure)
                const goldBlock = Math.random() < 0.12; // Reduced from 0.15 to 0.12
                const cursedBlock = Math.random() < 0.15; // Increased from 0.10 to 0.15
                const weakBlock = Math.random() < 0.40; // Increased from 0.30 to 0.40
                
                if (goldBlock) {
                    block.classList.add('gold');
                    block.textContent = '+100';
                    block.dataset.type = 'gold';
                    blockCounts.gold++;
                } else if (cursedBlock) {
                    block.classList.add('cursed');
                    block.textContent = '-50';
                    block.dataset.type = 'cursed';
                    blockCounts.cursed++;
                } else if (weakBlock) {
                    block.classList.add('weak');
                    block.textContent = floor + 1;
                    block.dataset.type = 'weak';
                    blockCounts.weak++;
                } else {
                    block.textContent = floor + 1;
                    block.dataset.type = 'normal';
                    blockCounts.normal++;
                }
                
                // Update block counts
                updateBlockCounts();
                
                // Apply mis-tap offset
                if (misTap) {
                    const offset = (Math.random() * 30) - 15;
                    block.style.marginLeft = `${offset}px`;
                }
                
                // Store previous tilt for direction calculation
                prevTilt = tilt;
                
                // Calculate tilt
                let newTilt = calculateTilt(floor);
                
                // Apply Magic Glue effect if active
                if (glueActive) {
                    newTilt *= 0.3; // Reduce tilt by 70%
                    glueBlocksLeft--;
                    
                    if (glueBlocksLeft <= 0) {
                        glueActive = false;
                        addAttackLogEntry('Magic Glue expired', 'Now');
                    }
                }
                
                // Add random tilt direction
                if (Math.random() < 0.5) {
                    newTilt = -newTilt;
                }
                
                // Apply tilt to the tower
                tilt += newTilt;
                
                // Calculate tilt delta and direction
                tiltDelta = tilt - prevTilt;
                if (tiltDelta > 0.1) {
                    tiltDirection = 'right';
                } else if (tiltDelta < -0.1) {
                    tiltDirection = 'left';
                } else {
                    tiltDirection = 'neutral';
                }
                
                // Apply tilt to the block
                block.style.transform = `rotate(${tilt}deg)`;
                
                // Add the block to the tower
                towerContainer.appendChild(block);
                
                // Update game state
                floor++;
                
                // Apply effects for different block types
                if (goldBlock) {
                    dragonRage += 30; // Increase dragon rage for gold blocks
                } else if (cursedBlock) {
                    // Random tilt surge for cursed blocks
                    const tiltSurge = Math.random() * 5 + 5;
                    tilt += Math.random() < 0.5 ? tiltSurge : -tiltSurge;
                }
                
                // Increase dragon rage
                dragonRage += 2;
                
                // Check if weak block is bearing too much weight
                if (weakBlock && floor >= 3) {
                    setTimeout(() => {
                        if (Math.random() < 0.3) {
                            block.classList.add('falling');
                            setTimeout(() => {
                                block.remove();
                                // Update block counts
                                blockCounts.weak--;
                                updateBlockCounts();
                                // This may cause a cascade failure
                                checkTowerIntegrity();
                                // Log weak block failure
                                addAttackLogEntry('Weak block failed', 'Now');
                            }, 1000);
                        }
                    }, 2000);
                }
                
                // Update UI
                updateFloorDisplay();
                updateTiltDisplay();
                updateRageMeter();
                updateTiltMeter();
                updateProgressFill();
                updatePotentialWin();
                updateProStats();
                
                // Add tilt history point
                addTiltHistoryPoint(tilt);
                
                // Check for dragon rage event
                if (dragonRage >= 100) {
                    triggerDragonFrenzy();
                }
                
                // Check if tilt is too high
                if (Math.abs(tilt) > 25) {
                    // Show "stable" indicator as a fake signal of safety
                    if (Math.abs(tilt) > 20) {
                        showStableIndicator();
                    }
                    
                    // Wait a bit for dramatic effect, then collapse
                    setTimeout(collapseTower, 500);
                    return;
                }
                
                // Check for Wing Blaster attack
                if (floor % 10 === 0 && Math.random() < 0.25) {
                    triggerWingBlaster();
                }
                
                // Check for Fire Breather attack
                if (floor >= 20 && Math.random() < 0.1) {
                    triggerFireBreather();
                }
                
                // Fake "stable" indicator as a deceptive signal
                if (Math.random() < 0.15 && Math.abs(tilt) > 15) {
                    showStableIndicator();
                }
                
                // Update probabilities
                updateProbabilities();
                
                // Easter egg: If they somehow reach floor 50, trigger nightmare mode
                if (floor === 50) {
                    triggerNightmareMode();
                }
            }
            
            // Calculate tilt based on current height
            function calculateTilt(currentHeight) {
                const baseTilt = Math.random() * 22; // Random 5-22° tilt per block (increased from 18°)
                const heightPenalty = Math.pow(currentHeight, 2) * 0.004; // height² × 0.4% tilt multiplier (increased from 0.3%)
                return baseTilt + heightPenalty; // Always favors overbalance
            }
            
            // Start doom timer for Ground Shaker attacks
            function startDoomTimer() {
                doomTimerValue = groundShakeInterval;
                updateDoomTimer();
                
                doomTimerInterval = setInterval(() => {
                    doomTimerValue--;
                    
                    if (doomTimerValue <= 0) {
                        // Trigger Ground Shaker attack
                        triggerGroundShaker();
                        
                        // Reset timer with reduced interval
                        groundShakeInterval = Math.max(3, groundShakeInterval - 0.5);
                        doomTimerValue = groundShakeInterval;
                    }
                    
                    updateDoomTimer();
                }, 1000);
            }
            
            // Trigger Ground Shaker attack (destroys base blocks)
            function triggerGroundShaker() {
                if (!isPlaying) return;
                
                // Show attack indication
                showAttackIndicator(dragonGreen, 'GROUND SHAKE!');
                
                // Animate dragon
                dragonGreen.classList.add('attacking');
                setTimeout(() => {
                    dragonGreen.classList.remove('attacking');
                }, 2000);
                
                // Shake the screen
                document.querySelector('.game-area').style.animation = 'shake 0.5s';
                setTimeout(() => {
                    document.querySelector('.game-area').style.animation = '';
                }, 500);
                
                // Target base blocks (first 3 blocks)
                const blocks = towerContainer.querySelectorAll('.tower-block');
                if (blocks.length > 3) {
                    // Get random block from the first 3 blocks
                    const targetIndex = Math.floor(Math.random() * Math.min(3, blocks.length));
                    blocks[targetIndex].classList.add('falling');
                    
                    // Update block counts
                    const blockType = blocks[targetIndex].dataset.type;
                    if (blockType) {
                        blockCounts[blockType]--;
                        updateBlockCounts();
                    }
                    
                    // Log attack
                    addAttackLogEntry('Ground Shaker', 'Now');
                    
                    // Remove block after animation
                    setTimeout(() => {
                        blocks[targetIndex].remove();
                        
                        // Check tower integrity after block removal
                        checkTowerIntegrity();
                    }, 1000);
                }
            }
            
            // Trigger Wing Blaster attack (knocks off top blocks)
            function triggerWingBlaster() {
                if (!isPlaying) return;
                
                // Show attack indication
                showAttackIndicator(dragonBlue, 'WING BLAST!');
                
                // Animate dragon
                dragonBlue.classList.add('attacking');
                setTimeout(() => {
                    dragonBlue.classList.remove('attacking');
                }, 2000);
                
                // Target top block
                const blocks = towerContainer.querySelectorAll('.tower-block');
                if (blocks.length > 0) {
                    const topBlock = blocks[blocks.length - 1];
                    topBlock.classList.add('falling');
                    
                    // Update block counts
                    const blockType = topBlock.dataset.type;
                    if (blockType) {
                        blockCounts[blockType]--;
                        updateBlockCounts();
                    }
                    
                    // Log attack
                    addAttackLogEntry('Wing Blaster', 'Now');
                    
                    // Remove block after animation
                    setTimeout(() => {
                        topBlock.remove();
                        floor--;
                        updateFloorDisplay();
                        
                        // Check tower integrity after block removal
                        checkTowerIntegrity();
                    }, 1000);
                }
            }
            
            // Trigger Fire Breather attack (burns entire sections)
            function triggerFireBreather() {
                if (!isPlaying) return;
                
                // Show attack indication
                showAttackIndicator(dragonRed, 'FIRE BREATH!');
                
                // Animate dragon
                dragonRed.classList.add('attacking');
                setTimeout(() => {
                    dragonRed.classList.remove('attacking');
                }, 2000);
                
                // Target middle section of the tower
                const blocks = towerContainer.querySelectorAll('.tower-block');
                if (blocks.length > 5) {
                    const startIndex = Math.floor(blocks.length / 3);
                    const endIndex = Math.min(blocks.length - 1, startIndex + Math.floor(blocks.length / 3));
                    
                    // Burn blocks
                    for (let i = startIndex; i <= endIndex; i++) {
                        blocks[i].classList.add('burning');
                    }
                    
                    // Log attack
                    addAttackLogEntry('Fire Breath', 'Now');
                    
                    // Check tower integrity after burning
                    setTimeout(() => {
                        // 90% chance of section destruction
                        if (Math.random() < 0.9) {
                            for (let i = startIndex; i <= endIndex; i++) {
                                blocks[i].classList.add('falling');
                                
                                // Update block counts
                                const blockType = blocks[i].dataset.type;
                                if (blockType) {
                                    blockCounts[blockType]--;
                                }
                            }
                            updateBlockCounts();
                            
                            // Remove blocks after animation
                            setTimeout(() => {
                                // We need to get blocks again as the DOM might have changed
                                const updatedBlocks = towerContainer.querySelectorAll('.tower-block');
                                let removedCount = 0;
                                for (let i = 0; i < updatedBlocks.length; i++) {
                                    if (updatedBlocks[i].classList.contains('falling')) {
                                        updatedBlocks[i].remove();
                                        removedCount++;
                                    }
                                }
                                
                                // Update floor
                                floor -= removedCount;
                                floor = Math.max(0, floor);
                                updateFloorDisplay();
                                
                                // Check tower integrity after block removal
                                checkTowerIntegrity();
                            }, 1000);
                        }
                    }, 2000);
                }
            }
            
            // Trigger Dragon Frenzy (all dragons attack at once)
            function triggerDragonFrenzy() {
                if (!isPlaying) return;
                
                // Show attack indication
                showAttackIndicator({ top: '50%', left: '50%' }, 'DRAGON FRENZY!', true);
                
                // Animate all dragons
                dragonRed.classList.add('attacking');
                dragonBlue.classList.add('attacking');
                dragonGreen.classList.add('attacking');
                
                setTimeout(() => {
                    dragonRed.classList.remove('attacking');
                    dragonBlue.classList.remove('attacking');
                    dragonGreen.classList.remove('attacking');
                }, 2000);
                
                // Log attack
                addAttackLogEntry('Dragon Frenzy', 'Now');
                
                // Trigger all attacks
                setTimeout(() => {
                    triggerGroundShaker();
                }, 500);
                
                setTimeout(() => {
                    triggerWingBlaster();
                }, 1500);
                
                setTimeout(() => {
                    triggerFireBreather();
                }, 2500);
                
                // Reset dragon rage
                dragonRage = 0;
                updateRageMeter();
            }
            
            // Check tower integrity after block removal
            function checkTowerIntegrity() {
                // Count how many blocks were destroyed in the last 10 seconds
                const blocks = towerContainer.querySelectorAll('.tower-block');
                
                // If 4+ blocks were destroyed in the last 10 seconds, trigger collapse (reduced from 5+)
                if (blocks.length < Math.max(1, floor - 4)) {
                    collapseTower();
                    return;
                }
                
                // If tilt is too high, trigger collapse (reduced from 25° to 23°)
                if (Math.abs(tilt) > 23) {
                    // Show "stable" indicator as a fake signal of safety if close to collapse
                    if (Math.abs(tilt) > 20) {
                        showStableIndicator();
                    }
                    
                    collapseTower();
                    return;
                }
                
                // Update Pro View stats after integrity check
                updateProStats();
            }
            
            // Collapse the tower (game over)
            function collapseTower() {
                if (!isPlaying) return;
                
                isPlaying = false;
                
                // Stop timers
                clearInterval(intervalTimer);
                clearInterval(doomTimerInterval);
                
                // Animate all blocks falling
                const blocks = towerContainer.querySelectorAll('.tower-block');
                blocks.forEach((block, index) => {
                    setTimeout(() => {
                        block.classList.add('falling');
                    }, index * 100);
                });
                
                // Log collapse
                addAttackLogEntry('Tower Collapsed', 'Now');
                
                // Update stats
                gameStats.totalGames++;
                
                if (floor > gameStats.highestFloor) {
                    gameStats.highestFloor = floor;
                }
                
                saveGameStats();
                saveBalance();
                updateStatsDisplay();
                
                // Show game over message after animation
                setTimeout(() => {
                    finalFloor.textContent = floor;
                    finalScore.textContent = `Lost ${currentBet} GA`;
                    gameMessage.style.display = 'block';
                    
                    // Enable retry button
                    startGameBtn.disabled = false;
                    betInput.disabled = false;
                    cashoutBtn.disabled = true;
                    glueBtn.disabled = true;
                }, 2000);
            }
            
            // Use Magic Glue to stabilize tower
            function useGlue() {
                if (!isPlaying || balance < 50) return;
                
                // Deduct GA
                balance -= 50;
                saveBalance();
                updateStatsDisplay();
                
                // Activate glue effect
                glueActive = true;
                glueBlocksLeft = 3;
                
                // Show effect
                showAttackIndicator({ top: '50%', left: '50%' }, 'MAGIC GLUE ACTIVE!', false, 'success');
                
                // Log glue use
                addAttackLogEntry('Magic Glue activated', 'Now');
            }
            
            // Show attack indicator
            function showAttackIndicator(dragon, text, big = false, type = 'danger') {
                const indicator = document.createElement('div');
                indicator.className = 'attack-indicator';
                indicator.textContent = text;
                
                if (big) {
                    indicator.style.fontSize = '1.5rem';
                    indicator.style.padding = '0.5rem 1rem';
                }
                
                if (type === 'success') {
                    indicator.style.background = 'rgba(76, 175, 80, 0.7)';
                }
                
                // Position indicator near the dragon
                if (dragon === dragonRed) {
                    indicator.style.top = '25%';
                    indicator.style.right = '15%';
                } else if (dragon === dragonBlue) {
                    indicator.style.top = '45%';
                    indicator.style.left = '15%';
                } else if (dragon === dragonGreen) {
                    indicator.style.bottom = '25%';
                    indicator.style.right = '20%';
                } else {
                    // Custom position
                    indicator.style.top = dragon.top;
                    indicator.style.left = dragon.left;
                    indicator.style.transform = 'translate(-50%, -50%)';
                }
                
                document.querySelector('.game-area').appendChild(indicator);
                
                // Remove indicator after animation
                setTimeout(() => {
                    indicator.remove();
                }, 2000);
            }
            
            // Show stable indicator (deceptive)
            function showStableIndicator() {
                stableIndicator.style.display = 'block';
                
                setTimeout(() => {
                    stableIndicator.style.display = 'none';
                }, 1000);
            }
            
            // Trigger nightmare mode (easter egg)
            function triggerNightmareMode() {
                // Make all dragons attack every 2 seconds
                intervalTimer = setInterval(() => {
                    const randomAttack = Math.random();
                    if (randomAttack < 0.33) {
                        triggerGroundShaker();
                    } else if (randomAttack < 0.66) {
                        triggerWingBlaster();
                    } else {
                        triggerFireBreather();
                    }
                }, 2000);
                
                // Log nightmare mode
                addAttackLogEntry('NIGHTMARE MODE', 'Now');
            }
            
            // Create ghost towers showing where other players failed
            function createGhostTowers() {
                const ghostHeights = [
                    { height: 12, x: 0.2, y: 0.3 },
                    { height: 18, x: 0.7, y: 0.4 },
                    { height: 7, x: 0.3, y: 0.2 },
                    { height: 23, x: 0.8, y: 0.6 },
                    { height: 15, x: 0.5, y: 0.5 }
                ];
                
                ghostHeights.forEach(ghost => {
                    const ghostTower = document.createElement('div');
                    ghostTower.className = 'ghost-tower';
                    ghostTower.dataset.height = ghost.height;
                    ghostTower.style.setProperty('--position-x', ghost.x);
                    ghostTower.style.setProperty('--position-y', ghost.y);
                    
                    ghostTowers.appendChild(ghostTower);
                });
            }
            
            // Create clouds for visual effect
            function createClouds() {
                for (let i = 0; i < 5; i++) {
                    const cloud = document.createElement('div');
                    cloud.className = 'cloud';
                    
                    // Random size and position
                    const size = 50 + Math.random() * 100;
                    cloud.style.width = `${size}px`;
                    cloud.style.height = `${size * 0.6}px`;
                    cloud.style.top = `${Math.random() * 100}%`;
                    cloud.style.left = `${Math.random() * 100}%`;
                    
                    // Animation
                    const duration = 30 + Math.random() * 30;
                    cloud.style.animation = `cloudMove ${duration}s linear infinite`;
                    cloud.style.animationDelay = `-${Math.random() * 30}s`;
                    
                    clouds.appendChild(cloud);
                }
            }
            
            // Pro View Functions
            
            // Add entry to attack log
            function addAttackLogEntry(attackType, time) {
                const entry = document.createElement('div');
                entry.className = 'attack-log-entry';
                entry.innerHTML = `
                    <span class="attack-type">${attackType}</span>
                    <span class="attack-time">${time}</span>
                `;
                
                attackLog.prepend(entry);
                
                // Limit log entries
                const entries = attackLog.querySelectorAll('.attack-log-entry');
                if (entries.length > 10) {
                    entries[entries.length - 1].remove();
                }
                
                // Save to attack history
                attackHistory.push({
                    type: attackType,
                    time: new Date(),
                    floor: floor
                });
            }
            
            // Add tilt history point
            function addTiltHistoryPoint(tiltValue) {
                // Save to tilt history
                tiltHistory.push({
                    tilt: tiltValue,
                    floor: floor,
                    time: new Date()
                });
                
                // Only update chart in Pro View
                if (viewMode === 'pro') {
                    // Add point to chart
                    const point = document.createElement('div');
                    point.className = 'tilt-graph-point';
                    
                    // Calculate position based on tilt and history
                    const xPercent = (tiltHistory.length / 30) * 100;
                    const yPercent = 50 + (tiltValue / 30) * 50;
                    
                    point.style.left = `${Math.min(100, xPercent)}%`;
                    point.style.top = `${Math.min(100, Math.max(0, yPercent))}%`;
                    
                    // Color based on danger
                    if (Math.abs(tiltValue) > 20) {
                        point.style.background = '#f44336';
                    } else if (Math.abs(tiltValue) > 15) {
                        point.style.background = '#ff9800';
                    } else if (Math.abs(tiltValue) > 10) {
                        point.style.background = '#ffc107';
                    }
                    
                    tiltChart.appendChild(point);
                    
                    // Limit points
                    const points = tiltChart.querySelectorAll('.tilt-graph-point');
                    if (points.length > 30) {
                        points[0].remove();
                        
                        // Reposition remaining points
                        points.forEach((p, i) => {
                            if (i > 0) {
                                const newXPercent = ((i - 1) / 30) * 100;
                                p.style.left = `${Math.min(100, newXPercent)}%`;
                            }
                        });
                    }
                }
            }
            
            // Update block counts display
            function updateBlockCounts() {
                normalBlockCount.textContent = blockCounts.normal;
                goldBlockCount.textContent = blockCounts.gold;
                cursedBlockCount.textContent = blockCounts.cursed;
                weakBlockCount.textContent = blockCounts.weak;
            }
            
            // Update Pro View statistics
            function updateProStats() {
                // Calculate risk level
                const tiltRisk = Math.abs(tilt) / 25;
                const heightRisk = floor / 50;
                const weakBlockRisk = blockCounts.weak / (blockCounts.normal + blockCounts.gold + blockCounts.cursed + blockCounts.weak);
                const totalRisk = (tiltRisk * 0.5) + (heightRisk * 0.3) + (weakBlockRisk * 0.2);
                
                updateRiskLevel(totalRisk);
                
                // Update tilt direction
                tiltDirectionValue.textContent = tiltDirection === 'right' ? 'Right' : 
                                              tiltDirection === 'left' ? 'Left' : 'Neutral';
                
                // Update tilt delta
                tiltDeltaValue.textContent = `${Math.abs(tiltDelta).toFixed(1)}°/s`;
                
                // Update stability score
                const stability = Math.max(0, 100 - (totalRisk * 100));
                stabilityValue.textContent = `${Math.round(stability)}%`;
            }
            
            // Update risk level display
            function updateRiskLevel(riskValue) {
                let riskText, riskClass;
                
                if (riskValue < 0.25) {
                    riskText = 'Low';
                    riskClass = 'risk-low';
                } else if (riskValue < 0.5) {
                    riskText = 'Medium';
                    riskClass = 'risk-medium';
                } else if (riskValue < 0.75) {
                    riskText = 'High';
                    riskClass = 'risk-high';
                } else {
                    riskText = 'Extreme';
                    riskClass = 'risk-extreme';
                }
                
                riskValue.innerHTML = `<span class="risk-level ${riskClass}">${riskText}</span>`;
            }
            
            // Update probabilities table
            function updateProbabilities() {
                // These probabilities are deliberately misleading
                // to make players think they have better chances
                
                // Get real probability based on current floor
                const realProbability10 = Math.max(0, 85 - (floor * 2));
                const realProbability20 = Math.max(0, 40 - (floor * 1.5));
                const realProbability30 = Math.max(0, 15 - (floor * 1));
                const realProbability50 = Math.max(0, 1 - (floor * 0.05));
                
                // Display optimistic probabilities
                const displayProb10 = Math.min(99, realProbability10 + 15);
                const displayProb20 = Math.min(95, realProbability20 + 25);
                const displayProb30 = Math.min(80, realProbability30 + 35);
                const displayProb50 = Math.min(50, realProbability50 + 15);
                
                // Calculate payouts
                const payout10 = 1 + (10 * 0.2);
                const payout20 = 1 + (20 * 0.2);
                const payout30 = 1 + (30 * 0.2);
                const payout50 = 1 + (50 * 0.2);
                
                // Update display
                document.getElementById('prob10').textContent = `${Math.round(displayProb10)}%`;
                document.getElementById('prob20').textContent = `${Math.round(displayProb20)}%`;
                document.getElementById('prob30').textContent = `${Math.round(displayProb30)}%`;
                document.getElementById('prob50').textContent = `${Math.round(displayProb50)}%`;
                
                document.getElementById('payout10').textContent = `${payout10.toFixed(1)}x`;
                document.getElementById('payout20').textContent = `${payout20.toFixed(1)}x`;
                document.getElementById('payout30').textContent = `${payout30.toFixed(1)}x`;
                document.getElementById('payout50').textContent = `${payout50.toFixed(1)}x`;
            }
            
            // Pro Button Functions
            
            // Analyze Game button
            function analyzeGame() {
                if (!isPlaying) return;
                
                // Create analysis text
                let analysisResult;
                
                if (Math.abs(tilt) > 20) {
                    analysisResult = "WARNING: Tower extremely unstable! Cash out immediately!";
                } else if (Math.abs(tilt) > 15) {
                    analysisResult = "Caution: Tower stability decreasing. Consider cashing out soon.";
                } else if (blockCounts.weak > 3) {
                    analysisResult = "Warning: Multiple weak blocks detected. Structure integrity compromised.";
                } else if (dragonRage > 70) {
                    analysisResult = "Alert: Dragon rage high. Expect attack soon.";
                } else {
                    analysisResult = "Analysis: Tower structure stable. Continue building for higher rewards.";
                }
                
                // Show analysis
                showAttackIndicator({ top: '40%', left: '50%' }, analysisResult, false, 'success');
                
                // Add analysis to log
                addAttackLogEntry('Analysis', 'Now');
            }
            
            // Stabilize Tower button
            function stabilizeTower() {
                if (!isPlaying || balance < 100) return;
                
                // Deduct GA
                balance -= 100;
                saveBalance();
                updateStatsDisplay();
                
                // Apply stabilization effect (reduce tilt by 40%)
                tilt *= 0.6;
                
                // Apply visual effect to blocks
                const blocks = towerContainer.querySelectorAll('.tower-block');
                blocks.forEach(block => {
                    block.style.transform = `rotate(${tilt}deg)`;
                });
                
                // Show effect
                showAttackIndicator({ top: '50%', left: '50%' }, 'TOWER STABILIZED!', false, 'success');
                
                // Update UI
                updateTiltDisplay();
                updateTiltMeter();
                updateProStats();
                
                // Add stabilization to log
                addAttackLogEntry('Tower Stabilized', 'Now');
                
                // Add tilt history point
                addTiltHistoryPoint(tilt);
            }
            
            // Predict Failure button
            function predictFailure() {
                if (!isPlaying) return;
                
                // Calculate prediction
                const tiltFactor = Math.abs(tilt) / 25;
                const heightFactor = floor / 50;
                const dragonFactor = dragonRage / 100;
                
                // Get all risk factors
                const totalRisk = (tiltFactor * 0.4) + (heightFactor * 0.3) + (dragonFactor * 0.3);
                
                // Calculate predicted floors before failure
                let predictedFloors;
                
                if (totalRisk > 0.8) {
                    predictedFloors = "1-2";
                } else if (totalRisk > 0.6) {
                    predictedFloors = "3-5";
                } else if (totalRisk > 0.4) {
                    predictedFloors = "6-10";
                } else if (totalRisk > 0.2) {
                    predictedFloors = "11-15";
                } else {
                    predictedFloors = "16+";
                }
                
                // This is deliberately misleading
                // Make it optimistic to encourage continued play
                if (totalRisk > 0.7) {
                    // No change, it's too risky to lie
                } else {
                    // Make prediction more optimistic
                    if (predictedFloors === "3-5") predictedFloors = "4-7";
                    else if (predictedFloors === "6-10") predictedFloors = "8-12";
                    else if (predictedFloors === "11-15") predictedFloors = "15-20";
                    else if (predictedFloors === "16+") predictedFloors = "20+";
                }
                
                // Show prediction
                showAttackIndicator({ top: '40%', left: '50%' }, `Predicted safe floors: ${predictedFloors}`, false, 'success');
                
                // Add prediction to log
                addAttackLogEntry(`Prediction: ${predictedFloors} floors`, 'Now');
            }
            
            // Update UI displays
            function updateFloorDisplay() {
                currentFloor.textContent = floor;
            }
            
            function updateTiltDisplay() {
                tiltValue.textContent = Math.abs(Math.round(tilt)) + '°';
                
                // Make tilt red when it's dangerous
                if (Math.abs(tilt) > 15) {
                    tiltValue.classList.add('danger');
                } else {
                    tiltValue.classList.remove('danger');
                }
            }
            
            function updateRageMeter() {
                rageMeter.style.width = `${dragonRage}%`;
                
                // Make rage meter pulse when it's high
                if (dragonRage > 70) {
                    rageMeter.classList.add('danger');
                } else {
                    rageMeter.classList.remove('danger');
                }
            }
            
            function updateTiltMeter() {
                // Severely underestimate the tilt to make it seem safer than it is
                const displayTilt = Math.abs(tilt) * 0.6; // Reduced for more deception
                tiltMeter.style.height = `${Math.min(100, displayTilt * 4)}%`;
            }
            
            function updateDoomTimer() {
                doomTimer.textContent = doomTimerValue.toFixed(1) + 's';
            }
            
            function updateProgressFill() {
                // Fake progress to make 50 floors seem achievable
                // But actually exaggerate progress so they feel closer to winning
                const fakeTotalFloors = 60;
                const fakeProgress = (floor / fakeTotalFloors) * 100;
                
                // Show 90% complete when they reach floor 30 (actually only halfway)
                progressFill.style.height = `${Math.min(100, fakeProgress * 1.5)}%`;
            }
            
            // Initialize the game
            initGame();
        });
    </script>
</body>
</html>