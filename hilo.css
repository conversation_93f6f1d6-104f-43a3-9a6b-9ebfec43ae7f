/* Hilo - Higher or Lower Card Game - Enhanced Mobile & Pro View */

:root {
    --primary-dark: #1a1a2e;
    --primary-medium: #16213e;
    --primary-light: #0f3460;
    --accent-green: #4caf50;
    --accent-green-dark: #388e3c;
    --accent-red: #f44336;
    --accent-red-dark: #d32f2f;
    --accent-blue: #2196f3;
    --accent-blue-dark: #1976d2;
    --accent-gold: #ffc107;
    --accent-gold-dark: #ffa000;
    --accent-purple: #9c27b0;
    --accent-purple-dark: #7b1fa2;
    --text-light: #f8f9fa;
    --text-medium: #e9ecef;
    --text-dark: #212529;
    --card-bg: #ffffff;
    --card-border: #cccccc;
    --success-green: #4caf50;
    --error-red: #f44336;
    --border-radius: 8px;
    --border-radius-large: 12px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    --box-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    
    /* Mobile-specific variables */
    --mobile-padding: 12px;
    --mobile-gap: 8px;
    --touch-target: 44px;
    --card-width-mobile: 120px;
    --card-height-mobile: 180px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Montserrat', sans-serif;
    background-color: var(--primary-dark);
    color: var(--text-light);
    line-height: 1.6;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%231a1a2e"/><path d="M25,0 L25,100 M50,0 L50,100 M75,0 L75,100" stroke="%2316213e" stroke-width="0.5" opacity="0.1"/><path d="M0,25 L100,25 M0,50 L100,50 M0,75 L100,75" stroke="%2316213e" stroke-width="0.5" opacity="0.1"/></svg>');
    overflow-x: hidden;
}

/* Pro View Mode Toggle */
body.pro-view-active .pro-analytics {
    display: block !important;
}

body.pro-view-active .info-panel {
    order: 2;
}

body.pro-view-active .game-section {
    order: 1;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header - Mobile First */
.game-header {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-gap);
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--primary-light);
    background: linear-gradient(0deg, transparent, rgba(33, 150, 243, 0.05), transparent);
}

.header-left {
    order: 1;
}

.header-center {
    order: 2;
    text-align: center;
}

.header-right {
    order: 3;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--mobile-gap);
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
    padding: 8px;
    border-radius: var(--border-radius);
}

.back-link i {
    margin-right: 6px;
}

.back-link:hover {
    color: var(--accent-blue);
    background: rgba(33, 150, 243, 0.1);
}

.back-text {
    display: none;
}

.game-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: 14px;
    font-weight: 300;
    color: var(--text-light);
    opacity: 0.8;
}

/* View Mode Toggle */
.view-mode-toggle {
    display: flex;
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 2px;
    border: 1px solid var(--primary-light);
}

.view-btn {
    background: transparent;
    border: none;
    color: var(--text-medium);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: var(--transition-fast);
    min-height: var(--touch-target);
}

.view-btn.active {
    background: var(--accent-blue);
    color: var(--text-light);
}

.view-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
}

.view-text {
    display: none;
}

.info-btn, .verify-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    transition: var(--transition);
    min-height: var(--touch-target);
    gap: 4px;
}

.info-btn:hover, .verify-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-blue);
}

.verify-btn {
    border-color: var(--accent-blue-dark);
    color: var(--accent-blue-dark);
}

.btn-text {
    display: none;
}

/* Mobile Status Bar */
.mobile-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    margin-bottom: 12px;
    box-shadow: var(--box-shadow-light);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-item i {
    color: var(--accent-blue);
}

.status-item.multiplier {
    color: var(--accent-gold);
    font-weight: 600;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 16px;
}

/* Game Section */
.game-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Game Board - Mobile First */
.game-board {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius-large);
    padding: 16px;
    box-shadow: var(--box-shadow);
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 16px;
    min-height: 320px;
}

/* Deck Area - Mobile Optimized */
.deck-area {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
}

.deck {
    width: 50px;
    height: 75px;
    position: relative;
}

.deck .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 6px;
    background: var(--card-bg);
    border: 2px solid var(--card-border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.card-back-design {
    width: 100%;
    height: 100%;
    background-color: var(--accent-blue-dark);
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><rect width="40" height="40" fill="%231976d2"/><path d="M0,0 L40,40 M0,40 L40,0" stroke="%232196f3" stroke-width="2"/></svg>');
}

.deck-info {
    font-size: 10px;
    color: var(--text-medium);
    text-align: center;
}

/* Round Display - Mobile Layout */
.round-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-right: 60px;
}

.round-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.round-number {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-light);
}

.streak-counter {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: var(--accent-gold);
}

.streak-counter i {
    color: var(--accent-gold);
}

/* GA Currency Display */
.ga-currency-display {
    display: none; /* Hidden on mobile, shown in status bar */
}

/* Cards Area - Mobile First */
.cards-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 12px 0;
}

.current-card-container, .next-card-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.current-card-label, .next-card-label {
    font-size: 12px;
    color: var(--text-medium);
    font-weight: 500;
}

.vs-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--accent-blue);
    border-radius: 50%;
    color: var(--text-light);
    font-weight: 700;
    font-size: 14px;
    order: 1;
}

.card-slot {
    width: var(--card-width-mobile);
    height: var(--card-height-mobile);
    border-radius: var(--border-radius-large);
    background: rgba(0, 0, 0, 0.2);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-medium);
    font-size: 12px;
}

/* Card Styling - Mobile Optimized */
.card {
    width: 100%;
    height: 100%;
    border-radius: var(--border-radius-large);
    background: var(--card-bg);
    border: 2px solid var(--card-border);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.card-red {
    color: var(--accent-red);
}

.card-black {
    color: var(--text-dark);
}

.card-value {
    font-size: 36px;
    font-weight: 700;
}

.card-suit {
    position: absolute;
    font-size: 18px;
    line-height: 1;
}

.card-suit.top-left {
    top: 8px;
    left: 8px;
}

.card-suit.bottom-right {
    bottom: 8px;
    right: 8px;
    transform: rotate(180deg);
}

/* Prediction Display */
.prediction-display {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 40px;
}

.prediction-result {
    background: rgba(0, 0, 0, 0.2);
    padding: 8px 16px;
    border-radius: var(--border-radius);
    text-align: center;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.prediction-result.hidden {
    opacity: 0;
}

.result-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.result-text.success {
    color: var(--success-green);
}

.result-text.failure {
    color: var(--error-red);
}

.result-details {
    font-size: 12px;
    color: var(--text-medium);
}

/* Game Controls - Mobile First */
.game-controls {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius-large);
    padding: 16px;
    box-shadow: var(--box-shadow);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Probability Displays */
.probability-displays {
    display: flex;
    gap: 8px;
}

.probability-display {
    flex: 1;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.probability-display.higher {
    border-left: 3px solid var(--accent-green);
}

.probability-display.lower {
    border-left: 3px solid var(--accent-red);
}

.probability-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.probability-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.probability-percent {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-light);
}

.multiplier {
    font-size: 12px;
    color: var(--accent-gold);
    background: rgba(255, 193, 7, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

/* Action Buttons - Mobile Optimized */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn {
    padding: 12px;
    border-radius: var(--border-radius);
    border: none;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    min-height: var(--touch-target);
}

.higher-btn {
    background: var(--accent-green);
    color: var(--text-light);
}

.higher-btn:hover:not(:disabled) {
    background: var(--accent-green-dark);
    transform: translateY(-2px);
}

.lower-btn {
    background: var(--accent-red);
    color: var(--text-light);
}

.lower-btn:hover:not(:disabled) {
    background: var(--accent-red-dark);
    transform: translateY(-2px);
}

.cashout-btn {
    background: var(--accent-gold);
    color: var(--text-dark);
}

.cashout-btn:hover:not(:disabled) {
    background: var(--accent-gold-dark);
    transform: translateY(-2px);
}

.cashout-btn:disabled {
    background: #d4d4d4;
    color: #8d8d8d;
    cursor: not-allowed;
    transform: none;
}

.new-game-btn {
    background: var(--accent-blue);
    color: var(--text-light);
}

.new-game-btn:hover:not(:disabled) {
    background: var(--accent-blue-dark);
    transform: translateY(-2px);
}

.cashout-multiplier {
    background: rgba(0, 0, 0, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

.action-btn.hidden {
    display: none;
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* GA Bet Controls - Mobile Enhanced */
.ga-bet-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: rgba(0, 0, 0, 0.2);
    padding: 12px;
    border-radius: var(--border-radius);
}

.ga-bet-input-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

#gaBetInput {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    border: 2px solid var(--accent-blue);
    border-radius: var(--border-radius);
    background-color: var(--primary-dark);
    color: var(--text-light);
    text-align: center;
    min-height: var(--touch-target);
}

.quick-bet-buttons {
    display: flex;
    gap: 6px;
}

.quick-bet {
    flex: 1;
    padding: 8px 4px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid var(--accent-blue);
    border-radius: var(--border-radius);
    background: transparent;
    color: var(--accent-blue);
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-bet:hover {
    background: var(--accent-blue);
    color: var(--text-light);
}

.ga-bet-buttons {
    display: flex;
    gap: 8px;
}

.ga-bet-btn {
    flex: 1;
    padding: 12px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: var(--transition);
    min-height: var(--touch-target);
}

.ga-bet-btn.place-bet {
    background-color: var(--success-green);
    color: var(--text-light);
}

.ga-bet-btn.place-bet:hover {
    background-color: var(--accent-green-dark);
}

.ga-bet-btn.withdraw-bet {
    background-color: var(--error-red);
    color: var(--text-light);
}

.ga-bet-btn.withdraw-bet:hover {
    background-color: var(--accent-red-dark);
}

/* Total Multiplier */
.total-multiplier {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 8px;
}

.multiplier-label {
    font-size: 12px;
    color: var(--text-medium);
}

.multiplier-value {
    font-size: 20px;
    font-weight: 600;
    color: var(--accent-gold);
}

/* Pro Analytics - Hidden by default, shown in Pro View */
.pro-analytics {
    display: none;
    background: linear-gradient(135deg, var(--accent-purple), var(--accent-purple-dark));
    border-radius: var(--border-radius-large);
    padding: 16px;
    box-shadow: var(--box-shadow);
    margin-top: 12px;
}

.analytics-section h3,
.card-probability-chart h3 {
    color: var(--text-light);
    font-size: 16px;
    margin-bottom: 12px;
    text-align: center;
}

.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 16px;
}

.analytics-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
    text-align: center;
}

.analytics-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.analytics-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
}

.chart-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 12px;
    margin-bottom: 12px;
    display: flex;
    justify-content: center;
}

.remaining-cards h4 {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 8px;
    text-align: center;
}

.remaining-cards-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
}

.remaining-card-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    padding: 4px;
    text-align: center;
    font-size: 10px;
    color: var(--text-light);
}

/* Game History - Mobile Optimized */
.game-history {
    background: var(--primary-medium);
    border-radius: var(--border-radius-large);
    padding: 12px;
    box-shadow: var(--box-shadow);
    display: flex;
    flex-direction: column;
    max-height: 200px;
}

.game-history h3 {
    font-size: 14px;
    color: var(--accent-blue);
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#historyRoundInfo {
    font-size: 12px;
    color: var(--text-medium);
}

.history-entries {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
}

.history-entry {
    padding: 6px 8px;
    margin-bottom: 6px;
    background: rgba(26, 26, 46, 0.5);
    border-radius: var(--border-radius);
    font-size: 12px;
    border-left: 3px solid var(--primary-light);
}

.history-entry.higher {
    border-left-color: var(--accent-green);
}

.history-entry.lower {
    border-left-color: var(--accent-red);
}

.history-entry.success {
    border-left-color: var(--success-green);
}

.history-entry.failure {
    border-left-color: var(--error-red);
}

.history-card {
    display: inline-block;
    padding: 2px 4px;
    margin: 0 2px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    font-weight: 600;
    font-size: 11px;
}

.history-card.red {
    color: var(--accent-red);
}

.history-card.black {
    color: var(--text-light);
}

.history-multiplier {
    color: var(--accent-gold);
    font-weight: 600;
}

/* Info Panel - Mobile Hidden by default */
.info-panel {
    display: none;
    flex-direction: column;
    gap: 12px;
}

.info-section {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--primary-light);
}

.info-section h3 {
    background: var(--primary-dark);
    color: var(--accent-blue);
    padding: 8px 12px;
    font-size: 14px;
    border-bottom: 1px solid var(--primary-light);
    text-align: center;
}

/* Stats Section */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    padding: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(0, 0, 0, 0.1);
    padding: 8px;
    border-radius: var(--border-radius);
}

.stat-label {
    font-size: 10px;
    color: var(--text-medium);
    text-align: center;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--accent-blue);
}

/* Fairness Section */
.fairness-info {
    padding: 12px;
}

.fairness-info p {
    font-size: 12px;
    margin-bottom: 8px;
    color: var(--text-medium);
}

.seed-info {
    display: flex;
    flex-direction: column;
    margin-bottom: 6px;
    background: rgba(26, 26, 46, 0.5);
    padding: 6px;
    border-radius: var(--border-radius);
}

.seed-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 3px;
}

.seed-value {
    font-size: 10px;
    color: var(--accent-blue);
    word-break: break-all;
    font-family: monospace;
}

.seed-value.hash {
    color: var(--accent-green);
}

.fairness-note {
    font-size: 10px !important;
    font-style: italic;
    margin-top: 4px !important;
}

/* Card Values Section */
.card-values {
    padding: 12px;
}

.card-value-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-value-row:last-child {
    border-bottom: none;
}

.card-value-item {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.mini-suit {
    font-size: 12px;
    margin-left: 3px;
}

.value-number {
    font-size: 12px;
    color: var(--accent-blue);
}

.small-cards {
    font-size: 12px;
}

.suits-note, .tie-note {
    padding: 8px 12px;
    font-size: 10px;
    color: var(--text-medium);
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.tie-note {
    font-style: italic;
    color: var(--accent-red-dark);
}

/* Modal Styling - Mobile First */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 16px;
}

.modal-content {
    background: var(--primary-medium);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--accent-blue-dark);
}

.modal-header {
    background: var(--primary-dark);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--accent-blue-dark);
}

.modal-header h2 {
    color: var(--accent-blue);
    font-size: 18px;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    width: var(--touch-target);
    height: var(--touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal-btn:hover {
    color: var(--accent-blue);
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 16px;
    overflow-y: auto;
    max-height: calc(90vh - 60px);
}

.tutorial-section, .verify-section {
    margin-bottom: 16px;
}

.tutorial-section h3, .verify-section h3 {
    color: var(--accent-blue);
    font-size: 16px;
    margin-bottom: 8px;
    border: none;
    padding: 0;
    text-align: left;
}

.tutorial-section p, .tutorial-section li, .verify-section p, .verify-section li {
    font-size: 13px;
    margin-bottom: 4px;
    color: var(--text-light);
}

.tutorial-section ul, .tutorial-section ol, .verify-section ul, .verify-section ol {
    padding-left: 16px;
    margin-top: 4px;
}

/* Verification Modal */
.verification-tool {
    background: rgba(26, 26, 46, 0.5);
    padding: 12px;
    border-radius: var(--border-radius);
    margin-top: 16px;
}

.verification-inputs {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-size: 11px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.input-group input, .input-group select {
    padding: 8px 10px;
    background: var(--primary-dark);
    border: 1px solid var(--primary-light);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 12px;
    font-family: monospace;
}

.verify-submit-btn {
    width: 100%;
    padding: 10px;
    background: var(--accent-blue);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    min-height: var(--touch-target);
}

.verify-submit-btn:hover {
    background: var(--accent-blue-dark);
}

.verification-result {
    margin-top: 12px;
    padding: 12px;
    background: var(--primary-dark);
    border-radius: var(--border-radius);
}

.result-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.result-content {
    font-size: 11px;
    color: var(--accent-blue);
    white-space: pre-wrap;
    font-family: monospace;
}

.past-rounds {
    max-height: 150px;
    overflow-y: auto;
}

.round-item {
    padding: 8px;
    margin-bottom: 6px;
    background: rgba(26, 26, 46, 0.5);
    border-radius: var(--border-radius);
    font-size: 11px;
    border-left: 3px solid var(--primary-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.round-item.win {
    border-left-color: var(--success-green);
}

.round-item.loss {
    border-left-color: var(--error-red);
}

.round-info {
    flex: 1;
}

.round-id {
    font-weight: 500;
    color: var(--accent-blue);
    margin-bottom: 2px;
}

.round-details {
    font-size: 10px;
    color: var(--text-medium);
}

.verify-round-btn {
    padding: 4px 8px;
    background: var(--primary-dark);
    border: 1px solid var(--accent-blue);
    border-radius: var(--border-radius);
    color: var(--accent-blue);
    font-size: 10px;
    cursor: pointer;
    transition: var(--transition);
}

.verify-round-btn:hover {
    background: var(--primary-light);
}

.no-rounds {
    text-align: center;
    padding: 16px;
    color: var(--text-medium);
    font-style: italic;
    font-size: 12px;
}

/* Result Modal */
.result-animation {
    background: rgba(26, 26, 46, 0.95);
    border-radius: var(--border-radius-large);
    padding: 24px;
    text-align: center;
    max-width: 350px;
    border: 2px solid var(--accent-blue);
    box-shadow: 0 0 30px rgba(33, 150, 243, 0.4);
}

.result-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--accent-blue);
    margin-bottom: 16px;
    text-shadow: 0 0 10px rgba(33, 150, 243, 0.6);
}

.result-title.success {
    color: var(--success-green);
    text-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
}

.result-title.failure {
    color: var(--error-red);
    text-shadow: 0 0 10px rgba(244, 67, 54, 0.6);
}

.result-image {
    width: 80px;
    height: 80px;
    margin: 0 auto 16px;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.result-message {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 24px;
    line-height: 1.5;
}

.result-multiplier {
    margin-top: 12px;
    font-size: 18px;
    color: var(--accent-gold);
}

.result-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.continue-btn, .result-actions .cashout-btn {
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    min-height: var(--touch-target);
}

.continue-btn {
    background: var(--accent-blue);
    color: var(--text-light);
}

.continue-btn:hover {
    background: var(--accent-blue-dark);
    transform: translateY(-2px);
}

.result-actions .cashout-btn {
    background: var(--accent-gold);
    color: var(--text-dark);
}

.result-actions .cashout-btn:hover {
    background: var(--accent-gold-dark);
    transform: translateY(-2px);
}

.hidden {
    display: none !important;
}

/* Tablet Styles */
@media (min-width: 481px) {
    .mobile-status-bar {
        display: none;
    }
    
    .ga-currency-display {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        background-color: var(--primary-dark);
        padding: 0.75rem;
        border-radius: var(--border-radius);
        margin-top: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .ga-balance, .ga-bet {
        display: flex;
        align-items: center;
        font-size: 1rem;
        color: var(--text-light);
    }
    
    .ga-balance i, .ga-bet i {
        margin-right: 0.5rem;
        color: var(--accent-blue);
    }
    
    .cards-area {
        flex-direction: row;
        gap: 40px;
    }
    
    .vs-indicator {
        order: 0;
    }
    
    .card-slot {
        width: 140px;
        height: 210px;
    }
    
    .card-value {
        font-size: 48px;
    }
    
    .card-suit {
        font-size: 24px;
    }
    
    .action-buttons {
        flex-direction: row;
        gap: 12px;
    }
    
    .ga-bet-input-container {
        flex-direction: row;
        align-items: center;
        gap: 12px;
    }
    
    #gaBetInput {
        flex: 1;
    }
    
    .quick-bet-buttons {
        flex: none;
        width: auto;
    }
    
    .info-panel {
        display: flex;
    }
    
    .btn-text {
        display: inline;
    }
    
    .view-text {
        display: inline;
    }
    
    .back-text {
        display: inline;
    }
    
    .result-actions {
        flex-direction: row;
        gap: 12px;
    }
}

/* Desktop Styles */
@media (min-width: 769px) {
    .game-container {
        padding: 20px;
    }
    
    .game-header {
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
    }
    
    .header-left, .header-right {
        flex: 1;
    }
    
    .header-right {
        justify-content: flex-end;
    }
    
    .header-center {
        flex: 2;
    }
    
    .game-title {
        font-size: 36px;
        margin-bottom: 5px;
    }
    
    .game-subtitle {
        font-size: 16px;
    }
    
    .main-content {
        flex-direction: row;
        gap: 20px;
    }
    
    .game-section {
        flex: 2;
        gap: 15px;
    }
    
    .game-board {
        padding: 20px;
        min-height: 400px;
    }
    
    .deck-area {
        top: 20px;
        right: 20px;
        gap: 10px;
    }
    
    .deck {
        width: 80px;
        height: 120px;
    }
    
    .round-display {
        padding-right: 100px;
        gap: 10px;
    }
    
    .round-info {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .round-number {
        font-size: 18px;
    }
    
    .streak-counter {
        font-size: 16px;
    }
    
    .cards-area {
        gap: 60px;
        padding: 20px 0;
    }
    
    .card-slot {
        width: 160px;
        height: 240px;
    }
    
    .card-value {
        font-size: 60px;
    }
    
    .card-suit {
        font-size: 30px;
    }
    
    .card-suit.top-left {
        top: 10px;
        left: 10px;
    }
    
    .card-suit.bottom-right {
        bottom: 10px;
        right: 10px;
    }
    
    .game-controls {
        padding: 20px;
        gap: 15px;
    }
    
    .probability-displays {
        gap: 20px;
    }
    
    .probability-display {
        padding: 10px 15px;
    }
    
    .probability-value {
        flex-direction: row;
        gap: 10px;
    }
    
    .probability-percent {
        font-size: 18px;
    }
    
    .multiplier {
        font-size: 14px;
        padding: 2px 5px;
    }
    
    .ga-bet-controls {
        gap: 0.75rem;
        padding: 1rem;
    }
    
    .multiplier-value {
        font-size: 24px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;
    }
    
    .analytics-item {
        padding: 12px;
    }
    
    .analytics-label {
        font-size: 12px;
        margin-bottom: 6px;
    }
    
    .analytics-value {
        font-size: 16px;
    }
    
    .info-panel {
        max-width: 350px;
    }
    
    .stats-grid {
        padding: 15px;
        gap: 15px;
    }
    
    .stat-item {
        padding: 10px;
    }
    
    .stat-label {
        font-size: 12px;
    }
    
    .stat-value {
        font-size: 18px;
    }
    
    .modal-content {
        max-width: 700px;
    }
    
    .modal-header {
        padding: 15px;
    }
    
    .modal-header h2 {
        font-size: 20px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .verification-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .verify-submit-btn {
        grid-column: span 2;
    }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
    body.pro-view-active .main-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 20px;
    }
    
    body.pro-view-active .game-section {
        order: 1;
    }
    
    body.pro-view-active .info-panel {
        order: 2;
        max-width: none;
    }
    
    .pro-analytics {
        margin-top: 0;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .remaining-cards-grid {
        grid-template-columns: repeat(13, 1fr);
    }
}

/* Accessibility and Touch Improvements */
@media (hover: none) {
    .action-btn:hover {
        transform: none;
    }
    
    .ga-bet-btn:hover {
        background-color: inherit;
    }
    
    .quick-bet:hover {
        background: transparent;
        color: var(--accent-blue);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .card {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .game-board, .game-controls, .info-section {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Dark mode adjustments for OLED displays */
@media (prefers-contrast: high) {
    :root {
        --primary-dark: #000000;
        --primary-medium: #111111;
        --primary-light: #222222;
    }
}

/* Animation for view mode transitions */
.pro-analytics {
    transition: all 0.3s ease;
    transform: translateY(0);
}

.pro-analytics.hidden {
    transform: translateY(-10px);
    opacity: 0;
}

/* Focus styles for keyboard navigation */
button:focus,
input:focus {
    outline: 2px solid var(--accent-blue);
    outline-offset: 2px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-blue);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}