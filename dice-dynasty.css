/* DICE DYNASTY Game Styles */

:root {
    --primary-color: #d4af37; /* Imperial gold */
    --primary-dark: #a98a29;
    --primary-light: #f9e076;
    --accent-color: #8b0000; /* Deep red */
    --accent-light: #b30000;
    --bg-dark: #1a0a0c;
    --bg-medium: #241012;
    --bg-light: #321618;
    --text-light: #ffffff;
    --text-muted: #cccccc;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --border-color: rgba(212, 175, 55, 0.3);
    --success-color: #4a8c3f;
    --warning-color: #d97706;
    --error-color: #b91c1c;
    --font-main: 'Poppins', sans-serif;
    --font-accent: 'Cinzel', serif;
}

/* General styles */
.dice-dynasty-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px;
    font-family: var(--font-main);
    color: var(--text-light);
}

/* Game Header */
.dynasty-header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 10px;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    margin-right: auto;
}

.back-button:hover {
    color: var(--primary-color);
}

.dynasty-title {
    font-family: var(--font-accent);
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px var(--shadow-color);
    margin: 0;
    text-align: center;
}

/* View Toggle */
.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: var(--bg-dark);
    border-color: var(--primary-light);
    box-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
}

.view-btn:hover:not(.active) {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    color: var(--text-light);
}

/* Mobile Wallet (only shown on mobile) */
.mobile-wallet {
    display: none;
    background: linear-gradient(135deg, var(--bg-dark), var(--bg-medium));
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px var(--shadow-color);
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-color);
}

.wallet-balance, .mobile-wins {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1rem;
}

.wallet-balance i, .mobile-wins i {
    font-size: 1rem;
}

/* Game Description */
.dynasty-description {
    text-align: center;
    font-size: 0.95rem;
    color: var(--text-muted);
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Game Layout */
.game-layout {
    display: grid;
    grid-template-columns: 1fr 3fr 1fr;
    gap: 20px;
}

/* Info Sidebar */
.info-sidebar {
    background: var(--bg-medium);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 6px var(--shadow-color);
}

.sidebar-section {
    margin-bottom: 25px;
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-family: var(--font-accent);
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 15px;
    text-align: center;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.stats-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.stat-card {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Pro View Sections */
.pro-view-section {
    display: none;
}

body.pro-view-active .pro-view-section {
    display: block;
}

/* Betting Analysis */
.betting-analysis {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.analysis-card {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.analysis-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-bottom: 5px;
}

.analysis-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
}

.analysis-value.positive {
    color: var(--success-color);
}

.analysis-value.negative {
    color: var(--error-color);
}

/* Hand Analytics */
.hand-analytics-container {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--border-color);
}

.chart-container {
    height: 200px;
    margin-bottom: 15px;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.analytics-table th,
.analytics-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid rgba(212, 175, 55, 0.1);
}

.analytics-table th {
    color: var(--primary-color);
    font-weight: 600;
}

.analytics-table td:last-child {
    text-align: right;
}

/* Dynasty Hands Table */
.dynasty-hands {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 0.85rem;
}

.dynasty-hands th,
.dynasty-hands td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid rgba(212, 175, 55, 0.1);
}

.dynasty-hands th {
    color: var(--primary-color);
    font-weight: 600;
}

.dynasty-hands td:last-child {
    color: var(--primary-color);
    font-weight: 600;
    text-align: right;
}

/* Main Game Area */
.game-main {
    background: var(--bg-medium);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 6px var(--shadow-color);
    display: flex;
    flex-direction: column;
}

/* Game Status */
.game-status {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 20px;
    padding: 10px;
    border-radius: 8px;
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
}

.game-phase {
    font-family: var(--font-accent);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

/* Game Board */
.game-board {
    background: linear-gradient(135deg, var(--bg-light), var(--bg-medium));
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    box-shadow: inset 0 0 20px rgba(212, 175, 55, 0.1);
    position: relative;
    min-height: 250px;
}

.board-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="rgba(212,175,55,0.1)" stroke-width="1"/></svg>');
    background-size: 100px 100px;
    opacity: 0.2;
    border-radius: 10px;
    pointer-events: none;
}

/* Dice Area */
.dice-area {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin: 25px 0;
}

.dice-wrapper {
    position: relative;
    transition: transform 0.3s ease;
    perspective: 600px;
}

.dice-wrapper.held {
    transform: translateY(-10px);
}

.die {
    width: 70px;
    height: 70px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 1.5s ease;
    cursor: pointer;
}

.die.rolling {
    animation: diceRoll 1s linear infinite;
}

.die-face {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
    border: 2px solid var(--primary-color);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    backface-visibility: hidden;
}

.die-symbol {
    width: 80%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-dark);
    font-weight: bold;
    user-select: none;
}

.hold-button {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.hold-button:hover {
    background: var(--primary-dark);
    color: var(--text-light);
}

.hold-button.held {
    background: var(--primary-color);
    color: var(--bg-dark);
    font-weight: 600;
}

/* Bet Controls */
.bet-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--bg-light);
    border-radius: 8px;
    margin-top: 10px;
    border: 1px solid var(--border-color);
    flex-wrap: wrap;
    gap: 10px;
}

.bet-amount-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bet-amount-container label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.bet-input-group {
    display: flex;
    align-items: center;
}

.bet-control-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-medium);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
}

.bet-control-button:hover {
    background: var(--primary-dark);
}

.bet-input {
    width: 80px;
    height: 32px;
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    font-size: 1rem;
    text-align: center;
    padding: 0 5px;
    margin: 0 5px;
    border-radius: 4px;
}

.bet-quick-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.quick-bet-button {
    background: var(--bg-medium);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 5px 10px;
    font-size: 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.quick-bet-button:hover {
    background: var(--primary-dark);
    color: var(--text-light);
}

.quick-bet-button.max {
    background: var(--accent-color);
    color: var(--text-light);
    font-weight: 600;
}

.balance-display {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.balance-label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.balance-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.game-button {
    padding: 12px 20px;
    border-radius: 8px;
    font-family: var(--font-main);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: none;
    font-size: 1rem;
}

.roll-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--bg-dark);
    box-shadow: 0 4px 6px var(--shadow-color);
    flex: 2;
}

.roll-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px var(--shadow-color);
}

.roll-button:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 3px var(--shadow-color);
}

.roll-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.new-game-button {
    background: var(--bg-light);
    color: var(--text-light);
    border: 1px solid var(--border-color);
    flex: 1;
}

.new-game-button:hover {
    background: var(--bg-medium);
    border-color: var(--primary-color);
}

/* Mobile Game Controls */
.mobile-game-controls {
    display: none;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 10px;
    margin-top: 15px;
}

.mobile-button {
    padding: 12px;
    border-radius: 8px;
    font-family: var(--font-main);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
}

.mobile-button.roll-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--bg-dark);
    box-shadow: 0 4px 6px var(--shadow-color);
    grid-column: span 2;
}

.mobile-button.new-game-button,
.mobile-button.settings-button {
    background: var(--bg-light);
    color: var(--text-light);
    border: 1px solid var(--border-color);
}

/* Hand Result */
.hand-result {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px var(--shadow-color);
    transition: all 0.3s ease;
}

.hand-result.winning {
    background: rgba(74, 140, 63, 0.2);
    border: 1px solid var(--success-color);
    animation: pulse 1.5s infinite;
}

.result-title {
    font-family: var(--font-accent);
    font-size: 1.4rem;
    color: var(--primary-color);
    margin-bottom: 5px;
    font-weight: 600;
}

.result-title.winning {
    color: var(--success-color);
}

.result-multiplier {
    font-size: 1.2rem;
    color: var(--text-light);
}

/* Fairness Section */
.fairness-panel {
    background: var(--bg-medium);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 6px var(--shadow-color);
}

.seed-info {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
}

.seed-label {
    font-size: 0.9rem;
    color: var(--primary-color);
    margin-bottom: 5px;
    font-weight: 600;
}

.seed-value {
    background: var(--bg-dark);
    border-radius: 4px;
    padding: 8px;
    font-family: monospace;
    font-size: 0.8rem;
    color: var(--text-muted);
    word-break: break-all;
    margin-bottom: 10px;
    border: 1px solid rgba(212, 175, 55, 0.1);
}

.verify-container {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border: 1px solid var(--border-color);
}

.verify-input {
    width: 100%;
    background: var(--bg-dark);
    border: 1px solid rgba(212, 175, 55, 0.1);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 10px;
    color: var(--text-light);
    font-family: monospace;
    font-size: 0.9rem;
}

.verify-button {
    width: 100%;
    background: var(--primary-color);
    color: var(--bg-dark);
    border: none;
    border-radius: 4px;
    padding: 10px 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.verify-button:hover {
    background: var(--primary-light);
}

.verify-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    font-size: 0.9rem;
    display: none;
}

.verify-result.success {
    background: rgba(74, 140, 63, 0.2);
    border: 1px solid var(--success-color);
    color: var(--success-color);
}

.verify-result.error {
    background: rgba(185, 28, 28, 0.2);
    border: 1px solid var(--error-color);
    color: var(--error-color);
}

/* Game History */
.history-section {
    margin-top: 20px;
}

.history-list {
    max-height: 200px;
    overflow-y: auto;
    background: var(--bg-light);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid var(--border-color);
}

.history-item {
    background: var(--bg-dark);
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 8px;
    border: 1px solid rgba(212, 175, 55, 0.1);
    font-size: 0.85rem;
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-item.profit {
    border-left: 3px solid var(--success-color);
}

.history-item.loss {
    border-left: 3px solid var(--error-color);
}

.history-round {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.history-hand {
    color: var(--text-light);
}

.history-dice {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.history-bet {
    color: var(--text-muted);
    font-size: 0.8rem;
    font-weight: 600;
}

.profit .history-bet {
    color: var(--success-color);
}

.loss .history-bet {
    color: var(--error-color);
}

/* Toggle Button */
.toggle-button {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0;
    text-decoration: underline;
    margin-top: 10px;
    display: block;
}

/* Help Panel */
.help-content {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-muted);
    display: none;
}

.help-content h4 {
    color: var(--primary-color);
    margin-top: 15px;
    margin-bottom: 8px;
    font-size: 1rem;
}

.help-content p {
    margin-bottom: 10px;
}

.help-content ul {
    padding-left: 20px;
    margin-bottom: 10px;
}

.help-content li {
    margin-bottom: 5px;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    z-index: 1000;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    display: none;
    animation: slideIn 0.3s ease-out forwards;
}

.notification-info {
    background: var(--primary-color);
    color: var(--bg-dark);
}

.notification-success {
    background: var(--success-color);
    color: white;
}

.notification-error {
    background: var(--error-color);
    color: white;
}

/* Mobile Stats Toggle */
.mobile-stats-toggle {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

.mobile-panel-toggle {
    background: var(--primary-color);
    color: var(--bg-dark);
    border: none;
    border-radius: 50px;
    width: 150px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-panel-toggle:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
}

/* Mobile Stats Panel */
.mobile-stats-panel {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-medium);
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
    z-index: 200;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
}

.mobile-stats-panel.active {
    transform: translateY(0);
}

.mobile-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.mobile-panel-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-family: var(--font-accent);
}

.mobile-panel-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.2rem;
    cursor: pointer;
}

.mobile-panel-content {
    padding: 15px;
}

.mobile-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.mobile-stat-card {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.mobile-section-title {
    font-family: var(--font-accent);
    font-size: 1.1rem;
    color: var(--primary-color);
    margin: 15px 0 10px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

.mobile-history-list {
    background: var(--bg-light);
    border-radius: 8px;
    padding: 10px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 300;
    animation: fadeIn 0.3s ease-in-out;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-medium);
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    animation: scaleIn 0.3s ease-in-out;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--bg-light);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    color: var(--primary-color);
    font-family: var(--font-accent);
    font-size: 1.3rem;
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    line-height: 1;
}

.modal-body {
    padding: 20px;
}

.modal-form-group {
    margin-bottom: 20px;
}

.modal-form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.modal-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.modal-btn-minus,
.modal-btn-plus {
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    border-radius: 4px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-btn-minus:hover,
.modal-btn-plus:hover {
    background: var(--primary-dark);
}

.modal-input {
    flex: 1;
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    color: var(--primary-color);
    font-size: 1.1rem;
    text-align: center;
}

.modal-preset-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.modal-preset-btn {
    flex: 1;
    min-width: 60px;
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    border-radius: 4px;
    padding: 8px 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.modal-preset-btn:hover {
    background: var(--primary-dark);
}

.modal-preset-btn.max {
    background: var(--accent-color);
    color: var(--text-light);
    font-weight: 600;
}

.modal-apply-btn {
    width: 100%;
    background: var(--primary-color);
    color: var(--bg-dark);
    border: none;
    border-radius: 6px;
    padding: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-apply-btn:hover {
    background: var(--primary-light);
    transform: translateY(-2px);
}

/* Animations */
@keyframes diceRoll {
    0% { transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg); }
    100% { transform: rotateX(360deg) rotateY(720deg) rotateZ(360deg); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(74, 140, 63, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(74, 140, 63, 0); }
    100% { box-shadow: 0 0 0 0 rgba(74, 140, 63, 0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
    to { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

/* Die face positioning */
.die-face:nth-child(1) { transform: rotateY(0deg) translateZ(35px); }
.die-face:nth-child(2) { transform: rotateY(180deg) translateZ(35px); }
.die-face:nth-child(3) { transform: rotateY(90deg) translateZ(35px); }
.die-face:nth-child(4) { transform: rotateY(-90deg) translateZ(35px); }
.die-face:nth-child(5) { transform: rotateX(90deg) translateZ(35px); }
.die-face:nth-child(6) { transform: rotateX(-90deg) translateZ(35px); }

/* RESPONSIVE STYLES */

/* Large Desktop (Default) */
@media (min-width: 1200px) {
    .game-layout {
        grid-template-columns: 1fr 3fr 1fr;
    }
}

/* Desktop */
@media (max-width: 1200px) {
    .game-layout {
        grid-template-columns: 1fr 2fr;
    }
    
    .fairness-panel {
        grid-column: span 2;
        margin-top: 20px;
    }
    
    .dynasty-title {
        font-size: 2rem;
    }
}

/* Tablet */
@media (max-width: 992px) {
    .dice-area {
        gap: 15px;
    }
    
    .die {
        width: 60px;
        height: 60px;
    }
    
    .hold-button {
        bottom: -25px;
        font-size: 0.75rem;
    }
    
    .bet-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .balance-display {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .dice-dynasty-container {
        padding: 10px;
    }
    
    .dynasty-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dynasty-title {
        margin: 10px 0;
        font-size: 1.8rem;
    }
    
    .back-button {
        margin-right: 0;
    }
    
    .view-toggle {
        margin-top: 10px;
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
        text-align: center;
    }
    
    .game-layout {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .fairness-panel {
        grid-column: 1;
    }
    
    .mobile-wallet {
        display: flex;
    }
    
    .info-sidebar {
        padding: 12px;
    }
    
    .dice-area {
        gap: 10px;
    }
    
    .die {
        width: 50px;
        height: 50px;
    }
    
    .die-face {
        font-size: 1.5rem;
    }
    
    .hold-button {
        bottom: -22px;
        padding: 3px 6px;
        font-size: 0.7rem;
    }
    
    .game-controls {
        display: none;
    }
    
    .mobile-game-controls {
        display: grid;
    }
    
    .mobile-stats-toggle {
        display: block;
    }
    
    .mobile-stats-panel {
        display: block;
    }
}

/* Small Mobile */
@media (max-width: 576px) {
    .dynasty-title {
        font-size: 1.5rem;
    }
    
    .dynasty-description {
        font-size: 0.85rem;
    }
    
    .dice-area {
        gap: 5px;
    }
    
    .die {
        width: 40px;
        height: 40px;
    }
    
    .die-face {
        font-size: 1.2rem;
    }
    
    .hold-button {
        bottom: -20px;
        padding: 2px 4px;
        font-size: 0.6rem;
    }
    
    .stats-container,
    .betting-analysis {
        grid-template-columns: 1fr;
    }
    
    .bet-quick-buttons {
        flex-wrap: wrap;
    }
    
    .quick-bet-button {
        flex-basis: calc(50% - 5px);
    }
    
    .modal-content {
        width: 95%;
    }
}

/* Pro View Mode Styling */
body.pro-view-active .info-sidebar {
    display: block;
}

body.pro-view-active .game-layout {
    grid-template-columns: 1fr;
}

@media (min-width: 1200px) {
    body.pro-view-active .game-layout {
        grid-template-columns: 1fr 2fr 1fr;
    }
}

@media (max-width: 1200px) and (min-width: 768px) {
    body.pro-view-active .game-layout {
        grid-template-columns: 1fr 2fr;
    }
    
    body.pro-view-active .fairness-panel {
        grid-column: span 2;
    }
}

/* Touch device optimizations */
@media (hover: none) {
    .hold-button {
        opacity: 1;
        visibility: visible;
        bottom: -26px;
    }
    
    .die {
        margin-bottom: 10px;
    }
}