/* Flip Memory Game CSS */
:root {
    --primary-color: #0277bd;
    --secondary-color: #37474f;
    --danger-color: #d32f2f;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --trap-color: #7b1fa2;
    --gold-color: #FFD700;
}

.flip-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1rem;
}

.flip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.back-link {
    display: flex;
    align-items: center;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
    background: none;
    border: none;
    cursor: pointer;
    font-size: inherit;
    font-family: inherit;
}

.back-link:hover {
    color: #007bff;
}

.back-link i {
    margin-right: 0.5rem;
}

.game-title {
    font-size: 1.8rem;
    text-align: center;
    margin: 0;
    background: linear-gradient(45deg, #0277bd, #03a9f4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.view-mode-toggle {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    padding: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-left: auto;
}

.view-mode-toggle button {
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    background: transparent;
}

.view-mode-toggle button.active {
    background: #0277bd;
    color: white;
}

.bet-container {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.bet-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.bet-input-group {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 200px;
}

.bet-input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    text-align: center;
    transition: all 0.2s ease;
    margin-right: 0.5rem;
}

.bet-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(2, 119, 189, 0.2);
}

.bet-currency {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    background: white;
    transition: all 0.2s ease;
}

.bet-currency:hover {
    border-color: var(--gold-color);
}

.currency-balance {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #333;
}

.currency-icon {
    width: 20px;
    height: 20px;
}

.currency-amount {
    color: var(--gold-color);
}

.bet-quick-amounts {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-amount {
    padding: 0.5rem 1rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    text-align: center;
    flex: 1;
    min-width: 60px;
}

.quick-amount:hover {
    border-color: var(--gold-color);
    background: rgba(255, 215, 0, 0.1);
}

.potential-win {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
    border: 1px dashed var(--gold-color);
}

.potential-win-label {
    font-weight: 600;
    color: #333;
}

.potential-win-value {
    font-weight: 700;
    color: var(--gold-color);
    font-size: 1.2rem;
    text-shadow: 0 0 3px rgba(255, 215, 0, 0.3);
}

.game-dashboard {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.game-stats {
    display: flex;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    flex: 1;
    flex-wrap: wrap;
    min-width: 280px;
}

.stat-item {
    flex: 1;
    min-width: 80px;
    text-align: center;
    padding: 0.5rem;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    font-weight: 500;
    color: #555;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
}

.stat-value.negative {
    color: var(--danger-color);
    animation: pulse 1s infinite;
}

.stat-value.positive {
    color: var(--success-color);
}

.stat-value.gold {
    color: var(--gold-color);
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.game-controls {
    flex: 1;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 280px;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 44px;
}

.btn-primary {
    background: #4361ee;
    color: white;
    box-shadow: 0 4px 6px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover {
    background: #3a56d4;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(67, 97, 238, 0.4);
}

.btn-warning {
    background: #ff9800;
    color: white;
    box-shadow: 0 4px 6px rgba(255, 152, 0, 0.3);
}

.btn-warning:hover {
    background: #f57c00;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(255, 152, 0, 0.4);
}

.btn-danger {
    background: #f44336;
    color: white;
    box-shadow: 0 4px 6px rgba(244, 67, 54, 0.3);
}

.btn-danger:hover {
    background: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(244, 67, 54, 0.4);
}

.btn-gold {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
    box-shadow: 0 4px 6px rgba(255, 215, 0, 0.3);
}

.btn-gold:hover {
    background: linear-gradient(45deg, #FFC500, #FF8C00);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(255, 215, 0, 0.4);
}

.btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;
}

.timer-container {
    position: relative;
    margin-bottom: 1.5rem;
    text-align: center;
}

.timer {
    font-size: 2.5rem;
    font-weight: 900;
    color: #4caf50;
    text-shadow: 0 2px 8px rgba(0,0,0,0.3);
    transition: color 0.3s ease;
}

.timer.warning {
    color: #ff9800;
    animation: timerPulse 1s infinite;
}

.timer.danger {
    color: #f44336;
    animation: timerShake 0.5s infinite;
}

@keyframes timerPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes timerShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.probability-meter {
    margin: 1rem auto;
    width: 100%;
    max-width: 300px;
    height: 20px;
    background: #ddd;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.probability-fill {
    height: 100%;
    width: 0%;
    background: linear-gradient(to right, #f44336, #ff5722);
    transition: width 0.3s ease;
}

.probability-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    font-size: 0.8rem;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(6, 1fr);
    gap: 8px;
    max-width: 500px;
    margin: 1.5rem auto;
    background: rgba(0, 0, 0, 0.1);
    padding: 1rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.card {
    aspect-ratio: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    user-select: none;
    min-height: 44px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.card:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.card.flipped {
    transform: rotateY(180deg);
    background: white;
    color: #333;
    border: 2px solid #ddd;
}

.card.matched {
    background: linear-gradient(135deg, #4caf50, #8bc34a);
    transform: scale(0.95);
    opacity: 0.8;
}

.card.trap {
    background: linear-gradient(135deg, #7b1fa2, #e91e63);
    animation: trapPulse 2s infinite;
}

@keyframes trapPulse {
    0%, 100% { box-shadow: 0 0 10px rgba(123, 31, 162, 0.5); }
    50% { box-shadow: 0 0 20px rgba(123, 31, 162, 0.8); }
}

.card.error {
    background: linear-gradient(135deg, #f44336, #ff5722);
    animation: errorShake 0.5s;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.card.preview {
    animation: previewFlash 3s;
}

@keyframes previewFlash {
    0%, 90%, 100% { opacity: 1; }
    5%, 85% { opacity: 0.3; }
}

.card-value {
    position: absolute;
    top: 3px;
    right: 3px;
    font-size: 0.7rem;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 1px 4px;
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card.flipped .card-value {
    opacity: 1;
}

.card-content {
    font-size: 1.2rem;
}

.pro-view-stats {
    display: none;
}

.pro-view-active .pro-view-stats {
    display: block;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.pro-view-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.pro-view-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.pro-stat-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
}

.pro-stat-label {
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 0.2rem;
}

.pro-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #333;
}

.risk-level {
    font-weight: bold;
}

.risk-low {
    color: #4CAF50;
}

.risk-medium {
    color: #FFC107;
}

.risk-high {
    color: #FF5722;
}

.risk-extreme {
    color: #F44336;
}

.memory-heatmap {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
    margin-top: 0.5rem;
    width: 100%;
    max-width: 150px;
}

.heatmap-cell {
    aspect-ratio: 1;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.heatmap-cell.cold {
    background: #e3f2fd;
}

.heatmap-cell.warm {
    background: #bbdefb;
}

.heatmap-cell.hot {
    background: #64b5f6;
}

.heatmap-cell.burning {
    background: #1976d2;
}

.flip-history {
    max-height: 120px;
    overflow-y: auto;
    background: rgba(0,0,0,0.05);
    border-radius: 6px;
    padding: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.flip-entry {
    margin-bottom: 0.25rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
}

.flip-entry:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.flip-type {
    font-weight: 600;
}

.flip-result {
    color: #666;
}

.flip-result.success {
    color: #4CAF50;
}

.flip-result.failure {
    color: #F44336;
}

.trap-analytics {
    margin-top: 0.75rem;
}

.trap-counter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255,255,255,0.5);
    border-radius: 6px;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
}

.trap-icon {
    font-size: 1rem;
}

.trap-count {
    font-weight: 600;
    color: var(--trap-color);
}

.efficiency-meter {
    width: 100%;
    height: 8px;
    background: #ddd;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.efficiency-fill {
    height: 100%;
    background: linear-gradient(to right, #f44336, #ff9800, #4caf50);
    transition: width 0.3s ease;
}

.pro-controls {
    display: none;
    margin-top: 0.75rem;
}

.pro-view-active .pro-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.pro-btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    background: rgba(0,0,0,0.05);
    color: #333;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
}

.pro-btn:hover {
    background: rgba(0,0,0,0.1);
}

.pro-btn i {
    margin-right: 0.25rem;
}

.bonus-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 1rem 2rem;
    border-radius: 15px;
    font-size: 1.5rem;
    font-weight: bold;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 1000;
    display: none;
    animation: popupBounce 0.5s ease;
}

@keyframes popupBounce {
    0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
    50% { transform: translate(-50%, -50%) scale(1.2); }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

.blood-splatter {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 20%, rgba(139, 0, 0, 0.3) 80%);
    z-index: 999;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.blood-splatter.active {
    opacity: 1;
}

.taunt-message {
    position: fixed;
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-family: 'Comic Sans MS', cursive;
    font-size: 1.5rem;
    color: #f44336;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    z-index: 1001;
    opacity: 0;
    pointer-events: none;
    animation: tauntFade 2s ease;
}

@keyframes tauntFade {
    0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
    50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
}

.game-info {
    margin-top: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.info-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.info-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 0.75rem;
    text-align: center;
}

.info-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.3rem;
}

.info-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
}

.info-value.debt {
    color: var(--danger-color);
}

.tips-section {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 0.75rem;
}

.tips-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
    font-size: 0.9rem;
}

.tips-list {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.tips-list li {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
    font-size: 0.85rem;
}

.tips-list li::before {
    content: "💀";
    position: absolute;
    left: 0;
    top: 0;
}

.failure-rate {
    text-align: center;
    font-size: 0.8rem;
    color: #666;
    margin-top: 1rem;
    font-style: italic;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .flip-container {
        padding: 1rem;
    }
    
    .game-title {
        font-size: 1.8rem;
    }
}

@media (max-width: 768px) {
    .flip-header {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .game-title {
        font-size: 1.5rem;
        margin: 0 auto;
        order: 2;
    }
    
    .back-link {
        order: 1;
    }
    
    .view-mode-toggle {
        order: 3;
        margin-top: 0.5rem;
        margin-left: auto;
    }
    
    .game-dashboard {
        flex-direction: column;
    }
    
    .bet-controls {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .bet-input-group {
        width: 100%;
        min-width: unset;
    }
    
    .currency-balance {
        align-self: flex-start;
    }
    
    .bet-quick-amounts {
        justify-content: center;
    }
    
    .game-board {
        max-width: 350px;
        gap: 6px;
        padding: 0.75rem;
    }
    
    .card {
        font-size: 1rem;
    }
    
    .timer {
        font-size: 2rem;
    }
    
    .info-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .pro-view-active .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .flip-container {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1.2rem;
    }
    
    .game-board {
        max-width: 300px;
        gap: 4px;
        padding: 0.5rem;
    }
    
    .card {
        font-size: 0.9rem;
        min-height: 40px;
    }
    
    .card-value {
        font-size: 0.6rem;
        padding: 1px 3px;
    }
    
    .timer {
        font-size: 1.8rem;
    }
    
    .probability-meter {
        height: 16px;
    }
    
    .probability-label {
        font-size: 0.7rem;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 0.4rem;
    }
    
    .stat-label {
        font-size: 0.75rem;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .info-grid {
        gap: 0.5rem;
    }
    
    .info-item {
        padding: 0.5rem;
    }
    
    .info-label {
        font-size: 0.7rem;
    }
    
    .info-value {
        font-size: 1.1rem;
    }
    
    .tips-section {
        padding: 0.5rem;
    }
    
    .tips-title {
        font-size: 0.85rem;
    }
    
    .tips-list li {
        font-size: 0.8rem;
        margin-bottom: 0.4rem;
    }
    
    .bet-quick-amounts {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .quick-amount {
        padding: 0.4rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .btn {
        padding: 0.8rem 1.5rem;
        min-height: 48px;
    }
    
    .view-mode-toggle button {
        min-height: 36px;
        padding: 0.5rem 0.75rem;
    }
    
    .pro-btn {
        min-height: 44px;
    }
    
    .card {
        min-height: 48px;
    }
    
    .quick-amount {
        min-height: 44px;
    }
    
    input[type="number"] {
        min-height: 44px;
        font-size: 16px; /* Prevents iOS zoom */
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .back-link {
        color: #ddd;
    }
    
    .bet-container,
    .game-stats,
    .game-controls,
    .game-info,
    .pro-view-stats {
        background: rgba(30, 30, 30, 0.8);
    }
    
    .stat-item,
    .info-item,
    .tips-section,
    .pro-stat-item {
        background: rgba(50, 50, 50, 0.5);
    }
    
    .stat-label,
    .info-label,
    .tips-title,
    .info-title,
    .pro-stat-label,
    .pro-view-title {
        color: #aaa;
    }
    
    .stat-value,
    .info-value,
    .pro-stat-value {
        color: #eee;
    }
    
    .tips-list li,
    .flip-entry,
    .flip-result {
        color: #ddd;
    }
    
    .flip-result.success {
        color: #4CAF50;
    }
    
    .flip-result.failure {
        color: #F44336;
    }
    
    .pro-btn {
        background: rgba(255,255,255,0.1);
        color: #ddd;
    }
    
    .pro-btn:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .flip-history {
        background: rgba(255,255,255,0.1);
        color: #ddd;
    }
    
    .trap-counter {
        background: rgba(50, 50, 50, 0.5);
    }
}