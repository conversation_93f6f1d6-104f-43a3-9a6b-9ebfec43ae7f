/**
 * Bet Manager for Sports Betting System
 * 
 * This service manages all bet-related operations including:
 * - Selection of bets
 * - Calculation of potential payouts
 * - Placement of bets
 * - Tracking of bet history
 * - Resolution of bets
 */
class SportsBettingBetManager {
    constructor(options = {}) {
        // Configuration
        this.config = {
            initialBalance: options.initialBalance || 1000,
            baseUrl: options.baseUrl || '/api',
            ...options
        };
        
        // API instance
        this.api = new SportsBettingAPI({
            baseUrl: this.config.baseUrl
        });
        
        // User data
        this.userData = null;
        
        // Current selections
        this.selectedBets = [];
        
        // Betting mode
        this.betMode = 'single'; // 'single' or 'parlay'
        
        // Event listeners
        this.eventListeners = {
            'balance-updated': [],
            'bets-updated': [],
            'bet-placed': [],
            'bet-resolved': [],
            'bet-mode-changed': [],
            'error': []
        };
        
        // Initialize
        this.initialize();
    }
    
    /**
     * Initialize bet manager
     */
    async initialize() {
        try {
            // Try to load user data if token exists
            if (localStorage.getItem('auth_token')) {
                try {
                    this.userData = await this.api.getCurrentUser();
                } catch (error) {
                    console.warn('Failed to load user data, may need to log in again');
                    this.userData = { balance: this.config.initialBalance };
                }
            } else {
                // Use default user data if not logged in
                this.userData = { balance: this.config.initialBalance };
            }
        } catch (error) {
            console.error('Failed to initialize bet manager:', error);
            this.userData = { balance: this.config.initialBalance };
        }
    }
    
    /**
     * Get current user balance
     */
    getBalance() {
        return this.userData ? this.userData.balance : this.config.initialBalance;
    }
    
    /**
     * Update user balance
     */
    async updateBalance(forceRefresh = false) {
        try {
            if (localStorage.getItem('auth_token') && forceRefresh) {
                const balance = await this.api.getWalletBalance();
                const previousBalance = this.userData ? this.userData.balance : 0;
                
                if (!this.userData) {
                    this.userData = {};
                }
                
                this.userData.balance = balance;
                
                // Trigger event
                this.triggerEvent('balance-updated', {
                    previousBalance,
                    newBalance: balance,
                    change: balance - previousBalance
                });
                
                return balance;
            }
            
            return this.userData ? this.userData.balance : this.config.initialBalance;
        } catch (error) {
            console.error('Failed to update balance:', error);
            this.triggerEvent('error', { message: 'Failed to update balance', error });
            return this.userData ? this.userData.balance : this.config.initialBalance;
        }
    }
    
    /**
     * Get bet history
     */
    async getBetHistory(limit = null, offset = 0) {
        try {
            const options = {};
            
            if (limit !== null) {
                options.per_page = limit;
                options.page = Math.floor(offset / limit) + 1;
            }
            
            const response = await this.api.getBetHistory(options);
            return response.data || [];
        } catch (error) {
            console.error('Failed to get bet history:', error);
            this.triggerEvent('error', { message: 'Failed to get bet history', error });
            return [];
        }
    }
    
    /**
     * Get current selections
     */
    getSelectedBets() {
        return this.selectedBets;
    }
    
    /**
     * Add a bet selection
     */
    addBetSelection(matchId, betType, odds, match) {
        // Check if this bet is already selected
        const existingIndex = this.selectedBets.findIndex(bet => 
            bet.matchId === matchId && bet.betType === betType
        );
        
        if (existingIndex >= 0) {
            // Remove existing bet if already selected (toggle behavior)
            this.selectedBets.splice(existingIndex, 1);
        } else {
            // Add new bet selection
            const betDescription = this.getBetDescription(match, betType);
            
            this.selectedBets.push({
                matchId,
                betType,
                odds,
                description: betDescription,
                match: {
                    id: match.id,
                    homeTeam: match.homeTeam,
                    awayTeam: match.awayTeam,
                    commenceTime: match.commenceTime
                }
            });
        }
        
        // Trigger event
        this.triggerEvent('bets-updated', {
            selections: this.selectedBets
        });
        
        return this.selectedBets;
    }
    
    /**
     * Generate bet description based on match and bet type
     */
    getBetDescription(match, betType) {
        switch (betType) {
            case 'home':
                return `${match.homeTeam} to win vs ${match.awayTeam}`;
            case 'away':
                return `${match.awayTeam} to win vs ${match.homeTeam}`;
            case 'draw':
                return `${match.homeTeam} vs ${match.awayTeam} - Draw`;
            default:
                return 'Unknown bet type';
        }
    }
    
    /**
     * Remove a bet selection
     */
    removeBetSelection(matchId, betType) {
        const previousLength = this.selectedBets.length;
        
        this.selectedBets = this.selectedBets.filter(bet => 
            !(bet.matchId === matchId && bet.betType === betType)
        );
        
        if (previousLength !== this.selectedBets.length) {
            // Trigger event if something was removed
            this.triggerEvent('bets-updated', {
                selections: this.selectedBets
            });
        }
        
        return this.selectedBets;
    }
    
    /**
     * Clear all bet selections
     */
    clearBetSelections() {
        if (this.selectedBets.length > 0) {
            this.selectedBets = [];
            
            // Trigger event
            this.triggerEvent('bets-updated', {
                selections: this.selectedBets
            });
        }
        
        return this.selectedBets;
    }
    
    /**
     * Set betting mode (single or parlay)
     */
    setBetMode(mode) {
        if (mode === 'single' || mode === 'parlay') {
            this.betMode = mode;
            
            // Trigger event
            this.triggerEvent('bet-mode-changed', { mode });
            
            return true;
        }
        return false;
    }
    
    /**
     * Get current betting mode
     */
    getBetMode() {
        return this.betMode;
    }
    
    /**
     * Calculate total odds for current selections
     */
    calculateTotalOdds() {
        if (!this.selectedBets.length) {
            return 0;
        }
        
        if (this.betMode === 'parlay') {
            // For parlay bets, multiply all odds together
            const totalOdds = this.selectedBets.reduce((acc, bet) => acc * bet.odds, 1);
            return parseFloat(totalOdds.toFixed(2));
        } else {
            // For single bets, return the odds of the first selection
            return parseFloat(this.selectedBets[0].odds.toFixed(2));
        }
    }
    
    /**
     * Calculate potential payout for current selections
     */
    calculatePotentialPayout(betAmount) {
        if (!this.selectedBets.length || !betAmount) {
            return 0;
        }
        
        if (this.betMode === 'parlay') {
            // For parlay bets, multiply all odds together
            const totalOdds = this.selectedBets.reduce((acc, bet) => acc * bet.odds, 1);
            const payout = betAmount * totalOdds;
            return parseFloat(payout.toFixed(2));
        } else {
            // For single bets, calculate payout based on the first selection only
            const payout = betAmount * this.selectedBets[0].odds;
            return parseFloat(payout.toFixed(2));
        }
    }
    
    /**
     * Place a bet with the current selections
     */
    async placeBet(betAmount) {
        betAmount = parseFloat(betAmount);
        
        // Validate bet
        if (!this.selectedBets.length) {
            throw new Error('No bets selected');
        }
        
        if (isNaN(betAmount) || betAmount <= 0) {
            throw new Error('Invalid bet amount');
        }
        
        if (betAmount > this.getBalance()) {
            throw new Error('Insufficient balance');
        }
        
        try {
            // Format bet data for API
            let betData;
            
            if (this.betMode === 'single' && this.selectedBets.length > 1) {
                // In single mode with multiple selections, only place bet on first selection
                const firstBet = this.selectedBets[0];
                
                betData = {
                    amount: betAmount,
                    bet_type: 'single',
                    selections: [
                        {
                            match_id: firstBet.matchId,
                            bet_type: firstBet.betType,
                            odds: firstBet.odds
                        }
                    ]
                };
            } else {
                // Parlay mode or single bet
                betData = {
                    amount: betAmount,
                    bet_type: this.betMode,
                    selections: this.selectedBets.map(bet => ({
                        match_id: bet.matchId,
                        bet_type: bet.betType,
                        odds: bet.odds
                    }))
                };
            }
            
            // Place bet via API
            const result = await this.api.placeBet(betData);
            
            // Update user balance
            if (result.balance !== undefined) {
                const previousBalance = this.userData ? this.userData.balance : 0;
                
                if (!this.userData) {
                    this.userData = {};
                }
                
                this.userData.balance = result.balance;
                
                // Trigger balance updated event
                this.triggerEvent('balance-updated', {
                    previousBalance,
                    newBalance: result.balance,
                    change: result.balance - previousBalance,
                    reason: 'bet-placed'
                });
            }
            
            // Handle selections based on bet mode
            if (this.betMode === 'single' && this.selectedBets.length > 1) {
                // Only remove the first bet from selections in single mode with multiple selections
                this.selectedBets.shift();
                
                // Trigger bets updated event
                this.triggerEvent('bets-updated', {
                    selections: this.selectedBets
                });
            } else {
                // Clear all selections in parlay mode or when only one selection remains
                this.clearBetSelections();
            }
            
            // Trigger bet placed event
            this.triggerEvent('bet-placed', { bet: result.bet });
            
            return result.bet;
        } catch (error) {
            console.error('Failed to place bet:', error);
            this.triggerEvent('error', { message: 'Failed to place bet', error });
            throw error;
        }
    }
    
    /**
     * Cancel a bet
     */
    async cancelBet(betId) {
        try {
            const result = await this.api.cancelBet(betId);
            
            // Update user balance
            if (result.balance !== undefined) {
                const previousBalance = this.userData ? this.userData.balance : 0;
                
                if (!this.userData) {
                    this.userData = {};
                }
                
                this.userData.balance = result.balance;
                
                // Trigger balance updated event
                this.triggerEvent('balance-updated', {
                    previousBalance,
                    newBalance: result.balance,
                    change: result.balance - previousBalance,
                    reason: 'bet-canceled'
                });
            }
            
            // Trigger bet resolved event
            this.triggerEvent('bet-resolved', { bet: result.bet });
            
            return result.bet;
        } catch (error) {
            console.error('Failed to cancel bet:', error);
            this.triggerEvent('error', { message: 'Failed to cancel bet', error });
            throw error;
        }
    }
    
    /**
     * Add an event listener
     */
    addEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
    }
    
    /**
     * Remove an event listener
     */
    removeEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event]
                .filter(cb => cb !== callback);
        }
    }
    
    /**
     * Trigger an event
     */
    triggerEvent(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
}

// Export the bet manager
window.SportsBettingBetManager = SportsBettingBetManager;