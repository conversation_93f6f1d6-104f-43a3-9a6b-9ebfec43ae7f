// Hilo - Higher or Lower Card Game - Enhanced Mobile & Pro View
document.addEventListener('DOMContentLoaded', function() {
    console.log("Hilo game initializing...");
    
    // Game elements
    const elements = {
        // Header buttons
        tutorialBtn: document.getElementById('tutorialBtn'),
        verifyBtn: document.getElementById('verifyBtn'),
        standardViewBtn: document.getElementById('standardViewBtn'),
        proViewBtn: document.getElementById('proViewBtn'),
        
        // Modals
        tutorialModal: document.getElementById('tutorialModal'),
        closeTutorialBtn: document.getElementById('closeTutorialBtn'),
        verifyModal: document.getElementById('verifyModal'),
        closeVerifyBtn: document.getElementById('closeVerifyBtn'),
        resultModal: document.getElementById('resultModal'),
        
        // Mobile status bar
        mobileBalance: document.getElementById('mobileBalance'),
        mobileStreak: document.getElementById('mobileStreak'),
        mobileBet: document.getElementById('mobileBet'),
        mobileMultiplier: document.getElementById('mobileMultiplier'),
        
        // GA Currency elements
        gaCurrencyBalance: document.getElementById('gaCurrencyBalance'),
        gaBetAmount: document.getElementById('gaBetAmount'),
        gaBetInput: document.getElementById('gaBetInput'),
        gaPlaceBetBtn: document.getElementById('gaPlaceBetBtn'),
        gaWithdrawBtn: document.getElementById('gaWithdrawBtn'),
        
        // Game board elements
        cardsRemaining: document.getElementById('cardsRemaining'),
        roundNumber: document.getElementById('roundNumber'),
        streakCounter: document.getElementById('streakCounter'),
        currentCardSlot: document.getElementById('currentCardSlot'),
        nextCardSlot: document.getElementById('nextCardSlot'),
        predictionResult: document.getElementById('predictionResult'),
        resultText: document.getElementById('resultText'),
        resultDetails: document.getElementById('resultDetails'),
        
        // Action buttons
        predictHigherBtn: document.getElementById('predictHigherBtn'),
        predictLowerBtn: document.getElementById('predictLowerBtn'),
        cashOutBtn: document.getElementById('cashOutBtn'),
        newGameBtn: document.getElementById('newGameBtn'),
        
        // Probability displays
        higherProbability: document.getElementById('higherProbability'),
        higherMultiplier: document.getElementById('higherMultiplier'),
        lowerProbability: document.getElementById('lowerProbability'),
        lowerMultiplier: document.getElementById('lowerMultiplier'),
        totalMultiplier: document.getElementById('totalMultiplier'),
        cashoutMultiplier: document.getElementById('cashoutMultiplier'),
        
        // Statistics
        statTotalRounds: document.getElementById('statTotalRounds'),
        statCorrectPredictions: document.getElementById('statCorrectPredictions'),
        statHighestStreak: document.getElementById('statHighestStreak'),
        statHighestMultiplier: document.getElementById('statHighestMultiplier'),
        
        // Pro View Analytics
        winRate: document.getElementById('winRate'),
        avgMultiplier: document.getElementById('avgMultiplier'),
        totalProfit: document.getElementById('totalProfit'),
        riskLevel: document.getElementById('riskLevel'),
        probabilityChart: document.getElementById('probabilityChart'),
        remainingCardsGrid: document.getElementById('remainingCardsGrid'),
        
        // Fairness elements
        serverSeedHash: document.getElementById('serverSeedHash'),
        clientSeed: document.getElementById('clientSeed'),
        roundId: document.getElementById('roundId'),
        
        // History
        historyRoundInfo: document.getElementById('historyRoundInfo'),
        historyEntries: document.getElementById('historyEntries'),
        
        // Verification elements
        verifyServerSeed: document.getElementById('verifyServerSeed'),
        verifyClientSeed: document.getElementById('verifyClientSeed'),
        verifyRoundId: document.getElementById('verifyRoundId'),
        verifySubmitBtn: document.getElementById('verifySubmitBtn'),
        verificationContent: document.getElementById('verificationContent'),
        pastRounds: document.getElementById('pastRounds'),
        
        // Result modal elements
        resultTitle: document.getElementById('resultTitle'),
        resultMessage: document.getElementById('resultMessage'),
        resultMultiplier: document.getElementById('resultMultiplier'),
        resultContinueBtn: document.getElementById('resultContinueBtn'),
        resultCashoutBtn: document.getElementById('resultCashoutBtn')
    };
    
    // Game state
    let gameState = {
        deck: [],
        currentCardIndex: 0,
        currentStreak: 0,
        totalMultiplier: 1.0,
        roundNumber: 1,
        isRoundActive: false,
        serverSeed: null,
        serverSeedHash: null,
        clientSeed: null,
        predictions: [],
        roundHistory: [],
        viewMode: 'standard', // 'standard' or 'pro'
        
        // GA Currency system
        gaCurrency: 1000,           // Initial GA currency balance
        gaBetAmount: 0,             // Current GA bet
        gaBetActive: false,         // Whether a GA bet is active
        gaConversionRate: 10,       // 1 credit = 10 GA
        gaMinBet: 50,               // Minimum GA bet
        gaMaxBet: 5000,             // Maximum GA bet
        
        statistics: {
            totalRounds: 0,
            correctPredictions: 0,
            highestStreak: 0,
            highestMultiplier: 1.0,
            totalWagered: 0,
            totalWon: 0,
            totalProfit: 0,
            winRate: 0,
            avgMultiplier: 0,
            gamesPlayed: 0
        }
    };
    
    // Card configuration
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const cardValues = {
        '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
        'J': 11, 'Q': 12, 'K': 13, 'A': 14
    };
    
    // Initialize game
    init();
    
    function init() {
        console.log("Initializing Hilo game...");
        
        setupEventListeners();
        loadGameState();
        
        // Initialize the game with proper GA Currency setup
        gameState.roundNumber = 0; // Will be incremented to 1 in startNewRound
        
        startNewRound();
        updateStatistics();
        updateGACurrencyDisplay();
        updateMobileStatusBar();
        initProViewMode();
        
        addHistoryEntry("Welcome to Hilo! Place a GA bet and predict if the next card will be higher or lower than the current card.");
        
        // Initially disable prediction buttons until bet is placed
        if (elements.predictHigherBtn) elements.predictHigherBtn.disabled = true;
        if (elements.predictLowerBtn) elements.predictLowerBtn.disabled = true;
    }
    
    function setupEventListeners() {
        // View mode toggle
        elements.standardViewBtn?.addEventListener('click', () => setViewMode('standard'));
        elements.proViewBtn?.addEventListener('click', () => setViewMode('pro'));
        
        // Modal controls
        elements.tutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, true));
        elements.closeTutorialBtn?.addEventListener('click', () => toggleModal(elements.tutorialModal, false));
        elements.verifyBtn?.addEventListener('click', () => toggleModal(elements.verifyModal, true));
        elements.closeVerifyBtn?.addEventListener('click', () => toggleModal(elements.verifyModal, false));
        
        // Action buttons
        elements.predictHigherBtn?.addEventListener('click', () => makePrediction('higher'));
        elements.predictLowerBtn?.addEventListener('click', () => makePrediction('lower'));
        elements.cashOutBtn?.addEventListener('click', cashOut);
        elements.newGameBtn?.addEventListener('click', startNewRound);
        
        // Result modal buttons
        elements.resultContinueBtn?.addEventListener('click', continueGame);
        elements.resultCashoutBtn?.addEventListener('click', cashOutFromModal);
        
        // Verification
        elements.verifySubmitBtn?.addEventListener('click', verifyRound);
        
        // GA Currency buttons
        elements.gaPlaceBetBtn?.addEventListener('click', placeGABet);
        elements.gaWithdrawBtn?.addEventListener('click', withdrawGABet);
        
        // Quick bet buttons
        document.querySelectorAll('.quick-bet').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const amount = parseInt(e.target.dataset.amount);
                if (elements.gaBetInput) {
                    elements.gaBetInput.value = amount;
                }
            });
        });
        
        // Close modals on overlay click
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    toggleModal(modal, false);
                }
            });
        });
        
        // Touch and responsive event handlers
        setupTouchHandlers();
    }
    
    function setupTouchHandlers() {
        // Prevent double-tap zoom on buttons
        document.querySelectorAll('button').forEach(btn => {
            btn.addEventListener('touchend', (e) => {
                e.preventDefault();
                btn.click();
            });
        });
        
        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                updateMobileStatusBar();
                if (gameState.viewMode === 'pro') {
                    updateProAnalytics();
                }
            }, 100);
        });
        
        // Handle resize for responsive layout
        window.addEventListener('resize', debounce(() => {
            updateMobileStatusBar();
            if (gameState.viewMode === 'pro') {
                updateProAnalytics();
            }
        }, 250));
    }
    
    function setViewMode(mode) {
        gameState.viewMode = mode;
        localStorage.setItem('hiloViewMode', mode);
        
        // Update button states
        if (elements.standardViewBtn && elements.proViewBtn) {
            elements.standardViewBtn.classList.toggle('active', mode === 'standard');
            elements.proViewBtn.classList.toggle('active', mode === 'pro');
        }
        
        // Toggle pro view styling
        if (mode === 'pro') {
            document.body.classList.add('pro-view-active');
            updateProAnalytics();
            updateRemainingCards();
        } else {
            document.body.classList.remove('pro-view-active');
        }
        
        addHistoryEntry(`Switched to ${mode} view`, 'info');
    }
    
    function initProViewMode() {
        // Load saved view mode
        const savedMode = localStorage.getItem('hiloViewMode') || 'standard';
        setViewMode(savedMode);
    }
    
    function updateMobileStatusBar() {
        if (elements.mobileBalance) elements.mobileBalance.textContent = gameState.gaCurrency;
        if (elements.mobileStreak) elements.mobileStreak.textContent = gameState.currentStreak;
        if (elements.mobileBet) elements.mobileBet.textContent = gameState.gaBetAmount;
        if (elements.mobileMultiplier) elements.mobileMultiplier.textContent = `x${gameState.totalMultiplier.toFixed(1)}`;
    }
    
    function updateProAnalytics() {
        if (gameState.viewMode !== 'pro') return;
        
        const stats = gameState.statistics;
        
        // Calculate win rate
        const winRate = stats.gamesPlayed > 0 ? (stats.correctPredictions / stats.gamesPlayed * 100) : 0;
        
        // Calculate average multiplier
        const avgMult = stats.gamesPlayed > 0 ? (stats.totalWon / Math.max(stats.totalWagered, 1)) : 0;
        
        // Determine risk level
        let riskLevel = 'Low';
        if (gameState.currentStreak >= 5 || gameState.totalMultiplier >= 3) {
            riskLevel = 'High';
        } else if (gameState.currentStreak >= 3 || gameState.totalMultiplier >= 2) {
            riskLevel = 'Medium';
        }
        
        // Update analytics display
        if (elements.winRate) elements.winRate.textContent = `${winRate.toFixed(1)}%`;
        if (elements.avgMultiplier) elements.avgMultiplier.textContent = `x${avgMult.toFixed(2)}`;
        if (elements.totalProfit) elements.totalProfit.textContent = `${stats.totalProfit} GA`;
        if (elements.riskLevel) {
            elements.riskLevel.textContent = riskLevel;
            elements.riskLevel.className = `analytics-value risk-${riskLevel.toLowerCase()}`;
        }
        
        // Update probability chart if canvas is available
        updateProbabilityChart();
    }
    
    function updateProbabilityChart() {
        const canvas = elements.probabilityChart;
        if (!canvas || !gameState.deck || gameState.currentCardIndex >= gameState.deck.length) return;
        
        const ctx = canvas.getContext('2d');
        const currentCard = gameState.deck[gameState.currentCardIndex];
        
        if (!currentCard) return;
        
        // Calculate probabilities
        const remainingCards = gameState.deck.slice(gameState.currentCardIndex + 1);
        const higherCards = remainingCards.filter(card => card.numericValue > currentCard.numericValue).length;
        const lowerCards = remainingCards.filter(card => card.numericValue < currentCard.numericValue).length;
        const equalCards = remainingCards.filter(card => card.numericValue === currentCard.numericValue).length;
        
        const total = remainingCards.length;
        if (total === 0) return;
        
        const higherProb = (higherCards / total) * 100;
        const lowerProb = (lowerCards / total) * 100;
        const equalProb = (equalCards / total) * 100;
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw bar chart
        const barWidth = canvas.width / 3 - 10;
        const maxHeight = canvas.height - 40;
        
        // Higher bar
        const higherHeight = (higherProb / 100) * maxHeight;
        ctx.fillStyle = '#4caf50';
        ctx.fillRect(5, canvas.height - higherHeight - 20, barWidth, higherHeight);
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Higher', barWidth/2 + 5, canvas.height - 5);
        ctx.fillText(`${higherProb.toFixed(1)}%`, barWidth/2 + 5, canvas.height - higherHeight - 25);
        
        // Lower bar
        const lowerHeight = (lowerProb / 100) * maxHeight;
        ctx.fillStyle = '#f44336';
        ctx.fillRect(barWidth + 15, canvas.height - lowerHeight - 20, barWidth, lowerHeight);
        ctx.fillStyle = '#ffffff';
        ctx.fillText('Lower', barWidth + 15 + barWidth/2, canvas.height - 5);
        ctx.fillText(`${lowerProb.toFixed(1)}%`, barWidth + 15 + barWidth/2, canvas.height - lowerHeight - 25);
        
        // Equal bar
        const equalHeight = (equalProb / 100) * maxHeight;
        ctx.fillStyle = '#ff9800';
        ctx.fillRect(2 * barWidth + 25, canvas.height - equalHeight - 20, barWidth, equalHeight);
        ctx.fillStyle = '#ffffff';
        ctx.fillText('Equal', 2 * barWidth + 25 + barWidth/2, canvas.height - 5);
        ctx.fillText(`${equalProb.toFixed(1)}%`, 2 * barWidth + 25 + barWidth/2, canvas.height - equalHeight - 25);
    }
    
    function updateRemainingCards() {
        if (!elements.remainingCardsGrid || gameState.viewMode !== 'pro') return;
        
        // Count remaining cards by value
        const remainingCards = gameState.deck ? gameState.deck.slice(gameState.currentCardIndex + 1) : [];
        const cardCounts = {};
        
        values.forEach(value => {
            cardCounts[value] = remainingCards.filter(card => card.value === value).length;
        });
        
        // Update grid
        elements.remainingCardsGrid.innerHTML = '';
        values.forEach(value => {
            const item = document.createElement('div');
            item.className = 'remaining-card-item';
            item.innerHTML = `
                <div>${value}</div>
                <div>${cardCounts[value] || 0}</div>
            `;
            elements.remainingCardsGrid.appendChild(item);
        });
    }
    
    function placeGABet() {
        if (!gameState.isRoundActive || !elements.gaBetInput) return;
        
        // Withdraw previous bet if exists
        if (gameState.gaBetActive && gameState.gaBetAmount > 0) {
            withdrawGABet();
        }
        
        const amount = parseInt(elements.gaBetInput.value);
        if (isNaN(amount) || amount <= 0) {
            alert("Please enter a valid bet amount");
            return;
        }
        
        if (amount < gameState.gaMinBet) {
            alert(`Minimum bet is ${gameState.gaMinBet} GA`);
            return;
        }
        
        if (amount > gameState.gaMaxBet) {
            alert(`Maximum bet is ${gameState.gaMaxBet} GA`);
            return;
        }
        
        if (amount > gameState.gaCurrency) {
            alert("You don't have enough GA Currency");
            return;
        }
        
        // Place the bet
        gameState.gaBetActive = true;
        gameState.gaBetAmount = amount;
        gameState.gaCurrency -= amount;
        gameState.statistics.totalWagered += amount;
        
        // Update display
        updateGACurrencyDisplay();
        updateMobileStatusBar();
        
        // Add to history
        addHistoryEntry(`Placed GA bet of ${amount}. Make your prediction now!`, 'info');
        
        // Enable prediction buttons to indicate player can now make a prediction
        enableActionButtons();
        
        saveGameState();
    }
    
    function withdrawGABet() {
        if (!gameState.gaBetActive || gameState.gaBetAmount <= 0) {
            alert("You don't have an active GA bet to withdraw");
            return;
        }
        
        // Return bet amount to balance
        gameState.gaCurrency += gameState.gaBetAmount;
        gameState.statistics.totalWagered -= gameState.gaBetAmount;
        
        // Reset bet
        gameState.gaBetActive = false;
        const previousBet = gameState.gaBetAmount;
        gameState.gaBetAmount = 0;
        
        // Update display
        updateGACurrencyDisplay();
        updateMobileStatusBar();
        
        // Add to history
        addHistoryEntry(`Withdrew GA bet of ${previousBet}`);
        
        saveGameState();
    }
    
    function updateGACurrencyDisplay() {
        if (elements.gaCurrencyBalance) {
            elements.gaCurrencyBalance.textContent = gameState.gaCurrency;
        }
        if (elements.gaBetAmount) {
            elements.gaBetAmount.textContent = gameState.gaBetAmount;
        }
    }
    
    function startNewRound() {
        gameState.roundNumber++;
        gameState.currentStreak = 0;
        gameState.totalMultiplier = 1.0;
        gameState.currentCardIndex = 0;
        gameState.predictions = [];
        gameState.isRoundActive = true;
        
        // Check if player has enough GA currency to play
        if (gameState.gaCurrency < gameState.gaMinBet) {
            alert(`Not enough GA Currency! You need at least ${gameState.gaMinBet} GA to play.`);
            gameState.isRoundActive = false;
            return;
        }
        
        // Generate seeds for provably fair system
        generateNewSeeds();
        
        // Generate and shuffle deck
        generateDeck();
        shuffleDeck();
        
        // Deal first card
        dealCurrentCard();
        
        // Update UI
        updateDisplay();
        resetButtons();
        updateFairnessDisplay();
        updateMobileStatusBar();
        
        if (gameState.viewMode === 'pro') {
            updateProAnalytics();
            updateRemainingCards();
        }
        
        // Prompt user to place a GA bet
        if (!gameState.gaBetActive || gameState.gaBetAmount <= 0) {
            addHistoryEntry(`Round ${gameState.roundNumber} started. Please place a GA bet to continue.`, 'info');
        } else {
            addHistoryEntry(`Round ${gameState.roundNumber} started. New deck shuffled. GA bet: ${gameState.gaBetAmount}`, 'info');
        }
        
        saveGameState();
    }
    
    function generateNewSeeds() {
        gameState.serverSeed = generateServerSeed();
        gameState.serverSeedHash = sha256(gameState.serverSeed);
        gameState.clientSeed = generateClientSeed();
    }
    
    function generateDeck() {
        gameState.deck = [];
        suits.forEach(suit => {
            values.forEach(value => {
                gameState.deck.push({
                    value: value,
                    suit: suit,
                    numericValue: cardValues[value],
                    isRed: suit === '♥' || suit === '♦'
                });
            });
        });
    }
    
    function shuffleDeck() {
        // Use provably fair system to shuffle deck
        const combinedSeed = gameState.serverSeed + gameState.clientSeed + gameState.roundNumber;
        const rng = seedRandom(combinedSeed);
        
        // Fisher-Yates shuffle with seeded random
        for (let i = gameState.deck.length - 1; i > 0; i--) {
            const j = Math.floor(rng() * (i + 1));
            [gameState.deck[i], gameState.deck[j]] = [gameState.deck[j], gameState.deck[i]];
        }
    }
    
    function dealCurrentCard() {
        if (gameState.currentCardIndex < gameState.deck.length) {
            const currentCard = gameState.deck[gameState.currentCardIndex];
            displayCard(elements.currentCardSlot, currentCard);
            
            // Calculate and display probabilities
            calculateProbabilities();
        }
    }
    
    function displayCard(container, card) {
        if (!container || !card) return;
        
        const cardElement = document.createElement('div');
        cardElement.className = `card ${card.isRed ? 'card-red' : 'card-black'}`;
        
        cardElement.innerHTML = `
            <div class="card-value">${card.value}</div>
            <div class="card-suit top-left">${card.suit}</div>
            <div class="card-suit bottom-right">${card.suit}</div>
        `;
        
        container.innerHTML = '';
        container.appendChild(cardElement);
    }
    
    function calculateProbabilities() {
        if (gameState.currentCardIndex >= gameState.deck.length) return;
        
        const currentCard = gameState.deck[gameState.currentCardIndex];
        const remainingCards = gameState.deck.slice(gameState.currentCardIndex + 1);
        
        if (remainingCards.length === 0) {
            // No more cards
            if (elements.higherProbability) elements.higherProbability.textContent = '0%';
            if (elements.lowerProbability) elements.lowerProbability.textContent = '0%';
            if (elements.higherMultiplier) elements.higherMultiplier.textContent = 'x0';
            if (elements.lowerMultiplier) elements.lowerMultiplier.textContent = 'x0';
            return;
        }
        
        const higherCards = remainingCards.filter(card => card.numericValue > currentCard.numericValue).length;
        const lowerCards = remainingCards.filter(card => card.numericValue < currentCard.numericValue).length;
        const totalCards = remainingCards.length;
        
        const higherProbability = (higherCards / totalCards) * 100;
        const lowerProbability = (lowerCards / totalCards) * 100;
        
        // Calculate multipliers (inverse of probability with house edge)
        const houseEdge = 0.05; // 5% house edge
        const higherMultiplier = higherCards > 0 ? (totalCards / higherCards) * (1 - houseEdge) : 0;
        const lowerMultiplier = lowerCards > 0 ? (totalCards / lowerCards) * (1 - houseEdge) : 0;
        
        // Update display
        if (elements.higherProbability) elements.higherProbability.textContent = `${higherProbability.toFixed(1)}%`;
        if (elements.lowerProbability) elements.lowerProbability.textContent = `${lowerProbability.toFixed(1)}%`;
        if (elements.higherMultiplier) elements.higherMultiplier.textContent = `x${higherMultiplier.toFixed(2)}`;
        if (elements.lowerMultiplier) elements.lowerMultiplier.textContent = `x${lowerMultiplier.toFixed(2)}`;
    }
    
    function makePrediction(prediction) {
        if (!gameState.isRoundActive || !gameState.gaBetActive || gameState.gaBetAmount <= 0) {
            alert("Please place a GA bet first!");
            return;
        }
        
        if (gameState.currentCardIndex >= gameState.deck.length - 1) {
            alert("No more cards in the deck!");
            return;
        }
        
        const currentCard = gameState.deck[gameState.currentCardIndex];
        const nextCard = gameState.deck[gameState.currentCardIndex + 1];
        
        // Determine if prediction is correct
        let isCorrect = false;
        if (prediction === 'higher' && nextCard.numericValue > currentCard.numericValue) {
            isCorrect = true;
        } else if (prediction === 'lower' && nextCard.numericValue < currentCard.numericValue) {
            isCorrect = true;
        }
        
        // Calculate multiplier for this prediction
        const remainingCards = gameState.deck.slice(gameState.currentCardIndex + 1);
        const targetCards = prediction === 'higher' 
            ? remainingCards.filter(card => card.numericValue > currentCard.numericValue).length
            : remainingCards.filter(card => card.numericValue < currentCard.numericValue).length;
        
        const houseEdge = 0.05;
        const predictionMultiplier = targetCards > 0 
            ? (remainingCards.length / targetCards) * (1 - houseEdge) 
            : 0;
        
        // Store prediction
        gameState.predictions.push({
            prediction: prediction,
            currentCard: currentCard,
            nextCard: nextCard,
            isCorrect: isCorrect,
            multiplier: predictionMultiplier
        });
        
        // Update statistics
        gameState.statistics.gamesPlayed++;
        if (isCorrect) {
            gameState.statistics.correctPredictions++;
            gameState.currentStreak++;
            gameState.totalMultiplier *= predictionMultiplier;
            
            if (gameState.currentStreak > gameState.statistics.highestStreak) {
                gameState.statistics.highestStreak = gameState.currentStreak;
            }
            
            if (gameState.totalMultiplier > gameState.statistics.highestMultiplier) {
                gameState.statistics.highestMultiplier = gameState.totalMultiplier;
            }
        } else {
            // Prediction failed - end round
            gameState.currentStreak = 0;
            endRound(false);
            return;
        }
        
        // Move to next card
        gameState.currentCardIndex++;
        
        // Display next card
        displayCard(elements.nextCardSlot, nextCard);
        
        // Show result
        showPredictionResult(isCorrect, prediction, currentCard, nextCard, predictionMultiplier);
        
        // Update display
        updateDisplay();
        updateMobileStatusBar();
        
        if (gameState.viewMode === 'pro') {
            updateProAnalytics();
            updateRemainingCards();
        }
        
        // Enable cash out button
        if (elements.cashOutBtn) {
            elements.cashOutBtn.disabled = false;
            if (elements.cashoutMultiplier) {
                elements.cashoutMultiplier.textContent = `x${gameState.totalMultiplier.toFixed(2)}`;
            }
        }
        
        // Check if this was the last card
        if (gameState.currentCardIndex >= gameState.deck.length - 1) {
            // Auto cash out on last card
            setTimeout(() => cashOut(), 2000);
        } else {
            // Continue with next card as current card
            setTimeout(() => {
                continueToNextCard();
            }, 2000);
        }
        
        saveGameState();
    }
    
    function continueToNextCard() {
        // Move next card to current position
        const nextCard = gameState.deck[gameState.currentCardIndex];
        displayCard(elements.currentCardSlot, nextCard);
        
        // Clear next card slot
        if (elements.nextCardSlot) {
            elements.nextCardSlot.innerHTML = '';
        }
        
        // Recalculate probabilities
        calculateProbabilities();
        
        // Hide prediction result
        if (elements.predictionResult) {
            elements.predictionResult.classList.add('hidden');
        }
        
        // Re-enable prediction buttons
        enableActionButtons();
        
        if (gameState.viewMode === 'pro') {
            updateProbabilityChart();
            updateRemainingCards();
        }
    }
    
    function showPredictionResult(isCorrect, prediction, currentCard, nextCard, multiplier) {
        if (!elements.predictionResult || !elements.resultText || !elements.resultDetails) return;
        
        elements.resultText.textContent = isCorrect ? 'Correct!' : 'Wrong!';
        elements.resultText.className = `result-text ${isCorrect ? 'success' : 'failure'}`;
        
        const currentCardText = `${currentCard.value}${currentCard.suit}`;
        const nextCardText = `${nextCard.value}${nextCard.suit}`;
        const predictionText = prediction === 'higher' ? 'higher' : 'lower';
        
        elements.resultDetails.textContent = isCorrect 
            ? `You predicted ${predictionText}. ${currentCardText} → ${nextCardText} (x${multiplier.toFixed(2)})`
            : `You predicted ${predictionText}. ${currentCardText} → ${nextCardText}`;
        
        elements.predictionResult.classList.remove('hidden');
        
        // Add to history
        const cardClass = isCorrect ? 'success' : 'failure';
        addHistoryEntry(
            `${currentCardText} → ${nextCardText}: Predicted ${predictionText} - ${isCorrect ? 'Correct!' : 'Wrong!'}`,
            cardClass
        );
        
        // Disable action buttons temporarily
        disableActionButtons();
    }
    
    function cashOut() {
        if (!gameState.gaBetActive || gameState.gaBetAmount <= 0) {
            alert("No active bet to cash out!");
            return;
        }
        
        const winAmount = Math.floor(gameState.gaBetAmount * gameState.totalMultiplier);
        const profit = winAmount - gameState.gaBetAmount;
        
        // Add winnings to balance
        gameState.gaCurrency += winAmount;
        gameState.statistics.totalWon += winAmount;
        gameState.statistics.totalProfit += profit;
        
        // Update win rate
        if (gameState.statistics.gamesPlayed > 0) {
            gameState.statistics.winRate = (gameState.statistics.correctPredictions / gameState.statistics.gamesPlayed) * 100;
        }
        
        // Calculate average multiplier
        if (gameState.statistics.totalWagered > 0) {
            gameState.statistics.avgMultiplier = gameState.statistics.totalWon / gameState.statistics.totalWagered;
        }
        
        addHistoryEntry(
            `Cashed out! Bet: ${gameState.gaBetAmount} GA → Won: ${winAmount} GA (Profit: ${profit >= 0 ? '+' : ''}${profit} GA)`,
            'success'
        );
        
        endRound(true);
    }
    
    function cashOutFromModal() {
        cashOut();
        toggleModal(elements.resultModal, false);
    }
    
    function continueGame() {
        toggleModal(elements.resultModal, false);
        continueToNextCard();
    }
    
    function endRound(won) {
        gameState.isRoundActive = false;
        gameState.gaBetActive = false;
        gameState.gaBetAmount = 0;
        gameState.statistics.totalRounds++;
        
        // Update displays
        updateDisplay();
        updateStatistics();
        updateGACurrencyDisplay();
        updateMobileStatusBar();
        
        if (gameState.viewMode === 'pro') {
            updateProAnalytics();
        }
        
        // Show new game button
        if (elements.newGameBtn) {
            elements.newGameBtn.classList.remove('hidden');
        }
        
        // Disable action buttons
        disableActionButtons();
        if (elements.cashOutBtn) {
            elements.cashOutBtn.disabled = true;
        }
        
        saveGameState();
    }
    
    function enableActionButtons() {
        if (elements.predictHigherBtn) elements.predictHigherBtn.disabled = false;
        if (elements.predictLowerBtn) elements.predictLowerBtn.disabled = false;
    }
    
    function disableActionButtons() {
        if (elements.predictHigherBtn) elements.predictHigherBtn.disabled = true;
        if (elements.predictLowerBtn) elements.predictLowerBtn.disabled = true;
    }
    
    function resetButtons() {
        if (elements.newGameBtn) elements.newGameBtn.classList.add('hidden');
        if (elements.cashOutBtn) {
            elements.cashOutBtn.disabled = true;
            if (elements.cashoutMultiplier) {
                elements.cashoutMultiplier.textContent = 'x1.0';
            }
        }
        
        // Disable prediction buttons until bet is placed
        disableActionButtons();
    }
    
    function updateDisplay() {
        if (elements.roundNumber) elements.roundNumber.textContent = gameState.roundNumber;
        if (elements.streakCounter) elements.streakCounter.textContent = gameState.currentStreak;
        if (elements.totalMultiplier) elements.totalMultiplier.textContent = `x${gameState.totalMultiplier.toFixed(1)}`;
        if (elements.cardsRemaining) {
            const remaining = gameState.deck ? gameState.deck.length - gameState.currentCardIndex - 1 : 0;
            elements.cardsRemaining.textContent = Math.max(0, remaining);
        }
        if (elements.historyRoundInfo) elements.historyRoundInfo.textContent = `Round ${gameState.roundNumber}`;
    }
    
    function updateStatistics() {
        if (elements.statTotalRounds) elements.statTotalRounds.textContent = gameState.statistics.totalRounds;
        if (elements.statCorrectPredictions) elements.statCorrectPredictions.textContent = gameState.statistics.correctPredictions;
        if (elements.statHighestStreak) elements.statHighestStreak.textContent = gameState.statistics.highestStreak;
        if (elements.statHighestMultiplier) elements.statHighestMultiplier.textContent = `x${gameState.statistics.highestMultiplier.toFixed(1)}`;
    }
    
    function updateFairnessDisplay() {
        if (elements.serverSeedHash) elements.serverSeedHash.textContent = gameState.serverSeedHash || '...';
        if (elements.clientSeed) elements.clientSeed.textContent = gameState.clientSeed || '...';
        if (elements.roundId) elements.roundId.textContent = gameState.roundNumber.toString();
    }
    
    function addHistoryEntry(message, type = '') {
        if (!elements.historyEntries) return;
        
        const entry = document.createElement('div');
        entry.className = `history-entry ${type}`;
        entry.innerHTML = `<div class="history-message">${message}</div>`;
        
        elements.historyEntries.appendChild(entry);
        
        // Limit history to last 20 entries
        while (elements.historyEntries.children.length > 20) {
            elements.historyEntries.removeChild(elements.historyEntries.firstChild);
        }
        
        // Scroll to bottom
        elements.historyEntries.scrollTop = elements.historyEntries.scrollHeight;
    }
    
    function toggleModal(modal, show) {
        if (!modal) return;
        
        if (show) {
            modal.classList.remove('hidden');
        } else {
            modal.classList.add('hidden');
        }
    }
    
    function verifyRound() {
        // Placeholder for verification functionality
        if (elements.verificationContent) {
            elements.verificationContent.textContent = "Verification functionality would be implemented here with actual cryptographic verification.";
        }
    }
    
    // Utility functions
    function generateServerSeed() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }
    
    function generateClientSeed() {
        return Math.random().toString(36).substring(2, 15);
    }
    
    function sha256(message) {
        // Simplified hash function for demo purposes
        let hash = 0;
        for (let i = 0; i < message.length; i++) {
            const char = message.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(16);
    }
    
    function seedRandom(seed) {
        // Simple seeded random number generator
        let x = 0;
        for (let i = 0; i < seed.length; i++) {
            x += seed.charCodeAt(i);
        }
        return function() {
            x = Math.sin(x) * 10000;
            return x - Math.floor(x);
        };
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Local storage functions
    function saveGameState() {
        try {
            const saveData = {
                gaCurrency: gameState.gaCurrency,
                statistics: gameState.statistics,
                viewMode: gameState.viewMode
            };
            localStorage.setItem('hiloGameState', JSON.stringify(saveData));
        } catch (e) {
            console.warn('Could not save game state:', e);
        }
    }
    
    function loadGameState() {
        try {
            const saved = localStorage.getItem('hiloGameState');
            if (saved) {
                const saveData = JSON.parse(saved);
                if (saveData.gaCurrency !== undefined) gameState.gaCurrency = saveData.gaCurrency;
                if (saveData.statistics) gameState.statistics = { ...gameState.statistics, ...saveData.statistics };
                if (saveData.viewMode) gameState.viewMode = saveData.viewMode;
            }
        } catch (e) {
            console.warn('Could not load game state:', e);
        }
    }
});