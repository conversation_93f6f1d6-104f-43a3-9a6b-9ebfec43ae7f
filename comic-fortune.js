// Comic Fortune - Superhero Slot Machine Game
class ComicFortuneGame {
    constructor() {
        // Game state
        this.gameState = 'ready'; // ready, spinning, result
        this.balance = 5000; // GA currency
        this.currentBet = 25;
        this.paylines = 5;
        this.totalWon = 0;
        this.biggestWin = 0;
        this.heroLevel = 1;
        this.gameActive = false;
        
        // Slot machine configuration
        this.reelCount = 5;
        this.symbolsPerReel = 20;
        this.visibleSymbols = 3;
        
        // Symbol definitions with weights (lower = rarer)
        this.symbols = {
            cherry: { emoji: '🍒', name: 'cherry', weight: 100 },
            lemon: { emoji: '🍋', name: 'lemon', weight: 90 },
            orange: { emoji: '🍊', name: 'orange', weight: 80 },
            bell: { emoji: '🔔', name: 'bell', weight: 50 },
            bar: { emoji: '📊', name: 'bar', weight: 40 },
            seven: { emoji: '7️⃣', name: 'seven', weight: 20 },
            diamond: { emoji: '💎', name: 'diamond', weight: 10 },
            star: { emoji: '⭐', name: 'star', weight: 5 }
        };
        
        // Paytable (multipliers of bet)
        this.paytable = {
            star: { 3: 500, 4: 2000, 5: 10000 },
            diamond: { 3: 200, 4: 1000, 5: 5000 },
            seven: { 3: 100, 4: 500, 5: 2500 },
            bell: { 3: 50, 4: 200, 5: 1000 },
            bar: { 3: 25, 4: 100, 5: 500 },
            orange: { 3: 10, 4: 50, 5: 200 },
            lemon: { 3: 5, 4: 25, 5: 100 },
            cherry: { 3: 2, 4: 10, 5: 50 }
        };
        
        // Payline definitions (positions on 5x3 grid)
        this.paylinePatterns = [
            [1, 1, 1, 1, 1], // Middle row
            [0, 0, 0, 0, 0], // Top row  
            [2, 2, 2, 2, 2], // Bottom row
            [0, 1, 2, 1, 0], // V shape
            [2, 1, 0, 1, 2]  // Inverted V
        ];
        
        // Provably fair
        this.serverSeed = '';
        this.clientSeed = this.generateRandomString(16);
        this.serverSeedHash = '';
        this.nonce = 0;
        
        // Game history
        this.spinHistory = [];
        this.achievements = new Set();
        
        // Initialize DOM elements and setup
        this.initElements();
        this.attachEventListeners();
        this.generateReels();
        this.generateNewServerSeed();
        this.updateDisplay();
    }
    
    initElements() {
        // Slot reels
        this.slotReels = document.getElementById('slotReels');
        this.reelElements = [];
        for (let i = 1; i <= this.reelCount; i++) {
            this.reelElements.push(document.getElementById(`reel${i}`));
        }
        
        // Control buttons
        this.spinBtn = document.getElementById('spinBtn');
        this.maxBetBtn = document.getElementById('maxBetBtn');
        this.autoSpinBtn = document.getElementById('autoSpinBtn');
        this.infoBtn = document.getElementById('infoBtn');
        
        // Betting controls
        this.betInput = document.getElementById('betAmount');
        this.paylinesDisplay = document.getElementById('paylines');
        this.betChips = document.querySelectorAll('.bet-chip');
        
        // Display elements
        this.balanceDisplay = document.getElementById('balance');
        this.currentBetDisplay = document.getElementById('currentBet');
        this.totalWonDisplay = document.getElementById('totalWon');
        this.biggestWinDisplay = document.getElementById('biggestWin');
        this.heroLevelDisplay = document.getElementById('heroLevel');
        
        // Win display
        this.winDisplay = document.getElementById('winDisplay');
        this.winAmount = document.getElementById('winAmount');
        this.winMessage = document.getElementById('winMessage');
        
        // Paylines display
        this.paylinesDisplayElement = document.getElementById('paylinesDisplay');
        
        // Modal
        this.resultModal = document.getElementById('resultModal');
        this.resultTitle = document.getElementById('resultTitle');
        this.resultMessage = document.getElementById('resultMessage');
        this.resultDetails = document.getElementById('resultDetails');
        this.achievementsDisplay = document.getElementById('achievements');
    }
    
    attachEventListeners() {
        // Main control buttons
        if (this.spinBtn) {
            this.spinBtn.addEventListener('click', () => this.spin());
        }
        
        if (this.maxBetBtn) {
            this.maxBetBtn.addEventListener('click', () => this.setMaxBet());
        }
        
        if (this.autoSpinBtn) {
            this.autoSpinBtn.addEventListener('click', () => this.toggleAutoSpin());
        }
        
        if (this.infoBtn) {
            this.infoBtn.addEventListener('click', () => this.showGameInfo());
        }
        
        // Bet controls
        if (this.betInput) {
            this.betInput.addEventListener('input', () => this.updateBet());
        }
        
        // Bet chips
        this.betChips.forEach(chip => {
            chip.addEventListener('click', () => {
                const amount = parseInt(chip.dataset.amount);
                this.betInput.value = amount;
                this.updateBet();
            });
        });
    }
    
    // Generate reel strips with symbols
    generateReels() {
        this.reelStrips = [];
        
        for (let reel = 0; reel < this.reelCount; reel++) {
            const strip = [];
            
            // Create weighted symbol distribution
            const symbolPool = [];
            Object.values(this.symbols).forEach(symbol => {
                for (let i = 0; i < symbol.weight; i++) {
                    symbolPool.push(symbol);
                }
            });
            
            // Fill reel strip
            for (let pos = 0; pos < this.symbolsPerReel; pos++) {
                const randomIndex = Math.floor(Math.random() * symbolPool.length);
                strip.push(symbolPool[randomIndex]);
            }
            
            this.reelStrips.push(strip);
            this.renderReel(reel, strip);
        }
    }
    
    // Render a reel in the DOM
    renderReel(reelIndex, strip) {
        const reelElement = this.reelElements[reelIndex];
        if (!reelElement) return;
        
        const content = reelElement.querySelector('.reel-content');
        if (!content) return;
        
        content.innerHTML = '';
        
        // Add extra symbols for seamless scrolling
        const extendedStrip = [...strip, ...strip.slice(0, this.visibleSymbols)];
        
        extendedStrip.forEach(symbol => {
            const symbolElement = document.createElement('div');
            symbolElement.className = `symbol ${symbol.name}`;
            symbolElement.textContent = symbol.emoji;
            content.appendChild(symbolElement);
        });
    }
    
    // Update bet amount
    updateBet() {
        const newBet = parseInt(this.betInput.value);
        if (newBet >= 5 && newBet <= 100 && newBet <= this.balance) {
            this.currentBet = newBet;
            this.updateDisplay();
        } else {
            this.betInput.value = this.currentBet;
            if (newBet > this.balance) {
                this.showNotification('Bet exceeds available balance!');
            }
        }
    }
    
    // Set maximum bet
    setMaxBet() {
        const maxBet = Math.min(100, this.balance);
        this.betInput.value = maxBet;
        this.updateBet();
    }
    
    // Main spin function
    spin() {
        if (this.gameState === 'spinning') return;
        
        // Validate bet and balance
        if (this.currentBet > this.balance) {
            this.showNotification('Insufficient balance!');
            return;
        }
        
        if (this.currentBet < 5) {
            this.showNotification('Minimum bet is 5 GA');
            return;
        }
        
        // Deduct bet
        this.balance -= this.currentBet;
        this.gameState = 'spinning';
        this.gameActive = true;
        this.nonce++;
        
        // Disable spin button
        if (this.spinBtn) {
            this.spinBtn.disabled = true;
            this.spinBtn.textContent = 'SPINNING...';
        }
        
        // Clear previous win display
        this.updateWinDisplay(0, 'Spinning...');
        
        // Generate spin result
        const result = this.generateSpinResult();
        
        // Animate reels spinning
        this.animateReels(result, () => {
            this.processSpinResult(result);
        });
        
        return result;
    }
    
    // Generate provably fair spin result
    generateSpinResult() {
        const combinedSeed = this.serverSeed + this.clientSeed + this.nonce.toString();
        let hash = this.hashCode(combinedSeed);
        
        const result = {
            reelPositions: [],
            finalSymbols: [],
            winAmount: 0,
            winningLines: [],
            winningCombination: null
        };
        
        // Generate stopping positions for each reel
        for (let reel = 0; reel < this.reelCount; reel++) {
            hash = this.hashCode(hash.toString());
            const position = Math.abs(hash) % this.symbolsPerReel;
            result.reelPositions.push(position);
        }
        
        // Extract visible symbols
        for (let reel = 0; reel < this.reelCount; reel++) {
            const reelSymbols = [];
            for (let row = 0; row < this.visibleSymbols; row++) {
                const symbolIndex = (result.reelPositions[reel] + row) % this.symbolsPerReel;
                reelSymbols.push(this.reelStrips[reel][symbolIndex]);
            }
            result.finalSymbols.push(reelSymbols);
        }
        
        // Check for winning combinations
        this.checkWinningCombinations(result);
        
        return result;
    }
    
    // Check for winning combinations on all paylines
    checkWinningCombinations(result) {
        let totalWin = 0;
        const winningLines = [];
        
        for (let lineIndex = 0; lineIndex < this.paylines; lineIndex++) {
            const pattern = this.paylinePatterns[lineIndex];
            const lineSymbols = [];
            
            // Extract symbols for this payline
            for (let reel = 0; reel < this.reelCount; reel++) {
                const row = pattern[reel];
                lineSymbols.push(result.finalSymbols[reel][row]);
            }
            
            // Check for winning combination
            const lineWin = this.checkLineWin(lineSymbols);
            if (lineWin.amount > 0) {
                totalWin += lineWin.amount * this.currentBet;
                winningLines.push({
                    lineIndex,
                    symbols: lineSymbols,
                    count: lineWin.count,
                    symbol: lineWin.symbol,
                    amount: lineWin.amount * this.currentBet
                });
                
                // Store best combination
                if (!result.winningCombination || lineWin.count > 3) {
                    result.winningCombination = lineSymbols.slice(0, lineWin.count)
                        .map(s => s.emoji).join('');
                }
            }
        }
        
        result.winAmount = totalWin;
        result.winningLines = winningLines;
    }
    
    // Check a single payline for wins
    checkLineWin(lineSymbols) {
        let matchCount = 1;
        const firstSymbol = lineSymbols[0];
        
        // Count consecutive matching symbols from left
        for (let i = 1; i < lineSymbols.length; i++) {
            if (lineSymbols[i].name === firstSymbol.name) {
                matchCount++;
            } else {
                break;
            }
        }
        
        // Check if we have a winning combination
        if (matchCount >= 3 && this.paytable[firstSymbol.name]) {
            const multiplier = this.paytable[firstSymbol.name][matchCount] || 0;
            return {
                symbol: firstSymbol.name,
                count: matchCount,
                amount: multiplier
            };
        }
        
        return { amount: 0, count: 0, symbol: null };
    }
    
    // Animate reel spinning
    animateReels(result, callback) {
        const spinDurations = [1000, 1200, 1400, 1600, 1800]; // Staggered stops
        let completedReels = 0;
        
        this.reelElements.forEach((reel, index) => {
            const content = reel.querySelector('.reel-content');
            if (!content) return;
            
            // Add spinning class
            content.classList.add('spinning');
            
            // Stop spinning after duration
            setTimeout(() => {
                content.classList.remove('spinning');
                
                // Set final position
                const finalPosition = result.reelPositions[index];
                const symbolHeight = 40; // Height of each symbol
                const translateY = -finalPosition * symbolHeight;
                content.style.transform = `translateY(${translateY}px)`;
                
                completedReels++;
                if (completedReels === this.reelCount) {
                    setTimeout(callback, 300); // Brief pause before showing results
                }
            }, spinDurations[index]);
        });
    }
    
    // Process spin result
    processSpinResult(result) {
        this.gameState = 'result';
        
        // Add winnings to balance
        this.balance += result.winAmount;
        this.totalWon += result.winAmount;
        
        if (result.winAmount > this.biggestWin) {
            this.biggestWin = result.winAmount;
        }
        
        // Update win display
        if (result.winAmount > 0) {
            this.updateWinDisplay(result.winAmount, this.getWinMessage(result));
            this.highlightWinningSymbols(result);
            
            // Show celebration for big wins
            if (result.winAmount >= this.currentBet * 50) {
                this.showBigWinCelebration(result);
            }
        } else {
            this.updateWinDisplay(0, 'No win this time');
        }
        
        // Check for achievements
        const achievements = this.checkAchievements(result);
        
        // Update hero level
        this.updateHeroLevel();
        
        // Save to history
        this.saveToHistory(result);
        
        // Update displays
        this.updateDisplay();
        
        // Reset game state
        setTimeout(() => {
            this.gameState = 'ready';
            this.gameActive = false;
            
            // Re-enable spin button
            if (this.spinBtn) {
                this.spinBtn.disabled = false;
                this.spinBtn.textContent = '🎲 SPIN';
            }
            
            // Clear winning highlights
            this.clearWinningHighlights();
            
            // Generate new server seed
            this.generateNewServerSeed();
        }, 3000);
        
        return result;
    }
    
    // Get appropriate win message
    getWinMessage(result) {
        if (result.winAmount >= this.currentBet * 100) {
            return '🎉 SUPER HERO WIN! 🎉';
        } else if (result.winAmount >= this.currentBet * 50) {
            return '💥 MEGA WIN! 💥';
        } else if (result.winAmount >= this.currentBet * 10) {
            return '⚡ BIG WIN! ⚡';
        } else if (result.winAmount > this.currentBet) {
            return '🏆 Nice Win! 🏆';
        } else {
            return '💪 Hero Win! 💪';
        }
    }
    
    // Update win display
    updateWinDisplay(amount, message) {
        if (this.winAmount) {
            this.winAmount.textContent = amount > 0 ? `${amount} GA` : message;
        }
        
        if (this.winMessage) {
            this.winMessage.textContent = amount > 0 ? message : '';
        }
        
        // Add animation for wins
        if (amount > 0 && this.winDisplay) {
            this.winDisplay.classList.add('slot-win-celebration');
            setTimeout(() => {
                this.winDisplay.classList.remove('slot-win-celebration');
            }, 2000);
        }
    }
    
    // Highlight winning symbols
    highlightWinningSymbols(result) {
        // Clear previous highlights
        this.clearWinningHighlights();
        
        // Highlight winning symbols
        result.winningLines.forEach(line => {
            const pattern = this.paylinePatterns[line.lineIndex];
            
            for (let reel = 0; reel < line.count; reel++) {
                const reelElement = this.reelElements[reel];
                if (!reelElement) continue;
                
                const symbols = reelElement.querySelectorAll('.symbol');
                const row = pattern[reel];
                const symbolIndex = result.reelPositions[reel] + row + this.visibleSymbols;
                
                if (symbols[symbolIndex]) {
                    symbols[symbolIndex].classList.add('winning');
                }
            }
            
            // Show winning payline
            const paylineElement = this.paylinesDisplayElement?.querySelector(`.payline-${line.lineIndex + 1}`);
            if (paylineElement) {
                paylineElement.classList.add('active');
            }
        });
    }
    
    // Clear winning highlights
    clearWinningHighlights() {
        // Clear symbol highlights
        document.querySelectorAll('.symbol.winning').forEach(symbol => {
            symbol.classList.remove('winning');
        });
        
        // Clear payline highlights
        document.querySelectorAll('.payline.active').forEach(payline => {
            payline.classList.remove('active');
        });
    }
    
    // Show big win celebration
    showBigWinCelebration(result) {
        const achievements = [];
        
        if (result.winAmount >= this.currentBet * 100) {
            achievements.push('🦸‍♂️ Super Hero Status!');
        } else if (result.winAmount >= this.currentBet * 50) {
            achievements.push('💥 Comic Book Legend!');
        }
        
        if (achievements.length > 0) {
            this.showResult('🎉 LEGENDARY WIN! 🎉', 
                `You won ${result.winAmount} GA with ${result.winningCombination}!`, 
                achievements);
        }
    }
    
    // Check for achievements
    checkAchievements(result) {
        const achievements = [];
        
        // First spin achievement
        if (this.spinHistory.length === 0) {
            achievements.push('🎰 First Spin Hero');
            this.achievements.add('first_spin');
        }
        
        // Big win achievements
        if (result.winAmount >= this.currentBet * 50 && !this.achievements.has('big_winner')) {
            achievements.push('💰 Big Winner');
            this.achievements.add('big_winner');
        }
        
        // Jackpot achievement
        if (result.winAmount >= this.currentBet * 100 && !this.achievements.has('jackpot')) {
            achievements.push('🎯 Jackpot Hunter');
            this.achievements.add('jackpot');
        }
        
        // Hot streak achievement (3 wins in a row)
        if (this.spinHistory.length >= 2) {
            const recentWins = this.spinHistory.slice(-2).every(spin => spin.winAmount > 0);
            if (recentWins && result.winAmount > 0 && !this.achievements.has('hot_streak')) {
                achievements.push('🔥 Hot Streak');
                this.achievements.add('hot_streak');
            }
        }
        
        return achievements;
    }
    
    // Update hero level based on total wins
    updateHeroLevel() {
        const newLevel = Math.floor(this.totalWon / 1000) + 1;
        if (newLevel > this.heroLevel) {
            this.heroLevel = newLevel;
            this.showNotification(`🆙 Hero Level Up! Now Level ${this.heroLevel}!`);
        }
    }
    
    // Save spin to history
    saveToHistory(result) {
        const historyItem = {
            nonce: this.nonce,
            bet: this.currentBet,
            winAmount: result.winAmount,
            finalSymbols: result.finalSymbols,
            winningLines: result.winningLines,
            timestamp: new Date()
        };
        
        this.spinHistory.unshift(historyItem);
        
        // Keep only last 50 spins
        if (this.spinHistory.length > 50) {
            this.spinHistory.pop();
        }
    }
    
    // Toggle auto spin
    toggleAutoSpin() {
        if (typeof this.startAutoSpin === 'function') {
            if (this.autoSpinActive) {
                this.stopAutoSpin();
            } else {
                this.startAutoSpin();
            }
        } else {
            this.showNotification('Auto spin available in Pro View mode!');
        }
    }
    
    // Show game information
    showGameInfo() {
        const info = `🦸‍♂️ COMIC FORTUNE RULES 🦸‍♂️

🎰 HOW TO PLAY:
• Set your bet (5-100 GA)
• Spin the reels for superhero combinations
• Match 3+ symbols on active paylines to win
• Higher value symbols = bigger payouts!

🏆 PAYTABLE (5 symbols):
⭐ Star: 10,000x bet (JACKPOT!)
💎 Diamond: 5,000x bet
7️⃣ Seven: 2,500x bet
🔔 Bell: 1,000x bet
📊 Bar: 500x bet
🍊 Orange: 200x bet
🍋 Lemon: 100x bet
🍒 Cherry: 50x bet

💡 PRO TIPS:
• Use Pro View for advanced analytics
• Track symbol frequency for patterns
• Set auto-spin with win/loss limits
• Level up your Hero status by winning!

🎯 FEATURES:
• 5 active paylines
• Provably fair results
• Achievement system
• Hero level progression
• Detailed statistics in Pro View

Good luck, Hero! 🦸‍♀️`;

        this.showNotification(info);
    }
    
    // Show result modal
    showResult(title, message, achievements = []) {
        if (!this.resultModal) return;
        
        this.resultTitle.textContent = title;
        this.resultMessage.textContent = message;
        
        this.resultDetails.innerHTML = `
            <p>Current Balance: ${this.balance.toLocaleString()} GA</p>
            <p>Hero Level: ${this.heroLevel}</p>
        `;
        
        if (achievements.length > 0) {
            this.achievementsDisplay.innerHTML = achievements.map(achievement => 
                `<div class="achievement-badge">${achievement}</div>`
            ).join('');
        } else {
            this.achievementsDisplay.innerHTML = '';
        }
        
        this.resultModal.style.display = 'block';
    }
    
    // Close result modal
    closeResult() {
        if (this.resultModal) {
            this.resultModal.style.display = 'none';
        }
    }
    
    // Update all display elements
    updateDisplay() {
        if (this.balanceDisplay) this.balanceDisplay.textContent = this.balance.toLocaleString();
        if (this.currentBetDisplay) this.currentBetDisplay.textContent = this.currentBet;
        if (this.totalWonDisplay) this.totalWonDisplay.textContent = this.totalWon.toLocaleString();
        if (this.biggestWinDisplay) this.biggestWinDisplay.textContent = this.biggestWin.toLocaleString();
        if (this.heroLevelDisplay) this.heroLevelDisplay.textContent = this.heroLevel;
        if (this.paylinesDisplay) this.paylinesDisplay.textContent = this.paylines;
    }
    
    // Show notification
    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(45deg, var(--comic-red), var(--comic-yellow));
            color: var(--comic-dark);
            padding: 1rem;
            border-radius: 10px;
            border: 3px solid var(--comic-gold);
            z-index: 10000;
            font-weight: bold;
            box-shadow: 0 8px 16px rgba(0,0,0,0.4);
            animation: slideIn 0.3s ease;
            max-width: 350px;
            white-space: pre-line;
            font-size: 0.9rem;
            font-family: 'Poppins', sans-serif;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideIn 0.3s ease reverse';
            setTimeout(() => notification.remove(), 300);
        }, message.length > 100 ? 8000 : 4000);
    }
    
    // Provably fair methods
    generateNewServerSeed() {
        this.serverSeed = this.generateRandomString(32);
        this.serverSeedHash = this.hashString(this.serverSeed);
    }
    
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return Math.abs(hash).toString(16).padStart(8, '0');
    }
    
    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash;
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM loaded, initializing Comic Fortune game...");
    try {
        // Check if enhanced version should be loaded
        if (typeof EnhancedComicFortuneGame !== 'undefined') {
            console.log("Enhanced version will be initialized by enhanced script");
        } else {
            window.comicFortuneGame = new ComicFortuneGame();
            
            // Global functions for HTML onclick attributes
            window.closeResult = function() {
                if (window.comicFortuneGame) {
                    window.comicFortuneGame.closeResult();
                }
            };
        }
        
        console.log("Comic Fortune game base initialized successfully!");
    } catch (error) {
        console.error("Error initializing Comic Fortune game:", error);
    }
});