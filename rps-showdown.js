// RPS Showdown - Provably Fair Rock Paper Scissors Game
class RPSGame {
    constructor() {
        // Game State
        this.playerScore = 0;
        this.opponentScore = 0;
        this.totalGames = 0;
        this.gamesWon = 0;
        this.roundsPlayed = 0;
        this.playerChoice = null;
        this.opponentChoice = null;
        this.gameActive = true;
        
        // Provably Fair Variables
        this.clientSeed = '';
        this.serverSeed = '';
        this.hashedServerSeed = '';
        this.nonce = 0;
        this.roundHistory = [];
        this.revealedSeeds = {}; // Map of nonce to revealed server seed
        
        // Game Economics
        this.balance = 1000; // Player's starting balance in GA currency
        this.currentBet = 50; // Default bet amount
        this.betMultiplier = 2; // Standard payout multiplier
        
        // Constants
        this.CHOICES = ['rock', 'paper', 'scissors'];
        this.ICONS = {
            'rock': '✊',
            'paper': '✋',
            'scissors': '✌️',
            'unknown': '<i class="fas fa-question"></i>'
        };
        this.OUTCOMES = {
            WIN: 'win',
            LOSE: 'lose',
            TIE: 'tie'
        };
        this.BEST_OF = 5; // First to 3 wins
        this.WIN_THRESHOLD = Math.ceil(this.BEST_OF / 2);
        
        // Initialize the game
        this.generateNewSeeds();
        this.updateDisplay();
        this.populateRoundHistory();
        this.updateBetDisplay();
        
        // Load saved data if available
        this.loadGameData();
    }
    
    // Generate new cryptographic seeds
    generateNewSeeds() {
        // Generate client seed (random string)
        this.clientSeed = this.generateRandomString(32);
        
        // Generate server seed and hash it
        this.serverSeed = this.generateRandomString(32);
        this.hashedServerSeed = this.sha256(this.serverSeed);
        
        // Update display
        const clientSeedEl = document.getElementById('clientSeed');
        const hashedServerSeedEl = document.getElementById('hashedServerSeed');
        const nonceEl = document.getElementById('nonce');
        
        if (clientSeedEl) clientSeedEl.textContent = this.clientSeed;
        if (hashedServerSeedEl) hashedServerSeedEl.textContent = this.hashedServerSeed;
        if (nonceEl) nonceEl.textContent = this.nonce;
    }
    
    // Generate random string for seeds
    generateRandomString(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
    
    // Simple SHA-256 implementation
    sha256(ascii) {
        function rightRotate(value, amount) {
            return (value >>> amount) | (value << (32 - amount));
        }
        
        let mathPow = Math.pow;
        let maxWord = mathPow(2, 32);
        let lengthProperty = 'length';
        let i, j;
        let result = '';

        let words = [];
        let asciiBitLength = ascii[lengthProperty] * 8;
        
        let hash = this.sha256.h = this.sha256.h || [];
        let k = this.sha256.k = this.sha256.k || [];
        let primeCounter = k[lengthProperty];

        let isComposite = {};
        for (let candidate = 2; primeCounter < 64; candidate++) {
            if (!isComposite[candidate]) {
                for (i = 0; i < 313; i += candidate) {
                    isComposite[i] = candidate;
                }
                hash[primeCounter] = (mathPow(candidate, .5) * maxWord) | 0;
                k[primeCounter++] = (mathPow(candidate, 1/3) * maxWord) | 0;
            }
        }
        
        ascii += '\x80';
        while (ascii[lengthProperty] % 64 - 56) ascii += '\x00';
        for (i = 0; i < ascii[lengthProperty]; i++) {
            j = ascii.charCodeAt(i);
            if (j >> 8) return;
            words[i >> 2] |= j << ((3 - i) % 4) * 8;
        }
        words[words[lengthProperty]] = ((asciiBitLength / maxWord) | 0);
        words[words[lengthProperty]] = (asciiBitLength);
        
        for (j = 0; j < words[lengthProperty];) {
            let w = words.slice(j, j += 16);
            let oldHash = hash;
            hash = hash.slice(0, 8);
            
            for (i = 0; i < 64; i++) {
                let w15 = w[i - 15], w2 = w[i - 2];
                let a = hash[0], e = hash[4];
                let temp1 = hash[7]
                    + (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25))
                    + ((e & hash[5]) ^ ((~e) & hash[6]))
                    + k[i]
                    + (w[i] = (i < 16) ? w[i] : (
                        w[i - 16]
                        + (rightRotate(w15, 7) ^ rightRotate(w15, 18) ^ (w15 >>> 3))
                        + w[i - 7]
                        + (rightRotate(w2, 17) ^ rightRotate(w2, 19) ^ (w2 >>> 10))
                    ) | 0
                );
                let temp2 = (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22))
                    + ((a & hash[1]) ^ (a & hash[2]) ^ (hash[1] & hash[2]));
                
                hash = [(temp1 + temp2) | 0].concat(hash);
                hash[4] = (hash[4] + temp1) | 0;
            }
            
            for (i = 0; i < 8; i++) {
                hash[i] = (hash[i] + oldHash[i]) | 0;
            }
        }
        
        for (i = 0; i < 8; i++) {
            for (j = 3; j + 1; j--) {
                let b = (hash[i] >> (j * 8)) & 255;
                result += ((b < 16) ? 0 : '') + b.toString(16);
            }
        }
        return result;
    }
    
    // Player makes a choice
    makeChoice(choice) {
        if (!this.gameActive) {
            this.showNotification("Please start a new game", "info");
            return false;
        }
        
        // Check if player has enough balance to place the bet
        if (this.balance < this.currentBet) {
            this.showNotification("Insufficient balance to place bet", "error");
            return false;
        }
        
        // Deduct bet amount from balance
        this.balance -= this.currentBet;
        this.updateDisplay();
        
        // Set player choice
        this.playerChoice = choice;
        
        // Calculate opponent choice using provably fair algorithm
        const opponentChoice = this.calculateOpponentChoice();
        this.opponentChoice = opponentChoice;
        
        // Update visuals
        this.updateHandDisplays();
        
        // Determine winner and update scores
        const outcome = this.determineOutcome(this.playerChoice, this.opponentChoice);
        this.updateScores(outcome);
        
        // Add to round history with bet information
        this.roundHistory.unshift({
            nonce: this.nonce,
            playerChoice: this.playerChoice,
            opponentChoice: this.opponentChoice,
            outcome: outcome,
            betAmount: this.currentBet,
            winAmount: outcome === this.OUTCOMES.WIN ? this.currentBet * this.betMultiplier : 0,
            clientSeed: this.clientSeed,
            hashedServerSeed: this.hashedServerSeed,
            revealedServerSeed: this.serverSeed,
            timestamp: new Date().toLocaleTimeString()
        });
        
        // Store revealed server seed for verification
        this.revealedSeeds[this.nonce] = this.serverSeed;
        
        // Update round history display
        this.populateRoundHistory();
        
        // Check if game is over
        if (this.playerScore >= this.WIN_THRESHOLD || this.opponentScore >= this.WIN_THRESHOLD) {
            this.endGame();
        } else {
            // Prepare for next round
            this.nonce++;
            this.generateNewSeeds();
            
            // Reset choices after a delay
            setTimeout(() => {
                this.playerChoice = null;
                this.opponentChoice = null;
                this.updateHandDisplays();
                this.updateDisplay();
            }, 2000);
        }
        
        this.roundsPlayed++;
        this.updateDisplay();
        this.saveGameData();
        
        return {
            playerChoice: this.playerChoice,
            opponentChoice: this.opponentChoice,
            outcome: outcome
        };
    }
    
    // Calculate opponent's choice using provably fair algorithm
    calculateOpponentChoice() {
        // Combine seeds and nonce
        const combinedSeed = this.clientSeed + this.serverSeed + this.nonce;
        const hash = this.sha256(combinedSeed);
        
        // Convert first 8 characters of hash to integer
        const hexSubstring = hash.substring(0, 8);
        const decimal = parseInt(hexSubstring, 16);
        
        // Map to one of three options (rock, paper, scissors)
        // This ensures exactly 1/3 probability for each option
        const index = decimal % 3;
        return this.CHOICES[index];
    }
    
    // Update hand displays with current choices
    updateHandDisplays() {
        const playerHand = document.getElementById('playerHand');
        const opponentHand = document.getElementById('opponentHand');
        
        // Update player hand
        if (playerHand) {
            if (this.playerChoice) {
                playerHand.innerHTML = this.ICONS[this.playerChoice];
                playerHand.className = 'hand-display';
                playerHand.classList.add(this.playerChoice);
            } else {
                playerHand.innerHTML = this.ICONS['unknown'];
                playerHand.className = 'hand-display';
            }
        }
        
        // Update opponent hand
        if (opponentHand) {
            if (this.opponentChoice) {
                opponentHand.innerHTML = this.ICONS[this.opponentChoice];
                opponentHand.className = 'hand-display';
                opponentHand.classList.add(this.opponentChoice);
                
                // Add shake animation
                opponentHand.classList.add('shake-animation');
                setTimeout(() => {
                    opponentHand.classList.remove('shake-animation');
                }, 500);
            } else {
                opponentHand.innerHTML = this.ICONS['unknown'];
                opponentHand.className = 'hand-display';
            }
        }
    }
    
    // Determine outcome of the round
    determineOutcome(playerChoice, opponentChoice) {
        if (playerChoice === opponentChoice) {
            return this.OUTCOMES.TIE;
        }
        
        if (
            (playerChoice === 'rock' && opponentChoice === 'scissors') ||
            (playerChoice === 'paper' && opponentChoice === 'rock') ||
            (playerChoice === 'scissors' && opponentChoice === 'paper')
        ) {
            return this.OUTCOMES.WIN;
        }
        
        return this.OUTCOMES.LOSE;
    }
    
    // Update scores based on outcome
    updateScores(outcome) {
        const resultDisplay = document.getElementById('resultDisplay');
        
        if (!resultDisplay) return;
        
        switch (outcome) {
            case this.OUTCOMES.WIN:
                this.playerScore++;
                resultDisplay.style.color = 'var(--win-color)';
                // Calculate winnings
                const winAmount = this.currentBet * this.betMultiplier;
                this.balance += winAmount;
                resultDisplay.innerHTML = `<i class="fas fa-trophy"></i> You Win This Round! +${winAmount} GA`;
                resultDisplay.classList.add('win-animation');
                this.playSound('win');
                break;
            
            case this.OUTCOMES.LOSE:
                this.opponentScore++;
                resultDisplay.style.color = 'var(--lose-color)';
                resultDisplay.innerHTML = `<i class="fas fa-times-circle"></i> AI Wins This Round. -${this.currentBet} GA`;
                this.playSound('lose');
                break;
            
            case this.OUTCOMES.TIE:
                resultDisplay.style.color = 'var(--tie-color)';
                // Return the bet amount on a tie
                this.balance += this.currentBet;
                resultDisplay.innerHTML = '<i class="fas fa-equals"></i> Tie Round! Bet Returned';
                this.playSound('tie');
                break;
        }
        
        // Remove animation class after animation completes
        setTimeout(() => {
            resultDisplay.classList.remove('win-animation');
        }, 1000);
        
        // Update score display
        const playerScoreEl = document.getElementById('playerScore');
        const opponentScoreEl = document.getElementById('opponentScore');
        
        if (playerScoreEl) playerScoreEl.textContent = this.playerScore;
        if (opponentScoreEl) opponentScoreEl.textContent = this.opponentScore;
        
        this.updateDisplay();
    }
    
    // End the current game
    endGame() {
        this.gameActive = false;
        const resultDisplay = document.getElementById('resultDisplay');
        
        if (!resultDisplay) return;
        
        if (this.playerScore > this.opponentScore) {
            resultDisplay.style.color = 'var(--win-color)';
            // Award game victory bonus (50% of current bet)
            const bonus = Math.round(this.currentBet * 0.5);
            this.balance += bonus;
            resultDisplay.innerHTML = `<i class="fas fa-crown"></i> VICTORY! You Won The Game! +${bonus} GA Bonus`;
            this.gamesWon++;
            this.playSound('gameWin');
            this.showNotification(`Victory bonus: +${bonus} GA!`, 'success');
        } else {
            resultDisplay.style.color = 'var(--lose-color)';
            resultDisplay.innerHTML = '<i class="fas fa-skull"></i> DEFEAT! AI Won The Game';
            this.playSound('gameLose');
        }
        
        this.totalGames++;
        this.updateDisplay();
        this.saveGameData();
        
        // Highlight the New Game button
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.style.animation = 'pulse 1s infinite';
        }
    }
    
    // Reset game for a new match
    resetGame() {
        this.playerScore = 0;
        this.opponentScore = 0;
        this.playerChoice = null;
        this.opponentChoice = null;
        this.gameActive = true;
        this.nonce++;
        
        // Generate new seeds
        this.generateNewSeeds();
        
        // Reset displays
        const playerScoreEl = document.getElementById('playerScore');
        const opponentScoreEl = document.getElementById('opponentScore');
        const resultDisplay = document.getElementById('resultDisplay');
        const resetBtn = document.getElementById('resetBtn');
        
        if (playerScoreEl) playerScoreEl.textContent = 0;
        if (opponentScoreEl) opponentScoreEl.textContent = 0;
        
        if (resultDisplay) {
            resultDisplay.style.color = 'white';
            resultDisplay.innerHTML = 'Make your choice to start the game!';
        }
        
        this.updateHandDisplays();
        this.updateDisplay();
        
        // Remove animation from reset button
        if (resetBtn) {
            resetBtn.style.animation = '';
        }
        
        this.playSound('newGame');
        
        // If player is broke, give them a small amount to continue
        if (this.balance < this.currentBet) {
            const refill = Math.max(100, this.currentBet * 2);
            this.balance += refill;
            this.showNotification(`Balance refilled with ${refill} GA`, 'info');
            this.updateDisplay();
        }
        
        this.saveGameData();
    }
    
    // Adjust bet amount
    adjustBet(amount) {
        const newBet = this.currentBet + amount;
        // Ensure bet is at least 10 and at most the player's balance
        if (newBet >= 10 && newBet <= this.balance) {
            this.currentBet = newBet;
            this.updateBetDisplay();
            this.saveGameData();
        } else if (newBet < 10) {
            this.showNotification("Minimum bet is 10 GA", "info");
        } else {
            this.showNotification("Bet cannot exceed your balance", "info");
        }
    }
    
    // Update bet display
    updateBetDisplay() {
        const betAmountEl = document.getElementById('betAmount');
        const potentialWinEl = document.getElementById('potentialWin');
        
        if (betAmountEl) betAmountEl.textContent = `${this.currentBet} GA`;
        if (potentialWinEl) potentialWinEl.textContent = `${Math.round(this.currentBet * this.betMultiplier)} GA`;
    }
    
    // Update all display elements
    updateDisplay() {
        const totalGamesEl = document.getElementById('totalGames');
        const gamesWonEl = document.getElementById('gamesWon');
        const winRateEl = document.getElementById('winRate');
        const playerBalanceEl = document.getElementById('playerBalance');
        const nonceEl = document.getElementById('nonce');
        
        if (totalGamesEl) totalGamesEl.textContent = this.totalGames;
        if (gamesWonEl) gamesWonEl.textContent = this.gamesWon;
        
        const winRate = this.totalGames > 0 ? 
            Math.round(this.gamesWon / this.totalGames * 100) : 0;
        if (winRateEl) winRateEl.textContent = winRate + '%';
        
        if (nonceEl) nonceEl.textContent = this.nonce;
        
        // Update currency display
        if (playerBalanceEl) playerBalanceEl.textContent = `${this.balance} GA`;
        
        // Update bet display
        this.updateBetDisplay();
    }
    
    // Populate round history display
    populateRoundHistory() {
        const historyContainer = document.getElementById('roundHistory');
        if (!historyContainer) return;
        
        historyContainer.innerHTML = '';

        this.roundHistory.slice(0, 10).forEach(round => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            
            let outcomeIcon, outcomeColor, betResult;
            switch (round.outcome) {
                case this.OUTCOMES.WIN:
                    outcomeIcon = '<i class="fas fa-trophy"></i>';
                    outcomeColor = 'var(--win-color)';
                    betResult = `+${round.winAmount} GA`;
                    break;
                case this.OUTCOMES.LOSE:
                    outcomeIcon = '<i class="fas fa-times-circle"></i>';
                    outcomeColor = 'var(--lose-color)';
                    betResult = `-${round.betAmount} GA`;
                    break;
                case this.OUTCOMES.TIE:
                    outcomeIcon = '<i class="fas fa-equals"></i>';
                    outcomeColor = 'var(--tie-color)';
                    betResult = `Bet Returned`;
                    break;
            }
            
            historyItem.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                    <strong style="font-size: 0.8rem;">Round #${round.nonce}</strong>
                    <span style="color: ${outcomeColor}; font-size: 0.8rem;">${outcomeIcon} ${round.outcome.toUpperCase()}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; font-size: 0.8rem;">
                    <span>You: ${this.ICONS[round.playerChoice]} ${round.playerChoice}</span>
                    <span>AI: ${this.ICONS[round.opponentChoice]} ${round.opponentChoice}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem; font-size: 0.8rem;">
                    <span>Bet: ${round.betAmount} GA</span>
                    <span style="color: ${outcomeColor};">${betResult}</span>
                </div>
                <div style="font-size: 0.7rem; color: rgba(255,255,255,0.6);">
                    <button class="rules-toggle" style="font-size: 0.7rem; margin-top: 0.5rem;" 
                            onclick="revealSeed(${round.nonce})">
                        Reveal Server Seed
                    </button>
                    <div id="seed-${round.nonce}" style="display: none; margin-top: 0.5rem;">
                        <div class="seed-display" style="font-size: 0.6rem;">
                            ${round.revealedServerSeed}
                        </div>
                    </div>
                </div>
            `;
            historyContainer.appendChild(historyItem);
        });
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.getElementById('notification');
        if (!notification) return;
        
        notification.textContent = message;
        
        // Set color based on type
        if (type === 'error') {
            notification.style.background = 'var(--lose-color)';
        } else if (type === 'success') {
            notification.style.background = 'var(--win-color)';
        } else {
            notification.style.background = 'var(--rps-blue)';
        }
        
        notification.className = 'notification show';
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
    
    // Play sound effects
    playSound(type) {
        try {
            // Simple beep sounds using Web Audio API
            const context = new (window.AudioContext || window.webkitAudioContext)();
            
            let oscillator = context.createOscillator();
            let gainNode = context.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(context.destination);
            
            switch (type) {
                case 'win':
                    oscillator.frequency.setValueAtTime(880, context.currentTime);
                    gainNode.gain.setValueAtTime(0.3, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    
                    setTimeout(() => {
                        let osc2 = context.createOscillator();
                        let gain2 = context.createGain();
                        osc2.connect(gain2);
                        gain2.connect(context.destination);
                        osc2.frequency.setValueAtTime(1320, context.currentTime);
                        gain2.gain.setValueAtTime(0.3, context.currentTime);
                        osc2.start(context.currentTime);
                        osc2.stop(context.currentTime + 0.1);
                    }, 150);
                    break;
                    
                case 'lose':
                    oscillator.frequency.setValueAtTime(440, context.currentTime);
                    gainNode.gain.setValueAtTime(0.3, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.2);
                    
                    setTimeout(() => {
                        let osc2 = context.createOscillator();
                        let gain2 = context.createGain();
                        osc2.connect(gain2);
                        gain2.connect(context.destination);
                        osc2.frequency.setValueAtTime(330, context.currentTime);
                        gain2.gain.setValueAtTime(0.3, context.currentTime);
                        osc2.start(context.currentTime);
                        osc2.stop(context.currentTime + 0.2);
                    }, 250);
                    break;
                    
                case 'tie':
                    oscillator.frequency.setValueAtTime(660, context.currentTime);
                    gainNode.gain.setValueAtTime(0.2, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    break;
                    
                case 'gameWin':
                    this.playArpeggio(context, [440, 554, 659, 880], 0.1, 0.2);
                    break;
                    
                case 'gameLose':
                    this.playArpeggio(context, [440, 392, 349, 330], 0.1, 0.2);
                    break;
                    
                case 'newGame':
                    oscillator.frequency.setValueAtTime(523, context.currentTime);
                    gainNode.gain.setValueAtTime(0.2, context.currentTime);
                    oscillator.start(context.currentTime);
                    oscillator.stop(context.currentTime + 0.1);
                    break;
            }
        } catch (error) {
            // Silently fail if Web Audio API is not available
            console.log('Audio not available:', error);
        }
    }
    
    // Helper function to play an arpeggio
    playArpeggio(context, frequencies, duration, spacing) {
        frequencies.forEach((freq, index) => {
            setTimeout(() => {
                try {
                    let osc = context.createOscillator();
                    let gain = context.createGain();
                    osc.connect(gain);
                    gain.connect(context.destination);
                    
                    osc.frequency.setValueAtTime(freq, context.currentTime);
                    gain.gain.setValueAtTime(0.2, context.currentTime);
                    gain.gain.exponentialRampToValueAtTime(0.01, context.currentTime + duration);
                    
                    osc.start(context.currentTime);
                    osc.stop(context.currentTime + duration);
                } catch (error) {
                    console.log('Audio error:', error);
                }
            }, index * spacing * 1000);
        });
    }
    
    // Save game data to localStorage
    saveGameData() {
        try {
            const gameData = {
                balance: this.balance,
                totalGames: this.totalGames,
                gamesWon: this.gamesWon,
                currentBet: this.currentBet,
                nonce: this.nonce,
                roundHistory: this.roundHistory.slice(0, 20), // Keep last 20 rounds
                timestamp: Date.now()
            };
            localStorage.setItem('rpsShowdownData', JSON.stringify(gameData));
        } catch (error) {
            console.log('Error saving game data:', error);
        }
    }
    
    // Load game data from localStorage
    loadGameData() {
        try {
            const savedData = localStorage.getItem('rpsShowdownData');
            if (savedData) {
                const gameData = JSON.parse(savedData);
                
                // Only load if data is recent (within 24 hours)
                const hoursSinceLastPlay = (Date.now() - gameData.timestamp) / (1000 * 60 * 60);
                if (hoursSinceLastPlay < 24) {
                    this.balance = gameData.balance || 1000;
                    this.totalGames = gameData.totalGames || 0;
                    this.gamesWon = gameData.gamesWon || 0;
                    this.currentBet = gameData.currentBet || 50;
                    this.nonce = gameData.nonce || 0;
                    this.roundHistory = gameData.roundHistory || [];
                    
                    this.updateDisplay();
                    this.populateRoundHistory();
                }
            }
        } catch (error) {
            console.log('Error loading game data:', error);
        }
    }
}

// Global functions for HTML onclick attributes
function makeChoice(choice) {
    if (window.rpsGame) {
        window.rpsGame.makeChoice(choice);
    }
}

function resetGame() {
    if (window.rpsGame) {
        window.rpsGame.resetGame();
    }
}

function adjustBet(amount) {
    if (window.rpsGame) {
        window.rpsGame.adjustBet(amount);
    }
}

function verifyResult() {
    if (window.rpsGame) {
        const clientSeed = document.getElementById('verifyClientSeed').value;
        const serverSeed = document.getElementById('verifyServerSeed').value;
        const nonce = parseInt(document.getElementById('verifyNonce').value);

        if (!clientSeed || !serverSeed || isNaN(nonce)) {
            window.rpsGame.showNotification('Please fill in all verification fields', 'error');
            return;
        }

        // Verify server seed hash matches the stored hash
        const storedHash = window.rpsGame.roundHistory.find(round => round.nonce === nonce)?.hashedServerSeed;
        const calculatedHash = window.rpsGame.sha256(serverSeed);
        
        if (storedHash && calculatedHash !== storedHash) {
            showVerificationResult(false, 'Server seed hash does not match the stored hash');
            return;
        }

        // Recreate the calculation
        const combinedSeed = clientSeed + serverSeed + nonce;
        const hash = window.rpsGame.sha256(combinedSeed);
        const hexSubstring = hash.substring(0, 8);
        const decimal = parseInt(hexSubstring, 16);
        const opponentChoice = window.rpsGame.CHOICES[decimal % 3];

        // Get the actual recorded choices for this round
        const historyEntry = window.rpsGame.roundHistory.find(round => round.nonce === nonce);
        
        if (!historyEntry) {
            showVerificationResult(false, 'No round found with this nonce');
            return;
        }

        const verified = opponentChoice === historyEntry.opponentChoice;

        // Display verification result
        showVerificationResult(verified, verified ? 
            `Verification successful! AI's choice (${opponentChoice}) matches the recorded choice.` : 
            `Verification failed! Calculated choice (${opponentChoice}) does not match recorded choice (${historyEntry.opponentChoice}).`
        );
    }
}

function showVerificationResult(success, message) {
    const verificationResult = document.getElementById('verificationResult');
    if (!verificationResult) return;
    
    verificationResult.style.display = 'block';
    verificationResult.style.borderLeft = success ? 
        '4px solid var(--win-color)' : '4px solid var(--lose-color)';
    
    verificationResult.innerHTML = `
        <h5 style="color: ${success ? 'var(--win-color)' : 'var(--lose-color)'}; margin-bottom: 1rem; font-size: 0.9rem;">
            ${success ? '✅ Verification Successful' : '❌ Verification Failed'}
        </h5>
        <p style="font-size: 0.8rem;">${message}</p>
    `;
}

function revealSeed(nonce) {
    const seedElement = document.getElementById(`seed-${nonce}`);
    if (seedElement) {
        seedElement.style.display = seedElement.style.display === 'none' ? 'block' : 'none';
    }
}

function toggleRules() {
    const rulesContent = document.getElementById('rulesContent');
    if (rulesContent) {
        if (rulesContent.style.display === 'none') {
            rulesContent.style.display = 'block';
        } else {
            rulesContent.style.display = 'none';
        }
    }
}

// Initialize the game when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log("DOM loaded, initializing RPS Showdown game...");
    try {
        // Check if enhanced version should be loaded
        if (typeof EnhancedRPSGame !== 'undefined') {
            console.log("Enhanced version will be initialized by enhanced script");
        } else {
            window.rpsGame = new RPSGame();
        }
        
        console.log("RPS Showdown game base initialized successfully!");
    } catch (error) {
        console.error("Error initializing RPS Showdown game:", error);
    }
});