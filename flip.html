<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flip Memory - GoldenAura</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="flip.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <aside class="sidebar" id="sidebar">
        <nav class="sidebar-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-dice"></i>
                <span>Casino</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-futbol"></i>
                <span>Sports</span>
            </a>
            <a href="search.html" class="nav-item">
                <i class="fas fa-search"></i>
                <span>Search</span>
            </a>
            <a href="wallet.html" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>Wallet</span>
            </a>
            <a href="promotions.html" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>Promotions</span>
            </a>
            <a href="support.html" class="nav-item">
                <i class="fas fa-headset"></i>
                <span>Support</span>
            </a>
            <a href="settings.html#account" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="mainContent">
        <div class="flip-container">
            <!-- Game Title and Back Button -->
            <div class="flip-header">
                <button onclick="window.location.href='flip.html'" class="back-link">
                    <i class="fas fa-plus-circle"></i>
                    <span>New Game</span>
                </button>
                <h1 class="game-title">FLIP MEMORY</h1>
                <div class="view-mode-toggle">
                    <button id="standardViewBtn" class="active">Standard</button>
                    <button id="proViewBtn">Pro View</button>
                </div>
            </div>

            <!-- Pro View Stats -->
            <div class="pro-view-stats">
                <div class="pro-view-title">
                    <i class="fas fa-chart-bar"></i> Pro View Analytics
                </div>
                
                <div class="stats-grid">
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Memory Risk</span>
                        <span class="pro-stat-value" id="memoryRiskValue">
                            <span class="risk-level risk-low">Low</span>
                        </span>
                    </div>
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Flip Accuracy</span>
                        <span class="pro-stat-value" id="flipAccuracyValue">0%</span>
                    </div>
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Trap Density</span>
                        <span class="pro-stat-value" id="trapDensityValue">39%</span>
                    </div>
                    <div class="pro-stat-item">
                        <span class="pro-stat-label">Efficiency</span>
                        <span class="pro-stat-value" id="efficiencyValue">0%</span>
                    </div>
                </div>
                
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 200px;">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-fire"></i> Memory Heatmap
                        </div>
                        <div class="memory-heatmap" id="memoryHeatmap">
                            <!-- Heatmap cells will be generated dynamically -->
                        </div>
                        <div class="efficiency-meter">
                            <div class="efficiency-fill" id="efficiencyFill"></div>
                        </div>
                    </div>
                    
                    <div style="flex: 1; min-width: 200px;">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-history"></i> Flip History
                        </div>
                        <div class="flip-history" id="flipHistory">
                            <div class="flip-entry">
                                <span class="flip-type">Game started</span>
                                <span class="flip-result">Ready</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="flex: 1; min-width: 200px;">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-bomb"></i> Trap Analytics
                        </div>
                        <div class="trap-analytics">
                            <div class="trap-counter">
                                <span class="trap-icon">💀</span>
                                <span>Skulls</span>
                                <span class="trap-count" id="skullCount">0</span>
                            </div>
                            <div class="trap-counter">
                                <span class="trap-icon">⏰</span>
                                <span>Clocks</span>
                                <span class="trap-count" id="clockCount">0</span>
                            </div>
                            <div class="trap-counter">
                                <span class="trap-icon">🪞</span>
                                <span>Mirrors</span>
                                <span class="trap-count" id="mirrorCount">0</span>
                            </div>
                            <div class="trap-counter">
                                <span class="trap-icon">💣</span>
                                <span>Bombs</span>
                                <span class="trap-count" id="bombCount">0</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="pro-controls">
                    <button class="pro-btn" id="analyzeMemoryBtn">
                        <i class="fas fa-brain"></i> Analyze Memory
                    </button>
                    <button class="pro-btn" id="predictTrapsBtn">
                        <i class="fas fa-crystal-ball"></i> Predict Traps
                    </button>
                    <button class="pro-btn" id="optimizePathBtn">
                        <i class="fas fa-route"></i> Optimize Path
                    </button>
                </div>
            </div>

            <!-- Betting Section -->
            <div class="bet-container" id="betContainer">
                <div class="bet-controls">
                    <div class="bet-input-group">
                        <input type="number" class="bet-input" id="betAmount" placeholder="Enter bet amount" min="1" step="1">
                        <div class="bet-currency" id="currencySelector">
                            <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GA" class="currency-icon">
                            <span>GA</span>
                        </div>
                    </div>
                    <div class="currency-balance">
                        <span>Balance:</span>
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GA" class="currency-icon">
                        <span class="currency-amount" id="userBalance">5000</span>
                        <span>GA</span>
                    </div>
                </div>
                <div class="bet-quick-amounts">
                    <div class="quick-amount" data-amount="10">10</div>
                    <div class="quick-amount" data-amount="50">50</div>
                    <div class="quick-amount" data-amount="100">100</div>
                    <div class="quick-amount" data-amount="500">500</div>
                    <div class="quick-amount" data-amount="1000">1000</div>
                    <div class="quick-amount" data-amount="max">MAX</div>
                </div>
                <div class="potential-win">
                    <span class="potential-win-label">Potential Win:</span>
                    <span class="potential-win-value" id="potentialWin">0</span>
                    <span>GA</span>
                </div>
                <button class="btn btn-gold" id="placeBetBtn">
                    <i class="fas fa-coins"></i> Place Bet & Start Game
                </button>
            </div>

            <!-- Game Dashboard -->
            <div class="game-dashboard">
                <div class="game-stats">
                    <div class="stat-item">
                        <span class="stat-label">Bet Amount</span>
                        <span class="stat-value gold" id="currentBet">0</span>
                        <span>GA</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Matches</span>
                        <span class="stat-value" id="matchesValue">0/18</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Multiplier</span>
                        <span class="stat-value" id="multiplierValue">1.0×</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Current Payout</span>
                        <span class="stat-value" id="payoutValue">0</span>
                        <span>GA</span>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button class="btn btn-warning" id="timeLoanBtn" disabled>
                        <i class="fas fa-clock"></i> Time Loan (+15s)
                    </button>
                    <button class="btn btn-danger" id="trapPurgeBtn" disabled>
                        <i class="fas fa-bomb"></i> Trap Purge (-3 traps)
                    </button>
                    <button class="btn btn-primary" id="cashoutBtn" disabled>
                        <i class="fas fa-money-bill-wave"></i> Cashout Now
                    </button>
                </div>
            </div>

            <!-- Timer and Probability -->
            <div class="timer-container">
                <div class="timer" id="timer">45</div>
                <div class="probability-meter">
                    <div class="probability-fill" id="probabilityFill"></div>
                    <div class="probability-label">Win Probability: <span id="winProbability">0%</span></div>
                </div>
            </div>

            <!-- Game Board -->
            <div class="game-board" id="gameBoard">
                <!-- Cards will be generated by JavaScript -->
            </div>

            <!-- Game Info Section -->
            <div class="game-info">
                <div class="info-header">
                    <div class="info-title">Your Memory Stats</div>
                </div>
                
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Games Played</div>
                        <div class="info-value" id="gamesPlayedValue">0</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Best Win</div>
                        <div class="info-value positive" id="bestWinValue">0</div>
                        <span>GA</span>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Worst Loss</div>
                        <div class="info-value debt" id="worstLossValue">0</div>
                        <span>GA</span>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Win Rate</div>
                        <div class="info-value" id="winRateValue">0%</div>
                    </div>
                </div>
                
                <div class="tips-section">
                    <div class="tips-title">Memory Trap Warning</div>
                    <ul class="tips-list">
                        <li>Every wrong flip reduces your potential multiplier</li>
                        <li>Skull cards shuffle the entire grid - avoid at all costs</li>
                        <li>Clock cards steal 30% of your remaining time</li>
                        <li>Mirror cards duplicate ALL trap cards on the board</li>
                        <li>Your memory decays - matched pairs fade after 15 seconds</li>
                    </ul>
                </div>
                
                <div class="failure-rate">
                    <i class="fas fa-exclamation-triangle"></i> 87% of players lose their bets in their first 10 games. Memory is not enough.
                </div>
            </div>
        </div>
    </main>

    <!-- Effects -->
    <div class="blood-splatter" id="bloodSplatter"></div>
    <div class="taunt-message" id="tauntMessage"></div>
    <div class="bonus-popup" id="bonusPopup"></div>

    <script src="assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Game elements
            const gameBoard = document.getElementById('gameBoard');
            const placeBetBtn = document.getElementById('placeBetBtn');
            const timeLoanBtn = document.getElementById('timeLoanBtn');
            const trapPurgeBtn = document.getElementById('trapPurgeBtn');
            const cashoutBtn = document.getElementById('cashoutBtn');
            const timer = document.getElementById('timer');
            const currentBet = document.getElementById('currentBet');
            const matchesValue = document.getElementById('matchesValue');
            const multiplierValue = document.getElementById('multiplierValue');
            const payoutValue = document.getElementById('payoutValue');
            const probabilityFill = document.getElementById('probabilityFill');
            const winProbability = document.getElementById('winProbability');
            const bloodSplatter = document.getElementById('bloodSplatter');
            const tauntMessage = document.getElementById('tauntMessage');
            const bonusPopup = document.getElementById('bonusPopup');
            const betContainer = document.getElementById('betContainer');
            const betAmount = document.getElementById('betAmount');
            const potentialWin = document.getElementById('potentialWin');
            const userBalance = document.getElementById('userBalance');
            const quickAmounts = document.querySelectorAll('.quick-amount');
            
            // Pro View elements
            const standardViewBtn = document.getElementById('standardViewBtn');
            const proViewBtn = document.getElementById('proViewBtn');
            const memoryRiskValue = document.getElementById('memoryRiskValue');
            const flipAccuracyValue = document.getElementById('flipAccuracyValue');
            const trapDensityValue = document.getElementById('trapDensityValue');
            const efficiencyValue = document.getElementById('efficiencyValue');
            const memoryHeatmap = document.getElementById('memoryHeatmap');
            const efficiencyFill = document.getElementById('efficiencyFill');
            const flipHistory = document.getElementById('flipHistory');
            const skullCount = document.getElementById('skullCount');
            const clockCount = document.getElementById('clockCount');
            const mirrorCount = document.getElementById('mirrorCount');
            const bombCount = document.getElementById('bombCount');
            const analyzeMemoryBtn = document.getElementById('analyzeMemoryBtn');
            const predictTrapsBtn = document.getElementById('predictTrapsBtn');
            const optimizePathBtn = document.getElementById('optimizePathBtn');
            
            // Stats elements
            const gamesPlayedValue = document.getElementById('gamesPlayedValue');
            const bestWinValue = document.getElementById('bestWinValue');
            const worstLossValue = document.getElementById('worstLossValue');
            const winRateValue = document.getElementById('winRateValue');
            
            // Game variables
            let isPlaying = false;
            let betValue = 0;
            let matches = 0;
            let multiplier = 1.0;
            let currentPayout = 0;
            let timeLeft = 45;
            let timerInterval = null;
            let memoryTaxInterval = null;
            let cards = [];
            let flippedCards = [];
            let canFlip = true;
            let flipDelay = 700; // 0.7s delay between flips
            let autoFlipTimer = null;
            let lastFlipTime = 0;
            let matchedPairs = [];
            let memoryDecayTimers = [];
            let balance = 5000; // Default starting balance
            let maxMultiplier = 15.0; // Maximum possible multiplier
            let viewMode = localStorage.getItem('flipMemoryViewMode') || 'standard';
            
            // Pro View variables
            let flipCount = 0;
            let successfulFlips = 0;
            let trapCounts = { skull: 0, clock: 0, mirror: 0, bomb: 0 };
            let flipHistoryData = [];
            let heatmapData = Array(36).fill(0);
            let memoryPredictions = [];
            
            // Game stats
            let gameStats = {
                gamesPlayed: 0,
                bestWin: 0,
                worstLoss: 0,
                wins: 0
            };
            
            // Card types and values
            const CARD_TYPES = {
                SKULL: { symbol: '💀', value: -0.5, type: 'trap' },
                CLOCK: { symbol: '⏰', value: -0.3, type: 'trap' },
                MIRROR: { symbol: '🪞', value: -0.1, type: 'trap' },
                BOMB: { symbol: '💣', value: -0.25, type: 'trap' },
                LOW1: { symbol: '🍎', value: 0.1, type: 'low' },
                LOW2: { symbol: '🍌', value: 0.2, type: 'low' },
                LOW3: { symbol: '🍇', value: 0.3, type: 'low' },
                HIGH1: { symbol: '💎', value: 0.5, type: 'high' },
                HIGH2: { symbol: '🏆', value: 0.75, type: 'high' },
                HIGH3: { symbol: '👑', value: 1.0, type: 'high' }
            };
            
            // Initialize game
            function initGame() {
                loadGameStats();
                updateStatsDisplay();
                loadBalance();
                setViewMode(viewMode);
                createMemoryHeatmap();
                
                placeBetBtn.addEventListener('click', placeBet);
                timeLoanBtn.addEventListener('click', timeLoan);
                trapPurgeBtn.addEventListener('click', trapPurge);
                cashoutBtn.addEventListener('click', cashoutNow);
                standardViewBtn.addEventListener('click', () => setViewMode('standard'));
                proViewBtn.addEventListener('click', () => setViewMode('pro'));
                analyzeMemoryBtn.addEventListener('click', analyzeMemory);
                predictTrapsBtn.addEventListener('click', predictTraps);
                optimizePathBtn.addEventListener('click', optimizePath);
                
                // Setup bet amount input
                betAmount.addEventListener('input', updatePotentialWin);
                
                // Setup quick amount buttons
                quickAmounts.forEach(btn => {
                    btn.addEventListener('click', () => {
                        const amount = btn.dataset.amount;
                        if (amount === 'max') {
                            betAmount.value = balance;
                        } else {
                            betAmount.value = amount;
                        }
                        updatePotentialWin();
                    });
                });
            }
            
            // Set view mode (standard or pro)
            function setViewMode(mode) {
                viewMode = mode;
                localStorage.setItem('flipMemoryViewMode', mode);
                
                if (mode === 'standard') {
                    document.body.classList.remove('pro-view-active');
                    standardViewBtn.classList.add('active');
                    proViewBtn.classList.remove('active');
                } else {
                    document.body.classList.add('pro-view-active');
                    standardViewBtn.classList.remove('active');
                    proViewBtn.classList.add('active');
                }
            }
            
            // Create memory heatmap
            function createMemoryHeatmap() {
                memoryHeatmap.innerHTML = '';
                for (let i = 0; i < 36; i++) {
                    const cell = document.createElement('div');
                    cell.className = 'heatmap-cell cold';
                    cell.dataset.index = i;
                    memoryHeatmap.appendChild(cell);
                }
            }
            
            // Update memory heatmap
            function updateMemoryHeatmap() {
                const cells = memoryHeatmap.querySelectorAll('.heatmap-cell');
                cells.forEach((cell, index) => {
                    const intensity = heatmapData[index];
                    cell.className = 'heatmap-cell ';
                    
                    if (intensity === 0) {
                        cell.className += 'cold';
                    } else if (intensity <= 2) {
                        cell.className += 'warm';
                    } else if (intensity <= 4) {
                        cell.className += 'hot';
                    } else {
                        cell.className += 'burning';
                    }
                });
            }
            
            // Add flip history entry
            function addFlipHistoryEntry(type, result, successful = false) {
                const entry = document.createElement('div');
                entry.className = 'flip-entry';
                entry.innerHTML = `
                    <span class="flip-type">${type}</span>
                    <span class="flip-result ${successful ? 'success' : result === 'Trap!' ? 'failure' : ''}">${result}</span>
                `;
                
                flipHistory.prepend(entry);
                
                // Limit history entries
                const entries = flipHistory.querySelectorAll('.flip-entry');
                if (entries.length > 15) {
                    entries[entries.length - 1].remove();
                }
                
                // Save to history data
                flipHistoryData.push({
                    type: type,
                    result: result,
                    successful: successful,
                    time: new Date()
                });
                
                // Update analytics
                updateProViewStats();
            }
            
            // Update Pro View statistics
            function updateProViewStats() {
                // Calculate flip accuracy
                const accuracy = flipCount > 0 ? Math.round((successfulFlips / flipCount) * 100) : 0;
                flipAccuracyValue.textContent = accuracy + '%';
                
                // Calculate trap density
                const totalTraps = trapCounts.skull + trapCounts.clock + trapCounts.mirror + trapCounts.bomb;
                const density = cards.length > 0 ? Math.round((totalTraps / cards.length) * 100) : 39;
                trapDensityValue.textContent = density + '%';
                
                // Calculate efficiency
                const efficiency = Math.max(0, Math.min(100, accuracy - (density / 2)));
                efficiencyValue.textContent = efficiency + '%';
                efficiencyFill.style.width = efficiency + '%';
                
                // Calculate memory risk
                const riskFactor = (density + (100 - accuracy)) / 2;
                updateMemoryRisk(riskFactor);
                
                // Update trap counters
                skullCount.textContent = trapCounts.skull;
                clockCount.textContent = trapCounts.clock;
                mirrorCount.textContent = trapCounts.mirror;
                bombCount.textContent = trapCounts.bomb;
            }
            
            // Update memory risk level
            function updateMemoryRisk(riskValue) {
                let riskText, riskClass;
                
                if (riskValue < 25) {
                    riskText = 'Low';
                    riskClass = 'risk-low';
                } else if (riskValue < 50) {
                    riskText = 'Medium';
                    riskClass = 'risk-medium';
                } else if (riskValue < 75) {
                    riskText = 'High';
                    riskClass = 'risk-high';
                } else {
                    riskText = 'Extreme';
                    riskClass = 'risk-extreme';
                }
                
                memoryRiskValue.innerHTML = `<span class="risk-level ${riskClass}">${riskText}</span>`;
            }
            
            // Load balance from localStorage or use default
            function loadBalance() {
                const savedBalance = localStorage.getItem('userBalance');
                if (savedBalance) {
                    balance = parseInt(savedBalance);
                }
                userBalance.textContent = balance.toLocaleString();
            }
            
            // Save balance to localStorage
            function saveBalance() {
                localStorage.setItem('userBalance', balance.toString());
                userBalance.textContent = balance.toLocaleString();
            }
            
            // Load game stats from localStorage
            function loadGameStats() {
                const savedStats = localStorage.getItem('flipMemoryStats');
                if (savedStats) {
                    gameStats = JSON.parse(savedStats);
                }
            }
            
            // Save game stats to localStorage
            function saveGameStats() {
                localStorage.setItem('flipMemoryStats', JSON.stringify(gameStats));
            }
            
            // Update stats display
            function updateStatsDisplay() {
                gamesPlayedValue.textContent = gameStats.gamesPlayed;
                bestWinValue.textContent = gameStats.bestWin.toLocaleString();
                worstLossValue.textContent = gameStats.worstLoss.toLocaleString();
                
                const winRate = gameStats.gamesPlayed > 0 ? 
                    Math.round((gameStats.wins / gameStats.gamesPlayed) * 100) : 0;
                winRateValue.textContent = winRate + '%';
            }
            
            // Update potential win based on bet amount
            function updatePotentialWin() {
                const bet = parseInt(betAmount.value) || 0;
                const potential = Math.floor(bet * maxMultiplier);
                potentialWin.textContent = potential.toLocaleString();
            }
            
            // Place bet and start game
            function placeBet() {
                const bet = parseInt(betAmount.value) || 0;
                
                if (bet <= 0) {
                    showTaunt('Enter a valid bet!');
                    return;
                }
                
                if (bet > balance) {
                    showTaunt('Insufficient balance!');
                    return;
                }
                
                // Deduct bet from balance
                balance -= bet;
                saveBalance();
                
                // Hide bet container
                betContainer.style.display = 'none';
                
                // Start the game
                betValue = bet;
                currentBet.textContent = bet.toLocaleString();
                startGame();
            }
            
            // Start the game
            function startGame() {
                if (isPlaying) return;
                
                resetGameState();
                isPlaying = true;
                
                // Enable game controls
                timeLoanBtn.disabled = false;
                trapPurgeBtn.disabled = false;
                cashoutBtn.disabled = false;
                
                // Generate cards
                generateCards();
                
                // Start timers
                startTimer();
                startMemoryTax();
                
                // Show brief preview of enemy patterns
                showEnemyPreview();
                
                // Add to flip history
                addFlipHistoryEntry('Game Start', 'Ready', true);
            }
            
            // Reset game state
            function resetGameState() {
                matches = 0;
                multiplier = 1.0;
                currentPayout = 0;
                timeLeft = 45;
                cards = [];
                flippedCards = [];
                canFlip = true;
                matchedPairs = [];
                memoryDecayTimers.forEach(timer => clearTimeout(timer));
                memoryDecayTimers = [];
                flipCount = 0;
                successfulFlips = 0;
                trapCounts = { skull: 0, clock: 0, mirror: 0, bomb: 0 };
                flipHistoryData = [];
                heatmapData = Array(36).fill(0);
                
                // Clear timers
                clearInterval(timerInterval);
                clearInterval(memoryTaxInterval);
                clearTimeout(autoFlipTimer);
                
                // Update UI
                updateGameDisplay();
                updateProbabilityMeter();
                updateMemoryHeatmap();
                updateProViewStats();
                
                // Clear board
                gameBoard.innerHTML = '';
                
                // Reset flip history
                flipHistory.innerHTML = '';
            }
            
            // Generate rigged card deck
            function generateCards() {
                const totalCards = 36;
                const trapIncrease = Math.floor(matches / 3) * 2;
                const trapCount = Math.min(24, 14 + trapIncrease); // Start with 14 traps, add 2 per 3 matches
                
                cards = [];
                
                // Add trap cards (40% base + progressive increase)
                for (let i = 0; i < trapCount; i++) {
                    const trapTypes = ['SKULL', 'CLOCK', 'MIRROR', 'BOMB'];
                    const trapType = trapTypes[Math.floor(Math.random() * trapTypes.length)];
                    cards.push({ ...CARD_TYPES[trapType], id: i, matched: false, flipped: false });
                    
                    // Count traps for Pro View
                    switch (trapType) {
                        case 'SKULL': trapCounts.skull++; break;
                        case 'CLOCK': trapCounts.clock++; break;
                        case 'MIRROR': trapCounts.mirror++; break;
                        case 'BOMB': trapCounts.bomb++; break;
                    }
                }
                
                // Add value cards (pairs)
                const remainingCards = totalCards - trapCount;
                const pairCount = Math.floor(remainingCards / 2);
                
                // 80% low value, 20% high value
                const highValuePairs = Math.ceil(pairCount * 0.2);
                const lowValuePairs = pairCount - highValuePairs;
                
                let cardId = trapCount;
                
                // Add low value pairs
                for (let i = 0; i < lowValuePairs; i++) {
                    const valueTypes = ['LOW1', 'LOW2', 'LOW3'];
                    const valueType = valueTypes[Math.floor(Math.random() * valueTypes.length)];
                    
                    // Add pair
                    cards.push({ ...CARD_TYPES[valueType], id: cardId++, matched: false, flipped: false, pairId: i });
                    cards.push({ ...CARD_TYPES[valueType], id: cardId++, matched: false, flipped: false, pairId: i });
                }
                
                // Add high value pairs
                for (let i = 0; i < highValuePairs; i++) {
                    const valueTypes = ['HIGH1', 'HIGH2', 'HIGH3'];
                    const valueType = valueTypes[Math.floor(Math.random() * valueTypes.length)];
                    
                    // Add pair
                    const pairId = lowValuePairs + i;
                    cards.push({ ...CARD_TYPES[valueType], id: cardId++, matched: false, flipped: false, pairId: pairId });
                    cards.push({ ...CARD_TYPES[valueType], id: cardId++, matched: false, flipped: false, pairId: pairId });
                }
                
                // Shuffle cards
                shuffleArray(cards);
                
                // Render cards
                renderCards();
                
                // Update Pro View stats after generation
                updateProViewStats();
            }
            
            // Shuffle array
            function shuffleArray(array) {
                for (let i = array.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [array[i], array[j]] = [array[j], array[i]];
                }
            }
            
            // Render cards on board
            function renderCards() {
                gameBoard.innerHTML = '';
                
                cards.forEach((card, index) => {
                    const cardElement = document.createElement('div');
                    cardElement.className = 'card';
                    cardElement.dataset.index = index;
                    
                    // Add card content (hidden initially)
                    const valueDisplay = card.value > 0 ? '+' + card.value.toFixed(1) : card.value.toFixed(1);
                    cardElement.innerHTML = `
                        <span class="card-content" style="display: none;">${card.symbol}</span>
                        <span class="card-value">${valueDisplay}</span>
                    `;
                    
                    cardElement.addEventListener('click', () => flipCard(index));
                    gameBoard.appendChild(cardElement);
                });
            }
            
            // Show brief enemy preview
            function showEnemyPreview() {
                cards.forEach((card, index) => {
                    if (card.type === 'trap') {
                        const cardElement = gameBoard.children[index];
                        
                        // Briefly show trap cards as high-value matches
                        cardElement.innerHTML = `
                            <span class="card-content">${CARD_TYPES.HIGH3.symbol}</span>
                            <span class="card-value">+1.0</span>
                        `;
                        cardElement.classList.add('flipped', 'preview');
                        
                        // Hide after preview
                        setTimeout(() => {
                            const valueDisplay = card.value > 0 ? '+' + card.value.toFixed(1) : card.value.toFixed(1);
                            cardElement.innerHTML = `
                                <span class="card-content" style="display: none;">${card.symbol}</span>
                                <span class="card-value">${valueDisplay}</span>
                            `;
                            cardElement.classList.remove('flipped', 'preview');
                        }, 3000);
                    }
                });
            }
            
            // Flip a card
            function flipCard(index) {
                if (!isPlaying || !canFlip || cards[index].flipped || cards[index].matched) return;
                
                // Check flip delay
                const now = Date.now();
                if (now - lastFlipTime < flipDelay) return;
                lastFlipTime = now;
                
                // 20% chance of mis-tap on mobile
                if (window.innerWidth <= 768 && Math.random() < 0.2) {
                    // Mis-tap - flip random adjacent card instead
                    const adjacentIndices = getAdjacentIndices(index);
                    if (adjacentIndices.length > 0) {
                        const randomAdjacent = adjacentIndices[Math.floor(Math.random() * adjacentIndices.length)];
                        if (!cards[randomAdjacent].flipped && !cards[randomAdjacent].matched) {
                            index = randomAdjacent;
                        }
                    }
                }
                
                const card = cards[index];
                const cardElement = gameBoard.children[index];
                
                // Update heatmap data
                heatmapData[index]++;
                updateMemoryHeatmap();
                
                // Flip the card
                card.flipped = true;
                cardElement.classList.add('flipped');
                cardElement.querySelector('.card-content').style.display = 'block';
                
                // Update flip count
                flipCount++;
                
                // Reset auto-flip timer
                resetAutoFlipTimer();
                
                // Update timer (penalty for each flip)
                timeLeft -= 0.5;
                updateTimer();
                
                if (card.type === 'trap') {
                    // Handle trap card
                    handleTrapCard(card, cardElement);
                    addFlipHistoryEntry('Flip', 'Trap!', false);
                } else {
                    // Handle value card
                    flippedCards.push({ card, element: cardElement, index });
                    addFlipHistoryEntry('Flip', card.symbol, false);
                    
                    if (flippedCards.length === 2) {
                        canFlip = false;
                        setTimeout(checkMatch, 1000);
                    }
                }
            }
            
            // Get adjacent card indices
            function getAdjacentIndices(index) {
                const row = Math.floor(index / 6);
                const col = index % 6;
                const adjacent = [];
                
                // Check all 8 directions
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;
                        
                        const newRow = row + dr;
                        const newCol = col + dc;
                        
                        if (newRow >= 0 && newRow < 6 && newCol >= 0 && newCol < 6) {
                            adjacent.push(newRow * 6 + newCol);
                        }
                    }
                }
                
                return adjacent;
            }
            
            // Handle trap card activation
            function handleTrapCard(card, cardElement) {
                cardElement.classList.add('trap');
                
                // Apply trap effect
                switch (card.symbol) {
                    case '💀': // Skull - shuffle grid + penalty
                        multiplier = Math.max(0.1, multiplier + card.value);
                        showBloodSplatter();
                        showTaunt('PATHETIC!');
                        setTimeout(() => {
                            shuffleGrid();
                        }, 1000);
                        break;
                        
                    case '⏰': // Clock - steal time
                        timeLeft = Math.floor(timeLeft * 0.7); // 30% time reduction
                        multiplier = Math.max(0.1, multiplier + card.value);
                        showTaunt('TIME THIEF!');
                        break;
                        
                    case '🪞': // Mirror - duplicate all traps
                        multiplier = Math.max(0.1, multiplier + card.value);
                        duplicateTraps();
                        showTaunt('DOUBLE TROUBLE!');
                        break;
                        
                    case '💣': // Bomb - general penalty
                        multiplier = Math.max(0.1, multiplier + card.value);
                        showBloodSplatter();
                        showTaunt('BOOM!');
                        break;
                }
                
                // Update displays
                updateGameDisplay();
                updateProbabilityMeter();
                updateProViewStats();
                
                // Check game over
                checkGameOver();
            }
            
            // Show blood splatter effect
            function showBloodSplatter() {
                bloodSplatter.classList.add('active');
                setTimeout(() => {
                    bloodSplatter.classList.remove('active');
                }, 500);
            }
            
            // Show taunt message
            function showTaunt(message) {
                tauntMessage.textContent = message;
                tauntMessage.style.animation = 'none';
                setTimeout(() => {
                    tauntMessage.style.animation = 'tauntFade 2s ease';
                }, 10);
            }
            
            // Shuffle the grid
            function shuffleGrid() {
                // Reset all non-matched cards
                cards.forEach((card, index) => {
                    if (!card.matched) {
                        card.flipped = false;
                        const cardElement = gameBoard.children[index];
                        cardElement.classList.remove('flipped', 'trap');
                        cardElement.querySelector('.card-content').style.display = 'none';
                    }
                });
                
                // Shuffle and re-render
                const nonMatchedCards = cards.filter(card => !card.matched);
                shuffleArray(nonMatchedCards);
                
                let nonMatchedIndex = 0;
                cards.forEach((card, index) => {
                    if (!card.matched) {
                        cards[index] = nonMatchedCards[nonMatchedIndex++];
                    }
                });
                
                renderCards();
                flippedCards = [];
                canFlip = true;
                
                // Add to flip history
                addFlipHistoryEntry('Grid Shuffle', 'Chaos!', false);
            }
            
            // Duplicate trap cards
            function duplicateTraps() {
                const newCards = [];
                cards.forEach(card => {
                    newCards.push(card);
                    if (card.type === 'trap' && !card.matched) {
                        // Add duplicate trap
                        const duplicate = { ...card, id: cards.length + newCards.length };
                        newCards.push(duplicate);
                        
                        // Update trap counts
                        switch (card.symbol) {
                            case '💀': trapCounts.skull++; break;
                            case '⏰': trapCounts.clock++; break;
                            case '🪞': trapCounts.mirror++; break;
                            case '💣': trapCounts.bomb++; break;
                        }
                    }
                });
                
                cards = newCards;
                shuffleArray(cards);
                renderCards();
                updateProViewStats();
            }
            
            // Check if two flipped cards match
            function checkMatch() {
                const [first, second] = flippedCards;
                
                if (first.card.symbol === second.card.symbol && first.card.pairId === second.card.pairId) {
                    // Match found
                    first.card.matched = true;
                    second.card.matched = true;
                    first.element.classList.add('matched');
                    second.element.classList.add('matched');
                    
                    matches++;
                    multiplier += first.card.value + second.card.value;
                    successfulFlips += 2;
                    
                    // Add to matched pairs for memory decay
                    const matchData = { first, second, timestamp: Date.now() };
                    matchedPairs.push(matchData);
                    
                    // Set memory decay timer (15 seconds)
                    const decayTimer = setTimeout(() => {
                        fadeMatchedPair(matchData);
                    }, 15000);
                    memoryDecayTimers.push(decayTimer);
                    
                    // Check for progressive poisoning
                    if (matches % 3 === 0) {
                        addTrapCards();
                    }
                    
                    // Show fake bonus popup occasionally
                    if (Math.random() < 0.3) {
                        showFakeBonusPopup();
                    }
                    
                    // Add to flip history
                    addFlipHistoryEntry('Match', first.card.symbol + ' Pair', true);
                    
                } else {
                    // No match - apply penalties
                    multiplier = Math.max(0.1, multiplier - 0.2); // Mismatch penalty
                    
                    first.element.classList.add('error');
                    second.element.classList.add('error');
                    
                    setTimeout(() => {
                        first.card.flipped = false;
                        second.card.flipped = false;
                        first.element.classList.remove('flipped', 'error');
                        second.element.classList.remove('flipped', 'error');
                        first.element.querySelector('.card-content').style.display = 'none';
                        second.element.querySelector('.card-content').style.display = 'none';
                    }, 1000);
                    
                    // Add to flip history
                    addFlipHistoryEntry('Mismatch', 'Failed', false);
                }
                
                // Calculate current payout
                currentPayout = Math.floor(betValue * multiplier);
                
                flippedCards = [];
                canFlip = true;
                updateGameDisplay();
                updateProbabilityMeter();
                updateProViewStats();
                checkGameOver();
            }
            
            // Add progressive trap cards
            function addTrapCards() {
                const trapTypes = ['SKULL', 'CLOCK', 'MIRROR', 'BOMB'];
                
                for (let i = 0; i < 2; i++) {
                    const trapType = trapTypes[Math.floor(Math.random() * trapTypes.length)];
                    const newTrap = { ...CARD_TYPES[trapType], id: cards.length, matched: false, flipped: false };
                    cards.push(newTrap);
                    
                    // Update trap counts
                    switch (trapType) {
                        case 'SKULL': trapCounts.skull++; break;
                        case 'CLOCK': trapCounts.clock++; break;
                        case 'MIRROR': trapCounts.mirror++; break;
                        case 'BOMB': trapCounts.bomb++; break;
                    }
                }
                
                shuffleArray(cards);
                renderCards();
                updateProViewStats();
                
                // Add to flip history
                addFlipHistoryEntry('Trap Spawn', '+2 Traps', false);
            }
            
            // Fade matched pair (memory decay)
            function fadeMatchedPair(matchData) {
                if (matchData.first.card.matched && matchData.second.card.matched) {
                    matchData.first.card.matched = false;
                    matchData.second.card.matched = false;
                    matchData.first.card.flipped = false;
                    matchData.second.card.flipped = false;
                    
                    matchData.first.element.classList.remove('matched', 'flipped');
                    matchData.second.element.classList.remove('matched', 'flipped');
                    matchData.first.element.querySelector('.card-content').style.display = 'none';
                    matchData.second.element.querySelector('.card-content').style.display = 'none';
                    
                    matches--;
                    multiplier = Math.max(0.1, multiplier - 0.3);
                    showTaunt('MEMORY FADED!');
                    
                    // Update current payout
                    currentPayout = Math.floor(betValue * multiplier);
                    updateGameDisplay();
                    
                    // Add to flip history
                    addFlipHistoryEntry('Memory Decay', 'Lost Match', false);
                }
            }
            
            // Show fake bonus popup
            function showFakeBonusPopup() {
                bonusPopup.textContent = '+MULTIPLIER BONUS!';
                bonusPopup.style.display = 'block';
                
                // Actually reduce multiplier when clicked
                bonusPopup.onclick = () => {
                    multiplier = Math.max(0.1, multiplier - 0.2);
                    bonusPopup.style.display = 'none';
                    updateGameDisplay();
                };
                
                // Auto-hide and reduce anyway
                setTimeout(() => {
                    if (bonusPopup.style.display === 'block') {
                        multiplier = Math.max(0.1, multiplier - 0.1);
                        bonusPopup.style.display = 'none';
                        updateGameDisplay();
                    }
                }, 3000);
            }
            
            // Start game timer
            function startTimer() {
                timerInterval = setInterval(() => {
                    timeLeft--;
                    updateTimer();
                    
                    if (timeLeft <= 0) {
                        endGame();
                    }
                }, 1000);
            }
            
            // Start memory tax (multiplier decay)
            function startMemoryTax() {
                memoryTaxInterval = setInterval(() => {
                    multiplier = Math.max(0.1, multiplier - 0.05); // Decay multiplier over time
                    currentPayout = Math.floor(betValue * multiplier);
                    updateGameDisplay();
                }, 5000); // Every 5 seconds
            }
            
            // Update timer display
            function updateTimer() {
                timer.textContent = Math.max(0, Math.floor(timeLeft));
                
                // Change timer appearance based on time left
                timer.className = 'timer';
                if (timeLeft <= 15) {
                    timer.classList.add('danger');
                } else if (timeLeft <= 30) {
                    timer.classList.add('warning');
                }
                
                // Overtime penalty
                if (timeLeft < 10 && timeLeft > 0) {
                    multiplier = Math.max(0.1, multiplier - 0.1); // Faster decay in overtime
                    currentPayout = Math.floor(betValue * multiplier);
                    updateGameDisplay();
                }
            }
            
            // Reset auto-flip timer
            function resetAutoFlipTimer() {
                clearTimeout(autoFlipTimer);
                autoFlipTimer = setTimeout(() => {
                    if (isPlaying && canFlip) {
                        // Auto-flip 2 random cards
                        autoFlipRandomCards();
                    }
                }, 3000); // 3 seconds of hesitation
            }
            
            // Auto-flip random cards
            function autoFlipRandomCards() {
                const availableCards = cards
                    .map((card, index) => ({ card, index }))
                    .filter(({ card }) => !card.flipped && !card.matched);
                
                if (availableCards.length >= 2) {
                    shuffleArray(availableCards);
                    flipCard(availableCards[0].index);
                    setTimeout(() => {
                        flipCard(availableCards[1].index);
                    }, 500);
                    
                    // Add to flip history
                    addFlipHistoryEntry('Auto-Flip', 'Impatience', false);
                }
            }
            
            // Time loan option (costs multiplier)
            function timeLoan() {
                if (!isPlaying || multiplier < 0.5) return;
                
                multiplier = Math.max(0.1, multiplier - 0.5);
                timeLeft += 15;
                
                updateGameDisplay();
                showTaunt('BORROWED TIME!');
                addFlipHistoryEntry('Time Loan', '+15s', true);
            }
            
            // Trap purge option
            function trapPurge() {
                if (!isPlaying || multiplier < 0.5) return;
                
                multiplier = Math.max(0.1, multiplier - 0.5);
                
                // Remove 3 random trap cards
                let trapCount = 0;
                for (let i = cards.length - 1; i >= 0 && trapCount < 3; i--) {
                    if (cards[i].type === 'trap' && !cards[i].matched) {
                        // Update trap counts
                        switch (cards[i].symbol) {
                            case '💀': trapCounts.skull--; break;
                            case '⏰': trapCounts.clock--; break;
                            case '🪞': trapCounts.mirror--; break;
                            case '💣': trapCounts.bomb--; break;
                        }
                        cards.splice(i, 1);
                        trapCount++;
                    }
                }
                
                renderCards();
                updateGameDisplay();
                updateProViewStats();
                showTaunt('TRAPS REMOVED!');
                addFlipHistoryEntry('Trap Purge', '-3 Traps', true);
            }
            
            // Cashout now option
            function cashoutNow() {
                if (!isPlaying) return;
                
                // Add current payout to balance
                if (currentPayout > 0) {
                    balance += currentPayout;
                    saveBalance();
                    
                    // Update stats
                    if (currentPayout > betValue) {
                        gameStats.wins++;
                        const profit = currentPayout - betValue;
                        if (profit > gameStats.bestWin) {
                            gameStats.bestWin = profit;
                        }
                        showTaunt(`WON ${profit}!`);
                        addFlipHistoryEntry('Cashout', `+${profit} GA`, true);
                    }
                }
                
                endGame(true);
            }
            
            // Pro View Button Functions
            
            // Analyze Memory button
            function analyzeMemory() {
                if (!isPlaying) return;
                
                // Create analysis based on current state
                let analysisResult;
                
                const accuracy = flipCount > 0 ? (successfulFlips / flipCount) * 100 : 0;
                const trapDensity = cards.length > 0 ? ((trapCounts.skull + trapCounts.clock + trapCounts.mirror + trapCounts.bomb) / cards.length) * 100 : 0;
                
                if (accuracy < 30) {
                    analysisResult = "CRITICAL: Memory performance failing! High risk of total loss.";
                } else if (accuracy < 60) {
                    analysisResult = "WARNING: Memory accuracy below optimal. Consider defensive play.";
                } else if (trapDensity > 70) {
                    analysisResult = "ALERT: Extremely high trap density detected. Proceed with extreme caution.";
                } else {
                    analysisResult = "Analysis: Memory performance within acceptable range. Continue current strategy.";
                }
                
                // Show analysis as taunt message
                showTaunt(analysisResult);
                addFlipHistoryEntry('Memory Analysis', 'Complete', true);
            }
            
            // Predict Traps button
            function predictTraps() {
                if (!isPlaying) return;
                
                // Highlight predicted trap locations (fake predictions)
                const unflippedCards = cards
                    .map((card, index) => ({ card, index }))
                    .filter(({ card }) => !card.flipped && !card.matched);
                
                if (unflippedCards.length > 0) {
                    // Randomly highlight some cards as "predicted traps"
                    const predictCount = Math.min(3, unflippedCards.length);
                    shuffleArray(unflippedCards);
                    
                    for (let i = 0; i < predictCount; i++) {
                        const cardElement = gameBoard.children[unflippedCards[i].index];
                        cardElement.style.border = '2px solid #ff9800';
                        
                        // Remove highlight after 5 seconds
                        setTimeout(() => {
                            cardElement.style.border = '';
                        }, 5000);
                    }
                    
                    showTaunt(`PREDICTED ${predictCount} TRAP LOCATIONS`);
                    addFlipHistoryEntry('Trap Prediction', `${predictCount} locations`, true);
                }
            }
            
            // Optimize Path button
            function optimizePath() {
                if (!isPlaying) return;
                
                // Show optimal path suggestions (fake optimization)
                const unflippedCards = cards
                    .map((card, index) => ({ card, index }))
                    .filter(({ card }) => !card.flipped && !card.matched);
                
                if (unflippedCards.length > 0) {
                    // Highlight "optimal" cards to flip
                    const pathCount = Math.min(4, unflippedCards.length);
                    shuffleArray(unflippedCards);
                    
                    for (let i = 0; i < pathCount; i++) {
                        const cardElement = gameBoard.children[unflippedCards[i].index];
                        cardElement.style.boxShadow = '0 0 15px #4CAF50';
                        
                        // Remove highlight after 8 seconds
                        setTimeout(() => {
                            cardElement.style.boxShadow = '';
                        }, 8000);
                    }
                    
                    showTaunt(`OPTIMAL PATH: ${pathCount} CARDS HIGHLIGHTED`);
                    addFlipHistoryEntry('Path Optimization', `${pathCount} suggestions`, true);
                }
            }
            
            // Update game display
            function updateGameDisplay() {
                currentBet.textContent = betValue.toLocaleString();
                matchesValue.textContent = `${matches}/18`;
                multiplierValue.textContent = multiplier.toFixed(1) + '×';
                multiplierValue.className = 'stat-value' + (multiplier < 1.0 ? ' negative' : ' positive');
                payoutValue.textContent = currentPayout.toLocaleString();
                payoutValue.className = 'stat-value' + (currentPayout > betValue ? ' positive' : ' negative');
            }
            
            // Update probability meter
            function updateProbabilityMeter() {
                // Calculate chance to win (rigged)
                let chance = matches * 5;
                
                // If multiplier < 1, always show low probability
                if (multiplier < 1.0) {
                    chance = Math.min(chance, 10);
                }
                
                // Cap at 95%
                chance = Math.min(chance, 95);
                
                probabilityFill.style.width = chance + '%';
                winProbability.textContent = chance + '%';
            }
            
            // Check game over conditions
            function checkGameOver() {
                if (matches >= 18) {
                    // False victory - trigger trap ambush if only 1 match remaining
                    if (matches === 17) {
                        addTrapCards();
                        showTaunt('NOT SO FAST!');
                        return;
                    }
                    endGame();
                } else if (timeLeft <= 0) {
                    endGame();
                }
            }
            
            // End the game
            function endGame(cashout = false) {
                isPlaying = false;
                
                // Clear timers
                clearInterval(timerInterval);
                clearInterval(memoryTaxInterval);
                clearTimeout(autoFlipTimer);
                memoryDecayTimers.forEach(timer => clearTimeout(timer));
                
                // If not a cashout, apply end-game logic
                if (!cashout) {
                    // Only pay if multiplier > 1 or all matches found
                    if (multiplier > 1.0 || matches >= 18) {
                        balance += currentPayout;
                        saveBalance();
                        
                        // Check if win
                        if (currentPayout > betValue) {
                            gameStats.wins++;
                            const profit = currentPayout - betValue;
                            if (profit > gameStats.bestWin) {
                                gameStats.bestWin = profit;
                            }
                            showTaunt(`WON ${profit} GA!`);
                            addFlipHistoryEntry('Game End', `+${profit} GA`, true);
                        } else {
                            const loss = betValue - currentPayout;
                            if (loss > gameStats.worstLoss) {
                                gameStats.worstLoss = loss;
                            }
                            showTaunt('LOST!');
                            addFlipHistoryEntry('Game End', 'Loss', false);
                        }
                    } else {
                        // Total loss
                        const loss = betValue;
                        if (loss > gameStats.worstLoss) {
                            gameStats.worstLoss = loss;
                        }
                        showTaunt('TOTAL FAILURE!');
                        addFlipHistoryEntry('Game End', 'Total Loss', false);
                    }
                }
                
                // Update stats
                gameStats.gamesPlayed++;
                saveGameStats();
                updateStatsDisplay();
                
                // Reset controls and show bet container
                setTimeout(() => {
                    betContainer.style.display = 'block';
                    timeLoanBtn.disabled = true;
                    trapPurgeBtn.disabled = true;
                    cashoutBtn.disabled = true;
                }, 2000);
            }
            
            // Initialize the game
            initGame();
        });
    </script>
</body>
</html>