/* Jacks or Better Pro - Provably Fair Video Poker */

:root {
    --primary-dark: #1a2639;
    --primary-medium: #2d4263;
    --primary-light: #3e5c8c;
    --table-green: #175e31;
    --table-green-light: #1e7841;
    --table-green-dark: #104220;
    --accent-gold: #c89b3c;
    --accent-gold-light: #deb761;
    --accent-gold-dark: #b07e1c;
    --accent-red: #d62828;
    --accent-blue: #4059ad;
    --text-light: #f9f9f9;
    --text-medium: #d1d1d1;
    --text-dark: #1a1a1a;
    --card-bg: #ffffff;
    --border-radius: 8px;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--primary-dark);
    color: var(--text-light);
    line-height: 1.6;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%231a2639"/><path d="M0,20 L100,20 M0,40 L100,40 M0,60 L100,60 M0,80 L100,80" stroke="%232d4263" stroke-width="0.5" opacity="0.2"/><path d="M20,0 L20,100 M40,0 L40,100 M60,0 L60,100 M80,0 L80,100" stroke="%232d4263" stroke-width="0.5" opacity="0.2"/></svg>');
    touch-action: manipulation;
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 10px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Rush Pro View Mode Styling */
body.rush-pro-view-active .rush-stats-bar,
body.rush-pro-view-active .rush-analytics-section,
body.rush-pro-view-active .rush-strategy-section,
body.rush-pro-view-active .rush-quick-actions,
body.rush-pro-view-active .rush-only {
    display: block;
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--primary-light);
    background: linear-gradient(0deg, transparent, rgba(65, 90, 175, 0.1), transparent);
    flex-wrap: wrap;
}

.header-left, .header-right {
    flex: 1;
}

.header-center {
    flex: 2;
    text-align: center;
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
    padding: 8px 12px;
    border-radius: var(--border-radius);
}

.back-link i {
    margin-right: 8px;
}

.back-link:hover {
    color: var(--accent-blue);
    background: rgba(64, 89, 173, 0.1);
}

.game-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: 14px;
    font-style: italic;
    color: var(--text-light);
    opacity: 0.8;
}

.view-toggle {
    display: flex;
    margin-bottom: 10px;
    background: var(--primary-dark);
    border-radius: var(--border-radius);
    padding: 2px;
    border: 1px solid var(--primary-light);
    max-width: 200px;
    margin-left: auto;
}

.view-btn {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-medium);
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    border-radius: calc(var(--border-radius) - 2px);
}

.view-btn.active {
    background: linear-gradient(145deg, var(--accent-gold), var(--accent-gold-dark));
    color: var(--text-dark);
    font-weight: 500;
}

.info-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--accent-gold-dark);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    transition: var(--transition);
    margin-left: 10px;
}

.info-btn i {
    margin-right: 6px;
}

.info-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-gold);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    gap: 20px;
}

/* Rush Stats Bar */
.rush-stats-bar {
    display: none;
    background: linear-gradient(145deg, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--accent-gold-dark);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    margin-bottom: 15px;
    overflow-x: auto;
    white-space: nowrap;
}

.rush-stat {
    display: inline-block;
    padding: 0 15px;
    border-right: 1px solid rgba(200, 155, 60, 0.3);
}

.rush-stat:last-child {
    border-right: none;
}

.rush-stat-label {
    font-size: 11px;
    color: var(--text-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
}

.rush-stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-gold);
}

/* Game Section */
.game-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Credits Display */
.credits-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, var(--primary-dark), var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    padding: 15px;
    border: 1px solid var(--primary-light);
    flex-wrap: wrap;
    gap: 15px;
}

.credits-display {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.credits-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.credits-item .label {
    font-size: 12px;
    color: var(--text-medium);
}

.credits-item .value {
    font-size: 18px;
    font-weight: 700;
    color: var(--accent-gold);
}

.bet-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.bet-btn {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 100px;
    justify-content: center;
}

.bet-btn i {
    font-size: 12px;
}

.bet-btn:hover {
    background: linear-gradient(to bottom, var(--accent-gold-dark), var(--accent-gold));
    border-color: var(--accent-gold);
    color: var(--text-dark);
}

.bet-btn.rush-only {
    display: none;
}

/* Mobile Paytable Toggle */
.mobile-paytable-toggle {
    display: none;
    margin-bottom: 15px;
}

.mobile-toggle-btn {
    width: 100%;
    background: linear-gradient(145deg, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--accent-gold-dark);
    color: var(--text-light);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mobile-toggle-btn i {
    font-size: 18px;
}

.mobile-toggle-btn.active {
    background: linear-gradient(145deg, var(--accent-gold), var(--accent-gold-dark));
    color: var(--text-dark);
}

/* Paytable */
.paytable {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    padding: 15px;
    border: 1px solid var(--primary-light);
}

.paytable h3 {
    text-align: center;
    margin-bottom: 10px;
    color: var(--accent-gold);
    font-size: 16px;
}

.paytable-grid {
    display: grid;
    grid-template-columns: 2fr repeat(5, 1fr);
    gap: 2px;
    font-size: 12px;
}

.pay-header,
.pay-row {
    display: contents;
}

.pay-header > *,
.pay-row > * {
    padding: 8px 4px;
    text-align: center;
    border: 1px solid var(--primary-medium);
    background-color: var(--primary-dark);
    transition: var(--transition);
}

.pay-header > * {
    background-color: var(--accent-gold-dark);
    color: var(--text-dark);
    font-weight: 700;
    font-size: 11px;
}

.pay-row .hand-name {
    text-align: left;
    padding-left: 8px;
    font-size: 11px;
}

.paytable-grid .pay-1, .paytable-grid .pay-2, .paytable-grid .pay-3, 
.paytable-grid .pay-4, .paytable-grid .pay-5 {
    font-weight: 500;
}

.paytable-grid .active {
    background-color: var(--accent-gold-light);
    color: var(--text-dark);
    font-weight: 700;
}

.pay-row.winning {
    animation: payRowHighlight 1s infinite alternate;
}

@keyframes payRowHighlight {
    0% { 
        background-color: var(--accent-gold-dark);
        color: var(--text-dark);
    }
    100% { 
        background-color: var(--accent-gold-light);
        color: var(--text-dark);
    }
}

/* Cards Display */
.cards-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: center;
}

.cards-container {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.card-position {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.card {
    width: 110px;
    height: 155px;
    background-color: var(--primary-medium);
    border-radius: 10px;
    position: relative;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid var(--primary-light);
}

.card.dealt {
    background-color: var(--card-bg);
    border: 1px solid #ccc;
}

.card.highlighted {
    box-shadow: 0 0 15px var(--accent-gold);
    transform: translateY(-5px);
}

.card .card-inner {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 10px;
}

.card .card-value {
    font-size: 20px;
    font-weight: bold;
}

.card .card-suit {
    font-size: 20px;
}

.card .card-top-left {
    position: absolute;
    top: 8px;
    left: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.card .card-bottom-right {
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: rotate(180deg);
}

.card .card-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 40px;
}

.card.red .card-value, .card.red .card-suit {
    color: var(--accent-red);
}

.card.black .card-value, .card.black .card-suit {
    color: var(--text-dark);
}

.hold-btn {
    background-color: var(--primary-medium);
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    font-weight: 700;
    transition: var(--transition);
    width: 80px;
    text-align: center;
    opacity: 0.7;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.hold-btn i {
    font-size: 10px;
}

.hold-btn.active {
    background-color: var(--accent-gold);
    color: var(--text-dark);
    opacity: 1;
    transform: scale(1.05);
}

.hold-btn:hover {
    opacity: 1;
}

.hand-result {
    background-color: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 15px 25px;
    text-align: center;
    font-size: 18px;
    font-weight: 700;
    color: var(--accent-gold);
    min-height: 50px;
    min-width: 250px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--accent-gold-dark);
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.main-btn {
    background: linear-gradient(to bottom, var(--accent-gold), var(--accent-gold-dark));
    border: 1px solid var(--accent-gold-dark);
    color: var(--text-dark);
    padding: 15px 25px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    transition: var(--transition);
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.main-btn i {
    font-size: 14px;
}

.main-btn:hover {
    background: linear-gradient(to bottom, var(--accent-gold-light), var(--accent-gold));
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.main-btn:active {
    transform: translateY(0);
    box-shadow: var(--box-shadow);
}

.main-btn.rush-only {
    display: none;
}

/* Rush Pro Quick Actions */
.rush-quick-actions {
    display: none;
    background: linear-gradient(145deg, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--accent-gold-dark);
    border-radius: var(--border-radius);
    padding: 15px;
}

.quick-action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.quick-action-btn {
    background: linear-gradient(145deg, var(--primary-light), var(--primary-medium));
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 12px 8px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.quick-action-btn i {
    font-size: 16px;
    margin-bottom: 2px;
}

.quick-action-btn:hover {
    background: linear-gradient(145deg, var(--accent-gold-dark), var(--accent-gold));
    color: var(--text-dark);
}

/* Mobile Info Toggle */
.mobile-info-toggle {
    display: none;
    margin-bottom: 15px;
}

/* Info Panel */
.info-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 350px;
}

.info-section {
    background: linear-gradient(135deg, var(--primary-medium), var(--primary-dark));
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--primary-light);
}

.info-section h3 {
    background: var(--primary-dark);
    color: var(--accent-gold);
    padding: 12px 15px;
    font-size: 16px;
    border-bottom: 1px solid var(--primary-light);
    text-align: center;
}

/* Rush Analytics Section */
.rush-analytics-section {
    display: none;
}

.rush-analytics-content {
    padding: 15px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.analytic-item {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.analytic-label {
    font-size: 11px;
    color: var(--text-medium);
    text-transform: uppercase;
}

.analytic-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-gold);
}

.rush-chart-container {
    background: var(--primary-dark);
    border-radius: var(--border-radius);
    padding: 10px;
    text-align: center;
}

#rushChart {
    max-width: 100%;
    height: auto;
}

/* Provably Fair Section */
.fair-info {
    padding: 15px;
}

.seed-display {
    margin-bottom: 15px;
}

.seed-item {
    margin-bottom: 10px;
}

.seed-label {
    display: block;
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 5px;
}

.seed-value {
    display: block;
    padding: 8px;
    background-color: var(--primary-dark);
    border-radius: 4px;
    font-size: 11px;
    word-break: break-all;
    font-family: monospace;
}

#clientSeedInput {
    width: 100%;
    background-color: var(--primary-dark);
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: monospace;
}

.seed-btn {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border: 1px solid var(--primary-light);
    color: var(--text-light);
    padding: 10px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
}

.seed-btn:hover {
    background: linear-gradient(to bottom, var(--accent-gold-dark), var(--accent-gold));
    border-color: var(--accent-gold);
    color: var(--text-dark);
}

/* Game History */
.history-list {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.history-item {
    padding: 12px;
    margin-bottom: 10px;
    background-color: var(--primary-dark);
    border-radius: var(--border-radius);
    font-size: 12px;
    border-left: 3px solid var(--accent-gold-dark);
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-item .history-round {
    font-weight: 700;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    color: var(--accent-gold);
}

.history-item .history-hand {
    margin-bottom: 6px;
    font-family: monospace;
    font-size: 11px;
}

.history-item .history-result {
    display: flex;
    justify-content: space-between;
    color: var(--accent-gold);
    font-weight: 500;
}

.no-history {
    text-align: center;
    color: var(--text-medium);
    font-style: italic;
    padding: 20px;
}

/* Statistics */
.stats-grid {
    padding: 15px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-medium);
}

.stat-value {
    font-size: 14px;
    font-weight: 700;
    color: var(--accent-gold);
}

/* Rush Strategy Section */
.rush-strategy-section {
    display: none;
}

.strategy-content {
    padding: 15px;
}

.strategy-tips {
    margin-bottom: 15px;
}

.strategy-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: var(--primary-dark);
    border-radius: var(--border-radius);
    font-size: 13px;
    border-left: 3px solid var(--accent-gold);
}

.strategy-tip i {
    color: var(--accent-gold);
    font-size: 14px;
}

.hand-strength-meter {
    margin-bottom: 15px;
}

.hand-strength-meter label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: var(--text-medium);
}

.strength-bar {
    background: var(--primary-dark);
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.strength-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-red), var(--accent-gold), var(--table-green));
    width: 0%;
    transition: var(--transition);
    border-radius: 10px;
}

.strength-text {
    font-size: 12px;
    color: var(--text-medium);
    margin-top: 5px;
    display: block;
}

.optimal-play-suggestion {
    background: var(--primary-dark);
    padding: 12px;
    border-radius: var(--border-radius);
    font-size: 13px;
    border-left: 3px solid var(--accent-gold);
}

.optimal-play-suggestion strong {
    color: var(--accent-gold);
}

/* Modal Styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--primary-medium);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--accent-gold-dark);
}

.modal-header {
    background: var(--primary-dark);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--accent-gold-dark);
}

.modal-header h2 {
    color: var(--accent-gold);
    font-size: 20px;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.close-modal-btn:hover {
    color: var(--accent-gold);
    background: rgba(200, 155, 60, 0.1);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

.rules-section, .verify-section {
    margin-bottom: 20px;
}

.rules-section h3, .verify-section h3 {
    color: var(--accent-gold);
    font-size: 18px;
    margin-bottom: 10px;
    border: none;
    padding: 0;
    text-align: left;
}

.rules-section p, .rules-section li, .verify-section p, .verify-section li {
    font-size: 14px;
    margin-bottom: 5px;
}

.rules-section ul, .rules-section ol, .verify-section ul, .verify-section ol {
    padding-left: 20px;
    margin-top: 5px;
    margin-bottom: 10px;
}

/* Verify Modal */
.verify-inputs {
    margin: 15px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    margin-bottom: 5px;
    font-size: 14px;
}

.input-group input {
    padding: 10px;
    border-radius: var(--border-radius);
    border: 1px solid var(--primary-light);
    background-color: var(--primary-dark);
    color: var(--text-light);
    font-family: monospace;
}

.verify-btn {
    background: linear-gradient(to bottom, var(--accent-gold-dark), var(--accent-gold));
    border: 1px solid var(--accent-gold-dark);
    color: var(--text-dark);
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: var(--transition);
    width: 100%;
    margin: 15px 0;
}

.verify-btn:hover {
    background: linear-gradient(to bottom, var(--accent-gold), var(--accent-gold-light));
}

.verify-result {
    padding: 15px;
    background-color: var(--primary-dark);
    border-radius: var(--border-radius);
    font-size: 14px;
    line-height: 1.6;
}

.current-hand-info {
    padding: 15px;
    background-color: var(--primary-dark);
    border-radius: var(--border-radius);
    font-size: 14px;
}

.hidden {
    display: none !important;
}

/* Animations */
@keyframes cardFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0deg); }
}

@keyframes highlight {
    0% { box-shadow: 0 0 5px var(--accent-gold); }
    50% { box-shadow: 0 0 20px var(--accent-gold); }
    100% { box-shadow: 0 0 5px var(--accent-gold); }
}

@keyframes resultShow {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .info-panel {
        max-width: none;
    }
}

@media (max-width: 992px) {
    .game-container {
        padding: 10px 5px;
    }
    
    .header-right {
        flex-basis: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
    }
    
    .view-toggle {
        margin-left: 0;
    }
    
    .cards-container {
        gap: 8px;
    }
    
    .card {
        width: 90px;
        height: 130px;
    }
    
    .card .card-value, .card .card-suit {
        font-size: 16px;
    }
    
    .card .card-center {
        font-size: 32px;
    }
    
    .credits-section {
        flex-direction: column;
        gap: 15px;
    }
    
    .credits-display {
        justify-content: center;
    }
    
    .bet-controls {
        justify-content: center;
    }
    
    /* Show mobile paytable toggle */
    .mobile-paytable-toggle {
        display: block;
    }
    
    /* Hide paytable by default on mobile */
    .paytable {
        display: none;
    }
    
    .paytable.active {
        display: block;
    }
}

@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        padding: 15px 0;
    }
    
    .header-left,
    .header-right {
        flex: none;
        width: 100%;
    }
    
    .header-right {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
        margin-top: 10px;
    }
    
    .back-link {
        margin: 0 auto;
        display: inline-flex;
    }
    
    .info-btn {
        margin: 0 5px;
        padding: 8px 12px;
        font-size: 12px;
    }
    
    .game-title {
        font-size: 24px;
    }
    
    .game-subtitle {
        font-size: 12px;
    }
    
    .cards-container {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .card {
        width: 80px;
        height: 115px;
    }
    
    .card .card-value, .card .card-suit {
        font-size: 14px;
    }
    
    .card .card-center {
        font-size: 28px;
    }
    
    .hold-btn {
        width: 70px;
        padding: 6px 8px;
        font-size: 10px;
    }
    
    .hand-result {
        min-width: 200px;
        padding: 12px 20px;
        font-size: 16px;
    }
    
    .main-btn {
        padding: 12px 20px;
        font-size: 14px;
        min-width: 100px;
    }
    
    .quick-action-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .paytable-grid {
        font-size: 10px;
    }
    
    .pay-header > *,
    .pay-row > * {
        padding: 6px 2px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .rush-stats-bar {
        padding: 10px;
        font-size: 12px;
    }
    
    .rush-stat {
        padding: 0 10px;
    }
    
    /* Show mobile info toggle */
    .mobile-info-toggle {
        display: block;
    }
    
    /* Hide info panel on mobile by default */
    .info-panel > .info-section {
        display: none;
    }
    
    .info-panel.active > .info-section {
        display: block;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-body {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 20px;
    }
    
    .game-subtitle {
        font-size: 11px;
    }
    
    .main-content {
        gap: 10px;
    }
    
    .game-section {
        gap: 10px;
    }
    
    .card {
        width: 70px;
        height: 100px;
    }
    
    .card .card-value, .card .card-suit {
        font-size: 12px;
    }
    
    .card .card-center {
        font-size: 24px;
    }
    
    .hold-btn {
        width: 60px;
        padding: 4px 6px;
        font-size: 9px;
    }
    
    .btn-text, .back-text, .bet-btn-text, .hold-text {
        display: none;
    }
    
    .info-btn i {
        margin-right: 0;
    }
    
    .info-btn {
        padding: 8px;
    }
    
    .hand-result {
        padding: 10px 15px;
        font-size: 14px;
        min-width: 180px;
    }
    
    .main-btn {
        padding: 10px 15px;
        font-size: 12px;
        min-width: 80px;
    }
    
    .bet-btn {
        min-width: 80px;
        padding: 8px 10px;
        font-size: 12px;
    }
    
    .credits-item .value {
        font-size: 16px;
    }
    
    .credits-item .label {
        font-size: 11px;
    }
    
    .quick-action-btn {
        padding: 8px 6px;
        font-size: 10px;
    }
    
    .quick-action-btn i {
        font-size: 14px;
    }
}

/* Special Styles for very small phones */
@media (max-width: 375px) {
    .cards-container {
        gap: 6px;
    }
    
    .card {
        width: 65px;
        height: 90px;
    }
    
    .card .card-value, .card .card-suit {
        font-size: 11px;
    }
    
    .card .card-center {
        font-size: 20px;
    }
    
    .hold-btn {
        width: 55px;
        padding: 3px 4px;
        font-size: 8px;
    }
    
    .hand-result {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 160px;
    }
    
    .paytable-grid {
        font-size: 9px;
    }
    
    .pay-header > *,
    .pay-row > * {
        padding: 4px 1px;
    }
    
    .main-btn {
        padding: 8px 12px;
        font-size: 11px;
        min-width: 70px;
    }
    
    .bet-btn {
        min-width: 70px;
        padding: 6px 8px;
        font-size: 11px;
    }
    
    .credits-display {
        gap: 15px;
    }
    
    .credits-item .value {
        font-size: 14px;
    }
    
    .credits-item .label {
        font-size: 10px;
    }
}