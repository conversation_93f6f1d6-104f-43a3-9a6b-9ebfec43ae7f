<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Scarab Spin - Ancient Egyptian Archaeology Game</title>
    <link rel="stylesheet" href="scarab.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;900&family=Papyrus&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="assets/diamond_favicon.svg" type="image/svg+xml">
</head>
<body>
    <!-- Pro View Toggle -->
    <button class="view-mode-toggle" id="viewModeToggle">
        <i class="fas fa-eye"></i>
        <span id="viewModeText">Pro View</span>
    </button>

    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Games
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">Scarab Spin</h1>
                <p class="game-subtitle">Archaeological Pattern Recognition & Egyptian Mathematics</p>
            </div>
            <div class="header-right">
                <button id="tutorialBtn" class="info-btn">
                    <i class="fas fa-scroll"></i>
                    Tutorial
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- Game Area -->
            <div class="game-section">
                <!-- Advanced Stats Panel (Pro View Only) -->
                <div class="advanced-stats pro-only">
                    <h3><i class="fas fa-chart-line"></i> Advanced Analytics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value" id="winRate">0%</span>
                            <span class="stat-label">Win Rate</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="avgMultiplier">1.0x</span>
                            <span class="stat-label">Avg Multiplier</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="patternEfficiency">0%</span>
                            <span class="stat-label">Pattern Efficiency</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="rtp">96.5%</span>
                            <span class="stat-label">RTP</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="variance">Medium</span>
                            <span class="stat-label">Variance</span>
                        </div>
                    </div>
                </div>

                <!-- Archaeology Meter -->
                <div class="archaeology-meter">
                    <div class="meter-label">
                        <i class="fas fa-search"></i>
                        Archaeology Progress
                        <span class="pro-only" style="margin-left: auto; font-size: 12px; opacity: 0.7;">
                            Next: <span id="nextReward">Math Lesson</span>
                        </span>
                    </div>
                    <div class="meter-container">
                        <div class="meter-fill" id="archaeologyFill"></div>
                        <div class="meter-text" id="archaeologyText">0%</div>
                    </div>
                </div>

                <!-- Reel Container -->
                <div class="reel-container">
                    <div class="sand-particles" id="sandParticles"></div>
                    
                    <!-- Probability Heatmap Overlay -->
                    <div class="probability-overlay" id="probabilityOverlay">
                        <div class="heatmap-toggle">
                            <input type="checkbox" id="heatmapToggle">
                            <label for="heatmapToggle">
                                <span class="standard-only">Show Hints</span>
                                <span class="pro-only">Probability Heatmap</span>
                            </label>
                        </div>
                    </div>

                    <div class="reels" id="reels">
                        <div class="reel" data-reel="0">
                            <div class="reel-header">
                                <button class="hold-btn" data-reel="0" id="holdBtn0">
                                    <i class="fas fa-lock-open"></i>
                                    <span class="hold-text">Hold</span>
                                </button>
                            </div>
                            <div class="symbol-container" data-position="0" tabindex="0" role="button" aria-label="Symbol position 1">
                                <div class="symbol" id="symbol_0_0">𓆣</div>
                            </div>
                            <div class="pro-only" style="text-align: center; font-size: 10px; padding: 2px; background: rgba(0,0,0,0.3);">
                                <span id="symbolProb_0">Loading...</span>
                            </div>
                        </div>
                        
                        <div class="reel" data-reel="1">
                            <div class="reel-header">
                                <button class="hold-btn" data-reel="1" id="holdBtn1">
                                    <i class="fas fa-lock-open"></i>
                                    <span class="hold-text">Hold</span>
                                </button>
                            </div>
                            <div class="symbol-container" data-position="1" tabindex="0" role="button" aria-label="Symbol position 2">
                                <div class="symbol" id="symbol_1_0">𓂀</div>
                            </div>
                            <div class="pro-only" style="text-align: center; font-size: 10px; padding: 2px; background: rgba(0,0,0,0.3);">
                                <span id="symbolProb_1">Loading...</span>
                            </div>
                        </div>
                        
                        <div class="reel" data-reel="2">
                            <div class="reel-header">
                                <button class="hold-btn" data-reel="2" id="holdBtn2">
                                    <i class="fas fa-lock-open"></i>
                                    <span class="hold-text">Hold</span>
                                </button>
                            </div>
                            <div class="symbol-container" data-position="2" tabindex="0" role="button" aria-label="Symbol position 3">
                                <div class="symbol" id="symbol_2_0">🏔️</div>
                            </div>
                            <div class="pro-only" style="text-align: center; font-size: 10px; padding: 2px; background: rgba(0,0,0,0.3);">
                                <span id="symbolProb_2">Loading...</span>
                            </div>
                        </div>
                        
                        <div class="reel" data-reel="3">
                            <div class="reel-header">
                                <button class="hold-btn" data-reel="3" id="holdBtn3">
                                    <i class="fas fa-lock-open"></i>
                                    <span class="hold-text">Hold</span>
                                </button>
                            </div>
                            <div class="symbol-container" data-position="3" tabindex="0" role="button" aria-label="Symbol position 4">
                                <div class="symbol" id="symbol_3_0">☥</div>
                            </div>
                            <div class="pro-only" style="text-align: center; font-size: 10px; padding: 2px; background: rgba(0,0,0,0.3);">
                                <span id="symbolProb_3">Loading...</span>
                            </div>
                        </div>
                        
                        <div class="reel" data-reel="4">
                            <div class="reel-header">
                                <button class="hold-btn" data-reel="4" id="holdBtn4">
                                    <i class="fas fa-lock-open"></i>
                                    <span class="hold-text">Hold</span>
                                </button>
                            </div>
                            <div class="symbol-container" data-position="4" tabindex="0" role="button" aria-label="Symbol position 5">
                                <div class="symbol" id="symbol_4_0">🦅</div>
                            </div>
                            <div class="pro-only" style="text-align: center; font-size: 10px; padding: 2px; background: rgba(0,0,0,0.3);">
                                <span id="symbolProb_4">Loading...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Pattern Detection Display -->
                    <div class="pattern-display" id="patternDisplay">
                        <div class="pattern-info">
                            <span class="pattern-label">Patterns Detected:</span>
                            <div class="pattern-list" id="patternList">None</div>
                        </div>
                        <div class="multiplier-info">
                            <span class="multiplier-label">Total Multiplier:</span>
                            <span class="multiplier-value" id="multiplierValue">1.0x</span>
                        </div>
                        <div class="pro-only" style="text-align: center; margin-top: 5px;">
                            <span style="font-size: 12px; opacity: 0.8;">Expected Value: <span id="expectedValue">0 pts</span></span>
                        </div>
                    </div>
                </div>

                <!-- Controls -->
                <div class="controls">
                    <div class="control-row">
                        <button id="spinBtn" class="spin-btn" aria-label="Spin the reels">
                            <i class="fas fa-sync-alt"></i>
                            <span>Spin Reels</span>
                        </button>
                        <button id="excavateBtn" class="excavate-btn" aria-label="Toggle excavation mode">
                            <i class="fas fa-search"></i>
                            <span>Excavate Mode</span>
                        </button>
                        <button id="analyzeBtn" class="analyze-btn" aria-label="Analyze current patterns">
                            <i class="fas fa-calculator"></i>
                            <span>Analyze Patterns</span>
                        </button>
                    </div>
                    
                    <div class="spin-counter">
                        <span>Spins Today: <span id="spinCount">0</span>/50</span>
                        <span class="separator">|</span>
                        <span>Knowledge Points: <span id="knowledgePoints">0</span></span>
                        <span class="pro-only">
                            <span class="separator">|</span>
                            <span>Session Score: <span id="sessionScore">0</span></span>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Information Panel -->
            <div class="info-panel">
                <!-- Performance Metrics (Pro View Only) -->
                <div class="info-section pro-only">
                    <h3><i class="fas fa-tachometer-alt"></i> Performance Metrics</h3>
                    <div class="performance-metrics">
                        <div class="metric-card">
                            <span class="metric-value" id="totalSpins">0</span>
                            <span class="metric-label">Total Spins</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-value" id="totalWins">0</span>
                            <span class="metric-label">Total Wins</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-value" id="biggestWin">0</span>
                            <span class="metric-label">Biggest Win</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-value" id="streakCount">0</span>
                            <span class="metric-label">Current Streak</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-value" id="learningProgress">0%</span>
                            <span class="metric-label">Learning Progress</span>
                        </div>
                        <div class="metric-card">
                            <span class="metric-value" id="efficiency">0%</span>
                            <span class="metric-label">Efficiency</span>
                        </div>
                    </div>
                </div>

                <!-- Symbol Probabilities -->
                <div class="info-section probability-section">
                    <h3>
                        <span class="standard-only">Symbol Values</span>
                        <span class="pro-only">Symbol Probabilities & RTP</span>
                    </h3>
                    <div class="probability-table">
                        <div class="prob-row">
                            <span class="symbol-icon">🪲</span>
                            <span class="symbol-name">Scarab</span>
                            <span class="probability">45%</span>
                            <span class="base-value">10 pts</span>
                        </div>
                        <div class="prob-row">
                            <span class="symbol-icon">𓂀</span>
                            <span class="symbol-name">Hieroglyph</span>
                            <span class="probability">30%</span>
                            <span class="base-value">25 pts</span>
                        </div>
                        <div class="prob-row">
                            <span class="symbol-icon">🏔️</span>
                            <span class="symbol-name">Pyramid</span>
                            <span class="probability">15%</span>
                            <span class="base-value">50 pts</span>
                        </div>
                        <div class="prob-row">
                            <span class="symbol-icon">☥</span>
                            <span class="symbol-name">Ankh</span>
                            <span class="probability">8%</span>
                            <span class="base-value">2x Multi</span>
                        </div>
                        <div class="prob-row special">
                            <span class="symbol-icon">🦅</span>
                            <span class="symbol-name">Sphinx</span>
                            <span class="probability">2%</span>
                            <span class="base-value">100x + Puzzle</span>
                        </div>
                    </div>
                    <div class="pro-only" style="padding: 10px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 12px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>Base RTP:</span>
                            <span style="color: var(--accent-gold-light);">96.5%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>With Patterns:</span>
                            <span style="color: var(--accent-gold-light);">98.2%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>Your RTP:</span>
                            <span style="color: var(--accent-gold-light);" id="personalRTP">---%</span>
                        </div>
                    </div>
                </div>

                <!-- Pattern Multipliers -->
                <div class="info-section pattern-section">
                    <h3>
                        <span class="standard-only">Pattern Guide</span>
                        <span class="pro-only">Pattern Multipliers & Strategy</span>
                    </h3>
                    <div class="pattern-guide">
                        <div class="pattern-item">
                            <span class="pattern-type">Horizontal Line</span>
                            <span class="pattern-multi">1.0x</span>
                        </div>
                        <div class="pattern-item">
                            <span class="pattern-type">Diagonal Pattern</span>
                            <span class="pattern-multi">1.5x</span>
                        </div>
                        <div class="pattern-item">
                            <span class="pattern-type">Scarab Cluster</span>
                            <span class="pattern-multi">2.0x</span>
                        </div>
                        <div class="pattern-item">
                            <span class="pattern-type">Hieroglyph Sequence</span>
                            <span class="pattern-multi">3.0x</span>
                        </div>
                    </div>
                    <div class="pro-only" style="padding: 10px; border-top: 1px solid rgba(255,255,255,0.1); font-size: 12px;">
                        <div style="margin-bottom: 8px; color: var(--accent-gold); font-weight: bold;">Optimal Strategy:</div>
                        <div id="strategyTip" style="line-height: 1.4;">Hold highest value symbols and aim for pattern formations.</div>
                    </div>
                </div>

                <!-- Score Display -->
                <div class="info-section score-section">
                    <h3>
                        <span class="standard-only">Current Session</span>
                        <span class="pro-only">Session Analytics</span>
                    </h3>
                    <div class="score-display">
                        <div class="score-item">
                            <span class="score-label">Total Score:</span>
                            <span class="score-value" id="totalScore">0</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Last Spin:</span>
                            <span class="score-value" id="lastSpinScore">0</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">Best Pattern:</span>
                            <span class="score-value" id="bestPattern">None</span>
                        </div>
                        <div class="pro-only score-item">
                            <span class="score-label">Avg Score/Spin:</span>
                            <span class="score-value" id="avgScorePerSpin">0</span>
                        </div>
                        <div class="pro-only score-item">
                            <span class="score-label">Success Rate:</span>
                            <span class="score-value" id="successRate">0%</span>
                        </div>
                    </div>
                </div>

                <!-- Puzzle Collection -->
                <div class="info-section puzzle-section">
                    <h3>
                        <span class="standard-only">Puzzle Collection</span>
                        <span class="pro-only">Learning Progress</span>
                    </h3>
                    <div class="puzzle-progress">
                        <div class="puzzle-pieces">
                            <div class="puzzle-piece" data-piece="0" id="piece0"></div>
                            <div class="puzzle-piece" data-piece="1" id="piece1"></div>
                            <div class="puzzle-piece" data-piece="2" id="piece2"></div>
                            <div class="puzzle-piece" data-piece="3" id="piece3"></div>
                            <div class="puzzle-piece" data-piece="4" id="piece4"></div>
                        </div>
                        <div class="puzzle-status">
                            <span id="puzzleCount">0</span>/5 pieces collected
                        </div>
                        <button id="solvePuzzleBtn" class="solve-puzzle-btn" disabled aria-label="Solve ancient puzzle when all pieces are collected">
                            Solve Ancient Puzzle
                        </button>
                        <div class="pro-only" style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.1); text-align: center; font-size: 12px;">
                            <div style="margin-bottom: 5px;">Learning Modules Completed:</div>
                            <div style="color: var(--accent-gold-light); font-weight: bold;"><span id="modulesCompleted">0</span>/12</div>
                            <div style="margin-top: 5px; font-size: 10px; opacity: 0.8;">Next: Egyptian Multiplication</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Educational Modal -->
        <div class="modal-overlay hidden" id="educationalModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">Educational Content</h2>
                    <button class="close-modal-btn" id="closeEducationalBtn" aria-label="Close educational content">×</button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- Dynamic educational content -->
                </div>
            </div>
        </div>

        <!-- Tutorial Modal -->
        <div class="modal-overlay hidden" id="tutorialModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>How to Play Scarab Spin</h2>
                    <button class="close-modal-btn" id="closeTutorialBtn" aria-label="Close tutorial">×</button>
                </div>
                <div class="modal-body">
                    <div class="tutorial-section">
                        <h3>Game Overview</h3>
                        <p>Scarab Spin combines archaeological exploration with pattern recognition. Align Egyptian symbols to uncover ancient treasures while learning about Egyptian mathematics and culture.</p>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>How to Play</h3>
                        <ol>
                            <li><strong>Spin the Reels:</strong> Tap "Spin Reels" to generate new symbols</li>
                            <li><strong>Hold Reels:</strong> Use "Hold" buttons to lock promising reels between spins</li>
                            <li><strong>Recognize Patterns:</strong> Look for horizontal lines, diagonals, clusters, and sequences</li>
                            <li><strong>Excavate:</strong> Use "Excavate Mode" to reveal hidden symbols</li>
                            <li><strong>Analyze:</strong> Tap "Analyze Patterns" to calculate optimal strategies</li>
                        </ol>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Mobile Controls</h3>
                        <ul>
                            <li>Tap symbols to learn about Egyptian culture</li>
                            <li>Swipe or tap hold buttons to lock reels</li>
                            <li>Use Pro View toggle for advanced analytics</li>
                            <li>Pinch to zoom is disabled for stable gameplay</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Pro View Features</h3>
                        <ul>
                            <li>Real-time probability calculations</li>
                            <li>Advanced performance metrics</li>
                            <li>Strategy optimization tips</li>
                            <li>Learning progress tracking</li>
                            <li>Personal RTP analysis</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Educational Features</h3>
                        <ul>
                            <li>Learn Egyptian mathematics through symbol calculations</li>
                            <li>Discover archaeological facts by tapping symbols</li>
                            <li>Solve ancient puzzles to unlock permanent upgrades</li>
                            <li>Experiment with probability in the Pro View mode</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Responsible Play</h3>
                        <ul>
                            <li>Daily limit of 50 spins to encourage thoughtful play</li>
                            <li>Focus on learning and pattern recognition over scoring</li>
                            <li>Use the probability tools to understand game mechanics</li>
                            <li>Take breaks and enjoy the educational content</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Puzzle Solving Modal -->
        <div class="modal-overlay hidden" id="puzzleModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Ancient Egyptian Puzzle</h2>
                    <button class="close-modal-btn" id="closePuzzleBtn" aria-label="Close puzzle">×</button>
                </div>
                <div class="modal-body">
                    <div class="puzzle-game" id="puzzleGame">
                        <!-- Dynamic puzzle content -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="scarab.js"></script>
</body>
</html>