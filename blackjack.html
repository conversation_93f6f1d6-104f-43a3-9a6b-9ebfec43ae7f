<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blackjack - Strategic Card Challenge</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="blackjack.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Session Timer -->
    <div class="session-timer" id="sessionTimer">
        <div>Session: <span id="sessionTime">00:00</span></div>
        <div style="font-size: 0.8rem; opacity: 0.8;">30min break recommended</div>
    </div>

    <div class="blackjack-container">
        <div class="game-header">
            <a href="index.html" class="back-link">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Games</span>
            </a>
            <div>
                <h1 class="game-title">STRATEGIC BLACKJACK</h1>
                <p class="game-subtitle">Fair odds • Skill-based • Educational • Transparent AI dealer</p>
            </div>
            <div class="view-mode-toggle">
                <button id="standardViewBtn" class="active">Standard</button>
                <button id="proViewBtn">Pro View</button>
            </div>
        </div>

        <!-- Pro View Stats -->
        <div class="pro-view-stats">
            <div class="pro-view-title">
                <i class="fas fa-chart-line"></i> Pro View Analytics
            </div>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Running Count</div>
                    <div class="pro-stat-value" id="runningCount">0</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">True Count</div>
                    <div class="pro-stat-value" id="trueCount">0.0</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Advantage</div>
                    <div class="pro-stat-value" id="playerAdvantage">0.0%</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Optimal Bet</div>
                    <div class="pro-stat-value" id="optimalBet">200</div>
                </div>
            </div>

            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <div style="flex: 1; min-width: 250px;">
                    <div class="card-count-tracker">
                        <div class="pro-view-title" style="font-size: 0.9rem;">
                            <i class="fas fa-calculator"></i> Card Counting
                        </div>
                        
                        <div class="count-display">
                            <span class="count-label">Hi-Lo Count:</span>
                            <span class="count-value count-neutral" id="hiLoCount">0</span>
                        </div>
                        
                        <div class="count-display">
                            <span class="count-label">Omega II Count:</span>
                            <span class="count-value count-neutral" id="omegaCount">0</span>
                        </div>
                        
                        <div class="count-display">
                            <span class="count-label">Cards Seen:</span>
                            <span class="count-value count-neutral" id="cardsSeen">0</span>
                        </div>
                    </div>
                </div>
                
                <div style="flex: 1; min-width: 250px;">
                    <div class="pro-view-title" style="font-size: 0.9rem;">
                        <i class="fas fa-history"></i> Hand History
                    </div>
                    <div class="hand-history" id="handHistory">
                        <div class="history-entry">
                            <span class="history-cards">No hands played yet</span>
                            <span class="history-result">-</span>
                        </div>
                    </div>
                </div>
                
                <div style="flex: 1; min-width: 250px;">
                    <div class="advanced-stats">
                        <div class="stats-chart">
                            <div class="chart-title">Win Rate by Hand</div>
                            <div class="chart-bar">
                                <span class="chart-label">Wins</span>
                                <div class="chart-fill">
                                    <div class="chart-progress" id="winChart" style="width: 0%"></div>
                                </div>
                                <span class="chart-value" id="winPercent">0%</span>
                            </div>
                            <div class="chart-bar">
                                <span class="chart-label">Push</span>
                                <div class="chart-fill">
                                    <div class="chart-progress" id="pushChart" style="width: 0%"></div>
                                </div>
                                <span class="chart-value" id="pushPercent">0%</span>
                            </div>
                            <div class="chart-bar">
                                <span class="chart-label">Loss</span>
                                <div class="chart-fill">
                                    <div class="chart-progress" id="lossChart" style="width: 0%"></div>
                                </div>
                                <span class="chart-value" id="lossPercent">0%</span>
                            </div>
                        </div>
                        
                        <div class="stats-chart">
                            <div class="chart-title">Action Frequency</div>
                            <div class="chart-bar">
                                <span class="chart-label">Hit</span>
                                <div class="chart-fill">
                                    <div class="chart-progress" id="hitChart" style="width: 0%"></div>
                                </div>
                                <span class="chart-value" id="hitPercent">0%</span>
                            </div>
                            <div class="chart-bar">
                                <span class="chart-label">Stand</span>
                                <div class="chart-fill">
                                    <div class="chart-progress" id="standChart" style="width: 0%"></div>
                                </div>
                                <span class="chart-value" id="standPercent">0%</span>
                            </div>
                            <div class="chart-bar">
                                <span class="chart-label">Double</span>
                                <div class="chart-fill">
                                    <div class="chart-progress" id="doubleChart" style="width: 0%"></div>
                                </div>
                                <span class="chart-value" id="doublePercent">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="strategy-matrix">
                <div class="pro-view-title" style="font-size: 0.9rem;">
                    <i class="fas fa-table"></i> Basic Strategy Matrix
                </div>
                <table class="matrix-table">
                    <thead>
                        <tr>
                            <th>Player</th>
                            <th>2</th><th>3</th><th>4</th><th>5</th><th>6</th>
                            <th>7</th><th>8</th><th>9</th><th>10</th><th>A</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th>8</th>
                            <td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td>
                            <td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td>
                        </tr>
                        <tr>
                            <th>9</th>
                            <td class="matrix-hit">H</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td>
                            <td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td>
                        </tr>
                        <tr>
                            <th>10</th>
                            <td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td>
                            <td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td>
                        </tr>
                        <tr>
                            <th>11</th>
                            <td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td>
                            <td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-double">D</td><td class="matrix-hit">H</td>
                        </tr>
                        <tr>
                            <th>12</th>
                            <td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td>
                            <td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td><td class="matrix-hit">H</td>
                        </tr>
                        <tr>
                            <th>17</th>
                            <td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td>
                            <td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td><td class="matrix-stand">S</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="pro-controls">
                <button class="pro-btn" id="analyzeHandBtn">
                    <i class="fas fa-search"></i> Analyze Hand
                </button>
                <button class="pro-btn" id="resetCountBtn">
                    <i class="fas fa-undo"></i> Reset Count
                </button>
                <button class="pro-btn" id="optimalBetBtn">
                    <i class="fas fa-coins"></i> Set Optimal Bet
                </button>
                <button class="pro-btn" id="exportDataBtn">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats -->
            <div class="stats-panel">
                <h3 style="color: white; margin-bottom: 1rem; text-align: center;">Player Stats</h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">Bankroll</div>
                        <div class="stat-value" id="bankroll">10,000</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Current Bet</div>
                        <div class="stat-value" id="currentBet">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Hands Won</div>
                        <div class="stat-value" id="handsWon">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Win Rate</div>
                        <div class="stat-value" id="winRate">0%</div>
                    </div>
                </div>

                <div class="difficulty-selector">
                    <button class="difficulty-btn active" data-level="novice">Novice</button>
                    <button class="difficulty-btn" data-level="pro">Pro</button>
                    <button class="difficulty-btn" data-level="expert">Expert</button>
                </div>
                
                <div class="deck-info">
                    <div class="stat-card">
                        <div class="stat-label">Cards Remaining</div>
                        <div class="stat-value" id="cardsRemaining">312</div>
                    </div>
                    
                    <div class="deck-penetration">
                        <div class="stat-label">Deck Penetration</div>
                        <div class="penetration-bar">
                            <div class="penetration-fill" id="penetrationFill" style="width: 0%"></div>
                        </div>
                        <div style="color: white; font-size: 0.8rem; margin-top: 0.5rem;">
                            <span id="penetrationPercent">0%</span> used
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Table -->
            <div class="game-table">
                <div class="betting-area">
                    <label style="color: white; font-weight: bold;">Place Your Bet:</label>
                    <input type="number" class="bet-input" id="betAmount" min="100" max="500" value="200" step="100">
                    <div class="bet-suggestions">
                        <div class="bet-chip chip-100" data-amount="100">100</div>
                        <div class="bet-chip chip-500" data-amount="500">500</div>
                        <div class="bet-chip chip-1000" data-amount="1000">1K</div>
                    </div>
                </div>

                <div class="cards-area">
                    <!-- Dealer Area -->
                    <div class="dealer-area">
                        <div class="hand-label">Dealer</div>
                        <div class="cards-container" id="dealerCards">
                            <!-- Cards will be added dynamically -->
                        </div>
                        <div class="hand-total" id="dealerTotal">0</div>
                    </div>

                    <!-- Player Area -->
                    <div class="player-area">
                        <div class="hand-label">Player</div>
                        <div class="cards-container" id="playerCards">
                            <!-- Cards will be added dynamically -->
                        </div>
                        <div class="hand-total" id="playerTotal">0</div>
                    </div>
                </div>

                <div class="game-controls">
                    <button class="control-btn btn-deal" id="dealBtn" onclick="dealNewHand()">Deal Cards</button>
                    <button class="control-btn btn-hit" id="hitBtn" onclick="playerHit()" disabled>Hit</button>
                    <button class="control-btn btn-stand" id="standBtn" onclick="playerStand()" disabled>Stand</button>
                    <button class="control-btn btn-double" id="doubleBtn" onclick="playerDouble()" disabled>Double</button>
                    <button class="control-btn btn-split" id="splitBtn" onclick="playerSplit()" disabled>Split</button>
                    <button class="control-btn btn-surrender" id="surrenderBtn" onclick="playerSurrender()" disabled>Surrender</button>
                </div>
            </div>

            <!-- Strategy Panel -->
            <div class="strategy-panel">
                <div class="strategy-coach">
                    <h3 style="color: white; margin-bottom: 1rem; text-align: center;">Strategy Coach</h3>
                    
                    <div class="coach-recommendation" id="strategyRecommendation">
                        <div class="recommendation-action">Waiting for hand...</div>
                        <div class="recommendation-reason">Place your bet and deal cards to begin</div>
                    </div>

                    <div class="risk-meter">
                        <div class="stat-label">Risk Assessment</div>
                        <div class="risk-bar">
                            <div class="risk-indicator" id="riskIndicator" style="left: 50%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; color: white; font-size: 0.8rem; margin-top: 0.3rem;">
                            <span>Safe</span>
                            <span>Risky</span>
                        </div>
                    </div>

                    <div class="probability-display">
                        <div class="prob-card">
                            <div class="prob-label">Win Probability</div>
                            <div class="prob-value prob-neutral" id="winProb">--</div>
                        </div>
                        <div class="prob-card">
                            <div class="prob-label">Bust Probability</div>
                            <div class="prob-value prob-neutral" id="bustProb">--</div>
                        </div>
                        <div class="prob-card">
                            <div class="prob-label">Expected Value</div>
                            <div class="prob-value prob-neutral" id="expectedValue">--</div>
                        </div>
                        <div class="prob-card">
                            <div class="prob-label">House Edge</div>
                            <div class="prob-value prob-negative" id="houseEdge">0.5%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Result Modal -->
    <div class="result-modal" id="resultModal">
        <h2 id="resultTitle">Hand Result</h2>
        <p id="resultMessage"></p>
        <div id="resultDetails"></div>
        <div id="achievements"></div>
        <button class="control-btn btn-deal" onclick="closeResult()">Continue Playing</button>
    </div>

    <script src="assets/js/script.js"></script>
    <script src="blackjack.js"></script>
    <script>
        // Enhanced Blackjack Game with Pro View Features
        class EnhancedBlackjackGame extends BlackjackGame {
            constructor() {
                super();
                
                // Pro View variables
                this.viewMode = localStorage.getItem('blackjackViewMode') || 'standard';
                this.runningCount = 0;
                this.trueCount = 0;
                this.hiLoCount = 0;
                this.omegaCount = 0;
                this.cardsSeen = 0;
                this.playerAdvantage = 0;
                this.optimalBetAmount = 200;
                
                // Action tracking
                this.actionHistory = {
                    hit: 0,
                    stand: 0,
                    double: 0,
                    split: 0,
                    surrender: 0
                };
                
                // Result tracking
                this.resultHistory = {
                    wins: 0,
                    losses: 0,
                    pushes: 0
                };
                
                // Pro View elements
                this.initProViewElements();
                this.attachProViewEventListeners();
                this.setViewMode(this.viewMode);
            }
            
            initProViewElements() {
                // View mode toggle
                this.standardViewBtn = document.getElementById('standardViewBtn');
                this.proViewBtn = document.getElementById('proViewBtn');
                
                // Card counting displays
                this.runningCountDisplay = document.getElementById('runningCount');
                this.trueCountDisplay = document.getElementById('trueCount');
                this.hiLoCountDisplay = document.getElementById('hiLoCount');
                this.omegaCountDisplay = document.getElementById('omegaCount');
                this.cardsSeenDisplay = document.getElementById('cardsSeen');
                this.playerAdvantageDisplay = document.getElementById('playerAdvantage');
                this.optimalBetDisplay = document.getElementById('optimalBet');
                
                // Hand history
                this.handHistoryDisplay = document.getElementById('handHistory');
                
                // Charts
                this.winChart = document.getElementById('winChart');
                this.pushChart = document.getElementById('pushChart');
                this.lossChart = document.getElementById('lossChart');
                this.hitChart = document.getElementById('hitChart');
                this.standChart = document.getElementById('standChart');
                this.doubleChart = document.getElementById('doubleChart');
                
                // Chart percentages
                this.winPercent = document.getElementById('winPercent');
                this.pushPercent = document.getElementById('pushPercent');
                this.lossPercent = document.getElementById('lossPercent');
                this.hitPercent = document.getElementById('hitPercent');
                this.standPercent = document.getElementById('standPercent');
                this.doublePercent = document.getElementById('doublePercent');
                
                // Pro controls
                this.analyzeHandBtn = document.getElementById('analyzeHandBtn');
                this.resetCountBtn = document.getElementById('resetCountBtn');
                this.optimalBetBtn = document.getElementById('optimalBetBtn');
                this.exportDataBtn = document.getElementById('exportDataBtn');
            }
            
            attachProViewEventListeners() {
                // View mode toggle
                this.standardViewBtn.addEventListener('click', () => this.setViewMode('standard'));
                this.proViewBtn.addEventListener('click', () => this.setViewMode('pro'));
                
                // Pro controls
                this.analyzeHandBtn.addEventListener('click', () => this.analyzeCurrentHand());
                this.resetCountBtn.addEventListener('click', () => this.resetCardCount());
                this.optimalBetBtn.addEventListener('click', () => this.setOptimalBet());
                this.exportDataBtn.addEventListener('click', () => this.exportGameData());
            }
            
            setViewMode(mode) {
                this.viewMode = mode;
                localStorage.setItem('blackjackViewMode', mode);
                
                if (mode === 'standard') {
                    document.body.classList.remove('pro-view-active');
                    this.standardViewBtn.classList.add('active');
                    this.proViewBtn.classList.remove('active');
                } else {
                    document.body.classList.add('pro-view-active');
                    this.standardViewBtn.classList.remove('active');
                    this.proViewBtn.classList.add('active');
                }
                
                this.updateProViewDisplay();
            }
            
            // Enhanced card counting
            updateCardCount(card) {
                this.cardsSeen++;
                
                // Hi-Lo counting system
                const hiLoValues = {
                    '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,
                    '7': 0, '8': 0, '9': 0,
                    '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1
                };
                
                // Omega II counting system
                const omegaValues = {
                    '2': 1, '3': 1, '4': 2, '5': 2, '6': 2, '7': 1,
                    '8': 0, '9': -1, '10': -2, 'J': -2, 'Q': -2, 'K': -2, 'A': 0
                };
                
                this.hiLoCount += hiLoValues[card.value] || 0;
                this.omegaCount += omegaValues[card.value] || 0;
                
                // Calculate running and true counts
                this.runningCount = this.hiLoCount;
                const decksRemaining = this.deck.length / 52;
                this.trueCount = decksRemaining > 0 ? this.runningCount / decksRemaining : 0;
                
                // Calculate player advantage (rough estimate)
                this.playerAdvantage = this.trueCount * 0.5; // 0.5% per true count
                
                // Calculate optimal bet based on Kelly criterion
                if (this.playerAdvantage > 1) {
                    this.optimalBetAmount = Math.min(
                        Math.floor(this.balance * 0.05), // Max 5% of bankroll
                        Math.floor(this.balance * (this.playerAdvantage / 100) * 0.25) // Kelly fraction
                    );
                } else {
                    this.optimalBetAmount = 100; // Minimum bet when no advantage
                }
                
                this.updateProViewDisplay();
            }
            
            // Override drawCard to track card counting
            drawCard() {
                const card = super.drawCard();
                if (this.viewMode === 'pro') {
                    this.updateCardCount(card);
                }
                return card;
            }
            
            // Track player actions for Pro View
            trackAction(action) {
                if (this.actionHistory[action] !== undefined) {
                    this.actionHistory[action]++;
                    this.updateActionCharts();
                }
            }
            
            // Override player actions to track them
            playerHit() {
                super.playerHit();
                this.trackAction('hit');
            }
            
            playerStand() {
                super.playerStand();
                this.trackAction('stand');
            }
            
            playerDouble() {
                super.playerDouble();
                this.trackAction('double');
            }
            
            playerSplit() {
                super.playerSplit();
                this.trackAction('split');
            }
            
            playerSurrender() {
                super.playerSurrender();
                this.trackAction('surrender');
            }
            
            // Enhanced endHand to track results
            endHand(result, message) {
                // Track results for Pro View
                if (result === 'win' || result === 'blackjack') {
                    this.resultHistory.wins++;
                } else if (result === 'push') {
                    this.resultHistory.pushes++;
                } else {
                    this.resultHistory.losses++;
                }
                
                // Add to hand history
                this.addHandToHistory(result);
                
                // Call parent method
                super.endHand(result, message);
                
                // Update Pro View displays
                this.updateResultCharts();
                this.updateProViewDisplay();
            }
            
            // Add hand to history for Pro View
            addHandToHistory(result) {
                const playerCards = this.playerHand.map(card => card.value).join(',');
                const dealerCards = this.dealerHand.map(card => card.value).join(',');
                
                const entry = document.createElement('div');
                entry.className = 'history-entry';
                entry.innerHTML = `
                    <span class="history-cards">P:${playerCards} D:${dealerCards}</span>
                    <span class="history-result history-${result === 'win' || result === 'blackjack' ? 'win' : result === 'push' ? 'push' : 'loss'}">${result.toUpperCase()}</span>
                `;
                
                this.handHistoryDisplay.prepend(entry);
                
                // Limit history entries
                const entries = this.handHistoryDisplay.querySelectorAll('.history-entry');
                if (entries.length > 10) {
                    entries[entries.length - 1].remove();
                }
            }
            
            // Update Pro View displays
            updateProViewDisplay() {
                if (this.viewMode !== 'pro') return;
                
                // Update card counting displays
                this.runningCountDisplay.textContent = this.runningCount;
                this.runningCountDisplay.className = 'pro-stat-value ' + (this.runningCount > 0 ? 'count-positive' : this.runningCount < 0 ? 'count-negative' : 'count-neutral');
                
                this.trueCountDisplay.textContent = this.trueCount.toFixed(1);
                this.trueCountDisplay.className = 'pro-stat-value ' + (this.trueCount > 0 ? 'count-positive' : this.trueCount < 0 ? 'count-negative' : 'count-neutral');
                
                this.hiLoCountDisplay.textContent = this.hiLoCount;
                this.hiLoCountDisplay.className = 'count-value ' + (this.hiLoCount > 0 ? 'count-positive' : this.hiLoCount < 0 ? 'count-negative' : 'count-neutral');
                
                this.omegaCountDisplay.textContent = this.omegaCount;
                this.omegaCountDisplay.className = 'count-value ' + (this.omegaCount > 0 ? 'count-positive' : this.omegaCount < 0 ? 'count-negative' : 'count-neutral');
                
                this.cardsSeenDisplay.textContent = this.cardsSeen;
                this.cardsSeenDisplay.className = 'count-value count-neutral';
                
                this.playerAdvantageDisplay.textContent = this.playerAdvantage.toFixed(1) + '%';
                this.playerAdvantageDisplay.className = 'pro-stat-value ' + (this.playerAdvantage > 0 ? 'count-positive' : 'count-negative');
                
                this.optimalBetDisplay.textContent = this.optimalBetAmount;
            }
            
            // Update result charts
            updateResultCharts() {
                const totalGames = this.resultHistory.wins + this.resultHistory.losses + this.resultHistory.pushes;
                
                if (totalGames > 0) {
                    const winPercent = (this.resultHistory.wins / totalGames) * 100;
                    const pushPercent = (this.resultHistory.pushes / totalGames) * 100;
                    const lossPercent = (this.resultHistory.losses / totalGames) * 100;
                    
                    this.winChart.style.width = winPercent + '%';
                    this.pushChart.style.width = pushPercent + '%';
                    this.lossChart.style.width = lossPercent + '%';
                    
                    this.winPercent.textContent = winPercent.toFixed(1) + '%';
                    this.pushPercent.textContent = pushPercent.toFixed(1) + '%';
                    this.lossPercent.textContent = lossPercent.toFixed(1) + '%';
                }
            }
            
            // Update action charts
            updateActionCharts() {
                const totalActions = Object.values(this.actionHistory).reduce((sum, count) => sum + count, 0);
                
                if (totalActions > 0) {
                    const hitPercent = (this.actionHistory.hit / totalActions) * 100;
                    const standPercent = (this.actionHistory.stand / totalActions) * 100;
                    const doublePercent = (this.actionHistory.double / totalActions) * 100;
                    
                    this.hitChart.style.width = hitPercent + '%';
                    this.standChart.style.width = standPercent + '%';
                    this.doubleChart.style.width = doublePercent + '%';
                    
                    this.hitPercent.textContent = hitPercent.toFixed(1) + '%';
                    this.standPercent.textContent = standPercent.toFixed(1) + '%';
                    this.doublePercent.textContent = doublePercent.toFixed(1) + '%';
                }
            }
            
            // Pro View button functions
            analyzeCurrentHand() {
                if (this.gameState !== 'playing' || this.playerHand.length === 0) {
                    this.showNotification('ℹ️ No active hand to analyze');
                    return;
                }
                
                const playerTotal = this.calculateHandValue(this.playerHand);
                const dealerUpcard = this.dealerHand[0];
                const recommendation = this.getBasicStrategyAction(this.playerHand, dealerUpcard);
                const explanation = this.getActionExplanation(recommendation, playerTotal, dealerUpcard);
                
                const actionNames = {
                    'H': 'HIT',
                    'S': 'STAND', 
                    'D': 'DOUBLE DOWN',
                    'P': 'SPLIT',
                    'R': 'SURRENDER'
                };
                
                const analysisText = `ANALYSIS: ${actionNames[recommendation] || 'HIT'}\n\n${explanation}\n\nCount: Hi-Lo ${this.hiLoCount}, True Count ${this.trueCount.toFixed(1)}\nPlayer Advantage: ${this.playerAdvantage.toFixed(1)}%`;
                
                this.showNotification(analysisText);
            }
            
            resetCardCount() {
                this.runningCount = 0;
                this.trueCount = 0;
                this.hiLoCount = 0;
                this.omegaCount = 0;
                this.cardsSeen = 0;
                this.playerAdvantage = 0;
                this.optimalBetAmount = 200;
                
                this.updateProViewDisplay();
                this.showNotification('🔄 Card count reset to zero');
            }
            
            setOptimalBet() {
                this.betInput.value = this.optimalBetAmount;
                this.showNotification(`💡 Optimal bet set to ${this.optimalBetAmount} GA based on current count`);
            }
            
            exportGameData() {
                const gameData = {
                    timestamp: new Date().toISOString(),
                    session: {
                        handsPlayed: this.handsPlayed,
                        handsWon: this.handsWon,
                        currentBalance: this.balance,
                        sessionDuration: Date.now() - this.sessionStartTime
                    },
                    cardCounting: {
                        runningCount: this.runningCount,
                        trueCount: this.trueCount,
                        hiLoCount: this.hiLoCount,
                        omegaCount: this.omegaCount,
                        cardsSeen: this.cardsSeen,
                        playerAdvantage: this.playerAdvantage
                    },
                    results: this.resultHistory,
                    actions: this.actionHistory,
                    difficulty: this.difficulty
                };
                
                const dataStr = JSON.stringify(gameData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = `blackjack-session-${Date.now()}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.showNotification('📊 Game data exported successfully');
            }
            
            // Enhanced notification system
            showNotification(message) {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 120px;
                    right: 20px;
                    background: linear-gradient(45deg, #FFD700, #FFA500);
                    color: #333;
                    padding: 1rem;
                    border-radius: 8px;
                    z-index: 10000;
                    font-weight: bold;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    animation: slideIn 0.3s ease;
                    max-width: 350px;
                    white-space: pre-line;
                    font-size: 0.9rem;
                `;
                notification.textContent = message;
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.style.animation = 'slideIn 0.3s ease reverse';
                    setTimeout(() => notification.remove(), 300);
                }, message.length > 100 ? 6000 : 4000);
            }
        }

        // DOM-ready functions
        document.addEventListener('DOMContentLoaded', function() {
            // Override the original BlackjackGame with enhanced version
            if (window.blackjackGame) {
                // Preserve any existing state if needed
                const existingBalance = window.blackjackGame.balance;
                const existingStats = {
                    handsPlayed: window.blackjackGame.handsPlayed,
                    handsWon: window.blackjackGame.handsWon
                };
                
                // Create enhanced game
                window.blackjackGame = new EnhancedBlackjackGame();
                
                // Restore state if needed
                if (existingBalance !== 10000) {
                    window.blackjackGame.balance = existingBalance;
                }
                window.blackjackGame.handsPlayed = existingStats.handsPlayed;
                window.blackjackGame.handsWon = existingStats.handsWon;
                window.blackjackGame.updateDisplay();
            } else {
                window.blackjackGame = new EnhancedBlackjackGame();
            }
            
            // Global functions for HTML onclick attributes
            window.dealNewHand = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.dealNewHand();
                }
            };
            
            window.playerHit = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.playerHit();
                }
            };
            
            window.playerStand = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.playerStand();
                }
            };
            
            window.playerDouble = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.playerDouble();
                }
            };
            
            window.playerSplit = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.playerSplit();
                }
            };
            
            window.playerSurrender = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.playerSurrender();
                }
            };
            
            window.closeResult = function() {
                if (window.blackjackGame) {
                    window.blackjackGame.closeResult();
                }
            };
        });
    </script>
</body>
</html>