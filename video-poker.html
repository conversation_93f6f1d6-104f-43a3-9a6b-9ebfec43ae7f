<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Jacks or Better Pro - Provably Fair Video Poker</title>
    <link rel="stylesheet" href="video-poker.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="assets/diamond_favicon.svg" type="image/svg+xml">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span class="back-text">Back to Games</span>
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">Jacks or Better Pro</h1>
                <p class="game-subtitle">Provably Fair Video Poker</p>
            </div>
            <div class="header-right">
                <div class="view-toggle">
                    <button id="standardViewBtn" class="view-btn active">Standard</button>
                    <button id="rushProViewBtn" class="view-btn">Rush Pro</button>
                </div>
                <button id="rulesBtn" class="info-btn">
                    <i class="fas fa-book"></i>
                    <span class="btn-text">Rules</span>
                </button>
                <button id="verifyBtn" class="info-btn">
                    <i class="fas fa-shield-alt"></i>
                    <span class="btn-text">Verify</span>
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- Game Section -->
            <div class="game-section">
                <!-- Rush Pro Stats Bar (visible only in Rush Pro view) -->
                <div class="rush-stats-bar" id="rushStatsBar">
                    <div class="rush-stat">
                        <span class="rush-stat-label">Win Rate:</span>
                        <span class="rush-stat-value" id="rushWinRate">0%</span>
                    </div>
                    <div class="rush-stat">
                        <span class="rush-stat-label">ROI:</span>
                        <span class="rush-stat-value" id="rushROI">0%</span>
                    </div>
                    <div class="rush-stat">
                        <span class="rush-stat-label">Best Hand:</span>
                        <span class="rush-stat-value" id="rushBestHand">None</span>
                    </div>
                    <div class="rush-stat">
                        <span class="rush-stat-label">Session:</span>
                        <span class="rush-stat-value" id="rushSessionTime">00:00</span>
                    </div>
                    <div class="rush-stat">
                        <span class="rush-stat-label">Hands/Min:</span>
                        <span class="rush-stat-value" id="rushHandsPerMin">0</span>
                    </div>
                </div>

                <!-- GA Currency Display -->
                <div class="credits-section">
                    <div class="credits-display">
                        <div class="credits-item">
                            <span class="label">GA Balance:</span>
                            <span class="value" id="credits">1000</span>
                        </div>
                        <div class="credits-item">
                            <span class="label">Current Bet:</span>
                            <span class="value" id="currentBet">0</span>
                        </div>
                        <div class="credits-item">
                            <span class="label">Last Win:</span>
                            <span class="value" id="lastWin">0</span>
                        </div>
                    </div>
                    <div class="bet-controls">
                        <button id="betOneBtn" class="bet-btn">
                            <i class="fas fa-plus"></i>
                            <span class="bet-btn-text">Bet +</span>
                        </button>
                        <button id="betMaxBtn" class="bet-btn">
                            <i class="fas fa-rocket"></i>
                            <span class="bet-btn-text">Max Bet</span>
                        </button>
                        <button id="quickBetBtn" class="bet-btn rush-only">
                            <i class="fas fa-bolt"></i>
                            <span class="bet-btn-text">Quick Bet</span>
                        </button>
                    </div>
                </div>

                <!-- Mobile Paytable Toggle -->
                <div class="mobile-paytable-toggle">
                    <button id="mobilePaytableToggleBtn" class="mobile-toggle-btn">
                        <i class="fas fa-table"></i> Show Paytable
                    </button>
                </div>

                <!-- Paytable -->
                <div class="paytable" id="paytableSection">
                    <h3>Paytable - Jacks or Better</h3>
                    <div class="paytable-grid">
                        <div class="pay-header">
                            <span class="hand-name-header">Hand</span>
                            <span class="pay-header-item">1 GA</span>
                            <span class="pay-header-item">2 GA</span>
                            <span class="pay-header-item">3 GA</span>
                            <span class="pay-header-item">4 GA</span>
                            <span class="pay-header-item">5 GA</span>
                        </div>
                        <div class="pay-row" data-hand="royalFlush">
                            <span class="hand-name">Royal Flush</span>
                            <span class="pay-1">250</span>
                            <span class="pay-2">500</span>
                            <span class="pay-3">750</span>
                            <span class="pay-4">1000</span>
                            <span class="pay-5">4000</span>
                        </div>
                        <div class="pay-row" data-hand="straightFlush">
                            <span class="hand-name">Straight Flush</span>
                            <span class="pay-1">50</span>
                            <span class="pay-2">100</span>
                            <span class="pay-3">150</span>
                            <span class="pay-4">200</span>
                            <span class="pay-5">250</span>
                        </div>
                        <div class="pay-row" data-hand="fourOfAKind">
                            <span class="hand-name">Four of a Kind</span>
                            <span class="pay-1">25</span>
                            <span class="pay-2">50</span>
                            <span class="pay-3">75</span>
                            <span class="pay-4">100</span>
                            <span class="pay-5">125</span>
                        </div>
                        <div class="pay-row" data-hand="fullHouse">
                            <span class="hand-name">Full House</span>
                            <span class="pay-1">9</span>
                            <span class="pay-2">18</span>
                            <span class="pay-3">27</span>
                            <span class="pay-4">36</span>
                            <span class="pay-5">45</span>
                        </div>
                        <div class="pay-row" data-hand="flush">
                            <span class="hand-name">Flush</span>
                            <span class="pay-1">6</span>
                            <span class="pay-2">12</span>
                            <span class="pay-3">18</span>
                            <span class="pay-4">24</span>
                            <span class="pay-5">30</span>
                        </div>
                        <div class="pay-row" data-hand="straight">
                            <span class="hand-name">Straight</span>
                            <span class="pay-1">4</span>
                            <span class="pay-2">8</span>
                            <span class="pay-3">12</span>
                            <span class="pay-4">16</span>
                            <span class="pay-5">20</span>
                        </div>
                        <div class="pay-row" data-hand="threeOfAKind">
                            <span class="hand-name">Three of a Kind</span>
                            <span class="pay-1">3</span>
                            <span class="pay-2">6</span>
                            <span class="pay-3">9</span>
                            <span class="pay-4">12</span>
                            <span class="pay-5">15</span>
                        </div>
                        <div class="pay-row" data-hand="twoPair">
                            <span class="hand-name">Two Pair</span>
                            <span class="pay-1">2</span>
                            <span class="pay-2">4</span>
                            <span class="pay-3">6</span>
                            <span class="pay-4">8</span>
                            <span class="pay-5">10</span>
                        </div>
                        <div class="pay-row" data-hand="jacksOrBetter">
                            <span class="hand-name">Jacks or Better</span>
                            <span class="pay-1">1</span>
                            <span class="pay-2">2</span>
                            <span class="pay-3">3</span>
                            <span class="pay-4">4</span>
                            <span class="pay-5">5</span>
                        </div>
                    </div>
                </div>

                <!-- Cards Display -->
                <div class="cards-section">
                    <div class="cards-container">
                        <div class="card-position" data-position="0">
                            <div class="card" id="card0"></div>
                            <button class="hold-btn" id="hold0">
                                <i class="fas fa-hand-paper"></i>
                                <span class="hold-text">HOLD</span>
                            </button>
                        </div>
                        <div class="card-position" data-position="1">
                            <div class="card" id="card1"></div>
                            <button class="hold-btn" id="hold1">
                                <i class="fas fa-hand-paper"></i>
                                <span class="hold-text">HOLD</span>
                            </button>
                        </div>
                        <div class="card-position" data-position="2">
                            <div class="card" id="card2"></div>
                            <button class="hold-btn" id="hold2">
                                <i class="fas fa-hand-paper"></i>
                                <span class="hold-text">HOLD</span>
                            </button>
                        </div>
                        <div class="card-position" data-position="3">
                            <div class="card" id="card3"></div>
                            <button class="hold-btn" id="hold3">
                                <i class="fas fa-hand-paper"></i>
                                <span class="hold-text">HOLD</span>
                            </button>
                        </div>
                        <div class="card-position" data-position="4">
                            <div class="card" id="card4"></div>
                            <button class="hold-btn" id="hold4">
                                <i class="fas fa-hand-paper"></i>
                                <span class="hold-text">HOLD</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="hand-result" id="handResult">
                        <span class="result-text"></span>
                    </div>
                </div>

                <!-- Game Controls -->
                <div class="game-controls">
                    <button id="dealBtn" class="main-btn">
                        <i class="fas fa-play"></i>
                        <span class="btn-text">DEAL</span>
                    </button>
                    <button id="drawBtn" class="main-btn hidden">
                        <i class="fas fa-sync"></i>
                        <span class="btn-text">DRAW</span>
                    </button>
                    <button id="autoPlayBtn" class="main-btn rush-only">
                        <i class="fas fa-forward"></i>
                        <span class="btn-text">AUTO PLAY</span>
                    </button>
                </div>

                <!-- Rush Pro Quick Actions (visible only in Rush Pro view) -->
                <div class="rush-quick-actions" id="rushQuickActions">
                    <div class="quick-action-grid">
                        <button class="quick-action-btn" id="quickDealBtn">
                            <i class="fas fa-bolt"></i>
                            <span>Quick Deal</span>
                        </button>
                        <button class="quick-action-btn" id="maxSpeedBtn">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Max Speed</span>
                        </button>
                        <button class="quick-action-btn" id="optimalHoldBtn">
                            <i class="fas fa-brain"></i>
                            <span>Optimal Hold</span>
                        </button>
                        <button class="quick-action-btn" id="turboModeBtn">
                            <i class="fas fa-rocket"></i>
                            <span>Turbo Mode</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Information Panel -->
            <div class="info-panel">
                <!-- Mobile Info Toggle (visible only on mobile) -->
                <div class="mobile-info-toggle">
                    <button id="mobileInfoToggleBtn" class="mobile-toggle-btn">
                        <i class="fas fa-chart-line"></i> Show Statistics
                    </button>
                </div>

                <!-- Rush Pro Analytics (visible only in Rush Pro view) -->
                <div class="info-section rush-analytics-section" id="rushAnalyticsSection">
                    <h3>Rush Analytics</h3>
                    <div class="rush-analytics-content">
                        <div class="analytics-grid">
                            <div class="analytic-item">
                                <span class="analytic-label">Session Profit:</span>
                                <span class="analytic-value" id="sessionProfit">+0 GA</span>
                            </div>
                            <div class="analytic-item">
                                <span class="analytic-label">Hand Speed:</span>
                                <span class="analytic-value" id="handSpeed">0/min</span>
                            </div>
                            <div class="analytic-item">
                                <span class="analytic-label">Optimal Plays:</span>
                                <span class="analytic-value" id="optimalPlays">0%</span>
                            </div>
                            <div class="analytic-item">
                                <span class="analytic-label">Efficiency:</span>
                                <span class="analytic-value" id="playEfficiency">0%</span>
                            </div>
                            <div class="analytic-item">
                                <span class="analytic-label">Expected Value:</span>
                                <span class="analytic-value" id="expectedValue">+0 GA</span>
                            </div>
                            <div class="analytic-item">
                                <span class="analytic-label">Variance:</span>
                                <span class="analytic-value" id="sessionVariance">0.00</span>
                            </div>
                        </div>
                        <div class="rush-chart-container">
                            <canvas id="rushChart" width="300" height="150"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Provably Fair Section -->
                <div class="info-section fair-section">
                    <h3>Provably Fair</h3>
                    <div class="fair-info">
                        <div class="seed-display">
                            <div class="seed-item">
                                <span class="seed-label">Server Seed Hash:</span>
                                <span class="seed-value" id="serverSeedHash">...</span>
                            </div>
                            <div class="seed-item">
                                <span class="seed-label">Client Seed:</span>
                                <span class="seed-value" id="clientSeed">
                                    <input type="text" id="clientSeedInput" maxlength="32" placeholder="Enter your seed">
                                </span>
                            </div>
                            <div class="seed-item">
                                <span class="seed-label">Round ID:</span>
                                <span class="seed-value" id="roundId">0</span>
                            </div>
                        </div>
                        <button id="newSeedBtn" class="seed-btn">Generate New Client Seed</button>
                    </div>
                </div>

                <!-- Game History -->
                <div class="info-section history-section">
                    <h3>Hand History</h3>
                    <div class="history-list" id="historyList">
                        <p class="no-history">No hands played yet</p>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="info-section stats-section">
                    <h3>Session Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Hands Played:</span>
                            <span class="stat-value" id="handsPlayed">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Hands Won:</span>
                            <span class="stat-value" id="handsWon">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Win Rate:</span>
                            <span class="stat-value" id="winRate">0%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Won:</span>
                            <span class="stat-value" id="totalWon">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Best Hand:</span>
                            <span class="stat-value" id="bestHand">None</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">RTP:</span>
                            <span class="stat-value" id="rtp">99.54%</span>
                        </div>
                    </div>
                </div>

                <!-- Rush Pro Strategy Guide (visible only in Rush Pro view) -->
                <div class="info-section rush-strategy-section" id="rushStrategySection">
                    <h3>Strategy Guide</h3>
                    <div class="strategy-content">
                        <div class="strategy-tips">
                            <div class="strategy-tip" id="currentTip">
                                <i class="fas fa-lightbulb"></i>
                                <span>Deal a hand to get strategy tips</span>
                            </div>
                        </div>
                        <div class="hand-strength-meter">
                            <label for="strengthMeter">Hand Strength:</label>
                            <div class="strength-bar">
                                <div class="strength-fill" id="strengthFill"></div>
                            </div>
                            <span class="strength-text" id="strengthText">Unknown</span>
                        </div>
                        <div class="optimal-play-suggestion" id="optimalPlaySuggestion">
                            <strong>Suggested Play:</strong> <span id="suggestedPlay">Deal first</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rules Modal -->
        <div class="modal-overlay hidden" id="rulesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Jacks or Better - Rules & Strategy</h2>
                    <button class="close-modal-btn" id="closeRulesBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="rules-section">
                        <h3>How to Play</h3>
                        <ol>
                            <li>Select your bet amount using the bet controls</li>
                            <li>Click "DEAL" to receive 5 cards</li>
                            <li>Click cards or "HOLD" buttons to keep cards you want</li>
                            <li>Click "DRAW" to replace non-held cards</li>
                            <li>Get paid based on your final poker hand</li>
                        </ol>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Winning Hands (Lowest to Highest)</h3>
                        <ul>
                            <li><strong>Jacks or Better:</strong> Pair of Jacks, Queens, Kings, or Aces</li>
                            <li><strong>Two Pair:</strong> Two pairs of different ranks</li>
                            <li><strong>Three of a Kind:</strong> Three cards of the same rank</li>
                            <li><strong>Straight:</strong> Five consecutive cards (A-2-3-4-5 or 10-J-Q-K-A)</li>
                            <li><strong>Flush:</strong> Five cards of the same suit</li>
                            <li><strong>Full House:</strong> Three of a kind plus a pair</li>
                            <li><strong>Four of a Kind:</strong> Four cards of the same rank</li>
                            <li><strong>Straight Flush:</strong> Straight in the same suit</li>
                            <li><strong>Royal Flush:</strong> 10-J-Q-K-A in the same suit</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Basic Strategy Tips</h3>
                        <ul>
                            <li>Always play maximum coins for the Royal Flush bonus</li>
                            <li>Never break a pat hand (straight, flush, full house, etc.)</li>
                            <li>Keep any pair of Jacks or better</li>
                            <li>Keep any four cards to a straight flush</li>
                            <li>Keep any three cards to a royal flush</li>
                            <li>Keep any four cards to a flush</li>
                            <li>Keep low pairs over drawing to straights</li>
                            <li>Keep any four cards to an outside straight</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Rush Pro Mode Features</h3>
                        <ul>
                            <li><strong>Quick Actions:</strong> Faster game controls for experienced players</li>
                            <li><strong>Strategy Suggestions:</strong> Real-time optimal play recommendations</li>
                            <li><strong>Advanced Analytics:</strong> Detailed session statistics and performance metrics</li>
                            <li><strong>Auto Play:</strong> Automated gameplay with customizable settings</li>
                            <li><strong>Turbo Mode:</strong> Ultra-fast gameplay for maximum hands per minute</li>
                        </ul>
                    </div>
                    
                    <div class="rules-section">
                        <h3>Theoretical Return to Player (RTP)</h3>
                        <p>Jacks or Better with optimal play has an RTP of 99.54%, making it one of the best casino games for players who use proper strategy.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verify Hand Modal -->
        <div class="modal-overlay hidden" id="verifyModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Verify Hand - Provably Fair</h2>
                    <button class="close-modal-btn" id="closeVerifyBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="verify-section">
                        <h3>How Provably Fair Works</h3>
                        <p>Each hand uses a provably fair system to ensure complete transparency:</p>
                        <ol>
                            <li>Server generates a random seed and shows you its hash before dealing</li>
                            <li>You provide a client seed (or use the generated one)</li>
                            <li>These seeds plus the round ID determine the entire deck order</li>
                            <li>After the hand, the server reveals its seed for verification</li>
                        </ol>
                    </div>
                    
                    <div class="verify-section">
                        <h3>Verify a Previous Hand</h3>
                        <div class="verify-inputs">
                            <div class="input-group">
                                <label for="verifyServerSeed">Server Seed:</label>
                                <input type="text" id="verifyServerSeed" placeholder="Revealed server seed">
                            </div>
                            <div class="input-group">
                                <label for="verifyClientSeed">Client Seed:</label>
                                <input type="text" id="verifyClientSeed" placeholder="Your client seed">
                            </div>
                            <div class="input-group">
                                <label for="verifyRoundId">Round ID:</label>
                                <input type="number" id="verifyRoundId" placeholder="Round number">
                            </div>
                        </div>
                        <button id="verifyHandBtn" class="verify-btn">Verify Hand</button>
                        <div class="verify-result" id="verifyResult"></div>
                    </div>
                    
                    <div class="verify-section">
                        <h3>Current Hand Information</h3>
                        <div class="current-hand-info" id="currentHandInfo">
                            <p>Deal a hand to see verification information</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="video-poker.js"></script>
</body>
</html>