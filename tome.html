<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Tome of Life - Mystical Symbol Revelation | GoldenAura Casino</title>
    <meta name="description" content="Play the most advanced mobile-optimized Tome of Life game with Pro analytics, provably fair gaming, and mystical symbol revelations. Unlock ancient secrets!">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="tome.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;900&family=Cormorant+Garamond:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#2a0845">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Tome of Life">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="tome.js" as="script">
    <link rel="preload" href="tome.css" as="style">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Main Game Container -->
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Games
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">📜 Tome of Life</h1>
                <p class="game-subtitle">🔮 Unlock ancient symbols to reveal the secrets of existence • Mobile Optimized • Pro Analytics</p>
            </div>
            <div class="header-right">
                <button id="rulesBtn" class="info-btn">
                    <i class="fas fa-question-circle"></i>
                    How to Play
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Section (Book Display) -->
            <div class="book-section">
                <div class="tome-container">
                    <!-- Background Elements -->
                    <div class="tome-background">
                        <div class="candlelight left" aria-hidden="true"></div>
                        <div class="candlelight right" aria-hidden="true"></div>
                    </div>
                    
                    <!-- The Tome -->
                    <div class="tome" id="tome" role="main" aria-label="Ancient Tome of Life">
                        <div class="tome-cover">
                            <div class="tome-title">The Tome of Life</div>
                            <div class="tome-ornament" aria-hidden="true"></div>
                        </div>
                        
                        <div class="tome-pages">
                            <div class="page left-page">
                                <div class="page-content">
                                    <!-- 3x3 Grid for Symbols -->
                                    <div class="symbol-grid" id="symbolGrid" role="grid" aria-label="Mystical symbol revelation grid">
                                        <div class="symbol-cell" data-index="0" role="gridcell" aria-label="Symbol position 1">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="1" role="gridcell" aria-label="Symbol position 2">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="2" role="gridcell" aria-label="Symbol position 3">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="3" role="gridcell" aria-label="Symbol position 4">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="4" role="gridcell" aria-label="Symbol position 5">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="5" role="gridcell" aria-label="Symbol position 6">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="6" role="gridcell" aria-label="Symbol position 7">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="7" role="gridcell" aria-label="Symbol position 8">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                        <div class="symbol-cell" data-index="8" role="gridcell" aria-label="Symbol position 9">
                                            <div class="symbol-inner" aria-live="polite"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="page right-page">
                                <div class="page-content">
                                    <div class="outcome-display" id="outcomeDisplay" role="status" aria-live="assertive">
                                        <div class="win-message hidden" id="winMessage">
                                            <h3>✨ Divine Alignment!</h3>
                                            <div class="win-amount" id="winAmount">0 GA</div>
                                            <div class="win-multiplier" id="winMultiplier">x1</div>
                                        </div>
                                        <div class="lose-message hidden" id="loseMessage">
                                            <h3>🌀 The Forces Dissipate</h3>
                                            <p>The symbols have not aligned in your favor this time.</p>
                                        </div>
                                    </div>
                                    
                                    <div class="paylines-display" id="paylinesDisplay" aria-hidden="true">
                                        <!-- Paylines will be highlighted here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Win Lines Overlay -->
                    <div class="win-lines" id="winLines" aria-hidden="true">
                        <!-- Win lines will be drawn here -->
                    </div>
                    
                    <!-- Controls -->
                    <div class="controls">
                        <div class="stake-control">
                            <label for="stakeAmount">
                                <i class="fas fa-coins"></i> Offering (GA):
                            </label>
                            <div class="stake-input-group">
                                <input type="number" id="stakeAmount" min="10" max="10000" value="100" 
                                       class="stake-input" aria-describedby="stakeHelp">
                                <div class="stake-buttons">
                                    <button id="halfStakeBtn" class="stake-btn" aria-label="Half offering">½</button>
                                    <button id="doubleStakeBtn" class="stake-btn" aria-label="Double offering">2×</button>
                                    <button id="maxStakeBtn" class="stake-btn" aria-label="Maximum offering">MAX</button>
                                </div>
                            </div>
                            <div id="stakeHelp" class="sr-only">Enter your offering amount in GA currency</div>
                        </div>
                        
                        <button id="revealBtn" class="reveal-btn" aria-describedby="revealHelp">
                            <span class="btn-text">Reveal Symbols</span>
                            <span class="btn-icon">✨</span>
                        </button>
                        <div id="revealHelp" class="sr-only">Click to reveal the mystical symbols and discover your fate</div>
                    </div>
                </div>
            </div>
            
            <!-- Right Section (Game Info) -->
            <div class="info-section">
                <!-- Player Stats -->
                <div class="panel stats-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-user"></i> Your Stats
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">GA Balance</span>
                            <span class="stat-value" id="balanceValue">10000 GA</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Current Stake</span>
                            <span class="stat-value" id="currentStakeValue">0 GA</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Last Win</span>
                            <span class="stat-value" id="lastWinValue">0 GA</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Revelations</span>
                            <span class="stat-value" id="totalPlaysValue">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- Provably Fair -->
                <div class="panel provably-fair-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-shield-alt"></i> Provably Fair
                    </h3>
                    <div class="fair-info">
                        <div class="seed-row">
                            <label>🔒 Server Seed Hash:</label>
                            <span class="seed-value" id="serverSeedHash">-</span>
                        </div>
                        <div class="seed-row">
                            <label>🎲 Client Seed:</label>
                            <div class="seed-input-group">
                                <input type="text" id="clientSeedInput" class="seed-input" maxlength="32" 
                                       aria-label="Your client seed for randomness" autocomplete="off">
                                <button id="randomizeSeedBtn" class="seed-btn" aria-label="Generate new random seed">🎲</button>
                            </div>
                        </div>
                        <div class="seed-row">
                            <label>🔢 Nonce:</label>
                            <span class="seed-value" id="nonceValue">0</span>
                        </div>
                        <button id="verifyBtn" class="verify-btn">
                            <i class="fas fa-check-circle"></i> Verify Revelation
                        </button>
                    </div>
                </div>
                
                <!-- Payout Table -->
                <div class="panel payout-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-table"></i> Symbol Combinations
                    </h3>
                    <div class="payout-table" id="payoutTable">
                        <div class="payout-row">
                            <div class="symbol-display sun">☀️</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x2</div>
                            </div>
                        </div>
                        <div class="payout-row">
                            <div class="symbol-display moon">🌙</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x2</div>
                            </div>
                        </div>
                        <div class="payout-row">
                            <div class="symbol-display star">⭐</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x3</div>
                            </div>
                        </div>
                        <div class="payout-row">
                            <div class="symbol-display heart">❤️</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x3</div>
                            </div>
                        </div>
                        <div class="payout-row">
                            <div class="symbol-display tree">🌳</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x5</div>
                            </div>
                        </div>
                        <div class="payout-row">
                            <div class="symbol-display phoenix">🔥</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x10</div>
                            </div>
                        </div>
                        <div class="payout-row">
                            <div class="symbol-display eye">👁️</div>
                            <div class="payout-info">
                                <div class="combo">3 in a row</div>
                                <div class="multiplier">x15</div>
                            </div>
                        </div>
                        <div class="payout-row negative">
                            <div class="symbol-display skull">💀</div>
                            <div class="payout-info">
                                <div class="combo">Any position</div>
                                <div class="multiplier">Lose</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Game History -->
                <div class="panel history-panel">
                    <h3 class="panel-title">
                        <i class="fas fa-history"></i> Recent Revelations
                    </h3>
                    <div class="history-list" id="historyList" role="log" aria-label="Recent game results">
                        <!-- History items will be added here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- How To Play Modal -->
        <div class="modal-overlay hidden" id="rulesModal" role="dialog" aria-labelledby="rulesModalTitle" aria-modal="true">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="rulesModalTitle">📜 How to Play Tome of Life</h2>
                    <button class="close-modal-btn" id="closeRulesBtn" aria-label="Close modal">×</button>
                </div>
                <div class="modal-body">
                    <h3>🎯 Game Overview</h3>
                    <p>The Tome of Life is an ancient book containing the cosmic forces that shape existence. By revealing its symbols, you can unlock great mystical powers and rewards.</p>
                    
                    <h3>🎮 How to Play</h3>
                    <ol>
                        <li>Set your offering amount (10-10000 GA)</li>
                        <li>Click "Reveal Symbols" to turn the page</li>
                        <li>The tome will reveal a 3×3 grid of mystical symbols</li>
                        <li>Matching symbols in specific patterns will yield rewards</li>
                        <li>Beware the Skull (💀) - it causes an immediate loss</li>
                    </ol>
                    
                    <h3>📱 Mobile Controls</h3>
                    <ul>
                        <li><strong>Tap "Reveal Symbols"</strong> to start each round</li>
                        <li><strong>Long press the tome</strong> for quick reveal (when ready)</li>
                        <li><strong>Use Pro view toggle</strong> for advanced analytics and patterns</li>
                        <li><strong>Haptic feedback</strong> provides mystical vibrations on supported devices</li>
                    </ul>
                    
                    <h3>✨ Symbol Combinations</h3>
                    <ul>
                        <li>3 matching symbols in a row (horizontal, vertical, or diagonal) yield rewards</li>
                        <li>Different symbols have different values and rarity levels</li>
                        <li>The Eye (👁️) symbol offers the highest reward (x15)</li>
                        <li>Common symbols (☀️🌙) are more frequent but offer lower rewards</li>
                        <li>Legendary symbols (👁️🔥) are rare but extremely valuable</li>
                    </ul>
                    
                    <h3>📊 Pro View Features</h3>
                    <ul>
                        <li><strong>Mystical Analytics:</strong> Track win rates and average multipliers</li>
                        <li><strong>Symbol Frequency:</strong> Monitor which symbols appear most often</li>
                        <li><strong>Win Pattern Analysis:</strong> See which line patterns win most</li>
                        <li><strong>Session Tracking:</strong> Monitor profits and peak balance</li>
                        <li><strong>Favorite Symbol:</strong> Discover your most successful symbol</li>
                    </ul>
                    
                    <h3>🔒 Provably Fair System</h3>
                    <p>Every revelation is determined using a provably fair system:</p>
                    <ul>
                        <li><strong>Server Seed:</strong> Generated for each round and hashed before play</li>
                        <li><strong>Client Seed:</strong> Your customizable input for additional randomness</li>
                        <li><strong>Nonce:</strong> Incremental counter ensuring each round is unique</li>
                        <li><strong>Verification:</strong> After each round, verify the results were predetermined</li>
                    </ul>
                    
                    <h3>🎯 Strategy Tips</h3>
                    <ul>
                        <li><strong>Conservative Play:</strong> Use smaller offerings to extend gameplay</li>
                        <li><strong>Risk Management:</strong> Monitor your balance and adjust offering sizes</li>
                        <li><strong>Pattern Analysis:</strong> Use Pro view to identify favorable trends</li>
                        <li><strong>Symbol Awareness:</strong> Remember that any skull ends the round</li>
                    </ul>
                    
                    <h3>⌨️ Keyboard Shortcuts</h3>
                    <ul>
                        <li><strong>Space:</strong> Reveal symbols (when ready)</li>
                        <li><strong>P:</strong> Toggle between Standard and Pro view modes</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Verification Modal -->
        <div class="modal-overlay hidden" id="verificationModal" role="dialog" aria-labelledby="verificationModalTitle" aria-modal="true">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="verificationModalTitle">🔍 Verify Revelation</h2>
                    <button class="close-modal-btn" id="closeVerifyBtn" aria-label="Close modal">×</button>
                </div>
                <div class="modal-body">
                    <h3>✅ Verify Your Game Results</h3>
                    <p>Enter the revealed server seed, your client seed, and the nonce to verify that your game result was fair and predetermined.</p>
                    
                    <div class="verification-form">
                        <div class="form-group">
                            <label for="verifyServerSeed">🔓 Server Seed:</label>
                            <input type="text" id="verifyServerSeed" class="verify-input" 
                                   aria-describedby="serverSeedHelp" autocomplete="off">
                            <div id="serverSeedHelp" class="sr-only">Enter the revealed server seed from your game round</div>
                        </div>
                        <div class="form-group">
                            <label for="verifyClientSeed">🎲 Client Seed:</label>
                            <input type="text" id="verifyClientSeed" class="verify-input" 
                                   aria-describedby="clientSeedHelp" autocomplete="off">
                            <div id="clientSeedHelp" class="sr-only">Enter your client seed that was used</div>
                        </div>
                        <div class="form-group">
                            <label for="verifyNonce">🔢 Nonce:</label>
                            <input type="number" id="verifyNonce" class="verify-input" 
                                   aria-describedby="nonceHelp" autocomplete="off">
                            <div id="nonceHelp" class="sr-only">Enter the nonce number from your game round</div>
                        </div>
                        
                        <button id="runVerificationBtn" class="run-verify-btn">
                            <i class="fas fa-play"></i> Verify
                        </button>
                    </div>
                    
                    <div class="verification-result hidden" id="verificationResult">
                        <h3>📋 Verification Result</h3>
                        <div class="verification-grid" id="verificationGrid" role="grid" aria-label="Verification symbol grid">
                            <!-- Verification symbols will be shown here -->
                        </div>
                        <div class="verification-message" id="verificationMessage" role="status" aria-live="polite"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="tome.js"></script>

    <!-- Service Worker for PWA (Optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Prevent zoom on iOS Safari
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });
        
        // Optimize viewport for mobile
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        window.addEventListener('resize', setViewportHeight);
        setViewportHeight();
        
        // Disable context menu on long press for game elements
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.tome, .stake-btn, .reveal-btn, .symbol-cell')) {
                e.preventDefault();
            }
        });
        
        // Preload critical resources
        const criticalImages = [
            'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png'
        ];
        
        criticalImages.forEach(src => {
            const img = new Image();
            img.src = src;
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && perfData.loadEventEnd > 0) {
                    console.log('Page load time:', Math.round(perfData.loadEventEnd), 'ms');
                }
            }, 0);
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            console.log('Connection restored');
        });
        
        window.addEventListener('offline', function() {
            console.log('Connection lost - game will continue offline');
        });
        
        // Focus management for accessibility
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Orientation change handling
        window.addEventListener('orientationchange', function() {
            setTimeout(setViewportHeight, 100);
        });
        
        // Battery optimization
        let isVisible = true;
        document.addEventListener('visibilitychange', function() {
            isVisible = !document.hidden;
            if (window.tomeGame) {
                if (!isVisible) {
                    console.log('Page hidden - optimizing performance');
                } else {
                    console.log('Page visible - resuming normal operation');
                }
            }
        });
        
        // Prevent pull-to-refresh on mobile
        let startY = 0;
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].pageY;
        });
        
        document.addEventListener('touchmove', function(e) {
            if (e.touches[0].pageY > startY && window.pageYOffset === 0) {
                e.preventDefault();
            }
        }, { passive: false });
        
        // Enhance tome interaction for mobile
        document.addEventListener('DOMContentLoaded', function() {
            const tome = document.getElementById('tome');
            if (tome) {
                // Prevent tome from being draggable
                tome.style.touchAction = 'none';
                tome.style.userSelect = 'none';
                tome.style.webkitUserSelect = 'none';
                
                // Add mystical touch feedback
                tome.addEventListener('touchstart', function() {
                    tome.style.transform = 'scale(1.02)';
                    tome.style.boxShadow = '0 8px 25px rgba(255, 215, 0, 0.3)';
                });
                
                tome.addEventListener('touchend', function() {
                    tome.style.transform = 'scale(1)';
                    tome.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.5)';
                });
            }
            
            // Add mystical particle effects (optional)
            function createMysticalParticle() {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.backgroundColor = '#ffd700';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.opacity = '0.8';
                particle.style.left = Math.random() * window.innerWidth + 'px';
                particle.style.top = window.innerHeight + 'px';
                particle.style.animation = 'float-up 3s linear forwards';
                
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    particle.remove();
                }, 3000);
            }
            
            // Add CSS for particle animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes float-up {
                    to {
                        transform: translateY(-${window.innerHeight + 100}px);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
            
            // Create occasional mystical particles
            setInterval(createMysticalParticle, 2000);
        });
    </script>
</body>
</html>