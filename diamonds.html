<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Diamond Hunter - Provably Fair Mining Game | GoldenAura Casino</title>
    <meta name="description" content="Play the most advanced mobile-optimized Diamond Hunter game with Pro analytics, provably fair gaming, and full responsive design. Mine diamonds, avoid bombs!">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="diamonds.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#00bcd4">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Diamond Hunter">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="diamonds.js" as="script">
    <link rel="preload" href="diamonds.css" as="style">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Main Game Container -->
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <h1>💎 Diamond Hunter</h1>
            <p class="tagline">🎯 Mine the grid, find diamonds, avoid bombs • Mobile Optimized • Pro Analytics</p>
        </header>

        <!-- Game Setup Panel -->
        <div class="setup-panel" id="setupPanel">
            <div class="setup-section">
                <!-- Bomb Count Selection -->
                <label for="bombCount">
                    <i class="fas fa-bomb"></i> Number of Bombs:
                </label>
                <div class="bomb-selector">
                    <button id="bombDecrease" class="bomb-btn" aria-label="Decrease bombs">-</button>
                    <span id="bombDisplay" aria-live="polite">3</span>
                    <button id="bombIncrease" class="bomb-btn" aria-label="Increase bombs">+</button>
                </div>
                
                <!-- Probability Display -->
                <div class="probability-display">
                    <p>💎 Diamond probability (first click): <span id="diamondProb">88%</span></p>
                    <p>📈 Multiplier per diamond: <span id="multiplierPreview">1.35x</span></p>
                </div>
                
                <!-- Bet Controls -->
                <div class="bet-controls">
                    <label>
                        <i class="fas fa-coins"></i> Bet Amount (GA):
                    </label>
                    <div class="bet-amount-selector">
                        <button id="betDecrease" class="bomb-btn" aria-label="Decrease bet">-</button>
                        <span id="betDisplay" aria-live="polite">50</span>
                        <button id="betIncrease" class="bomb-btn" aria-label="Increase bet">+</button>
                    </div>
                </div>
                
                <!-- Balance Display -->
                <div class="balance-display">
                    <p>💰 Your Balance: <span id="balanceDisplay">10000 GA</span></p>
                </div>
            </div>

            <!-- Fairness Section -->
            <div class="fairness-section">
                <h3>
                    <i class="fas fa-shield-alt"></i> Provably Fair System
                </h3>
                <div class="seed-info">
                    <div class="seed-row">
                        <label>🔒 Server Seed (Hash):</label>
                        <span id="serverSeedHash" class="seed-value">-</span>
                    </div>
                    <div class="seed-row">
                        <label>🎲 Client Seed:</label>
                        <div class="seed-input-container">
                            <input type="text" id="clientSeed" placeholder="Enter your seed" maxlength="32" autocomplete="off">
                            <button id="randomizeSeed" aria-label="Generate random seed">🎲</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Start Game Button -->
            <button id="startGame" class="start-btn">
                <i class="fas fa-play"></i> Start Mining
            </button>
        </div>

        <!-- Game Panel -->
        <div class="game-panel hidden" id="gamePanel">
            <!-- Game Stats -->
            <div class="game-stats">
                <div class="stat-item">
                    <label><i class="fas fa-bomb"></i> Bombs</label>
                    <span id="currentBombs">3</span>
                </div>
                <div class="stat-item">
                    <label><i class="fas fa-gem"></i> Diamonds</label>
                    <span id="diamondsFound">0</span>
                </div>
                <div class="stat-item">
                    <label><i class="fas fa-chart-line"></i> Multiplier</label>
                    <span id="currentMultiplier">0.00x</span>
                </div>
                <div class="stat-item">
                    <label><i class="fas fa-coins"></i> Potential</label>
                    <span id="potentialWinnings">0.00 GA</span>
                </div>
            </div>

            <!-- Game Grid -->
            <div class="game-grid" id="gameGrid" role="grid" aria-label="Diamond mining grid">
                <!-- Grid squares will be generated by JavaScript -->
            </div>

            <!-- Game Controls -->
            <div class="game-controls">
                <button id="cashOut" class="cash-out-btn" disabled>
                    <i class="fas fa-hand-holding-usd"></i> Cash Out
                </button>
                <button id="newGame" class="new-game-btn">
                    <i class="fas fa-redo"></i> New Game
                </button>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="results-panel hidden" id="resultsPanel">
            <div class="result-content">
                <h2 id="resultTitle">Game Over</h2>
                <div class="result-stats">
                    <p>💎 Diamonds Found: <span id="finalDiamonds">0</span></p>
                    <p>📈 Final Multiplier: <span id="finalMultiplier">0.00x</span></p>
                    <p>💰 Result: <span id="finalResult">0.00 GA</span></p>
                </div>
                
                <!-- Verification Section -->
                <div class="verification-section">
                    <h3>🔍 Verify This Round</h3>
                    <div class="seed-reveal">
                        <label>🔓 Server Seed (Revealed):</label>
                        <span id="revealedServerSeed" class="seed-value">-</span>
                    </div>
                    <button id="verifyRound">
                        <i class="fas fa-check-circle"></i> Verify Round
                    </button>
                </div>
                
                <button id="playAgain" class="play-again-btn">
                    <i class="fas fa-play"></i> Play Again
                </button>
            </div>
        </div>

        <!-- How to Play Modal -->
        <div class="modal-overlay hidden" id="howToPlayModal" role="dialog" aria-labelledby="modalTitle" aria-modal="true">
            <div class="modal-content">
                <h2 id="modalTitle">💎 How to Play Diamond Hunter</h2>
                <div class="rules-content">
                    <h3>🎯 Game Objective</h3>
                    <p>Click grid squares to find diamonds while avoiding bombs. The more bombs you choose, the higher your potential rewards!</p>
                    
                    <h3>⚙️ Game Setup</h3>
                    <ul>
                        <li>Choose the number of bombs (1-24) before starting</li>
                        <li>More bombs = higher multiplier per diamond found</li>
                        <li>Fewer bombs = safer gameplay but lower rewards</li>
                        <li>Set your bet amount (minimum 10 GA)</li>
                    </ul>
                    
                    <h3>🎮 Gameplay</h3>
                    <ul>
                        <li>Click squares to reveal their contents</li>
                        <li>💎 <strong>Diamond:</strong> Increases your multiplier and potential winnings</li>
                        <li>💣 <strong>Bomb:</strong> Ends the game immediately and you lose your bet</li>
                        <li>🤑 <strong>Cash out anytime</strong> after finding at least one diamond</li>
                        <li>The more diamonds you find, the higher your multiplier grows</li>
                    </ul>
                    
                    <h3>📱 Mobile Controls</h3>
                    <ul>
                        <li>Tap any square to reveal it</li>
                        <li>Long press for encouragement (no gameplay effect)</li>
                        <li>Use the Pro view toggle for advanced analytics</li>
                        <li>Haptic feedback provides game state information</li>
                    </ul>
                    
                    <h3>🔒 Provably Fair System</h3>
                    <p>Every game uses cryptographic seeds to ensure complete fairness:</p>
                    <ul>
                        <li><strong>Server Seed:</strong> Generated before you see it, determines bomb positions</li>
                        <li><strong>Client Seed:</strong> Your input that adds randomness to the mix</li>
                        <li><strong>Verification:</strong> After each game, verify the round was fair</li>
                        <li><strong>Transparency:</strong> All bomb positions are predetermined and verifiable</li>
                    </ul>
                    
                    <h3>📊 Pro View Features</h3>
                    <ul>
                        <li><strong>Win Rate Tracking:</strong> Monitor your success over time</li>
                        <li><strong>Risk Analysis:</strong> Get recommendations based on your play style</li>
                        <li><strong>Session Statistics:</strong> Track profits, games played, and streaks</li>
                        <li><strong>Recent Results:</strong> Quick view of your last 8 games</li>
                    </ul>
                    
                    <h3>⌨️ Keyboard Shortcuts</h3>
                    <ul>
                        <li><strong>Space:</strong> Start new game (when in setup)</li>
                        <li><strong>C:</strong> Cash out (when playing)</li>
                        <li><strong>P:</strong> Toggle Pro view</li>
                    </ul>
                </div>
                <button id="closeModal" class="close-modal-btn">
                    <i class="fas fa-check"></i> Got It!
                </button>
            </div>
        </div>

        <!-- Game Info Footer -->
        <footer class="game-footer">
            <button id="showRules" class="info-btn">
                <i class="fas fa-question-circle"></i> How to Play
            </button>
            <button id="gameHistory" class="info-btn">
                <i class="fas fa-history"></i> Game History
            </button>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="assets/js/script.js"></script>
    <script src="diamonds.js"></script>

    <!-- Service Worker for PWA (Optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Prevent zoom on iOS Safari
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });
        
        // Optimize viewport for mobile
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        window.addEventListener('resize', setViewportHeight);
        setViewportHeight();
        
        // Disable context menu on long press for game elements
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.game-grid, .bomb-btn, .start-btn, .cash-out-btn')) {
                e.preventDefault();
            }
        });
        
        // Preload critical resources
        const criticalImages = [
            'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png'
        ];
        
        criticalImages.forEach(src => {
            const img = new Image();
            img.src = src;
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            setTimeout(function() {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && perfData.loadEventEnd > 0) {
                    console.log('Page load time:', Math.round(perfData.loadEventEnd), 'ms');
                }
            }, 0);
        });
        
        // Handle online/offline status
        window.addEventListener('online', function() {
            console.log('Connection restored');
        });
        
        window.addEventListener('offline', function() {
            console.log('Connection lost - game will continue offline');
        });
        
        // Focus management for accessibility
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });
        
        document.addEventListener('mousedown', function() {
            document.body.classList.remove('keyboard-navigation');
        });
        
        // Orientation change handling
        window.addEventListener('orientationchange', function() {
            setTimeout(setViewportHeight, 100);
        });
        
        // Battery optimization
        let isVisible = true;
        document.addEventListener('visibilitychange', function() {
            isVisible = !document.hidden;
            if (window.diamondHunter) {
                // Pause non-essential animations when page is hidden
                if (!isVisible) {
                    console.log('Page hidden - optimizing performance');
                } else {
                    console.log('Page visible - resuming normal operation');
                }
            }
        });
    </script>
</body>
</html>