/**
 * Configuration for Sports Betting System
 * 
 * This file contains configuration settings for the sports betting system.
 * Update the API settings and other configurations as needed.
 */
const SPORTS_BETTING_CONFIG = {
    // API Configuration
    api: {
        baseUrl: '/api',           // Base URL for the API endpoints
        
        // Cache settings
        cache: {
            time: 15 * 60 * 1000,     // 15 minutes in milliseconds
            requestInterval: 500       // 500 milliseconds between requests
        }
    },
    
    // User Configuration
    user: {
        initialBalance: 1000,          // Starting balance for new users
        storageKey: 'sports-betting-data', // Local storage key
    },
    
    // Sports Configuration
    sports: {
        default: 'football',       // Default sport to show
        available: [
            {
                id: 'football',
                name: 'Football',
                hasDrawOption: true
            },
            {
                id: 'basketball',
                name: 'Basketball',
                hasDrawOption: false
            },
            {
                id: 'tennis',
                name: 'Tennis',
                hasDrawOption: false
            },
            {
                id: 'baseball',
                name: 'Baseball',
                hasDrawOption: false
            },
            {
                id: 'soccer',
                name: 'Soccer',
                hasDrawOption: true
            }
        ]
    },
    
    // UI Configuration
    ui: {
        autoRefreshInterval: 5 * 60 * 1000, // 5 minutes in milliseconds
        containerSelector: '.sports-betting-container',
        dateFormat: {
            matches: {
                options: {
                    weekday: 'short',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }
            }
        }
    },
    
    // Demo Mode (use mock data instead of real API)
    // Should be set to false for production to use the real backend
    demoMode: true
};

// Export the configuration
window.SPORTS_BETTING_CONFIG = SPORTS_BETTING_CONFIG;