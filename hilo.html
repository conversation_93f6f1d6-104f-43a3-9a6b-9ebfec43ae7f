<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hilo - Higher or Lower Card Game</title>
    <link rel="stylesheet" href="hilo.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="assets/hilo-game/hilo-icon.png" type="image/png">
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <header class="game-header">
            <div class="header-left">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span class="back-text">Back to Games</span>
                </a>
            </div>
            <div class="header-center">
                <h1 class="game-title">Hilo</h1>
                <p class="game-subtitle">Test your intuition • Predict Higher or Lower • Build your streak</p>
            </div>
            <div class="header-right">
                <div class="view-mode-toggle">
                    <button id="standardViewBtn" class="view-btn active">
                        <i class="fas fa-mobile-alt"></i>
                        <span class="view-text">Standard</span>
                    </button>
                    <button id="proViewBtn" class="view-btn">
                        <i class="fas fa-chart-line"></i>
                        <span class="view-text">Pro</span>
                    </button>
                </div>
                <button id="tutorialBtn" class="info-btn">
                    <i class="fas fa-circle-info"></i>
                    <span class="btn-text">How to Play</span>
                </button>
                <button id="verifyBtn" class="verify-btn">
                    <i class="fas fa-shield-check"></i>
                    <span class="btn-text">Verify</span>
                </button>
            </div>
        </header>

        <div class="main-content">
            <!-- Mobile Status Bar -->
            <div class="mobile-status-bar">
                <div class="status-item">
                    <i class="fas fa-coins"></i>
                    <span id="mobileBalance">1000</span> GA
                </div>
                <div class="status-item">
                    <i class="fas fa-fire"></i>
                    <span id="mobileStreak">0</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-dice"></i>
                    <span id="mobileBet">0</span> GA
                </div>
                <div class="status-item multiplier">
                    <span id="mobileMultiplier">x1.0</span>
                </div>
            </div>

            <!-- Game Area -->
            <div class="game-section">
                <!-- Game Board -->
                <div class="game-board">
                    <div class="deck-area">
                        <div class="deck" id="deck">
                            <div class="card card-back">
                                <div class="card-back-design"></div>
                            </div>
                        </div>
                        <div class="deck-info" id="deckInfo">
                            <span id="cardsRemaining">52</span> cards remaining
                        </div>
                    </div>

                    <div class="round-display">
                        <div class="round-info">
                            <div class="round-number">Round <span id="roundNumber">1</span></div>
                            <div class="streak-counter">
                                <i class="fas fa-fire"></i> Streak: <span id="streakCounter">0</span>
                            </div>
                        </div>
                        <div class="ga-currency-display">
                            <div class="ga-balance">
                                <i class="fas fa-coins"></i> GA Balance: <span id="gaCurrencyBalance">1000</span>
                            </div>
                            <div class="ga-bet">
                                <i class="fas fa-dice"></i> GA Bet: <span id="gaBetAmount">0</span>
                            </div>
                        </div>
                    </div>

                    <div class="cards-area">
                        <div class="current-card-container">
                            <div class="current-card-label">Current Card</div>
                            <div class="card-slot" id="currentCardSlot">
                                <!-- Current card will be inserted here -->
                            </div>
                        </div>
                        
                        <div class="vs-indicator">
                            <span>VS</span>
                        </div>
                        
                        <div class="next-card-container">
                            <div class="next-card-label">Next Card</div>
                            <div class="card-slot" id="nextCardSlot">
                                <!-- Next card will be inserted here -->
                            </div>
                        </div>
                    </div>

                    <div class="prediction-display" id="predictionDisplay">
                        <div class="prediction-result hidden" id="predictionResult">
                            <div class="result-text" id="resultText">Correct!</div>
                            <div class="result-details" id="resultDetails">You predicted higher, and it was higher!</div>
                        </div>
                    </div>
                </div>
                
                <!-- Game Controls -->
                <div class="game-controls">
                    <div class="probability-displays">
                        <div class="probability-display higher">
                            <div class="probability-label">Higher</div>
                            <div class="probability-value">
                                <span class="probability-percent" id="higherProbability">-</span>
                                <span class="multiplier" id="higherMultiplier">-</span>
                            </div>
                        </div>
                        
                        <div class="probability-display lower">
                            <div class="probability-label">Lower</div>
                            <div class="probability-value">
                                <span class="probability-percent" id="lowerProbability">-</span>
                                <span class="multiplier" id="lowerMultiplier">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="action-buttons" id="actionButtons">
                        <button id="predictHigherBtn" class="action-btn higher-btn">
                            <i class="fas fa-arrow-up"></i>
                            <span class="btn-text">Higher</span>
                        </button>
                        
                        <button id="predictLowerBtn" class="action-btn lower-btn">
                            <i class="fas fa-arrow-down"></i>
                            <span class="btn-text">Lower</span>
                        </button>
                        
                        <button id="cashOutBtn" class="action-btn cashout-btn" disabled>
                            <i class="fas fa-check-circle"></i>
                            <span class="btn-text">Cash Out</span>
                            <span id="cashoutMultiplier" class="cashout-multiplier">x1.0</span>
                        </button>
                        
                        <button id="newGameBtn" class="action-btn new-game-btn hidden">
                            <i class="fas fa-redo"></i>
                            <span class="btn-text">New Game</span>
                        </button>
                    </div>
                    
                    <div class="ga-bet-controls">
                        <div class="ga-bet-input-container">
                            <input type="number" id="gaBetInput" min="50" max="5000" step="50" placeholder="Enter GA bet amount">
                            <div class="quick-bet-buttons">
                                <button class="quick-bet" data-amount="50">50</button>
                                <button class="quick-bet" data-amount="100">100</button>
                                <button class="quick-bet" data-amount="250">250</button>
                                <button class="quick-bet" data-amount="500">500</button>
                            </div>
                        </div>
                        <div class="ga-bet-buttons">
                            <button id="gaPlaceBetBtn" class="ga-bet-btn place-bet">
                                <i class="fas fa-plus-circle"></i>
                                <span class="btn-text">Place Bet</span>
                            </button>
                            <button id="gaWithdrawBtn" class="ga-bet-btn withdraw-bet">
                                <i class="fas fa-minus-circle"></i>
                                <span class="btn-text">Withdraw</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="total-multiplier">
                        <div class="multiplier-label">Total Multiplier</div>
                        <div class="multiplier-value" id="totalMultiplier">x1.0</div>
                    </div>
                </div>
                
                <!-- Pro View Analytics -->
                <div class="pro-analytics">
                    <div class="analytics-section">
                        <h3>Advanced Analytics</h3>
                        <div class="analytics-grid">
                            <div class="analytics-item">
                                <div class="analytics-label">Win Rate</div>
                                <div class="analytics-value" id="winRate">0%</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">Avg Multiplier</div>
                                <div class="analytics-value" id="avgMultiplier">x0.0</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">Total Profit</div>
                                <div class="analytics-value" id="totalProfit">0 GA</div>
                            </div>
                            <div class="analytics-item">
                                <div class="analytics-label">Risk Level</div>
                                <div class="analytics-value" id="riskLevel">Low</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-probability-chart">
                        <h3>Card Probability Analysis</h3>
                        <div class="chart-container">
                            <canvas id="probabilityChart" width="300" height="150"></canvas>
                        </div>
                        <div class="remaining-cards">
                            <h4>Remaining Cards by Value</h4>
                            <div id="remainingCardsGrid" class="remaining-cards-grid">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Game History -->
                <div class="game-history">
                    <h3>Game History <span id="historyRoundInfo">Round 1</span></h3>
                    <div class="history-entries" id="historyEntries">
                        <div class="history-entry">
                            <div class="history-message">Welcome to Hilo! Predict if the next card will be higher or lower than the current card.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Panel -->
            <div class="info-panel">
                <!-- Statistics Section -->
                <div class="info-section stats-section">
                    <h3>Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">Total Rounds</div>
                            <div class="stat-value" id="statTotalRounds">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Correct Predictions</div>
                            <div class="stat-value" id="statCorrectPredictions">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Highest Streak</div>
                            <div class="stat-value" id="statHighestStreak">0</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Highest Multiplier</div>
                            <div class="stat-value" id="statHighestMultiplier">x1.0</div>
                        </div>
                    </div>
                </div>
                
                <!-- Fairness Information -->
                <div class="info-section fairness-section">
                    <h3>Provably Fair System</h3>
                    <div class="fairness-info">
                        <p>All card draws are determined by a transparent provably fair system:</p>
                        <div class="seed-info">
                            <div class="seed-label">Server Seed Hash:</div>
                            <div class="seed-value hash" id="serverSeedHash">...</div>
                        </div>
                        <div class="seed-info">
                            <div class="seed-label">Client Seed:</div>
                            <div class="seed-value" id="clientSeed">...</div>
                        </div>
                        <div class="seed-info">
                            <div class="seed-label">Round ID:</div>
                            <div class="seed-value" id="roundId">...</div>
                        </div>
                        <p class="fairness-note">The server seed will be revealed after each round for verification.</p>
                    </div>
                </div>

                <!-- Card Values Reference -->
                <div class="info-section card-values-section">
                    <h3>Card Values</h3>
                    <div class="card-values">
                        <div class="card-value-row">
                            <div class="card-value-item">A<span class="mini-suit">♠</span></div>
                            <div class="value-number">14</div>
                        </div>
                        <div class="card-value-row">
                            <div class="card-value-item">K<span class="mini-suit">♠</span></div>
                            <div class="value-number">13</div>
                        </div>
                        <div class="card-value-row">
                            <div class="card-value-item">Q<span class="mini-suit">♠</span></div>
                            <div class="value-number">12</div>
                        </div>
                        <div class="card-value-row">
                            <div class="card-value-item">J<span class="mini-suit">♠</span></div>
                            <div class="value-number">11</div>
                        </div>
                        <div class="card-value-row">
                            <div class="card-value-item">10<span class="mini-suit">♠</span></div>
                            <div class="value-number">10</div>
                        </div>
                        <div class="card-value-row">
                            <div class="card-value-item small-cards">9-2<span class="mini-suit">♠</span></div>
                            <div class="value-number">9-2</div>
                        </div>
                    </div>
                    <div class="suits-note">All suits (♠ ♥ ♦ ♣) have the same values.</div>
                    <div class="tie-note">Note: If the next card has the same value, you lose the round.</div>
                </div>
            </div>
        </div>

        <!-- Tutorial Modal -->
        <div class="modal-overlay hidden" id="tutorialModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>How to Play Hilo</h2>
                    <button class="close-modal-btn" id="closeTutorialBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="tutorial-section">
                        <h3>Game Objective</h3>
                        <p>Test your intuition by predicting whether the next card will be Higher or Lower than the current card. Build a streak of correct predictions to increase your multiplier!</p>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Game Rules</h3>
                        <ul>
                            <li><strong>Card Values:</strong> 2 is the lowest card (2), Ace is the highest (14)</li>
                            <li><strong>Starting:</strong> The first card is dealt face-up as your "Current Card"</li>
                            <li><strong>Prediction:</strong> Choose "Higher" or "Lower" based on what you think the next card will be</li>
                            <li><strong>Outcome:</strong> If your prediction is correct, you can continue to build your streak or Cash Out</li>
                            <li><strong>Tie Cards:</strong> If the next card has the same value as your current card, you lose the round</li>
                            <li><strong>New Round:</strong> After a loss or Cash Out, a new round begins with a freshly shuffled deck</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Multipliers</h3>
                        <p>The multiplier for each prediction is calculated based on the probability of being correct:</p>
                        <ul>
                            <li>Higher probability predictions have lower multipliers</li>
                            <li>Lower probability predictions have higher multipliers</li>
                            <li>Each successful prediction compounds your total multiplier</li>
                            <li>You can Cash Out at any time to secure your current multiplier</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Pro View Features</h3>
                        <p>Enable Pro View for advanced features:</p>
                        <ul>
                            <li><strong>Advanced Analytics:</strong> Win rate, average multiplier, total profit tracking</li>
                            <li><strong>Card Probability Charts:</strong> Visual representation of odds</li>
                            <li><strong>Remaining Card Analysis:</strong> See exactly which cards are left in the deck</li>
                            <li><strong>Risk Assessment:</strong> Real-time risk level calculations</li>
                        </ul>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Provably Fair System</h3>
                        <p>Every card draw is determined through a transparent and verifiable system:</p>
                        <ol>
                            <li>Before each round, the server generates a random seed and shows you its cryptographic hash</li>
                            <li>Your client seed combines with the server seed to determine the deck shuffle</li>
                            <li>After a round ends, the server reveals its seed, allowing you to verify the result was fair</li>
                        </ol>
                    </div>
                    
                    <div class="tutorial-section">
                        <h3>Strategy Tips</h3>
                        <ul>
                            <li>For middle cards (7, 8, 9), the probabilities are more balanced</li>
                            <li>For extreme cards (2, 3 or Q, K, A), the probabilities heavily favor one direction</li>
                            <li>Consider the remaining cards in the deck when making your decision</li>
                            <li>Know when to Cash Out to secure your multiplier</li>
                            <li>Use Pro View to make more informed decisions with detailed analytics</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verification Modal -->
        <div class="modal-overlay hidden" id="verifyModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Verify Fairness</h2>
                    <button class="close-modal-btn" id="closeVerifyBtn">×</button>
                </div>
                <div class="modal-body">
                    <div class="verify-section">
                        <h3>How Our Provably Fair System Works</h3>
                        <p>The outcome of each card draw is predetermined before you make your prediction, yet is completely random and verifiable:</p>
                        
                        <ol>
                            <li>The server generates a random seed for each round</li>
                            <li>You're shown the SHA-256 hash of this seed before making your choice</li>
                            <li>Your client seed and the round ID combine with the server seed to generate the deck shuffle</li>
                            <li>After the round, the server reveals its seed so you can verify the outcome</li>
                        </ol>
                        
                        <div class="verification-tool">
                            <h3>Verify Past Games</h3>
                            <div class="verification-inputs">
                                <div class="input-group">
                                    <label for="verifyServerSeed">Server Seed:</label>
                                    <input type="text" id="verifyServerSeed" placeholder="Revealed server seed">
                                </div>
                                <div class="input-group">
                                    <label for="verifyClientSeed">Client Seed:</label>
                                    <input type="text" id="verifyClientSeed" placeholder="Your client seed">
                                </div>
                                <div class="input-group">
                                    <label for="verifyRoundId">Round ID:</label>
                                    <input type="text" id="verifyRoundId" placeholder="Round number">
                                </div>
                                <button class="verify-submit-btn" id="verifySubmitBtn">Verify Deck Shuffle</button>
                            </div>
                            <div class="verification-result" id="verificationResult">
                                <div class="result-label">Verification Result:</div>
                                <div class="result-content" id="verificationContent">Select a round to verify</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="verify-section">
                        <h3>Past Rounds</h3>
                        <div class="past-rounds" id="pastRounds">
                            <div class="no-rounds">Complete a round to see your game history</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Result Modal -->
        <div class="modal-overlay hidden" id="resultModal">
            <div class="result-animation">
                <div class="result-title" id="resultTitle">Correct!</div>
                <div class="result-image" id="resultImage"></div>
                <div class="result-message" id="resultMessage">
                    Great prediction! The next card was higher.
                    <div class="result-multiplier">Multiplier: <span id="resultMultiplier">x1.5</span></div>
                </div>
                <div class="result-actions">
                    <button class="continue-btn" id="resultContinueBtn">Continue</button>
                    <button class="cashout-btn" id="resultCashoutBtn">Cash Out</button>
                </div>
            </div>
        </div>
    </div>

    <script src="hilo.js"></script>
</body>
</html>