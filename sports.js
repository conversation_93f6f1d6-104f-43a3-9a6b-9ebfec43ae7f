class SportsBetting {
    constructor() {
        this.balance = 1000.00;
        this.selectedBets = [];
        this.betHistory = [];
        this.currentSport = 'football';
        
        // Mock data - in real app, this would come from API
        this.mockMatches = {
            football: [
                {
                    id: 1,
                    homeTeam: "Chiefs",
                    awayTeam: "Patriots",
                    time: "2024-01-15 20:00",
                    odds: { home: 1.85, draw: 3.20, away: 2.10 }
                },
                {
                    id: 2,
                    homeTeam: "Cowboys",
                    awayTeam: "Eagles",
                    time: "2024-01-16 19:30",
                    odds: { home: 2.05, draw: 3.15, away: 1.75 }
                }
            ],
            basketball: [
                {
                    id: 3,
                    homeTeam: "Lakers",
                    awayTeam: "Warriors",
                    time: "2024-01-15 21:00",
                    odds: { home: 1.95, away: 1.90 }
                }
            ],
            tennis: [
                {
                    id: 4,
                    homeTeam: "Djokovic",
                    awayTeam: "Nadal",
                    time: "2024-01-16 14:00",
                    odds: { home: 1.60, away: 2.30 }
                }
            ],
            soccer: [
                {
                    id: 5,
                    homeTeam: "Barcelona",
                    awayTeam: "Real Madrid",
                    time: "2024-01-17 16:00",
                    odds: { home: 2.20, draw: 3.00, away: 3.10 }
                }
            ]
        };
        
        this.init();
    }
    
    init() {
        this.updateBalance();
        this.loadMatches();
        this.setupEventListeners();
        this.loadBetHistory();
    }
    
    setupEventListeners() {
        // Sport tabs
        document.querySelectorAll('.sport-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                document.querySelectorAll('.sport-tab').forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
                this.currentSport = e.target.dataset.sport;
                this.loadMatches();
            });
        });
        
        // Bet amount input
        document.getElementById('bet-amount').addEventListener('input', () => {
            this.updatePotentialPayout();
            this.updatePlaceBetButton();
        });
        
        // Place bet button
        document.getElementById('place-bet').addEventListener('click', () => {
            this.placeBets();
        });
    }
    
    loadMatches() {
        const matchesContainer = document.getElementById('matches-list');
        const matches = this.mockMatches[this.currentSport] || [];
        
        matchesContainer.innerHTML = matches.map(match => this.createMatchCard(match)).join('');
        
        // Add event listeners to betting options
        document.querySelectorAll('.bet-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.selectBet(e.target);
            });
        });
    }
    
    createMatchCard(match) {
        const matchTime = new Date(match.time).toLocaleString();
        const hasDrawOption = match.odds.draw !== undefined;
        
        return `
            <div class="match-card">
                <div class="match-header">
                    <span class="match-time">${matchTime}</span>
                </div>
                <div class="teams">
                    <span class="team">${match.homeTeam}</span>
                    <span class="vs">VS</span>
                    <span class="team">${match.awayTeam}</span>
                </div>
                <div class="betting-options">
                    <div class="bet-option" 
                         data-match-id="${match.id}" 
                         data-bet-type="home" 
                         data-odds="${match.odds.home}">
                        ${match.homeTeam} Win<br>
                        <strong>${match.odds.home}</strong>
                    </div>
                    ${hasDrawOption ? `
                        <div class="bet-option" 
                             data-match-id="${match.id}" 
                             data-bet-type="draw" 
                             data-odds="${match.odds.draw}">
                            Draw<br>
                            <strong>${match.odds.draw}</strong>
                        </div>
                    ` : ''}
                    <div class="bet-option" 
                         data-match-id="${match.id}" 
                         data-bet-type="away" 
                         data-odds="${match.odds.away}">
                        ${match.awayTeam} Win<br>
                        <strong>${match.odds.away}</strong>
                    </div>
                </div>
            </div>
        `;
    }
    
    selectBet(optionElement) {
        const matchId = optionElement.dataset.matchId;
        const betType = optionElement.dataset.betType;
        const odds = parseFloat(optionElement.dataset.odds);
        
        // Toggle selection
        if (optionElement.classList.contains('selected')) {
            optionElement.classList.remove('selected');
            this.selectedBets = this.selectedBets.filter(bet => 
                !(bet.matchId == matchId && bet.betType === betType)
            );
        } else {
            optionElement.classList.add('selected');
            
            const match = this.findMatch(matchId);
            const betDescription = this.getBetDescription(match, betType);
            
            this.selectedBets.push({
                matchId: matchId,
                betType: betType,
                odds: odds,
                description: betDescription
            });
        }
        
        this.updateBetSlip();
        this.updatePotentialPayout();
        this.updatePlaceBetButton();
    }
    
    findMatch(matchId) {
        for (const sport in this.mockMatches) {
            const match = this.mockMatches[sport].find(m => m.id == matchId);
            if (match) return match;
        }
        return null;
    }
    
    getBetDescription(match, betType) {
        switch (betType) {
            case 'home':
                return `${match.homeTeam} to win vs ${match.awayTeam}`;
            case 'away':
                return `${match.awayTeam} to win vs ${match.homeTeam}`;
            case 'draw':
                return `${match.homeTeam} vs ${match.awayTeam} - Draw`;
            default:
                return 'Unknown bet';
        }
    }
    
    updateBetSlip() {
        const betSlipContainer = document.getElementById('selected-bets');
        
        if (this.selectedBets.length === 0) {
            betSlipContainer.innerHTML = '<p class="empty-slip">No bets selected</p>';
            return;
        }
        
        betSlipContainer.innerHTML = this.selectedBets.map(bet => `
            <div class="selected-bet">
                <div>${bet.description}</div>
                <div>Odds: <strong>${bet.odds}</strong></div>
            </div>
        `).join('');
    }
    
    updatePotentialPayout() {
        const betAmount = parseFloat(document.getElementById('bet-amount').value) || 0;
        const totalOdds = this.selectedBets.reduce((total, bet) => total * bet.odds, 1);
        const payout = betAmount * totalOdds;
        
        document.getElementById('payout-amount').textContent = payout.toFixed(2);
    }
    
    updatePlaceBetButton() {
        const button = document.getElementById('place-bet');
        const betAmount = parseFloat(document.getElementById('bet-amount').value) || 0;
        const hasSelectedBets = this.selectedBets.length > 0;
        const hasSufficientBalance = betAmount <= this.balance;
        
        button.disabled = !(hasSelectedBets && betAmount > 0 && hasSufficientBalance);
    }
    
    placeBets() {
        const betAmount = parseFloat(document.getElementById('bet-amount').value);
        
        if (betAmount > this.balance) {
            alert('Insufficient balance!');
            return;
        }
        
        // Create bet record
        const bet = {
            id: Date.now(),
            bets: [...this.selectedBets],
            amount: betAmount,
            potentialPayout: parseFloat(document.getElementById('payout-amount').textContent),
            timestamp: new Date(),
            status: 'pending'
        };
        
        // Deduct from balance
        this.balance -= betAmount;
        this.updateBalance();
        
        // Add to history
        this.betHistory.unshift(bet);
        this.loadBetHistory();
        
        // Clear selections
        this.clearSelections();
        
        // Simulate bet resolution after 10 seconds
        setTimeout(() => {
            this.resolveBet(bet.id);
        }, 10000);
        
        alert('Bet placed successfully!');
    }
    
    resolveBet(betId) {
        const bet = this.betHistory.find(b => b.id === betId);
        if (!bet || bet.status !== 'pending') return;
        
        // Simulate random outcome (30% win rate for demo)
        const won = Math.random() < 0.3;
        
        bet.status = won ? 'won' : 'lost';
        
        if (won) {
            this.balance += bet.potentialPayout;
            this.updateBalance();
        }
        
        this.loadBetHistory();
    }
    
    clearSelections() {
        this.selectedBets = [];
        document.querySelectorAll('.bet-option.selected').forEach(option => {
            option.classList.remove('selected');
        });
        document.getElementById('bet-amount').value = '';
        this.updateBetSlip();
        this.updatePotentialPayout();
        this.updatePlaceBetButton();
    }
    
    updateBalance() {
        document.getElementById('user-balance').textContent = this.balance.toFixed(2);
    }
    
    loadBetHistory() {
        const historyContainer = document.getElementById('bet-history-list');
        
        if (this.betHistory.length === 0) {
            historyContainer.innerHTML = '<p style="color: #ccc; text-align: center;">No bets yet</p>';
            return;
        }
        
        historyContainer.innerHTML = this.betHistory.slice(0, 5).map(bet => `
            <div class="bet-history-item">
                <div>
                    <div>${bet.bets.map(b => b.description).join(', ')}</div>
                    <div style="font-size: 0.9rem; color: #ccc;">
                        $${bet.amount} → $${bet.potentialPayout.toFixed(2)}
                    </div>
                </div>
                <span class="bet-status ${bet.status}">${bet.status.toUpperCase()}</span>
            </div>
        `).join('');
    }
}

// API Integration Notes
/*
FREE API OPTIONS:

1. The Odds API (https://the-odds-api.com/)
   - Free tier: 500 requests/month
   - Sports: NFL, NBA, MLB, NHL, soccer, MMA, etc.
   - Features: Pre-game odds, live odds, historical data
   - Implementation example:
     fetch(`https://api.the-odds-api.com/v4/sports/upcoming/odds/?apiKey=YOUR_API_KEY&regions=us&markets=h2h,spreads,totals`)
       .then(response => response.json())
       .then(data => console.log(data));

2. API-Football (https://www.api-football.com/)
   - Free tier: 100 requests/day
   - Sports: Soccer/football leagues worldwide
   - Features: Fixtures, live scores, standings, player stats, odds
   - Implementation example:
     fetch('https://v3.football.api-sports.io/odds?league=39&season=2023', {
       headers: {
         'x-rapidapi-host': 'v3.football.api-sports.io',
         'x-rapidapi-key': 'YOUR_API_KEY'
       }
     }).then(response => response.json())
       .then(data => console.log(data));

3. SportDataIO (https://sportsdata.io/)
   - Free tier: Limited access
   - Sports: NFL, NBA, MLB, NHL, soccer, etc.
   - Features: Scores, schedules, player stats, odds
   - Implementation example:
     fetch('https://api.sportsdata.io/v3/nfl/odds/json/LiveGameOddsByWeek/2023REG/1?key=YOUR_API_KEY')
       .then(response => response.json())
       .then(data => console.log(data));

4. TheSportsDB (https://www.thesportsdb.com/api.php)
   - Completely free (with attribution)
   - Sports: Multiple sports worldwide
   - Features: Teams, players, events (limited odds data)
   - Implementation example:
     fetch('https://www.thesportsdb.com/api/v1/json/YOUR_API_KEY/eventsnextleague.php?id=4328')
       .then(response => response.json())
       .then(data => console.log(data));

5. Sports Open Data (https://www.sportsopendata.net/)
   - Completely free
   - Sports: Soccer/football primarily
   - Features: Leagues, teams, standings (no odds data)
   - Implementation example:
     fetch('https://api.sportsopendata.net/v1/leagues')
       .then(response => response.json())
       .then(data => console.log(data));

ALTERNATIVE APPROACH:

If you can't use a sports API, consider these options:

1. Web Scraping (with proper permissions)
   - Scrape odds data from public betting sites
   - Requires regular maintenance as site structures change
   - Use tools like Puppeteer, Cheerio, or BeautifulSoup

2. Crowd-sourced Data
   - Allow users to input match data and odds
   - Community-driven approach
   - Requires moderation

3. Manual Updates
   - Manually update match data for important games
   - Labor-intensive but no API costs
   - Works for sites with fewer betting options
*/

// Initialize the sports betting system
document.addEventListener('DOMContentLoaded', () => {
    new SportsBetting();
});