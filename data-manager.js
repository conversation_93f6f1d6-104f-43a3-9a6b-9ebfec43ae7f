/**
 * Data Manager for Sports Betting System
 * 
 * This service manages all data interactions between the UI and the API.
 * It handles data formatting, caching, and provides a unified data structure.
 */
class SportsBettingDataManager {
    constructor(options = {}) {
        // Configuration
        this.config = {
            baseUrl: options.baseUrl || '/api',
            defaultSport: options.defaultSport || 'football',
            autoRefreshInterval: options.autoRefreshInterval || 5 * 60 * 1000, // 5 minutes
            ...options
        };
        
        // Initialize API service
        this.api = new SportsBettingAPI({
            baseUrl: this.config.baseUrl
        });
        
        // Data storage
        this.sports = [];
        this.matches = {};
        this.currentSport = this.config.defaultSport;
        
        // Auto-refresh timer
        this.refreshTimer = null;
        
        // Event listeners
        this.eventListeners = {
            'data-updated': [],
            'error': []
        };
        
        // User data
        this.userData = null;
    }
    
    /**
     * Initialize the data manager
     */
    async initialize() {
        try {
            // Load available sports
            await this.loadSports();
            
            // Load matches for default sport
            await this.loadMatches(this.currentSport);
            
            // Try to load user data if token exists
            if (localStorage.getItem('auth_token')) {
                try {
                    this.userData = await this.api.getCurrentUser();
                } catch (error) {
                    console.warn('Failed to load user data, may need to log in again');
                    // Don't throw error here, just continue without user data
                }
            }
            
            // Start auto-refresh
            this.startAutoRefresh();
            
            return true;
        } catch (error) {
            console.error('Failed to initialize data manager:', error);
            this.triggerEvent('error', { message: 'Failed to initialize data', error });
            return false;
        }
    }
    
    /**
     * Load available sports from the API
     */
    async loadSports() {
        try {
            this.sports = await this.api.getSports();
            this.triggerEvent('data-updated', { type: 'sports', data: this.sports });
            return this.sports;
        } catch (error) {
            console.error('Failed to load sports:', error);
            this.triggerEvent('error', { message: 'Failed to load sports', error });
            throw error;
        }
    }
    
    /**
     * Load matches for a specific sport
     */
    async loadMatches(sportId, options = {}) {
        try {
            // Update current sport
            this.currentSport = sportId;
            
            // Default options for match loading
            const defaultOptions = {
                status: 'upcoming',  // upcoming, live, completed, all
                limit: 20,           // number of matches per page
                page: 1              // page number
            };
            
            const requestOptions = { ...defaultOptions, ...options };
            
            // Get matches from API
            const matches = await this.api.getMatches(sportId, requestOptions);
            
            // Store matches for this sport
            this.matches[sportId] = matches;
            
            // Trigger update event
            this.triggerEvent('data-updated', { 
                type: 'matches', 
                sportId, 
                data: this.matches[sportId] 
            });
            
            return this.matches[sportId];
        } catch (error) {
            console.error(`Failed to load matches for sport ${sportId}:`, error);
            this.triggerEvent('error', { 
                message: `Failed to load matches for ${sportId}`, 
                error 
            });
            throw error;
        }
    }
    
    /**
     * Load live matches across all sports
     */
    async loadLiveMatches() {
        try {
            const liveMatches = await this.api.getLiveMatches();
            
            this.triggerEvent('data-updated', {
                type: 'live-matches',
                data: liveMatches
            });
            
            return liveMatches;
        } catch (error) {
            console.error('Failed to load live matches:', error);
            this.triggerEvent('error', {
                message: 'Failed to load live matches',
                error
            });
            throw error;
        }
    }
    
    /**
     * Load featured matches
     */
    async loadFeaturedMatches(limit = 10) {
        try {
            const featuredMatches = await this.api.getFeaturedMatches(limit);
            
            this.triggerEvent('data-updated', {
                type: 'featured-matches',
                data: featuredMatches
            });
            
            return featuredMatches;
        } catch (error) {
            console.error('Failed to load featured matches:', error);
            this.triggerEvent('error', {
                message: 'Failed to load featured matches',
                error
            });
            throw error;
        }
    }
    
    /**
     * Start auto-refresh for current sport data
     */
    startAutoRefresh() {
        // Clear any existing timer
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        // Set new timer
        this.refreshTimer = setInterval(async () => {
            try {
                await this.loadMatches(this.currentSport);
                console.log(`Auto-refreshed data for ${this.currentSport}`);
            } catch (error) {
                console.error('Auto-refresh failed:', error);
            }
        }, this.config.autoRefreshInterval);
    }
    
    /**
     * Stop auto-refresh
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    /**
     * Get matches for the current sport
     */
    getCurrentMatches() {
        return this.matches[this.currentSport] || [];
    }
    
    /**
     * Get a specific match by ID
     */
    getMatchById(matchId) {
        for (const sportId in this.matches) {
            const match = this.matches[sportId].find(m => m.id === matchId);
            if (match) return match;
        }
        return null;
    }
    
    /**
     * Change the current sport and load its matches
     */
    async changeSport(sportId) {
        if (sportId !== this.currentSport) {
            await this.loadMatches(sportId);
        }
        return this.matches[sportId];
    }
    
    /**
     * Get bet history for current user
     */
    async getBetHistory(options = {}) {
        try {
            const betHistory = await this.api.getBetHistory(options);
            
            this.triggerEvent('data-updated', {
                type: 'bet-history',
                data: betHistory
            });
            
            return betHistory;
        } catch (error) {
            console.error('Failed to load bet history:', error);
            this.triggerEvent('error', {
                message: 'Failed to load bet history',
                error
            });
            throw error;
        }
    }
    
    /**
     * Place a bet
     */
    async placeBet(betData) {
        try {
            const result = await this.api.placeBet(betData);
            
            // Update user data if available
            if (result.balance !== undefined && this.userData) {
                this.userData.balance = result.balance;
            }
            
            this.triggerEvent('data-updated', {
                type: 'bet-placed',
                data: result
            });
            
            return result;
        } catch (error) {
            console.error('Failed to place bet:', error);
            this.triggerEvent('error', {
                message: 'Failed to place bet',
                error
            });
            throw error;
        }
    }
    
    /**
     * Cancel a bet
     */
    async cancelBet(betId) {
        try {
            const result = await this.api.cancelBet(betId);
            
            // Update user data if available
            if (result.balance !== undefined && this.userData) {
                this.userData.balance = result.balance;
            }
            
            this.triggerEvent('data-updated', {
                type: 'bet-canceled',
                data: result
            });
            
            return result;
        } catch (error) {
            console.error('Failed to cancel bet:', error);
            this.triggerEvent('error', {
                message: 'Failed to cancel bet',
                error
            });
            throw error;
        }
    }
    
    /**
     * Get user wallet balance
     */
    async getWalletBalance() {
        try {
            const balance = await this.api.getWalletBalance();
            
            // Update user data if available
            if (this.userData) {
                this.userData.balance = balance;
            }
            
            return balance;
        } catch (error) {
            console.error('Failed to get wallet balance:', error);
            throw error;
        }
    }
    
    /**
     * Add an event listener
     */
    addEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].push(callback);
        }
    }
    
    /**
     * Remove an event listener
     */
    removeEventListener(event, callback) {
        if (this.eventListeners[event]) {
            this.eventListeners[event] = this.eventListeners[event]
                .filter(cb => cb !== callback);
        }
    }
    
    /**
     * Trigger an event
     */
    triggerEvent(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in event listener for ${event}:`, error);
                }
            });
        }
    }
}

// Export the data manager
window.SportsBettingDataManager = SportsBettingDataManager;