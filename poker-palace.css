/* Poker Palace - Texas Hold'em - Professional Casino Design */

:root {
    /* Poker-specific color palette */
    --poker-green: #0d5016;
    --poker-green-light: #1a7527;
    --poker-green-dark: #062208;
    --felt-color: #1a7527;
    --chip-red: #d32f2f;
    --chip-blue: #1976d2;
    --chip-green: #388e3c;
    --chip-black: #424242;
    --chip-white: #fafafa;
    --card-bg: #ffffff;
    --card-red: #d32f2f;
    --card-black: #212529;
    
    /* Base theme colors */
    --primary-dark: #0f1728;
    --primary-medium: #16213e;
    --primary-light: #1a1a2e;
    --accent-blue: #0097e6;
    --accent-gold: #ffc107;
    --accent-purple: #9c27b0;
    --text-light: #ffffff;
    --text-medium: #e9ecef;
    --text-tertiary: #adb5bd;
    --text-muted: #6c757d;
    --border-color: rgba(255, 255, 255, 0.1);
    --success-color: #4caf50;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    
    /* Glassmorphism effects */
    --glass-bg: rgba(22, 33, 62, 0.8);
    --glass-border: rgba(255, 255, 255, 0.08);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    
    /* Spacing and layout */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    
    /* Mobile-specific variables */
    --mobile-padding: 12px;
    --mobile-gap: 8px;
    --touch-target: 44px;
    --card-width: 60px;
    --card-height: 84px;
    --card-width-mobile: 40px;
    --card-height-mobile: 56px;
    
    /* Enhanced mobile layout */
    --mobile-header-height: 50px;
    --mobile-table-height: 350px;
    --mobile-controls-height: 200px;
    --mobile-pro-panel-height: 180px;
    --mobile-font-sm: 11px;
    --mobile-font-xs: 9px;
    
    /* Mobile animation speeds */
    --mobile-transition: all 0.2s ease;
    --mobile-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Montserrat', sans-serif;
    background: radial-gradient(ellipse at center, var(--poker-green) 0%, var(--poker-green-dark) 100%);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

/* Pro View Mode Toggle */
body.pro-view-active .pro-analytics {
    display: block !important;
}

.game-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--mobile-padding);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.game-header {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-gap);
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(0deg, transparent, rgba(33, 150, 243, 0.05), transparent);
}

.header-left {
    order: 1;
}

.header-center {
    order: 2;
    text-align: center;
}

.header-right {
    order: 3;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--mobile-gap);
}

.back-link {
    display: inline-flex;
    align-items: center;
    color: var(--text-light);
    text-decoration: none;
    font-size: 14px;
    transition: var(--transition);
    padding: 8px;
    border-radius: var(--border-radius);
}

.back-link i {
    margin-right: 6px;
}

.back-link:hover {
    color: var(--accent-blue);
    background: rgba(33, 150, 243, 0.1);
}

.back-text {
    display: none;
}

.game-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-subtitle {
    font-size: 14px;
    font-weight: 300;
    color: var(--text-light);
    opacity: 0.8;
}

/* View Mode Toggle */
.view-mode-toggle {
    display: flex;
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 2px;
    border: 1px solid var(--border-color);
}

.view-btn {
    background: transparent;
    border: none;
    color: var(--text-medium);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: var(--transition-fast);
    min-height: var(--touch-target);
}

.view-btn.active {
    background: var(--accent-blue);
    color: var(--text-light);
}

.view-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
}

.view-text {
    display: none;
}

.info-btn, .verify-btn {
    background: linear-gradient(to bottom, var(--primary-medium), var(--primary-dark));
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    transition: var(--transition);
    min-height: var(--touch-target);
    gap: 4px;
}

.info-btn:hover, .verify-btn:hover {
    background: linear-gradient(to bottom, var(--primary-light), var(--primary-medium));
    border-color: var(--accent-blue);
}

.btn-text {
    display: none;
}

/* Mobile Status Bar */
.mobile-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--primary-medium);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-item i {
    color: var(--accent-blue);
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 16px;
}

/* Poker Table */
.poker-table {
    background: radial-gradient(ellipse at center, var(--felt-color) 0%, var(--poker-green-dark) 100%);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.3), 0 4px 15px rgba(0, 0, 0, 0.4);
    position: relative;
    min-height: 400px;
    border: 3px solid #8B4513;
    overflow: hidden;
}

.poker-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none"/><path d="M0,50 Q50,0 100,50 Q50,100 0,50" stroke="rgba(255,255,255,0.03)" stroke-width="1" fill="none"/></svg>') repeat;
    pointer-events: none;
}

/* Community Cards */
.community-cards {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.community-cards-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.cards-container {
    display: flex;
    gap: 6px;
    justify-content: center;
}

.card-slot {
    width: var(--card-width-mobile);
    height: var(--card-height-mobile);
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.3);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: var(--transition);
}

.card-slot:hover {
    border-color: var(--accent-gold);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.3);
}

.card {
    width: 100%;
    height: 100%;
    background: var(--card-bg);
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px;
    position: relative;
    border: 1px solid #ddd;
}

.card-back {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--accent-blue), #1565c0);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    position: relative;
    overflow: hidden;
}

.card-back::before {
    content: '♠';
    position: absolute;
    font-size: 18px;
    opacity: 0.3;
}

.card-value {
    font-size: 14px;
    font-weight: bold;
    line-height: 1;
}

.card-suit {
    font-size: 10px;
    line-height: 1;
}

.card-red .card-value,
.card-red .card-suit {
    color: var(--card-red);
}

.card-black .card-value,
.card-black .card-suit {
    color: var(--card-black);
}

/* Pot Display */
.pot-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    border-radius: var(--border-radius);
    padding: 12px 16px;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.pot-label {
    font-size: 12px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.pot-amount {
    font-size: 18px;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 8px;
}

.pot-chips {
    display: flex;
    justify-content: center;
    gap: 4px;
}

.chip {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.chip-red {
    background: var(--chip-red);
}

.chip-blue {
    background: var(--chip-blue);
}

.chip-green {
    background: var(--chip-green);
}

/* Players Grid */
.players-grid {
    position: relative;
    width: 100%;
    height: 100%;
}

.player-seat {
    position: absolute;
    width: 100px;
    background: var(--glass-bg);
    border-radius: var(--border-radius);
    padding: 8px;
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(8px);
    transition: var(--transition);
}

.player-seat:hover {
    border-color: var(--accent-gold);
    box-shadow: 0 0 15px rgba(255, 193, 7, 0.2);
}

/* Player positioning around table */
.player-seat#player0 {
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
}

.player-seat#player1 {
    bottom: 30%;
    right: 10px;
}

.player-seat#player2 {
    top: 30%;
    right: 10px;
}

.player-seat#player3 {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
}

.player-seat#player4 {
    top: 30%;
    left: 10px;
}

.player-seat#player5 {
    bottom: 30%;
    left: 10px;
}

.player-info {
    text-align: center;
    margin-bottom: 6px;
}

.player-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 2px;
}

.player-chips {
    font-size: 10px;
    color: var(--accent-gold);
    margin-bottom: 2px;
}

.player-status {
    font-size: 9px;
    color: var(--text-medium);
    margin-bottom: 2px;
}

.ai-difficulty {
    font-size: 8px;
    color: var(--accent-blue);
    background: rgba(33, 150, 243, 0.1);
    padding: 1px 4px;
    border-radius: 3px;
}

.player-cards {
    display: flex;
    gap: 3px;
    justify-content: center;
    margin-bottom: 6px;
}

.player-cards .card-slot {
    width: 24px;
    height: 34px;
}

.player-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.action-btn {
    padding: 6px 8px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    min-height: 32px;
}

.fold-btn {
    background: var(--danger-color);
    color: white;
}

.fold-btn:hover {
    background: #d32f2f;
    transform: translateY(-1px);
}

.call-btn {
    background: var(--accent-blue);
    color: white;
}

.call-btn:hover {
    background: #1976d2;
    transform: translateY(-1px);
}

.raise-btn {
    background: var(--success-color);
    color: white;
}

.raise-btn:hover {
    background: #388e3c;
    transform: translateY(-1px);
}

.check-btn {
    background: var(--warning-color);
    color: white;
}

.check-btn:hover {
    background: #f57c00;
    transform: translateY(-1px);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Dealer Button */
.dealer-button {
    position: absolute;
    width: 30px;
    height: 30px;
    background: var(--accent-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-dark);
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    top: 60%;
    right: 15%;
    border: 2px solid white;
}

/* Game Controls */
.game-controls {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.betting-controls {
    margin-bottom: 16px;
}

.bet-amount-container label {
    display: block;
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 8px;
    font-weight: 600;
}

.bet-input-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.bet-slider {
    flex: 1;
    height: 6px;
    background: var(--primary-dark);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.bet-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: var(--accent-gold);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bet-input {
    width: 80px;
    padding: 8px;
    background: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-light);
    text-align: center;
    font-size: 14px;
}

.quick-bet-buttons {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.quick-bet {
    padding: 6px 12px;
    background: var(--primary-medium);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quick-bet:hover {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
}

/* Game Info Panel */
.game-info-panel {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.info-section {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 12px;
}

.info-section h3 {
    font-size: 14px;
    color: var(--accent-gold);
    margin-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 4px;
}

.hand-strength {
    margin-bottom: 8px;
}

.strength-label {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 4px;
}

.strength-bars {
    display: flex;
    gap: 2px;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: var(--primary-dark);
    border-radius: 2px;
}

.strength-bar.active {
    background: var(--accent-gold);
}

.win-probability {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prob-label {
    font-size: 12px;
    color: var(--text-medium);
}

.prob-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--success-color);
}

.blind-amounts {
    display: flex;
    justify-content: space-between;
}

.blind-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.blind-label {
    font-size: 11px;
    color: var(--text-medium);
}

.blind-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--accent-gold);
}

.round-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.round-phase {
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-blue);
}

.players-active, .next-action {
    font-size: 12px;
    color: var(--text-medium);
}

/* Pro Analytics - Hidden by default */
.pro-analytics {
    display: none;
    background: linear-gradient(135deg, var(--accent-purple), #7b1fa2);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    margin-top: 16px;
    box-shadow: var(--glass-shadow);
}

/* Show Pro Analytics when Pro View is active */
body.pro-view-active .pro-analytics {
    display: block !important;
}

.analytics-section h3,
.opponent-analysis h3,
.hand-history h3 {
    color: var(--text-light);
    font-size: 16px;
    margin-bottom: 12px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.analytics-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
    text-align: center;
}

.analytics-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.analytics-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-light);
}

.opponent-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;
}

.opponent-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
}

.opponent-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    margin-bottom: 2px;
}

.opponent-tendency {
    font-size: 11px;
    color: var(--accent-gold);
    margin-bottom: 2px;
}

.opponent-notes {
    font-size: 10px;
    color: var(--text-medium);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.history-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-hand {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
}

.history-result {
    font-size: 11px;
    color: var(--success-color);
}

.history-action {
    font-size: 10px;
    color: var(--text-medium);
}

/* Actions Panel */
.actions-panel {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    margin-top: 16px;
}

.game-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.deal-btn {
    background: var(--success-color);
    color: white;
}

.auto-fold-btn {
    background: var(--warning-color);
    color: white;
}

.sit-out-btn {
    background: var(--accent-blue);
    color: white;
}

.leave-btn {
    background: var(--danger-color);
    color: white;
}

.difficulty-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.difficulty-selector label {
    font-size: 12px;
    color: var(--text-light);
    font-weight: 600;
}

.difficulty-select {
    padding: 8px;
    background: var(--primary-dark);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-light);
    font-size: 12px;
}

/* Info Panel */
.info-panel {
    display: none;
}

.hand-rankings,
.position-guide,
.session-stats {
    background: var(--glass-bg);
    border-radius: var(--border-radius-lg);
    padding: 16px;
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    margin-bottom: 16px;
}

.hand-rankings h3,
.position-guide h3,
.session-stats h3 {
    color: var(--accent-gold);
    font-size: 16px;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.ranking-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.ranking-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
}

.ranking-number {
    width: 20px;
    height: 20px;
    background: var(--accent-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
}

.ranking-name {
    flex: 1;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
}

.ranking-example {
    font-size: 10px;
    color: var(--text-medium);
    font-family: monospace;
}

.position-tips {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.position-tip {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 8px;
}

.position-name {
    font-size: 12px;
    font-weight: 600;
    color: var(--accent-blue);
    margin-bottom: 2px;
}

.position-advice {
    font-size: 11px;
    color: var(--text-medium);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.stat-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius);
    padding: 8px;
    text-align: center;
}

.stat-label {
    font-size: 10px;
    color: var(--text-medium);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--accent-gold);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 16px;
}

.modal-content {
    background: var(--primary-medium);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--accent-blue);
}

.modal-header {
    background: var(--primary-dark);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    color: var(--accent-gold);
    font-size: 18px;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 24px;
    cursor: pointer;
    width: var(--touch-target);
    height: var(--touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal-btn:hover {
    color: var(--accent-blue);
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 16px;
    overflow-y: auto;
    max-height: calc(90vh - 60px);
}

.tutorial-section,
.rules-section {
    margin-bottom: 16px;
}

.tutorial-section h3,
.rules-section h3 {
    color: var(--accent-blue);
    font-size: 16px;
    margin-bottom: 8px;
}

.tutorial-section p,
.tutorial-section li,
.rules-section p,
.rules-section li {
    font-size: 13px;
    margin-bottom: 4px;
    color: var(--text-light);
}

.tutorial-section ul,
.tutorial-section ol,
.rules-section ul,
.rules-section ol {
    padding-left: 16px;
    margin-top: 4px;
}

.hidden {
    display: none !important;
}

/* Enhanced Mobile Styles (320px - 480px) */
@media (max-width: 480px) {
    :root {
        --mobile-padding: 8px;
        --mobile-gap: 6px;
        --card-width-mobile: 35px;
        --card-height-mobile: 49px;
    }
    
    .game-container {
        padding: var(--mobile-padding);
        min-height: 100vh;
        overflow-x: hidden;
    }
    
    /* Enhanced mobile header */
    .game-header {
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium));
        border-radius: var(--border-radius);
        padding: 8px 12px;
        margin-bottom: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    .header-right {
        gap: 4px;
    }
    
    .view-btn {
        padding: 6px 8px;
        font-size: 10px;
        min-width: 60px;
    }
    
    .info-btn, .verify-btn {
        padding: 6px 8px;
        font-size: 10px;
    }
    
    /* Enhanced mobile status bar */
    .mobile-status-bar {
        background: linear-gradient(90deg, var(--primary-medium), var(--primary-dark));
        padding: 10px 12px;
        margin-bottom: 8px;
        border-radius: var(--border-radius);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
        border: 1px solid var(--border-color);
    }
    
    .status-item {
        font-size: 11px;
        font-weight: 600;
    }
    
    .status-item i {
        font-size: 12px;
    }
    
    /* Enhanced mobile poker table */
    .poker-table {
        height: var(--mobile-table-height);
        padding: 12px;
        position: relative;
        border-radius: var(--border-radius-lg);
        overflow: visible;
    }
    
    /* Optimized community cards for mobile */
    .community-cards {
        top: 25%;
        transform: translateX(-50%);
    }
    
    .community-cards-label {
        font-size: 10px;
        margin-bottom: 6px;
        font-weight: 600;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    }
    
    .cards-container {
        gap: 4px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .card-slot {
        width: var(--card-width-mobile);
        height: var(--card-height-mobile);
        border-width: 1px;
    }
    
    /* Enhanced mobile pot display */
    .pot-display {
        padding: 8px 12px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }
    
    .pot-label {
        font-size: 10px;
        font-weight: 600;
    }
    
    .pot-amount {
        font-size: 16px;
        font-weight: 700;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }
    
    .chip {
        width: 16px;
        height: 16px;
        border-width: 1px;
    }
    
    /* Enhanced mobile player seats */
    .player-seat {
        width: 85px;
        padding: 6px;
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
    }
    
    .player-seat#player0 {
        width: 100px;
        bottom: 8px;
    }
    
    .player-name {
        font-size: 10px;
        font-weight: 700;
    }
    
    .player-chips {
        font-size: 9px;
        font-weight: 600;
    }
    
    .player-status {
        font-size: 8px;
    }
    
    .ai-difficulty {
        font-size: 7px;
        padding: 1px 3px;
    }
    
    .player-cards .card-slot {
        width: 20px;
        height: 28px;
    }
    
    /* Enhanced mobile action buttons */
    .action-btn {
        font-size: 9px;
        padding: 4px 6px;
        min-height: 28px;
        border-radius: 4px;
    }
    
    .action-btn i {
        font-size: 8px;
    }
    
    /* Enhanced mobile dealer button */
    .dealer-button {
        width: 24px;
        height: 24px;
        font-size: 10px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    }
    
    /* Enhanced mobile game controls */
    .game-controls {
        padding: 12px;
        margin-bottom: 8px;
        border-radius: var(--border-radius-lg);
    }
    
    .bet-amount-container label {
        font-size: 12px;
        margin-bottom: 6px;
    }
    
    .bet-input-group {
        gap: 8px;
        margin-bottom: 8px;
    }
    
    .bet-input {
        width: 70px;
        padding: 6px;
        font-size: 12px;
        border-radius: 4px;
    }
    
    .quick-bet-buttons {
        gap: 4px;
    }
    
    .quick-bet {
        padding: 4px 8px;
        font-size: 10px;
        min-height: 32px;
        border-radius: 4px;
    }
    
    /* Enhanced mobile info panel */
    .game-info-panel {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .info-section {
        padding: 8px;
        border-radius: var(--border-radius);
    }
    
    .info-section h3 {
        font-size: 12px;
        margin-bottom: 6px;
    }
    
    .strength-label {
        font-size: 10px;
    }
    
    .prob-label, .prob-value {
        font-size: 11px;
    }
    
    .blind-label {
        font-size: 9px;
    }
    
    .blind-value {
        font-size: 10px;
    }
    
    .round-phase {
        font-size: 12px;
    }
    
    .players-active, .next-action {
        font-size: 10px;
    }
    
    /* Enhanced mobile actions panel */
    .actions-panel {
        padding: 12px;
        border-radius: var(--border-radius-lg);
    }
    
    .game-actions {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        margin-bottom: 12px;
    }
    
    .difficulty-selector label {
        font-size: 11px;
    }
    
    .difficulty-select {
        padding: 6px;
        font-size: 11px;
    }
}

/* Enhanced Pro View for Mobile */
@media (max-width: 480px) {
    body.pro-view-active .pro-analytics {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: var(--mobile-pro-panel-height);
        background: linear-gradient(135deg, var(--accent-purple), #7b1fa2);
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        z-index: 1000;
        overflow-y: auto;
        padding: 12px;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
        border-top: 2px solid var(--accent-gold);
        animation: slideUpMobile 0.3s var(--mobile-bounce);
    }
    
    @keyframes slideUpMobile {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    body.pro-view-active .main-content {
        padding-bottom: calc(var(--mobile-pro-panel-height) + 20px);
    }
    
    .pro-analytics h3 {
        font-size: 14px;
        margin-bottom: 8px;
        color: var(--text-light);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 4px;
    }
    
    .analytics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
        margin-bottom: 12px;
    }
    
    .analytics-item {
        padding: 6px;
        border-radius: 4px;
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        transition: all 0.2s ease;
    }
    
    .analytics-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
    }
    
    .analytics-label {
        font-size: 9px;
        margin-bottom: 2px;
        font-weight: 600;
        color: var(--text-medium);
    }
    
    .analytics-value {
        font-size: 12px;
        font-weight: 700;
        color: var(--text-light);
        margin-bottom: 1px;
    }
    
    .analytics-help {
        font-size: 7px;
        color: var(--text-tertiary);
        opacity: 0.8;
    }
    
    /* Enhanced opponent analysis for mobile */
    .opponent-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3px;
    }
    
    .opponent-tendency {
        font-size: 8px;
        padding: 1px 4px;
        border-radius: 3px;
        font-weight: 600;
    }
    
    .opponent-tendency.tight-passive {
        background: rgba(76, 175, 80, 0.2);
        color: var(--success-color);
    }
    
    .opponent-tendency.loose-aggressive {
        background: rgba(244, 67, 54, 0.2);
        color: var(--danger-color);
    }
    
    .opponent-tendency.tight-aggressive {
        background: rgba(255, 152, 0, 0.2);
        color: var(--warning-color);
    }
    
    .opponent-stats-mini {
        font-size: 7px;
        color: var(--text-tertiary);
        margin-top: 2px;
    }
    
    /* Enhanced history items */
    .history-item {
        padding: 6px;
        border-radius: 4px;
        font-size: 9px;
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
    }
    
    .history-item.won {
        border-left-color: var(--success-color);
        background: rgba(76, 175, 80, 0.1);
    }
    
    .history-item.lost {
        border-left-color: var(--danger-color);
        background: rgba(244, 67, 54, 0.1);
    }
    
    .history-item.folded {
        border-left-color: var(--text-muted);
        background: rgba(108, 117, 125, 0.1);
    }
    
    .history-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2px;
    }
    
    .history-time {
        font-size: 7px;
        color: var(--text-tertiary);
        margin-top: 1px;
    }
    
    .opponent-stats {
        gap: 6px;
        margin-bottom: 12px;
    }
    
    .opponent-item {
        padding: 6px;
        border-radius: 4px;
    }
    
    .opponent-name {
        font-size: 10px;
        margin-bottom: 1px;
    }
    
    .opponent-tendency {
        font-size: 9px;
        margin-bottom: 1px;
    }
    
    .opponent-notes {
        font-size: 8px;
    }
    
    .history-list {
        gap: 4px;
        max-height: 80px;
        overflow-y: auto;
    }
    
    .history-item {
        padding: 6px;
        border-radius: 4px;
        font-size: 9px;
    }
    
    .history-hand {
        font-size: 10px;
    }
    
    .history-result {
        font-size: 9px;
    }
    
    .history-action {
        font-size: 8px;
    }
    
    /* Pro View toggle enhancement for mobile */
    .pro-analytics::before {
        content: '';
        position: absolute;
        top: 6px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }
    
    /* Enhanced mobile pro view sections */
    .analytics-section,
    .opponent-analysis,
    .hand-history {
        margin-bottom: 8px;
    }
    
    .analytics-section h3,
    .opponent-analysis h3,
    .hand-history h3 {
        font-size: 13px;
        margin-bottom: 6px;
    }
}

/* Tablet Styles */
@media (min-width: 481px) and (max-width: 768px) {
    .mobile-status-bar {
        display: flex;
        background: linear-gradient(90deg, var(--primary-medium), var(--primary-dark));
        padding: 8px 16px;
        margin-bottom: 12px;
        border-radius: var(--border-radius);
    }
    
    .card-slot {
        width: var(--card-width);
        height: var(--card-height);
    }
    
    .player-cards .card-slot {
        width: 32px;
        height: 45px;
    }
    
    .cards-container {
        gap: 8px;
    }
    
    .game-info-panel {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .analytics-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .game-actions {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .btn-text {
        display: inline;
    }
    
    .view-text {
        display: inline;
    }
    
    .back-text {
        display: inline;
    }
    
    /* Enhanced Pro View for Tablet */
    body.pro-view-active .pro-analytics {
        position: relative;
        height: auto;
        margin-top: 16px;
        border-radius: var(--border-radius-lg);
        animation: none;
    }
}

/* Desktop Styles */
@media (min-width: 769px) {
    .game-container {
        padding: 20px;
    }
    
    .game-header {
        flex-direction: row;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
    }
    
    .header-left, .header-right {
        flex: 1;
    }
    
    .header-right {
        justify-content: flex-end;
    }
    
    .header-center {
        flex: 2;
    }
    
    .game-title {
        font-size: 36px;
        margin-bottom: 5px;
    }
    
    .game-subtitle {
        font-size: 16px;
    }
    
    .main-content {
        flex-direction: row;
        gap: 20px;
    }
    
    .game-section {
        flex: 2;
    }
    
    .poker-table {
        min-height: 500px;
        padding: 30px;
    }
    
    .pot-display {
        padding: 16px 20px;
    }
    
    .pot-amount {
        font-size: 24px;
    }
    
    .community-cards-label {
        font-size: 14px;
        margin-bottom: 12px;
    }
    
    .player-seat {
        width: 140px;
        padding: 12px;
    }
    
    .player-seat#player0 {
        width: 160px;
    }
    
    .player-name {
        font-size: 14px;
    }
    
    .player-chips {
        font-size: 12px;
    }
    
    .player-status {
        font-size: 11px;
    }
    
    .ai-difficulty {
        font-size: 10px;
    }
    
    .action-btn {
        font-size: 12px;
        padding: 8px 12px;
        min-height: 36px;
    }
    
    .dealer-button {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
    
    .game-controls {
        padding: 20px;
    }
    
    .game-info-panel {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .info-panel {
        display: flex;
        flex-direction: column;
        flex: 1;
        max-width: 350px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
    }
    
    .modal-content {
        max-width: 700px;
    }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
    body.pro-view-active .main-content {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 20px;
    }
    
    body.pro-view-active .game-section {
        order: 1;
    }
    
    body.pro-view-active .info-panel {
        order: 2;
        max-width: none;
    }
    
    .pro-analytics {
        margin-top: 0;
    }
}

/* Animations */
@keyframes dealCard {
    from {
        transform: translateY(-100px) rotate(-180deg);
        opacity: 0;
    }
    to {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
}

@keyframes chipStack {
    from {
        transform: scale(0) rotate(180deg);
        opacity: 0;
    }
    to {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.card {
    animation: dealCard 0.5s ease-out;
}

.chip {
    animation: chipStack 0.3s ease-out;
}

.player-seat.active {
    animation: pulse 1.5s infinite;
}

/* Player status indicators */
.player-seat.folded {
    opacity: 0.5;
    filter: grayscale(50%);
    transition: all 0.3s ease;
}

.player-seat.all-in .player-info {
    border: 2px solid var(--danger-color);
    border-radius: var(--border-radius);
    animation: pulse 1.5s infinite;
}

.player-seat.winner .player-info {
    border: 2px solid var(--success-color);
    border-radius: var(--border-radius);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
    animation: winner-glow 2s ease-in-out infinite alternate;
}

@keyframes winner-glow {
    from {
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.4);
    }
    to {
        box-shadow: 0 0 25px rgba(76, 175, 80, 0.6);
    }
}

/* Mobile-specific enhancements */
@media (max-width: 480px) {
    /* Enhanced touch feedback */
    .action-btn:active,
    .quick-bet:active,
    .view-btn:active {
        transform: scale(0.95) !important;
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    /* Improved mobile button spacing */
    .player-actions {
        gap: 6px;
    }
    
    .player-actions .action-btn {
        min-height: 36px;
        font-size: 10px;
        padding: 6px 8px;
        border-radius: 6px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    /* Mobile Pro View enhancement indicators */
    body.pro-view-active .mobile-status-bar {
        border: 2px solid var(--accent-purple);
        box-shadow: 0 0 15px rgba(156, 39, 176, 0.3);
        animation: pro-view-pulse 2s ease-in-out infinite;
    }
    
    @keyframes pro-view-pulse {
        0%, 100% {
            border-color: var(--accent-purple);
            box-shadow: 0 0 15px rgba(156, 39, 176, 0.3);
        }
        50% {
            border-color: var(--accent-gold);
            box-shadow: 0 0 20px rgba(255, 193, 7, 0.4);
        }
    }
    
    /* Pro View notification badge */
    body.pro-view-active .view-mode-toggle::after {
        content: 'PRO ACTIVE';
        position: absolute;
        top: -8px;
        right: -8px;
        background: var(--accent-purple);
        color: white;
        font-size: 8px;
        padding: 2px 4px;
        border-radius: 3px;
        font-weight: bold;
        animation: badge-bounce 0.5s ease-out;
    }
    
    @keyframes badge-bounce {
        0% {
            transform: scale(0);
            opacity: 0;
        }
        50% {
            transform: scale(1.2);
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
    
    /* Enhanced mobile slider styling */
    .bet-slider {
        height: 8px;
        background: linear-gradient(to right, var(--primary-dark), var(--accent-blue));
        border-radius: 4px;
        outline: none;
        -webkit-appearance: none;
        position: relative;
    }
    
    .bet-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 24px;
        height: 24px;
        background: var(--accent-gold);
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        border: 2px solid white;
        position: relative;
    }
    
    .bet-slider::-webkit-slider-thumb:active {
        transform: scale(1.1);
        box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
    }
    
    /* Mobile card animations */
    .card {
        transition: all 0.3s var(--mobile-bounce);
    }
    
    .card:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }
    
    /* Mobile chip stack animation */
    .chip {
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .chip:nth-child(1) {
        animation-delay: 0.1s;
    }
    
    .chip:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .chip:nth-child(3) {
        animation-delay: 0.3s;
    }
    
    /* Mobile gesture hint */
    body:not(.pro-view-active) .mobile-status-bar::after {
        content: '👆 Swipe up for Pro View';
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 10px;
        color: var(--text-tertiary);
        opacity: 0.7;
        animation: swipe-hint 3s ease-in-out infinite;
    }
    
    @keyframes swipe-hint {
        0%, 80%, 100% {
            opacity: 0.7;
            transform: translateX(-50%) translateY(0);
        }
        10%, 70% {
            opacity: 0.9;
            transform: translateX(-50%) translateY(-3px);
        }
    }
    
    /* Hide hint after user has used Pro View */
    body.has-used-pro-view .mobile-status-bar::after {
        display: none;
    }
    
    /* Mobile orientation optimization */
    @media (orientation: landscape) {
        .poker-table {
            height: 280px;
            min-height: 280px;
        }
        
        .game-controls {
            padding: 8px 12px;
        }
        
        .mobile-status-bar {
            padding: 6px 12px;
        }
        
        body.pro-view-active .pro-analytics {
            height: 120px;
        }
        
        .game-info-panel {
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
        }
    }
    
    /* Mobile performance optimizations */
    .poker-table,
    .game-controls,
    .actions-panel,
    .pro-analytics {
        transform: translateZ(0); /* Hardware acceleration */
        backface-visibility: hidden;
        perspective: 1000px;
    }
    
    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        .card,
        .chip,
        .action-btn,
        .player-seat {
            animation: none !important;
            transition: none !important;
        }
    }
}