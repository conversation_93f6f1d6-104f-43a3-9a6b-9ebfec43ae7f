<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TREASURE CASES - Mobile Optimized | GoldenAura Casino</title>
    <meta name="description" content="Play Treasure Cases, a strategic case selection game with mobile-optimized controls and professional analytics. Collect puzzle pieces and unlock treasures!">
    
    <!-- CSS Dependencies -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="cases.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body class="standard-view">
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- Animated Background -->
    <div class="treasure-background"></div>
    
    <!-- View Mode Toggle (will be created by JavaScript) -->

    <div class="cases-container">
        <div class="game-header">
            <h1 class="game-title">TREASURE CASES</h1>
            <p class="game-subtitle">Strategic case selection challenge • Collect puzzle pieces • Master the hunt • Mobile optimized</p>
        </div>

        <div class="game-dashboard">
            <!-- Main Stats Panel -->
            <div class="stats-panel">
                <div class="stat-card">
                    <div class="stat-label">Balance</div>
                    <div class="stat-value" id="balance">1000 GA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Bet Amount</div>
                    <div class="stat-value" id="betAmount">50 GA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Score</div>
                    <div class="stat-value" id="score">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Round</div>
                    <div class="stat-value" id="round">1</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Streak</div>
                    <div class="stat-value" id="streak">0</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">Time Left</div>
                    <div class="stat-value" id="timeDisplay">90s</div>
                </div>
            </div>

            <!-- Timer Display (Mobile-Optimized) -->
            <div class="timer-display">
                <div class="timer-circle" id="timerCircle">
                    <div class="timer-inner" id="timerValue">90</div>
                </div>
                <div class="stat-label" style="margin-top: 1rem;">Time Remaining</div>
            </div>

            <!-- Power-ups Panel -->
            <div class="power-ups">
                <button class="power-up" id="spotlightBtn">
                    <i class="fas fa-search"></i> 🔦 Spotlight
                    <div class="power-up-cooldown" id="spotlightCooldown"></div>
                </button>
                <button class="power-up" id="lockdownBtn">
                    <i class="fas fa-lock"></i> 🔒 Lockdown
                    <div class="power-up-cooldown" id="lockdownCooldown"></div>
                </button>
                <button class="power-up" id="timeCapsuleBtn">
                    <i class="fas fa-clock"></i> ⏰ Time +15s
                    <div class="power-up-cooldown" id="timeCapsuleCooldown"></div>
                </button>
            </div>
        </div>

        <!-- Puzzle Tracker -->
        <div class="puzzle-tracker">
            <div class="puzzle-piece" id="piece1">🧩</div>
            <div class="puzzle-piece" id="piece2">🧩</div>
            <div class="puzzle-piece" id="piece3">🧩</div>
        </div>

        <!-- Streak Indicator -->
        <div class="streak-indicator" id="streakIndicator" style="display: none;"></div>

        <!-- Cases Grid -->
        <div class="cases-grid" id="casesGrid">
            <!-- Cases will be generated by JavaScript -->
        </div>

        <!-- Bet Controls -->
        <div class="bet-controls">
            <button class="bet-btn" data-amount="-50">
                <i class="fas fa-minus"></i> 50
            </button>
            <button class="bet-btn" data-amount="-10">
                <i class="fas fa-minus"></i> 10
            </button>
            <button class="bet-btn" data-amount="10">
                <i class="fas fa-plus"></i> 10
            </button>
            <button class="bet-btn" data-amount="50">
                <i class="fas fa-plus"></i> 50
            </button>
        </div>
        
        <!-- Game Controls -->
        <div class="game-controls">
            <button class="btn btn-primary" id="startBtn">
                <i class="fas fa-play"></i>
                Start Adventure
            </button>
            <button class="btn btn-secondary" id="resetBtn" disabled>
                <i class="fas fa-refresh"></i>
                Reset Game
            </button>
        </div>

        <!-- Pro View: Rules and Strategy Guide -->
        <div class="strategy-hints" style="margin-top: 2rem;">
            <div class="panel-title">
                <i class="fas fa-book"></i>
                Game Rules & Strategy
            </div>
            <div class="hint-item">
                <strong>🎯 Objective:</strong> Open 5 cases within the time limit to maximize your score and winnings.
            </div>
            <div class="hint-item">
                <strong>💰 Points Cases:</strong> Earn base points with streak bonuses (10% per streak level).
            </div>
            <div class="hint-item">
                <strong>✖️ Multiplier Cases:</strong> Multiply your current score by the found value.
            </div>
            <div class="hint-item">
                <strong>🧩 Puzzle Pieces:</strong> Collect all 3 pieces for a massive 5x score bonus!
            </div>
            <div class="hint-item">
                <strong>⚡ Power-up Cases:</strong> Grant random power-ups to help your adventure.
            </div>
            <div class="hint-item">
                <strong>🔄 Reshuffling:</strong> Case values reshuffle every 3 opened cases (unless locked).
            </div>
            <div class="hint-item">
                <strong>💡 Strategy:</strong> Use Spotlight early, save Lockdown for good values, and Time Capsule for emergencies.
            </div>
        </div>
    </div>

    <!-- Round Complete Modal -->
    <div class="round-complete" id="roundComplete">
        <h2>
            <i class="fas fa-trophy"></i>
            Round Complete!
        </h2>
        <p id="roundResult"></p>
        <p><strong>Round Winnings: <span id="roundWinnings">0 GA</span></strong></p>
        <button class="btn btn-primary" onclick="nextRound()">
            <i class="fas fa-arrow-right"></i>
            Continue Adventure
        </button>
    </div>

    <!-- Loading indicator for mobile -->
    <div id="loadingIndicator" style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        z-index: 9999;
        display: none;
    ">
        <i class="fas fa-spinner fa-spin"></i>
        Loading...
    </div>

    <!-- JavaScript Dependencies -->
    <script src="assets/js/script.js"></script>
    <script src="cases.js"></script>
    
    <!-- Additional mobile optimization script -->
    <script>
        // Mobile-specific optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent zoom on double tap for game elements
            const gameElements = document.querySelectorAll('.cases-grid, .game-dashboard, .game-controls');
            gameElements.forEach(element => {
                element.addEventListener('touchend', function(e) {
                    e.preventDefault();
                }, { passive: false });
            });

            // Optimize for mobile viewport
            function adjustMobileViewport() {
                if (window.innerWidth <= 768) {
                    // Adjust viewport meta tag for better mobile experience
                    const viewport = document.querySelector('meta[name=viewport]');
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                    
                    // Add mobile-specific classes
                    document.body.classList.add('mobile-device');
                    
                    // Optimize cases grid for very small screens
                    if (window.innerWidth <= 480) {
                        const casesGrid = document.querySelector('.cases-grid');
                        if (casesGrid) {
                            casesGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';
                            casesGrid.style.gap = '0.4rem';
                        }
                    }
                }
            }

            // Run on load and resize
            adjustMobileViewport();
            window.addEventListener('resize', adjustMobileViewport);

            // Service Worker for offline capability (optional)
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js').catch(err => {
                    console.log('ServiceWorker registration failed: ', err);
                });
            }

            // Preload critical images for better mobile performance
            const criticalImages = [
                'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png'
            ];
            
            criticalImages.forEach(src => {
                const img = new Image();
                img.src = src;
            });

            // Add performance monitoring
            if (performance && performance.timing) {
                window.addEventListener('load', function() {
                    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                    console.log('Cases game load time:', loadTime + 'ms');
                    
                    // Show warning if load time is too high on mobile
                    if (loadTime > 3000 && window.innerWidth <= 768) {
                        console.warn('Slow loading detected on mobile device');
                    }
                });
            }

            // Enhanced mobile touch feedback
            const touchElements = document.querySelectorAll('.case, .power-up, .btn, .bet-btn');
            touchElements.forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.filter = 'brightness(0.8)';
                }, { passive: true });
                
                element.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.filter = '';
                    }, 150);
                }, { passive: true });
            });

            // Optimize animations for mobile devices
            if (window.innerWidth <= 768) {
                // Reduce animation complexity on mobile
                document.documentElement.style.setProperty('--animation-duration', '0.2s');
            }

            // Add orientation change handler
            window.addEventListener('orientationchange', function() {
                setTimeout(adjustMobileViewport, 100);
            });
        });

        // Performance optimization for mobile
        const isLowPerformanceDevice = () => {
            return navigator.hardwareConcurrency <= 2 || 
                   navigator.deviceMemory <= 2 ||
                   /Android.*Chrome\/[.0-9]*.*Mobile/i.test(navigator.userAgent);
        };

        if (isLowPerformanceDevice()) {
            // Reduce particle effects and animations
            document.documentElement.style.setProperty('--particle-count', '3');
            document.documentElement.style.setProperty('--animation-duration', '0.15s');
        }
    </script>
</body>
</html>