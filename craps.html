<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DICE DYNASTY - Provably Fair Craps Game | GoldenAura Casino</title>
    <meta name="description" content="Play Dice Dynasty, a provably fair craps game with mobile-optimized controls and professional analytics. Experience authentic casino craps with transparency.">
    
    <!-- CSS Dependencies -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="craps.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body class="standard-view">
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <!-- View Mode Toggle (will be created by JavaScript) -->

    <div class="craps-container">
        <div class="game-header">
            <h1 class="game-title">DICE DYNASTY</h1>
            <p class="game-subtitle">Provably Fair • Classic Casino Dice • Mobile Optimized • Pro Analytics Available</p>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats Panel -->
            <div class="stats-panel">
                <h3 class="panel-title">
                    <i class="fas fa-chart-bar"></i>
                    Player Stats
                </h3>
                
                <div class="stat-card">
                    <div class="stat-label">Total Games</div>
                    <div class="stat-value" id="totalGames">0</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Games Won</div>
                    <div class="stat-value" id="gamesWon">0</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Win Rate</div>
                    <div class="stat-value" id="winRate">0%</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-label">Balance</div>
                    <div class="stat-value" id="playerBalance">1000 GA</div>
                </div>

                <!-- Advanced Stats (Pro View Only) -->
                <div class="advanced-stats">
                    <div class="stat-card">
                        <div class="stat-label">Total Wagered</div>
                        <div class="stat-value" id="totalWagered">0 GA</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Net Profit</div>
                        <div class="stat-value" id="netProfit">0 GA</div>
                    </div>
                </div>

                <!-- Rules Section -->
                <div class="rules-section">
                    <button class="rules-toggle" onclick="toggleRules()">
                        <i class="fas fa-info-circle"></i> How to Play & Fairness
                    </button>
                    <div class="rules-content" id="rulesContent" style="display: none;">
                        <p><strong>Game Rules:</strong></p>
                        <p>Craps is a dice game where players bet on the outcomes of a pair of dice.</p>
                        
                        <p><strong>Come Out Roll:</strong></p>
                        <ul>
                            <li>Natural (7, 11): Wins on Pass Line, loses on Don't Pass</li>
                            <li>Craps (2, 3, 12): Loses on Pass Line, wins on Don't Pass (except 12 is a push)</li>
                            <li>Point (4, 5, 6, 8, 9, 10): Establishes the Point number</li>
                        </ul>
                        
                        <p><strong>Point Phase:</strong></p>
                        <ul>
                            <li>If Point number is rolled again: Pass Line wins, Don't Pass loses</li>
                            <li>If 7 is rolled: Pass Line loses, Don't Pass wins</li>
                            <li>Other numbers: No effect, continue rolling</li>
                        </ul>
                        
                        <p><strong>Proposition Bets:</strong></p>
                        <ul>
                            <li>Any 7: Wins if 7 is rolled (higher house edge)</li>
                            <li>Any Craps: Wins if 2, 3, or 12 is rolled</li>
                            <li>Hard Ways: Wins if the exact number is rolled as a pair</li>
                        </ul>
                        
                        <div class="bet-odds">
                            <p><strong>Bet Odds & Win Chances:</strong></p>
                            <table class="odds-table">
                                <tr>
                                    <th>Bet Type</th>
                                    <th>Win Chance</th>
                                    <th>House Edge</th>
                                </tr>
                                <tr>
                                    <td>Pass Line</td>
                                    <td>49.29%</td>
                                    <td>1.41%</td>
                                </tr>
                                <tr>
                                    <td>Don't Pass</td>
                                    <td>47.93%</td>
                                    <td>1.36%</td>
                                </tr>
                                <tr>
                                    <td>Any 7</td>
                                    <td>16.67%</td>
                                    <td>16.67%</td>
                                </tr>
                                <tr>
                                    <td>Any Craps</td>
                                    <td>11.11%</td>
                                    <td>11.11%</td>
                                </tr>
                                <tr>
                                    <td>Hard 4 or 10</td>
                                    <td>2.78%</td>
                                    <td>11.11%</td>
                                </tr>
                                <tr>
                                    <td>Hard 6 or 8</td>
                                    <td>4.17%</td>
                                    <td>9.09%</td>
                                </tr>
                            </table>
                            <p style="margin-top: 1rem;">
                                <strong>Overall Player Win Chance:</strong> 
                                <span style="color: var(--craps-yellow);">This game is designed with a statistical house advantage, meaning the player's overall win chance is less than 30%.</span>
                            </p>
                        </div>
                        
                        <p><strong>Provably Fair:</strong></p>
                        <ul>
                            <li>Each roll uses cryptographic seeds to ensure fairness</li>
                            <li>Client seed: generated by your browser</li>
                            <li>Server seed: pre-committed and hashed</li>
                            <li>Verify any roll outcome using the verification tool</li>
                        </ul>
                        
                        <p><strong>Mobile Controls:</strong></p>
                        <ul>
                            <li>Touch any betting area to place bets</li>
                            <li>Use +/- buttons to adjust bet amounts</li>
                            <li>Toggle between Standard and Pro views</li>
                            <li>Haptic feedback on supported devices</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Main Craps Table -->
            <div class="craps-table">
                <div class="bet-selector">
                    <div class="bet-type selected" data-bet="pass-line">Pass Line</div>
                    <div class="bet-type" data-bet="dont-pass">Don't Pass</div>
                    <div class="bet-type" data-bet="any-seven">Any 7</div>
                    <div class="bet-type" data-bet="any-craps">Any Craps</div>
                    <div class="bet-type" data-bet="hard-way">Hard Way</div>
                </div>
                
                <!-- Bet Controls (will be created by JavaScript if not present) -->
                
                <div class="table-layout">
                    <div class="pass-line" id="passLine">PASS LINE</div>
                    <div class="dont-pass" id="dontPass">DON'T PASS</div>
                    <div class="point-box" id="pointBox">-</div>
                    
                    <div class="prop-bets">
                        <div class="prop-bet" data-bet="any-seven">
                            <div class="prop-bet-value">7</div>
                            <div>Any Seven</div>
                        </div>
                        <div class="prop-bet" data-bet="any-craps">
                            <div class="prop-bet-value">2,3,12</div>
                            <div>Any Craps</div>
                        </div>
                        <div class="prop-bet" data-bet="hard-4">
                            <div class="prop-bet-value">4</div>
                            <div>Hard Way</div>
                        </div>
                        <div class="prop-bet" data-bet="hard-6">
                            <div class="prop-bet-value">6</div>
                            <div>Hard Way</div>
                        </div>
                        <div class="prop-bet" data-bet="hard-8">
                            <div class="prop-bet-value">8</div>
                            <div>Hard Way</div>
                        </div>
                        <div class="prop-bet" data-bet="hard-10">
                            <div class="prop-bet-value">10</div>
                            <div>Hard Way</div>
                        </div>
                    </div>
                </div>
                
                <div class="game-info">
                    <div class="game-phase" id="gamePhase">Come Out Roll</div>
                    <div class="game-message" id="gameMessage">Place your bets and roll the dice!</div>
                </div>
                
                <div class="dice-area">
                    <div class="die" id="die1">
                        <div class="die-dots" id="die1Dots"></div>
                    </div>
                    <div class="die" id="die2">
                        <div class="die-dots" id="die2Dots"></div>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button class="roll-btn" id="rollBtn">
                        <i class="fas fa-dice"></i>
                        ROLL DICE
                    </button>
                    <button class="new-game-btn" id="newGameBtn">
                        <i class="fas fa-refresh"></i>
                        NEW GAME
                    </button>
                </div>
            </div>

            <!-- Fairness & Verification Panel -->
            <div class="fairness-panel">
                <h3 class="panel-title">
                    <i class="fas fa-shield-alt"></i>
                    Provably Fair System
                </h3>
                
                <div class="fairness-info">
                    <h4 style="color: white; margin-bottom: 1rem; font-size: 1rem;">Current Roll Seeds</h4>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--craps-yellow);">Client Seed:</strong>
                        <div class="seed-display" id="clientSeed">--</div>
                    </div>
                    
                    <div style="margin-bottom: 0.75rem;">
                        <strong style="color: var(--craps-orange);">Server Seed (Hashed):</strong>
                        <div class="seed-display" id="hashedServerSeed">--</div>
                    </div>
                    
                    <div>
                        <strong style="color: var(--craps-green);">Nonce:</strong>
                        <div class="seed-display" id="nonce">0</div>
                    </div>
                </div>

                <div class="verification-tool">
                    <h4 style="color: white; margin-bottom: 1rem; font-size: 1rem;">
                        <i class="fas fa-check-circle"></i>
                        Verify Past Roll
                    </h4>
                    
                    <input type="text" class="verify-input" id="verifyClientSeed" placeholder="Enter client seed">
                    <input type="text" class="verify-input" id="verifyServerSeed" placeholder="Enter revealed server seed">
                    <input type="number" class="verify-input" id="verifyNonce" placeholder="Enter nonce">
                    
                    <button class="verify-btn" onclick="verifyResult()">
                        <i class="fas fa-check-circle"></i> Verify Result
                    </button>
                    
                    <div id="verificationResult" style="margin-top: 1rem; padding: 1rem; background: rgba(0,0,0,0.2); border-radius: 8px; display: none;">
                        <!-- Verification result will be displayed here -->
                    </div>
                </div>

                <!-- Detailed History (Pro View Enhancement) -->
                <div class="detailed-history">
                    <div class="history-container">
                        <h4 style="color: white; margin-bottom: 1rem; font-size: 1rem;">
                            <i class="fas fa-history"></i>
                            Roll History
                        </h4>
                        <div id="rollHistory">
                            <!-- Roll history will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div class="notification" id="notification"></div>

    <!-- Loading indicator for mobile -->
    <div id="loadingIndicator" style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        z-index: 9999;
        display: none;
    ">
        <i class="fas fa-spinner fa-spin"></i>
        Loading...
    </div>

    <!-- JavaScript Dependencies -->
    <script src="assets/js/script.js"></script>
    <script src="craps.js"></script>
    
    <!-- Additional mobile optimization script -->
    <script>
        // Mobile-specific optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent zoom on double tap for game elements
            const gameElements = document.querySelectorAll('.craps-table, .dice-area, .game-controls');
            gameElements.forEach(element => {
                element.addEventListener('touchend', function(e) {
                    e.preventDefault();
                }, { passive: false });
            });

            // Optimize for mobile viewport
            function adjustMobileViewport() {
                if (window.innerWidth <= 768) {
                    // Adjust viewport meta tag for better mobile experience
                    const viewport = document.querySelector('meta[name=viewport]');
                    if (viewport) {
                        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                    
                    // Add mobile-specific classes
                    document.body.classList.add('mobile-device');
                    
                    // Optimize dice size for small screens
                    if (window.innerWidth <= 480) {
                        const dice = document.querySelectorAll('.die');
                        dice.forEach(die => {
                            die.style.width = '45px';
                            die.style.height = '45px';
                            die.style.fontSize = '1.1rem';
                        });
                    }
                }
            }

            // Run on load and resize
            adjustMobileViewport();
            window.addEventListener('resize', adjustMobileViewport);

            // Service Worker for offline capability (optional)
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js').catch(err => {
                    console.log('ServiceWorker registration failed: ', err);
                });
            }

            // Preload critical images for better mobile performance
            const criticalImages = [
                'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png'
            ];
            
            criticalImages.forEach(src => {
                const img = new Image();
                img.src = src;
            });
        });

        // Performance monitoring for mobile
        window.addEventListener('load', function() {
            // Log performance metrics
            if (performance && performance.timing) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log('Page load time:', loadTime + 'ms');
                
                // Show warning if load time is too high on mobile
                if (loadTime > 3000 && window.innerWidth <= 768) {
                    console.warn('Slow loading detected on mobile device');
                }
            }
        });
    </script>
</body>
</html>