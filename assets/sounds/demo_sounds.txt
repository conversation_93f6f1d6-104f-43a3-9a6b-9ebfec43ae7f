# Sound Effects for Diamond Hunter

This directory is prepared for sound effects that would enhance the game experience:

## Required Sound Files:
- click.mp3 - Button/grid click sound
- diamond.mp3 - Diamond discovery sound (positive)
- bomb.mp3 - Bomb explosion sound (negative)
- cashout.mp3 - Successful cash out sound (win)

## Implementation:
The game JavaScript already includes code to play these sounds when:
- A player clicks a grid square
- A diamond is discovered
- A bomb is hit
- A successful cash out occurs

## Note:
Sound files are optional and the game works without them. Players can add their own audio files by placing them in this directory with the correct names.