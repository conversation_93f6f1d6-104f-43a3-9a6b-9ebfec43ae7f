<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 128 128">
  <defs>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0277bd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#039be5;stop-opacity:1" />
    </linearGradient>
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="2" dy="3" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.5" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect x="5" y="5" width="118" height="118" rx="10" ry="10" fill="url(#cardGradient)" filter="url(#dropShadow)" />

  <!-- Up arrow -->
  <path d="M45,75 L35,95 L55,95 Z" fill="#4caf50" stroke="white" stroke-width="2"/>
  
  <!-- Down arrow -->
  <path d="M85,55 L75,35 L95,35 Z" fill="#f44336" stroke="white" stroke-width="2"/>
  
  <!-- High card -->
  <g transform="translate(30, 35)">
    <rect x="0" y="0" width="20" height="28" rx="2" ry="2" fill="white" stroke="#455a64" stroke-width="1"/>
    <text x="3" y="12" font-family="Arial" font-weight="bold" font-size="10" fill="#f44336">A</text>
    <text x="3" y="25" font-family="Arial" font-size="14" fill="#f44336">♥</text>
  </g>
  
  <!-- Low card -->
  <g transform="translate(75, 65)">
    <rect x="0" y="0" width="20" height="28" rx="2" ry="2" fill="white" stroke="#455a64" stroke-width="1"/>
    <text x="3" y="12" font-family="Arial" font-weight="bold" font-size="10" fill="#212121">2</text>
    <text x="3" y="25" font-family="Arial" font-size="14" fill="#212121">♠</text>
  </g>
  
  <!-- HILO text -->
  <text x="64" y="118" font-family="Arial" font-weight="bold" font-size="16" fill="white" text-anchor="middle">HILO</text>
</svg>