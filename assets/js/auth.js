/**
 * Authentication Module for GoldenAura Gaming Platform
 * 
 * This module handles user authentication, registration, and related functionality.
 */

class AuthManager {
    constructor() {
        this.apiUrl = 'http://localhost:8000/api'; // Update this to your backend API URL
        this.token = localStorage.getItem('auth_token');
        this.user = JSON.parse(localStorage.getItem('user'));
        
        // Attach event listeners for auth forms
        this.attachEventListeners();
        
        // Update UI based on current auth state
        this.updateUI();
    }
    
    /**
     * Attach event listeners to authentication-related elements
     */
    attachEventListeners() {
        // Login button in header
        const loginBtn = document.querySelector('.btn-login');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => this.showLoginModal());
        }
        
        // Register button in header
        const registerBtn = document.querySelector('.btn-register');
        if (registerBtn) {
            registerBtn.addEventListener('click', () => this.showRegisterModal());
        }
        
        // Logout functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.logout-btn')) {
                this.logout();
            }
        });
        
        // Listen for auth modals in the DOM
        document.addEventListener('DOMContentLoaded', () => {
            // Login form submission
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.login({
                        login: document.getElementById('loginUsername').value,
                        password: document.getElementById('loginPassword').value
                    });
                });
            }
            
            // Register form submission
            const registerForm = document.getElementById('registerForm');
            if (registerForm) {
                registerForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.register({
                        name: document.getElementById('registerName').value,
                        email: document.getElementById('registerEmail').value,
                        username: document.getElementById('registerUsername').value,
                        password: document.getElementById('registerPassword').value,
                        password_confirmation: document.getElementById('registerPasswordConfirm').value
                    });
                });
            }
        });
    }
    
    /**
     * Show the login modal
     */
    showLoginModal() {
        // Create modal if it doesn't exist
        if (!document.getElementById('loginModal')) {
            const modalHtml = `
                <div id="loginModal" class="auth-modal">
                    <div class="auth-modal-content">
                        <span class="close-modal">&times;</span>
                        <h2>Login to GoldenAura</h2>
                        <form id="loginForm">
                            <div class="form-group">
                                <label for="loginUsername">Username or Email</label>
                                <input type="text" id="loginUsername" required>
                            </div>
                            <div class="form-group">
                                <label for="loginPassword">Password</label>
                                <input type="password" id="loginPassword" required>
                            </div>
                            <div class="form-error" id="loginError"></div>
                            <button type="submit" class="btn btn-primary">Login</button>
                        </form>
                        <div class="auth-footer">
                            <p>Don't have an account? <a href="#" id="showRegister">Register</a></p>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // Add event listeners
            document.querySelector('.close-modal').addEventListener('click', () => {
                document.getElementById('loginModal').style.display = 'none';
            });
            
            document.getElementById('showRegister').addEventListener('click', (e) => {
                e.preventDefault();
                document.getElementById('loginModal').style.display = 'none';
                this.showRegisterModal();
            });
            
            // Setup form submission
            document.getElementById('loginForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.login({
                    login: document.getElementById('loginUsername').value,
                    password: document.getElementById('loginPassword').value
                });
            });
        }
        
        // Show the modal
        document.getElementById('loginModal').style.display = 'block';
    }
    
    /**
     * Show the register modal
     */
    showRegisterModal() {
        // Create modal if it doesn't exist
        if (!document.getElementById('registerModal')) {
            const modalHtml = `
                <div id="registerModal" class="auth-modal">
                    <div class="auth-modal-content">
                        <span class="close-modal">&times;</span>
                        <h2>Create Your Account</h2>
                        <form id="registerForm">
                            <div class="form-group">
                                <label for="registerName">Full Name</label>
                                <input type="text" id="registerName" required>
                            </div>
                            <div class="form-group">
                                <label for="registerEmail">Email</label>
                                <input type="email" id="registerEmail" required>
                            </div>
                            <div class="form-group">
                                <label for="registerUsername">Username</label>
                                <input type="text" id="registerUsername" required>
                            </div>
                            <div class="form-group">
                                <label for="registerPassword">Password</label>
                                <input type="password" id="registerPassword" required>
                            </div>
                            <div class="form-group">
                                <label for="registerPasswordConfirm">Confirm Password</label>
                                <input type="password" id="registerPasswordConfirm" required>
                            </div>
                            <div class="form-group checkbox">
                                <input type="checkbox" id="termsAgreement" required>
                                <label for="termsAgreement">I agree to the <a href="terms.html" target="_blank">Terms of Service</a></label>
                            </div>
                            <div class="form-error" id="registerError"></div>
                            <button type="submit" class="btn btn-primary">Create Account</button>
                        </form>
                        <div class="auth-footer">
                            <p>Already have an account? <a href="#" id="showLogin">Login</a></p>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            // Add event listeners
            document.querySelector('#registerModal .close-modal').addEventListener('click', () => {
                document.getElementById('registerModal').style.display = 'none';
            });
            
            document.getElementById('showLogin').addEventListener('click', (e) => {
                e.preventDefault();
                document.getElementById('registerModal').style.display = 'none';
                this.showLoginModal();
            });
            
            // Setup form submission
            document.getElementById('registerForm').addEventListener('submit', (e) => {
                e.preventDefault();
                this.register({
                    name: document.getElementById('registerName').value,
                    email: document.getElementById('registerEmail').value,
                    username: document.getElementById('registerUsername').value,
                    password: document.getElementById('registerPassword').value,
                    password_confirmation: document.getElementById('registerPasswordConfirm').value
                });
            });
        }
        
        // Show the modal
        document.getElementById('registerModal').style.display = 'block';
    }
    
    /**
     * Login a user
     * @param {Object} credentials - User credentials
     */
    async login(credentials) {
        try {
            // First, get CSRF cookie
            await this.getCsrfCookie();
            
            const response = await fetch(`${this.apiUrl}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                credentials: 'include', // Important for cookies
                body: JSON.stringify(credentials)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Login failed');
            }
            
            // Store auth data
            this.token = data.token;
            this.user = data.user;
            localStorage.setItem('auth_token', this.token);
            localStorage.setItem('user', JSON.stringify(this.user));
            
            // Update UI
            this.updateUI();
            
            // Close modal
            document.getElementById('loginModal').style.display = 'none';
            
            // Show success notification
            this.showNotification('Login successful. Welcome back!', 'success');
        } catch (error) {
            console.error('Login error:', error);
            document.getElementById('loginError').textContent = error.message;
        }
    }
    
    /**
     * Register a new user
     * @param {Object} userData - User registration data
     */
    async register(userData) {
        try {
            // First, get CSRF cookie
            await this.getCsrfCookie();
            
            const response = await fetch(`${this.apiUrl}/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                credentials: 'include', // Important for cookies
                body: JSON.stringify(userData)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                const errorMessage = data.message || 'Registration failed';
                // Handle validation errors
                if (data.errors) {
                    const errorMessages = Object.values(data.errors).flat();
                    throw new Error(errorMessages.join('\n'));
                }
                throw new Error(errorMessage);
            }
            
            // Store auth data
            this.token = data.token;
            this.user = data.user;
            localStorage.setItem('auth_token', this.token);
            localStorage.setItem('user', JSON.stringify(this.user));
            
            // Update UI
            this.updateUI();
            
            // Close modal
            document.getElementById('registerModal').style.display = 'none';
            
            // Show success notification
            this.showNotification('Registration successful. Welcome to GoldenAura!', 'success');
        } catch (error) {
            console.error('Registration error:', error);
            document.getElementById('registerError').textContent = error.message;
        }
    }
    
    /**
     * Logout the current user
     */
    async logout() {
        try {
            if (this.token) {
                await fetch(`${this.apiUrl}/logout`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${this.token}`
                    },
                    credentials: 'include'
                });
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            // Clear auth data even if API call fails
            this.token = null;
            this.user = null;
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user');
            
            // Update UI
            this.updateUI();
            
            // Show notification
            this.showNotification('You have been logged out.', 'info');
            
            // Redirect to home page if on protected page
            const protectedPages = ['wallet.html', 'settings.html'];
            const currentPage = window.location.pathname.split('/').pop();
            if (protectedPages.includes(currentPage)) {
                window.location.href = 'index.html';
            }
        }
    }
    
    /**
     * Get CSRF cookie for CSRF protection
     */
    async getCsrfCookie() {
        try {
            await fetch(`${this.apiUrl.replace('/api', '')}/sanctum/csrf-cookie`, {
                method: 'GET',
                credentials: 'include',
            });
        } catch (error) {
            console.error('Failed to get CSRF cookie:', error);
        }
    }
    
    /**
     * Update UI based on authentication state
     */
    updateUI() {
        const headerRight = document.querySelector('.header-right');
        
        if (this.isAuthenticated()) {
            // Show authenticated UI
            if (headerRight) {
                headerRight.innerHTML = `
                    <div class="user-menu">
                        <div class="user-balance">
                            <i class="fas fa-coins"></i>
                            <span>${this.user.balance.toLocaleString()} GA</span>
                        </div>
                        <div class="user-avatar">
                            <img src="${this.user.avatar || 'assets/images/default-avatar.png'}" alt="${this.user.username}">
                            <div class="user-dropdown">
                                <div class="user-dropdown-header">
                                    <p>Welcome,</p>
                                    <h4>${this.user.username}</h4>
                                </div>
                                <ul class="user-dropdown-menu">
                                    <li><a href="wallet.html"><i class="fas fa-wallet"></i> Wallet</a></li>
                                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                                    <li><a href="#" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            }
        } else {
            // Show unauthenticated UI
            if (headerRight) {
                headerRight.innerHTML = `
                    <button class="btn btn-login">Login</button>
                    <button class="btn btn-register">Register</button>
                `;
                
                // Re-attach event listeners
                const loginBtn = headerRight.querySelector('.btn-login');
                if (loginBtn) {
                    loginBtn.addEventListener('click', () => this.showLoginModal());
                }
                
                const registerBtn = headerRight.querySelector('.btn-register');
                if (registerBtn) {
                    registerBtn.addEventListener('click', () => this.showRegisterModal());
                }
            }
        }
        
        // Update content visibility based on auth state
        this.updateContentVisibility();
    }
    
    /**
     * Update content visibility based on authentication state
     */
    updateContentVisibility() {
        const authRequiredElements = document.querySelectorAll('.auth-required');
        const noAuthElements = document.querySelectorAll('.no-auth');
        
        if (this.isAuthenticated()) {
            // Show elements that require authentication
            authRequiredElements.forEach(el => {
                el.style.display = '';
            });
            
            // Hide elements for non-authenticated users
            noAuthElements.forEach(el => {
                el.style.display = 'none';
            });
        } else {
            // Hide elements that require authentication
            authRequiredElements.forEach(el => {
                el.style.display = 'none';
            });
            
            // Show elements for non-authenticated users
            noAuthElements.forEach(el => {
                el.style.display = '';
            });
        }
    }
    
    /**
     * Check if user is authenticated
     * @returns {boolean} True if authenticated
     */
    isAuthenticated() {
        return !!(this.token && this.user);
    }
    
    /**
     * Get the current user
     * @returns {Object|null} User object or null
     */
    getUser() {
        return this.user;
    }
    
    /**
     * Get the authentication token
     * @returns {string|null} Auth token or null
     */
    getToken() {
        return this.token;
    }
    
    /**
     * Show a notification message
     * @param {string} message - Message to display
     * @param {string} type - Type of notification (success, error, info)
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <p>${message}</p>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Automatically remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('hide');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
    }
}

// Initialize authentication manager
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});