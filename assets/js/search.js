// Enhanced Search Page - Mobile-First Responsive Implementation
document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced Search Page Loading...");
    
    // Mobile detection and optimization
    const isMobile = window.innerWidth <= 767;
    const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
    const isDesktop = window.innerWidth >= 1024;
    
    // Touch and haptic support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const hasHaptic = 'vibrate' in navigator && isMobile;
    
    // Search state management
    const searchState = {
        query: '',
        activeTab: 'all',
        filters: {
            gameType: 'all',
            provider: 'all',
            category: 'all'
        },
        sortBy: 'relevance',
        results: {},
        suggestions: [],
        recentSearches: JSON.parse(localStorage.getItem('recentSearches')) || [],
        isSearching: false,
        hasSearched: false
    };
    
    // Cache DOM elements
    const elements = {
        searchInput: document.getElementById('pageSearchInput'),
        headerSearchInput: document.getElementById('searchInput'),
        clearButton: document.getElementById('clearSearch'),
        searchSuggestions: document.getElementById('searchSuggestions'),
        searchResults: document.getElementById('searchResults'),
        searchLoading: document.getElementById('searchLoading'),
        searchTabs: document.querySelectorAll('.search-tab'),
        resultsContent: document.querySelectorAll('.results-content'),
        filterButtons: document.querySelectorAll('.filter-button'),
        sortDropdown: document.getElementById('sortOptions'),
        recentSearches: document.querySelector('.recent-searches .search-pills'),
        trendingSearches: document.querySelector('.trending-searches .search-pills'),
        clearRecentBtn: document.getElementById('clearRecent'),
        noResults: document.getElementById('noResults')
    };
    
    // Sample data for search functionality
    const searchData = {
        games: [
            { id: 'crash', name: 'CRASH', icon: '🚀', players: 2805, url: 'crash.html', type: 'originals', provider: 'GoldenAura' },
            { id: 'mines', name: 'MINES', icon: '💣', players: 3597, url: 'mines.html', type: 'originals', provider: 'GoldenAura' },
            { id: 'dice', name: 'DICE', icon: '🎲', players: 4394, url: 'dice.html', type: 'table', provider: 'GoldenAura' },
            { id: 'limbo', name: 'LIMBO', icon: '📈', players: 3422, url: 'limbo.html', type: 'originals', provider: 'GoldenAura' },
            { id: 'plinko', name: 'PLINKO', icon: '🎯', players: 2811, url: 'plinko.html', type: 'arcade', provider: 'GoldenAura' },
            { id: 'slide', name: 'SLIDE', icon: '🎢', players: 353, url: 'slide.html', type: 'arcade', provider: 'GoldenAura' },
            { id: 'tome', name: 'TOME OF LIFE', icon: '📚', players: 219, url: 'tome.html', type: 'slots', provider: 'GoldenAura' },
            { id: 'blackjack', name: 'BLACKJACK', icon: '🃏', players: 1189, url: 'blackjack.html', type: 'table', provider: 'Evolution' },
            { id: 'roulette', name: 'ROULETTE', icon: '🎰', players: 2491, url: 'roulette.html', type: 'table', provider: 'Evolution' },
            { id: 'baccarat', name: 'BACCARAT', icon: '🎴', players: 567, url: 'baccarat.html', type: 'table', provider: 'Evolution' },
            { id: 'poker', name: 'POKER PALACE', icon: '♠️', players: 234, url: 'poker-palace.html', type: 'table', provider: 'GoldenAura' },
            { id: 'keno', name: 'KENO', icon: '🎱', players: 890, url: 'keno.html', type: 'lottery', provider: 'GoldenAura' },
            { id: 'video-poker', name: 'VIDEO POKER', icon: '🃏', players: 389, url: 'video-poker.html', type: 'video-poker', provider: 'NetEnt' },
            { id: 'wheel', name: 'WHEEL OF FORTUNE', icon: '🎡', players: 1245, url: 'wheel.html', type: 'arcade', provider: 'Pragmatic' }
        ],
        sports: [
            { id: 'soccer', name: 'Soccer', icon: '⚽', matches: 12, url: 'sports.html?sport=soccer' },
            { id: 'basketball', name: 'Basketball', icon: '🏀', matches: 8, url: 'sports.html?sport=basketball' },
            { id: 'tennis', name: 'Tennis', icon: '🎾', matches: 15, url: 'sports.html?sport=tennis' },
            { id: 'football', name: 'American Football', icon: '🏈', matches: 5, url: 'sports.html?sport=football' },
            { id: 'baseball', name: 'Baseball', icon: '⚾', matches: 7, url: 'sports.html?sport=baseball' },
            { id: 'hockey', name: 'Ice Hockey', icon: '🏒', matches: 4, url: 'sports.html?sport=hockey' }
        ],
        promotions: [
            { id: 'welcome', name: 'Welcome Bonus', description: '100% up to GA 10,000', type: 'bonus', url: 'promotions.html' },
            { id: 'crash-tournament', name: 'Crash Masters Tournament', description: 'Win up to GA 100,000', type: 'tournament', url: 'promotions.html' },
            { id: 'french-open', name: 'French Open Special', description: 'Free bet GA 500', type: 'sports', url: 'promotions.html' }
        ],
        providers: [
            { id: 'goldenaura', name: 'GoldenAura', games: 8 },
            { id: 'evolution', name: 'Evolution Gaming', games: 3 },
            { id: 'netent', name: 'NetEnt', games: 1 },
            { id: 'pragmatic', name: 'Pragmatic Play', games: 1 }
        ],
        support: [
            { id: 'crash-guide', title: 'How to play Crash game?', snippet: 'Learn how to play our popular Crash game...', category: 'Game Guide' },
            { id: 'deposits', title: 'Deposits and Withdrawals', snippet: 'Complete guide to making deposits and withdrawals...', category: 'Payments' },
            { id: 'security', title: 'Account Security', snippet: 'Protect your GoldenAura account with our security best practices...', category: 'Security' }
        ]
    };
    
    // Initialize search functionality
    init();
    
    function init() {
        console.log("Initializing Enhanced Search...");
        
        // Setup event listeners
        setupEventListeners();
        
        // Initialize mobile optimizations
        setupMobileOptimizations();
        
        // Load recent searches
        updateRecentSearches();
        
        // Setup trending searches
        setupTrendingSearches();
        
        // Set active nav item
        setActiveNavItem();
        
        console.log("Search page initialized successfully!");
    }
    
    function setupEventListeners() {
        // Search input events
        if (elements.searchInput) {
            elements.searchInput.addEventListener('input', debounce(handleSearchInput, 300));
            elements.searchInput.addEventListener('focus', showSuggestions);
            elements.searchInput.addEventListener('blur', hideSuggestions);
            elements.searchInput.addEventListener('keydown', handleSearchKeydown);
        }
        
        // Header search integration
        if (elements.headerSearchInput) {
            elements.headerSearchInput.addEventListener('focus', () => {
                elements.searchInput.focus();
            });
        }
        
        // Clear button
        if (elements.clearButton) {
            elements.clearButton.addEventListener('click', clearSearch);
        }
        
        // Search tabs
        elements.searchTabs.forEach(tab => {
            tab.addEventListener('click', () => switchTab(tab.dataset.tab));
        });
        
        // Filter buttons
        elements.filterButtons.forEach(button => {
            button.addEventListener('click', handleFilterClick);
        });
        
        // Sort dropdown
        if (elements.sortDropdown) {
            elements.sortDropdown.addEventListener('change', handleSortChange);
        }
        
        // Recent searches
        if (elements.clearRecentBtn) {
            elements.clearRecentBtn.addEventListener('click', clearRecentSearches);
        }
        
        // Search suggestions
        if (elements.searchSuggestions) {
            elements.searchSuggestions.addEventListener('click', handleSuggestionClick);
        }
        
        // Search pills (recent and trending)
        setupSearchPills();
        
        // Window resize
        window.addEventListener('resize', debounce(handleResize, 250));
        
        // Keyboard shortcuts
        document.addEventListener('keydown', handleGlobalKeydown);
    }
    
    function setupMobileOptimizations() {
        // Add device classes
        document.body.classList.toggle('mobile', isMobile);
        document.body.classList.toggle('tablet', isTablet);
        document.body.classList.toggle('desktop', isDesktop);
        document.body.classList.toggle('touch-device', hasTouch);
        
        // Mobile-specific touch interactions
        if (isMobile) {
            setupMobileTouchInteractions();
        }
        
        // Optimize viewport for mobile search
        if (isMobile) {
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }
        }
    }
    
    function setupMobileTouchInteractions() {
        // Enhanced touch feedback for interactive elements
        const touchElements = document.querySelectorAll(`
            .search-pill, .search-tab, .filter-button, .game-tile,
            .sport-result, .promo-result, .support-result, .suggestion-item
        `);
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.98)';
                element.style.transition = 'transform 0.1s ease';
                triggerHapticFeedback('light');
            }, { passive: true });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = 'transform 0.3s ease';
                }, 100);
            }, { passive: true });
        });
    }
    
    function handleSearchInput(e) {
        const query = e.target.value.trim();
        searchState.query = query;
        
        if (query.length > 0) {
            elements.clearButton.style.display = 'flex';
            performSearch(query);
            generateSuggestions(query);
        } else {
            elements.clearButton.style.display = 'none';
            hideSearchResults();
            hideSuggestions();
        }
    }
    
    function handleSearchKeydown(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            const query = e.target.value.trim();
            if (query) {
                executeSearch(query);
                hideSuggestions();
            }
        } else if (e.key === 'Escape') {
            hideSuggestions();
            elements.searchInput.blur();
        }
    }
    
    function performSearch(query) {
        if (query.length < 2) return;
        
        searchState.isSearching = true;
        showSearchResults();
        showLoading();
        
        // Simulate search delay for UX
        setTimeout(() => {
            const results = searchContent(query);
            searchState.results = results;
            displaySearchResults(results);
            hideLoading();
            searchState.isSearching = false;
            searchState.hasSearched = true;
            
            // Add to recent searches
            addToRecentSearches(query);
        }, 500);
    }
    
    function executeSearch(query) {
        performSearch(query);
        
        // Haptic feedback for mobile
        if (isMobile) {
            triggerHapticFeedback('medium');
        }
    }
    
    function searchContent(query) {
        const lowerQuery = query.toLowerCase();
        const results = {
            games: [],
            sports: [],
            promotions: [],
            providers: [],
            support: [],
            total: 0
        };
        
        // Search games
        results.games = searchData.games.filter(game => 
            game.name.toLowerCase().includes(lowerQuery) ||
            game.type.toLowerCase().includes(lowerQuery) ||
            game.provider.toLowerCase().includes(lowerQuery)
        );
        
        // Search sports
        results.sports = searchData.sports.filter(sport => 
            sport.name.toLowerCase().includes(lowerQuery)
        );
        
        // Search promotions
        results.promotions = searchData.promotions.filter(promo => 
            promo.name.toLowerCase().includes(lowerQuery) ||
            promo.description.toLowerCase().includes(lowerQuery) ||
            promo.type.toLowerCase().includes(lowerQuery)
        );
        
        // Search providers
        results.providers = searchData.providers.filter(provider => 
            provider.name.toLowerCase().includes(lowerQuery)
        );
        
        // Search support
        results.support = searchData.support.filter(item => 
            item.title.toLowerCase().includes(lowerQuery) ||
            item.snippet.toLowerCase().includes(lowerQuery) ||
            item.category.toLowerCase().includes(lowerQuery)
        );
        
        // Calculate total
        results.total = results.games.length + results.sports.length + 
                       results.promotions.length + results.providers.length + 
                       results.support.length;
        
        return results;
    }
    
    function displaySearchResults(results) {
        // Update tab counts
        updateTabCounts(results);
        
        // Update results based on active tab
        updateResultsDisplay(results);
        
        // Show no results if needed
        if (results.total === 0) {
            showNoResults();
        } else {
            hideNoResults();
        }
    }
    
    function updateTabCounts(results) {
        const counts = {
            all: results.total,
            games: results.games.length,
            sports: results.sports.length,
            promotions: results.promotions.length,
            providers: results.providers.length,
            support: results.support.length
        };
        
        elements.searchTabs.forEach(tab => {
            const tabType = tab.dataset.tab;
            const countElement = tab.querySelector('.search-tab-count');
            if (countElement && counts[tabType] !== undefined) {
                countElement.textContent = `(${counts[tabType]})`;
            }
        });
    }
    
    function updateResultsDisplay(results) {
        const activeTab = searchState.activeTab;
        
        // Hide all content
        elements.resultsContent.forEach(content => {
            content.classList.remove('active');
        });
        
        // Show active content
        const activeContent = document.getElementById(`${activeTab}-results`);
        if (activeContent) {
            activeContent.classList.add('active');
            
            // Populate content based on tab
            populateTabContent(activeTab, results);
        }
    }
    
    function populateTabContent(tab, results) {
        switch (tab) {
            case 'all':
                populateAllResults(results);
                break;
            case 'games':
                populateGamesResults(results.games);
                break;
            case 'sports':
                populateSportsResults(results.sports);
                break;
            case 'promotions':
                populatePromotionsResults(results.promotions);
                break;
            case 'providers':
                populateProvidersResults(results.providers);
                break;
            case 'support':
                populateSupportResults(results.support);
                break;
        }
    }
    
    function populateAllResults(results) {
        const container = document.getElementById('all-results');
        if (!container) return;
        
        let html = '';
        
        // Games section
        if (results.games.length > 0) {
            html += '<div class="results-section"><h2 class="results-section-title">Games</h2>';
            html += '<div class="game-results-grid">';
            results.games.slice(0, 6).forEach(game => {
                html += createGameTileHTML(game);
            });
            html += '</div></div>';
        }
        
        // Sports section
        if (results.sports.length > 0) {
            html += '<div class="results-section"><h2 class="results-section-title">Sports</h2>';
            results.sports.slice(0, 3).forEach(sport => {
                html += createSportResultHTML(sport);
            });
            html += '</div>';
        }
        
        // Promotions section
        if (results.promotions.length > 0) {
            html += '<div class="results-section"><h2 class="results-section-title">Promotions</h2>';
            results.promotions.forEach(promo => {
                html += createPromoResultHTML(promo);
            });
            html += '</div>';
        }
        
        container.innerHTML = html;
        
        // Add click handlers
        addResultClickHandlers();
    }
    
    function populateGamesResults(games) {
        const container = document.querySelector('#games-results .game-results-grid');
        if (!container) return;
        
        const html = games.map(game => createGameTileHTML(game)).join('');
        container.innerHTML = html;
        addResultClickHandlers();
    }
    
    function populateSportsResults(sports) {
        const container = document.getElementById('sports-results');
        if (!container) return;
        
        const html = sports.map(sport => createSportResultHTML(sport)).join('');
        container.innerHTML = html;
        addResultClickHandlers();
    }
    
    function populatePromotionsResults(promotions) {
        const container = document.getElementById('promotions-results');
        if (!container) return;
        
        const html = promotions.map(promo => createPromoResultHTML(promo)).join('');
        container.innerHTML = html;
        addResultClickHandlers();
    }
    
    function populateProvidersResults(providers) {
        const container = document.querySelector('#providers-results .provider-results');
        if (!container) return;
        
        const html = providers.map(provider => createProviderResultHTML(provider)).join('');
        container.innerHTML = html;
        addResultClickHandlers();
    }
    
    function populateSupportResults(supportItems) {
        const container = document.getElementById('support-results');
        if (!container) return;
        
        const html = supportItems.map(item => createSupportResultHTML(item)).join('');
        container.innerHTML = html;
        addResultClickHandlers();
    }
    
    function createGameTileHTML(game) {
        const gradient = getGameGradient(game.type);
        return `
            <div class="game-tile" style="background: ${gradient};" data-url="${game.url}">
                <div class="game-icon">${game.icon}</div>
                <h3>${game.name}</h3>
                <span class="player-count">${game.players.toLocaleString()} playing</span>
            </div>
        `;
    }
    
    function createSportResultHTML(sport) {
        return `
            <div class="sport-result" data-url="${sport.url}">
                <div class="sport-result-info">
                    <div class="sport-league">${sport.name}</div>
                    <div class="sport-teams">${sport.matches} live matches</div>
                    <div class="sport-time">
                        <i class="fas fa-clock sport-time-icon"></i>
                        <span>Available now</span>
                    </div>
                </div>
                <div class="sport-result-odds">
                    <div class="odd-box">
                        <div class="odd-team">Matches</div>
                        <div class="odd-value">${sport.matches}</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    function createPromoResultHTML(promo) {
        return `
            <div class="promo-result" data-url="${promo.url}">
                <div class="promo-result-content">
                    <div class="promo-info">
                        <h3 class="promo-title">${promo.name}</h3>
                        <p class="promo-description">${promo.description}</p>
                        <div class="promo-expiry">Limited time offer</div>
                    </div>
                    <button class="btn btn-register">Claim</button>
                </div>
            </div>
        `;
    }
    
    function createProviderResultHTML(provider) {
        return `
            <div class="provider-result" data-provider="${provider.id}">
                <div class="provider-logo">
                    <i class="fas fa-building"></i>
                </div>
                <h3 class="provider-name">${provider.name}</h3>
            </div>
        `;
    }
    
    function createSupportResultHTML(item) {
        return `
            <div class="support-result" data-support="${item.id}">
                <h3 class="support-title">
                    <i class="fas fa-question-circle support-icon"></i>
                    ${item.title}
                </h3>
                <p class="support-snippet">${item.snippet}</p>
                <span class="support-category">${item.category}</span>
            </div>
        `;
    }
    
    function getGameGradient(type) {
        const gradients = {
            'originals': 'linear-gradient(135deg, #2e2e2e, #1c1c1c)',
            'table': 'linear-gradient(135deg, #4a1e5a, #7c4dff)',
            'slots': 'linear-gradient(135deg, #4a148c, #6a1b9a)',
            'arcade': 'linear-gradient(135deg, #512da8, #673ab7)',
            'lottery': 'linear-gradient(135deg, #1a237e, #3949ab)',
            'video-poker': 'linear-gradient(135deg, #0d47a1, #1976d2)'
        };
        return gradients[type] || gradients.originals;
    }
    
    function addResultClickHandlers() {
        // Game tiles
        document.querySelectorAll('.game-tile[data-url]').forEach(tile => {
            tile.addEventListener('click', () => {
                triggerHapticFeedback('medium');
                window.location.href = tile.dataset.url;
            });
        });
        
        // Sport results
        document.querySelectorAll('.sport-result[data-url]').forEach(result => {
            result.addEventListener('click', () => {
                triggerHapticFeedback('medium');
                window.location.href = result.dataset.url;
            });
        });
        
        // Promo results
        document.querySelectorAll('.promo-result[data-url]').forEach(result => {
            result.addEventListener('click', () => {
                triggerHapticFeedback('medium');
                window.location.href = result.dataset.url;
            });
        });
    }
    
    function generateSuggestions(query) {
        if (query.length < 2) {
            hideSuggestions();
            return;
        }
        
        const suggestions = [];
        const lowerQuery = query.toLowerCase();
        
        // Add game suggestions
        searchData.games.forEach(game => {
            if (game.name.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'game',
                    text: game.name,
                    icon: 'fas fa-dice',
                    category: 'Game'
                });
            }
        });
        
        // Add sport suggestions
        searchData.sports.forEach(sport => {
            if (sport.name.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'sport',
                    text: sport.name,
                    icon: 'fas fa-futbol',
                    category: 'Sport'
                });
            }
        });
        
        // Add promotion suggestions
        searchData.promotions.forEach(promo => {
            if (promo.name.toLowerCase().includes(lowerQuery)) {
                suggestions.push({
                    type: 'promotion',
                    text: promo.name,
                    icon: 'fas fa-gift',
                    category: 'Promotion'
                });
            }
        });
        
        displaySuggestions(suggestions, query);
    }
    
    function displaySuggestions(suggestions, query) {
        if (suggestions.length === 0) {
            hideSuggestions();
            return;
        }
        
        let html = '';
        const categories = {};
        
        // Group by category
        suggestions.forEach(suggestion => {
            if (!categories[suggestion.category]) {
                categories[suggestion.category] = [];
            }
            categories[suggestion.category].push(suggestion);
        });
        
        // Generate HTML
        Object.keys(categories).forEach(category => {
            html += `<div class="suggestions-category">${category}s</div>`;
            categories[category].slice(0, 3).forEach(suggestion => {
                html += `
                    <div class="suggestion-item" data-text="${suggestion.text}">
                        <i class="${suggestion.icon} suggestion-icon"></i>
                        <span class="suggestion-text">${highlightQuery(suggestion.text, query)}</span>
                        <span class="suggestion-category">${suggestion.category}</span>
                    </div>
                `;
            });
        });
        
        elements.searchSuggestions.innerHTML = html;
        showSuggestions();
    }
    
    function highlightQuery(text, query) {
        const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
        return text.replace(regex, '<span class="highlight">$1</span>');
    }
    
    function escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    function showSuggestions() {
        elements.searchSuggestions.classList.add('active');
    }
    
    function hideSuggestions() {
        setTimeout(() => {
            elements.searchSuggestions.classList.remove('active');
        }, 150); // Delay to allow click events
    }
    
    function handleSuggestionClick(e) {
        const suggestionItem = e.target.closest('.suggestion-item');
        if (suggestionItem) {
            const text = suggestionItem.dataset.text;
            elements.searchInput.value = text;
            executeSearch(text);
            hideSuggestions();
            triggerHapticFeedback('medium');
        }
    }
    
    function switchTab(tabName) {
        searchState.activeTab = tabName;
        
        // Update tab states
        elements.searchTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update content
        if (searchState.hasSearched) {
            updateResultsDisplay(searchState.results);
        }
        
        // Haptic feedback
        triggerHapticFeedback('light');
    }
    
    function setupSearchPills() {
        // Recent search pills
        document.addEventListener('click', (e) => {
            if (e.target.closest('.search-pill')) {
                const pill = e.target.closest('.search-pill');
                const text = pill.querySelector('.search-pill-text').textContent;
                elements.searchInput.value = text;
                executeSearch(text);
                triggerHapticFeedback('medium');
            }
        });
    }
    
    function setupTrendingSearches() {
        const trendingTerms = ['Crash Game', 'Mines', 'French Open', 'NBA Finals', 'Welcome Bonus'];
        
        // Add click handlers for trending searches
        const trendingPills = document.querySelectorAll('.trending-pill');
        trendingPills.forEach((pill, index) => {
            pill.addEventListener('click', () => {
                const term = trendingTerms[index] || pill.querySelector('.search-pill-text').textContent;
                elements.searchInput.value = term;
                executeSearch(term);
                triggerHapticFeedback('medium');
            });
        });
    }
    
    function addToRecentSearches(query) {
        if (!query || query.length < 2) return;
        
        // Remove if already exists
        const index = searchState.recentSearches.indexOf(query);
        if (index > -1) {
            searchState.recentSearches.splice(index, 1);
        }
        
        // Add to beginning
        searchState.recentSearches.unshift(query);
        
        // Limit to 10 recent searches
        if (searchState.recentSearches.length > 10) {
            searchState.recentSearches = searchState.recentSearches.slice(0, 10);
        }
        
        // Save to localStorage
        localStorage.setItem('recentSearches', JSON.stringify(searchState.recentSearches));
        
        // Update display
        updateRecentSearches();
    }
    
    function updateRecentSearches() {
        if (!elements.recentSearches) return;
        
        const html = searchState.recentSearches.slice(0, 4).map(search => `
            <div class="search-pill">
                <span class="search-pill-text">${search}</span>
                <i class="fas fa-history search-pill-icon"></i>
            </div>
        `).join('');
        
        elements.recentSearches.innerHTML = html;
    }
    
    function clearRecentSearches() {
        searchState.recentSearches = [];
        localStorage.removeItem('recentSearches');
        updateRecentSearches();
        triggerHapticFeedback('light');
    }
    
    function clearSearch() {
        elements.searchInput.value = '';
        elements.clearButton.style.display = 'none';
        hideSearchResults();
        hideSuggestions();
        searchState.query = '';
        searchState.hasSearched = false;
        triggerHapticFeedback('light');
    }
    
    function showSearchResults() {
        elements.searchResults.classList.add('active');
        
        // Hide search page header sections on mobile when showing results
        if (isMobile) {
            document.querySelector('.recent-searches').style.display = 'none';
            document.querySelector('.trending-searches').style.display = 'none';
        }
    }
    
    function hideSearchResults() {
        elements.searchResults.classList.remove('active');
        
        // Show search page header sections
        if (isMobile) {
            document.querySelector('.recent-searches').style.display = 'block';
            document.querySelector('.trending-searches').style.display = 'block';
        }
    }
    
    function showLoading() {
        elements.searchLoading.classList.add('active');
    }
    
    function hideLoading() {
        elements.searchLoading.classList.remove('active');
    }
    
    function showNoResults() {
        if (elements.noResults) {
            elements.noResults.style.display = 'block';
            elements.resultsContent.forEach(content => {
                content.style.display = 'none';
            });
        }
    }
    
    function hideNoResults() {
        if (elements.noResults) {
            elements.noResults.style.display = 'none';
            elements.resultsContent.forEach(content => {
                content.style.display = 'block';
            });
        }
    }
    
    function setActiveNavItem() {
        // Set search nav item as active
        const searchNavItem = document.querySelector('.nav-item[href="search.html"]');
        if (searchNavItem) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            searchNavItem.classList.add('active');
        }
    }
    
    function handleFilterClick(e) {
        // Placeholder for filter functionality
        console.log('Filter clicked:', e.target.textContent);
        triggerHapticFeedback('light');
    }
    
    function handleSortChange(e) {
        searchState.sortBy = e.target.value;
        if (searchState.hasSearched) {
            // Re-sort and display results
            displaySearchResults(searchState.results);
        }
        triggerHapticFeedback('light');
    }
    
    function handleGlobalKeydown(e) {
        // Search shortcut: Ctrl/Cmd + K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            elements.searchInput.focus();
        }
        
        // Escape to clear search
        if (e.key === 'Escape' && elements.searchInput === document.activeElement) {
            clearSearch();
        }
    }
    
    function handleResize() {
        const newIsMobile = window.innerWidth <= 767;
        const newIsTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
        const newIsDesktop = window.innerWidth >= 1024;
        
        // Update device classes
        document.body.classList.toggle('mobile', newIsMobile);
        document.body.classList.toggle('tablet', newIsTablet);
        document.body.classList.toggle('desktop', newIsDesktop);
        
        // Re-setup mobile interactions if needed
        if (newIsMobile && !isMobile) {
            setupMobileTouchInteractions();
        }
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!hasHaptic || !isMobile) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Public API
    window.SearchPage = {
        search: executeSearch,
        clearSearch,
        switchTab,
        triggerHapticFeedback,
        state: () => ({ ...searchState })
    };
    
    console.log("Enhanced Search Page Ready! 🔍");
});