// Enhanced GoldenAura Casino - Engagement-Optimized Platform
document.addEventListener('DOMContentLoaded', function() {
    console.log("GoldenAura Casino - Engagement-Optimized Platform Loading...");
    
    // Mobile detection and optimization
    const isMobile = window.innerWidth <= 767;
    const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
    const isDesktop = window.innerWidth >= 1024;
    
    // Touch and haptic support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const hasHaptic = 'vibrate' in navigator && isMobile;
    
    // Cache DOM elements for performance
    const elements = {
        menuToggle: document.getElementById('menuToggle'),
        sidebar: document.getElementById('sidebar'),
        mainContent: document.getElementById('mainContent'),
        searchInput: document.getElementById('searchInput'),
        overlay: null, // Will be created dynamically
        navItems: document.querySelectorAll('.nav-item'),
        contentSections: document.querySelectorAll('.content-section'),
        gameTiles: document.querySelectorAll('.game-tile'),
        buttons: document.querySelectorAll('.btn'),
        header: document.querySelector('.header'),
        
        // New engagement elements
        welcomeBanner: document.getElementById('welcomeBanner'),
        closeBanner: document.getElementById('closeBanner'),
        liveTicker: document.getElementById('liveTicker'),
        totalPlayers: document.getElementById('totalPlayers'),
        totalWinnings: document.getElementById('totalWinnings'),
        supportFab: document.querySelector('.support-fab'),
        bonusFab: document.querySelector('.bonus-fab'),
        bonusClaimBtn: document.querySelector('.bonus-claim-btn'),
        featuredBtn: document.querySelector('.featured-btn')
    };
    
    // Enhanced state management with engagement tracking
    const state = {
        sidebarOpen: false,
        currentSection: 'casino',
        searchQuery: '',
        viewMode: localStorage.getItem('viewMode') || 'standard',
        lastTouchTime: 0,
        touchStartY: 0,
        scrollPosition: 0,
        isScrolling: false,
        
        // Engagement state
        bannerDismissed: localStorage.getItem('bannerDismissed') === 'true',
        sessionStartTime: Date.now(),
        totalInteractions: 0,
        lastTickerUpdate: 0,
        playerCount: 23456,
        totalWinningsAmount: 1234567,
        
        // Live data simulation
        winners: [
            { name: 'Player***23', amount: 2450, game: 'Mines', time: Date.now() },
            { name: 'Lucky***89', amount: 1890, game: 'Dice Rush', time: Date.now() - 60000 },
            { name: 'Winner***45', amount: 3200, game: 'Plinko', time: Date.now() - 120000 },
            { name: 'Gold***12', amount: 5600, game: 'Roulette', time: Date.now() - 180000 },
            { name: 'Pro***67', amount: 890, game: 'Blackjack', time: Date.now() - 240000 }
        ],
        currentWinnerIndex: 0
    };
    
    // Initialize the platform
    init();
    
    function init() {
        console.log("Initializing Engagement-Optimized Platform...");
        
        // Setup mobile optimizations
        setupMobileOptimizations();
        
        // Create overlay for mobile
        createOverlay();
        
        // Initialize engagement features
        initializeEngagementFeatures();
        
        // Start live data updates
        startLiveUpdates();
        
        // Setup event listeners
        setupEventListeners();
        
        // Initialize welcome flow
        initializeWelcomeFlow();
        
        // Setup responsive features
        setupResponsiveFeatures();
        
        // Setup touch interactions
        if (hasTouch) {
            setupTouchInteractions();
        }
        
        // Setup navigation
        setupNavigation();
        
        // Setup search functionality
        setupSearch();
        
        // Setup game tiles
        setupGameTiles();
        
        // Setup performance optimizations
        setupPerformanceOptimizations();
        
        // Apply initial view mode
        setViewMode(state.viewMode);
        
        console.log("Platform initialized successfully!");
    }
    
    function initializeEngagementFeatures() {
        // Show welcome banner if not dismissed
        if (!state.bannerDismissed && elements.welcomeBanner) {
            setTimeout(() => {
                elements.welcomeBanner.classList.add('show');
                trackEngagement('banner_shown');
            }, 2000);
        }
        
        // Setup banner close functionality
        if (elements.closeBanner) {
            elements.closeBanner.addEventListener('click', dismissBanner);
        }
        
        // Setup bonus claim button
        if (elements.bonusClaimBtn) {
            elements.bonusClaimBtn.addEventListener('click', () => {
                handleBonusClaim();
                trackEngagement('bonus_claim_attempt');
            });
        }
        
        // Setup featured tournament button
        if (elements.featuredBtn) {
            elements.featuredBtn.addEventListener('click', () => {
                handleTournamentJoin();
                trackEngagement('tournament_join_attempt');
            });
        }
        
        // Setup floating action buttons
        if (elements.supportFab) {
            elements.supportFab.addEventListener('click', () => {
                openLiveSupport();
                trackEngagement('support_clicked');
            });
        }
        
        if (elements.bonusFab) {
            elements.bonusFab.addEventListener('click', () => {
                showBonusModal();
                trackEngagement('bonus_fab_clicked');
            });
        }
        
        // Initialize live stats animation
        animateLiveStats();
    }
    
    function startLiveUpdates() {
        // Update live ticker every 8 seconds
        setInterval(updateLiveTicker, 8000);
        
        // Update player counts every 5 seconds
        setInterval(updatePlayerCounts, 5000);
        
        // Update total stats every 10 seconds
        setInterval(updateTotalStats, 10000);
        
        // Add new winners every 15 seconds
        setInterval(addNewWinner, 15000);
        
        // Initial updates
        updateLiveTicker();
        updatePlayerCounts();
    }
    
    function updateLiveTicker() {
        if (!elements.liveTicker) return;
        
        const winner = state.winners[state.currentWinnerIndex];
        const tickerContent = elements.liveTicker.querySelector('.ticker-content');
        
        if (tickerContent) {
            // Add slide out animation
            tickerContent.style.transform = 'translateX(-100%)';
            tickerContent.style.opacity = '0';
            
            setTimeout(() => {
                tickerContent.innerHTML = `
                    <div class="ticker-item">
                        <span class="winner-name">${winner.name}</span> just won 
                        <span class="win-amount">$${winner.amount.toLocaleString()}</span> 
                        on ${winner.game}! 🎉
                    </div>
                `;
                
                // Slide in animation
                tickerContent.style.transform = 'translateX(0)';
                tickerContent.style.opacity = '1';
            }, 300);
        }
        
        state.currentWinnerIndex = (state.currentWinnerIndex + 1) % state.winners.length;
        state.lastTickerUpdate = Date.now();
    }
    
    function updatePlayerCounts() {
        // Simulate realistic player count fluctuations
        elements.gameTiles.forEach(tile => {
            const playerCountElement = tile.querySelector('.player-count');
            if (playerCountElement) {
                const currentCount = parseInt(playerCountElement.textContent.replace(/[^\d]/g, ''));
                const fluctuation = Math.floor(Math.random() * 20) - 10; // ±10 players
                const newCount = Math.max(0, currentCount + fluctuation);
                
                animateCountChange(playerCountElement, newCount);
            }
        });
    }
    
    function updateTotalStats() {
        // Update total players with slight increases
        state.playerCount += Math.floor(Math.random() * 50) - 20; // ±20 players
        state.playerCount = Math.max(20000, state.playerCount);
        
        // Update total winnings
        state.totalWinningsAmount += Math.floor(Math.random() * 10000);
        
        if (elements.totalPlayers) {
            animateCountChange(elements.totalPlayers, state.playerCount);
        }
        
        if (elements.totalWinnings) {
            const formatted = `$${(state.totalWinningsAmount / 1000000).toFixed(1)}M`;
            animateCountChange(elements.totalWinnings, formatted, false);
        }
    }
    
    function addNewWinner() {
        const games = ['Mines', 'Dice Rush', 'Plinko', 'Blackjack', 'Roulette', 'Keno'];
        const names = ['Player***', 'Lucky***', 'Winner***', 'Gold***', 'Pro***', 'Epic***'];
        
        const newWinner = {
            name: names[Math.floor(Math.random() * names.length)] + Math.floor(Math.random() * 99),
            amount: Math.floor(Math.random() * 5000) + 100,
            game: games[Math.floor(Math.random() * games.length)],
            time: Date.now()
        };
        
        state.winners.unshift(newWinner);
        state.winners = state.winners.slice(0, 10); // Keep only last 10 winners
    }
    
    function animateCountChange(element, newValue, isNumber = true) {
        if (!element) return;
        
        element.style.transform = 'scale(1.1)';
        element.style.color = '#ffd700';
        
        setTimeout(() => {
            if (isNumber && typeof newValue === 'number') {
                element.textContent = newValue.toLocaleString() + (element.textContent.includes('playing') ? ' playing' : '');
            } else {
                element.textContent = newValue;
            }
            
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 200);
    }
    
    function animateLiveStats() {
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.animation = 'fadeIn 0.5s ease-out';
            }, index * 200);
        });
    }
    
    function dismissBanner() {
        if (elements.welcomeBanner) {
            elements.welcomeBanner.classList.remove('show');
            localStorage.setItem('bannerDismissed', 'true');
            state.bannerDismissed = true;
            trackEngagement('banner_dismissed');
        }
    }
    
    function handleBonusClaim() {
        // Show exciting bonus claim animation
        showBonusAnimation();
        
        // Simulate bonus claim process
        setTimeout(() => {
            showSuccessMessage('Welcome Bonus Claimed! Check your wallet for $1,000 + 50 Free Spins!');
            dismissBanner();
        }, 2000);
        
        triggerHapticFeedback('success');
    }
    
    function handleTournamentJoin() {
        showSuccessMessage('Joining Mega Mines Tournament... Good luck!');
        
        // Simulate tournament join
        setTimeout(() => {
            window.location.href = 'mines.html?tournament=mega';
        }, 1500);
        
        triggerHapticFeedback('medium');
    }
    
    function openLiveSupport() {
        // Simulate opening live support
        showSuccessMessage('Connecting to live support... Average wait time: 30 seconds');
        
        setTimeout(() => {
            window.location.href = 'support.html';
        }, 1000);
        
        triggerHapticFeedback('light');
    }
    
    function showBonusModal() {
        // Simulate bonus modal
        showSuccessMessage('Check out our latest promotions!');
        
        setTimeout(() => {
            window.location.href = 'promotions.html';
        }, 1000);
        
        triggerHapticFeedback('medium');
    }
    
    function showBonusAnimation() {
        // Create floating bonus animation
        const bonus = document.createElement('div');
        bonus.className = 'floating-bonus';
        bonus.innerHTML = '💰 +$1,000';
        bonus.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            font-weight: bold;
            color: #ffd700;
            z-index: 10000;
            animation: bonusFloat 2s ease-out forwards;
            pointer-events: none;
        `;
        
        document.body.appendChild(bonus);
        
        setTimeout(() => {
            bonus.remove();
        }, 2000);
    }
    
    function showSuccessMessage(message) {
        const toast = document.createElement('div');
        toast.className = 'success-toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
            animation: toastSlideIn 0.5s ease-out;
            max-width: 90%;
            text-align: center;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'toastSlideOut 0.5s ease-in forwards';
            setTimeout(() => toast.remove(), 500);
        }, 4000);
    }
    
    function trackEngagement(action) {
        state.totalInteractions++;
        const sessionTime = Date.now() - state.sessionStartTime;
        
        console.log(`Engagement: ${action} | Session: ${Math.floor(sessionTime / 1000)}s | Interactions: ${state.totalInteractions}`);
        
        // In a real implementation, this would send data to analytics
        // analytics.track(action, { sessionTime, totalInteractions: state.totalInteractions });
    }
    
    function initializeWelcomeFlow() {
        // Implement progressive disclosure for new users
        const isNewUser = !localStorage.getItem('hasVisited');
        
        if (isNewUser) {
            // Mark as visited
            localStorage.setItem('hasVisited', 'true');
            
            // Show welcome tour after 5 seconds
            setTimeout(() => {
                showWelcomeTour();
            }, 5000);
        }
    }
    
    function showWelcomeTour() {
        // Simple welcome tour highlighting key features
        const tour = document.createElement('div');
        tour.className = 'welcome-tour';
        tour.innerHTML = `
            <div class="tour-content">
                <h3>Welcome to GoldenAura! 🎉</h3>
                <p>Discover amazing games, claim bonuses, and win big!</p>
                <button class="tour-btn" onclick="this.parentElement.parentElement.remove()">
                    Let's Play!
                </button>
            </div>
        `;
        tour.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10001;
            animation: fadeIn 0.5s ease-out;
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            .tour-content {
                background: linear-gradient(135deg, #1e1e1e, #2d2d2d);
                padding: 32px;
                border-radius: 20px;
                text-align: center;
                max-width: 90%;
                border: 2px solid #ffd700;
                box-shadow: 0 20px 60px rgba(255, 215, 0, 0.3);
            }
            .tour-content h3 {
                color: #ffd700;
                margin-bottom: 16px;
                font-size: 24px;
            }
            .tour-content p {
                color: #f5f5f5;
                margin-bottom: 24px;
                line-height: 1.6;
            }
            .tour-btn {
                background: linear-gradient(135deg, #ffd700, #f8c400);
                color: #000;
                border: none;
                padding: 12px 32px;
                border-radius: 25px;
                font-weight: 700;
                font-size: 16px;
                cursor: pointer;
                transition: transform 0.3s ease;
            }
            .tour-btn:hover {
                transform: scale(1.05);
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(tour);
        trackEngagement('welcome_tour_shown');
    }
    
    function setupMobileOptimizations() {
        // Add mobile class to body
        document.body.classList.toggle('mobile', isMobile);
        document.body.classList.toggle('tablet', isTablet);
        document.body.classList.toggle('desktop', isDesktop);
        document.body.classList.toggle('touch-device', hasTouch);
        
        // Set viewport meta tag for better mobile experience
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport && isMobile) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        }
        
        // Prevent zoom on double tap for game elements
        if (isMobile) {
            document.addEventListener('touchend', function(e) {
                const now = Date.now();
                if (now - state.lastTouchTime <= 300) {
                    e.preventDefault();
                }
                state.lastTouchTime = now;
            });
        }
        
        // Add CSS custom properties for device info
        document.documentElement.style.setProperty('--device-width', window.innerWidth + 'px');
        document.documentElement.style.setProperty('--device-height', window.innerHeight + 'px');
        document.documentElement.style.setProperty('--is-mobile', isMobile ? '1' : '0');
    }
    
    function createOverlay() {
        if (!elements.overlay) {
            elements.overlay = document.createElement('div');
            elements.overlay.className = 'sidebar-overlay';
            elements.overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 998;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                backdrop-filter: blur(2px);
            `;
            document.body.appendChild(elements.overlay);
            
            elements.overlay.addEventListener('click', closeSidebar);
        }
    }
    
    function setupEventListeners() {
        // Enhanced menu toggle with animation
        if (elements.menuToggle) {
            elements.menuToggle.addEventListener('click', toggleSidebar);
        }
        
        // Enhanced button interactions
        elements.buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Add ripple effect
                createRippleEffect(this, e);
                trackEngagement('button_clicked');
                triggerHapticFeedback('light');
            });
        });
        
        // Enhanced game tile interactions
        elements.gameTiles.forEach(tile => {
            tile.addEventListener('click', function() {
                trackEngagement('game_selected');
                triggerHapticFeedback('medium');
                
                // Add selection animation
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
            
            // Add hover effects for desktop
            if (!isMobile) {
                tile.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                    this.style.boxShadow = '0 12px 40px rgba(0, 0, 0, 0.3)';
                });
                
                tile.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            }
        });
        
        // Window resize handler
        window.addEventListener('resize', debounce(handleResize, 250));
        
        // Scroll handler for header effects
        window.addEventListener('scroll', debounce(handleScroll, 16));
    }
    
    function setupResponsiveFeatures() {
        // Dynamic font sizing based on screen size
        const baseFontSize = isMobile ? 14 : isTablet ? 15 : 16;
        document.documentElement.style.fontSize = baseFontSize + 'px';
        
        // Adjust touch targets for mobile
        if (isMobile) {
            const touchElements = document.querySelectorAll('.btn, .nav-item, .game-tile');
            touchElements.forEach(element => {
                const currentHeight = element.offsetHeight;
                if (currentHeight < 44) {
                    element.style.minHeight = '44px';
                    element.style.display = 'flex';
                    element.style.alignItems = 'center';
                    element.style.justifyContent = 'center';
                }
            });
        }
    }
    
    function setupTouchInteractions() {
        // Enhanced touch feedback
        const touchElements = document.querySelectorAll('.btn, .nav-item, .game-tile');
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', function(e) {
                this.style.transform = 'scale(0.98)';
                this.style.transition = 'transform 0.1s ease';
            }, { passive: true });
            
            element.addEventListener('touchend', function(e) {
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.transition = 'transform 0.3s ease';
                }, 100);
            }, { passive: true });
        });
        
        // Swipe gesture for sidebar
        let touchStartX = 0;
        let touchStartY = 0;
        
        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchmove', function(e) {
            if (!touchStartX || !touchStartY) return;
            
            const touchEndX = e.touches[0].clientX;
            const touchEndY = e.touches[0].clientY;
            
            const diffX = touchStartX - touchEndX;
            const diffY = touchStartY - touchEndY;
            
            // Detect horizontal swipe
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0 && state.sidebarOpen) {
                    // Swipe left to close sidebar
                    closeSidebar();
                } else if (diffX < 0 && !state.sidebarOpen && touchStartX < 50) {
                    // Swipe right from edge to open sidebar
                    openSidebar();
                }
            }
        }, { passive: true });
        
        document.addEventListener('touchend', function() {
            touchStartX = 0;
            touchStartY = 0;
        }, { passive: true });
    }
    
    function setupNavigation() {
        elements.navItems.forEach(navItem => {
            navItem.addEventListener('click', function(e) {
                e.preventDefault();
                
                const section = this.dataset.section;
                const href = this.getAttribute('href');
                
                if (section) {
                    switchSection(section);
                } else if (href && href.includes('.html')) {
                    // Handle external links
                    window.location.href = href;
                }
                
                // Close sidebar on mobile after navigation
                if (isMobile) {
                    setTimeout(closeSidebar, 300);
                }
                
                trackEngagement('navigation_used');
            });
        });
    }
    
    function setupSearch() {
        if (!elements.searchInput) return;
        
        let searchTimeout;
        elements.searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            const query = e.target.value.toLowerCase().trim();
            
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });
        
        elements.searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch(this.value.toLowerCase().trim());
            }
        });
    }
    
    function setupGameTiles() {
        // Add intersection observer for game tile animations
        const gameObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
                    entry.target.style.animationDelay = Math.random() * 0.3 + 's';
                }
            });
        }, { threshold: 0.1 });
        
        elements.gameTiles.forEach(tile => {
            gameObserver.observe(tile);
        });
    }
    
    function setupPerformanceOptimizations() {
        // Lazy load images
        const images = document.querySelectorAll('img');
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });
        
        images.forEach(img => {
            if (img.dataset.src) {
                imageObserver.observe(img);
            }
        });
        
        // Preload critical game assets
        const criticalGames = ['mines.html', 'dice.html', 'limbo.html'];
        criticalGames.forEach(game => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = game;
            document.head.appendChild(link);
        });
    }
    
    function toggleSidebar() {
        if (state.sidebarOpen) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }
    
    function openSidebar() {
        if (elements.sidebar && elements.overlay) {
            elements.sidebar.classList.add('active');
            elements.overlay.style.opacity = '1';
            elements.overlay.style.visibility = 'visible';
            state.sidebarOpen = true;
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
            
            triggerHapticFeedback('medium');
            trackEngagement('sidebar_opened');
        }
    }
    
    function closeSidebar() {
        if (elements.sidebar && elements.overlay) {
            elements.sidebar.classList.remove('active');
            elements.overlay.style.opacity = '0';
            elements.overlay.style.visibility = 'hidden';
            state.sidebarOpen = false;
            
            // Restore body scroll
            document.body.style.overflow = '';
            
            triggerHapticFeedback('light');
        }
    }
    
    function switchSection(sectionName) {
        // Remove active class from all nav items
        elements.navItems.forEach(item => item.classList.remove('active'));
        
        // Add active class to clicked item
        const activeItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
        
        // Switch content sections
        elements.contentSections.forEach(section => {
            section.classList.remove('active');
        });
        
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        state.currentSection = sectionName;
        trackEngagement(`section_${sectionName}_viewed`);
    }
    
    function performSearch(query) {
        if (!query) {
            // Reset all game tiles visibility
            elements.gameTiles.forEach(tile => {
                tile.style.display = '';
            });
            return;
        }
        
        state.searchQuery = query;
        let matchCount = 0;
        
        elements.gameTiles.forEach(tile => {
            const title = tile.querySelector('h3').textContent.toLowerCase();
            const isMatch = title.includes(query);
            
            tile.style.display = isMatch ? '' : 'none';
            if (isMatch) {
                matchCount++;
                // Highlight matching tiles
                tile.style.animation = 'searchHighlight 0.5s ease-out';
            }
        });
        
        trackEngagement('search_performed');
        console.log(`Search: "${query}" - ${matchCount} matches found`);
    }
    
    function setViewMode(mode) {
        state.viewMode = mode;
        localStorage.setItem('viewMode', mode);
        
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
        } else {
            document.body.classList.add('pro-view-active');
        }
        
        trackEngagement(`view_mode_${mode}`);
    }
    
    function createRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = (event.clientX || event.touches[0].clientX) - rect.left - size / 2;
        const y = (event.clientY || event.touches[0].clientY) - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            left: ${x}px;
            top: ${y}px;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!hasHaptic) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function handleResize() {
        // Update device detection
        const newIsMobile = window.innerWidth <= 767;
        const newIsTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
        const newIsDesktop = window.innerWidth >= 1024;
        
        // Update body classes
        document.body.classList.toggle('mobile', newIsMobile);
        document.body.classList.toggle('tablet', newIsTablet);
        document.body.classList.toggle('desktop', newIsDesktop);
        
        // Close sidebar if switching to desktop
        if (newIsDesktop && state.sidebarOpen) {
            closeSidebar();
        }
        
        // Update CSS custom properties
        document.documentElement.style.setProperty('--device-width', window.innerWidth + 'px');
        document.documentElement.style.setProperty('--device-height', window.innerHeight + 'px');
        
        trackEngagement('window_resized');
    }
    
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add header shadow on scroll
        if (elements.header) {
            if (scrollTop > 10) {
                elements.header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.3)';
                elements.header.style.backdropFilter = 'blur(20px)';
            } else {
                elements.header.style.boxShadow = '';
                elements.header.style.backdropFilter = 'blur(10px)';
            }
        }
        
        state.scrollPosition = scrollTop;
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Add required CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes ripple {
            to { transform: scale(2); opacity: 0; }
        }
        
        @keyframes searchHighlight {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
        }
        
        @keyframes bonusFloat {
            0% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; }
            100% { transform: translate(-50%, -100px) scale(1); opacity: 0; }
        }
        
        @keyframes toastSlideIn {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
        
        @keyframes toastSlideOut {
            from { transform: translateX(-50%) translateY(0); opacity: 1; }
            to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
    
    // Public API
    window.GoldenAura = {
        openSidebar,
        closeSidebar,
        switchSection,
        performSearch,
        setViewMode,
        triggerHapticFeedback,
        trackEngagement,
        state: () => ({ ...state })
    };
    
    console.log("🎰 GoldenAura Casino - Engagement Platform Ready! 🎉");
});