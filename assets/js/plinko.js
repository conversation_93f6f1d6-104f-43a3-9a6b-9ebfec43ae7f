// Plinko Game JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Game elements
    const canvas = document.getElementById('plinkoCanvas');
    const ctx = canvas.getContext('2d');
    const dropZone = document.getElementById('dropZone');
    const slotZone = document.getElementById('slotZone');
    const balanceValue = document.getElementById('balanceValue');
    const mobileBalanceValue = document.getElementById('mobileBalanceValue');
    const mobileLastWin = document.getElementById('mobileLastWin');
    const betAmountInput = document.getElementById('betAmount');
    const decreaseBetBtn = document.getElementById('decreaseBet');
    const increaseBetBtn = document.getElementById('increaseBet');
    const scoreValue = document.getElementById('scoreValue');
    const ballsDropped = document.getElementById('ballsDropped');
    const lastPoints = document.getElementById('lastPoints');
    const bestPoints = document.getElementById('bestPoints');
    const totalWagered = document.getElementById('totalWagered');
    const winRate = document.getElementById('winRate');
    const profitLoss = document.getElementById('profitLoss');
    const dropBallBtn = document.getElementById('dropBallBtn');
    const mobileDropBallBtn = document.getElementById('mobileDropBallBtn');
    const resetBtn = document.getElementById('resetBtn');
    const boardSizeSelect = document.getElementById('boardSize');
    const riskLevelSelect = document.getElementById('riskLevel');
    const autoDropCheckbox = document.getElementById('autoDrop');
    const autoDropCountInput = document.getElementById('autoDropCount');
    const decreaseCountBtn = document.getElementById('decreaseCount');
    const increaseCountBtn = document.getElementById('increaseCount');
    const winLossIndicator = document.getElementById('winLossIndicator');
    const headerWinLossIndicator = document.getElementById('headerWinLossIndicator');
    const gameStatus = document.getElementById('gameStatus');
    const autoDropStatus = document.getElementById('autoDropStatus');
    const autoDropRemaining = document.getElementById('autoDropRemaining');
    const autoDropProgressFill = document.getElementById('autoDropProgressFill');
    const slotStatsTableBody = document.getElementById('slotStatsTableBody');
    const historyTableBody = document.getElementById('historyTableBody');
    const historyPlaceholder = document.getElementById('historyPlaceholder');
    const standardViewBtn = document.getElementById('standardView');
    const proViewBtn = document.getElementById('proView');
    const soundToggleBtn = document.getElementById('soundToggle');
    const mobileSoundBtn = document.getElementById('mobileSoundBtn');
    const mobileAutoBallBtn = document.getElementById('mobileAutoBallBtn');
    const mobileBetBtn = document.getElementById('mobileBetBtn');
    const mobileBoardBtn = document.getElementById('mobileBoardBtn');
    
    // Modal elements
    const betSettingsModal = document.getElementById('betSettingsModal');
    const boardSettingsModal = document.getElementById('boardSettingsModal');
    const rulesModal = document.getElementById('rulesModal');
    const modalBetAmount = document.getElementById('modalBetAmount');
    const modalDecreaseBet = document.getElementById('modalDecreaseBet');
    const modalIncreaseBet = document.getElementById('modalIncreaseBet');
    const modalAutoDropCount = document.getElementById('modalAutoDropCount');
    const modalDecreaseCount = document.getElementById('modalDecreaseCount');
    const modalIncreaseCount = document.getElementById('modalIncreaseCount');
    const modalAutoDrop = document.getElementById('modalAutoDrop');
    const modalApplyBtn = document.getElementById('modalApplyBtn');
    const modalBoardSize = document.getElementById('modalBoardSize');
    const modalRiskLevel = document.getElementById('modalRiskLevel');
    const modalApplyBoardBtn = document.getElementById('modalApplyBoardBtn');
    const closeBtns = document.querySelectorAll('.close-modal');
    
    // Toggle buttons
    const statsToggle = document.getElementById('statsToggle');
    const slotStatsToggle = document.getElementById('slotStatsToggle');
    const historyToggle = document.getElementById('historyToggle');
    
    // Audio elements
    const pegHitSound = document.getElementById('pegHitSound');
    const slotSound = document.getElementById('slotSound');
    const winSound = document.getElementById('winSound');
    const loseSound = document.getElementById('loseSound');
    const clickSound = document.getElementById('clickSound');
    
    // Preset buttons
    const betPresetBtns = document.querySelectorAll('.preset-btn');
    const dropPresetBtns = document.querySelectorAll('.drop-preset-btn');
    const modalPresetBtns = document.querySelectorAll('.modal-preset-btn');
    
    // Game configuration
    const BOARD_SIZES = {
        small: { rows: 8, startSlots: 5, endSlots: 6 },
        medium: { rows: 10, startSlots: 5, endSlots: 8 },
        large: { rows: 12, startSlots: 7, endSlots: 10 },
        extreme: { rows: 14, startSlots: 9, endSlots: 12 }
    };
    
    const RISK_LEVELS = {
        low: { winningSlots: 5, maxMultiplier: 150 },
        medium: { winningSlots: 3, maxMultiplier: 250 },
        high: { winningSlots: 2, maxMultiplier: 500 }
    };
    
    // Default configuration
    let PLINKO_CONFIG = {
        ROWS: 10,
        START_SLOTS: 5,
        END_SLOTS: 8,
        BALL_RADIUS: 8,
        GRAVITY: 0.5,
        ELASTICITY: 0.6,
        PEG_RADIUS: 6,
        DEFLECTION_FORCE: 5.5,
        BOARD_WIDTH: 600,
        BOARD_HEIGHT: 400,
        WINNING_SLOTS: 3,
        MAX_MULTIPLIER: 250
    };
    
    // Game state
    let gameState = {
        score: 0,
        ballsDropped: 0,
        lastScore: 0,
        bestScore: 0,
        selectedSlot: 2, // Default middle slot
        balls: [],
        pegs: [],
        rewards: [], // Base rewards
        activeRewards: [], // Current active rewards (including zeros)
        isDropping: false,
        animationId: null,
        balance: 1000, // Default starting balance
        betAmount: 10, // Default bet amount
        autoDropActive: false,
        autoDropCount: 5,
        autoDropInterval: null,
        autoDropRemaining: 0,
        soundEnabled: true,
        viewMode: 'standard',
        boardSize: 'medium',
        riskLevel: 'medium',
        
        // Statistics
        statistics: {
            totalGames: 0,
            gamesWon: 0,
            totalWagered: 0,
            totalProfit: 0,
            bestWin: 0,
            slotStats: {}
        },
        
        // Game history
        gameHistory: []
    };
    
    // Chart instance
    let slotStatsChart = null;
    
    // Initialize the game
    function initGame() {
        // Set canvas size
        resizeCanvas();
        
        // Load state from localStorage
        loadGameState();
        
        // Setup initial board
        updateBoardConfig();
        resetBoard();
        
        // Generate drop zone slots
        generateDropZone();
        
        // Setup event listeners
        setupEventListeners();
        
        // Update displays
        updateBalanceDisplay();
        updateStatsDisplay();
        updateHistoryDisplay();
        updateAutoDropDisplay();
        
        // Initialize chart if in Pro View
        if (gameState.viewMode === 'pro') {
            initSlotStatsChart();
        }
    }
    
    // Resize canvas based on container size
    function resizeCanvas() {
        const container = canvas.parentElement;
        const containerWidth = container.clientWidth - 20; // Account for padding
        
        // Set board width based on container
        PLINKO_CONFIG.BOARD_WIDTH = Math.min(600, containerWidth);
        
        // Set board height proportionally
        const aspectRatio = 3/4; // Height is 3/4 of width
        PLINKO_CONFIG.BOARD_HEIGHT = PLINKO_CONFIG.BOARD_WIDTH * aspectRatio;
        
        // Set canvas dimensions
        canvas.width = PLINKO_CONFIG.BOARD_WIDTH;
        canvas.height = PLINKO_CONFIG.BOARD_HEIGHT;
    }
    
    // Update board configuration based on selected size and risk level
    function updateBoardConfig() {
        const sizeConfig = BOARD_SIZES[gameState.boardSize];
        const riskConfig = RISK_LEVELS[gameState.riskLevel];
        
        PLINKO_CONFIG.ROWS = sizeConfig.rows;
        PLINKO_CONFIG.START_SLOTS = sizeConfig.startSlots;
        PLINKO_CONFIG.END_SLOTS = sizeConfig.endSlots;
        PLINKO_CONFIG.WINNING_SLOTS = riskConfig.winningSlots;
        PLINKO_CONFIG.MAX_MULTIPLIER = riskConfig.maxMultiplier;
        
        // Adjust ball and peg size based on board size
        const scaleFactor = PLINKO_CONFIG.BOARD_WIDTH / 600;
        PLINKO_CONFIG.BALL_RADIUS = Math.max(6, Math.floor(8 * scaleFactor));
        PLINKO_CONFIG.PEG_RADIUS = Math.max(4, Math.floor(6 * scaleFactor));
    }
    
    // Reset the board
    function resetBoard() {
        // Clear any existing animation
        if (gameState.animationId) {
            cancelAnimationFrame(gameState.animationId);
            gameState.animationId = null;
        }
        
        // Reset game state
        gameState.balls = [];
        gameState.pegs = [];
        gameState.isDropping = false;
        
        // Generate pegs
        generatePegs();
        
        // Generate slot rewards
        generateSlotRewards();
        
        // Generate slot zone
        generateSlotZone();
        
        // Draw the board
        drawBoard();
        
        // Enable drop button
        dropBallBtn.disabled = false;
        mobileDropBallBtn.disabled = false;
        
        // Update game status
        updateGameStatus('Select a drop position and click "Drop Ball" to start!', '');
    }
    
    // Generate pegs on the board
    function generatePegs() {
        const rows = PLINKO_CONFIG.ROWS;
        const startSlots = PLINKO_CONFIG.START_SLOTS;
        const endSlots = PLINKO_CONFIG.END_SLOTS;
        const boardWidth = PLINKO_CONFIG.BOARD_WIDTH;
        const boardHeight = PLINKO_CONFIG.BOARD_HEIGHT;
        
        // Calculate horizontal and vertical spacing
        const horizontalSpacing = boardWidth / (endSlots + 1);
        const verticalSpacing = (boardHeight - 100) / (rows + 1);
        
        // Generate pegs for each row
        for (let row = 0; row < rows; row++) {
            // Calculate number of pegs in this row
            const pegsInRow = Math.floor(startSlots + (row * (endSlots - startSlots) / (rows - 1)));
            
            // Calculate offset for centering
            const offset = (boardWidth - (pegsInRow - 1) * horizontalSpacing) / 2;
            
            // Create pegs
            for (let col = 0; col < pegsInRow; col++) {
                gameState.pegs.push({
                    x: offset + col * horizontalSpacing,
                    y: 60 + row * verticalSpacing,
                    radius: PLINKO_CONFIG.PEG_RADIUS,
                    hit: false
                });
            }
        }
    }
    
    // Generate slot rewards based on risk level
    function generateSlotRewards() {
        const endSlots = PLINKO_CONFIG.END_SLOTS;
        const winningSlots = PLINKO_CONFIG.WINNING_SLOTS;
        const maxMultiplier = PLINKO_CONFIG.MAX_MULTIPLIER;
        
        // Create array with all zeros
        const rewards = Array(endSlots).fill(0);
        
        // Define possible rewards
        const possibleRewards = [100, 150, 200, 250];
        
        // Determine winning slots
        const winningIndices = [];
        while (winningIndices.length < winningSlots) {
            const index = Math.floor(Math.random() * endSlots);
            if (!winningIndices.includes(index) && 
                !winningIndices.includes(index - 1) && 
                !winningIndices.includes(index + 1)) {
                winningIndices.push(index);
            }
        }
        
        // Assign rewards to winning slots
        winningIndices.forEach(index => {
            const reward = possibleRewards[Math.floor(Math.random() * possibleRewards.length)];
            rewards[index] = reward;
        });
        
        // Add high value jackpot for high risk
        if (gameState.riskLevel === 'high' && maxMultiplier >= 500) {
            const jackpotIndex = winningIndices[Math.floor(Math.random() * winningIndices.length)];
            rewards[jackpotIndex] = 500;
        }
        
        gameState.rewards = rewards;
        gameState.activeRewards = [...rewards];
    }
    
    // Generate drop zone slots
    function generateDropZone() {
        dropZone.innerHTML = '';
        const startSlots = PLINKO_CONFIG.START_SLOTS;
        
        for (let i = 0; i < startSlots; i++) {
            const slot = document.createElement('div');
            slot.className = 'drop-slot';
            slot.dataset.index = i;
            
            // Set middle slot as active by default
            if (i === Math.floor(startSlots / 2)) {
                slot.classList.add('active');
                gameState.selectedSlot = i;
            }
            
            slot.addEventListener('click', function() {
                selectDropSlot(i);
            });
            
            dropZone.appendChild(slot);
        }
    }
    
    // Generate slot zone
    function generateSlotZone() {
        slotZone.innerHTML = '';
        const endSlots = PLINKO_CONFIG.END_SLOTS;
        
        for (let i = 0; i < endSlots; i++) {
            const slot = document.createElement('div');
            slot.className = 'slot';
            slot.dataset.index = i;
            slot.dataset.value = gameState.activeRewards[i];
            
            // Display the slot value
            slot.textContent = gameState.activeRewards[i] > 0 ? 
                `${gameState.activeRewards[i]}×` : '0×';
            
            slotZone.appendChild(slot);
        }
    }
    
    // Select a drop slot
    function selectDropSlot(index) {
        // Remove active class from all slots
        document.querySelectorAll('.drop-slot').forEach(slot => {
            slot.classList.remove('active');
        });
        
        // Add active class to selected slot
        document.querySelector(`.drop-slot[data-index="${index}"]`).classList.add('active');
        
        // Update selected slot
        gameState.selectedSlot = index;
        
        // Play click sound
        playSound(clickSound);
    }
    
    // Draw the board
    function drawBoard() {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw background
        ctx.fillStyle = '#212121';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Draw pegs
        gameState.pegs.forEach(peg => {
            ctx.beginPath();
            ctx.arc(peg.x, peg.y, peg.radius, 0, Math.PI * 2);
            ctx.fillStyle = peg.hit ? '#e91e63' : '#9e9e9e';
            ctx.fill();
            
            // Add highlight effect
            ctx.beginPath();
            ctx.arc(peg.x - peg.radius * 0.3, peg.y - peg.radius * 0.3, peg.radius * 0.4, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.fill();
        });
        
        // Draw balls
        gameState.balls.forEach(ball => {
            if (!ball.isActive) return;
            
            // Draw ball trail
            ball.trail.forEach((pos, index) => {
                const alpha = index / ball.trail.length;
                ctx.beginPath();
                ctx.arc(pos.x, pos.y, ball.radius * alpha, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(233, 30, 99, ${alpha * 0.5})`;
                ctx.fill();
            });
            
            // Draw ball
            ctx.beginPath();
            ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
            ctx.fillStyle = '#e91e63';
            ctx.fill();
            
            // Add highlight effect
            ctx.beginPath();
            ctx.arc(ball.x - ball.radius * 0.3, ball.y - ball.radius * 0.3, ball.radius * 0.4, 0, Math.PI * 2);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
            ctx.fill();
        });
    }
    
    // Ball class for physics simulation
    class Ball {
        constructor(x, y) {
            this.x = x;
            this.y = y;
            this.vx = 0;
            this.vy = 0;
            this.radius = PLINKO_CONFIG.BALL_RADIUS;
            this.trail = [];
            this.isActive = true;
            this.settled = false;
            
            // Add small initial horizontal velocity to increase randomness
            // 70% chance to move away from center to reduce win probability
            const centerX = PLINKO_CONFIG.BOARD_WIDTH / 2;
            const isLeftSide = this.x < centerX;
            const pushAwayFromCenter = Math.random() < 0.7;
            
            // Direction is away from center with 70% probability
            const direction = pushAwayFromCenter ? 
                (isLeftSide ? -1 : 1) :  // Away from center 
                (isLeftSide ? 1 : -1);   // Toward center
                
            this.vx = direction * (1 + Math.random() * 2); // Random initial push
        }
        
        update() {
            if (!this.isActive) return;
            
            // Apply gravity
            this.vy += PLINKO_CONFIG.GRAVITY;
            
            // Update position
            this.x += this.vx;
            this.y += this.vy;
            
            // Add to trail
            this.trail.push({ x: this.x, y: this.y });
            if (this.trail.length > 20) {
                this.trail.shift();
            }
            
            // Check peg collisions
            this.checkPegCollisions();
            
            // Apply air resistance
            this.vx *= 0.99;
            this.vy *= 0.999;
            
            // Boundary checks
            if (this.x < this.radius) {
                this.x = this.radius;
                this.vx = Math.abs(this.vx) * 0.5;
            } else if (this.x > PLINKO_CONFIG.BOARD_WIDTH - this.radius) {
                this.x = PLINKO_CONFIG.BOARD_WIDTH - this.radius;
                this.vx = -Math.abs(this.vx) * 0.5;
            }
            
            // Check if ball has settled
            if (this.y > PLINKO_CONFIG.BOARD_HEIGHT - 30 && !this.settled) {
                this.settled = true;
                this.settleInSlot();
            }
        }
        
        checkPegCollisions() {
            gameState.pegs.forEach(peg => {
                const dx = this.x - peg.x;
                const dy = this.y - peg.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < this.radius + peg.radius) {
                    // Collision detected
                    peg.hit = true;
                    
                    // Play peg hit sound
                    playSound(pegHitSound);
                    
                    // Normalize collision vector
                    const nx = dx / distance;
                    const ny = dy / distance;
                    
                    // Separate the ball from the peg
                    const overlap = this.radius + peg.radius - distance;
                    this.x += nx * overlap * 0.5;
                    this.y += ny * overlap * 0.5;
                    
                    // Calculate relative velocity
                    const relativeVelX = this.vx;
                    const relativeVelY = this.vy;
                    
                    // Calculate relative velocity in collision normal direction
                    const velAlongNormal = relativeVelX * nx + relativeVelY * ny;
                    
                    // Do not resolve if velocities are separating
                    if (velAlongNormal > 0) return;
                    
                    // Calculate restitution
                    const restitution = PLINKO_CONFIG.ELASTICITY;
                    
                    // Calculate impulse scalar
                    let impulseScalar = -(1 + restitution) * velAlongNormal;
                    
                    // Apply impulse
                    this.vx += impulseScalar * nx;
                    this.vy += impulseScalar * ny;
                    
                    // Add deflection force for more random bounces
                    const deflection = PLINKO_CONFIG.DEFLECTION_FORCE;
                    const randomDeflection = Math.random() * deflection - deflection / 2;
                    
                    // Apply more deflection to balls near center to reduce advantage
                    const centerX = PLINKO_CONFIG.BOARD_WIDTH / 2;
                    const distanceFromCenter = Math.abs(this.x - centerX);
                    const centerMultiplier = 1 - (distanceFromCenter / (PLINKO_CONFIG.BOARD_WIDTH / 2)) * 0.5;
                    
                    this.vx += randomDeflection * centerMultiplier;
                }
            });
        }
        
        settleInSlot() {
            // Determine which slot the ball landed in
            const endSlots = PLINKO_CONFIG.END_SLOTS;
            const slotWidth = PLINKO_CONFIG.BOARD_WIDTH / endSlots;
            const slotIndex = Math.min(endSlots - 1, Math.max(0, Math.floor(this.x / slotWidth)));
            
            // Get the reward for this slot
            const reward = gameState.activeRewards[slotIndex];
            
            // Animate the slot
            const slotElement = document.querySelector(`.slot[data-index="${slotIndex}"]`);
            slotElement.classList.add('active');
            
            // Play appropriate sound
            if (reward > 0) {
                playSound(slotSound);
            } else {
                playSound(loseSound);
            }
            
            // Update score
            const pointsWon = reward * gameState.betAmount;
            gameState.lastScore = pointsWon;
            gameState.score += pointsWon;
            
            // Update balance
            if (pointsWon > 0) {
                gameState.balance += pointsWon;
                showWinLossIndicator(true, pointsWon);
                updateGameStatus(`🎉 You won ${pointsWon} GA with a ${reward}× multiplier!`, 'success');
                
                // Update statistics
                gameState.statistics.gamesWon++;
                gameState.statistics.totalProfit += pointsWon;
                
                if (pointsWon > gameState.statistics.bestWin) {
                    gameState.statistics.bestWin = pointsWon;
                }
                
                // Update best score
                if (pointsWon > gameState.bestScore) {
                    gameState.bestScore = pointsWon;
                }
                
                // Update mobile last win
                mobileLastWin.textContent = `Last: +${pointsWon} GA`;
            } else {
                showWinLossIndicator(false, gameState.betAmount);
                updateGameStatus(`💥 You lost ${gameState.betAmount} GA!`, 'danger');
                gameState.statistics.totalProfit -= gameState.betAmount;
                
                // Update mobile last win
                mobileLastWin.textContent = `Last: -${gameState.betAmount} GA`;
            }
            
            // Update slot statistics
            if (!gameState.statistics.slotStats[slotIndex]) {
                gameState.statistics.slotStats[slotIndex] = {
                    hits: 0,
                    wins: 0,
                    totalWon: 0
                };
            }
            
            gameState.statistics.slotStats[slotIndex].hits++;
            if (reward > 0) {
                gameState.statistics.slotStats[slotIndex].wins++;
                gameState.statistics.slotStats[slotIndex].totalWon += pointsWon;
            }
            
            // Increment balls dropped
            gameState.ballsDropped++;
            gameState.statistics.totalGames++;
            
            // Add to game history
            addToGameHistory({
                id: gameState.gameHistory.length + 1,
                bet: gameState.betAmount,
                board: gameState.boardSize,
                position: gameState.selectedSlot,
                slot: slotIndex,
                reward: reward,
                profit: pointsWon - gameState.betAmount,
                time: new Date()
            });
            
            // Update displays
            updateBalanceDisplay();
            updateStatsDisplay();
            updateHistoryDisplay();
            
            // Remove ball from active list after a delay
            setTimeout(() => {
                this.isActive = false;
                slotElement.classList.remove('active');
                
                // Re-enable drop button if not auto dropping
                if (!gameState.autoDropActive) {
                    gameState.isDropping = false;
                    dropBallBtn.disabled = false;
                    mobileDropBallBtn.disabled = false;
                }
                
                // Save game state
                saveGameState();
                
                // Continue auto dropping if active
                if (gameState.autoDropActive && gameState.autoDropRemaining > 0) {
                    gameState.autoDropRemaining--;
                    updateAutoDropDisplay();
                    
                    // Check if player has enough balance
                    if (gameState.balance >= gameState.betAmount) {
                        setTimeout(() => {
                            dropBall();
                        }, 500);
                    } else {
                        stopAutoDropping();
                        updateGameStatus('Not enough balance to continue auto dropping!', 'warning');
                    }
                } else if (gameState.autoDropActive && gameState.autoDropRemaining === 0) {
                    stopAutoDropping();
                }
            }, 1000);
        }
    }
    
    // Drop a ball
    function dropBall() {
        if (gameState.isDropping) return;
        
        // Check if player has enough balance
        if (gameState.balance < gameState.betAmount) {
            updateGameStatus('Not enough balance to place this bet!', 'warning');
            return;
        }
        
        // Deduct bet amount from balance
        gameState.balance -= gameState.betAmount;
        gameState.statistics.totalWagered += gameState.betAmount;
        updateBalanceDisplay();
        
        // Set dropping state
        gameState.isDropping = true;
        
        // Disable drop button
        dropBallBtn.disabled = true;
        mobileDropBallBtn.disabled = true;
        
        // Reset peg hit states
        gameState.pegs.forEach(peg => {
            peg.hit = false;
        });
        
        // Calculate drop position
        const startSlots = PLINKO_CONFIG.START_SLOTS;
        const dropX = (gameState.selectedSlot + 0.5) * (PLINKO_CONFIG.BOARD_WIDTH / startSlots);
        const dropY = 20; // Top of board
        
        // Create a new ball
        const ball = new Ball(dropX, dropY);
        gameState.balls.push(ball);
        
        // Start animation
        if (!gameState.animationId) {
            animate();
        }
        
        // Update game status
        updateGameStatus('Ball dropping... Watch it bounce!', '');
    }
    
    // Start auto dropping
    function startAutoDropping() {
        // Toggle state if already active
        if (gameState.autoDropActive) {
            stopAutoDropping();
            return;
        }
        
        // Check if player has enough balance for at least one drop
        if (gameState.balance < gameState.betAmount) {
            updateGameStatus('Not enough balance to place this bet!', 'warning');
            return;
        }
        
        // Set auto drop state
        gameState.autoDropActive = true;
        gameState.autoDropRemaining = parseInt(autoDropCountInput.value);
        
        // Update checkbox
        autoDropCheckbox.checked = true;
        if (modalAutoDrop) modalAutoDrop.checked = true;
        
        // Update buttons
        mobileAutoBallBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Auto';
        
        // Update auto drop display
        updateAutoDropDisplay();
        
        // Start first drop if not already dropping
        if (!gameState.isDropping) {
            dropBall();
        }
        
        // Play click sound
        playSound(clickSound);
    }
    
    // Stop auto dropping
    function stopAutoDropping() {
        // Set auto drop state
        gameState.autoDropActive = false;
        gameState.autoDropRemaining = 0;
        
        // Update checkbox
        autoDropCheckbox.checked = false;
        if (modalAutoDrop) modalAutoDrop.checked = false;
        
        // Update buttons
        mobileAutoBallBtn.innerHTML = '<i class="fas fa-magic"></i> Auto Drop';
        
        // Update auto drop display
        updateAutoDropDisplay();
        
        // Play click sound
        playSound(clickSound);
    }
    
    // Animation loop
    function animate() {
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw board
        drawBoard();
        
        // Update and filter active balls
        gameState.balls = gameState.balls.filter(ball => ball.isActive);
        gameState.balls.forEach(ball => ball.update());
        
        // Continue animation if there are active balls
        if (gameState.balls.length > 0) {
            gameState.animationId = requestAnimationFrame(animate);
        } else {
            gameState.animationId = null;
        }
    }
    
    // Show win/loss indicator
    function showWinLossIndicator(isWin, amount) {
        // Main indicator
        const indicator = document.createElement('div');
        indicator.className = isWin ? 'win' : 'loss';
        indicator.textContent = isWin ? `+${amount} GA` : `-${amount} GA`;
        
        winLossIndicator.innerHTML = '';
        winLossIndicator.appendChild(indicator);
        
        // Header indicator
        const headerIndicator = document.createElement('div');
        headerIndicator.className = isWin ? 'header-win-indicator' : 'header-loss-indicator';
        headerIndicator.innerHTML = `
            <i class="${isWin ? 'fas fa-trophy' : 'fas fa-times-circle'} indicator-icon"></i>
            <span>${isWin ? '+' : '-'}${Math.abs(amount)} GA</span>
        `;
        
        headerWinLossIndicator.innerHTML = '';
        headerWinLossIndicator.appendChild(headerIndicator);
        
        // Play appropriate sound
        if (isWin) {
            playSound(winSound);
        }
    }
    
    // Update balance display
    function updateBalanceDisplay() {
        balanceValue.textContent = `${gameState.balance} GA`;
        mobileBalanceValue.textContent = `${gameState.balance} GA`;
    }
    
    // Update stats display
    function updateStatsDisplay() {
        // Update basic stats
        ballsDropped.textContent = gameState.ballsDropped;
        lastPoints.textContent = gameState.lastScore > 0 ? 
            `${gameState.lastScore} GA` : '-';
        bestPoints.textContent = gameState.bestScore > 0 ? 
            `${gameState.bestScore} GA` : '-';
        
        // Update advanced stats
        totalWagered.textContent = `${gameState.statistics.totalWagered} GA`;
        
        const winRateValue = gameState.statistics.totalGames > 0 ? 
            Math.round((gameState.statistics.gamesWon / gameState.statistics.totalGames) * 100) : 0;
        winRate.textContent = `${winRateValue}%`;
        
        profitLoss.textContent = `${gameState.statistics.totalProfit >= 0 ? '+' : ''}${gameState.statistics.totalProfit} GA`;
        profitLoss.className = `stat-value ${gameState.statistics.totalProfit >= 0 ? 'positive' : 'negative'}`;
        
        // Update slot statistics table if in Pro View
        if (gameState.viewMode === 'pro') {
            updateSlotStatsTable();
            updateSlotStatsChart();
        }
    }
    
    // Update slot statistics table
    function updateSlotStatsTable() {
        slotStatsTableBody.innerHTML = '';
        
        for (let i = 0; i < PLINKO_CONFIG.END_SLOTS; i++) {
            const stats = gameState.statistics.slotStats[i] || { hits: 0, wins: 0, totalWon: 0 };
            const row = document.createElement('tr');
            
            const winPercent = stats.hits > 0 ? 
                Math.round((stats.wins / stats.hits) * 100) : 0;
            
            row.innerHTML = `
                <td>${i + 1}</td>
                <td>${gameState.activeRewards[i]}×</td>
                <td>${stats.hits}</td>
                <td>${winPercent}%</td>
                <td>${stats.totalWon} GA</td>
            `;
            
            slotStatsTableBody.appendChild(row);
        }
    }
    
    // Initialize slot statistics chart
    function initSlotStatsChart() {
        const ctx = document.getElementById('slotStatsChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (slotStatsChart) {
            slotStatsChart.destroy();
        }
        
        slotStatsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: Array.from({ length: PLINKO_CONFIG.END_SLOTS }, (_, i) => `Slot ${i + 1}`),
                datasets: [{
                    label: 'Hits',
                    data: Array.from({ length: PLINKO_CONFIG.END_SLOTS }, (_, i) => {
                        const stats = gameState.statistics.slotStats[i] || { hits: 0 };
                        return stats.hits;
                    }),
                    backgroundColor: '#3f51b5',
                    borderColor: '#303f9f',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#aaa'
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: '#eee'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const slotIndex = context.dataIndex;
                                const stats = gameState.statistics.slotStats[slotIndex] || { hits: 0, wins: 0, totalWon: 0 };
                                const winPercent = stats.hits > 0 ? 
                                    Math.round((stats.wins / stats.hits) * 100) : 0;
                                    
                                return [
                                    `Hits: ${stats.hits}`,
                                    `Wins: ${stats.wins} (${winPercent}%)`,
                                    `Total Won: ${stats.totalWon} GA`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Update slot statistics chart
    function updateSlotStatsChart() {
        if (!slotStatsChart) return;
        
        slotStatsChart.data.datasets[0].data = Array.from({ length: PLINKO_CONFIG.END_SLOTS }, (_, i) => {
            const stats = gameState.statistics.slotStats[i] || { hits: 0 };
            return stats.hits;
        });
        
        slotStatsChart.update();
    }
    
    // Update history display
    function updateHistoryDisplay() {
        historyTableBody.innerHTML = '';
        
        if (gameState.gameHistory.length === 0) {
            historyPlaceholder.style.display = 'block';
            return;
        }
        
        historyPlaceholder.style.display = 'none';
        
        gameState.gameHistory.slice(0, 10).forEach(game => {
            const row = document.createElement('tr');
            
            const timeString = formatTime(game.time);
            const resultClass = game.profit >= 0 ? 'win' : 'loss';
            
            row.innerHTML = `
                <td>#${game.id}</td>
                <td>${game.bet} GA</td>
                <td>${game.board.charAt(0).toUpperCase() + game.board.slice(1)}</td>
                <td>${game.position + 1}</td>
                <td>${game.slot + 1}</td>
                <td class="${resultClass}">${game.profit >= 0 ? '+' : ''}${game.profit} GA</td>
                <td>${timeString}</td>
            `;
            
            historyTableBody.appendChild(row);
        });
    }
    
    // Update auto drop display
    function updateAutoDropDisplay() {
        if (gameState.autoDropActive) {
            autoDropStatus.textContent = 'Auto Drop: Active';
            autoDropRemaining.textContent = `Remaining: ${gameState.autoDropRemaining}`;
            
            // Update progress bar
            const totalDrops = parseInt(autoDropCountInput.value);
            const progress = ((totalDrops - gameState.autoDropRemaining) / totalDrops) * 100;
            autoDropProgressFill.style.width = `${progress}%`;
        } else {
            autoDropStatus.textContent = 'Auto Drop: Off';
            autoDropRemaining.textContent = '';
            autoDropProgressFill.style.width = '0%';
        }
    }
    
    // Update game status
    function updateGameStatus(message, type = '') {
        gameStatus.textContent = message;
        gameStatus.className = `game-status ${type}`;
    }
    
    // Add to game history
    function addToGameHistory(game) {
        // Convert date to string for localStorage
        const gameWithDateString = {
            ...game,
            time: game.time.toISOString()
        };
        
        gameState.gameHistory.unshift(gameWithDateString);
        
        // Limit history size
        if (gameState.gameHistory.length > 50) {
            gameState.gameHistory.pop();
        }
    }
    
    // Format time
    function formatTime(timeString) {
        const date = new Date(timeString);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    // Toggle view mode
    function toggleViewMode(mode) {
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
            standardViewBtn.classList.add('active');
            proViewBtn.classList.remove('active');
            gameState.viewMode = 'standard';
        } else {
            document.body.classList.add('pro-view-active');
            proViewBtn.classList.add('active');
            standardViewBtn.classList.remove('active');
            gameState.viewMode = 'pro';
            
            // Initialize chart if it doesn't exist
            if (!slotStatsChart) {
                initSlotStatsChart();
            }
        }
        
        // Play click sound
        playSound(clickSound);
    }
    
    // Toggle sound
    function toggleSound() {
        gameState.soundEnabled = !gameState.soundEnabled;
        
        if (gameState.soundEnabled) {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
            mobileSoundBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        } else {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
            mobileSoundBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        }
        
        // Don't play click sound here to avoid confusion
    }
    
    // Play sound if enabled
    function playSound(sound) {
        if (gameState.soundEnabled) {
            // Clone the sound to allow overlapping plays
            const soundClone = sound.cloneNode();
            soundClone.volume = 0.3; // Lower volume
            soundClone.play().catch(e => {
                // Ignore autoplay errors
                console.log("Sound play prevented:", e);
            });
        }
    }
    
    // Save game state to localStorage
    function saveGameState() {
        const stateToSave = {
            balance: gameState.balance,
            score: gameState.score,
            ballsDropped: gameState.ballsDropped,
            lastScore: gameState.lastScore,
            bestScore: gameState.bestScore,
            betAmount: gameState.betAmount,
            autoDropCount: gameState.autoDropCount,
            soundEnabled: gameState.soundEnabled,
            viewMode: gameState.viewMode,
            boardSize: gameState.boardSize,
            riskLevel: gameState.riskLevel,
            statistics: gameState.statistics,
            gameHistory: gameState.gameHistory
        };
        
        localStorage.setItem('plinkoGameState', JSON.stringify(stateToSave));
    }
    
    // Load game state from localStorage
    function loadGameState() {
        const savedState = localStorage.getItem('plinkoGameState');
        
        if (savedState) {
            try {
                const parsedState = JSON.parse(savedState);
                
                // Update game state with saved values
                gameState.balance = parsedState.balance || 1000;
                gameState.score = parsedState.score || 0;
                gameState.ballsDropped = parsedState.ballsDropped || 0;
                gameState.lastScore = parsedState.lastScore || 0;
                gameState.bestScore = parsedState.bestScore || 0;
                gameState.betAmount = parsedState.betAmount || 10;
                gameState.autoDropCount = parsedState.autoDropCount || 5;
                gameState.soundEnabled = parsedState.soundEnabled !== undefined ? parsedState.soundEnabled : true;
                gameState.viewMode = parsedState.viewMode || 'standard';
                gameState.boardSize = parsedState.boardSize || 'medium';
                gameState.riskLevel = parsedState.riskLevel || 'medium';
                gameState.statistics = parsedState.statistics || {
                    totalGames: 0,
                    gamesWon: 0,
                    totalWagered: 0,
                    totalProfit: 0,
                    bestWin: 0,
                    slotStats: {}
                };
                
                // Convert date strings back to Date objects for history
                gameState.gameHistory = parsedState.gameHistory || [];
                
                // Update inputs with loaded values
                betAmountInput.value = gameState.betAmount;
                autoDropCountInput.value = gameState.autoDropCount;
                boardSizeSelect.value = gameState.boardSize;
                riskLevelSelect.value = gameState.riskLevel;
                
                // Update modal inputs
                modalBetAmount.value = gameState.betAmount;
                modalAutoDropCount.value = gameState.autoDropCount;
                modalBoardSize.value = gameState.boardSize;
                modalRiskLevel.value = gameState.riskLevel;
                
                // Update view mode
                if (gameState.viewMode === 'pro') {
                    toggleViewMode('pro');
                }
                
                // Update sound toggle
                if (!gameState.soundEnabled) {
                    soundToggleBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                    mobileSoundBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                }
            } catch (e) {
                console.error('Error loading game state:', e);
            }
        }
    }
    
    // Setup event listeners
    function setupEventListeners() {
        // Drop ball button
        dropBallBtn.addEventListener('click', dropBall);
        mobileDropBallBtn.addEventListener('click', dropBall);
        
        // Reset button
        resetBtn.addEventListener('click', () => {
            resetBoard();
            playSound(clickSound);
        });
        
        // Auto drop checkbox
        autoDropCheckbox.addEventListener('change', () => {
            if (autoDropCheckbox.checked) {
                startAutoDropping();
            } else {
                stopAutoDropping();
            }
        });
        
        // Auto drop mobile button
        mobileAutoBallBtn.addEventListener('click', startAutoDropping);
        
        // Board size select
        boardSizeSelect.addEventListener('change', () => {
            gameState.boardSize = boardSizeSelect.value;
            updateBoardConfig();
            resetBoard();
            playSound(clickSound);
        });
        
        // Risk level select
        riskLevelSelect.addEventListener('change', () => {
            gameState.riskLevel = riskLevelSelect.value;
            updateBoardConfig();
            resetBoard();
            playSound(clickSound);
        });
        
        // Bet amount input
        betAmountInput.addEventListener('change', () => {
            const betAmount = parseInt(betAmountInput.value);
            
            if (betAmount < 1 || isNaN(betAmount)) {
                betAmountInput.value = 1;
                gameState.betAmount = 1;
            } else if (betAmount > gameState.balance) {
                betAmountInput.value = gameState.balance;
                gameState.betAmount = gameState.balance;
            } else {
                gameState.betAmount = betAmount;
            }
            
            // Update modal input
            modalBetAmount.value = gameState.betAmount;
        });
        
        // Auto drop count input
        autoDropCountInput.addEventListener('change', () => {
            const count = parseInt(autoDropCountInput.value);
            
            if (count < 1 || isNaN(count)) {
                autoDropCountInput.value = 1;
                gameState.autoDropCount = 1;
            } else if (count > 100) {
                autoDropCountInput.value = 100;
                gameState.autoDropCount = 100;
            } else {
                gameState.autoDropCount = count;
            }
            
            // Update modal input
            modalAutoDropCount.value = gameState.autoDropCount;
        });
        
        // Decrease bet button
        decreaseBetBtn.addEventListener('click', () => {
            const currentBet = parseInt(betAmountInput.value);
            if (currentBet > 1) {
                betAmountInput.value = currentBet - 1;
                gameState.betAmount = currentBet - 1;
                modalBetAmount.value = gameState.betAmount;
                playSound(clickSound);
            }
        });
        
        // Increase bet button
        increaseBetBtn.addEventListener('click', () => {
            const currentBet = parseInt(betAmountInput.value);
            if (currentBet < gameState.balance) {
                betAmountInput.value = currentBet + 1;
                gameState.betAmount = currentBet + 1;
                modalBetAmount.value = gameState.betAmount;
                playSound(clickSound);
            }
        });
        
        // Decrease count button
        decreaseCountBtn.addEventListener('click', () => {
            const currentCount = parseInt(autoDropCountInput.value);
            if (currentCount > 1) {
                autoDropCountInput.value = currentCount - 1;
                gameState.autoDropCount = currentCount - 1;
                modalAutoDropCount.value = gameState.autoDropCount;
                playSound(clickSound);
            }
        });
        
        // Increase count button
        increaseCountBtn.addEventListener('click', () => {
            const currentCount = parseInt(autoDropCountInput.value);
            if (currentCount < 100) {
                autoDropCountInput.value = currentCount + 1;
                gameState.autoDropCount = currentCount + 1;
                modalAutoDropCount.value = gameState.autoDropCount;
                playSound(clickSound);
            }
        });
        
        // Bet preset buttons
        betPresetBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const amount = btn.dataset.amount;
                
                if (amount === 'max') {
                    betAmountInput.value = gameState.balance;
                    gameState.betAmount = gameState.balance;
                } else {
                    const presetAmount = parseInt(amount);
                    
                    if (presetAmount <= gameState.balance) {
                        betAmountInput.value = presetAmount;
                        gameState.betAmount = presetAmount;
                    } else {
                        betAmountInput.value = gameState.balance;
                        gameState.betAmount = gameState.balance;
                    }
                }
                
                modalBetAmount.value = gameState.betAmount;
                playSound(clickSound);
            });
        });
        
        // Drop preset buttons
        dropPresetBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const count = parseInt(btn.dataset.count);
                
                autoDropCountInput.value = count;
                gameState.autoDropCount = count;
                modalAutoDropCount.value = count;
                playSound(clickSound);
            });
        });
        
        // View mode buttons
        standardViewBtn.addEventListener('click', () => toggleViewMode('standard'));
        proViewBtn.addEventListener('click', () => toggleViewMode('pro'));
        
        // Sound toggle
        soundToggleBtn.addEventListener('click', toggleSound);
        mobileSoundBtn.addEventListener('click', toggleSound);
        
        // Toggle buttons
        statsToggle.addEventListener('click', () => {
            const content = document.querySelector('.stats-content');
            content.classList.toggle('expanded');
            statsToggle.innerHTML = content.classList.contains('expanded') ? 
                '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
            playSound(clickSound);
        });
        
        slotStatsToggle.addEventListener('click', () => {
            const content = document.querySelector('.slot-stats-content');
            content.classList.toggle('expanded');
            slotStatsToggle.innerHTML = content.classList.contains('expanded') ? 
                '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
            playSound(clickSound);
        });
        
        historyToggle.addEventListener('click', () => {
            const content = document.querySelector('.history-content');
            content.classList.toggle('expanded');
            historyToggle.innerHTML = content.classList.contains('expanded') ? 
                '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
            playSound(clickSound);
        });
        
        // Mobile modal buttons
        mobileBetBtn.addEventListener('click', () => {
            betSettingsModal.style.display = 'block';
            playSound(clickSound);
        });
        
        mobileBoardBtn.addEventListener('click', () => {
            boardSettingsModal.style.display = 'block';
            playSound(clickSound);
        });
        
        // Modal close buttons
        closeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                btn.closest('.modal').style.display = 'none';
                playSound(clickSound);
            });
        });
        
        // Close modals when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
        
        // Modal bet controls
        modalDecreaseBet.addEventListener('click', () => {
            const currentBet = parseInt(modalBetAmount.value);
            if (currentBet > 1) {
                modalBetAmount.value = currentBet - 1;
                playSound(clickSound);
            }
        });
        
        modalIncreaseBet.addEventListener('click', () => {
            const currentBet = parseInt(modalBetAmount.value);
            if (currentBet < gameState.balance) {
                modalBetAmount.value = currentBet + 1;
                playSound(clickSound);
            }
        });
        
        modalBetAmount.addEventListener('change', () => {
            const betAmount = parseInt(modalBetAmount.value);
            
            if (betAmount < 1 || isNaN(betAmount)) {
                modalBetAmount.value = 1;
            } else if (betAmount > gameState.balance) {
                modalBetAmount.value = gameState.balance;
            }
        });
        
        // Modal auto drop controls
        modalDecreaseCount.addEventListener('click', () => {
            const currentCount = parseInt(modalAutoDropCount.value);
            if (currentCount > 1) {
                modalAutoDropCount.value = currentCount - 1;
                playSound(clickSound);
            }
        });
        
        modalIncreaseCount.addEventListener('click', () => {
            const currentCount = parseInt(modalAutoDropCount.value);
            if (currentCount < 100) {
                modalAutoDropCount.value = currentCount + 1;
                playSound(clickSound);
            }
        });
        
        modalAutoDropCount.addEventListener('change', () => {
            const count = parseInt(modalAutoDropCount.value);
            
            if (count < 1 || isNaN(count)) {
                modalAutoDropCount.value = 1;
            } else if (count > 100) {
                modalAutoDropCount.value = 100;
            }
        });
        
        modalAutoDrop.addEventListener('change', () => {
            if (modalAutoDrop.checked !== autoDropCheckbox.checked) {
                autoDropCheckbox.checked = modalAutoDrop.checked;
                if (modalAutoDrop.checked) {
                    startAutoDropping();
                } else {
                    stopAutoDropping();
                }
            }
        });
        
        // Modal preset buttons
        modalPresetBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const amount = btn.dataset.amount;
                
                if (amount === 'max') {
                    modalBetAmount.value = gameState.balance;
                } else {
                    const presetAmount = parseInt(amount);
                    
                    if (presetAmount <= gameState.balance) {
                        modalBetAmount.value = presetAmount;
                    } else {
                        modalBetAmount.value = gameState.balance;
                    }
                }
                
                playSound(clickSound);
            });
        });
        
        // Apply modal settings
        modalApplyBtn.addEventListener('click', () => {
            const betAmount = parseInt(modalBetAmount.value);
            const autoDropCount = parseInt(modalAutoDropCount.value);
            
            betAmountInput.value = betAmount;
            gameState.betAmount = betAmount;
            
            autoDropCountInput.value = autoDropCount;
            gameState.autoDropCount = autoDropCount;
            
            // Update auto drop state
            if (modalAutoDrop.checked !== autoDropCheckbox.checked) {
                autoDropCheckbox.checked = modalAutoDrop.checked;
                if (modalAutoDrop.checked) {
                    startAutoDropping();
                } else {
                    stopAutoDropping();
                }
            }
            
            // Close modal
            betSettingsModal.style.display = 'none';
            playSound(clickSound);
        });
        
        // Modal board settings
        modalApplyBoardBtn.addEventListener('click', () => {
            gameState.boardSize = modalBoardSize.value;
            gameState.riskLevel = modalRiskLevel.value;
            
            boardSizeSelect.value = gameState.boardSize;
            riskLevelSelect.value = gameState.riskLevel;
            
            updateBoardConfig();
            resetBoard();
            
            // Close modal
            boardSettingsModal.style.display = 'none';
            playSound(clickSound);
        });
        
        // Window resize
        window.addEventListener('resize', () => {
            resizeCanvas();
            updateBoardConfig();
            resetBoard();
        });
    }
    
    // Initialize the game
    initGame();
});