document.addEventListener('DOMContentLoaded', function() {
    // Auth modal elements
    const loginBtn = document.querySelector('.btn-login');
    const registerBtn = document.querySelector('.btn-register');
    
    // Check if buttons exist
    if (!loginBtn || !registerBtn) {
        console.error('Login or register buttons not found');
        return;
    }
    
    const authModalOverlay = document.createElement('div');
    authModalOverlay.className = 'auth-modal-overlay';
    
    // Create and append the modal HTML
    authModalOverlay.innerHTML = `
        <div class="auth-modal">
            <div class="auth-modal-header">
                <div class="auth-logo">
                    <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura">
                    <span class="auth-logo-text">GoldenAura</span>
                </div>
                <button class="auth-close-btn" id="authCloseBtn">&times;</button>
            </div>
            
            <div class="auth-tabs">
                <div class="auth-tab login-tab active" data-tab="login">Sign In</div>
                <div class="auth-tab register-tab" data-tab="register">Create Account</div>
            </div>
            
            <div class="auth-content">
                <!-- Login Form -->
                <div class="auth-form login-form active">
                    <div class="auth-error">
                        <p>Invalid username or password. Please try again.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginUsername">Username or Email</label>
                        <input type="text" class="form-control" id="loginUsername" placeholder="Enter your username or email">
                        <div class="error-message">Please enter your username or email</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="loginPassword">Password</label>
                        <input type="password" class="form-control" id="loginPassword" placeholder="Enter your password">
                        <i class="fas fa-eye password-toggle" id="loginPasswordToggle"></i>
                        <div class="error-message">Please enter your password</div>
                    </div>
                    
                    <div class="auth-options">
                        <div class="remember-me">
                            <input type="checkbox" id="rememberMe">
                            <label for="rememberMe">Remember me</label>
                        </div>
                        <a href="#" class="forgot-password">Forgot Password?</a>
                    </div>
                    
                    <button class="auth-submit" id="loginSubmit">Sign In</button>
                    
                    <div class="auth-divider">
                        <hr><span>OR</span><hr>
                    </div>
                    
                    <div class="social-login">
                        <div class="social-btn google" title="Sign in with Google">
                            <i class="fab fa-google"></i>
                        </div>
                        <div class="social-btn facebook" title="Sign in with Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </div>
                        <div class="social-btn discord" title="Sign in with Discord">
                            <i class="fab fa-discord"></i>
                        </div>
                        <div class="social-btn steam" title="Sign in with Steam">
                            <i class="fab fa-steam"></i>
                        </div>
                    </div>
                    
                    <div class="auth-footer">
                        Don't have an account? <a href="#" class="switch-to-register">Create one now</a>
                    </div>
                </div>
                
                <!-- Register Form -->
                <div class="auth-form register-form">
                    <div class="auth-error">
                        <p>Registration failed. Please check your information and try again.</p>
                    </div>
                    
                    <div class="verification-notice">
                        <p>Account created successfully! Please check your email to verify your account.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerUsername">Username</label>
                        <input type="text" class="form-control" id="registerUsername" placeholder="Choose a username">
                        <div class="error-message">Username must be 3-20 characters and contain only letters, numbers, and underscores</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerName">Full Name</label>
                        <input type="text" class="form-control" id="registerName" placeholder="Enter your full name">
                        <div class="error-message">Please enter your full name</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerEmail">Email Address</label>
                        <input type="email" class="form-control" id="registerEmail" placeholder="Enter your email address">
                        <div class="error-message">Please enter a valid email address</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerPassword">Password</label>
                        <input type="password" class="form-control" id="registerPassword" placeholder="Create a password">
                        <i class="fas fa-eye password-toggle" id="registerPasswordToggle"></i>
                        <div class="error-message">Password must be at least 8 characters with letters, numbers, and special characters</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="registerConfirmPassword">Confirm Password</label>
                        <input type="password" class="form-control" id="registerConfirmPassword" placeholder="Confirm your password">
                        <i class="fas fa-eye password-toggle" id="registerConfirmPasswordToggle"></i>
                        <div class="error-message">Passwords do not match</div>
                    </div>
                    
                    <div class="form-group">
                        <div class="remember-me">
                            <input type="checkbox" id="agreeTerms">
                            <label for="agreeTerms">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
                        </div>
                        <div class="error-message">You must agree to the terms and conditions</div>
                    </div>
                    
                    <button class="auth-submit" id="registerSubmit">Create Account</button>
                    
                    <div class="auth-divider">
                        <hr><span>OR</span><hr>
                    </div>
                    
                    <div class="social-login">
                        <div class="social-btn google" title="Sign up with Google">
                            <i class="fab fa-google"></i>
                        </div>
                        <div class="social-btn facebook" title="Sign up with Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </div>
                        <div class="social-btn discord" title="Sign up with Discord">
                            <i class="fab fa-discord"></i>
                        </div>
                        <div class="social-btn steam" title="Sign up with Steam">
                            <i class="fab fa-steam"></i>
                        </div>
                    </div>
                    
                    <div class="auth-footer">
                        Already have an account? <a href="#" class="switch-to-login">Sign in</a>
                    </div>
                </div>
                
                <!-- Success Animation (shown after successful register) -->
                <div class="success-checkmark">
                    <div class="check-icon">
                        <span class="icon-line line-tip"></span>
                        <span class="icon-line line-long"></span>
                        <div class="icon-circle"></div>
                        <div class="icon-fix"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Append the modal to the body
    document.body.appendChild(authModalOverlay);
    
    // Initialize API service if it exists
    const api = window.SportsBettingAPI ? new SportsBettingAPI({
        baseUrl: '/api'
    }) : null;
    
    // Get elements after appending to DOM
    const authCloseBtn = document.getElementById('authCloseBtn');
    const loginTab = document.querySelector('.login-tab');
    const registerTab = document.querySelector('.register-tab');
    const loginForm = document.querySelector('.login-form');
    const registerForm = document.querySelector('.register-form');
    const switchToRegister = document.querySelector('.switch-to-register');
    const switchToLogin = document.querySelector('.switch-to-login');
    const loginSubmit = document.getElementById('loginSubmit');
    const registerSubmit = document.getElementById('registerSubmit');
    const successCheckmark = document.querySelector('.success-checkmark');
    const verificationNotice = document.querySelector('.verification-notice');
    
    // Password toggle functionality
    const loginPasswordToggle = document.getElementById('loginPasswordToggle');
    const registerPasswordToggle = document.getElementById('registerPasswordToggle');
    const registerConfirmPasswordToggle = document.getElementById('registerConfirmPasswordToggle');
    const loginPassword = document.getElementById('loginPassword');
    const registerPassword = document.getElementById('registerPassword');
    const registerConfirmPassword = document.getElementById('registerConfirmPassword');
    
    // Toggle password visibility
    function togglePasswordVisibility(passwordInput, toggleIcon) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
    
    // Add password toggle event listeners
    if (loginPasswordToggle && loginPassword) {
        loginPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(loginPassword, this);
        });
    }
    
    if (registerPasswordToggle && registerPassword) {
        registerPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(registerPassword, this);
        });
    }
    
    if (registerConfirmPasswordToggle && registerConfirmPassword) {
        registerConfirmPasswordToggle.addEventListener('click', function() {
            togglePasswordVisibility(registerConfirmPassword, this);
        });
    }
    
    // Show modal functions
    function showLoginModal() {
        console.log('Showing login modal');
        
        // Reset any previous errors or success messages
        document.querySelectorAll('.auth-error').forEach(error => error.classList.remove('show'));
        document.querySelectorAll('.form-group').forEach(group => group.classList.remove('has-error'));
        if (successCheckmark) successCheckmark.classList.remove('show');
        if (verificationNotice) verificationNotice.classList.remove('show');
        
        // Set the active tab to login
        if (loginTab) loginTab.classList.add('active');
        if (registerTab) registerTab.classList.remove('active');
        if (loginForm) loginForm.classList.add('active');
        if (registerForm) registerForm.classList.remove('active');
        
        // Show the modal
        authModalOverlay.classList.add('active');
        
        // Add body class to prevent scrolling
        document.body.style.overflow = 'hidden';
    }
    
    function showRegisterModal() {
        console.log('Showing register modal');
        
        // Reset any previous errors or success messages
        document.querySelectorAll('.auth-error').forEach(error => error.classList.remove('show'));
        document.querySelectorAll('.form-group').forEach(group => group.classList.remove('has-error'));
        if (successCheckmark) successCheckmark.classList.remove('show');
        if (verificationNotice) verificationNotice.classList.remove('show');
        
        // Set the active tab to register
        if (loginTab) loginTab.classList.remove('active');
        if (registerTab) registerTab.classList.add('active');
        if (loginForm) loginForm.classList.remove('active');
        if (registerForm) registerForm.classList.add('active');
        
        // Show the modal
        authModalOverlay.classList.add('active');
        
        // Add body class to prevent scrolling
        document.body.style.overflow = 'hidden';
    }
    
    // Close modal function
    function closeAuthModal() {
        console.log('Closing auth modal');
        authModalOverlay.classList.remove('active');
        
        // Reset form values
        const loginUsername = document.getElementById('loginUsername');
        const loginPassword = document.getElementById('loginPassword');
        const registerUsername = document.getElementById('registerUsername');
        const registerName = document.getElementById('registerName');
        const registerEmail = document.getElementById('registerEmail');
        const registerPassword = document.getElementById('registerPassword');
        const registerConfirmPassword = document.getElementById('registerConfirmPassword');
        const rememberMe = document.getElementById('rememberMe');
        const agreeTerms = document.getElementById('agreeTerms');
        
        if (loginUsername) loginUsername.value = '';
        if (loginPassword) loginPassword.value = '';
        if (registerUsername) registerUsername.value = '';
        if (registerName) registerName.value = '';
        if (registerEmail) registerEmail.value = '';
        if (registerPassword) registerPassword.value = '';
        if (registerConfirmPassword) registerConfirmPassword.value = '';
        if (rememberMe) rememberMe.checked = false;
        if (agreeTerms) agreeTerms.checked = false;
        
        // Remove body class to re-enable scrolling
        document.body.style.overflow = '';
    }
    
    // Event listeners for buttons and tabs
    loginBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Login button clicked');
        showLoginModal();
    });
    
    registerBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Register button clicked');
        showRegisterModal();
    });
    
    if (authCloseBtn) {
        authCloseBtn.addEventListener('click', closeAuthModal);
    }
    
    // Allow clicking outside the modal to close it
    authModalOverlay.addEventListener('click', function(e) {
        if (e.target === authModalOverlay) {
            closeAuthModal();
        }
    });
    
    // Switch between login and register forms
    if (loginTab) {
        loginTab.addEventListener('click', function() {
            loginTab.classList.add('active');
            if (registerTab) registerTab.classList.remove('active');
            if (loginForm) loginForm.classList.add('active');
            if (registerForm) registerForm.classList.remove('active');
            if (successCheckmark) successCheckmark.classList.remove('show');
            if (verificationNotice) verificationNotice.classList.remove('show');
        });
    }
    
    if (registerTab) {
        registerTab.addEventListener('click', function() {
            if (loginTab) loginTab.classList.remove('active');
            registerTab.classList.add('active');
            if (loginForm) loginForm.classList.remove('active');
            if (registerForm) registerForm.classList.add('active');
        });
    }
    
    if (switchToRegister) {
        switchToRegister.addEventListener('click', function(e) {
            e.preventDefault();
            if (registerTab) registerTab.click();
        });
    }
    
    if (switchToLogin) {
        switchToLogin.addEventListener('click', function(e) {
            e.preventDefault();
            if (loginTab) loginTab.click();
        });
    }
    
    // Check if user is already logged in
    function checkAuthStatus() {
        const authToken = localStorage.getItem('auth_token');
        
        if (authToken) {
            // Update UI to show logged in state
            updateUIForLoggedInUser();
        }
    }
    
    // Update UI when user is logged in
    function updateUIForLoggedInUser() {
        if (loginBtn) {
            loginBtn.textContent = 'My Account';
            loginBtn.classList.add('logged-in');
        }
        
        if (registerBtn) {
            registerBtn.textContent = 'Logout';
            registerBtn.classList.add('logout-btn');
            
            // Change event handler for register button to handle logout
            registerBtn.removeEventListener('click', showRegisterModal);
            registerBtn.addEventListener('click', handleLogout);
        }
    }
    
    // Handle logout
    function handleLogout(e) {
        e.preventDefault();
        
        if (api) {
            // Show loading state
            registerBtn.textContent = 'Logging out...';
            registerBtn.disabled = true;
            
            // Call logout API
            api.logout()
                .then(() => {
                    // Clear auth token
                    localStorage.removeItem('auth_token');
                    
                    // Reset UI
                    loginBtn.textContent = 'Login';
                    loginBtn.classList.remove('logged-in');
                    
                    registerBtn.textContent = 'Register';
                    registerBtn.classList.remove('logout-btn');
                    registerBtn.disabled = false;
                    
                    // Re-attach register event handler
                    registerBtn.removeEventListener('click', handleLogout);
                    registerBtn.addEventListener('click', showRegisterModal);
                    
                    // Reload page to reset state
                    window.location.reload();
                })
                .catch(error => {
                    console.error('Logout failed:', error);
                    
                    // Reset button state
                    registerBtn.textContent = 'Logout';
                    registerBtn.disabled = false;
                    
                    // Still clear auth token and reload on error
                    localStorage.removeItem('auth_token');
                    window.location.reload();
                });
        } else {
            // If API is not available, just clear token and reload
            localStorage.removeItem('auth_token');
            window.location.reload();
        }
    }
    
    // Form submission and validation
    
    // Simple email validation
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Username validation
    function isValidUsername(username) {
        const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
        return usernameRegex.test(username);
    }
    
    // Password validation
    function isValidPassword(password) {
        // At least 8 characters, one letter, one number
        const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
        return passwordRegex.test(password);
    }
    
    // Login form validation and submission
    if (loginSubmit) {
        loginSubmit.addEventListener('click', async function(e) {
            e.preventDefault();
            
            let isValid = true;
            const usernameField = document.getElementById('loginUsername');
            const passwordField = document.getElementById('loginPassword');
            const rememberMeField = document.getElementById('rememberMe');
            
            if (!usernameField || !passwordField) {
                console.error('Login form fields not found');
                return;
            }
            
            const username = usernameField.value.trim();
            const password = passwordField.value;
            const rememberMe = rememberMeField ? rememberMeField.checked : false;
            
            // Validate username/email
            if (!username) {
                usernameField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                usernameField.parentElement.classList.remove('has-error');
            }
            
            // Validate password
            if (!password) {
                passwordField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                passwordField.parentElement.classList.remove('has-error');
            }
            
            if (isValid) {
                // Show loading state
                loginSubmit.textContent = 'Signing in...';
                loginSubmit.disabled = true;
                
                try {
                    if (api) {
                        // Call login API
                        const result = await api.makeRequest('/login', {
                            method: 'POST',
                            skipAuth: true,
                            data: {
                                login: username,
                                password: password
                            }
                        });
                        
                        if (result.status === 'success' && result.token) {
                            // Save auth token
                            api.setAuthToken(result.token);
                            
                            // Close modal
                            closeAuthModal();
                            
                            // Update UI
                            updateUIForLoggedInUser();
                            
                            // Reload page to reflect logged in state
                            window.location.reload();
                        } else {
                            throw new Error(result.message || 'Login failed');
                        }
                    } else {
                        // Mock login for demo
                        setTimeout(function() {
                            // Check if credentials are correct (this is just a simple demo)
                            if ((username === '<EMAIL>' || username === 'demo') && password === 'password123') {
                                // Mock successful login
                                localStorage.setItem('auth_token', 'mock_token_' + Date.now());
                                
                                // Close modal
                                closeAuthModal();
                                
                                // Update UI
                                updateUIForLoggedInUser();
                            } else {
                                // Show error
                                const errorElement = document.querySelector('.login-form .auth-error');
                                if (errorElement) errorElement.classList.add('show');
                            }
                            
                            // Reset button
                            loginSubmit.textContent = 'Sign In';
                            loginSubmit.disabled = false;
                        }, 1500);
                    }
                } catch (error) {
                    console.error('Login failed:', error);
                    
                    // Show error message
                    const errorElement = document.querySelector('.login-form .auth-error');
                    if (errorElement) errorElement.classList.add('show');
                    
                    // Reset button
                    loginSubmit.textContent = 'Sign In';
                    loginSubmit.disabled = false;
                }
            }
        });
    }
    
    // Register form validation and submission
    if (registerSubmit) {
        registerSubmit.addEventListener('click', async function(e) {
            e.preventDefault();
            
            let isValid = true;
            const usernameField = document.getElementById('registerUsername');
            const nameField = document.getElementById('registerName');
            const emailField = document.getElementById('registerEmail');
            const passwordField = document.getElementById('registerPassword');
            const confirmPasswordField = document.getElementById('registerConfirmPassword');
            const agreeTermsField = document.getElementById('agreeTerms');
            
            if (!usernameField || !nameField || !emailField || !passwordField || !confirmPasswordField || !agreeTermsField) {
                console.error('Register form fields not found');
                return;
            }
            
            const username = usernameField.value.trim();
            const name = nameField.value.trim();
            const email = emailField.value.trim();
            const password = passwordField.value;
            const confirmPassword = confirmPasswordField.value;
            const agreeTerms = agreeTermsField.checked;
            
            // Validate username
            if (!isValidUsername(username)) {
                usernameField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                usernameField.parentElement.classList.remove('has-error');
            }
            
            // Validate name
            if (!name) {
                nameField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                nameField.parentElement.classList.remove('has-error');
            }
            
            // Validate email
            if (!isValidEmail(email)) {
                emailField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                emailField.parentElement.classList.remove('has-error');
            }
            
            // Validate password
            if (!isValidPassword(password)) {
                passwordField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                passwordField.parentElement.classList.remove('has-error');
            }
            
            // Validate confirm password
            if (password !== confirmPassword) {
                confirmPasswordField.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                confirmPasswordField.parentElement.classList.remove('has-error');
            }
            
            // Validate terms
            if (!agreeTerms) {
                agreeTermsField.parentElement.parentElement.classList.add('has-error');
                isValid = false;
            } else {
                agreeTermsField.parentElement.parentElement.classList.remove('has-error');
            }
            
            if (isValid) {
                // Show loading state
                registerSubmit.textContent = 'Creating account...';
                registerSubmit.disabled = true;
                
                try {
                    if (api) {
                        console.log('Calling register API with data:', {
                            name: name,
                            email: email,
                            username: username,
                            password: password,
                            password_confirmation: confirmPassword
                        });
                        
                        // Call register API
                        const result = await api.makeRequest('/register', {
                            method: 'POST',
                            skipAuth: true,
                            data: {
                                name: name,
                                email: email,
                                username: username,
                                password: password,
                                password_confirmation: confirmPassword
                            }
                        });
                        
                        console.log('Register API response:', result);
                        
                        if (result.status === 'success' && result.token) {
                            // Save auth token
                            api.setAuthToken(result.token);
                            
                            // Hide the form
                            registerForm.style.display = 'none';
                            
                            // Show success animation
                            if (successCheckmark) successCheckmark.classList.add('show');
                            
                            // After animation, redirect or show welcome
                            setTimeout(function() {
                                // Close modal
                                closeAuthModal();
                                
                                // Update UI
                                updateUIForLoggedInUser();
                                
                                // Reload page to reflect logged in state
                                window.location.reload();
                            }, 2000);
                        } else {
                            throw new Error(result.message || 'Registration failed');
                        }
                    } else {
                        // Mock registration for demo
                        setTimeout(function() {
                            // Hide the form
                            registerForm.style.display = 'none';
                            
                            // Show success animation
                            if (successCheckmark) successCheckmark.classList.add('show');
                            
                            // After animation, show verification notice or redirect
                            setTimeout(function() {
                                // For demo, we'll just show the success message
                                if (successCheckmark) successCheckmark.classList.remove('show');
                                if (verificationNotice) verificationNotice.classList.add('show');
                                registerForm.style.display = 'block';
                                
                                // Reset form
                                usernameField.value = '';
                                nameField.value = '';
                                emailField.value = '';
                                passwordField.value = '';
                                confirmPasswordField.value = '';
                                agreeTermsField.checked = false;
                                
                                // Reset button
                                registerSubmit.textContent = 'Create Account';
                                registerSubmit.disabled = false;
                            }, 2000);
                        }, 1500);
                    }
                } catch (error) {
                    console.error('Registration failed:', error);
                    
                    // Show error message
                    const errorElement = document.querySelector('.register-form .auth-error');
                    if (errorElement) errorElement.classList.add('show');
                    
                    // Reset button
                    registerSubmit.textContent = 'Create Account';
                    registerSubmit.disabled = false;
                }
            }
        });
    }
    
    // Form input event listeners for real-time validation
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Remove error styling as user types
            this.parentElement.classList.remove('has-error');
            
            // Hide any displayed error messages
            const formError = this.closest('.auth-form').querySelector('.auth-error');
            if (formError && formError.classList.contains('show')) {
                formError.classList.remove('show');
            }
        });
    });
    
    // Check auth status on page load
    checkAuthStatus();
    
    console.log('Auth modal initialized successfully');
});