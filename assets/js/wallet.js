// Enhanced Wallet Page - Mobile-First Responsive Implementation
document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced Wallet Page Loading...");
    
    // Mobile detection and optimization
    const isMobile = window.innerWidth <= 767;
    const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
    const isDesktop = window.innerWidth >= 1024;
    
    // Touch and haptic support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const hasHaptic = 'vibrate' in navigator && isMobile;
    
    // Wallet state management
    const walletState = {
        balance: {
            ga: 50000,
            usd: 5000,
            eur: 4500,
            btc: 0.0
        },
        isBalanceVisible: true,
        activeCurrency: 'ga',
        transactions: [],
        filters: {
            type: 'all',
            time: 'all',
            status: 'all',
            search: ''
        },
        security: {
            twoFA: false,
            biometric: true,
            transactionPIN: true,
            notifications: true
        },
        notifications: []
    };
    
    // Cache DOM elements
    const elements = {
        balanceAmount: document.getElementById('balanceAmount'),
        toggleBalance: document.getElementById('toggleBalance'),
        quickActions: document.querySelectorAll('.quick-action'),
        currencyItems: document.querySelectorAll('.currency-item'),
        transactionFilters: {
            type: document.getElementById('typeFilter'),
            time: document.getElementById('timeFilter'),
            status: document.getElementById('statusFilter')
        },
        transactionSearch: document.querySelector('.transaction-search-input'),
        transactionList: document.querySelector('.transaction-list'),
        securityToggles: {
            twoFA: document.getElementById('2faToggle'),
            biometric: document.getElementById('bioToggle'),
            pin: document.getElementById('pinToggle'),
            notifications: document.getElementById('notifToggle')
        },
        clearNotifications: document.querySelector('.clear-notifications'),
        addPaymentMethod: document.querySelector('.add-payment-method'),
        paymentMethodBtns: document.querySelectorAll('.payment-method-btn')
    };
    
    // Sample transaction data
    const sampleTransactions = [
        {
            id: 'tx001',
            type: 'deposit',
            description: 'Credit Card',
            amount: 5000,
            currency: 'GA',
            status: 'completed',
            date: new Date('2025-06-04T10:15:00'),
            icon: 'fas fa-arrow-down',
            direction: 'incoming'
        },
        {
            id: 'tx002',
            type: 'bet',
            description: 'Crash Game',
            amount: -1000,
            currency: 'GA',
            status: 'completed',
            date: new Date('2025-06-03T20:30:00'),
            icon: 'fas fa-arrow-up',
            direction: 'outgoing'
        },
        {
            id: 'tx003',
            type: 'win',
            description: 'Crash Game',
            amount: 2500,
            currency: 'GA',
            status: 'completed',
            date: new Date('2025-06-03T20:30:00'),
            icon: 'fas fa-trophy',
            direction: 'incoming'
        },
        {
            id: 'tx004',
            type: 'transfer',
            description: 'To: JohnDoe123',
            amount: -500,
            currency: 'GA',
            status: 'completed',
            date: new Date('2025-06-02T15:45:00'),
            icon: 'fas fa-paper-plane',
            direction: 'outgoing'
        },
        {
            id: 'tx005',
            type: 'bonus',
            description: 'Welcome Bonus',
            amount: 1000,
            currency: 'GA',
            status: 'completed',
            date: new Date('2025-06-01T09:00:00'),
            icon: 'fas fa-gift',
            direction: 'incoming'
        },
        {
            id: 'tx006',
            type: 'withdrawal',
            description: 'To Bank Account',
            amount: -2000,
            currency: 'GA',
            status: 'pending',
            date: new Date('2025-05-28T14:20:00'),
            icon: 'fas fa-money-bill-wave',
            direction: 'outgoing'
        }
    ];
    
    // Initialize wallet functionality
    init();
    
    function init() {
        console.log("Initializing Enhanced Wallet...");
        
        // Load wallet data from localStorage
        loadWalletData();
        
        // Setup event listeners
        setupEventListeners();
        
        // Initialize mobile optimizations
        setupMobileOptimizations();
        
        // Load transactions
        walletState.transactions = sampleTransactions;
        updateTransactionDisplay();
        
        // Update UI
        updateBalanceDisplay();
        updateSecurityToggles();
        
        // Set active nav item
        setActiveNavItem();
        
        console.log("Wallet page initialized successfully!");
    }
    
    function loadWalletData() {
        // Load saved wallet state from localStorage
        const savedState = localStorage.getItem('walletState');
        if (savedState) {
            const parsed = JSON.parse(savedState);
            Object.assign(walletState, parsed);
        }
    }
    
    function saveWalletData() {
        // Save wallet state to localStorage
        localStorage.setItem('walletState', JSON.stringify(walletState));
    }
    
    function setupEventListeners() {
        // Balance visibility toggle
        if (elements.toggleBalance) {
            elements.toggleBalance.addEventListener('click', toggleBalanceVisibility);
        }
        
        // Quick actions
        elements.quickActions.forEach(action => {
            action.addEventListener('click', handleQuickAction);
        });
        
        // Currency selection
        elements.currencyItems.forEach(item => {
            if (!item.classList.contains('coming-soon')) {
                item.addEventListener('click', () => selectCurrency(item));
            }
        });
        
        // Transaction filters
        Object.values(elements.transactionFilters).forEach(filter => {
            if (filter) {
                filter.addEventListener('change', handleFilterChange);
            }
        });
        
        // Transaction search
        if (elements.transactionSearch) {
            elements.transactionSearch.addEventListener('input', debounce(handleSearchChange, 300));
        }
        
        // Security toggles
        Object.entries(elements.securityToggles).forEach(([key, toggle]) => {
            if (toggle) {
                toggle.addEventListener('click', () => toggleSecurity(key, toggle));
            }
        });
        
        // Clear notifications
        if (elements.clearNotifications) {
            elements.clearNotifications.addEventListener('click', clearAllNotifications);
        }
        
        // Add payment method - redirect to Telegram bot
        if (elements.addPaymentMethod) {
            elements.addPaymentMethod.addEventListener('click', function() {
                const telegramBotURL = 'https://t.me/Goldenaura_PY_bot';
                const message = encodeURIComponent('Hi! I want to add a new payment method to my GoldenAura account.');
                window.open(`${telegramBotURL}?start=add_payment&text=${message}`, '_blank');
            });
        }
        
        // Payment method buttons
        elements.paymentMethodBtns.forEach(btn => {
            btn.addEventListener('click', handlePaymentMethodAction);
        });
        
        // Window resize
        window.addEventListener('resize', debounce(handleResize, 250));
        
        // Transaction item clicks
        setupTransactionClickHandlers();
    }
    
    function setupMobileOptimizations() {
        // Add device classes
        document.body.classList.toggle('mobile', isMobile);
        document.body.classList.toggle('tablet', isTablet);
        document.body.classList.toggle('desktop', isDesktop);
        document.body.classList.toggle('touch-device', hasTouch);
        
        // Mobile-specific touch interactions
        if (isMobile) {
            setupMobileTouchInteractions();
        }
    }
    
    function setupMobileTouchInteractions() {
        // Enhanced touch feedback for interactive elements
        const touchElements = document.querySelectorAll(`
            .quick-action, .currency-item, .transaction-item,
            .security-toggle, .payment-method-btn, .notification
        `);
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                if (!element.classList.contains('coming-soon')) {
                    element.style.transform = 'scale(0.98)';
                    element.style.transition = 'transform 0.1s ease';
                    triggerHapticFeedback('light');
                }
            }, { passive: true });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = 'transform 0.3s ease';
                }, 100);
            }, { passive: true });
        });
    }
    
    function toggleBalanceVisibility() {
        walletState.isBalanceVisible = !walletState.isBalanceVisible;
        updateBalanceDisplay();
        
        // Update toggle icon
        const icon = elements.toggleBalance.querySelector('i');
        if (walletState.isBalanceVisible) {
            icon.className = 'fas fa-eye-slash';
        } else {
            icon.className = 'fas fa-eye';
        }
        
        triggerHapticFeedback('light');
        saveWalletData();
    }
    
    function updateBalanceDisplay() {
        if (!elements.balanceAmount) return;
        
        const balance = walletState.balance[walletState.activeCurrency];
        const currency = walletState.activeCurrency.toUpperCase();
        
        if (walletState.isBalanceVisible) {
            elements.balanceAmount.textContent = `${balance.toLocaleString()} ${currency}`;
            elements.balanceAmount.classList.remove('hidden');
        } else {
            elements.balanceAmount.textContent = '••••••';
            elements.balanceAmount.classList.add('hidden');
        }
    }
    
    function handleQuickAction(e) {
        const action = e.currentTarget.id;
        
        triggerHapticFeedback('medium');
        
        // Telegram Bot URL
        const telegramBotURL = 'https://t.me/Goldenaura_PY_bot';
        
        // Function to redirect to Telegram bot with message
        function redirectToTelegramBot(actionType) {
            const messages = {
                'addFunds': 'Hi! I want to deposit funds to my GoldenAura account.',
                'sendMoney': 'Hi! I want to send money from my GoldenAura account.',
                'receiveMoney': 'Hi! I want to receive money to my GoldenAura account.',
                'withdrawFunds': 'Hi! I want to withdraw funds from my GoldenAura account.',
                'telegramBot': 'Hi! I need help with my GoldenAura wallet.'
            };
            
            const message = encodeURIComponent(messages[actionType] || messages['telegramBot']);
            window.open(`${telegramBotURL}?start=${actionType}&text=${message}`, '_blank');
        }
        
        switch (action) {
            case 'addFunds':
                redirectToTelegramBot('addFunds');
                break;
            case 'sendMoney':
                redirectToTelegramBot('sendMoney');
                break;
            case 'receiveMoney':
                redirectToTelegramBot('receiveMoney');
                break;
            case 'withdrawFunds':
                redirectToTelegramBot('withdrawFunds');
                break;
            case 'telegramBot':
                redirectToTelegramBot('telegramBot');
                break;
        }
    }
    
    function selectCurrency(item) {
        // Remove active class from all currency items
        elements.currencyItems.forEach(curr => {
            curr.classList.remove('active');
        });
        
        // Add active class to selected item
        item.classList.add('active');
        
        // Update active currency (simplified)
        const currencyName = item.querySelector('.currency-name').textContent.toLowerCase();
        if (currencyName.includes('golden')) {
            walletState.activeCurrency = 'ga';
        } else if (currencyName.includes('usd')) {
            walletState.activeCurrency = 'usd';
        } else if (currencyName.includes('eur')) {
            walletState.activeCurrency = 'eur';
        }
        
        updateBalanceDisplay();
        triggerHapticFeedback('light');
        saveWalletData();
    }
    
    function handleFilterChange(e) {
        const filterType = e.target.id.replace('Filter', '');
        const value = e.target.value.toLowerCase().replace(/\s+/g, '-');
        
        walletState.filters[filterType] = value;
        updateTransactionDisplay();
        triggerHapticFeedback('light');
    }
    
    function handleSearchChange(e) {
        walletState.filters.search = e.target.value.toLowerCase();
        updateTransactionDisplay();
    }
    
    function updateTransactionDisplay() {
        if (!elements.transactionList) return;
        
        const filteredTransactions = filterTransactions(walletState.transactions);
        const html = filteredTransactions.map(transaction => createTransactionHTML(transaction)).join('');
        
        elements.transactionList.innerHTML = html;
        
        // Re-setup click handlers
        setupTransactionClickHandlers();
        
        // Show empty state if no transactions
        if (filteredTransactions.length === 0) {
            showEmptyTransactionState();
        }
    }
    
    function filterTransactions(transactions) {
        return transactions.filter(transaction => {
            // Type filter
            if (walletState.filters.type !== 'all') {
                const filterType = walletState.filters.type;
                if (filterType === 'deposits' && transaction.direction !== 'incoming') return false;
                if (filterType === 'withdrawals' && transaction.type !== 'withdrawal') return false;
                if (filterType === 'transfers' && transaction.type !== 'transfer') return false;
                if (filterType === 'bets' && transaction.type !== 'bet') return false;
                if (filterType === 'winnings' && transaction.type !== 'win') return false;
            }
            
            // Status filter
            if (walletState.filters.status !== 'all' && 
                transaction.status !== walletState.filters.status) {
                return false;
            }
            
            // Time filter
            if (walletState.filters.time !== 'all') {
                const now = new Date();
                const transactionDate = new Date(transaction.date);
                const timeDiff = now - transactionDate;
                
                switch (walletState.filters.time) {
                    case 'last-24-hours':
                        if (timeDiff > 24 * 60 * 60 * 1000) return false;
                        break;
                    case 'last-7-days':
                        if (timeDiff > 7 * 24 * 60 * 60 * 1000) return false;
                        break;
                    case 'last-30-days':
                        if (timeDiff > 30 * 24 * 60 * 60 * 1000) return false;
                        break;
                }
            }
            
            // Search filter
            if (walletState.filters.search && 
                !transaction.description.toLowerCase().includes(walletState.filters.search) &&
                !transaction.type.toLowerCase().includes(walletState.filters.search)) {
                return false;
            }
            
            return true;
        });
    }
    
    function createTransactionHTML(transaction) {
        const date = new Date(transaction.date);
        const formattedDate = date.toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        }) + ' • ' + date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
        
        const amountPrefix = transaction.amount > 0 ? '+' : '';
        const amountClass = transaction.direction;
        
        return `
            <div class="transaction-item" data-transaction-id="${transaction.id}">
                <div class="transaction-left">
                    <div class="transaction-icon ${transaction.direction}">
                        <i class="${transaction.icon}"></i>
                    </div>
                    <div class="transaction-details">
                        <div class="transaction-type">${capitalizeFirst(transaction.type)}</div>
                        <div class="transaction-desc">${transaction.description}</div>
                        <div class="transaction-date">${formattedDate}</div>
                    </div>
                </div>
                <div class="transaction-right">
                    <div class="transaction-amount ${amountClass}">
                        ${amountPrefix}${Math.abs(transaction.amount).toLocaleString()} ${transaction.currency}
                    </div>
                    <div class="transaction-status ${transaction.status}">${capitalizeFirst(transaction.status)}</div>
                </div>
            </div>
        `;
    }
    
    function setupTransactionClickHandlers() {
        const transactionItems = document.querySelectorAll('.transaction-item');
        transactionItems.forEach(item => {
            item.addEventListener('click', () => {
                const transactionId = item.dataset.transactionId;
                openTransactionDetails(transactionId);
                triggerHapticFeedback('medium');
            });
        });
    }
    
    function showEmptyTransactionState() {
        elements.transactionList.innerHTML = `
            <div class="empty-transaction-state">
                <div class="empty-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No transactions found</h3>
                <p>Try adjusting your filters or search terms</p>
            </div>
        `;
    }
    
    function toggleSecurity(key, toggle) {
        walletState.security[key] = !walletState.security[key];
        toggle.classList.toggle('active', walletState.security[key]);
        
        // Show confirmation for critical security changes
        if (key === 'twoFA' && walletState.security[key]) {
            showSecurityConfirmation('Two-Factor Authentication enabled');
        }
        
        triggerHapticFeedback('medium');
        saveWalletData();
    }
    
    function updateSecurityToggles() {
        Object.entries(elements.securityToggles).forEach(([key, toggle]) => {
            if (toggle && walletState.security[key] !== undefined) {
                toggle.classList.toggle('active', walletState.security[key]);
            }
        });
    }
    
    function clearAllNotifications() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach((notification, index) => {
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-out forwards';
                setTimeout(() => notification.remove(), 300);
            }, index * 100);
        });
        
        triggerHapticFeedback('medium');
        showToast('All notifications cleared', 'success');
    }
    
    function addPaymentMethod() {
        // In a real app, this would open a payment method modal
        showToast('Add payment method feature coming soon!', 'info');
        triggerHapticFeedback('medium');
    }
    
    function handlePaymentMethodAction(e) {
        const action = e.target.textContent.toLowerCase();
        const isRemove = action.includes('remove');
        
        if (isRemove) {
            showToast('Payment method removed', 'success');
        } else {
            showToast('Payment method updated', 'success');
        }
        
        triggerHapticFeedback('light');
    }
    
    function openDepositModal() {
        // In a real app, this would open a deposit modal
        showToast('Deposit modal coming soon!', 'info');
        console.log('Opening deposit modal...');
    }
    
    function openSendModal() {
        // In a real app, this would open a send money modal
        showToast('Send money modal coming soon!', 'info');
        console.log('Opening send modal...');
    }
    
    function openReceiveModal() {
        // In a real app, this would open a receive money modal with QR code
        showToast('Receive money modal coming soon!', 'info');
        console.log('Opening receive modal...');
    }
    
    function openWithdrawModal() {
        // In a real app, this would open a withdrawal modal
        showToast('Withdrawal modal coming soon!', 'info');
        console.log('Opening withdraw modal...');
    }
    
    function openTransactionDetails(transactionId) {
        // In a real app, this would open transaction details modal
        const transaction = walletState.transactions.find(t => t.id === transactionId);
        if (transaction) {
            showToast(`Transaction: ${transaction.type} - ${Math.abs(transaction.amount)} ${transaction.currency}`, 'info');
        }
        console.log('Opening transaction details for:', transactionId);
    }
    
    function showSecurityConfirmation(message) {
        showToast(message, 'success');
    }
    
    function setActiveNavItem() {
        // Set wallet nav item as active
        const walletNavItem = document.querySelector('.nav-item[href="wallet.html"]');
        if (walletNavItem) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            walletNavItem.classList.add('active');
        }
    }
    
    function handleResize() {
        const newIsMobile = window.innerWidth <= 767;
        const newIsTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
        const newIsDesktop = window.innerWidth >= 1024;
        
        // Update device classes
        document.body.classList.toggle('mobile', newIsMobile);
        document.body.classList.toggle('tablet', newIsTablet);
        document.body.classList.toggle('desktop', newIsDesktop);
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!hasHaptic || !isMobile) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function showToast(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        // Add to DOM
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
    
    function capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Add CSS for toast notifications
    const toastStyle = document.createElement('style');
    toastStyle.textContent = `
        .toast {
            position: fixed;
            top: calc(var(--mobile-header-height) + 20px);
            right: 20px;
            background: var(--wallet-success);
            color: white;
            padding: 12px 16px;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
        }
        
        .toast.show {
            transform: translateX(0);
        }
        
        .toast.error {
            background: var(--wallet-danger);
        }
        
        .toast.warning {
            background: var(--wallet-warning);
        }
        
        .toast.info {
            background: var(--wallet-info);
        }
        
        .empty-transaction-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-color-muted);
        }
        
        .empty-transaction-state .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
        
        .empty-transaction-state h3 {
            font-size: 18px;
            color: var(--text-color);
            margin-bottom: 8px;
        }
        
        @keyframes slideOut {
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    `;
    document.head.appendChild(toastStyle);
    
    // Public API
    window.WalletPage = {
        toggleBalance: toggleBalanceVisibility,
        addTransaction: (transaction) => {
            walletState.transactions.unshift(transaction);
            updateTransactionDisplay();
        },
        updateBalance: (currency, amount) => {
            walletState.balance[currency] = amount;
            updateBalanceDisplay();
            saveWalletData();
        },
        triggerHapticFeedback,
        state: () => ({ ...walletState })
    };
    
    console.log("Enhanced Wallet Page Ready! 💰");
});