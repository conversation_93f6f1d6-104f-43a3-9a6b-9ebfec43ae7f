// Enhanced Promotions Page - Mobile-First Responsive Implementation
document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced Promotions Page Loading...");
    
    // Mobile detection and optimization
    const isMobile = window.innerWidth <= 767;
    const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
    const isDesktop = window.innerWidth >= 1024;
    
    // Touch and haptic support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const hasHaptic = 'vibrate' in navigator && isMobile;
    
    // Promotions state management
    const promotionsState = {
        activeFilter: 'all',
        userTier: 'gold',
        claimedPromotions: JSON.parse(localStorage.getItem('claimedPromotions')) || [],
        favoritePromotions: JSON.parse(localStorage.getItem('favoritePromotions')) || [],
        notifications: JSON.parse(localStorage.getItem('promoNotifications')) || [],
        loyaltyPoints: parseInt(localStorage.getItem('loyaltyPoints')) || 75000,
        isAnimating: false
    };
    
    // Cache DOM elements
    const elements = {
        filterButtons: document.querySelectorAll('.filter-button'),
        promotionCards: document.querySelectorAll('.promotion-card'),
        featuredCards: document.querySelectorAll('.featured-card'),
        categoryCards: document.querySelectorAll('.category-card'),
        loyaltyTiers: document.querySelectorAll('.loyalty-tier'),
        ctaButtons: document.querySelectorAll('.cta-button, .cta-secondary'),
        cardButtons: document.querySelectorAll('.card-button'),
        promotionButtons: document.querySelectorAll('.promotion-button'),
        comingSoonBanner: document.querySelector('.coming-soon-banner'),
        heroTitle: document.querySelector('.hero-title'),
        loyaltySection: document.querySelector('.loyalty-section')
    };
    
    // Sample promotional data
    const promotionalData = {
        featured: [
            {
                id: 'welcome-bonus',
                title: 'Welcome Bonus Package',
                type: 'Casino',
                value: 'GA 10,000',
                description: 'Get started with a bang! Claim your 100% welcome bonus up to GA 10,000 on your first deposit.',
                minDeposit: 'GA 500',
                wagering: '20x',
                expires: '2 Days',
                status: 'coming-soon',
                categories: ['casino', 'deposit']
            },
            {
                id: 'crash-tournament',
                title: 'Crash Masters Tournament',
                type: 'Tournament',
                value: 'GA 100,000',
                description: 'Compete with other players in our Crash game tournament and win up to GA 100,000!',
                entryFee: 'GA 1,000',
                players: '--',
                launchDate: 'TBA',
                status: 'coming-soon',
                categories: ['casino', 'limited']
            },
            {
                id: 'referral-program',
                title: 'Referral Program',
                type: 'Referral',
                value: 'GA 1,000',
                description: 'Invite your friends to GoldenAura and both of you will receive GA 1,000 when they make their first deposit.',
                yourBonus: 'GA 1,000',
                friendBonus: 'GA 1,000',
                launchDate: 'TBA',
                status: 'coming-soon',
                categories: ['referrals']
            }
        ],
        regular: [
            {
                id: 'first-deposit',
                title: 'First Deposit Bonus',
                type: 'Casino',
                value: '100% up to GA 10,000',
                description: 'Start your GoldenAura journey with a 100% match on your first deposit. Double your playing power instantly!',
                terms: '20x wagering requirement. Min deposit GA 500. Valid for 30 days after activation.',
                status: 'coming-soon',
                categories: ['casino', 'deposit']
            },
            {
                id: 'french-open-special',
                title: 'French Open Special',
                type: 'Sports',
                value: 'Free Bet up to GA 500',
                description: 'Place a bet on any French Open match and get a free bet of equal value up to GA 500!',
                terms: 'Min odds 1.5. Free bet valid for 7 days. Stake not returned with winnings.',
                status: 'coming-soon',
                categories: ['sports']
            },
            {
                id: 'weekly-cashback',
                title: 'Weekly Cashback',
                type: 'Casino',
                value: '10% up to GA 2,000',
                description: 'Receive 10% cashback on all your net losses each week, automatically credited to your account every Monday.',
                terms: 'Min loss GA 1,000. Cashback paid as cash with no wagering requirements.',
                status: 'coming-soon',
                categories: ['casino', 'cashback']
            },
            {
                id: 'sport-raffle',
                title: 'GoldenAura\'s Sport Raffle',
                type: 'Limited',
                value: 'GA 50,000 Prize Pool',
                description: 'Every GA 100 bet on sports gives you one raffle ticket. Win a share of our GA 50,000 prize pool!',
                terms: 'Min bet GA 100. Winners announced on July 1, 2025. Prizes credited as cash.',
                status: 'coming-soon',
                categories: ['sports', 'limited']
            },
            {
                id: 'weekend-reload',
                title: 'Weekend Reload',
                type: 'Casino',
                value: '50 Free Spins',
                description: 'Deposit GA 1,000 or more during the weekend and get 50 free spins on our featured slot game.',
                terms: 'Min deposit GA 1,000. 20x wagering on winnings. Valid Fri-Sun only.',
                status: 'coming-soon',
                categories: ['casino', 'deposit']
            },
            {
                id: 'mobile-app-bonus',
                title: 'Mobile App Download Bonus',
                type: 'No Deposit',
                value: 'GA 50 Free',
                description: 'Download our mobile app and receive GA 50 free credit just for signing in on your mobile device.',
                terms: 'For new mobile users only. 30x wagering. Max withdrawal GA 500.',
                status: 'coming-soon',
                categories: ['no-deposit']
            }
        ],
        categories: {
            'welcome-bonuses': { count: 3, icon: 'fas fa-gift' },
            'reload-bonuses': { count: 5, icon: 'fas fa-redo' },
            'tournaments': { count: 7, icon: 'fas fa-trophy' },
            'sports-promotions': { count: 4, icon: 'fas fa-futbol' },
            'cashback': { count: 2, icon: 'fas fa-coins' },
            'referrals': { count: 1, icon: 'fas fa-users' }
        }
    };
    
    // Loyalty tier information
    const loyaltyTiers = [
        { name: 'Bronze', min: 0, max: 10000, cashback: 5 },
        { name: 'Silver', min: 10001, max: 50000, cashback: 7 },
        { name: 'Gold', min: 50001, max: 100000, cashback: 10 },
        { name: 'Platinum', min: 100001, max: Infinity, cashback: 15 }
    ];
    
    // Initialize promotions functionality
    init();
    
    function init() {
        console.log("Initializing Enhanced Promotions...");
        
        // Setup event listeners
        setupEventListeners();
        
        // Initialize mobile optimizations
        setupMobileOptimizations();
        
        // Setup promotional animations
        setupPromotionalAnimations();
        
        // Update loyalty tier display
        updateLoyaltyTierDisplay();
        
        // Setup intersection observers for animations
        setupIntersectionObservers();
        
        // Set active nav item
        setActiveNavItem();
        
        // Load user preferences
        loadUserPreferences();
        
        console.log("Promotions page initialized successfully!");
    }
    
    function setupEventListeners() {
        // Filter buttons
        elements.filterButtons.forEach(button => {
            button.addEventListener('click', () => handleFilterChange(button));
        });
        
        // Featured card interactions
        elements.featuredCards.forEach(card => {
            card.addEventListener('click', () => handleFeaturedCardClick(card));
        });
        
        // Category card interactions
        elements.categoryCards.forEach(card => {
            card.addEventListener('click', () => handleCategoryClick(card));
        });
        
        // CTA button interactions
        elements.ctaButtons.forEach(button => {
            button.addEventListener('click', () => handleCtaClick(button));
        });
        
        // Card button interactions
        elements.cardButtons.forEach(button => {
            button.addEventListener('click', (e) => handleCardButtonClick(e, button));
        });
        
        // Promotion button interactions
        elements.promotionButtons.forEach(button => {
            button.addEventListener('click', (e) => handlePromotionButtonClick(e, button));
        });
        
        // Coming soon banner interaction
        if (elements.comingSoonBanner) {
            elements.comingSoonBanner.addEventListener('click', handleComingSoonClick);
        }
        
        // Window resize
        window.addEventListener('resize', debounce(handleResize, 250));
        
        // Scroll handling for animations
        window.addEventListener('scroll', debounce(handleScroll, 16));
    }
    
    function setupMobileOptimizations() {
        // Add device classes
        document.body.classList.toggle('mobile', isMobile);
        document.body.classList.toggle('tablet', isTablet);
        document.body.classList.toggle('desktop', isDesktop);
        document.body.classList.toggle('touch-device', hasTouch);
        
        // Mobile-specific touch interactions
        if (isMobile) {
            setupMobileTouchInteractions();
        }
        
        // Optimize filter buttons for mobile
        if (isMobile) {
            optimizeFiltersForMobile();
        }
    }
    
    function setupMobileTouchInteractions() {
        // Enhanced touch feedback for interactive elements
        const touchElements = document.querySelectorAll(`
            .filter-button, .featured-card, .category-card, .promotion-card,
            .card-button, .promotion-button, .cta-button, .loyalty-tier
        `);
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                if (!element.classList.contains('disabled')) {
                    element.style.transform = 'scale(0.98)';
                    element.style.transition = 'transform 0.1s ease';
                    triggerHapticFeedback('light');
                }
            }, { passive: true });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = 'transform 0.3s ease';
                }, 100);
            }, { passive: true });
        });
    }
    
    function setupPromotionalAnimations() {
        // Animate hero title with typing effect
        if (elements.heroTitle) {
            animateHeroTitle();
        }
        
        // Setup card hover animations
        setupCardAnimations();
        
        // Setup coming soon pulse animation
        setupComingSoonAnimations();
        
        // Setup loyalty tier glow effects
        setupLoyaltyAnimations();
    }
    
    function animateHeroTitle() {
        const text = elements.heroTitle.textContent;
        elements.heroTitle.textContent = '';
        
        let index = 0;
        const typeInterval = setInterval(() => {
            elements.heroTitle.textContent += text[index];
            index++;
            
            if (index >= text.length) {
                clearInterval(typeInterval);
                elements.heroTitle.classList.add('typing-complete');
            }
        }, 50);
    }
    
    function setupCardAnimations() {
        // Add staggered animation to featured cards
        elements.featuredCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-on-load');
        });
        
        // Add staggered animation to category cards
        elements.categoryCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.05}s`;
            card.classList.add('animate-on-load');
        });
    }
    
    function setupComingSoonAnimations() {
        // Add pulse animation to coming soon elements
        const comingSoonElements = document.querySelectorAll('.card-badge, .coming-soon');
        comingSoonElements.forEach(element => {
            element.style.animation = 'pulse 2s infinite';
        });
    }
    
    function setupLoyaltyAnimations() {
        // Add glow effect to active loyalty tier
        const activeTier = document.querySelector('.loyalty-tier.active');
        if (activeTier) {
            activeTier.style.animation = 'loyaltyGlow 3s ease-in-out infinite alternate';
        }
    }
    
    function setupIntersectionObservers() {
        const observerOptions = {
            root: null,
            rootMargin: '10px',
            threshold: 0.1
        };
        
        const fadeInObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-visible');
                    fadeInObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe sections for fade-in animation
        const sections = document.querySelectorAll('.featured-promotions, .categories-section, .regular-promotions, .loyalty-section');
        sections.forEach(section => {
            section.classList.add('fade-in-element');
            fadeInObserver.observe(section);
        });
    }
    
    function handleFilterChange(button) {
        if (promotionsState.isAnimating) return;
        
        // Remove active class from all buttons
        elements.filterButtons.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to clicked button
        button.classList.add('active');
        
        // Update state
        const newFilter = button.textContent.toLowerCase().replace(/\s+/g, '-');
        promotionsState.activeFilter = newFilter;
        
        // Filter promotions
        filterPromotions(newFilter);
        
        // Haptic feedback
        triggerHapticFeedback('medium');
        
        // Save preference
        localStorage.setItem('activePromoFilter', newFilter);
    }
    
    function filterPromotions(filter) {
        promotionsState.isAnimating = true;
        
        elements.promotionCards.forEach((card, index) => {
            const shouldShow = filter === 'all' || 
                              card.dataset.categories?.includes(filter) ||
                              card.dataset.type?.toLowerCase().includes(filter);
            
            if (shouldShow) {
                setTimeout(() => {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.5s ease-out forwards';
                }, index * 50);
            } else {
                card.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    card.style.display = 'none';
                }, 300);
            }
        });
        
        setTimeout(() => {
            promotionsState.isAnimating = false;
        }, elements.promotionCards.length * 50 + 500);
    }
    
    function handleFeaturedCardClick(card) {
        const promoId = card.dataset.promoId || 'featured-promo';
        
        // Show promotion details
        showPromotionDetails(promoId);
        
        triggerHapticFeedback('medium');
    }
    
    function handleCategoryClick(card) {
        const categoryName = card.querySelector('.category-title').textContent.toLowerCase().replace(/\s+/g, '-');
        
        // Update filter to show category
        const filterButton = Array.from(elements.filterButtons).find(btn => 
            btn.textContent.toLowerCase().includes(categoryName.split('-')[0])
        );
        
        if (filterButton) {
            handleFilterChange(filterButton);
        }
        
        triggerHapticFeedback('medium');
        showToast(`Showing ${card.querySelector('.category-title').textContent}`, 'info');
    }
    
    function handleCtaClick(button) {
        const buttonText = button.textContent.toLowerCase();
        
        if (buttonText.includes('notify')) {
            handleNotificationSignup();
        } else if (buttonText.includes('loyalty')) {
            showLoyaltyDetails();
        } else {
            showToast('Feature coming soon!', 'info');
        }
        
        triggerHapticFeedback('medium');
    }
    
    function handleCardButtonClick(e, button) {
        e.stopPropagation();
        
        const buttonText = button.textContent.toLowerCase();
        
        if (buttonText.includes('notify')) {
            handleNotificationSignup();
        } else if (buttonText.includes('claim')) {
            handlePromotionClaim(button);
        } else {
            showToast('Feature coming soon!', 'info');
        }
        
        triggerHapticFeedback('medium');
    }
    
    function handlePromotionButtonClick(e, button) {
        e.stopPropagation();
        
        if (button.classList.contains('disabled')) {
            showToast('This promotion is coming soon!', 'info');
            return;
        }
        
        const card = button.closest('.promotion-card');
        const promoId = card.dataset.promoId || 'promo';
        
        handlePromotionClaim(button, promoId);
        triggerHapticFeedback('success');
    }
    
    function handleComingSoonClick() {
        handleNotificationSignup();
        triggerHapticFeedback('medium');
    }
    
    function handleNotificationSignup() {
        const email = prompt('Enter your email to get notified when promotions go live:');
        
        if (email && isValidEmail(email)) {
            // Add to notifications list
            if (!promotionsState.notifications.includes(email)) {
                promotionsState.notifications.push(email);
                localStorage.setItem('promoNotifications', JSON.stringify(promotionsState.notifications));
                showToast('You\'ll be notified when promotions are available!', 'success');
            } else {
                showToast('You\'re already signed up for notifications!', 'info');
            }
        } else if (email) {
            showToast('Please enter a valid email address', 'error');
        }
    }
    
    function handlePromotionClaim(button, promoId = null) {
        if (button.classList.contains('disabled')) {
            showToast('This promotion is not available yet', 'warning');
            return;
        }
        
        // Add claiming animation
        button.classList.add('claiming');
        button.textContent = 'Claiming...';
        
        setTimeout(() => {
            if (promoId && !promotionsState.claimedPromotions.includes(promoId)) {
                promotionsState.claimedPromotions.push(promoId);
                localStorage.setItem('claimedPromotions', JSON.stringify(promotionsState.claimedPromotions));
                
                button.textContent = 'Claimed!';
                button.classList.add('claimed');
                showToast('Promotion claimed successfully!', 'success');
            } else {
                showToast('Promotion coming soon!', 'info');
                button.textContent = 'Coming Soon';
            }
            
            button.classList.remove('claiming');
        }, 1500);
    }
    
    function showPromotionDetails(promoId) {
        // In a real app, this would open a detailed modal
        showToast(`Opening details for promotion: ${promoId}`, 'info');
        console.log('Opening promotion details for:', promoId);
    }
    
    function showLoyaltyDetails() {
        const currentTier = getCurrentLoyaltyTier();
        const nextTier = getNextLoyaltyTier();
        
        let message = `You're currently ${currentTier.name} tier with ${promotionsState.loyaltyPoints.toLocaleString()} points.`;
        
        if (nextTier) {
            const pointsNeeded = nextTier.min - promotionsState.loyaltyPoints;
            message += ` You need ${pointsNeeded.toLocaleString()} more points to reach ${nextTier.name} tier.`;
        } else {
            message += ' You\'ve reached the highest tier!';
        }
        
        showToast(message, 'info');
    }
    
    function updateLoyaltyTierDisplay() {
        const currentTier = getCurrentLoyaltyTier();
        
        elements.loyaltyTiers.forEach((tierElement, index) => {
            const tier = loyaltyTiers[index];
            
            if (tier.name.toLowerCase() === currentTier.name.toLowerCase()) {
                tierElement.classList.add('active');
            } else {
                tierElement.classList.remove('active');
            }
        });
    }
    
    function getCurrentLoyaltyTier() {
        return loyaltyTiers.find(tier => 
            promotionsState.loyaltyPoints >= tier.min && promotionsState.loyaltyPoints <= tier.max
        ) || loyaltyTiers[0];
    }
    
    function getNextLoyaltyTier() {
        const currentTier = getCurrentLoyaltyTier();
        const currentIndex = loyaltyTiers.indexOf(currentTier);
        return loyaltyTiers[currentIndex + 1] || null;
    }
    
    function optimizeFiltersForMobile() {
        const filtersContainer = document.querySelector('.promotions-filters');
        if (filtersContainer) {
            // Add swipe indicators for mobile
            filtersContainer.style.position = 'relative';
            
            if (filtersContainer.scrollWidth > filtersContainer.clientWidth) {
                // Add gradient indicators for scrollable content
                const gradientLeft = document.createElement('div');
                gradientLeft.className = 'scroll-indicator left';
                gradientLeft.style.cssText = `
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 20px;
                    background: linear-gradient(to right, var(--bg-color), transparent);
                    pointer-events: none;
                    z-index: 1;
                `;
                
                const gradientRight = document.createElement('div');
                gradientRight.className = 'scroll-indicator right';
                gradientRight.style.cssText = `
                    position: absolute;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    width: 20px;
                    background: linear-gradient(to left, var(--bg-color), transparent);
                    pointer-events: none;
                    z-index: 1;
                `;
                
                filtersContainer.parentElement.appendChild(gradientLeft);
                filtersContainer.parentElement.appendChild(gradientRight);
            }
        }
    }
    
    function loadUserPreferences() {
        // Load saved filter
        const savedFilter = localStorage.getItem('activePromoFilter');
        if (savedFilter) {
            const filterButton = Array.from(elements.filterButtons).find(btn => 
                btn.textContent.toLowerCase().replace(/\s+/g, '-') === savedFilter
            );
            if (filterButton) {
                handleFilterChange(filterButton);
            }
        }
        
        // Update claimed promotions display
        updateClaimedPromotionsDisplay();
    }
    
    function updateClaimedPromotionsDisplay() {
        promotionsState.claimedPromotions.forEach(promoId => {
            const button = document.querySelector(`[data-promo-id="${promoId}"] .promotion-button`);
            if (button) {
                button.textContent = 'Claimed!';
                button.classList.add('claimed');
                button.disabled = true;
            }
        });
    }
    
    function setActiveNavItem() {
        // Set promotions nav item as active
        const promotionsNavItem = document.querySelector('.nav-item[href="promotions.html"]');
        if (promotionsNavItem) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            promotionsNavItem.classList.add('active');
        }
    }
    
    function handleResize() {
        const newIsMobile = window.innerWidth <= 767;
        const newIsTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
        const newIsDesktop = window.innerWidth >= 1024;
        
        // Update device classes
        document.body.classList.toggle('mobile', newIsMobile);
        document.body.classList.toggle('tablet', newIsTablet);
        document.body.classList.toggle('desktop', newIsDesktop);
        
        // Re-optimize filters for mobile if needed
        if (newIsMobile && !isMobile) {
            optimizeFiltersForMobile();
        }
    }
    
    function handleScroll() {
        // Add scroll-based animations or effects here
        const scrollY = window.scrollY;
        
        // Parallax effect for hero section
        const hero = document.querySelector('.promotions-hero');
        if (hero) {
            const scrolled = scrollY * 0.5;
            hero.style.transform = `translateY(${scrolled}px)`;
        }
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!hasHaptic || !isMobile) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function showToast(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `promo-toast ${type}`;
        toast.textContent = message;
        
        // Add to DOM
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Add CSS for toast notifications and animations
    const promotionsStyle = document.createElement('style');
    promotionsStyle.textContent = `
        .promo-toast {
            position: fixed;
            top: calc(var(--mobile-header-height) + 20px);
            right: 20px;
            background: var(--promo-primary);
            color: white;
            padding: 12px 16px;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
        }
        
        .promo-toast.show {
            transform: translateX(0);
        }
        
        .promo-toast.error {
            background: var(--promo-danger);
        }
        
        .promo-toast.warning {
            background: var(--promo-warning);
        }
        
        .promo-toast.info {
            background: var(--promo-info);
        }
        
        .promo-toast.success {
            background: var(--promo-success);
        }
        
        .animate-on-load {
            animation: slideInUp 0.6s ease-out forwards;
        }
        
        .fade-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        
        .fade-in-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .claiming {
            position: relative;
            pointer-events: none;
        }
        
        .claiming::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            border: 2px solid currentColor;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .claimed {
            background: var(--promo-success) !important;
            border-color: var(--promo-success) !important;
        }
        
        .typing-complete {
            border-right: 2px solid var(--promo-primary);
            animation: blink 1s infinite;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }
        
        @keyframes spin {
            to {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        @keyframes loyaltyGlow {
            from {
                box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
            }
            to {
                box-shadow: 0 8px 40px rgba(255, 215, 0, 0.4);
            }
        }
        
        @media (max-width: 767px) {
            .promo-toast {
                right: 16px;
                left: 16px;
                max-width: none;
            }
        }
    `;
    document.head.appendChild(promotionsStyle);
    
    // Public API
    window.PromotionsPage = {
        claimPromotion: (promoId) => {
            const button = document.querySelector(`[data-promo-id="${promoId}"] .promotion-button`);
            if (button) {
                handlePromotionClaim(button, promoId);
            }
        },
        filterPromotions: (filter) => {
            const filterButton = Array.from(elements.filterButtons).find(btn => 
                btn.textContent.toLowerCase().replace(/\s+/g, '-') === filter
            );
            if (filterButton) {
                handleFilterChange(filterButton);
            }
        },
        addLoyaltyPoints: (points) => {
            promotionsState.loyaltyPoints += points;
            localStorage.setItem('loyaltyPoints', promotionsState.loyaltyPoints.toString());
            updateLoyaltyTierDisplay();
        },
        triggerHapticFeedback,
        state: () => ({ ...promotionsState })
    };
    
    console.log("Enhanced Promotions Page Ready! 🎁");
});