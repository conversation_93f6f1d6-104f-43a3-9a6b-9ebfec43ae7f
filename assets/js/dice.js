// Dice Rush Game JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Game elements
    const diceContainer = document.getElementById('diceContainer');
    const balanceValue = document.getElementById('balanceValue');
    const mobileBalanceValue = document.getElementById('mobileBalanceValue');
    const scoreValue = document.getElementById('scoreValue');
    const mobileScoreValue = document.getElementById('mobileScoreValue');
    const multiplierValue = document.getElementById('multiplierValue');
    const turnsValue = document.getElementById('turnsValue');
    const gameStatus = document.getElementById('gameStatus');
    const targetCards = document.getElementById('targetCards');
    const rollBtn = document.getElementById('rollBtn');
    const mobileRollBtn = document.getElementById('mobileRollBtn');
    const continuousRollBtn = document.getElementById('continuousRollBtn');
    const mobileAutoRollBtn = document.getElementById('mobileAutoRollBtn');
    const rollsCounter = document.getElementById('rollsCounter');
    const rerollBtn = document.getElementById('rerollBtn');
    const mobileRerollBtn = document.getElementById('mobileRerollBtn');
    const claimBtn = document.getElementById('claimBtn');
    const mobileClaimBtn = document.getElementById('mobileClaimBtn');
    const startGameBtn = document.getElementById('startGameBtn');
    const autoRollBtn = document.getElementById('autoRollBtn');
    const diceCountSelect = document.getElementById('diceCount');
    const difficultySelect = document.getElementById('difficulty');
    const betAmountInput = document.getElementById('betAmount');
    const decreaseBetBtn = document.getElementById('decreaseBet');
    const increaseBetBtn = document.getElementById('increaseBet');
    const winLossIndicator = document.getElementById('winLossIndicator');
    const progressFill = document.getElementById('progressFill');
    const progressPercent = document.getElementById('progressPercent');
    const rulesBtn = document.getElementById('rulesBtn');
    const rulesModal = document.getElementById('rulesModal');
    const closeModal = document.querySelector('.close-modal');
    const soundToggleBtn = document.getElementById('soundToggle');
    const standardViewBtn = document.getElementById('standardView');
    const proViewBtn = document.getElementById('proView');
    
    // Statistics and history elements
    const gamesPlayed = document.getElementById('gamesPlayed');
    const winRate = document.getElementById('winRate');
    const avgScore = document.getElementById('avgScore');
    const bestScore = document.getElementById('bestScore');
    const totalWinnings = document.getElementById('totalWinnings');
    const highestMultiplier = document.getElementById('highestMultiplier');
    const historyTableBody = document.getElementById('historyTableBody');
    
    // Toggle elements
    const targetToggle = document.getElementById('targetToggle');
    const statsToggle = document.getElementById('statsToggle');
    const historyToggle = document.getElementById('historyToggle');
    
    // Preset buttons
    const betPresetBtns = document.querySelectorAll('.preset-btn');
    
    // Sound effects
    const sounds = {
        roll: new Audio(),
        claim: new Audio(),
        win: new Audio(),
        lose: new Audio(),
        reroll: new Audio(),
        click: new Audio()
    };
    
    // Setup placeholder sounds
    function setupPlaceholderSounds() {
        Object.keys(sounds).forEach(key => {
            sounds[key] = {
                play: function() {
                    if (gameState.soundEnabled) {
                        console.log(`Playing sound: ${key}`);
                    }
                },
                load: function() {},
                volume: 0.5
            };
        });
    }
    
    // Game state
    let gameState = {
        status: 'READY', // READY, ROLL, TARGET_ANALYSIS, CLAIM, GAME_END
        score: 0,
        turn: 1,
        maxTurns: 10,
        rollsInTurn: 0,
        maxRollsPerTurn: 10,
        diceCount: 3,
        diceValues: [],
        multiplier: 1.0,
        rerolls: 0,
        activeTargets: [],
        selectedTarget: null,
        targetWinScore: 1500, // Default medium difficulty
        balance: 1000,
        currentBet: 10,
        autoRollActive: false,
        autoRollInterval: null,
        soundEnabled: true,
        viewMode: 'standard',
        
        // Statistics
        statistics: {
            gamesPlayed: 0,
            gamesWon: 0,
            totalScore: 0,
            bestScore: 0,
            totalWinnings: 0,
            highestMultiplier: 1.0
        },
        
        // Game history
        gameHistory: []
    };
    
    // Target definitions
    const targetDefinitions = [
        {
            id: 'pair',
            name: 'Pair',
            description: 'Two dice with the same value',
            baseValue: 50,
            check: (diceValues) => {
                const counts = getCounts(diceValues);
                return Object.values(counts).some(count => count >= 2);
            },
            getMatches: (diceValues) => {
                const counts = getCounts(diceValues);
                const pairs = [];
                
                for (const [value, count] of Object.entries(counts)) {
                    if (count >= 2) {
                        pairs.push({
                            type: 'pair',
                            value: parseInt(value),
                            baseValue: 50,
                            displayValue: `Pair of ${value}s`
                        });
                    }
                }
                
                return pairs;
            }
        },
        {
            id: 'three-of-a-kind',
            name: 'Three of a Kind',
            description: 'Three dice with the same value',
            baseValue: 100,
            check: (diceValues) => {
                const counts = getCounts(diceValues);
                return Object.values(counts).some(count => count >= 3);
            },
            getMatches: (diceValues) => {
                const counts = getCounts(diceValues);
                const triples = [];
                
                for (const [value, count] of Object.entries(counts)) {
                    if (count >= 3) {
                        triples.push({
                            type: 'three-of-a-kind',
                            value: parseInt(value),
                            baseValue: 100,
                            displayValue: `Three ${value}s`
                        });
                    }
                }
                
                return triples;
            }
        },
        {
            id: 'four-of-a-kind',
            name: 'Four of a Kind',
            description: 'Four dice with the same value',
            baseValue: 200,
            check: (diceValues) => {
                const counts = getCounts(diceValues);
                return Object.values(counts).some(count => count >= 4);
            },
            getMatches: (diceValues) => {
                const counts = getCounts(diceValues);
                const quads = [];
                
                for (const [value, count] of Object.entries(counts)) {
                    if (count >= 4) {
                        quads.push({
                            type: 'four-of-a-kind',
                            value: parseInt(value),
                            baseValue: 200,
                            displayValue: `Four ${value}s`
                        });
                    }
                }
                
                return quads;
            }
        },
        {
            id: 'straight',
            name: 'Straight',
            description: 'Consecutive numbers',
            baseValue: 150,
            check: (diceValues) => {
                const unique = [...new Set(diceValues)].sort((a, b) => a - b);
                if (unique.length < 3) return false;
                
                for (let i = 0; i <= unique.length - 3; i++) {
                    let consecutive = 1;
                    for (let j = i + 1; j < unique.length; j++) {
                        if (unique[j] === unique[j-1] + 1) {
                            consecutive++;
                        } else {
                            break;
                        }
                    }
                    if (consecutive >= 3) return true;
                }
                return false;
            },
            getMatches: (diceValues) => {
                const unique = [...new Set(diceValues)].sort((a, b) => a - b);
                const straights = [];
                
                for (let i = 0; i <= unique.length - 3; i++) {
                    let consecutive = [unique[i]];
                    for (let j = i + 1; j < unique.length; j++) {
                        if (unique[j] === unique[j-1] + 1) {
                            consecutive.push(unique[j]);
                        } else {
                            break;
                        }
                    }
                    if (consecutive.length >= 3) {
                        straights.push({
                            type: 'straight',
                            value: consecutive.length,
                            baseValue: 150 + (consecutive.length - 3) * 50,
                            displayValue: `Straight: ${consecutive.join('-')}`
                        });
                    }
                }
                
                return straights;
            }
        },
        {
            id: 'full-house',
            name: 'Full House',
            description: 'Three of a kind + Pair',
            baseValue: 250,
            check: (diceValues) => {
                const counts = getCounts(diceValues);
                const values = Object.values(counts);
                return values.includes(3) && values.includes(2);
            },
            getMatches: (diceValues) => {
                const counts = getCounts(diceValues);
                const values = Object.values(counts);
                
                if (values.includes(3) && values.includes(2)) {
                    return [{
                        type: 'full-house',
                        value: 0,
                        baseValue: 250,
                        displayValue: 'Full House'
                    }];
                }
                
                return [];
            }
        },
        {
            id: 'all-same',
            name: 'All Same',
            description: 'All dice show the same number',
            baseValue: 500,
            check: (diceValues) => {
                return new Set(diceValues).size === 1;
            },
            getMatches: (diceValues) => {
                if (new Set(diceValues).size === 1) {
                    return [{
                        type: 'all-same',
                        value: diceValues[0],
                        baseValue: 500,
                        displayValue: `All ${diceValues[0]}s!`
                    }];
                }
                return [];
            }
        },
        {
            id: 'high-sum',
            name: 'High Sum',
            description: 'Sum of dice ≥ 20',
            baseValue: 100,
            check: (diceValues) => {
                return diceValues.reduce((sum, val) => sum + val, 0) >= 20;
            },
            getMatches: (diceValues) => {
                const sum = diceValues.reduce((sum, val) => sum + val, 0);
                if (sum >= 20) {
                    return [{
                        type: 'high-sum',
                        value: sum,
                        baseValue: 100 + (sum - 20) * 10,
                        displayValue: `High Sum: ${sum}`
                    }];
                }
                return [];
            }
        },
        {
            id: 'all-even',
            name: 'All Even',
            description: 'All dice show even numbers',
            baseValue: 120,
            check: (diceValues) => {
                return diceValues.every(val => val % 2 === 0);
            },
            getMatches: (diceValues) => {
                if (diceValues.every(val => val % 2 === 0)) {
                    return [{
                        type: 'all-even',
                        value: 0,
                        baseValue: 120,
                        displayValue: 'All Even Numbers'
                    }];
                }
                return [];
            }
        },
        {
            id: 'all-odd',
            name: 'All Odd',
            description: 'All dice show odd numbers',
            baseValue: 120,
            check: (diceValues) => {
                return diceValues.every(val => val % 2 === 1);
            },
            getMatches: (diceValues) => {
                if (diceValues.every(val => val % 2 === 1)) {
                    return [{
                        type: 'all-odd',
                        value: 0,
                        baseValue: 120,
                        displayValue: 'All Odd Numbers'
                    }];
                }
                return [];
            }
        }
    ];
    
    // Helper function to count dice values
    function getCounts(diceValues) {
        const counts = {};
        diceValues.forEach(value => {
            counts[value] = (counts[value] || 0) + 1;
        });
        return counts;
    }
    
    // Initialize game
    function initGame() {
        // Reset game state
        gameState.status = 'READY';
        gameState.score = 0;
        gameState.turn = 1;
        gameState.rollsInTurn = 0;
        gameState.diceValues = [];
        gameState.multiplier = 1.0;
        gameState.rerolls = 0;
        gameState.activeTargets = [];
        gameState.selectedTarget = null;
        gameState.autoRollActive = false;
        
        // Get configuration
        gameState.diceCount = parseInt(diceCountSelect.value);
        const difficulty = difficultySelect.value;
        
        // Set target score based on difficulty
        switch(difficulty) {
            case 'easy': gameState.targetWinScore = 1000; break;
            case 'hard': gameState.targetWinScore = 2000; break;
            default: gameState.targetWinScore = 1500; // medium
        }
        
        // Update UI
        updateDisplay();
        generateDicePlaceholder();
        clearTargetCards();
        updateButtons();
        
        gameStatus.textContent = 'Place your bet and roll the dice to start!';
        gameStatus.className = 'game-status';
        
        // Enable controls
        betAmountInput.disabled = false;
        decreaseBetBtn.disabled = false;
        increaseBetBtn.disabled = false;
        diceCountSelect.disabled = false;
        difficultySelect.disabled = false;
        
        // Clear any auto roll interval
        if (gameState.autoRollInterval) {
            clearInterval(gameState.autoRollInterval);
            gameState.autoRollInterval = null;
        }
    }
    
    // Update display
    function updateDisplay() {
        balanceValue.textContent = `${gameState.balance} GA`;
        mobileBalanceValue.textContent = `${gameState.balance} GA`;
        scoreValue.textContent = gameState.score;
        mobileScoreValue.textContent = `Score: ${gameState.score}`;
        multiplierValue.textContent = `${gameState.multiplier.toFixed(1)}×`;
        turnsValue.textContent = `${gameState.turn}/${gameState.maxTurns}`;
        
        // Update progress bar
        const progress = Math.min((gameState.score / gameState.targetWinScore) * 100, 100);
        progressFill.style.width = `${progress}%`;
        progressPercent.textContent = `${Math.round(progress)}%`;
        
        // Update statistics
        updateStatistics();
    }
    
    // Update statistics display
    function updateStatistics() {
        gamesPlayed.textContent = gameState.statistics.gamesPlayed;
        winRate.textContent = gameState.statistics.gamesPlayed > 0 ? 
            `${Math.round((gameState.statistics.gamesWon / gameState.statistics.gamesPlayed) * 100)}%` : '0%';
        avgScore.textContent = gameState.statistics.gamesPlayed > 0 ? 
            Math.round(gameState.statistics.totalScore / gameState.statistics.gamesPlayed) : 0;
        bestScore.textContent = gameState.statistics.bestScore;
        totalWinnings.textContent = `${gameState.statistics.totalWinnings} GA`;
        highestMultiplier.textContent = `${gameState.statistics.highestMultiplier.toFixed(1)}×`;
    }
    
    // Generate dice placeholder
    function generateDicePlaceholder() {
        diceContainer.innerHTML = `
            <div class="dice-placeholder">
                <i class="fas fa-dice"></i>
                <span>Roll to start!</span>
            </div>
        `;
    }
    
    // Generate dice
    function generateDice() {
        diceContainer.innerHTML = '';
        
        for (let i = 0; i < gameState.diceCount; i++) {
            const dice = document.createElement('div');
            dice.className = 'dice';
            dice.dataset.index = i;
            
            // Create 6 faces
            for (let face = 1; face <= 6; face++) {
                const faceElement = document.createElement('div');
                faceElement.className = 'dice-face';
                faceElement.textContent = face;
                dice.appendChild(faceElement);
            }
            
            diceContainer.appendChild(dice);
        }
    }
    
    // Roll dice
    function rollDice() {
        if (gameState.status === 'READY') {
            startGame();
            return;
        }
        
        if (gameState.status !== 'ROLL' && gameState.status !== 'TARGET_ANALYSIS') return;
        
        // Check if max rolls reached
        if (gameState.rollsInTurn >= gameState.maxRollsPerTurn) {
            gameStatus.textContent = 'Maximum rolls per turn reached!';
            gameStatus.className = 'game-status warning';
            return;
        }
        
        // Generate new dice values
        gameState.diceValues = [];
        for (let i = 0; i < gameState.diceCount; i++) {
            gameState.diceValues.push(Math.floor(Math.random() * 6) + 1);
        }
        
        gameState.rollsInTurn++;
        rollsCounter.textContent = `Roll #${gameState.rollsInTurn}`;
        
        // Play roll sound
        sounds.roll.play();
        
        // Animate dice
        animateDice();
        
        // Update status
        gameState.status = 'TARGET_ANALYSIS';
        gameStatus.textContent = 'Analyzing targets...';
        gameStatus.className = 'game-status';
        
        // Analyze targets after animation
        setTimeout(() => {
            analyzeTargets();
        }, 1500);
    }
    
    // Start game
    function startGame() {
        // Check bet amount
        const betAmount = parseInt(betAmountInput.value);
        if (betAmount <= 0 || isNaN(betAmount)) {
            gameStatus.textContent = 'Please enter a valid bet amount!';
            gameStatus.className = 'game-status warning';
            return;
        }
        
        if (betAmount > gameState.balance) {
            gameStatus.textContent = 'Insufficient balance for this bet!';
            gameStatus.className = 'game-status warning';
            return;
        }
        
        // Deduct bet from balance
        gameState.balance -= betAmount;
        gameState.currentBet = betAmount;
        
        // Disable controls
        betAmountInput.disabled = true;
        decreaseBetBtn.disabled = true;
        increaseBetBtn.disabled = true;
        diceCountSelect.disabled = true;
        difficultySelect.disabled = true;
        
        // Start game
        gameState.status = 'ROLL';
        generateDice();
        rollDice();
        
        updateDisplay();
    }
    
    // Animate dice
    function animateDice() {
        const diceElements = document.querySelectorAll('.dice');
        
        diceElements.forEach((dice, index) => {
            // Add rolling animation
            dice.style.animation = 'rollDice 1.5s ease-out';
            
            // Set final rotation based on dice value
            setTimeout(() => {
                const value = gameState.diceValues[index];
                setDiceRotation(dice, value);
                dice.style.animation = '';
            }, 1500);
        });
    }
    
    // Set dice rotation to show specific value
    function setDiceRotation(dice, value) {
        const rotations = {
            1: 'rotateX(0deg) rotateY(0deg)',
            2: 'rotateX(0deg) rotateY(180deg)',
            3: 'rotateX(0deg) rotateY(90deg)',
            4: 'rotateX(0deg) rotateY(-90deg)',
            5: 'rotateX(90deg) rotateY(0deg)',
            6: 'rotateX(-90deg) rotateY(0deg)'
        };
        
        dice.style.transform = rotations[value] || rotations[1];
    }
    
    // Analyze targets
    function analyzeTargets() {
        gameState.activeTargets = [];
        
        // Check each target definition
        targetDefinitions.forEach(targetDef => {
            if (targetDef.check(gameState.diceValues)) {
                const matches = targetDef.getMatches(gameState.diceValues);
                gameState.activeTargets.push(...matches);
            }
        });
        
        // Update target cards
        updateTargetCards();
        
        // Update buttons
        updateButtons();
        
        // Update status
        if (gameState.activeTargets.length > 0) {
            gameStatus.textContent = `Found ${gameState.activeTargets.length} target(s)! Select one to claim or risk a reroll.`;
            gameStatus.className = 'game-status success';
        } else {
            gameStatus.textContent = 'No targets found. Try rerolling or end turn.';
            gameStatus.className = 'game-status warning';
        }
    }
    
    // Update target cards
    function updateTargetCards() {
        targetCards.innerHTML = '';
        
        if (gameState.activeTargets.length === 0) {
            targetCards.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 20px;">No targets available</p>';
            return;
        }
        
        gameState.activeTargets.forEach((target, index) => {
            const card = document.createElement('div');
            card.className = 'target-card';
            card.dataset.index = index;
            
            const scoreValue = Math.floor(target.baseValue * gameState.multiplier);
            
            card.innerHTML = `
                <div class="target-title">${target.displayValue}</div>
                <div class="target-desc">${targetDefinitions.find(def => def.id === target.type)?.description || ''}</div>
                <div class="target-value">${scoreValue} pts</div>
            `;
            
            card.addEventListener('click', () => selectTarget(index));
            targetCards.appendChild(card);
        });
    }
    
    // Select target
    function selectTarget(index) {
        // Clear previous selection
        document.querySelectorAll('.target-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Select new target
        gameState.selectedTarget = index;
        const selectedCard = document.querySelector(`.target-card[data-index="${index}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }
        
        // Update buttons
        updateButtons();
        
        // Play click sound
        sounds.click.play();
    }
    
    // Claim target
    function claimTarget() {
        if (gameState.selectedTarget === null) {
            gameStatus.textContent = 'Please select a target first!';
            gameStatus.className = 'game-status warning';
            return;
        }
        
        const target = gameState.activeTargets[gameState.selectedTarget];
        const scoreValue = Math.floor(target.baseValue * gameState.multiplier);
        
        // Add score
        gameState.score += scoreValue;
        
        // Play claim sound
        sounds.claim.play();
        
        // Update statistics
        if (gameState.multiplier > gameState.statistics.highestMultiplier) {
            gameState.statistics.highestMultiplier = gameState.multiplier;
        }
        
        // Check for win
        if (gameState.score >= gameState.targetWinScore) {
            gameWin();
            return;
        }
        
        // Next turn
        nextTurn();
    }
    
    // Risk reroll
    function riskReroll() {
        if (gameState.rollsInTurn >= gameState.maxRollsPerTurn) {
            gameStatus.textContent = 'Maximum rolls per turn reached!';
            gameStatus.className = 'game-status warning';
            return;
        }
        
        // Increase multiplier
        gameState.multiplier += 0.2;
        gameState.rerolls++;
        
        // Clear selected target
        gameState.selectedTarget = null;
        gameState.activeTargets = [];
        
        // Play reroll sound
        sounds.reroll.play();
        
        // Update display
        updateDisplay();
        clearTargetCards();
        updateButtons();
        
        // Animate multiplier
        const multiplierElement = document.querySelector('.multiplier');
        multiplierElement.classList.add('pulse');
        setTimeout(() => {
            multiplierElement.classList.remove('pulse');
        }, 500);
        
        gameStatus.textContent = `Multiplier increased to ${gameState.multiplier.toFixed(1)}×! Roll again for higher scores.`;
        gameStatus.className = 'game-status warning';
        
        // Set status back to roll
        gameState.status = 'ROLL';
    }
    
    // Next turn
    function nextTurn() {
        gameState.turn++;
        gameState.rollsInTurn = 0;
        gameState.multiplier = 1.0;
        gameState.rerolls = 0;
        gameState.selectedTarget = null;
        gameState.activeTargets = [];
        
        // Check if max turns reached
        if (gameState.turn > gameState.maxTurns) {
            gameOver();
            return;
        }
        
        // Update display
        updateDisplay();
        generateDicePlaceholder();
        clearTargetCards();
        updateButtons();
        
        gameStatus.textContent = `Turn ${gameState.turn}: Roll dice to continue!`;
        gameStatus.className = 'game-status';
        gameState.status = 'ROLL';
    }
    
    // Game win
    function gameWin() {
        gameState.status = 'GAME_END';
        
        // Calculate payout
        const difficulty = difficultySelect.value;
        let payoutMultiplier;
        switch(difficulty) {
            case 'easy': payoutMultiplier = 3; break;
            case 'hard': payoutMultiplier = 8; break;
            default: payoutMultiplier = 5; // medium
        }
        
        const winAmount = gameState.currentBet * payoutMultiplier;
        gameState.balance += winAmount;
        
        // Update statistics
        gameState.statistics.gamesPlayed++;
        gameState.statistics.gamesWon++;
        gameState.statistics.totalScore += gameState.score;
        gameState.statistics.totalWinnings += winAmount - gameState.currentBet;
        if (gameState.score > gameState.statistics.bestScore) {
            gameState.statistics.bestScore = gameState.score;
        }
        
        // Add to history
        addToGameHistory({
            id: gameState.gameHistory.length + 1,
            score: gameState.score,
            turns: gameState.turn,
            bet: gameState.currentBet,
            result: 'win',
            payout: winAmount - gameState.currentBet,
            time: new Date()
        });
        
        // Play win sound
        sounds.win.play();
        
        // Show win indicator
        showWinLossIndicator(true, winAmount - gameState.currentBet);
        
        gameStatus.textContent = `🎉 You won! Score: ${gameState.score} | Payout: +${winAmount - gameState.currentBet} GA`;
        gameStatus.className = 'game-status success';
        
        updateDisplay();
        updateButtons();
    }
    
    // Game over
    function gameOver() {
        gameState.status = 'GAME_END';
        
        // Update statistics
        gameState.statistics.gamesPlayed++;
        gameState.statistics.totalScore += gameState.score;
        if (gameState.score > gameState.statistics.bestScore) {
            gameState.statistics.bestScore = gameState.score;
        }
        
        // Add to history
        addToGameHistory({
            id: gameState.gameHistory.length + 1,
            score: gameState.score,
            turns: gameState.turn - 1,
            bet: gameState.currentBet,
            result: 'loss',
            payout: -gameState.currentBet,
            time: new Date()
        });
        
        // Play lose sound
        sounds.lose.play();
        
        // Show loss indicator
        showWinLossIndicator(false, gameState.currentBet);
        
        gameStatus.textContent = `Game Over! Final Score: ${gameState.score} | Lost: ${gameState.currentBet} GA`;
        gameStatus.className = 'game-status danger';
        
        updateDisplay();
        updateButtons();
    }
    
    // Clear target cards
    function clearTargetCards() {
        targetCards.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 20px;">Roll dice to find targets</p>';
    }
    
    // Update buttons
    function updateButtons() {
        const isReady = gameState.status === 'READY';
        const canRoll = gameState.status === 'ROLL' || gameState.status === 'TARGET_ANALYSIS';
        const hasTargets = gameState.activeTargets.length > 0;
        const hasSelection = gameState.selectedTarget !== null;
        const canReroll = gameState.rollsInTurn < gameState.maxRollsPerTurn && hasTargets;
        
        // Roll buttons
        rollBtn.disabled = !canRoll;
        mobileRollBtn.disabled = !canRoll;
        
        // Claim buttons
        claimBtn.disabled = !hasSelection;
        mobileClaimBtn.disabled = !hasSelection;
        
        // Reroll buttons
        rerollBtn.disabled = !canReroll;
        mobileRerollBtn.disabled = !canReroll;
        
        // Auto roll buttons
        autoRollBtn.disabled = !canRoll;
        mobileAutoRollBtn.disabled = !canRoll;
        
        // Update start game button
        if (isReady) {
            startGameBtn.innerHTML = '<i class="fas fa-play"></i> Start Game';
        } else if (gameState.status === 'GAME_END') {
            startGameBtn.innerHTML = '<i class="fas fa-play"></i> New Game';
        } else {
            startGameBtn.innerHTML = '<i class="fas fa-sync-alt"></i> New Game';
        }
    }
    
    // Show win/loss indicator
    function showWinLossIndicator(isWin, amount) {
        const indicator = document.createElement('div');
        indicator.className = isWin ? 'win-indicator' : 'loss-indicator';
        
        const icon = isWin ? 'fas fa-trophy' : 'fas fa-times-circle';
        const text = isWin ? `+${amount} GA` : `-${amount} GA`;
        
        indicator.innerHTML = `
            <i class="${icon} indicator-icon"></i>
            <span>${text}</span>
        `;
        
        winLossIndicator.innerHTML = '';
        winLossIndicator.appendChild(indicator);
        
        // Remove after animation
        setTimeout(() => {
            indicator.remove();
        }, 3000);
    }
    
    // Add to game history
    function addToGameHistory(game) {
        gameState.gameHistory.unshift(game);
        if (gameState.gameHistory.length > 20) {
            gameState.gameHistory.pop();
        }
        
        updateHistoryTable();
    }
    
    // Update history table
    function updateHistoryTable() {
        historyTableBody.innerHTML = '';
        
        gameState.gameHistory.forEach(game => {
            const row = document.createElement('tr');
            
            const timeString = formatTime(game.time);
            const resultClass = game.result === 'win' ? 'win' : 'loss';
            const resultText = game.result === 'win' ? 'WIN' : 'LOSS';
            
            row.innerHTML = `
                <td>#${game.id}</td>
                <td>${game.score}</td>
                <td>${game.turns}</td>
                <td>${game.bet} GA</td>
                <td class="${resultClass}">${resultText}</td>
                <td>${timeString}</td>
            `;
            
            historyTableBody.appendChild(row);
        });
    }
    
    // Format time
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    // Toggle auto roll
    function toggleAutoRoll() {
        if (gameState.autoRollActive) {
            // Stop auto roll
            gameState.autoRollActive = false;
            if (gameState.autoRollInterval) {
                clearInterval(gameState.autoRollInterval);
                gameState.autoRollInterval = null;
            }
            
            autoRollBtn.innerHTML = '<i class="fas fa-magic"></i> Auto Roll';
            mobileAutoRollBtn.innerHTML = '<i class="fas fa-magic"></i> Auto Roll';
            continuousRollBtn.innerHTML = '<i class="fas fa-dice-d20"></i> Auto Roll';
        } else {
            // Start auto roll
            gameState.autoRollActive = true;
            
            autoRollBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Auto';
            mobileAutoRollBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Auto';
            continuousRollBtn.innerHTML = '<i class="fas fa-stop"></i> Stop Auto';
            
            gameState.autoRollInterval = setInterval(() => {
                if (gameState.status === 'ROLL' || gameState.status === 'TARGET_ANALYSIS') {
                    rollDice();
                } else if (gameState.activeTargets.length > 0) {
                    // Auto-select best target and claim
                    const bestTarget = gameState.activeTargets.reduce((best, target, index) => {
                        const currentValue = Math.floor(target.baseValue * gameState.multiplier);
                        const bestValue = Math.floor(best.target.baseValue * gameState.multiplier);
                        return currentValue > bestValue ? {target, index} : best;
                    }, {target: gameState.activeTargets[0], index: 0});
                    
                    selectTarget(bestTarget.index);
                    setTimeout(() => claimTarget(), 500);
                } else {
                    // No targets, end turn or continue if possible
                    if (gameState.rollsInTurn < gameState.maxRollsPerTurn) {
                        rollDice();
                    } else {
                        nextTurn();
                    }
                }
            }, 2000);
        }
        
        sounds.click.play();
    }
    
    // Toggle view mode
    function toggleViewMode(mode) {
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
            standardViewBtn.classList.add('active');
            proViewBtn.classList.remove('active');
            gameState.viewMode = 'standard';
        } else {
            document.body.classList.add('pro-view-active');
            proViewBtn.classList.add('active');
            standardViewBtn.classList.remove('active');
            gameState.viewMode = 'pro';
        }
        
        sounds.click.play();
    }
    
    // Toggle sound
    function toggleSound() {
        gameState.soundEnabled = !gameState.soundEnabled;
        
        if (gameState.soundEnabled) {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        } else {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        }
        
        sounds.click.play();
    }
    
    // EVENT LISTENERS
    
    // Roll dice buttons
    rollBtn.addEventListener('click', rollDice);
    mobileRollBtn.addEventListener('click', rollDice);
    
    // Claim buttons
    claimBtn.addEventListener('click', claimTarget);
    mobileClaimBtn.addEventListener('click', claimTarget);
    
    // Reroll buttons
    rerollBtn.addEventListener('click', riskReroll);
    mobileRerollBtn.addEventListener('click', riskReroll);
    
    // Auto roll buttons
    autoRollBtn.addEventListener('click', toggleAutoRoll);
    mobileAutoRollBtn.addEventListener('click', toggleAutoRoll);
    continuousRollBtn.addEventListener('click', toggleAutoRoll);
    
    // Start game button
    startGameBtn.addEventListener('click', () => {
        if (gameState.status === 'GAME_END' || gameState.status === 'READY') {
            initGame();
        } else {
            initGame();
        }
    });
    
    // Bet adjustment buttons
    decreaseBetBtn.addEventListener('click', () => {
        const currentBet = parseInt(betAmountInput.value);
        if (currentBet >= 20) {
            betAmountInput.value = currentBet - 10;
            sounds.click.play();
        }
    });
    
    increaseBetBtn.addEventListener('click', () => {
        const currentBet = parseInt(betAmountInput.value);
        if (currentBet + 10 <= gameState.balance) {
            betAmountInput.value = currentBet + 10;
            sounds.click.play();
        } else {
            gameStatus.textContent = 'Insufficient balance for higher bet!';
            gameStatus.className = 'game-status warning';
            setTimeout(() => {
                if (gameState.status === 'READY') {
                    gameStatus.textContent = 'Place your bet and roll the dice to start!';
                    gameStatus.className = 'game-status';
                }
            }, 1500);
        }
    });
    
    // Bet input validation
    betAmountInput.addEventListener('change', () => {
        let value = parseInt(betAmountInput.value);
        
        if (value < 10 || isNaN(value)) {
            value = 10;
        }
        
        if (value > gameState.balance) {
            value = gameState.balance;
        }
        
        value = Math.floor(value / 10) * 10;
        betAmountInput.value = value;
    });
    
    // Bet preset buttons
    betPresetBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            if (gameState.status !== 'READY') return;
            
            const amount = btn.dataset.amount;
            
            if (amount === 'max') {
                betAmountInput.value = gameState.balance;
            } else {
                const presetAmount = parseInt(amount);
                
                if (presetAmount <= gameState.balance) {
                    betAmountInput.value = presetAmount;
                } else {
                    gameStatus.textContent = 'Insufficient balance for this bet!';
                    gameStatus.className = 'game-status warning';
                    setTimeout(() => {
                        if (gameState.status === 'READY') {
                            gameStatus.textContent = 'Place your bet and roll the dice to start!';
                            gameStatus.className = 'game-status';
                        }
                    }, 1500);
                    return;
                }
            }
            
            sounds.click.play();
        });
    });
    
    // View mode toggle
    standardViewBtn.addEventListener('click', () => toggleViewMode('standard'));
    proViewBtn.addEventListener('click', () => toggleViewMode('pro'));
    
    // Sound toggle
    soundToggleBtn.addEventListener('click', toggleSound);
    
    // Rules modal
    rulesBtn.addEventListener('click', () => {
        rulesModal.style.display = 'block';
        sounds.click.play();
    });
    
    closeModal.addEventListener('click', () => {
        rulesModal.style.display = 'none';
    });
    
    window.addEventListener('click', (e) => {
        if (e.target === rulesModal) {
            rulesModal.style.display = 'none';
        }
    });
    
    // Toggle sections
    targetToggle.addEventListener('click', () => {
        const content = document.querySelector('.target-content');
        content.classList.toggle('expanded');
        targetToggle.innerHTML = content.classList.contains('expanded') ? 
            '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
        sounds.click.play();
    });
    
    statsToggle.addEventListener('click', () => {
        const content = document.querySelector('.stats-content');
        content.classList.toggle('expanded');
        statsToggle.innerHTML = content.classList.contains('expanded') ? 
            '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
        sounds.click.play();
    });
    
    historyToggle.addEventListener('click', () => {
        const content = document.querySelector('.history-content');
        content.classList.toggle('expanded');
        historyToggle.innerHTML = content.classList.contains('expanded') ? 
            '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
        sounds.click.play();
    });
    
    // Handle sidebar toggle for proper layout
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    menuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('open');
        mainContent.classList.toggle('sidebar-open');
    });
    
    // Screen orientation change handling
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            updateDisplay();
        }, 300);
    });
    
    // Setup sounds
    setupPlaceholderSounds();
    
    // Initialize game on load
    initGame();
});