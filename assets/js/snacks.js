/**
 * SNACKS OF DOOM Game
 * A strategic match-3 style game with a dark/horror theme and gambling mechanics
 * Enhanced with mobile responsiveness and Pro View features
 */

// Snack types configuration
const SNACK_TYPES = {
    // Penalty snacks (60%)
    POISON: { symbol: '☠️', value: -50, type: 'penalty', weight: 20 },
    ROTTEN: { symbol: '🤢', value: -30, type: 'penalty', weight: 15 },
    MOLDY: { symbol: '🦠', value: -25, type: 'penalty', weight: 15 },
    TOXIC: { symbol: '☣️', value: -40, type: 'penalty', weight: 10 },
    
    // Low value (30%)
    CRACKER: { symbol: '🍪', value: 10, type: 'low', weight: 20 },
    CHIP: { symbol: '🥨', value: 20, type: 'low', weight: 10 },
    
    // High value (10%)
    GOLDEN: { symbol: '🏆', value: 50, type: 'high', weight: 7 },
    DIAMOND: { symbol: '💎', value: 100, type: 'high', weight: 3 },
    
    // Special penalty items
    POISON_APPLE: { symbol: '🍎', value: -100, type: 'special', weight: 0 },
    ROTTEN_EGG: { symbol: '🥚', value: -25, type: 'special', weight: 0 },
    TAX_COLLECTOR: { symbol: '🏛️', value: 0, type: 'special', weight: 0 }
};

// Game constants
const GRID_SIZE = 5;
const DEFAULT_MOVE_TIME = 5.0;
const DEFAULT_BALANCE = 1000;

/**
 * SnacksGame Class - Main game controller
 */
class SnacksGame {
    constructor() {
        // Initialize game state
        this.initializeState();
        
        // Set up DOM elements
        this.setupDOMElements();
        
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize the game
        this.initializeGame();
    }
    
    /**
     * Initialize game state variables
     */
    initializeState() {
        this.grid = [];
        
        // Load saved data or use defaults
        const savedData = this.loadGameData();
        
        // Game state
        this.state = {
            // Player stats
            balance: savedData.balance || DEFAULT_BALANCE,
            multiplier: savedData.multiplier || 1.0,
            debt: savedData.debt || 0,
            losses: savedData.losses || 0,
            
            // Game mechanics
            selectedCell: null,
            gameState: 'playing',
            moveTimeLeft: DEFAULT_MOVE_TIME,
            pendingGamble: null,
            isAnimating: false,
            
            // Analytics
            totalWagered: savedData.totalWagered || 0,
            totalWon: savedData.totalWon || 0,
            highestDebt: savedData.highestDebt || 0,
            matchStats: savedData.matchStats || {
                good: 0,
                bad: 0,
                special: 0
            },
            snackMatches: savedData.snackMatches || {},
            
            // Game history
            history: savedData.history || [],
            achievements: savedData.achievements || [],
            
            // UI state
            viewMode: savedData.viewMode || 'standard',
            isMobileStatsOpen: false,
            soundEnabled: true,
            
            // Gamble stats
            gambleAttempts: savedData.gambleAttempts || 0,
            gambleSuccesses: savedData.gambleSuccesses || 0
        };
        
        // Timer reference
        this.moveTimer = null;
        
        // Initialize snack match statistics for all types
        Object.keys(SNACK_TYPES).forEach(key => {
            if (!this.state.snackMatches[key]) {
                this.state.snackMatches[key] = {
                    matches: 0,
                    impact: 0
                };
            }
        });
    }
    
    /**
     * Set up DOM element references
     */
    setupDOMElements() {
        // Main game elements
        this.gameBoard = document.getElementById('gameBoard');
        this.moveTimerEl = document.getElementById('moveTimer');
        this.mobileTimerEl = document.getElementById('mobileTimer');
        this.debtIndicator = document.getElementById('debtIndicator');
        
        // Stats elements
        this.pointsEl = document.getElementById('points');
        this.multiplierEl = document.getElementById('multiplier');
        this.debtEl = document.getElementById('debt');
        this.lossesEl = document.getElementById('losses');
        
        // Mobile stats
        this.mobileBalanceEl = document.getElementById('mobileBalance');
        this.mobilePointsEl = document.getElementById('mobilePoints');
        this.mobileMultiplierEl = document.getElementById('mobileMultiplier');
        this.mobileDebtEl = document.getElementById('mobileDebt');
        this.mobileLossesEl = document.getElementById('mobileLosses');
        
        // Buttons
        this.newGameBtn = document.getElementById('newGameBtn');
        this.mobileNewGameBtn = document.getElementById('mobileNewGameBtn');
        this.gambleBtn = document.getElementById('gambleBtn');
        this.mobileGambleBtn = document.getElementById('mobileGambleBtn');
        this.acceptGambleBtn = document.getElementById('acceptGamble');
        
        // View toggle buttons
        this.standardViewBtn = document.getElementById('standardView');
        this.proViewBtn = document.getElementById('proView');
        
        // Overlay elements
        this.gambleOverlay = document.getElementById('gambleOverlay');
        this.messageOverlay = document.getElementById('messageOverlay');
        this.achievementEl = document.getElementById('achievement');
        this.countdownEl = document.getElementById('countdown');
        
        // Pro View elements
        this.totalWageredEl = document.getElementById('totalWagered');
        this.totalWonEl = document.getElementById('totalWon');
        this.netProfitEl = document.getElementById('netProfit');
        this.highestDebtEl = document.getElementById('highestDebt');
        this.winRateEl = document.getElementById('winRate');
        this.gambleSuccessEl = document.getElementById('gambleSuccess');
        this.matchStatsBodyEl = document.getElementById('matchStatsBody');
        this.historyTableBodyEl = document.getElementById('historyTableBody');
        this.historyPlaceholderEl = document.getElementById('historyPlaceholder');
        
        // Mobile panels
        this.mobileStatsPanel = document.getElementById('mobileStatsPanel');
        this.mobileHistoryList = document.getElementById('mobileHistoryList');
        this.mobileAchievementsList = document.getElementById('mobileAchievementsList');
        
        // Toggle buttons
        this.rulesToggle = document.getElementById('rulesToggle');
        this.valuesToggle = document.getElementById('valuesToggle');
        this.analyticsToggle = document.getElementById('analyticsToggle');
        this.historyToggle = document.getElementById('historyToggle');
        
        // Content sections
        this.rulesContent = document.getElementById('rulesContent');
        this.snackValuesContent = document.getElementById('snackValuesContent');
        this.analyticsContent = document.getElementById('analyticsContent');
        this.historyContent = document.getElementById('historyContent');
        
        // Header win/loss indicator
        this.headerWinLossIndicator = document.getElementById('headerWinLossIndicator');
    }
    
    /**
     * Set up all event listeners
     */
    setupEventListeners() {
        // Game board click for cell selection
        this.gameBoard.addEventListener('click', (e) => {
            const cell = e.target.closest('.grid-cell');
            if (cell && this.state.gameState === 'playing') {
                this.handleCellClick(cell);
            }
        });
        
        // Game control buttons
        this.newGameBtn.addEventListener('click', () => this.resetGame());
        this.mobileNewGameBtn.addEventListener('click', () => this.resetGame());
        this.gambleBtn.addEventListener('click', () => this.showGambleModal());
        this.mobileGambleBtn.addEventListener('click', () => this.showGambleModal());
        this.acceptGambleBtn.addEventListener('click', () => this.processGamble());
        
        // View toggle
        this.standardViewBtn.addEventListener('click', () => this.setViewMode('standard'));
        this.proViewBtn.addEventListener('click', () => this.setViewMode('pro'));
        
        // Mobile stats panel
        document.getElementById('mobileStatsToggle').addEventListener('click', () => this.toggleMobileStatsPanel());
        document.getElementById('closeStatsPanel').addEventListener('click', () => this.closeMobileStatsPanel());
        
        // Section toggles
        this.rulesToggle.addEventListener('click', () => this.toggleSection(this.rulesContent));
        this.valuesToggle.addEventListener('click', () => this.toggleSection(this.snackValuesContent));
        this.analyticsToggle.addEventListener('click', () => this.toggleSection(this.analyticsContent));
        this.historyToggle.addEventListener('click', () => this.toggleSection(this.historyContent));
        
        // Window resize event for responsive adjustments
        window.addEventListener('resize', () => this.handleResize());
        
        // Touch events for mobile swipe
        this.setupTouchEvents();
    }
    
    /**
     * Set up touch events for mobile swipe gestures
     */
    setupTouchEvents() {
        let touchStartX = 0;
        let touchStartY = 0;
        
        this.gameBoard.addEventListener('touchstart', (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        }, false);
        
        this.gameBoard.addEventListener('touchmove', (e) => {
            if (!this.state.selectedCell || this.state.isAnimating) return;
            
            const touchEndX = e.touches[0].clientX;
            const touchEndY = e.touches[0].clientY;
            const diffX = touchEndX - touchStartX;
            const diffY = touchEndY - touchStartY;
            
            // Minimum distance for a swipe
            const minSwipeDistance = 30;
            
            // Determine swipe direction if distance is enough
            if (Math.abs(diffX) > minSwipeDistance || Math.abs(diffY) > minSwipeDistance) {
                e.preventDefault(); // Prevent scrolling
                
                const row = this.state.selectedCell.row;
                const col = this.state.selectedCell.col;
                
                // Determine direction (horizontal or vertical)
                if (Math.abs(diffX) > Math.abs(diffY)) {
                    // Horizontal swipe
                    if (diffX > 0 && col < GRID_SIZE - 1) {
                        // Right swipe
                        this.swapCells(row, col, row, col + 1);
                    } else if (diffX < 0 && col > 0) {
                        // Left swipe
                        this.swapCells(row, col, row, col - 1);
                    }
                } else {
                    // Vertical swipe
                    if (diffY > 0 && row < GRID_SIZE - 1) {
                        // Down swipe
                        this.swapCells(row, col, row + 1, col);
                    } else if (diffY < 0 && row > 0) {
                        // Up swipe
                        this.swapCells(row, col, row - 1, col);
                    }
                }
                
                // Clear selection after swap
                this.clearSelection();
                
                // Reset touch start position
                touchStartX = touchEndX;
                touchStartY = touchEndY;
            }
        }, false);
    }
    
    /**
     * Handle window resize events
     */
    handleResize() {
        // Adjust game board cells for better mobile experience
        const isMobile = window.innerWidth <= 768;
        const cells = document.querySelectorAll('.grid-cell');
        
        if (isMobile) {
            // Smaller font size for mobile
            cells.forEach(cell => {
                const snack = cell.querySelector('.snack');
                if (snack) {
                    snack.style.fontSize = window.innerWidth <= 480 ? '1.2rem' : '1.5rem';
                }
            });
        } else {
            // Restore desktop font size
            cells.forEach(cell => {
                const snack = cell.querySelector('.snack');
                if (snack) {
                    snack.style.fontSize = '1.8rem';
                }
            });
        }
    }
    
    /**
     * Initialize the game
     */
    initializeGame() {
        // Apply saved view mode
        this.setViewMode(this.state.viewMode, false);
        
        // Initialize the grid
        this.initializeGrid();
        
        // Start the move timer
        this.startMoveTimer();
        
        // Update all displays
        this.updateAllDisplays();
        
        // Check for achievements
        this.checkAchievements();
        
        // Set up initial section states
        this.toggleSection(this.rulesContent, true);
    }
    
    /**
     * Initialize the grid with snacks
     */
    initializeGrid() {
        // Clear the game board
        this.gameBoard.innerHTML = '';
        
        // Reset grid array
        this.grid = [];
        
        // Create grid cells
        for (let row = 0; row < GRID_SIZE; row++) {
            this.grid[row] = [];
            for (let col = 0; col < GRID_SIZE; col++) {
                // Create grid cell
                const cell = document.createElement('div');
                cell.className = 'grid-cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                // Generate a random snack
                const snack = this.generateSnack();
                this.grid[row][col] = snack;
                
                // Add snack to cell
                cell.innerHTML = `<div class="snack ${snack.type}">${snack.symbol}</div>`;
                
                // Add cell to game board
                this.gameBoard.appendChild(cell);
            }
        }
        
        // Add special penalty items
        this.addSpecialItems();
        
        // Initial check for any matches
        this.checkForInitialMatches();
    }
    
    /**
     * Check for any matches on initial board setup
     * to avoid starting with matches already present
     */
    checkForInitialMatches() {
        const matches = this.findMatches();
        if (matches.length > 0) {
            // Re-initialize if matches found
            this.initializeGrid();
        }
    }
    
    /**
     * Generate a random snack
     */
    generateSnack() {
        const roll = Math.random() * 100;
        const snackKeys = Object.keys(SNACK_TYPES);
        
        // Weighted selection favoring penalties
        if (roll < 60) {
            // 60% penalty snacks
            const penaltySnacks = snackKeys.filter(key => SNACK_TYPES[key].type === 'penalty');
            return { ...SNACK_TYPES[penaltySnacks[Math.floor(Math.random() * penaltySnacks.length)]] };
        } else if (roll < 90) {
            // 30% low value
            const lowSnacks = snackKeys.filter(key => SNACK_TYPES[key].type === 'low');
            return { ...SNACK_TYPES[lowSnacks[Math.floor(Math.random() * lowSnacks.length)]] };
        } else {
            // 10% high value
            const highSnacks = snackKeys.filter(key => SNACK_TYPES[key].type === 'high');
            return { ...SNACK_TYPES[highSnacks[Math.floor(Math.random() * highSnacks.length)]] };
        }
    }
    
    /**
     * Add special penalty items to the grid
     */
    addSpecialItems() {
        // Add poison apples (3-4 per grid)
        for (let i = 0; i < 3 + Math.floor(Math.random() * 2); i++) {
            const row = Math.floor(Math.random() * GRID_SIZE);
            const col = Math.floor(Math.random() * GRID_SIZE);
            this.grid[row][col] = { ...SNACK_TYPES.POISON_APPLE };
            this.updateCellDisplay(row, col);
        }
        
        // Add rotten eggs (2-3 per grid)
        for (let i = 0; i < 2 + Math.floor(Math.random() * 2); i++) {
            const row = Math.floor(Math.random() * GRID_SIZE);
            const col = Math.floor(Math.random() * GRID_SIZE);
            this.grid[row][col] = { ...SNACK_TYPES.ROTTEN_EGG };
            this.updateCellDisplay(row, col);
        }
        
        // Add tax collector (1 per grid)
        const row = Math.floor(Math.random() * GRID_SIZE);
        const col = Math.floor(Math.random() * GRID_SIZE);
        this.grid[row][col] = { ...SNACK_TYPES.TAX_COLLECTOR };
        this.updateCellDisplay(row, col);
    }
    
    /**
     * Update the visual display of a cell
     */
    updateCellDisplay(row, col) {
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        const snack = this.grid[row][col];
        cell.innerHTML = `<div class="snack ${snack.type}">${snack.symbol}</div>`;
    }
    
    /**
     * Handle click on a grid cell
     */
    handleCellClick(cell) {
        // Don't process clicks if another animation is in progress
        if (this.state.isAnimating) return;
        
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        
        // Provide immediate visual feedback
        const snackEl = cell.querySelector('.snack');
        if (snackEl) {
            snackEl.style.transform = 'scale(1.15)';
            setTimeout(() => {
                snackEl.style.transform = '';
            }, 150);
        }
        
        if (this.state.selectedCell) {
            if (this.state.selectedCell.row === row && this.state.selectedCell.col === col) {
                // Deselect if clicking the same cell
                this.clearSelection();
                return;
            }
            
            // Try to swap if cells are adjacent
            if (this.isAdjacent(this.state.selectedCell.row, this.state.selectedCell.col, row, col)) {
                this.state.isAnimating = true; // Prevent further clicks during animation
                
                this.swapCells(this.state.selectedCell.row, this.state.selectedCell.col, row, col);
                this.clearSelection();
                
                // Log the move
                this.addToHistory({
                    action: 'Swap',
                    details: `Swapped (${this.state.selectedCell.row},${this.state.selectedCell.col}) with (${row},${col})`,
                    result: 'Pending',
                    balance: this.state.balance,
                    time: new Date()
                });
                
                // Wait for swap animation to complete
                setTimeout(() => {
                    this.checkMatches();
                    this.state.isAnimating = false;
                }, 400);
            } else {
                // If not adjacent, select the new cell instead
                this.clearSelection();
                this.selectCell(row, col);
            }
        } else {
            // First selection
            this.selectCell(row, col);
        }
    }
    
    /**
     * Select a cell
     */
    selectCell(row, col) {
        this.state.selectedCell = { row, col };
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        cell.classList.add('selected');
        
        // Add pulsing animation
        const snackEl = cell.querySelector('.snack');
        if (snackEl) {
            snackEl.style.animation = 'pulse 1s infinite';
        }
    }
    
    /**
     * Clear cell selection
     */
    clearSelection() {
        if (this.state.selectedCell) {
            const cell = document.querySelector(
                `[data-row="${this.state.selectedCell.row}"][data-col="${this.state.selectedCell.col}"]`
            );
            cell.classList.remove('selected');
            
            // Remove animation
            const snackEl = cell.querySelector('.snack');
            if (snackEl) {
                snackEl.style.animation = '';
            }
            
            this.state.selectedCell = null;
        }
    }
    
    /**
     * Check if two cells are adjacent
     */
    isAdjacent(row1, col1, row2, col2) {
        return Math.abs(row1 - row2) + Math.abs(col1 - col2) === 1;
    }
    
    /**
     * Swap two cells with animation
     */
    swapCells(row1, col1, row2, col2) {
        // Get cell elements
        const cell1 = document.querySelector(`[data-row="${row1}"][data-col="${col1}"]`);
        const cell2 = document.querySelector(`[data-row="${row2}"][data-col="${col2}"]`);
        
        // Save current positions for animation
        const rect1 = cell1.getBoundingClientRect();
        const rect2 = cell2.getBoundingClientRect();
        
        // Calculate translation needed
        const deltaX = rect2.left - rect1.left;
        const deltaY = rect2.top - rect1.top;
        
        // Create clones for smooth animation
        const clone1 = cell1.cloneNode(true);
        const clone2 = cell2.cloneNode(true);
        
        // Position clones absolutely
        clone1.style.position = 'absolute';
        clone2.style.position = 'absolute';
        clone1.style.left = rect1.left + 'px';
        clone1.style.top = rect1.top + 'px';
        clone2.style.left = rect2.left + 'px';
        clone2.style.top = rect2.top + 'px';
        clone1.style.zIndex = '100';
        clone2.style.zIndex = '100';
        clone1.style.transition = 'all 0.3s ease';
        clone2.style.transition = 'all 0.3s ease';
        clone1.style.width = rect1.width + 'px';
        clone1.style.height = rect1.height + 'px';
        clone2.style.width = rect2.width + 'px';
        clone2.style.height = rect2.height + 'px';
        
        // Add clones to document
        document.body.appendChild(clone1);
        document.body.appendChild(clone2);
        
        // Hide original cells during animation
        cell1.style.opacity = '0';
        cell2.style.opacity = '0';
        
        // Animate the clones
        setTimeout(() => {
            clone1.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
            clone2.style.transform = `translate(${-deltaX}px, ${-deltaY}px)`;
        }, 50);
        
        // After animation completes, update grid and clean up
        setTimeout(() => {
            // Update the grid data
            const temp = this.grid[row1][col1];
            this.grid[row1][col1] = this.grid[row2][col2];
            this.grid[row2][col2] = temp;
            
            // Update cell displays
            this.updateCellDisplay(row1, col1);
            this.updateCellDisplay(row2, col2);
            
            // Show original cells again
            cell1.style.opacity = '1';
            cell2.style.opacity = '1';
            
            // Remove the animation clones
            document.body.removeChild(clone1);
            document.body.removeChild(clone2);
        }, 350);
    }
    
    /**
     * Check for matches after a swap
     */
    checkMatches() {
        const matches = this.findMatches();
        if (matches.length > 0) {
            this.processMatches(matches);
        } else {
            // No matches found - apply time penalty
            this.applyTimePenalty();
        }
        
        // Reset the move timer
        this.resetMoveTimer();
    }
    
    /**
     * Find all matches in the grid
     */
    findMatches() {
        const matches = [];
        
        // Check horizontal matches
        for (let row = 0; row < GRID_SIZE; row++) {
            let count = 1;
            let currentSymbol = this.grid[row][0].symbol;
            
            for (let col = 1; col < GRID_SIZE; col++) {
                if (this.grid[row][col].symbol === currentSymbol) {
                    count++;
                } else {
                    // Check if we found a match before changing symbol
                    if (count >= 3) {
                        matches.push({
                            cells: Array.from({length: count}, (_, i) => ({row, col: col-count+i})),
                            type: this.grid[row][col-1].type,
                            symbol: currentSymbol,
                            snackKey: this.getSnackKeyBySymbol(currentSymbol)
                        });
                    }
                    count = 1;
                    currentSymbol = this.grid[row][col].symbol;
                }
            }
            
            // Check for match at the end of row
            if (count >= 3) {
                matches.push({
                    cells: Array.from({length: count}, (_, i) => ({row, col: GRID_SIZE-count+i})),
                    type: this.grid[row][GRID_SIZE-1].type,
                    symbol: currentSymbol,
                    snackKey: this.getSnackKeyBySymbol(currentSymbol)
                });
            }
        }
        
        // Check vertical matches
        for (let col = 0; col < GRID_SIZE; col++) {
            let count = 1;
            let currentSymbol = this.grid[0][col].symbol;
            
            for (let row = 1; row < GRID_SIZE; row++) {
                if (this.grid[row][col].symbol === currentSymbol) {
                    count++;
                } else {
                    // Check if we found a match before changing symbol
                    if (count >= 3) {
                        matches.push({
                            cells: Array.from({length: count}, (_, i) => ({row: row-count+i, col})),
                            type: this.grid[row-1][col].type,
                            symbol: currentSymbol,
                            snackKey: this.getSnackKeyBySymbol(currentSymbol)
                        });
                    }
                    count = 1;
                    currentSymbol = this.grid[row][col].symbol;
                }
            }
            
            // Check for match at the end of column
            if (count >= 3) {
                matches.push({
                    cells: Array.from({length: count}, (_, i) => ({row: GRID_SIZE-count+i, col})),
                    type: this.grid[GRID_SIZE-1][col].type,
                    symbol: currentSymbol,
                    snackKey: this.getSnackKeyBySymbol(currentSymbol)
                });
            }
        }
        
        return matches;
    }
    
    /**
     * Get snack key by symbol for statistics tracking
     */
    getSnackKeyBySymbol(symbol) {
        for (const key in SNACK_TYPES) {
            if (SNACK_TYPES[key].symbol === symbol) {
                return key;
            }
        }
        return null;
    }
    
    /**
     * Process matches found in the grid
     */
    processMatches(matches) {
        let totalGA = 0;
        let hasGoodMatch = false;
        let matchDescription = '';
        
        // First highlight the matches
        matches.forEach(match => {
            match.cells.forEach(cell => {
                const cellEl = document.querySelector(`[data-row="${cell.row}"][data-col="${cell.col}"]`);
                const snackEl = cellEl.querySelector('.snack');
                
                // Add highlight effect based on match type
                snackEl.style.boxShadow = match.type === 'penalty' || match.type === 'special' ? 
                    '0 0 15px 5px rgba(255, 0, 0, 0.7)' : 
                    '0 0 15px 5px rgba(255, 255, 0, 0.7)';
                snackEl.style.transform = 'scale(1.2)';
            });
        });
        
        // Wait a moment to show the highlight, then process
        setTimeout(() => {
            matches.forEach(match => {
                // Update match statistics
                this.updateMatchStatistics(match);
                
                // Process based on match type
                if (match.type === 'penalty' || match.type === 'special') {
                    // Penalty matches only need 3
                    if (match.cells.length >= 3) {
                        const penalty = -50 * match.cells.length;
                        totalGA += penalty;
                        this.state.multiplier -= 0.5; // Harsh multiplier penalty
                        this.triggerCascadingPenalty(match.cells);
                        
                        // Update match description
                        matchDescription += `${match.cells.length}× ${match.symbol} (${penalty} GA), `;
                    }
                } else {
                    // Good matches need 4+ items
                    if (match.cells.length >= 4) {
                        const snackValue = this.grid[match.cells[0].row][match.cells[0].col].value;
                        const points = snackValue * match.cells.length;
                        totalGA += points;
                        this.state.multiplier += 0.1; // Small multiplier bonus
                        hasGoodMatch = true;
                        
                        // Update match description
                        matchDescription += `${match.cells.length}× ${match.symbol} (+${points} GA), `;
                    }
                }
                
                // Remove matched cells
                match.cells.forEach(cell => {
                    this.grid[cell.row][cell.col] = null;
                });
            });
            
            // Clean up match description
            if (matchDescription.length > 0) {
                matchDescription = matchDescription.slice(0, -2); // Remove trailing comma and space
            }
            
            // Apply multiplier
            const rawTotal = totalGA;
            totalGA = Math.floor(totalGA * this.state.multiplier);
            
            // Log the match in history
            this.addToHistory({
                action: 'Match',
                details: matchDescription,
                result: totalGA >= 0 ? `+${totalGA} GA` : `${totalGA} GA`,
                balance: this.state.balance + totalGA,
                time: new Date()
            });
            
            // Update balance
            this.state.balance += totalGA;
            
            // Track analytics
            if (totalGA > 0) {
                this.state.totalWon += totalGA;
            } else {
                this.state.totalWagered -= totalGA; // Track losses as "wagered"
            }
            
            // Apply debt interest
            if (this.state.debt > 0) {
                const interest = Math.floor(this.state.balance * (this.state.debt / 100));
                this.state.balance -= interest;
                this.showMessage(`Debt Interest: -${interest} GA!`, 'penalty');
                
                // Log debt interest in history
                this.addToHistory({
                    action: 'Debt Interest',
                    details: `${this.state.debt}% applied`,
                    result: `-${interest} GA`,
                    balance: this.state.balance,
                    time: new Date()
                });
            }
            
            // Ensure multiplier doesn't go below 0
            this.state.multiplier = Math.max(0, this.state.multiplier);
            
            // Clear matched cells and refill grid
            this.refillGrid();
            
            // Force gamble after any good match
            if (hasGoodMatch && totalGA > 0) {
                this.state.pendingGamble = totalGA;
                this.gambleBtn.disabled = false;
                this.mobileGambleBtn.disabled = false;
                this.showMessage('MANDATORY GAMBLE INCOMING!', 'warning');
            }
            
            // Check for game over
            this.checkGameOver();
            
            // Update displays
            this.updateAllDisplays();
            
            // Check for achievements
            this.checkAchievements();
        }, 300);
    }
    
    /**
     * Update match statistics for analytics
     */
    updateMatchStatistics(match) {
        // Count by type
        if (match.type === 'penalty' || match.type === 'special') {
            this.state.matchStats.bad++;
        } else if (match.cells.length >= 4) {
            this.state.matchStats.good++;
        }
        
        // Count by specific snack type
        if (match.snackKey && this.state.snackMatches[match.snackKey]) {
            this.state.snackMatches[match.snackKey].matches++;
            
            // Calculate impact (value * cells * multiplier)
            const snackValue = this.grid[match.cells[0].row][match.cells[0].col].value;
            const impact = snackValue * match.cells.length * this.state.multiplier;
            this.state.snackMatches[match.snackKey].impact += impact;
        }
    }
    
    /**
     * Trigger cascading penalty effect to adjacent cells
     */
    triggerCascadingPenalty(matchCells) {
        // Spread penalty to adjacent cells
        matchCells.forEach(cell => {
            for (let dr = -1; dr <= 1; dr++) {
                for (let dc = -1; dc <= 1; dc++) {
                    const newRow = cell.row + dr;
                    const newCol = cell.col + dc;
                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        if (this.grid[newRow][newCol] && this.grid[newRow][newCol].type !== 'penalty') {
                            this.state.balance -= 10; // Adjacent penalty of 10 GA
                            
                            // Visual feedback for adjacent penalty
                            const adjCell = document.querySelector(`[data-row="${newRow}"][data-col="${newCol}"]`);
                            const adjSnack = adjCell.querySelector('.snack');
                            adjSnack.style.boxShadow = '0 0 8px 2px rgba(255, 0, 0, 0.5)';
                            setTimeout(() => {
                                adjSnack.style.boxShadow = '';
                            }, 500);
                        }
                    }
                }
            }
        });
    }
    
    /**
     * Refill the grid after matches are removed
     */
    refillGrid() {
        // Create a copy of current grid for animation
        const oldGrid = JSON.parse(JSON.stringify(this.grid));
        
        // Drop existing snacks down
        for (let col = 0; col < GRID_SIZE; col++) {
            let writeIndex = GRID_SIZE - 1;
            for (let row = GRID_SIZE - 1; row >= 0; row--) {
                if (this.grid[row][col] !== null) {
                    this.grid[writeIndex][col] = this.grid[row][col];
                    if (writeIndex !== row) {
                        this.grid[row][col] = null;
                    }
                    writeIndex--;
                }
            }
            
            // Fill empty spaces with new snacks
            for (let row = writeIndex; row >= 0; row--) {
                this.grid[row][col] = this.generateSnack();
            }
        }
        
        // Animate the falling snacks
        this.animateRefill(oldGrid);
    }
    
    /**
     * Animate the refilling of the grid
     */
    animateRefill(oldGrid) {
        // First, update all cells with their final state but with special styling for new ones
        for (let row = 0; row < GRID_SIZE; row++) {
            for (let col = 0; col < GRID_SIZE; col++) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                const snack = this.grid[row][col];
                
                if (!oldGrid[row][col]) {
                    // This is a new snack, prepare for animation
                    cell.innerHTML = `<div class="snack ${snack.type}" 
                        style="opacity: 0; transform: translateY(-50px);">${snack.symbol}</div>`;
                } else {
                    // This is an existing snack
                    cell.innerHTML = `<div class="snack ${snack.type}">${snack.symbol}</div>`;
                }
            }
        }
        
        // Now animate them in with a staggered delay
        setTimeout(() => {
            for (let row = 0; row < GRID_SIZE; row++) {
                for (let col = 0; col < GRID_SIZE; col++) {
                    const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                    const snackEl = cell.querySelector('.snack');
                    
                    if (snackEl && snackEl.style.opacity === '0') {
                        // Enhanced falling animation
                        snackEl.style.transition = 'opacity 0.4s ease, transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                        
                        // Stagger by both row and column for more natural cascade
                        const delay = (row * 30) + (col * 50);
                        
                        setTimeout(() => {
                            snackEl.style.opacity = '1';
                            snackEl.style.transform = 'translateY(0)';
                        }, delay);
                    }
                }
            }
            
            // Check for any new matches after refilling
            setTimeout(() => {
                const newMatches = this.findMatches();
                if (newMatches.length > 0) {
                    this.processMatches(newMatches);
                }
            }, 600); // Wait for animations to complete
        }, 150);
    }
    
    /**
     * Apply time penalty when no match is found
     */
    applyTimePenalty() {
        const penalty = Math.floor(Math.random() * 30) + 10;
        this.state.balance -= penalty;
        this.showMessage(`Time penalty: -${penalty} GA!`, 'penalty');
        
        // Log the penalty
        this.addToHistory({
            action: 'Time Penalty',
            details: 'No match found',
            result: `-${penalty} GA`,
            balance: this.state.balance,
            time: new Date()
        });
        
        // Check for game over
        if (this.state.balance <= 0) {
            this.state.balance = 0;
            this.showMessage('💀 BANKRUPT! 💀', 'bankrupt');
            this.state.gameState = 'bankrupt';
        }
        
        this.updateAllDisplays();
    }
    
    /**
     * Show gamble modal
     */
    showGambleModal() {
        this.gambleOverlay.style.display = 'flex';
        let countdown = 3;
        this.countdownEl.textContent = countdown;
        
        const timer = setInterval(() => {
            countdown--;
            this.countdownEl.textContent = countdown;
            if (countdown <= 0) {
                clearInterval(timer);
                this.processGamble();
            }
        }, 1000);
    }
    
    /**
     * Process gamble outcome
     */
    processGamble() {
        this.gambleOverlay.style.display = 'none';
        
        if (this.state.pendingGamble) {
            this.state.gambleAttempts++;
            const success = Math.random() < 0.15; // 15% success rate
            
            if (success) {
                const winnings = this.state.pendingGamble * 3;
                this.state.balance += winnings;
                this.state.totalWon += winnings;
                this.state.gambleSuccesses++;
                
                this.showMessage(`MIRACLE! +${winnings} GA!`, 'win');
                
                // Show win indicator
                this.showWinLossIndicator(true, winnings);
                
                // Log the successful gamble
                this.addToHistory({
                    action: 'Gamble Success',
                    details: `${this.state.pendingGamble} GA risked`,
                    result: `+${winnings} GA`,
                    balance: this.state.balance,
                    time: new Date()
                });
            } else {
                this.state.balance -= this.state.pendingGamble;
                this.state.losses++;
                this.state.debt += 10; // Add debt interest
                this.state.totalWagered += this.state.pendingGamble; // Track as wagered
                
                // Update highest debt tracking
                if (this.state.debt > this.state.highestDebt) {
                    this.state.highestDebt = this.state.debt;
                }
                
                this.showMessage(`GAMBLE FAILED! -${this.state.pendingGamble} GA!`, 'penalty');
                this.showMessage('DEBT SPIRAL ACTIVATED!', 'penalty');
                
                // Show loss indicator
                this.showWinLossIndicator(false, this.state.pendingGamble);
                
                // Log the failed gamble
                this.addToHistory({
                    action: 'Gamble Failed',
                    details: `${this.state.pendingGamble} GA lost`,
                    result: `Debt +10%`,
                    balance: this.state.balance,
                    time: new Date()
                });
                
                // Show bankrupt message if points <= 0
                if (this.state.balance <= 0) {
                    this.state.balance = 0;
                    this.showMessage('💀 BANKRUPT! 💀', 'bankrupt');
                    this.state.gameState = 'bankrupt';
                }
            }
            
            this.state.pendingGamble = null;
            this.gambleBtn.disabled = true;
            this.mobileGambleBtn.disabled = true;
        }
        
        this.updateAllDisplays();
        this.checkAchievements();
    }
    
    /**
     * Show win/loss indicator in header
     */
    showWinLossIndicator(isWin, amount) {
        // Create indicator element
        const indicator = document.createElement('div');
        indicator.className = isWin ? 'win-indicator' : 'loss-indicator';
        indicator.innerHTML = `
            <i class="${isWin ? 'fas fa-trophy' : 'fas fa-skull'} indicator-icon"></i>
            <span>${isWin ? '+' : '-'}${Math.abs(amount)} GA</span>
        `;
        
        // Add to header
        this.headerWinLossIndicator.innerHTML = '';
        this.headerWinLossIndicator.appendChild(indicator);
        
        // Remove after animation finishes
        setTimeout(() => {
            if (this.headerWinLossIndicator.contains(indicator)) {
                this.headerWinLossIndicator.removeChild(indicator);
            }
        }, 3000);
    }
    
    /**
     * Start the move timer
     */
    startMoveTimer() {
        clearInterval(this.moveTimer);
        this.moveTimer = setInterval(() => {
            this.state.moveTimeLeft -= 0.1;
            this.moveTimerEl.textContent = `Time: ${this.state.moveTimeLeft.toFixed(1)}s`;
            this.mobileTimerEl.textContent = `${this.state.moveTimeLeft.toFixed(1)}s`;
            
            // Visual feedback as time runs low
            if (this.state.moveTimeLeft <= 1.5) {
                this.moveTimerEl.style.color = '#ff4444';
                this.mobileTimerEl.style.color = '#ff4444';
            } else {
                this.moveTimerEl.style.color = '';
                this.mobileTimerEl.style.color = '';
            }
            
            if (this.state.moveTimeLeft <= 0) {
                this.applyTimePenalty();
                this.resetMoveTimer();
            }
        }, 100);
    }
    
    /**
     * Reset the move timer
     */
    resetMoveTimer() {
        this.state.moveTimeLeft = DEFAULT_MOVE_TIME;
        this.moveTimerEl.textContent = `Time: ${this.state.moveTimeLeft.toFixed(1)}s`;
        this.mobileTimerEl.textContent = `${this.state.moveTimeLeft.toFixed(1)}s`;
        this.moveTimerEl.style.color = '';
        this.mobileTimerEl.style.color = '';
    }
    
    /**
     * Show message overlay
     */
    showMessage(text, type) {
        this.messageOverlay.textContent = text;
        this.messageOverlay.className = `message-overlay ${type}`;
        this.messageOverlay.style.display = 'block';
        
        setTimeout(() => {
            this.messageOverlay.style.display = 'none';
        }, 2000);
    }
    
    /**
     * Check for game over
     */
    checkGameOver() {
        if (this.state.balance <= 0) {
            this.state.balance = 0;
            this.state.gameState = 'bankrupt';
            this.showMessage('💀 TOTAL BANKRUPTCY! 💀', 'bankrupt');
            
            // Stop the timer
            clearInterval(this.moveTimer);
        }
    }
    
    /**
     * Check for achievements
     */
    checkAchievements() {
        // Check for various achievements
        const achievements = [];
        
        // Loss-related achievements
        if (this.state.losses >= 10 && !this.hasAchievement('10_losses')) {
            achievements.push({
                id: '10_losses',
                title: '💀 Glutton for Punishment',
                description: '10 Failed Gambles'
            });
        }
        
        if (this.state.losses >= 25 && !this.hasAchievement('25_losses')) {
            achievements.push({
                id: '25_losses',
                title: '⚰️ Hardcore Masochist',
                description: '25 Failed Gambles'
            });
        }
        
        // Debt-related achievements
        if (this.state.debt >= 50 && !this.hasAchievement('50_debt')) {
            achievements.push({
                id: '50_debt',
                title: '🏛️ Debt Slave',
                description: '50% Interest Rate'
            });
        }
        
        if (this.state.debt >= 100 && !this.hasAchievement('100_debt')) {
            achievements.push({
                id: '100_debt',
                title: '📉 Financial Ruin',
                description: '100% Interest Rate'
            });
        }
        
        // Balance achievements
        if (this.state.balance >= 2000 && !this.hasAchievement('2000_balance')) {
            achievements.push({
                id: '2000_balance',
                title: '💰 Temporary Prosperity',
                description: '2000+ GA Balance'
            });
        }
        
        // Match achievements
        if (this.state.matchStats.good >= 10 && !this.hasAchievement('10_good_matches')) {
            achievements.push({
                id: '10_good_matches',
                title: '🍪 Snack Collector',
                description: '10 Good Matches'
            });
        }
        
        if (this.state.matchStats.bad >= 20 && !this.hasAchievement('20_bad_matches')) {
            achievements.push({
                id: '20_bad_matches',
                title: '☠️ Poison Connoisseur',
                description: '20 Bad Matches'
            });
        }
        
        // Unlock achievements
        achievements.forEach(achievement => {
            this.unlockAchievement(achievement);
        });
    }
    
    /**
     * Check if player already has an achievement
     */
    hasAchievement(id) {
        return this.state.achievements.some(a => a.id === id);
    }
    
    /**
     * Unlock a new achievement
     */
    unlockAchievement(achievement) {
        // Add to achievements list
        this.state.achievements.push({
            ...achievement,
            unlocked: new Date()
        });
        
        // Show achievement notification
        this.achievementEl.textContent = `${achievement.title}: ${achievement.description}`;
        this.achievementEl.style.display = 'block';
        
        // Log the achievement
        this.addToHistory({
            action: 'Achievement',
            details: achievement.title,
            result: achievement.description,
            balance: this.state.balance,
            time: new Date()
        });
        
        // Hide after a few seconds
        setTimeout(() => {
            this.achievementEl.style.display = 'none';
        }, 3000);
        
        // Update mobile achievements list
        this.updateMobileAchievements();
        
        // Save state
        this.saveGameData();
    }
    
    /**
     * Add entry to game history
     */
    addToHistory(entry) {
        // Add to history array
        this.state.history.unshift(entry);
        
        // Limit history size
        if (this.state.history.length > 50) {
            this.state.history.pop();
        }
        
        // Update history displays
        this.updateHistoryDisplay();
        this.updateMobileHistory();
        
        // Save state
        this.saveGameData();
    }
    
    /**
     * Update all game displays
     */
    updateAllDisplays() {
        // Update basic stats
        this.pointsEl.textContent = this.state.balance;
        this.multiplierEl.textContent = `${this.state.multiplier.toFixed(1)}×`;
        this.debtEl.textContent = `${this.state.debt}%`;
        this.lossesEl.textContent = this.state.losses;
        
        // Update mobile stats
        this.mobileBalanceEl.textContent = `${this.state.balance} GA`;
        this.mobilePointsEl.textContent = `${this.state.balance} GA`;
        this.mobileMultiplierEl.textContent = `${this.state.multiplier.toFixed(1)}×`;
        this.mobileDebtEl.textContent = `${this.state.debt}%`;
        this.mobileLossesEl.textContent = this.state.losses;
        
        // Show/hide debt indicator
        this.debtIndicator.style.display = this.state.debt > 0 ? 'block' : 'none';
        
        // Update Pro View analytics
        this.updateProViewAnalytics();
        
        // Update history displays
        this.updateHistoryDisplay();
        this.updateMobileHistory();
        
        // Update mobile achievements
        this.updateMobileAchievements();
    }
    
    /**
     * Update Pro View analytics display
     */
    updateProViewAnalytics() {
        // Calculate derived metrics
        const netProfit = this.state.totalWon - this.state.totalWagered;
        const winRate = this.state.matchStats.good + this.state.matchStats.bad > 0 ?
            Math.round((this.state.matchStats.good / (this.state.matchStats.good + this.state.matchStats.bad)) * 100) : 0;
        const gambleSuccessRate = this.state.gambleAttempts > 0 ?
            Math.round((this.state.gambleSuccesses / this.state.gambleAttempts) * 100) : 0;
        
        // Update displays
        this.totalWageredEl.textContent = `${this.state.totalWagered} GA`;
        this.totalWonEl.textContent = `${this.state.totalWon} GA`;
        this.netProfitEl.textContent = `${netProfit >= 0 ? '+' : ''}${netProfit} GA`;
        this.highestDebtEl.textContent = `${this.state.highestDebt}%`;
        this.winRateEl.textContent = `${winRate}%`;
        this.gambleSuccessEl.textContent = `${gambleSuccessRate}%`;
        
        // Add appropriate classes based on profit/loss
        if (netProfit > 0) {
            this.netProfitEl.className = 'analytics-value positive';
        } else if (netProfit < 0) {
            this.netProfitEl.className = 'analytics-value negative';
        } else {
            this.netProfitEl.className = 'analytics-value';
        }
        
        // Update match statistics table
        this.updateMatchStatsTable();
    }
    
    /**
     * Update match statistics table
     */
    updateMatchStatsTable() {
        this.matchStatsBodyEl.innerHTML = '';
        
        // Get top 5 most common matches
        const snackEntries = Object.entries(this.state.snackMatches)
            .filter(([key, stats]) => stats.matches > 0)
            .sort((a, b) => b[1].matches - a[1].matches)
            .slice(0, 5);
        
        if (snackEntries.length === 0) {
            const emptyRow = document.createElement('div');
            emptyRow.className = 'match-stat-row';
            emptyRow.innerHTML = `
                <div class="match-stat-cell" colspan="3">No match data yet</div>
            `;
            this.matchStatsBodyEl.appendChild(emptyRow);
            return;
        }
        
        snackEntries.forEach(([key, stats]) => {
            const snack = SNACK_TYPES[key];
            const row = document.createElement('div');
            row.className = 'match-stat-row';
            
            row.innerHTML = `
                <div class="match-stat-cell">${snack.symbol}</div>
                <div class="match-stat-cell">${stats.matches}</div>
                <div class="match-stat-cell ${stats.impact >= 0 ? 'positive' : 'negative'}">
                    ${stats.impact >= 0 ? '+' : ''}${stats.impact} GA
                </div>
            `;
            
            this.matchStatsBodyEl.appendChild(row);
        });
    }
    
    /**
     * Update history display
     */
    updateHistoryDisplay() {
        this.historyTableBodyEl.innerHTML = '';
        
        if (this.state.history.length === 0) {
            this.historyPlaceholderEl.style.display = 'block';
            return;
        }
        
        this.historyPlaceholderEl.style.display = 'none';
        
        this.state.history.forEach((entry, index) => {
            const row = document.createElement('tr');
            
            // Format time
            const time = new Date(entry.time);
            const timeString = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;
            
            // Format result with color class
            const resultClass = entry.result.includes('+') ? 'positive' : 
                                entry.result.includes('-') ? 'negative' : '';
            
            row.innerHTML = `
                <td>#${this.state.history.length - index}</td>
                <td>${entry.action}</td>
                <td class="${resultClass}">${entry.result}</td>
                <td>${entry.balance} GA</td>
                <td>${timeString}</td>
            `;
            
            this.historyTableBodyEl.appendChild(row);
        });
    }
    
    /**
     * Update mobile history list
     */
    updateMobileHistory() {
        this.mobileHistoryList.innerHTML = '';
        
        if (this.state.history.length === 0) {
            this.mobileHistoryList.innerHTML = '<div class="mobile-history-item">No history yet</div>';
            return;
        }
        
        // Show only the 5 most recent entries
        this.state.history.slice(0, 5).forEach(entry => {
            const item = document.createElement('div');
            item.className = 'mobile-history-item';
            
            // Format time
            const time = new Date(entry.time);
            const timeString = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;
            
            // Format result with color
            const resultClass = entry.result.includes('+') ? 'positive' : 
                               entry.result.includes('-') ? 'negative' : '';
            
            item.innerHTML = `
                <strong>${entry.action}:</strong> 
                <span class="${resultClass}">${entry.result}</span> 
                <small>(${timeString})</small>
            `;
            
            this.mobileHistoryList.appendChild(item);
        });
    }
    
    /**
     * Update mobile achievements list
     */
    updateMobileAchievements() {
        this.mobileAchievementsList.innerHTML = '';
        
        if (this.state.achievements.length === 0) {
            this.mobileAchievementsList.innerHTML = '<div class="mobile-achievement-item">No achievements yet</div>';
            return;
        }
        
        this.state.achievements.forEach(achievement => {
            const item = document.createElement('div');
            item.className = 'mobile-achievement-item';
            
            item.innerHTML = `
                <i class="fas fa-trophy"></i>
                <span>${achievement.title}: ${achievement.description}</span>
            `;
            
            this.mobileAchievementsList.appendChild(item);
        });
    }
    
    /**
     * Reset the game
     */
    resetGame() {
        // Confirm if player has balance
        if (this.state.balance > 0 && this.state.gameState !== 'bankrupt') {
            if (!confirm('Reset game? You will lose your current balance!')) {
                return;
            }
        }
        
        // Stop timer
        clearInterval(this.moveTimer);
        
        // Reset game state
        this.state.balance = DEFAULT_BALANCE;
        this.state.multiplier = 1.0;
        this.state.debt = 0;
        this.state.selectedCell = null;
        this.state.gameState = 'playing';
        this.state.pendingGamble = null;
        
        // Keep other stats for continuity
        
        // Log the reset
        this.addToHistory({
            action: 'Game Reset',
            details: 'New game started',
            result: `Balance set to ${DEFAULT_BALANCE} GA`,
            balance: DEFAULT_BALANCE,
            time: new Date()
        });
        
        // Initialize grid
        this.initializeGrid();
        
        // Start timer
        this.startMoveTimer();
        
        // Update displays
        this.updateAllDisplays();
        
        // Disable gamble button
        this.gambleBtn.disabled = true;
        this.mobileGambleBtn.disabled = true;
    }
    
    /**
     * Toggle a section's visibility
     */
    toggleSection(section, forceShow = false) {
        if (forceShow) {
            section.classList.add('active');
            return;
        }
        
        section.classList.toggle('active');
        
        // Update toggle button icon
        const button = section.previousElementSibling.querySelector('.toggle-btn');
        if (button) {
            button.innerHTML = section.classList.contains('active') ? 
                '<i class="fas fa-chevron-up"></i>' : 
                '<i class="fas fa-chevron-down"></i>';
        }
    }
    
    /**
     * Set the view mode (standard or pro)
     */
    setViewMode(mode, saveState = true) {
        this.state.viewMode = mode;
        
        // Update body class
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
            this.standardViewBtn.classList.add('active');
            this.proViewBtn.classList.remove('active');
        } else {
            document.body.classList.add('pro-view-active');
            this.proViewBtn.classList.add('active');
            this.standardViewBtn.classList.remove('active');
        }
        
        // Save to state if requested
        if (saveState) {
            this.saveGameData();
        }
    }
    
    /**
     * Toggle mobile stats panel
     */
    toggleMobileStatsPanel() {
        this.mobileStatsPanel.classList.toggle('active');
        this.state.isMobileStatsOpen = this.mobileStatsPanel.classList.contains('active');
    }
    
    /**
     * Close mobile stats panel
     */
    closeMobileStatsPanel() {
        this.mobileStatsPanel.classList.remove('active');
        this.state.isMobileStatsOpen = false;
    }
    
    /**
     * Save game data to localStorage
     */
    saveGameData() {
        const dataToSave = {
            balance: this.state.balance,
            multiplier: this.state.multiplier,
            debt: this.state.debt,
            losses: this.state.losses,
            totalWagered: this.state.totalWagered,
            totalWon: this.state.totalWon,
            highestDebt: this.state.highestDebt,
            matchStats: this.state.matchStats,
            snackMatches: this.state.snackMatches,
            history: this.state.history,
            achievements: this.state.achievements,
            viewMode: this.state.viewMode,
            gambleAttempts: this.state.gambleAttempts,
            gambleSuccesses: this.state.gambleSuccesses
        };
        
        localStorage.setItem('snacksGameData', JSON.stringify(dataToSave));
    }
    
    /**
     * Load game data from localStorage
     */
    loadGameData() {
        const savedData = localStorage.getItem('snacksGameData');
        return savedData ? JSON.parse(savedData) : {};
    }
}

// Start the game when page loads
document.addEventListener('DOMContentLoaded', () => {
    new SnacksGame();
});