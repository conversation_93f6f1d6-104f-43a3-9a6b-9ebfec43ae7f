// Mines Game JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Game elements
    const gameBoard = document.getElementById('gameBoard');
    const walletValue = document.getElementById('walletValue');
    const mobileWalletValue = document.getElementById('mobileWalletValue');
    const multiplierValue = document.getElementById('multiplierValue');
    const payoutValue = document.getElementById('payoutValue');
    const flagsValue = document.getElementById('flagsValue');
    const gameStatus = document.getElementById('gameStatus');
    const startGameBtn = document.getElementById('startGame');
    const mobileStartGameBtn = document.getElementById('mobileStartGame');
    const cashoutBtn = document.getElementById('cashoutBtn');
    const mobileCashoutBtn = document.getElementById('mobileCashoutBtn');
    const gridSizeSelect = document.getElementById('gridSize');
    const minesCountInput = document.getElementById('minesCount');
    const decreaseMinesBtn = document.getElementById('decreaseMines');
    const increaseMinesBtn = document.getElementById('increaseMines');
    const revealModeBtn = document.getElementById('revealMode');
    const flagModeBtn = document.getElementById('flagMode');
    const mobileRevealModeBtn = document.getElementById('mobileRevealMode');
    const mobileFlagModeBtn = document.getElementById('mobileFlagMode');
    const betAmountInput = document.getElementById('betAmount');
    const decreaseBetBtn = document.getElementById('decreaseBet');
    const increaseBetBtn = document.getElementById('increaseBet');
    const historyTableBody = document.getElementById('historyTableBody');
    const historyToggle = document.getElementById('historyToggle');
    const standardViewBtn = document.getElementById('standardView');
    const proViewBtn = document.getElementById('proView');
    const soundToggleBtn = document.getElementById('soundToggle');
    
    // Preset buttons
    const betPresetBtns = document.querySelectorAll('.preset-btn');
    const minesPresetBtns = document.querySelectorAll('.mines-preset-btn');
    
    // Sound effects
    const sounds = {
        click: new Audio(),
        reveal: new Audio(),
        flag: new Audio(),
        mine: new Audio(),
        win: new Audio(),
        cashout: new Audio(),
        multiplier: new Audio()
    };
    
    // Load sound effects
    function loadSounds() {
        sounds.click.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/click.mp3';
        sounds.reveal.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/reveal.mp3';
        sounds.flag.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/flag.mp3';
        sounds.mine.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/mine.mp3';
        sounds.win.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/win.mp3';
        sounds.cashout.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/cashout.mp3';
        sounds.multiplier.src = 'https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/multiplier.mp3';
        
        // Preload sounds
        Object.values(sounds).forEach(sound => {
            sound.load();
            sound.volume = 0.5;
        });
    }
    
    // Use placeholder sounds until real ones are available
    function setupPlaceholderSounds() {
        Object.keys(sounds).forEach(key => {
            // Just create a minimal audio context for each sound
            sounds[key] = {
                play: function() {
                    // Only play sounds if enabled
                    if (gameState.soundEnabled) {
                        console.log(`Playing sound: ${key}`);
                        // In a real implementation, you'd play the actual sound here
                    }
                },
                load: function() {},
                volume: 0.5
            };
        });
    }
    
    // Game state
    let gameState = {
        status: 'READY', // READY, PLAYING, WIN, LOSS
        grid: [],
        gridSize: 8,
        totalMines: 12,
        minesPlaced: false,
        revealMode: true, // true = reveal, false = flag
        multiplier: 1.0,
        multiplierStep: 0.1,
        flagsUsed: 0,
        safeCellsRevealed: 0,
        totalSafeCells: 0,
        firstClick: true,
        soundEnabled: true,
        viewMode: 'standard', // standard or pro
        gameHistory: [],
        
        // Wallet and betting
        walletBalance: 1000, // Starting balance in GA
        betAmount: 10, // Default bet amount in GA
        potentialWin: 0, // Potential winnings based on current multiplier
        currentBet: 0, // Active bet amount for current game
    };
    
    // Initialize game
    function initGame() {
        // Reset game state
        gameState.status = 'READY';
        gameState.grid = [];
        gameState.multiplier = 1.0;
        gameState.flagsUsed = 0;
        gameState.safeCellsRevealed = 0;
        gameState.firstClick = true;
        gameState.minesPlaced = false;
        gameState.potentialWin = 0;
        
        // Get grid size from select
        gameState.gridSize = parseInt(gridSizeSelect.value);
        
        // Get mines count from input
        gameState.totalMines = parseInt(minesCountInput.value);
        
        // Validate mines count
        const maxMines = Math.floor(gameState.gridSize * gameState.gridSize * 0.4); // Max 40% of cells
        if (gameState.totalMines > maxMines) {
            gameState.totalMines = maxMines;
            minesCountInput.value = maxMines;
        }
        
        gameState.totalSafeCells = (gameState.gridSize * gameState.gridSize) - gameState.totalMines;
        
        // Update UI
        multiplierValue.textContent = '1.0×';
        flagsValue.textContent = `0/${gameState.totalMines}`;
        gameStatus.textContent = 'Place your bet and click any cell to start!';
        gameStatus.className = 'game-status';
        payoutValue.textContent = '0 GA';
        
        // Update cashout buttons
        cashoutBtn.disabled = true;
        mobileCashoutBtn.disabled = true;
        
        // Update the display with current wallet balance
        updateWalletDisplay();
        
        // Generate grid
        generateGrid();
        
        // Enable bet and grid controls
        betAmountInput.disabled = false;
        decreaseBetBtn.disabled = false;
        increaseBetBtn.disabled = false;
        gridSizeSelect.disabled = false;
        minesCountInput.disabled = false;
        decreaseMinesBtn.disabled = false;
        increaseMinesBtn.disabled = false;
        
        // Update start button text
        startGameBtn.textContent = 'Start Game';
        mobileStartGameBtn.textContent = 'Start Game';
        
        // Update start button
        startGameBtn.innerHTML = '<i class="fas fa-play"></i> Start Game';
        mobileStartGameBtn.innerHTML = '<i class="fas fa-play"></i> Start Game';
    }
    
    // Generate grid UI
    function generateGrid() {
        // Clear game board
        gameBoard.innerHTML = '';
        
        // Set grid columns
        gameBoard.style.gridTemplateColumns = `repeat(${gameState.gridSize}, 1fr)`;
        
        // Initialize grid data structure
        gameState.grid = [];
        for (let row = 0; row < gameState.gridSize; row++) {
            gameState.grid[row] = [];
            for (let col = 0; col < gameState.gridSize; col++) {
                gameState.grid[row][col] = {
                    row,
                    col,
                    isMine: false,
                    isRevealed: false,
                    isFlagged: false,
                    adjacentMines: 0
                };
                
                // Create cell element
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.row = row;
                cell.dataset.col = col;
                
                // Add event listeners
                cell.addEventListener('click', handleCellClick);
                cell.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                    handleCellRightClick(row, col);
                });
                
                // Add to game board
                gameBoard.appendChild(cell);
            }
        }
    }
    
    // Place mines randomly (ensuring first click is safe)
    function placeMines(firstRow, firstCol) {
        let minesPlaced = 0;
        
        // Calculate safe zone around first click (3x3 area)
        const safeZone = [];
        for (let r = Math.max(0, firstRow - 1); r <= Math.min(gameState.gridSize - 1, firstRow + 1); r++) {
            for (let c = Math.max(0, firstCol - 1); c <= Math.min(gameState.gridSize - 1, firstCol + 1); c++) {
                safeZone.push({row: r, col: c});
            }
        }
        
        // Place mines randomly until we reach the target
        while (minesPlaced < gameState.totalMines) {
            const row = Math.floor(Math.random() * gameState.gridSize);
            const col = Math.floor(Math.random() * gameState.gridSize);
            
            // Check if in safe zone
            const inSafeZone = safeZone.some(pos => pos.row === row && pos.col === col);
            
            // Check if already a mine
            if (!inSafeZone && !gameState.grid[row][col].isMine) {
                gameState.grid[row][col].isMine = true;
                minesPlaced++;
            }
        }
        
        // Calculate adjacent mines for each cell
        for (let row = 0; row < gameState.gridSize; row++) {
            for (let col = 0; col < gameState.gridSize; col++) {
                if (!gameState.grid[row][col].isMine) {
                    gameState.grid[row][col].adjacentMines = countAdjacentMines(row, col);
                }
            }
        }
        
        gameState.minesPlaced = true;
    }
    
    // Count adjacent mines
    function countAdjacentMines(row, col) {
        let count = 0;
        
        // Check all 8 surrounding cells
        for (let r = Math.max(0, row - 1); r <= Math.min(gameState.gridSize - 1, row + 1); r++) {
            for (let c = Math.max(0, col - 1); c <= Math.min(gameState.gridSize - 1, col + 1); c++) {
                // Skip the cell itself
                if (r === row && c === col) continue;
                
                // Increment count if mine
                if (gameState.grid[r][c].isMine) count++;
            }
        }
        
        return count;
    }
    
    // Handle cell click
    function handleCellClick(e) {
        if (gameState.status !== 'PLAYING' && gameState.status !== 'READY') return;
        
        const row = parseInt(this.dataset.row);
        const col = parseInt(this.dataset.col);
        
        // If in flag mode, toggle flag instead
        if (!gameState.revealMode) {
            handleCellRightClick(row, col);
            return;
        }
        
        // Can't reveal flagged cells
        if (gameState.grid[row][col].isFlagged) return;
        
        // Play click sound
        sounds.click.play();
        
        // If first click, start game and place mines
        if (gameState.firstClick) {
            startGame(row, col);
            return;
        }
        
        // Already revealed, do nothing
        if (gameState.grid[row][col].isRevealed) return;
        
        // Reveal the cell
        revealCell(row, col);
    }
    
    // Start game function
    function startGame(row, col) {
        // Check if bet amount is valid
        const betAmount = parseInt(betAmountInput.value);
        if (betAmount <= 0 || isNaN(betAmount)) {
            gameStatus.textContent = 'Please enter a valid bet amount!';
            return;
        }
        
        // Check if player has enough balance
        if (betAmount > gameState.walletBalance) {
            gameStatus.textContent = 'Insufficient balance for this bet!';
            return;
        }
        
        // Deduct bet amount from wallet
        gameState.walletBalance -= betAmount;
        gameState.currentBet = betAmount;
        updateWalletDisplay();
        
        // Disable bet controls during game
        betAmountInput.disabled = true;
        decreaseBetBtn.disabled = true;
        increaseBetBtn.disabled = true;
        gridSizeSelect.disabled = true;
        minesCountInput.disabled = true;
        decreaseMinesBtn.disabled = true;
        increaseMinesBtn.disabled = true;
        
        // Update start button
        startGameBtn.innerHTML = '<i class="fas fa-sync-alt"></i> New Game';
        mobileStartGameBtn.innerHTML = '<i class="fas fa-sync-alt"></i> New Game';
        
        // Enable cashout button
        cashoutBtn.disabled = false;
        mobileCashoutBtn.disabled = false;
        
        gameState.status = 'PLAYING';
        gameState.firstClick = false;
        placeMines(row, col);
        gameStatus.textContent = 'Game in progress... Click to reveal or flag to mark mines!';
        
        // Reveal the first clicked cell
        revealCell(row, col);
    }
    
    // Handle right click (flag toggling)
    function handleCellRightClick(row, col) {
        if (gameState.status !== 'PLAYING' && gameState.status !== 'READY') return;
        
        // Can't flag revealed cells
        if (gameState.grid[row][col].isRevealed) return;
        
        // If first action is a flag, start game with random mine placement
        if (gameState.firstClick) {
            startGame(row, col);
            return;
        }
        
        const cell = gameState.grid[row][col];
        
        // Toggle flag
        if (cell.isFlagged) {
            cell.isFlagged = false;
            gameState.flagsUsed--;
            sounds.flag.play();
        } else {
            // Only allow flagging if we haven't used all flags
            if (gameState.flagsUsed < gameState.totalMines) {
                cell.isFlagged = true;
                gameState.flagsUsed++;
                sounds.flag.play();
            } else {
                // Notify user they're out of flags
                gameStatus.textContent = 'No more flags available!';
                setTimeout(() => {
                    if (gameState.status === 'PLAYING') {
                        gameStatus.textContent = 'Game in progress...';
                    }
                }, 1500);
                return;
            }
        }
        
        // Update UI
        updateCell(row, col);
        flagsValue.textContent = `${gameState.flagsUsed}/${gameState.totalMines}`;
    }
    
    // Reveal a cell and update game state
    function revealCell(row, col) {
        const cell = gameState.grid[row][col];
        
        // Skip if already revealed or flagged
        if (cell.isRevealed || cell.isFlagged) return;
        
        // Mark as revealed
        cell.isRevealed = true;
        
        // Update UI
        updateCell(row, col);
        
        // Play reveal sound
        sounds.reveal.play();
        
        // If it's a mine, game over
        if (cell.isMine) {
            sounds.mine.play();
            gameOver(false);
            return;
        }
        
        // Increment safe cells revealed
        gameState.safeCellsRevealed++;
        
        // Increase multiplier
        gameState.multiplier += gameState.multiplierStep;
        gameState.multiplier = Math.round(gameState.multiplier * 10) / 10; // Round to 1 decimal
        
        // Calculate potential win amount
        gameState.potentialWin = Math.floor(gameState.currentBet * gameState.multiplier);
        
        // Update UI with new multiplier and potential win
        multiplierValue.textContent = gameState.multiplier.toFixed(1) + '×';
        payoutValue.textContent = `${gameState.potentialWin} GA`;
        
        // Play multiplier increase sound
        sounds.multiplier.play();
        
        // Add animation to multiplier
        const multiplierElement = document.querySelector('.multiplier');
        multiplierElement.classList.add('pulse');
        setTimeout(() => {
            multiplierElement.classList.remove('pulse');
        }, 500);
        
        // Add animation to payout
        const payoutElement = document.querySelector('.payout .stat-value');
        payoutElement.classList.add('highlight-payout');
        setTimeout(() => {
            payoutElement.classList.remove('highlight-payout');
        }, 800);
        
        // If no adjacent mines, reveal surrounding cells (flood fill)
        if (cell.adjacentMines === 0) {
            // Check all 8 surrounding cells
            for (let r = Math.max(0, row - 1); r <= Math.min(gameState.gridSize - 1, row + 1); r++) {
                for (let c = Math.max(0, col - 1); c <= Math.min(gameState.gridSize - 1, col + 1); c++) {
                    // Skip the cell itself
                    if (r === row && c === col) continue;
                    
                    // Recursively reveal
                    revealCell(r, c);
                }
            }
        }
        
        // Check for win
        if (gameState.safeCellsRevealed === gameState.totalSafeCells) {
            gameOver(true);
        }
    }
    
    // Update cell UI
    function updateCell(row, col) {
        const cell = gameState.grid[row][col];
        const cellElement = document.querySelector(`.cell[data-row="${row}"][data-col="${col}"]`);
        
        // Reset classes
        cellElement.className = 'cell';
        
        if (cell.isRevealed) {
            cellElement.classList.add('revealed');
            
            if (cell.isMine) {
                cellElement.classList.add('mine');
                cellElement.innerHTML = '<i class="fas fa-bomb"></i>';
            } else if (cell.adjacentMines > 0) {
                cellElement.dataset.mines = cell.adjacentMines;
                cellElement.textContent = cell.adjacentMines;
            } else {
                cellElement.textContent = '';
            }
        } else if (cell.isFlagged) {
            cellElement.classList.add('flagged');
            cellElement.innerHTML = '<i class="fas fa-flag"></i>';
        } else {
            cellElement.textContent = '';
        }
    }
    
    // Reveal all mines (for game over)
    function revealAllMines() {
        for (let row = 0; row < gameState.gridSize; row++) {
            for (let col = 0; col < gameState.gridSize; col++) {
                if (gameState.grid[row][col].isMine) {
                    gameState.grid[row][col].isRevealed = true;
                    updateCell(row, col);
                    
                    // Add small delay for visual effect
                    const cellElement = document.querySelector(`.cell[data-row="${row}"][data-col="${col}"]`);
                    setTimeout(() => {
                        cellElement.classList.add('revealed');
                    }, 100 + Math.random() * 300);
                }
            }
        }
    }
    
    // Update wallet display
    function updateWalletDisplay() {
        walletValue.textContent = `${gameState.walletBalance} GA`;
        mobileWalletValue.textContent = `${gameState.walletBalance} GA`;
    }
    
    // Cashout function
    function cashout() {
        if (gameState.status !== 'PLAYING') return;
        
        // Add winnings to wallet
        const winAmount = gameState.potentialWin;
        gameState.walletBalance += winAmount;
        updateWalletDisplay();
        
        // Play cashout sound
        sounds.cashout.play();
        
        // Update game status
        gameStatus.textContent = `💰 Cashed out! You won ${winAmount} GA.`;
        gameStatus.className = 'game-status win';
        
        // Add to game history
        addToGameHistory({
            id: gameState.gameHistory.length + 1,
            mines: gameState.totalMines,
            bet: gameState.currentBet,
            multiplier: gameState.multiplier,
            profit: winAmount - gameState.currentBet,
            result: 'cashout',
            time: new Date()
        });
        
        // Change game status and disable cashout button
        gameState.status = 'WIN';
        cashoutBtn.disabled = true;
        mobileCashoutBtn.disabled = true;
        
        // Update start button
        startGameBtn.innerHTML = '<i class="fas fa-play"></i> New Game';
        mobileStartGameBtn.innerHTML = '<i class="fas fa-play"></i> New Game';
        
        // Reveal all mines for visual effect
        revealAllMines();
    }
    
    // Game over (win or lose)
    function gameOver(isWin) {
        gameState.status = isWin ? 'WIN' : 'LOSS';
        
        if (isWin) {
            // Add winnings to wallet
            const winAmount = gameState.potentialWin;
            gameState.walletBalance += winAmount;
            updateWalletDisplay();
            
            // Play win sound
            sounds.win.play();
            
            gameStatus.textContent = `🎉 You won ${winAmount} GA!`;
            gameStatus.className = 'game-status win';
            
            // Add win animation to game board
            gameBoard.classList.add('win-animation');
            setTimeout(() => {
                gameBoard.classList.remove('win-animation');
            }, 3000);
            
            // Add to game history
            addToGameHistory({
                id: gameState.gameHistory.length + 1,
                mines: gameState.totalMines,
                bet: gameState.currentBet,
                multiplier: gameState.multiplier,
                profit: winAmount - gameState.currentBet,
                result: 'win',
                time: new Date()
            });
        } else {
            // Reset multiplier on mine hit
            gameState.multiplier = 1.0;
            multiplierValue.textContent = '1.0×';
            payoutValue.textContent = '0 GA';
            
            // Reveal all mines
            revealAllMines();
            
            gameStatus.textContent = `💥 Game over! You lost ${gameState.currentBet} GA.`;
            gameStatus.className = 'game-status loss';
            
            // Add to game history
            addToGameHistory({
                id: gameState.gameHistory.length + 1,
                mines: gameState.totalMines,
                bet: gameState.currentBet,
                multiplier: 1.0,
                profit: -gameState.currentBet,
                result: 'loss',
                time: new Date()
            });
        }
        
        // Disable cashout button
        cashoutBtn.disabled = true;
        mobileCashoutBtn.disabled = true;
        
        // Update start button
        startGameBtn.innerHTML = '<i class="fas fa-play"></i> New Game';
        mobileStartGameBtn.innerHTML = '<i class="fas fa-play"></i> New Game';
    }
    
    // Add to game history
    function addToGameHistory(game) {
        // Add to game history array (limit to last 20 games)
        gameState.gameHistory.unshift(game);
        if (gameState.gameHistory.length > 20) {
            gameState.gameHistory.pop();
        }
        
        // Update history table
        updateHistoryTable();
    }
    
    // Update history table
    function updateHistoryTable() {
        historyTableBody.innerHTML = '';
        
        gameState.gameHistory.forEach(game => {
            const row = document.createElement('tr');
            
            // Format time
            const timeString = formatTime(game.time);
            
            // Create profit class
            const profitClass = game.profit >= 0 ? 'win' : 'loss';
            
            row.innerHTML = `
                <td>#${game.id}</td>
                <td>${game.mines}</td>
                <td>${game.bet} GA</td>
                <td>${game.multiplier.toFixed(1)}×</td>
                <td class="${profitClass}">${game.profit > 0 ? '+' : ''}${game.profit} GA</td>
                <td>${timeString}</td>
            `;
            
            historyTableBody.appendChild(row);
        });
    }
    
    // Format time for history
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    // Update mines count input
    function updateMinesCount(value) {
        let minesCount = parseInt(minesCountInput.value);
        
        // Apply change
        minesCount += value;
        
        // Validate min/max
        const minMines = 1;
        const maxMines = Math.floor(gameState.gridSize * gameState.gridSize * 0.4); // Max 40% of cells
        
        if (minesCount < minMines) minesCount = minMines;
        if (minesCount > maxMines) minesCount = maxMines;
        
        // Update input and game state
        minesCountInput.value = minesCount;
        gameState.totalMines = minesCount;
        
        // Update flags display
        flagsValue.textContent = `0/${minesCount}`;
        
        // Update active mines preset button
        updateActiveMinesPreset(minesCount);
    }
    
    // Update active mines preset button
    function updateActiveMinesPreset(count) {
        // Remove active class from all preset buttons
        minesPresetBtns.forEach(btn => btn.classList.remove('active'));
        
        // Add active class to matching preset button if exists
        const matchingBtn = Array.from(minesPresetBtns).find(btn => parseInt(btn.dataset.mines) === count);
        if (matchingBtn) {
            matchingBtn.classList.add('active');
        }
    }
    
    // Toggle view mode
    function toggleViewMode(mode) {
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
            standardViewBtn.classList.add('active');
            proViewBtn.classList.remove('active');
            gameState.viewMode = 'standard';
        } else {
            document.body.classList.add('pro-view-active');
            proViewBtn.classList.add('active');
            standardViewBtn.classList.remove('active');
            gameState.viewMode = 'pro';
        }
    }
    
    // Toggle sound
    function toggleSound() {
        gameState.soundEnabled = !gameState.soundEnabled;
        
        if (gameState.soundEnabled) {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        } else {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        }
    }
    
    // EVENT LISTENERS
    
    // Start game button
    startGameBtn.addEventListener('click', () => {
        if (gameState.status === 'PLAYING' || gameState.status === 'WIN' || gameState.status === 'LOSS') {
            initGame();
        } else {
            // If game hasn't started, simulate a click on first cell
            const firstCell = document.querySelector('.cell');
            if (firstCell) {
                firstCell.click();
            }
        }
    });
    
    // Mobile start game button
    mobileStartGameBtn.addEventListener('click', () => {
        if (gameState.status === 'PLAYING' || gameState.status === 'WIN' || gameState.status === 'LOSS') {
            initGame();
        } else {
            // If game hasn't started, simulate a click on first cell
            const firstCell = document.querySelector('.cell');
            if (firstCell) {
                firstCell.click();
            }
        }
    });
    
    // Cashout button
    cashoutBtn.addEventListener('click', cashout);
    mobileCashoutBtn.addEventListener('click', cashout);
    
    // Toggle between reveal and flag mode
    revealModeBtn.addEventListener('click', () => {
        gameState.revealMode = true;
        revealModeBtn.classList.add('active');
        flagModeBtn.classList.remove('active');
        mobileRevealModeBtn.classList.add('active');
        mobileFlagModeBtn.classList.remove('active');
        sounds.click.play();
    });
    
    flagModeBtn.addEventListener('click', () => {
        gameState.revealMode = false;
        flagModeBtn.classList.add('active');
        revealModeBtn.classList.remove('active');
        mobileFlagModeBtn.classList.add('active');
        mobileRevealModeBtn.classList.remove('active');
        sounds.click.play();
    });
    
    // Mobile mode buttons
    mobileRevealModeBtn.addEventListener('click', () => {
        gameState.revealMode = true;
        revealModeBtn.classList.add('active');
        flagModeBtn.classList.remove('active');
        mobileRevealModeBtn.classList.add('active');
        mobileFlagModeBtn.classList.remove('active');
        sounds.click.play();
    });
    
    mobileFlagModeBtn.addEventListener('click', () => {
        gameState.revealMode = false;
        flagModeBtn.classList.add('active');
        revealModeBtn.classList.remove('active');
        mobileFlagModeBtn.classList.add('active');
        mobileRevealModeBtn.classList.remove('active');
        sounds.click.play();
    });
    
    // Bet adjustment buttons
    decreaseBetBtn.addEventListener('click', () => {
        const currentBet = parseInt(betAmountInput.value);
        if (currentBet >= 20) {
            betAmountInput.value = currentBet - 10;
            gameState.betAmount = currentBet - 10;
            sounds.click.play();
        }
    });
    
    increaseBetBtn.addEventListener('click', () => {
        const currentBet = parseInt(betAmountInput.value);
        // Only allow increasing if player has enough balance
        if (currentBet + 10 <= gameState.walletBalance) {
            betAmountInput.value = currentBet + 10;
            gameState.betAmount = currentBet + 10;
            sounds.click.play();
        } else {
            gameStatus.textContent = 'Insufficient balance for higher bet!';
            setTimeout(() => {
                if (gameState.status === 'READY') {
                    gameStatus.textContent = 'Place your bet and click any cell to start!';
                }
            }, 1500);
        }
    });
    
    // Mines adjustment buttons
    decreaseMinesBtn.addEventListener('click', () => {
        updateMinesCount(-1);
        sounds.click.play();
    });
    
    increaseMinesBtn.addEventListener('click', () => {
        updateMinesCount(1);
        sounds.click.play();
    });
    
    // Bet input validation
    betAmountInput.addEventListener('change', () => {
        let value = parseInt(betAmountInput.value);
        
        // Enforce minimum bet
        if (value < 10 || isNaN(value)) {
            value = 10;
        }
        
        // Enforce maximum bet (wallet balance)
        if (value > gameState.walletBalance) {
            value = gameState.walletBalance;
        }
        
        // Ensure bet is in multiples of 10
        value = Math.floor(value / 10) * 10;
        
        betAmountInput.value = value;
        gameState.betAmount = value;
    });
    
    // Mines input validation
    minesCountInput.addEventListener('change', () => {
        let value = parseInt(minesCountInput.value);
        
        // Enforce minimum mines
        if (value < 1 || isNaN(value)) {
            value = 1;
        }
        
        // Enforce maximum mines (40% of cells)
        const maxMines = Math.floor(gameState.gridSize * gameState.gridSize * 0.4);
        if (value > maxMines) {
            value = maxMines;
        }
        
        // Update input and game state
        minesCountInput.value = value;
        gameState.totalMines = value;
        
        // Update flags display
        flagsValue.textContent = `0/${value}`;
        
        // Update active mines preset button
        updateActiveMinesPreset(value);
    });
    
    // Grid size change
    gridSizeSelect.addEventListener('change', () => {
        // Update grid size
        gameState.gridSize = parseInt(gridSizeSelect.value);
        
        // Recalculate max mines
        const maxMines = Math.floor(gameState.gridSize * gameState.gridSize * 0.4);
        if (gameState.totalMines > maxMines) {
            gameState.totalMines = maxMines;
            minesCountInput.value = maxMines;
        }
        
        // Regenerate grid
        initGame();
    });
    
    // Bet preset buttons
    betPresetBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            if (gameState.status !== 'READY') return;
            
            const amount = btn.dataset.amount;
            
            if (amount === 'max') {
                // Set to max (wallet balance)
                betAmountInput.value = gameState.walletBalance;
                gameState.betAmount = gameState.walletBalance;
            } else {
                const presetAmount = parseInt(amount);
                
                // Only set if player has enough balance
                if (presetAmount <= gameState.walletBalance) {
                    betAmountInput.value = presetAmount;
                    gameState.betAmount = presetAmount;
                } else {
                    gameStatus.textContent = 'Insufficient balance for this bet!';
                    setTimeout(() => {
                        if (gameState.status === 'READY') {
                            gameStatus.textContent = 'Place your bet and click any cell to start!';
                        }
                    }, 1500);
                    return;
                }
            }
            
            sounds.click.play();
        });
    });
    
    // Mines preset buttons
    minesPresetBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            if (gameState.status !== 'READY') return;
            
            const minesCount = parseInt(btn.dataset.mines);
            
            // Update mines count input and game state
            minesCountInput.value = minesCount;
            gameState.totalMines = minesCount;
            
            // Update flags display
            flagsValue.textContent = `0/${minesCount}`;
            
            // Update active button
            minesPresetBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            sounds.click.play();
        });
    });
    
    // History toggle
    historyToggle.addEventListener('click', () => {
        const historyContent = document.querySelector('.history-content');
        historyContent.classList.toggle('expanded');
        
        if (historyContent.classList.contains('expanded')) {
            historyToggle.innerHTML = '<i class="fas fa-chevron-up"></i>';
        } else {
            historyToggle.innerHTML = '<i class="fas fa-chevron-down"></i>';
        }
        
        sounds.click.play();
    });
    
    // View mode toggle
    standardViewBtn.addEventListener('click', () => {
        toggleViewMode('standard');
        sounds.click.play();
    });
    
    proViewBtn.addEventListener('click', () => {
        toggleViewMode('pro');
        sounds.click.play();
    });
    
    // Sound toggle
    soundToggleBtn.addEventListener('click', () => {
        toggleSound();
    });
    
    // Handle sidebar toggle for proper layout
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    menuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('open');
        mainContent.classList.toggle('sidebar-open');
        
        // Adjust the game board if needed
        setTimeout(() => {
            // Force redraw of grid to adjust to new width
            const currentGridSize = gameState.gridSize;
            gameBoard.style.gridTemplateColumns = `repeat(${currentGridSize}, 1fr)`;
        }, 300);
    });
    
    // Screen orientation change handling
    window.addEventListener('orientationchange', function() {
        // Force redraw of grid to adjust to new dimensions
        setTimeout(() => {
            const currentGridSize = gameState.gridSize;
            gameBoard.style.gridTemplateColumns = `repeat(${currentGridSize}, 1fr)`;
        }, 300);
    });
    
    // Setup sounds (use placeholder sounds for now)
    setupPlaceholderSounds();
    
    // Initialize game on load
    initGame();
});