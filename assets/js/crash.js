// Crash Game JavaScript - Core Gameplay Logic
document.addEventListener('DOMContentLoaded', function() {
    // Game Configuration
    const GAME_CONFIG = {
        BETTING_PHASE_DURATION: 15000, // 15 seconds
        COUNTDOWN_DURATION: 5000,       // 5 seconds
        HOUSE_EDGE: 0.01,              // 1% house edge
        MIN_MULTIPLIER: 1.01,          // Minimum crash point
        MAX_MULTIPLIER: 1000000,       // Maximum theoretical crash
        UPDATE_INTERVAL: 100,          // 100ms update rate
        CHART_HISTORY_POINTS: 100      // Number of points to show on chart
    };

    // Game State Machine
    const GAME_STATES = {
        WAITING: 'WAITING',
        BETTING: 'BETTING', 
        COUNTDOWN: 'COUNTDOWN',
        IN_PROGRESS: 'IN_PROGRESS',
        CRASHED: 'CRASHED'
    };

    // DOM Elements
    const gameStateElement = document.getElementById('gameState');
    const timeRemainingElement = document.getElementById('timeRemaining');
    const currentMultiplierElement = document.getElementById('currentMultiplier');
    const placeBetBtn = document.getElementById('placeBetBtn');
    const cashOutBtn = document.getElementById('cashOutBtn');
    const betAmountInput = document.getElementById('betAmount');
    const autoCashoutInput = document.getElementById('autoCashout');
    const chartCanvas = document.getElementById('multiplierChart');
    const crashOverlay = document.getElementById('crashOverlay');
    const crashMultiplierElement = document.getElementById('crashMultiplier');
    const playersListElement = document.getElementById('playersList');
    const crashHistoryElement = document.getElementById('crashHistory');
    const clientSeedElement = document.getElementById('clientSeed');
    const serverSeedHashElement = document.getElementById('serverSeedHash');
    const roundIdElement = document.getElementById('roundId');
    const newSeedBtn = document.getElementById('newSeedBtn');
    
    // Check if all required elements exist
    const missingElements = [];
    if (!gameStateElement) missingElements.push('gameState');
    if (!timeRemainingElement) missingElements.push('timeRemaining');
    if (!currentMultiplierElement) missingElements.push('currentMultiplier');
    if (!placeBetBtn) missingElements.push('placeBetBtn');
    if (!cashOutBtn) missingElements.push('cashOutBtn');
    if (!betAmountInput) missingElements.push('betAmount');
    if (!autoCashoutInput) missingElements.push('autoCashout');
    if (!chartCanvas) missingElements.push('multiplierChart');
    if (!crashOverlay) missingElements.push('crashOverlay');
    if (!crashMultiplierElement) missingElements.push('crashMultiplier');
    if (!playersListElement) missingElements.push('playersList');
    if (!crashHistoryElement) missingElements.push('crashHistory');
    if (!clientSeedElement) missingElements.push('clientSeed');
    if (!serverSeedHashElement) missingElements.push('serverSeedHash');
    if (!roundIdElement) missingElements.push('roundId');
    if (!newSeedBtn) missingElements.push('newSeedBtn');
    
    if (missingElements.length > 0) {
        console.error('Missing DOM elements:', missingElements.join(', '));
    }

    // Game State
    let gameState = {
        currentState: GAME_STATES.WAITING,
        timeRemaining: 0,
        currentMultiplier: 1.00,
        crashPoint: 0,
        roundStartTime: 0,
        elapsedTime: 0,
        roundId: 1,
        players: [],
        crashHistory: [],
        isPlayerActive: false,
        playerBet: 0,
        playerAutoCashout: 2.00,
        hasPlayerCashedOut: false,
        playerCashoutMultiplier: 0,
        seeds: {
            clientSeed: '',
            serverSeedHash: '',
            nonce: 1
        }
    };

    // Chart setup
    const chartCtx = chartCanvas ? chartCanvas.getContext('2d') : null;
    let chartData = [];
    let animationId = null;

    // Initialize game
    function initGame() {
        if (!chartCanvas) {
            console.error('Chart canvas not found');
            return;
        }
        setupCanvas();
        generateClientSeed();
        generateServerSeedHash();
        loadCrashHistory();
        startGameLoop();
        updateUI();
    }

    // ===== CORE GAME LOGIC =====

    // Generate cryptographically secure crash point
    function generateCrashPoint(houseEdge = GAME_CONFIG.HOUSE_EDGE) {
        // Use Web Crypto API for cryptographically secure random values
        const array = new Uint32Array(1);
        crypto.getRandomValues(array);
        const randomValue = array[0] / (0xffffffff + 1); // Normalize to [0, 1)
        
        // Ensure minimum crash point and apply house edge
        const adjustedRandom = Math.max(randomValue, 0.000001); // Prevent division by zero
        const crashPoint = (1 - houseEdge) / (1 - adjustedRandom);
        
        return Math.max(GAME_CONFIG.MIN_MULTIPLIER, Math.min(crashPoint, GAME_CONFIG.MAX_MULTIPLIER));
    }

    // Calculate current multiplier based on elapsed time and crash point
    function calculateMultiplier(elapsedTime, crashPoint) {
        if (elapsedTime <= 0) return 1.00;
        
        // Exponential growth curve - starts slow, accelerates
        const timeInSeconds = elapsedTime / 1000;
        const growthRate = Math.log(crashPoint) / 10; // Adjust curve based on crash point
        const multiplier = Math.exp(growthRate * timeInSeconds);
        
        return Math.min(multiplier, crashPoint);
    }

    // Process player cash out
    function processCashOut(player, multiplier) {
        if (!player.active || player.cashedOut) return false;
        
        player.cashedOut = true;
        player.cashoutMultiplier = multiplier;
        player.winAmount = player.betAmount * multiplier;
        
        return true;
    }

    // ===== GAME STATE MACHINE =====

    function startNewRound() {
        try {
            // Reset round state
            gameState.roundId++;
            gameState.currentMultiplier = 1.00;
            gameState.crashPoint = generateCrashPoint();
            gameState.elapsedTime = 0;
            gameState.isPlayerActive = false;
            gameState.hasPlayerCashedOut = false;
            gameState.playerCashoutMultiplier = 0;
            
            // Add crash point to history (for verification)
            console.log(`Round ${gameState.roundId} - Generated crash point: ${gameState.crashPoint.toFixed(2)}x`);
            
            // Reset chart data
            chartData = [];
            
            // Hide crash overlay
            if (crashOverlay) {
                crashOverlay.classList.remove('active');
            }
            
            // Start betting phase
            startBettingPhase();
        } catch (error) {
            console.error('Error starting new round:', error);
        }
    }

    function startBettingPhase() {
        gameState.currentState = GAME_STATES.BETTING;
        gameState.timeRemaining = GAME_CONFIG.BETTING_PHASE_DURATION;
        
        // Enable betting controls
        if (placeBetBtn) placeBetBtn.disabled = false;
        if (cashOutBtn) cashOutBtn.disabled = true;
        
        // Generate simulated players
        generateSimulatedPlayers();
        
        updateUI();
    }

    function startCountdownPhase() {
        gameState.currentState = GAME_STATES.COUNTDOWN;
        gameState.timeRemaining = GAME_CONFIG.COUNTDOWN_DURATION;
        
        // Disable betting
        if (placeBetBtn) placeBetBtn.disabled = true;
        
        updateUI();
    }

    function startMultiplierPhase() {
        gameState.currentState = GAME_STATES.IN_PROGRESS;
        gameState.roundStartTime = Date.now();
        gameState.timeRemaining = 0;
        
        // Enable cash out if player has active bet
        if (gameState.isPlayerActive && cashOutBtn) {
            cashOutBtn.disabled = false;
        }
        
        updateUI();
    }

    function crashGame() {
        gameState.currentState = GAME_STATES.CRASHED;
        
        // Add to crash history
        gameState.crashHistory.unshift(gameState.crashPoint);
        if (gameState.crashHistory.length > 20) {
            gameState.crashHistory.pop();
        }
        
        // Process remaining players (they lost)
        gameState.players.forEach(player => {
            if (player.active && !player.cashedOut) {
                player.active = false;
                player.winAmount = 0;
            }
        });
        
        // Handle player if they didn't cash out
        if (gameState.isPlayerActive && !gameState.hasPlayerCashedOut) {
            gameState.isPlayerActive = false;
            updatePlayerStats();
            showNotification("You lost! Better luck next time.", "error");
        }
        
        // Create crash particles animation
        createCrashParticles();
        
        // Show crash overlay with enhanced animation
        if (crashOverlay && crashMultiplierElement) {
            crashOverlay.classList.add('active');
            crashMultiplierElement.textContent = `${gameState.crashPoint.toFixed(2)}x`;
            
            // Add screen shake effect
            const container = document.getElementById('chartContainer');
            if (container) {
                container.classList.add('crash-animation');
                setTimeout(() => {
                    container.classList.remove('crash-animation');
                }, 500);
            }
        }
        
        // Disable cash out
        if (cashOutBtn) cashOutBtn.disabled = true;
        
        updateUI();
        updateCrashHistory();
        
        // Start new round after delay
        setTimeout(() => {
            startNewRound();
        }, 3000);
    }

    // ===== GAME LOOP =====

    function gameLoop() {
        try {
            const now = Date.now();
            
            switch (gameState.currentState) {
                case GAME_STATES.BETTING:
                    gameState.timeRemaining -= GAME_CONFIG.UPDATE_INTERVAL;
                    if (gameState.timeRemaining <= 0) {
                        startCountdownPhase();
                    }
                    break;
                    
                case GAME_STATES.COUNTDOWN:
                    gameState.timeRemaining -= GAME_CONFIG.UPDATE_INTERVAL;
                    if (gameState.timeRemaining <= 0) {
                        startMultiplierPhase();
                    }
                    break;
                    
                case GAME_STATES.IN_PROGRESS:
                    gameState.elapsedTime = now - gameState.roundStartTime;
                    gameState.currentMultiplier = calculateMultiplier(gameState.elapsedTime, gameState.crashPoint);
                    
                    // Check if we should crash
                    if (gameState.currentMultiplier >= gameState.crashPoint) {
                        gameState.currentMultiplier = gameState.crashPoint;
                        crashGame();
                        break;
                    }
                    
                    // Update chart data
                    updateChartData();
                    
                    // Process auto-cashouts
                    processAutoCashouts();
                    
                    break;
                    
                case GAME_STATES.CRASHED:
                    // Wait for next round
                    break;
            }
            
            updateUI();
            renderChart();
        } catch (error) {
            console.error('Error in game loop:', error);
        }
        
        // Continue game loop
        setTimeout(gameLoop, GAME_CONFIG.UPDATE_INTERVAL);
    }

    // ===== PLAYER ACTIONS =====

    // Make placeBet accessible from HTML onclick
    window.placeBet = function() {
        console.log('Current game state:', gameState.currentState);
        
        // Allow betting during WAITING, BETTING, or COUNTDOWN phases
        if (gameState.currentState === GAME_STATES.IN_PROGRESS || gameState.currentState === GAME_STATES.CRASHED) {
            showNotification("Betting is closed! Wait for the next round.", "error");
            return false;
        }
        
        if (!betAmountInput || !autoCashoutInput) return false;
        
        const betAmount = parseInt(betAmountInput.value);
        const autoCashout = parseFloat(autoCashoutInput.value);
        
        if (betAmount < 1 || autoCashout < 1.01) {
            showNotification("Please enter a valid bet amount and auto-cashout value!", "error");
            return false;
        }
        
        // Trigger bet placed animation
        triggerBetPlacedAnimation();
        
        gameState.isPlayerActive = true;
        gameState.playerBet = betAmount;
        gameState.playerAutoCashout = autoCashout;
        gameState.hasPlayerCashedOut = false;
        
        if (placeBetBtn) placeBetBtn.disabled = true;
        updatePlayerStats();
        
        showNotification(`Bet placed: ${betAmount} @ ${autoCashout}x`, "success");
        return true;
    }

    // Make cashOut accessible from HTML onclick
    window.cashOut = function() {
        if (!gameState.isPlayerActive || gameState.hasPlayerCashedOut) {
            showNotification("No active bet to cash out!", "error");
            return false;
        }
        if (gameState.currentState !== GAME_STATES.IN_PROGRESS) {
            showNotification("Cash out is only available during the in-progress phase!", "error");
            return false;
        }
        
        gameState.hasPlayerCashedOut = true;
        gameState.playerCashoutMultiplier = gameState.currentMultiplier;
        gameState.isPlayerActive = false;
        
        // Calculate win amount
        const winAmount = gameState.playerBet * gameState.playerCashoutMultiplier;
        
        // Trigger cashout animation
        triggerCashoutAnimation(winAmount);
        
        if (cashOutBtn) cashOutBtn.disabled = true;
        updatePlayerStats();
        
        showNotification("Cashed out successfully at " + gameState.currentMultiplier.toFixed(2) + "x!", "success");
        return true;
    }

    function processAutoCashouts() {
        // Process player auto-cashout
        if (gameState.isPlayerActive && !gameState.hasPlayerCashedOut) {
            if (gameState.currentMultiplier >= gameState.playerAutoCashout) {
                cashOut();
            }
        }
        
        // Process simulated players auto-cashouts
        gameState.players.forEach(player => {
            if (player.active && !player.cashedOut) {
                if (gameState.currentMultiplier >= player.autoCashout) {
                    processCashOut(player, gameState.currentMultiplier);
                }
            }
        });
    }

    // ===== CHART RENDERING =====

    function setupCanvas() {
        if (!chartCanvas || !chartCtx) {
            console.error("Canvas or context is null");
            return;
        }
        
        // Force canvas size regardless of container
        chartCanvas.width = 600;
        chartCanvas.height = 400;
        
        // Make sure canvas is visible with a border
        chartCanvas.style.width = '100%';
        chartCanvas.style.height = '100%';
        chartCanvas.style.display = 'block';
        chartCanvas.style.backgroundColor = '#1a1a1a';
        chartCanvas.style.border = '1px solid #383838';
        
        console.log('Canvas dimensions set:', chartCanvas.width, chartCanvas.height);
    }

    function updateChartData() {
        chartData.push({
            time: gameState.elapsedTime,
            multiplier: gameState.currentMultiplier
        });
        
        // Keep only recent points
        if (chartData.length > GAME_CONFIG.CHART_HISTORY_POINTS) {
            chartData.shift();
        }
    }

    function renderChart() {
        if (!chartCanvas || !chartCtx) return;
        
        try {
            // Draw a background to make sure chart is visible
            chartCtx.fillStyle = '#1a1a1a';
            chartCtx.fillRect(0, 0, chartCanvas.width, chartCanvas.height);
            
            // Add a visible border
            chartCtx.strokeStyle = '#383838';
            chartCtx.lineWidth = 2;
            chartCtx.strokeRect(0, 0, chartCanvas.width, chartCanvas.height);
            
            if (chartData.length < 2) {
                // Draw some text if no data yet
                chartCtx.fillStyle = '#555';
                chartCtx.font = '20px Poppins';
                chartCtx.textAlign = 'center';
                chartCtx.fillText('Waiting for game to start...', chartCanvas.width/2, chartCanvas.height/2);
                return;
            }
            
            // Calculate scale
            const maxTime = Math.max(...chartData.map(d => d.time));
            const maxMultiplier = Math.max(...chartData.map(d => d.multiplier));
            
            const scaleX = rect.width / maxTime;
            const scaleY = rect.height / maxMultiplier;
            
            // Draw grid
            chartCtx.strokeStyle = '#333';
            chartCtx.lineWidth = 1;
            
            // Vertical grid lines
            for (let i = 1; i <= 10; i++) {
                const x = (rect.width / 10) * i;
                chartCtx.beginPath();
                chartCtx.moveTo(x, 0);
                chartCtx.lineTo(x, rect.height);
                chartCtx.stroke();
            }
            
            // Horizontal grid lines
            for (let i = 1; i <= 10; i++) {
                const y = (rect.height / 10) * i;
                chartCtx.beginPath();
                chartCtx.moveTo(0, y);
                chartCtx.lineTo(rect.width, y);
                chartCtx.stroke();
            }
            
            // Draw multiplier curve
            chartCtx.strokeStyle = '#00c853';
            chartCtx.lineWidth = 3;
            chartCtx.beginPath();
            
            chartData.forEach((point, index) => {
                const x = point.time * scaleX;
                const y = rect.height - (point.multiplier * scaleY);
                
                if (index === 0) {
                    chartCtx.moveTo(x, y);
                } else {
                    chartCtx.lineTo(x, y);
                }
            });
            
            chartCtx.stroke();
            
            // Draw multiplier labels
            chartCtx.fillStyle = 'white';
            chartCtx.font = '14px Poppins';
            chartCtx.textAlign = 'right';
            
            for (let i = 1; i <= 5; i++) {
                const multiplier = (maxMultiplier / 5) * i;
                const y = rect.height - (multiplier * scaleY);
                chartCtx.fillText(`${multiplier.toFixed(2)}x`, rect.width - 10, y + 5);
            }
        } catch (error) {
            console.error('Error rendering chart:', error);
        }
    }

    // ===== SIMULATED PLAYERS =====

    function generateSimulatedPlayers() {
        const playerNames = ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve', 'Frank', 'Grace', 'Henry'];
        gameState.players = [];
        
        const numPlayers = Math.floor(Math.random() * 6) + 3; // 3-8 players
        
        for (let i = 0; i < numPlayers; i++) {
            const name = playerNames[Math.floor(Math.random() * playerNames.length)] + Math.floor(Math.random() * 1000);
            const betAmount = Math.floor(Math.random() * 95) + 5; // 5-100 bet
            const autoCashout = 1.1 + Math.random() * 4.9; // 1.1x - 6.0x
            
            gameState.players.push({
                name,
                betAmount,
                autoCashout,
                active: true,
                cashedOut: false,
                cashoutMultiplier: 0,
                winAmount: 0
            });
        }
        
        updatePlayersList();
    }

    // ===== UI UPDATES =====

    function updateUI() {
        // Update game state display
        if (gameStateElement) {
            gameStateElement.textContent = gameState.currentState;
            gameStateElement.className = gameState.currentState.toLowerCase();
            
            // Update betting phase indicator
            const bettingPhaseElement = document.querySelector('.status-info div');
            const phaseTextElement = document.getElementById('phaseText');
            
            if (bettingPhaseElement && phaseTextElement) {
                // Remove animation classes first
                phaseTextElement.classList.remove('countdown-animation');
                
                if (gameState.currentState === GAME_STATES.BETTING) {
                    bettingPhaseElement.style.color = '#00c853';
                    bettingPhaseElement.innerHTML = '<span style="display: inline-block; width: 8px; height: 8px; background-color: #00c853; border-radius: 50%; margin-right: 4px;"></span> <span id="phaseText" class="countdown-animation">Betting Phase: Bet now!</span>';
                } else if (gameState.currentState === GAME_STATES.COUNTDOWN) {
                    bettingPhaseElement.style.color = '#ffd600';
                    bettingPhaseElement.innerHTML = '<span style="display: inline-block; width: 8px; height: 8px; background-color: #ffd600; border-radius: 50%; margin-right: 4px;"></span> <span id="phaseText" class="countdown-animation">Countdown: Get ready!</span>';
                } else if (gameState.currentState === GAME_STATES.IN_PROGRESS) {
                    bettingPhaseElement.style.color = '#ff4b4b';
                    bettingPhaseElement.innerHTML = '<span style="display: inline-block; width: 8px; height: 8px; background-color: #ff4b4b; border-radius: 50%; margin-right: 4px;"></span> <span id="phaseText">In Progress: Cash out!</span>';
                    
                    // Show the multiplier display
                    toggleMultiplierDisplay(true);
                } else if (gameState.currentState === GAME_STATES.CRASHED) {
                    bettingPhaseElement.style.color = '#ff4b4b';
                    bettingPhaseElement.innerHTML = '<span style="display: inline-block; width: 8px; height: 8px; background-color: #ff4b4b; border-radius: 50%; margin-right: 4px;"></span> <span id="phaseText" class="crash-animation">Crashed! Wait for next round.</span>';
                    
                    // Hide the multiplier display
                    toggleMultiplierDisplay(false);
                } else {
                    bettingPhaseElement.style.color = '#9e9e9e';
                    bettingPhaseElement.innerHTML = '<span style="display: inline-block; width: 8px; height: 8px; background-color: #9e9e9e; border-radius: 50%; margin-right: 4px;"></span> <span id="phaseText">Waiting for next round...</span>';
                }
            }
        }
        
        // Update time remaining
        if (timeRemainingElement) {
            if (gameState.timeRemaining > 0) {
                timeRemainingElement.textContent = `${Math.ceil(gameState.timeRemaining / 1000)}s`;
            } else {
                timeRemainingElement.textContent = '--';
            }
        }
        
        // Update current multiplier
        if (currentMultiplierElement) {
            // Determine color based on multiplier value
            let color = '#ffffff';
            if (gameState.currentMultiplier >= 2) color = '#ffd600';
            if (gameState.currentMultiplier >= 5) color = '#ff9800';
            if (gameState.currentMultiplier >= 10) color = '#ff4b4b';
            
            currentMultiplierElement.textContent = `${gameState.currentMultiplier.toFixed(2)}x`;
            currentMultiplierElement.style.color = color;
            
            // Update big multiplier display in the center
            const bigMultiplierElement = document.getElementById('bigMultiplier');
            if (bigMultiplierElement && gameState.currentState === GAME_STATES.IN_PROGRESS) {
                bigMultiplierElement.textContent = `${gameState.currentMultiplier.toFixed(2)}x`;
                bigMultiplierElement.style.color = color;
                bigMultiplierElement.className = 'multiplier-growing';
                
                // Scale based on multiplier size
                const scale = Math.min(1 + (gameState.currentMultiplier / 20), 1.5);
                bigMultiplierElement.style.transform = `scale(${scale})`;
            }
        }
        
        // Update round ID
        if (roundIdElement) {
            roundIdElement.textContent = gameState.roundId;
        }
    }
    
    // Animation functions
    function toggleMultiplierDisplay(show) {
        const bettingDisplay = document.getElementById('bettingDisplay');
        const multiplierDisplay = document.getElementById('multiplierDisplay');
        
        if (bettingDisplay && multiplierDisplay) {
            if (show) {
                bettingDisplay.style.display = 'none';
                multiplierDisplay.style.display = 'block';
            } else {
                bettingDisplay.style.display = 'flex';
                multiplierDisplay.style.display = 'none';
            }
        }
    }
    
    function triggerBetPlacedAnimation() {
        const ripple = document.getElementById('betBtnRipple');
        if (ripple) {
            ripple.style.transform = 'scale(3)';
            ripple.style.opacity = '1';
            
            setTimeout(() => {
                ripple.style.transform = 'scale(0)';
                ripple.style.opacity = '0';
            }, 500);
        }
    }
    
    function triggerCashoutAnimation(amount) {
        // Button ripple effect
        const ripple = document.getElementById('cashoutBtnRipple');
        if (ripple) {
            ripple.style.transform = 'scale(3)';
            ripple.style.opacity = '1';
            
            setTimeout(() => {
                ripple.style.transform = 'scale(0)';
                ripple.style.opacity = '0';
            }, 500);
        }
        
        // Show success overlay
        const overlay = document.getElementById('cashoutSuccessOverlay');
        const amountEl = document.getElementById('cashoutAmount');
        
        if (overlay && amountEl) {
            amountEl.textContent = `+${amount.toFixed(2)}`;
            overlay.style.display = 'block';
            
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 2000);
        }
    }
    
    function createCrashParticles() {
        const container = document.getElementById('particlesContainer');
        if (!container) return;
        
        // Clear previous particles
        container.innerHTML = '';
        
        // Create new particles
        for (let i = 0; i < 30; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random size between 3px and 8px
            const size = Math.random() * 5 + 3;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            
            // Random position
            const posX = Math.random() * 100;
            const posY = Math.random() * 100;
            particle.style.left = `${posX}%`;
            particle.style.top = `${posY}%`;
            
            // Random animation
            const duration = Math.random() * 1 + 0.5;
            const delay = Math.random() * 0.5;
            particle.style.animation = `fadeInUp ${duration}s ${delay}s both`;
            
            container.appendChild(particle);
        }
    }
    
    function showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('gameNotification');
        
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'gameNotification';
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.padding = '10px 20px';
            notification.style.borderRadius = '5px';
            notification.style.fontWeight = '600';
            notification.style.zIndex = '1000';
            notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
            notification.style.transition = 'all 0.3s ease';
            notification.style.transform = 'translateX(120%)';
            document.body.appendChild(notification);
        }
        
        // Set style based on type
        if (type === 'error') {
            notification.style.backgroundColor = '#ff4b4b';
            notification.style.color = 'white';
        } else if (type === 'success') {
            notification.style.backgroundColor = '#00c853';
            notification.style.color = 'white';
        } else {
            notification.style.backgroundColor = '#333';
            notification.style.color = 'white';
        }
        
        // Set message and show
        notification.textContent = message;
        notification.style.transform = 'translateX(0)';
        
        // Hide after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(120%)';
        }, 3000);
    }

    function updatePlayerStats() {
        try {
            const currentBetElement = document.getElementById('currentBet');
            const potentialWinElement = document.getElementById('potentialWin');
            const lastWinElement = document.getElementById('lastWin');
            
            if (currentBetElement) {
                currentBetElement.textContent = gameState.isPlayerActive ? gameState.playerBet : '0';
            }
            
            if (potentialWinElement) {
                let potentialWin = 0;
                if (gameState.isPlayerActive) {
                    potentialWin = gameState.playerBet * gameState.currentMultiplier;
                }
                potentialWinElement.textContent = potentialWin.toFixed(2);
            }
            
            if (lastWinElement) {
                let lastWin = 0;
                if (gameState.hasPlayerCashedOut) {
                    lastWin = gameState.playerBet * gameState.playerCashoutMultiplier;
                }
                lastWinElement.textContent = lastWin.toFixed(2);
            }
        } catch (error) {
            console.error('Error updating player stats:', error);
        }
    }

    function updatePlayersList() {
        if (!playersListElement) return;
        
        try {
            playersListElement.innerHTML = '';
            
            gameState.players.forEach(player => {
                const playerCard = document.createElement('div');
                playerCard.className = 'player-card';
                
                const statusClass = player.cashedOut ? 'cashed-out' : (player.active ? '' : 'bust');
                
                playerCard.innerHTML = `
                    <div class="player-name">${player.name}</div>
                    <div class="player-bet">Bet: ${player.betAmount}</div>
                    <div class="player-target">Target: ${player.autoCashout.toFixed(2)}x</div>
                    <div class="player-status ${statusClass}"></div>
                `;
                
                playersListElement.appendChild(playerCard);
            });
        } catch (error) {
            console.error('Error updating players list:', error);
        }
    }

    function updateCrashHistory() {
        if (!crashHistoryElement) return;
        
        try {
            crashHistoryElement.innerHTML = '';
            
            gameState.crashHistory.forEach(crashPoint => {
                const historyItem = document.createElement('div');
                historyItem.className = 'crash-history-item';
                
                // Color code based on crash point
                if (crashPoint >= 10.0) {
                    historyItem.classList.add('crash-history-high');
                } else if (crashPoint >= 2.0) {
                    historyItem.classList.add('crash-history-medium');
                } else {
                    historyItem.classList.add('crash-history-low');
                }
                
                historyItem.textContent = `${crashPoint.toFixed(2)}x`;
                crashHistoryElement.appendChild(historyItem);
            });
        } catch (error) {
            console.error('Error updating crash history:', error);
        }
    }

    // ===== PROVABLY FAIR =====

    function generateClientSeed() {
        if (!clientSeedElement) return;
        
        try {
            const array = new Uint8Array(16);
            crypto.getRandomValues(array);
            gameState.seeds.clientSeed = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
            clientSeedElement.textContent = gameState.seeds.clientSeed;
        } catch (error) {
            console.error('Error generating client seed:', error);
            gameState.seeds.clientSeed = 'error-generating-seed';
            if (clientSeedElement) {
                clientSeedElement.textContent = gameState.seeds.clientSeed;
            }
        }
    }

    function generateServerSeedHash() {
        if (!serverSeedHashElement) return;
        
        try {
            // In a real implementation, this would be provided by the server
            const array = new Uint8Array(32);
            crypto.getRandomValues(array);
            gameState.seeds.serverSeedHash = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
            serverSeedHashElement.textContent = gameState.seeds.serverSeedHash;
        } catch (error) {
            console.error('Error generating server seed hash:', error);
            gameState.seeds.serverSeedHash = 'error-generating-hash';
            if (serverSeedHashElement) {
                serverSeedHashElement.textContent = gameState.seeds.serverSeedHash;
            }
        }
    }

    function loadCrashHistory() {
        // Initialize with some sample data
        gameState.crashHistory = [2.34, 1.23, 15.67, 1.05, 3.21, 7.89, 1.78, 2.56, 1.12, 4.33];
        updateCrashHistory();
    }

    // ===== EVENT LISTENERS =====

    if (placeBetBtn) {
        placeBetBtn.addEventListener('click', window.placeBet);
        console.log('Place bet button listener added');
    } else {
        console.error('Place bet button not found in DOM');
    }
    
    if (cashOutBtn) {
        cashOutBtn.addEventListener('click', window.cashOut);
        console.log('Cash out button listener added');
    } else {
        console.error('Cash out button not found in DOM');
    }
    
    if (newSeedBtn) {
        newSeedBtn.addEventListener('click', generateClientSeed);
    }

    // Update potential win as user types
    if (betAmountInput) {
        betAmountInput.addEventListener('input', updatePlayerStats);
    }
    
    if (autoCashoutInput) {
        autoCashoutInput.addEventListener('input', updatePlayerStats);
    }

    // Handle sidebar toggle
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (menuToggle && sidebar && mainContent) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('open');
            mainContent.classList.toggle('sidebar-open');
        });
    }

    // Handle window resize
    window.addEventListener('resize', setupCanvas);

    // ===== INITIALIZE GAME =====
    console.log('Initializing Crash game...');
    
    // Log if any DOM elements are missing
    if (missingElements.length > 0) {
        console.warn('Some UI elements are missing. Game UI may not display correctly.');
    } else {
        console.log('All UI elements found.');
    }
    
    initGame();
    startNewRound();
});