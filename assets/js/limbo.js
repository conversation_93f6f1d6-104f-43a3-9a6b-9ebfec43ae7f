// Limbo Game JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Game elements
    const targetInput = document.getElementById('targetMultiplier');
    const betAmountInput = document.getElementById('betAmount');
    const balanceValue = document.getElementById('balanceValue');
    const mobileBalanceValue = document.getElementById('mobileBalanceValue');
    const mobileLastWin = document.getElementById('mobileLastWin');
    const overBtn = document.getElementById('overBtn');
    const underBtn = document.getElementById('underBtn');
    const mobileOverBtn = document.getElementById('mobileOverBtn');
    const mobileUnderBtn = document.getElementById('mobileUnderBtn');
    const playBtn = document.getElementById('playBtn');
    const mobilePlayBtn = document.getElementById('mobilePlayBtn');
    const autoBetBtn = document.getElementById('autoBetBtn');
    const mobileAutoBetBtn = document.getElementById('mobileAutoBetBtn');
    const outcomeValue = document.getElementById('outcomeValue');
    const outcomeStatus = document.getElementById('outcomeStatus');
    const gameStatus = document.getElementById('gameStatus');
    const headerWinLossIndicator = document.getElementById('headerWinLossIndicator');
    const winLossIndicator = document.getElementById('winLossIndicator');
    const decreaseBetBtn = document.getElementById('decreaseBet');
    const increaseBetBtn = document.getElementById('increaseBet');
    const decreaseMultiplierBtn = document.getElementById('decreaseMultiplier');
    const increaseMultiplierBtn = document.getElementById('increaseMultiplier');
    const soundToggleBtn = document.getElementById('soundToggle');
    const standardViewBtn = document.getElementById('standardView');
    const proViewBtn = document.getElementById('proView');
    
    // Probability and payout displays
    const overProbability = document.getElementById('overProbability');
    const underProbability = document.getElementById('underProbability');
    const overPayout = document.getElementById('overPayout');
    const underPayout = document.getElementById('underPayout');
    const mobileOverInfo = document.getElementById('mobileOverInfo');
    const mobileUnderInfo = document.getElementById('mobileUnderInfo');
    
    // Visual elements
    const targetMarker = document.getElementById('targetMarker');
    const resultMarker = document.getElementById('resultMarker');
    
    // Provably fair elements
    const clientSeed = document.getElementById('clientSeed');
    const serverSeedHash = document.getElementById('serverSeedHash');
    const nonceElement = document.getElementById('nonce');
    const regenerateClientSeedBtn = document.getElementById('regenerateClientSeed');
    const verifyBtn = document.getElementById('verifyBtn');
    
    // Statistics elements
    const gamesPlayed = document.getElementById('gamesPlayed');
    const winRate = document.getElementById('winRate');
    const totalWagered = document.getElementById('totalWagered');
    const netProfit = document.getElementById('netProfit');
    const biggestWin = document.getElementById('biggestWin');
    const avgMultiplier = document.getElementById('avgMultiplier');
    
    // History elements
    const historyTableBody = document.getElementById('historyTableBody');
    const historyPlaceholder = document.getElementById('historyPlaceholder');
    
    // Toggle elements
    const statsToggle = document.getElementById('statsToggle');
    const fairToggle = document.getElementById('fairToggle');
    const historyToggle = document.getElementById('historyToggle');
    
    // Preset buttons
    const betPresetBtns = document.querySelectorAll('.preset-btn');
    const multiplierPresetBtns = document.querySelectorAll('.multiplier-preset-btn');
    
    // Sound effects
    const sounds = {
        bet: new Audio(),
        win: new Audio(),
        lose: new Audio(),
        click: new Audio(),
        cashout: new Audio()
    };
    
    // Setup placeholder sounds
    function setupPlaceholderSounds() {
        Object.keys(sounds).forEach(key => {
            sounds[key] = {
                play: function() {
                    if (gameState.soundEnabled) {
                        console.log(`Playing sound: ${key}`);
                    }
                },
                load: function() {},
                volume: 0.5
            };
        });
    }
    
    // Game state
    let gameState = {
        selectedDirection: 'over',
        isPlaying: false,
        autoBetting: false,
        autoBetInterval: null,
        clientSeedValue: '',
        serverSeedValue: '',
        serverSeedHashValue: '',
        nonce: 1,
        lastResult: null,
        gameHistory: [],
        balance: 1000,
        betAmount: 10,
        targetMultiplier: 2.00,
        soundEnabled: true,
        viewMode: 'standard',
        
        // Statistics
        statistics: {
            gamesPlayed: 0,
            gamesWon: 0,
            totalWagered: 0,
            totalWinnings: 0,
            biggestWin: 0,
            totalMultiplier: 0
        }
    };
    
    // Initialize the game
    function initGame() {
        generateClientSeed();
        generateServerSeed();
        updateProbabilityDisplay();
        updateNonceDisplay();
        loadGameHistory();
        loadPlayerBalance();
        updateStatistics();
        
        // Set initial values
        updateBalanceDisplay();
        betAmountInput.value = gameState.betAmount;
        targetInput.value = gameState.targetMultiplier.toFixed(2);
    }
    
    // Load player balance from localStorage
    function loadPlayerBalance() {
        const savedBalance = localStorage.getItem('goldenAuraBalance');
        if (savedBalance !== null) {
            gameState.balance = parseInt(savedBalance);
        }
    }
    
    // Save player balance to localStorage
    function savePlayerBalance() {
        localStorage.setItem('goldenAuraBalance', gameState.balance.toString());
    }
    
    // Update balance display
    function updateBalanceDisplay() {
        balanceValue.textContent = `${gameState.balance} GA`;
        mobileBalanceValue.textContent = `${gameState.balance} GA`;
    }
    
    // Generate cryptographically secure client seed
    function generateClientSeed() {
        const array = new Uint8Array(16);
        crypto.getRandomValues(array);
        gameState.clientSeedValue = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
        clientSeed.textContent = gameState.clientSeedValue;
    }
    
    // Generate server seed and its hash
    function generateServerSeed() {
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        gameState.serverSeedValue = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
        
        // Create hash of server seed for display
        gameState.serverSeedHashValue = CryptoJS.SHA256(gameState.serverSeedValue).toString();
        serverSeedHash.textContent = gameState.serverSeedHashValue.substring(0, 16) + '...';
    }
    
    // Update probability and payout displays
    function updateProbabilityDisplay() {
        const target = parseFloat(targetInput.value) || 1.00;
        
        // Validate target
        if (target < 1.00) {
            targetInput.value = '1.00';
            return;
        }
        if (target > 999.99) {
            targetInput.value = '999.99';
            return;
        }
        
        gameState.targetMultiplier = target;
        
        // Calculate probabilities (simplified for demo)
        const overProb = Math.max(5, Math.min(95, 100 / target));
        const underProb = 100 - overProb;
        
        // Calculate payouts
        const overPayoutMultiplier = target;
        const underPayoutMultiplier = Math.max(1.01, 100 / underProb);
        
        // Update displays
        overProbability.textContent = `${overProb.toFixed(2)}%`;
        underProbability.textContent = `${underProb.toFixed(2)}%`;
        overPayout.textContent = `${overPayoutMultiplier.toFixed(2)}× payout`;
        underPayout.textContent = `${underPayoutMultiplier.toFixed(2)}× payout`;
        
        // Update mobile displays
        mobileOverInfo.textContent = `${overProb.toFixed(0)}% • ${overPayoutMultiplier.toFixed(2)}×`;
        mobileUnderInfo.textContent = `${underProb.toFixed(0)}% • ${underPayoutMultiplier.toFixed(2)}×`;
        
        // Update target marker position
        const position = Math.min(90, Math.max(10, (Math.log10(target) / Math.log10(100)) * 80 + 10));
        targetMarker.style.left = `${position}%`;
        targetMarker.querySelector('span').textContent = `${target.toFixed(2)}×`;
    }
    
    // Update nonce display
    function updateNonceDisplay() {
        nonceElement.textContent = gameState.nonce;
    }
    
    // Generate random outcome
    function generateOutcome() {
        // Enhanced random generation for more balanced outcomes
        const combined = gameState.clientSeedValue + gameState.serverSeedValue + gameState.nonce;
        const hash = CryptoJS.SHA256(combined).toString();
        
        // Convert first 8 characters of hash to number
        const hashNum = parseInt(hash.substring(0, 8), 16);
        const normalized = hashNum / 0xffffffff;
        
        // Enhanced algorithm with multiple randomness sources
        let result;
        
        // Primary calculation
        const primary = 1 / (1 - normalized);
        
        // Add variability to prevent predictable patterns
        const secondary = parseFloat(hash.substring(8, 16), 16) / 0xffffffff;
        const tertiary = parseFloat(hash.substring(16, 24), 16) / 0xffffffff;
        
        // Combine multiple sources for enhanced randomness
        const variance = (secondary - 0.5) * 0.1 + (tertiary - 0.5) * 0.05;
        result = Math.max(1.00, primary + variance);
        
        // Apply logarithmic scaling for better distribution
        if (result > 10) {
            const logScale = Math.log10(result / 10) * 2 + 10;
            result = Math.min(999.99, logScale);
        }
        
        return Math.round(result * 100) / 100;
    }
    
    // Play game
    function playGame() {
        if (gameState.isPlaying) return;
        
        // Validate bet amount
        const betAmount = parseInt(betAmountInput.value);
        if (betAmount <= 0 || isNaN(betAmount)) {
            showGameStatus('Please enter a valid bet amount!', 'warning');
            return;
        }
        
        if (betAmount > gameState.balance) {
            showGameStatus('Insufficient balance!', 'warning');
            return;
        }
        
        // Validate target multiplier
        const target = parseFloat(targetInput.value);
        if (target < 1.00 || target > 999.99 || isNaN(target)) {
            showGameStatus('Invalid target multiplier!', 'warning');
            return;
        }
        
        gameState.isPlaying = true;
        gameState.betAmount = betAmount;
        gameState.targetMultiplier = target;
        
        // Deduct bet from balance
        gameState.balance -= betAmount;
        updateBalanceDisplay();
        savePlayerBalance();
        
        // Update statistics
        gameState.statistics.gamesPlayed++;
        gameState.statistics.totalWagered += betAmount;
        
        // Play bet sound
        sounds.bet.play();
        
        // Disable play button
        playBtn.disabled = true;
        mobilePlayBtn.disabled = true;
        playBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Playing...';
        mobilePlayBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Playing...';
        
        showGameStatus('Generating outcome...', '');
        
        // Generate outcome with delay for suspense
        setTimeout(() => {
            const outcome = generateOutcome();
            processGameResult(outcome);
        }, 1500);
    }
    
    // Process game result
    function processGameResult(outcome) {
        gameState.lastResult = outcome;
        
        // Update outcome display
        outcomeValue.querySelector('.outcome-text').textContent = `${outcome.toFixed(2)}×`;
        
        // Update result marker
        const position = Math.min(90, Math.max(10, (Math.log10(outcome) / Math.log10(100)) * 80 + 10));
        resultMarker.style.left = `${position}%`;
        resultMarker.querySelector('span').textContent = `${outcome.toFixed(2)}×`;
        resultMarker.classList.add('show');
        
        // Determine win/loss
        let isWin = false;
        let payout = 0;
        let winAmount = 0;
        
        if (gameState.selectedDirection === 'over') {
            isWin = outcome > gameState.targetMultiplier;
            if (isWin) {
                payout = gameState.targetMultiplier;
                winAmount = Math.floor(gameState.betAmount * payout);
            }
        } else {
            isWin = outcome < gameState.targetMultiplier;
            if (isWin) {
                const underProb = 100 - (100 / gameState.targetMultiplier);
                payout = Math.max(1.01, 100 / underProb);
                winAmount = Math.floor(gameState.betAmount * payout);
            }
        }
        
        // Update display based on result
        if (isWin) {
            outcomeValue.classList.add('win');
            outcomeValue.classList.remove('loss');
            outcomeStatus.textContent = `You won ${winAmount} GA!`;
            showGameStatus(`🎉 You won ${winAmount} GA!`, 'success');
            
            // Add winnings to balance
            gameState.balance += winAmount;
            
            // Update statistics
            gameState.statistics.gamesWon++;
            gameState.statistics.totalWinnings += winAmount;
            if (winAmount > gameState.statistics.biggestWin) {
                gameState.statistics.biggestWin = winAmount;
            }
            
            // Show win indicators
            showWinLossIndicator(true, winAmount - gameState.betAmount);
            
            // Update mobile last win
            mobileLastWin.textContent = `Last: +${winAmount - gameState.betAmount} GA`;
            
            // Play win sound
            sounds.win.play();
        } else {
            outcomeValue.classList.add('loss');
            outcomeValue.classList.remove('win');
            outcomeStatus.textContent = `You lost ${gameState.betAmount} GA`;
            showGameStatus(`💥 You lost ${gameState.betAmount} GA`, 'danger');
            
            // Show loss indicators
            showWinLossIndicator(false, gameState.betAmount);
            
            // Update mobile last win
            mobileLastWin.textContent = `Last: -${gameState.betAmount} GA`;
            
            // Play lose sound
            sounds.lose.play();
        }
        
        // Update statistics
        gameState.statistics.totalMultiplier += outcome;
        
        // Add to history
        addToGameHistory({
            id: gameState.gameHistory.length + 1,
            bet: gameState.betAmount,
            target: gameState.targetMultiplier,
            result: outcome,
            direction: gameState.selectedDirection,
            isWin: isWin,
            profit: isWin ? winAmount - gameState.betAmount : -gameState.betAmount,
            time: new Date()
        });
        
        // Update displays
        updateBalanceDisplay();
        updateStatistics();
        savePlayerBalance();
        
        // Enable verify button
        verifyBtn.disabled = false;
        
        // Increment nonce for next game
        gameState.nonce++;
        updateNonceDisplay();
        
        // Reset play button
        gameState.isPlaying = false;
        playBtn.disabled = false;
        mobilePlayBtn.disabled = false;
        playBtn.innerHTML = '<i class="fas fa-play"></i> PLAY';
        mobilePlayBtn.innerHTML = '<i class="fas fa-play"></i> PLAY';
        
        // Continue auto betting if active
        if (gameState.autoBetting) {
            setTimeout(() => {
                if (gameState.autoBetting && gameState.balance >= gameState.betAmount) {
                    playGame();
                } else {
                    stopAutoBetting();
                }
            }, 2000);
        }
    }
    
    // Show game status
    function showGameStatus(message, type = '') {
        gameStatus.textContent = message;
        gameStatus.className = `game-status ${type}`;
    }
    
    // Show win/loss indicator
    function showWinLossIndicator(isWin, amount) {
        // Main indicator
        const indicator = document.createElement('div');
        indicator.className = isWin ? 'win' : 'loss';
        indicator.textContent = isWin ? `+${amount} GA` : `-${amount} GA`;
        
        winLossIndicator.innerHTML = '';
        winLossIndicator.appendChild(indicator);
        
        // Header indicator
        const headerIndicator = document.createElement('div');
        headerIndicator.className = isWin ? 'header-win-indicator' : 'header-loss-indicator';
        headerIndicator.innerHTML = `
            <i class="${isWin ? 'fas fa-trophy' : 'fas fa-times-circle'}"></i>
            <span>${isWin ? '+' : '-'}${Math.abs(amount)} GA</span>
        `;
        
        headerWinLossIndicator.innerHTML = '';
        headerWinLossIndicator.appendChild(headerIndicator);
        
        // Remove after animation
        setTimeout(() => {
            indicator.remove();
            headerIndicator.remove();
        }, 3000);
    }
    
    // Toggle direction
    function toggleDirection(direction) {
        gameState.selectedDirection = direction;
        
        // Update button states
        overBtn.classList.toggle('active', direction === 'over');
        underBtn.classList.toggle('active', direction === 'under');
        mobileOverBtn.classList.toggle('active', direction === 'over');
        mobileUnderBtn.classList.toggle('active', direction === 'under');
        
        sounds.click.play();
    }
    
    // Start auto betting
    function startAutoBetting() {
        if (gameState.autoBetting) {
            stopAutoBetting();
            return;
        }
        
        gameState.autoBetting = true;
        autoBetBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
        mobileAutoBetBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
        
        sounds.click.play();
        
        // Start first auto bet
        if (gameState.balance >= gameState.betAmount && !gameState.isPlaying) {
            playGame();
        }
    }
    
    // Stop auto betting
    function stopAutoBetting() {
        gameState.autoBetting = false;
        autoBetBtn.innerHTML = '<i class="fas fa-magic"></i> Auto';
        mobileAutoBetBtn.innerHTML = '<i class="fas fa-magic"></i> Auto';
        
        if (gameState.autoBetInterval) {
            clearInterval(gameState.autoBetInterval);
            gameState.autoBetInterval = null;
        }
    }
    
    // Update statistics display
    function updateStatistics() {
        gamesPlayed.textContent = gameState.statistics.gamesPlayed;
        winRate.textContent = gameState.statistics.gamesPlayed > 0 ? 
            `${Math.round((gameState.statistics.gamesWon / gameState.statistics.gamesPlayed) * 100)}%` : '0%';
        totalWagered.textContent = `${gameState.statistics.totalWagered} GA`;
        
        const netProfitValue = gameState.statistics.totalWinnings - gameState.statistics.totalWagered;
        netProfit.textContent = `${netProfitValue >= 0 ? '+' : ''}${netProfitValue} GA`;
        netProfit.className = `stat-value ${netProfitValue >= 0 ? 'positive' : 'negative'}`;
        
        biggestWin.textContent = `${gameState.statistics.biggestWin} GA`;
        avgMultiplier.textContent = gameState.statistics.gamesPlayed > 0 ? 
            `${(gameState.statistics.totalMultiplier / gameState.statistics.gamesPlayed).toFixed(2)}×` : '0.00×';
    }
    
    // Add to game history
    function addToGameHistory(game) {
        gameState.gameHistory.unshift(game);
        if (gameState.gameHistory.length > 50) {
            gameState.gameHistory.pop();
        }
        
        updateHistoryTable();
    }
    
    // Update history table
    function updateHistoryTable() {
        historyTableBody.innerHTML = '';
        
        if (gameState.gameHistory.length === 0) {
            historyPlaceholder.style.display = 'block';
            return;
        }
        
        historyPlaceholder.style.display = 'none';
        
        gameState.gameHistory.slice(0, 20).forEach(game => {
            const row = document.createElement('tr');
            
            const timeString = formatTime(game.time);
            const resultClass = game.isWin ? 'win' : 'loss';
            const directionClass = game.direction;
            const profitText = game.profit >= 0 ? `+${game.profit}` : `${game.profit}`;
            
            row.innerHTML = `
                <td>#${game.id}</td>
                <td>${game.bet} GA</td>
                <td>${game.target.toFixed(2)}×</td>
                <td>${game.result.toFixed(2)}×</td>
                <td class="${directionClass}">${game.direction.toUpperCase()}</td>
                <td class="${resultClass}">${profitText} GA</td>
                <td>${timeString}</td>
            `;
            
            historyTableBody.appendChild(row);
        });
    }
    
    // Format time
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    }
    
    // Toggle view mode
    function toggleViewMode(mode) {
        if (mode === 'standard') {
            document.body.classList.remove('pro-view-active');
            standardViewBtn.classList.add('active');
            proViewBtn.classList.remove('active');
            gameState.viewMode = 'standard';
        } else {
            document.body.classList.add('pro-view-active');
            proViewBtn.classList.add('active');
            standardViewBtn.classList.remove('active');
            gameState.viewMode = 'pro';
        }
        
        sounds.click.play();
    }
    
    // Toggle sound
    function toggleSound() {
        gameState.soundEnabled = !gameState.soundEnabled;
        
        if (gameState.soundEnabled) {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        } else {
            soundToggleBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        }
        
        sounds.click.play();
    }
    
    // Load game history from localStorage
    function loadGameHistory() {
        const savedHistory = localStorage.getItem('limboGameHistory');
        if (savedHistory) {
            try {
                gameState.gameHistory = JSON.parse(savedHistory).map(game => ({
                    ...game,
                    time: new Date(game.time)
                }));
                updateHistoryTable();
            } catch (e) {
                console.error('Error loading game history:', e);
            }
        }
        
        const savedStats = localStorage.getItem('limboGameStats');
        if (savedStats) {
            try {
                gameState.statistics = {...gameState.statistics, ...JSON.parse(savedStats)};
            } catch (e) {
                console.error('Error loading game statistics:', e);
            }
        }
    }
    
    // Save game history to localStorage
    function saveGameHistory() {
        localStorage.setItem('limboGameHistory', JSON.stringify(gameState.gameHistory));
        localStorage.setItem('limboGameStats', JSON.stringify(gameState.statistics));
    }
    
    // EVENT LISTENERS
    
    // Play buttons
    playBtn.addEventListener('click', playGame);
    mobilePlayBtn.addEventListener('click', playGame);
    
    // Auto bet buttons
    autoBetBtn.addEventListener('click', startAutoBetting);
    mobileAutoBetBtn.addEventListener('click', startAutoBetting);
    
    // Direction buttons
    overBtn.addEventListener('click', () => toggleDirection('over'));
    underBtn.addEventListener('click', () => toggleDirection('under'));
    mobileOverBtn.addEventListener('click', () => toggleDirection('over'));
    mobileUnderBtn.addEventListener('click', () => toggleDirection('under'));
    
    // Bet adjustment buttons
    decreaseBetBtn.addEventListener('click', () => {
        const currentBet = parseInt(betAmountInput.value);
        if (currentBet >= 2) {
            betAmountInput.value = currentBet - 1;
            gameState.betAmount = currentBet - 1;
            sounds.click.play();
        }
    });
    
    increaseBetBtn.addEventListener('click', () => {
        const currentBet = parseInt(betAmountInput.value);
        if (currentBet < gameState.balance) {
            betAmountInput.value = currentBet + 1;
            gameState.betAmount = currentBet + 1;
            sounds.click.play();
        }
    });
    
    // Multiplier adjustment buttons
    decreaseMultiplierBtn.addEventListener('click', () => {
        const current = parseFloat(targetInput.value);
        if (current >= 1.01) {
            const newValue = Math.max(1.00, current - 0.01);
            targetInput.value = newValue.toFixed(2);
            updateProbabilityDisplay();
            sounds.click.play();
        }
    });
    
    increaseMultiplierBtn.addEventListener('click', () => {
        const current = parseFloat(targetInput.value);
        if (current < 999.99) {
            const newValue = Math.min(999.99, current + 0.01);
            targetInput.value = newValue.toFixed(2);
            updateProbabilityDisplay();
            sounds.click.play();
        }
    });
    
    // Input validation
    betAmountInput.addEventListener('change', () => {
        let value = parseInt(betAmountInput.value);
        
        if (value < 1 || isNaN(value)) {
            value = 1;
        }
        
        if (value > gameState.balance) {
            value = gameState.balance;
        }
        
        betAmountInput.value = value;
        gameState.betAmount = value;
    });
    
    targetInput.addEventListener('change', updateProbabilityDisplay);
    
    // Bet preset buttons
    betPresetBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const amount = btn.dataset.amount;
            
            if (amount === 'max') {
                betAmountInput.value = gameState.balance;
                gameState.betAmount = gameState.balance;
            } else {
                const presetAmount = parseInt(amount);
                
                if (presetAmount <= gameState.balance) {
                    betAmountInput.value = presetAmount;
                    gameState.betAmount = presetAmount;
                } else {
                    showGameStatus('Insufficient balance for this bet!', 'warning');
                    return;
                }
            }
            
            sounds.click.play();
        });
    });
    
    // Multiplier preset buttons
    multiplierPresetBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const multiplier = parseFloat(btn.dataset.multiplier);
            
            targetInput.value = multiplier.toFixed(2);
            updateProbabilityDisplay();
            
            // Update active button
            multiplierPresetBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            sounds.click.play();
        });
    });
    
    // View mode toggle
    standardViewBtn.addEventListener('click', () => toggleViewMode('standard'));
    proViewBtn.addEventListener('click', () => toggleViewMode('pro'));
    
    // Sound toggle
    soundToggleBtn.addEventListener('click', toggleSound);
    
    // Provably fair
    regenerateClientSeedBtn.addEventListener('click', () => {
        generateClientSeed();
        sounds.click.play();
    });
    
    verifyBtn.addEventListener('click', () => {
        if (gameState.lastResult !== null) {
            const combined = gameState.clientSeedValue + gameState.serverSeedValue + (gameState.nonce - 1);
            const hash = CryptoJS.SHA256(combined).toString();
            
            alert(`Verification:
            Client Seed: ${gameState.clientSeedValue}
            Server Seed: ${gameState.serverSeedValue}
            Nonce: ${gameState.nonce - 1}
            Hash: ${hash}
            Result: ${gameState.lastResult.toFixed(2)}×`);
        }
        sounds.click.play();
    });
    
    // Toggle sections
    statsToggle.addEventListener('click', () => {
        const content = document.querySelector('.stats-content');
        content.classList.toggle('expanded');
        statsToggle.innerHTML = content.classList.contains('expanded') ? 
            '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
        sounds.click.play();
    });
    
    fairToggle.addEventListener('click', () => {
        const content = document.querySelector('.fair-content');
        content.classList.toggle('expanded');
        fairToggle.innerHTML = content.classList.contains('expanded') ? 
            '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
        sounds.click.play();
    });
    
    historyToggle.addEventListener('click', () => {
        const content = document.querySelector('.history-content');
        content.classList.toggle('expanded');
        historyToggle.innerHTML = content.classList.contains('expanded') ? 
            '<i class="fas fa-chevron-up"></i>' : '<i class="fas fa-chevron-down"></i>';
        sounds.click.play();
    });
    
    // Handle sidebar toggle for proper layout
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    menuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('open');
        mainContent.classList.toggle('sidebar-open');
    });
    
    // Screen orientation change handling
    window.addEventListener('orientationchange', function() {
        setTimeout(() => {
            updateProbabilityDisplay();
        }, 300);
    });
    
    // Save data before page unload
    window.addEventListener('beforeunload', () => {
        saveGameHistory();
        savePlayerBalance();
    });
    
    // Setup sounds
    setupPlaceholderSounds();
    
    // Initialize game on load
    initGame();
});