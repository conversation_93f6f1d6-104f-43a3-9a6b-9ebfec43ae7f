// Enhanced Support Page - Mobile-First Responsive Implementation
document.addEventListener('DOMContentLoaded', function() {
    console.log("Enhanced Support Page Loading...");
    
    // Mobile detection and optimization
    const isMobile = window.innerWidth <= 767;
    const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1023;
    const isDesktop = window.innerWidth >= 1024;
    
    // Touch and haptic support
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const hasHaptic = 'vibrate' in navigator && isMobile;
    
    // Support state management
    const supportState = {
        activeFaqTab: 'general',
        chatbotOpen: false,
        chatHistory: JSON.parse(localStorage.getItem('supportChatHistory')) || [],
        supportTickets: JSON.parse(localStorage.getItem('supportTickets')) || [],
        userPreferences: JSON.parse(localStorage.getItem('supportPreferences')) || {
            preferredContact: 'chat',
            notifications: true,
            language: 'en'
        },
        searchQuery: '',
        isSearching: false,
        currentSession: {
            startTime: Date.now(),
            interactions: 0,
            resolvedQuestions: 0
        }
    };
    
    // Cache DOM elements
    const elements = {
        searchInput: document.getElementById('supportSearchInput'),
        searchButton: document.querySelector('.search-button'),
        quickLinks: document.querySelectorAll('.quick-link'),
        categoryCards: document.querySelectorAll('.category-card'),
        contactButtons: document.querySelectorAll('.contact-button'),
        faqTabs: document.querySelectorAll('.faq-tab'),
        faqContents: document.querySelectorAll('.faq-content'),
        faqItems: document.querySelectorAll('.faq-item'),
        faqQuestions: document.querySelectorAll('.faq-question'),
        chatbotToggle: document.getElementById('chatbotToggle'),
        chatbotContainer: document.getElementById('chatbotContainer'),
        chatbotClose: document.getElementById('chatbotClose'),
        chatbotMessages: document.getElementById('chatbotMessages'),
        chatbotInput: document.getElementById('chatbotInput'),
        chatbotSend: document.getElementById('chatbotSend'),
        liveChatButton: document.getElementById('liveChatButton'),
        chatbotLink: document.getElementById('chatbotLink'),
        communityCards: document.querySelectorAll('.community-card')
    };
    
    // FAQ Data
    const faqData = {
        general: [
            {
                question: "What is GoldenAura?",
                answer: "GoldenAura is a premium online gaming and sports betting platform offering a wide array of casino-style games and comprehensive sports betting options. Our platform features a user-friendly interface, secure payment methods, and excellent customer support.",
                keywords: ["goldenaura", "platform", "about", "casino", "sports betting"]
            },
            {
                question: "Is GoldenAura legal and safe to use?",
                answer: "Yes, GoldenAura operates with full compliance with applicable laws and regulations. We are licensed and regulated by the appropriate gambling authorities, ensuring a safe and fair gaming environment. We employ industry-standard security measures to protect our users' data and transactions.",
                keywords: ["legal", "safe", "license", "security", "regulation"]
            },
            {
                question: "What is GA currency?",
                answer: "GA (GoldenAura Currency) is our platform's virtual currency. The exchange rate is $1 USD = 10 GA. All transactions, wagers, and payouts on our platform are conducted in GA. You can easily convert your real money to GA and vice versa.",
                keywords: ["ga currency", "exchange rate", "virtual currency", "conversion"]
            },
            {
                question: "How do I contact customer support?",
                answer: "You can contact our customer support team through several channels:\n\n• Live Chat: Available 24/7 directly on our website\n• Email: <EMAIL>\n• Telegram: @GoldenAuraSupport\n• Community Forum: For peer-to-peer assistance",
                keywords: ["contact", "support", "help", "chat", "email", "telegram"]
            },
            {
                question: "What devices can I use to access GoldenAura?",
                answer: "GoldenAura is fully responsive and optimized for all devices. You can access our platform via:\n\n• Desktop computers (Windows, Mac, Linux)\n• Mobile phones (iOS, Android)\n• Tablets\n• Smart TVs with web browsers\n\nNo dedicated app installation is required as our platform runs smoothly in any modern web browser.",
                keywords: ["devices", "mobile", "desktop", "tablet", "browser", "responsive"]
            }
        ],
        account: [
            {
                question: "How do I create an account?",
                answer: "Creating an account is simple. Click the 'Register' button at the top right of the page and follow the step-by-step instructions. You'll need to provide your email, create a password, and complete basic personal information to comply with regulatory requirements. The process typically takes less than 2 minutes.",
                keywords: ["create account", "register", "signup", "registration"]
            },
            {
                question: "Why do I need to verify my account?",
                answer: "Account verification is a standard security measure in the online gambling industry. It helps us:\n\n• Prevent fraud and money laundering\n• Ensure players are of legal gambling age\n• Comply with regulatory requirements\n• Protect your account from unauthorized access\n\nThe verification process typically involves submitting copies of identification documents such as a passport, driver's license, or national ID card, along with proof of address.",
                keywords: ["verification", "verify", "identity", "documents", "kyc"]
            },
            {
                question: "How can I reset my password?",
                answer: "To reset your password:\n\n1. Click on 'Login' at the top of the page\n2. Select 'Forgot Password' below the login form\n3. Enter the email address associated with your account\n4. Check your email for a password reset link\n5. Follow the link to create a new password\n\nIf you don't receive the email, please check your spam folder or contact our support team for assistance.",
                keywords: ["password reset", "forgot password", "login issues", "email"]
            },
            {
                question: "How do I enable Two-Factor Authentication (2FA)?",
                answer: "Enabling 2FA adds an extra layer of security to your account. To set it up:\n\n1. Log into your account\n2. Go to Settings > Security\n3. Select 'Enable Two-Factor Authentication'\n4. Download an authenticator app (like Google Authenticator) if you don't already have one\n5. Scan the QR code provided or manually enter the key\n6. Enter the 6-digit code from your authenticator app to confirm\n\nOnce enabled, you'll need to enter a code from your authenticator app each time you log in.",
                keywords: ["2fa", "two factor", "authentication", "security", "google authenticator"]
            }
        ],
        payments: [
            {
                question: "What payment methods do you accept?",
                answer: "We accept a variety of payment methods to make deposits and withdrawals convenient for our users:\n\n• Credit/Debit Cards (Visa, MasterCard)\n• Bank Transfers\n• E-wallets (PayPal, Skrill, Neteller)\n• Cryptocurrency (Bitcoin, Ethereum, Litecoin)\n• Mobile Payment Solutions\n\nProcessing times and fees may vary depending on the method chosen.",
                keywords: ["payment methods", "deposit", "withdrawal", "credit card", "crypto", "bitcoin"]
            },
            {
                question: "How long do withdrawals take?",
                answer: "Withdrawal times depend on the method you choose:\n\n• E-wallets: 1-24 hours\n• Credit/Debit Cards: 1-5 business days\n• Bank Transfers: 3-7 business days\n• Cryptocurrency: 1-24 hours\n\nAll withdrawals are subject to our verification process. VIP and loyalty tier members enjoy faster processing times.",
                keywords: ["withdrawal time", "processing", "payout", "how long", "timeframe"]
            },
            {
                question: "Are there any fees for deposits or withdrawals?",
                answer: "We strive to keep fees minimal:\n\n• Deposits: Generally free, though some payment providers may charge their own fees\n• Withdrawals: First 3 withdrawals per month are free, after which a small processing fee may apply\n• Currency Conversion: Standard rates apply for non-USD currencies\n\nSpecific fees are displayed before you complete any transaction.",
                keywords: ["fees", "charges", "deposit fee", "withdrawal fee", "cost"]
            }
        ],
        games: [
            {
                question: "What games are available on GoldenAura?",
                answer: "GoldenAura offers a comprehensive selection of games:\n\n• Classic Casino Games: Blackjack, Roulette, Baccarat, Poker\n• Slot Games: Video slots, progressive jackpots\n• Specialty Games: Crash, Mines, Plinko, Limbo\n• Live Dealer Games: Real-time games with professional dealers\n• Table Games: Various poker variants, dice games\n\nAll games are provably fair and regularly audited for fairness.",
                keywords: ["games", "casino games", "slots", "blackjack", "roulette", "crash", "mines"]
            },
            {
                question: "How do provably fair games work?",
                answer: "Provably fair is a technology that ensures game outcomes are random and not manipulated. Here's how it works:\n\n1. A client seed (your input) and server seed (our input) are combined\n2. These seeds generate a cryptographic hash\n3. The hash determines the game outcome\n4. You can verify the fairness by checking the seeds and hash\n\nThis system provides mathematical proof that neither the player nor the house can predict or manipulate results.",
                keywords: ["provably fair", "fairness", "random", "hash", "verification"]
            },
            {
                question: "Can I play games for free?",
                answer: "Yes! Most of our games offer a demo mode where you can play with virtual credits without risking real money. This is perfect for:\n\n• Learning game rules and strategies\n• Testing new games\n• Practicing before playing with real money\n• Entertainment without financial commitment\n\nSimply select 'Demo Mode' when launching any supported game.",
                keywords: ["demo mode", "free play", "practice", "virtual credits", "no money"]
            }
        ],
        sports: [
            {
                question: "What sports can I bet on?",
                answer: "GoldenAura offers comprehensive sports betting coverage:\n\n• Football (Soccer): Premier League, Champions League, World Cup\n• American Sports: NFL, NBA, MLB, NHL\n• Tennis: Grand Slams, ATP, WTA tournaments\n• Basketball: NBA, EuroLeague, international competitions\n• Other Sports: Cricket, Rugby, Golf, MMA, Boxing\n• Esports: CS:GO, Dota 2, League of Legends\n\nWe cover thousands of events annually across these sports.",
                keywords: ["sports betting", "football", "soccer", "tennis", "basketball", "esports"]
            },
            {
                question: "How do betting odds work?",
                answer: "Betting odds represent the likelihood of an outcome and determine your potential winnings:\n\n• Decimal Odds (e.g., 2.50): Multiply your stake to get total return\n• Fractional Odds (e.g., 3/2): Traditional format showing profit to stake ratio\n• American Odds (e.g., +150): Positive shows profit on $100 bet, negative shows stake needed to win $100\n\nHigher odds mean lower probability but higher potential winnings.",
                keywords: ["odds", "betting odds", "decimal", "fractional", "american", "probability"]
            },
            {
                question: "What is live betting?",
                answer: "Live betting allows you to place bets on games that are already in progress. Features include:\n\n• Real-time odds that change based on game events\n• Multiple betting markets available during play\n• Live statistics and match updates\n• Cash-out options to secure winnings early\n• Enhanced viewing experience with live scores\n\nLive betting is available for most major sporting events.",
                keywords: ["live betting", "in-play", "real-time", "cash out", "live odds"]
            }
        ],
        bonuses: [
            {
                question: "What bonuses are available?",
                answer: "GoldenAura offers various bonuses and promotions:\n\n• Welcome Bonus: 100% match up to GA 10,000 on first deposit\n• Reload Bonuses: Regular deposit bonuses for existing players\n• Cashback: Weekly cashback on net losses\n• Tournaments: Regular competitions with prize pools\n• Loyalty Program: Tiered rewards based on your activity\n• Referral Bonus: Rewards for inviting friends\n\nCheck our Promotions page for current offers and terms.",
                keywords: ["bonuses", "welcome bonus", "promotions", "cashback", "loyalty", "referral"]
            },
            {
                question: "What are wagering requirements?",
                answer: "Wagering requirements specify how many times you must bet bonus money before you can withdraw winnings:\n\n• Example: GA 100 bonus with 20x wagering = GA 2,000 total bets required\n• Different games contribute differently to requirements\n• Slots usually contribute 100%\n• Table games may contribute 10-20%\n• Time limits apply to complete requirements\n\nAlways read bonus terms before claiming.",
                keywords: ["wagering requirements", "playthrough", "bonus terms", "rollover"]
            }
        ]
    };
    
    // Chatbot responses
    const chatbotResponses = {
        greetings: [
            "Hello! I'm your GoldenAura virtual assistant. How can I help you today?",
            "Hi there! Welcome to GoldenAura support. What can I assist you with?",
            "Greetings! I'm here to help with any questions about GoldenAura. What do you need?"
        ],
        fallback: [
            "I'm not sure I understand that question. Could you try rephrasing it?",
            "That's a great question! For complex issues, I'd recommend contacting our live support team.",
            "I don't have information about that specific topic. Would you like me to connect you with a human agent?",
            "I'm still learning! For the best answer, please contact our support team via live chat or email."
        ],
        keywords: {
            "account": "For account-related questions, you can find detailed information in our Account FAQ section. Need help with account creation, verification, or password reset?",
            "payment": "For payment and withdrawal information, check our Payments FAQ. We support various methods including cards, e-wallets, and crypto.",
            "bonus": "Looking for information about bonuses? Visit our Bonuses FAQ or check out the latest promotions on our Promotions page.",
            "game": "Need help with games? Our Games FAQ covers rules, strategies, and provably fair explanations for all our casino games.",
            "sport": "For sports betting questions, check our Sports FAQ covering odds, live betting, and available sports markets.",
            "withdrawal": "Withdrawal times vary by method: E-wallets (1-24h), Cards (1-5 days), Bank transfers (3-7 days), Crypto (1-24h).",
            "deposit": "Deposits are usually instant. We accept cards, e-wallets, bank transfers, and cryptocurrency.",
            "verification": "Account verification helps secure your account and comply with regulations. Upload your ID and proof of address in Settings.",
            "2fa": "Two-factor authentication adds security to your account. Enable it in Settings > Security using an authenticator app.",
            "support": "You can contact support via live chat (24/7), email (<EMAIL>), or Telegram (@GoldenAuraSupport)."
        }
    };
    
    // Initialize support functionality
    init();
    
    function init() {
        console.log("Initializing Enhanced Support...");
        
        // Setup event listeners
        setupEventListeners();
        
        // Initialize mobile optimizations
        setupMobileOptimizations();
        
        // Setup FAQ functionality
        setupFAQSystem();
        
        // Initialize chatbot
        setupChatbot();
        
        // Setup search functionality
        setupSearchSystem();
        
        // Load chat history
        loadChatHistory();
        
        // Set active nav item
        setActiveNavItem();
        
        // Setup intersection observers
        setupIntersectionObservers();
        
        console.log("Support page initialized successfully!");
    }
    
    function setupEventListeners() {
        // Search functionality
        if (elements.searchInput) {
            elements.searchInput.addEventListener('input', debounce(handleSearch, 300));
            elements.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    handleSearchSubmit();
                }
            });
        }
        
        if (elements.searchButton) {
            elements.searchButton.addEventListener('click', handleSearchSubmit);
        }
        
        // Quick links
        elements.quickLinks.forEach(link => {
            link.addEventListener('click', () => handleQuickLinkClick(link));
        });
        
        // Category cards
        elements.categoryCards.forEach(card => {
            card.addEventListener('click', () => handleCategoryClick(card));
        });
        
        // Contact buttons
        elements.contactButtons.forEach(button => {
            button.addEventListener('click', () => handleContactButtonClick(button));
        });
        
        // FAQ tabs
        elements.faqTabs.forEach(tab => {
            tab.addEventListener('click', () => handleFaqTabChange(tab));
        });
        
        // FAQ questions
        elements.faqQuestions.forEach(question => {
            question.addEventListener('click', () => handleFaqToggle(question));
        });
        
        // Chatbot controls
        if (elements.chatbotToggle) {
            elements.chatbotToggle.addEventListener('click', toggleChatbot);
        }
        
        if (elements.chatbotClose) {
            elements.chatbotClose.addEventListener('click', closeChatbot);
        }
        
        if (elements.chatbotSend) {
            elements.chatbotSend.addEventListener('click', sendChatMessage);
        }
        
        if (elements.chatbotInput) {
            elements.chatbotInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    sendChatMessage();
                }
            });
        }
        
        // Live chat and chatbot links
        if (elements.liveChatButton) {
            elements.liveChatButton.addEventListener('click', () => {
                toggleChatbot();
                showToast('Live chat opened! Ask me anything.', 'info');
            });
        }
        
        if (elements.chatbotLink) {
            elements.chatbotLink.addEventListener('click', () => {
                toggleChatbot();
                showToast('AI assistant ready to help!', 'info');
            });
        }
        
        // Community cards
        elements.communityCards.forEach(card => {
            card.addEventListener('click', () => handleCommunityClick(card));
        });
        
        // Window resize
        window.addEventListener('resize', debounce(handleResize, 250));
    }
    
    function setupMobileOptimizations() {
        // Add device classes
        document.body.classList.toggle('mobile', isMobile);
        document.body.classList.toggle('tablet', isTablet);
        document.body.classList.toggle('desktop', isDesktop);
        document.body.classList.toggle('touch-device', hasTouch);
        
        // Mobile-specific touch interactions
        if (isMobile) {
            setupMobileTouchInteractions();
            optimizeChatbotForMobile();
        }
    }
    
    function setupMobileTouchInteractions() {
        // Enhanced touch feedback for interactive elements
        const touchElements = document.querySelectorAll(`
            .quick-link, .category-card, .contact-button, .faq-question,
            .faq-tab, .chatbot-button, .community-card
        `);
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                element.style.transform = 'scale(0.98)';
                element.style.transition = 'transform 0.1s ease';
                triggerHapticFeedback('light');
            }, { passive: true });
            
            element.addEventListener('touchend', (e) => {
                setTimeout(() => {
                    element.style.transform = '';
                    element.style.transition = 'transform 0.3s ease';
                }, 100);
            }, { passive: true });
        });
    }
    
    function setupFAQSystem() {
        // Load FAQ data
        loadFAQContent();
        
        // Set initial active tab
        showFAQTab(supportState.activeFaqTab);
    }
    
    function loadFAQContent() {
        Object.keys(faqData).forEach(tabKey => {
            const content = document.getElementById(`${tabKey}-faqs`);
            if (content) {
                const items = faqData[tabKey];
                content.innerHTML = `
                    <ul class="faq-items">
                        ${items.map((item, index) => `
                            <li class="faq-item" data-keywords="${item.keywords.join(',')}">
                                <div class="faq-question">${item.question}</div>
                                <div class="faq-answer">
                                    <p>${item.answer.replace(/\n/g, '<br>')}</p>
                                </div>
                            </li>
                        `).join('')}
                    </ul>
                `;
                
                // Add event listeners to new FAQ items
                const newQuestions = content.querySelectorAll('.faq-question');
                newQuestions.forEach(question => {
                    question.addEventListener('click', () => handleFaqToggle(question));
                });
            }
        });
    }
    
    function setupChatbot() {
        // Initialize chatbot with welcome message
        if (supportState.chatHistory.length === 0) {
            addChatMessage('bot', getRandomResponse(chatbotResponses.greetings));
        }
    }
    
    function setupSearchSystem() {
        // Prepare search index
        buildSearchIndex();
    }
    
    function buildSearchIndex() {
        // Build search index from FAQ data
        supportState.searchIndex = [];
        
        Object.keys(faqData).forEach(category => {
            faqData[category].forEach(item => {
                supportState.searchIndex.push({
                    type: 'faq',
                    category,
                    question: item.question,
                    answer: item.answer,
                    keywords: item.keywords
                });
            });
        });
    }
    
    function setupIntersectionObservers() {
        const observerOptions = {
            root: null,
            rootMargin: '10px',
            threshold: 0.1
        };
        
        const fadeInObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-visible');
                    fadeInObserver.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        // Observe sections for fade-in animation
        const sections = document.querySelectorAll('.support-categories, .contact-methods, .faq-section, .community-section');
        sections.forEach(section => {
            section.classList.add('fade-in-element');
            fadeInObserver.observe(section);
        });
    }
    
    function handleSearch(e) {
        const query = e.target.value.toLowerCase().trim();
        supportState.searchQuery = query;
        
        if (query.length < 2) {
            clearSearchResults();
            return;
        }
        
        supportState.isSearching = true;
        performSearch(query);
    }
    
    function handleSearchSubmit() {
        if (supportState.searchQuery.trim()) {
            performSearch(supportState.searchQuery);
            showToast(`Searching for: "${supportState.searchQuery}"`, 'info');
        }
    }
    
    function performSearch(query) {
        const results = supportState.searchIndex.filter(item => {
            return item.question.toLowerCase().includes(query) ||
                   item.answer.toLowerCase().includes(query) ||
                   item.keywords.some(keyword => keyword.toLowerCase().includes(query));
        });
        
        displaySearchResults(results, query);
    }
    
    function displaySearchResults(results, query) {
        // Highlight relevant FAQ items
        const allFaqItems = document.querySelectorAll('.faq-item');
        allFaqItems.forEach(item => {
            item.classList.remove('search-highlight');
        });
        
        if (results.length > 0) {
            results.forEach(result => {
                if (result.type === 'faq') {
                    // Switch to relevant tab
                    showFAQTab(result.category);
                    
                    // Highlight matching items
                    const faqItems = document.querySelectorAll(`#${result.category}-faqs .faq-item`);
                    faqItems.forEach(item => {
                        if (item.querySelector('.faq-question').textContent === result.question) {
                            item.classList.add('search-highlight');
                            
                            // Scroll to the item
                            setTimeout(() => {
                                item.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }, 300);
                        }
                    });
                }
            });
            
            showToast(`Found ${results.length} result(s) for "${query}"`, 'success');
        } else {
            showToast(`No results found for "${query}". Try rephrasing your question.`, 'warning');
        }
        
        supportState.isSearching = false;
    }
    
    function clearSearchResults() {
        const allFaqItems = document.querySelectorAll('.faq-item');
        allFaqItems.forEach(item => {
            item.classList.remove('search-highlight');
        });
    }
    
    function handleQuickLinkClick(link) {
        const target = link.dataset.target;
        
        if (target) {
            const targetElement = document.getElementById(target);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
                
                // Special handling for FAQ section
                if (target === 'faq-section') {
                    showFAQTab('general');
                }
            }
        }
        
        triggerHapticFeedback('medium');
        supportState.currentSession.interactions++;
    }
    
    function handleCategoryClick(card) {
        const categoryTitle = card.querySelector('.category-title').textContent.toLowerCase();
        
        // Map category titles to FAQ tabs
        const categoryMap = {
            'getting started': 'account',
            'payments & withdrawals': 'payments',
            'casino games': 'games',
            'sports betting': 'sports',
            'account security': 'account',
            'bonuses & promotions': 'bonuses'
        };
        
        const faqTab = categoryMap[categoryTitle] || 'general';
        
        // Scroll to FAQ section and show relevant tab
        const faqSection = document.getElementById('faq-section');
        if (faqSection) {
            faqSection.scrollIntoView({ behavior: 'smooth' });
            setTimeout(() => {
                showFAQTab(faqTab);
            }, 500);
        }
        
        triggerHapticFeedback('medium');
        showToast(`Showing ${categoryTitle} help articles`, 'info');
    }
    
    function handleContactButtonClick(button) {
        const buttonText = button.textContent.toLowerCase();
        
        if (buttonText.includes('chat')) {
            toggleChatbot();
        } else if (buttonText.includes('email')) {
            window.location.href = 'mailto:<EMAIL>?subject=Support Request&body=Hello GoldenAura Support Team,%0D%0A%0D%0AI need help with:%0D%0A%0D%0A';
        } else if (buttonText.includes('telegram')) {
            window.open('https://t.me/GoldenAuraSupport', '_blank');
        } else if (buttonText.includes('community')) {
            scrollToCommunity();
        }
        
        triggerHapticFeedback('medium');
        supportState.currentSession.interactions++;
    }
    
    function handleFaqTabChange(tab) {
        const tabName = tab.dataset.tab;
        showFAQTab(tabName);
        triggerHapticFeedback('light');
    }
    
    function showFAQTab(tabName) {
        // Update active tab
        elements.faqTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });
        
        // Update active content
        elements.faqContents.forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}-faqs`);
        });
        
        supportState.activeFaqTab = tabName;
    }
    
    function handleFaqToggle(question) {
        const faqItem = question.closest('.faq-item');
        const isActive = faqItem.classList.contains('active');
        
        // Close all other FAQ items in the same section
        const section = question.closest('.faq-content');
        const otherItems = section.querySelectorAll('.faq-item.active');
        otherItems.forEach(item => {
            if (item !== faqItem) {
                item.classList.remove('active');
            }
        });
        
        // Toggle current item
        faqItem.classList.toggle('active', !isActive);
        
        if (!isActive) {
            supportState.currentSession.resolvedQuestions++;
        }
        
        triggerHapticFeedback('medium');
    }
    
    function handleCommunityClick(card) {
        const cardTitle = card.querySelector('.community-card-title').textContent;
        showToast(`Opening ${cardTitle} community section`, 'info');
        
        // In a real implementation, this would navigate to the community forum
        console.log('Opening community section:', cardTitle);
        
        triggerHapticFeedback('medium');
    }
    
    function toggleChatbot() {
        supportState.chatbotOpen = !supportState.chatbotOpen;
        
        if (supportState.chatbotOpen) {
            openChatbot();
        } else {
            closeChatbot();
        }
    }
    
    function openChatbot() {
        if (elements.chatbotContainer) {
            elements.chatbotContainer.classList.add('active');
            supportState.chatbotOpen = true;
            
            // Focus input
            if (elements.chatbotInput) {
                setTimeout(() => {
                    elements.chatbotInput.focus();
                }, 300);
            }
            
            triggerHapticFeedback('medium');
        }
    }
    
    function closeChatbot() {
        if (elements.chatbotContainer) {
            elements.chatbotContainer.classList.remove('active');
            supportState.chatbotOpen = false;
            triggerHapticFeedback('light');
        }
    }
    
    function sendChatMessage() {
        const input = elements.chatbotInput;
        if (!input || !input.value.trim()) return;
        
        const userMessage = input.value.trim();
        input.value = '';
        
        // Add user message
        addChatMessage('user', userMessage);
        
        // Generate bot response
        setTimeout(() => {
            const botResponse = generateBotResponse(userMessage);
            addChatMessage('bot', botResponse);
            
            // Save chat history
            saveChatHistory();
        }, 500 + Math.random() * 1000); // Simulate typing delay
        
        supportState.currentSession.interactions++;
        triggerHapticFeedback('light');
    }
    
    function addChatMessage(type, message) {
        if (!elements.chatbotMessages) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `chatbot-message ${type}`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = type === 'bot' ? 'GA' : 'YOU';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        content.textContent = message;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        elements.chatbotMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        elements.chatbotMessages.scrollTop = elements.chatbotMessages.scrollHeight;
        
        // Save to history
        supportState.chatHistory.push({
            type,
            message,
            timestamp: Date.now()
        });
        
        // Limit chat history length
        if (supportState.chatHistory.length > 100) {
            supportState.chatHistory = supportState.chatHistory.slice(-50);
        }
    }
    
    function generateBotResponse(userMessage) {
        const message = userMessage.toLowerCase();
        
        // Check for greetings
        if (message.includes('hello') || message.includes('hi') || message.includes('hey')) {
            return getRandomResponse(chatbotResponses.greetings);
        }
        
        // Check for keywords
        for (const [keyword, response] of Object.entries(chatbotResponses.keywords)) {
            if (message.includes(keyword)) {
                return response;
            }
        }
        
        // Search FAQ for relevant answer
        const faqMatch = findFAQMatch(message);
        if (faqMatch) {
            return `I found this in our FAQ: ${faqMatch.answer.substring(0, 200)}... Would you like me to show you the complete answer?`;
        }
        
        // Fallback response
        return getRandomResponse(chatbotResponses.fallback);
    }
    
    function findFAQMatch(query) {
        const matches = supportState.searchIndex.filter(item => {
            return item.question.toLowerCase().includes(query) ||
                   item.keywords.some(keyword => query.includes(keyword.toLowerCase()));
        });
        
        return matches.length > 0 ? matches[0] : null;
    }
    
    function getRandomResponse(responses) {
        return responses[Math.floor(Math.random() * responses.length)];
    }
    
    function loadChatHistory() {
        if (supportState.chatHistory.length > 0 && elements.chatbotMessages) {
            elements.chatbotMessages.innerHTML = '';
            
            supportState.chatHistory.forEach(chat => {
                addChatMessage(chat.type, chat.message);
            });
        }
    }
    
    function saveChatHistory() {
        localStorage.setItem('supportChatHistory', JSON.stringify(supportState.chatHistory));
    }
    
    function optimizeChatbotForMobile() {
        if (elements.chatbotContainer) {
            // Adjust chatbot size for mobile
            elements.chatbotContainer.style.width = 'calc(100vw - 40px)';
            elements.chatbotContainer.style.height = '70vh';
            elements.chatbotContainer.style.bottom = '10px';
            elements.chatbotContainer.style.right = '10px';
            elements.chatbotContainer.style.left = '10px';
        }
    }
    
    function scrollToCommunity() {
        const communitySection = document.getElementById('community-section');
        if (communitySection) {
            communitySection.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    function setActiveNavItem() {
        // Set support nav item as active
        const supportNavItem = document.querySelector('.nav-item[href="support.html"]');
        if (supportNavItem) {
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            supportNavItem.classList.add('active');
        }
    }
    
    function handleResize() {
        const newIsMobile = window.innerWidth <= 767;
        
        // Update device classes
        document.body.classList.toggle('mobile', newIsMobile);
        document.body.classList.toggle('tablet', window.innerWidth >= 768 && window.innerWidth <= 1023);
        document.body.classList.toggle('desktop', window.innerWidth >= 1024);
        
        // Re-optimize chatbot for mobile if needed
        if (newIsMobile && !isMobile) {
            optimizeChatbotForMobile();
        } else if (!newIsMobile && isMobile) {
            // Reset chatbot styles for desktop
            if (elements.chatbotContainer) {
                elements.chatbotContainer.style.width = '';
                elements.chatbotContainer.style.height = '';
                elements.chatbotContainer.style.left = '';
            }
        }
    }
    
    function triggerHapticFeedback(type = 'light') {
        if (!hasHaptic || !isMobile) return;
        
        const patterns = {
            light: [10],
            medium: [20],
            heavy: [30],
            success: [10, 50, 10],
            error: [50, 50, 50]
        };
        
        try {
            navigator.vibrate(patterns[type] || patterns.light);
        } catch (e) {
            console.log('Haptic feedback not supported');
        }
    }
    
    function showToast(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `support-toast ${type}`;
        toast.textContent = message;
        
        // Add to DOM
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }
    
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Add CSS for toast notifications and search highlighting
    const supportStyle = document.createElement('style');
    supportStyle.textContent = `
        .support-toast {
            position: fixed;
            top: calc(var(--mobile-header-height) + 20px);
            right: 20px;
            background: var(--support-primary);
            color: white;
            padding: 12px 16px;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            max-width: 300px;
        }
        
        .support-toast.show {
            transform: translateX(0);
        }
        
        .support-toast.error {
            background: var(--support-danger);
        }
        
        .support-toast.warning {
            background: var(--support-warning);
        }
        
        .support-toast.info {
            background: var(--support-info);
        }
        
        .support-toast.success {
            background: var(--support-success);
        }
        
        .search-highlight {
            border-color: var(--support-primary) !important;
            background: rgba(33, 150, 243, 0.1) !important;
            animation: searchHighlight 1s ease-out;
        }
        
        .fade-in-element {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        
        .fade-in-visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        @keyframes searchHighlight {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 767px) {
            .support-toast {
                right: 16px;
                left: 16px;
                max-width: none;
            }
        }
    `;
    document.head.appendChild(supportStyle);
    
    // Public API
    window.SupportPage = {
        openChat: () => openChatbot(),
        closeChat: () => closeChatbot(),
        searchFAQ: (query) => performSearch(query),
        showFAQSection: (tab) => showFAQTab(tab),
        addChatMessage: (type, message) => addChatMessage(type, message),
        triggerHapticFeedback,
        state: () => ({ ...supportState })
    };
    
    console.log("Enhanced Support Page Ready! 🎧");
});