/**
 * BALLOON PUMP Game
 * A gambling game where players pump a balloon for increasing multipliers at increasing risk
 */

// Game State
const gameState = {
    // Player stats
    balance: 0,
    currentBet: 0,
    currentMultiplier: 1.0,
    
    // Game mechanics
    pumpCount: 0,
    maxPumps: 8, // Hidden limit
    isPlaying: false,
    burstChance: 0,
    specialItemActive: false,
    hasMandatoryRound: false,
    
    // Tracking
    consecutiveWins: 0,
    lossStreak: 0,
    
    // Statistics
    totalGames: 0,
    wins: 0,
    losses: 0,
    totalWagered: 0,
    totalWon: 0,
    biggestWin: 0,
    biggestLoss: 0,
    netProfit: 0,
    zoneStats: {
        green: { attempts: 0, bursts: 0, cashouts: 0, totalMultiplier: 0 },
        yellow: { attempts: 0, bursts: 0, cashouts: 0, totalMultiplier: 0 },
        red: { attempts: 0, bursts: 0, cashouts: 0, totalMultiplier: 0 }
    },
    pumpStats: [],
    history: [],
    
    // View mode
    viewMode: 'standard' // 'standard' or 'pro'
};

// Charts
let performanceChart = null;
let burstHeatmap = {};

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Load saved data
    loadGameData();
    
    // Set up DOM elements
    setupDOMElements();
    
    // Set up event listeners
    setupEventListeners();
    
    // Initialize game UI
    initializeGame();
    
    // Initialize Pro View charts if in Pro View mode
    if (gameState.viewMode === 'pro') {
        initializeCharts();
    }
    
    // Initial UI update
    updateAllDisplays();
});

// Set up DOM elements
function setupDOMElements() {
    // Game elements
    window.balloon = document.getElementById('balloon');
    window.balloonFace = document.getElementById('balloonFace');
    window.progressBar = document.getElementById('progressBar');
    window.gameBackground = document.getElementById('gameBackground');
    window.ghostBalloons = document.getElementById('ghostBalloons');
    window.congratsBanner = document.getElementById('congratsBanner');
    window.explosionContainer = document.getElementById('explosionContainer');
    
    // Control buttons
    window.pumpBtn = document.getElementById('pumpBtn');
    window.mobilePumpBtn = document.getElementById('mobilePumpBtn');
    window.cashoutBtn = document.getElementById('cashoutBtn');
    window.mobileCashoutBtn = document.getElementById('mobileCashoutBtn');
    window.placeBetBtn = document.getElementById('placeBetBtn');
    window.tryAgainBtn = document.getElementById('tryAgainBtn');
    
    // Bet controls
    window.betInput = document.getElementById('betInput');
    window.decreaseBetBtn = document.getElementById('decreaseBet');
    window.increaseBetBtn = document.getElementById('increaseBet');
    window.minBetBtn = document.getElementById('minBetBtn');
    window.halfBetBtn = document.getElementById('halfBetBtn');
    window.doubleBetBtn = document.getElementById('doubleBetBtn');
    window.maxBetBtn = document.getElementById('maxBetBtn');
    
    // Stats display
    window.balanceValue = document.getElementById('balanceValue');
    window.betValue = document.getElementById('betValue');
    window.multiplierValue = document.getElementById('multiplierValue');
    window.potentialWinValue = document.getElementById('potentialWinValue');
    window.mobileBalance = document.getElementById('mobileBalance');
    window.mobileMultiplier = document.getElementById('mobileMultiplier');
    window.gameFeedback = document.getElementById('gameFeedback');
    
    // Game stats
    window.totalGamesValue = document.getElementById('totalGamesValue');
    window.winRateValue = document.getElementById('winRateValue');
    window.biggestWinValue = document.getElementById('biggestWinValue');
    window.biggestLossValue = document.getElementById('biggestLossValue');
    window.avgMultiplierValue = document.getElementById('avgMultiplierValue');
    window.netProfitValue = document.getElementById('netProfitValue');
    
    // Pro View stats
    window.totalWagered = document.getElementById('totalWagered');
    window.totalCashouts = document.getElementById('totalCashouts');
    window.totalBursts = document.getElementById('totalBursts');
    window.avgBet = document.getElementById('avgBet');
    window.roiValue = document.getElementById('roiValue');
    window.pumpEfficiency = document.getElementById('pumpEfficiency');
    
    // Risk zone stats
    window.greenBurstRate = document.getElementById('greenBurstRate');
    window.greenAvgCashout = document.getElementById('greenAvgCashout');
    window.yellowBurstRate = document.getElementById('yellowBurstRate');
    window.yellowAvgCashout = document.getElementById('yellowAvgCashout');
    window.redBurstRate = document.getElementById('redBurstRate');
    window.redAvgCashout = document.getElementById('redAvgCashout');
    
    // Optimal strategy
    window.strategyText = document.getElementById('strategyText');
    window.conservativeRec = document.getElementById('conservativeRec');
    window.balancedRec = document.getElementById('balancedRec');
    window.aggressiveRec = document.getElementById('aggressiveRec');
    
    // History list
    window.historyList = document.getElementById('historyList');
    
    // Mobile elements
    window.mobileStatsPanel = document.getElementById('mobileStatsPanel');
    window.mobileTotalGames = document.getElementById('mobileTotalGames');
    window.mobileWinRate = document.getElementById('mobileWinRate');
    window.mobileBestWin = document.getElementById('mobileBestWin');
    window.mobileNetProfit = document.getElementById('mobileNetProfit');
    window.mobileHistoryList = document.getElementById('mobileHistoryList');
    window.mobileCashoutPoint = document.getElementById('mobileCashoutPoint');
    window.mobileRedZoneRate = document.getElementById('mobileRedZoneRate');
    
    // Overlay elements
    window.burstOverlay = document.getElementById('burstOverlay');
    window.popupOverlay = document.getElementById('popupOverlay');
    window.specialItemPopup = document.getElementById('specialItemPopup');
    window.acceptSpecialBtn = document.getElementById('acceptSpecialBtn');
    window.rejectSpecialBtn = document.getElementById('rejectSpecialBtn');
    window.popupContent = document.getElementById('popupContent');
    
    // Toggle buttons
    window.historyToggle = document.getElementById('historyToggle');
    window.riskToggle = document.getElementById('riskToggle');
    window.historyContent = document.getElementById('historyContent');
    window.riskContent = document.getElementById('riskContent');
    
    // View mode toggle
    window.standardViewBtn = document.getElementById('standardView');
    window.proViewBtn = document.getElementById('proView');
    
    // Heatmap
    window.burstHeatmapEl = document.getElementById('burstHeatmap');
    
    // Header win/loss indicator
    window.headerWinLossIndicator = document.getElementById('headerWinLossIndicator');
}

// Set up event listeners
function setupEventListeners() {
    // Game control buttons
    pumpBtn.addEventListener('click', pumpBalloon);
    mobilePumpBtn.addEventListener('click', pumpBalloon);
    cashoutBtn.addEventListener('click', cashout);
    mobileCashoutBtn.addEventListener('click', cashout);
    placeBetBtn.addEventListener('click', placeBet);
    tryAgainBtn.addEventListener('click', resetGame);
    
    // Bet controls
    decreaseBetBtn.addEventListener('click', () => adjustBet(-10));
    increaseBetBtn.addEventListener('click', () => adjustBet(10));
    betInput.addEventListener('change', validateBetInput);
    minBetBtn.addEventListener('click', () => setBetAmount(10));
    halfBetBtn.addEventListener('click', () => setBetAmount(Math.floor(gameState.balance / 2)));
    doubleBetBtn.addEventListener('click', () => setBetAmount(Math.min(gameState.balance, gameState.currentBet * 2 || 20)));
    maxBetBtn.addEventListener('click', () => setBetAmount(gameState.balance));
    
    // Special item popup
    acceptSpecialBtn.addEventListener('click', () => handleSpecialItem(true));
    rejectSpecialBtn.addEventListener('click', () => handleSpecialItem(false));
    
    // Mobile stats panel
    document.getElementById('mobileStatsToggle').addEventListener('click', toggleMobileStatsPanel);
    document.getElementById('closeStatsPanel').addEventListener('click', closeMobileStatsPanel);
    
    // View mode toggle
    standardViewBtn.addEventListener('click', () => setViewMode('standard'));
    proViewBtn.addEventListener('click', () => setViewMode('pro'));
    
    // Section toggles
    historyToggle.addEventListener('click', () => toggleSection(historyContent));
    riskToggle.addEventListener('click', () => toggleSection(riskContent));
    
    // Window resize handler
    window.addEventListener('resize', handleResize);
}

// Load saved game data from localStorage
function loadGameData() {
    // Load balance
    const savedBalance = localStorage.getItem('pumpGameBalance');
    gameState.balance = savedBalance ? parseInt(savedBalance) : 1000;
    
    // Load game stats
    const savedStats = localStorage.getItem('pumpGameStats');
    if (savedStats) {
        const stats = JSON.parse(savedStats);
        gameState.totalGames = stats.totalGames || 0;
        gameState.wins = stats.wins || 0;
        gameState.losses = stats.losses || 0;
        gameState.totalWagered = stats.totalWagered || 0;
        gameState.totalWon = stats.totalWon || 0;
        gameState.biggestWin = stats.biggestWin || 0;
        gameState.biggestLoss = stats.biggestLoss || 0;
        gameState.netProfit = stats.netProfit || 0;
        gameState.zoneStats = stats.zoneStats || {
            green: { attempts: 0, bursts: 0, cashouts: 0, totalMultiplier: 0 },
            yellow: { attempts: 0, bursts: 0, cashouts: 0, totalMultiplier: 0 },
            red: { attempts: 0, bursts: 0, cashouts: 0, totalMultiplier: 0 }
        };
        gameState.pumpStats = stats.pumpStats || [];
        gameState.history = stats.history || [];
    }
    
    // Load view mode
    const savedViewMode = localStorage.getItem('pumpGameViewMode');
    gameState.viewMode = savedViewMode || 'standard';
}

// Save game data to localStorage
function saveGameData() {
    // Save balance
    localStorage.setItem('pumpGameBalance', gameState.balance.toString());
    
    // Save game stats
    const statsToSave = {
        totalGames: gameState.totalGames,
        wins: gameState.wins,
        losses: gameState.losses,
        totalWagered: gameState.totalWagered,
        totalWon: gameState.totalWon,
        biggestWin: gameState.biggestWin,
        biggestLoss: gameState.biggestLoss,
        netProfit: gameState.netProfit,
        zoneStats: gameState.zoneStats,
        pumpStats: gameState.pumpStats,
        history: gameState.history.slice(0, 50) // Limit history length
    };
    
    localStorage.setItem('pumpGameStats', JSON.stringify(statsToSave));
    
    // Save view mode
    localStorage.setItem('pumpGameViewMode', gameState.viewMode);
}

// Initialize game
function initializeGame() {
    // Initialize ghost balloons
    createGhostBalloons();
    
    // Initialize burst heatmap data if not already created
    if (!gameState.pumpStats.length) {
        for (let i = 1; i <= 10; i++) {
            gameState.pumpStats.push({
                pumpCount: i,
                attempts: 0,
                bursts: 0,
                burstRate: 0
            });
        }
    }
    
    // Set view mode
    setViewMode(gameState.viewMode, false);
    
    // Check for user-specific recommendations
    updateStrategyRecommendations();
}

// Handle resize event for responsive adjustments
function handleResize() {
    // Adjust game UI based on screen size
    adjustGameUi();
    
    // Update charts if in Pro View mode
    if (gameState.viewMode === 'pro' && performanceChart) {
        performanceChart.resize();
    }
}

// Adjust game UI based on screen size
function adjustGameUi() {
    // Get current screen width
    const screenWidth = window.innerWidth;
    
    // Adjust balloon size
    if (screenWidth <= 576) {
        // Mobile portrait
        balloon.style.width = '120px';
        balloon.style.height = '150px';
        balloonFace.style.fontSize = '40px';
    } else if (screenWidth <= 768) {
        // Mobile landscape / small tablet
        balloon.style.width = '130px';
        balloon.style.height = '160px';
        balloonFace.style.fontSize = '45px';
    } else {
        // Tablet / desktop
        balloon.style.width = '150px';
        balloon.style.height = '180px';
        balloonFace.style.fontSize = '50px';
    }
}

// Place a bet to start the game
function placeBet() {
    const betAmount = parseInt(betInput.value);
    
    if (isNaN(betAmount) || betAmount <= 0) {
        showFeedback('Please enter a valid bet amount', 'error');
        return;
    }
    
    if (betAmount > gameState.balance) {
        showFeedback('Not enough balance for this bet', 'error');
        return;
    }
    
    if (betAmount < Math.max(10, Math.floor(gameState.balance * 0.05)) && gameState.hasMandatoryRound) {
        showFeedback('Minimum bet is 5% of your balance', 'error');
        return;
    }
    
    // Apply house edge (20% cut)
    const actualBet = betAmount;
    const houseDeduction = Math.floor(actualBet * 0.2);
    gameState.currentBet = actualBet - houseDeduction;
    
    // Update balance
    updateBalance(gameState.balance - actualBet);
    
    // Add to total wagered
    gameState.totalWagered += actualBet;
    
    // Update game state
    gameState.isPlaying = true;
    gameState.pumpCount = 0;
    gameState.burstChance = 0.5; // Starting burst chance
    gameState.currentMultiplier = 1.0;
    
    // Update UI
    updateBet(actualBet);
    updateMultiplier(1.0);
    updatePotentialWin(Math.floor(gameState.currentBet * gameState.currentMultiplier));
    
    // Reset balloon and UI
    resetBalloonState();
    
    // Enable game controls
    enableGameControls(true);
    enableBetControls(false);
    
    // Add to history
    addToHistory({
        type: 'bet',
        amount: actualBet,
        time: new Date(),
        result: `Bet placed with ${houseDeduction} GA house fee`
    });
    
    showFeedback(`Bet placed! 20% house fee applied. Pump at least once before cashing out!`, 'info');
    
    // Show special item (5% chance for Golden Pump, 10% chance for Lucky Patch)
    const specialRoll = Math.random();
    if (specialRoll < 0.05) {
        // Golden pump - forces into red zone
        showPopup('Golden Pump Found!', 'This special pump will make your balloon grow faster! (Forces you into the Red Zone, but with a chance for bigger wins!)', 'golden');
    } else if (specialRoll < 0.15) {
        // Lucky patch - adds pumps but doubles loss
        showPopup('Lucky Patch Found!', 'This patch adds +3 extra pumps, but doubles your loss penalty if the balloon bursts!', 'lucky');
    }
}

// Pump the balloon
function pumpBalloon() {
    if (!gameState.isPlaying) return;
    
    gameState.pumpCount++;
    
    // Enable cashout button after first pump
    if (gameState.pumpCount === 1) {
        cashoutBtn.disabled = false;
        mobileCashoutBtn.disabled = false;
    }
    
    // Calculate new multiplier based on pump count
    // The growth accelerates as pump count increases
    const baseMultiplier = 1.0 + (gameState.pumpCount * 0.15);
    let newMultiplier = baseMultiplier;
    
    // Special item effect - golden pump
    if (gameState.specialItemActive === 'golden') {
        newMultiplier = Math.max(5.1, baseMultiplier); // Force into red zone
    }
    
    // Update multiplier and UI
    updateMultiplier(newMultiplier);
    updatePotentialWin(Math.floor(gameState.currentBet * gameState.currentMultiplier));
    
    // Update balloon size and color
    const balloonScale = 1 + (gameState.pumpCount * 0.1);
    balloon.style.transform = `scale(${balloonScale})`;
    
    // Animate pump
    balloon.style.animation = 'pump 0.2s ease';
    setTimeout(() => {
        balloon.style.animation = '';
    }, 200);
    
    // Update progress bar
    const progressPercent = Math.min(100, (gameState.currentMultiplier - 1) * 20);
    progressBar.style.width = `${progressPercent}%`;
    
    // Change balloon face based on pressure
    updateBalloonFace();
    
    // Change progress bar color based on zone
    updateProgressBarColor();
    
    // Change background color based on zone
    updateBackgroundColor();
    
    // Track which zone we're in
    const currentZone = getCurrentZone();
    gameState.zoneStats[currentZone].attempts++;
    
    // Check if balloon should burst
    if (shouldBurst() || gameState.pumpCount >= gameState.maxPumps) {
        // Update zone burst stats
        gameState.zoneStats[currentZone].bursts++;
        
        // Update pump stats
        updatePumpStats(true);
        
        burstBalloon();
        return;
    }
    
    // Update pump stats
    updatePumpStats(false);
    
    // Near-win trap
    if (gameState.currentMultiplier > 1.9 && gameState.currentMultiplier < 2.1) {
        showFakeCongrats('Almost at 2x! One more pump!');
    } else if (gameState.currentMultiplier > 4.8 && gameState.currentMultiplier < 5.2) {
        showFakeCongrats('Almost at 5x! So close to the BIG WIN!');
    }
    
    // If we're in a higher risk zone, make the balloon shake
    if (gameState.currentMultiplier >= 2.0) {
        balloon.style.animation = 'shake 0.2s infinite';
        setTimeout(() => {
            balloon.style.animation = '';
        }, 500);
    }
    
    // If reaching a "fake milestone", slow down the pump button
    if (Math.abs(gameState.currentMultiplier - Math.floor(gameState.currentMultiplier)) < 0.1 && gameState.currentMultiplier > 2) {
        pumpBtn.disabled = true;
        mobilePumpBtn.disabled = true;
        showFeedback('Reached a milestone! Checking safety...', 'info');
        
        // Actually increases burst chance, but player doesn't know
        gameState.burstChance += 0.05;
        
        setTimeout(() => {
            if (gameState.isPlaying) {
                pumpBtn.disabled = false;
                mobilePumpBtn.disabled = false;
                showFeedback('Safe to continue pumping!', 'success');
            }
        }, 1000); // Fake delay
    }
}

// Cash out and collect winnings
function cashout() {
    if (!gameState.isPlaying) return;
    
    // Require at least one pump before cashout
    if (gameState.pumpCount === 0) {
        showFeedback('You must pump at least once before cashing out!', 'error');
        return;
    }
    
    // 30% chance of mis-tap registering as extra pump
    if (Math.random() < 0.3) {
        showFeedback('Processing cashout...', 'info');
        
        // Delay then pump instead
        setTimeout(() => {
            if (gameState.isPlaying) {
                pumpBalloon();
            }
        }, 500);
        return;
    }
    
    // 1-second "confirm" delay (allows burst during confirmation)
    cashoutBtn.disabled = true;
    mobileCashoutBtn.disabled = true;
    pumpBtn.disabled = true;
    mobilePumpBtn.disabled = true;
    showFeedback('Confirming cashout...', 'info');
    
    setTimeout(() => {
        if (!gameState.isPlaying) return; // Balloon might have burst during delay
        
        // Calculate winnings (bet * multiplier)
        const winAmount = Math.floor(gameState.currentBet * gameState.currentMultiplier);
        updateBalance(gameState.balance + winAmount);
        
        // Track which zone we cashed out in
        const currentZone = getCurrentZone();
        gameState.zoneStats[currentZone].cashouts++;
        gameState.zoneStats[currentZone].totalMultiplier += gameState.currentMultiplier;
        
        // Update stats
        gameState.totalGames++;
        gameState.wins++;
        gameState.totalWon += winAmount;
        
        if (winAmount > gameState.biggestWin) {
            gameState.biggestWin = winAmount;
        }
        
        // Calculate net profit
        gameState.netProfit = gameState.totalWon - gameState.totalWagered;
        
        // Increment consecutive wins
        gameState.consecutiveWins++;
        gameState.lossStreak = 0;
        
        // Check if player has 3 consecutive wins
        if (gameState.consecutiveWins >= 3) {
            gameState.hasMandatoryRound = true;
            showFeedback('3 wins in a row! Next round is a high-risk round!', 'warning');
        }
        
        // Add to history
        addToHistory({
            type: 'cashout',
            amount: winAmount,
            multiplier: gameState.currentMultiplier.toFixed(1),
            pumpCount: gameState.pumpCount,
            time: new Date(),
            result: `Cashed out with ${gameState.currentMultiplier.toFixed(1)}× multiplier`
        });
        
        // Show win indicator
        showWinLossIndicator(true, winAmount);
        
        showFeedback(`Cashed out ${winAmount} GA at ${gameState.currentMultiplier.toFixed(1)}×!`, 'success');
        
        // Save stats
        saveGameData();
        
        // Update all displays
        updateAllDisplays();
        
        // Reset game
        resetGame();
    }, 1000);
}

// Burst the balloon
function burstBalloon() {
    gameState.isPlaying = false;
    
    // Animate balloon burst
    balloon.style.animation = 'burst 0.5s forwards';
    balloonFace.textContent = '😵';
    
    // Create explosion effect
    createExplosionEffect();
    
    // Show burst overlay after animation
    setTimeout(() => {
        burstOverlay.style.display = 'flex';
    }, 600);
    
    // Update stats
    gameState.totalGames++;
    gameState.losses++;
    
    if (gameState.currentBet > gameState.biggestLoss) {
        gameState.biggestLoss = gameState.currentBet;
    }
    
    // Calculate net profit
    gameState.netProfit = gameState.totalWon - gameState.totalWagered;
    
    // Reset consecutive wins, increment loss streak
    gameState.consecutiveWins = 0;
    gameState.lossStreak++;
    
    // Add to history
    addToHistory({
        type: 'burst',
        amount: gameState.currentBet,
        multiplier: gameState.currentMultiplier.toFixed(1),
        pumpCount: gameState.pumpCount,
        time: new Date(),
        result: `Balloon burst at ${gameState.currentMultiplier.toFixed(1)}×`
    });
    
    // Show loss indicator
    showWinLossIndicator(false, gameState.currentBet);
    
    // Apply special item effect message - lucky patch doubles loss (psychological effect only)
    if (gameState.specialItemActive === 'lucky') {
        showFeedback('Lucky patch increased your loss!', 'error');
    }
    
    showFeedback(`BURST! You lost ${gameState.currentBet} GA!`, 'error');
    
    // Save stats
    saveGameData();
    
    // Update all displays
    updateAllDisplays();
}

// Create explosion effect
function createExplosionEffect() {
    // Clear any existing particles
    explosionContainer.innerHTML = '';
    
    // Create explosion particles
    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'explosion-particle';
        particle.textContent = Math.random() < 0.5 ? '💥' : '💣';
        
        // Random position and animation
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * 150 + 50;
        const duration = Math.random() * 0.5 + 0.5;
        const delay = Math.random() * 0.2;
        
        // Set X, Y, and rotation variables for the animation
        particle.style.setProperty('--x', Math.cos(angle) * distance + 'px');
        particle.style.setProperty('--y', Math.sin(angle) * distance + 'px');
        particle.style.setProperty('--r', Math.random() * 360 + 'deg');
        
        // Position at balloon's current location
        const balloonRect = balloon.getBoundingClientRect();
        const containerRect = explosionContainer.getBoundingClientRect();
        
        particle.style.left = (balloonRect.left - containerRect.left + balloonRect.width / 2) + 'px';
        particle.style.top = (balloonRect.top - containerRect.top + balloonRect.height / 2) + 'px';
        particle.style.fontSize = (Math.random() * 20 + 20) + 'px';
        particle.style.animation = `flyOut ${duration}s ease-out ${delay}s forwards`;
        
        explosionContainer.appendChild(particle);
    }
    
    // Remove particles after animation completes
    setTimeout(() => {
        explosionContainer.innerHTML = '';
    }, 2000);
}

// Reset game for a new round
function resetGame() {
    // Hide burst overlay
    burstOverlay.style.display = 'none';
    
    // Reset balloon and UI
    resetBalloonState();
    
    // Reset game state
    gameState.isPlaying = false;
    gameState.currentBet = 0;
    gameState.pumpCount = 0;
    gameState.currentMultiplier = 1.0;
    gameState.specialItemActive = false;
    
    // Update UI
    updateBet(0);
    updateMultiplier(1.0);
    updatePotentialWin(0);
    
    // Reset controls
    enableGameControls(false);
    enableBetControls(true);
    
    if (gameState.hasMandatoryRound) {
        // Force higher minimum bet for mandatory round
        betInput.min = Math.floor(gameState.balance * 0.8);
        showFeedback('High-risk round! Minimum bet is 80% of your balance', 'warning');
        gameState.hasMandatoryRound = false;
    } else {
        betInput.min = 10;
        showFeedback('Place a bet to start a new game', 'info');
    }
    
    // Update ghost balloons
    updateGhostBalloons();
    
    // Update strategy recommendations
    updateStrategyRecommendations();
}

// Reset balloon state
function resetBalloonState() {
    balloon.style.transform = 'scale(1)';
    balloon.style.animation = '';
    balloonFace.textContent = '😊';
    progressBar.style.width = '0%';
    progressBar.style.backgroundColor = 'var(--green-zone)';
    gameBackground.style.backgroundColor = '';
}

// Enable/disable game controls
function enableGameControls(enabled) {
    pumpBtn.disabled = !enabled;
    mobilePumpBtn.disabled = !enabled;
    
    // Cashout requires at least one pump
    cashoutBtn.disabled = true; 
    mobileCashoutBtn.disabled = true;
}

// Enable/disable bet controls
function enableBetControls(enabled) {
    placeBetBtn.disabled = !enabled;
    betInput.disabled = !enabled;
    decreaseBetBtn.disabled = !enabled;
    increaseBetBtn.disabled = !enabled;
    minBetBtn.disabled = !enabled;
    halfBetBtn.disabled = !enabled;
    doubleBetBtn.disabled = !enabled;
    maxBetBtn.disabled = !enabled;
}

// Check if balloon should burst
function shouldBurst() {
    // Calculate burst probability based on current multiplier
    let baseBurstChance;
    
    // Different burst chances for different zones
    if (gameState.currentMultiplier <= 2.0) {
        // Green zone: 40-50% burst chance
        baseBurstChance = 0.4 + (gameState.pumpCount * 0.02);
    } else if (gameState.currentMultiplier <= 5.0) {
        // Yellow zone: 60-75% burst chance
        baseBurstChance = 0.6 + (gameState.pumpCount * 0.03);
    } else {
        // Red zone: 80-95% burst chance
        baseBurstChance = 0.8 + (gameState.pumpCount * 0.03);
    }
    
    // Apply loss streak penalty
    const adjustedBurstChance = Math.min(0.95, baseBurstChance + (gameState.lossStreak * 0.03));
    
    // Roll for burst
    return Math.random() < adjustedBurstChance;
}

// Update balloon face based on multiplier
function updateBalloonFace() {
    if (gameState.currentMultiplier <= 2.0) {
        balloonFace.textContent = '😊';
    } else if (gameState.currentMultiplier <= 3.0) {
        balloonFace.textContent = '😀';
    } else if (gameState.currentMultiplier <= 4.0) {
        balloonFace.textContent = '😬';
    } else if (gameState.currentMultiplier <= 5.0) {
        balloonFace.textContent = '😨';
    } else {
        balloonFace.textContent = '😱';
    }
}

// Update progress bar color based on zone
function updateProgressBarColor() {
    if (gameState.currentMultiplier <= 2.0) {
        progressBar.style.backgroundColor = 'var(--green-zone)';
    } else if (gameState.currentMultiplier <= 5.0) {
        progressBar.style.backgroundColor = 'var(--yellow-zone)';
    } else {
        progressBar.style.backgroundColor = 'var(--red-zone)';
    }
}

// Update background color based on multiplier
function updateBackgroundColor() {
    let redAmount = Math.min(255, Math.floor(gameState.currentMultiplier * 30));
    gameBackground.style.backgroundColor = `rgba(${redAmount}, 100, 100, 0.1)`;
}

// Get current risk zone based on multiplier
function getCurrentZone() {
    if (gameState.currentMultiplier <= 2.0) {
        return 'green';
    } else if (gameState.currentMultiplier <= 5.0) {
        return 'yellow';
    } else {
        return 'red';
    }
}

// Update pump statistics
function updatePumpStats(didBurst) {
    // Find the stats entry for this pump count
    const pumpIndex = Math.min(gameState.pumpCount, 10) - 1;
    if (pumpIndex >= 0) {
        gameState.pumpStats[pumpIndex].attempts++;
        
        if (didBurst) {
            gameState.pumpStats[pumpIndex].bursts++;
        }
        
        // Update burst rate
        gameState.pumpStats[pumpIndex].burstRate = 
            gameState.pumpStats[pumpIndex].bursts / gameState.pumpStats[pumpIndex].attempts;
    }
    
    // Update heatmap if in Pro View
    if (gameState.viewMode === 'pro') {
        updateBurstHeatmap();
    }
}

// Update balance display
function updateBalance(newBalance) {
    gameState.balance = newBalance;
    balanceValue.textContent = gameState.balance;
    mobileBalance.textContent = `${gameState.balance} GA`;
}

// Update bet display
function updateBet(bet) {
    betValue.textContent = bet;
}

// Update multiplier display
function updateMultiplier(multiplier) {
    gameState.currentMultiplier = Math.min(10, Math.round(multiplier * 10) / 10);
    multiplierValue.textContent = gameState.currentMultiplier.toFixed(1) + '×';
    mobileMultiplier.textContent = gameState.currentMultiplier.toFixed(1) + '×';
    
    // Make multiplier flash in red zone
    if (gameState.currentMultiplier > 5.0) {
        multiplierValue.style.color = 'var(--red-zone)';
        mobileMultiplier.style.color = 'var(--red-zone)';
        multiplierValue.style.animation = 'shake 0.2s infinite';
    } else if (gameState.currentMultiplier > 2.0) {
        multiplierValue.style.color = 'var(--yellow-zone)';
        mobileMultiplier.style.color = 'var(--yellow-zone)';
        multiplierValue.style.animation = '';
    } else {
        multiplierValue.style.color = '';
        mobileMultiplier.style.color = '';
        multiplierValue.style.animation = '';
    }
}

// Update potential win display
function updatePotentialWin(amount) {
    potentialWinValue.textContent = amount;
}

// Show feedback message
function showFeedback(message, type) {
    gameFeedback.textContent = message;
    
    // Set color based on type
    if (type === 'error') {
        gameFeedback.style.color = 'var(--red-zone)';
    } else if (type === 'success') {
        gameFeedback.style.color = 'var(--green-zone)';
    } else if (type === 'warning') {
        gameFeedback.style.color = 'var(--yellow-zone)';
    } else {
        gameFeedback.style.color = 'var(--text-primary)';
    }
}

// Show fake congratulations message
function showFakeCongrats(message) {
    congratsBanner.textContent = message;
    congratsBanner.style.opacity = '1';
    
    setTimeout(() => {
        congratsBanner.style.opacity = '0';
    }, 2000);
}

// Show popup for special items
function showPopup(title, message, type) {
    document.querySelector('.popup-title').textContent = title;
    popupContent.textContent = message;
    
    specialItemPopup.style.display = 'block';
    popupOverlay.style.display = 'block';
    
    // Store the type for later reference
    specialItemPopup.dataset.type = type;
}

// Handle special item acceptance/rejection
function handleSpecialItem(accepted) {
    const type = specialItemPopup.dataset.type;
    
    if (accepted) {
        gameState.specialItemActive = type;
        
        if (type === 'golden') {
            // Golden pump forces player into red zone
            showFeedback('Golden pump activated! Balloon will grow faster!', 'warning');
        } else if (type === 'lucky') {
            // Lucky patch adds pumps but doubles loss (psychological effect)
            gameState.maxPumps += 3;
            showFeedback('Lucky patch applied! You can pump 3 more times!', 'success');
        }
    }
    
    // Hide popup
    specialItemPopup.style.display = 'none';
    popupOverlay.style.display = 'none';
}

// Add entry to game history
function addToHistory(entry) {
    // Add to history array
    gameState.history.unshift(entry);
    
    // Limit history length
    if (gameState.history.length > 50) {
        gameState.history.pop();
    }
    
    // Update history displays
    updateHistoryDisplay();
    updateMobileHistoryDisplay();
}

// Update history display
function updateHistoryDisplay() {
    historyList.innerHTML = '';
    
    if (gameState.history.length === 0) {
        historyList.innerHTML = '<div class="history-placeholder">No recent activity to show</div>';
        return;
    }
    
    // Show the 10 most recent entries
    gameState.history.slice(0, 10).forEach(entry => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        
        // Format time
        const time = new Date(entry.time);
        const timeString = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;
        
        // Different format based on entry type
        if (entry.type === 'bet') {
            historyItem.innerHTML = `
                <div><strong>Bet:</strong> ${entry.amount} GA <span class="history-time">${timeString}</span></div>
                <div>${entry.result}</div>
            `;
        } else if (entry.type === 'cashout') {
            historyItem.innerHTML = `
                <div class="history-win"><strong>Win:</strong> ${entry.amount} GA (${entry.multiplier}×) <span class="history-time">${timeString}</span></div>
                <div>${entry.result} after ${entry.pumpCount} pumps</div>
            `;
        } else if (entry.type === 'burst') {
            historyItem.innerHTML = `
                <div class="history-loss"><strong>Loss:</strong> ${entry.amount} GA <span class="history-time">${timeString}</span></div>
                <div>${entry.result} after ${entry.pumpCount} pumps</div>
            `;
        }
        
        historyList.appendChild(historyItem);
    });
}

// Update mobile history display
function updateMobileHistoryDisplay() {
    mobileHistoryList.innerHTML = '';
    
    if (gameState.history.length === 0) {
        mobileHistoryList.innerHTML = '<div class="mobile-history-item">No recent activity</div>';
        return;
    }
    
    // Show the 5 most recent entries
    gameState.history.slice(0, 5).forEach(entry => {
        const historyItem = document.createElement('div');
        historyItem.className = 'mobile-history-item';
        
        // Format time
        const time = new Date(entry.time);
        const timeString = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;
        
        // Different display based on entry type
        let amount, resultClass;
        
        if (entry.type === 'bet') {
            amount = `-${entry.amount} GA`;
            resultClass = 'history-loss';
        } else if (entry.type === 'cashout') {
            amount = `+${entry.amount} GA`;
            resultClass = 'history-win';
        } else if (entry.type === 'burst') {
            amount = `-${entry.amount} GA`;
            resultClass = 'history-loss';
        }
        
        historyItem.innerHTML = `
            <div>${entry.type === 'cashout' ? entry.multiplier + '×' : timeString}</div>
            <div class="${resultClass}">${amount}</div>
        `;
        
        mobileHistoryList.appendChild(historyItem);
    });
}

// Create ghost balloons
function createGhostBalloons() {
    // Clear existing ghost balloons
    ghostBalloons.innerHTML = '';
    
    // Create ghost balloons showing where others supposedly burst
    for (let i = 0; i < 5; i++) {
        const ghostBalloon = document.createElement('div');
        ghostBalloon.className = 'ghost-balloon';
        
        // Random position
        const top = Math.random() * 80 + 10;
        const left = Math.random() * 80 + 10;
        ghostBalloon.style.top = `${top}%`;
        ghostBalloon.style.left = `${left}%`;
        
        // Random size (multiplier)
        const size = (Math.random() * 2 + 3).toFixed(1);
        ghostBalloon.style.opacity = '0.3';
        ghostBalloon.textContent = `${size}×`;
        
        ghostBalloons.appendChild(ghostBalloon);
    }
}

// Update ghost balloons
function updateGhostBalloons() {
    // Add a new ghost balloon where player burst or cashed out
    if (gameState.pumpCount > 0 && !gameState.isPlaying) {
        const newGhost = document.createElement('div');
        newGhost.className = 'ghost-balloon';
        
        // Position near center
        const top = 40 + (Math.random() * 20);
        const left = 40 + (Math.random() * 20);
        newGhost.style.top = `${top}%`;
        newGhost.style.left = `${left}%`;
        
        // Set size based on when player burst or cashed out
        newGhost.textContent = `${gameState.currentMultiplier.toFixed(1)}×`;
        
        ghostBalloons.appendChild(newGhost);
        
        // Remove oldest ghost if there are too many
        if (ghostBalloons.children.length > 8) {
            ghostBalloons.removeChild(ghostBalloons.children[0]);
        }
    }
}

// Update all displays
function updateAllDisplays() {
    // Update basic stats
    updateGameStats();
    
    // Update Pro View analytics
    if (gameState.viewMode === 'pro') {
        updateProViewAnalytics();
    }
    
    // Update mobile displays
    updateMobileDisplays();
}

// Update game statistics
function updateGameStats() {
    // Calculate win rate
    const winRate = gameState.totalGames > 0 ? 
        Math.round((gameState.wins / gameState.totalGames) * 100) : 0;
    
    // Calculate average multiplier for cashouts
    let totalCashouts = 0;
    let totalMultiplier = 0;
    
    Object.keys(gameState.zoneStats).forEach(zone => {
        totalCashouts += gameState.zoneStats[zone].cashouts;
        totalMultiplier += gameState.zoneStats[zone].totalMultiplier;
    });
    
    const avgMultiplier = totalCashouts > 0 ? 
        (totalMultiplier / totalCashouts).toFixed(1) : '0.0';
    
    // Update display
    totalGamesValue.textContent = gameState.totalGames;
    winRateValue.textContent = `${winRate}%`;
    biggestWinValue.textContent = gameState.biggestWin;
    biggestLossValue.textContent = gameState.biggestLoss;
    avgMultiplierValue.textContent = `${avgMultiplier}×`;
    netProfitValue.textContent = `${gameState.netProfit >= 0 ? '+' : ''}${gameState.netProfit} GA`;
    
    // Apply color to net profit
    if (gameState.netProfit > 0) {
        netProfitValue.style.color = 'var(--green-zone)';
    } else if (gameState.netProfit < 0) {
        netProfitValue.style.color = 'var(--red-zone)';
    } else {
        netProfitValue.style.color = '';
    }
}

// Update Pro View analytics
function updateProViewAnalytics() {
    // Calculate derived metrics
    const totalCashouts = Object.values(gameState.zoneStats).reduce((sum, zone) => sum + zone.cashouts, 0);
    const totalBursts = Object.values(gameState.zoneStats).reduce((sum, zone) => sum + zone.bursts, 0);
    const avgBetAmount = gameState.totalGames > 0 ? Math.round(gameState.totalWagered / gameState.totalGames) : 0;
    const roi = gameState.totalWagered > 0 ? Math.round((gameState.netProfit / gameState.totalWagered) * 100) : 0;
    const pumpEfficiencyValue = totalCashouts + totalBursts > 0 ? 
        Math.round((totalCashouts / (totalCashouts + totalBursts)) * 100) : 0;
    
    // Update analytics display
    totalWagered.textContent = `${gameState.totalWagered} GA`;
    totalCashouts.textContent = totalCashouts;
    totalBursts.textContent = totalBursts;
    avgBet.textContent = `${avgBetAmount} GA`;
    roiValue.textContent = `${roi}%`;
    pumpEfficiency.textContent = `${pumpEfficiencyValue}%`;
    
    // Apply appropriate class to ROI
    if (roi > 0) {
        roiValue.className = 'analytics-value positive';
    } else if (roi < 0) {
        roiValue.className = 'analytics-value negative';
    } else {
        roiValue.className = 'analytics-value';
    }
    
    // Update risk zone statistics
    updateRiskZoneStats();
    
    // Update performance chart
    updatePerformanceChart();
    
    // Update burst heatmap
    updateBurstHeatmap();
}

// Update risk zone statistics
function updateRiskZoneStats() {
    // Calculate burst rates for each zone
    const greenBurstRateValue = gameState.zoneStats.green.attempts > 0 ? 
        Math.round((gameState.zoneStats.green.bursts / gameState.zoneStats.green.attempts) * 100) : 0;
    
    const yellowBurstRateValue = gameState.zoneStats.yellow.attempts > 0 ? 
        Math.round((gameState.zoneStats.yellow.bursts / gameState.zoneStats.yellow.attempts) * 100) : 0;
    
    const redBurstRateValue = gameState.zoneStats.red.attempts > 0 ? 
        Math.round((gameState.zoneStats.red.bursts / gameState.zoneStats.red.attempts) * 100) : 0;
    
    // Calculate average cashout multiplier for each zone
    const greenAvgCashoutValue = gameState.zoneStats.green.cashouts > 0 ? 
        (gameState.zoneStats.green.totalMultiplier / gameState.zoneStats.green.cashouts).toFixed(1) : '0.0';
    
    const yellowAvgCashoutValue = gameState.zoneStats.yellow.cashouts > 0 ? 
        (gameState.zoneStats.yellow.totalMultiplier / gameState.zoneStats.yellow.cashouts).toFixed(1) : '0.0';
    
    const redAvgCashoutValue = gameState.zoneStats.red.cashouts > 0 ? 
        (gameState.zoneStats.red.totalMultiplier / gameState.zoneStats.red.cashouts).toFixed(1) : '0.0';
    
    // Update display
    greenBurstRate.textContent = `${greenBurstRateValue}%`;
    greenAvgCashout.textContent = `${greenAvgCashoutValue}×`;
    yellowBurstRate.textContent = `${yellowBurstRateValue}%`;
    yellowAvgCashout.textContent = `${yellowAvgCashoutValue}×`;
    redBurstRate.textContent = `${redBurstRateValue}%`;
    redAvgCashout.textContent = `${redAvgCashoutValue}×`;
    
    // Update mobile display
    mobileRedZoneRate.textContent = `${redBurstRateValue}%`;
}

// Initialize charts for Pro View
function initializeCharts() {
    initializePerformanceChart();
    initializeBurstHeatmap();
}

// Initialize performance chart
function initializePerformanceChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    
    // Prepare data for the chart
    const gameHistory = [...gameState.history].reverse();
    const balanceHistory = [];
    let runningBalance = gameState.balance;
    
    // Work backwards through history to reconstruct balance
    for (let i = gameHistory.length - 1; i >= 0; i--) {
        const entry = gameHistory[i];
        
        if (entry.type === 'cashout') {
            runningBalance -= entry.amount;
        } else if (entry.type === 'bet') {
            runningBalance += entry.amount;
        } else if (entry.type === 'burst') {
            // No adjustment needed, bet was already deducted
        }
        
        balanceHistory.push(runningBalance);
    }
    
    // Add current balance
    balanceHistory.push(gameState.balance);
    
    // Create labels (game numbers)
    const labels = Array.from({length: balanceHistory.length}, (_, i) => i + 1);
    
    // Destroy existing chart if it exists
    if (performanceChart) {
        performanceChart.destroy();
    }
    
    // Create chart
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Balance History',
                data: balanceHistory,
                borderColor: '#FF5722',
                backgroundColor: 'rgba(255, 87, 34, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return `Game ${context[0].label}`;
                        },
                        label: function(context) {
                            return `Balance: ${context.parsed.y} GA`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#666'
                    }
                },
                x: {
                    display: false
                }
            }
        }
    });
}

// Update performance chart
function updatePerformanceChart() {
    if (!performanceChart) return;
    
    // Prepare data for the chart
    const gameHistory = [...gameState.history].reverse();
    const balanceHistory = [];
    let runningBalance = gameState.balance;
    
    // Work backwards through history to reconstruct balance
    for (let i = gameHistory.length - 1; i >= 0; i--) {
        const entry = gameHistory[i];
        
        if (entry.type === 'cashout') {
            runningBalance -= entry.amount;
        } else if (entry.type === 'bet') {
            runningBalance += entry.amount;
        } else if (entry.type === 'burst') {
            // No adjustment needed, bet was already deducted
        }
        
        balanceHistory.push(runningBalance);
    }
    
    // Add current balance
    balanceHistory.push(gameState.balance);
    
    // Create labels (game numbers)
    const labels = Array.from({length: balanceHistory.length}, (_, i) => i + 1);
    
    // Update chart data
    performanceChart.data.labels = labels;
    performanceChart.data.datasets[0].data = balanceHistory;
    performanceChart.update();
}

// Initialize burst heatmap
function initializeBurstHeatmap() {
    burstHeatmapEl.innerHTML = '';
    
    // Create 10x10 grid for pump count (1-10) and burst chance (0-100%)
    for (let i = 0; i < 10; i++) {
        for (let j = 0; j < 10; j++) {
            const cell = document.createElement('div');
            cell.className = 'heatmap-cell';
            cell.id = `heatmap-${i}-${j}`;
            
            const content = document.createElement('div');
            content.className = 'cell-content';
            
            // Heat level is based on the column (0-9 representing 0-90% burst chance)
            content.classList.add(`heat-${j}`);
            
            // Cell shows burst probability for each pump count
            if (i === 9 && j === 9) {
                content.textContent = '90%+';
            } else if (i === 0 && j === 0) {
                content.textContent = '0%';
            } else if (j % 3 === 0 && i % 3 === 0) {
                content.textContent = `${j * 10}%`;
            }
            
            cell.appendChild(content);
            burstHeatmapEl.appendChild(cell);
        }
    }
    
    // Update with actual data
    updateBurstHeatmap();
}

// Update burst heatmap
function updateBurstHeatmap() {
    if (!burstHeatmapEl) return;
    
    // Clear highlighted cells
    const highlightedCells = document.querySelectorAll('.heatmap-cell.highlighted');
    highlightedCells.forEach(cell => cell.classList.remove('highlighted'));
    
    // Highlight cells based on actual burst rates
    gameState.pumpStats.forEach((stat, index) => {
        if (stat.attempts > 0) {
            // Calculate which column (0-9) based on burst rate
            const column = Math.min(9, Math.floor(stat.burstRate * 10));
            
            // Highlight cell
            const cell = document.getElementById(`heatmap-${index}-${column}`);
            if (cell) {
                cell.classList.add('highlighted');
                
                // Update content to show actual burst rate if attempts > 5
                if (stat.attempts >= 5) {
                    const content = cell.querySelector('.cell-content');
                    content.textContent = `${Math.round(stat.burstRate * 100)}%`;
                }
            }
        }
    });
}

// Update strategy recommendations
function updateStrategyRecommendations() {
    // Find optimal cashout point based on player history
    let conservativePoint = 1.5;
    let balancedPoint = 2.5;
    let aggressivePoint = 4.0;
    
    // Use zone stats to refine recommendations
    if (gameState.zoneStats.green.attempts > 10) {
        const greenBurstRate = gameState.zoneStats.green.bursts / gameState.zoneStats.green.attempts;
        
        // Adjust conservative point based on green zone performance
        if (greenBurstRate < 0.3) {
            conservativePoint = 1.7;
        } else if (greenBurstRate > 0.6) {
            conservativePoint = 1.3;
        }
    }
    
    if (gameState.zoneStats.yellow.attempts > 10) {
        const yellowBurstRate = gameState.zoneStats.yellow.bursts / gameState.zoneStats.yellow.attempts;
        
        // Adjust balanced point based on yellow zone performance
        if (yellowBurstRate < 0.4) {
            balancedPoint = 3.0;
        } else if (yellowBurstRate > 0.7) {
            balancedPoint = 2.2;
        }
    }
    
    if (gameState.zoneStats.red.attempts > 5) {
        const redBurstRate = gameState.zoneStats.red.bursts / gameState.zoneStats.red.attempts;
        
        // Adjust aggressive point based on red zone performance
        if (redBurstRate < 0.5) {
            aggressivePoint = 5.0;
        } else if (redBurstRate > 0.8) {
            aggressivePoint = 3.5;
        }
    }
    
    // Round to 1 decimal place
    conservativePoint = Math.round(conservativePoint * 10) / 10;
    balancedPoint = Math.round(balancedPoint * 10) / 10;
    aggressivePoint = Math.round(aggressivePoint * 10) / 10;
    
    // Update strategy recommendation text
    let strategyMessage = '';
    if (gameState.totalGames < 10) {
        strategyMessage = 'Play more games to receive personalized strategy recommendations.';
    } else {
        const winRate = Math.round((gameState.wins / gameState.totalGames) * 100);
        
        if (winRate < 30) {
            strategyMessage = `Based on your ${winRate}% win rate, you should try cashing out earlier at <strong>${conservativePoint}×</strong> to improve results.`;
        } else if (winRate > 70) {
            strategyMessage = `Your win rate is excellent at ${winRate}%! You could try pushing to <strong>${aggressivePoint}×</strong> for bigger wins.`;
        } else {
            strategyMessage = `With a balanced ${winRate}% win rate, your optimal cashout point is <strong>${balancedPoint}×</strong>.`;
        }
    }
    
    // Update UI
    strategyText.innerHTML = strategyMessage;
    conservativeRec.textContent = `${conservativePoint}×`;
    balancedRec.textContent = `${balancedPoint}×`;
    aggressiveRec.textContent = `${aggressivePoint}×`;
    
    // Update mobile recommendation
    mobileCashoutPoint.textContent = balancedPoint + '×';
}

// Update mobile displays
function updateMobileDisplays() {
    mobileTotalGames.textContent = gameState.totalGames;
    
    const winRate = gameState.totalGames > 0 ? 
        Math.round((gameState.wins / gameState.totalGames) * 100) : 0;
    mobileWinRate.textContent = `${winRate}%`;
    
    mobileBestWin.textContent = `${gameState.biggestWin} GA`;
    mobileNetProfit.textContent = `${gameState.netProfit >= 0 ? '+' : ''}${gameState.netProfit} GA`;
    
    // Apply color to net profit
    if (gameState.netProfit > 0) {
        mobileNetProfit.style.color = 'var(--green-zone)';
    } else if (gameState.netProfit < 0) {
        mobileNetProfit.style.color = 'var(--red-zone)';
    } else {
        mobileNetProfit.style.color = '';
    }
}

// Show win/loss indicator
function showWinLossIndicator(isWin, amount) {
    const indicator = document.createElement('div');
    indicator.className = isWin ? 'win-indicator' : 'loss-indicator';
    
    indicator.innerHTML = `
        <i class="${isWin ? 'fas fa-trophy' : 'fas fa-skull'}"></i>
        <span>${isWin ? '+' : '-'}${amount} GA</span>
    `;
    
    headerWinLossIndicator.innerHTML = '';
    headerWinLossIndicator.appendChild(indicator);
    
    // Remove after animation completes
    setTimeout(() => {
        if (headerWinLossIndicator.contains(indicator)) {
            headerWinLossIndicator.removeChild(indicator);
        }
    }, 3000);
}

// Validate bet input
function validateBetInput() {
    let value = parseInt(betInput.value);
    
    // Ensure minimum bet
    if (isNaN(value) || value < 10) {
        value = 10;
    }
    
    // Ensure maximum bet
    if (value > gameState.balance) {
        value = gameState.balance;
    }
    
    betInput.value = value;
}

// Set bet amount
function setBetAmount(amount) {
    betInput.value = Math.min(amount, gameState.balance);
}

// Adjust bet amount
function adjustBet(amount) {
    let currentBet = parseInt(betInput.value) || 0;
    setBetAmount(currentBet + amount);
}

// Toggle mobile stats panel
function toggleMobileStatsPanel() {
    mobileStatsPanel.classList.toggle('active');
}

// Close mobile stats panel
function closeMobileStatsPanel() {
    mobileStatsPanel.classList.remove('active');
}

// Toggle content section
function toggleSection(section) {
    section.classList.toggle('active');
    
    // Update toggle button icon
    const button = section.previousElementSibling.querySelector('.toggle-btn');
    if (button) {
        button.innerHTML = section.classList.contains('active') ? 
            '<i class="fas fa-chevron-up"></i>' : 
            '<i class="fas fa-chevron-down"></i>';
    }
}

// Set view mode (standard or pro)
function setViewMode(mode, savePreference = true) {
    gameState.viewMode = mode;
    
    if (mode === 'standard') {
        document.body.classList.remove('pro-view-active');
        standardViewBtn.classList.add('active');
        proViewBtn.classList.remove('active');
    } else {
        document.body.classList.add('pro-view-active');
        proViewBtn.classList.add('active');
        standardViewBtn.classList.remove('active');
        
        // Initialize charts if they don't exist
        if (!performanceChart) {
            initializeCharts();
        } else {
            // Update existing charts
            updatePerformanceChart();
            updateBurstHeatmap();
        }
    }
    
    if (savePreference) {
        localStorage.setItem('pumpGameViewMode', mode);
    }
}