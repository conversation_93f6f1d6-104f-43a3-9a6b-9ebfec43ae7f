/* Enhanced Settings Page - Mobile-First Responsive Design */
:root {
    /* Settings-specific color palette */
    --settings-primary: #6366f1;
    --settings-primary-hover: #5855eb;
    --settings-secondary: #8b5cf6;
    --settings-accent: #06b6d4;
    --settings-success: #10b981;
    --settings-warning: #f59e0b;
    --settings-danger: #ef4444;
    --settings-info: #3b82f6;
    
    /* Settings card backgrounds */
    --settings-card-bg: rgba(255, 255, 255, 0.05);
    --settings-card-border: rgba(255, 255, 255, 0.1);
    --settings-card-hover: rgba(255, 255, 255, 0.08);
    
    /* Form colors */
    --form-bg: rgba(255, 255, 255, 0.05);
    --form-border: rgba(255, 255, 255, 0.15);
    --form-focus: var(--settings-primary);
    
    /* Status colors */
    --status-active: #10b981;
    --status-inactive: #6b7280;
    --status-warning: #f59e0b;
    --status-error: #ef4444;
    
    /* Mobile-specific variables */
    --mobile-settings-padding: 16px;
    --mobile-sidebar-width: 100%;
    --desktop-sidebar-width: 280px;
    --settings-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-first base layout */
body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
    background: var(--dark-bg-1, #121212);
    color: var(--text-color, #f5f5f5);
    line-height: 1.6;
    min-height: 100vh;
}

/* Enhanced Header */
.header {
    background: var(--settings-card-bg);
    border-bottom: 1px solid var(--settings-card-border);
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px var(--mobile-settings-padding);
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--text-color);
    transition: var(--settings-transition);
}

.logo-link:hover {
    color: var(--settings-primary);
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.logo-text {
    font-size: 18px;
    font-weight: 700;
    display: none;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 16px;
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    background: var(--form-bg);
    border: 1px solid var(--form-border);
    border-radius: 8px;
    padding: 8px 16px 8px 40px;
    color: var(--text-color);
    font-size: 14px;
    outline: none;
    transition: var(--settings-transition);
}

.search-input:focus {
    border-color: var(--form-focus);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-muted);
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-profile {
    display: none;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
    font-size: 14px;
}

.user-profile i {
    font-size: 20px;
    color: var(--settings-primary);
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--settings-transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn-logout {
    background: var(--settings-danger);
    color: white;
}

.btn-logout:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.menu-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--settings-transition);
}

.menu-toggle:hover {
    background: var(--settings-card-hover);
}

/* Settings Container */
.settings-container {
    display: flex;
    min-height: calc(100vh - 60px);
    max-width: 1400px;
    margin: 0 auto;
    background: var(--dark-bg-1, #121212);
}

/* Enhanced Settings Sidebar */
.settings-sidebar {
    width: var(--mobile-sidebar-width);
    background: var(--settings-card-bg);
    border-right: 1px solid var(--settings-card-border);
    padding: 24px 0;
    position: fixed;
    top: 60px;
    left: -100%;
    height: calc(100vh - 60px);
    z-index: 50;
    transition: var(--settings-transition);
    backdrop-filter: blur(10px);
    overflow-y: auto;
}

.settings-sidebar.active {
    left: 0;
}

.settings-nav {
    padding: 0 var(--mobile-settings-padding);
}

.settings-nav h2 {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-nav h2 i {
    color: var(--settings-primary);
}

.back-to-app {
    margin-bottom: 24px;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color-muted);
    text-decoration: none;
    font-size: 14px;
    transition: var(--settings-transition);
}

.back-link:hover {
    color: var(--settings-primary);
}

.settings-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-color-muted);
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: var(--settings-transition);
    border: 1px solid transparent;
}

.menu-item:hover {
    background: var(--settings-card-hover);
    color: var(--text-color);
}

.menu-item.active {
    background: rgba(99, 102, 241, 0.15);
    border-color: var(--settings-primary);
    color: var(--settings-primary);
}

.menu-item i {
    width: 18px;
    text-align: center;
    font-size: 16px;
}

/* Enhanced Settings Content */
.settings-content {
    flex: 1;
    padding: 24px var(--mobile-settings-padding);
    margin-left: 0;
    transition: var(--settings-transition);
    background: var(--dark-bg-1, #121212);
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    font-size: 14px;
    color: var(--text-color-muted);
}

.breadcrumb .separator {
    color: var(--text-color-muted);
}

.breadcrumb span:last-child {
    color: var(--settings-primary);
    font-weight: 600;
}

/* Section Styling */
.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.section-header {
    margin-bottom: 32px;
}

.section-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
}

.section-header p {
    color: var(--text-color-muted);
    font-size: 16px;
    margin: 0;
}

/* Enhanced Settings Cards */
.settings-card {
    background: var(--dark-bg-2, #1e1e1e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
    transition: var(--settings-transition);
}

.settings-card:hover {
    border-color: var(--accent-color, #ffd700);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.1);
}

.settings-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.danger-zone {
    border-color: rgba(239, 68, 68, 0.3);
}

.danger-zone:hover {
    border-color: rgba(239, 68, 68, 0.5);
    box-shadow: 0 4px 20px rgba(239, 68, 68, 0.1);
}

/* Enhanced Form Styling */
.settings-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.form-input,
.form-select {
    background: var(--dark-bg-3, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    padding: 12px 16px;
    color: var(--text-color, #f5f5f5);
    font-size: 14px;
    outline: none;
    transition: var(--settings-transition);
    width: 100%;
    box-sizing: border-box;
}

.form-input:focus,
.form-select:focus {
    border-color: var(--accent-color, #ffd700);
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

.form-hint {
    font-size: 12px;
    color: var(--text-color-muted);
    margin-top: 4px;
}

.time-input {
    max-width: 120px;
}

.time-range {
    display: flex;
    align-items: center;
    gap: 12px;
}

.time-range span {
    color: var(--text-color-muted);
    font-size: 14px;
}

/* Enhanced Form Actions */
.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.btn-primary {
    background: var(--accent-color, #ffd700);
    color: #000;
    padding: 12px 24px;
    font-weight: 600;
}

.btn-primary:hover {
    background: var(--accent-color-hover, #f8c400);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-secondary {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color, #f5f5f5);
    padding: 12px 24px;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--accent-color, #ffd700);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--settings-primary);
    color: var(--settings-primary);
    padding: 10px 20px;
}

.btn-outline:hover {
    background: var(--settings-primary);
    color: white;
}

.btn-warning {
    background: var(--settings-warning);
    color: white;
    padding: 10px 20px;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-danger {
    background: var(--settings-danger);
    color: white;
    padding: 10px 20px;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-text {
    background: none;
    border: none;
    color: var(--settings-primary);
    padding: 8px 12px;
    font-size: 13px;
}

.btn-text:hover {
    background: rgba(99, 102, 241, 0.1);
}

.btn-link {
    background: none;
    border: none;
    color: var(--settings-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    font-size: 14px;
}

.btn-link:hover {
    text-decoration: underline;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Profile Picture Section */
.profile-picture-section {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.current-picture {
    flex-shrink: 0;
}

.profile-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--settings-card-border);
}

.picture-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Enhanced Toggle Switches */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
    cursor: pointer;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--text-color-muted);
    border-radius: 24px;
    transition: var(--settings-transition);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    border-radius: 50%;
    transition: var(--settings-transition);
}

.toggle-switch input:checked + .toggle-slider {
    background: var(--settings-primary);
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(24px);
}

.toggle-switch input:disabled + .toggle-slider {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Control Items */
.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid var(--settings-card-border);
}

.control-item:last-child {
    border-bottom: none;
}

.control-info {
    flex: 1;
}

.control-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.control-info p {
    font-size: 13px;
    color: var(--text-color-muted);
    margin: 0;
    line-height: 1.4;
}

/* Password Strength Meter */
.password-strength {
    margin-top: 8px;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--settings-card-border);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 4px;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: var(--settings-transition);
    border-radius: 2px;
}

.strength-fill.weak {
    background: var(--settings-danger);
}

.strength-fill.medium {
    background: var(--settings-warning);
}

.strength-fill.strong {
    background: var(--settings-info);
}

.strength-fill.very-strong {
    background: var(--settings-success);
}

.strength-text {
    font-size: 12px;
    color: var(--text-color-muted);
}

/* MFA Section */
.mfa-section {
    text-align: center;
}

.mfa-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 12px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-indicator i {
    font-size: 16px;
}

.status-indicator.disabled {
    color: var(--status-inactive);
}

.status-indicator.enabled {
    color: var(--status-active);
}

.mfa-description {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.5;
    margin: 0;
}

/* Sessions List */
.sessions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--dark-bg-3, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    gap: 16px;
}

.session-item.current {
    border-color: var(--settings-primary);
    background: rgba(99, 102, 241, 0.05);
}

.session-info {
    flex: 1;
}

.device-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.device-info i {
    color: var(--settings-primary);
    width: 16px;
}

.device-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
}

.current-label {
    background: var(--settings-success);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.session-details {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: var(--text-color-muted);
}

.sessions-actions {
    margin-top: 16px;
}

/* Theme Options */
.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 16px;
    margin-top: 8px;
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.theme-option input {
    display: none;
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 6px;
    border: 2px solid var(--settings-card-border);
    overflow: hidden;
    position: relative;
    transition: var(--settings-transition);
}

.theme-option input:checked + .theme-preview {
    border-color: var(--settings-primary);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.theme-preview.light {
    background: #ffffff;
}

.theme-preview.dark {
    background: #1f2937;
}

.theme-preview.auto {
    background: linear-gradient(45deg, #ffffff 50%, #1f2937 50%);
}

.preview-header {
    height: 12px;
    background: rgba(0, 0, 0, 0.1);
}

.theme-preview.light .preview-header {
    background: rgba(0, 0, 0, 0.05);
}

.theme-preview.dark .preview-header {
    background: rgba(255, 255, 255, 0.1);
}

.preview-content {
    height: calc(100% - 12px);
    background: rgba(0, 0, 0, 0.02);
}

.theme-preview.dark .preview-content {
    background: rgba(255, 255, 255, 0.05);
}

.theme-option span {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-color);
}

/* Color Options */
.color-options {
    display: flex;
    gap: 12px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.color-option {
    cursor: pointer;
}

.color-option input {
    display: none;
}

.color-swatch {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 3px solid transparent;
    transition: var(--settings-transition);
}

.color-option input:checked + .color-swatch {
    border-color: white;
    box-shadow: 0 0 0 2px var(--settings-primary);
}

.color-swatch.blue {
    background: #3b82f6;
}

.color-swatch.purple {
    background: #8b5cf6;
}

.color-swatch.green {
    background: #10b981;
}

.color-swatch.orange {
    background: #f59e0b;
}

/* Action Items */
.action-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding: 20px 0;
    border-bottom: 1px solid var(--settings-card-border);
}

.action-item:last-child {
    border-bottom: none;
}

.action-info {
    flex: 1;
}

.action-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.action-info p {
    font-size: 14px;
    color: var(--text-color-muted);
    margin: 0;
    line-height: 1.4;
}

/* Connections List */
.connections-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.connection-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--dark-bg-3, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    gap: 16px;
}

.connection-item.disconnected {
    opacity: 0.7;
}

.connection-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.connection-logo {
    width: 40px;
    height: 40px;
    background: var(--settings-card-bg);
    border: 1px solid var(--settings-card-border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-color);
}

.connection-logo.crypto {
    background: #f7931a;
    color: white;
    border-color: #f7931a;
}

.connection-details h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.connection-details p {
    font-size: 12px;
    color: var(--text-color-muted);
    margin: 0;
}

/* Plan Details */
.plan-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.plan-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.plan-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1f2937;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.plan-badge.premium i {
    color: #d97706;
}

.plan-pricing h4 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 4px;
}

.plan-pricing p {
    font-size: 14px;
    color: var(--text-color-muted);
    margin: 0;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-color);
}

.plan-features li i {
    color: var(--settings-success);
    font-size: 12px;
}

/* Payment Methods */
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.payment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--dark-bg-3, #2d2d2d);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    gap: 16px;
}

.payment-item.primary {
    border-color: var(--settings-primary);
    background: rgba(99, 102, 241, 0.05);
}

.payment-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: var(--settings-card-bg);
    border: 1px solid var(--settings-card-border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--text-color);
}

.card-icon.paypal {
    background: #0070ba;
    color: white;
    border-color: #0070ba;
}

.card-details h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.card-details p {
    font-size: 12px;
    color: var(--text-color-muted);
    margin-bottom: 4px;
}

.primary-badge {
    background: var(--settings-success);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.payment-actions {
    display: flex;
    gap: 8px;
}

.add-payment {
    text-align: center;
    padding: 16px;
    border: 2px dashed var(--settings-card-border);
    border-radius: 8px;
    transition: var(--settings-transition);
}

.add-payment:hover {
    border-color: var(--settings-primary);
    background: rgba(99, 102, 241, 0.02);
}

/* Billing Table */
.billing-history {
    overflow-x: auto;
}

.billing-table {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
    min-width: 600px;
    gap: 1px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.table-header {
    display: contents;
}

.header-cell {
    background: var(--dark-bg-2, #1e1e1e);
    padding: 12px 16px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-color, #f5f5f5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-row {
    display: contents;
}

.table-cell {
    background: var(--dark-bg-3, #2d2d2d);
    padding: 16px;
    font-size: 14px;
    color: var(--text-color, #f5f5f5);
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background: var(--settings-success);
    color: white;
}

.status-badge.pending {
    background: var(--settings-warning);
    color: white;
}

.status-badge.failed {
    background: var(--settings-danger);
    color: white;
}

.table-actions {
    margin-top: 16px;
    text-align: center;
}

/* Legal Documents */
.legal-documents {
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--settings-card-border);
}

.legal-documents h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 12px;
}

.document-links {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.document-links li {
    font-size: 14px;
}

.document-links a {
    color: var(--settings-primary);
    text-decoration: none;
    font-weight: 500;
}

.document-links a:hover {
    text-decoration: underline;
}

/* API Section */
.api-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.api-info p {
    font-size: 14px;
    color: var(--text-color-muted);
    margin-bottom: 16px;
}

.api-keys {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.key-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--form-bg);
    border: 1px solid var(--form-border);
    border-radius: 8px;
    gap: 16px;
}

.key-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.key-info p {
    font-size: 12px;
    color: var(--text-color-muted);
    margin: 0;
}

.key-actions {
    display: flex;
    gap: 8px;
}

/* Webhooks */
.webhooks-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.webhooks-info p {
    font-size: 14px;
    color: var(--text-color-muted);
    margin-bottom: 16px;
}

.webhook-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.webhook-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--form-bg);
    border: 1px solid var(--form-border);
    border-radius: 8px;
    gap: 16px;
}

.webhook-info h4 {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
    word-break: break-all;
}

.webhook-info p {
    font-size: 12px;
    color: var(--text-color-muted);
    margin: 0;
}

.webhook-actions {
    display: flex;
    gap: 8px;
}

/* Responsive Breakpoints */

/* Tablet Styles */
@media (min-width: 768px) {
    .logo-text {
        display: block;
    }
    
    .user-profile {
        display: flex;
    }
    
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
    
    .profile-picture-section {
        flex-wrap: nowrap;
    }
    
    .theme-options {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .plan-details {
        flex-direction: row;
        align-items: center;
    }
    
    .plan-info {
        flex-wrap: nowrap;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .menu-toggle {
        display: none;
    }
    
    .settings-sidebar {
        position: static;
        left: 0;
        width: var(--desktop-sidebar-width);
        height: auto;
        min-height: calc(100vh - 60px);
    }
    
    .settings-content {
        margin-left: 0;
        padding: 32px 40px;
    }
    
    .section-header h1 {
        font-size: 32px;
    }
    
    .section-header p {
        font-size: 18px;
    }
    
    .settings-card {
        padding: 32px;
    }
    
    .settings-card h3 {
        font-size: 20px;
    }
    
    .form-actions {
        flex-wrap: nowrap;
    }
    
    .billing-table {
        grid-template-columns: 120px 1fr 120px 100px 80px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .settings-content {
        padding: 40px 48px;
    }
}

/* Mobile Overlay */
@media (max-width: 1023px) {
    .settings-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 40;
        opacity: 0;
        visibility: hidden;
        transition: var(--settings-transition);
    }
    
    .settings-overlay.active {
        opacity: 1;
        visibility: visible;
    }
}

/* Enhanced animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading-skeleton {
    background: linear-gradient(90deg, 
        rgba(99, 102, 241, 0.1) 25%, 
        rgba(99, 102, 241, 0.2) 50%, 
        rgba(99, 102, 241, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus accessibility */
.menu-item:focus,
.form-input:focus,
.form-select:focus,
.btn:focus,
.toggle-switch:focus-within {
    outline: 2px solid var(--settings-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .settings-sidebar,
    .header,
    .form-actions,
    .btn {
        display: none !important;
    }
    
    .settings-container {
        display: block !important;
    }
    
    .settings-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .settings-section {
        display: block !important;
    }
}