/* Mines Game Styles */

:root {
    --mines-primary: #4a8c3f;
    --mines-primary-light: #5aac4f;
    --mines-primary-dark: #2d5a27;
    --mines-secondary: #ffd700;
    --mines-secondary-light: #ffeb80;
    --mines-secondary-dark: #ffb700;
    --mines-danger: #dc3545;
    --mines-success: #28a745;
    --mines-warning: #f59e0b;
    --mines-info: #3498db;
    --cell-size-desktop: 50px;
    --cell-size-tablet: 40px;
    --cell-size-mobile: 32px;
    --cell-size-small-mobile: 28px;
}

/* Main Container */
.mines-container {
    padding: 15px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Dashboard Layout */
.game-dashboard {
    display: grid;
    grid-template-columns: minmax(280px, 320px) 1fr;
    gap: 20px;
    width: 100%;
}

/* Mobile Wallet (only shown on mobile) */
.mobile-wallet {
    display: none;
    background: linear-gradient(135deg, #1e1e1e, #2a2a2a);
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.wallet-balance {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--mines-secondary);
    font-weight: 600;
    font-size: 1.1rem;
}

.wallet-balance i {
    font-size: 1rem;
}

/* Header Styles */
.mines-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    margin-right: auto;
}

.back-link:hover {
    color: var(--mines-primary-light);
}

.game-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--mines-primary-dark), var(--mines-primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.view-toggle {
    display: flex;
    gap: 5px;
    margin-left: auto;
}

.view-btn {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: linear-gradient(135deg, var(--mines-primary-dark), var(--mines-primary));
    color: white;
    border-color: var(--mines-primary);
    box-shadow: 0 0 8px rgba(74, 140, 63, 0.5);
}

.view-btn:hover:not(.active) {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    color: var(--text-color);
}

/* Dashboard Left Column */
.dashboard-left {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #3a3a3a;
}

/* Game Stats */
.game-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    gap: 10px;
    margin-bottom: 5px;
}

.stat-item {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-radius: 8px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    border: 1px solid #444;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.wallet .stat-value {
    color: var(--mines-secondary);
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.3);
}

.multiplier .stat-value {
    color: var(--mines-primary-light);
    text-shadow: 0 0 5px rgba(90, 172, 79, 0.3);
}

.payout .stat-value {
    color: var(--mines-success);
    text-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
}

/* Betting Controls */
.betting-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.bet-amount-control {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bet-amount-control label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.bet-input-group {
    display: flex;
    align-items: center;
}

.bet-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bet-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.bet-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
    font-weight: 600;
}

/* Bet Presets */
.bet-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 10px;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.preset-btn.max {
    background: linear-gradient(135deg, #3d3d3d, #444);
    color: var(--mines-secondary);
    font-weight: 600;
}

/* Game Configuration */
.game-config {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.config-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-group label {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.config-select {
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    color: var(--text-color);
    font-size: 0.85rem;
    min-width: 80px;
}

.mines-count-control {
    display: flex;
    align-items: center;
}

.mines-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mines-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.mines-input {
    width: 50px;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 4px 8px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 0.9rem;
    text-align: center;
    font-weight: 600;
}

.mines-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.mines-preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 0;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 36px;
    text-align: center;
}

.mines-preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.mines-preset-btn.active {
    background: linear-gradient(135deg, var(--mines-primary-dark), var(--mines-primary));
    color: white;
    border-color: var(--mines-primary);
}

/* Game Actions */
.game-actions {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto auto;
    gap: 10px;
}

.action-mode {
    display: flex;
    gap: 5px;
    grid-column: 1 / -1;
}

.mode-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 8px 12px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    flex: 1;
}

.mode-btn.active {
    background: linear-gradient(135deg, var(--mines-primary-dark), var(--mines-primary));
    color: white;
    border-color: var(--mines-primary);
}

.mode-btn:hover:not(.active) {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.action-btn {
    padding: 10px 15px !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--mines-secondary-dark), var(--mines-secondary)) !important;
    color: #000 !important;
    border-color: var(--mines-secondary-dark) !important;
}

.btn-success {
    background: linear-gradient(135deg, #218838, #28a745) !important;
    color: white !important;
    border-color: #1e7e34 !important;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem !important;
    background: linear-gradient(135deg, #2a2a2a, #333333) !important;
    color: var(--text-secondary) !important;
    border: 1px solid #444 !important;
}

/* Dashboard Right Column */
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Status */
.game-status {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    padding: 12px 15px;
    border-radius: 10px;
    text-align: center;
    font-size: 1rem;
    color: var(--text-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    margin-bottom: 0;
    border: 1px solid #3d3d3d;
}

.game-status.win {
    background: linear-gradient(135deg, #1c5928, #28a745);
    color: white;
    border: 1px solid #28a745;
}

.game-status.loss {
    background: linear-gradient(135deg, #9c1f2d, #dc3545);
    color: white;
    border: 1px solid #dc3545;
}

/* Flags Container */
.flags-container {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

.flags-counter {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border: 1px solid #3d3d3d;
    border-radius: 20px;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
    color: var(--mines-warning);
}

/* Game Board */
.game-board {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    padding: 15px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    margin: 0 auto;
    max-width: 100%;
    width: 100%;
    aspect-ratio: 1;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #3d3d3d;
}

.cell {
    width: 100%;
    aspect-ratio: 1;
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    box-shadow: inset 0 -2px 0 rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid #3a3a3a;
    position: relative;
}

.cell::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0) 70%);
    opacity: 0;
    transition: opacity 0.2s ease;
    border-radius: 4px;
    pointer-events: none;
}

.cell:hover::before {
    opacity: 1;
}

.cell:hover:not(.revealed):not(.flagged) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: #5a5a5a;
}

.cell.revealed {
    background: linear-gradient(135deg, #2a2a2a, #323232);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    border-color: #3a3a3a;
    transform: none;
}

.cell.mine {
    background: linear-gradient(135deg, #9c1f2d, #dc3545);
    color: white;
    border-color: #dc3545;
}

.cell.flagged {
    background: linear-gradient(135deg, #333, #3d3d3d);
    color: var(--mines-warning);
    border-color: #4d4d4d;
}

.cell.safe-pick {
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.7);
    border-color: var(--mines-success);
}

.cell.potential-mine {
    box-shadow: 0 0 8px rgba(220, 53, 69, 0.7);
    border-color: var(--mines-danger);
}

/* Cell Number Colors */
.cell[data-mines="1"] {
    color: #3498db;
}

.cell[data-mines="2"] {
    color: #2ecc71;
}

.cell[data-mines="3"] {
    color: #e74c3c;
}

.cell[data-mines="4"] {
    color: #9b59b6;
}

.cell[data-mines="5"] {
    color: #f39c12;
}

.cell[data-mines="6"] {
    color: #1abc9c;
}

.cell[data-mines="7"] {
    color: #34495e;
}

.cell[data-mines="8"] {
    color: #7f8c8d;
}

/* Loading Placeholder */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    color: var(--text-secondary);
    gap: 15px;
    grid-column: 1 / -1;
}

.loading-placeholder i {
    font-size: 3rem;
    color: var(--mines-primary);
}

.loading-placeholder p {
    font-size: 1.1rem;
}

/* Mobile Controls (hidden on desktop) */
.mobile-controls {
    display: none;
    flex-direction: column;
    gap: 10px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #3d3d3d;
    margin-top: 15px;
}

.mobile-actions {
    display: flex;
    gap: 10px;
}

.mobile-btn {
    flex: 1;
    padding: 12px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.mobile-mode-toggle {
    display: flex;
    gap: 10px;
}

.mobile-mode-toggle .mode-btn {
    flex: 1;
    padding: 10px;
}

/* Game History */
.game-history {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.toggle-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    color: var(--text-color);
}

.history-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 0 15px 15px;
}

.history-table-container {
    width: 100%;
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    min-width: 600px;
}

.history-table th,
.history-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #3d3d3d;
}

.history-table th {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.history-table td {
    color: var(--text-color);
    font-size: 0.9rem;
}

.history-table .win {
    color: var(--mines-success);
}

.history-table .loss {
    color: var(--mines-danger);
}

/* Pro View Mode (activated via JavaScript) */
.pro-view .game-board {
    max-width: 100%;
}

.pro-view .cell {
    font-size: 0.9rem;
}

.pro-view .dashboard-left {
    position: sticky;
    top: 15px;
}

/* Animation for revealing cells */
@keyframes reveal {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.cell.revealed {
    animation: reveal 0.2s ease forwards;
}

/* Animation for multiplier increase */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.multiplier.pulse .stat-value {
    animation: pulse 0.5s ease;
}

/* Animation for win */
@keyframes win-glow {
    0% {
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.8);
    }
    100% {
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
}

.win-animation {
    animation: win-glow 1.5s infinite;
}

/* Animation for payout increase */
@keyframes highlight {
    0% {
        color: var(--mines-success);
    }
    50% {
        color: white;
    }
    100% {
        color: var(--mines-success);
    }
}

.highlight-payout {
    animation: highlight 0.8s ease;
}

/* RESPONSIVE STYLES */

/* Tablet View */
@media (max-width: 1024px) {
    .game-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .dashboard-left {
        max-width: 100%;
        width: 100%;
    }
    
    .game-stats {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .betting-controls, 
    .game-config, 
    .game-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .bet-amount-control,
    .bet-presets,
    .action-mode {
        grid-column: 1 / -1;
    }
    
    .cell {
        font-size: 1rem;
    }
    
    .game-board {
        max-width: 550px;
        margin: 0 auto;
    }
}

/* Mobile View */
@media (max-width: 768px) {
    .mines-container {
        padding: 10px;
    }
    
    .mines-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .back-link {
        margin-right: 0;
    }
    
    .game-title {
        font-size: 1.8rem;
        margin: 5px 0;
    }
    
    .view-toggle {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
        text-align: center;
    }
    
    .mobile-wallet {
        display: flex;
        justify-content: space-between;
    }
    
    .dashboard-left {
        padding: 12px;
    }
    
    .game-stats {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .stat-item {
        padding: 8px;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .bet-presets,
    .mines-presets {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }
    
    .mines-presets {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .preset-btn,
    .mines-preset-btn {
        font-size: 0.8rem;
        padding: 8px 0;
    }
    
    .game-board {
        padding: 10px;
    }
    
    .cell {
        font-size: 0.9rem;
    }
    
    /* Show mobile controls */
    .mobile-controls {
        display: flex;
    }
    
    /* Hide desktop controls on mobile */
    .dashboard-left .game-actions {
        display: none;
    }
    
    .history-title {
        padding: 12px 15px;
        font-size: 1.1rem;
    }
    
    .history-content {
        padding: 0 10px 10px;
    }
}

/* Small Mobile View */
@media (max-width: 480px) {
    .mines-container {
        padding: 8px;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-stats {
        gap: 5px;
    }
    
    .stat-label {
        font-size: 0.7rem;
    }
    
    .stat-value {
        font-size: 0.9rem;
    }
    
    .betting-controls, 
    .game-config {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    
    .bet-presets,
    .mines-presets {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }
    
    .bet-input-group,
    .mines-count-control {
        width: 100%;
    }
    
    .cell {
        font-size: 0.8rem;
    }
    
    .game-board {
        padding: 8px;
    }
    
    .game-status {
        font-size: 0.9rem;
        padding: 10px;
    }
    
    .flags-counter {
        font-size: 0.8rem;
        padding: 4px 12px;
    }
}

/* Portrait/Landscape Specific Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .game-dashboard {
        grid-template-columns: 320px 1fr;
    }
    
    .mobile-wallet {
        display: none;
    }
    
    .dashboard-left .game-actions {
        display: grid;
    }
    
    .mobile-controls {
        display: none;
    }
}

/* Pro View Mode Special Styling */
.pro-view-active .game-dashboard {
    grid-template-columns: 1fr;
}

.pro-view-active .dashboard-left {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.pro-view-active .betting-controls,
.pro-view-active .game-config {
    height: 100%;
}

.pro-view-active .game-board {
    max-width: 100%;
    margin-top: 0;
}

.pro-view-active .game-stats {
    grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
    .pro-view-active .dashboard-left {
        grid-template-columns: 1fr;
    }
    
    .pro-view-active .game-board {
        height: 350px;
    }
    
    .pro-view-active .cell {
        font-size: 0.8rem;
    }
}

/* Screen Rotation Support */
@media screen and (orientation: portrait) {
    .game-board {
        max-width: 100%;
        width: 100%;
        aspect-ratio: 1;
    }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
    .game-board {
        max-height: 70vh;
        aspect-ratio: 1;
    }
}