/* <PERSON><PERSON> Rush Game Styles */

:root {
    --dice-primary: #d4af37;
    --dice-primary-light: #f5cc7f;
    --dice-primary-dark: #b8941f;
    --dice-secondary: #6c5ce7;
    --dice-secondary-light: #a29bfe;
    --dice-success: #28a745;
    --dice-warning: #ffc107;
    --dice-danger: #dc3545;
    --dice-info: #17a2b8;
}

/* Main Container */
.dice-rush-container {
    padding: 15px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Dashboard Layout */
.game-dashboard {
    display: grid;
    grid-template-columns: minmax(280px, 320px) 1fr;
    gap: 20px;
    width: 100%;
}

/* Mobile Wallet (only shown on mobile) */
.mobile-wallet {
    display: none;
    background: linear-gradient(135deg, #1e1e1e, #2a2a2a);
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    justify-content: space-between;
    align-items: center;
}

.wallet-balance, .mobile-score {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--dice-primary);
    font-weight: 600;
    font-size: 1rem;
}

.wallet-balance i, .mobile-score i {
    font-size: 1rem;
}

/* Header Styles */
.dice-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    margin-right: auto;
}

.back-link:hover {
    color: var(--dice-primary-light);
}

.game-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--dice-primary-dark), var(--dice-primary-light));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.view-toggle {
    display: flex;
    gap: 5px;
    margin-left: auto;
}

.view-btn {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: linear-gradient(135deg, var(--dice-primary-dark), var(--dice-primary));
    color: white;
    border-color: var(--dice-primary);
    box-shadow: 0 0 8px rgba(212, 175, 55, 0.5);
}

.view-btn:hover:not(.active) {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    color: var(--text-color);
}

/* Dashboard Left Column */
.dashboard-left {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #3a3a3a;
}

/* Game Stats */
.game-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
    gap: 10px;
    margin-bottom: 5px;
}

.stat-item {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-radius: 8px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    border: 1px solid #444;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.balance .stat-value {
    color: var(--dice-primary);
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.3);
}

.score .stat-value {
    color: var(--dice-info);
    text-shadow: 0 0 5px rgba(23, 162, 184, 0.3);
}

.multiplier .stat-value {
    color: var(--dice-success);
    text-shadow: 0 0 5px rgba(40, 167, 69, 0.3);
}

.turns .stat-value {
    color: var(--dice-warning);
    text-shadow: 0 0 5px rgba(255, 193, 7, 0.3);
}

/* Betting Controls */
.betting-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.bet-amount-control {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bet-amount-control label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.bet-input-group {
    display: flex;
    align-items: center;
}

.bet-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bet-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.bet-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
    font-weight: 600;
}

/* Bet Presets */
.bet-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 10px;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.preset-btn.max {
    background: linear-gradient(135deg, #3d3d3d, #444);
    color: var(--dice-primary);
    font-weight: 600;
}

/* Game Configuration */
.game-config {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.config-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-group label {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.config-select {
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    color: var(--text-color);
    font-size: 0.85rem;
    min-width: 120px;
}

/* Game Actions */
.game-actions {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 10px;
}

.action-btn {
    padding: 10px 15px !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--dice-primary-dark), var(--dice-primary)) !important;
    color: #000 !important;
    border-color: var(--dice-primary-dark) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--dice-secondary), var(--dice-secondary-light)) !important;
    color: white !important;
    border-color: var(--dice-secondary) !important;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem !important;
    background: linear-gradient(135deg, #2a2a2a, #333333) !important;
    color: var(--text-secondary) !important;
    border: 1px solid #444 !important;
}

/* Dashboard Right Column */
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Status */
.game-status {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    padding: 12px 15px;
    border-radius: 10px;
    text-align: center;
    font-size: 1rem;
    color: var(--text-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    margin-bottom: 0;
    border: 1px solid #3d3d3d;
}

.game-status.success {
    background: linear-gradient(135deg, #1c5928, #28a745);
    color: white;
    border: 1px solid #28a745;
}

.game-status.warning {
    background: linear-gradient(135deg, #c69500, #ffc107);
    color: #000;
    border: 1px solid #ffc107;
}

.game-status.danger {
    background: linear-gradient(135deg, #9c1f2d, #dc3545);
    color: white;
    border: 1px solid #dc3545;
}

/* Progress Bar */
.progress-container {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #3d3d3d;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #333;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--dice-primary-dark), var(--dice-primary));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* Dice Table */
.dice-table {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    padding: 20px;
    margin: 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #3d3d3d;
    position: relative;
}

.dice-container {
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    perspective: 600px;
    flex-wrap: wrap;
}

.dice {
    width: 50px;
    height: 50px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 1.5s ease-out;
    cursor: pointer;
}

.dice-face {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--dice-primary), var(--dice-primary-light));
    border: 2px solid #ffffff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    font-weight: bold;
    color: #000;
    box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.2);
}

.dice-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: var(--text-secondary);
}

.dice-placeholder i {
    font-size: 2.5rem;
    color: var(--dice-primary);
}

.dice-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.roll-btn {
    background: linear-gradient(135deg, var(--dice-primary-dark), var(--dice-primary));
    color: #000;
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    font-size: 1rem;
}

.continuous-roll-btn {
    background: linear-gradient(135deg, var(--dice-secondary), var(--dice-secondary-light));
    color: white;
    border: none;
}

.rolls-counter {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-left: 10px;
}

/* Mobile Controls (hidden on desktop) */
.mobile-controls {
    display: none;
    flex-direction: column;
    gap: 10px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #3d3d3d;
    margin-top: 15px;
}

.mobile-actions, .mobile-quick-actions {
    display: flex;
    gap: 10px;
}

.mobile-btn {
    flex: 1;
    padding: 12px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

/* Target Cards */
.target-section {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.toggle-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    color: var(--text-color);
}

.target-content {
    padding: 15px;
}

.target-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.target-card {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #3a3a3a;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.target-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.target-card.active {
    border: 1px solid var(--dice-primary);
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(245, 204, 127, 0.1));
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
}

.target-card.selected {
    border: 2px solid var(--dice-primary);
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(245, 204, 127, 0.2));
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.4);
}

.target-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.target-desc {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
}

.target-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--dice-primary);
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
    margin-top: auto;
    align-self: flex-end;
}

/* Game Controls */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.control-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.2), rgba(23, 162, 184, 0.1));
    border: 1px solid var(--dice-info);
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 0.9rem;
    color: var(--text-color);
}

.control-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.btn-warning {
    background: linear-gradient(135deg, #e0a800, var(--dice-warning)) !important;
    color: #000 !important;
    border: none !important;
}

.btn-success {
    background: linear-gradient(135deg, #218838, var(--dice-success)) !important;
    color: white !important;
    border: none !important;
}

.btn-info {
    background: linear-gradient(135deg, #138496, var(--dice-info)) !important;
    color: white !important;
    border: none !important;
}

/* Game Statistics */
.game-statistics {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.stats-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.stats-content {
    padding: 15px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-card {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #3a3a3a;
    text-align: center;
}

.stat-card-title {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.stat-card-value {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--dice-primary);
}

/* Game History */
.game-history {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.history-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 0 15px 15px;
}

.history-table-container {
    width: 100%;
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    min-width: 600px;
}

.history-table th,
.history-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #3d3d3d;
}

.history-table th {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.history-table td {
    color: var(--text-color);
    font-size: 0.9rem;
}

.history-table .win {
    color: var(--dice-success);
}

.history-table .loss {
    color: var(--dice-danger);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
    animation: fadeIn 0.3s;
}

.modal-content {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    margin: 10% auto;
    padding: 25px;
    border: 1px solid #4a4a4a;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    animation: slideIn 0.3s;
}

.close-modal {
    color: var(--text-secondary);
    float: right;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-modal:hover {
    color: var(--dice-primary);
}

.rules-content {
    margin-top: 20px;
}

.rules-content h3 {
    margin-top: 20px;
    color: var(--dice-primary);
    font-size: 1.2rem;
}

.rules-content ul {
    padding-left: 20px;
}

.rules-content li {
    margin-bottom: 8px;
}

/* Win/Loss Indicator */
.win-loss-indicator {
    position: relative;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    overflow: hidden;
    min-width: 80px;
}

.win-indicator, .loss-indicator {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 5px;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
    position: absolute;
    white-space: nowrap;
}

.win-indicator {
    background: linear-gradient(135deg, var(--dice-success), #34ce57);
    color: white;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.loss-indicator {
    background: linear-gradient(135deg, var(--dice-danger), #e35d6a);
    color: white;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

/* Animations */
@keyframes rollDice {
    0% { transform: rotateX(0) rotateY(0) rotateZ(0); }
    100% { transform: rotateX(360deg) rotateY(720deg) rotateZ(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes flyIn {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Dice face positioning */
.dice-face:nth-child(1) { transform: rotateY(0deg) translateZ(25px); }
.dice-face:nth-child(2) { transform: rotateY(180deg) translateZ(25px); }
.dice-face:nth-child(3) { transform: rotateY(90deg) translateZ(25px); }
.dice-face:nth-child(4) { transform: rotateY(-90deg) translateZ(25px); }
.dice-face:nth-child(5) { transform: rotateX(90deg) translateZ(25px); }
.dice-face:nth-child(6) { transform: rotateX(-90deg) translateZ(25px); }

/* Pro View Mode Special Styling */
.pro-view-active .game-dashboard {
    grid-template-columns: 1fr;
}

.pro-view-active .dashboard-left {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.pro-view-active .betting-controls,
.pro-view-active .game-config {
    height: 100%;
}

.pro-view-active .game-statistics,
.pro-view-active .game-history {
    display: block;
}

/* RESPONSIVE STYLES */

/* Tablet View */
@media (max-width: 1024px) {
    .game-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .dashboard-left {
        max-width: 100%;
        width: 100%;
    }
    
    .game-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .betting-controls, 
    .game-config, 
    .game-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .bet-amount-control,
    .bet-presets {
        grid-column: 1 / -1;
    }
    
    .dice {
        width: 45px;
        height: 45px;
    }
    
    .dice-face {
        font-size: 1.2rem;
    }
    
    .target-cards {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

/* Mobile View */
@media (max-width: 768px) {
    .dice-rush-container {
        padding: 10px;
    }
    
    .dice-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .back-link {
        margin-right: 0;
    }
    
    .game-title {
        font-size: 1.8rem;
        margin: 5px 0;
    }
    
    .view-toggle {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
        text-align: center;
    }
    
    .mobile-wallet {
        display: flex;
    }
    
    .dashboard-left {
        padding: 12px;
    }
    
    .game-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-item {
        padding: 8px;
    }
    
    .stat-value {
        font-size: 1rem;
    }
    
    .bet-presets {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }
    
    .preset-btn {
        font-size: 0.8rem;
        padding: 8px 0;
    }
    
    .dice-table {
        padding: 15px;
    }
    
    .dice {
        width: 40px;
        height: 40px;
    }
    
    .dice-face {
        font-size: 1rem;
    }
    
    .dice-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .target-cards {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .control-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .control-buttons .action-btn {
        width: 100%;
    }
    
    /* Show mobile controls */
    .mobile-controls {
        display: flex;
    }
    
    /* Hide desktop controls on mobile */
    .dashboard-left .game-actions {
        display: none;
    }
    
    .game-controls .control-buttons {
        display: none;
    }
    
    .dice-controls {
        display: none;
    }
    
    .win-loss-indicator {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .section-title, .stats-title, .history-title {
        padding: 12px 15px;
        font-size: 1.1rem;
    }
    
    .target-content, .stats-content, .history-content {
        padding: 0 10px 10px;
    }
}

/* Small Mobile View */
@media (max-width: 480px) {
    .dice-rush-container {
        padding: 8px;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-stats {
        gap: 5px;
    }
    
    .stat-label {
        font-size: 0.7rem;
    }
    
    .stat-value {
        font-size: 0.9rem;
    }
    
    .betting-controls, 
    .game-config {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    
    .bet-presets {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }
    
    .bet-input-group {
        width: 100%;
    }
    
    .dice {
        width: 35px;
        height: 35px;
    }
    
    .dice-face {
        font-size: 0.9rem;
    }
    
    .dice-table {
        padding: 10px;
    }
    
    .game-status {
        font-size: 0.9rem;
        padding: 10px;
    }
    
    .progress-container {
        padding: 8px;
    }
    
    .target-cards {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* Portrait/Landscape Specific Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .game-dashboard {
        grid-template-columns: 320px 1fr;
    }
    
    .mobile-wallet {
        display: none;
    }
    
    .dashboard-left .game-actions {
        display: grid;
    }
    
    .mobile-controls {
        display: none;
    }
    
    .dice-controls {
        display: flex;
    }
    
    .game-controls .control-buttons {
        display: flex;
    }
}

/* Pro View Mode Special Mobile Styling */
@media (max-width: 768px) {
    .pro-view-active .dashboard-left {
        grid-template-columns: 1fr;
    }
    
    .pro-view-active .dice-table {
        margin-top: 0;
    }
    
    .pro-view-active .dice {
        width: 35px;
        height: 35px;
    }
    
    .pro-view-active .dice-face {
        font-size: 0.9rem;
    }
}

/* Screen Rotation Support */
@media screen and (orientation: portrait) {
    .dice-table {
        max-width: 100%;
        width: 100%;
    }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
    .dice-table {
        max-height: 60vh;
    }
    
    .target-section, .game-statistics, .game-history {
        max-height: 50vh;
        overflow-y: auto;
    }
}