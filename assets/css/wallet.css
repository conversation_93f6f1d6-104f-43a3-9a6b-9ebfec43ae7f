/* Enhanced Wallet Page - Mobile-First Responsive Design */
:root {
    /* Wallet-specific color palette */
    --wallet-primary: #00c853;
    --wallet-primary-hover: #00a845;
    --wallet-secondary: #4caf50;
    --wallet-accent: #81c784;
    --wallet-success: #4caf50;
    --wallet-warning: #ff9800;
    --wallet-danger: #f44336;
    --wallet-info: #2196f3;
    
    /* Balance colors */
    --balance-bg: linear-gradient(135deg, #00c853, #4caf50);
    --balance-shadow: rgba(0, 200, 83, 0.3);
    
    /* Transaction colors */
    --transaction-incoming: #4caf50;
    --transaction-outgoing: #f44336;
    --transaction-pending: #ff9800;
    --transaction-completed: #4caf50;
    --transaction-failed: #f44336;
    
    /* Security colors */
    --security-bg: rgba(76, 175, 80, 0.1);
    --security-border: rgba(76, 175, 80, 0.3);
    
    /* Mobile-specific variables */
    --mobile-wallet-padding: 16px;
    --mobile-wallet-gap: 12px;
    --mobile-action-size: 70px;
    --mobile-transaction-height: 80px;
}

/* Mobile-first wallet page layout */
.wallet-page {
    padding: var(--mobile-wallet-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
    max-width: 1400px;
    margin: 0 auto;
}

/* Enhanced Wallet Header */
.wallet-header {
    margin-bottom: 20px;
}

.wallet-title {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--wallet-primary), var(--wallet-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 12px;
}

.wallet-title::before {
    content: '💰';
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Enhanced Balance Card */
.balance-card {
    background: var(--balance-bg);
    border-radius: var(--mobile-border-radius);
    padding: 24px var(--mobile-wallet-padding);
    margin-bottom: 24px;
    box-shadow: 0 8px 32px var(--balance-shadow);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(-45deg);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(-45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(-45deg); }
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.balance-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.balance-visibility {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.balance-visibility:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.balance-amount {
    font-size: 36px;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.balance-amount.hidden {
    filter: blur(8px);
    user-select: none;
}

.balance-usd {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    position: relative;
    z-index: 2;
}

/* Enhanced Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(var(--mobile-action-size), 1fr));
    gap: var(--mobile-wallet-gap);
    margin-bottom: 32px;
    padding: 0 4px;
}

.quick-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 8px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-decoration: none;
    color: var(--text-color);
    position: relative;
    overflow: hidden;
}

.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.quick-action:hover::before {
    left: 100%;
}

.quick-action:hover {
    background: var(--glass-hover);
    border-color: var(--wallet-primary);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 200, 83, 0.2);
}

.quick-action:active {
    transform: translateY(0) scale(0.98);
}

.quick-action-icon {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, var(--wallet-primary), var(--wallet-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(0, 200, 83, 0.3);
    transition: var(--transition);
}

.quick-action:hover .quick-action-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 200, 83, 0.4);
}

.quick-action-text {
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    color: var(--text-color);
    line-height: 1.2;
}

/* Enhanced Currency Selector */
.currency-selector {
    margin-bottom: 32px;
}

.currency-selector-header {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.currency-selector-header::before {
    content: '💱';
    font-size: 16px;
}

.currency-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.currency-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.currency-item.active {
    background: rgba(0, 200, 83, 0.1);
    border-color: var(--wallet-primary);
    box-shadow: 0 4px 15px rgba(0, 200, 83, 0.2);
}

.currency-item.coming-soon {
    opacity: 0.6;
    cursor: not-allowed;
    position: relative;
}

.currency-item.coming-soon::after {
    content: 'Coming Soon';
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%);
    font-size: 10px;
    background: var(--wallet-warning);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.currency-item:hover:not(.coming-soon) {
    background: var(--glass-hover);
    border-color: var(--wallet-primary);
    transform: translateY(-1px);
}

.currency-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--wallet-primary), var(--wallet-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    font-weight: 700;
    box-shadow: 0 2px 10px rgba(0, 200, 83, 0.3);
}

.currency-info {
    flex: 1;
}

.currency-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.currency-balance {
    font-size: 14px;
    color: var(--text-color-muted);
    font-weight: 500;
}

/* Enhanced Notifications Section */
.notifications-section {
    margin-bottom: 32px;
}

.notifications-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.notifications-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.notifications-title::before {
    content: '🔔';
    font-size: 16px;
}

.clear-notifications {
    background: none;
    border: none;
    color: var(--text-color-muted);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.clear-notifications:hover {
    color: var(--wallet-danger);
    background: rgba(244, 67, 54, 0.1);
}

.notification {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    margin-bottom: 8px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    position: relative;
}

.notification:hover {
    background: var(--glass-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.notification.success {
    border-left: 4px solid var(--wallet-success);
}

.notification.warning {
    border-left: 4px solid var(--wallet-warning);
}

.notification.error {
    border-left: 4px solid var(--wallet-danger);
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.notification.success .notification-icon {
    background: rgba(76, 175, 80, 0.2);
    color: var(--wallet-success);
}

.notification.warning .notification-icon {
    background: rgba(255, 152, 0, 0.2);
    color: var(--wallet-warning);
}

.notification:not(.success):not(.warning) .notification-icon {
    background: rgba(33, 150, 243, 0.2);
    color: var(--wallet-info);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.notification-text {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.4;
}

.notification-time {
    font-size: 11px;
    color: var(--text-color-muted);
    margin-top: 8px;
    align-self: flex-start;
}

/* Enhanced Transaction History */
.transaction-history {
    margin-bottom: 32px;
}

.transaction-header {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.transaction-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.transaction-title::before {
    content: '📊';
    font-size: 16px;
}

.transaction-filters {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-select {
    flex: 1;
    min-width: 100px;
    height: 36px;
    padding: 0 8px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a0a0a0' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 6px center;
    background-repeat: no-repeat;
    background-size: 12px;
    padding-right: 24px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--wallet-primary);
    box-shadow: 0 0 0 2px rgba(0, 200, 83, 0.2);
}

.transaction-search {
    position: relative;
    margin-bottom: 16px;
}

.transaction-search-input {
    width: 100%;
    height: 44px;
    padding: 0 16px 0 44px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    color: var(--text-color);
    font-size: 14px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.transaction-search-input::placeholder {
    color: var(--text-color-muted);
}

.transaction-search-input:focus {
    outline: none;
    border-color: var(--wallet-primary);
    box-shadow: 0 0 0 2px rgba(0, 200, 83, 0.2);
}

.transaction-search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-muted);
    font-size: 16px;
    pointer-events: none;
}

.transaction-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.transaction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    cursor: pointer;
    min-height: var(--mobile-transaction-height);
}

.transaction-item:hover {
    background: var(--glass-hover);
    border-color: var(--wallet-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.transaction-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.transaction-icon.incoming {
    background: linear-gradient(135deg, var(--transaction-incoming), #66bb6a);
}

.transaction-icon.outgoing {
    background: linear-gradient(135deg, var(--transaction-outgoing), #ef5350);
}

.transaction-details {
    flex: 1;
    min-width: 0;
}

.transaction-type {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.transaction-desc {
    font-size: 12px;
    color: var(--text-color-muted);
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.transaction-date {
    font-size: 11px;
    color: var(--text-color-muted);
}

.transaction-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}

.transaction-amount {
    font-size: 14px;
    font-weight: 700;
}

.transaction-amount.incoming {
    color: var(--transaction-incoming);
}

.transaction-amount.outgoing {
    color: var(--transaction-outgoing);
}

.transaction-status {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.transaction-status.completed {
    background: rgba(76, 175, 80, 0.2);
    color: var(--transaction-completed);
}

.transaction-status.pending {
    background: rgba(255, 152, 0, 0.2);
    color: var(--transaction-pending);
}

.transaction-status.failed {
    background: rgba(244, 67, 54, 0.2);
    color: var(--transaction-failed);
}

/* Enhanced Payment Methods */
.payment-methods {
    margin-bottom: 32px;
}

.payment-methods-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.payment-methods-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-methods-title::before {
    content: '💳';
    font-size: 16px;
}

.add-payment-method {
    background: var(--wallet-primary);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.add-payment-method:hover {
    background: var(--wallet-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 200, 83, 0.3);
}

.payment-method {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    margin-bottom: 8px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.payment-method:hover {
    background: var(--glass-hover);
    border-color: var(--wallet-primary);
    transform: translateY(-1px);
}

.payment-method-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.payment-method-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--wallet-info), #42a5f5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.payment-method-info {
    flex: 1;
}

.payment-method-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.payment-method-details {
    font-size: 12px;
    color: var(--text-color-muted);
}

.payment-method-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.payment-method-btn {
    padding: 6px 12px;
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    background: transparent;
    color: var(--text-color);
    font-size: 11px;
    cursor: pointer;
    transition: var(--transition);
}

.payment-method-btn:hover {
    background: var(--wallet-primary);
    border-color: var(--wallet-primary);
    color: white;
}

.payment-method-btn.remove:hover {
    background: var(--wallet-danger);
    border-color: var(--wallet-danger);
}

/* Enhanced Security Section */
.security-section {
    margin-bottom: 32px;
}

.security-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-title::before {
    content: '🔒';
    font-size: 16px;
}

.security-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: var(--security-bg);
    border: 1px solid var(--security-border);
    border-radius: var(--mobile-border-radius);
    margin-bottom: 8px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.security-item:hover {
    background: rgba(76, 175, 80, 0.15);
    border-color: var(--wallet-primary);
    transform: translateY(-1px);
}

.security-item-left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.security-item-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--wallet-primary), var(--wallet-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.security-item-info {
    flex: 1;
}

.security-item-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.security-item-desc {
    font-size: 12px;
    color: var(--text-color-muted);
    line-height: 1.3;
}

.security-toggle {
    width: 48px;
    height: 24px;
    background: var(--glass-border);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    flex-shrink: 0;
}

.security-toggle::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.security-toggle.active {
    background: var(--wallet-primary);
}

.security-toggle.active::before {
    transform: translateX(24px);
}

/* Responsive Breakpoints */

/* Tablet Styles */
@media (min-width: 768px) {
    .wallet-page {
        padding: 20px;
    }
    
    .wallet-title {
        font-size: 28px;
    }
    
    .balance-card {
        padding: 32px 24px;
    }
    
    .balance-amount {
        font-size: 42px;
    }
    
    .quick-actions {
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
    }
    
    .quick-action {
        padding: 20px 12px;
    }
    
    .quick-action-icon {
        width: 52px;
        height: 52px;
        font-size: 20px;
    }
    
    .quick-action-text {
        font-size: 13px;
    }
    
    .currency-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .transaction-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .transaction-filters {
        flex-wrap: nowrap;
        gap: 12px;
    }
    
    .payment-method-actions {
        flex-wrap: nowrap;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .wallet-page {
        padding: 2rem;
        display: grid;
        grid-template-columns: 1fr 350px;
        gap: 2rem;
        align-items: start;
    }
    
    .wallet-main {
        order: 1;
    }
    
    .wallet-sidebar {
        order: 2;
    }
    
    .wallet-title {
        font-size: 32px;
    }
    
    .balance-card {
        padding: 40px 32px;
    }
    
    .balance-amount {
        font-size: 48px;
    }
    
    .quick-actions {
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
    }
    
    .currency-list {
        grid-template-columns: 1fr;
    }
    
    .transaction-item {
        padding: 20px;
    }
    
    .payment-method {
        padding: 20px;
    }
    
    .security-item {
        padding: 20px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .wallet-page {
        grid-template-columns: 1fr 400px;
        gap: 2.5rem;
    }
}

/* Enhanced animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading animations */
.loading-skeleton {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.1) 25%, 
        rgba(255, 255, 255, 0.2) 50%, 
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Custom scrollbar */
.transaction-list::-webkit-scrollbar {
    width: 4px;
}

.transaction-list::-webkit-scrollbar-track {
    background: transparent;
}

.transaction-list::-webkit-scrollbar-thumb {
    background: var(--wallet-primary);
    border-radius: 2px;
}

/* High DPI support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .quick-action-icon,
    .currency-icon,
    .payment-method-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .balance-card::before {
        animation: none !important;
    }
}

/* Focus accessibility */
.quick-action:focus,
.currency-item:focus,
.transaction-item:focus,
.payment-method-btn:focus,
.security-toggle:focus {
    outline: 2px solid var(--wallet-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .quick-actions,
    .security-section,
    .payment-methods {
        display: none !important;
    }
    
    .wallet-page {
        display: block !important;
        grid-template-columns: none !important;
    }
}