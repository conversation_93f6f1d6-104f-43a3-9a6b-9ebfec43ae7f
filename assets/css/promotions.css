/* Enhanced Promotions Page - Mobile-First Responsive Design */
:root {
    /* Promotions-specific color palette */
    --promo-primary: #ff6b35;
    --promo-primary-hover: #e55a2b;
    --promo-secondary: #ff8c42;
    --promo-accent: #ffa726;
    --promo-gold: #ffd700;
    --promo-silver: #c0c0c0;
    --promo-bronze: #cd7f32;
    --promo-platinum: #e5e4e2;
    
    /* Promotional colors */
    --promo-success: #4caf50;
    --promo-warning: #ff9800;
    --promo-danger: #f44336;
    --promo-info: #2196f3;
    --promo-coming-soon: #9c27b0;
    
    /* Card backgrounds */
    --promo-card-bg: rgba(255, 255, 255, 0.05);
    --promo-card-border: rgba(255, 255, 255, 0.1);
    --promo-card-hover: rgba(255, 255, 255, 0.08);
    
    /* Featured promotions */
    --featured-card-shadow: rgba(255, 107, 53, 0.3);
    --featured-overlay: rgba(0, 0, 0, 0.6);
    
    /* Mobile-specific variables */
    --mobile-promo-padding: 16px;
    --mobile-promo-gap: 12px;
    --mobile-card-height: 280px;
    --mobile-hero-height: 200px;
}

/* Mobile-first promotions page layout */
.promotions-page {
    padding: var(--mobile-promo-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
    max-width: 1400px;
    margin: 0 auto;
}

/* Enhanced Promotions Hero */
.promotions-hero {
    background: linear-gradient(135deg, var(--promo-primary), var(--promo-secondary));
    border-radius: var(--mobile-border-radius);
    padding: 32px var(--mobile-promo-padding);
    margin-bottom: 32px;
    position: relative;
    overflow: hidden;
    min-height: var(--mobile-hero-height);
    display: flex;
    align-items: center;
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
}

.promotions-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: heroShimmer 4s infinite;
    pointer-events: none;
}

@keyframes heroShimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    text-align: center;
}

.hero-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin-bottom: 24px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.coming-soon-banner {
    display: flex;
    align-items: center;
    gap: 16px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--mobile-border-radius);
    padding: 16px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.coming-soon-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
}

.coming-soon-text h2 {
    font-size: 16px;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
}

.coming-soon-text p {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    margin: 0;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.cta-secondary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 12px 24px;
    border-radius: var(--mobile-border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.cta-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

/* Section Styling */
.section-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-icon {
    color: var(--promo-primary);
    font-size: 18px;
}

/* Enhanced Featured Promotions */
.featured-promotions {
    margin-bottom: 40px;
}

.featured-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--mobile-promo-gap);
}

.featured-card {
    background: var(--promo-card-bg);
    border: 1px solid var(--promo-card-border);
    border-radius: var(--mobile-border-radius);
    overflow: hidden;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    position: relative;
    min-height: var(--mobile-card-height);
}

.featured-card:hover {
    background: var(--promo-card-hover);
    border-color: var(--promo-primary);
    transform: translateY(-4px);
    box-shadow: 0 8px 32px var(--featured-card-shadow);
}

.card-image {
    height: 120px;
    background-size: cover;
    background-position: center;
    position: relative;
    overflow: hidden;
}

.card-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--featured-overlay);
    z-index: 1;
}

.card-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--promo-coming-soon);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(156, 39, 176, 0.3);
}

.card-content {
    padding: 16px;
}

.card-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
}

.card-description {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.4;
    margin-bottom: 12px;
}

.card-value {
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--promo-primary), var(--promo-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
}

.card-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 16px;
}

.card-detail {
    background: rgba(255, 255, 255, 0.05);
    padding: 8px;
    border-radius: var(--border-radius);
    text-align: center;
}

.card-detail-label {
    display: block;
    font-size: 10px;
    color: var(--text-color-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.card-detail-value {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-color);
}

.card-actions {
    display: flex;
    gap: 8px;
}

.card-button {
    flex: 1;
    padding: 10px 16px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-button.primary {
    background: var(--promo-primary);
    color: white;
}

.card-button.primary:hover {
    background: var(--promo-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.card-button.secondary {
    background: transparent;
    border: 1px solid var(--promo-primary);
    color: var(--promo-primary);
}

.card-button.secondary:hover {
    background: var(--promo-primary);
    color: white;
    transform: translateY(-1px);
}

/* Enhanced Categories Section */
.categories-section {
    margin-bottom: 40px;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--mobile-promo-gap);
}

.category-card {
    background: var(--promo-card-bg);
    border: 1px solid var(--promo-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 20px 16px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.category-card:hover {
    background: var(--promo-card-hover);
    border-color: var(--promo-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
}

.category-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--promo-primary), var(--promo-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin: 0 auto 12px;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.category-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.category-count {
    font-size: 12px;
    color: var(--text-color-muted);
    margin: 0;
}

/* Enhanced Regular Promotions */
.regular-promotions {
    margin-bottom: 40px;
}

.promotions-filters {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding: 4px 0;
    scrollbar-width: none;
}

.promotions-filters::-webkit-scrollbar {
    display: none;
}

.filter-button {
    flex: 0 0 auto;
    padding: 8px 16px;
    background: var(--promo-card-bg);
    border: 1px solid var(--promo-card-border);
    border-radius: 20px;
    color: var(--text-color);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    backdrop-filter: blur(10px);
}

.filter-button.active,
.filter-button:hover {
    background: var(--promo-primary);
    border-color: var(--promo-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.promotions-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.promotion-card {
    background: var(--promo-card-bg);
    border: 1px solid var(--promo-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.promotion-card:hover {
    background: var(--promo-card-hover);
    border-color: var(--promo-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
}

.promotion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    flex-wrap: wrap;
    gap: 8px;
}

.promotion-type {
    background: var(--promo-primary);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.promotion-value {
    font-size: 16px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--promo-primary), var(--promo-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.promotion-content {
    margin-bottom: 16px;
}

.promotion-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
}

.promotion-description {
    font-size: 14px;
    color: var(--text-color);
    line-height: 1.5;
    margin-bottom: 8px;
}

.promotion-terms {
    font-size: 12px;
    color: var(--text-color-muted);
    line-height: 1.4;
    font-style: italic;
}

.promotion-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
}

.promotion-expiry {
    font-size: 12px;
    color: var(--text-color-muted);
}

.promotion-expiry.coming-soon {
    color: var(--promo-coming-soon);
    font-weight: 600;
}

.promotion-button {
    padding: 10px 20px;
    background: var(--promo-primary);
    border: none;
    border-radius: var(--border-radius);
    color: white;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
}

.promotion-button:hover {
    background: var(--promo-primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.promotion-button.disabled {
    background: var(--text-color-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.promotion-button.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Enhanced Loyalty Section */
.loyalty-section {
    background: var(--promo-card-bg);
    border: 1px solid var(--promo-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 24px var(--mobile-promo-padding);
    margin-bottom: 40px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.loyalty-title {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--promo-gold), var(--promo-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 12px;
}

.loyalty-description {
    font-size: 14px;
    color: var(--text-color-muted);
    line-height: 1.5;
    margin-bottom: 24px;
}

.loyalty-tiers {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
}

.loyalty-tier {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    transition: var(--transition);
}

.loyalty-tier.active {
    border-color: var(--promo-gold);
    background: rgba(255, 215, 0, 0.1);
    transform: scale(1.02);
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.2);
}

.tier-name {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
}

.loyalty-tier:nth-child(1) .tier-name {
    color: var(--promo-bronze);
}

.loyalty-tier:nth-child(2) .tier-name {
    color: var(--promo-silver);
}

.loyalty-tier:nth-child(3) .tier-name {
    color: var(--promo-gold);
}

.loyalty-tier:nth-child(4) .tier-name {
    color: var(--promo-platinum);
}

.tier-requirement {
    font-size: 12px;
    color: var(--text-color-muted);
    margin-bottom: 12px;
    font-weight: 600;
}

.tier-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tier-benefits li {
    font-size: 12px;
    color: var(--text-color);
    padding: 4px 0;
    position: relative;
    padding-left: 16px;
}

.tier-benefits li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--promo-success);
    font-weight: bold;
}

.cta-button {
    background: linear-gradient(135deg, var(--promo-primary), var(--promo-secondary));
    border: none;
    color: white;
    padding: 16px 32px;
    border-radius: var(--mobile-border-radius);
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);
}

/* Enhanced Terms Section */
.terms-section {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    margin-bottom: 40px;
}

.terms-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.terms-title::before {
    content: '📋';
    font-size: 14px;
}

.terms-content p {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.5;
    margin-bottom: 12px;
}

.terms-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.terms-list li {
    font-size: 12px;
    color: var(--text-color-muted);
    line-height: 1.5;
    padding: 4px 0;
    position: relative;
    padding-left: 16px;
}

.terms-list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--promo-primary);
    font-weight: bold;
}

/* Responsive Breakpoints */

/* Tablet Styles */
@media (min-width: 768px) {
    .promotions-page {
        padding: 20px;
    }
    
    .promotions-hero {
        padding: 40px 32px;
        min-height: 240px;
    }
    
    .hero-title {
        font-size: 32px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .featured-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
    }
    
    .card-details {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .loyalty-tiers {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .promotion-header {
        flex-wrap: nowrap;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .promotions-page {
        padding: 2rem;
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 2rem;
        align-items: start;
    }
    
    .promotions-main {
        order: 1;
    }
    
    .promotions-sidebar {
        order: 2;
    }
    
    .promotions-hero {
        padding: 60px 40px;
        min-height: 300px;
    }
    
    .hero-title {
        font-size: 38px;
    }
    
    .hero-subtitle {
        font-size: 18px;
    }
    
    .featured-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 20px;
    }
    
    .category-card {
        padding: 24px 20px;
    }
    
    .loyalty-tiers {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .loyalty-tier {
        padding: 24px;
    }
    
    .promotion-card {
        padding: 24px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .promotions-page {
        grid-template-columns: 1fr 350px;
        gap: 2.5rem;
    }
}

/* Enhanced animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Loading animations */
.loading-skeleton {
    background: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.1) 25%, 
        rgba(255, 255, 255, 0.2) 50%, 
        rgba(255, 255, 255, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Custom scrollbar */
.promotions-filters::-webkit-scrollbar {
    display: none;
}

/* High DPI support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .category-icon,
    .card-badge {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .promotions-hero::before {
        animation: none !important;
    }
}

/* Focus accessibility */
.filter-button:focus,
.card-button:focus,
.promotion-button:focus,
.category-card:focus,
.cta-button:focus {
    outline: 2px solid var(--promo-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .promotions-hero,
    .hero-cta,
    .card-actions,
    .promotion-footer {
        display: none !important;
    }
    
    .promotions-page {
        display: block !important;
        grid-template-columns: none !important;
    }
}