/* Enhanced Support Page - Mobile-First Responsive Design */
:root {
    /* Support-specific color palette */
    --support-primary: #2196f3;
    --support-primary-hover: #1976d2;
    --support-secondary: #03a9f4;
    --support-accent: #00bcd4;
    --support-success: #4caf50;
    --support-warning: #ff9800;
    --support-danger: #f44336;
    --support-info: #2196f3;
    
    /* Chat colors */
    --chat-primary: #2196f3;
    --chat-user-bg: #e3f2fd;
    --chat-bot-bg: rgba(255, 255, 255, 0.1);
    --chat-border: rgba(255, 255, 255, 0.1);
    
    /* Card backgrounds */
    --support-card-bg: rgba(255, 255, 255, 0.05);
    --support-card-border: rgba(255, 255, 255, 0.1);
    --support-card-hover: rgba(255, 255, 255, 0.08);
    
    /* FAQ colors */
    --faq-question-bg: rgba(33, 150, 243, 0.1);
    --faq-answer-bg: rgba(255, 255, 255, 0.05);
    
    /* Mobile-specific variables */
    --mobile-support-padding: 16px;
    --mobile-support-gap: 12px;
    --mobile-hero-height: 200px;
    --mobile-card-height: 240px;
}

/* Mobile-first support page layout */
.support-page {
    padding: var(--mobile-support-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
    max-width: 1400px;
    margin: 0 auto;
}

/* Enhanced Support Hero */
.support-hero {
    background: linear-gradient(135deg, var(--support-primary), var(--support-secondary));
    border-radius: var(--mobile-border-radius);
    padding: 32px var(--mobile-support-padding);
    margin-bottom: 32px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: var(--mobile-hero-height);
    display: flex;
    align-items: center;
    box-shadow: 0 8px 32px rgba(33, 150, 243, 0.3);
}

.support-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: heroShimmer 4s infinite;
    pointer-events: none;
}

@keyframes heroShimmer {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
}

.hero-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin-bottom: 24px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.search-wrapper {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
    display: flex;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--mobile-border-radius);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.search-wrapper .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    z-index: 2;
}

.support-search {
    flex: 1;
    background: transparent;
    border: none;
    padding: 12px 16px 12px 40px;
    color: white;
    font-size: 14px;
    outline: none;
}

.support-search::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.search-button:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Section Styling */
.section-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-icon {
    color: var(--support-primary);
    font-size: 18px;
}

/* Enhanced Quick Links */
.quick-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--mobile-support-gap);
    margin-bottom: 32px;
}

.quick-link {
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.quick-link:hover {
    background: var(--support-card-hover);
    border-color: var(--support-primary);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

.quick-link-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--support-primary), var(--support-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin: 0 auto 12px;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.quick-link-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 6px;
}

.quick-link-description {
    font-size: 12px;
    color: var(--text-color-muted);
    line-height: 1.4;
    margin: 0;
}

/* Enhanced Support Categories */
.support-categories {
    margin-bottom: 40px;
}

.categories-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.category-card {
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.category-card:hover {
    background: var(--support-card-hover);
    border-color: var(--support-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
}

.category-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--support-primary), var(--support-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.category-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.category-description {
    font-size: 14px;
    color: var(--text-color-muted);
    line-height: 1.5;
    margin-bottom: 16px;
}

.category-articles {
    list-style: none;
    padding: 0;
    margin: 0 0 16px 0;
}

.category-article {
    margin-bottom: 8px;
}

.category-article a {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 13px;
    padding: 8px 0;
    transition: var(--transition);
}

.category-article a:hover {
    color: var(--support-primary);
    padding-left: 8px;
}

.article-icon {
    color: var(--support-primary);
    font-size: 12px;
    flex-shrink: 0;
}

.see-all-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--support-primary);
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    transition: var(--transition);
}

.see-all-link:hover {
    gap: 12px;
}

.see-all-icon {
    transition: var(--transition);
}

/* Enhanced Contact Methods */
.contact-methods {
    margin-bottom: 40px;
}

.methods-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.contact-card {
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 24px;
    text-align: center;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.contact-card:hover {
    background: var(--support-card-hover);
    border-color: var(--support-primary);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
}

.contact-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--support-primary), var(--support-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    margin: 0 auto 16px;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.contact-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
}

.contact-description {
    font-size: 14px;
    color: var(--text-color-muted);
    line-height: 1.5;
    margin-bottom: 12px;
}

.contact-info {
    font-size: 13px;
    color: var(--support-primary);
    font-weight: 600;
    margin-bottom: 16px;
}

.contact-button {
    background: var(--support-primary);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 100%;
}

.contact-button:hover {
    background: var(--support-primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

/* Enhanced FAQ Section */
.faq-section {
    margin-bottom: 40px;
}

.faq-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding: 4px 0;
    scrollbar-width: none;
}

.faq-tabs::-webkit-scrollbar {
    display: none;
}

.faq-tab {
    flex: 0 0 auto;
    padding: 8px 16px;
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: 20px;
    color: var(--text-color);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    backdrop-filter: blur(10px);
}

.faq-tab.active,
.faq-tab:hover {
    background: var(--support-primary);
    border-color: var(--support-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.faq-content {
    display: none;
}

.faq-content.active {
    display: block;
}

.faq-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.faq-item {
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: var(--mobile-border-radius);
    margin-bottom: 12px;
    backdrop-filter: blur(10px);
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    border-color: var(--support-primary);
}

.faq-question {
    padding: 16px 20px;
    background: var(--faq-question-bg);
    color: var(--text-color);
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    position: relative;
    transition: var(--transition);
}

.faq-question::after {
    content: '+';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    font-weight: bold;
    color: var(--support-primary);
    transition: var(--transition);
}

.faq-item.active .faq-question::after {
    transform: translateY(-50%) rotate(45deg);
}

.faq-answer {
    padding: 0 20px;
    background: var(--faq-answer-bg);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.faq-item.active .faq-answer {
    padding: 16px 20px;
    max-height: 1000px;
}

.faq-answer p {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.6;
    margin-bottom: 12px;
}

.faq-answer p:last-child {
    margin-bottom: 0;
}

.faq-answer a {
    color: var(--support-primary);
    text-decoration: none;
}

.faq-answer a:hover {
    text-decoration: underline;
}

/* Enhanced Community Section */
.community-section {
    margin-bottom: 40px;
}

.community-header {
    text-align: center;
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 24px;
    margin-bottom: 24px;
    backdrop-filter: blur(10px);
}

.community-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 12px;
}

.community-description {
    font-size: 14px;
    color: var(--text-color-muted);
    line-height: 1.5;
    margin-bottom: 20px;
}

.community-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.community-card {
    background: var(--support-card-bg);
    border: 1px solid var(--support-card-border);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.community-card:hover {
    background: var(--support-card-hover);
    border-color: var(--support-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
}

.community-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.community-card-icon {
    color: var(--support-primary);
    font-size: 20px;
}

.community-card-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.community-card-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-color-muted);
}

.stat-icon {
    color: var(--support-primary);
    font-size: 10px;
}

.community-card-latest {
    border-top: 1px solid var(--support-card-border);
    padding-top: 16px;
}

.latest-title {
    font-size: 13px;
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 8px;
}

.latest-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: var(--text-color-muted);
}

.latest-user {
    display: flex;
    align-items: center;
    gap: 6px;
}

.user-avatar {
    width: 20px;
    height: 20px;
    background: var(--support-primary);
    border-radius: 50%;
}

/* Enhanced Chatbot Widget */
.chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.chatbot-button {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, var(--support-primary), var(--support-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
    transition: var(--transition);
    animation: chatbotPulse 2s infinite;
}

.chatbot-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
}

@keyframes chatbotPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.chatbot-container {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 300px;
    height: 400px;
    background: var(--bg-color);
    border: 1px solid var(--chat-border);
    border-radius: var(--mobile-border-radius);
    display: none;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.chatbot-container.active {
    display: flex;
}

.chatbot-header {
    background: var(--support-primary);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 14px;
}

.chatbot-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: var(--transition);
}

.chatbot-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chatbot-message {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    max-width: 85%;
}

.chatbot-message.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    flex-shrink: 0;
}

.chatbot-message.bot .message-avatar {
    background: var(--support-primary);
    color: white;
}

.chatbot-message.user .message-avatar {
    background: var(--text-color-muted);
    color: white;
}

.message-content {
    background: var(--chat-bot-bg);
    border: 1px solid var(--chat-border);
    border-radius: 12px;
    padding: 8px 12px;
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-color);
}

.chatbot-message.user .message-content {
    background: var(--support-primary);
    color: white;
    border-color: var(--support-primary);
}

.chatbot-input {
    border-top: 1px solid var(--chat-border);
    padding: 12px;
    display: flex;
    gap: 8px;
}

.chatbot-input input {
    flex: 1;
    background: var(--support-card-bg);
    border: 1px solid var(--chat-border);
    border-radius: 20px;
    padding: 8px 16px;
    color: var(--text-color);
    font-size: 13px;
    outline: none;
}

.chatbot-input input::placeholder {
    color: var(--text-color-muted);
}

.chatbot-send {
    background: var(--support-primary);
    border: none;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    font-size: 12px;
}

.chatbot-send:hover {
    background: var(--support-primary-hover);
    transform: scale(1.1);
}

/* Footer */
.footer {
    background: var(--support-card-bg);
    border-top: 1px solid var(--support-card-border);
    padding: 24px var(--mobile-support-padding);
    text-align: center;
    margin-top: 40px;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--text-color-muted);
    text-decoration: none;
    font-size: 13px;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--support-primary);
}

.footer-text {
    font-size: 12px;
    color: var(--text-color-muted);
    margin: 0;
}

/* Responsive Breakpoints */

/* Tablet Styles */
@media (min-width: 768px) {
    .support-page {
        padding: 20px;
    }
    
    .support-hero {
        padding: 40px 32px;
        min-height: 240px;
    }
    
    .hero-title {
        font-size: 32px;
    }
    
    .hero-subtitle {
        font-size: 16px;
    }
    
    .quick-links {
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
    }
    
    .methods-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    
    .community-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .chatbot-container {
        width: 350px;
        height: 450px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .support-page {
        padding: 2rem;
        display: grid;
        grid-template-columns: 1fr 300px;
        gap: 2rem;
        align-items: start;
    }
    
    .support-main {
        order: 1;
    }
    
    .support-sidebar {
        order: 2;
        position: sticky;
        top: 2rem;
    }
    
    .support-hero {
        grid-column: 1 / -1;
        padding: 60px 40px;
        min-height: 300px;
    }
    
    .hero-title {
        font-size: 38px;
    }
    
    .hero-subtitle {
        font-size: 18px;
    }
    
    .quick-links {
        grid-column: 1 / -1;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 28px;
    }
    
    .community-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chatbot-container {
        width: 380px;
        height: 500px;
    }
    
    .category-card {
        padding: 24px;
    }
    
    .contact-card {
        padding: 28px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .support-page {
        grid-template-columns: 1fr 350px;
        gap: 2.5rem;
    }
    
    .categories-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Enhanced animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading animations */
.loading-skeleton {
    background: linear-gradient(90deg, 
        rgba(33, 150, 243, 0.1) 25%, 
        rgba(33, 150, 243, 0.2) 50%, 
        rgba(33, 150, 243, 0.1) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Custom scrollbar */
.faq-tabs::-webkit-scrollbar,
.chatbot-messages::-webkit-scrollbar {
    width: 4px;
}

.faq-tabs::-webkit-scrollbar-track,
.chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
}

.faq-tabs::-webkit-scrollbar-thumb,
.chatbot-messages::-webkit-scrollbar-thumb {
    background: var(--support-primary);
    border-radius: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .support-hero::before,
    .chatbot-button {
        animation: none !important;
    }
}

/* Focus accessibility */
.quick-link:focus,
.contact-button:focus,
.faq-question:focus,
.chatbot-button:focus {
    outline: 2px solid var(--support-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .support-hero,
    .chatbot-widget,
    .contact-methods {
        display: none !important;
    }
    
    .support-page {
        display: block !important;
        grid-template-columns: none !important;
    }
    
    .faq-answer {
        max-height: none !important;
        padding: 16px 20px !important;
    }
}