/* Plinko Game Styles */

:root {
    --plinko-primary: #e91e63;
    --plinko-primary-dark: #c2185b;
    --plinko-primary-light: #f8bbd0;
    --plinko-secondary: #3f51b5;
    --plinko-secondary-light: #7986cb;
    --plinko-success: #4caf50;
    --plinko-warning: #ffc107;
    --plinko-danger: #f44336;
    --plinko-info: #2196f3;
    --plinko-board-bg: #212121;
    --plinko-peg-color: #9e9e9e;
    --plinko-slot-0: #f44336;
    --plinko-slot-100: #4caf50;
    --plinko-slot-150: #8bc34a;
    --plinko-slot-200: #ffc107;
    --plinko-slot-250: #ff9800;
}

/* Main Container */
.plinko-container {
    padding: 15px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Dashboard Layout */
.game-dashboard {
    display: grid;
    grid-template-columns: minmax(280px, 320px) 1fr;
    gap: 20px;
    width: 100%;
}

/* Mobile Wallet (only shown on mobile) */
.mobile-wallet {
    display: none;
    background: linear-gradient(135deg, #1e1e1e, #2a2a2a);
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    justify-content: space-between;
    align-items: center;
}

.wallet-balance, .mobile-last-win {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--plinko-primary);
    font-weight: 600;
    font-size: 1rem;
}

.wallet-balance i, .mobile-last-win i {
    font-size: 1rem;
}

/* Header Styles */
.plinko-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    margin-right: auto;
}

.back-link:hover {
    color: var(--plinko-primary-light);
}

.game-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--plinko-primary-dark), var(--plinko-primary));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.view-toggle {
    display: flex;
    gap: 5px;
    margin-left: auto;
}

.view-btn {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: linear-gradient(135deg, var(--plinko-primary-dark), var(--plinko-primary));
    color: white;
    border-color: var(--plinko-primary);
    box-shadow: 0 0 8px rgba(233, 30, 99, 0.5);
}

.view-btn:hover:not(.active) {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    color: var(--text-color);
}

/* Dashboard Left Column */
.dashboard-left {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #3a3a3a;
}

/* Balance Display */
.balance-display {
    background: linear-gradient(135deg, #9e8a21, #ffd700);
    border-radius: 8px;
    padding: 12px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.balance-label {
    font-size: 0.9rem;
    color: #1a1625;
    margin-bottom: 5px;
    font-weight: 600;
}

.balance-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a1625;
}

.win-loss-indicator {
    position: absolute;
    top: -10px;
    right: -10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
}

.win-loss-indicator.win {
    background: var(--plinko-success);
    color: white;
}

.win-loss-indicator.loss {
    background: var(--plinko-danger);
    color: white;
}

/* Betting Controls */
.betting-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.bet-amount-control {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bet-amount-control label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.bet-input-group {
    display: flex;
    align-items: center;
}

.bet-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bet-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.bet-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
    font-weight: 600;
}

/* Bet Presets */
.bet-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 10px;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.preset-btn.max {
    background: linear-gradient(135deg, #3d3d3d, #444);
    color: var(--plinko-primary);
    font-weight: 600;
}

/* Board Configuration */
.board-config {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.config-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-group label {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.config-select {
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    color: var(--text-color);
    font-size: 0.85rem;
    min-width: 150px;
}

/* Auto Drop Controls */
.auto-drop-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.auto-drop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.auto-drop-header label {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
}

input:checked + .slider {
    background-color: var(--plinko-primary);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

.auto-drop-settings {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.auto-drop-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.auto-drop-group label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.count-input-group {
    display: flex;
    align-items: center;
}

.count-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.count-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.count-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
    font-weight: 600;
}

.auto-drop-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.drop-preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 10px;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.drop-preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

/* Game Actions */
.game-actions {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 10px;
}

.play-btn {
    background: linear-gradient(135deg, var(--plinko-primary-dark), var(--plinko-primary)) !important;
    color: white !important;
    border-color: var(--plinko-primary-dark) !important;
    font-weight: 600 !important;
    font-size: 0.9rem !important;
    padding: 10px 15px !important;
}

.play-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--plinko-primary), var(--plinko-primary-light)) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.reset-btn {
    background: linear-gradient(135deg, #333333, #444444) !important;
    color: var(--text-color) !important;
    border-color: #555555 !important;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem !important;
    background: linear-gradient(135deg, #2a2a2a, #333333) !important;
    color: var(--text-secondary) !important;
    border: 1px solid #444 !important;
}

/* Dashboard Right Column */
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Status */
.game-status {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    padding: 12px 15px;
    border-radius: 10px;
    text-align: center;
    font-size: 1rem;
    color: var(--text-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    margin-bottom: 0;
    border: 1px solid #3d3d3d;
}

.game-status.success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
    border: 1px solid var(--plinko-success);
}

.game-status.warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.1));
    border: 1px solid var(--plinko-warning);
}

.game-status.danger {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.3), rgba(244, 67, 54, 0.1));
    border: 1px solid var(--plinko-danger);
}

/* Plinko Board */
.plinko-board-container {
    position: relative;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid #3d3d3d;
    overflow: hidden;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.drop-zone {
    display: flex;
    justify-content: space-around;
    width: 100%;
    padding: 10px 0;
    z-index: 2;
}

.drop-slot {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px dashed var(--plinko-primary-light);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: relative;
}

.drop-slot:hover {
    background-color: rgba(233, 30, 99, 0.2);
    transform: scale(1.1);
}

.drop-slot.active {
    background-color: rgba(233, 30, 99, 0.4);
    border: 2px solid var(--plinko-primary);
}

.drop-slot.active::after {
    content: '';
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 15px;
    background-color: var(--plinko-primary);
    animation: pulse 1s infinite;
}

.plinko-canvas {
    background-color: var(--plinko-board-bg);
    border-radius: 6px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
    width: 100%;
    max-height: 70vh;
    height: auto;
}

.slot-zone {
    display: flex;
    justify-content: space-around;
    width: 100%;
    padding: 10px 0;
    z-index: 2;
}

.slot {
    width: 50px;
    height: 30px;
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.8rem;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.slot::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), transparent);
    opacity: 0.5;
    pointer-events: none;
}

.slot.active {
    transform: translateY(-3px);
    box-shadow: 0 -4px 8px rgba(0, 0, 0, 0.3);
    animation: glow 1s infinite;
}

.slot[data-value="0"] {
    background-color: var(--plinko-slot-0);
}

.slot[data-value="100"] {
    background-color: var(--plinko-slot-100);
}

.slot[data-value="150"] {
    background-color: var(--plinko-slot-150);
}

.slot[data-value="200"] {
    background-color: var(--plinko-slot-200);
}

.slot[data-value="250"] {
    background-color: var(--plinko-slot-250);
}

/* Auto Drop Progress */
.auto-drop-progress {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    padding: 10px 15px;
    border: 1px solid #3d3d3d;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #333;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--plinko-primary-dark), var(--plinko-primary));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* Mobile Controls (hidden on desktop) */
.mobile-controls {
    display: none;
    flex-direction: column;
    gap: 10px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #3d3d3d;
    margin-top: 15px;
}

.mobile-ball-controls {
    display: flex;
    gap: 10px;
}

.mobile-btn {
    flex: 1;
    padding: 12px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mobile-settings {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
}

.mobile-icon-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.2rem !important;
}

/* Drop Statistics Panel */
.drop-statistics {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.stats-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.toggle-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    color: var(--text-color);
}

.stats-content {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-card {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #3a3a3a;
    text-align: center;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--plinko-primary);
}

/* Slot Statistics Panel */
.slot-statistics {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
    display: none; /* Hidden by default, shown in Pro View */
}

.slot-stats-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.slot-stats-content {
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.slot-stats-chart-container {
    height: 250px;
    width: 100%;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 10px;
}

.slot-stats-table-container {
    width: 100%;
    overflow-x: auto;
}

.slot-stats-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.slot-stats-table th,
.slot-stats-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #3d3d3d;
}

.slot-stats-table th {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.slot-stats-table td {
    color: var(--text-color);
    font-size: 0.9rem;
}

/* Game History */
.game-history {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.history-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 0 15px 15px;
}

.history-table-container {
    width: 100%;
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    min-width: 600px;
}

.history-table th,
.history-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #3d3d3d;
}

.history-table th {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.history-table td {
    color: var(--text-color);
    font-size: 0.9rem;
}

.history-table .win {
    color: var(--plinko-success);
}

.history-table .loss {
    color: var(--plinko-danger);
}

.history-placeholder {
    text-align: center;
    color: var(--text-secondary);
    padding: 20px;
    font-style: italic;
}

/* Header Win Loss Indicator */
.header-win-loss-indicator {
    position: relative;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    overflow: hidden;
    min-width: 80px;
}

.header-win-indicator, .header-loss-indicator {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 5px;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
    position: absolute;
    white-space: nowrap;
}

.header-win-indicator {
    background: linear-gradient(135deg, var(--plinko-success), #2d8636);
    color: white;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.header-loss-indicator {
    background: linear-gradient(135deg, var(--plinko-danger), #c62828);
    color: white;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
    animation: fadeIn 0.3s;
}

.modal-content {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    margin: 10% auto;
    padding: 25px;
    border: 1px solid #4a4a4a;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    animation: slideIn 0.3s;
}

.close-modal {
    color: var(--text-secondary);
    float: right;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-modal:hover {
    color: var(--plinko-primary);
}

.modal-body {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.modal-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.modal-form-group label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.modal-input-group {
    display: flex;
    align-items: center;
}

.modal-btn-minus, .modal-btn-plus {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 8px 12px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1.1rem;
    text-align: center;
    font-weight: 600;
}

.modal-preset-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.modal-preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 8px 12px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.modal-preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.modal-preset-btn.max {
    background: linear-gradient(135deg, #3d3d3d, #444);
    color: var(--plinko-primary);
    font-weight: 600;
}

.modal-select {
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 8px 12px;
    color: var(--text-color);
    font-size: 0.9rem;
    width: 100%;
}

.modal-auto-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.modal-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.modal-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: 0.4s;
}

.modal-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
}

input:checked + .modal-slider {
    background-color: var(--plinko-primary);
}

input:checked + .modal-slider:before {
    transform: translateX(30px);
}

.modal-slider.round {
    border-radius: 30px;
}

.modal-slider.round:before {
    border-radius: 50%;
}

.modal-apply-btn {
    background: linear-gradient(135deg, var(--plinko-primary-dark), var(--plinko-primary)) !important;
    color: white !important;
    border: none !important;
    padding: 12px 20px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    margin-top: 10px !important;
}

/* Rules Content */
.rules-content {
    margin-top: 20px;
}

.rules-content h3 {
    margin-top: 20px;
    color: var(--plinko-primary);
    font-size: 1.2rem;
}

.rules-content ul, 
.rules-content ol {
    padding-left: 20px;
}

.rules-content li {
    margin-bottom: 8px;
}

/* Pro View Mode Special Styling */
.pro-view-active .game-dashboard {
    grid-template-columns: 1fr;
}

.pro-view-active .dashboard-left {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.pro-view-active .betting-controls,
.pro-view-active .board-config,
.pro-view-active .auto-drop-controls {
    height: 100%;
}

.pro-view-active .slot-statistics {
    display: block;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes flyIn {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes glow {
    0% { box-shadow: 0 -2px 5px rgba(233, 30, 99, 0.5); }
    50% { box-shadow: 0 -2px 15px rgba(233, 30, 99, 0.8); }
    100% { box-shadow: 0 -2px 5px rgba(233, 30, 99, 0.5); }
}

@keyframes bounce {
    0% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
    100% { transform: translateY(0); }
}

/* RESPONSIVE STYLES */

/* Tablet View */
@media (max-width: 1024px) {
    .game-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .dashboard-left {
        max-width: 100%;
        width: 100%;
    }
    
    .betting-controls, 
    .board-config, 
    .auto-drop-controls, 
    .game-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .bet-amount-control,
    .bet-presets,
    .auto-drop-header,
    .auto-drop-settings {
        grid-column: 1 / -1;
    }
    
    .drop-slot {
        width: 25px;
        height: 25px;
    }
    
    .slot {
        width: 40px;
        font-size: 0.75rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Mobile View */
@media (max-width: 768px) {
    .plinko-container {
        padding: 10px;
    }
    
    .plinko-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .back-link {
        margin-right: 0;
    }
    
    .game-title {
        font-size: 1.8rem;
        margin: 5px 0;
    }
    
    .view-toggle {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
        text-align: center;
    }
    
    .mobile-wallet {
        display: flex;
    }
    
    .dashboard-left {
        padding: 12px;
    }
    
    .balance-value {
        font-size: 1.5rem;
    }
    
    .bet-presets,
    .auto-drop-presets {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }
    
    .preset-btn,
    .drop-preset-btn {
        font-size: 0.8rem;
        padding: 8px 0;
    }
    
    .drop-slot {
        width: 20px;
        height: 20px;
    }
    
    .slot {
        width: 35px;
        height: 25px;
        font-size: 0.7rem;
    }
    
    /* Show mobile controls */
    .mobile-controls {
        display: flex;
    }
    
    /* Hide desktop controls on mobile */
    .dashboard-left .game-actions {
        display: none;
    }
    
    .header-win-loss-indicator {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-title, .slot-stats-title, .history-title {
        padding: 12px 15px;
        font-size: 1.1rem;
    }
    
    .stats-content, .slot-stats-content, .history-content {
        padding: 0 10px 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 15px;
    }
}

/* Small Mobile View */
@media (max-width: 480px) {
    .plinko-container {
        padding: 8px;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .betting-controls, 
    .board-config,
    .auto-drop-controls {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    
    .bet-presets,
    .auto-drop-presets {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }
    
    .bet-input-group,
    .count-input-group {
        width: 100%;
    }
    
    .game-status {
        font-size: 0.9rem;
        padding: 10px;
    }
    
    .drop-slot {
        width: 15px;
        height: 15px;
        border-width: 1px;
    }
    
    .slot {
        width: 30px;
        height: 20px;
        font-size: 0.65rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .history-table {
        min-width: 450px;
    }
    
    .mobile-btn {
        padding: 10px !important;
        font-size: 0.9rem !important;
    }
    
    .mobile-icon-btn {
        width: 40px;
        height: 40px;
    }
}

/* Portrait/Landscape Specific Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .game-dashboard {
        grid-template-columns: 320px 1fr;
    }
    
    .mobile-wallet {
        display: none;
    }
    
    .dashboard-left .game-actions {
        display: grid;
    }
    
    .mobile-controls {
        display: none;
    }
}

/* Pro View Mode Special Mobile Styling */
@media (max-width: 768px) {
    .pro-view-active .dashboard-left {
        grid-template-columns: 1fr;
    }
    
    .pro-view-active .plinko-board-container {
        margin-top: 0;
    }
    
    .pro-view-active .slot-stats-chart-container {
        height: 200px;
    }
}

/* Screen Rotation Support */
@media screen and (orientation: portrait) {
    .plinko-board-container {
        max-width: 100%;
        width: 100%;
    }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
    .plinko-board-container {
        max-height: 70vh;
    }
    
    .drop-statistics, .slot-statistics, .game-history {
        max-height: 50vh;
        overflow-y: auto;
    }
}