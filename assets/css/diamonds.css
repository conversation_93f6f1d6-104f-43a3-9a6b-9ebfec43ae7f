/* Diamonds Game Styles */

:root {
    --diamond-blue: #1E88E5;
    --diamond-dark-blue: #0D47A1;
    --diamond-purple: #6A1B9A;
    --diamond-light-blue: #64B5F6;
    --diamond-teal: #26A69A;
    --diamond-pink: #EC407A;
    --diamond-red: #E53935;
    --diamond-dark: #1A237E;
    --diamond-light: #E3F2FD;
    
    --diamond-shadow: 0 0 15px rgba(30, 136, 229, 0.4);
    --diamond-gradient: linear-gradient(135deg, #1A237E, #283593, #303F9F);
    --diamond-text-gradient: linear-gradient(45deg, #64B5F6, #1E88E5);
}

.diamonds-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Game Header */
.diamonds-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 20px;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: var(--diamond-light-blue);
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    background: var(--diamond-text-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Game Controls */
.game-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    background: var(--diamond-gradient);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    align-items: center;
    justify-content: space-between;
    border: 1px solid #303F9F;
}

.game-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    flex-grow: 1;
}

.stat-item {
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    min-width: 120px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.multiplier .stat-value {
    color: #64B5F6;
    text-shadow: 0 0 5px rgba(100, 181, 246, 0.5);
}

.bomb-selector {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bomb-selector h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.bomb-slider-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.bomb-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(to right, #64B5F6, #E53935);
    outline: none;
}

.bomb-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.bomb-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ffffff;
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.bomb-count-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bomb-label {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.bomb-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
}

.bomb-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-diamond {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.start-btn {
    background: linear-gradient(135deg, #64B5F6, #1E88E5);
    color: white;
    box-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(100, 181, 246, 0.7);
}

.cashout-btn {
    background: linear-gradient(135deg, #66BB6A, #43A047);
    color: white;
    box-shadow: 0 0 10px rgba(102, 187, 106, 0.5);
}

.cashout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 15px rgba(102, 187, 106, 0.7);
}

.cashout-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Game Status */
.game-status {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    border-radius: 8px;
    text-align: center;
    font-size: 1.1rem;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-status.win {
    background: linear-gradient(135deg, rgba(102, 187, 106, 0.2), rgba(67, 160, 71, 0.2));
    border: 1px solid rgba(102, 187, 106, 0.3);
}

.game-status.loss {
    background: linear-gradient(135deg, rgba(229, 57, 53, 0.2), rgba(183, 28, 28, 0.2));
    border: 1px solid rgba(229, 57, 53, 0.3);
}

/* Game Grid */
.game-grid {
    display: grid;
    gap: 8px;
    padding: 20px;
    background: rgba(26, 35, 126, 0.8);
    border-radius: 10px;
    margin: 0 auto;
    max-width: 600px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-5x5 {
    grid-template-columns: repeat(5, 1fr);
}

.grid-7x7 {
    grid-template-columns: repeat(7, 1fr);
}

.grid-9x9 {
    grid-template-columns: repeat(9, 1fr);
}

.cell {
    width: 100%;
    aspect-ratio: 1;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.cell::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.cell:hover:not(.revealed) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.3);
}

.cell:hover::before {
    opacity: 1;
}

.cell.revealed {
    cursor: default;
}

.cell.diamond {
    background: linear-gradient(135deg, #64B5F6, #1E88E5);
    box-shadow: 0 0 15px rgba(100, 181, 246, 0.6);
    border-color: #64B5F6;
}

.cell.bomb {
    background: linear-gradient(135deg, #E53935, #B71C1C);
    box-shadow: 0 0 15px rgba(229, 57, 53, 0.6);
    border-color: #E53935;
}

/* Fairness Section */
.fairness-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.fairness-title {
    font-size: 1.3rem;
    margin-top: 0;
    margin-bottom: 15px;
    color: white;
    text-align: center;
}

.seed-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.seed-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.seed-label {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
}

.seed-value {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: white;
    word-break: break-all;
}

/* Verify Section */
.verify-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.verify-title {
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 15px;
    color: white;
    text-align: center;
}

.verify-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.verify-input {
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: white;
    font-family: 'Courier New', monospace;
}

.verify-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.verify-btn {
    background: linear-gradient(135deg, #64B5F6, #1E88E5);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.verify-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

.verify-result {
    margin-top: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    display: none;
}

/* Game History */
.history-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-title {
    font-size: 1.2rem;
    margin-top: 0;
    margin-bottom: 15px;
    color: white;
    text-align: center;
}

.history-container {
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.history-item {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-game-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.history-game-number {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.history-bombs {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

.history-result {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.history-outcome {
    font-size: 0.9rem;
    font-weight: 600;
}

.history-outcome.win {
    color: #66BB6A;
}

.history-outcome.loss {
    color: #E53935;
}

.history-multiplier {
    font-size: 1.1rem;
    font-weight: 700;
    color: white;
}

/* Animations */
@keyframes reveal {
    0% {
        transform: scale(0.8) rotate(-10deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        transform: scale(1) rotate(0);
        opacity: 1;
    }
}

.cell.revealed {
    animation: reveal 0.3s ease forwards;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.multiplier.pulse .stat-value {
    animation: pulse 0.5s ease;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .game-controls {
        flex-direction: column;
    }
    
    .game-stats {
        width: 100%;
        justify-content: space-between;
    }
    
    .bomb-selector {
        width: 100%;
    }
    
    .cell {
        font-size: 1.2rem;
    }
    
    .diamonds-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .stat-item {
        flex-grow: 1;
        min-width: 0;
    }
    
    .cell {
        font-size: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}