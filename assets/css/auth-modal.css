/**
 * Authentication Modal Styles
 */

/* Modal Overlay */
.auth-modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    padding: 1rem;
}

.auth-modal-overlay.active {
    display: flex;
}

/* Modal Container */
.auth-modal {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    width: 100%;
    max-width: 450px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    position: relative;
    animation: modalFadeIn 0.3s ease-out;
    border: 1px solid #2a2a5a;
    overflow: hidden;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mo<PERSON> Header */
.auth-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #2a2a5a;
}

.auth-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.auth-logo img {
    width: 32px;
    height: 32px;
}

.auth-logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-close-btn {
    background: none;
    border: none;
    color: #888;
    font-size: 24px;
    cursor: pointer;
    transition: color 0.2s;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-close-btn:hover {
    color: #fff;
}

/* Auth Tabs */
.auth-tabs {
    display: flex;
    background-color: #0f0f23;
}

.auth-tab {
    flex: 1;
    padding: 1rem;
    text-align: center;
    background: none;
    border: none;
    color: #888;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
    border-bottom: 2px solid transparent;
}

.auth-tab.active {
    color: #fff;
    background-color: #1a1a2e;
    border-bottom-color: #FFD700;
}

.auth-tab:hover:not(.active) {
    color: #ccc;
    background-color: #141426;
}

/* Auth Content */
.auth-content {
    padding: 2rem;
}

/* Auth Forms */
.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #ccc;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    background-color: #0f0f23;
    border: 1px solid #2a2a5a;
    border-radius: 6px;
    color: #fff;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #FFD700;
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

.form-control::placeholder {
    color: #666;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
    cursor: pointer;
    transition: color 0.2s;
    margin-top: 12px;
}

.password-toggle:hover {
    color: #FFD700;
}

/* Error States */
.form-group.has-error .form-control {
    border-color: #e74c3c;
}

.error-message {
    display: none;
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.form-group.has-error .error-message {
    display: block;
}

/* Auth Error */
.auth-error {
    display: none;
    background-color: rgba(231, 76, 60, 0.1);
    border: 1px solid #e74c3c;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    color: #e74c3c;
    font-size: 0.9rem;
}

.auth-error.show {
    display: block;
}

/* Verification Notice */
.verification-notice {
    display: none;
    background-color: rgba(39, 174, 96, 0.1);
    border: 1px solid #27ae60;
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 1rem;
    color: #27ae60;
    font-size: 0.9rem;
}

.verification-notice.show {
    display: block;
}

/* Auth Options */
.auth-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.remember-me label {
    margin: 0;
    color: #ccc;
    font-size: 0.9rem;
    cursor: pointer;
}

.forgot-password {
    color: #FFD700;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s;
}

.forgot-password:hover {
    color: #FFA500;
    text-decoration: underline;
}

/* Submit Button */
.auth-submit {
    width: 100%;
    padding: 0.75rem;
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.auth-submit:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.auth-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Divider */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    gap: 1rem;
}

.auth-divider hr {
    flex: 1;
    border: none;
    height: 1px;
    background-color: #2a2a5a;
}

.auth-divider span {
    color: #888;
    font-size: 0.9rem;
}

/* Social Login */
.social-login {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0f0f23;
    border: 1px solid #2a2a5a;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.social-btn:hover {
    background-color: #1a1a2e;
    border-color: #FFD700;
    transform: translateY(-2px);
}

.social-btn.google:hover {
    background-color: #db4437;
    border-color: #db4437;
}

.social-btn.facebook:hover {
    background-color: #3b5998;
    border-color: #3b5998;
}

.social-btn.discord:hover {
    background-color: #7289da;
    border-color: #7289da;
}

.social-btn.steam:hover {
    background-color: #171a21;
    border-color: #c5c3c0;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    color: #888;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #FFD700;
    text-decoration: none;
    transition: color 0.2s;
}

.auth-footer a:hover {
    color: #FFA500;
    text-decoration: underline;
}

/* Success Checkmark Animation */
.success-checkmark {
    display: none;
    text-align: center;
    padding: 2rem;
}

.success-checkmark.show {
    display: block;
}

.check-icon {
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 50%;
    box-sizing: content-box;
    border: 4px solid #27ae60;
    margin: 0 auto 1rem;
}

.check-icon::before {
    top: 3px;
    left: -2px;
    width: 30px;
    transform-origin: 100% 50%;
    border-radius: 100px 0 0 100px;
}

.check-icon::after {
    top: 0;
    left: 30px;
    width: 60px;
    transform-origin: 0 50%;
    border-radius: 0 100px 100px 0;
    animation: rotate-circle 4.25s ease-in;
}

.check-icon::before,
.check-icon::after {
    content: '';
    height: 100px;
    position: absolute;
    background: #1a1a2e;
    transform: rotate(-45deg);
}

.check-icon .icon-line {
    height: 5px;
    background-color: #27ae60;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}

.check-icon .icon-line.line-tip {
    top: 46px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip 0.75s;
}

.check-icon .icon-line.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long 0.75s;
}

.check-icon .icon-circle {
    top: -4px;
    left: -4px;
    z-index: 10;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: absolute;
    box-sizing: content-box;
    border: 4px solid rgba(39, 174, 96, 0.2);
}

.check-icon .icon-fix {
    top: 8px;
    width: 5px;
    left: 26px;
    z-index: 1;
    height: 85px;
    position: absolute;
    transform: rotate(-45deg);
    background-color: #1a1a2e;
}

@keyframes rotate-circle {
    0% {
        transform: rotate(-45deg);
    }
    5% {
        transform: rotate(-45deg);
    }
    12% {
        transform: rotate(-405deg);
    }
    100% {
        transform: rotate(-405deg);
    }
}

@keyframes icon-line-tip {
    0% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    54% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    70% {
        width: 50px;
        left: -8px;
        top: 37px;
    }
    84% {
        width: 17px;
        left: 21px;
        top: 48px;
    }
    100% {
        width: 25px;
        left: 14px;
        top: 45px;
    }
}

@keyframes icon-line-long {
    0% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    65% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    84% {
        width: 55px;
        right: 0px;
        top: 35px;
    }
    100% {
        width: 47px;
        right: 8px;
        top: 38px;
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-modal {
        margin: 1rem;
        max-width: none;
    }
    
    .auth-content {
        padding: 1.5rem;
    }
    
    .auth-modal-header {
        padding: 1rem;
    }
    
    .auth-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .social-login {
        gap: 0.75rem;
    }
    
    .social-btn {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }
}