/* Crash Game Styles */

:root {
    --crash-primary: #ff4b4b;
    --crash-primary-dark: #d10000;
    --crash-success: #00c853;
    --crash-warning: #ffd600;
    --crash-graph-bg: #1a1a1a;
    --crash-panel-bg: #262626;
    --crash-panel-border: #383838;
}

.crash-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Game Header */
.crash-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    margin-right: 20px;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: var(--primary-color);
}

.game-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    background: linear-gradient(135deg, #ff4b4b, #ff7b7b);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Game Status Bar */
.game-status-bar {
    display: flex;
    justify-content: space-between;
    background: linear-gradient(135deg, #262626, #1e1e1e);
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

.status-info, .timer-info, .multiplier-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.status-label, .timer-label, .multiplier-label {
    font-size: 0.8rem;
    color: var(--text-color-muted);
    margin-bottom: 5px;
}

.status-value, .timer-value, .multiplier-value {
    font-size: 1.2rem;
    font-weight: 600;
}

#gameState {
    color: var(--crash-warning);
}

#gameState.betting {
    color: var(--crash-warning);
}

#gameState.countdown {
    color: var(--crash-warning);
    animation: pulse 1s infinite;
}

#gameState.in-progress {
    color: var(--crash-success);
}

#gameState.crashed {
    color: var(--crash-primary);
}

#timeRemaining {
    color: white;
}

#currentMultiplier {
    color: var(--crash-success);
    font-weight: 700;
    font-size: 1.4rem;
}

/* Main Game Area */
.game-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* Chart Container */
.chart-container {
    position: relative;
    background-color: var(--crash-graph-bg);
    border-radius: 10px;
    padding: 20px;
    height: 400px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.multiplier-chart {
    width: 100%;
    height: 100%;
}

.crash-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.crash-overlay.active {
    opacity: 1;
    visibility: visible;
}

.crash-text {
    font-size: 3rem;
    font-weight: 700;
    color: var(--crash-primary);
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(255, 75, 75, 0.8);
    animation: shakeText 0.5s ease-in-out;
}

.crash-multiplier {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
}

/* Controls Panel */
.controls-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.betting-controls, .player-stats {
    background-color: var(--crash-panel-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.betting-controls h3, .player-stats h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: white;
    border-bottom: 1px solid var(--crash-panel-border);
    padding-bottom: 8px;
}

.bet-input-group, .auto-cashout-group {
    margin-bottom: 15px;
}

.bet-input-group label, .auto-cashout-group label {
    display: block;
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: var(--text-color-muted);
}

.bet-input-group input, .auto-cashout-group input {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid var(--crash-panel-border);
    background-color: #1a1a1a;
    color: white;
    font-size: 1rem;
}

.auto-cashout-group {
    position: relative;
}

.auto-suffix {
    position: absolute;
    right: 15px;
    top: 35px;
    color: var(--text-color-muted);
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--crash-success), #009624);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    flex: 1;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00e676, #00c853);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 200, 83, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--crash-primary), var(--crash-primary-dark));
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    flex: 1;
    font-weight: 600;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.btn-secondary:not(:disabled):hover {
    opacity: 1;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 75, 75, 0.3);
}

.btn-secondary:disabled {
    background: #555;
    cursor: not-allowed;
    opacity: 0.5;
}

/* Player Stats */
.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--crash-panel-border);
}

.stat-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.stat-label {
    color: var(--text-color-muted);
}

.stat-value {
    font-weight: 600;
    color: white;
}

/* Active Players */
.active-players {
    background-color: var(--crash-panel-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

.active-players h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: white;
    border-bottom: 1px solid var(--crash-panel-border);
    padding-bottom: 8px;
}

.players-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.player-card {
    background-color: #1a1a1a;
    border-radius: 5px;
    padding: 15px;
    position: relative;
}

.player-name {
    font-weight: 600;
    margin-bottom: 5px;
}

.player-bet {
    font-size: 0.9rem;
    color: var(--text-color-muted);
    margin-bottom: 5px;
}

.player-target {
    font-size: 0.8rem;
    color: var(--crash-warning);
}

.player-status {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--crash-success);
}

.player-status.cashed-out {
    background-color: var(--crash-success);
}

.player-status.bust {
    background-color: var(--crash-primary);
}

/* Game History */
.game-history {
    background-color: var(--crash-panel-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

.game-history h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: white;
    border-bottom: 1px solid var(--crash-panel-border);
    padding-bottom: 8px;
}

.crash-history-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.crash-history-item {
    padding: 8px 12px;
    border-radius: 5px;
    font-weight: 600;
    font-size: 0.9rem;
}

.crash-history-high {
    background-color: rgba(0, 200, 83, 0.2);
    color: var(--crash-success);
}

.crash-history-medium {
    background-color: rgba(255, 214, 0, 0.2);
    color: var(--crash-warning);
}

.crash-history-low {
    background-color: rgba(255, 75, 75, 0.2);
    color: var(--crash-primary);
}

/* Provably Fair */
.provably-fair {
    background-color: var(--crash-panel-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.provably-fair h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: white;
    border-bottom: 1px solid var(--crash-panel-border);
    padding-bottom: 8px;
}

.fair-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.seed-info {
    display: flex;
    flex-direction: column;
    background-color: #1a1a1a;
    padding: 10px;
    border-radius: 5px;
}

.seed-info label {
    font-size: 0.8rem;
    color: var(--text-color-muted);
    margin-bottom: 5px;
}

.seed-value {
    font-family: monospace;
    word-break: break-all;
    font-size: 0.9rem;
    color: white;
}

.btn-text {
    background: none;
    border: none;
    color: var(--crash-success);
    cursor: pointer;
    text-align: center;
    text-decoration: underline;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.btn-text:hover {
    color: #00e676;
}

/* Animations */
@keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

@keyframes shakeText {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-main {
        grid-template-columns: 1fr;
    }
    
    .game-status-bar {
        flex-direction: column;
        gap: 10px;
        align-items: center;
    }
    
    .players-list {
        grid-template-columns: 1fr;
    }
}