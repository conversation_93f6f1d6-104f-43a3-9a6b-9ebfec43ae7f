/* BALLOON PUMP Game Styles - Mobile-first design */

:root {
    --green-zone: #4CAF50;
    --yellow-zone: #FFC107;
    --red-zone: #FF5252;
    --bg-gradient-start: #a0e6ff;
    --bg-gradient-end: #64c8ff;
    --bg-dark: rgba(0, 0, 0, 0.05);
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    --border-light: rgba(255, 255, 255, 0.2);
    --border-dark: rgba(0, 0, 0, 0.1);
    --card-bg: rgba(255, 255, 255, 0.8);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1);
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
}

/* Base Container */
.pump-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px;
    color: var(--text-primary);
}

/* Game Header */
.game-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 10px;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    padding: 8px 12px;
    border-radius: 6px;
    background: var(--card-bg);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.back-link:hover {
    color: var(--text-primary);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.game-title {
    font-size: 1.8rem;
    text-align: center;
    margin: 0;
    background: linear-gradient(45deg, #FF9800, #FF5722);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* View Toggle */
.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid var(--border-dark);
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: linear-gradient(45deg, #FF9800, #FF5722);
    color: var(--text-light);
    border-color: #FF5722;
    box-shadow: 0 0 8px rgba(255, 87, 34, 0.3);
}

.view-btn:hover:not(.active) {
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-primary);
}

/* Mobile Wallet (hidden on desktop) */
.mobile-wallet {
    display: none;
    background: var(--card-bg);
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 15px;
    box-shadow: var(--shadow-sm);
    justify-content: space-between;
    align-items: center;
}

.wallet-balance, .wallet-multiplier {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 1rem;
}

.wallet-balance {
    color: var(--text-primary);
}

.wallet-multiplier {
    color: #FF5722;
}

.wallet-balance i, .wallet-multiplier i {
    font-size: 1rem;
}

/* Game Dashboard Layout */
.game-dashboard {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* Left Column: Stats & Controls */
.dashboard-left {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Stats */
.game-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    background: var(--card-bg);
    padding: 15px;
    border-radius: 10px;
    box-shadow: var(--shadow-md);
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.5);
    padding: 10px;
    border-radius: 8px;
    border: 1px solid var(--border-dark);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.stat-value {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: bold;
}

/* Bet Controls */
.bet-controls {
    background: var(--card-bg);
    padding: 15px;
    border-radius: 10px;
    box-shadow: var(--shadow-md);
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.bet-input {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.bet-input label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.bet-input-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

.bet-control-button {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid var(--border-dark);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bet-control-button:hover {
    background: rgba(255, 255, 255, 0.8);
}

#betInput {
    flex: 1;
    height: 36px;
    padding: 0 10px;
    border: 1px solid var(--border-dark);
    border-radius: 4px;
    font-size: 1rem;
    text-align: center;
}

.bet-shortcuts {
    display: flex;
    gap: 8px;
}

.bet-shortcut-btn {
    flex: 1;
    padding: 8px 5px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid var(--border-dark);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.bet-shortcut-btn:hover {
    background: rgba(255, 255, 255, 0.8);
}

.place-bet-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(45deg, #FF9800, #FF5722);
    border: none;
    border-radius: 6px;
    color: var(--text-light);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.place-bet-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 87, 34, 0.3);
}

/* Game History Section */
.history-section,
.risk-analysis-section,
.performance-section,
.burst-patterns-section {
    background: var(--card-bg);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.section-title,
.performance-title,
.burst-patterns-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.5);
    padding: 12px 15px;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-dark);
}

.section-title i,
.performance-title i,
.burst-patterns-title i {
    margin-right: 8px;
    color: #FF5722;
}

.toggle-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 5px;
}

.toggle-btn:hover {
    color: var(--text-primary);
}

.history-content,
.risk-content,
.performance-content,
.burst-patterns-content {
    padding: 15px;
}

.history-list {
    max-height: 200px;
    overflow-y: auto;
    border-radius: 6px;
    border: 1px solid var(--border-dark);
    background: rgba(255, 255, 255, 0.5);
}

.history-item {
    padding: 10px;
    border-bottom: 1px solid var(--border-dark);
    font-size: 0.9rem;
}

.history-item:last-child {
    border-bottom: none;
}

.history-win {
    color: var(--success-color);
}

.history-loss {
    color: var(--error-color);
}

.history-placeholder {
    padding: 15px;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
}

/* Risk Analysis */
.risk-zones {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.risk-zone {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.risk-zone.green {
    border: 1px solid var(--green-zone);
}

.risk-zone.yellow {
    border: 1px solid var(--yellow-zone);
}

.risk-zone.red {
    border: 1px solid var(--red-zone);
}

.zone-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}

.zone-name {
    font-weight: 600;
}

.risk-zone.green .zone-header {
    background: rgba(76, 175, 80, 0.1);
}

.risk-zone.yellow .zone-header {
    background: rgba(255, 193, 7, 0.1);
}

.risk-zone.red .zone-header {
    background: rgba(255, 82, 82, 0.1);
}

.risk-zone.green .zone-name {
    color: var(--green-zone);
}

.risk-zone.yellow .zone-name {
    color: var(--yellow-zone);
}

.risk-zone.red .zone-name {
    color: var(--red-zone);
}

.zone-multiplier {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.zone-stats {
    display: flex;
    padding: 10px;
    background: rgba(255, 255, 255, 0.5);
}

.zone-stat {
    flex: 1;
    text-align: center;
}

.zone-stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.zone-stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

.optimal-strategy {
    margin-top: 15px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 12px;
    border: 1px solid var(--border-dark);
}

.strategy-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--text-primary);
}

.strategy-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Pro View Sections (hidden by default) */
.pro-view-section {
    display: none;
}

body.pro-view-active .pro-view-section {
    display: block;
}

/* Performance Analytics */
.chart-container {
    width: 100%;
    height: 200px;
    margin-bottom: 15px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.analytics-item {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    border: 1px solid var(--border-dark);
}

.analytics-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.analytics-value {
    font-weight: 600;
    color: var(--text-primary);
}

.analytics-value.positive {
    color: var(--success-color);
}

.analytics-value.negative {
    color: var(--error-color);
}

/* Burst Patterns */
.burst-heatmap {
    margin-bottom: 15px;
}

.heatmap-header {
    padding: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px 6px 0 0;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    border: 1px solid var(--border-dark);
    border-bottom: none;
}

.heatmap-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid var(--border-dark);
    border-radius: 0 0 6px 6px;
    overflow: hidden;
}

.heatmap-cell {
    padding-top: 100%; /* Makes cells square */
    position: relative;
    border-right: 1px solid var(--border-dark);
    border-bottom: 1px solid var(--border-dark);
}

.heatmap-cell:nth-child(10n) {
    border-right: none;
}

.heatmap-cell:nth-last-child(-n+10) {
    border-bottom: none;
}

.cell-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-light);
}

.optimal-cashout {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid var(--border-dark);
}

.optimal-cashout h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--text-primary);
    text-align: center;
}

.cashout-recommendation {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-dark);
}

.cashout-recommendation:last-child {
    border-bottom: none;
}

.recommendation-label {
    color: var(--text-secondary);
}

.recommendation-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* Right Column: Game Area */
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Area */
.game-area {
    background: linear-gradient(to bottom, var(--bg-gradient-start), var(--bg-gradient-end));
    border-radius: 20px;
    padding: 20px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.game-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    transition: background-color 0.5s ease;
}

.ghost-balloons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.ghost-balloon {
    position: absolute;
    width: 60px;
    height: 70px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    opacity: 0.5;
}

.balloon-container {
    position: relative;
    width: 100%;
    height: 250px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
}

.balloon {
    position: relative;
    width: 150px;
    height: 180px;
    background: #ff4081;
    border-radius: 50%;
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: inset -15px -15px 25px rgba(0, 0, 0, 0.15), 
                inset 15px 15px 25px rgba(255, 255, 255, 0.2),
                0 5px 15px rgba(0, 0, 0, 0.1);
}

.balloon-face {
    font-size: 50px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.balloon-string {
    position: absolute;
    bottom: -80px;
    left: 50%;
    width: 2px;
    height: 80px;
    background: #333;
    transform: translateX(-50%);
}

.progress-container {
    width: 100%;
    height: 30px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 15px;
    margin-top: 1rem;
    overflow: hidden;
    position: relative;
    z-index: 2;
}

.progress-bar {
    height: 100%;
    background: var(--green-zone);
    width: 0%;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 15px;
    position: relative;
}

.progress-markers {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    pointer-events: none;
}

.marker {
    height: 100%;
    position: absolute;
    border-right: 2px dashed rgba(0, 0, 0, 0.2);
}

.marker.green-yellow {
    left: 33.33%;
}

.marker.yellow-red {
    left: 66.66%;
}

.marker::after {
    content: attr(data-label);
    position: absolute;
    top: -20px;
    right: -15px;
    font-size: 0.8rem;
    color: #333;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 5px;
    border-radius: 4px;
}

.congrats-banner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(45deg, #FF9800, #FF5722);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.2rem;
    font-weight: 600;
    z-index: 5;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 87, 34, 0.4);
}

.game-controls {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    z-index: 2;
    position: relative;
}

.pump-btn {
    height: 80px;
    width: 80px;
    border-radius: 50%;
    background: linear-gradient(to bottom, #f06292, #ec407a);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 6px 10px rgba(236, 64, 122, 0.4);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pump-btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 8px 15px rgba(236, 64, 122, 0.5);
}

.pump-btn:active:not(:disabled) {
    transform: scale(0.95);
    box-shadow: 0 4px 8px rgba(236, 64, 122, 0.3);
}

.pump-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

.cashout-btn {
    background: linear-gradient(to bottom, #4CAF50, #43A047);
    color: white;
    padding: 0 30px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    height: 50px;
    align-self: center;
    box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.cashout-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(76, 175, 80, 0.4);
}

.cashout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

/* Mobile Pump Controls (hidden on desktop) */
.mobile-pump-controls {
    display: none;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
    z-index: 2;
    position: relative;
    width: 100%;
}

.mobile-pump-btn {
    height: 70px;
    border-radius: 10px;
    background: linear-gradient(to bottom, #f06292, #ec407a);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 6px 10px rgba(236, 64, 122, 0.4);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-cashout-btn {
    height: 70px;
    background: linear-gradient(to bottom, #4CAF50, #43A047);
    color: white;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    box-shadow: 0 4px 6px rgba(76, 175, 80, 0.3);
}

.mobile-pump-btn:disabled,
.mobile-cashout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

.game-feedback {
    margin-top: 15px;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    min-height: 30px;
}

/* Stats Panel */
.stats-panel {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    box-shadow: var(--shadow-md);
    margin-bottom: 20px;
}

.stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-dark);
    padding-bottom: 10px;
}

.stats-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.stats-item {
    padding: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    text-align: center;
    border: 1px solid var(--border-dark);
}

/* Loss Leaderboard */
.loss-leaderboard {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 15px;
    box-shadow: var(--shadow-md);
    margin-bottom: 20px;
}

.leaderboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-dark);
    padding-bottom: 10px;
}

.leaderboard-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.leaderboard-title i {
    color: #FF5722;
}

.leaderboard-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid var(--border-dark);
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.rank {
    font-weight: 700;
    color: var(--red-zone);
    width: 30px;
}

.player {
    flex: 1;
}

.loss-amount {
    font-weight: 700;
    color: var(--red-zone);
}

.safety-message {
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 15px;
    font-style: italic;
}

/* Mobile Stats Toggle */
.mobile-stats-toggle {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

.mobile-panel-toggle {
    background: linear-gradient(45deg, #FF9800, #FF5722);
    color: white;
    border: none;
    border-radius: 50px;
    width: 150px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-panel-toggle:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
    transform: translateY(-2px);
}

/* Mobile Stats Panel */
.mobile-stats-panel {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
    z-index: 200;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
}

.mobile-stats-panel.active {
    transform: translateY(0);
}

.mobile-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-dark);
}

.mobile-panel-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.3rem;
}

.mobile-panel-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
}

.mobile-panel-content {
    padding: 15px;
}

.mobile-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.mobile-stat-card {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    border: 1px solid var(--border-dark);
}

.mobile-section-title {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin: 20px 0 10px;
    border-bottom: 1px solid var(--border-dark);
    padding-bottom: 5px;
}

.mobile-history-list {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 10px;
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid var(--border-dark);
}

.mobile-history-item {
    padding: 8px;
    border-bottom: 1px solid var(--border-dark);
    font-size: 0.85rem;
    display: flex;
    justify-content: space-between;
}

.mobile-history-item:last-child {
    border-bottom: none;
}

.mobile-strategy-tips {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid var(--border-dark);
}

.mobile-tip {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 8px;
    border-bottom: 1px solid var(--border-dark);
}

.mobile-tip:last-child {
    border-bottom: none;
}

.mobile-tip i {
    color: #FF9800;
    margin-top: 2px;
}

/* Burst Overlay */
.burst-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 82, 82, 0.5);
    z-index: 300;
    display: none;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    animation: fadeIn 0.3s forwards;
}

.burst-message {
    font-size: 2.5rem;
    color: #fff;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    animation: popIn 0.5s forwards;
}

.try-again-btn {
    background: linear-gradient(45deg, #FF5722, #FF9800);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.try-again-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(255, 87, 34, 0.4);
}

/* Special Item Popup */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 299;
    display: none;
}

.popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 300;
    max-width: 400px;
    width: 90%;
    text-align: center;
    display: none;
}

.popup-title {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #FF9800;
    font-weight: 600;
}

.popup-content {
    margin-bottom: 20px;
    color: var(--text-primary);
    line-height: 1.5;
}

.popup-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.popup-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.popup-accept {
    background: linear-gradient(45deg, #4CAF50, #43A047);
    color: white;
}

.popup-reject {
    background: #f5f5f5;
    color: var(--text-primary);
    border: 1px solid #ddd;
}

.popup-accept:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.popup-reject:hover {
    background: #eee;
}

/* Explosion Container */
.explosion-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

.explosion-particle {
    position: absolute;
    font-size: 24px;
    transform-origin: center;
    z-index: 101;
}

/* Header Win/Loss Indicator */
.header-win-loss-indicator {
    position: relative;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    overflow: hidden;
    min-width: 80px;
}

.win-indicator, .loss-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
    position: absolute;
    white-space: nowrap;
}

.win-indicator {
    background: linear-gradient(45deg, var(--green-zone), #3d8b40);
    color: white;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.loss-indicator {
    background: linear-gradient(45deg, var(--red-zone), #d32f2f);
    color: white;
    box-shadow: 0 0 10px rgba(255, 82, 82, 0.5);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes popIn {
    0% { transform: scale(0.5); opacity: 0; }
    80% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes pump {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes burst {
    0% { transform: scale(1); opacity: 1; }
    20% { transform: scale(1.2); }
    100% { transform: scale(3); opacity: 0; }
}

@keyframes flyOut {
    0% { 
        opacity: 1;
        transform: translate(0, 0) rotate(0deg);
    }
    100% { 
        opacity: 0;
        transform: translate(var(--x, 100px), var(--y, 100px)) rotate(var(--r, 180deg));
    }
}

@keyframes flyIn {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* RESPONSIVE STYLING - Mobile First */

/* Medium screens (Tablets) */
@media (min-width: 768px) and (max-width: 991px) {
    .game-dashboard {
        grid-template-columns: 1fr 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .analytics-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Small screens (Landscape phones) */
@media (min-width: 576px) and (max-width: 767px) {
    .game-dashboard {
        grid-template-columns: 1fr;
    }
    
    .dashboard-left {
        order: 2;
    }
    
    .dashboard-right {
        order: 1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .analytics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .view-toggle {
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
    }
}

/* Extra small screens (Portrait phones) */
@media (max-width: 575px) {
    .game-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .back-link {
        margin-bottom: 10px;
    }
    
    .game-title {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }
    
    .view-toggle {
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
    }
    
    .mobile-wallet {
        display: flex;
    }
    
    .game-dashboard {
        grid-template-columns: 1fr;
    }
    
    .dashboard-left {
        order: 2;
    }
    
    .dashboard-right {
        order: 1;
    }
    
    .game-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .game-controls {
        display: none;
    }
    
    .mobile-pump-controls {
        display: grid;
    }
    
    .mobile-stats-toggle {
        display: block;
    }
    
    .mobile-stats-panel {
        display: block;
    }
    
    .balloon {
        width: 120px;
        height: 150px;
    }
    
    .balloon-face {
        font-size: 40px;
    }
    
    .burst-message {
        font-size: 2rem;
    }
    
    .game-area {
        padding: 15px;
        min-height: 350px;
    }
    
    .popup {
        width: 95%;
        padding: 20px;
    }
    
    .popup-buttons {
        flex-direction: column;
        gap: 10px;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .mobile-stats-panel {
        max-height: 70vh;
    }
    
    .balloon-container {
        height: 180px;
    }
    
    .balloon {
        width: 100px;
        height: 120px;
    }
    
    .balloon-face {
        font-size: 35px;
    }
    
    .balloon-string {
        height: 60px;
        bottom: -60px;
    }
}

/* Pro View Mode Styling */
body.pro-view-active .stats-panel {
    margin-bottom: 0;
}

body.pro-view-active .mobile-stats-panel .mobile-section-title:last-of-type,
body.pro-view-active .mobile-stats-panel .mobile-strategy-tips {
    display: block;
}

/* Heat Cell Color Function */
.heat-0 { background-color: rgba(76, 175, 80, 0.7); }
.heat-1 { background-color: rgba(76, 175, 80, 0.8); }
.heat-2 { background-color: rgba(76, 175, 80, 0.9); }
.heat-3 { background-color: rgba(255, 235, 59, 0.7); }
.heat-4 { background-color: rgba(255, 193, 7, 0.7); }
.heat-5 { background-color: rgba(255, 152, 0, 0.7); }
.heat-6 { background-color: rgba(255, 87, 34, 0.7); }
.heat-7 { background-color: rgba(244, 67, 54, 0.7); }
.heat-8 { background-color: rgba(244, 67, 54, 0.8); }
.heat-9 { background-color: rgba(244, 67, 54, 0.9); }