:root {
    --dark-bg-1: #121212;
    --dark-bg-2: #1e1e1e;
    --dark-bg-3: #2d2d2d;
    --text-color: #f5f5f5;
    --text-color-muted: #a0a0a0;
    --accent-color: #ffd700;
    --accent-color-hover: #f8c400;
    --gold-gradient: linear-gradient(to right, #ffd700, #f8c400);
    --gold-shadow: 0 2px 10px rgba(255, 215, 0, 0.4);
    --border-radius: 8px;
    --transition: all 0.3s ease;
    
    /* Enhanced engagement colors */
    --hot-color: #ff4444;
    --hot-glow: 0 0 20px rgba(255, 68, 68, 0.6);
    --trending-color: #00ff88;
    --trending-glow: 0 0 20px rgba(0, 255, 136, 0.6);
    --new-color: #8a2be2;
    --new-glow: 0 0 20px rgba(138, 43, 226, 0.6);
    --winner-color: #ffd700;
    --winner-glow: 0 0 30px rgba(255, 215, 0, 0.8);
    
    /* Mobile-first responsive variables */
    --mobile-padding: 12px;
    --mobile-gap: 8px;
    --touch-target: 44px;
    --mobile-header-height: 60px;
    --mobile-sidebar-width: 280px;
    --mobile-border-radius: 12px;
    
    /* Enhanced color palette for mobile */
    --primary-blue: #007bff;
    --primary-blue-hover: #0056b3;
    --success-green: #28a745;
    --success-green-hover: #1e7e34;
    --warning-orange: #fd7e14;
    --warning-orange-hover: #d63384;
    --danger-red: #dc3545;
    --danger-red-hover: #bd2130;
    
    /* Glass morphism effects */
    --glass-bg: rgba(30, 30, 30, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    
    /* Mobile status bar safe areas */
    --safe-area-top: env(safe-area-inset-top);
    --safe-area-bottom: env(safe-area-inset-bottom);
    --safe-area-left: env(safe-area-inset-left);
    --safe-area-right: env(safe-area-inset-right);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* Welcome Bonus Banner */
.welcome-bonus-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #ffd700, #ff6b35, #f7931e);
    z-index: 1000;
    padding: 12px 16px;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
    box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
    animation: bannerPulse 2s infinite alternate;
}

.welcome-bonus-banner.show {
    transform: translateY(0);
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.bonus-icon {
    font-size: 24px;
    animation: bounce 1s infinite;
}

.bonus-text {
    flex: 1;
    margin-left: 12px;
    color: #000;
}

.bonus-title {
    font-weight: 700;
    font-size: 14px;
    display: block;
}

.bonus-amount {
    font-weight: 600;
    font-size: 12px;
    opacity: 0.8;
}

.bonus-claim-btn {
    background: #000;
    color: #ffd700;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.bonus-claim-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.banner-close {
    background: none;
    border: none;
    color: #000;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    margin-left: 12px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.banner-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

.banner-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    pointer-events: none;
}

.banner-particles::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255, 255, 255, 0.1) 2px,
        rgba(255, 255, 255, 0.1) 4px
    );
    animation: sparkle 3s linear infinite;
}

/* Live Activity Ticker */
.live-ticker {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid var(--accent-color);
    border-radius: 25px;
    padding: 8px 16px;
    z-index: 999;
    max-width: 300px;
    backdrop-filter: blur(10px);
    box-shadow: var(--winner-glow);
    animation: tickerSlide 0.5s ease-out;
}

.ticker-content {
    color: var(--text-color);
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
}

.ticker-item {
    animation: tickerText 4s ease-in-out;
}

.winner-name {
    color: var(--accent-color);
    font-weight: 700;
}

.win-amount {
    color: var(--hot-color);
    font-weight: 700;
}

/* Live Support Indicator */
.live-support-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--text-color);
    margin-right: 16px;
}

.support-dot {
    width: 8px;
    height: 8px;
    background: var(--trending-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Navigation Indicators */
.nav-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.nav-indicator.hot {
    background: var(--hot-color);
    box-shadow: var(--hot-glow);
}

.nav-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--new-color);
    color: white;
    font-size: 8px;
    font-weight: 700;
    padding: 2px 4px;
    border-radius: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Trust Indicators */
.trust-indicators {
    margin-top: auto;
    padding: 16px;
    border-top: 1px solid var(--glass-border);
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    font-size: 11px;
    color: var(--text-color-muted);
}

.trust-item i {
    color: var(--trending-color);
    width: 14px;
}

/* Enhanced Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;
}

.live-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 8px 12px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.stat-item i {
    color: var(--accent-color);
    margin-bottom: 4px;
    display: block;
}

.stat-item span {
    display: block;
    font-weight: 700;
    color: var(--text-color);
    font-size: 14px;
}

.stat-item small {
    color: var(--text-color-muted);
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Featured Game Banner */
.featured-game-banner {
    background: linear-gradient(135deg, #ff6b35, #f7931e, #ffd700);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
    animation: featuredPulse 3s ease-in-out infinite alternate;
}

.featured-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.featured-text {
    flex: 1;
}

.featured-badge {
    background: rgba(0, 0, 0, 0.8);
    color: var(--accent-color);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    margin-bottom: 8px;
}

.featured-game-banner h2 {
    color: #000;
    font-size: 20px;
    font-weight: 800;
    margin-bottom: 6px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.featured-game-banner p {
    color: rgba(0, 0, 0, 0.8);
    font-size: 14px;
    margin-bottom: 12px;
}

.tournament-stats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-weight: 800;
    color: #000;
    font-size: 16px;
}

.stat-label {
    font-size: 10px;
    color: rgba(0, 0, 0, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.featured-btn {
    background: #000;
    color: var(--accent-color);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.featured-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.featured-visual {
    position: relative;
}

.floating-coins {
    font-size: 24px;
    animation: float 2s ease-in-out infinite;
}

/* Enhanced Game Tiles */
.game-tile {
    position: relative;
    overflow: hidden;
}

.game-tile.hot-game {
    animation: hotPulse 2s ease-in-out infinite alternate;
    box-shadow: var(--hot-glow);
}

.game-tile.trending {
    animation: trendingGlow 2s ease-in-out infinite alternate;
    box-shadow: var(--trending-glow);
}

.game-tile.new-game {
    animation: newShimmer 2s ease-in-out infinite alternate;
    box-shadow: var(--new-glow);
}

.game-badge {
    position: absolute;
    top: 8px;
    left: 8px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    z-index: 2;
    animation: badgePulse 2s infinite;
}

.game-badge.hot {
    background: var(--hot-color);
    color: white;
    box-shadow: 0 0 10px rgba(255, 68, 68, 0.6);
}

.game-badge.trending {
    background: var(--trending-color);
    color: #000;
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.6);
}

.game-badge.new {
    background: var(--new-color);
    color: white;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.6);
}

.game-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;
}

.player-count {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
}

.win-rate {
    font-size: 11px;
    color: var(--trending-color);
    font-weight: 600;
}

.recent-win {
    font-size: 10px;
    color: var(--accent-color);
    background: rgba(0, 0, 0, 0.3);
    padding: 4px 6px;
    border-radius: 6px;
    margin-top: 4px;
}

/* Footer Enhancements */
.footer-certifications {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 16px;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-color-muted);
}

.cert-item i {
    color: var(--trending-color);
}

/* Floating Action Buttons */
.floating-actions {
    position: fixed;
    bottom: 80px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 998;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.support-fab {
    background: linear-gradient(135deg, #007bff, #0056b3);
    animation: fabBounce 3s ease-in-out infinite;
}

.bonus-fab {
    background: linear-gradient(135deg, #ffd700, #f8c400);
    color: #000;
    animation: fabPulse 2s ease-in-out infinite alternate;
}

.fab:hover {
    transform: scale(1.1);
}

.fab::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
}

.fab:active::before {
    width: 120%;
    height: 120%;
}

/* Animations */
@keyframes bannerPulse {
    0% { box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4); }
    100% { box-shadow: 0 6px 30px rgba(255, 215, 0, 0.8); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes sparkle {
    0% { transform: translateX(-100%) translateY(-100%); }
    100% { transform: translateX(100%) translateY(100%); }
}

@keyframes tickerSlide {
    0% { transform: translateX(100%); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes tickerText {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes featuredPulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.02); }
}

@keyframes float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

@keyframes hotPulse {
    0% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.6); }
    100% { box-shadow: 0 0 30px rgba(255, 68, 68, 0.9), 0 0 40px rgba(255, 68, 68, 0.3); }
}

@keyframes trendingGlow {
    0% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.6); }
    100% { box-shadow: 0 0 30px rgba(0, 255, 136, 0.9), 0 0 40px rgba(0, 255, 136, 0.3); }
}

@keyframes newShimmer {
    0% { box-shadow: 0 0 20px rgba(138, 43, 226, 0.6); }
    100% { box-shadow: 0 0 30px rgba(138, 43, 226, 0.9), 0 0 40px rgba(138, 43, 226, 0.3); }
}

@keyframes badgePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes fabBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

@keyframes fabPulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

/* Mobile Adjustments */
@media (max-width: 767px) {
    .welcome-bonus-banner {
        padding: 8px 12px;
    }
    
    .bonus-text {
        margin-left: 8px;
        margin-right: 8px;
    }
    
    .bonus-title {
        font-size: 12px;
    }
    
    .bonus-amount {
        font-size: 11px;
    }
    
    .bonus-claim-btn {
        font-size: 11px;
        padding: 6px 12px;
    }
    
    .live-ticker {
        bottom: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        text-align: center;
    }
    
    .live-support-indicator {
        display: none;
    }
    
    .live-stats {
        gap: 12px;
    }
    
    .stat-item {
        padding: 6px 8px;
    }
    
    .stat-item span {
        font-size: 12px;
    }
    
    .featured-game-banner {
        padding: 16px;
    }
    
    .featured-content {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }
    
    .tournament-stats {
        justify-content: center;
        gap: 12px;
    }
    
    .floating-actions {
        bottom: 60px;
        right: 16px;
    }
    
    .fab {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }
    
    .footer-certifications {
        gap: 16px;
        flex-wrap: wrap;
    }
    
    .cert-item {
        font-size: 11px;
    }
}

/* High-end mobile and tablet adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .live-ticker {
        max-width: 400px;
    }
    
    .tournament-stats {
        gap: 20px;
    }
    
    .floating-actions {
        bottom: 100px;
    }
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--dark-bg-1), var(--dark-bg-2));
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    padding-top: calc(var(--mobile-header-height) + var(--safe-area-top));
    padding-left: var(--safe-area-left);
    padding-right: var(--safe-area-right);
    padding-bottom: var(--safe-area-bottom);
}

/* Mobile-first approach */
@media (max-width: 767px) {
    body {
        font-size: 14px;
        line-height: 1.5;
    }
}

/* Enhanced Header Styles - Mobile First */
.header {
    position: fixed;
    top: var(--safe-area-top);
    left: 0;
    right: 0;
    height: var(--mobile-header-height);
    background: linear-gradient(135deg, var(--dark-bg-2), var(--dark-bg-3));
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    border-bottom: 1px solid var(--glass-border);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--mobile-padding);
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--mobile-gap);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--mobile-gap);
    flex: 0 0 auto;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 var(--mobile-gap);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--mobile-gap);
    flex: 0 0 auto;
}

/* Enhanced Mobile Menu Toggle */
.menu-toggle {
    width: var(--touch-target);
    height: var(--touch-target);
    border: none;
    background: var(--glass-bg);
    color: var(--text-color);
    border-radius: var(--mobile-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.menu-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.menu-toggle:hover::before {
    left: 100%;
}

.menu-toggle:hover {
    background: var(--accent-color);
    color: var(--dark-bg-1);
    transform: translateY(-2px);
    box-shadow: var(--gold-shadow);
}

.menu-toggle:active {
    transform: translateY(0) scale(0.95);
}

.menu-toggle i {
    font-size: 16px;
    transition: var(--transition);
}

/* Enhanced Logo */
.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    transition: var(--transition);
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    transition: var(--transition);
}

.logo-text {
    font-size: 18px;
    font-weight: 700;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: none;
}

.logo:hover .logo-img {
    transform: rotate(5deg) scale(1.1);
    box-shadow: var(--gold-shadow);
}

/* Enhanced Search Container */
.search-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-input {
    width: 100%;
    height: var(--touch-target);
    padding: 0 40px 0 var(--mobile-padding);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    color: var(--text-color);
    font-size: 14px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.search-input::placeholder {
    color: var(--text-color-muted);
    font-size: 12px;
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
    background: rgba(30, 30, 30, 0.9);
}

.search-icon {
    position: absolute;
    right: var(--mobile-padding);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-muted);
    font-size: 14px;
    pointer-events: none;
    transition: var(--transition);
}

.search-input:focus + .search-icon {
    color: var(--accent-color);
}

/* Enhanced Button Styles */
.btn {
    height: var(--touch-target);
    padding: 0 16px;
    border: none;
    border-radius: var(--mobile-border-radius);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    min-width: var(--touch-target);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: scale(0.95);
}

/* Login Button */
.btn-login {
    background: var(--glass-bg);
    color: var(--text-color);
    border: 1px solid var(--glass-border);
}

.btn-login:hover {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Register Button */
.btn-register {
    background: var(--gold-gradient);
    color: var(--dark-bg-1);
    border: 1px solid var(--accent-color);
    font-weight: 700;
}

.btn-register:hover {
    background: var(--accent-color-hover);
    transform: translateY(-2px);
    box-shadow: var(--gold-shadow);
}

/* Secondary Button */
.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-color);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--success-green);
    border-color: var(--success-green);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Enhanced Sidebar - Mobile First */
.sidebar {
    position: fixed;
    top: calc(var(--mobile-header-height) + var(--safe-area-top));
    left: -100%;
    width: var(--mobile-sidebar-width);
    height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
    background: linear-gradient(135deg, var(--dark-bg-2), var(--dark-bg-3));
    backdrop-filter: blur(20px);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
    z-index: 999;
    transition: left 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-right: 1px solid var(--glass-border);
    overflow-y: auto;
}

.sidebar.active {
    left: 0;
}

.sidebar-nav {
    padding: var(--mobile-padding);
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--mobile-gap);
    padding: var(--mobile-padding) 16px;
    text-decoration: none;
    color: var(--text-color);
    border-radius: var(--mobile-border-radius);
    transition: var(--transition);
    font-size: 14px;
    font-weight: 500;
    min-height: var(--touch-target);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--gold-gradient);
    transition: width 0.3s ease;
    z-index: -1;
}

.nav-item:hover::before,
.nav-item.active::before {
    width: 4px;
}

.nav-item:hover {
    background: var(--glass-bg);
    color: var(--accent-color);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-item.active {
    background: var(--glass-bg);
    color: var(--accent-color);
    border: 1px solid var(--glass-border);
    transform: translateX(4px);
}

.nav-item i {
    font-size: 16px;
    width: 20px;
    text-align: center;
    transition: var(--transition);
}

.nav-item:hover i,
.nav-item.active i {
    transform: scale(1.1);
}

.nav-item span {
    font-weight: 600;
    transition: var(--transition);
}

/* Enhanced Main Content */
.main-content {
    flex: 1;
    padding: var(--mobile-padding);
    margin-left: 0;
    transition: var(--transition);
    min-height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
}

/* Content Section */
.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Section Header */
.section-header {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-gap);
    margin-bottom: 20px;
    padding: var(--mobile-padding);
    background: var(--glass-bg);
    border-radius: var(--mobile-border-radius);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
}

.section-header h1 {
    font-size: 24px;
    font-weight: 700;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 4px;
}

.controls {
    display: flex;
    flex-wrap: wrap;
    gap: var(--mobile-gap);
    align-items: center;
}

.sort-dropdown {
    height: var(--touch-target);
    padding: 0 var(--mobile-padding);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    color: var(--text-color);
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a0a0a0' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 32px;
}

.sort-dropdown:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

/* Enhanced Games Grid - Mobile First */
.games-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--mobile-gap);
    padding: var(--mobile-padding) 0;
}

/* Game Tile Enhancements */
.game-tile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px var(--mobile-padding);
    border-radius: var(--mobile-border-radius);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 120px;
    cursor: pointer;
    background-size: cover;
    background-position: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.game-tile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    transition: var(--transition);
    z-index: 1;
}

.game-tile:hover::before {
    background: rgba(0, 0, 0, 0.1);
}

.game-tile > * {
    position: relative;
    z-index: 2;
}

.game-tile:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--accent-color);
}

.game-tile:active {
    transform: translateY(-2px) scale(1.01);
}

.game-icon {
    font-size: 32px;
    margin-bottom: 8px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
    transition: var(--transition);
}

.game-tile:hover .game-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.7));
}

.game-tile h3 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 4px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    transition: var(--transition);
}

.game-tile:hover h3 {
    color: var(--accent-color);
    transform: scale(1.05);
}

.player-count {
    font-size: 11px;
    color: var(--text-color-muted);
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
    transition: var(--transition);
}

.game-tile:hover .player-count {
    color: var(--text-color);
}

/* New Badge for games */
.new-badge {
    position: absolute !important;
    top: 8px !important;
    right: 8px !important;
    background: var(--danger-red) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 12px !important;
    font-size: 10px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    z-index: 3 !important;
    animation: pulse 2s infinite !important;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.4) !important;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* Overlay for mobile menu */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    backdrop-filter: blur(4px);
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Tablet Styles */
@media (min-width: 768px) {
    .header-container {
        padding: 0 2rem;
    }
    
    .logo-text {
        display: block;
    }
    
    .search-input::placeholder {
        font-size: 14px;
    }
    
    .btn {
        font-size: 14px;
        padding: 0 20px;
    }
    
    .section-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }
    
    .section-header h1 {
        font-size: 28px;
        margin-bottom: 0;
    }
    
    .games-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .game-tile {
        min-height: 140px;
        padding: 24px;
    }
    
    .game-icon {
        font-size: 40px;
        margin-bottom: 12px;
    }
    
    .game-tile h3 {
        font-size: 16px;
        margin-bottom: 6px;
    }
    
    .player-count {
        font-size: 12px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .sidebar {
        position: fixed;
        left: 0;
        width: 250px;
        top: calc(var(--mobile-header-height) + var(--safe-area-top));
    }
    
    .main-content {
        margin-left: 250px;
        padding: 2rem;
    }
    
    .menu-toggle {
        display: none;
    }
    
    .games-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    
    .game-tile {
        min-height: 160px;
        padding: 28px;
    }
    
    .game-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }
    
    .game-tile h3 {
        font-size: 18px;
        margin-bottom: 8px;
    }
    
    .player-count {
        font-size: 13px;
    }
    
    .section-header h1 {
        font-size: 32px;
    }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
    .games-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 24px;
    }
    
    .main-content {
        padding: 2.5rem;
    }
}

/* Ultra-wide Desktop */
@media (min-width: 1400px) {
    .games-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support (already default, but explicit) */
@media (prefers-color-scheme: dark) {
    :root {
        --dark-bg-1: #0a0a0a;
        --dark-bg-2: #141414;
        --dark-bg-3: #1f1f1f;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.nav-item:focus,
.search-input:focus,
.sort-dropdown:focus,
.game-tile:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid var(--accent-color);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Error states */
.error {
    border-color: var(--danger-red) !important;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
}

/* Success states */
.success {
    border-color: var(--success-green) !important;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2) !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg-1);
}

::-webkit-scrollbar-thumb {
    background: var(--dark-bg-3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Selection styles */
::selection {
    background: var(--accent-color);
    color: var(--dark-bg-1);
}