/* SNACKS OF DOOM - Game Styles */

:root {
    --snacks-primary: #8b0000; /* Deep blood red */
    --snacks-primary-dark: #5a0000;
    --snacks-primary-light: #c72c2c;
    --snacks-secondary: #2c1810; /* Dark brown */
    --snacks-secondary-dark: #1a0f0a;
    --snacks-accent: #ffd700; /* Gold */
    --snacks-good: #4caf50; /* Green */
    --snacks-bad: #f44336; /* Red */
    --snacks-special: #9c27b0; /* Purple */
    --snacks-neutral: #9e9e9e; /* Gray */
    --snacks-text: #ffffff;
    --snacks-text-muted: #bbbbbb;
    --snacks-border: rgba(139, 0, 0, 0.3);
    --font-main: 'Poppins', sans-serif;
    --font-accent: 'Creepster', cursive;
}

/* Base Container */
.snacks-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 15px;
    font-family: var(--font-main);
    color: var(--snacks-text);
}

/* Game Header */
.game-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-bottom: 15px;
    gap: 10px;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--snacks-text-muted);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    padding: 5px 10px;
    border-radius: 5px;
    background: rgba(139, 0, 0, 0.2);
    border: 1px solid var(--snacks-border);
}

.back-button:hover {
    color: var(--snacks-text);
    background: rgba(139, 0, 0, 0.4);
}

.game-title {
    font-family: var(--font-accent);
    font-size: 2rem;
    text-align: center;
    margin: 0;
    color: var(--snacks-primary);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    letter-spacing: 2px;
}

.subtitle {
    color: var(--snacks-text-muted);
    font-style: italic;
    text-align: center;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

/* View Toggle */
.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    background: rgba(44, 24, 16, 0.7);
    border: 1px solid var(--snacks-border);
    border-radius: 5px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--snacks-text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: var(--snacks-primary);
    color: var(--snacks-text);
    border-color: var(--snacks-primary-light);
    box-shadow: 0 0 8px rgba(139, 0, 0, 0.5);
}

.view-btn:hover:not(.active) {
    background: rgba(139, 0, 0, 0.3);
    color: var(--snacks-text);
}

/* Mobile Wallet (hidden on desktop) */
.mobile-wallet {
    display: none;
    background: rgba(44, 24, 16, 0.8);
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--snacks-border);
}

.wallet-balance, .mobile-timer {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--snacks-primary);
    font-weight: 600;
    font-size: 1rem;
}

.wallet-balance i, .mobile-timer i {
    font-size: 1rem;
}

/* Game Dashboard Layout */
.game-dashboard {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* Left Column: Stats & Controls */
.dashboard-left {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Stats */
.game-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid var(--snacks-primary);
}

.stat-item {
    text-align: center;
    background: rgba(44, 24, 16, 0.7);
    padding: 10px;
    border-radius: 8px;
    border: 1px solid var(--snacks-border);
}

.stat-label {
    color: var(--snacks-text-muted);
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.stat-value {
    color: var(--snacks-primary);
    font-size: 1.2rem;
    font-weight: bold;
}

/* Game Controls */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    background: linear-gradient(135deg, var(--snacks-primary), var(--snacks-primary-dark));
    color: white;
    border: none;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid var(--snacks-primary-dark);
    font-family: var(--font-main);
}

.control-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--snacks-primary-light), var(--snacks-primary));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 0, 0, 0.4);
}

.control-btn:disabled {
    background: #333;
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.7;
}

/* Rules Section */
.rules-section,
.snack-values-section,
.analytics-section,
.history-section {
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    border: 1px solid var(--snacks-border);
    overflow: hidden;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(44, 24, 16, 0.7);
    color: var(--snacks-primary);
    margin: 0;
    padding: 12px 15px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid var(--snacks-border);
}

.section-title i {
    margin-right: 8px;
}

.toggle-btn {
    background: none;
    border: none;
    color: var(--snacks-text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 5px;
}

.toggle-btn:hover {
    color: var(--snacks-text);
}

.rules-content,
.snack-values-content,
.analytics-content,
.history-content {
    padding: 15px;
    font-size: 0.9rem;
    color: var(--snacks-text-muted);
    display: none;
}

.rules-content.active,
.snack-values-content.active,
.analytics-content.active,
.history-content.active {
    display: block;
}

.rules-content p {
    margin-bottom: 10px;
}

.rules-content ul {
    margin-bottom: 15px;
    padding-left: 20px;
}

.rules-content li {
    margin-bottom: 5px;
}

.good-snack {
    color: var(--snacks-good);
    font-weight: 600;
}

.bad-snack {
    color: var(--snacks-bad);
    font-weight: 600;
}

.special-snack {
    color: var(--snacks-special);
    font-weight: 600;
}

.warning-text {
    color: var(--snacks-primary);
    font-weight: 600;
    font-style: italic;
}

/* Snack Values Table */
.snack-table {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.snack-row {
    display: grid;
    grid-template-columns: 40px 1fr 1fr;
    align-items: center;
    padding: 8px;
    border-radius: 5px;
    background: rgba(44, 24, 16, 0.5);
}

.snack-row.bad {
    background: rgba(244, 67, 54, 0.1);
}

.snack-row.special {
    background: rgba(156, 39, 176, 0.1);
}

.snack-cell:last-child {
    text-align: right;
    font-weight: 600;
}

.snack-row.bad .snack-cell:last-child {
    color: var(--snacks-bad);
}

.snack-row.special .snack-cell:last-child {
    color: var(--snacks-special);
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.analytics-item {
    background: rgba(44, 24, 16, 0.5);
    padding: 10px;
    border-radius: 5px;
    text-align: center;
}

.analytics-label {
    color: var(--snacks-text-muted);
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.analytics-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--snacks-primary);
}

.analytics-value.positive {
    color: var(--snacks-good);
}

.analytics-value.negative {
    color: var(--snacks-bad);
}

.sub-section-title {
    font-size: 1rem;
    color: var(--snacks-text);
    margin: 15px 0 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--snacks-border);
}

.match-stats-table {
    width: 100%;
    border-collapse: collapse;
}

.match-stat-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    border-bottom: 1px solid rgba(139, 0, 0, 0.2);
}

.match-stat-row.header {
    font-weight: 600;
    color: var(--snacks-text);
    background: rgba(44, 24, 16, 0.7);
}

.match-stat-cell {
    padding: 8px;
    text-align: center;
}

/* History Table */
.history-table-container {
    max-height: 250px;
    overflow-y: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.85rem;
}

.history-table th, .history-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid rgba(139, 0, 0, 0.2);
}

.history-table th {
    background: rgba(44, 24, 16, 0.7);
    color: var(--snacks-text);
    position: sticky;
    top: 0;
    z-index: 10;
}

.history-placeholder {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: var(--snacks-text-muted);
}

/* Pro View Sections (hidden by default) */
.pro-view-section {
    display: none;
}

body.pro-view-active .pro-view-section {
    display: block;
}

/* Right Column: Game Board */
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Status */
.game-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.move-timer {
    background: rgba(0, 0, 0, 0.8);
    color: var(--snacks-primary);
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: bold;
    border: 2px solid var(--snacks-primary);
}

.debt-indicator {
    background: rgba(139, 0, 0, 0.9);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: bold;
    display: none;
    animation: pulse 2s infinite;
}

.debt-indicator i {
    margin-right: 5px;
}

/* Game Board */
.game-board {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(5, 1fr);
    gap: 5px;
    aspect-ratio: 1/1;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 15px;
    border: 3px solid var(--snacks-primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.grid-cell {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #4a4a4a;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    aspect-ratio: 1/1;
}

.grid-cell:hover {
    border-color: var(--snacks-primary);
    background: rgba(139, 0, 0, 0.2);
}

.grid-cell.selected {
    border-color: var(--snacks-primary-light);
    background: rgba(199, 44, 44, 0.3);
    box-shadow: 0 0 15px rgba(199, 44, 44, 0.5);
}

.snack {
    width: 80%;
    height: 80%;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform, opacity;
    position: relative;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

/* Penalty snacks */
.snack.penalty { background: rgba(45, 27, 105, 0.5); }

/* Low value snacks */
.snack.low { background: rgba(218, 165, 32, 0.5); }

/* High value snacks */
.snack.high { background: rgba(255, 215, 0, 0.5); }

/* Special penalty items */
.snack.special { 
    background: radial-gradient(circle, rgba(139, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
    animation: pulse 2s infinite;
}

/* Mobile Game Controls (hidden on desktop) */
.mobile-game-controls {
    display: none;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 10px;
}

.mobile-control-btn {
    background: linear-gradient(135deg, var(--snacks-primary), var(--snacks-primary-dark));
    color: white;
    border: none;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid var(--snacks-primary-dark);
    font-family: var(--font-main);
}

.mobile-control-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--snacks-primary-light), var(--snacks-primary));
}

.mobile-control-btn:disabled {
    background: #333;
    color: #666;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Mobile Stats Toggle */
.mobile-stats-toggle {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

.mobile-panel-toggle {
    background: var(--snacks-primary);
    color: white;
    border: none;
    border-radius: 50px;
    width: 150px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-panel-toggle:hover {
    background: var(--snacks-primary-light);
    transform: translateY(-2px);
}

/* Mobile Stats Panel */
.mobile-stats-panel {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(44, 24, 16, 0.9);
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
    z-index: 200;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    max-height: 80vh;
    overflow-y: auto;
}

.mobile-stats-panel.active {
    transform: translateY(0);
}

.mobile-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--snacks-border);
}

.mobile-panel-header h3 {
    margin: 0;
    color: var(--snacks-primary);
    font-family: var(--font-accent);
    font-size: 1.3rem;
}

.mobile-panel-close {
    background: none;
    border: none;
    color: var(--snacks-text-muted);
    font-size: 1.2rem;
    cursor: pointer;
}

.mobile-panel-content {
    padding: 15px;
}

.mobile-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.mobile-stat-card {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    padding: 10px;
    text-align: center;
    border: 1px solid var(--snacks-border);
}

.mobile-section-title {
    font-size: 1.1rem;
    color: var(--snacks-primary);
    margin: 15px 0 10px;
    border-bottom: 1px solid var(--snacks-border);
    padding-bottom: 5px;
}

.mobile-history-list,
.mobile-achievements-list {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    padding: 10px;
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid var(--snacks-border);
}

.mobile-history-item {
    padding: 8px;
    border-bottom: 1px solid rgba(139, 0, 0, 0.2);
    font-size: 0.85rem;
}

.mobile-history-item:last-child {
    border-bottom: none;
}

.mobile-achievement-item {
    padding: 8px;
    border-bottom: 1px solid rgba(139, 0, 0, 0.2);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mobile-achievement-item:last-child {
    border-bottom: none;
}

.mobile-achievement-item i {
    color: var(--snacks-accent);
}

/* Gamble Overlay */
.gamble-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.gamble-modal {
    background: linear-gradient(135deg, var(--snacks-secondary), var(--snacks-secondary-dark));
    padding: 25px;
    border-radius: 15px;
    border: 3px solid var(--snacks-primary);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.gamble-modal h3 {
    color: var(--snacks-primary);
    margin-bottom: 15px;
    font-size: 1.5rem;
    font-family: var(--font-accent);
}

.gamble-modal p {
    margin-bottom: 10px;
    color: var(--snacks-text-muted);
}

.countdown {
    font-size: 3rem;
    color: var(--snacks-primary);
    margin: 20px 0;
    font-weight: bold;
}

.modal-btn {
    background: linear-gradient(135deg, var(--snacks-primary), var(--snacks-primary-dark));
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.modal-btn:hover {
    background: linear-gradient(135deg, var(--snacks-primary-light), var(--snacks-primary));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(139, 0, 0, 0.4);
}

/* Message Overlay */
.message-overlay {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: var(--snacks-primary);
    padding: 20px 30px;
    border-radius: 15px;
    border: 3px solid var(--snacks-primary);
    font-size: 1.5rem;
    font-weight: bold;
    z-index: 999;
    display: none;
    text-align: center;
    font-family: var(--font-accent);
}

.message-overlay.win {
    color: var(--snacks-good);
    border-color: var(--snacks-good);
}

.message-overlay.penalty {
    color: var(--snacks-bad);
    border-color: var(--snacks-bad);
}

.message-overlay.warning {
    color: var(--snacks-primary);
    border-color: var(--snacks-primary);
    animation: pulse 1s infinite;
}

.message-overlay.bankrupt {
    color: var(--snacks-bad);
    border-color: var(--snacks-bad);
    animation: shake 0.5s infinite;
}

/* Achievement Notification */
.achievement {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(139, 0, 0, 0.9);
    color: white;
    padding: 15px;
    border-radius: 10px;
    border: 2px solid var(--snacks-primary-light);
    display: none;
    z-index: 999;
    animation: slideIn 0.5s ease;
    max-width: 300px;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes shake {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    25% { transform: translate(-50%, -50%) rotate(-2deg); }
    75% { transform: translate(-50%, -50%) rotate(2deg); }
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Win/Loss Indicator */
.header-win-loss-indicator {
    position: relative;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    overflow: hidden;
    min-width: 80px;
}

.win-indicator, .loss-indicator {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 5px;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
    position: absolute;
    white-space: nowrap;
}

.win-indicator {
    background: linear-gradient(135deg, var(--snacks-good), #286c2a);
    color: white;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
}

.loss-indicator {
    background: linear-gradient(135deg, var(--snacks-bad), #b71c1c);
    color: white;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
}

@keyframes flyIn {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* RESPONSIVE STYLING */

/* Tablet */
@media (max-width: 1024px) {
    .game-dashboard {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .dashboard-left {
        order: 2;
    }
    
    .dashboard-right {
        order: 1;
    }
    
    .game-stats {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .game-controls {
        flex-direction: row;
    }
    
    .control-btn {
        flex: 1;
    }
    
    .rules-content,
    .snack-values-content,
    .analytics-content,
    .history-content {
        display: none;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .game-title {
        font-size: 1.5rem;
    }
    
    .game-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .back-button {
        margin-bottom: 10px;
    }
    
    .view-toggle {
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
        text-align: center;
    }
    
    .mobile-wallet {
        display: flex;
    }
    
    .game-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .game-controls {
        display: none;
    }
    
    .mobile-game-controls {
        display: grid;
    }
    
    .mobile-stats-toggle {
        display: block;
    }
    
    .mobile-stats-panel {
        display: block;
    }
    
    .game-board {
        padding: 10px;
    }
    
    .snack {
        font-size: 1.5rem;
    }
    
    .game-status {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .move-timer, .debt-indicator {
        text-align: center;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .match-stat-row {
        font-size: 0.8rem;
    }
    
    .gamble-modal {
        padding: 20px;
        width: 95%;
    }
    
    .message-overlay {
        font-size: 1.2rem;
        padding: 15px 20px;
        width: 90%;
        max-width: 300px;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .snacks-container {
        padding: 10px;
    }
    
    .game-title {
        font-size: 1.3rem;
    }
    
    .game-board {
        padding: 8px;
        gap: 3px;
        border-width: 2px;
    }
    
    .grid-cell {
        border-width: 1px;
        border-radius: 5px;
    }
    
    .snack {
        font-size: 1.2rem;
    }
    
    .mobile-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .achievement {
        padding: 10px;
        max-width: 250px;
        font-size: 0.9rem;
    }
}

/* Portrait/Landscape Specific Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .game-board {
        max-height: 60vh;
        aspect-ratio: auto;
        width: 60vh;
    }
    
    .mobile-stats-panel {
        max-height: 60vh;
    }
}

/* Pro View Mode Specific Styling */
body.pro-view-active .rules-content,
body.pro-view-active .snack-values-content,
body.pro-view-active .analytics-content,
body.pro-view-active .history-content {
    display: block;
}