/* Enhanced Search Page - Mobile-First Responsive Design */
:root {
    /* Search-specific color palette */
    --search-primary: #2196f3;
    --search-primary-hover: #1976d2;
    --search-secondary: #03a9f4;
    --search-accent: #00bcd4;
    --search-success: #4caf50;
    --search-warning: #ff9800;
    --search-danger: #f44336;
    
    /* Search UI colors */
    --suggestion-bg: rgba(33, 150, 243, 0.1);
    --suggestion-border: rgba(33, 150, 243, 0.2);
    --suggestion-hover: rgba(33, 150, 243, 0.15);
    
    /* Result colors */
    --result-bg: rgba(255, 255, 255, 0.05);
    --result-border: rgba(255, 255, 255, 0.1);
    --result-hover: rgba(255, 255, 255, 0.08);
    
    /* Mobile-specific variables */
    --mobile-search-padding: 16px;
    --mobile-search-gap: 12px;
    --mobile-result-height: 120px;
    --mobile-tab-height: 44px;
}

/* Mobile-first search page layout */
.search-page {
    padding: var(--mobile-search-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--safe-area-top) - var(--safe-area-bottom));
    max-width: 1400px;
    margin: 0 auto;
}

/* Enhanced Search Page Header */
.search-page-header {
    margin-bottom: 24px;
}

.search-page-title {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--search-primary), var(--search-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-page-title::before {
    content: '🔍';
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Enhanced Search Input Container */
.search-page-input-container {
    position: relative;
    margin-bottom: 20px;
}

.search-page-input {
    width: 100%;
    height: 56px;
    padding: 0 50px 0 50px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    color: var(--text-color);
    font-size: 16px;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-page-input::placeholder {
    color: var(--text-color-muted);
    font-size: 14px;
}

.search-page-input:focus {
    outline: none;
    border-color: var(--search-primary);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2), 0 4px 20px rgba(0, 0, 0, 0.15);
    background: rgba(30, 30, 30, 0.95);
}

.search-page-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-muted);
    font-size: 18px;
    transition: var(--transition);
    pointer-events: none;
}

.search-page-input:focus + .search-page-icon {
    color: var(--search-primary);
}

.search-page-clear {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    color: var(--text-color-muted);
    cursor: pointer;
    transition: var(--transition);
    display: none;
    align-items: center;
    justify-content: center;
}

.search-page-clear:hover {
    background: var(--search-danger);
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.search-page-input:not(:placeholder-shown) + .search-page-icon + .search-page-clear {
    display: flex;
}

/* Enhanced Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--mobile-border-radius);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 4px;
    display: none;
}

.search-suggestions.active {
    display: block;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.suggestions-category {
    padding: 12px 16px 8px;
    font-size: 12px;
    font-weight: 700;
    color: var(--search-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: rgba(33, 150, 243, 0.05);
    border-bottom: 1px solid var(--glass-border);
}

.suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.suggestion-item:hover {
    background: var(--suggestion-hover);
    transform: translateX(2px);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    color: var(--search-accent);
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.suggestion-text {
    flex: 1;
    font-size: 14px;
    color: var(--text-color);
}

.suggestion-text .highlight {
    background: linear-gradient(135deg, var(--search-primary), var(--search-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.suggestion-category {
    font-size: 11px;
    color: var(--text-color-muted);
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Recent and Trending Searches */
.recent-searches,
.trending-searches {
    margin-bottom: 24px;
}

.recent-searches-header,
.trending-searches-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.recent-searches-title,
.trending-searches-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.recent-searches-title::before {
    content: '🕒';
    font-size: 14px;
}

.trending-searches-title::before {
    content: '🔥';
    font-size: 14px;
}

.clear-recent {
    background: none;
    border: none;
    color: var(--text-color-muted);
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.clear-recent:hover {
    color: var(--search-danger);
    background: rgba(244, 67, 54, 0.1);
}

/* Search Pills */
.search-pills {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.search-pill {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 13px;
    color: var(--text-color);
    backdrop-filter: blur(10px);
}

.search-pill:hover {
    background: var(--result-hover);
    border-color: var(--search-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.search-pill.trending-pill {
    background: rgba(255, 152, 0, 0.1);
    border-color: rgba(255, 152, 0, 0.3);
    color: var(--search-warning);
}

.search-pill.trending-pill:hover {
    background: rgba(255, 152, 0, 0.2);
    transform: translateY(-1px) scale(1.02);
}

.search-pill-text {
    font-weight: 500;
}

.search-pill-icon {
    font-size: 11px;
    opacity: 0.7;
}

/* Search Results Section */
.search-results {
    display: none;
    animation: fadeIn 0.5s ease-out;
}

.search-results.active {
    display: block;
}

/* Search Tabs */
.search-tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
    padding: 4px;
    overflow-x: auto;
    scrollbar-width: none;
}

.search-tabs::-webkit-scrollbar {
    display: none;
}

.search-tab {
    flex: 0 0 auto;
    padding: 8px 12px;
    border: none;
    background: transparent;
    color: var(--text-color-muted);
    border-radius: calc(var(--border-radius) - 2px);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    min-height: var(--mobile-tab-height);
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-tab.active {
    background: var(--search-primary);
    color: white;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.search-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.search-tab-count {
    font-size: 10px;
    opacity: 0.8;
}

/* Search Controls */
.search-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 12px;
    flex-wrap: wrap;
}

.search-filters {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-button {
    padding: 6px 12px;
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    backdrop-filter: blur(10px);
}

.filter-button:hover {
    background: var(--result-hover);
    border-color: var(--search-primary);
    transform: translateY(-1px);
}

.filter-icon {
    font-size: 10px;
}

.search-sort {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-label {
    font-size: 12px;
    color: var(--text-color-muted);
    white-space: nowrap;
}

.sort-dropdown {
    padding: 6px 8px;
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: var(--border-radius);
    color: var(--text-color);
    font-size: 12px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23a0a0a0' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 6px center;
    background-repeat: no-repeat;
    background-size: 12px;
    padding-right: 24px;
}

/* Loading State */
.search-loading {
    display: none;
    text-align: center;
    padding: 40px;
}

.search-loading.active {
    display: block;
}

.loader {
    width: 40px;
    height: 40px;
    border: 3px solid var(--glass-border);
    border-top: 3px solid var(--search-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Content */
.results-content {
    display: none;
}

.results-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.results-section {
    margin-bottom: 32px;
}

.results-section-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--search-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.results-section-title::before {
    width: 4px;
    height: 20px;
    background: var(--search-primary);
    border-radius: 2px;
    content: '';
}

/* Game Results */
.game-results-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.game-tile {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border-radius: var(--mobile-border-radius);
    text-decoration: none;
    color: var(--text-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: var(--mobile-result-height);
    cursor: pointer;
    background-size: cover;
    background-position: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.game-tile::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    transition: var(--transition);
    z-index: 1;
}

.game-tile:hover::before {
    background: rgba(0, 0, 0, 0.1);
}

.game-tile > * {
    position: relative;
    z-index: 2;
}

.game-tile:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--search-primary);
}

.game-icon {
    font-size: 32px;
    margin-bottom: 8px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

.game-tile h3 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 4px;
    text-align: center;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
}

.player-count {
    font-size: 11px;
    color: var(--text-color-muted);
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
}

/* Sport Results */
.sport-result {
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: var(--mobile-border-radius);
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.sport-result:hover {
    background: var(--result-hover);
    border-color: var(--search-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.sport-result-info {
    margin-bottom: 12px;
}

.sport-league {
    font-size: 11px;
    color: var(--text-color-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.sport-teams {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 6px;
}

.sport-time {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--text-color-muted);
}

.sport-time.live {
    color: var(--search-danger);
    font-weight: 600;
}

.sport-time-icon {
    font-size: 10px;
}

.sport-time.live .sport-time-icon {
    animation: pulse 2s infinite;
}

.sport-result-odds {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.odd-box {
    flex: 1;
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: var(--border-radius);
    padding: 8px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.odd-box:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: var(--search-primary);
    transform: translateY(-1px);
}

.odd-team {
    font-size: 10px;
    color: var(--text-color-muted);
    margin-bottom: 2px;
    text-transform: uppercase;
}

.odd-value {
    font-size: 14px;
    font-weight: 700;
    color: var(--text-color);
}

/* Promotion Results */
.promo-result {
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: var(--mobile-border-radius);
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.promo-result:hover {
    background: var(--result-hover);
    border-color: var(--search-warning);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2);
}

.promo-result-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.promo-info {
    flex: 1;
}

.promo-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
}

.promo-description {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.4;
    margin-bottom: 6px;
}

.promo-expiry {
    font-size: 11px;
    color: var(--search-warning);
    font-weight: 600;
}

/* Provider Results */
.provider-results {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.provider-result {
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: var(--mobile-border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.provider-result:hover {
    background: var(--result-hover);
    border-color: var(--search-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.provider-logo {
    font-size: 32px;
    color: var(--search-accent);
    margin-bottom: 12px;
}

.provider-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

/* Support Results */
.support-result {
    background: var(--result-bg);
    border: 1px solid var(--result-border);
    border-radius: var(--mobile-border-radius);
    padding: 16px;
    margin-bottom: 12px;
    transition: var(--transition);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.support-result:hover {
    background: var(--result-hover);
    border-color: var(--search-success);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.support-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.support-icon {
    color: var(--search-success);
    font-size: 14px;
}

.support-snippet {
    font-size: 13px;
    color: var(--text-color-muted);
    line-height: 1.4;
    margin-bottom: 8px;
}

.support-category {
    font-size: 11px;
    color: var(--search-success);
    background: rgba(76, 175, 80, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* No Results State */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-color-muted);
}

.no-results-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-results-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 12px;
}

.no-results-text {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.no-results-tips {
    list-style: none;
    padding: 0;
    margin: 0 0 24px 0;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.no-results-tips li {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    margin-bottom: 8px;
    justify-content: center;
}

.tip-icon {
    color: var(--search-success);
    font-size: 12px;
}

.no-results-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Breakpoints */

/* Tablet Styles */
@media (min-width: 768px) {
    .search-page {
        padding: 20px;
    }
    
    .search-page-title {
        font-size: 28px;
    }
    
    .search-page-input {
        height: 60px;
        font-size: 18px;
    }
    
    .search-pills {
        gap: 10px;
    }
    
    .search-pill {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .search-tabs {
        margin-bottom: 24px;
    }
    
    .search-tab {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    .search-controls {
        margin-bottom: 24px;
    }
    
    .game-results-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }
    
    .provider-results {
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
    }
    
    .sport-result-odds {
        gap: 12px;
    }
    
    .promo-result-content {
        gap: 20px;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .search-page {
        padding: 2rem;
        display: grid;
        grid-template-columns: 350px 1fr;
        gap: 2rem;
        align-items: start;
    }
    
    .search-page-header {
        order: 1;
    }
    
    .search-results {
        order: 2;
    }
    
    .search-page-title {
        font-size: 32px;
    }
    
    .search-page-input {
        height: 64px;
        font-size: 20px;
    }
    
    .game-results-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }
    
    .provider-results {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    
    .sport-result {
        padding: 20px;
    }
    
    .promo-result {
        padding: 20px;
    }
    
    .support-result {
        padding: 20px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .search-page {
        grid-template-columns: 400px 1fr;
        gap: 2.5rem;
    }
    
    .game-results-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Enhanced animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Custom scrollbar */
.search-suggestions::-webkit-scrollbar {
    width: 4px;
}

.search-suggestions::-webkit-scrollbar-track {
    background: transparent;
}

.search-suggestions::-webkit-scrollbar-thumb {
    background: var(--search-primary);
    border-radius: 2px;
}

/* High DPI support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .game-icon,
    .provider-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .sport-time.live .sport-time-icon {
        animation: none !important;
    }
}

/* Focus accessibility */
.search-tab:focus,
.filter-button:focus,
.search-pill:focus,
.game-tile:focus,
.sport-result:focus,
.support-result:focus {
    outline: 2px solid var(--search-primary);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .search-suggestions,
    .search-tabs,
    .search-controls {
        display: none !important;
    }
    
    .search-results {
        display: block !important;
    }
    
    .results-content {
        display: block !important;
    }
}