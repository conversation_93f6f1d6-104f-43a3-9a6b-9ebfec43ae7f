/* Limbo Game Styles */

:root {
    --limbo-primary: #3498db;
    --limbo-primary-dark: #2980b9;
    --limbo-primary-light: #85c1e9;
    --limbo-secondary: #9b59b6;
    --limbo-secondary-light: #bb8fce;
    --limbo-success: #2ecc71;
    --limbo-danger: #e74c3c;
    --limbo-warning: #f39c12;
    --limbo-info: #17a2b8;
    --limbo-over: #2ecc71;
    --limbo-under: #e74c3c;
}

/* Main Container */
.limbo-container {
    padding: 15px;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Dashboard Layout */
.game-dashboard {
    display: grid;
    grid-template-columns: minmax(280px, 320px) 1fr;
    gap: 20px;
    width: 100%;
}

/* Mobile Wallet (only shown on mobile) */
.mobile-wallet {
    display: none;
    background: linear-gradient(135deg, #1e1e1e, #2a2a2a);
    border-radius: 10px;
    padding: 12px 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    justify-content: space-between;
    align-items: center;
}

.wallet-balance, .mobile-last-win {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--limbo-primary);
    font-weight: 600;
    font-size: 1rem;
}

.wallet-balance i, .mobile-last-win i {
    font-size: 1rem;
}

/* Header Styles */
.limbo-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    margin-right: auto;
}

.back-link:hover {
    color: var(--limbo-primary-light);
}

.game-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--limbo-primary-dark), var(--limbo-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.view-toggle {
    display: flex;
    gap: 5px;
    margin-left: auto;
}

.view-btn {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 8px 15px;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: linear-gradient(135deg, var(--limbo-primary-dark), var(--limbo-primary));
    color: white;
    border-color: var(--limbo-primary);
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.5);
}

.view-btn:hover:not(.active) {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    color: var(--text-color);
}

/* Dashboard Left Column */
.dashboard-left {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: fit-content;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #3a3a3a;
    overflow: visible;
}

/* Balance Display */
.balance-display {
    background: linear-gradient(135deg, #9e8a21, #ffd700);
    border-radius: 8px;
    padding: 12px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.balance-label {
    font-size: 0.9rem;
    color: #1a1625;
    margin-bottom: 5px;
    font-weight: 600;
}

.balance-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a1625;
}

.win-loss-indicator {
    position: absolute;
    top: -10px;
    right: -10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
}

.win-loss-indicator.win {
    background: var(--limbo-success);
    color: white;
}

.win-loss-indicator.loss {
    background: var(--limbo-danger);
    color: white;
}

/* Betting Controls */
.betting-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.bet-amount-control {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bet-amount-control label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.bet-input-group {
    display: flex;
    align-items: center;
}

.bet-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.bet-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.bet-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
    font-weight: 600;
}

/* Bet Presets */
.bet-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 10px;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.preset-btn.max {
    background: linear-gradient(135deg, #3d3d3d, #444);
    color: var(--limbo-primary);
    font-weight: 600;
}

/* Target Controls */
.target-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 12px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 8px;
    border: 1px solid #3d3d3d;
}

.target-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.target-input-group label {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.multiplier-input-group {
    display: flex;
    align-items: center;
}

.multiplier-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.multiplier-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.multiplier-input {
    flex: 1;
    background: linear-gradient(135deg, #222, #2a2a2a);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 6px 10px;
    margin: 0 5px;
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
    font-weight: 600;
}

/* Multiplier Presets */
.multiplier-presets {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.multiplier-preset-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 6px;
    padding: 5px 0;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 40px;
    text-align: center;
}

.multiplier-preset-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.multiplier-preset-btn.active {
    background: linear-gradient(135deg, var(--limbo-primary-dark), var(--limbo-primary));
    color: white;
    border-color: var(--limbo-primary);
}

/* Direction Selection */
.direction-controls {
    margin-bottom: 15px;
}

.direction-selection {
    display: flex !important;
    gap: 10px;
}

.direction-btn {
    flex: 1;
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
}

.direction-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
}

.direction-btn.active.over-btn {
    background: linear-gradient(135deg, var(--limbo-over), #27ae60);
    color: white;
    border-color: var(--limbo-over);
}

.direction-btn.active.under-btn {
    background: linear-gradient(135deg, var(--limbo-under), #c0392b);
    color: white;
    border-color: var(--limbo-under);
}

.direction-icon {
    font-size: 1.5rem;
}

.direction-label {
    font-weight: 600;
    font-size: 1rem;
}

.direction-info {
    text-align: center;
    font-size: 0.8rem;
}

.probability {
    font-weight: 600;
}

.payout {
    opacity: 0.8;
}

/* Game Actions */
.game-actions {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 10px;
}

.play-btn {
    background: linear-gradient(135deg, var(--limbo-primary-dark), var(--limbo-primary)) !important;
    color: white !important;
    border-color: var(--limbo-primary-dark) !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    padding: 12px 20px !important;
}

.auto-btn {
    background: linear-gradient(135deg, var(--limbo-secondary), var(--limbo-secondary-light)) !important;
    color: white !important;
    border-color: var(--limbo-secondary) !important;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem !important;
    background: linear-gradient(135deg, #2a2a2a, #333333) !important;
    color: var(--text-secondary) !important;
    border: 1px solid #444 !important;
}

/* Dashboard Right Column */
.dashboard-right {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Game Status */
.game-status {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    padding: 12px 15px;
    border-radius: 10px;
    text-align: center;
    font-size: 1rem;
    color: var(--text-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    margin-bottom: 0;
    border: 1px solid #3d3d3d;
}

.game-status.success {
    background: linear-gradient(135deg, #1c5928, #28a745);
    color: white;
    border: 1px solid #28a745;
}

.game-status.warning {
    background: linear-gradient(135deg, #c69500, #ffc107);
    color: #000;
    border: 1px solid #ffc107;
}

.game-status.danger {
    background: linear-gradient(135deg, #9c1f2d, #dc3545);
    color: white;
    border: 1px solid #dc3545;
}

/* Result Display */
.result-display {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #3d3d3d;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.outcome-container {
    text-align: center;
}

.outcome-label {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 10px;
}

.outcome-value {
    font-size: 3rem;
    font-weight: 700;
    color: var(--limbo-primary);
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
}

.outcome-value.win {
    color: var(--limbo-success);
    text-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
}

.outcome-value.loss {
    color: var(--limbo-danger);
    text-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

.outcome-status {
    font-size: 1rem;
    color: var(--text-secondary);
}

/* Visual Multiplier Display */
.multiplier-visual {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #3a3a3a;
}

.multiplier-scale {
    position: relative;
    height: 40px;
    margin: 10px 0;
}

.scale-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--limbo-success), var(--limbo-warning), var(--limbo-danger));
    transform: translateY(-50%);
}

.target-marker, .result-marker {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--limbo-primary);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
}

.target-marker {
    left: 20%;
    background: var(--limbo-warning);
    color: #000;
}

.result-marker {
    left: 50%;
    background: var(--limbo-primary);
    opacity: 0;
    transition: all 0.5s ease;
}

.result-marker.show {
    opacity: 1;
}

.scale-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 10px;
}

/* Mobile Controls (hidden on desktop) */
.mobile-controls {
    display: none;
    flex-direction: column;
    gap: 10px;
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid #3d3d3d;
    margin-top: 15px;
}

.mobile-direction-buttons {
    display: flex;
    gap: 10px;
}

.mobile-direction-btn {
    flex: 1;
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.mobile-direction-btn.active.over-btn {
    background: linear-gradient(135deg, var(--limbo-over), #27ae60);
    color: white;
    border-color: var(--limbo-over);
}

.mobile-direction-btn.active.under-btn {
    background: linear-gradient(135deg, var(--limbo-under), #c0392b);
    color: white;
    border-color: var(--limbo-under);
}

.mobile-play-actions {
    display: flex;
    gap: 10px;
}

.mobile-play-btn, .mobile-auto-btn {
    flex: 1;
    padding: 12px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

/* Statistics Panel */
.statistics-panel {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.stats-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.toggle-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    color: var(--text-color);
}

.stats-content {
    padding: 15px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-card {
    background: linear-gradient(135deg, #1e1e1e, #262626);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #3a3a3a;
    text-align: center;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.stat-value {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--limbo-primary);
}

.stat-value.positive {
    color: var(--limbo-success);
}

.stat-value.negative {
    color: var(--limbo-danger);
}

/* Provably Fair */
.provably-fair {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.fair-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.fair-content {
    padding: 15px;
}

.fair-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.fair-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.fair-item label {
    min-width: 140px;
    color: var(--text-secondary);
}

.seed-value {
    flex: 1;
    background: linear-gradient(135deg, #1e1e1e, #262626);
    padding: 6px 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.8rem;
    color: var(--text-color);
    word-break: break-all;
}

.seed-btn {
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border: 1px solid #444;
    border-radius: 4px;
    padding: 6px 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.seed-btn:hover {
    background: linear-gradient(135deg, #333333, #3d3d3d);
    color: var(--text-color);
}

.verify-button {
    width: 100%;
    background: linear-gradient(135deg, var(--limbo-success), #27ae60) !important;
    color: white !important;
    border: none !important;
    padding: 10px 15px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
}

.verify-button:disabled {
    background: linear-gradient(135deg, #333, #444) !important;
    color: var(--text-secondary) !important;
    cursor: not-allowed !important;
}

/* Game History */
.game-history {
    background: linear-gradient(135deg, #252525, #2d2d2d);
    border-radius: 10px;
    border: 1px solid #3d3d3d;
    margin-top: 20px;
    overflow: hidden;
}

.history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: 0;
    font-size: 1.2rem;
    color: var(--text-color);
    background: linear-gradient(135deg, #2a2a2a, #333333);
    border-bottom: 1px solid #3d3d3d;
}

.history-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 0 15px 15px;
}

.history-table-container {
    width: 100%;
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    min-width: 600px;
}

.history-table th,
.history-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #3d3d3d;
}

.history-table th {
    color: var(--text-secondary);
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
}

.history-table td {
    color: var(--text-color);
    font-size: 0.9rem;
}

.history-table .win {
    color: var(--limbo-success);
}

.history-table .loss {
    color: var(--limbo-danger);
}

.history-table .over {
    color: var(--limbo-over);
}

.history-table .under {
    color: var(--limbo-under);
}

.history-placeholder {
    text-align: center;
    color: var(--text-secondary);
    padding: 20px;
    font-style: italic;
}

/* Header Win Loss Indicator */
.header-win-loss-indicator {
    position: relative;
    height: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    overflow: hidden;
    min-width: 80px;
}

.header-win-indicator, .header-loss-indicator {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 5px;
    animation: flyIn 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
    position: absolute;
    white-space: nowrap;
}

.header-win-indicator {
    background: linear-gradient(135deg, var(--limbo-success), #27ae60);
    color: white;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
}

.header-loss-indicator {
    background: linear-gradient(135deg, var(--limbo-danger), #c0392b);
    color: white;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow: auto;
    animation: fadeIn 0.3s;
}

.modal-content {
    background: linear-gradient(135deg, #2a2a2a, #3a3a3a);
    margin: 10% auto;
    padding: 25px;
    border: 1px solid #4a4a4a;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    animation: slideIn 0.3s;
}

.close-modal {
    color: var(--text-secondary);
    float: right;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-modal:hover {
    color: var(--limbo-primary);
}

.rules-content {
    margin-top: 20px;
}

.rules-content h3 {
    margin-top: 20px;
    color: var(--limbo-primary);
    font-size: 1.2rem;
}

.rules-content ul, .rules-content ol {
    padding-left: 20px;
}

.rules-content li {
    margin-bottom: 8px;
}

/* Pro View Mode Special Styling */
.pro-view-active .game-dashboard {
    grid-template-columns: 1fr;
}

.pro-view-active .dashboard-left {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
}

.pro-view-active .betting-controls,
.pro-view-active .target-controls {
    height: 100%;
}

.pro-view-active .statistics-panel {
    display: block;
}

/* Animations */
@keyframes flyIn {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* RESPONSIVE STYLES */

/* Tablet View */
@media (max-width: 1024px) {
    .game-dashboard {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .dashboard-left {
        max-width: 100%;
        width: 100%;
    }
    
    .betting-controls, 
    .target-controls, 
    .game-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .bet-amount-control,
    .bet-presets,
    .target-input-group,
    .multiplier-presets {
        grid-column: 1 / -1;
    }
    
    .direction-selection {
        grid-column: 1 / -1;
        gap: 15px;
    }
    
    .outcome-value {
        font-size: 2.5rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Mobile View */
@media (max-width: 768px) {
    .limbo-container {
        padding: 10px;
    }
    
    .limbo-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 10px;
    }
    
    .back-link {
        margin-right: 0;
    }
    
    .game-title {
        font-size: 1.8rem;
        margin: 5px 0;
    }
    
    .view-toggle {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
    }
    
    .view-btn {
        flex: 1;
        text-align: center;
    }
    
    .mobile-wallet {
        display: flex;
    }
    
    .dashboard-left {
        padding: 12px;
    }
    
    .balance-value {
        font-size: 1.5rem;
    }
    
    .bet-presets,
    .multiplier-presets {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
    }
    
    .preset-btn,
    .multiplier-preset-btn {
        font-size: 0.8rem;
        padding: 8px 0;
    }
    
    .direction-selection {
        flex-direction: column;
        gap: 8px;
    }
    
    .direction-btn {
        flex-direction: row;
        justify-content: space-between;
        padding: 10px 15px;
    }
    
    .direction-info {
        text-align: right;
    }
    
    .outcome-value {
        font-size: 2rem;
    }
    
    .multiplier-visual {
        padding: 10px;
    }
    
    .multiplier-scale {
        height: 30px;
    }
    
    /* Show mobile controls */
    .mobile-controls {
        display: flex;
    }
    
    /* Hide desktop controls on mobile */
    .dashboard-left .game-actions {
        display: none;
    }
    
    .direction-selection {
        display: flex !important;
        margin-bottom: 20px;
    }
    
    .header-win-loss-indicator {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .fair-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .fair-item label {
        min-width: auto;
    }
    
    .stats-title, .fair-title, .history-title {
        padding: 12px 15px;
        font-size: 1.1rem;
    }
    
    .stats-content, .fair-content, .history-content {
        padding: 0 10px 10px;
    }
}

/* Small Mobile View */
@media (max-width: 480px) {
    .limbo-container {
        padding: 8px;
    }
    
    .game-title {
        font-size: 1.5rem;
    }
    
    .betting-controls, 
    .target-controls {
        grid-template-columns: 1fr;
        padding: 10px;
    }
    
    .bet-presets,
    .multiplier-presets {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }
    
    .bet-input-group,
    .multiplier-input-group {
        width: 100%;
    }
    
    .outcome-value {
        font-size: 1.8rem;
    }
    
    .result-display {
        padding: 15px;
    }
    
    .game-status {
        font-size: 0.9rem;
        padding: 10px;
    }
    
    .mobile-direction-btn {
        padding: 10px;
        font-size: 0.8rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .history-table {
        min-width: 480px;
    }
}

/* Portrait/Landscape Specific Adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .game-dashboard {
        grid-template-columns: 320px 1fr;
    }
    
    .mobile-wallet {
        display: none;
    }
    
    .dashboard-left .game-actions {
        display: grid;
    }
    
    .mobile-controls {
        display: flex;
    }
    
    .direction-selection {
        display: flex !important;
    }
}

/* Pro View Mode Special Mobile Styling */
@media (max-width: 768px) {
    .pro-view-active .dashboard-left {
        grid-template-columns: 1fr;
    }
    
    .pro-view-active .result-display {
        margin-top: 0;
    }
    
    .pro-view-active .outcome-value {
        font-size: 1.8rem;
    }
}

/* Screen Rotation Support */
@media screen and (orientation: portrait) {
    .result-display {
        max-width: 100%;
        width: 100%;
    }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
    .result-display {
        max-height: 60vh;
    }
    
    .statistics-panel, .provably-fair, .game-history {
        max-height: 50vh;
        overflow-y: auto;
    }
}