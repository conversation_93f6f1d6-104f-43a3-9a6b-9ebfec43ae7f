# Diamond Hunter - Game Implementation Details

## Game Concept
Diamond Hunter is a provably fair mining game where players explore a grid to find diamonds while avoiding bombs. Players choose their risk level by selecting how many bombs to place in the grid - more bombs means higher potential rewards but lower chances of success.

## Game Mechanics
- **Grid Structure**: 5×5 grid (25 squares)
- **Possible Outcomes**: Diamonds (rewards) or Bombs (game over)
- **Player Control**: Select number of bombs (1-24) before starting
- **Risk/Reward Balance**: Higher bomb count = higher multiplier per diamond
- **Win Condition**: Successfully cash out after finding diamonds
- **Lose Condition**: Hitting a bomb

## Provably Fair System
The game implements a provably fair system that ensures complete transparency:
1. **Server Seed**: Generated at the start, only its hash is revealed
2. **Client Seed**: Can be provided by the player or auto-generated
3. **Deterministic Algorithm**: Bomb placement is determined by combining both seeds
4. **Verification**: After the game ends, the server seed is revealed, allowing players to verify the bomb locations were predetermined

## Multiplier Calculation
The multiplier increases with each discovered diamond, calculated as:
- Base multiplier = (Total Squares / Diamond Squares) × (1 - house edge)
- Final multiplier = Base multiplier ^ Number of diamonds found

## Technical Implementation
- **Frontend**: HTML, CSS, and vanilla JavaScript
- **State Management**: Client-side for demonstration purposes
- **Randomization**: Deterministic pseudo-random generator using combined seeds
- **Verification**: Functions to independently verify game fairness

## Future Enhancements
- Server-side validation
- Multiple grid size options
- Animation improvements
- Social features and leaderboards