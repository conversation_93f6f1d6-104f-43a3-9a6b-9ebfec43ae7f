<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RPS Showdown - Provably Fair Rock Paper Scissors</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="rps-showdown.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" type="image/x-icon">
</head>
<body>
    <!-- Top Navigation Bar -->
    <header class="header">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" id="menuToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html" class="logo-link">
                        <img src="https://public.youware.com/users-website-assets/prod/01d9d3db-bf3e-4184-8401-17c0df4da3f3/tokens-8620520_1280.png" alt="GoldenAura" class="logo-img">
                        <span class="logo-text">GoldenAura</span>
                    </a>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search games, sports, promotions..." id="searchInput">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            
            <div class="header-right">
                <button class="btn btn-login">Login</button>
                <button class="btn btn-register">Register</button>
            </div>
        </div>
    </header>

    <div class="rps-container">
        <div class="game-header">
            <a href="index.html" class="back-link">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Games</span>
            </a>
            <div>
                <h1 class="game-title">RPS SHOWDOWN</h1>
                <p class="game-subtitle">✊ Provably Fair • Transparent • Best of 5 ✋</p>
            </div>
            <div class="view-mode-toggle">
                <button id="standardViewBtn" class="active">Standard</button>
                <button id="proViewBtn">Pro View</button>
            </div>
        </div>

        <!-- Pro View Stats -->
        <div class="pro-view-stats">
            <div class="pro-view-title">
                <i class="fas fa-brain"></i> Advanced Strategy Analytics
            </div>
            
            <div class="pro-stats-grid">
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Win Streak</div>
                    <div class="pro-stat-value" id="winStreak">0</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Best Streak</div>
                    <div class="pro-stat-value" id="bestStreak">0</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Rounds Won</div>
                    <div class="pro-stat-value" id="roundsWon">0</div>
                </div>
                <div class="pro-stat-item">
                    <div class="pro-stat-label">Total Profit</div>
                    <div class="pro-stat-value" id="totalProfit">0 GA</div>
                </div>
            </div>

            <div class="pro-view-sections">
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-chart-line"></i> Your Patterns
                    </div>
                    
                    <div class="pattern-analysis" id="patternAnalysis">
                        <div class="pattern-item">
                            <div class="pattern-icon">✊</div>
                            <div class="pattern-label">Rock</div>
                            <div class="pattern-value" id="rockUsage">33%</div>
                        </div>
                        <div class="pattern-item">
                            <div class="pattern-icon">✋</div>
                            <div class="pattern-label">Paper</div>
                            <div class="pattern-value" id="paperUsage">33%</div>
                        </div>
                        <div class="pattern-item">
                            <div class="pattern-icon">✌️</div>
                            <div class="pattern-label">Scissors</div>
                            <div class="pattern-value" id="scissorsUsage">33%</div>
                        </div>
                    </div>
                    
                    <div class="ai-predictor">
                        <div class="predictor-title">AI Strategy Analysis</div>
                        <div class="prediction-grid">
                            <div class="prediction-item">
                                <div class="prediction-choice">✊</div>
                                <div class="prediction-probability" id="aiRockProb">33.3%</div>
                            </div>
                            <div class="prediction-item">
                                <div class="prediction-choice">✋</div>
                                <div class="prediction-probability" id="aiPaperProb">33.3%</div>
                            </div>
                            <div class="prediction-item">
                                <div class="prediction-choice">✌️</div>
                                <div class="prediction-probability" id="aiScissorsProb">33.3%</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-trophy"></i> Performance Charts
                    </div>
                    
                    <div class="chart-container">
                        <div class="chart-bar">
                            <span class="chart-label">Wins</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="winsChart" style="width: 0%"></div>
                            </div>
                            <span class="chart-value" id="winsPercent">0%</span>
                        </div>
                        <div class="chart-bar">
                            <span class="chart-label">Ties</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="tiesChart" style="width: 0%"></div>
                            </div>
                            <span class="chart-value" id="tiesPercent">0%</span>
                        </div>
                        <div class="chart-bar">
                            <span class="chart-label">Profit</span>
                            <div class="chart-fill">
                                <div class="chart-progress" id="profitChart" style="width: 50%"></div>
                            </div>
                            <span class="chart-value" id="profitPercent">0%</span>
                        </div>
                    </div>
                    
                    <div class="game-history" id="gameHistory">
                        <div class="history-entry">
                            <span class="history-choices">No games played yet</span>
                            <span class="history-result">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="pro-section">
                    <div class="pro-section-title">
                        <i class="fas fa-lightbulb"></i> Strategy Matrix
                    </div>
                    
                    <div class="strategy-matrix">
                        <div class="matrix-cell">
                            <div class="matrix-choice">vs ✊</div>
                            <div class="matrix-percentage" id="vsRockWin">0%</div>
                        </div>
                        <div class="matrix-cell">
                            <div class="matrix-choice">vs ✋</div>
                            <div class="matrix-percentage" id="vsPaperWin">0%</div>
                        </div>
                        <div class="matrix-cell">
                            <div class="matrix-choice">vs ✌️</div>
                            <div class="matrix-percentage" id="vsScissorsWin">0%</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 0.75rem; padding: 0.5rem; background: rgba(0,0,0,0.2); border-radius: 6px;">
                        <div style="font-size: 0.8rem; color: var(--rps-yellow); font-weight: 600; margin-bottom: 0.5rem;">
                            <i class="fas fa-robot"></i> Strategy Tip
                        </div>
                        <div id="strategyTip" style="font-size: 0.75rem; line-height: 1.4;">
                            Play more rounds to get personalized strategy recommendations!
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="pro-controls">
                <button class="pro-btn" id="analyzeBtn">
                    <i class="fas fa-search"></i> Analyze
                </button>
                <button class="pro-btn" id="optimizeBtn">
                    <i class="fas fa-magic"></i> Optimize
                </button>
                <button class="pro-btn" id="resetStatsBtn">
                    <i class="fas fa-refresh"></i> Reset Stats
                </button>
                <button class="pro-btn" id="exportBtn">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Player Stats -->
            <div class="stats-panel">
                <h3 class="panel-title">🎯 Player Stats</h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">Total Games</div>
                        <div class="stat-value" id="totalGames">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Games Won</div>
                        <div class="stat-value" id="gamesWon">0</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Win Rate</div>
                        <div class="stat-value" id="winRate">0%</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-label">Balance</div>
                        <div class="stat-value" id="playerBalance">1000 GA</div>
                    </div>
                </div>

                <div class="rules-section">
                    <button class="rules-toggle" onclick="toggleRules()">
                        <i class="fas fa-info-circle"></i> How to Play & Fairness
                    </button>
                    <div class="rules-content" id="rulesContent" style="display: none;">
                        <p><strong>Game Rules:</strong></p>
                        <ul>
                            <li>Rock beats Scissors</li>
                            <li>Scissors beats Paper</li>
                            <li>Paper beats Rock</li>
                            <li>First to win 3 rounds wins the game</li>
                            <li>Win games to earn GA currency</li>
                        </ul>
                        <p><strong>Provably Fair:</strong></p>
                        <ul>
                            <li>Each game uses cryptographic seeds to ensure fairness</li>
                            <li>Client seed: generated by your browser</li>
                            <li>Server seed: pre-committed and hashed</li>
                            <li>Verify any game outcome using the verification tool</li>
                            <li>AI opponent has exactly 1/3 probability for each choice</li>
                        </ul>
                        <p><strong>Pro View Features:</strong></p>
                        <ul>
                            <li>Pattern analysis of your play style</li>
                            <li>Win streak and performance tracking</li>
                            <li>Strategy optimization recommendations</li>
                            <li>Detailed game history and statistics</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Game Area -->
            <div class="game-area">
                <div class="scoreboard">
                    <div class="score-display player">
                        <div class="score-label">YOU</div>
                        <div class="score-value" id="playerScore">0</div>
                    </div>
                    
                    <div class="best-of">
                        <div class="score-label">BEST OF</div>
                        <div class="score-value">5</div>
                    </div>
                    
                    <div class="score-display opponent">
                        <div class="score-label">AI</div>
                        <div class="score-value" id="opponentScore">0</div>
                    </div>
                </div>
                
                <div class="battle-area">
                    <div class="player-side">
                        <div class="player-label">YOUR CHOICE</div>
                        <div class="hand-display" id="playerHand">
                            <i class="fas fa-question"></i>
                        </div>
                    </div>
                    
                    <div class="versus">VS</div>
                    
                    <div class="opponent-side">
                        <div class="opponent-label">AI CHOICE</div>
                        <div class="hand-display" id="opponentHand">
                            <i class="fas fa-question"></i>
                        </div>
                    </div>
                </div>
                
                <div class="result-display" id="resultDisplay">
                    Make your choice to start the game!
                </div>
                
                <div class="bet-controls">
                    <div class="bet-control-group">
                        <button class="bet-adjust-btn" onclick="adjustBet(-10)">-</button>
                        <span class="bet-amount" id="betAmount">50 GA</span>
                        <button class="bet-adjust-btn" onclick="adjustBet(10)">+</button>
                    </div>
                    <div class="bet-control-group">
                        <span style="font-size: 0.8rem;">Win:</span>
                        <span class="potential-win" id="potentialWin">100 GA</span>
                    </div>
                </div>
                
                <div class="choice-buttons">
                    <button class="choice-btn rock" id="rockBtn" onclick="makeChoice('rock')">
                        <div class="icon">✊</div>
                        <div>Rock</div>
                    </button>
                    
                    <button class="choice-btn paper" id="paperBtn" onclick="makeChoice('paper')">
                        <div class="icon">✋</div>
                        <div>Paper</div>
                    </button>
                    
                    <button class="choice-btn scissors" id="scissorsBtn" onclick="makeChoice('scissors')">
                        <div class="icon">✌️</div>
                        <div>Scissors</div>
                    </button>
                </div>
                
                <div class="game-controls">
                    <button class="control-btn" id="resetBtn" onclick="resetGame()">
                        <i class="fas fa-redo"></i> New Game
                    </button>
                </div>
            </div>

            <!-- Fairness & Verification -->
            <div class="fairness-panel">
                <h3 class="panel-title">🔒 Provably Fair System</h3>
                
                <div class="fairness-info">
                    <h4 style="color: white; margin-bottom: 0.75rem; font-size: 0.9rem;">Current Round Seeds</h4>
                    
                    <div style="margin-bottom: 0.5rem;">
                        <strong style="color: var(--rps-blue); font-size: 0.8rem;">Client Seed:</strong>
                        <div class="seed-display" id="clientSeed">--</div>
                    </div>
                    
                    <div style="margin-bottom: 0.5rem;">
                        <strong style="color: var(--rps-purple); font-size: 0.8rem;">Server Seed (Hashed):</strong>
                        <div class="seed-display" id="hashedServerSeed">--</div>
                    </div>
                    
                    <div>
                        <strong style="color: var(--rps-green); font-size: 0.8rem;">Nonce:</strong>
                        <div class="seed-display" id="nonce">0</div>
                    </div>
                </div>

                <div class="verification-tool">
                    <h4 style="color: white; margin-bottom: 0.75rem; font-size: 0.9rem;">Verify Past Round</h4>
                    
                    <input type="text" class="verify-input" id="verifyClientSeed" placeholder="Enter client seed">
                    <input type="text" class="verify-input" id="verifyServerSeed" placeholder="Enter revealed server seed">
                    <input type="number" class="verify-input" id="verifyNonce" placeholder="Enter nonce">
                    
                    <button class="verify-btn" onclick="verifyResult()">
                        <i class="fas fa-check-circle"></i> Verify Result
                    </button>
                    
                    <div id="verificationResult" style="margin-top: 0.75rem; padding: 0.75rem; background: rgba(0,0,0,0.2); border-radius: 8px; display: none; font-size: 0.8rem;">
                        <!-- Verification result will be displayed here -->
                    </div>
                </div>

                <div class="history-container">
                    <h4 style="color: white; margin-bottom: 0.75rem; font-size: 0.9rem;">Round History</h4>
                    <div id="roundHistory">
                        <!-- Round history will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification"></div>

    <script src="assets/js/script.js"></script>
    <script src="rps-showdown.js"></script>
    <script>
        // Enhanced RPS Showdown Game with Pro View Features
        class EnhancedRPSGame extends RPSGame {
            constructor() {
                super();
                
                // Pro View variables
                this.viewMode = localStorage.getItem('rpsViewMode') || 'standard';
                this.winStreak = 0;
                this.bestStreak = 0;
                this.roundsWon = 0;
                this.totalProfit = 0;
                this.sessionStartTime = Date.now();
                
                // Pattern tracking
                this.playerChoiceHistory = [];
                this.aiChoiceHistory = [];
                this.roundResultHistory = [];
                this.choiceFrequency = { rock: 0, paper: 0, scissors: 0 };
                this.vsChoiceWinRate = { 
                    vsRock: { wins: 0, total: 0 },
                    vsPaper: { wins: 0, total: 0 },
                    vsScissors: { wins: 0, total: 0 }
                };
                
                // Strategy analysis
                this.strategyTips = [
                    "Try varying your choices to be less predictable",
                    "Look for patterns in AI behavior",
                    "Rock is often chosen first by beginners",
                    "Paper beats rock - counter common first choices",
                    "Scissors can surprise opponents expecting rock",
                    "Analyze your win rate against each AI choice",
                    "Avoid repeating the same choice too often",
                    "Consider the psychological aspect of choice making"
                ];
                
                // Initialize Pro View elements
                this.initProViewElements();
                this.attachProViewEventListeners();
                this.setViewMode(this.viewMode);
                this.updateProViewDisplay();
            }
            
            initProViewElements() {
                // View mode toggle buttons
                this.standardViewBtn = document.getElementById('standardViewBtn');
                this.proViewBtn = document.getElementById('proViewBtn');
                
                // Pro View statistics displays
                this.winStreakDisplay = document.getElementById('winStreak');
                this.bestStreakDisplay = document.getElementById('bestStreak');
                this.roundsWonDisplay = document.getElementById('roundsWon');
                this.totalProfitDisplay = document.getElementById('totalProfit');
                
                // Pattern analysis displays
                this.rockUsageDisplay = document.getElementById('rockUsage');
                this.paperUsageDisplay = document.getElementById('paperUsage');
                this.scissorsUsageDisplay = document.getElementById('scissorsUsage');
                
                // AI prediction displays
                this.aiRockProbDisplay = document.getElementById('aiRockProb');
                this.aiPaperProbDisplay = document.getElementById('aiPaperProb');
                this.aiScissorsProbDisplay = document.getElementById('aiScissorsProb');
                
                // Chart displays
                this.winsChartDisplay = document.getElementById('winsChart');
                this.tiesChartDisplay = document.getElementById('tiesChart');
                this.profitChartDisplay = document.getElementById('profitChart');
                this.winsPercentDisplay = document.getElementById('winsPercent');
                this.tiesPercentDisplay = document.getElementById('tiesPercent');
                this.profitPercentDisplay = document.getElementById('profitPercent');
                
                // Strategy matrix displays
                this.vsRockWinDisplay = document.getElementById('vsRockWin');
                this.vsPaperWinDisplay = document.getElementById('vsPaperWin');
                this.vsScissorsWinDisplay = document.getElementById('vsScissorsWin');
                
                // Game history and strategy tip
                this.gameHistoryDisplay = document.getElementById('gameHistory');
                this.strategyTipDisplay = document.getElementById('strategyTip');
                
                // Pro View buttons
                this.analyzeBtn = document.getElementById('analyzeBtn');
                this.optimizeBtn = document.getElementById('optimizeBtn');
                this.resetStatsBtn = document.getElementById('resetStatsBtn');
                this.exportBtn = document.getElementById('exportBtn');
            }
            
            attachProViewEventListeners() {
                // View mode toggle
                if (this.standardViewBtn) {
                    this.standardViewBtn.addEventListener('click', () => this.setViewMode('standard'));
                }
                
                if (this.proViewBtn) {
                    this.proViewBtn.addEventListener('click', () => this.setViewMode('pro'));
                }
                
                // Pro View buttons
                if (this.analyzeBtn) {
                    this.analyzeBtn.addEventListener('click', () => this.analyzePerformance());
                }
                
                if (this.optimizeBtn) {
                    this.optimizeBtn.addEventListener('click', () => this.optimizeStrategy());
                }
                
                if (this.resetStatsBtn) {
                    this.resetStatsBtn.addEventListener('click', () => this.resetProStats());
                }
                
                if (this.exportBtn) {
                    this.exportBtn.addEventListener('click', () => this.exportGameData());
                }
            }
            
            setViewMode(mode) {
                this.viewMode = mode;
                localStorage.setItem('rpsViewMode', mode);
                
                if (mode === 'standard') {
                    document.body.classList.remove('pro-view-active');
                    this.standardViewBtn.classList.add('active');
                    this.proViewBtn.classList.remove('active');
                } else {
                    document.body.classList.add('pro-view-active');
                    this.standardViewBtn.classList.remove('active');
                    this.proViewBtn.classList.add('active');
                    
                    // Update Pro View displays
                    this.updateProViewDisplay();
                }
            }
            
            // Override makeChoice to track additional statistics
            makeChoice(choice) {
                // Call parent method
                const result = super.makeChoice(choice);
                
                if (result) {
                    // Track choice patterns
                    this.playerChoiceHistory.push(choice);
                    this.aiChoiceHistory.push(this.opponentChoice);
                    this.choiceFrequency[choice]++;
                    
                    // Track round result
                    const outcome = this.determineOutcome(choice, this.opponentChoice);
                    this.roundResultHistory.push({
                        playerChoice: choice,
                        aiChoice: this.opponentChoice,
                        outcome: outcome,
                        timestamp: Date.now()
                    });
                    
                    // Update vs choice win rates
                    const vsKey = 'vs' + this.opponentChoice.charAt(0).toUpperCase() + this.opponentChoice.slice(1);
                    if (this.vsChoiceWinRate[vsKey]) {
                        this.vsChoiceWinRate[vsKey].total++;
                        if (outcome === 'win') {
                            this.vsChoiceWinRate[vsKey].wins++;
                        }
                    }
                    
                    // Update streaks and rounds won
                    if (outcome === 'win') {
                        this.winStreak++;
                        this.roundsWon++;
                        this.bestStreak = Math.max(this.bestStreak, this.winStreak);
                        this.totalProfit += this.currentBet * this.betMultiplier - this.currentBet;
                    } else if (outcome === 'lose') {
                        this.winStreak = 0;
                        this.totalProfit -= this.currentBet;
                    } else {
                        // Tie - no profit/loss change
                    }
                    
                    // Update Pro View displays
                    this.updateProViewDisplay();
                    this.updateGameHistory();
                    this.updateStrategyTip();
                }
                
                return result;
            }
            
            // Override endGame to track game-level statistics
            endGame() {
                // Call parent method
                super.endGame();
                
                // Update Pro View displays after game ends
                this.updateProViewDisplay();
            }
            
            // Update Pro View displays
            updateProViewDisplay() {
                if (this.viewMode !== 'pro') return;
                
                // Update main stats
                if (this.winStreakDisplay) {
                    this.winStreakDisplay.textContent = this.winStreak;
                }
                
                if (this.bestStreakDisplay) {
                    this.bestStreakDisplay.textContent = this.bestStreak;
                }
                
                if (this.roundsWonDisplay) {
                    this.roundsWonDisplay.textContent = this.roundsWon;
                }
                
                if (this.totalProfitDisplay) {
                    this.totalProfitDisplay.textContent = (this.totalProfit >= 0 ? '+' : '') + this.totalProfit + ' GA';
                    this.totalProfitDisplay.style.color = this.totalProfit >= 0 ? 'var(--win-color)' : 'var(--lose-color)';
                }
                
                // Update pattern analysis
                const totalChoices = this.playerChoiceHistory.length;
                if (totalChoices > 0) {
                    const rockPercent = (this.choiceFrequency.rock / totalChoices * 100).toFixed(1);
                    const paperPercent = (this.choiceFrequency.paper / totalChoices * 100).toFixed(1);
                    const scissorsPercent = (this.choiceFrequency.scissors / totalChoices * 100).toFixed(1);
                    
                    if (this.rockUsageDisplay) this.rockUsageDisplay.textContent = rockPercent + '%';
                    if (this.paperUsageDisplay) this.paperUsageDisplay.textContent = paperPercent + '%';
                    if (this.scissorsUsageDisplay) this.scissorsUsageDisplay.textContent = scissorsPercent + '%';
                }
                
                // Update charts
                const totalRounds = this.roundResultHistory.length;
                if (totalRounds > 0) {
                    const wins = this.roundResultHistory.filter(r => r.outcome === 'win').length;
                    const ties = this.roundResultHistory.filter(r => r.outcome === 'tie').length;
                    
                    const winPercent = (wins / totalRounds * 100);
                    const tiePercent = (ties / totalRounds * 100);
                    const profitPercent = Math.max(0, Math.min(100, (this.totalProfit / (this.currentBet * totalRounds) + 1) * 50));
                    
                    if (this.winsChartDisplay) this.winsChartDisplay.style.width = winPercent + '%';
                    if (this.winsPercentDisplay) this.winsPercentDisplay.textContent = winPercent.toFixed(1) + '%';
                    
                    if (this.tiesChartDisplay) this.tiesChartDisplay.style.width = tiePercent + '%';
                    if (this.tiesPercentDisplay) this.tiesPercentDisplay.textContent = tiePercent.toFixed(1) + '%';
                    
                    if (this.profitChartDisplay) this.profitChartDisplay.style.width = profitPercent + '%';
                    if (this.profitPercentDisplay) {
                        const actualProfitPercent = this.totalProfit > 0 ? 
                            '+' + ((this.totalProfit / (this.currentBet * totalRounds)) * 100).toFixed(1) + '%' :
                            ((this.totalProfit / (this.currentBet * totalRounds)) * 100).toFixed(1) + '%';
                        this.profitPercentDisplay.textContent = actualProfitPercent;
                        this.profitPercentDisplay.style.color = this.totalProfit >= 0 ? 'var(--win-color)' : 'var(--lose-color)';
                    }
                }
                
                // Update strategy matrix
                if (this.vsRockWinDisplay) {
                    const rate = this.vsChoiceWinRate.vsRock.total > 0 ? 
                        (this.vsChoiceWinRate.vsRock.wins / this.vsChoiceWinRate.vsRock.total * 100).toFixed(0) : 0;
                    this.vsRockWinDisplay.textContent = rate + '%';
                }
                
                if (this.vsPaperWinDisplay) {
                    const rate = this.vsChoiceWinRate.vsPaper.total > 0 ? 
                        (this.vsChoiceWinRate.vsPaper.wins / this.vsChoiceWinRate.vsPaper.total * 100).toFixed(0) : 0;
                    this.vsPaperWinDisplay.textContent = rate + '%';
                }
                
                if (this.vsScissorsWinDisplay) {
                    const rate = this.vsChoiceWinRate.vsScissors.total > 0 ? 
                        (this.vsChoiceWinRate.vsScissors.wins / this.vsChoiceWinRate.vsScissors.total * 100).toFixed(0) : 0;
                    this.vsScissorsWinDisplay.textContent = rate + '%';
                }
                
                // AI is provably fair with 33.3% for each choice, so no prediction needed
                // But we can show this to educate users
                if (this.aiRockProbDisplay) this.aiRockProbDisplay.textContent = '33.3%';
                if (this.aiPaperProbDisplay) this.aiPaperProbDisplay.textContent = '33.3%';
                if (this.aiScissorsProbDisplay) this.aiScissorsProbDisplay.textContent = '33.3%';
            }
            
            // Update game history display
            updateGameHistory() {
                if (!this.gameHistoryDisplay || this.viewMode !== 'pro') return;
                
                this.gameHistoryDisplay.innerHTML = '';
                
                // Show last 10 rounds
                const recentRounds = this.roundResultHistory.slice(-10).reverse();
                
                if (recentRounds.length === 0) {
                    const entry = document.createElement('div');
                    entry.className = 'history-entry';
                    entry.innerHTML = `
                        <span class="history-choices">No rounds played yet</span>
                        <span class="history-result">-</span>
                    `;
                    this.gameHistoryDisplay.appendChild(entry);
                    return;
                }
                
                recentRounds.forEach(round => {
                    const entry = document.createElement('div');
                    entry.className = 'history-entry';
                    
                    const playerIcon = this.getChoiceIcon(round.playerChoice);
                    const aiIcon = this.getChoiceIcon(round.aiChoice);
                    
                    const resultClass = round.outcome === 'win' ? 'history-win' : 
                                       round.outcome === 'lose' ? 'history-loss' : 'history-tie';
                    
                    const resultText = round.outcome === 'win' ? 'WIN' : 
                                      round.outcome === 'lose' ? 'LOSE' : 'TIE';
                    
                    entry.innerHTML = `
                        <span class="history-choices">${playerIcon} vs ${aiIcon}</span>
                        <span class="history-result ${resultClass}">${resultText}</span>
                    `;
                    
                    this.gameHistoryDisplay.appendChild(entry);
                });
            }
            
            // Get choice icon
            getChoiceIcon(choice) {
                const icons = { rock: '✊', paper: '✋', scissors: '✌️' };
                return icons[choice] || '?';
            }
            
            // Update strategy tip
            updateStrategyTip() {
                if (!this.strategyTipDisplay || this.viewMode !== 'pro') return;
                
                if (this.roundResultHistory.length < 5) {
                    this.strategyTipDisplay.textContent = "Play more rounds to get personalized strategy recommendations!";
                    return;
                }
                
                // Analyze patterns and provide tips
                let tip = "";
                
                // Check for choice bias
                const totalChoices = this.playerChoiceHistory.length;
                const rockPercent = this.choiceFrequency.rock / totalChoices;
                const paperPercent = this.choiceFrequency.paper / totalChoices;
                const scissorsPercent = this.choiceFrequency.scissors / totalChoices;
                
                if (rockPercent > 0.5) {
                    tip = "You're using Rock too often! Try mixing in more Paper and Scissors to be less predictable.";
                } else if (paperPercent > 0.5) {
                    tip = "You're favoring Paper! Consider using Rock and Scissors more to balance your strategy.";
                } else if (scissorsPercent > 0.5) {
                    tip = "Scissors is your go-to choice! Balance it out with Rock and Paper for better unpredictability.";
                } else {
                    // Check win rates against specific AI choices
                    const bestVsChoice = Object.keys(this.vsChoiceWinRate).reduce((best, current) => {
                        const currentRate = this.vsChoiceWinRate[current].total > 0 ? 
                            this.vsChoiceWinRate[current].wins / this.vsChoiceWinRate[current].total : 0;
                        const bestRate = this.vsChoiceWinRate[best].total > 0 ? 
                            this.vsChoiceWinRate[best].wins / this.vsChoiceWinRate[best].total : 0;
                        return currentRate > bestRate ? current : best;
                    });
                    
                    const worstVsChoice = Object.keys(this.vsChoiceWinRate).reduce((worst, current) => {
                        const currentRate = this.vsChoiceWinRate[current].total > 0 ? 
                            this.vsChoiceWinRate[current].wins / this.vsChoiceWinRate[current].total : 1;
                        const worstRate = this.vsChoiceWinRate[worst].total > 0 ? 
                            this.vsChoiceWinRate[worst].wins / this.vsChoiceWinRate[worst].total : 1;
                        return currentRate < worstRate ? current : worst;
                    });
                    
                    if (this.vsChoiceWinRate[worstVsChoice].total > 0) {
                        const choiceName = worstVsChoice.replace('vs', '').toLowerCase();
                        const counter = { rock: 'Paper', paper: 'Scissors', scissors: 'Rock' }[choiceName];
                        tip = `You struggle against AI's ${choiceName}. Try using ${counter} more when you expect ${choiceName}!`;
                    } else {
                        tip = this.strategyTips[Math.floor(Math.random() * this.strategyTips.length)];
                    }
                }
                
                this.strategyTipDisplay.textContent = tip;
            }
            
            // Pro View button functions
            analyzePerformance() {
                if (this.roundResultHistory.length === 0) {
                    this.showNotification('No games to analyze yet. Start playing to gather data!', 'info');
                    return;
                }
                
                let analysis = `Performance Analysis Report\n\n`;
                
                const totalRounds = this.roundResultHistory.length;
                const wins = this.roundResultHistory.filter(r => r.outcome === 'win').length;
                const ties = this.roundResultHistory.filter(r => r.outcome === 'tie').length;
                const losses = totalRounds - wins - ties;
                
                analysis += `📊 Overall Statistics:\n`;
                analysis += `Total Rounds: ${totalRounds}\n`;
                analysis += `Wins: ${wins} (${(wins/totalRounds*100).toFixed(1)}%)\n`;
                analysis += `Ties: ${ties} (${(ties/totalRounds*100).toFixed(1)}%)\n`;
                analysis += `Losses: ${losses} (${(losses/totalRounds*100).toFixed(1)}%)\n`;
                analysis += `Current Win Streak: ${this.winStreak}\n`;
                analysis += `Best Win Streak: ${this.bestStreak}\n\n`;
                
                analysis += `💰 Financial Performance:\n`;
                analysis += `Total Profit/Loss: ${this.totalProfit >= 0 ? '+' : ''}${this.totalProfit} GA\n`;
                analysis += `Average per Round: ${(this.totalProfit/totalRounds).toFixed(1)} GA\n`;
                analysis += `ROI: ${((this.totalProfit/(this.currentBet*totalRounds))*100).toFixed(1)}%\n\n`;
                
                analysis += `🎯 Choice Distribution:\n`;
                const totalChoices = this.playerChoiceHistory.length;
                analysis += `Rock: ${this.choiceFrequency.rock} (${(this.choiceFrequency.rock/totalChoices*100).toFixed(1)}%)\n`;
                analysis += `Paper: ${this.choiceFrequency.paper} (${(this.choiceFrequency.paper/totalChoices*100).toFixed(1)}%)\n`;
                analysis += `Scissors: ${this.choiceFrequency.scissors} (${(this.choiceFrequency.scissors/totalChoices*100).toFixed(1)}%)\n\n`;
                
                analysis += `🔍 Performance vs AI Choices:\n`;
                Object.entries(this.vsChoiceWinRate).forEach(([key, data]) => {
                    const choiceName = key.replace('vs', '');
                    const rate = data.total > 0 ? (data.wins / data.total * 100).toFixed(1) : 'N/A';
                    analysis += `vs ${choiceName}: ${data.wins}/${data.total} (${rate}%)\n`;
                });
                
                analysis += `\n💡 Key Insights:\n`;
                if (wins/totalRounds > 0.4) {
                    analysis += `• Excellent performance! You're beating the 33% expected win rate.\n`;
                } else if (wins/totalRounds > 0.25) {
                    analysis += `• Good performance, close to the theoretical optimum.\n`;
                } else {
                    analysis += `• Room for improvement. Consider varying your strategy more.\n`;
                }
                
                if (this.bestStreak >= 5) {
                    analysis += `• Impressive win streak! You had ${this.bestStreak} consecutive wins.\n`;
                }
                
                if (Math.abs(this.choiceFrequency.rock - this.choiceFrequency.paper) < totalChoices * 0.1 &&
                    Math.abs(this.choiceFrequency.paper - this.choiceFrequency.scissors) < totalChoices * 0.1) {
                    analysis += `• Good choice distribution - you're being unpredictable!\n`;
                } else {
                    analysis += `• Try to balance your choices more for better unpredictability.\n`;
                }
                
                this.showNotification(analysis, 'info');
            }
            
            optimizeStrategy() {
                if (this.roundResultHistory.length < 10) {
                    this.showNotification('Play at least 10 rounds to get strategy optimization!', 'info');
                    return;
                }
                
                let optimization = `Strategy Optimization\n\n`;
                
                // Analyze choice patterns
                const totalChoices = this.playerChoiceHistory.length;
                const rockPercent = this.choiceFrequency.rock / totalChoices;
                const paperPercent = this.choiceFrequency.paper / totalChoices;
                const scissorsPercent = this.choiceFrequency.scissors / totalChoices;
                
                optimization += `🎯 Current Choice Distribution:\n`;
                optimization += `Rock: ${(rockPercent*100).toFixed(1)}%\n`;
                optimization += `Paper: ${(paperPercent*100).toFixed(1)}%\n`;
                optimization += `Scissors: ${(scissorsPercent*100).toFixed(1)}%\n\n`;
                
                optimization += `📈 Optimization Recommendations:\n`;
                
                // Ideal distribution is 33.33% each for maximum unpredictability
                if (Math.max(rockPercent, paperPercent, scissorsPercent) > 0.45) {
                    const favored = rockPercent > 0.45 ? 'Rock' : paperPercent > 0.45 ? 'Paper' : 'Scissors';
                    optimization += `• You're overusing ${favored}! Reduce usage by 15-20%\n`;
                }
                
                if (Math.min(rockPercent, paperPercent, scissorsPercent) < 0.25) {
                    const underused = rockPercent < 0.25 ? 'Rock' : paperPercent < 0.25 ? 'Paper' : 'Scissors';
                    optimization += `• Use ${underused} more often to improve balance\n`;
                }
                
                // Find best performing strategies
                const bestVs = Object.entries(this.vsChoiceWinRate).reduce((best, [key, data]) => {
                    const rate = data.total > 0 ? data.wins / data.total : 0;
                    return rate > best.rate ? { choice: key, rate: rate, data: data } : best;
                }, { rate: 0 });
                
                if (bestVs.rate > 0.5) {
                    const choiceName = bestVs.choice.replace('vs', '').toLowerCase();
                    optimization += `• You excel against AI's ${choiceName} (${(bestVs.rate*100).toFixed(1)}% win rate)\n`;
                }
                
                optimization += `\n🎲 Optimal Strategy for RPS:\n`;
                optimization += `• Aim for 33.3% usage of each choice\n`;
                optimization += `• Avoid patterns in your choice sequence\n`;
                optimization += `• Don't try to "read" the AI - it's provably random\n`;
                optimization += `• Focus on staying unpredictable\n`;
                optimization += `• Manage your bankroll with consistent bet sizes\n\n`;
                
                optimization += `💰 Bankroll Management:\n`;
                const averageProfit = this.totalProfit / this.roundResultHistory.length;
                if (averageProfit > 0) {
                    optimization += `• You're profitable! Consider slightly increasing bet size\n`;
                } else {
                    optimization += `• Consider reducing bet size until performance improves\n`;
                }
                
                optimization += `• Recommended bet: ${Math.max(10, Math.min(50, Math.floor(this.balance * 0.05)))} GA (5% of balance)\n`;
                optimization += `• Set win/loss limits for each session\n`;
                
                this.showNotification(optimization, 'info');
            }
            
            resetProStats() {
                if (confirm('Are you sure you want to reset all Pro View statistics? This cannot be undone.')) {
                    // Reset all pro stats
                    this.winStreak = 0;
                    this.bestStreak = 0;
                    this.roundsWon = 0;
                    this.totalProfit = 0;
                    this.playerChoiceHistory = [];
                    this.aiChoiceHistory = [];
                    this.roundResultHistory = [];
                    this.choiceFrequency = { rock: 0, paper: 0, scissors: 0 };
                    this.vsChoiceWinRate = { 
                        vsRock: { wins: 0, total: 0 },
                        vsPaper: { wins: 0, total: 0 },
                        vsScissors: { wins: 0, total: 0 }
                    };
                    
                    // Update displays
                    this.updateProViewDisplay();
                    this.updateGameHistory();
                    this.updateStrategyTip();
                    
                    this.showNotification('Pro View statistics have been reset!', 'success');
                }
            }
            
            exportGameData() {
                const gameData = {
                    timestamp: new Date().toISOString(),
                    session: {
                        totalGames: this.totalGames,
                        gamesWon: this.gamesWon,
                        currentBalance: this.balance,
                        sessionDuration: Date.now() - this.sessionStartTime
                    },
                    proStats: {
                        winStreak: this.winStreak,
                        bestStreak: this.bestStreak,
                        roundsWon: this.roundsWon,
                        totalProfit: this.totalProfit,
                        choiceFrequency: this.choiceFrequency,
                        vsChoiceWinRate: this.vsChoiceWinRate
                    },
                    gameHistory: this.roundResultHistory,
                    settings: {
                        viewMode: this.viewMode,
                        currentBet: this.currentBet
                    }
                };
                
                // Create downloadable JSON file
                const dataStr = JSON.stringify(gameData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                
                const downloadLink = document.createElement('a');
                downloadLink.href = url;
                downloadLink.download = `rps-showdown-data-${Date.now()}.json`;
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);
                
                this.showNotification('Game data exported successfully! 📊', 'success');
            }
            
            // Override showNotification for enhanced Pro View notifications
            showNotification(message, type = 'info') {
                const notification = document.getElementById('notification');
                notification.textContent = message;
                
                // Set color based on type
                if (type === 'error') {
                    notification.style.background = 'var(--lose-color)';
                } else if (type === 'success') {
                    notification.style.background = 'var(--win-color)';
                } else {
                    notification.style.background = 'var(--rps-blue)';
                }
                
                notification.className = 'notification show';
                
                // Auto-hide after appropriate time based on message length
                const displayTime = message.length > 200 ? 12000 : 5000;
                setTimeout(() => {
                    notification.classList.remove('show');
                }, displayTime);
            }
        }

        // Initialize the enhanced game when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log("DOM loaded, initializing Enhanced RPS Showdown game...");
            try {
                // If base game already exists, replace it with enhanced version
                if (window.rpsGame) {
                    // Save current state if needed
                    const balance = window.rpsGame.balance;
                    const totalGames = window.rpsGame.totalGames;
                    const gamesWon = window.rpsGame.gamesWon;
                    
                    // Create enhanced game
                    window.rpsGame = new EnhancedRPSGame();
                    
                    // Restore state if needed
                    window.rpsGame.balance = balance;
                    window.rpsGame.totalGames = totalGames;
                    window.rpsGame.gamesWon = gamesWon;
                    window.rpsGame.updateDisplay();
                } else {
                    window.rpsGame = new EnhancedRPSGame();
                }
                
                console.log("Enhanced RPS Showdown game initialized successfully!");
            } catch (error) {
                console.error("Error initializing Enhanced RPS Showdown game:", error);
            }
        });
    </script>
</body>
</html>